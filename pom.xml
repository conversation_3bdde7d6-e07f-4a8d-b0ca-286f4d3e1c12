<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jeecgframework.boot</groupId>
  <artifactId>jeecg-boot-parent</artifactId>
  <version>2.2.0</version>
  <packaging>pom</packaging>

  	<parent>
	    <groupId>org.springframework.boot</groupId>
	    <artifactId>spring-boot-starter-parent</artifactId>
	    <version>2.3.12.RELEASE</version>
	    <relativePath/>
	</parent>


	<modules>
		<module>jeecg-boot-base-common</module>
        <module>eleven-cms</module>
        <module>jeecg-boot-module-system</module>
    </modules>

	<distributionManagement>
	  	<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>http://maven.jeecg.com:8090/nexus/content/repositories/jeecg</url>
        </repository>
        <snapshotRepository>
	        <id>jeecg-snapshots</id>
            <name>jeecg Snapshot Repository</name>
            <url>http://maven.jeecg.com:8090/nexus/content/repositories/snapshots/</url>
	    </snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
            <id>aliyun</id>
            <name>aliyun Repository</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
		<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
	</repositories>

    <properties>
        <jeecgboot.version>2.2.0</jeecgboot.version>
        <java.version>1.8</java.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.1.2</mybatis-plus.version>
        <druid.version>1.1.17</druid.version>
        <jwt.version>0.9.1</jwt.version>
        <commons.version>2.6</commons.version>
        <aliyun-java-sdk-core.version>4.1.0</aliyun-java-sdk-core.version>
		<aliyun-java-sdk-dysmsapi.version>1.0.0</aliyun-java-sdk-dysmsapi.version>
		<aliyun.oss.version>3.6.0</aliyun.oss.version>
		<guava.version>26.0-jre</guava.version>
        <elasticsearch.version>7.8.0</elasticsearch.version>
    </properties>

	<dependencies>
		<!-- Prometheus 指标格式支持 -->
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<!--集成springmvc框架并实现自动配置 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--修复log4j严重漏洞-->
        <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-to-slf4j -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
            <version>2.17.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--修复log4j严重漏洞-->
        <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-api -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.17.0</version>
        </dependency>

        <dependency>
    		<groupId>org.springframework.boot</groupId>
    		<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>
		<dependency>
	        <groupId>org.springframework.boot</groupId>
	        <artifactId>spring-boot-starter-test</artifactId>
	        <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>android-json</artifactId>
                    <groupId>com.vaadin.external.google</groupId>
                </exclusion>
            </exclusions>
        </dependency>
	    <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
	    <!-- <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency> -->

		<!-- commons -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>${commons.version}</version>
		</dependency>

		<!-- freemarker -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>

		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<!-- mybatis-plus -->
		 <dependency>
	        <groupId>com.baomidou</groupId>
	        <artifactId>mybatis-plus-boot-starter</artifactId>
	        <version>${mybatis-plus.version}</version>
	    </dependency>

		<!-- druid -->
       <dependency>
		   <groupId>com.alibaba</groupId>
		   <artifactId>druid-spring-boot-starter</artifactId>
		   <version>${druid.version}</version>
		</dependency>

		<!-- 动态数据源 -->
		<dependency>
		  <groupId>com.baomidou</groupId>
		  <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
		  <version>2.5.4</version>
		</dependency>

		<!-- json -->
		<dependency>
         	<groupId>com.alibaba</groupId>
         	<artifactId>fastjson</artifactId>
         	<version>1.2.60</version>
        </dependency>

		<!--mysql-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.47</version>
			<scope>runtime</scope>
		</dependency>
		<!--  sqlserver-->
		<dependency>
		    <groupId>com.microsoft.sqlserver</groupId>
		    <artifactId>sqljdbc4</artifactId>
		    <version>4.0</version>
		    <scope>runtime</scope>
		</dependency>
		<!-- oracle驱动 -->
		<dependency>
		    <groupId>com.oracle</groupId>
		    <artifactId>ojdbc6</artifactId>
		    <version>11.2.0.3</version>
			<scope>runtime</scope>
		</dependency>
        <!-- postgresql驱动 -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.2.6</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-re</artifactId>
			<version>2.1.5</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-collections</groupId>
					<artifactId>commons-collections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		 <!-- Quartz定时任务 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>

		<!--JWT-->
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.7.0</version>
		</dependency>

		<!--shiro-->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring-boot-starter</artifactId>
            <version>1.12.0</version>
        </dependency>
		<!-- shiro-redis -->
        <dependency>
            <groupId>org.crazycake</groupId>
            <artifactId>shiro-redis</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-core</artifactId>
                </exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
        </dependency>

		<!-- Swagger API文档 -->
        <dependency>
		    <groupId>io.springfox</groupId>
		    <artifactId>springfox-swagger2</artifactId>
		    <version>2.9.2</version>
			<exclusions>
				<exclusion>
					<artifactId>swagger-annotations</artifactId>
					<groupId>io.swagger</groupId>
				</exclusion>
				<exclusion>
					<artifactId>swagger-models</artifactId>
					<groupId>io.swagger</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
		    <groupId>io.springfox</groupId>
		    <artifactId>springfox-swagger-ui</artifactId>
		    <version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>swagger-bootstrap-ui</artifactId>
			<version>1.9.3</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-bean-validators</artifactId>
			<version>2.9.2</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
        <!-- # 增加两个配置解决 NumberFormatException -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.22</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.22</version>
        </dependency>


		<!-- Redis -->
		<dependency>
			 <groupId>org.springframework.boot</groupId>
			  <artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.15.0</version>
        </dependency>
        <!--rabbitmq-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

		<!-- 代码生成器 -->
		<!-- 如果下载失败，看这个链接http://jeecg-boot.mydoc.io/?t=345672 -->
		<dependency>
			<groupId>org.jeecgframework.boot</groupId>
			<artifactId>codegenerate</artifactId>
			<version>1.2.0</version>
		</dependency>

        <!-- AutoPoi Excel工具类-->
		<dependency>
			<groupId>org.jeecgframework</groupId>
			<artifactId>autopoi-web</artifactId>
			<version>1.1.1</version>
			<exclusions>
				<exclusion>
					<groupId>commons-codec</groupId>
					<artifactId>commons-codec</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
		    <groupId>cn.hutool</groupId>
		    <artifactId>hutool-all</artifactId>
		    <version>5.7.17</version>
		</dependency>

		<!-- mini文件存储服务 -->
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>4.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>

		<!-- 阿里云短信 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>${aliyun-java-sdk-dysmsapi.version}</version>
		</dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>${aliyun-java-sdk-core.version}</version>
        </dependency>

		<!-- websocket -->
		<dependency>
		   <groupId>org.springframework.boot</groupId>
		   <artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>

		<!-- aliyun oss -->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>${aliyun.oss.version}</version>
		</dependency>

		<!-- 第三方登录 -->
		<dependency>
			<groupId>com.xkcoding.justauth</groupId>
			<artifactId>justauth-spring-boot-starter</artifactId>
			<version>1.3.2</version>
			<exclusions>
				<exclusion>
					<artifactId>fastjson</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
			</exclusions>
		</dependency>

        <!-- 控制台 SQL日志打印插件 -->
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.8.1</version>
        </dependency>

        <!--  打印okhttp的请求为curl格式,然后可以导入postman测试-->
        <dependency>
            <groupId>com.github.mrmike</groupId>
            <artifactId>ok2curl</artifactId>
            <version>0.6.0</version>
        </dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.69</version>
		</dependency>


        <!-- es -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 引入validation支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- commons-exec -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>1.3</version>
        </dependency>

		<!-- 阿里云支付SDK -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java</artifactId>
            <version>4.39.200.ALL</version>
		</dependency>

        <!-- ipip.net归属地查询-->
        <dependency>
            <groupId>net.ipip</groupId>
            <artifactId>ipdb</artifactId>
            <version>1.1.3</version>
        </dependency>

        <!--springframework -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

		<!--阿里云号码认证sdk -->
<!--		<dependency>-->
<!--			<groupId>com.aliyun</groupId>-->
<!--			<artifactId>dypnsapi20170525</artifactId>-->
<!--			<version>1.0.2</version>-->
<!--		</dependency>-->

        <!--腾讯云语音合成tts-->
        <!-- https://mvnrepository.com/artifact/com.tencentcloudapi/tencentcloud-sdk-java-tts -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-tts</artifactId>
            <version>3.1.624</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>net.sourceforge.lame</groupId>-->
<!--            <artifactId>lame</artifactId>-->
<!--            <version>3.98.4</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.cloudburst/java-lame -->
        <!--        This java port of LAME 3.98.4 was created by Ken Händel for his 'jump3r - Java Unofficial MP3 EncodeR' project: http://sourceforge.net/projects/jsidplay2/-->
        <!--        Original sources by the authors of LAME: http://www.sourceforge.net/projects/lame-->
        <dependency>
            <groupId>com.cloudburst</groupId>
            <artifactId>java-lame</artifactId>
            <version>3.98.4</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.bytedeco/javacv-platform -->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco</groupId>-->
<!--            <artifactId>javacv-platform</artifactId>-->
<!--            <version>1.5.7</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv</artifactId>
            <version>1.5.7</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.bytedeco</groupId>-->
<!--            <artifactId>ffmpeg</artifactId>-->
<!--            <version>5.0-1.5.7</version>-->
<!--            <classifier>windows-x86_64</classifier>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <version>5.0-1.5.7</version>
            <classifier>linux-x86_64</classifier>
        </dependency>

        <!-- xss过滤 -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.12.1</version>
		</dependency>

        <!-- https://mvnrepository.com/artifact/com.jayway.jsonpath/json-path -->
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.4.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.dataformat/jackson-dataformat-xml -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.11.0</version>
        </dependency>

        <!--阿里云智能媒体服务相关-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ice20201109</artifactId>
            <version>1.3.7</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>mts20140618</artifactId>
            <version>3.3.56</version>
        </dependency>
        <!--阿里云MNS消息服务,用于阿里云媒体服务的消息通知-->
        <dependency>
            <groupId>com.aliyun.mns</groupId>
            <artifactId>aliyun-sdk-mns</artifactId>
            <version>1.1.9.1</version>
        </dependency>

		<!-- 微信投诉  -->
		<dependency>
			<groupId>com.github.wechatpay-apiv3</groupId>
			<artifactId>wechatpay-apache-httpclient</artifactId>
			<version>0.4.9</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.6</version>
		</dependency>

		<dependency>
			<groupId>com.github.wechatpay-apiv3</groupId>
			<artifactId>wechatpay-java</artifactId>
			<version>0.2.11</version>
		</dependency>

        <!--火山AIGC sdk-->
        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>volc-sdk-java</artifactId>
            <version>LATEST</version>
        </dependency>
        <!--腾讯云 tencentcloud-sdk-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-common</artifactId>
            <version>3.1.1000</version>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-vclm</artifactId>
            <version>3.1.1119</version>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-aiart</artifactId>
            <version>3.1.1113</version>
        </dependency>


    </dependencies>

	<dependencyManagement>
		<dependencies>
			<!-- jeecg-boot-base-common -->
			<dependency>
	    		<groupId>org.jeecgframework.boot</groupId>
	    		<artifactId>jeecg-boot-base-common</artifactId>
	    		<version>${jeecgboot.version}</version>
	    	</dependency>
	    	<!-- 七牛云SDK -->
<!--			<dependency>-->
<!--				<groupId>com.qiniu</groupId>-->
<!--				<artifactId>qiniu-java-sdk</artifactId>-->
<!--				<version>7.2.23</version>-->
<!--			</dependency>-->
		</dependencies>
	</dependencyManagement>
	<build>
		<plugins>
			 <!--指定JDK编译版本-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- 打包跳过测试 -->
            <plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-surefire-plugin</artifactId>
		        <configuration>
		          <skipTests>true</skipTests>
		        </configuration>
	        </plugin>
	         <!-- 避免font文件的二进制文件格式压缩破坏 -->
	         <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
					<encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
						<nonFilteredFileExtension>pem</nonFilteredFileExtension>
						<nonFilteredFileExtension>pfx</nonFilteredFileExtension>
						<nonFilteredFileExtension>p12</nonFilteredFileExtension>
						<nonFilteredFileExtension>ipdb</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
					<delimiters>                                                        n
						<!--这里干掉了对@符号的解析，这个是该插件的一个问题-->
						<delimiter>${*}</delimiter>
					</delimiters>
                </configuration>
            </plugin>
		</plugins>
		<resources>
			<resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>**/*.json</include>
					<include>**/*.ftl</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>
