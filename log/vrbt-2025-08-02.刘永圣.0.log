2025-08-02 11:31:01.432 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-08-02 11:31:02.887 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-08-02 11:31:07.045 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-02 11:31:07.047 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-02 11:31:07.174 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 122ms. Found 11 Elasticsearch repository interfaces.
2025-08-02 11:31:07.972 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-02 11:31:07.973 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-02 11:31:08.037 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 65ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-02 11:31:08.058 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-02 11:31:08.060 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 11:31:08.110 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 39ms. Found 0 Redis repository interfaces.
2025-08-02 11:31:08.798 [main] INFO  GenericScope:295 - BeanFactory id=6012de34-8e6b-3188-9718-70344705731c
2025-08-02 11:31:08.887 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$c127a8d0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.041 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.043 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppMobilePayLinkConfigClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.044 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.047 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$4422b09] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.149 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$96e5d75f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.730 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.734 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$ab0b00b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.755 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.764 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:09.845 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:10.199 [main] INFO  ShiroConfig:254 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-02 11:31:10.202 [main] INFO  ShiroConfig:272 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-08-02 11:31:10.211 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:10.222 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:10.251 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:10.264 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$bf6301a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:10.376 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$b43b1cea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:10.382 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 11:31:11.129 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-08-02 11:31:11.150 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-08-02 11:31:11.152 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-08-02 11:31:11.153 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-02 11:31:11.278 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-08-02 11:31:11.279 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 8357 ms
2025-08-02 11:31:12.150 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-08-02 11:31:12.164 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-08-02 11:31:15.590 [main] INFO  DruidDataSource:1003 - {dataSource-1,member} inited
2025-08-02 11:31:18.019 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-08-02 11:31:20.638 [main] INFO  DruidDataSource:1003 - {dataSource-3,miniapp} inited
2025-08-02 11:31:23.478 [main] INFO  DruidDataSource:1003 - {dataSource-4,master} inited
2025-08-02 11:31:23.484 [main] ERROR DruidDataSource:978 - {dataSource-5} init error
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-02 11:31:23.485 [main] ERROR DynamicDataSourceCreator:244 - druid数据源启动失败
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-02 11:31:23.486 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 5 个数据源
2025-08-02 11:31:23.486 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-08-02 11:31:23.486 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-08-02 11:31:23.486 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 centric 成功
2025-08-02 11:31:23.486 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-08-02 11:31:23.487 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-08-02 11:31:23.487 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-08-02 11:31:33.437 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-02 11:31:33.446 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-02 11:31:33.499 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-02 11:31:33.499 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-02 11:31:33.520 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-02 11:31:33.526 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-02 11:31:33.529 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-02 11:31:33.530 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-02 11:31:33.530 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-02 11:31:33.530 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6ffbae2e
2025-08-02 11:31:35.795 [main] INFO  Version:41 - Redisson 3.15.0
2025-08-02 11:31:38.081 [redisson-netty-4-25] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-08-02 11:31:39.489 [redisson-netty-4-21] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-08-02 11:31:43.382 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-02 11:31:43.384 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-08-02 11:31:51.264 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.265 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.266 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.266 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.350 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.350 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.351 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.353 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 11:31:51.863 [main] TRACE WIRE:64 - [b50042d] Sending request GET / with parameters: 
2025-08-02 11:31:52.470 [I/O dispatcher 1] TRACE WIRE:97 - [b50042d] Received raw response: 200 OK
2025-08-02 11:31:52.908 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-08-02 11:31:52.908 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-08-02 11:31:52.909 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-08-02 11:31:52.910 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-08-02 11:31:52.910 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-08-02 11:32:28.129 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-08-02 11:32:30.173 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-08-02 11:32:54.027 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-08-02 11:32:54.028 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-08-02 11:32:54.029 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-08-02 11:32:54.029 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-08-02 11:32:54.952 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-02 11:32:54.953 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-08-02 11:32:55.718 [main] TRACE WIRE:64 - [5f1b6fbf] Sending request HEAD /insert_code_2025-08?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-08-02 11:32:55.837 [I/O dispatcher 1] TRACE WIRE:97 - [5f1b6fbf] Received raw response: 200 OK
2025-08-02 11:32:56.032 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-02 11:32:56.033 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-02 11:32:56.092 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-02 11:32:56.092 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-02 11:33:02.281 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-08-02 11:33:02.941 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-08-02 11:33:03.004 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-08-02 11:33:03.392 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-02 11:33:04.495 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#60285daa:0/SimpleConnection@cf9073a [delegate=amqp://admin@**************:5672/, localPort= 65108]
2025-08-02 11:33:20.638 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 140.208 seconds (JVM running for 141.241)
2025-08-02 11:33:20.700 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-08-02 11:33:21.694 [RMI TCP Connection(51)-*********] TRACE WIRE:64 - [5dcc620b] Sending request GET /_cluster/health/ with parameters: 
2025-08-02 11:33:21.860 [I/O dispatcher 1] TRACE WIRE:97 - [5dcc620b] Received raw response: 200 OK
2025-08-02 11:33:22.372 [RMI TCP Connection(54)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 11:33:22.373 [RMI TCP Connection(54)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-08-02 11:33:22.720 [RMI TCP Connection(54)-*********] INFO  DispatcherServlet:547 - Completed initialization in 346 ms
2025-08-02 12:00:00.087 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 12:00:03.523 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T12:00:03.522 | 耗时 229 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T12:00:00.088+0800' AND is_deleted = 0;
2025-08-02 12:00:04.002 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 12:00:04.003 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 3915 ms
2025-08-02 12:10:40.016 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-08-02T12:10:40.016 | 耗时 382 ms | SQL 语句：
select ovr.id from openApi_upload_video_record ovr left join mini_app_drama_episode made on ovr.id = made.video_record_id and made.is_deleted = 0 where ovr.status = 4 and ovr.open_video_id is not null and made.video_record_id is null;
2025-08-02 12:10:40.293 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-08-02T12:10:40.293 | 耗时 223 ms | SQL 语句：
SELECT id,album_id,episode_id,resource_video_url,noty_msg,open_video_id,dy_cloud_id,title,description,resource_type,seq,album_name,status,upload_time,create_by,update_by,create_time,update_time FROM openApi_upload_video_record WHERE id='1947921811485810689' ;
2025-08-02 12:10:40.572 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-08-02T12:10:40.572 | 耗时 158 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,per_episode_cost,cover_url,order_no,is_deleted,view_count,icon,create_time,update_time,rating,hot_value FROM mini_app_mini_drama WHERE album_id = '7528220755478184486' AND is_deleted = 0 limit 1;
2025-08-02 12:10:40.800 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-08-02T12:10:40.8 | 耗时 203 ms | SQL 语句：
SELECT id,album_id,title,seq_num,cover_list,year,album_status,review_status,authorize_status,online_status,version,recommendation,desp,tag_list,qualification,status,name,duration,seq_count,seqs_count,production_organisation,director,producer,actor,summary,cost_distribution_uri,cost_url,assurance_uri,playlet_production_cost,screen_writer,license_num,registration_num,ordinary_record_num,key_record_num,record_type,broadcast_record_number,create_by,update_by,create_time,update_time,audit_msg,nick_name FROM openApi_album_info WHERE album_id = '7528220755478184486' limit 1;
2025-08-02 12:10:40.814 [http-nio-8082-exec-3] ERROR JeecgBootExceptionHandler:70 - message:null，handlerMethod:com.eleven.cms.douyinduanju.controller.MiniAppDramaEpisodeController#syncEpisode(),request description:uri=/cms-vrbt-miniapp/douyinduanju/miniAppDramaEpisode/syncEpisode;client=0:0:0:0:0:0:0:1,request parameterMap:org.apache.catalina.util.ParameterMap@4f04c93e
java.lang.NullPointerException: null
	at com.eleven.cms.douyinduanju.service.impl.MiniAppDramaEpisodeServiceImpl.syncOpenApiUploadVideoRecord(MiniAppDramaEpisodeServiceImpl.java:70)
	at com.eleven.cms.douyinduanju.service.impl.MiniAppDramaEpisodeServiceImpl$$FastClassBySpringCGLIB$$49ab15e0.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.eleven.cms.douyinduanju.service.impl.MiniAppDramaEpisodeServiceImpl$$EnhancerBySpringCGLIB$$3cb3c9c.syncOpenApiUploadVideoRecord(<generated>)
	at com.eleven.cms.douyinduanju.controller.MiniAppDramaEpisodeController.syncEpisode(MiniAppDramaEpisodeController.java:227)
	at com.eleven.cms.douyinduanju.controller.MiniAppDramaEpisodeController$$FastClassBySpringCGLIB$$eb3c074.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at org.jeecg.modules.system.aspect.DictAspect.doAround(DictAspect.java:50)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.eleven.cms.douyinduanju.controller.MiniAppDramaEpisodeController$$EnhancerBySpringCGLIB$$a172d798.syncEpisode(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:28:20.094 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb2858c71, L:/*********:64883 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:29:49.741 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb8ef56c5, L:/*********:64869 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:29:49.741 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc8059694, L:/*********:64868 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:29:50.345 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe6dfe64f, L:/*********:64879 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:29:50.436 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8dc3f980, L:/*********:64884 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:29:50.438 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x01b0ee32, L:/*********:64887 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:29:50.440 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x613944bd, L:/*********:64882 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:29:51.137 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd723ea89, L:0.0.0.0/0.0.0.0:64893]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:30:00.162 [schedule-pool-3] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 12:30:00.440 [schedule-pool-3] INFO  p6spy:60 - 2025-08-02T12:30:00.44 | 耗时 122 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T12:30:00.162+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 12:30:00.445 [schedule-pool-3] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 12:30:00.448 [schedule-pool-3] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 286 ms
2025-08-02 12:30:19.834 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6445027e, L:/*********:64870 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:30:19.834 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x839668c0, L:/*********:64874 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 12:30:19.835 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8d350e55, L:/*********:64877 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 13:00:00.116 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 13:00:00.494 [schedule-pool-2] INFO  p6spy:60 - 2025-08-02T13:00:00.494 | 耗时 219 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T13:00:00.118+0800' AND is_deleted = 0;
2025-08-02 13:00:00.495 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 13:00:00.496 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 378 ms
2025-08-02 13:30:00.051 [schedule-pool-5] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 13:30:00.871 [schedule-pool-5] INFO  p6spy:60 - 2025-08-02T13:30:00.871 | 耗时 663 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T13:30:00.052+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 13:30:00.872 [schedule-pool-5] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 13:30:00.941 [schedule-pool-5] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 889 ms
2025-08-02 13:48:43.162 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-08-02T13:48:43.161 | 耗时 215 ms | SQL 语句：
SELECT id,album_id,episode_id,resource_video_url,noty_msg,open_video_id,dy_cloud_id,title,description,resource_type,seq,album_name,status,upload_time,create_by,update_by,create_time,update_time FROM openApi_upload_video_record WHERE status = 1 AND album_id IN ('7533415203413688847') AND open_video_id IS NULL;
2025-08-02 13:56:37.098 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0699c606, L:/*********:64871 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 13:57:38.800 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xea916f85, L:/*********:64891 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 13:57:38.800 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0ea2f594, L:/*********:64892 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 13:58:39.906 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaddb00f4, L:/*********:52489 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:00:00.046 [schedule-pool-8] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 14:00:00.334 [schedule-pool-8] INFO  p6spy:60 - 2025-08-02T14:00:00.333 | 耗时 133 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T14:00:00.046+0800' AND is_deleted = 0;
2025-08-02 14:00:00.334 [schedule-pool-8] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 14:00:00.335 [schedule-pool-8] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 289 ms
2025-08-02 14:00:07.801 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0978d673, L:/*********:64872 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:02:08.906 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xdb542ab5, L:/*********:64889 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:02:08.906 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4151b375, L:/*********:64886 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:02:08.906 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf62c76d1, L:/*********:64888 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:02:08.906 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaddfe774, L:/*********:64880 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:02:09.309 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x60dbda0c, L:/*********:58322 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:02:09.602 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x096693e0, L:/*********:64890 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:02:09.602 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2463c624, L:/*********:52524 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:05:08.806 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xad6724e2, L:/*********:64867 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:05:08.807 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x51bf3822, L:/*********:64876 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:05:08.905 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9f10177e, L:/*********:64873 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:05:09.505 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1fe2d0f7, L:/*********:64885 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.407 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc9a2a183, L:/*********:58702 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.408 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x86f27fd7, L:/*********:58701 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.409 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc13afa57, L:/*********:58703 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.714 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5d2f80f6, L:/*********:52526 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.821 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x65cf0e34, L:/*********:52485 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.822 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x70617369, L:/*********:52484 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.823 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x33b182e1, L:/*********:52487 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.823 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xefb5b6cd, L:/*********:52488 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:11.914 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x38746dd6, L:/*********:58705 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:08:59.608 [http-nio-8082-exec-7] INFO  NasFileRecordController:38 - 收到NAS目录扫描请求: ScanRequest{nasPath='短剧图书内容/产品线短剧百度网盘自动同步2.0'}
2025-08-02 14:08:59.851 [http-nio-8082-exec-7] INFO  NasFileRecordServiceImpl:104 - 开始扫描NAS目录，路径: 短剧图书内容/产品线短剧百度网盘自动同步2.0, 扫描批次: SCAN_20250802140859_9851, 操作系统: Windows
2025-08-02 14:09:05.109 [http-nio-8082-exec-7] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步2.0\恶犬驯养指南
2025-08-02 14:10:13.408 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9b2caf52, L:/*********:52525 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:10:13.512 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x11169478, L:/*********:52491 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:10:13.609 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfb2f2d49, L:/*********:58082 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:10:13.808 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xad9837b2, L:/*********:52390 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:11:00.292 [http-nio-8082-exec-7] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步2.0\无人区之消失的她
2025-08-02 14:11:00.461 [http-nio-8082-exec-7] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步2.0\工业崛起：我为科技扛大旗
2025-08-02 14:11:01.137 [http-nio-8082-exec-7] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步2.0\黑神话：麒麟
2025-08-02 14:11:29.698 [http-nio-8082-exec-7] INFO  NasFileRecordServiceImpl:302 - 批量保存文件记录成功，总数: 67
2025-08-02 14:11:29.698 [http-nio-8082-exec-7] INFO  NasFileRecordServiceImpl:142 - NAS目录扫描完成，批次: SCAN_20250802140859_9851, 总数: 67, 目录: 0, 文件: 67, 总大小: 17605795617 bytes, 耗时: 149847 ms
2025-08-02 14:13:15.541 [http-nio-8082-exec-10] INFO  NasFileRecordController:38 - 收到NAS目录扫描请求: ScanRequest{nasPath='短剧图书内容/产品线短剧百度网盘自动同步'}
2025-08-02 14:13:15.849 [http-nio-8082-exec-10] INFO  NasFileRecordServiceImpl:104 - 开始扫描NAS目录，路径: 短剧图书内容/产品线短剧百度网盘自动同步, 扫描批次: SCAN_20250802141315_5849, 操作系统: Windows
2025-08-02 14:13:19.085 [http-nio-8082-exec-10] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步\恶犬驯养指南
2025-08-02 14:13:20.321 [http-nio-8082-exec-10] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步\无罪释放2
2025-08-02 14:13:20.929 [http-nio-8082-exec-10] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步\无人区之消失的她
2025-08-02 14:13:22.194 [http-nio-8082-exec-10] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步\工业崛起：我为科技扛大旗
2025-08-02 14:13:22.321 [http-nio-8082-exec-10] INFO  NasFileRecordServiceImpl:124 - 路径不存在: \\192.168.6.160\短剧图书内容/产品线短剧百度网盘自动同步\黑神话：麒麟
2025-08-02 14:13:22.321 [http-nio-8082-exec-10] INFO  NasFileRecordServiceImpl:142 - NAS目录扫描完成，批次: SCAN_20250802141315_5849, 总数: 0, 目录: 0, 文件: 0, 总大小: 0 bytes, 耗时: 6472 ms
2025-08-02 14:15:20.423 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x600a7071, L:/*********:59050 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:15:20.424 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcb67d481, L:/*********:59048 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:15:20.424 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x060be2d7, L:/*********:59049 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:15:20.426 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa7ec6308, L:/*********:59051 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:15:47.112 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7ae603b2, L:/*********:58226 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:18:50.817 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x446ef774, L:/*********:59430 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:19:44.320 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe1218040, L:/*********:58483 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:19:44.322 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9b216727, L:/*********:58480 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:19:44.325 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x20204fe2, L:/*********:58481 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:19:44.328 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd956d3f2, L:/*********:58484 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:19:44.330 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf3033cd5, L:/*********:58482 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:11.921 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xda3f8527, L:/*********:64875 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.127 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1ff408e9, L:/*********:58147 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.132 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xdab0f3f9, L:/*********:58148 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.327 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9b749b69, L:/*********:52486 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.439 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2c634734, L:/*********:58485 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.440 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x30525a17, L:/*********:58486 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.454 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd6e342d0, L:/*********:58890 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.528 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd9db349f, L:/*********:58891 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.529 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc3b39dd8, L:/*********:58893 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.530 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x39e75e7c, L:/*********:58897 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.531 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5b4f2540, L:/*********:58894 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.532 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2193ffbf, L:/*********:58896 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.532 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1318eb80, L:/*********:58895 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.533 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x33587824, L:/*********:58892 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:15.533 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfb2c5849, L:/*********:58898 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:16.326 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1df36d0f, L:/*********:59678 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:16.416 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1001043f, L:/*********:59679 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:16.417 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5f230288, L:/*********:59680 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:16.417 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcfe180d4, L:/*********:59681 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:16.418 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3e9963e3, L:/*********:59682 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:23.320 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5da4c1f7, L:/*********:59625 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:24.227 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0d6020f3, L:/*********:59396 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:24.227 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd0945800, L:/*********:59397 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:24.228 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7d7323bc, L:/*********:59398 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:21:24.228 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4e5e6d3c, L:/*********:59399 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:26:48.141 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa77e9e87, L:/*********:59775 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:26:48.141 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc303e24c, L:/*********:59783 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:26:48.142 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x787e94f9, L:/*********:59785 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:26:48.145 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x90638eec, L:/*********:59782 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:26:48.147 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa46ebfc4, L:/*********:59784 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:26:48.148 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x836be1f0, L:/*********:59786 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:20.126 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd75941ef, L:/*********:59799 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:20.127 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6afdda16, L:/*********:59798 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:20.127 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x746f04db, L:/*********:59800 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:20.127 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3b317589, L:/*********:59801 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:26.032 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf32e6b78, L:/*********:59807 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:26.634 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf0d6e834, L:/*********:59811 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:27.325 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x65234a32, L:/*********:59809 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:27.326 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xef1c3698, L:/*********:59810 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:27:27.327 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x42976e71, L:/*********:59812 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:00.424 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x13ff02cc, L:/*********:60238 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:00.426 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3e622036, L:/*********:60239 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.631 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa6b73be2, L:/*********:59787 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.632 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf8d94ac3, L:/*********:59788 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.632 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb9693aff, L:/*********:59789 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.633 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb5f1e901, L:/*********:59791 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.633 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0f81da3a, L:/*********:59790 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.633 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3a4af788, L:/*********:59793 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.633 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf9820b9c, L:/*********:59792 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.635 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe89ecf9d, L:/*********:59796 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.637 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf31974c7, L:/*********:59794 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:29:49.639 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc8a8f519, L:/*********:59797 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:30:00.054 [schedule-pool-1] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 14:30:04.946 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T14:30:04.946 | 耗时 3346 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T14:30:00.055+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 14:30:04.957 [schedule-pool-1] INFO  UserExpirationCheckTask:86 - 发现 1 个可能过期的用户需要检查
2025-08-02 14:30:05.584 [schedule-pool-1] INFO  UserMembershipServiceImpl:310 - 开始检查用户过期状态，用户ID: 10028
2025-08-02 14:30:06.168 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T14:30:06.168 | 耗时 527 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE id='10028' ;
2025-08-02 14:30:06.171 [schedule-pool-1] INFO  UserMembershipServiceImpl:331 - 用户已过期，开始处理，用户ID: 10028, 过期时间: Sat Aug 02 14:13:28 CST 2025
2025-08-02 14:30:06.626 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T14:30:06.626 | 耗时 380 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE user_id = '10028' AND status = 1 AND is_deleted = 0 ORDER BY membership_type ASC , expire_time DESC limit 1;
2025-08-02 14:30:06.939 [Druid-ConnectionPool-Create-48444237] ERROR DruidDataSource:2699 - create connection SQLException, url: *************************************************************************************************************************, errorCode 0, state 08S01
com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,009 milliseconds ago.  The last packet sent successfully to the server was 1,009 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425)
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:990)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3562)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3462)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3905)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2530)
	at com.mysql.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:3935)
	at sun.reflect.GeneratedMethodAccessor314.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1384)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1672)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2697)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:170)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.jdbc.util.ReadAheadInputStream.fill(ReadAheadInputStream.java:101)
	at com.mysql.jdbc.util.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:144)
	at com.mysql.jdbc.util.ReadAheadInputStream.read(ReadAheadInputStream.java:174)
	at com.mysql.jdbc.MysqlIO.readFully(MysqlIO.java:3011)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3472)
	... 11 common frames omitted
2025-08-02 14:30:07.065 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T14:30:07.065 | 耗时 416 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE user_id = '10028' AND status = 0 AND is_deleted = 0 ORDER BY create_time ASC;
2025-08-02 14:30:07.360 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T14:30:07.36 | 耗时 231 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE user_id = '10028' AND status = 1 AND is_deleted = 0 ORDER BY expire_time DESC;
2025-08-02 14:30:07.368 [schedule-pool-1] INFO  UserMembershipServiceImpl:543 - 计算用户过期时间完成，用户ID: 10028, 过期时间: Sat Aug 02 14:13:28 CST 2025, 生效会员数: 1, 未生效会员数: 0
2025-08-02 14:30:07.368 [schedule-pool-1] INFO  UserMembershipServiceImpl:336 - 用户有生效的会员，更新用户状态，用户ID: 10028
2025-08-02 14:30:07.528 [schedule-pool-1] INFO  UserMembershipServiceImpl:543 - 计算用户过期时间完成，用户ID: 10028, 过期时间: Sat Aug 02 14:13:28 CST 2025, 生效会员数: 1, 未生效会员数: 0
2025-08-02 14:30:08.881 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T14:30:08.881 | 耗时 1074 ms | SQL 语句：
UPDATE duan_ju_user SET nick_name='用户1753855895349', open_id='_000UcRl_k1nG3TeNzGfEM3Px-_-aLGywMCN', register_time='2025-07-30T14:11:35.000+0800', expire_time='2025-08-02T14:13:28.000+0800', phone_sub_expire_status=1, third_party_sub_expire_status=0, member_status=2, member_type=3, pay_flat=1, last_operate_time='2025-07-30T14:44:51.000+0800', source='xddjc', resource='xddjc', sub_channel='', create_time='2025-07-30T14:11:35.000+0800', update_time='2025-08-02T14:30:07.534+0800', is_deleted=0 WHERE id=10028;
2025-08-02 14:30:10.762 [schedule-pool-1] INFO  UserExpirationCheckTask:110 - 用户过期处理完成，总计: 1, 成功: 1, 失败: 0
2025-08-02 14:30:10.763 [schedule-pool-1] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 1，耗时: 10708 ms
2025-08-02 14:36:52.835 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe1039620, L:/*********:60419 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.837 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa9af7348, L:/*********:60423 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.839 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcc62e61b, L:/*********:60420 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.841 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc586c71b, L:/*********:60418 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.844 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x159b7cf1, L:/*********:60417 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.846 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa8e2ecdf, L:/*********:60427 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.846 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6ef5e025, L:/*********:60421 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.847 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xefd1f383, L:/*********:60425 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.848 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3d3f5fb0, L:/*********:60428 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:52.849 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3db49e1f, L:/*********:60426 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:55.738 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x49ab31d0, L:/*********:60222 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:55.738 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4448b155, L:/*********:60224 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:55.739 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5e578588, L:/*********:60225 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:36:55.739 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4159c9a3, L:/*********:60223 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:37:58.243 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x150bf928, L:/*********:60870 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:37:58.244 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa1f1bdde, L:/*********:60869 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:37:58.245 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x48f65ab9, L:/*********:60871 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:37:58.246 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7cd86fa6, L:/*********:60872 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:45:01.438 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4c66dbb2, L:/*********:60932 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:45:01.545 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc25e7093, L:/*********:60931 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:45:01.842 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb8d527aa, L:/*********:60235 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:45:01.843 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe03df75a, L:/*********:60232 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:45:01.936 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa60defc9, L:/*********:60930 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:45:01.938 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7ea31ed3, L:/*********:60933 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:45:02.645 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7946fb83, L:/*********:60237 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:36.753 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x654035ae, L:/*********:61377 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:36.755 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x95560f77, L:/*********:61374 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:36.756 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x936ac40e, L:/*********:61373 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:36.757 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x646d82b7, L:/*********:61376 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.255 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaf6ceb04, L:/*********:60362 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.256 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7a6511bd, L:/*********:60361 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.347 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc869be6b, L:/*********:60180 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.347 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x291d4faa, L:/*********:60181 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.347 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x28f235a7, L:/*********:60179 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.347 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x304d0d6f, L:/*********:60182 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.348 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3b733571, L:/*********:60183 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.349 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2c6f27ab, L:/*********:60184 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 14:56:37.854 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7d906770, L:/*********:61379 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 15:00:00.049 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 15:00:01.000 [schedule-pool-2] INFO  p6spy:60 - 2025-08-02T15:00:01 | 耗时 214 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T15:00:00.050+0800' AND is_deleted = 0;
2025-08-02 15:00:01.003 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 15:00:01.004 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 954 ms
2025-08-02 15:02:36.856 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xec75dec9, L:/*********:61371 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-02 15:30:00.047 [schedule-pool-6] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 15:30:00.582 [schedule-pool-6] INFO  p6spy:60 - 2025-08-02T15:30:00.582 | 耗时 202 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T15:30:00.049+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 15:30:00.584 [schedule-pool-6] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 15:30:00.584 [schedule-pool-6] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 535 ms
2025-08-02 16:00:00.054 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 16:00:00.583 [schedule-pool-4] INFO  p6spy:60 - 2025-08-02T16:00:00.582 | 耗时 248 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T16:00:00.055+0800' AND is_deleted = 0;
2025-08-02 16:00:00.587 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 16:00:00.589 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 534 ms
2025-08-02 16:30:00.052 [schedule-pool-5] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 16:30:00.558 [schedule-pool-5] INFO  p6spy:60 - 2025-08-02T16:30:00.556 | 耗时 256 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T16:30:00.053+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 16:30:00.596 [schedule-pool-5] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 16:30:00.597 [schedule-pool-5] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 544 ms
2025-08-02 17:00:00.052 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 17:00:00.461 [schedule-pool-9] INFO  p6spy:60 - 2025-08-02T17:00:00.461 | 耗时 234 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T17:00:00.053+0800' AND is_deleted = 0;
2025-08-02 17:00:00.462 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 17:00:00.462 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 409 ms
2025-08-02 17:30:00.045 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 17:30:00.456 [schedule-pool-2] INFO  p6spy:60 - 2025-08-02T17:30:00.456 | 耗时 200 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T17:30:00.047+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 17:30:00.456 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 17:30:00.457 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 410 ms
2025-08-02 18:00:00.046 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 18:00:00.553 [schedule-pool-2] INFO  p6spy:60 - 2025-08-02T18:00:00.552 | 耗时 305 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T18:00:00.046+0800' AND is_deleted = 0;
2025-08-02 18:00:00.554 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 18:00:00.554 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 508 ms
2025-08-02 18:30:00.049 [schedule-pool-1] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 18:30:00.446 [schedule-pool-1] INFO  p6spy:60 - 2025-08-02T18:30:00.446 | 耗时 202 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T18:30:00.049+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 18:30:00.449 [schedule-pool-1] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 18:30:00.450 [schedule-pool-1] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 401 ms
2025-08-02 19:00:00.055 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 19:00:00.542 [schedule-pool-3] INFO  p6spy:60 - 2025-08-02T19:00:00.542 | 耗时 243 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T19:00:00.057+0800' AND is_deleted = 0;
2025-08-02 19:00:00.543 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 19:00:00.544 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 487 ms
2025-08-02 19:30:00.048 [schedule-pool-6] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 19:30:00.422 [schedule-pool-6] INFO  p6spy:60 - 2025-08-02T19:30:00.421 | 耗时 222 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T19:30:00.049+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 19:30:00.422 [schedule-pool-6] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 19:30:00.423 [schedule-pool-6] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 374 ms
2025-08-02 19:34:54.819 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:54.819 | 耗时 488 ms | SQL 语句：
SELECT id,name,full_path,relative_path,parent_path,file_type,file_size,extension,last_modified,scan_batch,scan_time,level,parent_path_name,readable,writable,remark,create_time,update_time,create_by,update_by,is_deleted FROM nas_file_record WHERE scan_batch = 'SCAN_20250801180359_9734';
2025-08-02 19:34:54.990 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:54.99 | 耗时 141 ms | SQL 语句：
SELECT id,album_id,title,seq_num,cover_list,year,album_status,review_status,authorize_status,online_status,version,recommendation,desp,tag_list,qualification,status,name,duration,seq_count,seqs_count,production_organisation,director,producer,actor,summary,cost_distribution_uri,cost_url,assurance_uri,playlet_production_cost,screen_writer,license_num,registration_num,ordinary_record_num,key_record_num,record_type,broadcast_record_number,create_by,update_by,create_time,update_time,audit_msg,nick_name FROM openApi_album_info WHERE nick_name IN ('无罪释放2','我是一把剑');
2025-08-02 19:34:55.233 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:55.233 | 耗时 228 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607639328186369', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/32.mp4', '无罪释放2-第32集', '无罪释放2-第32集', 2, 32, '无罪释放2', 1, '2025-08-02T19:34:54.993+0800' );
2025-08-02 19:34:55.507 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:55.507 | 耗时 272 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607640297070593', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/8.mp4', '无罪释放2-第8集', '无罪释放2-第8集', 2, 8, '无罪释放2', 1, '2025-08-02T19:34:55.234+0800' );
2025-08-02 19:34:55.758 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:55.758 | 耗时 249 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607641471475714', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/23.mp4', '无罪释放2-第23集', '无罪释放2-第23集', 2, 23, '无罪释放2', 1, '2025-08-02T19:34:55.508+0800' );
2025-08-02 19:34:56.049 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:56.049 | 耗时 287 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607642549411842', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/52.mp4', '无罪释放2-第52集', '无罪释放2-第52集', 2, 52, '无罪释放2', 1, '2025-08-02T19:34:55.760+0800' );
2025-08-02 19:34:56.343 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:56.343 | 耗时 291 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607643769954306', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/4.mp4', '无罪释放2-第4集', '无罪释放2-第4集', 2, 4, '无罪释放2', 1, '2025-08-02T19:34:56.050+0800' );
2025-08-02 19:34:56.603 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:56.602 | 耗时 251 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607645019856898', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/36.mp4', '无罪释放2-第36集', '无罪释放2-第36集', 2, 36, '无罪释放2', 1, '2025-08-02T19:34:56.346+0800' );
2025-08-02 19:34:56.820 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:56.819 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607646106181633', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/1.mp4', '无罪释放2-第1集', '无罪释放2-第1集', 2, 1, '无罪释放2', 1, '2025-08-02T19:34:56.605+0800' );
2025-08-02 19:34:57.065 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:57.065 | 耗时 228 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607647016345601', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/22.mp4', '无罪释放2-第22集', '无罪释放2-第22集', 2, 22, '无罪释放2', 1, '2025-08-02T19:34:56.823+0800' );
2025-08-02 19:34:57.276 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:57.275 | 耗时 207 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607647989424130', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/57.mp4', '无罪释放2-第57集', '无罪释放2-第57集', 2, 57, '无罪释放2', 1, '2025-08-02T19:34:57.066+0800' );
2025-08-02 19:34:57.550 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:57.55 | 耗时 271 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607648895393794', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/42.mp4', '无罪释放2-第42集', '无罪释放2-第42集', 2, 42, '无罪释放2', 1, '2025-08-02T19:34:57.277+0800' );
2025-08-02 19:34:57.865 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:57.865 | 耗时 309 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607650044633090', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/60.mp4', '无罪释放2-第60集', '无罪释放2-第60集', 2, 60, '无罪释放2', 1, '2025-08-02T19:34:57.553+0800' );
2025-08-02 19:34:58.076 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:58.076 | 耗时 207 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607651349061634', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/43.mp4', '无罪释放2-第43集', '无罪释放2-第43集', 2, 43, '无罪释放2', 1, '2025-08-02T19:34:57.867+0800' );
2025-08-02 19:34:58.288 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:58.288 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607652263419905', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/50.mp4', '无罪释放2-第50集', '无罪释放2-第50集', 2, 50, '无罪释放2', 1, '2025-08-02T19:34:58.078+0800' );
2025-08-02 19:34:58.508 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:58.508 | 耗时 212 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607653181972481', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/15.mp4', '无罪释放2-第15集', '无罪释放2-第15集', 2, 15, '无罪释放2', 1, '2025-08-02T19:34:58.291+0800' );
2025-08-02 19:34:58.782 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:58.782 | 耗时 264 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607654087942146', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/48.mp4', '无罪释放2-第48集', '无罪释放2-第48集', 2, 48, '无罪释放2', 1, '2025-08-02T19:34:58.511+0800' );
2025-08-02 19:34:59.033 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:59.033 | 耗时 249 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607655228792833', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/20.mp4', '无罪释放2-第20集', '无罪释放2-第20集', 2, 20, '无罪释放2', 1, '2025-08-02T19:34:58.784+0800' );
2025-08-02 19:34:59.285 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:59.285 | 耗时 249 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607656260591618', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/17.mp4', '无罪释放2-第17集', '无罪释放2-第17集', 2, 17, '无罪释放2', 1, '2025-08-02T19:34:59.034+0800' );
2025-08-02 19:34:59.563 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:59.563 | 耗时 274 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607657292390402', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/56.mp4', '无罪释放2-第56集', '无罪释放2-第56集', 2, 56, '无罪释放2', 1, '2025-08-02T19:34:59.287+0800' );
2025-08-02 19:34:59.778 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:34:59.778 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607658517127169', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/18.mp4', '无罪释放2-第18集', '无罪释放2-第18集', 2, 18, '无罪释放2', 1, '2025-08-02T19:34:59.565+0800' );
2025-08-02 19:35:00.015 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:00.015 | 耗时 231 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607659414708225', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/10.mp4', '无罪释放2-第10集', '无罪释放2-第10集', 2, 10, '无罪释放2', 1, '2025-08-02T19:34:59.780+0800' );
2025-08-02 19:35:00.264 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:00.264 | 耗时 246 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607660379398146', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/14.mp4', '无罪释放2-第14集', '无罪释放2-第14集', 2, 14, '无罪释放2', 1, '2025-08-02T19:35:00.016+0800' );
2025-08-02 19:35:00.895 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:00.894 | 耗时 628 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607661411196929', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/12.mp4', '无罪释放2-第12集', '无罪释放2-第12集', 2, 12, '无罪释放2', 1, '2025-08-02T19:35:00.265+0800' );
2025-08-02 19:35:01.308 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:01.307 | 耗时 406 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607664053608450', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/62.mp4', '无罪释放2-第62集', '无罪释放2-第62集', 2, 62, '无罪释放2', 1, '2025-08-02T19:35:00.897+0800' );
2025-08-02 19:35:01.611 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:01.611 | 耗时 299 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607665802633217', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/39.mp4', '无罪释放2-第39集', '无罪释放2-第39集', 2, 39, '无罪释放2', 1, '2025-08-02T19:35:01.309+0800' );
2025-08-02 19:35:01.903 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:01.903 | 耗时 288 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607667098673153', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/45.mp4', '无罪释放2-第45集', '无罪释放2-第45集', 2, 45, '无罪释放2', 1, '2025-08-02T19:35:01.612+0800' );
2025-08-02 19:35:02.471 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:02.471 | 耗时 565 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607668323409921', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/21.mp4', '无罪释放2-第21集', '无罪释放2-第21集', 2, 21, '无罪释放2', 1, '2025-08-02T19:35:01.903+0800' );
2025-08-02 19:35:02.757 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:02.757 | 耗时 283 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607670688997378', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/3.mp4', '无罪释放2-第3集', '无罪释放2-第3集', 2, 3, '无罪释放2', 1, '2025-08-02T19:35:02.472+0800' );
2025-08-02 19:35:02.969 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:02.969 | 耗时 209 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607671850819585', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/9.mp4', '无罪释放2-第9集', '无罪释放2-第9集', 2, 9, '无罪释放2', 1, '2025-08-02T19:35:02.758+0800' );
2025-08-02 19:35:03.184 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:03.184 | 耗时 211 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607672756789249', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/58.mp4', '无罪释放2-第58集', '无罪释放2-第58集', 2, 58, '无罪释放2', 1, '2025-08-02T19:35:02.970+0800' );
2025-08-02 19:35:03.403 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:03.403 | 耗时 211 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607673658564609', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/11.mp4', '无罪释放2-第11集', '无罪释放2-第11集', 2, 11, '无罪释放2', 1, '2025-08-02T19:35:03.187+0800' );
2025-08-02 19:35:03.623 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:03.623 | 耗时 211 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607674627448834', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/29.mp4', '无罪释放2-第29集', '无罪释放2-第29集', 2, 29, '无罪释放2', 1, '2025-08-02T19:35:03.406+0800' );
2025-08-02 19:35:03.834 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:03.833 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607675533418497', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/54.mp4', '无罪释放2-第54集', '无罪释放2-第54集', 2, 54, '无罪释放2', 1, '2025-08-02T19:35:03.623+0800' );
2025-08-02 19:35:04.048 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:04.047 | 耗时 209 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607676384862209', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/38.mp4', '无罪释放2-第38集', '无罪释放2-第38集', 2, 38, '无罪释放2', 1, '2025-08-02T19:35:03.835+0800' );
2025-08-02 19:35:04.259 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:04.259 | 耗时 206 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607677269860354', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/27.mp4', '无罪释放2-第27集', '无罪释放2-第27集', 2, 27, '无罪释放2', 1, '2025-08-02T19:35:04.050+0800' );
2025-08-02 19:35:04.534 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:04.534 | 耗时 273 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607678188412929', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/49.mp4', '无罪释放2-第49集', '无罪释放2-第49集', 2, 49, '无罪释放2', 1, '2025-08-02T19:35:04.260+0800' );
2025-08-02 19:35:04.808 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:04.808 | 耗时 270 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607679346040834', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/28.mp4', '无罪释放2-第28集', '无罪释放2-第28集', 2, 28, '无罪释放2', 1, '2025-08-02T19:35:04.535+0800' );
2025-08-02 19:35:05.102 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:05.102 | 耗时 292 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607680507863041', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/19.mp4', '无罪释放2-第19集', '无罪释放2-第19集', 2, 19, '无罪释放2', 1, '2025-08-02T19:35:04.809+0800' );
2025-08-02 19:35:05.407 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:05.407 | 耗时 299 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607681724211202', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/53.mp4', '无罪释放2-第53集', '无罪释放2-第53集', 2, 53, '无罪释放2', 1, '2025-08-02T19:35:05.105+0800' );
2025-08-02 19:35:05.644 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:05.644 | 耗时 233 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607683020251138', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/16.mp4', '无罪释放2-第16集', '无罪释放2-第16集', 2, 16, '无罪释放2', 1, '2025-08-02T19:35:05.408+0800' );
2025-08-02 19:35:05.893 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:05.893 | 耗时 247 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607683989135361', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/59.mp4', '无罪释放2-第59集', '无罪释放2-第59集', 2, 59, '无罪释放2', 1, '2025-08-02T19:35:05.645+0800' );
2025-08-02 19:35:06.221 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:06.22 | 耗时 324 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607685016739842', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/64.mp4', '无罪释放2-第64集', '无罪释放2-第64集', 2, 64, '无罪释放2', 1, '2025-08-02T19:35:05.894+0800' );
2025-08-02 19:35:06.474 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:06.474 | 耗时 247 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607686438608897', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/46.mp4', '无罪释放2-第46集', '无罪释放2-第46集', 2, 46, '无罪释放2', 1, '2025-08-02T19:35:06.223+0800' );
2025-08-02 19:35:06.689 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:06.689 | 耗时 209 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607687466213377', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/2.mp4', '无罪释放2-第2集', '无罪释放2-第2集', 2, 2, '无罪释放2', 1, '2025-08-02T19:35:06.475+0800' );
2025-08-02 19:35:08.108 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:08.108 | 耗时 1410 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607688367988737', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/6.mp4', '无罪释放2-第6集', '无罪释放2-第6集', 2, 6, '无罪释放2', 1, '2025-08-02T19:35:06.692+0800' );
2025-08-02 19:35:08.437 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:08.437 | 耗时 327 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607694307123202', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/35.mp4', '无罪释放2-第35集', '无罪释放2-第35集', 2, 35, '无罪释放2', 1, '2025-08-02T19:35:08.109+0800' );
2025-08-02 19:35:08.756 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:08.756 | 耗时 315 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607695724797954', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/47.mp4', '无罪释放2-第47集', '无罪释放2-第47集', 2, 47, '无罪释放2', 1, '2025-08-02T19:35:08.438+0800' );
2025-08-02 19:35:09.299 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:09.298 | 耗时 535 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607697071169537', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/30.mp4', '无罪释放2-第30集', '无罪释放2-第30集', 2, 30, '无罪释放2', 1, '2025-08-02T19:35:08.757+0800' );
2025-08-02 19:35:10.600 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:10.6 | 耗时 1299 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607699340288002', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/51.mp4', '无罪释放2-第51集', '无罪释放2-第51集', 2, 51, '无罪释放2', 1, '2025-08-02T19:35:09.299+0800' );
2025-08-02 19:35:11.679 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:11.679 | 耗时 1077 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607704755134465', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/44.mp4', '无罪释放2-第44集', '无罪释放2-第44集', 2, 44, '无罪释放2', 1, '2025-08-02T19:35:10.601+0800' );
2025-08-02 19:35:12.654 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:12.654 | 耗时 970 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607709293371393', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/24.mp4', '无罪释放2-第24集', '无罪释放2-第24集', 2, 24, '无罪释放2', 1, '2025-08-02T19:35:11.680+0800' );
2025-08-02 19:35:12.892 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:12.892 | 耗时 235 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607713366040578', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/55.mp4', '无罪释放2-第55集', '无罪释放2-第55集', 2, 55, '无罪释放2', 1, '2025-08-02T19:35:12.655+0800' );
2025-08-02 19:35:13.435 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:13.435 | 耗时 540 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607714393645058', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/5.mp4', '无罪释放2-第5集', '无罪释放2-第5集', 2, 5, '无罪释放2', 1, '2025-08-02T19:35:12.893+0800' );
2025-08-02 19:35:13.681 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:13.681 | 耗时 244 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607716641792002', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/63.mp4', '无罪释放2-第63集', '无罪释放2-第63集', 2, 63, '无罪释放2', 1, '2025-08-02T19:35:13.436+0800' );
2025-08-02 19:35:14.231 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:14.231 | 耗时 544 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607717719728130', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/65.mp4', '无罪释放2-第65集', '无罪释放2-第65集', 2, 65, '无罪释放2', 1, '2025-08-02T19:35:13.684+0800' );
2025-08-02 19:35:14.450 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:14.45 | 耗时 214 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607720043372545', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/33.mp4', '无罪释放2-第33集', '无罪释放2-第33集', 2, 33, '无罪释放2', 1, '2025-08-02T19:35:14.233+0800' );
2025-08-02 19:35:14.666 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:14.666 | 耗时 213 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607720953536513', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/31.mp4', '无罪释放2-第31集', '无罪释放2-第31集', 2, 31, '无罪释放2', 1, '2025-08-02T19:35:14.451+0800' );
2025-08-02 19:35:14.945 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:14.944 | 耗时 277 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607721796591617', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/67.mp4', '无罪释放2-第67集', '无罪释放2-第67集', 2, 67, '无罪释放2', 1, '2025-08-02T19:35:14.666+0800' );
2025-08-02 19:35:15.179 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:15.179 | 耗时 233 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607722966802433', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/7.mp4', '无罪释放2-第7集', '无罪释放2-第7集', 2, 7, '无罪释放2', 1, '2025-08-02T19:35:14.945+0800' );
2025-08-02 19:35:15.518 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:15.518 | 耗时 336 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607724006989826', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/13.mp4', '无罪释放2-第13集', '无罪释放2-第13集', 2, 13, '无罪释放2', 1, '2025-08-02T19:35:15.180+0800' );
2025-08-02 19:35:16.329 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:16.329 | 耗时 808 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607725428858881', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/25.mp4', '无罪释放2-第25集', '无罪释放2-第25集', 2, 25, '无罪释放2', 1, '2025-08-02T19:35:15.519+0800' );
2025-08-02 19:35:17.061 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:17.061 | 耗时 730 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607728780107778', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/26.mp4', '无罪释放2-第26集', '无罪释放2-第26集', 2, 26, '无罪释放2', 1, '2025-08-02T19:35:16.330+0800' );
2025-08-02 19:35:17.295 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:17.295 | 耗时 231 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607731892281346', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/34.mp4', '无罪释放2-第34集', '无罪释放2-第34集', 2, 34, '无罪释放2', 1, '2025-08-02T19:35:17.062+0800' );
2025-08-02 19:35:17.602 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:17.602 | 耗时 305 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607732869554178', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/66.mp4', '无罪释放2-第66集', '无罪释放2-第66集', 2, 66, '无罪释放2', 1, '2025-08-02T19:35:17.296+0800' );
2025-08-02 19:35:17.831 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:17.831 | 耗时 222 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607734165594114', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/61.mp4', '无罪释放2-第61集', '无罪释放2-第61集', 2, 61, '无罪释放2', 1, '2025-08-02T19:35:17.605+0800' );
2025-08-02 19:35:18.043 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:18.043 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607735130284034', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/40.mp4', '无罪释放2-第40集', '无罪释放2-第40集', 2, 40, '无罪释放2', 1, '2025-08-02T19:35:17.832+0800' );
2025-08-02 19:35:18.257 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:18.257 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607736023670786', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/41.mp4', '无罪释放2-第41集', '无罪释放2-第41集', 2, 41, '无罪释放2', 1, '2025-08-02T19:35:18.045+0800' );
2025-08-02 19:35:18.472 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:18.471 | 耗时 211 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607736866725889', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/无罪释放2/37.mp4', '无罪释放2-第37集', '无罪释放2-第37集', 2, 37, '无罪释放2', 1, '2025-08-02T19:35:18.258+0800' );
2025-08-02 19:35:18.717 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:18.717 | 耗时 235 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607737768501250', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/32.mp4', '我是一把剑-第32集', '我是一把剑-第32集', 2, 32, '我是一把剑', 1, '2025-08-02T19:35:18.474+0800' );
2025-08-02 19:35:18.931 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:18.931 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607738804494337', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/8.mp4', '我是一把剑-第8集', '我是一把剑-第8集', 2, 8, '我是一把剑', 1, '2025-08-02T19:35:18.719+0800' );
2025-08-02 19:35:19.171 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:19.171 | 耗时 234 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607739706269698', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/23.mp4', '我是一把剑-第23集', '我是一把剑-第23集', 2, 23, '我是一把剑', 1, '2025-08-02T19:35:18.933+0800' );
2025-08-02 19:35:19.381 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:19.381 | 耗时 206 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607740738068481', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/52.mp4', '我是一把剑-第52集', '我是一把剑-第52集', 2, 52, '我是一把剑', 1, '2025-08-02T19:35:19.172+0800' );
2025-08-02 19:35:19.597 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:19.597 | 耗时 209 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607741639843842', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/4.mp4', '我是一把剑-第4集', '我是一把剑-第4集', 2, 4, '我是一把剑', 1, '2025-08-02T19:35:19.384+0800' );
2025-08-02 19:35:19.806 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:19.806 | 耗时 207 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607742533230594', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/36.mp4', '我是一把剑-第36集', '我是一把剑-第36集', 2, 36, '我是一把剑', 1, '2025-08-02T19:35:19.598+0800' );
2025-08-02 19:35:20.025 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:20.025 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607743367897089', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/1.mp4', '我是一把剑-第1集', '我是一把剑-第1集', 2, 1, '我是一把剑', 1, '2025-08-02T19:35:19.810+0800' );
2025-08-02 19:35:20.256 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:20.256 | 耗时 227 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607744282255361', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/22.mp4', '我是一把剑-第22集', '我是一把剑-第22集', 2, 22, '我是一把剑', 1, '2025-08-02T19:35:20.026+0800' );
2025-08-02 19:35:20.514 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:20.514 | 耗时 256 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607745251139585', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/57.mp4', '我是一把剑-第57集', '我是一把剑-第57集', 2, 57, '我是一把剑', 1, '2025-08-02T19:35:20.257+0800' );
2025-08-02 19:35:20.873 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:20.873 | 耗时 356 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607746358435842', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/42.mp4', '我是一把剑-第42集', '我是一把剑-第42集', 2, 42, '我是一把剑', 1, '2025-08-02T19:35:20.515+0800' );
2025-08-02 19:35:21.155 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:21.155 | 耗时 281 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607747855802370', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/60.mp4', '我是一把剑-第60集', '我是一把剑-第60集', 2, 60, '我是一把剑', 1, '2025-08-02T19:35:20.873+0800' );
2025-08-02 19:35:21.368 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:21.368 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607749021818881', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/43.mp4', '我是一把剑-第43集', '我是一把剑-第43集', 2, 43, '我是一把剑', 1, '2025-08-02T19:35:21.156+0800' );
2025-08-02 19:35:21.576 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:21.576 | 耗时 206 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607749915205633', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/50.mp4', '我是一把剑-第50集', '我是一把剑-第50集', 2, 50, '我是一把剑', 1, '2025-08-02T19:35:21.369+0800' );
2025-08-02 19:35:21.784 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:21.784 | 耗时 205 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607750821175298', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/15.mp4', '我是一把剑-第15集', '我是一把剑-第15集', 2, 15, '我是一把剑', 1, '2025-08-02T19:35:21.578+0800' );
2025-08-02 19:35:21.994 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:21.994 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607751655841793', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/48.mp4', '我是一把剑-第48集', '我是一把剑-第48集', 2, 48, '我是一把剑', 1, '2025-08-02T19:35:21.784+0800' );
2025-08-02 19:35:22.201 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:22.201 | 耗时 206 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607752557617154', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/20.mp4', '我是一把剑-第20集', '我是一把剑-第20集', 2, 20, '我是一把剑', 1, '2025-08-02T19:35:21.995+0800' );
2025-08-02 19:35:22.414 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:22.414 | 耗时 209 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607753409060865', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/17.mp4', '我是一把剑-第17集', '我是一把剑-第17集', 2, 17, '我是一把剑', 1, '2025-08-02T19:35:22.202+0800' );
2025-08-02 19:35:22.697 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:22.697 | 耗时 280 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607754315030529', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/56.mp4', '我是一把剑-第56集', '我是一把剑-第56集', 2, 56, '我是一把剑', 1, '2025-08-02T19:35:22.415+0800' );
2025-08-02 19:35:22.904 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:22.904 | 耗时 205 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607755485241345', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/18.mp4', '我是一把剑-第18集', '我是一把剑-第18集', 2, 18, '我是一把剑', 1, '2025-08-02T19:35:22.697+0800' );
2025-08-02 19:35:23.114 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:23.114 | 耗时 207 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607756387016706', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/10.mp4', '我是一把剑-第10集', '我是一把剑-第10集', 2, 10, '我是一把剑', 1, '2025-08-02T19:35:22.905+0800' );
2025-08-02 19:35:23.339 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:23.339 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607757301374978', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/14.mp4', '我是一把剑-第14集', '我是一把剑-第14集', 2, 14, '我是一把剑', 1, '2025-08-02T19:35:23.129+0800' );
2025-08-02 19:35:23.556 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:23.556 | 耗时 212 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607758219927554', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/12.mp4', '我是一把剑-第12集', '我是一把剑-第12集', 2, 12, '我是一把剑', 1, '2025-08-02T19:35:23.341+0800' );
2025-08-02 19:35:23.769 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:23.769 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607759134285825', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/62.mp4', '我是一把剑-第62集', '我是一把剑-第62集', 2, 62, '我是一把剑', 1, '2025-08-02T19:35:23.557+0800' );
2025-08-02 19:35:23.984 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:23.984 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607759843123201', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/39.mp4', '我是一把剑-第39集', '我是一把剑-第39集', 2, 39, '我是一把剑', 1, '2025-08-02T19:35:23.770+0800' );
2025-08-02 19:35:24.438 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:24.438 | 耗时 238 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607759843123202', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/45.mp4', '我是一把剑-第45集', '我是一把剑-第45集', 2, 45, '我是一把剑', 1, '2025-08-02T19:35:23.985+0800' );
2025-08-02 19:35:24.719 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:24.719 | 耗时 279 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607762779136001', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/21.mp4', '我是一把剑-第21集', '我是一把剑-第21集', 2, 21, '我是一把剑', 1, '2025-08-02T19:35:24.439+0800' );
2025-08-02 19:35:25.026 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:25.025 | 耗时 304 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607763974512641', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/3.mp4', '我是一把剑-第3集', '我是一把剑-第3集', 2, 3, '我是一把剑', 1, '2025-08-02T19:35:24.720+0800' );
2025-08-02 19:35:25.302 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:25.301 | 耗时 273 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607765304107009', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/9.mp4', '我是一把剑-第9集', '我是一把剑-第9集', 2, 9, '我是一把剑', 1, '2025-08-02T19:35:25.026+0800' );
2025-08-02 19:35:25.556 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:25.556 | 耗时 252 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607766419791874', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/58.mp4', '我是一把剑-第58集', '我是一把剑-第58集', 2, 58, '我是一把剑', 1, '2025-08-02T19:35:25.302+0800' );
2025-08-02 19:35:25.862 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:25.862 | 耗时 304 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607767472562178', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/11.mp4', '我是一把剑-第11集', '我是一把剑-第11集', 2, 11, '我是一把剑', 1, '2025-08-02T19:35:25.557+0800' );
2025-08-02 19:35:26.070 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:26.07 | 耗时 206 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607768806350849', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/29.mp4', '我是一把剑-第29集', '我是一把剑-第29集', 2, 29, '我是一把剑', 1, '2025-08-02T19:35:25.863+0800' );
2025-08-02 19:35:26.283 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:26.283 | 耗时 209 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607769653600257', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/54.mp4', '我是一把剑-第54集', '我是一把剑-第54集', 2, 54, '我是一把剑', 1, '2025-08-02T19:35:26.071+0800' );
2025-08-02 19:35:26.494 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:26.494 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607770567958529', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/38.mp4', '我是一把剑-第38集', '我是一把剑-第38集', 2, 38, '我是一把剑', 1, '2025-08-02T19:35:26.284+0800' );
2025-08-02 19:35:26.889 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:26.889 | 耗时 387 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607771490705409', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/27.mp4', '我是一把剑-第27集', '我是一把剑-第27集', 2, 27, '我是一把剑', 1, '2025-08-02T19:35:26.498+0800' );
2025-08-02 19:35:27.099 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:27.099 | 耗时 206 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607773101318145', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/49.mp4', '我是一把剑-第49集', '我是一把剑-第49集', 2, 49, '我是一把剑', 1, '2025-08-02T19:35:26.890+0800' );
2025-08-02 19:35:27.307 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:27.307 | 耗时 206 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607774011482114', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/28.mp4', '我是一把剑-第28集', '我是一把剑-第28集', 2, 28, '我是一把剑', 1, '2025-08-02T19:35:27.100+0800' );
2025-08-02 19:35:27.517 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:27.517 | 耗时 207 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607774858731522', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/19.mp4', '我是一把剑-第19集', '我是一把剑-第19集', 2, 19, '我是一把剑', 1, '2025-08-02T19:35:27.308+0800' );
2025-08-02 19:35:27.732 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:27.732 | 耗时 213 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607775752118273', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/53.mp4', '我是一把剑-第53集', '我是一把剑-第53集', 2, 53, '我是一把剑', 1, '2025-08-02T19:35:27.518+0800' );
2025-08-02 19:35:27.948 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:27.948 | 耗时 209 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607776653893634', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/16.mp4', '我是一把剑-第16集', '我是一把剑-第16集', 2, 16, '我是一把剑', 1, '2025-08-02T19:35:27.735+0800' );
2025-08-02 19:35:28.164 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:28.164 | 耗时 211 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607777555668994', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/59.mp4', '我是一把剑-第59集', '我是一把剑-第59集', 2, 59, '我是一把剑', 1, '2025-08-02T19:35:27.949+0800' );
2025-08-02 19:35:28.397 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:28.397 | 耗时 228 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607778457444353', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/46.mp4', '我是一把剑-第46集', '我是一把剑-第46集', 2, 46, '我是一把剑', 1, '2025-08-02T19:35:28.166+0800' );
2025-08-02 19:35:28.662 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:28.662 | 耗时 262 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607779426328577', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/2.mp4', '我是一把剑-第2集', '我是一把剑-第2集', 2, 2, '我是一把剑', 1, '2025-08-02T19:35:28.398+0800' );
2025-08-02 19:35:28.972 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:28.971 | 耗时 303 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607780512653313', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/6.mp4', '我是一把剑-第6集', '我是一把剑-第6集', 2, 6, '我是一把剑', 1, '2025-08-02T19:35:28.664+0800' );
2025-08-02 19:35:29.184 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:29.184 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607781854830594', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/35.mp4', '我是一把剑-第35集', '我是一把剑-第35集', 2, 35, '我是一把剑', 1, '2025-08-02T19:35:28.972+0800' );
2025-08-02 19:35:29.464 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:29.464 | 耗时 275 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607782702080002', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/47.mp4', '我是一把剑-第47集', '我是一把剑-第47集', 2, 47, '我是一把剑', 1, '2025-08-02T19:35:29.187+0800' );
2025-08-02 19:35:29.859 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:29.859 | 耗时 393 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607783872290817', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/30.mp4', '我是一把剑-第30集', '我是一把剑-第30集', 2, 30, '我是一把剑', 1, '2025-08-02T19:35:29.466+0800' );
2025-08-02 19:35:30.071 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:30.071 | 耗时 210 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607785541623810', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/51.mp4', '我是一把剑-第51集', '我是一把剑-第51集', 2, 51, '我是一把剑', 1, '2025-08-02T19:35:29.860+0800' );
2025-08-02 19:35:30.322 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:30.322 | 耗时 249 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607786439204865', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/44.mp4', '我是一把剑-第44集', '我是一把剑-第44集', 2, 44, '我是一把剑', 1, '2025-08-02T19:35:30.072+0800' );
2025-08-02 19:35:30.614 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:30.614 | 耗时 287 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607787479392257', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/24.mp4', '我是一把剑-第24集', '我是一把剑-第24集', 2, 24, '我是一把剑', 1, '2025-08-02T19:35:30.324+0800' );
2025-08-02 19:35:30.858 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:30.858 | 耗时 242 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607788750266369', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/55.mp4', '我是一把剑-第55集', '我是一把剑-第55集', 2, 55, '我是一把剑', 1, '2025-08-02T19:35:30.615+0800' );
2025-08-02 19:35:31.112 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:31.112 | 耗时 250 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607789740122114', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/5.mp4', '我是一把剑-第5集', '我是一把剑-第5集', 2, 5, '我是一把剑', 1, '2025-08-02T19:35:30.860+0800' );
2025-08-02 19:35:31.477 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:31.477 | 耗时 363 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607790780309505', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/33.mp4', '我是一把剑-第33集', '我是一把剑-第33集', 2, 33, '我是一把剑', 1, '2025-08-02T19:35:31.112+0800' );
2025-08-02 19:35:31.719 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:31.719 | 耗时 239 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607792340590593', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/31.mp4', '我是一把剑-第31集', '我是一把剑-第31集', 2, 31, '我是一把剑', 1, '2025-08-02T19:35:31.478+0800' );
2025-08-02 19:35:32.004 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:32.004 | 耗时 283 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607793368195073', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/7.mp4', '我是一把剑-第7集', '我是一把剑-第7集', 2, 7, '我是一把剑', 1, '2025-08-02T19:35:31.720+0800' );
2025-08-02 19:35:32.239 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:32.238 | 耗时 230 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607794525822978', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/13.mp4', '我是一把剑-第13集', '我是一把剑-第13集', 2, 13, '我是一把剑', 1, '2025-08-02T19:35:32.006+0800' );
2025-08-02 19:35:32.498 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:32.498 | 耗时 253 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607795570204674', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/25.mp4', '我是一把剑-第25集', '我是一把剑-第25集', 2, 25, '我是一把剑', 1, '2025-08-02T19:35:32.240+0800' );
2025-08-02 19:35:32.837 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:32.837 | 耗时 336 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607796610392065', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/26.mp4', '我是一把剑-第26集', '我是一把剑-第26集', 2, 26, '我是一把剑', 1, '2025-08-02T19:35:32.499+0800' );
2025-08-02 19:35:33.113 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:33.113 | 耗时 274 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607798028066817', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/34.mp4', '我是一把剑-第34集', '我是一把剑-第34集', 2, 34, '我是一把剑', 1, '2025-08-02T19:35:32.837+0800' );
2025-08-02 19:35:33.332 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:33.332 | 耗时 217 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607799185694722', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/61.mp4', '我是一把剑-第61集', '我是一把剑-第61集', 2, 61, '我是一把剑', 1, '2025-08-02T19:35:33.114+0800' );
2025-08-02 19:35:33.545 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:33.545 | 耗时 208 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607800162967554', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/40.mp4', '我是一把剑-第40集', '我是一把剑-第40集', 2, 40, '我是一把剑', 1, '2025-08-02T19:35:33.334+0800' );
2025-08-02 19:35:33.772 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:33.772 | 耗时 224 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607801006022658', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/41.mp4', '我是一把剑-第41集', '我是一把剑-第41集', 2, 41, '我是一把剑', 1, '2025-08-02T19:35:33.547+0800' );
2025-08-02 19:35:33.997 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-08-02T19:35:33.997 | 耗时 219 ms | SQL 语句：
INSERT INTO openApi_upload_video_record ( id, resource_video_url, title, description, resource_type, seq, album_name, status, create_time ) VALUES ( '1951607801987489794', 'http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG/产品线短剧百度网盘自动同步2.0/我是一把剑/37.mp4', '我是一把剑-第37集', '我是一把剑-第37集', 2, 37, '我是一把剑', 1, '2025-08-02T19:35:33.774+0800' );
2025-08-02 19:43:21.134 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-08-02 19:43:22.316 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-08-02 19:43:25.577 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-02 19:43:25.578 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-02 19:43:25.689 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 106ms. Found 11 Elasticsearch repository interfaces.
2025-08-02 19:43:26.268 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-02 19:43:26.269 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-02 19:43:26.329 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 60ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-02 19:43:26.343 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-02 19:43:26.344 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 19:43:26.389 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 37ms. Found 0 Redis repository interfaces.
2025-08-02 19:43:26.899 [main] INFO  GenericScope:295 - BeanFactory id=6012de34-8e6b-3188-9718-70344705731c
2025-08-02 19:43:26.962 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$18bb0025] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.087 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.088 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppMobilePayLinkConfigClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.089 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.092 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$5bd5825e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.181 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$ee792eb4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.567 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.571 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$62440760] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.583 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.588 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.652 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.953 [main] INFO  ShiroConfig:254 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-02 19:43:27.956 [main] INFO  ShiroConfig:272 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-08-02 19:43:27.963 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:27.973 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:28.001 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:28.017 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$6389876f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:28.116 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$bce743f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:28.121 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 19:43:28.651 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-08-02 19:43:28.870 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-08-02 19:43:28.871 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-08-02 19:43:28.872 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-02 19:43:28.967 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-08-02 19:43:28.968 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 6622 ms
2025-08-02 19:43:29.708 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-08-02 19:43:29.721 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-08-02 19:43:32.962 [main] INFO  DruidDataSource:1003 - {dataSource-1,member} inited
2025-08-02 19:43:35.419 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-08-02 19:43:37.850 [main] INFO  DruidDataSource:1003 - {dataSource-3,miniapp} inited
2025-08-02 19:43:40.269 [main] INFO  DruidDataSource:1003 - {dataSource-4,master} inited
2025-08-02 19:43:40.277 [main] ERROR DruidDataSource:978 - {dataSource-5} init error
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-02 19:43:40.277 [main] ERROR DynamicDataSourceCreator:244 - druid数据源启动失败
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-02 19:43:40.278 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 5 个数据源
2025-08-02 19:43:40.278 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-08-02 19:43:40.278 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-08-02 19:43:40.278 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 centric 成功
2025-08-02 19:43:40.279 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-08-02 19:43:40.279 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-08-02 19:43:40.279 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-08-02 19:43:47.756 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-02 19:43:47.761 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-02 19:43:47.799 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-02 19:43:47.799 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-02 19:43:47.813 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-02 19:43:47.818 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-02 19:43:47.820 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-02 19:43:47.820 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-02 19:43:47.820 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-02 19:43:47.821 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6da50b8f
2025-08-02 19:43:49.594 [main] INFO  Version:41 - Redisson 3.15.0
2025-08-02 19:43:51.295 [redisson-netty-4-13] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-08-02 19:43:52.817 [redisson-netty-4-20] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-08-02 19:43:55.195 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-02 19:43:55.196 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-08-02 19:43:59.095 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.096 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.096 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.096 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.134 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.135 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.135 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.135 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-02 19:43:59.389 [main] TRACE WIRE:64 - [2caae5b5] Sending request GET / with parameters: 
2025-08-02 19:43:59.857 [I/O dispatcher 1] TRACE WIRE:97 - [2caae5b5] Received raw response: 200 OK
2025-08-02 19:44:00.074 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-08-02 19:44:00.075 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-08-02 19:44:00.075 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-08-02 19:44:00.075 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-08-02 19:44:00.076 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-08-02 19:44:20.512 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-08-02 19:44:21.671 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-08-02 19:44:34.001 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-08-02 19:44:34.002 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-08-02 19:44:34.002 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-08-02 19:44:34.003 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-08-02 19:44:34.383 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-02 19:44:34.384 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-08-02 19:44:34.663 [main] TRACE WIRE:64 - [1898d1d3] Sending request HEAD /insert_code_2025-08?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-08-02 19:44:34.952 [I/O dispatcher 1] TRACE WIRE:97 - [1898d1d3] Received raw response: 200 OK
2025-08-02 19:44:35.137 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-02 19:44:35.138 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-02 19:44:35.187 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-02 19:44:35.187 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-02 19:44:39.071 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-08-02 19:44:39.894 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-08-02 19:44:40.004 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-08-02 19:44:40.325 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-02 19:44:41.321 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#e48dfaf:0/SimpleConnection@5a60e976 [delegate=amqp://admin@**************:5672/, localPort= 63491]
2025-08-02 19:44:55.844 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 95.554 seconds (JVM running for 96.52)
2025-08-02 19:44:55.870 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-08-02 19:44:56.260 [RMI TCP Connection(4)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 19:44:56.261 [RMI TCP Connection(4)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-08-02 19:44:56.542 [RMI TCP Connection(4)-*********] INFO  DispatcherServlet:547 - Completed initialization in 281 ms
2025-08-02 19:44:57.024 [RMI TCP Connection(6)-*********] TRACE WIRE:64 - [72fe136c] Sending request GET /_cluster/health/ with parameters: 
2025-08-02 19:44:57.130 [I/O dispatcher 1] TRACE WIRE:97 - [72fe136c] Received raw response: 200 OK
2025-08-02 20:00:00.032 [schedule-pool-5] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 20:00:03.912 [schedule-pool-5] INFO  p6spy:60 - 2025-08-02T20:00:03.911 | 耗时 249 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T20:00:00.033+0800' AND is_deleted = 0;
2025-08-02 20:00:04.432 [schedule-pool-5] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 20:00:04.432 [schedule-pool-5] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 4399 ms
2025-08-02 20:30:00.046 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 20:30:00.520 [schedule-pool-2] INFO  p6spy:60 - 2025-08-02T20:30:00.52 | 耗时 204 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T20:30:00.047+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 20:30:00.521 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 20:30:00.522 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 475 ms
2025-08-02 21:00:00.097 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 21:00:00.484 [schedule-pool-9] INFO  p6spy:60 - 2025-08-02T21:00:00.484 | 耗时 129 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T21:00:00.097+0800' AND is_deleted = 0;
2025-08-02 21:00:00.487 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 21:00:00.487 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 390 ms
2025-08-02 21:30:00.157 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 21:30:00.392 [schedule-pool-2] INFO  p6spy:60 - 2025-08-02T21:30:00.392 | 耗时 130 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T21:30:00.158+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 21:30:00.394 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 21:30:00.397 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 239 ms
2025-08-02 22:00:00.124 [schedule-pool-0] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 22:00:00.528 [schedule-pool-0] INFO  p6spy:60 - 2025-08-02T22:00:00.526 | 耗时 223 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T22:00:00.125+0800' AND is_deleted = 0;
2025-08-02 22:00:00.539 [schedule-pool-0] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 22:00:00.541 [schedule-pool-0] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 416 ms
2025-08-02 22:30:00.065 [schedule-pool-6] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 22:30:00.516 [schedule-pool-6] INFO  p6spy:60 - 2025-08-02T22:30:00.516 | 耗时 216 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T22:30:00.066+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 22:30:00.518 [schedule-pool-6] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 22:30:00.518 [schedule-pool-6] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 452 ms
2025-08-02 23:00:00.056 [schedule-pool-5] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-02 23:00:00.511 [schedule-pool-5] INFO  p6spy:60 - 2025-08-02T23:00:00.51 | 耗时 203 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-02T23:00:00.058+0800' AND is_deleted = 0;
2025-08-02 23:00:00.515 [schedule-pool-5] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-02 23:00:00.517 [schedule-pool-5] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 458 ms
2025-08-02 23:30:00.046 [schedule-pool-0] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-02 23:30:00.504 [schedule-pool-0] INFO  p6spy:60 - 2025-08-02T23:30:00.504 | 耗时 298 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-02T23:30:00.047+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-02 23:30:00.506 [schedule-pool-0] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-02 23:30:00.507 [schedule-pool-0] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 460 ms
