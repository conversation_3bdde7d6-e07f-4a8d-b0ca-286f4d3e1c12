2025-07-04 02:52:57.523 [pool-4-thread-2] INFO  AliMnsService:576 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	... 11 common frames omitted
2025-07-04 02:52:57.523 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-3 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-3 [ACTIVE]
	... 11 common frames omitted
2025-07-04 09:00:28.792 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-04 09:00:30.591 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-04 09:00:34.321 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 09:00:34.323 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-04 09:00:34.449 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 119ms. Found 11 Elasticsearch repository interfaces.
2025-07-04 09:00:35.107 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 09:00:35.108 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-04 09:00:35.171 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 63ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-04 09:00:35.187 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 09:00:35.189 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 09:00:35.242 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 43ms. Found 0 Redis repository interfaces.
2025-07-04 09:00:35.789 [main] INFO  GenericScope:295 - BeanFactory id=0fbbb8cf-edb7-3054-b5b2-245295025eeb
2025-07-04 09:00:35.849 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$e64dbdc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:35.963 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:35.965 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:35.967 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$517f5e15] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.050 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$e4230a6b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.459 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.462 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$57ede317] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.474 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.478 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.550 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.866 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-04 09:00:36.868 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-04 09:00:36.875 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.884 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.910 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:36.939 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$59336326] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:37.027 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$1784ff6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:37.034 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:00:37.557 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-04 09:00:37.575 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-04 09:00:37.576 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-04 09:00:37.577 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-04 09:00:37.867 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-04 09:00:37.867 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 7237 ms
2025-07-04 09:00:38.633 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-04 09:00:38.650 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-04 09:00:43.758 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-04 09:00:47.240 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-04 09:00:51.615 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-04 09:00:56.885 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-04 09:00:56.885 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-04 09:00:56.885 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-04 09:00:56.897 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-04 09:00:56.897 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-04 09:00:56.897 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-04 09:00:56.897 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-04 09:01:04.012 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-04 09:01:04.012 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-04 09:01:04.043 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-04 09:01:04.043 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-04 09:01:04.060 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-04 09:01:04.075 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-04 09:01:04.078 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-04 09:01:04.078 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-04 09:01:04.078 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-04 09:01:04.078 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@52891a77
2025-07-04 09:01:07.613 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 09:01:07.615 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-04 09:01:10.432 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-04 09:01:12.467 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-04 09:01:14.049 [redisson-netty-6-20] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-04 09:01:15.349 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.349 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.349 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.349 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.383 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.383 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.383 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.383 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:01:15.605 [main] TRACE WIRE:64 - [185010b5] Sending request GET / with parameters: 
2025-07-04 09:01:16.173 [I/O dispatcher 1] TRACE WIRE:97 - [185010b5] Received raw response: 200 OK
2025-07-04 09:01:16.384 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-04 09:01:16.384 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-04 09:01:16.384 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-04 09:01:16.384 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-04 09:01:16.384 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-04 09:01:35.525 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-04 09:01:36.659 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-04 09:01:47.788 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-04 09:01:47.789 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-04 09:01:47.790 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-04 09:01:47.790 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-04 09:01:48.288 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 09:01:48.288 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-04 09:01:48.617 [main] TRACE WIRE:64 - [7e6d6279] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-04 09:01:48.724 [I/O dispatcher 1] TRACE WIRE:97 - [7e6d6279] Received raw response: 200 OK
2025-07-04 09:01:48.842 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 09:01:48.843 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 09:01:48.866 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 09:01:48.866 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 09:01:52.750 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-04 09:01:53.518 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-04 09:01:53.598 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-04 09:01:53.870 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 09:01:54.882 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#2f7cf665:0/SimpleConnection@186c1dab [delegate=amqp://admin@**************:5672/, localPort= 58214]
2025-07-04 09:02:06.657 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 98.763 seconds (JVM running for 100.116)
2025-07-04 09:02:06.688 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-04 09:02:07.143 [RMI TCP Connection(34)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 09:02:07.143 [RMI TCP Connection(34)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-04 09:02:07.265 [RMI TCP Connection(34)-*********] INFO  DispatcherServlet:547 - Completed initialization in 122 ms
2025-07-04 09:02:07.619 [RMI TCP Connection(37)-*********] TRACE WIRE:64 - [43aff18f] Sending request GET /_cluster/health/ with parameters: 
2025-07-04 09:02:07.835 [I/O dispatcher 1] TRACE WIRE:97 - [43aff18f] Received raw response: 200 OK
2025-07-04 09:05:48.727 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T09:05:48.726 | 耗时 294 ms | SQL 语句：
select * from sys_user where username = 'admin' and del_flag = 0;
2025-07-04 09:05:49.906 [http-nio-8082-exec-2] INFO  JwtFilter:39 - JwtFilter.isAccessAllowed.uri：/cms-vrbt-miniapp/error
2025-07-04 09:05:49.947 [http-nio-8082-exec-2] ERROR [dispatcherServlet]:175 - Servlet.service() for servlet [dispatcherServlet] threw exception
org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:710)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:384)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:312)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:398)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:257)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:179)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 09:05:49.949 [http-nio-8082-exec-2] ERROR [localhost]:175 - Exception Processing ErrorPage[errorCode=0, location=/error]
javax.servlet.ServletException: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录
	at org.apache.shiro.web.servlet.AdviceFilter.cleanup(AdviceFilter.java:196)
	at org.apache.shiro.web.filter.authc.AuthenticatingFilter.cleanup(AuthenticatingFilter.java:155)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:148)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:710)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:384)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:312)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:398)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:257)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:179)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
Caused by: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:40)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	... 44 common frames omitted
Caused by: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	... 49 common frames omitted
2025-07-04 09:06:01.432 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-07-04T09:06:01.432 | 耗时 204 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '11';
2025-07-04 09:07:47.799 [http-nio-8082-exec-6] INFO  p6spy:60 - 2025-07-04T09:07:47.799 | 耗时 98 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1221';
2025-07-04 09:07:48.079 [http-nio-8082-exec-6] INFO  p6spy:60 - 2025-07-04T09:07:48.079 | 耗时 220 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,cover_url,order_no,is_deleted,create_time,update_time FROM mini_app_mini_drama WHERE column_id = '1221' LIMIT 0,10;
2025-07-04 09:13:39.984 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-07-04T09:13:39.984 | 耗时 118 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1221';
2025-07-04 09:13:40.350 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-07-04T09:13:40.35 | 耗时 359 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,cover_url,order_no,is_deleted,create_time,update_time FROM mini_app_mini_drama WHERE column_id = '1221' LIMIT 0,10;
2025-07-04 09:15:32.965 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T09:15:32.965 | 耗时 142 ms | SQL 语句：
SELECT id,name,status,order_no,is_deleted,create_by,create_time,update_by,update_time FROM mini_app_drama_column WHERE status = 1 AND is_deleted = 0 ORDER BY order_no ASC;
2025-07-04 09:18:23.062 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-04 09:18:24.130 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-04 09:18:26.057 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 09:18:26.059 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-04 09:18:26.163 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 98ms. Found 11 Elasticsearch repository interfaces.
2025-07-04 09:18:26.686 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 09:18:26.687 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-04 09:18:26.744 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 56ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-04 09:18:26.758 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 09:18:26.759 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 09:18:26.799 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.
2025-07-04 09:18:27.287 [main] INFO  GenericScope:295 - BeanFactory id=f39c5d15-d743-3400-960a-32daa23dddc6
2025-07-04 09:18:27.347 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$8df1d89] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.465 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.467 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.469 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$4bf99fc2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.554 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$de9d4c18] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.948 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.951 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$526824c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.962 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:27.966 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.034 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.306 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-04 09:18:28.309 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-04 09:18:28.316 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.323 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.349 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.363 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$53ada4d3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.449 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$fbf291a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.453 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 09:18:28.925 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-04 09:18:28.938 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-04 09:18:28.939 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-04 09:18:28.939 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-04 09:18:29.024 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-04 09:18:29.024 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 4869 ms
2025-07-04 09:18:29.875 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-04 09:18:29.887 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-04 09:18:34.987 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-04 09:18:39.572 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-04 09:18:44.081 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-04 09:18:49.725 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-04 09:18:49.725 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-04 09:18:49.726 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-04 09:18:49.726 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-04 09:18:49.726 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-04 09:18:49.726 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-04 09:18:49.727 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-04 09:18:56.813 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-04 09:18:56.822 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-04 09:18:56.868 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-04 09:18:56.869 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-04 09:18:56.885 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-04 09:18:56.893 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-04 09:18:56.896 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-04 09:18:56.897 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-04 09:18:56.897 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-04 09:18:56.897 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@65b2f815
2025-07-04 09:19:01.205 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 09:19:01.206 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-04 09:19:04.920 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-04 09:19:07.756 [redisson-netty-6-25] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-04 09:19:09.240 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-04 09:19:11.812 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:11.812 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:11.812 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:11.812 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:11.878 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:11.878 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:11.878 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:11.878 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 09:19:12.149 [main] TRACE WIRE:64 - [189738f4] Sending request GET / with parameters: 
2025-07-04 09:19:12.678 [I/O dispatcher 1] TRACE WIRE:97 - [189738f4] Received raw response: 200 OK
2025-07-04 09:19:12.948 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-04 09:19:12.948 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-04 09:19:12.948 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-04 09:19:12.948 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-04 09:19:12.948 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-04 09:19:30.307 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-04 09:19:31.354 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-04 09:19:41.322 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-04 09:19:41.324 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-04 09:19:41.324 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-04 09:19:41.324 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-04 09:19:41.773 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 09:19:41.773 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-04 09:19:42.005 [main] TRACE WIRE:64 - [15c90b87] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-04 09:19:42.155 [I/O dispatcher 1] TRACE WIRE:97 - [15c90b87] Received raw response: 200 OK
2025-07-04 09:19:42.331 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 09:19:42.332 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 09:19:42.366 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 09:19:42.367 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 09:19:45.848 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-04 09:19:46.338 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-04 09:19:46.380 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-04 09:19:46.702 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 09:19:47.959 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#4af58e7b:0/SimpleConnection@128e9772 [delegate=amqp://admin@**************:5672/, localPort= 59603]
2025-07-04 09:20:02.281 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 99.95 seconds (JVM running for 100.898)
2025-07-04 09:20:02.306 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-04 09:20:02.656 [RMI TCP Connection(26)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 09:20:02.657 [RMI TCP Connection(26)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-04 09:20:02.697 [RMI TCP Connection(26)-*********] INFO  DispatcherServlet:547 - Completed initialization in 40 ms
2025-07-04 09:20:03.051 [RMI TCP Connection(28)-*********] TRACE WIRE:64 - [fea37fd] Sending request GET /_cluster/health/ with parameters: 
2025-07-04 09:20:03.265 [I/O dispatcher 1] TRACE WIRE:97 - [fea37fd] Received raw response: 200 OK
2025-07-04 09:20:22.562 [http-nio-8082-exec-1] INFO  p6spy:60 - 2025-07-04T09:20:22.561 | 耗时 555 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1221' AND status = 1 AND is_deleted = 0;
2025-07-04 09:21:41.453 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T09:21:41.453 | 耗时 313 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1221' AND status = 1 AND is_deleted = 0;
2025-07-04 09:21:41.760 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T09:21:41.76 | 耗时 253 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,cover_url,order_no,is_deleted,create_time,update_time FROM mini_app_mini_drama WHERE column_id = '1221' AND status = 1 AND is_deleted = 0 ORDER BY order_no ASC , create_time ASC LIMIT 0,10;
2025-07-04 09:23:04.066 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-07-04T09:23:04.066 | 耗时 94 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0;
2025-07-04 09:23:04.306 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-07-04T09:23:04.306 | 耗时 239 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,cover_url,order_no,is_deleted,create_time,update_time FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0 ORDER BY order_no ASC , create_time ASC LIMIT 0,10;
2025-07-04 09:24:25.803 [http-nio-8082-exec-5] INFO  p6spy:60 - 2025-07-04T09:24:25.803 | 耗时 205 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0;
2025-07-04 09:24:26.068 [http-nio-8082-exec-5] INFO  p6spy:60 - 2025-07-04T09:24:26.068 | 耗时 263 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,cover_url,order_no,is_deleted,create_time,update_time FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0 ORDER BY order_no ASC , create_time ASC LIMIT 0,10;
2025-07-04 09:24:54.168 [http-nio-8082-exec-1] INFO  p6spy:60 - 2025-07-04T09:24:54.168 | 耗时 196 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0;
2025-07-04 09:24:54.330 [http-nio-8082-exec-1] INFO  p6spy:60 - 2025-07-04T09:24:54.33 | 耗时 160 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,cover_url,order_no,is_deleted,create_time,update_time FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0 ORDER BY order_no ASC , create_time ASC LIMIT 0,10;
2025-07-04 09:25:00.948 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-07-04T09:25:00.948 | 耗时 137 ms | SQL 语句：
SELECT id,type,location,description,image,jump_source,jump_type,order_by,status,create_by,create_time,update_by,update_time FROM duan_ju_banner WHERE status = 1 ORDER BY order_by ASC , create_time DESC;
2025-07-04 09:25:10.277 [http-nio-8082-exec-4] INFO  p6spy:60 - 2025-07-04T09:25:10.277 | 耗时 126 ms | SQL 语句：
SELECT id,type,location,description,image,jump_source,jump_type,order_by,status,create_by,create_time,update_by,update_time FROM duan_ju_banner WHERE status = 1 ORDER BY order_by ASC , create_time DESC;
2025-07-04 09:26:57.561 [http-nio-8082-exec-7] INFO  p6spy:60 - 2025-07-04T09:26:57.56 | 耗时 97 ms | SQL 语句：
SELECT id,type,location,description,image,jump_source,jump_type,order_by,status,create_by,create_time,update_by,update_time FROM duan_ju_banner WHERE status = 1 ORDER BY order_by ASC , create_time DESC;
2025-07-04 09:27:11.623 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-07-04T09:27:11.623 | 耗时 123 ms | SQL 语句：
SELECT id,type,location,description,image,jump_source,jump_type,order_by,status,create_by,create_time,update_by,update_time FROM duan_ju_banner WHERE status = 1 ORDER BY order_by ASC , create_time DESC;
2025-07-04 09:27:19.554 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-07-04T09:27:19.554 | 耗时 97 ms | SQL 语句：
SELECT id,type,location,description,image,jump_source,jump_type,order_by,status,create_by,create_time,update_by,update_time FROM duan_ju_banner WHERE status = 1 ORDER BY order_by ASC , create_time DESC;
2025-07-04 10:26:20.149 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-04 10:26:21.203 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-04 10:26:23.990 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 10:26:23.991 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-04 10:26:24.102 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 105ms. Found 11 Elasticsearch repository interfaces.
2025-07-04 10:26:24.647 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 10:26:24.648 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-04 10:26:24.707 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 58ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-04 10:26:24.720 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 10:26:24.722 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 10:26:24.763 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 35ms. Found 0 Redis repository interfaces.
2025-07-04 10:26:25.261 [main] INFO  GenericScope:295 - BeanFactory id=f39c5d15-d743-3400-960a-32daa23dddc6
2025-07-04 10:26:25.337 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$24a5e4a9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.455 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.456 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.458 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$67c066e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.540 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$fa641338] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.940 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.945 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$6e2eebe4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.956 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:25.961 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:26.034 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:26.331 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-04 10:26:26.335 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-04 10:26:26.343 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:26.352 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:26.385 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:26.398 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$6f746bf3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:26.491 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$17b958c3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:26.498 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 10:26:27.031 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-04 10:26:27.049 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-04 10:26:27.050 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-04 10:26:27.050 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-04 10:26:27.155 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-04 10:26:27.155 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5925 ms
2025-07-04 10:26:27.980 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-04 10:26:27.992 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-04 10:26:31.856 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-04 10:26:35.643 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-04 10:26:39.298 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-04 10:26:43.725 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-04 10:26:43.726 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-04 10:26:43.726 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-04 10:26:43.727 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-04 10:26:43.727 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-04 10:26:43.727 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-04 10:26:43.727 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-04 10:26:50.710 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-04 10:26:50.719 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-04 10:26:50.760 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-04 10:26:50.760 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-04 10:26:50.776 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-04 10:26:50.781 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-04 10:26:50.784 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-04 10:26:50.784 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-04 10:26:50.784 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-04 10:26:50.784 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@14062855
2025-07-04 10:26:54.813 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 10:26:54.814 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-04 10:26:57.428 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-04 10:26:59.366 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-04 10:27:01.082 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-04 10:27:02.303 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.304 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.304 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.304 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.339 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.339 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.339 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.339 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 10:27:02.570 [main] TRACE WIRE:64 - [437eeb4] Sending request GET / with parameters: 
2025-07-04 10:27:03.102 [I/O dispatcher 1] TRACE WIRE:97 - [437eeb4] Received raw response: 200 OK
2025-07-04 10:27:03.303 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-04 10:27:03.303 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-04 10:27:03.304 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-04 10:27:03.304 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-04 10:27:03.304 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-04 10:27:23.169 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-04 10:27:24.386 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-04 10:27:36.163 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-04 10:27:36.164 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-04 10:27:36.164 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-04 10:27:36.164 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-04 10:27:36.840 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 10:27:36.841 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-04 10:27:37.183 [main] TRACE WIRE:64 - [487cdf22] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-04 10:27:37.278 [I/O dispatcher 1] TRACE WIRE:97 - [487cdf22] Received raw response: 200 OK
2025-07-04 10:27:37.403 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 10:27:37.404 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 10:27:37.442 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 10:27:37.442 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 10:27:42.365 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-04 10:27:43.023 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-04 10:27:43.071 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-04 10:27:43.405 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 10:27:44.332 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#24a42e9d:0/SimpleConnection@80a26d6 [delegate=amqp://admin@**************:5672/, localPort= 63124]
2025-07-04 10:27:52.925 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 93.538 seconds (JVM running for 94.574)
2025-07-04 10:27:52.960 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-04 10:27:53.420 [RMI TCP Connection(76)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 10:27:53.420 [RMI TCP Connection(76)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-04 10:27:53.478 [RMI TCP Connection(76)-*********] INFO  DispatcherServlet:547 - Completed initialization in 56 ms
2025-07-04 10:27:53.645 [RMI TCP Connection(77)-*********] TRACE WIRE:64 - [65e6de88] Sending request GET /_cluster/health/ with parameters: 
2025-07-04 10:27:53.741 [I/O dispatcher 1] TRACE WIRE:97 - [65e6de88] Received raw response: 200 OK
2025-07-04 10:28:06.182 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:599 - 抖音开放平台请求参数:{"client_key":"tt13a865158c9932af01","grant_type":"client_credential","client_secret":"12ac3c42f6b7711443911cc3503cab01deb908c9"},url:https://open.douyin.com/oauth/client_token/
2025-07-04 10:28:06.945 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:609 - 抖音开放平台请求结果:{"data":{"access_token":"clt.7f66c02e8bd86f2f774582afe5ecf365GiTmzmCrBcemigBSrgL7m2DLt2aV_lf","captcha":"","desc_url":"","description":"","error_code":0,"expires_in":7200,"log_id":"202507041028064CFF2B2BFC5DAE76D1B3"},"message":"success"}
2025-07-04 10:28:07.195 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:599 - 抖音开放平台请求参数:{episode_id=7508557231474868773, album_id=7507553384406942246, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/play_info/
2025-07-04 10:28:07.921 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:609 - 抖音开放平台请求结果:{"data":{"definition":"1080p","format":"mp4","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0\u0026auth_key=1751603422-e97b6bb0de434229b1ede7a5485c3691-0-8efd9887ffde78bad68a0837b26e2936\u0026br=10051\u0026bt=10051\u0026cd=0%7C0%7C0\u0026ch=0\u0026cr=0\u0026cs=0\u0026dr=0\u0026ds=4\u0026eid=v0279cg10004d0rt0aaljht8scvm7fjg\u0026er=\u0026l=20250704102807318A1C6E7284F514D130\u0026lr=\u0026mime_type=video_mp4\u0026net=0\u0026pl=0\u0026qs=13\u0026rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D\u0026vl=\u0026vr=","size":174248843,"url_expire":"1751602987","bitrate":10292312,"codec":"h264"},"err_msg":"","err_no":0,"log_id":"20250704102807F93FA2A64DDB2162B522"}
2025-07-04 10:28:14.574 [http-nio-8082-exec-2] INFO  DouYinApiController:235 - result:OpenApiRes(errNo=0, errMsg=, logId=20250704102807F93FA2A64DDB2162B522, data={"codec":"h264","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0&auth_key=1751603422-e97b6bb0de434229b1ede7a5485c3691-0-8efd9887ffde78bad68a0837b26e2936&br=10051&bt=10051&cd=0%7C0%7C0&ch=0&cr=0&cs=0&dr=0&ds=4&eid=v0279cg10004d0rt0aaljht8scvm7fjg&er=&l=20250704102807318A1C6E7284F514D130&lr=&mime_type=video_mp4&net=0&pl=0&qs=13&rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D&vl=&vr=","size":174248843,"format":"mp4","definition":"1080p","bitrate":10292312,"url_expire":"1751602987"}, success=null, message=null)
2025-07-04 10:59:56.051 [http-nio-8082-exec-6] INFO  p6spy:60 - 2025-07-04T10:59:56.05 | 耗时 457 ms | SQL 语句：
SELECT COUNT(1) FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0;
2025-07-04 10:59:56.283 [http-nio-8082-exec-6] INFO  p6spy:60 - 2025-07-04T10:59:56.283 | 耗时 186 ms | SQL 语句：
SELECT id,column_id,name,status,album_info_id,album_id,free_num,cover_url,order_no,is_deleted,view_count,create_time,update_time FROM mini_app_mini_drama WHERE column_id = '1926812954441347074' AND status = 1 AND is_deleted = 0 ORDER BY order_no ASC , create_time ASC LIMIT 0,10;
2025-07-04 11:00:09.337 [http-nio-8082-exec-7] INFO  p6spy:60 - 2025-07-04T11:00:09.337 | 耗时 120 ms | SQL 语句：
SELECT id,type,location,description,image,jump_source,jump_type,order_by,status,create_by,create_time,update_by,update_time FROM duan_ju_banner WHERE status = 1 ORDER BY order_by ASC , create_time DESC;
2025-07-04 11:07:24.670 [http-nio-8082-exec-10] INFO  JwtFilter:39 - JwtFilter.isAccessAllowed.uri：/cms-vrbt-miniapp/mini-app/miniDrama/delete
2025-07-04 11:07:24.700 [http-nio-8082-exec-10] ERROR [dispatcherServlet]:175 - Servlet.service() for servlet [dispatcherServlet] in context with path [/cms-vrbt-miniapp] threw exception [org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录] with root cause
org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 11:07:25.151 [http-nio-8082-exec-10] INFO  JwtFilter:39 - JwtFilter.isAccessAllowed.uri：/cms-vrbt-miniapp/error
2025-07-04 11:07:25.151 [http-nio-8082-exec-10] ERROR [dispatcherServlet]:175 - Servlet.service() for servlet [dispatcherServlet] threw exception
org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:710)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:384)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:312)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:398)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:257)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:352)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:177)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 11:07:25.151 [http-nio-8082-exec-10] ERROR [localhost]:175 - Exception Processing ErrorPage[errorCode=0, location=/error]
javax.servlet.ServletException: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录
	at org.apache.shiro.web.servlet.AdviceFilter.cleanup(AdviceFilter.java:196)
	at org.apache.shiro.web.filter.authc.AuthenticatingFilter.cleanup(AuthenticatingFilter.java:155)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:148)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:710)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:384)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:312)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:398)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:257)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:352)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:177)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
Caused by: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:40)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	... 45 common frames omitted
Caused by: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	... 50 common frames omitted
2025-07-04 11:24:05.169 [http-nio-8082-exec-2] INFO  ShiroRealm:153 - ——————————用户在线操作，更新token保证不掉线—————————jwtTokenRefresh——————— eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTE0MjI1NjAsInVzZXJuYW1lIjoiYWRtaW4ifQ.Z0DASh0qt2eE5max_aKzAMz4L7ARs9HhTI0Ufe4ouOU
2025-07-04 11:24:05.988 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T11:24:05.988 | 耗时 345 ms | SQL 语句：
UPDATE ai_union_ai_face_template SET template_name='花木兰', template_remark='昔有奇女子，替父赴戎行。 战甲遮娇面，英名万古扬。', template_type=0, activity_id='at_1860880104954122240', material_id='mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176', template_id='e5db4ee787dc427fb3defd08d3024629', order_by=99, pic_url='https://appmedia.kunpengtn.com/common/1742281034070.png', video_url='https://appmedia.kunpengtn.com/common/1742281057909.mp4', status=0, clips_param='{"pic1":"960:540","pic2":"960:540","pic3":"960:540"}', topic_type=1, update_by='admin', update_time='2025-07-04T11:24:05.390+0800' WHERE id='11';
2025-07-04 11:24:07.957 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T11:24:07.957 | 耗时 302 ms | SQL 语句：
INSERT INTO sys_log ( id, create_by, create_time, cost_time, ip, request_param, method, username, userid, log_content, log_type, operate_type ) VALUES ( '1940974879618183170', 'admin', '2025-07-04T11:24:07.183+0800', 1584, '0:0:0:0:0:0:0:1', '[{"activityId":"at_1860880104954122240","clipsParam":"{\"pic1\":\"960:540\",\"pic2\":\"960:540\",\"pic3\":\"960:540\"}","id":"11","materialId":"mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176","orderBy":99,"picUrl":"https://appmedia.kunpengtn.com/common/1742281034070.png","status":0,"templateId":"e5db4ee787dc427fb3defd08d3024629","templateName":"花木兰","templateRemark":"昔有奇女子，替父赴戎行。\n战甲遮娇面，英名万古扬。","templateType":0,"topicType":1,"updateBy":"admin","updateTime":1751599445390,"videoUrl":"https://appmedia.kunpengtn.com/common/1742281057909.mp4"}]', 'com.eleven.cms.aiunion.controller.template.AiUnionAiFaceTemplateController.edit()', '管理员', 'admin', 'ai_union_ai_face_template-编辑', 2, 3 );
2025-07-04 11:24:46.304 [http-nio-8082-exec-3] INFO  AiUnionApiController:164 - ai联盟-查询主题模板,请求参数:4
2025-07-04 11:24:47.265 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-07-04T11:24:47.265 | 耗时 207 ms | SQL 语句：
SELECT id,template_name,template_remark,template_type,activity_id,material_id,template_id,pic_slot_count,order_by,pic_url,material_pic_url,video_url,status,param,clips_param,remark,topic_type,create_by,create_time,update_by,update_time FROM ai_union_ai_face_template WHERE topic_type = 4 AND status = 1 ORDER BY order_by ASC , id ASC;
2025-07-04 12:04:43.174 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-04 12:04:44.254 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-04 12:04:46.309 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 12:04:46.309 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-04 12:04:46.422 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 108ms. Found 11 Elasticsearch repository interfaces.
2025-07-04 12:04:47.053 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 12:04:47.054 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-04 12:04:47.114 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 62ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-04 12:04:47.125 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 12:04:47.136 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 12:04:47.184 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 39ms. Found 0 Redis repository interfaces.
2025-07-04 12:04:47.888 [main] INFO  GenericScope:295 - BeanFactory id=f39c5d15-d743-3400-960a-32daa23dddc6
2025-07-04 12:04:47.966 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$1c6977b9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.086 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.088 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.090 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$5f83f9f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.178 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$f227a648] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.568 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.570 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$65f27ef4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.582 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.587 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:48.654 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:49.204 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-04 12:04:49.208 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-04 12:04:49.218 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:49.226 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:49.255 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:49.269 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$6737ff03] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:49.354 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$f7cebd3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:49.364 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 12:04:49.887 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-04 12:04:49.904 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-04 12:04:49.905 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-04 12:04:49.905 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-04 12:04:49.990 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-04 12:04:49.990 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5711 ms
2025-07-04 12:04:50.949 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-04 12:04:50.967 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-04 12:04:53.899 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-04 12:04:56.523 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-04 12:05:00.047 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-04 12:05:05.082 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-04 12:05:05.083 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-04 12:05:05.083 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-04 12:05:05.083 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-04 12:05:05.083 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-04 12:05:05.083 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-04 12:05:05.083 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-04 12:05:13.249 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-04 12:05:13.256 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-04 12:05:13.288 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-04 12:05:13.288 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-04 12:05:13.303 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-04 12:05:13.308 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-04 12:05:13.310 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-04 12:05:13.310 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-04 12:05:13.310 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-04 12:05:13.310 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@77c0457f
2025-07-04 12:05:17.803 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 12:05:17.804 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-04 12:05:20.720 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-04 12:05:23.503 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-04 12:05:24.782 [redisson-netty-6-18] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-04 12:05:26.425 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.426 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.427 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.427 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.478 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.479 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.480 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.480 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 12:05:26.775 [main] TRACE WIRE:64 - [3c242c57] Sending request GET / with parameters: 
2025-07-04 12:05:27.398 [I/O dispatcher 1] TRACE WIRE:97 - [3c242c57] Received raw response: 200 OK
2025-07-04 12:05:27.633 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-04 12:05:27.635 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-04 12:05:27.635 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-04 12:05:27.635 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-04 12:05:27.635 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-04 12:05:45.520 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-04 12:05:46.600 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-04 12:05:56.701 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-04 12:05:56.702 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-04 12:05:56.702 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-04 12:05:56.702 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-04 12:05:57.184 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 12:05:57.184 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-04 12:05:57.424 [main] TRACE WIRE:64 - [7921a40c] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-04 12:05:57.597 [I/O dispatcher 1] TRACE WIRE:97 - [7921a40c] Received raw response: 200 OK
2025-07-04 12:05:57.760 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 12:05:57.760 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 12:05:57.790 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 12:05:57.790 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 12:06:01.312 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-04 12:06:01.792 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-04 12:06:01.834 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-04 12:06:02.136 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 12:06:03.556 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#7f914800:0/SimpleConnection@78acdf28 [delegate=amqp://admin@**************:5672/, localPort= 50636]
2025-07-04 12:06:15.147 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 92.75 seconds (JVM running for 93.702)
2025-07-04 12:06:15.174 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-04 12:06:15.599 [RMI TCP Connection(61)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 12:06:15.599 [RMI TCP Connection(61)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-04 12:06:15.656 [RMI TCP Connection(61)-*********] INFO  DispatcherServlet:547 - Completed initialization in 56 ms
2025-07-04 12:06:15.929 [RMI TCP Connection(62)-*********] TRACE WIRE:64 - [59229663] Sending request GET /_cluster/health/ with parameters: 
2025-07-04 12:06:16.027 [I/O dispatcher 1] TRACE WIRE:97 - [59229663] Received raw response: 200 OK
2025-07-04 12:06:37.311 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-07-04T12:06:37.309 | 耗时 1087 ms | SQL 语句：
SELECT id,app_id,app_secret,private_key,key_version,app_name,business_type,public_key,call_back_url,is_deleted,create_by,create_time,update_by,update_time FROM douyin_app_config WHERE business_type = 'default' AND is_deleted = 0 limit 1;
2025-07-04 12:06:38.429 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-07-04T12:06:38.429 | 耗时 260 ms | SQL 语句：
SELECT id,app_id,app_secret,private_key,key_version,app_name,business_type,public_key,call_back_url,is_deleted,create_by,create_time,update_by,update_time FROM douyin_app_config WHERE business_type = 'default' AND is_deleted = 0 limit 1;
2025-07-04 12:06:38.756 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:773 - 抖音开放平台请求参数:{"client_key":"tt13a865158c9932af01","grant_type":"client_credential","client_secret":"12ac3c42f6b7711443911cc3503cab01deb908c9"},url:https://open.douyin.com/oauth/client_token/
2025-07-04 12:06:39.581 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:783 - 抖音开放平台请求结果:{"data":{"access_token":"clt.936b7cb2d759993cc9146ec7f5a59a533VbcACCcEnS5ZmPK46vQMSSiGj2g_hl","captcha":"","desc_url":"","description":"","error_code":0,"expires_in":7200,"log_id":"2025070412063947088B264EDE4486FA72"},"message":"success"}
2025-07-04 12:06:39.981 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:773 - 抖音开放平台请求参数:{episode_id=7508557231474868773, album_id=7507553384406942246, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/play_info/
2025-07-04 12:06:40.503 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:783 - 抖音开放平台请求结果:{"data":{"play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0\u0026auth_key=1751609335-0cfd17b1c44c482f88dba328526a5c14-0-b7b79146da9a5d7e935dc28878efd21a\u0026br=10051\u0026bt=10051\u0026cd=0%7C0%7C0\u0026ch=0\u0026cr=0\u0026cs=0\u0026dr=0\u0026ds=4\u0026eid=v0279cg10004d0rt0aaljht8scvm7fjg\u0026er=\u0026l=2025070412064094F2D8322FB01017B1CC\u0026lr=\u0026mime_type=video_mp4\u0026net=0\u0026pl=0\u0026qs=13\u0026rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D\u0026vl=\u0026vr=","size":174248843,"url_expire":"1751608900","bitrate":10292312,"codec":"h264","definition":"1080p","format":"mp4"},"err_msg":"","err_no":0,"log_id":"20250704120640A1582FDC3B3E706BFCC2"}
2025-07-04 12:06:40.504 [http-nio-8082-exec-3] INFO  DouYinApiController:235 - result:OpenApiRes(errNo=0, errMsg=, logId=20250704120640A1582FDC3B3E706BFCC2, data={"codec":"h264","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0&auth_key=1751609335-0cfd17b1c44c482f88dba328526a5c14-0-b7b79146da9a5d7e935dc28878efd21a&br=10051&bt=10051&cd=0%7C0%7C0&ch=0&cr=0&cs=0&dr=0&ds=4&eid=v0279cg10004d0rt0aaljht8scvm7fjg&er=&l=2025070412064094F2D8322FB01017B1CC&lr=&mime_type=video_mp4&net=0&pl=0&qs=13&rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D&vl=&vr=","size":174248843,"format":"mp4","bitrate":10292312,"definition":"1080p","url_expire":"1751608900"}, success=null, message=null)
2025-07-04 13:54:11.019 [AMQP Connection **************:5672] WARN  ForgivingExceptionHandler:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-04 13:54:11.227 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.274 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.274 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.290 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.290 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.290 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.290 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.306 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.306 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.306 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-04 13:54:11.594 [redisson-netty-6-14] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x3ef2cbb7, L:/*********:50513 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-12] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd69548cf, L:/*********:50492 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-2] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x3d27c422, L:/*********:50487 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-26] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xbbc3a38d, L:/*********:50502 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x18042756, L:/*********:50495 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.609 [redisson-netty-6-27] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xe4a1f062, L:/*********:50501 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.609 [redisson-netty-6-29] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x4984f163, L:/*********:50505 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [lettuce-nioEventLoop-12-1] INFO  CommandHandler:217 - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.609 [redisson-netty-6-11] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x492a05d6, L:/*********:50496 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-32] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x9ba0828e, L:/*********:50508 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-1] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x4e5331ea, L:/*********:50507 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.625 [redisson-netty-6-4] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x0e84611f, L:/*********:50489 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.625 [redisson-netty-6-7] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xca961017, L:/*********:50493 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x49ad29e5, L:/*********:50488 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-28] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x59ddf8e7, L:/*********:50506 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-5] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x5bebeb03, L:/*********:50512 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-6] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd6c7e99a, L:/*********:50497 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.625 [redisson-netty-6-30] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x4eb8b6ad, L:/*********:50504 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-31] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x600c8b0f, L:/*********:50503 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.625 [redisson-netty-6-8] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x91623a58, L:/*********:50490 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-17] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x3b71a906, L:/*********:50514 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.594 [redisson-netty-6-25] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x64ce7a40, L:/*********:50500 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.625 [redisson-netty-6-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x3a116af7, L:/*********:50494 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.641 [redisson-netty-6-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xfde81fe5, L:/*********:50511 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.641 [redisson-netty-6-5] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6b769f21, L:/*********:50491 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.641 [redisson-netty-6-14] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x97205361, L:/*********:50499 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 13:54:11.657 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@75050599: tags=[[amq.ctag-UlG_o0JLQjLBgxO-T32mtQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,8), conn: Proxy@30e9e7ea Shared Rabbit Connection: SimpleConnection@78acdf28 [delegate=amqp://admin@**************:5672/, localPort= 50636], acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.657 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1d73d00a: tags=[[amq.ctag-69_Sjl3QVetHmKD9eeOmLw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,4), conn: Proxy@30e9e7ea Shared Rabbit Connection: SimpleConnection@78acdf28 [delegate=amqp://admin@**************:5672/, localPort= 50636], acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.657 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1abeae00: tags=[[amq.ctag-EcpqXqDYXnCVpG_sx55vzg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,9), conn: Proxy@30e9e7ea Shared Rabbit Connection: SimpleConnection@78acdf28 [delegate=amqp://admin@**************:5672/, localPort= 50636], acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.691 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:54:11.705 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@3b7e34af: tags=[[amq.ctag-lq5Abqvywjl6kBItmiFFRA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,5), conn: Proxy@30e9e7ea Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.705 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@751a1a56: tags=[[amq.ctag-fldPTil2BYCnpV9iIIr0RQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,7), conn: Proxy@30e9e7ea Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.705 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@704b2822: tags=[[amq.ctag-y8Q89kDg3hsHB8eQB0nIfg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,6), conn: Proxy@30e9e7ea Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.705 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@e52e46a: tags=[[amq.ctag-fGRcBo3B6qRS9pzcXMhFWQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@30e9e7ea Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.736 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@107db00: tags=[[amq.ctag--TobZxqnjN6J9Bk1nCndYA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,2), conn: Proxy@30e9e7ea Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.736 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@264a4a0c: tags=[[amq.ctag-sVMbeEWWAy1lro5DJtardw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,3), conn: Proxy@30e9e7ea Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:54:11.768 [lettuce-eventExecutorLoop-1-12] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-07-04 13:54:11.768 [lettuce-eventExecutorLoop-4-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-07-04 13:54:21.846 [lettuce-nioEventLoop-12-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:21.846 [lettuce-nioEventLoop-11-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:21.958 [lettuce-eventExecutorLoop-1-1] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:21.961 [lettuce-eventExecutorLoop-4-5] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:31.965 [lettuce-nioEventLoop-12-3] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:31.965 [lettuce-nioEventLoop-11-3] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:32.061 [lettuce-eventExecutorLoop-1-16] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:32.061 [lettuce-eventExecutorLoop-4-6] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:32.781 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:54:32.781 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createBareChannel(CachingConnectionFactory.java:702)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getCachedChannelProxy(CachingConnectionFactory.java:676)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getChannel(CachingConnectionFactory.java:567)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.access$1600(CachingConnectionFactory.java:102)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory$ChannelCachingConnectionProxy.createChannel(CachingConnectionFactory.java:1439)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2095)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 16 common frames omitted
2025-07-04 13:54:42.070 [lettuce-nioEventLoop-12-4] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:42.070 [lettuce-nioEventLoop-11-4] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:42.166 [lettuce-eventExecutorLoop-1-2] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:42.169 [lettuce-eventExecutorLoop-4-7] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:45.236 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0e84611f, L:/*********:50489 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3d27c422, L:/*********:50487 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6b769f21, L:/*********:50491 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd6c7e99a, L:/*********:50497 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x49ad29e5, L:/*********:50488 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xca961017, L:/*********:50493 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x492a05d6, L:/*********:50496 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x18042756, L:/*********:50495 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x91623a58, L:/*********:50490 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd69548cf, L:/*********:50492 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:45.348 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3a116af7, L:/*********:50494 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x97205361, L:/*********:50499 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x64ce7a40, L:/*********:50500 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4eb8b6ad, L:/*********:50504 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe4a1f062, L:/*********:50501 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x600c8b0f, L:/*********:50503 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x59ddf8e7, L:/*********:50506 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4984f163, L:/*********:50505 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.049 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xbbc3a38d, L:/*********:50502 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.050 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9ba0828e, L:/*********:50508 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.050 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4e5331ea, L:/*********:50507 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.346 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfde81fe5, L:/*********:50511 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.538 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5bebeb03, L:/*********:50512 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.634 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3b71a906, L:/*********:50514 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:46.634 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3ef2cbb7, L:/*********:50513 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-04 13:54:52.186 [lettuce-nioEventLoop-12-5] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:52.186 [lettuce-nioEventLoop-11-5] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:54:52.252 [lettuce-eventExecutorLoop-4-8] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:52.266 [lettuce-eventExecutorLoop-1-3] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:54:53.821 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:54:53.821 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:55:02.278 [lettuce-nioEventLoop-12-6] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:02.278 [lettuce-nioEventLoop-11-6] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:02.358 [lettuce-eventExecutorLoop-1-5] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:02.358 [lettuce-eventExecutorLoop-4-9] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:12.377 [lettuce-nioEventLoop-12-7] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:12.377 [lettuce-nioEventLoop-11-7] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:12.456 [lettuce-eventExecutorLoop-1-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:12.456 [lettuce-eventExecutorLoop-4-10] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:14.857 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:55:14.857 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:55:22.465 [lettuce-nioEventLoop-11-8] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:22.465 [lettuce-nioEventLoop-12-8] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:22.657 [lettuce-eventExecutorLoop-1-8] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:22.657 [lettuce-eventExecutorLoop-4-11] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:32.669 [lettuce-nioEventLoop-11-9] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:32.686 [lettuce-nioEventLoop-12-9] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:32.958 [lettuce-eventExecutorLoop-1-11] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:32.958 [lettuce-eventExecutorLoop-4-12] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:35.918 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:55:35.918 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:55:42.962 [lettuce-nioEventLoop-11-10] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:42.978 [lettuce-nioEventLoop-12-10] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:43.555 [lettuce-eventExecutorLoop-4-13] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:43.571 [lettuce-eventExecutorLoop-1-12] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:53.571 [lettuce-nioEventLoop-12-11] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:53.587 [lettuce-nioEventLoop-11-11] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:55:54.660 [lettuce-eventExecutorLoop-1-13] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:54.660 [lettuce-eventExecutorLoop-4-14] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:55:56.974 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:55:56.974 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:56:04.676 [lettuce-nioEventLoop-11-12] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:56:04.676 [lettuce-nioEventLoop-12-12] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:56:06.758 [lettuce-eventExecutorLoop-4-15] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:56:06.773 [lettuce-eventExecutorLoop-1-14] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:56:16.777 [lettuce-nioEventLoop-12-13] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:56:16.792 [lettuce-nioEventLoop-11-13] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:56:18.024 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-4] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:56:18.024 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:56:20.968 [lettuce-eventExecutorLoop-4-1] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:56:20.968 [lettuce-eventExecutorLoop-1-1] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:56:30.991 [lettuce-nioEventLoop-12-14] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:56:30.991 [lettuce-nioEventLoop-11-14] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:56:39.097 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-4] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:56:39.097 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:56:39.258 [lettuce-eventExecutorLoop-4-16] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:56:39.274 [lettuce-eventExecutorLoop-1-5] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:56:49.277 [lettuce-nioEventLoop-12-15] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:56:49.293 [lettuce-nioEventLoop-11-15] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:57:00.242 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:57:05.341 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 13:57:05.341 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@f37a2c8: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:57:05.758 [lettuce-eventExecutorLoop-1-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:57:05.758 [lettuce-eventExecutorLoop-4-2] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:57:15.774 [lettuce-nioEventLoop-12-16] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:57:15.774 [lettuce-nioEventLoop-11-16] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:57:21.289 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:57:26.481 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 13:57:26.483 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@5ad3ed34: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:57:42.337 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:57:46.029 [lettuce-eventExecutorLoop-1-10] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:57:46.061 [lettuce-eventExecutorLoop-4-3] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:57:47.543 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 13:57:47.543 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@69c2cd5c: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:57:56.073 [lettuce-nioEventLoop-11-1] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:57:56.153 [lettuce-nioEventLoop-12-1] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:58:03.405 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:58:08.603 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 13:58:08.603 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@353deb26: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:58:24.465 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-4] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:58:26.165 [lettuce-eventExecutorLoop-4-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:58:26.165 [lettuce-eventExecutorLoop-1-11] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:58:29.665 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 13:58:29.665 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@792cd67: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:58:36.179 [lettuce-nioEventLoop-11-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:58:36.179 [lettuce-nioEventLoop-12-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:58:45.541 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-4] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:58:45.541 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-4] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:59:06.259 [lettuce-eventExecutorLoop-4-5] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:59:06.275 [lettuce-eventExecutorLoop-1-1] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:59:06.580 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:59:11.782 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-4] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 13:59:11.782 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-4] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@5adb73dd: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:59:16.273 [lettuce-nioEventLoop-12-3] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:59:16.290 [lettuce-nioEventLoop-11-3] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:59:27.615 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-5] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:59:32.802 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-3] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 13:59:32.802 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@6e0bc3dd: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 13:59:46.371 [lettuce-eventExecutorLoop-1-3] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:59:46.371 [lettuce-eventExecutorLoop-4-6] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 13:59:48.670 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-5] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 13:59:48.670 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-5] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 13:59:56.416 [lettuce-nioEventLoop-11-4] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 13:59:56.432 [lettuce-nioEventLoop-12-4] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:00:09.726 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:00:14.932 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-5] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 14:00:14.932 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-5] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@fa94962: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 14:00:26.464 [lettuce-eventExecutorLoop-4-7] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:00:26.464 [lettuce-eventExecutorLoop-1-6] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:00:30.776 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 14:00:30.776 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:00:36.474 [lettuce-nioEventLoop-11-5] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:00:36.474 [lettuce-nioEventLoop-12-5] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:00:51.823 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:00:51.823 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 14:01:06.571 [lettuce-eventExecutorLoop-4-8] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:01:06.571 [lettuce-eventExecutorLoop-1-11] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:01:12.883 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:01:12.883 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 14:01:16.574 [lettuce-nioEventLoop-11-6] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:01:16.574 [lettuce-nioEventLoop-12-6] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:01:33.955 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:01:33.955 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 14:01:46.675 [lettuce-eventExecutorLoop-4-9] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:01:46.675 [lettuce-eventExecutorLoop-1-13] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:01:55.011 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 14:01:55.011 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:01:56.689 [lettuce-nioEventLoop-11-7] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:01:56.689 [lettuce-nioEventLoop-12-7] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:02:16.065 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 14:02:16.065 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:02:26.775 [lettuce-eventExecutorLoop-4-10] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:02:26.775 [lettuce-eventExecutorLoop-1-16] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:02:36.782 [lettuce-nioEventLoop-12-8] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:02:36.782 [lettuce-nioEventLoop-11-8] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:02:37.114 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-4] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:02:42.307 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-3] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 14:02:42.307 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@20e1798d: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 14:02:58.140 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-5] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:03:03.145 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-4] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 14:03:03.145 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-4] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@50599231: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 14:03:06.872 [lettuce-eventExecutorLoop-4-11] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:03:06.872 [lettuce-eventExecutorLoop-1-5] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:03:16.888 [lettuce-nioEventLoop-11-9] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:03:16.888 [lettuce-nioEventLoop-12-9] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:03:19.196 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:03:19.196 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-5] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-04 14:03:40.237 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:03:45.418 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-3] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 14:03:45.419 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@2acec71e: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 14:03:46.982 [lettuce-eventExecutorLoop-4-12] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:03:46.982 [lettuce-eventExecutorLoop-1-7] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:03:56.996 [lettuce-nioEventLoop-12-10] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:03:56.996 [lettuce-nioEventLoop-11-10] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:04:01.306 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:04:06.414 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 14:04:06.414 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@6b9d4656: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 14:04:22.370 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:04:27.066 [lettuce-eventExecutorLoop-4-13] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:04:27.073 [lettuce-eventExecutorLoop-1-12] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:04:27.394 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-3] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 14:04:27.394 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@20d03195: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 14:04:37.081 [lettuce-nioEventLoop-11-11] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:04:37.081 [lettuce-nioEventLoop-12-11] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-04 14:04:43.403 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 14:04:44.774 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#7f914800:31/SimpleConnection@46523bf0 [delegate=amqp://admin@**************:5672/, localPort= 61186]
2025-07-04 14:04:48.598 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3] WARN  SimpleMessageListenerContainer:1459 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-07-04 14:04:48.598 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@e9695b: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-04 14:05:07.168 [lettuce-eventExecutorLoop-4-14] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:05:07.170 [lettuce-eventExecutorLoop-1-15] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-04 14:05:07.297 [lettuce-nioEventLoop-12-12] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-07-04 14:05:07.406 [lettuce-nioEventLoop-11-12] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6e18758a, L:/********:61203 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x407f1b5a, L:/********:61205 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x20dcd503, L:/********:61204 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd92b4781, L:/********:61211 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa20929cb, L:/********:61214 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7b08eb74, L:/********:61206 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x05c492c8, L:/********:61208 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfaeea940, L:/********:61209 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.907 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x26f6bf62, L:/********:61207 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.998 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9b2feb3b, L:/********:61213 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:41:59.998 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb19d907a, L:/********:61215 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:00.098 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1a67a0ad, L:/********:61218 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.305 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd66e74ca, L:/********:61191 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.499 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7ba07cfc, L:/********:61192 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.598 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x73aaf1c3, L:/********:61193 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.598 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x30ca9b7a, L:/********:61199 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.598 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xec0aa6d6, L:/********:61198 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.598 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x58a4ca60, L:/********:61195 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.598 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x26f93195, L:/********:61194 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.600 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5bfbb1a7, L:/********:61197 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.600 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf46dd7de, L:/********:61196 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:29.798 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9d8a574a, L:/********:61200 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:30.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8de5380f, L:/********:61210 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:30.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc6d97307, L:/********:61212 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:42:30.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb01d22d7, L:/********:61217 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:49.502 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x69442c4c, L:/********:63299 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.802 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfb850fc8, L:/********:63353 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa21e8a7b, L:/********:63354 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0f00068a, L:/********:63355 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x276a1dcd, L:/********:63338 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0b1365af, L:/********:63357 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x45750c90, L:/********:63349 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfe4e12c6, L:/********:63351 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x85fdbc4f, L:/********:63339 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:56.900 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x88f0ca76, L:/********:63346 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xefa5b192, L:/********:63344 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x513728e4, L:/********:63364 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8c384213, L:/********:63345 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2280270c, L:/********:63365 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x655f28fe, L:/********:63359 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd63ad8a8, L:/********:63360 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x723cb8f8, L:/********:63356 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 14:43:57.102 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x64663ebc, L:/********:63358 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:18:34.368 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-04 15:18:35.530 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-04 15:18:38.486 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 15:18:38.488 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-04 15:18:38.587 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 95ms. Found 11 Elasticsearch repository interfaces.
2025-07-04 15:18:39.090 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 15:18:39.091 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-04 15:18:39.149 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 57ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-04 15:18:39.161 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 15:18:39.164 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 15:18:39.205 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 35ms. Found 0 Redis repository interfaces.
2025-07-04 15:18:39.721 [main] INFO  GenericScope:295 - BeanFactory id=feeb060e-8851-3d0e-bf4d-93c3be59fc7e
2025-07-04 15:18:39.779 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$3fbb952b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:39.890 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:39.891 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:39.893 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$82d61764] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:39.968 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$1579c3ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.344 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.348 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$89449c66] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.362 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.367 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.432 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.736 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-04 15:18:40.738 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-04 15:18:40.747 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.758 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.787 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.800 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$8a8a1c75] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.898 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$32cf0945] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:40.903 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 15:18:41.428 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-04 15:18:41.444 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-04 15:18:41.445 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-04 15:18:41.445 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-04 15:18:41.749 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-04 15:18:41.749 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 6188 ms
2025-07-04 15:18:42.701 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-04 15:18:42.719 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-04 15:18:47.140 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-04 15:18:50.135 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-04 15:18:54.710 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-04 15:19:00.704 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-04 15:19:00.704 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-04 15:19:00.705 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-04 15:19:00.705 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-04 15:19:00.705 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-04 15:19:00.705 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-04 15:19:00.705 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-04 15:19:10.737 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-04 15:19:10.746 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-04 15:19:10.811 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-04 15:19:10.811 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-04 15:19:10.845 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-04 15:19:10.853 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-04 15:19:10.857 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-04 15:19:10.857 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-04 15:19:10.857 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-04 15:19:10.858 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@72416c9a
2025-07-04 15:19:16.437 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 15:19:16.439 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-04 15:19:19.549 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-04 15:19:22.462 [redisson-netty-6-25] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-04 15:19:23.807 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-04 15:19:25.162 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.163 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.163 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.163 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.204 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.204 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.205 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.205 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 15:19:25.462 [main] TRACE WIRE:64 - [71e28a63] Sending request GET / with parameters: 
2025-07-04 15:19:26.384 [I/O dispatcher 1] TRACE WIRE:97 - [71e28a63] Received raw response: 200 OK
2025-07-04 15:19:26.614 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-04 15:19:26.614 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-04 15:19:26.614 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-04 15:19:26.614 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-04 15:19:26.615 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-04 15:19:51.058 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-04 15:19:52.238 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-04 15:20:08.584 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-04 15:20:08.586 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-04 15:20:08.586 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-04 15:20:08.586 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-04 15:20:09.454 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 15:20:09.455 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-04 15:20:09.995 [main] TRACE WIRE:64 - [15e1af9f] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-04 15:20:10.130 [I/O dispatcher 1] TRACE WIRE:97 - [15e1af9f] Received raw response: 200 OK
2025-07-04 15:20:10.318 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 15:20:10.318 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 15:20:10.374 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 15:20:10.374 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 15:20:20.959 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-04 15:20:21.976 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-04 15:20:22.062 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-04 15:20:22.669 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 15:20:23.865 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#43e49626:0/SimpleConnection@1e19603c [delegate=amqp://admin@**************:5672/, localPort= 65269]
2025-07-04 15:20:34.671 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 121.122 seconds (JVM running for 122.249)
2025-07-04 15:20:34.711 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-04 15:20:35.571 [RMI TCP Connection(41)-********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 15:20:35.572 [RMI TCP Connection(41)-********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-04 15:20:35.830 [RMI TCP Connection(41)-********] INFO  DispatcherServlet:547 - Completed initialization in 257 ms
2025-07-04 15:20:35.953 [RMI TCP Connection(40)-********] TRACE WIRE:64 - [55dbbfac] Sending request GET /_cluster/health/ with parameters: 
2025-07-04 15:20:36.125 [I/O dispatcher 1] TRACE WIRE:97 - [55dbbfac] Received raw response: 200 OK
2025-07-04 15:34:27.881 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T15:34:27.88 | 耗时 279 ms | SQL 语句：
SELECT id,app_id,app_secret,private_key,key_version,app_name,business_type,public_key,call_back_url,is_deleted,create_by,create_time,update_by,update_time FROM douyin_app_config WHERE business_type = 'default' AND is_deleted = 0 limit 1;
2025-07-04 15:34:28.566 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T15:34:28.566 | 耗时 224 ms | SQL 语句：
SELECT id,app_id,app_secret,private_key,key_version,app_name,business_type,public_key,call_back_url,is_deleted,create_by,create_time,update_by,update_time FROM douyin_app_config WHERE business_type = 'default' AND is_deleted = 0 limit 1;
2025-07-04 15:34:28.802 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:774 - 抖音开放平台请求参数:{"client_key":"tt13a865158c9932af01","grant_type":"client_credential","client_secret":"12ac3c42f6b7711443911cc3503cab01deb908c9"},url:https://open.douyin.com/oauth/client_token/
2025-07-04 15:34:29.576 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:784 - 抖音开放平台请求结果:{"data":{"access_token":"clt.970481f65f7b5905bbd22a421d299f57R1HgIh3kFTFMfYKE4Unm64wLK14h_lf","captcha":"","desc_url":"","description":"","error_code":0,"expires_in":7200,"log_id":"20250704153429CAFAA2E5D7571D9D11A5"},"message":"success"}
2025-07-04 15:34:30.083 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:774 - 抖音开放平台请求参数:{episode_id=7508557231474868773, album_id=7507553384406942246, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/play_info/
2025-07-04 15:34:30.451 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:784 - 抖音开放平台请求结果:{"data":{"size":174248843,"url_expire":"1751621370","bitrate":10292312,"codec":"h264","definition":"1080p","format":"mp4","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0\u0026auth_key=1751621805-c3480f8c65694e29a82c7f3eb015fc4c-0-085ca813b24eab8718635ee3e2925c23\u0026br=10051\u0026bt=10051\u0026cd=0%7C0%7C0\u0026ch=0\u0026cr=0\u0026cs=0\u0026dr=0\u0026ds=4\u0026eid=v0279cg10004d0rt0aaljht8scvm7fjg\u0026er=\u0026l=20250704153430E0897C8FEE0A3D72F6E1\u0026lr=\u0026mime_type=video_mp4\u0026net=0\u0026pl=0\u0026qs=13\u0026rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D\u0026vl=\u0026vr="},"err_msg":"","err_no":0,"log_id":"20250704153430F874402825DB08B0B403"}
2025-07-04 15:34:30.452 [http-nio-8082-exec-2] INFO  DouYinApiController:235 - result:OpenApiRes(errNo=0, errMsg=, logId=20250704153430F874402825DB08B0B403, data={"codec":"h264","size":174248843,"play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0&auth_key=1751621805-c3480f8c65694e29a82c7f3eb015fc4c-0-085ca813b24eab8718635ee3e2925c23&br=10051&bt=10051&cd=0%7C0%7C0&ch=0&cr=0&cs=0&dr=0&ds=4&eid=v0279cg10004d0rt0aaljht8scvm7fjg&er=&l=20250704153430E0897C8FEE0A3D72F6E1&lr=&mime_type=video_mp4&net=0&pl=0&qs=13&rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D&vl=&vr=","format":"mp4","bitrate":10292312,"definition":"1080p","url_expire":"1751621370"}, success=null, message=null)
2025-07-04 15:53:29.227 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4803801d, L:/********:65060 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:29.228 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x54052252, L:/********:65054 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:29.228 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xbf3c0db2, L:/********:65053 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:59.295 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcc7653c8, L:/********:65056 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:59.295 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xacfc3445, L:/********:65057 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:59.295 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xee01cbc5, L:0.0.0.0/0.0.0.0:65059]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:59.295 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0ed08f13, L:/********:65052 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:59.295 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaad8e413, L:0.0.0.0/0.0.0.0:65055]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:59.997 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2ddda42e, L:/********:65069 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:53:59.997 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x61700172, L:/********:65066 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.101 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc037193f, L:0.0.0.0/0.0.0.0:65068]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.101 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe72e8c00, L:/********:65071 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.101 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x48bab3e4, L:/********:65067 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.101 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1a44b160, L:/********:65070 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.101 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x45a70745, L:0.0.0.0/0.0.0.0:65074]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.101 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5946cb20, L:0.0.0.0/0.0.0.0:65075 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.101 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x97a753ba, L:/********:65072 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.113 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3ce3d75e, L:/********:65073 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.617 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7c1821a5, L:/********:65077 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.617 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xbd6c47ec, L:/********:65076 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.710 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3e2947cc, L:/********:65078 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 15:54:00.711 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcb30778c, L:/********:65079 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 16:09:05.158 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-07-04 16:09:15.856 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-04 16:09:17.378 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-04 16:09:19.506 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 16:09:19.508 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-04 16:09:19.623 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 110ms. Found 11 Elasticsearch repository interfaces.
2025-07-04 16:09:20.236 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 16:09:20.237 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-04 16:09:20.301 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 63ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-04 16:09:20.314 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-04 16:09:20.316 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 16:09:20.363 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 39ms. Found 0 Redis repository interfaces.
2025-07-04 16:09:20.935 [main] INFO  GenericScope:295 - BeanFactory id=feeb060e-8851-3d0e-bf4d-93c3be59fc7e
2025-07-04 16:09:21.008 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$f984068b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.133 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.134 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.136 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$3c9e88c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.223 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$cf42351a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.619 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.622 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$430d0dc6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.635 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.640 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.708 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:21.983 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-04 16:09:21.985 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-04 16:09:21.993 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:22.003 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:22.032 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:22.045 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$44528dd5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:22.139 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$ec977aa5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:22.145 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 16:09:22.643 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-04 16:09:22.658 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-04 16:09:22.659 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-04 16:09:22.659 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-04 16:09:22.940 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-04 16:09:22.941 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5523 ms
2025-07-04 16:09:23.727 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-04 16:09:23.741 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-04 16:09:28.115 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-04 16:09:32.208 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-04 16:09:37.147 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-04 16:09:42.149 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-04 16:09:42.149 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-04 16:09:42.150 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-04 16:09:42.150 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-04 16:09:42.150 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-04 16:09:42.150 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-04 16:09:42.150 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-04 16:09:49.267 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-04 16:09:49.274 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-04 16:09:49.314 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-04 16:09:49.314 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-04 16:09:49.332 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-04 16:09:49.338 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-04 16:09:49.340 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-04 16:09:49.341 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-04 16:09:49.341 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-04 16:09:49.341 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1c1f348f
2025-07-04 16:09:53.301 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 16:09:53.304 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-04 16:09:56.192 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-04 16:09:58.325 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-04 16:09:59.823 [redisson-netty-6-15] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-04 16:10:01.070 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.071 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.071 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.071 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.113 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.114 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.114 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.114 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-04 16:10:01.358 [main] TRACE WIRE:64 - [5a5b0e14] Sending request GET / with parameters: 
2025-07-04 16:10:01.794 [I/O dispatcher 1] TRACE WIRE:97 - [5a5b0e14] Received raw response: 200 OK
2025-07-04 16:10:02.000 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-04 16:10:02.000 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-04 16:10:02.000 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-04 16:10:02.001 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-04 16:10:02.001 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-04 16:10:22.228 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-04 16:10:24.427 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-04 16:10:41.195 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-04 16:10:41.195 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-04 16:10:41.196 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-04 16:10:41.196 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-04 16:10:42.427 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-04 16:10:42.428 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-04 16:10:42.823 [main] TRACE WIRE:64 - [6f886e9b] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-04 16:10:43.069 [I/O dispatcher 1] TRACE WIRE:97 - [6f886e9b] Received raw response: 200 OK
2025-07-04 16:10:43.189 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 16:10:43.189 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 16:10:43.220 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-04 16:10:43.220 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-04 16:10:48.378 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-04 16:10:48.897 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-04 16:10:48.944 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-04 16:10:49.203 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-04 16:10:50.249 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#31c1d078:0/SimpleConnection@5e851c46 [delegate=amqp://admin@**************:5672/, localPort= 50765]
2025-07-04 16:11:01.281 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 106.189 seconds (JVM running for 107.231)
2025-07-04 16:11:01.307 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-04 16:11:01.741 [RMI TCP Connection(53)-********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 16:11:01.741 [RMI TCP Connection(53)-********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-04 16:11:01.786 [RMI TCP Connection(53)-********] INFO  DispatcherServlet:547 - Completed initialization in 44 ms
2025-07-04 16:11:01.933 [RMI TCP Connection(50)-********] TRACE WIRE:64 - [554b5b05] Sending request GET /_cluster/health/ with parameters: 
2025-07-04 16:11:02.077 [I/O dispatcher 1] TRACE WIRE:97 - [554b5b05] Received raw response: 200 OK
2025-07-04 16:35:34.684 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T16:35:34.683 | 耗时 159 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,member_status,pay_flat,last_operate_time,source,create_time,update_time FROM duan_ju_user WHERE open_id = '123';
2025-07-04 16:35:35.627 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-04T16:35:35.626 | 耗时 274 ms | SQL 语句：
INSERT INTO duan_ju_user ( nick_name, open_id, register_time, member_status, pay_flat, last_operate_time, source, create_time, update_time ) VALUES ( '用户1751618135169', '123', '2025-07-04T16:35:35.169+0800', 1, 0, '2025-07-04T16:35:35.169+0800', 'app', '2025-07-04T16:35:35.169+0800', '2025-07-04T16:35:35.169+0800' );
2025-07-04 16:35:35.635 [http-nio-8082-exec-2] INFO  DuanJuUserServiceImpl:59 - 新用户注册成功，用户ID: 10002, openId: 123
2025-07-04 16:35:36.128 [http-nio-8082-exec-2] INFO  TokenServiceImpl:73 - 生成token成功，userId: 10002, tokenType: default, token: 248d94edfa434425906be9251aa0ee18
2025-07-04 16:35:50.595 [http-nio-8082-exec-6] WARN  TokenValidationAspect:78 - Token为空，请求路径: /cms-vrbt-miniapp/miniApi/duanju/api/updateViewingHistory
2025-07-04 16:35:50.604 [http-nio-8082-exec-6] WARN  TokenExceptionHandler:33 - Token校验异常: Token校验失败: Token不能为空
2025-07-04 16:39:04.384 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-07-04T16:39:04.384 | 耗时 129 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,member_status,pay_flat,last_operate_time,source,create_time,update_time FROM duan_ju_user WHERE open_id = '1234';
2025-07-04 16:39:04.618 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-07-04T16:39:04.618 | 耗时 225 ms | SQL 语句：
INSERT INTO duan_ju_user ( nick_name, open_id, register_time, member_status, pay_flat, last_operate_time, source, create_time, update_time ) VALUES ( '用户1751618344385', '1234', '2025-07-04T16:39:04.385+0800', 1, 0, '2025-07-04T16:39:04.385+0800', 'app', '2025-07-04T16:39:04.385+0800', '2025-07-04T16:39:04.385+0800' );
2025-07-04 16:39:04.625 [http-nio-8082-exec-9] INFO  DuanJuUserServiceImpl:59 - 新用户注册成功，用户ID: 10003, openId: 1234
2025-07-04 16:39:04.726 [http-nio-8082-exec-9] INFO  TokenServiceImpl:73 - 生成token成功，userId: 10003, tokenType: default, token: 26833b19b512407b98144cbb45b8e522
2025-07-04 16:39:22.072 [http-nio-8082-exec-10] INFO  p6spy:60 - 2025-07-04T16:39:22.072 | 耗时 171 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,member_status,pay_flat,last_operate_time,source,create_time,update_time FROM duan_ju_user WHERE id='10003' ;
2025-07-04 16:39:22.474 [http-nio-8082-exec-10] INFO  p6spy:60 - 2025-07-04T16:39:22.474 | 耗时 366 ms | SQL 语句：
SELECT id,user_id,drama_id,album_id,episode_num,watch_duration,dou_yin_episode_id,open_id,create_time,update_time FROM mini_app_drama_viewing_history WHERE id='1940719536879292417' ;
2025-07-04 16:39:22.964 [http-nio-8082-exec-10] INFO  p6spy:60 - 2025-07-04T16:39:22.964 | 耗时 428 ms | SQL 语句：
UPDATE mini_app_drama_viewing_history SET user_id='123', album_id='7517177146540818995', episode_num=5, watch_duration=123, dou_yin_episode_id='7515324088408080946', create_time='2025-07-03T18:29:29.000+0800', update_time='2025-07-04T16:39:22.477+0800' WHERE id='1940719536879292417';
2025-07-04 17:26:13.562 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb152219c, L:/********:50591 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:13.562 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x41ccf27c, L:/********:50597 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:15.800 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x31e28f6d, L:0.0.0.0/0.0.0.0:50603 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:15.800 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x07c4335c, L:/********:50607 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:15.800 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3534dcc0, L:/********:50605 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:15.803 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x51025b96, L:/********:50611 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:15.803 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2efdde4f, L:/********:50604 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:43.593 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3acdd7a3, L:/********:50601 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:43.593 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe69eff1e, L:/********:50598 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:45.896 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xbbf39777, L:/********:50610 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:45.896 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1f9b5c8c, L:/********:50609 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:45.898 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe9e51505, L:/********:50608 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:45.903 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd17e9380, L:/********:50602 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:45.903 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1e9f8a35, L:/********:50615 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:26:45.903 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x02820af3, L:/********:50614 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:27:15.993 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaacec878, L:/********:50613 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:27:15.994 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x802aa21a, L:/********:50612 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:02.406 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc03350ed, L:/********:53217 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:02.693 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2b09c114, L:/********:53224 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:02.693 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x448e99fb, L:/********:53225 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:02.693 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x938e88e9, L:/********:53227 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:32.501 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xca26e21d, L:/********:53215 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:32.502 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x172a2965, L:/********:53220 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:32.502 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7934dc6a, L:/********:53219 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:32.504 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9e932b2f, L:/********:53218 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:44.403 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x33f42e41, L:0.0.0.0/0.0.0.0:50596 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 17:30:44.403 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9283425a, L:0.0.0.0/0.0.0.0:50593]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-04 18:00:16.611 [http-nio-8082-exec-10] INFO  p6spy:60 - 2025-07-04T18:00:16.611 | 耗时 355 ms | SQL 语句：
UPDATE ai_union_ai_face_template SET template_name='花木兰', template_remark='昔有奇女子，替父赴戎行。 战甲遮娇面，英名万古扬。', template_type=0, activity_id='at_1860880104954122240', material_id='mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176', template_id='e5db4ee787dc427fb3defd08d3024629', order_by=99, pic_url='https://appmedia.kunpengtn.com/common/1742281034070.png', video_url='https://appmedia.kunpengtn.com/common/1742281057909.mp4', status=0, clips_param='{"pic1":"960:540","pic2":"960:540","pic3":"960:540"}', topic_type=1, update_by='admin', update_time='2025-07-04T18:00:15.871+0800' WHERE id='11';
2025-07-04 18:00:17.985 [http-nio-8082-exec-10] INFO  p6spy:60 - 2025-07-04T18:00:17.985 | 耗时 258 ms | SQL 语句：
INSERT INTO sys_log ( id, create_by, create_time, cost_time, ip, request_param, method, username, userid, log_content, log_type, operate_type ) VALUES ( '1941074579012767746', 'admin', '2025-07-04T18:00:17.565+0800', 1551, '0:0:0:0:0:0:0:1', '[{"activityId":"at_1860880104954122240","clipsParam":"{\"pic1\":\"960:540\",\"pic2\":\"960:540\",\"pic3\":\"960:540\"}","id":"11","materialId":"mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176","orderBy":99,"picUrl":"https://appmedia.kunpengtn.com/common/1742281034070.png","status":0,"templateId":"e5db4ee787dc427fb3defd08d3024629","templateName":"花木兰","templateRemark":"昔有奇女子，替父赴戎行。\n战甲遮娇面，英名万古扬。","templateType":0,"topicType":1,"updateBy":"admin","updateTime":1751623215871,"videoUrl":"https://appmedia.kunpengtn.com/common/1742281057909.mp4"}]', 'com.eleven.cms.aiunion.controller.template.AiUnionAiFaceTemplateController.edit()', '管理员', 'admin', 'ai_union_ai_face_template-编辑', 2, 3 );
2025-07-04 21:46:18.675 [pool-4-thread-2] INFO  AliMnsService:576 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.http.impl.nio.reactor.SessionInputBufferImpl.fill(SessionInputBufferImpl.java:231)
	at org.apache.http.impl.nio.codecs.AbstractMessageParser.fillBuffer(AbstractMessageParser.java:136)
	at org.apache.http.impl.nio.DefaultNHttpClientConnection.consumeInput(DefaultNHttpClientConnection.java:241)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:81)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:114)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	... 18 common frames omitted
