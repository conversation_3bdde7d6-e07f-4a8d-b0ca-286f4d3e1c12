2025-08-03 00:00:00.053 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 00:00:00.404 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T00:00:00.404 | 耗时 148 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T00:00:00.071+0800' AND is_deleted = 0;
2025-08-03 00:00:00.405 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 00:00:00.405 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 334 ms
2025-08-03 00:30:00.044 [schedule-pool-7] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 00:30:00.494 [schedule-pool-7] INFO  p6spy:60 - 2025-08-03T00:30:00.493 | 耗时 303 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T00:30:00.046+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 00:30:00.496 [schedule-pool-7] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 00:30:00.496 [schedule-pool-7] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 450 ms
2025-08-03 01:00:00.049 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 01:00:00.319 [schedule-pool-1] INFO  p6spy:60 - 2025-08-03T01:00:00.319 | 耗时 125 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T01:00:00.050+0800' AND is_deleted = 0;
2025-08-03 01:00:00.320 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 01:00:00.320 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 270 ms
2025-08-03 01:30:00.050 [schedule-pool-6] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 01:30:00.381 [schedule-pool-6] INFO  p6spy:60 - 2025-08-03T01:30:00.381 | 耗时 177 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T01:30:00.051+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 01:30:00.384 [schedule-pool-6] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 01:30:00.385 [schedule-pool-6] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 334 ms
2025-08-03 02:00:00.046 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 02:00:00.544 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T02:00:00.544 | 耗时 287 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T02:00:00.046+0800' AND is_deleted = 0;
2025-08-03 02:00:00.548 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 02:00:00.549 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 503 ms
2025-08-03 02:30:00.049 [schedule-pool-0] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 02:30:00.420 [schedule-pool-0] INFO  p6spy:60 - 2025-08-03T02:30:00.42 | 耗时 188 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T02:30:00.049+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 02:30:00.423 [schedule-pool-0] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 02:30:00.424 [schedule-pool-0] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 375 ms
2025-08-03 03:00:00.059 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 03:00:00.465 [schedule-pool-1] INFO  p6spy:60 - 2025-08-03T03:00:00.465 | 耗时 255 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T03:00:00.062+0800' AND is_deleted = 0;
2025-08-03 03:00:00.467 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 03:00:00.469 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 407 ms
2025-08-03 03:30:00.047 [schedule-pool-4] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 03:30:00.972 [schedule-pool-4] INFO  p6spy:60 - 2025-08-03T03:30:00.972 | 耗时 319 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T03:30:00.048+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 03:30:00.977 [schedule-pool-4] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 03:30:00.978 [schedule-pool-4] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 930 ms
2025-08-03 03:42:40.706 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	... 11 common frames omitted
2025-08-03 04:00:00.052 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 04:00:00.378 [schedule-pool-1] INFO  p6spy:60 - 2025-08-03T04:00:00.378 | 耗时 125 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T04:00:00.053+0800' AND is_deleted = 0;
2025-08-03 04:00:00.382 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 04:00:00.383 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 330 ms
2025-08-03 04:30:00.055 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 04:30:00.466 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T04:30:00.466 | 耗时 218 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T04:30:00.055+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 04:30:00.467 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 04:30:00.468 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 413 ms
2025-08-03 05:00:00.045 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 05:00:00.603 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T05:00:00.603 | 耗时 327 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T05:00:00.046+0800' AND is_deleted = 0;
2025-08-03 05:00:00.604 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 05:00:00.606 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 560 ms
2025-08-03 05:30:00.046 [schedule-pool-9] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 05:30:00.747 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T05:30:00.746 | 耗时 137 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T05:30:00.046+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 05:30:00.753 [schedule-pool-9] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 05:30:00.755 [schedule-pool-9] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 709 ms
2025-08-03 06:00:00.083 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 06:00:00.561 [schedule-pool-1] INFO  p6spy:60 - 2025-08-03T06:00:00.56 | 耗时 247 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T06:00:00.086+0800' AND is_deleted = 0;
2025-08-03 06:00:00.561 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 06:00:00.562 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 476 ms
2025-08-03 06:30:00.113 [schedule-pool-6] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 06:30:00.425 [schedule-pool-6] INFO  p6spy:60 - 2025-08-03T06:30:00.425 | 耗时 137 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T06:30:00.114+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 06:30:00.426 [schedule-pool-6] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 06:30:00.426 [schedule-pool-6] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 312 ms
2025-08-03 07:00:00.113 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 07:00:00.712 [schedule-pool-6] INFO  p6spy:60 - 2025-08-03T07:00:00.711 | 耗时 184 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T07:00:00.114+0800' AND is_deleted = 0;
2025-08-03 07:00:00.715 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 07:00:00.716 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 602 ms
2025-08-03 07:30:00.066 [schedule-pool-6] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 07:30:00.525 [schedule-pool-6] INFO  p6spy:60 - 2025-08-03T07:30:00.525 | 耗时 227 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T07:30:00.067+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 07:30:00.526 [schedule-pool-6] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 07:30:00.527 [schedule-pool-6] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 460 ms
2025-08-03 08:00:00.047 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 08:00:00.495 [schedule-pool-4] INFO  p6spy:60 - 2025-08-03T08:00:00.494 | 耗时 241 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T08:00:00.047+0800' AND is_deleted = 0;
2025-08-03 08:00:00.497 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 08:00:00.499 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 452 ms
2025-08-03 08:30:00.051 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 08:30:00.656 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T08:30:00.656 | 耗时 264 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T08:30:00.051+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 08:30:00.659 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 08:30:00.663 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 612 ms
2025-08-03 09:00:00.057 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 09:00:00.734 [schedule-pool-4] INFO  p6spy:60 - 2025-08-03T09:00:00.734 | 耗时 348 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T09:00:00.058+0800' AND is_deleted = 0;
2025-08-03 09:00:00.735 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 09:00:00.736 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 678 ms
2025-08-03 09:30:00.054 [schedule-pool-9] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 09:30:00.626 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T09:30:00.626 | 耗时 346 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T09:30:00.055+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 09:30:00.627 [schedule-pool-9] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 09:30:00.628 [schedule-pool-9] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 573 ms
2025-08-03 10:00:00.046 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 10:00:00.518 [schedule-pool-3] INFO  p6spy:60 - 2025-08-03T10:00:00.518 | 耗时 224 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T10:00:00.047+0800' AND is_deleted = 0;
2025-08-03 10:00:00.520 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 10:00:00.521 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 474 ms
2025-08-03 10:30:00.064 [schedule-pool-9] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 10:30:00.473 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T10:30:00.473 | 耗时 203 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T10:30:00.065+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 10:30:00.473 [schedule-pool-9] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 10:30:00.473 [schedule-pool-9] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 408 ms
2025-08-03 11:00:00.097 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 11:00:00.480 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T11:00:00.48 | 耗时 204 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T11:00:00.098+0800' AND is_deleted = 0;
2025-08-03 11:00:00.482 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 11:00:00.482 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 384 ms
2025-08-03 11:30:00.073 [schedule-pool-7] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 11:30:00.587 [schedule-pool-7] INFO  p6spy:60 - 2025-08-03T11:30:00.587 | 耗时 307 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T11:30:00.074+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 11:30:00.591 [schedule-pool-7] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 11:30:00.592 [schedule-pool-7] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 518 ms
2025-08-03 12:00:00.057 [schedule-pool-8] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 12:00:00.715 [schedule-pool-8] INFO  p6spy:60 - 2025-08-03T12:00:00.714 | 耗时 336 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T12:00:00.058+0800' AND is_deleted = 0;
2025-08-03 12:00:00.719 [schedule-pool-8] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 12:00:00.721 [schedule-pool-8] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 663 ms
2025-08-03 12:30:00.051 [schedule-pool-1] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 12:30:00.780 [schedule-pool-1] INFO  p6spy:60 - 2025-08-03T12:30:00.78 | 耗时 280 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T12:30:00.053+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 12:30:00.783 [schedule-pool-1] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 12:30:00.786 [schedule-pool-1] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 733 ms
2025-08-03 13:00:00.052 [schedule-pool-5] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 13:00:00.773 [schedule-pool-5] INFO  p6spy:60 - 2025-08-03T13:00:00.773 | 耗时 145 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T13:00:00.055+0800' AND is_deleted = 0;
2025-08-03 13:00:00.774 [schedule-pool-5] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 13:00:00.777 [schedule-pool-5] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 722 ms
2025-08-03 13:30:00.046 [schedule-pool-9] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 13:30:00.463 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T13:30:00.462 | 耗时 288 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T13:30:00.047+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 13:30:00.466 [schedule-pool-9] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 13:30:00.467 [schedule-pool-9] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 420 ms
2025-08-03 14:00:00.054 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 14:00:00.660 [schedule-pool-6] INFO  p6spy:60 - 2025-08-03T14:00:00.66 | 耗时 397 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T14:00:00.057+0800' AND is_deleted = 0;
2025-08-03 14:00:00.661 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 14:00:00.661 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 604 ms
2025-08-03 14:30:00.059 [schedule-pool-8] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 14:30:00.658 [schedule-pool-8] INFO  p6spy:60 - 2025-08-03T14:30:00.657 | 耗时 215 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T14:30:00.059+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 14:30:00.660 [schedule-pool-8] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 14:30:00.661 [schedule-pool-8] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 602 ms
2025-08-03 15:00:00.057 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 15:00:00.455 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T15:00:00.455 | 耗时 202 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T15:00:00.059+0800' AND is_deleted = 0;
2025-08-03 15:00:00.456 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 15:00:00.456 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 397 ms
2025-08-03 15:30:00.092 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 15:30:00.582 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T15:30:00.582 | 耗时 239 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T15:30:00.094+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 15:30:00.588 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 15:30:00.589 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 495 ms
2025-08-03 16:00:00.108 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 16:00:00.497 [schedule-pool-4] INFO  p6spy:60 - 2025-08-03T16:00:00.496 | 耗时 203 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T16:00:00.108+0800' AND is_deleted = 0;
2025-08-03 16:00:00.499 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 16:00:00.500 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 392 ms
2025-08-03 16:30:00.074 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 16:30:00.490 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T16:30:00.489 | 耗时 200 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T16:30:00.075+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 16:30:00.493 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 16:30:00.494 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 419 ms
2025-08-03 17:00:00.050 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 17:00:00.589 [schedule-pool-4] INFO  p6spy:60 - 2025-08-03T17:00:00.588 | 耗时 305 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T17:00:00.051+0800' AND is_deleted = 0;
2025-08-03 17:00:00.591 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 17:00:00.593 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 542 ms
2025-08-03 17:30:00.058 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 17:30:00.502 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T17:30:00.502 | 耗时 222 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T17:30:00.058+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 17:30:00.505 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 17:30:00.505 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 447 ms
2025-08-03 18:00:00.051 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 18:00:00.475 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T18:00:00.475 | 耗时 242 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T18:00:00.052+0800' AND is_deleted = 0;
2025-08-03 18:00:00.477 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 18:00:00.478 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 426 ms
2025-08-03 18:30:00.059 [schedule-pool-9] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 18:30:00.573 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T18:30:00.573 | 耗时 376 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T18:30:00.060+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 18:30:00.574 [schedule-pool-9] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 18:30:00.575 [schedule-pool-9] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 515 ms
2025-08-03 19:00:00.055 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 19:00:00.567 [schedule-pool-9] INFO  p6spy:60 - 2025-08-03T19:00:00.567 | 耗时 306 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T19:00:00.056+0800' AND is_deleted = 0;
2025-08-03 19:00:00.569 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 19:00:00.571 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 515 ms
2025-08-03 19:30:00.059 [schedule-pool-2] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 19:30:00.463 [schedule-pool-2] INFO  p6spy:60 - 2025-08-03T19:30:00.463 | 耗时 203 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T19:30:00.061+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 19:30:00.465 [schedule-pool-2] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 19:30:00.466 [schedule-pool-2] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 405 ms
2025-08-03 20:00:00.073 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 20:00:00.572 [schedule-pool-7] INFO  p6spy:60 - 2025-08-03T20:00:00.572 | 耗时 248 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T20:00:00.074+0800' AND is_deleted = 0;
2025-08-03 20:00:00.575 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 20:00:00.576 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 502 ms
2025-08-03 20:30:00.070 [schedule-pool-7] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 20:30:00.356 [schedule-pool-7] INFO  p6spy:60 - 2025-08-03T20:30:00.356 | 耗时 128 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T20:30:00.071+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 20:30:00.357 [schedule-pool-7] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 20:30:00.357 [schedule-pool-7] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 286 ms
2025-08-03 21:00:00.064 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 21:00:00.568 [schedule-pool-3] INFO  p6spy:60 - 2025-08-03T21:00:00.568 | 耗时 381 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T21:00:00.066+0800' AND is_deleted = 0;
2025-08-03 21:00:00.570 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 21:00:00.571 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 505 ms
2025-08-03 21:30:00.062 [schedule-pool-1] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 21:30:00.564 [schedule-pool-1] INFO  p6spy:60 - 2025-08-03T21:30:00.564 | 耗时 205 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T21:30:00.063+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 21:30:00.566 [schedule-pool-1] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 21:30:00.567 [schedule-pool-1] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 504 ms
2025-08-03 22:00:00.051 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 22:00:00.416 [schedule-pool-7] INFO  p6spy:60 - 2025-08-03T22:00:00.415 | 耗时 173 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T22:00:00.051+0800' AND is_deleted = 0;
2025-08-03 22:00:00.417 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 22:00:00.418 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 367 ms
2025-08-03 22:30:00.049 [schedule-pool-3] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 22:30:00.348 [schedule-pool-3] INFO  p6spy:60 - 2025-08-03T22:30:00.348 | 耗时 134 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T22:30:00.050+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 22:30:00.349 [schedule-pool-3] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 22:30:00.349 [schedule-pool-3] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 299 ms
2025-08-03 23:00:00.053 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-03 23:00:00.445 [schedule-pool-7] INFO  p6spy:60 - 2025-08-03T23:00:00.445 | 耗时 182 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-03T23:00:00.054+0800' AND is_deleted = 0;
2025-08-03 23:00:00.447 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-03 23:00:00.448 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 394 ms
2025-08-03 23:30:00.054 [schedule-pool-0] INFO  UserExpirationCheckTask:42 - 开始执行用户过期检查定时任务
2025-08-03 23:30:00.746 [schedule-pool-0] INFO  p6spy:60 - 2025-08-03T23:30:00.745 | 耗时 334 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-03T23:30:00.054+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-03 23:30:00.746 [schedule-pool-0] INFO  UserExpirationCheckTask:82 - 没有需要处理的过期用户
2025-08-03 23:30:00.747 [schedule-pool-0] INFO  UserExpirationCheckTask:53 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 693 ms
