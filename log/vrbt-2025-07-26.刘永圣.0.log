2025-07-26 00:00:00.053 [schedule-pool-8] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 00:00:00.287 [schedule-pool-8] INFO  p6spy:60 - 2025-07-26T00:00:00.287 | 耗时 100 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T00:00:00.089+0800' AND is_deleted = 0;
2025-07-26 00:00:00.288 [schedule-pool-8] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 00:00:00.288 [schedule-pool-8] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 199 ms
2025-07-26 00:30:00.054 [schedule-pool-7] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 00:30:00.487 [schedule-pool-7] INFO  p6spy:60 - 2025-07-26T00:30:00.487 | 耗时 203 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T00:30:00.056+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 00:30:00.487 [schedule-pool-7] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 00:30:00.488 [schedule-pool-7] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 432 ms
2025-07-26 01:00:00.057 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 01:00:00.379 [schedule-pool-3] INFO  p6spy:60 - 2025-07-26T01:00:00.379 | 耗时 183 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T01:00:00.057+0800' AND is_deleted = 0;
2025-07-26 01:00:00.380 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 01:00:00.380 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 323 ms
2025-07-26 01:30:00.054 [schedule-pool-4] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 01:30:00.442 [schedule-pool-4] INFO  p6spy:60 - 2025-07-26T01:30:00.442 | 耗时 161 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T01:30:00.054+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 01:30:00.443 [schedule-pool-4] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 01:30:00.443 [schedule-pool-4] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 389 ms
2025-07-26 02:00:00.063 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 02:00:00.491 [schedule-pool-9] INFO  p6spy:60 - 2025-07-26T02:00:00.491 | 耗时 223 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T02:00:00.063+0800' AND is_deleted = 0;
2025-07-26 02:00:00.492 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 02:00:00.492 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 429 ms
2025-07-26 02:30:00.093 [schedule-pool-3] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 02:30:00.502 [schedule-pool-3] INFO  p6spy:60 - 2025-07-26T02:30:00.502 | 耗时 205 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T02:30:00.093+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 02:30:00.503 [schedule-pool-3] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 02:30:00.503 [schedule-pool-3] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 410 ms
2025-07-26 03:00:00.132 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 03:00:00.435 [schedule-pool-9] INFO  p6spy:60 - 2025-07-26T03:00:00.434 | 耗时 182 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T03:00:00.132+0800' AND is_deleted = 0;
2025-07-26 03:00:00.435 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 03:00:00.435 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 303 ms
2025-07-26 03:30:00.111 [schedule-pool-9] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 03:30:00.537 [schedule-pool-9] INFO  p6spy:60 - 2025-07-26T03:30:00.537 | 耗时 239 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T03:30:00.111+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 03:30:00.538 [schedule-pool-9] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 03:30:00.538 [schedule-pool-9] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 427 ms
2025-07-26 03:42:11.123 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x29bf382d, L:/********:61200 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-7] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x2b666d43, L:/********:61198 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-27] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x8cace31a, L:/********:61188 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-28] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x38aedee8, L:/********:61190 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-29] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x37b49ff1, L:/********:61189 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-8] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x06569046, L:/********:62328 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-11] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x062225f9, L:/********:62329 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x69efa089, L:/********:62330 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-25] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x052db816, L:/********:61186 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-15] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x052e0c0e, L:/********:62337 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-14] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xfb208202, L:/********:62336 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-21] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x3bb0731d, L:/********:64538 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6b294463, L:/********:61176 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-18] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x718d5cad, L:/********:62338 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-2] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x3a4db30a, L:/********:61181 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-16] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x9c1eefe6, L:/********:62334 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-17] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x54d93037, L:/********:62335 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-12] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd851423e, L:/********:62332 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-4] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x27e0cd3b, L:/********:61183 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-13] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x8054670e, L:/********:62333 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.123 [redisson-netty-6-31] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x5484cfe2, L:/********:61192 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.128 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x07984c9d, L:/********:62331 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.128 [redisson-netty-6-11] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x53ec9d2f, L:/********:61178 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.128 [redisson-netty-6-8] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc61aa6fe, L:/********:61199 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.128 [redisson-netty-6-14] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x5ef85fb3, L:/********:61185 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.183 [AMQP Connection **************:5672] WARN  ForgivingExceptionHandler:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-26 03:42:11.194 [lettuce-nioEventLoop-12-1] INFO  CommandHandler:217 - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 03:42:11.240 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.247 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.253 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.256 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.257 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.258 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.258 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.259 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.260 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.261 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.262 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.263 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-26 03:42:11.313 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@590b7d48: tags=[[amq.ctag-0FrPMVxQXADVWdYsAmzgEA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,10), conn: Proxy@2e86c629 Shared Rabbit Connection: SimpleConnection@73017377 [delegate=amqp://admin@**************:5672/, localPort= 61412], acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:11.338 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-26 03:42:11.347 [lettuce-eventExecutorLoop-4-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /8.140.21.7:6379
2025-07-26 03:42:11.355 [lettuce-eventExecutorLoop-1-14] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-07-26 03:42:11.489 [lettuce-nioEventLoop-12-2] INFO  ReconnectionHandler:194 - Reconnected to 8.140.21.7:6379
2025-07-26 03:42:11.621 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@d7e706c: tags=[[amq.ctag-XBUjyA0rLZDLSuybuALd7Q]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,9), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:11.731 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@237f66a0: tags=[[amq.ctag-6bIy5e-GdD4zGyggb_HUlg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,8), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:11.737 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@6880f559: tags=[[amq.ctag-mKA24JOeGUsSp_FISuPaRw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,11), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:11.791 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@69a9d915: tags=[[amq.ctag-JtjDG6kG3sP7mKdVwGSTEA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,5), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:11.794 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@29144af1: tags=[[amq.ctag-M-fbSDSHUcBGkjZtMXuLOw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,7), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:11.794 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@682e459b: tags=[[amq.ctag-W7i1DCUvRpMEN5B7UtgJYA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,3), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:11.794 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@6aa13003: tags=[[amq.ctag-XTBT8YUb4KdZHeFofxEjdw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:12.042 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@65f712f4: tags=[[amq.ctag-X-PMzyb5wQF3z33JwWAj3g]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,4), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:12.167 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@7759d7b1: tags=[[amq.ctag-cYR_uwPxX4Zs0EMe8_aCMg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,6), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:12.167 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@40b093fd: tags=[[amq.ctag-6A_odkDgLRDHrdeiv_of3Q]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,2), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-26 03:42:21.386 [lettuce-nioEventLoop-11-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-26 03:42:21.443 [lettuce-eventExecutorLoop-1-2] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-26 03:42:21.690 [lettuce-nioEventLoop-11-3] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-07-26 03:42:32.409 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-26 03:42:32.409 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createBareChannel(CachingConnectionFactory.java:702)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getCachedChannelProxy(CachingConnectionFactory.java:676)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getChannel(CachingConnectionFactory.java:567)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.access$1600(CachingConnectionFactory.java:102)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory$ChannelCachingConnectionProxy.createChannel(CachingConnectionFactory.java:1439)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2095)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 16 common frames omitted
2025-07-26 03:42:33.409 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#1393dd72:2/SimpleConnection@7a0f60f2 [delegate=amqp://admin@**************:5672/, localPort= 60006]
2025-07-26 03:42:40.962 [pool-4-thread-2] INFO  AliMnsService:576 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	... 11 common frames omitted
2025-07-26 03:42:41.940 [pool-4-thread-1] INFO  AliMnsService:496 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-3 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-3 [ACTIVE]
	... 11 common frames omitted
2025-07-26 03:42:42.034 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-2 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-2 [ACTIVE]
	... 11 common frames omitted
2025-07-26 03:42:48.841 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x53ec9d2f, L:/********:61178 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:50.040 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3a4db30a, L:/********:61181 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:50.040 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6b294463, L:/********:61176 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:50.043 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x27e0cd3b, L:/********:61183 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:50.043 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8cace31a, L:/********:61188 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:50.043 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5ef85fb3, L:/********:61185 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:50.043 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x052db816, L:/********:61186 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.041 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x38aedee8, L:/********:61190 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.041 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x37b49ff1, L:/********:61189 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.041 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5484cfe2, L:/********:61192 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.042 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc61aa6fe, L:/********:61199 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.042 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x29bf382d, L:/********:61200 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.042 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2b666d43, L:/********:61198 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.042 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd851423e, L:/********:62332 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.042 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x06569046, L:/********:62328 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.042 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x07984c9d, L:/********:62331 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.042 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x052e0c0e, L:/********:62337 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.043 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfb208202, L:/********:62336 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.043 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8054670e, L:/********:62333 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 03:42:51.043 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9c1eefe6, L:/********:62334 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-26 04:00:00.065 [schedule-pool-0] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 04:00:02.171 [schedule-pool-0] INFO  p6spy:60 - 2025-07-26T04:00:02.171 | 耗时 397 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T04:00:00.065+0800' AND is_deleted = 0;
2025-07-26 04:00:02.172 [schedule-pool-0] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 04:00:02.172 [schedule-pool-0] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 2107 ms
2025-07-26 04:30:00.061 [schedule-pool-0] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 04:30:00.379 [schedule-pool-0] INFO  p6spy:60 - 2025-07-26T04:30:00.379 | 耗时 128 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T04:30:00.061+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 04:30:00.380 [schedule-pool-0] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 04:30:00.382 [schedule-pool-0] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 321 ms
2025-07-26 05:00:00.050 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 05:00:00.417 [schedule-pool-3] INFO  p6spy:60 - 2025-07-26T05:00:00.417 | 耗时 170 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T05:00:00.050+0800' AND is_deleted = 0;
2025-07-26 05:00:00.417 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 05:00:00.418 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 368 ms
2025-07-26 05:30:00.053 [schedule-pool-7] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 05:30:00.521 [schedule-pool-7] INFO  p6spy:60 - 2025-07-26T05:30:00.521 | 耗时 210 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T05:30:00.053+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 05:30:00.521 [schedule-pool-7] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 05:30:00.522 [schedule-pool-7] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 469 ms
2025-07-26 06:00:00.051 [schedule-pool-8] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 06:00:00.498 [schedule-pool-8] INFO  p6spy:60 - 2025-07-26T06:00:00.498 | 耗时 290 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T06:00:00.051+0800' AND is_deleted = 0;
2025-07-26 06:00:00.498 [schedule-pool-8] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 06:00:00.499 [schedule-pool-8] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 448 ms
2025-07-26 06:30:00.055 [schedule-pool-3] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 06:30:00.835 [schedule-pool-3] INFO  p6spy:60 - 2025-07-26T06:30:00.834 | 耗时 536 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T06:30:00.055+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 06:30:00.836 [schedule-pool-3] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 06:30:00.836 [schedule-pool-3] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 781 ms
2025-07-26 07:00:00.058 [schedule-pool-8] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 07:00:00.608 [schedule-pool-8] INFO  p6spy:60 - 2025-07-26T07:00:00.608 | 耗时 339 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T07:00:00.058+0800' AND is_deleted = 0;
2025-07-26 07:00:00.609 [schedule-pool-8] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 07:00:00.609 [schedule-pool-8] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 551 ms
2025-07-26 07:30:00.080 [schedule-pool-8] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 07:30:00.409 [schedule-pool-8] INFO  p6spy:60 - 2025-07-26T07:30:00.409 | 耗时 205 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T07:30:00.080+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 07:30:00.409 [schedule-pool-8] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 07:30:00.409 [schedule-pool-8] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 329 ms
2025-07-26 08:00:00.076 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 08:00:00.611 [schedule-pool-1] INFO  p6spy:60 - 2025-07-26T08:00:00.611 | 耗时 202 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T08:00:00.076+0800' AND is_deleted = 0;
2025-07-26 08:00:00.611 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 08:00:00.612 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 536 ms
2025-07-26 08:30:00.057 [schedule-pool-7] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 08:30:00.492 [schedule-pool-7] INFO  p6spy:60 - 2025-07-26T08:30:00.492 | 耗时 193 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T08:30:00.057+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 08:30:00.492 [schedule-pool-7] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 08:30:00.493 [schedule-pool-7] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 436 ms
2025-07-26 09:00:00.077 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 09:00:00.300 [schedule-pool-3] INFO  p6spy:60 - 2025-07-26T09:00:00.299 | 耗时 119 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T09:00:00.077+0800' AND is_deleted = 0;
2025-07-26 09:00:00.300 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 09:00:00.300 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 223 ms
2025-07-26 09:30:00.073 [schedule-pool-5] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 09:30:00.497 [schedule-pool-5] INFO  p6spy:60 - 2025-07-26T09:30:00.497 | 耗时 209 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T09:30:00.074+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 09:30:00.498 [schedule-pool-5] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 09:30:00.498 [schedule-pool-5] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 424 ms
2025-07-26 10:00:00.051 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 10:00:00.485 [schedule-pool-7] INFO  p6spy:60 - 2025-07-26T10:00:00.485 | 耗时 202 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T10:00:00.051+0800' AND is_deleted = 0;
2025-07-26 10:00:00.486 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 10:00:00.486 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 435 ms
2025-07-26 10:30:00.057 [schedule-pool-4] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 10:30:00.434 [schedule-pool-4] INFO  p6spy:60 - 2025-07-26T10:30:00.434 | 耗时 148 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T10:30:00.057+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 10:30:00.435 [schedule-pool-4] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 10:30:00.435 [schedule-pool-4] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 378 ms
2025-07-26 11:00:00.065 [schedule-pool-0] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 11:00:00.474 [schedule-pool-0] INFO  p6spy:60 - 2025-07-26T11:00:00.474 | 耗时 200 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T11:00:00.065+0800' AND is_deleted = 0;
2025-07-26 11:00:00.474 [schedule-pool-0] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 11:00:00.475 [schedule-pool-0] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 410 ms
2025-07-26 11:30:00.069 [schedule-pool-2] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 11:30:00.477 [schedule-pool-2] INFO  p6spy:60 - 2025-07-26T11:30:00.477 | 耗时 242 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T11:30:00.069+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 11:30:00.477 [schedule-pool-2] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 11:30:00.477 [schedule-pool-2] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 408 ms
2025-07-26 12:00:00.078 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 12:00:00.463 [schedule-pool-3] INFO  p6spy:60 - 2025-07-26T12:00:00.463 | 耗时 185 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T12:00:00.078+0800' AND is_deleted = 0;
2025-07-26 12:00:00.464 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 12:00:00.464 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 386 ms
2025-07-26 12:30:00.074 [schedule-pool-4] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 12:30:00.481 [schedule-pool-4] INFO  p6spy:60 - 2025-07-26T12:30:00.481 | 耗时 239 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T12:30:00.074+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 12:30:00.483 [schedule-pool-4] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 12:30:00.483 [schedule-pool-4] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 409 ms
2025-07-26 13:00:00.067 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 13:00:00.484 [schedule-pool-4] INFO  p6spy:60 - 2025-07-26T13:00:00.484 | 耗时 207 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T13:00:00.067+0800' AND is_deleted = 0;
2025-07-26 13:00:00.485 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 13:00:00.485 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 418 ms
2025-07-26 13:30:00.056 [schedule-pool-9] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 13:30:00.472 [schedule-pool-9] INFO  p6spy:60 - 2025-07-26T13:30:00.472 | 耗时 245 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T13:30:00.056+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 13:30:00.473 [schedule-pool-9] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 13:30:00.473 [schedule-pool-9] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 417 ms
2025-07-26 14:00:00.061 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 14:00:00.576 [schedule-pool-7] INFO  p6spy:60 - 2025-07-26T14:00:00.576 | 耗时 210 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T14:00:00.061+0800' AND is_deleted = 0;
2025-07-26 14:00:00.577 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 14:00:00.577 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 516 ms
2025-07-26 14:30:00.052 [schedule-pool-6] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 14:30:00.475 [schedule-pool-6] INFO  p6spy:60 - 2025-07-26T14:30:00.475 | 耗时 250 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T14:30:00.052+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 14:30:00.476 [schedule-pool-6] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 14:30:00.476 [schedule-pool-6] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 424 ms
2025-07-26 15:00:00.058 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 15:00:00.455 [schedule-pool-3] INFO  p6spy:60 - 2025-07-26T15:00:00.455 | 耗时 202 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T15:00:00.058+0800' AND is_deleted = 0;
2025-07-26 15:00:00.456 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 15:00:00.456 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 398 ms
2025-07-26 15:30:00.062 [schedule-pool-6] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 15:30:00.450 [schedule-pool-6] INFO  p6spy:60 - 2025-07-26T15:30:00.45 | 耗时 204 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T15:30:00.062+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 15:30:00.450 [schedule-pool-6] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 15:30:00.451 [schedule-pool-6] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 389 ms
2025-07-26 16:00:00.074 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 16:00:00.464 [schedule-pool-6] INFO  p6spy:60 - 2025-07-26T16:00:00.464 | 耗时 203 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T16:00:00.074+0800' AND is_deleted = 0;
2025-07-26 16:00:00.464 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 16:00:00.464 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 390 ms
2025-07-26 16:30:00.110 [schedule-pool-2] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 16:30:00.645 [schedule-pool-2] INFO  p6spy:60 - 2025-07-26T16:30:00.645 | 耗时 355 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T16:30:00.110+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 16:30:00.648 [schedule-pool-2] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 16:30:00.648 [schedule-pool-2] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 538 ms
2025-07-26 17:00:00.106 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 17:00:00.350 [schedule-pool-2] INFO  p6spy:60 - 2025-07-26T17:00:00.348 | 耗时 119 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T17:00:00.106+0800' AND is_deleted = 0;
2025-07-26 17:00:00.350 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 17:00:00.350 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 244 ms
2025-07-26 17:30:00.078 [schedule-pool-0] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 17:30:00.813 [schedule-pool-0] INFO  p6spy:60 - 2025-07-26T17:30:00.813 | 耗时 204 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T17:30:00.078+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 17:30:00.814 [schedule-pool-0] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 17:30:00.814 [schedule-pool-0] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 736 ms
2025-07-26 18:00:00.062 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 18:00:00.370 [schedule-pool-6] INFO  p6spy:60 - 2025-07-26T18:00:00.369 | 耗时 174 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T18:00:00.062+0800' AND is_deleted = 0;
2025-07-26 18:00:00.370 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 18:00:00.370 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 308 ms
2025-07-26 18:30:00.061 [schedule-pool-2] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 18:30:00.452 [schedule-pool-2] INFO  p6spy:60 - 2025-07-26T18:30:00.452 | 耗时 154 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T18:30:00.061+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 18:30:00.452 [schedule-pool-2] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 18:30:00.452 [schedule-pool-2] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 391 ms
2025-07-26 19:00:00.066 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 19:00:00.493 [schedule-pool-4] INFO  p6spy:60 - 2025-07-26T19:00:00.493 | 耗时 205 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T19:00:00.066+0800' AND is_deleted = 0;
2025-07-26 19:00:00.493 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 19:00:00.493 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 427 ms
2025-07-26 19:30:00.059 [schedule-pool-2] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 19:30:00.499 [schedule-pool-2] INFO  p6spy:60 - 2025-07-26T19:30:00.499 | 耗时 211 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T19:30:00.059+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 19:30:00.500 [schedule-pool-2] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 19:30:00.500 [schedule-pool-2] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 441 ms
2025-07-26 20:00:00.058 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 20:00:00.387 [schedule-pool-6] INFO  p6spy:60 - 2025-07-26T20:00:00.387 | 耗时 212 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T20:00:00.058+0800' AND is_deleted = 0;
2025-07-26 20:00:00.388 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 20:00:00.388 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 330 ms
2025-07-26 20:30:00.063 [schedule-pool-8] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 20:30:00.499 [schedule-pool-8] INFO  p6spy:60 - 2025-07-26T20:30:00.499 | 耗时 209 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T20:30:00.063+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 20:30:00.499 [schedule-pool-8] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 20:30:00.500 [schedule-pool-8] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 437 ms
2025-07-26 21:00:00.084 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 21:00:00.809 [schedule-pool-6] INFO  p6spy:60 - 2025-07-26T21:00:00.809 | 耗时 410 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T21:00:00.084+0800' AND is_deleted = 0;
2025-07-26 21:00:00.810 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 21:00:00.810 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 726 ms
2025-07-26 21:30:00.086 [schedule-pool-7] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 21:30:00.819 [schedule-pool-7] INFO  p6spy:60 - 2025-07-26T21:30:00.819 | 耗时 160 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T21:30:00.086+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 21:30:00.819 [schedule-pool-7] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 21:30:00.820 [schedule-pool-7] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 734 ms
2025-07-26 22:00:00.071 [schedule-pool-5] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 22:00:00.507 [schedule-pool-5] INFO  p6spy:60 - 2025-07-26T22:00:00.507 | 耗时 203 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T22:00:00.071+0800' AND is_deleted = 0;
2025-07-26 22:00:00.507 [schedule-pool-5] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 22:00:00.508 [schedule-pool-5] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 437 ms
2025-07-26 22:30:00.057 [schedule-pool-6] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 22:30:00.580 [schedule-pool-6] INFO  p6spy:60 - 2025-07-26T22:30:00.58 | 耗时 327 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T22:30:00.057+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 22:30:00.581 [schedule-pool-6] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 22:30:00.581 [schedule-pool-6] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 524 ms
2025-07-26 23:00:00.064 [schedule-pool-8] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-26 23:00:00.499 [schedule-pool-8] INFO  p6spy:60 - 2025-07-26T23:00:00.499 | 耗时 205 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-26T23:00:00.064+0800' AND is_deleted = 0;
2025-07-26 23:00:00.500 [schedule-pool-8] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-26 23:00:00.500 [schedule-pool-8] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 436 ms
2025-07-26 23:30:00.055 [schedule-pool-9] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-26 23:30:00.570 [schedule-pool-9] INFO  p6spy:60 - 2025-07-26T23:30:00.57 | 耗时 278 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-26T23:30:00.055+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-26 23:30:00.570 [schedule-pool-9] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-26 23:30:00.570 [schedule-pool-9] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 515 ms
