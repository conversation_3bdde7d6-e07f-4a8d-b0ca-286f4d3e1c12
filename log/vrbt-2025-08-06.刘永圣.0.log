2025-08-06 00:00:00.058 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 00:00:00.381 [schedule-pool-1] INFO  p6spy:60 - 2025-08-06T00:00:00.381 | 耗时 128 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T00:00:00.087+0800' AND is_deleted = 0;
2025-08-06 00:00:00.384 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 00:00:00.385 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 298 ms
2025-08-06 00:30:00.055 [schedule-pool-4] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 00:30:00.425 [schedule-pool-4] INFO  p6spy:60 - 2025-08-06T00:30:00.425 | 耗时 204 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T00:30:00.057+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 00:30:00.426 [schedule-pool-4] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 00:30:00.426 [schedule-pool-4] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 369 ms
2025-08-06 01:00:00.052 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 01:00:00.421 [schedule-pool-7] INFO  p6spy:60 - 2025-08-06T01:00:00.42 | 耗时 229 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T01:00:00.052+0800' AND is_deleted = 0;
2025-08-06 01:00:00.422 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 01:00:00.422 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 370 ms
2025-08-06 01:30:00.049 [schedule-pool-2] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 01:30:00.461 [schedule-pool-2] INFO  p6spy:60 - 2025-08-06T01:30:00.461 | 耗时 152 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T01:30:00.049+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 01:30:00.462 [schedule-pool-2] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 01:30:00.462 [schedule-pool-2] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 413 ms
2025-08-06 02:00:00.052 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 02:00:00.413 [schedule-pool-9] INFO  p6spy:60 - 2025-08-06T02:00:00.413 | 耗时 204 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T02:00:00.052+0800' AND is_deleted = 0;
2025-08-06 02:00:00.414 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 02:00:00.414 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 362 ms
2025-08-06 02:30:00.067 [schedule-pool-2] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 02:30:00.412 [schedule-pool-2] INFO  p6spy:60 - 2025-08-06T02:30:00.412 | 耗时 204 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T02:30:00.067+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 02:30:00.413 [schedule-pool-2] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 02:30:00.413 [schedule-pool-2] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 346 ms
2025-08-06 03:00:00.091 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 03:00:00.520 [schedule-pool-7] INFO  p6spy:60 - 2025-08-06T03:00:00.52 | 耗时 202 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T03:00:00.091+0800' AND is_deleted = 0;
2025-08-06 03:00:00.521 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 03:00:00.521 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 430 ms
2025-08-06 03:30:00.076 [schedule-pool-8] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 03:30:00.427 [schedule-pool-8] INFO  p6spy:60 - 2025-08-06T03:30:00.427 | 耗时 210 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T03:30:00.076+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 03:30:00.427 [schedule-pool-8] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 03:30:00.428 [schedule-pool-8] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 352 ms
2025-08-06 03:48:32.702 [redisson-netty-4-32] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xeff62193, L:/********:61055 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc5a8f61d, L:/********:50221 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-16] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xffcfc10f, L:/********:50227 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xbe14b35a, L:/********:56122 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-18] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x06bf402d, L:/********:50088 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xb0f0ba46, L:/********:50223 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.buffer.WrappedByteBuf.writeBytes(WrappedByteBuf.java:821)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-26] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x5d26bbd5, L:/********:56113 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-22] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xf78fb3a6, L:/********:50271 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-11] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc021bfd4, L:/********:56104 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-5] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xe6339717, L:/********:56123 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-1] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x5cd7972e, L:/********:50328 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-2] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xb4fdbd94, L:/********:50145 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-30] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x09849247, L:/********:50136 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-24] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x2e1851f3, L:/********:50276 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-31] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xebd91af0, L:/********:50137 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-25] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6cd66068, L:/********:50280 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-15] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xa45a5cfd, L:/********:56125 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.702 [redisson-netty-4-19] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x8297d84c, L:/********:50090 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.707 [redisson-netty-4-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xa76677e6, L:/********:50341 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.707 [redisson-netty-4-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xf66f8028, L:/********:56101 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.707 [redisson-netty-4-26] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xe90a9aff, L:/********:50281 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.707 [redisson-netty-4-1] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd0661ae4, L:/********:56119 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.708 [redisson-netty-4-19] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x79b70e72, L:/********:50266 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.708 [redisson-netty-4-24] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x9c8a89a9, L:/********:50101 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.742 [redisson-netty-4-12] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x5c86430c, L:/********:50029 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.746 [AMQP Connection **************:5672] WARN  ForgivingExceptionHandler:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-08-06 03:48:32.746 [lettuce-nioEventLoop-12-1] INFO  CommandHandler:217 - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:32.842 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.848 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.851 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.854 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.854 [schedule-pool-2] ERROR TaskUtils$LoggingErrorHandler:95 - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.del(LettuceKeyCommands.java:128)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.del(DefaultedRedisConnection.java:82)
	at org.springframework.data.redis.core.RedisTemplate.lambda$delete$2(RedisTemplate.java:713)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.delete(RedisTemplate.java:713)
	at org.jeecg.common.util.RedisUtil.del(RedisUtil.java:87)
	at com.eleven.cms.aivrbt.annotaion.aop.DistributedLockAspect.around(DistributedLockAspect.java:41)
	at sun.reflect.GeneratedMethodAccessor302.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.eleven.cms.aiunion.service.impl.AiUnionRecordServiceImpl$$EnhancerBySpringCGLIB$$d1e298de.querySecurityResultScheduled(<generated>)
	at sun.reflect.GeneratedMethodAccessor308.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy836.del(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.del(LettuceKeyCommands.java:126)
	... 34 common frames omitted
Caused by: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-08-06 03:48:32.856 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.857 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.858 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.860 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.861 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.866 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.868 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.869 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.870 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-08-06 03:48:32.940 [lettuce-eventExecutorLoop-1-12] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-08-06 03:48:32.951 [lettuce-eventExecutorLoop-7-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /8.140.21.7:6379
2025-08-06 03:48:33.001 [lettuce-nioEventLoop-12-2] INFO  ReconnectionHandler:194 - Reconnected to 8.140.21.7:6379
2025-08-06 03:48:33.005 [lettuce-nioEventLoop-11-2] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-08-06 03:48:33.045 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@629680ec: tags=[[amq.ctag-7PlTeoGiyrPJvJ0c3K4bHg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,4), conn: Proxy@5cbc027c Shared Rabbit Connection: SimpleConnection@30705816 [delegate=amqp://admin@**************:5672/, localPort= 56335], acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.045 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@4e9b110f: tags=[[amq.ctag-zpKQqQXuAZl1t0m3Rq3j_w]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,3), conn: Proxy@5cbc027c Shared Rabbit Connection: SimpleConnection@30705816 [delegate=amqp://admin@**************:5672/, localPort= 56335], acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.080 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-06 03:48:33.302 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1772987e: tags=[[amq.ctag-zsk0SfJjnNj5Iaf5RshfyA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,8), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.348 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@570cca52: tags=[[amq.ctag-dy93CICJr6lcM4jop9w7Bg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,5), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.486 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@229365f4: tags=[[amq.ctag-JpfBQKeNMQEcd5CkeS8Idw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,10), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.532 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#9-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@30bac2a0: tags=[[amq.ctag-FVWIQqPh5_SFuWgFWLiDmg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,11), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.534 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1b6909c8: tags=[[amq.ctag-Q6JjVVrRZCulDIZskL8lBg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,9), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.647 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@4bfcb524: tags=[[amq.ctag-EiH5V9MhZHYABC6sYJDYKQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,6), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.647 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@2c318030: tags=[[amq.ctag-lYHMSxJcyzzfLfbzDlTUfA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.657 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#9-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@79bec433: tags=[[amq.ctag-S7WsbWLRonIj-wq_tSKfuw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,12), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.657 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@76566931: tags=[[amq.ctag-x02XllwkHcfPn3lQVy6R6A]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,7), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:33.705 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@44f6c738: tags=[[amq.ctag-KzKsuh7nQlYPTEBI7a2N3A]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,2), conn: Proxy@5cbc027c Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-08-06 03:48:38.034 [lettuce-eventExecutorLoop-1-15] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-08-06 03:48:38.035 [lettuce-eventExecutorLoop-7-6] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /8.140.21.7:6379
2025-08-06 03:48:38.093 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-06 03:48:38.094 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpIOException: java.io.IOException
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:70)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createBareChannel(CachingConnectionFactory.java:702)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getCachedChannelProxy(CachingConnectionFactory.java:676)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getChannel(CachingConnectionFactory.java:567)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.access$1600(CachingConnectionFactory.java:102)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory$ChannelCachingConnectionProxy.createChannel(CachingConnectionFactory.java:1439)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2095)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: null
	at com.rabbitmq.client.impl.AMQChannel.wrap(AMQChannel.java:129)
	at com.rabbitmq.client.impl.AMQChannel.wrap(AMQChannel.java:125)
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:396)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 16 common frames omitted
Caused by: com.rabbitmq.client.ShutdownSignalException: connection error
	at com.rabbitmq.utility.ValueOrException.getValue(ValueOrException.java:66)
	at com.rabbitmq.utility.BlockingValueOrException.uninterruptibleGetValue(BlockingValueOrException.java:36)
	at com.rabbitmq.client.impl.AMQChannel$BlockingRpcContinuation.getReply(AMQChannel.java:502)
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:326)
	... 21 common frames omitted
Caused by: java.io.EOFException: null
	at java.io.DataInputStream.readUnsignedByte(DataInputStream.java:290)
	at com.rabbitmq.client.impl.Frame.readFrom(Frame.java:91)
	at com.rabbitmq.client.impl.SocketFrameHandler.readFrame(SocketFrameHandler.java:184)
	at com.rabbitmq.client.impl.AMQConnection$MainLoop.run(AMQConnection.java:665)
	... 1 common frames omitted
2025-08-06 03:48:39.306 [pool-4-thread-1] INFO  AliMnsService:496 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-6 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-6 [ACTIVE]
	... 11 common frames omitted
2025-08-06 03:48:42.115 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-5 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-5 [ACTIVE]
	... 11 common frames omitted
2025-08-06 03:48:42.556 [pool-4-thread-2] INFO  AliMnsService:576 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-7 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-7 [ACTIVE]
	... 11 common frames omitted
2025-08-06 03:48:48.051 [lettuce-nioEventLoop-12-3] WARN  ConnectionWatchdog:151 - Cannot reconnect to [8.140.21.7:6379]: connection timed out: /8.140.21.7:6379
2025-08-06 03:48:48.051 [lettuce-nioEventLoop-11-3] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-08-06 03:48:48.134 [lettuce-eventExecutorLoop-1-1] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-08-06 03:48:48.140 [lettuce-eventExecutorLoop-7-7] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was 8.140.21.7:6379
2025-08-06 03:48:48.283 [lettuce-nioEventLoop-12-4] INFO  ReconnectionHandler:194 - Reconnected to 8.140.21.7:6379
2025-08-06 03:48:48.377 [lettuce-nioEventLoop-11-4] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-08-06 03:48:59.126 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-06 03:48:59.126 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-08-06 03:48:59.758 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf66f8028, L:/********:56101 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:59.758 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc021bfd4, L:/********:56104 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:59.758 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5d26bbd5, L:/********:56113 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:48:59.857 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-2] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#5218997b:3/SimpleConnection@552b7757 [delegate=amqp://admin@**************:5672/, localPort= 63613]
2025-08-06 03:49:00.955 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xeff62193, L:/********:61055 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:00.955 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x09849247, L:/********:50136 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:00.955 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x79b70e72, L:/********:50266 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:01.661 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd0661ae4, L:/********:56119 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:01.661 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xbe14b35a, L:/********:56122 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:01.661 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe6339717, L:/********:56123 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:01.661 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa45a5cfd, L:/********:56125 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:01.754 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5cd7972e, L:/********:50328 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:01.853 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xebd91af0, L:/********:50137 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:02.153 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc5a8f61d, L:/********:50221 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-06 03:49:02.953 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x06bf402d, L:/********:50088 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:03.256 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xffcfc10f, L:/********:50227 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:04.060 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8297d84c, L:/********:50090 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:04.458 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb0f0ba46, L:/********:50223 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:04.459 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2e1851f3, L:/********:50276 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:05.157 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf78fb3a6, L:/********:50271 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:05.453 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb4fdbd94, L:/********:50145 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:06.454 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9c8a89a9, L:/********:50101 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:06.954 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe90a9aff, L:/********:50281 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:07.754 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5c86430c, L:/********:50029 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:09.660 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa76677e6, L:/********:50341 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 03:49:09.753 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6cd66068, L:/********:50280 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-06 04:00:00.074 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 04:00:01.973 [schedule-pool-9] INFO  p6spy:60 - 2025-08-06T04:00:01.973 | 耗时 317 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T04:00:00.074+0800' AND is_deleted = 0;
2025-08-06 04:00:01.974 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 04:00:01.974 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 1900 ms
2025-08-06 04:30:00.070 [schedule-pool-4] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 04:30:00.502 [schedule-pool-4] INFO  p6spy:60 - 2025-08-06T04:30:00.5 | 耗时 175 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T04:30:00.070+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 04:30:00.504 [schedule-pool-4] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 04:30:00.506 [schedule-pool-4] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 436 ms
2025-08-06 05:00:00.058 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 05:00:00.514 [schedule-pool-3] INFO  p6spy:60 - 2025-08-06T05:00:00.514 | 耗时 196 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T05:00:00.058+0800' AND is_deleted = 0;
2025-08-06 05:00:00.515 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 05:00:00.515 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 457 ms
2025-08-06 05:30:00.065 [schedule-pool-4] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 05:30:00.465 [schedule-pool-4] INFO  p6spy:60 - 2025-08-06T05:30:00.465 | 耗时 142 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T05:30:00.065+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 05:30:00.466 [schedule-pool-4] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 05:30:00.467 [schedule-pool-4] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 402 ms
2025-08-06 06:00:00.066 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 06:00:00.473 [schedule-pool-6] INFO  p6spy:60 - 2025-08-06T06:00:00.473 | 耗时 165 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T06:00:00.067+0800' AND is_deleted = 0;
2025-08-06 06:00:00.474 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 06:00:00.474 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 407 ms
2025-08-06 06:30:00.054 [schedule-pool-4] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 06:30:00.607 [schedule-pool-4] INFO  p6spy:60 - 2025-08-06T06:30:00.607 | 耗时 307 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T06:30:00.054+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 06:30:00.608 [schedule-pool-4] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 06:30:00.608 [schedule-pool-4] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 554 ms
2025-08-06 07:00:00.075 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 07:00:00.464 [schedule-pool-7] INFO  p6spy:60 - 2025-08-06T07:00:00.464 | 耗时 157 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T07:00:00.075+0800' AND is_deleted = 0;
2025-08-06 07:00:00.465 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 07:00:00.465 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 390 ms
2025-08-06 07:30:00.089 [schedule-pool-5] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 07:30:00.383 [schedule-pool-5] INFO  p6spy:60 - 2025-08-06T07:30:00.383 | 耗时 115 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T07:30:00.089+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 07:30:00.384 [schedule-pool-5] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 07:30:00.384 [schedule-pool-5] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 295 ms
2025-08-06 08:00:00.081 [schedule-pool-5] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 08:00:00.622 [schedule-pool-5] INFO  p6spy:60 - 2025-08-06T08:00:00.622 | 耗时 202 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T08:00:00.081+0800' AND is_deleted = 0;
2025-08-06 08:00:00.623 [schedule-pool-5] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 08:00:00.623 [schedule-pool-5] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 542 ms
2025-08-06 08:30:00.077 [schedule-pool-3] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-06 08:30:00.451 [schedule-pool-3] INFO  p6spy:60 - 2025-08-06T08:30:00.451 | 耗时 125 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-06T08:30:00.077+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-06 08:30:00.452 [schedule-pool-3] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-06 08:30:00.452 [schedule-pool-3] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 375 ms
2025-08-06 09:00:00.069 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-06 09:00:00.288 [schedule-pool-6] INFO  p6spy:60 - 2025-08-06T09:00:00.288 | 耗时 106 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-06T09:00:00.069+0800' AND is_deleted = 0;
2025-08-06 09:00:00.289 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-06 09:00:00.289 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 220 ms
2025-08-06 09:00:40.680 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-08-06 09:00:43.009 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-08-06 09:00:46.776 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-06 09:00:46.777 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-06 09:00:46.892 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 110ms. Found 11 Elasticsearch repository interfaces.
2025-08-06 09:00:47.536 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-06 09:00:47.537 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-06 09:00:47.603 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 66ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-06 09:00:47.618 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-06 09:00:47.621 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-06 09:00:47.672 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 42ms. Found 0 Redis repository interfaces.
2025-08-06 09:00:48.246 [main] INFO  GenericScope:295 - BeanFactory id=7bafe521-6f8c-3f1a-9a87-30fc20a81963
2025-08-06 09:00:48.317 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$c01e6c28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:48.455 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:48.456 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppMobilePayLinkConfigClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:48.458 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:48.461 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$338ee61] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:48.559 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$95dc9ab7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.041 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.045 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9a77363] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.062 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.073 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.146 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.495 [main] INFO  ShiroConfig:254 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-06 09:00:49.497 [main] INFO  ShiroConfig:272 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-08-06 09:00:49.506 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.519 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.547 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.561 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$aecf372] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.676 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$b331e042] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:49.681 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:00:50.277 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-08-06 09:00:50.530 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-08-06 09:00:50.531 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-08-06 09:00:50.531 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-06 09:00:50.647 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-08-06 09:00:50.647 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 7608 ms
2025-08-06 09:00:51.468 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-08-06 09:00:51.482 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-08-06 09:00:54.694 [main] INFO  DruidDataSource:1003 - {dataSource-1,member} inited
2025-08-06 09:00:58.172 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-08-06 09:01:01.448 [main] INFO  DruidDataSource:1003 - {dataSource-3,miniapp} inited
2025-08-06 09:01:04.217 [main] INFO  DruidDataSource:1003 - {dataSource-4,master} inited
2025-08-06 09:01:04.223 [main] ERROR DruidDataSource:978 - {dataSource-5} init error
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-06 09:01:04.224 [main] ERROR DynamicDataSourceCreator:244 - druid数据源启动失败
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-06 09:01:04.224 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 5 个数据源
2025-08-06 09:01:04.224 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-08-06 09:01:04.225 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-08-06 09:01:04.225 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 centric 成功
2025-08-06 09:01:04.225 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-08-06 09:01:04.225 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-08-06 09:01:04.225 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-08-06 09:01:12.951 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-06 09:01:12.959 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-06 09:01:13.100 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-06 09:01:13.101 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-06 09:01:13.135 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-06 09:01:13.146 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-06 09:01:13.151 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-06 09:01:13.153 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-06 09:01:13.153 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-06 09:01:13.155 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@770feaab
2025-08-06 09:01:17.218 [main] INFO  Version:41 - Redisson 3.15.0
2025-08-06 09:01:19.904 [redisson-netty-4-22] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-08-06 09:01:21.279 [redisson-netty-4-18] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-08-06 09:01:25.340 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-06 09:01:25.342 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-08-06 09:01:29.700 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:29.701 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:29.701 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:29.701 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:29.747 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:29.748 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:29.748 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:29.748 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-06 09:01:30.042 [main] TRACE WIRE:64 - [7ee7a75e] Sending request GET / with parameters: 
2025-08-06 09:01:30.479 [I/O dispatcher 1] TRACE WIRE:97 - [7ee7a75e] Received raw response: 200 OK
2025-08-06 09:01:30.727 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-08-06 09:01:30.727 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-08-06 09:01:30.727 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-08-06 09:01:30.728 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-08-06 09:01:30.728 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-08-06 09:02:06.957 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-08-06 09:02:09.469 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-08-06 09:02:42.536 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-08-06 09:02:42.544 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-08-06 09:02:42.545 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-08-06 09:02:42.547 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-08-06 09:02:43.602 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-06 09:02:43.603 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-08-06 09:02:45.870 [main] TRACE WIRE:64 - [320f6f8c] Sending request HEAD /insert_code_2025-08?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-08-06 09:02:46.167 [I/O dispatcher 1] TRACE WIRE:97 - [320f6f8c] Received raw response: 200 OK
2025-08-06 09:02:46.735 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-06 09:02:46.736 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-06 09:02:46.862 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-06 09:02:46.863 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-06 09:02:58.446 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-06 09:02:58.974 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-08-06 09:03:00.269 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-08-06 09:03:00.369 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-08-06 09:03:02.428 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-06 09:03:02.432 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-06 09:03:04.309 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#75be998c:0/SimpleConnection@15307c1e [delegate=amqp://admin@**************:5672/, localPort= 64816]
