2025-07-27 00:00:00.056 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 00:00:00.367 [schedule-pool-9] INFO  p6spy:60 - 2025-07-27T00:00:00.367 | 耗时 176 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T00:00:00.059+0800' AND is_deleted = 0;
2025-07-27 00:00:00.367 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 00:00:00.368 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 309 ms
2025-07-27 00:30:00.060 [schedule-pool-2] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 00:30:00.482 [schedule-pool-2] INFO  p6spy:60 - 2025-07-27T00:30:00.482 | 耗时 203 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T00:30:00.061+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 00:30:00.483 [schedule-pool-2] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 00:30:00.483 [schedule-pool-2] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 422 ms
2025-07-27 01:00:00.064 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 01:00:00.478 [schedule-pool-2] INFO  p6spy:60 - 2025-07-27T01:00:00.478 | 耗时 203 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T01:00:00.064+0800' AND is_deleted = 0;
2025-07-27 01:00:00.479 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 01:00:00.479 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 415 ms
2025-07-27 01:30:00.061 [schedule-pool-2] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 01:30:00.589 [schedule-pool-2] INFO  p6spy:60 - 2025-07-27T01:30:00.589 | 耗时 200 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T01:30:00.062+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 01:30:00.590 [schedule-pool-2] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 01:30:00.590 [schedule-pool-2] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 528 ms
2025-07-27 02:00:00.059 [schedule-pool-0] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 02:00:00.372 [schedule-pool-0] INFO  p6spy:60 - 2025-07-27T02:00:00.372 | 耗时 176 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T02:00:00.059+0800' AND is_deleted = 0;
2025-07-27 02:00:00.373 [schedule-pool-0] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 02:00:00.373 [schedule-pool-0] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 314 ms
2025-07-27 02:30:00.057 [schedule-pool-6] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 02:30:00.383 [schedule-pool-6] INFO  p6spy:60 - 2025-07-27T02:30:00.383 | 耗时 118 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T02:30:00.057+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 02:30:00.385 [schedule-pool-6] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 02:30:00.385 [schedule-pool-6] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 328 ms
2025-07-27 03:00:00.063 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 03:00:00.464 [schedule-pool-9] INFO  p6spy:60 - 2025-07-27T03:00:00.464 | 耗时 199 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T03:00:00.063+0800' AND is_deleted = 0;
2025-07-27 03:00:00.465 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 03:00:00.465 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 402 ms
2025-07-27 03:30:00.052 [schedule-pool-8] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 03:30:00.469 [schedule-pool-8] INFO  p6spy:60 - 2025-07-27T03:30:00.469 | 耗时 202 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T03:30:00.052+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 03:30:00.470 [schedule-pool-8] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 03:30:00.470 [schedule-pool-8] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 418 ms
2025-07-27 03:46:11.463 [redisson-netty-6-23] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc6ee6845, L:/********:59958 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-28] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x158bdcc1, L:/********:59954 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-25] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x0e11b08b, L:/********:59955 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-1] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6eed9a6b, L:/********:59948 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-7] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x41399ac0, L:/********:59962 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-12] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xe0613ab5, L:/********:59967 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-22] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xa3d6aec5, L:/********:59945 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.467 [AMQP Connection **************:5672] WARN  ForgivingExceptionHandler:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-27 03:46:11.464 [redisson-netty-6-32] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x39509307, L:/********:59946 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-24] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x506ccc76, L:/********:59953 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-13] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xfe95641f, L:/********:59968 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.463 [redisson-netty-6-2] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x73a7489f, L:/********:59947 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-11] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x0a7149e5, L:/********:59966 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-5] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x5a3ef803, L:/********:59957 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-21] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xe473b2d9, L:/********:59944 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-8] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd991219c, L:/********:59963 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x308abac9, L:/********:59965 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-4] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x15da0edc, L:/********:59959 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-6] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x84c56150, L:/********:59961 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc1bfe353, L:/********:59964 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-30] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc9d9e560, L:/********:59950 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-29] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x58bfdf33, L:/********:59949 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-27] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x74dd1cab, L:/********:59952 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.buffer.WrappedByteBuf.writeBytes(WrappedByteBuf.java:821)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-31] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6cad4f7b, L:/********:59951 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.464 [redisson-netty-6-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x7a7d97f8, L:/********:59960 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.467 [redisson-netty-6-26] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x179b51b0, L:/********:59956 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:11.469 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.470 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.470 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.472 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.488 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.493 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.497 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.499 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.500 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.502 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.505 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-27 03:46:11.517 [lettuce-eventExecutorLoop-1-8] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-07-27 03:46:11.565 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-4] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1d60048e: tags=[[amq.ctag-db-hxCkNq-eKSvbnuRxjJw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,5), conn: Proxy@2e86c629 Shared Rabbit Connection: SimpleConnection@7a0f60f2 [delegate=amqp://admin@**************:5672/, localPort= 60006], acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:11.567 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-5] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-27 03:46:11.580 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@62067f7f: tags=[[amq.ctag-r-jEWWIFpd-sYnshly6z5A]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,10), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:11.595 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@3c3b175c: tags=[[amq.ctag-PSaOomxjA66fkPGPKTz9eQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,4), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:11.781 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@462cc6da: tags=[[amq.ctag-YHbOgUqvd9Vau32jLtmrxA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,6), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:11.813 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@14366884: tags=[[amq.ctag-qSH1KkqVflBmFcQZfVj-oQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,3), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:11.813 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-3] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@2e68deb2: tags=[[amq.ctag-Cucw3qONBb9RGpdEBe8zRA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,7), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:11.813 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@3138d222: tags=[[amq.ctag-meylkwESmlQwuS8EvtMDqQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:11.917 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@7c4360ca: tags=[[amq.ctag-kEHsZuqzYXoSnSPyqIhrCA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,2), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:12.126 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-4] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@40b741e: tags=[[amq.ctag-PgSWYxjSml1hOeSzhd6Dow]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,11), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:12.126 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@6f9ffc47: tags=[[amq.ctag-6wxYyEIRkQyVOXlnK5a_0w]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,8), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:12.404 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@5d6c88d5: tags=[[amq.ctag-XyJs17vXvUVBgigoSK7mog]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,9), conn: Proxy@2e86c629 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-27 03:46:21.552 [lettuce-nioEventLoop-11-4] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-27 03:46:21.630 [lettuce-eventExecutorLoop-1-10] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-27 03:46:21.874 [lettuce-nioEventLoop-11-5] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-07-27 03:46:22.211 [pool-4-thread-1] INFO  AliMnsService:496 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-9 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-9 [ACTIVE]
	... 11 common frames omitted
2025-07-27 03:46:24.094 [pool-4-thread-2] INFO  AliMnsService:576 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-8 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-8 [ACTIVE]
	... 11 common frames omitted
2025-07-27 03:46:24.529 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-10 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-10 [ACTIVE]
	... 11 common frames omitted
2025-07-27 03:46:32.609 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-27 03:46:32.609 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-5] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createBareChannel(CachingConnectionFactory.java:702)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getCachedChannelProxy(CachingConnectionFactory.java:676)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getChannel(CachingConnectionFactory.java:567)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.access$1600(CachingConnectionFactory.java:102)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory$ChannelCachingConnectionProxy.createChannel(CachingConnectionFactory.java:1439)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2095)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 16 common frames omitted
2025-07-27 03:46:33.858 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#1393dd72:4/SimpleConnection@558db7f8 [delegate=amqp://admin@**************:5672/, localPort= 61943]
2025-07-27 03:46:40.916 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe473b2d9, L:/********:59944 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:40.916 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd991219c, L:/********:59963 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:40.917 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x74dd1cab, L:/********:59952 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:40.917 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa3d6aec5, L:/********:59945 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:40.917 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6eed9a6b, L:/********:59948 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:40.917 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6cad4f7b, L:/********:59951 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:40.917 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7a7d97f8, L:/********:59960 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.015 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfe95641f, L:/********:59968 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.015 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x158bdcc1, L:/********:59954 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.015 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc1bfe353, L:/********:59964 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.016 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0e11b08b, L:/********:59955 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.016 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x73a7489f, L:/********:59947 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.016 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x15da0edc, L:/********:59959 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.016 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x58bfdf33, L:/********:59949 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.016 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x308abac9, L:/********:59965 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.017 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc9d9e560, L:/********:59950 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.017 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5a3ef803, L:/********:59957 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.017 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x506ccc76, L:/********:59953 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.017 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x84c56150, L:/********:59961 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.117 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x54d93037, L:/********:62335 ! R:/**************:6379]
org.redisson.client.WriteRedisConnectionException: Channel has been closed! Can't write command: (PING), params: [] to channel: [id: 0x158bdcc1, L:/********:59954 ! R:/**************:6379]
	at org.redisson.client.handler.CommandsQueue.channelInactive(CommandsQueue.java:76)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at org.redisson.client.handler.ConnectionWatchdog.channelInactive(ConnectionWatchdog.java:82)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:831)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:497)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.117 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x062225f9, L:/********:62329 ! R:/**************:6379]
org.redisson.client.WriteRedisConnectionException: Channel has been closed! Can't write command: (PING), params: [] to channel: [id: 0xa3d6aec5, L:/********:59945 ! R:/**************:6379]
	at org.redisson.client.handler.CommandsQueue.channelInactive(CommandsQueue.java:76)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at org.redisson.client.handler.ConnectionWatchdog.channelInactive(ConnectionWatchdog.java:82)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:831)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:497)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.118 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x69efa089, L:/********:62330 ! R:/**************:6379]
org.redisson.client.WriteRedisConnectionException: Channel has been closed! Can't write command: (PING), params: [] to channel: [id: 0xe473b2d9, L:/********:59944 ! R:/**************:6379]
	at org.redisson.client.handler.CommandsQueue.channelInactive(CommandsQueue.java:76)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at org.redisson.client.handler.ConnectionWatchdog.channelInactive(ConnectionWatchdog.java:82)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:831)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:497)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.118 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x718d5cad, L:/********:62338 ! R:/**************:6379]
org.redisson.client.WriteRedisConnectionException: Channel has been closed! Can't write command: (PING), params: [] to channel: [id: 0x58bfdf33, L:/********:59949 ! R:/**************:6379]
	at org.redisson.client.handler.CommandsQueue.channelInactive(CommandsQueue.java:76)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at org.redisson.client.handler.ConnectionWatchdog.channelInactive(ConnectionWatchdog.java:82)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:831)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:497)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.118 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe0613ab5, L:/********:59967 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.118 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc6ee6845, L:/********:59958 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.118 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x41399ac0, L:/********:59962 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.118 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x179b51b0, L:/********:59956 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.119 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x39509307, L:/********:59946 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.119 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0a7149e5, L:/********:59966 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:41.415 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3bb0731d, L:/********:64538 ! R:/**************:6379]
org.redisson.client.WriteRedisConnectionException: Channel has been closed! Can't write command: (PING), params: [] to channel: [id: 0x73a7489f, L:/********:59947 ! R:/**************:6379]
	at org.redisson.client.handler.CommandsQueue.channelInactive(CommandsQueue.java:76)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at org.redisson.client.handler.ConnectionWatchdog.channelInactive(ConnectionWatchdog.java:82)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:241)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:262)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:248)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$8.run(AbstractChannel.java:831)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:497)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-27 03:46:47.836 [pool-4-thread-1] INFO  AliMnsService:496 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.http.impl.nio.reactor.SessionInputBufferImpl.fill(SessionInputBufferImpl.java:231)
	at org.apache.http.impl.nio.codecs.AbstractMessageParser.fillBuffer(AbstractMessageParser.java:136)
	at org.apache.http.impl.nio.DefaultNHttpClientConnection.consumeInput(DefaultNHttpClientConnection.java:241)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:81)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:114)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	... 18 common frames omitted
2025-07-27 04:00:00.062 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 04:00:02.194 [schedule-pool-9] INFO  p6spy:60 - 2025-07-27T04:00:02.194 | 耗时 272 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T04:00:00.062+0800' AND is_deleted = 0;
2025-07-27 04:00:02.195 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 04:00:02.195 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 2133 ms
2025-07-27 04:30:00.054 [schedule-pool-1] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 04:30:00.549 [schedule-pool-1] INFO  p6spy:60 - 2025-07-27T04:30:00.549 | 耗时 338 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T04:30:00.054+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 04:30:00.550 [schedule-pool-1] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 04:30:00.550 [schedule-pool-1] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 496 ms
2025-07-27 05:00:00.061 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 05:00:00.442 [schedule-pool-3] INFO  p6spy:60 - 2025-07-27T05:00:00.442 | 耗时 204 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T05:00:00.061+0800' AND is_deleted = 0;
2025-07-27 05:00:00.443 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 05:00:00.443 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 382 ms
2025-07-27 05:30:00.055 [schedule-pool-3] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 05:30:00.336 [schedule-pool-3] INFO  p6spy:60 - 2025-07-27T05:30:00.336 | 耗时 126 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T05:30:00.055+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 05:30:00.337 [schedule-pool-3] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 05:30:00.337 [schedule-pool-3] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 282 ms
2025-07-27 06:00:00.057 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 06:00:00.381 [schedule-pool-9] INFO  p6spy:60 - 2025-07-27T06:00:00.381 | 耗时 183 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T06:00:00.057+0800' AND is_deleted = 0;
2025-07-27 06:00:00.382 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 06:00:00.382 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 325 ms
2025-07-27 06:30:00.074 [schedule-pool-0] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 06:30:00.444 [schedule-pool-0] INFO  p6spy:60 - 2025-07-27T06:30:00.444 | 耗时 125 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T06:30:00.074+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 06:30:00.445 [schedule-pool-0] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 06:30:00.445 [schedule-pool-0] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 371 ms
2025-07-27 07:00:00.061 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 07:00:00.432 [schedule-pool-2] INFO  p6spy:60 - 2025-07-27T07:00:00.432 | 耗时 215 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T07:00:00.061+0800' AND is_deleted = 0;
2025-07-27 07:00:00.433 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 07:00:00.433 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 372 ms
2025-07-27 07:30:00.067 [schedule-pool-1] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 07:30:00.489 [schedule-pool-1] INFO  p6spy:60 - 2025-07-27T07:30:00.489 | 耗时 152 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T07:30:00.067+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 07:30:00.489 [schedule-pool-1] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 07:30:00.490 [schedule-pool-1] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 423 ms
2025-07-27 08:00:00.062 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 08:00:00.429 [schedule-pool-1] INFO  p6spy:60 - 2025-07-27T08:00:00.429 | 耗时 208 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T08:00:00.062+0800' AND is_deleted = 0;
2025-07-27 08:00:00.430 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 08:00:00.430 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 368 ms
2025-07-27 08:30:00.063 [schedule-pool-6] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 08:30:00.522 [schedule-pool-6] INFO  p6spy:60 - 2025-07-27T08:30:00.522 | 耗时 204 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T08:30:00.063+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 08:30:00.523 [schedule-pool-6] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 08:30:00.524 [schedule-pool-6] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 461 ms
2025-07-27 09:00:00.061 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 09:00:00.528 [schedule-pool-2] INFO  p6spy:60 - 2025-07-27T09:00:00.528 | 耗时 213 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T09:00:00.061+0800' AND is_deleted = 0;
2025-07-27 09:00:00.529 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 09:00:00.529 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 468 ms
2025-07-27 09:30:00.066 [schedule-pool-7] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 09:30:00.513 [schedule-pool-7] INFO  p6spy:60 - 2025-07-27T09:30:00.513 | 耗时 178 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T09:30:00.066+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 09:30:00.514 [schedule-pool-7] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 09:30:00.514 [schedule-pool-7] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 448 ms
2025-07-27 10:00:00.057 [schedule-pool-6] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 10:00:00.510 [schedule-pool-6] INFO  p6spy:60 - 2025-07-27T10:00:00.51 | 耗时 243 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T10:00:00.057+0800' AND is_deleted = 0;
2025-07-27 10:00:00.510 [schedule-pool-6] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 10:00:00.510 [schedule-pool-6] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 453 ms
2025-07-27 10:30:00.077 [schedule-pool-4] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 10:30:00.519 [schedule-pool-4] INFO  p6spy:60 - 2025-07-27T10:30:00.519 | 耗时 207 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T10:30:00.077+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 10:30:00.520 [schedule-pool-4] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 10:30:00.520 [schedule-pool-4] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 443 ms
2025-07-27 11:00:00.082 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-07-27 11:00:00.419 [schedule-pool-2] INFO  p6spy:60 - 2025-07-27T11:00:00.419 | 耗时 200 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-07-27T11:00:00.082+0800' AND is_deleted = 0;
2025-07-27 11:00:00.421 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-07-27 11:00:00.421 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 339 ms
2025-07-27 11:30:00.070 [schedule-pool-7] INFO  UserExpirationCheckTask:39 - 开始执行用户过期检查定时任务
2025-07-27 11:30:00.626 [schedule-pool-7] INFO  p6spy:60 - 2025-07-27T11:30:00.626 | 耗时 347 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted FROM duan_ju_user WHERE expire_time < '2025-07-27T11:30:00.070+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-07-27 11:30:00.627 [schedule-pool-7] INFO  UserExpirationCheckTask:79 - 没有需要处理的过期用户
2025-07-27 11:30:00.627 [schedule-pool-7] INFO  UserExpirationCheckTask:50 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 557 ms
