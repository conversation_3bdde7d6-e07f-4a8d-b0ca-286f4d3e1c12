2025-08-05 00:00:00.078 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 00:00:00.390 [schedule-pool-4] INFO  p6spy:60 - 2025-08-05T00:00:00.389 | 耗时 108 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T00:00:00.091+0800' AND is_deleted = 0;
2025-08-05 00:00:00.391 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 00:00:00.392 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 301 ms
2025-08-05 00:30:00.061 [schedule-pool-1] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 00:30:00.408 [schedule-pool-1] INFO  p6spy:60 - 2025-08-05T00:30:00.408 | 耗时 147 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T00:30:00.062+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 00:30:00.410 [schedule-pool-1] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 00:30:00.411 [schedule-pool-1] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 349 ms
2025-08-05 01:00:00.070 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 01:00:00.407 [schedule-pool-4] INFO  p6spy:60 - 2025-08-05T01:00:00.407 | 耗时 201 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T01:00:00.071+0800' AND is_deleted = 0;
2025-08-05 01:00:00.410 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 01:00:00.412 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 341 ms
2025-08-05 01:30:00.060 [schedule-pool-2] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 01:30:00.473 [schedule-pool-2] INFO  p6spy:60 - 2025-08-05T01:30:00.473 | 耗时 167 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T01:30:00.061+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 01:30:00.473 [schedule-pool-2] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 01:30:00.474 [schedule-pool-2] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 413 ms
2025-08-05 02:00:00.064 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 02:00:00.407 [schedule-pool-3] INFO  p6spy:60 - 2025-08-05T02:00:00.406 | 耗时 222 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T02:00:00.065+0800' AND is_deleted = 0;
2025-08-05 02:00:00.409 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 02:00:00.411 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 346 ms
2025-08-05 02:30:00.058 [schedule-pool-5] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 02:30:00.510 [schedule-pool-5] INFO  p6spy:60 - 2025-08-05T02:30:00.51 | 耗时 250 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T02:30:00.059+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 02:30:00.518 [schedule-pool-5] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 02:30:00.520 [schedule-pool-5] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 461 ms
2025-08-05 03:00:00.060 [schedule-pool-9] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 03:00:00.394 [schedule-pool-9] INFO  p6spy:60 - 2025-08-05T03:00:00.394 | 耗时 201 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T03:00:00.060+0800' AND is_deleted = 0;
2025-08-05 03:00:00.396 [schedule-pool-9] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 03:00:00.398 [schedule-pool-9] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 338 ms
2025-08-05 03:30:00.069 [schedule-pool-2] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 03:30:00.358 [schedule-pool-2] INFO  p6spy:60 - 2025-08-05T03:30:00.358 | 耗时 153 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T03:30:00.070+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 03:30:00.359 [schedule-pool-2] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 03:30:00.359 [schedule-pool-2] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 289 ms
2025-08-05 03:42:38.074 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-1 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-1 [ACTIVE]
	... 11 common frames omitted
2025-08-05 04:00:00.068 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 04:00:00.470 [schedule-pool-4] INFO  p6spy:60 - 2025-08-05T04:00:00.47 | 耗时 268 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T04:00:00.070+0800' AND is_deleted = 0;
2025-08-05 04:00:00.475 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 04:00:00.476 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 406 ms
2025-08-05 04:30:00.081 [schedule-pool-2] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 04:30:00.614 [schedule-pool-2] INFO  p6spy:60 - 2025-08-05T04:30:00.614 | 耗时 307 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T04:30:00.082+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 04:30:00.618 [schedule-pool-2] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 04:30:00.620 [schedule-pool-2] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 538 ms
2025-08-05 05:00:00.075 [schedule-pool-1] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 05:00:00.411 [schedule-pool-1] INFO  p6spy:60 - 2025-08-05T05:00:00.411 | 耗时 103 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T05:00:00.075+0800' AND is_deleted = 0;
2025-08-05 05:00:00.413 [schedule-pool-1] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 05:00:00.415 [schedule-pool-1] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 340 ms
2025-08-05 05:30:00.069 [schedule-pool-3] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 05:30:00.474 [schedule-pool-3] INFO  p6spy:60 - 2025-08-05T05:30:00.473 | 耗时 202 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T05:30:00.070+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 05:30:00.475 [schedule-pool-3] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 05:30:00.476 [schedule-pool-3] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 406 ms
2025-08-05 06:00:00.069 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 06:00:00.508 [schedule-pool-3] INFO  p6spy:60 - 2025-08-05T06:00:00.508 | 耗时 299 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T06:00:00.071+0800' AND is_deleted = 0;
2025-08-05 06:00:00.509 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 06:00:00.510 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 439 ms
2025-08-05 06:30:00.057 [schedule-pool-1] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 06:30:00.442 [schedule-pool-1] INFO  p6spy:60 - 2025-08-05T06:30:00.442 | 耗时 241 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T06:30:00.058+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 06:30:00.443 [schedule-pool-1] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 06:30:00.443 [schedule-pool-1] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 385 ms
2025-08-05 07:00:00.060 [schedule-pool-3] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 07:00:00.604 [schedule-pool-3] INFO  p6spy:60 - 2025-08-05T07:00:00.604 | 耗时 404 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T07:00:00.061+0800' AND is_deleted = 0;
2025-08-05 07:00:00.605 [schedule-pool-3] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 07:00:00.605 [schedule-pool-3] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 544 ms
2025-08-05 07:30:00.065 [schedule-pool-5] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 07:30:00.402 [schedule-pool-5] INFO  p6spy:60 - 2025-08-05T07:30:00.402 | 耗时 208 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T07:30:00.065+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 07:30:00.403 [schedule-pool-5] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 07:30:00.404 [schedule-pool-5] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 339 ms
2025-08-05 08:00:00.061 [schedule-pool-7] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 08:00:00.497 [schedule-pool-7] INFO  p6spy:60 - 2025-08-05T08:00:00.497 | 耗时 200 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T08:00:00.062+0800' AND is_deleted = 0;
2025-08-05 08:00:00.501 [schedule-pool-7] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 08:00:00.502 [schedule-pool-7] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 440 ms
2025-08-05 08:30:00.065 [schedule-pool-7] INFO  UserExpirationCheckTask:41 - 开始执行用户过期检查定时任务
2025-08-05 08:30:00.494 [schedule-pool-7] INFO  p6spy:60 - 2025-08-05T08:30:00.494 | 耗时 198 ms | SQL 语句：
SELECT id,nick_name,mobile,open_id,register_time,expire_time,phone_sub_expire_status,third_party_sub_expire_status,member_status,member_type,pay_flat,last_operate_time,source,resource,sub_channel,create_time,update_time,is_deleted,template_id FROM duan_ju_user WHERE expire_time < '2025-08-05T08:30:00.066+0800' AND member_status IN (2,3) AND is_deleted = 0;
2025-08-05 08:30:00.497 [schedule-pool-7] INFO  UserExpirationCheckTask:81 - 没有需要处理的过期用户
2025-08-05 08:30:00.497 [schedule-pool-7] INFO  UserExpirationCheckTask:52 - 用户过期检查定时任务执行完成，处理用户数: 0，耗时: 431 ms
2025-08-05 09:00:00.067 [schedule-pool-2] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 09:00:02.953 [schedule-pool-2] INFO  p6spy:60 - 2025-08-05T09:00:02.953 | 耗时 1961 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T09:00:00.069+0800' AND is_deleted = 0;
2025-08-05 09:00:02.954 [schedule-pool-2] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 09:00:02.954 [schedule-pool-2] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 2885 ms
2025-08-05 09:00:26.403 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5be6f963, L:/********:50924 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.404 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcdced5cf, L:/********:50931 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.405 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xee453fd4, L:/********:50927 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.406 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x57d36e23, L:/********:50926 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.406 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa8f85723, L:/********:50921 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.407 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x509b28dc, L:/********:50925 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.407 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0741028b, L:/********:50922 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.409 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x032a1793, L:0.0.0.0/0.0.0.0:50928 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.411 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xed94ffe5, L:/********:50929 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.411 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3aad5a76, L:0.0.0.0/0.0.0.0:50923]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.412 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4f21abd8, L:0.0.0.0/0.0.0.0:50930 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.413 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcc3843f7, L:0.0.0.0/0.0.0.0:50933]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.414 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xab615937, L:/********:50934 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.417 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x99f1c510, L:0.0.0.0/0.0.0.0:50936]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.542 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x58236a8b, L:/********:50940 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.543 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf2446fe1, L:0.0.0.0/0.0.0.0:50939]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.556 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8a566289, L:/********:50937 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.557 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9a3a1c97, L:/********:50935 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.558 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa9daa600, L:0.0.0.0/0.0.0.0:50938]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.559 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x18474d18, L:0.0.0.0/0.0.0.0:50942]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.560 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x39a5303f, L:/********:50941 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.561 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x535089f8, L:0.0.0.0/0.0.0.0:50944]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.841 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x472f1687, L:/********:50946 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.841 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7f8f88d2, L:/********:50943 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 09:00:26.851 [redisson-timer-6-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf144e2db, L:0.0.0.0/0.0.0.0:50945]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-08-05 15:54:32.764 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-08-05 15:54:35.015 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-08-05 15:54:38.347 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:54:38.348 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-05 15:54:38.467 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 113ms. Found 11 Elasticsearch repository interfaces.
2025-08-05 15:54:39.103 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:54:39.104 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-05 15:54:39.177 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 73ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-05 15:54:39.192 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:54:39.194 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 15:54:39.248 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 46ms. Found 0 Redis repository interfaces.
2025-08-05 15:54:39.798 [main] INFO  GenericScope:295 - BeanFactory id=6012de34-8e6b-3188-9718-70344705731c
2025-08-05 15:54:39.870 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$b43938cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:39.996 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:39.997 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppMobilePayLinkConfigClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:39.998 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:40.000 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$f753bb05] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:40.092 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$89f7675b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:40.555 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:40.560 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$fdc24007] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:40.583 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:40.595 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:40.685 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:41.161 [main] INFO  ShiroConfig:254 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-05 15:54:41.164 [main] INFO  ShiroConfig:272 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-08-05 15:54:41.175 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:41.190 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:41.229 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:41.246 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$ff07c016] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:41.361 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$a74cace6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:41.368 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:54:42.438 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-08-05 15:54:42.462 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-08-05 15:54:42.463 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-08-05 15:54:42.463 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-05 15:54:42.598 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-08-05 15:54:42.598 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 7552 ms
2025-08-05 15:54:44.899 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-08-05 15:54:44.965 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-08-05 15:54:48.794 [main] INFO  DruidDataSource:1003 - {dataSource-1,member} inited
2025-08-05 15:54:50.966 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-08-05 15:54:53.233 [main] INFO  DruidDataSource:1003 - {dataSource-3,miniapp} inited
2025-08-05 15:54:55.383 [main] INFO  DruidDataSource:1003 - {dataSource-4,master} inited
2025-08-05 15:54:55.397 [main] ERROR DruidDataSource:978 - {dataSource-5} init error
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-05 15:54:55.398 [main] ERROR DynamicDataSourceCreator:244 - druid数据源启动失败
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-05 15:54:55.399 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 5 个数据源
2025-08-05 15:54:55.399 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-08-05 15:54:55.400 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-08-05 15:54:55.400 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 centric 成功
2025-08-05 15:54:55.400 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-08-05 15:54:55.400 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-08-05 15:54:55.401 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-08-05 15:55:07.338 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-05 15:55:07.345 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-05 15:55:07.381 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 15:55:07.382 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 15:55:07.395 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-05 15:55:07.400 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-05 15:55:07.403 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-05 15:55:07.403 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-05 15:55:07.403 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-05 15:55:07.403 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@23bc053c
2025-08-05 15:55:09.363 [main] INFO  Version:41 - Redisson 3.15.0
2025-08-05 15:55:11.184 [redisson-netty-4-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-08-05 15:55:12.542 [redisson-netty-4-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-08-05 15:55:14.879 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-05 15:55:14.882 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-08-05 15:55:19.063 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.063 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.064 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.064 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.105 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.105 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.106 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.106 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:55:19.337 [main] TRACE WIRE:64 - [7859c88f] Sending request GET / with parameters: 
2025-08-05 15:55:19.754 [I/O dispatcher 1] TRACE WIRE:97 - [7859c88f] Received raw response: 200 OK
2025-08-05 15:55:19.984 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-08-05 15:55:19.985 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-08-05 15:55:19.985 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-08-05 15:55:19.985 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-08-05 15:55:19.986 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-08-05 15:55:42.720 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-08-05 15:55:44.080 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-08-05 15:55:58.800 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-08-05 15:55:58.802 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-08-05 15:55:58.802 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-08-05 15:55:58.802 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-08-05 15:55:59.226 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-05 15:55:59.226 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-08-05 15:55:59.462 [main] TRACE WIRE:64 - [2a996df9] Sending request HEAD /insert_code_2025-08?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-08-05 15:55:59.552 [I/O dispatcher 1] TRACE WIRE:97 - [2a996df9] Received raw response: 200 OK
2025-08-05 15:55:59.796 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-05 15:55:59.797 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-05 15:55:59.831 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-05 15:55:59.831 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-05 15:56:05.138 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-05 15:56:05.283 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-08-05 15:56:05.845 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-08-05 15:56:05.896 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-08-05 15:56:07.247 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-05 15:56:07.250 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-05 15:56:08.315 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#5418a0f6:0/SimpleConnection@78df7ed3 [delegate=amqp://admin@**************:5672/, localPort= 64073]
2025-08-05 15:56:24.614 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 113.754 seconds (JVM running for 114.647)
2025-08-05 15:56:24.641 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://**********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://**********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-08-05 15:56:25.361 [RMI TCP Connection(19)-**********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 15:56:25.361 [RMI TCP Connection(19)-**********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 15:56:25.507 [RMI TCP Connection(19)-**********] INFO  DispatcherServlet:547 - Completed initialization in 146 ms
2025-08-05 15:56:25.861 [RMI TCP Connection(17)-**********] TRACE WIRE:64 - [69bc4aea] Sending request GET /_cluster/health/ with parameters: 
2025-08-05 15:56:25.953 [I/O dispatcher 1] TRACE WIRE:97 - [69bc4aea] Received raw response: 200 OK
2025-08-05 15:57:51.430 [http-nio-8082-exec-4] WARN  TokenServiceImpl:106 - Token不存在或已过期，token: 7a58f8cee81a452993beb4d884b51c9e, tokenType: default
2025-08-05 15:57:51.430 [http-nio-8082-exec-4] WARN  TokenValidationAspect:87 - Token校验失败，token: 7a58f8cee81a452993beb4d884b51c9e, tokenType: default, 请求路径: /cms-vrbt-miniapp/miniApi/duanju/api/createOrder
2025-08-05 15:57:51.435 [http-nio-8082-exec-4] WARN  TokenExceptionHandler:33 - Token校验异常: Token校验失败: Token无效或已过期
2025-08-05 16:00:00.020 [schedule-pool-4] INFO  UserMembershipExpirationTask:44 - 开始执行会员过期检查定时任务
2025-08-05 16:00:02.722 [schedule-pool-4] INFO  p6spy:60 - 2025-08-05T16:00:02.722 | 耗时 212 ms | SQL 语句：
SELECT id,user_id,order_id,membership_type,membership_name,start_time,expire_time,status,package_id,package_name,duration_days,auto_renew,create_time,update_time,remark,create_by,update_by,is_deleted FROM mini_app_user_membership WHERE status = 1 AND expire_time < '2025-08-05T16:00:00.020+0800' AND is_deleted = 0;
2025-08-05 16:00:03.057 [schedule-pool-4] INFO  UserMembershipExpirationTask:84 - 没有需要处理的过期会员记录
2025-08-05 16:00:03.059 [schedule-pool-4] INFO  UserMembershipExpirationTask:55 - 会员过期检查定时任务执行完成，处理过期会员数: 0，耗时: 3039 ms
2025-08-05 16:19:19.275 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-08-05 16:19:21.521 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-08-05 16:19:24.544 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:19:24.545 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-05 16:19:24.652 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 103ms. Found 11 Elasticsearch repository interfaces.
2025-08-05 16:19:25.283 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:19:25.285 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-05 16:19:25.351 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 65ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-05 16:19:25.367 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:19:25.369 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 16:19:25.416 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 39ms. Found 0 Redis repository interfaces.
2025-08-05 16:19:25.924 [main] INFO  GenericScope:295 - BeanFactory id=af376f54-0461-3fe6-a6b2-807e9e4b9f68
2025-08-05 16:19:25.985 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$7d303d66] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.102 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.104 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppMobilePayLinkConfigClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.105 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.107 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$c04abf9f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.179 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$52ee6bf5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.566 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.569 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c6b944a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.582 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.587 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.654 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.973 [main] INFO  ShiroConfig:254 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-05 16:19:26.976 [main] INFO  ShiroConfig:272 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-08-05 16:19:26.983 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:26.996 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:27.028 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:27.043 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$c7fec4b0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:27.151 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$7043b180] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:27.158 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:19:27.897 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-08-05 16:19:27.914 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-08-05 16:19:27.915 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-08-05 16:19:27.915 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-05 16:19:28.013 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-08-05 16:19:28.014 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 6463 ms
2025-08-05 16:19:28.802 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-08-05 16:19:28.815 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-08-05 16:19:31.780 [main] INFO  DruidDataSource:1003 - {dataSource-1,member} inited
2025-08-05 16:19:34.074 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-08-05 16:19:36.462 [main] INFO  DruidDataSource:1003 - {dataSource-3,miniapp} inited
2025-08-05 16:19:38.702 [main] INFO  DruidDataSource:1003 - {dataSource-4,master} inited
2025-08-05 16:19:38.710 [main] ERROR DruidDataSource:978 - {dataSource-5} init error
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-05 16:19:38.710 [main] ERROR DynamicDataSourceCreator:244 - druid数据源启动失败
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-05 16:19:38.711 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 5 个数据源
2025-08-05 16:19:38.711 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-08-05 16:19:38.711 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-08-05 16:19:38.711 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 centric 成功
2025-08-05 16:19:38.712 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-08-05 16:19:38.712 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-08-05 16:19:38.712 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-08-05 16:19:47.165 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-05 16:19:47.172 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-05 16:19:47.212 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 16:19:47.213 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 16:19:47.228 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-05 16:19:47.236 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-05 16:19:47.238 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-05 16:19:47.239 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-05 16:19:47.239 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-05 16:19:47.239 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@54271d08
2025-08-05 16:19:49.380 [main] INFO  Version:41 - Redisson 3.15.0
2025-08-05 16:19:51.375 [redisson-netty-4-15] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-08-05 16:19:52.647 [redisson-netty-4-20] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-08-05 16:19:55.135 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-05 16:19:55.137 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-08-05 16:19:59.755 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:19:59.755 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:19:59.756 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:19:59.757 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:19:59.799 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:19:59.799 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:19:59.800 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:19:59.800 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:20:00.087 [main] TRACE WIRE:64 - [307cea0a] Sending request GET / with parameters: 
2025-08-05 16:20:00.569 [I/O dispatcher 1] TRACE WIRE:97 - [307cea0a] Received raw response: 200 OK
2025-08-05 16:20:00.856 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-08-05 16:20:00.857 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-08-05 16:20:00.857 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-08-05 16:20:00.857 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-08-05 16:20:00.857 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-08-05 16:20:25.509 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-08-05 16:20:27.077 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-08-05 16:20:42.204 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-08-05 16:20:42.205 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-08-05 16:20:42.205 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-08-05 16:20:42.205 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-08-05 16:20:42.814 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-05 16:20:42.815 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-08-05 16:20:43.181 [main] TRACE WIRE:64 - [2ce5f148] Sending request HEAD /insert_code_2025-08?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-08-05 16:20:43.279 [I/O dispatcher 1] TRACE WIRE:97 - [2ce5f148] Received raw response: 200 OK
2025-08-05 16:20:43.508 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-05 16:20:43.508 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-05 16:20:43.561 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-05 16:20:43.561 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-05 16:20:49.860 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-05 16:20:49.997 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-08-05 16:20:50.579 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-08-05 16:20:50.634 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-08-05 16:20:52.019 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-05 16:20:52.022 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-05 16:20:53.084 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#26381009:0/SimpleConnection@45502ba2 [delegate=amqp://admin@**************:5672/, localPort= 61517]
2025-08-05 16:21:11.088 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-08-05 16:21:13.325 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-08-05 16:21:15.668 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:21:15.669 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-05 16:21:15.781 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 106ms. Found 11 Elasticsearch repository interfaces.
2025-08-05 16:21:16.417 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:21:16.418 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-05 16:21:16.481 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 61ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-05 16:21:16.495 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:21:16.497 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 16:21:16.547 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 43ms. Found 0 Redis repository interfaces.
2025-08-05 16:21:17.085 [main] INFO  GenericScope:295 - BeanFactory id=6012de34-8e6b-3188-9718-70344705731c
2025-08-05 16:21:17.148 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$70b722aa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.299 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.300 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppMobilePayLinkConfigClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.302 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.305 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$b3d1a4e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.379 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$46755139] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.764 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.767 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$ba4029e5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.780 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.784 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:17.851 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:18.184 [main] INFO  ShiroConfig:254 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-05 16:21:18.186 [main] INFO  ShiroConfig:272 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-08-05 16:21:18.195 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:18.205 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:18.234 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:18.251 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$bb85a9f4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:18.357 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$63ca96c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:18.363 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:21:19.099 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-08-05 16:21:19.116 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-08-05 16:21:19.117 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-08-05 16:21:19.117 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-05 16:21:19.210 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-08-05 16:21:19.211 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5857 ms
2025-08-05 16:21:20.026 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-08-05 16:21:20.040 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-08-05 16:21:22.791 [main] INFO  DruidDataSource:1003 - {dataSource-1,member} inited
2025-08-05 16:21:24.875 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-08-05 16:21:27.003 [main] INFO  DruidDataSource:1003 - {dataSource-3,miniapp} inited
2025-08-05 16:21:29.046 [main] INFO  DruidDataSource:1003 - {dataSource-4,master} inited
2025-08-05 16:21:29.053 [main] ERROR DruidDataSource:978 - {dataSource-5} init error
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-05 16:21:29.053 [main] ERROR DynamicDataSourceCreator:244 - druid数据源启动失败
java.sql.SQLException: com.mysql.cj.jdbc.Driver
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:620)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:890)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:331)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.alibaba.druid.util.JdbcUtils.createDriver(JdbcUtils.java:618)
	... 53 common frames omitted
2025-08-05 16:21:29.054 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 5 个数据源
2025-08-05 16:21:29.054 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-08-05 16:21:29.054 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-08-05 16:21:29.054 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 centric 成功
2025-08-05 16:21:29.054 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-08-05 16:21:29.054 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-08-05 16:21:29.055 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-08-05 16:21:36.866 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-05 16:21:36.873 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-05 16:21:36.919 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 16:21:36.920 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 16:21:36.936 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-05 16:21:36.941 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-05 16:21:36.943 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-05 16:21:36.944 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-05 16:21:36.944 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-05 16:21:36.944 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@24d5a3b8
2025-08-05 16:21:39.055 [main] INFO  Version:41 - Redisson 3.15.0
2025-08-05 16:21:40.714 [redisson-netty-4-14] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-08-05 16:21:42.481 [redisson-netty-4-15] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-08-05 16:21:45.089 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-05 16:21:45.090 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-08-05 16:21:49.423 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.423 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.424 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.424 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.466 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.466 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.466 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.467 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 16:21:49.741 [main] TRACE WIRE:64 - [444983c9] Sending request GET / with parameters: 
2025-08-05 16:21:50.163 [I/O dispatcher 1] TRACE WIRE:97 - [444983c9] Received raw response: 200 OK
2025-08-05 16:21:50.380 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-08-05 16:21:50.380 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-08-05 16:21:50.381 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-08-05 16:21:50.381 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-08-05 16:21:50.381 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-08-05 16:22:15.883 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-08-05 16:22:17.933 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-08-05 16:22:38.973 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-08-05 16:22:38.975 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-08-05 16:22:38.975 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-08-05 16:22:38.975 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-08-05 16:22:39.479 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-08-05 16:22:39.480 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-08-05 16:22:39.800 [main] TRACE WIRE:64 - [79f2b409] Sending request HEAD /insert_code_2025-08?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-08-05 16:22:39.893 [I/O dispatcher 1] TRACE WIRE:97 - [79f2b409] Received raw response: 200 OK
2025-08-05 16:22:40.081 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-05 16:22:40.081 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-05 16:22:40.114 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-08-05 16:22:40.114 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-05 16:22:46.352 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-05 16:22:46.484 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-08-05 16:22:47.044 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-08-05 16:22:47.098 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-08-05 16:22:48.477 [main] INFO  InetUtils:170 - Cannot determine local hostname
2025-08-05 16:22:48.480 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-08-05 16:22:49.404 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#5d34e3b3:0/SimpleConnection@4bec43d4 [delegate=amqp://admin@**************:5672/, localPort= 63178]
2025-08-05 16:23:03.222 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 113.991 seconds (JVM running for 114.91)
2025-08-05 16:23:03.251 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://**********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://**********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-08-05 16:23:04.223 [RMI TCP Connection(15)-**********] TRACE WIRE:64 - [46ae8f28] Sending request GET /_cluster/health/ with parameters: 
2025-08-05 16:23:04.322 [I/O dispatcher 1] TRACE WIRE:97 - [46ae8f28] Received raw response: 200 OK
2025-08-05 16:23:04.386 [RMI TCP Connection(17)-**********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 16:23:04.386 [RMI TCP Connection(17)-**********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 16:23:04.536 [RMI TCP Connection(17)-**********] INFO  DispatcherServlet:547 - Completed initialization in 149 ms
