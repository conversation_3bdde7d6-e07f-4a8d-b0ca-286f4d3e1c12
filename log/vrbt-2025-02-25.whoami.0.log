2025-02-25 16:42:14.859 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-02-25 16:42:15.595 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-02-25 16:42:17.099 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:42:17.100 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-02-25 16:42:17.160 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 57ms. Found 11 Elasticsearch repository interfaces.
2025-02-25 16:42:17.540 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:42:17.543 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-02-25 16:42:17.598 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 55ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-02-25 16:42:17.609 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:42:17.610 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-25 16:42:17.655 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 38ms. Found 0 Redis repository interfaces.
2025-02-25 16:42:17.920 [main] INFO  GenericScope:295 - BeanFactory id=ea335453-5c53-3e6f-b258-32ccea021e22
2025-02-25 16:42:17.965 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$74ff44b2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.041 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.042 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$b819c6eb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.085 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$4abd7341] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.290 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.294 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$be884bed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.302 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.304 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.330 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.375 [main] INFO  ShiroConfig:234 - ===============(1)创建缓存管理器RedisCacheManager
2025-02-25 16:42:18.376 [main] INFO  ShiroConfig:252 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-02-25 16:42:18.379 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.385 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.405 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.414 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$bfcdcbfc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.466 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$6812b8cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.469 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:42:18.770 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-02-25 16:42:18.777 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-02-25 16:42:18.777 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-02-25 16:42:18.777 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-02-25 16:42:18.823 [main] INFO  [/cms-vrbt]:173 - Initializing Spring embedded WebApplicationContext
2025-02-25 16:42:18.823 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3209 ms
2025-02-25 16:42:19.360 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-02-25 16:42:19.365 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-02-25 16:42:20.470 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-02-25 16:42:21.424 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-02-25 16:42:22.674 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-02-25 16:42:22.675 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 3 个数据源
2025-02-25 16:42:22.676 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-02-25 16:42:22.677 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-02-25 16:42:22.677 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-02-25 16:42:22.677 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-02-25 16:42:26.647 [main] INFO  Version:41 - Redisson 3.15.0
2025-02-25 16:42:27.249 [redisson-netty-2-17] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-02-25 16:42:27.535 [redisson-netty-2-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-02-25 16:42:28.273 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-02-25 16:42:28.274 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-02-25 16:42:28.280 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-02-25 16:42:28.280 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-02-25 16:42:28.283 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-02-25 16:42:28.284 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-02-25 16:42:28.285 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'whoami1740472948273'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-02-25 16:42:28.285 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-02-25 16:42:28.285 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-02-25 16:42:28.285 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@45bf1ee3
2025-02-25 16:42:30.663 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-02-25 16:42:30.664 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-02-25 16:42:31.703 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.703 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.703 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.704 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.724 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.725 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.725 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.725 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:42:31.767 [main] TRACE WIRE:64 - [11c94cf7] Sending request GET / with parameters: 
2025-02-25 16:42:31.885 [I/O dispatcher 1] TRACE WIRE:97 - [11c94cf7] Received raw response: 200 OK
2025-02-25 16:42:32.118 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-02-25 16:42:32.118 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-02-25 16:42:32.118 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-02-25 16:42:32.119 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-02-25 16:42:32.119 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.17.24
2025-02-25 16:42:32.119 [main] WARN  VersionInfo:72 - Version mismatch in between Elasticsearch Client and Cluster: 7.8.0 - 7.17.24
2025-02-25 16:42:38.753 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-02-25 16:42:39.125 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-02-25 16:42:41.379 [pool-15-thread-3] INFO  AliMnsService:555 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-02-25 16:42:41.379 [pool-15-thread-2] INFO  AliMnsService:475 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-02-25 16:42:41.379 [pool-15-thread-1] INFO  AliMnsService:395 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-02-25 16:42:41.503 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-02-25 16:42:41.503 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-02-25 16:42:41.602 [main] TRACE WIRE:64 - [63ce19a] Sending request HEAD /insert_code_2025-02?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-02-25 16:42:41.650 [I/O dispatcher 1] TRACE WIRE:97 - [63ce19a] Received raw response: 200 OK
2025-02-25 16:42:41.651 [main] WARN  RestClient:65 - request [HEAD http://**************:9200/insert_code_2025-02?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false] returned 1 warnings: [299 Elasticsearch-7.17.24-fcf25fff740db6ab3ed5d145c58d70e4c3528ea7 "[ignore_throttled] parameter is deprecated because frozen indices have been deprecated. Consider cold or frozen tiers in place of frozen indices."]
2025-02-25 16:42:41.666 [main] WARN  ArchaiusAutoConfiguration:123 - No spring.application.name found, defaulting to 'application'
2025-02-25 16:42:41.668 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-02-25 16:42:41.669 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-25 16:42:41.673 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-02-25 16:42:41.673 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-25 16:42:43.297 [main] INFO  EndpointLinksResolver:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-02-25 16:42:43.554 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-02-25 16:42:43.568 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt'
2025-02-25 16:42:43.697 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-02-25 16:42:43.928 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#14d25248:0/SimpleConnection@7c57c200 [delegate=amqp://admin@**************:5672/, localPort= 58573]
2025-02-25 16:42:45.340 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 31.117 seconds (JVM running for 31.689)
2025-02-25 16:42:45.351 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt/
	External: 	http://*************:8082/cms-vrbt/
	Swagger-UI: 		http://*************:8082/cms-vrbt/doc.html
----------------------------------------------------------
2025-02-25 16:42:46.227 [RMI TCP Connection(1)-*************] INFO  [/cms-vrbt]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-25 16:42:46.228 [RMI TCP Connection(1)-*************] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-02-25 16:42:46.320 [RMI TCP Connection(1)-*************] INFO  DispatcherServlet:547 - Completed initialization in 92 ms
2025-02-25 16:42:46.321 [RMI TCP Connection(3)-*************] TRACE WIRE:64 - [5ee8b0d3] Sending request GET /_cluster/health/ with parameters: 
2025-02-25 16:42:46.368 [I/O dispatcher 1] TRACE WIRE:97 - [5ee8b0d3] Received raw response: 200 OK
2025-02-25 16:42:55.798 [pool-5-thread-1] INFO  RedisDelayedQueueManager:391 - ===============回调任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-4-thread-1] INFO  RedisDelayedQueueManager:370 - ===============回调任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-10-thread-1] INFO  RedisDelayedQueueManager:496 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-12-thread-1] INFO  RedisDelayedQueueManager:538 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-11-thread-1] INFO  RedisDelayedQueueManager:517 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-8-thread-1] INFO  RedisDelayedQueueManager:453 - ===============企业彩铃任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-7-thread-1] INFO  RedisDelayedQueueManager:433 - ===============企业彩铃任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-3-thread-1] INFO  RedisDelayedQueueManager:350 - ===============延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-9-thread-1] INFO  RedisDelayedQueueManager:475 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:42:55.798 [pool-6-thread-1] INFO  RedisDelayedQueueManager:412 - ===============回调任务延时队列监听器启动==============
2025-02-25 16:43:03.831 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-02-25 16:43:03.833 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:649 - Successfully waited for workers to finish.
2025-02-25 16:43:03.882 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-02-25 16:43:04.068 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:649 - Successfully waited for workers to finish.
2025-02-25 16:43:04.168 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-02-25 16:46:58.255 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-02-25 16:46:58.958 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-02-25 16:47:00.466 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:47:00.467 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-02-25 16:47:00.527 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 56ms. Found 11 Elasticsearch repository interfaces.
2025-02-25 16:47:00.894 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:47:00.894 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-02-25 16:47:00.947 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 52ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-02-25 16:47:00.958 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:47:00.959 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-25 16:47:01.006 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 42ms. Found 0 Redis repository interfaces.
2025-02-25 16:47:01.262 [main] INFO  GenericScope:295 - BeanFactory id=ea335453-5c53-3e6f-b258-32ccea021e22
2025-02-25 16:47:01.302 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$3889aa10] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.375 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.377 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$7ba42c49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.418 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$e47d89f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.620 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.623 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8212b14b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.631 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.633 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.660 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.705 [main] INFO  ShiroConfig:234 - ===============(1)创建缓存管理器RedisCacheManager
2025-02-25 16:47:01.706 [main] INFO  ShiroConfig:252 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-02-25 16:47:01.709 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.715 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.736 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.746 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$8358315a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.799 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$2b9d1e2a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:01.802 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:47:02.100 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-02-25 16:47:02.107 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-02-25 16:47:02.108 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-02-25 16:47:02.108 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-02-25 16:47:02.147 [main] INFO  [/cms-vrbt]:173 - Initializing Spring embedded WebApplicationContext
2025-02-25 16:47:02.148 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3169 ms
2025-02-25 16:47:02.755 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-02-25 16:47:02.759 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-02-25 16:47:03.918 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-02-25 16:47:04.871 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-02-25 16:47:06.060 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-02-25 16:47:06.062 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 3 个数据源
2025-02-25 16:47:06.063 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-02-25 16:47:06.063 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-02-25 16:47:06.063 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-02-25 16:47:06.064 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-02-25 16:47:10.074 [main] INFO  Version:41 - Redisson 3.15.0
2025-02-25 16:47:10.674 [redisson-netty-2-23] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-02-25 16:47:10.963 [redisson-netty-2-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-02-25 16:47:11.643 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-02-25 16:47:11.644 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-02-25 16:47:11.652 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-02-25 16:47:11.652 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-02-25 16:47:11.656 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-02-25 16:47:11.659 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-02-25 16:47:11.662 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'whoami1740473231644'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-02-25 16:47:11.663 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-02-25 16:47:11.663 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-02-25 16:47:11.664 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4ea01c2a
2025-02-25 16:47:14.624 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-02-25 16:47:14.626 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-02-25 16:47:15.841 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.842 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.842 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.842 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.864 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.865 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.865 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.865 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:47:15.908 [main] TRACE WIRE:64 - [7fe646a] Sending request GET / with parameters: 
2025-02-25 16:47:16.250 [I/O dispatcher 1] TRACE WIRE:97 - [7fe646a] Received raw response: 200 OK
2025-02-25 16:47:16.283 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-02-25 16:47:16.284 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-02-25 16:47:16.284 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-02-25 16:47:16.284 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-02-25 16:47:16.284 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.17.24
2025-02-25 16:47:16.284 [main] WARN  VersionInfo:72 - Version mismatch in between Elasticsearch Client and Cluster: 7.8.0 - 7.17.24
2025-02-25 16:47:22.973 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-02-25 16:47:23.359 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-02-25 16:47:27.879 [pool-15-thread-1] INFO  AliMnsService:395 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-02-25 16:47:27.880 [pool-15-thread-2] INFO  AliMnsService:475 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-02-25 16:47:27.894 [pool-15-thread-3] INFO  AliMnsService:555 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-02-25 16:47:28.339 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-02-25 16:47:28.340 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-02-25 16:47:28.457 [main] TRACE WIRE:64 - [6c6f49d1] Sending request HEAD /insert_code_2025-02?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-02-25 16:47:28.508 [I/O dispatcher 1] TRACE WIRE:97 - [6c6f49d1] Received raw response: 200 OK
2025-02-25 16:47:28.509 [main] WARN  RestClient:65 - request [HEAD http://**************:9200/insert_code_2025-02?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false] returned 1 warnings: [299 Elasticsearch-7.17.24-fcf25fff740db6ab3ed5d145c58d70e4c3528ea7 "[ignore_throttled] parameter is deprecated because frozen indices have been deprecated. Consider cold or frozen tiers in place of frozen indices."]
2025-02-25 16:47:28.529 [main] WARN  ArchaiusAutoConfiguration:123 - No spring.application.name found, defaulting to 'application'
2025-02-25 16:47:28.532 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-02-25 16:47:28.532 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-25 16:47:28.596 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-02-25 16:47:28.596 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-25 16:47:30.848 [main] INFO  EndpointLinksResolver:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-02-25 16:47:31.064 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-02-25 16:47:31.076 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt'
2025-02-25 16:47:31.191 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-02-25 16:47:31.415 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#2540ad71:0/SimpleConnection@4dbc47b1 [delegate=amqp://admin@**************:5672/, localPort= 58886]
2025-02-25 16:47:32.799 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 35.011 seconds (JVM running for 35.571)
2025-02-25 16:47:32.804 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt/
	External: 	http://*************:8082/cms-vrbt/
	Swagger-UI: 		http://*************:8082/cms-vrbt/doc.html
----------------------------------------------------------
2025-02-25 16:47:33.247 [RMI TCP Connection(18)-*************] INFO  [/cms-vrbt]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-25 16:47:33.247 [RMI TCP Connection(18)-*************] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-02-25 16:47:33.266 [RMI TCP Connection(18)-*************] INFO  DispatcherServlet:547 - Completed initialization in 19 ms
2025-02-25 16:47:33.337 [RMI TCP Connection(17)-*************] TRACE WIRE:64 - [4f94bf3b] Sending request GET /_cluster/health/ with parameters: 
2025-02-25 16:47:33.386 [I/O dispatcher 1] TRACE WIRE:97 - [4f94bf3b] Received raw response: 200 OK
2025-02-25 16:47:39.913 [pool-9-thread-1] INFO  RedisDelayedQueueManager:475 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:47:39.913 [pool-8-thread-1] INFO  RedisDelayedQueueManager:453 - ===============企业彩铃任务延时队列监听器启动==============
2025-02-25 16:47:39.913 [pool-3-thread-1] INFO  RedisDelayedQueueManager:350 - ===============延时队列监听器启动==============
2025-02-25 16:47:39.913 [pool-7-thread-1] INFO  RedisDelayedQueueManager:433 - ===============企业彩铃任务延时队列监听器启动==============
2025-02-25 16:47:39.913 [pool-12-thread-1] INFO  RedisDelayedQueueManager:538 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:47:39.922 [pool-10-thread-1] INFO  RedisDelayedQueueManager:496 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:47:39.923 [pool-4-thread-1] INFO  RedisDelayedQueueManager:370 - ===============回调任务延时队列监听器启动==============
2025-02-25 16:47:39.922 [pool-11-thread-1] INFO  RedisDelayedQueueManager:517 - ===============数据回执任务延时队列监听器启动==============
2025-02-25 16:47:39.923 [pool-5-thread-1] INFO  RedisDelayedQueueManager:391 - ===============回调任务延时队列监听器启动==============
2025-02-25 16:47:39.923 [pool-6-thread-1] INFO  RedisDelayedQueueManager:412 - ===============回调任务延时队列监听器启动==============
2025-02-25 16:48:16.911 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-02-25 16:48:17.480 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:649 - Successfully waited for workers to finish.
2025-02-25 16:48:17.577 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-02-25 16:48:17.906 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:649 - Successfully waited for workers to finish.
2025-02-25 16:48:17.958 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-02-25 16:48:18.192 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:649 - Successfully waited for workers to finish.
2025-02-25 16:48:18.318 [SpringContextShutdownHook] INFO  ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'outSidecallbackExecutor'
2025-02-25 16:48:18.450 [SpringContextShutdownHook] INFO  ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-02-25 16:48:18.451 [SpringContextShutdownHook] INFO  SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-02-25 16:48:18.451 [SpringContextShutdownHook] INFO  QuartzScheduler:666 - Scheduler MyScheduler_$_whoami1740473231644 shutting down.
2025-02-25 16:48:18.451 [SpringContextShutdownHook] INFO  QuartzScheduler:585 - Scheduler MyScheduler_$_whoami1740473231644 paused.
2025-02-25 16:48:18.453 [SpringContextShutdownHook] INFO  QuartzScheduler:740 - Scheduler MyScheduler_$_whoami1740473231644 shutdown complete.
2025-02-25 16:48:18.536 [SpringContextShutdownHook] INFO  DynamicRoutingDataSource:182 - closing dynamicDatasource  ing....
2025-02-25 16:48:18.539 [SpringContextShutdownHook] INFO  DruidDataSource:1948 - {dataSource-3} closing ...
2025-02-25 16:48:18.545 [SpringContextShutdownHook] INFO  DruidDataSource:2020 - {dataSource-3} closed
2025-02-25 16:48:18.546 [SpringContextShutdownHook] INFO  DruidDataSource:1948 - {dataSource-2} closing ...
2025-02-25 16:48:18.547 [SpringContextShutdownHook] INFO  DruidDataSource:2020 - {dataSource-2} closed
2025-02-25 16:48:18.547 [SpringContextShutdownHook] INFO  DruidDataSource:1948 - {dataSource-1} closing ...
2025-02-25 16:48:18.548 [SpringContextShutdownHook] INFO  DruidDataSource:2020 - {dataSource-1} closed
2025-02-25 16:48:52.990 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-02-25 16:48:53.691 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-02-25 16:48:55.146 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:48:55.147 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-02-25 16:48:55.206 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 55ms. Found 11 Elasticsearch repository interfaces.
2025-02-25 16:48:55.576 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:48:55.577 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-02-25 16:48:55.627 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 50ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-02-25 16:48:55.636 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-25 16:48:55.637 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-25 16:48:55.674 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 31ms. Found 0 Redis repository interfaces.
2025-02-25 16:48:55.938 [main] INFO  GenericScope:295 - BeanFactory id=ea335453-5c53-3e6f-b258-32ccea021e22
2025-02-25 16:48:55.978 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$3889aa10] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.055 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.056 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$7ba42c49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.099 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$e47d89f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.297 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.299 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8212b14b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.307 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.310 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.338 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.385 [main] INFO  ShiroConfig:234 - ===============(1)创建缓存管理器RedisCacheManager
2025-02-25 16:48:56.386 [main] INFO  ShiroConfig:252 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-02-25 16:48:56.389 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.394 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.414 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.423 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$8358315a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.474 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$2b9d1e2a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.477 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 16:48:56.766 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-02-25 16:48:56.774 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-02-25 16:48:56.774 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-02-25 16:48:56.775 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-02-25 16:48:56.814 [main] INFO  [/cms-vrbt]:173 - Initializing Spring embedded WebApplicationContext
2025-02-25 16:48:56.814 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3103 ms
2025-02-25 16:48:57.386 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-02-25 16:48:57.390 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-02-25 16:48:58.503 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-02-25 16:48:59.448 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-02-25 16:49:00.695 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-02-25 16:49:00.695 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 3 个数据源
2025-02-25 16:49:00.696 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-02-25 16:49:00.696 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-02-25 16:49:00.696 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-02-25 16:49:00.696 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-02-25 16:49:04.565 [main] INFO  Version:41 - Redisson 3.15.0
2025-02-25 16:49:05.127 [redisson-netty-2-23] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-02-25 16:49:05.414 [redisson-netty-2-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-02-25 16:49:06.125 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-02-25 16:49:06.126 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-02-25 16:49:06.132 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-02-25 16:49:06.132 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-02-25 16:49:06.135 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-02-25 16:49:06.136 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-02-25 16:49:06.136 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'whoami1740473346125'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-02-25 16:49:06.136 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-02-25 16:49:06.137 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-02-25 16:49:06.137 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@538c823c
2025-02-25 16:49:08.689 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-02-25 16:49:08.689 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-02-25 16:49:09.908 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.909 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.909 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.909 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.927 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.928 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.928 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.928 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-02-25 16:49:09.971 [main] TRACE WIRE:64 - [4cdc1d81] Sending request GET / with parameters: 
2025-02-25 16:49:10.103 [I/O dispatcher 1] TRACE WIRE:97 - [4cdc1d81] Received raw response: 200 OK
2025-02-25 16:49:10.332 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-02-25 16:49:10.332 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-02-25 16:49:10.332 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-02-25 16:49:10.333 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-02-25 16:49:10.333 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.17.24
2025-02-25 16:49:10.333 [main] WARN  VersionInfo:72 - Version mismatch in between Elasticsearch Client and Cluster: 7.8.0 - 7.17.24
2025-02-25 16:49:16.380 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-02-25 16:49:16.700 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-02-25 16:49:18.757 [pool-5-thread-3] INFO  AliMnsService:555 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-02-25 16:49:18.757 [pool-5-thread-2] INFO  AliMnsService:475 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-02-25 16:49:18.757 [pool-5-thread-1] INFO  AliMnsService:395 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-02-25 16:49:18.869 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-02-25 16:49:18.869 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-02-25 16:49:18.952 [main] TRACE WIRE:64 - [66910ced] Sending request HEAD /insert_code_2025-02?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-02-25 16:49:19.000 [I/O dispatcher 1] TRACE WIRE:97 - [66910ced] Received raw response: 200 OK
2025-02-25 16:49:19.001 [main] WARN  RestClient:65 - request [HEAD http://**************:9200/insert_code_2025-02?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false] returned 1 warnings: [299 Elasticsearch-7.17.24-fcf25fff740db6ab3ed5d145c58d70e4c3528ea7 "[ignore_throttled] parameter is deprecated because frozen indices have been deprecated. Consider cold or frozen tiers in place of frozen indices."]
2025-02-25 16:49:19.014 [main] WARN  ArchaiusAutoConfiguration:123 - No spring.application.name found, defaulting to 'application'
2025-02-25 16:49:19.017 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-02-25 16:49:19.017 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-25 16:49:19.021 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-02-25 16:49:19.021 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-25 16:49:20.472 [main] INFO  EndpointLinksResolver:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-02-25 16:49:20.735 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-02-25 16:49:20.748 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt'
2025-02-25 16:49:20.866 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-02-25 16:49:21.088 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#cfdd1c4:0/SimpleConnection@46061e1c [delegate=amqp://admin@**************:5672/, localPort= 59122]
2025-02-25 16:49:22.400 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 29.876 seconds (JVM running for 30.388)
2025-02-25 16:49:22.406 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt/
	External: 	http://*************:8082/cms-vrbt/
	Swagger-UI: 		http://*************:8082/cms-vrbt/doc.html
----------------------------------------------------------
2025-02-25 16:49:22.732 [RMI TCP Connection(1)-*************] TRACE WIRE:64 - [68f1bb9c] Sending request GET /_cluster/health/ with parameters: 
2025-02-25 16:49:22.780 [I/O dispatcher 1] TRACE WIRE:97 - [68f1bb9c] Received raw response: 200 OK
2025-02-25 16:49:22.916 [RMI TCP Connection(3)-*************] INFO  [/cms-vrbt]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-25 16:49:22.917 [RMI TCP Connection(3)-*************] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-02-25 16:49:22.934 [RMI TCP Connection(3)-*************] INFO  DispatcherServlet:547 - Completed initialization in 17 ms
2025-02-25 16:49:30.975 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:646 - Waiting for workers to finish.
2025-02-25 16:49:31.796 [SpringContextShutdownHook] INFO  SimpleMessageListenerContainer:649 - Successfully waited for workers to finish.
