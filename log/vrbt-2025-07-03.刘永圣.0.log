2025-07-03 02:57:28.861 [AMQP Connection **************:5672] WARN  ForgivingExceptionHandler:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-03 02:57:28.892 [redisson-netty-6-30] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xe09c3b6f, L:/********:63599 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-8] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x10c4ab81, L:/********:63578 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-26] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd894f21c, L:/********:63594 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xb7b630bd, L:/********:63588 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x4efd760a, L:/********:63584 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-5] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x0e1551c8, L:/********:63602 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-15] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x117c3c6b, L:/********:63604 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-1] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xee003620, L:/********:63597 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [lettuce-nioEventLoop-12-1] INFO  CommandHandler:217 - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-11] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x43dd5a36, L:/********:63582 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-6] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x99aacc4d, L:/********:63586 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-14] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x309c789e, L:/********:63603 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-28] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x7f3f266e, L:/********:63596 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-27] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x388cdc8a, L:/********:63593 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-2] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc65ba0c3, L:/********:63583 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-29] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x7c940b12, L:/********:63595 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-32] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x439c998f, L:/********:63600 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xaeeb6255, L:/********:63580 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-7] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x017b701e, L:/********:63587 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-12] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x4a6fb278, L:/********:63585 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.892 [redisson-netty-6-4] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x8f3a0865, L:/********:63579 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-31] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x2e5e1dde, L:/********:63598 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.894 [redisson-netty-6-25] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xb526a6b6, L:/********:63592 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.896 [schedule-pool-9] ERROR TaskUtils$LoggingErrorHandler:95 - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.del(LettuceKeyCommands.java:128)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.del(DefaultedRedisConnection.java:82)
	at org.springframework.data.redis.core.RedisTemplate.lambda$delete$2(RedisTemplate.java:713)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.delete(RedisTemplate.java:713)
	at org.jeecg.common.util.RedisUtil.del(RedisUtil.java:87)
	at com.eleven.cms.aivrbt.annotaion.aop.DistributedLockAspect.around(DistributedLockAspect.java:41)
	at sun.reflect.GeneratedMethodAccessor302.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.eleven.cms.aivrbt.service.impl.TencentAiService$$EnhancerBySpringCGLIB$$3fbe534d.queryTencentJobScheduled(<generated>)
	at sun.reflect.GeneratedMethodAccessor313.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy799.del(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.del(LettuceKeyCommands.java:126)
	... 34 common frames omitted
Caused by: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-07-03 02:57:28.901 [redisson-netty-6-3] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x44b5188d, L:/********:63601 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.901 [redisson-netty-6-14] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6d8cc803, L:/********:63591 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.901 [redisson-netty-6-5] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6808254d, L:/********:63581 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 02:57:28.905 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.906 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.907 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.908 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.908 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.909 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.910 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.910 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.911 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:28.911 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 02:57:29.057 [lettuce-eventExecutorLoop-4-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-07-03 02:57:29.057 [lettuce-eventExecutorLoop-1-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-07-03 02:57:29.607 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1007d7b1: tags=[[amq.ctag-spnNCeU1ErgTNeXcVxmwlg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,2), conn: Proxy@658809f3 Shared Rabbit Connection: SimpleConnection@5033ff30 [delegate=amqp://admin@**************:5672/, localPort= 63725], acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.607 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@2b70e806: tags=[[amq.ctag-l76zkYEwsSX_NBdSgP5YDg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,7), conn: Proxy@658809f3 Shared Rabbit Connection: SimpleConnection@5033ff30 [delegate=amqp://admin@**************:5672/, localPort= 63725], acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.610 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@4a3637ed: tags=[[amq.ctag-8vcGYARuee6Ch87lvjF7Bg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,9), conn: Proxy@658809f3 Shared Rabbit Connection: SimpleConnection@5033ff30 [delegate=amqp://admin@**************:5672/, localPort= 63725], acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.610 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@225b9cf3: tags=[[amq.ctag-ox2UEBar99zJLJr4XIjY8g]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,5), conn: Proxy@658809f3 Shared Rabbit Connection: SimpleConnection@5033ff30 [delegate=amqp://admin@**************:5672/, localPort= 63725], acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.618 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-3] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 02:57:29.640 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@37c7abb4: tags=[[amq.ctag-Ejhl47ls418MbJfO2m0A_w]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,4), conn: Proxy@658809f3 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.640 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@75472842: tags=[[amq.ctag-bK3l1EsljSH_Q3pZbX73uw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@658809f3 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.823 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@47875773: tags=[[amq.ctag-HZclC50Jqd2xblcdU6qPAg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,6), conn: Proxy@658809f3 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.823 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@44473eed: tags=[[amq.ctag-ZEVgn53V3shluX_15xDHrg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,3), conn: Proxy@658809f3 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:29.823 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@37f7c2af: tags=[[amq.ctag-TzCaa8xJ6PdIYwYvc0ZKZw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,8), conn: Proxy@658809f3 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 02:57:38.038 [pool-4-thread-1] INFO  AliMnsService:496 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	... 11 common frames omitted
2025-07-03 02:57:39.152 [lettuce-nioEventLoop-11-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-03 02:57:39.152 [lettuce-nioEventLoop-12-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-03 02:57:39.208 [lettuce-eventExecutorLoop-1-7] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-03 02:57:39.208 [lettuce-eventExecutorLoop-4-5] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-03 02:57:39.332 [lettuce-nioEventLoop-12-3] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-07-03 02:57:39.333 [lettuce-nioEventLoop-11-3] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-07-03 02:57:39.737 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-2 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-2 [ACTIVE]
	... 11 common frames omitted
2025-07-03 02:57:41.463 [pool-4-thread-2] INFO  AliMnsService:576 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-3 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-3 [ACTIVE]
	... 11 common frames omitted
2025-07-03 02:57:50.706 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 02:57:50.706 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-3] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createBareChannel(CachingConnectionFactory.java:702)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getCachedChannelProxy(CachingConnectionFactory.java:676)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getChannel(CachingConnectionFactory.java:567)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.access$1600(CachingConnectionFactory.java:102)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory$ChannelCachingConnectionProxy.createChannel(CachingConnectionFactory.java:1439)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2095)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 16 common frames omitted
2025-07-03 02:57:52.111 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#70137569:2/SimpleConnection@39e87e2 [delegate=amqp://admin@**************:5672/, localPort= 61595]
2025-07-03 02:58:02.525 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8f3a0865, L:/********:63579 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.566 [pool-4-thread-1] INFO  AliMnsService:496 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.http.impl.nio.reactor.SessionInputBufferImpl.fill(SessionInputBufferImpl.java:231)
	at org.apache.http.impl.nio.codecs.AbstractMessageParser.fillBuffer(AbstractMessageParser.java:136)
	at org.apache.http.impl.nio.DefaultNHttpClientConnection.consumeInput(DefaultNHttpClientConnection.java:241)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:81)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:114)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	... 18 common frames omitted
2025-07-03 02:58:02.611 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x017b701e, L:/********:63587 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.612 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4efd760a, L:/********:63584 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.614 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6808254d, L:/********:63581 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.615 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb7b630bd, L:/********:63588 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.615 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc65ba0c3, L:/********:63583 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.616 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaeeb6255, L:/********:63580 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.616 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x10c4ab81, L:/********:63578 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.616 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x99aacc4d, L:/********:63586 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.617 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4a6fb278, L:/********:63585 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:02.617 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x43dd5a36, L:/********:63582 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.211 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6d8cc803, L:/********:63591 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.311 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd894f21c, L:/********:63594 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.413 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x388cdc8a, L:/********:63593 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.413 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7f3f266e, L:/********:63596 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.413 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7c940b12, L:/********:63595 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.414 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xee003620, L:/********:63597 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.414 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe09c3b6f, L:/********:63599 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.414 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x439c998f, L:/********:63600 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.414 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb526a6b6, L:/********:63592 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:03.415 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2e5e1dde, L:/********:63598 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:04.017 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x44b5188d, L:/********:63601 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:04.312 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0e1551c8, L:/********:63602 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:04.410 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x309c789e, L:/********:63603 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 02:58:04.410 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x117c3c6b, L:/********:63604 ! R:/**************:6379]
io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-03 08:58:58.756 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 08:58:59.860 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 08:59:03.007 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 08:59:03.008 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 08:59:03.110 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 96ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 08:59:03.776 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 08:59:03.777 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 08:59:03.854 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 76ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 08:59:03.870 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 08:59:03.872 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 08:59:03.929 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 48ms. Found 0 Redis repository interfaces.
2025-07-03 08:59:05.258 [main] INFO  GenericScope:295 - BeanFactory id=35255a23-9860-3ebc-992e-bb403135b10a
2025-07-03 08:59:05.355 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$5b36909e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:05.549 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:05.550 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:05.554 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$9e5112d7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:05.707 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$30f4bf2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.239 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.243 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a4bf97d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.259 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.264 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.350 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.688 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 08:59:06.692 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 08:59:06.711 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.724 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.758 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.773 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$a60517e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.895 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$4e4a04b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:06.903 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 08:59:07.526 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 08:59:07.545 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 08:59:07.547 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 08:59:07.547 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 08:59:07.834 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 08:59:07.835 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 7948 ms
2025-07-03 08:59:08.682 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 08:59:08.696 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 08:59:12.248 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 08:59:15.312 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 08:59:18.760 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 08:59:22.962 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 08:59:22.962 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 08:59:22.962 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 08:59:22.962 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 08:59:22.963 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 08:59:22.963 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 08:59:22.963 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 08:59:30.550 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 08:59:30.557 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 08:59:30.595 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 08:59:30.596 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 08:59:30.613 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 08:59:30.620 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 08:59:30.623 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 08:59:30.623 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 08:59:30.623 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 08:59:30.624 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6423fba7
2025-07-03 08:59:34.352 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 08:59:34.354 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 08:59:37.015 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 08:59:40.031 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 08:59:42.421 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 08:59:43.611 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.611 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.612 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.612 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.650 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.651 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.651 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.651 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 08:59:43.867 [main] TRACE WIRE:64 - [602af317] Sending request GET / with parameters: 
2025-07-03 08:59:44.658 [I/O dispatcher 1] TRACE WIRE:97 - [602af317] Received raw response: 200 OK
2025-07-03 08:59:44.870 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 08:59:44.870 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 08:59:44.870 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 08:59:44.870 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 08:59:44.871 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 09:00:02.432 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 09:00:03.572 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 09:00:14.007 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 09:00:14.008 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 09:00:14.008 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 09:00:14.008 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 09:00:14.463 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 09:00:14.464 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 09:00:14.756 [main] TRACE WIRE:64 - [446c63b4] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 09:00:15.190 [I/O dispatcher 1] TRACE WIRE:97 - [446c63b4] Received raw response: 200 OK
2025-07-03 09:00:15.306 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:00:15.306 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:00:15.345 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:00:15.346 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:00:19.110 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 09:00:19.878 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 09:00:19.963 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 09:00:20.244 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 09:00:22.170 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#66dee88a:0/SimpleConnection@2e0cc9c8 [delegate=amqp://admin@**************:5672/, localPort= 62005]
2025-07-03 09:00:36.501 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 98.549 seconds (JVM running for 99.518)
2025-07-03 09:00:36.530 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 09:00:36.749 [RMI TCP Connection(37)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 09:00:36.749 [RMI TCP Connection(37)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 09:00:36.794 [RMI TCP Connection(37)-*********] INFO  DispatcherServlet:547 - Completed initialization in 44 ms
2025-07-03 09:00:37.597 [RMI TCP Connection(40)-*********] TRACE WIRE:64 - [4d2eaf47] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 09:00:38.763 [I/O dispatcher 1] TRACE WIRE:97 - [4d2eaf47] Received raw response: 200 OK
2025-07-03 09:00:49.507 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:525 - 抖音开放平台请求参数:{"client_key":"tt13a865158c9932af01","grant_type":"client_credential","client_secret":"12ac3c42f6b7711443911cc3503cab01deb908c9"},url:https://open.douyin.com/oauth/client_token/
2025-07-03 09:00:50.592 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:535 - 抖音开放平台请求结果:{"data":{"access_token":"clt.1031e4b7a3bd1bb14994c94876d4bd8frGYUIb3iXJ711fIzmpI1TVwMoJQY_lq","captcha":"","desc_url":"","description":"","error_code":0,"expires_in":7200,"log_id":"20250703090050A9A6AF68E4CBD86D6939"},"message":"success"}
2025-07-03 09:00:51.109 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:525 - 抖音开放平台请求参数:{episode_id=7508557231474868773, album_id=7507553384406942246, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/play_info/
2025-07-03 09:00:52.617 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:535 - 抖音开放平台请求结果:{"data":{"bitrate":10292312,"codec":"h264","definition":"1080p","format":"mp4","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0\u0026auth_key=1751511787-70a56fab4ccb40efa3b5a0872e84cdcb-0-80a8a759cb4e93ace58e47f21e1b96f4\u0026br=10051\u0026bt=10051\u0026cd=0%7C0%7C0\u0026ch=0\u0026cr=0\u0026cs=0\u0026dr=0\u0026ds=4\u0026eid=v0279cg10004d0rt0aaljht8scvm7fjg\u0026er=\u0026l=20250703090052C5AE2764362DEE1DCCF2\u0026lr=\u0026mime_type=video_mp4\u0026net=0\u0026pl=0\u0026qs=13\u0026rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D\u0026vl=\u0026vr=","size":174248843,"url_expire":"1751511352"},"err_msg":"","err_no":0,"log_id":"202507030900524D8A276A1F62C84F604A"}
2025-07-03 09:00:52.938 [http-nio-8082-exec-3] INFO  DouYinApiController:235 - result:OpenApiRes(err_no=0, err_msg=, log_id=202507030900524D8A276A1F62C84F604A, data={"codec":"h264","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0&auth_key=1751511787-70a56fab4ccb40efa3b5a0872e84cdcb-0-80a8a759cb4e93ace58e47f21e1b96f4&br=10051&bt=10051&cd=0%7C0%7C0&ch=0&cr=0&cs=0&dr=0&ds=4&eid=v0279cg10004d0rt0aaljht8scvm7fjg&er=&l=20250703090052C5AE2764362DEE1DCCF2&lr=&mime_type=video_mp4&net=0&pl=0&qs=13&rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D&vl=&vr=","size":174248843,"format":"mp4","bitrate":10292312,"definition":"1080p","url_expire":"1751511352"}, success=null)
2025-07-03 09:01:55.107 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 09:01:56.167 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 09:01:58.535 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:01:58.536 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 09:01:58.670 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 129ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 09:01:59.399 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:01:59.399 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 09:01:59.456 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 56ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 09:01:59.471 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:01:59.472 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 09:01:59.522 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 43ms. Found 0 Redis repository interfaces.
2025-07-03 09:02:00.183 [main] INFO  GenericScope:295 - BeanFactory id=35255a23-9860-3ebc-992e-bb403135b10a
2025-07-03 09:02:00.251 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$e78e1883] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.365 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.366 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.369 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$2aa89abc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.449 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$bd4c4712] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.801 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.803 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$31171fbe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.820 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.827 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:00.916 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:01.312 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 09:02:01.315 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 09:02:01.327 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:01.339 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:01.403 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:01.435 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$325c9fcd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:01.568 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$daa18c9d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:01.576 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:02:02.115 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 09:02:02.129 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 09:02:02.130 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 09:02:02.130 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 09:02:02.290 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 09:02:02.291 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 6098 ms
2025-07-03 09:02:03.254 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 09:02:03.266 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 09:02:12.325 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 09:02:22.351 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 09:02:34.229 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 09:02:43.271 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 09:02:43.273 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 09:02:43.274 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 09:02:43.275 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 09:02:43.276 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 09:02:43.277 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 09:02:43.277 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 09:02:50.139 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 09:02:50.145 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 09:02:50.178 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 09:02:50.178 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 09:02:50.192 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 09:02:50.198 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 09:02:50.201 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 09:02:50.201 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 09:02:50.201 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 09:02:50.202 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@75084fe4
2025-07-03 09:02:54.105 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 09:02:54.106 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 09:02:56.834 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 09:02:59.202 [redisson-netty-6-29] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 09:03:01.548 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 09:03:02.708 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.709 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.709 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.710 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.749 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.749 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.749 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.750 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:03:02.984 [main] TRACE WIRE:64 - [43b36393] Sending request GET / with parameters: 
2025-07-03 09:03:03.588 [I/O dispatcher 1] TRACE WIRE:97 - [43b36393] Received raw response: 200 OK
2025-07-03 09:03:03.796 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 09:03:03.796 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 09:03:03.797 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 09:03:03.797 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 09:03:03.797 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 09:03:22.713 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 09:03:23.942 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 09:03:35.073 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 09:03:35.074 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 09:03:35.074 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 09:03:35.074 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 09:03:35.536 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 09:03:35.536 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 09:03:35.786 [main] TRACE WIRE:64 - [20cfcd3] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 09:03:35.885 [I/O dispatcher 1] TRACE WIRE:97 - [20cfcd3] Received raw response: 200 OK
2025-07-03 09:03:36.055 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:03:36.055 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:03:36.085 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:03:36.085 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:03:39.716 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 09:03:40.166 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 09:03:40.207 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 09:03:40.481 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 09:03:41.924 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#305cb78b:0/SimpleConnection@bfeaa61 [delegate=amqp://admin@**************:5672/, localPort= 62552]
2025-07-03 09:03:55.293 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 120.933 seconds (JVM running for 121.785)
2025-07-03 09:03:55.323 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 09:03:55.474 [RMI TCP Connection(53)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 09:03:55.474 [RMI TCP Connection(53)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 09:03:55.527 [RMI TCP Connection(53)-*********] INFO  DispatcherServlet:547 - Completed initialization in 52 ms
2025-07-03 09:03:56.575 [RMI TCP Connection(57)-*********] TRACE WIRE:64 - [14be6635] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 09:03:56.742 [I/O dispatcher 1] TRACE WIRE:97 - [14be6635] Received raw response: 200 OK
2025-07-03 09:04:00.532 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:525 - 抖音开放平台请求参数:{episode_id=7508557231474868773, album_id=7507553384406942246, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/play_info/
2025-07-03 09:04:01.692 [http-nio-8082-exec-2] INFO  DouYinHttpUtil:535 - 抖音开放平台请求结果:{"data":{"size":174248843,"url_expire":"**********","bitrate":10292312,"codec":"h264","definition":"1080p","format":"mp4","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0\u0026auth_key=**********-656760c112f2421f9ff38852899e2af8-0-107665dd4aaf66b47e01665ce8364c88\u0026br=10051\u0026bt=10051\u0026cd=0%7C0%7C0\u0026ch=0\u0026cr=0\u0026cs=0\u0026dr=0\u0026ds=4\u0026eid=v0279cg10004d0rt0aaljht8scvm7fjg\u0026er=\u0026l=202507030904017F10686FDD28EC058094\u0026lr=\u0026mime_type=video_mp4\u0026net=0\u0026pl=0\u0026qs=13\u0026rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D\u0026vl=\u0026vr="},"err_msg":"","err_no":0,"log_id":"20250703090401599EFB8CC8A05F61EDCE"}
2025-07-03 09:04:02.027 [http-nio-8082-exec-2] INFO  DouYinApiController:235 - result:OpenApiRes(errNo=0, err_msg=, log_id=20250703090401599EFB8CC8A05F61EDCE, data={"codec":"h264","size":174248843,"play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0&auth_key=**********-656760c112f2421f9ff38852899e2af8-0-107665dd4aaf66b47e01665ce8364c88&br=10051&bt=10051&cd=0%7C0%7C0&ch=0&cr=0&cs=0&dr=0&ds=4&eid=v0279cg10004d0rt0aaljht8scvm7fjg&er=&l=202507030904017F10686FDD28EC058094&lr=&mime_type=video_mp4&net=0&pl=0&qs=13&rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D&vl=&vr=","format":"mp4","bitrate":10292312,"definition":"1080p","url_expire":"**********"}, success=null)
2025-07-03 09:04:17.553 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:525 - 抖音开放平台请求参数:{episode_id=7508557231474868773, album_id=7507553384406942246, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/play_info/
2025-07-03 09:04:17.802 [http-nio-8082-exec-3] INFO  DouYinHttpUtil:535 - 抖音开放平台请求结果:{"data":{"play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0\u0026auth_key=1751511992-3151516c05a940a9bc1c93446f699545-0-fde80ea43eb36c7135385d37d86461df\u0026br=10051\u0026bt=10051\u0026cd=0%7C0%7C0\u0026ch=0\u0026cr=0\u0026cs=0\u0026dr=0\u0026ds=4\u0026eid=v0279cg10004d0rt0aaljht8scvm7fjg\u0026er=\u0026l=20250703090417319786EBC6247C1F596C\u0026lr=\u0026mime_type=video_mp4\u0026net=0\u0026pl=0\u0026qs=13\u0026rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D\u0026vl=\u0026vr=","size":174248843,"url_expire":"1751511557","bitrate":10292312,"codec":"h264","definition":"1080p","format":"mp4"},"err_msg":"","err_no":0,"log_id":"202507030904171D621259CB941A52A713"}
2025-07-03 09:04:29.356 [http-nio-8082-exec-3] INFO  DouYinApiController:235 - result:OpenApiRes(errNo=0, err_msg=, log_id=202507030904171D621259CB941A52A713, data={"codec":"h264","play_url":"https://douyin.cdmhwh.com/tt13a865158c9932af01/1f9zpsea/%E5%A4%A7%E5%B0%8F%E5%A7%90%E9%A9%BE%E5%88%B0-%E7%BB%9F%E7%BB%9F%E9%97%AA%E5%BC%80-%E7%AC%AC1%E9%9B%86.mp4?a=0&auth_key=1751511992-3151516c05a940a9bc1c93446f699545-0-fde80ea43eb36c7135385d37d86461df&br=10051&bt=10051&cd=0%7C0%7C0&ch=0&cr=0&cs=0&dr=0&ds=4&eid=v0279cg10004d0rt0aaljht8scvm7fjg&er=&l=20250703090417319786EBC6247C1F596C&lr=&mime_type=video_mp4&net=0&pl=0&qs=13&rc=amlwZjtrb2R3MzczNGY6M0ApbTp5dndtZDN1ZzMzajw1eWdnNHNwcWdeLW9hLS1kNi9zc2RjamA1ZWlecS0xLS5gNC06Yw%3D%3D&vl=&vr=","size":174248843,"format":"mp4","bitrate":10292312,"definition":"1080p","url_expire":"1751511557"}, success=null)
2025-07-03 09:05:40.486 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 09:05:41.651 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 09:05:43.599 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:05:43.600 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 09:05:43.702 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 96ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 09:05:44.229 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:05:44.230 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 09:05:44.286 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 56ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 09:05:44.299 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:05:44.301 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 09:05:44.342 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 35ms. Found 0 Redis repository interfaces.
2025-07-03 09:05:44.864 [main] INFO  GenericScope:295 - BeanFactory id=35255a23-9860-3ebc-992e-bb403135b10a
2025-07-03 09:05:44.921 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$4bcf1860] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.028 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.029 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.031 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$8ee99a99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.113 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$218d46ef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.521 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.525 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$95581f9b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.539 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.544 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.628 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.958 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 09:05:45.961 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 09:05:45.969 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:45.980 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:46.056 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:46.096 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$969d9faa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:46.261 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$3ee28c7a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:46.268 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:05:46.797 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 09:05:46.811 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 09:05:46.812 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 09:05:46.812 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 09:05:46.907 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 09:05:46.907 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5229 ms
2025-07-03 09:05:47.792 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 09:05:47.803 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 09:05:53.109 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 09:05:57.921 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 09:06:04.263 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 09:06:10.375 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 09:06:10.377 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 09:06:10.378 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 09:06:10.378 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 09:06:10.379 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 09:06:10.379 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 09:06:10.379 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 09:06:16.968 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 09:06:16.977 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 09:06:17.013 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 09:06:17.013 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 09:06:17.026 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 09:06:17.030 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 09:06:17.034 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 09:06:17.034 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 09:06:17.034 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 09:06:17.034 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@39f9c6f5
2025-07-03 09:06:20.808 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 09:06:20.810 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 09:06:23.629 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 09:06:25.496 [redisson-netty-6-25] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 09:06:26.726 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 09:06:28.079 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.079 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.079 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.079 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.111 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.111 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.111 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.111 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:06:28.333 [main] TRACE WIRE:64 - [1ca5b7c7] Sending request GET / with parameters: 
2025-07-03 09:06:28.901 [I/O dispatcher 1] TRACE WIRE:97 - [1ca5b7c7] Received raw response: 200 OK
2025-07-03 09:06:29.111 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 09:06:29.111 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 09:06:29.111 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 09:06:29.111 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 09:06:29.111 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 09:06:47.025 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 09:06:48.028 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 09:06:58.618 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 09:06:58.619 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 09:06:58.619 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 09:06:58.619 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 09:06:59.106 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 09:06:59.106 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 09:06:59.425 [main] TRACE WIRE:64 - [7a238e5f] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 09:06:59.526 [I/O dispatcher 1] TRACE WIRE:97 - [7a238e5f] Received raw response: 200 OK
2025-07-03 09:06:59.643 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:06:59.644 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:06:59.708 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:06:59.708 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:07:03.119 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 09:07:03.596 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 09:07:03.638 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 09:07:03.927 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 09:07:05.092 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#6d642c00:0/SimpleConnection@679bc4ea [delegate=amqp://admin@**************:5672/, localPort= 63124]
2025-07-03 09:07:19.090 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 99.609 seconds (JVM running for 100.778)
2025-07-03 09:07:19.116 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 09:07:19.799 [RMI TCP Connection(32)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 09:07:19.799 [RMI TCP Connection(32)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 09:07:19.854 [RMI TCP Connection(29)-*********] TRACE WIRE:64 - [15fc702b] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 09:07:19.885 [RMI TCP Connection(32)-*********] INFO  DispatcherServlet:547 - Completed initialization in 86 ms
2025-07-03 09:07:19.951 [I/O dispatcher 1] TRACE WIRE:97 - [15fc702b] Received raw response: 200 OK
2025-07-03 09:08:45.450 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-03T09:08:45.449 | 耗时 239 ms | SQL 语句：
select * from sys_user where username = 'admin' and del_flag = 0;
2025-07-03 09:08:46.576 [http-nio-8082-exec-2] INFO  JwtFilter:39 - JwtFilter.isAccessAllowed.uri：/cms-vrbt-miniapp/mini-app/miniDrama/createAlbumInfo
2025-07-03 09:08:46.605 [http-nio-8082-exec-2] ERROR [dispatcherServlet]:175 - Servlet.service() for servlet [dispatcherServlet] in context with path [/cms-vrbt-miniapp] threw exception [org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录] with root cause
org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:08:47.479 [http-nio-8082-exec-2] INFO  JwtFilter:39 - JwtFilter.isAccessAllowed.uri：/cms-vrbt-miniapp/error
2025-07-03 09:08:47.479 [http-nio-8082-exec-2] ERROR [dispatcherServlet]:175 - Servlet.service() for servlet [dispatcherServlet] threw exception
org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:710)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:384)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:312)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:398)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:257)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:352)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:177)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:08:47.480 [http-nio-8082-exec-2] ERROR [localhost]:175 - Exception Processing ErrorPage[errorCode=0, location=/error]
javax.servlet.ServletException: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录
	at org.apache.shiro.web.servlet.AdviceFilter.cleanup(AdviceFilter.java:196)
	at org.apache.shiro.web.filter.authc.AuthenticatingFilter.cleanup(AuthenticatingFilter.java:155)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:148)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:103)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:710)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:384)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:312)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:398)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:257)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:352)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:177)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
Caused by: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:40)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.preHandle(JwtFilter.java:74)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	... 45 common frames omitted
Caused by: org.apache.shiro.authc.AuthenticationException: Token失效，请重新登录!
	at org.jeecg.modules.shiro.authc.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:125)
	at org.jeecg.modules.shiro.authc.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:97)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.executeLogin(JwtFilter.java:54)
	at org.jeecg.modules.shiro.authc.aop.JwtFilter.isAccessAllowed(JwtFilter.java:36)
	... 50 common frames omitted
2025-07-03 09:11:17.856 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-07-03T09:11:17.855 | 耗时 235 ms | SQL 语句：
SELECT id,album_id,title,seq_num,cover_list,year,album_status,review_status,authorize_status,online_status,version,recommendation,desp,tag_list,qualification,status,name,duration,seq_count,production_organisation,director,producer,actor,summary,cost_distribution_uri,assurance_uri,playlet_production_cost,screen_writer,license_num,registration_num,ordinary_record_num,key_record_num,record_type,broadcast_record_number,create_by,update_by,create_time,update_time FROM openApi_album_info WHERE id='1933342854223409153' ;
2025-07-03 09:11:19.567 [http-nio-8082-exec-8] INFO  DouYinHttpUtil:525 - 抖音开放平台请求参数:{album_info={desp=null, qualification=null, year=2023, tag_list=[], recommendation=null, title=测试, seq_num=3, cover_list=[测试], record_audit_info={record_material={duration=null, actor=[], summary=null, cost_distribution_uri=null, production_organisation=null, seqs_count=10, director=[], playlet_production_cost=null, name=测试, producer=[], screen_writer=[]}}, album_status=2}, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/create/
2025-07-03 09:11:20.297 [http-nio-8082-exec-8] INFO  DouYinHttpUtil:535 - 抖音开放平台请求结果:{"err_no":28001007,"err_msg":"qualification must have a value","log_id":"202507030911200A781E18E4C87E8815F7"}
2025-07-03 09:11:46.045 [http-nio-8082-exec-7] INFO  p6spy:60 - 2025-07-03T09:11:46.045 | 耗时 226 ms | SQL 语句：
SELECT id,album_id,title,seq_num,cover_list,year,album_status,review_status,authorize_status,online_status,version,recommendation,desp,tag_list,qualification,status,name,duration,seq_count,production_organisation,director,producer,actor,summary,cost_distribution_uri,assurance_uri,playlet_production_cost,screen_writer,license_num,registration_num,ordinary_record_num,key_record_num,record_type,broadcast_record_number,create_by,update_by,create_time,update_time FROM openApi_album_info WHERE id='1933342854223409153' ;
2025-07-03 09:11:47.126 [http-nio-8082-exec-7] INFO  DouYinHttpUtil:525 - 抖音开放平台请求参数:{album_info={desp=null, qualification=null, year=2023, tag_list=[], recommendation=null, title=测试, seq_num=3, cover_list=[测试], record_audit_info={record_material={duration=null, actor=[], summary=null, cost_distribution_uri=null, production_organisation=null, seqs_count=10, director=[], playlet_production_cost=null, name=测试, producer=[], screen_writer=[]}}, album_status=2}, ma_app_id=tt13a865158c9932af01},url:https://open.douyin.com/api/playlet/v2/video/create/
2025-07-03 09:11:47.415 [http-nio-8082-exec-7] INFO  DouYinHttpUtil:535 - 抖音开放平台请求结果:{"err_no":28001007,"err_msg":"qualification must have a value","log_id":"2025070309114725AAC23FEF8398881177"}
2025-07-03 09:12:31.187 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xea6f2e1c, L:/*********:62977 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:57.370 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe2ca5573, L:/*********:62961 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:57.376 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc63f2c6b, L:/*********:62957 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:57.378 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8311a565, L:/*********:62965 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:57.380 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x19c03883, L:/*********:62966 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:57.381 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x900ea7cf, L:/*********:62958 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.471 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x216a12e7, L:/*********:62959 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.474 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2c4b497a, L:0.0.0.0/0.0.0.0:62964 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.476 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7a68fea4, L:/*********:62963 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.479 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3ecfa723, L:/*********:62960 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.483 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x39f8038c, L:/*********:62967 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.485 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x705128a7, L:/*********:62962 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.494 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd05ab0d8, L:/*********:62975 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.496 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7726d5e2, L:/*********:62974 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.497 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2c44c0b5, L:/*********:62969 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.498 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xdab19725, L:/*********:62971 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.500 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb8dc09f2, L:/*********:62973 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:15:58.505 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x45f6122c, L:/*********:62970 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:37:31.163 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 09:37:32.244 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 09:37:35.692 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:37:35.694 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 09:37:35.826 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 125ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 09:37:37.069 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:37:37.070 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 09:37:37.136 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 65ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 09:37:37.151 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 09:37:37.153 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 09:37:37.200 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 40ms. Found 0 Redis repository interfaces.
2025-07-03 09:37:37.783 [main] INFO  GenericScope:295 - BeanFactory id=35255a23-9860-3ebc-992e-bb403135b10a
2025-07-03 09:37:37.845 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$e7df9575] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:37.966 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:37.968 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:37.970 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$2afa17ae] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.053 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$bd9dc404] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.446 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.449 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$31689cb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.461 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.465 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.538 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.832 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 09:37:38.835 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 09:37:38.845 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.852 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.879 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.892 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$32ae1cbf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.986 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$daf3098f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:38.991 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 09:37:39.573 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 09:37:39.591 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 09:37:39.592 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 09:37:39.592 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 09:37:39.718 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 09:37:39.718 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 7438 ms
2025-07-03 09:37:40.784 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 09:37:40.798 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 09:37:47.980 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 09:37:54.424 [main] ERROR DruidDataSource:936 - init datasource error, url: jdbc:mysql://**************:3306/febs_xxl_test?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false
com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,016 milliseconds ago.  The last packet sent successfully to the server was 1,016 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425)
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:990)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3562)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3462)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3905)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2530)
	at com.mysql.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:3935)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1384)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1672)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:932)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:170)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.jdbc.util.ReadAheadInputStream.fill(ReadAheadInputStream.java:101)
	at com.mysql.jdbc.util.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:144)
	at com.mysql.jdbc.util.ReadAheadInputStream.read(ReadAheadInputStream.java:174)
	at com.mysql.jdbc.MysqlIO.readFully(MysqlIO.java:3011)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3472)
	... 64 common frames omitted
2025-07-03 09:37:54.425 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 09:38:03.228 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 09:38:10.102 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 09:38:10.104 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 09:38:10.104 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 09:38:10.104 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 09:38:10.106 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 09:38:10.106 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 09:38:10.106 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 09:38:17.896 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 09:38:17.902 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 09:38:17.939 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 09:38:17.939 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 09:38:17.953 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 09:38:17.957 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 09:38:17.959 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 09:38:17.960 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 09:38:17.960 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 09:38:17.960 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@778067f5
2025-07-03 09:38:21.991 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 09:38:21.993 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 09:38:25.404 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 09:38:27.571 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 09:38:29.650 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 09:38:30.754 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:30.755 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:30.755 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:30.755 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:30.789 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:30.790 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:30.790 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:30.790 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 09:38:31.010 [main] TRACE WIRE:64 - [2d03be3] Sending request GET / with parameters: 
2025-07-03 09:38:31.724 [I/O dispatcher 1] TRACE WIRE:97 - [2d03be3] Received raw response: 200 OK
2025-07-03 09:38:31.927 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 09:38:31.927 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 09:38:31.928 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 09:38:31.928 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 09:38:31.928 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 09:38:50.005 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 09:38:51.045 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 09:39:01.945 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 09:39:01.946 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 09:39:01.946 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 09:39:01.946 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 09:39:02.419 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 09:39:02.420 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 09:39:02.613 [main] TRACE WIRE:64 - [24131b6f] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 09:39:03.510 [I/O dispatcher 1] TRACE WIRE:97 - [24131b6f] Received raw response: 200 OK
2025-07-03 09:39:03.722 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:39:03.722 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:39:03.752 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 09:39:03.752 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 09:39:07.666 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 09:39:08.480 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 09:39:08.530 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 09:39:08.805 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 09:39:10.966 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#6967bbf3:0/SimpleConnection@6cdbecd5 [delegate=amqp://admin@**************:5672/, localPort= 49423]
2025-07-03 09:39:58.960 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2b56a135, L:/*********:49266 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:58.960 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0b8069bf, L:/*********:49265 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:58.961 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0c8560a8, L:/*********:49267 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:58.961 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x54d4e178, L:/*********:49268 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:58.962 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x52a8774f, L:/*********:49263 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:58.962 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x82bee8f4, L:/*********:49264 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:59.037 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe3c75133, L:/*********:49262 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:59.548 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3eb2414e, L:/*********:49270 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:39:59.637 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x29661dc6, L:/*********:49271 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:58.045 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6005be63, L:/*********:49250 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:58.045 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x15035925, L:/*********:49248 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:58.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x21e0b7d2, L:/*********:49255 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:58.046 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6dca7821, L:/*********:49247 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:58.047 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa83438c3, L:/*********:49252 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:58.047 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x506a2fce, L:/*********:49256 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:58.952 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1d2186ef, L:/*********:49261 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:59.139 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0f980c46, L:/*********:49260 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:40:59.139 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9f626326, L:/*********:49259 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:00.038 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9642cfa0, L:/*********:49272 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:00.138 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xdba4bc4b, L:/*********:49273 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:28.038 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc2b57eae, L:/*********:49254 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:28.138 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8e8089e4, L:/*********:49251 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:28.138 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7636bad4, L:/*********:49257 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:28.138 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8f40e1ea, L:/*********:49249 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:28.139 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe78283c9, L:/*********:49253 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 09:41:38.901 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 248.55 seconds (JVM running for 249.515)
2025-07-03 09:41:38.925 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 09:41:39.197 [RMI TCP Connection(71)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 09:41:39.198 [RMI TCP Connection(71)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 09:41:39.274 [RMI TCP Connection(71)-*********] INFO  DispatcherServlet:547 - Completed initialization in 76 ms
2025-07-03 09:41:39.721 [RMI TCP Connection(74)-*********] TRACE WIRE:64 - [25f2831e] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 09:41:40.090 [I/O dispatcher 1] TRACE WIRE:97 - [25f2831e] Received raw response: 200 OK
2025-07-03 09:42:09.778 [http-nio-8082-exec-5] INFO  p6spy:60 - 2025-07-03T09:42:09.778 | 耗时 372 ms | SQL 语句：
SELECT COUNT(1) FROM duan_ju_package;
2025-07-03 09:42:10.070 [http-nio-8082-exec-5] INFO  p6spy:60 - 2025-07-03T09:42:10.07 | 耗时 252 ms | SQL 语句：
SELECT id,name,type,pay_type,right_desc,complimentary_num,bottom_heading,recharge_amount,original_price,actual_price,order_no,status,is_deleted,description,label_content,icon,create_by,create_time,update_by,update_time FROM duan_ju_package LIMIT 0,10;
2025-07-03 09:42:11.041 [http-nio-8082-exec-5] INFO  p6spy:60 - 2025-07-03T09:42:11.041 | 耗时 410 ms | SQL 语句：
INSERT INTO sys_log ( id, create_time, cost_time, ip, request_param, method, log_content, log_type, operate_type ) VALUES ( '1940586835354402818', '2025-07-03T09:42:10.410+0800', 3320, '0:0:0:0:0:0:0:1', ' duanJuPackage: DuanJuPackage(id=null, name=null, type=null, payType=null, rightDesc=null, complimentaryNum=null, bottomHeading=null, rechargeAmount=null, originalPrice=null, actualPrice=null, orderNo=null, status=null, isDeleted=null, description=null, labelContent=null, icon=null, createBy=null, createTime=null, updateBy=null, updateTime=null) pageNo: 1 pageSize: 10 req: org.apache.shiro.web.servlet.ShiroHttpServletRequest@1a2a87eb', 'com.eleven.cms.douyinduanju.controller.DuanJuPackageController.queryPageList()', 'duan_ju_package-分页列表查询', 2, 1 );
2025-07-03 10:55:40.660 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 10:55:41.744 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 10:55:44.736 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 10:55:44.741 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 10:55:44.842 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 96ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 10:55:45.354 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 10:55:45.355 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 10:55:45.411 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 56ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 10:55:45.424 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 10:55:45.426 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 10:55:45.467 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.
2025-07-03 10:55:45.950 [main] INFO  GenericScope:295 - BeanFactory id=0fbbb8cf-edb7-3054-b5b2-245295025eeb
2025-07-03 10:55:46.008 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$7fe230d3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.120 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.121 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.124 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$c2fcb30c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.198 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$55a05f62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.552 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.555 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c96b380e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.566 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.569 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.634 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.936 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 10:55:46.940 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 10:55:46.952 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:46.964 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:47.013 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:47.028 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$cab0b81d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:47.174 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$72f5a4ed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:47.190 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 10:55:48.050 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 10:55:48.076 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 10:55:48.077 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 10:55:48.078 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 10:55:48.228 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 10:55:48.228 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 6452 ms
2025-07-03 10:55:49.689 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 10:55:49.712 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 10:55:53.646 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 10:55:58.725 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 10:56:07.576 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 10:56:16.517 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 10:56:16.519 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 10:56:16.521 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 10:56:16.521 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 10:56:16.521 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 10:56:16.521 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 10:56:16.521 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 10:56:27.612 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 10:56:27.617 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 10:56:27.662 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 10:56:27.662 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 10:56:27.682 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 10:56:27.686 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 10:56:27.689 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 10:56:27.689 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 10:56:27.689 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 10:56:27.689 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a171ac9
2025-07-03 10:56:33.256 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 10:56:33.258 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 10:56:37.395 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 10:56:40.684 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 10:56:42.450 [redisson-netty-6-17] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 10:56:43.885 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:43.886 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:43.886 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:43.886 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:43.918 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:43.918 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:43.918 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:43.918 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 10:56:44.137 [main] TRACE WIRE:64 - [31abea6a] Sending request GET / with parameters: 
2025-07-03 10:56:44.825 [I/O dispatcher 1] TRACE WIRE:97 - [31abea6a] Received raw response: 200 OK
2025-07-03 10:56:45.051 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 10:56:45.052 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 10:56:45.052 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 10:56:45.052 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 10:56:45.052 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 10:57:08.671 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 10:57:10.505 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 10:57:28.565 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 10:57:28.565 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 10:57:28.565 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 10:57:28.567 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 10:57:29.380 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 10:57:29.386 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 10:57:30.059 [main] TRACE WIRE:64 - [6d78841e] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 10:57:30.267 [I/O dispatcher 1] TRACE WIRE:97 - [6d78841e] Received raw response: 200 OK
2025-07-03 10:57:30.475 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 10:57:30.475 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 10:57:30.525 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 10:57:30.525 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 10:57:36.146 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 10:57:36.670 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 10:57:36.736 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 10:57:37.284 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 10:57:38.231 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#6e859a10:0/SimpleConnection@57d6ac42 [delegate=amqp://admin@**************:5672/, localPort= 56769]
2025-07-03 10:57:54.000 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 134.076 seconds (JVM running for 134.988)
2025-07-03 10:57:54.029 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 10:57:54.844 [RMI TCP Connection(86)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 10:57:54.845 [RMI TCP Connection(86)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 10:57:54.890 [RMI TCP Connection(86)-*********] INFO  DispatcherServlet:547 - Completed initialization in 45 ms
2025-07-03 10:57:55.298 [RMI TCP Connection(85)-*********] TRACE WIRE:64 - [96464d7] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 10:57:55.471 [I/O dispatcher 1] TRACE WIRE:97 - [96464d7] Received raw response: 200 OK
2025-07-03 11:13:56.983 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-03T11:13:56.983 | 耗时 697 ms | SQL 语句：
SELECT COUNT(1) FROM ai_union_ai_face_template;
2025-07-03 11:13:57.254 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-03T11:13:57.254 | 耗时 199 ms | SQL 语句：
SELECT id,template_name,template_remark,template_type,activity_id,material_id,template_id,pic_slot_count,order_by,pic_url,material_pic_url,video_url,status,param,clips_param,remark,topic_type,create_by,create_time,update_by,update_time FROM ai_union_ai_face_template LIMIT 0,10;
2025-07-03 11:13:58.221 [http-nio-8082-exec-2] INFO  p6spy:60 - 2025-07-03T11:13:58.221 | 耗时 366 ms | SQL 语句：
INSERT INTO sys_log ( id, create_by, create_time, cost_time, ip, request_param, method, username, userid, log_content, log_type, operate_type ) VALUES ( '1940609934317678594', 'admin', '2025-07-03T11:13:57.606+0800', 6308, '0:0:0:0:0:0:0:1', ' aiUnionAiFaceTemplate: AiUnionAiFaceTemplate(id=null, templateName=null, templateRemark=null, templateType=null, activityId=null, materialId=null, templateId=null, picSlotCount=null, orderBy=null, picUrl=null, materialPicUrl=null, videoUrl=null, status=null, param=null, clipsParam=null, remark=null, topicType=null, createBy=null, createTime=null, updateBy=null, updateTime=null) pageNo: 1 pageSize: 10 req: org.apache.shiro.web.servlet.ShiroHttpServletRequest@103bffe4', 'com.eleven.cms.aiunion.controller.template.AiUnionAiFaceTemplateController.queryPageList()', '管理员', 'admin', 'ai_union_ai_face_template-分页列表查询', 2, 1 );
2025-07-03 11:14:18.110 [http-nio-8082-exec-4] ERROR JeecgBootExceptionHandler:93 - 不支持GET请求方法
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:213)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:422)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:367)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:110)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:59)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:396)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1234)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1016)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:14:23.457 [http-nio-8082-exec-3] ERROR JeecgBootExceptionHandler:93 - 不支持POST请求方法
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:213)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:422)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:367)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:110)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:59)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:396)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1234)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1016)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:14:27.531 [http-nio-8082-exec-5] INFO  p6spy:60 - 2025-07-03T11:14:27.531 | 耗时 350 ms | SQL 语句：
UPDATE ai_union_ai_face_template SET template_name='花木兰', template_remark='昔有奇女子，替父赴戎行。 战甲遮娇面，英名万古扬。', template_type=0, activity_id='at_1860880104954122240', material_id='mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176', template_id='e5db4ee787dc427fb3defd08d3024629', order_by=99, pic_url='https://appmedia.kunpengtn.com/common/1742281034070.png', video_url='https://appmedia.kunpengtn.com/common/1742281057909.mp4', status=0, clips_param='{"pic1":"960:540","pic2":"960:540","pic3":"960:540"}', topic_type=1, update_by='admin', update_time='2025-07-03T11:14:26.991+0800' WHERE id='11';
2025-07-03 11:14:28.577 [http-nio-8082-exec-5] INFO  p6spy:60 - 2025-07-03T11:14:28.577 | 耗时 318 ms | SQL 语句：
INSERT INTO sys_log ( id, create_by, create_time, cost_time, ip, request_param, method, username, userid, log_content, log_type, operate_type ) VALUES ( '1940610062105538562', 'admin', '2025-07-03T11:14:28.258+0800', 1187, '0:0:0:0:0:0:0:1', '[{"activityId":"at_1860880104954122240","clipsParam":"{\"pic1\":\"960:540\",\"pic2\":\"960:540\",\"pic3\":\"960:540\"}","id":"11","materialId":"mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176","orderBy":99,"picUrl":"https://appmedia.kunpengtn.com/common/1742281034070.png","status":0,"templateId":"e5db4ee787dc427fb3defd08d3024629","templateName":"花木兰","templateRemark":"昔有奇女子，替父赴戎行。\n战甲遮娇面，英名万古扬。","templateType":0,"topicType":1,"updateBy":"admin","updateTime":1751512466991,"videoUrl":"https://appmedia.kunpengtn.com/common/1742281057909.mp4"}]', 'com.eleven.cms.aiunion.controller.template.AiUnionAiFaceTemplateController.edit()', '管理员', 'admin', 'ai_union_ai_face_template-编辑', 2, 3 );
2025-07-03 11:18:14.865 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaf65e3b3, L:/*********:56528 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.951 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc30b3936, L:/*********:56521 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.953 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xbeef40dd, L:/*********:56526 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.954 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x45628771, L:/*********:56522 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.956 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x029cec31, L:/*********:56525 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.957 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xda2438d1, L:/*********:56523 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.959 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0e8ed746, L:/*********:56524 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.960 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x355ed047, L:/*********:56529 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.962 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc23d4612, L:/*********:56527 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.963 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd1686990, L:/*********:56520 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:14.964 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x480ecff4, L:/*********:56530 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.638 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6691a1ac, L:/*********:56538 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.638 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x768ca92b, L:/*********:56537 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.639 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe75d0f67, L:/*********:56540 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.639 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2046ba80, L:/*********:56535 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.639 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x74c78460, L:/*********:56536 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.639 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x14b0b8af, L:/*********:56542 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.640 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf79fed81, L:0.0.0.0/0.0.0.0:56539 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.640 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8a52f721, L:/*********:56541 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.640 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x97b2e333, L:/*********:56533 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:15.640 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa125162b, L:/*********:56534 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:16.639 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf08b171c, L:/*********:56544 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:16.742 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x18e5073a, L:/*********:56546 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:16.743 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa8c79d48, L:/*********:56547 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:16.745 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4f4cd5f1, L:/*********:56545 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:18:28.595 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-07-03T11:18:28.595 | 耗时 1067 ms | SQL 语句：
UPDATE ai_union_ai_face_template SET template_name='花木兰', template_remark='昔有奇女子，替父赴戎行。 战甲遮娇面，英名万古扬。', template_type=0, activity_id='at_1860880104954122240', material_id='mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176', template_id='e5db4ee787dc427fb3defd08d3024629', order_by=99, pic_url='https://appmedia.kunpengtn.com/common/1742281034070.png', video_url='https://appmedia.kunpengtn.com/common/1742281057909.mp4', status=0, clips_param='{"pic1":"960:540","pic2":"960:540","pic3":"960:540"}', topic_type=1, update_by='admin', update_time='2025-07-03T11:18:17.309+0800' WHERE id='11';
2025-07-03 11:18:35.889 [http-nio-8082-exec-8] INFO  p6spy:60 - 2025-07-03T11:18:35.888 | 耗时 863 ms | SQL 语句：
INSERT INTO sys_log ( id, create_by, create_time, cost_time, ip, request_param, method, username, userid, log_content, log_type, operate_type ) VALUES ( '1940611097062633473', 'admin', '2025-07-03T11:18:35.019+0800', 17710, '0:0:0:0:0:0:0:1', '[{"activityId":"at_1860880104954122240","clipsParam":"{\"pic1\":\"960:540\",\"pic2\":\"960:540\",\"pic3\":\"960:540\"}","id":"11","materialId":"mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176","orderBy":99,"picUrl":"https://appmedia.kunpengtn.com/common/1742281034070.png","status":0,"templateId":"e5db4ee787dc427fb3defd08d3024629","templateName":"花木兰","templateRemark":"昔有奇女子，替父赴戎行。\n战甲遮娇面，英名万古扬。","templateType":0,"topicType":1,"updateBy":"admin","updateTime":1751512697309,"videoUrl":"https://appmedia.kunpengtn.com/common/1742281057909.mp4"}]', 'com.eleven.cms.aiunion.controller.template.AiUnionAiFaceTemplateController.edit()', '管理员', 'admin', 'ai_union_ai_face_template-编辑', 2, 3 );
2025-07-03 11:19:19.378 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-07-03T11:19:19.378 | 耗时 330 ms | SQL 语句：
UPDATE ai_union_ai_face_template SET template_name='花木兰', template_remark='昔有奇女子，替父赴戎行。 战甲遮娇面，英名万古扬。', template_type=0, activity_id='at_1860880104954122240', material_id='mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176', template_id='e5db4ee787dc427fb3defd08d3024629', order_by=99, pic_url='https://appmedia.kunpengtn.com/common/1742281034070.png', video_url='https://appmedia.kunpengtn.com/common/1742281057909.mp4', status=0, clips_param='{"pic1":"960:540","pic2":"960:540","pic3":"960:540"}', topic_type=1, update_by='admin', update_time='2025-07-03T11:19:19.039+0800' WHERE id='11';
2025-07-03 11:19:21.102 [http-nio-8082-exec-9] INFO  p6spy:60 - 2025-07-03T11:19:21.102 | 耗时 555 ms | SQL 语句：
INSERT INTO sys_log ( id, create_by, create_time, cost_time, ip, request_param, method, username, userid, log_content, log_type, operate_type ) VALUES ( '1940611288058654721', 'admin', '2025-07-03T11:19:20.545+0800', 1507, '0:0:0:0:0:0:0:1', '[{"activityId":"at_1860880104954122240","clipsParam":"{\"pic1\":\"960:540\",\"pic2\":\"960:540\",\"pic3\":\"960:540\"}","id":"11","materialId":"mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176","orderBy":99,"picUrl":"https://appmedia.kunpengtn.com/common/1742281034070.png","status":0,"templateId":"e5db4ee787dc427fb3defd08d3024629","templateName":"花木兰","templateRemark":"昔有奇女子，替父赴戎行。\n战甲遮娇面，英名万古扬。","templateType":0,"topicType":1,"updateBy":"admin","updateTime":1751512759039,"videoUrl":"https://appmedia.kunpengtn.com/common/1742281057909.mp4"}]', 'com.eleven.cms.aiunion.controller.template.AiUnionAiFaceTemplateController.edit()', '管理员', 'admin', 'ai_union_ai_face_template-编辑', 2, 3 );
2025-07-03 11:43:16.522 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 11:43:17.622 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 11:43:19.515 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 11:43:19.516 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 11:43:19.616 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 95ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 11:43:20.119 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 11:43:20.120 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 11:43:20.176 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 56ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 11:43:20.189 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 11:43:20.191 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 11:43:20.232 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 35ms. Found 0 Redis repository interfaces.
2025-07-03 11:43:20.715 [main] INFO  GenericScope:295 - BeanFactory id=0fbbb8cf-edb7-3054-b5b2-245295025eeb
2025-07-03 11:43:20.774 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$6f1edb0e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:20.879 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:20.880 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:20.882 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$b2395d47] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:20.954 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$44dd099d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.304 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.307 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$b8a7e249] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.319 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.324 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.394 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.669 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 11:43:21.672 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 11:43:21.679 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.687 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.711 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.723 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$b9ed6258] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.809 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$62324f28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:21.813 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:43:22.304 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 11:43:22.319 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 11:43:22.319 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 11:43:22.319 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 11:43:22.403 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 11:43:22.403 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 4755 ms
2025-07-03 11:43:23.264 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 11:43:23.277 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 11:43:28.520 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 11:43:32.289 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 11:43:37.404 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 11:43:42.599 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 11:43:42.599 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 11:43:42.600 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 11:43:42.600 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 11:43:42.600 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 11:43:42.600 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 11:43:42.600 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 11:43:50.210 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 11:43:50.217 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 11:43:50.254 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 11:43:50.255 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 11:43:50.272 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 11:43:50.277 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 11:43:50.279 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 11:43:50.280 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 11:43:50.280 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 11:43:50.280 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@54cf024c
2025-07-03 11:43:54.574 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 11:43:54.576 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 11:43:57.475 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 11:43:59.856 [redisson-netty-6-25] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 11:44:01.433 [redisson-netty-6-20] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 11:44:02.751 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:02.752 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:02.752 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:02.753 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:02.787 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:02.788 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:02.788 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:02.788 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:44:03.008 [main] TRACE WIRE:64 - [38cac3f9] Sending request GET / with parameters: 
2025-07-03 11:44:03.660 [I/O dispatcher 1] TRACE WIRE:97 - [38cac3f9] Received raw response: 200 OK
2025-07-03 11:44:03.876 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 11:44:03.876 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 11:44:03.876 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 11:44:03.877 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 11:44:03.877 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 11:44:34.136 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 11:44:35.244 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 11:44:47.287 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 11:44:47.289 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 11:44:47.288 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 11:44:47.289 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 11:44:47.800 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 11:44:47.800 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 11:44:48.012 [main] TRACE WIRE:64 - [568a79d1] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 11:44:48.184 [I/O dispatcher 1] TRACE WIRE:97 - [568a79d1] Received raw response: 200 OK
2025-07-03 11:44:48.413 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 11:44:48.414 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 11:44:48.446 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 11:44:48.446 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 11:44:52.490 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 11:44:53.930 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 11:44:54.061 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 11:44:54.688 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 11:44:55.873 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#6537b264:0/SimpleConnection@5ccffc02 [delegate=amqp://admin@**************:5672/, localPort= 60316]
2025-07-03 11:45:12.098 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 116.319 seconds (JVM running for 117.305)
2025-07-03 11:45:12.123 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 11:45:12.602 [RMI TCP Connection(45)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 11:45:12.602 [RMI TCP Connection(45)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 11:45:12.647 [RMI TCP Connection(45)-*********] INFO  DispatcherServlet:547 - Completed initialization in 45 ms
2025-07-03 11:45:13.592 [RMI TCP Connection(49)-*********] TRACE WIRE:64 - [6fdd70d1] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 11:45:13.995 [I/O dispatcher 1] TRACE WIRE:97 - [6fdd70d1] Received raw response: 200 OK
2025-07-03 11:45:30.962 [http-nio-8082-exec-1] INFO  DouYinHttpUtil:561 - 抖音开放平台请求参数:{"client_key":"tt13a865158c9932af01","grant_type":"client_credential","client_secret":"12ac3c42f6b7711443911cc3503cab01deb908c9"},url:https://open.douyin.com/oauth/client_token/
2025-07-03 11:45:31.731 [http-nio-8082-exec-1] INFO  DouYinHttpUtil:571 - 抖音开放平台请求结果:{"data":{"access_token":"clt.4ec278eaa0351f31587c00440a3d52aaxpOFAibIOcxpiOnU6WCX2GJeXFl1_lf","captcha":"","desc_url":"","description":"","error_code":0,"expires_in":7200,"log_id":"20250703114531A4B084FFC03EE3042067"},"message":"success"}
2025-07-03 11:45:32.014 [http-nio-8082-exec-1] INFO  DouYinHttpUtil:561 - 抖音开放平台请求参数:{open_id=111, ma_app_id=tt13a865158c9932af01, coupon_status={ToBeUsed=10}},url:https://open.douyin.com/api/promotion/v1/coupon/get_coupon_receive_info/
2025-07-03 11:45:32.239 [http-nio-8082-exec-1] INFO  DouYinHttpUtil:571 - 抖音开放平台请求结果:{"err_no":28001007,"err_msg":"app_id must have a value","log_id":"20250703114532201F3E4EABA96003F816"}
2025-07-03 11:46:04.811 [http-nio-8082-exec-10] INFO  DouYinHttpUtil:561 - 抖音开放平台请求参数:{open_id=111, ma_app_id=tt13a865158c9932af01, coupon_status={ToBeUsed=10}},url:https://open.douyin.com/api/promotion/v1/coupon/get_coupon_receive_info/
2025-07-03 11:46:05.291 [http-nio-8082-exec-10] INFO  DouYinHttpUtil:571 - 抖音开放平台请求结果:{"err_no":28001007,"err_msg":"app_id must have a value","log_id":"2025070311460556CEDDB0870DA401D093"}
2025-07-03 11:47:07.059 [pool-4-thread-2] INFO  AliMnsService:576 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-4 [ACTIVE]
	... 11 common frames omitted
2025-07-03 11:47:07.059 [pool-4-thread-1] INFO  AliMnsService:496 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-1 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-1 [ACTIVE]
	... 11 common frames omitted
2025-07-03 11:47:07.059 [pool-4-thread-3] INFO  AliMnsService:676 - 阿里云MNS消息服务-消息处理异常!
com.aliyun.mns.common.ClientException: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-2 [ACTIVE]
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.timeout(HttpAsyncRequestExecutor.java:387)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:92)
	at org.apache.http.impl.nio.client.InternalIODispatch.onTimeout(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.timeout(AbstractIODispatch.java:175)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.sessionTimedOut(BaseIOReactor.java:261)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.timeoutCheck(AbstractIOReactor.java:502)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.validate(BaseIOReactor.java:211)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:280)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: 33,000 milliseconds timeout on connection http-outgoing-2 [ACTIVE]
	... 11 common frames omitted
2025-07-03 11:47:41.540 [http-nio-8082-exec-8] INFO  DouYinHttpUtil:561 - 抖音开放平台请求参数:{open_id=111, ma_app_id=tt13a865158c9932af01, coupon_status={ToBeUsed=10}},url:https://open.douyin.com/api/promotion/v1/coupon/get_coupon_receive_info/
2025-07-03 11:47:41.816 [http-nio-8082-exec-8] INFO  DouYinHttpUtil:571 - 抖音开放平台请求结果:{"err_no":28001007,"err_msg":"app_id must have a value","log_id":"20250703114741F92E541CB8ADF00403F2"}
2025-07-03 11:53:54.482 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 11:53:55.582 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 11:53:57.616 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 11:53:57.616 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 11:53:57.724 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 104ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 11:53:58.365 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 11:53:58.367 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 11:53:58.460 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 92ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 11:53:58.478 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 11:53:58.481 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 11:53:58.535 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 44ms. Found 0 Redis repository interfaces.
2025-07-03 11:53:59.075 [main] INFO  GenericScope:295 - BeanFactory id=0fbbb8cf-edb7-3054-b5b2-245295025eeb
2025-07-03 11:53:59.138 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$4b2e430d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.246 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.246 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.256 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$8e48c546] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.340 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$20ec719c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.713 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.717 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$94b74a48] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.729 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.733 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:53:59.796 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:54:00.092 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 11:54:00.094 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 11:54:00.102 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:54:00.111 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:54:00.140 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:54:00.159 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$95fcca57] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:54:00.272 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$3e41b727] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:54:00.276 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 11:54:00.859 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 11:54:00.890 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 11:54:00.892 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 11:54:00.893 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 11:54:01.047 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 11:54:01.047 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5432 ms
2025-07-03 11:54:02.521 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 11:54:02.546 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 11:54:07.570 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 11:54:10.742 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 11:54:16.914 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 11:54:21.846 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 11:54:21.846 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 11:54:21.846 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 11:54:21.846 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 11:54:21.848 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 11:54:21.848 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 11:54:21.850 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 11:54:30.337 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 11:54:30.337 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 11:54:30.376 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 11:54:30.376 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 11:54:30.387 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 11:54:30.397 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 11:54:30.399 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 11:54:30.399 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 11:54:30.399 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 11:54:30.399 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3232dbf8
2025-07-03 11:54:34.902 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 11:54:34.904 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 11:54:38.151 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 11:54:40.134 [redisson-netty-6-13] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 11:54:41.351 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 11:54:42.640 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.640 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.640 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.640 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.681 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.681 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.681 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.681 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 11:54:42.926 [main] TRACE WIRE:64 - [3a74b2f1] Sending request GET / with parameters: 
2025-07-03 11:54:43.570 [I/O dispatcher 1] TRACE WIRE:97 - [3a74b2f1] Received raw response: 200 OK
2025-07-03 11:54:43.767 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 11:54:43.767 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 11:54:43.767 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 11:54:43.767 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 11:54:43.767 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 11:55:08.005 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 11:55:09.175 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 11:55:20.199 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 11:55:20.199 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 11:55:20.199 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 11:55:20.199 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 11:55:20.657 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 11:55:20.667 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 11:55:20.857 [main] TRACE WIRE:64 - [191e18d7] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 11:55:21.017 [I/O dispatcher 1] TRACE WIRE:97 - [191e18d7] Received raw response: 200 OK
2025-07-03 11:55:21.227 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 11:55:21.227 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 11:55:21.247 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 11:55:21.257 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 11:55:24.753 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 11:55:25.251 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 11:55:25.297 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 11:55:25.567 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 11:55:26.597 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#5b83e954:0/SimpleConnection@7cc7632b [delegate=amqp://admin@**************:5672/, localPort= 61230]
2025-07-03 11:55:39.037 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 105.313 seconds (JVM running for 106.317)
2025-07-03 11:55:39.062 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 11:55:39.517 [RMI TCP Connection(45)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 11:55:39.517 [RMI TCP Connection(45)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 11:55:39.565 [RMI TCP Connection(45)-*********] INFO  DispatcherServlet:547 - Completed initialization in 48 ms
2025-07-03 11:55:39.997 [RMI TCP Connection(47)-*********] TRACE WIRE:64 - [16317750] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 11:55:40.091 [I/O dispatcher 1] TRACE WIRE:97 - [16317750] Received raw response: 200 OK
2025-07-03 11:57:24.099 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x40d1de86, L:/*********:61070 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 11:58:39.793 [http-nio-8082-exec-7] INFO  p6spy:60 - 2025-07-03T11:58:39.792 | 耗时 411 ms | SQL 语句：
UPDATE ai_union_ai_face_template SET template_name='花木兰', template_remark='昔有奇女子，替父赴戎行。 战甲遮娇面，英名万古扬。', template_type=0, activity_id='at_1860880104954122240', material_id='mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176', template_id='e5db4ee787dc427fb3defd08d3024629', order_by=99, pic_url='https://appmedia.kunpengtn.com/common/1742281034070.png', video_url='https://appmedia.kunpengtn.com/common/1742281057909.mp4', status=0, clips_param='{"pic1":"960:540","pic2":"960:540","pic3":"960:540"}', topic_type=1, update_by='admin', update_time='2025-07-03T11:58:37.622+0800' WHERE id='11';
2025-07-03 11:58:41.825 [http-nio-8082-exec-7] INFO  p6spy:60 - 2025-07-03T11:58:41.825 | 耗时 317 ms | SQL 语句：
INSERT INTO sys_log ( id, create_by, create_time, cost_time, ip, request_param, method, username, userid, log_content, log_type, operate_type ) VALUES ( '1940621190416130049', 'admin', '2025-07-03T11:58:41.289+0800', 3527, '0:0:0:0:0:0:0:1', '[{"activityId":"at_1860880104954122240","clipsParam":"{\"pic1\":\"960:540\",\"pic2\":\"960:540\",\"pic3\":\"960:540\"}","id":"11","materialId":"mt_1861354538235846656,mt_1861354548876795904,mt_1861354566106034176","orderBy":99,"picUrl":"https://appmedia.kunpengtn.com/common/1742281034070.png","status":0,"templateId":"e5db4ee787dc427fb3defd08d3024629","templateName":"花木兰","templateRemark":"昔有奇女子，替父赴戎行。\n战甲遮娇面，英名万古扬。","templateType":0,"topicType":1,"updateBy":"admin","updateTime":1751515117622,"videoUrl":"https://appmedia.kunpengtn.com/common/1742281057909.mp4"}]', 'com.eleven.cms.aiunion.controller.template.AiUnionAiFaceTemplateController.edit()', '管理员', 'admin', 'ai_union_ai_face_template-编辑', 2, 3 );
2025-07-03 14:02:50.204 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x15faa606, L:0.0.0.0/0.0.0.0:61074 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:02:50.209 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x01d16c20, L:/*********:61072 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:47:59.228 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb01c1b03, L:/*********:61044 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:47:59.233 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf6c16507, L:/*********:61050 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:47:59.233 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x480480ab, L:/*********:61049 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:47:59.234 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb4cdfba3, L:/*********:61061 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:13.518 [lettuce-eventExecutorLoop-1-11] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was /**************:6379
2025-07-03 14:56:18.780 [AMQP Connection **************:5672] WARN  ForgivingExceptionHandler:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-03 14:56:18.809 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.810 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.811 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.812 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.815 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.819 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.821 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.821 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.822 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:18.824 [AMQP Connection **************:5672] ERROR CachingConnectionFactory:1575 - Channel shutdown: connection error
2025-07-03 14:56:19.013 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1972ffee: tags=[[amq.ctag--eKcOttgkYeZsT1NOdY9hg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@2da588fd Shared Rabbit Connection: SimpleConnection@7cc7632b [delegate=amqp://admin@**************:5672/, localPort= 61230], acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.043 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 14:56:19.111 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@28f3815c: tags=[[amq.ctag-z6vEbcMTwvhssKpoVOf92w]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,4), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.112 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@2735e052: tags=[[amq.ctag-kmNoeCHajDP5V3YMWnWhWA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,6), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.158 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@1982e17c: tags=[[amq.ctag-mColz5Nfe-acjG4TfqySOg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,8), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.158 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@4c8e54: tags=[[amq.ctag-l8aYPp9j6DdAUJS7fmObRA]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,2), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.158 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@322eded1: tags=[[amq.ctag-0O7-I6z2QIzoilrgtRQoMg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,3), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.160 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@2759181a: tags=[[amq.ctag-TEMJJ0Ege_aqlRN00gfeSQ]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,5), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.160 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-2] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@6b206a6f: tags=[[amq.ctag-arc_wc9ExAHkgRV_pRaGpg]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,9), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.160 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-1] INFO  SimpleMessageListenerContainer:1438 - Restarting Consumer@33d394e7: tags=[[amq.ctag-THkuxB9t8ACWxmDupt5hKw]], channel=Cached Rabbit Channel: PublisherCallbackChannelImpl: AMQChannel(amqp://admin@**************:5672/,7), conn: Proxy@2da588fd Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-03 14:56:19.945 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xa7138ff2, L:/*********:61450 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:20.067 [redisson-netty-6-16] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x35dec79b, L:/*********:61073 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:20.835 [redisson-netty-6-8] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x8209d861, L:/*********:51269 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:22.847 [redisson-netty-6-12] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x26969a76, L:/*********:61045 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:23.294 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x833e55ad, L:/*********:51272 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:23.563 [lettuce-nioEventLoop-11-2] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-03 14:56:23.606 [lettuce-eventExecutorLoop-1-14] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-03 14:56:23.817 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6de4db9a, L:/*********:49419 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:26.462 [redisson-netty-6-2] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x18329f1e, L:/*********:61052 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:27.239 [redisson-netty-6-28] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x559eafff, L:/*********:61060 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:27.256 [redisson-netty-6-5] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xe8f5ffdb, L:/*********:61043 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:27.376 [redisson-netty-6-27] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x221ef30c, L:/*********:61059 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:27.376 [redisson-netty-6-31] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd64e2c4e, L:/*********:61065 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:27.888 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xc1b2329c, L:/*********:61047 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:27.966 [redisson-netty-6-10] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x6e508188, L:/*********:49418 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:28.211 [redisson-netty-6-7] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x393036e5, L:/*********:51270 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:28.520 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x849bd00b, L:/*********:61051 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:28.521 [redisson-netty-6-4] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0xd11c57df, L:/*********:61048 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:28.522 [redisson-netty-6-6] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x4dff01ef, L:/*********:61053 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:28.522 [redisson-netty-6-7] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x512bb18f, L:/*********:61046 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:29.042 [redisson-netty-6-9] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x0e01013a, L:/*********:51271 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:29.838 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x35dec79b, L:/*********:61073 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.263 [redisson-netty-6-24] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x9b36d60d, L:/*********:61057 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.572 [redisson-netty-6-1] ERROR ErrorsLoggingHandler:47 - Exception occured. Channel: [id: 0x18563310, L:/*********:61064 - R:/**************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.741 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa7138ff2, L:/*********:61450 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.936 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x26969a76, L:/*********:61045 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.942 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x18329f1e, L:/*********:61052 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.954 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc1b2329c, L:/*********:61047 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.954 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4dff01ef, L:/*********:61053 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.964 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd11c57df, L:/*********:61048 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.970 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x512bb18f, L:/*********:61046 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.971 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x849bd00b, L:/*********:61051 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.984 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe8f5ffdb, L:/*********:61043 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.991 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x559eafff, L:/*********:61060 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.993 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd64e2c4e, L:/*********:61065 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.993 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x221ef30c, L:/*********:61059 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.994 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x18563310, L:/*********:61064 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:30.998 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3b1803f4, L:0.0.0.0/0.0.0.0:61058 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.001 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9b36d60d, L:/*********:61057 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.001 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x04adb983, L:/*********:61066 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.004 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x718d7fce, L:0.0.0.0/0.0.0.0:61063 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.004 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x430bad31, L:/*********:61067 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.434 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8209d861, L:/*********:51269 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.541 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6e508188, L:/*********:49418 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.543 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6de4db9a, L:/*********:49419 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.543 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x833e55ad, L:/*********:51272 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.632 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x393036e5, L:/*********:51270 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:31.633 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0e01013a, L:/*********:51271 ! R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 14:56:33.630 [lettuce-nioEventLoop-11-3] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-03 14:56:33.707 [lettuce-eventExecutorLoop-1-15] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-03 14:56:40.077 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 14:56:40.078 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createBareChannel(CachingConnectionFactory.java:702)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getCachedChannelProxy(CachingConnectionFactory.java:676)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.getChannel(CachingConnectionFactory.java:567)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.access$1600(CachingConnectionFactory.java:102)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory$ChannelCachingConnectionProxy.createChannel(CachingConnectionFactory.java:1439)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2095)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 16 common frames omitted
2025-07-03 14:56:43.724 [lettuce-nioEventLoop-11-4] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-03 14:56:43.808 [lettuce-eventExecutorLoop-1-13] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-03 14:56:53.833 [lettuce-nioEventLoop-11-5] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-03 14:56:53.911 [lettuce-eventExecutorLoop-1-1] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-03 14:57:01.120 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] ERROR SimpleMessageListenerContainer:1875 - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:524)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:751)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2089)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2062)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2042)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1888)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1869)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1356)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1202)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1137)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:562)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:535)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:487)
	... 12 common frames omitted
2025-07-03 14:57:01.120 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 14:57:03.928 [lettuce-nioEventLoop-11-6] WARN  ConnectionWatchdog:151 - Cannot reconnect to [**************:6379]: connection timed out: /**************:6379
2025-07-03 14:57:04.005 [lettuce-eventExecutorLoop-1-4] INFO  ConnectionWatchdog:171 - Reconnecting, last destination was **************:6379
2025-07-03 14:57:13.967 [lettuce-nioEventLoop-11-7] INFO  ReconnectionHandler:194 - Reconnected to **************:6379
2025-07-03 14:57:17.017 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-2] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#5b83e954:3/SimpleConnection@3eb51622 [delegate=amqp://admin@**************:5672/, localPort= 52064]
2025-07-03 15:20:18.845 [redisson-timer-8-1] ERROR PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5b674550, L:/*********:52115 - R:/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:207)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 15:26:10.114 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 15:26:11.262 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 15:26:14.731 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 15:26:14.732 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 15:26:14.837 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 100ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 15:26:15.389 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 15:26:15.390 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 15:26:15.448 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 57ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 15:26:15.461 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 15:26:15.462 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 15:26:15.510 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 39ms. Found 0 Redis repository interfaces.
2025-07-03 15:26:16.002 [main] INFO  GenericScope:295 - BeanFactory id=0fbbb8cf-edb7-3054-b5b2-245295025eeb
2025-07-03 15:26:16.060 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$57710c28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.174 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.175 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.177 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$9a8b8e61] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.254 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$2d2f3ab7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.613 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.616 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a0fa1363] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.627 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.630 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.696 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.974 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 15:26:16.978 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 15:26:16.990 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:16.997 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:17.022 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:17.035 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$a23f9372] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:17.117 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$4a848042] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:17.122 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 15:26:17.641 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 15:26:17.662 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 15:26:17.663 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 15:26:17.663 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 15:26:17.806 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 15:26:17.806 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 6514 ms
2025-07-03 15:26:18.755 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 15:26:18.767 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 15:26:23.965 [main] ERROR DruidDataSource:936 - init datasource error, url: jdbc:mysql://**************:3306/jeecg_boot_cms_test?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false
com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,017 milliseconds ago.  The last packet sent successfully to the server was 1,017 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425)
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:990)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3562)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3462)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3905)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2530)
	at com.mysql.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:3935)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1384)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1672)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:932)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDruidDataSource(DynamicDataSourceCreator.java:242)
	at com.baomidou.dynamic.datasource.DynamicDataSourceCreator.createDataSource(DynamicDataSourceCreator.java:139)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:59)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:202)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getIfUnique(DefaultListableBeanFactory.java:1969)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.getDataSourceInitializer(DataSourceInitializerInvoker.java:98)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker.afterPropertiesSet(DataSourceInitializerInvoker.java:61)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1858)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration.init(RefreshAutoConfiguration.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:415)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1791)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:869)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at org.jeecg.JeecgApplication.main(JeecgApplication.java:36)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:170)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.jdbc.util.ReadAheadInputStream.fill(ReadAheadInputStream.java:101)
	at com.mysql.jdbc.util.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:144)
	at com.mysql.jdbc.util.ReadAheadInputStream.read(ReadAheadInputStream.java:174)
	at com.mysql.jdbc.MysqlIO.readFully(MysqlIO.java:3011)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:3472)
	... 64 common frames omitted
2025-07-03 15:26:23.968 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 15:26:31.426 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 15:26:36.540 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 15:26:42.683 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 15:26:42.684 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 15:26:42.686 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 15:26:42.687 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 15:26:42.687 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 15:26:42.687 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 15:26:42.687 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 15:26:49.769 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 15:26:49.775 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 15:26:49.817 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 15:26:49.817 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 15:26:49.833 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 15:26:49.839 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 15:26:49.841 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 15:26:49.841 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 15:26:49.842 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 15:26:49.842 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@24d7df03
2025-07-03 15:26:53.821 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 15:26:53.823 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 15:26:56.674 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 15:26:58.552 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 15:26:59.732 [redisson-netty-6-19] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 15:27:00.950 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:00.951 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:00.952 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:00.952 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:00.988 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:00.989 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:00.989 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:00.989 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 15:27:01.245 [main] TRACE WIRE:64 - [6046e11d] Sending request GET / with parameters: 
2025-07-03 15:27:01.970 [I/O dispatcher 1] TRACE WIRE:97 - [6046e11d] Received raw response: 200 OK
2025-07-03 15:27:02.223 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 15:27:02.224 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 15:27:02.224 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 15:27:02.224 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 15:27:02.224 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 15:27:20.201 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 15:27:21.326 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 15:27:33.243 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 15:27:33.244 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 15:27:33.244 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 15:27:33.244 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 15:27:33.771 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 15:27:33.771 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 15:27:34.032 [main] TRACE WIRE:64 - [5e1f1883] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 15:27:34.450 [I/O dispatcher 1] TRACE WIRE:97 - [5e1f1883] Received raw response: 200 OK
2025-07-03 15:27:34.668 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 15:27:34.669 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 15:27:34.700 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 15:27:34.700 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 15:27:38.589 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 15:27:39.440 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 15:27:39.511 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 15:27:39.792 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 15:27:41.163 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#885620d:0/SimpleConnection@168d1004 [delegate=amqp://admin@**************:5672/, localPort= 53773]
2025-07-03 15:27:54.285 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 104.954 seconds (JVM running for 105.99)
2025-07-03 15:27:54.312 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 15:27:55.018 [RMI TCP Connection(39)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 15:27:55.019 [RMI TCP Connection(39)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 15:27:55.220 [RMI TCP Connection(39)-*********] INFO  DispatcherServlet:547 - Completed initialization in 201 ms
2025-07-03 15:27:55.486 [RMI TCP Connection(41)-*********] TRACE WIRE:64 - [17cb4462] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 15:27:55.727 [I/O dispatcher 1] TRACE WIRE:97 - [17cb4462] Received raw response: 200 OK
2025-07-03 15:34:22.152 [http-nio-8082-exec-2] ERROR JeecgBootExceptionHandler:93 - 不支持GET请求方法
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:213)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:422)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:367)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:110)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:59)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:396)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1234)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1016)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 15:34:48.006 [http-nio-8082-exec-10] ERROR JeecgBootExceptionHandler:93 - 不支持GET请求方法
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:213)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:422)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:367)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:110)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:59)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:396)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1234)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1016)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 15:34:48.566 [http-nio-8082-exec-4] INFO  DouYinDuanjuApiController:460 - 用户领券结果回调通知
: {
    "type": "send_coupon",
    "msg": "{\"coupon_id\":\"***************\",\"app_id\":\"ttxxxxx\",\"open_id\":\"********\",\"coupon_status\":10,\"receive_time\":**********,\"merchant_meta_no\":\"70908135***********\",\"valid_begin_time\":**********,\"valid_end_time\":**********,\"talent_open_id\":\"***********\",\"talent_account\":\"************\",\"union_id\":\"3d5f4913-xxxx-443d-b7ab-538db3f4e237\"}"
}
2025-07-03 15:34:53.457 [http-nio-8082-exec-4] ERROR DouYinDuanjuApiController:463 - 用户领券结果回调通知异常
com.alibaba.fastjson.JSONException: syntax error, expect {, actual string, pos 42, fieldName msg, fastjson-version 1.2.60
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:489)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseRest(JavaBeanDeserializer.java:1538)
	at com.alibaba.fastjson.parser.deserializer.FastjsonASMDeserializer_2_MsgData.deserialze(Unknown Source)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:284)
	at com.alibaba.fastjson.parser.deserializer.DefaultFieldDeserializer.parseField(DefaultFieldDeserializer.java:85)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseField(JavaBeanDeserializer.java:1224)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:850)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseRest(JavaBeanDeserializer.java:1538)
	at com.alibaba.fastjson.parser.deserializer.FastjsonASMDeserializer_1_CouponMessage.deserialze(Unknown Source)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:284)
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseObject(DefaultJSONParser.java:692)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:383)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:287)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:560)
	at com.eleven.cms.douyinduanju.service.impl.MiniAppCouponRecordServiceImpl.add(MiniAppCouponRecordServiceImpl.java:28)
	at com.eleven.cms.douyinduanju.service.impl.MiniAppCouponRecordServiceImpl$$FastClassBySpringCGLIB$$4502237d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.eleven.cms.douyinduanju.service.impl.MiniAppCouponRecordServiceImpl$$EnhancerBySpringCGLIB$$390bda30.add(<generated>)
	at com.eleven.cms.douyinduanju.controller.DouYinDuanjuApiController.couponCallBack(DouYinDuanjuApiController.java:461)
	at com.eleven.cms.douyinduanju.controller.DouYinDuanjuApiController$$FastClassBySpringCGLIB$$743e4573.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at org.jeecg.modules.system.aspect.DictAspect.doAround(DictAspect.java:50)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.eleven.cms.douyinduanju.controller.DouYinDuanjuApiController$$EnhancerBySpringCGLIB$$297fd01e.couponCallBack(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 15:36:12.596 [http-nio-8082-exec-9] ERROR JeecgBootExceptionHandler:93 - 不支持GET请求方法
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:213)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:422)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:367)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:110)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:59)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:396)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1234)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1016)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-03 15:36:41.477 [http-nio-8082-exec-7] INFO  DouYinDuanjuApiController:460 - 用户领券结果回调通知
: {
    "type": "send_coupon",
    "msg": {
        "coupon_id": "***************",
        "app_id": "ttxxxxx",
        "open_id": "********",
        "coupon_status": 10,
        "receive_time": **********,
        "merchant_meta_no": "70908135***********",
        "valid_begin_time": **********,
        "valid_end_time": **********,
        "talent_open_id": "***********",
        "talent_account": "************",
        "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237"
    }
}
2025-07-03 15:37:03.143 [http-nio-8082-exec-7] INFO  p6spy:60 - 2025-07-03T15:37:03.143 | 耗时 211 ms | SQL 语句：
INSERT INTO mini_app_coupon_record ( id, coupon_id, app_id, open_id, union_id, coupon_status, merchant_meta_no, talent_open_id, talent_account, callback_data, create_time ) VALUES ( '1940676135769169922', '***************', 'ttxxxxx', '********', '3d5f4913-xxxx-443d-b7ab-538db3f4e237', 10, '70908135***********', '***********', '************', '{ "type": "send_coupon", "msg": { "coupon_id": "***************", "app_id": "ttxxxxx", "open_id": "********", "coupon_status": 10, "receive_time": **********, "merchant_meta_no": "70908135***********", "valid_begin_time": **********, "valid_end_time": **********, "talent_open_id": "***********", "talent_account": "************", "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237" } }', '2025-07-03T15:37:01.066+0800' );
2025-07-03 16:03:35.853 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 16:03:36.994 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 16:03:38.945 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 16:03:38.946 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 16:03:39.050 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 99ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 16:03:39.651 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 16:03:39.651 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 16:03:39.718 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 66ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 16:03:39.734 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 16:03:39.736 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 16:03:39.783 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 39ms. Found 0 Redis repository interfaces.
2025-07-03 16:03:40.295 [main] INFO  GenericScope:295 - BeanFactory id=0fbbb8cf-edb7-3054-b5b2-245295025eeb
2025-07-03 16:03:40.357 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$4b580d79] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.471 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.472 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.474 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$8e728fb2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.547 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$21163c08] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.945 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.947 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$94e114b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.959 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:40.963 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:41.028 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:41.303 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 16:03:41.306 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 16:03:41.313 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:41.321 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:41.347 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:41.359 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$962694c3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:41.447 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$3e6b8193] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:41.452 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:03:42.202 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 16:03:42.227 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 16:03:42.228 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 16:03:42.228 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 16:03:42.390 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 16:03:42.391 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5369 ms
2025-07-03 16:03:43.964 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 16:03:43.985 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 16:03:49.160 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 16:03:53.326 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 16:03:58.496 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 16:04:04.231 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 16:04:04.231 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 16:04:04.232 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 16:04:04.232 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 16:04:04.232 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 16:04:04.232 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 16:04:04.232 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 16:04:18.634 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 16:04:18.641 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 16:04:18.686 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 16:04:18.687 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 16:04:18.701 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 16:04:18.706 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 16:04:18.708 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 16:04:18.708 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 16:04:18.708 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 16:04:18.709 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7fac91fb
2025-07-03 16:04:22.784 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 16:04:22.786 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 16:04:25.912 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 16:04:27.890 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 16:04:29.486 [redisson-netty-6-17] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 16:04:30.754 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:30.755 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:30.755 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:30.756 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:30.794 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:30.794 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:30.795 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:30.795 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:04:31.056 [main] TRACE WIRE:64 - [3255707f] Sending request GET / with parameters: 
2025-07-03 16:04:31.529 [I/O dispatcher 1] TRACE WIRE:97 - [3255707f] Received raw response: 200 OK
2025-07-03 16:04:31.751 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 16:04:31.751 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 16:04:31.751 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 16:04:31.751 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 16:04:31.752 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 16:04:51.672 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 16:04:52.975 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 16:05:04.876 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 16:05:04.878 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 16:05:04.878 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 16:05:04.878 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 16:05:05.466 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 16:05:05.467 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 16:05:05.675 [main] TRACE WIRE:64 - [24ef9204] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 16:05:06.094 [I/O dispatcher 1] TRACE WIRE:97 - [24ef9204] Received raw response: 200 OK
2025-07-03 16:05:06.327 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 16:05:06.328 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 16:05:06.363 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 16:05:06.364 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 16:05:10.142 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 16:05:11.141 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 16:05:11.205 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 16:05:11.488 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 16:05:12.814 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#b63ce92:0/SimpleConnection@3a7007d9 [delegate=amqp://admin@**************:5672/, localPort= 55933]
2025-07-03 16:05:25.827 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 111.061 seconds (JVM running for 112.183)
2025-07-03 16:05:25.867 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 16:05:26.173 [RMI TCP Connection(85)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 16:05:26.173 [RMI TCP Connection(85)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 16:05:26.271 [RMI TCP Connection(85)-*********] INFO  DispatcherServlet:547 - Completed initialization in 97 ms
2025-07-03 16:05:27.281 [RMI TCP Connection(88)-*********] TRACE WIRE:64 - [77ddb3a5] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 16:05:27.576 [I/O dispatcher 1] TRACE WIRE:97 - [77ddb3a5] Received raw response: 200 OK
2025-07-03 16:05:59.333 [http-nio-8082-exec-4] INFO  DouYinDuanjuApiController:460 - 用户领券结果回调通知
: {
    "type": "send_coupon",
    "msg": {
        "coupon_id": "***************",
        "app_id": "ttxxxxx",
        "open_id": "********",
        "coupon_status": 10,
        "receive_time": **********,
        "merchant_meta_no": "70908135***********",
        "valid_begin_time": **********,
        "valid_end_time": **********,
        "talent_open_id": "***********",
        "talent_account": "************",
        "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237"
    }
}
2025-07-03 16:06:02.563 [http-nio-8082-exec-4] INFO  p6spy:60 - 2025-07-03T16:06:02.562 | 耗时 351 ms | SQL 语句：
INSERT INTO mini_app_coupon_record ( id, coupon_id, app_id, open_id, union_id, coupon_status, receive_time, merchant_meta_no, talent_open_id, talent_account, callback_data, create_time ) VALUES ( '1940683431819677697', '***************', 'ttxxxxx', '********', '3d5f4913-xxxx-443d-b7ab-538db3f4e237', 10, '1970-01-20T20:29:06.782+0800', '70908135***********', '***********', '************', '{ "type": "send_coupon", "msg": { "coupon_id": "***************", "app_id": "ttxxxxx", "open_id": "********", "coupon_status": 10, "receive_time": **********, "merchant_meta_no": "70908135***********", "valid_begin_time": **********, "valid_end_time": **********, "talent_open_id": "***********", "talent_account": "************", "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237" } }', '2025-07-03T16:06:00.582+0800' );
2025-07-03 16:06:44.080 [http-nio-8082-exec-1] INFO  DouYinDuanjuApiController:460 - 用户领券结果回调通知
: {
    "type": "send_coupon",
    "msg": {
        "coupon_id": "***************",
        "app_id": "ttxxxxx",
        "open_id": "********",
        "coupon_status": 10,
        "receive_time": **********,
        "merchant_meta_no": "70908135***********",
        "valid_begin_time": **********,
        "valid_end_time": **********,
        "talent_open_id": "***********",
        "talent_account": "************",
        "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237"
    }
}
2025-07-03 16:06:46.041 [http-nio-8082-exec-1] INFO  p6spy:60 - 2025-07-03T16:06:46.041 | 耗时 637 ms | SQL 语句：
INSERT INTO mini_app_coupon_record ( id, coupon_id, app_id, open_id, union_id, coupon_status, receive_time, merchant_meta_no, talent_open_id, talent_account, callback_data, create_time ) VALUES ( '1940683618218741761', '***************', 'ttxxxxx', '********', '3d5f4913-xxxx-443d-b7ab-538db3f4e237', 10, '2023-06-12T13:13:02.000+0800', '70908135***********', '***********', '************', '{ "type": "send_coupon", "msg": { "coupon_id": "***************", "app_id": "ttxxxxx", "open_id": "********", "coupon_status": 10, "receive_time": **********, "merchant_meta_no": "70908135***********", "valid_begin_time": **********, "valid_end_time": **********, "talent_open_id": "***********", "talent_account": "************", "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237" } }', '2025-07-03T16:06:45.399+0800' );
2025-07-03 16:10:30.844 [background-preinit] INFO  Version:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-03 16:10:31.937 [main] INFO  JeecgApplication:652 - The following profiles are active: dev
2025-07-03 16:10:34.388 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 16:10:34.390 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-03 16:10:34.497 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 101ms. Found 11 Elasticsearch repository interfaces.
2025-07-03 16:10:35.187 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 16:10:35.189 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-03 16:10:35.262 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 72ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-03 16:10:35.278 [main] INFO  RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-03 16:10:35.279 [main] INFO  RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-03 16:10:35.325 [main] INFO  RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 39ms. Found 0 Redis repository interfaces.
2025-07-03 16:10:35.887 [main] INFO  GenericScope:295 - BeanFactory id=0fbbb8cf-edb7-3054-b5b2-245295025eeb
2025-07-03 16:10:35.950 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.ShiroConfig$$EnhancerBySpringCGLIB$$3bc9ec25] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.071 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.AppFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.073 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.eleven.cms.client.UnifyRightsFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.075 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'asyncExecutorConfig' of type [com.eleven.cms.config.AsyncExecutorConfig$$EnhancerBySpringCGLIB$$7ee46e5e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.167 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$11881ab4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.574 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.576 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8552f360] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.587 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.591 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.654 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.modules.shiro.authc.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.958 [main] INFO  ShiroConfig:255 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-03 16:10:36.961 [main] INFO  ShiroConfig:273 - ===============(2)创建RedisManager,连接Redis..URL= **************:6379
2025-07-03 16:10:36.969 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:36.977 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:37.003 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:37.017 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.config.RedisConfig$$EnhancerBySpringCGLIB$$8698736f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:37.100 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$2edd603f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:37.104 [main] INFO  PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-03 16:10:37.560 [main] INFO  TomcatWebServer:108 - Tomcat initialized with port(s): 8082 (http)
2025-07-03 16:10:37.574 [main] INFO  Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8082"]
2025-07-03 16:10:37.575 [main] INFO  StandardService:173 - Starting service [Tomcat]
2025-07-03 16:10:37.575 [main] INFO  StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-07-03 16:10:37.662 [main] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-03 16:10:37.662 [main] INFO  ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 5700 ms
2025-07-03 16:10:38.547 [main] INFO  DynamicDataSourceCreator:113 - 动态数据源-检测到druid存在,如配置中未指定type,druid会默认配置
2025-07-03 16:10:38.559 [main] INFO  DynamicRoutingDataSource:170 - 动态数据源-检测到并开启了p6spy
2025-07-03 16:10:43.091 [main] INFO  DruidDataSource:1003 - {dataSource-1,master} inited
2025-07-03 16:10:46.149 [main] INFO  DruidDataSource:1003 - {dataSource-2,xxl} inited
2025-07-03 16:10:49.959 [main] INFO  DruidDataSource:1003 - {dataSource-3,member} inited
2025-07-03 16:10:53.716 [main] INFO  DruidDataSource:1003 - {dataSource-4,miniapp} inited
2025-07-03 16:10:53.716 [main] INFO  DynamicRoutingDataSource:203 - 初始共加载 4 个数据源
2025-07-03 16:10:53.716 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 member 成功
2025-07-03 16:10:53.716 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 miniapp 成功
2025-07-03 16:10:53.716 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 xxl 成功
2025-07-03 16:10:53.716 [main] INFO  DynamicRoutingDataSource:142 - 动态数据源-加载 master 成功
2025-07-03 16:10:53.716 [main] INFO  DynamicRoutingDataSource:212 - 当前的默认数据源是单数据源，数据源名为 master
2025-07-03 16:11:00.849 [main] INFO  StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-03 16:11:00.855 [main] INFO  SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-03 16:11:00.903 [main] INFO  SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-03 16:11:00.903 [main] INFO  QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-03 16:11:00.915 [main] INFO  LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-03 16:11:00.915 [main] INFO  LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-03 16:11:00.925 [main] INFO  QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '刘永圣*************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-03 16:11:00.925 [main] INFO  StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-03 16:11:00.925 [main] INFO  StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-03 16:11:00.925 [main] INFO  QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@44debb87
2025-07-03 16:11:05.035 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 16:11:05.035 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'aiRingThreadPoolTaskExecutor'
2025-07-03 16:11:07.649 [main] INFO  Version:41 - Redisson 3.15.0
2025-07-03 16:11:09.777 [redisson-netty-6-24] INFO  MasterPubSubConnectionPool:167 - 1 connections initialized for /**************:6379
2025-07-03 16:11:11.001 [redisson-netty-6-18] INFO  MasterConnectionPool:167 - 24 connections initialized for /**************:6379
2025-07-03 16:11:12.167 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.167 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.168 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.168 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.202 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.geo.Point to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.202 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.geo.Point as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.203 [main] WARN  CustomConversions:273 - Registering converter from class org.springframework.data.elasticsearch.core.geo.GeoPoint to interface java.util.Map as writing converter although it doesn't convert to a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.203 [main] WARN  CustomConversions:263 - Registering converter from interface java.util.Map to class org.springframework.data.elasticsearch.core.geo.GeoPoint as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-03 16:11:12.417 [main] TRACE WIRE:64 - [69f7da24] Sending request GET / with parameters: 
2025-07-03 16:11:12.933 [I/O dispatcher 1] TRACE WIRE:97 - [69f7da24] Received raw response: 200 OK
2025-07-03 16:11:13.128 [main] INFO  VersionInfo:59 - Version Spring Data Elasticsearch: 4.0.9.RELEASE
2025-07-03 16:11:13.128 [main] INFO  VersionInfo:60 - Version Elasticsearch Client in build: 7.6.2
2025-07-03 16:11:13.128 [main] INFO  VersionInfo:61 - Version Elasticsearch Client used: 7.8.0
2025-07-03 16:11:13.128 [main] WARN  VersionInfo:64 - Version mismatch in between Elasticsearch Clients build/use: 7.6.2 - 7.8.0
2025-07-03 16:11:13.128 [main] INFO  VersionInfo:69 - Version Elasticsearch cluster: 7.8.0
2025-07-03 16:11:30.926 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.BusinessChargeDto.
2025-07-03 16:11:32.032 [main] WARN  TableInfoHelper:285 - Warn: Could not find @TableId in Class: com.eleven.cms.dto.DataCollectDto.
2025-07-03 16:11:44.600 [main] INFO  AliMnsService:119 - 阿里云MNS消息服务-===============轮询启动==============
2025-07-03 16:11:44.601 [pool-4-thread-1] INFO  AliMnsService:442 - 阿里云MNS消息服务-===============轮询启动-海艺==============
2025-07-03 16:11:44.602 [pool-4-thread-3] INFO  AliMnsService:602 - 阿里云MNS消息服务-===============轮询启动-腾讯ai穿越换脸==============
2025-07-03 16:11:44.602 [pool-4-thread-2] INFO  AliMnsService:522 - 阿里云MNS消息服务-===============轮询启动-腾讯ai换脸==============
2025-07-03 16:11:45.290 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-03 16:11:45.290 [main] INFO  ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'outSidecallbackExecutor'
2025-07-03 16:11:45.487 [main] TRACE WIRE:64 - [69a9487] Sending request HEAD /insert_code_2025-07?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=false with parameters: 
2025-07-03 16:11:45.634 [I/O dispatcher 1] TRACE WIRE:97 - [69a9487] Received raw response: 200 OK
2025-07-03 16:11:45.872 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 16:11:45.873 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 16:11:45.911 [main] WARN  URLConfigurationSource:121 - No URLs will be polled as dynamic configuration sources.
2025-07-03 16:11:45.911 [main] INFO  URLConfigurationSource:122 - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-03 16:11:51.007 [main] INFO  EndpointLinksResolver:58 - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-03 16:11:51.930 [main] INFO  Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8082"]
2025-07-03 16:11:51.996 [main] INFO  TomcatWebServer:220 - Tomcat started on port(s): 8082 (http) with context path '/cms-vrbt-miniapp'
2025-07-03 16:11:52.556 [main] INFO  CachingConnectionFactory:560 - Attempting to connect to: [**************:5672]
2025-07-03 16:11:54.072 [main] INFO  CachingConnectionFactory:511 - Created new connection: rabbitConnectionFactory#d88551a:0/SimpleConnection@57bb38ce [delegate=amqp://admin@**************:5672/, localPort= 56624]
2025-07-03 16:12:09.363 [main] INFO  JeecgApplication:61 - Started JeecgApplication in 99.244 seconds (JVM running for 100.24)
2025-07-03 16:12:10.000 [main] INFO  JeecgApplication:41 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8082/cms-vrbt-miniapp/
	External: 	http://*********:8082/cms-vrbt-miniapp/
	Swagger-UI: 		http://*********:8082/cms-vrbt-miniapp/doc.html
----------------------------------------------------------
2025-07-03 16:12:11.471 [RMI TCP Connection(58)-*********] INFO  [/cms-vrbt-miniapp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 16:12:11.471 [RMI TCP Connection(58)-*********] INFO  DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-03 16:12:11.819 [RMI TCP Connection(58)-*********] INFO  DispatcherServlet:547 - Completed initialization in 348 ms
2025-07-03 16:12:11.860 [RMI TCP Connection(57)-*********] TRACE WIRE:64 - [394c832a] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 16:12:11.998 [I/O dispatcher 1] TRACE WIRE:97 - [394c832a] Received raw response: 200 OK
2025-07-03 16:12:41.106 [http-nio-8082-exec-3] INFO  DouYinDuanjuApiController:460 - 用户领券结果回调通知
: {
    "type": "send_coupon",
    "msg": {
        "coupon_id": "***************",
        "app_id": "ttxxxxx",
        "open_id": "********",
        "coupon_status": 10,
        "receive_time": **********,
        "merchant_meta_no": "70908135***********",
        "valid_begin_time": **********,
        "valid_end_time": **********,
        "talent_open_id": "***********",
        "talent_account": "************",
        "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237"
    }
}
2025-07-03 16:12:44.326 [http-nio-8082-exec-3] INFO  p6spy:60 - 2025-07-03T16:12:44.326 | 耗时 335 ms | SQL 语句：
SELECT id,coupon_id,app_id,open_id,union_id,coupon_status,receive_time,merchant_meta_no,valid_begin_time,valid_end_time,talent_open_id,talent_account,use_time,order_id,discount_amount,min_consume_amount,source_channel,callback_data,process_status,process_result,retry_count,remark,create_time,update_time,deleted FROM mini_app_coupon_record WHERE coupon_id = '***************' limit 1;
2025-07-03 16:12:44.620 [http-nio-8082-exec-3] WARN  MiniAppCouponRecordServiceImpl:40 - 优惠券记录已存在:***************
2025-07-03 16:13:00.249 [http-nio-8082-exec-4] INFO  DouYinDuanjuApiController:460 - 用户领券结果回调通知
: {
    "type": "send_coupon",
    "msg": {
        "coupon_id": "***************",
        "app_id": "ttxxxxx",
        "open_id": "********",
        "coupon_status": 10,
        "receive_time": **********,
        "merchant_meta_no": "70908135***********",
        "valid_begin_time": **********,
        "valid_end_time": **********,
        "talent_open_id": "***********",
        "talent_account": "************",
        "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237"
    }
}
2025-07-03 16:13:00.499 [http-nio-8082-exec-4] INFO  p6spy:60 - 2025-07-03T16:13:00.499 | 耗时 246 ms | SQL 语句：
SELECT id,coupon_id,app_id,open_id,union_id,coupon_status,receive_time,merchant_meta_no,valid_begin_time,valid_end_time,talent_open_id,talent_account,use_time,order_id,discount_amount,min_consume_amount,source_channel,callback_data,process_status,process_result,retry_count,remark,create_time,update_time,deleted FROM mini_app_coupon_record WHERE coupon_id = '***************' limit 1;
2025-07-03 16:13:00.552 [RMI TCP Connection(62)-*********] TRACE WIRE:64 - [63e288ff] Sending request GET /_cluster/health/ with parameters: 
2025-07-03 16:13:00.864 [I/O dispatcher 1] TRACE WIRE:97 - [63e288ff] Received raw response: 200 OK
2025-07-03 16:13:01.668 [http-nio-8082-exec-4] INFO  p6spy:60 - 2025-07-03T16:13:01.668 | 耗时 326 ms | SQL 语句：
INSERT INTO mini_app_coupon_record ( id, coupon_id, app_id, open_id, union_id, coupon_status, receive_time, merchant_meta_no, valid_begin_time, valid_end_time, talent_open_id, talent_account, callback_data, create_time ) VALUES ( '1940685193746145282', '***************', 'ttxxxxx', '********', '3d5f4913-xxxx-443d-b7ab-538db3f4e237', 10, '2023-06-12T13:13:02.000+0800', '70908135***********', '2021-12-16T16:29:54.000+0800', '2021-12-16T16:22:28.000+0800', '***********', '************', '{ "type": "send_coupon", "msg": { "coupon_id": "***************", "app_id": "ttxxxxx", "open_id": "********", "coupon_status": 10, "receive_time": **********, "merchant_meta_no": "70908135***********", "valid_begin_time": **********, "valid_end_time": **********, "talent_open_id": "***********", "talent_account": "************", "union_id": "3d5f4913-xxxx-443d-b7ab-538db3f4e237" } }', '2025-07-03T16:13:00.507+0800' );
