

ALTER TABLE `onl_cgform_field`
MODIFY COLUMN `dict_table` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字典表' AFTER `dict_field`;

ALTER TABLE `onl_cgform_field`
ADD COLUMN `converter` varchar(255) NULL COMMENT '自定义值转换器' AFTER `create_by`;

INSERT INTO `onl_cgform_head`(`id`, `table_name`, `table_type`, `table_version`, `table_txt`, `is_checkbox`, `is_db_synch`, `is_page`, `is_tree`, `id_sequence`, `id_type`, `query_mode`, `relation_type`, `sub_table_str`, `tab_order_num`, `tree_parent_id_field`, `tree_id_field`, `tree_fieldname`, `form_category`, `form_template`, `form_template_mobile`, `scroll`, `copy_version`, `copy_type`, `physic_id`, `update_by`, `update_time`, `create_by`, `create_time`, `theme_template`) VALUES ('3d447fa919b64f6883a834036c14aa67', 'test_enhance_select', 1, 5, 'js增强实现下拉联动效果', 'N', 'Y', 'Y', 'N', NULL, 'UUID', 'single', NULL, NULL, NULL, NULL, NULL, NULL, 'bdfl_include', '1', NULL, 0, NULL, 0, NULL, 'admin', '2020-02-21 17:58:46', 'admin', '2020-02-20 16:19:00', 'normal');
INSERT INTO `onl_cgform_field`(`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_value_rule_code`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`) VALUES ('209ddb923d8dab9f454d56d82c0cc725', '3d447fa919b64f6883a834036c14aa67', 'create_by', '创建人', NULL, 0, 1, 'string', 50, 0, '', '', '', '', 'text', '', 120, NULL, '0', '', '', 0, 0, 0, 0, 'single', '', '', 6, 'admin', '2020-02-21 17:58:46', '2020-02-20 16:19:00', 'admin', NULL);
INSERT INTO `onl_cgform_field`(`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_value_rule_code`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`) VALUES ('37462a4d78968d0e432d746423603b81', '3d447fa919b64f6883a834036c14aa67', 'province', '省份', NULL, 0, 1, 'String', 100, 0, '', '', '{table:\'sys_category\',txt:\'name\',key:\'id\',linkField:\'city,area\',idField:\'id\',pidField:\'pid\', condition:\" pid = \'1230769196661510146\' \"         }', '', 'link_down', '', 120, NULL, '0', '', '', 0, 1, 1, 0, 'single', '', '', 2, 'admin', '2020-02-21 17:58:46', '2020-02-20 16:19:00', 'admin', NULL);
INSERT INTO `onl_cgform_field`(`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_value_rule_code`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`) VALUES ('7cdbe0d563e15f3fb1fb32d996fe4ba7', '3d447fa919b64f6883a834036c14aa67', 'area', '区', NULL, 0, 1, 'String', 100, 0, '', '', '', '', 'text', '', 120, NULL, '0', '', '', 0, 1, 1, 0, 'single', '', '', 4, 'admin', '2020-02-21 17:58:46', '2020-02-20 16:19:00', 'admin', NULL);
INSERT INTO `onl_cgform_field`(`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_value_rule_code`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`) VALUES ('91f7cd9b59c0da033363f8a09b02ec96', '3d447fa919b64f6883a834036c14aa67', 'create_time', '创建日期', NULL, 0, 1, 'Date', 20, 0, '', '', '', '', 'datetime', '', 120, NULL, '0', '', '', 0, 0, 0, 0, 'single', '', '', 5, 'admin', '2020-02-21 17:58:46', '2020-02-20 16:19:00', 'admin', NULL);
INSERT INTO `onl_cgform_field`(`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_value_rule_code`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`) VALUES ('9bd056786694d67666f6924cc225b1a0', '3d447fa919b64f6883a834036c14aa67', 'id', '主键', NULL, 1, 0, 'string', 36, 0, '', '', '', '', 'text', '', 120, NULL, '0', '', '', 0, 0, 0, 0, 'single', '', '', 1, 'admin', '2020-02-21 17:58:46', '2020-02-20 16:19:00', 'admin', NULL);
INSERT INTO `onl_cgform_field`(`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_value_rule_code`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`) VALUES ('c988bd2b6125c01ceb3579a207dd8784', '3d447fa919b64f6883a834036c14aa67', 'city', '市', NULL, 0, 1, 'String', 100, 0, '', '', '', '', 'text', '', 120, NULL, '0', '', '', 0, 1, 1, 0, 'single', '', '', 3, 'admin', '2020-02-21 17:58:46', '2020-02-20 16:19:00', 'admin', NULL);

INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769855347593217', '1230769769930592257', 'C区', NULL, 'admin', '2020-02-21 16:22:54', NULL, NULL, 'A01A03', NULL);
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769769930592257', '1230769253267836929', '淮南市', NULL, 'admin', '2020-02-21 16:22:34', 'admin', '2020-02-21 16:22:54', 'A01A03', '1');
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769620021972993', '1230769470889299970', 'B区', NULL, 'admin', '2020-02-21 16:21:58', NULL, NULL, 'A01A03', NULL);
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769547519234050', '1230769470889299970', 'A区', NULL, 'admin', '2020-02-21 16:21:41', NULL, NULL, 'A01A03', NULL);
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769470889299970', '1230769290609725441', '济南市', NULL, 'admin', '2020-02-21 16:21:23', 'admin', '2020-02-21 16:21:41', 'A01A03', '1');
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769407907631106', '1230769347157331969', '包河区', NULL, 'admin', '2020-02-21 16:21:08', NULL, NULL, 'A01A03', NULL);
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769347157331969', '1230769253267836929', '合肥市', NULL, 'admin', '2020-02-21 16:20:53', 'admin', '2020-02-21 16:21:08', 'A01A03', '1');
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769290609725441', '1230769196661510146', '山东省', NULL, 'admin', '2020-02-21 16:20:40', 'admin', '2020-02-21 16:21:23', 'A01A03', '1');
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769253267836929', '1230769196661510146', '安徽省', NULL, 'admin', '2020-02-21 16:20:31', 'admin', '2020-02-21 16:20:53', 'A01A03', '1');
INSERT INTO `sys_category`(`id`, `pid`, `name`, `code`, `create_by`, `create_time`, `update_by`, `update_time`, `sys_org_code`, `has_child`) VALUES ('1230769196661510146', '0', '省', NULL, 'admin', '2020-02-21 16:20:16', 'admin', '2020-02-21 16:20:31', 'A01A03', '1');

INSERT INTO `sys_dict`(`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES ('1232913193820581889', 'Online表单业务分类', 'ol_form_biz_type', '', 0, 'admin', '2020-02-27 14:19:46', 'admin', '2020-02-27 14:20:23', 0);
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1232913424813486081', '1232913193820581889', '官方示例', 'demo', '', 1, 1, 'admin', '2020-02-27 14:20:42', 'admin', '2020-02-27 14:21:37');
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1232913493717512194', '1232913193820581889', '流程表单', 'bpm', '', 2, 1, 'admin', '2020-02-27 14:20:58', 'admin', '2020-02-27 14:22:20');
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1232913605382467585', '1232913193820581889', '测试表单', 'temp', '', 4, 1, 'admin', '2020-02-27 14:21:25', 'admin', '2020-02-27 14:22:16');
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1232914232372195330', '1232913193820581889', '导入表单', 'bdfl_include', '', 5, 1, 'admin', '2020-02-27 14:23:54', NULL, NULL);

INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1235823781053313025', 'e41b69c57a941a3bbcce45032fe57605', 'AUTO在线内嵌子表', '/online/cgformInnerTableList/:code', 'modules/online/cgform/auto/innerTable/OnlCgformInnerTableList', '1', NULL, NULL, '1', NULL, '1', '999', '0', NULL, '1', '0', '1', NULL, 'admin', '2020-03-06 15:05:24', 'admin', '2020-03-06 15:07:42', '0', '0', '1', '0');

DELETE from onl_cgreport_item where cgrhead_id not in (select id from onl_cgreport_head);
DELETE  from onl_cgreport_param where cgrhead_id not in (select id from onl_cgreport_head);

ALTER TABLE `onl_cgform_field`
ADD COLUMN `query_def_val` varchar(50) NULL COMMENT '查询默认值' AFTER `converter`,
ADD COLUMN `query_dict_text` varchar(100) NULL COMMENT '查询配置字典text' AFTER `query_def_val`,
ADD COLUMN `query_dict_field` varchar(100) NULL COMMENT '查询配置字典code' AFTER `query_dict_text`,
ADD COLUMN `query_dict_table` varchar(500) NULL COMMENT '查询配置字典table' AFTER `query_dict_field`,
ADD COLUMN `query_show_type` varchar(50) NULL COMMENT '查询显示控件' AFTER `query_dict_table`,
ADD COLUMN `query_config_flag` varchar(3) NULL COMMENT '是否启用查询配置1是0否' AFTER `query_show_type`,
ADD COLUMN `query_valid_type` varchar(50) NULL COMMENT '查询字段校验类型' AFTER `query_config_flag`,
ADD COLUMN `query_must_input` varchar(3) NULL COMMENT '查询字段是否必填1是0否' AFTER `query_valid_type`,
ADD COLUMN `sort_flag` varchar(3) NULL COMMENT '是否支持排序1是0否' AFTER `query_must_input`;


UPDATE `onl_cgform_field` SET `field_value_rule_code` =  CONCAT('${', field_value_rule_code, '}') WHERE field_value_rule_code IS NOT NULL AND field_value_rule_code != '';
ALTER TABLE `onl_cgform_field`
CHANGE COLUMN `field_value_rule_code` `field_default_value`  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '控件默认值，不同的表达式展示不同的结果。\r\n1. 纯字符串直接赋给默认值；\r\n2. #{普通变量}；\r\n3. {{ 动态JS表达式 }}；\r\n4. ${填值规则编码}；\r\n填值规则表达式只允许存在一个，且不能和其他规则混用。' AFTER `field_extend_json`;

INSERT INTO `onl_cgform_head` (`id`, `table_name`, `table_type`, `table_version`, `table_txt`, `is_checkbox`, `is_db_synch`, `is_page`, `is_tree`, `id_sequence`, `id_type`, `query_mode`, `relation_type`, `sub_table_str`, `tab_order_num`, `tree_parent_id_field`, `tree_id_field`, `tree_fieldname`, `form_category`, `form_template`, `form_template_mobile`, `scroll`, `copy_version`, `copy_type`, `physic_id`, `update_by`, `update_time`, `create_by`, `create_time`, `theme_template`) VALUES ('4adec929a6594108bef5b35ee9966e9f', 'demo_field_def_val_main', '2', '1', '示例：控件默认值（主表）', 'Y', 'N', 'Y', 'N', NULL, 'UUID', 'single', NULL, 'demo_field_def_val_sub', NULL, NULL, NULL, NULL, 'demo', '1', NULL, '1', NULL, '0', NULL, 'admin', '2020-04-10 19:51:27', 'admin', '2020-04-10 19:35:57', 'normal');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('101a73df0aa5199ac05c4ce92a4f0e3e', '4adec929a6594108bef5b35ee9966e9f', 'name', '姓名', NULL, '0', '1', 'String', '200', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '#{sysUserCode}', '0', '1', '1', '0', 'single', '', '', '3', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('4c4f4067fa31737f3ff9e088130ef793', '4adec929a6594108bef5b35ee9966e9f', 'sex', '性别', NULL, '0', '1', 'String', '200', '0', '', 'sex', '', '', 'list', '', '120', NULL, '0', '', '{{ 2 }}', '0', '1', '1', '0', 'single', '', '', '4', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('5f5ac4d37fd1a3a09e2b19f0d4d99c0f', '4adec929a6594108bef5b35ee9966e9f', 'code', '编码', NULL, '0', '1', 'String', '200', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '${order_num_rule_param}', '0', '1', '1', '0', 'single', '', '', '2', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('7b4c181e4ebd76022f75535ed6fd9de3', '4adec929a6594108bef5b35ee9966e9f', 'update_time', '更新日期', NULL, '0', '1', 'Date', '20', '0', '', '', '', '', 'datetime', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '10', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('8e080f4ded1e3b2a1daa5b11eca4a0ff', '4adec929a6594108bef5b35ee9966e9f', 'create_by', '创建人', NULL, '0', '1', 'string', '50', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '7', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('951c51699d728072d88196d30f7aad10', '4adec929a6594108bef5b35ee9966e9f', 'address', '地址', NULL, '0', '1', 'String', '200', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '{{ demoFieldDefVal_getAddress() }}', '0', '1', '1', '0', 'single', '', '', '5', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('9ceff249ef81ca6fa145456667c89051', '4adec929a6594108bef5b35ee9966e9f', 'create_time', '创建日期', NULL, '0', '1', 'Date', '20', '0', '', '', '', '', 'datetime', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '8', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('c0c80f370e1d400fe670d8702c3b84da', '4adec929a6594108bef5b35ee9966e9f', 'address_param', '地址（传参）', NULL, '0', '1', 'String', '32', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '{{ demoFieldDefVal_getAddress(\"昌平区\") }}', '0', '1', '1', '0', 'single', '', '', '6', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:40:53', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('c2b9eae184afe56d59ea7940d77cfced', '4adec929a6594108bef5b35ee9966e9f', 'id', '主键', NULL, '1', '0', 'string', '36', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '1', 'single', '', '', '1', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:57', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('df58a7b1c3ed8f58a1994c0c0855db16', '4adec929a6594108bef5b35ee9966e9f', 'sys_org_code', '所属部门', NULL, '0', '1', 'string', '64', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '11', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('fed133a00f57245d4cfb02dd3c3ce7c1', '4adec929a6594108bef5b35ee9966e9f', 'update_by', '更新人', NULL, '0', '1', 'string', '50', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '9', 'admin', '2020-04-10 19:43:38', '2020-04-10 19:35:58', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
-- 主表JS增强
INSERT INTO `onl_cgform_enhance_js` (`ID`, `CG_JS`, `CG_JS_TYPE`, `CONTENT`, `CGFORM_HEAD_ID`) VALUES ('44cad4eec436328ed3cc134855f8d1d5', ' onlChange(){\n   return {\n    name(that, event) {\n      that.executeMainFillRule()\n    }\n  }\n }', 'form', NULL, '4adec929a6594108bef5b35ee9966e9f');
-- 控件默认值示例：子表
INSERT INTO `onl_cgform_head` (`id`, `table_name`, `table_type`, `table_version`, `table_txt`, `is_checkbox`, `is_db_synch`, `is_page`, `is_tree`, `id_sequence`, `id_type`, `query_mode`, `relation_type`, `sub_table_str`, `tab_order_num`, `tree_parent_id_field`, `tree_id_field`, `tree_fieldname`, `form_category`, `form_template`, `form_template_mobile`, `scroll`, `copy_version`, `copy_type`, `physic_id`, `update_by`, `update_time`, `create_by`, `create_time`, `theme_template`) VALUES ('4fb8e12a697f4d5bbe9b9fb1e9009486', 'demo_field_def_val_sub', '3', '1', '示例：控件默认值（子表）', 'Y', 'N', 'Y', 'N', NULL, 'UUID', 'single', '0', NULL, '1', NULL, NULL, NULL, 'demo', '1', NULL, '1', NULL, '0', NULL, 'admin', '2020-04-10 19:51:31', 'admin', '2020-04-10 19:47:01', 'normal');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('0a453a63e33654aa1b9ee2affa854a6d', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'create_by', '创建人', NULL, '0', '1', 'string', '50', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '6', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('1d95bdf1120c5a1776df022d0a571f21', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'create_time', '创建日期', NULL, '0', '1', 'Date', '20', '0', '', '', '', '', 'datetime', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '7', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('2e6f741fa96a49a0adccc7b4682c1cef', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'name', '名称', NULL, '0', '1', 'String', '200', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '#{sysUserName}', '0', '1', '1', '0', 'single', '', '', '3', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('6a0082c8ffbae092e99689520f1c83f7', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'update_time', '更新日期', NULL, '0', '1', 'Date', '20', '0', '', '', '', '', 'datetime', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '9', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('6e5c9d8e8b7eb6980ec246cb813b180b', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'sys_org_code', '所属部门', NULL, '0', '1', 'string', '64', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '10', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('9341a3b2a734d8c73455c136e1cac8ad', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'update_by', '更新人', NULL, '0', '1', 'string', '50', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', '', '', '8', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', 'text', '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('bef85fd2846dd7ffc42d747095557d14', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'date', '日期', NULL, '0', '1', 'string', '200', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '#{date}', '0', '1', '1', '0', 'single', '', '', '4', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('c382877700a9987f4bcc2deea9ee9daf', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'id', '主键', NULL, '1', '0', 'string', '36', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '1', 'single', '', '', '1', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('d44b89cc5f1828f7ceb9be196d549665', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'code', '编码', NULL, '0', '1', 'String', '200', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '${order_num_rule_param}', '0', '1', '1', '0', 'single', '', '', '2', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:01', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');
INSERT INTO `onl_cgform_field` (`id`, `cgform_head_id`, `db_field_name`, `db_field_txt`, `db_field_name_old`, `db_is_key`, `db_is_null`, `db_type`, `db_length`, `db_point_length`, `db_default_val`, `dict_field`, `dict_table`, `dict_text`, `field_show_type`, `field_href`, `field_length`, `field_valid_type`, `field_must_input`, `field_extend_json`, `field_default_value`, `is_query`, `is_show_form`, `is_show_list`, `is_read_only`, `query_mode`, `main_table`, `main_field`, `order_num`, `update_by`, `update_time`, `create_time`, `create_by`, `converter`, `query_def_val`, `query_dict_text`, `query_dict_field`, `query_dict_table`, `query_show_type`, `query_config_flag`, `query_valid_type`, `query_must_input`, `sort_flag`) VALUES ('faaaca21b7b2b16089c885f3224e4dc5', '4fb8e12a697f4d5bbe9b9fb1e9009486', 'main_id', '主表ID', NULL, '0', '1', 'String', '200', '0', '', '', '', '', 'text', '', '120', NULL, '0', '', '', '0', '0', '0', '0', 'single', 'demo_field_def_val_main', 'id', '5', 'admin', '2020-04-10 19:51:27', '2020-04-10 19:47:55', 'admin', '', '', '', '', '', NULL, '0', NULL, NULL, '0');

ALTER TABLE `onl_cgform_index`
ADD COLUMN `is_db_synch` varchar(2) NULL DEFAULT 'N' COMMENT '是否同步数据库 N未同步 Y已同步' AFTER `update_time`,
ADD COLUMN `del_flag` int(1) NULL DEFAULT 0 COMMENT '是否删除 0未删除 1删除' AFTER `is_db_synch`;

ALTER TABLE `onl_cgform_button`
ADD COLUMN `OPT_POSITION` varchar(3) NULL COMMENT '按钮位置1侧面 2底部' AFTER `ORDER_NUM`;
update ONL_CGFORM_BUTTON SET OPT_POSITION = '2';

INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1234371726545010689', '4e4602b3e3686f0911384e188dc7efb4', '左模糊', 'LEFT_LIKE', '左模糊', 7.00, 1, 'admin', '2020-03-02 14:55:27', NULL, NULL);
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1234371809495760898', '4e4602b3e3686f0911384e188dc7efb4', '右模糊', 'RIGHT_LIKE', '右模糊', 7.00, 1, 'admin', '2020-03-02 14:55:47', NULL, NULL);

-- 将空字符串改为null
UPDATE sys_user SET phone = NULL WHERE phone = '';
UPDATE sys_user SET email = NULL WHERE email = '';

ALTER TABLE `sys_user`
    ADD UNIQUE INDEX `uniq_sys_user_username` (`username`) USING BTREE,
    ADD UNIQUE INDEX `uniq_sys_user_phone` (`phone`) USING BTREE,
    ADD UNIQUE INDEX `uniq_sys_user_email` (`email`) USING BTREE;

ALTER TABLE `sys_user`
ADD COLUMN `third_id`  varchar(100) NULL COMMENT '第三方登录的唯一标识' AFTER `del_flag`;
ALTER TABLE `sys_user`
ADD COLUMN `third_type`  varchar(100) NULL COMMENT '第三方类型' AFTER `third_id`;
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1169504891467464705', '第三方登录角色', 'third_role', '第三方登录角色', 'admin', '2019-09-05 14:57:49', 'admin', '2019-09-05 14:57:56');

ALTER TABLE `sys_category`
ADD UNIQUE INDEX `index_code`(`code`);

ALTER TABLE `sys_announcement`
ADD COLUMN `msg_abstract` text COMMENT '摘要' AFTER `user_ids`;

ALTER TABLE `sys_user`
CHANGE COLUMN `identity` `user_identity` tinyint(1) DEFAULT NULL COMMENT '身份（1普通成员 2上级）' AFTER `update_time`;

ALTER TABLE `sys_quartz_job`
ADD UNIQUE INDEX `uniq_job_class_name` USING BTREE (`job_class_name`) ;

ALTER TABLE `sys_position`
ADD UNIQUE INDEX `uniq_code` USING BTREE (`code`) ;

ALTER TABLE `sys_depart`
ADD UNIQUE INDEX `uniq_depart_org_code` USING BTREE (`org_code`) ;

ALTER TABLE `sys_depart`
MODIFY COLUMN `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述' AFTER `depart_order`;

INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES ('1250687930947620866', '定时任务状态', 'quartz_status', '', '0', 'admin', '2020-04-16 15:30:14', '', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1250688147579228161', '1250687930947620866', '正常', '0', '', '1', '1', 'admin', '2020-04-16 15:31:05', '', NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1250688201064992770', '1250687930947620866', '停止', '-1', '', '1', '1', 'admin', '2020-04-16 15:31:18', '', NULL);

UPDATE `sys_dict` SET `id`='1174511106530525185', `dict_name`='机构类型', `dict_code`='org_category', `description`='机构类型 1公司，2部门 3岗位', `del_flag`='0', `create_by`='admin', `create_time`='2019-09-19 10:30:43', `update_by`=NULL, `update_time`=NULL, `type`='0' WHERE (`id`='1174511106530525185');
UPDATE `sys_dict_item` SET `id`='1174511197735665665', `dict_id`='1174511106530525185', `item_text`='公司', `item_value`='1', `description`='公司', `sort_order`='1', `status`='1', `create_by`='admin', `create_time`='2019-09-19 10:31:05', `update_by`=NULL, `update_time`=NULL WHERE (`id`='1174511197735665665');
UPDATE `sys_dict_item` SET `id`='1174511244036587521', `dict_id`='1174511106530525185', `item_text`='部门', `item_value`='2', `description`='部门', `sort_order`='1', `status`='1', `create_by`='admin', `create_time`='2019-09-19 10:31:16', `update_by`=NULL, `update_time`=NULL WHERE (`id`='1174511244036587521');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1174509082208395266', '1174511106530525185', '岗位', '3', '岗位', '1', '1', 'admin', '2019-09-19 10:31:16', '', NULL);

update SYS_PERMISSION set URL = '/account/settings/BaseSetting',COMPONENT_NAME='account-settings-base' where id = '1367a93f2c410b169faa7abcbad2f77c';
update SYS_PERMISSION set URL = '/account/settings/Index' where id = '6e73eb3c26099c191bf03852ee1310a1';




