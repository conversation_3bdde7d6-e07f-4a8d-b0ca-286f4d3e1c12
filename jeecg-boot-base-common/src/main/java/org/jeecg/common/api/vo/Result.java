package org.jeecg.common.api.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 *   接口返回数据格式
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019年1月19日
 */
@Data
@ApiModel(value = "接口返回对象", description = "接口返回对象")
public class Result<T> implements Serializable {


    private static final long serialVersionUID = 1L;

    public static final String MSG_BLACK_LIMIT = "暂时无法提供服务，请谅解";
    private static final String MSG_PROVINCE_LIMIT = "暂未开放，敬请期待";
    private static final String MSG_DUPLICATE_LIMIT = "您好，目前该业务服务正在维护中，建议您稍后再来尝试";
    private static final String MSG_APP_LIMIT = "暂无资格，敬请期待";
    private static final String MSG_AMOUNT_LIMIT = "今日活动已结束";
    private static final String MSG_SMS_DAY_LIMIT = "今日获取短信验证码的次数过多";
    private static final String MSG_ISP_RESTRICT = "由于业务限制，您目前无法开通此业务。";
    private static final String MSG_TIME_RESTRICT = "目前时段无法提供服务，请稍后再试";
    private static final String MSG_IP_LIMIT = "系统开小差了，请稍后再试";
	private static final String MSG_REPEAT_SUB = "你已开通,请勿重复开通";
	private static final String MSG_SYSTEM_ERROR = "系统繁忙,请稍后再试!";
	private static final String MSG_SYSTEM_NOT_SUPPORT = "暂不支持办理,请联系管理员";
	private static final String MSG_SMS_ERROR = "获取验证码失败";
	private static final String MSG_NOT_SUB = "暂无订购资格，请稍后再试!";
	private static final String MSG_SMS_DELAY_ERROR = "获取验证码失败,请稍后再试!";
	private static final String MSG_SMS_CODE_ERROR = "获取短信验证码失败";
	private static final String MSG_SEND_SMS_DELAY_ERROR = "短信发送失败,请稍后再试";
	private static final String MSG_SEND_SMS_TOO_FREQUENT = "操作频繁,请稍后再试";
	private static final String MSG_WX_MINI_NOT_SUPPORT_LOGIN = "当前小程序不支持登录，请联系管理员";

	private static final Result<Object> OK = ok(CommonConstant.SC_OK_200, "成功");

	private static final Result<Object> ERROR_BLACK_LIMIT = error(MSG_BLACK_LIMIT);
	private static final Result<Object> ERROR_PROVINCE_LIMIT = error(MSG_PROVINCE_LIMIT);
	private static final Result<Object> ERROR_DUPLICATE_LIMIT = error(MSG_DUPLICATE_LIMIT);
	private static final Result<Object> ERROR_APP_LIMIT = error(MSG_APP_LIMIT);
	private static final Result<Object> ERROR_AMOUNT_LIMIT = error(MSG_AMOUNT_LIMIT);
	private static final Result<Object> ERROR_ISP_RESTRICT = error(MSG_ISP_RESTRICT);
	private static final Result<Object> ERROR_TIME_RESTRICT = error(MSG_TIME_RESTRICT);
	private static final Result<Object> ERROR_SMS_DAY_LIMIT = error(MSG_SMS_DAY_LIMIT);
	private static final Result<Object> ERROR_IP_LIMIT = error(MSG_IP_LIMIT);
	private static final Result<Object> ERROR_REPEAT_SUB = error(MSG_REPEAT_SUB);
	private static final Result<Object> ERROR_SYSTEM_ERROR = error(MSG_SYSTEM_ERROR);
	private static final Result<Object> ERROR_SYSTEM_NOT_SUPPORT = error(MSG_SYSTEM_NOT_SUPPORT);
	private static final Result<Object> ERROR_SMS_ERROR = error(MSG_SMS_ERROR);
	private static final Result<Object> ERROR_NOT_SUB_ERROR = error(MSG_NOT_SUB);
	private static final Result<Object> ERROR_SMS_DELAY_ERROR = error(MSG_SMS_DELAY_ERROR);
	private static final Result<Object> ERROR_SMS_CODE_ERROR = error(MSG_SMS_CODE_ERROR);
	private static final Result<Object> ERROR_SEND_SMS_DELAY_ERROR = error(MSG_SEND_SMS_DELAY_ERROR);
	private static final Result<Object> ERROR_SEND_SMS_TOO_FREQUENT = error(MSG_SEND_SMS_TOO_FREQUENT);
	private static final Result<Object> ERROR_WX_MINI_NOT_SUPPORT_LOGIN = error(MSG_WX_MINI_NOT_SUPPORT_LOGIN);

	/**
     * 成功标志
     */
    @ApiModelProperty(value = "成功标志")
    private boolean success = true;

    /**
     * 返回处理消息
     */
	@ApiModelProperty(value = "返回处理消息")
	private String message = "操作成功！";

	/**
	 * 返回代码
	 */
	@ApiModelProperty(value = "返回代码")
	private Integer code = 0;

	/**
	 * 返回数据对象 data
	 */
	@ApiModelProperty(value = "返回数据对象")
	private T result;

	/**
	 * 订单号码
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String orderId;

	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private long timestamp = System.currentTimeMillis();

    /**
     * serviceId
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String serviceId;

	/**
	 * 过期时间
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String invalidTime;


    /**
     * channelCode
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String channelCode;
	/**
	 * token
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String token;

	/**
	 * level
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Integer level;


	/**
	 * goldenBean
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Integer goldenBean;
	/**
	 * 影楼用户编号
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String numberId;

	/**
	 * mobile
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String mobile;

	/**
	 * status
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private int status;
	/**
	 * ringUrl
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String ringUrl;

	/**
	 * ringPic
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String ringPic;

	/**
	 * 错误描述
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String errorMsg;

	/**
	 * 错误code
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String returnCode;


	public Result() {

	}

	public Result<T> success(String message) {
		this.message = message;
		this.code = CommonConstant.SC_OK_200;
		this.success = true;
		return this;
	}

	public static Result<Object> ok(Integer code, String msg) {
		Result<Object> r = new Result<>();
		r.setSuccess(true);
		r.setCode(code);
		r.setMessage(msg);
		return r;
	}

	public static Result<Object> ok(String msg) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(true);
		r.setCode(CommonConstant.SC_OK_200);
		r.setMessage(msg);
		return r;
	}

	public static Result<Object> ok(Object data) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(true);
		r.setCode(CommonConstant.SC_OK_200);
		r.setResult(data);
		return r;
	}


    public static Result<Object> okAndSetData(Object data) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_OK_200);
        r.setMessage("成功");
        r.setResult(data);
        return r;
    }



	public static Result<Object> error(String msg) {
		return error(CommonConstant.SC_INTERNAL_SERVER_ERROR_500, msg);
	}

	public static Result<Object> error(int code, String msg) {
		Result<Object> r = new Result<>();
		r.setCode(code);
		r.setMessage(msg);
		r.setSuccess(false);
		return r;
	}

    public static Result<Object> errorAndSetData(Object data) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
        r.setMessage("成功");
        r.setResult(data);
        return r;
    }


	public static Result<Object> error(int code, String msg,Object data) {
		Result<Object> r = new Result<Object>();
		r.setCode(code);
		r.setMessage(msg);
		r.setResult(data);
		return r;
	}

	public Result<T> error500(String message) {
		this.message = message;
		this.code = CommonConstant.SC_INTERNAL_SERVER_ERROR_500;
		this.success = false;
		return this;
	}
	/**
	 * 无权限访问返回结果
	 */
	public static Result<Object> noauth(String msg) {
		return error(CommonConstant.SC_JEECG_NO_AUTH, msg);
	}
	/**
	 * 振铃已发送短信访问返回结果
	 */
	public static Result<Object> noauth(String msg,Object data) {
		Result<Object> error =  error(CommonConstant.SC_JEECG_NO_AUTH, msg);
		error.setResult(data);
		return error;
	}

	/**
	 * 业务已存在
	 */
	public static Result<Object> bizExists(String msg) {
		return error(CommonConstant.SC_JEECG_BIZ_EXISTS, msg);
	}

	/**
	 * 需要二次确认,跳转运营商业务页面去确认(success视作成功,因为success为真的会缓存验证通过的信息)
	 */
	public static Result<Object> bizConfirm(String msg) {
		Result<Object> r = new Result<Object>();
		r.setCode(CommonConstant.SC_JEECG_BIZ_CONFIRM);
		r.setMessage(msg);
		r.setSuccess(true);
		return r;
	}

    /**
     * 移动业务使用咪咕支付
     */
    public static Result<Object> cmccMiguPay(String msg) {
        Result<Object> r = new Result<Object>();
        r.setCode(CommonConstant.SC_JEECG_CMCC_MIGU_PAY);
        r.setMessage(msg);
        r.setSuccess(true);
        return r;
    }

	/**
	 * 无权限访问返回结果
	 */
	public static Result<Object> captchaErr(String msg) {
		return error(CommonConstant.SC_JEECG_CAPTCHA_ERR, msg);
	}

	public static void main(String[] args) throws JsonProcessingException {
		String json = "{\n" + "    \"code\": 500,\n" + "    \"message\": \"success\"\n" + "}";
		final Result result = new ObjectMapper().readValue(json, Result.class);
		System.out.println("result = " + result);
	}

	public boolean isOK() {
		return this.code.equals(CommonConstant.SC_OK_200);
	}


    /**
     * 创建订单
     */
    public static Result<Object> createOrder(String msg,Object result) {
        Result<Object> r = new Result<Object>();
        r.setCode(CommonConstant.SC_JEECG_BIZ_REDIRECT);
        r.setMessage(msg);
        r.setSuccess(true);
        r.setResult(result);
        return r;
    }


    /**
     * 需要确认
     */
    public static Result<Object> confirm(String msg) {
        Result<Object> r = new Result<Object>();
        r.setMessage(msg);
        r.setCode(CommonConstant.TIANYI_CONFIRM);
        r.setSuccess(true);
        return r;
    }

    /**
     * 跳转到运营商页面
     */
    public static Result<Object> redirect(String msg,Object data) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_JEECG_BIZ_REDIRECT);
        r.setMessage(msg);
        r.setResult(data);
        return r;
    }

    /**
     * 跳转到四川移动和生活
     */
    public static Result<Object> scmccRedirect(String msg) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_JEECG_SCMCC_REDIRECT);
        r.setMessage(msg);
        return r;
    }

    /**
     * 移动业务跳转
     */
    public static Result<Object> cmccRedirect(String msg) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_JEECG_CMCC_REDIRECT);
        r.setMessage(msg);
        return r;
    }

    /**
     * 跳转到联通
     */
    public static Result<Object> unicomRedirect(String msg) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_JEECG_UNICOM_REDIRECT);
        r.setMessage(msg);
        return r;
    }

    /**
     * 跳转到电信
     */
    public static Result<Object> telecomRedirect(String msg) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_JEECG_TELECOM_REDIRECT);
        r.setMessage(msg);
        return r;
    }

	public static Result<Object> ok(String msg,Object data) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(true);
		r.setCode(CommonConstant.SC_OK_200);
		r.setMessage(msg);
		r.setResult(data);
		return r;
	}
	public static Result<Object> error(String msg,Object data) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(false);
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(msg);
		r.setResult(data);
		return r;
	}
	public static Result<Object> loop() {
		return error(CommonConstant.LOOP_BJHY_STATE, "继续循环查询");
	}

	public static Result<Object> okObj(Object data) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(true);
		r.setCode(CommonConstant.SC_OK_200);
		r.setResult(data);
		return r;
	}


    public static Result<Object> miguLoginToken(Object data) {
        Result<Object> r = new Result<Object>();
        r.setSuccess(true);
        r.setCode(CommonConstant.SC_JEECG_MIGU_TOKEN);
        r.setResult(data);
        return r;
    }
	/**
	 * 需要登录
	 * @return
	 */
	public static Result<Object> needLogin(Object data) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(false);
		r.setCode(HttpStatus.NETWORK_AUTHENTICATION_REQUIRED.value());
		r.setMessage("请登录");
		r.setResult(data);
		return r;
	}
	public static Result<Object> ok(String msg,Object data,String orderId) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(true);
		r.setCode(CommonConstant.SC_OK_200);
		r.setMessage(msg);
		r.setResult(data);
		r.setOrderId(orderId);
		return r;
    }

    /**
     * 已发送短信访问返回结果
     */
    public static Result<Object> noauth(String msg, Object data, String orderId) {
        Result<Object> error = error(CommonConstant.SC_JEECG_NO_AUTH, msg);
        error.setResult(data);
        error.setOrderId(orderId);
        return error;
    }

	/**
	 * 操作成功
	 */
	public static Result<Object> ok() {
		return OK;
	}

    /**
     * 黑名单返回结果
     */
    public static Result<Object> msgBlackLimit() {
		return ERROR_BLACK_LIMIT;
    }

    /**
     * 省份限制返回结果
     */
    public static Result<Object> msgProvinceLimit() {
		return ERROR_PROVINCE_LIMIT;
    }

    /**
     * 3个月内重复开通返回结果
     */
    public static Result<Object> msgDuplicateLimit() {
		return ERROR_DUPLICATE_LIMIT;
    }

    /**
     * app限制返回结果
     */
    public static Result<Object> msgAppLimit() {
		return ERROR_APP_LIMIT;
    }

    /**
     * 限量返回结果
     */
    public static Result<Object> msgAmountLimit() {
		return ERROR_AMOUNT_LIMIT;
    }


	/**
	 * 运营商限制
	 */
	public static Result<Object> msgIspRestrict() {
		return ERROR_ISP_RESTRICT;
    }

    /**
     * 时间段限制
     */
    public static Result<Object> msgTimeRestrict() {
		return ERROR_TIME_RESTRICT;
    }

    /**
     * 时间段限制
     */
    public static Result<Object> msgSmsDayLimit() {
		return ERROR_SMS_DAY_LIMIT;
    }

    /**
     * ip限制
     */
    public static Result<Object> msgIpLimit() {
		return ERROR_IP_LIMIT;
    }
	/**
	 * 重复开通返回结果
	 */
	public static Result<Object> msgRepeatSub() {
		return ERROR_REPEAT_SUB;
	}

    /**
     * 重复开通返回结果
     */
    public static Result<Object> msgSystemError() {
		return ERROR_SYSTEM_ERROR;
    }
	public static Result<Object> errorSystemMsg(String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_SYSTEM_ERROR);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		return r;
	}

    public static Result<Object> msgSystemNotSupport() {
		return ERROR_SYSTEM_NOT_SUPPORT;
    }


	/**
	 * 获取验证码失败
	 */
	public static Result<Object> msgSmsError() {
		return ERROR_SMS_ERROR;
	}
	public static Result<Object> errorSmsMsg(String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_SMS_ERROR);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		return r;
	}



	/**
	 * 暂无订购资格，请稍后再试!
	 */
	public static Result<Object> msgNotSubError() {
		return ERROR_NOT_SUB_ERROR;
	}
	public static Result<Object> errorNotSubMsg(String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_NOT_SUB);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		return r;
	}
	/**
	 * 获取验证码失败,请稍后再试!
	 */
	public static Result<Object> msgSmsDelayerror() {
		return ERROR_SMS_DELAY_ERROR;
	}
	public static Result<Object> errorSmsDelayMsg(String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_SMS_DELAY_ERROR);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		return r;
	}
	/**
	 * 获取短信验证码失败
	 */
	public static Result<Object> msgSmsCodeError() {
		return ERROR_SMS_CODE_ERROR;
	}

	public static Result<Object> errorSmsCodeMsg(String msg,String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(msg);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		return r;
	}

	/**
	 * 短信发送失败,请稍后再试
	 */
	public static Result<Object> msgSendSmsDelayerror() {
		return ERROR_SEND_SMS_DELAY_ERROR;
	}
	public static Result<Object> errorSendSmsDelayMsg(String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_SEND_SMS_DELAY_ERROR);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		return r;
	}



	public static Result<Object> okMsg(String msg,Object data,String errorMsg) {
		Result<Object> r = new Result<Object>();
		r.setSuccess(true);
		r.setCode(CommonConstant.SC_OK_200);
		r.setMessage(msg);
		r.setResult(data);
		r.setErrorMsg(errorMsg);
		return r;
	}


	public static Result<Object> errorSmsMsgYunnan(Object data,String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_SMS_ERROR);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		r.setResult(data);
		return r;
	}


	public static Result<Object> errorSmsMsgYunnan(Object data) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_SMS_ERROR);
		r.setSuccess(false);
		r.setResult(data);
		return r;
	}







	public static Result<Object> errorDuplicateMsg(String errorMsg) {
		Result<Object> r = new Result<>();
		r.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
		r.setMessage(MSG_DUPLICATE_LIMIT);
		r.setSuccess(false);
		r.setErrorMsg(errorMsg);
		return r;
	}

	public static Result<Object> msgSendSmsTooFrequent() {
		return ERROR_SEND_SMS_TOO_FREQUENT;
	}

	public static Result<Object> msgWxMiniNotSupport() {
		return ERROR_WX_MINI_NOT_SUPPORT_LOGIN;
	}
}
