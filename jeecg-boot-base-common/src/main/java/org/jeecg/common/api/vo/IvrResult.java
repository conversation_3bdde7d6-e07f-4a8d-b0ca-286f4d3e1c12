package org.jeecg.common.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *   接口返回数据格式
 * <AUTHOR>
 * @email <EMAIL>
 * @date  2019年1月19日
 */
@Data
@ApiModel(value="接口返回对象", description="接口返回对象")
public class IvrResult implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 成功标志
	 */
	@ApiModelProperty(value = "成功标志")
	private boolean success = true;

	/**
	 * 返回处理消息
	 */
	@ApiModelProperty(value = "返回处理消息")
	private String message = "操作成功！";

	/**
	 * 返回代码
	 */
	@ApiModelProperty(value = "返回代码")
	private Integer code = 0;


	/**
	 * 支付时间
	 */
	@ApiModelProperty(value = "支付时间")
	private String payTime;


	/**
	 * 订单金额
	 */
	@ApiModelProperty(value = "订单金额")
	private String orderAmount;


	/**
	 * 产品名称
	 */
	@ApiModelProperty(value = "产品名称")
	private String businessName;


	/**
	 * 退款时间
	 */
	@ApiModelProperty(value = "退款时间")
	private String refundTime;


	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private long timestamp = System.currentTimeMillis();

	public static IvrResult response(int code, String msg) {
		IvrResult r = new IvrResult();
		r.setCode(code);
		r.setMessage(msg);
		return r;
	}
	public static IvrResult response(int code, String msg,String payTime, String orderAmount,String businessName,String refundTime) {
		IvrResult r = new IvrResult();
		r.setCode(code);
		r.setMessage(msg);
		r.setPayTime(payTime);
		r.setOrderAmount(orderAmount);
		r.setBusinessName(businessName);
		r.setRefundTime(refundTime);
		return r;
	}
}
