package org.jeecg.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @datetime 2024/12/10 11:10
 */
@Slf4j
@Service
public class RedisDistributeLock {

    @Resource
    private RedissonClient redissonClient;

    public <T> T lock(String lockName, Object businessId, Supplier<T> success) {
        RLock rLock = getLock(lockName, businessId);

        if (rLock == null) {
            return null;
        }
        long startTime = 0L;
        if (log.isDebugEnabled()) {
            startTime = System.currentTimeMillis();
            log.debug("加锁,lockName:{},businessId:{}", lockName, businessId);
        }
        try {
            rLock.lock();
            if (log.isDebugEnabled()) {
                log.debug("等待锁,lockName:{},businessId:{},耗时:{}ms", lockName, businessId,
                        (System.currentTimeMillis() - startTime));
            }
            return success.get();
        } finally {
            rLock.unlockAsync();
            if (log.isDebugEnabled()) {
                log.debug("释放锁,lockName:{},businessId:{},耗时:{}ms", lockName, businessId,
                        (System.currentTimeMillis() - startTime));
            }
        }
    }

    public <T> T tryLock(String lockName, Object businessId, Supplier<T> success, Supplier<T> fail) {
        RLock rLock = getLock(lockName, businessId);

        if (rLock == null || !rLock.tryLock()) {
            return handleSupplier(fail);
        }

        try {
            return handleSupplier(success);
        } finally {
            rLock.forceUnlockAsync();
        }
    }

    private <T> T handleSupplier(Supplier<T> supplier) {
        if (supplier != null) {
            return supplier.get();
        }
        return null;
    }

    @SuppressWarnings("unused")
    public <T> T tryLock(String lockName, Object businessId, Supplier<T> success, Supplier<T> fail,
                         long time, TimeUnit timeUnit) {
        RLock rLock = getLock(lockName, businessId);

        if (rLock == null) {
            return handleSupplier(fail);
        }

        Boolean result = null;

        try {
            result = rLock.tryLock(time, timeUnit);
        } catch (InterruptedException e) {
            log.error("分布式锁中断异常", e);
        }

        if (result != null && result) {
            try {
                return handleSupplier(success);
            } finally {
                rLock.forceUnlockAsync();
            }
        } else {
            return handleSupplier(fail);
        }
    }

    @SuppressWarnings("unused")
    public <T> T lock(Set<String> lockNameSet, Supplier<T> success) {
        RLock rLock = getLock(lockNameSet);

        if (rLock == null) {
            return null;
        }

        try {
            rLock.lock();
            return success.get();
        } finally {
            rLock.unlockAsync();
        }
    }

    @SuppressWarnings("unused")
    public <T> T tryLock(Set<String> lockNameSet, Supplier<T> success, Supplier<T> fail) {
        RLock rLock = getLock(lockNameSet);

        if (rLock == null || !rLock.tryLock()) {
            return handleSupplier(fail);
        }

        try {
            return handleSupplier(success);
        } finally {
            rLock.unlockAsync();
        }
    }

    @SuppressWarnings("unused")
    public <T> T tryLock(Set<String> lockNameSet, Supplier<T> success, Supplier<T> fail,
                         long time, TimeUnit timeUnit) {
        RLock rLock = getLock(lockNameSet);

        if (rLock == null) {
            return handleSupplier(fail);
        }

        Boolean result = null;

        try {
            result = rLock.tryLock(time, timeUnit);
        } catch (InterruptedException e) {
            log.error("分布式锁中断异常", e);
        }

        if (result != null && result) {
            try {
                return handleSupplier(success);
            } finally {
                rLock.unlockAsync();
            }
        } else {
            return handleSupplier(fail);
        }
    }

    private RLock getLock(Set<String> lockNameSet) {
        if (!CollectionUtil.isEmpty(lockNameSet)) {
            if (lockNameSet.size() == 1) {
                Iterator<String> iterator = lockNameSet.iterator();

                if (iterator.hasNext()) {
                    Object item = lockNameSet.iterator().next();

                    if (item != null) {
                        String itemName = item.toString();

                        if (StrUtil.isNotEmpty(itemName)) {
                            return redissonClient.getLock(itemName);
                        }
                    }
                }

            } else if (lockNameSet.size() > 1) {
                RLock[] lockArray = new RLock[lockNameSet.size()];
                int index = 0;

                for (Object item : lockNameSet) {
                    if (item == null) {
                        log.error("分布式锁元素为空");
                        return null;
                    }

                    String itemName = item.toString();

                    if (StrUtil.isEmpty(itemName)) {
                        log.error("分布式锁元素为空");
                        return null;
                    }

                    lockArray[index] = redissonClient.getLock(itemName);
                    index++;
                }

                return redissonClient.getMultiLock(lockArray);
            }
        }

        log.error("分布式锁名称为空");
        return null;
    }

    @SuppressWarnings("all")
    private RLock getLock(String lockName, Object bussinessId) {
        if (StrUtil.isEmpty(lockName)) {
            log.error("分布式锁名称为空");
            return null;
        }

        if (bussinessId != null) {
            if (bussinessId instanceof Set) {
                Set set = (Set) bussinessId;

                if (set.size() == 1) {
                    Iterator<String> iterator = set.iterator();

                    if (iterator.hasNext()) {
                        Object item = set.iterator().next();

                        if (item != null) {
                            lockName += item.toString();
                        }
                    }

                    return redissonClient.getLock(lockName);
                } else if (set.size() > 1) {
                    RLock[] lockArray = new RLock[set.size()];
                    int index = 0;

                    for (Object item : set) {
                        if (item == null || StrUtil.isEmpty(item.toString())) {
                            log.error("分布式锁业务id为空");
                            return null;
                        }

                        lockArray[index] = redissonClient.getLock(lockName + item);
                        index++;
                    }

                    return redissonClient.getMultiLock(lockArray);
                }
            } else {
                return redissonClient.getLock(lockName + bussinessId.toString());
            }
        }

        return redissonClient.getLock(lockName);
    }
}