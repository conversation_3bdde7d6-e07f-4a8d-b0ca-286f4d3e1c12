package org.jeecg.common.util;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * redis 工具类
 *
 * <AUTHOR>
 */
@Component
public class VrbtRedisUtil {

    @Autowired
    @Qualifier("vrbtRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    /**
     * 删除缓存
     *
     * @param keys 匹配的key
     */
    public void del(Set<String> keys) {
        if (keys != null && !keys.isEmpty()) {
            // 删除匹配的键
            redisTemplate.delete(keys);
        }
    }

    // ============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 不存在就放入缓存
     *
     * @param key   键
     * @param value 值
     * @return 如果key存在返回false, 设置成功返回true
     */
    public boolean setIfAbsent(String key, Object value) {
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 不存在就放入缓存,并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return 如果key存在返回false, 设置成功返回true
     */
    public boolean setIfAbsent(String key, Object value, long time) {
        try {
            if (time > 0) {
                return redisTemplate.opsForValue().setIfAbsent(key, value, time, TimeUnit.SECONDS);
            } else {
                return redisTemplate.opsForValue().setIfAbsent(key, value);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return
     */
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    // ================================Map=================================

    /**
     * HashGet
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public Object hget(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        redisTemplate.opsForHash().delete(key, item);
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        return redisTemplate.opsForHash().hasKey(key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return
     */
    public double hincr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, by);
    }

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     * @return
     */
    public double hdecr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, -by);
    }

    // ============================set=============================

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return
     */
    public Set<Object> sGet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public boolean sHasKey(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSet(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0) {
                expire(key, time);
            }
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return
     */
    public long sGetSetSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    // ===============================list=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     * @return
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return
     */
    public long lGetListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     */
    public Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * list left pop
     *
     * @param key 键
     * @return
     */
    public Object lPop(String key) {
        try {
            return redisTemplate.opsForList().leftPop(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    //public boolean lSet(String key, Object value) {
    //	try {
    //		redisTemplate.opsForList().rightPush(key, value);
    //		return true;
    //	} catch (Exception e) {
    //		e.printStackTrace();
    //		return false;
    //	}
    //}

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    //public boolean lSet(String key, Object value, long time) {
    //	try {
    //		redisTemplate.opsForList().rightPush(key, value);
    //		if (time > 0) {
    //			expire(key, time);
    //		}
    //		return true;
    //	} catch (Exception e) {
    //		e.printStackTrace();
    //		return false;
    //	}
    //}

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public boolean lSet(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 限流脚本
     * 调用的时候不超过阈值，则直接返回并执行计算器自加。
     *
     * @return lua脚本
     */
    private String buildLuaScript() {
        //return "local c" +
        //        "\nc = redis.call('get',KEYS[1])" +
        //        "\nif c and tonumber(c) > tonumber(ARGV[1]) then" +
        //        "\nreturn c;" +
        //        "\nend" +
        //        "\nc = redis.call('incr',KEYS[1])" +
        //        "\nif tonumber(c) == 1 then" +
        //        "\nredis.call('expire',KEYS[1],ARGV[2])" +
        //        "\nend" +
        //        "\nreturn c;";

        //local key = "rate.limit:" .. KEYS[1]
        //local limit = tonumber(ARGV[1])
        //local expire_time = ARGV[2]
        //
        //local is_exists = redis.call("EXISTS", key)
        //if is_exists == 1 then
        //    if redis.call("INCR", key) > limit then
        //        return 0
        //    else
        //        return 1
        //    end
        //else
        //    redis.call("SET", key, 1)
        //    redis.call("EXPIRE", key, expire_time)
        //    return 1
        //end


        //local key = KEYS[1]
        //local limit = tonumber(ARGV[1])
        //local expire_time = ARGV[2]
        //local is_exists = redis.call("EXISTS", key)
        //if is_exists == 1 then
        //  return redis.call("INCR", key)
        //else
        //    redis.call("SET", key, 1)
        //    redis.call("EXPIRE", key, expire_time)
        //    return 1
        //end

        return "local key = KEYS[1]\n" +
                "local limit = tonumber(ARGV[1])\n" +
                "local expire_time = ARGV[2]\n" +
                "local is_exists = redis.call(\"EXISTS\", key)\n" +
                "if is_exists == 1 then\n" +
                "  return redis.call(\"INCR\", key)\n" +
                "else\n" +
                "    redis.call(\"SET\", key, 1)\n" +
                "    redis.call(\"EXPIRE\", key, expire_time)\n" +
                "    return 1\n" +
                "end";

    }

    /**
     * redis限流
     *
     * @param key         redis key name
     * @param limitCount  限制值  注意:这个类型只能为int,不能为long,否则执行脚本会报异常,因为脚本内部把这个值转换为了tonumber
     * @param limitPeriod 周期,单位:秒  注意:这个类型只能为int,不能为long,否则执行脚本会报异常
     * @return 未超出限制返回false, 超出限制则会返回true
     */
    public boolean rateLimit(String key, int limitCount, int limitPeriod) {
        String luaScript = buildLuaScript();
        RedisScript<Number> redisScript = new DefaultRedisScript<>(luaScript, Number.class);
        ImmutableList<String> keys = ImmutableList.of(key);
        Number count = redisTemplate.execute(redisScript, keys, limitCount, limitPeriod);
        return count == null || count.intValue() > limitCount;
    }

    /**
     * 判断一个时间段内是否存在某个key(即某个时间段内是否已经做了某件时间)
     *
     * @param existsKey redis key name
     * @param expire    过期时间,单位:秒
     * @return 设置成功表示已过期或者未设置过
     */
    public boolean isExpired(String existsKey, long expire) {
        return setIfAbsent(existsKey, existsKey, expire);
    }

    /**
     * redis限流 脚本参考redis官网 https://developer.redis.com/develop/java/spring/rate-limiting/fixed-window/reactive-lua/
     * 注意:此脚本有bug,已修复fix: redis限流脚本自动延长过期时间bug
     *
     * @param key         redis key name
     * @param limitCount  限制值  注意:这个类型只能为int,不能为long,否则执行脚本会报异常,因为脚本内部把这个值转换为了tonumber
     * @param limitPeriod 周期,单位:秒  注意:这个类型只能为int,不能为long,否则执行脚本会报异常
     * @return 未超出限制返回false, 超出限制则会返回true
     */
    public boolean rateLimitV2(String key, int limitCount, int limitPeriod) {
        //脚本参考redis官网 https://developer.redis.com/develop/java/spring/rate-limiting/fixed-window/reactive-lua/
        String luaScript = "local key = KEYS[1]\n"
                + "local requests = tonumber(redis.call('GET', key) or '-1')\n"
                + "local max_requests = tonumber(ARGV[1])\n"
                + "local expiry = tonumber(ARGV[2])\n"
                + "\n"
                + "if (requests == -1) then\n"
                + "  redis.call('INCR', key)\n"
                + "  redis.call('EXPIRE', key, expiry)\n"
                + "  return false\n"
                + "elseif (requests < max_requests) then\n"
                + "  redis.call('INCR', key)\n"
                + "  return false\n"
                + "else\n"
                + "  return true\n"
                + "end";
        final RedisScript<Boolean> redisScript = RedisScript.of(luaScript, Boolean.class);
        ImmutableList<String> keys = ImmutableList.of(key);

        return Boolean.TRUE.equals(redisTemplate.execute(redisScript, keys, limitCount, limitPeriod));
    }

    /**
     * redis限流(当日是否超出限制) 脚本参考redis官网 https://developer.redis.com/develop/java/spring/rate-limiting/fixed-window/reactive-lua/
     *
     * @param key        redis key name
     * @param limitCount 限制值  注意:这个类型只能为int,不能为long,否则执行脚本会报异常,因为脚本内部把这个值转换为了tonumber
     * @return 未超出限制返回false, 超出限制则会返回true
     */
    public boolean rateLimitToday(String key, int limitCount) {
        final long limitPeriod = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
        return rateLimitV2(key, limitCount, (int) limitPeriod);
    }

    public void addCacheableKey(String key, String matchCacheKey) {
        Set<String> cacheKeys = (Set<String>) get(key);
        if (ObjectUtil.isEmpty(cacheKeys)) {
            cacheKeys = new HashSet<>();
        }
        cacheKeys.add(matchCacheKey);
        set(key, cacheKeys, 60 * 60 * 24);
    }

    public void delCacheableKeys(String cacheKey) {
        Set<String> setKeys = (Set<String>) get(cacheKey);
        if (Objects.nonNull(setKeys)) {
            del(setKeys);
        }
        del(cacheKey);
    }
}
