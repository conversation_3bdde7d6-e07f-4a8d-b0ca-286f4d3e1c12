package org.jeecg.common.util;

import net.ipip.ipdb.City;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * IP地址
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2019年01月14日
 */
public class IPUtils {
	private static Logger logger = LoggerFactory.getLogger(IPUtils.class);
    private static City ipdb;

    static {
        try {
            ipdb = new City(new ClassPathResource("ipdb/ipipfree.ipdb").getInputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * ipip.net的ip归属地查询
     * @param ip
     * @return   返回示例[中国,四川,成都]
     */
    public static String[] ipLocation(String ip){
        if(StringUtils.isNotEmpty(ip)){
            try {
                return ipdb.find(ip,"CN");
            }catch (Exception e) {
                //经常会报不支持ipv6
                //e.printStackTrace();
            }
        }
        return new String[]{"unknown", "unknown", "unknown"};
    }
	/**
	 * 获取IP地址
	 *
	 * 使用Nginx等反向代理软件， 则不能通过request.getRemoteAddr()获取IP地址
	 * 如果使用了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP地址，X-Forwarded-For中第一个非unknown的有效IP字符串，则为真实IP地址
	 */
	public static String getIpAddr(HttpServletRequest request) {
    	String ip = null;
        try {
            ip = request.getHeader("x-forwarded-for");
            if (StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (StringUtils.isEmpty(ip) || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
        } catch (Exception e) {
        	logger.error("IPUtils ERROR ", e);
        }

        //使用代理，则获取第一个IP地址
        if(StringUtils.isEmpty(ip) && ip.length() > 15) {
			if(ip.indexOf(",") > 0) {
				ip = ip.substring(0, ip.indexOf(","));
			}
		}

        return ip;
    }

    private static final List<String> IP_HEADER_NAMES = Arrays.asList(
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
    );

    public static String getPublicIp(HttpServletRequest request) {
        // 优先从代理头中获取客户端IP
        for (String header : IP_HEADER_NAMES) {
            String ipList = request.getHeader(header);
            if (ipList != null && ipList.length() != 0 && !"unknown".equalsIgnoreCase(ipList)) {
                // 多个IP取第一个为公网IP
                String ip = ipList.split(",")[0];
                if (!isPrivateIp(ip)) {
                    return ip;
                }
            }
        }

        // 没有代理头则获取原始客户端IP
        String remoteAddr = request.getRemoteAddr();
        if (!isPrivateIp(remoteAddr)) {
            return remoteAddr;
        }

        return null;
    }

    private static boolean isPrivateIp(String ip) {
        // 检查是否为私有IP地址
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            return true;
        }

        // 常见私有IP网段判断
        if (ip.startsWith("10.") ||
                ip.startsWith("172.") && Integer.parseInt(ip.split("\\.")[1]) >= 16 && Integer.parseInt(ip.split("\\.")[1]) <= 31 ||
                ip.startsWith("192.168.") ||
                ip.startsWith("127.") ||
                ip.startsWith("0:")) {
            return true;
        }

        return false;
    }
    
}
