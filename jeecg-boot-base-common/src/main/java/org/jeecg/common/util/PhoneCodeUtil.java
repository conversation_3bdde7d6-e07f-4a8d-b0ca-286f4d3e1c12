package org.jeecg.common.util;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Author: <EMAIL>
 * Date: 2024/9/14 11:31
 * Desc: 湖南移动插码-手机号加密工具类
 */

public class PhoneCodeUtil {
    public static Pattern pattern = Pattern.compile("^(\\+[0-9]{2,}-?)?1[0-9]{10}$");

    public static boolean isMobile(String mobile) {
        if (null == mobile || mobile.length() < 11)
            return false;
        if (mobile.length() > 11)
            mobile = mobile.substring(mobile.length() - 11);
        return pattern.matcher(mobile).matches();
    }

    public static String encodeMobile(String mobile) {
        try {
            if (isMobile(mobile)) {
                if (mobile.length() > 11)
                    mobile = mobile.substring(mobile.length() - 11);
                List<String> fregList = new ArrayList<>();
                StringBuilder builder = new StringBuilder();
                builder.append(mobile.substring(0, 2));
                builder.append(randomNumber());
                builder.append(randomNumber());
                fregList.add(builder.toString());
                builder.setLength(0);
                builder.append(mobile.substring(2, 5));
                builder.append(randomNumber());
                fregList.add(builder.toString());
                builder.setLength(0);
                builder.append(randomNumber());
                builder.append(mobile.substring(5, 8));
                fregList.add(builder.toString());
                builder.setLength(0);
                builder.append(randomNumber());
                builder.append(mobile.substring(8, 11));
                fregList.add(builder.toString());
                builder.setLength(0);
                for (int i = 0; i < fregList.size(); i++) {
                    builder.append(Integer.parseInt(fregList.get(i), 16) ^ Integer.parseInt(genKey(), 16));
                    if (i != fregList.size() - 1)
                        builder.append("-");
                }
                return builder.toString();
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    public static String decodeMobile(String encryptMobile) {
        if (null == encryptMobile)
            return "";
        String[] frags = encryptMobile.split("-");
        StringBuilder builder = new StringBuilder();
        try {
            if (frags.length > 0) {
                String str1 = Integer.toString(Integer.parseInt(frags[0]) ^ Integer.parseInt(genKey(), 16), 16);
                str1 = preFixZero(str1);
                builder.append(str1.substring(0, 2));
            }
            if (frags.length > 1) {
                String str2 = Integer.toString(Integer.parseInt(frags[1]) ^ Integer.parseInt(genKey(), 16), 16);
                str2 = preFixZero(str2);
                builder.append(str2.substring(0, 3));
            }
            if (frags.length > 2) {
                String str3 = Integer.toString(Integer.parseInt(frags[2]) ^ Integer.parseInt(genKey(), 16), 16);
                str3 = preFixZero(str3);
                builder.append(str3.substring(1));
            }
            if (frags.length > 3) {
                String str4 = Integer.toString(Integer.parseInt(frags[3]) ^ Integer.parseInt(genKey(), 16), 16);
                str4 = preFixZero(str4);
                builder.append(str4.substring(1));
            }
        } catch (Exception e) {
            return null;
        }
        return builder.toString();
    }

    public static String randomNumber() throws NoSuchAlgorithmException {
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        String[] numbers = {
                "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
                "a", "b", "c", "d", "e", "f" };
        int randomKey = Integer.valueOf(random.nextInt(16) + "", 10).intValue();
        return numbers[randomKey];
    }

    public static String randomNumber10() throws NoSuchAlgorithmException {
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        String[] numbers = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" };
        int randomKey = Integer.valueOf(random.nextInt(10) + "", 10).intValue();
        return numbers[randomKey];
    }

    private static String preFixZero(String str) {
        str = "000" + str;
        return str.substring(str.length() - 4);
    }

    private static String genKey() {
        return "1abc";
    }

    public static void main(String[] args) throws Exception {
        String cryptMobile = "";
        System.out.println(decodeMobile("6477-4660-4660-564"));
        System.out.println(Integer.toString(Integer.parseInt("6276") ^ Integer.parseInt(genKey(), 16), 16));
        while (true) {
            StringBuffer mobile = new StringBuffer("1");
            int n = 0;
            while (n < 10) {
                mobile.append(randomNumber10());
                n++;
            }
            cryptMobile = encodeMobile(mobile.toString());
            if (!mobile.toString().equals(decodeMobile(cryptMobile)))
                System.out.println(mobile.toString() + "::" + cryptMobile);
            Thread.sleep(500L);
        }
    }
}
