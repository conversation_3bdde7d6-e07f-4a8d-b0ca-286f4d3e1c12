package org.jeecg.common.util;

import com.alibaba.fastjson.JSON;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

public class HttpUtil {
    /**
     * 获取当前请求
     */
    public static HttpServletRequest getCurrRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    private static final String CURRENT_USER_HEADER = "currentUser";
    /**
     * 获取当前用户
     */
    public static LoginUser getCurrUser() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            try {
                loginUser = JSON.parseObject(URLDecoder.decode(getCurrRequest().getHeader(CURRENT_USER_HEADER), StandardCharsets.UTF_8.name()), LoginUser.class);
            } catch (Exception e) {
                loginUser = new LoginUser();
                loginUser.setUsername("系统默认用户");
                loginUser.setRealname("系统默认用户");
            }

        }
        return loginUser;
    }
}
