package org.jeecg.common.constant;

/**
 * @author: huangxutao
 * @date: 2019-06-14
 * @description: 缓存常量
 */
public interface CacheConstant {

    /**
     * 字典信息缓存
     */
    public static final String SYS_DICT_CACHE = "sys:cache:dict";
    /**
     * 表字典信息缓存
     */
    public static final String SYS_DICT_TABLE_CACHE = "sys:cache:dictTable";

    /**
     * 数据权限配置缓存
     */
    public static final String SYS_DATA_PERMISSIONS_CACHE = "sys:cache:permission:datarules";

    /**
     * 缓存用户信息
     */
    public static final String SYS_USERS_CACHE = "sys:cache:user";

    /**
     * 全部部门信息缓存
     */
    public static final String SYS_DEPARTS_CACHE = "sys:cache:depart:alldata";


    /**
     * 全部部门ids缓存
     */
    public static final String SYS_DEPART_IDS_CACHE = "sys:cache:depart:allids";


    /**
     * 测试缓存key
     */
    public static final String TEST_DEMO_CACHE = "test:demo";

    /**
     * 字典信息缓存
     */
    public static final String SYS_DYNAMICDB_CACHE = "sys:cache:dbconnect:dynamic:";

    /**
     * 渠道信息缓存
     */
    public static final String CMS_CHANNEL_CACHE = "cms:cache:channel";

    /**
     * 渠道信息缓存
     */
    public static final String CMS_CHANNEL_AD_PLATFORM_CACHE = "cms:cache:channelAdPlatform";

    /**
     * 手机归属地缓存
     */
    public static final String CMS_MOBILE_REGION_CACHE = "cms:cache:mobile:region";
    /**
     * 手机归属地缓存
     */
    public static final String CMS_MOBILE_REGION_SQL_CACHE = "cms:cache:mobile:region:sql";
    /**
     * 手机归属地缓存
     */
    public static final String CMS_MOBILE_REGION_CHUANGLAN_CACHE = "cms:cache:mobile:region:chuanglan";

    /**
     * 栏目信息缓存
     */
    public static final String CMS_COLUMN_CACHE = "cms:cache:column";
    /**
     * 订阅包栏目信息缓存
     */
    public static final String CMS_COLUMNDY_CACHE = "cms:cache:columndy";

    /**
     * 歌曲信息缓存
     */
    public static final String CMS_MUSIC_CACHE = "cms:cache:music";

    /**
     * 随机铃音版权id缓存
     */
    public static final String CMS_ORDER_VRBT_CACHE = "cms:cache:orderVrbt";

    /**
     * 咪咕api长期缓存
     */
    public static final String CMS_MIGU_API_CACHE = "cms:cache:migu:api";

    /**
     *   咪咕api短期缓存
     */
    public static final String CMS_MIGU_API_SHORT_TERM_CACHE = "cms:cache:migu:api:st:";

    /**
     * 电信视频彩铃接口缓存
     */
    public static final String CMS_DIANXIN_VRBT_API_CACHE = "cms:cache:dianxin:vrbt:api";

    /**
     * 渠道订阅缓存
     */
    public static final String CMS_SUBSCRIBE_CACHE = "cms:cache:subscribe";

    /**
     * 省份限制缓存
     */
    public static final String CMS_PROVINCE_LIMIT_CACHE = "cms:cache:province:limit";
    /**
     * 业务产品缓存
     */
    public static final String CMS_MIGU_PACK_CACHE = "cms:migu:pack";
    /**
     * 业务产品缓存
     */
    public static final String CMS_BUSINESS_PACK_LIST_CACHE = "cms:business:pack:list";
    /**
     * 权益领取记录缓存
     */
    public static final String CMS_RIGHTS_RECEIVE_LOG_CACHE = "cms:rights:receive:log";
    /**
     * 视频彩铃渠道(开放平台/彩铃中心)切换省份配置缓存
     */
    public static final String CMS_VRBT_PROVINCE_SWITCH_CONFIG_CACHE = "cms:cache:vrbtProvinceSwitchConfig";
    /**
     * 视频彩铃渠道(开放平台/彩铃中心)切换省份配置缓存
     */
    public static final String CMS_VRBT_CHANNEL_PROVINCE_CONFIG_CACHE = "cms:cache:vrbtChannelProvinceConfig";
    /**
     * 天翼空间订单号缓存
     */
    public static final String MOBILE_ORDER_CACHE = "cache:mobile:tianyi:order";
    /**
     * 天翼空间订单url缓存
     */
    public static final String MOBILE_ORDER_URL_CACHE = "cache:mobile:tianyi:order:url";

    /**
     * 天翼空间cookie缓存
     */
    public static final String MOBILE_COOKIE_CACHE = "cache:mobile:cookie";
    /**
     * 天翼空间短信缓存
     */
    public static final String MOBILE_SMS_CACHE = "cache:mobile:tianyi:sms";


    //test mobile redis key
    public static final String TEST_MOBILE_REDIS_KEY = "TEST_MOBILE_LIST";

    //每天自增序列缓存
    public static final String SERIAL_REDIS_KEY = "serial:number";

    //每天自增序列缓存
    public static final String SERIAL_REDIS_JUNBO_KEY = "serial:junbo:number";

    //电信当前渠道号
    public static final String DIANXIN_CURRENT_CHANNEL_KEY = "dianxin_current_channel";

    public static final String DIANXIN_EXISTS_ORDER = "dianxin:exists:order";

    //渠道短信发送
    public static final String SMS_DIANXIN_CHANNEL_KEY = "sms_dianxin_channel";


    //电信渠道省份
    public static final String PROVINCE_DIANXIN_CHANNEL_KEY = "province_dianxin_channel";

    /**
     * 视频彩铃项目权益领取登录接口缓存
     */
    public static final String CMS_CACHE_LOGIN_TOKEN = "cms:cache:login:token:";
    /**
     * 视频彩铃app项目登录接口缓存
     */
    public static final String CMS_CACHE_VRBT_APP_LOGIN_TOKEN = "cms:cache:vrbt:app:login:token:";
    /**
     * 统一登录接口缓存
     */
    public static final String CMS_CACHE_UNIFY_RIGHT_LOGIN_TOKEN = "cms:cache:unify:right:login:token:";


    /**
     * 抖音权益领取测试缓存
     */
    public static final String RIGHT_DOUYIN_TEST = "right:douyin:test";

    /**
     * 话费充值缓存
     */
    public static final String CMS_CACHE_MOBILE_FEE_CHARGE = "cms:cache:mobile:fee:charge";


    /**
     * pptv权益领取测试缓存
     */
    public static final String RIGHT_PPTV_TEST = "right:pptv:test";


    /**
     * 湖南移动视频彩铃开关
     */
    public static final String CMS_BIZ_SWITCH_HNYD = "cms:cache:hnyd";

    /**
     * 湖南移动全网追剧开关
     */
    public static final String CMS_BIZ_SWITCH_HNYD_QWZJ = "cms:cache:hnydqwzj";

    /**
     * 湖南移动芒果包开关
     */
    public static final String CMS_BIZ_SWITCH_HNYD_MGB = "cms:cache:hnydmgb";

    /**
     * 贵州移动业务开关
     */
    public static final String CMS_BIZ_SWITCH_GZYD = "cms:cache:gzyd";

    /**
     * 湖南移动业务开关
     */
    public static final String CMS_BIZ_SWITCH_HLJYD = "cms:cache:hljyd";

    /**
     * 业务开通成功自增序列
     */
    public static final String BIZ_SERIAL_REDIS_KEY = "biz:serial:number";

    /**
     * 渠道省份限制
     */
    public static final String CMS_PROVINCE_CHANNEL_ALLOW = "cms:cache:provinceChannelAllow";
    /**
     * 渠道省份号段限制
     */
    public static final String CMS_PROVINCE_CHANNEL_PHONE_ALLOW = "cms:cache:provinceChannelPhoneAllow";
    /**
     * 渠道省份限量
     */
    public static final String CMS_PROVINCE_CHANNEL_LIMIT = "cms:cache:provinceChannelLimit";

    /**
     * 渠道省份归属限量
     */
    public static final String CMS_PROVINCE_CHANNEL_OWNER_LIMIT = "cms:cache:provinceChannelOwnerLimit";
    /**
     * 渠道省份限量
     */
    public static final String CMS_PROVINCE_CHANNEL_LIMIT_MOBILE = "cms:cache:provinceChannelLimitMobile";

    /**
     * 渠道省份限量
     */
    public static final String CMS_PROVINCE_CHANNEL_LIMIT_CPA_MOBILE = "cms:cache:provinceChannelLimitCpaMobile";

    /**
     * 渠道限量配置
     */
    public static final String CMS_CHANNEL_LIMIT_CONFIG = "cms:cache:channelLimitConifg";

    /**
     * 业务短信模板缓存
     */
    public static final String CMS_SMS_MODEL_CHANNEL_CACHE = "cms:sms:model:channel:cache";
    /**
     * 业务短信模板缓存
     */
    public static final String CMS_SMS_MODEL_CACHE = "cms:sms:model:cache";

    /**
     * 黑龙江视频彩铃业务缓存
     */
    public static final String CMS_CACHE_BIZ_HLJYD_VRBT = "cms:cache:biz:hljyd:vrbt";

    /**
     * 黑龙江视频彩铃抖音快手业务缓存
     */
    public static final String CMS_CACHE_BIZ_HLJYD_DYVRBT = "cms:cache:biz:hljyd:dyvrbt";

    /**
     * 安徽移动验证码数据缓存
     */
    public static final String CMS_CACHE_ANHUI_SMS_DATA = "cms:cache:anhui:sms:data:";
    /**
     * 微信小程序获取openid缓存
     */
    public static final String CMS_CACHE_WECHAT_OPENID = "cms:cache:wechat:openid";
    /**
     * 微信获取access_token缓存
     */
    public static final String CMS_CACHE_WECHAT_OAUTH = "cms:cache:wechat:oauth";

    /**
     * 微信获取userInfo缓存
     */
    public static final String CMS_CACHE_WECHAT_INFO = "cms:cache:wechat:info";

    /**
     * 微信获取token缓存
     */
    public static final String CMS_CACHE_WECHAT_TOKEN = "cms:cache:wechat:token";

    /**
     * 微信获取Scheme缓存
     */
    public static final String CMS_CACHE_WECHAT_SCHEME = "cms:cache:wechat:scheme";


    /**
     * 企业彩铃会员变化状态
     */
    public static final String QYCL_CACHE_MEMBER_STATUS = "qycl:cache:member:";


    /**
     * 企业彩铃铃音变化状态
     */
    public static final String QYCL_CACHE_RING_STATUS = "qycl:cache:ring:";


    /**
     * 企业彩铃公用缓存
     */
    public static final String QYCL_CACHE_COMMON = "qycl:cache:common";

    /**
     * 广东超炫XR数据缓存
     */
    public static final String CMS_CACHE_GUANGDONG_SMS_DATA = "cms:cache:guangdong:sms:data:";

    /**
     * 企业彩铃支付配置
     */
    public static final String QYCL_CACHE_WECHAT_PAY_CACHE = "qycl:cache:wechat:pay:config";
    /**
     * 企业彩铃支付创建订单缓存
     */
    public static final String QYCL_CACHE_WECHAT_PAY_ORDER_CACHE = "qycl:cache:wechat:pay:order";
    /**
     * 企业彩铃支付配置
     */
    public static final String QYCL_CACHE_WECHAT_NOTIFY_CACHE = "qycl:cache:wechat:notify:config";

    /**
     * 企业彩铃支付配置
     */
    public static final String QYCL_CACHE_DY_PAY_CACHE = "qycl:cache:dy:pay:config";
    /**
     * 企业彩铃支付配置
     */
    public static final String QYCL_CACHE_KS_PAY_CACHE = "qycl:cache:ks:pay:config";
    /**
     * 企业彩铃支付配置
     */
    public static final String QYCL_CACHE_DY_TOKEN_CACHE = "qycl:cache:dy:token";
    /**
     * 企业彩铃支付配置
     */
    public static final String QYCL_CACHE_DY_NOTIFY_CACHE = "qycl:cache:dy:notify:config";


    /**
     * 业务限量短信
     */
    public static final String CMS_CACHE_SMS_LIMIT = "cms:sms:limit:";

    /**
     * 业务即将限量短信
     */
    public static final String CMS_CACHE_SMS_VERGE_LIMIT = "cms:sms:verge:limit:";

    /**
     * 业务开通成功自增序列
     */
    public static final String CMS_CACHE_ANHUI_SMS_LIMIT = "cms:anhui:sms:limit";

    /**
     * 视频彩铃切换到四川移动炫视视频娱乐包
     */
    public static final String CMS_VRBT_SWITCH_SCYD = "cms:vrbt:switch:scyd";

    /**
     * 组合包切换到四川移动炫视视频娱乐包
     */
    public static final String CMS_ZHB_SWITCH_SCYD = "cms:zhb:switch:scyd";


    /**
     * 视频彩铃切换到四川移动炫视视频娱乐包
     */
    public static final String CMS_VR_SELECT_SCYD = "cms:vr:select:scyd";

    /**
     * 视频彩铃切换到广东移动
     */
    public static final String CMS_VRBT_SWITCH_GDYD = "cms:vrbt:switch:gdyd";

    /**
     * 视频彩铃切换到广东移动
     */
    public static final String CMS_VRBT_SWITCH_JSYD = "cms:vrbt:switch:jsyd";


    /**
     * 企业彩铃部门信息缓存
     */
    public static final String QYCL_DEPARTMENT_CACHE = "qycl:department:info:";


    /**
     * 企业彩铃部门铃声信息缓存
     */
    public static final String QYCL_DEPARTMENT_RING_CACHE = "qycl:department:ring:info:";

    /**
     * 企业彩铃部门铃声信息缓存
     */
    public static final String QYCL_DEPARTMENT_USEDRING_CACHE = "qycl:department:usedring:info:";

    /**
     * 江苏移动token缓存
     */
    public static final String CMS_JSYD_TOKEN_CACHE = "cms:jsyd:token:";

    /**
     * APP渠道业务黑名单配置缓存
     */
    public static final String CMS_CHANNEL_AD_SITE_BUSINESS_CACHE = "cms:cache:channelAdSiteBusiness";

    /**
     * APP渠道业务外部子渠道号黑名单配置缓存
     */
    public static final String CMS_CHANNEL_AD_SITE_BUSINESS_OS_CACHE = "cms:cache:channelAdSiteBusinessOs";



    /**
     * 业务产品缓存
     */
    public static final String CMS_BUSINESS_SERVICE_NAME_CACHE = "cms:business:service:name";


    /**
     * 业务产品缓存
     */
    public static final String CMS_BUSINESS_RIGHTS_LIST_CACHE = "cms:business:rights:list";


    /**
     * 业务产品缓存
     */
    public static final String CMS_BUSINESS_SERVICE_LIST_CACHE = "cms:business:service:list";


    /**
     * 业务产品缓存
     */
    public static final String CMS_BUSINESS_SERVICE_LIST_VRBT_CACHE = "cms:business:pack:list:vrbt";

    /**
     * 业务产品缓存
     */
    public static final String CMS_BUSINESS_RIGHTS_LIST_VRBT_CACHE= "cms:business:rights:list:vrbt";

    /**
     * 业务产品缓存
     */
    public static final String  CMS_BUSINESS_SERVICE_NAME_VRBT_CACHE= "cms:business:service:name:vrbt";

    /**
     * 阿里支付渠道配置
     */
    public static final String  ALI_CHANNEL_CONFIG_CACHE= "ali:channel:config";

    /**
     * 外部渠道配置缓存
     */
    public static final String CMS_OUTSIDE_CONFIG_CACHE = "cms:cache:outside:config";

    /**
     * 外部渠道配置缓存
     */
    public static final String CMS_OUTSIDE_CONFIG_BUSINESS_CACHE = "cms:cache:outside:config:business";


    /**
     * 阿里支付权益领取渠道配置
     */
    public static final String  ALI_PAY_RIGHTS_CHANNEL_CONFIG_CACHE= "ali:pay:rights:channel:config";

    /**
     * 咪咕互娱获取token
     */
    public static final String CMS_CACHE_KUAIYOU_VR_TOKEN = "cms:cache:KuaiYou:vr:token";

    /**
     * 咪咕互娱获取支付地址
     */
    public static final String CMS_CACHE_KUAIYOU_VR_PAY_URL = "cms:cache:KuaiYou:vr:pay:url";
    /**
     * 咪咕互娱获取用户支付状态
     */
    public static final String CMS_CACHE_KUAIYOU_VR_USER = "cms:cache:KuaiYou:vr:user";
    /**
     * 咪咕互娱获取订购信息
     */
    public static final String CMS_CACHE_HUYU_VR_USER_STATE = "cms:cache:huyu:vr:user:state";


    /**
     * 沃阅读查询骏伯充值记录
     */
    public static final String CMS_CACHE_WOREAD_JUNBO_CHARGE_LIST= "cms:cache:woRead:junbo:Charge:List";

    /**
     * 电信视频彩铃接口缓存
     */
    public static final String CMS_MIND_ORDER_CACHE = "cms:cache:mind:order:";

    /**
     * 公司渠道配置
     */
    public static final String COMPANY_CHANNEL_CONFIG_CACHE = "company:channel:config";
    /**
     * keyValue配置
     */
    public static final String KEY_VALUE_CONFIG_CACHE = "key:value:config";
    /**
     * 权益领取咪咕业务列表
     */
    public static final String CMS_RIGHTS_MIGU_PACK_LIST_CACHE = "cms:rights:migu:pack:list";


    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_TYPE_CHANEL_CACHE = "cms:cache:bizType:channel:";


    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_PAGE_CACHE = "cms:cache:bizPage:";
    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_POP_CACHE = "cms:cache:bizPop:";

    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_POP_SELF_CACHE = "cms:cache:self:bizPop:";

    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_PAGE_CONFIG_LINK_CACHE = "cms:cache:pageConfig:link:";


    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_PAGE_CONFIG_POP_CACHE = "cms:cache:pageConfig:pop:";

    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_PAGE_CONFIG_POP_SELF_CACHE = "cms:cache:self:pageConfig:pop:";


    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_PAGE_CONFIG_CACHE = "cms:cache:bizPage:config:";


    /**
     * 渠道归属
     */
    public static final String CMS_SUBCHANNEL_OWNER_CACHE = "cms:subchannel:owner:";


    /**
     * VR破解切换业务开关
     */
    public static final String CMS_BIZ_SWITCH_VR_CRACK = "cms:cache:vr:crack";

    /**
     * 业务渠道配置缓存
     */
    public static final String CMS_BIZ_PROVINCE_CACHE = "cms:cache:province:";

    /**
     * 视频彩铃抖音小程序首页图片缓存
     */
    public static final String CMS_VRBT_DYXCX_HOME_IMG_CACHE = "cms:cache:vrbt:dyxcx:home:img";
    /**
     * 视频彩铃抖音小程序歌曲缓存
     */
    public static final String CMS_VRBT_DYXCX_MUSIC_CACHE = "cms:cache:vrbt:dyxcx:music";


    /**
     * 视频彩铃抖音小程序人气榜单歌曲缓存
     */
    public static final String CMS_VRBT_DYXCX_MUSIC_HOT_LEVEL_CACHE = "cms:cache:vrbt:dyxcx:music:hot:level";


    /**
     * 抖音获取token
     */
    public static final String CACHE_DOUYIN_TOKEN_CACHE = "cms:cache:douyin:token";

    /**
     * 子渠道号配置缓存
     */
    public static final String SUB_CHANNEL_CONFIG_CACHE = "cms:cache:sub:channel";


    /**
     * 彩讯获取验证码缓存
     */
    public static final String CAIXUN_SMS_CODE_RESULT = "cms:cache:caixun:smsCode:result";


    /**
     * 西藏移动获取token缓存
     */
    public static final String XIZANG_MOBILE_TOKEN_RESULT = "cms:cache:xizang:mobile:token:result";


    /**
     * 企业彩铃小程序所有号码可登录
     */
    public static final String BIZ_QYCL_XCX_LOGIN_ALL = "cms:cache:qycl:xcx:login:all";


    /**
     * 彩讯呼叫秀token缓存
     */
    public static final String CAIXUN_TOKEN_RESULT = "cms:cache:caixun:token:result";

    public static final String GZYD_GETSMS_API_CACHE = "cms:cache:gzyd:getsms:url";

    /**
     * 河北移动token缓存
     */
    public static final String HBYD_TOKEN_CACHE = "cms:cache:hbyd:token:";

    /**
     *  查询业务名称
     */
    public static final String CMS_CACHE_RIGHTS_TITLENAME = "cms:cache:rights:titlename";


    /**
     * 查询业务列表
     */
    public static final String  CMS_CACHE_RIGHTS_LIST = "cms:cache:rights:list";


    /**
     * 查询业务详细
     */
    public static final String  CMS_CACHE_RIGHTS_DETAILS = "cms:cache:rights:details";




    /**
     * 查询业务列表
     */
    public static final String  CMS_CACHE_RIGHTS_LIST_VRVT = "cms:cache:rights:list:vrbt";


    /**
     * 查询业务详细
     */
    public static final String  CMS_CACHE_RIGHTS_DETAILS_VRVT = "cms:cache:rights:details:vrbt";

    /**
     * 渠道号配置
     */
    public static final String CMS_CACHE_CHANNEL_CRACK_CONFIG = "cms:cache:channel:crack:config";

    /**
     * 渠道号配置
     */
    public static final String CMS_CACHE_GDJJ_GOODSID_INFO = "cms:cache:gdjj:goodsId:info";

    /**
     * 渠道号配置
     */
    public static final String CMS_CACHE_GDJJ_SMSSERIALNUM_INFO = "cms:cache:gdjj:smsSerialNum:info";

    /**
     * 业务回传成功序列
     */
    public static final String BIZ_SERIAL_REDIS_CALLBACK_KEY = "biz:serial:callback";


    /**
     * 话费充值token缓存
     */
    public static final String  CMS_CACHE_FEE_RECHARGE_TOKEN = "cms:cache:fee:recharge:token";

    /**
     * AI彩铃公用缓存
     */
    public static final String AI_CACHE_COMMON = "ai:cache:common";

    /**
     * 枣米糖token缓存
     */
    public static final String CMS_CACHE_ZMT_TOKEN = "cms:cache:zmt:token";
    public static final String CMS_CACHE_SZR_TOKEN = "cms:cache:szr:token";

    /**
     * 外部渠道配置缓存
     */
    public static final String CMS_OUTSIDE_NOTIFY_SUCCESS_CACHE = "cms:cache:outside:notify:success";

    /**
     * 江西移动校验并获取验证码缓存
     */
    public static final String CMS_JXYD_GETSMS_CACHE = "cms:jxyd:getsms:";
    /**
     * ai联盟人脸模板缓存
     */
    public static final String CMS_CACHE_AI_UNION_FACE_TEMPLATE = "cms:cache:aiUnion:aiUnionAiFaceTemplate";
    /**
     * ai联盟人脸模板缓存
     */
    public static final String CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE = "cms:cache:kpAiUnion:aiUnionAiFaceTemplate";
    /**
     * ai联盟权益缓存
     */
    public static final String CMS_CACHE_KP_AI_UNION_RIGHT = "cms:cache:kpAiUnion:right";
    /**
     * ai彩铃模板缓存
     */
    public static final String CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE = "cms:cache:aiVrbt:aiVrbtAiColumnTemplate";

    /**
     * ai彩铃模板缓存
     */
    public static final String CMS_CACHE_AI_VRBT_BANNER = "cms:cache:aiVrbt:banner";



    /**
     * 接口参数配置
     */
    public static final String PORT_CACHE_CHANNEL_CRACK_CONFIG = "cms:cache:port:channel:crack:config";



    /**
     * 接口参数配置
     */
    public static final String PORT_CACHE_COMPANYNAME_CRACK_CONFIG = "cms:cache:port:companyname:crack:config";

}
