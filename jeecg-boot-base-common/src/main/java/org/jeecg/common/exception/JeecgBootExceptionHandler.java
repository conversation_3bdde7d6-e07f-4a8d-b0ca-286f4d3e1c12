package org.jeecg.common.exception;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.PersistenceException;
import org.apache.ibatis.executor.BatchExecutorException;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.UnauthorizedException;
import org.jeecg.common.api.vo.Result;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.connection.PoolException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.BatchUpdateException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 异常处理器
 * 
 * <AUTHOR>
 * @Date 2019
 */
@RestControllerAdvice
@Slf4j
public class JeecgBootExceptionHandler/* extends ResponseEntityExceptionHandler */{

	private static final String DUPLICATE_DATA_IN_DB = "数据库中已存在该记录";

	/**
	 * 处理自定义异常
	 */
	@ExceptionHandler(JeecgBootException.class)
	public Result<?> handleRRException(JeecgBootException e,HandlerMethod handlerMethod, WebRequest request){
        String referer = request.getHeader("Referer");
        log.error("message:{}，handlerMethod:{},request description:{},referer:{},request parameterMap:{}", e.getMessage(), handlerMethod,request.getDescription(true),referer,request.getParameterMap(), e);
        return Result.error(e.getMessage());
	}

	@ExceptionHandler(NoHandlerFoundException.class)
	public Result<?> handlerNoFoundException(Exception e) {
		log.error(e.getMessage(), e);
		return Result.error(404, "路径不存在，请检查路径是否正确");
	}

	@ExceptionHandler(DuplicateKeyException.class)
	public Result<?> handleDuplicateKeyException(DuplicateKeyException e){
		log.error(e.getMessage(), e);
		return Result.error(DUPLICATE_DATA_IN_DB);
	}

	@ExceptionHandler({UnauthorizedException.class, AuthorizationException.class})
	public Result<?> handleAuthorizationException(AuthorizationException e){
		log.error(e.getMessage(), e);
		return Result.noauth("没有权限，请联系管理员授权");
	}

	@ExceptionHandler(Exception.class)
	public Result<?> handleException(Exception e, HandlerMethod handlerMethod, WebRequest request){
		log.error("message:{}，handlerMethod:{},request description:{},request parameterMap:{}", e.getMessage(), handlerMethod,request.getDescription(true),request.getParameterMap(), e);
		return Result.error("操作失败，"+e.getMessage());
	}
	
	/**
	 * <AUTHOR>
	 * @param e
	 * @return
	 */
	@ExceptionHandler(HttpRequestMethodNotSupportedException.class)
	public Result<?> HttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e){
		StringBuffer sb = new StringBuffer();
		sb.append("不支持");
		sb.append(e.getMethod());
		sb.append("请求方法");
		//sb.append("支持以下");
		//String [] methods = e.getSupportedMethods();
		//if(methods!=null){
		//	for(String str:methods){
		//		sb.append(str);
		//		sb.append("、");
		//	}
		//}
		log.error(sb.toString(), e);
		//return Result.error("没有权限，请联系管理员授权");
		return Result.error(405,sb.toString());
	}
	
	 /** 
	  * spring默认上传大小100MB 超出大小捕获异常MaxUploadSizeExceededException 
	  */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Result<?> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
    	log.error(e.getMessage(), e);
        return Result.error("文件大小超出10MB限制, 请压缩或降低文件质量! ");
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result<?> handleDataIntegrityViolationException(DataIntegrityViolationException e) {
    	log.error(e.getMessage(), e);
        return Result.error("字段太长,超出数据库字段的长度");
    }

    @ExceptionHandler(PoolException.class)
    public Result<?> handlePoolException(PoolException e) {
    	log.error(e.getMessage(), e);
        return Result.error("Redis 连接异常!");
    }


    @ExceptionHandler(ConstraintViolationException.class)
    public Result handle(ConstraintViolationException exception) {
        List<String> collect = exception.getConstraintViolations()
                .stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.toList());
        return Result.error(collect.get(0));
    }

	/**
	 * 处理参数校验异常
	 *
	 * @param e MethodArgumentNotValidException
	 * @return Result
	 */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
		log.error(e.getMessage(), e);
		List<ObjectError> allErrors = e.getBindingResult().getAllErrors();

		StringBuilder sb = new StringBuilder("【");
		int size = allErrors.size();
		if (size == 1) {
			sb.append(allErrors.get(0).getDefaultMessage());
		} else if (size > 1) {
			int maxIndex = size - 1;
			for (int i = 0; i < size; i++) {
				sb.append(i + 1).append(".").append(allErrors.get(i).getDefaultMessage());
				if (i < maxIndex) {
					sb.append(", ");
				}
			}
		}
		sb.append("】");
		return Result.error(sb.toString());
	}

	/**
	 * 处理持久化异常
	 *
	 * @param e PersistenceException
	 * @return Result
	 */
	@ExceptionHandler(PersistenceException.class)
	public Result handlePersistenceException(PersistenceException e) {
		log.error(e.getMessage(), e);
		Throwable cause0 = e.getCause();
		if (cause0 instanceof BatchExecutorException) {
			Throwable cause1 = cause0.getCause();
			if (cause1 instanceof BatchUpdateException) {
				String message = cause1.getMessage();
				if (message.length() > 8 && "Duplicate".equals(message.substring(0, 9))) {
					return Result.error(DUPLICATE_DATA_IN_DB);
				}
			}
		}
		return Result.error(e.getMessage());
	}
}
