package org.jeecg.common.system.query;

import com.google.common.collect.Lists;
import javassist.expr.NewArray;
import org.jeecg.common.util.oConvertUtils;

import java.util.List;

/**
 * 栏目分类常量
 */
public enum ColumnClassEnum {
    QUDAOBAO("QUDA<PERSON><PERSON>O","渠道包"),
    DINGYUEBAO("DINGYUE<PERSON>O","订阅包"),
    DIANXIN("DIANXIN","电信"),
    LIANTONG("LIANTONG","联通");

    private String columnClass;

    private String columnClassName;
    ColumnClassEnum(String columnClass, String columnClassName){
        this.columnClass = columnClass;
        this.columnClassName = columnClassName;
    }

    public String getColumnClass() {
        return columnClass;
    }

    public void setColumnClass(String columnClass) {
        this.columnClass = columnClass;
    }

    public String getColumnClassName() {
        return columnClassName;
    }

    public void setColumnClassName(String columnClassName) {
        this.columnClassName = columnClassName;
    }

    public static List<ColumnClassEnum> getColumnClassList(){
        List<ColumnClassEnum> list= Lists.newArrayList();
        list.add(ColumnClassEnum.DIANXIN);
        list.add(ColumnClassEnum.DINGYUEBAO);
        list.add(ColumnClassEnum.LIANTONG);
        list.add(ColumnClassEnum.QUDAOBAO);
        return list;
    }
}
