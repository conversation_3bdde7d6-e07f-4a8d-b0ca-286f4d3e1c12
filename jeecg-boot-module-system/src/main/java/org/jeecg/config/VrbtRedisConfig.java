package org.jeecg.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

@Configuration
@EnableCaching // 开启缓存支持
public class VrbtRedisConfig {
    // VRBT Redis 配置
    @Bean(name = "vrbtRedisProperties")
    @ConfigurationProperties(prefix = "vrbt-redis")
    public RedisProperties vrbtRedisProperties() {
        return new RedisProperties();
    }

    @Bean(name = "vrbtConnectionFactory")
    public RedisConnectionFactory vrbtConnectionFactory(
            @Qualifier("vrbtRedisProperties") RedisProperties redisProperties) {
        return createLettuceConnectionFactory(redisProperties);
    }

    @Bean(name = "vrbtRedisTemplate")
    public RedisTemplate<String, Object> vrbtRedisTemplate(
            @Qualifier("vrbtConnectionFactory") RedisConnectionFactory connectionFactory) {
        return createRedisTemplate(connectionFactory);
    }

    // 创建 RedisTemplate 实例
    private RedisTemplate<String, Object> createRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 设置序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();

        template.setKeySerializer(stringSerializer);
        template.setValueSerializer(jsonSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setHashValueSerializer(jsonSerializer);

        template.afterPropertiesSet();
        return template;
    }

    // 创建 Lettuce 连接工厂
    private RedisConnectionFactory createLettuceConnectionFactory(RedisProperties properties) {
        // 配置 Redis 服务器信息
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(properties.getHost());
        config.setPort(properties.getPort());
        config.setDatabase(properties.getDatabase());
        if (properties.getPassword() != null && !properties.getPassword().isEmpty()) {
            config.setPassword(properties.getPassword());
        }

        // 配置连接池
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder =
                LettucePoolingClientConfiguration.builder();

        if (properties.getLettuce() != null && properties.getLettuce().getPool() != null) {
            RedisProperties.Pool pool = properties.getLettuce().getPool();
            builder.poolConfig(new org.apache.commons.pool2.impl.GenericObjectPoolConfig<Object>() {{
                setMaxTotal(pool.getMaxActive());
                setMaxIdle(pool.getMaxIdle());
                setMinIdle(pool.getMinIdle());
                setMaxWaitMillis(pool.getMaxWait().toMillis());
            }});
        }

        if (properties.getLettuce() != null && properties.getLettuce().getShutdownTimeout() != null) {
            builder.shutdownTimeout(properties.getLettuce().getShutdownTimeout());
        }

        LettuceClientConfiguration clientConfig = builder.build();
        return new LettuceConnectionFactory(config, clientConfig);
    }

    // Redis 配置属性类
    @Data
    public static class RedisProperties {
        private int database;
        private String host;
        private int port;
        private String password;
        private Lettuce lettuce;

        // 内部类：Lettuce 配置
        @Data
        public static class Lettuce {
            private Pool pool;
            private Duration shutdownTimeout;
        }

        // 内部类：连接池配置
        @Data
        public static class Pool {
            private int maxActive;
            private int maxIdle;
            private int minIdle;
            private Duration maxWait;
        }
    }


}
