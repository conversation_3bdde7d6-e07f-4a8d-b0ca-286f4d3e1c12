package org.jeecg.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.*;

/**
 * Author: <EMAIL>
 * Date: 2023/5/22 13:25
 * Desc: spring定时任务线程池配置,解决默认单线程只有一个任务能跑的问题
 */


//启用定时任务
@Configuration
@EnableScheduling
public class ScheduleConfig implements SchedulingConfigurer {

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.setScheduler(getExecutor());
    }


    /**
     * 获取线程池
     *
     * @return 返回队列
     */
    protected ScheduledExecutorService getExecutor() {
        
        int corePoolSize = 10;
        int maxPoolSize = 30;
        long keepAliveSeconds = 30;

        //最小线程数
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(corePoolSize);
        //最大线程数
        executor.setMaximumPoolSize(maxPoolSize);
        //允许空闲时间(秒)
        executor.setKeepAliveTime(keepAliveSeconds, TimeUnit.SECONDS);
        //线程名称
        executor.setThreadFactory(new ThreadFactoryBuilder().setNameFormat("schedule-pool-%d").build());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        return executor;
    }

}