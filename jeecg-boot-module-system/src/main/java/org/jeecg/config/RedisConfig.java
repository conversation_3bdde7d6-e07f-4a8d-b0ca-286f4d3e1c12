package org.jeecg.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper.DefaultTyping;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.*;

import java.time.Duration;

import static java.util.Collections.singletonMap;

@Configuration
@EnableCaching // 开启缓存支持
public class RedisConfig extends CachingConfigurerSupport {

//	/**
//	 * @description 自定义的缓存key的生成策略 若想使用这个key
//	 *              只需要讲注解上keyGenerator的值设置为keyGenerator即可</br>
//	 * @return 自定义策略生成的key
//	 */
//	@Override
//	@Bean
//	public KeyGenerator keyGenerator() {
//		return new KeyGenerator() {
//			@Override
//			public Object generate(Object target, Method method, Object... params) {
//				StringBuilder sb = new StringBuilder();
//				sb.append(target.getClass().getName());
//				sb.append(method.getDeclaringClass().getName());
//				Arrays.stream(params).map(Object::toString).forEach(sb::append);
//				return sb.toString();
//			}
//		};
//	}

	/**
	 * RedisTemplate配置
	 *
	 * @param redisConnectionFactory
	 * @return
	 */
	@Bean
	public RedisTemplate<String, Object> redisTemplate(@Qualifier("redisConnectionFactory") RedisConnectionFactory redisConnectionFactory) {
		// 设置序列化
		Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<Object>(Object.class);
		ObjectMapper om = new ObjectMapper();
		om.setVisibility(PropertyAccessor.ALL, Visibility.ANY);
		om.enableDefaultTyping(DefaultTyping.NON_FINAL);
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        jackson2JsonRedisSerializer.setObjectMapper(om);
		// 配置redisTemplate
		RedisTemplate<String, Object> redisTemplate = new RedisTemplate<String, Object>();
		redisTemplate.setConnectionFactory(redisConnectionFactory);
		RedisSerializer<?> stringSerializer = new StringRedisSerializer();
		redisTemplate.setKeySerializer(stringSerializer);// key序列化
		redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);// value序列化
		redisTemplate.setHashKeySerializer(stringSerializer);// Hash key序列化
		redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);// Hash value序列化
		redisTemplate.afterPropertiesSet();
		return redisTemplate;
	}

	@Value("${spring.redis.host}")
	private String host;
	@Value("${spring.redis.port}")
	private int port;
	@Value("${spring.redis.password}")
	private String password;
	@Value("${spring.redis.database}")
	private int database;
	@Value("${spring.redis.lettuce.pool.max-active}")
	private int maxActive;
	@Value("${spring.redis.lettuce.pool.max-idle}")
	private int maxIdle;
	@Value("${spring.redis.lettuce.pool.min-idle}")
	private int minIdle;
	@Value("${spring.redis.lettuce.pool.max-wait}")
	private int maxWait;
	@Value("${spring.redis.lettuce.shutdown-timeout}")
	private String poolShutdownTimeout;


	@Bean("redisConnectionFactory")
	public RedisConnectionFactory redisConnectionFactory() {
		RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
		config.setHostName(host);
		config.setPort(port);
		config.setPassword(password);
		config.setDatabase(database);
		// 配置连接池
		LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder =
				LettucePoolingClientConfiguration.builder();

		builder.poolConfig(new org.apache.commons.pool2.impl.GenericObjectPoolConfig<Object>() {{
			setMaxTotal(maxActive);
			setMaxWaitMillis(maxWait);
			setMaxIdle(maxIdle);
			setMinIdle(minIdle);
		}});
//		builder.shutdownTimeout(Duration.parse(poolShutdownTimeout));

		LettuceClientConfiguration clientConfig = builder.build();
		return new LettuceConnectionFactory(config, clientConfig);
	}
	/**
	 * 缓存配置管理器
	 *
	 * @param redisConnectionFactory
	 * @return
	 */
	@Bean
	public CacheManager cacheManager(@Qualifier("redisConnectionFactory") RedisConnectionFactory redisConnectionFactory) {

        // 配置序列化（缓存默认有效期 6小时）
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofHours(6));
        RedisCacheConfiguration redisCacheConfiguration = config.serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                												.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));

		// 以锁写入的方式创建RedisCacheWriter对象
		//RedisCacheWriter writer = RedisCacheWriter.lockingRedisCacheWriter(factory);
		// 创建默认缓存配置对象
		/* 默认配置，设置缓存有效期 1小时*/
		//RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofHours(1));
		/* 自定义配置test:demo 的超时时间为 5分钟*/
		RedisCacheManager cacheManager = RedisCacheManager.builder(RedisCacheWriter.lockingRedisCacheWriter(redisConnectionFactory)).cacheDefaults(redisCacheConfiguration)
				.withInitialCacheConfigurations(singletonMap(CacheConstant.TEST_DEMO_CACHE, RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofMinutes(5)).disableCachingNullValues()))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MOBILE_REGION_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MOBILE_REGION_SQL_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MOBILE_REGION_CHUANGLAN_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CHANNEL_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(2))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CHANNEL_AD_PLATFORM_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(2))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_COLUMN_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_COLUMNDY_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MUSIC_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(10))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_ORDER_VRBT_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(60))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_SUBSCRIBE_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(10))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MIGU_API_CACHE, redisCacheConfiguration.entryTtl(Duration.ofDays(1L))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MIGU_API_SHORT_TERM_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5L))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_DIANXIN_VRBT_API_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(10))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_PROVINCE_LIMIT_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MIGU_PACK_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BUSINESS_PACK_LIST_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_RIGHTS_RECEIVE_LOG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_VRBT_PROVINCE_SWITCH_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_VRBT_CHANNEL_PROVINCE_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_SMS_MODEL_CHANNEL_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(10))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_SMS_MODEL_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(10))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_PROVINCE_CHANNEL_LIMIT, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_PROVINCE_CHANNEL_ALLOW, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_PROVINCE_CHANNEL_LIMIT_MOBILE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_PROVINCE_CHANNEL_LIMIT_CPA_MOBILE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_PROVINCE_CHANNEL_OWNER_LIMIT, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CHANNEL_LIMIT_CONFIG, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_WECHAT_OAUTH, redisCacheConfiguration.entryTtl(Duration.ofMinutes(600))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_WECHAT_INFO, redisCacheConfiguration.entryTtl(Duration.ofMinutes(600))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_WECHAT_OPENID, redisCacheConfiguration.entryTtl(Duration.ofMinutes(600))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_WECHAT_TOKEN, redisCacheConfiguration.entryTtl(Duration.ofMinutes(110))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_WECHAT_SCHEME, redisCacheConfiguration.entryTtl(Duration.ofDays(7L))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_WECHAT_PAY_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_WECHAT_NOTIFY_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_DY_PAY_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_DY_TOKEN_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(110))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_DY_NOTIFY_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_WECHAT_PAY_ORDER_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(1))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_DEPARTMENT_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_DEPARTMENT_RING_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_DEPARTMENT_USEDRING_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_COMMON, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_JSYD_TOKEN_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(30))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CHANNEL_AD_SITE_BUSINESS_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CHANNEL_AD_SITE_BUSINESS_OS_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BUSINESS_SERVICE_NAME_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(72))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BUSINESS_RIGHTS_LIST_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(72))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BUSINESS_SERVICE_LIST_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(72))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BUSINESS_SERVICE_LIST_VRBT_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(72))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BUSINESS_RIGHTS_LIST_VRBT_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(72))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BUSINESS_SERVICE_NAME_VRBT_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(72))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.ALI_CHANNEL_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.COMPANY_CHANNEL_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.KEY_VALUE_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_OUTSIDE_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_OUTSIDE_CONFIG_BUSINESS_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.ALI_PAY_RIGHTS_CHANNEL_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_KUAIYOU_VR_TOKEN, redisCacheConfiguration.entryTtl(Duration.ofSeconds(10))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_KUAIYOU_VR_PAY_URL, redisCacheConfiguration.entryTtl(Duration.ofSeconds(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_KUAIYOU_VR_USER, redisCacheConfiguration.entryTtl(Duration.ofSeconds(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_HUYU_VR_USER_STATE, redisCacheConfiguration.entryTtl(Duration.ofSeconds(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_WOREAD_JUNBO_CHARGE_LIST, redisCacheConfiguration.entryTtl(Duration.ofMinutes(1))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_MIND_ORDER_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(30))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.QYCL_CACHE_KS_PAY_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_RIGHTS_MIGU_PACK_LIST_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_TYPE_CHANEL_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_PAGE_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_POP_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_POP_SELF_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_PAGE_CONFIG_LINK_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_PAGE_CONFIG_POP_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_PAGE_CONFIG_POP_SELF_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_PAGE_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_SUBCHANNEL_OWNER_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(5))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_BIZ_PROVINCE_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_VRBT_DYXCX_HOME_IMG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_VRBT_DYXCX_MUSIC_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_VRBT_DYXCX_MUSIC_HOT_LEVEL_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.CACHE_DOUYIN_TOKEN_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(110))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.SUB_CHANNEL_CONFIG_CACHE, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.GZYD_GETSMS_API_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(3))))
                .withInitialCacheConfigurations(singletonMap(CacheConstant.HBYD_TOKEN_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(45))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_RIGHTS_TITLENAME, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_RIGHTS_LIST, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_RIGHTS_DETAILS, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_RIGHTS_LIST_VRVT, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_RIGHTS_DETAILS_VRVT, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
            .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_CHANNEL_CRACK_CONFIG, redisCacheConfiguration.entryTtl(Duration.ofMinutes(30))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_FEE_RECHARGE_TOKEN, redisCacheConfiguration.entryTtl(Duration.ofHours(5))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_SZR_TOKEN, redisCacheConfiguration.entryTtl(Duration.ofHours(1))))
            .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_CACHE_ZMT_TOKEN, redisCacheConfiguration.entryTtl(Duration.ofHours(24))))
            .withInitialCacheConfigurations(singletonMap(CacheConstant.CMS_JXYD_GETSMS_CACHE, redisCacheConfiguration.entryTtl(Duration.ofMinutes(3))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.PORT_CACHE_CHANNEL_CRACK_CONFIG, redisCacheConfiguration.entryTtl(Duration.ofMinutes(30))))
				.withInitialCacheConfigurations(singletonMap(CacheConstant.PORT_CACHE_COMPANYNAME_CRACK_CONFIG, redisCacheConfiguration.entryTtl(Duration.ofMinutes(30))))
                .transactionAware().build();
		return cacheManager;
	}
}
