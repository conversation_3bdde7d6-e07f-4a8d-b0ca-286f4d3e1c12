package org.jeecg.config;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;

import java.util.Arrays;

/**
 * Author: <EMAIL>
 * Date: 2021/2/23 14:41
 * Desc: redisson配置
 */
@SpringBootConfiguration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String redisHost;
    @Value("${spring.redis.port}")
    private String redisPort;
    @Value("${spring.redis.password}")
    private String redisPwd;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        if(StringUtils.isNotBlank(redisPwd)){
            config.useSingleServer().setAddress("redis://" + redisHost + ":" + redisPort).setPassword(redisPwd);
        }else {
            config.useSingleServer().setAddress("redis://" + redisHost + ":" + redisPort);
        }

        return Redisson.create(config);
    }
}
