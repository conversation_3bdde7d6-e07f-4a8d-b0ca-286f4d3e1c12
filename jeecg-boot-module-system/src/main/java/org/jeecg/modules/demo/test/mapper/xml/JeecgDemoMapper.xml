<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.test.mapper.JeecgDemoMapper">

	<!-- 根据用户名查询 -->
	<select id="getDemoByName" resultType="org.jeecg.modules.demo.test.entity.JeecgDemo">
		select * from  demo  where name = #{name}
	</select>
	
	<!-- 根据权限sql查询数据集 -->
	<select id="queryListWithPermission" parameterType="Object" resultType="org.jeecg.modules.demo.test.entity.JeecgDemo">
		select * from demo where 1=1 ${permissionSql}
	</select>
</mapper>