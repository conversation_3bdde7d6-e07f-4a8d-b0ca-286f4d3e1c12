package org.jeecg.modules.system.aspect;

import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 切面处理类，微信登录拦截
 */
@Slf4j
@Aspect
@Component
public class RightsLoginAspect {
    @Autowired
    private RedisUtil redisUtil;

    //拦截所有被注解RightsLogin标注的方法
    @Pointcut("@annotation(org.jeecg.common.aspect.annotation.RightsLogin)")
    public void pointcut() {
    }

    /**
     * 环绕增强，验证权限
     *
     * @param joinPoint 目标对象
     */
    @Around("pointcut()")
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        //先拿到Request请求体
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String url = request.getRequestURL().toString();
        String method = request.getMethod();
        String uri = request.getRequestURI();

        String params = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        log.info("请求开始, 各个参数, url: {}, method: {}, uri: {}, params: {}", url, method, uri, params);

        final String token = request.getHeader("token");
        String key = CacheConstant.CMS_CACHE_LOGIN_TOKEN;
        Map<Object, Object> entries = redisUtil.hmget(key);
        if(entries.values().contains(token)){
            return joinPoint.proceed();
        }
        final String mobile = request.getHeader("mobile");
        if (StringUtils.isNotBlank(mobile) && mobile.matches(BizConstant.MOBILE_REG)) {
            return joinPoint.proceed();
        }
        return new FebsResponse().needLogin().message("请登录");
    }

}
