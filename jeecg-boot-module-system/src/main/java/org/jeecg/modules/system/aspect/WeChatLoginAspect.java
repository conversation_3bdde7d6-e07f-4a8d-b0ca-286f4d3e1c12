package org.jeecg.modules.system.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 切面处理类，微信登录拦截
 */
@Slf4j
@Aspect
@Component
public class WeChatLoginAspect{
    @Autowired
    private RedisUtil redisUtil;

    //拦截所有被注解WeChatLogin标注的方法
    @Pointcut("@annotation(org.jeecg.common.aspect.annotation.WeChatLogin)")
    public void pointcut() {
    }
    /**
     *  环绕增强，验证权限
     * @param joinPoint 目标对象
     */
    @Around("pointcut()")
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        //先拿到Request请求体
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        final String token = request.getHeader("token");
        if (Strings.isEmpty(token)||!redisUtil.hasKey(token)){
            return Result.error(CommonConstant.SC_JEECG_CAPTCHA_ERR,"请登录");
        }
        return joinPoint.proceed();
    }
}
