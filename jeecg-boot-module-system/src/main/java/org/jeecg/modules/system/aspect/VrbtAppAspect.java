package org.jeecg.modules.system.aspect;



import com.eleven.cms.annotation.IvrAlipayUnsubscribeLimit;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.VrbtAppLogin;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 切面处理类 视频彩铃app登录拦截
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/15 15:08
 **/
@Slf4j
@Aspect
@Component
public class VrbtAppAspect {
    @Autowired
    private RedisUtil redisUtil;
    //拦截所有被注解VrbtAppLogin标注的方法
    @Pointcut("@annotation(org.jeecg.common.aspect.annotation.VrbtAppLogin)")
    public void pointcut() {
    }
    /**
     * 环绕增强，验证权限
     *
     * @param joinPoint 目标对象
     */
    @Around("pointcut()")
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        String methodName = signature.getName();
        VrbtAppLogin vrbtAppLogin = ((MethodSignature)signature).getMethod().getAnnotation(VrbtAppLogin.class);
        //先拿到Request请求体
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String mobile = request.getParameter("mobile");
        Map<String, String[]> parameter=request.getParameterMap();
        log.info(vrbtAppLogin.methodDesc()+"--->方法名:{},手机号:{},请求参数:{}",methodName,mobile,parameter);
        if (StringUtils.isBlank(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.needLogin(false);
        }
        String key = CacheConstant.CMS_CACHE_VRBT_APP_LOGIN_TOKEN+mobile;
        if(!redisUtil.hasKey(key)){
            return Result.needLogin(false);
        }
        return joinPoint.proceed();
    }
}
