<#assign base=springMacroRequestContext.getContextUrl("")>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <link rel="stylesheet" href="${base}/bigscreen/template2/js/layui/css/layui.css" type="text/css">
    <title>域名配置</title>
</head>
<body>
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
    <legend>域名配置文件上传</legend>
</fieldset>

<div class="layui-fluid">
    <div class="layui-form-item">
        <button type="button" class="layui-btn" id="uploadZip">
            <i class="layui-icon">&#xe67c;</i>上传域名压缩包
        </button>

    </div>
</div>
<script type="text/javascript" src="${base}/bigscreen/template2/js/jquery-3.3.1.min.js"></script>
<script type="text/javascript" src="${base}/bigscreen/template2/js/layui/layui.js"></script>

<script>
    layui.use(['upload'], function () {

        var upload = layui.upload;
        //执行实例
        var uploadInst = upload.render({
            elem: '#uploadZip' //绑定元素
            , url: "${base}/api/domainConfig" //上传接口
            , accept: 'file'
            , exts: 'zip' //只允许上传压缩文件
            , size: 1024*5 //限制文件大小，单位 KB
            , done: function (res) {
                layer.msg(res.message);
            }
            , error: function () {
                layer.msg("系统错误", {icon: 2, time: 2000});
            }
        });
    });
</script>
</body>
</html>