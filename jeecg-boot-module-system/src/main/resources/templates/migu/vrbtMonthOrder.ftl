<#assign base=springMacroRequestContext.getContextUrl("")>
<!DOCTYPE html>
<html lang="utf-8">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <script src="https://hz.migu.cn/order/pub-ui/js/and/migus.js"></script>
    <script src="https://hz.migu.cn/order/pub-ui/js/and/openMusicSDK.js"></script>
    <script src="${base}/generic/migu/migu-sdk.js"></script>
    <script src="${base}/generic/migu/xtiper.min.js"></script>
    <link rel="stylesheet" href="${base}/generic/migu/css/xtiper.css">
    <title>视频彩铃包月开通</title>
</head>
<body>

</body>
<script>
    //默认渠道号使用订阅包的渠道号
    // var ch_dyb_default = "00210K7";
    //渠道包-渠道包
    // var ch_qdb = "00210NX";
    var callbackUrl = '${callbackUrl!}';
    var channelCode = '${vrbtCombinResult.channelCode!}';
    var serviceId = '${vrbtCombinResult.serviceId!}';
    var token = '${vrbtCombinResult.token!}';
    var resCode =  '${vrbtCombinResult.resCode!}';
    var monStatus = '${vrbtCombinResult.monthStatus!}';
    var funStatus = '${vrbtCombinResult.funStatus!}';
    function tipAndcallback(tipMsg,callbackLocation){
        xtip.msg(tipMsg);
        if (callbackLocation&&callbackLocation.indexOf('http')>-1){
            setTimeout(function(){window.location.href = callbackLocation}, 1500);
        }
    }
    if(resCode === '000000'){
        //0=未包月
        if(monStatus === '0'){
            miguSdk.init(channelCode);
            miguSdk.config(channelCode, serviceId, token);
            //订阅侧开通
            if(serviceId==='698039035100000014'){
                //1=无视频彩铃功能,走购物车
                if (funStatus === '1') {
                    miguSdk.exec("vrbt_openAndMonthDYB").then(resp => {
                        tipAndcallback('视频彩铃包月业务开通成功',callbackUrl);
                    }).catch(resp => {
                        tipAndcallback('视频彩铃包月业务开通失败',callbackUrl);
                    });
                }else {
                    miguSdk.exec("queryStrategyBYB", {count: "1", type: "16"}).then(resp => {
                        const {bizCode, cpId, cpparam, salePrice} = resp.bizInfoMon;
                        miguSdk.exec("miguOrderSubVrbtDYB", {bizCode, cpId, cpparam, salePrice}).then(resp => {
                            tipAndcallback('视频彩铃包月业务开通成功',callbackUrl);
                        }).catch(resp => {
                            tipAndcallback('视频彩铃包月业务开通失败',callbackUrl);
                        })
                    }).catch(resp => {
                        tipAndcallback('视频彩铃包月业务开通失败',callbackUrl);
                    })
                }
            //渠道侧开通
            }else {
                //1=无视频彩铃功能,提示拨打电话开通
                if (funStatus === '1') {
                    tipAndcallback('请拨打12530开启视频彩铃功能再重新开通',callbackUrl);
                } else { <#--ow 渠道视频包订购接口(先查包月策略)-->
                    miguSdk.exec("queryStrategyBYB", {count: "1", type: "16"}).then(resp => {
                        const {bizCode, cpId, cpparam, salePrice} = resp.bizInfoMon;
                        miguSdk.exec("miguOrderSubVrbtQDB", {bizCode, cpId, cpparam, salePrice}).then(resp => {
                            tipAndcallback('视频彩铃包月业务开通成功',callbackUrl);
                        }).catch(resp => {
                            tipAndcallback('视频彩铃包月业务开通失败',callbackUrl);
                        })
                    }).catch(resp => {
                        tipAndcallback('视频彩铃包月业务开通失败',callbackUrl);
                    })
                }
            }
        }else {
            tipAndcallback('您已开通视频彩铃包月',callbackUrl);
        }
    }else {
        tipAndcallback('${vrbtCombinResult.resMsg!}',callbackUrl);
    }

</script>
</html>