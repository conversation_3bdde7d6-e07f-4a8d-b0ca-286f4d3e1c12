<#assign base=springMacroRequestContext.getContextUrl("")>
<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <title>天翼空间20元包月退订</title>
  <script src="${base}/generic/js/vue-2.6.14.min.js"></script>
  <script src="${base}/generic/js/axios.min.js"></script>
    <link rel="stylesheet" href="${base}/generic/telecom/css/reset.css">
    <link rel="stylesheet" href="${base}/generic/telecom/css/index.css">
</head>

<body>
  <div id="main">
    <div class="headerNav">
      <div class="headerLogo">
        <img src="${base}/generic/telecom/img/logo.png" class="LogoImg" alt="">
      </div>
      <div class="headerInfo">
        <p>退订天翼空间20元包月</p>
      </div>
    </div>
    <div class="informationFrame">
      <div class="FrameInput">
        <p class="FrameTitle">手机号</p>
        <input type="text" placeholder="请输入手机号码" v-model="mobile">
      </div>
    </div>
    <!-- 按钮 -->
    <div class="Buttongroup">
      <div class="Button" @click="query">查询</div>
      <div class="Button backRed" @click="ok">确定</div>
    </div>
    <#--    <p class="txt">本产品开通立即生效，次月自动续订，退订次月失效</p>-->
  </div>
  <script src="${base}/generic/layer_mobile/layer.js"></script>
  <script type="text/javascript">
    var app = new Vue({
      el: '#main',
      data: {
        mobile: '',
        code: '',
        timer: null,
        time: "",
        loading: null
      },
      computed: {
        showTime() {
          return this.timer ? "重新获取" + this.time + "s" : "获取验证码";
        }
      },
      methods: {
        checkMobile(mobile) {
          return /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(mobile);
        },
        checkCode(code) {
          return /^\d{6}$/.test(code);
        },
        showMsg(msg, time) {
          layer.open({
            content: msg,
            skin: "msg",
            time: time ? time : 3 // 3秒后自动关闭
          });
        },
        // 查询开通
        query() {
          if (!this.checkMobile(this.mobile)) {
            return this.showMsg("请输入正确格式手机号");
          }
          // loading
          this.loading = layer.open({
            shadeClose: false, // 点击关闭loading层
            type: 2,
            content: "加载中"
          });
          // https://crbt.cdyrjygs.com/cms-vrbt/api/dianxinPackageExists?mobile=17781670643
          axios.post("${base}/api/tianyiRelationShip?phone=" + this.mobile)
            .then(resp => {
                this.showMsg(resp.data.message);
            }).catch(err => {
              console.log("err => ", err);
              this.showMsg("网络异常,请稍后再试");
            }).finally(_ => {
              layer.close(this.loading);
            });
        },


        // 退订
        ok() {
          if (!this.checkMobile(this.mobile)) {
            return this.showMsg("请输入正确格式手机号");
          }
          this.loading = layer.open({
            shadeClose: false, // 点击关闭loading层
            type: 2,
            content: "处理中"
          });

          // https://crbt.cdyrjygs.com/cms-vrbt/api/dianxinUnsubscribe?mobile=17781670643
          axios.post("${base}/api/tianyiUnsubscribe?phone=" + this.mobile)
            .then(resp => {
              console.log(resp.data)
              /**
               * 	"success": true,
                  "message": "{\"BasicJTResponse\":{\"res_code\":0,\"res_message\":\"重复退订!\"}}",
                  "code": 200,
                  "result": null,
                  "timestamp": 1623738911479
              */
              // if (resp.data.code !== 200) {
              //   return this.showMsg(resp.data.message);
              // }
              this.showMsg(resp.data.message);
              // 接口请求成功开始倒计时
            }).catch(err => {
              console.log("err => ", err);
              this.showMsg("网络异常,请稍后再试");
            }).finally(_ => {
              layer.close(this.loading);
            });
        },
        cancel() {
          history.back();
        }
      }
    })
  </script>
</body>

</html>