<#assign base=springMacroRequestContext.getContextUrl("")>
<table>
    <tr>
        手机号码：<input value="${subscribe.mobile}" id="mobileQuery"><button id="querySmsCodeData">查询</button>
    </tr>
</table>
<br/>
<table>
    <tr>
        <th>手机号码</th>
        <th>短信内容</th>
        <th>计数</th>
        <th>运营商</th>
        <th>省份</th>
        <th>提交时间</th>
        <th>发送时间</th>
        <th>回执时间</th>
        <th>发送结果</th>
        <th>批次号</th>
    </tr>
    <tr>
        <td><span>${subscribe.mobile}</span></td>
        <td><span>${subscribe.remark}</span></td>
        <td>1</td>
        <td><span>${subscribe.isp}</span></td>
        <td><span>${subscribe.province}</span></td>
        <td><span>2022-06-15 17:27:44</span></td>
        <td><span>2022-06-15 17:27:48</span></td>
        <td><span>2022-06-15 17:27:48</span></td>
        <td><span>成功(DELIVRD)</span></td>
        <td><span>${subscribe.deviceInfo}</span></td>
    </tr>
</table>
