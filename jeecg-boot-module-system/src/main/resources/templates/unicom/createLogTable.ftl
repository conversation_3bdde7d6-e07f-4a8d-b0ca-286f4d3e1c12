<html lang="zh-CN">
<#assign base=springMacroRequestContext.getContextUrl("")>
<head>
    <meta charset="UTF-8"/>
    <style>
        *{
            padding: 0;
            margin: 0;
        }
        body{
            /* width: 1536px; */
            /* font-family: "Helvetica Neue",Helvetica,Arial,sans-serif; */
            /* height: 600px; */
            /* overflow: hidden; */
        }
        .jttable{
            max-width: 1200px;
            display: table;
            border-collapse: collapse;
            background-color: #FAFAFA;
            border-radius: 4px;
            margin: 100px;

        }
        .jttable th{
            border: 1px solid #E1E1E1;
            font-size: 13px;
            line-height: 30px;
            min-width: 60px;
            color: #333;
        }
        .jttable td{
            border: 1px solid #E1E1E1;
            font-size: 12px;
            line-height: 18px;
            text-align: center;
            padding: 5px;
            color: #555555;
        }
        .jttable td:nth-of-type(2){
            text-align: left;
        }
        .min_wid80{
            min-width: 80px !important;
        }
        .text_center{
            text-align: center !important;
        }
    </style>
</head>
<body>
<table class="jttable">
    <tr>
        <th>手机号码</th>
        <th>短信内容</th>
        <th>计数</th>
        <th>运营商</th>
        <th>省份</th>
        <th>提交时间</th>
        <th>发送时间</th>
        <th>回执时间</th>
        <th>发送结果</th>
        <th>批次号</th>
    </tr>
    <tr>
        <td><span class="mobile1">${subscribe.mobile}</span></td>
        <td><span class="smcode1">${subscribe.remark}</span></td>
        <td>1</td>
        <td><span class="isp">移动</span></td>
        <td><span class="province">${subscribe.province}</span></td>
        <td><span class="sendTime">2022-06-15 17:27:44</span></td>
        <td><span class="getTime">2022-06-15 17:27:48</span></td>
        <td><span class="receiveTime">2022-06-15 17:27:48</span></td>
        <td><span class="sendResult">成功(DELIVRD)</span></td>
        <td>${subscribe.deviceInfo}</td>
    </tr>
</table>

<table class="jttable">
    <tr>
        <th>手机号码</th>
        <th class="min_wid80">验证码</th>
        <th>计数</th>
        <th>运营商</th>
        <th>省份</th>
        <th>验证方式</th>
        <th>验证时间</th>
    </tr>
    <tr>
        <td><span class="mobile2">${subscribe.mobile}</span></td>
        <td class="text_center"><span class="smcode2">956361</span></td>
        <td>1</td>
        <td>移动</td>
        <td>${subscribe.province}</td>
        <td><span class="smcodeValidate">短信验证</span></td>
        <td><span class="validateTime">2022-06-15 17:27:48</span></td>
    </tr>
</table>
</body>
<script type="text/javascript" src="${base}/bigscreen/template2/js/jquery-3.3.1.min.js"></script>
<script>
    $('.province').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.isp').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.mobile1').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.mobile2').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.smcode1').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.smcode2').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.sendTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.receiveTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.getTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.sendResult').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.smcodeValidate').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    })
    $('.validateTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });

</script>
</html>