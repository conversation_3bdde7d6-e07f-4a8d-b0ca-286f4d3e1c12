<#assign base=springMacroRequestContext.getContextUrl("")>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>联通酷炫彩铃包月退订</title>
    <script src="${base}/generic/js/vue-2.6.14.min.js"></script>
    <script src="${base}/generic/js/axios.min.js"></script>
    <link rel="stylesheet" href="${base}/generic/unicom/css/reset.css">
    <link rel="stylesheet" href="${base}/generic/unicom/css/index.css">
</head>
<body>
<div id="main">
    <div class="headerNav">
        <div class="headerLogo">
            <img src="${base}/generic/unicom/img/logo.png" class="LogoImg" alt="">
        </div>
        <div class="headerInfo">
            <p>退订联通酷炫彩铃</p>
<#--            <p class="InfoPrice">￥6元/月</p>-->
        </div>
    </div>
    <!-- 输入框 -->
    <div class="informationFrame">
        <div class="FrameInput">
            <p class="FrameTitle" >手机号</p>
            <input type="text" placeholder="请输入手机号码" v-model="mobile">
        </div>
        <div class="FrameInput">
            <p class="FrameTitle">渠道</p>
            <select v-model="vrbtLtChannel">
                <option value="">请选择</option>
                <option value="leqing">贝乐虎(乐擎)</option>
                <option value="ruijin">瑞金</option>
                <option value="hongsheng">鸿盛天极</option>
            </select>
        </div>
<#--        <div class="FrameInput">-->
<#--            <p class="FrameTitle" >验证码</p>-->
<#--            <input type="text" placeholder="请输入验证码" v-model="code">-->
<#--            <p class="phoneYzm" @click="sendCode">{{ showTime }}</p>-->
<#--        </div>-->
    </div>
    <!-- 按钮 -->
    <div class="Buttongroup">
        <div class="Button" @click="query">查询</div>
        <div class="Button backRed" @click="ok">确定</div>
    </div>
<#--    <p class="txt">本产品开通立即生效，次月自动续订，退订次月失效</p>-->
</div>
<script src="${base}/generic/layer_mobile/layer.js"></script>
<script type="text/javascript">
    var app = new Vue({
        el: '#main',
        data: {
            mobile: '',
            vrbtLtChannel: '',
            code: '',
            timer: null,
            time: "",
            loading: null
        },
        computed: {
            showTime() {
                return this.timer ? "重新获取"+ this.time+"s" : "获取验证码";
            }
        },
        methods: {
            checkMobile(mobile) {
                return /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(mobile);
            },
            checkCode(code) {
                return /^\d{6}$/.test(code);
            },
            showMsg(msg, time) {
                layer.open({
                    content: msg,
                    skin: "msg",
                    time: time ? time : 3 // 3秒后自动关闭
                });
            },
            // 获取验证码成功后倒计时
            startTimer() {
                this.time = 60;
                this.timer = setInterval(() => {
                    --this.time;
                    if (this.time <= 0) {
                        this.time = "";
                        window.clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            },
            sendCode() {
                if (!this.timer) {
                    if (!this.checkMobile(this.mobile)) {
                        return this.showMsg("请输入正确格式手机号");
                    }
                    // loading
                    this.loading = layer.open({
                        shadeClose: false, // 点击关闭loading层
                        type: 2,
                        content: "加载中"
                    });
                    axios.post("${base}/api/ltSendUnSubCode?mobile="+this.mobile+"&vrbtLtChannel="+this.vrbtLtChannel)
                            .then(resp => {
                                console.log(resp.data)
                                if(resp.data.code !== 200){
                                    return this.showMsg(resp.data.message);
                                }
                                this.showMsg("获取验证码成功");
                                // 接口请求成功开始倒计时
                                this.startTimer();
                            }).catch(err => {
                                console.log("err => ", err);
                                this.showMsg("获取验证码失败");
                            }).finally(_=>{
                                layer.close(this.loading);
                            });
                }
            },
            ok() {
                if (!this.checkMobile(this.mobile)) {
                    return this.showMsg("请输入正确格式手机号");
                }
                if (!this.vrbtLtChannel) {
                    return this.showMsg("请选择渠道");
                }
                // if(!this.checkCode(this.code)) {
                //     return this.showMsg("请输入正确的短信验证码");
                // }
                // loading
                this.loading = layer.open({
                    shadeClose: false, // 点击关闭loading层
                    type: 2,
                    content: "处理中"
                });
                //axios.post("${base}/api/ltUnSubProduct?mobile="+this.mobile+"&code="+this.code)
                axios.post("${base}/api/ltUnSubProductNoToken?mobile="+this.mobile+"&vrbtLtChannel="+this.vrbtLtChannel)
                        .then(resp => {
                            console.log(resp.data)
                            if(resp.data.code !== 200){
                                return this.showMsg(resp.data.message);
                            }
                            this.showMsg("视频彩铃包月业务退订成功");
                            // 接口请求成功开始倒计时
                        }).catch(err => {
                            console.log("err => ", err);
                            this.showMsg("网络异常,请稍后再试");
                        }).finally(_=>{
                            layer.close(this.loading);
                        });
            },
            query(){
                if (!this.checkMobile(this.mobile)) {
                    return this.showMsg("请输入正确格式手机号");
                }
                if (!this.vrbtLtChannel) {
                    return this.showMsg("请选择渠道");
                }
                // if(!this.checkCode(this.code)) {
                //     return this.showMsg("请输入正确的短信验证码");
                // }
                // loading
                this.loading = layer.open({
                    shadeClose: false, // 点击关闭loading层
                    type: 2,
                    content: "处理中"
                });
                //axios.post("${base}/api/ltSubedMonQuery?mobile="+this.mobile+"&code="+this.code)
                axios.post("${base}/api/ltSubedMonQuery?mobile="+this.mobile+"&vrbtLtChannel="+this.vrbtLtChannel)
                        .then(resp => {
                            console.log(resp.data)
                            this.showMsg(resp.data.message);
                            // 接口请求成功开始倒计时
                        }).catch(err => {
                            console.log("err => ", err);
                            this.showMsg("网络异常,请稍后再试");
                        }).finally(_=>{
                            layer.close(this.loading);
                        });
            }
        }
    })
</script>
</body>
</html>