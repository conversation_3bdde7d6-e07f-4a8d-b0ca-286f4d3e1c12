<html lang="zh-CN">
<#assign base=springMacroRequestContext.getContextUrl("")>
<head>
    <meta charset="UTF-8"/>
    <style>
        table {
            width: 90%;
            font-size: .938em;
            border-collapse: collapse;/*边框合并*/
        }
        th {
            text-align: left;
            padding: .5em .5em;
            font-weight: bold;
            background: #C5C8D3;color: #5E5E5E;
        }
        td {
            padding: .5em .5em;
            border-bottom: solid 1px #ccc;
        }
        input{
            border: 1px solid #ccc;
            padding: 7px 0px;
            border-radius: 3px;
            padding-left:5px;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
            transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s
        }
        input:focus{
            border-color: #66afe9;
            outline: 0;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)
        }
    </style>
</head>
<body>
<table>
    <tr>
        手机号码：<input value="${subscribe.mobile}">
    </tr>
</table>
<br/>
<table>
    <tr>
        <th>操作</th>
        <th>最后操作时间</th>
        <th>结果</th>
        <th>输入参数</th>
    </tr>
    <td style="background: #E2E4E9">一键登录</td>
    <td style="background: #E2E4E9">${openTime}</td>
    <td style="background: #E2E4E9">成功</td>
    <td style="background: #E2E4E9">${openTime} [http-nio-9527-exec-99] INFO ApiController:3475 阿里云本机校验result=>phoneNumber:${subscribe.mobile},verifyResult:PASS</td>
</table>
<br/>
<table>
    <tr>
        <th>手机号码</th>
        <th>短信验证码</th>
        <th>计数</th>
        <th>运营商</th>
        <th>省份</th>
        <th>提交时间</th>
        <th>发送时间</th>
        <th>回执时间</th>
        <th>发送结果</th>
        <th>批次号</th>
    </tr>
    <td style="background: #E2E4E9" width="100px"><span class="mobile">${subscribe.mobile}</span></td>
    <td style="background: #E2E4E9" width="100px"><span class="smcode">123456</span></td>
    <td style="background: #E2E4E9" width="50px"><span class="count">1</span></td>
    <td style="background: #E2E4E9" width="100px"><span class="isp">移动</span></td>
    <td style="background: #E2E4E9" width="100px"><span class="province">${subscribe.province}</span></td>
    <td style="background: #E2E4E9" width="220px"><span width="660px" class="commitTime">2022-10-15 33:24:61</span></td>
    <td style="background: #E2E4E9" width="220px"><span class="sendTime">2022-10-15 33:24:61</span></td>
    <td style="background: #E2E4E9" width="220px"><span class="getTime">2022-10-15 33:24:61</span></td>
    <td style="background: #E2E4E9" width="100px"><span class="sendResult">成功(DELIVRD) </span></td>
    <td style="background: #E2E4E9" width="100px"><span class="batchNo">${subscribe.deviceInfo}</span></td>
</table>
<br/>

<table>
    <tr>
        <th>手机号码</th>
        <th>运营商</th>
        <th>省份</th>
        <th>操作类型</th>
        <th>参数</th>
        <th>操作时间</th>
    </tr>
    <td style="border:1px solid #dde2ec; width="100px"><span>${subscribe.mobile}</span></td>
    <td style="border:1px solid #dde2ec; width="100px"><span>移动</span></td>
    <td style="border:1px solid #dde2ec; width="100px"><span>${subscribe.province}</span></td>
    <td style="border:1px solid #dde2ec; width="220px"><span width="660px">确认开通彩铃功能</span></td>
    <td style="border:1px solid #dde2ec; width="220px"><span>phone"."${subscribe.mobile}""channelCode":"${subscribe.channel}","serviceld":"..."</span></td>
    <td style="border:1px solid #dde2ec; width="220px"><span class="t2_createTime">2022-10-15 33:24:61</span></td>
</table>
<br/>

<table>
    <tr>
        <th style="border:1px solid #0094ff;">title</th>
        <th style="border:1px solid #0094ff;">createTime</th>
        <th style="border:1px solid #0094ff;">request</th>
        <th style="border:1px solid #0094ff;">result</th>
    </tr>
    <#list list as createLog>
      <tr>
          <td style="border:1px solid #0094ff;">${createLog.title}</td>
          <td style="border:1px solid #0094ff;">${createLog.createTime}</td>
          <td style="border:1px solid #0094ff;">${createLog.request}</td>
          <td style="border:1px solid #0094ff;">${createLog.result}</td>
      </tr>
    </#list>
</table>
</body>
<script type="text/javascript" src="${base}/bigscreen/template2/js/jquery-3.3.1.min.js"></script>
<script>
    $('.mobile').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.smcode').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.count').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.isp').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.province').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.commitTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.sendTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.getTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.sendResult').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.batchNo').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    })
    $('.t2_createTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });

</script>
</html>