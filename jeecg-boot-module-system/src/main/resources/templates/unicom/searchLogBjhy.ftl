<html lang="zh-CN">
<head>
    <meta charset="UTF-8"/>
    <style>
        /*只对英文起作用，以字母作为换行依据*/
        .p1 {
            word-break: break-all;
            width: 150px;
        }

        /*--只对英文起作用，以单词作为换行依据*/
        .p2 {
            display: flex;
            width: 100%;
            color: #fff;
            word-wrap:break-word;word-break:break-all;overflow:hidden;
            font-size: 14px;
            /*word-wrap: break-word;*/
            /*width: 150px;*/
            background-color: black;
        }

        /*只对中文起作用，强制换行*/
        .p3 {
            white-space: pre-wrap;
            width: 150px;
        }

        /*强制不换行，都起作用*/
        .p4 {
            white-space: nowrap;
            width: 10px;
        }

        /*不换行，超出部分隐藏且以省略号形式出现*/
        .p5 {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100px;
        }
    </style>
</head>
<body>
<table>
    <#list list as log>
        <div>${log.title}</div>
        <div class="p2">${log.log}</div>
    </#list>
</table>
</body>
</html>