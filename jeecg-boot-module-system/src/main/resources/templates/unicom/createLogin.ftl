
<#assign base=springMacroRequestContext.getContextUrl("")>
<table>
    <tr>
        手机号码：<input value="${mobile}">
    </tr>
</table>
<br/>
<table>
    <tr>
        <th>操作</th>
        <th>最后操作时间</th>
        <th>结果</th>
        <th>输入参数</th>
    </tr>
    <td style="background: #E2E4E9">一键登录</td>
    <td style="background: #E2E4E9">${openTime}</td>
    <td style="background: #E2E4E9">成功</td>
    <td style="background: #E2E4E9">${openTime} [http-nio-9527-exec-99] INFO ApiController:3475 阿里云本机校验result=>phoneNumber:${mobile},verifyResult:PASS</td>
</table>