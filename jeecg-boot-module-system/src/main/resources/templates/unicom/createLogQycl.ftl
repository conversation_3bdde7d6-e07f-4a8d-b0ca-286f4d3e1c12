<html lang="zh-CN">
<#assign base=springMacroRequestContext.getContextUrl("")>
<head>
    <meta charset="UTF-8"/>
    <style>
        table {
            width: 90%;
            font-size: .938em;
        }
        th {
            text-align: left;
            padding: .5em .5em;
            font-weight: bold;
            background: #f1f1f1;
            color: #929292;
        }
        td {
            padding: .5em .5em;
            background: #fafafa;
            color: #888888;
        }
        input{
            border: 1px solid #ccc;
            padding: 7px 0px;
            border-radius: 3px;
            padding-left:5px;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
            transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s
        }
        input:focus{
            border-color: #66afe9;
            outline: 0;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)
        }
    </style>
</head>
<body>
<table>
    <tr>
        <th>手机号</th>
        <th>省份</th>
        <th>城市</th>
        <th>运营商</th>
        <th>返回码</th>
        <th>返回信息</th>
        <th>操作行为</th>
        <th>创建时间</th>
    </tr>
    <td style="background: #FCFCFC"><span class="mobile">${mobile}</span></td>
    <td style="background: #FCFCFC"><span class="province">${province}</span></td>
    <td style="background: #FCFCFC"><span class="city">${city}</span></td>
    <td style="background: #FCFCFC"><span class="isp">移动</span></td>
    <td style="background: #FCFCFC"><span class="code">0000</span></td>
    <td style="background: #FCFCFC"><span class="msg">'{"code":"0000","data":${data},"spid":"002","desc":"成功"}'</span></td>
    <td style="background: #FCFCFC"><span class="action">一键登录</span></td>
    <td style="background: #FCFCFC"><span class="createTime">${createTime}</span></td>
</table>
</body>
<script type="text/javascript" src="${base}/bigscreen/template2/js/jquery-3.3.1.min.js"></script>
<script>
    $('.mobile').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.province').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.city').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.isp').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.code').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.msg').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.action').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });
    $('.createTime').click(function () {
        var th = $(this);
        var html = th.html();
        var input = $("<input type='text' value='" + html + "'/>");
        th.html(input);//附上input
        //再次点击不变
        input.click(function () { return false; });
        //自动获取焦点
        input.trigger("focus");
        //文本框失去焦点变回来
        input.blur(function () {
            var val = $(this).val();
            if (val != html) {
                th.html(val);
            } else {
                th.html(val);
            }
        })
    });

</script>
</html>