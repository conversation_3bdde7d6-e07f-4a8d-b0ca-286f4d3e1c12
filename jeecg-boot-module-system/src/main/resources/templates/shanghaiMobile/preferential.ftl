<#assign base=springMacroRequestContext.getContextUrl("")>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <title>上海移动特惠包</title>
    <link rel="stylesheet" href="${base}/generic/unicom/css/reset.css">
    <link rel="stylesheet" href="${base}/generic/unicom/css/index.css">
</head>

<body>
<div class="i_main">
    <span class="span1"></span>
    <span class="span2"></span>
    <span class="span3"></span>
    <span class="span4"></span>
    <span class="span5"></span>
    <span class="span6"></span>
    <span class="span7"></span>
    <span class="span8"></span>
    <div class="price">
        <input type="text" id="phone" value="">
        <input type="text" id="code"  value="">
        <input type="hidden" id="orderId"  value="">
        <input type="hidden" id="buyId" value="">
        <input type="hidden" id="openId" value="">

        <div class="l_price" id="${fiveTenGb.name()}" style="display:none">
            <p >${fiveTenGb.activityType}</p>
            <div class="line"></div>
            <span class="m_span">￥</span><span class="p_span" >${fiveTenGb.discountPrice}</span>
            <div class="o_price">原价 ￥<span >${fiveTenGb.originalPrice}</span></div>
        </div>
        <div class="l_price" id="${tenTenGb.name()}" style="display:none">
            <p >${tenTenGb.activityType}</p>
            <div class="line"></div>
            <span class="m_span">￥</span><span class="p_span" >${tenTenGb.discountPrice}</span>
            <div class="o_price">原价 ￥<span >${tenTenGb.originalPrice}</span></div>
        </div>
        <div class="l_price" id="${fiveFiveGb.name()}" style="display:none">
            <p >${fiveFiveGb.activityType}</p>
            <div class="line"></div>
            <span class="m_span">￥</span><span class="p_span" >${fiveFiveGb.discountPrice}</span>
            <div class="o_price">原价 ￥<span >${fiveFiveGb.originalPrice}</span></div>
        </div>
        <div class="r_price" id="${tenFiveGb.name()}" style="display:none">
            <p >${tenFiveGb.activityType}</p>
            <div class="line"></div>
            <span class="m_span">￥</span><span class="p_span" >${tenFiveGb.discountPrice}</span>
            <div class="o_price">原价 ￥<span >>${tenFiveGb.originalPrice}</span></div>
        </div>
    </div>
    <div class="explain" style="top: 29.64rem;">
        <div class="content">
            <div class="e_title">权益内容</div>
            <p>百度网盘超级会员享有5T超大容量、极速下载、在线加压8G压缩包、视频倍速播放、单次转存文件提升、20G大文件上传、批量上传无限制、视频播放极速加载等18项会员服务。
            </p>
        </div>
        <div class="content time">
            <div class="e_title">权益有效期</div>
            <p>百度网盘超级会员月卡的有效期按照自然月计算，百度网盘超级会员季卡按 照自然季度计算，续费将百度网盘超级会员有效期按自然月、自然季度顺延。例如：您在2020-06-15日购买了一个月百度网盘超级会员，即在2020-07-15日到期；购买百度网盘超级会员季卡，即在2020-09-15到期。
            </p>
        </div>
        <div class="content">
            <div class="e_title">使用说明</div>
            <p class="e_use_body">
                1、订购成功后将向订购的手机号码充值百度网盘超级会员权益。1小时内到账；如您的百度网盘账号未绑定订购的手机号码，请尽快绑定手机号码并开启手机登录。  绑定操作手册>>><br>
                2、超级会员有效期从充值成功之日算起，查询百度网盘超级会员有效期，请登录百度网盘APP，点击“我的”页面查看。
            </p>
        </div>
        <div class="content rule">
            <div class="e_title">活动规则</div>
            <p>1、本次活动仅面向北京移动嗨购用户开放。<br> 2、本产品为虚拟产品，订购成功后，不支持退款。
                <br> 3、如订购成功后未收到激活码或对活动有任何疑问，请在工作时间联系客服：4006071007。
                <br> 本产品由第三方提供，北京移动拥有产品最终解释权。
            </p>
        </div>
    </div>
</div>
<button class="layui-btn  layui-btn-sm layui-btn-primary" id="send-from" >发送</button>

<button class="layui-btn  layui-btn-sm layui-btn-primary" id="check-from" >验证</button>

<button class="layui-btn  layui-btn-sm layui-btn-primary" id="confirm-from" >确认</button>

<button class="layui-btn  layui-btn-sm layui-btn-primary" id="submit-from" >提交</button>

<script src="${base}/generic/layer_mobile/layer.js"></script>
<script type="text/javascript">
    // var ctx = [[@{/}]];
    layui.use(['jquery','layer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
            $('#send-from').bind('click',function () {
                if($("#phone").val()==null || $("#phone").val()==""){
                    //alert("请输入手机号");
                    layer.msg("手机号不正确");
                    return false;
                }else{
                    let phone = /^[1][3,4,5,7,8][0-9]{9}$/;
                    if (!phone.test($("#phone").val())) {
                        $("#phone").val("")
                        layer.msg("手机号不正确");
                        return false;
                    }
                }
                // 单击之后提交按钮不可选,防止重复提交
                var DISABLED = 'layui-btn-disabled';
                // 增加样式
                $('#send-from').addClass(DISABLED);
                // 增加属性
                $('#send-from').attr('disabled', 'disabled');
                $.post('/shanghaiMobile/send',{"mobile":$("#phone").val()}, function (r) {
                    $('#send-from').removeClass(DISABLED);
                    $('#send-from').removeAttr('disabled');
                    layer.msg(r.message);
                });
            });


        $('#check-from').bind('click',function () {
            if($("#phone").val()==null || $("#phone").val()==""){
                //alert("请输入手机号");
                layer.msg("手机号不正确");
                return false;
            }else{
                let phone = /^[1][3,4,5,7,8][0-9]{9}$/;
                if (!phone.test($("#phone").val())) {
                    $("#phone").val("")
                    layer.msg("手机号不正确");
                    return false;
                }
            }
            if($("#code").val()==null || $("#code").val()==""){
                //alert("请输入手机号");
                layer.msg("请输入验证码");
                return false;
            }
            // 单击之后提交按钮不可选,防止重复提交
            var DISABLED = 'layui-btn-disabled';
            // 增加样式
            $('#check-from').addClass(DISABLED);
            // 增加属性
            $('#check-from').attr('disabled', 'disabled');
            $.post('/shanghaiMobile/white/handle',{"mobile":$("#phone").val(),"code":$("#code").val()}, function (r) {
                $('#check-from').removeClass(DISABLED);
                $('#check-from').removeAttr('disabled');
                if(r.data.code=="200"){
                    $("#orderId").val(r.data.orderId)
                    $("#openId").val(r.data.message)
                }else{
                    layer.msg(r.data.message);
                }


            });
        });
        $('#confirm-from').bind('click',function () {
            if($("#openId").val()==null || $("#openId").val()==""){
                //alert("请输入手机号");
                layer.msg("请选择查询用户权限");
                return false;
            }
            if($("#orderId").val()==null || $("#orderId").val()==""){
                //alert("请输入手机号");
                layer.msg("订单号码不能为空");
                return false;
            }
            // 单击之后提交按钮不可选,防止重复提交
            var DISABLED = 'layui-btn-disabled';
            // 增加样式
            $('#confirm-from').addClass(DISABLED);
            // 增加属性
            $('#confirm-from').attr('disabled', 'disabled');
            $.post('/shanghaiMobile/business/handle',{"openId":$("#openId").val(),"orderId":$("#orderId").val()}, function (r) {
                $('#confirm-from').removeClass(DISABLED);
                $('#confirm-from').removeAttr('disabled');
                if(r.data.code=="200"){
                    $('#buyId').val(r.data.message);
                    $("#"+r.data.message).show();
                }else{
                    layer.msg(r.data.message);
                }
            });
        });

        $('#submit-from').bind('click',function () {
            if($("#buyId").val()==null || $("#buyId").val()==""){
                //alert("请输入手机号");
                layer.msg("请选择套餐");
                return false;
            }
            // 单击之后提交按钮不可选,防止重复提交
            var DISABLED = 'layui-btn-disabled';
            // 增加样式
            $('#submit-from').addClass(DISABLED);
            // 增加属性
            $('#submit-from').attr('disabled', 'disabled');
            $.post('/shanghaiMobile/flow/handle',{"orderId":$("#orderId").val(),"buyId":$("#buyId").val()}, function (r) {
                $('#submit-from').removeClass(DISABLED);
                $('#submit-from').removeAttr('disabled');
                layer.msg(r.data.message);
            });
        });
    });
</script>
</body>
</html>
