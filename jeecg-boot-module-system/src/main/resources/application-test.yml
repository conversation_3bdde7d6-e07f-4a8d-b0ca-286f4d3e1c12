server:
  port: 8080
  tomcat:
    max-swallow-size: -1
    #  解决springmvc接收unicode参数报400错误的问题 https://stackoverflow.com/questions/46251131/invalid-character-found-in-the-request-target-in-spring-boot
    #    relaxed-query-chars: '\,|,{,},[,]'
    relaxed-query-chars: '\,|'
  servlet:
    context-path: /jeecg-boot
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
 endpoints:
  web:
   exposure:
    include: metrics,httptrace

spring:
  servlet:
     multipart:
        max-file-size: 10MB
        max-request-size: 10MB
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  # quartz定时任务,采用数据库方式,jeecgboot集成的quartz定时任务有bug,跑着跑着就不执行了,所以使用spring自带的比较好
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #设置自动启动，默认为 true
    auto-startup: false
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
      datasource:
          master:
            url: ***************************************************************************************************************
            username: root
            password: root
            driver-class-name: com.mysql.jdbc.Driver
          #多数据源配置
          slave:
            url: ***************************************************************************************************************
            username: root
            password: root
            driver-class-name: com.mysql.jdbc.Driver
  #redis 配置
  redis:
     database: 0
     host: *************
     lettuce:
       pool:
         max-active: 8   #最大连接数据库连接数,设 0 为没有限制
         max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
         max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
         min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
       shutdown-timeout: 100ms
     password: ''
     port: 6379
#mybatis plus 设置
mybatis-plus:
   mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml,com/eleven/cms/**/xml/*Mapper.xml
   global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: 4
      # 默认数据库表下划线命名
      table-underline: true
   configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
jeecg :
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: local
  path :
    #文件上传根目录 设置
    upload: D://opt//upFiles
    #webapp文件路径
    webapp: D://opt//webapp
  #短信秘钥
  sms:
     accessKeyId: LTAIpW4gUG7xYDNI
     accessKeySecret: ??
  shiro:
     excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**
  #阿里云oss存储配置
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKey: ??
    secretKey: ??
    bucketName: jeecgos
    staticDomain: https://static.jeecg.com
  # ElasticSearch 设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: http://fileview.jeecg.com
    check-enabled: false
  # 表单设计器配置
  desform:
    # 主题颜色（仅支持 16进制颜色代码）
    theme-color: "#1890ff"
  # 在线预览文件服务器地址配置
  file-view-domain: http://127.0.0.1:8012
  # minio文件上传
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: otatest
#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas
#enable swagger
swagger:
  enable: true

#电信视频彩铃配置
dianxin:
  vrbt:
    #合作商账号，爱音乐分配，请向商务人员获取。又名deviceid或AppKey
    #    deviceId: 2000000002044
    deviceId:  2000000002244
    #爱音乐分配的秘钥，请向商务人员获取，又名devicePwd或AppSecret；
    #    devicePwd: IyoadK78R0QP
    devicePwd: 0aCu5uZqSfAv
    #渠道号，爱音乐分配，请向商务人员获取。 又名channelid
    #    channelId: 7190
    channelId: 7120
    #包月套餐ID
    #    packageId: 135999999999999000021
    packageId: 135999999999999000032
    #api基地址
    apiBaseUrl: http://api.118100.cn/openapi/services
    #包月产品订购关系查询
    #queryPackageListUrl: ${dianxin.vrbt.apiBaseUrl}/v2/package/packageservice/querypackagelist.json
    queryPackageListUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/querypackagelist.json
    #包月产品退订
    unSubscribeByempUrl: ${dianxin.vrbt.apiBaseUrl}/v2/package/packageservice/unsubscribebyemp.json
    #一键开通视频彩铃订购包月产品(音乐盒代计费)验证码下发
    asyncOpenOrderSendRandomUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/onekey/async_open_order_sendrandom.json
    #一键开通视频彩铃订购包月产品(音乐盒代计费)下单
    asyncOpenAccountOrderPackageBycrbtUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/onekey/async_openaccount_orderpackage_bycrbt.json
    #H5计费下单发起接口(可选验证类型),调用该接口生成订单,并返回计费认证H5页面地址,用户跳到计费认证页面完成计费.
    confirmOrderLaunchedExUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/confirm_order_launched_ex.json
    # H5 计费下单发起接口（一键开户订购）  调用该接口生成订单。并返回计费认证H5页面地址。用户跳到计费认证页面完成计费。
    #一键开户+订购
    confirmOpenOrderLaunchedExUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/confirm_open_order_launched_ex.json
    #H5 计费下单发起接口（EMP 计费） 调用该接口生成订单。并返回计费认证H5页面地址。用户跳到计费认证页面完成计费。
    #通过该接口下单的订单，在 H5 中必须使用 EMP 计费发起接口，向用户下发 EMP 短信验证码
    confirmOrderLaunchedEmpUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/confirm_order_launched_emp.json
    #视频彩铃一键接口订单详情查询
    queryOrderUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/onekey/query_order.json
    #H5计费订单详情查询
    queryH5OrderUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/query_h5_order.json
    #使用包月产品权益免费订购视频铃音
    addToneFreeOnProductUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/ringtone/addtonefreeonproduct.json
    #视频彩铃信息查询接口
    videoQueryUrl: ${dianxin.vrbt.apiBaseUrl}/v3/qkservice/video/query.json
    #h5下单 计费认证页面操作后的返回地址
    returnUrl: https://crbt.cdyrjygs.com/vrbt_ch_v4/#/submitwp


#阿里云短信配置
aliyun:
  sms:
    region-id: cn-hangzhou
    accessKeyId: LTAI4xjDqYEriyvv
    secret: HKCNOj1ApS6EjaqoXlHtJAxalNqQRW
    queryParameterRegionId: cn-hangzhou
    queryParameterSignName: 荐音振铃
    #验证码短信模板
    validate-template-code: SMS_190785336
    validate-template-param-key: code
    request:
      method: post
      domain: dysmsapi.aliyuncs.com
      version: 2017-05-25
      action: SendSms

#骏伯多会员充值配置(测试环境)
junbo-api:
  #测试环境
  #  baseUrl: http://test-coupon.liulianglf.com
  #生产环境
  baseUrl: http://h5.coupon.liulianglf.com
  #多会员直充
  rechargeVipUrl: ${junbo-api.baseUrl}/rechargeVIP
  #订单查询
  orderQueryUrl: ${junbo-api.baseUrl}/orderQuery
  uid: yrjy
  version: 1.0.423.2021011210050872400041
  partnerNo: 2021011210050872400041
  rsaPrivateKeyPkcs8: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDSdRw6Wr42SSstAccb+DwtL2FgvW2sj1TTtbKP6sBhoATyCmKxDPFITXq8IvPxvD3vv9nY5FwlgfDiTsDXPrFL188aaBKB4aZDUYnSjzfvYq2PPjAuobhda0r9gbquKDgcowtXNtaukhwDtzJR8H+thybQ2so71rS+OFOdi2fxvBe/a/LgxkUNLp0EgCheyzOu/2mDV1WM2lZ7HlV2dPrJVK9Jo4YMc6uPfNEPlwIvW6ikSChVLimt7H4LFcsTkb72DMvYRnSgNc01Av/1/CQraBRrfC0fB9jLW4kW/zVtaTNLvQ4esQ/xT3XeeiAvYVniCm3pjqxDfFFB/OUALj0BAgMBAAECggEBAM6Y9jf2wokp9L9+4kX7vp8gxvgfnN1r6vrVgE+1RFvRqWktdsqViPrQTG8J5O1mPGrxq9o1+ps7XwvaAYaLXaK1XPJwPdLQf9XP4nsX6vINrZFTnBr62LCkVf5cbrTueH7sM4LPK5o9hlDDcxtq+vuPFDSswyBQ2idtxe9/XW+3zNpNLmBUJk1W36dm2FII2sez/3iSDkeL1bUTrUAL2bepJNmAIIUSAl12yVUBYMYpj1s/WULxntysLNL7tveokh3xcFcgE71c6isyvpVgly/W4uByJFahjK3yfI/tWQgy3qytzEz1a66JMf02AIMqx2e9Dw8HoH8/of0x8yJ25G0CgYEA6wx7nbhORwUX30yQP8TL0M3nujjxH9fhdDJasB2ZpV0XeRS6iT09+7M1o3KFvZfzFTLhHINzw7v2lgo5TM3pUGqmzoBcBJpbrVVeE03tgtBhE5+mzn8ohnx4kUH6oLxl5Zf7h45XdMsk0/zKkYPLxZXhagCG9K+WtB2rYLituFMCgYEA5Td8IE8FVHkh5hH3x89HK9ZPUiDEXpk1hYGX3qX08MqgJ+IRXTZGryqMOLprQf2q+3aYtUuD3lFiiystekBsVdTyeyme1HOQK2ON2qRsaL8FCNhCpoVlOCrARpD6eExCu2XVfT3b4rPlq9T13cufmLhkT0mQ67qvdNRegOAeetsCgYEAvKLEAowLpkucIQVdLxBNUkItmvJf8WJb+dj/lx/qUyAm2nGcUs/nkrz8azRZyRLNb1Hp/+wvWALdnwSNf+oxOOye+lNhhgArWyyL14pO9xEtF4alZEwAxg3W5RzCe4U3cc9LejruTTlLUSYrnCTdwLDMuzm5FV5l4B9/jpwoLykCgYB9Gkhj9d0J2scWo+3hnw7QLTrDr2Cd5O7iO/XgriE+nsjEm80wW6Q+jHFVsOFDdiiw8GyuXlXNrAulu14p4CmAuyXQ9MlcWOpx0/cjQyRsEv2zcx0Fd77j5ellWlFqAO1XDGB156IwXFFY9HVwpWco2lLdN2CiWBTVJeeiXTAliwKBgHp72BtRJOIz5EkB9byTAMil0JG/rHJCRSBtky5Q5sZZdrfPdN/FB/CuOVHForh3jYYhl6nTI76bqf8AWWE3iBJLjJuKm8X1vnWgkAfOVlX9TtSTjJ4ibEYAgIilTMvX+bolK3fbhGH530xBxiRK2kXp3x7xiBZ+jLBYN146cWNI
  callbackUrl: http://crbt.cdyrjygs.com:9527/cms-vrbt/api/junboRechargeResult
  otherRechargeMap:
    00210H0:
      channel: 00210H0
      serviceId: 698039020103880544
      name: 宜搜
      key: r00Ixn14MKMZqXR4
      isRecharge: 0

    00210T7:
      channel: 00210T7
      serviceId: 698039020103880544
      name: 动意
      key: tjWFK8bVU3nITEfO
      isRecharge: 0

    csgmapp:
      channel: csgmapp
      serviceId:
      name: 长沙格美APP
      key: J&BbpkuLlUV^ur%B8&ss0Sgji20rn8*Z
      #      是否限制充值
      isRecharge: 1
