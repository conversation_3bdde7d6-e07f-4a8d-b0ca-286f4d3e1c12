<#--noinspection JSDuplicatedDeclaration-->
<#list subTables as sub>
#segment#${sub.entityName}SubTable.vue
<template>
  <a-table
    rowKey="id"
    size="middle"
    bordered
    :loading="loading"
    :columns="columns"
    :dataSource="dataSource"
    :pagination="false"
  >

    <template slot="htmlSlot" slot-scope="text">
      <div v-html="text"></div>
    </template>

    <template slot="imgSlot" slot-scope="text">
      <div style="font-size: 12px;font-style: italic;">
        <span v-if="!text">无图片</span>
        <img v-else :src="getImgView(text)" alt="" style="max-width:80px;height:25px;"/>
      </div>
    </template>

    <template slot="fileSlot" slot-scope="text">
      <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
      <a-button
              v-else
              ghost
              type="primary"
              icon="download"
              size="small"
              @click="uploadFile(text)"
      >
        <span>下载</span>
      </a-button>
    </template>

  </a-table>
</template>

<script>
  import { getAction } from '@api/manage'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: '${sub.entityName}SubTable',
    mixins: [JeecgListMixin],
    props: {
      record: {
        type: Object,
        default: null,
      }
    },
    data() {
      return {
        description: '${sub.ftlDescription}内嵌列表',
        disableMixinCreated: true,
        loading: false,
        dataSource: [],
        columns: [
<#-- begin 遍历表头 -->
<#list sub.originalColumns as po>
  <#if po.isShowList == 'Y'>
          {
            title: '${po.filedComment}',
            align: 'center',
    <#if po.classType == 'date'>
            dataIndex: '${po.fieldName}',
    <#elseif po.fieldDbType=='Blob'>
            dataIndex: '${po.fieldName}String'
    <#elseif po.classType=='umeditor'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'htmlSlot'}
    <#elseif po.classType=='file'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'fileSlot'}
    <#elseif po.classType=='image'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'imgSlot'}
    <#elseif po.classType=='list' || po.classType=='list_multi' || po.classType=='sel_search' || po.classType=='radio' || po.classType=='checkbox' || po.classType=='sel_depart' || po.classType=='sel_user'>
            dataIndex: '${po.fieldName}_dictText'
    <#elseif po.classType=='cat_tree'>
      <#if list_need_category>
            dataIndex: '${po.fieldName}',
            customRender: (text) => (text ? filterMultiDictText(this.dictOptions['${po.fieldName}'], text) : '')
      <#else>
            dataIndex: '${po.fieldName}',
            customRender: (text, record) => (text ? record['${po.dictText}'] : '')
      </#if>
    <#else>
            dataIndex: '${po.fieldName}',
    </#if>
          },
  </#if>
</#list>
<#-- end 遍历表头 -->
        ],
<#assign urlPrefix="/${entityPackage}/${entityName?uncap_first}">
        url: {
          listByMainId: '${urlPrefix}/query${sub.entityName}ByMainId',
        },
      }
    },
    watch: {
      record: {
        immediate: true,
        handler() {
          if (this.record != null) {
            this.loadData(this.record)
          }
        }
      }
    },
    methods: {

      loadData(record) {
        this.loading = true
        this.dataSource = []
        getAction(this.url.listByMainId, {
          id: record.id
        }).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records
          }
        }).finally(() => {
          this.loading = false
        })
      },

    },
  }
</script>

<style scoped>

</style>
</#list>