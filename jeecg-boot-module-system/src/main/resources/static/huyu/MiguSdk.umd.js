(function(y, h) {
        typeof exports == "object" && typeof module < "u" ? h(exports) : typeof define == "function" && define.amd ? define(["exports"], h) : (y = typeof globalThis < "u" ? globalThis : y || self,
            h(y.MiguSdk = {}))
    }
)(this, function(y) {
    "use strict";
    var L = (y,h,v)=>new Promise((H,D)=>{
            var E = b=>{
                try {
                    C(v.next(b))
                } catch (K) {
                    D(K)
                }
            }
                , M = b=>{
                try {
                    C(v.throw(b))
                } catch (K) {
                    D(K)
                }
            }
                , C = b=>b.done ? H(b.value) : Promise.resolve(b.value).then(E, M);
            C((v = v.apply(y, h)).next())
        }
    );
    function h(t) {
        var g = Math.floor((Math.random() + Math.floor(Math.random() * 9 + 1)) * Math.pow(10, t - 1));
        return g
    }
    function v() {
        window.sessionStorage.getItem("visitor") && sessionStorage.setItem("v-deviceId", "deviceId-" + new Date().getTime().toString() + "-" + h(9)),
        window.localStorage.getItem("deviceId") || window.localStorage.setItem("deviceId", "deviceId-" + new Date().getTime().toString() + "-" + h(9))
    }
    var H = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
    function D(t) {
        return t && t.__esModule && Object.prototype.hasOwnProperty.call(t, "default") ? t.default : t
    }
    var E = {
        exports: {}
    };
    (function(t, g) {
            (function(i, c) {
                    t.exports = c()
                }
            )(H, function() {
                var i = function() {}
                    , c = {}
                    , w = {}
                    , d = {};
                function f(e, a) {
                    e = e.push ? e : [e];
                    var r = [], n = e.length, o = n, u, m, p, l;
                    for (u = function(U, S) {
                        S.length && r.push(U),
                            o--,
                        o || a(r)
                    }
                        ; n--; ) {
                        if (m = e[n],
                            p = w[m],
                            p) {
                            u(m, p);
                            continue
                        }
                        l = d[m] = d[m] || [],
                            l.push(u)
                    }
                }
                function $(e, a) {
                    if (e) {
                        var r = d[e];
                        if (w[e] = a,
                            !!r)
                            for (; r.length; )
                                r[0](e, a),
                                    r.splice(0, 1)
                    }
                }
                function j(e, a) {
                    e.call && (e = {
                        success: e
                    }),
                        a.length ? (e.error || i)(a) : (e.success || i)(e)
                }
                function x(e, a, r, n) {
                    var o = document, u = r.async, m = (r.numRetries || 0) + 1, p = r.before || i, l = e.replace(/[\?|#].*$/, ""), U = e.replace(/^(css|img|module|nomodule)!/, ""), S, z, s;
                    if (n = n || 0,
                        /(^css!|\.css$)/.test(l))
                        s = o.createElement("link"),
                            s.rel = "stylesheet",
                            s.href = U,
                            S = "hideFocus"in s,
                        S && s.relList && (S = 0,
                            s.rel = "preload",
                            s.as = "style");
                    else if (/(^img!|\.(png|gif|jpg|svg|webp)$)/.test(l))
                        s = o.createElement("img"),
                            s.src = U;
                    else if (s = o.createElement("script"),
                        s.src = U,
                        s.async = u === void 0 ? !0 : u,
                        z = "noModule"in s,
                        /^module!/.test(l)) {
                        if (!z)
                            return a(e, "l");
                        s.type = "module"
                    } else if (/^nomodule!/.test(l) && z)
                        return a(e, "l");
                    s.onload = s.onerror = s.onbeforeload = function(q) {
                        var _ = q.type[0];
                        if (S)
                            try {
                                s.sheet.cssText.length || (_ = "e")
                            } catch (B) {
                                B.code != 18 && (_ = "e")
                            }
                        if (_ == "e") {
                            if (n += 1,
                            n < m)
                                return x(e, a, r, n)
                        } else if (s.rel == "preload" && s.as == "style")
                            return s.rel = "stylesheet";
                        a(e, _, q.defaultPrevented)
                    }
                        ,
                    p(e, s) !== !1 && o.head.appendChild(s)
                }
                function k(e, a, r) {
                    e = e.push ? e : [e];
                    var n = e.length, o = n, u = [], m, p;
                    for (m = function(l, U, S) {
                        if (U == "e" && u.push(l),
                        U == "b")
                            if (S)
                                u.push(l);
                            else
                                return;
                        n--,
                        n || a(u)
                    }
                             ,
                             p = 0; p < o; p++)
                        x(e[p], m, r)
                }
                function I(e, a, r) {
                    var n, o;
                    if (a && a.trim && (n = a),
                        o = (n ? r : a) || {},
                        n) {
                        if (n in c)
                            throw "LoadJS";
                        c[n] = !0
                    }
                    function u(m, p) {
                        k(e, function(l) {
                            j(o, l),
                            m && j({
                                success: m,
                                error: p
                            }, l),
                                $(n, l)
                        }, o)
                    }
                    if (o.returnPromise)
                        return new Promise(u);
                    u()
                }
                return I.ready = function(a, r) {
                    return f(a, function(n) {
                        j(r, n)
                    }),
                        I
                }
                    ,
                    I.done = function(a) {
                        $(a, [])
                    }
                    ,
                    I.reset = function() {
                        c = {},
                            w = {},
                            d = {}
                    }
                    ,
                    I.isDefined = function(a) {
                        return a in c
                    }
                    ,
                    I
            })
        }
    )(E);
    var M = E.exports;
    const C = D(M);
    let b = "libmigu";
    b = "test";
    const T = {
        development: {
            baseURL: "http://223.111.8.117:38080/",
            loginUrl: "https://union-passport.migu-net.cn/",
            jsonUrl: "https://freeserver.migufun.com/resource/beta/media/jszt/",
            H5url: "https://www.migufun.com/",
            applyData: {
                appId: "migu_beta",
                accessKey: "b291b88e5eddef20b5a530c22f368159",
                bizId: "lightplay_sdk_web",
                accessKeySecret: "85d89d4883200d56bd8c19383577bb43"
            },
            accessInfo: {
                accessKey: "a8c14c093f7d0a3c583e2e6a47304d37",
                accessKeyID: "7f64350ef76"
            },
            bundleUrl: "https://www.migufun.com/miguplay/html/miguplayUtils/bundle.js",
            PUBLICKEY: "041f034b27c3d500be0ebc60e4a024b550421fbc595cfbf910a681abab053c98f65c6cf9f8c383aa1ae069b43414ce4698614b11118005ae0557aaaeb3275c9472",
            originH5Url: "https://www.migufun.com"
        },
        test: {
            baseURL: "http://223.111.8.117:38080/",
            loginUrl: "https://union-passport.migu-net.cn/",
            jsonUrl: "https://freeserver.migufun.com/resource/beta/media/jszt/",
            H5url: "https://www.migufun.com/",
            applyData: {
                appId: "migu_beta",
                accessKey: "b291b88e5eddef20b5a530c22f368159",
                bizId: "lightplay_sdk_web",
                accessKeySecret: "85d89d4883200d56bd8c19383577bb43"
            },
            accessInfo: {
                accessKey: "a8c14c093f7d0a3c583e2e6a47304d37",
                accessKeyID: "7f64350ef76"
            },
            bundleUrl: "https://www.migufun.com/miguplay/html/miguplayUtils/bundle.js",
            PUBLICKEY: "041f034b27c3d500be0ebc60e4a024b550421fbc595cfbf910a681abab053c98f65c6cf9f8c383aa1ae069b43414ce4698614b11118005ae0557aaaeb3275c9472",
            originH5Url: "https://www.migufun.com"
        },
        prod: {
            baseURL: "https://betagame.migufun.com/",
            loginUrl: "https://passport.migu.cn/",
            jsonUrl: "https://freeserver.migufun.com/resource/beta/media/jszt/",
            H5url: "https://www.migufun.com/",
            applyData: {
                appId: "migu",
                accessKey: "c9fa7d1b8b82b48e2ead44aecdaf1046",
                bizId: "lightplay_sdk_web",
                accessKeySecret: "81023b4219b5893d722bb706eac878aa"
            },
            accessInfo: {
                accessKey: "77a626f87a9ceac9b7d8cb03bb899007",
                accessKeyID: "1e536667ec3"
            },
            bundleUrl: "https://h5cdn.migufun.com/miguplay/html/miguplayUtils/bundle.js",
            PUBLICKEY: "04b40e07e63e44d5210d8b75dd0c3ddd7deaf4d4c2104066d63eb7053c34acda9117ba7029ffb9be98d42000680f4ddfcee706dd1959a9431de1c549fb7a821f3e",
            originH5Url: "https://www.migufun.com"
        },
        lib: {
            baseURL: "https://betagame.migufun.com/",
            loginUrl: "https://passport.migu.cn/",
            jsonUrl: "https://freeserver.migufun.com/resource/beta/media/jszt/",
            H5url: "https://www.migufun.com/",
            applyData: {
                appId: "migu",
                accessKey: "c9fa7d1b8b82b48e2ead44aecdaf1046",
                bizId: "lightplay_sdk_web",
                accessKeySecret: "81023b4219b5893d722bb706eac878aa"
            },
            accessInfo: {
                accessKey: "77a626f87a9ceac9b7d8cb03bb899007",
                accessKeyID: "1e536667ec3"
            },
            bundleUrl: "https://h5cdn.migufun.com/miguplay/html/miguplayUtils/bundle.js",
            PUBLICKEY: "04b40e07e63e44d5210d8b75dd0c3ddd7deaf4d4c2104066d63eb7053c34acda9117ba7029ffb9be98d42000680f4ddfcee706dd1959a9431de1c549fb7a821f3e",
            originH5Url: "https://www.migufun.com"
        }
    }[b];
    v();
    function O(t) {
        if (!document.getElementById("miguIframe")) {
            const c = document.createElement("iframe");
            c.id = "miguIframe",
                c.src = T.originH5Url + "/miguplay/html/appSystemHtml/interface/index.html",
                c.style.display = "none",
                document.body.appendChild(c)
        }
        let i = T.bundleUrl;
        C([i], ()=>L(this, null, function*() {
            t && t(),
                console.log("%c资源加载完成", "color:#00FF7F;font-size:12px")
        }))
    }
    function R(t) {
        const g = document.getElementById("miguIframe");
        return new Promise((i,c)=>{
                const w = Math.random().toString(36).substring(7)
                    , d = f=>{
                        (f.origin === "http://h5test.migufun.com:8008" || f.origin === "https://www.migufun.com") && f.data.type === "interface-response" && f.data.callbackId === w && (window.removeEventListener("message", d),
                            f.data.error ? (t.onError(JSON.parse(f.data.error)),
                                c(JSON.parse(f.data.error))) : (t.onSuccess(JSON.parse(f.data.response).data),
                                i(JSON.parse(f.data.response).data)))
                    }
                ;
                window.addEventListener("message", d),
                    g.contentWindow.postMessage({
                        type: "interface-request",
                        data: {
                            callbackId: w,
                            type: t.type,
                            url: t.url,
                            params: t.params
                        }
                    }, "*")
            }
        )
    }
    let P = {};
    P.dealOrder = t=>L(this, null, function*() {
        let g = {
            type: "post",
            url: "/member/calls/v1.1.0.8/dealOrder",
            params: {
                headers: {
                    appChannel: "zy-gw-mghy"
                },
                data: t
            },
            onSuccess: i=>{
                console.log("interface-success", i);
                let {resultData: c, returnCode: w} = i;
                if (w == "000000") {
                    let d = c.cipherText.split("/")
                        , f = t.backurl
                        , $ = c.price
                        , j = t.name
                        , x = t.type == 1 ? "01" : "02"
                        , k = c.pubChannelId ? c.pubChannelId : "10011000000"
                        , I = c.netId ? c.netId : "9532d4f105694bb28b0a66320b6a86ab"
                        , e = `https://g.10086.cn/miguplay/html/miniPay/index.html?channelCode=${k}&spServCode=${d[1]}&productID=${d[2]}&cpID=${d[3]}&spCode=${d[4]}&payType=7&operCode=${x}&fee=${$}&description=${j}&orderId=${c.orderId}&jsID=${d[1]}&netId=${I}&backUrl=${f}&payTitle=${j}&isVoucherHf=1`;
                    console.log(e),
                        window.location.href = e
                }
            }
            ,
            onError: i=>{
                console.log("interface-error", i)
            }
        };
        O(()=>{
                setTimeout(()=>{
                        R(g)
                    }
                    , 1e3)
            }
        )
    }),
        P.queryDealResult = t=>L(this, null, function*() {
            let g = {
                type: "post",
                url: "/member/calls/v1.1.0.8/queryDealResult",
                params: {
                    headers: {
                        appChannel: "zy-gw-mghy"
                    },
                    data: t
                },
                onSuccess: i=>{
                    console.log("interface-success", i)
                }
                ,
                onError: i=>{
                    console.log("interface-error", i)
                }
            };
            O(()=>{
                    setTimeout(()=>{
                            R(g)
                        }
                        , 1e3)
                }
            )
        }),
        y.pay = P,
        Object.defineProperty(y, Symbol.toStringTag, {
            value: "Module"
        })
});
