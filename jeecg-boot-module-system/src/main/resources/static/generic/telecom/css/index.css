body {
  margin: 0 auto;
}
body #main {
  padding: 0.8rem;
  margin: 0 auto;
}
body #main .headerNav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
body #main .headerNav .headerLogo .LogoImg {
  display: block;
  /* width: 6.5rem; */
  /* height: 7.4rem; */
}
body #main .headerNav .headerInfo {
  font-size: 1rem;
  color: #d6000f;
  font-weight: bold;
  text-align: right;
}
body #main .headerNav .headerInfo .InfoPrice {
  font-size: 0.8rem;
  display: inline-block;
  padding: 0.1rem 0.4rem;
  border-radius: 0.8rem;
  background-color: #ffe8e7;
}
body #main .informationFrame .FrameInput {
  display: flex;
  justify-content: space-between;
  padding: 0.4rem;
  background-color: #efefef;
  border-radius: 0.2rem;
  margin-top: 0.8rem;
  position: relative;
}
body #main .informationFrame .FrameInput .FrameTitle {
  width: 3rem;
  font-size: 0.8rem;
  font-weight: bolder;
  color: #555;
}
body #main .informationFrame .FrameInput input {
  border: none;
  outline: none;
  background: none;
  flex: 8;
  font-size: 0.8rem;
}
body #main .informationFrame .FrameInput .phoneYzm {
  font-size: 0.8rem;
  color: #ff262e;
}
body #main .Buttongroup {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.8rem;
}
body #main .Buttongroup .Button {
  font-size: 0.8rem;
  color: #ff262e;
  text-align: center;
  padding: 0 0.9rem;
  flex: 1;
  border-radius: 2rem;
  line-height: 2rem;
  border: 1px solid #ff262e;
  margin-right: 0.4rem;
}
body #main .Buttongroup .Button:last-child {
  margin: 0 ;
}
body #main .Buttongroup .backRed {
  color: #fff;
  background-color: #ff262e;
}
body #main .txt {
  font-size: 0.6rem;
  text-align: center;
  color: #888;
  margin-top: 0.4rem;
}
