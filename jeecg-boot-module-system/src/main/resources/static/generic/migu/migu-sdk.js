/**
 * 此js的函数必须在页面包含了咪咕的如下script才可以使用
 *
 */
// import Vue from "vue";
// import VueCookies from "vue-cookies";
// Vue.use(VueCookies);

//直接返回一个promise,不区分成功失败,调用方式如下:
// miguSdk.init(this.channelCode).then(function(resp) {
//   console.log(resp);
// });
// export const miguSdk = {
//   init: channelCode => {
//     return new Promise((resolve, _) => {
//       const callbackName = "openMiGuInitCallback";
//       window[callbackName] = resolve;
//       window["openMiGuInit"](channelCode, callbackName, "");
//     });
//   }
// };

//区分成功和失败,调用方式如下:
// miguSdk
// .init(this.channelCode)
// .then(function(resp) {
//   console.log("success", resp);
// })
// .catch(resp => {
//   console.log("error:", resp);
// });
const miguSdk = {
  //miguSdk公共参数
  commParam: {
    channelCode: "",
    serviceId: "",
    // msisdn: "",
    token: ""
  },
  // setCookieToken(token) {
  //     Vue.$cookies.set("miguSdkToken", token, 60 * 60 * 1);
  // },
  // getCookieToken() {
  //     return Vue.$cookies.get("miguSdkToken");
  // },
  //配置公共参数,配置后这些参数会在后面所有的方法调用中生效
  config(channelCode, serviceId, token) {
    this.commParam.channelCode = channelCode
      ? channelCode
      : this.commParam.channelCode;
    this.commParam.serviceId = serviceId ? serviceId : this.commParam.serviceId;
    this.commParam.token = token ? token : this.commParam.token;
  },
  //sdk初始化
  init(channelCode, customUiOrUmark = "") {
    return new Promise((resolve, reject) => {
      const callbackName = "openMiGuInitMiguSdkCallback";
      window[callbackName] = resp => {
        if (resp.resultCode && resp.resultCode === "0000") {
          console.log("openMiGuInit response=>", resp);
          resolve(resp);
        } else {
          console.error("openMiGuInit response=>", resp);
          reject(resp);
        }
      };
      console.log("openMiGuInit request param=>", channelCode, callbackName, customUiOrUmark);
      const initMethod = channelCode.indexOf("014X") === 0 ? "openMiGuInit_centrality" : "openMiGuInit";
      window[initMethod](channelCode, callbackName, customUiOrUmark);
    });
  },
  //调用miguSdk的函数,如果已使用config方法配置了公共参数,这些公共参数可以不用再在funcParam中传递
  exec(
    funcName,
    funcParam = {},
    isSuccess = resp => resp.resCode && resp.resCode === "000000"
  ) {
    return new Promise((resolve, reject) => {
      const callbackName = funcName + "MiguSdkCallback";
      window[callbackName] = resp => {
        if (isSuccess(resp)) {
          console.log(funcName + " respone=>", resp);
          resolve(resp);
        } else {
          console.error(funcName + " respone=>", resp);
          reject(resp);
        }
      };
      console.log(funcName + " request=>", JSON.stringify( { ...this.commParam, ...funcParam, ...{youCallbackName: callbackName}}));
      // 两种es6的对象合并方法
      // window[funcName](Object.assign({}, this.commParam, funcParam, {youCallbackName: callbackName}));
      // if(funcName==="miguOrderSubVrbt_centrality") {
      //   window[funcName]({ channelCode:this.commParam.channelCode,token:this.commParam.token, ...funcParam, ...{youCallbackName: callbackName}});
      // }
      window[funcName]({ ...this.commParam, ...funcParam, ...{youCallbackName: callbackName}});
    });
  }
};

// export default miguSdk;
