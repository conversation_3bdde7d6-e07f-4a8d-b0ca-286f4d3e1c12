@charset "utf-8";
html, body {overflow-x:hidden;}
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,code,pre,form,fieldset,legend,input,button,textarea,area,blockquote,th,td,p {margin:0; padding:0;}
.xtiper {position:relative;}
.xtiper_msg {transition-duration:0.2s; transition-timing-function:ease-out; transition-property:transform,opacity;}
.xtiper_msg {max-width:752px; position:fixed; left:50%; height:50px; line-height:50px; font-size:14px; border-radius:3px; overflow:hidden; z-index:99999; opacity:0; box-shadow:0 0 10px rgba(0,0,0,0.2);}
.xtiper_msg_black {background-color:rgba(0,0,0,0.75); color:#fff;}
.xtiper_msg_white {background-color:rgba(255,255,255,0.95); color:#222;}
.xtiper_msg_middle {top:50%; margin-top:-25px; transform:scale(0,0);}
.xtiper_msg_top {transform:translate(0, -100%) scale(1, 1); top:0;}
.xtiper_msg_bottom {transform:translate(0, 100%) scale(1, 1); bottom:0;}
.xtiper_msg.xon {opacity:1;}
.xtiper_msg_middle.xon {transform:scale(1,1);}
.xtiper_msg_top.xon {transform:translate(0, 0) scale(1, 1);}
.xtiper_msg_bottom.xon {transform:translate(0, 0) scale(1, 1);}
.xtiper_msg p {padding:0 24px; word-spacing:nowrap;}
.xtiper_danmu {border:3px solid transparent; left:0; transition-property:none; opacity:1; background-origin:content-box; font-size:16px; font-weight:bold;}
.xtiper_danmu_animate {animation:danmu_animate 6s 1 linear; -webkit-animation:danmu_animate 6s 1 linear;}
.xtiper_danmu_light {border:3px solid red;}

@keyframes danmu_animate
{
	100% {transform:translateX(-102%);}
}

.xtiper_tips {position:absolute; z-index:99999; max-width:200px; opacity:0; transform:scale(0,0); transition-duration:0.1s; transition-timing-function:ease-in; transition-property:opacity,transform; box-shadow:0 0 5px rgba(0,0,0,0.3);}
.xtiper_tips.xon {transition-duration:0.2s; opacity:1; transform:scale(1,1);}
.xtiper_tips p {padding:10px 16px; background-color:#000; border-radius:3px; color:#fff; line-height:18px; font-size:12px; position:relative; z-index:2;}
.xtiper_tips em {display:block; position:absolute; width:10px; height:10px; background-color:#000; transform: rotate(45deg); box-shadow:0 0 5px rgba(0,0,0,0.3); z-index:1;}
.xtiper_tips_left em {top:12px; right:-4px;}
.xtiper_tips_right em {top:12px; left:-4px;}
.xtiper_tips_top em {left:12px; bottom:-4px;}
.xtiper_tips_bottom em {left:12px; top:-4px;}

.xtiper_win {z-index:99999;}
.xtiper_win_fixed {width:100%; height:100%; position:fixed; top:0; left:0; z-index:99999;}
.xtiper_bg {width:100%; height:100%; position:absolute; top:0; left:0; z-index:1; background-color:rgba(0,0,0,0); transition-duration:0.1s; transition-timing-function:ease-in; transition-property:background-color,opacity;}
.xtiper_bg_white {background-color:rgba(255,255,255,0); transition:0.05s ease-in;}
.xtiper_win.xon .xtiper_bg {background-color:rgba(0,0,0,0.5);}
.xtiper_win.xon .xtiper_bg_white {background-color:rgba(255,255,255,0.5);  transition:0.05s ease-in;}
.xtiper_main {width:300px; background-color:#fff; position:absolute; box-shadow:0 0 10px rgba(0,0,0,0.2); z-index:2; opacity:0; transition-duration:0.15s;  transition-timing-function:ease-in; transition-property:opacity,transform; transform:scale(0,0);}
.xtiper_main.xapp {background-color:transparent;}
.xtiper_main_photo {transition-property:opacity,transform,height,top;}
.xtiper_main_photo.xon {transition-property:opacity,transform,height;}
.xtiper_win.xon .xtiper_main {transform:scale(1,1); opacity:1;}
.xtiper_win.xon .xtiper_main, .xtiper_win.xon .xtiper_bg {transition-duration:0.2s; transition-timing-function:ease-in;}
.xtiper_win.xoff .xtiper_bg {opacity:0;}
.xtiper_win.xoff .xtiper_main {transform:scale(0,0); opacity:0;}
.xtiper_tit {height:40px; line-height:40px; color:#222; font-size:14px; background-color:#f8f8f8; padding:0 18px; position:relative; user-select:none; cursor:move;}
.xtiper_tit:after {content:""; display:block; width:100%; height:1px; background-color:#eee; position:absolute; bottom:0; left:0;}
.xtiper_tit_none {height:8px; background-color:#f0f0f0;}
.xtiper_tit.xminmax {cursor:default;}
.xtiper_tit p {text-overflow:ellipsis; overflow:hidden; white-space:nowrap; margin-right:28px;}
.xtiper_tit.xmcss1 p {margin-right:62px;}
.xtiper_tit.xmcss2 p {margin-right:96px;}
.xtiper_tit.xmin p {margin-right:62px;}

.xtiper_minmax {position:absolute; top:5px; right:6px;}
.xtiper_close {z-index:10;}
.xtiper_close, .xtiper_min, .xtiper_max {width:30px; height:30px; cursor:pointer; box-sizing:border-box; margin-right:4px; float:left; background-size:18px; background-repeat:no-repeat; background-position:center center; position:relative;}
.xtiper_minmax div:last-child {margin-right:0;}
.xtiper_close:before, .xtiper_close:after {content:""; display:block; width:14px; height:2px; background-color:#2d2c3b; position:absolute; top:14px; left:8px;}
.xtiper_close:before {transform:rotate(45deg);}
.xtiper_close:after {transform:rotate(-45deg);}
.xtiper_close:hover, .xtiper_min:hover, .xtiper_max:hover {opacity:0.8;}
.xtiper_close:before, .xtiper_close:after {transition-duration:0.2s; transition-timing-function:ease-in; transition-property:transform;}
.xtiper_close:hover:before {transform:rotate(135deg);}
.xtiper_close:hover:after {transform:rotate(45deg);}
.xtiper_close_notit {margin-right:0; background-color:#333; border:3px solid #fff; position:absolute; top:-10px; right:-10px; border-radius:50%; transition-duration:0.2s; transition-timing-function:ease-in; transition-property:background-color;}
.xtiper_close_notit:before, .xtiper_close_notit:after {background-color:#fff; top:11px; left:5px;}
.xtiper_close_notit:hover {opacity:1; background-color:#2b84d0;}
.xtiper_close_notitmin:before, .xtiper_close_notitmin:after {top:7px; left:3px; width:10px;}
.xtiper_close_notitmin {width:20px; height:20px; border-width:2px}
.xtiper_close_photoapp {top:6px; right:6px; background-color:transparent;}
.xtiper_close_photoapp:hover {background-color:transparent;}
.xtiper_close_photoapp:hover:before {transform:rotate(45deg);}
.xtiper_close_photoapp:hover:after {transform:rotate(-45deg);}

.xtiper_photo {position:relative; overflow:hidden; transition-property:transform,opacity;}
.xtiper_photo_ul {padding:0 60px; height:100%;}
.xtiper_photo_ul ul {height:calc(100% - 26px); position:relative; overflow:hidden;}
.xtiper_photo_li {width:100%; height:100%; position:absolute; top:0; left:0; text-align:center; opacity:0; transform:scale(0, 0); transition-duration:0.3s; transition-timing-function:ease-out; transition-property:transform,opacity;}
.xtiper_photo_li.xon {opacity:1; transform:scale(1, 1);}
.xtiper_photo_li.xold_prev {transform:scale(1, 1) translate(100%, 0);}
.xtiper_photo_li.xold_next {transform:scale(1, 1) translate(-100%, 0);}

.xtiper_sheet {width:100%; background-color:#fff; bottom:0; left:0; position:absolute; z-index:2; transition:0.1s ease-in; transition-property:transform; transform:translate(0, 100%);}
.xtiper_sheet_ul, .xtiper_sheet_tit {background-color:#efeff4;}
.xtiper_win.xon .xtiper_sheet {transition:0.2s ease-in; transform:translate(0, 0);}
.xtiper_win.xoff .xtiper_sheet {transform:translateY(100%);}
.xtiper_sheet_left {text-align:left;}
.xtiper_sheet_right {text-align:right;}
.xtiper_sheet_center {text-align:center;}
.xtiper_sheet_tit {padding:15px; font-size:15px; color:#222; line-height:20px; font-weight:bold;}
.xtiper_sheet_li {font-size:14px; height:48px; line-height:48px; background-color:#fff; border-top:1px solid #d9d9d9; cursor:pointer; color:#222; user-select:none; -ms-user-select:none;}
.xtiper_sheet_li a {display:block; width:100%; color:#222; text-decoration:none;}
.xtiper_sheet_li a:hover {color:#222; text-decoration:none;}
.xtiper_sheet_li p {padding:0 15px;}
.xtiper_sheet_li.xlast {margin-top:8px; border-top:0;}

.xtiper_min {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M949.2 560.2H74.8c-26.6 0-48.2-21.6-48.2-48.2 0-26.6 21.6-48.2 48.2-48.2H949.2c26.6 0 48.2 21.6 48.2 48.2 0 26.6-21.6 48.2-48.2 48.2z' fill='%232d2c3b'/%3E%3C/svg%3E");}
.xtiper_min.xon {margin-right:0; background-image:url("data:image/svg+xml,%3Csvg width='2000' height='2000' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M879.1 783.6h-60.4a34.3 34.3 0 0 1 0-68.6h60.4c14.3 0 26.6-11.8 26.6-26.6V142.6c0-14.3-11.8-26.6-26.6-26.6H332.8c-14.3 0-26.6 11.8-26.6 26.6v60.4a34.3 34.3 0 0 1-68.6 0V142.6c0-52.2 42.5-94.72 94.72-94.7h546.3c52.2 0 94.72 42.5 94.72 94.7v546.3c0.5 52.2-42.5 94.72-94.2 94.7z' fill='%232c2c2c'/%3E%3Cpath d='M708.1 964.3H162.3c-57.9 0-103.9-47.1-103.9-103.9V312.6c0-57.9 47.1-103.9 103.9-103.9h546.3c57.9 0 103.9 47.1 103.9 103.9v546.8c0.5 58.4-46.08 104.96-104.4 104.96zM162.3 276.72c-19.5 0-35.84 16.4-35.84 35.8v546.8c0 19.5 16.4 35.84 35.84 35.84h546.3c19.5 0 35.84-16.4 35.84-35.84V312.6c0-19.5-16.4-35.84-35.84-35.8H162.3z' fill='%232d2c3b'/%3E%3C/svg%3E");}
.xtiper_max {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M944.0 0.8H79.1C35.8 0.8 0.3 36.2 0.3 79.4v864.5c0 43.2 35.4 78.6 78.7 78.6h865.9c43.3 0 78.7-35.4 78.7-78.6V79.4C1023.7 36.2 988.3 0.8 944.0 0.8z m0 943.1H79.1V79.4h865.9v864.5z m0 0' fill='%23231814'/%3E%3Cpath d='M342.8 735.7l137.8-137.5c15.7-15.7 15.7-39.3 0-55.0-15.7-15.7-39.4-15.7-55.1 0l-137.8 137.5-90.5-90.4V826.0h236.2l-90.5-90.4z m228.3-243.6c11.8 0 19.7-3.9 27.6-11.8l137.8-137.5 90.5 90.4V197.3H590.7l90.5 90.4-137.8 137.5c-15.7 15.7-15.7 39.3 0 55.0 7.9 7.9 19.7 11.8 27.5 11.8z m0 0' fill='%232d2c3b'/%3E%3C/svg%3E");}
.xtiper_max.xon {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M944.0 0.8H79.1C35.8 0.8 0.3 36.2 0.3 79.4v864.5c0 43.2 35.4 78.6 78.7 78.6h865.9c43.3 0 78.7-35.4 78.7-78.6V79.4C1023.7 36.2 988.3 0.8 944.0 0.8z m0 943.1H79.1V79.4h865.9v864.5z m0 0' fill='%23231814'/%3E%3Cpath d='M346.7 621.7l-137.8 137.5c-15.7 15.7-15.7 39.3 0 55.0 15.7 15.7 39.4 15.7 55.1 0l137.8-137.5 90.5 90.4V531.3H256.2l90.5 90.4zM787.5 197.3c-11.8 0-19.7 3.9-27.6 11.8l-137.8 137.5-90.5-90.4v235.8h236.2l-90.5-90.4 137.8-137.5c15.7-15.7 15.7-39.3 0-55.0-7.9-7.9-19.7-11.8-27.5-11.8z' fill='%232d2c3b'/%3E%3C/svg%3E");}
.xtiper_times {color:#e85445;}
.xtiper_pad {padding:15px;}
.xtiper_tip {font-size:14px; line-height:20px; min-height:60px; overflow:hidden;}
.xtiper_btn ul {text-align:center; display:flex; overflow:hidden; border-top:1px solid #eaeaea; background-color:#fff;}
.xtiper_btn ul:after {content:""; display:block; clear:both;}
.xtiper_btn li {float:left; overflow:hidden; position:relative;}
.xtiper_btn li:after {content:""; display:block; width:1px; height:40px; overflow:hidden; position:absolute; top:0; right:0; background-color:#eaeaea;}
.xtiper_btn li.xactive:after {background-color:rgba(255,255,255,0.2);}
.xtiper_btn1 li {width:100%; float:none;}
.xtiper_btn2 li {width:50%;}
.xtiper_btn3 li {width:33.3%; -webkit-box-flex:1; -ms-flex:1; flex:1;}
.xtiper_btn4 li {width:25%; -webkit-box-flex:1; -ms-flex:1; flex:1;}
.xtiper_btn li button {width:100%; height:40px; background-color:#fff; cursor:pointer; color:#222; font-size:14px; border:0;}
.xtiper_btn li.xactive button {color:#fff;}
.xtiper_btn li.xactive button {background-color:#a8a8a8;}
.xtiper_btn_success li.xactive button, .xtiper_btn_hello li.xactive button, .xtiper_btn_success .xtiper_btnbor, .xtiper_btn_hello .xtiper_btnbor {background-color:#37b72c;}
.xtiper_btn_error li.xactive button, .xtiper_btn_error .xtiper_btnbor {background-color:#e75445;}
.xtiper_btn_warning li.xactive button, .xtiper_btn_ask li.xactive button, .xtiper_btn_warning .xtiper_btnbor, .xtiper_btn_ask .xtiper_btnbor {background-color:#f89310;}
.xtiper_btn1 li.xactive button {background-color:#fff; color:#222;}
.xtiper_btn li:last-child:after {display:none;}
.xtiper_btn li button:hover {opacity:0.9;}
.xtiper_btnbor {width:100%; height:4px; overflow:hidden; background-color:#c3c3c3; position:relative;}
.xtiper_btnbor:after {content:""; display:block; width:100%; height:4px; background-color:rgba(0,0,0,0.1); position:absolute; top:0; left:0;}
.xtiper_btn1 .xtiper_btnbor:after {display:none;}
.xtiper_btn2 li:nth-child(2) {opacity:0.92;}
.xtiper_btn3 li:nth-child(1) {opacity:0.84;}
.xtiper_btn3 li:nth-child(2) {opacity:0.92;}
.xtiper_btn4 li:nth-child(1) {opacity:0.76;}
.xtiper_btn4 li:nth-child(2) {opacity:0.84;}
.xtiper_btn4 li:nth-child(3) {opacity:0.92;}
.xtiper_pr {position:relative;}
.xtiper_content {height:100%; overflow:auto; position:relative;}
.xtiper_content.xtit {height:calc(100% - 40px);}
.xtiper_content.xmin {height:calc(100% - 8px);}
.xtiper_over {overflow:hidden;}
.xtiper_content .zw {width:100%; height:100%; overflow:hidden; position:absolute; top:0; left:0; display:none;}
.xtiper_main.xon .xtiper_content .zw {display:block;}
.xtiper_con {padding-top:8px; padding-bottom:12px;}
.xtiper_con_icon {padding-left:42px;}
.xtiper_conin {max-height:400px; overflow-x:hidden; overflow-y:auto;}
.xtiper_conin::-webkit-scrollbar {width:5px;}
.xtiper_conin::-webkit-scrollbar-track {background-color:#f1f1f1; border-radius:3px;}
.xtiper_conin::-webkit-scrollbar-thumb {background-color:#c1c1c1; border-radius:3px;}
.xtiper_loadin {width:34px; height:34px; display:block; position:absolute; top:50%; margin-top:-17px; left:50%; margin-left:-17px; opacity:0; transition:0.1s ease-in; transition-property:opacity; z-index:2;}
.xtiper_loadin span {width:280px; position:absolute; top:36px; left:50%; margin-left:-140px; text-align:center; height:24px; line-height:24px; font-size:14px; color:#222; white-space:nowrap;}
.xtiper_win.xon .xtiper_loadin {opacity:1; transition:0.2s ease-in;}
.xtiper_icon {width:34px; height:34px; display:inline-block; background-size:100% 100%; background-repeat:no-repeat; position:absolute; top:0; left:0; vertical-align:middle;}
.xtiper_icon img {width:34px; height:34px;}
.xtiper_icon_min {width:20px; height:20px; position:static; top:auto; left:auto; transform:translateY(-1px); margin-right:5px;}
.xtiper_icon_min img {width:20px; height:20px;}
.xtiper_icon_success {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M512.7 0.1C230.4 0.1 0.8 229.8 0.8 512.1c0 282.3 229.6 511.9 511.0 511.9 282.3 0 511.9-229.6 511.9-511.9C1024.6 229.8 795.0 0.1 512.7 0.1z m0 950.5c-241.8 0-438.5-196.7-438.5-438.5 0-241.8 196.7-438.5 438.5-438.5 241.7 0 438.5 196.6 438.5 438.5-0.0 241.7-196.8 438.5-438.5 438.5z' fill='%2337b72c'/%3E%3Cpath d='M754.8 337.0L470.1 630.1l-178.5-155.2c-15.8-13.7-39.7-12.0-53.4 3.7-13.7 15.8-12.0 39.7 3.7 53.4l205.2 178.4a37.7 37.7 0 0 0 10.9 6.7 37.8 37.8 0 0 0 15.9 2.7 37.9 37.9 0 0 0 15.2-3.0c0.0-0.0 0.0 0.0 0.0-0.0a37.7 37.7 0 0 0 10.4-7.7l309.4-317.5c14.6-14.0 14.3-38.9-0.7-53.5-14.0-14.6-38.9-14.3-53.5 0.7z' fill='%2337b72c'/%3E%3C/svg%3E");}
.xtiper_icon_error {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M512.7 1023.0C230.4 1023.0 0.7 794.4 0.7 512.1 0.8 229.8 230.4 0.1 512.7 0.1c282.3 0 511.9 229.6 511.9 511.0 0 282.2-229.6 511.9-511.9 511.9z m0-950.4c-241.8 0-438.5 196.6-438.5 438.5 0 241.7 196.7 438.5 438.5 438.5 241.7 0 438.5-196.7 438.5-438.5 0-241.8-196.8-438.5-438.5-438.5z m51.4 439.0l158.1-156.3c14.4-14.2 14.5-37.4 0.3-51.7-14.2-15.4-37.4-14.5-52.6-0.3l-157.4 156.5-155.9-156.4c-15.3-15.2-37.4-14.3-51.7 0-14.3 14.2-14.3 37.4 0 51.7l155.7 156.1-157.0 155.3c-14.4 14.2-14.5 37.3-0.3 51.7 7.2 7.2 16.6 10.9 25.0 10.9 9.3 0 18.6-3.6 25.8-10.6l157.2-155.5 158.4 159.0c7.2 6.2 16.6 10.7 25.0 10.7 9.3 0 18.7-4.5 25.8-10.6 14.3-14.3 14.4-37.4 0.1-51.7l-158.3-158.8z' fill='%23e75445'/%3E%3C/svg%3E");}
.xtiper_icon_warning {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M512.7 0.1C230.4 0.1 0.8 229.8 0.8 512.1c0 282.3 229.6 511.9 511.0 511.9 282.3 0 511.9-229.6 511.9-511.9C1024.6 229.8 795.0 0.1 512.7 0.1z m0 950.5c-241.8 0-438.5-196.7-438.5-438.5 0-241.8 196.7-438.5 438.5-438.5 241.7 0 438.5 196.6 438.5 438.5 0 241.7-196.8 438.5-438.5 438.5z' fill='%23f89310'/%3E%3Cpath d='M511.6 681.4c-15.5 0-28.6 5.4-39.4 16.1s-16.1 23.7-16.1 38.0c0 17.5 5.6 31.1 16.8 40.8 11.2 9.7 24.3 14.6 39.4 14.6 14.8 0 27.7-4.9 38.8-14.8 11.1-9.9 16.6-23.4 16.6-40.6 0-15.3-5.5-28.3-16.4-38.0-10.0-10.7-24.2-16.1-39.7-16.1zM514.5 232.4c-17.5 0-31.6 5.7-42.3 17.2s-16.1 27.4-16.1 47.9c0 15.0 1.1 39.8 3.3 74.3l11.8 177.0c2.2 22.9 5.9 39.0 11.1 51.2 5.2 11.2 14.4 16.8 27.7 16.8 13.1 0 22.4-5.8 28.1-17.4 5.7-11.6 9.4-28.2 11.1-49.9l15.9-182.2c1.7-16.7 2.6-33.3 2.6-49.5 0-27.6-3.6-48.7-10.7-63.4-7.2-14.7-21.3-21.0-42.5-21.0z' fill='%23f89310'/%3E%3C/svg%3E");}
.xtiper_icon_ask {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M512.7 0.1C230.4 0.1 0.8 229.8 0.8 512.1c0 282.3 229.6 511.9 511.0 511.9 282.3 0 511.9-229.6 511.9-511.9C1024.6 229.8 795.0 0.1 512.7 0.1z m0 950.5c-241.8 0-438.5-196.7-438.5-438.5 0-241.8 196.7-438.5 438.5-438.5 241.7 0 438.5 196.6 438.5 438.5 0 241.7-196.8 438.5-438.5 438.5z' fill='%23f89310'/%3E%3Cpath d='M513.9 681.3c-15.8 0-29.1 5.3-39.9 15.9-10.8 10.6-16.3 23.7-16.3 39.2 0 17.5 5.6 31.1 16.8 40.8 11.2 9.7 24.3 14.6 39.4 14.6 14.5 0 27.3-4.9 38.4-14.8 11.1-9.9 16.6-23.4 16.6-40.6 0-15.5-5.3-28.6-15.9-39.2-10.6-10.6-23.7-15.9-39.2-15.9zM617.4 252.0c-27.3-13.2-58.9-19.8-94.6-19.8-38.4 0-72.1 7.9-100.9 23.7-28.8 15.8-50.8 35.7-65.8 59.7-15.0 24.0-22.5 47.7-22.5 71.1 0 11.3 4.7 21.9 14.2 31.6 9.5 9.7 21.1 14.6 34.9 14.6 23.4 0 39.3-13.9 47.7-41.8 8.9-26.6 19.7-46.7 32.5-60.4 12.8-13.7 32.8-20.5 59.9-20.5 23.2 0 42.1 6.8 56.7 20.3 14.7 13.6 21.0 30.2 21.0 49.9 0 10.1-2.4 19.5-7.2 28.1-4.8 8.6-10.7 16.4-17.7 23.5s-18.4 17.4-34.2 31.2c-17.0 15.8-32.3 29.4-42.9 40.8-10.6 11.5-19.1 24.8-25.5 39.9-6.4 15.2-9.6 33.1-9.6 53.8 0 16.5 4.4 28.0 13.1 37.3 8.7 8.4 19.5 12.6 32.3 12.6 24.6 0 39.3-12.8 43.0-38.4 2.7-12.1 4.7-20.5 6.1-25.3 1.4-4.8 3.3-9.6 5.7-14.4 2.5-4.8 6.2-10.1 11.3-15.9 5.0-5.8 11.8-12.5 20.1-20.1 30.3-27.1 51.3-46.4 63.0-57.8 11.7-11.5 21.8-25.1 30.3-40.8 8.5-15.8 12.7-34.1 12.7-55.1 0-26.6-7.5-51.2-22.4-73.9-14.9-22.7-36.0-40.6-63.4-53.8z' fill='%23f89310'/%3E%3C/svg%3E");}
.xtiper_icon_hello {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M512.7 0.1C230.4 0.1 0.8 229.8 0.8 512.1c0 282.3 229.6 511.9 511.0 511.9 282.3 0 511.9-229.6 511.9-511.9C1024.6 229.8 795.0 0.1 512.7 0.1z m0 950.5c-241.8 0-438.5-196.7-438.5-438.5 0-241.8 196.7-438.5 438.5-438.5 241.7 0 438.5 196.6 438.5 438.5-0.0 241.7-196.8 438.5-438.5 438.5z' fill='%2337b72c'/%3E%3Cpath d='M355.5 478.0c9.8-9.7 15.2-22.5 15.2-36.2v-72.1c0-27.0-22.8-50.8-50.7-50.8-28.0 0-50.8 22.8-50.8 50.8v72.1c0 27.0 22.8 50.8 50.8 50.8 13.3 0 25.9-5.1 35.5-14.6zM706.5 615.4c-13.7-11.8-37.6-8.9-49.4 4.9-41.6 46.3-84.9 65.0-145.1 65.0-62.8 0-100.7-17.2-144.8-65.7-12.2-14.2-35.0-17.0-49.7-5.2-15.1 13.1-17.4 35.3-5.0 49.9 59.1 66.8 114.9 92.8 199.5 92.8 82.0 0 145.4-29.5 199.6-92.9 12.1-13.0 9.8-36.7-5.1-49.6zM704.0 319.8c-28.0 0-50.7 22.8-50.7 50.8v72.1c0 28.1 22.8 50.8 50.7 50.8 28.0 0 50.7-22.8 50.7-50.8v-72.1c0-27.0-22.8-50.8-50.7-50.8z' fill='%2337b72c'/%3E%3C/svg%3E");}
.xtiper_icon_load {background-image:url("data:image/svg+xml,%3Csvg width='600' height='600' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M979.73 513.01c-0.96-1.70-2.66-2.65-4.46-2.65h-69.90c0-84.45-26.02-162.63-70.11-227.32-0.53-0.85-0.95-1.91-1.52-2.76-4.57-6.59-9.67-11.69-14.55-19.02-1.81-2.37-3.51-4.78-5.42-7.12-7.22-8.92-14.87-17.20-22.73-25.38-0.74-0.85-1.49-1.59-2.23-2.34-25.50-26.88-56.41-49.50-89.13-67.56-0.83-0.53-1.70 0-2.55-1.49-9.35-5.10-19.02-9.77-28.89-14.13-2.44 0-4.78-2.23-7.22-3.29-8.39-3.51-15.94-6.59-25.6-8.50-4.15-1.38-8.18-2.86-12.32-4.15-7.54-2.23-15.30-4.16-23.05-6.05-5.21-1.17-10.31-2.55-15.62-3.51-2.12-0.42-3.18-1.17-6.37-1.49-7.33-1.28-14.77-2.02-22.10-2.98-2.65-0.30-4.25-0.74-7.91 0-13.28-1.27-26.45-2.23-39.63-2.23-80.51 0-159.12 24.96-226.58 72.76-21.46 15.30-26.77 45.25-11.68 67.02 15.09 21.78 44.72 27.09 66.28 11.79l3.31-0.71c52.37-36.11 112.60-55.77 176.33-54.92a329.67 329.67 0 0 1 24.64 1.24c3.29 0.32 6.59 0.85 9.99 1.27 6.37 0.85 12.75 1.70 18.00 2.97 3.83 0.75 7.65 1.70 11.37 2.66 6.16 1.48 12.20 2.92 18.16 4.68 2.87 0.85 5.64 1.91 8.50 2.86 6.90 2.34 13.70 4.78 20.39 7.54 1.49 0.64 2.98 1.38 4.46 2.02 7.86 3.47 15.51 7.11 22.94 11.15 0.32 0.21 0.64 0.32 0 0.53 25.18 14.02 48.23 31.55 68.52 51.95 0.32 0.32 0.63 0.74 0.96 0 6.27 5.31 12.32 13.06 17.95 19.97 1.17 1.48 2.34 3.08 3.51 4.56 41.22 52.16 66.07 118.12 66.07 189.93h-69.89c-1.81 0-3.51 0.96-4.46 2.65-0.96 1.70-0.74 3.72 0.21 5.21l117.38 178.24c0.96 1.38 2.44 2.33 3.19 2.33 1.70 0 3.29-0.95 3.19-2.33l117.38-178.24c0.96-1.52 1.17-3.51 0.21-5.21zM687.56 761.37l-3.30 0.71c-52.37 36.11-112.60 55.77-176.33 54.92a329.29 329.29 0 0 1-24.64-1.25c-3.29-0.32-6.59-0.85-9.99-1.27-6.37-0.85-12.75-1.70-19.00-2.97-3.83-0.74-7.65-1.70-11.36-2.66-6.17-1.48-12.20-2.92-18.16-4.67-2.87-0.85-5.64-1.91-8.50-2.87-6.90-2.33-13.70-4.78-20.39-7.54-1.49-0.64-2.98-1.38-4.46-2.02-7.86-3.47-15.51-7.11-22.94-11.15-0.32-0.21-0.64-0.32 0-0.53-25.18-14.02-48.22-31.55-68.52-51.95-0.32-0.32-0.63-0.74-0.95 0-6.27-5.31-12.32-13.06-17.95-19.97-1.17-1.49-2.35-3.08-3.51-4.56-41.21-52.16-66.07-118.12-66.07-189.93h69.89c1.81 0 3.51-0.96 4.46-2.65 0.96-1.70 0.74-3.72-0.21-5.21L168.24 327.54c-0.96-1.38-2.45-2.34-3.19-2.34-1.70 0-3.29 0.95-3.19 2.34L44.48 505.79c-0.95 1.52-1.17 3.50-0.22 5.21 0.96 1.70 2.66 2.65 4.46 2.65h69.90c0 84.45 26.02 162.63 70.11 227.32 0.53 0.85 0.95 1.91 1.52 2.76 4.57 6.59 9.67 11.69 14.55 19.02 1.81 2.37 3.51 4.78 5.42 7.12 7.22 8.92 14.87 17.20 22.73 25.38 0.74 0.85 1.49 1.59 2.23 2.34 25.50 26.87 56.41 49.50 89.13 67.55 0.83 0.53 1.70 0 2.55 1.49 9.35 5.10 19.02 9.77 28.90 14.13 2.44 0 4.78 2.23 7.22 3.29 8.39 3.51 15.94 6.59 25.60 8.50 4.14 1.38 8.18 2.87 12.32 4.15 7.54 2.23 15.30 4.16 23.05 6.05 5.21 1.17 10.30 2.55 15.62 3.50 2.12 0.42 3.19 1.17 6.37 1.49 7.33 1.28 14.77 2.02 22.10 2.97 2.66 0.30 4.25 0.75 7.91 0 13.28 1.28 26.45 2.23 39.63 2.23 80.51 0 159.12-24.96 226.58-72.76 21.45-15.30 26.77-45.26 11.68-67.02-15.08-21.78-44.72-27.09-66.28-11.79z' fill='%23333333'/%3E%3C/svg%3E");}
.xtiper_icon_load {animation:revolve 1.5s infinite linear; -webkit-animation:revolve 1.5s infinite linear;}
@keyframes revolve
{
	0% {transform:rotate(0deg);}
	100% {transform:rotate(360deg);}
}

@media (max-width: 760px) {
	.xtiper_msg {max-width:80%;}
}
@media (max-width: 480px) {
	.xtiper_msg {max-width:300px;}
	.xtiper_photo_ul {padding:0;}
	.xtiper_photo_btn {display:none;}
}

.xtiper_photo_li p {width:100%; max-height:100%; min-height:100px; position:absolute; top:50%; transform:translate(0, -50%); background-position:center center; background-repeat:no-repeat; background-size:contain; background-color:#fff;}
.xtiper_photo_li a {display:block; width:100%; height:100%; position:relative; z-index:2;}
.xtiper_photo_load {position:absolute; top:50%; left:50%; margin-left:-17px; margin-top:-17px; z-index:1;}
.xtiper_photo_li img {max-width:100%; visibility:hidden;}
.xtiper_photo_li.xapp img {width:100%; visibility:visible; position:relative; z-index:2;}
.xtiper_photo_btn {position:absolute; top:50%; transform:translate(0, -50%); width:32px; height:46px; overflow:hidden; cursor:pointer; z-index:2;}
.xtiper_photo_prev {left:16px;}
.xtiper_photo_next {right:16px;}
.xtiper_photo_btn:before, .xtiper_photo_btn:after {content:""; display:block; width:26px; height:4px; background-color:#666; position:absolute; border-radius:4px; z-index:1;}
.xtiper_photo_btn:hover {opacity:0.7;}
.xtiper_photo_prev:before {transform:rotate(-45deg); top:13px; left:3px;}
.xtiper_photo_prev:after {transform:rotate(45deg); top:29px; left:3px;}
.xtiper_photo_next:before {transform:rotate(45deg); top:13px; right:3px;}
.xtiper_photo_next:after {transform:rotate(-45deg); top:29px; right:3px;}
.xtiper_photo_num {position:absolute; bottom:5px; text-align:center; left:50%; transform:translate(-50%, 0); font-size:13px; z-index:10; height:26px; line-height:26px; overflow:hidden; user-select:none; -ms-user-select:none;}
.xtiper_nummax {padding-left:8px;}
.xtiper_photo_num, .xtiper_nummax {display:none;}
.xtiper_photo_num.xon, .xtiper_nummax.xon {display:inline-block;}
