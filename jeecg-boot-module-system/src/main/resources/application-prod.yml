server:
  port: 9547
  tomcat:
    max-swallow-size: -1
    #  解决springmvc接收unicode参数报400错误的问题 https://stackoverflow.com/questions/46251131/invalid-character-found-in-the-request-target-in-spring-boot
    #    relaxed-query-chars: '\,|,{,},[,]'
    relaxed-query-chars: '\,|'
  servlet:
    context-path: /cms-vrbt-miniapp
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
ai-mini-app:
  manager:
    app-id-manager:
      dyAivrbtMiniApp_1 : tt35db4366aaefe3a301
      dyAivrbtMiniApp_2 : tt79fe0f43e6cf32c401
      dyAivrbtMiniApp_3 : tt5b2d2d90c427e29301
      dyAivrbtMiniApp_4 : tt58062ebbc70c5a5c01
      mgaispcl: tt3f5349eea058c2b801
      mgspcl: tt3f5349eea058c2b801
      lyhz: tt0f3d79809036f28501
      clhz: tt6a901c2df59b8ed701
      clgf: tt79fe0f43e6cf32c401
    secret-manager:
      dyAivrbtMiniApp_1 : 0c88966f5b259cef066e0ca894a78c565fbbb756
      dyAivrbtMiniApp_2 : 92a13ee4918832dc7ec3a7335bd4b0fcaa3430df
      dyAivrbtMiniApp_3 : 9c88b80560acafa21712eb2ba062112b91f53e18
      dyAivrbtMiniApp_4 : 4405a4c61b8f4accfa46561c66d816ecc97d892f
      mgaispcl: 44899c422d99b22f2aa8492d84461b20af14b533
      mgspcl: 44899c422d99b22f2aa8492d84461b20af14b533
      lyhz: 4e28a6c96ff931721cb17dae6f8c43d951e81dfa
      clhz: 9a84ae4532ef986607da3d20f1935338d14bf2b0
      clgf: 92a13ee4918832dc7ec3a7335bd4b0fcaa3430df
aigc:
  migu:
    ai-rights-uri: /open/api/ringbacktone/ai-ability/remain-count/v1.0
    pre-reduce-count-uri: /open/api/user/ai-charging/pre/reduce/v1.0
    cancel-task-uri: /open/api/user/ai-charging/cancel/task/v1.0
    report-result-uri: /open/api/user/ai-charging/report/result/v1.0
    host-name: ***************:31010
    app-id: 4e616d0bb6d56f8e7030dd174404806f
    app-secret: b20e88feded99b9ca40e3a2daafc4490
    protocol: http://
spring:
  elasticsearch:
    rest:
      uris: **************:9200
      username: data_in
      password: Hstj@2021
      connection-timeout: 60000
      read-timeout: 120000
  servlet:
     multipart:
        max-file-size: 80MB
        max-request-size: 80MB
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  # quartz定时任务,采用数据库方式,jeecgboot集成的quartz定时任务有bug,跑着跑着就不执行了,所以使用spring自带的比较好
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #设置自动启动，默认为 true
    auto-startup: false
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 1000
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
      datasource:
#        master:
#          url: *******************************************************************************************************************
#          username: root
#          password:
#          driver-class-name: com.mysql.jdbc.Driver
#        #多数据源配置
#        slave:
#          url: ************************************************************************************************************************
#          username: root
#          password:
#          driver-class-name: com.mysql.jdbc.Driver
        master:
          url: *****************************************************************************************************************************************************
          username: vrbt_admin
          password: Qa2pJHx7nGidkuYgz_oMBpAs
          driver-class-name: com.mysql.jdbc.Driver
        #多数据源配置
#        slave:
#          url: **********************************************************************************************************************************************************
#          username: crbt_admin
#          password: 6JTKTgqkumLDw6bQZSz8w_Si
#          driver-class-name: com.mysql.jdbc.Driver
        xxl:
          url: ****************************************************************************************************************************************************************************
          username: xxl_admin
          password: x_KVR7KWCVEE_6pkd7TeJ433
          driver-class-name: com.mysql.jdbc.Driver
        # 百度网盘会员数据库
        member:
          username: member_admin
          password: RY2_PqmbOt_WiAlxeXWYcHg8
          driver-class-name: com.mysql.jdbc.Driver
          url: *************************************************************************************************************************************************************************************************************
        miniapp:
          username: miniapp_admin
          password: VqCmJLOq3ySI7jZoUfk9p52pBAfKCJ1c
          driver-class-name: com.mysql.jdbc.Driver
          url: *********************************************************************************************************************************************************************************************************
        centric:
          url: **********************************************************************************************************************************************
          username: centric_admin
          password: zk5m9tFEpft!c5eapSNqn#@zdfNAbp9F
          driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 0
    host: **************
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: jY.Zx#YbL9
    port: 16379
  #rabbitmq 配置
  rabbitmq: # http://***************:15672/
    host: ************** #生产环境使用内网ip
    port: 5672
    username: hstj
    password: YFCmvzHAAcrv_sxi
    publisher-confirm-type: correlated #开启交换机确认功能
    publisher-returns: true #开启消息回退功能
    listener:
      simple:
        #        acknowledge-mode: manual #开启手动应答机制，默认自动应答
        prefetch: 1 #设置为其他数字时表示在单个请求中处理的消息个数，他应该大于等于事务数量(unack的最大数量)，用以达到限流的目的
        #每个customer会在MQ预取一些消息放入内存的LinkedBlockingQueue中，这个值越高，消息传递的越快，但非顺序处理消息的风险更高。如果ack模式为none，则忽略。
        #如有必要，将增加此值以匹配txSize或messagePerAck。从2.0开始默认为250；设置为1将还原为以前的行为。
        #        concurrency: 1 #消费端的监听个数(即@RabbitListener开启几个线程去处理数据。)
        #这个是个全局配置，一般情况下，一个listener对应一个consumer是够用的。只是针对部分场景，才需要一对多。所以建议直接在@RabbitListener上配置,如@RabbitListener(queues = "xxx-queue",concurrency = "5-10")
        #        max-concurrency: 10 #消费端的监听最大个数
#        retry:
#          enabled: true #开启消费者重试
#          max-attempts: 3 #最大重试次数
#          initial-interval: 3000 #重试间隔时间 单位毫秒

#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml,com/eleven/cms/**/xml/*Mapper.xml,com/eleven/qycl/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: 4
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
jeecg :
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: local
  path :
    #文件上传根目录 设置
    upload: /opt/jeecg-boot/upload
    #webapp文件路径
    webapp: /opt/jeecg-boot/webapp
  #短信秘钥
  sms:
     accessKeyId: LTAIpW4gUG7xYDNI
     accessKeySecret: ??
  shiro:
     excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**
  #阿里云oss存储配置
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKey: LTAI5tSNJ76SGabNF92HTnWu
    secretKey: ******************************
    bucketName: vrbt-kunpeng
    staticDomain: https://static.cdyrjygs.com
  # ElasticSearch 设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: 127.0.0.1:9200
    check-enabled: false
  # 表单设计器配置
  desform:
    # 主题颜色（仅支持 16进制颜色代码）
    theme-color: "#1890ff"
  # 在线预览文件服务器地址配置
  file-view-domain: http://fileview.jeecg.com
  # minio文件上传
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: otatest
#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas
logging:
  level:
    org.jeecg.modules.system.mapper : warn
#enable swagger
swagger:
  enable: false



#电信视频彩铃配置
dianxin:
  vrbt:
    #合作商账号，爱音乐分配，请向商务人员获取。又名deviceid或AppKey
    #    deviceId: 2000000002044
    deviceId:  2000000002244
    #爱音乐分配的秘钥，请向商务人员获取，又名devicePwd或AppSecret；
    #    devicePwd: IyoadK78R0QP
    devicePwd: 0aCu5uZqSfAv
    #渠道号，爱音乐分配，请向商务人员获取。 又名channelid
    #    channelId: 7190
    channelId: 7120
    #包月套餐ID
    #    packageId: 135999999999999000021
    packageId: 135999999999999000032
    #api基地址
    apiBaseUrl: http://api.118100.cn/openapi/services
    #包月产品订购关系查询
    #queryPackageListUrl: ${dianxin.vrbt.apiBaseUrl}/v2/package/packageservice/querypackagelist.json
    queryPackageListUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/querypackagelist.json
    #包月产品退订
    unSubscribeByempUrl: ${dianxin.vrbt.apiBaseUrl}/v2/package/packageservice/unsubscribebyemp.json
    #一键开通视频彩铃订购包月产品(音乐盒代计费)验证码下发
    asyncOpenOrderSendRandomUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/onekey/async_open_order_sendrandom.json
    #一键开通视频彩铃订购包月产品(音乐盒代计费)下单
    asyncOpenAccountOrderPackageBycrbtUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/onekey/async_openaccount_orderpackage_bycrbt.json
    #H5计费下单发起接口(可选验证类型),调用该接口生成订单,并返回计费认证H5页面地址,用户跳到计费认证页面完成计费.
    confirmOrderLaunchedExUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/confirm_order_launched_ex.json
    # H5 计费下单发起接口（一键开户订购）  调用该接口生成订单。并返回计费认证H5页面地址。用户跳到计费认证页面完成计费。
    #一键开户+订购
    confirmOpenOrderLaunchedExUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/confirm_open_order_launched_ex.json
    #H5 计费下单发起接口（EMP 计费） 调用该接口生成订单。并返回计费认证H5页面地址。用户跳到计费认证页面完成计费。
    #通过该接口下单的订单，在 H5 中必须使用 EMP 计费发起接口，向用户下发 EMP 短信验证码
    confirmOrderLaunchedEmpUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/confirm_order_launched_emp.json
    #视频彩铃一键接口订单详情查询
    queryOrderUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/onekey/query_order.json
    #H5计费订单详情查询
    queryH5OrderUrl: ${dianxin.vrbt.apiBaseUrl}/v3/packageservice/query_h5_order.json
    #使用包月产品权益免费订购视频铃音
    addToneFreeOnProductUrl: ${dianxin.vrbt.apiBaseUrl}/v3/vrbtservice/ringtone/addtonefreeonproduct.json
    #视频彩铃信息查询接口
    videoQueryUrl: ${dianxin.vrbt.apiBaseUrl}/v3/qkservice/video/query.json
    #h5下单 计费认证页面操作后的返回地址
    returnUrl: https://crbt.kunpengtn.com/vrbt_ch_v4/#/submitwp




#阿里云短信配置
aliyun:
  sms:
    region-id: cn-hangzhou
    accessKeyId: LTAI4xjDqYEriyvv
    secret: HKCNOj1ApS6EjaqoXlHtJAxalNqQRW
    queryParameterRegionId: cn-hangzhou
    queryParameterSignName: 荐音振铃
    #验证码短信模板
    validate-template-code: SMS_190785336
    validate-template-param-key: code
    request:
      method: post
      domain: dysmsapi.aliyuncs.com
      version: 2017-05-25
      action: SendSms

#骏伯多会员充值配置(测试环境)
junbo-api:
  #测试环境
  #  baseUrl: http://test-coupon.liulianglf.com
  #生产环境
  baseUrl: http://h5.coupon.liulianglf.com
  #多会员直充
#  rechargeVipUrl: ${junbo-api.baseUrl}/rechargeVIP
  rechargeVipUrl: ${junbo-api.baseUrl}/buyVIP
  #订单查询
#  orderQueryUrl: ${junbo-api.baseUrl}/orderQuery
  orderQueryUrl: ${junbo-api.baseUrl}/query
  uid: yrjy
  version: 1.0.423.2021011210050872400041
#  partnerNo: 2021011210050872400041
  partnerNo: 2024112010561685030035
  key: junbo888hstj
  rsaPrivateKeyPkcs8: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDSdRw6Wr42SSstAccb+DwtL2FgvW2sj1TTtbKP6sBhoATyCmKxDPFITXq8IvPxvD3vv9nY5FwlgfDiTsDXPrFL188aaBKB4aZDUYnSjzfvYq2PPjAuobhda0r9gbquKDgcowtXNtaukhwDtzJR8H+thybQ2so71rS+OFOdi2fxvBe/a/LgxkUNLp0EgCheyzOu/2mDV1WM2lZ7HlV2dPrJVK9Jo4YMc6uPfNEPlwIvW6ikSChVLimt7H4LFcsTkb72DMvYRnSgNc01Av/1/CQraBRrfC0fB9jLW4kW/zVtaTNLvQ4esQ/xT3XeeiAvYVniCm3pjqxDfFFB/OUALj0BAgMBAAECggEBAM6Y9jf2wokp9L9+4kX7vp8gxvgfnN1r6vrVgE+1RFvRqWktdsqViPrQTG8J5O1mPGrxq9o1+ps7XwvaAYaLXaK1XPJwPdLQf9XP4nsX6vINrZFTnBr62LCkVf5cbrTueH7sM4LPK5o9hlDDcxtq+vuPFDSswyBQ2idtxe9/XW+3zNpNLmBUJk1W36dm2FII2sez/3iSDkeL1bUTrUAL2bepJNmAIIUSAl12yVUBYMYpj1s/WULxntysLNL7tveokh3xcFcgE71c6isyvpVgly/W4uByJFahjK3yfI/tWQgy3qytzEz1a66JMf02AIMqx2e9Dw8HoH8/of0x8yJ25G0CgYEA6wx7nbhORwUX30yQP8TL0M3nujjxH9fhdDJasB2ZpV0XeRS6iT09+7M1o3KFvZfzFTLhHINzw7v2lgo5TM3pUGqmzoBcBJpbrVVeE03tgtBhE5+mzn8ohnx4kUH6oLxl5Zf7h45XdMsk0/zKkYPLxZXhagCG9K+WtB2rYLituFMCgYEA5Td8IE8FVHkh5hH3x89HK9ZPUiDEXpk1hYGX3qX08MqgJ+IRXTZGryqMOLprQf2q+3aYtUuD3lFiiystekBsVdTyeyme1HOQK2ON2qRsaL8FCNhCpoVlOCrARpD6eExCu2XVfT3b4rPlq9T13cufmLhkT0mQ67qvdNRegOAeetsCgYEAvKLEAowLpkucIQVdLxBNUkItmvJf8WJb+dj/lx/qUyAm2nGcUs/nkrz8azRZyRLNb1Hp/+wvWALdnwSNf+oxOOye+lNhhgArWyyL14pO9xEtF4alZEwAxg3W5RzCe4U3cc9LejruTTlLUSYrnCTdwLDMuzm5FV5l4B9/jpwoLykCgYB9Gkhj9d0J2scWo+3hnw7QLTrDr2Cd5O7iO/XgriE+nsjEm80wW6Q+jHFVsOFDdiiw8GyuXlXNrAulu14p4CmAuyXQ9MlcWOpx0/cjQyRsEv2zcx0Fd77j5ellWlFqAO1XDGB156IwXFFY9HVwpWco2lLdN2CiWBTVJeeiXTAliwKBgHp72BtRJOIz5EkB9byTAMil0JG/rHJCRSBtky5Q5sZZdrfPdN/FB/CuOVHForh3jYYhl6nTI76bqf8AWWE3iBJLjJuKm8X1vnWgkAfOVlX9TtSTjJ4ibEYAgIilTMvX+bolK3fbhGH530xBxiRK2kXp3x7xiBZ+jLBYN146cWNI
#  callbackUrl: http://notify.kunpengtn.com/cms-vrbt/api/junboRechargeResult
  callbackUrl: https://xkld.kunpengtn.com/cms-vrbt-miniapp/api/junboRechargeNotify
  otherRechargeMap:
    00210H0:
      channel: 00210H0
      bizType:
      serviceId: 698039020103880544
      name: 宜搜
      key: r00Ixn14MKMZqXR4
      mobileKey:
      isRecharge: 0
      sendbackUrl:
      receivebackUrl:

    00210T7:
      channel: 00210T7
      bizType:
      serviceId: 698039020103880544
      name: 动意
      key: tjWFK8bVU3nITEfO
      mobileKey:
      isRecharge: 0
      sendbackUrl:
      receivebackUrl:

    csgmapp:
      channel: csgmapp
      bizType:
      serviceId:
      name: 长沙格美APP
      key: J&BbpkuLlUV^ur%B8&ss0Sgji20rn8*Z
      mobileKey:
      #      是否限制充值
      isRecharge: 1
      sendbackUrl:
      receivebackUrl:

    MIGUHUYU:
      channel: MIGUHUYU
      bizType:
      serviceId:
      name: 咪咕互娱
      key: ACKYr54qtZ
      mobileKey:
      #      是否限制充值
      isRecharge: 0
      sendbackUrl: https://betagame.migufun.com/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: https://betagame.migufun.com/member/vouchercp/v1.0.7.7/receiveProductCallback

    HEFEIZHELIN:
      channel: HEFEIZHELIN
      bizType:
      serviceId: HEFEIZHELIN
      name: 合肥哲林白金会员充值
      key: hKFt$&HZcXuh818QD3*#Sb%Jq3omIgJF
      mobileKey:
      #      是否限制充值
      isRecharge: 0
      sendbackUrl:
      receivebackUrl:

    swfz:
      channel:
      bizType: SWFZ
      serviceId: SWFZ100001
      name: 思维方阵
      key: Tx7xKg6HeQ
      mobileKey: vkswvtpvx7ffz0jl
      isRecharge: 0
      sendbackUrl: https://hy.hongsengtech.com/huyu/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: https://hy.hongsengtech.com/huyu/member/vouchercp/v1.0.7.7/receiveProductCallback

    dyxc:
      channel:
      bizType: DYXC
      serviceId: DYXC_0300
      name: 大鱼消除
      key: vsvVdQyfvq
      mobileKey: vkswvtpvx7ffz0jl
      isRecharge: 0
      sendbackUrl: https://dyxc.ijoylive.com/huyu/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: https://dyxc.ijoylive.com/huyu/member/vouchercp/v1.0.7.7/receiveProductCallback

    cfdtw:
      channel:
      bizType: CFDTW
      serviceId: CFDTW010300
      name: 厨房大逃亡
      key: uIyfmjUSRq
      mobileKey: vkswvtpvx7ffz0jl
      isRecharge: 0
      sendbackUrl: https://cfdtw.ijoylive.com/huyu/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: https://cfdtw.ijoylive.com/huyu/member/vouchercp/v1.0.7.7/receiveProductCallback

    wzwd:
      channel:
      bizType: WZWD
      serviceId: WZWD100001
      name: 我自为道
      key: dIBJOlaL2V
      mobileKey: vkswvtpvx7ffz0jl
      isRecharge: 0
      sendbackUrl: https://hy.hongsengtech.com/huyu/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: https://hy.hongsengtech.com/huyu/member/vouchercp/v1.0.7.7/receiveProductCallback

    duanju:
      channel:
      bizType: DUANJU
      serviceId: 100010000325
      name: 短剧
      key: 3e9e2459006a4c
      mobileKey: vkswvtpvx7ffz0jl
      isRecharge: 0
      sendbackUrl: http://hydj.kunpengtn.com/huyu/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: http://hydj.kunpengtn.com/huyu/member/vouchercp/v1.0.7.7/receiveProductCallback

    sjcs:
      channel:
      bizType: SJCS
      serviceId: 100010000042
      name: 水晶传说
      key: 3e9e2459006a4c
      mobileKey: vkswvtpvx7ffz0jl
      isRecharge: 0
      sendbackUrl: http://hydj.kunpengtn.com/huyu/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: http://hydj.kunpengtn.com/huyu/member/vouchercp/v1.0.7.7/receiveProductCallback

    ahzz:
      channel:
      bizType: AHZZ
      serviceId:
      name: 暗黑主宰
      key: 03d5bfa8cac646
      mobileKey: vkswvtpvx7ffz0jl
      isRecharge: 0
      sendbackUrl: https://betagame.migufun.com/member/vouchercp/v1.0.7.7/sendProductCallback
      receivebackUrl: https://betagame.migufun.com/member/vouchercp/v1.0.7.7/receiveProductCallback

#贝乐虎权益充值参数配置(正式环境)
beilehu:
  #正式环境
  baseUrl: https://openapi.ubestkid.com
  #贝乐虎权益订单充值接口
  rechargeUrl: ${beilehu.baseUrl}/jointOrder/order/import
  #贝乐虎权益订单查询接口
  orderQueryUrl: ${beilehu.baseUrl}/jointOrder/order/query
  cpId: blh262e713222c94dbb
  cpSecret: 2c69faff629f13f0f4472ad2c51efeab
  beilehuProductMap:
    #贝乐虎大会员月卡
    beilehuVip:
      productId: com.ubestkid.supervip.m1
    #贝乐虎儿歌视频会员月卡
    beilehuSvip:
      productId: com.ubestkid.beilehu.vip.m1

#业务配置
biz:
  migu:
    api-base-url: http://hz.migu.cn/order/rest

    #号码归属地查询
    #    mobile-region-server-url: http://**************:20001/area-service/api/queryArea
    mobile-region-server-url: http://location.kunpengtn.com/area-service/api/queryArea
    mobile-region-api-key: e1ff994c34176877cee75e228ef2ea1d
    #    service-id: 698039035100000014
    #即时开通号码接收服务
    push-subscibe-server-url: http://*************:1575/api/channelOrderNotify/yrjy
    #    push-sms-code-server-url: http://localhost:8888/api/submitSmsCode
    push-sms-code-server-url: http://*************:1575/api/submitSmsCode
    #业务开通页面-视频彩铃 https://crbt.cdyrjygs.com/vrbt_open/#/?mobile=13438828200&channelCode=00210OC
    subscribe-vrbt-order-page: https://crbt.cdyrjygs.com/vrbt_open/#/
    #业务开通页面-视频彩铃(彩铃中心订阅) https://crbt.cdyrjygs.com/vrbt_dy_open/#/?mobile=13438828200&channelCode=014X04C
    subscribe-vrbt-centrality-order-page: https://crbt.cdyrjygs.com/vrbt_dy_open/#/
    #业务开通页面-振铃 https://crbt.cdyrjygs.com/rt_open/#/?mobile=13438828200&channelCode=00210OA&serviceId=698039020050006602
    subscribe-rt-order-page: https://crbt.cdyrjygs.com/rt_open/#/
    #业务开通页面-白金会员 https://crbt.cdyrjygs.com/bjhy_open/#/?mobile=13438828200&channelCode=00210PP
    subscribe-bjhy-order-page: https://crbt.cdyrjygs.com/bjhy_open/#/
    #联通二次确认开通页面 https://open.10155.com/confirm/showPage?orderId=1210114102501105324
    subscribe-liantong-vrbt-order-page: https://open.10155.com/confirm/showPage
    #电信二次确认开通页面 https://m.imusic.cn/openauth/confirm?order_no=0fca98e654284b3ea8a1a0f44c6814e4
    #    subscribe-dianxin-vrbt-order-page: https://m.imusic.cn/openauth/confirm
    subscribe-dianxin-vrbt-order-page: https://m.imusic.cn/paycenter/#/openauth/confirm
    subscribe-dianxin-vrbt-order-report-page: https://crbt.cdyrjygs.com/vrbt_telecom_maihe/
    #业务开通页面-渠道专属包月 https://crbt.cdyrjygs.com/cpmb_open/#/?mobile=13438828200&channelCode=002103L&serviceId=698039020050006172
    subscribe-cpmb-order-page: https://crbt.cdyrjygs.com/cpmb_open/#/
    #头条广告线索api
    toutiao-ad-track-api: https://ad.toutiao.com/track/activate/
    #推啊广告转化上报api
    tuia-ad-effect-api: https://activity.tuia.cn/log/effect/v2
    #推啊广告密钥[代理]
    tuia-ad-advert-key: FFE2CAE3678CA92133F35786D888D2AB
    #推啊广告密钥[鸿盛]
    tuia-ad-advert-key-hongsheng: 938A34F50384FB793535E8A00ECBCAEF
    #推啊广告密钥[麦禾]
    tuia-ad-advert-key-maihe: BE35DD5ABA350E98CE8128A22830281C
    #推啊广告密钥[麦禾-赤金]
    tuia-ad-advert-key-maihe-cijin: 255D3B0790AFED441A0117AEF5784CB2
    #变现猫广告转化上报api
    bianxianmao-ad-effect-api: http://api.bianxianmao.com/dting/monitor/appActivate
    #豆盟广告转化上报api
    #初步转化数据，一般是订单提交、账号注册等
    doumeng-ad-deep-translate-api: https://openapi.bayimob.com/openApi/deepTranslate
    #后续转化数据，例如订单签收，退回等。
    doumeng-ad-order-deep-translate-api: https://openapi.bayimob.com/openApi/orderDeepTranslate
    doumeng-ad-accound-id: df4435ce2750fa0268e0d9a7b182071f
    doumeng-ad-secret: 2750fa0268e0d9a7
    doumeng-ad-accound-id-hongsheng: ddae9603e7f77d0935e005b59cd3cef5
    doumeng-ad-secret-hongsheng: e7f77d0935e005b5
    doumeng-ad-accound-id-hongsheng-cpmb: 5c6dd3d826242c01f0f97c710bf3b852
    doumeng-ad-secret-hongsheng-cpmb: 26242c01f0f97c71
    #互动推广告转化上报api
    hudongtui-ad-effect-api: http://open.adhudong.com/saveEffect.htm
    #腾讯广告转化上报api
    #    tengxun-ad-track-api: http://tracking.e.qq.com/conv
    tengxun-ad-track-api: https://api.e.qq.com/v1.1/user_actions/add

  #阿里云智能媒体服务相关配置
ali:
  media:
    accessKeyId: ${jeecg.oss.accessKey}
    accessKeySecret: ${jeecg.oss.secretKey}
    #注意ice的消息队列必须以ice-callback开头才能接收消息
    jobCallbackMnsQueue: ice-callback-queue-job-hstj
    jobCallbackMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackMnsQueue}
    jobCallbackDiyMnsQueue: ice-callback-queue-job-diy
    jobCallbackDiyMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackDiyMnsQueue}
    jobCallbackManualMnsQueue: ice-callback-queue-job-manual
    jobCallbackManualMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackManualMnsQueue}
    jobCallbackYinglouMnsQueue: ice-callback-queue-job-yinglou
    jobCallbackYinglouMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackYinglouMnsQueue}
    jobCallbackHYAIMnsQueue: ice-callback-queue-job-hyai
    jobCallbackHYAIMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackYinglouMnsQueue}
    jobCallbackTxaiMnsQueue: ice-callback-queue-job-txai
    jobCallbackTxaiMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackTxaiMnsQueue}
    jobCallbackTxAiFaceMnsQueue: ice-callback-queue-job-txaiface
    jobCallbackTxaiFaceMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackTxAiFaceMnsQueue}
    jobCallbackTxAiAcrossFaceMnsQueue: ice-callback-queue-job-txaiacrossface
    jobCallbackTxaiAcrossFaceMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackTxAiAcrossFaceMnsQueue}
    jobCallbackTalkShowMnsQueue: ice-callback-queue-job-TalkShow
    jobCallbackTalkShowMnsNotifyUrl: mns://1301516171574083.mns.cn-beijing.aliyuncs.com/queues/${ali.media.jobCallbackTalkShowMnsQueue}
    mnsEndpoint: http://1301516171574083.mns.cn-beijing.aliyuncs.com
    regionId: cn-beijing
    bucketName: ims-media-kunpeng
    userDataDir: user-media
    iproductionDir: iproduction
    mediaProduceDir: output
#    bgMusicUrl: https://ims-media.oss-cn-beijing.aliyuncs.com/background.mp3
    bgMusicUrl: https://appmedia.kunpengtn.com/bgm/bgm1.mp3
    outputVideoHeight: 1280
    outputVideoWidth: 720
    staticDomain: https://appmedia.kunpengtn.com/


blacklist:
  saveUrl: https://tg.kunpengtn.com/saveBlacklist
  queryBlackUrl: https://tg.kunpengtn.com/getBlacklistByMobile
  queryRedUrl: https://tg.kunpengtn.com/getRedlistByMobile

channelowner:
  queryUrl: https://tg.kunpengtn.com/ad/ownerBySubChannel
  mobileListMap:
    xianghong:
      - 13408669942
      - 13678193992
      - 17711089556
      - 13550027280
    xiaofeng:
      - 13408669942
      - 13550358512
      - 13880538866
      - 13550027280
    dongsw:
      - 15708497294
      - 13228193081
      - 18299061763
      - 13265923435
      - 18010645187
    cpa:
      - 15618678117
#支付宝转账证书文件路径配置:
alipay-refund:
  certPath: /data/alipay_cert/appCertPublicKey_appId.crt
  alipayPublicCertPath: /data/alipay_cert/alipayCertPublicKey_RSA2_appId.crt
  rootCertPath: /data/alipay_cert/alipayRootCert_appId.crt

#咪咕快游VR竞盟
kuaiyou-vr-jingmeng-api:
  appId: 78b1e6c174e30a0
  appSecret: 3a56eaff5b714d6babc15f62460eebda
  tokenUrl: http://*************:8090/api/getToken
  feeUrl: http://*************:8090/sso/getSSOUrl?token=
  userUrl: http://*************:8090/justify/getVrUser?token=
  channelMap:
    HY_10_XX:
      channel: 40423544142
      channelName: 休闲10元包
      productId: 760000153723
    HY_25_CW:
#      channel: 40423546323
      channel: 40423547757
      channelName: 畅玩25元包
      productId: 760000153417
    HY_25_CWS:
      channel: 40423546323
      channelName: 畅玩25元包(老渠道号)
      productId: 760000153417

#骏伯流量包
junbo:
  liuliangbao:
    junBoLiuLiangBaoMap:
      #流量包
      JUNBO_LLB:
        #权限id
        pid:
        #产品编码
        productCode:
        #获取验证码地址
        smsCodeUrl: https://card-api.liulianglf.cn/api/onlineSaleFlow/getAuthCode
        #校验验证码地址
        checkSmsCodeUrl: https://card-api.liulianglf.cn/api/onlineSaleFlow/checkAuthCode
        #下单地址
        submitCodeUrl: https://card-api.liulianglf.cn/api/onlineSaleFlow/handleOrder
        #当前落地页连接
        url:

commonGetSmsCodeUrl: http://member.kunpengtn.com/api/common/getSmsCode
commonSubmitCodeUrl: http://member.kunpengtn.com/api/common/submitCode
commonFilterCheckUrl: http://member.kunpengtn.com/api/common/filterCheck


#广东移动存量业务
guangdong:
  stock:
    apiBaseUrl: https://221.179.11.204:443
    #创建订单
    createOrderUrl: ${guangdong.stock.apiBaseUrl}/eaop/rest/BSS/commodity/create_productorder_ckcommid/v1.1.1
    #发送短信验证码
    sendSmsUrl: ${guangdong.stock.apiBaseUrl}/eaop/rest/BSS/service/smscodeapply/v1.1.1
    #提交短信验证码
    submitSmsUrl: ${guangdong.stock.apiBaseUrl}/eaop/rest/BSS/commodity/smscodechkodcommitorder/v1.1.1
    productMap:
      GDYD_STOCK:
        #产品编码
        productCode: GS.prod.10086000061876
        #产品id
        productId: GS.prod.10086000061876
        #产品名称
        productName: 30元欢乐享礼包 (A)
      GDYD_LLB:
        #产品编码
        productCode: GS.prod.10086000053672
        #产品id
        productId: GS.prod.10086000053672
        #产品名称
        productName: 视频彩铃订阅-炫视流量包
      GDYD_XFJ:
        #产品编码
        productCode: YTF20YXFQ7ZYH6Y
        #产品id
        productId: YTF20YXFQ7ZYH6Y
        #产品名称
        productName: 20元小福券7折优惠（合约6个月，每月赠送6元）
    #应用ID
    appId: 110041
    #私钥
    privateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCJSFOsYo12ov9vJgjkDyPoSHbmKFNADbJG84QQtJq8+YsVnx+olY0OtRXjuvMN293Dhc4hzLtF/KoaOeR6xrIzfwp81/aBotOhe6i/WrgyPWvRqNZFI28lr1prhy2UhZv5kX+ikT+eHq2Yam42Whbx8OEFTQnXdeG/gxd1TVz8v8y/oGgF+5hrd6ZtLtdoR9+y71PEFRYNFhTnj6IDLpMIP3uKhwImX502s3XRx9LXgflt4ymmFApIMy8RWzp4AmKiOPZsZ1n5JvFqLW9hNbA9xpwyGpeSdbIwGdvZjUbOxbQmfoKTrNNDMBFkOJyzziFdqvGfooywg99jn4hTgV3FAgMBAAECggEAbRGNiNPz2GG/1yq8vZRZzt5juJApNCatkTk/n5Ydqi4IlnEJ0gKfQyQT9A4/tK8ODZ1tG+VwlDYdXyDHqdPbGJbCWlWqf2xB/3Yjp4QH+8JYuwYM1nlEgQhEKlIHoIxWU32Jjjsf9Bwjh4hnb787o5Tg8jk2rHv3kxQvuv5v9k2vYRffcp/BLKrQ2vhuZhn/lZ0HAxWAb/gvbWyyxSDFXm8zQO0Vo+7mEUreqDIXyv4J5Ki3tz2k1qUDyI0dr4aROj21KxYjV4/IJRBabUjEtcbSWEb8Z9fZoynGwJXq8cbl+2U452ayrHu19mtDzVpCO2H6+26dld4uHO487SKmsQKBgQDWiTtFRNhKpi9wpDuRP6i8wpG1Fu3SzP3rdRlZgYOChOH0wNLaVLK+27A1bpNUnAVHpBkWZSdwRpey/AvvGr56VbSEEa+toTZswUvwKNSKGRj/cO8KEnFiW+BIJ/nQSBO1jH86/5ask0VVX0SN+XyzTTZMr6KVYo9TPow8eZ7UOwKBgQCj0MMFenDAds/7Ekfac7TrNKotxRrQ69bzURZUOATjo00TsspdvWV4EZKzKpTM6b5lbeTrEzFV/29XR4OdvpgOmv4HSRmAp9lkSMmVuv1AqTawF1y8SupxZ5v1nyUnyFpchJWM8IB1YJH3lDfqf7ifYUjGsrX+t2iep8wnMhd1/wKBgA0sL+944q7ytUb9TosElZ/BcqDj34Zp/81NS1krkp+Y+SbGECxHtxHw+0jMmU9ApTJY6RFu9JT4QKhEwZbU1Y09ZfAWBetrUqQq2jbMywAPunE0xfigWUXozkSf777SLiOflNBSkTXXdRJg3xy4jOeVfhgHULxLaPF7jbDI5u+xAoGAdgaJHD9VUTXJL0nCfz/yT6xIGGX0piXlXrVDczZRP3l+pPyiFNvKabGvkmrRfrnCNe3xivTvDNjW7XlW+KZDg/Jjp/gg6bMFS92tb7WjlZzVDBYo+Hi5ZS+TDGq3KGyPvizAWyVESE1lnY0qpEeVSy+acZ58xaJ7PCQFURAkGx0CgYAd+WZegfBzR7w69hqQpKlI7/yKiSAG9Jbb2fj7dOlB1yaNKrwLmYMeUxGKYVLxUJCkgGiWrbCxfB7ibVmL+Cmblcy1ca7poWwrBAkPuzDirb7RwDQhwaml5yFWZLIzmyH6cgYaQZNBgwT3OVdZ8Syh8lITF2ig8mJm11/BNHa9Cg==

#西藏移动配置
xizang-mobile-api:
  appId: 911004591
  appKey: 465cdfa89145b36d2c2cf6d9540a439c
  #渠道号
  channelId: 911004591
  #工号
  operatorId: 90359307
  desKey: FSNYO
  tokenUrl: http://223.92.69.97:18003/aopoauth/oauth/token
  sendSmsMethod: CRM_TO_CMOS_SEND_RANDOM_PWD_001
  authMethod: CRM_TO_CMOS_AUTH_RANDOM_PWD_001
#  orderMethod: CRM_ORD_VAS_OFFER_OPER_001
  orderMethod: CRM_ORD_VAS_OFFER_VERIFY_CODE
  oppfUrl: http://223.92.69.97:18000/oppf
  productMap:
    XZ_XUANSHI:
      productName: 炫视专属包
      productId: 111090173170
      smsContent: 【验证密码】${VERIFY_CODE}，尊敬的客户，您好！您将订购炫视专属包-流量优惠版，资费15元/月，订购后立即生效，如不主动退订则长期有效。产品生效期间可享受视频彩铃订阅-炫视专属6元包、视频彩铃全站0元包、10GB全国通用流量（其中5GB合约流量仅限当月使用，不可结转至次月、不可转赠、不可共享）。活动合约期12个月，合约到期后5GB合约通用流量自动失效，其他业务按产品资费15元/月续订并续费。合约期内销户、携号转网、退订活动按照解约赔付标准解约。验证码有效期${MINUTE}分钟。若非本人操作请勿泄露，任何索取行为均可能涉嫌诈骗。【中国移动】

#咪咕互娱渠道包月查询
miguhuyu:
  monthly:
    channelMap:
      TEST:
        key: ve3N1I75AJ0Oy6nA
        channelId: 10001038684
        channelName: 测试
        monthlyUrl: https://betagame.migufun.com/member/shareRights/v1.1.0.7/queryRightsInfo
#        monthlyUrl: https://interface.hongsengtech.com/member/shareRights/v1.1.0.7/queryRightsInfo
      HYQD_HS:
        key: ve3N1I75AJ0Oy6nA
        channelId: 40455052016
        channelName: 鸿盛
        monthlyUrl: https://betagame.migufun.com/member/shareRights/v1.1.0.7/queryRightsInfo
#        monthlyUrl: https://interface.hongsengtech.com/member/shareRights/v1.1.0.7/queryRightsInfo
      HYQD_O_HS:
        key: ve3N1I75AJ0Oy6nA
        channelId: 40455052016
        channelName: 鸿盛
        monthlyUrl: https://betagame.migufun.com/member/shareRights/v1.1.0.7/queryRightsInfo
#        monthlyUrl: https://interface.hongsengtech.com/member/shareRights/v1.1.0.7/queryRightsInfo
      HYQD_MAIHE:
        key: ve3N1I75AJ0Oy6nA
        channelId: 40454451966
        channelName: 麦禾
        monthlyUrl: https://betagame.migufun.com/member/shareRights/v1.1.0.7/queryRightsInfo

#辽宁分省配置
hetu-fensheng-api:
  sendSmsUrl: http://betagame.migufun.com/member/lnRights/v1.1.1.0/cpSendSmsCode
  createOrderUrl: http://betagame.migufun.com/member/lnRights/v1.1.1.0/cpCreateOrder
  channelMap:
    HETU_LN:
        appId: 199360
        privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCFQ9u7OgArTwmupm4w/5pNgTVbL7R8E8SmseE/EzoqkMIU5yn0Dq+9CbcshpF8obBmOXueJYvcPFDUmTf6ZkYWGftEHGZTQzM90Us/pMSL4x3pjh4vUtN/MJiBLBTxS0Yq0vvDcYGQxiY5Oz2j/kItrQe2thdVQMdQc6cRmLhdOAmSD8jLs95VKyFXcstQzAxXhUatPp+JIz8NynXmRav/eLdXb1oIyejJVSqyUUUSfgMPkgJgkL4jjiCAyaHvo2EE3076e14Nr9rxD06naHBtvaBKdibLzbjaGJkdQXH+sAa2dvcc/8Olt1uV9ZYTxZNMjECCRnYIEqbunIYmIy3pAgMBAAECggEAH0pboY5w07pvL47seJzLgj9AY/TDkA6kMe+6T7EYCz6KE5Zjh2abZOuPEulV58mfPaFpoSx3tnKDGCgRuL2i3zWihc+VRx+UPIOUA5GQhLgDIf+4nqv39PlYFZxgHnfqNcvsSWuxXGjqOK3sP0FIEKl8Ei6XM/FN21oMdFlDkL9sjHX+j5SB/tByuYdbf4iFyJXYP0WHoFcPrh+GzTHMmLLm8Db1nHHsT8ml2VZGxqW/j92tf+ZtdrOjy3zJTCSKVjKP64Rr064qa5jRQkSQu6TPnWLJyxToKm6XZj7FKx5hzSZ6WEfxpgt8Jk/WPKMLVo8ovBLMaG94KpuuHQWLMQKBgQDJvCKVDG7myAse0fhskOINq8Pmzjs24uytsaCuMAFrvW5DO4SEHRe42fK9euJ9+wJMot0uswkUdqdfRvaXqJlEMawMe1hO/75Tvxe1AYUE6Yd8RcfzQbYJjpZ2aV6jy3su8EGnTbAZx7n2wwf5syre55dOYq5l2oTNiVgaknnqlwKBgQCpHMFMaMajP+RLBeA75fpGyaPylciq5bghnFN9GkY3stfzn+loZZ1pmflkDNdzoOQE2tF8NgQDbSHwSbgTgwhoX/7JaoCQ4f2JsdBB9NjsJSi1+3UNccIvvQ4u3dcJZNtS9R46mjyIbaRz5lwRKBYDfXhzgBewbpIqJacHSAE7fwKBgQC5bTtgQwhhCnjmQFuW9ulBZaNwsBlPju+7i+zEjw2PSZRjKf7ZLKCUYYi0T5KF98WaSY0CksAC1jlr4x9gMTGHtByPcISwQHRiJ+CTmKz64eiN+toaJ0k6qZx1hM7fwXYdo+rE7Cy01ZeCUKwxAVWjg73Y5+2jY2L0RYz9B6wWSwKBgGOIviin3ooFN+wEOFdo46a4VZuZFzOm43o81VLjnQJ5a+tDapB8hmq6KnP9ru2qe6pz1CqEbFZL7FbOPN6XF+K+5WpNGgCsUpqxon6/itFqiYyaxOk11HJelrN09HTd/MzV4oXhc9Fknw9sPTIH4vUI61UnxkmcusC9ypJZiggnAoGAOEsT6pguMFvdCsvTSPNdnAboXgD36+XzZAwS46K6C1pxYJimYUi+EDg8QAZopTurgeXoAmDpmx1CDcm576uqITWBgt/WzBYr+PAj3CN1TgeRdJpYIFEYL+IfWOLRrObss9/XgQfNTZV44QFJQuQHPe+3ZvGpqt8CyxFlHzP7cjE=
        publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhUPbuzoAK08JrqZuMP+aTYE1Wy+0fBPEprHhPxM6KpDCFOcp9A6vvQm3LIaRfKGwZjl7niWL3DxQ1Jk3+mZGFhn7RBxmU0MzPdFLP6TEi+Md6Y4eL1LTfzCYgSwU8UtGKtL7w3GBkMYmOTs9o/5CLa0HtrYXVUDHUHOnEZi4XTgJkg/Iy7PeVSshV3LLUMwMV4VGrT6fiSM/Dcp15kWr/3i3V29aCMnoyVUqslFFEn4DD5ICYJC+I44ggMmh76NhBN9O+nteDa/a8Q9Op2hwbb2gSnYmy8242hiZHUFx/rAGtnb3HP/DpbdblfWWE8WTTIxAgkZ2CBKm7pyGJiMt6QIDAQAB
        notifyPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnMRbdJcW/S9J7bWWIHpzlpdvDCWGHNG5WR6gADLOofToo/0Awd2WHiXwc41CLPW81JWYld4A0WU2wYzkAMlH726oMLhqy7ovxk3Qpv5Tv+PWZExGWXZJg/+M2tJ9qPxwJIugJQY5KG/y5B+IlGZGyxDXSXS+YIwq/bTUVhyZX+RMAqLQAg7I581IL2+PJhltIrP8AbdCMrD0GZRWLkswyaHJ45jLDtPOBfdBy5V1+bICbgGvq9qE85tTqUmmU7Kath1JV+KjI6u9d9D+uJ2UY4hAjr/jz3OwYs2e+MT38dL1XRDKA/S6U/QsLHoXw0XDQZAIFbJMEsKeKAiZU6vD2wIDAQAB
        rightsId: 11015
        mobilePublicKey: 3059301306072a8648ce3d020106082a811ccf5501822d03420004f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30
        appName: com.ss.android.article.news
        #报备地址
        snapshotUrl: https://crbt.kunpengtn.com/mobile_provinces/hetu

#互娱全网配置
huyu-quanwang-api:
  huyuNotifyUrl: https://crbt.kunpengtn.com/cms-vrbt/api/huyuOrderNotify
  channelMap:
    HUYU_QW_15:
      propsId: 10000325
      privateKey: 308193020100301306072a8648ce3d020106082a811ccf5501822d047930770201010420393a6c4ede0c43cb4c82d4ef890aa4ac026b6f48583647a2f334041e397bebd5a00a06082a811ccf5501822da14403420004f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30
      publicKey: 3059301306072a8648ce3d020106082a811ccf5501822d03420004f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30
      #15元
      chargeId: 760000159648
      channelId: 40452351759

    HUYU_QW_20:
      propsId: 10000325
      privateKey: 308193020100301306072a8648ce3d020106082a811ccf5501822d047930770201010420393a6c4ede0c43cb4c82d4ef890aa4ac026b6f48583647a2f334041e397bebd5a00a06082a811ccf5501822da14403420004f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30
      publicKey: 3059301306072a8648ce3d020106082a811ccf5501822d03420004f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30
      #20元
      chargeId: 760000159658
      channelId: 40452351759



#河图分省重庆配置
hetu-chongqing-api:
  sendSmsUrl: http://betagame.migufun.com/member/gameRights/v1.1.1.4/cpSendSmsCode
  createOrderUrl: http://betagame.migufun.com/member/gameRights/v1.1.1.4/cpCreateOrder
  channelMap:
    HETU_CQ:
      appId: 72007
      chargeId: locpp.10411316
      propsId: 11015
      notifyChargeId: 760000159041
      privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCb+Iekm83xg6WwfCoeGePdwUKlXB9/QclGpWnbHvvHSBIpM8jvNuYqIr1ApBqlyZ3ydo41QLMSsHGF4shM2+oTpenrdPBotJ3/nufTi1rx4NjfTgq4fxlMiD+oallopGmKoKMHuKaRHMdi6W6L49USRBDSSHO0a8KWx+MEtu6q0lM4qoNQHbgc3nlMXO8AoSTN0Jxiq1Zg4J15CCxepOyFNNbWnPm1lRryOxOA4UxaOGU114V0d5LZUdX8FlszTIq87u8u+sqRs2obRcCfCxzoEwsvf+a62/GD9Yl04dWd+m6NL+nzUF2LagtRzmXfIKYSIy3NP7OvLDjkXavSt43vAgMBAAECggEAX09QFFbhUmSQK3i0hBBuHmSqxUG2PZ2F0ZXjSfN1CCISdvseHo/9tmHkJEwjvIun6TJpVEFlLFaGCLq8i7sXH0qT1z5uao10v2/rWmod+RDddhfKlVOVsh7QR6o1WKyfkgb3mRBNOP/NF7RtyS5AVhkh7l431ljDYEBlicv/rIJFj5gCLRqzz/Di5lWpHrULv4NRDm/bzt+FclexZzESS+6FrZXyqN4WdFdQr0rvaIEvuvUrLyKv3keGqx/8XB3FpLwC5/eLZHsWLQG/40OicRQc00drtUORvyx2dri87wh694lnInogd6lHxvBXoWc5JjbGX4aY2XAlR5eflQhqAQKBgQDUQiPk2Br3aofRKsG9sCO8LfO8Cdy8jhz5nHPks+wRmsOAvaxwtQLNyzCFVN7hQ/nUnq+AI5toeUzKiqM6ZuwcC21aoIuLqca8zWNlXGFSnbCv7uwWmISlVa6nIxHHOCfsN6W0EeUS4VadAvlSgn13k8z4mO+qiorZOAQPc8D9vwKBgQC8HOU21EjouKZQBj5UD443tPAZ0MW9UlV1uQFImDGvq/IyBLd2f2P5XzSpVd5szbnbyfTx9Tuy6LeW1IOMAoTU9GKKnCzYp3LL74it3k6FvBMa32MIgpaqJ/J4IqsCLVSk51v9WPkho8ZRQoHUwcwGas1h9iW93eY26tx62Grb0QKBgQCUPbkp25BB2gIPIIh8/WaHx6QhYNXsTmiIIC+Eu/AUf30pDc9AS/swVUeG5zZeJ4Z1a3pwyO2B8q2iC19iq30dPRHbqKKtIR6uv9c6dGF4PK+ov/nc8ho6CnRXQJPrCho/zMGK2LCfFgfS0r/kU0RjXjNaMB26gtXkTU3wqMxRqwKBgHzUp/7kjKVys1rzabUaxYOOHK7k7fOeiXufk4x7Aq3S6tvqaVzHWK6JUUb15q5JLwb9VACkfkNRT1NWHx1wJnmy2xmkXdY/+FwAzrCij3ULw5nXl75z+ktcSvkR+G1va3Yq2c6blmeOaPJPshxfpmazxSs2zwDaGgVjgYA70V2hAoGAHOPzZRF3BigBLfOsCCGtoRjrpzDEtWSdCQiNQlWP9Vec9hFTvbMTR3m91vNu4QlTrqt3vBekQMGekL0WoU3sDUXDuPo4eQww33Nt1wUf4JsxHhZlpmwR5vZihWIYiglDVGZ0XUWTpNSyxtuuw2lLHS9GXpkJV65CMgJ0hSU8770=
      publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAm/iHpJvN8YOlsHwqHhnj3cFCpVwff0HJRqVp2x77x0gSKTPI7zbmKiK9QKQapcmd8naONUCzErBxheLITNvqE6Xp63TwaLSd/57n04ta8eDY304KuH8ZTIg/qGpZaKRpiqCjB7imkRzHYului+PVEkQQ0khztGvClsfjBLbuqtJTOKqDUB24HN55TFzvAKEkzdCcYqtWYOCdeQgsXqTshTTW1pz5tZUa8jsTgOFMWjhlNdeFdHeS2VHV/BZbM0yKvO7vLvrKkbNqG0XAnwsc6BMLL3/mutvxg/WJdOHVnfpujS/p81Bdi2oLUc5l3yCmEiMtzT+zryw45F2r0reN7wIDAQAB
      mobilePublicKey: 3059301306072a8648ce3d020106082a811ccf5501822d03420004f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30
      mobilePrivateKey: 308193020100301306072a8648ce3d020106082a811ccf5501822d047930770201010420393a6c4ede0c43cb4c82d4ef890aa4ac026b6f48583647a2f334041e397bebd5a00a06082a811ccf5501822da14403420004f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30
      appName: com.ss.android.article.news
      #报备地址
      snapshotUrl: https://crbt.kunpengtn.com/stationconstructions/promotion/1118/

#迅游⼿游加速器VIP卡30天
xunyou-api:
  #正式环境
  baseUrl:  https://api.xunyou.mobi
  #会员直充
  rechargeVipUrl: ${xunyou-api.baseUrl}/api/v2/android/partnerName/phones
  #订单查询
  orderQueryUrl: ${xunyou-api.baseUrl}/api/v1/android/orders/orderId/third_part
  #订单退款
  orderRefundUrl: ${xunyou-api.baseUrl}/api/v1/android/orders/orderId/refund
  partnerName: maihe
  key: '090224cb-cbfe-488e-9e9c-e121c7d5869e'
  queryKey: 'ui3Bc1GLoC4bQTqD'
  refundkey: 'bi3Bc1GLpC4bQTqa107'

#多点优惠券充值
duodian:
  #测试环境
  baseUrl:  https://andes.dmall.com
  #会员直充
  rechargeVipUrl: ${duodian.baseUrl}/api/output/coupon/receiveCoupon
  #订单查询
  orderQueryUrl: ${duodian.baseUrl}/api/output/coupon/queryReceiveStatus
  appId: '2701'
  key: 'bc29e68fb5404c4c8ef60fb12f7b9531'
  publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDKuTS3tJRgM+20WzYtt17S1rVGHzEwiaxso0A8LdLz8TtYw+Iw66qlD9qjLDOb7nlh9EjYUOHhTuTlkqnbf7G2vrW7dm1EZgSBeQPZDjwCBCDCy442E41cibyM2kYAvAYKpdihD1ta7HbrRiUbdBPzv6q2TRTi56zDRxtlj0P1MwIDAQAB'

TaskShow:
  Ali:
    region: 'cn-north-1'
    host: 'visual.volcengineapi.com'
    secretAccessKey: 'TldNMFpEZzRNelZpTmpFMU5ESXdaVGxsT0RZME1EQTVPR1JqTjJFM09UTQ=='
    accessKeyID: 'AKLTNGUzYmU0MTkyMDU1NDQ3NDllNjBlZGI0MzUyYzAyYjk'

kpApp:
  baseUrl: https://xkld.kunpengtn.com/backend-vrbt-app
  tmpToken: 54a6g7d2-f588-4851-a485h-fc57h6j7k9
vrbt-redis:
  database: 0
  host: **************
  lettuce:
    pool:
      max-active: 4   #最大连接数据库连接数,设 0 为没有限制
      max-idle: 4     #最大等待连接中的数量,设 0 为没有限制
      max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
      min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
    shutdown-timeout: 100ms
  password: jYZTYYL3
  port: 6379
