package org.jeecg;

import com.eleven.cms.entity.CouponCode;
import com.eleven.cms.service.ICouponCodeService;
import com.eleven.cms.util.BizConstant;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 激活码设置状态
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/16 11:59
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CouponCodeServiceTest {
    @Autowired
    private ICouponCodeService couponCodeService;
    @Test
    public void updateStatus() {
        Date date=new Date();
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\贝乐虎卡密需要设置失效.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                if(!stringList.isEmpty()){
                    stringList.forEach(code->{
                        couponCodeService.lambdaUpdate().eq(CouponCode::getCouponCode, code.trim()).ne(CouponCode::getStatus, BizConstant.ALREADY_EXPIRED).set(CouponCode::getStatus, BizConstant.ALREADY_EXPIRED).set(CouponCode::getRemark,"手动设置已过期").set(CouponCode::getUpdateTime,date).update();
                    });
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void updateStatusList() {
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\贝乐虎卡密需要设置失效.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);

                List<String> codeList= Lists.newArrayList();
                 if(!stringList.isEmpty()){
                    stringList.forEach(code->{
                        if(StringUtils.isNotBlank(code.trim())){
                            codeList.add(code.trim());
                        }
                    });
                 }
                 List<String> codesList= Lists.newArrayList();
                 for(int i=0;i<codeList.size();i++){
                     codesList.add(codeList.get(i));
                     if(i%1000==0){
                         couponCodeService.updateStatusList(codesList);
                         codesList.clear();
                     }
                     if(i==codeList.size()-1){
                         couponCodeService.updateStatusList(codesList);
                     }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    @Test
    public void sendCodeScheduleDeduct() {
        couponCodeService.ahzzSendCodeScheduleDeduct("1846034211493384193");
        couponCodeService.ahzzSendCodeScheduleDeduct("1846037709249318913");
    }
    @Test
    public void code() {
        FileInputStream inputStream = null;
        try {
            inputStream = new FileInputStream("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表2.xls");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        Workbook workbook = null;
        try {
            workbook = new HSSFWorkbook(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<String> stringList1 =Lists.newArrayList();
        List<String> stringList2 =Lists.newArrayList();
        List<String> stringList3=Lists.newArrayList();
        List<String> stringList4=Lists.newArrayList();
        List<String> stringList5=Lists.newArrayList();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            for (Row row : sheet) {
                for (Cell cell : row) {
                    if(sheet.getSheetName().equals("梦幻神秘礼包")){
                        stringList1.add(cell.toString());
                    }else if(sheet.getSheetName().equals("璀璨宝石礼包")){
                        stringList2.add(cell.toString());
                    }else if(sheet.getSheetName().equals("奇迹加速礼包")){
                        stringList3.add(cell.toString());
                    }else if(sheet.getSheetName().equals("梦想转生礼包")){
                        stringList4.add(cell.toString());
                    }else if(sheet.getSheetName().equals("黑暗进阶礼包")){
                        stringList5.add(cell.toString());
                    }
                }
            }
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_1.txt"))) {
            for (String line : stringList1) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_2.txt"))) {
            for (String line : stringList2) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_3.txt"))) {
            for (String line : stringList3) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_4.txt"))) {
            for (String line : stringList4) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_5.txt"))) {
            for (String line : stringList5) {
                writer.write(line);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }




    @Test
    public void codeList() {
        List<String> listFile=Lists.newArrayList();
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表3.xls");
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表4.xls");
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表5.xls");
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表6.xls");
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表7.xls");
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表8.xls");
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表9.xls");
        listFile.add("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2024-09\\礼包码\\激活码奖励表10.xls");
        listFile.forEach(file->{
            FileInputStream inputStream = null;
            try {
                inputStream = new FileInputStream(file);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
            Workbook workbook = null;
            try {
                workbook = new HSSFWorkbook(inputStream);
            } catch (IOException e) {
                e.printStackTrace();
            }
            List<String> stringList1 =Lists.newArrayList();
            List<String> stringList2 =Lists.newArrayList();
            List<String> stringList3=Lists.newArrayList();
            List<String> stringList4=Lists.newArrayList();
            List<String> stringList5=Lists.newArrayList();
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                for (Row row : sheet) {
                    for (Cell cell : row) {
                        if(sheet.getSheetName().equals("梦幻神秘礼包")){
                            stringList1.add(cell.toString());
                        }else if(sheet.getSheetName().equals("璀璨宝石礼包")){
                            stringList2.add(cell.toString());
                        }else if(sheet.getSheetName().equals("奇迹加速礼包")){
                            stringList3.add(cell.toString());
                        }else if(sheet.getSheetName().equals("梦想转生礼包")){
                            stringList4.add(cell.toString());
                        }else if(sheet.getSheetName().equals("黑暗进阶礼包")){
                            stringList5.add(cell.toString());
                        }
                    }
                }
            }
            try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_1.txt",true))) {
                for (String line : stringList1) {
                    writer.write(line);
                    writer.newLine();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_2.txt",true))) {
                for (String line : stringList2) {
                    writer.write(line);
                    writer.newLine();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_3.txt",true))) {
                for (String line : stringList3) {
                    writer.write(line);
                    writer.newLine();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_4.txt",true))) {
                for (String line : stringList4) {
                    writer.write(line);
                    writer.newLine();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\暗黑主宰ahzz_5.txt",true))) {
                for (String line : stringList5) {
                    writer.write(line);
                    writer.newLine();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });

    }
}
