package org.jeecg;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.VrbtCombinResult;
import com.eleven.cms.vo.VrbtProduct;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.eleven.cms.remote.MiguApiService.*;
import static com.eleven.cms.vo.VrbtCombinResult.VRBT_FUN_STATUS_NONE;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class MiguApiServiceTest {

    @Autowired
    MiguApiService miguApiService;
    @Autowired
    IMemberService memberService;
    @Autowired
    private BizProperties bizProperties;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private ISubscribeService subscribeService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private  final static String CH = "00210OC";

    @Test
    public void miguLogin(){
        //final RemoteResult remoteResult = miguApiService.miguLogin("***********", CH);
        //RemoteResult remoteResult = miguApiService.miguLogin("***********", CH);
        //System.out.println(remoteResult);
        //System.out.println("是否为彩铃运营中心渠道号:"+MiguApiService.isCentralityChannel(CH));
        //remoteResult = miguApiService.miguLogin("***********", "014X04C");
        //System.out.println(remoteResult);
        //remoteResult = miguApiService.miguLogin("***********", "014X04D");
        //miguApiService.miguLogin("***********", "014X04G");
        //System.out.println(remoteResult);
        //System.out.println("是否为彩铃运营中心渠道号:"+MiguApiService.isCentralityChannel("014X04D"));
       //miguApiService.miguLogin("***********", "00210U1");
       //miguApiService.miguLogin("***********", "00210U2");
       //miguApiService.miguLogin("***********", "00210U3");       //miguApiService.miguLogin("***********", "00210U4");
       //miguApiService.miguLogin("***********", "00210TY");
       //miguApiService.miguLogin("***********", "00210VV");
       //miguApiService.miguLogin("***********", "014X04D");
       //miguApiService.miguLogin("***********", "014X09T");
       //miguApiService.miguLogin("***********", "014X01E");
       //miguApiService.miguLogin("***********", "014X09T");
       //miguApiService.miguLogin("***********", "014X02D");
       //miguApiService.miguLogin("***********", "014X02F");
       miguApiService.miguLogin("***********", "0021180");
       //miguApiService.miguLogin("***********", "00211AH");
       //miguApiService.miguLogin("***********", "00211AI");
       //miguApiService.miguLogin("***********", "002118T");
       //miguApiService.miguLogin("***********", "014X02G");
       //miguApiService.miguLogin("***********", "00210W0");
       //miguApiService.miguLogin("***********", "014X056");
       //miguApiService.miguLogin("***********", "00210W5");
       //miguApiService.miguLogin("***********", "014X0DJ");
       //miguApiService.miguLogin("***********", "014X04N");
       //miguApiService.miguLogin("***********", "014X04O");
       //miguApiService.miguLogin("***********", "014X04P");
       //miguApiService.miguLogin("***********", "00210W6");
       //miguApiService.miguLogin("***********", "00210W7");
       //miguApiService.miguLogin("***********", "00210XB");
       //miguApiService.miguLogin("***********", "00210XC");
       //miguApiService.miguLogin("***********", "00210XD");
       //miguApiService.miguLogin("***********", "014X0F7");
       //miguApiService.miguLogin("***********", "002112O");
       //miguApiService.miguLogin("***********", "0021107");
       //miguApiService.miguLogin("***********", "002112S");
       //miguApiService.miguLogin("***********", "002112T");

        //final Map<String, MiguChannelConfig> miguChannelConfigMap = bizProperties.getMiguChannelConfigMap();
        //System.out.println(miguChannelConfigMap.get("0021109"));
        //System.out.println();

    }

    @Test
    public void clearUserTone() {
        String mobiles = "15184328572";
        String channelCode = "00210QZ";
        Pattern.compile(",").splitAsStream(mobiles).forEach(mobile -> miguApiService.clearUserTone(mobile,channelCode));
    }



    @Test
    public void fetchToken() {
        //9af3fbe880b34a26bcb2ecebe4a97011
        final String token = miguApiService.fetchToken("***********", "00210H0");
        System.out.println("token = " + token);
        final String newToken = miguApiService.fetchToken(token, "00210H0");
        System.out.println("newToken = " + newToken);
    }

    @Test
    public void filerCheck() {
        //华岸
//        System.out.println(miguApiService.filerCheck("***********", "00210Y0"));
//        System.out.println(miguApiService.filerCheck("***********", "00210Y0"));
//        System.out.println(miguApiService.filerCheck("***********", "00210XZ"));
//        System.out.println(miguApiService.filerCheck("***********", "00210XZ"));
//        System.out.println(miguApiService.filerCheck("***********", "00210Q6"));
//        System.out.println(miguApiService.filerCheck("***********", "00210Q6"));
//        //七彩歌曲
//        System.out.println(miguApiService.filerCheck("***********", "002112O"));
//        System.out.println(miguApiService.filerCheck("***********", "002112O"));
//        System.out.println(miguApiService.filerCheck("***********", "002112S"));
//        System.out.println(miguApiService.filerCheck("***********", "002112S"));
//        System.out.println(miguApiService.filerCheck("***********", "002112T"));
//        System.out.println(miguApiService.filerCheck("***********", "002112T"));
//        //光明网
//        System.out.println(miguApiService.filerCheck("***********", "002110A"));
//        System.out.println(miguApiService.filerCheck("***********", "002110A"));
//        System.out.println(miguApiService.filerCheck("***********", "0021109"));
//        System.out.println(miguApiService.filerCheck("***********", "0021109"));
//        System.out.println(miguApiService.filerCheck("***********", "00210W5"));
//        System.out.println(miguApiService.filerCheck("***********", "00210W5"));
//        //龙腾
//        System.out.println(miguApiService.filerCheck("***********", "0021107"));
//        System.out.println(miguApiService.filerCheck("***********", "0021107"));
//        System.out.println(miguApiService.filerCheck("***********", "0021108"));
//        System.out.println(miguApiService.filerCheck("***********", "0021108"));
//        //麦禾白金会员
//        System.out.println(miguApiService.filerCheck("***********", "00210U1"));
//        System.out.println(miguApiService.filerCheck("***********", "00210U1"));
//        System.out.println(miguApiService.filerCheck("***********", "00210PP"));
//        System.out.println(miguApiService.filerCheck("***********", "00210PP"));
        //讯飞圈子视频
        System.out.println(miguApiService.filerCheck("15251023767", "0021180"));
        System.out.println(miguApiService.filerCheck("18252095608", "0021180"));
        System.out.println(miguApiService.filerCheck("13961604965", "0021180"));
        System.out.println(miguApiService.filerCheck("15195229786", "0021180"));
        System.out.println(miguApiService.filerCheck("13813584987", "0021180"));
        //网易云
        //System.out.println(miguApiService.wangyiyunFilerCheck("15104248418"));

    }

    @Test
    public void bizCommonFilerCheck(){
//        boolean b = SpringContextUtils.getBean(CpmbCommonServiceImpl.class).filerCheck("***********", "00210Y0");
//        boolean scyd_qpjyb = SpringContextUtils.getBean(SichuanYidongCommonServiceImpl.class).filerCheck("***********", "SCYD_QPJYB");
//        System.out.println(b);
//        System.out.println(scyd_qpjyb);
    }

    @Test
    public void miguULogin() {
        //9af3fbe880b34a26bcb2ecebe4a97011
        RemoteResult remoteResult = miguApiService.miguULogin("STnid0000011634264379477a1evdwpnwcI2U8Umqw0fFJ8z0ywk6doB", "00210K7");
        log.info("remoteResult = {}",remoteResult);
    }

    //@Test
    //public void miguQueryStrategy() {
    //    final RemoteResult remoteResult = miguApiService.miguQueryStrategy("***********", BIZ_BJHY_CHANNEL_CODE);
    //    System.out.println("remoteResult = " + remoteResult);
    //}

    @Test
    public void vrbtOpen() {
        final RemoteResult remoteResult = miguApiService.vrbtOpen("***********", "00210QZ");
        System.out.println("remoteResult = " + remoteResult);

        //RemoteResult remoteResult = miguApiService.vrbtOpen("13678000213", CH);
        //System.out.println("remoteResult = " + remoteResult);
        //
        //remoteResult = miguApiService.vrbtOpen("15035577879", CH);
        //System.out.println("remoteResult = " + remoteResult);
    }

    @Test
    public void vrbtTryToSeeUrl() {
        // "contentId": "600926000008167600",
        //  "copyrightId": "638799T9253",
        System.out.println(miguApiService.vrbtTryToSeeUrl(null, MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, "638799T9253"));
        System.out.println(miguApiService.vrbtTryToSeeUrl(null, MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, "600926000008167600"));
    }

    @Test
    public void vrbtCancel() {
        final RemoteResult remoteResult = miguApiService.vrbtCancel("***********", "00210OC");
        System.out.println("remoteResult = " + remoteResult);

        //RemoteResult remoteResult = miguApiService.vrbtOpen("13678000213", CH);
        //System.out.println("remoteResult = " + remoteResult);
        //
        //remoteResult = miguApiService.vrbtOpen("15035577879", CH);
        //System.out.println("remoteResult = " + remoteResult);
    }

    //@Test
    //public void vrbtMonthStatusQuery() {
    //    //final RemoteResult remoteResult = miguApiService.vrbtMonthStatusQuery("***********", CH);
    //    //final RemoteResult remoteResult = miguApiService.vrbtMonthStatusQuery("9af3fbe880b34a26bcb2ecebe4a97011", CH);
    //    //System.out.println("remoteResult = " + remoteResult);
    //
    //    final RemoteResult remoteResult = miguApiService.vrbtMonthStatusQuery("15766895890", CH_DYB_DEFAULT);
    //    boolean isMonthly = remoteResult!=null && remoteResult.isOK() && !VrbtCombinResult.VRBT_MONTH_STATUS_NONE.equals(remoteResult.getStatus());
    //    System.out.println("isMonthly = " + isMonthly);
    //}


    @Test
    public void rtMonthStatusQuery() {
        final RemoteResult remoteResult = miguApiService.rtMonthStatusQuery("***********", BIZ_RT_CHANNEL_CODE);
        System.out.println("remoteResult = " + remoteResult);
    }

    @Test
    public void bjhyQuery() throws InterruptedException {
        RemoteResult remoteResult;
        remoteResult = miguApiService.bjhyQuery("18778826188", "00210U1");
        System.out.println("remoteResult = " + remoteResult);
        //remoteResult = miguApiService.bjhyQuery("***********", "00210SR");
        //System.out.println("remoteResult = " + remoteResult);

        //while (true){
        //    RemoteResult remoteResult = miguApiService.bjhyQuery("***********", "00210PP");
        //    //final RemoteResult remoteResult = miguApiService.bjhyQuery("***********", BIZ_BJHY_CHANNEL_CODE);
        //    System.out.println("remoteResult = " + remoteResult);
        //    TimeUnit.MILLISECONDS.sleep(100L);
        //}
    }

    @Test
    public void bjhyCancel() throws InterruptedException {
        RemoteResult remoteResult;
        remoteResult = miguApiService.bjhyCancel("***********", "00210PP");
        System.out.println("remoteResult = " + remoteResult);
        //remoteResult = miguApiService.bjhyQuery("***********", "00210SR");
        //System.out.println("remoteResult = " + remoteResult);

        //while (true){
        //    RemoteResult remoteResult = miguApiService.bjhyQuery("***********", "00210PP");
        //    //final RemoteResult remoteResult = miguApiService.bjhyQuery("***********", BIZ_BJHY_CHANNEL_CODE);
        //    System.out.println("remoteResult = " + remoteResult);
        //    TimeUnit.MILLISECONDS.sleep(100L);
        //}
    }

    @Test
    public void activeVrbtQuery(){
        System.out.println(miguApiService.activeVrbtQuery("15915401656", "00211DJ"));
        System.out.println(miguApiService.activeVrbtQuery("***********", "00211DJ"));
    }

    @Test
    public void activeVrbtFreeOrder(){
        System.out.println(miguApiService.activeVrbtFreeOrder("***********", "00211DI", "600926000013720577", "1", "1" ));
        System.out.println(miguApiService.activeVrbtFreeOrder("***********", "00211DI", "600926000013720495", "0", "1" ));
    }

    @Test
    public void fetchVrbtProduct(){
        //System.out.println(miguApiService.fetchVrbtProduct("113025T5905M"));
        //System.out.println(miguApiService.fetchVrbtProduct("600926000013680358"));
        //System.out.println(miguApiService.fetchVrbtProduct("11111"));

        final String outFileName = "D:\\tmp_files\\cpmb_vrbt.txt";
        final String separator = "\t";
        try (PrintWriter printWriter = new PrintWriter(new FileWriter(outFileName))){
            Lists.newArrayList("113025T5904",
                            "113025T5905",
                            "113025T5906",
                            "113025T5907",
                            "113025T5908",
                            "113025T5909",
                            "113025T5910",
                            "113025T5911",
                            "113025T5912",
                            "113025T5913",
                            "113025T5914",
                            "113025T5915",
                            "113025T5916",
                            "113025T5917",
                            "113025T5918")
                    .forEach(vrbtId -> {
                        final VrbtProduct vrbtProduct = miguApiService.fetchVrbtProduct(vrbtId);
                        printWriter.println(vrbtProduct.getMusicName() + separator + vrbtId + separator + vrbtProduct.getVrbtProductId());
                    });

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void cpmbCombinQuery(){
        System.out.println(miguApiService.cpmbCombinQuery("***********", "00211DJ"));
        System.out.println(miguApiService.cpmbCombinQuery("13999193554", "00211DJ"));
    }

    @Test
    public void bjhyOrder() throws InterruptedException {
        RemoteResult remoteResult;
        remoteResult = miguApiService.bjhyOrder("***********", "00210T9");
        System.out.println("remoteResult = " + remoteResult);
    }

    //https接口测试
    @Test
    public void bjhyAudition() {
        final String remoteResult = miguApiService.bjhyAudition("***********", "00210H0","63879900166");
        System.out.println("remoteResult = " + remoteResult);
    }

    //https接口测试
    @Test
    public void bjhyDownlink() {
        final String remoteResult = miguApiService.bjhyDownlink("***********", BIZ_BJHY_CHANNEL_CODE,"63879900166");
        System.out.println("remoteResult = " + remoteResult);
    }

    @Test
    public void vrbtStatusQuery(){
        //RemoteResult remoteResult = miguApiService.vrbtStatusQuery("***********", CH);
        //System.out.println(remoteResult);
        //remoteResult = miguApiService.vrbtStatusQuery("***********", "014X04C");
        //System.out.println(remoteResult);
        //remoteResult = miguApiService.vrbtStatusQuery("***********", "014X04D");
        //System.out.println(remoteResult);
        //final RemoteResult statusResult = miguApiService.vrbtStatusQuery("***********", BizConstant.RECHARGE_VRBT_CHANNEL_ID);
        //System.out.println("statusResult = " + statusResult);

        miguApiService.vrbtStatusQuery("***********", "00210Q6");
        miguApiService.vrbtStatusQuery("***********", "00210XZ");
        miguApiService.vrbtStatusQuery("***********", "00211DI");
        miguApiService.vrbtStatusQuery("***********", "00210Y0");
        miguApiService.vrbtStatusQuery("***********", "002110A");
        miguApiService.vrbtStatusQuery("***********", "00211DG");
        miguApiService.vrbtStatusQuery("***********", "00211DF");
        miguApiService.vrbtStatusQuery("***********", "00210W5");
    }

    @Test
    public void vrbtMonthStatusQuery(){
        //RemoteResult remoteResult = miguApiService.vrbtMonthStatusQuery("***********", CH, true);
        //System.out.println(remoteResult);
        //remoteResult = miguApiService.vrbtMonthStatusQuery("***********", "014X04C", true);
        //System.out.println(remoteResult);
        //remoteResult = miguApiService.vrbtMonthStatusQuery("***********", "014X04D", true);
        //System.out.println(remoteResult);
        //System.out.println(miguApiService.vrbtMonthStatusQuery("***********", "014X0A3", true));

        System.out.println(miguApiService.verifyMiguVrbtMonthStatus("15289045235",MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, true));
        System.out.println(miguApiService.verifyMiguVrbtMonthStatus("13908992137",MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, true));
        //System.out.println(miguApiService.verifyMiguVrbtMonthStatus("13989066578",MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, true));
        //System.out.println(miguApiService.verifyMiguVrbtMonthStatus("18708086855",MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, true));
        //System.out.println(miguApiService.verifyMiguVrbtMonthStatus("13908928521",MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, true));
    }

    @Test
    public void vrbtCombinQuery(){
        //VrbtCombinResult vrbtCombinResult = miguApiService.vrbtCombinQuery("***********", CH);
        //System.out.println(vrbtCombinResult);
        //vrbtCombinResult = miguApiService.vrbtCombinQuery("***********", "014X04C");
        //System.out.println(vrbtCombinResult);
        //vrbtCombinResult = miguApiService.vrbtCombinQuery("***********", "014X04D");
        //System.out.println(vrbtCombinResult);

        VrbtCombinResult vrbtCombinResult = miguApiService.vrbtCombinQuery("***********", "014X02D");
        System.out.println(vrbtCombinResult);
    }

    @Test
    public void vrbtToneQuery(){
        String remoteResult = miguApiService.vrbtToneQuery("***********", "014X04C",null,null,null,null);
        System.out.println(remoteResult);
    }

    @Test
    public void vrbtToneDelete(){
        //咪咕视频彩铃内容删除响应=>mobileOrToken:***********,渠道号:014X04C,响应:{"resCode":"000000","resMsg":"成功"}
        //咪咕视频彩铃内容删除响应=>mobileOrToken:***********,渠道号:014X04F,响应:{"resCode":"302002","resMsg":"该铃音或铃音盒不存在"}
        RemoteResult remoteResult = miguApiService.vrbtToneDelete("***********", "014X04F",CENTRALITY_SERVICE_ID);
        //RemoteResult remoteResult = miguApiService.vrbtToneDelete("***********", "014X04F","600926000001809146");
        System.out.println(remoteResult);
    }


    @Test
    public void vrbtUnsubscribe(){
        //咪咕视频彩铃内容删除响应=>mobileOrToken:***********,渠道号:014X04C,响应:{"resCode":"000000","resMsg":"成功"}
        //咪咕视频彩铃内容删除响应=>mobileOrToken:***********,渠道号:014X04F,响应:{"resCode":"302002","resMsg":"该铃音或铃音盒不存在"}
        RemoteResult remoteResult = miguApiService.vrbtUnsubscribe("***********", "014X02D");
        System.out.println(remoteResult);
    }

    @Test
    public void vrbtProductQuery(){
        System.out.println(miguApiService.vrbtProductQuery(MiguApiService.MOBILE_FOR_TEST, CH_QDB, "600000T0117"));
        //System.out.println(miguApiService.vrbtProductQuery(MiguApiService.MOBILE_FOR_TEST, CH_DYB_DEFAULT, "600926000003958154"));
    }

    @Test
    public void vrbtTryToSee(){
        String remoteResult = miguApiService.vrbtTryToSee(null, CH,"600926000002402092");
        //System.out.println(remoteResult);
        //String remoteResult = miguApiService.vrbtTryToSee("***********", "014X05A","699297T4414");
        System.out.println(remoteResult);
    }

    @Test
    public void vrbtToneFreeMonthOrder(){
        // System.out.println(miguApiService.vrbtToneFreeMonthOrder("***********", CH,"699052T2921","1"));
        // System.out.println(miguApiService.vrbtToneFreeMotnthOrder("15287113814", "00210OW","699052T3027","1"));
        // System.out.println(miguApiService.vrbtToneFreeMonthOrder("***********", "014X04C","600926000002727730","0"));
        // System.out.println(miguApiService.vrbtToneFreeMonthOrder("***********", "014X0F7","699295T8657M",MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE));
        System.out.println(miguApiService.vrbtToneFreeMonthOrder("13408525626", "002118U","600000T0104",MiguApiService.VRBT_TONE_ORDER_SETFLAG_BOTH_ZBJ));
    }

    @Test
    public void cpmbQuery() {
        //miguApiService.cpmbQuery("***********", BIZ_CPMB_30_CHANNEL_CODE_2S);
        //miguApiService.cpmbQuery("***********", BIZ_CPMB_30_CHANNEL_CODE_2T);
        //miguApiService.cpmbQuery("***********", BIZ_CPMB_30_CHANNEL_CODE_WU);
        //miguApiService.cpmbQuery("***********", BIZ_CPMB_30_CHANNEL_CODE_X1);
        //miguApiService.cpmbQuery("***********", BIZ_CPMB_30_CHANNEL_CODE_X2);
        //final RemoteResult remoteResult = miguApiService.cpmbQuery("***********", BIZ_CPMB_10_CHANNEL_CODE);
        //System.out.println("remoteResult = " + remoteResult);

        //miguApiService.cpmbQuery("***********", "00210Q6");
        //miguApiService.cpmbQuery("***********", "00210XZ");
        //miguApiService.cpmbQuery("***********", "00211DI");
        //miguApiService.cpmbQuery("***********", "00210Y0");
        //miguApiService.cpmbQuery("***********", "002110A");
        //miguApiService.cpmbQuery("***********", "00211DG");
        //miguApiService.cpmbQuery("***********", "00211DF");
        //miguApiService.cpmbQuery("***********", "00210W5");
        final String outFileName = "D:\\tmp_files\\BJHY_uniq-result-"+ LocalDate.now() +".txt";
        final String inFileName = "D:\\tmp_files\\BJHY_uniq.txt";
        final String separator = "\t";

        final int parallelism = 48;
        ForkJoinPool forkJoinPool = new ForkJoinPool(parallelism);
        //long start = System.nanoTime();
        try (Stream<String> lines = Files.lines(Paths.get(inFileName)).parallel();
             PrintWriter PrintWriter = new PrintWriter(new FileWriter(outFileName))){
            final List<String> resultLines = forkJoinPool.submit(() ->
                            /* Parallel task here, for example */
                            lines.map(line -> {
                                String mobile = StringUtils.substringBefore(line, separator);
                                String channelCode = StringUtils.substringAfter(line, separator);
                                System.out.println(Thread.currentThread().getName() + "->" + mobile + separator + channelCode);
                                //final RemoteResult remoteResult = RemoteResult.fail("不做查询");
                                final RemoteResult cpmbQuery = miguApiService.bjhyQuery(mobile, channelCode);
                                final RemoteResult vrbtStatusQuery = miguApiService.vrbtStatusQuery(mobile, channelCode);
                                return line + separator + (cpmbQuery.isBjhyMember() ? "是" : "否") + (VRBT_FUN_STATUS_NONE.equals(vrbtStatusQuery.getStatus()) ? "无" : "有");
                            }).collect(Collectors.toList()))
                    .get();
            resultLines.forEach(PrintWriter::println);
        } catch (IOException | InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            forkJoinPool.shutdown();
        }
    }

    @Test
    public void schQuery() {
        //miguApiService.schQuery("13408669942", "00211DQ");
        //miguApiService.schQuery("***********", "00211DQ");
        //miguApiService.schQuery("18881484185", "002118U");
        //miguApiService.schQuery("***********", "002118U");
        final String outFileName = "D:\\tmp_files\\002118U-result-"+ LocalDate.now() +".txt";
        final String inFileName = "D:\\tmp_files\\002118U.txt";
        final String channelCode = "002118U";
        final String separator = "\t";

        final int parallelism = 24;
        ForkJoinPool forkJoinPool = new ForkJoinPool(parallelism);
        //long start = System.nanoTime();
        try (Stream<String> lines = Files.lines(Paths.get(inFileName)).parallel();
                PrintWriter PrintWriter = new PrintWriter(new FileWriter(outFileName))){
            final List<String> resultLines = forkJoinPool.submit(() ->
                    /* Parallel task here, for example */
                    lines.map(line -> {
                        String mobile = StringUtils.substringBefore(line, separator);
                        System.out.println(Thread.currentThread().getName() + "->" + mobile);
                        //final RemoteResult remoteResult = RemoteResult.fail("不做查询");
                        final RemoteResult remoteResult = miguApiService.schQuery(mobile, channelCode);
                        return line + separator + (remoteResult.isSchMember() ? "是" : "否");
                    }).collect(Collectors.toList()))
                    .get();
            resultLines.forEach(PrintWriter::println);
        } catch (IOException | InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            forkJoinPool.shutdown();
        }

    }

    @Test
    public void cpmbCancel() {
        final RemoteResult remoteResult = miguApiService.cpmbCancel("***********", BIZ_CPMB_20_CHANNEL_CODE_1);
        System.out.println("remoteResult = " + remoteResult);
    }

    @Test
    public void streamQuery() {
        final String remoteResult = miguApiService.streamQuery("***********",BIZ_CPMB_10_CHANNEL_CODE, "63358600320" );
        System.out.println("remoteResult = " + remoteResult);
    }

    @Test
    public void getChannelByServiceId(){
        final List<String> channelByServiceId = bizProperties.getChannelByServiceId("698039020108689345");
        System.out.println("channelByServiceId = " + channelByServiceId);
    }

    @Test
    public void getChannelByConfig() {
        String ch,channelQudao,channelDingyue;
        boolean isChannelDingyue;
        ch = "00210OC";
        channelQudao = bizProperties.getChannelQudao(ch);
        System.out.println(ch +" => channelQudao = " + channelQudao);
        channelDingyue = bizProperties.getChannelDingyue(ch);
        System.out.println(ch +" => channelDingyue = " + channelDingyue);
        isChannelDingyue = bizProperties.isChannelDingyue(ch);
        System.out.println(ch +" => isChannelDingyue = " + isChannelDingyue);


        ch = "00210OW";
        channelQudao = bizProperties.getChannelQudao(ch);
        System.out.println(ch +" => channelQudao = " + channelQudao);
        channelDingyue = bizProperties.getChannelDingyue(ch);
        System.out.println(ch +" => channelDingyue = " + channelDingyue);
        isChannelDingyue = bizProperties.isChannelDingyue(ch);
        System.out.println(ch +" => isChannelDingyue = " + isChannelDingyue);

        ch = "00210QG";
        channelQudao = bizProperties.getChannelQudao(ch);
        System.out.println(ch +" => channelQudao = " + channelQudao);
        channelDingyue = bizProperties.getChannelDingyue(ch);
        System.out.println(ch +" => channelDingyue = " + channelDingyue);
        isChannelDingyue = bizProperties.isChannelDingyue(ch);
        System.out.println(ch +" => isChannelDingyue = " + isChannelDingyue);

        ch = "00210QH";
        channelQudao = bizProperties.getChannelQudao(ch);
        System.out.println(ch +" => channelQudao = " + channelQudao);
        channelDingyue = bizProperties.getChannelDingyue(ch);
        System.out.println(ch +" => channelDingyue = " + channelDingyue);
        isChannelDingyue = bizProperties.isChannelDingyue(ch);
        System.out.println(ch +" => isChannelDingyue = " + isChannelDingyue);


    }

    @Test
    public void decryptMobile(){
        System.out.println(miguApiService.decryptMobile("gOQjTOJf7IKfohabJo65CA==", BIZ_CPMB_30_CHANNEL_CODE_X2));
    }

    @Test
    public void clickoffReceipt(){
        //miguApiService.clickoffReceipt("0100864", "4a7018e8e78049ff8c4dbcc1532a469b", "e1da89b1-9748-47ae-93bf-91e57585614400100D");
        miguApiService.clickoffReceipt("0100537", "7fe76102949b42ae8331263fd01f4922", "e1da89b1-9748-47ae-93bf-91e57585614400100D");
    }

    @Test
    public void vrbtZeroOrder() throws InterruptedException {
        miguApiService.vrbtZeroOrder("***********", "014X0A3", 1);
        //miguApiService.vrbtMonthStatusQuery("***********", "014X0A3", true);
        //miguApiService.vrbtStatusQuery("***********", "014X0A3");
        //miguApiService.queryVrbtBusinessPolicy("***********", "014X0A3","638799T3907");
        //miguApiService.vrbtToneFreeMonthOrder("***********", "014X0A3","638799T3907","0");
        //TimeUnit.SECONDS.sleep(10L);
        //miguApiService.vrbtMonthStatusQuery("***********", "014X0A3", true);
        //miguApiService.vrbtToneFreeMonthOrder("***********", "014X0A3","600926100600861624","1");
        //miguApiService.vrbtToneFreeMonthOrder("***********", "014X0A3","600926100600861624","1");
        //miguApiService.vrbtToneFreeMonthOrder("***********", "014X0A3","600926100600861624","1");

    }

    @Test
    public void vrbtZeroOrderWithLog(){
        final FebsResponse febsResponse = memberService.rechargeVrbtRights("***********", 1);
        System.out.println("febsResponse = " + febsResponse);
        //final FebsResponse febsResponse1 = memberService.rechargeVrbtRights("***********", 1);
        //System.out.println("febsResponse1 = " + febsResponse1);
        miguApiService.vrbtToneFreeMonthOrder("***********", BizConstant.RECHARGE_VRBT_CHANNEL_ID,"638799T7347",MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
        //miguApiService.vrbtToneFreeMonthOrder("***********", BizConstant.RECHARGE_VRBT_CHANNEL_ID,"638799T3923","1");

    }

    @Test
    public void crbtMonthQuery() throws InterruptedException {
        RemoteResult remoteResult;
        remoteResult = miguApiService.crbtMonthQuery("15101155784", "002107Y", null);
        remoteResult = miguApiService.crbtMonthQuery("18210986584", "002107Y", null);
        remoteResult = miguApiService.crbtMonthQuery("15811048655", "002107Y", null);
        System.out.println("remoteResult = " + remoteResult);
        //while (true){
        //    RemoteResult remoteResult = miguApiService.bjhyQuery("***********", "00210PP");
        //    //final RemoteResult remoteResult = miguApiService.bjhyQuery("***********", BIZ_BJHY_CHANNEL_CODE);
        //    System.out.println("remoteResult = " + remoteResult);
        //    TimeUnit.MILLISECONDS.sleep(100L);
        //}
    }

    @Test
    public void crbtMonthCancel() throws InterruptedException {
        miguApiService.crbtMonthCancel("15101155784", "002107Y");
        TimeUnit.SECONDS.sleep(2L);
         miguApiService.crbtMonthQuery("15101155784", "002107Y", null);
    }

    @Test
    public void asMemberQuery() {
        //RemoteResult remoteResult = miguApiService.asMemberQuery("***********", BIZ_AS_MEMBER_CHANNEL_CODE);
        ////final RemoteResult remoteResult = miguApiService.cpmbQuery("***********", BIZ_CPMB_10_CHANNEL_CODE);
        //System.out.println("remoteResult = " + remoteResult);
        //
        //remoteResult = miguApiService.asMemberCancel("***********", BIZ_AS_MEMBER_CHANNEL_CODE);
        //System.out.println("remoteResult = " + remoteResult);
        System.out.println(miguApiService.asMemberQuery("18987366348",MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174));
    }

    @Test
    public void asMemberCancel() {
        final RemoteResult remoteResult = miguApiService.asMemberCancel("***********", BIZ_AS_MEMBER_CHANNEL_CODE);
        System.out.println("remoteResult = " + remoteResult);
    }

    @Test
    public void fetchServiceId(){

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(CH);
        String serviceId = cmsCrackConfig != null ? cmsCrackConfig.getServiceId() : "";
        System.out.println("serviceId = " + serviceId);

        CmsCrackConfig cmsCrackConfig1 = cmsCrackConfigService.getCrackConfigByChannel("123456");
        String serviceId1 = cmsCrackConfig1 != null ? cmsCrackConfig1.getServiceId() : "";
        System.out.println("serviceId1 = " + serviceId1);
    }

    @Test
    public void vrbtInitiativeStatusQuery(){
//        miguApiService.vrbtInitiativeStatusQuery("15915401656","014X02F");
//        miguApiService.vrbtInitiativeStatusQuery("15184328572","014X02F");
//        miguApiService.vrbtInitiativeStatusQuery("***********","014X02F");
//        miguApiService.vrbtInitiativeStatusQuery("***********","014X02F");
        miguApiService.vrbtInitiativeStatusQuery("***********","014X02F", VRBT_FUN_PRODUCT_TYPE_PASSIVE);
        miguApiService.vrbtInitiativeStatusQuery("***********","014X02F", VRBT_FUN_PRODUCT_TYPE_ACTIVE);

        miguApiService.vrbtInitiativeStatusQuery("***********","014X04E", VRBT_FUN_PRODUCT_TYPE_PASSIVE);
        miguApiService.vrbtInitiativeStatusQuery("***********","014X04E", VRBT_FUN_PRODUCT_TYPE_ACTIVE);
    }

    @Test
    public void vrbtInitiativeSubscribe(){
        miguApiService.vrbtInitiativeSubscribe("***********","014X02F");
    }

    @Test
    public void vrbtInitiativeQuerySetting(){
        miguApiService.vrbtInitiativeQuerySetting("***********","014X02F");
    }


    @Test
    public void vrbtInitiativeSetting(){
        miguApiService.vrbtInitiativeSetting("***********", "014X02F", "600926000004323955");
    }

    @Test
    public void specialProductSub(){
        //"name":"如果成功",
        //        "id":"600926000011622939",
        //        "vrbtId":"118798T0002",
        //miguApiService.specialProductSub("18881484185", "002118U", "600926000011622939","118798T0002");
        //miguApiService.specialProductSub("***********", "002118U");
        //miguApiService.specialProductSub("17260807125", "002118U");
        //miguApiService.specialProductSub("14795570039", "002118U");
        miguApiService.specialProductSub("15948945532", "00211DQ");
        //miguApiService.specialProductSub("19834912362", "014X0I9");
    }

    @Test
    public void sxh() throws IOException {
        //RemoteResult remoteResult;
        //System.out.println(miguApiService.sxhGetCircleId("18881484185", BIZ_SXH_CHANNEL_CODE));
        //System.out.println("remoteResult = " + remoteResult);
        //铃音编码	铃音名称	类型	渠道号
        //600926600602276528	绿林音频.mp3	音频	002115U
        //600926100700246950	绿林.mp4	视频
        //600926600602276530	山雾音频.mp3	音频
        //600926100700246954	山雾.mp4	视频
        //final String circleId = remoteResult.getToken();
        //remoteResult = miguApiService.sxhRingOperate(circleId,BIZ_SXH_CHANNEL_CODE,"0","600926100700246950");
        //System.out.println("remoteResult = " + remoteResult);
        //remoteResult = miguApiService.sxhRingOperate(circleId,BIZ_SXH_CHANNEL_CODE,"2",null);
        //System.out.println("remoteResult = " + remoteResult);
        //remoteResult = miguApiService.vrbtStatusQuery("17260807125", BIZ_SXH_CHANNEL_CODE);
        //System.out.println("remoteResult = " + remoteResult);

        //System.out.println(miguApiService.sxhQueryRingList("13605935565",BIZ_SXH_CHANNEL_CODE));
        //System.out.println(miguApiService.sxhRingOperate("13605935565","1799802752688324608",BIZ_SXH_CHANNEL_CODE,"2","600926100700247126"));
        //System.out.println(miguApiService.sxhRingOperate("13605935565","1799802752688324608",BIZ_SXH_CHANNEL_CODE,"2","600926600602282534"));
        //System.out.println(miguApiService.sxhRingOperate("13605935565","1799802752688324608",BIZ_SXH_CHANNEL_CODE,"3","600926100700247126"));
        //System.out.println(miguApiService.sxhRingOperate("13605935565","1799802752688324608",BIZ_SXH_CHANNEL_CODE,"3","600926600602282534"));
        //System.out.println(miguApiService.sxhQueryRingList("13605935565",BIZ_SXH_CHANNEL_CODE));
        //System.out.println(miguApiService.sxhGetCircleId("13605935565", BIZ_SXH_CHANNEL_CODE));
        //System.out.println(miguApiService.sxhGetCircleId("18881484185", BIZ_SXH_CHANNEL_CODE_TEST));

        //miguApiService.sxhRingUpload("15915401656","1821447459222794240","00211A0",
        //        RequestBody.create(MediaType.parse("application/octet-stream"), new File("D:\\tmp_files\\vrbt_v1_img9.mp4")));

        //final String fileName = "D:\\tmp_files\\00211E4-result.txt";
        //final String outFileName = "D:\\tmp_files\\00211E4.txt";
        //final String channelCode = "00211E4";

        //miguApiService.bjhyQuery("19565337230", "00211A0");

        //final String fileName = "D:\\tmp_files\\00211A0-result-"+ LocalDate.now() +".txt";
        //final String outFileName = "D:\\tmp_files\\00211A0.txt";
        //final String channelCode = "00211A0";
        //try (PrintWriter printWriter = new PrintWriter(new FileWriter(fileName))){
        //    Files.lines(Paths.get(outFileName)).forEach(mobile->{
        //        final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, channelCode);
        //        final boolean bjhyMember = remoteResult.isBjhyMember();
        //        printWriter.println(mobile+"\t"+(bjhyMember ?"已包月":"未包月") + "\t"+(bjhyMember ?remoteResult.getValidTime():""));
        //    });
        //} catch (IOException e) {
        //    throw new RuntimeException(e);
        //}

        final String channelCode = "00211E4";
        final String inFileName = "D:\\tmp_files\\"+channelCode+ "_" +LocalDate.now() +".txt";
        final String outFileName = "D:\\tmp_files\\"+ channelCode + "_" +LocalDate.now() +"-result.txt";
        final String separator = "\t";

        final int parallelism = 24;
        ForkJoinPool forkJoinPool = new ForkJoinPool(parallelism);
        //long start = System.nanoTime();
        try (Stream<String> lines = Files.lines(Paths.get(inFileName)).parallel();
             PrintWriter PrintWriter = new PrintWriter(new FileWriter(outFileName))){
            final List<String> resultLines = forkJoinPool.submit(() ->
                            /* Parallel task here, for example */
                            lines.map(line -> {
                                String mobile = StringUtils.substringBefore(line, separator);
                                System.out.println(Thread.currentThread().getName() + "->" + mobile);
                                //final RemoteResult remoteResult = RemoteResult.fail("不做查询");
                                final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, channelCode);
                                return line + separator + (remoteResult.isBjhyMember() ? "是" : "否") + separator +(remoteResult.isBjhyMember() ?remoteResult.getValidTime():"");
                            }).collect(Collectors.toList()))
                    .get();
            resultLines.forEach(PrintWriter::println);
        } catch (IOException | InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            forkJoinPool.shutdown();
        }

    }

    @Test
    public void provinceBjhyOrder(){
        System.out.println(miguApiService.provinceBjhyOrder("18881484185", true));
        System.out.println();
    }

    /**
     * 特定渠道号包月校验
     */
    @Test
    public void monthCheck(){
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
            List<Map<String,String>> subscribeList= Lists.newArrayList();
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);

                if(!stringList.isEmpty()){
                    stringList.forEach(mobile->{
                        Subscribe sub=subscribeService.lambdaQuery().select(Subscribe::getMobile,Subscribe::getProvince,Subscribe::getChannel).eq(Subscribe::getMobile,mobile.trim()).eq(Subscribe::getChannel,"002118U").eq(Subscribe::getStatus,1).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
                        if(sub!=null){
                            final RemoteResult schResult=miguApiService.schQuery(mobile.trim(), sub.getChannel());
                            if(schResult.isSchMember()){
                                Map<String,String> map= Maps.newHashMap();
                                map.put("手机号",mobile.trim());
                                map.put("渠道号",sub.getChannel());
                                map.put("是否包月","是");
                                map.put("省份",sub.getProvince());
                                subscribeList.add(map);
                            }else{
                                Map<String,String> map= Maps.newHashMap();
                                map.put("手机号",mobile.trim());
                                map.put("渠道号",sub.getChannel());
                                map.put("是否包月","否");
                                map.put("省份",sub.getProvince());
                                subscribeList.add(map);
                            }
                        }
                    });
                }
            }
            // 创建ObjectMapper实例
            ObjectMapper objectMapper = new ObjectMapper();
            String json = null;
            try {
                json = objectMapper.writeValueAsString(subscribeList);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            log.info("产品列表:{}",json);

            try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\新建文本文档 (4).txt"))) {
                writer.write(json);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Qualifier("threadPoolExecutor")
    ThreadPoolExecutor executor;
    /**
     * 多线程特定渠道号包月校验
     */
    @Test
    public void monthCheckDecodeExecutor() {
                List<Map<String, String>> mobileList = Lists.newArrayList();
                File file = new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
                if (file.exists()) {
                    List<String> stringList = null;
                    try {
                        stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    if (!stringList.isEmpty()) {
                        int a = 0;
                        Map<String, List<String>> mapMobileList = Maps.newHashMap();
                        List<String> zero = Lists.newArrayList();
                        for (int i = 0; i < stringList.size(); i++) {
                            if(StringUtils.isNotBlank(stringList.get(i).trim())){
                                zero.add(stringList.get(i).trim());
                                if (i % 100 == 0) {
                                    a++;
                                    List<String> one = Lists.newArrayList();
                                    one.addAll(zero);
                                    mapMobileList.put("mobile" + a, one);
                                    zero.clear();
                                } else if (i == stringList.size() - 1) {
                                    a++;
                                    List<String> one = Lists.newArrayList();
                                    one.addAll(zero);
                                    mapMobileList.put("mobile" + a, one);
                                }
                            }

                        }
                        List<CompletableFuture<Void>> futures =Lists.newArrayList();
                        for (int i = 0; i < mapMobileList.size()+1; i++) {
                            int taskId = i;
                            List<String> mapList = mapMobileList.get("mobile" + taskId);
                            if(mapList!=null && mapList.size()>0){
                                CompletableFuture<Void> future =doSomethingAsync(mapList,mobileList);
                                futures.add(future);
                            }
                        }
                        try {
                            CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()])).get();
                            ObjectMapper objectMapper = new ObjectMapper();
                            String json = null;
                            try {
                                json = objectMapper.writeValueAsString(mobileList);
                            } catch (JsonProcessingException e) {
                                e.printStackTrace();
                            }
                            try (BufferedWriter writer = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\mobile.txt",true))) {
                                writer.write(json);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        } catch (ExecutionException e) {
                            e.printStackTrace();
                        }
                    }
                }
    }
    private CompletableFuture<Void>  doSomethingAsync(List<String> mapList, List<Map<String, String>> mobileList){
        CompletableFuture<Void> future =CompletableFuture.runAsync(() -> {
            mapList.forEach(mobile -> {
                Subscribe sub = subscribeService.lambdaQuery().select(Subscribe::getMobile, Subscribe::getProvince, Subscribe::getChannel).eq(Subscribe::getMobile, mobile.trim()).eq(Subscribe::getChannel, "002118U").eq(Subscribe::getStatus, 1).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
                if (sub != null) {
                    final RemoteResult schResult = miguApiService.schQuery(mobile.trim(), sub.getChannel());
                    if (schResult.isSchMember()) {
                        Map<String, String> maps = Maps.newHashMap();
                        maps.put("手机号", mobile.trim());
                        maps.put("渠道号", sub.getChannel());
                        maps.put("是否包月", "是");
                        maps.put("省份", sub.getProvince());
                        mobileList.add(maps);
                    } else {
                        Map<String, String> maps = Maps.newHashMap();
                        maps.put("手机号", mobile.trim());
                        maps.put("渠道号", sub.getChannel());
                        maps.put("是否包月", "否");
                        maps.put("省份", sub.getProvince());
                        mobileList.add(maps);
                    }
                }
            });
        }, executor);
        return future;
    }
}
