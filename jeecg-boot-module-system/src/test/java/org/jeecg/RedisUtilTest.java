package org.jeecg;

import com.eleven.cms.aspect.SmsDailyLimitAspect;
import com.eleven.cms.entity.HetuCouponCode;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.impl.SubscribeServiceImpl;
import com.google.common.collect.Lists;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.RedisUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;


/**
 * Author: <EMAIL>
 * Date: 2020/12/4 14:27
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RedisUtilTest {
    @Autowired
    RedisUtil redisUtil;

    /**
     * redis限流
     */
    @Test
    public void rateLimitV1(){
        String key = "rateLimit:ip:127.0.0.1";
        int limitCount = 5;
        int limitPeriod = 1;
        boolean isLimit;
        isLimit = redisUtil.rateLimit(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimit(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimit(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimit(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimit(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimit(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
    }

    /**
     * redis限流
     */
    @Test
    public void rateLimitV2(){
        String key = "rateLimit:ip:127.0.0.1_1";
        int limitCount = 5;
        int limitPeriod = 3600;
        boolean isLimit;
        isLimit = redisUtil.rateLimitV2(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimitV2(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimitV2(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimitV2(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimitV2(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
        isLimit = redisUtil.rateLimitV2(key, limitCount, limitPeriod);
        System.out.println("isLimit = " + isLimit);
    }

    @Test
    public void getStringValue(){
        //redisUtil.set("cms:cache:alipay:returnUrl","https://www.baidu.com");
        final String val = (String) redisUtil.get("cms:cache:alipay:returnUrl");
        System.out.println("val = " + val);
        final String nullVal = (String) redisUtil.get("key_not_exists");
        System.out.println("nullVal = " + nullVal);
    }


    @Test
    public void setIfAbsent(){
        final boolean first = redisUtil.setIfAbsent("test::key", "exists");
        System.out.println("first = " + first);
        final boolean sencond = redisUtil.setIfAbsent("test::key", "exists");
        System.out.println("sencond = " + sencond);

    }

    @Test
    public void setIfAbsentWithExpire() throws InterruptedException {
        final boolean first = redisUtil.setIfAbsent("test::key:expire", "exists",5);
        System.out.println("first = " + first);
        final boolean sencond = redisUtil.setIfAbsent("test::key:expire", "exists",5);
        System.out.println("sencond = " + sencond);

        TimeUnit.SECONDS.sleep(6);
        final boolean third = redisUtil.setIfAbsent("test::key:expire", "exists",5);
        System.out.println("third = " + third);

    }

    @Test
    public void testRedisNumberSet() {
        //String redisKey = SmsDailyLimitAspect.SMS_DAILY_COUNTER_KEY_PREFIX + LocalDate.now()+ ":" + "13400000000";
        //final long expire = Duration.between(LocalTime.now(), LocalTime.MAX).getSeconds();
        //redisUtil.del(redisKey);
        //final Object o = redisUtil.get(redisKey);
        //System.out.println("o = " + o);
        //
        //System.out.println((Integer) o);
        //
        //redisUtil.set(redisKey,1,expire);
        //final Object o1 = redisUtil.get(redisKey);
        //System.out.println("o1 = " + o1);
        //redisUtil.incr(redisKey, 1L);
        //final Object o2 = redisUtil.get(redisKey);
        //System.out.println("o2 = " + o2);
        //
        //
        ////redis中没有Long类型，存储进去后取出来会是Interger类型。需要自行转化，不可直接强转。否则将CCE。
        //redisUtil.set(redisKey,1L,expire);
        //final Object o3= redisUtil.get(redisKey);
        //System.out.println("o3 = " + o3);
        //redisUtil.incr(redisKey, 1L);
        //final Object o4 = redisUtil.get(redisKey);
        //System.out.println("o4= " + o4);
        String key = "testRedisKey";
        redisUtil.del(key);
        final Object o = redisUtil.get(key);
        System.out.println(o);
        System.out.println(o==null);
        final Integer l = (Integer)o;
        System.out.println(l);
        System.out.println(l==null);
        //int i = 1;
        //redisUtil.set(key, i);
        ////java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.Long (java.lang.Integer and java.lang.Long are in module java.base of loader 'bootstrap')
        //final Long l1 = (Long) redisUtil.get(key);
        //System.out.println(l1);
        //System.out.println(l1==null);
        Long j = 1L;
        redisUtil.set(key, j);
        //        ////java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.Long (java.lang.Integer and java.lang.Long are in module java.base of loader 'bootstrap')
        final Integer l2 = (Integer) redisUtil.get(key);
        System.out.println(l2);
        System.out.println(l2==null);

        Long k = 1L;
        redisUtil.set(key, k);
        //        ////java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.Long (java.lang.Integer and java.lang.Long are in module java.base of loader 'bootstrap')
        final Long l3 = ((Number)redisUtil.get(key)).longValue();
        System.out.println(l3);
        System.out.println(l3==null);
        redisUtil.del(key);

        redisUtil.del(key);

    }

    @Test
    public void testIncrAndExpire() {
        //String redisKey = "testIncrAndExpire";
        //final long expire = Duration.between(LocalTime.now(), LocalTime.MAX).getSeconds();
        //redisUtil.del(redisKey);
        //final Object o = redisUtil.get(redisKey);
        //System.out.println("o = " + o);
        //final long incr = redisUtil.incr(redisKey, 1L);
        //redisUtil.expire(redisKey,expire);
        //System.out.println("incr = " + incr);
        //final Object o1 = redisUtil.get(redisKey);
        //System.out.println("o1 = " + o1);
        //System.out.println(redisUtil.decr(redisKey,1L));
        //System.out.println(redisUtil.decr(redisKey,1L));
        long dayCounter = getDayCounter();
        System.out.println("dayCounter = " + dayCounter);
        dayCounter = incrDayCounter();
        System.out.println("dayCounter = " + dayCounter);
        dayCounter = getDayCounter();
        System.out.println("dayCounter = " + dayCounter);
        dayCounter = incrDayCounter();
        System.out.println("dayCounter = " + dayCounter);
        dayCounter = getDayCounter();
        System.out.println("dayCounter = " + dayCounter);

    }

    @Test
    public void setGetObject(){
        Subscribe upd = new Subscribe();
        upd.setId("11111111111111111");
        upd.setIspOrderNo("2222222222222222222");
        String mobile = "13400000000";
        upd.setMobile(mobile); //拿给reidis作为缓存
        String dianxinExistsOrderKey = SubscribeServiceImpl.DIANXIN_ORDER_FREQUENCY_KEY_PREFIX+ mobile;
        boolean set = redisUtil.set(dianxinExistsOrderKey, upd, SubscribeServiceImpl.DIANXIN_ORDER_FREQUENCY_CACHE_SECONDS);
        System.out.println("set = " + set);
        Subscribe sub = (Subscribe)redisUtil.get(dianxinExistsOrderKey);
        System.out.println("sub = " + sub);

    }



    @Test
    public void ipLocation() {
        String[] ipLocation = IPUtils.ipLocation("**************");
        System.out.println(ipLocation[0]+":"+ipLocation[1]+":"+ipLocation[2]);
        //ipLocation = IPUtils.ipLocation("127.0.0.1");
        //System.out.println(ipLocation[0]+":"+ipLocation[1]+":"+ipLocation[2]);
    }


    /**
     * 获取当天订购计数
     *
     */
    public long getDayCounter() {
        final String todayCounterKey = "testIncrAndExpire" + LocalDate.now();
        if (!redisUtil.hasKey(todayCounterKey)) {
            return 0;
        } else {
            return (Integer) redisUtil.get(todayCounterKey);
        }
    }

    /**
     * 增加当天订购计数
     *
     */
    public long incrDayCounter() {
        final String todayCounterKey = "testIncrAndExpire" + LocalDate.now();
        if (!redisUtil.hasKey(todayCounterKey)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(todayCounterKey, 1, expire);
            return 1;
        } else {
            return redisUtil.incr(todayCounterKey, 1);
        }
    }

    @Test
    public void testRedisListOpt() {
        final String key = "testRedisList";
        System.out.println(redisUtil.lGetListSize(key));
        System.out.println(redisUtil.lPop(key));

        System.out.println(redisUtil.lSet(key, Lists.newArrayList(new HetuCouponCode(),new HetuCouponCode())));
        System.out.println(redisUtil.lGetListSize(key));
        System.out.println((HetuCouponCode)redisUtil.lPop(key));
        System.out.println(redisUtil.lGetListSize(key));
        System.out.println((HetuCouponCode)redisUtil.lPop(key));
        System.out.println(redisUtil.lGetListSize(key));
        System.out.println((HetuCouponCode)redisUtil.lPop(key));
       
    }
}



