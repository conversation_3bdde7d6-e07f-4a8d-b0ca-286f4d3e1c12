package org.jeecg;

import com.eleven.cms.remote.LiantongVrbtService;
import com.eleven.cms.util.BizConstant;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LiantongVrbtServiceTest {

    @Autowired
    LiantongVrbtService liantongVrbtService;

    //@Test
    //public void testScanner() throws Exception{
    //    Scanner scanner = new Scanner(System.in);
    //    String line = scanner.nextLine();
    //    System.out.println(line);
    //}
    
    //@Test
    //public void testOrderNotify(){
    //    //curl --insecure -X POST -H "Authorization:3000008270:test" -H "timestamp:1609207718338" -H "Content-Type:application/json; charset=utf-8" -d '{"test":"foo"}' "https://crbt.cdyrjygs.com/cms-vrbt/api/liantongVrbt/orderNotify"
    //    liantongVrbtService.testOrderNotify();
    //}

    @Test
    public void querySubedProductsAndRing() {
        liantongVrbtService.qrySubedProductsNoToken("18613222501", BizConstant.BIZ_LT_CHANNEL_DEFAULT);
        liantongVrbtService.userVideoRingDepotList("18613222501",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void isSubedMon() {
        final boolean subedMon = liantongVrbtService.isSubedMon("13137924229",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
        System.out.println("subedMon = " + subedMon);
    }

    @Test
    public void sendLoginCode() {
        liantongVrbtService.sendLoginCode("18613222501",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void codeLogin() {
        liantongVrbtService.codeLogin("18613222501","",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void onePointProductMon() {
        liantongVrbtService.onePointProductMon("18613222501",null,null,BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void sendMsg() {
        liantongVrbtService.sendMsg("18613222501",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void orderRingOnePointMon() {
        liantongVrbtService.settingRingOnePointMon("18613222501","80768000202011189337270",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void sendVerifyCode() {
        liantongVrbtService.sendVerifyCode("18613222501",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void unSubProductWithVCode() {
        liantongVrbtService.unSubProductWithVCode("18613222501","570541",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void unSubProductNoToken() {
        liantongVrbtService.unSubProductNoToken("18613222501",BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }

    @Test
    public void isCanOpenVideoRing() {
        final boolean isCanOpenVideoRing = liantongVrbtService.isCanOpenVideoRing("18613222501",BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
        System.out.println("isCanOpenVideoRing = " + isCanOpenVideoRing);
    }


    @Test
    public void exchangeRing() {
        liantongVrbtService.exchangeRing("18848341487",BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
    }

    @Test
    public void licenseQuery() {
        liantongVrbtService.licenseQuery("18848341487",BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
    }
}