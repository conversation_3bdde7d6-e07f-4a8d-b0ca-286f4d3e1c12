package org.jeecg;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.service.impl.SubscribeServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class VrbtCommonServiceImplTest {

    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Test
    public void testGetSmsCode() throws JsonProcessingException {
//        IBizCommonService bizCommonService = SpringUtil.getBean("sxhCommonService");
//        String jsonData = "{\n" +
//                "    \"transactionId\": \"\",\n" +
//                "    \"mobile\": \"13699402402\",\n" +
//                "    \"channel\": \"002115U\",\n" +
//                "    \"crack\":\"1\",\n" +
//                "    \"smsCode\":\"\",\n" +
//                "    \"_\": 1669018640734\n" +
//                "}";
//        Subscribe subscribe = mapper.readValue(jsonData, Subscribe.class);
//        subscribe.setIsp("1");
//        bizCommonService.getSmsCode(subscribe);

        boolean bizRepeat = SpringUtil.getBean(SubscribeServiceImpl.class).checkBizRepeat("13851827217", "014X02H");
        System.out.println(bizRepeat);
        bizRepeat = SpringUtil.getBean(SubscribeServiceImpl.class).checkBizRepeat("13767675786", "QYCL_GR_MH");
        System.out.println(bizRepeat);
        //bizCommonService.submitSmsCode(subscribe);
    }

}