package org.jeecg;

import com.eleven.cms.config.SmsNotifyProperties;
import com.eleven.cms.config.WxMpProperties;
import com.eleven.cms.entity.CmsAlarmUser;
import com.eleven.cms.service.ICmsAlarmUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ICmsAlarmUserServiceTest {

    @Autowired
    private ICmsAlarmUserService iCmsAlarmUserService;

    @Autowired
    private SmsNotifyProperties smsNotifyProperties;

    @Autowired
    private WxMpProperties wxMpProperties;

    Map<String, String> mobileRealNameMapping = new HashMap<String, String>() {
        {
            put("18080928200", "叶");
            put("13550358512", "小峰");
            put("13880538866", "刘启");
            put("13408669942", "潘潘");
            put("13550027280", "罗超");

            put("18502860309", "侯旭");
            put("17723327836", "熊君");
            put("17711089556", "向宏");
            put("13678193992", "肖玲杰");
            put("18180212398", "杨锐玲");

            put("15708497294", "董松炜");
            put("13228193081", "陈子威");
            put("18299061763", "宾仕林");
            put("18010645187", "谢天甲");
            put("13265923435", "投放二部成员");
        }
    };

    @Test
    public void testAdd() {
        List<String> alertNotifyMobileList = smsNotifyProperties.getAlertNotifyMobileList();
        List<String> onlyWxMsgMobileList = smsNotifyProperties.getOnlyWxMsgMobileList();

        alertNotifyMobileList.addAll(onlyWxMsgMobileList);
        List<String> list = alertNotifyMobileList.stream().distinct().collect(Collectors.toList());

        Map<String, String> mobileOpenIdMap = wxMpProperties.getMobileOpenIdMap();

        List<CmsAlarmUser> userList = new ArrayList<>();
        for (String phone : list) {
            CmsAlarmUser cmsAlarmUser = new CmsAlarmUser();
            cmsAlarmUser.setCreateBy("admin");
            cmsAlarmUser.setPhone(phone);
            cmsAlarmUser.setOpenId(mobileOpenIdMap.get(phone));
            cmsAlarmUser.setRealName(mobileRealNameMapping.get(phone));
            userList.add(cmsAlarmUser);
        }
        iCmsAlarmUserService.saveBatch(userList);
    }
}
