package org.jeecg;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.eleven.cms.config.OutsideProperties;
import com.eleven.cms.config.WandaSichuanYidongProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.repository.EsSubscribeRepository;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.impl.SubscribeServiceImpl;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.VrbtCombinResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.eleven.cms.util.BizConstant.*;

/**
 * Author: <EMAIL>
 * Date: 2020/3/22 16:48
 * Desc:
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
class SubscribeServiceImplTest {
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;

    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private SubscribeVerifyService subscribeVerifyService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    OutsideCallbackService outsideCallbackService;

    @Autowired
    OutsideProperties outsideProperties;
    @Autowired
    IMemberService memberService;
    @Autowired
    EsSubscribeRepository esSubscribeRepository;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    WandaSichuanYidongProperties wandaSichuanYidongProperties;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;

    @Test
    public void isVrbtDiyMember() {
        final boolean vrbtDiyMember = memberService.isVrbtDiyMember("***********");
        System.out.println("vrbtDiyMember = " + vrbtDiyMember);
    }

    @Test
    public void hasSuccSubAfter() {
        final Subscribe subscribe = subscribeService.getById("1686633183560974338");
        final String channelCode = subscribe.getChannel();
        //判断该手机号在后续1个小时内有无成功开通记录,如果有成功的,本条就应保持开通失败
        boolean hasSuccSubAfter = subscribeService.count(
                Wrappers.<Subscribe>lambdaQuery()
                        .eq(Subscribe::getMobile,subscribe.getMobile())
                        .between(Subscribe::getCreateTime,subscribe.getCreateTime(), DateUtils.addHours(subscribe.getCreateTime(),1))
                        .eq(Subscribe::getChannel,channelCode)
                        .eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS)
        )>0;
        System.out.println("hasSuccSubAfter = " + hasSuccSubAfter);

        final Subscribe subscribe1 = subscribeService.getById("1686939024512065538");
        final String channelCode1 = subscribe1.getChannel();
        //判断该手机号在后续1个小时内有无成功开通记录,如果有成功的,本条就应保持开通失败
        boolean hasSuccSubAfter1 = subscribeService.count(
                Wrappers.<Subscribe>lambdaQuery()
                        .eq(Subscribe::getMobile,subscribe1.getMobile())
                        .between(Subscribe::getCreateTime,subscribe1.getCreateTime(), DateUtils.addHours(subscribe1.getCreateTime(),1))
                        .eq(Subscribe::getChannel,channelCode1)
                        .eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS)
        )>0;
        System.out.println("hasSuccSubAfter1 = " + hasSuccSubAfter1);
    }

    @Test
    public void test1(){

        SpringUtil.getBean(SubscribeServiceImpl.class).incrChannelProvinceLimit("HLJYD_VRBT", "黑龙江");
//        SpringUtil.getBean(SubscribeServiceImpl.class).incrChannelProvinceLimit("00210PP", "四川");



//        List<Subscribe> list = subscribeService.lambdaQuery()
//                .eq(Subscribe::getIsp, "1")
//                .eq(Subscribe::getStatus, 1).eq(Subscribe::getChannel, "00210PP")
//                .gt(Subscribe::getCreateTime, "2022-04-01")
//                .lt(Subscribe::getCreateTime, "2022-04-22").list();
//
////
//        list.forEach(subscribe -> {
//            RemoteResult bjhyResult = miguApiService.bjhyQuery(subscribe.getMobile(), subscribe.getChannel());
//            int status = bjhyResult.isBjhyMember() ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
//            Subscribe sub = new Subscribe();
//            sub.setId(subscribe.getId());
//            sub.setVerifyStatusDaily(status);
//            subscribeService.updateSubscribeDbAndEs(sub);
//        });


//        List<Subscribe> list = subscribeService.lambdaQuery()
//                .eq(Subscribe::getIsp, "1")
//                .eq(Subscribe::getStatus, 1)
//                .in(Subscribe::getChannel, "00210OC","00210OW")
//                .in(Subscribe::getSubChannel, "A7pTA1","A7pMR1", "A7pMR2")
//                .gt(Subscribe::getCreateTime, "2021-07-01")
//                .lt(Subscribe::getCreateTime, "2021-10-01").list();
//
//        System.out.println(list.size());
//
//        list.forEach(subscribe -> {
//
//            Integer status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), subscribe.getChannel(), false);
//            Subscribe sub = new Subscribe();
//            sub.setId(subscribe.getId());
//            sub.setPrice(status);
//            subscribeService.updateSubscribeDbAndEs(sub);
//        });





        //String sql = "select * from cms_subscribe WHERE create_time > '2022-04-21' and create_time <  '2022-04-22' and isp = 4 and `status` = 1 and channel = 'DXVRBT' and sub_channel = 'OSVRBT03'";

//        List<Subscribe> list = subscribeService.lambdaQuery().eq(Subscribe::getIsp, "4").eq(Subscribe::getStatus, 1).eq(Subscribe::getChannel, "DXVRBT").eq(Subscribe::getSubChannel, "OSVRBT03")
//                .ge(Subscribe::getCreateTime, "2022-04-21")
//                .le(Subscribe::getCreateTime, "2022-04-22").list();
//
//        list.forEach(subscribe -> {
//            outsideCallbackService.outSidecallback(subscribe, "");
//        });

    }
//    @Test
    void validateMonthStatus() {
        //subscribeService.lambdaQuery().between(Subscribe::getCreateTime, LocalDate.of(2021, 6, 1).atStartOfDay(),
        //        LocalDate.of(2021, 6, 2).atTime(LocalTime.MAX))
        //                .eq(Subscribe::getChannel,MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1)
        //                .eq(Subscribe::getStatus,1)
        //                .list()
        //                .forEach(subscribe -> {
        //                    final RemoteResult result = miguApiService.cpmbQuery(subscribe.getMobile(),
        //                            subscribe.getChannel());
        //                    int validateStatus = result.isOK() ? 1 : 0;
        //                    try {
        //                        subscribeService.lambdaUpdate().eq(Subscribe::getId, subscribe.getId())
        //                                        .set(Subscribe::getVerifyStatus,validateStatus)
        //                                        .set(Subscribe::getExtra,new ObjectMapper().writeValueAsString(result))
        //                                        .update();
        //                    } catch (JsonProcessingException e) {
        //                        e.printStackTrace();
        //                    }
        //                });
        //将跑的结果保存在city_code字段
        SFunction<Subscribe, String> cityCodeFun = Subscribe::getCityCode;
        subscribeService.lambdaQuery()
                .select(Subscribe::getId,Subscribe::getMobile,Subscribe::getChannel)
                //.between(Subscribe::getCreateTime,
                //        LocalDate.of(2021, 7, 1).atStartOfDay(),
                //        LocalDate.of(2021, 7, 31).atTime(LocalTime.MAX))
                .eq(Subscribe::getSubChannel,"jb02")
                //.eq(Subscribe::getSubChannel,"A7pTA1")
                .eq(Subscribe::getIsp,"1")
                .eq(Subscribe::getStatus,1)
                .isNull(cityCodeFun)
                .list()
                .forEach(subscribe -> {
                    final RemoteResult result = miguApiService.vrbtMonthStatusQuery(subscribe.getMobile(), subscribe.getChannel(), true);
                    int validateStatus = result.isOK() && !"0".equals(result.getStatus()) ? 1 : 0;
                    try {
                        subscribeService.lambdaUpdate().eq(Subscribe::getId, subscribe.getId()).set(cityCodeFun,validateStatus).update();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
    }


    @Test
    public void test() throws InterruptedException {




//        LocalDateTime start = YearMonth.now().minusMonths(6L).atDay(1).atTime(LocalTime.MIN);
//        LocalDateTime end = YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MAX);
//
//        List<String> idList = subscribeService.lambdaQuery().select(Subscribe::getId).between(Subscribe::getCreateTime,
//                start, end).eq(Subscribe::getStatus, 1).eq(Subscribe::getIsp, "1").and(
//                i -> i.isNull(Subscribe::getPrice).or().eq(Subscribe::getPrice, 1)).last("LIMIT 1000").list()
//                .stream().map(Subscribe::getId).collect(Collectors.toList());









//        Subscribe subscribe = new Subscribe();
//        subscribe.setId(UUID.randomUUID().toString());
//        subscribe.setMobile("17781670643");
//        subscribe.setIspOrderNo("885c34df297142ea8e153a4cf2ce09de");
//        pushSubscribeService.pushDxVrbt(subscribe);

//        LocalDateTime start = YearMonth.now().minusMonths(6L).atDay(1).atTime(LocalTime.MIN);
//        LocalDateTime end = YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MAX);
//        List<String> idList = subscribeService.lambdaQuery().select(Subscribe::getId).between(Subscribe::getCreateTime,
//                start, end).eq(Subscribe::getStatus, 1).eq(Subscribe::getIsp, "1").and(
//                i -> i.isNull(Subscribe::getPrice).or().eq(Subscribe::getPrice, 1)).last("LIMIT 1000").list()
//                .stream().map(Subscribe::getId).collect(Collectors.toList());
//        System.out.println("idList = " + idList);
//
//        Subscribe upd = new Subscribe();
//        upd.setId("1318815322463617026");
//        upd.setPrice(1);
//        subscribeService.updateSubscribeDbAndEs(upd);
//        TimeUnit.SECONDS.sleep(10L);

//        subscribeService.findLast6MonthToVerify();
    }



    //public Integer monthVerify(Subscribe subscribe) {
    //    int status = -1;
    //    switch (subscribe.getBizType()){
    //        case BizConstant.BIZ_TYPE_VRBT:
    //            status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), subscribe.getChannel())> 0 ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS: BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
    //            break;
    //        case BizConstant.BIZ_TYPE_CPMB:
    //            final RemoteResult result = miguApiService.cpmbQuery(subscribe.getMobile(), subscribe.getChannel());
    //            status = result.isOK() ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
    //            break;
    //    }
    //    return status;
    //}

    //@Test
    //public void hasRecentSucceedOrder(){
    //    boolean b = subscribeService.hasRecentSucceedOrder("15622092962");
    //    System.out.println("b = " + b);
    //    b = subscribeService.hasRecentSucceedOrder("15622092962");
    //    System.out.println("b = " + b);
    //}

    //@Test
    //public void updateSatus() throws InterruptedException {
    //    val arr = new String[]{
    //            "1470310037561405442",  "1470574248094871554"
    //    };
    //    for (String id : arr) {
    //        Subscribe upd = new Subscribe();
    //        upd.setId(id);
    //        upd.setStatus(1);
    //        upd.setResult("业务开通成功结果=>{\"resCode\":\"000000\",\"resMsg\":\"成功\"}");
    //        upd.setOpenTime(new Date());
    //        upd.setModifyTime(new Date());
    //        subscribeService.updateSubscribeDbAndEs(upd);
    //    }
    //    TimeUnit.SECONDS.sleep(60L);
    //}

    @Test
    public void findLast6MonthToVerify(){
        List<String> last6MonthToVerify = subscribeVerifyService.findLast6MonthToVerify(YearMonth.now().minusMonths(1L).atEndOfMonth());
        System.out.println("last6MonthToVerify = " + last6MonthToVerify);
    }

    @Test
    public void fetchRings(){
        String[] ids = {"1321983550266404865",
                "1324226254324969473",
                "1324259548714315777",
                "1324590051799674881",
                "1324804021278027777",
                "1324894777669922818",
                "1324913073102336001",
                "1324926866310115329",
                "1324927021235122178",
                "1324953750041341953",
                "1324953812142206977",
                "1324978828258914306",
                "1324985003264258050",
                "1324990070319034369",
                "1325012800405843970",
                "1325012859876880386",
                "1325025290896355329",
                "1325039170431299586",
                "1325094342427750401",
                "1325094452549201921",
                "1325106852883148801",
                "1325281610664849409",
                "1325314663843966978",
                "1325330712781991937",
                "1325348748494712833",
                "1325349016733036546",
                "1325353584002936834",
                "1325660777365471234",
                "1325685701723389953",
                "1326031426806173698",
                "1326042174009724929",
                "1326045573040132098",
                "1326057573124231170",
                "1326063254799732738",
                "1326126464261173249",
                "1326360784812912641",
                "1326370502281277442",
                "1326373671346384897",
                "1326373844793438209",
                "1326398176970158082",
                "1326404555428605954",
                "1326645463080337410",
                "1326649305679364098",
                "1326712034159067137",
                "1326735199933399041",
                "1326735479148216321",
                "1326735556369547266",
                "1326748676852908033",
                "1326821820783493121",
                "1326851050963566593",
                "1326861989880365057",
                "1326868401306042370",
                "1326868983014064130",
                "1326887802243743746",
                "1326907447931666433",
                "1326908938021076994",
                "1326916042530324482",
                "1327008708630773762",
                "1327010542049132546",
                "1327087180027887618",
                "1327094427609038850",
                "1327178624830926850",
                "1327178624856092674",
                "1327178624864481281",
                "1327178685447008258",
                "1327200245956235265",
                "1327207811302051841",
                "1327218089309786113",
                "1327222266010292226",
                "1327234842337234946",
                "1327244823782510593",
                "1327261557935591426",
                "1327275653644963842",
                "1327328330768732161",
                "1327356475790086146",
                "1327361431884550146",
                "1327370544857239554",
                "1327452725990412290",
                "1327470367073652737",
                "1327480372288049153",
                "1327538994531553281",
                "1327539275252125697",
                "1327577236198936577",
                "1327805592236478465",
                "1327826149929668609",
                "1327842216349552641",
                "1327884542988013570",
                "1327940542717775873",
                "1327957452985487362",
                "1327972176280178689",
                "1327981657810554882",
                "1327992893663105025",
                "1327993081748279298",
                "1328014744409886722",
                "1328050222089654273",
                "1328135653636460545",
                "1328168382780428290",
                "1328168824671326210",
                "1384352231822299137",
        };
        List<Subscribe> list = subscribeService.lambdaQuery().in(Subscribe::getId, ids).list();
        list.forEach(sub->{
            String mobile = sub.getMobile();
            String channel = sub.getChannel();
            Integer monthVerify = miguApiService.verifyMiguVrbtMonthStatus(mobile, channel,false)> 0 ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS: BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
            sub.setPrice(monthVerify);
            if(BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS.equals(monthVerify)){
                //{
                //    "recordSum": "1",
                //    "resultType": "2",
                //    "toneInfos": [
                //        {
                //            "cpCode": "600926",
                //            "info": "",
                //            "offset": "",
                //            "orderTime": "2020-11-15",
                //            "orderTimes": "0",
                //            "price": "300",
                //            "setTimes": "0",
                //            "singerName": "",
                //            "singerNameLetter": "#",
                //            "status": "1",
                //            "toneID": "600926000001308028",
                //            "toneName": "快艾特你的姐妹一起暴富吧！-月哥说了算",
                //            "toneNameLetter": "K",
                //            "tonePreListenAddress": "",
                //            "toneType": "1",
                //            "toneValidDay": "2022-03-31",
                //            "updateTime": "2020-12-31 00:57"
                //        }
                //    ],
                //    "resCode": "000000",
                //    "resMsg": "成功"
                //}
                String toneQuery = miguApiService.vrbtToneQuery(mobile, channel, null, null, null, null);
                try {
                    JsonNode tree = mapper.readTree(toneQuery);
                    List<String> toneInfoList =new ArrayList<>();

                    if(tree.at("/recordSum").asInt()>0){
                        tree.at("/toneInfos").elements().forEachRemaining(item->{
                            toneInfoList.add(item.get("toneID").asText()+":"+item.get("toneName").asText());
                        });
                    }
                    String collect = toneInfoList.stream().collect(Collectors.joining(","));
                    sub.setUserAgent(collect);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            subscribeService.updateById(sub);
        });
    }


    @Test
    public void customVerify(){
        subscribeService.lambdaQuery().select(Subscribe::getId,Subscribe::getMobile,Subscribe::getChannel)
                .in(Subscribe::getProvince,"北京","天津")
                .eq(Subscribe::getIsp,"1")
                .in(Subscribe::getChannel,"00210OC","00210OW")
                .eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS)
                .isNull(Subscribe::getCityCode)
                .list()
                .forEach(sub->{
                    String mobile = sub.getMobile();
                    String channel = sub.getChannel();
                    //Integer monthVerify = miguApiService.verifyMiguVrbtMonthStatus(mobile, channel)> 0 ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS: BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
                    VrbtCombinResult result = miguApiService.vrbtFunAndMonthStatusQuery(mobile, channel);

                    subscribeService.lambdaUpdate().eq(Subscribe::getId,sub.getId())
                            .set(Subscribe::getProvinceCode,result.isMonth()?"1":"0")
                            .set(Subscribe::getCityCode,result.isFun()?"1":"0")
                            .update();
                });
        
    }

    ///**
    // * 北京天津视频彩铃包月和功能每日少量退订(1天退100个)
    // */
    //@Test
    //public void cancelVrbtBeijinTianjin(){
    //    //String lineNumbers = "13520810667,00210OC,北京北京,1,1\n" + "13621022084,00210OC,北京北京,1,1\n" + "18801270358,00210OC,北京北京,1,1\n" + "18310265793,00210OC,北京北京,1,1\n" + "13718749778,00210OC,北京北京,1,1\n" + "18301054227,00210OC,北京北京,1,1\n" + "13717551735,00210OC,北京北京,1,1\n" + "18701470120,00210OC,北京北京,1,1\n" + "13910855182,00210OC,北京北京,1,1\n" + "18301221632,00210OC,北京北京,1,1\n" + "18310255283,00210OC,北京北京,1,1\n" + "15726603360,00210OC,北京北京,1,1\n" + "13716682679,00210OC,北京北京,1,1\n" + "18710214731,00210OC,北京北京,1,1\n" + "15210675715,00210OC,北京北京,1,1\n" + "13522327665,00210OC,北京北京,1,1\n" + "13716697630,00210OC,北京北京,1,1\n" + "15210235927,00210OC,北京北京,1,1\n" + "15201326513,00210OC,北京北京,1,1\n" + "13718724336,00210OC,北京北京,1,1\n" + "13718374858,00210OC,北京北京,1,1\n" + "13651141085,00210OC,北京北京,1,1\n" + "18410168513,00210OC,北京北京,1,1\n" + "18801274822,00210OC,北京北京,1,1\n" + "18310775235,00210OC,北京北京,1,1\n" + "13718518133,00210OC,北京北京,1,1\n" + "13466647229,00210OC,北京北京,1,1\n" + "18801107752,00210OC,北京北京,1,1\n" + "15010720112,00210OC,北京北京,1,1\n" + "13717900288,00210OC,北京北京,1,1\n" + "13720043985,00210OC,北京北京,1,1\n" + "13681158956,00210OC,北京北京,1,1\n" + "18410400601,00210OC,北京北京,1,1\n" + "13718361332,00210OC,北京北京,1,1\n" + "13426047489,00210OC,北京北京,1,1\n" + "13488673645,00210OC,北京北京,1,1\n" + "13436807977,00210OC,北京北京,1,1\n" + "15210793130,00210OC,北京北京,1,1\n" + "15010094578,00210OC,北京北京,1,1\n" + "18710214413,00210OC,北京北京,1,1\n" + "15712802648,00210OC,北京北京,1,1\n" + "13681137206,00210OC,北京北京,1,1\n" + "13521067367,00210OC,北京北京,1,1\n" + "15001095435,00210OC,北京北京,1,1\n" + "15712963278,00210OW,北京北京,1,1\n" + "13521105171,00210OW,北京北京,1,1\n" + "15811508813,00210OW,北京北京,1,1\n" + "13718995694,00210OW,北京北京,1,1\n" + "13520246714,00210OW,北京北京,1,1\n" + "18410429176,00210OW,北京北京,1,1\n" + "15101603108,00210OW,北京北京,1,1\n" + "13716858616,00210OW,北京北京,1,1\n" + "18710006902,00210OW,北京北京,1,1\n" + "15910775963,00210OW,北京北京,1,1\n" + "13681348529,00210OW,北京北京,1,1\n" + "13911393477,00210OW,北京北京,1,1\n" + "13520028598,00210OW,北京北京,1,1\n" + "13701266138,00210OW,北京北京,1,1\n" + "15210519331,00210OW,北京北京,1,1\n" + "13522054566,00210OW,北京北京,1,1\n" + "13718234590,00210OW,北京北京,1,1\n" + "15010406470,00210OW,北京北京,1,1\n" + "15010348150,00210OW,北京北京,1,1\n" + "13701334461,00210OW,北京北京,1,1\n" + "15811371205,00210OW,北京北京,1,1\n" + "13693684030,00210OW,北京北京,1,1\n" + "13716868690,00210OW,北京北京,1,1\n" + "13521754798,00210OW,北京北京,1,1\n" + "13521246398,00210OW,北京北京,1,1\n" + "15910441750,00210OW,北京北京,1,1\n" + "15910683775,00210OW,北京北京,1,1\n" + "18401324421,00210OW,北京北京,1,1\n" + "13581656162,00210OW,北京北京,1,1\n" + "15001136352,00210OW,北京北京,1,1\n" + "15810218365,00210OW,北京北京,1,1\n" + "15810870472,00210OW,北京北京,1,1\n" + "18301406755,00210OW,北京北京,1,1\n" + "18801033177,00210OW,北京北京,1,1\n" + "15010621690,00210OW,北京北京,1,1\n" + "15001283963,00210OW,北京北京,1,1\n" + "15201199103,00210OW,北京北京,1,1\n" + "15210970387,00210OW,北京北京,1,1\n" + "13611371957,00210OW,北京北京,1,1\n" + "13716843148,00210OW,北京北京,1,1\n" + "13691275221,00210OW,北京北京,1,1\n" + "15810364653,00210OW,北京北京,1,1\n" + "13718185574,00210OW,北京北京,1,1\n" + "15701228151,00210OW,北京北京,1,1\n" + "13691084736,00210OW,北京北京,1,1\n" + "18810032041,00210OW,北京北京,1,1\n" + "13601397427,00210OW,北京北京,1,1\n" + "13718029243,00210OW,北京北京,1,1\n" + "15210618437,00210OW,北京北京,1,1\n" + "18301615120,00210OW,北京北京,1,1\n" + "15210304764,00210OW,北京北京,1,1\n" + "15711026675,00210OW,北京北京,1,1\n" + "13521321570,00210OW,北京北京,1,1\n" + "18810102237,00210OW,北京北京,1,1\n" + "18811323634,00210OW,北京北京,1,1\n" + "13439875116,00210OW,北京北京,1,1\n" + "13716417857,00210OW,北京北京,1,1\n" + "15101025652,00210OW,北京北京,1,1\n" + "15711047546,00210OW,北京北京,1,1\n" + "15001276702,00210OW,北京北京,1,1\n" + "13693318380,00210OW,北京北京,1,1\n" + "13718353968,00210OW,北京北京,1,1\n" + "13522897483,00210OW,北京北京,1,1\n" + "13699148660,00210OW,北京北京,1,1\n" + "13522688890,00210OW,北京北京,1,1\n" + "18810255917,00210OW,北京北京,1,1\n" + "13683376332,00210OW,北京北京,1,1\n" + "18410389137,00210OW,北京北京,1,1\n" + "13693329169,00210OW,北京北京,1,1\n" + "15701309053,00210OW,北京北京,1,1\n" + "13821408559,00210OW,天津天津,1,1\n" + "15822707632,00210OW,天津天津,1,1\n" + "15022150220,00210OW,天津天津,1,1\n" + "15122455156,00210OW,天津天津,1,1\n" + "15122500444,00210OW,天津天津,1,1\n" + "13820693714,00210OW,天津天津,1,1\n" + "15822712269,00210OW,天津天津,1,1\n" + "15222346601,00210OW,天津天津,1,1\n" + "15222538792,00210OW,天津天津,1,1\n" + "15022675476,00210OW,天津天津,1,1\n" + "15822607361,00210OW,天津天津,1,1\n" + "15822456276,00210OW,天津天津,1,1\n" + "13672137098,00210OW,天津天津,1,1\n" + "15822190507,00210OW,天津天津,1,1\n" + "15222820675,00210OW,天津天津,1,1\n" + "18722202959,00210OW,天津天津,1,1\n" + "18722623485,00210OW,天津天津,1,1\n" + "18822178533,00210OW,天津天津,1,1\n" + "15022243022,00210OW,天津天津,1,1\n" + "15822601278,00210OW,天津天津,1,1\n" + "15822126559,00210OW,天津天津,1,1\n" + "13612029114,00210OW,天津天津,1,1\n" + "13752646657,00210OW,天津天津,1,1\n" + "15022628687,00210OW,天津天津,1,1\n" + "15022546489,00210OW,天津天津,1,1\n" + "15122456513,00210OW,天津天津,1,1\n" + "15900283933,00210OW,天津天津,1,1\n" + "15122054916,00210OW,天津天津,1,1\n" + "15822611171,00210OW,天津天津,1,1\n" + "15022483375,00210OW,天津天津,1,1\n" + "15222110029,00210OW,天津天津,1,1\n" + "18812786491,00210OW,天津天津,1,1\n" + "13512829814,00210OW,天津天津,1,1\n" + "15900229509,00210OW,天津天津,1,1\n" + "15022398669,00210OW,天津天津,1,1\n" + "15922014927,00210OW,天津天津,1,1\n" + "13512275344,00210OW,天津天津,1,1\n" + "13821885238,00210OW,天津天津,1,1\n" + "15122723571,00210OW,天津天津,1,1\n" + "18892250627,00210OW,天津天津,1,1\n" + "15922010253,00210OW,天津天津,1,1\n" + "13612109325,00210OW,天津天津,1,1\n" + "17802272377,00210OW,天津天津,1,1\n" + "13752459383,00210OW,天津天津,1,1\n" + "13820621183,00210OW,天津天津,1,1\n" + "15122253062,00210OW,天津天津,1,1\n" + "13920355437,00210OW,天津天津,1,1\n" + "15122392310,00210OW,天津天津,1,1\n" + "13702083335,00210OW,天津天津,1,1\n" + "18222268601,00210OW,天津天津,1,1\n" + "18202650783,00210OW,天津天津,1,1\n" + "13820926395,00210OW,天津天津,1,1\n" + "13821878917,00210OW,天津天津,1,1\n" + "15022453815,00210OW,天津天津,1,1\n" + "13512943863,00210OW,天津天津,1,1\n" + "18322455180,00210OW,天津天津,1,1\n" + "17822188739,00210OW,天津天津,1,1\n" + "13682154948,00210OW,天津天津,1,1\n" + "15122141404,00210OW,天津天津,1,1\n" + "13752420635,00210OW,天津天津,1,1\n" + "13821955413,00210OW,天津天津,1,1\n" + "18812613242,00210OW,天津天津,1,1\n" + "15022399256,00210OW,天津天津,1,1\n" + "18322784855,00210OW,天津天津,1,1\n" + "15822930115,00210OW,天津天津,1,1\n" + "13662095856,00210OW,天津天津,1,1\n" + "13652174591,00210OW,天津天津,1,1\n" + "18322272825,00210OW,天津天津,1,1\n" + "18322282582,00210OW,天津天津,1,1\n" + "13920306221,00210OW,天津天津,1,1\n" + "15222475784,00210OW,天津天津,1,1\n" + "13920383566,00210OW,天津天津,1,1\n" + "18822450145,00210OW,天津天津,1,1\n" + "15122538819,00210OW,天津天津,1,1\n" + "13502161335,00210OW,天津天津,1,1\n" + "13502131361,00210OW,天津天津,1,1\n" + "15922098205,00210OW,天津天津,1,1\n" + "13821014117,00210OW,天津天津,1,1\n" + "15222523433,00210OW,天津天津,1,1\n" + "17802271430,00210OW,天津天津,1,1\n" + "13920864945,00210OW,天津天津,1,1\n" + "18722008969,00210OW,天津天津,1,1\n" + "15902226931,00210OW,天津天津,1,1\n" + "18322217081,00210OW,天津天津,1,1\n" + "13821536697,00210OW,天津天津,1,1\n" + "15900205505,00210OW,天津天津,1,1\n" + "15822785109,00210OW,天津天津,1,1\n" + "13820720599,00210OW,天津天津,1,1\n" + "13642150087,00210OW,天津天津,1,1\n" + "13752228670,00210OW,天津天津,1,1\n" + "15122376758,00210OW,天津天津,1,1\n" + "15022001617,00210OW,天津天津,1,1\n" + "13920904824,00210OW,天津天津,1,1\n" + "15900378726,00210OW,天津天津,1,1\n" + "13820777114,00210OW,天津天津,1,1\n" + "15822385714,00210OW,天津天津,1,1\n" + "18222689166,00210OW,天津天津,1,1\n" + "13920039711,00210OW,天津天津,1,1\n" + "15922279591,00210OW,天津天津,1,1\n" + "15222747213,00210OW,天津天津,1,1\n" + "15822716009,00210OW,天津天津,1,1\n" + "13821514373,00210OW,天津天津,1,1\n" + "15022079970,00210OW,天津天津,1,1\n" + "13902048121,00210OW,天津天津,1,1\n" + "13662096940,00210OW,天津天津,1,1\n" + "13920634261,00210OW,天津天津,1,1\n" + "15822200141,00210OW,天津天津,1,1\n" + "15122829267,00210OW,天津天津,1,1\n" + "13512004802,00210OW,天津天津,1,1\n" + "13702167141,00210OW,天津天津,1,1\n" + "15022410553,00210OW,天津天津,1,1\n" + "13752781930,00210OW,天津天津,1,1\n" + "15900317901,00210OW,天津天津,1,1\n" + "17822135128,00210OW,天津天津,1,1\n" + "13682086278,00210OW,天津天津,1,1\n" + "15222811544,00210OW,天津天津,1,1\n" + "18722181816,00210OW,天津天津,1,1\n" + "13820674900,00210OW,天津天津,1,1\n" + "18222404462,00210OW,天津天津,1,1\n" + "13512088318,00210OW,天津天津,1,1\n" + "18722232459,00210OW,天津天津,1,1\n" + "15822468233,00210OW,天津天津,1,1\n" + "18322093451,00210OW,天津天津,1,1\n" + "15022255228,00210OW,天津天津,1,1\n" + "18322078844,00210OW,天津天津,1,1\n" + "18322792427,00210OW,天津天津,1,1\n" + "15222058461,00210OW,天津天津,1,1\n" + "18322507042,00210OW,天津天津,1,1\n" + "18322716053,00210OW,天津天津,1,1\n" + "13920857969,00210OW,天津天津,1,1\n" + "15822444639,00210OW,天津天津,1,1\n" + "15902270120,00210OW,天津天津,1,1\n" + "13502175832,00210OW,天津天津,1,1\n" + "18722052734,00210OW,天津天津,1,1\n" + "13920041733,00210OW,天津天津,1,1\n" + "15822926995,00210OW,天津天津,1,1\n" + "15022175314,00210OW,天津天津,1,1\n" + "15222869076,00210OW,天津天津,1,1\n" + "15122914587,00210OW,天津天津,1,1\n" + "13752116528,00210OW,天津天津,1,1\n" + "15900313270,00210OW,天津天津,1,1\n" + "15222599177,00210OW,天津天津,1,1\n" + "15822972915,00210OW,天津天津,1,1\n" + "15122148929,00210OW,天津天津,1,1\n" + "15122337824,00210OW,天津天津,1,1\n" + "18322069810,00210OW,天津天津,1,1\n" + "15122740078,00210OW,天津天津,1,1\n" + "15222457569,00210OW,天津天津,1,1\n" + "15122438258,00210OW,天津天津,1,1\n" + "15022031169,00210OW,天津天津,1,1\n" + "13920915600,00210OW,天津天津,1,1\n" + "15022613501,00210OW,天津天津,1,1\n" + "18322608057,00210OW,天津天津,1,1\n" + "15900243093,00210OW,天津天津,1,1\n" + "15822844579,00210OW,天津天津,1,1\n" + "15002257011,00210OW,天津天津,1,1\n" + "15822037930,00210OW,天津天津,1,1\n" + "15222704071,00210OW,天津天津,1,1\n" + "13752325969,00210OW,天津天津,1,1\n" + "15222094970,00210OW,天津天津,1,1\n" + "15822920065,00210OW,天津天津,1,1\n" + "13672034825,00210OW,天津天津,1,1\n" + "13512900757,00210OW,天津天津,1,1\n" + "15822564977,00210OW,天津天津,1,1\n" + "13702156753,00210OW,天津天津,1,1\n" + "13662024520,00210OW,天津天津,1,1\n" + "15122561040,00210OW,天津天津,1,1\n" + "13516267235,00210OW,天津天津,1,1\n" + "15222498247,00210OW,天津天津,1,1\n" + "18822331028,00210OW,天津天津,1,1\n" + "13516298884,00210OW,天津天津,1,1\n" + "15022273547,00210OW,天津天津,1,1\n" + "13612195327,00210OW,天津天津,1,1\n" + "15822624864,00210OW,天津天津,1,1\n" + "15222190717,00210OW,天津天津,1,1\n" + "13702034294,00210OW,天津天津,1,1\n" + "15900383905,00210OW,天津天津,1,1\n" + "19523416539,00210OW,天津天津,1,1\n" + "18892253324,00210OW,天津天津,1,1\n" + "13642109001,00210OW,天津天津,1,1\n" + "18322331343,00210OW,天津天津,1,1\n" + "15822634638,00210OW,天津天津,1,1\n" + "13502053594,00210OW,天津天津,1,1\n" + "13512840472,00210OW,天津天津,1,1\n" + "13821207330,00210OW,天津天津,1,1\n" + "15222574911,00210OW,天津天津,1,1\n" + "13920975148,00210OW,天津天津,1,1\n" + "15922109746,00210OW,天津天津,1,1\n" + "18702277982,00210OW,天津天津,1,1\n" + "13803014045,00210OW,天津天津,1,1\n" + "13752039911,00210OW,天津天津,1,1\n" + "18722239403,00210OW,天津天津,1,1\n" + "15122929022,00210OW,天津天津,1,1\n" + "13512260601,00210OW,天津天津,1,1\n" + "18822123104,00210OW,天津天津,1,1\n" + "15222325196,00210OW,天津天津,1,1\n" + "18822201713,00210OW,天津天津,1,1\n" + "15222356683,00210OW,天津天津,1,1\n" + "15900364272,00210OW,天津天津,1,1\n" + "17822152771,00210OW,天津天津,1,1\n" + "18822127464,00210OW,天津天津,1,1\n" + "15902262317,00210OW,天津天津,1,1\n" + "13920843568,00210OW,天津天津,1,1\n" + "13820369331,00210OW,天津天津,1,1\n" + "13920800679,00210OW,天津天津,1,1\n" + "15822104221,00210OW,天津天津,1,1\n" + "15122781697,00210OW,天津天津,1,1\n" + "18222044432,00210OW,天津天津,1,1\n" + "15022657323,00210OW,天津天津,1,1\n" + "15222423763,00210OW,天津天津,1,1\n" + "13602071885,00210OW,天津天津,1,1\n" + "18322336580,00210OW,天津天津,1,1\n" + "15822952945,00210OW,天津天津,1,1\n" + "15222649037,00210OW,天津天津,1,1\n" + "15822359052,00210OW,天津天津,1,1\n" + "13602047075,00210OW,天津天津,1,1\n" + "15122726189,00210OW,天津天津,1,1\n" + "13622019740,00210OW,天津天津,1,1\n" + "13821631367,00210OW,天津天津,1,1\n" + "13512029252,00210OW,天津天津,1,1\n" + "18822431625,00210OW,天津天津,1,1\n" + "15222061836,00210OW,天津天津,1,1\n" + "18322084871,00210OW,天津天津,1,1\n" + "13821186094,00210OW,天津天津,1,1\n" + "13821429220,00210OW,天津天津,1,1\n" + "13821933398,00210OW,天津天津,1,1\n" + "15122825836,00210OW,天津天津,1,1\n" + "15002223278,00210OW,天津天津,1,1\n" + "13821103256,00210OW,天津天津,1,1\n" + "13612040665,00210OW,天津天津,1,1\n" + "15122247219,00210OW,天津天津,1,1\n" + "13821208623,00210OW,天津天津,1,1\n" + "13672050704,00210OW,天津天津,1,1\n" + "13622153819,00210OW,天津天津,1,1\n" + "15802242855,00210OW,天津天津,1,1\n" + "18322176406,00210OW,天津天津,1,1\n" + "13820951501,00210OW,天津天津,1,1\n" + "13820564874,00210OW,天津天津,1,1\n" + "15822404406,00210OW,天津天津,1,1\n" + "18322780963,00210OW,天津天津,1,1\n" + "18322657895,00210OW,天津天津,1,1\n" + "13920064786,00210OW,天津天津,1,1\n" + "15022031680,00210OW,天津天津,1,1\n" + "15222452269,00210OW,天津天津,1,1\n" + "15002287094,00210OW,天津天津,1,1\n" + "15922041430,00210OW,天津天津,1,1\n" + "15922090574,00210OW,天津天津,1,1\n" + "15902244532,00210OW,天津天津,1,1\n" + "18822522264,00210OW,天津天津,1,1\n" + "15222706279,00210OW,天津天津,1,1\n" + "13652093653,00210OW,天津天津,1,1\n" + "15822341239,00210OW,天津天津,1,1\n" + "13652070202,00210OW,天津天津,1,1\n" + "13820949162,00210OW,天津天津,1,1\n" + "15222404500,00210OW,天津天津,1,1\n" + "13752329773,00210OW,天津天津,1,1\n" + "13920516183,00210OW,天津天津,1,1\n" + "15222749463,00210OW,天津天津,1,1\n" + "18722361271,00210OW,天津天津,1,1\n" + "15022371876,00210OW,天津天津,1,1\n" + "13502011467,00210OW,天津天津,1,1\n" + "13920548009,00210OW,天津天津,1,1\n" + "15002273633,00210OW,天津天津,1,1\n" + "13622026351,00210OW,天津天津,1,1\n" + "15122510027,00210OW,天津天津,1,1\n" + "13642022306,00210OW,天津天津,1,1\n" + "15822435023,00210OW,天津天津,1,1\n" + "15022417127,00210OW,天津天津,1,1\n" + "15122327761,00210OW,天津天津,1,1\n" + "13920223364,00210OW,天津天津,1,1\n" + "18322484651,00210OW,天津天津,1,1\n" + "15122997923,00210OW,天津天津,1,1\n" + "13820742183,00210OW,天津天津,1,1\n" + "13752482711,00210OW,天津天津,1,1\n" + "13821123152,00210OW,天津天津,1,1\n" + "18322634381,00210OW,天津天津,1,1\n" + "18322785190,00210OW,天津天津,1,1\n" + "13820177924,00210OW,天津天津,1,1\n" + "15022644636,00210OW,天津天津,1,1\n" + "13602063459,00210OW,天津天津,1,1\n" + "13622057805,00210OW,天津天津,1,1\n" + "13752161085,00210OW,天津天津,1,1\n" + "18722477075,00210OW,天津天津,1,1\n" + "13672198499,00210OW,天津天津,1,1\n" + "13622135908,00210OW,天津天津,1,1\n" + "15122335612,00210OW,天津天津,1,1\n" + "13821154637,00210OW,天津天津,1,1\n" + "18892252856,00210OW,天津天津,1,1\n" + "18722342537,00210OW,天津天津,1,1\n" + "13752420581,00210OW,天津天津,1,1\n" + "15222136971,00210OW,天津天津,1,1\n" + "15122862968,00210OW,天津天津,1,1\n" + "15222855486,00210OW,天津天津,1,1\n" + "18722112795,00210OW,天津天津,1,1\n" + "13652065887,00210OW,天津天津,1,1\n" + "13516290255,00210OW,天津天津,1,1\n" + "13920547063,00210OW,天津天津,1,1\n" + "13752113176,00210OW,天津天津,1,1\n" + "18322662812,00210OW,天津天津,1,1\n" + "15122731326,00210OW,天津天津,1,1\n" + "15122319038,00210OW,天津天津,1,1\n" + "13642067283,00210OW,天津天津,1,1\n" + "15022101778,00210OW,天津天津,1,1\n" + "15022130803,00210OW,天津天津,1,1\n" + "13820590106,00210OW,天津天津,1,1\n" + "13512986896,00210OW,天津天津,1,1\n" + "15122108472,00210OW,天津天津,1,1\n" + "15802265132,00210OW,天津天津,1,1\n" + "15122076648,00210OW,天津天津,1,1\n" + "15102237618,00210OW,天津天津,1,1\n" + "18202592715,00210OC,天津天津,1,1\n" + "13920263113,00210OC,天津天津,1,1\n" + "18222924947,00210OC,天津天津,1,1\n" + "15822039918,00210OC,天津天津,1,1\n" + "15022717035,00210OC,天津天津,1,1\n" + "13820980892,00210OC,天津天津,1,1\n" + "18222533501,00210OC,天津天津,1,1\n" + "13602106176,00210OC,天津天津,1,1\n" + "15022421138,00210OC,天津天津,1,1\n" + "18222883019,00210OC,天津天津,1,1\n" + "15822572827,00210OC,天津天津,1,1\n" + "15022366799,00210OC,天津天津,1,1\n" + "13820195922,00210OC,天津天津,1,1\n" + "13602011166,00210OC,天津天津,1,1\n" + "13821172945,00210OC,天津天津,1,1\n" + "15022352195,00210OC,天津天津,1,1\n" + "17822276346,00210OC,天津天津,1,1\n" + "18222755082,00210OC,天津天津,1,1\n" + "13920881046,00210OC,天津天津,1,1\n" + "18202678216,00210OC,天津天津,1,1\n" + "13672084817,00210OC,天津天津,1,1\n" + "15122485097,00210OC,天津天津,1,1\n" + "18322459310,00210OC,天津天津,1,1\n" + "15022238689,00210OC,天津天津,1,1\n" + "15922275219,00210OC,天津天津,1,1\n" + "13682043655,00210OC,天津天津,1,1\n" + "18722631708,00210OC,天津天津,1,1\n" + "18202219760,00210OC,天津天津,1,1\n" + "13682100872,00210OC,天津天津,1,1\n" + "15822440805,00210OC,天津天津,1,1\n" + "13821113545,00210OC,天津天津,1,1\n" + "13820495894,00210OC,天津天津,1,1\n" + "13821892986,00210OC,天津天津,1,1\n" + "15122065073,00210OC,天津天津,1,1\n" + "15022579006,00210OC,天津天津,1,1\n" + "18822642419,00210OC,天津天津,1,1\n" + "15822955478,00210OC,天津天津,1,1\n" + "15222718393,00210OC,天津天津,1,1\n" + "18202500901,00210OC,天津天津,1,1\n" + "18722216356,00210OC,天津天津,1,1\n" + "13752425050,00210OC,天津天津,1,1\n" + "13652172446,00210OC,天津天津,1,1\n" + "15122455436,00210OC,天津天津,1,1\n" + "15122733153,00210OC,天津天津,1,1\n" + "18722138607,00210OC,天津天津,1,1\n" + "17822206458,00210OC,天津天津,1,1\n" + "13682163139,00210OC,天津天津,1,1\n" + "13920456532,00210OC,天津天津,1,1\n" + "15022500683,00210OC,天津天津,1,1\n" + "15922057899,00210OC,天津天津,1,1\n" + "18822699200,00210OC,天津天津,1,1\n" + "15022260302,00210OC,天津天津,1,1\n" + "15822821412,00210OC,天津天津,1,1\n" + "18892209417,00210OC,天津天津,1,1\n" + "13642038504,00210OC,天津天津,1,1\n" + "13752432647,00210OC,天津天津,1,1\n" + "13821931748,00210OC,天津天津,1,1\n" + "13920251380,00210OC,天津天津,1,1\n" + "15222519283,00210OC,天津天津,1,1\n" + "13821010545,00210OC,天津天津,1,1\n" + "13820606524,00210OC,天津天津,1,1\n" + "13662128450,00210OC,天津天津,1,1\n" + "13642047551,00210OC,天津天津,1,1\n" + "15822165023,00210OC,天津天津,1,1\n" + "15822082193,00210OC,天津天津,1,1\n" + "15822830390,00210OC,天津天津,1,1\n" + "13920305853,00210OC,天津天津,1,1\n" + "13821773839,00210OC,天津天津,1,1\n" + "13516119302,00210OC,天津天津,1,1\n" + "18822577729,00210OC,天津天津,1,1\n" + "15900306878,00210OC,天津天津,1,1\n" + "15122832914,00210OC,天津天津,1,1\n" + "13512220257,00210OC,天津天津,1,1\n" + "18222119011,00210OC,天津天津,1,1\n" + "18722518855,00210OC,天津天津,1,1\n" + "18722129248,00210OC,天津天津,1,1\n" + "13920153310,00210OC,天津天津,1,1\n" + "13512421035,00210OC,天津天津,1,1\n" + "13752055720,00210OC,天津天津,1,1\n" + "13821897568,00210OC,天津天津,1,1\n" + "15122270348,00210OC,天津天津,1,1\n" + "13702048059,00210OC,天津天津,1,1\n" + "13672039208,00210OC,天津天津,1,1\n" + "15122398383,00210OC,天津天津,1,1\n" + "13502032460,00210OC,天津天津,1,1\n" + "18202617235,00210OC,天津天津,1,1\n" + "13820338102,00210OC,天津天津,1,1\n" + "15822895908,00210OC,天津天津,1,1\n" + "15202282509,00210OC,天津天津,1,1\n" + "18722448729,00210OC,天津天津,1,1\n" + "13682023959,00210OC,天津天津,1,1\n" + "13662049680,00210OC,天津天津,1,1\n" + "13672037465,00210OC,天津天津,1,1\n" + "13512220813,00210OC,天津天津,1,1\n" + "13920942186,00210OC,天津天津,1,1\n" + "18222418878,00210OC,天津天津,1,1\n" + "18722002046,00210OC,天津天津,1,1\n" + "13702009906,00210OC,天津天津,1,1\n" + "15900381239,00210OC,天津天津,1,1\n" + "18812783012,00210OC,天津天津,1,1\n" + "15822596058,00210OC,天津天津,1,1\n" + "13752533880,00210OC,天津天津,1,1\n" + "13821818067,00210OC,天津天津,1,1\n" + "13512901828,00210OC,天津天津,1,1\n" + "15222444866,00210OC,天津天津,1,1\n" + "13820185839,00210OC,天津天津,1,1\n" + "13682012589,00210OC,天津天津,1,1\n" + "15822340047,00210OC,天津天津,1,1\n" + "13502180333,00210OC,天津天津,1,1\n" + "15822021210,00210OC,天津天津,1,1\n" + "15122543309,00210OC,天津天津,1,1\n" + "13702108681,00210OC,天津天津,1,1\n" + "18322009372,00210OC,天津天津,1,1\n" + "18722022907,00210OC,天津天津,1,1\n" + "13512214800,00210OC,天津天津,1,1\n" + "15222800473,00210OC,天津天津,1,1";
    //    String lineNumbers = "";
    //    final String[] split = lineNumbers.split("\\r?\\n");
    //    log.info("北京天津视频彩铃包月和功能退订,数量：{}",split.length);
    //    //log.info("北京天津视频彩铃包月和功能查询,数量：{}",split.length);
    //    Arrays.stream(split).forEach(line->{
    //          try {
    //              final String[] strings = line.split(",");
    //              String mobile = strings[0];
    //              String channelCode = strings[1];
    //              miguApiService.vrbtUnsubscribe(mobile,channelCode);
    //              //TimeUnit.MILLISECONDS.sleep(50L);
    //              //退功能
    //              miguApiService.vrbtCancel(mobile,"00210OC");
    //
    //              //final VrbtCombinResult vrbtCombinResult = miguApiService.vrbtCombinQuery(mobile, channelCode);
    //              //if (vrbtCombinResult.isMonth()){
    //              //    log.info("北京天津视频彩铃包月和功能查询,有订购关系：{}", line);
    //              //}
    //              //TimeUnit.MILLISECONDS.sleep(100L);
    //
    //          } catch (Exception e) {
    //              e.printStackTrace();
    //          }
    //      });
    //}

    /**
     * 北京天津音频彩铃包月批量退订
     */
    @Test
    public void cancelCrbtBeijinTianjin(){
        final String CH = "002107Y";
        String lineNumbers = "00210BY,金立软件商店白金版,18710256242,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18811215468,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901070626,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910958900,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201521937,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811237225,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201307491,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18701191931,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18301192704,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15101601516,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18410022140,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911079390,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310607730,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210881538,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15210115405,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201541383,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810018552,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15701649947,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15710091537,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801092607,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901497109,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810922118,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310866983,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901213170,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810201835,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010436370,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810377591,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18410739196,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911029626,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801630797,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810911607,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810429500,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801620596,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010320987,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910958274,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201252669,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810601196,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15210911709,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18801416267,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15011303574,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15718899588,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901539345,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801666577,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010562805,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18701014508,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901487938,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811186283,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310468177,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810866676,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811192458,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210277810,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310911241,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801111572,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201256518,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901213524,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210265004,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15727390037,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18811460344,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910231940,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18701286532,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18211085839,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201348035,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810767720,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910988063,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15701683646,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810973186,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15210488837,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810561045,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201207030,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310152142,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811110216,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18710197306,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15711153262,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18301356690,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810723980,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010134179,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901395623,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210240039,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18301076365,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18710167983,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18701207305,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811041546,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810409121,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910976086,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911104567,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18811457629,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310972908,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901267892,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18813062017,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15110041406,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15711002008,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18410056653,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911149264,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210003934,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201335626,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811188745,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15110201665,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810325220,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010406132,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010992622,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201493806,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15101115330,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15011166982,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210344270,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811361361,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15210030359,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201081303,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010857350,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911020999,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18211131340,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910430885,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15011479321,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15210352891,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210194506,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901220117,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15001288174,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910305735,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201321102,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801098859,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811341259,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810316244,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810318674,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811074378,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811364875,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901579198,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18710204471,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810369897,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811553363,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15116904948,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810133624,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18710148635,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15701200558,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901264682,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15711353779,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801471537,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18311244457,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18710139008,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18710101408,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911100567,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811027020,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910200106,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210623404,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201495170,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010662571,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010278171,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18810979123,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910298003,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801158200,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18401482701,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15110290363,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810670269,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810130097,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15910550745,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210169707,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18401208320,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15711286870,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201567163,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210614879,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15701372253,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201104936,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18211016014,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310069532,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810474142,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010125129,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15001270611,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18811317725,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210348914,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18311398230,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210721042,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15801592336,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201430843,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911178527,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210530080,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15911050372,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010849500,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901069173,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201663492,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15010186295,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201530760,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210084516,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15710038916,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15001373070,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15811541571,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15810246585,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18201384209,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210863787,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18811459765,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15201699972,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310768420,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18301050890,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310089518,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310328236,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18310075719,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18311211760,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901081902,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18210916953,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15210360032,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15901057537,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,15711193179,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,18301114993,北京北京,金立彩铃\n" + "00210BY,金立软件商店白金版,13821238880,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15102257853,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15822409561,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15002242568,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13821784098,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15822272625,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13920169808,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13516119711,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15900286729,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,18722329758,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13752439544,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15822247299,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13920564658,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13672078656,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15022356145,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13622183360,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13920382757,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15022131967,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13820941078,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13821449006,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13821124181,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13820904617,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15002256734,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13821408658,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15822639703,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15900387914,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13612012455,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13502133832,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13672147216,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15022453923,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13512457694,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13821399802,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13920092403,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13642131742,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,18722614606,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15902268785,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13920035974,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13820105019,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13821367961,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13622038364,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,18322704286,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15122562969,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13752204100,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,18322333079,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15222218836,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,15222884820,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13920009817,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13752682067,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,18322593404,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13516159566,天津天津,金立彩铃\n" + "00210K7,铃听之声-铂金版,13672148015,天津天津,金立彩铃\n" + "1060000,,15011113772,北京北京,金立彩铃\n" + "1060000,,15718867780,北京北京,金立彩铃\n" + "1060000,,18800099755,北京北京,金立彩铃";
        final String[] split = lineNumbers.split("\\r?\\n");
        log.info("北京天津音频彩铃包月和功能退订,数量：{}",split.length);
        for (int i = 0; i < split.length; i++) {
            String line = split[i];
            log.info("北京天津音频彩铃包月和功能退订=>{},当前退订第{}个",line,i+1);
            try {
                final String[] strings = line.split(",");
                String mobile = strings[2];
                final RemoteResult queryResult1 = miguApiService.crbtMonthQuery(mobile, CH, null);
                if("0".equals(queryResult1.getStatus())){
                    log.info("北京天津音频彩铃包月和功能退订=>{},退订结果:{}",line,"用户未包月");
                    continue;
                }
                //退包月
                final RemoteResult cancelResult1 = miguApiService.crbtMonthCancel(mobile, CH);
                //TimeUnit.MILLISECONDS.sleep(50L);
                if(!cancelResult1.isOK()){
                    log.info("北京天津音频彩铃包月和功能退订=>{},退订结果:{}",line,"退订包月失败");
                    continue;
                }
                final RemoteResult queryResult2 = miguApiService.crbtMonthQuery(mobile, CH, null);
                if("0".equals(queryResult2.getStatus())){
                    log.info("北京天津音频彩铃包月和功能退订=>{},退订结果:{}",line,"退订包月成功");
                    continue;
                }
                //再次退包月,预留间隔时间2秒
                TimeUnit.SECONDS.sleep(2L);
                final RemoteResult cancelResult2 = miguApiService.crbtMonthCancel(mobile, CH);
                //TimeUnit.MILLISECONDS.sleep(50L);
                if(!cancelResult2.isOK()){
                    log.info("北京天津音频彩铃包月和功能退订=>{},退订结果:{}",line,"退订包月失败");
                    continue;
                }
                TimeUnit.SECONDS.sleep(2L);
                final RemoteResult queryResult3 = miguApiService.crbtMonthQuery(mobile, CH, null);
                if("0".equals(queryResult3.getStatus())){
                    log.info("北京天津音频彩铃包月和功能退订=>{},退订结果:{}",line,"退订包月成功");
                    continue;
                }
                log.info("北京天津音频彩铃包月和功能退订=>{},退订结果:{}",line,"退订包月失败");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 跑老孙的渠道包存量
     * SELECT price,COUNT(*) FROM cms_subscribe
     * WHERE isp = '1' AND STATUS = 1 AND channel IN ('00210OC','00210OW') AND sub_channel IN ('A7pTA1','A7pMR1','A7pMR2') AND create_time > '2021-07-01' AND create_time < '2021-10-01'
     * GROUP BY price;
     */
    //@Test
    //public void verifyVrbtSunZong(){
    //    List<Subscribe> list = subscribeService.lambdaQuery()
    //            .eq(Subscribe::getIsp, "1")
    //            .eq(Subscribe::getStatus, 1)
    //            .ne(Subscribe::getPrice,0)  //只跑还有包月的
    //            .in(Subscribe::getChannel, "00210OC","00210OW")
    //            .in(Subscribe::getSubChannel, "A7pTA1","A7pMR1", "A7pMR2")
    //            .gt(Subscribe::getCreateTime, "2021-07-01")
    //            .lt(Subscribe::getCreateTime, "2021-10-01").list();
    //
    //    System.out.println(list.size());
    //
    //    list.forEach(subscribe -> {
    //        Integer status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), subscribe.getChannel(), false);
    //        Subscribe sub = new Subscribe();
    //        sub.setId(subscribe.getId());
    //        sub.setPrice(status);
    //        subscribeService.updateSubscribeDbAndEs(sub);
    //    });
    //}

    @Test
    public void fixSubscribeStatus(){
        
        String fileName = "D:\\tmp_files\\互娱25元业务被误改为失败的.txt";
        //read file into stream, try-with-resources
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(id -> {
                subscribeService.updateStatusResultByIdAnd2DbAndEs(id, SUBSCRIBE_STATUS_SUCCESS, "修复为成功");
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void junboUnsubscribeCallback(){

        String fileName = "D:\\tmp_files\\junbo_osvrbt17_0718-0731_补发.txt";
        //read file into stream, try-with-resources
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(id -> {
                final Subscribe subscribe = subscribeService.getById(id);
                outsideCallbackService.unsubscribeNotifyAsync(subscribe);
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void stpCallback() {
        //outsideCallbackService.stpCallback("13438828200",IdWorker.get32UUID(), new Date(),"test.com","测试");


        String scyd_wdyyb = wandaSichuanYidongProperties.getChannelCidMap().get("SCYD_WDYYB");
        String scyd_gyqy = wandaSichuanYidongProperties.getChannelCidMap().get("SCYD_GYQY");
        System.out.println(scyd_gyqy);
        System.out.println(scyd_wdyyb);

        List<Subscribe> list = subscribeService.lambdaQuery().eq(Subscribe::getMobile, "***********")
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).gt(Subscribe::getCreateTime, LocalDate.now().minusMonths(6L).atTime(LocalTime.MIN)).list();


//        String fileName = "D:\\tmp_files\\stp-scyd_0822-0823_补发.txt";
//        //read file into stream, try-with-resources
//        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
//            stream.forEach(id -> {
//                final Subscribe subscribe = subscribeService.getById(id);
//                outsideCallbackService.stpCallback(subscribe.getMobile(),subscribe.getIspOrderNo(), subscribe.getOpenTime(),subscribe.getReferer(),"补发");
//            });
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }


    @Test
    public void lbtxTlvrbtCallback(){

        String fileName = "D:\\手机号.txt";
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(mobile -> {
                Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).in(Subscribe::getChannel, BIZ_CHANNEL_SCYD_TLVRBTLXB, BIZ_CHANNEL_SCYD_TLVRBTLXB_PLUS).eq(Subscribe::getStatus, 1).one();
                try {
                    outsideCallbackService.lbtxScydTlvrbtlxCallbackAsync(subscribe.getMobile(), subscribe.getChannel(), subscribe.getId(), subscribe.getIspOrderNo(), subscribe.getStatus(), org.jeecg.common.util.DateUtils.datetimeFormat.get().format(subscribe.getCreateTime()), subscribe.getResult());
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
//        subscribeService.lambdaQuery().in(Subscribe::getChannel, BIZ_CHANNEL_SCYD_TLVRBTLXB, BIZ_CHANNEL_SCYD_TLVRBTLXB_PLUS).eq(Subscribe::getStatus, 1).list();
//        outsideCallbackService.lbtxScydTlvrbtlxCallback();
    }


    @Test
    public void lbtxCallback(){
        String fileName = "D:\\手机号.txt";
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(mobile -> {
                Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).in(Subscribe::getChannel, BIZ_CHANNEL_SCYD_YLCWB_30).eq(Subscribe::getStatus, 1).one();
                try {
                    outsideCallbackService.lbtxScydCallbackAsync(subscribe.getMobile(), subscribe.getChannel(), subscribe.getId(), subscribe.getIspOrderNo(), subscribe.getStatus(), org.jeecg.common.util.DateUtils.datetimeFormat.get().format(subscribe.getCreateTime()), subscribe.getResult());
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void jxydYinlianMemberCallback(){
        outsideCallbackService.jxydYinlianMemberCallbackAsync("13400000000", "9999999999999999999", "测试产品",1, "开通结果回调测试", "123456", new Date());
    }



    @Test
    public void mobileToMd5(){

        String fileName = "D:\\tmp_files\\new.log";
        //read file into stream, try-with-resources
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(mobile -> {
                log.info(DigestUtils.md5DigestAsHex(mobile.getBytes(StandardCharsets.UTF_8)));
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }



    @Test
    public void notifyData(){

        String mobiles = "13703930163\n" +
                "13801280676\n" +
                "13521180805\n" +
                "18811663458\n";


        List<Subscribe> list = subscribeService.lambdaQuery().eq(Subscribe::getBizType, BIZ_TYPE_SHOUJIZIXUN_YD).eq(Subscribe::getTuiaFeedbackStatus, "1").ne(Subscribe::getStatus, 1).in(Subscribe::getMobile, mobiles.split("\n")).list();
        list.forEach(subscribe -> {
            Subscribe udp = new Subscribe();
            udp.setId(subscribe.getId());
            udp.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            udp.setResult("订购成功");
            subscribeService.updateSubscribeDbAndEs(udp);
        });
    }

    /**
     * 骏伯四川移动重复数据清理
     */
    @Test
    public void junboScydDataDupClear() throws IOException {

        //String fileName = "D:\\tmp_files\\junbo_sichuan_20031114.txt";
        //List<String> ispOrderNumberList = Files.lines(Paths.get(fileName)).collect(Collectors.toList());
        //Integer count = subscribeService.lambdaQuery().select(Subscribe::getId).between(Subscribe::getCreateTime,
        //        "2023-11-14 00:00:00", "2023-11-14 23:59:59").in(Subscribe::getIspOrderNo, ispOrderNumberList).count();
        //System.out.println("count = " + count);
        //
        //fileName = "D:\\tmp_files\\junbo_sichuan_20031117.txt";
        //ispOrderNumberList = Files.lines(Paths.get(fileName)).collect(Collectors.toList());
        //count = subscribeService.lambdaQuery().select(Subscribe::getId).between(Subscribe::getCreateTime,
        //        "2023-11-17 00:00:00", "2023-11-17 23:59:59").in(Subscribe::getIspOrderNo, ispOrderNumberList).count();
        //System.out.println("count = " + count);

        //String fileName = "D:\\tmp_files\\junbo_sichuan_20031114.txt";
        //String fileName = "D:\\tmp_files\\junbo_sichuan_20031117.txt";
        String fileName = "D:\\tmp_files\\junbo_sichuan_200310_remove.txt";
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(ispOrderNumber -> {
                final Subscribe one = subscribeService.lambdaQuery().select(Subscribe::getId)
                        .eq(Subscribe::getIspOrderNo, ispOrderNumber)
                        //.between(Subscribe::getCreateTime, "2023-11-14 00:00:00", "2023-11-14 23:59:59")
                        //.between(Subscribe::getCreateTime, "2023-11-17 00:00:00", "2023-11-17 23:59:59")
                        .between(Subscribe::getCreateTime, "2023-10-01 00:00:00", "2023-10-31 23:59:59")
                        .one();
                final String id = one.getId();
                log.info("四川移动骏伯数据清理:"+fileName+"||"+ispOrderNumber+"||"+id);
                subscribeService.removeById(id);
                esSubscribeRepository.deleteById(id);
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
        //清除测试数据
        //subscribeService.lambdaQuery()
        //        .select(Subscribe::getId)
        //        //.eq(Subscribe::getIspOrderNo, testIspOrderNumber)
        //        .between(Subscribe::getCreateTime,"2023-10-01 00:00:00", "2023-10-31 23:59:59")
        //        .eq(Subscribe::getResult,"测试")
        //        .list()
        //        .stream()
        //        .forEach(subscribe -> {
        //            String id  = subscribe.getId();
        //            subscribeService.removeById(id);
        //            esSubscribeRepository.deleteById(id);
        //        });

    }

    @Test
    public void lhHjxCallback() {
        List<Subscribe> list = subscribeService.lambdaQuery().
                gt(Subscribe::getCreateTime, "2024-06-07 14:43:33").
                eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).
                eq(Subscribe::getChannel, BIZ_CHANNEL_LH_HJX).list();

//        2024-06-06 16:56:16

        list.forEach(subscribe -> {
            outsideCallbackService.lhHjxCallbackAsync(subscribe.getId());
        });
    }

    @Test
    public void zclyCallback() {
//        Subscribe subscribe = subscribeService.lambdaQuery().
//                eq(Subscribe::getMobile, "13688260106").
//                eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).
//                eq(Subscribe::getChannel, BIZ_CHANNEL_SCYD_BYZHBC).one();
//
////        2024-06-06 16:56:16
//            outsideCallbackService.zclyCallback(subscribe.getId());

        boolean a = SpringContextUtils.getBean(ZhongcaileyangService.class).queryOrder(BIZ_CHANNEL_SCYD_BYZHBC, "13688260106");
        boolean b = SpringContextUtils.getBean(ZhongcaileyangService.class).queryOrder(BIZ_CHANNEL_SCYD_BYZHBC, "***********");
        System.out.println(a);
        System.out.println(b);
    }

    @Test
    public void outsideCallback() {
//        List<Subscribe> list = subscribeService.lambdaQuery().
//            eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).
//            eq(Subscribe::getChannel, "HENYD_YR_5GXTH").
//            gt(Subscribe::getCreateTime, "2024-08-23 00:00:00").
//            orderByAsc(Subscribe::getCreateTime).list();
//        list.forEach(subscribe -> {
//            outsideCallbackService.zclyCallback(subscribe.getId());
//        });

        List<Subscribe> list = subscribeService.lambdaQuery().
            eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).
            eq(Subscribe::getChannel, "SCYD_BYZHBC_LLB").
            gt(Subscribe::getCreateTime, "2024-08-16 00:00:00").
            lt(Subscribe::getCreateTime, "2024-08-26 17:07:00").
            orderByAsc(Subscribe::getCreateTime).list();
        list.forEach(subscribe -> {
            outsideCallbackService.zclyCallbackAsync(subscribe.getId());
        });


    }

    @Test
    public void caixunCallback() {
        List<Subscribe> subscribes = subscribeService.lambdaQuery()
                .gt(Subscribe::getCreateTime, "2024-06-12 00:00:00")
                .eq(Subscribe::getChannel, "CX_DJSJ")
                .eq(Subscribe::getStatus, 1)
                .notIn(Subscribe::getId, "1803807775894601730").list();
        subscribes.forEach(subscribe -> {
            outsideCallbackService.caixunCallbackAsync(subscribe.getId());
        });
    }

    @Test
    public void validationLimit() throws JsonProcessingException {
//        boolean a = subscribeService.validationLimit("HYQY_JUNBO", "四川", "ABG1dTXN3");
//        System.out.println(a);

        Subscribe subscribe = new Subscribe();
//        subscribe.setChannel("002115U");
//        subscribe.setSubChannel("123");
//        subscribe.setMobile("***********");
//        subscribe.setProvince("江苏");
//        Result result = SpringContextUtils.getBean(SxhCommonServiceImpl.class).filerCheck(subscribe);
//        System.out.println(result.isOK());
//
//
//        subscribe.setChannel("SCYD_BYZHB");
//        subscribe.setSubChannel("123");
//        subscribe.setMobile("***********");
//        subscribe.setProvince("四川");
//        result = SpringContextUtils.getBean(SichuanYidongCommonServiceImpl.class).filerCheck(subscribe);
//        System.out.println(result.isOK());

        subscribe.setChannel("00210QL");
        subscribe.setSubChannel("123");
        subscribe.setMobile("***********");
        subscribe.setProvince("四川");
        Result result = SpringContextUtils.getBean(InnerUnionMemberService.class).callFilterCheck(subscribe, "wangyiyunCommonService");
        System.out.println(result.isOK());

//        subscribe.setChannel("READ_DJXP_JUNJUE");
//        subscribe.setSubChannel("ABG1dTXN3");
//        subscribe.setMobile("***********");
//        subscribe.setProvince("广东");
//        SpringContextUtils.getBean(InnerUnionMemberService.class).callFilterCheck(subscribe,"readCommonService");


//        boolean a = SpringContextUtils.getBean(CpmbCommonServiceImpl.class).filerCheck("***********","014X04C","","四川");
//        System.out.println(a);
//
//        boolean b = SpringContextUtils.getBean(SxhCommonServiceImpl.class).filerCheck("***********","002115U","123","四川");
//        System.out.println(b);
//        boolean c = SpringContextUtils.getBean(CpmbCommonServiceImpl.class).filerCheck("***********","002110A","ABG1dTXN3","四川");
//        System.out.println(c);
//
//        boolean d = SpringContextUtils.getBean(CpmbCommonServiceImpl.class).filerCheck("***********","002115U","ABG1dTXN3","江苏");
//        System.out.println(d);


//        provinceBusinessChannelConfigService.getByChannel("HYQY_JUNBO");
//        provinceBusinessChannelConfigService.getByChannel("HYQY_JUNBO");
//
//        boolean a = subscribeService.validationLimit("HYQY_JUNBO", "四川", "ABG1dTXN3");
//        boolean b = subscribeService.validationLimit("HYQY_JUNBO", "四川", "ABG1dTXN3");
//        boolean c = subscribeService.validationLimit("HYQY_JUNBO", "四川", "ABG1dTXN3");


    }

}