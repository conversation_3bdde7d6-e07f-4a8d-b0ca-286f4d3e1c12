package org.jeecg;

import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static com.eleven.cms.remote.MiguApiService.BIZ_SXH_CHANNEL_CODE_TEST;

/**
 * Author: <EMAIL>
 * Date: 2024/4/28 15:06
 * Desc: 视宣号接口测试
 */
@Slf4j
public class MiguShixuanhaoInterfaceTest {
    

    //public static final String GET_CIRCLELIST_URL = "http://***************:80/evrms-thirdparty/qz/thirdparty/sxh/circle/getCircleList";
    //public static final String GET_CIRCLELIST_URL = "http://***************:31010/evrms-thirdparty/qz/thirdparty/sxh/circle/getCircleList";
    public static final String GET_CIRCLELIST_URL = "http://**************:31010/evrms-thirdparty/qz/thirdparty/sxh/circle/getCircleList";


    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    private static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    @SneakyThrows
    private static String signWithSHA256(Map<String, String> sysParam, String busiParam, String key)  {
        Map<String, String> map = new HashMap<String, String>(sysParam);
        map.put("content", busiParam);
        String signStr = map.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(entry -> entry.getKey()+entry.getValue()).collect(Collectors.joining("",key,key));
        System.out.println("signStr=" +signStr);
        return new HmacUtils(HmacAlgorithms.HMAC_SHA_256, Hex.decodeHex(key.toLowerCase())).hmacHex(signStr).toUpperCase();
    }


    public static void main(String[] args) {
        //String data = mapper.createObjectNode()
        //        .put("circleId", "11111")
        //        .put("setType", "0000")
        //        .set("ringId", mapper.createArrayNode().add("12341234124"))
        //        .toPrettyString();
        //System.out.println(data);


        shxGetCircleId("13943619500","002115U","f14cd7dd56674a309587443ff6d7b600");
        shxGetCircleId("18482155682", "00211A0","d1b90291760049a19f20ec3638879a73");


    }

    private static void shxGetCircleId(String mobile,String channelCode,String signatureSecretKey) {
        final OkHttpClient client = OkHttpClientUtils.getSingletonInstance().newBuilder()
                .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                .addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        String seq = DateUtil.formatFullTime(LocalDateTime.now());
        String digest = DigestUtils.md5DigestAsHex((seq+signatureSecretKey).getBytes(StandardCharsets.UTF_8));
        //final Request request = new Request.Builder().url("https://httpbin.org/ip").build();
        final Request request = new Request.Builder().url(GET_CIRCLELIST_URL)
                .addHeader("seq",seq)
                .addHeader("client",channelCode)
                .addHeader("digest",digest)
                .post(RequestBody.create(JSON, mapper.createObjectNode()
                        .put("circleOwnerMobile", mobile)
                        //.put("memberMobile", "mobile")
                        .put("status", "01")
                        .toString()))
                .build();
        log.info("请求接口:{}",request);
        try (Response response = client.newCall(request).execute()) {
            //{"access_token":"5d80577b-4208-40bb-bfa7-6d2ae3b4be05","token_type":"bearer","expires_in":84538}
            String content  = response.body().string();
            log.info("接口响应:{}",content);
        } catch (IOException e) {
           e.printStackTrace();
        }
    }
}
