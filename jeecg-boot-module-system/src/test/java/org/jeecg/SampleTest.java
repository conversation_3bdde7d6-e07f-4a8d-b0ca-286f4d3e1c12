package org.jeecg;

import java.security.KeyPair;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Locale;

import javax.annotation.Resource;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.AsymmetricAlgorithm;
import com.eleven.cms.config.OutsideProperties;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.demo.mock.MockController;
import org.jeecg.modules.demo.test.entity.JeecgDemo;
import org.jeecg.modules.demo.test.mapper.JeecgDemoMapper;
import org.jeecg.modules.demo.test.service.IJeecgDemoService;
import org.jeecg.modules.system.service.ISysDataLogService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SampleTest {

	@Resource
	private JeecgDemoMapper jeecgDemoMapper;
	@Resource
	private IJeecgDemoService jeecgDemoService;
	@Resource
	private ISysDataLogService sysDataLogService;
	@Resource
	private MockController mock;
    @Autowired
    OutsideProperties outsideProperties;
    @Autowired
    ISysBaseAPI sysBaseAPI;


	@Test
	public void testSelect() {
		System.out.println(("----- selectAll method test ------"));
		List<JeecgDemo> userList = jeecgDemoMapper.selectList(null);
		Assert.assertEquals(5, userList.size());
		userList.forEach(System.out::println);
	}

	@Test
	public void testXmlSql() {
		System.out.println(("----- selectAll method test ------"));
		List<JeecgDemo> userList = jeecgDemoMapper.getDemoByName("Sandy12");
		userList.forEach(System.out::println);
	}

	/**
	 * 测试事务
	 */
	@Test
	public void testTran() {
		jeecgDemoService.testTran();
	}
	
	//author:lvdandan-----date：20190315---for:添加数据日志测试----
	/**
	 * 测试数据日志添加
	 */
	@Test
	public void testDataLogSave() {
		System.out.println(("----- datalog test ------"));
		String tableName = "jeecg_demo";
		String dataId = "4028ef81550c1a7901550c1cd6e70001";
		String dataContent = mock.sysDataLogJson();
		sysDataLogService.addDataLog(tableName, dataId, dataContent);
	}
	//author:lvdandan-----date：20190315---for:添加数据日志测试----

    @Test
    public void isJunboOutsideChannel(){
//        System.out.println(outsideProperties.isJunboOutsideChannel("OSVRBT04"));
//        System.out.println(outsideProperties.isJunboOutsideChannel("OSVRBT14"));
    }

    @Test
    public void sendSysAnnouncement(){
        sysBaseAPI.sendSysAnnouncement("admin","admin","测试企业彩铃","xxx添加用户");
    }

    public static void main(String[] args) {
        /**
         * 生成 RSA 公私钥
         *
         * @return
         */

            System.out.println("生成公私钥===============");
            //秘钥位数 建议1024 （1024 2048 都支持）
            KeyPair keyPair = SecureUtil.generateKeyPair(AsymmetricAlgorithm.RSA.getValue(), 1024);
            System.out.println("==base64 start==");
            System.out.println("私钥（base64）：" + Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded()));
            System.out.println("公钥（base64）：" + Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded()));
            System.out.println("==base64 end==");
        
    }
}
