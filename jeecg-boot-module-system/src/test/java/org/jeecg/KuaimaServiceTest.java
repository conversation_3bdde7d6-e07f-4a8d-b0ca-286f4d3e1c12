package org.jeecg;

import com.eleven.cms.config.KuaimaProperties;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.ISmsValidateService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class KuaimaServiceTest {

    @Autowired
    KuaimaService kuaimaService;
    @Autowired
    KuaimaProperties kuaimaProperties;
    @Autowired
    OutsideCallbackService outsideCallbackService;
    @Autowired
    KuaimaGansuService kuaimaGansuService;
    @Autowired
    GuangxiYidongService guangxiYidongService;
    @Autowired
    GansuYidongService gansuYidongService;

    ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Autowired
    YuxunHunanDianxinService yuxunHunanDianxinService;
    @Autowired
    ISmsValidateService smsValidateService;



    @Test
    public void getSms() throws Exception {

        boolean result = smsValidateService.create("17323059958", MiguApiService.CHAOXUAN_CHANNEL_CODE);


//        yuxunHunanDianxinService.accpectOrder("19976960046","910899910252");

//        guangxiYidongService.getSms("13699402402");
    }

    @Test
    public void smsCode() throws Exception {
        gansuYidongService.smsCode("15117010133","1234","f381b4032a4d4deba876a437ef6e6cf7");
//        guangxiYidongService.smsCode("13893311672","390639");

//        if (kuaimaResult.getData() != null) {
//            System.out.println(kuaimaResult.getData().getOrderNo());
//        }

    }

    @Test
    public void callback() throws Exception {

        outsideCallbackService.kuaimaSrqyCallbackAsync("13699402402","test","SCYD_SRQY","测试开通失败",new Date(),0);
//        try {
//            BufferedReader br = new BufferedReader(new FileReader(new File("D:\\kuaima.txt")));//构造一个BufferedReader类来读取文件
//            String lineText = null;
//            while ((lineText = br.readLine()) != null) {//使用readLine方法，一次读一行
//                Pattern pattern = Pattern.compile("\\{\"([a-zA-Z_]+)\":(.+)}");
//                Matcher matcher = pattern.matcher(lineText);
//                String fullResult = "";
//                if (matcher.find()) {
//                    fullResult = matcher.group(0);
//                }
//                pattern = Pattern.compile("orderNo:(.{28})");
//                matcher = pattern.matcher(lineText);
//                if (matcher.find()) {
//                    System.out.println(matcher.group(0));
//                }
//                boolean result = outsideCallbackService.kuaimaCallback("13699402402", fullResult, kuaimaProperties.getCallbackUrl());
//            }
//            br.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        String fullResult = "{\"result\":\"2840b0311ce3e7768e22027b732ed6b52e9546476082077946116e884000fc0fcac3830943e3d87267f766efd6a9d1a06b79aec612a573f0283acd4bd82d11727e37e00d3961b8daaf498f4f842737eb7f25a1a81ddeef9c281df89b6b60979c05bb7f50000083ca9b74c35824d29f8088f3a7b817c1ebd5863d6a706d78b3228183e1ce20b694a3d10c81aea6fd55e5df212105d247e70a3e5c6b4f1ecbb171a2de203c84825e1eaf4f9a8f90e9096692f56341760f07b64a2fd8ef2549ea88\",\"detailmsg\":\"\",\"res_code\":\"0000\",\"res_msg\":\"成功\"}";
//        outsideCallbackService.kuaimaCallback("13688391222", fullResult, kuaimaProperties.getCallbackUrl());
//        fullResult = "{\"result\":\"2840b0311ce3e7768e22027b732ed6b52e9546476082077946116e884000fc0fcac3830943e3d87267f766efd6a9d1a06b79aec612a573f0283acd4bd82d11727e37e00d3961b8daaf498f4f842737eb26e6ff0a7c4ca3ffcc6cafff0eb4ba1a54dd5913d23a3a67bbecc2b6ceed190188f3a7b817c1ebd5863d6a706d78b32254ef50a907496583d83dbb1c9f681b761f167da90631bda6b431c79cbef8b104a2de203c84825e1eaf4f9a8f90e90966bf4d211ccd61a55b464b2c96d6855684\",\"detailmsg\":\"\",\"res_code\":\"0000\",\"res_msg\":\"成功\"}";
//        outsideCallbackService.kuaimaCallback("13990222109", fullResult, kuaimaProperties.getCallbackUrl());

    }

    public static void main(String[] args) {



//        String text = "2022-06-14 09:27:33.840 [http-nio-9527-exec-146] INFO  ApiController:2321 - 四川移动订单开通结果数据,mobile:13699402402,orderNo:1000039920220614092642060767,status:0,result:尊敬的用户，您目前已经订购过该产品，请勿重复订购，谢谢。,fullResult:{\"result\":\"05962232201d812a7233181445c2535cd2b54d6d9d5b734ae32326a578a319929804c518cf2db6d72164e7b8c26244bb0dcffdbea4322d40818833cd16086311\",\"detailmsg\":\"\",\"res_code\":\"E13013509\",\"res_msg\":\"尊敬的用户，您目前已经订购过该产品，请勿重复订购，谢谢。\"}";
//
//        Pattern pattern = Pattern.compile("\\{\"([a-zA-Z_]+)\":(.+)}");
//        Matcher matcher = pattern.matcher(text);
//        if (matcher.find()) {
//            System.out.println(matcher.group(0));
//        }
//        pattern = Pattern.compile("orderNo:(.{28})");
//        matcher = pattern.matcher(text);
//        if (matcher.find()) {
//            System.out.println(matcher.group(0));
//        }
    }

    //@Test
    //public void updateSubscribe() {
    //    subscribeService.lambdaQuery().select(Subscribe::getId,Subscribe::getMobile).isNull(Subscribe::getProvince).list()
    //                    .forEach(sub->{
    //                        try {
    //                            final MobileRegionResult result = mobileRegionService.query(sub.getMobile());
    //                            if(result!=null){
    //                                subscribeService.lambdaUpdate().eq(Subscribe::getId,sub.getId())
    //                                .set(Subscribe::getProvince,result.getProvince())
    //                                .set(Subscribe::getCity,result.getCity())
    //                                .update();
    //                            }
    //                        } catch (Exception e) {
    //                            e.printStackTrace();
    //                        }
    //                    });
    //}

}