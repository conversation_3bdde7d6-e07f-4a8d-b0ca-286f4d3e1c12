package org.jeecg;

import com.eleven.cms.entity.SubChannel;
import com.eleven.cms.remote.GuangdongJujieService;
import com.eleven.cms.remote.HebeiYidongService;
import com.eleven.cms.service.ISubChannelService;
import com.eleven.cms.vo.GuangdongJujieResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * @author: cai lei
 * @create: 2024-07-04 11:19
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class GuangdongJujieServiceTest {

    @Autowired
    GuangdongJujieService guangdongJujieService;
    @Autowired
    ISubChannelService subChannelService;

    public static final String ua = "Mozilla/5.0 (Linux; Android 10; FGD-AL00 Build/HUAWEIFGD-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.88 Mobile Safari/537.36 open_news open_news_u_s/6144/com.barter.confid";

    @Test
    public void queryGoods() {
        guangdongJujieService.queryGoods("17323059958", "3");
        guangdongJujieService.queryGoods("13699402402", "2");
    }

    @Test
    public void getSms() {
        SubChannel channel = subChannelService.findSubChannel("A82cJL1");
        String goodsId = "1002016_101899_7889";
        GuangdongJujieResult result = guangdongJujieService.getSms("17323059958", goodsId, channel.getAdPlatform(), "com.test", "0.0.0.0", ua);
        System.out.println(result);
    }

    @Test
    public void order() {
        SubChannel channel = subChannelService.findSubChannel("A82cJL1");
        String goodsId = "1002016_101899_7889";
        String ispOrderNo = "1234567890";
        GuangdongJujieResult result = guangdongJujieService.order("17323059958", goodsId, ispOrderNo, "123456", channel.getAdPlatform(), "com.test", "0.0.0.0", ua);
        System.out.println(result);
    }

}
