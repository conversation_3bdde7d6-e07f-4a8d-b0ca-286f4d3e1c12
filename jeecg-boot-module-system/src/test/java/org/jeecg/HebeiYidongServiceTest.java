package org.jeecg;

import com.eleven.cms.remote.HebeiYidongService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * @author: cai lei
 * @create: 2024-07-04 11:19
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class HebeiYidongServiceTest {

    @Autowired
    HebeiYidongService hebeiYidongService;

    @Test
    public void getToken() {
        hebeiYidongService.getToken();
    }

    @Test
    public void queryUserInfo() {
//        String region = hebeiYidongService.queryRegion("15130957171");
//        System.out.println("region = " + region);
    }

    @Test
    public void getSms() {
//        hebeiYidongService.getSms("13933090330", "HBYD_LLB");
//        String code = "123456";
//        hebeiYidongService.getSms("18633091027", "HBYD_LLB");
        hebeiYidongService.getSms("18633091027", "HBYD_LLB");
//        hebeiYidongService.smsCode("18633091027", "123456", "HBYD_LLB");

    }

}
