package org.jeecg;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.SneakyThrows;
import okhttp3.*;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2024/4/28 15:06
 * Desc:Todo
 */
public class XizangYidongInterfaceTest {

    //APP_ID: 911004591
    //APP_KEY: 465cdfa89145b36d2c2cf6d9540a439c
    //METHOD:
    //CRM_TO_CMOS_SEND_RANDOM_PWD_001
    //CRM_ORD_VAS_OFFER_OPER_001
    //CRM_TO_CMOS_AUTH_RANDOM_PWD_001
    //CHANNEL_ID: 911004591
    //OPERATOR_ID: 90359307
    //DES_KEY: FSNYO
    public static final String APP_ID = "911004591";
    public static final String APP_KEY = "465cdfa89145b36d2c2cf6d9540a439c";
    public static final String CRM_TO_CMOS_SEND_RANDOM_PWD_001 = "CRM_TO_CMOS_SEND_RANDOM_PWD_001";
    public static final String CRM_ORD_VAS_OFFER_OPER_001 = "CRM_ORD_VAS_OFFER_OPER_001";
    public static final String CRM_TO_CMOS_AUTH_RANDOM_PWD_001 = "CRM_TO_CMOS_AUTH_RANDOM_PWD_001";
    public static final String CHANNEL_ID = "911004591";
    public static final String OPERATOR_ID = "90359307";
    //public static final String DES_KEY = "FSNYO"+'\0'+'\0'+'\0';
    public static final String DES_KEY = "FSNYO";

    public static final String OPPF_URL = "http://************:18000/oppf";
    public static final String AOPOAUTH_URL = "http://************:18001/aopoauth/oauth/token";

    public static final okhttp3.MediaType JSON = okhttp3.MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    public static final String SMS_CONTENT = "【验证密码】${VERIFY_CODE}，尊敬的客户，您好！您将订购炫视专属包-流量优惠版，资费15元/月，订购后立即生效，如不主动退订则长期有效。产品生效期间可享受视频彩铃订阅-炫视专属6元包、视频彩铃全站0元包、10GB全国通用流量（其中5GB合约流量仅限当月使用，不可结转至次月、不可转赠、不可共享）。活动合约期12个月，合约到期后5GB合约通用流量自动失效，其他业务按产品资费15元/月续订并续费。合约期内销户、携号转网、退订活动按照解约赔付标准解约。验证码有效期${MINUTE}分钟。若非本人操作请勿泄露，任何索取行为均可能涉嫌诈骗。【中国移动】";

    @SneakyThrows
    private static String signWithSHA256Old(Map<String, String> sysParam, String busiParam, String key)  {
        Map<String, String> map = new HashMap<String, String>(sysParam);
        map.put("content", busiParam);
        String[] keys = (String[])map.keySet().toArray((Object[])new String[map.size()]);
        Arrays.sort((Object[])keys);
        StringBuilder buf = new StringBuilder(200);
        buf.append(key);
        byte b;
        int i;
        String[] arrayOfString1;
        for (i = (arrayOfString1 = keys).length, b = 0; b < i; ) {
            String k = arrayOfString1[b];
            if (!"sign".equalsIgnoreCase(k))
                buf.append(k).append(map.get(k));
            b++;
        }
        buf.append(key);
        System.out.println("signStr=" + buf.toString());

        return new HmacUtils(HmacAlgorithms.HMAC_SHA_256, Hex.decodeHex(key.toLowerCase())).hmacHex(buf.toString()).toUpperCase();
    }

    @SneakyThrows
    private static String signWithSHA256(Map<String, String> sysParam, String busiParam, String key)  {
        Map<String, String> map = new HashMap<String, String>(sysParam);
        map.put("content", busiParam);
        String signStr = map.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(entry -> entry.getKey()+entry.getValue()).collect(Collectors.joining("",key,key));
        System.out.println("signStr=" +signStr);
        return new HmacUtils(HmacAlgorithms.HMAC_SHA_256, Hex.decodeHex(key.toLowerCase())).hmacHex(signStr).toUpperCase();
    }


    public static void main(String[] args) throws InvalidKeyException, NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, IllegalBlockSizeException, BadPaddingException {
        String src = "1234";
        //////KEY转换
        //DESKeySpec desKeySpec = new DESKeySpec(DES_KEY.getBytes(StandardCharsets.UTF_8));
        //SecretKeyFactory factory = SecretKeyFactory.getInstance("DES");
        //Key convertSecretKey = factory.generateSecret(desKeySpec);
        ////加密
        //Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
        //cipher.init(Cipher.ENCRYPT_MODE, convertSecretKey);
        //byte[] result = cipher.doFinal(src.getBytes());
        //System.out.println("bc des encrypt : " + Hex.encodeHexString(result));

        SecureRandom random = new SecureRandom();
        DESKeySpec desKey = new DESKeySpec( StringUtils.rightPad(DES_KEY, 8, '\0').getBytes());
        //创建一个密匙工厂，然后用它把DESKeySpec转换成
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey securekey = keyFactory.generateSecret(desKey);
        //Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance("DES");
        //用密匙初始化Cipher对象,ENCRYPT_MODE用于将 Cipher 初始化为加密模式的常量
        cipher.init(Cipher.ENCRYPT_MODE, securekey, random);
        //现在，获取数据并加密
        //正式执行加密操作
        final byte[] result = cipher.doFinal(src.getBytes());//按单部分操作加密或解密数据，或者结束一个多部分操作
        System.out.println("bc des encrypt : " + Hex.encodeHexString(result));


        SymmetricCrypto  des =new SymmetricCrypto(SymmetricAlgorithm.DES, StringUtils.rightPad(DES_KEY, 8, '\0').getBytes());
        //加密为16进制，解密为原字符串
        String encryptHex = des.encryptHex(src);
        System.out.println("hutool encryptHex = " + encryptHex);


        final OkHttpClient client = OkHttpClientUtils.getSingletonInstance().newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();;
        ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        final HttpUrl tokenUrl = HttpUrl.parse(AOPOAUTH_URL)
                .newBuilder()
                .addQueryParameter("app_id", APP_ID)
                .addQueryParameter("app_key", APP_KEY)
                .addQueryParameter("grant_type", "client_credentials")
                .build();
        String token = "";
        System.out.println("tokenUrl = " + tokenUrl.newBuilder());
        try (Response response = client.newCall(new okhttp3.Request.Builder().url(tokenUrl).build()).execute()) {
            //{"access_token":"5d80577b-4208-40bb-bfa7-6d2ae3b4be05","token_type":"bearer","expires_in":84538}
            String content  = response.body().string();
            System.out.println("content = " + content);
            token = mapper.readTree(content).get("access_token").asText();
        } catch (IOException e) {
           e.printStackTrace();
        }
        System.out.println("token = " + token);

        String userMobile = "13889030028";
        String busiSerial = IdWorker.get32UUID();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        // 能力开放平台系统参数(除sign参数外)
        Map<String, String> sysParam = new HashMap<>();
        sysParam.put("method", CRM_TO_CMOS_SEND_RANDOM_PWD_001);
        sysParam.put("format", "json");
        sysParam.put("appId", APP_ID);
        sysParam.put("operId", OPERATOR_ID);
        sysParam.put("version", "1.0");
        sysParam.put("accessToken", token);
        sysParam.put("timestamp", timestamp);
        sysParam.put("busiSerial", busiSerial);

        final ObjectNode dataNode = mapper.createObjectNode()
        .putPOJO("input",
                        mapper.createObjectNode()
                                .put("userMobile",userMobile)
                                .put("smsContent",SMS_CONTENT))
        .putPOJO("pubInfo",
                mapper.createObjectNode()
                        .put("countyCode","1001")
                        .put("opId",OPERATOR_ID)
                        .put("orgId",CHANNEL_ID)
                        .put("regionCode","891"));


        // 如果业务参数需要加密，则这里需是加密后的值
        String busiParam = dataNode.toString();

        // 能力开放平台支持两种签名算法，即HmacSHA256与RSAWithMD5；
        // 在开发者视图的应用创建页面，分别对应签名算法下拉选项中的SHA与RSA
        // 考虑到性能问题，现在大多使用HmacSHA256签名算法，下面以HmacSHA256算法为例调用api生成签名sign

        // HmacSHA256签名生成示例
        String appKey = "edf3def8681986d00cf19e654e2f9150";
        String signOld = signWithSHA256Old(sysParam, busiParam, appKey);
        System.out.println("signOld = " + signOld);
        String sign = signWithSHA256(sysParam, busiParam, appKey);
        System.out.println("sign by HmacSHA256 is:" + sign);

        sysParam.put("sign", sign);
        
        //http://************:18000/oppf?accessToken=b3c76896-061c-4a24-aaec-ebf8acdc5074&appId=911004602&busiSerial=bb9a19b623de47568e069885db6c5d13&format=json
        // &method=CRM_TO_CMOS_SEND_RANDOM_PWD_001&operId=90360105&timestamp=20240428103358&version=1.0&sign=28E9436827A399584A8DC7BF9069BF49A5BB3C934ACC273201881027C8C208D7
        final HttpUrl.Builder builder = HttpUrl.parse(OPPF_URL).newBuilder();
        sysParam.forEach(builder::addQueryParameter);

        RequestBody body = RequestBody.create(JSON, busiParam);
        Request request = new Request.Builder().url(builder.build())
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            //{"access_token":"5d80577b-4208-40bb-bfa7-6d2ae3b4be05","token_type":"bearer","expires_in":84538}
            String content  = response.body().string();
            System.out.println("content = " + content);
        } catch (IOException e) {
            e.printStackTrace();
        }


    }
}
