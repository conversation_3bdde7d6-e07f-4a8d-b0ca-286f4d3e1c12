package org.jeecg;

import com.eleven.qycl.entity.QyclIndustryTemplate;
import com.eleven.qycl.entity.QyclRingVideo;
import com.eleven.qycl.service.IQyclIndustryTemplateService;
import com.eleven.qycl.service.IQyclRingVideoService;
import com.eleven.qycl.service.TtsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Author: <EMAIL>
 * Date: 2022/11/11 17:23
 * Desc: 企业彩铃行业模板测试
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class QyclIndustryTemplateServiceImplTest {
    @Autowired
    IQyclIndustryTemplateService qyclIndustryTemplateService;
    @Autowired
    TtsService ttsService;

    @Autowired
    IQyclRingVideoService qyclRingVideoService;

    /**
     * 生成所有行业基于"某某有限公司"的样例
     */
    @Test
    public void genTemplateVoice() {
        qyclIndustryTemplateService.list().forEach(item -> {
            final String ringTxt = item.getRingTxt().replace("[companyTitle]","某某有限公司");
            final String path = ttsService.textToVoiceFile(ringTxt);
            qyclIndustryTemplateService.lambdaUpdate().eq(QyclIndustryTemplate::getId,item.getId()).set(QyclIndustryTemplate::getFilePath,path).update();
        });
    }

    @Test
    public void test() {
        QyclRingVideo qyclRingVideo = qyclRingVideoService.randomQyclGrRingVideo();
        System.out.println(qyclRingVideo.getRingId());
        qyclRingVideo = qyclRingVideoService.randomQyclGrRingVideo();
        System.out.println(qyclRingVideo.getRingId());

        qyclRingVideo = qyclRingVideoService.randomQyclGrRingVideo();
        System.out.println(qyclRingVideo.getRingId());

        qyclRingVideo = qyclRingVideoService.randomQyclGrRingVideo();
        System.out.println(qyclRingVideo.getRingId());

    }
}
