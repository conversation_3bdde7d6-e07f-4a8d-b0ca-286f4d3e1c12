package org.jeecg;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.impl.*;
import com.eleven.cms.vo.MobileRegionResult;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

/**
 * @author: cai lei
 * @create: 2022-08-12 15:33
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BizCommonServiceTest {


    @Test
    public void order() throws IOException {

        String mobile = "13438828200";
        String source = "http://www.baidu.com";
        String app = "com.app.test";
        String ip = "**************";
        String ua = "Mozilla/5.0 (Linux; Android 13; PJB110 Build/TP1A.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/103.0.5060.129 Mobile Safari/537.36 open_news open_news_u_s/5908/com.mm.aweather.plus";

        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setSource(source);
        subscribe.setReferer(app);
        subscribe.setUserAgent(ua);
        subscribe.setIp(ip);
        subscribe.setIsp(MobileRegionResult.ISP_YIDONG);
        subscribe.setChannel("00211DI");

        Result result = SpringUtil.getBean(CpmbCommonNewServiceImpl.class).getSmsCode(subscribe);
        System.out.println(result);
        // result = SpringUtil.getBean(CpmbCommonNewServiceImpl.class).submitSmsCode(subscribe);
        //System.out.println(result);
//        mobile = "15226612829";
//        subscribe.setMobile(mobile);
//        result = SpringUtil.getBean(YouranHebeiYidongCommonServiceImpl.class).filerCheck(subscribe);
//        System.out.println(result);
//        mobile = "15226612829";
//        subscribe.setMobile(mobile);
//        result = SpringUtil.getBean(YouranHebeiYidongCommonServiceImpl.class).filerCheck(subscribe);
//        System.out.println(result);
//        mobile = "15933031696";
//        subscribe.setMobile(mobile);
//        result = SpringUtil.getBean(YouranHebeiYidongCommonServiceImpl.class).filerCheck(subscribe);
//        System.out.println(result);

        result = SpringUtil.getBean(CpmbCommonNewServiceImpl.class).submitSmsCode(subscribe);
        System.out.println(result);
        subscribe.setTransactionId(result.getResult().toString());
        String smsCode = "123456";
        subscribe.setSmsCode(smsCode);
//
//        result = SpringUtil.getBean(LiantongWydCrackCommonServiceImpl.class).submitSmsCode(subscribe);
//        System.out.println(result);

//        SpringUtil.getBean(VrbtCommonServiceImpl.class).getSmsCode(subscribe);
//        SpringUtil.getBean(VrbtCommonServiceImpl.class).getSmsCode(subscribe);
//        SpringUtil.getBean(VrbtCommonServiceImpl.class).getSmsCode(subscribe);


//        subscribe.setSmsCode("JXYD_DXLLB");

//        SpringUtil.getBean(LianbaoGansuCommonServiceImpl.class).submitSmsCode(subscribe);


//        stpJiangsuYidongService.getSms("18362544023","","http://www.baidu.com","com.app.test","**************");
//
    }
}
