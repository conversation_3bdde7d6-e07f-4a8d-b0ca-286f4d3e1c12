package org.jeecg;

import com.eleven.cms.service.IMiGuKuaiYouVRJingMengService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class VRJingMengServiceTest {
    @Autowired
    private IMiGuKuaiYouVRJingMengService vrJingMengService;


    @Test
    public void VRJingMengGetToken(){
        String mobile = "18500138792";
//        String channel="HY_10_XX";
        String channel="HY_25_CW";
        FebsResponse token=null;
        for(int i=0;i<1;i++){
            token=vrJingMengService.vrJingMengGetToken(mobile,channel);
            log.info("token:{}",token);
        }
        for(int i=0;i<1;i++){
            FebsResponse fee=vrJingMengService.vrJingMengFee(mobile,token.get("data").toString());
            log.info("fee:{}",fee);
        }
        for(int i=0;i<1;i++){
            FebsResponse user=vrJingMengService.vrJingMengGetUser(mobile,channel);
            log.info("user:{}",user);
        }
    }

    @Test
    public void orderQuery1(){

        String fileName = "D:\\UserData\\eleven\\Desktop\\VR_退订.txt";
        //read file into stream, try-with-resources
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(line -> {
                final String[] split = line.split("\\s");
                String mobile = split[0];
                String status = split[1];
                FebsResponse resp =vrJingMengService.vrJingMengGetUser(mobile, BizConstant.BIZ_CHANNEl_VR_JM_25);
                String validStatus = resp.isOK() ? "100" : "2";
                log.info("VR订购状态查询结果:{},{}",mobile,validStatus);
                if(!StringUtils.equals(status,validStatus)){
                    log.info("VR订购状态差异:{},{},{}",mobile,validStatus,status);
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(500L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void orderQuery2(){

        String fileName = "F:\\VR-2024-02-17.txt";
        //read file into stream, try-with-resources
        try (Stream<String> stream = Files.lines(Paths.get(fileName))) {
            stream.forEach(mobile -> {


                FebsResponse resp =vrJingMengService.vrJingMengGetUser(mobile, BizConstant.BIZ_CHANNEl_VR_JM_25);
                String validStatus = resp.isOK() ? "在订" : "退订";
                log.info("VR订购状态查询结果:{},{}",mobile,validStatus);
                try {
                    TimeUnit.MILLISECONDS.sleep(500L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void orderQuery3(){
        Arrays.asList(
                        "13576230668",
                        "13647090608",
                        "13763900743",
                        "13907954805"
                ).stream()
                .forEach(mobile -> {
                    FebsResponse resp = vrJingMengService.vrJingMengGetUser(mobile, BizConstant.BIZ_CHANNEl_VR_JM_25);
                    String validStatus = resp.isOK() ? "100" : "2";
                    log.info("VR订购状态查询结果:{},{}", mobile, validStatus);
                    try {
                        TimeUnit.MILLISECONDS.sleep(500L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                });

    }





}
