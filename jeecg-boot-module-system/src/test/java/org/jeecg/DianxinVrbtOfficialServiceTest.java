package org.jeecg;

import com.eleven.cms.remote.DianxinVrbtOfficialService;
import com.eleven.cms.vo.DianxinResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DianxinVrbtOfficialServiceTest {

    @Autowired
    DianxinVrbtOfficialService dianxinVrbtOfficialService;

    //@Test
    //public void queryPackageExist() {
    //    final boolean packageExist = dianxinVrbtService.queryPackageExist("18080928200");
    //    System.out.println("packageExist = " + packageExist);
    //}

    //@Test
    //public void unSubscribeByemp() {
    //    dianxinVrbtService.unSubscribeByemp("18080928200");
    //}

    @Test
    public void sendTemplateSms() {
        DianxinResult dianxinResult = dianxinVrbtOfficialService.sendTemplateSms("18080928200");
        System.out.println(dianxinResult);
    }

    @Test
    public void onKeyOrderPackage() {
        DianxinResult dianxinResult = dianxinVrbtOfficialService.onKeyOrderPackage("18080928200","5825");
        System.out.println(dianxinResult);
    }

    @Test
    public void matchValidCode() {
        DianxinResult dianxinResult = dianxinVrbtOfficialService.matchValidCode("17342680919","7598");
        System.out.println(dianxinResult);
    }

    @Test
    public void getUserInfo() {
        String tmpToken = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        DianxinResult dianxinResult = dianxinVrbtOfficialService.getUserInfo("17342680919",tmpToken);
        System.out.println(dianxinResult);
    }

    @Test
    public void queryPackageExist() {
        String authorization = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        boolean dianxinResult = dianxinVrbtOfficialService.queryPackageExist("17342680919",authorization);
        System.out.println(dianxinResult);
    }

    @Test
    public void orderBoxLoadIsmp() {
        String authorization = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        DianxinResult dianxinResult = dianxinVrbtOfficialService.orderBoxLoadIsmp("17342680919",authorization);
        System.out.println(dianxinResult);
    }

    @Test
    public void orderRingByPackage() {
        String authorization = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        DianxinResult dianxinResult = dianxinVrbtOfficialService.orderRingByPackage("17342680919",authorization,"910100081713");
        System.out.println(dianxinResult);
    }
}