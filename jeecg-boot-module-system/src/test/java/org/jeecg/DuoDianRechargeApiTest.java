package org.jeecg;

import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IDuoDianRechargeApiService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.DuoDianOrderQueryResult;
import com.eleven.cms.vo.DuoDianRechargeResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class DuoDianRechargeApiTest {
    @Autowired
    IDuoDianRechargeApiService duoDianRechargeApiService;

    @Autowired
    IJunboChargeLogService junboChargeLogService;

    @Test
    public void receiveCoupon(){
        String couponId = "4961";
        String mobile = "15915401656";
        String orderId = UUID.randomUUID().toString().replace("-", "");
        DuoDianRechargeResult duoDianRechargeResult=duoDianRechargeApiService.receiveCoupon(orderId,mobile,couponId);
        log.info("duoDianRechargeResult:{}",duoDianRechargeResult);

    }
    @Test
    public void queryReceiveStatus(){
        String couponId = "4961";
        String mobile = "15915401656";
        String orderId ="fa2eb4a62c3541dfa0d60d1c8492083b";
        DuoDianOrderQueryResult duoDianOrderQueryResult=duoDianRechargeApiService.queryReceiveStatus(orderId,mobile,couponId);
        log.info("duoDianOrderQueryResult:{}",duoDianOrderQueryResult);

    }


}

