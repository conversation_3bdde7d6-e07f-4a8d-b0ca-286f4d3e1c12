package org.jeecg;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.repository.EsSubscribeRepository;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.remote.SubscribeVerifyService;
import com.eleven.cms.service.ISubscribeService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jeecg.common.util.RedisUtil;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2020/3/22 16:48
 * Desc:
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
class OutsideCallbackServiceTest {
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;

    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private SubscribeVerifyService subscribeVerifyService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    OutsideCallbackService outsideCallbackService;
    @Autowired
    EsSubscribeRepository esSubscribeRepository;
    @Autowired
    RedisUtil redisUtil;


    @Test
    void batchCallback() {
        final List<Subscribe> subscribeList = subscribeService
                .lambdaQuery()
                .between(Subscribe::getCreateTime,
                        LocalDate.of(2024, 9, 1).atTime(LocalTime.MIN),
                        LocalDate.of(2024, 8, 8).atTime(LocalTime.MAX))
                .eq(Subscribe::getChannel, "JXYD_ADLD_YR")
                //.eq(Subscribe::getSubChannel,"A7pTA1")
                .eq(Subscribe::getStatus, 1).list();
        log.info("共查询到{}条数据", subscribeList.size());
        subscribeList
                .forEach(subscribe -> {
                    subscribe.setSubChannel("OSVRBT113");
                    //记得把回调切换成同步的再测试
                    outsideCallbackService.outsideCallbackAsync(subscribe, "开通成功");
                });
    }

    @Test
    void callback() {
//        final List<Subscribe> subscribeList = subscribeService
//                .lambdaQuery()
//                .eq(Subscribe::getId, "1836321761185624065")
//                //.eq(Subscribe::getSubChannel,"A7pTA1")
//                //.eq(Subscribe::getStatus, 1)
//                .list();
//        log.info("共查询到{}条数据", subscribeList.size());
//        subscribeList
//                .forEach(subscribe -> {
//                    //记得把回调切换成同步的再测试
//                    outsideCallbackService.outsideCallback(subscribe, "开通成功");
//                });

        Subscribe subscribe = subscribeService.getById("1704781543073189889");

        outsideCallbackService.outsideCallbackAsync(subscribe, "开通成功");
        outsideCallbackService.outsideCallbackAsync(subscribe, "开通成功");
        subscribe = subscribeService.getById("1704782380918968322");

        outsideCallbackService.outsideCallbackAsync(subscribe, "开通失败");
    }

    @Test
    void testMcjCallback() {
//        String mobile = "13408608881";
//        String smsCode = "1111";
//        Subscribe subscribe = new Subscribe();
//        subscribe.setMobile(mobile);
//        subscribe.setSmsCode(smsCode);
//        outsideCallbackService.mcjCallback(subscribe, "");

        final List<Subscribe> subscribeList = subscribeService
                .lambdaQuery()
                .eq(Subscribe::getId, "1845009958312644610")
                //.eq(Subscribe::getSubChannel,"A7pTA1")
                //.eq(Subscribe::getStatus, 1)
                .list();
        log.info("共查询到{}条数据", subscribeList.size());
        subscribeList
                .forEach(subscribe -> {
                    //记得把回调切换成同步的再测试
                    outsideCallbackService.mcjCallbackAsync(subscribe, "开通成功");
                });
    }

    @Test
    public void ztmCallbak() {
        outsideCallbackService.zmtCallbackAsync("1803322746890371073");
    }

    @Test
    public void aztCallbak() throws IOException {

        List<String> mobiles = FileUtils.readLines(new File("D:\\手机号.txt"), "utf-8");
//
        final List<Subscribe> subscribeList = subscribeService
            .lambdaQuery()
            .between(Subscribe::getCreateTime,
                LocalDate.of(2024, 10, 23).atTime(LocalTime.MIN),
                LocalDate.of(2024, 10, 31).atTime(LocalTime.of(10, 0, 0)))
            .eq(Subscribe::getChannel, "JXYD_MCAI_HS")
            .eq(Subscribe::getStatus, 1)
            .in(Subscribe::getMobile, mobiles).list();
        log.info("共查询到{}条数据", subscribeList.size());
        subscribeList.forEach(subscribe -> {
            //记得把回调切换成同步的再测试
            outsideCallbackService.mcAztCallbackAsync(subscribe);
        });


//        Subscribe subscribe = subscribeService.getById("1850867673673080834");
//        outsideCallbackService.mcAztCallback(subscribe);
    }

    public static void main(String[] args) throws IOException {
        List<String> mobiles = FileUtils.readLines(new File("D:\\手机号.txt"), "utf-8");
        mobiles.forEach(System.out::println);
    }
}