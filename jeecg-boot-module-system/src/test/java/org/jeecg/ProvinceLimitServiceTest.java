package org.jeecg;

import com.eleven.cms.entity.ProvinceLimit;
import com.eleven.cms.service.IProvinceLimitService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ProvinceLimitServiceTest {
    @Autowired
    IProvinceLimitService provinceLimitService;
    @Test
    public void selectByCode() {
        final ProvinceLimit provinceLimit = provinceLimitService.selectByCode("100");
        System.out.println("provinceLimit = " + provinceLimit);
    }

    @Test
    public void isAvailableUnicom() {
        final boolean availableUnicom = provinceLimitService.isAvailableUnicom("100");
        System.out.println("availableUnicom = " + availableUnicom);
        final boolean availableUnicom1 = provinceLimitService.isAvailableUnicom("100");
        System.out.println("availableUnicom1 = " + availableUnicom1);
    }
}
