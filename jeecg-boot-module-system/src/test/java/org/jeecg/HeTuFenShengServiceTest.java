package org.jeecg;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IHeTuFenShengService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/19 9:16
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class HeTuFenShengServiceTest {
    @Autowired
    IHeTuFenShengService heTuFenShengService;
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Test
    public void filerCheck(){
        IBizCommonService bizCommonService = SpringUtil.getBean("gansuMobileCommonService");
        String jsonData = "{\n" +
                "    \"id\": \"\",\n" +
                "    \"mobile\": \"13993560374\",\n" +
                "    \"channel\": \"GSYD_HS_DKJY\",\n" +
                "    \"smsCode\":\"\",\n" +
                "    \"ip\":\"**************\",\n" +
                "    \"_\": 1669018640734\n" +
                "}";
        Subscribe subscribe = null;
        try {
            subscribe = mapper.readValue(jsonData, Subscribe.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        subscribe.setIsp("1");
        Result<?> result=bizCommonService.filerCheck(subscribe);
        log.info("result:{}",result);

    }

    @Test
    public void sendSms(){
        IBizCommonService bizCommonService = SpringUtil.getBean("gansuMobileCommonService");
        String jsonData = "{\n" +
                "    \"id\": \"\",\n" +
                "    \"mobile\": \"13809308582\",\n" +
                "    \"channel\": \"GSYD_HS_DKJY\",\n" +
                "    \"smsCode\":\"\",\n" +
                "    \"ip\":\"**************\",\n" +
                "    \"_\": 1669018640734\n" +
                "}";
        Subscribe subscribe = null;
        try {
            subscribe = mapper.readValue(jsonData, Subscribe.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        subscribe.setIsp("1");
        Result<?> result=bizCommonService.getSmsCode(subscribe);
        log.info("result:{}",result);

    }
    @Test
    public void submitSmsCode(){



        IBizCommonService bizCommonService = SpringUtil.getBean("gansuMobileCommonService");
        String jsonData = "{\n" +
                "    \"id\": \"1826895817388335105\",\n" +
                "    \"mobile\": \"13809308582\",\n" +
                "    \"channel\": \"GSYD_HS_DKJY\",\n" +
                "    \"ispOrderNo\":\"1\",\n" +
                "    \"ip\":\"**************\",\n" +
                "    \"smsCode\":\"1234\",\n" +
                "    \"_\": 1669018640734\n" +
                "}";
        Subscribe subscribe = null;
        try {
            subscribe = mapper.readValue(jsonData, Subscribe.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        subscribe.setIsp("1");
        Result<?> result=bizCommonService.submitSmsCode(subscribe);
        log.info("result:{}",result);

    }
}
