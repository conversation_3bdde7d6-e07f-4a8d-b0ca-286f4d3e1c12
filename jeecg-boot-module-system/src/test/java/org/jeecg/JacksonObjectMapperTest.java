package org.jeecg;

import com.eleven.cms.entity.Subscribe;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;

/**
 * Author: <EMAIL>
 * Date: 2024/4/11 10:36
 * Desc:Jackson ObjectMapper 属性配置支持
 */
public class JacksonObjectMapperTest {
    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper()
        .setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY) //任何访问可见度字段
        .enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL) //只允许非final字段
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)//反序列化忽略JSON中没有的字段，防止反序列化对象的时候报找不到属性字段的异常
        .configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true)//允许使用未带引号的字段名
        .configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true) //允许属性名使用单引号
        .configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true)//允许属性名不带双引号""，比较简单，示例略
        .configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true)//允许JSON字符串包含非引号控制字符（值小于32的ASCII字符，包含制表符和换行符）
        .configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true)//允许反斜杠转义任何字符
        .configure(JsonParser.Feature.ALLOW_COMMENTS, true)//允许/* */或者//这种类型的注释出现。
        .configure(JsonParser.Feature.ALLOW_YAML_COMMENTS, true)//开启后将支持Yaml格式的的注释，也就是#形式的注释语法
        .configure(JsonParser.Feature.ALLOW_NUMERIC_LEADING_ZEROS, true)//允许像00001这样的“数字”出现（而不报错）
        .configure(JsonParser.Feature.ALLOW_LEADING_DECIMAL_POINT_FOR_NUMBERS, true)//允许小数点.打头，也就是说.1这种小数格式是否合法。默认是不合法的，需要开启此特征才能支持，例子就略了，基本同上。
        .configure(JsonParser.Feature.ALLOW_NON_NUMERIC_NUMBERS, true)//允许一些解析器识别一组“非数字”(如NaN)作为合法的浮点数值。
        .configure(JsonParser.Feature.ALLOW_MISSING_VALUES, true)//允许支持JSON数组中“缺失”值。怎么理解：数组中缺失了值表示两个逗号之间，啥都没有，形如这样[value1, , value3]
        .configure(JsonParser.Feature.ALLOW_TRAILING_COMMA, true);//是否允许最后一个多余的逗号（一定是最后一个）。这个特征是非常重要的，若开关打开，有如下效果：[true,true,]等价于[true, true];{"a": true,}等价于{"a": true}

        objectMapper.convertValue(new Subscribe(), HashMap.class);

    }
}
