package org.jeecg;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.remote.LemobaApiService;
import com.eleven.cms.vo.LemobaResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;

/**
 * 乐摩吧接口测试
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LemobaApiServiceTest {
    @Autowired
    LemobaApiService lemobaApiService;
    @Test
    public void buildUserCenterUrl() {
//        String uuid = "9693deb223175aa9d4c8a8ffcf52f6c8";
//        String userCenterUrl = lemobaApiService.buildUserCenterUrl(uuid, "17323059958", "00210QX","202112");
//        System.out.println("userCenterUrl = " + userCenterUrl);
    }

    @Test
    public void unSubscribe() {
        String uuid = "9693deb223175aa9d4c8a8ffcf52f6c8";
        LemobaResult lemobaResult = lemobaApiService.unSubscribe(uuid, "17323059958", "00210QX");
        System.out.println(lemobaResult.toString());
        System.out.println("result = " + lemobaResult.getCode());
    }
}
