package org.jeecg;

import com.eleven.cms.entity.AdSiteBusinessConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/4 12:20
 **/
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class AdSiteBusinessConfigServiceTest {
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    /**
     * 特定渠道号包月校验
     */
    @Test
    public void saveAdSite(){
        try {
            File channelFile=new File("C:\\Users\\<USER>\\Desktop\\渠道号.txt");
            if(channelFile.exists()){
                List<String> channelList = FileUtils.readLines(channelFile, StandardCharsets.UTF_8);
                if(!channelList.isEmpty()){
                    channelList.forEach(channel->{
                        File appFile=new File("C:\\Users\\<USER>\\Desktop\\app包名.txt");
                        if(appFile.exists()){
                            List<String> appList = null;
                            try {
                                appList = FileUtils.readLines(appFile, StandardCharsets.UTF_8);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            appList.forEach(app->{
                                boolean adSite=adSiteBusinessConfigService.isBlack(channel,app);
                                if(!adSite){
                                    AdSiteBusinessConfig adSiteBusinessConfig=new AdSiteBusinessConfig();
                                    adSiteBusinessConfig.setChannel(channel);
                                    adSiteBusinessConfig.setAdSite(app);
                                    adSiteBusinessConfig.setOsVrbtBlack(1);
                                    adSiteBusinessConfig.setRemark("手动添加");
                                    adSiteBusinessConfig.setCreateBy("admin");
                                    adSiteBusinessConfig.setCreateTime(new Date());
                                    adSiteBusinessConfigService.save(adSiteBusinessConfig);
                                }
                            });
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
