package org.jeecg; /**
 Maven引入：

 <dependency>
 <groupId>com.aliyun</groupId>
 <artifactId>aliyun-java-sdk-core</artifactId>
 <version>4.5.3</version>
 </dependency>
 <dependency>
 <groupId>com.aliyun</groupId>
 <artifactId>ice20201109</artifactId>
 <version>1.2.0</version>
 </dependency>
 <dependency>
 <groupId>com.aliyun</groupId>
 <artifactId>mts20140618</artifactId>
 <version>3.3.33</version>
 </dependency>
 <dependency>
 <groupId>com.alibaba</groupId>
 <artifactId>fastjson</artifactId>
 </dependency>
 */


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.ice20201109.Client;
import com.aliyun.ice20201109.models.*;
import com.aliyun.mts20140618.models.QueryIProductionJobRequest;
import com.aliyun.mts20140618.models.QueryIProductionJobResponse;
import com.aliyun.mts20140618.models.SubmitIProductionJobRequest;
import com.aliyun.mts20140618.models.SubmitIProductionJobResponse;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.google.common.io.CharStreams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Create by oushu
 * Date 2021/12/7 上午9:28   {"NotifyAddress":"ice-callback-****"}
 */
@Slf4j
public class AliMediaProduceTest {
    private final String notifyUrl = "http://crbt.cdyrjygs.com:9527/cms-vrbt/test/notifyLog";
    private final String iceCallbackMnsQueueName = "ice-callback-queue-job";
    private String accessKeyId;
    private String accessKeySecret;
    private OSS ossClient;
    private String bucket;
    private String regionId;
    private com.aliyun.mts20140618.Client mpsClient;
    private Client iceClient;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    public void initClient() throws Exception {

        //accessKeyId = "LTAI5tC75QZVysemu139RBpg";
        //accessKeySecret = "******************************";
        accessKeyId = "LTAI4G4srM2mS3vD5NfQKoXq";
        accessKeySecret = "******************************";
        bucket = "ims-media";
        regionId = "cn-beijing";
        ossClient = createOssClient();
        mpsClient = createMpsClient();
        iceClient = createIceClient();
    }

    public static void main(String[] args) throws Exception {


        AliMediaProduceTest aliMediaProduce = new AliMediaProduceTest();
        aliMediaProduce.initClient();
        aliMediaProduce.waitJobComplete("563a597368684994bf7c90a7b3b87c40");
//        ////batchProduceVideo.batchProduceVideo();
//        String templateId = "d7e3b047f3114dbaa96e4fb221145acb";
//        ////String templateId = "IceSys_VETemplate_s101039";
//        //System.out.println("templateId = " + templateId);
//        final String templateInfo = aliMediaProduce.fetchTemplateInfo(templateId);
//        System.out.println("templateInfo = " + templateInfo);
//        //////String clipsParam = "{\"Media4\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"Media3\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/2.jpg\",\"Media2\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/3.jpg\",\"Media1\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/11.jpg\",\"Media0\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/22.jpg\"}";
//        ////String clipsParam = "{\"pic1\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"pic2\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/2.jpg\",\"pic4\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/480x270.jpg\"}";
//        ////String clipsParam = "{\"Text4\":\"Text4\",\"Text3\":\"Text3\",\"Text2\":\"Text2\",\"Text1\":\"Text1\",\"Text0\":\"Text0\",\"Media3\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"Media2\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"Media1\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\"}";
//
//        String clipsParam = "{\"tex\":\"这是公司名字\"}";
//        //String clipsParam = "{}";
//        aliMediaProduce.mergeTemplate(templateId, clipsParam);
        ////aliMediaProduce.generateTemplateJsonFile();    -

        //File previewMp4File = new File("\\\\*************\\鸿盛天极\\视频彩铃-AE模板\\高级模板API上传测试-1.0\\高级模板API上传测试.mp4");
        //File templateZipFile = new File("\\\\*************\\鸿盛天极\\视频彩铃-AE模板\\高级模板API上传测试-1.0\\高级模板API上传测试-1.0.zip");
        //aliMediaProduce.createTemplate(previewMp4File,templateZipFile);

        //aliMediaProduce.generateTemplate();

        //final InputStream objectInputStream = aliMediaProduce.getObjectInputStream(
        //        "https://ims-media.oss-cn-beijing.aliyuncs.com/user-upload/20230612/a28da4b2-2963-4408-b6d9-86c576b06153.mp4");
        //IOUtils.copy(objectInputStream,new FileOutputStream("D:\\ouput_xxx.mp4"));

        //final String mediaId = aliMediaProduce.registerMedia("oss://ims-media/company_hstj/company_hstj-8.jpg");
        //System.out.println("mediaId = " + mediaId); //0de02aa00b2e71ee8fe0e7f7c75b6301
        //aliMediaProduce.digitalHuman();
    }

    public void generateTemplate(){
        final Path templateContainerPath = Paths.get("\\\\*************\\鸿盛天极\\视频彩铃-AE模板");
        try {
            Files.find(templateContainerPath,1,(path, basicFileAttributes) -> {
                if(!basicFileAttributes.isDirectory()){
                    return false;
                }
                if(path.equals(templateContainerPath)){
                    return false;
                }
                return path.toFile().listFiles((dir, name) -> name.toLowerCase().endsWith(".json")).length == 0;
            }).forEach(path -> {
                log.info("需要创建模板的文件夹:{}", path.getFileName());
                final File[] files = path.toFile().listFiles();
                File previewMp4File = null;
                File templateZipFile = null;
                for (int i = 0; i < files.length; i++) {
                    File file = files[i];
                    if (file.getName().toLowerCase().endsWith(".mp4")) {
                        previewMp4File = file;
                    } else if (file.getName().toLowerCase().endsWith(".zip")) {
                        templateZipFile = file;
                    }
                }
                if (previewMp4File != null && templateZipFile != null) {
                    try {
                        final String templateId = createTemplate(previewMp4File, templateZipFile);
                        waitTemplateCreateAndGenJsonFile(path, templateId);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public String createTemplate(File previewMp4File,File templateZipFile) throws Exception {
            String templateOssPath = "template/advance/";
            final String mp4FileOssPath = templateOssPath + previewMp4File.getName();
            putObjectFile(mp4FileOssPath, previewMp4File);
            final String mp4FileUrl =  "oss://" + bucket + "/" + mp4FileOssPath;
            final String mp4MediaId = registerMedia(mp4FileUrl);
            final String zipFileOssPath = templateOssPath + templateZipFile.getName();
             putObjectFile(zipFileOssPath, templateZipFile);
            //final String zipFileUrl = "oss://" + bucket + "/" + mp4FileOssPath;
            final String zipFileUrl = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + zipFileOssPath;
            AddTemplateRequest request = new AddTemplateRequest();
            request.setType("VETemplate");
            request.setName(FilenameUtils.removeExtension(previewMp4File.getName()));
            request.setConfig("{\"oss_url\":\""+ zipFileUrl +"\"}");
            request.setPreviewMedia(mp4MediaId);
            AddTemplateResponse response = iceClient.addTemplate(request);
            final String templateId = response.getBody().getTemplate().getTemplateId();
            //System.out.println("templateId : " + templateId);
            log.info("创建模板=>名称:{},模板id:{},响应:{}",previewMp4File.getName(),templateId,mapper.writeValueAsString(response));
        return templateId;
    }

    public void waitTemplateCreateAndGenJsonFile(Path templateDirPath,String templateId){
        try {
            Stopwatch stopWatch = Stopwatch.createStarted();
            // 等待合成任务完成
            while (true) {
                final String templateInfo = fetchTemplateInfo(templateId);
                if (StringUtils.isNotEmpty(templateInfo)) {
                    final String newTemplateInfo = templateInfo.replaceAll("mediaId", "960:540");
                    log.info("模板信息抓取=>templateId:{},,阿里模板解析用时:{},newTemplateInfo:{}",templateId,stopWatch,newTemplateInfo);
                    final String jsonFileName = FilenameUtils.concat(templateDirPath.toAbsolutePath().toString(), templateId + ".json");
                    FileUtils.write(new File(jsonFileName),newTemplateInfo,StandardCharsets.UTF_8);
                    break;
                }
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            log.info("等待模板创建异常:",e);
        }
    }

    public void generateTemplateJsonFile(){
        final Path templateContainerPath = Paths.get("\\\\*************\\鸿盛天极\\视频彩铃-AE模板");
        try {
            Files.find(templateContainerPath,2,(path, basicFileAttributes) -> basicFileAttributes.isRegularFile() && path.getFileName().toString().endsWith(".txt"))
                    .forEach(path -> {
                        try {
                            final File file = path.toFile();
                            final String templateId = FilenameUtils.removeExtension(file.getName());
                            final String templateInfo = this.fetchTemplateInfo(templateId);
                            final String newTemplateInfo = templateInfo.replaceAll("mediaId", "960:540");
                            log.info("模板信息抓取=>templateId:{},newTemplateInfo:{}",templateId,newTemplateInfo);
                            FileUtils.write(file,newTemplateInfo,StandardCharsets.UTF_8);
                            final String jsonFileName = FilenameUtils.concat(path.getParent().toAbsolutePath().toString(), templateId + ".json");
                            file.renameTo(new File(jsonFileName));
                            //String clipsParam = "{\"Media4\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"Media3\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/2.jpg\",\"Media2\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/3.jpg\",\"Media1\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/11.jpg\",\"Media0\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/22.jpg\"}";
                            String clipsParam = "{\"pic1\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"pic2\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/2.jpg\",\"pic4\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/480x270.jpg\"}";
                            //String clipsParam = "{}";
                            this.mergeTemplate(templateId, clipsParam);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    });
            
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 模板合成
     * @param templateId
     * @param clipsParam
     * @return
     */
    public String mergeTemplate(String templateId, String clipsParam){
        int height = 1280;
        int width = 720;
        String mediaProduceDir = "output";
        String jobCallbackMnsQueue = "ice-callback-queue-job";
        SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();
        submitMediaProducingJobRequest.setTemplateId(templateId);
        submitMediaProducingJobRequest.setClipsParam(clipsParam);
        final String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String mediaURL = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + mediaProduceDir + "/" + dateString + "/" + DigestUtils.md5Hex(String.valueOf(Math.random())) + ".mp4";
        //System.out.println("mediaURL = " + mediaURL);
        //输出参数参看 https://help.aliyun.com/document_detail/357745.html?spm=a2c4g.441147.0.0.11bf5664mMawEA#title-4j6-ve7-g31
        //submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + "}");
        submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height+ ",\"Bitrate\":" + 2000 + ",\"Video\":{\"Codec\":\"H.264\",\"Fps\":22,\"Profile\":\"main\",\"Preset\":\"medium\",\"Crf\":20}"+ "}");
        submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\""+jobCallbackMnsQueue+"\"}"); //MNS回调地址（必须以ice-callback开头的消息队列才可以进行回调）
        System.out.println("outputMediaConfig:" + submitMediaProducingJobRequest.getOutputMediaConfig());
        try {
            SubmitMediaProducingJobResponse response = iceClient.submitMediaProducingJob(submitMediaProducingJobRequest);
            System.out.println("submit mps response : " + JSONObject.toJSONString(response.body));
            final String jobId = response.getBody().getJobId();
            waitJobComplete(jobId);
            return jobId;
        } catch (Exception e) {
            log.info("模板合成,异常:", e);
            return null;
        }
    }

    /**
     *  获取模板信息
     */
    public String fetchTemplateInfo(String templateId) {
        GetTemplateRequest request = new GetTemplateRequest();
        request.setTemplateId(templateId);
        try {
            GetTemplateResponse response = iceClient.getTemplate(request);
            GetTemplateResponseBody.GetTemplateResponseBodyTemplate template = response.getBody().getTemplate();
            return template.getClipsParam();
        } catch (Exception e) {
            log.info("获取模板信息,异常:", e);
            return null;
        }
    }

    /**
     *  等待合成任务完成
     * @param jobId
     */
    public void waitJobComplete(String jobId){
        try {
            Stopwatch stopWatch = Stopwatch.createStarted();
            // 等待合成任务完成
            while (true) {
                GetMediaProducingJobRequest getMediaProducingJobRequest = new GetMediaProducingJobRequest();
                getMediaProducingJobRequest.setJobId(jobId);
                GetMediaProducingJobResponse getMediaProducingJobResponse = iceClient.getMediaProducingJob(getMediaProducingJobRequest);
                System.out.println("GetMediaProducingJobResponse : " + JSONObject.toJSONString(getMediaProducingJobResponse.body));
                String status = getMediaProducingJobResponse.getBody().getMediaProducingJob().getStatus();
                if ("Success".equals(status)) {
                    log.info("jobId:{},媒体合成用时:{}", jobId, stopWatch);
                    break;
                }
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            log.info("媒体合成异常:",e);
        }
    }

    public void digitalHuman() throws Exception {
        // 视频尺寸
        Integer width = 720;
        Integer height = 1280;
        Stopwatch stopWatch = Stopwatch.createStarted();
        String timeline = "{\n" + "    \"VideoTracks\": [{\n" + "        \"VideoTrackClips\": [{\n" + "            \"Type\": \"GlobalImage\",\n" + "            \"MediaId\": \"0de02aa00b2e71ee8fe0e7f7c75b6301\"\n" + "        }]\n" + "    }, {\n" + "        \"VideoTrackClips\": [{\n" + "            \"Comment\": \"数字人-输入为文字\",\n" + "            \"Type\": \"AI_Avatar\",\n" + "            \"AvatarId\": \"yunxin\",\n" + "            \"Content\": \"您好，欢迎致电鸿盛天极科技有限公司，我公司以人为本，真诚服务，以优质的产品开拓市场，以满意的服务赢得客户。让客户满意是我们的标准，为客户创造价值，是我们的心愿。愿我们真诚的服务为您带去愉悦。电话接通中，请稍后\",\n" + "            \"Voice\": \"zhitian\",\n" + "            \"Effects\": [{\n" + "                \"Type\": \"AI_ASR\",\n" + "                \"Font\": \"Alibaba PuHuiTi\",\n" + "                \"FontSize\": 60,\n" + "                \"Spacing\": 1,\n" + "                \"Ratio\": 1\n" + "            }]\n" + "        }]\n" + "    }]\n" + "}";
        System.out.println(timeline);
        // 提交合成任务
        SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();
        submitMediaProducingJobRequest.setTimeline(timeline);
        String mediaURL = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + DigestUtils.md5Hex(String.valueOf(Math.random())) + ".mp4";
        submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + "}");
        //submitIProductionJobRequest.setNotifyUrl(notifyUrl); //智能媒体剪辑不支持在这个方法上设置回调通知
        //submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\""+notifyUrl+"\"}"); //http方式回调
        submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\""+iceCallbackMnsQueueName+"\"}"); //MNS回调地址（必须以ice-callback开头的消息队列才可以进行回调）
        SubmitMediaProducingJobResponse submitMediaProducingJobResponse = iceClient.submitMediaProducingJob(submitMediaProducingJobRequest);
        System.out.println("job created, jobId : " + submitMediaProducingJobResponse.body.jobId + ", requestId : " + submitMediaProducingJobResponse.body.getRequestId() + ", mediaURL : " + mediaURL);

        // 等待合成任务完成
        while (true) {
            GetMediaProducingJobRequest getMediaProducingJobRequest = new GetMediaProducingJobRequest();
            getMediaProducingJobRequest.setJobId(submitMediaProducingJobResponse.body.jobId);
            GetMediaProducingJobResponse getMediaProducingJobResponse = iceClient.getMediaProducingJob(getMediaProducingJobRequest);
            System.out.println("GetMediaProducingJobResponse : " + JSONObject.toJSONString(getMediaProducingJobResponse.body));
            String status = getMediaProducingJobResponse.getBody().getMediaProducingJob().getStatus();
            if ("Success".equals(status)) {
                log.info("媒体合成用时:" + stopWatch);
                break;
            }
            Thread.sleep(1000);
        }
        System.out.println("Produce succeed : " + mediaURL);
    }

    public void batchProduceVideo() throws Exception {

        // 文字素材
        //String text = "人们懂得用五味杂陈形容人生，因为懂得味道是每个人心中固守的情怀。在这个时代，每一个人都经历了太多的苦痛和喜悦，人们总会将苦涩藏在心里，而把幸福变成食物，呈现在四季的餐桌之上";
        //String text = "您好，欢迎您致电兄弟洗车美容中心，本店主要经营汽车美容、装潢、汽车修理、车辆保险、二手车买卖、汽车租赁、商务接待、机场接送、婚庆车队等服务。我们秉承诚信为本，客户至上的经营理念，竭诚为客户提供优质的服务。电话接通中，请稍后。";
        String text = "您好，欢迎致电鸿盛天极科技有限公司，我公司以人为本，真诚服务，以优质的产品开拓市场，以满意的服务赢得客户。让客户满意是我们的标准，为客户创造价值，是我们的心愿。愿我们真诚的服务为您带去愉悦。电话接通中，请稍后";

        // 视频素材
        List<String> videoUrls = new ArrayList();
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_1.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_2.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_3.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_4.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_5.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_6.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_7.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_8.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_9.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_10.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_11.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your_video_12.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/video.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/video.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/video.mp4");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/video.mp4");
        //https://ims-media.oss-cn-beijing.aliyuncs.com/company_hstj/company_hstj-1.jpg
        videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-1.jpg");
        videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-2.jpg");
        videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-3.jpg");
        videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-4.jpg");
        videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-5.jpg");
        videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-6.jpg");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-7.jpg");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-8.jpg");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj_x-1.jpg");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj_x-2.jpg");
        //videoUrls.add("https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj_x-3.jpg");

        // 背景音乐
        //String bgMusic = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/iproduction/demo/music/generated_6_good.wav";
        //String bgMusic = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/turning.mp3";
        String bgMusic = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/background.mp3";

        // 字幕样式设置
        //Integer fontSize = 45;
        Integer fontSize = 50;
        String fontName = "WenQuanYi Zen Hei Mono";
        String fontColor = "#FFFFFF";

        // 视频尺寸
        Integer width = 720;
        Integer height = 1280;
        //Integer width = 640;
        //Integer height = 360;
        //Integer width = 1080;
        //Integer height = 1920;

        // logo
        //String logoUrl = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/your-logo.png";
        //String logoUrl = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/android_logo.png";
        String logoUrl = "";
        Integer logoX = 20;
        Integer logoY = 20;

        //String title = "这里是标题";
        String title = "鸿盛天极科技有限公司";
        //String subTitle = "这里是副标题";
        String subTitle = "";

        // 每次提交一个任务，业务方根据需要更换不同参数提交多次
        produceSingleVideo(text, videoUrls, title, subTitle, bgMusic, fontSize, fontName, fontColor, logoUrl, logoX, logoY, width, height);
    }

    // 提交单个任务
    public void produceSingleVideo(String text, List<String> videoUrls, String title, String subtitle, String bgMusic, int fontSize, String fontName, String fontColor,
                                   String logoUrl, Integer logoX, Integer logoY, int width, int height) throws Exception {
        Stopwatch stopWatch = Stopwatch.createStarted();
        //多音字替换
        String duoyinziWord = "鸿盛天极";
        String duoyinziWordReplace = "鸿圣天极";
        text = text.replaceAll(duoyinziWord, duoyinziWordReplace);
        // 提交MPS任务，文字生成语音和字幕
        //String voice = "zhichu";
        //String voice = "siyue";
        String voice = "aida";  //男声
        //String voice = "zhiyue";
        String jobParams = "{\"voice\":\"" + voice + "\",\"format\":\"mp3\",\"sample_rate\":16000}";
        final String dateString = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String textObject = "iproduction/"+ dateString +"/" + DigestUtils.md5Hex(voice+text) + ".txt";
        text = text.replaceAll("[，,.]", "。"); // AsyncTextToSpeech任务用句号进行断句
        putObjectContent(textObject, text);
        String input = "oss://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + textObject;
        System.out.println("input : " + input);

        SubmitIProductionJobRequest submitIProductionJobRequest = new SubmitIProductionJobRequest();
        submitIProductionJobRequest.setFunctionName("AsyncTextToSpeech");
        submitIProductionJobRequest.setInput(input);
        
        submitIProductionJobRequest.setOutput("oss://" + bucket + ".oss-" + regionId + ".aliyuncs.com/iproduction/"+dateString+"/{source}-{sequenceId}.{resultType}");

        submitIProductionJobRequest.setJobParams(jobParams);
        submitIProductionJobRequest.setNotifyUrl("mns://1431075016168616.mns.cn-beijing.aliyuncs.com/queues/"+iceCallbackMnsQueueName); //这个方法设置的是mns的消息通知
        //submitIProductionJobRequest.setUserData("{\"NotifyAddress\":\""+notifyUrl+"\"}"); //也收不到回调
        SubmitIProductionJobResponse submitIProductionJobResponse = mpsClient.submitIProductionJob(submitIProductionJobRequest);
        System.out.println("submit mps response : " + JSONObject.toJSONString(submitIProductionJobResponse.body));

        // 等待任务完成
        String jobId = submitIProductionJobResponse.body.jobId;
        String result;
        while (true) {
            QueryIProductionJobRequest queryIProductionJobRequest = new QueryIProductionJobRequest();
            queryIProductionJobRequest.setJobId(jobId);
            QueryIProductionJobResponse queryIProductionJobResponse = mpsClient.queryIProductionJob(queryIProductionJobRequest);
            System.out.println("job info : " + JSONObject.toJSONString(queryIProductionJobResponse.body));
            if ("Success".equals(queryIProductionJobResponse.body.state)) {
                result = queryIProductionJobResponse.body.result;
                log.info("文字转语音和字幕用时:" + stopWatch);
                break;
            }
            Thread.sleep(1000);
        }
        stopWatch.reset().start();
        // 获取生成的音频和字幕
        JSONObject resultObj = JSONObject.parseObject(result);
        String dataString = resultObj.getString("Data");
        System.out.println("data : " + dataString);

        String audioObject = null;
        String subtitleObject = null;
        JSONObject dataObject = JSONObject.parseObject(dataString);
        JSONArray array = dataObject.getJSONArray("result");
        for (Object obj : array) {
            JSONObject jsonObject = (JSONObject)obj;
            String object = jsonObject.getString("file");
            if (object.endsWith("mp3")) {
                audioObject = object;
            } else if (object.endsWith("txt")) {
                subtitleObject = object;
            }
        }
        System.out.println("audioObject : " + audioObject);
        System.out.println("subtitleObject : " + subtitleObject);

        // 获取字幕内容
        String subtitleContent = getObjectContent(subtitleObject);
        subtitleContent = subtitleContent.replaceAll(duoyinziWordReplace, duoyinziWord);
        System.out.println("subtitleContent : " + subtitleContent);

        // 组装字幕轨
        if (fontSize <= 0) {
            fontSize = 32;
        }
        if (StringUtils.isBlank(fontName)) {
            fontName = "WenQuanYi Zen Hei Mono";
        }
        if (StringUtils.isBlank(fontColor)) {
            fontColor = "#000000";
        }

        // 每次循环将视频素材随机，并提交合成任务
        Collections.shuffle(videoUrls);

        JSONArray subtitleTrackClips = new JSONArray();
        JSONArray mpsSubtitles = JSONArray.parseArray(subtitleContent);
        JSONArray videoTrackClips = new JSONArray();

        // 字幕距离底部距离
        //float subtitleBottom = 0.25f;
        float subtitleBottom = 0.10f;
        // 随机特效，更多特效见：https://help.aliyun.com/document_detail/207059.html
        //List<String> vfxs = Arrays.asList("heartfireworks", "colorfulradial", "meteorshower", "starry", "colorfulstarry", "moons_and_stars", "flyfire", "starexplosion", "spotfall", "sparklestarfield");
        // 随机转场，更多转场见：https://help.aliyun.com/document_detail/204853.html
        List<String> transitions = Arrays.asList("windowslice", "displacement", "bowTieVertical", "linearblur", "waterdrop", "polka", "wiperight", "gridflip", "hexagonalize", "windowblinds", "风车");
        //float transDuration = 0.3f;
        float transDuration = 0.6f;
        float totalDuration = 0f;
        //Collections.shuffle(vfxs);
        for (int i = 0; i < mpsSubtitles.size(); i++) {
            JSONObject mpsSubtitle = mpsSubtitles.getJSONObject(i);
            String content = mpsSubtitle.getString("text");
            content = content.replaceAll("。", "");
            Float timelineIn = mpsSubtitle.getFloat("begin_time") / 1000;
            Float timelineOut = mpsSubtitle.getFloat("end_time") / 1000;
            totalDuration = timelineOut;
            String subtitleClip = "{\"Content\":\"" + content + "\",\"TimelineIn\":" + timelineIn + ",\"TimelineOut\":" + timelineOut +
                    ",\"Type\":\"Text\",\"X\":0.0,\"Y\":" + subtitleBottom + ",\"Font\":\"" + fontName + "\",\"Alignment\":\"BottomCenter\",\"FontSize\":" + fontSize +
                    ",\"FontColor\":\"" + fontColor + "\",\"OutlineColour\":\"#000000\",\"FontColor\":\"#ffffff\",}";
            subtitleTrackClips.add(JSONObject.parseObject(subtitleClip));
        }

        //图片固定时长轮播
        float duration = 3.0f;
        for (int i = 0; i*duration<=totalDuration; i++) {
            // 随机特效
            //String vfx = vfxs.get(i % vfxs.size());
            String transition = transitions.get(i % transitions.size());
            String url = videoUrls.get(i % videoUrls.size());
            JSONObject clip = new JSONObject();
            clip.put("MediaURL", url);
            if (url.endsWith(".jpg")) {
                clip.put("Duration", duration + transDuration);
                clip.put("Type", "Image");
            } else {
                clip.put("Out", duration + transDuration);
            }
            JSONArray effects = new JSONArray();
            // 添加背景模糊
            effects.add(JSONObject.parseObject("{\"Type\":\"Background\",\"SubType\":\"Blur\",\"Radius\":0.1}"));
            // 添加氛围类特效
            //effects.add(JSONObject.parseObject("{\"Type\":\"VFX\",\"SubType\":\"" + vfx + "\"}"));
            // 视频静音
            //effects.add(JSONObject.parseObject("{\"Type\":\"Volume\",\"Gain\":0}"));
            // 添加转场
            effects.add(JSONObject.parseObject("{\"Type\":\"Transition\",\"SubType\":\"" + transition + "\",\"Duration\":"+transDuration+"}"));

            clip.put("Effects", effects);
            videoTrackClips.add(clip);
        }

        if (title != null && title.length() > 0) {
            float titleY = 80;
            int titleFontSize = 65;
            String titleFont = "AlibabaPuHuiTi";
            String titleClip = "{\"Type\":\"Text\",\"X\":0,\"Y\":" + titleY + ",\"Font\":\"" + titleFont + "\",\"Content\":\"" + title + "\",\"Alignment\":\"TopCenter\",\"FontSize\":" + titleFontSize + ",\"FontColor\":\"#FFD700\",\"Outline\":4,\"OutlineColour\":\"#000000\",\"FontFace\":{\"Bold\":true,\"Italic\":false,\"Underline\":false}}";
            subtitleTrackClips.add(JSONObject.parse(titleClip));
        }
        if (subtitle != null && subtitle.length() > 0) {
            float subtitleY = 160;
            int subtitleFontSize = 60;
            String subtitleFont = "AlibabaPuHuiTi";
            String subtitleClip = "{\"Type\":\"Text\",\"X\":0,\"Y\":" + subtitleY + ",\"Font\":\"" + subtitleFont + "\",\"Content\":\"" + subtitle + "\",\"Alignment\":\"TopCenter\",\"FontSize\":" + subtitleFontSize + ",\"FontColorOpacity\":1,\"FontColor\":\"#ffffff\",\"Outline\":2,\"OutlineColour\":\"#000000\",\"FontFace\":{\"Bold\":false,\"Italic\":false,\"Underline\":false}}";
            subtitleTrackClips.add(JSONObject.parse(subtitleClip));
        }

        // 组装音频轨
        String audioUrl = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + audioObject;
        String audioTrackClips = "";
        if (StringUtils.isBlank(bgMusic)) {
            audioTrackClips = "[{\"MediaURL\":\"" + audioUrl + "\"}]";
        } else {
            // 两个音频轨，一个人声，一个音乐
            audioTrackClips = "[{\"MediaURL\":\"" + audioUrl + "\"}]},{\"AudioTrackClips\":[{\"MediaURL\":\"" + bgMusic + "\",\"Effects\":[{\"Type\":\"Volume\",\"Gain\":\""+0.2+"\"}]}]";
        }

        // 图片轨，用于展示logo
        String logoClip = "";
        int logoWidth = 32;
        int logoHeight = 36;
        if (StringUtils.isNotBlank(logoUrl)) {
            logoClip = "{\"ImageURL\":\"" + logoUrl + "\",\"X\":" + logoX + ",\"Y\":"
                    + logoY + ",\"Width\":\"" + logoWidth + "\",\"Height\":\"" + logoHeight + "\"}";
        }

        // 拼时间线
        String timeline = "{\"VideoTracks\":[{\"VideoTrackClips\":" + videoTrackClips.toJSONString() + "}]," +
                "\"SubtitleTracks\":[{\"SubtitleTrackClips\":" + subtitleTrackClips.toJSONString() + "}]," +
                "\"AudioTracks\":[{\"AudioTrackClips\":" + audioTrackClips + "}]," +
                "\"ImageTracks\":[{\"ImageTrackClips\":[" + logoClip + "]}]}";
        System.out.println("timeline : " + timeline);

        // 提交合成任务
        SubmitMediaProducingJobRequest submitMediaProducingJobRequest = new SubmitMediaProducingJobRequest();
        submitMediaProducingJobRequest.setTimeline(timeline);
        String mediaURL = "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/" + DigestUtils.md5Hex(String.valueOf(Math.random())) + ".mp4";
        submitMediaProducingJobRequest.setOutputMediaConfig("{\"MediaURL\":\"" + mediaURL + "\",\"Width\":" + width + ",\"Height\":" + height + "}");
        //submitIProductionJobRequest.setNotifyUrl(notifyUrl); //智能媒体剪辑不支持在这个方法上设置回调通知
        //submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\""+notifyUrl+"\"}"); //http方式回调
        submitMediaProducingJobRequest.setUserData("{\"NotifyAddress\":\""+iceCallbackMnsQueueName+"\"}"); //MNS回调地址（必须以ice-callback开头的消息队列才可以进行回调）
        SubmitMediaProducingJobResponse submitMediaProducingJobResponse = iceClient.submitMediaProducingJob(submitMediaProducingJobRequest);
        System.out.println("job created, jobId : " + submitMediaProducingJobResponse.body.jobId + ", requestId : " + submitMediaProducingJobResponse.body.getRequestId() + ", mediaURL : " + mediaURL);

        // 等待合成任务完成
        while (true) {
            GetMediaProducingJobRequest getMediaProducingJobRequest = new GetMediaProducingJobRequest();
            getMediaProducingJobRequest.setJobId(submitMediaProducingJobResponse.body.jobId);
            GetMediaProducingJobResponse getMediaProducingJobResponse = iceClient.getMediaProducingJob(getMediaProducingJobRequest);
            System.out.println("GetMediaProducingJobResponse : " + JSONObject.toJSONString(getMediaProducingJobResponse.body));
            String status = getMediaProducingJobResponse.getBody().getMediaProducingJob().getStatus();
            if ("Success".equals(status)) {
                log.info("媒体合成用时:" + stopWatch);
                break;
            }
            Thread.sleep(1000);
        }
        System.out.println("Produce succeed : " + mediaURL);

    }
    public com.aliyun.mts20140618.Client createMpsClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = "mts." + regionId + ".aliyuncs.com";
        return new com.aliyun.mts20140618.Client(config);
    }

    public OSS createOssClient() {
        String endpoint = "http://oss-" + regionId + ".aliyuncs.com";
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        return ossClient;
    }

    public String getObjectContent(String object) throws Exception {
        OSSObject obj = ossClient.getObject(bucket, object);
        InputStream stream = obj.getObjectContent();
        String result = CharStreams.toString(new InputStreamReader(stream, StandardCharsets.UTF_8));
        return result;
    }

    public InputStream getObjectInputStream(String object) {
        String aliDomain = "aliyuncs.com/";
        if (StringUtils.contains(object, aliDomain)) {
            object = StringUtils.substringAfter(object, aliDomain);
        }
        OSSObject obj = ossClient.getObject(bucket, object);
        return obj.getObjectContent();
    }

    public Client createIceClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = "ice." + regionId + ".aliyuncs.com";
        return new Client(config);
    }

    public void putObjectContent(String object, String content) throws Exception {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, new ByteArrayInputStream(content.getBytes()));
        ossClient.putObject(putObjectRequest);
    }

    public void putObjectFile(String object, File file) throws Exception {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, object, file);
        ossClient.putObject(putObjectRequest);
    }

    /**
     *  注册媒资内容
     * @param inputUrl  oss地址  如: oss://example-bucket/example.mp4 （此格式默认oss region与服务接入区域一致
     * @return
     * @throws Exception
     */
    public String registerMedia(String inputUrl) throws Exception {
        try {
            RegisterMediaInfoRequest registerMediaInfoRequest = new RegisterMediaInfoRequest();
            //inputUrl如果为oss地址包含region信息就去掉
            //final String newInputUrl = StringUtils.startsWith(inputUrl, "oss:") ? inputUrl.replace(".oss-" + regionId + ".aliyuncs.com/", "/") : inputUrl;
            registerMediaInfoRequest.setInputURL(inputUrl);
            //registerMediaInfoRequest.setMediaType("video");//此字段建议用户按需填写。当InputURL字段是OSS URL时，也支持按照文件后缀自动判断媒资类型（仅限图片、视频、音频文件后缀）
            RegisterMediaInfoResponse registerMediaInfoResponse = iceClient.registerMediaInfo(registerMediaInfoRequest);
            return  registerMediaInfoResponse.getBody().getMediaId();
        } catch (TeaException e) {
            //com.aliyun.tea.TeaException: code: 409, The media with the given inputUrl "oss://ims-media/template/advance/高级模板API上传测试.mp4" has already been registered with mediaId "c99364e006a371eeb652f6f6c5596302". request id: 432CF594-A7A0-58DA-9682-18A69060FFE4
            final String message = e.getMessage();
            if(message.contains("registered with mediaId")){
                return StringUtils.substringBetween(message, "registered with mediaId \"", "\".");
            }
            throw e;
        }
    }

}