package org.jeecg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eleven.cms.ad.IAdFeedbackService;
import com.eleven.cms.dto.TestDTO;
import com.eleven.cms.entity.CouponCode;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.AliComplainService;
import com.eleven.cms.remote.JiangsuYidongService;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.service.*;
import com.eleven.cms.vo.JiangsuResponseQueryInfo;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.qycl.service.IDataCollectService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.SpringContextUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Stream;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class AdApiServiceTest {
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    AliComplainService aliComplainService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    //@Autowired
    //ToutiaoAdService toutiaoAdService;
    //@Autowired
    //TuiaAdService tuiaAdApiService;
    //@Autowired
    //DoumengAdService doumengAdService;
    //@Autowired
    //BianxianmaoAdService bianxianmaoAdService;
    //@Autowired
    //QutoutiaoAdService qutoutiaoAdService;
    //@Autowired
    //HudongtuiAdService hudongtuiAdService;
    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    OutsideCallbackService outsideCallbackService;

    public static ObjectMapper mapper = new ObjectMapper();

    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    //@Test
    //public void smsNotify(){
    //    aliComplainService.smsNotify("MEMBER_990_PLUS,此条为测试短信,请忽略!");
    //}


    //@Test
    //public void countFeedbackByMobile(){
    //    String mobile = "13438828200";
    //    final Integer count = subscribeService.lambdaQuery()
    //                                          .eq(Subscribe::getMobile, mobile)
    //                                          .between(Subscribe::getCreateTime, LocalDate.now().atTime(LocalTime.MIN), LocalDate.now().atTime(LocalTime.MAX))
    //                                          .eq(Subscribe::getTuiaFeedbackStatus, 1)
    //                                          .count();
    //    System.out.println("count = " + count);
    //}

    @Autowired
    private MiguApiService miguApiService;

    @Test
    public void test() throws InterruptedException, IOException {
        List<Subscribe> list = subscribeService.list(new LambdaQueryWrapper<Subscribe>()
                .between(Subscribe::getCreateTime, "2024-11-01 00:00:00", "2024-11-11 23:59:59")
                .eq(Subscribe::getChannel,"SXYD_JBAICL")
                .eq(Subscribe::getStatus, 1));
        System.out.println(list.size());

        List<TestDTO> data = new CopyOnWriteArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(100);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        for (Subscribe subscribe : list) {
            executorService.execute(() -> {
                TestDTO testDTO = new TestDTO();
                testDTO.setId(subscribe.getId());
                testDTO.setChannel(subscribe.getChannel());
                testDTO.setMiguChannel("00211DQ");
                testDTO.setMobile(subscribe.getMobile());
                testDTO.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(subscribe.getCreateTime()));
                testDTO.setStatus(subscribe.getStatus());
                RemoteResult remoteResult = miguApiService.schQuery(subscribe.getMobile(), testDTO.getMiguChannel());
                if (remoteResult.isSchMember()) {
                    testDTO.setTdStatus(0);
                } else {
                    testDTO.setTdStatus(1);
                }
                data.add(testDTO);
                countDownLatch.countDown();
            });
        }
        System.out.println("等待");
        countDownLatch.await();
        System.out.println("完成");
        System.out.println(data.size());
//        return export("11.01-11.11 SXYD_JBAICL成功订单退订情况", TestDTO.class, data);
        FileOutputStream fileOutputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\11.01-11.11 SXYD_JBAICL成功订单退订情况.csv");
        StringBuilder sb = new StringBuilder();
        for (TestDTO obj : data) {
            sb
//                    .append(obj.getId()).append(",")
                    .append(obj.getChannel()).append(",")
                    .append(obj.getMiguChannel()).append(",")
                    .append(obj.getMobile()).append(",")
                    .append(obj.getCreateTime()).append(",")
                    .append(obj.getStatus()).append(",")
                    .append(obj.getTdStatus()).append("\n");
        }
        fileOutputStream.write(sb.toString().getBytes());
    }

    @Test
    public void testSxhGetCircleId() {
        miguApiService.sxhGetCircleId("18881484185", "00211AH");
    }

    public static void main(String[] args) throws IOException {
        List<TestDTO> data = new CopyOnWriteArrayList<>();
        TestDTO testDTO = new TestDTO();
        testDTO.setId("1");
        testDTO.setChannel("2");
        testDTO.setMiguChannel("2");
        testDTO.setMobile("2");
        testDTO.setCreateTime(new Date().toString());
        testDTO.setStatus(1);
        testDTO.setTdStatus(1);
        data.add(testDTO);
        data.add(testDTO);
        data.add(testDTO);
//        return export("11.01-11.11 SXYD_JBAICL成功订单退订情况", TestDTO.class, data);
        FileOutputStream fileOutputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\test.txt");
        StringBuilder sb = new StringBuilder();
        for (TestDTO obj : data) {
            sb.append(obj.getId()).append(",")
                    .append(obj.getChannel()).append(",")
                    .append(obj.getMiguChannel()).append(",")
                    .append(obj.getMobile()).append(",")
                    .append(obj.getCreateTime()).append(",")
                    .append(obj.getStatus()).append(",")
                    .append(obj.getTdStatus()).append("\n");
            fileOutputStream.write(sb.toString().getBytes());
        }
    }

    @Test
    public void toutiao() throws InterruptedException, JsonProcessingException {
//        //String url = "https://crbt.cdyrjygs.com/vrbt_channel/?adid=1684219866241718&clickid=ELa15_af-f4CGI7IlrbZ5f0CKIOQj8q1tP4C&creativeid=1684219866241718&creativetype=1#/?subChannel=xxltt";
//        String url = "https://crbt.cdyrjygs.com/vrbt_channel/?adid=1684219866241718&clickid=ELa15_af-f4CGI7IlrbZ5f0CKIOQj8q1tP4C&creativeid=1684219866241718&creativetype=1";
//        Subscribe subscribe = new Subscribe();
//        subscribe.setDeviceInfo(url);
//        final IAdFeedbackService bean = SpringContextUtils.getBean("toutiaoAdService",IAdFeedbackService.class);
//        bean.feedback(subscribe);
//        TimeUnit.SECONDS.sleep(5L);
//        String token = jiangsuYidongService.getToken();
//        System.out.println("token = " + token);

//        JiangsuResponseCreateOrder jiangsuResponseCreateOrder = jiangsuYidongService.createOrder("13770312019","com.abc","123","https://crbt.cdyrjygs.com/vrbt_channel/?adid=1684219866241718");
//        System.out.println("jiangsuResponseCreateOrder.isOk() = " + jiangsuResponseCreateOrder.isOk());
//        JiangsuResponseSmsValidate jiangsuResponseSmsValidate = jiangsuYidongService.smsValidate("13770312019","7039510104618999808","123456", jiangsuYidongService.getToken());
//        System.out.println("jiangsuResponseSmsValidate.isOk() = " + jiangsuResponseSmsValidate.isOk());

//        JiangsuResponseQueryInfo jiangsuResponseQueryInfo = jiangsuYidongService.queryOrder("13770312019", "7039510104618999808", jiangsuYidongService.getToken());
//        System.out.println("jiangsuResponseQueryInfo.isOk() = " + jiangsuResponseQueryInfo.isOk());
//        jiangsuResponseQueryInfo = jiangsuYidongService.queryOrder("13770312019", "7039481884534345728", jiangsuYidongService.getToken());
//        System.out.println("jiangsuResponseQueryInfo.isOk() = " + jiangsuResponseQueryInfo.isOk());
//        jiangsuYidongService.orderCheck("13770312019",jiangsuYidongService.getToken() );
//        jiangsuYidongService.orderCheck("13815835589",jiangsuYidongService.getToken() );
        JiangsuResponseQueryInfo jiangsuResponseQueryInfo = jiangsuYidongService.queryOrder("13852709113", "7067015957509881856", jiangsuYidongService.getToken());
        System.out.println(new ObjectMapper().writeValueAsString(jiangsuResponseQueryInfo));


    }

    @Test
    public void tuia() throws InterruptedException {
        Subscribe subscribe = new Subscribe();
        subscribe.setDeviceInfo("1234568");
        subscribe.setIp("***************");
        subscribe.setUserAgent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36");
        final IAdFeedbackService bean = SpringContextUtils.getBean("tuiaAdService",IAdFeedbackService.class);
        bean.feedback(subscribe);
        TimeUnit.SECONDS.sleep(5L);
    }

    @Test
    public void doumeng() throws InterruptedException {
        Subscribe subscribe = new Subscribe();
        subscribe.setDeviceInfo("5fbc9d4fe8e24419fdae28d0");
        final IAdFeedbackService bean = SpringContextUtils.getBean("doumengAdService",IAdFeedbackService.class);
        bean.feedback(subscribe);
        TimeUnit.SECONDS.sleep(5L);
    }

    @Test
    public void bianxianmao() throws InterruptedException {
        Subscribe subscribe = new Subscribe();
        subscribe.setDeviceInfo("123456");
        final IAdFeedbackService bean = SpringContextUtils.getBean("bianxianmaoAdService",IAdFeedbackService.class);
        bean.feedback(subscribe);
        TimeUnit.SECONDS.sleep(5L);
    }

    @Test
    public void qutoutiao() throws InterruptedException {
        //qutoutiaoAdService.effectFeedback("https%3A%2F%2Fadv.aiclk.com%2Ftools%2Fcallback%2Ftarget-url-monitor%2F44957%3Fmd5%3Da954c8616fb6c0e4ed8e3831054c8e64");
        Subscribe subscribe = new Subscribe();
        subscribe.setDeviceInfo("https://adv.aiclk.com/tools/callback/target-url-monitor/44957?md5=a954c8616fb6c0e4ed8e3831054c8e64");
        final IAdFeedbackService bean = SpringContextUtils.getBean("qutoutiaoAdService",IAdFeedbackService.class);
        bean.feedback(subscribe);
        TimeUnit.SECONDS.sleep(5L);
    }

    //互动推广告转化上报API-响应:{"code":200,"data":{"msg":"success"}}
    @Test
    public void hudongtui() throws InterruptedException {
        Subscribe subscribe = new Subscribe();
        subscribe.setDeviceInfo("E0H2YD9S1KZV7TD682");
        subscribe.setMobile("18215600194");
        final  IAdFeedbackService bean = SpringContextUtils.getBean("hudongtuiAdService",IAdFeedbackService.class);
        bean.feedback(subscribe);
        TimeUnit.SECONDS.sleep(5L);
    }

    //https://crbt.cdyrjygs.com/cpmb_ch/?qz_gdt=ydvzqyadaaaa2tpnfvia#/?subChannel=CPMB_TXHS
    @Test
    public void tengxun() throws InterruptedException {
        Subscribe subscribe = new Subscribe();
        subscribe.setDeviceInfo("ftxzyyacaaahjpgjpcla");
        subscribe.setMobile("13438828200");
        final  IAdFeedbackService bean = SpringContextUtils.getBean("tengxunAdMaiheService",IAdFeedbackService.class);
        bean.feedback(subscribe);
        TimeUnit.SECONDS.sleep(5L);
    }

    @Test
    public void byBeanName() throws InterruptedException {
        Subscribe subscribe = new Subscribe();
        subscribe.setDeviceInfo("5fbc9d4fe8e24419fdae28d0");
        final IAdFeedbackService bean = SpringContextUtils.getBean("doumengAdService",IAdFeedbackService.class);
        bean.feedback(subscribe);
        TimeUnit.SECONDS.sleep(5L);
    }

    @Autowired
    private ICouponCodeService couponCodeService;
    @Test
    public void couponCode() throws InterruptedException {
//        Subscribe subscribe = new Subscribe();
//        subscribe.setDeviceInfo("5fbc9d4fe8e24419fdae28d0");
//        final IAdFeedbackService bean = SpringContextUtils.getBean("doumengAdService",IAdFeedbackService.class);
//        bean.feedback(subscribe);
//        TimeUnit.SECONDS.sleep(5L);
        List<CouponCode> couponCodeList = Lists.newArrayList();
        for (int i = 0; i < 61758; i++) {
            String sid = RandomStringUtils.randomAlphanumeric(10);
            CouponCode couponCode = new CouponCode();
            couponCode.setCouponCode(sid);
            couponCode.setRightsId("tuniu");
            couponCode.setStatus(0);
            couponCode.setCreateTime(new Date());
            couponCode.setUpdateTime(new Date());
            couponCode.setUpdateBy("1");
            couponCodeList.add(couponCode);
            if (couponCodeList.size() % 1000 == 0 || i >= 40000 - 1) {
                couponCodeService.saveBatch(couponCodeList);
                couponCodeList.clear();
            }
        }


    }


    @Autowired
    private IGuangDongMobileService guangDongMobileService;
    @Test
    public void sendSms() throws InterruptedException {
        Result<?> result=guangDongMobileService.sendSms("15915401656","GDYD_XFJ7");
        log.info("result:{}",result);
    }


    @Test
    public void submitSms() throws InterruptedException {
        guangDongMobileService.submitSms("13530179870","1459","840d121334758c67ca43e702d82dbd03","","GDYD_LLB");
    }
    //数据回执省份录入
    @Autowired
    private IDataCollectService dataCollectService;
    @Test
    public void updateDataNotify() throws InterruptedException {
        dataCollectService.updateDataNotify();
    }
    //企业彩铃数据汇总
    @Test
    public void dataCollect() throws InterruptedException {
        dataCollectService.dataCollect();
    }
    //添加渠道订购数据定时任务
    @Autowired
    private IOrderTarckService orderTarckService;
    @Test
    public void addTarck() throws Exception {
        orderTarckService.addTarck();
    }


    /**
     * 途牛权益发送券码消息队列接收消息
     * @param tuniuSendCodeDeductMessage
     */
    @Autowired
    private ITuniuCouponCodeChargeLogService tuniuCouponCodeChargeLogService;
    @Test
    public void  tuniuSendCodeQueueListener(){
        //途牛权益发送券码
        tuniuCouponCodeChargeLogService.sendCodeScheduleDeduct("1774635772651360258");
    }

    @Test
    public void readImageFile() throws FileNotFoundException {
//        String drainageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/drainageImg.jpg"));
//        System.out.println(drainageImgFileToBase64);
//        String landingPageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/landingPageImg.jpg"));
//        System.out.println(landingPageImgFileToBase64);
//        String confirmPageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/confirmPageImg.png"));
//        System.out.println(confirmPageImgFileToBase64);
//        System.out.println(outsideConfigService.isZsyOutsideChannel("OSVRBT51"));
//        System.out.println(outsideConfigService.isZsyOutsideChannel("OSVRBT48"));
//        Subscribe subscribe = subscribeService.getById("1788477554739769345");
//        if (outsideConfigService.isZsyOutsideChannel(subscribe.getSubChannel())) {
//            SpringUtil.getBean(OutsideCallbackService.class).zysCallback(subscribe, "回调通知");
//        } else {
//            System.out.println("非中视游通知");
//        }

//        LocalDateTime start = LocalDate.now().minusDays(1).atTime(LocalTime.MIN);
//        LocalDateTime end = LocalDate.now().minusDays(1).atTime(LocalTime.MAX);
//        List<Subscribe> list = subscribeService.lambdaQuery().eq(Subscribe::getChannel, BizConstant.BIZ_CHANNEl_JXYD_FPLHHY).eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).gt(Subscribe::getCreateTime, start).lt(Subscribe::getCreateTime, end).orderByAsc(Subscribe::getCreateTime).last("limit 20").list();
//        list.forEach(subscribe -> {
//            outsideCallbackService.zysCallback(subscribeService.getById(subscribe.getId()), subscribe.getResult());
//        });


    }


    /**
     * 补发广告平台消息
     */
    @Test
    public void sendAdReportMessage() {
        String filename = "F:\\Download\\RabbitMQ消息接收-json.txt";
        try (Stream<String> lines = Files.lines(Paths.get(filename), Charset.defaultCharset())) {
            lines.forEachOrdered(line -> {
                log.info(line);
                try {
                    rabbitMQMsgSender.sendAdReportMessage(mapper.readTree(line));
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

