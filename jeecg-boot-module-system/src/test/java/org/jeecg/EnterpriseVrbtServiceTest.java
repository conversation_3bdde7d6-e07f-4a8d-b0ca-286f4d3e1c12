package org.jeecg;

import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.entity.QyclActionLog;
import com.eleven.qycl.service.AliMediaService;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.util.PoiPublicUtil;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/7/21 15:14
 * Desc:企业视频彩铃api测试
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class EnterpriseVrbtServiceTest {

    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    AliMediaService aliMediaService;
    //测试环境
    public static final String TEST_DEPARTMENT_ID = "3099952769716";
    //public static final String PROD_DEPARTMENT_ID = "3099952896248";
    public static final String PROD_DEPARTMENT_ID = "3099952820907";


    @Test
    public void networkTest() {
        boolean qycl = enterpriseVrbtService.verifyMonth("18482155682", "QYCL");
        System.out.println("qycl = " + qycl);
        boolean qycl_mh = enterpriseVrbtService.verifyMonth("18482155682", "QYCL_MH");
        System.out.println("qycl_mh = " + qycl_mh);
    }

//    @Test
//    public void submitRing() {
//        //final File ringFile = new File("D:\\ring_video_sample.mp4");
//        final File ringFile = new File("D:\\9596e1a230fd13bdd893025cd32dc1bd.mp4");
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRing("3099952820907","4a53edf78716b16c41cac5375f89f8d1",ringFile,EnterpriseVrbtService.RING_TYPE_VEDIO);
//
//        //final File ringFile = new File("D:\\merge.mp3");
//        //final EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRing(PROD_DEPARTMENT_ID,"某某有限公司测试",ringFile,EnterpriseVrbtService.RING_TYPE_AUDIO);
//        log.info("entVrbtResult:{}",entVrbtResult);
//    }
//
//    @Test
//    public void submitRingInputStream() throws IOException {
//        String ringOssUrl = "https://ims-media.oss-cn-beijing.aliyuncs.com/output/20230328/4a53edf78716b16c41cac5375f89f8d1.mp4";
//        final InputStream objectInputStream = aliMediaService.getObjectInputStream(org.apache.commons.lang3.StringUtils.substringAfter(ringOssUrl,"aliyuncs.com/"));
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRing("3099952820907","4a53edf78716b16c41cac5375f89f8d2",objectInputStream,EnterpriseVrbtService.RING_TYPE_VEDIO);
//
//        //final File ringFile = new File("D:\\merge.mp3");
//        //final EntVrbtResult entVrbtResult = enterpriseVrbtService.submitRing(PROD_DEPARTMENT_ID,"某某有限公司测试",ringFile,EnterpriseVrbtService.RING_TYPE_AUDIO);
//        log.info("entVrbtResult:{}",entVrbtResult);
//    }
//
//    @Test
//    public void setDeptRingsByTime() {
//        //final EntVrbtResult entVrbtResult = enterpriseVrbtService.setDeptRingsByTime(PROD_DEPARTMENT_ID,EnterpriseVrbtService.RING_TYPE_VEDIO,"600926100600700826");
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.setDeptRingsByTime("3099952820907",EnterpriseVrbtService.RING_TYPE_VEDIO,"600926100600918650");
//        //final EntVrbtResult entVrbtResult = enterpriseVrbtService.setDeptRingsByTime(PROD_DEPARTMENT_ID,EnterpriseVrbtService.RING_TYPE_AUDIO,"600926600600698960");
//        log.info("entVrbtResult:{}",entVrbtResult);
//    }
//
//    @Test
//    public void searchEcRing() {
//        enterpriseVrbtService.queryContentMembers("", "13438828200", QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.searchEcRing("3100013196333", QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//        log.info("entVrbtResult:{}",entVrbtResult);
//    }
////
//    @Test
//    public void searchEcRingSetting() {
//        EntVrbtResult entVrbtResult = enterpriseVrbtService.searchEcRingSetting("3100013196333", QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//        log.info("entVrbtResult:{}",entVrbtResult);
//        entVrbtResult = enterpriseVrbtService.searchEcRingSetting("3100040655851", QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//        log.info("entVrbtResult:{}", entVrbtResult);
//        entVrbtResult = enterpriseVrbtService.searchEcRingSetting("3100043682641", QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//        log.info("entVrbtResult:{}", entVrbtResult);
//
//    }
//
//    @Test
//    public void updateVideoFunc() {
//         EntVrbtResult entVrbtResult = enterpriseVrbtService.updateVideoFunc(PROD_DEPARTMENT_ID,1);
//        //entVrbtResult = enterpriseVrbtService.updateVideoFunc(TEST_DEPARTMENT_ID,1);
//        log.info("entVrbtResult:{}",entVrbtResult);
//    }
////
//    @Test
//    public void ecOperation() {
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.ecOperation("0","DP"+ DateUtil.formatFullTime(LocalDateTime.now()),null,"maihe");
////        //final EntVrbtResult entVrbtResult = enterpriseVrbtService.ecOperation("2","悠然荐音修改部门测试","3099952770730");
////        final EntVrbtResult entVrbtResult = enterpriseVrbtService.ecOperation("1","","3099952770730");
//        log.info("entVrbtResult:{}",entVrbtResult);
////        log.info("departmentId:{}",entVrbtResult.getData().at("/departmentId").asText());
//    }
//
//    @Test
//    public void searchEcDepartments() {
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.searchEcDepartments("yrjy");
//        log.info("entVrbtResult:{}",entVrbtResult);
//    }
//
//    @Test
//    public void addContentMembers() {
//        String[] billNums = "13699402402,15915401656".split(",");
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.addContentMembers("3100043682641", QyclConstant.QYCL_COMPANY_OWNER_YRJY, billNums);
//        log.info("entVrbtResult:{}", entVrbtResult);
//    }
////
////    @Test
////    public void deleteContentMembers() {
////        final EntVrbtResult entVrbtResult = enterpriseVrbtService.deleteContentMembers("3099954434299","13438828200");
////        log.info("entVrbtResult:{}",entVrbtResult);
////    }
//
//    @Test
//    public void queryContentMembers() {
//        //final EntVrbtResult entVrbtResult = enterpriseVrbtService.queryContentMembers("3099954349285","");
//        final EntVrbtResult entVrbtResult = enterpriseVrbtService.queryContentMembersImmediate("","13438828200",QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//        //final EntVrbtResult entVrbtResult = enterpriseVrbtService.queryContentMembersImmediate("3099985740753", "", QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//        //log.info("entVrbtResult:{}",entVrbtResult);
//        final EntVrbtResult entVrbtResult1 = enterpriseVrbtService.searchEcRing("3100013196333", QyclConstant.QYCL_COMPANY_OWNER_YRJY);
//        //log.info("entVrbtResult1:{}",entVrbtResult1);
//        //enterpriseVrbtService.setDeptRingsByTime("3100013196333","1",QyclConstant.QYCL_COMPANY_OWNER_YRJY,"600926100601024702");
//        enterpriseVrbtService.setDeptRingsByTime("3100013196333","1",QyclConstant.QYCL_COMPANY_OWNER_YRJY,"600926100601855664");
//        System.out.println();
//    }
//
//    //@Test
//    //public void manualAddMember(){
//    //
//    //    String filePath = "D:\\data.xlsx";
//    //    ImportParams params = new ImportParams();
//    //    params.setTitleRows(0);
//    //    List<Map<String, Object>> list = ExcelImportUtil.importExcel(
//    //            new File(filePath), Map.class, params);
//    //    for (int i = 0; i < list.size(); i++) {
//    //        Map<String, Object> map = list.get(i);
//    //        String mobile = (String) map.get("手机号");
//    //        String departmentId = (String) map.get("部门id");
//    //        if (StringUtils.isNotBlank(departmentId)) {
//    //            enterpriseVrbtService.addContentMembers(departmentId, "yrjy" ,mobile);
//    //        }
//    //    }
//    //}
//
//    public static void main(String[] args) throws JsonProcessingException {
//        ObjectMapper objectMapper = new ObjectMapper();
//        String json = "{\"code\":\"000000\",\"info\":\"操作成功\",\"data\":{\"videoRingFunc\":1,\"settingsList\":[{\"ringType\":\"1\",\"startTime\":\"0\",\"endTime\":\"0\",\"ringsList\":[\"600926100601855664\"]},{\"ringType\":\"0\",\"startTime\":\"0\",\"endTime\":\"0\",\"ringsList\":null}]}}";
//        EntVrbtResult entVrbtResult = objectMapper.readValue(json, EntVrbtResult.class);
//        System.out.println(entVrbtResult.getData().at("/settingsList").size() > 0);
//        json = "{\"code\":\"000000\",\"info\":\"操作成功\",\"data\":{\"videoRingFunc\":1,\"settingsList\":[]}}";
//        entVrbtResult = objectMapper.readValue(json, EntVrbtResult.class);
//        System.out.println(entVrbtResult.getData().at("/settingsList").size() > 0);
//    }
}
