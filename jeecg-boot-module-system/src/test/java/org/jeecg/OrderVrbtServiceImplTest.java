package org.jeecg;

import com.eleven.cms.entity.OrderVrbt;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IOrderVrbtService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.SpringContextUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2020/11/16 10:59
 * Desc:Todo
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OrderVrbtServiceImplTest {
    @Autowired
    IOrderVrbtService orderVrbtService;
    @Autowired
    MiguApiService miguApiService;

    private final ObjectMapper mapper =  new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);


    //@Test
    //public void orderByRandomAsync() {
    //    //orderVrbtService.orderByRandomDelay("18215600194", "00210OW");
    //    orderVrbtService.orderByRandomDelay(null, "13438828200", "00210OW");
    //}

    @Test
    public void orderByRandomFromTextFile() throws IOException {
        //orderVrbtService.orderByRandomDelay("18215600194", "00210OW");
//        Files.lines(Paths.get("D:\\workspace-springsource\\jeecg-boot-cms\\to_order_vrbt.txt"))
//             .forEach(line->{
//                 try {
//                     final String[] split = line.split("\\s");
//                     String mobile =split[0];
//                     String channelCode = split[1];
//                     orderVrbt(null, mobile, channelCode);
//                     TimeUnit.SECONDS.sleep(1L);
//                 } catch (Exception e) {
//                     e.printStackTrace();
//                 }
//             });

//        String channelCode = "014X05A";
//        final String isp = MiguApiService.isCentralityChannel(channelCode) ? MobileRegionResult.ISP_DINGYUE : MobileRegionResult.ISP_YIDONG;
//
//        final OrderVrbt vrbt = orderVrbtService.lambdaQuery()
//                .eq(OrderVrbt::getStatus, 1)
//                .eq(OrderVrbt::getIsp, isp)
//                .eq(MobileRegionResult.ISP_DINGYUE.equals(isp),OrderVrbt::getSingerName,channelCode)
//                .last("ORDER BY RAND() LIMIT 1")
//                .one();
//
//
//        final String channelCopyrightId = vrbt.getCopyrightId();
//        log.info("随机订购一首视频彩铃开始=>渠道号:{},版权id:{}", channelCode, channelCopyrightId);

        miguApiService.vrbtToneFreeMonthOrder("15915401656", "014X05A", "699297T4391", MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);

    }

    @Test
    public void send(){
        orderVrbtService.alertRandomVrbtToneFreeOrderFail("13400000000","渠道号","版权id","测试用失败消息");
    }

    private void orderVrbt(String subscribeId,String mobile,String channelCode) throws JsonProcessingException {
        final OrderVrbt vrbt = orderVrbtService.lambdaQuery()
                                   .eq(OrderVrbt::getStatus, 1)
                                   .last("ORDER BY RAND() LIMIT 1")
                                   .one();
        log.info("随机订购一首视频彩铃开始=>订单号:{},手机号:{},渠道号:{},版权id:{},视频彩铃产品id:{}",subscribeId,mobile,channelCode,vrbt.getCopyrightId(),vrbt.getVrbtProductId());
        if(vrbt!=null){
            final RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, vrbt.getCopyrightId(), MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
            String extra = mapper.writeValueAsString(remoteResult);

            log.info("随机订购一首视频彩铃结果=>订单号:{},手机号:{},渠道号:{},版权id:{},视频彩铃产品id:{},结果:{}",subscribeId,mobile,channelCode,vrbt.getCopyrightId(),vrbt.getVrbtProductId(),extra);

            if(remoteResult!=null&&remoteResult.isOK()){
                synchronized (this){
                    orderVrbtService.plusOrderCount(vrbt.getId());
                }
            }
        }
    }
}