package org.jeecg;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.ad.IAdFeedbackService;
import com.eleven.cms.entity.CouponCode;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.AliComplainService;
import com.eleven.cms.remote.JiangsuYidongService;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.JiangsuResponseQueryInfo;
import com.eleven.qycl.service.IDataCollectService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileNotFoundException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class JunboChargeLogServiceImplTest {
    @Autowired
    ISubscribeService subscribeService;

    @Autowired
    IJunboChargeLogService junboChargeLogService;
    
    @Test
    public void addTarck() throws Exception {
        List<Subscribe> subscribeList=subscribeService.lambdaQuery().eq(Subscribe::getMobile, "13438828200")
                .eq(Subscribe::getBizType, BizConstant.BIZ_TYPE_HETU)
                .between(Subscribe::getCreateTime, LocalDateTime.now().minusHours(24),LocalDateTime.now())
                .in(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS,BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED)
                .orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).list();

        boolean rechargeSuccess= junboChargeLogService.lambdaQuery()
                .eq(JunboChargeLog::getAccount, "sadfasdfasfas")
                .eq(JunboChargeLog::getRightsMonth, DateUtil.formatYearMonth(LocalDateTime.now()))
                .in(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS)
                .eq(JunboChargeLog::getServiceId,"sadfasfas")
                .count()>0;
        
    }



}

