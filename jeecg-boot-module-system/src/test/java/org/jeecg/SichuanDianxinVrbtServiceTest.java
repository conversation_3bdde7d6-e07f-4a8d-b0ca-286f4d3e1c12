package org.jeecg;

import com.eleven.cms.remote.SichuanDianxinVrbtService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.SichuanDianxinVrbtResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;

/**
 * @author: cai lei
 * @create: 2021-12-14 15:57
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SichuanDianxinVrbtServiceTest {
    @Autowired
    SichuanDianxinVrbtService sichuanDianxinVrbtService;

    @Test
    public void getCode() throws Exception {
        SichuanDianxinVrbtResult sichuanDianxinVrbtResult = sichuanDianxinVrbtService.getSmsCode("17723327836", BizConstant.OFFERCOMBID_15);
        System.out.println(sichuanDianxinVrbtResult.isOk());
    }

    @Test
    public void code() throws Exception {
        sichuanDianxinVrbtService.order("17723327836","272945","8c55a447-5d81-446f-a24a-6952a592ab88");
    }

}
