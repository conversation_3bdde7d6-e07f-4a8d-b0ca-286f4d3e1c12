package org.jeecg;

import com.eleven.cms.config.YunnanYidongProperties;
import com.eleven.cms.remote.YunnanYidongService;
import com.eleven.cms.service.IXiZangMobileService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.YunnanMobileCheckSaleActiveResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 西藏移动业务测试
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/28 10:39
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class YunnanYidongServiceTest {
    @Autowired
    YunnanYidongService yunnanYidongService;
    @Autowired
    YunnanYidongProperties yunnanYidongProperties;

    @Test
    public void checkSaleActive() throws Exception {
        String channel = BizConstant.BIZ_CHANNEL_YN_BJHY_YR;
//        String channel = "";
        final YunnanMobileCheckSaleActiveResult result = yunnanYidongService.checkSaleActive("18314361446", channel,"");
        System.out.println("result = " + result);
    }
}
