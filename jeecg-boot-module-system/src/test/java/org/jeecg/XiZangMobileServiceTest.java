package org.jeecg;

import com.eleven.cms.es.repository.EsSubscribeRepository;
import com.eleven.cms.service.IXiZangMobileService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 西藏移动业务测试
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/28 10:39
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class XiZangMobileServiceTest {
    @Autowired
    IXiZangMobileService xiZangMobileService;
    @Test
    public void getToken() throws Exception {
        String  resultToken=xiZangMobileService.getToken("13989944007");
        log.info("result:{}",resultToken);
//
//        Result<?>  result=xiZangMobileService.sendSms("13989944007","ca361698-fc79-4655-9bbc-5ba2486dc072","2024072218523235632","XZ_XUANSHI");
//        log.info("result:{}",result);

//
//        Result<?>  resultauthSms=xiZangMobileService.authSms("13889030028", "a7db1b2c-77bf-45a6-b624-d54f312c0151","2024042815023235632","7360");
//        log.info("result:{}",resultauthSms);
//
//
        Result<?>  submitSms=xiZangMobileService.newSubmitOrder("13989944007", "ca361698-fc79-4655-9bbc-5ba2486dc072","2024072218523235632","XZ_XUANSHI","1473");
        log.info("result:{}",submitSms);
    }
}
