package org.jeecg;

import com.eleven.cms.remote.HebeiYidongService;
import com.eleven.cms.remote.JunboLlbService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * @author: cai lei
 * @create: 2024-07-04 11:19
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class JunboLlbServiceTest {

    @Autowired
    JunboLlbService junboLlbService;

    @Test
    public void getSms() {
        String sysOrderId = JunboLlbService.getSysOrderId();
        junboLlbService.getSms("13699402402", sysOrderId, "JUNBO_TEST", "", "", "");
    }

    @Test
    public void handleOrder() {
        String sysOrderId = JunboLlbService.getSysOrderId();
        junboLlbService.handleOrder("13699402402", sysOrderId, "1234", "JUNBO_TEST", "", "", "");
    }


}
