package org.jeecg;

import com.eleven.cms.util.JacksonUtils;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.entity.BackgroundMusic;
import com.eleven.qycl.entity.VoiceGender;
import com.eleven.qycl.service.AliMediaService;
import com.eleven.qycl.service.IQyclRingTemplateService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AliMediaServiceTest {

    public static final String TEXT = "我是我是我吾问无为谓问问,撒大大十大大撒大撒啊倒萨打算";
    public static final ImmutableMap<String, String> WORD_REPLACE = ImmutableMap.of("鸿盛天极", "鸿圣天极");
    public static final String TITLE = "鸿盛天极科技有限公司";
    @Autowired
    AliMediaService aliMediaService;
    @Autowired
    AliMediaProperties aliMediaProperties;
    @Autowired
    IQyclRingTemplateService qyclRingTemplateService;
//    @Autowired
//    IAiRingTemplateService aiRingTemplateService;

//    @Autowired
//    HuoShanAIGCService huoShanAIGCService;
    /**
     * 从NAS放置模板的共享文件夹创建模板,创建成功后会在该文件夹下产生模板信息json文件
     */
    @Test
    public void generateTemplate() {
        final Path templateContainerPath = Paths.get("\\\\192.168.2.200\\鸿盛天极\\视频彩铃-AE模板");
        qyclRingTemplateService.generateTemplate(templateContainerPath);
    }

    @Test
    public void generateAITemplate() {
        final Path templateContainerPath = Paths.get("\\\\192.168.2.200\\鸿盛天极\\视频彩铃-AE模板\\海艺");
        qyclRingTemplateService.generateAITemplate(templateContainerPath);
    }
    @Test
    public void generateAIFaceTemplate() {
        final Path templateContainerPath = Paths.get("\\\\192.168.2.200\\鸿盛天极\\视频彩铃-AE模板\\财神");
        qyclRingTemplateService.generateFaceAITemplate(templateContainerPath);
    }

//    /**
//     * 从NAS放置模板的共享文件夹创建模板,创建成功后会在该文件夹下产生模板信息json文件
//     */
//    @Test
//    public void generateAiTemplate() {
//        final Path templateContainerPath = Paths.get("\\\\192.168.2.200\\鸿盛天极\\AI视频彩铃-AE模板");
//        aiRingTemplateService.generateTemplate(templateContainerPath);
//    }

    @Test
    public void tts() throws InterruptedException {
        VoiceGender voiceGender = VoiceGender.FEMALE;
        final String jobId = aliMediaService.tts(AliMediaProperties.JOB_QUEUE_TAG_QYCL, TEXT, voiceGender, WORD_REPLACE);
        System.out.println("jobId = " + jobId);
//        TimeUnit.SECONDS.sleep(60L);
    }

    @Test
    public void fetchTtsResultAndProduceVideo() throws InterruptedException {
        String ttsJobId = "4c39bc6334024476a2b5fd85e41f3d1c";
        final Pair<String, String> ttsResult = aliMediaService.fetchTtsResult(ttsJobId, ImmutableMap.of());
        System.out.println("ttsResult = " + ttsResult);
        final String audioUrl = ttsResult.getLeft();
        System.out.println("audioUrl = " + audioUrl);
        final String subtitleContent = ttsResult.getRight();
        String bucket = aliMediaProperties.getBucketName();
        String regionId = aliMediaProperties.getRegionId();
//        final ArrayList<String> videoUrls = Lists.newArrayList(
//                "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-1.jpg",
//                "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-2.jpg",
//                "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-3.jpg",
//                "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-4.jpg",
//                "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-5.jpg",
//                "https://" + bucket + ".oss-" + regionId + ".aliyuncs.com/company_hstj/company_hstj-6.jpg");
//        aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_QYCL, videoUrls, BackgroundMusic.valueOf("BGM3").getMusicPath(), "", "", TITLE);
//        TimeUnit.SECONDS.sleep(90L);
    }

    @Test
    public void fetchTemplateInfo() {
        final String clipsParam = aliMediaService.fetchTemplateInfo("bd60ca968d7c4aa68f823c4469961f72");
        System.out.println("clipsParam = " + clipsParam);
    }

    @Test
    public void mergeTemplate() {
        //String clipsParam = "{\"Media4\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"Media3\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/2.jpg\",\"Media2\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/3.jpg\",\"Media1\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/11.jpg\",\"Media0\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/22.jpg\"}";
        Map<String, String> clipsParamMap = new LinkedHashMap<>();
        clipsParamMap.put("pic1", "https://ims-media.oss-cn-beijing.aliyuncs.com/user-media/20241021/0e707bda-5908-4da3-aa33-a078f1035de0.jpg");
        clipsParamMap.put("pic2", "https://ims-media.oss-cn-beijing.aliyuncs.com/hy/2024/10/21/a34a1b87e6a8ac6cbf33dcdaab07a404_mmexport1725009115833.jpg");

        Map<String, String> data = new LinkedHashMap<>();
        data.put("3D-男1", "5e63a3dcc1434b3dbac4c4c75e6aceba");
        data.put("3D卡通女1", "d0dcaaf323af45bdb9d87fa4ec61184f");
        data.put("3D卡通女2-预览", "447e46951e4d4d7caea3ea74a3255a3d");
        data.put("国风女1-预览", "50bb293fd4bb4512a0549b82054ddd8d");
        data.put("国风女2-预览", "50a9d46ae02748939e9147c7b26222a6");

        data.put("国风男1-预览视频", "46bdfb42a59c468b8d8eed1933a5b0ca");
        data.put("国风男2-10.17", "d775c9de01344386b37a553d0fa6357e");
        data.put("皮克斯-女-10.12", "de8b8657f1dc40b9982d72690444f510");
        data.put("皮克斯-男1示例", "cdfc50e7dffb489697e80928855f84bd");
        data.put("皮克斯风格小孩-示例", "646b6d8911894525bad7c70df8254a26");

        data.put("日漫2D一家人示例", "74f3ea1d823d4836afe5426f3ec57e9d");
        data.put("漫画情侣-预览", "4ce05acc5cce4188baeee8d7a6750238");
        data.put("漫画星星女示例视频", "ec070bb33fef44f894c292e1f8d76631");

        data.forEach((k,v) -> {
            System.out.println(k);
            final String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_HYAI,
                    v, JacksonUtils.toJson(clipsParamMap));
//            System.out.println("jobId = " + jobId);
        });
    }
    @Test
    public void mergeVideoTemplate() {
        //String clipsParam = "{\"Media4\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/1.jpg\",\"Media3\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/2.jpg\",\"Media2\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/3.jpg\",\"Media1\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/11.jpg\",\"Media0\":\"https://ims-media.oss-cn-beijing.aliyuncs.com/tmplate-test/22.jpg\"}";
        Map<String, String> clipsParamMap = new LinkedHashMap<>();
        clipsParamMap.put("media", "https://freevod.vip.migu.cn/9ts9%2Bb3iYG39qlcIehgtuHbZ04BFYCCMpHfn%2F3QOUOUISga4kk9kaUYGl8N%2FreRhQeULz2hjjRGIvHUrxB2r9VTy4k8LNa1cZnjQm2Q9nos%3D/3ea8636bd34441e28e6bf1065ad75f22mp4_720x1280.mp4?ec=2&flag=+&F=013015&channelid=08&msisdn=72f52be1-36a4-4c92-afea-3c2a7f1be557");


            final String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_TXAI_FACE,
                    "546192257c43411eb72271c0d1f4ee24", JacksonUtils.toJson(clipsParamMap));
//            System.out.println("jobId = " + jobId);
    }
    @Test
    public void getObjectInputStream() throws Exception {
        String ossUrl = "https://ims-media.oss-cn-beijing.aliyuncs.com/output/20240930/cb2aceb88fc42a6c2e8d5470c51ba2e1.mp4";
        FileUtils.copyInputStreamToFile(aliMediaService.getObjectInputStream(ossUrl), new File("D:\\test.mp4"));
    }

    @Test
    public void produceVideo() throws Exception {

        Pair<String, String> pair = aliMediaService.fetchTtsResult("594136b35bef4b10aa57d97e66c76119", ImmutableMap.of());
        String audioUrl = pair.getLeft();
        String subtitleContent = pair.getRight();
        List<String> mediaList = new ArrayList<>();
        mediaList.add("https://ims-media.oss-cn-beijing.aliyuncs.com/test/video/20241015145854.mp4");
        aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_QYCL, mediaList, "https://ims-media.oss-cn-beijing.aliyuncs.com/bgm/bgm1.mp3", audioUrl, subtitleContent, TITLE);
    }


    @Test
    public void produceVideoForAi() throws Exception {
//        huoShanAIGCService.generateVideo(new String[]{"https://vrbt-yrjy.oss-cn-chengdu.aliyuncs.com/test/ef7c7672-0638-4440-8a7c-5015140c2865.jpg","https://ims-media.oss-cn-beijing.aliyuncs.com/gazi/img_to_img_1308599495_760b2896-7c1b-4c90-babf-ca4fff5b6b52-1308599495.jpg"}, "rxdm");
//        huoShanAIGCService.generateVideo(new String[]{"https://ims-media.oss-cn-beijing.aliyuncs.com/gazi/20241017102618.jpg"}, ImageStyleEnum.IMAGE_STYLE_TYPE_TDF);
//        huoShanAIGCService.generateVideo(new String[]{"https://ims-media.oss-cn-beijing.aliyuncs.com/gazi/20241017102618.jpg"}, ImageStyleEnum.IMAGE_STYLE_TYPE_DMF);
//        huoShanAIGCService.generateVideo(new String[]{"https://ims-media.oss-cn-beijing.aliyuncs.com/gazi/20241017102618.jpg"}, ImageStyleEnum.IMAGE_STYLE_TYPE_QWF);
    }

    public static void main(String[] args) {
        System.out.println("d7ea168d25a24955bd28740e5b98c2f0".length());
    }
}