package org.jeecg;

import com.eleven.cms.remote.BizhanLiantongService;
import com.eleven.cms.remote.SichuanLiantongService;
import com.eleven.cms.remote.XinjiangYidongService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.XinjiangYidongResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: cai lei
 * @create: 2022-10-11 16:49
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SichuanLiantongServiceTest {

    @Autowired
    SichuanLiantongService sichuanLiantongService;

    @Autowired
    BizhanLiantongService bizhanLiantongService;

    @Test
    public void order() throws Exception {
        bizhanLiantongService.getSms("13199942377");
        bizhanLiantongService.smsCode("13199942377","1234","20221011165543815940");
    }
}
