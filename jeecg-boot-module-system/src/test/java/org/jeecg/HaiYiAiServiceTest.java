package org.jeecg;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aivrbt.entity.AiRingVideo;
import com.eleven.cms.aivrbt.service.IAiRingImageService;
import com.eleven.cms.aivrbt.service.IAiRingVideoService;
import com.eleven.cms.aivrbt.service.impl.HaiYiAiService;
import com.eleven.cms.dto.HaiYiFilterTaskCreateDTO;
import com.eleven.cms.dto.HaiYiPicPreLinkQueryDTO;
import com.eleven.cms.dto.HaiYiPicUploadConfirmDTO;
import com.eleven.cms.dto.HyFtpUploadDTO;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.WenshengVideoService;
import com.eleven.cms.service.impl.HaiYiMediaNotifyService;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.VrbtProduct;
import com.eleven.qycl.service.AliMediaService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/10/9 10:24
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class HaiYiAiServiceTest {

    @Autowired
    private HaiYiAiService haiYiAiService;
    @Autowired
    private WenshengVideoService wenshengVideoService;
    @Autowired
    private HaiYiMediaNotifyService haiYiMediaNotifyService;
    @Autowired
    private IAiRingImageService iAiRingImageService;
    @Autowired
    private AliMediaService aliMediaService;
    @Autowired
    private IAiRingVideoService aiRingVideoService;
    @Autowired
    private MiguApiService miguApiService;

    @Test
    public void createFilterTask() {
        HaiYiFilterTaskCreateDTO haiYiFilterTaskCreateDTO = new HaiYiFilterTaskCreateDTO();
        haiYiFilterTaskCreateDTO.setHyTemplateId("22eb7745353a0d165a3953631b6fed7a");
        haiYiFilterTaskCreateDTO.setImageUrl("https://cdn2.seaart.me/static/upload/20241008/d8b8324e-2e96-4745-9f33-fbf9847ddfe9.jpg");
        System.out.println(haiYiAiService.hyCreateFilterTask(haiYiFilterTaskCreateDTO));
    }

    @Test
    public void queryPicPreLink() {
        HaiYiPicPreLinkQueryDTO haiYiPicPreLinkQueryDTO = new HaiYiPicPreLinkQueryDTO();
        haiYiPicPreLinkQueryDTO.setContentType("image/jpg");
        haiYiPicPreLinkQueryDTO.setFileName("test.jpg");
        haiYiPicPreLinkQueryDTO.setFileSize(1234L);
        System.out.println(haiYiAiService.queryPicPreLink(haiYiPicPreLinkQueryDTO));
    }

    //    @Test
    public void testUploadFileContent() throws IOException {
//        haiYiAiService.uploadFileContent("https://upload2.api.haiyiapi.com/upload-image/static/upload/********/d7ef04b9-932b-459e-9d02-6f0cbd5d3bb5_/1.jpg?Expires=**********&GoogleAccessId=cloudstorage-w%40aiart-380003.iam.gserviceaccount.com&Signature=NUB23Di%2BkcEzEFlyA5aNJa4tIlYZF8Uuh9IuNnguuSJwzo8fm19xu0vcoVZbGB7nR5KtJsfYEfPxMr5CLyRYPJWcaHa1wk52PaiCH3447lnxbMvbgWqtDFBcQM3%2BS068A3QzS%2BLQZe2YTujsuIFZS2Kte41L5hJNU9kPqUbCpXgu1Ym2jklVc8SteXMSVelmShQqBQQGkSeOvKkyD77KldKCVPB3mkDfk%2BcFeVCSBpWBCD%2Ftm%2BtNFb5b1M6NeyWx86gblOWmG%2FNP08Cku0YnBDsWTLOah2HwwRms6Zps8V55m3PC681ygZjyBzbZZNXx2BtDtE5aM2scLoCtyef%2BZg%3D%3D",
//                new FileInputStream("C:\\Users\\<USER>\\Desktop\\638ee35ba0708bfeaca64b0da0eaa103.jpg"));

//        String url = "https://upload2.api.haiyiapi.com/upload-image/static/upload/********/671e5006-e728-4f62-9716-978ef75105cc_/1.jpg?Expires=**********&GoogleAccessId=cloudstorage-w%40aiart-380003.iam.gserviceaccount.com&Signature=sQwObQPN7ZF07u8lzM1nX%2BxWTnMqQCkRRJHjVosx5n1mUfwiZcmdDwNrqHP3C15od0dVcUgCqHTCy%2B5IGyVq6ZTZ%2FwpP5k9%2BSISEqgwqcxY0cwjVbJBqwH9lz8%2BzjWSNwkIpy03u4U4MiFqzLjR6RBPJg%2Fyzqa51RgN7xRCsS5kO1J%2FlgESdoVz%2B5pfMzh7TLMLy1h7KLGrXgo%2Bb8YqFWA6Xw4d3h3TDfHjF%2Fb6SacLidRx5qoPG62pc9%2B5nNNNQxxrSWLwZg3eluK%2BY7HOtmBjN%2FN3bhUfN5YeFk%2FX9m0Pzj6lBxxy%2BwmCl5kZgzvEHoe010DmEcDTKG85sdGajhQ%3D%3D";
//        RequestBody picFileBody = OkHttpClientUtils.createInputStreamRequestBody(new FileInputStream("C:\\Users\\<USER>\\Desktop\\638ee35ba0708bfeaca64b0da0eaa103.jpg"));
//        picFileBody.
//        MultipartBody multipartBody = new MultipartBody.Builder()
//                .setType(MultipartBody.FORM)
//                .addFormDataPart("foo", "bar.raw", picFileBody)
//                .build();
//        Request request = new Request.Builder().url(url)
//                //解决java.io.IOException: unexpected end of stream on unknown的问题
//                //.header("Connection","close")
//                .put(picFileBody)
//                .build();
//        try (Response response = OkHttpClientUtils.getIgnoreVerifySSLInstance().newCall(request)
//                .execute()) {
//            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
//            String pageContent = response.body().string();
//            System.out.println("pageContent = " + pageContent);
//            //ObjectMapper mapper = new ObjectMapper();
//            //mapper.enable(SerializationFeature.INDENT_OUTPUT);
//
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }

        OkHttpClient client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
        MediaType mediaType = MediaType.parse("image/jpg");
        RequestBody body = RequestBody.create(mediaType, IOUtils.toByteArray(new FileInputStream("C:\\Users\\<USER>\\Desktop\\638ee35ba0708bfeaca64b0da0eaa103.jpg")));
        Request request = new Request.Builder()
                .url("https://upload2.api.haiyiapi.com/upload-image/static/upload/********/5181a015-6130-48e4-b508-7be691f1b598_/1.jpg?Expires=**********&GoogleAccessId=cloudstorage-w%40aiart-380003.iam.gserviceaccount.com&Signature=PcNDBZluO2TG1obrn95ffU%2FPA6VWiSRH1tGURpmqjLduc5RSBaiGgq3GURuu0heaC0ktygFtyg2o2MZIpXDdEgYeqlj%2BuoLVDvSyxj8s6zasixOA7jAT7sguo9YLfjiaDN0tqCZI5t8mj%2FchjynfLMyZeoZMAHoZYajd6J6ganPDZ88N%2B0u917Y4KiJKsEYaHxhVUBKogo7ym4Co2YZGU14yF5Wb%2FYRxKM4hNs2fVmEyL16%2FlrYL1h7W1SzYt2bXYjD%2FwgZtAOcR4%2FPE9BLFWIk3RXComI6bvRVGiguVcOvauY2tWxakW4pOFLZmoo5yAdE8ZPT1HvwvgvdS5LYfeg%3D%3D")
                .method("PUT", body)
                .addHeader("Content-Type", "image/jpg")
                .build();
        Response response = client.newCall(request).execute();
        System.out.println(response);
        System.out.println();
    }

    public static void main(String[] args) throws IOException {
        new HaiYiAiServiceTest().testUploadFileContent();
    }

    @Test
    public void confirmPicUpload() {
        HaiYiPicUploadConfirmDTO haiYiPicUploadConfirmDTO = new HaiYiPicUploadConfirmDTO();
        haiYiPicUploadConfirmDTO.setFileId("1234");
        System.out.println(haiYiAiService.confirmPicUpload(haiYiPicUploadConfirmDTO));
    }

    @Autowired
    private RedisUtil redisUtil;

    @Test
    public void testHyTaskCallBack() {
        List<String> data = new ArrayList<>();
        data.add("{\"id\":\"cs9nt0le878c73d68l40\",\"type\":\"task.finish\",\"create_at\":1729330856915,\"payload\":{\"task_id\":\"cs9nt0le878c73d68l40\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-19/cs9nt0le878c73d68l40/cffa31f74f2bbb162fa57bcdc7cda4ec17326b8e_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs9i25te878c73d68gb0\",\"type\":\"task.finish\",\"create_at\":1729306948006,\"payload\":{\"task_id\":\"cs9i25te878c73d68gb0\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-19/cs9i25te878c73d68gb0/d1738562ae8ea72d77514582510bd65f1b538e82_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs9i1jte878c73d68ga0\",\"type\":\"task.finish\",\"create_at\":1729306841005,\"payload\":{\"task_id\":\"cs9i1jte878c73d68ga0\",\"category\":1,\"type\":9,\"status_desc\":\"finish\",\"reverse_prompts\":[\"arafed woman in a pink jacket and green dress standing in a field, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng, qifeng lin, xue han, qichao wang\",\"arafed woman in a pink jacket and green dress standing in a field, a picture inspired by Kim Jeong-hui, tumblr, realism, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng\"]}}");
//        data.add("{\"id\":\"cs9hqmle878c73fsmfag\",\"type\":\"task.finish\",\"create_at\":1729306000469,\"payload\":{\"task_id\":\"cs9hqmle878c73fsmfag\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-19/cs9hqmle878c73fsmfag/9614ba2c75ed0498a3e0b6749b0f38790d49aef1_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs9hq1le878c73fsmf9g\",\"type\":\"task.finish\",\"create_at\":1729305868195,\"payload\":{\"task_id\":\"cs9hq1le878c73fsmf9g\",\"category\":1,\"type\":9,\"status_desc\":\"finish\",\"reverse_prompts\":[\"arafed woman in a pink jacket and green dress standing in a field, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng, qifeng lin, xue han, qichao wang\",\"arafed woman in a pink jacket and green dress standing in a field, a picture inspired by Kim Jeong-hui, tumblr, realism, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng\"]}}");
//        data.add("{\"id\":\"cs9hl7le878c73d68g80\",\"type\":\"task.finish\",\"create_at\":1729305284696,\"payload\":{\"task_id\":\"cs9hl7le878c73d68g80\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-19/cs9hl7le878c73d68g80/4fcee013de04787bee5116e4be4aeb89c67a2a2a_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs9hkcde878c73fsmf8g\",\"type\":\"task.finish\",\"create_at\":1729305142649,\"payload\":{\"task_id\":\"cs9hkcde878c73fsmf8g\",\"category\":1,\"type\":9,\"status_desc\":\"finish\",\"reverse_prompts\":[\"arafed woman in a pink jacket and green dress standing in a field, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng, qifeng lin, xue han, qichao wang\",\"arafed woman in a pink jacket and green dress standing in a field, a picture inspired by Kim Jeong-hui, tumblr, realism, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng\"]}}");
//        data.add("{\"id\":\"cs9hbnde878c73fsmf5g\",\"type\":\"task.finish\",\"create_at\":1729304085668,\"payload\":{\"task_id\":\"cs9hbnde878c73fsmf5g\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-19/cs9hbnde878c73fsmf5g/d110e2ab51104a53b251c6ab7eafc1b41718478c_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs9h7bte878c73d68g10\",\"type\":\"task.finish\",\"create_at\":1729303477211,\"payload\":{\"task_id\":\"cs9h7bte878c73d68g10\",\"category\":1,\"type\":9,\"status_desc\":\"finish\",\"reverse_prompts\":[\"arafed woman in a pink jacket and green dress standing in a field, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng, qifeng lin, xue han, qichao wang\",\"arafed woman in a pink jacket and green dress standing in a field, a picture inspired by Kim Jeong-hui, tumblr, realism, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng\"]}}");
//        data.add("{\"id\":\"cs9h165e878c73d68fs0\",\"type\":\"task.finish\",\"create_at\":1729302733011,\"payload\":{\"task_id\":\"cs9h165e878c73d68fs0\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-19/cs9h165e878c73d68fs0/3e3fdd4f88d872fec4460b6e211198554c2ab424_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs9h04le878c73fsmeu0\",\"type\":\"task.finish\",\"create_at\":1729302551128,\"payload\":{\"task_id\":\"cs9h04le878c73fsmeu0\",\"category\":1,\"type\":9,\"status_desc\":\"finish\",\"reverse_prompts\":[\"arafed woman in a pink jacket and green dress standing in a field, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng, qifeng lin, xue han, qichao wang\",\"arafed woman in a pink jacket and green dress standing in a field, a picture inspired by Kim Jeong-hui, tumblr, realism, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng\"]}}");
//        data.add("{\"id\":\"cs91lhde878c73fsm3ng\",\"type\":\"task.finish\",\"create_at\":1729239796057,\"payload\":{\"task_id\":\"cs91lhde878c73fsm3ng\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-18/cs91lhde878c73fsm3ng/61b0112e36cea6170a2d2f318d8e2a24b284f703_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs91hnde878c73fsm3lg\",\"type\":\"task.finish\",\"create_at\":1729239265918,\"payload\":{\"task_id\":\"cs91hnde878c73fsm3lg\",\"category\":1,\"type\":9,\"status_desc\":\"finish\",\"reverse_prompts\":[\"arafed woman in a pink jacket and green dress standing in a field, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng, qifeng lin, xue han, qichao wang\",\"arafed woman in a pink jacket and green dress standing in a field, a picture inspired by Kim Jeong-hui, tumblr, realism, xintong chen, jinyoung shin, wenfei ye, bae suzy, xision wu, very very low quality picture, album art, standing in floweeld, chengyou liu, jaeyeon nam, zmonzheng\"]}}");
//        data.add("{\"id\":\"cs910kte878c73chh6e0\",\"type\":\"task.finish\",\"create_at\":1729237118789,\"payload\":{\"task_id\":\"cs910kte878c73chh6e0\",\"category\":1,\"type\":20,\"status_desc\":\"finish\",\"images\":[{\"url\":\"https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-18/cs910kte878c73chh6e0/f82c2d2f8d99c2effec78e2a0acb868322c6c9e4_high.webp\",\"width\":768,\"height\":1024,\"nsfw\":2}]}}");
//        data.add("{\"id\":\"cs90rf5e878c73dpilj0\",\"type\":\"task.finish\",\"create_at\":1729236418356,\"payload\":{\"task_id\":\"cs90rf5e878c73dpilj0\",\"category\":1,\"type\":9,\"status_desc\":\"finish\",\"images\":[]}}");

        for (String json : data) {
            JsonNode jsonNode = JacksonUtils.readTree(json);
//            for (int i = 0; i < 10; i++) {
            haiYiMediaNotifyService.hyTaskCallBack(jsonNode);
//            }
        }
    }

    @Test
    public void testPicToText() {
        System.out.println(haiYiAiService.imgToText("https://ims-media.oss-cn-beijing.aliyuncs.com/user-media/20241017/f32b6c1d-9bc7-4358-a9f7-844c0d95d1fc.jpg"));
    }

    @Test
    public void testUploadToOSS() {
        List<File> files = new ArrayList<>();
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\动态封面(1)\\动态封面\\3D卡通女2-动态封面.webp"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\动态封面(1)\\动态封面\\国风男1-动态封面.webp"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\动态封面(1)\\动态封面\\国风男2-动态封面.webp"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\动态封面(1)\\动态封面\\国风女1-动态封面.webp"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\动态封面(1)\\动态封面\\国风女2-动态封面.webp"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\动态封面(1)\\动态封面\\漫画星星动态封面.webp"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\动态封面(1)\\动态封面\\日漫一家人动态封面.webp"));

//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\3D卡通1.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\3D卡通2.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\国风男1.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\国风男2.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\国风女1.jpg"));
//
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\国风女2.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\漫画情侣.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\漫画星星女封面图.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\皮克斯男1封面图.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\皮克斯女.jpg"));
//
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\皮克斯女2封面图.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\皮克斯小孩封面图.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\日漫2D一家人封面图.jpg"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\静态封面\\静态封面\\3D女1.png"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\db9f8c3a273178cd6159c05659f7704.png"));
//        files.add(new File("C:\\Users\\<USER>\\Desktop\\map\\20241114-104717.png"));
//        files.add(new File("C:\\Users\\<USER>\\Downloads\\视频换脸-国风女汉服1\\国风女汉服1动态封面.webp"));
//        files.add(new File("C:\\Users\\<USER>\\Downloads\\视频换脸-国风女汉服1\\国风女汉服1预览视频.mp4"));
//        files.add(new File("C:\\\\Users\\\\<USER>\\\\Downloads\\\\飞书20241121-193332.webp"));
        traverseDirectory(new File("C:\\Users\\<USER>\\Desktop\\312"), files);
        for (File file : files) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
            String aliyunFileUrl = "";
            try (FileInputStream in = new FileInputStream(file)) {
                aliyunFileUrl = aliMediaService.putObjectFile("hy" + simpleDateFormat.format(new Date()) + IdWorker.get32UUID() + "_" + file.getName(), in);
            } catch (Exception e) {
                log.error("图片上传阿里云异常", e);
                throw new JeecgBootException(e.getMessage());
            }
            log.info("{}：  {}", file.getName(), aliyunFileUrl);
        }
    }
    public static void traverseDirectory(File directory, List<File> files) {
        // 如果当前路径不是目录，直接返回
        if (!directory.isDirectory()) {
            return;
        }

        // 获取目录下的所有文件和子目录
        File[] entries = directory.listFiles();

        // 如果目录为空，直接返回
        if (entries == null) {
            return;
        }

        // 遍历目录下的每个条目
        for (File entry : entries) {
            if (entry.isFile()) {
                // 如果是文件，添加到列表中
                files.add(entry);
            } else if (entry.isDirectory()) {
                // 如果是目录，递归调用
                traverseDirectory(entry, files);
            }
        }
    }
    @Test
    public void test() {
        String url = "https://image.cdn.centcloud.seaart.me/static/biz/589367b6edea11df5a2256339d45c014/2024-10-19/cs9hqmle878c73fsmfag/9614ba2c75ed0498a3e0b6749b0f38790d49aef1_high.webp";
        haiYiAiService.getInputStream(url);
    }

    @Test
    public void fetchMusicInfo() {
        // 动漫
        List<String> data1 = Arrays.asList(
                "600050TA9S7",
                "600050TA9S9",
                "600050TA9SC",
                "600050TA9SD"
        );
        test111(data1, "1003");
//        // 动物
//        List<String> data2 = Arrays.asList(
//                "600050T0381",
//                "600050T0380",
//                "600050T0383",
//                "600050T0382",
//                "600050T0385",
//                "600050T0384",
//                "600050T0376",
//                "600050T0375",
//                "600050T0378",
//                "600050T0377",
//                "600050T0379",
//                "600050T0390",
//                "600050T0392",
//                "600050T0391",
//                "600050T0394",
//                "600050T0393",
//                "600050T0396",
//                "600050T0395",
//                "600050T0387",
//                "600050T0386",
//                "600050T0389",
//                "600050T0388",
//                "600050T0374",
//                "600050T0398",
//                "600050T0397",
//                "600050T0399",
//                "600050T0411",
//                "600050T0410",
//                "600050T0413",
//                "600050T0412",
//                "600050T0415",
//                "600050T0414",
//                "600050T0406",
//                "600050T0405",
//                "600050T0408",
//                "600050T0407",
//                "600050T0409",
//                "600050T0400",
//                "600050T0402",
//                "600050T0401",
//                "600050T0404",
//                "600050T0403"
//        );
//        test111(data2, "1849641143357759490");
//        // 风景
//        List<String> data3 = Arrays.asList(
//                "600050T0361",
//                "600050T0360",
//                "600050T0363",
//                "600050T0362",
//                "600050T0354",
//                "600050T0353",
//                "600050T0356",
//                "600050T0355",
//                "600050T0358",
//                "600050T0357",
//                "600050T0359",
//                "600050T0370",
//                "600050T0372",
//                "600050T0371",
//                "600050T0373",
//                "600050T0365",
//                "600050T0364",
//                "600050T0367",
//                "600050T0366",
//                "600050T0369",
//                "600050T0368",
//                "600050T0341",
//                "600050T0340",
//                "600050T0339",
//                "600050T0332",
//                "600050T0331",
//                "600050T0334",
//                "600050T0333",
//                "600050T0336",
//                "600050T0335",
//                "600050T0338",
//                "600050T0337",
//                "600050T0350",
//                "600050T0352",
//                "600050T0351",
//                "600050T0343",
//                "600050T0342",
//                "600050T0345",
//                "600050T0344",
//                "600050T0347",
//                "600050T0346",
//                "600050T0349",
//                "600050T0348",
//                "600050T0330",
//                "600050T0329",
//                "600050T0328",
//                "600050T0325",
//                "600050T0327",
//                "600050T0326"
//
//        );
//        test111(data3, "1002");
//        // 国风
//        List<String> data4 = Arrays.asList(
//                "600050T0307",
//                "600050T0306",
//                "600050T0309",
//                "600050T0308",
//                "600050T0301",
//                "600050T0300",
//                "600050T0303",
//                "600050T0302",
//                "600050T0305",
//                "600050T0304",
//                "600050T0318",
//                "600050T0317",
//                "600050T0319",
//                "600050T0310",
//                "600050T0312",
//                "600050T0311",
//                "600050T0314",
//                "600050T0313",
//                "600050T0316",
//                "600050T0315",
//                "600050T0321",
//                "600050T0320",
//                "600050T0323",
//                "600050T0322",
//                "600050T0324",
//                "600050T0299",
//                "600050T0298",
//                "600050T0286",
//                "600050T0285",
//                "600050T0291",
//                "600050T0290",
//                "600050T0293",
//                "600050T0292",
//                "600050T0295",
//                "600050T0294",
//                "600050T0297",
//                "600050T0296",
//                "600050T0288",
//                "600050T0287",
//                "600050T0289"
//
//
//        );
//        test111(data4, "1004");
//        // 节日
//        List<String> data5 = Arrays.asList(
//                "600050T0260",
//                "600050T0262",
//                "600050T0261",
//                "600050T0264",
//                "600050T0263",
//                "600050T0255",
//                "600050T0254",
//                "600050T0257",
//                "600050T0256",
//                "600050T0259",
//                "600050T0258",
//                "600050T0271",
//                "600050T0270",
//                "600050T0273",
//                "600050T0272",
//                "600050T0275",
//                "600050T0274",
//                "600050T0266",
//                "600050T0265",
//                "600050T0268",
//                "600050T0267",
//                "600050T0269",
//                "600050T0242",
//                "600050T0251",
//                "600050T0250",
//                "600050T0253",
//                "600050T0252",
//                "600050T0244",
//                "600050T0243",
//                "600050T0246",
//                "600050T0245",
//                "600050T0248",
//                "600050T0247",
//                "600050T0249",
//                "600050T0280",
//                "600050T0282",
//                "600050T0281",
//                "600050T0284",
//                "600050T0283",
//                "600050T0277",
//                "600050T0276",
//                "600050T0279",
//                "600050T0278"
//
//
//
//        );
//        test111(data5, "1006");
//        // 节日
//        List<String> data6 = Arrays.asList(
//                "600050T0240",
//                "600050T0241",
//                "600050T0233",
//                "600050T0232",
//                "600050T0235",
//                "600050T0234",
//                "600050T0237",
//                "600050T0236",
//                "600050T0239",
//                "600050T0238",
//                "600050T0220",
//                "600050T0219",
//                "600050T0218",
//                "600050T0211",
//                "600050T0210",
//                "600050T0213",
//                "600050T0212",
//                "600050T0214",
//                "600050T0217",
//                "600050T0216",
//                "600050T0231",
//                "600050T0230",
//                "600050T0229",
//                "600050T0222",
//                "600050T0221",
//                "600050T0224",
//                "600050T0223",
//                "600050T0226",
//                "600050T0225",
//                "600050T0228",
//                "600050T0227",
//                "600050T0208",
//                "600050T0207",
//                "600050T0209",
//                "600050T0200",
//                "600050T0202",
//                "600050T0201",
//                "600050T0204",
//                "600050T0206",
//                "600050T0205"
//
//
//
//
//        );
//        test111(data6, "1007");
    }

    public void test111(List<String> data, String columnId) {

        List<AiRingVideo> result = new ArrayList<>();
        data.forEach(e -> {
            VrbtProduct r = miguApiService.fetchVrbtProduct(e);

            AiRingVideo aiRingVideo = new AiRingVideo();
            aiRingVideo.setCopyrightId(r.getCopyrightId());
            aiRingVideo.setVrbtProductId(r.getVrbtProductId());
            aiRingVideo.setRingName(r.getMusicName().replace("AI一语成片-", ""));
            aiRingVideo.setRingPicUrl(r.getVrbtImg());
            aiRingVideo.setRingUrl(r.getVrbtVideo());
            aiRingVideo.setColumnId(columnId);
            result.add(aiRingVideo);
        });

        aiRingVideoService.saveBatch(result);
    }

    @Test
    public void testGetToken() {
        System.out.println(haiYiAiService.getToken());
    }

    @Test
    public void testSetRing() {
        HyFtpUploadDTO hyFtpUploadDTO = new HyFtpUploadDTO();
        hyFtpUploadDTO.setId("123");
        hyFtpUploadDTO.setMobile("456");
        hyFtpUploadDTO.setMvName("789");
        hyFtpUploadDTO.setFilePath("369");
        haiYiAiService.setRing(hyFtpUploadDTO);
    }

    @Test
    public void testSetRingYYCP() {
        HyFtpUploadDTO hyFtpUploadDTO = new HyFtpUploadDTO();
        hyFtpUploadDTO.setId("1825416202838302721");
        hyFtpUploadDTO.setMobile("456");
        hyFtpUploadDTO.setMvName("789");
        hyFtpUploadDTO.setFilePath("369");
        wenshengVideoService.setRing(hyFtpUploadDTO);
    }

}
