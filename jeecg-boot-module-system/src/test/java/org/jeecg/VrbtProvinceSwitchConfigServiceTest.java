package org.jeecg;

import com.eleven.cms.entity.Music;
import com.eleven.cms.remote.ChongqingYidongVrbtService;
import com.eleven.cms.remote.HebeiYidongVrbtService;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IMusicService;
import com.eleven.cms.service.IVrbtProvinceSwitchConfigService;
import com.eleven.cms.vo.MusicVo;
import org.jeecg.common.util.RedisUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class VrbtProvinceSwitchConfigServiceTest {

    @Autowired
    IVrbtProvinceSwitchConfigService vrbtProvinceSwitchConfigService;

    @Autowired
    HebeiYidongVrbtService hebeiYidongVrbtService;
    @Autowired
    ChongqingYidongVrbtService chongqingYidongVrbtService;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    IMusicService musicService;

    @Test
    public void findByTitle() {
        String miguChannel = vrbtProvinceSwitchConfigService.vrbtMiguChannelSwitch(MiguApiService.CH_DYB_DEFAULT,"江苏");
        System.out.println(miguChannel);
        //miguChannel = vrbtProvinceSwitchConfigService.vrbtMiguChannelSwitch("山西");
        //System.out.println(miguChannel);
        //miguChannel = vrbtProvinceSwitchConfigService.vrbtMiguChannelSwitch("四川");
        //System.out.println(miguChannel);
    }

    //@Test
    //public void getSmsCode() throws Exception {
    //    chongqingYidongVrbtService.getSms("13983241634");
    //}
    //@Test
    //public void sendSmsCode(){
    //    chongqingYidongVrbtService.smsCode("13983241634","471343","20220308113532000001");
    //
    //
    //}




}