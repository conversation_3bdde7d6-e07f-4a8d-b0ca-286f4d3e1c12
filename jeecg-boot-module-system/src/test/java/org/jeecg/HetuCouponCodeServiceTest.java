package org.jeecg;

import com.eleven.cms.entity.HetuCouponCode;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MiGuHuYuQueryMember;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.*;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class HetuCouponCodeServiceTest {

    @Autowired
    IHetuCouponCodeService hetuCouponCodeService;
    @Autowired
    private ISubscribeService subscribeService;
    @Test
    public void isMember(){
        boolean isMember=hetuCouponCodeService.isMember("***********","HYQY_YR");
        log.info("isMember:{}",isMember);
//
//        final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean("miGuHuYuRightsServiceImpl", IBusinessRightsSubService.class);
//        FebsResponse febsResponse=  businessRightsSubService.memberVerify("***********","MiGuHuYu");
//        log.info("febsResponse:{}",febsResponse);
    }
//    @Test
//    public void queryMember(){
//        ObjectNode node = mapper.createObjectNode();
//        List<Map<String,String>> mobileList=Lists.newArrayList();
//        try {
//            File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
//            if(file.exists()){
//                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
//                if(!stringList.isEmpty()){
//                    stringList.forEach(mobile->{
//                        Map<String,String> map=Maps.newHashMap();
//                        map.put("手机号",mobile.trim());
//                        MiGuHuYuQueryMember HYQY_YR=hetuCouponCodeService.queryMember(mobile.trim(),"HYQY_YR");
//                        map.put("77071订购状态",String.valueOf(HYQY_YR.getResultData().getSubStatus()));
//                        map.put("77071绑定状态",String.valueOf(HYQY_YR.getResultData().getAutoStatus()));
//
//
//                        MiGuHuYuQueryMember HYQY_YRVS=hetuCouponCodeService.queryMember(mobile,"HYQY_YRVS");
//                        map.put("25007订购状态",String.valueOf(HYQY_YRVS.getResultData().getSubStatus()));
//                        map.put("25007绑定状态",String.valueOf(HYQY_YRVS.getResultData().getAutoStatus()));
//                        mobileList.add(map);
//                    });
//
//
//                }
//            }
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        node.putPOJO("新建文本文档",mobileList);
//        log.info("手机号列表:{}",node);
//    }
    @Test
    public void countDistinct() {
        log.debug("开始查询");
        final String codeType = "hetu_6";
        HetuCouponCode hetuCouponCode = hetuCouponCodeService.lambdaQuery()
                .select(HetuCouponCode::getId, HetuCouponCode::getCouponCode)
                /*.gt(HetuCouponCode::getInvalidTime,new Date())*/
                .eq(HetuCouponCode::getRightsId, codeType)
                .eq(HetuCouponCode::getStatus, BizConstant.NOT_USE)
                /*.orderByAsc(HetuCouponCode::getCreateTime)*/
                .last(BizConstant.SQL_LIMIT_ONE)
                .one();
        log.debug("查询结果：{}",hetuCouponCode);
    }
    @Autowired
    MobileRegionService mobileRegionService;
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 河图退订用户
     */
    @Test
    public void hetuMobileDecode(){
        List<String> stringList = null;
        try {
            stringList = FileUtils.readLines(new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ObjectNode node = mapper.createObjectNode();
        List<Map<String,String>> mobileList=Lists.newArrayList();
        stringList.forEach(userId->{
            String mobile=Long.toString(Long.valueOf(userId.trim()),13);
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            Map<String,String> map=Maps.newHashMap();
            map.put("USER_ID",userId);
            map.put("手机号",mobile);
            map.put("省份",mobileRegionResult.getProvince());
            map.put("城市",mobileRegionResult.getCity());
            mobileList.add(map);
        });
        node.putPOJO("新建文本文档",mobileList);
        log.info("手机号列表:{}",node);
    }
    @Qualifier("threadPoolExecutor")
    ThreadPoolExecutor executor;
    /**
     * 多线程河图退订用户
     */
    @Test
    public void hetuMobileDecodeExecutor() {
        List<Map<String,String>> mobileListAll=Lists.newArrayList();

        final CompletableFuture<Void> zero = CompletableFuture.runAsync(() -> {
            try {
                File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
                if(file.exists()){
                    List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                    if(!stringList.isEmpty()){
                        List<Map<String,String>> mobileList=Lists.newArrayList();
                        stringList.forEach(userId->{
                            String mobile=Long.toString(Long.valueOf(userId.trim()),13);
                            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                            Map<String,String> map=Maps.newHashMap();
                            map.put("USER_ID",userId);
                            map.put("手机号",mobile);
                            map.put("省份",mobileRegionResult.getProvince());
                            map.put("城市",mobileRegionResult.getCity());
                            mobileList.add(map);
                        });
                        mobileListAll.addAll(mobileList);
                    }
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }, executor);

        final CompletableFuture<Void> one = CompletableFuture.runAsync(() -> {
            try {
                File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档1.txt");
                if(file.exists()){
                    List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                    if(!stringList.isEmpty()){
                        List<Map<String,String>> mobileList=Lists.newArrayList();
                        stringList.forEach(userId->{
                            String mobile=Long.toString(Long.valueOf(userId.trim()),13);
                            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                            Map<String,String> map=Maps.newHashMap();
                            map.put("USER_ID",userId);
                            map.put("手机号",mobile);
                            map.put("省份",mobileRegionResult.getProvince());
                            map.put("城市",mobileRegionResult.getCity());
                            mobileList.add(map);
                        });
                        mobileListAll.addAll(mobileList);
                    }
                }


            } catch (IOException e) {
                e.printStackTrace();
            }
        }, executor);





        final CompletableFuture<Void> two = CompletableFuture.runAsync(() -> {
            try {
                File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档2.txt");
                if(file.exists()){
                    List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                    if(!stringList.isEmpty()){
                        List<Map<String,String>> mobileList=Lists.newArrayList();
                        stringList.forEach(userId->{
                            String mobile=Long.toString(Long.valueOf(userId.trim()),13);
                            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                            Map<String,String> map=Maps.newHashMap();
                            map.put("USER_ID",userId);
                            map.put("手机号",mobile);
                            map.put("省份",mobileRegionResult.getProvince());
                            map.put("城市",mobileRegionResult.getCity());
                            mobileList.add(map);
                        });
                        mobileListAll.addAll(mobileList);
                    }
                }


            } catch (IOException e) {
                e.printStackTrace();
            }
        }, executor);




        final CompletableFuture<Void> three = CompletableFuture.runAsync(() -> {
            try {
                File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档3.txt");
                if(file.exists()){
                    List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                    if(!stringList.isEmpty()){
                        List<Map<String,String>> mobileList=Lists.newArrayList();
                        stringList.forEach(userId->{
                            String mobile=Long.toString(Long.valueOf(userId.trim()),13);
                            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                            Map<String,String> map=Maps.newHashMap();
                            map.put("USER_ID",userId);
                            map.put("手机号",mobile);
                            map.put("省份",mobileRegionResult.getProvince());
                            map.put("城市",mobileRegionResult.getCity());
                            mobileList.add(map);
                        });
                        mobileListAll.addAll(mobileList);
                    }
                }


            } catch (IOException e) {
                e.printStackTrace();
            }
        }, executor);




        final CompletableFuture<Void> four = CompletableFuture.runAsync(() -> {
            try {
                File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档4.txt");
                if(file.exists()){
                    List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                    if(!stringList.isEmpty()){
                        List<Map<String,String>> mobileList=Lists.newArrayList();
                        stringList.forEach(userId->{
                            String mobile=Long.toString(Long.valueOf(userId.trim()),13);
                            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                            Map<String,String> map=Maps.newHashMap();
                            map.put("USER_ID",userId);
                            map.put("手机号",mobile);
                            map.put("省份",mobileRegionResult.getProvince());
                            map.put("城市",mobileRegionResult.getCity());
                            mobileList.add(map);
                        });
                        mobileListAll.addAll(mobileList);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }, executor);


        final CompletableFuture<Void> five = CompletableFuture.runAsync(() -> {
            try {
                File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档5.txt");
                if(file.exists()){
                    List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                    if(!stringList.isEmpty()){
                        List<Map<String,String>> mobileList=Lists.newArrayList();
                        stringList.forEach(userId->{
                            String mobile=Long.toString(Long.valueOf(userId.trim()),13);
                            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                            Map<String,String> map=Maps.newHashMap();
                            map.put("USER_ID",userId);
                            map.put("手机号",mobile);
                            map.put("省份",mobileRegionResult.getProvince());
                            map.put("城市",mobileRegionResult.getCity());
                            mobileList.add(map);
                        });
                        mobileListAll.addAll(mobileList);
                    }
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }, executor);
        try {
            CompletableFuture.allOf(zero,one,two,three,four,five).get();
            ObjectNode node = mapper.createObjectNode();
            node.putPOJO("新建文本文档",mobileListAll);
            log.info("手机号列表:{}",node);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }

    }
        public static List<String> getContent(MultipartFile file){
        BufferedReader br = null;
        List<String> strings = Lists.newArrayList();
        String line = null;
        try {
            //根据文件路径创建缓冲输入流
            InputStream inputStream = file.getInputStream();
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
            br = new BufferedReader(inputStreamReader);
            String str = "";

            //循环读取文件的每一行，对需要修改的行进行修改，放入缓冲对象中
            while ((line = br.readLine()) != null) {
                //设置正则将多余空格都转为一个空格
                str = line;
                strings.add(str);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if (br != null) {// 关闭流
                try {
                    br.close();
                } catch (IOException e) {
                    br = null;
                }
            }
        }
        return strings;
    }
   @Autowired
   private IEsDataService esDataService;
    @Test
    public void  updateSubscribeVerfyStatusAndDailyById(){
        LocalDate fetchDay = LocalDate.of(2024,6,14);
        LocalDate endDay = LocalDate.of(2024, 6, 6);
        while (!fetchDay.isBefore(endDay)) {
                LocalDateTime start = fetchDay.atTime(LocalTime.MIN);
                LocalDateTime end = fetchDay.equals(LocalDate.of(2024,6,14)) ? fetchDay.atTime(LocalTime.of(23,59)) : fetchDay.atTime(LocalTime.MAX);
                List<Subscribe> subscribes=subscribeService.lambdaQuery().select(Subscribe::getId,Subscribe::getMobile).between(Subscribe::getCreateTime,start,end).eq(Subscribe::getChannel,"HYQY_YR").eq(Subscribe::getStatus,1).and(i -> i.isNull(Subscribe::getVerifyStatus).or().eq(Subscribe::getVerifyStatus, SUBSCRIBE_MONTH_VERIFY_INIT)).list();
                subscribes.forEach(sub->{
                    boolean isMember=hetuCouponCodeService.isMember(sub.getMobile(),"HYQY_YR");
                    Integer status = isMember? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                    subscribeService.lambdaUpdate().eq(Subscribe::getId,sub.getId()).set(Subscribe::getVerifyStatus,status).set(Subscribe::getVerifyStatusDaily,status).update();
                    esDataService.updateSubscribeVerfyStatusDailyById(sub.getId(),status);
                });
                fetchDay = fetchDay.minusDays(1L);
        }
    }
    @Test
    public void createHeTuBaoBei(){
        Subscribe subscribe=new Subscribe();
        subscribe.setMobile("18218011857");
        subscribe.setBizType("COMIC");
        subscribe.setChannel("COMIC");
        subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
        subscribe.setSubChannel("baobei");
        subscribe.setOpenTime(new Date());
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            String isp = mobileRegionResult.getOperator();
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            subscribe.setIsp(isp);
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
    }
    @Test
    public void updateSubVerfyStatusById(){
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                if(!stringList.isEmpty()){
                    stringList.forEach(mobile->{
                        Subscribe sub=subscribeService.lambdaQuery().select(Subscribe::getId,Subscribe::getMobile).eq(Subscribe::getMobile,mobile.trim()).eq(Subscribe::getChannel,"HYQY_YR_20").eq(Subscribe::getStatus,1).eq(Subscribe::getVerifyStatus, SUBSCRIBE_MONTH_VERIFY_NONE).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
                        if(sub!=null){
                            boolean isMember=hetuCouponCodeService.isMember(sub.getMobile(),"HYQY_YR_20");
                            Integer status = isMember? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                            subscribeService.lambdaUpdate().eq(Subscribe::getId,sub.getId()).set(Subscribe::getVerifyStatus,status).update();
                            esDataService.updateSubVerfyStatusById(sub.getId(),status);
                        }
                    });


                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void queryCode(){
        ObjectNode node = mapper.createObjectNode();
        List<Map<String,String>> mobileList=Lists.newArrayList();
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                if(!stringList.isEmpty()){
                    stringList.forEach(mobile->{
                        Map<String,String> map=Maps.newHashMap();
                        HetuCouponCode code=hetuCouponCodeService.lambdaQuery().select(HetuCouponCode::getCouponCode).eq(HetuCouponCode::getMobile,mobile.trim()).last("limit 1").one();
                        map.put("手机号",mobile.trim());
                        map.put("发送状态",code!=null?"已发放":"未发放");
                        map.put("激活码",code!=null?code.getCouponCode():"未发放");
                        mobileList.add(map);
                    });


                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        node.putPOJO("新建文本文档",mobileList);
        log.info("手机号列表:{}",node);
    }

    @Autowired
    private ICouponCodeService couponCodeService;

//    /**
//     * 生成券码
//     */
//    @Test
//    public void  saveCodeTime(){
//        try {
//            String content = couponCodeService.saveCode("HYQY_JUNBO","2024-07-01 00:00:00","2024-07-30 00:00:00");
//            log.info("content:{}",content);
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//
//    }


    @Test
    public void code(){
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);

                List<String> codeList =Lists.newArrayList();
                if(!stringList.isEmpty()){
                    stringList.forEach(code->{
                        codeList.add(code.trim());
                    });
                }
                log.info("激活码列表:{}", String.join(",", codeList.stream().distinct().collect(Collectors.toList())));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void fetchCouponFromCache(){
        System.out.println(couponCodeService.fetchCouponFromCache("hetu_6"));
        System.out.println(couponCodeService.fetchCouponFromCache("cfdtw_5","cfdtw"));
    }

    @Autowired
    IJunboChargeLogService junboChargeLogService;
    //河图券码补发
    @Test
    public void hetuCodeResume(){
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                if(!stringList.isEmpty()){
                    stringList.forEach(mobile->{
                        JunboChargeLog junboChargeLog=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getMobile,mobile.trim()).eq(JunboChargeLog::getRightsMonth,"202407").eq(JunboChargeLog::getServiceId,"MIGUHUYU100002").isNotNull(JunboChargeLog::getCouponCode).orderByDesc(JunboChargeLog::getCreateTime).last("limit 1").one();
                        if(junboChargeLog!=null){
                            couponCodeService.hetuCodeResume(junboChargeLog);
                        }
                    });
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
//    @Autowired
//    IWxpayService wxpayService;
//    @Test
//    public void weiXinActivateCode(){
//        wxpayService.weiXinActivateCode();
//    }
//    @Test
//    public void sendWeiXinCode(){
//        wxpayService.sendWeiXinCode("osVrzwochhhbRiBnLk-FIAiGqGkU","202408080951");
//    }
}
