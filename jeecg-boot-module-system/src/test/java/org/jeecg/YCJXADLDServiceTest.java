package org.jeecg;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.impl.YCJXADLDService;
import com.eleven.cms.util.AdPlatformUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class YCJXADLDServiceTest {

    @Autowired
    private YCJXADLDService ycjxadldService;

    private String mobile = "18879424870";

    /**
     * 发送短信
     */
    @Test
    public void testSendSms() {
        Subscribe subscribe = new Subscribe();
        subscribe.setChannel("test");

        subscribe.setMobile(mobile);
        subscribe.setIp("127.0.0.1");
        subscribe.setReferer("com.test.test");
        subscribe.setReturnUrl("http:127.0.0.1:8080");
        subscribe.setUserId("127.0.0.1");
        ycjxadldService.sendSms(subscribe);
    }

    /**
     * 提交订购
     */
    @Test
    public void testSubmitOrder() {
        Subscribe subscribe = new Subscribe();
        subscribe.setChannel("test");

        subscribe.setMobile(mobile);
        subscribe.setSmsCode("1231");
        ycjxadldService.submitOrder(subscribe);
    }

    /**
     * 订购结果通知
     */
    @Test
    public void testSubResultNotify() {
//        ycjxadldService.subResultNotify();
        System.out.println(AdPlatformUtils.getAdPlatform("JXYD_ADLD_YR"));
    }
}
