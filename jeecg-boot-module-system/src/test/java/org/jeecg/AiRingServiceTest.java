package org.jeecg;

import com.eleven.cms.aivrbt.controller.AiVrbtApiController;
import com.eleven.cms.aivrbt.dto.SameStyleTaskCreateDTO;
import com.eleven.cms.aivrbt.service.IAiRingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @datetime 2024/10/11 10:26
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class AiRingServiceTest {

    @Autowired
    private IAiRingService ringService;
    @Autowired
    private AiVrbtApiController apiController;

    @Test
    public void testQueryUsableCount() {
        System.out.println(ringService.queryUsableCount("1"));
    }

    @Test
    public void testapiController() {
        SameStyleTaskCreateDTO sameStyleTaskCreateDTO = new SameStyleTaskCreateDTO();
        sameStyleTaskCreateDTO.setMobile("18681691784");
        System.out.println(apiController.createSameStyleTask(sameStyleTaskCreateDTO));
    }
}
