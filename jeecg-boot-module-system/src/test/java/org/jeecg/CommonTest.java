package org.jeecg;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.CmsAlarmUserConfigDetailDto;
import com.eleven.cms.entity.CmsMusicTelecom;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.EsInsertCode;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.repository.EsInsertCodeRepository;
import com.eleven.cms.es.repository.EsSubStatisticsRepository;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.es.service.impl.EsDataServiceImpl;
import com.eleven.cms.job.MonthRenewSubSmsTask;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.SubscribeVerifyService;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.SubscribeServiceImpl;
import com.eleven.cms.util.AdPlatformUtils;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.shandong.ShanDongYiDongConfig;
import com.eleven.cms.util.shandong.ShanDongYiDongHttpUtils;
import com.eleven.cms.vo.JunboLlbResult;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jeecg.common.util.RedisUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StringUtils;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.eleven.cms.es.service.impl.EsDataServiceImpl.INDEX_COORDINATES;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;

/**
 * <AUTHOR>
 * @datetime 2024/9/13 10:44
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CommonTest {

    @Autowired
    private IBizTypeService bizTypeService;
    @Autowired
    private ICmsAlarmUserConfigDetailService cmsAlarmUserConfigDetailService;
    @Autowired
    private ICmsAlarmUserService cmsAlarmUserService;
    @Autowired
    private ICmsMusicTelecomService cmsMusicTelecomService;
    @Autowired
    private SubscribeServiceImpl subscribeService;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    private SubscribeVerifyService subscribeVerifyService;
    @Autowired
    private EsSubStatisticsRepository esSubStatisticsRepository;
    @Autowired
    private IEsDataService esDataService;
    @Autowired
    private ShanDongYiDongConfig shanDongYiDongConfig;
    @Autowired
    private RestHighLevelClient restHighLevelClient;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private EsInsertCodeRepository esInsertCodeRepository;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IOrderVrbtService orderVrbtService;
    @Autowired
    private RabbitMQMsgSender rabbitMQMsgSender;
    @Test
    public void why1() {
        String str = "{\"success\":true,\"code\":\"0000\",\"message\":\"执行成功\",\"data\":{\"msg\":\"输入的验证码与发送给用户的验证码不匹配！\",\"responseCode\":\"1\",\"orderCode\":\"\",\"subMediaCode\":\"\",\"url\":\"\",\"routeCode\":\"\",\"sellerId\":\"\",\"token\":\"\",\"threePhotoUrl\":\"\",\"tips\":\"\",\"payId\":\"\",\"prepayStatus\":\"\",\"paraStr\":\"\",\"operatorMsg\":\"\",\"payChannel\":\"\",\"autoSendMsg\":null,\"extParam\":{},\"eraseCapEnabled\":0,\"captchaValidityPeriod\":null,\"captchaValidityPeriodTime\":\"\",\"confirmPicture\":\"\",\"subProductCode\":\"\",\"detailsPopupTitle\":\"\",\"detailsPopupInfo\":\"\",\"detailsPopupOrderInfo\":\"\",\"operatorResp\":\"\",\"jsUrl\":\"\",\"ogaction\":\"\",\"protocolUrl\":\"\",\"generalFlag\":\"\",\"extParamInfo\":\"\",\"signStr\":\"\",\"byteAuthorization\":\"\",\"data\":\"\",\"iframe\":null}}";
        JunboLlbResult junboLlbResult = JacksonUtils.readValue(str, JunboLlbResult.class);
        if (junboLlbResult.isOperationOk()) {
            log.info("1");
        } else {
            System.out.println(SUBSCRIBE_STATUS_FAIL);
            if(junboLlbResult.getData()!=null){
                System.out.println(junboLlbResult.getData().getMsg());
            }else{
                System.out.println(junboLlbResult.getMessage());
            }
            log.info("-1");
        }
    }

    @Test
    public void why0() {
        List<Subscribe> list = subscribeService.list(new LambdaQueryWrapper<Subscribe>()
                .eq(Subscribe::getSubChannel, "A152dJL5")
                .eq(Subscribe::getProvince, "山西")
                .eq(Subscribe::getStatus, "5")
                .between(Subscribe::getCreateTime, "2025-02-05 00:00:00", "2025-02-05 23:59:59")
        );
        System.out.println(list.size());
        for (Subscribe subscribe : list) {
            System.out.println(subscribe.getId());
            subscribe.setModifyTime(new Date());
            subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            subscribe.setResult("下单成功:下单成功");
            subscribeService.updateSubscribeDbAndEs(subscribe);
        }
//        if (one != null) {
//            Subscribe upd = new Subscribe();
//            upd.setId(one.getId());
//            upd.setModifyTime(new Date());
//            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//            upd.setResult("下单成功");
//            subscribeService.updateSubscribeDbAndEs(upd);
//        } else {
//            log.error("why empty? {}", line);
//        }
    }

    @Test
    public void schRingPublish() {
        rabbitMQMsgSender.sendSchRingPublishMsg("13408608881", "00211MO");
//
//        final Result<?> result = orderVrbtService.schRingPublish("15054559617", "00211MO");
//        //final Result<?> result = orderVrbtService.schRingPublish("17260807125", "00211M1");
//        System.out.println(result);
    }

    @Test
    public void updateRedisVal() {
//        // 获取当前的 LocalDateTime
//        LocalDateTime now = LocalDateTime.now();
//        // 获取下个月第一天的最后一秒的 LocalDateTime
//        LocalDateTime lastSecondOfNextMonth = now
//                .plusMonths(1) // 调整到下个月
//                .with(TemporalAdjusters.firstDayOfMonth()) // 调整到下个月的第一天
//                .with(LocalTime.MAX); // 将时间设置为当天的最后一秒
//        final long expire = Duration.between(now, lastSecondOfNextMonth).getSeconds();

        final long expire = Duration.between(LocalDateTime.now(), YearMonth.now().atEndOfMonth().atTime(LocalTime.MAX)).getSeconds();
        redisUtil.set("SubscribeVerifyStatus::Month:" + DateUtil.formatYearMonth(LocalDateTime.now()), "FINISH", expire);
    }

    @Test
    public void queryLQ() {
//        List<String> CHANNEL_LIST= Lists.newArrayList(/*MiguApiService.BIZ_SCH_CHANNEL_CODE_SZR,*/
////                MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP
////                MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX,
//                BizConstant.BIZ_CHANNEL_SXYD_JBAICL
//        );
//
//        MonthRenewSubSmsTask monthRenewSubSmsTask = new MonthRenewSubSmsTask();
//
//        EsSubscribe esSubscribe=new EsSubscribe();
//        esSubscribe.setStatus(1);
//        esSubscribe.setVerifyStatus(1);
////        esSubscribe.setVerifyStatusDaily(1);
//        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(monthRenewSubSmsTask.buildExportQuery(esSubscribe, CHANNEL_LIST),
//                EsSubscribe.class, EsDataServiceImpl.INDEX_COORDINATES);
//        List<EsSubscribe> esSubscribeList=searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
//        System.out.println(esSubscribeList.size());
//
//        int count = 0;
//        for (EsSubscribe subscribe : esSubscribeList) {
//            miguApiService.specialProductSub(subscribe.getMobile(), MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_SXJB);
//            count++;
//            System.out.println(count);
//        }
    }

    @Test
    public void updateInsetCode() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 6; i++) {
            LocalDate start = LocalDate.parse("2025-01-01");
            LocalDate end = LocalDate.parse("2025-01-11");
            BoolQueryBuilder builder = QueryBuilders.boolQuery();
            builder.mustNot(QueryBuilders.existsQuery("triggerAction"));
            builder.must(QueryBuilders.rangeQuery("createTime")
                    .gte(start)
                    .lte(end));
            builder.must(QueryBuilders.termQuery("trigger.keyword", "wl"));
            NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                    withQuery(builder).build();
//            nativeSearchQuery.setTrackTotalHits(true);
            nativeSearchQuery.setMaxResults(10000);
            SearchHits<EsInsertCode> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsInsertCode.class,
                    IndexCoordinates.of("insert_code_2025-01"));
            if (searchHits.getTotalHits() == 0) {
                System.out.println("task finish");
                break;
            }
            CountDownLatch countDownLatch = new CountDownLatch((int) searchHits.getTotalHits());
            List<EsInsertCode> result = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
            result.forEach(e -> {
                e.setTriggerAction("未知");
                executorService.execute(() -> {
                    esInsertCodeRepository.save(e);
                    countDownLatch.countDown();
                });
            });
            countDownLatch.await();
        }
    }

    @Test
    public void updateInsetCode12() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 1; i++) {
            LocalDate start = LocalDate.parse("2024-12-26");
            LocalDate end = LocalDate.parse("2024-12-31");
            BoolQueryBuilder builder = QueryBuilders.boolQuery();
//            builder.mustNot(QueryBuilders.existsQuery("triggerAction"));
            builder.must(QueryBuilders.rangeQuery("createTime")
                    .gte(start)
                    .lte(end));
            builder.must(QueryBuilders.termQuery("trigger.keyword", "wl"));
            NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                    withQuery(builder).build();
//            nativeSearchQuery.setTrackTotalHits(true);
            nativeSearchQuery.setMaxResults(10000);
            SearchHits<EsInsertCode> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsInsertCode.class,
                    IndexCoordinates.of("insert_code_2025-01"));
            if (searchHits.getTotalHits() == 0) {
                System.out.println("task finish");
                break;
            }
            CountDownLatch countDownLatch = new CountDownLatch((int) searchHits.getTotalHits());
            List<EsInsertCode> result = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
            result.forEach(e -> {
//                e.setTriggerAction("未知");
                executorService.execute(() -> {
//                    esInsertCodeRepository.save(e);
                    esInsertCodeRepository.delete(e);
                    countDownLatch.countDown();
                });
            });
            countDownLatch.await();
        }
    }

    @Test
    public void t1() {
        long start = System.currentTimeMillis();
        LocalDate now = LocalDate.parse("2025-01-08");
        SearchRequest searchRequest = new SearchRequest("insert_code*");
        // 不设置查询条件，表示对所有文档进行聚合
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("trigger.keyword", "wl"));
        boolQuery.must(QueryBuilders.rangeQuery("createTime")
                .gte(now)
                .lte(now));
//        1.日期
//        2.渠道号
//        3.子渠道号
//        4.省份
//        5.尾量类型
//        6.业务办理状态
//        7.尾量访问结果
//        8.指标
        // 创建日期直方图聚合：（按天）分组
        DateHistogramAggregationBuilder byDay = AggregationBuilders.dateHistogram("by_day")
                .field("createTime")
                .dateHistogramInterval(DateHistogramInterval.DAY)
                .timeZone(ZoneId.of("UTC"))
                .format("yyyy-MM-dd");
        TermsAggregationBuilder byChannel = AggregationBuilders.terms("by_channel")
                .field("channel.keyword")
                .size(10000);
        TermsAggregationBuilder bySubChannel = AggregationBuilders.terms("by_subChannel")
                .field("subChannel.keyword")
                .size(10000);
        TermsAggregationBuilder byProvinceName = AggregationBuilders.terms("by_provinceName")
                .field("provinceName.keyword")
                .size(50);
        TermsAggregationBuilder byTailCode = AggregationBuilders.terms("by_tailCode")
                .field("tailCode.keyword")
                .size(10);
        TermsAggregationBuilder byEventCode = AggregationBuilders.terms("by_eventCode")
                .field("bizHandleStatus.keyword")
                .size(10);
        TermsAggregationBuilder byTriggerAction = AggregationBuilders.terms("by_triggerAction")
                .field("triggerAction.keyword")
                .size(10);
        TermsAggregationBuilder byUserId = AggregationBuilders.terms("by_userId")
                .field("userId.keyword")
                .size(100000);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.aggregation(
                byDay.subAggregation(
                        byChannel.subAggregation(
                                bySubChannel.subAggregation(
                                        byProvinceName.subAggregation(
                                                byTailCode.subAggregation(
                                                        byEventCode.subAggregation(
                                                                byTriggerAction.subAggregation(byUserId)))))))
        );
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            System.out.println(searchResponse.getHits().getTotalHits());

            // 获取日期直方图聚合的结果
            Histogram by_day = searchResponse.getAggregations().get("by_day");
            for (Histogram.Bucket by_dayBucket : by_day.getBuckets()) {
                Terms by_channelTerms = by_dayBucket.getAggregations().get("by_channel");

                for (Terms.Bucket by_channelBucket : by_channelTerms.getBuckets()) {
                    Terms by_subChannelTerms = by_channelBucket.getAggregations().get("by_subChannel");

                    for (Terms.Bucket by_subChannelTermsBucket : by_subChannelTerms.getBuckets()) {
                        Terms by_provinceNameTerms = by_subChannelTermsBucket.getAggregations().get("by_provinceName");

                        for (Terms.Bucket by_provinceNameTermsBucket : by_provinceNameTerms.getBuckets()) {
                            Terms by_tailCodeTerms = by_provinceNameTermsBucket.getAggregations().get("by_tailCode");

                            for (Terms.Bucket by_tailCodeTermsBucket : by_tailCodeTerms.getBuckets()) {
                                Terms by_eventCodeTerms = by_tailCodeTermsBucket.getAggregations().get("by_eventCode");

                                for (Terms.Bucket by_eventCodeTermsBucket : by_eventCodeTerms.getBuckets()) {
                                    Terms by_triggerActionTerms = by_eventCodeTermsBucket.getAggregations().get("by_triggerAction");

                                    for (Terms.Bucket by_triggerActionTermsBucket : by_triggerActionTerms.getBuckets()) {
                                        Terms by_userIdTerms = by_triggerActionTermsBucket.getAggregations().get("by_userId");

                                        for (Terms.Bucket by_userIdTermsBucket : by_userIdTerms.getBuckets()) {
                                            System.out.println(by_dayBucket.getKeyAsString());
                                            System.out.println(by_channelBucket.getKeyAsString());
                                            System.out.println(by_subChannelTermsBucket.getKeyAsString());
                                            System.out.println(by_provinceNameTermsBucket.getKeyAsString());
                                            System.out.println(by_tailCodeTermsBucket.getKeyAsString());
                                            System.out.println(by_eventCodeTermsBucket.getKeyAsString());
                                            System.out.println(by_triggerActionTermsBucket.getKeyAsString());
                                            System.out.println(by_userIdTermsBucket.getKeyAsString());
                                            System.out.println(by_userIdTermsBucket.getDocCount());
                                            System.out.println("------------------------------------------------");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("统计尾量数据error", e);
        }
        System.out.println("costTime：" + (System.currentTimeMillis() - start));
}

    @Test
    public void testSaveEsInsertCode() {
        LocalDateTime now1 = LocalDateTime.of(2024, 12, 14, 17, 30);
        for (int i = 0; i < 10; i++) {
            EsInsertCode EsInsertCode = new EsInsertCode();
            EsInsertCode.setId(IdWorker.getIdStr());
            EsInsertCode.setCreateTime(now1);
            EsInsertCode.setCreateTimestamp(now1.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli());
            elasticsearchRestTemplate.save(EsInsertCode);
        }
    }

    @Test
    public void testQueryAll() {
        // 执行查询
        Query query = new NativeSearchQueryBuilder().build();
        SearchHits<EsInsertCode> searchHits = elasticsearchRestTemplate.search(query, EsInsertCode.class);
        System.out.println(searchHits.getTotalHits());
        // 获取结果
        List<SearchHit<EsInsertCode>> searchHitList = searchHits.getSearchHits();
        for (SearchHit<EsInsertCode> hit : searchHitList) {
            // 获取文档中的原始数据（JSON格式）
            EsInsertCode content = hit.getContent();
            System.out.println("Source as string: " + content);
            System.out.println(content.getCreateTime());
        }
    }

    @Test
    public void testQueryEsInsertCode() throws ParseException {
        ZonedDateTime startTime = ZonedDateTime.of(LocalDateTime.of(2024, 12, 16, 0, 0), ZoneOffset.UTC);
        ZonedDateTime endTime = ZonedDateTime.of(LocalDateTime.of(2024, 12, 16, 23, 59), ZoneOffset.UTC);

        // 构建查询
        Query query = new NativeSearchQueryBuilder()
                .withQuery(rangeQuery("createTime") // "timestamp" 是你的日期字段名
                        .from(startTime)
                        .to(endTime))
                .build();

        // 执行查询
        SearchHits<EsInsertCode> searchHits = elasticsearchRestTemplate.search(query, EsInsertCode.class);
        System.out.println(searchHits.getTotalHits());

        // 获取结果
        List<SearchHit<EsInsertCode>> searchHitList = searchHits.getSearchHits();
        for (SearchHit<EsInsertCode> hit : searchHitList) {
            // 获取文档中的原始数据（JSON格式）
            EsInsertCode content = hit.getContent();
            System.out.println("Source as string: " + content);
        }
    }

    @Test
    public void testShanDongYiDongConfig() {
        Map<String, Map<String, String>> productMap = shanDongYiDongConfig.getProductMap();
        System.out.println(productMap);
    }

    @Test
    public void testGenSubStatistics() {
        Set<String> data = new HashSet<>();
//        esSubStatisticsRepository.deleteAll();
//        subscribeVerifyService.genSubStatistics();
//        subscribeVerifyService.genSubStatisticsByChannel("00211DQ");
//        List<EsSubStatistics> esSubStatistics = esDataService.subscribeStatisticsLast6MonthByChannel(LocalDate.of(2024, 9, 1),
//                LocalDate.of(2024, 9, 30), "00211DQ");
//        System.out.println(esSubStatistics);
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                from(LocalDate.of(2024, 12, 16)).
                to(LocalDate.of(2024, 12, 16))
                .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("channel.keyword", "QYCL_GR_MH"));
         builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                withSourceFilter(new FetchSourceFilter(new String[]{"id", "mobile"}, new String[]{""})).  //不查询任何结果
                        build();
        nativeSearchQuery.setTrackTotalHits(true);
//        nativeSearchQuery.setMaxResults(0); //不查询具体数据
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        System.out.println(searchHits.getTotalHits());
        List<SearchHit<EsSubscribe>> searchHits1 = searchHits.getSearchHits();
//        Map<String, Integer> map = new HashMap<>();
        searchHits1.forEach(e -> {
            EsSubscribe content = e.getContent();
            if (! data.contains(content.getMobile())) {
                System.out.println(content.getMobile());
            }
        });
//        System.out.println(map);
    }


    @Test
    public void testSchQuery() {
//        RemoteResult schResult = miguApiService.schQuery("18205547218", "00211DQ");
//        System.out.println(schResult.isSchMember());

        RemoteResult schResult1 = miguApiService.schQuery("13994810130", MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP);
        System.out.println(schResult1.isSchMember());
    }

    /**
     * 获取广告平台名称
     */
    @Test
    public void testGetAdPlatform() {
        System.out.println(AdPlatformUtils.getAdPlatform("JXYD_ADLD_YR"));
    }

    /**
     * 查询渠道号列表
     */
    @Test
    public void testQueryChannelList() {
        final List<CmsAlarmUserConfigDetailDto> qyclGr = cmsAlarmUserConfigDetailService.getAlarmUserList("QYCL_GR",
                "2");
        qyclGr.forEach(System.out::println);

        System.out.println(cmsAlarmUserService.findOpenIdByPhone("17723327836"));
    }

    @Test
    public void telecomVrbtListPage() {
        QueryWrapper<CmsMusicTelecom> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("create_time");

        // 过滤vrtb_img为空的数据
        queryWrapper.isNotNull("vrbt_img");
        queryWrapper.ne("vrbt_img", "");

        Page<CmsMusicTelecom> page = new Page<CmsMusicTelecom>(1, 10);
        cmsMusicTelecomService.page(page, queryWrapper);
    }

    @Test
    public void updateOrderStatus() throws IOException {
        BufferedReader reader = new BufferedReader(new FileReader("C:\\Users\\<USER>\\Desktop\\PD2410240730.txt"));
        String line;
        while ((line = reader.readLine()) != null) {
            Subscribe one = subscribeService.getOne(new LambdaQueryWrapper<Subscribe>()
                    .eq(Subscribe::getIspOrderNo, line));
            if (one != null) {
                Subscribe upd = new Subscribe();
                upd.setId(one.getId());
                upd.setModifyTime(new Date());
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult("下单成功");
                subscribeService.updateSubscribeDbAndEs(upd);
            } else {
                log.error("why empty? {}", line);
            }
        }
    }

    /**
     * 山东移动外部互联网渠道接入 初始化公私钥
     *
     * 已生成记录：
     * {RSAPublicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3XIsoKlymvVNGbUnWCEB2XHzSO0Uui48c2rdH
     * UKcPwL5Rn9wYfPHyYpjNvq2HLSbl5CfhmiuUV95B/PT2XUeLRIaJdJMe2PIKcNoVfnHqu+6Z4463
     * mAZkPuR9YFYf32CeKVFDehJ3AqNbGrY3NA2cziIjYMWJi7MDRGS4WkRIaQIDAQAB
     * , RSAPrivateKey=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALdciygqXKa9U0ZtSdYIQHZcfNI7
     * RS6Ljxzat0dQpw/AvlGf3Bh88fJimM2+rYctJuXkJ+GaK5RX3kH89PZdR4tEhol0kx7Y8gpw2hV+
     * ceq77pnjjreYBmQ+5H1gVh/fYJ4pUUN6EncCo1satjc0DZzOIiNgxYmLswNEZLhaREhpAgMBAAEC
     * gYB0mYHYQc/3LA+nilnYpBix67BoU85zgcW4+JBx5iTGTK/54ekpHVMR6ob21tksS5cfu41s1gf+
     * Zj3swnnXrIuQPVMPDrhPY/LpowA499q5CUlHEmyQQoGajbcgCPnaQwy3kJQLCXEMOy0scKs6Fx1X
     * hyCfrA54VUeUakDa2P6OgQJBAOQKPAs3pQCFMxNZRXkPeMXzQuV67icyofWvd0t3u+pVfiBP3vwN
     * wyDkS12EoKlwvOwwGRw6p6IH00873zOWYxkCQQDN1+6IGxSisUY33gXvh8zOZujqdaMUJVjLUCCP
     * +HV7rkxGCtxlbBTU8TtaiveD6h2uoLksPznHz2561KxYvYnRAkB6a+AmKX5EVD3CQmeggSTWKC0e
     * oysdEfqOud8yPZo9SB5I3HgaGo1JqCWpk9zoeeSCa6PfWsHJo6fWXo8J089pAkEAkSyieXGH+zpF
     * yK8YxpejNUrVKE6xJks0taecX42e5gzGlHuidxFkG87Kyo/KA0pBhuCp2G/7A1XjmFOX/bonkQJA
     * cWfczQC5C33s0xMqJUDHcjXEUg2/xpBmWvLKRBC8mvI8Ti+ZhodwUZFu053o52NqYEv2K2cNLoE6
     * 8tTq1dQE5Q==
     * }
     */
//    public static Map<String, Object> initKey() throws Exception {
//        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
//        keyPairGen.initialize(1024);
//        KeyPair keyPair = keyPairGen.generateKeyPair();
//        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
//        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
//        Map<String, Object> keyMap = new HashMap<String, Object>(2);
//        keyMap.put("RSAPublicKey", (new BASE64Encoder()).encodeBuffer(publicKey.getEncoded()));
//        keyMap.put("RSAPrivateKey", (new BASE64Encoder()).encodeBuffer(privateKey.getEncoded()));
//        return keyMap;
//    }
//    public static void main(String[] args) throws Exception {
//        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3XIsoKlymvVNGbUnWCEB2XHzSO0Uui48c2rdHUKcPwL5Rn9wYfPHyYpjNvq2HLSbl5CfhmiuUV95B/PT2XUeLRIaJdJMe2PIKcNoVfnHqu+6Z4463mAZkPuR9YFYf32CeKVFDehJ3AqNbGrY3NA2cziIjYMWJi7MDRGS4WkRIaQIDAQAB";
//
//        String channelCode = "HSTJ";
//        String countCode = "lo0mEv18jW";
//        String telnum = "13589103053";
//        String orderId = "CC" + DateUtils.yyyyMMdd.get().format(new Date()) + IdWorker.getId();
//        System.out.println(orderId);
//
//        System.out.println("营销策略：" + RSAUtils.sign((channelCode + "_" + countCode + "_" + telnum).getBytes()));
//        System.out.println("获取短信验证码：" + RSAUtils.sign((telnum + "_" + channelCode).getBytes()));
//        System.out.println("业务办理：" + RSAUtils.sign((orderId + "_" + channelCode).getBytes()));
//    }

    @Test
    public void getSmsCode() {
//        ObjectNode jsonNode = JacksonUtils.createObjectNode();
//        jsonNode.put("mobile", "13589103053");
//        jsonNode.put("channel", "SDYD_QYCSLLB_HS");
//        SpringUtil.getBean(ShanDongYiDongService.class).getSmsCode(jsonNode);

//        ShanDongCountCodeQueryDTO shanDongCountCodeQueryDTO = new ShanDongCountCodeQueryDTO();
//        shanDongCountCodeQueryDTO.setTelnum("13589103053");
//        shanDongCountCodeQueryDTO.setCountCode("lo0mEv18jW");
//        ShanDongYiDongHttpUtils.countCodeQuery(shanDongCountCodeQueryDTO, null);

        ShanDongGetSmsCodeDTO shanDongGetSmsCodeDTO = new ShanDongGetSmsCodeDTO();
        shanDongGetSmsCodeDTO.setTelnum("13589103053");
        shanDongGetSmsCodeDTO.setCountCode("lo0mEv18jW");
        ShanDongYiDongHttpUtils.getSmsCode(shanDongGetSmsCodeDTO, null);
    }

    @Test
    public void testOrder() {
        ShanDongOrderDTO shanDongOrderDTO = new ShanDongOrderDTO();
        shanDongOrderDTO.setTelnum("13589103053");
        shanDongOrderDTO.setCountCode("lo0mEv18jW");
        shanDongOrderDTO.setSmsCode("3912");
//        shanDongOrderDTO.setHandleUrl("https://crbt.hongsengtech.com/mobile_provinces/sd_bjhy?test=1");
        shanDongOrderDTO.setHandleUrl("https://crbt.kunpengtn.com/stationconstructions/promotion/1219/?xwPointC=11&xwBusC=bjhyjtll&xwEnPicC=241218");
        shanDongOrderDTO.setDeviceCode("%227c93b71b-1f82-408e-965e-11e7d6f84767%22");
        shanDongOrderDTO.setPlatformCode("");

//        Map<String, Object> extraInfoMap = new HashMap<>();
//        extraInfoMap.put("手机号", "13589103053");
//        extraInfoMap.put("渠道号", "SDYD_QYCSLLB_HS");
        ShanDongYiDongHttpUtils.order(shanDongOrderDTO, null);
    }

    @Test
    public void testQryOrderComplaint() {
        ShanDongOrderComplaintQryDTO complaintQryDTO = new ShanDongOrderComplaintQryDTO();
        complaintQryDTO.setTime("2025/01/20");
        complaintQryDTO.setPage("1");
        ShanDongYiDongHttpUtils.qryOrderComplaint(complaintQryDTO);
    }

    @Test
    public void testUnsubscribe() {
        ShanDongUnsubscribeDTO unsubscribeDTO = new ShanDongUnsubscribeDTO();
        unsubscribeDTO.setOpTime("2025/01/20");
        unsubscribeDTO.setPage("1");
        ShanDongYiDongHttpUtils.unsubscribe(unsubscribeDTO);
    }

    @Test
    public void testSettlement() {
        ShanDongSettlementDTO settlementDTO = new ShanDongSettlementDTO();
        settlementDTO.setCycle("202501");
        settlementDTO.setPage("1");
        ShanDongYiDongHttpUtils.settlement(settlementDTO);
    }


    @Test
    public void testQueryOrderFromMysql() throws IOException {
        List<String> mobiles = subscribeService.list(new LambdaQueryWrapper<Subscribe>()
                .select(Subscribe::getMobile)
                .between(Subscribe::getCreateTime, "2024-10-01 00:00:00", "2024-10-31 23:59:59")
                .like(Subscribe::getChannel, "GZYD")
                .eq(Subscribe::getStatus, 1)).stream().map(Subscribe::getMobile).collect(Collectors.toList()).subList(0, 100);
        Set<String> data = new HashSet<>();
        for (String mobile : mobiles) {
            data.add(DesensitizedUtil.mobilePhone(mobile));
        }

        BufferedReader bufferedReader = new BufferedReader(new FileReader("C:\\Users\\<USER>\\Desktop\\mobile.txt"));
        List<String> flag = new ArrayList<>();
        String str;
        while ((str = bufferedReader.readLine()) != null) {
            if (data.contains(str)) {
                flag.add("1");
                System.out.println("???");
            } else {
                flag.add("0");
            }
        }

        BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\flag.txt"));
        for (int i = 0; i < flag.size(); i++) {
            bufferedWriter.write(flag.get(i) + "\n");
        }

        bufferedReader.close();
        bufferedWriter.close();
    }

//    public static void main(String[] args) {
////        String str = "13984136968";
////        System.out.println(DesensitizedUtil.mobilePhone(str));
//
//        JsonNode jsonNode = JacksonUtils.readTree("{\"transactionId\":\"1868576491180740610\",\"linkId\":\"1156\",\"mobile\":\"13623509681\",\"subChannel\":\"A152dJL5\",\"deviceInfo\":\"ENrI5ZiLv50DGK3PsPrbAiDnq8yXsAEwDDjb_rmoA0IpZmZkYTc5ZTItNzRjNy00YTEwLWE2MTAtOGQxNTE1M2Q1ZjZmdTU5NzdIgNKTrQOQAQA\",\"smsCode\":\"126563\",\"source\":\"https://crbt.kunpengtn.com/stationconstructions/promotion/1156/?phone=13623509681&subChannel=A152dJL5&union_site=2460024518&projectid=7448823959037329420&promotionid=7448824495714435126&creativetype=16&clickid=ENrI5ZiLv50DGK3PsPrbAiDnq8yXsAEwDDjb_rmoA0IpZmZkYTc5ZTItNzRjNy00YTEwLWE2MTAtOGQxNTE1M2Q1ZjZmdTU5NzdIgNKTrQOQAQA&ad_id=1818560668411162&_toutiao_params=%7B%22cid%22%3A1818560877388890%2C%22device_id%22%3A47294125543%2C%22log_extra%22%3A%22%7B%5C%22ad_price%5C%22%3A%5C%22Z1-lEAAI9gZnX6UQAAj2BjWWTqgx0rnRuRk2vA%5C%22%2C%5C%22city_id%5C%22%3Anull%2C%5C%22convert_id%5C%22%3A0%2C%5C%22country_id%5C%22%3Anull%2C%5C%22dma_id%5C%22%3Anull%2C%5C%22orit%5C%22%3A900000000%2C%5C%22province_id%5C%22%3Anull%2C%5C%22req_id%5C%22%3A%5C%22ffda79e2-74c7-4a10-a610-8d15153d5f6fu5977%5C%22%2C%5C%22rit%5C%22%3A890142555%7D%22%2C%22orit%22%3A900000000%2C%22req_id%22%3A%22ffda79e2-74c7-4a10-a610-8d15153d5f6fu5977%22%2C%22rit%22%3A890142555%2C%22sign%22%3A%22D41D8CD98F00B204E9800998ECF8427E%22%2C%22uid%22%3A93403752365%2C%22ut%22%3A12%7D&entryPageId=1066\"}");
//        System.out.println(jsonNode.get("deviceCode").asText());
//    }

    @Test
    public void testQueryOrderFromEs() {
        //需要查询的字段
        String[] includes = {"id"};
        //构建搜索条件
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.should(QueryBuilders.termQuery("channel", "GZYD_XSZS"));
        builder.should(QueryBuilders.termQuery("channel", "GZYD_TNLY_HYLB"));
        builder.should(QueryBuilders.termQuery("channel", "GZYD_KDX_ZSB"));
        builder.should(QueryBuilders.termQuery("channel", "GZYD_QYJXB"));
        builder.should(QueryBuilders.termQuery("channel", "GZYD_NSXK"));
        builder.should(QueryBuilders.termQuery("channel", "GZYD_TSQY"));
        builder.must(rangeQuery("createTime").
                gte(getTime("2024-10-01 00:00:00")).
                lte(getTime("2024-10-01 23:59:59")));
        builder.must(QueryBuilders.termQuery("status", 1));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withSourceFilter(new SourceFilter() {
                    @Override
                    public String[] getIncludes() {
                        return includes;
                    }

                    @Override
                    public String[] getExcludes() {
                        return new String[0];
                    }
                }).
                withQuery(builder).
                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(120000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class,
                INDEX_COORDINATES);
        long totalHits = searchHits.getTotalHits();
        System.out.println(totalHits);
//        List<EsSubscribe> list = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
//        System.out.println(list.size());
    }

    public long getTime(String dateStr) {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        // 设置时区，如果字符串中不包含时区信息，可以忽略这一步
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        Calendar calendar = null;
        try {
            calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(dateStr));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return calendar.getTimeInMillis();
    }

    @Test
    public void updateOrder() {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                from(LocalDate.now()).
                to(LocalDate.now())
                .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("channel.keyword", "SDYD_BJ20_HS"));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).build();
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        long totalHits = searchHits.getTotalHits();
        System.out.println(totalHits);
    }

    public static void main(String[] args) {
        String str = "{\"code\":60002,\"message\":\"禁止营销客户\",\"timestamp\":1736224629999,\"data\":null,\"userMessage\":\"该业务暂时不能办理。\",\"busiInfos\":null,\"blackList\":[\"【外部互联网-新】低流量客户群1231\"]}";
        JsonNode jsonNode = JacksonUtils.readTree(str);
        System.out.println(jsonNode.at("/message").asText());
        System.out.println(jsonNode.at("/blackList").toString());

        String message = jsonNode.at("/message").asText();
        String blackListStr = jsonNode.at("/blackList").toString();

        StringBuilder sb = new StringBuilder();
        sb.append(message);
        if (! StringUtils.isEmpty(blackListStr)) {
            sb.append(": ").append(blackListStr);
        }
        System.out.println(sb.toString());
    }

//    2024-03-01，2024-06-30
//    2024-07-01，2024-9-30
//    2024-10-01，2024-12-31
//    2025-01-01，2025-01-13
    @Test
    public void queryQZCL() throws IOException, InterruptedException {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                from(LocalDate.parse("2024-01-01")).
                to(LocalDate.parse("2025-01-13"))
                .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("status", "1"));
        builder.must(
                QueryBuilders.boolQuery()
                        .should(QueryBuilders.termQuery("channel.keyword", "00211AI"))
                        .should(QueryBuilders.termQuery("channel.keyword", "0021180"))
        );
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder)
                .withFields("mobile", "channel", "openTime")
                .build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        long totalHits = searchHits.getTotalHits();
        System.out.println("总数：" + totalHits);

        CountDownLatch countDownLatch = new CountDownLatch((int) totalHits);
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        List<ObjectNode> result = new CopyOnWriteArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        searchHits.forEach(e -> {
            executorService.execute(() -> {
                EsSubscribe subscribe = e.getContent();
                RemoteResult remoteResult = miguApiService.vrbtStatusQuery(subscribe.getMobile(), subscribe.getChannel());
                ObjectNode objectNode = JacksonUtils.createObjectNode();
                objectNode.put("channel", subscribe.getChannel());
                objectNode.put("mobile", subscribe.getMobile());
                objectNode.put("openTime", sdf.format(subscribe.getOpenTime()));
                if (remoteResult.isVrbtFun()) {
                    objectNode.put("orderStatus", "1");
                } else {
                    objectNode.put("orderStatus", "0");
                }
                result.add(objectNode);
                countDownLatch.countDown();
            });
        });

        countDownLatch.await();
//        渠道号、手机号、订购时间、是否有彩铃功能
        FileWriter fileWriter = new FileWriter("C:\\Users\\<USER>\\Desktop\\hello.csv");
        fileWriter.write("渠道号,手机号,订购时间,是否有视频彩铃功能(0:无 1:有)\n");
        result.forEach(e -> {
            try {
                fileWriter.write(e.get("channel").asText() + ","
                        + e.get("mobile").asText() + ","
                        + e.get("openTime").asText() + ","
                        + e.get("orderStatus").asText() + "\n");
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        });
        fileWriter.close();
    }
}
