package org.jeecg;

import com.eleven.cms.entity.ColumnMusic;
import com.eleven.cms.entity.Music;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IColumnMusicService;
import com.eleven.cms.service.IColumnService;
import com.eleven.cms.service.IMusicService;
import com.eleven.cms.vo.MusicVo;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class MusicServiceImplTest {

    @Autowired
    IMusicService musicService;
    @Autowired
    IColumnMusicService columnMusicService;
    @Autowired
    IColumnService columnService;
    @Autowired
    MiguApiService miguApiService;


    @Test
    public void findVrbtInfoByCopyrightId(){
        final MusicVo musicVo = musicService.findVrbtInfoByCopyrightId("699052T2079");
        System.out.println("musicVo = " + musicVo);
        final MusicVo musicVo1 = musicService.findVrbtInfoByCopyrightId("699052T2079");
        System.out.println("musicVo1 = " + musicVo1);
    }

    @Test
    public void getContentIdByCopyrightId(){
        final String contentid = musicService.getContentIdByCopyrightId("699052T2079");
        System.out.println("contentid = " + contentid);
        final String contentid1 = musicService.getContentIdByCopyrightId("699052T2079");
        System.out.println("contentid1 = " + contentid1);
    }

    ///**
    // * 更新播放地址
    // */
    //@Test
    //public void fetch(){
    //    musicService.lambdaQuery()
    //            //.eq(Music::getMusicType,"明星")
    //            //.isNull(Music::getVrbtVideo)
    //            .list()
    //            .forEach(music -> musicService.fetchVideoUrl(music));
    //
    //}

    @Test
    public void chuangwang() {
        String str = "690116T0198,法治讲堂\n" +
                "690116T0193,纵览天下\n" +
                "690116T0196,我叫元小雅\n" +
                "690116T0206,法治讲堂\n" +
                "690116T0207,法治讲堂\n" +
                "690116T0211,法治讲堂\n" +
                "690116T0209,法治讲堂\n" +
                "690116T0210,法治讲堂\n" +
                "690116T0212,最美村落\n" +
                "690116T0213,最美村落\n" +
                "690116T0214,最美村落\n" +
                "690116T0215,最美村落\n" +
                "690116T0216,最美村落\n" +
                "690116T0217,萌宠时代\n" +
                "690116T0218,萌宠时代\n" +
                "690116T0237,法治讲堂\n" +
                "690116T0238,我叫元小雅\n" +
                "690116T0229,纵览天下\n" +
                "690116T0239,最美村落\n" +
                "690116T0240,我叫元小雅\n" +
                "690116T0241,法治讲堂\n" +
                "690116T0242,法治讲堂\n" +
                "690116T0243,法治讲堂\n" +
                "690116T0230,纵览天下\n" +
                "690116T0231,纵览天下\n" +
                "690116T0232,法治讲堂\n" +
                "690116T0244,最美村落\n" +
                "690116T0245,法治讲堂\n" +
                "690116T0246,我叫元小雅\n" +
                "690116T0247,我叫元小雅\n" +
                "690116T0248,最美村落\n" +
                "690116T0233,最美村落\n" +
                "690116T0249,纵览天下\n" +
                "690116T0234,法治讲堂\n" +
                "690116T0250,纵览天下\n" +
                "690116T0235,法治讲堂\n" +
                "690116T0236,纵览天下\n" +
                "690116T0221,萌宠时代\n" +
                "690116T0222,萌宠时代\n" +
                "690116T0223,闲适一刻\n" +
                "690116T0224,萌宠时代\n" +
                "690116T0225,最美村落\n" +
                "690116T0226,最美村落\n" +
                "690116T0227,最美村落\n" +
                "690116T0228,我叫元小雅\n";

        String[] arrs = str.split("\n");

        String fzsd = "1527547015717265409";
        String zmcl = "1527547985679425537";
        String zltx = "1527548521770196994";
        String qt = "1527548616423055361";

        int fzsdXl = 0;
        int zmclXl = 0;
        int zltxXl = 0;
        int qtXl = 0;

        int xl = 0;
        String columnId = "";
        for (int i = 0; i < arrs.length; i++) {
            if ("法治讲堂".equals(arrs[i].split(",")[1])) {
                fzsdXl++;
                xl = fzsdXl;
                columnId = fzsd;
            } else if ("最美村落".equals(arrs[i].split(",")[1])) {
                zmclXl++;
                xl =zmclXl;
                columnId = zmcl;
            } else if ("纵览天下".equals(arrs[i].split(",")[1])) {
                columnId = zltx;
                zltxXl++;
                xl = zltxXl;
            } else {
                qtXl++;
                xl = qtXl;
                columnId = qt;
            }
            ColumnMusic columnMusic = new ColumnMusic();
            columnMusic.setColumnId(columnId);
            columnMusic.setPriority(xl);
            Music music = musicService.lambdaQuery().eq(Music::getCopyrightId, arrs[i].split(",")[0]).one();
            columnMusic.setMusicId(music.getId());
            columnMusic.setTitle(music.getMusicName());
//            columnMusicService.save(columnMusic);

        }


    }

    /**
     * 校验歌曲是否有效，是否能获取到播发地址
     */
    @Test
    public void valid() {
        final ObjectMapper mapper = new ObjectMapper();
        final String token = miguApiService.fetchToken(MiguApiService.MOBILE_FOR_TEST, MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT);
        //try (FileInputStream fileInputStream = new FileInputStream("D:\\歌曲校验_精选内容.xlsx")) {
        try (FileInputStream fileInputStream = new FileInputStream("D:\\歌曲校验_测试.xlsx")) {
            ImportParams params = new ImportParams();
            //params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            List<Music> list = ExcelImportUtil.importExcel(fileInputStream, Music.class, params);
            log.info("歌曲有效性校验，需要校验共计:{}条",list.size());
            for (int i = 0; i < list.size(); i++) {
                log.info("歌曲有效性校验，当前校验第{}条",i);
                Music music = list.get(i);
                try {
                    music = musicService.lambdaQuery().eq(Music::getCopyrightId, music.getCopyrightId()).one();
                    String json = miguApiService.vrbtTryToSee(null, MiguApiService.CH_DYB_DEFAULT, music.getCopyrightId());
                    RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(json);
                    music.setMusicType(String.format("渠道:%s", result.isOK() ? "有效" : "无效"));
                    json = miguApiService.vrbtTryToSee(token, MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, music.getDyCopyrightId());
                    result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(json);
                    music.setMusicStyle(String.format("订阅:%s", result.isOK() ? "有效" : "无效"));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                list.set(i, music);
            }
            final ExportParams exportParams = new ExportParams("歌曲校验报表", "导出人:test", "歌曲");
            final Workbook workbook = ExcelExportUtil.exportExcel(exportParams, Music.class, list);
            //File outFile = new File("D:\\歌曲校验_精选内容_校验结果"+ (workbook instanceof HSSFWorkbook ?".xls" :".xlsx"));
            File outFile = new File("D:\\歌曲校验_测试_校验结果"+ (workbook instanceof HSSFWorkbook ?".xls" :".xlsx"));
            try (FileOutputStream fileOutputStream = new FileOutputStream(outFile)) {
                workbook.write(fileOutputStream);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    /**
     *
     */
    @Test
    public void fetchAllVrbtVideo() {
        final String token = miguApiService.fetchToken(MiguApiService.MOBILE_FOR_TEST, MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT);
        final List<Music> list = musicService.list();
        log.info("抓取歌曲播放地址，需要校验共计:{}条",list.size());
        for (int i = 0; i < list.size(); i++) {
            log.info("抓取歌曲播放地址，当前校验第{}条",i);
            Music music = list.get(i);
            try {
                String vrbtVideoUrl = miguApiService.vrbtTryToSeeUrl(null, MiguApiService.CH_DYB_DEFAULT, music.getCopyrightId());
                if(StringUtils.isBlank(vrbtVideoUrl)) {
                    vrbtVideoUrl = miguApiService.vrbtTryToSeeUrl(token, MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, music.getDyCopyrightId());
                }
                if(StringUtils.isNotBlank(vrbtVideoUrl)) {
                    musicService.lambdaUpdate().eq(Music::getId,music.getId()).set(Music::getVrbtVideo,vrbtVideoUrl).update();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @Test
    public void fetchMusicInfo() throws IOException {
        //System.out.println(miguApiService.fetchVrbtProduct("664767T0613M"));
        //System.out.println(miguApiService.fetchVrbtProduct("600926000011244887"));
        System.out.println(miguApiService.fetchVrbtProduct("638799T5132M"));
        System.out.println(miguApiService.fetchVrbtProduct("638799T9116"));
        //String vrbtIdStr = "600926000011244765\n" + "600926000011244887\n" + "600926000011244963\n" + "600926000011245010\n" + "600926000011245042\n" + "600926000011244954\n" + "600926000011244949\n" + "600926000011244965\n" + "600926000011244882\n" + "600926000011244840\n" + "600926000011244747\n" + "600926000011244953\n" + "600926000011244943\n" + "600926000011244974\n" + "600926000011244790\n" + "600926000011244927\n" + "600926000011244785\n" + "600926000011244911\n" + "600926000011244880\n" + "600926000011244777\n" + "600926000011245052\n" + "600926000011244845\n" + "600926000011244972\n" + "600926000011244733";
        //final List<VrbtProduct> collect = Arrays.stream(vrbtIdStr.trim().split("\n")).map(
        //        miguApiService::fetchVrbtProduct).collect(Collectors.toList());
        //new ObjectMapper().writeValue(System.out, collect);
    }

    /**
     * 歌曲信息抓取,根据第三方给的歌曲excel获取歌曲图片和视频播放地址,输出json格式(彩铃中心的版权id)
     */
    //@Test
    //public void fetchVrbtInfo() {
    //
    //    String channelCode = "014X04N";
    //    //excel格式为1行标题,第一列为版权id,第二列为歌曲名,第三列为视频彩铃产品id
    //    //String filePath = "D:\\歌曲校验_精选内容.xlsx";
    //    String filePath = "D:\\workspace-springsource\\jeecg-boot-cms\\视频彩铃歌曲信息抓取.xlsx";
    //    final String token = miguApiService.fetchToken(MiguApiService.MOBILE_FOR_TEST, channelCode);
    //    try (FileInputStream fileInputStream = new FileInputStream(filePath)) {
    //        ImportParams params = new ImportParams();
    //        //params.setTitleRows(2);
    //        params.setHeadRows(1);
    //        params.setNeedSave(false);
    //        List<Music> list = ExcelImportUtil.importExcel(fileInputStream, Music.class, params);
    //        log.info("歌曲信息抓取，需要抓取共计:{}条",list.size());
    //        for (int i = 0; i < list.size(); i++) {
    //            log.info("歌曲信息抓取，当前抓取第{}条",i);
    //            Music music = list.get(i);
    //            if(StringUtils.isBlank(music.getCopyrightId())  || StringUtils.isBlank(music.getVrbtProductId())) {
    //                continue;
    //            }
    //            try {
    //                final String imgUrl = miguApiService.fetchImg(music.getVrbtProductId());
    //                if(StringUtils.isBlank(imgUrl)){
    //                    log.info("抓取预览图失败,歌曲名:{}",music.getMusicName());
    //                }else {
    //                    music.setVrbtImg(imgUrl);
    //                }
    //                String playUrl = miguApiService.vrbtTryToSeeUrl(token, channelCode, music.getCopyrightId());
    //                if(StringUtils.isEmpty(playUrl)){
    //                    log.info("抓取播放地址失败,歌曲名:{}",music.getMusicName());
    //                }else {
    //                    music.setVrbtVideo(playUrl);
    //                }
    //            } catch (Exception e) {
    //                log.error(e.getMessage(), e);
    //            }
    //        }
    //        new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL).writerWithDefaultPrettyPrinter().writeValue(System.out,list);
    //        TimeUnit.SECONDS.sleep(1L);
    //        log.info("抓取歌曲信息完成");
    //    } catch (Exception e) {
    //        log.error(e.getMessage(), e);
    //    }
    //
    //}



}