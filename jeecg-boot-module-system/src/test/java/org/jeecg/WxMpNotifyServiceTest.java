package org.jeecg;

import com.eleven.cms.config.WxMpProperties;
import com.eleven.cms.service.impl.WxMpNotifyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class WxMpNotifyServiceTest {

    @Autowired
    WxMpNotifyService wxMpNotifyService;

    @Autowired
    private WxMpProperties wxMpProperties;

    @Test
    public void fetchAccessToken() {
        final String accessToken = wxMpNotifyService.fetchAccessToken();
        System.out.println("accessToken = " + accessToken);
    }
    
    @Test
    public void sendTemplateMessage() {
        String channelCode = "002115U";
        String province = "四川";
        String limitDesc = "已达限量500";
        String paramJson = "{\"thing2\":{\"value\":\"渠道号" + channelCode + "\"},\"thing7\":{\"value\":\"省份" + province + "\"},\"thing3\":{\"value\":\"" + limitDesc + "\"}}";
        final boolean result = wxMpNotifyService.sendTemplateMessage("oLvmG6qFNxcjgBLiB9dhR8FXz8ao", wxMpProperties.getTemplateMessageId(),paramJson);
        System.out.println("result = " + result);
    }

    @Test
    public void sendLimitMessage() {
        String channelCode = "002115U";
        String province = "四川";
        //String limitDesc = "已经到达限量,限250";
        String limitDesc = "即将限量,限250,当前230我要超出长度看看能发出去消息吗？";
        wxMpNotifyService.sendLimitMessage("18080928200", channelCode, province, limitDesc);
        //wxMpNotifyService.batchSendLimitMessage(wxMpProperties.getMobileOpenIdMap().keySet(),channelCode,province,limitDesc);
    }

    @Test
    public void sendAlertMessage() {
        String channelCode = "002115U";
        String alertTypeDesc = "业务开通发生告警";
        String alertMessage = "最近有50条订购失败";
        wxMpNotifyService.sendAlertMessage("18080928200", channelCode, alertTypeDesc, alertMessage);
        //wxMpNotifyService.batchSendLimitMessage(wxMpProperties.getMobileOpenIdMap().keySet(),channelCode,province,limitDesc);
    }

}