package org.jeecg;

import com.eleven.cms.remote.RechargeAlertService;
import com.eleven.cms.service.IMemberService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: cai lei
 * @create: 2022-04-15 15:49
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RechargeAlertServiceTest {

    @Autowired
    IMemberService memberService;

    @Test
    public void batc() {
        log.info("话费充值表同步,20元北岸唐唱音乐包");
        memberService.feeRechargeAdd("00210Q6","698039020108689345",null);
    }

    @Test
    public void comic() {
        log.info("话费充值表同步,骏伯咪咕动漫联合会员");
        memberService.feeRechargeAdd("","330000132",null);
    }

    @Test
    public void recharg() {
        log.info("话费充值，到账");
        memberService.feeRecharge(null);
    }

}
