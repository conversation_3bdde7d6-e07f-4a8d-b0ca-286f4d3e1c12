package org.jeecg;

import com.eleven.cms.queue.MiguRingFtpUploadMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.util.JacksonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @datetime 2024/11/1 13:11
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RabbitMQMsgSenderTest {

    @Autowired
    private RabbitMQMsgSender rabbitMQMsgSender;

    @Test
    public void testSendSchSpecialProductSubMsg() {
        rabbitMQMsgSender.sendSchSpecialProductSubMsg("123", "456");
    }

    @Test
    public void testSendMiguRingFtpUploadMessage() {
        MiguRingFtpUploadMessage miguRingFtpUploadMessage = new MiguRingFtpUploadMessage();
        miguRingFtpUploadMessage.setMobile("19834912362");
        miguRingFtpUploadMessage.setTag("014X0I9");
        rabbitMQMsgSender.sendMiguRingFtpUploadMessage(miguRingFtpUploadMessage);
    }

    @Test
    public void testReportPageOrderMessage() {
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("mobile", "1234");
        rabbitMQMsgSender.reportPageOrderMessage(objectNode);
    }
}