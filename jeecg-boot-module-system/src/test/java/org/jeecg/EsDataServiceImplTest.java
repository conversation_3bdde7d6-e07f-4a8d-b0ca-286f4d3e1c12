package org.jeecg;

import com.eleven.cms.es.entity.EsSubStatistics;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.entity.TestEsSub;
import com.eleven.cms.es.repository.EsSubscribeRepository;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class EsDataServiceImplTest {

    @Autowired
    ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    EsSubscribeRepository esSubscribeRepository;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    IEsDataService esDataService;
    @Autowired
    RestHighLevelClient restHighLevelClient;

    public static final ObjectMapper mapper = new ObjectMapper().registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .enable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    
    @Test
    public void queryRecentAllBizByMobile() {
        final List<EsSubscribe> esSubscribes = esDataService.queryRecentAllBizByMobile("18285746413",3L);
        System.out.println("esSubscribes = " + esSubscribes);
        final List<EsSubscribe> esSubscribes1 = esDataService.queryRecentAllBizByMobile("17830272381",3L);
        System.out.println("esSubscribes1 = " + esSubscribes1);
        final List<EsSubscribe> esSubscribes2 = esDataService.queryRecentAllBizByMobile("13452426167",3L);
        System.out.println("esSubscribes2 = " + esSubscribes2);
    }

    @Test
    public void queryAllBizByMobile() {
        final List<Map<String, Object>> allBizByMobile = esDataService.queryAllBizByMobile("13438828200");
        System.out.println("allBizByMobile = " + allBizByMobile);
    }

    @Test                                                                                                     
    public void wildcardQuery() throws IOException {
        SearchRequest searchRequest = new SearchRequest("subscribe");
        //searchRequest.types("_doc");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.wildcardQuery("mobile", "1343882820*"));
        searchRequest.source(searchSourceBuilder);
        final SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        System.out.println("searchResponse = " + searchResponse);

    }

    @Test
    public void saveOrUpdate(){
        final String id = "1753301584399863810";
        final Subscribe subscribe = subscribeService.getById(id);
        esDataService.saveOrUpdateSubscribe(subscribe);
        final EsSubscribe esSubscribe = elasticsearchRestTemplate.get(id, EsSubscribe.class);
        System.out.println(esSubscribe);
    }

    @Test
    public void updateVerifyStatusDbAndEs(){
        subscribeService.updateVerifyStatusDbAndEs("1767071955171147777",0);
    }

    @Test
    public void aggByChannel(){
        final YearMonth targetYearMonth = YearMonth.now().minusMonths(2);
        String channel = "014X02G";
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                        from(targetYearMonth.atDay(1).toString()).
                        to(targetYearMonth.atEndOfMonth().toString())
                        .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("channel.keyword", channel));
        builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));
        builder.must(QueryBuilders.termQuery("isp.keyword", MobileRegionResult.ISP_YIDONG));
        TermsAggregationBuilder tab = AggregationBuilders.terms("subChannelGroup").field("subChannel.keyword").size(1000)
                .subAggregation(AggregationBuilders.terms("provinceGroup").field("province.keyword").size(1000)
                                        .subAggregation(AggregationBuilders.terms("verifyStatusGroup").field("price")
                                                .order(BucketOrder.key(true))));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                addAggregation(tab).
                withSourceFilter(new FetchSourceFilter(new String[]{""}, new String[]{""})).  //不查询任何结果
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(0); //不查询具体数据
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class);
        List<? extends Terms.Bucket> subChannelBuckets = ((Terms) searchHits.getAggregations().asMap().get("subChannelGroup")).getBuckets();
        List<EsSubStatistics> statisticsByChannel = new ArrayList<>();
        subChannelBuckets.forEach(subChannelBucket -> {
            List<? extends Terms.Bucket> provinceBuckets = ((Terms) subChannelBucket.getAggregations().asMap().get("provinceGroup")).getBuckets();
            provinceBuckets.forEach(provinceBucket -> {
                EsSubStatistics ss = new EsSubStatistics();
                ss.setChannel(channel);
                ss.setBizType(BizConstant.getBizTypeByMiguChannel(channel));
                ss.setSubChannel(subChannelBucket.getKeyAsString());
                ss.setProvince(provinceBucket.getKeyAsString());
                ss.setTotal(provinceBucket.getDocCount());
                ss.setSubMonth(targetYearMonth.toString());
                ss.setStatsMonth(YearMonth.now().toString());
                List<? extends Terms.Bucket> verifyStatusBuckets = ((Terms) provinceBucket.getAggregations().asMap().get("verifyStatusGroup")).getBuckets();
                verifyStatusBuckets.forEach(verifyStatusBucket -> {
                    if(Integer.parseInt(verifyStatusBucket.getKeyAsString()) == 1){
                        ss.setSucc(verifyStatusBucket.getDocCount());
                    }
                });
                statisticsByChannel.add(ss);
            });
        });
        System.out.println(statisticsByChannel.size());
        System.out.println(statisticsByChannel);
    }

    @Test
    public void aggAllChannel(){
        final YearMonth targetYearMonth = YearMonth.now().minusMonths(1);
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                        from(targetYearMonth.atDay(1).toString()).
                        to(targetYearMonth.atEndOfMonth().toString())
                .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));
        builder.must(QueryBuilders.termQuery("isp.keyword", MobileRegionResult.ISP_YIDONG));
        TermsAggregationBuilder tab = AggregationBuilders.terms("channelGroup").field("channel.keyword").size(1000)
                        .subAggregation(AggregationBuilders.terms("verifyStatusGroup").field("price")
                                .order(BucketOrder.key(true)));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                addAggregation(tab).
                withSourceFilter(new FetchSourceFilter(new String[]{""}, new String[]{""})).  //不查询任何结果
                build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(0); //不查询具体数据
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class);
        List<? extends Terms.Bucket> channelBuckets = ((Terms) searchHits.getAggregations().asMap().get("channelGroup")).getBuckets();
        List<EsSubStatistics> statisticsByChannel = new ArrayList<>();
        channelBuckets.forEach(channelBucket -> {
                EsSubStatistics ss = new EsSubStatistics();
                ss.setChannel(channelBucket.getKeyAsString());
                ss.setBizType(BizConstant.getBizTypeByMiguChannel(channelBucket.getKeyAsString()));
                ss.setTotal(channelBucket.getDocCount());
                ss.setSubMonth(targetYearMonth.toString());
                ss.setStatsMonth(YearMonth.now().toString());
                List<? extends Terms.Bucket> verifyStatusBuckets = ((Terms) channelBucket.getAggregations().asMap().get("verifyStatusGroup")).getBuckets();
                verifyStatusBuckets.forEach(verifyStatusBucket -> {
                    if(Integer.parseInt(verifyStatusBucket.getKeyAsString()) == 1){
                        ss.setSucc(verifyStatusBucket.getDocCount());
                    }
                });
                statisticsByChannel.add(ss);

        });
        System.out.println(statisticsByChannel.size());
        System.out.println(statisticsByChannel);
    }



    @Test
    public void testTransTestEsSubData(){
        subscribeService.lambdaQuery()
                .between(Subscribe::getCreateTime,
                        LocalDate.of(2024, 1, 31).atStartOfDay(),
                        LocalDate.of(2024, 1, 31).atTime(LocalTime.MAX))
                .list()
                .forEach(subscribe -> {
                    try {
                        esDataService.transTestEsSub(subscribe);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
    }

    @Test
    public void testTransTestEsOrderData(){
        subscribeService.lambdaQuery()
                .between(Subscribe::getCreateTime,
                        LocalDate.of(2024, 1, 31).atStartOfDay(),
                        LocalDate.of(2024, 1, 31).atTime(LocalTime.MAX))
                .last(" limit 10")
                .list()
                .forEach(subscribe -> {
                    try {
                        esDataService.transTestEsOrder(subscribe);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
        esDataService.modifyAndfindTestEsOrder("1752361044212781058");
    }

    @Test
    public void findTestEsOrder(){
        esDataService.modifyAndfindTestEsOrder("1752361044212781058");
    }

    @Test
    public void testUpdatePattern(){
        UpdateRequest updateRequest = new UpdateRequest("test_sub*", "1752361044212781058");
        TestEsSub testEsSub = new TestEsSub();
        testEsSub.setExtra("测试更新模式");
        try {
            String source = mapper.writeValueAsString(testEsSub);
            updateRequest.doc(source, XContentType.JSON);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        // 执行请求
        try {
            UpdateResponse updateResponse = restHighLevelClient.update( updateRequest, RequestOptions.DEFAULT);
            log.info("根据id更新EsSubscribe状态,响应:{}",updateResponse);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 支持类似mysql in查询
     */
    @Test
    public void sqlInLikeQuery(){
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                        from(LocalDate.now().toString()).
                        to(LocalDate.now().toString())
                .format("yyyy-MM-dd"));
        //查询条件(词条查询：对应ES query里的terms)
        builder.must(QueryBuilders.termsQuery("channel.keyword", "014X02G"));
        builder.must(QueryBuilders.termsQuery("status", new int[]{1}));
        //创建查询条件构建器SearchSourceBuilder(对应ES外面的大括号)
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withQuery(builder).build();
        nativeSearchQuery.setMaxResults(100); 
        //查询,获取查询结果
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class);
        //获取总记录数
        long totalHits = searchHits.getTotalHits();
        System.out.println("totalHits = " + totalHits);
        //获取值返回
        searchHits.getSearchHits().stream().map(SearchHit::getContent).forEach(System.out::println);
    }

    @Test
    public void subscribeStatisticsQyclVerifyStatusToday() {
        final Map<Long, Long> statYrjy = esDataService.subscribeStatisticsQyclVerifyStatusToday("QYCL_GR");
        System.out.println("stat = " + statYrjy);
        final Map<Long, Long> statMh = esDataService.subscribeStatisticsQyclVerifyStatusToday("QYCL_GR_MH");
        System.out.println("stat = " + statMh);
    }


    //@Test
    //public void findLast6MonthEsSubscribeByMobile() {
    //    final List<EsSubscribe> list = esDataService.findLast3MonthEsSubscribeByMobile("13699402402", );
    //    System.out.println("list = " + list.size());
    //}

    @Test
    public void subscribeMonitoAllChannel() {
        final Map<String, Boolean> stringBooleanMap = esDataService.subscribeMonitoAllChannel(24L,10);
        System.out.println(stringBooleanMap);
    }


    public static void main(String[] args) {
        System.out.println((10L * 9 * 8 * 7 * 6 * 5 * 4 * 3 * 2 * 1) / (4L * 3 * 2 * 1 * 6 * 5 * 4 * 3 * 2 * 1));
    }

}