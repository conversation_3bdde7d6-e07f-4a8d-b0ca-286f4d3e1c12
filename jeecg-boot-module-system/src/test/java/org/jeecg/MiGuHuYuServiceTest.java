package org.jeecg;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.remote.MiGuHuYuService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.MiGuHuYuMonthlyResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;

/**
 * 咪咕互娱查询包月状态
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5 10:49
 **/
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class MiGuHuYuServiceTest {
    @Autowired
    MiGuHuYuService miGuHuYuService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IEsDataService esDataService;

    @Test
    public void queryMonthly(){
        List<String> list= Lists.newArrayList();
        list.add("13999193554");
        list.forEach(mobile->{
            boolean sub= miGuHuYuService.queryMonthly(mobile,"HYQD_HS");
            log.info("手机号:{},包月状态:{}",mobile,sub);
        });

    }



    @Test
    public void updateSubVerfyStatusById(){
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\退订.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                if(!stringList.isEmpty()){
                    stringList.forEach(mobile->{
                        Subscribe sub=subscribeService.lambdaQuery().select(Subscribe::getId,Subscribe::getMobile,Subscribe::getChannel).eq(Subscribe::getMobile,mobile.trim()).in(Subscribe::getChannel,"HYQD_HS","HYQD_O_HS","HYQD_MAIHE").eq(Subscribe::getStatus,1).eq(Subscribe::getVerifyStatus, SUBSCRIBE_MONTH_VERIFY_NONE).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
                        if(sub!=null){
                            boolean isMember= miGuHuYuService.queryMonthly(mobile,sub.getChannel());
                            Integer status = isMember? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                            subscribeService.lambdaUpdate().eq(Subscribe::getId,sub.getId()).set(Subscribe::getVerifyStatus,status).update();
                            esDataService.updateSubVerfyStatusById(sub.getId(),status);
                        }
                    });
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
