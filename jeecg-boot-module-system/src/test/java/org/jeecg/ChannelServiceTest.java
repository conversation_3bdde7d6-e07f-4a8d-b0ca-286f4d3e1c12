package org.jeecg;

import com.alibaba.fastjson.JSON;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.MiguChannelConfig;
import com.eleven.cms.config.YidongVRCrackProperties;
import com.eleven.cms.config.YidongVrbtCrackProperties;
import com.eleven.cms.entity.Channel;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.Music;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IMusicService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MusicVo;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ChannelServiceTest {

    @Autowired
    IChannelService channelService;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    IMusicService musicService;

    @Autowired
    ISubscribeService subscribeService;

    @Autowired
    BizProperties bizProperties;
    @Autowired
    YidongVrbtCrackProperties yidongVrbtCrackProperties;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    YidongVRCrackProperties yidongVRCrackProperties;

//    @Test
//    public void findByTitle() {
//        final Channel yjyj = channelService.findByTitle("not exsits");
//        System.out.println("yjyj = " + yjyj);
//    }
//
//    @Test
//    public void exists() {
//        final boolean yjyj = channelService.aopProxyTest("yrjy");
//        System.out.println("yjyj = " + yjyj);
//    }
//
//    @Test
//    public void redisGetNull(){
//        Integer not_exits_key = (Integer)redisUtil.get("not_exits_key");
//        System.out.println("not_exits_key = " + not_exits_key);
//    }
//    @Test
//    public void selectByCondition(){
//        List<Map<String, Object>> list = channelService.selectByCondition();
//        System.out.println("list = " + JSONObject.toJSONString(list));
//    }


    @Test
    public void test() {
//        Subscribe subscribe = new Subscribe();
//        subscribe.setId("111");
//        subscribe.setSubChannel("test1");
//        subscribe.setMobile("15888888888");
//        subscribe.setDeviceInfo("111");
//        channelService.AdEffectFeedbackNew(subscribe,1);


        String excelPath = "D:/歌曲.xls";
        File file = new File(excelPath);
        ImportParams params = new ImportParams();
        params.setTitleRows(2);
        params.setHeadRows(1);
        params.setNeedSave(true);
        try {
            List<Music> list = ExcelImportUtil.importExcel(file, Music.class, params);
            list.forEach(music -> {
                MusicVo musicVo = musicService.findVrbtInfoByCopyrightId(music.getCopyrightId());
                if (StringUtils.isNotBlank(music.getLtRingId()))
                    musicService.lambdaUpdate().eq(Music::getId, musicVo.getId()).set(Music::getLtRingId, music.getLtRingId()).update();
            });
            System.out.println(list.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
//    @Test
//    public void uploadData(){
//        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId("15984556074", "1000039020220112201903254002");
//        System.out.println(JSON.toJSONString(subscribe));
////        channelService.AdEffectFeedback(subscribe, subscribe.getStatus());
//    }

    @Test
    public void initData() {

//        bizProperties.getMiguChannelConfigMap().forEach((anyChannel, miguChannelConfig) -> {
//            CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(anyChannel);
//            if (cmsCrackConfig == null) {
//                cmsCrackConfig = new CmsCrackConfig();
//                cmsCrackConfig.setChannel(anyChannel);
//                cmsCrackConfig.setServiceId(miguChannelConfig.getServiceId());
//                cmsCrackConfig.setLoginSecretKey(miguChannelConfig.getLoginSecretKey());
//                cmsCrackConfig.setSignatureSecretKey(miguChannelConfig.getSignatureSecretKey());
//                cmsCrackConfig.setSmsSignDatang(miguChannelConfig.getSmsSignDatang());
//                cmsCrackConfig.setSmsSuffix(miguChannelConfig.getSmsSuffix());
//                cmsCrackConfig.setForwardDomain(miguChannelConfig.getSmsSuffix());
//                cmsCrackConfig.setBizType(BizConstant.getBizTypeByMiguChannel(anyChannel));
//                cmsCrackConfigService.save(cmsCrackConfig);
//            }
//        });



//        yidongVrbtCrackProperties.getChannelConfigMap().forEach((anyChannel, config) -> {
//            CmsCrackConfig cmsCrackConfig = new CmsCrackConfig();
//            cmsCrackConfig.setChannel(anyChannel);
//            cmsCrackConfig.setCpid(config.getCpid());
//            cmsCrackConfig.setFee(config.getFee());
//            cmsCrackConfig.setPayCode(config.getPaycode());
//            cmsCrackConfig.setType(config.getType());
//            cmsCrackConfig.setLogTag(config.getLogTag());
//            MiguChannelConfig miguChannelConfig = bizProperties.getMiguChannelConfigMap().get(anyChannel);
//            if(miguChannelConfig!= null) {
//                cmsCrackConfig.setServiceId(miguChannelConfig.getServiceId());
//                cmsCrackConfig.setLoginSecretKey(miguChannelConfig.getLoginSecretKey());
//                cmsCrackConfig.setSignatureSecretKey(miguChannelConfig.getSignatureSecretKey());
//                cmsCrackConfig.setSmsSignDatang(miguChannelConfig.getSmsSignDatang());
//                cmsCrackConfig.setSmsSuffix(miguChannelConfig.getSmsSuffix());
//            }
//            cmsCrackConfig.setBizType(BizConstant.getBizTypeByMiguChannel(anyChannel));
//            cmsCrackConfigService.save(cmsCrackConfig);
//        });

//        yidongVRCrackProperties.getChannelConfigMap().forEach((anyChannel, config) -> {
//            CmsCrackConfig cmsCrackConfig = new CmsCrackConfig();
//            cmsCrackConfig.setChannel(anyChannel);
//            cmsCrackConfig.setCpid(config.getCpid());
//            cmsCrackConfig.setFee(config.getFee());
//            cmsCrackConfig.setPayCode(config.getPaycode());
//            cmsCrackConfig.setType(config.getType());
//            cmsCrackConfig.setItemId(config.getItemid());
//            cmsCrackConfig.setLogTag(config.getLogTag());
//            MiguChannelConfig miguChannelConfig = bizProperties.getMiguChannelConfigMap().get(anyChannel);
//            if(miguChannelConfig!= null) {
//                cmsCrackConfig.setServiceId(miguChannelConfig.getServiceId());
//                cmsCrackConfig.setLoginSecretKey(miguChannelConfig.getLoginSecretKey());
//                cmsCrackConfig.setSignatureSecretKey(miguChannelConfig.getSignatureSecretKey());
//                cmsCrackConfig.setSmsSignDatang(miguChannelConfig.getSmsSignDatang());
//                cmsCrackConfig.setSmsSuffix(miguChannelConfig.getSmsSuffix());
//            }
//            cmsCrackConfig.setBizType(BizConstant.getBizTypeByMiguChannel(anyChannel));
//            cmsCrackConfigService.save(cmsCrackConfig);
//        });

    }


}