package org.jeecg;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.response.AlipayFundAccountQueryResponse;
import com.alipay.api.response.AlipayFundTransCommonQueryResponse;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.entity.AliSignChargingOrder;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.service.IAliSignChargingOrderService;
import com.eleven.cms.service.IAliSignRecordService;
import com.eleven.cms.service.IAlipayService;
import com.eleven.cms.service.ISmsModelService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AlipayServiceTest {
    @Autowired
    private IAlipayService alipayService;


    @Test
    public void aliPayTransferFee(){
        String outBizNo = IdWorker.get32UUID();
        String amount="0.10";
        //用户标识
        String identity="2088912984222313";
        //用户标识类型
        String identityType="ALIPAY_USER_ID";
        String businessType="M_R_CERTIFICATE";
        //业务备注
        String remark="1";
        //收款支付宝账号姓名   identity_type=ALIPAY_LOGON_ID 时，本字段必填
        String name="";
        //是否展示付款方别名
        String payerShowNameUseAlias="false";
        AlipayFundTransUniTransferResponse result=alipayService.aliPayTransferFeeNew( outBizNo, amount,identityType,identity,name,businessType,remark,payerShowNameUseAlias);
        String jsonObject = JSONObject.toJSONString(result);
        log.info("订单号:{},响应:{}",outBizNo,jsonObject);
    }

    @Test
    public void aliPayQueryTransferFeeNew(){
        String outBizNo ="54c66952f485e54631774b6fc7126b2a";
        String businessType="M_R_CERTIFICATE";
        AlipayFundTransCommonQueryResponse result=alipayService.aliPayQueryTransferFeeNew( outBizNo,businessType);
        log.info("订单号:{},响应:{}",outBizNo,result);
    }

    @Test
    public void aliPayQueryAccountBalance(){
        String businessType="M_R_CERTIFICATE";
        AlipayFundAccountQueryResponse result=alipayService.aliPayQueryAccountBalance(businessType);
        log.info("result:{}",result);
    }

    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    private ISmsModelService smsModelService;
    @Test
    public void monthDeductionMsgs()  {
        log.info("发送提醒短信");
        this.monthDeductionMsg();
    }


    private Boolean monthDeductionMsg() {
        List<AliSignRecord> aliSignRecordList = aliSignRecordService.lambdaQuery()
                .select(AliSignRecord::getId, AliSignRecord::getMobile)
                .eq(AliSignRecord::getNextDeductTime, LocalDateTime.of(LocalDate.now().plusDays(2), LocalTime.MIN))
                .ne(AliSignRecord::getSendDeductMsgDay, formatYearMonthDay(LocalDateTime.now()))
                .eq(AliSignRecord::getSignStatus,"1")
                .last("LIMIT 1000")
                .list();
        boolean isFinish = aliSignRecordList==null || aliSignRecordList.size()==0;
        if(isFinish){
            log.info("定时任务-支付宝签约协议周期扣款前三天发送扣款提醒短信结束");
            return isFinish;
        }
        log.info("定时任务-支付宝签约协议周期扣款前三天发送扣款提醒短信");
//        aliSignRecordList.forEach(aliSignRecord -> {
//            try {
//                Map<String,String> smsMap= Maps.newHashMap();
//                Boolean isSms=smsModelService.sendSms(aliSignRecord.getMobile(),"IVR_ALIPAY","IVR_ALIPAY","4",smsMap);
//                if(isSms){
//                    aliSignRecord.setSendDeductMsgDay(this.formatYearMonthDay(LocalDateTime.now()));
//                    aliSignRecordService.updateById(aliSignRecord);
//                }
//            } catch (Exception e) {
//                log.error("id:{},手机号:{},支付宝签约协议周期扣款前三天发送扣款提醒短信异常:{}",aliSignRecord.getId(),aliSignRecord.getMobile(),e.getMessage());
//                e.printStackTrace();
//            }
//        });
        return false;
    }
    private String formatYearMonthDay(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, "yyyy-MM-dd");
    }
    private String formatFullTime(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(dateTimeFormatter);
    }

    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Test
    public void aliPayCallback()  {
        log.info("支付宝回传测试");
        aliSignChargingOrderService.lambdaQuery().in(AliSignChargingOrder::getMobile,"***********").list().forEach( aliSignChargingOrder ->{
            aliSignChargingOrderService.aliPayCallback(aliSignChargingOrder.getOrderNo(),aliSignChargingOrder.getMobile(),aliSignChargingOrder.getBusinessType());
        });


    }
}
