package org.jeecg;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.eleven.cms.es.service.impl.EsDataServiceImpl.INDEX_COORDINATES;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * <AUTHOR>
 * @datetime 2025/2/14 16:48
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DataTest {

    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;


    @Test
    public void t2() {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.rangeQuery("fmtCreateTime").
                from(LocalDate.of(2022, 01, 01)).
                to(LocalDate.of(2025, 02, 20))
                .format("yyyy-MM-dd"));
        builder.must(QueryBuilders.termQuery("status", SUBSCRIBE_STATUS_SUCCESS));

        String str =
//                "GZYD_LLB_JC5G,GZYD_HJHY_TYHD,GZYD_SXK,GZYD_SXX,GZYD_SPCL_CXTH," +
//                "GZYD_SXK_SJDTYHD,GZYD_QX20G_LLTB,GZYD_VRBT_XSZS,GZYD_LLB_12Y20G,GZYD_BJHY_10GLLB," +
//                "GZYD_PLUS_HYYB,GZYD_SXX_HY,AIDOU_GZYD_20"
                        "GZYD_KY_DLB,GZYD_YDYP_HJHY," +
                "GZYD_ZSHY,GZYD_ZSHY_SYTH,GZYD_MGZQ_QQB,GZYD_QXX_PLUS,GZYD_QXX_BJHY," +
                "GZYD_XSZS,GZYD_TNLY_HYLB,GZYD_KDX_ZSB,GZYD_QYJXB,GZYD_NSXK,GZYD_TSQY"
                ;
        builder.must(new TermsQueryBuilder("channel.keyword", str.split(",")));

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).
                withSourceFilter(new FetchSourceFilter(new String[]{"mobile", "channel"}, new String[]{""})).  //不查询任何结果
                        build();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(1100000);
        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class, INDEX_COORDINATES);
        System.out.println("总数：" + searchHits.getTotalHits());
        List<EsSubscribe> originalData = new ArrayList<>();
        for (SearchHit<EsSubscribe> searchHit : searchHits) {
            EsSubscribe content = searchHit.getContent();
            content.setSource(content.getMobile());
            content.setMobile(DesensitizedUtil.mobilePhone(searchHit.getContent().getMobile()));
            originalData.add(content);
        }

        String filePath = "C:\\Users\\<USER>\\Desktop\\test.txt"; // 替换为你的文件路径
        List<String> mobileList = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                mobileList.add(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        for (String data : mobileList) {
            for (int i = 0; i < originalData.size(); i++) {
                EsSubscribe esSubscribe = originalData.get(i);
                if (esSubscribe.getMobile().equals(data)) {
                    System.out.println("1,"+ esSubscribe.getSource() + "," + esSubscribe.getChannel());
                    break;
                }
                if (i == (originalData.size() - 1)) {
                    System.out.println("0," + "-" + "," + esSubscribe.getChannel());
                }
            }
        }
    }

    @Test
    public void t1() {
        String filePath = "C:\\Users\\<USER>\\Desktop\\test.txt"; // 替换为你的文件路径

        Set<String> mobileSet = new HashSet<>();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                mobileSet.add(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        int result = subscribeService.count(new LambdaQueryWrapper<Subscribe>()
                .eq(Subscribe::getChannel, "QYCL_GR_MH")
                .between(Subscribe::getCreateTime, "2025-01-01 00:00:00", "2025-01-31 23:59:59")
                .in(Subscribe::getMobile, mobileSet)
                .eq(Subscribe::getStatus, 1)
        );
        log.info("result:{}", result);
    }

    @Test
    public void t0() {
//        Subscribe subscribe = subscribeService.getById("");
//        subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//        subscribe.setResult("订购成功");
//        subscribeService.updateSubscribeDbAndEs(subscribe);
    }
}
