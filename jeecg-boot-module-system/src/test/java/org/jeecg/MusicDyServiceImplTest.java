package org.jeecg;

import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IColumnMusicService;
import com.eleven.cms.service.IColumnService;
import com.eleven.cms.service.IMusicDyService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class MusicDyServiceImplTest {

    @Autowired
    IMusicDyService musicDyService;
    @Autowired
    IColumnMusicService columnMusicService;
    @Autowired
    IColumnService columnService;
    @Autowired
    MiguApiService miguApiService;

    /**
     * 歌曲信息抓取,根据第三方给的歌曲excel获取歌曲图片和视频播放地址,输出json格式(彩铃中心的版权id)
     */
    @Test
    public void importFromExcelFile() {
        String filePath = "E:\\data.xls";
        musicDyService.importFromExcelFile(filePath);
    }


    //@Test
    //public void validate() {
    //    final boolean validate1 = musicDyService.validate("600926000005513095");
    //    System.out.println("validate1 = " + validate1);
    //    final boolean validate2 = musicDyService.validate("112131414");
    //    System.out.println("validate2 = " + validate2);
    //
    //}

    //@Test
    //public void syncMusic() {
    //    musicDyService.syncMusic();
    //}


    /**
     * 同步歌曲产品信息(播放地址,图片地址,有效期,是否有效)
     */

    @Test
    public void syncMusicProduct() {
        musicDyService.list().forEach(musicDy -> {
             musicDyService.fillProductInfo(musicDy);
             musicDyService.updateById(musicDy);
        });
    }




}