package org.jeecg;

import com.alipay.api.response.AlipayUserAgreementQueryResponse;
import com.eleven.cms.config.WoyinyueConfigProperties;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.*;
import com.eleven.cms.util.DateUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * pptv测试接口
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PPTVApiServiceTest {
    @Autowired
    IHttpRequestService httpRequestService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IPPTVApiService pptvApiService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    private IAlipayService alipayService;
    @Test
    public void create() {
        List<AliSignRecord> aliSignRecord = aliSignRecordService.lambdaQuery().in(AliSignRecord::getSignStatus, "1","3").between(AliSignRecord::getCreateTime, DateUtil.parseString("2023-03-31 11:30:15", DateUtil.FULL_TIME_SPLIT_PATTERN), DateUtil.parseString("2023-03-31 12:30:15", DateUtil.FULL_TIME_SPLIT_PATTERN)).list();
        aliSignRecord.forEach(item -> {
            AlipayUserAgreementQueryResponse response = alipayService.aliTradePayQuery(item.getExternalAgreementNo(), item.getBusinessType());
            log.info("externalAgreementNo :{} ,nextdeducttime :{} response:{},", item.getExternalAgreementNo(), response.getNextDeductTime(),response);
        });
    }
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Test
    public void flow() throws IOException {
//        Result<?> result= dgydFlowPackOrderService.sendSms("***********","KDX");
        Subscribe subscribe=new Subscribe();
        subscribe.setMobile("***********");
        subscribe.setSmsCode("8258");
        subscribe.setTransactionId("1613081170261925889");
        Result<?> result= subscribeService.dongguanMobileLLBOpenUp(subscribe);
        log.info("东莞移动流量包响应结果==>result:{}",result);
    }
    @Test
    public void pptv() throws IOException {
//        String outOrderId= UUID.randomUUID().toString().replaceAll("-", "");
//        pptvApiService.pptvRightsReceive("***********",outOrderId,"");
    }
    /**
     * 生成签名
     * @param dataNode 待签名数据
     * @return 签名
     */
    public static String getSigns(final ObjectNode dataNode,String keys){
        Iterator<Map.Entry<String, JsonNode>> jsonNode = dataNode.fields();
        StringBuffer valueStr = new StringBuffer();
        while (jsonNode.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNode.next();
            String key =  entry.getKey();
            String value =  entry.getValue().asText();
            valueStr.append(key+value);
        }
        valueStr.append(keys);
        String md5 = valueStr.toString();
        System.out.println("md5:"+md5);
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return sign;
    }
}
