package org.jeecg;

import com.eleven.cms.aivrbt.dto.*;
import com.eleven.cms.aivrbt.utils.TencentCloudAIUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2024/10/28 15:51
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TencentCloudAIUtilsTest {

    @Test
    public void testFuseFace() throws NoSuchAlgorithmException, InvalidKeyException {
        PicFuseFaceDTO fuseFaceDTO = new PicFuseFaceDTO();
        fuseFaceDTO.setProjectId("at_1851546636923293696");
        fuseFaceDTO.setModelId("mt_1851908997545537536");
        fuseFaceDTO.setRspImgType("url");

        List<PicFuseFaceMergeInfoDTO> mergeInfoDTOS = new ArrayList<>();
        PicFuseFaceMergeInfoDTO fuseFaceMergeInfoDTO = new PicFuseFaceMergeInfoDTO();
        fuseFaceMergeInfoDTO.setUrl("https://img1.baidu.com/it/u=2655125035,2668656891&fm=253&fmt=auto&app=120&f=JPEG?w=800&h=500");
        fuseFaceMergeInfoDTO.setTemplateFaceID("mt_1851908997545537536_1");
        mergeInfoDTOS.add(fuseFaceMergeInfoDTO);
        fuseFaceDTO.setMergeInfos(mergeInfoDTOS);

        Map<String, Object> extraInfoMap = new HashMap<>();
        extraInfoMap.put("手机号", "123456789");
        System.out.println(TencentCloudAIUtils.fuseFace(fuseFaceDTO, extraInfoMap));
    }

    @Test
    public void testListMaterial() throws NoSuchAlgorithmException, InvalidKeyException {
        MaterialListDTO materialListDTO = new MaterialListDTO();
        materialListDTO.setActivityId("at_1851546636923293696");
        materialListDTO.setMaterialId("mt_1851908997545537536");
        materialListDTO.setOffset(0);
        materialListDTO.setLimit(10);
        System.out.println(TencentCloudAIUtils.listMaterial(materialListDTO));
    }

    @Test
    public void testParseListMaterial() throws NoSuchAlgorithmException, InvalidKeyException {
        String text = "{\n" +
                "\t\"Response\": {\n" +
                "\t\t\"MaterialInfos\": [{\n" +
                "\t\t\t\"MaterialId\": \"mt_1855801148340690944\",\n" +
                "\t\t\t\"MaterialName\": \"双人照国风1-静态封面.png\",\n" +
                "\t\t\t\"MaterialStatus\": 21,\n" +
                "\t\t\t\"AuditResult\": \"\",\n" +
                "\t\t\t\"MaterialFaceList\": [{\n" +
                "\t\t\t\t\"FaceId\": \"mt_1855801148340690944_1\",\n" +
                "\t\t\t\t\"FaceInfo\": {\n" +
                "\t\t\t\t\t\"X\": 380,\n" +
                "\t\t\t\t\t\"Y\": 442,\n" +
                "\t\t\t\t\t\"Width\": 60,\n" +
                "\t\t\t\t\t\"Height\": 80\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"FaceId\": \"mt_1855801148340690944_2\",\n" +
                "\t\t\t\t\"FaceInfo\": {\n" +
                "\t\t\t\t\t\"Y\": 613,\n" +
                "\t\t\t\t\t\"Width\": 55,\n" +
                "\t\t\t\t\t\"Height\": 71,\n" +
                "\t\t\t\t\t\"X\": 170\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"CreateTime\": \"2024-11-11 10:34:07\",\n" +
                "\t\t\t\"UpdateTime\": \"2024-11-11 10:34:07\"\n" +
                "\t\t}],\n" +
                "\t\t\"Count\": 1,\n" +
                "\t\t\"RequestId\": \"54a14705-4e37-4730-b5fc-11e0c897851c\"\n" +
                "\t}\n" +
                "}";
        System.out.println(TencentCloudAIUtils.parseListMaterialResponse(text));
    }
    @Test
    public void testVideoFaceFusion() throws NoSuchAlgorithmException, InvalidKeyException {
        VideoFaceFusionDTO videoFaceFusionDTO = new VideoFaceFusionDTO();
        videoFaceFusionDTO.setProjectId("at_1851546590672703488");
        videoFaceFusionDTO.setModelId("mt_1854428772914946048");

        List<VideoFaceFusionMergeInfoDTO> mergeInfoDTOS = new ArrayList<>();
        VideoFaceFusionMergeInfoDTO videoFaceFusionMergeInfoDTO = new VideoFaceFusionMergeInfoDTO();
        videoFaceFusionMergeInfoDTO.setUrl("https://static.cdyrjygs.com/test/dabfca91-bd8f-4acb-a4bd-d446792afa1d.jpg");
        mergeInfoDTOS.add(videoFaceFusionMergeInfoDTO);
        videoFaceFusionDTO.setMergeInfos(mergeInfoDTOS);

        Map<String, Object> extraInfoMap = new HashMap<>();
        extraInfoMap.put("手机号", "123456789");
        System.out.println(TencentCloudAIUtils.videoFaceFusion(videoFaceFusionDTO, extraInfoMap));
    }

    @Test
    public void testQueryVideoFaceFusionJobId() throws NoSuchAlgorithmException, InvalidKeyException {
        String jobId = "00465614BAE99ADA289D1A3891BCDACE33B";

        Map<String, Object> extraInfoMap = new HashMap<>();
        extraInfoMap.put("手机号", "123456789");
        System.out.println(TencentCloudAIUtils.queryVideoFaceFusionJobId(jobId, extraInfoMap));
    }
}
