package org.jeecg;

import com.google.common.util.concurrent.RateLimiter;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Author: ye<PERSON><PERSON><PERSON>@qq.com
 * Date: 2024/3/29 15:44
 * Desc: Guava限流
 */
public class GuavaRateLimiter {

        public static ConcurrentHashMap<String, RateLimiter> resourceRateLimiter = new ConcurrentHashMap<String, RateLimiter>();
        //初始化限流工具RateLimiter
        static {
            createResourceRateLimiter("order", 50);
        }
        public static void createResourceRateLimiter(String resource, double qps) {
            if (resourceRateLimiter.contains(resource)) {
                resourceRateLimiter.get(resource).setRate(qps);
            } else {
                //创建限流工具，每秒发出50个令牌指令
                RateLimiter rateLimiter = RateLimiter.create(qps);
                resourceRateLimiter.putIfAbsent(resource, rateLimiter);
            }
        }
        public static void main(String[] args) {
            //for (int i = 0; i < 5000; i++) {
            //    new Thread(() -> {
            //        //如果获得令牌指令，则执行业务逻辑
            //        if (resourceRateLimiter.get("order").tryAcquire(10, TimeUnit.MICROSECONDS)) {
            //        } else {
            //            System.out.println("限流");
            //        }
            //    }).start();
            //}
            
        }

}
