package org.jeecg;

import com.eleven.cms.service.ICompanyWechatService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/9 15:05
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CompanyWechatServiceTest {
    @Autowired
    ICompanyWechatService companyWechatService;

    /**
     * 企业微信机器人测试
     * @throws InterruptedException
     */
    @Test
    public void sendMsg() throws InterruptedException {
        companyWechatService.sendMsg("","暴打小怪兽","TEST");
    }
}
