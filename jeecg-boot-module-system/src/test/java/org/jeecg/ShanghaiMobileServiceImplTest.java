package org.jeecg;

import com.eleven.cms.entity.ShydChargeOrder;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IQyclWxpayService;
import com.eleven.cms.service.IShydChargeOrderService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.shanghaimobile.properties.ShanghaiMobileBusiness;
import com.eleven.cms.shanghaimobile.service.IShanghaiMobileService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ShanghaiMobileServiceImplTest {

    @Autowired
    private IShanghaiMobileService shanghaiMobileService;

//    /**
//     * 上海移动订阅特惠包
//     */
//    @Test
//    void shanghaiMobileSubscribeDiscount() {
//        String phone="13661893646";
//        String ip = IPUtils.getIpAddr(HttpContextUtil.getHttpServletRequest());
//        String orerId="shyd"+DateUtil.formatForMiguGroupApi(LocalDateTime.now())+"_"+phone;
//        ShanghaiMobileReturn.MethodWhiteListReturn methodWhiteListReturn= shanghaiMobileService.postMethodWhiteListNumberJudge(phone,orerId,ip,"101200002110");
//        Boolean flag=false;
//        if("0000".equals(methodWhiteListReturn.getResponse().getErrorInfo().getCode())){
//            if(methodWhiteListReturn.getResponse().getRetInfo().getFlag()==1){
//                flag=true;
//            }
//        }
//        System.out.println("===========>flag:"+flag);
//        if(!flag){
//            methodWhiteListReturn= shanghaiMobileService.postMethodWhiteListNumberJudge(phone, orerId,ip,"101200002155");
//            if("0000".equals(methodWhiteListReturn.getResponse().getErrorInfo().getCode())){
//                if(methodWhiteListReturn.getResponse().getRetInfo().getFlag()==1){
//                    flag=true;
//                }
//            }
//            System.out.println("===========>flag:"+flag);
//            if(flag){
//                ShanghaiMobileReturn.BusinessJudgeReturn businessJudgeReturn= shanghaiMobileService.postMethodBusinessJudge(phone,orerId,ip,"390020010915");
//                Boolean judge=false;
//                if("0000".equals(businessJudgeReturn.getResponse().getErrorInfo().getCode())){
//                    if(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()>0){
//                        judge=true;
//                        for(ShanghaiMobileReturn.BusinessJudgeReturn.ResponseBean.RetInfoBean.CheckResultInfoBean checkResultInfo: businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()){
//                            if (!"0000".equals(checkResultInfo.getCode())) {
//                                judge=false;
//                            }
//                        }
//                    }
//                }
//                System.out.println("===========>judge:"+judge);
//                if(judge){
//                    ShanghaiMobileReturn.BusinessHandleReturn businessHandleReturn=  shanghaiMobileService.postMethodBusinessHandle(phone, orerId,ip,"390020010915,111000750170");
//                    Boolean check=false;
//                    if(businessHandleReturn.getResponse().getErrorInfo().getCode().equals("0000")){
//                        check=true;
//                    }
//                    System.out.println("===========>check:"+check);
//                }else{
//                    businessJudgeReturn= shanghaiMobileService.postMethodBusinessJudge(phone,orerId,ip,"390020010917");
//                    if("0000".equals(businessJudgeReturn.getResponse().getErrorInfo().getCode())){
//                        if(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()>0){
//                            judge=true;
//                            for(ShanghaiMobileReturn.BusinessJudgeReturn.ResponseBean.RetInfoBean.CheckResultInfoBean checkResultInfo: businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()){
//                                if (!"0000".equals(checkResultInfo.getCode())) {
//                                    judge=false;
//                                }
//                            }
//                        }
//                    }
//                    System.out.println("===========>judge:"+judge);
//                    if(judge){
//                        ShanghaiMobileReturn.BusinessHandleReturn businessHandleReturn=  shanghaiMobileService.postMethodBusinessHandle(phone, orerId,ip,"390020010917,111000750170");
//                        Boolean check=false;
//                        if(businessHandleReturn.getResponse().getErrorInfo().getCode().equals("0000")){
//                            check=true;
//                        }
//                        System.out.println("===========>check:"+check);
//                    }
//                }
//            }
//        }else{
//            ShanghaiMobileReturn.BusinessJudgeReturn businessJudgeReturn= shanghaiMobileService.postMethodBusinessJudge(phone,orerId,ip,"390020010915");
//            Boolean judge=false;
//            if("0000".equals(businessJudgeReturn.getResponse().getErrorInfo().getCode())){
//                if(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()>0){
//                    judge=true;
//                    for(ShanghaiMobileReturn.BusinessJudgeReturn.ResponseBean.RetInfoBean.CheckResultInfoBean checkResultInfo: businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()){
//                        if (!"0000".equals(checkResultInfo.getCode())) {
//                            judge=false;
//                        }
//                    }
//                }
//            }
//            System.out.println("===========>judge:"+judge);
//            if(judge){
//                ShanghaiMobileReturn.BusinessHandleReturn businessHandleReturn= shanghaiMobileService.postMethodBusinessHandle(phone,orerId,ip,"390020010915,111000744954");
//                Boolean check=false;
//                if(businessHandleReturn.getResponse().getErrorInfo().getCode().equals("0000")){
//                    check=true;
//                }
//                System.out.println("===========>check:"+check);
//            }else{
//                businessJudgeReturn= shanghaiMobileService.postMethodBusinessJudge(phone,orerId,ip,"390020010917");
//                if("0000".equals(businessJudgeReturn.getResponse().getErrorInfo().getCode())){
//                    if(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()>0){
//                        judge=true;
//                        for(ShanghaiMobileReturn.BusinessJudgeReturn.ResponseBean.RetInfoBean.CheckResultInfoBean checkResultInfo: businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()){
//                            if (!"0000".equals(checkResultInfo.getCode())) {
//                                judge=false;
//                            }
//                        }
//                    }
//                }
//                System.out.println("===========>judge:"+judge);
//                if(judge){
//                    ShanghaiMobileReturn.BusinessHandleReturn businessHandleReturn=  shanghaiMobileService.postMethodBusinessHandle(phone, orerId,ip,"390020010917,111000744954");
//                    Boolean check=false;
//                    if(businessHandleReturn.getResponse().getErrorInfo().getCode().equals("0000")){
//                        check=true;
//                    }
//                    System.out.println("===========>check:"+check);
//                }
//            }
//        }
//    }
//
    /**
     * 上海移动彩铃PLUS
     */
    @Test
    void shanghaiMobileVideoSound() {
        String phone="***********";
        //SHYD_DY13817966127_1677296189802 ***********
        Result<?> result= shanghaiMobileService.shangHaiMobileBusinessOrder(phone,"************",0, ShanghaiMobileBusiness.productIdMap.get("SHYD_XSNB"),"9010","SHYD***********1715134733256");
        log.info("上海移动视频彩铃=>result:{}",result);
    }
    @Autowired
    ISubscribeService subscribeService;

    @Autowired
    private IShydChargeOrderService shydChargeOrderService;
    @Test
    public void fixSubscribeStatus(){
        List<String> stringList = null;
        try {
            stringList = FileUtils.readLines(new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Map<String, String> stringMap = Maps.newHashMap();
        stringList.forEach(str->{
            String[] strMap= str.split("  ");
            stringMap.put(strMap[0],strMap[1]);
        });
        stringMap.forEach((key, value) -> {
            Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,key).eq(Subscribe::getStatus,1).in(Subscribe::getChannel,"SHYD_DYSP","SHYD_XSSP","SHYD_XSSP_VS","SHYD_LLB","SHYD_SXXHY","SHYD_LLBJT","SHYD_XSMGSP","SHYD_XSNB","SHYD_BJHY","SHYD_HJSXX","SHYD_SXXSHB").orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(subscribe!=null){
                subscribeService.updateStatusResultByIdAnd2DbAndEs(subscribe.getId(), BizConstant.SUBSCRIBE_STATUS_FAIL, value+"更新");

                shydChargeOrderService.lambdaUpdate().eq(ShydChargeOrder::getMobile,key).eq(ShydChargeOrder::getChargeStatus,1).set(ShydChargeOrder::getChargeStatus,2).set(ShydChargeOrder::getCallbackStatus,2).update();
            }
        });
    }
    public static List<String> getContent(MultipartFile file){
        BufferedReader br = null;
        List<String> strings = Lists.newArrayList();
        String line = null;
        try {
            //根据文件路径创建缓冲输入流
            InputStream inputStream = file.getInputStream();
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
            br = new BufferedReader(inputStreamReader);
            String str = "";

            //循环读取文件的每一行，对需要修改的行进行修改，放入缓冲对象中
            while ((line = br.readLine()) != null) {
                //设置正则将多余空格都转为一个空格
                str = line;
                strings.add(str);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if (br != null) {// 关闭流
                try {
                    br.close();
                } catch (IOException e) {
                    br = null;
                }
            }
        }
        return strings;
    }
}
