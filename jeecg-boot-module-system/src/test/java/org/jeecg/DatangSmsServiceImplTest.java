package org.jeecg;

import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.ISmsValidateService;
import com.eleven.cms.service.impl.MessageNotifyService;
import com.eleven.cms.service.impl.SmsNotifyService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DatangSmsServiceImplTest {

    @Autowired
    IDatangSmsService datangSmsService;
    @Autowired
    ISmsValidateService smsValidateService;
    @Autowired
    SmsNotifyService smsNotifyService;
    @Autowired
    MessageNotifyService messageNotifyService;

    @Test
    public void sendQyclVerifyLimitNotify() {
        boolean result = smsNotifyService.sendQyclVerifyLimitNotify("QYCL_GR", "16");
        System.out.println("result = " + result);
        result = smsNotifyService.sendNotify("13438828200", "手机号:13550027280,内容:渠道号:HY_25_CW,省份:吉林即将到达限量,限量数250，现在总户数231");
        System.out.println("result = " + result);
    }

    @Test
    public void sendValidateNotice() {
        boolean result = datangSmsService.sendValidateNotice("13438828200", "【白金会员】", "客服电话：4006071007", "123456");
        System.out.println("result = " + result);
    }

    @Test
    public void sendSms() {
        //datangSmsService.sendSms("18080928200", "【休闲集舍】测试一下");
        datangSmsService.sendSms("13438828200", "【\"剧\"好看】测试一下");
    }

    @Test
    public void sendNotify() {
        messageNotifyService.sendTotalVergeNotify("18080928200", "HY_25_CW", 500, 400);
        messageNotifyService.sendTotalNotify("18080928200", "HY_25_CW", 500);
        messageNotifyService.sendProvinceNotify("18080928200", "HY_25_CW", "四川", 500);
        messageNotifyService.sendProvinceVergeNotify("18080928200", "HY_25_CW", "四川", 500, 400);
    }


    //@Test
    //public void sendSmsNew() {
    //    boolean result = datangSmsService.sendSms("13438828200", "【企业彩铃】你要的企业视频彩铃请点此 https://crbt.cdyrjygs.com/enterprise_rbt_v4/ 链接进行开通！");
    //    System.out.println("result = " + result);
    //}

    @Test
    public void sendFlashSms() {
        boolean result = datangSmsService.sendFlashSms("13438828200");
        System.out.println("result = " + result);
    }

    @Test
    public void create() {
        boolean result = smsValidateService.create("13438828200", MiguApiService.BIZ_BJHY_CHANNEL_CODE);
        System.out.println("result = " + result);

    }

    @Test
    public void batchSendSms() throws InterruptedException {
        String[] phoneNumbers = {
                "13438828200"
        };
        String msgContent = "您好，企业彩铃免制作费开通啦，点击链接定制专属企业彩铃 https://crbt.cdyrjygs.com/enterprise_rbt/ 如有疑问可咨询客服热线  4000355330";
        for (String phoneNumber : phoneNumbers) {
            datangSmsService.sendSms(phoneNumber, msgContent);
            TimeUnit.MILLISECONDS.sleep(200L);
        }

    }


    public static void main(String[] args) throws JsonProcessingException {
        String content = "[{\"srcid\":\"10685001\",\"mobile\":\"13438828200\",\"id\":\"102880911585900621500223488\",\"msgcontent\":\"通知测试短信上行通知测试短信上行通知测试短信上行通知测试短信上行通知测试短信上行通知测试短信上行通知测试短信上行通知测试短信上行通知测\",\"time\":\"20221028154547\"}]";
        final JsonNode jsonNode = new ObjectMapper().readTree(content);
        System.out.println(jsonNode.isArray());
        System.out.println(jsonNode.at("/0/id"));
        System.out.println(jsonNode.at("/0/mobile"));
        System.out.println(jsonNode.at("/0/msgcontent"));
    }
}