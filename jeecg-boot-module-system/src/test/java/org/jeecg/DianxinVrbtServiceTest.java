package org.jeecg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eleven.cms.config.DianxinVrbtProperties;
import com.eleven.cms.entity.CmsMusicTelecom;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.DianxinVrbtService;
import com.eleven.cms.remote.HuanqiuwangDianxinVrbtService;
import com.eleven.cms.service.ICmsMusicTelecomService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DianxinAuthUtils;
import com.eleven.cms.vo.DianxinResp;
import com.eleven.cms.vo.HuanqiuwangDianxinVrbtResult;
import com.eleven.cms.vo.MiGuHuYuQueryMember;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class DianxinVrbtServiceTest {

    @Autowired
    DianxinVrbtService dianxinVrbtService;

    @Autowired
    ICmsMusicTelecomService cmsMusicTelecomService;
    @Autowired
    HuanqiuwangDianxinVrbtService huanqiuwangDianxinVrbtService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    DianxinVrbtProperties dianxinVrbtProperties;

    static ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void getCompanyByPackageId() {
        System.out.println(dianxinVrbtProperties.getCompanyByPackageId("135999999999999000427"));
        System.out.println(dianxinVrbtProperties.getCompanyByPackageId("135999999999999000157"));
    }


    @Test
    public void validateNotifySign() {
        final boolean result = DianxinAuthUtils.validateNotifySign("2000000002044", "20201126145743", "423e5ff0aa54636d4359a78377d5bd8c",BizConstant.BIZ_DIANXIN_CHANNEL_DEFAULT);
        System.out.println("result = " + result);
    }

    @Test
    public void queryPackageExist() {
        boolean packageExist = dianxinVrbtService.queryPackageExist("***********", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE);
        System.out.println("packageExist = " + packageExist);


//        String s = dianxinVrbtService.unSubscribeByemp("***********", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_6);
//        System.out.println("s = " + s);
//        s = dianxinVrbtService.unSubscribeByemp("***********", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE);
//        System.out.println("s = " + s);

    }

    //@Test
    //public void unSubscribePackageByemp() {
    //    dianxinVrbtService.unSubscribeByemp("***********","135999999999999000052");
    //}

    @Test
    public void asyncOpenOrderSendRandom() throws InterruptedException {
        dianxinVrbtService.asyncOpenOrderSendRandom("***********",BizConstant.BIZ_DIANXIN_CHANNEL_DEFAULT);
        //TimeUnit.SECONDS.sleep(5L);
        //dianxinVrbtService.asyncOpenOrderSendRandom("***********");
    }

    @Test
    public void confirmOpenOrderLaunchedEx() throws InterruptedException {
        DianxinResp dianxinResp = dianxinVrbtService.newConfirmOpenOrderLaunchedEx("***********", "1", null,"AYY_AISPCL_HS");
        //DianxinResp dianxinResp = dianxinVrbtService.confirmOpenOrderLaunchedEx("***********", "1", "0");
        System.out.println("dianxinResp = " + dianxinResp);
    }

    @Test
    public void asyncOpenAccountOrderPackageBycrbt() {
        dianxinVrbtService.asyncOpenAccountOrderPackageBycrbt("***********", "4019",BizConstant.BIZ_DIANXIN_CHANNEL_DEFAULT);
    }

    @Test
    public void queryOrder() {
        dianxinVrbtService.queryOrder("8171e8975ca248f884fbcc4ebc6a8efe",BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
    }

    @Test
    public void addToneFreeOnProduct() {
        String channel ="AYY_AISPCL_HS";
        dianxinVrbtService.addToneFreeOnProduct("***********", "************",channel);
        //dianxinVrbtService.addToneFreeOnProduct("***********", "************", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_6);
        //dianxinVrbtService.addToneFreeOnProduct("***********", "************", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_6);
    }

    @Test
    public void videoQuery() {
        //System.out.println(dianxinVrbtService.videoQuery("************",BizConstant.BIZ_DIANXIN_CHANNEL_DEFAULT));
        //System.out.println(dianxinVrbtService.videoQuery("**********",BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF));
        System.out.println(dianxinVrbtService.videoQuery("910100350929",BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF));

    }

    @Test
    public void bind() throws UnsupportedEncodingException {
//        boolean bind = huanqiuwangDianxinVrbtService.bind("17319438705");
//        log.info("bindResult:" + bind);
        HuanqiuwangDianxinVrbtResult huanqiuwangDianxinVrbtResult = huanqiuwangDianxinVrbtService.order("17319438705");
        log.info(huanqiuwangDianxinVrbtResult.toString());
    }

    @Test
    public void setMusicTelecomImageAndVideo() {
        String company = "maihe_ai";
        String channel = BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_AI;

        List<CmsMusicTelecom> list = cmsMusicTelecomService.lambdaQuery().eq(CmsMusicTelecom::getSingerName, company).isNull(CmsMusicTelecom::getVrbtVideo).list();
        for (CmsMusicTelecom cmsMusicTelecom : list) {
            try {
                String result = dianxinVrbtService.videoQuery(cmsMusicTelecom.getResourceCode(), channel);
                JsonNode tree = objectMapper.readTree(result);
                JsonNode imageLists = tree.get("data").get("imageList");
                for (int i = 0; i < imageLists.size(); i++) {
                    String format = imageLists.get(i).get("format").asText();
                    if ("jpg".equals(format)) {
                        String path = imageLists.get(i).get("path").asText();
                        cmsMusicTelecom.setVrbtImg(path);
                        break;
                    }
                }
                JsonNode fileLists = tree.get("data").get("fileList");
                for (int i = 0; i < fileLists.size(); i++) {
                    String fileFormat = fileLists.get(i).get("file_format").asText();
                    if ("mp4".equals(fileFormat)) {
                        String filePath = fileLists.get(i).get("file_path").asText();
                        cmsMusicTelecom.setVrbtVideo(filePath);
                        break;
                    }
                }
                log.info("设置歌曲:{} 封面地址:{},播放地址{}", cmsMusicTelecom.getMusicName(), cmsMusicTelecom.getVrbtImg(), cmsMusicTelecom.getVrbtVideo());
            } catch (Exception e) {
                log.error("歌曲:{}获取封面和播放地址错误", cmsMusicTelecom.getMusicName(), e);
            }
        }
        cmsMusicTelecomService.updateBatchById(list);
    }

    //@Test
    //public void addBlackList() throws JsonProcessingException {
    //    blackListService.saveBlackList("***********","退订","VRBT");
    //}

    @Test
    public void queryAccountInfo() {
        dianxinVrbtService.queryAccountInfo("***********", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
    }

    @Test
    public void ismpOrderLaunchedThird() {
        dianxinVrbtService.ismpOrderLaunchedThird("***********", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
        System.out.println("debug");
    }

    @Test
    public void ismpVerifyCodeSend() {
        dianxinVrbtService.ismpVerifyCodeSend("***********", "8171e8975ca248f884fbcc4ebc6a8efe", BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
    }

    @Test
    public void ismpConfirmOrder() {
        dianxinVrbtService.ismpConfirmOrder("***********", "8171e8975ca248f884fbcc4ebc6a8efe","2411",BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
    }


    @Test
    public void ismpConfirmOrderThird() {
        dianxinVrbtService.ismpConfirmOrderThird("***********", "8171e8975ca248f884fbcc4ebc6a8efe", IdWorker.get32UUID(), BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
    }

    @Test
    public void queryH5Order() {
        dianxinVrbtService.queryH5Order("***********","8171e8975ca248f884fbcc4ebc6a8efe",BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
    }

    @Test
    public void unSubscribeByemp() {
        String channel ="AYY_AISPCL_HS";
        dianxinVrbtService.unSubscribeByemp("***********",channel);
    }

    /**
     * 根据专题 ID 查询相应的视频合成模板内容
     */
    @Test
    public void queryTemplateVideo() throws JsonProcessingException {
        Map<String, List<String>> map = new HashMap<>();

        String json = dianxinVrbtService.queryTemplateVideo("13563", "8175", "maihe_ai");
        JsonNode array1 = objectMapper.readTree(json).at("/ctrlList");
        for (JsonNode node1 : array1) {
            JsonNode array2 = node1.at("/musicList");
            for (JsonNode node2 : array2) {
                List<String> list = new ArrayList<>();
                list.add(node2.at("/artistPic").textValue());
                list.add(node2.at("/mvUrl").textValue());
                map.put(node2.at("/background").textValue(), list);
            }
        }

        List<CmsMusicTelecom> maiheAiTemplate = cmsMusicTelecomService.list(new LambdaQueryWrapper<CmsMusicTelecom>()
                .eq(CmsMusicTelecom::getSingerName, "maihe_ai_template"));
        for (CmsMusicTelecom cmsMusicTelecom : maiheAiTemplate) {
            if (map.containsKey(cmsMusicTelecom.getToneCode())) {
                cmsMusicTelecom.setVrbtImg(map.get(cmsMusicTelecom.getToneCode()).get(0));
                cmsMusicTelecom.setVrbtVideo(map.get(cmsMusicTelecom.getToneCode()).get(1));
            }
        }

        List<CmsMusicTelecom> data = maiheAiTemplate.stream().filter(x -> StringUtils.isNotEmpty(x.getVrbtImg())).collect(Collectors.toList());
        System.out.println(cmsMusicTelecomService.updateBatchById(data));
    }

    /**
     * 获取 CMS AI 模板链接地址
     */
    @Test
    public void getAiTemplateUrl() throws JsonProcessingException {
        List<CmsMusicTelecom> maiheAiTemplate = cmsMusicTelecomService.list(new LambdaQueryWrapper<CmsMusicTelecom>()
                .eq(CmsMusicTelecom::getSingerName, "maihe_ai_template"));
        for (CmsMusicTelecom cmsMusicTelecom : maiheAiTemplate) {
            String json = dianxinVrbtService.getAiTemplateUrl("135999999999999000427", cmsMusicTelecom.getToneCode(), "maihe_ai");
            String aiTemplateUrl = objectMapper.readTree(json).at("/data/templateUrl").textValue();
            cmsMusicTelecom.setResourceCode(aiTemplateUrl);
        }
        cmsMusicTelecomService.updateBatchById(maiheAiTemplate);
    }


    @Test
    public void saveCmsMusic(){
        String channel ="AYY_AISPCL_HS";
        try {
            File file=new File("C:\\Users\\<USER>\\Desktop\\新建文本文档.txt");
            if(file.exists()){
                List<String> stringList = FileUtils.readLines(file, StandardCharsets.UTF_8);
                if(!stringList.isEmpty()){
                    stringList.forEach(resourceCode->{
                        String result = dianxinVrbtService.videoQuery(resourceCode, channel);
                        JsonNode tree = null;
                        try {
                            tree = objectMapper.readTree(result);
                        } catch (JsonProcessingException e) {
                            e.printStackTrace();
                        }
                        String code=tree.at("/code").asText();
                        if("0000".equals(code)){
                            CmsMusicTelecom cmsMusicTelecom=new CmsMusicTelecom();
                            //电信铃音编码
                            cmsMusicTelecom.setToneCode(tree.at("/data/ring_id").asText());
                            /**电信资源编码*/
                            cmsMusicTelecom.setResourceCode(tree.at("/data/resource_id").asText());
                            /**歌曲名*/
                            cmsMusicTelecom.setMusicName(tree.at("/data/video_name").asText());
                            /**歌手名*/
                            cmsMusicTelecom.setSingerName(channel);
                            JsonNode imageLists = tree.get("data").get("imageList");
                            for (int i = 0; i < imageLists.size(); i++) {
                                String format = imageLists.get(i).get("format").asText();
                                if ("jpg".equals(format)) {
                                    String path = imageLists.get(i).get("path").asText();
                                    cmsMusicTelecom.setVrbtImg(path);
                                    break;
                                }
                            }
                            JsonNode fileLists = tree.get("data").get("fileList");
                            for (int i = 0; i < fileLists.size(); i++) {
                                String fileFormat = fileLists.get(i).get("file_format").asText();
                                if ("mp4".equals(fileFormat)) {
                                    String filePath = fileLists.get(i).get("file_path").asText();
                                    cmsMusicTelecom.setVrbtVideo(filePath);
                                    break;
                                }
                            }
                            cmsMusicTelecomService.save(cmsMusicTelecom);
                        }
                    });
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
