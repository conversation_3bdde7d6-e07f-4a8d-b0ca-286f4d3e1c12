package org.jeecg;

import com.eleven.cms.service.IXiMiLeApiService;
import com.eleven.cms.vo.XiMiLeQueryBalanceResult;
import com.eleven.cms.vo.XiMiLeQueryOrderResult;
import com.eleven.cms.vo.XiMiLeRechargeResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 西米乐权益充值测试
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 16:20
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class XiMiLeServiceTest {
    @Autowired
    IXiMiLeApiService xiMiLeApiService;


    @Test
    public void recharge(){
        String orderId=String.valueOf(System.currentTimeMillis());
        String skuId="sp_306_sp_ces13PYxbV";
        String skuType="999";
        String accountId="***********";
        String faceValue="1";
        XiMiLeRechargeResult xiMiLeRechargeResult= xiMiLeApiService.recharge( orderId,  skuId,  skuType,  accountId,  faceValue);
        log.info("orderId:{},xiMiLeRechargeResult:{}",orderId,xiMiLeRechargeResult);
    }

    @Test
    public void queryBalance() {
        XiMiLeQueryBalanceResult xiMiLeQueryBalanceResult= xiMiLeApiService.queryBalance( );
        log.info("xiMiLeQueryBalanceResult:{}",xiMiLeQueryBalanceResult);

    }

    @Test
    public void queryOrder() {
        String orderId="*************";
        XiMiLeQueryOrderResult xiMiLeQueryOrderResult= xiMiLeApiService.queryOrder( orderId);
        log.info("orderId:{},xiMiLeQueryOrderResult:{}",orderId,xiMiLeQueryOrderResult);

    }

}
