package org.jeecg;

import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.service.IXunYouRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 讯游充值测试
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/4 14:27
 **/
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class XunYouRechargeServiceTest {
    @Autowired
    IXunYouRechargeService xunYouRechargeService;
    /**
     * 充值
     */
    @Test
    public void recharge() {
        JunboChargeLog junboChargeLog=new JunboChargeLog();
        junboChargeLog.setMobile("15915401656");
        junboChargeLog.setCouponId("3ace6acfb1");
        junboChargeLog.setMiguOrderId("20241204145825");
        xunYouRechargeService.recharge(junboChargeLog);
    }


    /**
     * 退款
     */
    @Test
    public void refund() {
        JunboChargeLog junboChargeLog=new JunboChargeLog();
        junboChargeLog.setMobile("15915401656");
        junboChargeLog.setJunboOrderId("9618411012fb4fb0a713e0ad3dff652b");
        xunYouRechargeService.refund(junboChargeLog);
    }

    /**
     * 查询
     */
    @Test
    public void query() {
        JunboChargeLog junboChargeLog=new JunboChargeLog();
        junboChargeLog.setMobile("15915401656");
        junboChargeLog.setJunboOrderId("9618411012fb4fb0a713e0ad3dff652b");
        xunYouRechargeService.query(junboChargeLog);
    }


}
