package org.jeecg;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.config.JiangxiCompanyConfig;
import com.eleven.cms.config.JiangxiYidongProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.impl.*;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.JiangxiYidongNoPageWriteResult;
import com.eleven.cms.vo.JiangxiYidongPdfResult;
import com.mysql.jdbc.util.Base64Decoder;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

/**
 * @author: cai lei
 * @create: 2022-08-12 15:33
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class JiangxiYidongHongshengServiceTest {

    @Autowired

    JiangxiYidongService jiangxiYidongService;
    @Autowired
    JiangxiYidongV2Service jiangxiYidongV2Service;
    @Autowired
    JiangxiYidongProperties jiangxiYidongProperties;
    @Autowired
    OutsideCallbackService outsideCallbackService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    StpYidongService stpYidongService;


    @Test
    public void order() throws Exception {
        //jiangxiYidongService.getSms("13907950173","JXYD_DXLLB","hstj");
//        jiangxiYidongService.smsCode("13907950173","JXYD_DXLLB","123456","4679240112550320","15007913698202305241510516078705","hstj");
//        jiangxiYidongService.order("13907950173","JXYD_DXLLB","123456","hstj");


        //JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig("zyhl");
        //String busiSerialNumber = jiangxiCompanyConfig.getUserId() + DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(4);
        //jiangxiYidongV2Service.getSms("15079108088", "JXYD_MSHYLLB", "zyhl", busiSerialNumber);
//        String code = "123456";
//        //   FileUtils.writeByteArrayToFile(new File("D:\\test.pdf"), Base64.getDecoder().decode(jiangxiYidongPdfResult.getData().getResultMsg().getResult().replaceAll("\\n","")));
//        jiangxiYidongV2Service.smsCode("15079108088", "JXYD_MSHYLLB", code, "", busiSerialNumber, "yrjy");

        //JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig("yrjy");
        //String busiSerialNumber = jiangxiCompanyConfig.getUserId() + DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(4);
        //jiangxiYidongV2Service.getSms("15079108088", "JXYD_QYCSLLB", "yrjy", busiSerialNumber);

        System.out.println(jiangxiYidongV2Service.fetchEncryption("JXYD_YLSVHY", "hstj"));
        System.out.println(jiangxiYidongV2Service.fetchEncryption("JXYD_YLSVHY", "yrjy"));
    }

    @Test
    public void callback() throws IOException {
//        List<String> strings = FileUtils.readLines(new File("D:\\补发.txt"), StandardCharsets.UTF_8);
//        strings.forEach(str -> {
//            Subscribe subscribe = subscribeService.getById(str);
//            outsideCallbackService.outsideCallback(subscribe, subscribe.getResult());
//        });

        String mobile = "18485545463";
        String source = "http://www.baidu.com";
        String app = "com.app.test";
        String ip = "**************";
        String ua = "Mozilla/5.0 (Linux; Android 13; PJB110 Build/TP1A.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/103.0.5060.129 Mobile Safari/537.36 open_news open_news_u_s/5908/com.mm.aweather.plus";

        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setSource(source);
        subscribe.setReferer(app);
        subscribe.setUserAgent(ua);
        subscribe.setIp(ip);
        subscribe.setChannel("GZGJ_QXX_BJHY");

        Result result = SpringUtil.getBean(GuizhouYidongGaoJieCommonServiceImpl.class).getSmsCode(subscribe);

        String transactionId = "1";
        subscribe.setTransactionId(transactionId);
        subscribe.setSmsCode("123456");

        result = SpringUtil.getBean(GuizhouYidongGaoJieCommonServiceImpl.class).submitSmsCode(subscribe);

//        SpringUtil.getBean(VrbtCommonServiceImpl.class).getSmsCode(subscribe);
//        SpringUtil.getBean(VrbtCommonServiceImpl.class).getSmsCode(subscribe);
//        SpringUtil.getBean(VrbtCommonServiceImpl.class).getSmsCode(subscribe);


//        subscribe.setSmsCode("JXYD_DXLLB");

//        SpringUtil.getBean(LianbaoGansuCommonServiceImpl.class).submitSmsCode(subscribe);


//        stpJiangsuYidongService.getSms("18362544023","","http://www.baidu.com","com.app.test","**************");
//
    }
}
