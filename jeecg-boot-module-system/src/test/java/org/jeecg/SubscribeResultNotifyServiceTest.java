package org.jeecg;

import com.eleven.cms.config.YunnanYidongProperties;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.remote.SubscribeResultNotifyService;
import com.eleven.cms.remote.YunnanYidongService;
import com.eleven.cms.vo.YunnanMobileCheckSaleActiveResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 西藏移动业务测试
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/28 10:39
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SubscribeResultNotifyServiceTest {
    @Autowired
    SubscribeResultNotifyService subscribeResultNotifyService;

    @Test
    public void resultJudgeMsgHandle() throws Exception {
        String[] henanNeedVerifySubscribeIds = {
                "1801834676269719553","1801835081435291649","1801835145092243457","1801835193788112898","1801835481513172993","1801835833440444418","1801835939602472962","1801835994992451586","1801836039263330305","1801836548988706818","1801836612201062402","1801836742388064258","1801836819206742017","1801837027114196994","1801837036748513282","1801837289388220418","1801837503926870018","1801838205537460226","1801839213416128514","1801839312603029506","1801839350834110465","1801839576185675777","1801840007213326337","1801840109772447746","1801840170568884226","1801840241008025601","1801840392720195586","1801840585456852994","1801840802327535617","1801840927405875202","1801841054283571202","1801841216745742337","1801841292998189057","1801841406072430593","1801841422312775682","1801841733739847682","1801842657979899906","1801842984309334017","1801843031423950849","1801843085350117378","1801843281698070529","1801843742723383298","1801843926274514946","1801844581898756097","1801844790984810498","1801845269978521602","1801846008478015490","1801846500704755713","1801846562818203649","1801846575770214401","1801846876346621954","1801847006219051010","1801848761493331969","1801848766769766402"
        };
        Arrays.stream(henanNeedVerifySubscribeIds).forEach(id->{
            subscribeResultNotifyService.resultJudgeMsgHandle(DelayedMessage.builder().id(id).build());
        });
        TimeUnit.SECONDS.sleep(30L);
    }
}
