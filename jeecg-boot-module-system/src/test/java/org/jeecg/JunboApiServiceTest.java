package org.jeecg;

import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.dto.JunboRespon;
import com.eleven.cms.dto.JunboResult;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.remote.AppOpenResultSyncService;
import com.eleven.cms.remote.JunboApiService;
import com.eleven.cms.remote.JunboHunanYidongService;
import com.eleven.cms.service.ICouponCodeService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.util.BizConstant;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.UUID;

/**
 * Author: <EMAIL>
 * Date: 2020/7/21 15:14
 * Desc:北京移动嗨购api测试
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class JunboApiServiceTest {

    @Autowired
    JunboApiService junboApiService;
    @Autowired
    JunboApiProperties junboApiProperties;
    @Autowired
    AppOpenResultSyncService appOpenResultSyncService;
    @Autowired
    JunboHunanYidongService junboHunanYidongService;
    @Autowired
    IJunboChargeLogService junboChargeLogService;
    @Autowired
    ICouponCodeService couponCodeService;
    /**
     * 河图发码失败重新补发券码
     */
    @Test
    public void failReissue() {
        List<JunboChargeLog> list=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getServiceId,"MIGUHUYU100002").eq(JunboChargeLog::getStatus,2).between(JunboChargeLog::getCreateTime,"2024-07-01 00:00:00","2024-07-31 00:00:00").eq(JunboChargeLog::getRemark,"暂无激活码").list();
        list.forEach(junboChargeLog->{
            couponCodeService.failReissue(junboChargeLog.getMiguOrderId(),junboChargeLog.getMobile(),junboChargeLog.getJunboOrderId(),junboChargeLog);
        });
    }

    @Test
    public void junboApiProperties() {
        System.out.println("junboApiProperties = " + junboApiProperties);

    }

    @Test
    public void rechargeVIP() {
        String couponId = "bibiy";
        String account = "***********";
        String mobile = account;
        String orderSEQ = UUID.randomUUID()
                              .toString()
                              .replace("-", "");
        final JunboRespon junboResult = junboApiService.rechargeVIPJunBoMD5(couponId, orderSEQ, account, mobile,null,null);
        System.out.println("junboResult = " + junboResult);

    }

    @Test
    public void orderQuery() {
        String orderSEQ = "************";
        final JunboResult junboResult = junboApiService.orderQuery(orderSEQ);
        System.out.println("junboResult = " + junboResult);

    }
    @Test
    public void appOpenResultCallback() {
        appOpenResultSyncService.appOpenResultCallback("***********",1,"1","ok");

    }
    @Test
    public void getSmsCode(){
        junboHunanYidongService.getSms("***********", JunboHunanYidongService.getSysOrderId(), BizConstant.BIZ_TYPE_HN_VRBT);
    }

    @Test
    public void smsCode(){
        junboHunanYidongService.smsCode("***********","*****************************","123455",BizConstant.BIZ_TYPE_HN_VRBT);
    }
}
