package org.jeecg;

import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.remote.SiChuanMobileApiService;
import com.eleven.cms.remote.SichuanMobileFlowPacketService;
import com.eleven.cms.service.ISubscribeService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.List;

import static com.eleven.cms.util.BizConstant.BIZ_SCMCC_CHANNEL_JUNBO;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class SichuanMobileFlowPacketServiceTest {

    @Autowired
    SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    OutsideCallbackService outsideCallbackService;

    @Test
    public void queryOrder() throws JsonProcessingException {
//        sichuanMobileFlowPacketService.queryOrder("1000039020220112093636252762", sichuanMobileFlowPacketProperties.getBjhyBizCode());
//        sichuanMobileFlowPacketService.queryOrder("1000039020211224163025252385", sichuanMobileFlowPacketProperties.getBjhyBizCode());
        sichuanMobileFlowPacketService.queryOrder("1000039020240621204839377714", "ACAZ50290", "yrjy");


//        outsideCallbackService.lbtxScydCallback("13980120816", "SCYD_TLVRBTLXB_P", "1679389388645720066", "1000039720230713151647095814", 1, "2023-07-13 15:17:09", "成功");

    }

    @Test
    public void qryPointAnalysi() throws JsonProcessingException {

        System.out.println(sichuanMobileFlowPacketService.qryPointAnalysi(YearMonth.now(), "yrjy"));
        System.out.println(sichuanMobileFlowPacketService.qryPointAnalysi(YearMonth.now().minusMonths(1L), "yrjy"));
        System.out.println(sichuanMobileFlowPacketService.qryPointAnalysi(YearMonth.now().minusMonths(2L), "yrjy"));
        System.out.println(sichuanMobileFlowPacketService.qryPointAnalysi(YearMonth.now().minusMonths(3L), "yrjy"));

    }
    @Autowired
    private SiChuanMobileApiService siChuanMobileApiService;
    @Test
    public void reCharge(){
        siChuanMobileApiService.reCharge("1000042720250221115546176918","SC_YJ_HS","13458781652", DateUtils.now());
    }


    @Test
    public void queryOrderList(){
        siChuanMobileApiService.queryOrder("1000042720250221143211177978","SC_YJ_HS","13118193713");
    }



    @Test
    public void junboReplenishCallbak(){
        LocalDate localDate = DateUtils.toLocalDate("2023-03-01");
        LocalDateTime start = localDate.atTime(LocalTime.MIN);
        localDate = DateUtils.toLocalDate("2023-03-31");
        LocalDateTime end = localDate.atTime(LocalTime.MAX);
        List<Subscribe> list = subscribeService.lambdaQuery().eq(Subscribe::getServiceId, BIZ_SCMCC_CHANNEL_JUNBO)
                .ge(Subscribe::getCreateTime, start).le(Subscribe::getCreateTime, end).eq(Subscribe::getIsp, "1")
                .isNotNull(Subscribe::getIspOrderNo).likeRight(Subscribe::getIspOrderNo, "10000402").list();
        System.out.println(list.size());
        list.forEach(subscribe -> {
            outsideCallbackService.junboCallbackAsync(subscribe, sichuanMobileFlowPacketProperties.getJunboCallbackUrl());
        });
    }
}
