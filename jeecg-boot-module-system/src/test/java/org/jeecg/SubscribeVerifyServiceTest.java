package org.jeecg;

import com.eleven.cms.es.entity.EsSubStatistics;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.MemberCommonMapper;
import com.eleven.cms.mapper.SubscribeMapper;
import com.eleven.cms.remote.SubscribeVerifyService;
import com.eleven.cms.service.ISubscribeService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.*;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SubscribeVerifyServiceTest {
    @Autowired
    SubscribeMapper subscribeMapper;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    MemberCommonMapper memberCommonMapper;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;

    @Test
    public void mapperTest(){
        final List<EsSubStatistics> list1 = subscribeMapper.statisticsAllChannel(
                //YearMonth.now().minusMonths(1L).atDay(1).atTime(LocalTime.MIN),
                YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MIN),
                YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MAX));
        System.out.println(list1);
        List<Map<String, Object>> listMap = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("title",new ExportParams(null,"sheet1", ExcelType.XSSF));//表格title
        map1.put("entity", EsSubStatistics.class);//表格对应实体
        map1.put("data", list1);
        listMap.add(map1);

        final List<EsSubStatistics> list2 = subscribeMapper.statisticsAllChannel(
                //YearMonth.now().minusMonths(1L).atDay(1).atTime(LocalTime.MIN),
                YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MIN),
                YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MAX));
        System.out.println(list2);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("title",new ExportParams(null,"sheet2", ExcelType.XSSF));//表格title
        map2.put("entity", EsSubStatistics.class);//表格对应实体
        map2.put("data", list2);
        listMap.add(map2);

        final Workbook workbook = ExcelExportUtil.exportExcel(listMap,ExcelType.XSSF.name());
        //File outFile = new File("D:\\歌曲校验_精选内容_校验结果"+ (workbook instanceof HSSFWorkbook ?".xls" :".xlsx"));
        File outFile = new File("D:\\每月统计测试"+ (workbook instanceof HSSFWorkbook ?".xls" :".xlsx"));
        try (FileOutputStream fileOutputStream = new FileOutputStream(outFile)) {
            workbook.write(fileOutputStream);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void transLast6MonthToVerifyIncludeBizType() {
        final Set<String> bizTypes = subscribeVerifyService.transLast6MonthToVerifyIncludeBizType();
        System.out.println(bizTypes.size());
        System.out.println(bizTypes);
    }

    @Test
    public void genSubStatistics() {
        subscribeVerifyService.genSubStatistics();
    }

    @Test
    public void genLast6MonthVerifyReport() {
        subscribeVerifyService.genLast6MonthVerifyReport();
    }
    
    @Test
    public void genAllChannelReport() {
        subscribeVerifyService.genAllChannelReport();
    }

    @Test
    public void genChannelReport() {
        subscribeVerifyService.genChannelReport("014X04C");
    }

    @Test
    public void resetLast6MonthToVerify() {
        subscribeVerifyService.resetLast6MonthToVerify();
    }

    @Test
    public void statisticsByChannel() {
        final List<EsSubStatistics> subscribeStatisticsList = memberCommonMapper.statisticsByChannel(
                YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MIN),
                YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MAX), "COMIC");
        System.out.println("subscribeStatisticsList = " + subscribeStatisticsList);
    }

    @Test
    public void monthVerify(){
         Subscribe subscribe1 = subscribeService.getById("1765584055623593985");
         Integer monthStatus1 = subscribeVerifyService.monthVerify(subscribe1);
        System.out.println("monthStatus1 = " + monthStatus1);
        Subscribe subscribe2 = subscribeService.getById("1765584263749152770");
        Integer monthStatus2 = subscribeVerifyService.monthVerify(subscribe2);
        System.out.println("monthStatus2= " + monthStatus2);
    }

    @Test
    public void qyclDailyVerifyLimit(){
        final Subscribe subscribe = new Subscribe();
        subscribe.setChannel("QYCL_GR");
        subscribeVerifyService.qyclDailyVerifyLimit(subscribe);
    }
}