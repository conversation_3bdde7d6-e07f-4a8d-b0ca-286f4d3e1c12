package org.jeecg;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.YidongVrbtLimitProperties;
import com.eleven.cms.remote.YidongSenyueCrackService;
import com.eleven.cms.remote.YidongVRCaoWeiCrackService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.IAlipayComplainService;
import com.eleven.cms.vo.BillingResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: cai lei
 * @create: 2022-09-02 09:25
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class YidongVrbtCrackServiceTest {

    @Autowired
    YidongVrbtLimitProperties yidongVrbtLimitProperties;

    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;

    @Autowired
    YidongVRCaoWeiCrackService yidongVRCaoWeiCrackService;

    @Autowired
    YidongSenyueCrackService yidongSenyueCrackService;

    @Test
    public void getLimit() {
        System.out.println(yidongVrbtLimitProperties.getLimit("014X02D", "江西"));
        System.out.println(yidongVrbtLimitProperties.getLimit("014X02F", "陕西"));
    }

    @Test
    public void filter() {
        System.out.println(yidongVrbtCrackService.filter("13438828200", "00210XZ"));
        System.out.println(yidongVrbtCrackService.filter("18482155682", "00210XZ"));
    }

    @Test
    public void getSms() {
//        yidongVrbtCrackService.getSms("13438828200","00210Q6");
        yidongVRCaoWeiCrackService.getSms("13699402402", "HYQY_YR");
    }
    @Autowired
    IAlipayComplainService alipayComplainService;
    @Test
    public void smsCode(){
        Result<?> result=alipayComplainService.alipayQueryComplainList(10, 1);
        log.info("result:{}",result);

    }

    @Test
    public void getSmsQycl(){
        final BillingResult result = yidongVrbtCrackService.getSmsQycl("17260807125", "QYCL_GR_MH", 0, "",
                "3099983264619", "", "maihe");
        log.info("result:{}",result);
        String transId = result.getTransId();
        String smsCode = "";
        System.out.println();
        System.out.println("smsCode = " + smsCode);
        final BillingResult billingResult = yidongVrbtCrackService.smsCode(transId, smsCode, "QYCL_GR_MH", "17260807125");
        log.info("billingResult:{}",billingResult);
    }

    @Test
    public void laoPj(){
        String mobile = "15225273795";
        String channel = "HYQY_SJCS_JB926";
        final BillingResult getSmsResult = yidongVrbtCrackService.getSms(mobile, channel);
        System.out.println(getSmsResult);
        final BillingResult billingResult = yidongVrbtCrackService.smsCode(getSmsResult.getTransId(), "", channel, mobile);
        System.out.println(billingResult);
    }


    @Test
    public void senyuePj(){
        String mobile = "15225273795";
        String channel = "HYQY_SJCS_JB926";
        final BillingResult getSmsResult = yidongSenyueCrackService.getSms(mobile, channel, IdWorker.get32UUID());
        System.out.println(getSmsResult);
        final BillingResult billingResult = yidongSenyueCrackService.smsCode(getSmsResult.getTransId(), "7040", channel, mobile);
        System.out.println(billingResult);
    }


}
