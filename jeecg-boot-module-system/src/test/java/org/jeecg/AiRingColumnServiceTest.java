package org.jeecg;

import com.eleven.cms.aivrbt.service.IAiRingColumnService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @datetime 2024/10/11 9:53
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class AiRingColumnServiceTest {

    @Autowired
    private IAiRingColumnService ringColumnService;

    @Test
    public void testQueryAIColumnAndTemplate() {
        System.out.println(ringColumnService.queryAIColumnAndTemplate("00211MN"));
    }
}
