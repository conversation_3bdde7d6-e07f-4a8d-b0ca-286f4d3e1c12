package org.jeecg;

import com.eleven.cms.entity.DataNotifyLog;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.SubscribeResultNotifyService;
import com.eleven.cms.service.IDataNotifyLogService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.qycl.entity.QyclCompany;
import com.eleven.qycl.service.IQyclCompanyService;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class QyclApiServiceTest {
    @Autowired
    IDataNotifyLogService dataNotifyLogService;
    @Autowired
    IQyclCompanyService qyclCompanyService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    SubscribeResultNotifyService subscribeResultNotifyService;

    @Test
    public void unionLimitSmsTest(){
        subscribeResultNotifyService.qyclUnionLimitSms("QYCL_GR");
        subscribeResultNotifyService.qyclUnionLimitSms("QYCL_GR_MH");
    }

    @Test
    public void saveDataNotify() {
        ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        try {
            List<DataNotifyLog>  dataNotifyLogList =Lists.newArrayList();
            Path path1 = Paths.get("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2023-05\\企业彩铃回执数据2-4月\\咪咕企业视频彩铃-成员状态回执-202302.txt");
            List<String>  lines1 = Files.readAllLines(path1);

            lines1.forEach(lines->{
                String[] logs=lines.split("\\|\\|");
                JsonNode jsonNode = null;
                try {
                    jsonNode = mapper.readTree(logs[1]);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                String createTime=logs[0];
                String mobile=jsonNode.at("/billNum").asText();
                int type = jsonNode.at("/type").asInt();
                DataNotifyLog dataNotifyLog=new DataNotifyLog();
                dataNotifyLog.setMobile(mobile);
                dataNotifyLog.setSource("MEMBER");

                QyclCompany company=qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile,mobile).orderByDesc(QyclCompany::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                String channel=null;
                if(company==null || StringUtils.isEmpty(company.getChannel())){
                    Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
                    if(subscribe!=null && StringUtils.isNotBlank(subscribe.getChannel())){
                        channel=subscribe.getChannel();
                    }
                }else{
                    channel=company.getChannel();
                }
                dataNotifyLog.setChannel(channel);
                if (1 == type) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_ORDER));
                } else if (2 == type) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_UNSUB));
                }
                try {
                    dataNotifyLog.setCreateTime(simpleDateFormat.parse(createTime));
                } catch (ParseException e) {
                    e.printStackTrace();
                    log.error("日期:{}",createTime,e.getMessage());
                }
                dataNotifyLog.setUpdateBy("手动添加");
                dataNotifyLogList.add(dataNotifyLog);
            });

            Path path2 = Paths.get("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2023-05\\企业彩铃回执数据2-4月\\咪咕企业视频彩铃-成员状态回执-202303.txt");
            List<String>  lines2 = Files.readAllLines(path2);
            lines2.forEach(lines->{
                 String[] logs=lines.split("\\|\\|");
                JsonNode jsonNode = null;
                try {
                    jsonNode = mapper.readTree(logs[1]);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                String createTime=logs[0];
                String mobile=jsonNode.at("/billNum").asText();
                int type = jsonNode.at("/type").asInt();
                DataNotifyLog dataNotifyLog=new DataNotifyLog();
                dataNotifyLog.setMobile(mobile);
                dataNotifyLog.setSource("MEMBER");

                QyclCompany company=qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile,mobile).orderByDesc(QyclCompany::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                String channel=null;
                if(company==null || StringUtils.isEmpty(company.getChannel())){
                    Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
                    if(subscribe!=null && StringUtils.isNotBlank(subscribe.getChannel())){
                        channel=subscribe.getChannel();
                    }
                }else{
                    channel=company.getChannel();
                }
                dataNotifyLog.setChannel(channel);
                if (1 == type) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_ORDER));
                } else if (2 == type) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_UNSUB));
                }
                try {
                    dataNotifyLog.setCreateTime(simpleDateFormat.parse(createTime));
                } catch (ParseException e) {
                    e.printStackTrace();
                    log.error("日期:{}",createTime,e.getMessage());
                }
                dataNotifyLog.setUpdateBy("手动添加");
                dataNotifyLogList.add(dataNotifyLog);
            });

            Path path3 = Paths.get("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2023-05\\企业彩铃回执数据2-4月\\咪咕企业视频彩铃-成员状态回执-202304.txt");
            List<String>  lines3 = Files.readAllLines(path3);
            lines3.forEach(lines->{
                 String[] logs=lines.split("\\|\\|");
                JsonNode jsonNode = null;
                try {
                    jsonNode = mapper.readTree(logs[1]);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                String createTime=logs[0];
                String mobile=jsonNode.at("/billNum").asText();
                int type = jsonNode.at("/type").asInt();
                DataNotifyLog dataNotifyLog=new DataNotifyLog();
                dataNotifyLog.setMobile(mobile);
                dataNotifyLog.setSource("MEMBER");

                QyclCompany company=qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile,mobile).orderByDesc(QyclCompany::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                String channel=null;
                if(company==null || StringUtils.isEmpty(company.getChannel())){
                    Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
                    if(subscribe!=null && StringUtils.isNotBlank(subscribe.getChannel())){
                        channel=subscribe.getChannel();
                    }
                }else{
                    channel=company.getChannel();
                }
                dataNotifyLog.setChannel(channel);

                if (1 == type) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_ORDER));
                } else if (2 == type) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_UNSUB));
                }
                try {
                    dataNotifyLog.setCreateTime(simpleDateFormat.parse(createTime));
                } catch (ParseException e) {
                    e.printStackTrace();
                    log.error("日期:{}",createTime,e.getMessage());
                }
                dataNotifyLog.setUpdateBy("手动添加");
                dataNotifyLogList.add(dataNotifyLog);
            });
            Path path4 = Paths.get("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2023-05\\企业彩铃回执数据2-4月\\咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果同步-202302.txt");
            List<String>  lines4 = Files.readAllLines(path4);
            lines4.forEach(lines->{
                 String[] logs=lines.split("\\|\\|");
                JsonNode jsonNode = null;
                try {
                    jsonNode = mapper.readTree(logs[1]);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                String createTime=logs[0];
                String mobile=jsonNode.at("/msisdn").asText();
                String oprType = jsonNode.at("/oprType").asText();
                DataNotifyLog dataNotifyLog=new DataNotifyLog();
                dataNotifyLog.setMobile(mobile);
                dataNotifyLog.setSource("SDK");

                QyclCompany company=qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile,mobile).orderByDesc(QyclCompany::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                String channel=null;
                if(company==null || StringUtils.isEmpty(company.getChannel())){
                    Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
                    if(subscribe!=null && StringUtils.isNotBlank(subscribe.getChannel())){
                        channel=subscribe.getChannel();
                    }
                }else{
                    channel=company.getChannel();
                }
                dataNotifyLog.setChannel(channel);
                if ("06".equals(oprType)) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_ORDER));
                } else if ("07".equals(oprType)) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_UNSUB));
                }
                try {
                    dataNotifyLog.setCreateTime(simpleDateFormat.parse(createTime));
                } catch (ParseException e) {
                    e.printStackTrace();
                    log.error("日期:{}",createTime,e.getMessage());
                }
                dataNotifyLog.setUpdateBy("手动添加");
                dataNotifyLogList.add(dataNotifyLog);
            });

            Path path5 = Paths.get("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2023-05\\企业彩铃回执数据2-4月\\咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果同步-202303.txt");
            List<String>  lines5 = Files.readAllLines(path5);
            lines5.forEach(lines->{
                 String[] logs=lines.split("\\|\\|");
                JsonNode jsonNode = null;
                try {
                    jsonNode = mapper.readTree(logs[1]);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                String createTime=logs[0];
                String mobile=jsonNode.at("/msisdn").asText();
                String oprType = jsonNode.at("/oprType").asText();
                DataNotifyLog dataNotifyLog=new DataNotifyLog();
                dataNotifyLog.setMobile(mobile);
                dataNotifyLog.setSource("SDK");

                QyclCompany company=qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile,mobile).orderByDesc(QyclCompany::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                String channel=null;
                if(company==null || StringUtils.isEmpty(company.getChannel())){
                    Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
                    if(subscribe!=null && StringUtils.isNotBlank(subscribe.getChannel())){
                        channel=subscribe.getChannel();
                    }
                }else{
                    channel=company.getChannel();
                }
                dataNotifyLog.setChannel(channel);
                if ("06".equals(oprType)) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_ORDER));
                } else if ("07".equals(oprType)) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_UNSUB));
                }
                try {
                    dataNotifyLog.setCreateTime(simpleDateFormat.parse(createTime));
                } catch (ParseException e) {
                    e.printStackTrace();
                    log.error("日期:{}",createTime,e.getMessage());
                }
                dataNotifyLog.setUpdateBy("手动添加");
                dataNotifyLogList.add(dataNotifyLog);
            });

            Path path6 = Paths.get("D:\\WeChat Files\\WeChat Files\\wxid_z9v06cnfuib922\\FileStorage\\File\\2023-05\\企业彩铃回执数据2-4月\\咪咕企业视频彩铃-网页SDK企业视频彩铃功能开通结果同步-202304.txt");
            List<String>  lines6 = Files.readAllLines(path6);
            lines6.forEach(lines->{
                 String[] logs=lines.split("\\|\\|");
                JsonNode jsonNode = null;
                try {
                    jsonNode = mapper.readTree(logs[1]);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                String createTime=logs[0];
                String mobile=jsonNode.at("/msisdn").asText();
                String oprType = jsonNode.at("/oprType").asText();
                DataNotifyLog dataNotifyLog=new DataNotifyLog();
                dataNotifyLog.setMobile(mobile);
                dataNotifyLog.setSource("SDK");

                QyclCompany company=qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile,mobile).orderByDesc(QyclCompany::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                String channel=null;
                if(company==null || StringUtils.isEmpty(company.getChannel())){
                    Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
                    if(subscribe!=null && StringUtils.isNotBlank(subscribe.getChannel())){
                        channel=subscribe.getChannel();
                    }
                }else{
                    channel=company.getChannel();
                }
                dataNotifyLog.setChannel(channel);
                if ("06".equals(oprType)) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_ORDER));
                } else if ("07".equals(oprType)) {
                    dataNotifyLog.setStatus(Integer.valueOf(QyclConstant.QYCL_FUN_STATUS_UNSUB));
                }
                try {
                    dataNotifyLog.setCreateTime(simpleDateFormat.parse(createTime));
                } catch (ParseException e) {
                    e.printStackTrace();
                    log.error("日期:{}",createTime,e.getMessage());
                }
                dataNotifyLog.setUpdateBy("手动添加");
                dataNotifyLogList.add(dataNotifyLog);
            });
            log.info("旧数据->size:{},dataNotifyLogList:{}",dataNotifyLogList.size(),dataNotifyLogList);
            dataNotifyLogService.saveBatch(dataNotifyLogList);
        } catch (Exception e) {
            e.printStackTrace();
        }






    }

}

