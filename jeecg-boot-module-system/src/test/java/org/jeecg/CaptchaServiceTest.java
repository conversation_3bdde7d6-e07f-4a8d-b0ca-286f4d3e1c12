package org.jeecg;

import com.eleven.cms.captcha.CaptchaService;
import com.eleven.cms.captcha.fateadm.Util;
import com.eleven.cms.config.YidongVrbtLimitProperties;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.IAlipayComplainService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.RemoteResult;
import com.google.common.io.Files;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.io.IOUtils;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;

/**
 * @author: cai lei
 * @create: 2022-09-02 09:25
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CaptchaServiceTest {

    @Autowired
    CaptchaService captchaService;

   

    @Test
    public void Predict(){
        String mobile = "15184328572";
        String captchaUrl = "https://eduapp.sd.chinamobile.com/mobile3/pull/ThirdHtml5?action=getImgVerifyCode&mobile=" + mobile;
        final OkHttpClient client = OkHttpClientUtils.getSingletonInstance();
        Request request = new Request.Builder().url(captchaUrl).build();
        try (Response response = client.newCall(request).execute()) {

            final byte[] bytes = response.body().bytes();
            Files.write(bytes, new File("D:/tempCaptcha.jfif"));
            final Util.HttpResp predict = captchaService.Predict(CaptchaService.PRED_TYPE_SHANDONG_HEXIAOYUAN, bytes, CaptchaService.SRC_URL_SHANDONG_HEXIAOYUAN);
            System.out.println(predict);
        } catch (IOException e) {
           e.printStackTrace();
        }

    }


}
