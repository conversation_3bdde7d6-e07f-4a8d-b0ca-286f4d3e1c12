package org.jeecg;

import com.eleven.cms.remote.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;

/**
 * @author: cai lei
 * @create: 2022-08-12 15:33
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class XinjiangYidongServiceTest {

    @Autowired
    XinjiangYidongService xinjiangYidongService;
    @Autowired
    GuangdongYidongYueyueService guangdongYidongYueyueService;

    @Autowired
    XinjiangYidongYouranService xinjiangYidongYouranService;

    @Autowired
    HainanYidongYunshemeiService hainanYidongYunshemeiService;

    @Autowired
    ChongqingYidongYunshemeiNewService chongqingYidongYunshemeiNewService;

    @Test
    public void order() throws UnsupportedEncodingException {
//        guangdongYidongYueyueService.getSms("15915401656", UUIDGenerator.generate());
//        5d3a9bd38c683e5a028681c2b6c9b2ea
        ObjectNode objectNode = new ObjectMapper().createObjectNode();
        objectNode.put("phonenum", "15915401656");
//        guangdongYidongYueyueService.smsCode("15915401656", "b3ba15449863743bdea30fd603d4f828", "7120", objectNode);
//        XinjiangYidongResult xinjiangYidongResult = xinjiangYidongService.order("15099657792", BizConstant.BIZ_TYPE_XJYD_RWX);
//        System.out.println(xinjiangYidongResult.getRespCode() + "-" +xinjiangYidongResult.getRespDesc());
    }

    @Test
    public void yrtest() throws UnsupportedEncodingException {
//        chongqingYidongYunshemeiNewService.getSms("18223181950", "SPCL_XSLXB", "", "");
//        chongqingYidongYunshemeiNewService.smsCode("18223181950", "SPCL_XSLXB", "1234");

//        hainanYidongYunshemeiService.getSms("19809231909","SPCL_KDXZS");
//        ObjectNode resultNode = hainanYidongYunshemeiService.smsCode("19809231909", "SPCL_KDXZS", "123456");
//        String code = resultNode.at("/code").asText();
//        String msg = resultNode.at("/msg").asText();
//        if("0".equals(code)){
//            System.out.println("办理成功");
//        }else{
//            System.out.println("办理失败,失败原因："+msg);
//        }

        xinjiangYidongYouranService.order("13999193554", "XJYD_RWXGJZB");


//        xinjiangYidongYouranService.getSms("13999193554", "XJYD_RWXGJZB");
//        String code = "123456";
//        boolean check = false;
//        check = xinjiangYidongYouranService.validateSmsCode("13999193554", "XJYD_RWXGJZB", code);
//        if (check) {
//            check = xinjiangYidongYouranService.checkAge("13999193554", "XJYD_RWXGJZB", 60);
//        }
//        if (check) {
//            check = xinjiangYidongYouranService.checkAge("13999193554", "XJYD_RWXGJZB", 18);
//        }
//        if (check) {
//            check = xinjiangYidongYouranService.checkPersonOpenDate("13999193554", "XJYD_RWXGJZB");
//        }
//        if (check) {
//            xinjiangYidongYouranService.unionCaseAcceptAcceptOrder("13999193554", "XJYD_RWXGJZB");
//        }


//        System.out.println(xinjiangYidongYouranService.checkAge("18290631177","XJYD_5GTHB",60));
//        System.out.println(xinjiangYidongYouranService.checkAge("18290631177","XJYD_5GTHB",18));
//        System.out.println(xinjiangYidongYouranService.checkPersonOpenDate("18290631177","XJYD_5GTHB"));
    }
}
