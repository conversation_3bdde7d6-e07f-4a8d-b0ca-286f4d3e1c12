package org.jeecg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eleven.cms.entity.Channel;
import com.eleven.cms.entity.VisitLog;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IVisitLogService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class VisitLogServiceTest {

    @Autowired
    IVisitLogService visitLogService;

    @Test
    public void countDistinct() {
        final LambdaQueryWrapper<VisitLog> queryWrapper = new QueryWrapper<VisitLog>().select("distinct finger")
                                                                            .lambda()
                                                                            .between(VisitLog::getCreateTime,
                                                                                    LocalDateTime.of(LocalDate.now(),
                                                                                            LocalTime.MIN),
                                                                                    LocalDateTime.of(LocalDate.now(),
                                                                                            LocalTime.MAX))
                                                                            .eq(VisitLog::getSubChannel, "xxlct");

        final int count = visitLogService.count(queryWrapper);
        System.out.println("count = " + count);
    }



}