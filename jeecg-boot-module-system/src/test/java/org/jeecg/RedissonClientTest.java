package org.jeecg;

import com.eleven.cms.entity.ProvinceLimit;
import com.eleven.cms.service.IProvinceLimitService;
import org.jeecg.common.util.RedisUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Author: <EMAIL>
 * Date: 2021/2/23 14:20
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RedissonClientTest {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IProvinceLimitService provinceLimitService;
    @Autowired
    RedissonClient redissonClient;

    /**
     * redisson限流 RRateLimiter
     */
    @Test
    public void testRateLimit() {
        RRateLimiter limiter = redissonClient.getRateLimiter("rate-limiter:" + "127.0.0.1");
        // Initialization required only once.
        // 5 permits per 2 seconds
        limiter.trySetRate(RateType.OVERALL, 5, 2, RateIntervalUnit.SECONDS);

        // acquire 3 permits or block until they became available
        limiter.acquire();
        // acquire 1 permits not blocking
        boolean acquire = limiter.tryAcquire();
        System.out.println("acquire = " + acquire);
        acquire = limiter.tryAcquire();
        System.out.println("acquire = " + acquire);
        acquire = limiter.tryAcquire();
        System.out.println("acquire = " + acquire);
        acquire = limiter.tryAcquire();
        System.out.println("acquire = " + acquire);
        acquire = limiter.tryAcquire();
        System.out.println("acquire = " + acquire);

    }

    /**
     * 测试对redis template的影响
     */
    @Test
    public void setIfAbsent(){
        redisUtil.del("test::key");

        final boolean first = redisUtil.setIfAbsent("test::key", "exists");
        System.out.println("first = " + first);
        final boolean sencond = redisUtil.setIfAbsent("test::key", "exists");
        System.out.println("sencond = " + sencond);
    }

    /**
     * 测试加入redisson以后,对spring cache的影响
     */
    @Test
    public void isAvailableUnicom() {
        final boolean availableUnicom = provinceLimitService.isAvailableUnicom("100");
        System.out.println("availableUnicom = " + availableUnicom);
        final boolean availableUnicom1 = provinceLimitService.isAvailableUnicom("100");
        System.out.println("availableUnicom1 = " + availableUnicom1);
    }

    @Test
    public void testLock(){
        final RLock testLock = redissonClient.getLock("testLock");
        testLock.lock();
        System.out.println("lock fetched");
        testLock.unlock();
    }

}
