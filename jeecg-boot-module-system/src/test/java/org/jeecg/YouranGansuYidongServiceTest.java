package org.jeecg;

import com.eleven.cms.entity.Music;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.repository.EsSubscribeRepository;
import com.eleven.cms.remote.YouranGansuYidongService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IMusicService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.MusicVo;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jeecg.common.util.RedisUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class YouranGansuYidongServiceTest {

    @Autowired
    YouranGansuYidongService youranGansuYidongService;
    @Autowired
    ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    EsSubscribeRepository esSubscribeRepository;

    @Test
    public void getSmsCode() throws Exception {
//        youranGansuYidongService.getSms("13619312957","117.173.153.25");
//        youranGansuYidongService.smsCode("13619312957","137684","9371593b3bd240f48e0812e4f15020fd","117.173.153.25");
//        youranGansuYidongService.queryOrder("13619312957","117.173.153.25");


//        BoolQueryBuilder builder = QueryBuilders.boolQuery();
//        builder.must(QueryBuilders.termQuery("mobile", "13438828200"));
//        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
//                withSort(SortBuilders.fieldSort("createTime").order(SortOrder.ASC)).
//                withQuery(builder).build();
//        SearchHits<EsSubscribe> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsSubscribe.class);
//        List<EsSubscribe> list = searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
//        list.forEach(esSubscribe -> {
//            System.out.println(esSubscribe.getId());
//            System.out.println(esSubscribe.getMobile());
//            System.out.println(esSubscribe.getFmtCreateTime());
//
//
//        });




        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("id", "13517814762958438421"));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().
                withQuery(builder).build();
        SearchHit<EsSubscribe> searchHit = elasticsearchRestTemplate.searchOne(nativeSearchQuery, EsSubscribe.class);
        System.out.println(searchHit.getContent());;


//        Optional<EsSubscribe> byId = esSubscribeRepository.findById("1351781476295843842");
//        Optional<EsSubscribe> byId1 = esSubscribeRepository.findById("1351781494306185218");
//        Optional<EsSubscribe> byId2 = esSubscribeRepository.findById("1762301724561346561");
//
//        System.out.println(byId.get());
//        System.out.println(byId1.get());
//        System.out.println(byId2.get());



    }

//    @Test
//    public void exists() {
//        final boolean yjyj = channelService.aopProxyTest("yrjy");
//        System.out.println("yjyj = " + yjyj);
//    }
//


}