package org.jeecg;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.MiguChannelConfig;
import com.eleven.cms.remote.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.<PERSON>ianxinResult;
import com.eleven.cms.vo.YuxunHunanDianxinMindResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.jeecg.common.util.UUIDGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: cai lei
 * @create: 2022-05-16 16:58
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class HebeiVrbtServiceTest {
    @Autowired
    HebeiYidongCityVrbtService hebeiYidongCityVrbtService;
    @Autowired
    HeilongjiangYidongVrbtService heilongjiangYidongVrbtService;
    @Autowired
    HainanYidongVrbtService hainanYidongVrbtService;
    @Autowired
    ShandongDianxinService shandongDianxinService;
    @Autowired
    YuxunHunanDianxinMindService yuxunHunanDianxinMindService;


    @Test
    public void getSms() throws JsonProcessingException {

        //YuxunHunanDianxinMindResult yuxunHunanDianxinMindResult = yuxunHunanDianxinMindService.accpectOrder("17342680919","13","https://crbt.cdyrjygs.com/cms-vrbt/api/yixunDianxinMindCallBack");
        YuxunHunanDianxinMindResult yuxunHunanDianxinMindResult = yuxunHunanDianxinMindService.accpectOrder("17773911831","13","http://crbt.cdyrjygs.com:9527/cms-vrbt/test/notifyLog");
        System.out.println(yuxunHunanDianxinMindResult.getData().getOrderNo());
        System.out.println(yuxunHunanDianxinMindResult.getData().getUrl());
        System.out.println();
//      ShandongDianxinResult shandongDianxinResult = shandongDianxinService.getSms("18906356067","218.88.31.212");
//      System.out.println(shandongDianxinResult.getResCode());
//        heilongjiangYidongVrbtService.getSms("15804619007","",BizConstant.BIZ_TYPE_HLJYD_DYVRBT);
//        hainanYidongVrbtService.getSms("19809231909");
    }


    @Test
    public void smsCode() throws JsonProcessingException {
//        heilongjiangYidongVrbtService.smsCode("15804619007","324266","哈尔滨",BizConstant.BIZ_TYPE_HLJYD_DYVRBT);

      ShandongDianxinResult shandongDianxinResult =  shandongDianxinService.smsCode("18906356067","565350","218.88.31.212");
        System.out.println(shandongDianxinResult.getResCode());

//        hainanYidongVrbtService.smsCode("19809231909","123456");

    }

}
