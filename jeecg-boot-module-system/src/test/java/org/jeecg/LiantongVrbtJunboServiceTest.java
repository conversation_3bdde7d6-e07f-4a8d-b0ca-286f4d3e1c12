package org.jeecg;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.MiguChannelConfig;
import com.eleven.cms.remote.BjhyDataService;
import com.eleven.cms.remote.LiantongJunboCrackService;
import com.eleven.cms.remote.LiantongVrbtJunboService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.LiantongJunboResp;
import org.jeecg.common.util.UUIDGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: cai lei
 * @create: 2022-05-16 16:58
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LiantongVrbtJunboServiceTest {
    @Autowired
    LiantongVrbtJunboService liantongVrbtJunboService;
    @Autowired
    BjhyDataService bjhyDataService;
    @Autowired
    BizProperties bizProperties;
    @Autowired
    LiantongJunboCrackService liantongJunboCrackService;

    @Test
    public void order() {
        liantongJunboCrackService.smsCode("C9D5C9C717B0000177C1100055D881A0","376850");
    }

    @Test
    public void sendCode() {
        LiantongJunboResp junboResp = liantongVrbtJunboService.subScribe("18613222501", UUIDGenerator.generate());
        if(junboResp.isCodeOK()) {
            liantongJunboCrackService.getSms("18613222501", junboResp.getResult().getSubUrl());
        }
    }

    @Test
    public void unsub() {


//        MiguChannelConfig miguChannelConfig = bizProperties.getMiguChannelConfigMap().get(BizConstant.BJHY_CHANNEL_CODE_PP);
//        bjhyDataService.saveData("13798908720", BizConstant.BJHY_CHANNEL_CODE_PP,miguChannelConfig.getServiceId());
//        liantongVrbtJunboService.unsubscribe("18613222501", "682981", "85d2b426b06c46bf899f1b44483d4d5f");
    }


    @Test
    public void queryInfo() {
        liantongVrbtJunboService.querySubInfo("18613222501");
//        MiguChannelConfig miguChannelConfig = bizProperties.getMiguChannelConfigMap().get(BizConstant.BJHY_CHANNEL_CODE_PP);
//        bjhyDataService.saveData("13798908720", BizConstant.BJHY_CHANNEL_CODE_PP,miguChannelConfig.getServiceId());
//        liantongVrbtJunboService.unsubscribe("18613222501", "682981", "85d2b426b06c46bf899f1b44483d4d5f");
    }
}
