package org.jeecg;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.entity.LianlianChargeLog;
import com.eleven.cms.entity.LianlianProduct;
import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.queue.GuiZhouMobilePayRechargeDeductMessage;
import com.eleven.cms.service.ILianLianFenXiaoService;
import com.eleven.cms.service.ILianlianChargeLogService;
import com.eleven.cms.service.ILianlianProductService;
import com.eleven.cms.service.IMemberRightsService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.LianLianFenXiaoCheckCreateOrder;
import com.eleven.cms.vo.LianLianFenXiaoCreateOrder;
import com.eleven.cms.vo.LianLianFenXiaoProduct;
import com.eleven.cms.vo.LianLianFenXiaoProductDetail;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 联联周边游接口测试
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 11:34
 **/
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LianLianFenXiaoServiceTest {
    @Autowired
    private ILianLianFenXiaoService lianLianFenXiaoService;
    @Autowired
    private ILianlianChargeLogService lianlianChargeLogService;

//    List<String> cityList1=Lists.newArrayList("110100","120100","130100","130200","130300","130400","130500","130600", "469024","469026","469029","500100","654200");
//    List<String> cityList2=Lists.newArrayList("130700","130800","130900","131000","140100","140200","140300","140400","140500","140600");
//    List<String> cityList3=Lists.newArrayList("140700","140800","140900","141000","150100","150200","150400","150500","150700","150800");
//    List<String> cityList4=Lists.newArrayList("150900","152200","152500","210100","210200","210300","210400","210500","210600","210700");
//    List<String> cityList5=Lists.newArrayList("210800","210900","211000","211100","211200","211300","220100","220200","220300","220500");
//    List<String> cityList6=Lists.newArrayList("220600","220700","220800","230100","230200","230300","230400","230500","230600","230700");
//    List<String> cityList7=Lists.newArrayList("230800","230900","231000","231100","231200","310100","320100","320200","320300","320400");
//    List<String> cityList8=Lists.newArrayList("320500","320600","320700","320800","320900","321000","321100","321200","330100","330200");
//    List<String> cityList9=Lists.newArrayList("330300","330400","330500","330600","330700","330800","330900","331000","340100","340200");
//    List<String> cityList10=Lists.newArrayList("340300","340400","340500","340600","340700","340800","341000","341100","341200","341300");
//    List<String> cityList11=Lists.newArrayList("341500","341600","341700","350100","350200","350300","350400","350500","350600","350700");
//    List<String> cityList12=Lists.newArrayList("350800","360100","360200","360300","360400","360500","360600","360700","360800","360900");
//    List<String> cityList13=Lists.newArrayList("361000","370100","370200","370300","370400","370500","370600","370700","370800","370900");
//    List<String> cityList14=Lists.newArrayList("371000","371100","371300","371400","371500","371600","410100","410200","410300","410400");
//    List<String> cityList15=Lists.newArrayList("410500","410600","410700","410800","410900","411000","411100","411200","411300","411400");
//    List<String> cityList16=Lists.newArrayList("411500","411600","411700","420100","420200","420300","420500","420600","420700","420800");
//    List<String> cityList17=Lists.newArrayList("420900","421000","421100","421200","421300","422800","429004","429005","429006","430100");
//    List<String> cityList18=Lists.newArrayList("430200","430300","430400","430500","430600","430700","430800","430900","431000","431100");
//    List<String> cityList19=Lists.newArrayList("431200","431300","440100","440200","440300","440400","440500","440600","440700","440800");
//    List<String> cityList20=Lists.newArrayList("440900","441200","441300","441400","441500","441600","441700","441800","441900","442000");
//    List<String> cityList21=Lists.newArrayList("445100","445200","450100","450200","450300","450400","450500","450600","450700","450800");
//    List<String> cityList22=Lists.newArrayList("450900","451000","451100","451200","451300","460100","460200","460400","469005","469023");
//    List<String> cityList23=Lists.newArrayList("510100","510300","510400","510500","510600","510700","510800","510900","511000","511100");
//    List<String> cityList24=Lists.newArrayList("511300","511400","511500","511600","511700","511800","511900","512000","513200","513300");
//    List<String> cityList25=Lists.newArrayList("520100","520200","520300","520400","520500","520600","522300","522600","530100","530300");
//    List<String> cityList26=Lists.newArrayList("530400","530500","530600","530700","530800","530900","532300","532500","532600","532800");
//    List<String> cityList27=Lists.newArrayList("532900","533300","540100","610100","610200","610300","610400","610500","610600","610700");
//    List<String> cityList28=Lists.newArrayList("610800","610900","620100","620300","620400","620500","620600","620700","620800","620900");
//    List<String> cityList29=Lists.newArrayList("621000","621100","621200","630100","630200","632500","632700","640100","640200","640300");
//    List<String> cityList30=Lists.newArrayList("640400","650100","650200","650500","652300","652700","652800","652900","653100","654000");
      List<String> cityList=Lists.newArrayList("110100","120100","130100","130200","130300","130400","130500","130600", "469024","469026","469029","500100","654200","130700","130800","130900","131000","140100","140200","140300","140400","140500","140600","140700","140800","140900","141000","150100","150200","150400","150500","150700","150800","150900","152200","152500","210100","210200","210300","210400","210500","210600","210700","210800","210900","211000","211100","211200","211300","220100","220200","220300","220500","220600","220700","220800","230100","230200","230300","230400","230500","230600","230700","230800","230900","231000","231100","231200","310100","320100","320200","320300","320400","320500","320600","320700","320800","320900","321000","321100","321200","330100","330200","330300","330400","330500","330600","330700","330800","330900","331000","340100","340200","340300","340400","340500","340600","340700","340800","341000","341100","341200","341300","341500","341600","341700","350100","350200","350300","350400","350500","350600","350700","350800","360100","360200","360300","360400","360500","360600","360700","360800","360900","361000","370100","370200","370300","370400","370500","370600","370700","370800","370900","371000","371100","371300","371400","371500","371600","410100","410200","410300","410400","410500","410600","410700","410800","410900","411000","411100","411200","411300","411400","411500","411600","411700","420100","420200","420300","420500","420600","420700","420800","420900","421000","421100","421200","421300","422800","429004","429005","429006","430100","430200","430300","430400","430500","430600","430700","430800","430900","431000","431100","431200","431300","440100","440200","440300","440400","440500","440600","440700","440800","440900","441200","441300","441400","441500","441600","441700","441800","441900","442000","445100","445200","450100","450200","450300","450400","450500","450600","450700","450800","450900","451000","451100","451200","451300","460100","460200","460400","469005","469023","510100","510300","510400","510500","510600","510700","510800","510900","511000","511100","511300","511400","511500","511600","511700","511800","511900","512000","513200","513300","520100","520200","520300","520400","520500","520600","522300","522600","530100","530300","530400","530500","530600","530700","530800","530900","532300","532500","532600","532800","532900","533300","540100","610100","610200","610300","610400","610500","610600","610700","610800","610900","620100","620300","620400","620500","620600","620700","620800","620900","621000","621100","621200","630100","630200","632500","632700","640100","640200","640300","640400","650100","650200","650500","652300","652700","652800","652900","653100","654000");
Map<String,String> cityMap=new ImmutableMap.Builder<String, String>()
.put("110100", "北京")
.put("120100", "天津")
.put("130100", "石家庄")
.put("130200", "唐山")
.put("130300", "秦皇岛")
.put("130400", "邯郸")
.put("130500", "邢台")
.put("130600", "保定")
.put("130700", "张家口")
.put("130800", "承德")
.put("130900", "沧州")
.put("131000", "廊坊")
.put("140100", "太原")
.put("140200", "大同")
.put("140300", "阳泉")
.put("140400", "长治")
.put("140500", "晋城")
.put("140600", "朔州")
.put("140700", "晋中")
.put("140800", "运城")
.put("140900", "忻州")
.put("141000", "临汾")
.put("150100", "呼和浩特")
.put("150200", "包头")
.put("150400", "赤峰")
.put("150500", "通辽")
.put("150700", "呼伦贝尔")
.put("150800", "巴彦淖尔")
.put("150900", "乌兰察布")
.put("152200", "兴安")
.put("152500", "锡林郭勒")
.put("210100", "沈阳")
.put("210200", "大连")
.put("210300", "鞍山")
.put("210400", "抚顺")
.put("210500", "本溪")
.put("210600", "丹东")
.put("210700", "锦州")
.put("210800", "营口")
.put("210900", "阜新")
.put("211000", "辽阳")
.put("211100", "盘锦")
.put("211200", "铁岭")
.put("211300", "朝阳")
.put("220100", "长春")
.put("220200", "吉林")
.put("220300", "四平")
.put("220500", "通化")
.put("220600", "白山")
.put("220700", "松原")
.put("220800", "白城")
.put("230100", "哈尔滨")
.put("230200", "齐齐哈尔")
.put("230300", "鸡西")
.put("230400", "鹤岗")
.put("230500", "双鸭山")
.put("230600", "大庆")
.put("230700", "伊春")
.put("230800", "佳木斯")
.put("230900", "七台河")
.put("231000", "牡丹江")
.put("231100", "黑河")
.put("231200", "绥化")
.put("310100", "上海")
.put("320100", "南京")
.put("320200", "无锡")
.put("320300", "徐州")
.put("320400", "常州")
.put("320500", "苏州")
.put("320600", "南通")
.put("320700", "连云港")
.put("320800", "淮安")
.put("320900", "盐城")
.put("321000", "扬州")
.put("321100", "镇江")
.put("321200", "泰州")
.put("330100", "杭州")
.put("330200", "宁波")
.put("330300", "温州")
.put("330400", "嘉兴")
.put("330500", "湖州")
.put("330600", "绍兴")
.put("330700", "金华")
.put("330800", "衢州")
.put("330900", "舟山")
.put("331000", "台州")
.put("340100", "合肥")
.put("340200", "芜湖")
.put("340300", "蚌埠")
.put("340400", "淮南")
.put("340500", "马鞍山")
.put("340600", "淮北")
.put("340700", "铜陵")
.put("340800", "安庆")
.put("341000", "黄山")
.put("341100", "滁州")
.put("341200", "阜阳")
.put("341300", "宿州")
.put("341500", "六安")
.put("341600", "亳州")
.put("341700", "池州")
.put("350100", "福州")
.put("350200", "厦门")
.put("350300", "莆田")
.put("350400", "三明")
.put("350500", "泉州")
.put("350600", "漳州")
.put("350700", "南平")
.put("350800", "龙岩")
.put("360100", "南昌")
.put("360200", "景德镇")
.put("360300", "萍乡")
.put("360400", "九江")
.put("360500", "新余")
.put("360600", "鹰潭")
.put("360700", "赣州")
.put("360800", "吉安")
.put("360900", "宜春")
.put("361000", "抚州")
.put("370100", "济南")
.put("370200", "青岛")
.put("370300", "淄博")
.put("370400", "枣庄")
.put("370500", "东营")
.put("370600", "烟台")
.put("370700", "潍坊")
.put("370800", "济宁")
.put("370900", "泰安")
.put("371000", "威海").put("371100", "日照")
.put("371300", "临沂").put("371400", "德州")
.put("371500", "聊城").put("371600", "滨州")
.put("410100", "郑州").put("410200", "开封")
.put("410300", "洛阳").put("410400", "平顶山")
.put("410500", "安阳").put("410600", "鹤壁")
.put("410700", "新乡").put("410800", "焦作")
.put("410900", "濮阳").put("411000", "许昌")
.put("411100", "漯河").put("411200", "三门峡")
.put("411300", "南阳").put("411400", "商丘")
.put("411500", "信阳").put("411600", "周口").put("411700", "驻马店")
.put("420100", "武汉").put("420200", "黄石").put("420300", "十堰")
.put("420500", "宜昌").put("420600", "襄阳").put("420700", "鄂州")
.put("420800", "荆门").put("420900", "孝感").put("421000", "荆州")
.put("421100", "黄冈").put("421200", "咸宁").put("421300", "随州").put("422800", "恩施")
.put("429004", "仙桃").put("429005", "潜江").put("429006", "天门")
.put("430100", "长沙").put("430200", "株洲").put("430300", "湘潭")
.put("430400", "衡阳").put("430500", "邵阳").put("430600", "岳阳")
.put("430700", "常德").put("430800", "张家界").put("430900", "益阳")
.put("431000", "郴州").put("431100", "永州").put("431200", "怀化")
.put("431300", "娄底").put("440100", "广州").put("440200", "韶关")
.put("440300", "深圳").put("440400", "珠海").put("440500", "汕头")
.put("440600", "佛山").put("440700", "江门").put("440800", "湛江")
.put("440900", "茂名").put("441200", "肇庆").put("441300", "惠州")
.put("441400", "梅州").put("441500", "汕尾").put("441600", "河源")
.put("441700", "阳江").put("441800", "清远").put("441900", "东莞")
.put("442000", "中山").put("445100", "潮州").put("445200", "揭阳")
.put("450100", "南宁").put("450200", "柳州").put("450300", "桂林")
.put("450400", "梧州").put("450500", "北海").put("450600", "防城港")
.put("450700", "钦州").put("450800", "贵港").put("450900", "玉林")
.put("451000", "百色").put("451100", "贺州").put("451200", "河池")
.put("451300", "来宾").put("460100", "海口").put("460200", "三亚")
.put("460400", "儋州").put("469005", "文昌").put("469023", "澄迈")
.put("469024", "临高").put("469026", "昌江").put("469029", "保亭")
.put("500100", "重庆").put("510100", "成都").put("510300", "自贡")
.put("510400", "攀枝花").put("510500", "泸州").put("510600", "德阳")
.put("510700", "绵阳").put("510800", "广元").put("510900", "遂宁")
.put("511000", "内江").put("511100", "乐山").put("511300", "南充")
.put("511400", "眉山").put("511500", "宜宾").put("511600", "广安")
.put("511700", "达州").put("511800", "雅安").put("511900", "巴中")
.put("512000", "资阳").put("513200", "阿坝").put("513300", "甘孜")
.put("520100", "贵阳").put("520200", "六盘水").put("520300", "遵义")
.put("520400", "安顺").put("520500", "毕节").put("520600", "铜仁")
.put("522300", "黔西南").put("522600", "黔东南").put("530100", "昆明")
.put("530300", "曲靖").put("530400", "玉溪").put("530500", "保山")
.put("530600", "昭通").put("530700", "丽江").put("530800", "普洱")
.put("530900", "临沧").put("532300", "楚雄").put("532500", "红河")
.put("532600", "文山").put("532800", "西双版纳").put("532900", "大理")
.put("533300", "怒江").put("540100", "拉萨").put("610100", "西安")
.put("610200", "铜川").put("610300", "宝鸡").put("610400", "咸阳")
.put("610500", "渭南").put("610600", "延安").put("610700", "汉中")
.put("610800", "榆林").put("610900", "安康").put("620100", "兰州")
.put("620300", "金昌").put("620400", "白银").put("620500", "天水")
.put("620600", "武威").put("620700", "张掖").put("620800", "平凉")
.put("620900", "酒泉")
.put("621000", "庆阳")
.put("621100", "定西")
.put("621200", "陇南")
.put("630100", "西宁")
.put("630200", "海东")
.put("632500", "海南")
.put("632700", "玉树")
.put("640100", "银川")
.put("640200", "石嘴山")
.put("640300", "吴忠")
.put("640400", "固原")
.put("650100", "乌鲁木齐")
.put("650200", "克拉玛依")
.put("650500", "哈密")
.put("652300", "昌吉")
.put("652700", "博州")
.put("652800", "巴州")
.put("652900", "阿克苏")
.put("653100", "喀什")
.put("654000", "伊犁")
.put("654200", "塔城")
.build();

    public static ObjectMapper mapper = new ObjectMapper();
    @Autowired
    private ILianlianProductService lianlianProductService;
    //查询产品列表
    @Test
    public void queryProductListByCityCode(){
        Date date=new Date();
        List<LianlianProduct> productList=queryProductList(cityList);
        if(productList!=null && !productList.isEmpty()){
            productList.forEach(product->{
                LianlianProduct lianlianProduct=lianlianProductService.lambdaQuery().eq(LianlianProduct::getProductId,product.getProductId()).eq(LianlianProduct::getItemId,product.getItemId()).eq(LianlianProduct::getCity,product.getCity()).orderByDesc(LianlianProduct::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
               if(lianlianProduct!=null){
                   product.setUpdateTime(date);
                   lianlianProductService.lambdaUpdate().eq(LianlianProduct::getId,lianlianProduct.getId()).update(product);
               }else{
                   lianlianProductService.save(product);
               }
            });
        }
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        String json = null;
        try {
            json = objectMapper.writeValueAsString(productList);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        log.info("产品列表:{}",json);
    }

    public List<LianlianProduct> queryProductList(List<String> cityList){
        List<LianlianProduct> products=Lists.newArrayList();
        cityList.forEach(city->{
            Result<?> result=lianLianFenXiaoService.queryProductListByCityCode(Integer.valueOf(city), 1,100);
            if(result.isOK()){
                LianLianFenXiaoProduct productList=(LianLianFenXiaoProduct)result.getResult();
                if(productList!=null && !productList.getList().isEmpty()){
                    if(productList.getTotal()<=100){
                        productList.getList().forEach(product->{
                            if(product!=null && !product.getItemList().isEmpty()){
                                product.getItemList().forEach(item->{
                                    LianlianProduct lianlianProduct=new LianlianProduct();
                                    lianlianProduct.setOnlyName(product.getOnlyName()+item.getSubTitle());
                                    /**封面图*/
                                    lianlianProduct.setFaceImg(product.getFaceImg());
                                    /**售价(分)*/
                                    lianlianProduct.setSalePrice(item.getSalePrice());
                                    /**原价(分)*/
                                    lianlianProduct.setOriginPrice(item.getOriginPrice());
                                    /**渠道结算价(分)*/
                                    lianlianProduct.setChannelPrice(item.getChannelPrice());
                                    /**产品编码*/
                                    lianlianProduct.setProductId(product.getProductId());
                                    /**套餐编码*/
                                    lianlianProduct.setItemId(item.getItemId());
                                    /**核销码类型1-有批量核销码和独立核销码以及电子码,2-没有批量核销码有独立电子码（不能直接用电子码生成对应核销码）没有qrCodeImgUrl,qrCodeUrl为HTML,3-没有批量核销码和独立核销码以及电子码*/
                                    lianlianProduct.setCodeType(product.getCodeType());
                                    /**售卖数量*/
                                    lianlianProduct.setSingleMax(item.getSingleMax());
                                    /**库存数量*/
                                    lianlianProduct.setStock(item.getStock());
                                    /**城市编码*/
                                    lianlianProduct.setCity(city);
                                    /**城市*/
                                    lianlianProduct.setCityName(cityMap.get(city));
                                    /**预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明*/
                                    lianlianProduct.setBookingType(product.getBookingType());
                                    /**是否需要填写配送地址 0-否 1-是*/
                                    lianlianProduct.setBookingShowAddress(product.getBookingShowAddress());
                                    /**是否需要身份证 0-否 1-是*/
                                    lianlianProduct.setOrderShowIdCard(product.getOrderShowIdCard());
                                    /**是否需要填写使用日期 0-否 1-是*/
                                    lianlianProduct.setOrderShowDate(product.getOrderShowDate());
                                    try {
                                        /**抢购开始时间*/
                                        if(StringUtils.isNotBlank(product.getBeginTime())){
                                            lianlianProduct.setBeginTime(DateUtil.stringToDate(product.getBeginTime()));
                                        }

                                        /**抢购结束时间*/
                                        if(StringUtils.isNotBlank(product.getEndTime())){
                                            lianlianProduct.setEndTime(DateUtil.stringToDate(product.getEndTime()));
                                        }

                                        /**购买后——有效开始时间*/
                                        if(StringUtils.isNotBlank(product.getValidBeginDate())){
                                            lianlianProduct.setValidBeginDate(DateUtil.stringToDate(product.getValidBeginDate()));
                                        }

                                        /**购买后——有效结束时间*/
                                        if(StringUtils.isNotBlank(product.getValidEndDate())){
                                            lianlianProduct.setValidEndDate(DateUtil.stringToDate(product.getValidEndDate()));
                                        }

                                        /**预约开始时间*/
                                        if(StringUtils.isNotBlank(product.getBookingBeginDate())){
                                            lianlianProduct.setBookingBeginDate(DateUtil.stringToDate(product.getBookingBeginDate()));
                                        }
                                        /**产品上线时间*/
                                        if(StringUtils.isNotBlank(product.getReleaseTime())){
                                            lianlianProduct.setReleaseTime(DateUtil.stringToDate(product.getReleaseTime()));
                                        }
                                    } catch (ParseException e) {
                                        e.printStackTrace();
                                    }
                                    /**商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）*/
                                    lianlianProduct.setEcommerce(product.getEcommerce());
                                    products.add(lianlianProduct);
                                });
                            }
                        });
                    }else{
                        Integer page= Integer.valueOf(BigDecimal.valueOf(productList.getTotal()).divide(BigDecimal.valueOf(100),0, RoundingMode.UP).stripTrailingZeros().toPlainString());
                        for(int i=0;i<page;i++){
                            Result<?> resultRecursion=lianLianFenXiaoService.queryProductListByCityCode(Integer.valueOf(city), i,100);
                            if(resultRecursion.isOK()){
                                LianLianFenXiaoProduct productListRecursion=(LianLianFenXiaoProduct)resultRecursion.getResult();
                                    if(productListRecursion!=null && !productListRecursion.getList().isEmpty()){
                                        productListRecursion.getList().forEach(productRecursion->{
                                            if(productRecursion!=null && !productRecursion.getItemList().isEmpty()){
                                                productRecursion.getItemList().forEach(itemRecursion->{
                                                    LianlianProduct lianlianProduct=new LianlianProduct();
                                                    lianlianProduct.setOnlyName(productRecursion.getOnlyName()+itemRecursion.getSubTitle());
                                                    /**封面图*/
                                                    lianlianProduct.setFaceImg(productRecursion.getFaceImg());
                                                    /**售价(分)*/
                                                    lianlianProduct.setSalePrice(itemRecursion.getSalePrice());
                                                    /**原价(分)*/
                                                    lianlianProduct.setOriginPrice(itemRecursion.getOriginPrice());
                                                    /**渠道结算价(分)*/
                                                    lianlianProduct.setChannelPrice(itemRecursion.getChannelPrice());
                                                    /**产品编码*/
                                                    lianlianProduct.setProductId(productRecursion.getProductId());
                                                    /**套餐编码*/
                                                    lianlianProduct.setItemId(itemRecursion.getItemId());
                                                    /**核销码类型1-有批量核销码和独立核销码以及电子码,2-没有批量核销码有独立电子码（不能直接用电子码生成对应核销码）没有qrCodeImgUrl,qrCodeUrl为HTML,3-没有批量核销码和独立核销码以及电子码*/
                                                    lianlianProduct.setCodeType(productRecursion.getCodeType());
                                                    /**售卖数量*/
                                                    lianlianProduct.setSingleMax(itemRecursion.getSingleMax());
                                                    /**库存数量*/
                                                    lianlianProduct.setStock(itemRecursion.getStock());
                                                    /**城市编码*/
                                                    lianlianProduct.setCity(city);
                                                    /**城市*/
                                                    lianlianProduct.setCityName(cityMap.get(city));
                                                    /**预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明*/
                                                    lianlianProduct.setBookingType(productRecursion.getBookingType());
                                                    /**是否需要填写配送地址 0-否 1-是*/
                                                    lianlianProduct.setBookingShowAddress(productRecursion.getBookingShowAddress());
                                                    /**是否需要身份证 0-否 1-是*/
                                                    lianlianProduct.setOrderShowIdCard(productRecursion.getOrderShowIdCard());
                                                    /**是否需要填写使用日期 0-否 1-是*/
                                                    lianlianProduct.setOrderShowDate(productRecursion.getOrderShowDate());

                                                    try {
                                                        /**抢购开始时间*/
                                                        if(StringUtils.isNotBlank(productRecursion.getBeginTime())){
                                                            lianlianProduct.setBeginTime(DateUtil.stringToDate(productRecursion.getBeginTime()));
                                                        }

                                                        /**抢购结束时间*/
                                                        if(StringUtils.isNotBlank(productRecursion.getEndTime())){
                                                            lianlianProduct.setEndTime(DateUtil.stringToDate(productRecursion.getEndTime()));
                                                        }

                                                        /**购买后——有效开始时间*/
                                                        if(StringUtils.isNotBlank(productRecursion.getValidBeginDate())){
                                                            lianlianProduct.setValidBeginDate(DateUtil.stringToDate(productRecursion.getValidBeginDate()));
                                                        }

                                                        /**购买后——有效结束时间*/
                                                        if(StringUtils.isNotBlank(productRecursion.getValidEndDate())){
                                                            lianlianProduct.setValidEndDate(DateUtil.stringToDate(productRecursion.getValidEndDate()));
                                                        }

                                                        /**预约开始时间*/
                                                        if(StringUtils.isNotBlank(productRecursion.getBookingBeginDate())){
                                                            lianlianProduct.setBookingBeginDate(DateUtil.stringToDate(productRecursion.getBookingBeginDate()));
                                                        }
                                                        /**产品上线时间*/
                                                        if(StringUtils.isNotBlank(productRecursion.getReleaseTime())){
                                                            lianlianProduct.setReleaseTime(DateUtil.stringToDate(productRecursion.getReleaseTime()));
                                                        }
                                                    } catch (ParseException e) {
                                                        e.printStackTrace();
                                                    }
                                                    /**商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）*/
                                                    lianlianProduct.setEcommerce(productRecursion.getEcommerce());
                                                    products.add(lianlianProduct);
                                                });
                                            }
                                        });
                                    }
                            }
                        }
                    }
                }
            }
        });
        return products;
    }

    //查询产品信息
    @Test
    public void queryProductDetailByProductId(){
        Map<String,String> productMap=new ImmutableMap.Builder<String, String>()
                .put("930714", "1442175")
                .put("1001258", "1647397")
                .put("927768", "1436458")
                .put("1012687", "1702901")
                .put("1010925", "1694300")
                .put("1011954", "1698734")
                .put("1011569", "1697453")
                .put("987091", "1599306")
                .put("1010922", "1694297")
                .put("985267", "1593508")
                .put("1008645", "1681905")
                .put("1004486", "1663308")
                .put("1004628", "1663748")
                .put("987075", "1598761")
                .build();
        List<LianlianProduct> productList=queryProductDetail(productMap);
        if(productList!=null && !productList.isEmpty()){
            productList.forEach(product->{
                LianlianProduct lianlianProduct=lianlianProductService.lambdaQuery().eq(LianlianProduct::getProductId,product.getProductId()).eq(LianlianProduct::getItemId,product.getItemId()).eq(LianlianProduct::getCity,product.getCity()).orderByDesc(LianlianProduct::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(lianlianProduct!=null){
                    product.setUpdateTime(new Date());
                    product.setCity(lianlianProduct.getCity());
                    product.setCityName(lianlianProduct.getCityName());
                    lianlianProductService.lambdaUpdate().eq(LianlianProduct::getId,lianlianProduct.getId()).update(product);
                }else{
                    lianlianProductService.save(product);
                }
            });
        }
        log.info("产品列表:{}",productList);
    }
    public List<LianlianProduct> queryProductDetail(Map<String,String> productMap){
        List<LianlianProduct> products=Lists.newArrayList();
        productMap.forEach((productId, itemId) -> {
            Result<?> result=lianLianFenXiaoService.queryProductDetailByProductId(productId, itemId);
            if(result.isOK()){
                LianLianFenXiaoProductDetail productDetail=(LianLianFenXiaoProductDetail)result.getResult();
                if(productDetail!=null){
                    productDetail.getItemList().forEach(item->{
                        LianlianProduct lianlianProduct=new LianlianProduct();
                        lianlianProduct.setOnlyName(productDetail.getOnlyName()+item.getSubTitle());
                        /**封面图*/
                        lianlianProduct.setFaceImg(productDetail.getFaceImg());
                        /**售价(分)*/
                        lianlianProduct.setSalePrice(item.getSalePrice());
                        /**原价(分)*/
                        lianlianProduct.setOriginPrice(item.getOriginPrice());
                        /**渠道结算价(分)*/
                        lianlianProduct.setChannelPrice(item.getChannelPrice());
                        /**产品编码*/
                        lianlianProduct.setProductId(productDetail.getProductId());
                        /**套餐编码*/
                        lianlianProduct.setItemId(item.getItemId());
                        /**核销码类型1-有批量核销码和独立核销码以及电子码,2-没有批量核销码有独立电子码（不能直接用电子码生成对应核销码）没有qrCodeImgUrl,qrCodeUrl为HTML,3-没有批量核销码和独立核销码以及电子码*/
                        lianlianProduct.setCodeType(productDetail.getCodeType());
                        /**售卖数量*/
                        lianlianProduct.setSingleMax(item.getSingleMax());
                        /**库存数量*/
                        lianlianProduct.setStock(item.getStock());
                        /**城市编码*/
                        lianlianProduct.setCity("");
                        /**城市*/
                        lianlianProduct.setCityName("");
                        /**预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明*/
                        lianlianProduct.setBookingType(productDetail.getBookingType());
                        /**是否需要填写配送地址 0-否 1-是*/
                        lianlianProduct.setBookingShowAddress(productDetail.getBookingShowAddress());
                        /**是否需要身份证 0-否 1-是*/
                        lianlianProduct.setOrderShowIdCard(productDetail.getOrderShowIdCard());
                        /**是否需要填写使用日期 0-否 1-是*/
                        lianlianProduct.setOrderShowDate(productDetail.getOrderShowDate());
                        try {
                            /**抢购开始时间*/
                            if(StringUtils.isNotBlank(productDetail.getBeginTime())){
                                lianlianProduct.setBeginTime(DateUtil.stringToDate(productDetail.getBeginTime()));
                            }

                            /**抢购结束时间*/
                            if(StringUtils.isNotBlank(productDetail.getEndTime())){
                                lianlianProduct.setEndTime(DateUtil.stringToDate(productDetail.getEndTime()));
                            }

                            /**购买后——有效开始时间*/
                            if(StringUtils.isNotBlank(productDetail.getValidBeginDate())){
                                lianlianProduct.setValidBeginDate(DateUtil.stringToDate(productDetail.getValidBeginDate()));
                            }

                            /**购买后——有效结束时间*/
                            if(StringUtils.isNotBlank(productDetail.getValidEndDate())){
                                lianlianProduct.setValidEndDate(DateUtil.stringToDate(productDetail.getValidEndDate()));
                            }

                            /**预约开始时间*/
                            if(StringUtils.isNotBlank(productDetail.getBookingBeginDate())){
                                lianlianProduct.setBookingBeginDate(DateUtil.stringToDate(productDetail.getBookingBeginDate()));
                            }
                            /**产品上线时间*/
                            if(StringUtils.isNotBlank(productDetail.getReleaseTime())){
                                lianlianProduct.setReleaseTime(DateUtil.stringToDate(productDetail.getReleaseTime()));
                            }
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        /**商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）*/
                        lianlianProduct.setEcommerce(productDetail.getEcommerce());
                        products.add(lianlianProduct);
                    });

                }
            }
        });
        return products;
    }
    //查询产品图文详情
    @Test
    public void queryProductImgByProductId(){
        Result<?> result=lianLianFenXiaoService.queryProductImgByProductId("3288990");
        log.info("查询产品图文详情-result:{}",result);
    }




    //查询城市店铺
    @Test
    public void queryShopListByByCityCode(){
        Result<?> result=lianLianFenXiaoService.queryShopListByByCityCode(510100, 0,10);
        log.info("查询城市店铺-result:{}",result);
    }

    //查询产品分类
    @Test
    public void queryProductClassByIsTree(){

        Result<?> result=lianLianFenXiaoService.queryProductClassByIsTree(0);
        log.info("查询产品分类-result:{}",result);
    }

    //验证-渠道订单创建条件和渠道订单创建
    @Test
    public void checkCreateOrder(){
        //                                    "bookingType": 1, // 预约方式 0-无需预约 1-网址预约 2-电话预约
//                                            "bookingShowAddress": 0, // 是否需要填写配送地址
//                                            "orderShowIdCard": 0, // 是否需要身份证
//                                            "orderShowDate": 0, // 是否需要填写使用日期
//                                            "beginTime": "2023-04-20 00:00:00", //抢购开始时间（可能为null）
//                                            "endTime": "2023-05-19 15:59:59", // 抢购结束时间
//                                            "validBeginDate": "2023-04-19 16:00:00", //购买后——有效开始时间（可能为null）
//                                            "validEndDate": "2023-06-30 15:59:59", //购买后——有效结束时间
//                                            "bookingBeginDate": null, // 预约开始时间
//                                            "releaseTime": "2023-04-20 01:47:18",  // 产品上线时间
//                                            "singleMin": 1, // 单次最小购买量
//                                            "singleMax": 10, // 单次最大购买量
//                                            "ecommerce": 0, // 商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）
        String mobile="15915401656";
        String orderNo= IdWorker.get32UUID();
        String productId="1012474";
        Integer itemId=1702353;
        Integer settlePrice=18246;
        String travelDate= DateUtil.formatSplitTime(LocalDateTime.now());
        String idCard="";
        String customerName="杨涛";
        String customerPhoneNumber="15915401656";
        String address="";
        Integer thirdSalePrice=18800;
        String memo="希望便宜点";
        Result<?> result=lianLianFenXiaoService.checkCreateOrder( mobile, orderNo, productId, itemId, settlePrice, travelDate, idCard, customerName, customerPhoneNumber, address, thirdSalePrice, memo);
        log.info("验证-渠道订单创建条件-result:{}",result);
        if(result.isOK()){
            LianLianFenXiaoCheckCreateOrder checkCreateOrder=(LianLianFenXiaoCheckCreateOrder)result.getResult();
            LianLianFenXiaoCreateOrder createOrder=(LianLianFenXiaoCreateOrder)lianLianFenXiaoService.createOrder(mobile,checkCreateOrder.getValidToken(), orderNo, productId, itemId, settlePrice, travelDate, idCard, customerName, customerPhoneNumber, address, thirdSalePrice, memo).getResult();
            log.info("渠道订单创建-result:{}",createOrder);
        }
    }

    //查询订单详情
    @Test
    public void queryOrderDetail(){
        String mobile="15915401656";
        String orderNo= "C240425011750010799";
        Result<?> result=lianLianFenXiaoService.queryOrderDetail(mobile,orderNo);
        log.info("查询订单详情-result:{}",result);
    }
    //刷新二维码
    @Test
    public void refreshCode(){
        String mobile="15915401656";
        String orderNo= "C240425012357010799";
        Result<?> result=lianLianFenXiaoService.refreshCode( mobile, orderNo);
        log.info("刷新二维码-result:{}",result);
    }

    //退款 渠道订单号和（小订单号列表）
    @Test
    public void refundOrder(){
        String mobile="15915401656";
        String orderNo= "C240516013114010799";
        List<String> orderList= Lists.newArrayList();
        orderList.add("240516013114");
        Result<?> result=lianLianFenXiaoService.refundOrder( mobile, orderNo,orderList);
        log.info("退款-渠道订单号和（小订单号列表）-result:{}",result);
    }


    //查询物流信息（小订单号）
    @Test
    public void queryExpress(){
        String mobile="15915401656";
        String orderId= "";
        Result<?> result=lianLianFenXiaoService.queryExpress( mobile, orderId);
        log.info("查询物流信息-result:{}",result);
    }

    //重发短信
    @Test
    public void refreshSms(){
        String mobile="15915401656";
        String orderNo= "";
        List<String> orderList= Lists.newArrayList();
        orderList.add("");
        Result<?> result=lianLianFenXiaoService.refreshSms( mobile, orderNo,  orderList);
        log.info("重发短信-result:{}",result);
    }
    @Test
    public void  guiZhouMobilePayRechargeQueueListener(){
        GuiZhouMobilePayRechargeDeductMessage guiZhouMobilePayRechargeDeductMessage=new GuiZhouMobilePayRechargeDeductMessage();
        guiZhouMobilePayRechargeDeductMessage.setId("1791023506785644546");
        guiZhouMobilePayRechargeDeductMessage.setOrderId("3ff408b8c29150ea4ac1541ff4b1a13a");
        log.info("贵州移动省包（在线支付）领取权益消息队列接收消息:{}",guiZhouMobilePayRechargeDeductMessage);
        try {
            //贵州移动省包（在线支付）领取权益
            lianlianChargeLogService.guiZhouMobilePayRechargeScheduleDeduct(guiZhouMobilePayRechargeDeductMessage.getId(),guiZhouMobilePayRechargeDeductMessage.getOrderId());
        } catch (Exception e) {
            log.info("处理收到的消息:{},异常!",guiZhouMobilePayRechargeDeductMessage,e);
        }
    }
    @Autowired
    private IMemberRightsService memberRightsService;
    @Test
    public void  lianLianProduct(){
            List<MemberRights> memberRightsList=memberRightsService.lambdaQuery().select(MemberRights::getId,MemberRights::getCouponId,MemberRights::getRightsId,MemberRights::getRightsName).eq(MemberRights::getCompanyOwner,"LIANLIAN").list();
            for(MemberRights memberRights:memberRightsList){
                try {
                    Result<?> result=lianLianFenXiaoService.queryProductDetailByProductId(memberRights.getCouponId(), memberRights.getRightsId());
                    if(!result.isOK()){
                        memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                        continue;
                    }
                    LianLianFenXiaoProductDetail productDetail=(LianLianFenXiaoProductDetail)result.getResult();
                    if(productDetail==null){
                        memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                        continue;
                    }
                    if(productDetail.getItemStock().intValue()>0){
                        Optional<LianLianFenXiaoProductDetail.ItemList> itemListOptional = productDetail.getItemList().stream()
                                .filter(item -> memberRights.getRightsId().equals(String.valueOf(item.getItemId())))
                                .findAny();
                        if(itemListOptional.isPresent() && itemListOptional.get().getStock()!=null && itemListOptional.get().getStock().intValue()>0){
                            memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,1).update();
                        }else{
                            memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                        }
                        continue;
                    }
                    if(productDetail.getStockAmount()!=null && productDetail.getStockAmount().intValue()>0){
                        memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,1).update();
                    }else{
                        memberRightsService.lambdaUpdate().eq(MemberRights::getId,memberRights.getId()).set(MemberRights::getRightsSwitchs,0).update();
                    }
                } catch (Exception e) {
                    log.error("产品ID:{},套餐ID:{},产品名称:{},联联分销产品同步上架下架异常:{}",memberRights.getCouponId(),memberRights.getRightsId(),memberRights.getRightsName(),e.getMessage());
                    e.printStackTrace();
                }
            }
    }
}
