package org.jeecg;

import com.eleven.cms.config.LiantongVrbtProperties;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.ISubscribeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:Todo
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MobileRegionServiceTest {

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    LiantongVrbtProperties liantongVrbtProperties;


    @Test
    public void query() {
        System.out.println(mobileRegionService.query("13438828200"));
        System.out.println(mobileRegionService.query("19390279881"));
        //System.out.println(mobileRegionService.query("18080928200"));
        //        final MobileRegionResult query = mobileRegionService.query("18613222501");  //联通成都
        //final MobileRegionResult query = mobileRegionService.query("13182763445");  //联通山东
        //final MobileRegionResult query = mobileRegionService.query("16651044676");  //联通江苏
        
        //System.out.println(liantongVrbtProperties.getUnSupportProvinceList().contains(query.getProvince()));
    }

    //@Test
    //public void updateSubscribe() {
    //    subscribeService.lambdaQuery().select(Subscribe::getId,Subscribe::getMobile).isNull(Subscribe::getProvince).list()
    //                    .forEach(sub->{
    //                        try {
    //                            final MobileRegionResult result = mobileRegionService.query(sub.getMobile());
    //                            if(result!=null){
    //                                subscribeService.lambdaUpdate().eq(Subscribe::getId,sub.getId())
    //                                .set(Subscribe::getProvince,result.getProvince())
    //                                .set(Subscribe::getCity,result.getCity())
    //                                .update();
    //                            }
    //                        } catch (Exception e) {
    //                            e.printStackTrace();
    //                        }
    //                    });
    //}

}