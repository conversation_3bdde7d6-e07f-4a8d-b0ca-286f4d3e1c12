package org.jeecg;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Author: <EMAIL>
 * Date: 2024/10/17 21:08
 * Desc:Todo
 */
public class CompletableFuturePparallelStreamTest {

    /**
     * @Description 使用这个类去构建一个运行长时间的任务-用于测试
     * @ClassName MyTask   原文链接：https://blog.csdn.net/qq_37896194/article/details/120437988
     * <AUTHOR>
     * @Date 2021/9/23 4:23 下午
     */
    public static void main(String[] args) {

        // 创建15个任务，每个任务执行时间是1秒
        List<MyTask> tasks = IntStream.range(0, 24)
                .mapToObj(i -> new MyTask(1))
                .collect(Collectors.toList());

        ////正常串行执行
        //long start = System.nanoTime();
        //List<Integer> result = tasks.stream()
        //        .map(MyTask::calculate)
        //        .collect(Collectors.toList());
        //long duration = (System.nanoTime() - start) / 1_000_000;
        //System.out.printf("Processed %d tasks in %d millis\n", tasks.size(), duration);
        //System.out.println(result);


        //使用并行流执行(底层使用ForkJoinPool,默认线程数量为cpu数)
        //long start = System.nanoTime();
        //List<Integer> result = tasks.parallelStream()
        //        .map(MyTask::calculate)
        //        .collect(Collectors.toList());
        //long duration = (System.nanoTime() - start) / 1_000_000;
        //System.out.printf("Processed %d tasks in %d millis\n", tasks.size(), duration);
        //System.out.println(result);

        //使用并行流执行(底层使用ForkJoinPool,默认线程数量为cpu数),并自定义ForkJoinPool
        final int parallelism = 4;
        ForkJoinPool forkJoinPool = new ForkJoinPool(parallelism);
        long start = System.nanoTime();
        try {

            List<Integer> result  = forkJoinPool.submit(() ->
                    // Parallel task here, for example
                    tasks.parallelStream()
                            .map(MyTask::calculate)
                            .collect(Collectors.toList())).get();
            long duration = (System.nanoTime() - start) / 1_000_000;
            System.out.printf("Processed %d tasks in %d millis\n", tasks.size(), duration);
            System.out.println(result);
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            forkJoinPool.shutdown();
        }

        //使用CompletableFutures(底层使用ForkJoinPool,默认线程数量为cpu数)
        //CompletableFutures大家应该不陌生，我们首先获取CompletableFutures集合，然后在每个future上调用join方法去等待他们逐一执行完。注意，join方法类似于get方法，唯一的不通点是前者不会抛出任何的受检查异常，所以在lambda表达式中更方便一些。
        //再有，你必须使用两个独立的stream(pipelines)管道，而不是将两个map操作放在一起，因为stream的中间操作都是懒加载的(intermediate stream operations are lazy)，你最终必须按顺序处理你的任务。这就是为什么首先需要CompletableFuture在list中，然后允许他们开始执行，直到执行完毕。
        //long start = System.nanoTime();
        //List<CompletableFuture<Integer>> futures =
        //        tasks.stream()
        //                .map(t -> CompletableFuture.supplyAsync(t::calculate))
        //                .collect(Collectors.toList());
        ////注意此处你必须使用两个独立的stream(pipelines)管道，而不是将两个map操作放在一起，因为stream的中间操作都是懒加载的(intermediate stream operations are lazy)，你最终必须按顺序处理你的任务。这就是为什么首先需要CompletableFuture在list中，然后允许他们开始执行，直到执行完毕。
        //List<Integer> result =
        //        futures.stream()
        //                .map(CompletableFuture::join)
        //                .collect(Collectors.toList());
        //long duration = (System.nanoTime() - start) / 1_000_000;
        //System.out.printf("Processed %d tasks in %d millis\n", tasks.size(), duration);
        //System.out.println(result);

        //使用CompletableFutures(底层使用ForkJoinPool,默认线程数量为cpu数),并使用自定义的线程池或者自定义的ForkJoinPool
        //CompletableFutures大家应该不陌生，我们首先获取CompletableFutures集合，然后在每个future上调用join方法去等待他们逐一执行完。注意，join方法类似于get方法，唯一的不通点是前者不会抛出任何的受检查异常，所以在lambda表达式中更方便一些。
        //再有，你必须使用两个独立的stream(pipelines)管道，而不是将两个map操作放在一起，因为stream的中间操作都是懒加载的(intermediate stream operations are lazy)，你最终必须按顺序处理你的任务。这就是为什么首先需要CompletableFuture在list中，然后允许他们开始执行，直到执行完毕。
        //ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(
        //        // 核心线程数量
        //        // 线程大小的规则：如果服务是cpu密集型的，设置为电脑的核数；如果服务是io密集型的，设置为电脑的核数*2
        //        Runtime.getRuntime().availableProcessors() * 2,
        //        // 最大线程数量
        //        Runtime.getRuntime().availableProcessors() * 4,
        //        // 当线程空闲时，保持活跃的时间
        //        30L,
        //        // 时间单元 ，毫秒级
        //        TimeUnit.SECONDS,
        //        // 线程任务队列
        //        new LinkedBlockingQueue<>(20000),
        //        // 创建线程的工厂
        //        //Executors.defaultThreadFactory(),//注意此非守护进程最导致程序无法结束
        //        new ThreadFactoryBuilder().setDaemon(true).build(),//此处使用guava的线程工厂，默认是守护进程，不会导致程序无法结束
        //        // 线程池的拒绝策略
        //        //线程池中，有三个重要的参数，决定影响了拒绝策略：corePoolSize - 核心线程数，也即最小的线程数。workQueue - 阻塞队列 。 maximumPoolSize - 最大线程数
        //        //当提交任务数大于 corePoolSize 的时候，会优先将任务放到 workQueue 阻塞队列中。当阻塞队列饱和后，会扩充线程池中线程数，直到达到 maximumPoolSize 最大线程数配置。此时，再多余的任务，则会触发线程池的拒绝策略了。总结起来，也就是一句话，当提交的任务数大于（workQueue.size() + maximumPoolSize ），就会触发线程池的拒绝策略。
        //        //二、拒绝策略定义
        //        //拒绝策略提供顶级接口 RejectedExecutionHandler ，其中方法 rejectedExecution 即定制具体的拒绝策略的执行逻辑。
        //        //jdk默认提供了四种拒绝策略：
        //        //
        //        //CallerRunsPolicy - 当触发拒绝策略，只要线程池没有关闭的话，则使用调用线程直接运行任务。一般并发比较小，性能要求不高，不允许失败。但是，由于调用者自己运行任务，如果任务提交速度过快，可能导致程序阻塞，性能效率上必然的损失较大
        //        //AbortPolicy - 丢弃任务，并抛出拒绝执行 RejectedExecutionException 异常信息。线程池默认的拒绝策略。必须处理好抛出的异常，否则会打断当前的执行流程，影响后续的任务执行。
        //        //DiscardPolicy - 直接丢弃，其他啥都没有
        //        //DiscardOldestPolicy - 当触发拒绝策略，只要线程池没有关闭的话，丢弃阻塞队列 workQueue 中最老的一个任务，并将新任务加入
        //        new ThreadPoolExecutor.AbortPolicy());
        //
        //long start = System.nanoTime();
        //List<CompletableFuture<Integer>> futures =
        //        tasks.stream()
        //                //使用 ForkJoinPool
        //                //.map(t -> CompletableFuture.supplyAsync(t::calculate,new ForkJoinPool(4)))
        //                //使用 固定线程池,注意线程池的线程工厂创建的线程要为守护线程
        //                //.map(t -> CompletableFuture.supplyAsync(t::calculate,Executors.newFixedThreadPool(4,new ThreadFactoryBuilder().setDaemon(true).build() )))
        //                //使用 自定义线程池,注意线程池的线程工厂创建的线程要为守护线程
        //                .map(t -> CompletableFuture.supplyAsync(t::calculate,poolExecutor))
        //                .collect(Collectors.toList());
        ////注意此处你必须使用两个独立的stream(pipelines)管道，而不是将两个map操作放在一起，因为stream的中间操作都是懒加载的(intermediate stream operations are lazy)，你最终必须按顺序处理你的任务。这就是为什么首先需要CompletableFuture在list中，然后允许他们开始执行，直到执行完毕。
        //List<Integer> result =
        //        futures.stream()
        //                .map(CompletableFuture::join)
        //                .collect(Collectors.toList());
        //long duration = (System.nanoTime() - start) / 1_000_000;
        //System.out.printf("Processed %d tasks in %d millis\n", tasks.size(), duration);
        //System.out.println(result);

        

    }



}

class MyTask {
    private final int quantity;

    public MyTask (int quantity) {
        this.quantity = quantity;
    }

    public int calculate() {
        System.out.println(Thread.currentThread().getName());
        try {
            Thread.sleep(quantity * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return quantity;
    }
}
