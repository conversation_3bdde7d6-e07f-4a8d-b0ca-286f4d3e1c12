package org.jeecg;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.job.VrbtZeroOrderTask;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.XunfeiJingxianVrbtService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.XunfeiVrbtZeroOrderResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2022-08-12 15:33
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class XunfeiJingxianVrbtServiceTest {

    @Autowired
    XunfeiJingxianVrbtService xunfeiJingxianVrbtService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    MiguApiService miguApiService;

    @Test
    public void resultCallback() throws IOException {

        File file = new File("D://xunfei.txt");
        String contents = FileUtils.readFileToString(file);
        String[] contentArray = StringUtils.split(contents, "\r\n");
        for (int i = 0; i < contentArray.length; i++) {
           String[] dataArray = contentArray[i].split(",");
           String mobile = dataArray[0];
           String channel = dataArray[1];
            xunfeiJingxianVrbtService.resultNewCallback(mobile, channel, SUBSCRIBE_STATUS_SUCCESS, "计费成功");
        }


//        xunfeiJingxianVrbtService.resultNewCallback("13438828200", MiguApiService.XUNFEI_CHANNEL_CODE, 1, "测试数据");
//        xunfeiJingxianVrbtService.resultCallback("13438828200", MiguApiService.XUNFEI_CHANNEL_CODE_02F, 1, "测试数据");
//        xunfeiJingxianVrbtService.resultCallback("13438828200", MiguApiService.XUNFEI_CHANNEL_CODE_02G, 1, "测试数据");
    }

    public static void main(String[] args) throws IOException {
        File file = new File("D://xunfei.txt");
        String contents = FileUtils.readFileToString(file);
        String[] contentArray = StringUtils.split(contents, "\r\n");
        for (int i = 0; i < contentArray.length; i++) {
            System.out.println(contentArray[i]);
        }

    }

    @Test
    public void resultNewCallback() {


        final String channel = "00211AI";
        subscribeService.lambdaQuery()
                .between(Subscribe::getCreateTime, "2024-09-01 00:00:00", "2024-09-02 23:59:59")
                .eq(Subscribe::getChannel, channel)
                .eq(Subscribe::getStatus, 1)
                .list().forEach(subscribe -> {
                    xunfeiJingxianVrbtService.resultNewCallback(subscribe.getMobile(), channel, 1, subscribe.getResult());
                });

    }

    @Test
    public void queryVrbtColumn() throws JsonProcessingException {
        System.out.println(new ObjectMapper().writeValueAsString(Result.ok(xunfeiJingxianVrbtService.queryVrbtColumn()))) ;
    }

    /**
     * 339033  精选
     * 339037  国风
     * 339045  颜值
     * 339049  节日
     * @throws JsonProcessingException
     */
    @Test
    public void queryVrbtRing() {
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339033",1)) ;
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339033",2)) ;
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339037",1)) ;
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339037",2)) ;
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339045",1)) ;
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339045",2)) ;
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339049",1)) ;
        //System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339049",2)) ;

        System.out.println(xunfeiJingxianVrbtService.queryVrbtRing("339653",1)) ;
    }

    @Test
    public void getRandomVrbtRing() throws JsonProcessingException {
        System.out.println(xunfeiJingxianVrbtService.getRandomVrbtRing()) ;
        System.out.println(xunfeiJingxianVrbtService.getRandomVrbtRing()) ;
        System.out.println(xunfeiJingxianVrbtService.getRandomVrbtRing()) ;
    }

    @Test
    public void vrbtZeroOrder() {
        //String mobile = "13438828200";
        //String channelCode = VrbtZeroOrderTask.CHANNEL_XUNFEI;
        //final XunfeiVrbtZeroOrderResult xunfeiVrbtZeroOrderResult = xunfeiJingxianVrbtService.vrbtZeroOrder(mobile);
        //System.out.println("xunfeiVrbtZeroOrderResult = " + xunfeiVrbtZeroOrderResult);
        //final String randomVrbtRing = xunfeiJingxianVrbtService.getRandomVrbtRing();
        //System.out.println("randomVrbtRing = " + randomVrbtRing);
        //final RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, randomVrbtRing, "0");
        //System.out.println("remoteResult = " + remoteResult);

        String mobile = "13438828200";
        String channelCode = VrbtZeroOrderTask.CHANNEL_XUNFEI_TEMP;
        final XunfeiVrbtZeroOrderResult xunfeiVrbtZeroOrderResult = xunfeiJingxianVrbtService.vrbtZeroOrderTemp(mobile);
        System.out.println("xunfeiVrbtZeroOrderResult = " + xunfeiVrbtZeroOrderResult);
        //final String randomVrbtRing = xunfeiJingxianVrbtService.getRandomVrbtRingTemp();
        //System.out.println("randomVrbtRing = " + randomVrbtRing);
        //System.out.println(miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, randomVrbtRing, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE));
        System.out.println(miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, "699295T8657", MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE));
    }

}
