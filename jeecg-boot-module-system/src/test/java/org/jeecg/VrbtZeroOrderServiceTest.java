package org.jeecg;

import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.eleven.cms.entity.VrbtZeroOrder;
import com.eleven.cms.job.VrbtZeroOrderTask;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.IVrbtZeroOrderService;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * Author: <EMAIL>
 * Date: 2020/6/9 10:13
 * Desc:视频彩铃包月0元订购测试
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class VrbtZeroOrderServiceTest {

    @Autowired
    MiguApiService miguApiService;
    @Autowired
    IMemberService memberService;
    @Autowired
    IVrbtZeroOrderService vrbtZeroOrderService;

    /**
     * 先给自己充一个试试有没有短信
     */
    @Test
    public void vrbtZeroOrderWithLog(){
        final FebsResponse febsResponse = memberService.rechargeVrbtRights("13438828200", 1);
        System.out.println("febsResponse = " + febsResponse);
    }

    /**
     * 我放三方支付视频彩铃0元订购
     */
    @Test
    public void vrbtZeroOrder(){
        //vrbtZeroOrderService.vrbtZeroOrder(851,1200,5000 );

    }

    /**
     * 讯飞三方支付视频彩铃包月0元订购
     */
    @Test
    public void vrbtZeroOrderXunfei(){
        //vrbtZeroOrderService.vrbtZeroOrderXunfei(1,600,5000);
    }

    @Test
    public void vrbtZeroOrderXunfeiTemp(){
        //System.out.println(
        //        vrbtZeroOrderService.lambdaQuery()
        //                //.eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
        //                .gt(VrbtZeroOrder::getModifyTime, LocalDate.now().atStartOfDay())
        //                .eq(VrbtZeroOrder::getChannel, VrbtZeroOrderTask.CHANNEL_XUNFEI_TEMP)
        //                .eq(VrbtZeroOrder::getExtra,"999029:【OPEN】非视频彩铃包月用户")
        //                .count()
        //);
        final AtomicInteger counter = new AtomicInteger(1);
        vrbtZeroOrderService.lambdaQuery()
            //.eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
            .gt(VrbtZeroOrder::getModifyTime, LocalDate.now().atStartOfDay())
            .eq(VrbtZeroOrder::getChannel, VrbtZeroOrderTask.CHANNEL_XUNFEI_TEMP)
            .eq(VrbtZeroOrder::getExtra,"999029:【OPEN】非视频彩铃包月用户")
            .list()
            .forEach(order -> {
                if(LocalTime.now().isBefore(LocalTime.of(23,59,59))){
                    String channelCode = order.getChannel();
                    String copyrightId = order.getCopyrightId();
                    String mobile = order.getMobile();
                    log.info("视频彩铃包月0元订购(讯飞),随机订购一首视频彩铃开始=>id:{},手机号:{},渠道号:{},版权id:{}", order.getId(), order.getMobile(), channelCode, copyrightId);
                    RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                    vrbtZeroOrderService.lambdaUpdate()
                            .eq(VrbtZeroOrder::getId, order.getId())
                            .set(VrbtZeroOrder::getExtra,remoteResult.getResCode()+":"+remoteResult.getResMsg())
                            .set(VrbtZeroOrder::getPrice,0)
                            .update();
                    System.out.println("已处理：" + counter.getAndIncrement());
                }
            });
    }

}
