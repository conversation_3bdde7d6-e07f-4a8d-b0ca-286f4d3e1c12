<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>jeecg-boot-module-system</artifactId>

	<parent>
		<groupId>org.jeecgframework.boot</groupId>
		<artifactId>jeecg-boot-parent</artifactId>
		<version>2.2.0</version>
	</parent>

	<repositories>
		<repository>
			<id>aliyun</id>
			<name>aliyun Repository</name>
			<url>https://maven.aliyun.com/nexus/content/groups/public</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>jeecg</id>
			<name>jeecg Repository</name>
			<url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>
	
	<dependencies>
		<dependency>
			<groupId>org.jeecgframework.boot</groupId>
			<artifactId>jeecg-boot-base-common</artifactId>
		</dependency>
        <dependency>
            <groupId>org.eleven</groupId>
            <artifactId>eleven-cms</artifactId>
            <version>1.0.0</version>
        </dependency>
	</dependencies>

    <build>
        <finalName>eleven-cms-vrbt-miniapp</finalName>
        <!--        springboot 项目打包优化（核心 class 与依赖 jar 分离） lib分离 参看https://xie.infoq.cn/article/37c92671412efef3a07a0c3f7-->
        <!--       启动命令 java -Dloader.path=eleven-cms-vrbt-lib/ -jar eleven-cms-vrbt.jar-->
        <plugins>
            <!--            <plugin>-->
            <!--                <groupId>org.springframework.boot</groupId>-->
            <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
            <!--                <configuration>-->
            <!--                    &lt;!&ndash; 指定该Main Class为全局的唯一入口 &ndash;&gt;-->
            <!--                    <mainClass>org.jeecg.JeecgApplication</mainClass>-->
            <!--                    &lt;!&ndash;把第三方jar包打入jar &ndash;&gt;-->
            <!--                    <includeSystemScope>true</includeSystemScope>-->
            <!--                    <fork>true</fork>-->
            <!--                    &lt;!&ndash; 设置为ZIP，此模式下spring-boot-maven-plugin会将Manifest.MF文件中的Main-Class设置为org.springframework.boot.loader.PropertiesLauncher &ndash;&gt;-->
            <!--                    <layout>ZIP</layout>-->
            <!--                    <includes>-->
            <!--                    &lt;!&ndash; 设置没有jar包,需要的话就注释掉&ndash;&gt;-->
            <!--&lt;!&ndash;                        <include>&ndash;&gt;-->
            <!--&lt;!&ndash;                            <groupId>nothing</groupId>&ndash;&gt;-->
            <!--&lt;!&ndash;                            <artifactId>nothing</artifactId>&ndash;&gt;-->
            <!--&lt;!&ndash;                        </include>&ndash;&gt;-->
            <!--                        <include>-->
            <!--                            <groupId>org.jeecgframework.boot</groupId>-->
            <!--                            <artifactId>jeecg-boot-base-common</artifactId>-->
            <!--                        </include>-->
            <!--                        <include>-->
            <!--                            <groupId>org.eleven</groupId>-->
            <!--                            <artifactId>eleven-cms</artifactId>-->
            <!--                        </include>-->
            <!--                    </includes>-->
            <!--                </configuration>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>repackage</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-dependency-plugin</artifactId>-->
            <!--                <version>3.3.0</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>copy-dependencies</goal>-->
            <!--                        </goals>-->
            <!--                        <configuration>-->
            <!--                            &lt;!&ndash; ${project.build.directory}是maven变量，内置的，表示target目录,如果不写，将在跟目录下创建/lib &ndash;&gt;-->
            <!--                            <outputDirectory>${project.build.directory}/eleven-cms-vrbt-lib</outputDirectory>-->
            <!--                            &lt;!&ndash; excludeTransitive:是否不包含间接依赖包，比如我们依赖A，但是A又依赖了B，我们是否也要把B打进去 默认不打&ndash;&gt;-->
            <!--                            <excludeTransitive>false</excludeTransitive>-->
            <!--                            &lt;!&ndash; 复制的jar文件去掉版本信息 true去掉 false 不去&ndash;&gt;-->
            <!--                            <stripVersion>false</stripVersion>-->
            <!--                            &lt;!&ndash; 排除前面打包进去的jar包&ndash;&gt;-->
            <!--                            <excludeArtifactIds>jeecg-boot-base-common,eleven-cms</excludeArtifactIds>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>