package com.github.wxpay.sdk;

import org.apache.http.client.HttpClient;

/**
 * 常量
 */
public class WXPayConstants {

    public enum SignType {
        MD5, HMACSHA256
    }

    public static final String DOMAIN_API = "api.mch.weixin.qq.com";
    public static final String DOMAIN_API2 = "api2.mch.weixin.qq.com";
    public static final String DOMAIN_APIHK = "apihk.mch.weixin.qq.com";
    public static final String DOMAIN_APIUS = "apius.mch.weixin.qq.com";


    public static final String FAIL     = "FAIL";
    public static final String SUCCESS  = "SUCCESS";
    public static final String HMACSHA256 = "HMAC-SHA256";
    public static final String MD5 = "MD5";

    public static final String FIELD_SIGN = "sign";
    public static final String FIELD_SIGN_TYPE = "sign_type";

    public static final String WXPAYSDK_VERSION = "WXPaySDK/3.0.9";
    public static final String USER_AGENT = WXPAYSDK_VERSION +
            " (" + System.getProperty("os.arch") + " " + System.getProperty("os.name") + " " + System.getProperty("os.version") +
            ") Java/" + System.getProperty("java.version") + " HttpClient/" + HttpClient.class.getPackage().getImplementationVersion();

    public static final String MICROPAY_URL_SUFFIX     = "/pay/micropay";
    public static final String UNIFIEDORDER_URL_SUFFIX = "/pay/unifiedorder";
    public static final String ORDERQUERY_URL_SUFFIX   = "/pay/orderquery";
    public static final String REVERSE_URL_SUFFIX      = "/secapi/pay/reverse";
    public static final String CLOSEORDER_URL_SUFFIX   = "/pay/closeorder";
    public static final String REFUND_URL_SUFFIX       = "/secapi/pay/refund";
    public static final String REFUNDQUERY_URL_SUFFIX  = "/pay/refundquery";
    public static final String DOWNLOADBILL_URL_SUFFIX = "/pay/downloadbill";
    public static final String REPORT_URL_SUFFIX       = "/payitil/report";
    public static final String SHORTURL_URL_SUFFIX     = "/tools/shorturl";
    public static final String AUTHCODETOOPENID_URL_SUFFIX = "/tools/authcodetoopenid";

    // sandbox
    public static final String SANDBOX_MICROPAY_URL_SUFFIX     = "/sandboxnew/pay/micropay";
    public static final String SANDBOX_UNIFIEDORDER_URL_SUFFIX = "/sandboxnew/pay/unifiedorder";
    public static final String SANDBOX_ORDERQUERY_URL_SUFFIX   = "/sandboxnew/pay/orderquery";
    public static final String SANDBOX_REVERSE_URL_SUFFIX      = "/sandboxnew/secapi/pay/reverse";
    public static final String SANDBOX_CLOSEORDER_URL_SUFFIX   = "/sandboxnew/pay/closeorder";
    public static final String SANDBOX_REFUND_URL_SUFFIX       = "/sandboxnew/secapi/pay/refund";
    public static final String SANDBOX_REFUNDQUERY_URL_SUFFIX  = "/sandboxnew/pay/refundquery";
    public static final String SANDBOX_DOWNLOADBILL_URL_SUFFIX = "/sandboxnew/pay/downloadbill";
    public static final String SANDBOX_REPORT_URL_SUFFIX       = "/sandboxnew/payitil/report";
    public static final String SANDBOX_SHORTURL_URL_SUFFIX     = "/sandboxnew/tools/shorturl";
    public static final String SANDBOX_AUTHCODETOOPENID_URL_SUFFIX = "/sandboxnew/tools/authcodetoopenid";

    //获取code
    public static final String WECHAT_CODE_URL= "https://open.weixin.qq.com/connect/oauth2/authorize";
    //获取access_token
    public static final String WECHAT_ACCESS_TOKEN_URL= "https://api.weixin.qq.com/sns/oauth2/access_token";
    //获取token
    public static final String WECHAT_TOKEN_URL= "https://api.weixin.qq.com/cgi-bin/token";
    //获取userInfo
    public static final String WECHAT_USER_INFO_URL= "https://api.weixin.qq.com/sns/userinfo";

    //刷新access_token
    public static final String WECHAT_REFRESH_TOKEN_URL= "https://api.weixin.qq.com/sns/oauth2/refresh_token";

    //检验授权凭证（access_token）是否有效
    public static final String WECHAT_AUTH_URL= "https://api.weixin.qq.com/sns/auth";

    //微信申请退款
    public static final String WECHAT_REFUND_URL= "https://api.mch.weixin.qq.com/pay/refundquery";

    //微信小程序获取openId
    public static final String WECHAT_APPLETS_OPEN_ID_URL="https://api.weixin.qq.com/sns/jscode2session";

    //获取小程序 scheme 码
    public static final String WECHAT_GENERATESCHEME_URL="https://api.weixin.qq.com/wxa/generatescheme";

    //微信退款
    public static final String WECHAT_REFUND       = "https://api.mch.weixin.qq.com/secapi/pay/refund";

    //查询投诉单详情
    public static final String WECHAT_QUERY_COMPLAIN_DETAIL_URL="https://api.mch.weixin.qq.com/v3/merchant-service/complaints-v2/";
    //微信投诉通知访问地址
    public static final String WECHAT_COMPLAIN_URL="https://api.mch.weixin.qq.com/v3/merchant-service/complaint-notifications";
    //微信处理投诉通知访问地址
    public static final String WECHAT_SOLVE_COMPLAIN_URL="https://api.mch.weixin.qq.com/v3/merchant-service/complaints-v2/{complaint_id}/response";
    //微信反馈处理完成
    public static final String WECHAT_FEEDBACK_OVER_COMPLAIN_URL="https://api.mch.weixin.qq.com/v3/merchant-service/complaints-v2/{complaint_id}/complete";
}

