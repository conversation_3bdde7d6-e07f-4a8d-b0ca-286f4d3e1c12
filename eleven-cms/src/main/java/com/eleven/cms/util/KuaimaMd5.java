package com.eleven.cms.util;

import com.eleven.cms.util.guizhou.AesEncryptUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Joiner;
import org.apache.commons.codec.digest.DigestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @author: cai lei
 * @create: 2024-02-04 15:52
 */
@Slf4j
public class KuaimaMd5 {
    /**
     * @param content 签名内容
     * @param key     密钥
     * @param sign    效验的签名
     * @return
     */
    public static boolean verificationSign(String content, String key, String sign) {
        if (sign == null || sign.isEmpty()) {
            return false;
        }

        return sign.equals(createSign(content, key));
    }

    /**
     * @param content 签名内容
     * @param key     密钥
     * @return 签名code
     */
    public static String createSign(String content, String key) {
        if (key == null || key.isEmpty()) {
            return null;
        }
        if (content == null || content.isEmpty()) {
            return null;
        }

        return DigestUtils.md5Hex(content + key);
    }

    /**
     * 校验签名是否正确
     *
     * @param params 签名内容
     * @param key    密钥
     * @param sign   效验的签名
     * @return
     */
    public static boolean verificationSign(Map<String, Object> params, String key, String sign) {
        if (sign == null || "" .equals(sign)) {
            return false;
        }
        String createSign = createSign(params, key);
        if (log.isInfoEnabled()) {
            log.info("原签名sign:{},我们的签名:{}", sign, createSign);
        }
        return sign.equals(createSign);
    }

    /**
     * @param params 签名params
     * @param key    密钥
     * @return 签名code
     */
    public static String createSign(Map<String, Object> params, String key) {
        if (key == null || key.isEmpty()) {
            return null;
        }
        if (params == null || params.keySet() == null || params.keySet().isEmpty()) {
            return null;
        }
        Object[] parKeys = params.keySet().toArray();
        Arrays.sort(parKeys);
        StringBuffer temp = new StringBuffer();
        boolean first = true;
        for (Object parKey : parKeys) {
            String parKeyStr = null;
            if (parKey != null) {
                parKeyStr = parKey.toString();
            }
            if ("sign" .equals(parKeyStr)) {
                continue;
            }
            if (first) {
                first = false;
            } else {
                temp.append("&");
            }
            Object value = params.get(parKey);
            if (value != null) {
                String v = value.toString();
                if (!"" .equals(v)) {
                    temp.append(parKey).append("=").append(value.toString());
                }
            }
        }
        temp.append("&").append("key").append("=").append(key);
        return DigestUtils.md5Hex(temp.toString());
    }

    /**
     * 对数据排序组装成排序后的字符串
     *
     * @param params
     * @return
     */
    public static String createSortStr(Map<String, Object> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        String prestr = "";
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            if ("sign" .equals(key)) {
                continue;
            }
            if ("" .equals(prestr)) {// 拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + "&" + key + "=" + value;
            }
        }
        return prestr;
    }

    public static String aesEncrypt(final String content, final String key) {
        try {
            byte[] raw = key.getBytes("UTF-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            //"算法/模式/补码方式"
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
            byte[] encrypted = cipher.doFinal(content .getBytes("UTF-8"));
            return DatatypeConverter.printHexBinary(encrypted);
        } catch (Exception e) {
            log.error("Exception:", e);
            return null;
        }
    }
}
