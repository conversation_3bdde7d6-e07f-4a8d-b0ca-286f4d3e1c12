package com.eleven.cms.util;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * @author: cai lei
 * @create: 2021-11-22 14:45
 */
public class UnzipUtil {

    public static List<String> unzip(File target) throws IOException {
        ZipFile zip = new ZipFile(target, Charset.forName("GBK"));
        List<String> replaceList = new ArrayList<>();
        for (Enumeration entries = zip.entries(); entries.hasMoreElements(); ) {
            ZipEntry entry = (ZipEntry) entries.nextElement();
            String fileName = entry.getName();
            InputStream inputStream = zip.getInputStream(entry);
            File file = new File(target.getParent() + File.separator + fileName);
            FileUtils.copyInputStreamToFile(inputStream, file);
            inputStream.close();
            replaceList.add(fileName);
        }
        return replaceList;
    }

    public static void main(String[] args) throws IOException {
        UnzipUtil.unzip(new File("D:\\test\\4412226_crbt.miguring.com_nginx.zip"));
    }
}

