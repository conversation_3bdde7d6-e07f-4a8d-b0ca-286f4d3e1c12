package com.eleven.cms.util.henan;

/**
 * <AUTHOR>
 * @create 2023/6/21 10:38
 */
public class HenanConstans {

    public static final String API_ID = "1126";
    public static final String APP_ID = "1126662";
    public static final String APP_KEY = "a72753ce5cd291117f6dfef9c584d1cd";
    public static final String HENAN_CACHE_KEY = "henan_fluent:";

    //public static final String TOKEN_API = "http://111.7.112.55:20200/aopoauth/oauth/token?app_id=" + APP_ID + "&app_key=" + APP_KEY + "&grant_type=client_credentials";
    public static final String TOKEN_API = "https://v.miguring.com/henan_proxy/aopoauth/oauth/token?app_id=" + APP_ID + "&app_key=" + APP_KEY + "&grant_type=client_credentials";

    //public static final String BASE_URL = "http://111.7.112.55:30110/oppf";
    public static final String BASE_URL = "https://v.miguring.com/henan_proxy/oppf";

    

}
