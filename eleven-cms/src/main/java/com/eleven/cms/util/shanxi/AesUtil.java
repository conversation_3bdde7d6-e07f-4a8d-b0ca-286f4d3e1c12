package com.eleven.cms.util.shanxi;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;

@Slf4j
public class AesUtil {


    public AesUtil() {
    }
    public static final String ENCRY_ALGORITHM  = "AES";
    public static final String CIPHER_MODE  ="AES/ECB/PKCS5Padding";
    private final static String charset = "UTF-8";

    /**
     * @param content AES加密前的明文
     * @param key     秘钥
     * @return AES加密后的内容
     */
    public static String encrypt(final String content, final String key) {
        try {
            byte[] raw = key.getBytes(charset);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, ENCRY_ALGORITHM);
            //"算法/模式/补码方式"
            Cipher cipher = Cipher.getInstance(CIPHER_MODE );
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
            byte[] encrypted = cipher.doFinal(content.getBytes(charset));
            //此处使用Hex做转码功能，同时能起到2次加密的作用。
            // 如果是PHP，使用如下方法
            //<?php
            //$str = "内容内容";
            //$hex = bin2hex($str); //加密
            //echo $hex . "<br>";
            //echo pack("H*",$hex) . "<br>"; //解密
            //?>
            return DatatypeConverter.printHexBinary(encrypted);
        } catch (Exception e) {
            log.error("Exception:", e);
            return null;
        }
    }



    /**
     * @param content AES加密后的内容
     * @param key     秘钥
     * @return AES解密后的明文
     */
    public static String decrypt(final String content, final String key) {
        try {
            byte[] raw = key.getBytes(charset);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, ENCRY_ALGORITHM);
            //"算法/模式/补码方式"
            Cipher cipher = Cipher.getInstance(CIPHER_MODE );
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            //先用Hex解密
            byte[] hexBinary = DatatypeConverter.parseHexBinary(content);
            byte[] original = cipher.doFinal(hexBinary);
            return new String(original, charset);
        } catch (Exception e) {
            log.error("Exception:", e);
            return null;
        }
    }

    public static void main(String[] a){
        String cotent = "E4694FD6D86925E90B0A3C0AA275A519738CD260B1B38326506B78B7DF74FDAB4A2304F34AB5EB8749432E227F505603";
        String key = "YDLG0YPJNHXAA06BYYXP7Z91COF5MYBK";
        System.out.println(AesUtil.decrypt(cotent,key));
    }

    /**
     * 加密
     *
     * @param data      对象转为json字符串
     * @param secretKey AES秘钥
     * @return 加密后的字符串
     */
    public static String aesEncrypt(String data, String secretKey) {
        try {
            // 加密算法/工作模式/填充方式
            Cipher cipher = Cipher.getInstance(CIPHER_MODE );
            byte[] dataBytes = data.getBytes(charset);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(Base64Utils.decodeFromString(secretKey), ENCRY_ALGORITHM));
            byte[] result = cipher.doFinal(dataBytes);
            return Base64Utils.encodeToString(result);
        } catch (Exception e) {
            log.error("执行CodecUtil.aesEncrypt失败：data={}，异常：{}", data, e);
        }
        return null;
    }

    /**
     * 解密
     *
     * @param encryptedDataBase64 加密后的字符串
     * @param securityKey         AES秘钥
     * @return 解密后的json字符串
     */
    public static String aesDecrypt(String encryptedDataBase64, String securityKey) {
        try {
            // 加密算法/工作模式/填充方式
            Cipher cipher = Cipher.getInstance(CIPHER_MODE );
            byte[] dataBytes = Base64Utils.decodeFromString(encryptedDataBase64);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(Base64Utils.decodeFromString(securityKey), ENCRY_ALGORITHM));
            byte[] result = cipher.doFinal(dataBytes);
            return new String(result,StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("执行aesDecrypt失败：data={}，异常：{}", encryptedDataBase64, e);
        }
        return null;
    }

}
