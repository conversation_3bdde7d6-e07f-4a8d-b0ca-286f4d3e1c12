package com.eleven.cms.util;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jcajce.provider.asymmetric.rsa.RSAUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * Author: <EMAIL>
 * Date: 2020/4/14 17:08
 * Desc:RSA加密工具类
 */
public class RSAUtils {
    private static final Logger logger = LoggerFactory.getLogger(RSAUtils.class);

    public static final String ALGORITHM ="RSA";
    public static final String SIGNATURE_ALGORITHM ="SHA1WithRSA";

    //生成签名
    public static String sign(byte[] json, String privateKey)
            throws InvalidKeySpecException, NoSuchAlgorithmException, SignatureException, InvalidKeyException {
        PKCS8EncodedKeySpec pkcs =new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));

        KeyFactory factory = KeyFactory.getInstance(ALGORITHM);
        PrivateKey key = factory.generatePrivate(pkcs);

        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(key);
        signature.update(json);

        return Base64.encodeBase64String(signature.sign());
    }

    //生成签名
    public static String sign(String data, String privateKey)
            throws InvalidKeySpecException, NoSuchAlgorithmException, SignatureException, InvalidKeyException {

        byte[] json = data.getBytes(StandardCharsets.UTF_8);

        return sign(json,privateKey);
    }

    //签名校验
    public static boolean verify(byte[] json, String publicKey, String sign){
        try{
            X509EncodedKeySpec keySpec =new X509EncodedKeySpec(Base64.decodeBase64(publicKey));

            KeyFactory factory = KeyFactory.getInstance(ALGORITHM);
            PublicKey key = factory.generatePublic(keySpec);

            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(key);
            signature.update(json);

            return signature.verify(Base64.decodeBase64(sign));
        }catch(Exception e){
        }

        return false;
    }

    //签名校验
    public static boolean verify(String data, String publicKey, String sign){
        byte[] json = data.getBytes(StandardCharsets.UTF_8);

        return verify(json,publicKey,sign);
    }

    //公钥加密
    public static byte[] encryptByPublicKey(byte[] data, String publicKey)
            throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));

        KeyFactory factory = KeyFactory.getInstance("RSA");
        PublicKey key = factory.generatePublic(keySpec);

        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.ENCRYPT_MODE, key);

        return cipher.doFinal(data);
    }

    //公钥加密
    public static String encryptByPublicKey(String data, String publicKey)
            throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {

        byte[] bytes = encryptByPublicKey(data.getBytes(StandardCharsets.UTF_8),publicKey);

        return Base64.encodeBase64String(bytes);
    }

    //私钥解密
    public static byte[] decryptByPrivateKey(byte[] data, String privateKey)
            throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException,
            IllegalBlockSizeException, BadPaddingException {
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));

        KeyFactory factory = KeyFactory.getInstance("RSA");
        PrivateKey key = factory.generatePrivate(keySpec);

        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.DECRYPT_MODE, key);

        return cipher.doFinal(data);
    }

    //私钥解密
    public static String decryptByPrivateKey(String data, String privateKey)
            throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException,
            IllegalBlockSizeException, BadPaddingException {

        byte[] bytes = decryptByPrivateKey(Base64.decodeBase64(data),privateKey);

        return new String(bytes,StandardCharsets.UTF_8);
    }

    /**
     *
     * @param is2048 false=生成1024长度的密钥对,true=生成2048长度的密钥对
     * @return
     * @throws Exception
     */
    public static KeyPair initKey(boolean is2048) throws Exception {

        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(ALGORITHM);

        keyPairGen.initialize(is2048?2048:1024);

        KeyPair keyPair = keyPairGen.generateKeyPair();
        ////公钥
        //final PublicKey publicKey = keyPair.getPublic();
        ////私钥
        //final PrivateKey privateKey = keyPair.getPrivate();

        return keyPair;

    }

    public static void main(String[] args) throws Exception {
//        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjcnvaOhD79JqqBWET3byigvbApaouZv0LxyiDC0Dw1vvUwgXlSxCqSFFF87Vu2uKT608dMe8h84FPeMD1oW+ZRYCQ9sS1Ho62TfAEKkFhmFN250CiGn+tBQeTGnDFBvo4/jpxDmeB6tXA8wOnJxUTYFt0KMDohck/i6blHIy4BtHD6d5I3hq8oGXYnO4RaGmwPHICb67dB19nqf0xyd39XKs1FdumGrn7IAcb7hNHxxhlivh7SYg5NKZ9Md4mHqy0svbMu2En1EGeKGkeoNYebo585lE2cEsNkOF20kUg5VnAbZJJ86hwik62myFuy+vdiLZIyIz18FOfWqmFPN5XQIDAQAB";
//        System.out.println("publicKey:");
//        System.out.println(publicKey);
//
//        String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCNye9o6EPv0mqoFYRPdvKKC9sClqi5m/QvHKIMLQPDW+9TCBeVLEKpIUUXztW7a4pPrTx0x7yHzgU94wPWhb5lFgJD2xLUejrZN8AQqQWGYU3bnQKIaf60FB5MacMUG+jj+OnEOZ4Hq1cDzA6cnFRNgW3QowOiFyT+LpuUcjLgG0cPp3kjeGrygZdic7hFoabA8cgJvrt0HX2ep/THJ3f1cqzUV26YaufsgBxvuE0fHGGWK+HtJiDk0pn0x3iYerLSy9sy7YSfUQZ4oaR6g1h5ujnzmUTZwSw2Q4XbSRSDlWcBtkknzqHCKTrabIW7L692ItkjIjPXwU59aqYU83ldAgMBAAECggEAfhYP7yN0noRoUjZoJV4qSnZGeOPPANddarO0qs0w5n4Psa+lgQMN+GbckVJeID9+jGIKO/MOhVnAsYr587+VU0EiHINpGEfloxp/kpErl5dZ7CyQfwx68lpdoyf90RkMngXQo611CW7lfzvr1nryQMYTCJCd/3tDa0G5GmX/NefOeTmWuLiaGlDpKn+SYhkWm2dHxP3G3Id2/LvpkSVofWm3xSLZax39+9QahWwqNQzI0REOGwTJIUdNWZPjVNTy8pE2VRix7DfALK4ADbGeWXbxsmCCPXmDDLaSrcups59ow+MwZR80WWE3Tfz0tYCDwsXHiQyLocKeykbhkKbJBQKBgQD0cvAK29AHyZ0NeluKDy/BrlU6HknyAZezCAl+H7iySH0gmwWWeI7R4KoX9rraO8OVfBhnoyyVSiYJNCKtunUei+dZDsJHHcIo8qDobFGEpijAlAgKaNgYRig7hq/kp+9Vht0bxFNGOtti1NrDHYnjqBZGZifyTf75HQt3YPVimwKBgQCUfSIMuw5VQd+tpL4vJTb9kYQc4vVY/UxwQJtRKHMgfGW6DZ5n6f2daCx9adqzNpndyrFECgyA32U4sF4sCMG8SZSJNk08KG2QFVNumdj9c3E8U7ppQzS1XOa+XGyzRITGSzOKuzfhOJbna+mywZwA81BoYWhix8urfZxeV/S3ZwKBgQCUZUYVwXVblOgzq2jwueFq+ZMhkgufz5mxBJsGpukvydO0PIe63fsNrA52N8EdBJTqJYiHOcax5m6KvcyjnILHBXVvWgdUttsMkqZeEou2NEyfA/6Rmy48JjL8V/plnBEt4Y5O8fL/unE+YStgsYjo3DyRhiiP7wtvXVN9qC2vKwKBgAPivmhc4MQkZCkUHZH4RFcgXreuzMLWZhCt8CKQj2qzNqpGvvVzLxrWZgumIH3+GMqwCjGAndElh22TV/OJCyDVQBjTHfRMDonttPA5AG+pfLYabs9gXdm9I6CppIKQcXUK9L9VpOG07GYD8eqmnGF9/IWuvt+O4OqM7/GcFaBTAoGAAKmTENjEwsTMYMqkxabvhM2aLLeWnEKaE5pdpaPI5dNUgR+YXpG7j2QOTVGkYrSTh1G02k5ONFJa3Okeq7UlRARD2hmUTKt+vSP3h3u6mi2zt8Dat24GaJ9rbhfzbZxw97XewGvtE7Jj6bK7+sv49TEnotgy7GIwEBat+qTbUd0=";
//        System.out.println("privateKey:");
//        System.out.println(privateKey);
//
//        //签名验证
//        String content = "abcdefg";
//        String sign =  sign(content,privateKey);
//        boolean checkSign = verify(content,publicKey,sign);
//        System.out.println("checkSign = " + checkSign);
//
//        //加密解密 2048长度的密钥,数据长度不能长于 2048/8 =256-11(pading) = 245
//        //加密解密 1024长度的密钥,数据长度不能长于 1024/8 =128-11(pading) = 117
//        String encrypt = encryptByPublicKey(content,publicKey);
//        System.out.println("encrypt = " + encrypt);
//        String decrypt = decryptByPrivateKey(encrypt,privateKey);
//        System.out.println("decrypt = " + decrypt);

//        final KeyPair keyPair = initKey(true);
//        String publicKey= new String(java.util.Base64.getEncoder().encode(keyPair.getPublic().getEncoded()));
//        String privateKey =new String(java.util.Base64.getEncoder().encode(keyPair.getPrivate().getEncoded()));
//        logger.info("publicKey:" + publicKey);
//        logger.info("privateKey:" + privateKey);
//
//        Map<String,String> map=new HashMap<>();
//        map.put("phone","15915401656");
//        map.put("couponId","01");
//        map.put("channelId","03");
//        map.put("callbackUrl","https://www.baidu.com/");
//        map.put("submitTime","2021-02-28 16:37:51");
//        String content = SignatureUtil.assembelSignaturingData(map);
//        logger.info("sign明文:" + content);
//        String privateKeySign =sign(content, privateKey);
//        logger.info("私钥sign加密:" + privateKeySign);
//
//        boolean judge=verify(content,publicKey,privateKeySign);
//        logger.info("签名校验:" + judge);
//
//        String encrypt = encryptByPublicKey(content,publicKey);
//        logger.info("公钥加密:" + encrypt);
//
//        String privateKeyData =decryptByPrivateKey(encrypt,privateKey);
//        logger.info("私钥解密:" + privateKeyData);
        //phone+encodeRule+couponId+submitTime(System.currentTimeMillis())+channelId+callbackUrl+couponName
        //String content = DigestUtils.md5DigestAsHex(("15915401656" + "BNsoh8HmekRX4lIm" + "01" + System.currentTimeMillis() + "03"+"https://www.baidu.com/"+"产品名称").getBytes(StandardCharsets.UTF_8));
        //logger.info("content:" + content);

        String raw = "alias123456app_id10000390app_key5bcbc7f0c0385770fbe79fcb2e3da164methodsQryRandPassNTK2pass_mode0phone_no13438828200prod_prcidACAZstatus1timestamp20210918110522version1.0";
        String rawWithSecret = "secret" + raw + "secret";
        String urlEncodeContent = URLEncoder.encode(rawWithSecret, "UTF-8");
        String md5Val = DigestUtils.md5DigestAsHex(urlEncodeContent.getBytes(StandardCharsets.UTF_8));
        System.out.println("md5Val = " + md5Val);
        String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJji9/vAr7wDas3ICNERlNZqBPJp01ctfzcx8lAeloR8o7hu9DEAfm51C6z8cn4m1Ynk0lIm7lgrNGASyADJwWxW9S4vu+DDSL/z0eYZLiSKTZT9a0ydFZyH70FnYDgjSVtKg0ZYBK0HedZWibFYUq8dedzzDqQ8ZhWYhzuMnPSXAgMBAAECgYARpnJWoCIppINaCnPqHwt9zXOQwOLjN59uMdf1o/JgjTsheb27QoFWIzEb4XEeW0Ff0Ajv7wqpJyO+y6xHWwN3XrFBsjHY6H25epAA/pgLOF08lXpcrsq1N5mV9P8vlg02T5jLVkazwreHKfMPm8Elon7ThbNd0Lyqc6Tljs24QQJBANPcJU/5Ca8OHK+NlZiNlBHSj3EBxkoX41duhSdmx9aZ0D3QqPQOi9IRBAhjCq8iL7YxVes3MZJxNsoV25EAHNECQQC4vWUPjWkIxW6tLLZTyzQV75ZF3enlp7aKNSliPrbWtTccKDIYcGkHAthYfz9d+zGGqskPUrGG4vcFr0DdQLTnAkAndhbhGYLrP1aqgFW/74cH9s9O/kSf4mkvDN/yYduRJl86VFZT9y2l+BNnOINx8Y0vg2r8f/BU1Y+d/opCLOIxAkAi0kg3Sz9OEPDQoYnod3fk3pkjqDWLPTaSDH01cH7EL7ooi4cNxjp1wNqIq8uE6nEmYhCt27dspmFLaAJA8g5nAkEAiCFv6dicI5W/t7sJO4PfNBhWTd0JcJQfa4uJlSn2kfnlkHwedBd4BQtrkdJWteWsX9+PhN1n2lDHVQCmqBe0cQ==";
        String sign = sign(md5Val.getBytes(StandardCharsets.UTF_8), privateKey);
        String reEncode = URLEncoder.encode(sign, "UTF-8");
        System.out.println("reEncode = " + reEncode);

    }
}
