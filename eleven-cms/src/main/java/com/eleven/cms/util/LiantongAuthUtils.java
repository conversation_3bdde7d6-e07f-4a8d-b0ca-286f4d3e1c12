package com.eleven.cms.util;

import com.eleven.cms.config.LiantongVrbtConfig;
import com.eleven.cms.config.LiantongVrbtProperties;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2020/11/24 11:29
 * Desc: 联通音乐签名相关工具类
 */
@Slf4j
public class LiantongAuthUtils {

    private static final String DELIMITER_COLON = ":";

    public static final String DELIMITER_EMPYT = "";


    /**
     * 联通沃音乐订购消息回调签名校验
     *
     * @param authorization 其中Authorization格式为：appKey:sign （客户端标识:签名）
     * @param timestamp
     * @param jsonData
     * @param ipAddr        联通出口ip ************** ************** **************
     * @return
     */
    public static boolean validateNotifySign(String authorization, String timestamp, String jsonData, String ipAddr) {

        final String[] appkeyAndSign = authorization.split(DELIMITER_COLON);
        if (appkeyAndSign.length != 2) {
            return false;
        }
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(BizConstant.BIZ_LT_CHANNEL_DEFAULT);
        String appKey = liantongVrbtConfig.getAppKey();
        String secret = liantongVrbtConfig.getSecret();
        if (!appKey.equals(appkeyAndSign[0])) {
            return false;
        }
        String signParams = "POST_DATA=" + jsonData + "&timestamp=" + timestamp + secret;
        String sign = DigestUtils.md5DigestAsHex(signParams.getBytes(StandardCharsets.UTF_8)).toUpperCase();

        return sign.equals(appkeyAndSign[1]);
    }

    /**
     * 联通沃音乐 第三方回调签名校验
     *
     * @param authorization 其中Authorization格式为：appKey:sign （客户端标识:签名）
     * @param timestamp
     * @param jsonData
     * @param ipAddr        联通出口ip ************** ************** **************
     * @return
     */
    public static boolean validateNotifyOtherSign(String authorization, String timestamp, String jsonData, String ipAddr, String company) {
        final String[] appkeyAndSign = authorization.split(DELIMITER_COLON);
        if (appkeyAndSign.length != 2) {
            return false;
        }
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        String appKey = liantongVrbtConfig.getAppKey();
        String secret = liantongVrbtConfig.getSecret();
        if (!appKey.equals(appkeyAndSign[0])) {
            return false;
        }
        String signParams = "POST_DATA=" + jsonData + "&timestamp=" + timestamp + secret;
        String sign = DigestUtils.md5DigestAsHex(signParams.getBytes(StandardCharsets.UTF_8)).toUpperCase();

        return sign.equals(appkeyAndSign[1]);
    }

    public static HttpUrl generateSign(Request request,LiantongVrbtConfig liantongVrbtConfig) {
        String appkey = liantongVrbtConfig.getAppKey();
        String secret = liantongVrbtConfig.getSecret();
        String timestamp = DateUtils.yyyymmddhhmmss.get().format(new Date());

        HttpUrl httpUrl = request.url().newBuilder()
                .addQueryParameter("appkey", appkey)
                .addQueryParameter("timestamp", timestamp).build();
        final String queryParams = httpUrl.queryParameterNames()
                .stream()
                .sorted()
                .map(name -> name + String.join(DELIMITER_EMPYT, httpUrl.queryParameterValues(name)))
                .collect(Collectors.joining());

        byte[] input = (queryParams + secret).getBytes(StandardCharsets.UTF_8);
        final String digest = DigestUtils.md5DigestAsHex(input).toUpperCase();

        HttpUrl newHttpUrl = httpUrl.newBuilder()
                .addQueryParameter("digest", digest)
                .build();

        return newHttpUrl;

    }


    private static LiantongVrbtConfig getLiantongVrbtConfigByCompany(String company) {
        LiantongVrbtProperties liantongVrbtProperties = SpringContextUtils.getBean(LiantongVrbtProperties.class);
        LiantongVrbtConfig liantongVrbtConfig = liantongVrbtProperties.getVrbtConfig(company);
        if (liantongVrbtConfig == null) {
            log.error("渠道号:{}未找到联通视频彩铃相关配置", company);
            throw new JeecgBootException("无效的联通渠道号");
        }
        return liantongVrbtConfig;
    }

}