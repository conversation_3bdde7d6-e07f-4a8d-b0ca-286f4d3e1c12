package com.eleven.cms.util;

import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import static java.time.temporal.ChronoField.*;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtil {

    public static final String FULL_TIME_PATTERN = "yyyyMMddHHmmss";

    public static final String FULL_TIME_PATTERN_WITH_MILL = "yyyyMMddHHmmssSSS";

    public static final String FULL_TIME_SPLIT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String FULL_TIME_HOURS_MINUTES = "yyyy-MM-dd HH:mm";

    public static final String YEAR_MONTH_PATTERN = "yyyyMM";

    public static final String CST_TIME_PATTERN = "EEE MMM dd HH:mm:ss zzz yyyy";

    public static final String DATE_START_TIME = " 00:00:00";
    public static final String DATE_END_TIME = " 23:59:59";

    public static final String YEAR_MONTH_DAY = "yyyy-MM-dd";

    public static String toFormatDate(String strDate) {
        try {
            Date date = new SimpleDateFormat("yyyyMMdd").parse(strDate);
            return new SimpleDateFormat(YEAR_MONTH_DAY).format(date)+DATE_START_TIME;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
    public static String formatFullTime(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(dateTimeFormatter);
    }

    public static String formatFullTime(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, FULL_TIME_PATTERN);
    }

    public static String formatSplitTime(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, FULL_TIME_SPLIT_PATTERN);
    }

    public static String formatForMiguGroupApi(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, FULL_TIME_PATTERN_WITH_MILL);
    }

    public static String formatYearMonth(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, YEAR_MONTH_PATTERN);
    }

    public static String formatYearMonthDay(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, YEAR_MONTH_DAY);
    }

    public static String getDateFormat(Date date, String dateFormatType) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormatType, Locale.CHINA);
        return simpleDateFormat.format(date);
    }

    public static String formatCSTTime(String date, String format) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(CST_TIME_PATTERN, Locale.US);
        Date usDate = simpleDateFormat.parse(date);
        return DateUtil.getDateFormat(usDate, format);
    }

    public static String formatJava8IsoTime(String date) throws ParseException {
        LocalDateTime localDateTime = LocalDateTime.parse(date, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        return formatSplitTime(localDateTime);
    }

    public static String formatInstant(Instant instant, String format) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return localDateTime.format(DateTimeFormatter.ofPattern(format));
    }

    public static LocalDateTime parseString(String dateTimeStr,String pattern){
        DateTimeFormatter df = DateTimeFormatter.ofPattern(StringUtils.isNotBlank(pattern)?pattern:FULL_TIME_SPLIT_PATTERN);
        return LocalDateTime.parse(dateTimeStr,df);
    }

    /**
     * 为sql日期添加开始时间
     * @param dateWithoutTime 如 2020-03-28
     * @return 如 2020-03-28 00:00:00
     */
    public static String handleSqlDateStartTime(String dateWithoutTime){
        return dateWithoutTime+DATE_START_TIME;
    }

    /**
     * 为sql日期添加结束时间
     * @param dateWithoutTime 如 2020-03-28
     * @return 如 2020-03-28 23:59:59
     */
    public static String handleSqlDateEndTime(String dateWithoutTime){
        return dateWithoutTime+DATE_END_TIME;
    }


    /**
     * 为日期设置最小时间
     *
     * @param date
     *         如 2020-03-28
     * @return 如 2020-03-28 00:00:00
     */
    public static LocalDateTime dayMinTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MIN);
    }

    /**
     * 为日期设置最大时间
     *
     * @param date
     *         如 2020-03-28
     * @return 如 2020-03-28 23:59:59
     */
    public static LocalDateTime dayMaxTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MAX);
    }

    /**
     * 获取当月第一天的最小时间
     * @return
     */
    public static LocalDateTime getFirstDayOfMonthWithMinTime() {
        LocalDate firstDayThisMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        return LocalDateTime.of(firstDayThisMonth, LocalTime.MIN);
    }

    /**
     * 获取和指定时间相同月份的第一天的最小时间
     * @return
     */
    public static LocalDateTime getFirstDayOfMonthWithMinTime(LocalDateTime targetTime) {
        LocalDate firstDayThisMonth = targetTime.toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
        return LocalDateTime.of(firstDayThisMonth, LocalTime.MIN);
    }

    /**
     * 获取当月最后一天的最大时间
     * @return
     */
    public static LocalDateTime getLastDayOfMonthWithMaxTime() {
        LocalDate lastDayThisMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        return LocalDateTime.of(lastDayThisMonth, LocalTime.MAX);
    }

    /**
     * 获取和指定时间相同月份的最后一天的最大时间
     * @return
     */
    public static LocalDateTime getLastDayOfMonthWithMaxTime(LocalDateTime targetTime) {
        LocalDate lastDayThisMonth = targetTime.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        return LocalDateTime.of(lastDayThisMonth, LocalTime.MAX);
    }

    public static boolean isInCurrentYearMonth(LocalDateTime createTime) {
        LocalDate now = LocalDate.now();
        return createTime.getYear() == now.getYear() && createTime.getMonthValue() == now.getMonthValue();
    }

    public static void main(String[] args) {
//        final LocalDateTime localDateTime = LocalDate.now()
//                                                     .with(TemporalAdjusters.firstDayOfMonth())
//                                                     .atTime(LocalTime.MIN);
//        System.out.println("getFirstDayOfMonthWithMinTime() = " + getFirstDayOfMonthWithMinTime());
//        System.out.println("getLastDayOfMonthWithMaxTime() = " + getLastDayOfMonthWithMaxTime());
//
//        System.out.println(LocalDateTime.parse("2020-04-02T01:44:52",DateTimeFormatter.ISO_LOCAL_DATE_TIME));
//
//        LocalDate now = LocalDate.now();
//        String currentYearMonth =  String.valueOf(now.getYear())+String.valueOf(now.getMonthValue());
//        System.out.println("currentYearMonth = " + currentYearMonth);
//        System.out.println(now.getMonth());
//
//        LocalDateTime lastMonthFirstDay = LocalDate.of(2020,1,1).withDayOfMonth(1).minusMonths(1).atTime(LocalTime.MIN);
//        System.out.println("currentYearMonth = " + formatYearMonth(lastMonthFirstDay));
//
//        DateTimeFormatter formatter = new DateTimeFormatterBuilder().parseCaseInsensitive()
//                                                            .append(DateTimeFormatter.ISO_LOCAL_DATE)
//                                                            .appendLiteral(' ')
//                                                            .appendValue(HOUR_OF_DAY, 2)
//                                                            .appendLiteral(':')
//                                                            .appendValue(MINUTE_OF_HOUR, 2)
//                                                            .optionalStart()
//                                                            .appendLiteral(':')
//                                                            .appendValue(SECOND_OF_MINUTE, 2)
//                                                            .optionalStart()
//                                                            .toFormatter();
//        String format = formatter.format(LocalDateTime.now());
//        System.out.println("format = " + format);
//
//        LocalDateTime localDate = LocalDate.of(2020, 5, 31).atTime(LocalTime.MAX).plusMonths(1);
//        System.out.println("localDate = " + localDate);


        System.out.println(isAm());

    }


    public static LocalDateTime maxMinuteTime(LocalDateTime localDateTime, Long minutes) {
        LocalDateTime then = localDateTime.plusMinutes(minutes);
        return then;
    }


    /**
     * date 转 localdateTime
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date){
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }



    /**
     * date 转 localdateTime
     * @param localDateTime
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime){
        if(localDateTime != null)
            return Date.from(localDateTime.atZone( ZoneId.systemDefault()).toInstant());
        return null;
    }

    public static Date stringToDate(String str) throws ParseException {
       SimpleDateFormat simpleDateFormat = new SimpleDateFormat(FULL_TIME_SPLIT_PATTERN);
       return simpleDateFormat.parse(str);
    }
    public static Date stringToDate(String str,String pattern) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.parse(str);
    }
    /**
     * 获取当前时间到当天24点相差秒数
     *
     * @return
     */
    public static long getSecondByNowDiffEndOfDay() {
        return  ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
    }


    /**
     * 获取当前时间是am还是pm
     *
     * @return
     */
    public static boolean isAm() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime noonClock = now.with(LocalTime.of(12, 0));
        return now.isBefore(noonClock);
    }


    /**
     * 获取当前时间是否处于上午7点到晚上7点直接
     *
     * @return
     */
    public static boolean isAmSevenClockAndPmSevenClock() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime amSevenClock = now.with(LocalTime.of(7, 0));
        LocalDateTime pmSevenClock = now.with(LocalTime.of(19, 0));
        return now.isAfter(amSevenClock) && now.isBefore(pmSevenClock);
    }

    /**
     * 获取当前时间是否处于14点后
     *
     * @return
     */
    public static boolean isAfterFourteenClock() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime fourteenClock = now.with(LocalTime.of(14, 0));
        return now.isAfter(fourteenClock);
    }

    /**
     * 获取当前日期是否为周末
     *
     * @return
     */
    public static boolean isWeek() {
        LocalDateTime now = LocalDateTime.now();
        DayOfWeek dayOfWeek = now.getDayOfWeek();
        if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
            return true;
        }
        return false;
    }



    /**
     * 为sql日期添加开始时间
     */
    public static LocalDateTime handleSqlDateStartTime(LocalDateTime localDateTime){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YEAR_MONTH_DAY);
        return LocalDateTime.parse(formatter.format(localDateTime)+DATE_START_TIME, DateTimeFormatter.ofPattern(FULL_TIME_SPLIT_PATTERN));
    }
    /**
     * 为sql日期添加结束时间
     */
    public static LocalDateTime handleSqlDateEndTime(LocalDateTime localDateTime){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YEAR_MONTH_DAY);
        return LocalDateTime.parse(formatter.format(localDateTime)+DATE_END_TIME, DateTimeFormatter.ofPattern(FULL_TIME_SPLIT_PATTERN));
    }
    /**
     * 支付下次扣款时间
     */
    public static Date nextDeductTime(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime (date);
        calendar.add(Calendar.MONTH, 1);
        if(Calendar.DAY_OF_MONTH >28){
            calendar.set(Calendar.DAY_OF_MONTH, 28);
        }
        return calendar.getTime();

    }

    /**
     * 是否今天为支付宝的扣款时间（前5天，并且当月）
     */
    public static boolean isNextDeductDay(Date date) throws ParseException {
        DateFormat ymdDf = new SimpleDateFormat(YEAR_MONTH_DAY);
        DateFormat fullDf = new SimpleDateFormat(FULL_TIME_SPLIT_PATTERN);
        //当前时间
        Date now = new Date();
        //扣款最小时间
        Date minTime = fullDf.parse(DateUtil.handleSqlDateEndTime(ymdDf.format(new Date(date.getTime() - 6000 * 60 * 60 * 24))));
        //扣款的最大时间
        Date maxTime = fullDf.parse(DateUtil.handleSqlDateStartTime(ymdDf.format(new Date(date.getTime() + 1000 * 60 * 60 * 24))));
        if(now.getTime() > minTime.getTime() //当前时间大于扣款的最小时间
                && now.getTime() < maxTime.getTime() //当前时间小于扣款的最大时间
                && (ymdDf.format(now).substring(4,7).equals(ymdDf.format(date).substring(4,7)))){//当前时间与预计扣款时间要在同月
            return true;
        }
        return false;
    }

    /**
     * 下月1号
     */
    public static Date firstDayOfNextMoth(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime (date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH,1);
        calendar.set(Calendar.HOUR_OF_DAY,RandomUtils.getRondom(9,17));
        calendar.set(Calendar.MINUTE,RandomUtils.getRondom(1,60));
        calendar.set(Calendar.SECOND,RandomUtils.getRondom(1,60));
        return calendar.getTime();
    }
    /**
     * 根据时间 和时间格式 校验是否正确
     * @param time 校验的日期
     * @param format 校验的格式
     * @return
     */
    public static boolean isLegalDate(String time,String format) {
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(time);
            return time.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * rfc3339标准格式转日期
     *
     * @param str   2015-05-20T13:29:35+08:00
     * @return
     */
    public static Date parseStrToDate(String str){
        if (StringUtils.isNotEmpty(str)) {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
            try {
                return df.parse(str);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
