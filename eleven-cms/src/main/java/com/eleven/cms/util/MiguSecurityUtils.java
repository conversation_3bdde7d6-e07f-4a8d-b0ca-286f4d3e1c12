package com.eleven.cms.util;

import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.UUID;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;

/**
 * <AUTHOR>
 * @datetime 2025/1/16 14:27
 */
@Slf4j
public class MiguSecurityUtils {

    private static final String TEST_APP_KEY = "zJrgHLF7bvwQHipj";
    private static final String APP_KEY = "zoZpVSNj5EZO0z33";
    private static final String TEST_URL = "http://***************:80/audit/api/report/v1.0";
    private static final String URL = "http://***************:31010/audit/api/report/v1.0";
    private static final String ACCOUNT = "timeTravelInOneImageService";

    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    /**
     * 数据送审
     */
    public static String pushDataCheck(Integer type, String resourceUrl) {
        ObjectNode auditDataNode = JacksonUtils.createObjectNode();
        auditDataNode.put("dataId", UUID.randomUUID().toString());
        auditDataNode.put("data", MiguSecurityAESUtils.aesEncrypt(resourceUrl, APP_KEY));
        switch (type) {
            case 0 :
                auditDataNode.put("dataType", "一图穿越-图片");
                break;
            case 1 :
                auditDataNode.put("dataType", "一图穿越-视频");
                break;
        }
//        objectNode.put("description", "");
//        objectNode.put("priority", "");

        ArrayNode AuditDataListNode = JacksonUtils.createArrayNode();
        AuditDataListNode.add(auditDataNode);

        HttpUrl url = HttpUrl.parse(URL);
        HashMap<Object, Object> params = new HashMap<Object, Object>() {
            {
                put("sources", AuditDataListNode);
//                put("manualAudit", "");
//                put("notifyName", "");
                put("account", ACCOUNT);
                put("notifyName", "timeTravelInOneImageServiceCallback");
            }
        };
        switch (type) {
            case 0 :
                params.put("type", "picture");
                params.put("preset", "3417");
                break;
            case 1 :
                params.put("type", "video");
                params.put("preset", "3419");
                break;
        }
        String json = JacksonUtils.toJson(params);
        RequestBody body = RequestBody.create(JSON, json);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String time = sdf.format(new Date());

        String signature = MiguSecurityMD5Utils.toMD5(ACCOUNT + APP_KEY + time);

        StringBuilder sb = new StringBuilder();
        sb.append("authv1").append("-")
                .append("timeTravelInOneImageService").append("-")
                .append(time).append("-").append(signature);
        Headers headers = new Headers.Builder()
                .add("Authorization", sb.toString())
                .build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, "咪咕安审", "数据送审");
    }


    public static void main(String[] args) {
//        pushDataCheck(0, "https://ims-media.oss-cn-beijing.aliyuncs.com/user-media/********/d7d3cf6c-7adc-438d-b451-25669afce7e0.png");
        pushDataCheck(1, "https://ims-media.oss-cn-beijing.aliyuncs.com/ai_zhmb/3264e2f6-152a-45dd-a477-303cd8d5081f.mp4");
    }
}
