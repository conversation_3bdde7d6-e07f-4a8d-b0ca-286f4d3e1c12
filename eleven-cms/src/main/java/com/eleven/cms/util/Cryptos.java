package com.eleven.cms.util;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Arrays;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class Cryptos {
    private static final String AES = "AES";
    //注意此算法已分配给抖音小程序使用,不要随便更改
    private static final String AES_CBC = "AES/CBC/PKCS7Padding";

    private static final String HMACSHA1 = "HmacSHA1";

    private static final int DEFAULT_HMACSHA1_KEYSIZE = 160;

    private static final int DEFAULT_AES_KEYSIZE = 128;

    private static final int DEFAULT_IVSIZE = 16;

    private static SecureRandom random = new SecureRandom();

    static {
        if(null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)){
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    public static byte[] hmacSha1(byte[] input, byte[] key) {
        try {
            SecretKey secretKey = new SecretKeySpec(key, HMACSHA1);
            Mac mac = Mac.getInstance(HMACSHA1);
            mac.init(secretKey);
            return mac.doFinal(input);
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean isMacValid(byte[] expected, byte[] input, byte[] key) {
        byte[] actual = hmacSha1(input, key);
        return Arrays.equals(expected, actual);
    }

    public static byte[] generateHmacSha1Key() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(HMACSHA1);
            keyGenerator.init(DEFAULT_HMACSHA1_KEYSIZE);
            SecretKey secretKey = keyGenerator.generateKey();
            return secretKey.getEncoded();
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }

    public static byte[] aesEncrypt(byte[] input, byte[] key) {
        return aes(input, key, 1);
    }

    public static byte[] aesEncrypt(byte[] input, byte[] key, byte[] iv) {
        return aes(input, key, iv, 1);
    }

    public static String aesDecrypt(byte[] input, byte[] key) {
        byte[] decryptResult = aes(input, key, 2);
        return new String(decryptResult);
    }

    public static String aesDecrypt(byte[] input, byte[] key, byte[] iv) {
        byte[] decryptResult = aes(input, key, iv, 2);
        return new String(decryptResult);
    }

    /**
     * AES/ECB/PKCS7Padding ECB比较简单,无需iv
     * @param input
     * @param key
     * @param mode
     * @return
     */
    private static byte[] aes(byte[] input, byte[] key, int mode) {
        try {
            //java.security.Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            SecretKey secretKey = new SecretKeySpec(key, AES);
            //Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            Cipher cipher = Cipher.getInstance("AES"); //这样加密出来的值和上面的使用bc库的AES/ECB/PKCS7Padding一致,表示java模式就是这种模式吧
            cipher.init(mode, secretKey);
            return cipher.doFinal(input);
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     *  AES/CBC/PKCS7Padding CBC需要带iv,不同的iv加密出来不一样
     * @param input
     * @param key
     * @param iv
     * @param mode
     * @return
     */
    private static byte[] aes(byte[] input, byte[] key, byte[] iv, int mode) {
        try {
            SecretKey secretKey = new SecretKeySpec(key, AES);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            //java.security.Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance(AES_CBC, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(mode, secretKey, ivSpec);
            return cipher.doFinal(input);
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }

    public static byte[] generateAesKey() {
        return generateAesKey(DEFAULT_AES_KEYSIZE);
    }

    public static byte[] generateAesKey(int keysize) {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES);
            keyGenerator.init(keysize);
            SecretKey secretKey = keyGenerator.generateKey();
            return secretKey.getEncoded();
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }

    public static byte[] generateIV() {
        byte[] bytes = new byte[DEFAULT_IVSIZE];
        random.nextBytes(bytes);
        return bytes;
    }

    public static void main(String[] args) throws DecoderException {
        //System.out.println("generateAesKey() = " + Base64.getEncoder().encodeToString(generateAesKey()));
        //System.out.println("generateAesKey() = " + Hex.encodeHexString(generateAesKey()));
        //byte[] bytes = aesEncrypt("hello word".getBytes(StandardCharsets.UTF_8), BizConstant.AES_KEY_FOR_DOUYIN_VRBT.getBytes(StandardCharsets.UTF_8));
        //System.out.println(Base64.getEncoder().encodeToString(bytes));
        //
        //String s = "GJ4TUfw2ma+zrfly4VFrWggnikVnQ0ETiKMBd8UnSvYTAT5Rv2c5wpFKmMxq2ckj4OqEKaRZDGSRRX9Vdmw/yVvx/WzO3fAhPS2ypvWh3+I=";
        //String decrypt = aesDecrypt(Base64.getDecoder().decode(s),
        //        BizConstant.AES_KEY_FOR_DOUYIN_VRBT.getBytes(StandardCharsets.UTF_8));
        //System.out.println("decrypt = " + decrypt);

        String encryptedData = "u793lB0p8jvAPgRWHoqdgF5hJuxS+EbuqHVgbHJecKWADZFYhc8+d7yqwvbqOO7DpTkIyhgJCD0xZoNMW7uMyQbAbuAHp7H5gSLnChCKnxM31qDfIU0abtqt+EE3oaknluv2adcDiQg0VpOmeocIPaieTFWgEtZKcrWq6N0oIRo2+gEtZdunzMh0j7vLOioOarnymy6H/xj0Bf0BN0k+WA==";
        String sessionKey = "xYPXkpoMdy7Z1znNa5CmDA==";
        String iv ="i3cvtccPSmkpyfI8tgBlNw==";

        String result = aesDecrypt(
                org.apache.commons.codec.binary.Base64.decodeBase64(encryptedData), org.apache.commons.codec.binary.Base64.decodeBase64(sessionKey),
                Base64.decodeBase64(iv));

        System.out.println("result = " + result);
    }
}

