package com.eleven.cms.util.sign;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @datetime 2024/9/30 11:09
 */
public class MCJSignUtils {

    /**
     * 生成HashMap中签名
     *
     * @param paramHashMap
     *            待生成签名的HttpServletRequest Parameter Map
     * @param key
     *            签名密钥
     * @return
     */
    public static String generateHttpParamSign(
            HashMap<String, String> paramHashMap, String key) {

        // HashMap按照Key构成字符串的assicc字节值从小到大排
        TreeMap<String, String> sortMap = sort(paramHashMap);

        // 按照顺序组装TreeMap的值
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortMap.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            if (name.equals("sign")) {
                continue;
            }
            sb.append(value);
            sb.append("|");
        }

        int len = sb.length();
        if (len >= 1) {
            sb.deleteCharAt(len - 1);
        }

        // 构造签名内容:AccessSecurityKey+"|"+Value
        String signContent = key + "|" + sb.toString();

        // 使用UTF-8编码转成二进制数组，然后使用32位MD5摘要算法生成签名摘要
        String sign = md5(signContent, Charset.forName("UTF-8"));

        // 返回签名
        return sign;
    }

    /**
     * HashMap按照Key构成字符串的assicc字节值从小到大排序
     *
     * @param paramMap
     * @return
     */
    private static TreeMap<String, String> sort(HashMap<String, String> paramMap) {

        TreeMap<String, String> treeMap = new TreeMap<String, String>(
                new Comparator<String>() {
                    /**
                     * int compare(Object str1, Object str2) 返回一个基本类型的整型
                     *
                     * @返回：负数表示str1 小于str2，
                     * @返回：0 表示str1和str2相等，
                     * @返回：正数表示str1大于str2。
                     */
                    @Override
                    public int compare(String str1, String str2) {
                        return str1.compareTo(str2);
                    }
                });
        treeMap.putAll(paramMap);
//        System.out.println(treeMap.toString());
        return treeMap;

    }

    /**
     * 字符串使用指定编码方式生成MD5摘要
     *
     * @param str
     *            需要生成摘要的字符串
     * @param charset
     *            编码方式
     * @return
     */
    public static String md5(String str, Charset charset) {
        String digest = null;
        try {
            digest = new String(str);
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bin = str.getBytes(charset);
            byte[] digestBin = md.digest(bin);
            digest = bin2HexString(digestBin);
        } catch (NoSuchAlgorithmException ex) {
            ex.printStackTrace();
        }
        return digest;
    }
    /**
     * 二进制数组转成十六进制字符串
     *
     * @param bin
     *            二进制数组
     * @return
     */
    private static String bin2HexString(byte[] bin) {
        StringBuffer sBuffer = new StringBuffer();
        for (int i = 0; i < bin.length; i++) {
            sBuffer.append(byte2HexString(bin[i]));
        }
        return sBuffer.toString();
    }

    /**
     * 十六进制字符串
     */
    private final static String[] hexString = { "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };

    /**
     * 单字节转成十六进制字符串
     *
     * @param b
     *            字节
     * @return
     */
    private static String byte2HexString(byte b) {
        int i = b;
        if (i < 0) {
            i += 256;
        }
        int iD1 = i / 16;
        int iD2 = i % 16;
        return hexString[iD1] + hexString[iD2];
    }
}
