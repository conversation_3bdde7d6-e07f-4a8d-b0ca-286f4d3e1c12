package com.eleven.cms.util;

import com.eleven.cms.config.DianxinVrbtConfig;
import com.eleven.cms.config.DianxinVrbtProperties;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.SpringContextUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2020/11/24 11:29
 * Desc: 电信爱音乐签名相关工具类
 */
@Slf4j
public class DianxinAuthUtils {
    private static final String HMAC_SHA1 = "HmacSHA1";
    private static final String HMAC_SHA256 = "HmacSHA256";
    public static final String HTTP_GET_METHOD = "GET";
    public static final String HTTP_POST_METHOD = "POST";
    public static final String DELIMITER_AMP = "&";

    public static String generateMacSignature( String secret,String data) {
        byte[] byteHMAC = null;
        try {
            Mac mac = Mac.getInstance(HMAC_SHA1);
            SecretKey secretKey = new SecretKeySpec(secret.getBytes(),HMAC_SHA1);
            mac.init(secretKey);
            byteHMAC = mac.doFinal(data.getBytes());
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return  Base64.getEncoder().encodeToString(byteHMAC);
    }



    public static String generateHmacSHA256Signature( String secret,String data) {
        byte[] byteHMAC = null;
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKey secretKey = new SecretKeySpec(secret.getBytes(),HMAC_SHA256);
            mac.init(secretKey);
            byteHMAC = mac.doFinal(data.getBytes());
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return  Base64.getEncoder().encodeToString(byteHMAC);
    }



    public static String getMD5Str(String str) {
        MessageDigest messageDigest = null;
        try {
            messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(str.getBytes("UTF-8"));
        } catch (NoSuchAlgorithmException e) {
            System.out.println("NoSuchAlgorithmException caught!");
            System.exit(-1);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        byte[] byteArray = messageDigest.digest();
        StringBuilder md5StrBuff = new StringBuilder();
        for (int i = 0; i < byteArray.length; i++) {
            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1) {
                md5StrBuff.append("0").append(Integer.toHexString(0xFF & byteArray[i]));
            } else {
                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
            }
        }
        return md5StrBuff.toString();
    }

    /**
     * 电信ISMP订购退订消息回调签名校验
     * @param deviceId
     * @param timestamp
     * @param signature
     * @return
     */
    public static boolean validateNotifySign(String deviceId, String timestamp, String signature,String company) {
        DianxinVrbtProperties dianxinVrbtProperties = SpringContextUtils.getBean(DianxinVrbtProperties.class);
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String myDeviceId = dianxinVrbtConfig.getDeviceId();
        String keyword = dianxinVrbtConfig.getDevicePwd();
        if(!myDeviceId.equals(deviceId)){
            return false;
        }
        String md5Str = getMD5Str(keyword + timestamp);
        return md5Str.equals(signature);
    }



    public static Request generateSign(Request request, DianxinVrbtConfig dianxinVrbtConfig){

        String deviceId = dianxinVrbtConfig.getDeviceId();
        String channelId = dianxinVrbtConfig.getChannelId();
        String timestamp = DateUtils.yyyymmddhhmmss.get().format(new Date());
        List<String> signParamList = Lists.newArrayList(deviceId, channelId, timestamp);

        if (HTTP_GET_METHOD.equals(request.method())) {
            HttpUrl httpUrl = request.url();
            for (int i = 0; i < httpUrl.querySize(); i++) {
                signParamList.add(httpUrl.queryParameterValue(i));
            }
        } else {
            RequestBody requestBody = request.body();
            if (requestBody instanceof FormBody) {
                FormBody formBody = (FormBody) requestBody;
                for (int i = 0; i < formBody.size(); i++) {
                    signParamList.add(formBody.value(i));
                }
            }
        }

        String signParam = String.join(DELIMITER_AMP, signParamList);
        Request.Builder requestBuilder = request.newBuilder();
        String signature = "";
        //if (BizConstant.BIZ_DIANXIN_CHANNEL_HSTJ.equals(dianxinVrbtConfig.getCompany())) {
        signature = generateHmacSHA256Signature(dianxinVrbtConfig.getDevicePwd(), signParam);
        requestBuilder.header("auth-signature-method", HMAC_SHA256);
        //} else {
        //    signature = generateMacSignature(dianxinVrbtConfig.getDevicePwd(), signParam);
        //    requestBuilder.header("auth-signature-method", HMAC_SHA1);
        //}

        requestBuilder.header("auth-deviceid", deviceId);
        requestBuilder.header("auth-channelid", channelId);
        requestBuilder.header("auth-timestamp", timestamp);
        requestBuilder.header("auth-signature", signature);
        return requestBuilder.build();

    }
    public static Request newGenerateSign(Request request, DianxinVrbtConfig dianxinVrbtConfig){

        String deviceId = dianxinVrbtConfig.getDeviceId();
        String channelId = dianxinVrbtConfig.getChannelId();
        String timestamp = DateUtils.yyyymmddhhmmss.get().format(new Date());
        List<String> signParamList = Lists.newArrayList(deviceId, channelId, timestamp);

        if (HTTP_GET_METHOD.equals(request.method())) {
            HttpUrl httpUrl = request.url();
            for (int i = 0; i < httpUrl.querySize(); i++) {
                if(!"ring_id".equals(httpUrl.queryParameterName(i))){
                    signParamList.add(httpUrl.queryParameterValue(i));
                }

            }
        } else {
            RequestBody requestBody = request.body();
            if (requestBody instanceof FormBody) {
                FormBody formBody = (FormBody) requestBody;
                for (int i = 0; i < formBody.size(); i++) {
                    if(!"ring_id".equals(formBody.name(i))){
                        signParamList.add(formBody.value(i));
                    }

                }
            }
        }
        String signParam = String.join(DELIMITER_AMP, signParamList);
        Request.Builder requestBuilder = request.newBuilder();
        String signature = "";
        //if (BizConstant.BIZ_DIANXIN_CHANNEL_HSTJ.equals(dianxinVrbtConfig.getCompany())) {
        signature = generateHmacSHA256Signature(dianxinVrbtConfig.getDevicePwd(), signParam);
        requestBuilder.header("auth-signature-method", HMAC_SHA256);
        //} else {
        //    signature = generateMacSignature(dianxinVrbtConfig.getDevicePwd(), signParam);
        //    requestBuilder.header("auth-signature-method", HMAC_SHA1);
        //}

        requestBuilder.header("auth-deviceid", deviceId);
        requestBuilder.header("auth-channelid", channelId);
        requestBuilder.header("auth-timestamp", timestamp);
        requestBuilder.header("auth-signature", signature);
        return requestBuilder.build();

    }



    public static void main(String[] args) {
        //pT5pDOo7joSpbYvd3bAg3RcO1TQ=
       // System.out.println(generateMacSignature("slie234$ere","10000000000000&1234&20160214162300&18910001234&135000000000000003147"));
       //byte[] byteHMAC = DigestUtil.hmac(HmacAlgorithm.HmacSHA1,"slie234$ere".getBytes()).digest("10000000000000&1234&20160214162300&18910001234&135000000000000003147");
       // String result = Base64.getEncoder().encodeToString(byteHMAC);
       // System.out.println(result);
        System.out.println(getMD5Str("IyoadK78R0QP"+"20201126145743"));

    }



}
