package com.eleven.cms.util.junbo;

import com.eleven.cms.dto.JunboResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;

import java.util.Map;

/**
 * 接收方
 * <AUTHOR>
 *
 */
public class Receiver {

	/**
	 * 接收私钥加密方式，用公钥解密
	 * @return	处理后AES加密返回
	 * @throws Exception
	 */
	public String receiveByPrive(String publicKey, String senderTxt) throws Exception{
		String k = senderTxt.substring(senderTxt.indexOf("K=")+2);
		k = k.substring(0, k.indexOf("&C="));
		String c = senderTxt.substring(senderTxt.indexOf("C=")+2);
		//
		System.out.println("服务器接收数据:");
		String serverK = java.net.URLDecoder.decode(k);
		System.out.println("serverK:"+serverK);

		String serverC = java.net.URLDecoder.decode(c);
		System.out.println("serverC:"+serverC);

		byte[] aesKey = CryptoUtility.decryptByPublicKey(CryptoUtility.loadPublicKey(publicKey), CryptoUtility.baseDecode(serverK));

		// AES 向量
		byte[] iv = CryptoUtility.aesIvParameter(aesKey);

		System.out.println("服务器解密获得的请求参数:");
		String strClientPara = new String(CryptoUtility.aesdecrypt(CryptoUtility.baseDecode(serverC), aesKey, iv));
		System.out.println(strClientPara);

		//RC字段在贵方发起请求时需要，订购接口返回的报文中就没有RC字段
		String serverReturn = "{'T':'20140801123001','F':'2015020112345','S':'100','C':{'ResultCode':'0100'}}";

		System.out.println("服务器返回信息的原文:");
		System.out.println(serverReturn);

		System.out.println("服务器进行AES加密:");
		byte[] aesReturn = CryptoUtility.aseEncrypt(serverReturn, aesKey, iv);

		String resultReturn = CryptoUtility.base64(aesReturn);

		System.out.println(resultReturn);
		return resultReturn;
	}

	/**
	 * 接收公钥加密方式，用私钥解密
	 * @return	处理后AES加密返回
	 * @throws Exception
	 */
	public String receiveByPublic(String privateKey, String senderTxt) throws Exception{

        final Map<String, String> queryMap = Splitter.on('&').trimResults().withKeyValueSeparator('=').split(senderTxt);
        //System.out.println("queryMap = " + queryMap);

        //String k = senderTxt.substring(senderTxt.indexOf("K=")+2);
		//k = k.substring(0, k.indexOf("&C="));
		//String c = senderTxt.substring(senderTxt.indexOf("C=")+2);
		//System.out.println("服务器接收数据:");

		String serverK = java.net.URLDecoder.decode(queryMap.get("K"),"utf-8");
		String serverC = java.net.URLDecoder.decode(queryMap.get("C"),"utf-8");
		byte[] aesKey = CryptoUtility.decryptByPriveKey(CryptoUtility.loadPrivateKey(privateKey), CryptoUtility.baseDecode(serverK));

		// AES 向量
		byte[] iv = CryptoUtility.aesIvParameter(aesKey);

		String strClientPara = new String(CryptoUtility.aesdecrypt(CryptoUtility.baseDecode(serverC), aesKey, iv));

		return strClientPara;
	}

    public static void main(String[] args) throws Exception {
        String rsaPrivateKeyPkcs8 = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDSdRw6Wr42SSstAccb+DwtL2FgvW2sj1TTtbKP6sBhoATyCmKxDPFITXq8IvPxvD3vv9nY5FwlgfDiTsDXPrFL188aaBKB4aZDUYnSjzfvYq2PPjAuobhda0r9gbquKDgcowtXNtaukhwDtzJR8H+thybQ2so71rS+OFOdi2fxvBe/a/LgxkUNLp0EgCheyzOu/2mDV1WM2lZ7HlV2dPrJVK9Jo4YMc6uPfNEPlwIvW6ikSChVLimt7H4LFcsTkb72DMvYRnSgNc01Av/1/CQraBRrfC0fB9jLW4kW/zVtaTNLvQ4esQ/xT3XeeiAvYVniCm3pjqxDfFFB/OUALj0BAgMBAAECggEBAM6Y9jf2wokp9L9+4kX7vp8gxvgfnN1r6vrVgE+1RFvRqWktdsqViPrQTG8J5O1mPGrxq9o1+ps7XwvaAYaLXaK1XPJwPdLQf9XP4nsX6vINrZFTnBr62LCkVf5cbrTueH7sM4LPK5o9hlDDcxtq+vuPFDSswyBQ2idtxe9/XW+3zNpNLmBUJk1W36dm2FII2sez/3iSDkeL1bUTrUAL2bepJNmAIIUSAl12yVUBYMYpj1s/WULxntysLNL7tveokh3xcFcgE71c6isyvpVgly/W4uByJFahjK3yfI/tWQgy3qytzEz1a66JMf02AIMqx2e9Dw8HoH8/of0x8yJ25G0CgYEA6wx7nbhORwUX30yQP8TL0M3nujjxH9fhdDJasB2ZpV0XeRS6iT09+7M1o3KFvZfzFTLhHINzw7v2lgo5TM3pUGqmzoBcBJpbrVVeE03tgtBhE5+mzn8ohnx4kUH6oLxl5Zf7h45XdMsk0/zKkYPLxZXhagCG9K+WtB2rYLituFMCgYEA5Td8IE8FVHkh5hH3x89HK9ZPUiDEXpk1hYGX3qX08MqgJ+IRXTZGryqMOLprQf2q+3aYtUuD3lFiiystekBsVdTyeyme1HOQK2ON2qRsaL8FCNhCpoVlOCrARpD6eExCu2XVfT3b4rPlq9T13cufmLhkT0mQ67qvdNRegOAeetsCgYEAvKLEAowLpkucIQVdLxBNUkItmvJf8WJb+dj/lx/qUyAm2nGcUs/nkrz8azRZyRLNb1Hp/+wvWALdnwSNf+oxOOye+lNhhgArWyyL14pO9xEtF4alZEwAxg3W5RzCe4U3cc9LejruTTlLUSYrnCTdwLDMuzm5FV5l4B9/jpwoLykCgYB9Gkhj9d0J2scWo+3hnw7QLTrDr2Cd5O7iO/XgriE+nsjEm80wW6Q+jHFVsOFDdiiw8GyuXlXNrAulu14p4CmAuyXQ9MlcWOpx0/cjQyRsEv2zcx0Fd77j5ellWlFqAO1XDGB156IwXFFY9HVwpWco2lLdN2CiWBTVJeeiXTAliwKBgHp72BtRJOIz5EkB9byTAMil0JG/rHJCRSBtky5Q5sZZdrfPdN/FB/CuOVHForh3jYYhl6nTI76bqf8AWWE3iBJLjJuKm8X1vnWgkAfOVlX9TtSTjJ4ibEYAgIilTMvX+bolK3fbhGH530xBxiRK2kXp3x7xiBZ+jLBYN146cWNI";
        String requestBody = "V=1.0.423.2021011210050872400041&K=WLT5kwWxKznlVF2iZbniQIvpI43g2lb9GUer63lPEcIDYdglSdZX1SaQNNdybiimFi6YYtEXxy15RVFjTLaWPEzSKlnmXpXX4nnpcoT597%2BxEE8XUuK1EEoHMQ5s7HoirYCPbnkP4IRVU%2BcrBNctAC0P%2BVwdaaJlYDLJuge8eRMDKeaZ6W%2FAXGxhAB4g%2Flou5o4jjeeyPhEG5MJr8vsIRyg2CIIJkQBA75czImRO%2BYUn%2BG2Lg3I19HcKR%2F7muQAWoewlXURTo6fuNb%2FlCx9iewZgGK9Nnh9wxrj%2B02JfEyaEf1JQhqufa3AQFUUN%2FDyVP0SQ9jbKEKhLTxfbPJ3IEQ%3D%3D&C=d5mLs%2BH6tfi55AB6PS7ke5zO%2F%2B14kJOm8HNmEu7tAEzQhUvdEEP%2BpNqcjTON35hs9dM0wBIQ85wfwB%2Fn4WjGfNUPySQLGrmjhnKSFsM9A6FW4zyAD8eAV0cjk9SgJvI51yln1y6dhD%2B%2BrFxUhbGcQU4%2FDs5yOEOMpFGuRj0jZTQ%3D";
        final String s = new Receiver().receiveByPublic(rsaPrivateKeyPkcs8, requestBody);
        //{"code":"1111","msg":"失败","orderId":"20210113172001571300027","orderSEQ":"9d840bde26fa4e1288d637ea45c03482","info":null}
        System.out.println("s = " + s);
        final JunboResult junboResult = new ObjectMapper().readValue(s, JunboResult.class);
        System.out.println("junboResult = " + junboResult);
    }
}

