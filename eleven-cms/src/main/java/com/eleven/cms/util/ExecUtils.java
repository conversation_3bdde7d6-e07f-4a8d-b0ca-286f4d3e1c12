package com.eleven.cms.util;

import com.google.common.base.Charsets;
import org.apache.commons.exec.CommandLine;
import org.apache.commons.exec.DefaultExecutor;
import org.apache.commons.exec.PumpStreamHandler;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.time.LocalDate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * @author: cai lei
 * @create: 2021-11-22 16:30
 */
public class ExecUtils {

    public static final Pattern smsCodePattern = Pattern.compile(".*(?<dt>20\\d{2}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}).*INFO.*\"smsCode\":\"(?<code>\\d{4,6})\".*");
    public static final Pattern smsCodePatternOutside = Pattern.compile(".*(?<dt>20\\d{2}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}).*INFO.*code:(?<code>\\d{4,6}).*");
    /**
     * 使用commonse-exec执行linux命令(不支持通配符和管道符)
     *
     * @param command 全路径的本地命令
     * @param args 命令参数
     * @return 标准输出和标准错误的输出
     */
    public static String execCmd(String command, String[] args) throws IOException {
        CommandLine cmdLine = new CommandLine(command);
        for (String arg : args) {
            cmdLine.addArgument(arg);
        }
        DefaultExecutor executor = new DefaultExecutor();
        executor.setExitValues(null);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
        PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);
        executor.setStreamHandler(streamHandler);
        executor.execute(cmdLine);
        String out = outputStream.toString(Charsets.UTF_8.name());//获取程序外部程序执行结果
        String error = errorStream.toString(Charsets.UTF_8.name());
        return out + error;
    }

    /**
     * 使用Runtime执行linux命令(支持通配符和管道符)
     *
     * @param command linux的命令和参数
     * @return 字符串数组
     */
    public static String execCmd(String command) throws IOException {
        String[] cmd = new String[] { "/bin/sh", "-c", command };
        Process ps = Runtime.getRuntime().exec(cmd);
        //标准输出
        BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
        StringBuilder sb = new StringBuilder();
        String line;
        while ((line = br.readLine()) != null) {
            sb.append(line).append("\n");
        }
        //标准错误
        br = new BufferedReader(new InputStreamReader(ps.getErrorStream()));
        while ((line = br.readLine()) != null) {
            sb.append(line).append("\n");
        }
        return sb.toString();
    }

    public static String grepVrbtLog(String mobile, String date) throws IOException {
        //final LocalDate localDate = LocalDate.parse(date);
        //final boolean beforeZipedFiles = localDate.isBefore(LocalDate.of(2023, 2, 1));
        //String grepCommand = beforeZipedFiles ? "zgrep" : "grep";
        
        final File targetDateFirstLogFile = new File("/usr/local/log/vrbt-" + date + ".0.log");
        String grepCommand = targetDateFirstLogFile.exists() ? "grep" : "zgrep";

        //String command = grepCommand + " " + mobile + " " +"/usr/local/log/vrbt-"+ date +".* |grep -v 归属地|grep -v 黑名单|grep -v 同步数据|grep -v 开通服务器|grep -v 联通视频彩铃api|grep -v 上报| grep -v 联通随机订购 | egrep -v '\"smsCode\":\"[[:digit:]]' | sed 's/鸿盛-//;s/瑞金-//;s/乐擎-//;s/睿梅-//'";
//        String command = grepCommand + " " + mobile + " " +"/usr/local/log/vrbt-"+ date +".* | egrep -v '归属地|黑名单|同步数据|开通服务器|上报|联通视频彩铃api|联通随机订购' | egrep -v '\"smsCode\":\"[[:digit:]]' | sed 's/破解//;s/鸿盛-//;s/瑞金-//;s/乐擎-//;s/睿梅-//'";
        String command = grepCommand + " " + mobile + " " +"/usr/local/log/vrbt-"+ date +".* | egrep -v '归属地|黑名单|同步数据|开通服务器|上报|联通视频彩铃api|联通随机订购' | sed 's/破解//;s/鸿盛-//;s/瑞金-//;s/乐擎-//;s/睿梅-//'";
        final String grepResult = execCmd(command);
        if(StringUtils.isBlank(grepResult)){
            return "";
        }
        final String[] lines = grepResult.split("\\r?\\n");
        StringBuilder sb = new StringBuilder();
        for (String line : lines) {
            final String s = line.replaceFirst("^/usr/local/log/vrbt.*?:", "");
            sb.append(s).append("\n");
            if(s.contains("彩铃一键设置") && s.contains("响应")){
                break;
            }
        }
        return sb.toString();
    }
    public static String grepSmsCodeLog(String id, String date, boolean isOutsideChannel) throws IOException {
        //final LocalDate localDate = LocalDate.parse(date);
        //final boolean beforeZipedFiles = localDate.isBefore(LocalDate.of(2023, 2, 1));
        //String grepCommand = beforeZipedFiles ? "zgrep" : "grep";

        final File targetDateFirstLogFile = new File("/usr/local/log/vrbt-" + date + ".0.log");
        String grepCommand = targetDateFirstLogFile.exists() ? "grep" : "zgrep";

        //String command = grepCommand + " " + mobile + " " +"/usr/local/log/vrbt-"+ date +".* |grep -v 归属地|grep -v 黑名单|grep -v 同步数据|grep -v 开通服务器|grep -v 联通视频彩铃api|grep -v 上报| grep -v 联通随机订购 | egrep -v '\"smsCode\":\"[[:digit:]]' | sed 's/鸿盛-//;s/瑞金-//;s/乐擎-//;s/睿梅-//'";
        String command = isOutsideChannel ? grepCommand + " " + "osOrderId:" + id + " " +"/usr/local/log/vrbt-"+ date +".*" : grepCommand + " " + "'\"transactionId\":\"" + id + "\"' " +"/usr/local/log/vrbt-"+ date +".*";
        Pattern codePattern = isOutsideChannel ? smsCodePatternOutside :smsCodePattern;

        final String grepResult = execCmd(command);

        if(StringUtils.isBlank(grepResult)){
            return "";
        }
        final String[] lines = grepResult.split("\\r?\\n");
        StringBuilder sb = new StringBuilder();
        for (String line : lines) {
            final Matcher matcher = smsCodePattern.matcher(line);
            if(matcher.find()){
//                System.out.println(matcher.group("dt").trim());
//                System.out.println(matcher.group("code"));
                sb.append("{transactionId:")
                        .append(id).append("} ")
                        .append("{createTime:")
                        .append(matcher.group("dt").trim())
                        .append("} ")
                        .append("{验证码:")
                        .append(matcher.group("code"))
                        .append("}")
                        .append("\r\n");
            }else{
                sb.append("未找到短信验证码！");
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) throws IOException {
        //final Process process = Runtime.getRuntime().exec(
        //        "/usr/bin/bash -c \"zgrep 13066822799 /usr/local/log/vrbt-2021-12-08.*\"");
        //
        //BufferedReader input = new BufferedReader(new InputStreamReader(process.getInputStream()));
        //String line = "";
        //while ((line = input.readLine()) != null) {
        //    System.out.println(line);
        //}
        //input.close();


        //String[] cmd = new String[] { "/bin/sh", "-c", "ls -l tomcat*" };
        //String[] cmd = new String[] { "/bin/sh", "-c", "zgrep 13066822799 /usr/local/log/vrbt-2021-12-08.*" };
        //Process ps = Runtime.getRuntime().exec(cmd);
        //
        //BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
        //StringBuffer sb = new StringBuffer();
        //String line;
        //while ((line = br.readLine()) != null) {
        //    sb.append(line).append("\n");
        //}
        //String result = sb.toString();
        //
        //System.out.println("result = " + result);

        //String cmd = "D:\\workspaceV6R3\\Java\\jdk1.8.0_191\\bin\\java.exe";

        //System.out.println(execCmd(cmd, new String[]{"-version"}));

        //final String result = execCmd("zgrep 13066822799 /usr/local/log/vrbt-2021-12-08.*|grep -v 归属地|grep -v 黑名单|grep -v 同步数据|grep -v 开通服务器|grep -v 上报| grep -v 联通随机订购");
        //System.out.println("result = " + result);

        //final String grepResult = grepVrbtLog(args[0], args[1]);
        //System.out.println(grepResult);
        final Pattern pattern = Pattern.compile(".*(?<dt>20\\d{2}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}).*INFO.*\"smsCode\":\"(?<code>\\d{4,6})\".*");
        ///usr/local/log/vrbt-2024-01-17.0.log:2024-01-17 15:27:24.195 [http-nio-9527-exec-1]
        String s = "/usr/local/log/vrbt-2024-01-17.0.log:2024-01-17 15:27:24.195 [http-nio-9527-exec-1] INFO  http-nio-9527-exec-1 c.e.c.c.ApiCommonController 公共接口提交短信验证码,参数:{\"transactionId\":\"1747520722412576769\",\"linkId\":\"32\",\"mobile\":\"18231316296\",\"subChannel\":\"A13xJL38\",\"deviceInfo\":\"\",\"smsCode\":\"580374\",\"source\":\"https://crbt.cdyrjygs.com/stationconstructions/promotion/32/?phone=18231316296&subChannel=A13xJL38&projectid=7324095520031965221&promotionid=7324095439777939468&creativetype=16&clickid=EKuAzt_qyJYDGP6HoYvS9asDIOzO0OHojUYwDDj7jeenA0IpYzIzZjZmMTItNjExMy00MDM1LTk3MjEtYjY1Y2VjOTQwZTFidTQ0OTFIgNKTrQOQAQA&ad_id=1788109406513155&_toutiao_params={%22cid%22:1788109439336491&%22device_id%22:308338054408044&%22log_extra%22:%22{\\\\%22ad_price\\\\%22:\\\\%22ZaX4oAAJJdplpfigAAkl2jj6h1yMnUMB_ixL2w\\\\%22&\\\\%22city_id\\\\%22:null&\\\\%22convert_id\\\\%22:0&\\\\%22country_id\\\\%22:null&\\\\%22dma_id\\\\%22:null&\\\\%22orit\\\\%22:900000000&\\\\%22province_id\\\\%22:null&\\\\%22req_id\\\\%22:\\\\%22c23f6f12-**************-b65cec940e1bu4491\\\\%22&\\\\%22rit\\\\%22:888784635}%22&%22orit%22:900000000&%22req_id%22:%22c23f6f12-**************-b65cec940e1bu4491%22&%22rit%22:888784635&%22sign%22:%22D41D8CD98F00B204E9800998ECF8427E%22&%22uid%22:1882007984948222&%22ut%22:12}\",\"_\":1705476442744}";
        final Matcher matcher = pattern.matcher(s);
        if(matcher.find()){
            System.out.println(matcher.group("dt").trim());
            System.out.println(matcher.group("code"));
        }

    }
}
