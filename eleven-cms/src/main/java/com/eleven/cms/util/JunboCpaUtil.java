package com.eleven.cms.util;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.jeecg.common.util.DateUtils;

import java.util.Date;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/15 14:17
 **/
public class JunboCpaUtil {
    public static String getSysOrderId() {
        return "CC" + DateUtils.yyyyMMdd.get().format(new Date()) + IdWorker.getId();
    }
    public static String getSCSysOrderId() {
        return "CC" + DateUtils.yyyymmddhhmmss.get().format(new Date()) + RandomUtils.randomCode9Num();
    }
    public static String getSCHTSysOrderId() {
        return "CC" + DateUtils.yyyyMMddHHmmssSSS.get().format(new Date()) + RandomUtils.randomCode();
    }
}
