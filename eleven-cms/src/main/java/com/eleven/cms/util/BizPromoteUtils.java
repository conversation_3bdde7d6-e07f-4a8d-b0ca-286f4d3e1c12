package com.eleven.cms.util;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MiguApiService;

import java.time.LocalTime;

public class BizPromoteUtils {

    /**
     * 是否可以推广
     *
     * @param subscribe
     *         渠道号
     * @return boolean
     */
    public static boolean isXunfeiSxhProvTimeLimit(Subscribe subscribe) {
        final LocalTime now = LocalTime.now();
        return MiguApiService.BIZ_SXH_CHANNEL_CODE_XF_AI.equals(subscribe.getSubChannel()) && "广东".equals(subscribe.getProvince()) && now.isAfter(LocalTime.of(8, 0)) && now.isBefore(LocalTime.of(20, 0));
    }
    /**
     * 山西移动_骏伯ai视频彩铃(封装一语成片业务)是否可以推广
     *
     * @param subscribe
     *         渠道号
     * @return boolean
     */
    public static boolean isSXYDJunBoAICaiLingTimeLimit(Subscribe subscribe) {
        LocalTime now = LocalTime.now();
        boolean isAfterMin = now.isAfter(LocalTime.of(8, 0,0));
        boolean isAfterMax = now.isBefore(LocalTime.of(10, 0,0));
        if(isAfterMin && isAfterMax){
            return BizConstant.BIZ_CHANNEL_SXYD_JBAICL.equals(subscribe.getChannel());
        }
        return false;
    }
}
