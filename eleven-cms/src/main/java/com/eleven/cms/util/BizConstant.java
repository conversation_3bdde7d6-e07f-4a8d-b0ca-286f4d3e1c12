package com.eleven.cms.util;

import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.google.api.client.repackaged.com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMultiset;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.SpringContextUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2019/12/18 15:18
 * Desc:业务常量
 */
public class BizConstant {
    //支付宝有效时间
    public static final Long ALIPAY_TIME =30L * 60L;
    //续订权益领取提醒短信有效时间
    public static final Long RENEW_TIME =24L * 60L * 60L;
    // 限制返回1个
    public static final String SQL_LIMIT_ONE = "LIMIT 1";

    // 短信验证码 Redis Key
    public static final String SMS_CODE_KEY_PREFIX = "sms_code_";
    // 短信验证码 Redis 过期时间 5分钟
    public static final Long SMS_CODE_TTL = 5*60L;

    //public static final String DF_PATTERN_MIGU_API = "YYYYMMddhhmmss";
    public static final String DF_PATTERN_MIGU_API = "YYYYMMddHHmmss";

    // 简单手机号正则（这里只是简单校验是否为 11位，实际规则更复杂）
    public static final String MOBILE_REG = "^[1][3,4,5,6,7,8,9][0-9]{9}$";

    //默认子渠道号
    public static final String SUB_CHANNEL_DEFAULT = "yrjy";

    //报备子渠道号
    public static final String SUB_CHANNEL_BAOBEI = "baobei";
    //头条信息流渠道号
    public static final String SUB_CHANNEL_TOUTIAO = "xxltt";
    public static final String SUB_CHANNEL_TUIA = "tuia";
    public static final String SUB_CHANNEL_TA01 = "ta01";
    public static final String SUB_CHANNEL_TA02 = "ta02";
    //变现猫信息流渠道号
    public static final String SUB_CHANNEL_BXM1 = "bxm1";
    public static final String SUB_CHANNEL_BX01 = "bx01";
    //豆盟信息流渠道号
    public static final String SUB_CHANNEL_DM01 = "dm01";
    //趣头条信息流渠道号
    public static final String SUB_CHANNEL_JB02 = "jb02";
    public static final String SUB_CHANNEL_QTT1 = "qtt1";
    public static final String SUB_CHANNEL_QTT2 = "qtt2";
    public static final String SUB_CHANNEL_QTT3 = "qtt3";
    public static final String SUB_CHANNEL_QTT4 = "qtt4";
    //互动推信息流渠道号
    public static final String SUB_CHANNEL_HUDT = "hudt";

    //开通状态
    public static final Integer SUBSCRIBE_STATUS_INIT = -1;  //初始状态
    public static final Integer SUBSCRIBE_STATUS_FAIL = 0; //开通失败
    public static final Integer SUBSCRIBE_STATUS_SUCCESS = 1; //开通成功
    public static final Integer SUBSCRIBE_STATUS_PUSHED = 2; //已派发开通
    public static final Integer SUBSCRIBE_STATUS_MONTHLY_EXISTS = 3;//已有包月
    public static final Integer SUBSCRIBE_STATUS_DUPLICATE_TODAY = 4;//当日重复
    public static final Integer SUBSCRIBE_STATUS_SMS_CODE_SUBMITED = 5;//已提交短信验证码

    public static final Integer SMS_VALIDATE_LOG_ACTION_CREATED = 0;  //发送验证码
    public static final Integer SMS_VALIDATE_LOG_ACTION_VALID = 1;  //验证通过

    //包月校验状态
    public static final Integer SUBSCRIBE_MONTH_VERIFY_INIT = -1;  //初始值
    public static final Integer SUBSCRIBE_MONTH_VERIFY_NONE = 0; //未包月
    public static final Integer SUBSCRIBE_MONTH_VERIFY_EXISTS = 1; //已包月

    //铃声设置初始状态
    public static final Integer RING_SETTING_STATUS_INIT = -1;  //初始状态
    public static final Integer RING_SETTING_STATUS_FAIL = 0;  //失败状态
    public static final Integer RING_SETTING_STATUS_SUCC = 1;  // 成功状态

    //电信爱音乐ISMP订购退订消息回调state  0 订购 1退订(短代点播产品 0成功 1失败)
    public static final String DIANXIN_ISMP_STATE_SUBSCRIBE = "0";
    public static final String DIANXIN_ISMP_STATE_UN_SUBSCRIBE = "1";

    //业务类型-视频彩铃(包含彩铃运营中心-订阅库)
    public static final String BIZ_TYPE_VRBT = "VRBT";
    //彩铃运营中心-订阅库serviceId
    public static final String BIZ_TYPE_VRBT_SERVICE_ID = "698039035100000014";
    //白金会员serviceId
    public static final String BIZ_TYPE_BJHY_PP_SERVICE_ID = "698039020105522717";
    //业务类型-渠道包月
    public static final String BIZ_TYPE_CPMB = "CPMB";
    //业务类型-振铃
    public static final String BIZ_TYPE_RT = "RT";
    //业务类型-白金会员
    public static final String BIZ_TYPE_BJHY = "BJHY";
    //业务类型-百度联合会员
    public static final String BIZ_TYPE_BAIDU = "BAIDU";
    //业务类型-视宣号
    public static final String BIZ_TYPE_SXH = "SXH";
    //业务类型-视彩号(查询订购关系用白金会员接口,订购铃音用视频彩铃的免费订购接口)
    public static final String BIZ_TYPE_SCH = "SCH";

    public static final String BIZ_TYPE_UNION_MEMBER = "UNION_MEMBER";

    //业务类型-网易云联合会员
    public static final String BIZ_TYPE_WANGYIYUN = "WANGYIYUN";
    //业务类型-四川移动和生活
    public static final String BIZ_CHANNEL_SCYD_HSH= "SCYD_HSH";

    //业务类型-上海移动
    public static final String BIZ_TYPE_SHANGHAI = "SHYD";
    //上海移动抖音视频渠道号
    public static final String BIZ_CHANNEL_SHYD_DYSP= "SHYD_DYSP";
    //上海移动炫视视频彩铃6元包渠道号
    public static final String BIZ_CHANNEL_SHYD_XSSP= "SHYD_XSSP";
    //上海移动炫视视频彩铃9.9元包渠道号
    public static final String BIZ_CHANNEL_SHYD_XSSP_VS= "SHYD_XSSP_VS";
    //上海移动流量包渠道号
    public static final String BIZ_CHANNEL_SHYD_LLB= "SHYD_LLB";
    //上海移动随心选会员渠道号
    public static final String BIZ_CHANNEL_SHYD_SXXHY= "SHYD_SXXHY";
    //上海移动流量版家庭产品渠道号
    public static final String BIZ_CHANNEL_SHYD_LLBJT= "SHYD_LLBJT";
    //上海移动炫视专属9.9元包&咕视频钻石会员（流量版 ）渠道号
    public static final String BIZ_CHANNEL_SHYD_XSMGSP= "SHYD_XSMGSP";
    //上海移动炫视流量版合约年包渠道号
    public static final String BIZ_CHANNEL_SHYD_XSNB= "SHYD_XSNB";
    //上海移动铂金会员PRO流量版渠道号
    public static final String BIZ_CHANNEL_SHYD_BJHY= "SHYD_BJHY";
    //上海移动黄金会员PRO流量优享版+随心选(上海流量版)渠道号
    public static final String BIZ_CHANNEL_SHYD_HJSXX= "SHYD_HJSXX";
    //上海移动随心选(上海流量版)+随心选生活包(上海7天流量版)渠道号
    public static final String BIZ_CHANNEL_SHYD_SXXSHB= "SHYD_SXXSHB";


    public static final String BIZ_TYPE_MEMBER_ALIPAY = "MEMBER_ALIPAY";
    public static final String BIZ_TYPE_VRBT_YD_ALIPAY="VRBT_YD_ALIPAY";
    public static final String BIZ_TYPE_VRBT_DX_ALIPAY="VRBT_DX_ALIPAY";
    public static final String BIZ_TYPE_VRBT_LT_ALIPAY="VRBT_LT_ALIPAY";
    public static final String BIZ_TYPE_WO_READ_FEE="WO_READ_FEE";
    //视频彩铃app 积分类型(0=初始,1=订购,2=签到)
    public static final String INITB_POINTS = "0"; //初始
    public static final String SUB_POINTS= "1"; //订购
    public static final String SIGN_IN_POINTS= "2"; //签到
    //订购积分
    public static final Integer SUB_POINTS_NUMBER=2000;
    //签到积分
    public static final Integer SIGN_IN_POINTS_NUMBER=50;
    //产品积分
    public static final Integer PRODUCT_NAME_POINTS=4000;


    //精品券
    public static final Integer JINGPIN_GOLDEN_BEAN_NUMBER=1888;
    //独家券
    public static final Integer DUJIA_GOLDEN_BEAN_NUMBER=3888;
    //默认app名称
    public static final String APP_NAME="com.yywvolone.dogcat";

    //业务类型-上海移动业务
    public static final String BIZ_TYPE_SHYD= "SHYD";
    public static Map<String, String> productIdMap = new ImmutableMap.Builder<String, String>()
            .put(BIZ_CHANNEL_SHYD_DYSP, "111000788275")
            .put(BIZ_CHANNEL_SHYD_XSSP, "391000213900")
            .put(BIZ_CHANNEL_SHYD_LLB, "390020010915")
            .put(BIZ_CHANNEL_SHYD_XSSP_VS, "111000900998")
            .put(BIZ_CHANNEL_SHYD_SXXHY, "111000887106")
            .build();
    public static String getProductIdByChannel(String channelCode) {
        return productIdMap.get(channelCode);
    }
    //业务类型-河北移动视频彩铃
    public static final String BIZ_TYPE_HBYD_VRBT= "HBYD_VRBT";

    public static final String BIZ_TYPE_HBYD_LLB= "HBYD_LLB";


    //业务类型-黑龙江移动视频彩铃
    public static final String BIZ_TYPE_HLJYD_VRBT= "HLJYD_VRBT";
    //业务类型-黑龙江移动抖音快手视频彩铃
    public static final String BIZ_TYPE_HLJYD_DYVRBT= "HLJYD_DYVRBT";
    //业务类型-黑龙江移动抖音快手视频彩铃 xqy
    public static final String BIZ_TYPE_HLJYD_XQY_DYVRBT= "HLJYD_XQY_DYVRBT";

    //海南移动视频彩铃
    public static final String BIZ_TYPE_HNYD_VRBT = "HNYD_VRBT";

    //业务类型-重庆移动视频彩铃定向包
    public static final String BIZ_TYPE_CQYD_VRBT_DX = "CQYD_SPCL_DX"; //业务代码 视频彩铃定向流量包 gl_spcl_yh2
    public static final String BIZ_TYPE_CQYD_5G_QYB = "CQYD_5G"; //业务代码 5G尊享权益包 gl_zxqy_2022_6
    public static final String BIZ_TYPE_CQYD_MGHYHYB = "CQYD_MGHYHYB"; //业务代码 5G尊享权益包 gl_zxqy_2022_6
    public static final String BIZ_TYPE_CQYD_VRBTHYB = "CQYD_VRBTHYB"; //业务代码 5G尊享权益包 locpp.10362626


    //渠道号-重庆移动
    public static final String BIZ_CHANNEL_SPCL_KDXLXB = "SPCL_KDXLXB";  //视频彩铃酷电秀乐享包
    public static final String BIZ_CHANNEL_SPCL_XSHXB = "SPCL_XSHXB"; //视频彩铃炫视惠享包
    public static final String BIZ_CHANNEL_SPCL_XSLXB = "SPCL_XSLXB"; //视频彩铃炫视乐享包
    public static final String BIZ_CHANNEL_SPCL_XSYXB = "SPCL_XSYXB"; //视频彩铃炫视优享包

    public static final String BIZ_TYPE_CQYD_YSM = "CQYD_YSM"; //业务类型


    //业务类型-甘肃移动30元流量包
    public static final String BIZ_TYPE_GANSU_LLB_30 = "GSYD_LLB_30";

    //业务类型-联保天下甘肃移动10元流量包
    public static final String BIZ_TYPE_LB_GANSU_LLB_10 = "LB_GSYD_LLB_10";

    //业务类型-快马江西9.9元视频彩铃炫视专属订阅包
    public static final String BIZ_TYPE_KMSX_VRBT_DYB = "KMSX_VRBT_DYB";

    //业务类型-山东和校园
    public static final String BIZ_TYPE_SD_HXY = "SD_HXY";

    //业务类型-甘肃移动30元流量包
    public static final String BIZ_TYPE_GANSU_LLNB_30 = "GSYD_LLNB_30";

    //业务类型-广西移动5元流量包
    public static final String BIZ_TYPE_GUANGXI_LLB_5= "GXYD_LLB_5";


    //业务类型-宁夏移动5G咪咕联合特惠包
    public static final String BIZ_CHANNEL_NX_XYL = "NXYD_LHTHB";
    public static final String BIZ_CHANNEL_NX_HJHY = "NXYD_HJHY";
    public static final String BIZ_CHANNEL_NX_MGDSP = "NXYD_MGDSP";
    public static final String BIZ_CHANNEL_NX_ZSYLHHY = "NXYD_ZSYLHHY";


    public static final String BIZ_TYPE_NXYD= "NXYD";


    //业务类型-四川移动
    public static final String BIZ_TYPE_SCYD= "SCYD";

    //业务类型-四川移动30元流量包
    public static final String BIZ_CHANNEL_SCYD_LLB_30= "SCYD_LLB_30";
    //业务类型-四川移动30元流量包
    public static final String BIZ_CHANNEL_SCYD_LLB_20= "SCYD_LLB_20";
    //业务类型-四川移动服务管家
    public static final String BIZ_CHANNEL_SCYD_FWGJ= "SCYD_FWGJ";
    //业务类型-四川移动视频彩铃畅玩
    public static final String BIZ_CHANNEL_SCYD_VRBTCW= "SCYD_VRBTCW";
    //业务类型-四川移动视频彩铃畅玩
    public static final String BIZ_CHANNEL_SCYD_VRBTYL= "SCYD_VRBTYL";
    //业务类型-四川移动铂金会员
    public static final String BIZ_CHANNEL_SCYD_BJHY= "SCYD_BJHY";
    //业务类型-四川移动 炫视视频彩铃娱乐包
    public static final String BIZ_CHANNEL_SCYD_XSVRBTYL = "SCYD_XSVRBTYL";
    //业务类型-四川移动 炫视视频彩铃畅玩包
    public static final String BIZ_CHANNEL_SCYD_XSVRBTCW = "SCYD_XSVRBTCW";
    //业务类型-四川移动 炫视视频彩铃娱乐包（年包促销包）
    public static final String BIZ_CHANNEL_SCYD_XSVRBTYLY = "SCYD_XSVRBTYLY";
    //业务类型-四川移动 炫视视频彩铃畅玩包（年包促销包）
    public static final String BIZ_CHANNEL_SCYD_XSVRBTCWY = "SCYD_XSVRBTCWY";
    //业务类型-四川移动 中国石油合约包
    public static final String BIZ_CHANNEL_SCYD_ZSYHYB = "SCYD_ZSYHYB";
    //业务类型-四川移动 随心看
    public static final String BIZ_CHANNEL_SCYD_SXK = "SCYD_SXK";
    //业务类型-四川移动 四川移动延长壳牌加油包
    public static final String BIZ_CHANNEL_SCYD_KPJYB= "SCYD_KPJYB";
    //业务类型-四川移动 中石油黄金月包
    public static final String BIZ_CHANNEL_SCYD_ZSYHJYB= "SCYD_ZSYHJYB";
    //业务类型-四川移动 一号双终端
    public static final String BIZ_CHANNEL_SCYD_YHSZD = "SCYD_YHSZD";
    //业务类型-四川移动
    public static final String BIZ_CHANNEL_SCYD_YLCWB = "SCYD_YLCWB";
    //业务类型-四川移动同旅视频彩铃乐享包
    public static final String BIZ_CHANNEL_SCYD_TLVRBTLXB = "SCYD_TLVRBTLXB";
    public static final String BIZ_CHANNEL_SCYD_TLVRBTLXB_PLUS = "SCYD_TLVRBTLXB_P";
    //业务类型-四川移动移麦相承五件套
    public static final String BIZ_CHANNEL_SCYD_YMXC = "SCYD_YMXC";
    //业务类型-四川移动壳牌加油包
    public static final String BIZ_CHANNEL_SCYD_QPJYB = "SCYD_QPJYB";
    //业务类型-娱乐畅玩包19.9元（同旅专属)
    public static final String BIZ_CHANNEL_SCYD_XYLCW = "SCYD_XYLCW";
    //加油权益包(中国石油)12个月
    public static final String BIZ_CHANNEL_SCYD_ZSYJYB = "SCYD_ZSYJYB";
    //娱乐畅玩包30元
    public static final String BIZ_CHANNEL_SCYD_YLCWB_30 = "SCYD_YLCWB_30";
    public static final String BIZ_CHANNEL_SCYD_YLCWB_199 = "SCYD_YLCWB_199";
    //业务类型-四川移动中石油联合会员
    public static final String BIZ_CHANNEL_SCYD_ZSYLHHY = "SCYD_ZSYLHHY";
    //业务类型-四川移动观影权益包
    public static final String BIZ_CHANNEL_SCYD_GYQY = "SCYD_GYQY";
    //业务类型-四川移动生活尊享福袋
    public static final String BIZ_CHANNEL_SCYD_SHFD = "SCYD_SHFD";
    //业务类型-四川移动生日权益
    public static final String BIZ_CHANNEL_SCYD_SRQY = "SCYD_SRQY";
    //业务类型-四川中石化加油包
    public static final String BIZ_CHANNEL_SCYD_ZSHJYB = "SCYD_ZSHJYB";
    //业务类型-四川移动万达影院包
    public static final String BIZ_CHANNEL_SCYD_WDYYB = "SCYD_WDYYB";
    //业务类型-5G新通话-明星来电白银组合包A
    public static final String BIZ_CHANNEL_SCYD_BYZHB = "SCYD_BYZHB";
    //业务类型-5G新通话-明星来电白银组合包C
    public static final String BIZ_CHANNEL_SCYD_BYZHBC = "SCYD_BYZHBC";
    //业务类型-商超会员
    public static final String BIZ_CHANNEL_SCYD_SCFD = "SCYD_SCFD";
    //业务类型-流量福袋
    public static final String BIZ_CHANNEL_SCYD_LLFD = "SCYD_LLFD";
    public static final String BIZ_CHANNEL_SCYD_BYZHBC_LLB = "SCYD_BYZHBC_LLB";
    //业务类型-快马30元流量包
    public static final String BIZ_TYPE_KM_LLB_30 = "KM_SCYD_LLB_30";
    //业务类型-快马铂金会员
    public static final String BIZ_TYPE_KM_BJHY = "KM_SCYD_BJHY";
    //业务类型-湖南视频彩铃流量包
    public static final String BIZ_TYPE_HN_VRBT = "HN_VRBT";
    //业务类型-湖南移动畅玩包
    public static final String BIZ_TYPE_HN_CWB = "HN_CWB";
    //业务类型-湖南移动观赛包
    public static final String BIZ_TYPE_HN_GSB = "HN_GSB";
    //业务类型-湖南移动观赛包B
    public static final String BIZ_TYPE_HN_GSBB = "HN_GSBB";
    //业务类型-天翼空间20元包月
    public static final String BIZ_TYPE_TYKJ = "TYKJ";
    //业务类型-联通心动彩铃
    public static final String BIZ_TYPE_XDCL = "XDCL";

    //业务类型-湖南视频彩铃流量包
    public static final String BIZ_CHANNEL_AHTS_HNYD = "AHTS_HNYD";
    public static final String BIZ_TYPE_AHTS_HNYD = "AHTS_HNYD";

    //业务类型-畅视多视频流量会员包
    public static final String BIZ_TYPE_SCLT_SPLLB= "SCLT_SPLLB";


    //业务类型-B站联通会员
    public static final String BIZ_CHANNEL_BZLTHY= "BZLTHY";
    public static final String BIZ_TYPE_BZLTHY= "BZLTHY";


    //业务类型-畅视多视频流量会员包
    public static final String BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT = "HN_DX_VRBT";

    //业务类型-湖南电信智能13元包
    public static final String BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_13 = "HN_DX_MIND_13";

    //业务类型-湖南电信智能9元包
    public static final String BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_9 = "HN_DX_MIND_9";

    //安徽移动视频彩铃
    public static final String BIZ_TYPE_CHANNEL_AHYD_VRBT = "AHYD_VRBT";

    //分省移动融合业务1
    public static final String BIZ_TYPE_CHANNEL_PROV_MIX1 = "PROV_MIX1";


    //分省移动融合业务1
    public static final String BIZ_TYPE_CHANNEL_PROV_MIX2 = "PROV_MIX2";

    //河图融合业务3
    public static final String BIZ_TYPE_CHANNEL_PROV_MIX3 = "PROV_MIX3";


    //山东电信
    public static final String BIZ_TYPE_CHANNEL_SDDX_SSHY = "SDDX_SSHY";

    //业务类型-咪咕类型
    public static final String BIZ_TYPE_READ= "READ";
    //业务类型-电信视频彩铃（独立）
    public static final String BIZ_TYPE_DX_VRBT= "DX_VRBT";
    //业务类型-四川电信视频彩铃15元流量包
    public static final String BIZ_TYPE_SYDX_VRBT_LLB_15= "SCDX_VRBT_LLB_15";
    //业务类型-四川电信视频彩铃25元流量包
    public static final String BIZ_TYPE_SYDX_VRBT_LLB_25= "SCDX_VRBT_LLB_25";

    //业务类型-新疆移动业随心选会员享流量-年包
    public static final String BIZ_TYPE_XJYD_SXX = "XJYD_SXX";
    //业务类型-新疆移动业9.9元视频彩铃特惠包
    public static final String BIZ_TYPE_XJYD_SPCL = "XJYD_SPCL";
    //业务类型-新疆移动业 25元权益至尊包
    public static final String BIZ_TYPE_XJYD_ZZQYB = "XJYD_ZZQYB";
    //业务类型-新疆移动业 15元任务选会员包
    public static final String BIZ_TYPE_XJYD_RWX = "XJYD_RWX";
    //业务类型-江苏移动视频彩铃酷电秀
    public static final String BIZ_TYPE_JSYD_VRBT = "JSYD_VRBT";
    //渠道号-江西移动定向流量包
    public static final String BIZ_CHANNEl_JXYD_LLB = "JXYD_DXLLB";
    //渠道号-江西移动炫视视频彩铃
    public static final String BIZ_CHANNEl_JXYD_VRBT = "JXYD_VRBT";
    //渠道号-江西移动定向流量包G版
    public static final String BIZ_CHANNEl_JXYD_VRBT_20 = "JXYD_VRBT_20";
    //渠道号-江西移动定向流量包D版(中屹互联)
    public static final String BIZ_CHANNEl_JXYD_VRBT_20_D = "JXYD_VRBT_20_D";
    //渠道号-江西移动豪华会员
    public static final String BIZ_CHANNEl_JXYD_HHHY = "JXYD_HHHY";
    //渠道号-江西移动银联双V会员月包
    public static final String BIZ_CHANNEl_JXYD_YLSVHY = "JXYD_YLSVHY";
    //渠道号-江西移动商超联合会员月包
    public static final String BIZ_CHANNEl_JXYD_SCLHHY = "JXYD_SCLHHY";
    //渠道号-江西移动饭票联合会员月包(中屹互联)
    public static final String BIZ_CHANNEl_JXYD_FPLHHY = "JXYD_FPLHHY";
    //渠道号-江西移动饭票联合会员月包(悠然)
    public static final String BIZ_CHANNEl_JXYD_FPLHHY_YR = "JXYD_FPLHHY_YR";
    //渠道号-江西移动萌宠Al联合会员月包(悠然)
    public static final String BIZ_CHANNEl_JXYD_MCAI_YR = "JXYD_MCAI_YR";
    //渠道号-江西移动美食会员流量畅享版
    public static final String BIZ_CHANNEl_JXYD_MSHYLLB = "JXYD_MSHYLLB";
    //渠道号-江西移动权益超市观影会员流量畅享版
    public static final String BIZ_CHANNEl_JXYD_QYCSLLB = "JXYD_QYCSLLB";
    //渠道号-江西移动银联双V会员月包
    public static final String BIZ_CHANNEl_JXYD_YL_HS="JXYD_YL_HS";


    //业务类型-江西移动
    public static final String BIZ_TYPE_JXYD = "JXYD";
    //和裕视频彩铃
    public static final String BIZ_CHANNEl_HEYU_VRBT = "HEYU_VRBT";
    //和裕视频彩铃
    public static final String BIZ_TYPE_HEYU_VRBT = "HEYU_VRBT";


    //渠道号-新疆移动#5G特惠包(2个月合约版)
    public static final String BIZ_CHANNEl_XJYD_5GTHBB = "XJYD_5GTHB";
    //渠道号-新疆移动#5G优享包
    public static final String BIZ_CHANNEl_XJYD_5GYXB = "XJYD_5GYXB";
    //渠道号-新疆移动#9.9新移惠mini会员
    public static final String BIZ_CHANNEl_XJYD_XYHMINI = "XJYD_XYHMINI";
    //渠道号-新疆移动#19.9新移惠plus会员
    public static final String BIZ_CHANNEl_XJYD_XYHPLUS = "XJYD_XYHPLUS";
    //渠道号-新疆移动#29.9新移惠max会员
    public static final String BIZ_CHANNEl_XJYD_XYHMAX = "XJYD_XYHMAX";
    //渠道号-新疆移动#NBA联盟通潮玩包(6个月)
    public static final String BIZ_CHANNEl_XJYD_NBACWB = "XJYD_NBACWB";
    //渠道号-新疆移动#9.9元炫视彩铃
    public static final String BIZ_CHANNEl_XJYD_XSVRBT = "XJYD_XSVRBT";
    //渠道号-新疆移动#人我选高价值版本
    public static final String BIZ_CHANNEl_XJYD_RWXGJZB = "XJYD_RWXGJZB";
    //渠道号-新疆移动#视频彩铃体验
    public static final String BIZ_CHANNEl_XJYD_SPCLTY = "XJYD_SPCLTY";
    //渠道号-新疆移动#手机端专属100M国内流量日包
    public static final String BIZ_CHANNEl_XJYD_LLRB = "XJYD_LLRB";
    //业务类型-江西移动
    public static final String BIZ_TYPE_XJYD = "XJYD";
    //业务类型-四川移动鸿盛
    public static final String BIZ_TYPE_SCYD_HS= "SCYD_HS";
    //vr竞盟
    public static final String BIZ_CHANNEl_VR_JM_10 = "HY_10_XX";
    //vr竞盟
    public static final String BIZ_CHANNEl_VR_JM_25 = "HY_25_CW";
    //业务类型-咪咕互娱
    public static final String BIZ_TYPE_MGHY = "MGHY";
    //业务类型-吉林移动鸿盛
    public static final String BIZ_TYPE_JLYD_HS= "JLYD_HS";
    //河图
    public static final String BIZ_CHANNEl_HETU = "HETU";
    public static final String BIZ_CHANNEl_HETU_GZ = "HETU_GZ";
    public static final String BIZ_CHANNEl_HETU_HB = "HETU_HB";
    public static final String BIZ_CHANNEl_HETU_HENAN = "HETU_HENAN";
    public static final String BIZ_CHANNEl_HYQY_YR = "HYQY_YR";
    public static final String BIZ_CHANNEl_HYQY_YR_20 = "HYQY_YR_20";
    public static final String BIZ_CHANNEl_HYQY_YR_30 = "HYQY_YR_30";
    public static final String BIZ_CHANNEl_HYQY_YR_20_912 = "HYQY_YR_20_912";
    public static final String BIZ_CHANNEl_HYQY_HTJB20_924 = "HYQY_HTJB20_924";
    public static final String BIZ_CHANNEl_HYQY_HTJB20_925 = "HYQY_HTJB20_925";
    public static final String BIZ_CHANNEl_HYQY_HTJB20_926 = "HYQY_HTJB20_926";
    public static final String BIZ_CHANNEl_HYQY_JUNBO = "HYQY_JUNBO";
    public static final String BIZ_CHANNEl_HYQY_JUNBO_20 = "HYQY_JUNBO_20";
    public static final String BIZ_CHANNEl_HYQY_ZUOYUE = "HYQY_ZUOYUE";
    public static final String BIZ_CHANNEl_HYQD_MAIHE = "HYQD_MAIHE";
    public static final String BIZ_CHANNEl_HYQD_HS = "HYQD_HS";
    public static final String BIZ_CHANNEl_HYQD_O_HS = "HYQD_O_HS";
    //咕互娱鸿盛渠道 特定渠道号收到破解通知后必须马上调用包月查询接口
    public static final List<String> HONGSHENG_CHANNEL_LIST = Arrays.asList(BIZ_CHANNEl_HYQD_HS,BIZ_CHANNEl_HYQD_O_HS,BIZ_CHANNEl_HYQD_MAIHE);

    public static final String BIZ_TYPE_HETU = "HETU";
    public static final String BIZ_TYPE_HYQY_TEST = "HYQY_TEST";
    public static final String BIZ_TYPE_HYQD="HYQD";
    //大鱼消除
    public static final String BIZ_CHANNEl_DYXC_HB = "DYXC_HB";
    public static final String BIZ_TYPE_DYXC = "DYXC";
    //思维方阵
    public static final String BIZ_TYPE_SWFZ = "SWFZ";
    //厨房大逃亡
    public static final String BIZ_TYPE_CFDTW = "CFDTW";
    //我自为道
    public static final String BIZ_TYPE_WZWD = "WZWD";
    //快游超级会员
    public static final String BIZ_CHANNEL_KYCJHY = "KYCJHY";
    public static final String BIZ_TYPE_KYCJHY= "KYCJHY";

    //贵州电信
    public static final String BIZ_CHANNEl_GZDX = "GZDX";
    public static final String BIZ_CHANNEl_GZDX_04 = "CN_GZ_LL_04"; //橙翼权益小合约(视频彩铃心动会员)
    public static final String BIZ_CHANNEl_GZDX_05 = "CN_GZ_LL_05"; //橙翼权益小合约(天翼云盘黄金会员)
    public static final String BIZ_CHANNEl_GZDX_06 = "CN_GZ_LL_06"; //橙翼权益小合约(通信助理尊享版)
    public static final String BIZ_TYPE_GZDX = "GZDX";
    //爱豆贵州移动20元包
    public static final String BIZ_CHANNEL_AIDOU_GZYD_20 = "AIDOU_GZYD_20";
    public static final String BIZ_TYPE_AIDOU_GZYD = "AIDOU_GZYD";

    //手机资讯20元包
    public static final String BIZ_CHANNEL_SHOUJIZIXUN_YD_20 = "SJZX_YD_20";
    //手机资讯15元包
    public static final String BIZ_CHANNEL_SHOUJIZIXUN_YD_15 = "SJZX_YD_15";
    //手机资讯和津惠饭票联名会员
    public static final String BIZ_CHANNEL_HEJINFANPIAO_YD_20 = "HJFP_YD_20";
    //手机资讯和津惠商超联合会员
    public static final String BIZ_CHANNEL_HEJINSHANGCHAO_YD_20 = "HJSC_YD_20";
    public static final String BIZ_CHANNEL_YEDAOFANPIAO_YD_19 = "YDFP_YD_19";
    public static final String BIZ_CHANNEL_SHANGHAICHIHUOZUANKA_YD_20 = "SHCHZK_YD_19";
    public static final String BIZ_CHANNEL_CHAOSHIYINPIN_YD_25 = "CSYP_YD_25";
    public static final String BIZ_CHANNEL_JM_DX_10 = "JM_DX_10";
    public static final String BIZ_CHANNEL_WM_DX_10 = "WM_DX_10";
    public static final String BIZ_CHANNEL_CQGW_YD_25 = "CQGW_YD_25";
    public static final String BIZ_CHANNEL_SCYLCW_YD_19 = "SCYLCW_YD_19";
    public static final String BIZ_CHANNEL_HNLLB_YD_19 = "HNLLB_YD_19";
    public static final String BIZ_CHANNEL_GZJYB_YD_20 = "GZJYB_YD_20";
    public static final String BIZ_CHANNEL_HNYX_DX_20 = "HNYX_DX_20";
    public static final String BIZ_CHANNEL_CNL_YD_15 = "CNL_YD_15";
    public static final String BIZ_CHANNEL_SPCL_LT_15 = "SPCL_LT_15";
    public static final String BIZ_CHANNEL_WYD_LT_20 = "WYD_LT_20";
    public static final String BIZ_CHANNEL_SPCL_YD_10 = "SPCL_YD_10";
    public static final String BIZ_CHANNEL_SPCL_YD_6 = "SPCL_YD_6";
    public static final String BIZ_CHANNEL_SPCL_YD_9 = "SPCL_YD_9";
    public static final String BIZ_CHANNEL_BJ_DX_19 = "BJ_DX_19";
    public static final String BIZ_CHANNEL_YHY_DX_20 = "YHY_DX_20";
    public static final String BIZ_CHANNEL_HJX_YD_15 = "HJX_YD_15";
    public static final String BIZ_CHANNEL_SPCL_LT_25 = "SPCL_LT_25";
    public static final String BIZ_CHANNEL_GSFP_YD_19 = "GSFP_YD_19";
    public static final String BIZ_CHANNEL_GSDF_YD_19 = "GSDF_YD_19";
    public static final String BIZ_CHANNEL_MDL_YD_19 = "MDL_YD_19";
    public static final String BIZ_CHANNEL_RXKF_YD_19 = "RXKF_YD_19";
    public static final String BIZ_TYPE_SHOUJIZIXUN_YD = "SJZX_YD";

    //蜂助手四川移动19.9平安健康包
    public static final String BIZ_CHANNEL_FENGZHUSHOU_SCYD_PAJKB = "FZS_SCYD_PAJK";
    public static final String BIZ_TYPE_FZS = "FZS";

    //蜂助手四川移动19.9平安健康包
    public static final String BIZ_CHANNEL_CQYD_QYCS = "CQYD_QYCS";
    public static final String BIZ_TYPE_CQYD_QYCS = "CQYD_QYCS";


    //骏伯上海移动存量业务
    public static final String BIZ_CHANNEL_SHYD_CUN_LIANG = "SHYD_CUN_LIANG";
    public static final String BIZ_TYPE_JUNBO_SHYD = "JUNBO_SHYD";

    //北京亿汇电信10元包
    public static final String BIZ_CHANNEL_BEIJINGYIHUI_DIANXIN = "BJYH_DX";
    public static final String BIZ_CHANNEL_BEIJINGYIHUI_LIANTONG = "BJYH_LT";
    public static final String BIZ_CHANNEL_BEIJINGYIHUI_SHANDONGYIDONG = "BJYH_SDYD";
    public static final String BIZ_CHANNEL_BEIJINGYIHUI_YIDONG = "BJYH_YD";
    public static final String BIZ_CHANNEL_BEIJINGYIHUI_SHANGHAIYIDONG = "BJYH_SHYD";
    public static final String BIZ_TYPE_BEIJINGYIHUI = "BJYH";

    //云摄美海南移动业务
    public static final String BIZ_CHANNEL_HNYD_YDSXX_PLUS = "YDSXX_PLUS";
    public static final String BIZ_CHANNEL_HNYD_SPCL_XSZS = "SPCL_XSZS"; //炫视专属视频彩铃
    public static final String BIZ_CHANNEL_HNYD_SPCL_KDXZS = "SPCL_KDXZS"; //酷电秀视频彩铃
    public static final String BIZ_TYPE_YSM_HNYD = "YSM_HNYD";


    //彩讯呼叫秀业务
    public static final String BIZ_CHANNEL_CX_DJSJ = "CX_DJSJ";
    public static final String BIZ_CHANNEL_CX_CLJP = "CX_CLJP";
    public static final String BIZ_CHANNEL_CX_HYTX = "CX_HYTX";
    public static final String BIZ_CHANNEL_CX_DMKJ = "CX_DMKJ";
    public static final String BIZ_TYPE_CAIXUN = "CAIXUN";

    //老贺呼叫秀
    public static final String BIZ_CHANNEL_LH_HJX = "LH_HJX";
    public static final String BIZ_TYPE_LH_HJX = "LH_HJX";


    //广东移动存量
    public static final String BIZ_CHANNEL_GDYD_STOCK = "GDYD_STOCK";
    public static final String BIZ_CHANNEL_GDYD_LLB = "GDYD_LLB";
    public static final String BIZ_CHANNEL_GDYD_XFJ = "GDYD_XFJ";
    public static final String BIZ_TYPE_GUANGDONG = "GDYD";

    //西藏移动
    public static final String BIZ_CHANNEL_XZ_XUANSHI = "XZ_XUANSHI";
    public static final String BIZ_TYPE_XIZANG = "XZYD";

    //天翼通讯助理
    public static final String BIZ_CHANNEL_TY_COMM = "TY_COMM_ASSIST";
    public static final String BIZ_CHANNEL_EXCLUSIVE_CARD = "EXCLUSIVE_CARD";
    public static final String BIZ_CHANNEL_AI_VRBT = "AI_VRBT";
    public static final String BIZ_CHANNEL_AI_VRBT_NEW = "AI_VRBT_NEW";
    public static final String BIZ_TYPE_TY = "TY_COMM";


    //卡塞存量业务
    public static final String BIZ_CHANNEL_KA_SAI_STOCK_YD = "KA_SAI_STOCK_YD";
    public static final String BIZ_CHANNEL_KA_SAI_STOCK_LT = "KA_SAI_STOCK_LT";
    public static final String BIZ_TYPE_KA_SAI_STOCK = "KA_SAI_STOCK";
    //斯特普业务
    public static final String BIZ_CHANNEL_STP_JSYD_STOCK = "STP_JSYD_STOCK";
    public static final String BIZ_CHANNEl_STP_JXYD_STOCK = "STP_JXYD_STOCK";
    public static final String BIZ_TYPE_STP = "STP";


    //电信云游戏
    public static final String BIZ_CHANNEL_DX_CLOUDGAME = "DX_CLOUDGAME";

    public static final List<String> GZDX_CHANNEL_LIST = Arrays.asList(BIZ_CHANNEl_GZDX_04,BIZ_CHANNEl_GZDX_05,BIZ_CHANNEl_GZDX_06);



    //时代星辰联通业务
    public static final String BIZ_CHANNEL_SDXC_LT_VRBT = "SDXC_LT_VRBT";
    public static final String BIZ_CHANNEL_SDXC_YD_VRBT = "SDXC_YD_VRBT";
    public static final String BIZ_TYPE_SDXC = "SDXC_LT";

    //广东笃行视频彩铃
    public static final String BIZ_CHANNEl_GDDX_VRBT = "GDDX_VRBT";
    public static final String BIZ_TYPE_GDDX_VRBT = "GDDX_VRBT";
    //咪咕音乐业务类型
    public static final String BIZ_TYPE_MIGU_MUSIC = "MIGU_MUSIC";


    //广州旭同移动业务
    //CCTV手机资讯20元
    public static final String BIZ_CHANNEL_CCTV_NEWS = "CCTV_NEWS";
    //呼叫秀-萌宠动物10元
    public static final String BIZ_CHANNEL_CALL_SHOW = "CALL_SHOW";
    //广东联通-乐享月卡会员（芒果TV版）
    public static final String BIZ_CHANNEL_LEXIANG_VIP = "LEXIANG_VIP";
    //江苏联通-5G畅享云守护权益包25元
    public static final String BIZ_CHANNEL_XT_YUNSHOUHU = "XT_YUNSHOUHU";
    //江苏联通-5G畅享视频彩铃铂金会员权益包25元
    public static final String BIZ_CHANNEL_XT_BOJINVIP = "XT_BOJINVIP";
    //江苏联通-5G加油包29元
    public static final String BIZ_CHANNEL_XT_JIAYOUBAO = "XT_JIAYOUBAO";
    //陕西电信-悦会员影音娱乐权益包20元
    public static final String BIZ_CHANNEL_XT_DX_YUEHUIYUAN = "XT_DX_YUEHUIYUAN";
    //广东联通-畅游权益包20元
    public static final String BIZ_CHANNEL_XT_LT_CHANGYOU = "XT_LT_CHANGYOU";
    public static final String BIZ_TYPE_XUTONG_YD = "XUTONG_YD";



    //软游通业务
    //联通业务
    public static final String BIZ_CHANNEL_RUANYOUTONG_LT = "RUANYOUTONG_LT";
    public static final String BIZ_CHANNEL_RYT_LT_JIAYOU = "RYT_LT_JIAYOU";
    public static final String BIZ_CHANNEL_RYT_LT_GECAI = "RYT_LT_GECAI";
    //移动业务
    public static final String BIZ_CHANNEL_RUANYOUTONG_YD = "RUANYOUTONG_YD";
    public static final String BIZ_CHANNEL_RYT_YD_XIANYU="RYT_YD_XIANYU";
    public static final String BIZ_CHANNEL_RYT_YD_QCYYB="RYT_YD_QCYYB";
    public static final String BIZ_CHANNEL_RYT_YD_SRQY="RYT_YD_SRQY";
    public static final String BIZ_CHANNEL_RYT_YD_YSYDH="RYT_YD_YSYDH";
    public static final String BIZ_CHANNEL_RYT_YD_HJX="RYT_YD_HJX";
    public static final String BIZ_CHANNEL_RYT_YD_CHIHUO="RYT_YD_CHIHUO";


    //电信业务
    public static final String BIZ_CHANNEL_RUANYOUTONG_DX = "RUANYOUTONG_DX";
    public static final String BIZ_TYPE_RUANYOUTONG = "RUANYOUTONG";

    //云南移动权益超市铂金会员(悠然)
    public static final String BIZ_CHANNEL_YN_BJHY_YR = "YN_BJHY_YR";
    //云南移动权益超市铂金会员(鸿盛)
    public static final String BIZ_CHANNEL_YN_BJHY_HS = "YN_BJHY_HS";
    //云南移动数智生活权益包(鸿盛)
    public static final String BIZ_CHANNEL_YN_SZSH_HS = "YN_SZSH_HS";
    //云南移动权益超市铂金会员(高姐)
    public static final String BIZ_CHANNEL_YN_BJHY_GJ = "YN_BJHY_GJ";

    public static final String BIZ_TYPE_YNYD = "YNYD";

    //新疆移动业务集合
    public static final List<String> XINJIANG_MOBILE_BIZ_LIST
            = Arrays.asList(BIZ_TYPE_XJYD_ZZQYB, BIZ_TYPE_XJYD_SPCL, BIZ_TYPE_XJYD_SXX, BIZ_TYPE_XJYD_RWX);

    //业务类型-湖北移动-湖北车主会员（轻享版）
    public static final String BIZ_TYPE_HUBYD_CZHY_QXB= "HUBYD_CZHY_QXB";
    //业务类型-湖北移动-湖北车主会员（轻享版）
    public static final String BIZ_TYPE_HUBYD_CZHY= "HUBYD_CZHY";
    //四川移动河图
    public static final String BIZ_CHANNEL_SC_HTXXJ_HS="SC_HTXXJ_HS";

    //业务类型-视频彩铃(白金会员&北岸唐唱音乐包)
    public static final String BIZ_TYPE_CPBY_BJHY = "CPBY,BJHY";
    //白金会员serviceId
    public static final String BIZ_TYPE_BJHY_SERVICE_ID = "698039020100000144";
    //北岸唐唱音乐包serviceId
    public static final String BIZ_TYPE_CPBY_SERVICE_ID = "698039020108689345";
    //彩铃运营中心订阅包serviceId
    public static final String BIZ_TYPE_VLZXVRBT_SERVICE_ID = "698039035100000057";

    //骏伯直充状态 -1=预约直充,0=直充中,1=直充成功,2=直充失败
    public static final Integer JUNBO_RECHARGE_STATUS_SCHEDULED = -1;//预约直充
    public static final Integer JUNBO_RECHARGE_STATUS_PROCESSING = 0;//直充中
    public static final Integer JUNBO_RECHARGE_STATUS_SUCCESS = 1;//直充成功
    public static final Integer JUNBO_RECHARGE_STATUS_FAIL = 2;//直充失败
    public static final Integer JUNBO_RECHARGE_STATUS_RECOVERY =3;//回收
    public static final Integer JUNBO_RECHARGE_STATUS_INVALID = -2;//失效
    //沃音乐订购状态 1: 正式订购（正常） 2: 暂停（隐藏） 4: 订购关系停止（等待删除）
    public static final String WO_MUSIC_STATUS_SUCCESS = "1";//正式订购
    public static final String WO_MUSIC_STATUS_STOP ="2";//暂停
    public static final String WO_MUSIC_STATUS_END ="4";//订购关系停止
    public static final String WO_MUSIC_PRODUCT_ID="4900753400";
    //骏伯充值备注
    public static final String JUNBO_MANUAL_RECHARGE_REMARK ="手动充值";//手动充值
    //pptv赠送备注
    public static final String PPTV_PRODUCT_NAME_REMARK ="pptv视频赠送";//pptv视频赠送
    //pptv视频赠送短信发送延迟时间
    public static final Long PPTV_MINUTES = 1L;//pptv视频赠送短信发送延迟时间
    //赠送状态:0=赠送中,1=赠送成功,2=赠送失败
    public static final Integer PPTV_GIVE_STATUS_SUCCESS = 1;//赠送成功
    //赠送状态:0=赠送中,1=赠送成功,2=赠送失败
    public static final Integer PPTV_GIVE_STATUS_FAIL = 2;//赠送失败
    //pptv视频短信发送状态:0=未发送,1=已发送
    public static final Integer PPTV_STATUS_RESERVE = 0;//未发送
    public static final Integer PPTV_STATUS_SUCCESS = 1;//已发送

    public static final String YES = "1";
    public static final String NO = "0";

    //天翼空间-退订
    public static final String TYKJ_TYPE_UNREG = "0";
    //天翼空间-订购
    public static final String TYKJ_TYPE_BOOK = "1";
    //天翼空间-续订
    public static final String TYKJ_TYPE_RENEW = "2";
    //天翼空间-百度网盘-成功
    public static final String TYKJ_BDWP_SUCCEED = "1";
    //天翼空间-百度网盘-失败
    public static final String TYKJ_BDWP_FAIL = "2";

    //视频彩铃融合
    public static final String VRBT_FUSE = "1";
    //移动咪咕业务使用咪咕支付
    public static final String CMCC_MIGU_PAY = "1";



    /**
     * 天翼空间serviceId
     */
    public static final String BIZ_TIANYI_SERVICE_ID = "90030426";

    /**
     * 咪咕阅读serviceId
     */
    public static final String BIZ_MIGU_READ_SERVICE_ID = "1610473";
    //酷狗权益ID
    public static final String KG_RIGHTS_ID = "kgykzhichong";
    //网易云权益ID
    public static final String WYY_RIGHTS_ID = "wangyiyunyinyueheijiaoVIPyueka";
    //河图辽宁分省业务
    public static final String BIZ_CHANNEL_HETU_LN = "HETU_LN";
    public static final String BIZ_TYPE_HETU_FENSHENG = "HETU_FENSHENG";
    //河图重庆分省业务
    public static final String BIZ_CHANNEL_HETU_CQ = "HETU_CQ";
    /**
     * 20元渠道包月包 北岸唐唱音乐包渠道号
     */
    public static final String BIZ_CHANNEL_CODE = "00210MK";
    public static final String SCHEDULED_MSG = "您领取的会员正在路上，预计1小时后到达@请凭本手机号登录官方APP查看";
    public static final String RECEIVE_MSG   = "本月会员权益你已领取,请下月再来领取";
    public static final String MEMBER_MSG    = "您还不是会员，立即办理！";
    public static final String RECHARGE_MSG  = "您领取的会员正在充值中，请耐心等待！";
    public static final String SUCCESS_MSG = "领取成功！@请凭本手机号登录官方APP查看";
    public static final String NOT_YET_MSG   = "您订购视频彩铃业务未满3个月，暂时无法领取";
    public static final String YETGET_MSG   = "会员权益已领取";
    public static final String WO_MUSIC_RECEIVE_MSG   = "您目前暂不符合会员领取资格，需订购满三个月才可领取，请持续关注。";


    /**
     *  根据渠道号确定业务类型
     * @param miguChannel
     * @return
     */
    public static String getBizTypeByMiguChannel(String miguChannel) {
        CmsCrackConfig cmsCrackConfig = SpringContextUtils.getBean(ICmsCrackConfigService.class).getCrackConfigByChannel(miguChannel);
        if (cmsCrackConfig != null) {
            return cmsCrackConfig.getBizType();
        }
        String bizType = BizConstant.BIZ_TYPE_VRBT;
        if(Strings.isNullOrEmpty(miguChannel)){
            return bizType;
        }
        switch (miguChannel) {
            case BIZ_QYCL:
            case BIZ_QYCL_GR:
            case BIZ_QYCL_MH:
            case BIZ_QYCL_GR_MH:
            case BIZ_QYCL_GR_18:
            case BIZ_QYCL_DY:
            case BIZ_QYCL_DY_MH:
                bizType = BizConstant.BIZ_TYPE_QYCL;
                break;
            case MiguApiService.BIZ_BJHY_CHANNEL_CODE:
            case MiguApiService.BIZ_BJHYYS_CHANNEL_CODE:
            case MiguApiService.BIZ_BJHYYS_CHANNEL_CODE_TY:
            case MiguApiService.BIZ_BJHYYS_CHANNEL_CODE_TZ:
            case MiguApiService.BIZ_BJHYDY_CHANNEL_CODE:
            case MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1:
            case MiguApiService.BIZ_BJHY_CHANNEL_CODE_U2:
            case MiguApiService.BIZ_BJHY_CHANNEL_CODE_U3:
            case MiguApiService.BIZ_BJHY_CHANNEL_CODE_U4:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W6:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W7:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XE:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XF:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XG:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XB:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XC:
            case MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XD:
                bizType = BizConstant.BIZ_TYPE_BJHY;
                break;
            case MiguApiService.BIZ_CPMB_10_CHANNEL_CODE:
            case MiguApiService.BIZ_CPMB_10_CHANNEL_CODE_VO:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_2:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_3:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_4:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_KUGOU:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_W5:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_09:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_0A:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_07:
            case MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_08:
            case MiguApiService.BIZ_CPMB_30_CHANNEL_CODE:
            case MiguApiService.BIZ_CPMB_30_CHANNEL_CODE_2S:
            case MiguApiService.BIZ_CPMB_30_CHANNEL_CODE_2T:
            case MiguApiService.BIZ_CPMB_30_CHANNEL_CODE_WU:
            case MiguApiService.BIZ_CPMB_30_CHANNEL_CODE_X1:
            case MiguApiService.BIZ_CPMB_30_CHANNEL_CODE_X2:
                bizType = BizConstant.BIZ_TYPE_CPMB;
                break;
            case MiguApiService.BIZ_RT_CHANNEL_CODE:
                bizType = BizConstant.BIZ_TYPE_RT;
                break;
            case MiguApiService.BIZ_SXH_CHANNEL_CODE:
            case MiguApiService.BIZ_SXH_CHANNEL_CODE_XF:
            case MiguApiService.BIZ_SXH_CHANNEL_CODE_XF_AH:
            case MiguApiService.BIZ_SXH_CHANNEL_CODE_XF_AI:
            case MiguApiService.BIZ_SXH_CHANNEL_CODE_XF_8T:
                bizType = BizConstant.BIZ_TYPE_SXH;
                break;
            case MiguApiService.BIZ_SCH_CHANNEL_CODE_SZR:
            case MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX:
                bizType = BizConstant.BIZ_TYPE_SCH;
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_HSH:
            case BizConstant.BIZ_CHANNEL_SCYD_LLB_30:
            case BizConstant.BIZ_CHANNEL_SCYD_FWGJ:
            case BizConstant.BIZ_CHANNEL_SCYD_VRBTCW:
            case BizConstant.BIZ_CHANNEL_SCYD_VRBTYL:
            case BizConstant.BIZ_CHANNEL_SCYD_BJHY:
            case BizConstant.BIZ_CHANNEL_SCYD_XSVRBTCW:
            case BizConstant.BIZ_CHANNEL_SCYD_XSVRBTYL:
            case BizConstant.BIZ_CHANNEL_SCYD_XSVRBTCWY:
            case BizConstant.BIZ_CHANNEL_SCYD_XSVRBTYLY:
            case BizConstant.BIZ_CHANNEL_SCYD_ZSYHYB:
            case BizConstant.BIZ_CHANNEL_SCYD_SXK:
            case BizConstant.BIZ_CHANNEL_SCYD_KPJYB:
            case BizConstant.BIZ_CHANNEL_SCYD_ZSYHJYB:
            case BizConstant.BIZ_CHANNEL_SCYD_YHSZD:
            case BizConstant.BIZ_CHANNEL_SCYD_YLCWB:
            case BizConstant.BIZ_CHANNEL_SCYD_TLVRBTLXB:
            case BizConstant.BIZ_CHANNEL_SCYD_TLVRBTLXB_PLUS:
            case BizConstant.BIZ_TYPE_KM_LLB_30:
            case BizConstant.BIZ_TYPE_KM_BJHY:
            case BizConstant.BIZ_CHANNEL_SCYD_YMXC:
            case BizConstant.BIZ_CHANNEL_SCYD_QPJYB:
            case BizConstant.BIZ_CHANNEL_SCYD_XYLCW:
            case BIZ_CHANNEL_SCYD_ZSYJYB:
            case BizConstant.BIZ_CHANNEL_SCYD_YLCWB_30:
            case BizConstant.BIZ_CHANNEL_SCYD_YLCWB_199:
            case BizConstant.BIZ_CHANNEL_SCYD_ZSYLHHY:
            case BizConstant.BIZ_CHANNEL_SCYD_GYQY:
            case BizConstant.BIZ_CHANNEL_SCYD_SHFD:
            case BizConstant.BIZ_CHANNEL_SCYD_SRQY:
            case BizConstant.BIZ_CHANNEL_SCYD_WDYYB:
            case BizConstant.BIZ_CHANNEL_SCYD_BYZHB:
            case BizConstant.BIZ_CHANNEL_SCYD_BYZHBC:
            case BizConstant.BIZ_CHANNEL_SCYD_SCFD:
            case BizConstant.BIZ_CHANNEL_SCYD_LLFD:
            case BizConstant.BIZ_CHANNEL_SCYD_BYZHBC_LLB:
                bizType = BIZ_TYPE_SCYD;
                break;
            case BizConstant.BIZ_CHANNEL_SHYD_DYSP:
            case BizConstant.BIZ_CHANNEL_SHYD_XSSP:
            case BizConstant.BIZ_CHANNEL_SHYD_XSSP_VS:
            case BizConstant.BIZ_CHANNEL_SHYD_LLB:
            case BizConstant.BIZ_CHANNEL_SHYD_SXXHY:
            case BizConstant.BIZ_CHANNEL_SHYD_LLBJT:
            case BizConstant.BIZ_CHANNEL_SHYD_XSMGSP:
            case BizConstant.BIZ_CHANNEL_SHYD_XSNB:
            case BizConstant.BIZ_CHANNEL_SHYD_BJHY:
            case BizConstant.BIZ_CHANNEL_SHYD_HJSXX:
            case BizConstant.BIZ_CHANNEL_SHYD_SXXSHB:
                bizType = BizConstant.BIZ_TYPE_SHANGHAI;
                break;
            case MiguApiService.BIZ_MIGU_RD_CHANNEL_CODE:
                bizType = BizConstant.BIZ_TYPE_READ;
                break;
            case BizConstant.BIZ_TYPE_SYDX_VRBT_LLB_15:
                bizType = BizConstant.BIZ_TYPE_SYDX_VRBT_LLB_15;
                break;
            case BizConstant.BIZ_TYPE_SYDX_VRBT_LLB_25:
                bizType = BizConstant.BIZ_TYPE_SYDX_VRBT_LLB_25;
                break;
            case BizConstant.BIZ_TYPE_CQYD_VRBT_DX:
                bizType = BizConstant.BIZ_TYPE_CQYD_VRBT_DX;
                break;
            case BizConstant.BIZ_TYPE_CQYD_5G_QYB:
                bizType = BizConstant.BIZ_TYPE_CQYD_5G_QYB;
                break;
            case BizConstant.BIZ_TYPE_CQYD_MGHYHYB:
                bizType = BizConstant.BIZ_TYPE_CQYD_MGHYHYB;
                break;
            case BizConstant.BIZ_TYPE_CQYD_VRBTHYB:
                bizType = BizConstant.BIZ_TYPE_CQYD_VRBTHYB;
                break;
            case BizConstant.BIZ_TYPE_HBYD_VRBT:
                bizType = BizConstant.BIZ_TYPE_HBYD_VRBT;
                break;
            case BizConstant.BIZ_TYPE_GANSU_LLB_30:
                bizType = BizConstant.BIZ_TYPE_GANSU_LLB_30;
                break;
            case BizConstant.BIZ_TYPE_LB_GANSU_LLB_10:
                bizType = BizConstant.BIZ_TYPE_LB_GANSU_LLB_10;
                break;
            case BizConstant.BIZ_TYPE_KMSX_VRBT_DYB:
                bizType = BizConstant.BIZ_TYPE_KMSX_VRBT_DYB;
                break;
            case BizConstant.BIZ_TYPE_SD_HXY:
                bizType = BizConstant.BIZ_TYPE_SD_HXY;
                break;
            case BizConstant.BIZ_TYPE_GANSU_LLNB_30:
                bizType = BizConstant.BIZ_TYPE_GANSU_LLNB_30;
                break;
            case BizConstant.BIZ_TYPE_GUANGXI_LLB_5:
                bizType = BizConstant.BIZ_TYPE_GUANGXI_LLB_5;
                break;
            case BizConstant.BIZ_TYPE_TYKJ:
                bizType = BizConstant.BIZ_TYPE_TYKJ;
                break;
            case BizConstant.BIZ_TYPE_XDCL:
                bizType = BizConstant.BIZ_TYPE_XDCL;
                break;
            case BizConstant.BIZ_TYPE_HN_VRBT:
                bizType = BizConstant.BIZ_TYPE_HN_VRBT;
                break;
            case BizConstant.BIZ_TYPE_HN_CWB:
                bizType = BizConstant.BIZ_TYPE_HN_CWB;
                break;
            case BizConstant.BIZ_TYPE_HN_GSB:
                bizType = BizConstant.BIZ_TYPE_HN_GSB;
                break;
            case BizConstant.BIZ_TYPE_HN_GSBB:
                bizType = BizConstant.BIZ_TYPE_HN_GSBB;
                break;
            case BizConstant.BFM_CHANNEL:
                bizType = BizConstant.BIZ_TYPE_BFM;
                break;
            case BizConstant.BIZ_TYPE_XJYD_ZZQYB:
                bizType = BizConstant.BIZ_TYPE_XJYD_ZZQYB;
                break;
            case BizConstant.BIZ_TYPE_XJYD_SXX:
                bizType = BizConstant.BIZ_TYPE_XJYD_SXX;
                break;
            case BizConstant.BIZ_TYPE_XJYD_SPCL:
                bizType = BizConstant.BIZ_TYPE_XJYD_SPCL;
                break;
            case BizConstant.BIZ_TYPE_XJYD_RWX:
                bizType = BizConstant.BIZ_TYPE_XJYD_RWX;
                break;
            case BizConstant.BIZ_CHANNEL_NX_XYL:
            case BizConstant.BIZ_CHANNEL_NX_HJHY:
            case BizConstant.BIZ_CHANNEL_NX_MGDSP:
            case BizConstant.BIZ_CHANNEL_NX_ZSYLHHY:
                bizType = BizConstant.BIZ_TYPE_NXYD;
                break;
            case BizConstant.BIZ_TYPE_SCLT_SPLLB:
                bizType = BizConstant.BIZ_TYPE_SCLT_SPLLB;
                break;
            case BizConstant.BIZ_CHANNEL_BZLTHY:
                bizType = BizConstant.BIZ_TYPE_BZLTHY;
                break;
            case BizConstant.BIZ_TYPE_GZYD_LLB_JC5G:
                bizType = BizConstant.BIZ_TYPE_GZYD_LLB_JC5G;
                break;
            case BizConstant.BIZ_TYPE_HLJYD_DYVRBT:
                bizType = BizConstant.BIZ_TYPE_HLJYD_DYVRBT;
                break;
            case BizConstant.BIZ_TYPE_HLJYD_XQY_DYVRBT:
                bizType = BizConstant.BIZ_TYPE_HLJYD_XQY_DYVRBT;
                break;
            case BizConstant.BIZ_TYPE_GDYD_CXXR_SPCL:
                bizType = BizConstant.BIZ_TYPE_GDYD_CXXR_SPCL;
                break;
            case BizConstant.BIZ_TYPE_GDYD_CXXR_SPCL6:
                bizType = BizConstant.BIZ_TYPE_GDYD_CXXR_SPCL6;
                break;
            case BizConstant.BIZ_TYPE_GDYD_CXXR_SPCL99:
                bizType = BizConstant.BIZ_TYPE_GDYD_CXXR_SPCL99;
                break;
            case BizConstant.BIZ_TYPE_HNYD_VRBT:
                bizType = BizConstant.BIZ_TYPE_HNYD_VRBT;
                break;
            case BizConstant.BIZ_TYPE_GZYD_SXK:
                bizType = BizConstant.BIZ_TYPE_GZYD_SXK;
                break;
            case BizConstant.BIZ_TYPE_GZYD_SXX:
                bizType = BizConstant.BIZ_TYPE_GZYD_SXX;
                break;
//            case BizConstant.BIZ_TYPE_GZYD_HJHY_TYHD:
//                bizType = BizConstant.BIZ_TYPE_GZYD_HJHY_TYHD;
//                break;
            case BizConstant.BIZ_TYPE_GZYD_SPCL_CXTH:
                bizType = BizConstant.BIZ_TYPE_GZYD_SPCL_CXTH;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_13:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_13;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_9:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_9;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_AHYD_VRBT:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_AHYD_VRBT;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_PROV_MIX1:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_PROV_MIX1;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_PROV_MIX2:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_PROV_MIX2;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_PROV_MIX3:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_PROV_MIX3;
                break;
            case MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE:
            case MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174:
                bizType = BizConstant.BIZ_TYPE_UNION_MEMBER;
                break;
            case BizConstant.BIZ_TYPE_CHANNEL_SDDX_SSHY:
                bizType = BizConstant.BIZ_TYPE_CHANNEL_SDDX_SSHY;
                break;
           /* case BizConstant.BIZ_TYPE_GZYD_LLB_RB1G:
                bizType = BizConstant.BIZ_TYPE_GZYD_LLB_RB1G;
                break;*/
                //case BizConstant.BIZ_TYPE_DX_VRBT:
            //    bizType = BizConstant.BIZ_TYPE_DX_VRBT;
            //    break;
            case BizConstant.BIZ_TYPE_CHANNEL_DGYD_LLB_CW:
            case BizConstant.BIZ_TYPE_CHANNEL_DGYD_LLB_CXXR:
            case BizConstant.BIZ_TYPE_CHANNEL_DGYD_LLB_KDX:
                bizType = BizConstant.BIZ_TYPE_DGYD_LLB;
                break;
            case BizConstant.BIZ_TYPE_JSYD_VRBT:
                bizType = BizConstant.BIZ_TYPE_JSYD_VRBT;
                break;
            case BizConstant.BIZ_TYPE_GZYD_BJHY_DZHD:
                bizType = BizConstant.BIZ_TYPE_GZYD_BJHY_DZHD;
                break;
            case BizConstant.BIZ_TYPE_GZYD_SXK_SJDTYHD:
                bizType = BizConstant.BIZ_TYPE_GZYD_SXK_SJDTYHD;
                break;
            case BizConstant.BIZ_CHANNEl_JXYD_LLB:
            case BizConstant.BIZ_CHANNEl_JXYD_VRBT:
            case BizConstant.BIZ_CHANNEl_JXYD_VRBT_20:
            case BizConstant.BIZ_CHANNEl_JXYD_VRBT_20_D:
            case BizConstant.BIZ_CHANNEl_JXYD_HHHY:
            case BizConstant.BIZ_CHANNEl_JXYD_YLSVHY:
            case BizConstant.BIZ_CHANNEl_JXYD_SCLHHY:
            case BizConstant.BIZ_CHANNEl_JXYD_FPLHHY:
            case BizConstant.BIZ_CHANNEl_JXYD_FPLHHY_YR:
            case BizConstant.BIZ_CHANNEl_JXYD_MSHYLLB:
            case BizConstant.BIZ_CHANNEl_JXYD_QYCSLLB:
            case BizConstant.BIZ_CHANNEl_JXYD_YL_HS:
                bizType = BizConstant.BIZ_TYPE_JXYD;
                break;
            case BizConstant.BIZ_TYPE_GZYD_QX20G_LLTB:
                bizType = BizConstant.BIZ_TYPE_GZYD_QX20G_LLTB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_YDYP_RHKJNB:
                bizType = BizConstant.BIZ_TYPE_GZYD_YDYP_RHKJNB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_VRBT_XSZS:
                bizType = BizConstant.BIZ_TYPE_GZYD_VRBT_XSZS;
                break;
            case BizConstant.BIZ_TYPE_GZYD_LLB_12Y20G:
                bizType = BizConstant.BIZ_TYPE_GZYD_LLB_12Y20G;
                break;
            case BizConstant.BIZ_TYPE_GZYD_BJHY_10GLLB:
                bizType = BizConstant.BIZ_TYPE_GZYD_BJHY_10GLLB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_PLUS_HYYB:
                bizType = BizConstant.BIZ_TYPE_GZYD_PLUS_HYYB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_SXX_HY:
                bizType = BizConstant.BIZ_TYPE_GZYD_SXX_HY;
                break;
            case BizConstant.BIZ_TYPE_GZYD_KY_DLB:
                bizType = BizConstant.BIZ_TYPE_GZYD_KY_DLB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_YDYP_HJHY:
                bizType = BizConstant.BIZ_TYPE_GZYD_YDYP_HJHY;
                break;
            case BizConstant.BIZ_TYPE_GZYD_ZSHY:
                bizType = BizConstant.BIZ_TYPE_GZYD_ZSHY;
                break;
            case BizConstant.BIZ_TYPE_GZYD_TNLY_LXBY:
                bizType = BizConstant.BIZ_TYPE_GZYD_TNLY_LXBY;
                break;
            case BizConstant.BIZ_TYPE_GZYD_TNLY_HYLB:
                bizType = BizConstant.BIZ_TYPE_GZYD_TNLY_HYLB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_ZSHY_SYTH:
                bizType = BizConstant.BIZ_TYPE_GZYD_ZSHY_SYTH;
                break;
            case BizConstant.BIZ_TYPE_GZYD_MGZQ_QQB:
                bizType = BizConstant.BIZ_TYPE_GZYD_MGZQ_QQB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_QXX_BJHY:
                bizType = BizConstant.BIZ_TYPE_GZYD_QXX_BJHY;
                break;
            case BizConstant.BIZ_TYPE_GZYD_QXX_PLUS:
                bizType = BizConstant.BIZ_TYPE_GZYD_QXX_PLUS;
                break;
            case BizConstant.BIZ_TYPE_GZYD_XSZS:
                bizType = BizConstant.BIZ_TYPE_GZYD_XSZS;
                break;
            case BizConstant.BIZ_TYPE_GZYD_KDX_ZSB:
                bizType = BizConstant.BIZ_TYPE_GZYD_KDX_ZSB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_QYJXB:
                bizType = BizConstant.BIZ_TYPE_GZYD_QYJXB;
                break;
            case BizConstant.BIZ_TYPE_GZYD_NSXK:
                bizType = BizConstant.BIZ_TYPE_GZYD_NSXK;
                break;
            case BizConstant.BIZ_TYPE_GZYD_TSQY:
                bizType = BizConstant.BIZ_TYPE_GZYD_TSQY;
                break;
            case BizConstant.BIZ_TYPE_GZGJ_PLUS_HYYB:
                bizType = BizConstant.BIZ_TYPE_GZGJ_PLUS_HYYB;
                break;
            case BizConstant.BIZ_TYPE_GZGJ_VRBT:
                bizType = BizConstant.BIZ_TYPE_GZGJ_VRBT;
                break;
            case BizConstant.BIZ_TYPE_GZGJ_LLB_12Y20G:
                bizType = BizConstant.BIZ_TYPE_GZGJ_LLB_12Y20G;
                break;
            case BizConstant.BIZ_TYPE_GZGJ_QXX_BJHY:
                bizType = BizConstant.BIZ_TYPE_GZGJ_QXX_BJHY;
                break;
            case BizConstant.BIZ_CHANNEl_XJYD_5GTHBB:
            case BizConstant.BIZ_CHANNEl_XJYD_5GYXB:
            case BizConstant.BIZ_CHANNEl_XJYD_XYHMINI:
            case BizConstant.BIZ_CHANNEl_XJYD_XYHPLUS:
            case BizConstant.BIZ_CHANNEl_XJYD_XYHMAX:
            case BizConstant.BIZ_CHANNEl_XJYD_NBACWB:
            case BizConstant.BIZ_CHANNEl_XJYD_XSVRBT:
            case BizConstant.BIZ_CHANNEl_XJYD_RWXGJZB:
            case BizConstant.BIZ_CHANNEl_XJYD_SPCLTY:
            case BizConstant.BIZ_CHANNEl_XJYD_LLRB:
                bizType = BizConstant.BIZ_TYPE_XJYD;
                break;
            case BizConstant.BIZ_CHANNEl_VR_JM_10:
            case BizConstant.BIZ_CHANNEl_VR_JM_25:
                bizType = BizConstant.BIZ_TYPE_MGHY;
                break;
            case BizConstant.BIZ_TYPE_WANGYIYUN_MM:
                bizType = BizConstant.BIZ_TYPE_WANGYIYUN_MM;
                break;
            case BizConstant.BIZ_CHANNEl_HETU:
            case BizConstant.BIZ_CHANNEl_HETU_GZ:
            case BizConstant.BIZ_CHANNEl_HETU_HB:
            case BizConstant.BIZ_CHANNEl_HETU_HENAN:
            case BizConstant.BIZ_CHANNEl_HYQY_YR:
            case BizConstant.BIZ_CHANNEl_HYQY_YR_20:
            case BizConstant.BIZ_CHANNEl_HYQY_YR_20_912:
            case BizConstant.BIZ_CHANNEl_HYQY_HTJB20_924:
            case BizConstant.BIZ_CHANNEl_HYQY_HTJB20_925:
            case BizConstant.BIZ_CHANNEl_HYQY_HTJB20_926:
            case BizConstant.BIZ_CHANNEl_HYQY_YR_30:
                bizType = BizConstant.BIZ_TYPE_HETU;
                break;
            case BizConstant.BIZ_CHANNEl_HYQY_JUNBO:
            case BizConstant.BIZ_CHANNEl_HYQY_JUNBO_20:
                bizType = BizConstant.BIZ_TYPE_CFDTW;
                break;
            case BizConstant.BIZ_CHANNEl_HYQD_MAIHE:
            case BizConstant.BIZ_CHANNEl_HYQD_HS:
            case BizConstant.BIZ_CHANNEl_HYQD_O_HS:
                bizType = BizConstant.BIZ_TYPE_HYQD;
                break;
            case BizConstant.BIZ_CHANNEl_HYQY_ZUOYUE:
            case BizConstant.BIZ_CHANNEl_DYXC_HB:
                bizType = BizConstant.BIZ_TYPE_DYXC;
                break;
            case BizConstant.BIZ_CHANNEL_KYCJHY:
                bizType = BizConstant.BIZ_TYPE_KYCJHY;
                break;
            case BizConstant.BIZ_CHANNEL_AHTS_HNYD:
                bizType = BizConstant.BIZ_TYPE_AHTS_HNYD;
                break;
            case BizConstant.BIZ_CHANNEl_GDDX_VRBT:
                bizType = BizConstant.BIZ_TYPE_GDDX_VRBT;
                break;
            case BizConstant.BIZ_CHANNEl_JUNBO_LLB:
                bizType = BizConstant.BIZ_TYPE_JUNBO_LLB;
                break;
            case BizConstant.BIZ_CHANNEL_YR_GSYD_DYKSLLB:
            case BizConstant.BIZ_CHANNEL_YR_GSYD_MXLDC:
                bizType = BizConstant.BIZ_TYPE_YR_GSYD;
                break;
            case BizConstant.BIZ_TYPE_HN_VRBT_DY_LLB:
                bizType = BizConstant.BIZ_TYPE_HN_VRBT_DY_LLB;
                break;
            case BizConstant.BIZ_TYPE_HN_QWZJ_LLB:
                bizType = BizConstant.BIZ_TYPE_HN_QWZJ_LLB;
                break;
            case BizConstant.BIZ_TYPE_HN_DGDD_MGB:
                bizType = BizConstant.BIZ_TYPE_HN_DGDD_MGB;
                break;
            case BizConstant.BIZ_TYPE_HN_DGDD_DJYB:
                bizType = BizConstant.BIZ_TYPE_HN_DGDD_DJYB;
                break;
            case BizConstant.BIZ_TYPE_HN_5GLL_QYYX:
                bizType = BizConstant.BIZ_TYPE_HN_5GLL_QYYX;
                break;
            case BizConstant.BIZ_TYPE_HN_5GTH_HYLLB:
                bizType = BizConstant.BIZ_TYPE_HN_5GTH_HYLLB;
                break;
            case BizConstant.BIZ_TYPE_HN_MGTQB_XSZS:
                bizType = BizConstant.BIZ_TYPE_HN_MGTQB_XSZS;
                break;
            case BizConstant.BIZ_TYPE_HN_HJMG_FYB:
                bizType = BizConstant.BIZ_TYPE_HN_HJMG_FYB;
                break;
            case BizConstant.BIZ_TYPE_HN_NBA_TQLLB:
                bizType = BizConstant.BIZ_TYPE_HN_NBA_TQLLB;
                break;
            case BizConstant.BIZ_TYPE_HN_5GTH_LLB:
                bizType = BizConstant.BIZ_TYPE_HN_5GTH_LLB;
                break;
            case BizConstant.BIZ_TYPE_HN_MGSJB:
                bizType = BizConstant.BIZ_TYPE_HN_MGSJB;
                break;
            case BizConstant.BIZ_TYPE_HN_SXK_XSZS:
                bizType = BizConstant.BIZ_TYPE_HN_SXK_XSZS;
                break;
            case BizConstant.BIZ_TYPE_HNYZ_VRBT_SXX:
                bizType = BizConstant.BIZ_TYPE_HNYZ_VRBT_SXX;
                break;
            case BizConstant.BIZ_TYPE_HNYZ_SPMS_ZHB:
                bizType = BizConstant.BIZ_TYPE_HNYZ_SPMS_ZHB;
                break;
            case BizConstant.BIZ_TYPE_HNYZ_SHFW_HYB:
                bizType = BizConstant.BIZ_TYPE_HNYZ_SHFW_HYB;
                break;
            case BizConstant.BIZ_TYPE_HNYZ_SXX:
                bizType = BizConstant.BIZ_TYPE_HNYZ_SXX;
                break;
            case BizConstant.BIZ_TYPE_HNYZ_MGYD_DJJP:
                bizType = BizConstant.BIZ_TYPE_HNYZ_MGYD_DJJP;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_HYB:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_HYB;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_SXX:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_SXX;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_MGB:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_MGB;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_40GLLB:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_40GLLB;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_ZSHY:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_ZSHY;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_5GLLB:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_5GLLB;
                break;
            case BIZ_CHANNEL_XIJIU_JIANGXI_VRBT:
                bizType = BizConstant.BIZ_TYPE_XIJIU_JX_VRBT;
                break;
            case BIZ_CHANNEL_ZYHL_QY:
                bizType = BizConstant.BIZ_TYPE_OUTSIDE_QY;
                break;
            case BIZ_CHANNEl_HEYU_VRBT:
                bizType = BizConstant.BIZ_TYPE_HEYU_VRBT;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_5GXTH:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_5GXTH;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_DJB:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_DJB;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_DYLLB:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_DYLLB;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_50GLLB:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_50GLLB;
                break;
            case BizConstant.BIZ_TYPE_HENYD_YR_HDDY:
                bizType = BizConstant.BIZ_TYPE_HENYD_YR_HDDY;
                break;
            case BizConstant.BIZ_TYPE_HNYZ_DYLLB:
                bizType = BizConstant.BIZ_TYPE_HNYZ_DYLLB;
                break;
            case BizConstant.BIZ_TYPE_HNYZ_50GLLB:
                bizType = BizConstant.BIZ_TYPE_HNYZ_50GLLB;
                break;
            case BIZ_CHANNEl_GZDX:
            case BIZ_CHANNEl_GZDX_04:
            case BIZ_CHANNEl_GZDX_05:
            case BIZ_CHANNEl_GZDX_06:
                bizType = BizConstant.BIZ_TYPE_GZDX;
                break;
            case BizConstant.BIZ_CHANNEL_AIDOU_GZYD_20:
                bizType = BizConstant.BIZ_TYPE_AIDOU_GZYD;
                break;
            case BizConstant.BIZ_CHANNEL_SHOUJIZIXUN_YD_20:
            case BizConstant.BIZ_CHANNEL_SHOUJIZIXUN_YD_15:
            case BizConstant.BIZ_CHANNEL_HEJINFANPIAO_YD_20:
            case BizConstant.BIZ_CHANNEL_HEJINSHANGCHAO_YD_20:
            case BizConstant.BIZ_CHANNEL_YEDAOFANPIAO_YD_19:
            case BizConstant.BIZ_CHANNEL_SHANGHAICHIHUOZUANKA_YD_20:
            case BizConstant.BIZ_CHANNEL_CHAOSHIYINPIN_YD_25:
            case BizConstant.BIZ_CHANNEL_JM_DX_10:
            case BizConstant.BIZ_CHANNEL_WM_DX_10:
            case BizConstant.BIZ_CHANNEL_CQGW_YD_25:
            case BizConstant.BIZ_CHANNEL_SCYLCW_YD_19:
            case BizConstant.BIZ_CHANNEL_HNLLB_YD_19:
            case BizConstant.BIZ_CHANNEL_GZJYB_YD_20:
            case BizConstant.BIZ_CHANNEL_HNYX_DX_20:
            case BizConstant.BIZ_CHANNEL_CNL_YD_15:
            case BizConstant.BIZ_CHANNEL_SPCL_LT_15:
            case BizConstant.BIZ_CHANNEL_WYD_LT_20:
            case BizConstant.BIZ_CHANNEL_SPCL_YD_10:
            case BizConstant.BIZ_CHANNEL_SPCL_YD_6:
            case BizConstant.BIZ_CHANNEL_SPCL_YD_9:
            case BizConstant.BIZ_CHANNEL_BJ_DX_19:
            case BizConstant.BIZ_CHANNEL_YHY_DX_20:
            case BizConstant.BIZ_CHANNEL_HJX_YD_15:
            case BizConstant.BIZ_CHANNEL_SPCL_LT_25:
            case BizConstant.BIZ_CHANNEL_GSFP_YD_19:
            case BizConstant.BIZ_CHANNEL_GSDF_YD_19:
            case BizConstant.BIZ_CHANNEL_MDL_YD_19:
            case BizConstant.BIZ_CHANNEL_RXKF_YD_19:
                bizType = BizConstant.BIZ_TYPE_SHOUJIZIXUN_YD;
                break;
            case BizConstant.BIZ_CHANNEL_BEIJINGYIHUI_DIANXIN:
            case BizConstant.BIZ_CHANNEL_BEIJINGYIHUI_LIANTONG:
            case BizConstant.BIZ_CHANNEL_BEIJINGYIHUI_SHANDONGYIDONG:
            case BizConstant.BIZ_CHANNEL_BEIJINGYIHUI_YIDONG:
            case BizConstant.BIZ_CHANNEL_BEIJINGYIHUI_SHANGHAIYIDONG:
                bizType = BizConstant.BIZ_TYPE_BEIJINGYIHUI;
                break;
            case BizConstant.BIZ_CHANNEL_HNYD_YDSXX_PLUS:
            case BizConstant.BIZ_CHANNEL_HNYD_SPCL_XSZS:
            case BizConstant.BIZ_CHANNEL_HNYD_SPCL_KDXZS:
                bizType = BizConstant.BIZ_TYPE_YSM_HNYD;
                break;
            case BizConstant.BIZ_CHANNEL_CX_HYTX:
            case BizConstant.BIZ_CHANNEL_CX_DJSJ:
            case BizConstant.BIZ_CHANNEL_CX_CLJP:
            case BizConstant.BIZ_CHANNEL_CX_DMKJ:
                bizType = BizConstant.BIZ_TYPE_CAIXUN;
                break;
            case BizConstant.BIZ_CHANNEL_LH_HJX:
                bizType = BizConstant.BIZ_TYPE_LH_HJX;
                break;
            case BizConstant.BIZ_CHANNEL_GDYD_STOCK:
            case BizConstant.BIZ_CHANNEL_GDYD_LLB:
            case BizConstant.BIZ_CHANNEL_GDYD_XFJ:
                bizType = BizConstant.BIZ_TYPE_GUANGDONG;
                break;
            case BizConstant.BIZ_CHANNEL_XZ_XUANSHI:
                bizType = BizConstant.BIZ_TYPE_XIZANG;
                break;
            case BizConstant.BIZ_CHANNEL_FENGZHUSHOU_SCYD_PAJKB:
                bizType = BizConstant.BIZ_TYPE_FZS;
                break;
            case BizConstant.BIZ_CHANNEL_TY_COMM:
            case BizConstant.BIZ_CHANNEL_EXCLUSIVE_CARD:
            case BizConstant.BIZ_CHANNEL_AI_VRBT:
            case BizConstant.BIZ_CHANNEL_AI_VRBT_NEW:
                bizType = BizConstant.BIZ_TYPE_TY;
                break;
            case BizConstant.BIZ_CHANNEL_KA_SAI_STOCK_YD:
            case BizConstant.BIZ_CHANNEL_KA_SAI_STOCK_LT:
                bizType = BizConstant.BIZ_TYPE_KA_SAI_STOCK;
                break;
            case BizConstant.BIZ_CHANNEL_STP_JSYD_STOCK:
            case BizConstant.BIZ_CHANNEl_STP_JXYD_STOCK:
                bizType = BizConstant.BIZ_TYPE_STP;
                break;
            case BizConstant.BIZ_CHANNEL_SDXC_LT_VRBT:
            case BizConstant.BIZ_CHANNEL_SDXC_YD_VRBT:
                bizType = BizConstant.BIZ_TYPE_SDXC;
                break;
            case BizConstant.BIZ_CHANNEL_CQYD_QYCS:
                bizType = BizConstant.BIZ_TYPE_CQYD_QYCS;
                break;
            case BizConstant.BIZ_CHANNEL_CALL_SHOW:
            case BizConstant.BIZ_CHANNEL_CCTV_NEWS:
            case BizConstant.BIZ_CHANNEL_LEXIANG_VIP:
            case BizConstant.BIZ_CHANNEL_XT_YUNSHOUHU:
            case BizConstant.BIZ_CHANNEL_XT_BOJINVIP:
            case BizConstant.BIZ_CHANNEL_XT_JIAYOUBAO:
            case BizConstant.BIZ_CHANNEL_XT_DX_YUEHUIYUAN:
            case BizConstant.BIZ_CHANNEL_XT_LT_CHANGYOU:
                bizType = BizConstant.BIZ_TYPE_XUTONG_YD;
                break;
            case BizConstant.BIZ_CHANNEL_RUANYOUTONG_LT:
            case BizConstant.BIZ_CHANNEL_RUANYOUTONG_YD:
            case BizConstant.BIZ_CHANNEL_RYT_LT_JIAYOU:
            case BizConstant.BIZ_CHANNEL_RUANYOUTONG_DX:
            case BizConstant.BIZ_CHANNEL_RYT_LT_GECAI:
            case BizConstant.BIZ_CHANNEL_RYT_YD_XIANYU:
            case BizConstant.BIZ_CHANNEL_RYT_YD_QCYYB:
            case BizConstant.BIZ_CHANNEL_RYT_YD_SRQY:
            case BizConstant.BIZ_CHANNEL_RYT_YD_YSYDH:
            case BizConstant.BIZ_CHANNEL_RYT_YD_HJX:
            case BizConstant.BIZ_CHANNEL_RYT_YD_CHIHUO:
                bizType = BizConstant.BIZ_TYPE_RUANYOUTONG;
                break;
            case BizConstant.BIZ_TYPE_HUBYD_CZHY_QXB:
                bizType = BizConstant.BIZ_TYPE_HUBYD_CZHY_QXB;
                break;
            case BizConstant.BIZ_TYPE_HUBYD_CZHY:
                bizType = BizConstant.BIZ_TYPE_HUBYD_CZHY;
                break;
            case BizConstant.BIZ_CHANNEL_DX_CLOUDGAME:
                bizType = BizConstant.BIZ_CHANNEL_DX_CLOUDGAME;
                break;
            case BizConstant.BIZ_CHANNEL_YN_BJHY_YR:
            case BizConstant.BIZ_CHANNEL_YN_BJHY_HS:
            case BizConstant.BIZ_CHANNEL_YN_SZSH_HS:
            case BizConstant.BIZ_CHANNEL_YN_BJHY_GJ:
                bizType = BizConstant.BIZ_TYPE_YNYD;
                break;
            case BizConstant.BIZ_CHANNEL_SPCL_KDXLXB:
            case BizConstant.BIZ_CHANNEL_SPCL_XSHXB:
            case BizConstant.BIZ_CHANNEL_SPCL_XSLXB:
            case BizConstant.BIZ_CHANNEL_SPCL_XSYXB:
                bizType = BizConstant.BIZ_TYPE_CQYD_YSM;
                break;
            case BizConstant.BIZ_CHANNEL_SHYD_CUN_LIANG:
                bizType = BizConstant.BIZ_TYPE_JUNBO_SHYD;
                break;
            case BizConstant.BIZ_CHANNEL_HETU_LN:
            case BizConstant.BIZ_CHANNEL_HETU_CQ:
                bizType = BizConstant.BIZ_TYPE_HETU_FENSHENG;
                break;
            case BizConstant.BIZ_CHANNEL_AYY_AISPCL_HS:
                bizType = BizConstant.BIZ_TYPE_DX_AIVRBT;
                break;
            case BizConstant.BIZ_CHANNEL_GZTN_JB:
            case BizConstant.BIZ_CHANNEL_GZSXK_JB:
                bizType = BizConstant.BIZ_TYPE_JUNBOLLB;
                break;
            case BizConstant.CHANNEL_ID_J6:
            case BizConstant.CHANNEL_ID_J7:
                bizType = BizConstant.BIZ_TYPE_VRBT;
                break;

        }
        return bizType;
    }

    public static boolean isMiguMusicChannel(String channelCode){

        return StringUtils.startsWithAny(channelCode, MiguApiService.CENTRALITY_CHANNEL_CODE_PREFFIX, MiguApiService.OPEN_CHANNEL_CODE_PREFFIX);
    }

    /**
     *  是否为白金会员+视频彩铃组合包渠道号
     */
    public static boolean isBjhyZhbChannel(String channelCode) {
        return ImmutableMultiset.of(
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W6,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W7,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XE,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XF,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XG,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XB,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XC,
                MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XD
                ).contains(channelCode);
    }

    /**
     *  是否为高姐帮忙破解视频彩铃(这些渠道号我们自己不推广,只是帮忙破解,不随机设置铃音)
     */
    public static boolean isVrbtGaojiePassthroughChannel(String channelCode) {
        return ImmutableMultiset.of("014X056","014X057","014X058","014X059","014X09J").contains(channelCode);
    }

    //咪咕渠道订阅成功
    public static final String MIGU_RES_CODE_SUCCEED = "000000";
    /**
     * 领取月份限制
     */
    public static final Integer MONTHLY_LIMIT = 3;

    public static final String IS_NOT_SOAP = "isNotSoap";

    public static final String BEGIN_TIME = "2021-09-01 00:00:00";


    /**
     * 上海移动serviceId
     */
    public static final String BIZ_SHANGHAI_MOBILE_SERVICE_ID = "111000788275";

    /**
     * 四川移动serviceId
     */
    public static final String BIZ_SICHUAN_MOBILE_SERVICE_ID = "111000788276";


    /**
     * 联通serviceId
     */
    public static final String BIZ_LT_SERVICE_ID = "4900720600";

    /**
     * 联通瑞金serviceId
     */
    public static final String BIZ_LT_RJ_SERVICE_ID = "4900721500";

    /**
     * 联通乐擎serviceId
     */
    public static final String BIZ_LT_BLH_SERVICE_ID = "4900731500";


    /**
     * 联通睿梅serviceId
     */
    public static final String BIZ_LT_RM_SERVICE_ID = "4900724800";


    /**
     * 联通鸿盛渠道号
     */
    public static final String BIZ_LT_CHANNEL_HONGSHENG = "hongsheng";

    /**
     * 联通鸿盛渠道号(三方支付)
     */
    public static final String BIZ_LT_CHANNEL_HONGSHENG_SFZF = "hongsheng_sfzf";

    /**
     * 联通瑞金渠道号
     */
    public static final String BIZ_LT_CHANNEL_RUIJIN = "ruijin";

    /**
     * 联通乐擎天渠道号(贝乐虎)
     */
    public static final String BIZ_LT_CHANNEL_LEQING = "leqing";


    /**
     * 联通睿梅渠道号
     */
    public static final String BIZ_LT_CHANNEL_RUIMEI = "ruimei";


    public static final String AES_KEY_FOR_DOUYIN_VRBT = "516c1a91116b133a74b8d0ac88038c90";


    /**
     * 电信麦禾10元渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_MAIHE = "maihe";

    /**
     * 电信麦禾渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_MAIHE_6 = "maihe_6";

    /**
     * 电信人民视讯渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_RMSX = "rmsx";

    /**
     * 四川移动yrjy渠道号
     */
    public static final String BIZ_SCMCC_CHANNEL_YRJY = "yrjy";
    /**
     * 四川移动骏伯渠道号
     */
    public static final String BIZ_SCMCC_CHANNEL_JUNBO = "junbo";
    /**
     * 四川移动kuaima渠道号
     */
    public static final String BIZ_SCMCC_CHANNEL_KUAIMA = "kuaima";

    /**
     * 四川移动kuaima小程序渠道号
     */
    public static final String BIZ_SCMCC_CHANNEL_KUAIMA_MINIAPP = "kuaima_xcx";

    /**
     * 四川移动lbtx渠道号
     */
    public static final String BIZ_SCMCC_CHANNEL_LBTX = "lbtx";

    /**
     * 四川移动kuaima小程序30元流量包
     */
    public static final String BIZ_KUAIMAMINIAPP_LLB_30 = "PIT_KM5GLLYB30YY000";
    /**
     * 四川移动kuaima小程序铂金会员
     */
    public static final String BIZ_KUAIMAMINIAPP_BJHY = "PIT_KMBJYB15YY000";


    /**
     * 河北移动xzj渠道号
     */
    public static final String BIZ_HCMCC_CHANNEL_XZJ = "XZJ";
    /**
     * 河北移动yd渠道号
     */
    public static final String BIZ_HCMCC_CHANNEL_YD = "YD";

    /**
     * 权益商城充值充值
     */
    public static final String BIZ_DO_CHANNEL_RECHARGE = "digitalRights";



    /**
     * 电信光明网渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_GUANGMINGWANG = "guangmingwang";

    /**
     * 电信环球网渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_HUANQIUWANG = "huanqiuwang";

    /**
     * 电信鸿盛天极渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_HSTJ = "hstj";

    /**
     * 电信鸿盛天极渠道号(三方支付)
     */
    public static final String BIZ_DIANXIN_CHANNEL_MAIHE_SFZF = "maihe_sfzf";


    /**
     * 电信麦禾ai渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_MAIHE_AI = "DX_VRBT_MH_AI";

    /**
     * 电信默认渠道号
     */
    public static final String BIZ_DIANXIN_CHANNEL_DEFAULT = BIZ_DIANXIN_CHANNEL_GUANGMINGWANG;


    /**
     * 联通默认渠道号
     */
    public static final String BIZ_LT_CHANNEL_DEFAULT = BIZ_LT_CHANNEL_HONGSHENG;

    //四川电信15元流量包 ecpid
    public static final String OFFERCOMBID_15 = "166412";

    //四川电信25元流量包 ecpid
    public static final String OFFERCOMBID_25 = "166411";
    /**
     * 电信鸿盛-AI视频彩铃智玩包渠道号
     */
    public static final String BIZ_TYPE_DX_AIVRBT = "DX_AIVRBT";
    //甘肃移动
    public static final String BIZ_TYPE_GANSU_MOBILE = "GANSU_MOBILE";
    public static final String BIZ_CHANNEL_AYY_AISPCL_HS = "AYY_AISPCL_HS";
    public static final String BIZ_CHANNEL_AYY_ZXLL_HS = "AYY_ZXLL_HS";
    public static final String BIZ_CHANNEL_AYY_ZX_HS = "AYY_ZX_HS";
    //四川电信流量包 ecpid bizType map
    public static Map<String,String> ecpIdBizTypeMap = ImmutableMap.of(
            BIZ_TYPE_SYDX_VRBT_LLB_15, OFFERCOMBID_15,
            BIZ_TYPE_SYDX_VRBT_LLB_25, OFFERCOMBID_25
    );


    /**
     * 联通上报渠道集合
     *
     */

    public static List UNICOM_UP_CHANNEL_LIST = new ArrayList<>(Arrays.asList("A16xJL5", "A16xJL6", "A17xJL11", "A17xJL12", "A5xJL21",
            "A18xJL3", "A17xJL15", "A17xJL16", "A17xJL17", "A17xJL13", "A17xJL14"));

//    public static void main(String[] args) {
//        String bizTypeByMiguChannel = getBizTypeByMiguChannel("014X04M");
//        System.out.println("bizTypeByMiguChannel = " + bizTypeByMiguChannel);
//    }

    //域名正则
    public static final String DOMAIN_REG = "[\\w]+.[\\w]+.(com|cn|net|org|biz|info|cc|tv|top|vip)";
    //nginx目录
    public static final String NGINX_DIR = "/data/nginx/";
    //nginx源配置文件
    public static final String NGINX_SOURCE_CONF_PATH = "/data/nginx/conf/conf.d/ssl_crbt.cdyrjygs.com.conf";

    //nginx配置文件内容
    public static final String SERVER_NAME = "server_name crbt.cdyrjygs.com";
    public static final String KEY = "4402898_crbt.cdyrjygs.com.key";
    public static final String PEM = "4402898_crbt.cdyrjygs.com.pem";

    public static final String RIGHTS_MSG_NOT_MEDIA_MEMBER = "暂无视听会员权益！";
    public static final String RIGHTS_MSG_NOT_SERVICE_PACK = "暂无业务包！";
    public static final String RIGHTS_MSG_NOT_ORDER = "暂无订购权限！";
    /**
     * 卡片类型，-1=暂无权益，0=月卡和周卡，1=月卡，2=周卡
     */
    public static final String NOT_RIGHTS= "-1";
    public static final String MONTH_CARD_AND_WEEK_CARD= "0";
    public static final String MONTH_CARD= "1";
    public static final String WEEK_CARD= "2";
    public static final String NOT_MEMBER_MSG= "你还不是会员，暂无权益领取";


    //白金会员订单状态 缓存时间 30分钟
    public static final long BJHY_ORDER_STATE = 1800L;

    //白金会员渠道号5元
    public static final String BJHY_CHANNEL_CODE_QX = "00210QX";
    //白金会员渠道号10元
    public static final String BJHY_CHANNEL_CODE_PP = "00210PP";

    //白金会员领取次数
    public static final Integer BJHY_CHANNEL_COUNT = 2;


    public static final String BJHY_RIGHTS_CHARGE_MSG   = "会员权益已经达到领取上限";

    public static final String NOT_RIGHTS_MSG= "暂无可以领取的权益！";


    //乐摩吧子渠道号
    public static final String SUB_CHANNEL_LEMOBA = "lemoba";

    //渠道全部699052、订阅佐悦638799、高姐699297   川网 690116, 科大讯飞-泾县699295 6009260000027(部分没给版权id) 祺音699212 118798/视彩号(数智人)  一语成片600050T
    public static final String[] LEGAL_VRBT_CP_ID_OR_PRODUCT_ID = {"699052","638799","699297", "690116", "699295", "699212", "118798", "600050T", "600000","60092600000"};
    public static boolean isLegalVrbtCopyrightId(String vrbtCopyrightId){
        if(StringUtils.isEmpty(vrbtCopyrightId)) {
            return false;
        }
        return Arrays.stream(LEGAL_VRBT_CP_ID_OR_PRODUCT_ID).anyMatch(vrbtCopyrightId::startsWith);
    }

    //需要走省份配置的渠道
    public static final List<String> CHANNEL_PROVINCE_CONFIG_LIST = Arrays.asList("00210QY", "00210QZ","014X05A");

    //需要破解开通渠道集合
    public static final List<String> CRACK_CHANNEL_LIST = Arrays.asList("014X04F", "014X05A", "00210QZ","00210QY","00210QQ","00210QR","014X09P","014X04E","014X02D");

    //不需要发送PPTV渠道
    public static final List<String> NOT_SEND_PPTV_CHANNEL_LIST = Arrays.asList("00210QZ","00210QY","");

    //彩铃运营中心订阅包
    public static final List<String> CAILING_CHANNEL_LIST = Lists.newArrayList("014X04C", "014X04D", "014X04E", "014X04F", "014X09P", "014X04G");

    public static void main(String[] args) {
        //String bizTypeByMiguChannel = getBizTypeByMiguChannel("014X04F");
        //System.out.println("bizTypeByMiguChannel = " + bizTypeByMiguChannel);
        //
        //System.out.println(Pattern.matches(BizConstant.DOMAIN_REG, "a.miguring.com"));
        //System.out.println(isLegalVrbtCopyrightId(""));
        //System.out.println(isLegalVrbtCopyrightId("699052T9F2Q"));
        //System.out.println(isLegalVrbtCopyrightId("638799T4366"));   .

        //System.out.println(StringUtils.containsAny("JXYD_AB", "JF"));
        //
        //System.out.println(isMiguMusicChannel(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0));
        //System.out.println(isMiguMusicChannel(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XB));
        System.out.println(XUANSHI_RPOVINCE_CHANNEL_LIST.contains("GZYD_XSZS"));


    }
    //电信渠道集合
    public static final List<String> DIANXIN_CHANNEL_LIST = Arrays.asList(BIZ_DIANXIN_CHANNEL_HSTJ, BIZ_DIANXIN_CHANNEL_GUANGMINGWANG);


    //电信渠道集合
    public static final List<String> LIANTONG_CHANNEL_CRACK_LIST = Arrays.asList(BIZ_LT_CHANNEL_RUIMEI, BIZ_LT_CHANNEL_RUIJIN, BIZ_LT_CHANNEL_HONGSHENG);


    //公众号发送短信渠道号
    public static final String BIZ_PUBLIC_CHANNEL_CODE = "AXXJS";

    //省份-重庆
    public static String CHONGQING_PROVINCE = "重庆";

    /**充值方式:0=直充,1=券码*/
    public static final Integer RECHARGE_STATE_CODE=1;

    /**激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中,5=已过期*/
    public static final Integer NOT_USE=0;
    public static final Integer IS_USE=1;
    public static final Integer IS_EXPIRED=2;
    public static final Integer RECHARGE_FAIL=3;
    public static final Integer RECHARGE_WAIT=4;
    public static final Integer ALREADY_EXPIRED=5;
    public static final Integer ALREADY_TIME=6;
    public static final List<String> PRODUCT_ID_LIST = Lists.newArrayList("6724","6725","6722","6723");
    //    final List<String> PRODUCT_ID_LIST = Lists.newArrayList("6246","6387","6247","6241","6250");
    //权益来源 1 公众号 2 抖音
    public static final String SOURSE_PUBLIC= "1";
    public static final String SOURSE_DOUYIN= "2";

    //否开启抖音小程序测试开关 0 关闭 1 开启
    public static final String DOUYIN_TEST_SWITCHS_CLOSE = "0";
    public static final String DOUYIN_TEST_SWITCHS_OPEN = "1";

    //否开启话费充值开关 0 关闭 1 开启
    public static final String MOBILE_FEE_CHARGE_SWITCHS_CLOSE = "0";
    public static final String MOBILE_FEE_CHARGE_SWITCHS_OPEN = "1";

    //否开启抖pptv权益短信开关 0 关闭 1 开启
    public static final String PPTV_TEST_SWITCHS_CLOSE = "0";
    public static final String PPTV_TEST_SWITCHS_OPEN = "1";

    //抖音渠道号
    public static final String DOUYING_CHANNEL_ID = "douyin";
    //抖音渠道密钥
    public static final String DOUYING_CHANNEL_PASSWORD = "5SFdlnvJvoTMHGKXrKqdTIFKhZgI6Ro0";

    //视频彩铃包月充值渠道号
    public static final String RECHARGE_VRBT_CHANNEL_ID="014X0A3";
    //白金会员充值渠道号
    public static final String RECHARGE_BJHY_CHANNEL_ID ="00210T9";

    //白金会员产品ID
    public static final String RECHARGE_COUPON_ID="mgyybjhy";
    //酷狗VIP月卡直充产品ID
    public static final String KUGOU_COUPON_ID="kgykzhichong";
    //咪咕视频彩铃直充产品ID
    public static final String VRBT_COUPON_ID="mgyyvrbt";
    //微信代金券产品ID
    public static final String WCDJQ_COUPON_ID="wechatdjq";
    //咪咕订阅号（月包）-成都畅想渠道号
    public static final String CHANNEL_ID_J6="014X0J6";
    //咪咕订阅号（月包）-成都创响渠道号
    public static final String CHANNEL_ID_J7 ="014X0J7";
    //黑龙江城市代码集合
    public static Map<String, String> HLJ_CITY_CODE_LIST = new ImmutableMap.Builder<String, String>()
            .put("佳木斯", "JMS")
            .put("齐齐哈尔", "QQHE")
            .put("鹤岗", "HG")
            .put("伊春", "YC")
            .put("黑河", "HH")
            .put("双鸭山", "SYS")
            .put("七台河", "QTH")
            .put("绥化", "SH")
            .put("大庆", "DQ")
            .put("鸡西", "JX")
            .put("哈尔滨", "HEB")
            .put("牡丹江", "MDJ")
            .put("大兴安岭", "DXAL").build();


    /**
     * 判断是否为本地业务类型
     *
     * @param bizType
     * @return
     */
    public static boolean isLocalBizType(String bizType) {
        boolean isLocalBizType = false;
        if(bizType.contains("SCYD")){
            return true;
        }
        switch (bizType) {
            case BIZ_TYPE_BJHY:
            case BIZ_TYPE_CPMB:
            case BIZ_TYPE_RT:
            case BIZ_TYPE_KM_LLB_30:
            case BIZ_TYPE_KM_BJHY:
            case BIZ_TYPE_SHANGHAI:
            case BIZ_TYPE_SYDX_VRBT_LLB_15:
            case BIZ_TYPE_SYDX_VRBT_LLB_25:
            case BIZ_TYPE_CQYD_VRBT_DX:
            case BIZ_TYPE_CQYD_5G_QYB:
            case BIZ_TYPE_HBYD_VRBT:
            case BIZ_TYPE_GANSU_LLB_30:
            case BIZ_TYPE_GANSU_LLNB_30:
            case BIZ_TYPE_GUANGXI_LLB_5:
            case BIZ_TYPE_XDCL:
            case BIZ_TYPE_HN_VRBT:
            case BIZ_TYPE_VRBT:
            case BIZ_TYPE_UNION_MEMBER:
            case BIZ_TYPE_QYCL:
            case BIZ_TYPE_WANGYIYUN_MM:
                isLocalBizType = true;
                break;
        }
        return isLocalBizType;
    }

    //电信可使用渠道号
    public static final List<String> DIANXIN_QUERY_CHANNEL_LIST = Arrays.asList(BIZ_DIANXIN_CHANNEL_HSTJ,BIZ_DIANXIN_CHANNEL_GUANGMINGWANG,BIZ_DIANXIN_CHANNEL_MAIHE);

    //佰付美联合会员
    public static final String BFM_CHANNEL = "00210R8";

    //业务类型-佰付美联合会员
    public static final String BIZ_TYPE_BFM = "BFM";

    //可动态打开关闭省份视频key
    public static final List<String> SWITCH_PROVICE_KEYS = Arrays.asList(CacheConstant.CMS_BIZ_SWITCH_HNYD,CacheConstant.CMS_BIZ_SWITCH_HNYD_QWZJ,CacheConstant.CMS_BIZ_SWITCH_HNYD_MGB,CacheConstant.CMS_BIZ_SWITCH_HLJYD,CacheConstant.CMS_VRBT_SWITCH_SCYD,CacheConstant.CMS_ZHB_SWITCH_SCYD,CacheConstant.CMS_VRBT_SWITCH_GDYD,CacheConstant.CMS_VRBT_SWITCH_JSYD,CacheConstant.CMS_BIZ_SWITCH_GZYD);

    //业务类型:0=主要,1=短信验证码,2=权益提醒短信,3=已领取权益短信,4=未领取权益短信,5=报备,6=破解,7=订购,8=续订,9=预警
    public static final String BUSINESS_TYPE_MAIN ="0";
    public static final String BUSINESS_TYPE_CODE ="1";
    public static final String BUSINESS_TYPE_RIGHTS ="2";
    public static final String BUSINESS_TYPE_YES_RIGHTS ="3";
    public static final String BUSINESS_TYPE_NO_RIGHTS ="4";
    public static final String BUSINESS_TYPE_OFFICIAL ="5";
    public static final String BUSINESS_TYPE_CRACK ="6";
    public static final String BUSINESS_TYPE_ORDER ="7";
    public static final String BUSINESS_TYPE_RENEW ="8";
    public static final String BUSINESS_TYPE_WARN ="9";
    //东莞移动流量包-业务ID
    public static final String BIZ_TYPE_DGYD_LLB= "DGYD_LLB"; //业务代码 5G特惠基础流量包（30元5G）
    //东莞移动流量包-畅玩流量包-渠道号
    public static final String BIZ_TYPE_CHANNEL_DGYD_LLB_CW= "DGYD_LLB_CW"; //东莞移动流量包-畅玩流量包-渠道号
    //东莞移动流量包-超炫XR流量包-渠道号
    public static final String BIZ_TYPE_CHANNEL_DGYD_LLB_CXXR= "DGYD_LLB_CXXR"; //东莞移动流量包-超炫XR流量包-渠道号
    //东莞移动流量包-酷电秀流量包-渠道号
    public static final String BIZ_TYPE_CHANNEL_DGYD_LLB_KDX= "DGYD_LLB_KDX"; //东莞移动流量包-酷电秀流量包-渠道号

    //东莞移动流量包-畅玩流量包
    public static final String DGYD_LLB_CW= "CW"; //东莞移动流量包-畅玩流量包
    //东莞移动流量包-超炫XR流量包
    public static final String DGYD_LLB_CXXR= "CXXR"; //东莞移动流量包-超炫XR流量包
    //东莞移动流量包-酷电秀流量包
    public static final String DGYD_LLB_KDX= "KDX"; //东莞移动流量包-酷电秀流量包

    //东莞移动流量包-订购
    public static final String DGYD_LLB_ORDER_TYPE_SUB= "1"; //东莞移动流量包-订购
    //东莞移动流量包-取消
    public static final String DGYD_LLB_ORDER_TYPE_CANCEL= "2"; //东莞移动流量包-取消
    //东莞移动流量包-修改
    public static final String DGYD_LLB_ORDER_TYPE_UPDATE= "3"; //东莞移动流量包-修改

    //业务类型-贵州移动基础流量包
    public static final String BIZ_TYPE_GZYD_LLB_JC5G= "GZYD_LLB_JC5G"; //业务代码 5G特惠基础流量包（30元5G）
    /*public static final String BIZ_TYPE_GZYD_LLB_RB1G= "GZYD_LLB_RB1G"; //业务代码 流量日包1GB版（3元）*/
    public static final String BIZ_TYPE_GZYD_SXK= "GZYD_SXK"; //业务代码 随心看
    public static final String BIZ_TYPE_GZYD_SXX= "GZYD_SXX"; //业务代码 随心选
//    public static final String BIZ_TYPE_GZYD_HJHY_TYHD= "GZYD_HJHY_TYHD"; //业务代码 0元黄金会员体验活动
    public static final String BIZ_TYPE_GZYD_SPCL_CXTH= "GZYD_SPCL_CXTH"; //业务代码 2020年视频彩铃畅享特惠活动
    public static final String BIZ_TYPE_GZYD_BJHY_DZHD= "GZYD_BJHY_DZHD"; //业务代码 铂金会员打折活动
    public static final String BIZ_TYPE_GZYD_SXK_SJDTYHD= "GZYD_SXK_SJDTYHD"; //业务代码 随心看首季度体验活动
    public static final String BIZ_TYPE_GZYD_QX20G_LLTB= "GZYD_QX20G_LLTB"; //业务代码 趣享20G流量套包
    public static final String BIZ_TYPE_GZYD_YDYP_RHKJNB= "GZYD_YDYP_RHKJNB"; //业务代码 移动云盘1000G融合空间年包
    public static final String BIZ_TYPE_GZYD_VRBT_XSZS= "GZYD_VRBT_XSZS"; //业务代码 视频彩铃订阅-炫视专属6元包(基础功能版)
    public static final String BIZ_TYPE_GZYD_LLB_12Y20G= "GZYD_LLB_12Y20G"; //业务代码 定向流量包12元20G
    public static final String BIZ_TYPE_GZYD_BJHY_10GLLB= "GZYD_BJHY_10GLLB"; //业务代码 铂金会员10G流量套包
    public static final String BIZ_TYPE_GZYD_PLUS_HYYB= "GZYD_PLUS_HYYB"; //业务代码 PLUS会员月包
    public static final String BIZ_TYPE_GZYD_SXX_HY= "GZYD_SXX_HY"; //业务代码 随心选会员
    public static final String BIZ_TYPE_GZYD_KY_DLB= "GZYD_KY_DLB"; //业务代码 快游大礼包
    public static final String BIZ_TYPE_GZYD_YDYP_HJHY= "GZYD_YDYP_HJHY"; //业务代码 移动云盘黄金会员
    public static final String BIZ_TYPE_GZYD_ZSHY= "GZYD_ZSHY"; //业务代码 咪咕视频钻石会员包月15元
    public static final String BIZ_TYPE_GZYD_TNLY_LXBY= "GZYD_TNLY_LXBY"; //业务代码 咪咕视频钻石会员包月15元
    public static final String BIZ_TYPE_GZYD_TNLY_HYLB= "GZYD_TNLY_HYLB"; //业务代码 途牛旅游会员礼包23元连续月包(NSB01)
    public static final String BIZ_TYPE_GZYD_ZSHY_SYTH= "GZYD_ZSHY_SYTH"; //业务代码 途牛旅游会员礼包23元连续月包(NSB01)
    public static final String BIZ_TYPE_GZYD_MGZQ_QQB= "GZYD_MGZQ_QQB"; //业务代码 咪咕足球全通包包月40元
    public static final String BIZ_TYPE_GZYD_QXX_BJHY= "GZYD_QXX_BJHY"; //业务代码 黔心选铂金会员
    public static final String BIZ_TYPE_GZYD_QXX_PLUS= "GZYD_QXX_PLUS"; //业务代码 黔心选PLUS会员
    public static final String BIZ_TYPE_GZYD_XSZS= "GZYD_XSZS"; //业务代码 炫视专属包
    public static final String BIZ_TYPE_GZYD_KDX_ZSB= "GZYD_KDX_ZSB"; //业务代码酷电秀专属包
    public static final String BIZ_TYPE_GZYD_QYJXB= "GZYD_QYJXB"; //业务代码权益嘉享包
    public static final String BIZ_TYPE_GZYD_NSXK= "GZYD_NSXK"; //业务代码新随心看
    public static final String BIZ_TYPE_GZYD_TSQY= "GZYD_TSQY"; //业务代码提速权益活动

    //贵州移动高姐
    public static final String BIZ_TYPE_GZGJ_PLUS_HYYB= "GZGJ_PLUS_HYYB"; //业务代码 PLUS会员月包
    public static final String BIZ_TYPE_GZGJ_VRBT= "GZGJ_VRBT"; //业务代码 贵州视频彩铃
    public static final String BIZ_TYPE_GZGJ_LLB_12Y20G= "GZGJ_LLB_12Y20G"; //业务代码 贵州12元20G流量
    public static final String BIZ_TYPE_GZGJ_QXX_BJHY = "GZGJ_QXX_BJHY"; //业务代码 黔心选铂金会员


    public static final String BIZ_CHANNEL_YR_GSYD_DYKSLLB= "YR_GSYD_DYKSLLB"; //悠然甘肃移动抖音快手流量包
    public static final String BIZ_CHANNEL_YR_GSYD_MXLDC = "YR_GSYD_MXLDC"; //5G新通话-明星来电一档明星C包
    public static final String BIZ_TYPE_YR_GSYD= "YR_GSYD"; //业务类型-悠然甘肃移动

    public static final String BIZ_TYPE_HN_VRBT_DY_LLB= "HN_VRBT_DY_LLB"; //湖南移动 视频彩铃订阅-炫视专属包抖音流量版 产品编码：99709850
    public static final String BIZ_TYPE_HN_QWZJ_LLB= "HN_QWZJ_LLB"; //湖南移动 全网追剧流量包 30元50GB 产品编码：99693751
    public static final String BIZ_TYPE_HN_DGDD_MGB= "HN_DGDD_MGB"; //湖南移动 动感地带·芒果包	22元/月 产品编码：99810202
    public static final String BIZ_TYPE_HN_DGDD_DJYB= "HN_DGDD_DJYB"; //湖南移动 动感地带电竞30元月包	22元/月 产品编码：91038029
    public static final String BIZ_TYPE_HN_5GLL_QYYX= "HN_5GLL_QYYX"; //湖南移动 5G流量权益优享月包（合约），30元20GB
    public static final String BIZ_TYPE_HN_5GTH_HYLLB= "HN_5GTH_HYLLB"; //湖南移动 5G特惠合约流量包（12个月版）30元30G
    public static final String BIZ_TYPE_HN_MGTQB_XSZS= "HN_MGTQB_XSZS"; //湖南移动 芒果特权包炫视专属版
    public static final String BIZ_TYPE_HN_HJMG_FYB= "HN_HJMG_FYB"; //湖南移动 黄金芒果分月包30元/月
    public static final String BIZ_TYPE_HN_NBA_TQLLB= "HN_NBA_TQLLB"; //湖南移动 NBA特权流量月包
    public static final String BIZ_TYPE_HN_5GTH_LLB= "HN_5GTH_LLB"; //湖南移动 5G特惠合约流量包（12个月版）
    public static final String BIZ_TYPE_HN_MGSJB= "HN_MGSJB"; //湖南移动 芒果升级包
    public static final String BIZ_TYPE_HN_SXK_XSZS= "HN_SXK_XSZS"; //湖南移动 随心看彩铃炫视专属版

    //业务类型-河南移动
    public static final String BIZ_TYPE_HNYZ_VRBT_SXX= "HNYZ_VRBT_SXX"; //业务代码 视频随心选E版季包
    public static final String BIZ_TYPE_HNYZ_SPMS_ZHB= "HNYZ_SPMS_ZHB"; //业务代码 智能视频秘书组合包A版5元享（12个月合约）
    public static final String BIZ_TYPE_HNYZ_SHFW_HYB= "HNYZ_SHFW_HYB"; //业务代码 生活服务会员车主版合约年包（电渠专属）
    public static final String BIZ_TYPE_HNYZ_SXX= "HNYZ_SXX"; //业务代码 生活服务会员车主版合约年包（电渠专属）
    public static final String BIZ_TYPE_HNYZ_MGYD_DJJP= "HNYZ_MGYD_DJJP"; //业务代码 咪咕阅读短剧精品包享流量12个月合约

    public static final String BIZ_TYPE_HENYD_YR_HYB= "HENYD_YR_HYB"; //业务代码 生活服务会员车主版合约年包（电渠专属）-悠然
    public static final String BIZ_TYPE_HENYD_YR_SXX= "HENYD_YR_SXX"; //业务代码 视频随心选E版合约半年包( 电渠专属 )-悠然
    public static final String BIZ_TYPE_HENYD_YR_MGB= "HENYD_YR_MGB"; //业务代码 动感地带·芒果包-悠然
    public static final String BIZ_TYPE_HENYD_YR_40GLLB= "HENYD_YR_40GLLB"; //业务代码 30元40G流量月包半价享(电渠专属)-悠然
    public static final String BIZ_TYPE_HENYD_YR_ZSHY= "HENYD_YR_ZSHY"; //业务代码 咪咕视频钻石会员季包(电渠专属)-悠然
    public static final String BIZ_TYPE_HENYD_YR_5GLLB= "HENYD_YR_5GLLB"; //业务代码 5G流量包-娱乐版6个月合约(电渠专属)
    public static final String BIZ_TYPE_HENYD_YR_5GXTH= "HENYD_YR_5GXTH"; //业务代码 5G流量包-娱乐版6个月合约(电渠专属)
    public static final String BIZ_TYPE_HENYD_YR_DJB= "HENYD_YR_DJB"; //业务代码 5G流量包-娱乐版6个月合约(电渠专属)
    public static final String BIZ_TYPE_HENYD_YR_DYLLB= "HENYD_YR_DYLLB"; //业务代码 20元100G抖音快手流量包
    public static final String BIZ_TYPE_HENYD_YR_50GLLB= "HENYD_YR_50GLLB"; //业务代码 15元享50G流量月包12个月合约（电渠专属）
    public static final String BIZ_TYPE_HNYZ_DYLLB= "HNYZ_DYLLB"; //业务代码 20元100G抖音快手流量包
    public static final String BIZ_TYPE_HNYZ_50GLLB= "HNYZ_50GLLB"; //业务代码 15元享50G流量月包12个月合约（电渠专属）
    public static final String BIZ_TYPE_HENYD_YR_HDDY = "HENYD_YR_HDDY"; //业务代码 横店电影联合会员12个月合约

    //中屹互联企业彩铃
    public static final String BIZ_CHANNEL_ZYHL_QY= "014X0EW";
    public static final String BIZ_TYPE_OUTSIDE_QY= "OUTSIDE_QY";

    //贵州移动业务code集合
    public static final List<String> GUIZHOU_MOBILE_BIZ_CODE_LIST
            = Arrays.asList(BIZ_TYPE_GZYD_LLB_JC5G,BIZ_TYPE_GZYD_SXK, BIZ_TYPE_GZYD_SXX,BIZ_TYPE_GZYD_SPCL_CXTH,BIZ_TYPE_GZYD_BJHY_DZHD,BIZ_TYPE_GZYD_SXK_SJDTYHD,BIZ_TYPE_GZYD_QX20G_LLTB,BIZ_TYPE_GZYD_YDYP_RHKJNB,BIZ_TYPE_GZYD_VRBT_XSZS,BIZ_TYPE_GZYD_LLB_12Y20G,BIZ_TYPE_GZYD_BJHY_10GLLB,BIZ_TYPE_GZYD_PLUS_HYYB,BIZ_TYPE_GZYD_SXX_HY,BIZ_TYPE_GZYD_KY_DLB,BIZ_TYPE_GZYD_YDYP_HJHY,BIZ_TYPE_GZYD_ZSHY,BIZ_TYPE_GZYD_TNLY_LXBY,BIZ_TYPE_GZYD_TNLY_HYLB,BIZ_TYPE_GZYD_ZSHY_SYTH,BIZ_TYPE_GZYD_MGZQ_QQB,BIZ_TYPE_GZYD_QXX_BJHY,BIZ_TYPE_GZYD_QXX_PLUS,BIZ_TYPE_GZYD_XSZS,BIZ_TYPE_GZYD_KDX_ZSB,BIZ_TYPE_GZYD_QYJXB,BIZ_TYPE_GZYD_NSXK,BIZ_TYPE_GZYD_TSQY);
    //贵州移动高姐业务code集合
    public static final List<String> GUIZHOU_GAOJIE_BIZ_CODE_LIST
        = Arrays.asList(BIZ_TYPE_GZGJ_PLUS_HYYB, BIZ_TYPE_GZGJ_VRBT, BIZ_TYPE_GZGJ_LLB_12Y20G, BIZ_TYPE_GZGJ_QXX_BJHY);

    //湖南移动业务code集合
    public static final List<String> HN_MOBILE_BIZ_CODE_LIST
            = Arrays.asList(BIZ_TYPE_HN_VRBT_DY_LLB,BIZ_TYPE_HN_QWZJ_LLB,BIZ_TYPE_HN_DGDD_MGB,BIZ_TYPE_HN_DGDD_DJYB,BIZ_TYPE_HN_5GLL_QYYX,BIZ_TYPE_HN_5GTH_HYLLB,BIZ_TYPE_HN_MGTQB_XSZS,BIZ_TYPE_HN_HJMG_FYB,BIZ_TYPE_HN_NBA_TQLLB,BIZ_TYPE_HN_5GTH_LLB,BIZ_TYPE_HN_MGSJB,BIZ_TYPE_HN_SXK_XSZS);

    //河南移动业务code集合
    public static final List<String> HENAN_MOBILE_BIZ_CODE_LIST
            = Arrays.asList(BIZ_TYPE_HNYZ_VRBT_SXX,BIZ_TYPE_HNYZ_SPMS_ZHB, BIZ_TYPE_HNYZ_SHFW_HYB,BIZ_TYPE_HENYD_YR_HYB,BIZ_TYPE_HENYD_YR_SXX,BIZ_TYPE_HENYD_YR_MGB,BIZ_TYPE_HENYD_YR_40GLLB,BIZ_TYPE_HENYD_YR_ZSHY,BIZ_TYPE_HENYD_YR_5GLLB,BIZ_TYPE_HNYZ_SXX,BIZ_TYPE_HNYZ_MGYD_DJJP,BIZ_TYPE_HENYD_YR_5GXTH,BIZ_TYPE_HENYD_YR_DJB,BIZ_TYPE_HENYD_YR_DYLLB,BIZ_TYPE_HENYD_YR_50GLLB,BIZ_TYPE_HNYZ_DYLLB,BIZ_TYPE_HNYZ_50GLLB,BIZ_TYPE_HENYD_YR_HDDY);


    //退订方式
    public static final String BIZ_UNSUBSCRIBE_REQUEST_WAY_IVR= "IVR"; //400电话ivr
    public static final String BIZ_UNSUBSCRIBE_REQUEST_WAY_SMS_MO= "SMS_MO";//用户大唐短信上行
    public static final String BIZ_UNSUBSCRIBE_REQUEST_WAY_CUSTOMER= "CUSTOMER"; //客服手动操作

    //业务类型-广东超炫XR视频彩铃
    public static final String BIZ_TYPE_GDYD_CXXR_SPCL= "GDYD_CXXR_SPCL"; //业务代码 广东超炫XR视频彩铃
    public static final String BIZ_TYPE_GDYD_CXXR_SPCL6= "GDYD_CXXR_SPCL6"; //业务代码 视频彩铃6_超炫流量包
    public static final String BIZ_TYPE_GDYD_CXXR_SPCL99= "GDYD_CXXR_SPCL99"; //业务代码 视频彩铃99_超炫流量包

    public static final String TRADE_TYPE_WECHAT = "JSAPI";
    public static final String TRADE_TYPE_HTML = "MWEB";
    public static final String TRADE_TYPE_TIKTOK = "TIKTOK";
    public static final String TRADE_TYPE_KUAISHOU = "KUAISHOU";
    public static final String BUSINESS_TYPE_KS_QYCL = "KS_QYCL";
    public static final String TRADE_TYPE_ALIPAY = "ALIPAY";
    public static final String TRADE_TYPE_TIKTOK_TRADE = "TIKTOK_TRADE";
    //业务类型-企业彩铃
    public static final String BIZ_TYPE_QYCL= "QYCL";
    //企业彩铃
    public static final String BIZ_QYCL= "QYCL";
    //企业彩铃
    public static final String BIZ_QYCL_MH= "QYCL_MH";
    //个人企业彩铃
    public static final String BIZ_QYCL_GR= "QYCL_GR";
    //个人企业彩铃(18元)
    public static final String BIZ_QYCL_GR_18 = "QYCL_GR_18";

    //个人企业彩铃
    public static final String BIZ_QYCL_GR_MH= "QYCL_GR_MH";
    //企业彩铃抖音
    public static final String BIZ_QYCL_DY= "QYCL_DY";
    public static final String BIZ_QYCL_DY_MH= "QYCL_DY_MH";

    public static final String BIZ_QYCL_SERVICE_ID = "9527004";


    public static final String BUSINESS_TYPE_WX_QYCLZY = "WX_QYCLZY";
    public static final String BUSINESS_TYPE_WX_QYCLDIY = "WX_QYCLDIY";
    public static final String BUSINESS_TYPE_WX_QYCL = "WX_QYCL";
    public static final String BUSINESS_TYPE_DY_QYCL = "DY_QYCL";

    //业务类型-禧九视频彩铃彩铃
    public static final String BIZ_TYPE_XIJIU_JX_VRBT= "XIJIU_JX_VRBT";
    //业务类型-禧九视频彩铃彩铃
    public static final String BIZ_CHANNEL_XIJIU_JIANGXI_VRBT= "XIJIU_JXYD_VRBT";
    /**
     * 是否有效:0=否,1=是
     */
    public static final Integer IS_VALID=1;

    //爱休闲集社渠道号
    public static final String BIZ_QYLI_ALL_CHANNEL_CODE = "QYLI_ALL";
    //北看台鹦鹉球迷会渠道号
    public static final String BIZ_QYLI_COMIC_CHANNEL_CODE = "QYLI_COMIC";
    //会员集社渠道号
    public static final String BIZ_QYLI_READ_CHANNEL_CODE = "QYLI_READ";
    //会员集社渠道号
    public static final String BIZ_QYLI_CW_CHANNEL_CODE = "014X09T";
    //联合会员权益包渠道号
    public static final String BIZ_LHHY_QYB_CHANNEL_CODE = "LHHY_QYB";
    //连续包月会员
    public static final String BIZ_LHHY_QYB_010_SERVICE_ID = "MEMBER_010";
    //会员大礼包
    public static final String BIZ_LHHY_QYB_990_SERVICE_ID = "MEMBER_990_VS";
    //特惠省钱包
    public static final String BIZ_LHHY_QYB_1880_SERVICE_ID = "MEMBER_1880";
    //至尊会员包
    public static final String BIZ_MEMBER_1990_SERVICE_ID = "MEMBER_1990";
    //连续包月会员
    public static final String BIZ_LHHY_QYB_010_PLUS_SERVICE_ID = "MEMBER_010_PLUS";
    //会员大礼包
    public static final String BIZ_LHHY_QYB_990_PLUS_SERVICE_ID = "MEMBER_990_PLUS";
    //特惠省钱包
    public static final String BIZ_LHHY_QYB_1880_PLUS_SERVICE_ID = "MEMBER_1880_PLUS";
    //连续包月会员
    public static final String BIZ_MEMBER_1990_VRBT_SERVICE_ID = "MEMBER_1990_VRBT";


    //会员体验(鸿盛天极)
    public static final String BIZ_MEMBERR_HSTJ_10_TEST_SERVICE_ID = "M_HSTJ_10_TEST";
    //尊享视听会员(鸿盛天极)
    public static final String BIZ_MEMBERR_HSTJ_1990_ZXST_SERVICE_ID = "M_HSTJ_1990_ZXST";
    //至尊会员礼包(鸿盛天极)
    public static final String BIZ_MEMBERR_HSTJ_2500_ZZHY_SERVICE_ID = "M_HSTJ_2500_ZZHY";


    //美团礼包(通道4)
    public static final String BIZ_M_XR_2500_MD_SERVICE_ID = "M_XR_2500_MD";
    //视听会员随心看(通道4)
    public static final String BIZ_M_XR_2500_SXK_SERVICE_ID = "M_XR_2500_SXK";
    //视频会员任意选(通道4)
    public static final String BIZ_M_XR_2500_RYX_SERVICE_ID = "M_XR_2500_RYX";

    //美团礼包(通道5)
    public static final String BIZ_M_WX_1990_MD_SERVICE_ID = "M_WX_1990_MD";
    //视频会员任意选(通道5)
    public static final String BIZ_M_WX_1990_RYX_SERVICE_ID = "M_WX_1990_RYX";
    //网易云MM
    public static final String BIZ_TYPE_WANGYIYUN_MM= "WANGYIYUN_MM"; //业务代码 网易云MM
    //短剧
    public static final String  BIZ_TYPE_DUANJU="DUANJU";
    //水晶传说
    public static final String  BIZ_TYPE_SJCS="SJCS";
    //暗黑主宰
    public static final String  BIZ_TYPE_AHZZ="AHZZ";
    //第七幻域
    public static final String  BIZ_TYPE_DQHY="HY_DQHY";
    //骏伯流量包 渠道号
    public static final String BIZ_CHANNEl_JUNBO_LLB = "JUNBO_LLB";
    //骏伯流量包 业务代码
    public static final String BIZ_TYPE_JUNBO_LLB= "JUNBO_LLB";
    //骏伯业务
    public static final String BIZ_TYPE_JUNBOLLB= "JUNBOLLB";
    public static final String BIZ_CHANNEL_GZTN_JB = "GZTN_JB";
    public static final String BIZ_CHANNEL_GZSXK_JB = "GZSXK_JB";
    //山西移动_骏伯ai视频彩铃(封装一语成片业务)
    public static final String BIZ_CHANNEL_SXYD_JBAICL= "SXYD_JBAICL";

    //qiyin视频彩铃集合
    public static final List<String>  QIYIN_CHANNEL_LIST = Arrays.asList(MiguApiService.QIYIN_CHANNEL_CODE_04N, MiguApiService.QIYIN_CHANNEL_CODE_04P, MiguApiService.QIYIN_CHANNEL_CODE_04O);
//    //会员权益大礼包
//    public static final List<String>  MEMBER_BIG_PACK= Lists.newArrayList(BIZ_LHHY_QYB_990_SERVICE_ID,BIZ_LHHY_QYB_1880_SERVICE_ID,BIZ_LHHY_QYB_990_PLUS_SERVICE_ID,BIZ_LHHY_QYB_1880_PLUS_SERVICE_ID,BIZ_MEMBER_1990_VRBT_SERVICE_ID,BIZ_MEMBER_1990_SERVICE_ID,BIZ_MEMBERR_HSTJ_10_TEST_SERVICE_ID,BIZ_MEMBERR_HSTJ_1990_ZXST_SERVICE_ID,BIZ_MEMBERR_HSTJ_2500_ZZHY_SERVICE_ID,BIZ_M_XR_2500_MD_SERVICE_ID,BIZ_M_XR_2500_SXK_SERVICE_ID,BIZ_M_XR_2500_RYX_SERVICE_ID,BIZ_M_WX_1990_MD_SERVICE_ID,BIZ_M_WX_1990_RYX_SERVICE_ID);

    //支付宝权益列表业务ID
    public static final List<String>  ALIPAY_RIGHTS_LIST= Lists.newArrayList(BIZ_M_WX_1990_RYX_SERVICE_ID,BIZ_LHHY_QYB_990_PLUS_SERVICE_ID);

    //通道1
    public static final List<String>  ROAD_ONE_LIST= Lists.newArrayList(BIZ_LHHY_QYB_990_SERVICE_ID,BIZ_LHHY_QYB_1880_SERVICE_ID,BIZ_MEMBER_1990_SERVICE_ID,BIZ_MEMBER_1990_VRBT_SERVICE_ID);
//
//    //通道2
//    public static final List<String>  ROAD_TWO_LIST= Lists.newArrayList(BIZ_LHHY_QYB_990_PLUS_SERVICE_ID,BIZ_LHHY_QYB_1880_PLUS_SERVICE_ID);
//
//    //通道3
//    public static final List<String>  ROAD_THREE_LIST= Lists.newArrayList(BIZ_MEMBERR_HSTJ_10_TEST_SERVICE_ID,BIZ_MEMBERR_HSTJ_1990_ZXST_SERVICE_ID,BIZ_MEMBERR_HSTJ_2500_ZZHY_SERVICE_ID);
//
//    //通道4
//    public static final List<String>  ROAD_FOUR_LIST= Lists.newArrayList(BIZ_M_XR_2500_MD_SERVICE_ID,BIZ_M_XR_2500_SXK_SERVICE_ID,BIZ_M_XR_2500_RYX_SERVICE_ID);
//
//    //通道5
//    public static final List<String>  ROAD_FIVE_LIST= Lists.newArrayList(BIZ_M_WX_1990_MD_SERVICE_ID,BIZ_M_WX_1990_RYX_SERVICE_ID);

//    public static Map<String, String> aliPayMap = new ImmutableMap.Builder<String, String>()
//            .put(BIZ_LHHY_QYB_990_SERVICE_ID, "1")
//            .put(BIZ_LHHY_QYB_1880_SERVICE_ID, "1")
//            .put(BIZ_MEMBER_1990_SERVICE_ID, "1")
//            .put(BIZ_MEMBER_1990_VRBT_SERVICE_ID, "1")
//            .put(BIZ_LHHY_QYB_990_PLUS_SERVICE_ID, "2")
//            .put(BIZ_LHHY_QYB_1880_PLUS_SERVICE_ID, "2")
//            .put(BIZ_MEMBERR_HSTJ_10_TEST_SERVICE_ID, "3")
//            .put(BIZ_MEMBERR_HSTJ_1990_ZXST_SERVICE_ID, "3")
//            .put(BIZ_MEMBERR_HSTJ_2500_ZZHY_SERVICE_ID, "3")
//            .put(BIZ_M_XR_2500_MD_SERVICE_ID, "4")
//            .put(BIZ_M_XR_2500_SXK_SERVICE_ID, "4")
//            .put(BIZ_M_XR_2500_RYX_SERVICE_ID, "4")
//            .put(BIZ_M_WX_1990_MD_SERVICE_ID, "5")
//            .put(BIZ_M_WX_1990_RYX_SERVICE_ID, "5")
//            .build();
//    public static List<String>  MEMBER_BIG_PACK=aliPayMap.keySet().stream().collect(Collectors.toList());
    //道具下发通知接口
    //测试环境：
    public static final String TEST_SEND_PRODUCT_CALLBACK = "http://cmgame.cdcxtc.com/ng_p/member/vouchercp/v1.0.7.7/sendProductCallback";
    //道具下发通知接口
    //生产环境：
    public static final String PRODUCTION_SEND_PRODUCT_CALLBACK = "https://betagame.migufun.com/member/vouchercp/v1.0.7.7/sendProductCallback";

    //道具领取通知接口
    //测试环境：
    public static final String TEST_RECEIVE_PRODUCT_CALLBACK = "http://cmgame.cdcxtc.com/ng_p/member/vouchercp/v1.0.7.7/receiveProductCallback";
    //道具领取通知接口
    //生产环境：
    public static final String PRODUCTION_RECEIVE_PRODUCT_CALLBACK = "https://betagame.migufun.com/member/vouchercp/v1.0.7.7/receiveProductCallback";

    public static final String BASE_CHECK_CODES = "qwertyuiplkjhgfdsazxcvbnmQWERTYUPLKJHGFDSAZXCVBNM1234567890";

    public static Map<Integer, String> IvrMsgMap = new ImmutableMap.Builder<Integer, String>()
            .put(201, "您的来电号码没有办理相关业务！")
            .put(301, "您已通过支付宝生成投诉订单，我们将尽快为您处理，请耐心等候！")
            .put(101, "系统查询到您在payTime办理了businessName，资费orderAmount元！")
            .put(102, "系统查询到您在payTime办理了特惠省钱包会员，我们已于refundTime通过转账方式退款，请注意查看支付宝账单信息和手机短信！")
            .put(103, "退款失败！")
            .put(610, "您的来电号码没有办理相关业务！")
            .put(601, "退款成功！")
            .put(602, "退款中！")
            .put(603, "退款失败！")
            .build();
    //拒绝接通
    public static Map<String, String> IvrTurnDownMsgMap = new ImmutableMap.Builder<String, String>()
            .put("MEMBER_1880_PLUS", "经核查您这边未订购相关业务，如对扣费有疑问，请拨打4007585858进行咨询。")
            .put("MEMBER_010_PLUS", "经核查您这边未订购相关业务，如对扣费有疑问，请拨打4007585858进行咨询。")
            .put("MEMBER_990_PLUS", "经核查您这边未订购相关业务，如对扣费有疑问，请拨打4007585858进行咨询。")
            .build();
    //发送短信业务id
    public static final String SMS_MODEL_COMMON_SERVICE_ID = "9527005";
    public static final List<String> ALIPAY_LIST= Lists.newArrayList("MEMBER_TRANSFER_FEE","M_R_CERTIFICATE","MEMBER_010","MEMBER_010_PLUS");


    //VR竞盟获取 token 令牌接口
    public static final String VR_JINGMENG_TOKEN_URL = "http://*************:8090/api/getToken";


    public static final String ALIPAY_RIGHTS_SERVICE_NOT_CONFIG = "支付宝视频彩铃业务权益业务未配置";
    public static final String ALIPAY_RIGHTS_SERVICE_NOT_RELEVANCE = "支付宝视频彩铃业务权益业务未关联";
    public static final String ALIPAY_RIGHTS_PRODUCT_NOT_CONFIG = "支付宝视频彩铃业务权益产品未配置";
    public static final String ALIPAY_RIGHTS_ORDER_RECHARGE_PASS ="支付宝视频彩铃已充值";
    public static final String ALIPAY_RIGHTS_ORDER_RECHARGE_SUCCESS ="直充成功";
    public static final String ALIPAY_RIGHTS_ORDER_RECHARGE_FAIL ="直充失败";
    public static final String ALIPAY_RIGHTS_ORDER_RECHARGE_SYSTEM_ERROR = "系统错误";
    public static final String COMPANY_OWNER_HUAYI = "HUAYI";
    public static final String COMPANY_OWNER_TUNIU = "TUNIU";


    //渠道归属
    public static final String CHANNEL_OWNER_CPA = "cpa";
    public static final String CHANNEL_OWNER_XIANGHONG = "xianghong";
    public static final String CHANNEL_OWNER_XIAOFENG = "xiaofeng";
    public static final String CHANNEL_OWNER_DONGSW = "dongsw";


    //江西移动视频彩铃订单状态
    public static final String JIANGXI_VRBT_ORDER_CODE_SUCCESS = "SUCCESS";
    public static final String JIANGXI_VRBT_ORDER_CODE_FAILED = "FAILED";

    public static final Map<String,String> provinceCodeMap = new ImmutableMap.Builder<String, String>()
            .put("北京","100")
            .put("天津","220")
            .put("河北","311")
            .put("山西","351")
            .put("内蒙古","471")
            .put("辽宁","240")
            .put("吉林","431")
            .put("黑龙江","451")
            .put("上海","210")
            .put("江苏","250")
            .put("浙江","571")
            .put("安徽","551")
            .put("福建","591")
            .put("江西","791")
            .put("山东","531")
            .put("河南","371")
            .put("湖北","270")
            .put("湖南","731")
            .put("广东","200")
            .put("广西","771")
            .put("海南","898")
            .put("重庆","230")
            .put("四川", "280")
            .put("贵州", "851")
            .put("云南", "871")
            .put("西藏", "891")
            .put("陕西", "290")
            .put("甘肃", "931")
            .put("青海", "971")
            .put("宁夏", "951")
            .put("新疆", "991")
            .build();

    /**
     * 判断是否为通过咪咕通知判断为开通成功依据的业务类型
     *
     * @param bizType
     * @return
     */
    public static boolean isMiguNotifySuccessBizType(String bizType) {
        return StringUtils.equalsAny(bizType, BIZ_QYCL, BIZ_TYPE_HETU, BIZ_TYPE_DYXC, BIZ_TYPE_CFDTW,BIZ_TYPE_DUANJU,BIZ_TYPE_SJCS,BIZ_TYPE_AHZZ,BIZ_TYPE_DX_AIVRBT);
    }


    //中财乐扬业务集合
    public static final List<String> ZYCL_CHANNEL_LIST = Arrays.asList(BIZ_CHANNEL_SCYD_BYZHBC, BIZ_CHANNEL_SCYD_BYZHBC_LLB, BIZ_TYPE_HENYD_YR_5GXTH, BIZ_CHANNEL_YR_GSYD_MXLDC);

    //佐悦炫视视频彩铃分省合作渠道号
    public static final List<String> XUANSHI_RPOVINCE_CHANNEL_LIST = Arrays.asList(BIZ_TYPE_GZYD_XSZS);


}




