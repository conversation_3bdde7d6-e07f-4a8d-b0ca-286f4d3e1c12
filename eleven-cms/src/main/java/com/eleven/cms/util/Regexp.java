package com.eleven.cms.util;

/**
 * 正则常量
 *
 * <AUTHOR>
 */
public class Regexp {

    // 简单手机号正则（这里只是简单校验是否为 11位，实际规则更复杂）
    public static final String MOBILE_REG = "^[1][3,4,5,6,7,8,9][0-9]{9}$";
    // 咪咕开通短信正则
    //public static final String MIGU_SMS_CODE_REG = "^\\d{4}|\\d{6}$";
    public static final String MIGU_SMS_CODE_REG = "^\\d{4}(?:\\d{2})?$";  //4位或者6位短信验证码校验正则
    public static final String LIANTONG_SMS_CODE_REG = "^\\d{6}$";
    public static final String DIANXIN_SMS_CODE_REG = "^\\d{4}$";

    public static void main(String[] args) {
        System.out.println("123".matches(MIGU_SMS_CODE_REG));
        System.out.println("1234".matches(MIGU_SMS_CODE_REG));
        System.out.println("12345".matches(MIGU_SMS_CODE_REG));
        System.out.println("123456".matches(MIGU_SMS_CODE_REG));
        System.out.println("1234567".matches(MIGU_SMS_CODE_REG));
    }
}
