package com.eleven.cms.util;

import okhttp3.FormBody;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created with IntelliJ IDEA.
 * User: zhaojiangming
 * Date: 4/5/13
 * Time: 11:41 AM
 * 随机数生成类
 */
public abstract class RandomUtils {

    private static final Random RANDOM = new Random();

    /**
     * 允许最小值为0,随机数包含上下限,上下限可以交换
     * @param lowerValue
     * @param upperValue
     * @return
     */
    public static int createRandom(int lowerValue, int upperValue) {
        //if (lowerValue < 0 || upperValue < 0) {
        //    throw new IllegalArgumentException("value cant't small than 0.");
        //}
        if(lowerValue>upperValue){
            lowerValue = lowerValue^upperValue;
            upperValue = lowerValue^upperValue;
            lowerValue = lowerValue^upperValue;
        }
        if (lowerValue < 0) {
            //补偿值
            int recoup = lowerValue;
            lowerValue = 0;
            upperValue -= recoup;
            return (int)(Math.random() * (upperValue - lowerValue + 1)) + recoup;
        }
        return (int) (Math.random() * (upperValue - lowerValue + 1)) + lowerValue;
    }
    /**
     * 不允许最小值为0
     * @param lowerValue
     * @param upperValue
     * @return
     */
    public static int getRondom(int lowerValue, int upperValue) {
        if (lowerValue < 1 ) {
            throw new IllegalArgumentException("lowerValue must big than 0.");
        }
        if (lowerValue > upperValue) {
            throw new IllegalArgumentException("lowerValue must small than upperValue.");
        }
        return RANDOM.nextInt(upperValue) % (upperValue - lowerValue + 1) + lowerValue;
    }

    /**
     * 生成6位短信验证码
     * @return
     */
    public static String randomCode() {
        StringBuilder str = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            str.append(random.nextInt(10));
        }
        return str.toString();
    }
    /**
     * 生成4位短信验证码
     * @return
     */
    public static String randomCode4Num() {
        StringBuilder str = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            str.append(random.nextInt(10));
        }
        return str.toString();
    }

    /**
     * 随机指定范围内N个不重复的数
     * @param lowerValue 指定范围最小值
     * @param upperValue 指定范围最大值
     * @param amount 随机数个数
     */
    public static int[] getRandomArray(int lowerValue, int upperValue, int amount) {

        if (lowerValue < 1 ) {
            throw new IllegalArgumentException("lowerValue must big than 0.");
        }
        if (lowerValue > upperValue) {
            throw new IllegalArgumentException("lowerValue must small than upperValue.");
        }

        int[] rendomArr = new int[amount];

        // 如果存入的数小于指定生成的个数，则调用递归再生成剩余个数的随机数，如此循环，直到达到指定大小
        for (int i = 0; i < amount ; i++) {
            // 调用Math.random()方法
            int rand = RANDOM.nextInt(upperValue) % (upperValue - lowerValue + 1) + lowerValue;
            // 将不同的数存入数组中
            rendomArr[i] = rand;
        }

        return rendomArr;
    }


    public static boolean isInRatio(int ratio){
        return getRondom(1,100)<=ratio;
    }



    public static void main(String[] args) {
        //int a = 8;
        //int b = 27;
        //a = a^b;

        //b = a^b;
        //a = a^b;
        //System.out.println("a = " + a);
        //System.out.println("b = " + b);
        Map<String, List<Integer>> collect = Stream.generate(() -> createRandom(-5, 5)).limit(100).collect(
                Collectors.groupingBy(String::valueOf));
        System.out.println("collect = " + collect);
       //for (int i = 0;i<=100;i++){
       //    System.out.println(createRandom(-5,5));
       //}


    }

    /**
     * 生成9位随机数
     * @return
     */
    public static String randomCode9Num() {
        StringBuilder str = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 9; i++) {
            str.append(random.nextInt(10));
        }
        return str.toString();
    }
}
