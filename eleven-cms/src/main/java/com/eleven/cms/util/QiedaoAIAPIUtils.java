package com.eleven.cms.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;

/**
 * 腾讯云AI
 *
 * <AUTHOR>
 * @datetime 2024/10/28 14:05
 */
@Slf4j
public class QiedaoAIAPIUtils {

    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    public static final String HOST = "ai.qiedao.fun";

    public static final String LOGIN = "/api/v1/auth/login";
    public static final String FACES_DETECTION = "/api/v1/faces/facesdetection_landmark_url";
//    public static final String DIGITAL_HUMAN = "/api/v1/human/record2digital_human";
//    public static final String SCHEDULE_DIGITAL_HUMAN = "/api/v1/human/schedule_digital_human";

    public static final String USER_NAME = "kptn";
    public static final String PASSWORD = "_sn=.N:K~Uv0E6#Q";
    public static final String LOG_TAG = "图片人脸识别";
    /**
     * 登录获取token
     *
     * @return String  token
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     * @throws InvalidKeyException      InvalidKeyException
     */
    private static String login() {
        HttpUrl url = HttpUrl.parse("https://" + HOST + LOGIN);

        HashMap params = new HashMap<>();
        params.put("username", USER_NAME);
        params.put("password", PASSWORD);
        RequestBody body = RequestBody.create(JSON, JacksonUtils.toJson(params));

        return HttpUtils.post(COMMON_CLIENT, url, body, null, LOG_TAG, "qiedaoAI-登录获取token");
    }

    private static void parseCommonResponse(JsonNode rootNode) {
        JsonNode codeNode = rootNode.path("state_code");
        if (codeNode == null || !NumberUtil.equals(codeNode.asInt(), 200)) {
            throw new JeecgBootException(rootNode.path("message").asText());
        }
    }

    private static String parseLoginResponse(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        parseCommonResponse(rootNode);
        if (rootNode.has("data")) {
            JsonNode dataNode = rootNode.path("data");
            return dataNode.path("access_token").asText();
        }
        throw new JeecgBootException("获取qiedaoAItoken异常");
    }

    private static String getToken() {
        RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
        String qiedaoAiToken = (String) redisUtil.get("aivrbtApp:cms:qiedaoAiToken");
        if (StrUtil.isBlank(qiedaoAiToken)) {
            qiedaoAiToken = parseLoginResponse(login());
            redisUtil.set("aivrbtApp:cms:qiedaoAiToken", qiedaoAiToken, 86000);
        }
        return "Bearer " + qiedaoAiToken;
    }

    public static Boolean parseFacesDetectionResponse(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        parseCommonResponse(rootNode);
        if (rootNode.has("data")) {
            JsonNode dataNode = rootNode.path("data");
            return dataNode.asBoolean();
        }
        throw new JeecgBootException("人脸识别异常");
    }

    /**
     * 人脸识别
     *
     * @return String  faceUrl
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     * @throws InvalidKeyException      InvalidKeyException
     */
    public static String facesDetection(String faceUrl) {
        HttpUrl.Builder builder = HttpUrl.parse("https://" + HOST + FACES_DETECTION).newBuilder();
        builder.addQueryParameter("source_url", faceUrl);
        Headers headers = new Headers.Builder()
                .add("Authorization", getToken())
                .build();

        return HttpUtils.get(COMMON_CLIENT, builder.build(), headers, LOG_TAG, "qiedaoAI-人脸识别");
    }

//    /**
//     * 创作图片播报任务
//     * @param videoUrl 音频
//     * @param faceUrl 人脸
//     * @param extraInfoMap
//     * @return 任务id
//     */
//    public static String picSpeakTask(String videoUrl, String faceUrl, Map<String, Object> extraInfoMap) {
//        HttpUrl url = HttpUrl.parse("https://" + HOST + DIGITAL_HUMAN);
//
//        HashMap params = new HashMap<>();
//        params.put("source_image_path", faceUrl);
//        params.put("driven_audio_path", videoUrl);
//        String json = JacksonUtils.toJson(params);
//        RequestBody body = RequestBody.create(JSON, json);
//
//        Headers headers = new Headers.Builder()
//                .add("Authorization", getToken())
//                .build();
//        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "qiedaoAI-图片播报", extraInfoMap);
//    }
//
//    public static String parsePicSpeakTaskResponse(String response) {
//        JsonNode rootNode = JacksonUtils.readTree(response);
//        parseCommonResponse(rootNode);
//        if (rootNode.has("data")) {
//            JsonNode dataNode = rootNode.path("data");
//            return dataNode.path("queue_uuid").asText();
//        }
//        throw new JeecgBootException("创建照片播报任务异常");
//    }
//
//
//    public static String queryTask(String jobId, Map<String, Object> extraInfoMap) {
//        HttpUrl.Builder builder = HttpUrl.parse("https://" + HOST + SCHEDULE_DIGITAL_HUMAN).newBuilder();
//        builder.addQueryParameter("task_uuid", jobId);
//        Headers headers = new Headers.Builder()
//                .add("Authorization", getToken())
//                .build();
//
//        return HttpUtils.get(COMMON_CLIENT, builder.build(), headers, LOG_TAG, "qiedaoAI-查询任务结果");
//    }
//
//    public static JsonNode parseQueryTaskResponse(String response) {
//        JsonNode rootNode = JacksonUtils.readTree(response);
//        parseCommonResponse(rootNode);
//        if (rootNode.has("data")) {
//            return rootNode.path("data");
//        }
//        throw new JeecgBootException("查询照片播报任务异常");
//    }
}
