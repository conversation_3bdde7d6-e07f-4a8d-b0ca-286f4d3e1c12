package com.eleven.cms.util;

import com.eleven.cms.util.okhttpcookie.CookieJarImpl;
import com.eleven.cms.util.okhttpcookie.MemoryCookieStore;
import okhttp3.*;
import okhttp3.internal.Util;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.io.InputStream;
import java.net.PasswordAuthentication;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Description:okhttp单例创建类
 *
 * @version 2015/12/18 20:12 V1.0
 */
public class OkHttpClientUtils {
    private static Logger logger = LoggerFactory.getLogger(OkHttpClientUtils.class);
    //default 5
    //private static final int MAX_IDLE_CONNECTIONS = 50;
    //default 10秒
    private static final int CONNECT_TIMEOUT = 15;
    //default 10秒
    private static final int WRITE_TIMEOUT = 15;
    //default 10秒
    private static final int READ_TIMEOUT = 15;

    public static final String junboProxyHost = "*************";
    public static final String proxyHost = "**************";
    public static final String testProxyHost = "*************";
    public static final String mirrorProxyHost = "***************";
    public static final String gaojieProxyHost = "*************";
    public static final int proxyPort = 9999;
    public static final int testProxyPort = 10087;
    public static final int mirrorProxyPort = 10087;
    public static final int junboProxyPort = 9999;
    public static final int gaojieProxyPort = 23455;

    static {
        Map<String, PasswordAuthentication> authMap = new HashMap<>();
        final PasswordAuthentication defaultPwdAuth = new PasswordAuthentication("s5", "rkxXolZ_kvQx0df3lW7AnqlK".toCharArray());
        final PasswordAuthentication gaojiePwdAuth = new PasswordAuthentication("zxc", "zxc".toCharArray());
        authMap.put(junboProxyHost, defaultPwdAuth);
        authMap.put(proxyHost, defaultPwdAuth);
        authMap.put(testProxyHost, defaultPwdAuth);
        authMap.put(mirrorProxyHost, defaultPwdAuth);
        authMap.put(gaojieProxyHost, gaojiePwdAuth);


        java.net.Authenticator.setDefault(new java.net.Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return authMap.getOrDefault(getRequestingHost(), defaultPwdAuth);
            }
        });

//        System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2");
    }

//    static {
////		System.setProperty("http.keepAlive", "true");
////		System.setProperty("http.keepAliveDuration", 10*60*1000+"");
//        System.setProperty("http.maxConnections", MAX_IDLE_CONNECTIONS + "");
//    }

//GET /api/v4/members/qian-jiang-12-47/activities?limit=7&session_id=980755116894420992&after_id=1575261976&desktop=True HTTP/1.1
//Host: www.zhihu.com
//Connection: keep-alive
//X-Ab-Param: se_cardrank_1=0;se_websearch=3;tp_qa_metacard=1;soc_bigone=1;ug_follow_topic_1=2;li_se_media_icon=1;li_video_section=0;zr_km_feed_prerank=new;se_hotmore=2;se_cardrank_3=1;tp_topic_head=0;top_universalebook=1;li_query_match=0;tp_club_pk=0;soc_zcfw_badcase=0;li_paid_answer_exp=0;top_quality=0;soc_leave_recommend=2;qap_question_author=0;se_zu_onebox=0;se_use_zitem=0;soc_notification=1;se_ltr_dnn_cp=0;zr_test_aa1=1;se_perf=0;zr_des_detail=0;se_site_onebox=0;qap_ques_invite=0;se_ltr_cp_new=0;se_waterfall=0;se_dnn_mt=0;se_multi_task_new=0;tp_qa_toast=1;ug_zero_follow=0;zw_payc_qaedit=0;se_new_topic=0;se_auto_syn=0;zr_rewrite_query=0;zr_video_recall=current_recall;zr_recall_heatscore=4_6;tsp_vote=2;ls_zvideo_license=1;li_qa_btn_text=0;se_topicfeed=0;se_p_slideshow=1;se_famous=1;se_payconsult=5;zr_km_style=base;se_movietab=1;li_se_section=1;zr_esmm_model_mix=model_080;li_se_heat=1;zw_sameq_sorce=999;li_salt_hot=2;zr_item_nn_recall=close;se_college_cm=1;soc_ri_merge=1;sem_up_growth=in_app;zr_paid_answer_mix=mixed_15;se_zu_recommend=0;se_subtext=1;se_sug=1;li_pay_banner_type=4;zr_rec_answer_cp=close;tp_m_intro_re_topic=1;soc_zcfw_broadcast=0;ug_follow_answerer=0;ls_zvideo_trans=0;li_qa_cover=old;zr_slot_cold_start=aver;soc_authormore=0;pf_newguide_vertical=0;zr_ans_rec=gbrank;zr_km_slot_style=event_card;zr_infinity_member=close;soc_bignew=1;zr_esmm_model=old;soc_wonderuser_recom=0;zr_km_recall_num=open;se_topiclabel=1;se_billboardsearch=0;se_lottery=0;ls_zvideo_rec=0;zr_new_commodity=1;se_go_ztext=0;se_whitelist=1;tp_club_qa=1;tp_meta_card=0;tsp_redirecthotlist=10;li_de=no;zr_km_answer=open_cvr;soc_stickypush=0;se_webmajorob=0;top_ebook=0;zr_km_item_cf=open;se_ctx=0;tsp_hotlist_ui=2;qap_question_visitor= 0;zr_se_new_kw=1;se_new_merger=1;tp_score_1=c;qap_payc_invite=0;se_entity_model=1;se_backsearch=0;tp_qa_metacard_top=top;zr_answer_rec_cp=open;se_aa_base=1;se_ctr=0;se_webtimebox=1;zr_paid_answer_exp=0;top_new_feed=5;ug_zero_follow_0=0;li_cln_vl=no;zr_km_item_prerank=old;se_amovietab=1;se_mobileweb=1;top_test_4_liguangyi=1;pf_creator_card=1;li_qa_new_cover=1;se_likebutton=0;se_mclick=0;se_timebox_up=0;ls_videoad=2;li_answer_card=0;se_ltr_user=0;soc_yxzl_zcfw=0;soc_zcfw_shipinshiti=0;li_album_liutongab=0;se_ctr_topic=0;se_wannasearch=a;tp_club_qa_pic=1;ug_follow_answerer_0=0;li_hot_score_ab=0;li_ebook_audio=0;se_cardrank_4=1;se_dnn_unbias=1;se_multianswer=2;se_club_post=5;tp_club_header=1;top_native_answer=1;ls_fmp4=0;zr_km_special=open;se_ab=0;ug_goodcomment_0=1;li_tjys_ec_ab=0;zr_km_feed_nlp=old;zr_rel_search=base;se_agency= 0;se_adxtest=1;tp_sft=a;ls_zvideo_like=0;ls_bullet_guide=0;top_v_album=1;pf_noti_entry_num=0;pf_foltopic_usernum=50;se_entity_model_14=1;se_college=default;tp_sft_v2=d;li_sku_bottom_bar_re=0;zr_video_rank=new_rank;se_expired_ob=0;se_spb309=0;se_mclick1=0;zr_expslotpaid=1;se_rel_search=0;se_cate=1;soc_zuichangfangwen=0;pf_fuceng=1;zr_km_recall=default;zr_book_chap=0;se_ctr_user=0;li_vip_no_ad_mon=0;se_webrs=1;li_vip_lr=2;zr_art_rec=base;zr_intervene=0;zr_article_new=close;zr_km_category=open;se_cardrank_2=1;se_ctr_pyc=0;ug_newtag=1;qap_thanks=0;se_hot_timebox=1;soc_update=1;ug_fw_answ_aut_1=0;zr_prerank_heatscore=true;zr_video_rank_nn=new_rank;se_featured=1;top_hotcommerce=5;top_ydyq=X;se_preset_tech=0;top_root=0;zr_km_topic_zann=new;se_colorfultab=1;se_ad_index=10;li_android_vip=0;li_purchase_test=0;zr_km_prerank=new;zr_slotpaidexp=9;se_ios_spb309=1;se_hotsearch=1;se_time_threshold=0;tp_header_style=1;soc_zcfw_broadcast2=1;tp_sticky_android=2;soc_special=0;se_col_boost=1;se_search_feed=N
//x-requested-with: fetch
//X-API-VERSION: 3.0.40
//User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36
//X-Zse-83: 3_2.0
//X-Zse-84: sLFq6A9yoLYxcMYqmTH0gHr8eTSxrR2qfHFBQ79qe_SfF9Y0zCxyQ0r068Sp2MYy
//Accept: */*
//Referer: https://www.zhihu.com/people/qian-jiang-12-47/activities
//Accept-Encoding: gzip, deflate, br
//Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
//Cookie: _xsrf=f22794c0-8093-4f2f-a6ea-c740639d874a; _zap=04cdd645-a321-4eee-931c-af8a40d542cf; l_n_c=1; n_c=1; d_c0="AIAjIpVXnA2PTp8f6fbEuN8MnqmeNh1JPPU=|1526610241"; __utmc=155987696; __utma=155987696.1295807004.1527070242.1527126759.1528794545.3; ISSW=1; z_c0="2|1:0|10:1571021090|4:z_c0|92:Mi4xZG4xMEFnQUFBQUFBZ0NNaWxWZWNEU1lBQUFCZ0FsVk5JaS1SWGdEUnFKbHhIUWZHR3czajUzUVhYd0xkTlhzZFdn|39edb51f3115863c72d37e27ba190423e44ede40c923b73525167f9b967ccbb0"; q_c1=52df9673877a4c9e84ce1bbe471cd584|1572418181000|1525832721000; tst=r; Hm_lvt_98beee57fd2ef70ccdd5ca52b9740c49=1574842511,1574924572,1574925083,1575268608; tgw_l7_route=302fb19f026f5b11b5e883a5cbbb5031; Hm_lpvt_98beee57fd2ef70ccdd5ca52b9740c49=1575357608


    /**
     * 生成OkHttpClient新实例
     *
     * @return
     */
    public static OkHttpClient getNewInstance() {

        ////添加post公共请求参数  Constants.PACKAGE 和 Constants.CFROM
        //BasicParamsInterceptor basicParamsInterceptor = new BasicParamsInterceptor.Builder()
        //        .addParam("from", "android") //添加公共参数到 post 请求体
        //        .addQueryParam("version","1")  // 添加公共版本号，加在 URL 后面
        //        .addHeaderLine("X-Ping: Pong")  // 示例： 添加公共消息头
        //        .addParamsMap(map) // 可以添加 Map 格式的参数
        //        .build();
        //
        ////然后在 OkHttpClient中添加拦截器就完成了
        //okHttpClient = new OkHttpClient.Builder()
        //        .readTimeout(READ_TIME_OUT, TimeUnit.MILLISECONDS)
        //        .connectTimeout(CONNECT_TIME_OUT, TimeUnit.MILLISECONDS)
        //        .writeTimeout(CONNECT_TIME_OUT, TimeUnit.SECONDS)
        //        .addInterceptor(basicParamsInterceptor) // 添加公共参数拦截器
        //        .addInterceptor(logInterceptor)
        //        .addInterceptor(noNetInterceptor)
        //        .addNetworkInterceptor(cacheInterceptor)
        //        .retryOnConnectionFailure(true)
        //        .cache(cache)
        //        .build();

        //解决jdk7及以下版本默认值支持TLSv1的问题
        //System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2");
        return new OkHttpClient.Builder()
                //设置超时时间
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                //使用fiddler代理查看http请求
                //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1",9999)))
                ////信任所有的https证书(如下只是解决了不受信任的证书问题,默认本身就是支持受信任证书的https)
                //.sslSocketFactory(createIgnoreVerifySSLSocketFactory(),new TrustAllCerts())
                ////实际是ssl主机信任
                //.hostnameVerifier(new HostnameVerifier() {
                //    @Override
                //    public boolean verify(String hostname, SSLSession session) {
                //        return true;
                //    }
                //})
                //解决java.io.IOException: unexpected end of stream on unknown的问题
                .retryOnConnectionFailure(true)
                .build();
    }

    /**
     * 生成OkHttpClient新实例
     *
     * @return
     */
    public static OkHttpClient getIgnoreVerifySSLInstance() {

        ////添加post公共请求参数  Constants.PACKAGE 和 Constants.CFROM
        //BasicParamsInterceptor basicParamsInterceptor = new BasicParamsInterceptor.Builder()
        //        .addParam("from", "android") //添加公共参数到 post 请求体
        //        .addQueryParam("version","1")  // 添加公共版本号，加在 URL 后面
        //        .addHeaderLine("X-Ping: Pong")  // 示例： 添加公共消息头
        //        .addParamsMap(map) // 可以添加 Map 格式的参数
        //        .build();
        //
        ////然后在 OkHttpClient中添加拦截器就完成了
        //okHttpClient = new OkHttpClient.Builder()
        //        .readTimeout(READ_TIME_OUT, TimeUnit.MILLISECONDS)
        //        .connectTimeout(CONNECT_TIME_OUT, TimeUnit.MILLISECONDS)
        //        .writeTimeout(CONNECT_TIME_OUT, TimeUnit.SECONDS)
        //        .addInterceptor(basicParamsInterceptor) // 添加公共参数拦截器
        //        .addInterceptor(logInterceptor)
        //        .addInterceptor(noNetInterceptor)
        //        .addNetworkInterceptor(cacheInterceptor)
        //        .retryOnConnectionFailure(true)
        //        .cache(cache)
        //        .build();

        //解决jdk7及以下版本默认值支持TLSv1的问题
        //System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2");
        return new OkHttpClient.Builder()
                //设置超时时间
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                //使用fiddler代理查看http请求
                //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1",9999)))
                //信任所有的https证书(如下只是解决了不受信任的证书问题,默认本身就是支持受信任证书的https)
                .sslSocketFactory(createIgnoreVerifySSLSocketFactory(),new TrustAllCerts())
                //实际是ssl主机信任
                .hostnameVerifier((hostname, session) -> true)
                //解决java.io.IOException: unexpected end of stream on unknown的问题
                .retryOnConnectionFailure(true)
                .build();
    }

    /**
     * 生成OkHttpClient新实例
     *
     * @return
     */
    public static OkHttpClient getNewInstanceCookieEnable() {

        //解决jdk7及以下版本默认值支持TLSv1的问题
        //System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2");
        return getNewInstance().newBuilder().cookieJar(new CookieJarImpl(new MemoryCookieStore())).build();
    }

    /**
     * 获取OkHttpClient单例
     *
     * @return
     */
    public static OkHttpClient getSingletonInstance() {
//      懒汉式（避免上面的资源浪费）、线程安全、代码简单。
//      因为java机制规定，内部类SingletonHolder只有在getInstance()方法第一次调用的时候才会被加载（实现了lazy），
//      而且其加载过程是线程安全的（实现线程安全）。内部类加载的时候实例化一次instance。
        return SingletonHolder.instance;
        //.newBuilder()
        //.addInterceptor(new AppendHeaderParamInterceptor())
        //.build();
    }

    /**
     * 类级的内部类，也就是静态的成员式内部类，该内部类的实例与外部类的实例没有绑定关系，
     * 而且只有被调用到才会装载，从而实现了延迟加载
     */
    private static class SingletonHolder {
        /**
         * 静态初始化器，由JVM类加载机制来保证线程安全
         */
        private static OkHttpClient instance = getNewInstance();

    }

    // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
    private static class TrustAllCerts implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] paramArrayOfX509Certificate,
                                       String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] paramArrayOfX509Certificate,
                                       String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    }

    private static SSLSocketFactory createIgnoreVerifySSLSocketFactory() {
        SSLSocketFactory ssfFactory = null;

        try {
            //与服务器保持一致，SSL算法或者TSL算法。
            // 注意如果要用这个来忽略某个网站的证书不受信任的问题,此处的tls版本一定要和服务端一致,可以通过chrome打开网站按F12然后进security面板查看
            //SSLContext sc = SSLContext.getInstance("TLS");
            SSLContext sc = SSLContext.getInstance("TLSv1.2");
            sc.init(null, new TrustManager[]{new TrustAllCerts()}, new SecureRandom());

            ssfFactory = sc.getSocketFactory();
        } catch (Exception e) {
        }

        return ssfFactory;
    }

    public static RequestBody createInputStreamRequestBody(final InputStream stream) {
        return new RequestBody() {
            @Override
            public MediaType contentType() {
                // 设置body mime类型，这里以二进制流为例
                return MediaType.get("application/octet-stream");
            }

            @Override
            public long contentLength() throws IOException {
                // 返回-1表示body长度未知，将开启http chunk支持
                // RequestBody中默认返回也时-1
                return -1;
            }

            @Override
            public void writeTo(BufferedSink sink) throws IOException {
                try (Source source = Okio.source(stream)) {
                    sink.writeAll(source);
                }
            }
        };
    }

    public static void main(String[] args) throws IOException {
        //HttpLoggingInterceptor logInterceptor = new HttpLoggingInterceptor(new HttpLoggingInterceptor.Logger() {
        //    @Override
        //    public void log(@NotNull String s) {
        //        logger.info(s);
        //    }
        //});//创建拦截对象
        //logInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);//这一句一定要记得写，否则没有数据输出

        //String url = "http://www.baidu.com";
        //String url = "https://cdn.bootcss.com/jquery/2.1.4/jquery.min.js";
        //String url = "https://pv.sohu.com/cityjson";
        //String url = "http://*************:1575/ip";
        //String url = "http://httpbin.org/ip";
        //String url = "https://crbt.cdyrjygs.com/cms-vrbt/api/gzyd/syncUrl?mobile=18386152756&channel=GZYD_YDYP_HJHY";
        //String url = "https://httpbin.org/headers";
        //String url = "https://www.zhihu.com/api/v4/members/qian-jiang-12-47/activities?limit=7&session_id=980755116894420992&after_id=1575261976&desktop=True";
        //String url = "https://www.zhihu.com/api/v4/members/qian-jiang-12-47/activities";
        //OkHttpClient client = new OkHttpClient.Builder()
        //        .addInterceptor(logInterceptor)
        //        .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1",9999)))
        //         .build();

        //OkHttpClient client = OkHttpClientUtils.getSingletonInstance()
        //        .newBuilder()
        //        //.addNetworkInterceptor(logInterceptor)
        //        .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 9999)))
        //        .build();

        //HttpUrl u = HttpUrl.parse(
        //        //"https://crbt.cdyrjygs.com/migu_qycl_open/?source=h5&phone=13438828200&departmentName=叶哥的small公司&openid=oDEAK5OIL0lgRLO-FGAhmCBmvxYk"
        //        "https://crbt.cdyrjygs.com/cms-vrbt/api/gzyd/syncUrl?mobile=18386152756&channel=GZYD_YDYP_HJHY"
        //);
        //System.out.println("u = " + u);

        //String dkey = HttpUrl.parse(
        //        "https://crbt.cdyrjygs.com/cpmb_ch_v2/#/?subChannel=CPMB_V2_DMHS&trackParam=aufWMfKrCLqy%2BT4n9nithZYemlATf9wswN%2BB4Z329zyofp4s0IuCiPVVLaQDFFtC&dkey=60e676e14bde21526a582ce9".replace("#/","")).queryParameter("dkey");
        //System.out.println("dkey = " + dkey);



        OkHttpClient client = OkHttpClientUtils.getNewInstance()
                                               .newBuilder()
                                               .build();
        //Request request = new Request.Builder().url(url)
        Request request = new Request.Builder().url("https://crbt.cdyrjygs.com/cms-vrbt/api/gzyd/syncUrl?mobile=18386152756&channel=GZYD_YDYP_HJHY")
                                               //解决java.io.IOException: unexpected end of stream on unknown的问题
                                               //.header("Connection","close")
                                                .post(Util.EMPTY_REQUEST)
                                               .build();
        try (Response response = client.newCall(request)
                                       .execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
            String pageContent = response.body().string();
            System.out.println("pageContent = " + pageContent);
            //ObjectMapper mapper = new ObjectMapper();
            //mapper.enable(SerializationFeature.INDENT_OUTPUT);

        }
    }

}
