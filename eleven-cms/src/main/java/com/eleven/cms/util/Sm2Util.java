package com.eleven.cms.util;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.regex.Pattern;

import cn.hutool.crypto.GlobalBouncyCastleProvider;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.CryptoException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.jcajce.provider.asymmetric.util.ECUtil;
import org.bouncycastle.jcajce.spec.OpenSSHPrivateKeySpec;
import org.bouncycastle.jcajce.spec.OpenSSHPublicKeySpec;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.bouncycastle.util.BigIntegers;
import org.springframework.util.Assert;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * SM2新加解密工具，去除hutool依赖，支持16进制密钥和base64格式密钥
 *
 * <AUTHOR>
 * @version [版本号, 2024/1/19]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Slf4j
public class Sm2Util
{
    //椭圆曲线ECParameters ASN.1 结构
    private static final X9ECParameters x9ECParameters = GMNamedCurves.getByName("sm2p256v1");

    /**
     * 字符常量：斜杠 {@code '/'}
     */
    public static final char SLASH = '/';

    public static final ECDomainParameters SM2_DOMAIN_PARAMS = toDomainParams(x9ECParameters);

    private ECPrivateKeyParameters privateKeyParams;

    private final ECPublicKeyParameters publicKeyParams;

    /**
     * 16进制字符串
     */
    public static final Pattern HEX = Pattern.compile("^[a-fA-F0-9]+$");

    @SneakyThrows
    public Sm2Util(String hexPublicKey, String hexPrivateKey)
    {
        this.publicKeyParams = decodePublicKeyParams(hexPublicKey);
        this.privateKeyParams = decodePrivateKeyParams(hexPrivateKey);
    }
    @SneakyThrows
    public Sm2Util(String hexPublicKey)
    {
        this.publicKeyParams = decodePublicKeyParams(hexPublicKey);
    }
    /**
     * sm2加密算法
     * @param plainData：公钥
     * @param plainData：要加密的字符串
     * @return：加密结果
     */
    public String encrypt(String plainData)
    {
        if (StringUtils.isBlank(plainData))
        {
            return null;
        }
        try
        {
            SM2Engine sm2Engine = MySm2Engine.createMySm2Engine(publicKeyParams, null, MySm2Engine.TYPE_ENCODE);
            //encrypt data
            byte[] in = plainData.getBytes(StandardCharsets.UTF_8);
            byte[] bytes = sm2Engine.processBlock(in, 0, in.length);
            return Base64.getEncoder().encodeToString(bytes);
        }
        catch (Exception e)
        {
            log.error("Sm2CryptTools encrypt is error,data=" + plainData, e);
        }
        return null;
    }

    /**
     * sm2解密算法
     * @param cipherData：私钥
     * @param cipherData：要解密的字符串
     * @return
     */
    public String decrypt(String cipherData)
    {
        try
        {
            if (StringUtils.isBlank(cipherData))
            {
                return null;
            }
            //init engine
            SM2Engine sm2Engine = MySm2Engine.createMySm2Engine(null, privateKeyParams, MySm2Engine.TYPE_DECODE);

            //decrypt data
            byte[] cipherDataByte = decode(cipherData);
            byte[] bytes = sm2Engine.processBlock(cipherDataByte, 0, cipherDataByte.length);
            return new String(bytes, StandardCharsets.UTF_8);
        }
        catch (Exception e)
        {
            log.error("Sm2CryptTools decrypt is error,data=" + cipherData, e);
        }
        return null;
    }

    public static byte[] decode(String key)
    {
        return isMatch(HEX, key) ? hexDecode(key.toLowerCase()) : Base64.getDecoder().decode(key);
    }

    /**
     * sm3验签
     * @param data
     * @param sign
     * @return
     */
    public boolean sm3CheckSign(String data, String sign)
    {
        return sign.equals(sm3Encrypt(data));
    }

    /**
     * sm3算法加密
     * @param data
     * @return
     */
    public static String sm3Encrypt(String data)
    {
        byte[] resultHash = hash(data.getBytes());
        return ByteUtils.toHexString(resultHash);
    }

    /**
     * 返回长度34的byte数组
     * @param srcData
     * @return
     */
    private static byte[] hash(byte[] srcData)
    {
        SM3Digest digest = new SM3Digest();
        digest.update(srcData, 0, srcData.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }

    public static boolean isMatch(Pattern pattern, CharSequence content)
    {
        if (content == null || pattern == null)
        {
            // 提供null的字符串为不匹配
            return false;
        }
        return pattern.matcher(content).matches();
    }

    public static byte[] hexDecode(CharSequence encoded)
    {
        if (null == encoded || encoded.length() == 0)
        {
            return new byte[0];
        }

        int len = encoded.length();

        if ((len & 0x01) != 0)
        {
            // 如果提供的数据是奇数长度，则前面补0凑偶数
            encoded = "0" + encoded;
            len = encoded.length();
        }

        final byte[] out = new byte[len >> 1];

        // two characters form the hex value.
        for (int i = 0, j = 0; j < len; i++)
        {
            int f = toDigit(encoded.charAt(j)) << 4;
            j++;
            f = f | toDigit(encoded.charAt(j));
            j++;
            out[i] = (byte)(f & 0xFF);
        }

        return out;
    }

    /**
     * 将十六进制字符转换成一个整数
     *
     * @param ch    十六进制char
     * @return 一个整数
     */
    private static int toDigit(char ch)
    {
        return Character.digit(ch, 16);
    }

    public static ECDomainParameters toDomainParams(X9ECParameters x9ECParameters)
    {
        return new ECDomainParameters(x9ECParameters.getCurve(), x9ECParameters.getG(), x9ECParameters.getN(),
            x9ECParameters.getH());
    }

    /**
     * 尝试解析转换各种类型私钥为{@link ECPrivateKeyParameters}，支持包括：
     *
     * <ul>
     *     <li>D值</li>
     *     <li>PKCS#8</li>
     *     <li>PKCS#1</li>
     * </ul>
     *
     * @param priKey 私钥
     * @return {@link ECPrivateKeyParameters}
     * @since 5.5.9
     */
    public static ECPrivateKeyParameters decodePrivateKeyParams(String priKey)
        throws CryptoException
    {
        byte[] priBytes = decode(priKey);
        try
        {
            // 尝试D值
            return new ECPrivateKeyParameters(BigIntegers.fromUnsignedByteArray(priBytes),
                Sm2Util.SM2_DOMAIN_PARAMS);
        }
        catch (Exception ignore)
        {
            // ignore
        }

        PrivateKey privateKey;
        //尝试PKCS#8
        try
        {
            privateKey = generatePrivateKey("sm2", new PKCS8EncodedKeySpec(priBytes));
        }
        catch (Exception ignore)
        {
            // 尝试PKCS#1
            privateKey = generatePrivateKey("sm2", new OpenSSHPrivateKeySpec(priBytes));
        }

        return toPrivateParams(privateKey);
    }

    /**
     * 尝试解析转换各种类型公钥为{@link ECPublicKeyParameters}，支持包括：
     *
     * <ul>
     *     <li>Q值</li>
     *     <li>X.509</li>
     *     <li>PKCS#1</li>
     * </ul>
     *
     * @param pubKey 公钥
     * @return {@link ECPublicKeyParameters}
     * @since 5.5.9
     */
    public static ECPublicKeyParameters decodePublicKeyParams(String pubKey)
        throws CryptoException
    {
        byte[] publicKeyBytes = decode(pubKey);
        try
        {
            // 尝试Q值
            return toSm2PublicParams(publicKeyBytes);
        }
        catch (Exception ignore)
        {
            // ignore
        }

        PublicKey publicKey;
        //尝试X.509
        try
        {
            publicKey = generatePublicKey("sm2", publicKeyBytes);
        }
        catch (Exception ignore)
        {
            // 尝试PKCS#1
            publicKey = generatePublicKey("sm2", createOpenSSHPublicKeySpec(publicKeyBytes));
        }

        return toPublicParams(publicKey);
    }

    /**
     * 转换为 ECPublicKeyParameters
     *
     * @param q 公钥Q值
     * @return ECPublicKeyParameters
     */
    public static ECPublicKeyParameters toSm2PublicParams(byte[] q)
    {
        return toPublicParams(q, SM2_DOMAIN_PARAMS);
    }

    /**
     * 转换为ECPublicKeyParameters
     *
     * @param pointEncoded     被编码的曲线坐标点
     * @param domainParameters ECDomainParameters
     * @return ECPublicKeyParameters
     * @since 5.4.3
     */
    public static ECPublicKeyParameters toPublicParams(byte[] pointEncoded, ECDomainParameters domainParameters)
    {
        final ECCurve curve = domainParameters.getCurve();
        return toPublicParams(curve.decodePoint(pointEncoded), domainParameters);
    }

    /**
     * 转换为ECPublicKeyParameters
     *
     * @param point            曲线坐标点
     * @param domainParameters ECDomainParameters
     * @return ECPublicKeyParameters
     * @since 5.4.3
     */
    public static ECPublicKeyParameters toPublicParams(org.bouncycastle.math.ec.ECPoint point,
        ECDomainParameters domainParameters)
    {
        return new ECPublicKeyParameters(point, domainParameters);
    }

    /**
     * 生成公钥，仅用于非对称加密<br>
     * 采用X509证书规范<br>
     *
     * @param algorithm 算法
     * @param key       密钥，必须为DER编码存储
     * @return 公钥 {@link PublicKey}
     */
    public static PublicKey generatePublicKey(String algorithm, byte[] key)
        throws CryptoException
    {
        if (null == key)
        {
            return null;
        }
        return generatePublicKey(algorithm, new X509EncodedKeySpec(key));
    }

    /**
     * 生成公钥，仅用于非对称加密<br>
     *
     * @param algorithm 算法
     * @param keySpec   {@link KeySpec}
     * @return 公钥 {@link PublicKey}
     * @since 3.1.1
     */
    public static PublicKey generatePublicKey(String algorithm, KeySpec keySpec)
        throws CryptoException
    {
        if (null == keySpec)
        {
            return null;
        }
        algorithm = getAlgorithmAfterWith(algorithm);
        try
        {
            return getKeyFactory(algorithm).generatePublic(keySpec);
        }
        catch (Exception e)
        {
            throw new CryptoException(e.getMessage());
        }
    }

    /**
     * 创建{@link OpenSSHPublicKeySpec}
     *
     * @param key 公钥，需为PKCS#1格式
     * @return {@link OpenSSHPublicKeySpec}
     * @since 5.5.9
     */
    public static KeySpec createOpenSSHPublicKeySpec(byte[] key)
    {
        return new OpenSSHPublicKeySpec(key);
    }

    /**
     * 公钥转换为 {@link ECPublicKeyParameters}
     *
     * @param publicKey 公钥，传入null返回null
     * @return {@link ECPublicKeyParameters}或null
     */
    public static ECPublicKeyParameters toPublicParams(PublicKey publicKey)
        throws CryptoException
    {
        if (null == publicKey)
        {
            return null;
        }
        try
        {
            return (ECPublicKeyParameters)ECUtil.generatePublicKeyParameter(publicKey);
        }
        catch (InvalidKeyException e)
        {
            throw new CryptoException(e.getMessage());
        }
    }

    /**
     * 生成私钥，仅用于非对称加密<br>
     *
     * @param algorithm 算法，如RSA、EC、SM2等
     * @param keySpec   {@link KeySpec}
     * @return 私钥 {@link PrivateKey}
     * @since 3.1.1
     */
    public static PrivateKey generatePrivateKey(String algorithm, KeySpec keySpec)
        throws CryptoException
    {
        if (null == keySpec)
        {
            return null;
        }
        algorithm = getAlgorithmAfterWith(algorithm);
        try
        {
            return getKeyFactory(algorithm).generatePrivate(keySpec);
        }
        catch (Exception e)
        {
            throw new CryptoException(e.getMessage());
        }
    }

    /**
     * 获取用于密钥生成的算法<br>
     * 获取XXXwithXXX算法的后半部分算法，如果为ECDSA或SM2，返回算法为EC
     *
     * @param algorithm XXXwithXXX算法
     * @return 算法
     */
    public static String getAlgorithmAfterWith(String algorithm)
    {
        Assert.notNull(algorithm, "algorithm must be not null !");

        if (StringUtils.startsWithIgnoreCase(algorithm, "ECIESWith"))
        {
            return "EC";
        }

        int indexOfWith = StringUtils.lastIndexOf(algorithm, "with");
        if (indexOfWith > 0)
        {
            algorithm = StringUtils.substring(algorithm, indexOfWith + "with".length());
        }
        if ("ECDSA".equalsIgnoreCase(algorithm) || "SM2".equalsIgnoreCase(algorithm)
            || "ECIES".equalsIgnoreCase(algorithm))
        {
            algorithm = "EC";
        }
        return algorithm;
    }

    /**
     * 获取{@link KeyFactory}
     *
     * @param algorithm 非对称加密算法
     * @return {@link KeyFactory}
     * @since 4.4.4
     */
    public static KeyFactory getKeyFactory(String algorithm)
        throws CryptoException
    {
        final Provider provider = GlobalBouncyCastleProvider.INSTANCE.getProvider();

        KeyFactory keyFactory;
        try
        {
            keyFactory = (null == provider) //
                ? KeyFactory.getInstance(getMainAlgorithm(algorithm)) //
                : KeyFactory.getInstance(getMainAlgorithm(algorithm), provider);
        }
        catch (NoSuchAlgorithmException e)
        {
            throw new CryptoException(e.getMessage());
        }
        return keyFactory;
    }

    /**
     * 获取主体算法名，例如RSA/ECB/PKCS1Padding的主体算法是RSA
     *
     * @param algorithm XXXwithXXX算法
     * @return 主体算法名
     * @since 4.5.2
     */
    public static String getMainAlgorithm(String algorithm)
    {
        final int slashIndex = algorithm.indexOf(SLASH);
        if (slashIndex > 0)
        {
            return algorithm.substring(0, slashIndex);
        }
        return algorithm;
    }

    /**
     * 私钥转换为 {@link ECPrivateKeyParameters}
     *
     * @param privateKey 私钥，传入null返回null
     * @return {@link ECPrivateKeyParameters}或null
     */
    public static ECPrivateKeyParameters toPrivateParams(PrivateKey privateKey)
        throws CryptoException
    {
        if (null == privateKey)
        {
            return null;
        }
        try
        {
            return (ECPrivateKeyParameters)ECUtil.generatePrivateKeyParameter(privateKey);
        }
        catch (InvalidKeyException e)
        {
            throw new CryptoException(e.getMessage());
        }
    }

}
