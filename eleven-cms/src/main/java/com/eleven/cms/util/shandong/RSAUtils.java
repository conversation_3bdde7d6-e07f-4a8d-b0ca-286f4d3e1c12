package com.eleven.cms.util.shandong;

import lombok.extern.slf4j.Slf4j;

import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2024/11/18 17:04
 */
@Slf4j
public class RSAUtils {

    private static final String PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALdciygqXKa9U0ZtSdYIQHZcfNI7RS6Ljxzat0dQpw/AvlGf3Bh88fJimM2+rYctJuXkJ+GaK5RX3kH89PZdR4tEhol0kx7Y8gpw2hV+ceq77pnjjreYBmQ+5H1gVh/fYJ4pUUN6EncCo1satjc0DZzOIiNgxYmLswNEZLhaREhpAgMBAAECgYB0mYHYQc/3LA+nilnYpBix67BoU85zgcW4+JBx5iTGTK/54ekpHVMR6ob21tksS5cfu41s1gf+Zj3swnnXrIuQPVMPDrhPY/LpowA499q5CUlHEmyQQoGajbcgCPnaQwy3kJQLCXEMOy0scKs6Fx1XhyCfrA54VUeUakDa2P6OgQJBAOQKPAs3pQCFMxNZRXkPeMXzQuV67icyofWvd0t3u+pVfiBP3vwNwyDkS12EoKlwvOwwGRw6p6IH00873zOWYxkCQQDN1+6IGxSisUY33gXvh8zOZujqdaMUJVjLUCCP+HV7rkxGCtxlbBTU8TtaiveD6h2uoLksPznHz2561KxYvYnRAkB6a+AmKX5EVD3CQmeggSTWKC0eoysdEfqOud8yPZo9SB5I3HgaGo1JqCWpk9zoeeSCa6PfWsHJo6fWXo8J089pAkEAkSyieXGH+zpFyK8YxpejNUrVKE6xJks0taecX42e5gzGlHuidxFkG87Kyo/KA0pBhuCp2G/7A1XjmFOX/bonkQJAcWfczQC5C33s0xMqJUDHcjXEUg2/xpBmWvLKRBC8mvI8Ti+ZhodwUZFu053o52NqYEv2K2cNLoE68tTq1dQE5Q==";

    public static String sign(byte[] data) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(PRIVATE_KEY);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
            Signature signature = Signature.getInstance("MD5withRSA");
            signature.initSign(priKey);
            signature.update(data);
            return Base64.getEncoder().encodeToString(signature.sign());
        } catch (Exception e) {
            log.error("sign error!", e);
        }
        return "";
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        keyPairGen.initialize(1024);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        Map<String, Object> keyMap = new HashMap<String, Object>(2);

        keyMap.put("RSAPublicKey", (Base64.getEncoder().encodeToString(publicKey.getEncoded())));
        keyMap.put("RSAPrivateKey", (Base64.getEncoder().encodeToString(privateKey.getEncoded())));
        System.out.println(keyMap);
    }
}
