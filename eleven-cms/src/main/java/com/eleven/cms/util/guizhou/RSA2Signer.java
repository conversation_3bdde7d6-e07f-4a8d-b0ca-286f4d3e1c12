package com.eleven.cms.util.guizhou;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/*
        类型:Signature，算法：NONEwithRSA
        类型:Signature，算法：SHA1withRSA
        类型:Signature，算法：SHA256withRSA
        类型:Signature，算法：SHA384withRSA
        类型:Signature，算法：SHA512withRSA
        类型:Signature，算法：MD5withRSA
        类型:Signature，算法：MD2withRSA
        */
public class RSA2Signer {

    /**
     * 本签名方法是否实现自带摘要，使用哪个算法进行摘要？
     * */
    public static String sign(String paramString, String privateKey) throws Exception {
        PrivateKey priKey = getPrivateKeyFromPKCS82("RSA", new ByteArrayInputStream(privateKey.getBytes("UTF-8")));
        /*
        类型:Signature，算法：NONEwithRSA
        类型:Signature，算法：SHA1withRSA
        类型:Signature，算法：SHA256withRSA
        类型:Signature，算法：SHA384withRSA
        类型:Signature，算法：SHA512withRSA
        类型:Signature，算法：MD5withRSA
        类型:Signature，算法：MD2withRSA
        */
        Signature signature = Signature.getInstance("NONEwithRSA");//SHA256WithRSA
        signature.initSign(priKey);
        signature.update(paramString.getBytes("UTF-8"));
        byte[] signed = signature.sign();
        return Base64.getEncoder().encodeToString(signed);
    }

    public static String signBySHA256WithRSA(String paramString, String privateKey) throws Exception {
        PrivateKey priKey = getPrivateKeyFromPKCS8("RSA", new ByteArrayInputStream(privateKey.getBytes("UTF-8")));

        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(priKey);
        signature.update(paramString.getBytes("UTF-8"));
        byte[] signed = signature.sign();
        return Base64.getEncoder().encodeToString(signed);
    }

    public static String signByMD5withRSA(String paramString, String privateKey) throws Exception {
        PrivateKey priKey = getPrivateKeyFromPKCS82("RSA", new ByteArrayInputStream(privateKey.getBytes("UTF-8")));

        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initSign(priKey);
        signature.update(paramString.getBytes("UTF-8"));
        byte[] signed = signature.sign();
        return Base64.getEncoder().encodeToString(signed);
    }

    /**
     * 本方法 获取私钥的的算法为RSA2时，使用的格式是哪种，是否有密码
     * */
    public static PrivateKey getPrivateKeyFromPKCS8(String algorithm, InputStream inputStream) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = inputStream.read(buff, 0, 100)) > 0) {
            byteArrayOutputStream.write(buff, 0, rc);
        }
        byte[] key = Base64.getDecoder().decode(byteArrayOutputStream.toByteArray());
        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(key));

//        byte[] encodedKey = StreamUtil.readText(inputStream).getBytes();
//        encodedKey = Base64.getDecoder().decode(encodedKey);
//        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
    }



    public static boolean verify(String paramString, String publicKey, String sign) throws Exception {
        PublicKey pubKey = getPublicKeyFromX509("RSA", new ByteArrayInputStream(publicKey.getBytes()));
        Signature signature = Signature.getInstance("NONEwithRSA");//SHA256WithRSA
        signature.initVerify(pubKey);
        signature.update(paramString.getBytes("UTF-8"));
        return signature.verify(Base64.getDecoder().decode(sign.getBytes()));
    }

    public static String sign2(String paramString, String privateKey) throws Exception {
        PrivateKey priKey = getPrivateKeyFromPKCS82("RSA", new ByteArrayInputStream(privateKey.getBytes("UTF-8")));
        Signature signature = Signature.getInstance("NONEwithRSA");//SHA256WithRSA
        signature.initSign(priKey);
        signature.update(paramString.getBytes("UTF-8"));
        byte[] signed = signature.sign();
        return new String(Base64.getEncoder().encode(signed),"UTF-8");
    }

    public static PublicKey getPublicKeyFromX509(String algorithm, InputStream ins) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        StringWriter writer = new StringWriter();
        StreamUtil.io(new InputStreamReader(ins), writer);
        byte[] encodedKey = writer.toString().getBytes();
        encodedKey = Base64.getDecoder().decode(encodedKey);
        return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
    }

    public static PrivateKey getPrivateKeyFromPKCS82(String algorithm, InputStream ins) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        byte[] encodedKey = StreamUtil.readText(ins).getBytes();
        encodedKey = Base64.getDecoder().decode(encodedKey);
        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
    }


    public static void main(String[] args)  throws Exception {
        String paramString = "sssf不中广核还";
        String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCOLcELFGOTDwNzrZ4+67bZoySXARlf8K4sAfA6nYaZheodNXuQSkoOJ6hxY7uXbJzii423ZmhdMqForDP3brI2gz4IJmRhE+V7IqApBpKBdl4bhIdmfSde21jKvYHV+40CBvlpKdpOHRm+a8/s3upekc7bNlZC404TRAlXq0HchIt7PxHrZgexHXNuA8YKARBk83O10AMQL9kdCQ8toPFRsg0yRiHIskkgb1fvsxp2x2rWF93d6LRIcFA1lLyRMVBkYh5qryQ5K2jzL+BC/oEt8NR4rzBmT0JMZCAVZnVm5SeLl28R1yrbyYVWi9fEK81shQvdnxPwwgD3gl78mGhZAgMBAAECggEAI1wXtZS1iv5h4mQchhODXy8my3NXhzfJaWJ2B23j6syWQ/toJqODNXABgWnHXsqBXgpNDX6nl/4ejHS/W9so8UvEoqxBrK8RFIWxBWMADriMgRV9wyx/HSU6OPnWj4xHLmjjoJbrFyA/pc8UYBm0eh6TLoud+najlUEHfck20Qu5sQEnIRw1jScqTeSLGmbSIUaUPieGenKY4IGTw94cZ1jZRL3bOcaMkXcxS6I7c1eIptimpwGhh2LfSLQQzogBvb2b7Hmsh35MrQEKG9ECJAm2WYClpODI9yWlC+1gLShFjMZs6I1+WjBAgSPg6eGbHgOxqny1ZfyGtTl5Dk6hgQKBgQDgCsz2TYBKBiXc4MW76SNEb3ro6qs1qFMzT3s2WBtcoob/wG/puKoj/iJEJUUgrdTDe2VTsAudlBS/NXvKbtEcsNQOvsM8NyI27n+bxRdI6ut5ZyTNcPxK8vXJH9IvHFuaVln+1QxZwwM/axLk+ykLTFd7QAm7JRpaxcQB5IggyQKBgQCidZmTgBDEKiWh4AyghNLbTXaSGepZPbBVprBpqBW7DEm3tZu6gsFp0Xar/UNg35DBW7atrmKl8oiKDJAaWWYGWFN/O8CKZQldkVAsaoD8p6ueVgVZrue5i56OOK+2UTCYO7IcdZysMQgSiamDEZkVyM5HV4rTR4x/WyilIJzjEQKBgQDXIGfYpWhnK+oVYltb6SwtHWKG3jaR6lQhs9MaxpVXaNR6JX0QjnoI35BloOf0zhW7svQD3Mn6gGM/iwqXwbrXcs/cbL4pRsN6vZ4Yo7KNGM4Mn8bN4gzPpuXtPrni9LbcxsfZkxct5bxrm9PjvWuW5rIlrcLqSWE6AiYIrzkrSQKBgC4spJjSxnEq1YVby9r3xYKFUtUVvpDcwrilZvCw0HRYer9XQ8rQDS8JVORvWG45PMhoS9SzguBNvdcW2QJUNHZLNngsQH5VV0Mb4SCuqLim41Ved6jU2JHZCBi+Ldrw2pH6LZZ/KyeZzASQ0fgp0Uvqxg5q6E9Cv8EW5MeCLHUxAoGBAJGQwXvIgqV1Zx5GWZnife9wg5erw95B+qv1dJ0oy6JXv7yR/qbMMkOQzxBIJ+C5ZxAZsqfuMeRVN1DL3UY38+6scVRhxqRsa6H5Amu6SXoUW6RLGQTU3dEqM2cjT8yf2XT74O/PamVtcaKK9TyKlwWTMvLHLrGprpP596fF6vhP";
        System.out.println(paramString);
        String encodedBase64 = sign(paramString,privateKey);
        System.out.println(encodedBase64);
    }
}
