package com.eleven.cms.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @author: cai lei
 * @create: 2021-09-18 11:55
 */
public class SichunMobileSignUtil {

    public static String generateSign(String privateKey, String raw) throws Exception {
        try {
            //第一步 拼接secret
            raw = "secret" + raw + "secret";
            //第二步 将上一步得到的字符串做urlencode操作，字符集采用utf-8；
            raw = URLEncoder.encode(raw, StandardCharsets.UTF_8.name());
            //第三步 将上一步得到的字符串进行md5运算，得到全小写的32位字符串；
            raw = DigestUtils.md5DigestAsHex(raw.getBytes(StandardCharsets.UTF_8.name()));
            //第四步进行ras 签名
            raw = RSAUtils.sign(raw.getBytes(), privateKey);
            //第五步 加密后的值做urlencode操作，字符集采用utf-8
            return URLEncoder.encode(raw, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            throw e;
        }
    }
}
