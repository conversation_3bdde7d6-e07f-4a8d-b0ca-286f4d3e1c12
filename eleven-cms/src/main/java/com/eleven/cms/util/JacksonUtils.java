package com.eleven.cms.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/10/8 16:54
 */
public class JacksonUtils {

    private static final ObjectMapper om = new ObjectMapper();

    /**
     * createObjectNode
     *
     * @return ObjectNode
     */
    public static ObjectNode createObjectNode() {
        return om.createObjectNode();
    }

    /**
     * createArrayNode
     *
     * @return ArrayNode
     */
    public static ArrayNode createArrayNode() {
        return om.createArrayNode();
    }

    /**
     * toJson
     *
     * @param obj obj
     * @return String
     */
    public static String toJson(Object obj) {
        try {
            return om.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * readTree
     *
     * @param content content
     * @return JsonNode
     */
    public static JsonNode readTree(String content) {
        try {
            return om.readTree(content);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * readValue
     *
     * @param content content
     * @param t       t
     * @param <T>
     * @return T
     */
    public static <T> T readValue(String content, Class<T> t) {
        try {
            return om.readValue(content, t);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T treeToValue(JsonNode node, Class<T> clazz) {
        try {
            return om.treeToValue(node, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> parseList(String param, Class<T> clazz) {
        return om.convertValue(readTree(param), om.getTypeFactory().constructCollectionType(List.class, clazz));
    }
}
