//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.eleven.cms.util.henan;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Locale;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

public final class SecurityUtils {

    static {
        if(null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)){
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    private SecurityUtils() {
    }

    public static byte[] initAES256Key() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(256);
        SecretKey secretKey = keyGenerator.generateKey();
        return secretKey.getEncoded();
    }

    public static byte[] initHmacSHA256Key() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("HmacSHA256");
        keyGenerator.init(256);
        SecretKey secretKey = keyGenerator.generateKey();
        return secretKey.getEncoded();
    }

    public static byte[] encodeHmacSHA256(byte[] data, byte[] key) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKey secretKey = new SecretKeySpec(key, "HmacSHA256");
        Mac mac = Mac.getInstance(secretKey.getAlgorithm());
        mac.init(secretKey);
        return mac.doFinal(data);
    }

    public static String encodeHmacSHA256HexUpper(String data, byte[] key) throws UnsupportedEncodingException, InvalidKeyException, NoSuchAlgorithmException {
        return AESUtil.bytesToHexString(encodeHmacSHA256(data.getBytes("UTF-8"), key)).toUpperCase(Locale.US);
    }

    public static byte[] encrypt(byte[] data, byte[] key) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        //Security.addProvider(new BouncyCastleProvider());
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
        cipher.init(1, new SecretKeySpec(key, "AES"));
        return cipher.doFinal(data);
    }

    public static byte[] decrypt(byte[] data, byte[] key) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        //Security.addProvider(new BouncyCastleProvider());
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
        cipher.init(2, new SecretKeySpec(key, "AES"));
        return cipher.doFinal(data);
    }

    public static String encodeHexUpper(byte[] data) throws UnsupportedEncodingException {
        return AESUtil.bytesToHexString(data).toUpperCase(Locale.US);
    }

    public static byte[] decodeHexUpper(String str) throws UnsupportedEncodingException {
        return Hex.decode(str.toLowerCase(Locale.US));
    }

    public static String decodeHexUpper(String str, String charsetName) throws UnsupportedEncodingException {
        return new String(Hex.decode(str.toLowerCase(Locale.US)), charsetName);
    }

    public static String encodeAES256HexUpper(String data, byte[] key) throws UnsupportedEncodingException, IllegalBlockSizeException, InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException {
        return encodeHexUpper(encrypt(data.getBytes("UTF-8"), key));
    }

    public static String decodeAES256HexUpper(String data, byte[] key) throws IllegalBlockSizeException, InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException, UnsupportedEncodingException {
        return new String(decrypt(Hex.decode(data.toLowerCase(Locale.US)), key), "UTF-8");
    }

    public static String encryptByPublicKey(String source, String publicKey) throws Exception {
        return RSAUtils.encryptByPublicKey(source, publicKey);
    }

    public static String decryptByPublicKey(String encrypted, String publicKey) throws Exception {
        return RSAUtils.decryptByPublicKey(encrypted, publicKey);
    }

    public static void main(String[] args) throws Exception {
        String s = "71A85BBAFE19AD58B60D0608D470ED6516D9ECB1C5E36E9C234C616A734866FA16E6CC45E928B762ABA78D56D7DD06AFDD37AD0626F24C0BA50AD02C0E1EBA23C6AC48AE8AEFC1A3DDCF5DF7D234B7E4A0F9A9A37F2FAE44720E563187EC39F50CCE27788160361AE1BB457A8B2A0BA85AAFAFF4DA8D98F4AF8ACE5308E9C32528BFA24CA74A726141DD51216996A6E9";
        String key = "91fc1ca202f3791689cf587f23cf871c";
    }
}
