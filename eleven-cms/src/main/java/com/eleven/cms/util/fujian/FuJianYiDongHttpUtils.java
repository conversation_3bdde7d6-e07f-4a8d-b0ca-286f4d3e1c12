package com.eleven.cms.util.fujian;

import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.eleven.cms.dto.FuJianOrderDTO;
import com.eleven.cms.dto.FuJianSmsCodeGetDTO;
import com.eleven.cms.util.JacksonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;

import java.util.Map;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;
import static com.eleven.cms.aivrbt.utils.HttpUtils.SYNC_ORDER_CLIENT;
import static org.springframework.util.MimeTypeUtils.APPLICATION_JSON_VALUE;

/**
 * <AUTHOR>
 * @datetime 2024/12/3 14:59
 */
public class FuJianYiDongHttpUtils {

    private static final String CHANNEL_KEY = "QBXYm5RB01ugWumEG/+WoKdYCHJC5klc0lj2f28mg+VR+DNukLEyEUYk1STTbIfOADdGKnkM9xlIGllVKuaJS6YXiWFGYRzaLnW64i5phkgWMeok3Du249rf1yETNLlEaAhRdlSUZAs/7IOYmx5Gk89AWs6gRrWFRW4XaT4qRIUM69uNse/Z465GDrg1HQ/sZeEu4R+tiA2vSaONobUiW1N3T2seHVcHEQ+VJ1fj4CbolQUIQkIlKiVQq15qWoRLOx2FITUkqdYY1YyaDoG1WWbbvjKwdFjp7Tf+8gJ08CGngJsgSo9XC3yclGrl/vdEd61bexlaX96LKJi6NAyWng==";
    private static final String CHANNEL_ID = "cpp-sC-api-schstj";
    private static final MediaType JSON = MediaType.parse(APPLICATION_JSON_VALUE);
    private static final RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);

    private static String getToken(String logTag, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://app.fmcc.com.cn/ywfx/sso/auth/getToken");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("channelId", CHANNEL_ID);
        objectNode.put("channelKey", CHANNEL_KEY);
        objectNode.put("nonce", String.valueOf(System.currentTimeMillis()));
        RequestBody body = RequestBody.create(JSON, objectNode.toString());

        String result = HttpUtils.post(COMMON_CLIENT, url, body, logTag, "省侧-认证鉴权", extraInfoMap);
        JsonNode resultNode = JacksonUtils.readTree(result);
        return resultNode.at("/loginToken/cppToken").textValue();
    }

    private static String getValidToken(String logTag, Map<String, Object> extraInfoMap) {
        String key = "fuJian:yiDong:fuNuo:token";
        if (redisUtil.hasKey(key)) {
            return (String) redisUtil.get(key);
        }
        String token = getToken(logTag, extraInfoMap);
        redisUtil.set(key, token, 60L * 10);
        return token;
    }

    public static String getSmsCode(FuJianSmsCodeGetDTO fuJianSmsCodeGetDTO, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://app.fmcc.com.cn/ywfx/funo/salecase/sendSmsCode");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("cppToken", getValidToken(fuJianSmsCodeGetDTO.getLogTag(), extraInfoMap));
        objectNode.put("productName", fuJianSmsCodeGetDTO.getLogTag());
        objectNode.put("saleId", fuJianSmsCodeGetDTO.getSaleId());
        objectNode.put("mobile", fuJianSmsCodeGetDTO.getMobile());
        objectNode.put("channelId", CHANNEL_ID);
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(COMMON_CLIENT, url, body, fuJianSmsCodeGetDTO.getLogTag(), "省侧-业务下发短信验证码", extraInfoMap);
    }

    public static String order(FuJianOrderDTO fuJianOrderDTO, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://app.fmcc.com.cn/ywfx/funo/salecase/orderSalecase");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("cppToken", getValidToken(fuJianOrderDTO.getLogTag(), extraInfoMap));
        objectNode.put("smsCode", fuJianOrderDTO.getSmsCode());
        objectNode.put("mobile", RSAUtils.encrypt(fuJianOrderDTO.getMobile()));
        objectNode.put("saleId", RSAUtils.encrypt(fuJianOrderDTO.getSaleId()));
        objectNode.put("channelId", CHANNEL_ID);
        objectNode.put("type", 2);
        objectNode.put("agentCode", fuJianOrderDTO.getAgentCode());
        objectNode.put("oid", fuJianOrderDTO.getOid());
        objectNode.put("productUrl", fuJianOrderDTO.getProductUrl());
        objectNode.put("firstContact", fuJianOrderDTO.getFirstContact());
        objectNode.put("secondContact", fuJianOrderDTO.getSecondContact());
        objectNode.put("thirdContact", fuJianOrderDTO.getThirdContact());
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(SYNC_ORDER_CLIENT,url, body, fuJianOrderDTO.getLogTag(), "省侧-受理营销案业务", extraInfoMap);
    }
}
