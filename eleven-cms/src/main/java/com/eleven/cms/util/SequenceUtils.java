package com.eleven.cms.util;

/**
 * @author: cai lei
 * @create: 2021-09-18 16:26
 */
public class SequenceUtils {

    static final int DEFAULT_LENGTH = 6;

    public static String getSequence(long seq) {
        String str = String.valueOf(seq);
        int len = str.length();
        if (len >= DEFAULT_LENGTH) {// 取决于业务规模,应该不会到达6
            return str;
        }
        int rest = DEFAULT_LENGTH - len;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < rest; i++) {
            sb.append('0');
        }
        sb.append(str);
        return sb.toString();
    }
}
