package com.eleven.cms.util;

import com.eleven.cms.aiunion.utils.TencentCloudAIParamHandleUtils;
import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.jeecg.common.exception.JeecgBootException;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;

/**
 * 腾讯云AI
 *
 * <AUTHOR>
 * @datetime 2024/10/28 14:05
 */
@Slf4j
public class TencentCloudAPIUtils {

    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    public static final String HOST = "ims.tencentcloudapi.com";
    public static final String CONTENT_TYPE = "application/json; charset=utf-8";
    public static final String REGION = "ap-beijing";
    public static final String LOG_TAG = "公共安全服务";

    public static final String PIC_CONTENT_VERSION = "2020-12-29";
    public static final String PIC_CONTENT_ACTION = "ImageModeration";

    /**
     * 分页查询-素材列表
     *
     * @param picUrl picUrl
     * @return String
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     * @throws InvalidKeyException      InvalidKeyException
     */
    public static String picContentSecurity(String picUrl) {
        HttpUrl url = HttpUrl.parse("https://" + HOST);
        HashMap<Object, Object> params = new HashMap<Object, Object>() {
            {
                put("FileUrl", picUrl);
            }
        };
        String json = JacksonUtils.toJson(params);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        Headers headers = new Headers.Builder()
                .add("Host", HOST)
                .add("X-TC-Timestamp", timestamp)
                .add("X-TC-Version", PIC_CONTENT_VERSION)
                .add("X-TC-Action", PIC_CONTENT_ACTION)
                .add("X-TC-Region", REGION)
                .add("X-TC-Token", "")
                .add("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
                .add("Authorization", TencentCloudAIParamHandleUtils.getAuth(HOST, CONTENT_TYPE, timestamp, json))
                .build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "腾讯云_图片安全");
    }

    /**
     * Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过
     * @param response  response
     * @return
     */
    public static Boolean parsePicContentSecurityResponse(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        JsonNode responseNode = rootNode.path("Response");

        if (responseNode != null && responseNode.has("Error")) {
            throw new JeecgBootException(responseNode.get("Error").get("Message").asText());
        }


        if (responseNode.has("Suggestion")) {
            JsonNode resultNode = responseNode.path("Suggestion");
            return "Pass".equals(resultNode.asText());
        }
        throw new JeecgBootException("内容安全识别错误，请重试！");
    }
}
