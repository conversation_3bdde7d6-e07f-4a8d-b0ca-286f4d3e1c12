package com.eleven.cms.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import java.security.*;
import java.text.ParseException;
import java.util.Base64;

/**
 * @author: cai lei
 * @create: 2021-11-30 10:01
 */
@Slf4j
public class DesUtils {
    private static Logger logger = LoggerFactory.getLogger(DesUtils.class);
    private static final String DES = "DES";

    public static String encryptByte(byte[] byteS,String key) {
        byte[] byteFina = null;
        Cipher cipher;
        try {
            cipher = Cipher.getInstance(DES);
            cipher.init(Cipher.ENCRYPT_MODE, getKey(key));
            byteFina = cipher.doFinal(byteS);
            return new String(org.apache.commons.codec.binary.Base64.encodeBase64(byteFina));
        } catch (Exception e) {
            log.error("Exception:", e);
            return null;
        }
    }

    public static byte[] decryptByte(byte[] byteD,String key) {
        Cipher cipher;
        byte[] byteFina = null;
        try {
            cipher = Cipher.getInstance(DES);
            cipher.init(Cipher.DECRYPT_MODE, getKey(key));
            byteFina = cipher.doFinal(byteD);
        } catch (Exception e) {
            log.error("Exception:", e);
            return null;
        }
        return byteFina;
    }

    private static Key getKey(String strKey) {
        try {
            SecureRandom _secureRandom = SecureRandom.getInstance("SHA1PRNG");
            _secureRandom.setSeed(strKey.getBytes());
            KeyGenerator kg = null;
            kg = KeyGenerator.getInstance(DES);
            kg.init(_secureRandom);
            return kg.generateKey();
        } catch (Exception e) {
            log.error("Exception:", e);
            return null;
        }
    }

}
