package com.eleven.cms.util;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.stream.Collectors;

import static javax.xml.crypto.dsig.SignatureMethod.HMAC_SHA1;

/**
 * @author: cai lei
 * @create: 2022-04-13 17:03
 */
@Slf4j
public class RSA256Utils {

    // MAX_DECRYPT_BLOCK应等于密钥长度/8（1byte=8bit），所以当密钥位数为2048时，最大解密长度应为256.
    // 128 对应 1024，256对应2048
    private static final int KEYSIZE = 2048;

    // RSA最大加密明文大小
    private static final int MAX_ENCRYPT_BLOCK = 117;

    // RSA最大解密密文大小
    private static final int MAX_DECRYPT_BLOCK = 128;

    // 不仅可以使用DSA算法，同样也可以使用RSA算法做数字签名
    private static final String KEY_ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";

    /**
     *
     * 用私钥对信息进行数字签名
     *
     * @param data       加密数据
     *
     * @param privateKey 私钥-base64加密的
     *
     * @return
     *
     * @throws Exception
     *
     */
    public static String signByPrivateKey(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = decryptBASE64(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory factory = KeyFactory.getInstance(KEY_ALGORITHM);
        PrivateKey priKey = factory.generatePrivate(keySpec);// 生成私钥
        // 用私钥对信息进行数字签名
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(data);
        return encryptBASE64(signature.sign());
    }

    /**
     *
     * BASE64Encoder 加密
     *
     * @param data 要加密的数据
     *
     * @return 加密后的字符串
     *
     */
    private static String encryptBASE64(byte[] data) {
        return new String(Base64.encodeBase64(data));
    }

    private static byte[] decryptBASE64(String data) {
        return Base64.decodeBase64(data);
    }

    ///**
    // * RSA公钥加密
    // *
    // * @param str       加密字符串
    // * @param publicKey 公钥
    // * @return 密文
    // * @throws Exception 加密过程中的异常信息
    // */
    //public static String encryptByPublicKey(String str, String publicKey) throws Exception {
    //    // base64编码的公钥
    //    byte[] keyBytes = decryptBASE64(publicKey);
    //    RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance(KEY_ALGORITHM).generatePublic(new X509EncodedKeySpec(keyBytes));
    //    // RSA加密
    //    Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
    //    cipher.init(Cipher.ENCRYPT_MODE, pubKey);
    //    byte[] data = str.getBytes("UTF-8");
    //    // 加密时超过117字节就报错。为此采用分段加密的办法来加密
    //    byte[] enBytes = null;
    //    for (int i = 0; i < data.length; i += MAX_ENCRYPT_BLOCK) {
    //        // 注意要使用2的倍数，否则会出现加密后的内容再解密时为乱码
    //        byte[] doFinal = cipher.doFinal(ArrayUtils.subarray(data, i, i + MAX_ENCRYPT_BLOCK));
    //        enBytes = ArrayUtils.addAll(enBytes, doFinal);
    //    }
    //    return encryptBASE64(enBytes);
    //}

    //签名校验
    public static boolean verify(byte[] json, String publicKey, String sign){
        try{
            X509EncodedKeySpec keySpec =new X509EncodedKeySpec(Base64.decodeBase64(publicKey));

            KeyFactory factory = KeyFactory.getInstance(KEY_ALGORITHM);
            PublicKey key = factory.generatePublic(keySpec);

            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(key);
            signature.update(json);

            return signature.verify(Base64.decodeBase64(sign));
        }catch(Exception e){
        }

        return false;
    }

    //签名校验
    public static boolean verify(String data, String publicKey, String sign){
        byte[] json = data.getBytes(StandardCharsets.UTF_8);

        return verify(json,publicKey,sign);
    }

    public static void main(String[] args) throws Exception {
        String privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCfTQV/tGHn0XJY9clqfnBwk4Lu3Urq3JAduoT9Wb6J/WjyK07dYC+uJNjPHcgg3ZZ2Ypzd7CkZNVB6SRjY8lXg8qNdsx9GYqMwbfimDhSD63SKZ5jxsbD+k9bnZW76fyXIUr62DdI/i0Ypt0nwBSYYHXLk18RcvM7unfJPuV/AWkdmJqIpm/0SH3F2FkN+7hvzMJNX9Z+iHABRoivg+TjMY4nZsysiLGPvrtDTBrjLvn8KWurJ9GnqgDNZLHXH4PVNxmSnmJXGAcd7pmYQN4lrticwmH5uWaQ9HdnU8eI5QCSVG/j+vDwWlyr4DCC33OaRXK2Md0Ub5c68es43BbUVAgMBAAECggEAYH8UjKYlBHsATFke2E6dJ+SDVNRh9GEFP6zjsGBzpj0GHGQVks4YM+IIH3ZH63ivEylrIpdS0f1Usa6jyY8KbIguY60EjetkNQr4qwYucTbo0ooswIPF1oCRlnwAPOdWaMGO2tMGgA2Kw+xOcoF78PGtzmfRf+ezZiHxTU/aa7aPhpY+Y8lvsBoVgwHTvZVh/SavaC4HuqUnhGbHyl0d2xGbzsvCTKRVux4PfXvItrXzptJohaDD5pMdQXHC6w8y97ldVJPjCKN+obaZKzeuh300ZNRgddmif3lWk6wtC7XGUsCGhkv/SV35rsxonp+Z/g3MRBg26hnY9SKrJnBLhQKBgQDNVEb5ak3hYjx/7VNB3DBXuKU7e0H6LreAwOFevbSOHFL4lXSn7GlN8dDdE9CXoRtL0DefF//BtsW6vdDprN6jjK7zkf0C9n9zxVbMMiGt+8LhogJIf03ewnOo5q6AVU1sYCty/ZfgO1NgHoyvypvXmFql5qeMe5Fh3bn0oUEX+wKBgQDGnOR4TKArRQkxMy5h5YpuvLW7cpNW+xH+rNjxgASZZ430D9OxLdA5wJThRludEl+1HwcjovyfvvBmtw3NMQ5JotU8ACH55LBoCwLGKeOFiRADd0BDSyzqIcKSm0DA0eS2kMLVeIUsV2dXwFduiL75kNikJCXXWFO8IrDhrV+KLwKBgE5Skow7b5/EoD1QIw0hV9NuXKD24HD1dwwHBzDjfkcjKMcoA4q2lGimEDz0fQIJ82aU81KdjfKvrHcSRumMBpUuaeIBUxjZPD7GDNjxWoWKXolBCkvrO6H4XLQV4oSThWgMuyYu88sV9jTZEmNQDuhLdnCILmtZC1jggjl9dV4XAoGAXuFck8unSIR2W7ajlcjeVQB5oM6fbJIMiqlwCg6qI87AlbNp4d6M9sn1VZkS8vpwFkp4UqHuJTJGDgus4DVrKepMC5nUoViKnWmFLb2dH5HWwJEbdgpXrpHqLbdVO8gJht2o9enZVNF8YQNVwUw6T2bO17sdCL79YYcBEQ6N8akCgYBNVdxfz20AbDzKDC40zT7CfERHjyNHa/VJMIb5uqRx+XQZogum+z8Ls/lwHfKTkfk4ik9hGrssK3VE6zsKVPp1h9zbjub+ohBIUt2Wg/Dm9VSm0mn7VK++vhyj0gaNKElVi5MZW57VsQ5wzNZYSg6YAFP38SGd0q7Gy0yMPw1hOA==";
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn00Ff7Rh59FyWPXJan5wcJOC7t1K6tyQHbqE/Vm+if1o8itO3WAvriTYzx3IIN2WdmKc3ewpGTVQekkY2PJV4PKjXbMfRmKjMG34pg4Ug+t0imeY8bGw/pPW52Vu+n8lyFK+tg3SP4tGKbdJ8AUmGB1y5NfEXLzO7p3yT7lfwFpHZiaiKZv9Eh9xdhZDfu4b8zCTV/WfohwAUaIr4Pk4zGOJ2bMrIixj767Q0wa4y75/ClrqyfRp6oAzWSx1x+D1TcZkp5iVxgHHe6ZmEDeJa7YnMJh+blmkPR3Z1PHiOUAklRv4/rw8Fpcq+Awgt9zmkVytjHdFG+XOvHrONwW1FQIDAQAB";
        String data = "channelCode=304&orderDate=20220422161748&orderItemId=1650615468213729&phoneNumber=15151811112&productCode=6144&transType=1";
        String sign = "WwgdKmRObZqTWntlJYO2cCtaexpFjU/Q4+QAHOgJ9DKqwcaWUfSPJ7NY8r+FTHLxwLaU8Q0YytZ/0Qrl1EBCBPa6VNm8MMb9FXpes+UndMIL2iYoVrljEZoRIBQVrQPWsXC09K7SHZoUyYYybrK1bMiZVdVqgoBX4W0LUDlXDujGXydhusyAe2tvhQaoFuC+GSjY4bp2emPxU4KvPP8B93z+2G+7mk71N2Ueuitz31XX20j6k9iSWO8grPNkD9IklXCUoANCANjYPJsmsO7EBoRO9R4L/6KcOwkwKjMqMSoNXBoKAj5PMK6MRYedebm4LfjVUey6VPLH1CXrq3gQ6A==";

        String s = signByPrivateKey(data.getBytes(), privateKey);
        System.out.println("s = " + s);
        boolean verifyResult = verify(data, publicKey, sign);
        System.out.println("verifyResult = " + verifyResult);
    }

    public static String generateSign(Map<String, Object> params,String secret) {
        TreeMap<String, String> map = new TreeMap<String, String>();
        Iterator<String> itParam = params.keySet().iterator();
        while (itParam.hasNext()) {
            String name = itParam.next();
            map.put(name,  params.get(name).toString());
        }
        String signStr = "";
        Iterator<String> it = map.keySet().iterator();
        while (it.hasNext()) {
            String name = it.next();
            String value = map.get(name);
            signStr = signStr + name + "=" + value + "&";
        }
        signStr = signStr.substring(0, signStr.length() - 1);
        return generateMacSignature(secret, signStr);
    }
    public static String generateSignSort(Map<String, Object> params,String secret) {
        params=params.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,(oleValue, newValue) -> oleValue, LinkedHashMap::new));
        TreeMap<String, String> map = new TreeMap<String, String>();
        Iterator<String> itParam = params.keySet().iterator();
        while (itParam.hasNext()) {
            String name = itParam.next();
            map.put(name,  params.get(name).toString());
        }
        String signStr = "";
        Iterator<String> it = map.keySet().iterator();
        while (it.hasNext()) {
            String name = it.next();
            String value = map.get(name);
            signStr = signStr + name + "=" + value + "&";
        }
        signStr = signStr.substring(0, signStr.length() - 1);
        return getHmacSHA1(signStr, secret);
    }

    public static String getHmacSHA1(String data, String secret) {
        try {
           Mac mac = Mac.getInstance("HmacSHA1");
           SecretKeySpec spec = new SecretKeySpec(secret.getBytes("utf-8"), "HmacSHA1");
           mac.init(spec);
           byte[] byteHMAC = mac.doFinal(data.getBytes("utf-8"));
           return new String(Base64.encodeBase64(byteHMAC));
        } catch (Exception e) {
            e.printStackTrace();
        }
    return null;
}


    public static String generateMacSignature(String secret, String data) {
        byte[] byteHMAC = null;
        try {
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKey secretKey = new SecretKeySpec(secret.getBytes("utf-8"), HMAC_SHA1);
            mac.init(secretKey);
            byteHMAC = mac.doFinal(data.getBytes("utf-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String result = byte2hex(byteHMAC);
        return result;
    }
     //二行制转字符串
     public static String byte2hex(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b!=null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }
}
