package com.eleven.cms.util.henan;
import com.eleven.cms.config.HenanYidongConfig;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;

public class SignUtil {
    public SignUtil() {
    }

    private static String signWithRSA(Map<String, String> sysParam, String busiParam,
                                      String publicKey) throws Exception {
        Map<String, String> map = new HashMap(sysParam);
        if (StringUtils.isNotBlank(busiParam)) {
            map.put("content", busiParam);
        }

        String[] keys = (String[]) map.keySet().toArray(new String[map.size()]);
        Arrays.sort(keys);
        StringBuilder buf = new StringBuilder(200);
        String[] var9 = keys;
        int var8 = keys.length;

        String encrypt_value;
        for (int var7 = 0; var7 < var8; ++var7) {
            encrypt_value = var9[var7];
            if (!"sign".equalsIgnoreCase(encrypt_value)) {
                buf.append((String) map.get(encrypt_value));
            }
        }

        encrypt_value = "";
        if (buf.toString().length() > 0) {
            encrypt_value = MD5Util.MD5(buf.toString());
        }

        return RSAUtils.encryptByPublicKey(encrypt_value, publicKey);
    }

    private static String signWithSHA256(Map<String, String> sysParam, String busiParam, String key) throws Exception {
        Map<String, String> map = new HashMap(sysParam);
        if (StringUtils.isNotBlank(busiParam)) {
            map.put("content", busiParam);
        }

        String[] keys = (String[]) map.keySet().toArray(new String[map.size()]);
        Arrays.sort(keys);
        StringBuilder buf = new StringBuilder(200);
        buf.append(key);
        String[] var9 = keys;
        int var8 = keys.length;

        for (int var7 = 0; var7 < var8; ++var7) {
            String k = var9[var7];
            if (!"sign".equalsIgnoreCase(k)) {
                buf.append(k).append((String) map.get(k));
            }
        }

        buf.append(key);
        return SecurityUtils.encodeHmacSHA256HexUpper(buf.toString(), SecurityUtils.decodeHexUpper(key));
    }

    public static String sign(Map<String, String> sysParam, String busiParam, String method,
                              String key) throws Exception {
        if (sysParam != null && !sysParam.isEmpty()) {
            if ("HmacSHA256".equals(method)) {
                return signWithSHA256(sysParam, busiParam, key);
            } else if ("RSAWithMD5".equals(method)) {
                return signWithRSA(sysParam, busiParam, key);
            } else {
                throw new Exception("对不起，暂不支持[" + method + "]签名方法！");
            }
        } else {
            return null;
        }
    }

    public static String sign(Map<String, String> sysParam, String busiParam, String publicKey) throws Exception {
        return sysParam != null && !sysParam.isEmpty() || busiParam != null && busiParam.trim()
                .length() != 0 ? signWithRSA(sysParam, busiParam, publicKey) : null;
    }

    public static String sign(String token, ObjectNode body, ObjectNode sysparams, HenanYidongConfig config) throws Exception {

        String date = DateUtil.formatFullTime(LocalDateTime.now());
        sysparams.put("busiSerial", config.getApiId() + date + (new Random().nextInt(899999) + 100000));
        Map<String, String> sysParam = new HashMap<String, String>();
        sysParam.put("method", "EC_" + sysparams.get("busiCode").asText());
        sysParam.put("format", "json");
        sysParam.put("timestamp", date);
        sysParam.put("appId", config.getAppId());
        sysParam.put("version", "1.0");
        sysParam.put("accessToken", token);
        sysParam.put("busiSerial", sysparams.get("busiSerial").asText());
        String entity = SecurityUtils.encodeAES256HexUpper(body.toString(),
                SecurityUtils.decodeHexUpper(config.getAppKey()));
        String sign = SignUtil.sign(sysParam, entity, "HmacSHA256", config.getAppKey());
        StringBuilder sb = new StringBuilder();
        sb.append("?method=").append(sysParam.get("method")).append("&");
        sb.append("format=").append("json").append("&");
        sb.append("appId=").append(config.getAppId()).append("&");
        sb.append("version=").append("1.0").append("&");
        sb.append("accessToken=").append(token).append("&");
        sb.append("timestamp=").append(date).append("&");
        sb.append("sign=").append(sign).append("&");
        sb.append("busiSerial=").append(sysparams.get("busiSerial").asText());
        body.put("query", sb.toString());

        return entity;
    }
}