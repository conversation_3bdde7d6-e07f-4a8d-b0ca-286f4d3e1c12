package com.eleven.cms.util;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.security.Security;

public class SMUtil {
    private static final Logger logger = LoggerFactory.getLogger(SMUtil.class);
    /**
     sm4的密钥需要16字节的字符串
     **/
    public static final String SM4_KEY = "1234567887654321";
    private static final String ALGORITHM_NAME = "SM4";
    private static final String ALGORITHM_ECB_PKCS5PADDING = "SM4/ECB/PKCS5Padding";
    /**
     sm2的公钥和私钥生成之后存起来使用即可，一般会把公钥提供给前端
     私钥: 05bf31a92115677af2cdca4a2424ac2207cb674dce8fcc3551f5b2fd13d78db7
     公钥: 0431771e60f38f6abb761e2a86f73dd54725db6b83845f2d0960fcfbec5429653f3460e62f8468d66ed58f048e9b6f847ae8d65db68e1bb237d298f1e702d6fede
     压缩后的公钥: 0231771e60f38f6abb761e2a86f73dd54725db6b83845f2d0960fcfbec5429653f
     **/
    //public static final String SM2_PRIVATE_KEY = "sm2私钥";
    public static final String SM2_PRIVATE_KEY = "05bf31a92115677af2cdca4a2424ac2207cb674dce8fcc3551f5b2fd13d78db7";
    //public static final String SM2_PUBLIC_KEY = "sm2公钥";
    public static final String SM2_PUBLIC_KEY = "0431771e60f38f6abb761e2a86f73dd54725db6b83845f2d0960fcfbec5429653f3460e62f8468d66ed58f048e9b6f847ae8d65db68e1bb237d298f1e702d6fede";
    /**
     * sm2加密模式有两种：C1C2C3和C1C3C2（开始国密标准使用C1C2C3，新标准使用C1C3C2）
     */
    public static final SM2Engine.Mode SM2ENGINE_MODE = SM2Engine.Mode.C1C3C2;
    /**
     * 我要说的是 我在加解密的时候  const cipherMode = 1 // 1 - C1C3C2，0 - C1C2C3，默认为1  这个东西啊 你一定得配合后端来 ,因为你不确定后端是用的什么加密方式,还存在一个补位的问题
     * 什么意思呢?就是 在加密的串前加上04,后端才能解密成功,同样后端加密后也会返回带04的加密串给前端(因为后端也是直接用别人的工具类),前端加密遇到04 的加密串且cipherMode  为1的话必须去掉04 才能解密成功
     */
    private static final String SM2BC_STR = "04";
    private static final SM2 sm2 = new SM2();

    /**
     * SM4算法目前只支持128位（即密钥16字节）
     */
    private static final int DEFAULT_KEY_SIZE = 128;
    private static  final String DEFAULT_ENCODING="utf-8";
    static {
        if(null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)){
            Security.addProvider(new BouncyCastleProvider());
        }
        initSm2Instance();
    }

    ///**
    // * sm3加密，文摘算法，杂凑算法
    // * @param content 加密内容
    // * @return
    // */
    //public static String sm3(String content){
    //    String res = "";
    //    try{
    //        byte[] srcData = content.getBytes(DEFAULT_ENCODING);
    //        byte[] resultHash = hash(srcData);
    //        res = ByteUtils.toHexString(resultHash);
    //    }catch (Exception e){
    //        e.printStackTrace();
    //    }
    //    return res;
    //}
    //
    ///**
    // * sm3加密
    // * @param srcData 加密内容的byte数组
    // **/
    //private static byte[] hash(byte[] srcData){
    //    SM3Digest digest = new SM3Digest();
    //    digest.update(srcData,0,srcData.length);
    //    byte[] hash = new byte[digest.getDigestSize()];
    //    digest.doFinal(hash,0);
    //    return  hash;
    //}
    //
    //
    //
    //
    ///**
    // * 生成sm4密钥,生成一次后将byte数组存起来使用即可（推荐：也可以自定义16字节字符串，本工具类使用的是自定义16字节字符串做密钥）
    // * <p>建议使用org.bouncycastle.util.encoders.Hex将二进制转成HEX字符串</p>
    // *
    // * @return 密钥16位
    // * @throws Exception 生成密钥异常
    // */
    //private static byte[] generateKey() throws Exception {
    //    KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
    //    kg.init(DEFAULT_KEY_SIZE, new SecureRandom());
    //    return kg.generateKey().getEncoded();
    //}
    //
    ///***
    // * SM4对称加密，
    // * @param content 加密内容
    // * @param key 密钥 需要128bit,即16个字符组成
    // * @return
    // */
    //public  static String sm4Encrypt(String content,String key){
    //    String res = "";
    //    try{
    //        byte[] resByte = encryptEcbPkcs5Padding(content.getBytes(DEFAULT_ENCODING),key.getBytes(DEFAULT_ENCODING));
    //        res = Hex.toHexString(resByte);
    //    }catch (Exception e){
    //        e.printStackTrace();
    //    }
    //    return res;
    //}
    //
    ///***
    // * SM4对称加密，
    // * @param content 加密内容
    // * @return
    // */
    //public  static String sm4Encrypt(String content){
    //    String res = "";
    //    try{
    //        byte[] resByte = encryptEcbPkcs5Padding(content.getBytes(DEFAULT_ENCODING),SM4_KEY.getBytes(DEFAULT_ENCODING));
    //        res = Hex.toHexString(resByte);
    //    }catch (Exception e){
    //        e.printStackTrace();
    //    }
    //    return res;
    //}
    //
    ///***
    // * SM4对称解密，
    // * @param data 解密前数据
    // * @param key 密钥 需要128bit,即16个字符组成
    // * @return
    // */
    //public static String sm4Decrypt(String data,String key){
    //    String res = "";
    //    try{
    //        byte[] resByte = decryptEcbPkcs5Padding(Hex.decode(data),key.getBytes(DEFAULT_ENCODING));
    //        res = new String(resByte, DEFAULT_ENCODING);
    //    }catch (Exception e){
    //        e.printStackTrace();
    //    }
    //    return res;
    //}
    //
    ///***
    // * SM4对称解密，
    // * @param data 解密前数据
    // * @return
    // */
    //public static String sm4Decrypt(String data){
    //    String res = "";
    //    try{
    //        byte[] resByte = decryptEcbPkcs5Padding(Hex.decode(data),SM4_KEY.getBytes(DEFAULT_ENCODING));
    //        res = new String(resByte, DEFAULT_ENCODING);
    //    }catch (Exception e){
    //        e.printStackTrace();
    //    }
    //    return res;
    //}
    //
    ///**
    // * 加密，SM4-ECB-PKCS5Padding
    // *
    // * @param data 要加密的明文
    // * @param key  密钥16字节，使用Sm4Util.generateKey()生成
    // * @return 加密后的密文
    // * @throws Exception 加密异常
    // */
    //private static byte[] encryptEcbPkcs5Padding(byte[] data, byte[] key) throws Exception {
    //    return sm4work(data, key, ALGORITHM_ECB_PKCS5PADDING, null, Cipher.ENCRYPT_MODE);
    //}
    //
    ///**
    // * 解密，SM4-ECB-PKCS5Padding
    // *
    // * @param data 要解密的密文
    // * @param key  密钥16字节，使用Sm4Util.generateKey()生成
    // * @return 解密后的明文
    // * @throws Exception 解密异常
    // */
    //private static byte[] decryptEcbPkcs5Padding(byte[] data, byte[] key) throws Exception {
    //    return sm4work(data, key, ALGORITHM_ECB_PKCS5PADDING, null, Cipher.DECRYPT_MODE);
    //}
    //
    ///**
    // * SM4对称加解密
    // *
    // * @param input   明文（加密模式）或密文（解密模式）
    // * @param key     密钥
    // * @param sm4mode sm4加密模式
    // * @param iv      初始向量(ECB模式下传NULL)
    // * @param mode    Cipher.ENCRYPT_MODE - 加密；Cipher.DECRYPT_MODE - 解密
    // * @return 密文（加密模式）或明文（解密模式）
    // * @throws Exception 加解密异常
    // */
    //private static byte[] sm4work(byte[] input, byte[] key, String sm4mode, byte[] iv, int mode)
    //        throws Exception {
    //    IvParameterSpec ivParameterSpec = null;
    //    if (null != iv) {
    //        ivParameterSpec = new IvParameterSpec(iv);
    //    }
    //    SecretKeySpec sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
    //    Cipher cipher = Cipher.getInstance(sm4mode, BouncyCastleProvider.PROVIDER_NAME);
    //    if (null == ivParameterSpec) {
    //        cipher.init(mode, sm4Key);
    //    } else {
    //        cipher.init(mode, sm4Key, ivParameterSpec);
    //    }
    //    return cipher.doFinal(input);
    //}

    /**
     * 初始化sm2实例
     * @return
     */
    private static void initSm2Instance() {
        logger.info("初始化sm2实例");
        ECPrivateKeyParameters privateKeyParameters = BCUtil.toSm2Params(SM2_PRIVATE_KEY);
        //产生的公钥为130字节，实际公钥为128字节，第一个字节表示是否压缩，这里用不上，所以从第二个字节开始截取横坐标和纵坐标
        String xhex = SM2_PUBLIC_KEY.substring(2, 66);
        String yhex = SM2_PUBLIC_KEY.substring(66, 130);
        ECPublicKeyParameters publicKeyParameters = BCUtil.toSm2Params(xhex, yhex);
        //设置公钥和私钥
        sm2.setPrivateKeyParams(privateKeyParameters);
        sm2.setPublicKeyParams(publicKeyParameters);
        //设置加密模式(C1C3C2)
        sm2.setMode(SM2ENGINE_MODE);
    }

    /**
     * 生成sm2公钥和私钥，一般生成之后将公钥和私钥存起来使用即可
     */
    public static void createSm2Key() {
        //创建sm2 对象
        SM2 createSm2Key = SmUtil.sm2();
        //这里会自动生成对应的随机秘钥对 , 注意！ 这里一定要强转，才能得到对应有效的秘钥信息
        byte[] privateKey = BCUtil.encodeECPrivateKey(createSm2Key.getPrivateKey());
        //这里公钥不压缩  公钥的第一个字节用于表示是否压缩  可以不要
        byte[] publicKey = ((BCECPublicKey) createSm2Key.getPublicKey()).getQ().getEncoded(false);
        //这里得到的 压缩后的公钥   ((BCECPublicKey) sm2.getPublicKey()).getQ().getEncoded(true);
        //byte[] publicKeyEc = BCUtil.encodeECPublicKey(createSm2Key.getPublicKey());
        //打印当前的公私秘钥
        System.out.println("私钥: " + HexUtil.encodeHexStr(privateKey));
        System.out.println("公钥: " + HexUtil.encodeHexStr(publicKey));
        //System.out.println("压缩后的公钥: " + HexUtil.encodeHexStr(publicKeyEc));
    }

    /**
     * sm2解密
     * @param data 密文
     * @return
     */
    public static String sm2Decrypt(String data) {
        String decryptData = "";
        try {
            long start = System.currentTimeMillis();
            //logger.info("初始化sm2实例");
            //initSm2Instance();
            //使用BC库解密需要在密文前面拼上”04“字符串(前端加密是不会加上04的，后端加密则会加上04，所以需要做多一层判断)
            //注意：请使用使用StringBuffer不要使用StringBuilder，前者是线程安全，后者非线程安全
            if (!data.startsWith(SM2BC_STR)) {
                data = new StringBuffer(SM2BC_STR).append(data).toString();
            }
            decryptData = sm2.decryptStr(data, KeyType.PrivateKey);
            long end = System.currentTimeMillis();
            logger.info("sm2解密一次所花时间：{}ms",(end - start));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decryptData;
    }

    /**
     * sm2加密
     * @param data 待加密文本
     * @return
     *    前端引入sm-crypto库( npm install --save sm-crypto )进行加密,代码如下
     *        const pubKey = "0431771e60f38f6abb761e2a86f73dd54725db6b83845f2d0960fcfbec5429653f3460e62f8468d66ed58f048e9b6f847ae8d65db68e1bb237d298f1e702d6fede"
     *        const raw = "hello world";
     *        let sm2 = require('sm-crypto').sm2;
     *        let cipherMode = 1; // 1 - C1C3C2，0 - C1C2C3，默认为1
     *        let encryptData = sm2.doEncrypt(raw, pubKey, cipherMode); // 加密结果
     *        console.log("加密结果",encryptData)
     */
    public static String sm2Encrypt(String data) {
        String encryptData = "";
        try {
            long start = System.currentTimeMillis();
            //logger.info("初始化sm2实例");
            //initSm2Instance();
            encryptData = sm2.encryptBcd(data, KeyType.PublicKey);
            long end = System.currentTimeMillis();
            logger.info("sm2加密一次所花时间：{}ms",(end - start));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encryptData;
    }


    public static void main(String[] args) {
        //createSm2Key();
        String decryptFromJs = sm2Decrypt("4538552fc9ac115710736878da6e46d0c6edd5c5816320f0010d259e3eb3f9f1982e43027a041e3b9d9ea5e64d17af40d2b7920164947b2c13e3ac7ec10bf9f9790031f7d20cc402362f31da6e7e66faacb91f40639bb254691e2d9a1f015b75f1fc26d58139f3d023fea5be6d0f8d832fb434faac9fb610f5d4ac6a85a56d9cb89193ae26eda71a6fd8c65c28cde69852124b10371134dff8a07d5b09aa203112b3f9b24f2ba14057f49fc46e54024c3b50d796874af5da485c0eb89a06c08d8f0c4dd7465b08820b2895afb6ed7782ffce0447b0a19e6197af050f990d13afaa82a8a17845e4cb496a810469a5cea26caac50f2b8f9c2c3219a3a638893f61d0b6301bcb1c041e470e74d6a5d1cf088eb2928763fabd932398628054317844b67ac4729a75d0663c7809b03e46991cd56d57078caae1f4823188e7abf36d73244239ab8b969985abebe975718c6afe5fcd7f5a7ada0bb6a0f3fe97b6b795e6fd4d0277a1c5d0a1398cdd66e88426ff3336239a5d8c0f40dffa55b408fe082b7e8dd21b062aa4bcbda903d6e2b914220206acb61ec58a7e300b755200dc751843d12c374d910c2a6ddc246a3ff00608593d7a894b41904b866ecd6f8c886914aae735643124ab12bac1c2d3940896bcaf7f60d6c8fe3880d1b34d58dc0353810a39ee8c85af1ddf65a7850d9c16d80b91632f1cd6f0bbc85df6ffb0d380dcf45da9369e0febb0ece42f05ba349668020bab2e376ebcbc5bf299ed6018a1decff11b4906495d2249e29029ae7eda3c51025cf2178ac2090751661b309eace3f79d9b16b4e5bf0bcc74c037179baec412a2d606a2f164a3316d7401124726db260f2292cc332b0eeda35fade51cee0455b4ab1fd9840de9e1ea867560d3487eb15d04857842517ddcc017619ee20d812f2c948449c276a268e307b7335d48eb3e3396d929e4f0e2ec977cd0efa10d1442dd095ed39988fe510e277e4f74edca319c13abdec63fc59ac147549709853d209c56d841ee24f5000a1b4acf5a50e033798863b5b8bfc5e3d3b5e1aec6f96bc7c95480d8ad149298b423391a79c8f2656aecee541ccc8ed007ea8fd2e0510d938c9c3fdaaa089e0e45a9967cf64427891a16ab3199515a5f61d8956d371165b0e33fc8a0eefb2c14451b8106a2b40cce07ec517a61b0a3d7da711e995035b851189907");
        System.out.println("decryptFromJs = " + decryptFromJs);

        String testData = "{\n" +
                "\"resCode\":\"000000\",\n" +
                "\"resMsg\":\"成功\",\n" +
                "\"mobile\":\"15915401656\",\n" +
                "\"orderId\":\"122880811475746663981580288\",\n" +
                "\"channelCode\":\"00210QX\"\n" +
                "}";
        String encrypt = sm2Encrypt(testData);
        System.out.println("encrypt = " + encrypt);
        String decrypt = sm2Decrypt(encrypt);
        System.out.println("decrypt = " + decrypt);
    }
}
