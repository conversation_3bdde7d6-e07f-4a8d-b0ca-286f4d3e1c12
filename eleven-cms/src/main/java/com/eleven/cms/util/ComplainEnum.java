package com.eleven.cms.util;

import org.apache.commons.lang3.StringUtils;

public enum ComplainEnum {
    MERCHANT_PROCESSING("MERCHANT_PROCESSING", "待处理"),

    MERCHANT_FEEDBACKED("MERCHANT_FEEDBACKED", "已处理"),

    FINISHED("FINISHED", "投诉完结"),

    CANCELLED("CANCELLED", "投诉关闭"),

    PLATFORM_PROCESSING("PLATFORM_PROCESSING", "客服处理中"),

    PLATFORM_FINISH("PLATFORM_FINISH", "客服处理完结"),

    CLOSED("CLOSED", "投诉关闭");

    /**
     * 投诉处理状态
     */
    private String complainStatus;
    /**
     * 投诉处理描述
     */
    private String complainMsg;
    public static String getByStatus(String complainStatus) {
        if (StringUtils.isEmpty(complainStatus)) {
            return null;
        }
        for (ComplainEnum val : values()) {
            if (val.getComplainStatus().contains(complainStatus)) {
                return val.getComplainMsg();
            }
        }
        return null;
    }
    public String getComplainStatus() {
        return complainStatus;
    }

    public void setComplainStatus(String complainStatus) {
        this.complainStatus = complainStatus;
    }

    public String getComplainMsg() {
        return complainMsg;
    }

    public void setComplainMsg(String complainMsg) {
        this.complainMsg = complainMsg;
    }

    ComplainEnum(String complainStatus, String complainMsg) {
        this.complainStatus = complainStatus;
        this.complainMsg = complainMsg;
    }
}
