package com.eleven.cms.util;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.entity.SubChannel;
import com.eleven.cms.service.impl.SubChannelServiceImpl;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @datetime 2024/9/13 10:29
 */
@Component
public class AdPlatformUtils {

    /**
     * 获取广告平台名称
     *
     * @param subChannel 子渠道号
     * @return String
     */
    public static String getAdPlatform(String subChannel) {
        SubChannel obj = SpringUtil.getBean(SubChannelServiceImpl.class).findSubChannel(subChannel);
        return obj != null? obj.getAdPlatform() : "";
    }
}
