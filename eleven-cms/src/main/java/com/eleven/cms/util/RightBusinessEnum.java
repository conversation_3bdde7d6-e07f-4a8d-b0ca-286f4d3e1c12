package com.eleven.cms.util;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.http.HttpStatus;

/**
 * 权益业务枚举
 */
public enum RightBusinessEnum {
//    /**
//     * 白金会员[畅听版]
//     */
//    BJHYCTB("698039020104154386", "BJHYCTB", "bjhy_ctb_rights_pack_1"),
    /**
     * 贝壳视听会员
     */
//    BKST("90030426", "BKST", "bkst_rights_pack_1&bkst_rights_pack_2"),
    /**
     * 抖音测试业务员
     */
    DYCS("9527001", "抖音测试业务员"),
    /**
     * 趣享音乐包
     */
    QXYYB("698039020050006172", "趣享音乐包"),
//    /**
//     * 炫酷来电
//     */
//    XKLD("698039035100000014", "炫酷来电"),
    /**
     * 悦动专属6元包-川网
     */
    YDZS("698039035100000140", "悦动专属6元包-川网"),
    /**
     * 白金会员[随心听]
     */
    BJHY("698039020105522717", "白金会员[随心听]"),
    /**
     * 白金会员[宜搜]
     */
    BJHYYS("698039020103880544", "白金会员[宜搜]"),
    /**
     * 北岸唐唱音乐包
     */
    BATCYYB("698039020108689345", "北岸唐唱音乐包"),
    /**
     * 酷狗北岸唐唱音乐包
     */
    KGBATCYYB("698039020108689345kg", "酷狗北岸唐唱音乐包"),
    /**
     * 音乐全曲包-七彩歌曲10元包
     */
    YYQQB("698039020050006178", "音乐全曲包-七彩歌曲10元包"),
    /**
     * 沃音乐视频彩铃
     */
    WYY("4900753400", "沃音乐视频彩铃"),
    /**
     * 10元电信视频彩铃
     */
    DDXSPCL("135999999999999000157", "10元电信视频彩铃"),
    /**
     * 藕粉咪咕同享会10元包
     */
    OFMGTXH("698039034100000066", "藕粉咪咕同享会10元包"),
    /**
     * 会员大礼包
     */
    MEMBER_990_VS("MEMBER_990_VS", "会员大礼包"),
    /**
     * 特惠省钱包
     */
    MEMBER_1880("MEMBER_1880", "特惠省钱包"),
    /**
     * 至尊会员包
     */
    MEMBER_1990("MEMBER_1990", "至尊会员包"),
    /**
     * 会员大礼包
     */
    MEMBER_990_PLUS("MEMBER_990_PLUS", "会员大礼包"),
    /**
     * 特惠省钱包
     */
    MEMBER_1880_PLUS("MEMBER_1880_PLUS", "特惠省钱包"),
    /**
     * 视听会员包
     */
    MEMBER_1990_VRBT("MEMBER_1990_VRBT", "视听会员包"),
    /**
     * 会员体验
     */
    M_HSTJ_10_TEST("M_HSTJ_10_TEST", "会员体验"),
    /**
     * 尊享视听会员
     */
    M_HSTJ_1990_ZXST("M_HSTJ_1990_ZXST", "尊享视听会员"),
    /**
     * 至尊会员礼包
     */
    M_HSTJ_2500_ZZHY("M_HSTJ_2500_ZZHY", "至尊会员礼包"),
    /**
     * 美团礼包
     */
    M_XR_2500_MD("M_XR_2500_MD", "美团礼包"),
    /**
     * 视听会员随心看
     */
    M_XR_2500_SXK("M_XR_2500_SXK", "视听会员随心看"),
    /**
     * 视频会员任意选
     */
    M_XR_2500_RYX("M_XR_2500_RYX", "视频会员任意选"),
    /**
     * 美团礼包
     */
    M_WX_1990_MD("M_WX_1990_MD", "美团礼包"),
    /**
     * 视频会员任意选
     */
    M_WX_1990_RYX("M_WX_1990_RYX", "视频会员任意选"),
//    /**
//     * 音乐全曲包-趣享音乐20元包
//     */
//    YYQQBQXYY("698039020050006030", "音乐全曲包-趣享音乐20元包"),
    /**
     * 音乐全曲包-经典音乐15元包
     */
    YYQQBJDYY("698039020050006041", "音乐全曲包-经典音乐15元包"),
    /**
     * 休闲10元包
     */
    HY_10_XX("760000153723", "休闲10元包"),
    /**
     * 畅玩25元包
     */
    HY_25_CW("760000153417", "畅玩25元包"),
    /**
     * 网易云MM
     */
    WANGYIYUN_MM("WANGYIYUN_MM", "网易云MM");


    /**
     * 业务id
     */
    private String serviceId;
    /**
     * 业务名称
     */
    private String serviceName;

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
    RightBusinessEnum(String serviceId, String serviceName) {
        this.serviceId = serviceId;
        this.serviceName = serviceName;
    }

    public static RightBusinessEnum getByServiceId(String serviceId) {
        if (StringUtils.isEmpty(serviceId)) {
            return null;
        }
        for (RightBusinessEnum val : values()) {
            if (val.getServiceId().contains(serviceId)) {
                return val;
            }
        }
        return null;
    }

    /**
     * 是否soap执行查询
     * @param serviceId
     * @return true 是  false 否
     */
    public static Boolean isSoapLog(String serviceId) {
        if (StringUtils.isEmpty(serviceId)) {
            return false;
        }
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(serviceId);
        if (rightBusinessEnum==null) {
            return false;
        }
        switch (rightBusinessEnum){
            case BATCYYB:
            case YYQQB:
            case BJHY:
                return true;
            default: return false;
        }
    }
    /**
     * 是否预约直充
     * @param serviceId
     * @return true 是  false 否
     */
    public static Boolean isPrepareCharge(String serviceId) {
        if (oConvertUtils.isEmpty(serviceId)) {
            return true;
        }
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(serviceId);
        if (rightBusinessEnum==null) {
            return true;
        }
        switch (rightBusinessEnum){
            case WYY:
            case DDXSPCL:
                return false;
            default: return true;
        }
    }

    public static String getServiceId(String name){
        for (RightBusinessEnum val : RightBusinessEnum.values()) {
            if(name.equals(val.name())){
                return val.getServiceId();
            }
        }
        return null;
    }
}
