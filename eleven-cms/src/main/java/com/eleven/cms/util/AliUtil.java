package com.eleven.cms.util;

//import com.aliyun.dypnsapi20170525.Client;
//import com.aliyun.teaopenapi.models.Config;
//import com.baomidou.mybatisplus.core.toolkit.IdWorker;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AliUtil {
    public static final Long USER_LOGIN_TIME =28860L;
    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    //public static com.aliyun.dypnsapi20170525.Client createClient(String accessKeyId, String accessKeySecret,String endpoint) {
    //    Client client = null;
    //    try {
    //        Config config = new Config()
    //                // AccessKey ID
    //                .setAccessKeyId(accessKeyId)
    //                //AccessKey Secret
    //                .setAccessKeySecret(accessKeySecret);
    //        // 访问的域名
    //        config.endpoint = endpoint;
    //        client = new Client(config);
    //    } catch (Exception e) {
    //        log.error("使用AK&SK初始化账号Client出错：",e);
    //    }
    //    return client;
    //}
}
