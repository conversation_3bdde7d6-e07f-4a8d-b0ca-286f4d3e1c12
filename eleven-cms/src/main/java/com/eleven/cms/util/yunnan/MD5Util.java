package com.eleven.cms.util.yunnan;

import java.security.MessageDigest;

/**
 * MD5���ܹ�����
 * 
 * Copyright: Copyright (c) 2011 Asiainfo
 * 
 * @ClassName: TransferSoapReqHelper.java
 * @Description: ����Ĺ�������
 *
 * @version: v1.0.0
 * @author: zhangmeng3
 * @date: 2014-9-17 ����10:38:29
 *
 */
public class MD5Util {
	
	private static final String MD5_ALGORITHM = "MD5";

	public final static String MD5(String s) throws Exception {
		char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
				'a', 'b', 'c', 'd', 'e', 'f' };
		byte[] btInput = s.getBytes();
		// ���MD5ժҪ�㷨�� MessageDigest ����
		MessageDigest mdInst = MessageDigest.getInstance(MD5_ALGORITHM);
		// ʹ��ָ�����ֽڸ���ժҪ
		mdInst.update(btInput);
		// �������
		byte[] md = mdInst.digest();
		// ������ת����ʮ�����Ƶ��ַ�����ʽ
		int j = md.length;
		char str[] = new char[j * 2];
		int k = 0;
		for (int i = 0; i < j; i++) {
			byte byte0 = md[i];
			str[k++] = hexDigits[byte0 >>> 4 & 0xf];
			str[k++] = hexDigits[byte0 & 0xf];
		}
		return new String(str);
    }
	
}
