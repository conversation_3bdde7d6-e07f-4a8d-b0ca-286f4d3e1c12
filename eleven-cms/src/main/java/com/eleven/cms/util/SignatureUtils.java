package com.eleven.cms.util;

import com.eleven.cms.util.guizhou.StreamUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;

import javax.crypto.Cipher;
import java.io.*;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 生成及验证签名信息工具类
 * <AUTHOR>
 * @version 2020-07-27
 */
@Slf4j
public class SignatureUtils {
    public static final String MD5_RSA = "MD5withRSA";
    private static final String KEY_ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    public static final String FIELD_SIGN = "sign";
    private static final String HMACSHA256_SIGN = "HmacSHA256";
    /**
     * 生成签名信息
     *
     * @param secretKey 产品私钥
     * @param params    接口请求参数名和参数值map，不包括signature参数名
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String genSignature(String secretKey, Map<String, String> params) throws UnsupportedEncodingException {
        // 1. 参数名按照ASCII码表升序排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 2. 按照排序拼接参数名与参数值
        StringBuffer paramBuffer = new StringBuffer();
        for (String key : keys) {
            paramBuffer.append(key).append(params.get(key) == null ? "" : params.get(key));
        }
        // 3. 将secretKey拼接到最后
        paramBuffer.append(secretKey);

        // 4. MD5是128位长度的摘要算法，用16进制表示，一个十六进制的字符能表示4个位，所以签名后的字符串长度固定为32个十六进制字符。
        return DigestUtils.md5Hex(paramBuffer.toString().getBytes("UTF-8"));
    }
    public static String generateSign(Map<String, String> params,String privateKey,String businessParams)throws Exception {
        Set<String> keySet = params.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            valueStr.append(k);
            valueStr.append("=");
            valueStr.append(params.get(k));
            valueStr.append("&");
        }
        String signStr = valueStr.toString().substring(0, valueStr.toString().length() - 1)+businessParams;
        return signByPrivateKey(signStr.getBytes(), privateKey);
    }

    /**
     *
     * 用私钥对信息进行数字签名
     *
     * @param data       加密数据
     *
     * @param privateKey 私钥-base64加密的
     *
     * @return
     *
     * @throws Exception
     *
     */
    public static String signByPrivateKey(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = decryptBASE64(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory factory = KeyFactory.getInstance(KEY_ALGORITHM);
        PrivateKey priKey = factory.generatePrivate(keySpec);// 生成私钥
        // 用私钥对信息进行数字签名
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(data);
        return encryptBASE64(signature.sign());
    }
    private static byte[] decryptBASE64(String data) {
        return Base64.decodeBase64(data);
    }
    private static String encryptBASE64(byte[] data) {
        return new String(Base64.encodeBase64(data));
    }

    //南山下发途牛权益通知验签
    public static boolean checkSign(String data, String sign,String publicKey){
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            StringWriter writer = new StringWriter();
            StreamUtil.io(new InputStreamReader( new ByteArrayInputStream(publicKey.getBytes())), writer);
            byte[] encodedKey = Base64.decodeBase64(writer.toString().getBytes());
            PublicKey pubKey= keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            Signature signature = Signature.getInstance(MD5_RSA);//SHA256WithRSA
            signature.initVerify(pubKey);
            signature.update(data.getBytes("UTF-8"));
            return signature.verify(Base64.decodeBase64(sign.getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
    //南山下发途牛权益生成签名
    public static String sign(String data,String privateKey){
        try {
            byte[] keyBytes = decryptBASE64(privateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory factory = KeyFactory.getInstance(KEY_ALGORITHM);
            PrivateKey priKey = factory.generatePrivate(keySpec);// 生成私钥
            Signature signature = Signature.getInstance(MD5_RSA);
            signature.initSign(priKey);
            signature.update(data.getBytes("UTF-8"));
            String sign = new String(Base64.encodeBase64(signature.sign()));
            return sign;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 西藏移动参数sign
     * @param sysParam
     * @param busiParam
     * @param key
     * @return
     */
    @SneakyThrows
    public static String signWithSHA256(Map<String, String> sysParam, String busiParam, String key)  {
        Map<String, String> map = new HashMap<String, String>(sysParam);
        map.put("content", busiParam);
        String signStr = map.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(entry -> entry.getKey()+entry.getValue()).collect(Collectors.joining("",key,key));
        return new HmacUtils(HmacAlgorithms.HMAC_SHA_256, Hex.decodeHex(key.toLowerCase())).hmacHex(signStr).toUpperCase();
    }


    public static boolean checkSignByPublicKey(String data, String sign,String publicKey){
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            StringWriter writer = new StringWriter();
            StreamUtil.io(new InputStreamReader( new ByteArrayInputStream(publicKey.getBytes())), writer);
            byte[] encodedKey = Base64.decodeBase64(writer.toString().getBytes());
            PublicKey pubKey= keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);//SHA256WithRSA
            signature.initVerify(pubKey);
            signature.update(data.getBytes("UTF-8"));
            return signature.verify(Base64.decodeBase64(sign.getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    public static String generateSign(Map<String, Object> params,String key)throws Exception {
        Set<String> keySet = params.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            valueStr.append(k);
            valueStr.append("=");
            valueStr.append(params.get(k));
            valueStr.append("&");
        }
        String sign = valueStr.toString()+"key="+key;
        return DigestUtils.md5Hex(sign.toString().getBytes("UTF-8"));
    }





    public static PublicKey getPublicKeyFromString(String publicKeyStr) throws Exception {
        byte[] publicKeyBytes = Base64.decodeBase64(publicKeyStr);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }
    /**
     * 分段加密
     *
     * @param ciphertext  密文
     * @param publicKey         加密秘钥
     * @param segmentSize 分段大小，<=0 不分段
     * @return
     */
    public static String encipher(String ciphertext, PublicKey publicKey, int segmentSize) {
        try {
            // 用公钥加密
            byte[] srcBytes = ciphertext.getBytes();

            // Cipher负责完成加密或解密工作，基于RSA
            Cipher cipher = Cipher.getInstance("RSA");
            // 根据公钥，对Cipher对象进行初始化
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] resultBytes = null;
            if (segmentSize > 0) {
                resultBytes = cipherDoFinal(cipher, srcBytes, segmentSize); //分段加密
            } else {
                resultBytes = cipher.doFinal(srcBytes);
            }
            return Base64.encodeBase64String(resultBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    /**
     * 分段大小
     *
     * @param cipher
     * @param srcBytes
     * @param segmentSize
     * @return
     * @throws Exception
     */
    public static byte[] cipherDoFinal(Cipher cipher, byte[] srcBytes, int segmentSize)throws  Exception {
        if (segmentSize <= 0) {
            throw new RuntimeException("分段大小必须大于0");
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int inputLen = srcBytes.length;
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > segmentSize) {
                cache = cipher.doFinal(srcBytes, offSet, segmentSize);
            } else {
                cache = cipher.doFinal(srcBytes, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * segmentSize;
        }
        byte[] data = out.toByteArray();
        out.close();
        return data;
    }
    public static boolean checkSign(Map<String, Object> params,String appkey,String timestamp,String sign){
        Set<String> keySet = params.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            valueStr.append(k);
            valueStr.append("=");
            valueStr.append(params.get(k));
            valueStr.append("&");
        }
        valueStr.append("appkey");
        valueStr.append("=");
        valueStr.append(appkey);
        valueStr.append("&");
        valueStr.append("timestamp");
        valueStr.append("=");
        valueStr.append(timestamp);
        String signStr = valueStr.toString();
        log.info("signStr:{}",signStr);
        try {
            return DigestUtils.md5Hex(signStr.toString().getBytes("UTF-8")).equals(sign);
        } catch (Exception e) {
            log.error("多彩验签异常-params:{},appkey:{},timestamp:{},sign:{}",params,appkey,timestamp,sign,e);
            return false;
        }
    }




    public static String sign(Map<String, Object> params,String appkey,String timestamp){
        Set<String> keySet = params.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            valueStr.append(k);
            valueStr.append("=");
            valueStr.append(params.get(k));
            valueStr.append("&");
        }
        valueStr.append("appkey");
        valueStr.append("=");
        valueStr.append(appkey);
        valueStr.append("&");
        valueStr.append("timestamp");
        valueStr.append("=");
        valueStr.append(timestamp);
        valueStr.append("&");
        String signStr = valueStr.toString();
        try {
            return DigestUtils.md5Hex(signStr.toString().getBytes("UTF-8"));
        } catch (Exception e) {
            log.error("多彩签名异常-params:{},appkey:{},timestamp:{}",params,appkey,timestamp,e);
            return null;
        }
    }
}
