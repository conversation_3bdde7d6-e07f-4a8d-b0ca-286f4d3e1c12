package com.eleven.cms.util;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TokenUtil {
    public static final Long USER_LOGIN_TIME =28860L;
    /**
     * 手机号码登录
     * @param phone
     * @return
     */
    public String setUserLoginTime(String phone) {
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean(RedisUtil.class);
        String token = CacheConstant.CMS_CACHE_LOGIN_TOKEN+IdWorker.get32UUID();
        redisUtil.set(token,phone,USER_LOGIN_TIME);
        return token;
    }
    /**
     * 统一登录
     * @param spcltoken，bdwpToken
     * @return
     */
    public String setUserLoginTime(String spcltoken,String bdwpToken) {
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean(RedisUtil.class);
        String token = CacheConstant.CMS_CACHE_UNIFY_RIGHT_LOGIN_TOKEN+IdWorker.get32UUID();
        ObjectMapper objectMapper=new ObjectMapper();
        ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("spcltoken",spcltoken);
        objectNode.put("bdwpToken",bdwpToken);
        redisUtil.set(token,objectNode.toString(),USER_LOGIN_TIME);
        return token;
    }


    /**
     * 手机号码登录
     * @param mobile
     * @return
     */
    public static String setLoginTime(String mobile) {
        RedisUtil redisUtil =SpringContextUtils.getBean(RedisUtil.class);
        String key = CacheConstant.CMS_CACHE_LOGIN_TOKEN;
        if(redisUtil.hHasKey(key,mobile)){
            redisUtil.hdel(key,mobile);
        }
        String token = CacheConstant.CMS_CACHE_LOGIN_TOKEN+IdWorker.get32UUID();
        redisUtil.hset(key,mobile,token,USER_LOGIN_TIME);
        return token;
    }


    /**
     * 获取手机号
     * @return
     */
    public static Object getMobile() {
        try {
            //先拿到Request请求体
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            final String token = request.getHeader("token");

            final String mobile = request.getHeader("mobile");
            if (StringUtils.isNotBlank(mobile) && mobile.matches(BizConstant.MOBILE_REG)) {
                return mobile;
            }

            RedisUtil redisUtil =SpringContextUtils.getBean(RedisUtil.class);
            String key = CacheConstant.CMS_CACHE_LOGIN_TOKEN;
            Map<Object, Object> entries = redisUtil.hmget(key);
            Map<Object, Object> exchangeMap = entries.entrySet().stream().collect(Collectors.toMap(o -> o.getValue(), o -> o.getKey()));
            return exchangeMap.get(token);
        } catch (Exception e) {
            log.error("登录异常",e);
            return null;
        }
    }


    /**
     * 手机号码登录
     * @param mobile
     * @return
     */
    public static void setVrbtAppLoginTime(String mobile) {
        RedisUtil redisUtil =SpringContextUtils.getBean(RedisUtil.class);
        String key = CacheConstant.CMS_CACHE_VRBT_APP_LOGIN_TOKEN+mobile;
        if(redisUtil.hasKey(key)){
            redisUtil.del(key);
        }
        redisUtil.set(key,mobile,USER_LOGIN_TIME);
    }




    /**
     * 获取手机号
     * @return
     */
    public static String getLoginMobile() {
        try {
            //先拿到Request请求体
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            final String token = request.getHeader("token");
            RedisUtil redisUtil =SpringContextUtils.getBean(RedisUtil.class);
            String key = CacheConstant.CMS_CACHE_LOGIN_TOKEN;
            Map<Object, Object> entries = redisUtil.hmget(key);
            Map<Object, Object> exchangeMap = entries.entrySet().stream().collect(Collectors.toMap(o -> o.getValue(), o -> o.getKey()));
            return String.valueOf(exchangeMap.get(token));
        } catch (Exception e) {
            log.error("登录异常",e);
            return null;
        }
    }
}
