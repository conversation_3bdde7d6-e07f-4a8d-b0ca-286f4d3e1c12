package com.eleven.cms.util;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.file.Files;
import java.util.Base64;
import java.util.List;

/**
 * @author: cai lei
 * @create: 2021-11-22 15:13
 */
public class FileUtil {

    public static String readFile(File src) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader reader = new BufferedReader(new FileReader(src));
        String line;
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line + "\n");
        }
        reader.close();
        return stringBuilder.toString();
    }

    public static boolean writeFile(String content, File dist) {
        try {
            BufferedWriter writer = new BufferedWriter(new FileWriter(dist));
            writer.write(content);
            writer.flush();
            writer.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将本地图片转换成Base64编码字符串
     *
     * @param classPathResource 图片目录路径
     * @return
     */
    public static String getImgFileToBase64(ClassPathResource classPathResource) {
        //将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        try {
            byte[] fileContent = IOUtils.toByteArray(classPathResource.getInputStream());
            return Base64.getEncoder().encodeToString(fileContent);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) throws IOException {
        //解压文件并获取需要替换的文件内容
        String domain = "crbt.miguring.com";
        String domianSuffix = domain.substring(domain.lastIndexOf(".") + 1);
        List<String> replaceList = UnzipUtil.unzip(new File("D:\\test\\4412226_crbt.miguring.com_nginx.zip"));
        File sourceConfigFile = new File("D:\\test\\ssl_www.cdyrjygs.com.conf");
        File targetConfigFile = new File(sourceConfigFile.getParent() + File.separator + "ssl_" + domain + ".conf");
        FileUtils.copyFile(sourceConfigFile, targetConfigFile);
        String content = FileUtil.readFile(targetConfigFile);
        if (!StringUtils.isEmpty(content)) {
            //替换文件内容
            for (String string : replaceList) {
                if (string.endsWith("pem")) {
                    content = content.replace(BizConstant.PEM, string);
                } else if (string.endsWith("key")) {
                    content = content.replace(BizConstant.KEY, string);
                }
            }
        }
        content = content.replace(BizConstant.SERVER_NAME, "server_name " + domain);
        FileUtil.writeFile(content, targetConfigFile);
        System.out.println("nginx替换后文件内容为:" + content);
    }
}
