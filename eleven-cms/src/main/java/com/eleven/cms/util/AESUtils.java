package com.eleven.cms.util;


import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.text.ParseException;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-11-30 10:01
 */
public class AESUtils {
    private static Logger logger = LoggerFactory.getLogger(AESUtils.class);
    private static final String AES = "AES";
    private static final String AES_CBC = "AES/CBC/PKCS5Padding";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final String IV_PARAMETER ="16-Bytes--String";
    private static final int KEY_LENGTH_BYTE = 32;
    private static final int TAG_LENGTH_BIT = 128;
    private static final String DEFAULT_URL_ENCODING = "UTF-8";
    private static final int DEFAULT_AES_KEY_SIZE = 128;
    private static final int DEFAULT_IV_SIZE = 16;

    private static final byte[] DEFAULT_KEY = new byte[]{-97, 88, -94, 9, 70, -76, 126, 25, 0, 3, -20, 113, 108, 28, 69, 125};

    private static SecureRandom random = new SecureRandom();

    /**
     * 使用AES加密原始字符串.
     *
     * @param input 原始输入字符数组
     */
    public static String aesEncrypt(String input) {
        try {
            return new String(Hex.encodeHex(aesEncrypt(input.getBytes(DEFAULT_URL_ENCODING), DEFAULT_KEY)));
        } catch (Exception e) {
            return "";
        }
    }


    /**
     * 使用AES加密原始字符串.
     *
     * @param input 原始输入字符数组
     * @param key   符合AES要求的密钥
     */
    public static String aesEncrypt(String input, String key) throws Exception {
        try {
            return new String(Hex.encodeHex(aesEncrypt(input.getBytes(DEFAULT_URL_ENCODING), Hex.decodeHex(key.toCharArray()))));
        } catch (UnsupportedEncodingException e) {
            return "";
        }
    }

    /**
     * 使用AES加密原始字符串.
     *
     * @param input 原始输入字符数组
     * @param key   符合AES要求的密钥
     */
    public static byte[] aesEncrypt(byte[] input, byte[] key) throws Exception {
        return aes(input, key, Cipher.ENCRYPT_MODE);
    }

    /**
     * 使用AES加密原始字符串.
     *
     * @param input 原始输入字符数组
     * @param key   符合AES要求的密钥
     * @param iv    初始向量
     */
    public static byte[] aesEncrypt(byte[] input, byte[] key, byte[] iv) throws Exception {
        return aes(input, key, iv, Cipher.ENCRYPT_MODE);
    }

    /**
     * 使用AES解密字符串, 返回原始字符串.
     * 若发生异常则返回原字符串
     *
     * @param input Hex编码的加密字符串
     */
    public static String aesDecrypt(String input) {
        try {
            return new String(aesDecrypt(Hex.decodeHex(input.toCharArray()), DEFAULT_KEY), DEFAULT_URL_ENCODING);
        } catch (Exception e) {
            return input;
        }
    }

    /**
     * 使用AES解密字符串, 返回原始字符串.
     *
     * @param input Hex编码的加密字符串
     * @param key   符合AES要求的密钥
     */
    public static String aesDecrypt(String input, String key) {
        try {
            return new String(aesDecrypt(Hex.decodeHex(input.toCharArray()), Hex.decodeHex(key.toCharArray())), DEFAULT_URL_ENCODING);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 使用AES解密字符串, 返回原始字符串.
     *
     * @param input Hex编码的加密字符串
     * @param key   符合AES要求的密钥
     */
    public static byte[] aesDecrypt(byte[] input, byte[] key) throws Exception {
        return aes(input, key, Cipher.DECRYPT_MODE);
    }

    /**
     * 使用AES解密字符串, 返回原始字符串.
     *
     * @param input Hex编码的加密字符串
     * @param key   符合AES要求的密钥
     * @param iv    初始向量
     */
    public static byte[] aesDecrypt(byte[] input, byte[] key, byte[] iv) throws Exception {
        return aes(input, key, iv, Cipher.DECRYPT_MODE);
    }

    /**
     * 使用AES加密或解密无编码的原始字节数组, 返回无编码的字节数组结果.
     *
     * @param input 原始字节数组
     * @param key   符合AES要求的密钥
     * @param mode  Cipher.ENCRYPT_MODE 或 Cipher.DECRYPT_MODE
     */
    private static byte[] aes(byte[] input, byte[] key, int mode) throws Exception {
        SecretKey secretKey = new SecretKeySpec(key, AES);
        Cipher cipher = Cipher.getInstance(AES);
        cipher.init(mode, secretKey);
        return cipher.doFinal(input);
    }

    /**
     * 使用AES加密或解密无编码的原始字节数组, 返回无编码的字节数组结果.
     *
     * @param input 原始字节数组
     * @param key   符合AES要求的密钥
     * @param iv    初始向量
     * @param mode  Cipher.ENCRYPT_MODE 或 Cipher.DECRYPT_MODE
     */
    private static byte[] aes(byte[] input, byte[] key, byte[] iv, int mode) throws Exception {

        SecretKey secretKey = new SecretKeySpec(key, AES);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        Cipher cipher = Cipher.getInstance(AES_CBC);
        cipher.init(mode, secretKey, ivSpec);
        return cipher.doFinal(input);

    }

    /**
     * 生成AES密钥,返回字节数组, 默认长度为128位(16字节).
     */
    public static String generateAesKeyString() throws NoSuchAlgorithmException {
        return new String(Hex.encodeHex(generateAesKey(DEFAULT_AES_KEY_SIZE)));
    }

    /**
     * 生成AES密钥,返回字节数组, 默认长度为128位(16字节).
     */
    public static byte[] generateAesKey() throws NoSuchAlgorithmException {
        return generateAesKey(DEFAULT_AES_KEY_SIZE);
    }

    /**
     * 生成AES密钥,可选长度为128,192,256位.
     */
    public static byte[] generateAesKey(int keysize) throws NoSuchAlgorithmException {

        KeyGenerator keyGenerator = KeyGenerator.getInstance(AES);
        keyGenerator.init(keysize);
        SecretKey secretKey = keyGenerator.generateKey();
        return secretKey.getEncoded();

    }


    public static String generateIVString() throws NoSuchAlgorithmException {
        return new String(Hex.encodeHex(generateIV()));
    }

    /**
     * 生成随机向量,默认大小为cipher.getBlockSize(), 16字节.
     */
    public static byte[] generateIV() {
        byte[] bytes = new byte[DEFAULT_IV_SIZE];
        random.nextBytes(bytes);
        return bytes;
    }

    /**
     * 微信投诉通知解密
     * @param associatedDataStr
     * @param nonceStr
     * @param ciphertext
     * @param aesKeyStr
     * @return
     * @throws ParseException
     */
    public static String setDecryptData(String associatedDataStr,String nonceStr,String ciphertext,String aesKeyStr) {
        byte[] associatedData = associatedDataStr.getBytes(StandardCharsets.UTF_8);
        byte[] nonce = nonceStr.getBytes(StandardCharsets.UTF_8);
        byte[] aesKey = aesKeyStr.getBytes(StandardCharsets.UTF_8);
        String decryptData = null;
        try {
            decryptData = decryptToString(associatedData, nonce, ciphertext,aesKey);
        } catch (GeneralSecurityException e) {
            e.printStackTrace();
        }
        return decryptData;
    }
    public static String decryptToString(byte[] associatedData, byte[] nonce, String ciphertext,byte[] aesKey) throws GeneralSecurityException {
        try {
            SecretKeySpec key = new SecretKeySpec(aesKey, "AES");
            GCMParameterSpec spec = new GCMParameterSpec(DEFAULT_AES_KEY_SIZE, nonce);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, key, spec);
            cipher.updateAAD(associatedData);
            return new String(cipher.doFinal(Base64.getDecoder().decode(ciphertext)), StandardCharsets.UTF_8);

        } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException | InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        }
        return null;
    }


    // 解密
    public static String decrypt(String sSrc, String sKey) throws Exception {
        if (sSrc == null) {
            return null;
        }
        String ss = new String(org.apache.commons.codec.binary.Base64.decodeBase64(sSrc));
        try {
            byte[] raw = sKey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, AES);
            Cipher cipher = Cipher.getInstance(AES_CBC);
            IvParameterSpec iv = new IvParameterSpec(IV_PARAMETER.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] original = cipher.doFinal(hexToByteArray(ss));
            String originalString = new String(original, "utf-8");
            return originalString.trim();
        } catch (Exception ex) {
            return null;
        }
    }
    public static byte[] hexToByteArray(String inHex) {
        int hexlen = inHex.length();
        byte[] result;
        if (hexlen % 2 == 1) {
            // 奇数
            hexlen++;
            result = new byte[(hexlen / 2)];
            inHex = "0" + inHex;
        } else {
            // 偶数
            result = new byte[(hexlen / 2)];
        }
        int j = 0;
        for (int i = 0; i < hexlen; i += 2) {
            result[j] = hexToByte(inHex.substring(i, i + 2));
            j++;
        }
        return result;
    }
    public static byte hexToByte(String inHex) {
        return (byte) Integer.parseInt(inHex, 16);
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        System.out.println(generateAesKeyString());
    }
}
