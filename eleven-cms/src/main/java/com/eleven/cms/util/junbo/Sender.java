package com.eleven.cms.util.junbo;

import org.apache.commons.lang3.tuple.Pair;

import java.nio.charset.StandardCharsets;

/**
 * 发送方
 * <AUTHOR>
 *
 */
public class Sender {

	private byte[] aesKey ;
	private byte[] iv ;
	private String privateKey;
	private String publicKey;

	/**
	 * 私钥加密发送
	 * @return	加密后内容
	 */
	public Pair<String,String> sendByPrive(String privateKey, String version, String senderJson) throws Exception{
		this.privateKey = privateKey;
		//创建接收方,进行发送	
		String versionC = version + senderJson;//版本号+内容json串形成versionC
		//String result = "";
		aesKey = CryptoUtility.sha256(versionC);//256散列数组 作为aesKey

		// AES 向量
		iv = CryptoUtility.aesIvParameter(aesKey);

		String K = CryptoUtility.base64(CryptoUtility.encryptByPriveKey(CryptoUtility.loadPrivateKey(privateKey), aesKey));//用户自己的秘钥对AESkey加密
		//System.out.println("K:"+K);
		//String temK = K;
		//K = java.net.URLEncoder.encode(K);//用于url传输
		String C = CryptoUtility.base64(CryptoUtility.aseEncrypt(senderJson, aesKey, iv));
		//System.out.println("C:"+C);
		//String temC = C;
		//C = java.net.URLEncoder.encode(C);
		//String param = "V=" + version + "&K=" + K + "&C=" + C;
		//return param;
		return Pair.of(K,C);
	}
	

	/**
	 * 公钥加密发送
	 * @return	加密后内容
	 * @throws Exception 
	 */
	public String sendByPublic(String publicKey,String version,String senderJson) throws Exception{
		this.publicKey = publicKey;
		//创建接收方,进行发送	
		String versionC = version + senderJson;//版本号+内容json串形成versionC
		aesKey = CryptoUtility.sha256(versionC);//256散列数组 作为aesKey

		// AES 向量
		iv = CryptoUtility.aesIvParameter(aesKey);

		String K = CryptoUtility.base64(CryptoUtility.encryptByPublicKey(CryptoUtility.loadPublicKey(publicKey), aesKey));//用户自己的秘钥对AESkey加密
		System.out.println("K:"+K);
		String temK = K;
		K = java.net.URLEncoder.encode(K);//用于url传输
		String C = CryptoUtility.base64(CryptoUtility.aseEncrypt(senderJson, aesKey, iv));
		System.out.println("C:"+C);
		String temC = C;
		C = java.net.URLEncoder.encode(C);
		String param = "V=" + version + "&K=" + K + "&C=" + C;
		return param;
	}
	
	/**
	 * 对结果进行解密
	 * @return
	 */
	public String aesDecrypt(String recerverResult) throws Exception{
		if(aesKey==null){
			throw new Exception("未对aesKey初始化,需要先调用发送到服务方");
		}

		byte[] baseResult = CryptoUtility.baseDecode(recerverResult);

		byte[] aesResult = CryptoUtility.aesdecrypt(baseResult, aesKey, iv);


		return new String(aesResult, StandardCharsets.UTF_8);
	}

}

