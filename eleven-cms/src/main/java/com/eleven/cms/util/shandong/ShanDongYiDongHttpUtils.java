package com.eleven.cms.util.shandong;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.eleven.cms.dto.*;
import com.eleven.cms.util.JacksonUtils;
import com.fasterxml.jackson.databind.node.ObjectNode;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.jeecg.common.util.DateUtils;

import java.util.Date;
import java.util.Map;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;
import static com.eleven.cms.aivrbt.utils.HttpUtils.SYNC_ORDER_CLIENT;
import static org.springframework.util.MimeTypeUtils.APPLICATION_JSON_VALUE;

/**
 * <AUTHOR>
 * @datetime 2024/11/20 15:37
 */
public class ShanDongYiDongHttpUtils {

    private static final MediaType JSON = MediaType.parse(APPLICATION_JSON_VALUE);
    private static final String CHANNEL_CODE = "HSTJ";

    public static String countCodeQuery(ShanDongCountCodeQueryDTO shanDongCountCodeQueryDTO, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://m.sd.10086.cn/zapi/h5_business/strategy/countCodeQuery");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("telnum", shanDongCountCodeQueryDTO.getTelnum());
        objectNode.put("channelCode", CHANNEL_CODE);
        objectNode.put("countCode", shanDongCountCodeQueryDTO.getCountCode());
        objectNode.put("sign", RSAUtils.sign((CHANNEL_CODE + "_" + shanDongCountCodeQueryDTO.getCountCode() + "_" + shanDongCountCodeQueryDTO.getTelnum()).getBytes()));
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(COMMON_CLIENT, url, body, shanDongCountCodeQueryDTO.getLogTag(), "策略中心2.0", extraInfoMap);
    }

    public static String getSmsCode(ShanDongGetSmsCodeDTO shanDongGetSmsCodeDTO, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://m.sd.10086.cn/zapi/h5_business/orderMangerPacke/getSmsCode");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("telnum", shanDongGetSmsCodeDTO.getTelnum());
        objectNode.put("channelCode", CHANNEL_CODE);
        objectNode.put("countCode", shanDongGetSmsCodeDTO.getCountCode());
        objectNode.put("sign", RSAUtils.sign((shanDongGetSmsCodeDTO.getTelnum() + "_" + CHANNEL_CODE).getBytes()));
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(COMMON_CLIENT, url, body, shanDongGetSmsCodeDTO.getLogTag(), "短信验证码获取", extraInfoMap);
    }

    public static String order(ShanDongOrderDTO shanDongOrderDTO, Map<String, Object> extraInfoMap) {
        String orderId = "CC" + DateUtils.yyyyMMdd.get().format(new Date()) + IdWorker.getId();

        HttpUrl url = HttpUrl.parse("https://m.sd.10086.cn/zapi/h5_business/orderMangerPacke/orderCommitNotStore");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("telnum", shanDongOrderDTO.getTelnum());
        objectNode.put("channelCode", CHANNEL_CODE);
        objectNode.put("countCode", shanDongOrderDTO.getCountCode());
        objectNode.put("sign", RSAUtils.sign((orderId + "_" + CHANNEL_CODE).getBytes()));
        objectNode.put("cityCode", "531");
        objectNode.put("isNotify", "1");
        objectNode.put("refereeTel", "");
        objectNode.put("operId", "");
        objectNode.put("orderId", orderId);
        objectNode.put("smsCode", shanDongOrderDTO.getSmsCode());
        objectNode.put("handleUrl", shanDongOrderDTO.getHandleUrl());
        objectNode.put("deviceCode", shanDongOrderDTO.getDeviceCode());
        objectNode.put("platformCode", shanDongOrderDTO.getPlatformCode());
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(SYNC_ORDER_CLIENT,url, body, shanDongOrderDTO.getLogTag(), "封装办理", extraInfoMap);
    }

    public static String qryOrderComplaint(ShanDongOrderComplaintQryDTO complaintQryDTO) {
        HttpUrl url = HttpUrl.parse("https://m.sd.10086.cn/zapi/h5_business/orderMangerPacke/qryOrderComplaint");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("time", complaintQryDTO.getTime());
        objectNode.put("page", complaintQryDTO.getPage());
        objectNode.put("channelCode", CHANNEL_CODE);
        objectNode.put("sign", RSAUtils.sign((complaintQryDTO.getTime() + "_" + CHANNEL_CODE).getBytes()));
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(COMMON_CLIENT,url, body, CHANNEL_CODE, "查询投诉信息");
    }

    public static String unsubscribe(ShanDongUnsubscribeDTO unsubscribeDTO) {
        HttpUrl url = HttpUrl.parse("https://m.sd.10086.cn/zapi/h5_business/orderMangerPacke/unsubscribe");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("opTime", unsubscribeDTO.getOpTime());
        objectNode.put("channelCode", CHANNEL_CODE);
        objectNode.put("sign", RSAUtils.sign((unsubscribeDTO.getOpTime() + "_" + CHANNEL_CODE).getBytes()));
        objectNode.put("page", unsubscribeDTO.getPage());
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(COMMON_CLIENT,url, body, CHANNEL_CODE, "查询退订订单");
    }

    public static String settlement(ShanDongSettlementDTO settlementDTO) {
        HttpUrl url = HttpUrl.parse("https://m.sd.10086.cn/zapi/h5_business/orderMangerPacke/settlement");
        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("cycle", settlementDTO.getCycle());
        objectNode.put("channelCode", CHANNEL_CODE);
        objectNode.put("sign", RSAUtils.sign((settlementDTO.getCycle() + "_" + CHANNEL_CODE).getBytes()));
        objectNode.put("page", settlementDTO.getPage());
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        return HttpUtils.post(COMMON_CLIENT,url, body, CHANNEL_CODE, "查询结算订单");
    }
}
