package com.eleven.cms.util;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.oConvertUtils;

/**
 * 业务订购判断
 */
public enum OrderProductEnum {
    /**
     * 20元渠道包月包
     */
    Q6("698039020108689345", "00210Q6", "20元渠道包月包"),
    MK("698039020108689345", "00210MK", "20元渠道包月包"),
    T9("698039020108689345", "00210T9", "20元渠道包月包"),
    /**
     * 10元渠道包月包
     */
    L("698039020050006172", "002103L", "10元渠道包月包"),
    /**
     * 咪咕音乐白金会员畅听版5元
     */
    QX("698039020108799364", "00210QX","咪咕音乐白金会员畅听版5元"),
    /**
     * 咪咕音乐超清白金会员10元
     */
    PP("698039020105522717", "00210PP", "咪咕音乐超清白金会员10元"),
    SR("698039020105522717", "00210SR", "咪咕音乐超清白金会员10元"),
    /**
     * 订阅包
     */
    OC("698039035100000014", "00210OC", "订阅包"),
    QG("698039035100000014", "00210QG", "订阅包"),
    QQ("698039035100000014", "00210QQ", "订阅包"),
    /**
     * 订阅包(麦禾)
     */
    QZ("698039035100000014", "00210QZ", "订阅包(麦禾)"),
    /**
     * 订阅包(悠然荐音-已配置成统一认证登录)
     */
    K7("698039035100000014", "00210K7", "订阅包(悠然荐音-已配置成统一认证登录)"),

    /**
     * 渠道包
     */
     OW("698039042105792434", "00210OW", "渠道包"),
     QH("698039042105792434", "00210QH", "渠道包"),
     QR("698039042105792434", "00210QR", "渠道包"),
     QY("698039042105792434", "00210QY", "渠道包"),
     NX("698039042105792434", "00210NX", "渠道包"),
    /**
     * 彩铃运营中心订阅包3
     */
     C("698039035100000057", "014X04C", "彩铃运营中心订阅包3"),
     D("698039035100000057", "014X04D", "彩铃运营中心订阅包3"),
     E("698039035100000057", "014X04E", "彩铃运营中心订阅包3"),
     F("698039035100000057", "014X04F", "彩铃运营中心订阅包3"),
    /**
     * 电信天翼空间
     */
    TIANYI("90030426", "tianyi", "电信天翼空间"),
    /**
     * 光明网-电信爱音乐
     */
    GUANGMINGWANG("7120", "guangmingwang", "光明网-电信爱音乐"),
    /**
     * 上海移动-抖音视频彩铃
     */
    VRBTDY("111000788275", "vrbtdy", "上海移动-抖音视频彩铃"),
    /**
     * 四川移动-视频彩铃
     */
    SCVRBT("111000788276", "SCVRBT", "四川移动-视频彩铃"),
    /**
     * 第三方
     */
    DISANFANG("disanfang", "disanfang", "第三方");



    /**
     * 业务id
     */
    private String serviceId;
    /**
     * 渠道号
     */
    private String channelCode;
    /**
     * 业务名称
     */
    private String packName;

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getPackName() {
        return packName;
    }

    public void setPackName(String packName) {
        this.packName = packName;
    }

    OrderProductEnum(String serviceId, String channelCode, String packName) {
        this.serviceId = serviceId;
        this.channelCode = channelCode;
        this.packName = packName;
    }

    public static OrderProductEnum getByServiceId(String serviceId) {
        if (oConvertUtils.isEmpty(serviceId)) {
            return null;
        }
        for (OrderProductEnum val : values()) {
            if (val.getServiceId().contains(serviceId)) {
                return val;
            }
        }
        return null;
    }
    public static OrderProductEnum getByServiceId(String serviceId,String channelCode) {
        if (oConvertUtils.isEmpty(serviceId)) {
            return null;
        }
        for (OrderProductEnum val : values()) {
            if (val.getServiceId().contains(serviceId) && val.getChannelCode().contains(channelCode)) {
                return val;
            }
        }
        return null;
    }

    //是否发送pptv权益
    public static Boolean isPptvRights(String serviceId,String channelCode) {
        if (oConvertUtils.isEmpty(serviceId)) {
            return false;
        }
        OrderProductEnum orderEnum=OrderProductEnum.getByServiceId(serviceId,channelCode);
        if(orderEnum==null){
            return false;
        }
        switch (orderEnum){
            /**
             * 20元渠道包月包
             */
            case Q6:
                return true;
            case MK:
                return true;
            case T9:
                return true;
            /**
             * 10元渠道包月包
             */
            case L:
                return true;
            /**
             * 咪咕音乐白金会员畅听版5元
             */
            case QX:
                return true;
                /**
                 * 咪咕音乐超清白金会员10元
                 */
            case PP:
                return false;
            case SR:
                return false;
            /**
             * 订阅包
             */
            case OC:
                return true;
            case QG:
                return true;
            case QQ:
                return true;
            /**
             * 订阅包(麦禾)
             */
            case QZ:
                return true;
            /**
             * 订阅包(悠然荐音-已配置成统一认证登录)
             */
            case K7:
                return true;
            /**
             * 渠道包
             */
            case OW:
                return true;
            case QH:
                return true;
            case QR:
                return true;
            case QY:
                return true;
            case NX:
                return true;
            /**
             * 彩铃运营中心订阅包3
             */
            case C:
                return false;
            case D:
                return false;
            case E:
                return false;
            case F:
                return false;
            /**
             * 电信天翼空间
             */
            case TIANYI:
                return true;
            /**
             * 光明网-电信爱音乐
             */
            case GUANGMINGWANG:
                return true;
            /**
             * 上海移动-抖音视频彩铃
             */
            case VRBTDY:
                return true;
            /**
             * 四川移动-视频彩铃
             */
            case SCVRBT:
                return true;
            /**
             * 第三方
             */
            case DISANFANG:
                return true;
            default: return false;
        }
    }

    //是否发送pptv通知短信
    public static Boolean isPptvSms(String serviceId,String channelCode) {
        if (oConvertUtils.isEmpty(serviceId)) {
            return false;
        }
        OrderProductEnum orderEnum=OrderProductEnum.getByServiceId(serviceId,channelCode);
        if(orderEnum==null){
            return false;
        }
        switch (orderEnum){
            /**
             * 20元渠道包月包
             */
            case Q6:
                return false;
            case MK:
                return false;
            case T9:
                return false;
                /**
                 * 10元渠道包月包
                 */
            case L:
                return false;
                /**
                 * 咪咕音乐白金会员畅听版5元
                 */
            case QX:
                /**
                 * 咪咕音乐超清白金会员10元
                 */
            case PP:
                return false;
            case SR:
                return false;
                /**
                 * 订阅包
                 */
            case OC:
                return true;
            case QG:
                return true;
            case QQ:
                return true;
                /**
                 * 订阅包(麦禾)
                 */
            case QZ:
                return true;
                /**
                 * 订阅包(悠然荐音-已配置成统一认证登录)
                 */
            case K7:
                return true;
                /**
                 * 渠道包
                 */
            case OW:
                return true;
            case QH:
                return true;
            case QR:
                return true;
            case QY:
                return true;
            case NX:
                return true;
                /**
                 * 彩铃运营中心订阅包3
                 */
            case C:
                return false;
            case D:
                return false;
            case E:
                return false;
            case F:
                return false;
                /**
                 * 电信天翼空间
                 */
            case TIANYI:
                return false;
                /**
                 * 光明网-电信爱音乐
                 */
            case GUANGMINGWANG:
                return false;
                /**
                 * 上海移动-抖音视频彩铃
                 */
            case VRBTDY:
                return false;
                /**
                 * 四川移动-视频彩铃
                 */
            case SCVRBT:
                return false;
                /**
                 * 第三方
                 */
            case DISANFANG:
                return false;
            default: return false;
        }
    }

    /**
     * 是否彩铃中心业务
     * @param serviceId
     * @param orderkey
     * @param resCode
     * @return
     */
    public static Boolean isCLZX(String serviceId, String orderkey, String resCode, String channelCode) {
        if (oConvertUtils.isEmpty(serviceId) || oConvertUtils.isEmpty(orderkey) || oConvertUtils.isEmpty(resCode)) {
            return false;
        }
        OrderProductEnum orderEnum=OrderProductEnum.getByServiceId(serviceId,channelCode);
        if(orderEnum==null){
            return false;
        }
        switch (orderEnum){
            /**
             * 订阅包
             */
            case OC:
            case QG:
            case QQ:
            case QZ:
            case K7:
            /**
             * 渠道包
             */
            case OW:
            case QH:
            case QR:
            case QY:
            case NX:
            /**
             * 彩铃运营中心订阅包3
             */
            case C:
            case D:
            case E:
            case F:
                return StringUtils.equals("SPBY",orderkey) && StringUtils.equals("000000",resCode);
            default: return false;
        }
    }
}
