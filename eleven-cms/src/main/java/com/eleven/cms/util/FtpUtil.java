package com.eleven.cms.util;

import com.eleven.cms.config.FtpProperties;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.*;

public class FtpUtil {
    private static final Logger logger = LoggerFactory.getLogger(FtpUtil.class);


    /**
     * 初始化FTP服务器
     *
     * @return
     */
    public static FTPClient getFtpClient(String hostname,Integer port,String username,String password) {

        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        try {
            //设置连接超时时间
            ftpClient.setDataTimeout(1000 * 120);
            logger.info("连接FTP服务器中:" + hostname + ":" + port);
            //连接ftp服务器
            ftpClient.connect(hostname, port);
            //登录ftp服务器
            ftpClient.login(username, password);
            // 是否成功登录服务器
            int replyCode = ftpClient.getReplyCode();
            if (FTPReply.isPositiveCompletion(replyCode)) {
                logger.info("连接FTP服务器成功:" + hostname + ":" + port);
            } else {
                logger.error("连接FTP服务器失败:" + hostname + ":" + port);
                closeFtpClient(ftpClient);
            }
        } catch (IOException e) {
            logger.error("连接ftp服务器异常", e);
        }
        return ftpClient;
    }


    /**
     * 上传文件
     *
     * @param pathName    路径
     * @param fileName    文件名
     * @param inputStream 输入文件流
     * @return
     */
    public static boolean uploadFileToFtp(String pathName,
                                          String fileName,
                                          InputStream inputStream,
                                          String hostname,
                                          Integer port,
                                          String username,
                                          String password) {
        boolean isSuccess = false;
        FTPClient ftpClient = getFtpClient(hostname,port,username,password);
        try {
            if (ftpClient.isConnected()) {
                logger.info("开始上传文件到FTP,文件名称:" + fileName);
                //设置上传文件类型为二进制，否则将无法打开文件
                ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
                //路径切换，如果目录不存在创建目录
                if (!ftpClient.changeWorkingDirectory(pathName)) {
                    boolean flag = changeAndMakeWorkingDir(ftpClient, pathName);
                    if (!flag) {
                        logger.error("路径切换(创建目录)失败");
                        return false;
                    }
                }
                //设置被动模式，文件传输端口设置(如上传文件夹成功，不能上传文件，注释这行，否则报错refused:connect)
                ftpClient.enterLocalPassiveMode();
                ftpClient.storeFile(fileName, inputStream);
                inputStream.close();
                ftpClient.logout();
                isSuccess = true;
                logger.info(fileName + "文件上传到FTP成功");
            } else {
                logger.error("FTP连接建立失败");
            }
        } catch (Exception e) {
            logger.error(fileName + "文件上传异常", e);

        } finally {
            closeFtpClient(ftpClient);
            closeStream(inputStream);
        }
        return isSuccess;
    }

    /**
     * 删除文件
     *
     * @param pathName 路径
     * @param fileName 文件名
     * @return
     */
    public static boolean deleteFile(String pathName,
                                     String fileName,
                                     String hostname,
                                     Integer port,
                                     String username,
                                     String password) {
        boolean flag = false;
        FTPClient ftpClient = getFtpClient(hostname,port,username,password);
        try {
            logger.info("开始删除文件");
            if (ftpClient.isConnected()) {
                //路径切换
                ftpClient.changeWorkingDirectory(pathName);
                ftpClient.enterLocalPassiveMode();
                ftpClient.dele(fileName);
                ftpClient.logout();
                flag = true;
                logger.info("删除文件成功");
            } else {
                logger.info("删除文件失败");
            }
        } catch (Exception e) {
            logger.error(fileName + "文件删除异常", e);
        } finally {
            closeFtpClient(ftpClient);
        }
        return flag;
    }

    /**
     * 关闭FTP连接
     *
     * @param ftpClient
     */
    public static void closeFtpClient(FTPClient ftpClient) {
        if (ftpClient.isConnected()) {
            try {
                ftpClient.disconnect();
            } catch (IOException e) {
                logger.error("关闭FTP连接异常", e);
            }
        }
    }

    /**
     * 关闭文件流
     *
     * @param closeable
     */
    public static void closeStream(Closeable closeable) {
        if (null != closeable) {
            try {
                closeable.close();
            } catch (IOException e) {
                logger.error("关闭文件流异常", e);
            }
        }
    }

    /**
     * 路径切换（没有则创建）
     *
     * @param ftpClient FTP服务器
     * @param path      路径
     */
    public static boolean changeAndMakeWorkingDir(FTPClient ftpClient, String path) {
        boolean flag = false;
        try {
            String[] path_array = path.split("/");
            for (String s : path_array) {
                boolean b = ftpClient.changeWorkingDirectory(s);
                if (!b) {
                    ftpClient.makeDirectory(s);
                    ftpClient.changeWorkingDirectory(s);
                }
            }
            flag = true;
        } catch (IOException e) {
            logger.error("路径切换异常", e);
        }
        return flag;
    }

    /**
     * 从FTP下载到本地文件夹
     *
     * @param ftpClient      FTP服务器
     * @param pathName       路径
     * @param targetFileName 文件名
     * @param localPath      本地路径
     * @return
     */
    public static boolean downloadFile(FTPClient ftpClient, String pathName, String targetFileName, String localPath) {
        boolean flag = false;
        OutputStream os = null;
        try {
            System.out.println("开始下载文件");
            //切换FTP目录
            ftpClient.changeWorkingDirectory(pathName);
            ftpClient.enterLocalPassiveMode();
            FTPFile[] ftpFiles = ftpClient.listFiles();
            for (FTPFile file : ftpFiles) {
                String ftpFileName = file.getName();
                if (targetFileName.equalsIgnoreCase(ftpFileName)) {
                    File localFile = new File(localPath + targetFileName);
                    os = new FileOutputStream(localFile);
                    ftpClient.retrieveFile(file.getName(), os);
                    os.close();
                }
            }
            ftpClient.logout();
            flag = true;
            logger.info("下载文件成功");
        } catch (Exception e) {
            logger.error("下载文件失败", e);
        } finally {
            closeFtpClient(ftpClient);
            closeStream(os);
        }
        return flag;
    }

    public static void main(String[] a){


        String hostname = "***************";
        Integer port = 21;
        String username = "638799";
        String password = "I!y9uOHp";

        try {
            File file = new File("C:\\Users\\<USER>\\Downloads\\20230707175705.tif");
            InputStream inputStream = null;
            inputStream = new FileInputStream(file);
            uploadFileToFtp("/userDiy",
                    "咪咕音乐有限公司数字音视频内容版权授权书.tif",
                    inputStream,
                    hostname,
                    port,
                    username,
                    password);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

    }

}