package com.eleven.cms.util;

import java.security.Key;
import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import org.apache.commons.codec.binary.Base64;

/**
 * Desc: DES工具类(讯飞加密手机号用)
 */
public class DesUtil {
    private final static String DES = "DES";

    // 加密块链模式--推荐
    public static final String DES_CBC = "DES/CBC/PKCS5Padding";

    // 字符编码
    public static final String CHARSET_UTF8 = "UTF-8";


    /**
     * DES加密字符串-CBC加密格式
     *
     * @param password
     *            加密密码，长度不能够小于8位
     * @param data
     *            待加密字符串
     * @return 加密后内容
     */
    public static String encryptCBC(String password, String iv, String data) {
        if (password == null || password.length() < 8) {
            throw new RuntimeException("加密失败，key不能小于8位");
        }
        if (data == null) {
            return null;
        }
        try {
            Key secretKey = generateKey(password);
            Cipher cipher = Cipher.getInstance(DES_CBC);
            IvParameterSpec spec = new IvParameterSpec(
                    iv.getBytes(CHARSET_UTF8));
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, spec);
            byte[] bytes = cipher.doFinal(data.getBytes(CHARSET_UTF8));
            // JDK1.8及以上可直接使用Base64，JDK1.7及以下可以使用BASE64Encoder
            return new String(Base64.encodeBase64(bytes));

        } catch (Exception e) {
            e.printStackTrace();
            return data;
        }
    }

    /**
     * 生成key
     *
     * @param password
     * @return
     * @throws Exception
     */
    private static Key generateKey(String password) throws Exception {
        DESKeySpec dks = new DESKeySpec(password.getBytes(CHARSET_UTF8));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        return keyFactory.generateSecret(dks);
    }
    /**
     * DES解密字符串--CBC格式
     *
     * @param password
     *            解密密码，长度不能够小于8位
     * @param data
     *            待解密字符串
     * @return 解密后内容
     */
    public static String decryptCBC(String password, String iv, String data)
            throws Exception {
        Key secretKey = generateKey(password);
        Cipher cipher = Cipher.getInstance(DES_CBC);
        IvParameterSpec spec = new IvParameterSpec(
                iv.getBytes(CHARSET_UTF8));
        cipher.init(Cipher.DECRYPT_MODE, secretKey, spec);
        return new String(cipher.doFinal(Base64.decodeBase64(
                data.getBytes(CHARSET_UTF8))), CHARSET_UTF8);

    }
    public static void main(String[] args) throws Exception {
        String value = encryptCBC("!iflytek","!iflytek","17755105631#20230315102023");
        System.out.println(value);
        System.out.println(decryptCBC("!iflytek","!iflytek", value));
    }
}

