package com.eleven.cms.util;

import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;

import java.security.SecureRandom;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/14 18:18
 **/
public class MySm2Engine {
    private MySm2Engine()
    {

    }

    public static final int TYPE_ENCODE = 0;

    public static final int TYPE_DECODE = 1;

    /**
     * 创建一个SM2引擎
     * @param pubKey
     * @param priKey
     * @param enOrde
     * @return
     * @throws Exception
     */
    public static SM2Engine createMySm2Engine(ECPublicKeyParameters pubKey, ECPrivateKeyParameters priKey, int enOrde)
    {
        if (enOrde == TYPE_ENCODE)
        {
            SM2Engine sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            sm2Engine.init(true, new ParametersWithRandom(pubKey, new SecureRandom()));
            return sm2Engine;
        }
        else
        {
            SM2Engine sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            sm2Engine.init(false, priKey);
            return sm2Engine;
        }
    }
}
