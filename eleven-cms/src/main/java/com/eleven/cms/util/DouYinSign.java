package com.eleven.cms.util;
import java.security.PublicKey;
import com.google.common.collect.Lists;

import java.security.PrivateKey;
import java.security.Signature;
import java.nio.charset.StandardCharsets;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;

/**
 * 抖音小程序下单生成密钥
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/25 14:51
 **/
public class DouYinSign {
    public static Map<String, String> getByteAuthorization(String skuId,Integer price,String title,String image,String outTradeNo,String path,String appId,String privateKey,String keyVersion) {
        Map<String, Object> data = Maps.newHashMap();

        Map<String, Object> skuMap = Maps.newHashMap();
        //外部商品id，如：号卡商品id、会员充值套餐id、某类服务id、付费工具id等
        skuMap.put("skuId",skuId);
        //价格 单位：分
        skuMap.put("price",price);
        //购买数量 0 < quantity <= 100
        skuMap.put("quantity",1);
        //商品标题，长度 <= 256字节
        skuMap.put("title",title);
        //商品图片链接，长度 <= 512 字节 注意：目前只支持传入一项
        List imageList = Lists.newLinkedList();
        imageList.add(image);
        skuMap.put("imageList",imageList);



        //商品类型
        //101：号卡商品
        //102：通信定制类商品(彩铃)
        //103：话费/宽带充值类商品
        //201：通用咨询类商品
        //202:  代写文书
        //301：虚拟工具类商品
        //302：信息付费
        //401：内容消费类商品
        //注意：根据接入规范，选择适合的商品类型ID传入
        skuMap.put("type",102);


        //交易规则标签组

//        tag_group_7272625659887960076
//        未制作全额退：
//        定制类服务未受理制作前可全额退款
//        定制后协商退：
//        开始定制后如有特殊退款诉求可与卖家协商退款，定制完成交付后将不再支持退款
//
//        tag_group_7272625659887976460
//        未制作全额退：
//        定制类服务未受理制作前可全额退款
//        定制后不可退：
//        开始定制后不支持无理由退款
        skuMap.put("tagGroupId","tag_group_7272625659887976460");

        List skuList=Lists.newLinkedList();
        skuList.add(skuMap);
        data.put("skuList",skuList);

        //外部订单号
        data.put("outOrderNo", outTradeNo);
        //订单总金额 单位：分
        data.put("totalAmount",price);


//        屏蔽的支付方式，当开发者没有进件某个支付渠道，可在下单时屏蔽对应的支付方式。如：[1, 2]表示屏蔽微信和支付宝
//        枚举说明：
//        1-微信
//        2-支付宝
        List limitPayWayList = Lists.newLinkedList();
        data.put("limitPayWayList",limitPayWayList);



//        支付超时时间，单位秒，例如 300 表示 300 秒后过期；不传或传 0 会使用默认值 300，不能超过48小时。
        data.put("payExpireSeconds",300);
        //订单详情页
        Map<String, String> orderEntrySchema = new HashMap<>();
        //小程序xxx详情页跳转路径，没有前导的“/”，路径后不可携带query参数，路径中不可携带『？: & *』等特殊字符，路径只可以是『英文字符、数字、_、/ 』等组成，长度<=512byte
        orderEntrySchema.put("path",path);
        data.put("orderEntrySchema", orderEntrySchema);
        // 请求时间戳
        long timestamp = System.currentTimeMillis()/1000L;
        // 随机字符串
        String nonceStr = UUID.randomUUID().toString();
        String byteAuthorization=getByteAuthorization(privateKey, JSONObject.toJSONString(data), appId, nonceStr, timestamp, keyVersion);
        Map<String, String> dataMap=Maps.newHashMap();
        dataMap.put("data", JSONObject.toJSONString(data));
        dataMap.put("byteAuthorization", byteAuthorization);
        return dataMap;
    }
    private static String getByteAuthorization(String privateKeyStr, String data, String appId, String nonceStr, long timestamp, String keyVersion) {
        String byteAuthorization = "";
        try {
            // 生成签名
            String signature = getSignature(privateKeyStr, "POST", "/requestOrder", timestamp, nonceStr, data);
            // 构造byteAuthorization
            StringBuilder sb = new StringBuilder();
            sb.append("SHA256-RSA2048 ").
                    append("appid=").append(appId).append(",").
                    append("nonce_str=").append(nonceStr).append(",").
                    append("timestamp=").append(timestamp).append(",").
                    append("key_version=").append(keyVersion).append(",").
                    append("signature=").append(signature);
            byteAuthorization = sb.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
        return byteAuthorization;
    }

    public static String getSignature(String privateKeyStr, String method, String uri, long timestamp, String nonce, String data) throws Exception {
        String rawStr = method + "\n" +
                uri + "\n" +
                timestamp + "\n" +
                nonce + "\n" +
                data + "\n";
        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initSign(string2PrivateKey(privateKeyStr));
        sign.update(rawStr.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(sign.sign());
    }

    private static PrivateKey string2PrivateKey(String privateKeyStr) {
        PrivateKey prvKey = null;
        try {
            byte[] privateBytes = Base64.getDecoder().decode(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            prvKey = keyFactory.generatePrivate(keySpec);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return prvKey;
    }


    public static boolean verify(String httpBody, String publicKey, String signStr, Long timestamp, String nonce) throws Exception {
        StringBuffer buffer = new StringBuffer();
        buffer.append(timestamp).append("\n");
        buffer.append(nonce).append("\n");
        buffer.append(httpBody).append("\n");
        String message = buffer.toString();
        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initVerify(string2PublicKey(publicKey)); // 注意验签时publicKey使用平台公钥而非应用公钥
        sign.update(message.getBytes(StandardCharsets.UTF_8));
        return sign.verify(Base64.getDecoder().decode(signStr.getBytes(StandardCharsets.UTF_8)));
    }

    public static PublicKey string2PublicKey(String publicKey) throws Exception{
        byte[] decoded = Base64.getDecoder().decode(publicKey);
        return KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
    }



}
