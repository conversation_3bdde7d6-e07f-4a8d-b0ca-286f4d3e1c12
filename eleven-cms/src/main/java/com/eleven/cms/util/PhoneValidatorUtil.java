package com.eleven.cms.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PhoneValidatorUtil {
    // 手机号正则表达式（主流号段）
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";
    // 预编译正则模式（提高性能）
    private static final Pattern PHONE_PATTERN = Pattern.compile(PHONE_REGEX);

    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return false;
        }

        // 清理非数字字符（保留0-9）
        String cleanedPhone = phone.replaceAll("\\D", "");

        // 检查长度是否为11位
        if (cleanedPhone.length() != 11) {
            return false;
        }

        // 正则匹配
        Matcher matcher = PHONE_PATTERN.matcher(cleanedPhone);
        return matcher.matches();
    }
}
