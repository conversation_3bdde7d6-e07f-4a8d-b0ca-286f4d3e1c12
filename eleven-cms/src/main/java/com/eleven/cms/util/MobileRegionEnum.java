package com.eleven.cms.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.oConvertUtils;

/**
 * 权益业务枚举
 */
public enum MobileRegionEnum {


    WEIZHI("-1", "-1","未知"),
    YIDONG("1", "1","移动"),
    LIANTONG("2", "3","联通"),
    DIANXIN("3", "4","电信"),
    GUANGDIAN("4", "5","广电");
    private String after;

    private String operator;

    private String optDesc;
    MobileRegionEnum(String after, String operator,String optDesc) {
        this.after = after;
        this.operator = operator;
        this.optDesc = optDesc;
    }
    public String getAfter() {
        return after;
    }

    public void setAfter(String after) {
        this.after = after;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOptDesc() {
        return optDesc;
    }

    public void setOptDesc(String optDesc) {
        this.optDesc = optDesc;
    }

    public static MobileRegionEnum getByAfter(String after) {
        if (StringUtils.isEmpty(after)) {
            return null;
        }
        for (MobileRegionEnum val : values()) {
            if (val.getAfter().equals(after)) {
                return val;
            }
        }
        return null;
    }
}
