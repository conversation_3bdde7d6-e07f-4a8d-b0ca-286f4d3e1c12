package com.eleven.cms.util;

import java.security.Key;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;

public class AESCodec {
   // 密钥算法
   public static final String KEY_ALGORITHM = "AES";

   // 加解密算法/工作模式/填充方式,Java6.0支持PKCS5Padding填充方式,BouncyCastle支持PKCS7Padding填充方式
   public static final String CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";

   /**
    * 转换密钥
    */
   public static Key toKey(byte[] key) throws Exception {
      return new SecretKeySpec(key, KEY_ALGORITHM);
   }

   /**
    * 加密数据 具体实现： Base64(AES256(明文数据，Base64编码后的密钥))
    * 
    * @param data
    *            待加密数据
    * @param key
    *            密钥， base64编码后的密钥
    * @return 加密后的数据
    * */
   public static String encrypt(String data, String key) throws Exception {
      Key k = toKey(Base64.decodeBase64(key.getBytes())); // 还原密钥
      // 使用PKCS7Padding填充方式,这里就得这么写了(即调用BouncyCastle组件实现)
      // Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM, "BC");
      Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM); // 实例化Cipher对象，它用于完成实际的加密操作
      cipher.init(Cipher.ENCRYPT_MODE, k); // 初始化Cipher对象，设置为加密模式
      return new String(Base64.encodeBase64(cipher.doFinal(data.getBytes()))); // 执行加密操作。加密后的结果通常都会用Base64编码进行传输
   }

   /**
    * 解密数据
    * 
    * @param data
    *            待解密的密文数据
    * @param key
    *            base64编码后的密钥
    * @return 解密后的明文数据
    * */
   public static String decrypt(String data, String key) throws Exception {
      Key k = toKey(Base64.decodeBase64(key.getBytes()));
      Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
      cipher.init(Cipher.DECRYPT_MODE, k); // 初始化Cipher对象，设置为解密模式
      return new String(cipher.doFinal(Base64.decodeBase64(data.getBytes()))); // 执行解密操作
   }

   public static void main(String[] args) throws Exception {

      //byte[] k = "8BD50C0320F874EAE05319AD190AA34F".getBytes();
      byte[] k = "6c67eebe576346b98d383426df94de32".getBytes();
      String keydef = Base64.encodeBase64String(k);
      //// 明文密码
      //String password = "18867115575";
      //System.out.println("明文:" + password);
      //
      //// 加密步骤
      //String sec = encrypt(password, keydef);
      //System.out.println("密文:" + sec);

      // 解密步骤
      //System.out.println("原文:" + decrypt(sec, keydef));
      System.out.println("原文:" + decrypt("a073kK15n1lWaujfzCC+xg==", keydef));
   }
}