package com.eleven.cms.util.junbo;

import com.eleven.cms.config.LiantongVrbtConfig;
import com.eleven.cms.config.LiantongVrbtJunboProperties;
import com.eleven.cms.config.LiantongVrbtProperties;
import com.eleven.cms.util.BizConstant;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: lihb
 * Date: 2022-5-11 17:09:43
 * Desc: 骏伯联通沃音乐计费点相关工具类
 */
@Slf4j
public class LiantongJunboUtils {

    private static LiantongVrbtJunboProperties liantongVrbtJunboProperties = SpringContextUtils.getBean(LiantongVrbtJunboProperties.class);

    public static String generateOrderSign(String phoneNo, String orderSEQ,Long timestamp) {
        StringBuilder builder = new StringBuilder();
        builder.append("backUrl")
                .append(liantongVrbtJunboProperties.getCallbackUrl())
                .append("orderSEQ")
                .append(orderSEQ)
                .append("phoneNo")
                .append(phoneNo)
                .append("partnerNo")
                .append(liantongVrbtJunboProperties.getPartnerNo())
                .append("productId")
                .append(liantongVrbtJunboProperties.getProductId())
                .append("redirectUrl")
                .append(liantongVrbtJunboProperties.getRedirectUrl())
                .append("timeStamp")
                .append(timestamp)
                .append("key")
                .append(liantongVrbtJunboProperties.getKey());
        return DigestUtils.md5DigestAsHex((builder.toString()).getBytes(StandardCharsets.UTF_8));
    }

    public static String generateverifyCodeSign(String phoneNo) {
        StringBuilder builder = new StringBuilder();
        builder.append("phoneNo")
                .append(phoneNo)
                .append("productId")
                .append(liantongVrbtJunboProperties.getProductId())
                .append("partnerNo")
                .append(liantongVrbtJunboProperties.getPartnerNo())
                .append("key")
                .append(liantongVrbtJunboProperties.getKey());
        return DigestUtils.md5DigestAsHex((builder.toString()).getBytes(StandardCharsets.UTF_8));
    }

    public static String generateUnsubscribeSign(String phoneNo, String thirdOrderId, String verifyCode) {
        StringBuilder builder = new StringBuilder();
        builder.append("phoneNo")
                .append(phoneNo)
                .append("productId")
                .append(liantongVrbtJunboProperties.getProductId())
                .append("partnerNo")
                .append(liantongVrbtJunboProperties.getPartnerNo())
                .append("thirdOrderId")
                .append(thirdOrderId)
                .append("verifyCode")
                .append(verifyCode)
                .append("key")
                .append(liantongVrbtJunboProperties.getKey());
        return DigestUtils.md5DigestAsHex((builder.toString()).getBytes(StandardCharsets.UTF_8));
    }

    public static String generateQuerySubInfoSign(String phoneNo) {
        StringBuilder builder = new StringBuilder();
        builder.append("phoneNo")
                .append(phoneNo)
                .append("partnerNo")
                .append(liantongVrbtJunboProperties.getPartnerNo())
                .append("key")
                .append(liantongVrbtJunboProperties.getKey());
        return DigestUtils.md5DigestAsHex((builder.toString()).getBytes(StandardCharsets.UTF_8));
    }


}