package com.eleven.cms.util;

/**
 * <AUTHOR>
 * @datetime 2025/1/22 16:35
 */

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/12/24 14:57
 */
public class TimeUtils {

    /**
     * 获取两个日期之间的所有日期（包括起始日期和结束日期）
     *
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 日期列表
     */
    public static List<String> getDateList(String startDate, String endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        List<String> dateList = new ArrayList<>();
        dateList.add(startDate);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(startDate));
        Date currentDate = calendar.getTime();
        while (currentDate.before(sdf.parse(endDate))) {
            calendar.add(Calendar.DATE, 1); // 日期加一天
            currentDate = calendar.getTime();
            dateList.add(sdf.format(currentDate));
        }
        return dateList;
    }

    /**
     * 获取当日剩余描秒数
     *
     * @return long
     */
    public static long getRemainingSecondOfDay() {
        // 获取当前时间
        ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
        // 获取今天的结束时间（23:59:59）
        ZonedDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59).atZone(ZoneId.systemDefault());
        // 计算当前时间到当天结束的秒数
        return java.time.Duration.between(now, endOfDay).getSeconds();
    }
}
