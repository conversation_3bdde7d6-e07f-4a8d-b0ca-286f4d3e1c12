package com.eleven.cms.util.guizhou;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Util {

    public static String encode(String str) throws Exception {
        return encode(str, null);
    }

    public static String encode(String str, String encoding) throws Exception {
        byte[] input =  null;
        if(encoding==null || encoding.isEmpty()) {
            input = str.getBytes();
        }else{
            input = str.getBytes(encoding);
        }

        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
        }
        catch (NoSuchAlgorithmException e1) {
            throw new RuntimeException("加密失败:待加密串=" + str, e1);
        }

        return encode(md5, input);
    }

    public static String encode(byte[] input) throws Exception {
        if(input==null || input.length==0){
            throw new RuntimeException("加密失败:待加密串为空");
        }
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
        }
        catch (NoSuchAlgorithmException e1) {
            throw new RuntimeException("加密失败:待加密串=" + input, e1);
        }

        return encode(md5, input);
    }

    private static String encode(MessageDigest md5, byte[] input) {
        StringBuffer buf = null;
        md5.update(input);
        byte[] b = md5.digest();

        buf = new StringBuffer("");
        for (int offset = 0; offset < b.length; offset++) {
            int i = b[offset];
            if (i < 0)
                i += 256;
            if (i < 16)
                buf.append("0");
            buf.append(Integer.toHexString(i));
        }
        return buf.toString();
    }
}
