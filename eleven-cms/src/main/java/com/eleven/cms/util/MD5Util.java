package com.eleven.cms.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

@Slf4j
public class MD5Util {
    public static final String FIELD_SIGN = "sign";
    public static String getSign(ObjectNode dataNode) {
        Iterator<Map.Entry<String, JsonNode>> jsonNode = dataNode.fields();
        StringBuffer valueStr = new StringBuffer();
        while (jsonNode.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNode.next();
            String value =  entry.getValue().asText();
            //追加表单信息
            valueStr.append(value+"&");
        }
        String md5 = valueStr.toString();
        md5 = md5.substring(0, md5.length() -1);
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8));
        return sign;
    }

    /**
     * 生成签名
     * @param data 待签名数据
     * @return 签名
     */
    public static String getSign(final Map<String, Object> data,String key){
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            if(data.get(k) !=null && StringUtils.isNotBlank(data.get(k).toString().trim()) && !StringUtils.equals("null",data.get(k).toString().trim())){ // 参数值为空，则不参与签名
                valueStr.append(data.get(k));
            }
        }
        String md5 =valueStr.toString();
        String sign = DigestUtils.md5DigestAsHex((md5+key).getBytes(StandardCharsets.UTF_8));
        return sign;
    }
    /**
     * 生成签名
     * @param dataNode 待签名数据
     * @return 签名
     */
    public static String getSign(final ObjectNode dataNode,String keys){
        Iterator<Map.Entry<String, JsonNode>> jsonNode = dataNode.fields();
        StringBuffer valueStr = new StringBuffer();
        while (jsonNode.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNode.next();
            String key =  entry.getKey();
            String value =  entry.getValue().asText();
            valueStr.append(key+value);
        }
        valueStr.append(keys);
        String md5 = valueStr.toString();
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return sign;
    }

    /**
     * 生成签名
     * @param dataNode
     * @param keys
     * @return
     */
    public static String getXunYouSign(final ObjectNode dataNode,String keys){
        Iterator<Map.Entry<String, JsonNode>> jsonNode = dataNode.fields();
        StringBuffer valueStr = new StringBuffer();
        while (jsonNode.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNode.next();
            String key =  entry.getKey();
            String value =  entry.getValue().asText();
            valueStr.append(key+"="+value+"&");
        }
        String md5 = valueStr.toString()+"key="+keys;
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8));
        return sign;
    }
    /**
     * 生成签名
     * @param data 待签名数据
     * @return 签名
     */
    public static String getSigns(final Map<String, Object> data,String key){
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            if(data.get(k) !=null && StringUtils.isNotBlank(data.get(k).toString().trim()) && !StringUtils.equals("null",data.get(k).toString().trim())){ // 参数值为空，则不参与签名
                valueStr.append(k+"="+data.get(k)+"&");
            }
        }
        String md5 =valueStr.toString()+"secret="+key;
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8));
        return sign;
    }

    /**
     * 生成签名
     * @param data 待签名数据
     * @return 签名
     */
    public static String getWangyiyunSign(final Map<String, Object> data){
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            if(data.get(k) !=null && StringUtils.isNotBlank(data.get(k).toString().trim()) && !StringUtils.equals("null",data.get(k).toString().trim())){ // 参数值为空，则不参与签名
                valueStr.append(k+"="+data.get(k)+"&");
            }
        }
        String md5 = valueStr.deleteCharAt(valueStr.length()-1).toString();
        //log.info("md5:" + md5);
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8));
        return sign;
    }


    /**
     * 生成签名
     * @param data 待签名数据
     * @return 签名
     */
    public static String getJiangXiVrbtSigns(final Map<String, Object> data,String key){
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            if(data.get(k) !=null && StringUtils.isNotBlank(data.get(k).toString().trim()) && !StringUtils.equals("null",data.get(k).toString().trim())){ // 参数值为空，则不参与签名
                valueStr.append(k+"="+data.get(k)+"&");
            }
        }
        String md5 =valueStr.toString().substring(0, valueStr.length() -1)+key;
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return sign;
    }

    /**
     * 生成签名
     * @param dataNode
     * @param keys
     * @return
     */
    public static String getDianxinCloudGameSign(final ObjectNode dataNode,String keys){
        Iterator<Map.Entry<String, JsonNode>> jsonNode = dataNode.fields();
        StringBuffer valueStr = new StringBuffer();
        while (jsonNode.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNode.next();
            String value =  entry.getValue().asText();
            String key =   entry.getKey();

            if(value !=null && StringUtils.isNotBlank(value.trim()) && !StringUtils.equals("null",value.trim())) { // 参数值为空，则不参与签名
                valueStr.append(key+"="+value+"&");
            }
        }
        String md5 =valueStr.toString()+keys;
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        return sign;
    }
    /**
     * 生成签名
     * @param data 待签名数据
     * @return 签名
     */
    public static String getFeeSign(final Map<String, Object> data,String key){
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            if(data.get(k) !=null && StringUtils.isNotBlank(data.get(k).toString().trim()) && !StringUtils.equals("null",data.get(k).toString().trim())){ // 参数值为空，则不参与签名
                valueStr.append(k+"="+data.get(k)+"&");
            }
        }
        String md5 =valueStr.toString()+"szKey="+key;
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        return sign;
    }


    /**
     * 生成签名
     * @param data 待签名数据
     * @return 签名
     */
    public static String getYiZunSignSHA(final Map<String, Object> data,String key){
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder valueStr = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(FIELD_SIGN)) {
                continue;
            }
            if(data.get(k) !=null && StringUtils.isNotBlank(data.get(k).toString().trim()) && !StringUtils.equals("null",data.get(k).toString().trim())){ // 参数值为空，则不参与签名
                valueStr.append(k+data.get(k));
            }
        }
        String shaStr=key+valueStr.toString()+key;
        return sha1Hex(shaStr);
    }

    public static String sha1Hex(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(messageDigest);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase();
    }

}
