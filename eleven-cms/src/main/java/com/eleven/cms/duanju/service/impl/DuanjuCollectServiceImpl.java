package com.eleven.cms.duanju.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.duanju.entity.DuanjuCollect;
import com.eleven.cms.duanju.entity.DuanjuUser;
import com.eleven.cms.duanju.mapper.DuanjuCollectMapper;
import com.eleven.cms.duanju.service.IDuanjuCollectService;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

/**
 * @Description: 咪咕互娱短剧收藏记录
 * @Author: jeecg-boot
 * @Date:   2024-08-13
 * @Version: V1.0
 */
@Service
public class DuanjuCollectServiceImpl extends ServiceImpl<DuanjuCollectMapper, DuanjuCollect> implements IDuanjuCollectService {
    @Override
    public Result queryUserCollect(String mobile, String dramaId) {
        boolean isCollect=this.lambdaQuery().eq(DuanjuCollect::getMobile,mobile).eq(DuanjuCollect::getDramaId,dramaId).count()>0;
        if(isCollect){
            return Result.ok("已收藏！");
        }
        return Result.error("未收藏！");
    }
    @Override
    public Result userCollect(String mobile, String dramaId) {
        boolean isCollect=this.lambdaQuery().eq(DuanjuCollect::getMobile,mobile).eq(DuanjuCollect::getDramaId,dramaId).count()>0;
        if(isCollect){
            this.remove(new QueryWrapper<DuanjuCollect>().lambda().eq(DuanjuCollect::getMobile,mobile).eq(DuanjuCollect::getDramaId,dramaId));
            return Result.ok("取消收藏成功！");
        }
        DuanjuCollect duanjuCollect=new DuanjuCollect();
        /**手机号*/
        duanjuCollect.setMobile(mobile);
        /**剧集ID*/
        duanjuCollect.setDramaId(dramaId);
        this.save(duanjuCollect);
        return Result.ok("收藏成功！");
    }

    @Override
    public Result queryUserCollectList(String mobile) {
        return Result.ok(this.baseMapper.findByMobile(mobile));
    }


    /**
     * 根据主键ID和版本号更新用户收藏
     * @param duanjuCollect
     * @return
     */
    @Override
    public void editDuanjuCollect(DuanjuCollect duanjuCollect) {
        this.baseMapper.updateDuanjuCollect(duanjuCollect);
    }
}
