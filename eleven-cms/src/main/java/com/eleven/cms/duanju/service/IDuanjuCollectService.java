package com.eleven.cms.duanju.service;

import com.eleven.cms.duanju.entity.DuanjuCollect;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 咪咕互娱短剧收藏记录
 * @Author: jeecg-boot
 * @Date:   2024-08-13
 * @Version: V1.0
 */
public interface IDuanjuCollectService extends IService<DuanjuCollect> {

    Result queryUserCollect(String mobile, String dramaId);

    Result userCollect(String mobile, String dramaId);

    Result queryUserCollectList(String mobile);

    void editDuanjuCollect(DuanjuCollect duanjuCollect);
}
