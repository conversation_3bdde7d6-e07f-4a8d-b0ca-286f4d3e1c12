package com.eleven.cms.duanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisode;
import com.eleven.cms.duanju.entity.DuanjuCoupon;
import com.eleven.cms.duanju.entity.DuanjuGoldenBeanOrder;
import com.eleven.cms.duanju.entity.DuanjuUser;
import com.eleven.cms.duanju.mapper.DuanjuCouponMapper;
import com.eleven.cms.duanju.service.ICmsDuanjuEpisodeService;
import com.eleven.cms.duanju.service.IDuanjuCouponService;
import com.eleven.cms.duanju.service.IDuanjuGoldenBeanOrderService;
import com.eleven.cms.duanju.service.IDuanjuUserService;
import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.service.IMemberRightsService;
import com.eleven.cms.util.BizConstant;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * @Description: 咪咕互娱短剧券码兑换记录
 * @Author: jeecg-boot
 * @Date:   2024-07-17
 * @Version: V1.0
 */
@Service
public class DuanjuCouponServiceImpl extends ServiceImpl<DuanjuCouponMapper, DuanjuCoupon> implements IDuanjuCouponService {
    @Autowired
    private IDuanjuUserService duanjuUserService;
    @Autowired
    private IMemberRightsService memberRightsService;
    @Autowired
    ICmsDuanjuEpisodeService cmsDuanjuEpisodeService;
    @Autowired
    private IDuanjuGoldenBeanOrderService duanjuGoldenBeanOrderService;
    @Override
    public Result exchangeCoupon(String mobile,String num, String rightsId) {
        DuanjuUser duanjuUser=duanjuUserService.lambdaQuery().select(DuanjuUser::getTotalGoldenBean,DuanjuUser::getInvalidTime,DuanjuUser::getLevel,DuanjuUser::getVersion,DuanjuUser::getId).eq(DuanjuUser::getMobile,mobile).orderByDesc(DuanjuUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duanjuUser==null){
            return Result.needLogin("请登录");
        }
        if(duanjuUser.getLevel().intValue()<=0){
            return Result.error(CommonConstant.SC_JEECG_CMCC_MIGU_PAY,"未包月");
        }
        //获取过期时间
        LocalDateTime invalidTime=duanjuUser.getInvalidTime()!=null?duanjuUser.getInvalidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime():duanjuUser.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        if(invalidTime.isBefore(LocalDateTime.now())){
            return Result.error(CommonConstant.SC_JEECG_CMCC_MIGU_PAY,"未包月");
        }


        MemberRights memberRights=memberRightsService.lambdaQuery().select(MemberRights::getRemark,MemberRights::getRightsName,MemberRights::getOriginalPrice).eq(MemberRights::getRightsId,rightsId).orderByDesc(MemberRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(memberRights==null || duanjuUser.getTotalGoldenBean().intValue()<=0 || Integer.valueOf(memberRights.getRemark()).intValue()<=0){
            return Result.error("非法请求");
        }
        int goldenBean=Integer.valueOf(memberRights.getRemark()).intValue()*Integer.valueOf(num).intValue();
        if(duanjuUser.getTotalGoldenBean().intValue()<goldenBean){
            return Result.error("金豆不足");
        }
        for(int i=0;i<Integer.valueOf(num);i++){
            String sid = RandomStringUtils.randomAlphanumeric(10);
            DuanjuCoupon duanjuCoupon=new DuanjuCoupon();
            duanjuCoupon.setMobile(mobile);
            duanjuCoupon.setCouponCode(sid);
            /**订单号*/
            duanjuCoupon.setOrderId(sid);
            //优惠券等级
            duanjuCoupon.setCouponLevel(memberRights.getOriginalPrice());
            duanjuCoupon.setCouponName(memberRights.getRightsName());
            /**券码类型:0=初始,1=包月,2=续订,3=金豆兑换*/
            duanjuCoupon.setCouponType(3);
            this.save(duanjuCoupon);

            //创建金豆消费记录
            DuanjuGoldenBeanOrder duanjuGoldenBeanOrder=new DuanjuGoldenBeanOrder();
            /**订单号*/
            duanjuGoldenBeanOrder.setOrderNo(sid);
            /**手机号*/
            duanjuGoldenBeanOrder.setMobile(mobile);
            /**订单金豆*/
            duanjuGoldenBeanOrder.setOrderGoldenBean(Integer.valueOf(memberRights.getRemark()));
            /**订单状态:0=发放,1=兑换*/
            duanjuGoldenBeanOrder.setOrderStatus(1);
            /**兑换码*/
            duanjuGoldenBeanOrder.setCouponCode(sid);
            duanjuGoldenBeanOrderService.save(duanjuGoldenBeanOrder);

        }
        /**总金豆*/
        Integer totalGoldenBean=Integer.valueOf(duanjuUser.getTotalGoldenBean().intValue()-goldenBean);
        duanjuUser.setTotalGoldenBean(totalGoldenBean);
        duanjuUserService.editDuanjuUser(duanjuUser);
        return Result.ok("兑换成功");
    }
    @Override
    public List<DuanjuCoupon> queryDuanjuCouponListByMobile(String mobile) {
       return this.baseMapper.findByMobile(mobile);
    }



    @Override
    public Result queryCouponList() {
        List<MemberRights> memberRights=memberRightsService.lambdaQuery().select(MemberRights::getRightsName,MemberRights::getRightsId,MemberRights::getRemark).eq(MemberRights::getCompanyOwner,"HUYU").ne(MemberRights::getRemark,"0").list();
        if(memberRights==null){
            return Result.error("系统错误");
        }
        return Result.ok(memberRights);
    }

    /**
     * 使用优惠券兑换剧集
     * @param mobile
     * @param dramaId
     * @return
     */
    @Override
    public Result useCoupon(String mobile,String dramaId){
        DuanjuUser duanjuUser=duanjuUserService.lambdaQuery().select(DuanjuUser::getTotalGoldenBean,DuanjuUser::getInvalidTime,DuanjuUser::getLevel,DuanjuUser::getId).eq(DuanjuUser::getMobile,mobile).orderByDesc(DuanjuUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duanjuUser==null){
            return Result.needLogin("请登录");
        }
        if(duanjuUser.getLevel().intValue()<=0){
            return Result.error(CommonConstant.SC_JEECG_CMCC_MIGU_PAY,"未包月");
        }
        //获取过期时间
        LocalDateTime invalidTime=duanjuUser.getInvalidTime()!=null?duanjuUser.getInvalidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime():duanjuUser.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        if(invalidTime.isBefore(LocalDateTime.now())){
            return Result.error(CommonConstant.SC_JEECG_CMCC_MIGU_PAY,"未包月");
        }

        CmsDuanjuEpisode cmsDuanjuEpisode=cmsDuanjuEpisodeService.lambdaQuery().select(CmsDuanjuEpisode::getType).eq(CmsDuanjuEpisode::getId,dramaId).orderByDesc(CmsDuanjuEpisode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(cmsDuanjuEpisode==null){
            return Result.error(CommonConstant.SC_JEECG_CMCC_REDIRECT,"剧集已删除");
        }
        //当前用户等级大于或者等于剧集等级 3级以上必须兑换优惠券才能观看
        if(cmsDuanjuEpisode.getType().intValue()<3 && duanjuUser.getLevel().intValue()>=cmsDuanjuEpisode.getType().intValue()){
            return Result.error(CommonConstant.SC_JEECG_BIZ_CONFIRM,"等级已满足");
        }
        //判断是否已经兑换
        boolean isPay = this.lambdaQuery().eq(DuanjuCoupon::getMobile, mobile).eq(DuanjuCoupon::getDramaId, dramaId).count()>0;
        if (isPay){
            return Result.error(CommonConstant.SC_JEECG_MIGU_TOKEN,"您已购买，请勿重复购买！");
        }
        //查询是否有可以用使用的优惠券
        DuanjuCoupon duanjuCoupon = this.lambdaQuery().select(DuanjuCoupon::getId,DuanjuCoupon::getVersion).eq(DuanjuCoupon::getMobile, mobile).eq(DuanjuCoupon::getCouponLevel, cmsDuanjuEpisode.getType()).eq(DuanjuCoupon::getStatus, 0).orderByDesc(DuanjuCoupon::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if (duanjuCoupon==null){
            return Result.error(CommonConstant.SC_JEECG_UNICOM_REDIRECT,"暂无可以使用的券码！");
        }
        //扣除券，保存购买记录
        duanjuCoupon.setStatus(1);
        duanjuCoupon.setDramaId(dramaId);
        this.baseMapper.updateDuanjuCoupon(duanjuCoupon);
        return Result.ok("兑换成功");
    }
}
