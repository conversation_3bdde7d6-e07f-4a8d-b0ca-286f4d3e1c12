package com.eleven.cms.duanju.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.duanju.entity.DuanjuLookHistory;
import com.eleven.cms.duanju.service.IDuanjuLookHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 咪咕互娱短剧观看记录
 * @Author: jeecg-boot
 * @Date:   2024-08-13
 * @Version: V1.0
 */
@Api(tags="咪咕互娱短剧观看记录")
@RestController
@RequestMapping("/cms/duanjuLookHistory")
@Slf4j
public class DuanjuLookHistoryController extends JeecgController<DuanjuLookHistory, IDuanjuLookHistoryService> {
	@Autowired
	private IDuanjuLookHistoryService duanjuLookHistoryService;

	/**
	 * 分页列表查询
	 *
	 * @param duanjuLookHistory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧观看记录-分页列表查询")
	@ApiOperation(value="咪咕互娱短剧观看记录-分页列表查询", notes="咪咕互娱短剧观看记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DuanjuLookHistory duanjuLookHistory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DuanjuLookHistory> queryWrapper = QueryGenerator.initQueryWrapper(duanjuLookHistory, req.getParameterMap());
		Page<DuanjuLookHistory> page = new Page<DuanjuLookHistory>(pageNo, pageSize);
		IPage<DuanjuLookHistory> pageList = duanjuLookHistoryService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param duanjuLookHistory
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧观看记录-添加")
	@ApiOperation(value="咪咕互娱短剧观看记录-添加", notes="咪咕互娱短剧观看记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DuanjuLookHistory duanjuLookHistory) {
		duanjuLookHistoryService.save(duanjuLookHistory);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param duanjuLookHistory
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧观看记录-编辑")
	@ApiOperation(value="咪咕互娱短剧观看记录-编辑", notes="咪咕互娱短剧观看记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DuanjuLookHistory duanjuLookHistory) {
		duanjuLookHistoryService.updateById(duanjuLookHistory);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧观看记录-通过id删除")
	@ApiOperation(value="咪咕互娱短剧观看记录-通过id删除", notes="咪咕互娱短剧观看记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		duanjuLookHistoryService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧观看记录-批量删除")
	@ApiOperation(value="咪咕互娱短剧观看记录-批量删除", notes="咪咕互娱短剧观看记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.duanjuLookHistoryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧观看记录-通过id查询")
	@ApiOperation(value="咪咕互娱短剧观看记录-通过id查询", notes="咪咕互娱短剧观看记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DuanjuLookHistory duanjuLookHistory = duanjuLookHistoryService.getById(id);
		if(duanjuLookHistory==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(duanjuLookHistory);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param duanjuLookHistory
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DuanjuLookHistory duanjuLookHistory) {
        return super.exportXls(request, duanjuLookHistory, DuanjuLookHistory.class, "咪咕互娱短剧观看记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DuanjuLookHistory.class);
    }

}
