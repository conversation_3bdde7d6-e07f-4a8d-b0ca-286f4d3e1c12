package com.eleven.cms.duanju.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_duanju_episode_info
 * @Author: jeecg-boot
 * @Date: 2024-07-17
 * @Version: V1.0
 */
@ApiModel(value = "cms_duanju_episode对象", description = "cms_duanju_episode")
@Data
@TableName("cms_duanju_episode_info")
public class CmsDuanjuEpisodeInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 剧集id
     */
    @ApiModelProperty(value = "剧集id")
    private String episodeId;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 当前集数
     */
    @Excel(name = "当前集数", width = 15)
    @ApiModelProperty(value = "当前集数")
    private Integer currentNumber;
    /**
     * 视频地址
     */
    @Excel(name = "视频地址", width = 15)
    @ApiModelProperty(value = "视频地址")
    private String videoPath;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonIgnore
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
