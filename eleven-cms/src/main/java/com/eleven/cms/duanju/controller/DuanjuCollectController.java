package com.eleven.cms.duanju.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.duanju.entity.DuanjuCollect;
import com.eleven.cms.duanju.service.IDuanjuCollectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 咪咕互娱短剧收藏记录
 * @Author: jeecg-boot
 * @Date:   2024-08-13
 * @Version: V1.0
 */
@Api(tags="咪咕互娱短剧收藏记录")
@RestController
@RequestMapping("/cms/duanjuCollect")
@Slf4j
public class DuanjuCollectController extends JeecgController<DuanjuCollect, IDuanjuCollectService> {
	@Autowired
	private IDuanjuCollectService duanjuCollectService;

	/**
	 * 分页列表查询
	 *
	 * @param duanjuCollect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧收藏记录-分页列表查询")
	@ApiOperation(value="咪咕互娱短剧收藏记录-分页列表查询", notes="咪咕互娱短剧收藏记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DuanjuCollect duanjuCollect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DuanjuCollect> queryWrapper = QueryGenerator.initQueryWrapper(duanjuCollect, req.getParameterMap());
		Page<DuanjuCollect> page = new Page<DuanjuCollect>(pageNo, pageSize);
		IPage<DuanjuCollect> pageList = duanjuCollectService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param duanjuCollect
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧收藏记录-添加")
	@ApiOperation(value="咪咕互娱短剧收藏记录-添加", notes="咪咕互娱短剧收藏记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DuanjuCollect duanjuCollect) {
		duanjuCollectService.save(duanjuCollect);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param duanjuCollect
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧收藏记录-编辑")
	@ApiOperation(value="咪咕互娱短剧收藏记录-编辑", notes="咪咕互娱短剧收藏记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DuanjuCollect duanjuCollect) {
		duanjuCollectService.updateById(duanjuCollect);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧收藏记录-通过id删除")
	@ApiOperation(value="咪咕互娱短剧收藏记录-通过id删除", notes="咪咕互娱短剧收藏记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		duanjuCollectService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧收藏记录-批量删除")
	@ApiOperation(value="咪咕互娱短剧收藏记录-批量删除", notes="咪咕互娱短剧收藏记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.duanjuCollectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "咪咕互娱短剧收藏记录-通过id查询")
	@ApiOperation(value="咪咕互娱短剧收藏记录-通过id查询", notes="咪咕互娱短剧收藏记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DuanjuCollect duanjuCollect = duanjuCollectService.getById(id);
		if(duanjuCollect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(duanjuCollect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param duanjuCollect
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DuanjuCollect duanjuCollect) {
        return super.exportXls(request, duanjuCollect, DuanjuCollect.class, "咪咕互娱短剧收藏记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DuanjuCollect.class);
    }

}
