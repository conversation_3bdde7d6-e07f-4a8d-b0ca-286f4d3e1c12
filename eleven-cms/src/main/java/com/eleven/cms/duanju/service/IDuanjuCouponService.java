package com.eleven.cms.duanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.duanju.entity.DuanjuCoupon;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * @Description: 咪咕互娱短剧券码兑换记录
 * @Author: jeecg-boot
 * @Date:   2024-07-17
 * @Version: V1.0
 */
public interface IDuanjuCouponService extends IService<DuanjuCoupon> {

    Result exchangeCoupon(String mobile,String num, String rightsId);

    List<DuanjuCoupon> queryDuanjuCouponListByMobile(String mobile);

    Result queryCouponList();

    Result useCoupon(String mobile,String dramaId);
}
