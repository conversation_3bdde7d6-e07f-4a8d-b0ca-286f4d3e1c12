package com.eleven.cms.duanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.config.OtherRecharge;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisode;
import com.eleven.cms.duanju.entity.DuanjuCoupon;
import com.eleven.cms.duanju.entity.DuanjuGoldenBeanOrder;
import com.eleven.cms.duanju.entity.DuanjuUser;
import com.eleven.cms.duanju.mapper.DuanjuUserMapper;
import com.eleven.cms.duanju.service.ICmsDuanjuEpisodeService;
import com.eleven.cms.duanju.service.IDuanjuCouponService;
import com.eleven.cms.duanju.service.IDuanjuGoldenBeanOrderService;
import com.eleven.cms.duanju.service.IDuanjuUserService;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.IMemberRightsService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 咪咕互娱短剧用户
 * @Author: jeecg-boot
 * @Date:   2024-07-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class DuanjuUserServiceImpl extends ServiceImpl<DuanjuUserMapper, DuanjuUser> implements IDuanjuUserService {
    @Autowired
    ICmsDuanjuEpisodeService cmsDuanjuEpisodeService;
    @Autowired
    private IDuanjuCouponService duanjuCouponService;
    @Autowired
    private IMemberRightsService memberRightsService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    private JunboApiProperties junboApiProperties;

    @Autowired
    private IDuanjuGoldenBeanOrderService duanjuGoldenBeanOrderService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    /**
     * 根据主键ID和版本号更新用户数据
     * @param duanjuUser
     * @return
     */
    @Override
    public void editDuanjuUser(DuanjuUser duanjuUser) {
        this.baseMapper.updateDuanjuUser(duanjuUser);
    }

    @Override
    public DuanjuUser login(String mobile){
        DuanjuUser duanjuUser=this.lambdaQuery().select(DuanjuUser::getLevel,DuanjuUser::getInvalidTime,DuanjuUser::getTotalGoldenBean,DuanjuUser::getLoginTime,DuanjuUser::getVersion,DuanjuUser::getId).eq(DuanjuUser::getMobile,mobile).orderByDesc(DuanjuUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duanjuUser==null){
            DuanjuUser user=new DuanjuUser();
            /**手机号*/
            user.setMobile(mobile);
            user.setLevel(-1);
            user.setTotalGoldenBean(0);
            this.save(user);
            return user;
        }else {
            //会员用户登录短剧平台
            if(duanjuUser.getLevel().intValue()>0){

                //查询本月没有发送领取结果通知的订单。
                DuanjuGoldenBeanOrder duanjuGoldenBeanOrder=duanjuGoldenBeanOrderService.lambdaQuery().select(DuanjuGoldenBeanOrder::getOrderNo,DuanjuGoldenBeanOrder::getExtrInfo,DuanjuGoldenBeanOrder::getVersion,DuanjuGoldenBeanOrder::getId).eq(DuanjuGoldenBeanOrder::getMobile,mobile).eq(DuanjuGoldenBeanOrder::getOrderStatus,0).eq(DuanjuGoldenBeanOrder::getIsNotify,0).in(DuanjuGoldenBeanOrder::getGoldenBeanType,1,2).ge(DuanjuGoldenBeanOrder::getCreateTime,DateUtil.formatSplitTime(DateUtil.getFirstDayOfMonthWithMinTime())).orderByDesc(DuanjuGoldenBeanOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(duanjuGoldenBeanOrder!=null){
                    junboChargeLogService.lambdaUpdate().eq(JunboChargeLog::getMiguOrderId,duanjuGoldenBeanOrder.getOrderNo()).set(JunboChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS).set(JunboChargeLog::getCallbackMsg,"直充成功") .set(JunboChargeLog::getUpdateTime,new Date()).update();

                    duanjuGoldenBeanOrderService.updateNotifyById(duanjuGoldenBeanOrder);
                    String orderId=duanjuGoldenBeanOrder.getOrderNo();
                    String extrInfo=duanjuGoldenBeanOrder.getExtrInfo();

                    ObjectNode dataNode =mapper.createObjectNode();
                    String timestamp= String.valueOf(System.currentTimeMillis());
                    final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("duanju");
                    String sign= null;
                    try {
                        sign = DigestUtils.md5DigestAsHex((orderId+timestamp+otherRecharge.getKey()).getBytes(StandardCharsets.UTF_8.name()));
                    } catch (UnsupportedEncodingException e) {
                        log.info("会员用户登录短剧平台-通知咪咕互娱道具使用状态生成签名异常:", e);
                    }
                    dataNode.put("orderId",orderId);
                    dataNode.put("receiveTime",timestamp);
                    dataNode.put("timestamp",timestamp);
                    dataNode.put("extrInfo",extrInfo);
//                    dataNode.put("sendServer","");
//                    dataNode.put("sendRole","");
                    String url=otherRecharge.getReceivebackUrl();
                    log.info("会员用户首次登录短剧平台-通知咪咕互娱道具使用状态,请求数据-地址:{},请求参数:{}",url,dataNode);
                    RequestBody body = RequestBody.create(JSON, dataNode.toString());
                    Request request = new Request.Builder().url(url).post(body).addHeader("sign",sign).build();
                    try (Response response =client.newCall(request).execute()){
                        if (!response.isSuccessful()) {
                            throw new IOException("Unexpected code " + response);
                        }
                        String content = response.body().string();
                        log.info("会员用户登录短剧平台-通知咪咕互娱道具使用状态,响应数据=>地址:{},请求参数:{},响应参数:{}",url,dataNode,content);
                    } catch (IOException e) {
                        log.error("会员用户登录短剧平台-通知咪咕互娱道具使用状态,请求异常=>地址:{},请求参数:{}",url,dataNode,e);
                        return null;
                    }

                }
            }
        }
        return duanjuUser;
    }

    @Override
    public Result episodeAuth(String mobile, String dramaId) {
        DuanjuUser duanjuUser=this.lambdaQuery().select(DuanjuUser::getLevel,DuanjuUser::getCreateTime,DuanjuUser::getInvalidTime,DuanjuUser::getTotalGoldenBean).eq(DuanjuUser::getMobile,mobile).orderByDesc(DuanjuUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duanjuUser==null){
            return Result.needLogin("请登录");
        }
        if(duanjuUser.getLevel().intValue()<=0){
            return Result.error(CommonConstant.SC_JEECG_CMCC_MIGU_PAY,"未包月");
        }
        //获取过期时间
        LocalDateTime invalidTime=duanjuUser.getInvalidTime()!=null?duanjuUser.getInvalidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime():duanjuUser.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        if(invalidTime.isBefore(LocalDateTime.now())){
            return Result.error(CommonConstant.SC_JEECG_CMCC_MIGU_PAY,"未包月");
        }
        //查询是否已经兑换剧集
        boolean isExchange = duanjuCouponService.lambdaQuery().eq(DuanjuCoupon::getMobile, mobile).eq(DuanjuCoupon::getDramaId, dramaId).eq(DuanjuCoupon::getStatus, 1).count()>0;
        if(isExchange){
            return Result.error(CommonConstant.SC_JEECG_BIZ_EXISTS,"优惠券已兑换");
        }
        CmsDuanjuEpisode cmsDuanjuEpisode=cmsDuanjuEpisodeService.lambdaQuery().select(CmsDuanjuEpisode::getType).eq(CmsDuanjuEpisode::getId,dramaId).orderByDesc(CmsDuanjuEpisode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(cmsDuanjuEpisode==null){
            return Result.error(CommonConstant.SC_JEECG_CMCC_REDIRECT,"剧集已删除");
        }
        //剧集等级大于或者等于当前用户等级 3级以上必须兑换优惠券才能观看 并且等级没有过期
        if(cmsDuanjuEpisode.getType().intValue()<3 && duanjuUser.getLevel().intValue()>=cmsDuanjuEpisode.getType().intValue() && invalidTime.isAfter(LocalDateTime.now())){
            return Result.error(CommonConstant.SC_JEECG_BIZ_CONFIRM,"等级已满足");
        }

        //判断是否可以使用优惠券
        boolean isCoupon=duanjuCouponService.lambdaQuery().eq(DuanjuCoupon::getMobile, mobile).eq(DuanjuCoupon::getStatus, 0).le(DuanjuCoupon::getCouponLevel, cmsDuanjuEpisode.getType()).count()>0;
        if(isCoupon){
            return Result.error(CommonConstant.SC_JEECG_BIZ_REDIRECT,"请使用优惠券兑换");
        }
        MemberRights memberRights=memberRightsService.lambdaQuery().select(MemberRights::getRemark).eq(MemberRights::getOriginalPrice,cmsDuanjuEpisode.getType()).eq(MemberRights::getCompanyOwner,"HUYU").orderByDesc(MemberRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        //判断金豆是否可以兑换
        if(memberRights!=null && duanjuUser.getTotalGoldenBean().intValue()>0 && Integer.valueOf(memberRights.getRemark()).intValue()>0 && duanjuUser.getTotalGoldenBean().intValue()>=Integer.valueOf(memberRights.getRemark()).intValue() ){
            return Result.error(CommonConstant.TIANYI_CONFIRM,"请使用金豆兑换优惠券");
        }
        return Result.error(CommonConstant.SC_JEECG_SCMCC_REDIRECT,"暂时无法观看");
    }



    @Override
    public Result queryWealth(String mobile){
        DuanjuUser duanjuUser=this.lambdaQuery().select(DuanjuUser::getLevel,DuanjuUser::getInvalidTime,DuanjuUser::getTotalGoldenBean).eq(DuanjuUser::getMobile,mobile).orderByDesc(DuanjuUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duanjuUser==null){
            return Result.needLogin("请登录");
        }
        List<DuanjuCoupon> duanjuCouponList=duanjuCouponService.queryDuanjuCouponListByMobile(mobile);
        Result result= Result.ok(duanjuCouponList);
        result.setLevel(duanjuUser.getLevel());
        result.setGoldenBean(duanjuUser.getTotalGoldenBean());
        if(duanjuUser.getInvalidTime()!=null){
            result.setInvalidTime(DateUtil.getDateFormat(duanjuUser.getInvalidTime(),DateUtil.FULL_TIME_SPLIT_PATTERN));
        }
        return result;
    }
}
