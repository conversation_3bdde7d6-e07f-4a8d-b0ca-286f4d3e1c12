package com.eleven.cms.duanju.service;

import com.eleven.cms.duanju.entity.CmsDuanjuEpisodeInfo;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisode;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: cms_duanju_episode
 * @Author: jeecg-boot
 * @Date: 2024-07-17
 * @Version: V1.0
 */
public interface ICmsDuanjuEpisodeService extends IService<CmsDuanjuEpisode> {

    /**
     * 添加一对多
     */
    public void saveMain(CmsDuanjuEpisode cmsDuanjuEpisode, List<CmsDuanjuEpisodeInfo> cmsDuanjuEpisodeInfoList);

    /**
     * 修改一对多
     */
    public void updateMain(CmsDuanjuEpisode cmsDuanjuEpisode, List<CmsDuanjuEpisodeInfo> cmsDuanjuEpisodeInfoList);

    /**
     * 删除一对多
     */
    public void delMain(String id);

    /**
     * 批量删除一对多
     */
    public void delBatchMain(Collection<? extends Serializable> idList);

}
