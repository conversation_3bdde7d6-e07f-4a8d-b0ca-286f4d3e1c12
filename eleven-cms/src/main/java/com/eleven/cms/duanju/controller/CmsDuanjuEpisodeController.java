package com.eleven.cms.duanju.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.util.HttpUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisodeInfo;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisode;
import com.eleven.cms.duanju.vo.CmsDuanjuEpisodePage;
import com.eleven.cms.duanju.service.ICmsDuanjuEpisodeService;
import com.eleven.cms.duanju.service.ICmsDuanjuEpisodeInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: cms_duanju_episode
 * @Author: jeecg-boot
 * @Date: 2024-07-17
 * @Version: V1.0
 */
@Api(tags = "cms_duanju_episode")
@RestController
@RequestMapping("/cms/cmsDuanjuEpisode")
@Slf4j
public class CmsDuanjuEpisodeController {
    @Autowired
    private ICmsDuanjuEpisodeService cmsDuanjuEpisodeService;
    @Autowired
    private ICmsDuanjuEpisodeInfoService cmsDuanjuEpisodeInfoService;

    /**
     * 分页列表查询
     *
     * @param cmsDuanjuEpisode
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "cms_duanju_episode-分页列表查询")
    @ApiOperation(value = "cms_duanju_episode-分页列表查询", notes = "cms_duanju_episode-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(CmsDuanjuEpisode cmsDuanjuEpisode,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<CmsDuanjuEpisode> queryWrapper = QueryGenerator.initQueryWrapper(cmsDuanjuEpisode, req.getParameterMap());
        Page<CmsDuanjuEpisode> page = new Page<CmsDuanjuEpisode>(pageNo, pageSize);
        IPage<CmsDuanjuEpisode> pageList = cmsDuanjuEpisodeService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param cmsDuanjuEpisodePage
     * @return
     */
    //@AutoLog(value = "cms_duanju_episode-添加")
    @ApiOperation(value = "cms_duanju_episode-添加", notes = "cms_duanju_episode-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CmsDuanjuEpisodePage cmsDuanjuEpisodePage) {
        CmsDuanjuEpisode cmsDuanjuEpisode = new CmsDuanjuEpisode();
        BeanUtils.copyProperties(cmsDuanjuEpisodePage, cmsDuanjuEpisode);
        cmsDuanjuEpisodeService.saveMain(cmsDuanjuEpisode, cmsDuanjuEpisodePage.getCmsDuanjuEpisodeInfoList());
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param cmsDuanjuEpisodePage
     * @return
     */
    //@AutoLog(value = "cms_duanju_episode-编辑")
    @ApiOperation(value = "cms_duanju_episode-编辑", notes = "cms_duanju_episode-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody CmsDuanjuEpisodePage cmsDuanjuEpisodePage) {
        CmsDuanjuEpisode cmsDuanjuEpisode = new CmsDuanjuEpisode();
        BeanUtils.copyProperties(cmsDuanjuEpisodePage, cmsDuanjuEpisode);
        CmsDuanjuEpisode cmsDuanjuEpisodeEntity = cmsDuanjuEpisodeService.getById(cmsDuanjuEpisode.getId());
        if (cmsDuanjuEpisodeEntity == null) {
            return Result.error("未找到对应数据");
        }
        cmsDuanjuEpisodeService.updateMain(cmsDuanjuEpisode, cmsDuanjuEpisodePage.getCmsDuanjuEpisodeInfoList());
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_duanju_episode-通过id删除")
    @ApiOperation(value = "cms_duanju_episode-通过id删除", notes = "cms_duanju_episode-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        cmsDuanjuEpisodeService.delMain(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "cms_duanju_episode-批量删除")
    @ApiOperation(value = "cms_duanju_episode-批量删除", notes = "cms_duanju_episode-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.cmsDuanjuEpisodeService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_duanju_episode-通过id查询")
    @ApiOperation(value = "cms_duanju_episode-通过id查询", notes = "cms_duanju_episode-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        CmsDuanjuEpisode cmsDuanjuEpisode = cmsDuanjuEpisodeService.getById(id);
        if (cmsDuanjuEpisode == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(cmsDuanjuEpisode);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "cms_duanju_episode_info通过主表ID查询")
    @ApiOperation(value = "cms_duanju_episode_info主表ID查询", notes = "cms_duanju_episode_info-通主表ID查询")
    @GetMapping(value = "/queryCmsDuanjuEpisodeInfoByMainId")
    public Result<?> queryCmsDuanjuEpisodeInfoListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<CmsDuanjuEpisodeInfo> cmsDuanjuEpisodeInfoList = cmsDuanjuEpisodeInfoService.selectByMainId(id);
        return Result.ok(cmsDuanjuEpisodeInfoList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param cmsDuanjuEpisode
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmsDuanjuEpisode cmsDuanjuEpisode) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<CmsDuanjuEpisode> queryWrapper = QueryGenerator.initQueryWrapper(cmsDuanjuEpisode, request.getParameterMap());
        LoginUser sysUser = HttpUtil.getCurrUser();

        //Step.2 获取导出数据
        List<CmsDuanjuEpisode> queryList = cmsDuanjuEpisodeService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<CmsDuanjuEpisode> cmsDuanjuEpisodeList = new ArrayList<CmsDuanjuEpisode>();
        if (oConvertUtils.isEmpty(selections)) {
            cmsDuanjuEpisodeList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            cmsDuanjuEpisodeList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }

        // Step.3 组装pageList
        List<CmsDuanjuEpisodePage> pageList = new ArrayList<CmsDuanjuEpisodePage>();
        for (CmsDuanjuEpisode main : cmsDuanjuEpisodeList) {
            CmsDuanjuEpisodePage vo = new CmsDuanjuEpisodePage();
            BeanUtils.copyProperties(main, vo);
            List<CmsDuanjuEpisodeInfo> cmsDuanjuEpisodeInfoList = cmsDuanjuEpisodeInfoService.selectByMainId(main.getId());
            vo.setCmsDuanjuEpisodeInfoList(cmsDuanjuEpisodeInfoList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "cms_duanju_episode列表");
        mv.addObject(NormalExcelConstants.CLASS, CmsDuanjuEpisodePage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("cms_duanju_episode数据", "导出人:" + sysUser.getRealname(), "cms_duanju_episode"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<CmsDuanjuEpisodePage> list = ExcelImportUtil.importExcel(file.getInputStream(), CmsDuanjuEpisodePage.class, params);
                for (CmsDuanjuEpisodePage page : list) {
                    CmsDuanjuEpisode po = new CmsDuanjuEpisode();
                    BeanUtils.copyProperties(page, po);
                    cmsDuanjuEpisodeService.saveMain(po, page.getCmsDuanjuEpisodeInfoList());
                }
                return Result.ok("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

}
