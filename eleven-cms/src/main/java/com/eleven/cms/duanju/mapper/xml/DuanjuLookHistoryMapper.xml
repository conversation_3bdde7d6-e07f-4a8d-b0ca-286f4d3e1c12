<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.duanju.mapper.DuanjuLookHistoryMapper">
    <select id="findByMobile" resultType="com.eleven.cms.duanju.entity.DuanjuLookHistory">
        SELECT e.cover as cover,e.title as title,c.drama_id as dramaId,e.number as number,c.new_number as newNumber,c.create_time as createTime,e.synopsis as synopsis
        FROM cms_duanju_look_history c
        LEFT JOIN  cms_duanju_episode e
        ON c.drama_id = e.id
        WHERE c.mobile =#{mobile}
        order by c.create_time desc limit 2
    </select>

    <update id="updateLookHistory" parameterType="com.eleven.cms.duanju.entity.DuanjuLookHistory">
        UPDATE cms_duanju_look_history
        SET `new_number` =#{history.newNumber},
            version = version + 1,
            update_time =now()
        WHERE id = #{history.id} AND version =#{history.version}
    </update>
</mapper>
