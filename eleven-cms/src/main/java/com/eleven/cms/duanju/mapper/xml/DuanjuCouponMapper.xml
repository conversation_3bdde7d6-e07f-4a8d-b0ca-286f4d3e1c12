<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.duanju.mapper.DuanjuCouponMapper">
    <select id="findByMobile" resultType="com.eleven.cms.duanju.entity.DuanjuCoupon">
        SELECT coupon_name as couponName,COUNT(0) as couponCount FROM cms_duanju_coupon
        WHERE mobile = #{mobile} and status=0
        group by coupon_name order by coupon_name
    </select>
    <update id="updateDuanjuCoupon" parameterType="com.eleven.cms.duanju.entity.DuanjuCoupon">
        UPDATE cms_duanju_coupon
        SET
        <if test="coupon.dramaId != null">
            drama_id =#{coupon.dramaId},
        </if>
        <if test="coupon.status != null">
            status =#{coupon.status},
        </if>
        version = version + 1,
        update_time =now()
        WHERE id = #{coupon.id} AND version =#{coupon.version}
    </update>
</mapper>
