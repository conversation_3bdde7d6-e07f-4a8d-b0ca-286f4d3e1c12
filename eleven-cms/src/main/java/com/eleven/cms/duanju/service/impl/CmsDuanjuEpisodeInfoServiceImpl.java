package com.eleven.cms.duanju.service.impl;

import com.eleven.cms.duanju.entity.CmsDuanjuEpisodeInfo;
import com.eleven.cms.duanju.mapper.CmsDuanjuEpisodeInfoMapper;
import com.eleven.cms.duanju.service.ICmsDuanjuEpisodeInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: cms_duanju_episode_info
 * @Author: jeecg-boot
 * @Date: 2024-07-17
 * @Version: V1.0
 */
@Service
public class CmsDuanjuEpisodeInfoServiceImpl extends ServiceImpl<CmsDuanjuEpisodeInfoMapper, CmsDuanjuEpisodeInfo> implements ICmsDuanjuEpisodeInfoService {

    @Autowired
    private CmsDuanjuEpisodeInfoMapper cmsDuanjuEpisodeInfoMapper;

    @Override
    public List<CmsDuanjuEpisodeInfo> selectByMainId(String mainId) {
        return cmsDuanjuEpisodeInfoMapper.selectByMainId(mainId);
    }

    @Override
    public void batchSaveOrUpdate(List<CmsDuanjuEpisodeInfo> list) {
        this.saveOrUpdateBatch(list);
    }
}
