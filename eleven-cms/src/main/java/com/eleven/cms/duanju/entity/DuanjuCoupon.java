package com.eleven.cms.duanju.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 咪咕互娱短剧券码兑换记录
 * @Author: jeecg-boot
 * @Date:   2024-07-17
 * @Version: V1.0
 */
@Data
@TableName("cms_duanju_coupon")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_duanju_coupon对象", description="咪咕互娱短剧券码兑换记录")
public class DuanjuCoupon implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**兑换码*/
	@Excel(name = "兑换码", width = 15)
    @ApiModelProperty(value = "兑换码")
    private String couponCode;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderId;
	/**剧集ID*/
	@Excel(name = "剧集ID", width = 15)
    @ApiModelProperty(value = "剧集ID")
    private String dramaId;
	/**券码名称*/
	@Excel(name = "券码名称", width = 15)
    @ApiModelProperty(value = "券码名称")
    private String couponName;

    /**券码等级:2=精品短剧观影券,3=会员独家观影券*/
    @Excel(name = "券码等级:2=精品短剧观影券,3=会员独家观影券", width = 15)
    @ApiModelProperty(value = "券码等级:2=精品短剧观影券,3=会员独家观影券")
    private Integer couponLevel;

    /**激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中*/
	@Excel(name = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中", width = 15)
    @ApiModelProperty(value = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中")
    private Integer status;
	/**券码类型:0=初始,1=包月,2=续订,3=金豆兑换*/
	@Excel(name = "券码类型:0=初始,1=包月,2=续订,3=金豆兑换", width = 15)
    @ApiModelProperty(value = "券码类型:0=初始,1=包月,2=续订,3=金豆兑换")
    private Integer couponType;
	/**发码时间*/
	@Excel(name = "发码时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "发码时间")
    private Date sendTime;
	/**过期时间*/
	@Excel(name = "过期时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "过期时间")
    private Date invalidTime;
	/**版本号*/
	@Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private Integer version;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;


    @Excel(name = "优惠券数量", width = 15)
    @TableField(exist = false)
    private String couponCount;
}
