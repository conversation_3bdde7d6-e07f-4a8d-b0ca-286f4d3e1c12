<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.duanju.mapper.DuanjuUserMapper">
    <update id="updateDuanjuUser" parameterType="com.eleven.cms.duanju.entity.DuanjuUser">
        UPDATE cms_duanju_user
        SET
        <if test="user.level != null">
            `level` =#{user.level},
        </if>
        <if test="user.totalGoldenBean != null">
            total_golden_bean =#{user.totalGoldenBean},
        </if>
        <if test="user.invalidTime != null">
            invalid_time =#{user.invalidTime},
        </if>
        version = version + 1,
        update_time =now()
        WHERE id = #{user.id} AND version =#{user.version}
    </update>


    <update id="updateTokenDuanjuUser" parameterType="com.eleven.cms.duanju.entity.DuanjuUser">
        UPDATE cms_duanju_user
        SET
        <if test="user.token != null">
            token =#{user.token},
        </if>
        version = version + 1,
        update_time =now()
        WHERE id = #{user.id} AND version =#{user.version}
    </update>


    <update id="updateLoginTime" parameterType="com.eleven.cms.duanju.entity.DuanjuUser">
        UPDATE cms_duanju_user
        SET version = version + 1,
        login_time =now()
        WHERE id = #{user.id} AND version =#{user.version}
    </update>
</mapper>
