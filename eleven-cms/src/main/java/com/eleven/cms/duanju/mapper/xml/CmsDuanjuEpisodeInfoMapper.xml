<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.duanju.mapper.CmsDuanjuEpisodeInfoMapper">

    <delete id="deleteByMainId" parameterType="java.lang.String">
        DELETE
        FROM cms_duanju_episode_info
        WHERE episode_id = #{mainId}    </delete>

    <select id="selectByMainId" parameterType="java.lang.String"
            resultType="com.eleven.cms.duanju.entity.CmsDuanjuEpisodeInfo">
        SELECT *
        FROM cms_duanju_episode_info
        WHERE episode_id = #{mainId}    </select>
</mapper>
