package com.eleven.cms.duanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 咪咕互娱短剧收藏记录
 * @Author: jeecg-boot
 * @Date:   2024-08-13
 * @Version: V1.0
 */
@Data
@TableName("cms_duanju_collect")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_duanju_collect对象", description="咪咕互娱短剧收藏记录")
public class DuanjuCollect implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键ID")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**剧集ID*/
	@Excel(name = "剧集ID", width = 15)
    @ApiModelProperty(value = "剧集ID")
    private String dramaId;
    /**最新集数*/
    @Excel(name = "最新集数", width = 15)
    @ApiModelProperty(value = "最新集数")
    private Integer newNumber;
	/**版本号*/
	@Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private Integer version;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;



    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @TableField(exist = false)
    private String title;
    /**
     * 集数
     */
    @Excel(name = "集数", width = 15)
    @TableField(exist = false)
    private Integer number;
    /**
     * 封面
     */
    @Excel(name = "封面", width = 15)
    @TableField(exist = false)
    private String cover;
    /**
     * 简介
     */
    @Excel(name = "简介", width = 15)
    @TableField(exist = false)
    private String synopsis;
}
