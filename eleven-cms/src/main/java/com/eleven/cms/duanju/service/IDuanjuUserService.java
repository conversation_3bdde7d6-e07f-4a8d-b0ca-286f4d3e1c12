package com.eleven.cms.duanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.duanju.entity.DuanjuUser;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 咪咕互娱短剧用户
 * @Author: jeecg-boot
 * @Date:   2024-07-17
 * @Version: V1.0
 */
public interface IDuanjuUserService extends IService<DuanjuUser> {
    void editDuanjuUser(DuanjuUser duanjuUser);

    DuanjuUser login(String mobile);

    Result episodeAuth(String mobile,String dramaId);

    Result<?> queryWealth(String mobile);
}
