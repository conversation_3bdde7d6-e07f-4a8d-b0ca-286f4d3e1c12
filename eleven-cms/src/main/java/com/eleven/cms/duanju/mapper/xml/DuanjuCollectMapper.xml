<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.duanju.mapper.DuanjuCollectMapper">
    <select id="findByMobile" resultType="com.eleven.cms.duanju.entity.DuanjuCollect">
        SELECT e.cover as cover,e.title as title,e.number as number,e.synopsis as synopsis,c.drama_id as dramaId,c.create_time as createTime,c.new_number as newNumber
        FROM cms_duanju_collect c
        LEFT JOIN  cms_duanju_episode e
        ON c.drama_id = e.id
        WHERE c.mobile =#{mobile}
        order by c.create_time desc
    </select>


    <update id="updateDuanjuCollect" parameterType="com.eleven.cms.duanju.entity.DuanjuCollect">
        UPDATE cms_duanju_collect
        SET `new_number` =#{collect.newNumber},
        version = version + 1,
        update_time =now()
        WHERE id = #{collect.id} AND version =#{collect.version}
    </update>
</mapper>
