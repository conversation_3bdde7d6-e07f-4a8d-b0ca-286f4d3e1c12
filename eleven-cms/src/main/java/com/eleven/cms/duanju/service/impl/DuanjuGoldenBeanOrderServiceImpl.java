package com.eleven.cms.duanju.service.impl;

import com.eleven.cms.duanju.entity.DuanjuGoldenBeanOrder;
import com.eleven.cms.duanju.mapper.DuanjuGoldenBeanOrderMapper;
import com.eleven.cms.duanju.service.IDuanjuGoldenBeanOrderService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 咪咕互娱短剧金豆兑换记录
 * @Author: jeecg-boot
 * @Date:   2024-07-17
 * @Version: V1.0
 */
@Service
public class DuanjuGoldenBeanOrderServiceImpl extends ServiceImpl<DuanjuGoldenBeanOrderMapper, DuanjuGoldenBeanOrder> implements IDuanjuGoldenBeanOrderService {

    @Override
    public void updateNotifyById(DuanjuGoldenBeanOrder duanjuGoldenBeanOrder) {
        this.baseMapper.updateNotify(duanjuGoldenBeanOrder);
    }
}
