package com.eleven.cms.duanju.service.impl;

import com.eleven.cms.duanju.entity.CmsDuanjuEpisode;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisodeInfo;
import com.eleven.cms.duanju.mapper.CmsDuanjuEpisodeInfoMapper;
import com.eleven.cms.duanju.mapper.CmsDuanjuEpisodeMapper;
import com.eleven.cms.duanju.service.ICmsDuanjuEpisodeService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: cms_duanju_episode
 * @Author: jeecg-boot
 * @Date: 2024-07-17
 * @Version: V1.0
 */
@Service
public class CmsDuanjuEpisodeServiceImpl extends ServiceImpl<CmsDuanjuEpisodeMapper, CmsDuanjuEpisode> implements ICmsDuanjuEpisodeService {

    @Autowired
    private CmsDuanjuEpisodeMapper cmsDuanjuEpisodeMapper;
    @Autowired
    private CmsDuanjuEpisodeInfoMapper cmsDuanjuEpisodeInfoMapper;

    @Override
    @Transactional
    public void saveMain(CmsDuanjuEpisode cmsDuanjuEpisode, List<CmsDuanjuEpisodeInfo> cmsDuanjuEpisodeInfoList) {
        cmsDuanjuEpisodeMapper.insert(cmsDuanjuEpisode);
        if (cmsDuanjuEpisodeInfoList != null && cmsDuanjuEpisodeInfoList.size() > 0) {
            for (CmsDuanjuEpisodeInfo entity : cmsDuanjuEpisodeInfoList) {
                //外键设置
                entity.setEpisodeId(cmsDuanjuEpisode.getId());
                cmsDuanjuEpisodeInfoMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateMain(CmsDuanjuEpisode cmsDuanjuEpisode, List<CmsDuanjuEpisodeInfo> cmsDuanjuEpisodeInfoList) {
        cmsDuanjuEpisodeMapper.updateById(cmsDuanjuEpisode);

        //1.先删除子表数据
        cmsDuanjuEpisodeInfoMapper.deleteByMainId(cmsDuanjuEpisode.getId());

        //2.子表数据重新插入
        if (cmsDuanjuEpisodeInfoList != null && cmsDuanjuEpisodeInfoList.size() > 0) {
            for (CmsDuanjuEpisodeInfo entity : cmsDuanjuEpisodeInfoList) {
                //外键设置
                entity.setEpisodeId(cmsDuanjuEpisode.getId());
                cmsDuanjuEpisodeInfoMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void delMain(String id) {
        cmsDuanjuEpisodeInfoMapper.deleteByMainId(id);
        cmsDuanjuEpisodeMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            cmsDuanjuEpisodeInfoMapper.deleteByMainId(id.toString());
            cmsDuanjuEpisodeMapper.deleteById(id);
        }
    }

}
