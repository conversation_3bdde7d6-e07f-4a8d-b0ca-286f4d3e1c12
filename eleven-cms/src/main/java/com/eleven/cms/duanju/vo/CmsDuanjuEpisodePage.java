package com.eleven.cms.duanju.vo;

import java.util.List;

import com.eleven.cms.duanju.entity.CmsDuanjuEpisode;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisodeInfo;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_duanju_episode
 * @Author: jeecg-boot
 * @Date: 2024-07-17
 * @Version: V1.0
 */
@Data
@ApiModel(value = "cms_duanju_episodePage对象", description = "cms_duanju_episode")
public class CmsDuanjuEpisodePage {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String title;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 集数
     */
    @Excel(name = "集数", width = 15)
    @ApiModelProperty(value = "集数")
    private Integer number;
    /**
     * 类型
     */
    @Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
    private Integer type;
    /**
     * 封面
     */
    @Excel(name = "封面", width = 15)
    @ApiModelProperty(value = "封面")
    private String cover;
    /**
     * 简介
     */
    @Excel(name = "简介", width = 15)
    @ApiModelProperty(value = "简介")
    private String synopsis;
    /**
     * 演员
     */
    @Excel(name = "演员", width = 15)
    @ApiModelProperty(value = "演员")
    private String performer;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    @ExcelCollection(name = "cms_duanju_episode_info")
    @ApiModelProperty(value = "cms_duanju_episode_info")
    private List<CmsDuanjuEpisodeInfo> cmsDuanjuEpisodeInfoList;

}
