package com.eleven.cms.duanju.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisode;
import com.eleven.cms.duanju.entity.CmsDuanjuEpisodeInfo;
import com.eleven.cms.duanju.entity.DuanjuUser;
import com.eleven.cms.duanju.service.*;
import com.eleven.cms.service.ISmsValidateService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.TokenUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.repackaged.com.google.common.base.Strings;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.Login;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.util.oss.OssBootUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: cai lei
 * @create: 2024-07-17 18:50
 */
@Api(tags = "duanju_api")
@RestController
@RequestMapping("/duanju/api")
@Slf4j
public class DuanjuApiController {

    @Autowired
    ICmsDuanjuEpisodeService cmsDuanjuEpisodeService;
    @Autowired
    ICmsDuanjuEpisodeInfoService cmsDuanjuEpisodeInfoService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Autowired
    private IDuanjuCouponService duanjuCouponService;
    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    private IDuanjuUserService duanjuUserService;
    @Autowired
    private IDuanjuCollectService duanjuCollectService;
    @Autowired
    private IDuanjuLookHistoryService duanjuLookHistoryService;

    @RequestMapping(value = "/episode/list")
    public Result queryEpisodeList(CmsDuanjuEpisode cmsDuanjuEpisode,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<CmsDuanjuEpisode> queryWrapper = QueryGenerator.initQueryWrapper(cmsDuanjuEpisode, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<CmsDuanjuEpisode> page = new Page<>(pageNo, pageSize);
        IPage<CmsDuanjuEpisode> pageList = cmsDuanjuEpisodeService.page(page, queryWrapper);
        return Result.ok(pageList);
    }


    @RequestMapping(value = "/episode/editEpisodeInfo/{episodeId}")
    public Result editEpisodeInfo(@PathVariable String episodeId,
                                  @RequestBody List<CmsDuanjuEpisodeInfo> list,
                                  HttpServletRequest req) {
        if (list.size() == 0) {
            return Result.error("剧集明细不能为空");
        }
        for (CmsDuanjuEpisodeInfo cmsDuanjuEpisodeInfo : list) {
            cmsDuanjuEpisodeInfo.setEpisodeId(episodeId);
//            cmsDuanjuEpisodeInfo.setUpdateTime(new Date());
        }
        cmsDuanjuEpisodeInfoService.batchSaveOrUpdate(list);
        return Result.ok();
    }


    @RequestMapping(value = "/episode/list/{type}")
    public Result queryEpisodeList(@PathVariable Integer type,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        CmsDuanjuEpisode cmsDuanjuEpisode = new CmsDuanjuEpisode();
        cmsDuanjuEpisode.setType(type);
        QueryWrapper<CmsDuanjuEpisode> queryWrapper = QueryGenerator.initQueryWrapper(cmsDuanjuEpisode, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<CmsDuanjuEpisode> page = new Page<>(pageNo, pageSize);
        IPage<CmsDuanjuEpisode> pageList = cmsDuanjuEpisodeService.page(page, queryWrapper);
        return Result.ok(pageList);
    }


    @RequestMapping(value = "/episodeInfo/list/dev/{id}")
    public Result queryDevEpisodeInfoList(@PathVariable String id,
                                          HttpServletRequest req) {
        CmsDuanjuEpisodeInfo cmsDuanjuEpisodeInfo = new CmsDuanjuEpisodeInfo();
        cmsDuanjuEpisodeInfo.setEpisodeId(id);
        QueryWrapper<CmsDuanjuEpisodeInfo> queryWrapper = QueryGenerator.initQueryWrapper(cmsDuanjuEpisodeInfo, req.getParameterMap());
        queryWrapper.orderByAsc("current_number");
        List<CmsDuanjuEpisodeInfo> list = cmsDuanjuEpisodeInfoService.list(queryWrapper);
        return Result.ok(list);
    }

    /**
     * 短剧登录-发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getCaptcha")
    @ResponseBody
    public Result<?> getCaptcha(@RequestParam("mobile") String mobile,
                                @RequestParam(name = "channelCode", required = false, defaultValue =BizConstant.BIZ_TYPE_DUANJU) String channelCode,
                                @RequestParam(name = "serviceId",   required = false, defaultValue =BizConstant.SMS_MODEL_COMMON_SERVICE_ID) String serviceId) {
        log.info("短剧登录-发送短信验证码=>手机号:{},渠道号:{},业务ID:{}",mobile,channelCode,serviceId);
        if (com.google.common.base.Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.rightsCreate(mobile,channelCode,serviceId);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }
        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }
    /**
     * 短剧登录接口
     * @param mobile
     * @param captcha
     * @return
     */
    @PostMapping(value = "/login")
    @ResponseBody
    public Result duanjuLogin(@RequestParam("mobile") String mobile,
                                    @RequestParam("captcha")String captcha, HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        log.info("短剧登录接口=>手机号:{},短信验证码:{},referer:{}",mobile,captcha,referer);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.error("请输入正确格式手机号");
        }
        if (Strings.isNullOrEmpty(captcha) || !captcha.matches("^\\d{6}$")) {
            return Result.error("验证码错误");
        }
        try {
            smsValidateService.check(mobile, captcha);
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
        String token = TokenUtil.setLoginTime(mobile);
        DuanjuUser user= duanjuUserService.login(mobile);
        Result result= new Result();
        result.setCode(CommonConstant.SC_OK_200);
        result.setMessage("成功");
        result.setToken(token);
        result.setLevel(user.getLevel());
        result.setGoldenBean(user.getTotalGoldenBean());
        if(user.getInvalidTime()!=null){
            result.setInvalidTime(DateUtil.getDateFormat(user.getInvalidTime(),DateUtil.FULL_TIME_SPLIT_PATTERN));
        }
        return result;
    }


    /**
     * 系统优惠券列表查询
     * @return
     */
    @RequestMapping(value = "/query/couponList")
    @Login
    public Result<?> queryCouponList(HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        log.info("系统优惠券列表查询=>手机号:{},referer:{}",mobile,referer);
        return duanjuCouponService.queryCouponList();
    }

    /**
     * 金豆兑换优惠券
     * @param rightsId
     * @return
     */
    @PostMapping(value = "/goldenBean/exchange/coupon")
    @ResponseBody
    @Login
    public Result exchangeCoupon(@RequestParam("rightsId") String rightsId,
                                 @RequestParam(value = "num", required = false, defaultValue = "1") String num,HttpServletRequest request) {

        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("金豆兑换优惠券=>手机号:{},兑换优惠券数量:{},兑换优惠券类型:{},referer:{}",mobile,num,rightsId,referer);
        if (StringUtils.isEmpty(rightsId)) {
            return Result.error("权益ID不能为空");
        }
        return duanjuCouponService.exchangeCoupon(mobile,num,rightsId);
    }

    /**
     * 优惠券兑换剧集
     * @param dramaId
     * @return
     */
    @PostMapping(value = "/episode/buyEpisode")
    @ResponseBody
    @Login
    public Result<?> buyEpisode(@RequestParam("dramaId") String dramaId,HttpServletRequest request) {

        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("优惠券兑换剧集=>手机号:{},剧集ID:{},referer:{}",mobile,dramaId,referer);
        if (StringUtils.isEmpty(dramaId)) {
            return Result.error("剧集ID不能为空");
        }
        return duanjuCouponService.useCoupon(mobile,dramaId);
    }
    /**
     * 剧集鉴权
     * @param dramaId
     * @return
     */
    @RequestMapping(value = "/episode/auth")
    @Login
    public Result<?> episodeAuth(@RequestParam String dramaId,HttpServletRequest request) {

        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("剧集鉴权=>手机号:{},剧集ID:{},referer:{}",mobile,dramaId,referer);
        if (StringUtils.isEmpty(dramaId)) {
            return Result.error("剧集ID不能为空");
        }
        return duanjuUserService.episodeAuth(mobile,dramaId);
    }

    /**
     * 资产查询
     * @return
     */
    @RequestMapping(value = "/query/wealth")
    @Login
    public Result<?> queryWealth(HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("资产查询=>手机号:{},referer:{}",mobile,referer);
        return duanjuUserService.queryWealth(mobile);
    }

    @RequestMapping(value = "/episodeInfo/list/{id}")
    @Login
    public Result queryEpisodeInfoList(@PathVariable String id,
                                       HttpServletRequest req) {
        CmsDuanjuEpisodeInfo cmsDuanjuEpisodeInfo = new CmsDuanjuEpisodeInfo();
        cmsDuanjuEpisodeInfo.setEpisodeId(id);
        QueryWrapper<CmsDuanjuEpisodeInfo> queryWrapper = QueryGenerator.initQueryWrapper(cmsDuanjuEpisodeInfo, req.getParameterMap());
        queryWrapper.orderByAsc("current_number");
        List<CmsDuanjuEpisodeInfo> list = cmsDuanjuEpisodeInfoService.list(queryWrapper);
        return Result.ok(list);
    }

    /*
     *  图片上传
     *
     * */
    @ResponseBody
    @RequestMapping("/upload")
    public Result<?> upload(MultipartFile file) {
        Result<?> result = new Result<>();
        String url = "";
        try {
            if (file != null) {
                url = OssBootUtil.upload(file, "duanju/");
                if (oConvertUtils.isNotEmpty(url)) {
                    result.setMessage(url);
                    result.setSuccess(true);
                } else {
                    result.setMessage("上传失败！");
                    result.setSuccess(false);
                }
            }
        } catch (Exception e) {
            log.error("上传失败：", e);
        }
        return result;
    }

    /**
     * 查询剧集是否收藏
     * @param dramaId
     * @param request
     * @return
     */
    @PostMapping(value = "/query/user/collect")
    @ResponseBody
    @Login
    public Result queryUserCollect(@RequestParam("dramaId") String dramaId,HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("查询剧集是否收藏=>手机号:{},剧集ID:{},referer:{}",mobile,dramaId,referer);
        if (StringUtils.isEmpty(dramaId)) {
            return Result.error("剧集ID不能为空");
        }
        return duanjuCollectService.queryUserCollect(mobile,dramaId);
    }
    /**
     * 用户收藏剧集
     * @param dramaId
     * @param request
     * @return
     */
    @PostMapping(value = "/user/collect")
    @ResponseBody
    @Login
    public Result userCollect(@RequestParam("dramaId") String dramaId,HttpServletRequest request) {

        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("用户收藏剧集=>手机号:{},剧集ID:{},referer:{}",mobile,dramaId,referer);
        if (StringUtils.isEmpty(dramaId)) {
            return Result.error("剧集ID不能为空");
        }
        return duanjuCollectService.userCollect(mobile,dramaId);
    }
    /**
     * 用户收藏列表
     * @param request
     * @return
     */
    @PostMapping(value = "/user/collectList")
    @ResponseBody
    @Login
    public Result userCollectList(HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("用户收藏列表=>手机号:{},referer:{}",mobile,referer);
        return duanjuCollectService.queryUserCollectList(mobile);
    }


    /**
     * 用户创建最近观看
     * @param dramaId
     * @param request
     * @return
     */
    @PostMapping(value = "/user/lookHistory")
    @ResponseBody
    @Login
    public Result userLookHistory(@RequestParam("dramaId") String dramaId,@RequestParam("number") String number,HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("用户创建最近观看=>手机号:{},剧集ID:{},集数:{},referer:{}",mobile,dramaId,number,referer);
        if (StringUtils.isEmpty(dramaId)) {
            return Result.error("剧集ID不能为空");
        }
        if (StringUtils.isEmpty(number)) {
            return Result.error("观看集数不能为空");
        }
        return duanjuLookHistoryService.userLookHistory(mobile,dramaId,number);
    }

    /**
     * 用户观看列表
     * @param request
     * @return
     */
    @PostMapping(value = "/user/lookHistoryList")
    @ResponseBody
    @Login
    public Result userLookHistoryList(HttpServletRequest request) {
        String mobile =String.valueOf(TokenUtil.getLoginMobile());
        String referer = request.getHeader("Referer");
        log.info("用户观看列表=>手机号:{},referer:{}",mobile,referer);
        return duanjuLookHistoryService.userLookHistoryList(mobile);
    }
}
