package com.eleven.cms.duanju.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.duanju.entity.DuanjuCollect;
import com.eleven.cms.duanju.entity.DuanjuLookHistory;
import com.eleven.cms.duanju.mapper.DuanjuLookHistoryMapper;
import com.eleven.cms.duanju.service.IDuanjuCollectService;
import com.eleven.cms.duanju.service.IDuanjuLookHistoryService;
import com.eleven.cms.entity.CommonCoupon;
import com.eleven.cms.util.BizConstant;
import com.google.api.client.util.Lists;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 咪咕互娱短剧观看记录
 * @Author: jeecg-boot
 * @Date:   2024-08-13
 * @Version: V1.0
 */
@Service
public class DuanjuLookHistoryServiceImpl extends ServiceImpl<DuanjuLookHistoryMapper, DuanjuLookHistory> implements IDuanjuLookHistoryService {
    @Autowired
    private IDuanjuCollectService duanjuCollectService;
    @Override
    public Result userLookHistory(String mobile, String dramaId, String number) {
        DuanjuLookHistory duanjuLookHistory= this.lambdaQuery().select(DuanjuLookHistory::getId,DuanjuLookHistory::getVersion).eq(DuanjuLookHistory::getMobile,mobile).eq(DuanjuLookHistory::getDramaId,dramaId).orderByDesc(DuanjuLookHistory::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duanjuLookHistory!=null){
            duanjuLookHistory.setNewNumber(Integer.valueOf(number));
            this.baseMapper.updateLookHistory(duanjuLookHistory);
        }else{
            DuanjuLookHistory history=new DuanjuLookHistory();
            /**手机号*/
            history.setMobile(mobile);
            /**剧集ID*/
            history.setDramaId(dramaId);
            /**集数*/
            history.setNewNumber(Integer.valueOf(number));
            this.save(history);
        }
        DuanjuCollect duanjuCollect= duanjuCollectService.lambdaQuery().select(DuanjuCollect::getId,DuanjuCollect::getVersion).eq(DuanjuCollect::getMobile,mobile).eq(DuanjuCollect::getDramaId,dramaId).orderByDesc(DuanjuCollect::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duanjuCollect!=null){
            duanjuCollect.setNewNumber(Integer.valueOf(number));
            duanjuCollectService.editDuanjuCollect(duanjuCollect);
        }
        return Result.ok("观看历史创建成功！");
    }

    @Override
    public Result userLookHistoryList(String mobile) {
        //分组查询当前用户全部观看过的历史剧集
        List<DuanjuLookHistory> duanjuLookHistoryList=this.baseMapper.findByMobile(mobile);
        return Result.ok(duanjuLookHistoryList);
    }
}
