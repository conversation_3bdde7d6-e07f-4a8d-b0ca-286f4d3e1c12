package com.eleven.cms.client;

import com.eleven.cms.dto.BusinessChargeDto;
import com.eleven.cms.dto.RightsDto;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.vo.FebsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 统一权益接口
 */

@Component
@FeignClient(name="febs-migu-member",url="${conf.address.febs-migu-member}", configuration = FeignConfig.class, primary = false)
public interface UnifyRightsFeignClient {


    /**
     * 会员登录
     * @param phone  手机号
     * @return
     */
    @PostMapping(value = "/api/unify/member/login")
    @ResponseBody
    FebsResponse memberLogin(@RequestParam(name = "phone") String phone,@RequestParam(name = "channelCode") String channelCode);
    /**
     * 查询是否会员
     * @return
     */
    @PostMapping(value = "/api/unify/check/member")
    @ResponseBody
    FebsResponse checkMember(@RequestParam(name = "unifyToken") String unifyToken,@RequestHeader(name = "token") String token,@RequestParam(name = "channelCode") String channelCode);

    /**
     *
     * 查询权益列表
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/unify/query/rights/list")
    @ResponseBody
    FebsResponse queryRightsList(@RequestParam(name = "serviceId") String serviceId,@RequestHeader(name = "token") String token);
    /**
     * 会员领取权益
     * @param serviceId
     * @param rightsId
     * @param packName
     * @return
     */
    @PostMapping(value = "/api/unify/member/receive/rights")
    @ResponseBody
    FebsResponse memberReceiveRights(@RequestParam(name = "serviceId") String serviceId, @RequestParam(name = "packName") String packName, @RequestParam(name = "rightsId") String rightsId,@RequestHeader(name = "token") String token,@RequestParam(name = "account") String account);

    /**
     * 查询充值列表
     * @return
     */
    @PostMapping(value = "/api/unify/query/charge/list")
    @ResponseBody
    FebsResponse queryChargeList(@RequestParam(name = "rightsMonth") String rightsMonth, @RequestParam(name = "sourse") String sourse,@RequestHeader(name = "token") String token);



    /**
     * 百度网盘权益领取
     * @return
     */
    @PostMapping(value = "/api/pan/acquire")
    @ResponseBody
    FebsResponse receiveBdwp(@RequestHeader(name = "token") String token);


    /**
     * 查询产品和产品订阅列表
     * @return
     */
    @PostMapping(value = "/api/unify/query/product/subscribe/list")
    @ResponseBody
    FebsResponse queryProductAndSubscribe(@RequestHeader(name = "token") String token);

    /**
     *
     * 抖音查询权益列表
     * @param mobile
     * @return
     */
    @PostMapping(value = "/api/douyin/query/rightsList")
    @ResponseBody
    FebsResponse douyinQueryRightsList(@RequestParam(name = "mobile") String mobile);


    /**
     * 抖音权益充值
     * @param mobile 手机号
     * @param account 账号
     * @param couponId 产品Id
     * @param couponName 产品名称
     * @return
     */
    @PostMapping(value = "/api/douyin/rights/recharge")
    @ResponseBody
    FebsResponse douyinRightsRecharge(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "account") String account,@RequestParam(name = "couponId") String couponId,@RequestParam(name = "couponName") String couponName,@RequestParam(name = "channelId") String channelId,@RequestParam(name = "sign") String sign);

    /**
     * 抖音查询权益产品价格
     * @param mobile 手机号
     * @param couponId 产品Id
     * @param channelId
     * @param sign
     * @return
     */
    @PostMapping(value = "/api/douyin/rights/product/price")
    @ResponseBody
    FebsResponse douyinQueryRightsProductPrice(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "couponId")  String couponId, @RequestParam(name = "channelId") String channelId, @RequestParam(name = "sign") String sign);


    /**
     * 业务订购记录列表
     * @param firstDay
     * @param endDay
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/business/order/log")
    @ResponseBody
    FebsResponse businessOrderLogList(@RequestParam(name = "firstDay") String firstDay, @RequestParam(name = "endDay") String endDay,@RequestParam(name = "serviceId") String serviceId);


    /**
     * 查询是否本月订购会员
     * @param mobile
     * @return
     */
    @PostMapping(value = "/api/query/member/log")
    @ResponseBody
    FebsResponse isMember(@RequestParam(name = "mobile") String mobile);



    /**
     * 途牛权益充值
     * @param mobile
     * @param actId
     * @return
     */
    @PostMapping(value = "/api/tuniu/recharge/rights")
    @ResponseBody
    FebsResponse tuniuSendRights(@RequestParam(name = "mobile") String mobile, @RequestParam(name = "actId") String actId);



    /**
     * 渠道订阅
     * @param subscribe
     * @return
     */
    @PostMapping(value = "/api/xxlSub")
    @ResponseBody
    FebsResponse xxlSubscribe(@RequestBody Subscribe subscribe, @RequestHeader("x-requested-with") String appPackage);


    /**
     * 查询业务包
     * @param titleName
     * @return
     */
    @PostMapping(value = "/api/queryPackByBusinessId")
    @ResponseBody
    FebsResponse queryPackByBusinessId(@RequestParam(name = "titleName") String titleName);


    /**
     * 查询业务包
     * @param id
     * @return
     */
    @PostMapping(value = "/api/query/rights/list")
    @ResponseBody
    FebsResponse queryRightsList(@RequestParam(name = "id")  String id);


    /**
     * 业务充值统计
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/query/business/list")
    @ResponseBody
    FebsResponse findByPage(@RequestBody BusinessChargeDto dto);
    /**
     * 业务关联权益查询
     * @return
     */
    @PostMapping(value = "/api/getServiceList")
    @ResponseBody
    FebsResponse getServiceList(@RequestParam(name = "serviceId") List<String> serviceId);

    /**
     * 业务关联权益查询
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/queryRightsList")
    @ResponseBody
    FebsResponse findByServiceId(@RequestBody RightsDto dto);



    /**
     * 百度网盘查询是否订购
     * @param mobile
     * @param channelList
     * @return
     */
    @PostMapping(value = "/api/rights/BaiDuWangPan/check/member")
    @ResponseBody
    FebsResponse baiDuWangPanCheckMember(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "channelList") List<String> channelList);


    /**
     * 贝壳视听查询是否订购
     * @param mobile
     * @return
     */
    @PostMapping(value = "/api/rights/BeiKeShiTing/check/member")
    @ResponseBody
    FebsResponse beiKeShiTingCheckMember(@RequestParam(name = "mobile") String mobile);


    /**
     * 咪咕阅读查询是否订购
     * @param mobile
     * @param productId
     * @return
     */
    @PostMapping(value = "/api/rights/MiGuRead/check/member")
    @ResponseBody
    FebsResponse miGuReadCheckMember(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "productId") String productId);


    /**
     * 咪咕动漫查询是否订购
     * @param mobile
     * @param channelCode
     * @return
     */
    @PostMapping(value = "/api/rights/MiGuComic/check/member")
    @ResponseBody
    FebsResponse miGuComicCheckMember(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "channelCode") String channelCode);


    /**
     * 联通喜马拉雅联合会员查询是否订购
     * @param mobile
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/rights/HiMaLaYan/check/member")
    @ResponseBody
    public FebsResponse hiMaLaYanCheckMember(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "serviceId") String serviceId);
    /**
     * 咪咕网易云联合会员查询是否订购
     * @param mobile
     * @param channelList
     * @return
     */
    @PostMapping(value = "/api/rights/WangYiYun/check/member")
    @ResponseBody
    public FebsResponse wangYiYunCheckMember(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "channelList") List<String> channelList);

    /**
     * 埋堆堆查询是否订购
     * @param mobile
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/rights/MaiDuiDui/check/member")
    @ResponseBody
    public FebsResponse maiDuiDuiCheckMember(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "serviceId") String serviceId);

    /**
     * 乐享悦听查询是否订购
     * @param mobile
     * @param serviceId
     * @param channelList
     * @return
     */
    @PostMapping(value = "/api/rights/LeXiangShiTing/check/member")
    @ResponseBody
    public FebsResponse leXiangShiTinCheckMember(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "serviceId") String serviceId,@RequestParam(name = "channelList") List<String> channelList);
    /**
     * 判断当月权益是否已全部发放
     * @param mobile
     * @return
     */
    @PostMapping(value = "/api/rights/monthlyIsRecharge")
    @ResponseBody
    public FebsResponse monthlyIsRecharge(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "serviceId") String serviceId);

    /**
     * 乐享悦听判断当月权益是否已全部发放
     * @param mobile
     * @return
     */
    @PostMapping(value = "/api/rights/LeXiangShiTing/monthlyIsRecharge")
    @ResponseBody
    public FebsResponse leXiangShiTingMonthlyIsRecharge(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "serviceId") String serviceId);
    /**
     * 校验当月会员权益是否已发放
     * @param mobile
     * @param packName
     * @param scheduledTime
     * @param couponId
     * @return
     */
    @PostMapping(value = "/api/rights/isRecharge")
    @ResponseBody
    public FebsResponse isRecharge(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "packName") String packName,@RequestParam(name = "scheduledTime") String scheduledTime,@RequestParam(name = "couponId") String couponId);


    @PostMapping(value = "/api/rights/LeXiangShiTing/isRecharge")
    @ResponseBody
    public FebsResponse leXiangShiTingIsRecharge(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "packName") String packName,@RequestParam(name = "scheduledTime") String scheduledTime,@RequestParam(name = "couponId") String couponId);

    /**
     * 查询当月是否已充值
     * @param mobile
     * @param packName
     * @return
     */
    @PostMapping(value = "/api/rights/month/rechargeSuccess")
    @ResponseBody
    public FebsResponse findByMobileAndSuccess(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "packName") String packName);


    /**
     * 咪咕阅读权益充值更新状态
     * @param mobile
     * @param month
     * @param status
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/rights/MiGuRead/UpdateStatus")
    @ResponseBody
    public FebsResponse miGuReadUpdateStatus(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "month") String month,@RequestParam(name = "status") String status,@RequestParam(name = "serviceId") String serviceId);

    /**
     * 咪咕动漫权益充值更新状态
     * @param mobile
     * @param month
     * @param status
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/rights/MiGuComic/UpdateStatus")
    @ResponseBody
    public FebsResponse miGuComicUpdateStatus(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "month") String month,@RequestParam(name = "status") String status,@RequestParam(name = "serviceId") String serviceId);

    /**
     * 联通喜马拉雅联合会员权益充值更新状态
     * @param mobile
     * @param month
     * @param status
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/rights/HiMaLaYan/UpdateStatus")
    @ResponseBody
    public FebsResponse hiMaLaYanUpdateStatus(@RequestParam(name = "mobile") String mobile,@RequestParam(name = "month") String month,@RequestParam(name = "status") String status,@RequestParam(name = "serviceId") String serviceId);


    /**
     * 查询权益包列表
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/rights/query/rightsPackList")
    @ResponseBody
    FebsResponse queryRightsPackList(@RequestParam(name = "serviceId") String serviceId);

    /**
     * 网页查询权益包列表
     * @param serviceId
     * @return
     */
    @PostMapping(value = "/api/rights/query/web/rightsPackList")
    @ResponseBody
    FebsResponse queryWebRightsPackList(@RequestParam(name = "serviceId") String serviceId);

    /**
     * 查询领取记录
     * @param mobile
     * @return
     */
    @PostMapping(value = "/api/rights/query/rechargeList")
    @ResponseBody
    FebsResponse queryRechargeList(@RequestParam(name = "mobile") String mobile);

//    /**
//     * 获取预约充值时间
//     * @param serviceId
//     * @param packName
//     * @return
//     */
//    @PostMapping(value = "/api/rights/query/scheduledTime")
//    @ResponseBody
//    FebsResponse getScheduledTime(@RequestParam(name = "serviceId") String serviceId,@RequestParam(name = "packName") String packName);

    /**
     * 根据ID查询业务
     * @return
     */
    @PostMapping(value = "/api/rights/query/rightsList")
    @ResponseBody
    FebsResponse queryRightsListById(@RequestParam(name = "id")String id);
}
