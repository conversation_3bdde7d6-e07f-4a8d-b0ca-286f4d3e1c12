package com.eleven.cms.client.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: app_mobile_pay_channel_config
 * @Author: jeecg-boot
 * @Date: 2025-06-03
 * @Version: V1.0
 */
@Data
@TableName("app_mobile_pay_channel_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "app_mobile_pay_channel_config对象", description = "app_mobile_pay_channel_config")
public class AppMobilePayChannelConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 运营商
     */
    @Excel(name = "运营商", width = 15)
    @ApiModelProperty(value = "运营商")
    @Dict(dicCode = "app_phone_operator")
    private String operator;
    /**
     * 计费代码
     */
    @Excel(name = "计费代码", width = 15)
    @ApiModelProperty(value = "计费代码")
    private String channel;
    /**
     * 支持省份
     */
    @Excel(name = "支持省份", width = 15)
    @ApiModelProperty(value = "支持省份")
    private String province;
    /**
     * 产品名称
     */
    @Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 资费
     */
    @Excel(name = "资费", width = 15)
    @ApiModelProperty(value = "资费")
    private BigDecimal payAmount;
    /**
     * 限量
     */
    @Excel(name = "限量", width = 15)
    @ApiModelProperty(value = "限量")
    @TableField(value = "limited")
    private Integer limited;
    /**
     * 限量说明
     */
    @Excel(name = "限量说明", width = 15)
    @ApiModelProperty(value = "限量说明")
    private String limitDescription;
    /**
     * 报备业务办理说明
     */
    @Excel(name = "报备业务办理说明", width = 15)
    @ApiModelProperty(value = "报备业务办理说明")
    private String businessInstructions;
    /**
     * 是否是全国省份 0否 1是
     */
    @Excel(name = "是否是全国省份", width = 15)
    private Integer allProvince;
    /**
     * 状态 0关闭 1开启
     */
    @Excel(name = "状态 0关闭 1开启", width = 15)
    @ApiModelProperty(value = "状态 0关闭 1开启")
    private Integer status;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
