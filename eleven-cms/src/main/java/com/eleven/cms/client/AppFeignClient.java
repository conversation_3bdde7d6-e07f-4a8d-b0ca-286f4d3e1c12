package com.eleven.cms.client;

import com.eleven.cms.aivrbt.dto.AppLoginDTO;
import com.eleven.cms.aivrbt.dto.MiniOrderPayDTO;
import com.eleven.cms.aivrbt.vo.AppMembershipPackageProductVO;
import com.eleven.cms.aivrbt.vo.AppUserVO;
import org.jeecg.common.api.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;


/**
 * 统一权益接口
 */

@Component
@FeignClient(name = "app-service", url = "${kpApp.baseUrl}", configuration = FeignConfig.class)
public interface AppFeignClient {

    @PostMapping(value = "/aivrbt_app/user/mini/login")
    @ResponseBody
    Result<AppUserVO> appMiniLogin(@RequestBody AppLoginDTO appLoginDTO, @RequestHeader("token") String token);

    @PostMapping(value = "/aivrbt_app/user/login")
    @ResponseBody
    Result<AppUserVO> appLogin(@RequestBody AppLoginDTO appLoginDTO);

    @PostMapping(value = "/aivrbt_app/order/mini/order/equity")
    @ResponseBody
    Result<AppUserVO> miniOrderEquity(@RequestBody MiniOrderPayDTO orderPayDTO, @RequestHeader("token") String token);

    @GetMapping("/aivrbt_app/order/getMemberShipPackageProduct")
    Result<AppMembershipPackageProductVO> getMemberShipPackageProduct(@RequestParam("id") String id);

}
