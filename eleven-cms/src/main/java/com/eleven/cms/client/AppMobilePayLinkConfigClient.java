package com.eleven.cms.client;

import com.eleven.cms.client.entity.AppMobilePayChannelConfig;
import org.jeecg.common.api.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "miniAppAiRingBannerClient", url = "${kpApp.baseUrl}", configuration = FeignConfig.class)
public interface AppMobilePayLinkConfigClient {

    @GetMapping(value = "/api/rpc/cms/appMobilePayLinkConfig/queryLinkByChannel")
    Result<?> queryLinkByChannel(@RequestParam(required = false) String appName
            , @RequestParam(required = false) String channel
            , @RequestParam(required = false) Integer pageType);

    @GetMapping(value = "/api/rpc/cms/appMobilePayLinkConfig/queryLinkList")
    Result<?> queryLinkList(@RequestParam(name = "appName") String appName);

    @GetMapping(value = "/api/rpc/cms/appMobilePayChannelConfig/filterPayConfig")
    List<AppMobilePayChannelConfig> filterPayConfig(@RequestParam String operator, @RequestParam String province, @RequestParam List<String> idList);
}
