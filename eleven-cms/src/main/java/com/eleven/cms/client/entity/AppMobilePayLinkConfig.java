package com.eleven.cms.client.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: app_mobile_pay_link_config
 * @Author: jeecg-boot
 * @Date: 2025-06-17
 * @Version: V1.0
 */
@Data
@TableName("app_mobile_pay_link_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "app_mobile_pay_link_config对象", description = "app_mobile_pay_link_config")
public class AppMobilePayLinkConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 关联支付渠道配置id
     */
    @Excel(name = "关联支付渠道配置id", width = 15)
    @ApiModelProperty(value = "关联支付渠道配置id")
    private String payChannelConfigId;
    /**
     * app包名
     */
    @Excel(name = "app包名", width = 15)
    @ApiModelProperty(value = "app包名")
    private String appName;
    /**
     * 0-app 1-小程序
     */
    @Excel(name = "应用类型", width = 15)
    private Integer appType;
    /**
     * 页面类型 0-破解页 1-报备页
     */
    @Excel(name = "破解链接", width = 15)
    @ApiModelProperty(value = "破解链接")
    private Integer pageType;
    /**
     * 推广链接
     */
    @Excel(name = "推广链接", width = 15)
    @ApiModelProperty(value = "推广链接")
    private String promotionalLink;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(exist = false)
    private String channel;

    @TableField(exist = false)
    private String productName;
}
