package com.eleven.cms.report.controller;

import com.eleven.cms.dto.ShanDongOrderComplaintQryDTO;
import com.eleven.cms.dto.ShanDongUnsubscribeDTO;
import com.eleven.cms.report.vo.ShanDongYiDongReportVO;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.TimeUtils;
import com.eleven.cms.util.shandong.ShanDongYiDongHttpUtils;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2025/1/22 16:11
 */
@Api(tags="ai_ring_column")
@RestController
@RequestMapping("/api/report")
@Slf4j
public class ReportController {

    /**
     * 导出山东移动报告
     *
     * @param startDate
     * @param endDate
     */
    @GetMapping(value = "/export/sdydReport")
    public ModelAndView exportSdydReport(String startDate, String endDate) throws ParseException {
        List<ShanDongYiDongReportVO> data = new ArrayList<>();

        List<String> dateList = TimeUtils.getDateList(startDate, endDate);
        for (String date : dateList) {
            ShanDongYiDongReportVO shanDongYiDongReportVO = new ShanDongYiDongReportVO();
            shanDongYiDongReportVO.setDate(date);
            shanDongYiDongReportVO.setTsCount(getTsCount(date));
            shanDongYiDongReportVO.setTdCount(getTdCount(date));
            data.add(shanDongYiDongReportVO);
        }

        String title = "山东移动投诉-退订数报告";
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, title); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, ShanDongYiDongReportVO.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams(title + "报表", "", title));
        mv.addObject(NormalExcelConstants.DATA_LIST, data);
        return mv;
    }

    private Integer getTsCount(String date) {
        int count = 0;
        int page = 1;
        boolean flag = true;
        while (flag) {
            ShanDongOrderComplaintQryDTO complaintQryDTO = new ShanDongOrderComplaintQryDTO();
            complaintQryDTO.setTime(date);
            complaintQryDTO.setPage(String.valueOf(page));
            String result = ShanDongYiDongHttpUtils.qryOrderComplaint(complaintQryDTO);

            JsonNode jsonNode = JacksonUtils.readTree(result);
            if ("10000".equals(jsonNode.at("/code").asText())) {
                count = count + jsonNode.at("/data/total").intValue();
                page++;
            } else {
                flag = false;
            }
        }
        return count;
    }

    private Integer getTdCount(String date) {
        int count = 0;
        int page = 1;
        boolean flag = true;
        while (flag) {
            ShanDongUnsubscribeDTO unsubscribeDTO = new ShanDongUnsubscribeDTO();
            unsubscribeDTO.setOpTime(date);
            unsubscribeDTO.setPage(String.valueOf(page));
            String result = ShanDongYiDongHttpUtils.unsubscribe(unsubscribeDTO);

            JsonNode jsonNode = JacksonUtils.readTree(result);
            if ("10000".equals(jsonNode.at("/code").asText())) {
                count = count + jsonNode.at("/data/total").intValue();
                page++;
            } else {
                flag = false;
            }
        }
        return count;
    }
}