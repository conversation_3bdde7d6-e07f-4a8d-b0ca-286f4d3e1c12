package com.eleven.cms.abstracts;

import com.fasterxml.jackson.databind.JsonNode;
import org.jeecg.common.api.vo.Result;

/**
 * 抽象业务办理类
 *
 * <AUTHOR>
 * @datetime 2024/11/20 14:35
 */
public abstract class AbstractOrder {

    /**
     * 获取验证码
     * @param jsonNode jsonNode
     *
     * @return Boolean
     */
    public Result getSmsCode(JsonNode jsonNode) {
        Result beforeGetSmsCodeResult = beforeGetSmsCodeRequest(jsonNode);
        return beforeGetSmsCodeResult.isOK()? getSmsCodeRequest(jsonNode) : beforeGetSmsCodeResult;
    }

    /**
     * 下单
     * @param jsonNode jsonNode
     *
     * @return Boolean
     */
    public Result order(JsonNode jsonNode) {
        Result beforeOrderResult = beforeOrderRequest(jsonNode);
        return beforeOrderResult.isOK()? orderRequest(jsonNode) : beforeOrderResult;
    }

    /**
     * 获取验证码前校验
     * @param jsonNode jsonNode
     *
     * @return Boolean
     */
    protected Result beforeGetSmsCodeRequest(JsonNode jsonNode) {
        return Result.ok();
    }

    /**
     * 获取验证码
     * @param jsonNode jsonNode
     *
     * @return Boolean
     */
    protected abstract Result getSmsCodeRequest(JsonNode jsonNode);

    /**
     * 提交验证码下单前校验
     * @param jsonNode jsonNode
     *
     * @return Boolean
     */
    protected Result beforeOrderRequest(JsonNode jsonNode) {
        return Result.ok();
    }

    /**
     * 提交验证码下单
     * @param jsonNode jsonNode
     *
     * @return Boolean
     */
    protected abstract Result orderRequest(JsonNode jsonNode);
}
