package com.eleven.cms.annotation;

import com.google.common.collect.Lists;

import java.lang.annotation.*;
import java.util.List;

@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface IvrAlipayUnsubscribeLimit {

    /**
     * 限制访问次数
     */
    int count();


    /**
     * 短信内容
     */
    String msgContent();
    /**
     * 发送短信手机号
     */
    String[] mobileArray() default {"17380148636", "18080928200"};


}
