package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_ali_sign_record
 * @Author: jeecg-boot
 * @Date:   2022-12-29
 * @Version: V1.0
 */
@Data
@TableName("cms_ali_sign_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_ali_sign_record对象", description="cms_ali_sign_record")
public class AliSignRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**自增主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "自增主键")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**商户签约号*/
	@Excel(name = "商户签约号", width = 15)
    @ApiModelProperty(value = "商户签约号")
    private String externalAgreementNo;

    /**商户号appId*/
    @Excel(name = "商户号appId", width = 15)
    @ApiModelProperty(value = "商户号appId")
    private String appId;

    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**业务名字*/
    @Excel(name = "业务名字", width = 15)
    @ApiModelProperty(value = "业务名字")
    private String businessName;

	/**签约状态(0:未签约 1:签约成功 2:签约失败)*/
	@Excel(name = "签约状态(0:未签约 1:签约成功 2:签约失败 3:解约)", width = 15)
    @ApiModelProperty(value = "签约状态(0:未签约 1:签约成功 2:签约失败 3:解约 4:签约失效)")
    private Integer signStatus;
    /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
    @Excel(name = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败", width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private java.lang.Integer refundStatus;
	/**发起签约时间*/
	@Excel(name = "发起签约时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发起签约时间")
    private Date signUpTime;
	/**签约成功时间*/
	@Excel(name = "签约成功时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签约成功时间")
    private Date signSuccessTime;
	/**协议号*/
	@Excel(name = "协议号", width = 15)
    @ApiModelProperty(value = "协议号")
    private String agreementNo;
	/**创建时间*/
    @Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新时间*/
    @Excel(name = "更新时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**扣费说明*/
	@Excel(name = "扣费说明", width = 15)
    @ApiModelProperty(value = "扣费说明")
    private String remark;
    /**扣费说明*/
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String alipayUserId;
    /**下次扣款日期*/
    @Excel(name = "下次扣款日期", width = 15)
    @ApiModelProperty(value = "下次扣款日期")
    private Date nextDeductTime;
    /**签约失效时间*/
    @Excel(name = "签约失效时间", width = 15)
    @ApiModelProperty(value = "签约失效时间")
    private String invalidTime;
    /**解约时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "解约时间")
    private java.util.Date unsignTime;
    /**子渠道号*/
    @Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private java.lang.String subChannel;

    /**扣款执行日期*/
    @Excel(name = "扣款执行日期", width = 15)
    @ApiModelProperty(value = "扣款执行日期")
    private String deductDay;

    /**发送扣款短信执行日期*/
    @Excel(name = "发送扣款短信执行日期", width = 15)
    @ApiModelProperty(value = "发送扣款短信执行日期")
    private String sendDeductMsgDay;


    /**业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃*/
    @Excel(name = "业务类型", width = 15, dicCode = "biz_type")
    @ApiModelProperty(value = "业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃")
    @Dict(dicCode = "biz_type")
    private String bizType;
}
