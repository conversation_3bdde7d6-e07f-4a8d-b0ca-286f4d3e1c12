package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_game_config
 * @Author: jeecg-boot
 * @Date:   2022-03-22
 * @Version: V1.0
 */
@ApiModel(value="cms_game_config对象", description="券码")
@Data
@TableName("cms_game_config")
public class GameConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;
	/**游戏名称*/
	@Excel(name = "游戏名称", width = 15)
    @ApiModelProperty(value = "游戏名称")
    private String gameName;

    /**日志标记*/
    @Excel(name = "日志标记", width = 15)
    @ApiModelProperty(value = "日志标记")
    private String logTag;


    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String bizType;

    /**验签密钥*/
    @Excel(name = "验签密钥", width = 15)
    @ApiModelProperty(value = "验签密钥")
    private String signKey;

    /**手机号解码密钥*/
    @Excel(name = "手机号解码密钥", width = 15)
    @ApiModelProperty(value = "手机号解码密钥")
    private String mobileKey;

    /**发码通知地址*/
    @Excel(name = "发码通知地址", width = 15)
    @ApiModelProperty(value = "发码通知地址")
    private String sendbackUrl;


    /**券码使用通知地址*/
    @Excel(name = "券码使用通知地址", width = 15)
    @ApiModelProperty(value = "券码使用通知地址")
    private String receivebackUrl;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
