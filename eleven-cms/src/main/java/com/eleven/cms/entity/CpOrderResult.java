package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_cp_order_result
 * @Author: jeecg-boot
 * @Date:   2024-10-22
 * @Version: V1.0
 */
@Data
@TableName("cms_cp_order_result")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_cp_order_result对象", description="cms_cp_order_result")
public class CpOrderResult implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;

	/**合作方ID*/
	@Excel(name = "合作方ID", width = 15)
    @ApiModelProperty(value = "合作方ID")
    private String moduleId;

    /**产品ID*/
    @Excel(name = "产品ID", width = 15)
    @ApiModelProperty(value = "产品ID")
    private String productId;

    /**省份*/
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;

    /**渠道订单号*/
    @Excel(name = "渠道订单号", width = 15)
    @ApiModelProperty(value = "渠道订单号")
    private String orderId;

    /**提交日期*/
    @Excel(name = "提交日期", width = 15)
    @ApiModelProperty(value = "提交日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**联系电话*/
    @Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**订单号来源渠道*/
    @Excel(name = "来源渠道订单号", width = 15)
    @ApiModelProperty(value = "来源渠道订单号")
    private String sourceOrderId;

    /**创建时间*/
    @Excel(name = "创建时间", width = 15)
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
