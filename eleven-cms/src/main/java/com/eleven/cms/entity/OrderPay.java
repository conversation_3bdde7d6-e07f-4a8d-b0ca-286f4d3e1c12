package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 支付记录
 * @Author: jeecg-boot
 * @Date:   2023-09-20
 * @Version: V1.0
 */
@Data
@TableName("cms_order_pay")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_order_pay对象", description="支付记录")
public class OrderPay implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**手机号*/
    @Excel(name = "登录手机号", width = 15)
    @ApiModelProperty(value = "登录手机号")
    private String loginMobile;
	/**手机号*/
	@Excel(name = "充值手机号", width = 15)
    @ApiModelProperty(value = "充值手机号")
    private String mobile;
    /**账号*/
    @Excel(name = "充值账号", width = 15)
    @ApiModelProperty(value = "充值账号")
    private String account;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**子渠道号*/
	@Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;
	/**订单id*/
	@Excel(name = "订单id", width = 15)
    @ApiModelProperty(value = "订单id")
    private String orderId;
	/**商户订单号*/
	@Excel(name = "商户订单号", width = 15)
    @ApiModelProperty(value = "商户订单号")
    private String outTradeNo;
    /**订单金额*/
    @Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private Integer totalFee;
    /**支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中*/
    @Excel(name = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中", width = 15)
    @ApiModelProperty(value = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中")
    private Integer status;
    /**支付时间*/
    @Excel(name = "支付时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充*/
    @Excel(name = "充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充", width = 15)
    @ApiModelProperty(value = "充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充")
    private Integer rightsStatus;
    /**权益名称*/
    @Excel(name = "权益名称", width = 15)
    @ApiModelProperty(value = "权益名称")
    private String rightsName;
    /**创建时间*/
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;


	/**交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付*/
    @ApiModelProperty(value = "交易类型:ALIPAY=支付宝H5支付,JSAPI=微信公众号支付,MWEB=微信H5支付")
    private String tradeType;
	/**用户标识*/
    @ApiModelProperty(value = "用户标识")
    private String userId;



	/**退款订单号*/
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;
	/**退款备注*/
    @ApiModelProperty(value = "退款备注")
    private String refundRemark;
	/**退款时间*/

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退款时间")
    private Date refundTime;
	/**退款金额*/

    @ApiModelProperty(value = "退款金额")
    private Integer refundAmount;
	/**业务类型:shop=商城*/
    @ApiModelProperty(value = "业务类型:shop=商城")
    private String bizType;

	/**权益Id*/
    @ApiModelProperty(value = "权益Id")
    private String rightsId;
    /**充值couponId*/
    @ApiModelProperty(value = "充值couponId")
    private String couponId;
    /**省份*/
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
    /**城市*/
    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;
	/**备注*/
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
