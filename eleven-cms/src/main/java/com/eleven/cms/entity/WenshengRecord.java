package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: cms_wensheng_record
 * @Author: jeecg-boot
 * @Date:   2024-08-19
 * @Version: V1.0
 */
@Data
@TableName("cms_wensheng_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_wensheng_record对象", description="cms_wensheng_record")
public class WenshengRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**任务id*/
	@Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "任务id")
    private String taskId;
	/**视频文字*/
	@Excel(name = "视频文字", width = 15)
    @ApiModelProperty(value = "视频文字")
    private String prompt;
	/**视频时长*/
	@Excel(name = "视频时长", width = 15)
    @ApiModelProperty(value = "视频时长")
    private String duration;
	/**视频地址*/
	@Excel(name = "视频地址", width = 15)
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;
    /**视频地址*/
    @Excel(name = "封面图", width = 15)
    @ApiModelProperty(value = "封面图")
    private String cover;
	/**视频生成状态,0: 等待中，1: 完成，2: 处理中，3: 失败，4: 未通过审核*/
	@Excel(name = "视频生成状态,0: 等待中，1: 完成，2: 处理中，3: 失败，4: 未通过审核", width = 15)
    @ApiModelProperty(value = "视频生成状态,0: 等待中，1: 完成，2: 处理中，3: 失败，4: 未通过审核")
    private Integer videoStatus;
    /**审核表id*/
    @Excel(name = "审核表id", width = 15)
    @ApiModelProperty(value = "审核表id")
    private String vrbtDiyVideoId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
}
