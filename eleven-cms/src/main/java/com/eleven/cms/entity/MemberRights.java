package com.eleven.cms.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 视听会员权益
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Data
@DS("master")
@TableName("cms_member_rights")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_member_rights对象", description="视听会员权益")
public class MemberRights implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**权益Id*/
	@Excel(name = "权益Id", width = 15)
    @ApiModelProperty(value = "权益Id")
    private String rightsId;
	/**充值couponId*/
	@Excel(name = "产品编码", width = 15)
    @ApiModelProperty(value = "充值couponId")
    private String couponId;
	/**权益名称*/
	@Excel(name = "权益名称", width = 15)
    @ApiModelProperty(value = "权益名称")
    private String rightsName;
	/**产品价格（单位：分）*/
	@Excel(name = "批发价", width = 15)
    @ApiModelProperty(value = "产品价格（单位：分）")
    private Integer productPrice;

    /**是否输入账号:0=否,1=是*/
    @Excel(name = "是否输入账号:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否输入账号:0=否,1=是")
    private Integer isAccount;

    /**充值方式:0=直充,1=券码*/
    @Excel(name = "充值方式:0=直充,1=券码", width = 15)
    @ApiModelProperty(value = "充值方式:0=直充,1=券码")
    private Integer rechargeState;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**产品原价（单位：分）*/
    @Excel(name = "产品原价（单位：分）", width = 15)
    @ApiModelProperty(value = "产品原价（单位：分）")
    private Integer originalPrice;

    /**权益开关:0=关,1=开*/
    @Excel(name = "权益开关:0=关,1=开", width = 15)
    @ApiModelProperty(value = "权益开关:0=关,1=开")
    private Integer rightsSwitchs;


    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 权益月份
     */
    @TableField(exist = false)
    private String rightsMonth;



    /**归属公司:JUNBO=骏伯,HUAYI=华逸*/
    @Excel(name = "归属公司", width = 15, dicCode = "company_owner")
    @ApiModelProperty(value = "归属公司:JUNBO=骏伯,HUAYI=华逸")
    @Dict(dicCode = "company_owner")
    private String companyOwner;

}
