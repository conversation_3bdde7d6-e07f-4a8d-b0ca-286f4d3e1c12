package com.eleven.cms.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 栏目歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
@ApiModel(value="cms_column对象", description="栏目")
@Data
@TableName("cms_column_music")
public class ColumnMusic implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
	@ApiModelProperty(value = "主键")
	private java.lang.String id;
	/**栏目id*/
	@ApiModelProperty(value = "栏目id")
	private java.lang.String columnId;
	/**歌曲id*/
	@Excel(name = "歌曲id", width = 15, dictTable = "cms_music", dicText = "music_name", dicCode = "id")
	@ApiModelProperty(value = "歌曲id")
	private java.lang.String musicId;
	/**排序*/
	@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "排序")
	private java.lang.Integer priority;
	/**标题*/
	@Excel(name = "标题", width = 15)
	@ApiModelProperty(value = "标题")
	private java.lang.String title;
	/**标签*/
	@Excel(name = "标签", width = 15)
	@ApiModelProperty(value = "标签")
	private java.lang.String tag;
	/**扩展信息*/
	@Excel(name = "扩展信息", width = 15)
	@ApiModelProperty(value = "扩展信息")
	private java.lang.String extra;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**所属部门编码*/
	@ApiModelProperty(value = "所属部门编码")
	private java.lang.String sysOrgCode;
}
