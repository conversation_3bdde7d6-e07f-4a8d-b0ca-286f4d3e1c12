package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_migu_vrbt_pay_order
 * @Author: jeecg-boot
 * @Date:   2022-12-30
 * @Version: V1.0
 */
@Data
@TableName("cms_migu_vrbt_pay_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_migu_vrbt_pay_order对象", description="cms_migu_vrbt_pay_order")
public class MiguVrbtPayOrder implements Serializable {
    private static final long serialVersionUID = 1L;
	/**自增主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "自增主键")
    private String id;
    /**订单号*/
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**交易类型:ALIPAYH5=支付宝H5支付,MWEB=微信H5支付*/
    @Excel(name = "交易类型:ALIPAYH5=支付宝H5支付,MWEB=微信H5支付", width = 15)
    @ApiModelProperty(value = "交易类型:ALIPAYH5=支付宝H5支付,MWEB=微信H5支付")
    private String tradeType;
    /**appId*/
    @Excel(name = "appId", width = 15)
    @ApiModelProperty(value = "appId")
    private String appId;
    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String bizType;
    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    /**子渠道号*/
    @Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;
    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**商户订单号*/
    @Excel(name = "商户订单号", width = 15)
    @ApiModelProperty(value = "商户订单号")
    private String outTradeNo;
    /**用户标识*/
    @Excel(name = "用户标识", width = 15)
    @ApiModelProperty(value = "用户标识")
    private String userId;
    /**支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中*/
    @Excel(name = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中", width = 15)
    @ApiModelProperty(value = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中")
    private Integer orderStatus;
    /**订单金额*/
    @Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private String orderAmount;
    /**支付时间*/
    @Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**退款订单号*/
    @Excel(name = "退款订单号", width = 15)
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;
    /**退款备注*/
    @Excel(name = "退款备注", width = 15)
    @ApiModelProperty(value = "退款备注")
    private String refundRemark;
    /**退款金额*/
    @Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private String refundAmount;
    /**退款时间*/
    @Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "退款时间")
    private Date refundTime;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;




    //铃音类型:1=主叫,2=被叫,3=主被叫
    @Excel(name = "铃音类型:1=主叫,2=被叫,3=主被叫", width = 15)
    @ApiModelProperty(value = "铃音类型:1=主叫,2=被叫,3=主被叫")
    private Integer ringType;

    //铃音id或者内容Id
    @Excel(name = "铃音id或者内容Id", width = 15)
    @ApiModelProperty(value = "铃音id或者内容Id")
    private String ringId;
    /**铃音名称*/
    @Excel(name = "铃音名称", width = 15)
    @ApiModelProperty(value = "铃音名称")
    private String ringName;
    //版权id
    @Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private String copyRightId;



    //包月状态:-1=订购中,0=未订购,1=订购成功,2=订购失败
    @Excel(name = "包月状态:-1=订购中,0=未订购,1=订购成功,2=订购失败", width = 15)
    @ApiModelProperty(value = "包月状态:-1=订购中,0=未订购,1=订购成功,2=订购失败")
    private Integer monthStatus;


    //功能开通状态:-1=开通中,0=未开通,1=开通成功,2=开通失败
    @Excel(name = "功能开通状态:-1=开通中,0=未开通,1=开通成功,2=开通失败", width = 15)
    @ApiModelProperty(value = "功能开通状态:-1=开通中,0=未开通,1=开通成功,2=开通失败")
    private Integer subStatus;


}
