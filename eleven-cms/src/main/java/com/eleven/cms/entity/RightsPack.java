package com.eleven.cms.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 会员权益业务关联
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@ApiModel(value="cms_migu_pack对象", description="咪咕业务包")
@Data
@TableName("cms_rights_pack")
public class RightsPack implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
	@ApiModelProperty(value = "主键")
	private String id;
	/**咪咕业务包id*/
	@ApiModelProperty(value = "咪咕业务包id")
	private String packId;
	/**视听会员权益id*/
	@Excel(name = "视听会员权益id", width = 15)
	@ApiModelProperty(value = "视听会员权益id")
	private String memberId;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@ApiModelProperty(value = "备注")
	private String remark;
	/**排序*/
	@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "排序")
	private Integer priority;
	/**权益名称*/
	@Excel(name = "权益名称", width = 15)
	@ApiModelProperty(value = "权益名称")
	private String title;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**修改人*/
	@ApiModelProperty(value = "修改人")
	private String updateBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "修改时间")
	private Date updateTime;
	/**
	 * 权益名称
	 */
	@TableField(exist = false)
	private String rightsName;

	/**
	 * 权益Id
	 */
	@TableField(exist = false)
	private String rightsId;
	/**
	 * 产品价格（单位：分）
	 */
	@TableField(exist = false)
	private Integer productPrice;

	/**页数*/
	@TableField(exist = false)
	private Integer pageNo;
	/**大小*/
	@TableField(exist = false)
	private Integer pageSize;

}
