package com.eleven.cms.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 订购日志
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Data
//@DS("slave")
@TableName("cms_soap_feedback")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_soap_feedback对象", description="订购日志")
public class SoapFeedback implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**用户手机号码*/
	@Excel(name = "用户手机号码", width = 15)
    @ApiModelProperty(value = "用户手机号码")
    private String msisdn;
	/**平台接收时间戳，格式为：yyyymmddHHMMss*/
	@Excel(name = "平台接收时间戳，格式为：yyyymmddHHMMss", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "平台接收时间戳，格式为：yyyymmddHHMMss")
    private Date revDate;
	/**结果代码*/
	@Excel(name = "结果代码", width = 15)
    @ApiModelProperty(value = "结果代码")
    private String resCode;
	/**结果描述*/
	@Excel(name = "结果描述", width = 15)
    @ApiModelProperty(value = "结果描述")
    private String resDesc;
	/**订购信息,格式:订购关键字@渠道编码@渠道自用编码@歌曲ID@平台类型@价格@自定义流水号*/
	@Excel(name = "订购信息,格式:订购关键字@渠道编码@渠道自用编码@歌曲ID@平台类型@价格@自定义流水号", width = 15)
    @ApiModelProperty(value = "订购信息,格式:订购关键字@渠道编码@渠道自用编码@歌曲ID@平台类型@价格@自定义流水号")
    private String msg;
	/**订购关键字说明*/
	@Excel(name = "订购关键字说明", width = 15)
    @ApiModelProperty(value = "订购关键字说明")
    private String orderKey;
	/**渠道编码*/
	@Excel(name = "渠道编码", width = 15)
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
	/**渠道自用编码*/
	@Excel(name = "渠道自用编码", width = 15)
    @ApiModelProperty(value = "渠道自用编码")
    private String subChannel;
	/**业务id*/
	@Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private String serviceId;
	/**平台类型*/
	@Excel(name = "平台类型", width = 15)
    @ApiModelProperty(value = "平台类型")
    private String platform;
	/**价格*/
	@Excel(name = "价格", width = 15)
    @ApiModelProperty(value = "价格")
    private String price;
	/**自定义流水号*/
	@Excel(name = "自定义流水号", width = 15)
    @ApiModelProperty(value = "自定义流水号")
    private String serialNum;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门编码*/
    @ApiModelProperty(value = "所属部门编码")
    private String sysOrgCode;
}
