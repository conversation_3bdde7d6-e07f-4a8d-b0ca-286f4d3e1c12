package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 影楼用户表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
@Data
@TableName("cms_yinglou_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_yinglou_user对象", description="影楼用户表")
public class YinglouUser implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**主账号*/
    @Excel(name = "主账号", width = 15)
    @ApiModelProperty(value = "主账号")
    private String majorUser;
	/**登录账号*/
	@Excel(name = "登录账号", width = 15)
    @ApiModelProperty(value = "登录账号")
    private String userName;


    /**用户编号*/
    @Excel(name = "用户编号", width = 15)
    @ApiModelProperty(value = "用户编号")
    private String numberId;
    /**登录账号*/
    @Excel(name = "用户别名", width = 15)
    @ApiModelProperty(value = "用户别名")
    private String userAlias;

	/**密码*/
	@Excel(name = "密码", width = 15)
    @ApiModelProperty(value = "密码")
    private String passWord;
	/**用户等级 0 主账号 1 次级账号*/
    @Excel(name = "用户等级", width = 15,dicCode = "level")
    @ApiModelProperty(value = "用户等级")
    @Dict(dicCode = "level")
    private Integer levels;
	/**分成比例*/
	@Excel(name = "分成比例", width = 15)
    @ApiModelProperty(value = "分成比例")
    private Integer divideIntoScale;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
