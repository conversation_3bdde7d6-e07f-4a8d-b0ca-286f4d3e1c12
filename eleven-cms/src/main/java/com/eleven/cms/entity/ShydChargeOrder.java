package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 上海移动办理订单,收到办理成功的通知后创建 Entity
 *
 * <AUTHOR>
 * @date 2021-04-01 10:41:00
 */
@Data
@TableName("cms_shyd_charge_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_shyd_charge_order对象", description="上海移动办理订单,收到办理成功的通知后创建")
public class ShydChargeOrder implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一，支付订单号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 办理5G特惠包手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 办理状态:0,未办理;1,办理成功;2,办理失败;3,发送验证码成功;4,发送验证码失败
     */
    @TableField("charge_status")
    private Integer chargeStatus;

    /**
     * 办理尝试次数
     */
    @TableField("charge_count")
    private Integer chargeCount;

    /**
     * 下次办理时间(重试时间)
     */
    @TableField("charge_retry_time")
    private LocalDateTime chargeRetryTime;

    /**
     * 价格，单位：分
     */
    @TableField("price")
    private Integer price;

    /**
     * 5G特惠包资费ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 限时特惠活动资费ID
     */
    @TableField("activity_id")
    private String activityId;

    /**
     * 白名单ID
     */
    @TableField("open_id")
    private String openId;

    /**
     * 上海移动5G特惠包产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 办理状态:0,未办理;1,办理成功;2,办理失败;3,发送验证码成功;4,发送验证码失败
     */
    @TableField("callback_status")
    private Integer callbackStatus;

    /**
     * 结果办理的响应消息
     */
    @TableField("callback_resp")
    private String callbackResp;

    /**
     * 备注(类型:1=流量包,2=抖音视频彩铃,3=订阅包)
     */
    @TableField("remark")
    private String remark;
    /**
     * 是否拥有权益:0=否,1=是
     */
    @TableField("is_right")
    private Integer isRight;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private LocalDateTime modifyTime;

}
