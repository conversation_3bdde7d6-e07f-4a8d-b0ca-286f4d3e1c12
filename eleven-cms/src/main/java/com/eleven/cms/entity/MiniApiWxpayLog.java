package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_miniApi_wxpay_log
 * @Author: jeecg-boot
 * @Date:   2022-05-25
 * @Version: V1.0
 */
@Data
@TableName("cms_miniApi_wxpay_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_miniApi_wxpay_log对象", description="cms_miniApi_wxpay_log")
public class MiniApiWxpayLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String outTradeNo;
	/**小程序APPID*/
	@Excel(name = "小程序APPID", width = 15)
    @ApiModelProperty(value = "小程序APPID")
    private String appid;
	/**支付价格。单位为[分]*/
	@Excel(name = "支付价格。单位为[分]", width = 15)
    @ApiModelProperty(value = "支付价格。单位为[分]")
    private String totalAmount;
	/**商品描述*/
	@Excel(name = "商品描述", width = 15)
    @ApiModelProperty(value = "商品描述")
    private String subject;
	/**商品详情*/
	@Excel(name = "商品详情", width = 15)
    @ApiModelProperty(value = "商品详情")
    private String body;
	/**通知地址*/
	@Excel(name = "通知地址", width = 15)
    @ApiModelProperty(value = "通知地址")
    private String notifyurl;
	/**签名*/
	@Excel(name = "签名", width = 15)
    @ApiModelProperty(value = "签名")
    private String sign;
	/**小程序支付接口返回的order_id*/
	@Excel(name = "小程序支付接口返回的order_id", width = 15)
    @ApiModelProperty(value = "小程序支付接口返回的order_id")
    private String orderId;
	/**小程序支付接口返回的order_token*/
	@Excel(name = "小程序支付接口返回的order_token", width = 15)
    @ApiModelProperty(value = "小程序支付接口返回的order_token")
    private String orderToken;
    /**支付状态 0-未支付，1-已支付*/
    @Excel(name = "支付状态，0：未支付 1：支付成功", width = 15)
    @ApiModelProperty(value = "支付状态，0：未支付 1：支付成功")
    private Integer payStatus;
    /**支付渠道侧单号*/
    @Excel(name = "支付渠道侧单号", width = 15)
    @ApiModelProperty(value = "支付渠道侧单号")
    String channelNo;
    /**支付渠道侧商家订单号*/
    @Excel(name = "支付渠道侧商家订单号", width = 15)
    @ApiModelProperty(value = "支付渠道侧商家订单号")
    String paymentOrderNo;
    /**该笔交易卖家商户号*/
    @Excel(name = "该笔交易卖家商户号", width = 15)
    @ApiModelProperty(value = "该笔交易卖家商户号")
    String sellerUid;
    /**支付时间，Unix 时间戳，10 位，整型数*/
    @Excel(name = "支付时间", width = 15)
    @ApiModelProperty(value = "支付时间")
    Integer paidAt;

    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    String mobile;

    /**权益产品id*/
    @Excel(name = "权益产品id", width = 15)
    @ApiModelProperty(value = "权益产品id")
    String couponId;

    /**QQ号*/
    @Excel(name = "QQ号", width = 15)
    @ApiModelProperty(value = "QQ号")
    String account;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
