package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_channel
 * @Author: jeecg-boot
 * @Date:   2020-10-23
 * @Version: V1.0
 */
@Data
@TableName("cms_channel")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_channel对象", description="cms_channel")
public class Channel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String title;
	/**介绍*/
	@Excel(name = "介绍", width = 15)
    @ApiModelProperty(value = "介绍")
    private java.lang.String intro;
	/**状态*/
	@Excel(name = "状态,1=有效,0=无效", width = 15)
    @ApiModelProperty(value = "状态,1=有效,0=无效")
    private java.lang.Integer status;
	/**是否开启短信验证*/
	@Excel(name = "是否开启短信验证", width = 15)
    @ApiModelProperty(value = "是否开启短信验证")
    private java.lang.Integer validateStatus;

    /**广告api的spring bean名称*/
    @Excel(name = "广告api的spring bean名称", width = 15)
    @ApiModelProperty(value = "广告api的spring bean名称")
    private java.lang.String adApiBeanName;
    /**广告id参数名称*/
    @Excel(name = "广告id参数名称", width = 15)
    @ApiModelProperty(value = "广告id参数名称")
    private java.lang.String adIdParamName;
    /**前端需要加载的js*/
    @Excel(name = "前端需要加载的js", width = 15)
    @ApiModelProperty(value = "前端需要加载的js")
    private java.lang.String adJsUrl;
    /**广告id参数名称*/
    @Excel(name = "前端订阅成功后需要回调上报的js代码", width = 15)
    @ApiModelProperty(value = "前端订阅成功后需要回调上报的js代码")
    private java.lang.String adJsEval;

    /**api地址*/
    @Excel(name = "api地址", width = 15)
    @ApiModelProperty(value = "api地址")
    private java.lang.String apiUrl;
    /**api密钥*/
    @Excel(name = "api密钥", width = 15)
    @ApiModelProperty(value = "api密钥")
    private java.lang.String apiKey;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;

    /*平台id*/
    private String adPlatformId;

    /*账号id*/
    private String accountId;

    /*平台*/
    @TableField(exist = false)
    private String adPlatform;

    /*账号*/
    @TableField(exist = false)
    private String account;

    /*推广链接*/
    private String spreadUrl;
}
