package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/20 11:18
 **/
@ApiModel(value="cms_column对象", description="订阅包栏目")
@Data
@TableName("cms_column_music_dy")
public class ColumnMusicDyVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**栏目id*/
    @ApiModelProperty(value = "栏目id")
    private String columnId;
    /**歌曲id*/
    @ApiModelProperty(value = "歌曲id")
    private String musicId;
    @Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private String copyrightId;
    /**排序*/
    @ApiModelProperty(value = "排序")
    private Integer priority;
    /**标题*/
    @Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private String title;
    /**标签*/
    @Excel(name = "标签", width = 15)
    @ApiModelProperty(value = "标签")
    private String tag;
    /**扩展信息*/
    @Excel(name = "扩展信息", width = 15)
    @ApiModelProperty(value = "扩展信息")
    private String extra;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**所属部门编码*/
    @ApiModelProperty(value = "所属部门编码")
    private String sysOrgCode;
}
