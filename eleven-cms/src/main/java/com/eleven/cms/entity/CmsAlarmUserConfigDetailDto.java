package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eleven.cms.util.BizConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 告警人详情
 * @Author: jeecg-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
@Data
public class CmsAlarmUserConfigDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String phone;
    /**微信公众号openId*/
    @Excel(name = "微信公众号openId", width = 15)
    @ApiModelProperty(value = "微信公众号openId")
    private String openId;

	/**开启手机短信通知，默认开启 0:关闭 1:开启*/
    @ApiModelProperty(value = "开启手机短信通知，默认开启 0:关闭 1:开启")
//    @Dict(dicCode = "enable_sms")
    private String enableSms;
	/**开启微信公众号通知，默认开启 0:关闭 1:开启*/
    @ApiModelProperty(value = "开启微信公众号通知，默认开启 0:关闭 1:开启")
//    @Dict(dicCode = "enable_vx")
    private String enableVx;



}
