package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_pptv_give_log
 * @Author: jeecg-boot
 * @Date:   2021-06-10
 * @Version: V1.0
 */
@Data
@TableName("cms_pptv_give_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_pptv_give_log对象", description="cms_pptv_give_log")
@AllArgsConstructor
@NoArgsConstructor
public class PptvGiveLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderId;
	/**充值账号*/
	@Excel(name = "充值账号", width = 15)
    @ApiModelProperty(value = "充值账号")
    private String account;
	/**通知手机号*/
	@Excel(name = "通知手机号", width = 15)
    @ApiModelProperty(value = "通知手机号")
    private String mobile;
	/**赠送状态:0=赠送中,1=赠送成功,2=赠送失败*/
	@Excel(name = "赠送状态:0=赠送中,1=赠送成功,2=赠送失败", width = 15)
    @ApiModelProperty(value = "赠送状态:0=赠送中,1=赠送成功,2=赠送失败")
    private Integer status;
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private String productName;
	/**结果状态码*/
	@Excel(name = "结果状态码", width = 15)
    @ApiModelProperty(value = "结果状态码")
    private String respCode;
	/**结果状态描述*/
	@Excel(name = "结果状态描述", width = 15)
    @ApiModelProperty(value = "结果状态描述")
    private String respMsg;

    /**短信发送状态:0=未发送,1=已发送*/
    @Excel(name = "短信发送状态:0=未发送,1=已发送", width = 15)
    @ApiModelProperty(value = "短信发送状态:0=未发送,1=已发送")
    private Integer sendSmsState;

    /**短信发送时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "短信发送时间")
    private Date sendSmsTime;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**业务Id*/
    @Excel(name = "业务Id", width = 15)
    @ApiModelProperty(value = "业务Id")
    private String serviceId;

    /**发送月份*/
    @Excel(name = "发送月份", width = 15)
    @ApiModelProperty(value = "发送月份")
    private String pptvMonth;

	/**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;


    //提交时间
    @TableField(exist = false)
    private String submitTime;
}
