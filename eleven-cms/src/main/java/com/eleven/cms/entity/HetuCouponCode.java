package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_hetu_coupon_code
 * @Author: jeecg-boot
 * @Date:   2022-03-22
 * @Version: V1.0
 */
@ApiModel(value="cms_hetu_coupon_code对象", description="券码")
@Data
@TableName("cms_hetu_coupon_code")
public class HetuCouponCode implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**兑换码*/
	@Excel(name = "兑换码", width = 15)
    @ApiModelProperty(value = "兑换码")
    private String couponCode;



    /**订单号*/
    @ApiModelProperty(value = "订单号")
    private String orderId;
    /**权益Id*/
    @Excel(name = "权益Id", width = 15, dicCode = "rights_id")
    @ApiModelProperty(value = "权益Id")
    @Dict(dicCode = "rights_id")
    private String rightsId;


	/**激活码状态:0=未使用,1=已使用,2=已失效*/
    @ApiModelProperty(value = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中")
    private Integer status;
    /**发码时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发码时间")
    private Date sendTime;

    /**过期时间*/
    @Excel(name = "过期时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date invalidTime;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;



    /**
     * 权益Id
     */
    @TableField(exist = false)
    private String rightsCode;
    /**
     * 数量
     */
    @TableField(exist = false)
    private String rightsCount;

    /**扩展字段（咪咕互娱透传用）*/
    @Excel(name = "扩展字段（咪咕互娱透传用）", width = 15)
    @ApiModelProperty(value = "扩展字段（咪咕互娱透传用）")
    private String extrInfo;

    /**区服*/
    @Excel(name = "区服", width = 15)
    @ApiModelProperty(value = "区服")
    private String sendServer;


    /**角色*/
    @Excel(name = "角色", width = 15)
    @ApiModelProperty(value = "角色")
    private String sendRole;


    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

}
