package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_common_coupon
 * @Author: jeecg-boot
 * @Date:   2024-07-04
 * @Version: V1.0
 */
@Data
@TableName("cms_common_coupon")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_common_coupon对象", description="cms_common_coupon")
public class CommonCoupon implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**兑换码*/
	@Excel(name = "兑换码", width = 15)
    @ApiModelProperty(value = "兑换码")
    private String couponCode;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    /**移动订单号*/
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "移动订单号")
    private String orderId;
    /**激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中*/
    @Excel(name = "激活码状态", width = 15, dicCode = "code_status")
    @ApiModelProperty(value = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中")
    private Integer status;
    /**用户兑换具体权益名称*/
    @Excel(name = "用户兑换具体权益名称", width = 15)
    @ApiModelProperty(value = "用户兑换具体权益名称")
    private String exchangeRightsName;
    /**发码时间*/
    @Excel(name = "发码时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发码时间")
    private Date exchangeTime;

    /**券码过期时间*/
    @Excel(name = "券码过期时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "券码过期时间")
    private Date invalidTime;

    /**订购状态:0=未订购,1=首月订购,2=续订*/
    @Excel(name = "订购状态", width = 15, dicCode = "order_status")
    @ApiModelProperty(value = "订购状态:0=未订购,1=首月订购,2=续订")
    private Integer nextMonthSend;

    /**事务跟踪id，每次请求保证唯一性*/
    @Excel(name = "事务跟踪id", width = 15)
    @ApiModelProperty(value = "事务跟踪id，每次请求保证唯一性")
    private String dataLinkId;
    /**扩展字段*/
    @Excel(name = "扩展字段", width = 15)
    @ApiModelProperty(value = "扩展字段")
    private String extra;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**创建人*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @Excel(name = "券码过期时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

	/**关联权益的业务Id*/
    @ApiModelProperty(value = "关联权益的业务Id")
    private String serviceId;
    /**回调通知地址*/
    @ApiModelProperty(value = "回调通知地址")
    private String notifyUrl;
    /**数量*/
    @ApiModelProperty(value = "数量")
    private Integer num;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

}
