package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 落地页业务省份配置弹窗版 Entity
 *
 * <AUTHOR>
 * @date 2024-07-09 17:03:02
 */
@Data
@TableName("xxl_page_business_province_config_self")
public class PageBusinessProvinceConfigSelf {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 落地页id
     */
    @TableField("page_id")
    private String pageId;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 移动渠道号
     */
    @TableField("yidong_channel")
    private String yidongChannel;

    /**
     * 移动弹窗
     */
    @TableField("yidong_pop")
    private String yidongPop;

    /**
     * 移动尾量
     */
    @TableField("yidong_tail_quantity")
    private String yidongTailQuantity;

    /**
     * 移动规则
     */
    @TableField("yidong_rule")
    private String yidongRule;

    /**
     * 移动备用渠道号
     */
    @TableField("yidong_spare_channel")
    private String yidongSpareChannel;

    /**
     * 移动备用弹窗
     */
    @TableField("yidong_spare_pop")
    private String yidongSparePop;

    /**
     * 移动备用尾量
     */
    @TableField("yidong_spare_tail_quantity")
    private String yidongSpareTailQuantity;

    /**
     * 联通备用规则
     */
    @TableField("yidong_spare_rule")
    private String yidongSpareRule;

    /**
     * 联通渠道号
     */
    @TableField("liantong_channel")
    private String liantongChannel;

    /**
     * 联通弹窗
     */
    @TableField("liantong_pop")
    private String liantongPop;

    /**
     * 联通尾量
     */
    @TableField("liantong_tail_quantity")
    private String liantongTailQuantity;

    /**
     * 联通规则
     */
    @TableField("liantong_rule")
    private String liantongRule;

    /**
     * 电信渠道号
     */
    @TableField("dianxin_channel")
    private String dianxinChannel;

    /**
     * 电信弹窗
     */
    @TableField("dianxin_pop")
    private String dianxinPop;

    /**
     * 电信尾量
     */
    @TableField("dianxin_tail_quantity")
    private String dianxinTailQuantity;

    /**
     * 电信规则
     */
    @TableField("dianxin_rule")
    private String dianxinRule;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态:1=开启,0=关闭
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 移动业务id
     */
    @TableField("yidong_biz_type_id")
    private String yidongBizTypeId;

    /**
     * 移动备用业务id
     */
    @TableField("yidong_spare_biz_type_id")
    private String yidongSpareBizTypeId;

    /**
     * 联通业务id
     */
    @TableField("liantong_biz_type_id")
    private String liantongBizTypeId;

    /**
     * 电信业务id
     */
    @TableField("dianxin_biz_type_id")
    private String dianxinBizTypeId;

    /**
     *
     */
    @TableField("yidong_ref_page")
    private String yidongRefPage;

    /**
     *
     */
    @TableField("yidong_spare_ref_page")
    private String yidongSpareRefPage;

    /**
     *
     */
    @TableField("liantong_ref_page")
    private String liantongRefPage;

    /**
     *
     */
    @TableField("dianxin_ref_page")
    private String dianxinRefPage;

    /**
     * 创建日期起
     */
    @TableField(exist = false)
    private String createTimeFrom;

    /**
     * 创建日期止
     */
    @TableField(exist = false)
    private String createTimeTo;


}
