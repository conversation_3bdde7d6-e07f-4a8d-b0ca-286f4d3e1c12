package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_sms_validate_log
 * @Author: jeecg-boot
 * @Date:   2020-10-26
 * @Version: V1.0
 */
@Data
@TableName("cms_sms_validate_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_sms_validate_log对象", description="cms_sms_validate_log")
public class SmsValidateLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
	/**用户ip*/
	@Excel(name = "用户ip", width = 15)
    @ApiModelProperty(value = "用户ip")
    private java.lang.String ip;
	/**操作:0=发送,1=验证通过*/
	@Excel(name = "操作:0=发送,1=验证通过", width = 15)
    @ApiModelProperty(value = "操作:0=发送,1=验证通过")
    private java.lang.Integer action;
	/**页面*/
	@Excel(name = "页面", width = 15)
    @ApiModelProperty(value = "页面")
    private java.lang.String page;
	/**推广渠道*/
	@Excel(name = "推广渠道", width = 15)
    @ApiModelProperty(value = "推广渠道")
    private java.lang.String subChannel;
	/**业务类型*/
	@Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private java.lang.String bizType;
	/**咪咕渠道*/
	@Excel(name = "咪咕渠道", width = 15)
    @ApiModelProperty(value = "咪咕渠道")
    private java.lang.String channel;
	/**业务id*/
	@Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private java.lang.String serviceId;
	/**版权id*/
	@Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private java.lang.String copyrightId;
	/**额外信息*/
	@Excel(name = "额外信息", width = 15)
    @ApiModelProperty(value = "额外信息")
    private java.lang.String extra;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
}
