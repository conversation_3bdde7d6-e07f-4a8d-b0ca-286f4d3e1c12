package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_wo_read_order
 * @Author: jeecg-boot
 * @Date:   2023-05-29
 * @Version: V1.0
 */
@Data
@TableName("cms_wo_read_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_wo_read_order对象", description="cms_wo_read_order")
public class WoReadOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**自增主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "自增主键")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;
	/**业务类型*/
	@Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;
	/**业务名称*/
	@Excel(name = "业务名称", width = 15)
    @ApiModelProperty(value = "业务名称")
    private String businessName;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**子渠道号*/
	@Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;
	/**支付类型,1=微信,2=支付宝*/
	@Excel(name = "支付类型,1=微信,2=支付宝,3=话费", width = 15)
    @ApiModelProperty(value = "支付类型,1=微信,2=支付宝,3=话费")
    private Integer payType;
	/**我方订单号*/
	@Excel(name = "我方订单号", width = 15)
    @ApiModelProperty(value = "我方订单号")
    private String orderNo;
	/**外部订单号,微信out_trade_no,支付宝orderid*/
	@Excel(name = "外部订单号,微信out_trade_no,支付宝orderid", width = 15)
    @ApiModelProperty(value = "外部订单号,微信out_trade_no,支付宝orderid")
    private String outTradeNo;
	/**订单状态(0:未支付 1:成功,2:失败)*/
	@Excel(name = "订单状态(0:未支付 1:成功,2:失败)", width = 15)
    @ApiModelProperty(value = "订单状态(0:未支付 1:成功,2:失败)")
    private Integer orderStatus;
	/**订单金额*/
	@Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private String orderAmount;
	/**订单时间*/
	@Excel(name = "订单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单时间")
    private Date orderTime;
	/**支付时间*/
	@Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
	/**支付月份*/
	@Excel(name = "支付月份", width = 15)
    @ApiModelProperty(value = "支付月份")
    private String payMonth;
	/**第几次支付*/
	@Excel(name = "第几次支付", width = 15)
    @ApiModelProperty(value = "第几次支付")
    private Integer payNo;
	/**订购状态,2正常,3已退订*/
	@Excel(name = "订购状态,2正常,3已退订", width = 15)
    @ApiModelProperty(value = "订购状态,2正常,3已退订")
    private Integer subStatus;
	/**退款订单号*/
	@Excel(name = "退款订单号", width = 15)
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;
	/**退款备注*/
	@Excel(name = "退款备注", width = 15)
    @ApiModelProperty(value = "退款备注")
    private String refundRemark;
	/**退款金额*/
	@Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private String refundAmount;
	/**退款时间*/
	@Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退款时间")
    private Date refundTime;
	/**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
	@Excel(name = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败", width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private Integer refundStatus;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**支付时间*/
    @Excel(name = "解约时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "解约时间")
    private Date unSignTime;
    /**渠道id*/
    @Excel(name = "渠道id", width = 15)
    @ApiModelProperty(value = "渠道id")
    private String channelId;

    /**业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃*/
    @Excel(name = "业务类型", width = 15, dicCode = "biz_type")
    @ApiModelProperty(value = "业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃,MEMBER_MOBILE_PAY=电信视频彩铃")
    @Dict(dicCode = "biz_type")
    private String bizType;
}
