package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_notify_xwfcs
 * @Author: jeecg-boot
 * @Date:   2025-02-12
 * @Version: V1.0
 */
@Data
@TableName("cms_notify_xwfcs")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_notify_xwfcs对象", description="cms_notify_xwfcs")
public class CmsNotifyXwfcs implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**运营商*/
	@Excel(name = "运营商", width = 15)
    @ApiModelProperty(value = "运营商")
    private String isp;
	/**下单时间*/
	@Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下单时间")
    private Date ordertime;
	/**手机号（base64，若流量主调用时有手机号，则回调时会带入）*/
	@Excel(name = "手机号（base64，若流量主调用时有手机号，则回调时会带入）", width = 15)
    @ApiModelProperty(value = "手机号（base64，若流量主调用时有手机号，则回调时会带入）")
    private String phone;
    @Excel(name = "解密的手机号", width = 15)
    @ApiModelProperty(value = "解密的手机号")
    private String vPhone;
	/**点位*/
	@Excel(name = "点位", width = 15)
    @ApiModelProperty(value = "点位")
    private String pointnum;
	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
	/**用户会话ID（流量主调用推荐时传递的值）*/
	@Excel(name = "用户会话ID（流量主调用推荐时传递的值）", width = 15)
    @ApiModelProperty(value = "用户会话ID（流量主调用推荐时传递的值）")
    private String sessionid;
	/**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
