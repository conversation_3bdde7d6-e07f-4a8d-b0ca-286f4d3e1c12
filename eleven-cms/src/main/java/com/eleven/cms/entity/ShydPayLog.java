package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 上海移动5G特惠包办理日志 Entity
 *
 * <AUTHOR>
 * @date 2021-04-01 10:41:24
 */
@Data
@TableName("cms_shyd_pay_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_shyd_pay_log对象", description="上海移动5G特惠包办理日志")
public class ShydPayLog implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    @TableField("out_trade_no")
    private String outTradeNo;

    /**
     * 办理5G特惠包手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 支付渠道:PHONEBILL
     */
    @TableField("pay_way")
    private String payWay;

    /**
     * 购买商品id:套餐5GB+10GB=MEAL_DISCOUNT_FIVE_TEN_GB,套餐10GB+10G=MEAL_DISCOUNT_TEN_TEN_GB,套餐5GB+5GB=MEAL_DISCOUNT_FIVE_FIVE_GB,套餐10GB+5GB=MEAL_DISCOUNT_TEN_FIVE_GB
     */
    @TableField("buy_goods_id")
    private String buyGoodsId;

    /**
     * 价格,单位分
     */
    @TableField("price")
    private String price;

    /**
     * 商品的标题
     */
    @TableField("subject")
    private String subject;

    /**
     * 办理状态:0,初始状态已下单;1,办理成功;2,办理失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 流程:    0,初始状态已下单;1,白名单查询成功;2,白名单查询失败;3,业务鉴权成功;4,业务鉴权失败;5,用户业务受理成功;6,用户业务受理失败
     */
    @TableField("flow")
    private Integer flow;

    /**
     * 错误码（0000表示通过鉴权，其他表示不通过）
     */
    @TableField("code")
    private String code;

    /**
     * 具体失败原因
     */
    @TableField("message")
    private String message;

    /**
     * 返回结果
     */
    @TableField("ret_info")
    private String retInfo;

    /**
     * 备注(类型:1=移动特惠包,2=彩铃PLUS)
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否拥有权益:0=否,1=是
     */
    @TableField("is_right")
    private Integer isRight;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

}
