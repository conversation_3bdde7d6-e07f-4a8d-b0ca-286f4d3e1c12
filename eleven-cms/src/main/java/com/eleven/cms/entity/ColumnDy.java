package com.eleven.cms.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 订阅包栏目
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
@ApiModel(value="cms_column_dy对象", description="订阅包栏目")
@Data
@TableName("cms_column_dy")
public class ColumnDy implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String title;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer priority;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;
	/**渠道*/
	@Excel(name = "渠道", width = 15)
    @ApiModelProperty(value = "渠道")
    private String channel;
	/**栏目分类名称*/
    @Excel(name = "栏目分类名称", width = 15)
    @Dict(dicCode = "column_class_dy")
    @ApiModelProperty(value = "栏目分类名称")
    private String columnClassName;
	/**是否轮播：0=否，1=是*/
	@Excel(name = "是否轮播：0=否，1=是", width = 15)
    @ApiModelProperty(value = "是否轮播：0=否，1=是")
    private String isCarousel;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门编码*/
    @ApiModelProperty(value = "所属部门编码")
    private String sysOrgCode;
	/**图片地址*/
	@Excel(name = "图片地址", width = 15)
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;
	/**视频地址*/
	@Excel(name = "视频地址", width = 15)
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;
	/**背景颜色*/
	@Excel(name = "背景颜色", width = 15)
    @ApiModelProperty(value = "背景颜色")
    private String backdropColor;
	/**文字介绍*/
	@Excel(name = "文字介绍", width = 15)
    @ApiModelProperty(value = "文字介绍")
    private String textIntroduction;
	/**文字颜色*/
	@Excel(name = "文字颜色", width = 15)
    @ApiModelProperty(value = "文字颜色")
    private String textColor;
}
