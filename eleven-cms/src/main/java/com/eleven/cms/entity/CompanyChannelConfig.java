package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_company_channel_config
 * @Author: jeecg-boot
 * @Date:   2023-06-09
 * @Version: V1.0
 */
@Data
@TableName("cms_company_channel_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_company_channel_config对象", description="cms_company_channel_config")
public class CompanyChannelConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**落地页*/
	@Excel(name = "落地页", width = 15)
    @ApiModelProperty(value = "落地页")
    private String landingPage;
	/**配置key*/
	@Excel(name = "配置key", width = 15)
    @ApiModelProperty(value = "配置key")
    private String configKey;
	/**公司*/
	@Excel(name = "公司", width = 15)
    @ApiModelProperty(value = "公司")
    private String company;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**是否破解，1=破解，0=不破解*/
	@Excel(name = "是否需要二确弹窗", width = 15)
    @ApiModelProperty(value = "是否需要二确弹窗，1=破解，0=不破解")
    private Integer cracking;
    /**尾量，1=流量果,2=增收包,3=发财树*/
    @Excel(name = "尾量", width = 15)
    @ApiModelProperty(value = "尾量，1=流量果,2=增收包,3=发财树")
    private Integer tailQuantity;
    /**小程序重定向地址*/
    @Excel(name = "小程序重定向地址", width = 15)
    @ApiModelProperty(value = "小程序重定向地址")
    private String redirectUrl;
    /**三方支付渠道号*/
    @Excel(name = "三方支付渠道号", width = 15)
    @ApiModelProperty(value = "三方支付渠道号")
    private String businessType;
    /**优先企业彩铃 1优先企业彩铃 0优先组合包*/
    @Excel(name = "优先企业彩铃", width = 15)
    @ApiModelProperty(value = "优先企业彩铃")
    private Integer qyclFirst;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
