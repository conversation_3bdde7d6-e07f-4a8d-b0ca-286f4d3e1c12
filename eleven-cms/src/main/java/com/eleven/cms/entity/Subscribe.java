package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import static com.eleven.cms.util.BizConstant.BIZ_LT_CHANNEL_DEFAULT;

/**
 * @Description: 渠道订阅
 * @Author: jeecg-boot
 * @Date:   2020-09-23
 * @Version: V1.0
 */
@Data
@TableName("cms_subscribe")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_subscribe对象", description="渠道订阅")
@AllArgsConstructor
@NoArgsConstructor
public class Subscribe implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
    /**运营商*/
    @Excel(name = "运营商", width = 15,dicCode = "isp_code")
    @ApiModelProperty(value = "运营商")
    @Dict(dicCode = "isp_code")
    private java.lang.String isp;
    /**业务类型,vrbt:视频彩铃,rt:振铃*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型,vrbt:视频彩铃,rt:振铃")
    private java.lang.String bizType;
    /**省份*/
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private java.lang.String province;
    /**城市*/
    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private java.lang.String city;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private java.lang.String channel;
	/**子渠道号*/
	@Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private java.lang.String subChannel;
    /**子渠道号*/
    @Excel(name = "渠道归属", width = 15)
    @ApiModelProperty(value = "渠道归属")
    private java.lang.String owner;
	/**咪咕serviceId*/
	//@Excel(name = "咪咕serviceId", width = 15)
    @ApiModelProperty(value = "咪咕serviceId")
    private java.lang.String serviceId;
	/**版权id*/
	@Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private java.lang.String copyrightId;
	/**内容id*/
	//@Excel(name = "内容id", width = 15)
    @ApiModelProperty(value = "内容id")
    private java.lang.String contentId;
    /**电信铃音编码*/
    //@Excel(name = "电信铃音编码", width = 15)
    @ApiModelProperty(value = "电信铃音编码")
    private java.lang.String dxToneCode;
    /**联通铃音id*/
    //@Excel(name = "联通铃音id", width = 15)
    @ApiModelProperty(value = "联通铃音id")
    private java.lang.String ltRingId;
    /**运营商一键下单订单号*/
    //@Excel(name = "营商一键下单订单号", width = 15)
    @ApiModelProperty(value = "运营商一键下单订单号")
    private java.lang.String ispOrderNo;
	/**状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)*/
	@Excel(name = "开通状态", width = 15,dicCode = "subscribe_status")
    @ApiModelProperty(value = "状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)")
    @Dict(dicCode = "subscribe_status")
    private java.lang.Integer status;
	/**订购结果*/
	@Excel(name = "订购结果", width = 40)
    @ApiModelProperty(value = "订购结果")
    private java.lang.String result;
    /**校验包月状态(延迟60分钟)*/
    @Excel(name = "60分钟退订校验", width = 15, dicCode = "verify_status")
    @ApiModelProperty(value = "60分钟退订校验(-1=未校验,0=未包月,1=已包月)")
    @Dict(dicCode = "verify_status")
    private java.lang.Integer verifyStatus;
    /**校验包月状态(延迟903天)*/
    @Excel(name = "3天退订校验", width = 15, dicCode = "verify_status")
    @ApiModelProperty(value = "3天退订校验(-1=未校验,0=未包月,1=已包月)")
    @Dict(dicCode = "verify_status")
    private java.lang.Integer verifyStatusDaily;
    /**免费订铃声的结果*/
    @Excel(name = "彩铃免费订购结果", width = 40)
    @ApiModelProperty(value = "彩铃免费订购结果")
    private java.lang.String extra;
	/**推送至即时开通服务器返回编码*/
	//@Excel(name = "推送至即时开通服务器返回编码", width = 15)
    @ApiModelProperty(value = "推送至即时开通服务器返回编码")
    private java.lang.Integer pushRespCode;
	/**推送至即时开通服务器返回消息*/
	//@Excel(name = "推送至即时开通服务器返回消息", width = 15)
    @ApiModelProperty(value = "推送至即时开通服务器返回消息")
    private java.lang.String pushRespMessage;
	/**6个月包月校验:-1=未校验,0=未包月,1=已包月*/
	//@Excel(name = "6个月包月校验:-1=未校验,0=未包月,1=已包月", width = 15)
    @ApiModelProperty(value = "6个月包月校验:-1=未校验,0=未包月,1=已包月")
    private java.lang.Integer price;

	/**省份编码*/
	//@Excel(name = "省份编码", width = 15)
    @ApiModelProperty(value = "省份编码")
    private java.lang.String provinceCode;
	/**城市编码*/
	//@Excel(name = "城市编码", width = 15)
    @ApiModelProperty(value = "城市编码")
    private java.lang.String cityCode;
	/**浏览器ua*/
	//@Excel(name = "浏览器ua", width = 15)
    @ApiModelProperty(value = "浏览器ua")
    private java.lang.String userAgent;
	/**app包名*/
	@Excel(name = "app包名", width = 20)
    @ApiModelProperty(value = "app包名")
    private java.lang.String referer;
	/**设备信息*/
	@Excel(name = "推广ID", width = 20)
    @ApiModelProperty(value = "推广ID")
    private java.lang.String deviceInfo;
	/**ip*/
	@Excel(name = "ip", width = 20)
    @ApiModelProperty(value = "ip")
    private java.lang.String ip;
    /**信息流转化上报状态(各渠道公用),-1未上报,0上报失败,1上报成功*/
    @ApiModelProperty(value = "信息流转化上报状态(各渠道公用),-1未上报,0上报失败,1上报成功")
    private java.lang.Integer tuiaFeedbackStatus;
	/**来源*/
	//@Excel(name = "来源", width = 15)
    @ApiModelProperty(value = "来源")
    private java.lang.String source;
	/**备注*/
	//@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**业务发生时间*/
	//@Excel(name = "业务发生时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "业务发生时间")
    private java.util.Date bizTime;
	/**业务开通时间*/
	@Excel(name = "业务开通时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "业务开通时间")
    private java.util.Date openTime;
	/**创建时间*/
    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改时间*/
	//@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date modifyTime;

    @TableField(exist = false)
    private String smsCode;
    @TableField(exist = false)
    private String transactionId;
    @TableField(exist = false)
    private String bizCode; //四川移动专用业务字段
    @TableField(exist = false)
    private String fuse; //是否融合 1融合 不等于1就是非融合
    @TableField(exist = false)
    private String vrbtChSwitch; //视频彩铃是否后台自动切换渠道号, 默认不切换 0不切换 1切换
    @TableField(exist = false)
    private String vrbtLtSwitch; //联通视频彩铃切换 默认使用自己,1使用瑞金
    @TableField(exist = false)
    private String vrbtLtChannel; //联通视频彩铃渠道,默认为鸿盛
    @TableField(exist = false)
    private String vrbtDxChannel; //电信视频彩铃渠道,默认为光明网
    @TableField(exist = false)
    private String miguPay; //移动咪咕业务是否使用咪咕支付 1使用咪咕支付(跳转到咪咕支付的二次确认页面) 不等于1就是非咪咕支付(老的破解开通或者自己开通)
    @TableField(exist = false)
    private String notAllowCmcc;// 1是不允许 不传0是允许
    @TableField(exist = false)
    private String scmccRedirect;//四川移动跳转 1就跳转 0就不跳转
    @TableField(exist = false)
    private String scmccChannel;//四川移动归属
    @TableField(exist = false)
    private String hcmccChannel;//河北移动渠道号
    @TableField(exist = false)
    private String jcmccChannel;//江西移动渠道号
    @TableField(exist = false)
    private String onlyAllowDianxin;// 1只允许电信
    @TableField(exist = false)
    private String crack;// 1破解
    @TableField(exist = false)
    private String limitSwitch;// 1不切换到讯飞

    @TableField(exist = false)
    private String fuseRead; //白金会员融合咪咕阅读 1融合 不等于1就是非融合

    @TableField(exist = false)
    private String qyclAddDp; //企业彩铃 是否新建部门 1=新建部门 0=加入部门
    @TableField(exist = false)
    private String qyclDpName; //企业彩铃 部门名称
    @TableField(exist = false)
    private String qyclDpId; // 企业彩铃 部门Id

    @TableField(exist = false)
    private String qyclCompanyOwner; // 企业彩铃公司归属

    @TableField(exist = false)
    private String pageId; // 页面配置id


    //业务类型
    @TableField(exist = false)
    private String businessType;

    //优先企业彩铃 1优先企业彩铃 0优先组合包
    @TableField(exist = false)
    private String qyclFirst;
    //产品ID
    @TableField(exist = false)
    private String rightsId;

    //账号
    @TableField(exist = false)
    private String account;
    //支付类型
    @TableField(exist = false)
    private String tradeType;
    //用户标识
    @TableField(exist = false)
    private String userId;

    //业务类型
    @TableField(exist = false)
    private String encryption;

    //彩讯reportTicket
    @TableField(exist = false)
    private String reportTicket;

    //支付重定向地址
    @TableField(exist = false)
    private String returnUrl;

    //产品名称
    @TableField(exist = false)
    private String subject;
    //微信标识
    @TableField(exist = false)
    private String openId;

    //铃音类型:1=主叫,2=被叫,3=主被叫
    @TableField(exist = false)
    private String ringType;

    //铃音名称
    @TableField(exist = false)
    private String ringName;

    //设备码
    @TableField(exist = false)
    private String deviceCode;

    //平台码
    @TableField(exist = false)
    private String platformCode;

    public Subscribe(String id, String mobile, String channel, Integer status, String result) {
        this.id = id;
        this.mobile = mobile;
        this.channel = channel;
        this.status = status;
        this.result = result;
    }

}
