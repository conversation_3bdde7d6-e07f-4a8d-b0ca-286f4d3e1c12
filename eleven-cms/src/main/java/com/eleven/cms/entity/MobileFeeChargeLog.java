package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 话费直充记录
 * @Author: jeecg-boot
 * @Date:   2022-09-28
 * @Version: V1.0
 */
@Data
@TableName("cms_mobile_fee_charge_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_mobile_fee_charge_log对象", description="话费直充记录")
public class MobileFeeChargeLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;

    /**订单号*/
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**订单号*/
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String transOrderId;


	/**通知手机号*/
	@Excel(name = "通知手机号", width = 15)
    @ApiModelProperty(value = "通知手机号")
    private String mobile;
	/**订购月份*/
	@Excel(name = "订购月份", width = 15)
    @ApiModelProperty(value = "订购月份")
    private String orderMonth;
	/**权益月份*/
	@Excel(name = "权益月份", width = 15)
    @ApiModelProperty(value = "权益月份")
    private String rightsMonth;
	/**业务id*/
	@Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private String serviceId;
	/**充值状态:0=直充中,1=直充成功,2=直充失败,-1=预约直充*/
	@Excel(name = "充值状态:0=直充中,1=直充成功,2=直充失败,-1=预约直充", width = 15)
    @ApiModelProperty(value = "充值状态:0=直充中,1=直充成功,2=直充失败,-1=预约直充,-2=失效")
    private Integer status;
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private String couponName;
	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
	/**城市*/
	@Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**账号*/
    @Excel(name = "账号", width = 15)
    @ApiModelProperty(value = "账号")
    private String account;
    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;

    /**权益包名*/
    @Excel(name = "权益包名", width = 15)
    @ApiModelProperty(value = "权益包名")
    private String packName;

    /**产品编码*/
    @Excel(name = "产品编码", width = 15)
    @ApiModelProperty(value = "产品编码")
    private String couponId;
    /**产品价格*/
    @Excel(name = "产品价格", width = 15)
    @ApiModelProperty(value = "产品价格")
    private String  productPrice;
    /**充值方式:0=直充,1=券码*/
    @Excel(name = "充值方式:0=直充,1=券码", width = 15)
    @ApiModelProperty(value = "充值方式:0=直充,1=券码")
    private String  rechargeState;


    /**子渠道号*/
    @Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String  subChannel;

    /**充值结果*/
    @Excel(name = "充值结果", width = 15)
    @ApiModelProperty(value = "充值结果")
    private String result;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

	/**创建时间*/
    @Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**修改时间*/
    @Excel(name = "到账时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "到账时间")
    private Date payTime;

	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
