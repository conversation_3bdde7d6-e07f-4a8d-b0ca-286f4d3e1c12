package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 业务短信模板
 * @Author: jeecg-boot
 * @Date:   2022-09-23
 * @Version: V1.0
 */
@Data
@TableName("cms_sms_model")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_sms_model对象", description="业务短信模板")
public class SmsModel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**业务id*/
	@Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private String serviceId;
	/**业务名字*/
	@Excel(name = "业务名字", width = 15)
    @ApiModelProperty(value = "业务名字")
    private String serviceName;
	/**业务类型:0=短信验证码,1=权益短信*/
	@Excel(name = "业务类型:0=主要,1=短信验证码,2=权益短信", width = 15)
    @ApiModelProperty(value = "业务类型:0=主要,1=短信验证码,2=权益短信")
    private String serviceType;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**短信模板*/
	@Excel(name = "短信模板", width = 15)
    @ApiModelProperty(value = "短信模板")
    private String smsModel;
	/**短信参数*/
	@Excel(name = "短信参数", width = 15)
    @ApiModelProperty(value = "短信参数")
    private String smsParameter;
	/**是否开启:0=否,1=是*/
	@Excel(name = "是否开启:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否开启:0=否,1=是")
    private Integer smsSwitchs;
    /**延迟时间(秒)*/
    @Excel(name = "延迟时间(秒)", width = 15)
    @ApiModelProperty(value = "延迟时间(秒)")
    private Integer delayTime;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
