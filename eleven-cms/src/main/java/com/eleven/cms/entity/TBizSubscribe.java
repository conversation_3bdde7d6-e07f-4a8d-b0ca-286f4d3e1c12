package com.eleven.cms.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @datetime 2025/2/18 15:33
 */
@Data
@TableName("t_biz_subscribe")
@DS("member")
public class TBizSubscribe {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 业务类型
     */
    @TableField("biz_type")
    private String bizType;

    /**
     * 渠道号
     */
    @TableField("channel")
    private String channel;

    /**
     * 子渠道号
     */
    @TableField("sub_channel")
    private String subChannel;
    /**
     * 渠道归属
     */
    @TableField("owner")
    private String owner;

    /**
     * 咪咕serviceId
     */
    @TableField("service_id")
    private String serviceId;
    /**
     * 运营商一键下单订单号
     */
    @TableField("isp_order_no")
    private String ispOrderNo;
    /**
     * 状态(-1=初始,0=失败,1=成功)
     */
    @TableField("status")
    private Integer status;

    /**
     * 订购结果
     */
    @TableField("result")
    private String result;

    /**校验包月状态(延迟60分钟) 60分钟退订校验(-1=未校验,0=未包月,1=已包月)*/
    @TableField("verify_status")
    private Integer verifyStatus;
    /**校验包月状态(延迟901天)* 1天退订校验(-1=未校验,0=未包月,1=已包月) */
    @TableField("verify_status_daily")
    private Integer verifyStatusDaily;
    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;
    /**
     * 推送至即时开通服务器返回编码
     */
    @TableField("push_resp_code")
    private Integer pushRespCode;

    /**
     * 推送至即时开通服务器返回消息
     */
    @TableField("push_resp_message")
    private String pushRespMessage;

    /**
     * 信息流彩图上报状态:-1未上报,0上报失败,1上报成功
     */
    @TableField("feedback_status_caitu")
    private Integer feedbackStatusCaitu;

    /**6个月包月校验:-1=未校验,0=未包月,1=已包月*/
    @TableField("price")
    private Integer price;


    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;


    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 浏览器ua
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 从哪个页面发起的订阅
     */
    @TableField("referer")
    private String referer;

    /**
     * 推广ID
     */
    @TableField("device_info")
    private String deviceInfo;

    /**
     * ip
     */
    @TableField("ip")
    private String ip;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 业务发生时间
     */
    @TableField("biz_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date bizTime;

    /**
     * 业务开通时间
     */
    @TableField("open_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date openTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @TableField(exist = false)
    private String createTimeFrom;
    @TableField(exist = false)
    private String createTimeTo;

    @TableField(exist = false)
    private String smsCode;
    @TableField
    private String transactionId;

    /**
     * 运营商
     */
    private java.lang.String isp;



    /**
     * 扩展字段,运营商订单号
     */
    private java.lang.String extra;
    /**
     * 联通渠道号
     */
    @TableField(exist = false)
    private String ltChannel;

    @TableField(exist = false)
    private String fuse; //是否融合 1融合 不等于1就是非融合

    //咪咕动漫渠道标识
    @TableField(exist = false)
    private String channelId;
    //⽤户ID,登录授权获取
    @TableField(exist = false)
    private String identity;
    //微信/⽀付宝OPENID
    @TableField(exist = false)
    private String openid;

    @TableField(exist = false)
    private String pushId;
    @TableField(exist = false)
    private String userId;
    @TableField(exist = false)
    private String aclId;
    @TableField(exist = false)
    private String sourceUrl;
    @TableField(exist = false)
    private String token;
    @TableField(exist = false)
    private String productId;

    @TableField(exist = false)
    private String productName;
    @TableField(exist = false)
    private String consignee;
    @TableField(exist = false)
    private String shippingAddress;
    @TableField(exist = false)
    private String detailAddress;
    @TableField(exist = false)
    private String consigneeMobile;
    @TableField(exist = false)
    private String style;
    @TableField(exist = false)
    private String size;

    // 步骤 1发送验证码 2 登录 3 下单
    @TableField(exist = false)
    private String step;

    @TableField(exist = false)
    private String srcContentId;

    @TableField(exist = false)
    private String crack;// 1破解
    @TableField(exist = false)
    private String scmccChannel;//四川移动归属
    @TableField(exist = false)
    private String jcmccChannel;//江西移动归属
}
