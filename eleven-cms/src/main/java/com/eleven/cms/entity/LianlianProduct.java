package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 联联分销产品
 * @Author: jeecg-boot
 * @Date:   2024-05-13
 * @Version: V1.0
 */
@Data
@TableName("cms_lianlian_product")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_lianlian_product对象", description="联联分销产品")
public class LianlianProduct implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private String onlyName;
    /**产品状态 0:下架 1:上架 2:售罄*/
    @Excel(name = "产品状态 0:下架 1:上架 2:售罄", width = 15)
    @ApiModelProperty(value = "产品状态 0:下架 1:上架 2:售罄")
    private Integer productType;
	/**封面图*/
	@Excel(name = "封面图", width = 15)
    @ApiModelProperty(value = "封面图")
    private String faceImg;
	/**售价(分)*/
	@Excel(name = "售价(分)", width = 15)
    @ApiModelProperty(value = "售价(分)")
    private Integer salePrice;
	/**原价(分)*/
	@Excel(name = "原价(分)", width = 15)
    @ApiModelProperty(value = "原价(分)")
    private Integer originPrice;
	/**渠道结算价(分)*/
	@Excel(name = "渠道结算价(分)", width = 15)
    @ApiModelProperty(value = "渠道结算价(分)")
    private Integer channelPrice;
	/**产品编码*/
	@Excel(name = "产品编码", width = 15)
    @ApiModelProperty(value = "产品编码")
    private Integer productId;
	/**套餐编码*/
	@Excel(name = "套餐编码", width = 15)
    @ApiModelProperty(value = "套餐编码")
    private Integer itemId;
	/**核销码类型1-有批量核销码和独立核销码以及电子码,2-没有批量核销码有独立电子码（不能直接用电子码生成对应核销码）没有qrCodeImgUrl,qrCodeUrl为HTML,3-没有批量核销码和独立核销码以及电子码*/
	@Excel(name = "核销码类型1-有批量核销码和独立核销码以及电子码,2-没有批量核销码有独立电子码（不能直接用电子码生成对应核销码）没有qrCodeImgUrl,qrCodeUrl为HTML,3-没有批量核销码和独立核销码以及电子码", width = 15)
    @ApiModelProperty(value = "核销码类型1-有批量核销码和独立核销码以及电子码,2-没有批量核销码有独立电子码（不能直接用电子码生成对应核销码）没有qrCodeImgUrl,qrCodeUrl为HTML,3-没有批量核销码和独立核销码以及电子码")
    private Integer codeType;
	/**售卖数量*/
	@Excel(name = "售卖数量", width = 15)
    @ApiModelProperty(value = "售卖数量")
    private Integer singleMax;
	/**库存数量*/
	@Excel(name = "库存数量", width = 15)
    @ApiModelProperty(value = "库存数量")
    private Integer stock;
	/**城市编码*/
	@Excel(name = "城市编码", width = 15)
    @ApiModelProperty(value = "城市编码")
    private String city;
	/**城市*/
	@Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String cityName;
	/**预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明*/
	@Excel(name = "预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明", width = 15)
    @ApiModelProperty(value = "预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明")
    private Integer bookingType;
	/**是否需要填写配送地址 0-否 1-是*/
	@Excel(name = "是否需要填写配送地址 0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否需要填写配送地址 0-否 1-是")
    private Integer bookingShowAddress;
	/**是否需要身份证 0-否 1-是*/
	@Excel(name = "是否需要身份证 0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否需要身份证 0-否 1-是")
    private Integer orderShowIdCard;
	/**是否需要填写使用日期 0-否 1-是*/
	@Excel(name = "是否需要填写使用日期 0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否需要填写使用日期 0-否 1-是")
    private Integer orderShowDate;
	/**抢购开始时间*/
	@Excel(name = "抢购开始时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "抢购开始时间")
    private Date beginTime;
	/**抢购结束时间*/
	@Excel(name = "抢购结束时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "抢购结束时间")
    private Date endTime;
	/**购买后——有效开始时间*/
	@Excel(name = "购买后——有效开始时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "购买后——有效开始时间")
    private Date validBeginDate;
	/**购买后——有效结束时间*/
	@Excel(name = "购买后——有效结束时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "购买后——有效结束时间")
    private Date validEndDate;
	/**预约开始时间*/
	@Excel(name = "预约开始时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预约开始时间")
    private Date bookingBeginDate;
	/**产品上线时间*/
	@Excel(name = "产品上线时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "产品上线时间")
    private Date releaseTime;
	/**商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）*/
	@Excel(name = "商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）", width = 15)
    @ApiModelProperty(value = "商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）")
    private Integer ecommerce;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;


    /**权益包名*/
    @TableField(exist = false)
    private String packName;
    /**权益包名*/
    @TableField(exist = false)
    private String serviceId;


}
