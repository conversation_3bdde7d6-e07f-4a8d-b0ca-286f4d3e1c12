package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 视频彩铃渠道(开放平台/彩铃中心)切换省份配置
 * @Description: cms_vrbt_province_switch_config
 * @Author: jeecg-boot
 * @Date:   2021-08-16
 * @Version: V1.0
 */
@Data
@TableName("cms_vrbt_province_switch_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_vrbt_province_switch_config对象", description="cms_vrbt_province_switch_config")
public class VrbtProvinceSwitchConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**业务名称*/
	@Excel(name = "业务名称", width = 15)
    @ApiModelProperty(value = "业务名称")
    private String businessName;
	/**咪咕渠道号*/
	@Excel(name = "咪咕渠道号", width = 15)
    @ApiModelProperty(value = "咪咕渠道号")
    private String miguChannel;
	/**是否优先 1=是,0=否*/
	@Excel(name = "是否优先", width = 15)
    @ApiModelProperty(value = "是否优先")
    @Dict(dicCode = "yn")
    private String isPrior;
	/**是否缺省 1=是,0=否*/
	@Excel(name = "是否缺省", width = 15)
    @ApiModelProperty(value = "是否缺省")
    @Dict(dicCode = "yn")
    private String isDefault;
	/**关联省份JSON*/
	@Excel(name = "关联省份JSON", width = 15)
    @ApiModelProperty(value = "关联省份JSON")
    private String provinceJson;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
