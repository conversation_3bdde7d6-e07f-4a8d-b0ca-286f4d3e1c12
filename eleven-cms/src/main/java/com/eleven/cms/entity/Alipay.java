package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 支付宝支付配置
 * @Author: jeecg-boot
 * @Date:   2022-12-29
 * @Version: V1.0
 */
@Data
@TableName("cms_alipay")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_alipay对象", description="支付宝支付配置")
public class Alipay implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**商户号名字*/
	@Excel(name = "商户号名字", width = 15)
    @ApiModelProperty(value = "商户号名字")
    private String mchName;
	/**商户号appId*/
	@Excel(name = "商户号appId", width = 15)
    @ApiModelProperty(value = "商户号appId")
    private String appId;
	/**商户号公钥*/
	@Excel(name = "商户号公钥", width = 15)
    @ApiModelProperty(value = "商户号公钥")
    private String publicKey;
	/**商户号私钥*/
	@Excel(name = "商户号私钥", width = 15)
    @ApiModelProperty(value = "商户号私钥")
    private String privateKey;
	/**业务类型*/
	@Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;
	/**业务名字*/
	@Excel(name = "业务名字", width = 15)
    @ApiModelProperty(value = "业务名字")
    private String businessName;
	/**总支付金额*/
	@Excel(name = "总支付金额", width = 15)
    @ApiModelProperty(value = "总支付金额")
    private String totalAmount;
	/**周期类型枚举值为DAY和MONTH*/
	@Excel(name = "周期类型枚举值为DAY和MONTH", width = 15)
    @ApiModelProperty(value = "周期类型枚举值为DAY和MONTH")
    private String periodType;
	/**周期数*/
	@Excel(name = "周期数", width = 15)
    @ApiModelProperty(value = "周期数")
    private Integer period;
	/**每次支付金额*/
	@Excel(name = "每次支付金额", width = 15)
    @ApiModelProperty(value = "每次支付金额")
    private String singleAmount;
	/**扣款次数*/
	@Excel(name = "扣款次数", width = 15)
    @ApiModelProperty(value = "扣款次数")
    private Integer totalPayments;
	/**当前用户签约请求的协议有效周期。d天m月 如果未传入，默认为长期有效。*/
	@Excel(name = "当前用户签约请求的协议有效周期。d天m月 如果未传入，默认为长期有效。", width = 15)
    @ApiModelProperty(value = "当前用户签约请求的协议有效周期。d天m月 如果未传入，默认为长期有效。")
    private String signValidityPeriod;
	/**支付异步通知地址*/
	@Excel(name = "支付异步通知地址", width = 15)
    @ApiModelProperty(value = "支付异步通知地址")
    private String payNotifyUrl;
	/**支付同步通知地址*/
	@Excel(name = "支付同步通知地址", width = 15)
    @ApiModelProperty(value = "支付同步通知地址")
    private String payReturnUrl;

    /**协议签约异步通知地址*/
    @Excel(name = "协议签约异步通知地址", width = 15)
    @ApiModelProperty(value = "协议签约异步通知地址")
    private String agreementNotifyUrl;
    /**协议签约同步通知地址*/
    @Excel(name = "协议签约同步通知地址", width = 15)
    @ApiModelProperty(value = "协议签约同步通知地址")
    private String agreementReturnUrl;

	/**是否有效:0=否,1=是*/
	@Excel(name = "是否有效:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否有效:0=否,1=是")
    private Integer isValid;
    /**预警缓存开关:0=否,1=是*/
    @Excel(name = "预警缓存开关:0=否,1=是", width = 15)
    @ApiModelProperty(value = "预警缓存开关:0=否,1=是")
    private String isSwitch;
	/**服务器IP*/
	@Excel(name = "服务器IP", width = 15)
    @ApiModelProperty(value = "服务器IP")
    private String serverIp;
    /**商户号*/
    @Excel(name = "商户号", width = 15)
    @ApiModelProperty(value = "商户号")
    private String externalLogonId;
    /**退款方式:1=转账,2=退款*/
    @Excel(name = "退款方式:1=转账,2=退款", width = 15)
    @ApiModelProperty(value = "退款方式:1=转账,2=退款")
    private String customerService;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
    @Excel(name = "创建日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
    @Excel(name = "更新日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;


    /**回传地址*/
    @Excel(name = "回传地址", width = 15)
    @ApiModelProperty(value = "回传地址")
    private String callbackUrl;
    /**是否回传:0=否,1=是*/
    @Excel(name = "是否回传:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否回传:0=否,1=是")
    private Integer isCallback;
    /**上报比率*/
    @Excel(name = "上报比率", width = 15)
    @ApiModelProperty(value = "上报比率")
    private Integer upRatio;
    /**次月是否回传:0=否,1=是*/
    @Excel(name = "次月是否回传:0=否,1=是", width = 15)
    @ApiModelProperty(value = "次月是否回传:0=否,1=是")
    private Integer downMonthIsCallback;
    /**次月上报比率*/
    @Excel(name = "次月上报比率", width = 15)
    @ApiModelProperty(value = "次月上报比率")
    private Integer downMonthUpRatio;



    /**业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃*/
    @Excel(name = "业务类型", width = 15, dicCode = "biz_type")
    @ApiModelProperty(value = "业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃")
    @Dict(dicCode = "biz_type")
    private String bizType;
}
