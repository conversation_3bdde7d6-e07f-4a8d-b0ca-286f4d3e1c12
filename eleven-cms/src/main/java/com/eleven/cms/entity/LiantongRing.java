package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 联通彩铃diy
 * @Author: jeecg-boot
 * @Date:   2023-08-22
 * @Version: V1.0
 */
@Data
@TableName("cms_liantong_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_liantong_ring对象", description="联通彩铃diy")
public class LiantongRing implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**内容ID*/
	@Excel(name = "内容ID", width = 15)
    @ApiModelProperty(value = "内容ID")
    private String contentId;
    /**视频封面*/
    @Excel(name = "视频封面", width = 15)
    @ApiModelProperty(value = "视频封面")
    private String videoImg;
	/**铃音播放地址*/
	@Excel(name = "铃音播放地址", width = 15)
    @ApiModelProperty(value = "铃音播放地址")
    private String videoUrl;
	/**铃音ID*/
	@Excel(name = "铃音ID", width = 15)
    @ApiModelProperty(value = "铃音ID")
    private String ringId;
    /**视频铃音状态 00=未上传,01=铃音上传失败,02=审核中,03=审核未通过,04=审核通过,05=设置成功,06=设置失败,07=内容ID创建失败*/
    @Excel(name = "视频铃音状态 00=未上传,01=铃音上传失败,02=审核中,03=审核未通过,04=审核通过,05=设置成功,06=设置失败,07=内容ID创建失败", width = 15)
    @ApiModelProperty(value = "视频铃音状态 00=未上传,01=铃音上传失败,02=审核中,03=审核未通过,04=审核通过,05=设置成功,06=设置失败,07=内容ID创建失败")
    private String ringStatus;

    /**内容ID创建结果*/
    @Excel(name = "内容ID创建结果", width = 15)
    @ApiModelProperty(value = "内容ID创建结果")
    private String contentResult;

    /**铃音上传结果*/
    @Excel(name = "铃音上传结果", width = 15)
    @ApiModelProperty(value = "铃音上传结果")
    private String ringResult;


	/**状态:0=无效,1=有效*/
	@Excel(name = "状态:0=无效,1=有效", width = 15)
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    private Integer status;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderBy;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;


	/**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
