package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Data
@TableName("cms_crack_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "cms_crack_config对象", description = "cms_crack_config")
public class CmsCrackConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 渠道号
     */
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;


    /**
     * 业务类型
     */
    @Excel(name = "业务类型", width = 15)
    private String bizType;

    /**
     * fee
     */
    @Excel(name = "payCode", width = 15)
    @ApiModelProperty(value = "fee")
    private String payCode;

    /**
     * fee
     */
    @Excel(name = "newPayCode", width = 15)
    @ApiModelProperty(value = "newPayCode")
    private String newPayCode;
    /**
     * 通知地址
     */
    @Excel(name = "fee", width = 15)
    @ApiModelProperty(value = "通知地址")
    private String fee;
    /**
     * cpid
     */
    @Excel(name = "cpid", width = 15)
    @ApiModelProperty(value = "cpid")
    private String cpid;
    /**
     * type
     */
    @Excel(name = "type", width = 15)
    @ApiModelProperty(value = "type")
    private String type;
    /**
     * 森越pj产品编码
     */
    @Excel(name = "type", width = 15)
    @ApiModelProperty(value = "type")
    private String senyueProductId;
    /**
     * 森越pj渠道编码
     */
    @Excel(name = "type", width = 15)
    @ApiModelProperty(value = "type")
    private String senyueChannelId;
    /**
     * itemId
     */
    @Excel(name = "type", width = 15)
    @ApiModelProperty(value = "type")
    private String itemId;
    /**
     * 备注
     */
    @Excel(name = "日志标记", width = 15)
    @ApiModelProperty(value = "备注")
    private String logTag;

    //咪咕相关参数
    private String serviceId;
    private String signatureSecretKey;
    private String loginSecretKey;
    private String forwardDomain;
    private String smsSignDatang;
    private String smsSuffix;


    /**
     * 备注
     */

    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 扩展字段
     */
    @Excel(name = "扩展字段", width = 15)
    @ApiModelProperty(value = "扩展字段")
    private String extend;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
