package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: cms_adv_info
 * @Author: jeecg-boot
 * @Date:   2022-11-21
 * @Version: V1.0
 */
@Data
@TableName("cms_adv_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_adv_info对象", description="cms_adv_info")
public class CmsAdvInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**任务ID*/
	@Excel(name = "任务ID", width = 15)
    @ApiModelProperty(value = "任务ID")
    private java.lang.Long taskSeq;
	/**触点ID*/
	@Excel(name = "触点ID", width = 15)
    @ApiModelProperty(value = "触点ID")
    private java.lang.Long advSeq;
	/**触点名称*/
	@Excel(name = "触点名称", width = 15)
    @ApiModelProperty(value = "触点名称")
    private java.lang.String advName;
	/**任务量*/
	@Excel(name = "任务量", width = 15)
    @ApiModelProperty(value = "任务量")
    private java.lang.Long planNum;
	/**通知类型*/
	@Excel(name = "通知类型", width = 15)
    @ApiModelProperty(value = "通知类型")
    private java.lang.String notifyType;
	/**触点链接*/
	@Excel(name = "触点链接", width = 15)
    @ApiModelProperty(value = "触点链接")
    private java.lang.String advUrl;
	/**触点图片链接*/
	@Excel(name = "触点图片链接", width = 15)
    @ApiModelProperty(value = "触点图片链接")
    private java.lang.String picUrl;
	/**触点说明*/
	@Excel(name = "触点说明", width = 15)
    @ApiModelProperty(value = "触点说明")
    private java.lang.String remark;
	/**投放开始时间*/
	@Excel(name = "投放开始时间", width = 15)
    @ApiModelProperty(value = "投放开始时间")
    private java.util.Date startTime;
	/**投放结束时间*/
	@Excel(name = "投放结束时间", width = 15)
    @ApiModelProperty(value = "投放结束时间")
    private java.util.Date endTime;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date modifyTime;
}
