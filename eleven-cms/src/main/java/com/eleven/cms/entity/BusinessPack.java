package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 业务渠道权益关联
 * @Author: jeecg-boot
 * @Date:   2022-12-19
 * @Version: V1.0
 */
@Data
@TableName("cms_business_pack")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_business_pack对象", description="业务渠道权益关联")
public class BusinessPack implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**业务渠道ID*/
	@Excel(name = "业务渠道ID", width = 15)
    @ApiModelProperty(value = "业务渠道ID")
    private String businessId;
	/**业务包ID*/
	@Excel(name = "业务包ID", width = 15)
    @ApiModelProperty(value = "业务包ID")
    private String packId;
	/**业务id*/
	@Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private String serviceId;
	/**包名*/
	@Excel(name = "包名", width = 15)
    @ApiModelProperty(value = "包名")
    private String titleName;
	/**业务包名称*/
	@Excel(name = "业务包名称", width = 15)
    @ApiModelProperty(value = "业务包名称")
    private String packName;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**业务bean名称*/
    @Excel(name = "业务bean名称", width = 15, dicCode = "service_api_bean_name")
    @ApiModelProperty(value = "业务bean名称")
    @Dict(dicCode = "service_api_bean_name")
    private String serviceApiBeanName;


	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;




}
