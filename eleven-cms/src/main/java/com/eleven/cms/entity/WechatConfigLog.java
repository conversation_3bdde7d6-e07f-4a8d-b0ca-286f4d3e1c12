package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: qycl_wechat_config_log
 * @Author: jeecg-boot
 * @Date:   2022-12-05
 * @Version: V1.0
 */
@Data
@TableName("qycl_wechat_config_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="qycl_wechat_config_log对象", description="qycl_wechat_config_log")
public class WechatConfigLog  implements Serializable  {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**公众号名字*/
	@Excel(name = "公众号名字", width = 15)
    @ApiModelProperty(value = "公众号名字")
    private String publicName;
	/**公众号appId*/
	@Excel(name = "公众号appId", width = 15)
    @ApiModelProperty(value = "公众号appId")
    private String appId;
	/**公众号appSecret*/
	@Excel(name = "公众号appSecret", width = 15)
    @ApiModelProperty(value = "公众号appSecret")
    private String appSecret;
	/**商户号名字*/
	@Excel(name = "商户号名字", width = 15)
    @ApiModelProperty(value = "商户号名字")
    private String mchName;
	/**商户号mchId*/
	@Excel(name = "商户号mchId", width = 15)
    @ApiModelProperty(value = "商户号mchId")
    private String mchId;
	/**商户号key*/
	@Excel(name = "商户号key", width = 15)
    @ApiModelProperty(value = "商户号key")
    private String mchKey;
	/**业务类型*/
	@Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    /**支付类型*/
    @Excel(name = "支付类型", width = 15)
    @ApiModelProperty(value = "支付类型")
    private String tradeType;
	/**业务名字*/
	@Excel(name = "业务名字", width = 15)
    @ApiModelProperty(value = "业务名字")
    private String businessName;
	/**抖音支付通知地址*/
	@Excel(name = "抖音支付通知地址", width = 15)
    @ApiModelProperty(value = "抖音支付通知地址")
    private String notifyUrl;
	/**抖音支付平台公钥*/
	@Excel(name = "抖音支付平台公钥", width = 15)
    @ApiModelProperty(value = "抖音支付平台公钥")
    private String returnUrl;
	/**是否有效:0=否,1=是*/
	@Excel(name = "是否有效:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否有效:0=否,1=是")
    private Integer isValid;
	/**服务器IP*/
	@Excel(name = "服务器IP", width = 15)
    @ApiModelProperty(value = "服务器IP")
    private String serverIp;
	/**支付金额*/
	@Excel(name = "支付金额", width = 15)
    @ApiModelProperty(value = "支付金额")
    private String totalAmount;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**抖音支付应用私钥*/
    @Excel(name = "抖音支付应用私钥", width = 15)
    @ApiModelProperty(value = "抖音支付应用私钥")
    private String apiKey;

    /**抖音支付应用版本号*/
    @Excel(name = "抖音支付应用版本号", width = 15)
    @ApiModelProperty(value = "抖音支付应用版本号")
    private String mchSerialNo;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
