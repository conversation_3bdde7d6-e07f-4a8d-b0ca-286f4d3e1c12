package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 落地页业务配置 Entity
 *
 * <AUTHOR>
 * @date 2023-08-08 14:42:58
 */
@Data
@TableName("xxl_page_business_config")
public class PageBusinessConfig {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 推广链接
     */
    @TableField("page_url")
    private String pageUrl;

    /**
     * 名称
     */
    @TableField("page_name")
    private String pageName;

    /**
     * 状态:0=报备,1=在投,2=停用
     */
    @TableField("status")
    private Integer status;

    /**
     * 省份跳转:1=跳转,0=不跳转
     */
    @TableField("province_redirect_status")
    private Integer provinceRedirectStatus;

    /**
     * 省份跳转链接
     */
    @TableField("province_redirect_link")
    private String provinceRedirectLink;

    /**
     * 其它跳转:1=跳转,0=不跳转
     */
    @TableField("other_redirect_status")
    private Integer otherRedirectStatus;

    /**
     * 其它跳转链接
     */
    @TableField("other_redirect_link")
    private String otherRedirectLink;

    /**
     * 返回时跳转:1=跳转,0=不跳转
     */
    @TableField("return_redirect_status")
    private Integer returnRedirectStatus;

    /**
     * 返回时跳转链接
     */
    @TableField("return_redirect_link")
    private String returnRedirectLink;

    /**
     * 是否过滤app空包名:1=过滤,0=不过滤
     */
    @TableField("app_pkg_null_filter")
    private Integer appPkgNullFilter;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    @TableField(exist = false)
    private List<PageBusinessProvinceConfig> list;

    /**
     * 时间起
     */
    @TableField(exist = false)
    private String timeFrom;

    /**
     * 时间止
     */
    @TableField(exist = false)
    private String timeTo;

}
