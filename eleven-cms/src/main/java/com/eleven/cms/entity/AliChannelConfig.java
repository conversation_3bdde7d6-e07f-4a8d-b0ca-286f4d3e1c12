package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_ali_channel_config
 * @Author: jeecg-boot
 * @Date:   2023-04-24
 * @Version: V1.0
 */
@Data
@TableName("cms_ali_channel_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_ali_channel_config对象", description="cms_ali_channel_config")
public class AliChannelConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**落地页*/
	@Excel(name = "落地页", width = 15)
    @ApiModelProperty(value = "落地页")
    private String landingPage;
	/**配置key*/
	@Excel(name = "配置key", width = 15)
    @ApiModelProperty(value = "配置key")
    private String configKey;
	/**配置value*/
	@Excel(name = "配置value", width = 15)
    @ApiModelProperty(value = "配置value")
    private String configValue;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**支付方式，1=微信，2=支付宝，3=微信加支付宝'*/
    @Excel(name = "支付方式", width = 15)
    @ApiModelProperty(value = "支付方式")
    private Integer payWay;

    /**支付提供方，1=我方，2=联通沃悦读悠然，3=联通沃悦读麦禾'*/
    @Excel(name = "支付提供方", width = 15)
    @ApiModelProperty(value = "支付提供方")
    private Integer paySupport;
}
