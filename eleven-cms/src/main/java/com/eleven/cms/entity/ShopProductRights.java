package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 商城产品价格关联
 * @Author: jeecg-boot
 * @Date:   2023-09-19
 * @Version: V1.0
 */
@Data
@TableName("cms_shop_product_rights")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_shop_product_rights对象", description="商城产品价格关联")
public class ShopProductRights implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**商城产品主键id*/
	@Excel(name = "商城产品主键id", width = 15)
    @ApiModelProperty(value = "商城产品主键id")
    private String shopId;
	/**产品价格主键id*/
	@Excel(name = "产品价格主键id", width = 15)
    @ApiModelProperty(value = "产品价格主键id")
    private String memberRightsId;

    /**权益Id*/
    @Excel(name = "权益Id", width = 15)
    @ApiModelProperty(value = "权益Id")
    private String rightsId;

	/**权益名称*/
	@Excel(name = "权益名称", width = 15)
    @ApiModelProperty(value = "权益名称")
    private String rightsName;
	/**现价（单位：分）*/
	@Excel(name = "现价（单位：分）", width = 15)
    @ApiModelProperty(value = "现价（单位：分）")
    private Integer productPrice;
	/**原价（单位：分）*/
	@Excel(name = "原价（单位：分）", width = 15)
    @ApiModelProperty(value = "原价（单位：分）")
    private Integer originalPrice;
    /**是否上架:0=否,1=是*/
    @Excel(name = "是否上架", width = 15, dicCode = "yn")
    @ApiModelProperty(value = "是否上架:0=否,1=是")
    @Dict(dicCode = "yn")
    private Integer isOnline;

    /**排序*/
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
