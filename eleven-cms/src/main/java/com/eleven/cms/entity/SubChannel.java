package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 *  Entity
 *
 * <AUTHOR>
 * @date 2021-06-15 14:28:25
 */
@Data
@TableName("xxl_channel")
public class SubChannel {

    /**
     * 
     */
    @TableId(value = "id", type = IdType.ID_WORKER_STR )
    private String id;

    /**
     * 名称
     */
    @TableField("title")
    private String title;

    /*平台*/
    @TableField(exist = false)
    private String adPlatform;

    /**
     * 广告平台账号id
     */
    @TableField("account_id")
    private String accountId;

    /*账号*/
    @TableField(exist = false)
    private String account;


    /**
     * 推广链接
     */
    @TableField("spread_url")
    private String spreadUrl;

    /**
     * 上报规则
     */
    @TableField("report_rule")
    private String reportRule;

    /**
     * 上报比率
     */
    @TableField("up_ratio")
    private Integer upRatio;

    /**
     * 是否上报全部(1：上报全部，0：只上报成功)
     */
    @TableField("report_status")
    private String reportStatus;

    /**
     * 归属
     */
    @TableField("owner")
    private String owner;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 移动上报状态，1-开启，0-关闭
     */
    @TableField("report_status_cmcc")
    private Integer reportStatusCmcc;

    /**
     * 联通上报状态，1-开启，0-关闭
     */
    @TableField("report_status_cucc")
    private Integer reportStatusCucc;

    /**
     * 电信上报状态，1-开启，0-关闭
     */
    @TableField("report_status_ctcc")
    private Integer reportStatusCtcc;

    /**
     * 不上报渠道号
     */
    @TableField("un_report_channel")
    private String unReportChannel;

    /**
     * 介绍
     */
    @TableField("intro")
    /*@ExcelField(value = "介绍")*/
    private String intro;



    /**
     * 状态,0=有效,1=无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 广告平台id
     */
    @TableField("ad_platform_id")
    private String adPlatformId;

    /**
     * 投放来源
     */
    @TableField("advertisement_source")
    private String advertisementSource;

    /**
     * 创建日期
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新日期
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 落地页url
     */
    @TableField("landing_page")
    private String landingPage;

   /* *//**
     * 类型
     *//*
    @TableField("type")
    private String type;

    *//**
     * 图片路径
     *//*
    @TableField("pic_url")
    private String picUrl;

    *//**
     * 模板
     *//*
    @TableField(exist = false)
    private String mould;
*/

    /**
     * 广告账号id
     */
    @TableField("ad_account_id")
    private String adAccountId;

    /*河北移动开关*/
    /*private Integer hbydConfig;*/

    /**
     * 上报状态码
     */
    @TableField("report_code")
    private String reportCode;

    /**
     * 上报第三方,1=企业彩铃个人版，2=广州趣盈
     */
    @TableField("report_else")
    private String reportElse;

    @TableField("create_by")
    private String createBy;

    @TableField("update_by")
    private String updateBy;

    /**
     * 优化师
     */
    @TableField("optimizer")
    private String optimizer;

}
