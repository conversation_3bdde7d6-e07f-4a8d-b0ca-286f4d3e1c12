package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Entity
 *
 * <AUTHOR>
 * @date 2024-05-28 18:06:31
 */
@Data
@TableName("xxl_biz_tail_quantity_config")
public class BizTailQuantityConfig {

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 尾量名称
     */
    @TableField("tail_quantity_name")
    private String tailQuantityName;

    /**
     * 归属
     */
    @TableField("biz_owner")
    private String bizOwner;

    /**
     * 微量配置json
     */
    @TableField("config_json")
    private String configJson;

    /**
     *
     */
    @TableField("create_time")
    private Date createTime;

    /**
     *
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 1启用0禁用
     */
    @TableField("is_enable")
    private Integer isEnable;

    /**
     * 备注
     */
    @TableField("remake")
    private String remake;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

}
