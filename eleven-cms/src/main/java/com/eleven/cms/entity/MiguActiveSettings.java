package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 *  Entity
 *
 * <AUTHOR>
 * @date 2025-01-24 14:07:31
 */
@Data
@TableName("cms_migu_active_settings")
public class MiguActiveSettings {

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建日期
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;

    /**
     * 跳转链接
     */
    @TableField("jump_url")
    private String jumpUrl;

    /**
     * 限量
     */
    @TableField(value = "limit_count", updateStrategy = FieldStrategy.IGNORED)
    private Long limitCount;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新日期
     */
    @TableField("update_time")
    private Date updateTime;

}
