package com.eleven.cms.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Description: 俊博直充记录
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Data
@DS("master")
@TableName("cms_junbo_charge_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_junbo_charge_log对象", description="俊博直充记录")
public class JunboChargeLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**咪咕订单号*/
	@Excel(name = "咪咕订单号", width = 15)
    @ApiModelProperty(value = "咪咕订单号")
    private String miguOrderId;
	/**俊博订单号*/
	@Excel(name = "俊博订单号", width = 15)
    @ApiModelProperty(value = "俊博订单号")
    private String junboOrderId;
    /**充值账号*/
    @Excel(name = "充值账号", width = 15)
    @ApiModelProperty(value = "充值账号")
    private String account;
    /**通知手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "通知手机号")
    private String mobile;
    /**充值状态:0=直充中,1=直充成功,2=直充失败*/
    @Excel(name = "充值状态", width = 15, dicCode = "junbo_charge_status")
    @ApiModelProperty(value = "充值状态:-1=预约直充,0=直充中,1=直充成功,2=直充失败")
    @Dict(dicCode = "junbo_charge_status")
    private Integer status;
    /**备注*/
    @Excel(name = "充值描述", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**充值方式:0=直充,1=券码*/
    @Excel(name = "充值方式", width = 15, dicCode = "recharge_state")
    @ApiModelProperty(value = "充值方式:0=直充,1=券码")
    private Integer rechargeState;
    /**产品名称*/
    @Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private String couponName;
    /**业务id*/
    @Excel(name = "业务名字", width = 15, dicCode = "service_name")
    @ApiModelProperty(value = "业务名字:135999999999999000157=10元电信视频彩,4900753400=沃音乐视频彩铃,698039020108799364=白金会员畅听版,90030426=贝壳视听会员,698039020050006172=趣享音乐包,698039035100000014=炫酷来电,698039020105522717=白金会员,698039020108689345=北岸唐唱音乐包,698039020103880544=宜搜白金会员,330000132=骏伯咪咕动漫,698039035103445177=咪咕音乐视频彩铃包月,698039020104154386=咪咕音乐白金会员畅听版5元,698039042105792434=视频彩铃-渠道包")
    private String serviceId;
    /**创建日期*/
    @Excel(name = "预约充值时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预约充值时间")
    private java.util.Date scheduledTime;
    /**创建时间*/
    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**支付时间*/
    @Excel(name = "支付时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;


	/**权益月份*/
	@Excel(name = "权益月份", width = 15)
    @ApiModelProperty(value = "权益月份")
    private String rightsMonth;
    /**权益包名*/
    @Excel(name = "权益包名", width = 15)
    @ApiModelProperty(value = "权益包名")
    private String packName;
	/**产品编码*/
	@Excel(name = "产品编码", width = 15)
    @ApiModelProperty(value = "产品编码")
    private String couponId;
	/**产品价格（单位：分）*/
	@Excel(name = "产品价格", width = 15)
    @ApiModelProperty(value = "产品价格（单位：分）")
    private Integer couponPrice;
	/**订购状态码*/
	@Excel(name = "订购状态码", width = 15)
    @ApiModelProperty(value = "订购状态码")
    private String respCode;
	/**订购状态描述*/
	@Excel(name = "订购状态描述", width = 15)
    @ApiModelProperty(value = "订购状态描述")
    private String respMsg;
	/**回调状态码*/
	@Excel(name = "回调状态码", width = 15)
    @ApiModelProperty(value = "回调状态码")
    private String callbackCode;
	/**回调状态描述*/
	@Excel(name = "回调状态描述", width = 15)
    @ApiModelProperty(value = "回调状态描述")
    private String callbackMsg;
    /**省份*/
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;

    /**城市*/
    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;

	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    private String titleName;
    @TableField(exist = false)
    private String rightsName;
    @TableField(exist = false)
    private String sourse;
    @TableField(exist = false)
    private String productImg;
    @TableField(exist = false)
    private String serviceName;
    @TableField(exist = false)
    private String channelId;
    @TableField(exist = false)
    private String callbackUrl;
    @TableField(exist = false)
    private LocalDateTime modifyTime;
    @TableField(exist = false)
    private String createTimeFrom;
    @TableField(exist = false)
    private String createTimeTo;

    /**归属公司:JUNBO=骏伯,HUAYI=华逸*/
    @Excel(name = "归属公司", width = 15, dicCode = "company_owner")
    @ApiModelProperty(value = "归属公司:JUNBO=骏伯,HUAYI=华逸")
    @Dict(dicCode = "company_owner")
    private String companyOwner;


    /**领取来源:login=登录领取,web=网页领取*/
    @Excel(name = "领取来源", width = 15, dicCode = "recharge_source")
    @ApiModelProperty(value = "领取来源:login=登录领取,web=网页领取,code=激活码直充")
    @Dict(dicCode = "recharge_source")
    private String rechargeSource;


    /**兑换码*/
    @Excel(name = "兑换码", width = 15)
    @ApiModelProperty(value = "兑换码")
    private String couponCode;


    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;



    /**子渠道号*/
    @Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;



}
