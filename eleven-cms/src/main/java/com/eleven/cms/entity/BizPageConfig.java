package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 *  Entity
 *
 * <AUTHOR>
 * @date 2023-08-08 15:27:49
 */
@Data
@TableName("xxl_biz_page_config")
public class BizPageConfig {

    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 页面名称
     */
    @TableField("page_name")
    private String pageName;

    /**
     * 页面链接
     */
    @TableField("link")
    private String link;

    /**
     * 页面配置json
     */
    @TableField("config_json")
    private String configJson;

    /**
     * 是否启用 1启用0禁用
     */
    @TableField("is_enable")
    private Integer isEnable;

    /**
     * 备注
     */
    @TableField("remake")
    private String remake;

    /**
     * 
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField("update_time")
    private Date updateTime;

}
