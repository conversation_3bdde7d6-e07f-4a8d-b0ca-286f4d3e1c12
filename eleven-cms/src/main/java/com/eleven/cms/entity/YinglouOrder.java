package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 影楼订单表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
@Data
@TableName("cms_yinglou_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_yinglou_order对象", description="影楼订单表")
public class YinglouOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**订单id*/
	@Excel(name = "订单id", width = 15)
    @ApiModelProperty(value = "订单id")
    private String orderId;
    /**商户订单号*/
    @Excel(name = "商户订单号", width = 15)
    @ApiModelProperty(value = "商户订单号")
    private String outTradeNo;
	/**开通手机号*/
	@Excel(name = "开通手机号", width = 15)
    @ApiModelProperty(value = "开通手机号")
    private String mobile;
	/**开通状态:-1=未使用,0=开通失败,1=开通成功*/
	@Excel(name = "开通状态:-1=未使用,0=开通失败,1=开通成功", width = 15)
    @ApiModelProperty(value = "开通状态:-1=未使用,0=开通失败,1=开通成功")
    private Integer status;
    /**主账号*/
    @Excel(name = "主账号", width = 15)
    @ApiModelProperty(value = "主账号")
    private String majorUser;
	/**支付账号*/
	@Excel(name = "支付账号", width = 15)
    @ApiModelProperty(value = "支付账号")
    private String payUserName;
    /**用户等级 0 主账号 1 次级账号*/
    @Excel(name = "用户等级", width = 15,dicCode = "level")
    @ApiModelProperty(value = "用户等级")
    @Dict(dicCode = "level")
    private Integer levels;
	/**分成金额*/
	@Excel(name = "分成金额", width = 15)
    @ApiModelProperty(value = "分成金额")
    private Integer divideIntoAmount;
	/**订单金额*/
	@Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private Integer totalAmount;

    /**分组日期*/
    @Excel(name = "分组日期", width = 15)
    @ApiModelProperty(value = "分组日期")
    private String payOrderTime;


    /**支付类型*/
    @Excel(name = "支付类型", width = 15)
    @ApiModelProperty(value = "支付类型")
    private String tradeType;

	/**支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中*/
	@Excel(name = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中", width = 15)
    @ApiModelProperty(value = "支付状态:-1=未支付,0=支付失败,1=支付成功,4=已退款,5=退款失败,6=退款中")
    private Integer payStatus;
	/**支付时间*/
	@Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**支付备注*/
    @Excel(name = "支付备注", width = 15)
    @ApiModelProperty(value = "支付备注")
    private String payRemark;
    /**公众号appId*/
    @Excel(name = "公众号appId", width = 15)
    @ApiModelProperty(value = "公众号appId")
    private String appId;

    /**商户号mchId*/
    @Excel(name = "商户号mchId", width = 15)
    @ApiModelProperty(value = "商户号mchId")
    private String mchId;

	/**退款订单号*/
	@Excel(name = "退款订单号", width = 15)
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;




	/**退款备注*/
	@Excel(name = "退款备注", width = 15)
    @ApiModelProperty(value = "退款备注")
    private String refundRemark;
	/**退款时间*/
	@Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "退款时间")
    private Date refundTime;
	/**退款金额*/
	@Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private Integer refundAmount;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;



}
