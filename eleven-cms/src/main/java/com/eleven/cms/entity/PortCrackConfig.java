package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_port_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Data
@TableName("cms_port_crack_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "cms_port_crack_config对象", description = "cms_port_crack_config")
public class PortCrackConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 渠道号
     */
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;

    /**
     * 日志标记
     */
    @Excel(name = "日志标记", width = 15)
    @ApiModelProperty(value = "日志标记")
    private String logTag;


    /**
     * '请求地址'
     */
    @Excel(name = "'请求地址'", width = 15)
    @ApiModelProperty(value = "'请求地址'")
    private String requestUrl;
    /**
     * 获取验证码
     */
    @Excel(name = "获取验证码", width = 15)
    @ApiModelProperty(value = "获取验证码")
    private String getSmsUrl;
    /**
     * 提交验证码
     */
    @Excel(name = "提交验证码", width = 15)
    @ApiModelProperty(value = "提交验证码")
    private String smsCodeUrl;
    /**
     * 查询用户欠费信息
     */
    @Excel(name = "查询用户欠费信息", width = 15)
    @ApiModelProperty(value = "查询用户欠费信息")
    private String queryFeeUrl;
    /**
     * 查询商品订单信息
     */
    @Excel(name = "查询商品订单信息", width = 15)
    @ApiModelProperty(value = "查询商品订单信息")
    private String queryProductUrl;
    /**
     * 校验产品能否办理
     */
    @Excel(name = "校验产品能否办理", width = 15)
    @ApiModelProperty(value = "校验产品能否办理")
    private String checkProductSubUrl;
    /**
     * 查询业务状态
     */
    @Excel(name = "查询业务状态", width = 15)
    @ApiModelProperty(value = "查询业务状态")
    private String queryServiceUrl;
    /**
     * 应用id
     */
    @Excel(name = "应用id", width = 15)
    @ApiModelProperty(value = "应用id")
    private String appId;
    /**
     * 业务类型
     */
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String busiType;
    /**
     * 产品编码
     */
    @Excel(name = "产品编码", width = 15)
    @ApiModelProperty(value = "产品编码")
    private String offerCode;
    /**
     * 加密密钥
     */
    @Excel(name = "加密密钥", width = 15)
    @ApiModelProperty(value = "加密密钥")
    private String secretKey;
    /**
     * 公钥
     */
    @Excel(name = "公钥", width = 15)
    @ApiModelProperty(value = "公钥")
    private String publicKey;
    /**
     * 私钥
     */
    @Excel(name = "私钥", width = 15)
    @ApiModelProperty(value = "私钥")
    private String privateKey;
    /**
     * 是否有效:0=否,1=是
     */
    @Excel(name = "是否有效:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否有效:0=否,1=是")
    private Integer isValid;
    /**
     * 来源地址
     */
    @Excel(name = "来源地址", width = 15)
    @ApiModelProperty(value = "来源地址")
    private String sourceUrl;

    /**
     * app名称
     */
    @Excel(name = "app名称", width = 15)
    @ApiModelProperty(value = "app名称")
    private String appName;

    /**
     * 企业简拼
     */
    @Excel(name = "企业简拼", width = 15)
    @ApiModelProperty(value = "企业简拼")
    private String companyName;

    /**
     * 备注
     */

    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
