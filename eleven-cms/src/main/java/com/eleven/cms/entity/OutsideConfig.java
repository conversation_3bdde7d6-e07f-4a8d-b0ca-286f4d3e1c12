package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_outside_config
 * @Author: jeecg-boot
 * @Date:   2023-04-26
 * @Version: V1.0
 */
@Data
@TableName("cms_outside_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_outside_config对象", description="cms_outside_config")
public class OutsideConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**id*/
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    /**
     * 外部渠道
     */
    @Excel(name = "外部渠道", width = 15)
    @ApiModelProperty(value = "外部渠道")
    private String outChannel;
    /**
     * 通知地址
     */
    @Excel(name = "通知地址", width = 15)
    @ApiModelProperty(value = "通知地址")
    private String notifyUrl;
    /**
     * 通知地址
     */
    @Excel(name = "退订通知地址", width = 15)
    @ApiModelProperty(value = "退订通知地址")
    private String unsubNotifyUrl;

    /**
     * 一天退订通知地址
     */
    @Excel(name = "一天退订通知地址", width = 15)
    @ApiModelProperty(value = "一天退订通知地址")
    private String dayUnsubNotifyUrl;
    /**
     * 扣量比例
     */
    @Excel(name = "扣量比例", width = 15)
    @ApiModelProperty(value = "扣量比例")
    private Integer deductionRatio;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**1启用0禁用*/
    @Excel(name = "1启用0禁用", width = 15)
    @ApiModelProperty(value = "1启用0禁用")
    @Dict(dicCode = "yn")
    private Integer status;
    /**分省业务*/
    @Excel(name = "分省业务", width = 15)
    @ApiModelProperty(value = "分省业务")
    private String provinceBusiness;
    /**业务类型id*/
    @Excel(name = "业务类型id", width = 15)
    @ApiModelProperty(value = "业务类型id")
    private String bizTypeId;


    /**是否破解：1=是，0=否*/
    @Excel(name = "是否破解：1=是，0=否", width = 15)
    @ApiModelProperty(value = "是否破解：1=是，0=否")
    @Dict(dicCode = "is_crack")
    private Integer isCrack;


    /**报备bean*/
    @Excel(name = "报备bean", width = 15)
    @ApiModelProperty(value = "报备bean")
    @Dict(dicCode = "report_bean")
    private String reportBean;

}
