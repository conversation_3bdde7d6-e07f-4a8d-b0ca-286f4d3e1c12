package com.eleven.cms.entity;

import java.util.Date;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 账号配置表 Entity
 *
 * <AUTHOR>
 * @date 2021-06-02 16:57:09
 */
@Data
@TableName("cms_account_cofig")
public class AccountCofig {

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;

    /**
     * 广告平台id
     */
    @TableField("ad_platform_id")
    private String adPlatformId;

    /**
     * 账号
     */
    @TableField("account")
    private String account;

    /**
     * 配置
     */
    @TableField("config")
    private String config;



    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 备用字段
     */
    @TableField("spare_field")
    private String spareField;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /*广告平台*/
    @TableField(exist = false)
    private String adPlatform;

}
