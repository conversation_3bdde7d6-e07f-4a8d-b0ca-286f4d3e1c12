package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_kugou_order
 * @Author: jeecg-boot
 * @Date:   2022-11-24
 * @Version: V1.0
 */
@Data
@TableName("cms_kugou_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_kugou_order对象", description="cms_kugou_order")
public class KugouOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;
	/**直充配额主key，同一个主key下可申请多个直充配额*/
	@Excel(name = "直充配额主key，同一个主key下可申请多个直充配额", width = 15)
    @ApiModelProperty(value = "直充配额主key，同一个主key下可申请多个直充配额")
    private String mainKey;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String outTradeNo;
	/**用户id，最长255字节，主要用于二次校验和后续对账。不填骏伯会把用户手机号记录为你方的用户id*/
	@Excel(name = "用户id，最长255字节，主要用于二次校验和后续对账。不填骏伯会把用户手机号记录为你方的用户id", width = 15)
    @ApiModelProperty(value = "用户id，最长255字节，主要用于二次校验和后续对账。不填骏伯会把用户手机号记录为你方的用户id")
    private String outUserId;
	/**充值的用户id类型，2表示通过手机号，4表示开放平台openid*/
	@Excel(name = "充值的用户id类型，2表示通过手机号，4表示开放平台openid", width = 15)
    @ApiModelProperty(value = "充值的用户id类型，2表示通过手机号，4表示开放平台openid")
    private Integer userType;
	/**手机号或者openid*/
	@Excel(name = "手机号或者openid", width = 15)
    @ApiModelProperty(value = "手机号或者openid")
    private String user;
	/**user_type为2的时候，选择给哪个绑定账号充值。1最近绑定的账号，2最近登录的账号。默认2*/
	@Excel(name = "user_type为2的时候，选择给哪个绑定账号充值。1最近绑定的账号，2最近登录的账号。默认2", width = 15)
    @ApiModelProperty(value = "user_type为2的时候，选择给哪个绑定账号充值。1最近绑定的账号，2最近登录的账号。默认2")
    private Integer bindType;
	/**是否返回用户头像等数据。0否， 1是， 默认0*/
	@Excel(name = "是否返回用户头像等数据。0否， 1是， 默认0", width = 15)
    @ApiModelProperty(value = "是否返回用户头像等数据。0否， 1是， 默认0")
    private Integer retUser;
	/**充值的产品类型，music表示音乐包，svip表示酷狗音乐豪华vip*/
	@Excel(name = "充值的产品类型，music表示音乐包，svip表示酷狗音乐豪华vip", width = 15)
    @ApiModelProperty(value = "充值的产品类型，music表示音乐包，svip表示酷狗音乐豪华vip")
    private String productType;
	/**充值的天数，与申请的直充配额要匹配，一个月的是31天*/
	@Excel(name = "充值的天数，与申请的直充配额要匹配，一个月的是31天", width = 15)
    @ApiModelProperty(value = "充值的天数，与申请的直充配额要匹配，一个月的是31天")
    private Integer days;
	/**签名的方式，目前支持 sha1*/
	@Excel(name = "签名的方式，目前支持 sha1", width = 15)
    @ApiModelProperty(value = "签名的方式，目前支持 sha1")
    private String signType;
	/**参数签名*/
	@Excel(name = "参数签名", width = 15)
    @ApiModelProperty(value = "参数签名")
    private String sign;
	/**请求的时间戳*/
	@Excel(name = "请求的时间戳", width = 15)
    @ApiModelProperty(value = "请求的时间戳")
    private String timestamp;
	/**第三方业务appid， 在user_type=4的时候传*/
	@Excel(name = "第三方业务appid， 在user_type=4的时候传", width = 15)
    @ApiModelProperty(value = "第三方业务appid， 在user_type=4的时候传")
    private String openappid;
	/**直充联合会员id，目前支持（京东：，网易严选：）*/
	@Excel(name = "直充联合会员id，目前支持（京东：，网易严选：）", width = 15)
    @ApiModelProperty(value = "直充联合会员id，目前支持（京东：，网易严选：）")
    private String partner;
	/**订单状态，0未到账，1到账，-1失败，2回收*/
	@Excel(name = "订单状态，0未到账，1到账，-1失败,2回收", width = 15)
    @ApiModelProperty(value = "订单状态，0未到账，1到账，-1失败")
    private Integer status;

    /**充值状态:0=未执行,1=已包月,2=未包月,3=已发送权益,4=校验中*/
    @Excel(name = "充值状态:0=未执行,1=已包月,2=未包月,3=已发送权益,4=校验中", width = 15)
    @ApiModelProperty(value = "充值状态:0=未执行,1=已包月,2=未包月,3=已发送权益,4=校验中")
    private Integer rechargeStatus;

    /**权益月份*/
    @Excel(name = "权益月份", width = 15)
    @ApiModelProperty(value = "权益月份")
    private String rightsMonth;

	/**接口返回状态码*/
	@Excel(name = "接口返回状态码", width = 15)
    @ApiModelProperty(value = "接口返回状态码")
    private Integer apiStatus;
	/**接口返回错误码*/
	@Excel(name = "接口返回错误码", width = 15)
    @ApiModelProperty(value = "接口返回错误码")
    private String apiErrorCode;
	/**接口返回的内容*/
	@Excel(name = "接口返回的内容", width = 15)
    @ApiModelProperty(value = "接口返回的内容")
    private String apiData;
	/**直充的酷狗id加密后的值*/
	@Excel(name = "直充的酷狗id加密后的值", width = 15)
    @ApiModelProperty(value = "直充的酷狗id加密后的值")
    private String drOpenid;
	/**酷狗直充订单号*/
	@Excel(name = "酷狗直充订单号", width = 15)
    @ApiModelProperty(value = "酷狗直充订单号")
    private String kgOrderNumber;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;


    /**业务ID*/
    @Excel(name = "业务ID", width = 15)
    @ApiModelProperty(value = "业务ID")
    private String serviceId;
    /**业务包名*/
    @Excel(name = "业务包名", width = 15)
    @ApiModelProperty(value = "业务包名")
    private String packName;

    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
}
