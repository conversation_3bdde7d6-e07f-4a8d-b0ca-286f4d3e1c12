package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import net.minidev.json.annotate.JsonIgnore;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 彩讯退订记录
 * @Author: jeecg-boot
 * @Date: 2024-05-27
 * @Version: V1.0
 */
@Data
@TableName("cms_caixun_unsubscribe")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "cms_caixun_unsubscribe对象", description = "彩讯退订记录")
public class CmsCaixunUnsubscribe implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 彩讯渠道号
     */
    @Excel(name = "彩讯渠道号", width = 15)
    @ApiModelProperty(value = "彩讯渠道号")
    @JsonProperty("channelCode")
    private String caixunChannelCode;
    /**
     * 渠道号
     */
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    @JsonProperty("channel")
    private String channelCode;
    /**
     * channelSource
     */
    @Excel(name = "channelSource", width = 15)
    @ApiModelProperty(value = "channelSource")
    private String channelSource;
    /**
     * 订购时间
     */
    @Excel(name = "订购时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订购时间")
    private Date orderTime;
    /**
     * 退订时间
     */
    @Excel(name = "退订时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退订时间")
    private Date unsubscribeTime;
}
