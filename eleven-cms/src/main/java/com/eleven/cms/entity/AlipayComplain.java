package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.alipay.api.domain.ReplyRecordResponse;
import com.alipay.api.internal.mapping.ApiField;
import com.alipay.api.internal.mapping.ApiListField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 支付宝投诉明细
 * @Author: jeecg-boot
 * @Date:   2023-04-04
 * @Version: V1.0
 */
@Data
@TableName("cms_alipay_complain")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_alipay_complain对象", description="支付宝投诉明细")
public class AlipayComplain implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**商户号appId*/
	@Excel(name = "商户号appId", width = 15)
    @ApiModelProperty(value = "商户号appId")
    private String appId;
	/**支付宝侧投诉单号*/
	@Excel(name = "支付宝侧投诉单号", width = 15)
    @ApiModelProperty(value = "支付宝侧投诉单号")
    private String complainEventId;
	/**状态
     待处理：MERCHANT_PROCESSING
     已处理：MERCHANT_FEEDBACKED
     投诉完结：FINISHED
     投诉关闭：CANCELLED
     客服处理中：PLATFORM_PROCESSING
     客服处理完结：PLATFORM_FINISH
     投诉关闭：CLOSED*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String complainStatus;
	/**支付宝交易号*/
	@Excel(name = "支付宝交易号", width = 15)
    @ApiModelProperty(value = "支付宝交易号")
    private String tradeNo;
	/**商家订单号*/
	@Excel(name = "商家订单号", width = 15)
    @ApiModelProperty(value = "商家订单号")
    private String merchantOrderNo;
	/**投诉单创建时间*/
	@Excel(name = "投诉单创建时间", width = 15)
    @ApiModelProperty(value = "投诉单创建时间")
    private String gmtCreate;
	/**投诉单修改时间*/
	@Excel(name = "投诉单修改时间", width = 15)
    @ApiModelProperty(value = "投诉单修改时间")
    private String gmtModified;
	/**投诉单完结时间*/
	@Excel(name = "投诉单完结时间", width = 15)
    @ApiModelProperty(value = "投诉单完结时间")
    private String gmtFinished;
	/**用户投诉诉求*/
	@Excel(name = "用户投诉诉求", width = 15)
    @ApiModelProperty(value = "用户投诉诉求")
    private String leafCategoryName;
	/**用户投诉原因*/
	@Excel(name = "用户投诉原因", width = 15)
    @ApiModelProperty(value = "用户投诉原因")
    private String complainReason;
	/**用户投诉内容*/
	@Excel(name = "用户投诉内容", width = 15)
    @ApiModelProperty(value = "用户投诉内容")
    private String complainContent;
	/**投诉人电话号码*/
	@Excel(name = "投诉人电话号码", width = 15)
    @ApiModelProperty(value = "投诉人电话号码")
    private String phoneNo;
	/**交易金额*/
	@Excel(name = "交易金额", width = 15)
    @ApiModelProperty(value = "交易金额")
    private String tradeAmount;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String complainRemark;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**签约状态(0:未签约 1:签约成功 2:签约失败)*/
    @Excel(name = "签约状态(0:未签约 1:签约成功 2:签约失败 3:解约)", width = 15)
    @ApiModelProperty(value = "签约状态(0:未签约 1:签约成功 2:签约失败 3:解约 4:签约失效)")
    private Integer signStatus;
    /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
    @Excel(name = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败", width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private java.lang.Integer refundStatus;
    /**
     * 用户与商家之间的协商记录
     */
    @TableField(exist = false)
    private String replyDetailInfos;

    /**
     * 投诉图片
     */
    @TableField(exist = false)
    private String images;

    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**业务名字*/
    @Excel(name = "业务名字", width = 15)
    @ApiModelProperty(value = "业务名字")
    private String businessName;

    /**支付时间*/
    @Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**手机号*/
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;

//    /**
//     * 用户与商家之间的协商记录
//     */
//    @TableField(exist = false)
//    private List<ReplyRecordResponse> replyDetailInfos;
//    /**
//     * 投诉图片
//     */
//    @TableField(exist = false)
//    List<String> images;
}
