package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * 电信订单entity
 *
 * @author: cai lei
 * @create: 2021-08-19 17:13
 */
@Data
@TableName("cms_telecom_order")
public class TelecomOrder {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;
    @TableField("version")
    private Integer version; //接口版本
    @TableField("orderid")
    @Excel(name = "订单号", width = 15)
    private String orderid; //订单号
    @TableField("phone")
    @Excel(name = "手机号", width = 15)
    private String phone; //手机号
    @TableField("correlator")
    @Excel(name = "集团订购流水号", width = 15)
    private String correlator; //集团订购流水号
    @TableField("res_code")
    @Excel(name = "计费结果", width = 15)
    private String resCode; //计费结果
    @TableField("res_desc")
    @Excel(name = "计费结果说明", width = 15)
    private String resDesc; //计费结果说明
    @TableField("fee")
    @Excel(name = "费用", width = 15)
    private String fee; //费用
    @TableField("type")
    @Excel(name = "通知类型", width = 15,dicCode = "tianyi_order_status")
    @Dict(dicCode = "tianyi_order_status")
    private String type; //通知类型
    @TableField("ext_data")
    private String extData; //渠道透传参数
    @TableField("unsub_flag")
    private Integer unsubFlag; //当月是否退订
    @TableField("sign")
    private String sign; //签名
    @TableField("send_status")
    @Dict(dicCode = "qy_send_status")
//    @Excel(name = "PP视频发放状态", width = 15,dicCode = "qy_send_status")
    private Integer sendStatus; //PP视频发放状态
    @TableField("video_send_status")
    @Excel(name = "视听会员发放状态", width = 15,dicCode = "qy_send_status")
    @Dict(dicCode = "qy_send_status")
    private Integer videoSendStatus; //视频会员发送状态
    @TableField("province")
    @Excel(name = "省份", width = 15)
    private String province; //省份
    @TableField("city")
    private String city; //城市
    @TableField("status")
    @Excel(name = "订购状态", width = 15,dicCode = "tianyi_order_status")
    @Dict(dicCode = "tianyi_order_status")
    private Integer status; //订购状态
    @TableField("create_time")
    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime; //创建时间
    @TableField("modify_time")
    private Date modifyTime; //修改时间

    @TableField(exist = false)
    private String createTimeFrom;
    @TableField(exist = false)
    private String createTimeTo;

}
