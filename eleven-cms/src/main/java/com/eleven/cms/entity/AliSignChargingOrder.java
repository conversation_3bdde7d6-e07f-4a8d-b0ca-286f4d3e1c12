package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_ali_sign_charging_order
 * @Author: jeecg-boot
 * @Date:   2022-12-30
 * @Version: V1.0
 */
@Data
@TableName("cms_ali_sign_charging_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_ali_sign_charging_order对象", description="cms_ali_sign_charging_order")
public class AliSignChargingOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**自增主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "自增主键")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**商户签约号*/
	@Excel(name = "商户签约号", width = 15)
    @ApiModelProperty(value = "商户签约号")
    private String externalAgreementNo;
    /**商户号appId*/
    @Excel(name = "商户号appId", width = 15)
    @ApiModelProperty(value = "商户号appId")
    private String appId;

    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**业务名字*/
    @Excel(name = "业务名字", width = 15)
    @ApiModelProperty(value = "业务名字")
    private String businessName;
    /**订单号*/
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**支付宝订单号*/
    @Excel(name = "支付宝订单号", width = 15)
    @ApiModelProperty(value = "支付宝订单号")
    private String tradeNo;
    /**买家支付宝账号*/
    @Excel(name = "买家支付宝账号", width = 15)
    @ApiModelProperty(value = "买家支付宝账号")
    private String buyerLogonId;

	/**订单状态(0:未支付 1:成功,2:失败)*/
	@Excel(name = "订单状态(0:未支付 1:成功,2:失败)", width = 15)
    @ApiModelProperty(value = "订单状态(0:未支付 1:成功,2:失败)")
    private Integer orderStatus;
	/**订单金额*/
	@Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private String orderAmount;
	/**订单时间*/
	@Excel(name = "订单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单时间")
    private Date orderTime;

    /**支付时间*/
    @Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

	/**创建时间*/
    @Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新时间*/
    @Excel(name = "更新时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**支付宝协议号*/
	@Excel(name = "支付宝协议号", width = 15)
    @ApiModelProperty(value = "支付宝协议号")
    private String agreementNo;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String alipayUserId;
    /**支付月份*/
    @Excel(name = "支付月份", width = 15)
    @ApiModelProperty(value = "支付月份")
    private String payMonth;


    /**退款订单号*/
    @Excel(name = "退款订单号", width = 15)
    @ApiModelProperty(value = "退款订单号")
    private java.lang.String refundOrderNo;
    /**退款备注*/
    @Excel(name = "退款备注", width = 15)
    @ApiModelProperty(value = "退款备注")
    private java.lang.String refundRemark;
    /**客服备注*/
    @Excel(name = "客服备注", width = 15)
    @ApiModelProperty(value = "客服备注")
    private java.lang.String waiterRemark;
    /**退款金额*/
    @Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private java.lang.String refundAmount;
    /**退款时间*/
    @Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "退款时间")
    private java.util.Date refundTime;
    /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
    @Excel(name = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败", width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private java.lang.Integer refundStatus;
    /**签约状态(0:未签约 1:签约成功 2:签约失败)*/
    @Excel(name = "签约状态(0:未签约 1:签约成功 2:签约失败 3:解约)", width = 15)
    @ApiModelProperty(value = "签约状态(0:未签约 1:签约成功 2:签约失败 3:解约 4:签约失效)")
    private Integer signStatus;
    /**解约时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "解约时间")
    private java.util.Date unsignTime;

    /**充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充*/
    @Excel(name = "充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充", width = 15)
    @ApiModelProperty(value = "充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充")
    private Integer rightsStatus;

    /**子渠道号*/
    @Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private java.lang.String subChannel;


    /**上报状态:0=未上报,1=上报成功,2=上报失败*/
    @Excel(name = "上报状态:0=未上报,1=上报成功,2=上报失败", width = 15)
    @ApiModelProperty(value = "上报状态:0=未上报,1=上报成功,2=上报失败")
    private Integer callbackStatus;

    /**业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃*/
    @Excel(name = "业务类型", width = 15, dicCode = "biz_type")
    @ApiModelProperty(value = "业务类型:MEMBER_ALIPAY=支付宝会员,VRBT_YD_ALIPAY=移动视频彩铃,VRBT_DX_ALIPAY=电信视频彩铃")
    @Dict(dicCode = "biz_type")
    private String bizType;

}
