package com.eleven.cms.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_province_business_channel_info
 * @Author: jeecg-boot
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@ApiModel(value="cms_province_business_channel_config对象", description="cms_province_business_channel_config")
@Data
@TableName("cms_province_business_channel_info")
public class ProvinceBusinessChannelInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
	@ApiModelProperty(value = "id")
	private String id;
	/**渠道号*/
	@ApiModelProperty(value = "渠道号")
	private String provinceChannelConfigId;
	/**省份*/
	@Excel(name = "省份", width = 15)
	@ApiModelProperty(value = "省份")
	private String province;
	/**是否启用*/
	@Excel(name = "是否启用", width = 15)
	@ApiModelProperty(value = "是否启用")
	private boolean enable;
	/**限量*/
	@Excel(name = "限量", width = 15)
	@ApiModelProperty(value = "限量")
	private Integer limitAmount;
    /**限量接收短信手机号*/
    @Excel(name = "接收短信手机号", width = 15)
    @ApiModelProperty(value = "接收短信手机号")
    private String receiveMobile;
    /**限量接收短信手机号*/
    @Excel(name = "向宏限量", width = 15)
    @ApiModelProperty(value = "向宏限量")
    @TableField(strategy= FieldStrategy.IGNORED)
    private Integer xianghongLimit;
    /**限量接收短信手机号*/
    @Excel(name = "小峰限量", width = 15)
    @ApiModelProperty(value = "小峰限量")
    @TableField(strategy= FieldStrategy.IGNORED)
    private Integer xiaofengLimit;
    /**限量接收短信手机号*/
    @Excel(name = "董松炜限量", width = 15)
    @ApiModelProperty(value = "董松炜限量")
    @TableField(strategy= FieldStrategy.IGNORED)
    private Integer dongswLimit;
    /**限量接收短信手机号*/
    @Excel(name = "cpa限量", width = 15)
    @ApiModelProperty(value = "cpa限量")
    @TableField(strategy= FieldStrategy.IGNORED)
    private Integer cpaLimit;
    /**cpa接收短信手机号*/
    @Excel(name = "cpa接收短信手机号", width = 15)
    @ApiModelProperty(value = "cpa接收短信手机号")
    private String cpaReceiveMobile;
    /**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "创建时间")

	private Date createTime;
	/**修改人*/
	@ApiModelProperty(value = "修改人")
	private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "修改时间")
	private Date updateTime;
//
//    /**
//     * 企业彩铃省份联合限量数
//     */
//    private String provinceUnionLimit;

}
