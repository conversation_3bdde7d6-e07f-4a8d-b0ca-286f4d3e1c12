package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 影楼铃音表
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
@Data
@TableName("cms_yinglou_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_yinglou_ring对象", description="影楼铃音表")
public class YinglouRing implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**开通手机号*/
    @Excel(name = "开通手机号", width = 15)
    @ApiModelProperty(value = "开通手机号")
    private String mobile;
    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    /**铃音类型*/
    @ApiModelProperty(value = "铃音类型")
    @Excel(name = "铃音类型", width = 15)
    private Integer ringType;

    /**铃音图片地址*/
    @ApiModelProperty(value = "铃音图片地址")
    @Excel(name = "铃音图片地址", width = 15)
    private String imageUrls;

    /**阿里云视频合成任务id*/
    @ApiModelProperty(value = "阿里云视频合成任务id")
    @Excel(name = "阿里云视频合成任务id", width = 15)
    private String aliVideoJobId;
    /**模板id*/
    @ApiModelProperty(value = "模板id")
    @Excel(name = "模板id", width = 15)
    private String templateId;
    /**铃音名称*/
    @Excel(name = "铃音名称", width = 15)
    @ApiModelProperty(value = "铃音名称")
    private String ringName;
    /**铃音路径*/
    @Excel(name = "铃音路径", width = 15)
    @ApiModelProperty(value = "铃音路径")
    private String ringUrl;

    /**激活状态(激活失败0，激活成功1,未上传2,上传成功3,上传失败4)*/
    @Excel(name = "激活状态(激活失败0，激活成功1,未上传2,上传成功3,上传失败4)", width = 15)
    @ApiModelProperty(value = "激活状态(激活失败0，激活成功1,未上传2,上传成功3,上传失败4)")
    private Integer status;
    /**回调状态描述*/
    @Excel(name = "回调状态描述", width = 15)
    @ApiModelProperty(value = "回调状态描述")
    private String callbackMsg;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

	/**铃音id*/
	@Excel(name = "铃音id", width = 15)
    @ApiModelProperty(value = "铃音id")
    private String ringId;
	/**商户ID*/
	@Excel(name = "商户ID", width = 15)
    @ApiModelProperty(value = "商户ID")
    private String circleId;
	/**铃音标识*/
	@Excel(name = "铃音标识", width = 15)
    @ApiModelProperty(value = "铃音标识")
    private String transactionId;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
