package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Author: <EMAIL>
 * Date: 2020/4/20 15:41
 * Desc:号码归属地查询结果
 */
@Data
@TableName("cms_mobile_region_result")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_mobile_region_result对象", description="手机号运营商")
public class MobileRegion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键id*/
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private java.lang.String id;

    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String phoneCode;

    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;

    @Excel(name = "省份编码", width = 15)
    @ApiModelProperty(value = "省份编码")
    private String provinceId;

    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;

    @Excel(name = "城市编码", width = 15)
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @Excel(name = "区编码", width = 15)
    @ApiModelProperty(value = "区编码")
    private String areaCode;
    /**
     * 运营商,1移动用户,3联通用户,4电信用户
     */
    @Excel(name = "运营商,1移动用户,3联通用户,4电信用户", width = 15)
    @ApiModelProperty(value = "运营商,1移动用户,3联通用户,4电信用户")
    private String operator;
    @Excel(name = "运营商", width = 15)
    @ApiModelProperty(value = "运营商")
    private String optDesc;
    /**
     * 是否携号转网 0=否，1=是
     */
    @Excel(name = "是否携号转网 0=否，1=是", width = 15)
    @ApiModelProperty(value = "是否携号转网 0=否，1=是")
    private Integer isResult;

    /**
     * 运营商,1移动用户,3联通用户,4电信用户
     */
    @Excel(name = "运营商,1移动用户,3联通用户,4电信用户", width = 15)
    @ApiModelProperty(value = "运营商,1移动用户,3联通用户,4电信用户")
    private String reverseOperator;
    @Excel(name = "运营商", width = 15)
    @ApiModelProperty(value = "运营商")
    private String reverseOptDesc;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

}
