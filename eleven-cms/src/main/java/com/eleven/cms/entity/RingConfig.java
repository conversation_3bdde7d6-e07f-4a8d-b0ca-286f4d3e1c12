package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 铃音配置
 * @Author: jeecg-boot
 * @Date:   2022-09-20
 * @Version: V1.0
 */
@Data
@TableName("cms_ring_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_ring_config对象", description="铃音配置")
public class RingConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**铃音编号*/
	@Excel(name = "铃音编号", width = 15)
    @ApiModelProperty(value = "铃音编号")
    private String ringNo;
	/**铃音名称*/
	@Excel(name = "铃音名称", width = 15)
    @ApiModelProperty(value = "铃音名称")
    private String ringName;
	/**铃音路径*/
	@Excel(name = "铃音路径", width = 15)
    @ApiModelProperty(value = "铃音路径")
    private String ringUrl;
}
