package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 微信代金券配置
 * @Author: jeecg-boot
 * @Date:   2024-08-09
 * @Version: V1.0
 */
@Data
@TableName("cms_wechat_coupon_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_wechat_coupon_config对象", description="微信代金券配置")
public class WechatCouponConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**批次号*/
	@Excel(name = "批次号", width = 15)
    @ApiModelProperty(value = "批次号")
    private String stockId;
	/**公众号appId*/
	@Excel(name = "公众号appId", width = 15)
    @ApiModelProperty(value = "公众号appId")
    private String appId;
	/**商户号mchId*/
	@Excel(name = "商户号mchId", width = 15)
    @ApiModelProperty(value = "商户号mchId")
    private String mchId;
	/**是否激活:0=否,1=是*/
	@Excel(name = "是否激活:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否激活:0=否,1=是")
    private Integer isValid;
	/**APIv3密钥*/
	@Excel(name = "APIv3密钥", width = 15)
    @ApiModelProperty(value = "APIv3密钥")
    private String apiKey;
	/**商户API证书的证书序列号*/
	@Excel(name = "商户API证书的证书序列号", width = 15)
    @ApiModelProperty(value = "商户API证书的证书序列号")
    private String mchSerialNo;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
