package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 券码重复数据查询
 * @Author: jeecg-boot
 * @Date:   2023-10-25
 * @Version: V1.0
 */
@Data
@TableName("cms_coupon_code_repeat_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_coupon_code_repeat_order对象", description="券码重复数据查询")
public class CouponCodeRepeatOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**兑换码*/
	@Excel(name = "兑换码", width = 15)
    @ApiModelProperty(value = "兑换码")
    private String couponCode;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderId;
	/**用户Id*/
	@Excel(name = "用户Id", width = 15)
    @ApiModelProperty(value = "用户Id")
    private String userId;
	/**激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中*/
	@Excel(name = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中", width = 15)
    @ApiModelProperty(value = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中")
    private Integer status;
	/**发码时间*/
	@Excel(name = "发码时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "发码时间")
    private Date sendTime;
	/**扩展字段（咪咕互娱透传用）*/
	@Excel(name = "扩展字段（咪咕互娱透传用）", width = 15)
    @ApiModelProperty(value = "扩展字段（咪咕互娱透传用）")
    private String extrInfo;
	/**区服*/
	@Excel(name = "区服", width = 15)
    @ApiModelProperty(value = "区服")
    private String sendServer;
	/**角色*/
	@Excel(name = "角色", width = 15)
    @ApiModelProperty(value = "角色")
    private String sendRole;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;



    /**手机号*/
    @ApiModelProperty(value = "手机号")
    private String mobile;


    /**订购状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)*/
    @Excel(name = "订购状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)", width = 15)
    @ApiModelProperty(value = "订购状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)")
    private Integer openStatus;

    /**订购时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "订购时间")
    private Date openTime;
}
