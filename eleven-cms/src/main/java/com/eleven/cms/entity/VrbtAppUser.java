package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 视频彩铃app用户
 * @Author: jeecg-boot
 * @Date:   2024-04-16
 * @Version: V1.0
 */
@Data
@TableName("cms_vrbt_app_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_vrbt_app_user对象", description="视频彩铃app用户")
public class VrbtAppUser implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**包月状态(-1=初始,0=未包月,1=已包月)*/
	@Excel(name = "包月状态(-1=初始,0=未包月,1=已包月)", width = 15)
    @ApiModelProperty(value = "包月状态(-1=初始,0=未包月,1=已包月)")
    private Integer subStatus;
	/**总积分*/
	@Excel(name = "总积分", width = 15)
    @ApiModelProperty(value = "总积分")
    private Integer totalPoints;
    /**签到天数*/
    @Excel(name = "签到天数", width = 15)
    @ApiModelProperty(value = "签到天数")
    private Integer signInDay;
	/**注册时间*/
	@Excel(name = "注册时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "注册时间")
    private Date registerTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    /**版本号*/
    @Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private Integer version;
}
