package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 联联分销充值记录
 * @Author: jeecg-boot
 * @Date:   2024-05-14
 * @Version: V1.0
 */
@Data
@TableName("cms_lianlian_charge_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_lianlian_charge_log对象", description="联联分销充值记录")
public class LianlianChargeLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**联联渠道订单号*/
    @Excel(name = "联联渠道订单号", width = 15)
    @ApiModelProperty(value = "联联渠道订单号")
    private String channelOrderId;

	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**业务id*/
	@Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private String serviceId;
	/**权益月份*/
	@Excel(name = "权益月份", width = 15)
    @ApiModelProperty(value = "权益月份")
    private String rightsMonth;
	/**权益包名*/
	@Excel(name = "权益包名", width = 15)
    @ApiModelProperty(value = "权益包名")
    private String packName;
	/**充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充,4=已核销,5=退款失败,6=退款中*/
	@Excel(name = "充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充,4=已核销,5=退款失败,6=退款中", width = 15)
    @ApiModelProperty(value = "充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充,4=已核销,5=退款失败,6=退款中")
    private Integer status;

    /**退款订单号*/
    @Excel(name = "退款订单号", width = 15)
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;


    /**退款备注*/
    @Excel(name = "退款备注", width = 15)
    @ApiModelProperty(value = "退款备注")
    private String refundRemark;

    /**预约状态 0 未预约 1 已预约 2 已取消*/
    @Excel(name = "预约状态 0 未预约 1 已预约 2 已取消", width = 15)
    @ApiModelProperty(value = "预约状态 0 未预约 1 已预约 2 已取消")
    private Integer bookingStatus;

    /**联联分销预约时间*/
    @Excel(name = "联联分销预约时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date bookingDate;

	/**预约要直充的时间,即订单生效的时间*/
	@Excel(name = "预约要直充的时间,即订单生效的时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "预约要直充的时间,即订单生效的时间")
    private Date scheduledTime;
    /**核销时间*/
    @Excel(name = "核销时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date  completeDate;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**产品ID*/
	@Excel(name = "产品ID", width = 15)
    @ApiModelProperty(value = "产品ID")
    private String productId;
	/**套餐ID*/
	@Excel(name = "套餐ID", width = 15)
    @ApiModelProperty(value = "套餐ID")
    private String itemId;
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private String productName;
	/**身份证号码*/
	@Excel(name = "身份证号码", width = 15)
    @ApiModelProperty(value = "身份证号码")
    private String idCard;
	/**名字*/
	@Excel(name = "名字", width = 15)
    @ApiModelProperty(value = "名字")
    private String customerName;
	/**收货号码*/
	@Excel(name = "收货号码", width = 15)
    @ApiModelProperty(value = "收货号码")
    private String customerPhoneNumber;
	/**收货地址*/
	@Excel(name = "收货地址", width = 15)
    @ApiModelProperty(value = "收货地址")
    private String address;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String memo;



    /**预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明*/
    @Excel(name = "预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明", width = 15)
    @ApiModelProperty(value = "预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明")
    private Integer bookingType;


    /**订单类型 0 话费支付 1 在线支付*/
    @Excel(name = "订单类型 0 话费支付 1 在线支付", width = 15)
    @ApiModelProperty(value = "订单类型 0 话费支付 1 在线支付")
    private Integer orderType;
    /*预约地址*/
    @Excel(name = "预约地址", width = 15)
    @ApiModelProperty(value = "预约地址")
    private String bookingUrl;

    @Excel(name = "商家电话", width = 15)
    @ApiModelProperty(value = "商家电话")
    private String phoneNumber;


    @Excel(name = "联联小订单号", width = 15)
    @ApiModelProperty(value = "联联小订单号")
    private String minOrderNo;

    @Excel(name = "核销码图片地址", width = 15)
    @ApiModelProperty(value = "核销码图片地址")
    private String codeImgUrl;


    /**渠道结算价(分)产品表（channel_price）*/
	@Excel(name = "渠道结算价(分)产品表（channel_price）", width = 15)
    @ApiModelProperty(value = "渠道结算价(分)产品表（channel_price）")
    private Integer settlePrice;


    /**申请退款金额*/
    @Excel(name = "申请退款金额", width = 15)
    @ApiModelProperty(value = "申请退款金额")
    private Integer applyAmount;
    /**真实退款金额*/
    @Excel(name = "真实退款金额", width = 15)
    @ApiModelProperty(value = "真实退款金额")
    private Integer refundAmount;

	/**售价(分)产品表（sale_price）*/
	@Excel(name = "售价(分)产品表（sale_price）", width = 15)
    @ApiModelProperty(value = "售价(分)产品表（sale_price）")
    private Integer thirdSalePrice;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**游玩日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "游玩日期")
    private Date travelDate;

    /**系统备注*/
    @Excel(name = "系统备注", width = 15)
    @ApiModelProperty(value = "系统备注")
    private String remark;
    /**订购状态描述*/
    @Excel(name = "订购状态描述", width = 15)
    @ApiModelProperty(value = "订购状态描述")
    private String respMsg;

	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
