package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 数据回执记录
 * @Author: jeecg-boot
 * @Date:   2023-04-23
 * @Version: V1.0
 */
@Data
@TableName("qycl_data_notify_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_data_notify_log对象", description="数据回执记录")
public class DataNotifyLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**来源*/
	@Excel(name = "来源", width = 15)
    @ApiModelProperty(value = "来源")
    private String source;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**状态(0=未开通,1=已开通,2=已退订)*/
	@Excel(name = "状态(0=未开通,1=已开通,2=已退订)", width = 15)
    @ApiModelProperty(value = "状态(0=未开通,1=已开通,2=已退订)")
    private Integer status;
    /**校验包月状态(60分钟退订校验)*/
    @Excel(name = "60分钟退订校验", width = 15, dicCode = "verify_status")
    @ApiModelProperty(value = "60分钟退订校验(-1=未校验,0=未包月,1=已包月)")
    @Dict(dicCode = "verify_status")
    private java.lang.Integer verifyStatus;
    /**校验包月状态(1天退订校验)*/
    @Excel(name = "1天退订校验", width = 15, dicCode = "verify_status")
    @ApiModelProperty(value = "1天退订校验(-1=未校验,0=未包月,1=已包月)")
    @Dict(dicCode = "verify_status")
    private java.lang.Integer verifyStatusDaily;

    /**归属公司*/
    @Excel(name = "归属公司", width = 15)
    @ApiModelProperty(value = "归属公司")
    private String companyOwner;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
    @Excel(name = "更新时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**开通来源*/
    @Excel(name = "开通来源", width = 15)
    @ApiModelProperty(value = "开通来源")
    private String operSystem;

    /**完成时间*/
    @Excel(name = "完成时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private Date finishedTime;


    /**状态*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String state;

    /**描述*/
    @Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String descMsg;


    /**省份*/
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
    /**城市*/
    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;

}
