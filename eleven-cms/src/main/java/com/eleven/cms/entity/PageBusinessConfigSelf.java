package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 落地页管理自有 Entity
 *
 * <AUTHOR>
 * @date 2024-07-09 16:06:17
 */
@Data
@TableName("xxl_page_business_config_self")
public class PageBusinessConfigSelf {

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 页面配置json
     */
    @TableField("config_json")
    private String configJson;

    /**
     * 页面名称
     */
    @TableField("page_name")
    private String pageName;

    /**
     * 页面链接
     */
    @TableField("page_url")
    private String pageUrl;

    /**
     * 归属
     */
    @TableField("biz_owner")
    private String bizOwner;

    /**
     * 1启用0禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     *
     */
    @TableField("create_time")
    private Date createTime;

    /**
     *
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否过滤app空包名:1=过滤,0=不过滤
     */
    @TableField("app_pkg_null_filter")
    private Integer appPkgNullFilter;
}
