package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 渠道订购
 * @Author: jeecg-boot
 * @Date:   2024-01-22
 * @Version: V1.0
 */
@Data
@TableName("cms_order_tarck")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_order_tarck对象", description="渠道订购")
public class OrderTarck implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**运营商,1移动用户,3联通用户,4电信用户*/
	@Excel(name = "运营商,1移动用户,3联通用户,4电信用户", width = 15)
    @ApiModelProperty(value = "运营商,1移动用户,3联通用户,4电信用户")
    private String isp;
	/**业务类型,vrbt:视频彩铃,rt:振铃*/
	@Excel(name = "业务类型,vrbt:视频彩铃,rt:振铃", width = 15)
    @ApiModelProperty(value = "业务类型,vrbt:视频彩铃,rt:振铃")
    private String bizType;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**子渠道号*/
	@Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private String subChannel;


    /**状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)*/
    @Excel(name = "开通状态", width = 15,dicCode = "subscribe_status")
    @ApiModelProperty(value = "状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)")
    @Dict(dicCode = "subscribe_status")
    private java.lang.Integer status;

	/**订购结果*/
	@Excel(name = "订购结果", width = 15)
    @ApiModelProperty(value = "订购结果")
    private String result;

    @Excel(name = "当前订购状态校验", width = 15, dicCode = "verify_status")
    @ApiModelProperty(value = "当前订购状态校验")
    @Dict(dicCode = "verify_status")
    private java.lang.Integer verifyStatus;

	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;

    /**城市*/
    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private java.lang.String city;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**查询时间*/
	@Excel(name = "查询时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "查询时间")
    private Date queryTime;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
