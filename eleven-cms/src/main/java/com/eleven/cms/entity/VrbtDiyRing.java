package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_vrbt_diy_ring
 * @Author: jeecg-boot
 * @Date:   2023-07-25
 * @Version: V1.0
 */
@Data
@TableName("cms_vrbt_diy_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_vrbt_diy_ring对象", description="cms_vrbt_diy_ring")
public class VrbtDiyRing implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**视频彩铃diy关联id*/
	@Excel(name = "视频彩铃diy关联id", width = 15)
    @ApiModelProperty(value = "视频彩铃diy关联id")
    private java.lang.String vrbtDiyVideoId;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
	/**视频铃声路径*/
	@Excel(name = "视频铃声路径", width = 15)
    @ApiModelProperty(value = "视频铃声路径")
    private java.lang.String videoPath;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**阿里云文字转语音任务id*/
	@Excel(name = "阿里云文字转语音任务id", width = 15)
    @ApiModelProperty(value = "阿里云文字转语音任务id")
    private java.lang.String aliTtsJobId;
	/**阿里云视频合成任务id*/
	@Excel(name = "阿里云视频合成任务id", width = 15)
    @ApiModelProperty(value = "阿里云视频合成任务id")
    private java.lang.String aliVideoJobId;
	/**铃音类型 1=最早的文字合成,2=diy类型(图片合成),3=个人彩铃(预制铃声),4=模板类型,5=视频上传铃声*/
	@Excel(name = "铃音类型 2=diy类型(图片合成),4=模板类型,5=视频上传铃声", width = 15)
    @ApiModelProperty(value = "铃音类型 2=diy类型(图片合成),4=模板类型,5=视频上传铃声")
    private java.lang.Integer ringType;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
	/**imageUrls*/
	@Excel(name = "imageUrls", width = 15)
    @ApiModelProperty(value = "imageUrls")
    private java.lang.String imageUrls;
	/**铃声制作状态 0制作中1制作完成*/
	@Excel(name = "铃声制作状态 0制作中1制作完成", width = 15)
    @ApiModelProperty(value = "铃声制作状态 0制作中1制作完成")
    private java.lang.Integer ringMakeStatus;
	/**子渠道号*/
	@Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private java.lang.String subChannel;
    /**背景音乐*/
    @ApiModelProperty(value = "bgm_url")
    private String bgmUrl;
    @TableField(exist = false)
    private Integer auditStatus;
    @TableField(exist = false)
    private String ringName;
}
