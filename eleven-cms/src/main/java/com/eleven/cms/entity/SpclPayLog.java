package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_spcl_pay_log
 * @Author: jeecg-boot
 * @Date:   2022-04-27
 * @Version: V1.0
 */
@Data
@TableName("cms_spcl_pay_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_spcl_pay_log对象", description="cms_spcl_pay_log")
public class SpclPayLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String outTradeNo;
	/**买家手机号*/
	@Excel(name = "买家手机号", width = 15)
    @ApiModelProperty(value = "买家手机号")
    private String mobile;
	/**渠道编码*/
	@Excel(name = "渠道编码", width = 15)
    @ApiModelProperty(value = "渠道编码")
    private String cid;
	/**支付渠道:WXPAY/ALIPAY*/
	@Excel(name = "支付渠道:WXPAY/ALIPAY", width = 15)
    @ApiModelProperty(value = "支付渠道:WXPAY/ALIPAY")
    private String payWay;
	/**购买商品id*/
	@Excel(name = "购买商品id", width = 15)
    @ApiModelProperty(value = "购买商品id")
    private String buyGoodsId;
	/**价格,单位分*/
	@Excel(name = "价格,单位分", width = 15)
    @ApiModelProperty(value = "价格,单位分")
    private String price;
	/**商品的标题/交易标题/订单标题*/
	@Excel(name = "商品的标题/交易标题/订单标题", width = 15)
    @ApiModelProperty(value = "商品的标题/交易标题/订单标题")
    private String subject;
	/**支付状态:0,初始状态已下单;1,支付成功;2,支付失败*/
	@Excel(name = "支付状态:0,初始状态已下单;1,支付成功;2,支付失败", width = 15)
    @ApiModelProperty(value = "支付状态:0,初始状态已下单;1,支付成功;2,支付失败")
    private Integer status;
	/**交易结果状态,对应微信支付result_code*/
	@Excel(name = "交易结果状态,对应微信支付result_code", width = 15)
    @ApiModelProperty(value = "交易结果状态,对应微信支付result_code")
    private String tradeStatus;
	/**订单总金额，对应微信支付total_fee*/
	@Excel(name = "订单总金额，对应微信支付total_fee", width = 15)
    @ApiModelProperty(value = "订单总金额，对应微信支付total_fee")
    private Integer totalAmount;
	/**交易凭证号,对应微信支付transaction_id*/
	@Excel(name = "交易凭证号,对应微信支付transaction_id", width = 15)
    @ApiModelProperty(value = "交易凭证号,对应微信支付transaction_id")
    private String tradeNo;
	/**该笔交易的买家付款时间,对应微信支付time_end*/
	@Excel(name = "该笔交易的买家付款时间,对应微信支付time_end", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "该笔交易的买家付款时间,对应微信支付time_end")
    private Date payTime;
	/**买家支付宝账号对应的支付宝唯一用户号*/
	@Excel(name = "买家支付宝账号对应的支付宝唯一用户号", width = 15)
    @ApiModelProperty(value = "买家支付宝账号对应的支付宝唯一用户号")
    private String buyerId;
	/**商户APPID,支付宝和微信都有此信息*/
	@Excel(name = "商户APPID,支付宝和微信都有此信息", width = 15)
    @ApiModelProperty(value = "商户APPID,支付宝和微信都有此信息")
    private String appId;
	/**微信商户号*/
	@Excel(name = "微信商户号", width = 15)
    @ApiModelProperty(value = "微信商户号")
    private String mchId;
	/**微信用户在商户appid下的唯一标识*/
	@Excel(name = "微信用户在商户appid下的唯一标识", width = 15)
    @ApiModelProperty(value = "微信用户在商户appid下的唯一标识")
    private String openId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
