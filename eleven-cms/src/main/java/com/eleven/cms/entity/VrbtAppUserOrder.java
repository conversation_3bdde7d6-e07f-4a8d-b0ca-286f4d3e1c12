package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 视频彩铃app用户订单
 * @Author: jeecg-boot
 * @Date:   2024-04-16
 * @Version: V1.0
 */
@Data
@TableName("cms_vrbt_app_user_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_vrbt_app_user_order对象", description="视频彩铃app用户订单")
public class VrbtAppUserOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderNo;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**订单积分*/
	@Excel(name = "订单积分", width = 15)
    @ApiModelProperty(value = "订单积分")
    private Integer orderPoints;
	/**包月状态(-1=初始,0=未包月,1=已包月)*/
	@Excel(name = "包月状态(-1=初始,0=未包月,1=已包月)", width = 15)
    @ApiModelProperty(value = "包月状态(-1=初始,0=未包月,1=已包月)")
    private Integer subStatus;
	/**订单状态:0=兑换中,1=兑换成功,2=兑换失败*/
	@Excel(name = "订单状态:0=兑换中,1=兑换成功,2=兑换失败", width = 15)
    @ApiModelProperty(value = "订单状态:0=兑换中,1=兑换成功,2=兑换失败")
    private Integer orderStatus;
	/**收货人手机号*/
	@Excel(name = "收货人手机号", width = 15)
    @ApiModelProperty(value = "收货人手机号")
    private String consigneeMobile;
	/**收货人名称*/
	@Excel(name = "收货人名称", width = 15)
    @ApiModelProperty(value = "收货人名称")
    private String consigneeName;
	/**用户地址*/
	@Excel(name = "用户地址", width = 15)
    @ApiModelProperty(value = "用户地址")
    private String address;
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private String productName;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
