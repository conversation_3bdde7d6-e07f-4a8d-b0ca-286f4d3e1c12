package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 订阅包歌曲
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
@Data
@TableName("cms_music_dy")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_music_dy对象", description="订阅包歌曲")
public class MusicDy implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**版权id*/
	@Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private String copyrightId;
	/**歌曲名*/
	@Excel(name = "歌曲名", width = 15)
    @ApiModelProperty(value = "歌曲名")
    private String musicName;
	/**歌手名*/
	@Excel(name = "歌手名", width = 15)
    @ApiModelProperty(value = "歌手名")
    private String singerName;
	/**状态:0=无效,1=有效*/
	@Excel(name = "状态:0=无效,1=有效", width = 15)
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    private Integer status;
	/**视频彩铃产品id*/
	@Excel(name = "视频彩铃产品id", width = 15)
    @ApiModelProperty(value = "视频彩铃产品id")
    private String vrbtProductId;
	/**彩铃产品id*/
	@Excel(name = "彩铃产品id", width = 15)
    @ApiModelProperty(value = "彩铃产品id")
    private String crbtProductId;
	/**振铃产品id*/
	@Excel(name = "振铃产品id", width = 15)
    @ApiModelProperty(value = "振铃产品id")
    private String ringProductId;
	/**全曲产品id*/
	@Excel(name = "全曲产品id", width = 15)
    @ApiModelProperty(value = "全曲产品id")
    private String songProductId;
	/**随身听产品id*/
	@Excel(name = "随身听产品id", width = 15)
    @ApiModelProperty(value = "随身听产品id")
    private String listenProductId;
	/**cp id*/
	@Excel(name = "cp id", width = 15)
    @ApiModelProperty(value = "cp id")
    private String cpId;
	/**歌曲类型*/
	@Excel(name = "歌曲类型", width = 15)
    @ApiModelProperty(value = "歌曲类型")
    private String musicType;
	/**歌曲风格*/
	@Excel(name = "歌曲风格", width = 15)
    @ApiModelProperty(value = "歌曲风格")
    private String musicStyle;
	/**视频彩铃预览图地址*/
	@Excel(name = "视频彩铃预览图地址", width = 15)
    @ApiModelProperty(value = "视频彩铃预览图地址")
    private String vrbtImg;
	/**视频彩铃播放地址*/
	@Excel(name = "视频彩铃播放地址", width = 15)
    @ApiModelProperty(value = "视频彩铃播放地址")
    private String vrbtVideo;
	/**热度*/
	@Excel(name = "热度", width = 15)
    @ApiModelProperty(value = "热度")
    private Integer hotLevel;
	/**播放量*/
	@Excel(name = "播放量", width = 15)
    @ApiModelProperty(value = "播放量")
    private Integer playCount;
	/**点赞数*/
	@Excel(name = "点赞数", width = 15)
    @ApiModelProperty(value = "点赞数")
    private Integer likeCount;
	/**收藏数*/
	@Excel(name = "收藏数", width = 15)
    @ApiModelProperty(value = "收藏数")
    private Integer favCount;
	/**有效期*/
	@Excel(name = "有效期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "有效期")
    private Date expiryDate;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
	/**所属部门编码*/
    @ApiModelProperty(value = "所属部门编码")
    private String sysOrgCode;
	/**电信铃音编码*/
	@Excel(name = "电信铃音编码", width = 15)
    @ApiModelProperty(value = "电信铃音编码")
    private String dxToneCode;
	/**电信资源编码*/
	@Excel(name = "电信资源编码", width = 15)
    @ApiModelProperty(value = "电信资源编码")
    private String dxResourceCode;
	/**联通铃音ID*/
	@Excel(name = "联通铃音ID", width = 15)
    @ApiModelProperty(value = "联通铃音ID")
    private String ltRingId;
}
