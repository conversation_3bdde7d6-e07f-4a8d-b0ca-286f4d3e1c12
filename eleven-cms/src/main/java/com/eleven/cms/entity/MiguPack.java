package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 咪咕业务包
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@ApiModel(value="cms_migu_pack对象", description="咪咕业务包")
@Data
@TableName("cms_migu_pack")
public class MiguPack implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**业务id*/
	@Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private String serviceId;
	/**包名*/
	@Excel(name = "包名", width = 15)
    @ApiModelProperty(value = "包名")
    private String titleName;

    /**权益包名*/
    @Excel(name = "权益包名", width = 15)
    @ApiModelProperty(value = "权益包名")
    private String packName;

    /**充值预约分钟*/
    @Excel(name = "充值预约分钟", width = 15)
    @ApiModelProperty(value = "充值预约分钟")
    private Long rechargeDelayMinute;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 产品图片
     */
    @Excel(name = "产品图片", width = 15)
    @ApiModelProperty(value = "产品图片")
    private String productImg;

    /**提供人名称*/
    @Excel(name = "提供人名称", width = 15)
    @ApiModelProperty(value = "提供人名称")
    private String providePersonName;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;



	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 渠道号
     */
    @TableField(exist = false)
    private String channelCode;
    /**
     * 是否soap关联查询:0=否,1=是
     */
    @Excel(name = "是否soap关联查询", width = 15)
    @ApiModelProperty(value = "是否soap关联查询:0=否,1=是")
    private Integer isSoap;
    /**
     * soap业务id
     */
    @Excel(name = "soap业务id", width = 15)
    @ApiModelProperty(value = "soap业务id")
    private String soapServiceId;
    /**
     * 是否有效:0=否,1=是
     */
    @Excel(name = "是否有效:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否有效:0=否,1=是")
    private Integer isValid;

    /**
     * 网页是否显示:0=否,1=是
     */
    @Excel(name = "网页是否显示", width = 15)
    @ApiModelProperty(value = "网页是否显示:0=否,1=是")
    private Integer isDisplay;



}
