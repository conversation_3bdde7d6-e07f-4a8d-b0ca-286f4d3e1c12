package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_vrbt_diy_video
 * @Author: jeecg-boot
 * @Date:   2023-06-29
 * @Version: V1.0
 */
@Data
@TableName("cms_vrbt_diy_video")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_vrbt_diy_video对象", description="cms_vrbt_diy_video")
public class VrbtDiyVideo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**mv地址*/
	@Excel(name = "mv地址", width = 15)
    @ApiModelProperty(value = "mv地址")
    private String mvUrl;
	/**mv名称*/
	@Excel(name = "mv名称", width = 15)
    @ApiModelProperty(value = "mv名称")
    private String mvName;
	/**版权id*/
	@Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private String copyrightId;
	/**版权类型:0=音乐,1=mv版权*/
	@Excel(name = "版权类型:0=音乐,1=mv版权", width = 15)
    @ApiModelProperty(value = "版权类型:0=音乐,1=mv版权")
    private Integer copyrightType;
	/**seq*/
	@Excel(name = "seq", width = 15)
    @ApiModelProperty(value = "seq")
    private String seq;
	/**事务ID*/
	@Excel(name = "事务ID", width = 15)
    @ApiModelProperty(value = "事务ID")
    private String transactionId;
	/**上报状态 1=成功，0=失败*/
	@Excel(name = "上报状态", width = 15)
    @ApiModelProperty(value = "上报状态")
    private Integer reportStatus;
	/**审核状态 -1=审核中，1=成功，0=失败*/
	@Excel(name = "审核状态", width = 15)
    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;
	/**分发状态 1=成功，0=失败*/
	@Excel(name = "分发状态", width = 15)
    @ApiModelProperty(value = "分发状态")
    private Integer distributeStatus;
    /**设置状态 1=成功，0=失败*/
    @Excel(name = "设置状态", width = 15)
    @ApiModelProperty(value = "设置状态")
    private Integer settingStatus;
    @Excel(name = "设置结果", width = 15)
    @ApiModelProperty(value = "设置结果")
    private String settingResult;
	/**分发成功率*/
	@Excel(name = "分发成功率", width = 15)
    @ApiModelProperty(value = "分发成功率")
    private String distributeRatio;
	/**上报回执结果码*/
	@Excel(name = "上报回执结果码", width = 15)
    @ApiModelProperty(value = "上报回执结果码")
    private String reportReturnCode;
    @Excel(name = "上报回执结果描述", width = 15)
    @ApiModelProperty(value = "上报回执结果描述")
    private String reportReturnDesc;
	/**审核回执结果码*/
	@Excel(name = "审核回执结果码", width = 15)
    @ApiModelProperty(value = "审核回执结果码")
    private String auditReturnCode;
    /**审核回执结果码*/
    @Excel(name = "审核回执结果描述", width = 15)
    @ApiModelProperty(value = "审核回执结果描述")
    private String auditReturnDesc;
	/**音乐编码*/
	@Excel(name = "音乐编码", width = 15)
    @ApiModelProperty(value = "音乐编码")
    private String musicId;
	/**铃音编码*/
	@Excel(name = "铃音编码", width = 15)
    @ApiModelProperty(value = "铃音编码")
    private String toneId;
    /**成功省份*/
    @Excel(name = "成功省份", width = 15)
    @ApiModelProperty(value = "成功省份")
    private String successProvinceName;
    /**失败省份*/
    @Excel(name = "失败省份", width = 15)
    @ApiModelProperty(value = "失败省份")
    private String failProvinceName;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**渠道号*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
}
