package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_sms_mo_log
 * @Author: jeecg-boot
 * @Date:   2022-10-31
 * @Version: V1.0
 */
@Data
@TableName("cms_sms_mo_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_sms_mo_log对象", description="cms_sms_mo_log")
public class SmsMoLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**用户上行手机号码*/
	@Excel(name = "用户上行手机号码", width = 15)
    @ApiModelProperty(value = "用户上行手机号码")
    private java.lang.String mobile;
	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private java.lang.String province;
	/**业务类型*/
	@Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private java.lang.String bizType;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private java.lang.String channel;
	/**子渠道号*/
	@Excel(name = "子渠道号", width = 15)
    @ApiModelProperty(value = "子渠道号")
    private java.lang.String subChannel;
	/**平台提供的接入号*/
	@Excel(name = "平台提供的接入号", width = 15)
    @ApiModelProperty(value = "平台提供的接入号")
    private java.lang.String srcId;
	/**用户上行内容信息*/
	@Excel(name = "用户上行内容信息", width = 15)
    @ApiModelProperty(value = "用户上行内容信息")
    private java.lang.String msgContent;
	/**操作:0=未做退订,1=已做退订*/
	@Excel(name = "操作:0=未做退订,1=已做退订", width = 15)
    @ApiModelProperty(value = "操作:0=未做退订,1=已做退订")
    @Dict(dicCode = "yn")
    private java.lang.Integer cancel;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**创建日期*/
    /**创建时间*/
    @Excel(name = "创建日期", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
}
