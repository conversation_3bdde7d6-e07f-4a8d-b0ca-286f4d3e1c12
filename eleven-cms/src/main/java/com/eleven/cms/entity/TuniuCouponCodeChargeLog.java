package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 途牛券码充值记录
 * @Author: jeecg-boot
 * @Date:   2024-03-29
 * @Version: V1.0
 */
@Data
@TableName("cms_tuniu_coupon_code_charge_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_tuniu_coupon_code_charge_log对象", description="途牛券码充值记录")
public class TuniuCouponCodeChargeLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**兑换码*/
	@Excel(name = "兑换码", width = 15)
    @ApiModelProperty(value = "兑换码")
    private String couponCode;
	/**订单号*/
	@Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderId;
	/**权益Id*/
	@Excel(name = "权益Id", width = 15)
    @ApiModelProperty(value = "权益Id")
    private String rightsId;
	/**事务跟踪id，每次请求保证唯一性*/
	@Excel(name = "事务跟踪id，每次请求保证唯一性", width = 15)
    @ApiModelProperty(value = "事务跟踪id，每次请求保证唯一性")
    private String dataLinkId;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**商品标识*/
	@Excel(name = "商品标识", width = 15)
    @ApiModelProperty(value = "商品标识")
    private String goodsNo;
	/**回调通知地址*/
	@Excel(name = "回调通知地址", width = 15)
    @ApiModelProperty(value = "回调通知地址")
    private String notifyUrl;
	/**激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中*/
	@Excel(name = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中", width = 15)
    @ApiModelProperty(value = "激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中")
    private Integer status;
	/**发码时间*/
	@Excel(name = "发码时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "发码时间")
    private Date sendTime;
	/**过期时间*/
	@Excel(name = "过期时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "过期时间")
    private Date invalidTime;
	/**数量*/
	@Excel(name = "数量", width = 15)
    @ApiModelProperty(value = "数量")
    private String num;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
