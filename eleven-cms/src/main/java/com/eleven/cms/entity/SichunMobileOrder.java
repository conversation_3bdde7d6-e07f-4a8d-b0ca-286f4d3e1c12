package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * 四川移动订单表
 *
 * @author: cai lei
 * @create: 2021-09-22 15:53
 */

@Data
@TableName("cms_sichuan_mobile_order")
public class SichunMobileOrder {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;
    @TableField("order_id")
    private String orderId; //订单号
    @TableField("phone")
    private String phone; //手机号
    @TableField("create_time")
    private Date createTime; //创建时间
}
