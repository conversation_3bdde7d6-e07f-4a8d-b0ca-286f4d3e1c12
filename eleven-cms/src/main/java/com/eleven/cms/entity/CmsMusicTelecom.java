package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_music_telecom
 * @Author: jeecg-boot
 * @Date:   2021-11-18
 * @Version: V1.0
 */
@Data
@TableName("cms_music_telecom")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_music_telecom对象", description="cms_music_telecom")
public class CmsMusicTelecom implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**电信铃音编码*/
	@Excel(name = "电信铃音编码", width = 15)
    @ApiModelProperty(value = "电信铃音编码")
    private String toneCode;
	/**电信资源编码*/
	@Excel(name = "电信资源编码", width = 15)
    @ApiModelProperty(value = "电信资源编码")
    private String resourceCode;
	/**歌曲名*/
	@Excel(name = "歌曲名", width = 15)
    @ApiModelProperty(value = "歌曲名")
    private String musicName;
	/**歌手名*/
	@Excel(name = "歌手名", width = 15)
    @ApiModelProperty(value = "歌手名")
    private String singerName;
	/**歌曲风格*/
	@Excel(name = "歌曲风格", width = 15)
    @ApiModelProperty(value = "歌曲风格")
    private String musicStyle;
	/**视频彩铃预览图地址*/
	@Excel(name = "视频彩铃预览图地址", width = 15)
    @ApiModelProperty(value = "视频彩铃预览图地址")
    private String vrbtImg;
	/**视频彩铃播放地址*/
	@Excel(name = "视频彩铃播放地址", width = 15)
    @ApiModelProperty(value = "视频彩铃播放地址")
    private String vrbtVideo;
	/**有效期*/
	@Excel(name = "有效期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "有效期")
    private Date expiryDate;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
