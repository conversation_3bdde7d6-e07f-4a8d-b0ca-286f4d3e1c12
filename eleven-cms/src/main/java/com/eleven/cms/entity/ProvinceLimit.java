package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 省份限制
 * @Author: jeecg-boot
 * @Date:   2021-02-22
 * @Version: V1.0
 */
@Data
@TableName("cms_province_limit")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_province_limit对象", description="省份限制")
public class ProvinceLimit implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**省份名称*/
	@Excel(name = "省份名称", width = 15)
    @ApiModelProperty(value = "省份名称")
    private String provinceName;
	/**省份编码*/
	@Excel(name = "省份编码", width = 15)
    @ApiModelProperty(value = "省份编码")
    private String provinceCode;
	/**是否启用（移动） 0：否 1：是*/
	@Excel(name = "是否启用（移动） 0：否 1：是", width = 15, dicCode = "province_available")
	@Dict(dicCode = "province_available")
    @ApiModelProperty(value = "是否启用（移动） 0：否 1：是")
    private Integer mobileAvailable;
	/**是否启用（联通）  0：否 1：是*/
	@Excel(name = "是否启用（联通）  0：否 1：是", width = 15, dicCode = "province_available")
	@Dict(dicCode = "province_available")
    @ApiModelProperty(value = "是否启用（联通）  0：否 1：是")
    private Integer unicomAvailable;
	/**是否启用（电信）  0：否 1：是*/
	@Excel(name = "是否启用（电信）  0：否 1：是", width = 15, dicCode = "province_available")
	@Dict(dicCode = "province_available")
    @ApiModelProperty(value = "是否启用（电信）  0：否 1：是")
    private Integer telecomAvailable;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
