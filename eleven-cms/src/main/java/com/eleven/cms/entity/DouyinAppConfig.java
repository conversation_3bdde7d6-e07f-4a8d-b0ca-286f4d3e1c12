package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: douyin_app_config
 * @Author: jeecg-boot
 * @Date: 2025-06-24
 * @Version: V1.0
 */
@Data
@TableName("douyin_app_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "douyin_app_config对象", description = "douyin_app_config")
public class DouyinAppConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 抖音appId
     */
    @Excel(name = "抖音appId", width = 15)
    @ApiModelProperty(value = "抖音appId")
    private String appId;
    /**
     * 密钥
     */
    @Excel(name = "密钥", width = 15)
    @ApiModelProperty(value = "密钥")
    private String appSecret;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 私钥版本
     */
    private String keyVersion;
    /**
     * app名称
     */
    @Excel(name = "app名称", width = 15)
    @ApiModelProperty(value = "app名称")
    private String appName;
    /**
     * 业务类型
     */
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 公钥
     */
    private String publicKey;


    /**
     * 回调地址
     */
    private String callBackUrl;
    private String refundCallBackUrl;
    /**
     * 是否删除 1：是  0：否
     */
    @Excel(name = "是否删除 1：是  0：否", width = 15)
    @ApiModelProperty(value = "是否删除 1：是  0：否")
    private Integer isDeleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    private String orderCoverUrl;


    @ApiModelProperty(value = "套餐模板id")
    private String packageTemplateId;

    @ApiModelProperty(value = "平台")
    private String platform;
}
