package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_sms_send_log
 * @Author: jeecg-boot
 * @Date:   2023-09-25
 * @Version: V1.0
 */
@Data
@TableName("cms_sms_send_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_sms_send_log对象", description="cms_sms_send_log")
public class SmsSendLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String mobile;
	/**大唐短信id*/
	@Excel(name = "大唐短信id", width = 15)
    @ApiModelProperty(value = "大唐短信id")
    private java.lang.String msgId;
	/**发送状态:-1-发送中,0=发送失败,1=发送成功*/
	@Excel(name = "发送状态:-1-发送中,0=发送失败,1=发送成功", width = 15, dicCode = "sms_send_status")
	@Dict(dicCode = "sms_send_status")
    @ApiModelProperty(value = "发送状态:-1-发送中,0=发送失败,1=发送成功")
    private java.lang.Integer sendStatus;
	/**短信内容*/
	@Excel(name = "短信内容", width = 15)
    @ApiModelProperty(value = "短信内容")
    private java.lang.String msgContent;
	/**回执的发送状态*/
	@Excel(name = "回执的发送状态", width = 15)
    @ApiModelProperty(value = "回执的发送状态")
    private java.lang.String notifyState;
	/**发送失败原因*/
	@Excel(name = "发送失败原因", width = 15)
    @ApiModelProperty(value = "发送失败原因")
    private java.lang.String failCause;
	/**额外信息*/
	@Excel(name = "额外信息", width = 15)
    @ApiModelProperty(value = "额外信息")
    private java.lang.String extra;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
}
