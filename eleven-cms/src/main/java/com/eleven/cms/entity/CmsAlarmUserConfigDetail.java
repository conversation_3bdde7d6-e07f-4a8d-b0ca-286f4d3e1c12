package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: cms_alarm_user_config_detail
 * @Author: jeecg-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
@Data
@TableName("cms_alarm_user_config_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_alarm_user_config_detail对象", description="cms_alarm_user_config_detail")
public class CmsAlarmUserConfigDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**告警配置表ID*/
	@Excel(name = "告警配置表ID", width = 15)
    @ApiModelProperty(value = "告警配置表ID")
    private String alarmUserConfigId;
	/**告警人ID*/
	@Excel(name = "告警人ID", width = 15)
    @ApiModelProperty(value = "告警人ID")
    private String alarmUserId;
	/**开启手机短信通知，默认开启 0:关闭 1:开启*/
	@Excel(name = "开启手机短信通知，默认开启 0:关闭 1:开启", width = 15)
    @ApiModelProperty(value = "开启手机短信通知，默认开启 0:关闭 1:开启")
//    @Dict(dicCode = "enable_sms")
    private String enableSms;
	/**开启微信公众号通知，默认开启 0:关闭 1:开启*/
	@Excel(name = "开启微信公众号通知，默认开启 0:关闭 1:开启", width = 15)
    @ApiModelProperty(value = "开启微信公众号通知，默认开启 0:关闭 1:开启")
//    @Dict(dicCode = "enable_vx")
    private String enableVx;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**告警人姓名*/
    @TableField(exist = false)
    @ApiModelProperty(value = "告警人姓名")
    private String realName;
    /**告警人ID集*/
    @ApiModelProperty(value = "告警人ID集")
    private List<String> alarmUserIds;
}
