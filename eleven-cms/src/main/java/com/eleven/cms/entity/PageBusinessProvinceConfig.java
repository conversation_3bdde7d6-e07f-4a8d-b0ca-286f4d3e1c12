package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 落地页业务省份配置 Entity
 *
 * <AUTHOR>
 * @date 2023-08-08 14:47:33
 */
@Data
@TableName("xxl_page_business_province_config")
public class PageBusinessProvinceConfig {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 落地页业务配置id
     */
    @TableField("page_business_config_id")
    private String pageBusinessConfigId;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 移动链接
     */
    @TableField("yidong_link")
    private String yidongLink;

    /**
     * 移动备用链接
     */
    @TableField("yidong_spare_link")
    private String yidongSpareLink;

    /**
     * 移动规则
     */
    @TableField("yidong_rule")
    private String yidongRule;

    /**
     * 联通链接
     */
    @TableField("liantong_link")
    private String liantongLink;

    /**
     * 联通规则
     */
    @TableField("liantong_rule")
    private String liantongRule;

    /**
     * 电信链接
     */
    @TableField("dianxin_link")
    private String dianxinLink;

    /**
     * 电信规则
     */
    @TableField("dianxin_rule")
    private String dianxinRule;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 状态，1=开启，0=关闭
     */
    @TableField("status")
    private Integer status;

}
