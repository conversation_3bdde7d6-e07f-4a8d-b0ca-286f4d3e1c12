package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eleven.cms.dto.WechatComplainDetail;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 微信投诉明细
 * @Author: jeecg-boot
 * @Date:   2023-04-11
 * @Version: V1.0
 */
@Data
@TableName("cms_wechat_complain")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_wechat_complain对象", description="微信投诉明细")
public class WechatComplain implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**微信投诉单号*/
	@Excel(name = "微信投诉单号", width = 15)
    @ApiModelProperty(value = "微信投诉单号")
    private String complaintId;
	/**投诉单状态 PENDING：待处理 PROCESSING：处理中 PROCESSED：已处理完成*/
	@Excel(name = "投诉单状态 PENDING：待处理 PROCESSING：处理中 PROCESSED：已处理完成", width = 15)
    @ApiModelProperty(value = "投诉单状态 PENDING：待处理 PROCESSING：处理中 PROCESSED：已处理完成")
    private String complainStatus;
	/**投诉的具体描述*/
	@Excel(name = "投诉的具体描述", width = 15)
    @ApiModelProperty(value = "投诉的具体描述")
    private String complaintDetail;
	/**投诉时间*/
	@Excel(name = "投诉时间", width = 15)
    @ApiModelProperty(value = "投诉时间")
    private String complaintTime;
	/**商户号*/
	@Excel(name = "商户号", width = 15)
    @ApiModelProperty(value = "商户号")
    private String complaintedMchid;
	/**投诉人联系方式*/
	@Excel(name = "投诉人联系方式", width = 15)
    @ApiModelProperty(value = "投诉人联系方式")
    private String payerPhone;
	/**投诉人openid*/
	@Excel(name = "投诉人openid", width = 15)
    @ApiModelProperty(value = "投诉人openid")
    private String payerOpenid;
	/**投诉单是否已全额退款:0=否,1=是*/
	@Excel(name = "投诉单是否已全额退款:0=否,1=是", width = 15)
    @ApiModelProperty(value = "投诉单是否已全额退款:0=否,1=是")
    private Integer complaintFullRefunded;
	/**是否有待回复的用户留言:0=否,1=是*/
	@Excel(name = "是否有待回复的用户留言:0=否,1=是", width = 15)
    @ApiModelProperty(value = "是否有待回复的用户留言:0=否,1=是")
    private Integer incomingUserResponse;
	/**问题描述*/
	@Excel(name = "问题描述", width = 15)
    @ApiModelProperty(value = "问题描述")
    private String problemDescription;
	/**用户投诉次数*/
	@Excel(name = "用户投诉次数", width = 15)
    @ApiModelProperty(value = "用户投诉次数")
    private Integer userComplaintTimes;
	/**问题类型 REFUND：申请退款 SERVICE_NOT_WORK：服务权益未生效 OTHERS：其他类型*/
	@Excel(name = "问题类型 REFUND：申请退款 SERVICE_NOT_WORK：服务权益未生效 OTHERS：其他类型", width = 15)
    @ApiModelProperty(value = "问题类型 REFUND：申请退款 SERVICE_NOT_WORK：服务权益未生效 OTHERS：其他类型")
    private String problemType;
	/**申请退款金额*/
	@Excel(name = "申请退款金额", width = 15)
    @ApiModelProperty(value = "申请退款金额")
    private String applyRefundAmount;
	/**微信订单号*/
	@Excel(name = "微信订单号", width = 15)
    @ApiModelProperty(value = "微信订单号")
    private String transactionId;
	/**商户订单号*/
	@Excel(name = "商户订单号", width = 15)
    @ApiModelProperty(value = "商户订单号")
    private String outTradeNo;
	/**订单金额*/
	@Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private String amount;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String complainRemark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/
	@Excel(name = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败", width = 15)
    @ApiModelProperty(value = "退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败")
    private Integer refundStatus;
    /**通知类型 COMPLAINT.CREATE：产生新投诉 COMPLAINT.STATE_CHANGE：投诉状态变化*/
    @Excel(name = "通知类型 COMPLAINT.CREATE：产生新投诉 COMPLAINT.STATE_CHANGE：投诉状态变化", width = 15)
    @ApiModelProperty(value = "通知类型 COMPLAINT.CREATE：产生新投诉 COMPLAINT.STATE_CHANGE：投诉状态变化")
    private String eventType;

    /**通知的资源数据类型，支付成功通知为encrypt-resource*/
    @Excel(name = "通知的资源数据类型，支付成功通知为encrypt-resource", width = 15)
    @ApiModelProperty(value = "通知的资源数据类型，支付成功通知为encrypt-resource")
    private String resourceType;
    /**回调摘要*/
    @Excel(name = "回调摘要", width = 15)
    @ApiModelProperty(value = "回调摘要化")
    private String summary;
    /**动作类型 CREATE_COMPLAINT：用户提交投诉 CONTINUE_COMPLAINT：用户继续投诉 USER_RESPONSE：用户新留言 RESPONSE_BY_PLATFORM：平台新留言 SELLER_REFUND：商户发起全额退款 MERCHANT_RESPONSE：商户新回复 MERCHANT_CONFIRM_COMPLETE：商户反馈处理完成 MERCHANT_APPROVE_REFUND：商户同意退款 MERCHANT_REJECT_REFUND：商户驳回退款 REFUND_SUCCESS：退款到账*/
    @Excel(name = "动作类型 CREATE_COMPLAINT：用户提交投诉 CONTINUE_COMPLAINT：用户继续投诉 USER_RESPONSE：用户新留言 RESPONSE_BY_PLATFORM：平台新留言 SELLER_REFUND：商户发起全额退款 MERCHANT_RESPONSE：商户新回复 MERCHANT_CONFIRM_COMPLETE：商户反馈处理完成 MERCHANT_APPROVE_REFUND：商户同意退款 MERCHANT_REJECT_REFUND：商户驳回退款 REFUND_SUCCESS：退款到账", width = 15)
    @ApiModelProperty(value = "动作类型 CREATE_COMPLAINT：用户提交投诉 CONTINUE_COMPLAINT：用户继续投诉 USER_RESPONSE：用户新留言 RESPONSE_BY_PLATFORM：平台新留言 SELLER_REFUND：商户发起全额退款 MERCHANT_RESPONSE：商户新回复 MERCHANT_CONFIRM_COMPLETE：商户反馈处理完成 MERCHANT_APPROVE_REFUND：商户同意退款 MERCHANT_REJECT_REFUND：商户驳回退款 REFUND_SUCCESS：退款到账")
    private String actionType;
    //投诉资料列表
    @TableField(exist = false)
    private String complaintMediaList;
    //投诉单关联订单信息
    @TableField(exist = false)
    private String complaintOrderInfo;
    //投诉单关联服务单信息
    @TableField(exist = false)
    private String serviceOrderInfo;
    //补充信息
    @TableField(exist = false)
    private String additionalInfo;




}
