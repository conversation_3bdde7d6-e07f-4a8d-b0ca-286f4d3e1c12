package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 广告平台表 Entity
 *
 * <AUTHOR>
 * @date 2021-06-15 14:02:47
 */
@Data
@TableName("xxl_ad_platform")
public class AdPlatform {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;

    /**
     * 广告平台名称
     */
    @TableField("ad_platform")
    private String adPlatform;

    /**
     * 广告api的spring bean名称
     */
    @TableField("ad_api_bean_name")
    private String adApiBeanName;

    /**
     * 平台上报Url
     */
    @TableField("ad_report_url")
    private String adReportUrl;

    /**
     * 广告id参数名称
     */
    @TableField("ad_id_param_name")
    private String adIdParamName;

    /**
     * 前端需要加载的js
     */
    @TableField("ad_js_url")
    private String adJsUrl;

    /**
     * 前端订阅成功后需要回调上报的js代码
     */
    @TableField("ad_js_eval")
    private String adJsEval;

    /**
     * 是 string CPS1（引流触点必须传入改字段，需到三级,传来源方式+引流平台+细分触点，各级之间用@隔开，长度限制40位以内,；例如张三快手直播入参为 zb@ks@zhangs001）来源方式打标说明：zb：直播，xxl：信息流，sx：随销，yb：腰部，pop：POP店，sy：私域，qt：其它。引流平台打标说明：ttx：头条系，ks：快手，gdt:广点通，bdx:百度系，qt：其它细分触点打标说明：渠道自定义，相同细分的用相同的字符进行标识。
     */
    @TableField("tag")
    private String tag;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 备用字段
     */
    @TableField("spare_field")
    private String spareField;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 广告id参数名称(备用)
     */
    @TableField("ad_id_param_name_spare")
    private String adIdParamNameSpare;

    /**
     * 广告平台代码
     */
    @TableField("ad_platform_type")
    private String adPlatformType;

}
