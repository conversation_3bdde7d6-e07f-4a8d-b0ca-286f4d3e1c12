package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_vr_jingmeng
 * @Author: jeecg-boot
 * @Date:   2024-03-18
 * @Version: V1.0
 */
@Data
@TableName("cms_vr_jingmeng")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_vr_jingmeng对象", description="cms_vr_jingmeng")
public class VrJingmeng implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**流水号(订单号)*/
	@Excel(name = "流水号(订单号)", width = 15)
    @ApiModelProperty(value = "流水号(订单号)")
    private String transactionId;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**产品标识，用来区分不同的服务类型*/
	@Excel(name = "产品标识，用来区分不同的服务类型", width = 15)
    @ApiModelProperty(value = "产品标识，用来区分不同的服务类型")
    private String productId;
	/**订购时间*/
	@Excel(name = "订购时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "订购时间")
    private Date subTime;
	/**退订时间*/
	@Excel(name = "退订时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "退订时间")
    private Date unSubTime;
	/**资费*/
	@Excel(name = "资费", width = 15)
    @ApiModelProperty(value = "资费")
    private String price;
	/**状态(-1=初始,1=订购,2=退订)*/
	@Excel(name = "状态(-1=初始,1=订购,2=退订)", width = 15)
    @ApiModelProperty(value = "状态(-1=初始,1=订购,2=退订)")
    private Integer status;
	/**计费类型，目前有两种，话费支付 1，第三方支付 2*/
	@Excel(name = "计费类型，目前有两种，话费支付 1，第三方支付 2", width = 15)
    @ApiModelProperty(value = "计费类型，目前有两种，话费支付 1，第三方支付 2")
    private String feeType;
	/**归属地编码*/
	@Excel(name = "归属地编码", width = 15)
    @ApiModelProperty(value = "归属地编码")
    private String region;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**咪咕通过识别用户来源添加渠道信息*/
	@Excel(name = "咪咕通过识别用户来源添加渠道信息", width = 15)
    @ApiModelProperty(value = "咪咕通过识别用户来源添加渠道信息")
    private String miguChannel;
	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
	/**城市*/
	@Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
