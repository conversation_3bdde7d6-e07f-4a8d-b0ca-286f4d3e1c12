package com.eleven.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 *  Entity
 *
 * <AUTHOR>
 * @date 2023-08-07 11:33:33
 */
@Data
@TableName("xxl_biz_type")
public class BizType {

    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 业务名称
     */
    @TableField("biz_name")
    private String bizName;

    /**
     * 渠道号
     */
    @TableField("channel_code")
    private String channelCode;

    /**
     * bean名称
     */
    @TableField("bean_name")
    private String beanName;

    /**
     * 备注
     */
    @TableField("remake")
    private String remake;

    /**
     * 是否启用 1启用0禁用
     */
    @TableField("is_enable")
    private Integer isEnable;

    /**
     * 服务地址类型 1视频彩铃服务器 2百度网盘服务器
     */
    @TableField("service_addr_type")
    private Integer serviceAddrType;

    /**
     * 工号
     */
    @TableField("company_owner")
    private String companyOwner;


    /**
     * 权益领取链接
     */
    @TableField("rights_link")
    private String rightsLink;

    /**
     * 创建日期
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新日期
     */
    @TableField("update_time")
    private Date updateTime;


}
