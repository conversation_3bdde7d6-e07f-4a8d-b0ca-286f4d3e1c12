package com.eleven.cms.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_outside_business_config
 * @Author: jeecg-boot
 * @Date:   2023-10-10
 * @Version: V1.0
 */
@ApiModel(value="cms_outside_config对象", description="cms_outside_config")
@Data
@TableName("cms_outside_business_config")
public class OutsideBusinessConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
	@ApiModelProperty(value = "id")
	private String id;
	/**外部渠道配置id*/
	@ApiModelProperty(value = "外部渠道配置id")
	private String outsideConfigId;
	/**业务类型id*/
	@Excel(name = "业务类型id", width = 15)
	@ApiModelProperty(value = "业务类型id")
	private String bizTypeId;
    /**业务类型id*/
    @Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**省份*/
	@Excel(name = "省份", width = 15)
	@ApiModelProperty(value = "省份")
	private String province;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@ApiModelProperty(value = "备注")
	private String remark;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "创建日期")
	private Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "更新日期")
	private Date updateTime;
	/**1启用0禁用*/
	@Excel(name = "1启用0禁用", width = 15)
	@ApiModelProperty(value = "1启用0禁用")
	private Integer status;
}
