package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_order_vrbt
 * @Author: jeecg-boot
 * @Date:   2020-11-16
 * @Version: V1.0
 */
@Data
@TableName("cms_order_vrbt")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_order_vrbt对象", description="cms_order_vrbt")
public class OrderVrbt implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private java.lang.String id;
    /**所属运营商*/
    @Excel(name = "所属运营商(1=移动,3=联通,4=电信)", width = 15)
    @ApiModelProperty(value = "所属运营商")
    private java.lang.String isp;
	/**歌曲名*/
	@Excel(name = "歌曲名", width = 15)
    @ApiModelProperty(value = "歌曲名")
    private java.lang.String musicName;
	/**歌手名*/
	@Excel(name = "歌手名", width = 15)
    @ApiModelProperty(value = "歌手名")
    private java.lang.String singerName;
	/**版权id*/
	@Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private java.lang.String copyrightId;
	/**视频彩铃产品id*/
	@Excel(name = "视频彩铃产品id", width = 15)
    @ApiModelProperty(value = "视频彩铃产品id")
    private java.lang.String vrbtProductId;
	/**彩铃产品id*/
	@Excel(name = "彩铃产品id", width = 15)
    @ApiModelProperty(value = "彩铃产品id")
    private java.lang.String crbtProductId;
	/**振铃产品id*/
	@Excel(name = "振铃产品id", width = 15)
    @ApiModelProperty(value = "振铃产品id")
    private java.lang.String ringProductId;
	/**全曲产品id*/
	@Excel(name = "全曲产品id", width = 15)
    @ApiModelProperty(value = "全曲产品id")
    private java.lang.String songProductId;
	/**随身听产品id*/
	@Excel(name = "随身听产品id", width = 15)
    @ApiModelProperty(value = "随身听产品id")
    private java.lang.String listenProductId;
	/**cp id*/
	@Excel(name = "cp id", width = 15)
    @ApiModelProperty(value = "cp id")
    private java.lang.String cpId;
	/**是否有效*/
	@Excel(name = "是否有效", width = 15, dicCode = "valid_status")
	@Dict(dicCode = "valid_status")
    @ApiModelProperty(value = "是否有效")
    private java.lang.Integer status;
	/**下载次数*/
	@Excel(name = "下载次数", width = 15)
    @ApiModelProperty(value = "下载次数")
    private java.lang.Integer orderCount;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
	/**所属部门编码*/
    @ApiModelProperty(value = "所属部门编码")
    private java.lang.String sysOrgCode;
}
