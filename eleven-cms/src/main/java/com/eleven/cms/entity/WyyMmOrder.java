package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_wyy_mm_order
 * @Author: jeecg-boot
 * @Date:   2023-06-07
 * @Version: V1.0
 */
@Data
@TableName("cms_wyy_mm_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_wyy_mm_order对象", description="cms_wyy_mm_order")
public class WyyMmOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**自增主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "自增主键")
    private String id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**商户订单号*/
	@Excel(name = "商户订单号", width = 15)
    @ApiModelProperty(value = "商户订单号")
    private String appOrderId;
	/**支付中心订单号*/
	@Excel(name = "支付中心订单号", width = 15)
    @ApiModelProperty(value = "支付中心订单号")
    private String orderId;
	/**订单状态:0=订单创建1=订购成功2=订购失败3=包月退订4=续订失败5=退款成功*/
	@Excel(name = "订单状态", width = 15)
    @ApiModelProperty(value = "订单状态:0=订单创建1=订购成功2=订购失败3=包月退订4=续订失败5=退款成功")
    private Integer status;
	/**订购状态,1正常,2已退订*/
	@Excel(name = "订购状态,1正常,2已退订", width = 15)
    @ApiModelProperty(value = "订购状态,1正常,2已退订")
    private Integer subStatus;
	/**是否续订:0=初定2=续订*/
	@Excel(name = "是否续订", width = 15)
    @ApiModelProperty(value = "是否续订:0=初定2=续订")
    private Integer isRenew;
	/**支付月份*/
	@Excel(name = "支付月份", width = 15)
    @ApiModelProperty(value = "支付月份")
    private String payMonth;
	/**第几次支付*/
	@Excel(name = "第几次支付", width = 15)
    @ApiModelProperty(value = "第几次支付")
    private Integer payNo;
	/**订单金额,单位分*/
	@Excel(name = "订单金额,单位分", width = 15)
    @ApiModelProperty(value = "订单金额,单位分")
    private Integer recAmount;
	/**实际支付金额,单位分*/
	@Excel(name = "实际支付金额,单位分", width = 15)
    @ApiModelProperty(value = "实际支付金额,单位分")
    private Integer payAmount;
	/**商户用户名*/
	@Excel(name = "商户用户名", width = 15)
    @ApiModelProperty(value = "商户用户名")
    private String userId;
	/**支付时间*/
	@Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
	/**退款时间*/
	@Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退款时间")
    private Date refundTime;
	/**退订时间*/
	@Excel(name = "退订时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退订时间")
    private Date unSignTime;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 权益有效日期
     */
    @TableField(exist = false)
    private Long days;

    /**
     * 订购有效开始时间
     */
    @TableField(exist = false)
    LocalDateTime payTimeStart;

    /**
     * 订购有效结束时间
     */
    @TableField(exist = false)
    LocalDateTime  payTimeEnd;

}
