package com.eleven.cms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: cms_vrbt_zero_order
 * @Author: jeecg-boot
 * @Date:   2022-10-20
 * @Version: V1.0
 */
@Data
@TableName("cms_vrbt_zero_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_vrbt_zero_order对象", description="cms_vrbt_zero_order")
public class VrbtZeroOrder implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
	/**年月,如202210*/
	@Excel(name = "年月,如202210", width = 15)
    @ApiModelProperty(value = "年月,如202210")
    private String ym;
	/**运营商,1移动用户,3联通用户,4电信用户*/
	@Excel(name = "运营商,1移动用户,3联通用户,4电信用户", width = 15)
    @ApiModelProperty(value = "运营商,1移动用户,3联通用户,4电信用户")
    private String isp;
	/**业务类型,vrbt:视频彩铃,rt:振铃*/
	@Excel(name = "业务类型,vrbt:视频彩铃,rt:振铃", width = 15)
    @ApiModelProperty(value = "业务类型,vrbt:视频彩铃,rt:振铃")
    private String bizType;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
	/**咪咕serviceId*/
	@Excel(name = "咪咕serviceId", width = 15)
    @ApiModelProperty(value = "咪咕serviceId")
    private String serviceId;
	/**版权id*/
	@Excel(name = "版权id", width = 15)
    @ApiModelProperty(value = "版权id")
    private String copyrightId;

    /**内容id*/
    @Excel(name = "内容id", width = 15)
    @ApiModelProperty(value = "内容id")
    private String contentId;


	/**状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)*/
	@Excel(name = "状态(-1=初始,0=失败,1=成功", width = 15)
    @ApiModelProperty(value = "状态(-1=初始,0=失败,1=成功")
    private Integer status;


    /**状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)*/
    @Excel(name = "状态(-1=初始,0=失败,1=成功", width = 15)
    @ApiModelProperty(value = "状态(-1=初始,0=失败,1=成功")
    private Integer ringStatus;
	/**订购结果*/
	@Excel(name = "订购结果", width = 15)
    @ApiModelProperty(value = "订购结果")
    private String result;
	/**额外信息,比如订包月后免费订铃声的结果*/
	@Excel(name = "额外信息,比如订包月后免费订铃声的结果", width = 15)
    @ApiModelProperty(value = "额外信息,比如订包月后免费订铃声的结果")
    private String extra;
	/**6个月包月校验:-1=未校验,0=未包月,1=已包月*/
	@Excel(name = "6个月包月校验:-1=未校验,0=未包月,1=已包月", width = 15)
    @ApiModelProperty(value = "6个月包月校验:-1=未校验,0=未包月,1=已包月")
    private Integer price;
	/**省份*/
	@Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
	/**城市*/
	@Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;
	/**来源*/
	@Excel(name = "来源", width = 15)
    @ApiModelProperty(value = "来源")
    private String source;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
