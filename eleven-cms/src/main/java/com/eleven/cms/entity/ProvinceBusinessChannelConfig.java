package com.eleven.cms.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_province_business_channel_config
 * @Author: jeecg-boot
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@ApiModel(value="cms_province_business_channel_config对象", description="cms_province_business_channel_config")
@Data
@TableName("cms_province_business_channel_config")
public class ProvinceBusinessChannelConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
	/**渠道号*/
	@Excel(name = "渠道号", width = 15)
    @ApiModelProperty(value = "渠道号")
    private String channel;
    @Excel(name = "省份", width = 15)
    @TableField(exist = false)
    private String province;
    /**总限量*/
    @Excel(name = "总限量", width = 15)
    @TableField(strategy= FieldStrategy.IGNORED)
    @ApiModelProperty(value = "总限量")
    private Integer limitTotalAmount;
    @Excel(name = "向宏总限量", width = 15)
    @TableField(strategy= FieldStrategy.IGNORED)
    @ApiModelProperty(value = "向宏总限量")
    private Integer xianghongTotalLimit;
    /**限量接收短信手机号*/
    @Excel(name = "小峰总限量", width = 15)
    @ApiModelProperty(value = "小峰总限量")
    @TableField(strategy= FieldStrategy.IGNORED)
    private Integer xiaofengTotalLimit;
    /**限量接收短信手机号*/
    @Excel(name = "cpa总限量", width = 15)
    @ApiModelProperty(value = "cpa总限量")
    @TableField(strategy= FieldStrategy.IGNORED)
    private Integer cpaTotalLimit;
    /**限量接收短信手机号*/
    @Excel(name = "董松炜总限量", width = 15)
    @TableField(strategy= FieldStrategy.IGNORED)
    @ApiModelProperty(value = "董松炜总限量")
    private Integer dongswTotalLimit;
    /**cpa接收短信手机号*/
    @Excel(name = "接收短信手机号", width = 15)
    @ApiModelProperty(value = "接收短信手机号")
    private String receiveMobile;
    /**cpa接收短信手机号*/
    @Excel(name = "cpa接收短信手机号", width = 15)
    @ApiModelProperty(value = "cpa接收短信手机号")
    private String cpaReceiveMobile;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")

    private String remark;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
