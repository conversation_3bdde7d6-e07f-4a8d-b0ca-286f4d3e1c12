package com.eleven.cms.aivrbt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CatalogSubResourceTypeEnum {

    COLUMN_RESOURCE(1, "栏目资源"),
    SHORT_PLAY_DRAMA(10001, "短剧剧目"),
    SHORT_PLAY_DRAMA_ITEM(10002, "短剧剧目的剧集"),

    ;

    private final int code;
    private final String desc;


    public static CatalogSubResourceTypeEnum getByCode(int code) {
        for (CatalogSubResourceTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
