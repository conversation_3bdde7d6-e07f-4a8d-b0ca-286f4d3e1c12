<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aivrbt.mapper.AiRingColumnSameTemplateMapper">

    <select id="queryPageList" resultType="com.eleven.cms.aivrbt.entity.AiRingColumnSameTemplate">
        SELECT
            a.id,
            a.column_id,
            a.template_id,
            a.ring_name,
            a.clips_param,
            a.pic_url,
            a.video_url,
            a.`status`,
            a.order_by,
            a.remark,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
        a.ali_ring_url,
            b.column_name
        FROM
            ai_ring_column_same_template a
        LEFT JOIN ai_ring_column b ON a.column_id = b.id
        WHERE 1=1
        <if test="aiRingColumnSameTemplate.columnName != null and aiRingColumnSameTemplate.columnName != ''">
            AND b.column_name=#{aiRingColumnSameTemplate.columnName}
        </if>
        <if test="aiRingColumnSameTemplate.ringName != null and aiRingColumnSameTemplate.ringName != ''">
            AND a.ring_name=#{aiRingColumnSameTemplate.ringName}
        </if>
        <if test="aiRingColumnSameTemplate.status != null">
            AND a.status=#{aiRingColumnSameTemplate.status}
        </if>
        ORDER BY a.create_time DESC
    </select>
</mapper>