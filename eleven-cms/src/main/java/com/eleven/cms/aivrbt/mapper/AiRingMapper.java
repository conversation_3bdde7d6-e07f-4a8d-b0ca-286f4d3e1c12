package com.eleven.cms.aivrbt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.vo.MyCreationQueryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: ai_ring
 * @Author: jeecg-boot
 * @Date: 2024-10-10
 * @Version: V1.0
 */
public interface AiRingMapper extends BaseMapper<AiRing> {

    IPage<MyCreationQueryVO> queryMyCreation(Page<AiRing> page, @Param("mobile") String mobile);

    List<MyCreationQueryVO> queryMyCreationMaking(@Param("idList") List<String> idList);

    Integer queryUsableCount(@Param("mobile") String mobile);
}
