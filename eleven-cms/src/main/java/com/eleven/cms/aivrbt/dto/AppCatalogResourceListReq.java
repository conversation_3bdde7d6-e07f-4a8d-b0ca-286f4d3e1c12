package com.eleven.cms.aivrbt.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AppCatalogResourceListReq {

    @NotNull(message = "父级id不能为空")
    private String pid;
    /**渠道id，区分哪个app*/
    /**资源类型 1=栏目， 2=桌面组件资源，3=静态壁纸资源，4=动态壁纸资源  5=banner资源*/
    /**资源类型对应的资源表id*/
    @ApiModelProperty(value = "资源类型对应的资源表id")
    private String resId;
    /**0:无效 1:有效*/
    @ApiModelProperty(value = "0:无效 1:有效")
    private Integer status;

    private String resName;
}
