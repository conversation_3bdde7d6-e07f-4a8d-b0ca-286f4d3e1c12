<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aivrbt.mapper.AiRingVideoMapper">

    <select id="queryPageList" resultType="com.eleven.cms.aivrbt.entity.AiRingVideo">
        SELECT
            a.id,
            a.copyright_id,
            a.vrbt_product_id,
            a.ring_name,
            a.ring_pic_url,
            a.ring_url,
            a.`status`,
            a.column_id,
            a.order_by,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.available_channel,
            b.column_name
        FROM
            ai_ring_video a
        LEFT JOIN ai_ring_column b ON a.column_id = b.id
        WHERE 1=1
        <if test="aiRingVideo.columnName != null and aiRingVideo.columnName != ''">
            AND b.column_name=#{aiRingVideo.columnName}
        </if>
        <if test="aiRingVideo.ringName != null and aiRingVideo.ringName != ''">
            AND a.ring_name=#{aiRingVideo.ringName}
        </if>
        <if test="aiRingVideo.status != null">
            AND a.status=#{aiRingVideo.status}
        </if>
        <if test="aiRingVideo.availableChannel != null and aiRingVideo.availableChannel !='' ">
            AND a.available_channel  LIKE CONCAT('%',#{aiRingVideo.availableChannel},'%')
        </if>
        ORDER BY a.order_by, a.id
    </select>
</mapper>
