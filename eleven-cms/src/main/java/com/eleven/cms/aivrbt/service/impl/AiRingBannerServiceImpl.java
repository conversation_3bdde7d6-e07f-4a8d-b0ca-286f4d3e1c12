package com.eleven.cms.aivrbt.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.entity.AiRingBanner;
import com.eleven.cms.aivrbt.mapper.AiRingBannerMapper;
import com.eleven.cms.aivrbt.service.IAiRingBannerService;
import com.eleven.cms.aivrbt.vo.AiRingValidBannerListVO;
import com.eleven.cms.aivrbt.vo.AiRingValidBannerVO;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: ai_ring_banner
 * @Author: jeecg-boot
 * @Date: 2024-10-31
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class AiRingBannerServiceImpl extends ServiceImpl<AiRingBannerMapper, AiRingBanner> implements IAiRingBannerService {

    private final AiRingBannerMapper aiRingBannerMapper;

    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_BANNER, key = "#root.methodName +':'+ #channelId + ':' + #resource")
    @Override
    public List<AiRingValidBannerListVO> queryValidBanner(String channelId, String resource) {
        List<AiRingValidBannerVO> bannerVOList = aiRingBannerMapper.queryValidBanner(channelId, resource);

        List<AiRingValidBannerListVO> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(bannerVOList)) {
            result = bannerVOList.stream()
                    .collect(Collectors.groupingBy(AiRingValidBannerVO::getBannerLocation))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        AiRingValidBannerListVO listVO = new AiRingValidBannerListVO();
                        listVO.setBannerLocation(entry.getKey());
                        listVO.setBannerList(entry.getValue());
                        return listVO;
                    })
                    .collect(Collectors.toList());
        }
        return result;
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_BANNER, allEntries = true)
    @Override
    public boolean save(AiRingBanner entity) {
        return super.save(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_BANNER, allEntries = true)
    @Override
    public boolean updateById(AiRingBanner entity) {
        return super.updateById(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_BANNER, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_BANNER, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
