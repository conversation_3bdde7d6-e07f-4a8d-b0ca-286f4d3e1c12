package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.vo.MyCreationQueryVO;

import java.util.List;

/**
 * @Description: ai_ring
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
public interface IAiRingService extends IService<AiRing> {

    /**
     * 查询本月还可免费生成次数
     *
     * @param mobile mobile
     * @return Integer
     */
    Integer queryUsableCount(String mobile);

    /**
     * 查询我的作品数量
     *
     * @param mobile mobile
     * @return Integer
     */
    Integer queryMyCreationCount(String mobile);

    /**
     * 查询我的作品
     *
     * @param page page
     * @param mobile mobile
     * @return IPage<MyCreationQueryVO>
     */
    IPage<MyCreationQueryVO> queryMyCreation(Page<AiRing> page, String mobile);
    /**
     * 查询我的创作中的作品状态
     * <p>
     * 此方法用于根据一系列ID查询对应的创作中环的信息它首先检查输入的ID列表是否为空，
     * 如果为空，则返回一个空列表这样做是为了避免不必要的数据库查询，并确保代码的健壮性
     *
     * @param idList 一个包含多个ID的列表，这些ID用于查询对应的创作中环的信息
     * @return 返回一个MyCreationQueryVO对象的列表，每个对象包含对应的状态
     */
    List<MyCreationQueryVO> queryMyCreationMaking(List<String> idList);
}
