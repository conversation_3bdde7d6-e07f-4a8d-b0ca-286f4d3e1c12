package com.eleven.cms.aivrbt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.entity.AiRingColumn;
import com.eleven.cms.aivrbt.entity.AiRingColumnSameTemplate;
import com.eleven.cms.aivrbt.entity.AiRingVideo;
import com.eleven.cms.aivrbt.mapper.AiRingColumnMapper;
import com.eleven.cms.aivrbt.service.IAiRingColumnSameTemplateService;
import com.eleven.cms.aivrbt.service.IAiRingColumnService;
import com.eleven.cms.aivrbt.service.IAiRingVideoService;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryVO;
import com.eleven.cms.aivrbt.vo.ColumnQueryVO;
import com.eleven.cms.aivrbt.vo.TemplateQueryVO;
import com.eleven.cms.aivrbt.vo.VideoQueryVO;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @Description: ai_ring_column
 * @Author: jeecg-boot
 * @Date: 2024-10-10
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class AiRingColumnServiceImpl extends ServiceImpl<AiRingColumnMapper, AiRingColumn> implements IAiRingColumnService {

    private final AiRingColumnMapper ringColumnMapper;
    private final IAiRingColumnSameTemplateService sameTemplateService;
    private final IAiRingVideoService aiRingVideoService;

    /**
     * AI视频彩铃创作专区-查询栏目及模板
     *
     * @return List<AIColumnAndTemplateQueryVO>
     */
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, key = "#root.methodName+':'+ #channelId")
    @Override
    public List<AIColumnAndTemplateQueryVO> queryAIColumnAndTemplate(String channelId) {
        return ringColumnMapper.queryAIColumnAndTemplate(channelId);
    }

    /**
     * AI视频彩铃创作专区-查询栏目及模板
     *
     * @return List<AIColumnAndTemplateQueryVO>
     */
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, key = "#root.methodName+':'+#channelId")
    @Override
    public List<AIColumnAndTemplateQueryVO> queryAllAIColumnAndTemplate(  String channelId) {
        return ringColumnMapper.queryAllAIColumnAndTemplate(channelId);
    }
    /**
     * 查询栏目
     *
     * @param type type
     * @return List<ColumnQueryVO>
     */
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, key = "#root.methodName + ':' + #p0+ ':' + #channelId + ':' + #resource")
    @Override
    public List<ColumnQueryVO> queryColumn(String type, String channelId, String resource) {
        List<AiRingColumn> list = list(new LambdaQueryWrapper<AiRingColumn>()
                .select(AiRingColumn::getId, AiRingColumn::getColumnName, AiRingColumn::getBackgroundUrl)
                .eq(StringUtils.isNotEmpty(type), AiRingColumn::getType, type)
                .eq(AiRingColumn::getStatus, 1)
                .like(StringUtils.isNotEmpty(channelId),AiRingColumn::getAvailableChannel, channelId)
                .like(StringUtils.isNotEmpty(resource), AiRingColumn::getAvailableMiniApp, resource)
                .orderByAsc(AiRingColumn::getOrderBy));

        List<ColumnQueryVO> result = new ArrayList<>();
        for (AiRingColumn item : list) {
            ColumnQueryVO one = new ColumnQueryVO();
            one.setId(item.getId());
            one.setColumnName(item.getColumnName());
            one.setBackgroundUrl(item.getBackgroundUrl());
            result.add(one);
        }
        return result;
    }

    /**
     * 拍同款-根据栏目ID查询模板
     *
     * @param columnId columnId
     * @return List<TemplateQueryVO>
     */
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, key = "#root.methodName + ':' + #p0")
    @Override
    public List<TemplateQueryVO> queryTemplateBySameColumnId(String columnId) {
        List<AiRingColumnSameTemplate> list = sameTemplateService.list(new LambdaQueryWrapper<AiRingColumnSameTemplate>()
                .eq(AiRingColumnSameTemplate::getColumnId, columnId)
                .eq(AiRingColumnSameTemplate::getStatus, 1)
                .orderByAsc(AiRingColumnSameTemplate::getOrderBy, AiRingColumnSameTemplate::getId));

        List<TemplateQueryVO> result = new ArrayList<>();
        for (AiRingColumnSameTemplate item : list) {
            TemplateQueryVO one = new TemplateQueryVO();
            one.setId(item.getId());
            one.setTemplateId(item.getTemplateId());
            one.setRingName(item.getRingName());
            one.setPicUrl(item.getPicUrl());
            one.setVideoUrl(item.getVideoUrl());
            one.setClipsParam(item.getClipsParam());
            one.setAliRingUrl(item.getAliRingUrl());
            result.add(one);
        }
        return result;
    }

    /**
     * 首页-AI彩铃工坊-查询栏目-根据栏目ID查询铃音视频
     *
     * @param page     page
     * @param columnId columnId
     * @return IPage<VideoQueryVO>
     */
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, key = "#root.methodName + ':'+#columnId +':' + #page.getCurrent()+':' +#page.getSize()+':'+#channelId")
    @Override
    public IPage<VideoQueryVO> pageVideoByClColumnId(Page<AiRingVideo> page, String columnId,String channelId) {
        IPage pageList = aiRingVideoService.page(page, new LambdaQueryWrapper<AiRingVideo>()
                .eq(AiRingVideo::getColumnId, columnId)
                .eq(AiRingVideo::getStatus, 1)
                .like(StringUtils.isNotEmpty(channelId), AiRingVideo::getAvailableChannel, channelId)
                .orderByAsc(AiRingVideo::getOrderBy, AiRingVideo::getId));

        List<AiRingVideo> records = pageList.getRecords();
        List<VideoQueryVO> result = new ArrayList<>();
        for (AiRingVideo item : records) {
            VideoQueryVO one = new VideoQueryVO();
            one.setId(item.getId());
            one.setRingName(item.getRingName());
            one.setRingPicUrl(item.getRingPicUrl());
            one.setRingUrl(item.getRingUrl());
            one.setCopyrightId(item.getCopyrightId());
            one.setAliRingUrl(item.getAliRingUrl());
            one.setVrbtProductId(item.getVrbtProductId());
            one.setAliCoverUrl(item.getAliCoverUrl());
            result.add(one);
        }
        return pageList.setRecords(result);
    }


    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean save(AiRingColumn entity) {
        return super.save(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean updateById(AiRingColumn entity) {
        return super.updateById(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
