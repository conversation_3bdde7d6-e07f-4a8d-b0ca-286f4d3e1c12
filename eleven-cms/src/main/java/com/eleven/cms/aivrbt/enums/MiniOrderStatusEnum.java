package com.eleven.cms.aivrbt.enums;

import lombok.Getter;

@Getter
public enum MiniOrderStatusEnum {
    SUBMIT_PAYMENT_FAIL(-1, "支付订单创建失败"),
    EQUITY_FAIL(-2, "权益下发失败"),
    PENDING_PAYMENT(0, "待支付"),
    PAID(1, "已付款"),
    TIMEOUT_CANCELLED(2, "超时取消"),
    REFUNDING(3, "退款中"),
    REFUND_SUCCESS(4, "退款成功"),
    REFUND_FAILED(5, "退款失败");

    private final Integer code;
    private final String description;

    MiniOrderStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据状态码获取枚举实例
    public static MiniOrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MiniOrderStatusEnum status : MiniOrderStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    // 判断状态是否为退款相关状态
    public boolean isRefundStatus() {
        return this == REFUNDING || this == REFUND_SUCCESS || this == REFUND_FAILED;
    }
}
