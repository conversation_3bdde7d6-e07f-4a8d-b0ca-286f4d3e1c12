<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aivrbt.mapper.AiRingColumnAiTemplateMapper">
    <resultMap id="templateResultMap" type="com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryChildVO">
        <id property="id" column="id"/>
        <id property="hyTemplateId" column="hy_template_id"/>
        <id property="ringName" column="ring_name"/>
        <id property="picUrl" column="pic_url"/>
        <id property="videoUrl" column="video_url"/>
        <id property="picSlotCount" column="pic_slot_count"/>
        <id property="templateId" column="template_id"/>
        <id property="activityId" column="activity_id"/>
        <id property="materialId" column="material_id"/>
        <id property="aiType" column="ai_type"/>
        <id property="remark" column="remark"/>
    </resultMap>
    <select id="queryPageList" resultType="com.eleven.cms.aivrbt.entity.AiRingColumnAiTemplate">
        SELECT a.id,
               a.column_id,
               a.template_id,
               a.ring_name,
               a.order_by,
               a.hy_template_id,
               a.clips_param,
               a.pic_url,
               a.video_url,
               a.status,
               a.pic_slot_count,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               b.column_name
        FROM ai_ring_column_ai_template a
                 LEFT JOIN ai_ring_column b ON a.column_id = b.id
        WHERE 1 = 1
        <if test="aiRingColumnAiTemplate.columnName != null and aiRingColumnAiTemplate.columnName != ''">
            AND b.column_name = #{aiRingColumnAiTemplate.columnName}
        </if>
        <if test="aiRingColumnAiTemplate.ringName != null and aiRingColumnAiTemplate.ringName != ''">
            AND a.ring_name = #{aiRingColumnAiTemplate.ringName}
        </if>
        <if test="aiRingColumnAiTemplate.status != null">
            AND a.status = #{aiRingColumnAiTemplate.status}
        </if>
        ORDER BY a.create_time DESC
    </select>

    <select id="queryAIColumnTemplateByColumnId" resultMap="templateResultMap">
        SELECT id,
               ring_name,
               pic_url,
               video_url,
               hy_template_id,
               pic_slot_count,
               template_id,
               activity_id,
               material_id,
               ai_type,
               remark
        FROM (SELECT art.id,
                     art.ring_name,
                     art.pic_url,
                     art.video_url,
                     art.hy_template_id,
                     art.pic_slot_count,
                     art.template_id,
                     NULL         AS activity_id,
                     NULL         AS material_id,
                     0            AS ai_type,
                     art.order_by AS order_by_template,
                     art.remark
              FROM ai_ring_column_ai_template art
              WHERE art.status = 1
                AND art.column_id = #{columnId}
              UNION ALL
              SELECT art.id,
                     art.ring_name,
                     art.pic_url,
                     art.video_url,
                     NULL         AS hy_template_id,
                     NULL         AS pic_slot_count,
                     art.template_id,
                     art.activity_id,
                     art.material_id,
                     art.ai_type,
                     art.order_by AS order_by_template,
                     art.remark
              FROM ai_ring_column_ai_face_template art
              WHERE art.status = 1
                AND art.column_id = #{columnId}) AS ai_template
        ORDER BY order_by_template
    </select>
</mapper>