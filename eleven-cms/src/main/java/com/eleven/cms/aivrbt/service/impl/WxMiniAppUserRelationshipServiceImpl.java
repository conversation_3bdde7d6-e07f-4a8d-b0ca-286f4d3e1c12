package com.eleven.cms.aivrbt.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.dto.AppLoginDTO;
import com.eleven.cms.aivrbt.dto.WxMiniUserInfoDTO;
import com.eleven.cms.aivrbt.entity.WxMiniAppUserRelationship;
import com.eleven.cms.aivrbt.enums.MiniAndAppRelationShipChannelEnum;
import com.eleven.cms.aivrbt.mapper.WxMiniAppUserRelationshipMapper;
import com.eleven.cms.aivrbt.service.WxMiniAppUserRelationshipService;
import com.eleven.cms.aivrbt.vo.AppUserVO;
import com.eleven.cms.aivrbt.vo.WxMiniGetUserInfoVO;
import com.eleven.cms.client.AppFeignClient;
import com.eleven.cms.wallpaper.entity.MiniAppWechatUser;
import com.eleven.cms.wallpaper.service.IMiniAppWechatUserService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class WxMiniAppUserRelationshipServiceImpl extends ServiceImpl<WxMiniAppUserRelationshipMapper, WxMiniAppUserRelationship> implements WxMiniAppUserRelationshipService {

    @Resource
    private AppFeignClient appFeignClient;

    private static final String MINI = "mini_";

    @Value("${kpApp.tmpToken}")
    private String tmpToken;

    @Resource
    private IMiniAppWechatUserService miniAppWechatUserService;

    @Override
    public WxMiniAppUserRelationship getByMiniOpenId(String miniChannelId, String miniOpenId, String appChannelId, String phoneNumber) {
        return getOne(new LambdaQueryWrapper<WxMiniAppUserRelationship>()
                .eq(WxMiniAppUserRelationship::getMiniChannelId, miniChannelId)
                .eq(WxMiniAppUserRelationship::getMiniOpenId, miniOpenId)
                .eq(WxMiniAppUserRelationship::getMobile, phoneNumber)
                .eq(WxMiniAppUserRelationship::getAppChannelId, appChannelId)
                .eq(WxMiniAppUserRelationship::getStatus, 1)
                .last("limit 1"));
    }

    @Override
    public WxMiniAppUserRelationship getByMobile(String mobile) {
        return getOne(new LambdaQueryWrapper<WxMiniAppUserRelationship>()
                .eq(WxMiniAppUserRelationship::getMobile, mobile)
                .orderByDesc(WxMiniAppUserRelationship::getCreateTime).last("limit 1"));
    }

    @Override
    public WxMiniGetUserInfoVO getUserInfoOrSaveRelationship(WxMiniGetUserInfoVO wxMiniGetUserInfoVO, WxMiniUserInfoDTO wxMiniUserInfoDTO) {
        MiniAndAppRelationShipChannelEnum miniChannelIdAndMiniSource = MiniAndAppRelationShipChannelEnum.getByMiniChannelIdAndMiniSource(wxMiniUserInfoDTO.getChannelId(), wxMiniUserInfoDTO.getMiniSource());
        if (Objects.isNull(miniChannelIdAndMiniSource) || Objects.isNull(wxMiniGetUserInfoVO)) {
            return null;
        }
        String appChannelId = miniChannelIdAndMiniSource.getAppChannelId();
        WxMiniAppUserRelationship wxMiniAppUserRelationship = getByMiniOpenId(wxMiniUserInfoDTO.getChannelId(), wxMiniGetUserInfoVO.getOpenId(), appChannelId, wxMiniGetUserInfoVO.getPhoneNumber());
        if (Objects.isNull(wxMiniAppUserRelationship)) {
            wxMiniAppUserRelationship = new WxMiniAppUserRelationship();
            wxMiniAppUserRelationship.setAppUid(wxMiniGetUserInfoVO.getAppUid());
            wxMiniAppUserRelationship.setAppChannelId(appChannelId);
            wxMiniAppUserRelationship.setMiniChannelId(wxMiniUserInfoDTO.getChannelId());
            wxMiniAppUserRelationship.setMobile(wxMiniGetUserInfoVO.getPhoneNumber());
            wxMiniAppUserRelationship.setStatus(1);
            wxMiniAppUserRelationship.setMiniOpenId(wxMiniGetUserInfoVO.getOpenId());
            wxMiniAppUserRelationship.setCreateBy(wxMiniGetUserInfoVO.getPhoneNumber());
            wxMiniAppUserRelationship.setCreateTime(new Date());
        }
        wxMiniAppUserRelationship.setUpdateBy(wxMiniGetUserInfoVO.getPhoneNumber());
        wxMiniAppUserRelationship.setUpdateTime(new Date());
        AppLoginDTO appLoginDTO = AppLoginDTO.builder()
                .loginType(4)//微信
                .appVersion(wxMiniUserInfoDTO.getAppVersion())
                .channelId(appChannelId)
                .captcha("686868")
                .sourceChannel(miniChannelIdAndMiniSource.getAppSourceChannelId())
                .mobile(wxMiniGetUserInfoVO.getPhoneNumber())
                .deviceInfo(wxMiniUserInfoDTO.getDeviceInfo())
                .oaId(MINI + wxMiniGetUserInfoVO.getOpenId()).build();
        Result<AppUserVO> result = appFeignClient.appMiniLogin(appLoginDTO, tmpToken);
        if (result.isSuccess()) {
            AppUserVO appUserVO = result.getResult();
            wxMiniGetUserInfoVO.setAppUid(appUserVO.getId());
            wxMiniGetUserInfoVO.setMembershipType(appUserVO.getMembershipType());
            wxMiniGetUserInfoVO.setMembershipExpiryTime(appUserVO.getMembershipExpiryTime());
            wxMiniGetUserInfoVO.setMembershipStartTime(appUserVO.getMembershipStartTime());
            wxMiniGetUserInfoVO.setMembershipStatus(appUserVO.getMembershipStatus());
            wxMiniGetUserInfoVO.setToken(result.getToken());
            wxMiniAppUserRelationship.setAppUid(appUserVO.getId());
            saveOrUpdate(wxMiniAppUserRelationship);
            return wxMiniGetUserInfoVO;
        } else {
            log.error("getUserInfoOrSaveRelationship error: {}", JSONObject.toJSONString(result));
            return null;
        }
    }

    @Override
    public Result<Object> loginAndApp(AppLoginDTO appLoginDTO) {
        MiniAndAppRelationShipChannelEnum miniChannelIdAndMiniSource = MiniAndAppRelationShipChannelEnum.getByAppSourceChannelId(appLoginDTO.getSourceChannel());
        if (Objects.isNull(miniChannelIdAndMiniSource)) {
            return Result.error("改微信应用未予app绑定请联系客服");
        }
        MiniAppWechatUser miniAppWechatUser = miniAppWechatUserService.lambdaQuery().eq(MiniAppWechatUser::getOpenId, appLoginDTO.getOpenId())
                .eq(MiniAppWechatUser::getChannelId, miniChannelIdAndMiniSource.getMiniChannelId())
                .last("limit 1").one();
        if (Objects.isNull(miniAppWechatUser)) {
            miniAppWechatUser = new MiniAppWechatUser();
            miniAppWechatUser.setOpenId(appLoginDTO.getOpenId());
            miniAppWechatUser.setBusinessType(miniChannelIdAndMiniSource.getMiniChannelId());
            miniAppWechatUser.setChannelId(miniChannelIdAndMiniSource.getMiniChannelId());
            miniAppWechatUser.setMobile(appLoginDTO.getMobile());
            miniAppWechatUserService.save(miniAppWechatUser);
        }
        Result<AppUserVO> result = appFeignClient.appLogin(appLoginDTO);
        if (!result.isSuccess()) {
            log.error("loginAndApp error: {}", JSONObject.toJSONString(result));
            return Result.error(result.getMessage());
        }
        AppUserVO appUserVO = result.getResult();

        String appChannelId = miniChannelIdAndMiniSource.getAppChannelId();
        WxMiniAppUserRelationship wxMiniAppUserRelationship = getByMiniOpenId(miniChannelIdAndMiniSource.getMiniChannelId(), appLoginDTO.getOpenId(), appChannelId, appLoginDTO.getMobile());
        if (Objects.isNull(wxMiniAppUserRelationship)) {
            wxMiniAppUserRelationship = new WxMiniAppUserRelationship();
            wxMiniAppUserRelationship.setAppUid(appUserVO.getId());
            wxMiniAppUserRelationship.setAppChannelId(appChannelId);
            wxMiniAppUserRelationship.setMiniChannelId(miniChannelIdAndMiniSource.getMiniChannelId());
            wxMiniAppUserRelationship.setMobile(appLoginDTO.getMobile());
            wxMiniAppUserRelationship.setStatus(1);
            wxMiniAppUserRelationship.setMiniOpenId(appLoginDTO.getOpenId());
            wxMiniAppUserRelationship.setCreateBy(appLoginDTO.getMobile());
            wxMiniAppUserRelationship.setCreateTime(new Date());
        }
        wxMiniAppUserRelationship.setUpdateBy(appLoginDTO.getMobile());
        wxMiniAppUserRelationship.setUpdateTime(new Date());
        wxMiniAppUserRelationship.setAppUid(appUserVO.getId());
        saveOrUpdate(wxMiniAppUserRelationship);
        return Result.ok();
    }
}