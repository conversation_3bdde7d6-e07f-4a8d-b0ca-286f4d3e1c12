package com.eleven.cms.aivrbt.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.eleven.cms.aivrbt.entity.AiRingVideo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: ai_ring_video
 * @Author: jeecg-boot
 * @Date:   2024-10-16
 * @Version: V1.0
 */
public interface AiRingVideoMapper extends BaseMapper<AiRingVideo> {

    /**
     * 分页列表查询
     *
     * @param page page
     * @param aiRingVideo aiRingVideo
     * @return IPage<AiRingVideo>
     */
    IPage<AiRingVideo> queryPageList(Page<AiRingVideo> page, AiRingVideo aiRingVideo);
}
