package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.entity.AiRingColumn;
import com.eleven.cms.aivrbt.entity.AiRingVideo;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryVO;
import com.eleven.cms.aivrbt.vo.ColumnQueryVO;
import com.eleven.cms.aivrbt.vo.TemplateQueryVO;
import com.eleven.cms.aivrbt.vo.VideoQueryVO;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * @Description: ai_ring_column
 * @Author: jeecg-boot
 * @Date: 2024-10-10
 * @Version: V1.0
 */
public interface IAiRingColumnService extends IService<AiRingColumn> {

    /**
     * AI视频彩铃创作专区-查询栏目及模板
     *
     * @return List<AIColumnAndTemplateQueryVO>
     */
    List<AIColumnAndTemplateQueryVO> queryAIColumnAndTemplate(String channelId) ;


    /**
     * 拍同款-根据栏目ID查询模板
     *
     * @param columnId columnId
     * @return List<TemplateQueryVO>
     */
    List<TemplateQueryVO> queryTemplateBySameColumnId(String columnId);


    List<AIColumnAndTemplateQueryVO> queryAllAIColumnAndTemplate(String channelId);

    /**
     * 查询栏目
     *
     * @param type type
     * @return List<ColumnQueryVO>
     */
    List<ColumnQueryVO> queryColumn(String type, String channelId, String resource);

    /**
     * 首页-AI彩铃工坊-查询栏目-根据栏目ID查询铃音视频
     *
     * @param page     page
     * @param columnId columnId
     * @return IPage<VideoQueryVO>
     */
    IPage<VideoQueryVO> pageVideoByClColumnId(Page<AiRingVideo> page, String columnId,String channelId);
}
