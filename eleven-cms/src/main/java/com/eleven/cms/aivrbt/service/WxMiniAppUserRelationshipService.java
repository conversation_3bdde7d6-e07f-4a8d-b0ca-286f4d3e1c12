package com.eleven.cms.aivrbt.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.dto.AppLoginDTO;
import com.eleven.cms.aivrbt.dto.WxMiniUserInfoDTO;
import com.eleven.cms.aivrbt.entity.WxMiniAppUserRelationship;
import com.eleven.cms.aivrbt.vo.WxMiniGetUserInfoVO;
import org.jeecg.common.api.vo.Result;

public interface WxMiniAppUserRelationshipService extends IService<WxMiniAppUserRelationship> {

    WxMiniAppUserRelationship getByMiniOpenId(String miniChannelId, String miniOpenId, String appChannelId, String phoneNumber);

    WxMiniAppUserRelationship getByMobile(String mobile);

    WxMiniGetUserInfoVO getUserInfoOrSaveRelationship(WxMiniGetUserInfoVO wxMiniGetUserInfoVO, WxMiniUserInfoDTO wxMiniUserInfoDTO);

    Result<Object> loginAndApp(AppLoginDTO appLoginDTO);
}
