package com.eleven.cms.aivrbt.vo;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 通用app栏目资源
 */
@Data
@SuperBuilder
public class CommonAppCatalogSubResVO {

    private String id;

    private String name;

    private String coverUrl;

    /**
     * 封面预览缩略图
     */
    private String coverPreviewUrl;

    private String jumpUrl;

    private Integer userRange;

    private Integer resType;

    private Integer orderNum;

    /**
     * 是否收藏
     */
    private Boolean favoriteStatus;

    /**
     * 角标
     */
    private String cornerUrl;
    private BigDecimal hotValue;
}
