package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ai_ring_face_image
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Data
@TableName("ai_ring_face_image")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ai_ring_face_image对象", description="ai_ring_face_image")
public class AiRingFaceImage implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**铃音ID*/
	@Excel(name = "铃音ID", width = 15)
    @ApiModelProperty(value = "铃音ID")
    private String aiRingId;
	/**上传人的手机号*/
	@Excel(name = "上传人的手机号", width = 15)
    @ApiModelProperty(value = "上传人的手机号")
    private String mobile;
	/**腾讯云活动ID*/
	@Excel(name = "腾讯云活动ID", width = 15)
    @ApiModelProperty(value = "腾讯云活动ID")
    private String activityId;
	/**腾讯云素材ID*/
	@Excel(name = "腾讯云素材ID", width = 15)
    @ApiModelProperty(value = "腾讯云素材ID")
    private String materialId;
	/**用户上传原图URL列表*/
	@Excel(name = "用户上传原图URL列表", width = 15)
    @ApiModelProperty(value = "用户上传原图URL列表")
    private String sourceUserAliUrls;
	/**腾讯云换脸图片URL*/
	@Excel(name = "腾讯云换脸图片URL", width = 15)
    @ApiModelProperty(value = "腾讯云换脸图片URL")
    private String convertImageAliUrl;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
