package com.eleven.cms.aivrbt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.service.IAiRingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: ai_ring
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Api(tags="ai_ring")
@RestController
@RequestMapping("/cms/aiRing")
@Slf4j
public class AiRingController extends JeecgController<AiRing, IAiRingService> {
	@Autowired
	private IAiRingService aiRingService;

	/**
	 * 分页列表查询
	 *
	 * @param aiRing
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai_ring-分页列表查询")
	@ApiOperation(value="ai_ring-分页列表查询", notes="ai_ring-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AiRing aiRing,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AiRing> queryWrapper = QueryGenerator.initQueryWrapper(aiRing, req.getParameterMap());
		Page<AiRing> page = new Page<AiRing>(pageNo, pageSize);
		IPage<AiRing> pageList = aiRingService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param aiRing
	 * @return
	 */
	@AutoLog(value = "ai_ring-添加")
	@ApiOperation(value="ai_ring-添加", notes="ai_ring-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AiRing aiRing) {
		aiRingService.save(aiRing);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param aiRing
	 * @return
	 */
	@AutoLog(value = "ai_ring-编辑")
	@ApiOperation(value="ai_ring-编辑", notes="ai_ring-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AiRing aiRing) {
		aiRingService.updateById(aiRing);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring-通过id删除")
	@ApiOperation(value="ai_ring-通过id删除", notes="ai_ring-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aiRingService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai_ring-批量删除")
	@ApiOperation(value="ai_ring-批量删除", notes="ai_ring-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aiRingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring-通过id查询")
	@ApiOperation(value="ai_ring-通过id查询", notes="ai_ring-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AiRing aiRing = aiRingService.getById(id);
		if(aiRing==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aiRing);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aiRing
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AiRing aiRing) {
        return super.exportXls(request, aiRing, AiRing.class, "ai_ring");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiRing.class);
    }

}
