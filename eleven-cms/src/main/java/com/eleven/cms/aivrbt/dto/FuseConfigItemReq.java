package com.eleven.cms.aivrbt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class FuseConfigItemReq {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 配置主键id
     */
    @ApiModelProperty(value = "配置主键id")
    private String fuseConfigId;
    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 渠道类型
     */
    @ApiModelProperty(value = "渠道类型")
    private String channelType;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;
    /**
     * 是否是默认 1：是 0：不是
     */
    @ApiModelProperty(value = "是否是默认 1：是 0：不是")
    private Integer defaultChannelCode;
    /**
     * 状态:0=无效,1=有效
     */
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    private Integer status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
