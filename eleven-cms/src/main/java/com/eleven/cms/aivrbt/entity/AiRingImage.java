package com.eleven.cms.aivrbt.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: ai_ring_image
 * @Author: jeecg-boot
 * @Date:   2024-10-23
 * @Version: V1.0
 */
@Data
@TableName("ai_ring_image")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ai_ring_image对象", description="ai_ring_image")
public class AiRingImage implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**铃音ID*/
	@Excel(name = "铃音ID", width = 15)
    @ApiModelProperty(value = "铃音ID")
    private String aiRingId;
	/**上传人的手机号*/
	@Excel(name = "上传人的手机号", width = 15)
    @ApiModelProperty(value = "上传人的手机号")
    private String mobile;
	/**文件名*/
	@Excel(name = "文件名", width = 15)
    @ApiModelProperty(value = "文件名")
    private String fileName;
	/**海艺模板ID*/
	@Excel(name = "海艺模板ID", width = 15)
    @ApiModelProperty(value = "海艺模板ID")
    private String hyTemplateId;
	/**海艺图生文ID*/
	@Excel(name = "海艺图生文ID", width = 15)
    @ApiModelProperty(value = "海艺图生文ID")
    private String imgToTextTaskId;
	/**风格转换任务ID*/
	@Excel(name = "风格转换任务ID", width = 15)
    @ApiModelProperty(value = "风格转换任务ID")
    private String filterCreateTaskId;
	/**阿里云原图*/
	@Excel(name = "阿里云原图", width = 15)
    @ApiModelProperty(value = "阿里云原图")
    private String sourceImageAliUrl;
	/**海艺原图*/
	@Excel(name = "海艺原图", width = 15)
    @ApiModelProperty(value = "海艺原图")
    private String sourceImageHyUrl;
	/**海艺转换图*/
	@Excel(name = "海艺转换图", width = 15)
    @ApiModelProperty(value = "海艺转换图")
    private String convertImageHyUrl;
	/**阿里云转换图*/
	@Excel(name = "阿里云转换图", width = 15)
    @ApiModelProperty(value = "阿里云转换图")
    private String convertImageAliUrl;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
