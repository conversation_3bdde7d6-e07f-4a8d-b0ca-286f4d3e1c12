package com.eleven.cms.aivrbt.annotaion.aop;

import com.eleven.cms.aivrbt.annotaion.DistributedLock;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeecg.common.util.RedisUtil;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

import static com.eleven.cms.aivrbt.service.impl.HaiYiAiService.AI_TAG_KEY;

@Aspect
@Component
@RequiredArgsConstructor
public class DistributedLockAspect {

    private final RedisUtil redisUtil;

    @Around("@annotation(com.eleven.cms.aivrbt.annotaion.DistributedLock)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        DistributedLock distributedLock = AnnotationUtils.findAnnotation(method, com.eleven.cms.aivrbt.annotaion.DistributedLock.class);

        String lockKey = distributedLock.name();
        String redisKey = AI_TAG_KEY + ":distributedLock:" + lockKey;
        boolean isLocked = false;
        try {
            isLocked = redisUtil.setIfAbsent(redisKey, "locked", 60);
            if (isLocked) {
                return joinPoint.proceed();
            }
        } finally {
            if (isLocked) {
                redisUtil.del(redisKey);
            }
        }
        return null;
    }
}