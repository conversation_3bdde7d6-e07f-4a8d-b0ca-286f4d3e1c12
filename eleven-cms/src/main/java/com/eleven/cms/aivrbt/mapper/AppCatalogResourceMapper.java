package com.eleven.cms.aivrbt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.aivrbt.dto.CatalogResUsageInfoDTO;
import com.eleven.cms.aivrbt.entity.MiniAppCatalogResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: app_catalog_resource
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
@DS("miniapp")
public interface AppCatalogResourceMapper extends BaseMapper<MiniAppCatalogResource> {

    List<CatalogResUsageInfoDTO> listUsageCatalogByResId(@Param("resType") int resType, @Param("resIds") List<String> resIds);

}
