package com.eleven.cms.aivrbt.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.aivrbt.entity.AiRingImage;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.aivrbt.service.IAiRingImageService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: ai_ring_image
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Api(tags="ai_ring_image")
@RestController
@RequestMapping("/cms/aiRingImage")
@Slf4j
public class AiRingImageController extends JeecgController<AiRingImage, IAiRingImageService> {
	@Autowired
	private IAiRingImageService aiRingImageService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aiRingImage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai_ring_image-分页列表查询")
	@ApiOperation(value="ai_ring_image-分页列表查询", notes="ai_ring_image-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AiRingImage aiRingImage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AiRingImage> queryWrapper = QueryGenerator.initQueryWrapper(aiRingImage, req.getParameterMap());
		Page<AiRingImage> page = new Page<AiRingImage>(pageNo, pageSize);
		IPage<AiRingImage> pageList = aiRingImageService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aiRingImage
	 * @return
	 */
	@AutoLog(value = "ai_ring_image-添加")
	@ApiOperation(value="ai_ring_image-添加", notes="ai_ring_image-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AiRingImage aiRingImage) {
		aiRingImageService.save(aiRingImage);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aiRingImage
	 * @return
	 */
	@AutoLog(value = "ai_ring_image-编辑")
	@ApiOperation(value="ai_ring_image-编辑", notes="ai_ring_image-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AiRingImage aiRingImage) {
		aiRingImageService.updateById(aiRingImage);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_image-通过id删除")
	@ApiOperation(value="ai_ring_image-通过id删除", notes="ai_ring_image-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aiRingImageService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai_ring_image-批量删除")
	@ApiOperation(value="ai_ring_image-批量删除", notes="ai_ring_image-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aiRingImageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_image-通过id查询")
	@ApiOperation(value="ai_ring_image-通过id查询", notes="ai_ring_image-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AiRingImage aiRingImage = aiRingImageService.getById(id);
		if(aiRingImage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aiRingImage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aiRingImage
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AiRingImage aiRingImage) {
        return super.exportXls(request, aiRingImage, AiRingImage.class, "ai_ring_image");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiRingImage.class);
    }

}
