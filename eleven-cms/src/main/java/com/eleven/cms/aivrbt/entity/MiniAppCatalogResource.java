package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: app_catalog_resource
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
@Data
@Builder
@TableName("mini_app_catalog_resource")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="app_catalog_resource对象", description="app_catalog_resource")
public class MiniAppCatalogResource implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**上级栏目id*/
	@Excel(name = "上级栏目id", width = 15)
    @ApiModelProperty(value = "上级栏目id")
    private String pid;
	/**渠道id，区分哪个app*/
	@Excel(name = "渠道id，区分哪个app", width = 15)
    @ApiModelProperty(value = "渠道id，区分哪个app")
    private String channelId;
	/**资源类型 1=栏目， 2=桌面组件资源，3=静态壁纸资源，4=动态壁纸资源  5=banner资源*/
	@Excel(name = "资源类型", width = 15)
    @ApiModelProperty(value = "资源类型")
    @Dict(dicCode = "app_channel_id")
    private Integer resType;
	/**资源类型对应的资源表id*/
	@Excel(name = "资源类型对应的资源表id", width = 15)
    @ApiModelProperty(value = "资源类型对应的资源表id")
    private String resId;
    /**
     * 资源名称
     */
    private String resName;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderNum;
    private String cornerUrl;

    /**
     * 0:无效 1:有效
     */
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    @Dict(dicCode = "available_status")
    private Integer status;
	/**0:全部用户 1:会员用户 暂时未启用，*/
	@Excel(name = "0:全部用户 1:会员用户", width = 15)
    @ApiModelProperty(value = "0:全部用户 1:会员用户")
    private Integer userRange;
	/**逻辑删除 0:未删除 1:已删除*/
	@Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
