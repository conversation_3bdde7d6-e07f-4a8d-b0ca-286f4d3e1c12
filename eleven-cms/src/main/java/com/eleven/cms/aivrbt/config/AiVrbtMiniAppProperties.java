package com.eleven.cms.aivrbt.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2024/10/9 10:54
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "ai-mini-app.manager")
@Slf4j
public class AiVrbtMiniAppProperties {

    private Map<String, String> appIdManager;

    private Map<String, String> secretManager;
}
