package com.eleven.cms.aivrbt.controller;

import com.eleven.cms.aivrbt.dto.AppCatalogListReqDTO;
import com.eleven.cms.aivrbt.dto.AppCatalogResDetailReqDTO;
import com.eleven.cms.aivrbt.dto.AppCatalogResListReqDTO;
import com.eleven.cms.aivrbt.service.IAppCatalogResourceService;
import com.eleven.cms.aivrbt.service.IAppCatalogService;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * ydeng
 */
@RestController
@RequestMapping("/aivrbt/api")
public class MiniCatalogController {

    @Resource
    private IAppCatalogService appCatalogService;

    @Resource
    private IAppCatalogResourceService appCatalogResourceService;

    /**
     * 通用查询栏目接口修改慎重
     *
     * @param
     * @return
     */
    @ApiOperation("查询栏目")
    @GetMapping("/catalog/list")
    public Result<Object> catalogList(@Validated AppCatalogListReqDTO reqDTO) {
        return appCatalogService.listAppCatalog(reqDTO);
    }


    /**
     * 通用查询栏目资源接口修改慎重
     *
     * @param
     * @return
     */
    @ApiOperation("查询栏目资源列表")
    @GetMapping("/catalog/res/list")
    public Result<Object> catalogResList(@Validated AppCatalogResListReqDTO reqDTO) {
        return appCatalogResourceService.catalogResList(reqDTO);
    }

    /**
     * 通用查询栏目资源详情
     *
     * @param
     * @return
     */
    @ApiOperation("查询栏目资源详情")
    @GetMapping("/catalog/res/detail")
    public Result<Object> catalogResDetail(@Validated AppCatalogResDetailReqDTO reqDTO) {
        return appCatalogResourceService.catalogResDetail(reqDTO);
    }


}
