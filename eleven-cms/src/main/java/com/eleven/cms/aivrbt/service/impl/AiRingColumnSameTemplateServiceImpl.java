package com.eleven.cms.aivrbt.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.entity.AiRingColumnSameTemplate;
import com.eleven.cms.aivrbt.mapper.AiRingColumnSameTemplateMapper;
import com.eleven.cms.aivrbt.service.IAiRingColumnSameTemplateService;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: ai_ring_column_same_template
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class AiRingColumnSameTemplateServiceImpl extends ServiceImpl<AiRingColumnSameTemplateMapper, AiRingColumnSameTemplate> implements IAiRingColumnSameTemplateService {

    private final AiRingColumnSameTemplateMapper aiRingColumnSameTemplateMapper;

    /**
     * 分页列表查询
     *
     * @param page page
     * @param aiRingColumnSameTemplate aiRingColumnSameTemplate
     * @return IPage<AiRingColumnSameTemplate>
     */
    @Override
    public IPage<AiRingColumnSameTemplate> queryPageList(Page<AiRingColumnSameTemplate> page, AiRingColumnSameTemplate aiRingColumnSameTemplate) {
        return aiRingColumnSameTemplateMapper.queryPageList(page,aiRingColumnSameTemplate);
    }


    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean save(AiRingColumnSameTemplate entity) {
        return super.save(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean updateById(AiRingColumnSameTemplate entity) {
        return super.updateById(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
