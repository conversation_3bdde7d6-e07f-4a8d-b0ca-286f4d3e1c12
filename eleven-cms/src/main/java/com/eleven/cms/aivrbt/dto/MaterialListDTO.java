package com.eleven.cms.aivrbt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @datetime 2024/10/28 15:59
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MaterialListDTO {

    @JsonProperty("ActivityId")
    private String ActivityId;

    @JsonProperty("MaterialId")
    private String MaterialId = "";

    @JsonProperty("Offset")
    private Integer Offset;

    @JsonProperty("Limit")
    private Integer Limit;
}
