package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ai_ring_video
 * @Author: jeecg-boot
 * @Date:   2024-10-24
 * @Version: V1.0
 */
@Data
@TableName("ai_ring_video")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ai_ring_video对象", description="ai_ring_video")
public class AiRingVideo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**版权ID*/
	@Excel(name = "版权ID", width = 15)
    @ApiModelProperty(value = "版权ID")
    private String copyrightId;
	/**产品ID*/
	@Excel(name = "产品ID", width = 15)
    @ApiModelProperty(value = "产品ID")
    private String vrbtProductId;
	/**铃音名称*/
	@Excel(name = "铃音名称", width = 15)
    @ApiModelProperty(value = "铃音名称")
    private String ringName;
	/**铃音图片地址*/
	@Excel(name = "铃音图片地址", width = 15)
    @ApiModelProperty(value = "铃音图片地址")
    private String ringPicUrl;
	/**铃音播放地址*/
	@Excel(name = "铃音播放地址", width = 15)
    @ApiModelProperty(value = "铃音播放地址")
    private String ringUrl;

    @Excel(name = "可用渠道号", width = 15)
    @ApiModelProperty(value = "可用渠道号")
    @Dict(dicCode = "available_channel")
    private String availableChannel;

	/**状态:0=无效,1=有效*/
	@Excel(name = "状态:0=无效,1=有效", width = 15)
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    @Dict(dicCode = "ai_clcyz_column_status")
    private Integer status;
	/**栏目id*/
	@Excel(name = "栏目id", width = 15)
    @ApiModelProperty(value = "栏目id")
    private String columnId;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderBy;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    private String columnName;

    @Excel(name = "阿里云铃音地址", width = 15)
    @ApiModelProperty(value = "阿里云铃音地址")
    private String aliRingUrl;
    private String aliCoverUrl;
}
