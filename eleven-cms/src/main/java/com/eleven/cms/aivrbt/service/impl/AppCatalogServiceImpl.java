package com.eleven.cms.aivrbt.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.dto.AppCatalogListReqDTO;
import com.eleven.cms.aivrbt.entity.MiniAppCatalog;
import com.eleven.cms.aivrbt.enums.CatalogSubResourceTypeEnum;
import com.eleven.cms.aivrbt.enums.MiniAppChannelEnum;
import com.eleven.cms.aivrbt.enums.StatusEnum;
import com.eleven.cms.aivrbt.mapper.AppCatalogMapper;
import com.eleven.cms.aivrbt.service.IAppCatalogService;
import com.eleven.cms.aivrbt.utils.RedisCacheKey;
import com.eleven.cms.aivrbt.vo.AppCatalogVo;
import com.eleven.cms.aivrbt.vo.CommonAppCatalogListVO;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: app_catalog
 * @Author: jeecg-boot
 * @Date: 2025-03-13
 * @Version: V1.0
 */
@Service
public class AppCatalogServiceImpl extends ServiceImpl<AppCatalogMapper, MiniAppCatalog> implements IAppCatalogService {

    @Resource
    private RedisUtil redisUtil;

    private static final String TOP_CATALOG_PARENT_ID = "0";

    @Override
    public IPage<AppCatalogVo> pageVo(Page<MiniAppCatalog> page, LambdaQueryWrapper<MiniAppCatalog> queryWrapper) {
        IPage<MiniAppCatalog> pageList = page(page, queryWrapper);
        IPage<AppCatalogVo> catalogIPage = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        catalogIPage.setPages(page.getPages());
        List<AppCatalogVo> collect = pageList.getRecords().stream().map(x -> {
            AppCatalogVo appCatalogVo = BeanUtil.copyProperties(x, AppCatalogVo.class);
            try {
                appCatalogVo.setChannelIdText(Objects.requireNonNull(MiniAppChannelEnum.getByCode(x.getChannelId())).getDesc());
                appCatalogVo.setStatusText(Objects.requireNonNull(StatusEnum.getByCode(x.getStatus())).getDesc());
                appCatalogVo.setSubResourceTypeText(Objects.requireNonNull(CatalogSubResourceTypeEnum.getByCode(x.getSubResourceType())).getDesc());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            return appCatalogVo;
        }).collect(Collectors.toList());
        catalogIPage.setRecords(collect);
        return catalogIPage;

    }

    /**
     * 通用app栏目查询接口
     *
     * @param reqDTO
     * @return
     */
    @Override
    public Result<Object> listAppCatalog(AppCatalogListReqDTO reqDTO) {
        String newChannelId = reqDTO.getChannelId();

        MiniAppCatalog catalog = getOne(new LambdaQueryWrapper<MiniAppCatalog>()
                .eq(MiniAppCatalog::getChannelId, newChannelId)
                .eq(MiniAppCatalog::getStyleCode, reqDTO.getStyleCode())
                .eq(MiniAppCatalog::getStatus, StatusEnum.VALID.getCode()).last("limit 1"));
        if (Objects.isNull(catalog)) {
            return Result.error("栏目不存在");
        }
        String redisKey = String.format(RedisCacheKey.APP_COMMON_CATALOG_LIST, reqDTO.getChannelId(), catalog.getId());
        Object catalogListObj = redisUtil.get(redisKey);
        if (Objects.nonNull(catalogListObj)) {
            CommonAppCatalogListVO catalogListVO = JSONUtil.toBean(catalogListObj.toString(), CommonAppCatalogListVO.class);
            return Result.ok(catalogListVO);
        }
        List<MiniAppCatalog> miniAppCatalogs = list(new LambdaQueryWrapper<MiniAppCatalog>()
                .eq(MiniAppCatalog::getPid, catalog.getId())
                .eq(MiniAppCatalog::getStatus, StatusEnum.VALID.getCode()).orderByAsc(MiniAppCatalog::getOrderNum, MiniAppCatalog::getId));
        List<CommonAppCatalogListVO> subResList = miniAppCatalogs.stream().map(x -> CommonAppCatalogListVO.builder()
                .coverUrl(x.getCoverUrl())
                .id(x.getId())
                .name(x.getName())
                .jumpUrl(x.getJumpUrl())
                .orderNum(x.getOrderNum())
                .userRange(x.getUserRange())
                .build()).sorted(Comparator.comparing(CommonAppCatalogListVO::getOrderNum)).collect(Collectors.toList());
        CommonAppCatalogListVO catalogList = CommonAppCatalogListVO.builder()
                .id(catalog.getId())
                .name(catalog.getName())
                .subList(subResList)
                .build();
        redisUtil.set(redisKey, JSONUtil.toJsonStr(catalogList), 60 * 60 * 24);
        return Result.ok(catalogList);
    }

    @Override
    public void deleteCache(MiniAppCatalog miniAppCatalog) {

        String redisKey = String.format(RedisCacheKey.APP_COMMON_CATALOG_LIST, miniAppCatalog.getChannelId(), miniAppCatalog.getId());
        redisUtil.del(redisKey);
        String resourceRedisKey = String.format(RedisCacheKey.APP_COMMON_CATALOG_RESOURCE_LIST_MATCH, miniAppCatalog.getChannelId(), miniAppCatalog.getId());
        Set<String> matchKeySet = (Set<String>) redisUtil.get(resourceRedisKey);
        if (ObjectUtil.isNotEmpty(matchKeySet)) {
            matchKeySet.forEach(x -> {
                redisUtil.del(x);
            });
        }
        redisUtil.del(redisKey);
    }
}
