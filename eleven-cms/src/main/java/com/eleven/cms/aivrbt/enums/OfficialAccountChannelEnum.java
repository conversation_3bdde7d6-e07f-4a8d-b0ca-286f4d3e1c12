package com.eleven.cms.aivrbt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 小程序app渠道枚举
 *
 * <AUTHOR>
 * @datetime 2025/2/27
 */
@Getter
@AllArgsConstructor
public enum OfficialAccountChannelEnum {

    COOL_INCOMING_CALL_OFFICIAL_ACCOUNT("炫酷来电", "coolIncomingCall-wxOfficialAccount", "炫酷彩铃秀公众号", "WX", "OA"),
    ;
    private final String channelId;

    private final String sourceChannel;

    private final String desc;

    private final String source;

    private final String tradeType;


    public static OfficialAccountChannelEnum getBySourceChannel(String sourceChannel) {
        for (OfficialAccountChannelEnum appChannelEnum : OfficialAccountChannelEnum.values()) {
            if (Objects.equals(sourceChannel, appChannelEnum.getSourceChannel())) {
                return appChannelEnum;
            }
        }
        return null;
    }
}
