package com.eleven.cms.aivrbt.controller.romote;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.MiniAppCatalog;
import com.eleven.cms.aivrbt.service.IAppCatalogService;
import com.eleven.cms.aivrbt.vo.AppCatalogVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: app_catalog
 * @Author: jeecg-boot
 * @Date: 2025-03-13
 * @Version: V1.0
 */
@Api(tags = "app_catalog")
@RestController
@RequestMapping("/api/rpc/cms/manage/catalog")
@Slf4j
public class RpcAppCatalogManagerController extends JeecgController<MiniAppCatalog, IAppCatalogService> {
    @Autowired
    private IAppCatalogService appCatalogService;

    /**
     * 分页列表查询
     *
     * @param miniAppCatalog
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppCatalog miniAppCatalog,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<MiniAppCatalog> page = new Page<MiniAppCatalog>(pageNo, pageSize);

        IPage<AppCatalogVo> pageList = appCatalogService.pageVo(page, new LambdaQueryWrapper<MiniAppCatalog>()
                .eq(ObjectUtil.isNotEmpty(miniAppCatalog.getPid()), MiniAppCatalog::getPid, miniAppCatalog.getPid())
                .like(ObjectUtil.isNotEmpty(miniAppCatalog.getName()), MiniAppCatalog::getName, miniAppCatalog.getName())
                .eq(ObjectUtil.isNotEmpty(miniAppCatalog.getStatus()), MiniAppCatalog::getStatus, miniAppCatalog.getStatus())
                .eq(ObjectUtil.isNotEmpty(miniAppCatalog.getChannelId()), MiniAppCatalog::getChannelId, miniAppCatalog.getChannelId())
                .orderByAsc(MiniAppCatalog::getOrderNum, MiniAppCatalog::getCreateTime)
        );

        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param miniAppCatalog
     * @return
     */
    @AutoLog(value = "app_catalog-添加")
    @ApiOperation(value = "app_catalog-添加", notes = "app_catalog-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniAppCatalog miniAppCatalog) {
        LoginUser sysUser = HttpUtil.getCurrUser();
        miniAppCatalog.setUpdateBy(sysUser.getUsername());
        miniAppCatalog.setCreateBy(sysUser.getUsername());
        miniAppCatalog.setUpdateTime(new Date());
        miniAppCatalog.setCreateTime(new Date());
        appCatalogService.save(miniAppCatalog);
        appCatalogService.deleteCache(miniAppCatalog);
        MiniAppCatalog parentCatalog = appCatalogService.getById(miniAppCatalog.getPid());
        if (Objects.nonNull(parentCatalog)) {
            appCatalogService.deleteCache(parentCatalog);
        }
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miniAppCatalog
     * @return
     */
    @AutoLog(value = "app_catalog-编辑")
    @ApiOperation(value = "app_catalog-编辑", notes = "app_catalog-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniAppCatalog miniAppCatalog) {
        LoginUser sysUser = HttpUtil.getCurrUser();
        miniAppCatalog.setUpdateBy(sysUser.getUsername());
        miniAppCatalog.setUpdateTime(new Date());
        appCatalogService.updateById(miniAppCatalog);
        appCatalogService.deleteCache(miniAppCatalog);
        MiniAppCatalog parentCatalog = appCatalogService.getById(miniAppCatalog.getPid());
        if (Objects.nonNull(parentCatalog)) {
            appCatalogService.deleteCache(parentCatalog);
        }
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "app_catalog-通过id删除")
    @ApiOperation(value = "app_catalog-通过id删除", notes = "app_catalog-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        MiniAppCatalog miniAppCatalog = appCatalogService.getById(id);
        appCatalogService.removeById(id);
        appCatalogService.deleteCache(miniAppCatalog);
        MiniAppCatalog parentCatalog = appCatalogService.getById(miniAppCatalog.getPid());
        if (Objects.nonNull(parentCatalog)) {
            appCatalogService.deleteCache(parentCatalog);
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "app_catalog-批量删除")
    @ApiOperation(value = "app_catalog-批量删除", notes = "app_catalog-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idstr = Arrays.asList(ids.split(","));
        appCatalogService.listByIds(idstr).forEach(appCatalog -> {
            appCatalogService.deleteCache(appCatalog);
            MiniAppCatalog parentCatalog = appCatalogService.getById(appCatalog.getPid());
            if (Objects.nonNull(parentCatalog)) {
                appCatalogService.deleteCache(parentCatalog);
            }
        });
        this.appCatalogService.removeByIds(idstr);
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "app_catalog-通过id查询")
    @ApiOperation(value = "app_catalog-通过id查询", notes = "app_catalog-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppCatalog miniAppCatalog = appCatalogService.getById(id);
        if (miniAppCatalog == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppCatalog);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppCatalog
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniAppCatalog miniAppCatalog) {
        return super.exportXls(request, miniAppCatalog, MiniAppCatalog.class, "app_catalog");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppCatalog.class);
    }

}
