package com.eleven.cms.aivrbt.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunion.config.AiUnionProperties;
import com.eleven.cms.aiunion.pojo.dto.ChannelConfigDTO;
import com.eleven.cms.aiunion.pojo.dto.DisplayConfigDto;
import com.eleven.cms.aiunionkp.service.IMiniPayOrderService;
import com.eleven.cms.aiunionkp.service.WechatOfficialAccountService;
import com.eleven.cms.aivrbt.annotaion.CheckBusinessOpenStatus;
import com.eleven.cms.aivrbt.dto.*;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.entity.AiRingVideo;
import com.eleven.cms.aivrbt.entity.MiniAppFuseConfig;
import com.eleven.cms.aivrbt.enums.MiniAppChannelEnum;
import com.eleven.cms.aivrbt.enums.PaymentMethod;
import com.eleven.cms.aivrbt.service.*;
import com.eleven.cms.aivrbt.service.impl.HaiYiAiService;
import com.eleven.cms.aivrbt.service.impl.TencentAiService;
import com.eleven.cms.aivrbt.utils.DouYinAPIUtil;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryChildVO;
import com.eleven.cms.aivrbt.vo.WxMiniGetUserInfoVO;
import com.eleven.cms.douyinduanju.dto.OpenApiRes;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import com.eleven.cms.dto.GenerateSchemaReq;
import com.eleven.cms.dto.HyFtpUploadDTO;
import com.eleven.cms.remote.CommonSecurityService;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.WxMiniApiService;
import com.eleven.cms.service.IAppDictService;
import com.eleven.cms.service.ISmsValidateService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.VrbtProduct;
import com.eleven.cms.wallpaper.entity.MiniAppWechatUser;
import com.eleven.cms.wallpaper.service.IMiniAppWechatUserService;
import com.eleven.qycl.entity.QyclQa;
import com.eleven.qycl.service.AliMediaService;
import com.eleven.qycl.service.IQyclQaService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oss.OssBootUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.eleven.cms.aivrbt.service.impl.HaiYiAiService.AI_TAG;
import static com.eleven.cms.aivrbt.service.impl.HaiYiAiService.AI_TAG_KEY;

/**
 * @author: cai lei
 * @create: 2024-09-30 10:30
 */
@Api(tags = "aivrbt_api")
@RestController
@RequestMapping("/aivrbt/api")
@Slf4j
@RequiredArgsConstructor
@Validated
public class AiVrbtApiController {

    private final HaiYiAiService haiYiAiService;
    private final IAiRingColumnService ringColumnService;
    private final IAiRingService ringService;
    private final IAiRingBannerService bannerService;
    private final RedisUtil redisUtil;
    private final TencentAiService tencentAiService;
    private final IAiRingColumnAiTemplateService aiRingColumnAiTemplateService;
    private final WxMiniApiService wxMiniApiService;
    private final CommonSecurityService commonSecurityService;
    private final ISmsValidateService smsValidateService;
    private final MiguApiService miguApiService;
    private final IQyclQaService qyclQaService;
    private final AiUnionProperties aiUnionProperties;
    private final WxMiniAppUserRelationshipService wxMiniAppUserRelationshipService;
    private final IMiniPayOrderService miniPayOrderService;
    private final IMiniAppWechatUserService miniAppWechatUserService;
    private final WechatOfficialAccountService wechatOfficialAccountService;

    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    public static String MINI_CREATE_ORDER_LOCK = "mini:comm:order:lock";

    @ApiOperation("查询栏目")
    @GetMapping("/query/column")
    public Result<Object> queryColumn(String type, @RequestParam(required = false, defaultValue = "00211GY") String channelId,
                                      @RequestParam(required = false) String resource) {
        return Result.ok(ringColumnService.queryColumn(type, channelId, resource));
    }

    @ApiOperation("首页-AI彩铃工坊-查询栏目-根据栏目ID查询铃音视频")
    @GetMapping("/page/video/byClColumnId")
    public Result<Object> pageVideoByClColumnId(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                @RequestParam String columnId,
                                                @RequestParam(required = false,defaultValue = "00211GY") String channelId
                                                ) {
        Page<AiRingVideo> page = new Page<>(pageNo, pageSize);
        return Result.ok(ringColumnService.pageVideoByClColumnId(page, columnId,channelId));
    }

    @ApiOperation("AI视频彩铃创作专区-查询栏目及模板")
    @GetMapping("/query/AIColumnAndTemplate")
    public Result<Object> queryAIColumnAndTemplate(@RequestParam(required = false)  String channelId) {
        return Result.ok(ringColumnService.queryAIColumnAndTemplate(channelId));
    }

    @ApiOperation("AI视频彩铃创作专区-查询栏目及模板")
    @GetMapping("/query/AllAIColumnAndTemplate")
    public Result<Object> queryAllAIColumnAndTemplate(@RequestParam(required = false)  String channelId) {
        return Result.ok(ringColumnService.queryAllAIColumnAndTemplate(channelId));
    }

    @ApiOperation("AI模板-查询模板-通过栏目id查询模板")
    @GetMapping("/query/AIColumnAndTemplate/byColumnId")
    public Result<Object> pageAIColumnTemplateByColumnId(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam("columnId") String columnId) {
        Page<AIColumnAndTemplateQueryChildVO> page = new Page<>(pageNo, pageSize);
        return Result.ok(aiRingColumnAiTemplateService.pageAIColumnTemplateByColumnId(page, columnId));
    }

    @ApiOperation("banner-查询所有生效的banner")
    @GetMapping("/query/banner")
    public Result<Object> queryValidBanner(@RequestParam(required = false, defaultValue = "00211GY") String channelId,
                                           @RequestParam(required = false) String resource) {
        return Result.ok(bannerService.queryValidBanner(channelId, resource));
    }

    @ApiOperation("查询本月还可免费生成次数")
    @GetMapping("/query/usableCount")
    public Result<Object> queryUsableCount(@RequestParam @NotBlank(message = "手机号不能为空") String mobile) {
        return Result.ok(ringService.queryUsableCount(mobile));
    }

    @ApiOperation("拍同款-根据栏目ID查询模板")
    @GetMapping("/query/template/bySameColumnId")
    public Result<Object> queryTemplateBySameColumnId(@RequestParam String columnId) {
        return Result.ok(ringColumnService.queryTemplateBySameColumnId(columnId));
    }

    @ApiOperation("查询我的作品数量")
    @GetMapping("/query/my-creation-count")
    public Result<Object> queryMyCreationCount(@RequestParam String mobile) {
        return Result.ok(ringService.queryMyCreationCount(mobile));
    }

    @ApiOperation("查询我的作品")
    @GetMapping("/page/my-creation")
    public Result<Object> queryMyCreation(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                          @RequestParam String mobile) {
        Page<AiRing> page = new Page<>(pageNo, pageSize);
        return Result.ok(ringService.queryMyCreation(page, mobile));
    }

    @ApiOperation("查询我的作品")
    @PostMapping("/query/my-creation-making")
    public Result<Object> queryMyCreationMaking(@RequestBody StatusQueryVO statusQueryVO) {
        return Result.ok(ringService.queryMyCreationMaking(statusQueryVO.getIdList()));
    }

    @ApiOperation("任务创作-AI滤镜")
    @PostMapping("/create/aiStyleTask")
    @CheckBusinessOpenStatus
    public Result<Object> createAiStyleTask(@RequestBody AIStyleTaskCreateDTO aiStyleTaskCreateDTO) {
        log.info("{}-AI滤镜任务创作,请求参数:{}", AI_TAG, aiStyleTaskCreateDTO);
        String redisKey = "hy:createAiStyleTask:" + aiStyleTaskCreateDTO.getMobile();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }
        Integer i = ringService.queryUsableCount(aiStyleTaskCreateDTO.getMobile());
        if (i <= 0) {
            return Result.error("本月生成次数已用完！");
        }
        Result<Object> result = Result.ok(haiYiAiService.createAiStyleTask(aiStyleTaskCreateDTO));
        redisUtil.del(redisKey);
        return result;
    }

    @ApiOperation("任务创作-拍同款")
    @PostMapping("/create/sameStyleTask")
    @CheckBusinessOpenStatus
    public Result<Object> createSameStyleTask(@RequestBody SameStyleTaskCreateDTO sameStyleTaskCreateDTO) {
        log.info("{}-拍同款任务创作,请求参数:{}", AI_TAG, sameStyleTaskCreateDTO);
        String redisKey = "hy:createSameStyleTask:" + sameStyleTaskCreateDTO.getMobile();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }
        Result<Object> result = Result.ok(haiYiAiService.createSameStyleTask(sameStyleTaskCreateDTO));
        redisUtil.del(redisKey);
        return result;
    }

    @ApiOperation("任务创作-图片换脸")
    @PostMapping("/create/facePicFusionTask")
    @CheckBusinessOpenStatus
    public Result<Object> createFacePicFusionTask(@RequestBody AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        log.info("{}-图片换脸任务创作,请求参数:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO);
        String redisKey = "ts:createFacePicFusionTask:" + aiPicFuseFaceTaskCreateDTO.getMobile();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }
        Integer i = ringService.queryUsableCount(aiPicFuseFaceTaskCreateDTO.getMobile());
        if (i <= 0) {
            return Result.error("本月生成次数已用完！");
        }
        Result<Object> result = Result.ok(tencentAiService.createFacePicFusionTask(aiPicFuseFaceTaskCreateDTO));
        redisUtil.del(redisKey);
        return result;
    }

    @ApiOperation("任务创作-单人图片换脸")
    @PostMapping("/create/faceSinglePicFusionTask")
    @CheckBusinessOpenStatus
    public Result<Object> faceSinglePicFusionTask(@RequestBody AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        log.info("{}-图片换脸任务创作,请求参数:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO);
        String redisKey = "ts:createFacePicFusionTask:" + aiPicFuseFaceTaskCreateDTO.getMobile();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }
        Integer i = ringService.queryUsableCount(aiPicFuseFaceTaskCreateDTO.getMobile());
        if (i <= 0) {
            return Result.error("本月生成次数已用完！");
        }
        Result<Object> result = Result.ok(tencentAiService.createFaceMultiPicFusionTask(aiPicFuseFaceTaskCreateDTO));
        redisUtil.del(redisKey);
        return  result;
    }

    @ApiOperation("任务创作-图片换脸查询人脸框")
    @GetMapping("/query/faceInfo")
    public Result<Object> queryFaceInfo(@RequestParam("id") String id) {
        return Result.ok(tencentAiService.queryFaceInfo(id));
    }

    @ApiOperation("任务创作-视频换脸")
    @PostMapping("/create/faceVideoFusionTask")
    public Result<Object> createFaceVideoFusionTask(@RequestBody AiVideoFuseFaceTaskCreateDTO aiVideoFuseFaceTaskCreateDTO) {
        log.info("{}-视频换脸任务创作,请求参数:{}", AI_TAG, aiVideoFuseFaceTaskCreateDTO);
        String redisKey = "ts:createFaceVideoFusionTask:" + aiVideoFuseFaceTaskCreateDTO.getMobile();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }
        Integer i = ringService.queryUsableCount(aiVideoFuseFaceTaskCreateDTO.getMobile());
        if (i <= 0) {
            return Result.error("本月生成次数已用完！");
        }
        // 已开通，进行创作
        Result<Object> result = Result.ok(tencentAiService.createFaceVideoFusionTask(aiVideoFuseFaceTaskCreateDTO));
        redisUtil.del(redisKey);
        return result;
    }

    @ApiOperation("查询任务创作结果")
    @GetMapping("/query/taskResult")
    public Result<Object> queryTaskResult(@RequestParam String taskId) {
        return Result.ok(haiYiAiService.queryTaskResult(taskId));
    }

    @ApiOperation("查询是否开通AI业务")
    @GetMapping("/query/businessOpenStatus")
    public Result<Object> queryBusinessOpenStatus(@RequestParam String mobile, @RequestParam(name = "subChannel", required = false) String subChannel,
                                                  @RequestParam(name = "channelId", required = false,defaultValue = MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX) String channelId) {
        if (StrUtil.isNotBlank(subChannel)) {
            if (redisUtil.get(String.format("%s:freeCheck:%s", AI_TAG_KEY, mobile)) != null) {
                return Result.ok();
            }
            if (redisUtil.setIfAbsent(String.format("%s:freeCheck:subChannel:%s:%s:%s", AI_TAG_KEY, channelId, subChannel, mobile), "", 1800)) {
                redisUtil.set(String.format("%s:freeCheck:%s", AI_TAG_KEY, mobile), "", 120);
                return Result.ok();
            }
        }
        return haiYiAiService.queryBusinessOpenStatus(mobile,channelId);
    }

    @ApiOperation("删除我的作品")
    @DeleteMapping("/delete/my-creation/{id}")
    public Result<Object> deleteMyCreation(@PathVariable String id) {
        return Result.ok(haiYiAiService.deleteMyCreation(id));
    }

    @ApiOperation("抖音登录获取手机号")
    @GetMapping("/fastLogin")
    public Result<Object> fastLogin(@RequestParam String encryptedData, @RequestParam String iv, @RequestParam String sessionKey) {
        return Result.ok(DouYinAPIUtil.exchangePhone(encryptedData, iv, sessionKey));
    }

    @ApiOperation("抖音登录获取session")
    @GetMapping("/getSession")
    public Result<Object> getSession(@RequestParam String code, @RequestParam String anonymousCode,
                                     @RequestParam(name = "source", required = false, defaultValue = "dyAivrbtMiniApp_1") String source) {
        MiniAppChannelEnum miniAppChannelEnum = MiniAppChannelEnum.getDyByCode(source);
        if(Objects.isNull(miniAppChannelEnum)){
            return Result.error("未匹配对应小程序");
        }
        return Result.ok(DouYinAPIUtil.parseGetSessionResponse(DouYinAPIUtil.getSession(code, anonymousCode, source)));
    }
    /**
     * dyAivrbtMiniApp_1 dyAivrbtMiniApp_2 wxAivrbtMiniApp_1
     * @param source
     * @return
     */
    @ApiOperation("查询是否展示ai主题")
    @GetMapping("/query/showMain")
    public Result<Object> showMain(@RequestParam(name = "source", required = false,defaultValue = "dyAivrbtMiniApp_1")String source,
                                   @RequestParam(name = "channelId", required = false,defaultValue = "00211GY")String channelId
                                   ) {
        MiniAppChannelEnum miniAppChannelEnum = MiniAppChannelEnum.getByCode(source);
        if(Objects.isNull(miniAppChannelEnum)){
            return Result.error("未匹配对应小程序");
        }
        String key= String.format("cms:cache:aiVrbt:showMain:%s:%s", source, channelId);
        return Result.ok(Optional.ofNullable(redisUtil.get(key)).orElseGet(() -> {
            DisplayConfigDto defaultDisplayConfig = DisplayConfigDto.builder()
                    .mainTopic(0)
                    .allowSwitch(false)
                    .build();
            redisUtil.set(key, defaultDisplayConfig);
            return defaultDisplayConfig;
        }));
    }

    @Resource
    private IMiniAppFuseConfigService miniAppFuseConfigService;


    @GetMapping("/getDefaultChannel")
    public Result<Object> getDefaultChannel(String source) {
        log.info("{}-获取默认渠道号,请求参数:{}", AI_TAG, source);
        return Result.ok(miniAppFuseConfigService.getDefaultChannel(source));
    }

    @GetMapping("/getSuitableChannel")
    public Result<Object> getSuitableChannel(String source, @RequestParam(name = "mobile", required = false) String mobile) {
        log.info("{}-获取用户手合适的渠道号:{},mobile:{}", AI_TAG, source, mobile);
        MiniAppFuseConfig config = miniAppFuseConfigService.getConfigBySource(source);
        return Result.ok(config);
    }

    @Resource
    IAppDictService appDictService;

    @ApiOperation("获取渠道号配置")
    @GetMapping("/getChannelConfig")
    public Result<Object> getChannelConfig(String source) {
        String distCode = (String) redisUtil.get("miniApp:airing:channelConfig:" + source);
        DictModel dictModel;
        String channel = "";
        List<DictModel> dictModels = appDictService.queryDictItemsByCode(distCode);
        if (CollectionUtil.isNotEmpty(dictModels)) {
            dictModel = dictModels.get(0);
            channel = dictModel.getValue();
        }
        if (StringUtils.equals(channel, "014X0JZ")) {
            ChannelConfigDTO sub = ChannelConfigDTO.builder()
                    .source(source)
                    .channelCode(channel)
                    .type("sub")
                    .build();
            return Result.ok(sub);

        } else {
            ChannelConfigDTO configDTO = ChannelConfigDTO.builder()
                    .source(source)
                    .channelCode(channel)
                    .type("channel")
                    .build();
            return Result.ok(configDTO);
        }
    }


    @ApiOperation("微信小程序获取手机号")
    @GetMapping("/wx/mini/getPhone")
    public Result<Object> wxMiniLogin(@RequestParam String code,
                                     @RequestParam(name = "source") String source) {
        return wxMiniApiService.wxMiniGetPhone(code,source);
    }

    @ApiOperation("app对应的微信小程序登录,通过encryptedData授权 获取手机号的")
    @PostMapping("/wx/mini/login")
    public Result<Object> wxMiniGetUserInfo(@RequestBody WxMiniUserInfoDTO wxMiniUserInfoDTO) {
        // WxMiniGetUserInfoVO wxMiniGetUserInfoVO = JSONObject.parseObject("{\"openId\":\"oM4ED7mcRkpkI-B7pwz4aLJ_X3ZM\",\"phoneNumber\":\"18161337090\"}", WxMiniGetUserInfoVO.class);
        WxMiniGetUserInfoVO wxMiniGetUserInfoVO = wxMiniApiService.wxMiniGetUserInfo(wxMiniUserInfoDTO);
        if (StringUtils.isNotEmpty(wxMiniGetUserInfoVO.getOpenId())) {
            MiniAppWechatUser miniAppWechatUser = miniAppWechatUserService.lambdaQuery().eq(MiniAppWechatUser::getOpenId, wxMiniGetUserInfoVO.getOpenId()).last("limit 1").one();
            if (Objects.isNull(miniAppWechatUser)) {
                miniAppWechatUser = new MiniAppWechatUser();
                miniAppWechatUser.setOpenId(wxMiniGetUserInfoVO.getOpenId());
                miniAppWechatUser.setBusinessType(wxMiniUserInfoDTO.getChannelId());
                miniAppWechatUser.setChannelId(wxMiniUserInfoDTO.getChannelId());
                miniAppWechatUser.setMobile(wxMiniGetUserInfoVO.getPhoneNumber());
                miniAppWechatUserService.save(miniAppWechatUser);
            }
            wxMiniGetUserInfoVO = wxMiniAppUserRelationshipService.getUserInfoOrSaveRelationship(wxMiniGetUserInfoVO, wxMiniUserInfoDTO);
        }

        return Result.ok(wxMiniGetUserInfoVO);
    }
    /**
     * 海艺视频文件上传ftp
     *
     * @param hyFtpUploadDTO hyFtpUploadDTO
     * @return FebsResponse
     */
    @PostMapping("/hy/ftp/upload")
    public Result<Object> hyFtpUpload(@RequestBody @Validated HyFtpUploadDTO hyFtpUploadDTO) {
        haiYiAiService.setRing(hyFtpUploadDTO);
        return Result.ok();
    }

    @ApiOperation("图片安审")
    @GetMapping("/check/picSecurity")
    public Result<Object> picSecurity(@RequestParam("picUrl") String picUrl
            , @RequestParam(value = "securityDetection", defaultValue = "true") Boolean securityDetection,
                                      @RequestParam(value = "faceDetection", required = false, defaultValue = "false") Boolean faceDetection) {
        return Result.ok(commonSecurityService.picSecurity(securityDetection, faceDetection, picUrl));
    }

    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     * @return
     */
    @PostMapping("/getCaptcha")
    @ResponseBody
    public Result<?> getCaptcha(@RequestParam("mobile") String mobile, @RequestParam(name = "channelCode", required = false, defaultValue = MiguApiService.CH_DYB_DEFAULT) String channelCode) {
        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        boolean result = smsValidateService.create(mobile, channelCode);
        if (!result) {
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }

        return Result.ok("验证码已发送至你的手机,5分钟内有效");
    }

    @ApiOperation(value = "咪咕登录", notes = "咪咕登录")
    @PostMapping(value = "/miguLogin")
    public Result<?> miguLogin(String mobile, String captcha, String channelCode, HttpServletRequest request) {

        if (Strings.isNullOrEmpty(mobile) || Strings.isNullOrEmpty(captcha)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!captcha.matches("^\\d{6}$")) {
            return Result.noauth("验证码错误");
        }
        //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
        try {
            List<String> freeCheckMobile = aiUnionProperties.getFreeCheckMobile();
            if (ObjectUtil.isNotEmpty(freeCheckMobile) && freeCheckMobile.contains(mobile)) {
                log.info("免校验登录名单：{}",freeCheckMobile.toString());
            } else {
                smsValidateService.check(mobile, captcha);
            }
        } catch (JeecgBootException e) {
            //e.printStackTrace();
            return Result.noauth(e.getMessage());
        }

        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if (result != null && result.isOK()) {
            Result resp = Result.ok("登录成功");
            resp.setResult(result.getToken());
            return resp;
        } else {
            return Result.noauth("登录失败");
        }
    }

    @ApiOperation(value = "咪咕直接登录", notes = "咪咕直接登录")
    @PostMapping(value = "/directLogin")
    public Result<?> directLogin(String mobile, String channelCode, HttpServletRequest request) {

        if (Strings.isNullOrEmpty(mobile)) {
            return Result.noauth("参数错误");
        }
        if (!mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        final String ipAddr = IPUtils.getIpAddr(request);

        log.info("咪咕直接登录=>手机号:{},渠道号:{},ip:{}", mobile, channelCode, ipAddr);

        RemoteResult result = miguApiService.miguLogin(mobile, channelCode);
        if (result.isOK()) {
            Result resp = Result.ok("登录成功");
            resp.setResult(result.getToken());
            return resp;
        } else {
            return Result.noauth("登录失败");
        }
    }

    /**
     * 阿里云oss签名
     *
     * @param region oss区域
     * @param bucket oss桶
     * @param dir    设置上传到OSS文件的前缀，可置空此项。置空后，文件将上传至Bucket的根目录下
     * @return
     */
    @PostMapping("/aliOssSign")
    @ResponseBody
    public Result<?> aliOssSign(@RequestParam(name = "region", required = false, defaultValue = "cn-beijing") String region,
                                @RequestParam(name = "bucket", required = false, defaultValue = "ims-media-kunpeng") String bucket,
                                @RequestParam(name = "dir", required = false, defaultValue = "user-media") String dir) {
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        String accessId = OssBootUtil.getAccessKeyId();
        String accessKey = OssBootUtil.getAccessKeySecret();
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "oss-" + region + ".aliyuncs.com";
        // 填写Host地址，格式为https://bucketname.endpoint。
        String host = "https://" + bucket + "." + endpoint;
        // 设置上传回调URL，即回调服务器地址，用于处理应用服务器与OSS之间的通信。OSS会在文件上传完成后，把文件上传信息通过此回调URL发送给应用服务器。
        //String callbackUrl = "https://***********:8888";

        // 创建ossClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessId, accessKey);
        try {
            //有效时间30分钟
            Date expiration = Date.from(LocalDateTime.now().plusMinutes(30L).atZone(ZoneId.systemDefault()).toInstant());
            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

            String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            final ObjectNode objectNode = mapper.createObjectNode()
                    .put("accessId", accessId)
                    .put("policy", encodedPolicy)
                    .put("signature", postSignature)
                    .put("dir", dir)
                    .put("host", host);
            //.put("expire", String.valueOf(expireEndTime / 1000));

            //JSONObject jasonCallback = new JSONObject();
            //jasonCallback.put("callbackUrl", callbackUrl);
            //jasonCallback.put("callbackBody", "filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}");
            //jasonCallback.put("callbackBodyType", "application/x-www-form-urlencoded");
            //String base64CallbackBody = BinaryUtil.toBase64String(jasonCallback.toString().getBytes());
            //respMap.put("callback", base64CallbackBody);

            //JSONObject ja1 = JSONObject.fromObject(respMap);
            //// System.out.println(ja1.toString());
            //response.setHeader("Access-Control-Allow-Origin", "*");
            //response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT");
            //response(request, response, ja1.toString());

            return Result.ok(objectNode);
        } catch (Exception e) {
            log.info("阿里云oss签名异常!", e);
            return Result.error("阿里云oss签名异常");
        }
    }

    /**
     * 列表查询
     *
     * @return
     */
    //@AutoLog(value = "qycl_qa-列表查询")
    @ApiOperation(value = "qycl_qa-列表查询", notes = "qycl_qa-列表查询")
    @GetMapping(value = "/qycl/qa/listAll")
    public Result<?> queryQaList(@RequestParam(defaultValue = "QYCL") String type) {
        List<QyclQa> pageList = qyclQaService.listByType(type);
        return Result.ok(pageList);
    }

    /**
     * 小程序创建订单
     *
     * @param orderReq
     * @param request
     * @return
     */
    @PostMapping("/mini/createOrder")
    public Result<Object> creatOrder(@RequestBody @Validated MiniOrderReq orderReq, HttpServletRequest request) {
        log.info("创建订单请求参数：{}", JSONObject.toJSON(orderReq));
        MiniAppChannelEnum miniAppChannelEnum = MiniAppChannelEnum.getByCode(orderReq.getChannelId());
        if (Objects.isNull(miniAppChannelEnum)) {
            return Result.error("当前渠道不支持支付");
        }
        String lockKey = MINI_CREATE_ORDER_LOCK + orderReq.getMobile();
        if (!redisUtil.setIfAbsent(lockKey, orderReq.getMobile(), 5)) {
            return Result.error("订单支付中，请稍后");
        }

        if (Objects.equals(orderReq.getPayType(), PaymentMethod.WECHAT_PAY.getCode())) {
            return miniPayOrderService.createOrder(orderReq);
        } else {
            return Result.error("仅支持微信支付");
        }
    }


    /**
     * 小程序创建订单退款
     *
     * @param
     * @return
     */
    @PostMapping("/mini/createRefundOrder")
    public Result<Object> createRefundOrder(@RequestBody @Validated MiniRefundOrderReq refundOrderReq) {
        log.info("创建退款订单请求参数：{}", JSONObject.toJSON(refundOrderReq));
        if (Objects.equals(refundOrderReq.getPayType(), PaymentMethod.WECHAT_PAY.getCode())) {
            return miniPayOrderService.createRefundOrder(refundOrderReq);
        } else {
            return Result.error("仅支持微信支付");
        }
    }

    /**
     * 获取公众号openId 接口
     *
     * @return
     */
    @GetMapping("/wx/officialAccount/openId")
    public Result<Object> openId(@RequestParam(name = "code") String code, @RequestParam(value = "appChannelId", required = false, defaultValue = "") String channelId
            , @RequestParam("sourceChannel") String sourceChannel) {
        return wechatOfficialAccountService.getOpenIdByCode(code, sourceChannel);
    }

    /**
     * 微信登录接口 该接口作用与 app 对应的一个微信小程序或者公众号的统一登录接口
     * 1.注册微信用户
     * 2.注册登录app用户(验证码登录的)
     */
    @PostMapping("/wx/mini/app/login")
    public Result<Object> wxMiniSimpleLogin(@Validated @RequestBody AppLoginDTO appLoginDTO) {
        return wxMiniAppUserRelationshipService.loginAndApp(appLoginDTO);
    }


    @Resource
    private IAiRingVideoService aiRingVideoService;

    @Resource
    private AliMediaService aliMediaService;

    @GetMapping("/test/addAiRingVideo")
    public Result addAiRingVideo(String columnId, String productIds) {
        List<String> data = Arrays.asList(productIds.split(","));
        List<AiRingVideo> result = new ArrayList<>();
        data.forEach(e -> {
            VrbtProduct r = miguApiService.fetchVrbtProduct(e);

            AiRingVideo aiRingVideo = new AiRingVideo();
            aiRingVideo.setCopyrightId(r.getCopyrightId());
            aiRingVideo.setVrbtProductId(r.getVrbtProductId());
            aiRingVideo.setRingName(r.getMusicName().replace("AI一语成片-", ""));
            aiRingVideo.setRingPicUrl(r.getVrbtImg());
            aiRingVideo.setRingUrl(r.getVrbtVideo());
            aiRingVideo.setColumnId(columnId);
            result.add(aiRingVideo);
        });
        aiRingVideoService.saveBatch(result);
        new Thread(() -> {
            for (AiRingVideo aiRingVideo : result) {
                String filePath = "aiRingVideo" + "/" + IdWorker.get32UUID() + ".mp4";
                String coverPath = "aiRingVideo" + "/" + IdWorker.get32UUID() + ".jpg";
                try {
                    String videoUrl = aliMediaService.putObjectRemoteUrlCdn(filePath, aiRingVideo.getRingUrl());
                    String coverUrl = aliMediaService.putObjectRemoteUrlCdn(coverPath, aiRingVideo.getRingPicUrl());
                    aiRingVideo.setAliRingUrl(videoUrl);
                    aiRingVideo.setAliCoverUrl(coverUrl);
                    aiRingVideoService.updateById(aiRingVideo);
                } catch (Exception e) {
                    log.error("铃声上传失败:{}", aiRingVideo.getId(), e);
                }
            }
        }).start();
        return Result.ok();
    }

    @Resource
    DouYinHttpUtil douYinHttpUtil;

    @PostMapping(value = "/generateSchema")
    public Result<Object> generateSchema(@RequestBody @Validated GenerateSchemaReq req) {

        OpenApiRes openApiRes = douYinHttpUtil.generateSchema(req.getResource(), req.getPath(), req.getQuery(), req.getExpireTime(), req.getNoExpire());
        if (!openApiRes.isSuccess()) {
            log.info("生成失败:{}", openApiRes.getErrMsg());
            return Result.error("生成失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(openApiRes.getData());
        String schema = jsonObject.getString("schema");
        return Result.ok(schema);
    }

    @GetMapping(value = "/test/updateAliUrl")
    public Result updateAliUrl(@RequestParam String columnId) {
        List<AiRingVideo> list = aiRingVideoService.lambdaQuery().eq(AiRingVideo::getColumnId, columnId).list();
        if (CollectionUtil.isEmpty(list)) {
            return Result.ok();
        }
        List<AiRingVideo> updateList = new ArrayList<>();
        for (AiRingVideo aiRingVideo : list) {
            String filePath = "aiRingVideo" + "/" + IdWorker.get32UUID() + ".mp4";
            if (StringUtils.isNotEmpty(aiRingVideo.getAliRingUrl())) {
                continue;
            }
            AiRingVideo update = new AiRingVideo();
            update.setId(aiRingVideo.getId());
            try {
                aliMediaService.putObjectRemoteUrl(filePath, aiRingVideo.getRingUrl());
                String url = OssBootUtil.getStaticDomain() + "/" + filePath;
                update.setAliRingUrl(url);
                updateList.add(update);
            } catch (Exception e) {
                log.error("铃声上传失败:{}", aiRingVideo.getId(), e);
            }
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            aiRingVideoService.updateBatchById(updateList);
        }
        return Result.ok();
    }

    @GetMapping(value = "/test/updateAliCoverUrl")
    public Result<Object> updateAliUrl() {
        List<AiRingVideo> list = aiRingVideoService.lambdaQuery().isNull(AiRingVideo::getAliCoverUrl).list();
        if (CollectionUtil.isEmpty(list)) {
            return Result.ok();
        }
        for (AiRingVideo aiRingVideo : list) {
            String coverPath = "aiRingVideo" + "/" + IdWorker.get32UUID() + ".jpg";
            if (StringUtils.isNotEmpty(aiRingVideo.getAliCoverUrl())) {
                continue;
            }
            AiRingVideo update = new AiRingVideo();
            update.setId(aiRingVideo.getId());
            try {
                String coverUrl = aliMediaService.putObjectRemoteUrlCdn(coverPath, aiRingVideo.getRingPicUrl());
                update.setAliCoverUrl(coverUrl);
                aiRingVideoService.updateById(update);
            } catch (Exception e) {
                log.error("铃声上传失败:{}", aiRingVideo.getId(), e);
            }
        }
        return Result.ok();
    }
}
