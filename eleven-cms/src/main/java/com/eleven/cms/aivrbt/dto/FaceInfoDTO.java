package com.eleven.cms.aivrbt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2024/11/5 11:10
 */
@Data
public class FaceInfoDTO {
    /**
     * 人脸框id
     */
    @JsonProperty("FaceId")
    private String faceId;
    /**
     * 人脸框
     */
    @JsonProperty("FaceInfo")
    private FaceRect faceInfo;

    /**
     * 面部框位置
     */
    @Data
    public static class FaceRect {
        @JsonProperty("Width")
        private Integer width;
        @JsonProperty("Height")
        private Integer height;
        @JsonProperty("X")
        private Integer x;
        @JsonProperty("Y")
        private Integer y;
    }
}
