package com.eleven.cms.aivrbt.utils;

import com.eleven.cms.aivrbt.dto.FaceInfoDTO;
import com.eleven.cms.aivrbt.dto.MaterialListDTO;
import com.eleven.cms.aivrbt.dto.PicFuseFaceDTO;
import com.eleven.cms.aivrbt.dto.VideoFaceFusionDTO;
import com.eleven.cms.util.JacksonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.jeecg.common.exception.JeecgBootException;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;

/**
 * 腾讯云AI
 *
 * <AUTHOR>
 * @datetime 2024/10/28 14:05
 */
@Slf4j
public class TencentCloudAIUtils {

    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    public static final String VIDEO_FACEFUSION_HOST = "facefusion.tencentcloudapi.com";
    public static final String CONTENT_TYPE = "application/json; charset=utf-8";
    public static final String REGION = "ap-chengdu";
    public static final String LOG_TAG = "AI彩铃创意站";
    public static final String MATERIAL_LIST_VERSION = "2022-09-27";
    public static final String MATERIAL_LIST_ACTION = "DescribeMaterialList";

    public static final String FUSE_FACE_VERSION = "2022-09-27";
    public static final String FUSE_FACE_ACTION = "FuseFace";

    public static final String VIDEO_FACE_FUSION_VERSION = "2018-12-01";
    public static final String VIDEO_FACE_FUSION_ACTION = "SubmitVideoFaceFusionJob";
    public static final String QUERY_VIDEO_FACE_FUSION_JOB_ACTION = "QueryVideoFaceFusionJob";

    /**
     * 分页查询-素材列表
     *
     * @param materialListDTO materialListDTO
     * @return String
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     * @throws InvalidKeyException      InvalidKeyException
     */
    public static String listMaterial(MaterialListDTO materialListDTO) {
        HttpUrl url = HttpUrl.parse("https://" + VIDEO_FACEFUSION_HOST);

        String json = JacksonUtils.toJson(materialListDTO);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        Headers headers = new Headers.Builder()
                .add("Host", VIDEO_FACEFUSION_HOST)
                .add("X-TC-Timestamp", timestamp)
                .add("X-TC-Version", MATERIAL_LIST_VERSION)
                .add("X-TC-Action", MATERIAL_LIST_ACTION)
                .add("X-TC-Region", REGION)
                .add("X-TC-Token", "")
                .add("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
                .add("Authorization", TencentCloudAIParamHandleUtils.getAuth(VIDEO_FACEFUSION_HOST, CONTENT_TYPE, timestamp, json))
                .build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "腾讯云AI_分页查询_素材列表");
    }

    public static List<FaceInfoDTO> parseListMaterialResponse(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);

        JsonNode materialInfosNode = rootNode.path("Response").path("MaterialInfos");

        // 检查 MaterialInfos 节点是否存在且不是空数组
        if (materialInfosNode.isMissingNode() || !materialInfosNode.isArray() || materialInfosNode.isEmpty()) {
            return new ArrayList<>();
        }

        List<FaceInfoDTO> faceInfoDTOs = new ArrayList<>();
        materialInfosNode.forEach(materialInfoNode -> {
            JsonNode materialFaceListNode = materialInfoNode.path("MaterialFaceList");

            // 检查 MaterialFaceList 节点是否存在且不是空数组
            if (!materialFaceListNode.isMissingNode() && materialFaceListNode.isArray() && !materialFaceListNode.isEmpty()) {
                materialFaceListNode.forEach(materialFaceNode -> {
                    try {
                        FaceInfoDTO faceInfoDTO = JacksonUtils.treeToValue(materialFaceNode, FaceInfoDTO.class);
                        faceInfoDTOs.add(faceInfoDTO);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        });

        return faceInfoDTOs;
    }

    /**
     * 图片换脸 单人/多人/选脸
     *
     * @param picFuseFaceDTO fuseFaceDTO
     * @return String
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     * @throws InvalidKeyException      InvalidKeyException
     */
    public static String fuseFace(PicFuseFaceDTO picFuseFaceDTO, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://" + VIDEO_FACEFUSION_HOST);

        String json = JacksonUtils.toJson(picFuseFaceDTO);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        Headers headers = new Headers.Builder()
                .add("Host", VIDEO_FACEFUSION_HOST)
                .add("X-TC-Timestamp", timestamp)
                .add("X-TC-Version", FUSE_FACE_VERSION)
                .add("X-TC-Action", FUSE_FACE_ACTION)
                .add("X-TC-Region", REGION)
                .add("X-TC-Token", "")
                .add("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
                .add("Authorization", TencentCloudAIParamHandleUtils.getAuth(VIDEO_FACEFUSION_HOST, CONTENT_TYPE, timestamp, json))
                .build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "腾讯云AI_图片换脸", extraInfoMap);
    }

    public static String parseFuseFaceResponse(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        JsonNode responseNode = rootNode.path("Response");

        if (responseNode!=null && responseNode.has("Error")) {
            throw new JeecgBootException(responseNode.get("Error").get("Message").asText());
        }


        if (responseNode.has("FusedImage")) {
            JsonNode fusedImageNode = responseNode.path("FusedImage");
            return fusedImageNode.asText();
        }
        throw new JeecgBootException("生成错误，请重试！");
    }

    /**
     * 视频换脸
     *
     * @param videoFaceFusionDTO videoFuseFaceDTO
     * @param extraInfoMap       extraInfoMap
     * @return String
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     * @throws InvalidKeyException      InvalidKeyException
     */
    public static String videoFaceFusion(VideoFaceFusionDTO videoFaceFusionDTO, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://" + VIDEO_FACEFUSION_HOST);

        String json = JacksonUtils.toJson(videoFaceFusionDTO);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        Headers headers = new Headers.Builder()
                .add("Host", VIDEO_FACEFUSION_HOST)
                .add("X-TC-Timestamp", timestamp)
                .add("X-TC-Version", VIDEO_FACE_FUSION_VERSION)
                .add("X-TC-Action", VIDEO_FACE_FUSION_ACTION)
                .add("X-TC-Region", REGION)
                .add("X-TC-Token", "")
                .add("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
                .add("Authorization", TencentCloudAIParamHandleUtils.getAuth(VIDEO_FACEFUSION_HOST, CONTENT_TYPE, timestamp, json))
                .build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "腾讯云AI_视频换脸", extraInfoMap);
    }


    public static String parseVideoFaceResponse(String response) {
        // 判空处理
        if (response == null || response.isEmpty()) {
            return null;
        }
        JsonNode responseNode = null;
        try {
            // 解析 JSON 字符串
            JsonNode rootNode = JacksonUtils.readTree(response);
            responseNode = rootNode.path("Response");

            // 获取 JobId
            if (responseNode.has("JobId")) {
                return responseNode.get("JobId").asText();
            }
        } catch (Exception e) {
            // 处理解析异常
            e.printStackTrace();
        }
        if (!(null == responseNode) && responseNode.has("Error")) {
            throw new JeecgBootException(responseNode.get("Error").get("Message").asText());
        }

        // 如果 JobId 不存在或解析失败
        throw new JeecgBootException("生成错误，请重试！");
    }

    /**
     * 查询视频换脸结果
     *
     * @param jobId        jobId
     * @param extraInfoMap extraInfoMap
     * @return String
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     * @throws InvalidKeyException      InvalidKeyException
     */
    public static String queryVideoFaceFusionJobId(String jobId, Map<String, Object> extraInfoMap) {
        HttpUrl url = HttpUrl.parse("https://" + VIDEO_FACEFUSION_HOST);

        Map<String, String> map = new HashMap<>();
        map.put("JobId", jobId);
        String json = JacksonUtils.toJson(map);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        Headers headers = new Headers.Builder()
                .add("Host", VIDEO_FACEFUSION_HOST)
                .add("X-TC-Timestamp", timestamp)
                .add("X-TC-Version", VIDEO_FACE_FUSION_VERSION)
                .add("X-TC-Action", QUERY_VIDEO_FACE_FUSION_JOB_ACTION)
                .add("X-TC-Region", REGION)
                .add("X-TC-Token", "")
                .add("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
                .add("Authorization", TencentCloudAIParamHandleUtils.getAuth(VIDEO_FACEFUSION_HOST, CONTENT_TYPE, timestamp, json))
                .build();
        String result = HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "腾讯云AI_视频换脸", extraInfoMap);
        log.info("查询任务结果:{}", result);
        return result;
    }

    public static Map<String, String> parseVideoFuseFaceJobResponse(String response) {
        String videoUrl = null;
        String jobStatusCode = null;
        try {

            JsonNode rootNode = JacksonUtils.readTree(response);

            // 获取 Response 节点
            JsonNode responseNode = rootNode.path("Response");

            // 获取 VideoUrl 和 JobStatusCode
            JsonNode videoUrlNode = responseNode.path("VideoFaceFusionOutput").path("VideoUrl");
            JsonNode jobStatusCodeNode = responseNode.path("JobStatusCode");

            // 判断是否为空
            videoUrl = videoUrlNode.isTextual() ? videoUrlNode.asText() : null;
            jobStatusCode = jobStatusCodeNode.isInt() ? jobStatusCodeNode.asText() : null;


        } catch (Exception e) {
            e.printStackTrace();
        }
        String finalVideoUrl = videoUrl;
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("videoUrl", finalVideoUrl);
        hashMap.put("jobStatusCode", jobStatusCode);
        return hashMap;
    }
}
