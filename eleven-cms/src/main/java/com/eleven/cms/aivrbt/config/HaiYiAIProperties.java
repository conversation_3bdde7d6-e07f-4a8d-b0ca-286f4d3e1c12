package com.eleven.cms.aivrbt.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @datetime 2024/10/9 10:54
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "ai.haiyi")
@Slf4j
public class HaiYiAIProperties {

    private String redisKey;
    private String clientId;
    private String secret;
    private String tokenUrl;
    private String templateTagUrl;
    private String templateListUrl;
    private String aiFilterUrl;
    private String preSignUrl;
    private String preSignConfirmUrl;
    private String imgToTextUrl;
}
