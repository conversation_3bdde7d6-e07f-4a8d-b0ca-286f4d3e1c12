package com.eleven.cms.aivrbt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.aivrbt.entity.AiRingBanner;
import com.eleven.cms.aivrbt.vo.AiRingValidBannerVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: ai_ring_banner
 * @Author: jeecg-boot
 * @Date: 2024-11-11
 * @Version: V1.0
 */
public interface AiRingBannerMapper extends BaseMapper<AiRingBanner> {

    List<AiRingValidBannerVO> queryValidBanner(@Param("channelId") String channelId, @Param("resource") String resource);
}
