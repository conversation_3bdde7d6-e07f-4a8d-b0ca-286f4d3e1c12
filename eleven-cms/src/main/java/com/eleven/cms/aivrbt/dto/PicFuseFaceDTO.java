package com.eleven.cms.aivrbt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/10/28 14:40
 */
@Data
public class PicFuseFaceDTO {

    @JsonProperty("ProjectId")
    private String ProjectId;

    @JsonProperty("ModelId")
    private String ModelId;

    @JsonProperty("RspImgType")
    private String RspImgType = "url";

    @JsonProperty("LogoAdd")
    private Integer LogoAdd = 0;

    @JsonProperty("MergeInfos")
    private List<PicFuseFaceMergeInfoDTO> MergeInfos;
}
