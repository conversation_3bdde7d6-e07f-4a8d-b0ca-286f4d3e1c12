package com.eleven.cms.aivrbt.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aivrbt.annotaion.DistributedLock;
import com.eleven.cms.aivrbt.dto.*;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiFaceTemplate;
import com.eleven.cms.aivrbt.entity.AiRingFaceImage;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiFaceTemplateService;
import com.eleven.cms.aivrbt.service.IAiRingFaceImageService;
import com.eleven.cms.aivrbt.service.IAiRingService;
import com.eleven.cms.aivrbt.utils.TencentCloudAIUtils;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.aivrbt.service.impl.HaiYiAiService.AI_TAG;
import static com.eleven.cms.aivrbt.service.impl.HaiYiAiService.AI_TAG_KEY;

/**
 * <AUTHOR>
 * @datetime 2024/11/4 16:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TencentAiService {

    private final RedisUtil redisUtil;

    private final IAiRingService aiRingService;

    private final AliMediaService aliMediaService;

    private final IAiRingColumnAiFaceTemplateService aiFaceTemplateService;

    private final IAiRingFaceImageService AiRingFaceImageService;

    private final ThreadPoolTaskExecutor aiRingThreadPoolTaskExecutor;

    @Lazy
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 任务创作-图片换脸
     *
     * @param aiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO
     * @return String 视频ID
     */
    public Object createFacePicFusionTask(AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {

        AiRingColumnAiFaceTemplate aiFaceTemplate = aiFaceTemplateService.getById(aiPicFuseFaceTaskCreateDTO.getId());
        if (aiFaceTemplate == null) {
            throw new JeecgBootException("未找到素材模板信息");
        }
        // 1.生成铃音任务
        AiRing aiRing = new AiRing();
        aiRing.setRingName(IdWorker.get32UUID());
        aiRing.setMobile(aiPicFuseFaceTaskCreateDTO.getMobile());
        aiRing.setTemplateId(aiPicFuseFaceTaskCreateDTO.getTemplateId());
        aiRing.setRingType(0);
        aiRing.setRingPicUrl(aiPicFuseFaceTaskCreateDTO.getRingPicUrl());
        aiRingService.save(aiRing);

        aiPicFuseFaceTaskCreateDTO.setAiRingId(aiRing.getId());
        aiPicFuseFaceTaskCreateDTO.setClipsParam(aiFaceTemplate.getClipsParam());
        try {
            rabbitTemplate.convertAndSend(RabbitMQConfig.TX_FACE_SEND_SMS_CODE_QUEUE_NAME, aiPicFuseFaceTaskCreateDTO, new CorrelationData(aiPicFuseFaceTaskCreateDTO.getAiRingId()));
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, aiRing.getId()));
            log.error("{}-腾讯云ai图片换脸MQ发送消息异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getMobile(), e);
        }

        return aiRing.getId();
    }
    public Object createFaceMultiPicFusionTask(AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {

        AiRingColumnAiFaceTemplate aiFaceTemplate = aiFaceTemplateService.getById(aiPicFuseFaceTaskCreateDTO.getId());
        if (aiFaceTemplate == null) {
            throw new JeecgBootException("未找到素材模板信息");
        }
        // 1.生成铃音任务
        AiRing aiRing = new AiRing();
        aiRing.setRingName(IdWorker.get32UUID());
        aiRing.setMobile(aiPicFuseFaceTaskCreateDTO.getMobile());
        aiRing.setTemplateId(aiPicFuseFaceTaskCreateDTO.getTemplateId());
        aiRing.setRingType(0);
        aiRing.setRingPicUrl(aiPicFuseFaceTaskCreateDTO.getRingPicUrl());
        aiRingService.save(aiRing);

        aiPicFuseFaceTaskCreateDTO.setAiRingId(aiRing.getId());
        aiPicFuseFaceTaskCreateDTO.setClipsParam(aiFaceTemplate.getClipsParam());
        try {
            handleMultipleCreateTaskMsg(aiPicFuseFaceTaskCreateDTO);
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, aiRing.getId()));
            log.error("{}-腾讯云ai图片换脸MQ发送消息异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getMobile(), e);
        }

        return aiRing.getId();
    }

    /**
     * 腾讯云图片换脸异步任务处理逻辑
     *
     * @param aiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO
     */
    public void handleMQMsg(AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        try {
            //1.生成人脸转化图
            Map<String, Object> extraInfoMap = new HashMap<>();
            extraInfoMap.put("手机号", aiPicFuseFaceTaskCreateDTO.getMobile());

            String fuseFaceUrl = TencentCloudAIUtils.parseFuseFaceResponse(TencentCloudAIUtils.fuseFace(buildPicFuseFaceTaskCreateDTO(aiPicFuseFaceTaskCreateDTO), extraInfoMap));
            //存储阿里云合成人脸图片，
            String filePath = "tx" + "/" + IdWorker.get32UUID() + ".jpg";

            String aliUrl = aliMediaService.putObjectRemoteUrl(filePath, fuseFaceUrl);


            AiRingFaceImage faceImage = new AiRingFaceImage();
            faceImage.setActivityId(aiPicFuseFaceTaskCreateDTO.getActivityId());
            faceImage.setMaterialId(aiPicFuseFaceTaskCreateDTO.getMaterialId());
            faceImage.setAiRingId(aiPicFuseFaceTaskCreateDTO.getId());
            faceImage.setConvertImageAliUrl(aliUrl);
            faceImage.setMobile(aiPicFuseFaceTaskCreateDTO.getMobile());
            List<String> sourceFaceUrl = aiPicFuseFaceTaskCreateDTO.getMergeInfos().stream()
                    .map(AiPicFuseFaceTaskCreateDTO.PicFuseFaceParam::getUrl)
                    .collect(Collectors.toList());
            faceImage.setSourceUserAliUrls(JacksonUtils.toJson(sourceFaceUrl));
            AiRingFaceImageService.save(faceImage);
            // 7.获取图片数据构造clipsParam
            LinkedHashMap<String, String> clipsParamMap = JacksonUtils.readValue(aiPicFuseFaceTaskCreateDTO.getClipsParam(), LinkedHashMap.class);
            for (Map.Entry<String, String> entry : clipsParamMap.entrySet()) {
                entry.setValue(aliUrl);
            }
            // 2.合成视频
            String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_TXAI_FACE, aiPicFuseFaceTaskCreateDTO.getTemplateId(), JacksonUtils.toJson(clipsParamMap));
            // 3.通过铃音ID更新JobId
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getJobId, jobId)
                    .set(AiRing::getJobType, 0)
                    .eq(AiRing::getId, aiPicFuseFaceTaskCreateDTO.getAiRingId()));
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, aiPicFuseFaceTaskCreateDTO.getAiRingId()));
            log.error("{}-腾讯云ai图片换脸消息处理异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getMobile(), e);
        }
    }


    public void handleMultipleCreateTaskMsg(AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        try {
            //1.生成人脸转化图
            Map<String, Object> extraInfoMap = new HashMap<>();
            extraInfoMap.put("手机号", aiPicFuseFaceTaskCreateDTO.getMobile());
            AiRingColumnAiFaceTemplate template = aiFaceTemplateService.getById(aiPicFuseFaceTaskCreateDTO.getId());


            String[] materialIdList = aiPicFuseFaceTaskCreateDTO.getMaterialId().split(",");
            List<String> fuseFaceUrlList = ListUtil.toList(materialIdList).stream()
                    .map(materialId -> {
                        PicFuseFaceDTO faceDTO = new PicFuseFaceDTO();
                        faceDTO.setProjectId(aiPicFuseFaceTaskCreateDTO.getActivityId());
                        faceDTO.setModelId(materialId);
                        PicFuseFaceMergeInfoDTO mergeInfoDTO = new PicFuseFaceMergeInfoDTO();
                        mergeInfoDTO.setUrl(aiPicFuseFaceTaskCreateDTO.getMergeInfos().get(0).getUrl());
                        //默认取第一张脸
                        mergeInfoDTO.setTemplateFaceID(materialId + "_1");
                        faceDTO.setMergeInfos(ListUtil.toList(mergeInfoDTO));
                        return TencentCloudAIUtils.parseFuseFaceResponse(com.eleven.cms.aiunionkp.utils.TencentCloudAIUtils.fuseFace(faceDTO, extraInfoMap));
                    }).map(txUrl -> {
                        //存储阿里云合成人脸图片
                        String filePath = "tx" + "/" + IdWorker.get32UUID() + ".jpg";
                        try {
                            return aliMediaService.putObjectRemoteUrl(filePath, txUrl);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).collect(Collectors.toList());

            AiRingFaceImage faceImage = new AiRingFaceImage();
            faceImage.setActivityId(aiPicFuseFaceTaskCreateDTO.getActivityId());
            faceImage.setMaterialId(aiPicFuseFaceTaskCreateDTO.getMaterialId());
            faceImage.setAiRingId(aiPicFuseFaceTaskCreateDTO.getId());
            faceImage.setConvertImageAliUrl(CollectionUtil.join(fuseFaceUrlList,","));
            faceImage.setMobile(aiPicFuseFaceTaskCreateDTO.getMobile());
            List<String> sourceFaceUrl = aiPicFuseFaceTaskCreateDTO.getMergeInfos().stream()
                    .map(AiPicFuseFaceTaskCreateDTO.PicFuseFaceParam::getUrl)
                    .collect(Collectors.toList());
            faceImage.setSourceUserAliUrls(JacksonUtils.toJson(sourceFaceUrl));
            AiRingFaceImageService.save(faceImage);

            // 7.获取图片数据构造clipsParam
            int index = 0;
            LinkedHashMap<String, String> clipsParamMap = JacksonUtils.readValue(template.getClipsParam(), LinkedHashMap.class);
            for (Map.Entry<String, String> entry : clipsParamMap.entrySet()) {
                if (index < fuseFaceUrlList.size()) {
                    entry.setValue(fuseFaceUrlList.get(index));
                    index++;
                } else {
                    break; // 如果 fuseFaceUrlList 的长度小于 clipsParamMap 的大小，停止匹配
                }
            }
            // 2.合成视频
            String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_TXAI_FACE, aiPicFuseFaceTaskCreateDTO.getTemplateId(), JacksonUtils.toJson(clipsParamMap));
            // 3.通过铃音ID更新JobId
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getJobId, jobId)
                    .set(AiRing::getJobType, 0)
                    .eq(AiRing::getId, aiPicFuseFaceTaskCreateDTO.getAiRingId()));
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, aiPicFuseFaceTaskCreateDTO.getAiRingId()));
            log.error("{}-腾讯云ai图片换脸消息处理异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getMobile(), e);
        }
    }


    private PicFuseFaceDTO buildPicFuseFaceTaskCreateDTO(AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        PicFuseFaceDTO picFuseFaceDTO = new PicFuseFaceDTO();
        picFuseFaceDTO.setProjectId(aiPicFuseFaceTaskCreateDTO.getActivityId());
        picFuseFaceDTO.setModelId(aiPicFuseFaceTaskCreateDTO.getMaterialId());
        List<PicFuseFaceMergeInfoDTO> convertParam = aiPicFuseFaceTaskCreateDTO.getMergeInfos().stream().map(e -> {
            PicFuseFaceMergeInfoDTO mergeInfoDTO = new PicFuseFaceMergeInfoDTO();
            mergeInfoDTO.setUrl(e.getUrl());
            mergeInfoDTO.setTemplateFaceID(e.getTemplateFaceId());
            return mergeInfoDTO;
        }).collect(Collectors.toList());

        picFuseFaceDTO.setMergeInfos(convertParam);

        return picFuseFaceDTO;
    }

    public List<FaceInfoDTO> queryFaceInfo(String id) {
        AiRingColumnAiFaceTemplate faceTemplate = aiFaceTemplateService.getOne(new LambdaQueryWrapper<AiRingColumnAiFaceTemplate>()
                .select(AiRingColumnAiFaceTemplate::getId, AiRingColumnAiFaceTemplate::getParam, AiRingColumnAiFaceTemplate::getActivityId, AiRingColumnAiFaceTemplate::getMaterialId)
                .eq(AiRingColumnAiFaceTemplate::getId, id));

        if (faceTemplate == null) {
            throw new JeecgBootException("不存在的模板");
        }

        List<FaceInfoDTO> faceInfoDTOS = new ArrayList<>();
        if (StrUtil.isBlank(faceTemplate.getParam())) {
            for (String materialId : faceTemplate.getMaterialId().split(",")) {
                MaterialListDTO materialListDTO = MaterialListDTO.builder()
                        .ActivityId(faceTemplate.getActivityId())
                        .MaterialId(materialId)
                        .Offset(0)
                        .Limit(1)
                        .build();
                List<FaceInfoDTO> queryResult = TencentCloudAIUtils.parseListMaterialResponse(TencentCloudAIUtils.listMaterial(materialListDTO));
                faceInfoDTOS.addAll(queryResult);
            }
            if (CollectionUtil.isEmpty(faceInfoDTOS)) {
                throw new JeecgBootException("模板获取失败，请重试");
            }

            aiFaceTemplateService.update(new LambdaUpdateWrapper<AiRingColumnAiFaceTemplate>()
                    .set(AiRingColumnAiFaceTemplate::getParam, JacksonUtils.toJson(faceInfoDTOS))
                    .eq(AiRingColumnAiFaceTemplate::getId, faceTemplate.getId()));
            return faceInfoDTOS;
        }
        return JacksonUtils.parseList(faceTemplate.getParam(), FaceInfoDTO.class);
    }

    /**
     * 任务创作-视频换脸
     *
     * @param aiVideoFuseFaceTaskCreateDTO aiVideoFuseFaceTaskCreateDTO
     * @return String 视频ID
     */
    public String createFaceVideoFusionTask(AiVideoFuseFaceTaskCreateDTO aiVideoFuseFaceTaskCreateDTO) {
        // 1.生成铃音任务
        AiRing aiRing = new AiRing();
        aiRing.setRingName(IdWorker.get32UUID());
        aiRing.setMobile(aiVideoFuseFaceTaskCreateDTO.getMobile());
        aiRing.setRingType(0);
        aiRing.setRingPicUrl(aiVideoFuseFaceTaskCreateDTO.getRingPicUrl());
        aiRingService.save(aiRing);

        try {
            // 2.合成视频
            Map<String, Object> extraInfoMap = new HashMap<>();
            extraInfoMap.put("手机号", aiVideoFuseFaceTaskCreateDTO.getMobile());
            //腾讯云Id
            String jobId = TencentCloudAIUtils.parseVideoFaceResponse(TencentCloudAIUtils.videoFaceFusion(buildVideoFuseFaceTaskCreateDTO(aiVideoFuseFaceTaskCreateDTO), extraInfoMap));

            //将jobId暂存至redis
            VideoFaceFusionJobDTO jobDTO = VideoFaceFusionJobDTO.builder()
                    .mobile(aiVideoFuseFaceTaskCreateDTO.getMobile())
                    .aiRingId(aiRing.getId())
                    .createTime(DateUtil.date())
                    .build();
            redisUtil.hset(String.format("%s:tencent:videoFusion:jobs", AI_TAG_KEY), jobId, JacksonUtils.toJson(jobDTO), 3600);
            // 3.通过铃音ID更新JobId
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getJobId, jobId)
                    .set(AiRing::getJobType, 1)
                    .eq(AiRing::getId, aiRing.getId()));

        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, aiRing.getId()));
            log.error("{}-腾讯云ai视频换脸消息处理异常!,手机号:{}", AI_TAG, aiVideoFuseFaceTaskCreateDTO.getMobile(), e);
        }

        return aiRing.getId();
    }

    private VideoFaceFusionDTO buildVideoFuseFaceTaskCreateDTO(AiVideoFuseFaceTaskCreateDTO aiVideoFuseFaceTaskCreateDTO) {
        VideoFaceFusionDTO videoFaceFusionDTO = new VideoFaceFusionDTO();
        videoFaceFusionDTO.setProjectId(aiVideoFuseFaceTaskCreateDTO.getActivityId());
        videoFaceFusionDTO.setModelId(aiVideoFuseFaceTaskCreateDTO.getMaterialId());
        List<VideoFaceFusionMergeInfoDTO> urlList = aiVideoFuseFaceTaskCreateDTO.getMergeInfos().stream().map(e -> {
            VideoFaceFusionMergeInfoDTO infoDTO = new VideoFaceFusionMergeInfoDTO();
            infoDTO.setUrl(e.getUrl());
            return infoDTO;
        }).collect(Collectors.toList());
        videoFaceFusionDTO.setMergeInfos(urlList);
        return videoFaceFusionDTO;
    }

    /**
     * 定时任务轮询视频换脸任务
     */
    @DistributedLock(name = "queryTencentJobScheduled")
    @Scheduled(cron = "0/8 * * * * ?")
    public void queryTencentJobScheduled() {
        Map<Object, Object> jobIdMap = redisUtil.hmget(String.format("%s:tencent:videoFusion:jobs", AI_TAG_KEY));
        if (CollectionUtil.isEmpty(jobIdMap)) {
            return;
        }
        DateTime current = DateUtil.date();
        jobIdMap.forEach((k, v) -> aiRingThreadPoolTaskExecutor.execute(() -> {
            String jobId = (String) k;
            VideoFaceFusionJobDTO jobDTO = JacksonUtils.readValue((String) v, VideoFaceFusionJobDTO.class);
            if (jobDTO == null) {
                redisUtil.hdel(String.format(String.format("%s:tencent:videoFusion:jobs", AI_TAG_KEY)), jobId);
                return;
            }
            //至少间隔11秒钟再查
            if (DateUtil.betweenMs(jobDTO.getCreateTime(), current) < 11) {
                return;
            }
            Map<String, Object> extraInfoMap = new HashMap<>();
            extraInfoMap.put("手机号", jobDTO.getMobile());
            Map<String, String> jobResponseMap = TencentCloudAIUtils.parseVideoFuseFaceJobResponse(TencentCloudAIUtils.queryVideoFaceFusionJobId(jobId, extraInfoMap));

            String jobStatusCode = jobResponseMap.get("jobStatusCode");
            String videoUrl = jobResponseMap.get("videoUrl");

            if (StrUtil.isBlank(jobStatusCode) || StrUtil.isNotBlank(jobStatusCode) && jobStatusCode.equals("5")) {
                aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                        .set(AiRing::getRingMakeStatus, -1)
                        .eq(AiRing::getId, jobDTO.getAiRingId()));
                redisUtil.hdel(String.format(String.format("%s:tencent:videoFusion:jobs", AI_TAG_KEY)), jobId);
                log.warn("{}:ai视频换脸任务失效! jobId：{},删除job轮询任务", AI_TAG, jobId);
                return;
            }

            if (StrUtil.isNotBlank(videoUrl)) {
                String aliVideoUrl = null;
                try {
                    String filePath = "tx" + "/" + IdWorker.get32UUID() + ".mp4";
                    aliVideoUrl = aliMediaService.putObjectRemoteUrlCdn(filePath, videoUrl);
                } catch (Exception e) {
                    log.error("{}:ai视频换脸任务上传阿里云失败! 原url：{}", AI_TAG, videoUrl, e);
                }

                aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                        .set(AiRing::getRingMakeStatus, StrUtil.isBlank(aliVideoUrl) ? 0 : 1)
                        .set(AiRing::getRingUrl, aliVideoUrl)
                        .eq(AiRing::getId, jobDTO.getAiRingId()));
                redisUtil.hdel(String.format(String.format("%s:tencent:videoFusion:jobs", AI_TAG_KEY)), jobId);
                log.info("{}:ai视频换脸任务轮询完成! jobId：{},任务结果：{}", AI_TAG, jobId, StrUtil.isBlank(aliVideoUrl));
            }
        }));
    }
}
