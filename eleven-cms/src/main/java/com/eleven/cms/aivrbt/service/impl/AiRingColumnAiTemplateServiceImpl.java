package com.eleven.cms.aivrbt.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiTemplate;
import com.eleven.cms.aivrbt.mapper.AiRingColumnAiTemplateMapper;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiTemplateService;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryChildVO;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: ai_ring_column_ai_template
 * @Author: jeecg-boot
 * @Date: 2024-10-10
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class AiRingColumnAiTemplateServiceImpl extends ServiceImpl<AiRingColumnAiTemplateMapper, AiRingColumnAiTemplate> implements IAiRingColumnAiTemplateService {

    private final AiRingColumnAiTemplateMapper aiRingColumnAiTemplateMapper;

    /**
     * 分页列表查询
     *
     * @param page                   page
     * @param aiRingColumnAiTemplate aiRingColumnAiTemplate
     * @return IPage<AiRingColumnAiTemplate>
     */
    @Override
    public IPage<AiRingColumnAiTemplate> queryPageList(Page<AiRingColumnAiTemplate> page, AiRingColumnAiTemplate aiRingColumnAiTemplate) {
        return aiRingColumnAiTemplateMapper.queryPageList(page, aiRingColumnAiTemplate);
    }

    /**
     * AI模板页-通过栏目查询模板
     *
     * @return List<AIColumnAndTemplateQueryVO>
     */
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, key = "#root.methodName + ':'+#columnId +':' + #page.getCurrent()+':' +#page.getSize()")
    @Override
    public IPage<AIColumnAndTemplateQueryChildVO> pageAIColumnTemplateByColumnId(Page<AIColumnAndTemplateQueryChildVO> page, String columnId) {
        return aiRingColumnAiTemplateMapper.queryAIColumnTemplateByColumnId(page, columnId);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean save(AiRingColumnAiTemplate entity) {
        return super.save(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean updateById(AiRingColumnAiTemplate entity) {
        return super.updateById(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
