package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.dto.AppCatalogListReqDTO;
import com.eleven.cms.aivrbt.entity.MiniAppCatalog;
import com.eleven.cms.aivrbt.vo.AppCatalogVo;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: app_catalog
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
public interface IAppCatalogService extends IService<MiniAppCatalog> {

    IPage<AppCatalogVo> pageVo(Page<MiniAppCatalog> page, LambdaQueryWrapper<MiniAppCatalog> queryWrapper);

    /**
     * 通用app查询栏目接口
     * @param reqDTO
     * @return
     */
    Result<Object> listAppCatalog(AppCatalogListReqDTO reqDTO);

    void deleteCache(MiniAppCatalog miniAppCatalog);

}
