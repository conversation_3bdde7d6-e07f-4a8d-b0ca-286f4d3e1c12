<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aivrbt.mapper.AiRingBannerMapper">
    <resultMap id="bannerResultMap" type="com.eleven.cms.aivrbt.vo.AiRingValidBannerVO">
        <id property="id" column="id"/>
        <id property="bannerType" column="banner_type"/>
        <id property="bannerLocation" column="banner_location"/>
        <id property="bannerDescription" column="banner_description"/>
        <id property="bannerImage" column="banner_image"/>
        <id property="bannerJumpSource" column="banner_jump_source"/>
        <id property="bannerJumpType" column="banner_jump_type"/>
        <id property="orderBy" column="order_by"/>
        <id property="availableChannel" column="available_channel"/>
        <id property="availableMiniApp" column="available_mini_app"/>
    </resultMap>
    <select id="queryValidBanner" resultMap="bannerResultMap">
        select id,
               banner_type,
               banner_location,
               banner_description,
               banner_image,
               banner_jump_source,
               banner_jump_type,
               order_by,
        available_channel,
        available_mini_app
        from ai_ring_banner
        where status = 1
        <if test=" channelId !=null and channelId !='' ">
           and available_channel LIKE CONCAT('%',#{channelId},'%')
        </if>
        <if test=" resource !=null and resource !='' ">
            and available_mini_app LIKE CONCAT('%',#{resource},'%')
        </if>
        order by banner_location, order_by
    </select>
</mapper>
