package com.eleven.cms.aivrbt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiFaceTemplate;
import com.eleven.cms.aivrbt.mapper.AiRingColumnAiFaceTemplateMapper;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiFaceTemplateService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: ai_ring_column_ai_face_template
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
@Service
public class AiRingColumnAiFaceTemplateServiceImpl extends ServiceImpl<AiRingColumnAiFaceTemplateMapper, AiRingColumnAiFaceTemplate> implements IAiRingColumnAiFaceTemplateService {
    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean save(AiRingColumnAiFaceTemplate entity) {
        return super.save(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean updateById(AiRingColumnAiFaceTemplate entity) {
        return super.updateById(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
