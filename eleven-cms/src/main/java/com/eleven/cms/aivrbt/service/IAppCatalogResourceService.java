package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.dto.*;
import com.eleven.cms.aivrbt.entity.MiniAppCatalogResource;
import com.eleven.cms.aivrbt.vo.CatalogResWarehouseBaseVO;
import com.eleven.cms.aivrbt.vo.ManageCatalogResourceVO;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * @Description: app_catalog_resource
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
public interface IAppCatalogResourceService extends IService<MiniAppCatalogResource> {

    IPage<CatalogResWarehouseBaseVO> warehousePage(AppCatalogResWarehouseDTO appCatalogResource);

    Result<?> saveResource(AppCatalogResourceAddDTO appCatalogResource);

    void updateResource(AppCatalogResourceUpdateDTO appCatalogResource);

    /**
     * 通用查询栏目资源接口修改慎重
     * @param
     * @return
     */
    Result<Object> catalogResList(AppCatalogResListReqDTO reqDTO);

    void deleteCache(String channelId,String catalogId);


    /**
     * 删除关联的栏目缓存
     */
    void deleteRelationCatalogResCache(int resType,List<String> ids);

    List<CatalogResUsageInfoDTO> listUsageCatalogByResId(List<String> staticWallpaperIds, int code);

    void deleteRelationResource(int resType, List<String> list);

    void updateRelationResourceStatus(Integer resType, List<String> list, Integer status, String username, String name);

    Result<Object> catalogResDetail(AppCatalogResDetailReqDTO reqDTO);

    IPage<ManageCatalogResourceVO> pageVo(Page<MiniAppCatalogResource> page, AppCatalogResourceListReq listReq);

    Result<?> listResWarehouseCategory(Integer resType);

}
