package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.entity.AiRingColumnSameTemplate;

/**
 * @Description: ai_ring_column_same_template
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
public interface IAiRingColumnSameTemplateService extends IService<AiRingColumnSameTemplate> {

    /**
     * 分页列表查询
     *
     * @param page page
     * @param aiRingColumnSameTemplate aiRingColumnSameTemplate
     * @return IPage<AiRingColumnSameTemplate>
     */
    IPage<AiRingColumnSameTemplate> queryPageList(Page<AiRingColumnSameTemplate> page, AiRingColumnSameTemplate aiRingColumnSameTemplate);
}
