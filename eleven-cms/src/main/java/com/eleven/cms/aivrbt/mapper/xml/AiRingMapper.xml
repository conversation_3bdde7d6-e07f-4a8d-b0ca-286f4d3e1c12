<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aivrbt.mapper.AiRingMapper">

    <select id="queryMyCreation" resultType="com.eleven.cms.aivrbt.vo.MyCreationQueryVO">
        SELECT
            a.id,
            a.ring_name,
            a.ring_pic_url,
            a.ring_url,
            a.ring_make_status,
            CASE
                WHEN (b.report_status = 0 or b.audit_status = 0
                    or b.distribute_status = 0 or b.setting_status = 0) THEN -1
                WHEN b.setting_status = -1 THEN 1
                WHEN b.setting_status = 1 THEN 2
                ELSE 0
                END AS ring_status
        FROM
            ai_ring a
                LEFT JOIN cms_vrbt_diy_video b ON a.vrbt_diy_video_id = b.id
        WHERE a.mobile = #{mobile} AND a.is_deleted = 0
        ORDER BY a.create_time DESC
    </select>

    <select id="queryMyCreationMaking" resultType="com.eleven.cms.aivrbt.vo.MyCreationQueryVO">
        SELECT
            a.id,
            a.ring_make_status,
            CASE
                WHEN (b.report_status = 0 or b.audit_status = 0
                    or b.distribute_status = 0 or b.setting_status = 0) THEN -1
                WHEN b.setting_status = -1 THEN 1
                WHEN b.setting_status = 1 THEN 2
                ELSE 0
                END AS ring_status
        FROM
            ai_ring a
                LEFT JOIN cms_vrbt_diy_video b ON a.vrbt_diy_video_id = b.id
        WHERE a.id in
        <foreach collection="idList" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="queryUsableCount" resultType="java.lang.Integer">
        SELECT 10 - (
            SELECT COUNT(1) FROM ai_ring
            WHERE mobile = #{mobile}
              and ring_type = 0
              and ring_make_status in (0, 1)
              and create_time BETWEEN DATE_FORMAT(CURDATE(),'%Y-%m-01 00:00:00') AND DATE_FORMAT(LAST_DAY(CURRENT_DATE()),'%Y-%m-%d 23:59:59')
        ) AS usable_count
    </select>
</mapper>