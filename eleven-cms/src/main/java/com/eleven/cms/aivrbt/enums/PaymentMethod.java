package com.eleven.cms.aivrbt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PaymentMethod {
    ALIPAY(1, "支付宝"),
    WECHAT_PAY(2, "微信支付"),
    PHONE_BILL_SUBSCRIPTION(3, "话费订阅");

    private final int code;
    private final String description;


    public static PaymentMethod fromCode(int code) {
        for (PaymentMethod method : values()) {
            if (method.code == code) {
                return method;
            }
        }
        throw new IllegalArgumentException("未知的支付方式代码: " + code);
    }
}
