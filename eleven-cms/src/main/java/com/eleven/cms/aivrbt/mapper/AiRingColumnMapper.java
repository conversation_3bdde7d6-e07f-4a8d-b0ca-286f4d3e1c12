package com.eleven.cms.aivrbt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRingColumn;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: ai_ring_column
 * @Author: jeecg-boot
 * @Date: 2024-10-10
 * @Version: V1.0
 */
public interface AiRingColumnMapper extends BaseMapper<AiRingColumn> {

    /**
     * AI视频彩铃创作专区-查询栏目及模板
     *
     * @return List<AIColumnAndTemplateQueryVO>
     */
    List<AIColumnAndTemplateQueryVO> queryAIColumnAndTemplate(@Param("channelId") String channelId);
    /**
     * AI视频彩铃创作专区-查询栏目及模板
     *
     * @return List<AIColumnAndTemplateQueryVO>
     */
    List<AIColumnAndTemplateQueryVO> queryAllAIColumnAndTemplate(@Param("channelId") String channelId);
}
