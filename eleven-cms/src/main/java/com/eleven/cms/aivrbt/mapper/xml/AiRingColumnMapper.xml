<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aivrbt.mapper.AiRingColumnMapper">
    <resultMap id="OrderResultMap" type="com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryVO">
        <id property="columnId" column="column_id"/>
        <id property="columnName" column="column_name"/>
        <id property="backgroundUrl" column="background_url"/>
        <id property="isHomepageDisplay" column="is_homepage_display"/>
        <id property="displayStyleType" column="display_style_type"/>
        <collection property="data" ofType="com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryChildVO">
            <id property="id" column="id"/>
            <id property="hyTemplateId" column="hy_template_id"/>
            <id property="ringName" column="ring_name"/>
            <id property="picUrl" column="pic_url"/>
            <id property="videoUrl" column="video_url"/>
            <id property="picSlotCount" column="pic_slot_count"/>
            <id property="templateId" column="template_id"/>
            <id property="activityId" column="activity_id"/>
            <id property="materialId" column="material_id"/>
            <id property="materialPicUrl" column="material_pic_url"/>
            <id property="aiType" column="ai_type"/>
            <id property="remark" column="remark"/>
        </collection>
    </resultMap>

    <select id="queryAIColumnAndTemplate" resultMap="OrderResultMap">
        SELECT id,
               column_id,
               column_name,
               ring_name,
               pic_url,
               video_url,
               background_url,
               hy_template_id,
               pic_slot_count,
               template_id,
               activity_id,
               material_id,
               material_pic_url,
               ai_type,
               is_homepage_display,
               display_style_type,
               remark
        FROM (SELECT a.id,
                     b.id       AS column_id,
                     b.column_name,
                     a.ring_name,
                     a.pic_url,
                     a.video_url,
                     b.background_url,
                     a.hy_template_id,
                     a.pic_slot_count,
                     a.template_id,
                     NULL       AS activity_id,
                     NULL       AS material_id,
                     NULL       AS material_pic_url,
                     0          AS ai_type,
                     b.is_homepage_display,
                     b.display_style_type,
                     a.remark,
                     a.order_by AS order_by_template,
                     b.order_by AS order_by_column
              FROM ai_ring_column_ai_template a
                       LEFT JOIN ai_ring_column b ON a.column_id = b.id
              WHERE b.type = 1
                AND b.STATUS = 1
                AND a.STATUS = 1
              AND b.is_homepage_display = 1
                <if test=" channelId !=null and channelId !='' ">
                    and b.available_channel LIKE CONCAT('%',#{channelId},'%')
                </if>

              UNION ALL
              SELECT a.id,
                     b.id       AS column_id,
                     b.column_name,
                     a.ring_name,
                     a.pic_url,
                     a.video_url,
                     b.background_url,
                     NULL       AS hy_template_id,
                     a.pic_slot_count,
                     a.template_id,
                     a.activity_id,
                     a.material_id,
                     a.material_pic_url,
                     a.ai_type,
                     b.is_homepage_display,
                     b.display_style_type,
                     a.remark,
                     a.order_by AS order_by_template,
                     b.order_by AS order_by_column
              FROM ai_ring_column_ai_face_template a
                       LEFT JOIN ai_ring_column b ON a.column_id = b.id
              WHERE b.type = 1
                AND b.STATUS = 1
                AND a.STATUS = 1
                AND b.is_homepage_display = 1
                <if test=" channelId !=null and channelId !='' ">
                    and b.available_channel LIKE CONCAT('%',#{channelId},'%')
                </if>) AS ai_template
        ORDER BY order_by_column,
                 order_by_template
    </select>

    <select id="queryAllAIColumnAndTemplate" resultMap="OrderResultMap">
        SELECT id,
               column_id,
               column_name,
               ring_name,
               pic_url,
               video_url,
               background_url,
               hy_template_id,
               pic_slot_count,
               template_id,
               activity_id,
               material_id,
               material_pic_url,
               ai_type,
               is_homepage_display,
               display_style_type,
               remark
        FROM (SELECT a.id,
                     b.id       AS column_id,
                     b.column_name,
                     a.ring_name,
                     a.pic_url,
                     a.video_url,
                     b.background_url,
                     a.hy_template_id,
                     a.pic_slot_count,
                     a.template_id,
                     NULL       AS activity_id,
                     NULL       AS material_id,
                     NULL       AS material_pic_url,
                     0          AS ai_type,
                     b.is_homepage_display,
                     b.display_style_type,
                     a.remark,
                     a.order_by AS order_by_template,
                     b.order_by AS order_by_column
              FROM ai_ring_column_ai_template a
                       LEFT JOIN ai_ring_column b ON a.column_id = b.id
              WHERE b.type = 1
                AND b.STATUS = 1
                AND a.STATUS = 1
                <if test=" channelId !=null and channelId !='' ">
                    and b.available_channel LIKE CONCAT('%',#{channelId},'%')
                </if>

        UNION ALL
              SELECT a.id,
                     b.id       AS column_id,
                     b.column_name,
                     a.ring_name,
                     a.pic_url,
                     a.video_url,
                     b.background_url,
                     NULL       AS hy_template_id,
                     a.pic_slot_count,
                     a.template_id,
                     a.activity_id,
                     a.material_id,
                     a.material_pic_url,
                     a.ai_type,
                     b.is_homepage_display,
                     b.display_style_type,
                     a.remark,
                     a.order_by AS order_by_template,
                     b.order_by AS order_by_column
              FROM ai_ring_column_ai_face_template a
                       LEFT JOIN ai_ring_column b ON a.column_id = b.id
              WHERE b.type = 1
                AND b.STATUS = 1
                <if test=" channelId !=null and channelId !='' ">
                    and b.available_channel LIKE CONCAT('%',#{channelId},'%')
                </if>
        AND a.STATUS = 1) AS ai_template
        ORDER BY order_by_column,
                 order_by_template
    </select>
</mapper>
