package com.eleven.cms.aivrbt.controller.romote;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRingBanner;
import com.eleven.cms.aivrbt.service.IAiRingBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
* @Description: ai_ring_banner
* @Author: jeecg-boot
* @Date:   2024-11-11
* @Version: V1.0
*/
@Api(tags="ai_ring_banner")
@RestController
@RequestMapping("/api/rpc/cms/aiRingBanner")
@Slf4j
public class RpcAiRingBannerController extends JeecgController<AiRingBanner, IAiRingBannerService> {
   @Autowired
   private IAiRingBannerService aiRingBannerService;

   /**
    * 分页列表查询
    *
    * @param aiRingBanner
    * @param pageNo
    * @param pageSize
    * @param req
    * @return
    */
   @AutoLog(value = "ai_ring_banner-分页列表查询")
   @ApiOperation(value="ai_ring_banner-分页列表查询", notes="ai_ring_banner-分页列表查询")
   @GetMapping(value = "/list")
   public Result<?> queryPageList(AiRingBanner aiRingBanner,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {
       if(StringUtils.isNotEmpty(aiRingBanner.getAvailableChannel())){
           aiRingBanner.setAvailableChannel("*"+aiRingBanner.getAvailableChannel()+"*");
       }
       QueryWrapper<AiRingBanner> queryWrapper = QueryGenerator.initQueryWrapper(aiRingBanner, req.getParameterMap());
       Page<AiRingBanner> page = new Page<AiRingBanner>(pageNo, pageSize);
       IPage<AiRingBanner> pageList = aiRingBannerService.page(page, queryWrapper);
       return Result.ok(pageList);
   }

   /**
    *   添加
    *
    * @param aiRingBanner
    * @return
    */
   @AutoLog(value = "ai_ring_banner-添加")
   @ApiOperation(value="ai_ring_banner-添加", notes="ai_ring_banner-添加")
   @PostMapping(value = "/add")
   public Result<?> add(@RequestBody AiRingBanner aiRingBanner) {
       aiRingBannerService.save(aiRingBanner);
       return Result.ok("添加成功！");
   }

   /**
    *  编辑
    *
    * @param aiRingBanner
    * @return
    */
   @AutoLog(value = "ai_ring_banner-编辑")
   @ApiOperation(value="ai_ring_banner-编辑", notes="ai_ring_banner-编辑")
   @PutMapping(value = "/edit")
   public Result<?> edit(@RequestBody AiRingBanner aiRingBanner) {
       aiRingBannerService.updateById(aiRingBanner);
       return Result.ok("编辑成功!");
   }

   /**
    *   通过id删除
    *
    * @param id
    * @return
    */
   @AutoLog(value = "ai_ring_banner-通过id删除")
   @ApiOperation(value="ai_ring_banner-通过id删除", notes="ai_ring_banner-通过id删除")
   @DeleteMapping(value = "/delete")
   public Result<?> delete(@RequestParam(name="id",required=true) String id) {
       aiRingBannerService.removeById(id);
       return Result.ok("删除成功!");
   }

   /**
    *  批量删除
    *
    * @param ids
    * @return
    */
   @AutoLog(value = "ai_ring_banner-批量删除")
   @ApiOperation(value="ai_ring_banner-批量删除", notes="ai_ring_banner-批量删除")
   @DeleteMapping(value = "/deleteBatch")
   public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
       this.aiRingBannerService.removeByIds(Arrays.asList(ids.split(",")));
       return Result.ok("批量删除成功!");
   }

   /**
    * 通过id查询
    *
    * @param id
    * @return
    */
   @AutoLog(value = "ai_ring_banner-通过id查询")
   @ApiOperation(value="ai_ring_banner-通过id查询", notes="ai_ring_banner-通过id查询")
   @GetMapping(value = "/queryById")
   public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
       AiRingBanner aiRingBanner = aiRingBannerService.getById(id);
       if(aiRingBanner==null) {
           return Result.error("未找到对应数据");
       }
       return Result.ok(aiRingBanner);
   }

   /**
   * 导出excel
   *
   * @param request
   * @param aiRingBanner
   */
   @RequestMapping(value = "/exportXls")
   public Result<?> exportXls(HttpServletRequest request, AiRingBanner aiRingBanner) {
       byte[] bytes = super.exportXlsRemote(request, aiRingBanner, AiRingBanner.class, "ai_ring_banner");
       return Result.ok(bytes);
   }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        System.out.println(1);
        return super.importExcel(request, response, AiRingBanner.class);
    }

}
