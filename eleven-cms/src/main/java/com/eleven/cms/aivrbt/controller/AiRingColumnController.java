package com.eleven.cms.aivrbt.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eleven.cms.aivrbt.entity.AiRingColumn;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.aivrbt.service.IAiRingColumnService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: ai_ring_column
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Api(tags="ai_ring_column")
@RestController
@RequestMapping("/cms/aiRingColumn")
@Slf4j
public class AiRingColumnController extends JeecgController<AiRingColumn, IAiRingColumnService> {
	@Autowired
	private IAiRingColumnService aiRingColumnService;

	/**
	 * 分页列表查询
	 *
	 * @param aiRingColumn
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai_ring_column-分页列表查询")
	@ApiOperation(value="ai_ring_column-分页列表查询", notes="ai_ring_column-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AiRingColumn aiRingColumn,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		if (StringUtils.isNotEmpty(aiRingColumn.getAvailableChannel())) {
			aiRingColumn.setAvailableChannel("*"+aiRingColumn.getAvailableChannel() + "*");
		}
		if (StringUtils.isNotEmpty(aiRingColumn.getAvailableMiniApp())) {
			aiRingColumn.setAvailableMiniApp("*" + aiRingColumn.getAvailableMiniApp() + "*");
		}
		QueryWrapper<AiRingColumn> queryWrapper = QueryGenerator.initQueryWrapper(aiRingColumn, req.getParameterMap());
		Page<AiRingColumn> page = new Page<AiRingColumn>(pageNo, pageSize);
		IPage<AiRingColumn> pageList = aiRingColumnService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param aiRingColumn
	 * @return
	 */
	@AutoLog(value = "ai_ring_column-添加")
	@ApiOperation(value="ai_ring_column-添加", notes="ai_ring_column-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AiRingColumn aiRingColumn) {
		aiRingColumnService.save(aiRingColumn);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param aiRingColumn
	 * @return
	 */
	@AutoLog(value = "ai_ring_column-编辑")
	@ApiOperation(value="ai_ring_column-编辑", notes="ai_ring_column-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AiRingColumn aiRingColumn) {
		aiRingColumnService.updateById(aiRingColumn);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_column-通过id删除")
	@ApiOperation(value="ai_ring_column-通过id删除", notes="ai_ring_column-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aiRingColumnService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai_ring_column-批量删除")
	@ApiOperation(value="ai_ring_column-批量删除", notes="ai_ring_column-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aiRingColumnService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_column-通过id查询")
	@ApiOperation(value="ai_ring_column-通过id查询", notes="ai_ring_column-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AiRingColumn aiRingColumn = aiRingColumnService.getById(id);
		if(aiRingColumn==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aiRingColumn);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aiRingColumn
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AiRingColumn aiRingColumn) {
        return super.exportXls(request, aiRingColumn, AiRingColumn.class, "ai_ring_column");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiRingColumn.class);
    }

}
