package com.eleven.cms.aivrbt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 小程序跟app的包枚举关系
 *
 * <AUTHOR>
 * @datetime 2025/2/27
 */
@Getter
@AllArgsConstructor
public enum MiniAndAppRelationShipChannelEnum {
    COOL_INCOMING_CALL_WX_MINI_APP("coolIncomingCallWxMiniApp", "炫酷来电", "WX", "wx-IncomingCallWxMiniApp"),
    COOL_INCOMING_CALL_OFFICIAL_ACCOUNT("coolIncomingCall-wxOfficialAccount", "炫酷来电", "WX", "coolIncomingCall-wxOfficialAccount"),
    ;
    private final String miniChannelId;

    private final String appChannelId;

    private final String miniSource;

    private final String appSourceChannelId;

    public static MiniAndAppRelationShipChannelEnum getByMiniChannelIdAndMiniSource(String miniChannelId, String miniSource) {
        for (MiniAndAppRelationShipChannelEnum miniAndAppRelationShipChannelEnum : values()) {
            if (miniAndAppRelationShipChannelEnum.getMiniChannelId().equals(miniChannelId) && miniAndAppRelationShipChannelEnum.getMiniSource().equalsIgnoreCase(miniSource)) {
                return miniAndAppRelationShipChannelEnum;
            }
        }
        return null;
    }

    public static MiniAndAppRelationShipChannelEnum getByMiniChannelId(String miniChannelId) {
        for (MiniAndAppRelationShipChannelEnum miniAndAppRelationShipChannelEnum : values()) {
            if (miniAndAppRelationShipChannelEnum.getMiniChannelId().equals(miniChannelId)) {
                return miniAndAppRelationShipChannelEnum;
            }
        }
        return null;
    }

    public static MiniAndAppRelationShipChannelEnum getByAppSourceChannelId(String appSourceChannelId) {
        for (MiniAndAppRelationShipChannelEnum miniAndAppRelationShipChannelEnum : values()) {
            if (miniAndAppRelationShipChannelEnum.getAppSourceChannelId().equals(appSourceChannelId)) {
                return miniAndAppRelationShipChannelEnum;
            }
        }
        return null;
    }
}
