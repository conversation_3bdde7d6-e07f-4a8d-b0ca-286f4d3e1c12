package com.eleven.cms.aivrbt.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.List;

@Data
public class FuseConfigReq {

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 小程序名称
     */
    @Excel(name = "小程序名称", width = 15)
    @ApiModelProperty(value = "小程序名称")
    private String name;
    /**
     * 小程序appId
     */
    private String appId;
    /**
     * 状态:0=无效,1=有效
     */
    @Excel(name = "状态:0=无效,1=有效", width = 15)
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    private Integer status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    private Integer isDeleted;
    /**
     * 默认渠道编码
     */
    private String defaultChannelCode;

    private String configCode;

    private List<FuseConfigItemReq> fuseConfigItemList;
}
