package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
@TableName("mini_app_fuse_config_item")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_fuse_config_item对象", description = "mini_app_fuse_config_item")
public class MiniAppFuseConfigItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 配置主键id
     */
    @Excel(name = "配置主键id", width = 15)
    @ApiModelProperty(value = "配置主键id")
    private String fuseConfigId;
    /**
     * 业务名称
     */
    @Excel(name = "业务名称", width = 15)
    @ApiModelProperty(value = "业务名称")
    private String businessName;
    /**
     * 渠道编码
     */
    @Excel(name = "渠道编码", width = 15)
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 渠道类型
     */
    @Excel(name = "渠道类型", width = 15)
    @ApiModelProperty(value = "渠道类型")
    private String channelType;
    /**
     * 优先级
     */
    @Excel(name = "优先级", width = 15)
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 状态:0=无效,1=有效
     */
    @Excel(name = "状态:0=无效,1=有效", width = 15)
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    private Integer status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 是否是默认 1：是 0：不是
     */
    private Integer defaultChannelCode;
}
