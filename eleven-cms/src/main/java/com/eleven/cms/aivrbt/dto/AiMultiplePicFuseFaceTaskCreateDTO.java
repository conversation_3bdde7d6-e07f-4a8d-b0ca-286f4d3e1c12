package com.eleven.cms.aivrbt.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 任务创作-图片换脸
 *
 * <AUTHOR>
 * @datetime 2024/11/4 15:04
 */

@Data
public class AiMultiplePicFuseFaceTaskCreateDTO {
    private String mobile;
    @NotBlank(message = "模版Id不能为空！")
    private String id;
    @NotBlank(message = "上传图片不能为空！")
    private String faceUrl;
    @NotBlank(message = "活动Id不能为空！")
    private String activityId;
    @NotBlank(message = "素材Id不能为空！")
    private String materialId;
}
