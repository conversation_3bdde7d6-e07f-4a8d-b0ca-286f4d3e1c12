package com.eleven.cms.aivrbt.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.dto.*;
import com.eleven.cms.aivrbt.entity.MiniAppCatalog;
import com.eleven.cms.aivrbt.entity.MiniAppCatalogResource;
import com.eleven.cms.aivrbt.enums.CatalogSubResourceTypeEnum;
import com.eleven.cms.aivrbt.enums.StatusEnum;
import com.eleven.cms.aivrbt.mapper.AppCatalogResourceMapper;
import com.eleven.cms.aivrbt.service.IAppCatalogResourceService;
import com.eleven.cms.aivrbt.service.IAppCatalogService;
import com.eleven.cms.aivrbt.utils.RedisCacheKey;
import com.eleven.cms.aivrbt.vo.*;
import com.eleven.cms.douyinduanju.dto.AlbumInfVO;
import com.eleven.cms.douyinduanju.dto.DramaReq;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaColumn;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanjuTagRelate;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDramaFavorite;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaColumnService;
import com.eleven.cms.douyinduanju.service.IMiniAppDuanjuTagRelateService;
import com.eleven.cms.douyinduanju.service.IMiniAppMiniDramaFavoriteService;
import com.eleven.cms.douyinduanju.service.IMiniAppMiniDramaService;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.douyinduanju.util.TokenUtils.getCurrentUserId;

/**
 * @Description: app_catalog_resource
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
@Service
public class AppCatalogResourceServiceImpl extends ServiceImpl<AppCatalogResourceMapper, MiniAppCatalogResource> implements IAppCatalogResourceService {

    @Resource
    private IAppCatalogService appCatalogService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IMiniAppDramaColumnService miniAppDramaColumnService;

    @Resource
    private IMiniAppMiniDramaService miniAppMiniDramaService;

    @Resource
    IMiniAppMiniDramaFavoriteService favoriteService;

    @Resource
    private IMiniAppDuanjuTagRelateService tagRelateService;


    @Override
    public IPage<CatalogResWarehouseBaseVO> warehousePage(AppCatalogResWarehouseDTO appCatalogResource) {
        Page<CatalogResWarehouseBaseVO> page = new Page<CatalogResWarehouseBaseVO>(appCatalogResource.getPageNo(), appCatalogResource.getPageSize());
        CatalogSubResourceTypeEnum resourceTypeEnum = CatalogSubResourceTypeEnum.getByCode(appCatalogResource.getResType());
        switch (resourceTypeEnum) {
            case SHORT_PLAY_DRAMA:

                DramaReq req = new DramaReq();
                req.setOnlineStatus(appCatalogResource.getOnlineStatus());
                req.setReviewStatus(appCatalogResource.getReviewStatus());
                req.setName(appCatalogResource.getResName());
                req.setStatus(appCatalogResource.getStatus());
                IPage<AlbumInfVO> albumInfVOIPage = new Page<>(appCatalogResource.getPageNo(), appCatalogResource.getPageSize());
                IPage<AlbumInfVO> infVOIPage = miniAppMiniDramaService.pageAlbumInfo(req, albumInfVOIPage);

                List<MiniAppCatalogResMiniAppMiniDramaVO> catalogResWarehouseBaseVOS = infVOIPage.getRecords().stream().map(x -> {
                    MiniAppCatalogResMiniAppMiniDramaVO miniAppCatalogResMiniAppMiniDramaVO = BeanUtil.copyProperties(x, MiniAppCatalogResMiniAppMiniDramaVO.class);
                    miniAppCatalogResMiniAppMiniDramaVO.setResName(x.getName());
                    miniAppCatalogResMiniAppMiniDramaVO.setResId(x.getId());
                    miniAppCatalogResMiniAppMiniDramaVO.setReviewStatus(x.getReviewStatus());
                    miniAppCatalogResMiniAppMiniDramaVO.setOnlineStatus(x.getOnlineStatus());
                    miniAppCatalogResMiniAppMiniDramaVO.setResType(CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode());
                    return miniAppCatalogResMiniAppMiniDramaVO;
                }).collect(Collectors.toList());
                page.setPages(infVOIPage.getPages());
                page.setCurrent(infVOIPage.getCurrent());
                page.setRecords((List<CatalogResWarehouseBaseVO>) (List<?>) catalogResWarehouseBaseVOS);
                page.setTotal(infVOIPage.getTotal());
                page.setSize(infVOIPage.getSize());
            default:
                break;
        }
        setIsAddStatus(page.getRecords(),appCatalogResource.getPid());
        return page;
    }

    @Override
    public Result<?> saveResource(AppCatalogResourceAddDTO appCatalogResource) {
        MiniAppCatalog miniAppCatalog = appCatalogService.getById(appCatalogResource.getPid());
        if (Objects.isNull(miniAppCatalog)) {
            return Result.error("父级栏目不存在");
        }
        List<AppCatalogResourceAddDTO.AppCatalogResourceAddResDTO> resList = appCatalogResource.getResList();
        List<String> resIds = resList.stream().map(AppCatalogResourceAddDTO.AppCatalogResourceAddResDTO::getResId).collect(Collectors.toList());

        int count = this.count(new LambdaQueryWrapper<MiniAppCatalogResource>()
                .eq(MiniAppCatalogResource::getPid, miniAppCatalog.getId())
                .eq(MiniAppCatalogResource::getResType, appCatalogResource.getResType())
                .in(MiniAppCatalogResource::getResId, resIds));
        if(count > 0) {
            return Result.error("资源已存在");
        }
        LoginUser sysUser = HttpUtil.getCurrUser();
        List<MiniAppCatalogResource> saveCatalogResourceList = resList.stream().map(res -> {
            return MiniAppCatalogResource.builder()
                    .resType(appCatalogResource.getResType())
                    .resId(res.getResId())
                    .resName(res.getResName())
                    .pid(appCatalogResource.getPid())
                    .channelId(miniAppCatalog.getChannelId())
                    .orderNum(1)
                    .updateBy(sysUser.getUsername())
                    .createBy(sysUser.getUsername())
                    .updateTime(new Date())
                    .createTime(new Date())
                    .status(StatusEnum.VALID.getCode())
                    .cornerUrl(res.getCornerUrl()).build();
        }).collect(Collectors.toList());
        // 只能一条一条加 批量新增插入不会切换数据源
        saveCatalogResourceList.forEach(x->{
            this.save(x);
        });
        deleteCache(miniAppCatalog.getChannelId(), miniAppCatalog.getId());
        return Result.ok();
    }

    @Override
    public void updateResource(AppCatalogResourceUpdateDTO req) {

        LoginUser sysUser = HttpUtil.getCurrUser();
        MiniAppCatalogResource catalogResource = getById(req.getId());
        MiniAppCatalog miniAppCatalog = appCatalogService.getById(catalogResource.getPid());
        if (Objects.isNull(miniAppCatalog)) {
            throw new JeecgBootException("父级栏目不存在");
        }
        catalogResource.setOrderNum(req.getOrderNum());
        catalogResource.setUserRange(req.getUserRange());
        catalogResource.setStatus(req.getStatus());
        catalogResource.setUpdateBy(sysUser.getUsername());
        catalogResource.setUpdateTime(new Date());
        catalogResource.setCornerUrl(req.getCornerUrl());
        updateById(catalogResource);
        deleteCache(miniAppCatalog.getChannelId(), miniAppCatalog.getId());
    }

    @Override
    public Result<Object> catalogResList(AppCatalogResListReqDTO reqDTO) {
        if (Objects.isNull(reqDTO.getStyleCode()) &&  Objects.isNull(reqDTO.getId())) {
            return Result.error("资源不存在");
        }
        MiniAppCatalog miniAppCatalog = appCatalogService.getOne(new LambdaQueryWrapper<MiniAppCatalog>()
                .eq(Objects.nonNull(reqDTO.getChannelId()), MiniAppCatalog::getChannelId, reqDTO.getChannelId())
                .eq(Objects.nonNull(reqDTO.getId()), MiniAppCatalog::getId, reqDTO.getId())
                .eq(MiniAppCatalog::getStatus, StatusEnum.VALID.getCode())
                .eq(Objects.nonNull(reqDTO.getStyleCode()), MiniAppCatalog::getStyleCode, reqDTO.getStyleCode()));
        if (Objects.isNull(miniAppCatalog)) {
            return Result.error("资源不存在");
        }

        CatalogSubResourceTypeEnum resourceTypeEnum = CatalogSubResourceTypeEnum.getByCode(miniAppCatalog.getSubResourceType());
        String redisKey = String.format(RedisCacheKey.APP_COMMON_CATALOG_RESOURCE_LIST, reqDTO.getChannelId(), miniAppCatalog.getId()
                ,reqDTO.getPageNo(),reqDTO.getPageSize());
        Object resObj = redisUtil.get(redisKey);
        if (Objects.nonNull(resObj)) {
            CommonAppCatalogResVO resVO = getCommonAppCatalogResVOByResType(resObj.toString());
            return Result.ok(resVO);
        }
        String matchRedisKey = String.format(RedisCacheKey.APP_COMMON_CATALOG_RESOURCE_LIST_MATCH, reqDTO.getChannelId(), miniAppCatalog.getId());
        Set<String> matchCacheKeySet = (Set<String>)redisUtil.get(matchRedisKey);
        if(ObjectUtil.isEmpty(matchCacheKeySet)){
            matchCacheKeySet = new HashSet<>();
        }
        matchCacheKeySet.add(redisKey);
        redisUtil.set(matchRedisKey,matchCacheKeySet);
        IPage<MiniAppCatalogResource> page = page(new Page<>(reqDTO.getPageNo(), reqDTO.getPageSize()), new LambdaQueryWrapper<MiniAppCatalogResource>()
                .eq(MiniAppCatalogResource::getStatus, StatusEnum.VALID.getCode())
                .eq(MiniAppCatalogResource::getPid, miniAppCatalog.getId())
                .orderByAsc(MiniAppCatalogResource::getOrderNum, MiniAppCatalogResource::getId));
        List<MiniAppCatalogResource> catalogResourcelist = page.getRecords();
        List<CommonAppCatalogSubResVO> subList = new ArrayList<>();
        switch (resourceTypeEnum) {
            case SHORT_PLAY_DRAMA:
                subList = listBuildMiniAppDrama(catalogResourcelist, reqDTO);
                break;
            default:
                break;
        }
        subList.forEach(res->{
            catalogResourcelist.stream().filter(x->Objects.equals(x.getResId(),res.getId()))
                    .findFirst().ifPresent(appCatalogResource -> {
                        res.setOrderNum(appCatalogResource.getOrderNum());
                    });
        });
        subList = subList.stream().sorted(Comparator.comparing(CommonAppCatalogSubResVO::getOrderNum)).collect(Collectors.toList());
        CommonAppCatalogResVO commonAppCatalogResVO = CommonAppCatalogResVO.builder()
                .id(miniAppCatalog.getId())
                .name(miniAppCatalog.getName())
                .pages(page.getPages())
                .total(page.getTotal())
                .current(page.getCurrent())
                .size(page.getSize())
                .subList(subList)
                .build();
        redisUtil.set(redisKey, JSONUtil.toJsonStr(commonAppCatalogResVO), 60 * 60 * 24);
        return Result.ok(commonAppCatalogResVO);
    }


    /**
     * 查询剧目对象
     *
     * @param catalogResourceList
     * @return
     */
    private List<CommonAppCatalogSubResVO> listBuildMiniAppDrama(List<MiniAppCatalogResource> catalogResourceList, AppCatalogResListReqDTO reqDTO) {
        List<String> ids = catalogResourceList.stream().map(MiniAppCatalogResource::getResId).collect(Collectors.toList());
        List<CommonAppCatalogSubResVO> collect = new ArrayList<>();
        if (!ids.isEmpty()) {
            List<MiniAppMiniDrama> records = miniAppMiniDramaService.listDrama(StringUtils.EMPTY, ids);

            Map<String, List<MiniAppDuanjuTagRelate>> miniAppDuanjuTagRelateMap = new HashMap<>();

            List<String> dramaIds = records.stream().map(MiniAppMiniDrama::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dramaIds)) {
                List<MiniAppDuanjuTagRelate> tagRelateList = tagRelateService.lambdaQuery().in(MiniAppDuanjuTagRelate::getDramaId, dramaIds).list();
                if (CollectionUtils.isNotEmpty(tagRelateList)) {
                    miniAppDuanjuTagRelateMap = tagRelateList.stream().collect(Collectors.groupingBy(MiniAppDuanjuTagRelate::getDramaId));
                }
            }
            //判断是否收藏
            checkDramaUserCollectStatus(records);
            Map<String, List<MiniAppDuanjuTagRelate>> finalMiniAppDuanjuTagRelateMap = miniAppDuanjuTagRelateMap;
            collect = records.stream().map(x -> {
                MiniAppCatalogDramaResVO miniAppCatalogDramaResVO = MiniAppCatalogDramaResVO.builder().build();
                BeanUtils.copyProperties(x, miniAppCatalogDramaResVO);
                miniAppCatalogDramaResVO.setResType(CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode());
                miniAppCatalogDramaResVO.setId(x.getId());

                List<MiniAppDuanjuTagRelate> relateList = finalMiniAppDuanjuTagRelateMap.get(x.getId());
                if (CollectionUtils.isNotEmpty(relateList)) {
                    List<String> tagNameList = relateList.stream().map(MiniAppDuanjuTagRelate::getTagName).collect(Collectors.toList());
                    miniAppCatalogDramaResVO.setTagNameList(tagNameList);
                }

                catalogResourceList.stream().filter(res -> Objects.equals(res.getResId(), x.getId()))
                        .findFirst().ifPresent(appCatalogResource -> {
                            miniAppCatalogDramaResVO.setCornerUrl(appCatalogResource.getCornerUrl());
                        });
                return miniAppCatalogDramaResVO;
            }).collect(Collectors.toList());

        }
        return collect;

    }

    private void checkDramaUserCollectStatus(List<MiniAppMiniDrama> records) {
        Integer currentUserId = getCurrentUserId();
        Map<String, MiniAppMiniDramaFavorite> favoriteMap = new HashMap<>();
        if (Objects.nonNull(currentUserId)) {
            List<MiniAppMiniDramaFavorite> favoriteList = favoriteService.getFavoriteByUserId(currentUserId);
            favoriteMap = favoriteList.stream().collect(Collectors.toMap(MiniAppMiniDramaFavorite::getAlbumId, v -> v));
        }
        for (MiniAppMiniDrama record : records) {
            MiniAppMiniDramaFavorite miniAppMiniDramaFavorite = favoriteMap.get(record.getAlbumId());
            if (Objects.nonNull(miniAppMiniDramaFavorite)) {
                record.setFavoriteStatus(true);
            } else {
                record.setFavoriteStatus(false);
            }
        }
    }

    /**
     * 根据json 转化成对应的栏目资源类型对象
     * @param json
     * @return
     */
    private CommonAppCatalogResVO getCommonAppCatalogResVOByResType(String json) {
        JSONObject jsonObject = JSONUtil.parseObj(json);
        JSONArray subListArr = jsonObject.getJSONArray("subList");
        // 这里不想加一堆构造方法，只能用gson去转，其他json转化依赖构造方法，容易报错
        Gson gson = new Gson();
        List<CommonAppCatalogSubResVO> commonAppCatalogSubResVOList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(subListArr)) {
            for (int i = 0; i < subListArr.size(); i++) {
                JSONObject resJsonObj = subListArr.getJSONObject(i);
                Integer resType = resJsonObj.get("resType",Integer.class);
                CatalogSubResourceTypeEnum resourceTypeEnum = CatalogSubResourceTypeEnum.getByCode(resType);
                switch (resourceTypeEnum) {
                    case SHORT_PLAY_DRAMA:
                        MiniAppCatalogDramaResVO miniAppCatalogDramaResVO = gson.fromJson(resJsonObj.toString(), MiniAppCatalogDramaResVO.class);
                        commonAppCatalogSubResVOList.add(miniAppCatalogDramaResVO);
                        break;
                    default:
                        CommonAppCatalogSubResVO commonAppCatalogSubResVO = gson.fromJson(resJsonObj.toString(), CommonAppCatalogSubResVO.class);
                        commonAppCatalogSubResVOList.add(commonAppCatalogSubResVO);
                }
            }
        }
        // 设置收藏
        Map<Integer, List<CommonAppCatalogSubResVO>> listMap = commonAppCatalogSubResVOList.stream().collect(Collectors.groupingBy(CommonAppCatalogSubResVO::getResType));
        listMap.forEach((resType, resList) -> {
            CatalogSubResourceTypeEnum catalogSubResourceTypeEnum = CatalogSubResourceTypeEnum.getByCode(resType);
            switch (catalogSubResourceTypeEnum) {
                case SHORT_PLAY_DRAMA:
                    List<MiniAppMiniDrama> miniAppMiniDramas = resList.stream()
                            .map(x -> {
                                MiniAppCatalogDramaResVO appCatalogDramaResVO = (MiniAppCatalogDramaResVO) x;
                                MiniAppMiniDrama miniAppMiniDrama = BeanUtil.copyProperties(appCatalogDramaResVO, MiniAppMiniDrama.class);
                                return miniAppMiniDrama;
                            })
                            .collect(Collectors.toList());
                    checkDramaUserCollectStatus(miniAppMiniDramas);
                    resList.forEach(res -> {
                        miniAppMiniDramas.stream().filter(x -> Objects.equals(x.getId(), res.getId()))
                                .findFirst().ifPresent(miniAppMiniDrama -> {
                                    res.setFavoriteStatus(miniAppMiniDrama.getFavoriteStatus());
                                });
                    });
                    break;
                default:
                    break;
            }
        });
        return CommonAppCatalogResVO.builder()
                .id(jsonObject.get("id").toString())
                .name(jsonObject.get("name").toString())
                .subList(commonAppCatalogSubResVOList)
                .total(Long.parseLong(jsonObject.get("total").toString()))
                .current(Long.parseLong(jsonObject.get("current").toString()))
                .size(Long.parseLong(jsonObject.get("size").toString()))
                .pages(Long.parseLong(jsonObject.get("pages").toString()))
                .build();
    }


    @Override
    public void deleteCache(String channelId, String catalogId) {
        if (StringUtils.isEmpty(catalogId) || StringUtils.isEmpty(channelId)) {
            return;
        }
        String redisKey = String.format(RedisCacheKey.APP_COMMON_CATALOG_RESOURCE_LIST_MATCH, channelId, catalogId);
        Set<String> matchKeySet = (Set<String>)redisUtil.get(redisKey);
        if (ObjectUtil.isNotEmpty(matchKeySet)) {
            matchKeySet.forEach(x->{
                redisUtil.del(x);
            });
        }
        redisUtil.del(redisKey);

    }

    @Override
    public void deleteRelationCatalogResCache(int resType, List<String> resIds) {
        List<MiniAppCatalogResource> list = list(new LambdaQueryWrapper<MiniAppCatalogResource>()
                .eq(MiniAppCatalogResource::getResType, resType)
                .in(MiniAppCatalogResource::getResId, resIds));
        if (!list.isEmpty()) {
            list.forEach(resource->{
                deleteCache(resource.getChannelId(),resource.getPid());
            });
        }

    }

    @Override
    public List<CatalogResUsageInfoDTO> listUsageCatalogByResId(List<String> resIds, int resType) {
        if (ObjectUtil.isEmpty(resIds)) {
            return Collections.emptyList();
        }
        return this.baseMapper.listUsageCatalogByResId(resType,resIds);
    }

    @Override
    public void deleteRelationResource(int resType, List<String> resIds) {
        List<MiniAppCatalogResource> miniAppCatalogResources = list(new LambdaQueryWrapper<MiniAppCatalogResource>()
                .eq(MiniAppCatalogResource::getResType, resType)
                .in(MiniAppCatalogResource::getResId, resIds));
        if (!miniAppCatalogResources.isEmpty()) {
            List<String> deleteIds = new ArrayList<>();
            for (MiniAppCatalogResource miniAppCatalogResource : miniAppCatalogResources) {
                deleteIds.add(miniAppCatalogResource.getId());
                deleteCache(miniAppCatalogResource.getChannelId(), miniAppCatalogResource.getPid());
            }
            removeByIds(deleteIds);
        }
    }

    @Override
    public void updateRelationResourceStatus(Integer resType, List<String> resIds, Integer status, String username, String resName) {
        List<MiniAppCatalogResource> miniAppCatalogResources = list(new LambdaQueryWrapper<MiniAppCatalogResource>()
                .eq(MiniAppCatalogResource::getResType, resType)
                .in(MiniAppCatalogResource::getResId, resIds));
        if (!miniAppCatalogResources.isEmpty()) {
            miniAppCatalogResources.forEach(res->{
                res.setStatus(status);
                res.setUpdateBy(username);
                res.setResName(resName);
                res.setUpdateTime(new Date());
                updateById(res);
            });
        }
    }

    @Override
    public Result<Object> catalogResDetail(AppCatalogResDetailReqDTO reqDTO) {
        String newChannelId = reqDTO.getChannelId();
        if (StringUtils.isEmpty(newChannelId)) {
            return Result.error("渠道错误");
        }

        CatalogSubResourceTypeEnum resourceTypeEnum = CatalogSubResourceTypeEnum.getByCode(reqDTO.getResType());
        List<MiniAppCatalogResource> catalogResourcelist = Collections.singletonList(MiniAppCatalogResource.builder().resId(reqDTO.getResId()).resType(resourceTypeEnum.getCode()).build());
        List<CommonAppCatalogSubResVO> resList = new ArrayList<>();
        switch (resourceTypeEnum) {
            case SHORT_PLAY_DRAMA:
                AppCatalogResListReqDTO resListReqDTO = new AppCatalogResListReqDTO();
                resListReqDTO.setChannelId(newChannelId);
                resListReqDTO.setPageNo(1);
                resListReqDTO.setPageSize(1);
                resList = listBuildMiniAppDrama(catalogResourcelist, resListReqDTO);
                List<MiniAppMiniDrama> miniAppMiniDramas = resList.stream()
                        .map(x -> {
                            MiniAppCatalogDramaResVO appCatalogDramaResVO = (MiniAppCatalogDramaResVO) x;
                            return BeanUtil.copyProperties(appCatalogDramaResVO, MiniAppMiniDrama.class);
                        })
                        .collect(Collectors.toList());
                checkDramaUserCollectStatus(miniAppMiniDramas);
                resList.forEach(res -> {
                    miniAppMiniDramas.stream().filter(x -> Objects.equals(x.getId(), res.getId()))
                            .findFirst().ifPresent(miniAppMiniDrama -> {
                                res.setFavoriteStatus(miniAppMiniDrama.getFavoriteStatus());
                            });
                });
                break;
            default:
                break;
        }
        if (resList.isEmpty()){
            return Result.error("数据已不存在");
        }
        return Result.ok(resList.get(0));
    }

    @Override
    public IPage<ManageCatalogResourceVO> pageVo(Page<MiniAppCatalogResource> page, AppCatalogResourceListReq listReq) {
        IPage<MiniAppCatalogResource> pageList = this.page(page, new LambdaQueryWrapper<MiniAppCatalogResource>()
                .eq(ObjectUtil.isNotEmpty(listReq.getPid()), MiniAppCatalogResource::getPid, listReq.getPid())
                .eq(ObjectUtil.isNotEmpty(listReq.getResId()), MiniAppCatalogResource::getResId, listReq.getResId())
                .eq(ObjectUtil.isNotEmpty(listReq.getStatus()), MiniAppCatalogResource::getStatus, listReq.getStatus())
                .orderByAsc(MiniAppCatalogResource::getOrderNum, MiniAppCatalogResource::getCreateTime));
        Map<Integer, List<MiniAppCatalogResource>> resTypeMap = pageList.getRecords().stream().collect(Collectors.groupingBy(MiniAppCatalogResource::getResType));
        List<ManageCatalogResourceVO>  manageCatalogResourceVOList = new ArrayList<>();
        for (Map.Entry<Integer, List<MiniAppCatalogResource>> entry : resTypeMap.entrySet()) {
            CatalogSubResourceTypeEnum resourceTypeEnum = CatalogSubResourceTypeEnum.getByCode(entry.getKey());
            switch (resourceTypeEnum) {
                case SHORT_PLAY_DRAMA:
                    List<ManageCatalogResourceVO> manageCatalogResourceVOS = entry.getValue().stream().map(x -> BeanUtil.copyProperties(x, ManageCatalogResourceVO.class)).collect(Collectors.toList());
                    improveDramaInfoVO(manageCatalogResourceVOS);
                    manageCatalogResourceVOList.addAll(manageCatalogResourceVOS);
                    break;
                default:
                    break;
            }
        }
        IPage<ManageCatalogResourceVO> resourceVOIPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resourceVOIPage.setPages(page.getPages());
        resourceVOIPage.setRecords(manageCatalogResourceVOList);
        return resourceVOIPage;
    }

    /**
     * 完善剧目信息
     *
     * @param manageCatalogResourceVOS
     */
    private void improveDramaInfoVO(List<ManageCatalogResourceVO> manageCatalogResourceVOS) {
        List<String> resIds = manageCatalogResourceVOS.stream().map(ManageCatalogResourceVO::getResId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(resIds)) {
            return;
        }
        List<MiniAppMiniDrama> miniAppMiniDramaList = miniAppMiniDramaService.list(new LambdaQueryWrapper<MiniAppMiniDrama>()
                .in(MiniAppMiniDrama::getId, resIds)
                .select(MiniAppMiniDrama::getId, MiniAppMiniDrama::getCoverUrl));
        manageCatalogResourceVOS.forEach(manageCatalogResource -> {
            miniAppMiniDramaList.stream().filter(x -> Objects.equals(x.getId(), manageCatalogResource.getResId()))
                    .findFirst().ifPresent(miniAppMiniDrama -> {
                        manageCatalogResource.setCoverUrl(miniAppMiniDrama.getCoverUrl());
                    });
        });

    }

    /**
     * 根据资源类型查询资源分类列表
     * @param resType
     * @return
     */
    @Override
    public Result<?> listResWarehouseCategory(Integer resType) {
        List<Object> categoryVOList = new ArrayList<>();
        CatalogSubResourceTypeEnum typeEnum = CatalogSubResourceTypeEnum.getByCode(resType);
        if (Objects.isNull(typeEnum)) {
            return Result.ok(categoryVOList);
        }
        switch (typeEnum) {
            case SHORT_PLAY_DRAMA:
                List<MiniAppDramaColumn> list = miniAppDramaColumnService.list(new LambdaQueryWrapper<MiniAppDramaColumn>()
                        .eq(MiniAppDramaColumn::getIsDeleted, StatusEnum.INVALID.getCode()));
                return Result.ok(list);
            default:
               break;
        }
        return Result.ok();
    }


    private void setIsAddStatus(List<CatalogResWarehouseBaseVO> records,String pid) {
        if (ObjectUtil.isNotEmpty(records)) {
            List<String> resIds = records.stream().map(CatalogResWarehouseBaseVO::getResId).collect(Collectors.toList());
            List<MiniAppCatalogResource> list = list(new LambdaQueryWrapper<MiniAppCatalogResource>()
                    .in(MiniAppCatalogResource::getResId, resIds)
                    .eq(MiniAppCatalogResource::getPid, pid));
            List<String> hasResIds = list
                    .stream().map(MiniAppCatalogResource::getResId).collect(Collectors.toList());
            records.forEach(x -> {
               if (hasResIds.contains(x.getResId())) {
                   x.setHasAdd(StatusEnum.VALID.getCode());
               } else {
                   x.setHasAdd(StatusEnum.INVALID.getCode());
               }
            });
        }
    }
}
