package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: app_catalog
 * @Author: jeecg-boot
 * @Date: 2025-03-13
 * @Version: V1.0
 */
@Data
@TableName("mini_app_catalog")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "app_catalog对象", description = "app_catalog")
public class MiniAppCatalog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 上级栏目id，0=最顶级栏目
     */
    @Excel(name = "上级栏目id，0=最顶级栏目", width = 15)
    @ApiModelProperty(value = "上级栏目id，0=最顶级栏目")
    private String pid;
    /**
     * 渠道id，区分哪个app
     */
    @Excel(name = "渠道id，区分哪个app", width = 15)
    @ApiModelProperty(value = "渠道id，区分哪个app")
    private String channelId;
    /**
     * 样式id
     */
    @Excel(name = "样式id", width = 15)
    @ApiModelProperty(value = "样式id")
    private String styleCode;
    private Integer styleType;
    /**
     * 栏目名称
     */
    @Excel(name = "栏目名称", width = 15)
    @ApiModelProperty(value = "栏目名称")
    private String name;
    /**
     * 下级资源类型 1=栏目资源， 2=桌面组件资源，3=静态壁纸资源，4=动态壁纸资源，
     */
    @Excel(name = "下级资源类型 1=栏目资源， 2=桌面组件资源，3=静态壁纸资源，4=动态壁纸资源，", width = 15)
    @ApiModelProperty(value = "下级资源类型 1=栏目资源， 2=桌面组件资源，3=静态壁纸资源，4=动态壁纸资源，")
    private Integer subResourceType;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderNum;
    /**
     * 0:全部用户 1:会员用户
     */
    @Excel(name = "0:全部用户 1:会员用户", width = 15)
    @ApiModelProperty(value = "0:全部用户 1:会员用户")
    private Integer userRange;
    /**
     * 图地址
     */
    @Excel(name = "图地址", width = 15)
    @ApiModelProperty(value = "图地址")
    private String coverUrl;
    /**
     * 跳转地址
     */
    @Excel(name = "跳转地址", width = 15)
    @ApiModelProperty(value = "跳转地址")
    private String jumpUrl;
    /**
     * banner跳转类型
     */
    @Excel(name = "banner跳转类型", width = 15)
    @ApiModelProperty(value = "banner跳转类型")
    private Integer jumpType;
    /**
     * 0:无效 1:有效
     */
    @Excel(name = "0:无效 1:有效", width = 15)
    @ApiModelProperty(value = "0:无效 1:有效")
    private Integer status;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
