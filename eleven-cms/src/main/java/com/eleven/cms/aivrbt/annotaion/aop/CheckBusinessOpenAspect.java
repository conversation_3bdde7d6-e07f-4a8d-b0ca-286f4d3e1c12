package com.eleven.cms.aivrbt.annotaion.aop;

import com.eleven.cms.aivrbt.service.impl.HaiYiAiService;
import com.eleven.cms.remote.MiguApiService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @datetime 2024/11/6 17:33
 */

@Aspect
@Component
@RequiredArgsConstructor
public class CheckBusinessOpenAspect {

    private final HaiYiAiService haiYiAiService;

    @Around("@annotation(com.eleven.cms.aivrbt.annotaion.CheckBusinessOpenStatus)")
    public Object checkBusinessOpenStatus(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        String mobile = getFiledValueFromArgs(args, "mobile");
        String channelId = getFiledValueFromArgs(args, "channelId");

        if (mobile == null) {
            return Result.error("请求参数中缺少手机号码！");
        }
        if(StringUtils.isEmpty(channelId)){
            channelId = MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX;
        }
        Result<Object> result = haiYiAiService.queryBusinessOpenStatus(mobile, channelId);
        if (!result.isOK()) {
            return result;
        }
        return joinPoint.proceed();
    }

    private String getFiledValueFromArgs(Object[] args, String filedName) {
        for (Object arg : args) {
            if (arg != null) {
                try {
                    Field field = arg.getClass().getDeclaredField(filedName);
                    field.setAccessible(true);
                    return (String) field.get(arg);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // 忽略异常，继续检查下一个参数
                }
            }
        }
        return null;
    }
}
