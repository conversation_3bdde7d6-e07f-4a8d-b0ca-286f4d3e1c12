package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ai_ring_banner
 * @Author: jeecg-boot
 * @Date:   2024-11-11
 * @Version: V1.0
 */
@Data
@TableName("ai_ring_banner")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ai_ring_banner对象", description="ai_ring_banner")
public class AiRingBanner implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**banner类型*/
	@Excel(name = "banner类型", width = 15, dicCode = "ai_clcyz_banner_type")
	@Dict(dicCode = "ai_clcyz_banner_type")
    @ApiModelProperty(value = "banner类型")
    private Integer bannerType;
	/**banner位置路径*/
	@Excel(name = "banner位置路径", width = 15)
    @ApiModelProperty(value = "banner位置路径")
    private String bannerLocation;
	/**banner描述*/
	@Excel(name = "banner描述", width = 15)
    @ApiModelProperty(value = "banner描述")
    private String bannerDescription;
	/**banner图片*/
	@Excel(name = "banner图片", width = 15)
    @ApiModelProperty(value = "banner图片")
    private String bannerImage;
	/**banner跳转资源*/
	@Excel(name = "banner跳转资源", width = 15)
    @ApiModelProperty(value = "banner跳转资源")
    private String bannerJumpSource;
	/**banner跳转类型*/
	@Excel(name = "banner跳转类型", width = 15)
    @ApiModelProperty(value = "banner跳转类型")
    @Dict(dicCode = "ai_clcyz_banner_jump_type")
    private Integer bannerJumpType;

    @Excel(name = "可用渠道号", width = 15)
    @ApiModelProperty(value = "可用渠道号")
    @Dict(dicCode = "available_channel")
    private String availableChannel;

	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private String orderBy;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "ai_clcyz_column_status")
	@Dict(dicCode = "ai_clcyz_column_status")
    @ApiModelProperty(value = "状态")
    private Integer status;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    private String availableMiniApp;

}
