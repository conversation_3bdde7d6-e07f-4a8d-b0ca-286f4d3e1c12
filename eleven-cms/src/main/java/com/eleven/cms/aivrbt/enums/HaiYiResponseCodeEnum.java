package com.eleven.cms.aivrbt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @datetime 2024/10/9 11:52
 */
@Getter
@AllArgsConstructor
public enum HaiYiResponseCodeEnum {

    SUCCESS(10000, "请求成功"),
    PARAMS_ERROR(10100, "参数错误"),
    SERVER_ERROR(10200, "服务异常"),
    TOKEN_INVALID(10400, "无效的token"),
    NOT_FOUND(10404, "数据不存在"),
    PROCESSING(10405, "处理中"),
    ACCOUNT_DISABLED(10408, "账号封禁"),
    CLIENTID_OR_SECRET_NOT_MATCH(10409, "client id 或者secret不匹配"),
    ACCOUNT_FORBIDDEN(20004, "账号被封禁"),
    CONFIRM_FILE_UPLOADED_FAILED(30000, "确认图片上传失败"),
    UPLOAD_FILE_EXPIRED(30001, "上传文件过期"),
    FILE_TOO_LARGE(30003, "文件大小超出限制"),
    PARSE_FILE_FAILED(30004, "解析文件失败（文件类型不符）"),
    FOUND_NSFW_IMAGE(30005, "是NSFW图片"),
    OPERATION_NOT_ALLOWED(70000, "任务不允许操作"),
    DAILY_TASK_LIMIT_EXCEEDED(70001, "任务量超过当日限制"),
    TASK_HANG_LIMIT_EXCEEDED(70002, "任务挂起并发限制"),
    VERSION_NOT_ALLOWED(70003, "模型版本不允许变体"),
    TASK_COMPLETION_CANNOT_BE_CANCELED(70004, "任务完成不能取消"),
    MODEL_FORBIDDEN(70009, "模型被禁用"),
    FEATURE_REQUIRES_PAYMENT(70010, "功能需要开通会员"),
    ART_MODEL_NOT_DOWNLOAD(70018, "模型禁止下载"),
    TASK_BLOCKED_QUEUE_HANG_LIMIT_EXCEEDED(70022, "任务无限队列挂起并发限制"),
    BALANCE_NOT_ENOUGH(80008, "算力不足");

    private final Integer code;

    private final String descp;
}
