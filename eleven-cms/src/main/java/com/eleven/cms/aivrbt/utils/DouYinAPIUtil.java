package com.eleven.cms.aivrbt.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.aivrbt.config.AiVrbtMiniAppProperties;
import com.eleven.cms.util.JacksonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Optional;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;
import static com.eleven.cms.aivrbt.utils.TencentCloudAIUtils.LOG_TAG;

/**
 * <AUTHOR>
 * @datetime 2024/12/24 16:26
 */
public class DouYinAPIUtil {
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);


    public static final String GET_TOKEN = "open.douyin.com/oauth/client_token/";

    public static final String GET_PHONE = "open.douyin.com/api/apps/v1/get_phonenumber_info/";

    public static final String CODE_TO_SESSION = "developer.toutiao.com/api/apps/v2/jscode2session";

    public static String getAuth(String appId, String key) {
        HttpUrl url = HttpUrl.parse("https://" + GET_TOKEN);
        HashMap<Object, Object> map = new HashMap<>();
        map.put("client_key", appId);
        map.put("client_secret", key);
        map.put("grant_type", "client_credential");
        RequestBody body = RequestBody.create(null, JacksonUtils.toJson(map));
        Headers headers = new Headers.Builder().add("content-type", "application/json").build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "抖音获取token");
    }

    public static String parseToken(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        if (rootNode.path("message").asText().equals("success")) {
            return rootNode.path("data").path("access_token").asText();
        }
        throw new JeecgBootException("抖音获取token失败");
    }

    public static String getToken(String appId, String key) {
        //缓存
        String token = (String) SpringUtil.getBean(RedisUtil.class).get("cms:aivrbt:douyin:token");
        if (StrUtil.isBlank(token)) {
            token = parseToken(getAuth(appId, key));
            SpringUtil.getBean(RedisUtil.class).set("cms:aivrbt:douyin:token", token, 7000);
            return token;
        }
        return token;
    }

    public static String getPhone(String token, String source) {

        HttpUrl url = HttpUrl.parse("https://" + GET_PHONE);

        HashMap<Object, Object> map = new HashMap<>();
        map.put("code", token);
        RequestBody body = RequestBody.create(null, JacksonUtils.toJson(map));


        String appId = SpringUtil.getBean(AiVrbtMiniAppProperties.class).getAppIdManager().get(source);
        String key = SpringUtil.getBean(AiVrbtMiniAppProperties.class).getSecretManager().get(source);
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(key)) {
            throw new JeecgBootException("来源错误，请重试");
        }
        Headers headers = new Headers.Builder().add("access-token", getToken(appId, key))
                .add("content-type", "application/json").build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "抖音获取token");
    }

    public static String parsePhoneResponse(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        if (rootNode.path("err_no").asInt() == 0) {
            return rootNode.path("data").asText();
        }
        throw new JeecgBootException("抖音获取token失败");
    }

//    public static String decrypt(String encryptedData, String privateKeyString) {
//        try {
//            // Decode the private key
//            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyString);
//            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
//            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
//            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
//
//            // Decrypt the data
//            Cipher cipher = Cipher.getInstance("RSA");
//            cipher.init(Cipher.DECRYPT_MODE, privateKey);
//            byte[] decryptedDataBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
//            return new String(decryptedDataBytes);
//        } catch (Exception e) {
//            throw new JeecgBootException("解密失败");
//        }
//
//    }

    public static String decrypt(String encryptedData, String sessionKey, String iv) {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] sessionKeyBytes = decoder.decode(sessionKey);
            byte[] ivBytes = decoder.decode(iv);
            byte[] encryptedBytes = decoder.decode(encryptedData);

            // JDK does not support PKCS7Padding, use PKCS5Padding instead
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec skeySpec = new SecretKeySpec(sessionKeyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivSpec);
            byte[] ret = cipher.doFinal(encryptedBytes);
            return new String(ret);
        } catch (Exception e) {
            throw new JeecgBootException("解密失败");
        }
    }

    public static String parsePhoneDecrypt(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        String purePhoneNumber = Optional.ofNullable(rootNode).map(e -> e.path("purePhoneNumber")).map(a -> a.asText()).orElse(
                null);
        if (StrUtil.isBlank(purePhoneNumber)) {
            throw new JeecgBootException("解密失败");
        }
        return purePhoneNumber;
    }

    public static String exchangePhone(String encryptedData, String iv, String sessionKey) {
        return parsePhoneDecrypt(decrypt(encryptedData, sessionKey, iv));
    }


    public static String getSession(String code, String anonymousCode,String source) {
        String appId = SpringUtil.getBean(AiVrbtMiniAppProperties.class).getAppIdManager().get(source);
        String key = SpringUtil.getBean(AiVrbtMiniAppProperties.class).getSecretManager().get(source);
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(key)) {
            throw new JeecgBootException("来源错误，请重试");
        }
        HttpUrl url = HttpUrl.parse("https://" + CODE_TO_SESSION);
        HashMap<Object, Object> map = new HashMap<>();
        map.put("appid", appId);
        map.put("secret", key);
        map.put("code", code);
        map.put("anonymous_code", anonymousCode);
        RequestBody body = RequestBody.create(null, JacksonUtils.toJson(map));
        Headers headers = new Headers.Builder().add("content-type", "application/json").build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "抖音获取授权");
    }

    public static String parseGetSessionResponse(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        if (rootNode.path("err_no").asInt() == 0) {
            return rootNode.path("data").path("session_key").asText();
        }
        throw new JeecgBootException("抖音获取token失败");
    }
}
