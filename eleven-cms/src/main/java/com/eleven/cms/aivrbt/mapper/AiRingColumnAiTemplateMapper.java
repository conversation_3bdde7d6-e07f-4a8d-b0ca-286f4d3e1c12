package com.eleven.cms.aivrbt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiTemplate;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryChildVO;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: ai_ring_column_ai_template
 * @Author: jeecg-boot
 * @Date: 2024-10-10
 * @Version: V1.0
 */
public interface AiRingColumnAiTemplateMapper extends BaseMapper<AiRingColumnAiTemplate> {

    /**
     * 分页列表查询
     *
     * @param page                   page
     * @param aiRingColumnAiTemplate aiRingColumnAiTemplate
     * @return IPage<AiRingColumnAiTemplate>
     */
    IPage<AiRingColumnAiTemplate> queryPageList(Page<AiRingColumnAiTemplate> page, AiRingColumnAiTemplate aiRingColumnAiTemplate);

    /**
     * 通过id查询AI模板-查询ai栏目模板
     *
     * @param columnId 栏目id
     * @param page     分页参数
     * @return List<AIColumnAndTemplateQueryVO>
     */
    IPage<AIColumnAndTemplateQueryChildVO> queryAIColumnTemplateByColumnId(Page<AIColumnAndTemplateQueryChildVO> page, @Param("columnId") String columnId);
}
