package com.eleven.cms.aivrbt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 会员套餐
 * @Author: jeecg-boot
 * @Date: 2025-02-12
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "app_membership_package_product对象", description = "会员套餐")
public class AppMembershipPackageProductVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 套餐名称
     */
    @Excel(name = "套餐名称", width = 15)
    @ApiModelProperty(value = "套餐名称")
    private String productName;
    /**
     * 套餐类型 0-按时 1-按次
     */
    @ApiModelProperty(value = "套餐类型")
    private Integer serviceType;
    /**
     * 可用次数
     */
    @ApiModelProperty(value = "可用次数")
    private Integer availableCount;
    /**
     * 服务天数
     */
    @ApiModelProperty(value = "服务天数")
    private Integer serviceDays;
    /**
     * 适用用户范围
     */
    @ApiModelProperty(value = "适用用户范围")
    private Integer userLimit;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderBy;
    /**
     * 状态
     */
    @Dict(dicCode = "app_product_status")
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 商品上架时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "商品上架时间")
    private Date productShelfTime;
    /**
     * 产品角标文案
     */
    @ApiModelProperty(value = "产品角标文案")
    private String productBadgeText;
    /**
     * 产品原价
     */
    @ApiModelProperty(value = "产品原价")
    private BigDecimal productOriginalPrice;
    /**
     * 实际价格
     */
    @ApiModelProperty(value = "实际价格")
    private BigDecimal actualPrice;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    @ApiModelProperty(value = "渠道")
    @NotEmpty(message = "渠道不能为空")
    private String channelId;

    @ApiModelProperty(value = "app发布平台id")
    @NotEmpty(message = "app发布平台不能为空")
    private String sourceChannel;

    /**
     * 是否永久
     */
    private Integer foreverStatus;
}
