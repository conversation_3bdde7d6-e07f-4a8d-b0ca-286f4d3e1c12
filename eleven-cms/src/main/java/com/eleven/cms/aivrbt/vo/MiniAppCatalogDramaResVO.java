package com.eleven.cms.aivrbt.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class MiniAppCatalogDramaResVO extends CommonAppCatalogSubResVO {
    /**
     * 栏目id
     */
    private String columnId;
    /**
     * 状态 0未生效  1:已生效
     */
    private Integer status;

    /**
     * 短剧信息主键
     */
    private String albumInfoId;
    /**
     * 抖音开放平台短剧id
     */
    private String albumId;

    /**
     * 免费集数
     */
    private Integer freeNum;

    /**
     * 播放量
     */
    private Long viewCount;

    private String icon;
    /**
     * createTime
     */
    private Long createTime;
    /**
     * updateTime
     */
    private Long updateTime;

    private Integer seqCount;

    private String firstEpisodeId;

    private List<String> tagNameList;
}
