package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ai_ring
 * @Author: jeecg-boot
 * @Date: 2024-10-22
 * @Version: V1.0
 */
@Data
@TableName("ai_ring")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ai_ring对象", description = "ai_ring")
public class AiRing implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 铃音名称
     */
    @Excel(name = "铃音名称", width = 15)
    @ApiModelProperty(value = "铃音名称")
    private String ringName;
    /**
     * 铃音生成状态 -1:生成失败 0:生成中 1:生成成功
     */
    @Excel(name = "铃音生成状态 -1:生成失败 0:生成中 1:生成成功", width = 15)
    @ApiModelProperty(value = "铃音生成状态 -1:生成失败 0:生成中 1:生成成功")
    private Integer ringMakeStatus;
    /**
     * 阿里MNS队列ID
     */
    @Excel(name = "阿里MNS队列ID", width = 15)
    @ApiModelProperty(value = "阿里MNS队列ID")
    private String jobId;
    /**
     * 任务队列ID类型 0-阿里云 1-腾讯云
     */
    @Excel(name = "任务队列ID类型 0-阿里云 1-腾讯云", width = 15)
    @ApiModelProperty(value = "任务队列ID类型 0-阿里云 1-腾讯云")
    private Integer jobType;
    /**
     * 铃音封面图地址
     */
    @Excel(name = "铃音封面图地址", width = 15)
    @ApiModelProperty(value = "铃音封面图地址")
    private String ringPicUrl;
    /**
     * 铃音播放地址
     */
    @Excel(name = "铃音播放地址", width = 15)
    @ApiModelProperty(value = "铃音播放地址")
    private String ringUrl;
    /**
     * 铃音类型 0:AI 1:拍同款
     */
    @Excel(name = "铃音类型 0:AI 1:拍同款", width = 15)
    @ApiModelProperty(value = "铃音类型 0:AI 1:拍同款")
    private Integer ringType;
    /**
     * 阿里云模板ID
     */
    @Excel(name = "阿里云模板ID", width = 15)
    @ApiModelProperty(value = "阿里云模板ID")
    private String templateId;
    /**
     * 审核表ID
     */
    @Excel(name = "审核表ID", width = 15)
    @ApiModelProperty(value = "审核表ID")
    private String vrbtDiyVideoId;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
