package com.eleven.cms.aivrbt.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AppCatalogResourceAddDTO {

    @NotNull(message = "父级id不能为空")
    private String pid;

    @NotNull(message = "资源类型不能为空")
    private Integer resType;

    @NotNull(message = "新增的资源信息不能为空")
    private List<AppCatalogResourceAddResDTO> resList;

    @Data
    public static class AppCatalogResourceAddResDTO {

        @NotNull(message = "资源id不能为空")
        private String resId;

        @NotNull(message = "资源名称不能为空")
        private String resName;

        private String cornerUrl;
        ;
    }

}
