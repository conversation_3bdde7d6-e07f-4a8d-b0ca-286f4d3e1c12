package com.eleven.cms.aivrbt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.MiniAppFuseConfigItem;
import com.eleven.cms.aivrbt.service.IMiniAppFuseConfigItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;


@Api(tags = "mini_app_fuse_config_item")
@RestController
@RequestMapping("/cms/miniAppFuseConfigItem")
@Slf4j
public class MiniAppFuseConfigItemController extends JeecgController<MiniAppFuseConfigItem, IMiniAppFuseConfigItemService> {
    @Autowired
    private IMiniAppFuseConfigItemService miniAppFuseConfigItemService;

    /**
     * 分页列表查询
     *
     * @param miniAppFuseConfigItem
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "mini_app_fuse_config_item-分页列表查询")
    @ApiOperation(value = "mini_app_fuse_config_item-分页列表查询", notes = "mini_app_fuse_config_item-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppFuseConfigItem miniAppFuseConfigItem,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<MiniAppFuseConfigItem> queryWrapper = QueryGenerator.initQueryWrapper(miniAppFuseConfigItem, req.getParameterMap());
        Page<MiniAppFuseConfigItem> page = new Page<MiniAppFuseConfigItem>(pageNo, pageSize);
        IPage<MiniAppFuseConfigItem> pageList = miniAppFuseConfigItemService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param miniAppFuseConfigItem
     * @return
     */
    @AutoLog(value = "mini_app_fuse_config_item-添加")
    @ApiOperation(value = "mini_app_fuse_config_item-添加", notes = "mini_app_fuse_config_item-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniAppFuseConfigItem miniAppFuseConfigItem) {
        miniAppFuseConfigItemService.save(miniAppFuseConfigItem);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miniAppFuseConfigItem
     * @return
     */
    @AutoLog(value = "mini_app_fuse_config_item-编辑")
    @ApiOperation(value = "mini_app_fuse_config_item-编辑", notes = "mini_app_fuse_config_item-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniAppFuseConfigItem miniAppFuseConfigItem) {
        miniAppFuseConfigItemService.updateById(miniAppFuseConfigItem);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_fuse_config_item-通过id删除")
    @ApiOperation(value = "mini_app_fuse_config_item-通过id删除", notes = "mini_app_fuse_config_item-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniAppFuseConfigItemService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "mini_app_fuse_config_item-批量删除")
    @ApiOperation(value = "mini_app_fuse_config_item-批量删除", notes = "mini_app_fuse_config_item-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.miniAppFuseConfigItemService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_fuse_config_item-通过id查询")
    @ApiOperation(value = "mini_app_fuse_config_item-通过id查询", notes = "mini_app_fuse_config_item-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppFuseConfigItem miniAppFuseConfigItem = miniAppFuseConfigItemService.getById(id);
        if (miniAppFuseConfigItem == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppFuseConfigItem);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppFuseConfigItem
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniAppFuseConfigItem miniAppFuseConfigItem) {
        return super.exportXls(request, miniAppFuseConfigItem, MiniAppFuseConfigItem.class, "mini_app_fuse_config_item");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppFuseConfigItem.class);
    }

}
