package com.eleven.cms.aivrbt.dto;

import com.eleven.cms.util.BizConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class MiniOrderReq {

    @NotNull(message = "openid 不能为空")
    private String openId;

    @Pattern(regexp = BizConstant.MOBILE_REG, message = "手机号格式不正确！")
    private String mobile;
    /**
     * 1=支付宝 2=微信
     */
    @NotNull(message = "支付方式 不能为空")
    private Integer payType;

    @NotNull(message = "小程序来源 不能为空")
    private String channelId;

    /**
     * 套餐id
     */
    @NotNull(message = "套餐id 不能为空")
    private String productId;
    /**
     * 套餐名称
     */
    private String productName;
}
