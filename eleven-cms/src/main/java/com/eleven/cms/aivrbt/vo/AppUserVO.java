package com.eleven.cms.aivrbt.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date: 2024-12-16
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppUserVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称", width = 15)
    @ApiModelProperty(value = "用户昵称")
    private String nickName;
    /**
     * 包渠道id
     */
    @ApiModelProperty(value = "包渠道id")
    @Dict(dicCode = "app_channel_id")
    @Excel(name = "渠道", width = 20, dicCode = "app_channel_id")
    private String channelId;
    /**
     * app版本
     */
    @Excel(name = "app版本", width = 15)
    @ApiModelProperty(value = "app版本")
    private String appVersion;
    /**
     * 最近打开时间
     */
    @Excel(name = "最近打开时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近打开时间")
    private Date lastUsingTime;
    /**
     * 登录方式 0-未登录 1-验证码登录 -2一键登录
     */
    @Excel(name = "登录方式", width = 15)
    @ApiModelProperty(value = "登录方式")
    @Dict(dicCode = "app_login_type")
    private Integer loginType;
    /**
     * 用户设备类型
     */
    @Excel(name = "用户设备类型", width = 15)
    @ApiModelProperty(value = "用户设备类型")
    private String deviceType;
    /**
     * 活跃天数
     */
    @Excel(name = "活跃天数", width = 15)
    @ApiModelProperty(value = "活跃天数")
    private Integer activeDays;
    /**
     * 日均使用时长
     */
    @Excel(name = "日均使用时长", width = 15)
    @ApiModelProperty(value = "日均使用时长")
    private Integer dailyUsageTime;

    /**
     * 累计总时长
     */
    @Excel(name = "累计总时长", width = 15)
    @ApiModelProperty(value = "累计总时长")
    private Integer totalUsageTime;
    /**
     * 当日使用时长
     */
    @Excel(name = "累计总时长", width = 15)
    @ApiModelProperty(value = "累计总时长")
    private Integer currentDayUseTime;
    /**
     * 订购状态
     */
    @Excel(name = "订购状态", width = 15)
    @ApiModelProperty(value = "订购状态")
    private Integer subscribeStatus;
    /**
     * 开通会员时间
     */
    @Excel(name = "最近打开时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开通会员时间")
    private Date membershipStartTime;
    /**
     * 会员状态
     */
    @Excel(name = "会员状态", width = 15)
    @ApiModelProperty(value = "会员状态")
    @Dict(dicCode = "app_member_status")
    private Integer membershipStatus;

    private String memberShipId;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 使用成本
     */
    @Excel(name = "使用成本", width = 15)
    @ApiModelProperty(value = "使用成本")
    private Integer useCost;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 设备序列号(OAID)
     */
    @Excel(name = "设备序列号(OAID)", width = 15)
    @ApiModelProperty(value = "设备序列号(OAID)")
    private String oaid;
    /**
     * 用户设备信息
     */
    @Excel(name = "用户设备信息", width = 15)
    @ApiModelProperty(value = "用户设备信息")
    private String mobileDeviceInfo;

    /**
     * 注销信息
     */
    @Excel(name = "注销信息", width = 15)
    @ApiModelProperty(value = "注销信息")
    private String cancelingReason;
    /**
     * 省份
     */
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;
    /**
     * 运营商,1移动用户,3联通用户,4电信用户
     */
    @Excel(name = "运营商", width = 15)
    @ApiModelProperty(value = "运营商")
    private String operator;
    /**
     * 账号状态
     */
    @Excel(name = "账号状态", width = 15)
    @ApiModelProperty(value = "账号状态")
    @Dict(dicCode = "app_ac_status")
    private Integer accountStatus;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 注销时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "注销时间")
    private Date cancelTime;

    /**
     * 会员类型0-订阅会员 1-付费会员
     */
    @Excel(name = "使用成本", width = 15)
    @ApiModelProperty(value = "使用成本")
    @Dict(dicCode = "app_membership_type")
    private Integer membershipType;
    /**
     * 会员到期时间
     */
    @Excel(name = "使用成本", width = 15)
    @ApiModelProperty(value = "使用成本")
    private Date membershipExpiryTime;
    /**
     * 付费标识
     */
    @Excel(name = "付费标识", width = 15)
    @ApiModelProperty(value = "付费标识")
    private String payTag;

    /**
     * 用户来源渠道
     */
    @Dict(dicCode = "app_release_platform_id")
    private String sourceChannel;

    /**
     * 用户来源子渠道
     */
    private String subSourceChannel;

    /**
     * 注册时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    /**
     * 默认付费渠道 1=订阅 2=三方支付
     */
    @TableField(exist = false)
    private Integer defaultPaymentStrategy;

    /**
     * 金币余额
     */
    @TableField(exist = false)
    private Integer goldCoinBalance;

    /**
     * 是否永久会员 1=是 0=否
     */
    @TableField(exist = false)
    private Integer membershipForeverStatus;

}
