package com.eleven.cms.aivrbt.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.dto.FuseConfigItemReq;
import com.eleven.cms.aivrbt.dto.FuseConfigReq;
import com.eleven.cms.aivrbt.entity.MiniAppFuseConfig;
import com.eleven.cms.aivrbt.entity.MiniAppFuseConfigItem;
import com.eleven.cms.aivrbt.mapper.MiniAppFuseConfigMapper;
import com.eleven.cms.aivrbt.service.IMiniAppFuseConfigItemService;
import com.eleven.cms.aivrbt.service.IMiniAppFuseConfigService;
import com.eleven.cms.aivrbt.vo.SuitableChannelVO;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.vo.RemoteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class MiniAppFuseConfigServiceImpl extends ServiceImpl<MiniAppFuseConfigMapper, MiniAppFuseConfig> implements IMiniAppFuseConfigService {

    @Resource
    private IMiniAppFuseConfigItemService miniAppFuseConfigItemService;

    @Override
    @Transactional
    public boolean saveConfig(FuseConfigReq req) {
        MiniAppFuseConfig fuseConfig = new MiniAppFuseConfig();
        BeanUtils.copyProperties(req, fuseConfig);
        save(fuseConfig);
        if (CollectionUtil.isNotEmpty(req.getFuseConfigItemList())) {
            List<FuseConfigItemReq> fuseConfigItemList = req.getFuseConfigItemList();
            for (FuseConfigItemReq fuseConfigItemReq : fuseConfigItemList) {
                MiniAppFuseConfigItem fuseConfigItem = new MiniAppFuseConfigItem();
                BeanUtils.copyProperties(fuseConfigItemReq, fuseConfigItem);
                fuseConfigItem.setFuseConfigId(fuseConfig.getId());
                miniAppFuseConfigItemService.save(fuseConfigItem);
            }
        }
        return true;
    }

    @Override
    @Transactional
    public boolean editConfig(FuseConfigReq req) {
        MiniAppFuseConfig fuseConfig = new MiniAppFuseConfig();
        BeanUtils.copyProperties(req, fuseConfig);
        updateById(fuseConfig);
        List<FuseConfigItemReq> itemList = req.getFuseConfigItemList();
        List<MiniAppFuseConfigItem> appFuseConfigItems = miniAppFuseConfigItemService.lambdaQuery().eq(MiniAppFuseConfigItem::getFuseConfigId, fuseConfig.getId()).list();
        if (CollectionUtil.isNotEmpty(appFuseConfigItems)) {
            List<String> ids = appFuseConfigItems.stream().map(MiniAppFuseConfigItem::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            miniAppFuseConfigItemService.removeByIds(ids);
        }
        if (CollectionUtil.isNotEmpty(itemList)) {
            for (FuseConfigItemReq itemReq : itemList) {
                MiniAppFuseConfigItem fuseConfigItem = new MiniAppFuseConfigItem();
                BeanUtils.copyProperties(itemReq, fuseConfigItem);
                fuseConfigItem.setFuseConfigId(fuseConfig.getId());
                miniAppFuseConfigItemService.save(fuseConfigItem);
            }
        }
        return true;
    }

    @Resource
    private MiguApiService miguApiService;

    @Override
    public MiniAppFuseConfig getConfigBySource(String source) {

        MiniAppFuseConfig config = lambdaQuery().eq(MiniAppFuseConfig::getConfigCode, source)
                .eq(MiniAppFuseConfig::getStatus, 1)
                .eq(MiniAppFuseConfig::getIsDeleted, 0).last("limit 1")
                .one();
        if (config != null) {
            List<MiniAppFuseConfigItem> configItems = miniAppFuseConfigItemService.lambdaQuery().eq(MiniAppFuseConfigItem::getFuseConfigId, config.getId())
                    .eq(MiniAppFuseConfigItem::getStatus, 1)
                    .orderByDesc(MiniAppFuseConfigItem::getPriority).list();
            config.setFuseConfigItemList(configItems);
            return config;
        }
        throw new BusinessException("请配置可用的渠道号");
    }

    @Override
    public String getDefaultChannel(String source) {
        MiniAppFuseConfig config = lambdaQuery().eq(MiniAppFuseConfig::getConfigCode, source)
                .eq(MiniAppFuseConfig::getStatus, 1)
                .eq(MiniAppFuseConfig::getIsDeleted, 0).last("limit 1")
                .one();
        if (Objects.nonNull(config)) {
            config.getDefaultChannelCode();
        }
        throw new RuntimeException("请配置可用的渠道");
    }
}
