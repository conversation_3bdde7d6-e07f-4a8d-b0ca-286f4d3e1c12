package com.eleven.cms.aivrbt.dto;

import com.eleven.cms.util.BizConstant;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 游客访问
 *
 * <AUTHOR>
 * @datetime 2024/11/4 15:04
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class AppLoginDTO {

    /**
     * 验证码
     */
    @NotEmpty(message = "请输入验证码")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确")
    private String captcha;
    /**
     * 手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = BizConstant.MOBILE_REG, message = "手机号格式不正确")
    private String mobile;
    /**
     * 省份
     */
    private String province;
    /**
     * 渠道标识
     */
    @NotEmpty(message = "渠道标识不能为空")
    private String channelId;
    /**
     * 设备信息
     */
    private String deviceInfo;
    /**
     * OAID
     */
    @NotEmpty(message = "OAID不能为空")
    private String oaId;

    /**
     * app版本
     */
    private String appVersion;

    @NotNull(message = "登录方式不能为空")
    private Integer loginType;

    @NotNull(message = "渠道不能为空")
    private String sourceChannel;

    @NotNull(message = "openId不能为空")
    private String openId;

}
