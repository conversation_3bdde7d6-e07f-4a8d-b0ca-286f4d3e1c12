package com.eleven.cms.aivrbt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRingFaceImage;
import com.eleven.cms.aivrbt.service.IAiRingFaceImageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: ai_ring_face_image
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Api(tags="ai_ring_face_image")
@RestController
@RequestMapping("/cms/aiRingFaceImage")
@Slf4j
public class AiRingFaceImageController extends JeecgController<AiRingFaceImage, IAiRingFaceImageService> {
	@Autowired
	private IAiRingFaceImageService aiRingFaceImageService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aiRingFaceImage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai_ring_face_image-分页列表查询")
	@ApiOperation(value="ai_ring_face_image-分页列表查询", notes="ai_ring_face_image-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AiRingFaceImage aiRingFaceImage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AiRingFaceImage> queryWrapper = QueryGenerator.initQueryWrapper(aiRingFaceImage, req.getParameterMap());
		Page<AiRingFaceImage> page = new Page<AiRingFaceImage>(pageNo, pageSize);
		IPage<AiRingFaceImage> pageList = aiRingFaceImageService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aiRingFaceImage
	 * @return
	 */
	@AutoLog(value = "ai_ring_face_image-添加")
	@ApiOperation(value="ai_ring_face_image-添加", notes="ai_ring_face_image-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AiRingFaceImage aiRingFaceImage) {
		aiRingFaceImageService.save(aiRingFaceImage);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aiRingFaceImage
	 * @return
	 */
	@AutoLog(value = "ai_ring_face_image-编辑")
	@ApiOperation(value="ai_ring_face_image-编辑", notes="ai_ring_face_image-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AiRingFaceImage aiRingFaceImage) {
		aiRingFaceImageService.updateById(aiRingFaceImage);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_face_image-通过id删除")
	@ApiOperation(value="ai_ring_face_image-通过id删除", notes="ai_ring_face_image-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aiRingFaceImageService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai_ring_face_image-批量删除")
	@ApiOperation(value="ai_ring_face_image-批量删除", notes="ai_ring_face_image-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aiRingFaceImageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_face_image-通过id查询")
	@ApiOperation(value="ai_ring_face_image-通过id查询", notes="ai_ring_face_image-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AiRingFaceImage aiRingFaceImage = aiRingFaceImageService.getById(id);
		if(aiRingFaceImage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aiRingFaceImage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aiRingFaceImage
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AiRingFaceImage aiRingFaceImage) {
        return super.exportXls(request, aiRingFaceImage, AiRingFaceImage.class, "ai_ring_face_image");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiRingFaceImage.class);
    }

}
