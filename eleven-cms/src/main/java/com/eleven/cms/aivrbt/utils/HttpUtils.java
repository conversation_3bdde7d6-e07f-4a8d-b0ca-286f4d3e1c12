package com.eleven.cms.aivrbt.utils;

import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OKHTTP工具类
 *
 * <AUTHOR>
 * @datetime 2024/10/25 17:03
 */
@Slf4j
public class HttpUtils {

    public static Map<String, OkHttpClient> map = new HashMap<String, OkHttpClient>() {{
        put("COMMON_CLIENT", COMMON_CLIENT);
        put("SYNC_ORDER_CLIENT", SYNC_ORDER_CLIENT);
    }};

    public static final OkHttpClient COMMON_CLIENT = new OkHttpClient()
            .newBuilder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10,TimeUnit.SECONDS)
            .readTimeout(10,TimeUnit.SECONDS)
            .addNetworkInterceptor(new CurlInterceptor(System.out::println))
            .build();

    public static final OkHttpClient SYNC_ORDER_CLIENT = new OkHttpClient()
            .newBuilder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10,TimeUnit.SECONDS)
            .readTimeout(40,TimeUnit.SECONDS)
            .addNetworkInterceptor(new CurlInterceptor(System.out::println))
            .build();

    private static final String LOG_FORMAT_ERROR = "{}-{}-【ERROR】-日记标记:{}=>请求体:{},Request body:{}";
    private static final String LOG_FORMAT_REQUEST = "{}-{}-【请求】-日记标记:{}=>请求体:{},Request body:{}";
    private static final String LOG_FORMAT_RESPONSE = "{}-{}-【响应】-日记标记:{}=>响应体:{},响应结果:{}";

    /**
     * get请求
     *
     * @param url url
     * @param tag tag
     * @param tagDescp tagDescp
     * @return String
     */
    public static String get(OkHttpClient client, HttpUrl url, String tag, String tagDescp) {
        return get(client, url, null, tag, tagDescp, null);
    }

    /**
     * get请求
     *
     * @param url url
     * @param headers headers
     * @param tag tag
     * @param tagDescp tagDescp
     * @return String
     */
    public static String get(OkHttpClient client, HttpUrl url, Headers headers, String tag, String tagDescp) {
        return get(client, url, headers, tag, tagDescp, null);
    }

    /**
     * post请求
     *
     * @param url url
     * @param body body
     * @param tag tag
     * @param tagDescp tagDescp
     * @return String
     */
    public static String post(OkHttpClient client, HttpUrl url, RequestBody body, String tag, String tagDescp) {
        return post(client, url, body, null, tag, tagDescp, null);
    }

    /**
     * post请求
     *
     * @param url url
     * @param body body
     * @param tag tag
     * @param tagDescp tagDescp
     * @param extraInfoMap extraInfoMap
     * @return String
     */
    public static String post(OkHttpClient client, HttpUrl url, RequestBody body, String tag, String tagDescp, Map<String, Object> extraInfoMap) {
        return post(client, url, body, null, tag, tagDescp, extraInfoMap);
    }

    /**
     * post请求
     *
     * @param url url
     * @param body body
     * @param headers headers
     * @param tag tag
     * @param tagDescp tagDescp
     * @return String
     */
    public static String post(OkHttpClient client, HttpUrl url, RequestBody body, Headers headers, String tag, String tagDescp) {
        return post(client, url, body, headers, tag, tagDescp, null);
    }

    /**
     * GET请求
     *
     * @param client client
     * @param url url
     * @param headers headers
     * @param tag tag
     * @param tagDescp tagDescp
     * @param extraInfoMap extraInfoMap
     * @return String
     */
    private static String get(OkHttpClient client, HttpUrl url, Headers headers, String tag, String tagDescp, Map<String, Object> extraInfoMap) {
        Request request = createRequest("GET", url, headers, null);
        return getResult(client, request, null, tag, tagDescp, extraInfoMap);
    }

    /**
     * post请求
     *
     * @param url url
     * @param body body
     * @param headers headers
     * @param tag tag
     * @param tagDescp tagDescp
     * @param extraInfoMap extraInfoMap
     * @return String
     */
    public static String post(OkHttpClient client, HttpUrl url, RequestBody body, Headers headers, String tag, String tagDescp, Map<String, Object> extraInfoMap) {
        Request request = createRequest("POST", url, headers, body);
        String bodyJsonStr = "";
        if (body != null) {
            Buffer buffer = new Buffer();
            try {
                body.writeTo(buffer);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            bodyJsonStr = buffer.readUtf8();
        }
        return getResult(client, request, bodyJsonStr, tag, tagDescp, extraInfoMap);
    }

    /**
     * getResult
     *
     * @param client
     * @param request
     * @param bodyJsonStr
     * @param tag
     * @param tagDescp
     * @param extraInfoMap
     * @return String
     */
    private static String getResult(OkHttpClient client, Request request, String bodyJsonStr, String tag, String tagDescp, Map<String, Object> extraInfoMap) {
        String result = null;
        log.info(LOG_FORMAT_REQUEST, tag, tagDescp, extraInfoMap, request, bodyJsonStr);
        try (Response response = client.newCall(request).execute()) {
            result = response.body().string();
            log.info(LOG_FORMAT_RESPONSE, tag, tagDescp, extraInfoMap, response, result);
        } catch (Exception e) {
            log.error(LOG_FORMAT_ERROR, tag, tagDescp, extraInfoMap, request, bodyJsonStr, e);
        }
        return result;
    }

    /**
     * createRequest
     *
     * @param requestMethod requestMethod
     * @param url url
     * @param headers headers
     * @param body body
     * @return Request
     */
    private static Request createRequest(String requestMethod, HttpUrl url, Headers headers, RequestBody body) {
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        if (headers != null) {
            builder.headers(headers);
        }
        switch (requestMethod) {
            case "GET" : builder.get(); break;
            case "POST" : builder.post(body); break;
            default :
                // ignore
        }
        return builder.build();
    }
}
