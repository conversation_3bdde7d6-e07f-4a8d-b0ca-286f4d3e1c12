package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.dto.FuseConfigReq;
import com.eleven.cms.aivrbt.entity.MiniAppFuseConfig;


public interface IMiniAppFuseConfigService extends IService<MiniAppFuseConfig> {

    boolean saveConfig(FuseConfigReq req);

    boolean editConfig(FuseConfigReq req);

    MiniAppFuseConfig getConfigBySource(String source);

    String getDefaultChannel(String source);
}
