package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@TableName("mini_app_fuse_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_fuse_config对象", description = "mini_app_fuse_config")
public class MiniAppFuseConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 小程序名称
     */
    @Excel(name = "小程序名称", width = 15)
    @ApiModelProperty(value = "小程序名称")
    private String name;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 配置编码
     */
    private String configCode;
    /**
     * 状态:0=无效,1=有效
     */
    @Excel(name = "状态:0=无效,1=有效", width = 15)
    @ApiModelProperty(value = "状态:0=无效,1=有效")
    private Integer status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 默认渠道编码
     */
    private String defaultChannelCode;

    private Integer isDeleted;
    /**
     * 配置项
     */
    @TableField(exist = false)
    private List<MiniAppFuseConfigItem> fuseConfigItemList;
}
