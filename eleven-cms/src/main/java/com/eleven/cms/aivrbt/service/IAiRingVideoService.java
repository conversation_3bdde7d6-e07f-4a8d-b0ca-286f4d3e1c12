package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.entity.AiRingVideo;

/**
 * @Description: ai_ring_video
 * @Author: jeecg-boot
 * @Date:   2024-10-16
 * @Version: V1.0
 */
public interface IAiRingVideoService extends IService<AiRingVideo> {

    /**
     * 分页列表查询
     *
     * @param page page
     * @param aiRingVideo aiRingVideo
     * @return IPage<AiRingVideo>
     */
    IPage<AiRingVideo> queryPageList(Page<AiRingVideo> page, AiRingVideo aiRingVideo);
}
