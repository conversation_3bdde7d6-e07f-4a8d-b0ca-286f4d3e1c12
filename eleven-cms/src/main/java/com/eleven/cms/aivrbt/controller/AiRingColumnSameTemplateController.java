package com.eleven.cms.aivrbt.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRingColumnSameTemplate;
import com.eleven.cms.aivrbt.service.IAiRingColumnSameTemplateService;
import com.eleven.qycl.service.AliMediaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: ai_ring_column_same_template
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Api(tags="ai_ring_column_same_template")
@RestController
@RequestMapping("/cms/aiRingColumnSameTemplate")
@Slf4j
public class AiRingColumnSameTemplateController extends JeecgController<AiRingColumnSameTemplate, IAiRingColumnSameTemplateService> {
	@Autowired
	private IAiRingColumnSameTemplateService aiRingColumnSameTemplateService;
	@Autowired
	private AliMediaService aliMediaService;

	private final String START_CATALOG = "output/sameTemplate/";
	
	/**
	 * 分页列表查询
	 *
	 * @param aiRingColumnSameTemplate
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_same_template-分页列表查询")
	@ApiOperation(value="ai_ring_column_same_template-分页列表查询", notes="ai_ring_column_same_template-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AiRingColumnSameTemplate aiRingColumnSameTemplate,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		Page<AiRingColumnSameTemplate> page = new Page<AiRingColumnSameTemplate>(pageNo, pageSize);
		return Result.ok(aiRingColumnSameTemplateService.queryPageList(page, aiRingColumnSameTemplate));
	}
	
	/**
	 *   添加
	 *
	 * @param aiRingColumnSameTemplate
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_same_template-添加")
	@ApiOperation(value="ai_ring_column_same_template-添加", notes="ai_ring_column_same_template-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AiRingColumnSameTemplate aiRingColumnSameTemplate) {
		if (StringUtils.isNotEmpty(aiRingColumnSameTemplate.getVideoUrl())) {
			String outputPath = START_CATALOG + DateUtils.formatDate(new Date(), "yyyyMMdd") + "/" + FilenameUtils.getName(aiRingColumnSameTemplate.getVideoUrl());
			try {
				String cdnPath = aliMediaService.putObjectRemoteUrlCdn(outputPath, aiRingColumnSameTemplate.getVideoUrl());
				aiRingColumnSameTemplate.setAliRingUrl(cdnPath);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

		}
		aiRingColumnSameTemplateService.save(aiRingColumnSameTemplate);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aiRingColumnSameTemplate
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_same_template-编辑")
	@ApiOperation(value="ai_ring_column_same_template-编辑", notes="ai_ring_column_same_template-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AiRingColumnSameTemplate aiRingColumnSameTemplate) {
		AiRingColumnSameTemplate template = aiRingColumnSameTemplateService.getById(aiRingColumnSameTemplate.getId());
		if (StringUtils.isNotEmpty(aiRingColumnSameTemplate.getVideoUrl())) {
			String outputPath = START_CATALOG + DateUtils.formatDate(new Date(), "yyyyMMdd") + "/" + FilenameUtils.getName(aiRingColumnSameTemplate.getVideoUrl());
			try {
				String cdnPath = aliMediaService.putObjectRemoteUrlCdn(outputPath, aiRingColumnSameTemplate.getVideoUrl());
				aiRingColumnSameTemplate.setAliRingUrl(cdnPath);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}

		aiRingColumnSameTemplateService.updateById(aiRingColumnSameTemplate);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_same_template-通过id删除")
	@ApiOperation(value="ai_ring_column_same_template-通过id删除", notes="ai_ring_column_same_template-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aiRingColumnSameTemplateService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_same_template-批量删除")
	@ApiOperation(value="ai_ring_column_same_template-批量删除", notes="ai_ring_column_same_template-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aiRingColumnSameTemplateService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_same_template-通过id查询")
	@ApiOperation(value="ai_ring_column_same_template-通过id查询", notes="ai_ring_column_same_template-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AiRingColumnSameTemplate aiRingColumnSameTemplate = aiRingColumnSameTemplateService.getById(id);
		if(aiRingColumnSameTemplate==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aiRingColumnSameTemplate);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aiRingColumnSameTemplate
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AiRingColumnSameTemplate aiRingColumnSameTemplate) {
        return super.exportXls(request, aiRingColumnSameTemplate, AiRingColumnSameTemplate.class, "ai_ring_column_same_template");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiRingColumnSameTemplate.class);
    }

}
