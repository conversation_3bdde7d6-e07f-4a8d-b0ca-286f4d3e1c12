package com.eleven.cms.aivrbt.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @datetime 2024/11/7 17:53
 */
@Configuration
public class ThreadPoolConfig {

    // 配置线程池
    @Bean(name = "aiRingThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor aiRingThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());

        // 最大线程数
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() + 1);

        // 空闲线程存活时间
        executor.setKeepAliveSeconds(60);

        // 线程池所使用的缓冲队列
        executor.setQueueCapacity(20);

        // 线程前缀
        executor.setThreadNamePrefix("aiRing-thread-");

        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化线程池
        executor.initialize();

        return executor;
    }
}