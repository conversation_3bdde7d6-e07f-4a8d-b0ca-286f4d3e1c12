package com.eleven.cms.aivrbt.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.entity.AiRingVideo;
import com.eleven.cms.aivrbt.mapper.AiRingVideoMapper;
import com.eleven.cms.aivrbt.service.IAiRingVideoService;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: ai_ring_video
 * @Author: jeecg-boot
 * @Date:   2024-10-16
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class AiRingVideoServiceImpl extends ServiceImpl<AiRingVideoMapper, AiRingVideo> implements IAiRingVideoService {

    private final AiRingVideoMapper aiRingVideoMapper;

    /**
     * 分页列表查询
     *
     * @param page page
     * @param aiRingVideo aiRingVideo
     * @return IPage<AiRingVideo>
     */
    @Override
    public IPage<AiRingVideo> queryPageList(Page<AiRingVideo> page, AiRingVideo aiRingVideo) {
        return aiRingVideoMapper.queryPageList(page, aiRingVideo);
    }


    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean save(AiRingVideo entity) {
        return super.save(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean updateById(AiRingVideo entity) {
        return super.updateById(entity);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_VRBT_COLUMN_TEMPLATE, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
