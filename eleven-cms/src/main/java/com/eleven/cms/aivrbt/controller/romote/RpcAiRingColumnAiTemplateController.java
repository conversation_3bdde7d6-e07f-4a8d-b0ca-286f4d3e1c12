package com.eleven.cms.aivrbt.controller.romote;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiTemplate;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
* @Description: ai_ring_column_ai_template
* @Author: jeecg-boot
* @Date:   2024-10-10
* @Version: V1.0
*/
@Api(tags="ai_ring_column_ai_template")
@RestController
@RequestMapping("/api/rpc/cms/aiRingColumnAiTemplate")
@Slf4j
public class RpcAiRingColumnAiTemplateController extends JeecgController<AiRingColumnAiTemplate, IAiRingColumnAiTemplateService> {
   @Autowired
   private IAiRingColumnAiTemplateService aiRingColumnAiTemplateService;

   /**
    * 分页列表查询
    *
    * @param aiRingColumnAiTemplate
    * @param pageNo
    * @param pageSize
    * @param req
    * @return
    */
   @AutoLog(value = "ai_ring_column_ai_template-分页列表查询")
   @ApiOperation(value="ai_ring_column_ai_template-分页列表查询", notes="ai_ring_column_ai_template-分页列表查询")
   @GetMapping(value = "/list")
   public Result<?> queryPageList(AiRingColumnAiTemplate aiRingColumnAiTemplate,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {
       Page<AiRingColumnAiTemplate> page = new Page<AiRingColumnAiTemplate>(pageNo, pageSize);
       return Result.ok(aiRingColumnAiTemplateService.queryPageList(page, aiRingColumnAiTemplate));
   }

   /**
    *   添加
    *
    * @param aiRingColumnAiTemplate
    * @return
    */
   @AutoLog(value = "ai_ring_column_ai_template-添加")
   @ApiOperation(value="ai_ring_column_ai_template-添加", notes="ai_ring_column_ai_template-添加")
   @PostMapping(value = "/add")
   public Result<?> add(@RequestBody AiRingColumnAiTemplate aiRingColumnAiTemplate) {
       aiRingColumnAiTemplateService.save(aiRingColumnAiTemplate);
       return Result.ok("添加成功！");
   }

   /**
    *  编辑
    *
    * @param aiRingColumnAiTemplate
    * @return
    */
   @AutoLog(value = "ai_ring_column_ai_template-编辑")
   @ApiOperation(value="ai_ring_column_ai_template-编辑", notes="ai_ring_column_ai_template-编辑")
   @PutMapping(value = "/edit")
   public Result<?> edit(@RequestBody AiRingColumnAiTemplate aiRingColumnAiTemplate) {
       aiRingColumnAiTemplateService.updateById(aiRingColumnAiTemplate);
       return Result.ok("编辑成功!");
   }

   /**
    *   通过id删除
    *
    * @param id
    * @return
    */
   @AutoLog(value = "ai_ring_column_ai_template-通过id删除")
   @ApiOperation(value="ai_ring_column_ai_template-通过id删除", notes="ai_ring_column_ai_template-通过id删除")
   @DeleteMapping(value = "/delete")
   public Result<?> delete(@RequestParam(name="id",required=true) String id) {
       aiRingColumnAiTemplateService.removeById(id);
       return Result.ok("删除成功!");
   }

   /**
    *  批量删除
    *
    * @param ids
    * @return
    */
   @AutoLog(value = "ai_ring_column_ai_template-批量删除")
   @ApiOperation(value="ai_ring_column_ai_template-批量删除", notes="ai_ring_column_ai_template-批量删除")
   @DeleteMapping(value = "/deleteBatch")
   public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
       this.aiRingColumnAiTemplateService.removeByIds(Arrays.asList(ids.split(",")));
       return Result.ok("批量删除成功!");
   }

   /**
    * 通过id查询
    *
    * @param id
    * @return
    */
   @AutoLog(value = "ai_ring_column_ai_template-通过id查询")
   @ApiOperation(value="ai_ring_column_ai_template-通过id查询", notes="ai_ring_column_ai_template-通过id查询")
   @GetMapping(value = "/queryById")
   public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
       AiRingColumnAiTemplate aiRingColumnAiTemplate = aiRingColumnAiTemplateService.getById(id);
       if(aiRingColumnAiTemplate ==null) {
           return Result.error("未找到对应数据");
       }
       return Result.ok(aiRingColumnAiTemplate);
   }

    /**
     * 导出excel
     *
     * @param request
     * @param aiRingColumnAiTemplate
     */
    @RequestMapping(value = "/exportXls")
    public Result<?> exportXls(HttpServletRequest request, AiRingColumnAiTemplate aiRingColumnAiTemplate) {
        byte[] bytes = super.exportXlsRemote(request, aiRingColumnAiTemplate, AiRingColumnAiTemplate.class, "ai_ring_column_ai_template");
        return Result.ok(bytes);
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiRingColumnAiTemplate.class);
    }

}
