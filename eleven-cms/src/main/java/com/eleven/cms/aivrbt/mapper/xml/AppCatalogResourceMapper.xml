<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aivrbt.mapper.AppCatalogResourceMapper">

    <select id="listUsageCatalogByResId" resultType="com.eleven.cms.aivrbt.dto.CatalogResUsageInfoDTO">
        SELECT
        ac.id,
        ac.`name` AS catalogName,
        res.res_id AS resId
        FROM
        mini_app_catalog ac
        LEFT JOIN mini_app_catalog_resource res ON res.pid = ac.id
        WHERE
        res.res_type = #{resType}
        AND res.res_id IN
        <foreach collection="resIds" close=")" open="(" separator="," item="resId">
            #{resId}
        </foreach>
    </select>

</mapper>