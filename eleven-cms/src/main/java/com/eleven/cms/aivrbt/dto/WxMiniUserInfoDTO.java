package com.eleven.cms.aivrbt.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class WxMiniUserInfoDTO {

    @NotNull(message = "code 不能为空")
    private String code;

    @NotNull(message = "channelId 不能为空")
    private String channelId;

    @NotNull(message = "encryptedData 不能为空")
    private String encryptedData;

    @NotNull(message = "iv 不能为空")
    private String iv;

    /**
     * wx ，dy
     */
    private String miniSource;

    private String appVersion;

    /**
     * 设备信息
     */
    private String deviceInfo;
}
