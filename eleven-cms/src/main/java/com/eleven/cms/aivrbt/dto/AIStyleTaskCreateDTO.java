package com.eleven.cms.aivrbt.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/10/8 15:47
 */
@Data
public class AIStyleTaskCreateDTO {

    List<FileInfoDTO> fileInfoDTOList;

    @NotBlank(message = "手机号不能为空！")
    @ApiModelProperty("手机号")
    private String mobile;

    @NotBlank(message = "模版Id不能为空！")
    @ApiModelProperty("模版Id")
    private String templateId;

    @NotBlank(message = "滤镜模版Id不能为空！")
    @ApiModelProperty("滤镜模版Id")
    private String hyTemplateId;

    private String aiRingId;
}
