package com.eleven.cms.aivrbt.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AppCatalogResWarehouseDTO {

    @NotNull(message = "父级id不能为空")
    private String pid;

    @NotNull(message = "页码不能为空")
    private Integer pageNo;

    @NotNull(message = "页大小不能为空")
    private Integer pageSize;

    /**
     * CatalogSubResourceTypeEnum
     */
    @NotNull(message = "资源类型不能为空")
    private Integer resType;

    private String resName;

    private String resId;

    /**
     * 资源分类ids
     */
    private List<String> resCategoryIds;

    /**
     * 资源分类id 多个,分割
     */
    private String resCategoryIdsStr;

    /**
     * 送审状态:1:未送审,2:审核中 3:未通过审核  4:审核通过
     */
    private Integer reviewStatus;

    /**
     * 上下架状态：1:未上线,2:上线,3:下线
     */
    private Integer onlineStatus;
    private Integer status;
}
