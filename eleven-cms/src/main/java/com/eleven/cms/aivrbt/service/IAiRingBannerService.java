package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.entity.AiRingBanner;
import com.eleven.cms.aivrbt.vo.AiRingValidBannerListVO;
import com.eleven.cms.aivrbt.vo.AiRingValidBannerVO;

import java.util.List;

/**
 * @Description: ai_ring_banner
 * @Author: jeecg-boot
 * @Date: 2024-10-31
 * @Version: V1.0
 */
public interface IAiRingBannerService extends IService<AiRingBanner> {
    /**
     * 查询有效的banner
     *
     * @return List<AiRingValidBannerVO>
     */
    List<AiRingValidBannerListVO> queryValidBanner(String channelId, String resource);
}
