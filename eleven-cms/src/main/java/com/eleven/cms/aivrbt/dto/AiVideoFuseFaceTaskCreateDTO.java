package com.eleven.cms.aivrbt.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 任务创作-视频换脸
 *
 * <AUTHOR>
 * @datetime 2024/11/4 15:04
 */
@Data
public class AiVideoFuseFaceTaskCreateDTO {

    @NotBlank(message = "手机号不能为空！")
    private String mobile;
    @NotBlank(message = "素材Id不能为空！")
    private String materialId;
    @NotBlank(message = "活动Id不能为空！")
    private String activityId;
    @NotBlank(message = "封面图片不能为空！")
    private String ringPicUrl;
    @NotEmpty
    @Valid
    private List<VideoFaceFusionParam> mergeInfos;

    @Data
    public static class VideoFaceFusionParam {
        @NotBlank(message = "上传人脸图不能为空！")
        private String url;
    }
}
