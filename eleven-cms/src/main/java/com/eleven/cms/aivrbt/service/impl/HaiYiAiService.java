package com.eleven.cms.aivrbt.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eleven.cms.aivrbt.config.HaiYiAIProperties;
import com.eleven.cms.aivrbt.dto.AIStyleTaskCreateDTO;
import com.eleven.cms.aivrbt.dto.FileInfoDTO;
import com.eleven.cms.aivrbt.dto.SameStyleTaskCreateDTO;
import com.eleven.cms.aivrbt.dto.TaskResultQueryVO;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.entity.AiRingImage;
import com.eleven.cms.aivrbt.enums.HaiYiResponseCodeEnum;
import com.eleven.cms.aivrbt.service.IAiRingImageService;
import com.eleven.cms.aivrbt.service.IAiRingService;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.VrbtDiyVideo;
import com.eleven.cms.enums.ReportTypeEnum;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.YycpReportService;
import com.eleven.cms.service.IVrbtDiyVideoService;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HaiYiQueryPicPreLinkVO;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.RING_SETTING_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.RING_SETTING_STATUS_SUCC;

/**
 * <AUTHOR>
 * @datetime 2024/10/8 15:57
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HaiYiAiService {

    private final HaiYiAIProperties properties;

    private final RedisUtil redisUtil;

    private static final String HEADER_AUTHORIZATION = "Authorization";
    public static final String AI_TAG = "AI彩铃创意站";
    public static final String AI_TAG_KEY = "aiRing";
    private final MiguApiService miguApiService;
    private final IAiRingService aiRingService;
    private final AliMediaService aliMediaService;
    private final IAiRingImageService aiRingImageService;
    private final IVrbtDiyVideoService vrbtDiyVideoService;
    private final YycpReportService yycpReportService;

    @Lazy
    @Autowired
    private RabbitTemplate rabbitTemplate;

    private static final OkHttpClient client = OkHttpClientUtils.getIgnoreVerifySSLInstance();

    /**
     * 获取访问凭证
     */
    public String getToken() {
        String key = properties.getRedisKey();
        if (redisUtil.hasKey(key)) {
            return (String) redisUtil.get(key);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("client_id", properties.getClientId());
        map.put("secret", properties.getSecret());

        HttpRequest request = HttpRequest.post(properties.getTokenUrl())
                .body(JacksonUtils.toJson(map));
        HttpResponse response = request.execute();
        String result = response.body();
        JsonNode jsonNode = JacksonUtils.readTree(result);
        if (jsonNode.at("/status/code").intValue() == HaiYiResponseCodeEnum.SUCCESS.getCode()) {
            String token = jsonNode.at("/data/access_token").asText();

            String expiresIn = jsonNode.at("/data/expires_in").asText();
            long expireAt = Long.parseLong(expiresIn);
            long now = Instant.now().getEpochSecond();
            long second = expireAt - now - 60;

            redisUtil.set(key, token, second);
            return token;
        }
        log.error("{}-获取token异常!:请求{},响应:{}", AI_TAG, request, response);
        throw new JeecgBootException(jsonNode.at("/status/msg").asText());
    }

    /**
     * 滤镜任务创作
     *
     * @param createFilterTaskDTO createFilterTaskDTO
     * @return String 任务ID
     */
    public String hyCreateFilterTask(HaiYiFilterTaskCreateDTO createFilterTaskDTO) {
        Map<String, Object> map = new HashMap<>();
        map.put("template_id", createFilterTaskDTO.getHyTemplateId());
        map.put("de_noising_strength_ratio", 50);
        map.put("width", createFilterTaskDTO.getWidth());
        map.put("height", createFilterTaskDTO.getHeight());
        map.put("image_url", createFilterTaskDTO.getImageUrl());
        map.put("prompt", createFilterTaskDTO.getPrompt());

        HttpRequest request = HttpRequest.post(properties.getAiFilterUrl())
                .body(JacksonUtils.toJson(map))
                .header(HEADER_AUTHORIZATION, getToken());
        HttpResponse response = request.execute();
        String result = response.body();
        JsonNode jsonNode = JacksonUtils.readTree(result);
        if (jsonNode.at("/status/code").intValue() == HaiYiResponseCodeEnum.SUCCESS.getCode()) {
            return jsonNode.at("/data/id").textValue();
        }
        log.error("{}-滤镜任务创作异常!,请求:{},响应:{}", AI_TAG, request, response);
        throw new JeecgBootException(jsonNode.at("/status/msg").asText());
    }

    /**
     * 获取图片预签名链接
     *
     * @param picPreLinkQueryDTO picPreLinkQueryDTO
     * @return HaiYiQueryPicPreLinkVO
     */
    public HaiYiQueryPicPreLinkVO queryPicPreLink(HaiYiPicPreLinkQueryDTO picPreLinkQueryDTO) {
        Map<String, Object> map = new HashMap<>();
        map.put("content_type", picPreLinkQueryDTO.getContentType());
        map.put("file_name", picPreLinkQueryDTO.getFileName());
        map.put("file_size", picPreLinkQueryDTO.getFileSize());
        map.put("category", 2);

        HttpRequest request = HttpRequest.post(properties.getPreSignUrl())
                .body(JacksonUtils.toJson(map))
                .header(HEADER_AUTHORIZATION, getToken());
        HttpResponse response = request.execute();
        String result = response.body();
        JsonNode jsonNode = JacksonUtils.readTree(result);
        if (jsonNode.at("/status/code").intValue() == HaiYiResponseCodeEnum.SUCCESS.getCode()) {
            HaiYiQueryPicPreLinkVO item = new HaiYiQueryPicPreLinkVO();
            item.setFileId(jsonNode.at("/data/file_id").textValue());
            item.setPreSign(jsonNode.at("/data/pre_sign").textValue());
            return item;
        }
        log.error("{}-获取图片预签名链接异常!,请求:{},响应:{}", AI_TAG, request, response);
        throw new JeecgBootException(jsonNode.at("/status/msg").asText());
    }

    /**
     * 上传图片内容
     *
     * @param requestUrl  requestUrl
     * @param inputStream inputStream
     * @return String
     */
    public void uploadFileContent(String requestUrl, InputStream inputStream, String imageType) {
        try (InputStream in = inputStream) {
            OkHttpClient client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
            MediaType mediaType = MediaType.parse(imageType);
            RequestBody body = RequestBody.create(mediaType, IOUtils.toByteArray(in));
            Request request = new Request.Builder()
                    .url(requestUrl)
                    .method("PUT", body)
                    .addHeader("Content-Type", imageType)
                    .build();
            Response response = client.newCall(request).execute();
            if (response.code() != HttpURLConnection.HTTP_OK) {
                log.error("{}-上传图片内容异常!,请求:{},响应:{}", AI_TAG, request, response);
            }
        } catch (Exception e) {
            log.error("上传图片内容异常!", e);
            throw new JeecgBootException(e.getMessage());
        }
    }

    /**
     * 确认图片上传
     *
     * @param picUploadConfirmDTO picUploadConfirmDTO
     * @return String 文件地址
     */
    public String confirmPicUpload(HaiYiPicUploadConfirmDTO picUploadConfirmDTO) {
        Map<String, Object> map = new HashMap<>();
        map.put("file_id", picUploadConfirmDTO.getFileId());
        map.put("category", 2);

        HttpRequest request = HttpRequest.post(properties.getPreSignConfirmUrl())
                .body(JacksonUtils.toJson(map))
                .header(HEADER_AUTHORIZATION, getToken());
        HttpResponse response = request.execute();
        String result = response.body();
        JsonNode jsonNode = JacksonUtils.readTree(result);
        if (jsonNode.at("/status/code").intValue() == HaiYiResponseCodeEnum.SUCCESS.getCode()) {
            return jsonNode.at("/data/url").textValue();
        }
        log.error("{}-确认图片上传异常!,请求:{},响应:{}", AI_TAG, request, response);
        throw new JeecgBootException(jsonNode.at("/status/msg").asText());
    }

    /**
     * 图生文
     *
     * @return String 任务ID
     */
    public String imgToText(String imageUrl) {
        Map<String, Object> map = new HashMap<>();
        map.put("image_url", imageUrl);

        HttpRequest request = HttpRequest.post(properties.getImgToTextUrl())
                .body(JacksonUtils.toJson(map))
                .header(HEADER_AUTHORIZATION, getToken());
        HttpResponse response = request.execute();
        String result = response.body();
        JsonNode jsonNode = JacksonUtils.readTree(result);
        if (jsonNode.at("/status/code").intValue() == HaiYiResponseCodeEnum.SUCCESS.getCode()) {
            return jsonNode.at("/data/id").textValue();
        }
        log.error("{}-图生文异常!,请求:{},响应:{}", AI_TAG, request, response);
        throw new JeecgBootException(jsonNode.at("/status/msg").asText());
    }

    /**
     * 任务创作-AI滤镜
     *
     * @param aiStyleTaskCreateDTO aiStyleTaskCreateDTO
     * @return Object
     */
    public Object createAiStyleTask(AIStyleTaskCreateDTO aiStyleTaskCreateDTO) {
        // 1.生成铃音任务
        AiRing aiRing = new AiRing();
        aiRing.setRingName(IdWorker.get32UUID());
        aiRing.setMobile(aiStyleTaskCreateDTO.getMobile());
        aiRing.setTemplateId(aiStyleTaskCreateDTO.getTemplateId());
        aiRing.setRingType(0);
        aiRing.setRingPicUrl(aiStyleTaskCreateDTO.getFileInfoDTOList().get(0).getFileUrl());
        aiRingService.save(aiRing);

        String aiRingId = aiRing.getId();
        aiStyleTaskCreateDTO.setAiRingId(aiRingId);
        try {
            // 2.发送消息到MQ
            rabbitTemplate.convertAndSend(RabbitMQConfig.HY_SEND_SMS_CODE_QUEUE_NAME, aiStyleTaskCreateDTO, new CorrelationData(aiStyleTaskCreateDTO.getAiRingId()));
            log.info("{}-任务创作-AI滤镜消息发送成功,手机号:{}", AI_TAG, aiStyleTaskCreateDTO.getMobile());
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, aiRingId));
            log.error("{}-MQ发送消息异常!,手机号:{}", AI_TAG, aiStyleTaskCreateDTO.getMobile(), e);
        }
        return aiRingId;
    }

    /**
     * 设置铃声
     *
     * @param hyFtpUploadDTO
     */
    public void setRing(HyFtpUploadDTO hyFtpUploadDTO) {
        try {
            AiRing aiRing = aiRingService.getById(hyFtpUploadDTO.getId());
            if (aiRing == null) {
                return;
            }
            String channelId = getChannelId(hyFtpUploadDTO.getChannelId());
            hyFtpUploadDTO.setChannelId(channelId);
            //已设置无需重复上传
            if (StrUtil.isNotBlank(aiRing.getVrbtDiyVideoId())) {
                VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.getOne(Wrappers.<VrbtDiyVideo>lambdaQuery().select(VrbtDiyVideo::getSettingStatus).eq(VrbtDiyVideo::getId, aiRing.getVrbtDiyVideoId()));
                if (vrbtDiyVideo != null && vrbtDiyVideo.getSettingStatus() == 1) {
                    RemoteResult result = miguApiService.vrbtToneFreeMonthOrder(hyFtpUploadDTO.getMobile(), channelId, vrbtDiyVideo.getCopyrightId(), MiguApiService.VRBT_TONE_ORDER_SETFLAG_BOTH_ZBJ);
                    VrbtDiyVideo update = new VrbtDiyVideo();
                    update.setId(vrbtDiyVideo.getId());
                    update.setSettingStatus(result.isOK() ? RING_SETTING_STATUS_SUCC : RING_SETTING_STATUS_FAIL);
                    update.setSettingResult(JacksonUtils.toJson(result));
                    vrbtDiyVideoService.updateById(update);
                    return;
                }
            }


            // 1.生成订单
            VrbtDiyVideo vrbtDiyVideo = new VrbtDiyVideo();
            vrbtDiyVideoService.save(vrbtDiyVideo);
            // 2.海艺主表关联
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getVrbtDiyVideoId, vrbtDiyVideo.getId())
                    .eq(AiRing::getId, hyFtpUploadDTO.getId()));
            // 3.异步上报
            hyFtpUploadDTO.setVrbtDiyVideoId(vrbtDiyVideo.getId());

            rabbitTemplate.convertAndSend(RabbitMQConfig.HY_SET_RING_QUEUE_NAME, hyFtpUploadDTO, new CorrelationData(hyFtpUploadDTO.getId()));
            log.info("{}-设置铃声消息发送成功,手机号:{}", AI_TAG, hyFtpUploadDTO.getMobile());
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, hyFtpUploadDTO.getId()));
            log.error("{}-MQ发送消息异常!,手机号:{}", AI_TAG, hyFtpUploadDTO.getMobile(), e);
        }
    }

    String getChannelId(String channelId) {
        if (StringUtils.isEmpty(channelId)) {
            channelId = MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX;
        }
        return channelId;
    }

    /**
     * 设置铃声消息处理
     *
     * @param hyFtpUploadDTO hyFtpUploadDTO
     */
    public void handleSetRingMQMsg(HyFtpUploadDTO hyFtpUploadDTO) {
        try {
            String id = hyFtpUploadDTO.getId();
            String mobile = hyFtpUploadDTO.getMobile();
            String filePath = hyFtpUploadDTO.getFilePath();
            String mvName = hyFtpUploadDTO.getMvName();

            String channelId = getChannelId(hyFtpUploadDTO.getChannelId());

            DateFormat df = new SimpleDateFormat("yyyyMMdd");
            String pathName = "/hy" + df.format(new Date());
            String fileName = "video" + IdWorker.getId() + ".mp4";
            boolean flag = vrbtDiyVideoService.uploadVideoToFtp(pathName, filePath, fileName);
            if (flag) {
                ReportDTO reportDTO = new ReportDTO();
                reportDTO.setPhone(mobile);
                reportDTO.setMvUrl(pathName + "/" + fileName);
                reportDTO.setMvName(mvName);
                reportDTO.setTransactionId(ReportTypeEnum.HY.getCode() + IdWorker.getId());
                reportDTO.setId(id);
                reportDTO.setChannelCode(channelId);
                yycpReportService.report(reportDTO, hyFtpUploadDTO.getVrbtDiyVideoId());
            }
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, hyFtpUploadDTO.getId()));
            log.error("{}-消息处理异常!,手机号:{}", AI_TAG, hyFtpUploadDTO.getMobile(), e);
        }
    }

    /**
     * 消息处理
     *
     * @param aiStyleTaskCreateDTO aiStyleTaskCreateDTO
     */
    public void handleMQMsg(AIStyleTaskCreateDTO aiStyleTaskCreateDTO) {
        try {
            List<FileInfoDTO> fileInfoDTOList = aiStyleTaskCreateDTO.getFileInfoDTOList();
            // 默认转换一张
            FileInfoDTO fileInfoDTO = fileInfoDTOList.get(0);
            // 1.上传图片到海艺
            String hyFileUrl = uploadFileToHaiYi(fileInfoDTO);
            // 2.图生文任务
            String imgToTextTaskId = imgToText(hyFileUrl);
            log.info("---------图生文任务成功，上传图片到海艺成功");
            // 3.保存图片创作记录
            AiRingImage aiRingImage = new AiRingImage();
            aiRingImage.setMobile(aiStyleTaskCreateDTO.getMobile());
            aiRingImage.setFileName(fileInfoDTO.getFileName());
            aiRingImage.setAiRingId(aiStyleTaskCreateDTO.getAiRingId());
            aiRingImage.setHyTemplateId(aiStyleTaskCreateDTO.getHyTemplateId());
            aiRingImage.setImgToTextTaskId(imgToTextTaskId);
            aiRingImage.setSourceImageAliUrl(fileInfoDTO.getFileUrl());
            aiRingImage.setSourceImageHyUrl(hyFileUrl);
            aiRingImageService.save(aiRingImage);
        } catch (Exception e) {
            aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                    .set(AiRing::getRingMakeStatus, -1)
                    .eq(AiRing::getId, aiStyleTaskCreateDTO.getAiRingId()));
            log.error("{}-消息处理异常!,手机号:{}", AI_TAG, aiStyleTaskCreateDTO.getMobile(), e);
        }
    }

    /**
     * 上传图片到海艺
     *
     * @param fileInfoDTO fileInfoDTO
     * @return String 文件地址
     */
    private String uploadFileToHaiYi(FileInfoDTO fileInfoDTO) {
        // 1.获取图片预上传链接
        HaiYiPicPreLinkQueryDTO picPreLinkQueryDTO = new HaiYiPicPreLinkQueryDTO();
        picPreLinkQueryDTO.setFileName(fileInfoDTO.getFileName());
        picPreLinkQueryDTO.setFileSize(fileInfoDTO.getFileSize());
        picPreLinkQueryDTO.setContentType(fileInfoDTO.getContentType());
        HaiYiQueryPicPreLinkVO haiYiQueryPicPreLinkVO = queryPicPreLink(picPreLinkQueryDTO);
        // 2.上传图片内容
        uploadFileContent(haiYiQueryPicPreLinkVO.getPreSign(), getInputStream(fileInfoDTO.getFileUrl()), fileInfoDTO.getContentType());
        // 3.确认图片上传
        HaiYiPicUploadConfirmDTO picUploadConfirmDTO = new HaiYiPicUploadConfirmDTO();
        picUploadConfirmDTO.setFileId(haiYiQueryPicPreLinkVO.getFileId());
        return confirmPicUpload(picUploadConfirmDTO);
    }

    /**
     * 任务创作-拍同款
     *
     * @param sameStyleTaskCreateDTO sameStyleTaskCreateDTO
     * @return
     */
    public Object createSameStyleTask(SameStyleTaskCreateDTO sameStyleTaskCreateDTO) {
        // 1.生成铃音任务
        AiRing aiRing = new AiRing();
        aiRing.setRingName(IdWorker.get32UUID());
        aiRing.setMobile(sameStyleTaskCreateDTO.getMobile());
        aiRing.setTemplateId(sameStyleTaskCreateDTO.getTemplateId());
        aiRing.setRingType(1);
        aiRing.setRingPicUrl(sameStyleTaskCreateDTO.getRingPicUrl());
        aiRingService.save(aiRing);
        // 2.合成视频
        String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_HYAI, sameStyleTaskCreateDTO.getTemplateId(), sameStyleTaskCreateDTO.getClipsParam());
        // 3.通过铃音ID更新JobId
        aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                .set(AiRing::getJobId, jobId)
                .eq(AiRing::getId, aiRing.getId()));
        return aiRing.getId();
    }

    /**
     * 查询任务创作结果
     *
     * @param taskId taskId
     * @return Object
     */
    public Object queryTaskResult(String taskId) {
        AiRing one = aiRingService.getOne(new LambdaQueryWrapper<AiRing>()
                .eq(AiRing::getId, taskId));
        if (one != null) {
            TaskResultQueryVO taskResultQueryVO = new TaskResultQueryVO();
            taskResultQueryVO.setId(one.getId());
            taskResultQueryVO.setRingName(one.getRingName());
            taskResultQueryVO.setRingUrl(one.getRingUrl());
            taskResultQueryVO.setRingMakeStatus(one.getRingMakeStatus());
            taskResultQueryVO.setRingMakeStatus(one.getRingMakeStatus());
            return taskResultQueryVO;
        }
        return null;
    }

    /**
     * 获取流
     *
     * @param requestUrl requestUrl
     * @return InputStream
     */
    public InputStream getInputStream(String requestUrl) {
        Request request = new Request.Builder().url(requestUrl).build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().byteStream();
            }
            throw new JeecgBootException(response.body().toString());
        } catch (Exception e) {
            log.error("{}-获取流异常!,请求地址:{}", AI_TAG, requestUrl, e);
            throw new JeecgBootException(e);
        }
    }

    /**
     * 查询是否开通AI业务
     *
     * @param mobile  mobile
     * @param channel
     * @return Result<Object>
     */
    public Result<Object> queryBusinessOpenStatus(String mobile, String channel) {
        if (redisUtil.hasKey(String.format("%s:freeCheck:%s", AI_TAG_KEY, mobile))) {
            return Result.ok();
        }
        RemoteResult remoteResult = miguApiService.schQuery(mobile, channel);

        if (MiguApiService.AI_RING_SUB_CHIANNEL.equals(channel)) {
            if (remoteResult.isSubMember()) {
                return Result.ok();
            } else {
                return Result.error(10000, "请先开通业务！");
            }
        }
        if (remoteResult.isSchMember()) {
            return Result.ok();
        }
        return Result.error(10000, "请先开通业务！");
    }

    /**
     * 删除我的作品
     *
     * @param id id
     * @return Object
     */
    public Object deleteMyCreation(String id) {
        aiRingService.update(new LambdaUpdateWrapper<AiRing>()
                .set(AiRing::getIsDeleted, 1)
                .eq(AiRing::getId, id));
        return true;
    }
}
