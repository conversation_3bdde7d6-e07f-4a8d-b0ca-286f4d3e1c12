package com.eleven.cms.aivrbt.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.dto.FuseConfigReq;
import com.eleven.cms.aivrbt.entity.MiniAppFuseConfig;
import com.eleven.cms.aivrbt.entity.MiniAppFuseConfigItem;
import com.eleven.cms.aivrbt.service.IMiniAppFuseConfigItemService;
import com.eleven.cms.aivrbt.service.IMiniAppFuseConfigService;
import com.eleven.cms.aivrbt.vo.SuitableChannelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static com.eleven.cms.aivrbt.service.impl.HaiYiAiService.AI_TAG;

/**
 * @Description: mini_app_fuse_config
 * @Author: jeecg-boot
 * @Date: 2025-06-23
 * @Version: V1.0
 */
@Api(tags = "mini_app_fuse_config")
@RestController
@RequestMapping("/miniApi/miniAppFuseConfig")
@Slf4j
public class MiniAppFuseConfigController extends JeecgController<MiniAppFuseConfig, IMiniAppFuseConfigService> {
    @Autowired
    private IMiniAppFuseConfigService miniAppFuseConfigService;

    @Resource
    private IMiniAppFuseConfigItemService miniAppFuseConfigItemService;

    /**
     * 分页列表查询
     *
     * @param miniAppFuseConfig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "mini_app_fuse_config-分页列表查询", notes = "mini_app_fuse_config-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppFuseConfig miniAppFuseConfig,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        QueryWrapper<MiniAppFuseConfig> queryWrapper = QueryGenerator.initQueryWrapper(miniAppFuseConfig, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        queryWrapper.eq("is_deleted", 0);
        Page<MiniAppFuseConfig> page = new Page<MiniAppFuseConfig>(pageNo, pageSize);
        IPage<MiniAppFuseConfig> pageList = miniAppFuseConfigService.page(page, queryWrapper);
        if (pageList.getRecords() != null && !pageList.getRecords().isEmpty()) {
            pageList.getRecords().forEach(item -> {
                List<MiniAppFuseConfigItem> fuseConfigItemList = miniAppFuseConfigItemService.lambdaQuery().eq(MiniAppFuseConfigItem::getFuseConfigId, item.getId()).list();
                item.setFuseConfigItemList(fuseConfigItemList);
            });
        }
        return Result.ok(pageList);
    }


    @ApiOperation(value = "mini_app_fuse_config-添加", notes = "mini_app_fuse_config-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody FuseConfigReq req) {
        miniAppFuseConfigService.saveConfig(req);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miniAppFuseConfig
     * @return
     */
    @ApiOperation(value = "mini_app_fuse_config-编辑", notes = "mini_app_fuse_config-编辑")
    @PostMapping(value = "/edit")
    public Result<?> edit(@RequestBody FuseConfigReq miniAppFuseConfig) {
        miniAppFuseConfigService.editConfig(miniAppFuseConfig);
        return Result.ok("编辑成功!");
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "mini_app_fuse_config-通过id删除", notes = "mini_app_fuse_config-通过id删除")
    @GetMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniAppFuseConfigService.lambdaUpdate().set(MiniAppFuseConfig::getIsDeleted, 1).eq(MiniAppFuseConfig::getId, id).update();
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "mini_app_fuse_config-批量删除", notes = "mini_app_fuse_config-批量删除")
    @GetMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        if (CollectionUtil.isEmpty(idList)) {
            return Result.error("参数ids不能为空");
        }
        miniAppFuseConfigService.lambdaUpdate().set(MiniAppFuseConfig::getIsDeleted, 1).in(MiniAppFuseConfig::getId, idList).update();
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_fuse_config-通过id查询")
    @ApiOperation(value = "mini_app_fuse_config-通过id查询", notes = "mini_app_fuse_config-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppFuseConfig miniAppFuseConfig = miniAppFuseConfigService.getById(id);
        if (miniAppFuseConfig == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppFuseConfig);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppFuseConfig
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniAppFuseConfig miniAppFuseConfig) {
        return super.exportXls(request, miniAppFuseConfig, MiniAppFuseConfig.class, "mini_app_fuse_config");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppFuseConfig.class);
    }

}
