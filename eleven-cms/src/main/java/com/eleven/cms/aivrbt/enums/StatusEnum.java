package com.eleven.cms.aivrbt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 是否有效
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {
    VALID(1, "有效"),
    INVALID(0, "失效"),
    ;
    private final int code;

    private final String desc;

    public static StatusEnum getByCode(Integer code) {
        for (StatusEnum adType : values()) {
            if (Objects.equals(adType.code, code)) {
                return adType;
            }
        }
        return null;
    }
}
