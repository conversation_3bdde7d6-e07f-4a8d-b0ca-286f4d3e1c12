package com.eleven.cms.aivrbt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: openApi_album_info
 * @Author: jeecg-boot
 * @Date: 2025-05-23
 * @Version: V1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MiniAppCatalogResMiniAppMiniDramaVO extends CatalogResWarehouseBaseVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private String id;
    /**
     * 栏目id
     */
    private String columnId;
    /**
     * 剧集名称
     */
    private String name;
    /**
     * 状态 0未生效  1:已生效
     */
    private Integer status;
    /**
     * 短剧更新状态 - 1：未上映，- 2：更新中， - 3：已完结
     */
    private Integer albumStatus;
    /**
     * 短剧信息主键
     */
    private String albumInfoId;
    /**
     * 抖音开放平台短剧id
     */
    private String albumId;
    /**
     * 短剧视频地址
     */
    private String videoUrl;
    /**
     * 集号
     */
    private Integer seq;
    /**
     * 是否付费 0:免费  1:付费
     */
    private Integer payflag;
    /**
     * 总集数
     */
    private Integer seqNum;
    /**
     * 短剧类目标签（1-3 个）
     */
    private String tagList;
    /**
     * 封面图列表
     */
    private String coverList;

    private String coverUrl;
    /**
     * 排序编码 正序
     */
    private Integer orderNo;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    private Integer isDeleted;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

    private Integer reviewStatus;

    private Integer onlineStatus;
}
