package com.eleven.cms.aivrbt.utils;

import java.util.regex.Pattern;

public class PatternUtil {

    private static final String SQL_INJECTION_REGEX = "('|--|;|/\\*|\\*/|OR\\s+1=1|(?i)AND|(?i)SELECT|(?i)UNION)";


    public static Boolean isNumber(String number){
        String regex = "^[1-9]\\d*$";
        // 编译正则表达式，提高匹配效率
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(number).matches();
    }

    public static Boolean hasInjectionSql(String str){
        // 编译正则表达式，提高匹配效率
        Pattern pattern = Pattern.compile(SQL_INJECTION_REGEX);
        return pattern.matcher(str).find();
    }

    public static void main(String[] args) {
        System.out.println(hasInjectionSql("1894363644"));
    }


}
