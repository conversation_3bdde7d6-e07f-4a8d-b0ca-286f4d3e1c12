package com.eleven.cms.aivrbt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 小程序app渠道枚举
 * <AUTHOR>
 * @datetime 2025/2/27
 */
@Getter
@AllArgsConstructor
public enum MiniAppChannelEnum {
    DY_RINGTONE_CREATIVE_STATION("dyAivrbtMiniApp_1", "彩铃创意站","DY"),
    DY_RINGTONE_WORKSHOP("dyAivrbtMiniApp_2", "彩铃工坊","DY"),
    DY_COOL_INCOMING_CALL("dyAivrbtMiniApp_3", "炫酷来电秀","DY"),
    DY_CALLER_SHOW("dyAivrbtMiniApp_4", "来电秀秀","DY"),
    RANDOM_RINGTONE_STATION("dyAivrbtMiniApp_5", "随心铃声站","DY"),
    MUSIC_RINGTONE_WORKSHOP("dyAivrbtMiniApp_6", "乐铃工坊","DY"),
    RINGTONE_BOX("dyAivrbtMiniApp_7", "铃声盒子","DY"),
    VOICE_RINGTONE("dyAivrbtMiniApp_8", "声动彩铃","DY"),
    ENJOY_LISTENING_RINGTONE("dyAivrbtMiniApp_9", "悦听铃声","DY"),
    CALL_SHOW("dyAivrbtMiniApp_10", "来电秀场","DY"),
    RINGTONE_ELF("dyAivrbtMiniApp_11", "彩铃精灵","DY"),
    DYNAMIC_RINGTONE_SHOW("dyAivrbtMiniApp_12", "动感彩铃秀","DY"),
    RINGTONE_MAKER("dyAivrbtMiniApp_13", "炫铃制造机","DY"),
    RINGTONE_COLOR_BOX("dyAivrbtMiniApp_14", "彩铃盒子","DY"),
    MIGU_RINGTONE("dyAivrbtMiniApp_15", "咪咕彩铃","DY"),
    COOL_INCOMING_CALL("wxAivrbtMiniApp_1","酷炫来电","WX"),
    AMUSING_INCOMING_CALL("wxAivrbtMiniApp_2","趣味来电","WX"),
    COOL_INCOMING_CALL_WX_MINI_APP("coolIncomingCallWxMiniApp", "炫酷来电微信小程序", "WX"),
    MGAISPCL("mgaispcl","咪咕ai视频彩铃","DY"),
    MGSPCL("mgspcl", "咪咕ai视频彩铃", "DY"),
    LYHZ("lyhz","铃音盒子","DY"),
    CYHZ("clhz", "彩铃盒子", "DY"),
    CLGF("clgf", "彩铃工坊", "DY"),
    COOL_INCOMING_CALL_OFFICIAL_ACCOUNT("coolIncomingCall-wxOfficialAccount", "炫酷彩铃秀公众号", "WX"),
    DY_SHORT_PLAY_VIDEO("dy_short_play_miniApp", "抖音短剧小程序", "DY"),

    ;
    private final String code;

    private final String desc;

    private final String source;

    public static MiniAppChannelEnum getWxByCode(String code){
        List<MiniAppChannelEnum> list = Arrays.stream(MiniAppChannelEnum.values()).filter(x -> Objects.equals(x.getSource(), "WX")).collect(Collectors.toList());
        for (MiniAppChannelEnum appChannelEnum:list) {
            if(Objects.equals(code,appChannelEnum.getCode())){
                return appChannelEnum;
            }
        }
        return null;
    }

    public static MiniAppChannelEnum getDyByCode(String code){
        List<MiniAppChannelEnum> list = Arrays.stream(MiniAppChannelEnum.values()).filter(x -> Objects.equals(x.getSource(), "DY")).collect(Collectors.toList());
        for (MiniAppChannelEnum appChannelEnum:list) {
            if(Objects.equals(code,appChannelEnum.getCode())){
                return appChannelEnum;
            }
        }
        return null;
    }

    public static MiniAppChannelEnum getByCode(String code){
        for (MiniAppChannelEnum appChannelEnum:MiniAppChannelEnum.values()) {
            if(Objects.equals(code,appChannelEnum.getCode())){
                return appChannelEnum;
            }
        }
        return null;
    }
}
