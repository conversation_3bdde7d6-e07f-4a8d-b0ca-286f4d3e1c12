package com.eleven.cms.aivrbt.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 任务创作-图片换脸
 *
 * <AUTHOR>
 * @datetime 2024/11/4 15:04
 */

@Data
public class AiPicFuseFaceTaskCreateDTO {
    @NotBlank(message = "id不能为空！")
    private String id;
    @NotBlank(message = "手机号不能为空！")
    private String mobile;
    @NotBlank(message = "活动Id不能为空！")
    private String activityId;
    @NotBlank(message = "素材Id不能为空！")
    private String materialId;
    @NotBlank(message = "模版Id不能为空！")
    private String templateId;
    @NotBlank(message = "彩铃封面图片不能为空！")
    private String ringPicUrl;
    @NotEmpty(message = "上传人脸图不能为空！")
    @Valid
    private List<PicFuseFaceParam> mergeInfos;

    private String aiRingId;

    private String clipsParam;

    private String channelId;

    @Data
    public static class PicFuseFaceParam {
        @NotBlank(message = "上传图片不能为空！")
        private String url;
        @NotBlank(message = "人脸框Id不能为空！")
        private String templateFaceId;

    }
}
