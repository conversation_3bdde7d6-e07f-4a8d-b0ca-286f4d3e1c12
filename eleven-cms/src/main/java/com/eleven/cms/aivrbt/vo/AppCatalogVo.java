package com.eleven.cms.aivrbt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class AppCatalogVo {

    private String id;
    /**
     * 上级栏目id，0=最顶级栏目
     */
    private String pid;
    /**
     * 渠道id，区分哪个app
     */
    private String channelId;
    private String channelIdText;
    /**
     * 样式id
     */
    private String styleCode;
    private Integer styleType;

    /**
     * 栏目名称
     */
    private String name;
    /**
     * 下级资源类型 1=栏目资源， 2=桌面组件资源，3=静态壁纸资源，4=动态壁纸资源，
     */
    private Integer subResourceType;
    private String subResourceTypeText;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderNum;

    /**
     * 图地址
     */
    @Excel(name = "图地址", width = 15)
    @ApiModelProperty(value = "图地址")
    private String coverUrl;
    /**
     * 跳转地址
     */
    @Excel(name = "跳转地址", width = 15)
    @ApiModelProperty(value = "跳转地址")
    private String jumpUrl;
    /**
     * banner跳转类型
     */
    @Excel(name = "banner跳转类型", width = 15)
    @ApiModelProperty(value = "banner跳转类型")
    private Integer jumpType;
    /**
     * 0:无效 1:有效
     */
    @Excel(name = "0:无效 1:有效", width = 15)
    @ApiModelProperty(value = "0:无效 1:有效")
    private Integer status;
    private String statusText;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
