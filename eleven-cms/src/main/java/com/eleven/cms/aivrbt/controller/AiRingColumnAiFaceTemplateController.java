package com.eleven.cms.aivrbt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiFaceTemplate;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiFaceTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: ai_ring_column_ai_face_template
 * @Author: jeecg-boot
 * @Date:   2024-11-11
 * @Version: V1.0
 */
@Api(tags="ai_ring_column_ai_face_template")
@RestController
@RequestMapping("/cms/aiRingColumnAiFaceTemplate")
@Slf4j
public class AiRingColumnAiFaceTemplateController extends JeecgController<AiRingColumnAiFaceTemplate, IAiRingColumnAiFaceTemplateService> {
	@Autowired
	private IAiRingColumnAiFaceTemplateService aiRingColumnAiFaceTemplateService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aiRingColumnAiFaceTemplate
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_ai_face_template-分页列表查询")
	@ApiOperation(value="ai_ring_column_ai_face_template-分页列表查询", notes="ai_ring_column_ai_face_template-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AiRingColumnAiFaceTemplate aiRingColumnAiFaceTemplate,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AiRingColumnAiFaceTemplate> queryWrapper = QueryGenerator.initQueryWrapper(aiRingColumnAiFaceTemplate, req.getParameterMap());
		Page<AiRingColumnAiFaceTemplate> page = new Page<AiRingColumnAiFaceTemplate>(pageNo, pageSize);
		IPage<AiRingColumnAiFaceTemplate> pageList = aiRingColumnAiFaceTemplateService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aiRingColumnAiFaceTemplate
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_ai_face_template-添加")
	@ApiOperation(value="ai_ring_column_ai_face_template-添加", notes="ai_ring_column_ai_face_template-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AiRingColumnAiFaceTemplate aiRingColumnAiFaceTemplate) {
		aiRingColumnAiFaceTemplateService.save(aiRingColumnAiFaceTemplate);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aiRingColumnAiFaceTemplate
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_ai_face_template-编辑")
	@ApiOperation(value="ai_ring_column_ai_face_template-编辑", notes="ai_ring_column_ai_face_template-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AiRingColumnAiFaceTemplate aiRingColumnAiFaceTemplate) {
		aiRingColumnAiFaceTemplateService.updateById(aiRingColumnAiFaceTemplate);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_ai_face_template-通过id删除")
	@ApiOperation(value="ai_ring_column_ai_face_template-通过id删除", notes="ai_ring_column_ai_face_template-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aiRingColumnAiFaceTemplateService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_ai_face_template-批量删除")
	@ApiOperation(value="ai_ring_column_ai_face_template-批量删除", notes="ai_ring_column_ai_face_template-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aiRingColumnAiFaceTemplateService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_ring_column_ai_face_template-通过id查询")
	@ApiOperation(value="ai_ring_column_ai_face_template-通过id查询", notes="ai_ring_column_ai_face_template-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AiRingColumnAiFaceTemplate aiRingColumnAiFaceTemplate = aiRingColumnAiFaceTemplateService.getById(id);
		if(aiRingColumnAiFaceTemplate==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aiRingColumnAiFaceTemplate);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aiRingColumnAiFaceTemplate
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AiRingColumnAiFaceTemplate aiRingColumnAiFaceTemplate) {
        return super.exportXls(request, aiRingColumnAiFaceTemplate, AiRingColumnAiFaceTemplate.class, "ai_ring_column_ai_face_template");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiRingColumnAiFaceTemplate.class);
    }

}
