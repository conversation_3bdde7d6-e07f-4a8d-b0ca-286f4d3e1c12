package com.eleven.cms.aivrbt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ai_ring_column
 * @Author: jeecg-boot
 * @Date:   2024-10-23
 * @Version: V1.0
 */
@Data
@TableName("ai_ring_column")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ai_ring_column对象", description="ai_ring_column")
public class AiRingColumn implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**栏目分类名称*/
	@Excel(name = "栏目分类名称", width = 15)
    @ApiModelProperty(value = "栏目分类名称")
    private String columnName;
	/**栏目类型，1=AI栏目,2=拍同款栏目,3=铃音视频栏目*/
	@Excel(name = "栏目类型，1=AI栏目,2=拍同款栏目,3=铃音视频栏目", width = 15)
    @ApiModelProperty(value = "栏目类型，1=AI栏目,2=拍同款栏目,3=铃音视频栏目")
    @Dict(dicCode = "ai_clcyz_column_type")
    private Integer type;
    /**是否首页展示 0-否 1-是*/
    @Excel(name = "是否首页展示 0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否首页展示 0-否 1-是")
    @Dict(dicCode = "ai_clcyz_yn_code")
    private Integer isHomepageDisplay;
    /**栏目展示风格类型*/
    @Excel(name = "栏目展示风格类型", width = 15)
    @ApiModelProperty(value = "栏目展示风格类型")
    @Dict(dicCode = "ai_clcyz_style_type")
    private Integer displayStyleType;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderBy;
	/**背景图地址*/
	@Excel(name = "背景图地址", width = 15)
    @ApiModelProperty(value = "背景图地址")
    private String backgroundUrl;
	/**状态：1=开启，0=关闭*/
	@Excel(name = "状态：1=开启，0=关闭", width = 15)
    @ApiModelProperty(value = "状态：1=开启，0=关闭")
    @Dict(dicCode = "ai_clcyz_column_status")
    private Integer status;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    @Excel(name = "可用渠道号", width = 15)
    @ApiModelProperty(value = "可用渠道号")
    @Dict(dicCode = "available_channel")
    private String availableChannel;
    private String availableMiniApp;
}
