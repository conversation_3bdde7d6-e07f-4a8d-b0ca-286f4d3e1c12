package com.eleven.cms.aivrbt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiTemplate;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryChildVO;
import com.eleven.cms.aivrbt.vo.AIColumnAndTemplateQueryVO;

import java.util.List;

/**
 * @Description: ai_ring_column_ai_template
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
public interface IAiRingColumnAiTemplateService extends IService<AiRingColumnAiTemplate> {

    /**
     * 分页列表查询
     *
     * @param page page
     * @param aiRingColumnAiTemplate aiRingColumnAiTemplate
     * @return IPage<AiRingColumnAiTemplate>
     */
    IPage<AiRingColumnAiTemplate> queryPageList(Page<AiRingColumnAiTemplate> page, AiRingColumnAiTemplate aiRingColumnAiTemplate);
    /**
     * AI模板页-通过栏目查询模板
     *
     * @param columnId columnId
     * @return List<AIColumnAndTemplateQueryChildVO>
     */
    IPage<AIColumnAndTemplateQueryChildVO> pageAIColumnTemplateByColumnId(Page<AIColumnAndTemplateQueryChildVO> page, String columnId);
}
