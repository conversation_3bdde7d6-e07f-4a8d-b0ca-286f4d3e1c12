package com.eleven.cms.aivrbt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxMiniGetUserInfoVO {

    private String openId;

    private String phoneNumber;

    private String appUid;

    /**
     * 开通会员时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date membershipStartTime;
    /**
     * 会员状态
     */
    private Integer membershipStatus;

    /**
     * 会员类型0-订阅会员 1-付费会员
     */
    @ApiModelProperty(value = "使用成本")
    private Integer membershipType;
    /**
     * 会员到期时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date membershipExpiryTime;

    private String token;
}
