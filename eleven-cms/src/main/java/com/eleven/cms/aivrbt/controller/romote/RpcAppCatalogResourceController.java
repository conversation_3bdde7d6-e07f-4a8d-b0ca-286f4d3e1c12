package com.eleven.cms.aivrbt.controller.romote;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.dto.AppCatalogResWarehouseDTO;
import com.eleven.cms.aivrbt.dto.AppCatalogResourceAddDTO;
import com.eleven.cms.aivrbt.dto.AppCatalogResourceListReq;
import com.eleven.cms.aivrbt.dto.AppCatalogResourceUpdateDTO;
import com.eleven.cms.aivrbt.entity.MiniAppCatalogResource;
import com.eleven.cms.aivrbt.service.IAppCatalogResourceService;
import com.eleven.cms.aivrbt.vo.CatalogResWarehouseBaseVO;
import com.eleven.cms.aivrbt.vo.ManageCatalogResourceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: app_catalog_resource
 * @Author: jeecg-boot
 * @Date: 2025-03-13
 * @Version: V1.0
 */
@Api(tags = "app_catalog_resource")
@RestController
@RequestMapping("/api/rpc/cms/manage/catalog/resource")
@Slf4j
public class RpcAppCatalogResourceController extends JeecgController<MiniAppCatalogResource, IAppCatalogResourceService> {
    @Autowired
    private IAppCatalogResourceService appCatalogResourceService;

    /**
     * 分页列表查询
     *
     * @param listReq
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(@Validated AppCatalogResourceListReq listReq,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<MiniAppCatalogResource> page = new Page<MiniAppCatalogResource>(pageNo, pageSize);
        IPage<ManageCatalogResourceVO> pageList = appCatalogResourceService.pageVo(page, listReq);
        return Result.ok(pageList);
    }

    /**
     * 资源仓库列表
     *
     * @param appCatalogResource
     * @param
     * @return
     */
    @GetMapping(value = "/warehouse/list")
    public Result<?> queryWarehousePageList(@Validated AppCatalogResWarehouseDTO appCatalogResource) {
        if (StringUtils.isNotEmpty(appCatalogResource.getResCategoryIdsStr())) {
            appCatalogResource.setResCategoryIds(Arrays.asList(appCatalogResource.getResCategoryIdsStr().split(",")));
        }
        IPage<CatalogResWarehouseBaseVO> pageList = appCatalogResourceService.warehousePage(appCatalogResource);
        return Result.ok(pageList);
    }

    /**
     * 资源仓库分类列表
     *
     * @param
     * @param
     * @return
     */
    @GetMapping(value = "/warehouse/category/list")
    public Result<?> queryWarehouseCategoryList(@RequestParam("resType") Integer resType) {
        return appCatalogResourceService.listResWarehouseCategory(resType);
    }

    /**
     * 添加
     *
     * @param appCatalogResource
     * @return
     */
    @AutoLog(value = "app_catalog_resource-添加")
    @ApiOperation(value = "app_catalog_resource-添加", notes = "app_catalog_resource-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody AppCatalogResourceAddDTO appCatalogResource) {

        return appCatalogResourceService.saveResource(appCatalogResource);
    }

    /**
     * 编辑
     *
     * @param appCatalogResource
     * @return
     */
    @AutoLog(value = "app_catalog_resource-编辑")
    @ApiOperation(value = "app_catalog_resource-编辑", notes = "app_catalog_resource-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody AppCatalogResourceUpdateDTO appCatalogResource) {
        appCatalogResourceService.updateResource(appCatalogResource);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "app_catalog_resource-通过id删除")
    @ApiOperation(value = "app_catalog_resource-通过id删除", notes = "app_catalog_resource-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        MiniAppCatalogResource miniAppCatalogResource = appCatalogResourceService.getById(id);
        appCatalogResourceService.removeById(id);
        appCatalogResourceService.deleteCache(miniAppCatalogResource.getChannelId(), miniAppCatalogResource.getPid());
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "app_catalog_resource-批量删除")
    @ApiOperation(value = "app_catalog_resource-批量删除", notes = "app_catalog_resource-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idsStr = Arrays.asList(ids.split(","));
        if (!idsStr.isEmpty()) {
            MiniAppCatalogResource miniAppCatalogResource = appCatalogResourceService.getById(idsStr.get(0));
            appCatalogResourceService.deleteCache(miniAppCatalogResource.getChannelId(), miniAppCatalogResource.getPid());
        }

        this.appCatalogResourceService.removeByIds(idsStr);
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "app_catalog_resource-通过id查询")
    @ApiOperation(value = "app_catalog_resource-通过id查询", notes = "app_catalog_resource-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppCatalogResource miniAppCatalogResource = appCatalogResourceService.getById(id);
        if (miniAppCatalogResource == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppCatalogResource);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppCatalogResource
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniAppCatalogResource miniAppCatalogResource) {
        return super.exportXls(request, miniAppCatalogResource, MiniAppCatalogResource.class, "app_catalog_resource");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppCatalogResource.class);
    }

}
