package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@Slf4j
public class WoReadProperties {
  /*获取token接口路径*/
  private String tokenUrl;
  /*客户端Id；业务对接人申请*/
  private String clientId;
  /*客户端密码；业务对接人申请*/
  private String clientSecret;
  /*微信预订购接口路径*/
  private String wxBeforeorderUrl;
  /*支付宝预订购接口路径*/
  private String aliBeforeorderUrl;
  /*获取用户包月产品的订购状态接口路径*/
  private String pkgOrderedStatusUrl;
  /*包月退订接口路径*/
  private String unsubscribeUrl;
  /*必传，套餐索引*/
  private String productpkgid;
  private WoReadWxProperties wx;
  private WoReadAliProperties ali;
  /*一键登录url*/
  private String shouTingLoginUrl;

}
