package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2023-7-14 10:44:54
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "hunan.yidong")
@Slf4j
public class HunanYidongProperties {

    private String baseUrl;
    private String appId;
    private String provinceCode;
    private String inModeCode;
    private String tradeEparchyCode;
    private String tradeCityCode;
    private String tradeDepartId;
    private String tradeStaffId;
    private String tradeDepartPasswd;
    private String tradeTypeCode;
    private String key;
    private String tradeTerminalId;

    //渠道业务代码配置
    private Map<String, String> channelConfig = new HashMap<>();


    //根据渠道号获取业务代码
    public String getBizCodeByChannel(String channel) {
        String bizCode = channelConfig.get(channel);
        if (StringUtils.isEmpty(bizCode)) {
            log.error("渠道号:{}未找到相关业务代码", channel);
            throw new JeecgBootException("无效的湖南移动渠道号");
        }
        return bizCode;
    }
}

