package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 南山途牛权益配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/29 11:05
 **/
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "nanshan-lianlian")
@Slf4j
public class NanshanLianlianProperties {
    //南山途牛权益请求证书私钥
    private String privateKey;
    //南山途牛权益请求证书公钥
    private String publicKey;
    //南山途牛权益回调证书私钥
    private String callbackPrivateKey;
    //南山途牛权益回调证书公钥
    private String callbackPublicKey;
    //南山途牛权益appKey
    private String appKey;
    //南山途牛权益回调加密key
    private String callbackKey;
    //南山途牛权益通知地址
    private String callbackUrl;
}
