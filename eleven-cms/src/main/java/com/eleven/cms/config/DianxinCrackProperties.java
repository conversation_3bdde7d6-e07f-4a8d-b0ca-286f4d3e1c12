package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:电信PJ配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "dianxin.crack")
public class DianxinCrackProperties {

    private String getSmsUrl;
    private String smsCodeUrl;
    private String a;
    private String logTag;

}
