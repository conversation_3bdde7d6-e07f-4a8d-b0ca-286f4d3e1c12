package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.city")
@Slf4j
public class HebeiYidongCityVrbtProperties {

    //城市彩铃配置
    private Map<String, HebeiYidongCityVrbtConfig> cityConfigMap = new HashMap<>();

    //根据城市获取电信视频彩铃配置
    public HebeiYidongCityVrbtConfig getHebeiYidongCityVrbtConfig(String city) {
        HebeiYidongCityVrbtConfig hebeiYidongCityVrbtConfig = cityConfigMap.get(city);
        if (hebeiYidongCityVrbtConfig == null) {
            log.error("城市:{}未找到河北视频彩铃相关配置", city);
            throw new JeecgBootException("无效的河北移动城市");
        }
        return hebeiYidongCityVrbtConfig;
    }
}

