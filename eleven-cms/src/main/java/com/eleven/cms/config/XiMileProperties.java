package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 西米乐权益充值配置参数
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 15:27
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "ximile",ignoreUnknownFields = true)
public class XiMileProperties {
    private String userId;
    private String key;
    private String rechargeUrl;
    private String queryOrderUrl;
    private String queryBalanceUrl;
    private String version;
}
