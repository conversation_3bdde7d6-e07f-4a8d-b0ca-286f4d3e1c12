package com.eleven.cms.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2023-09-25 10:26
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "channelowner")
public class ChannelOwnerProperties {
    private String queryUrl;
    //渠道下发短信手机号配置
    private Map<String, List<String>> mobileListMap = new HashMap<>();

    public List<String> getMobiles(String owner) {
        if(StringUtils.isNotBlank(owner)) {
            List<String> mobileList = mobileListMap.get(owner);
            return mobileList;
        }else{
            return null;
        }
    }
}
