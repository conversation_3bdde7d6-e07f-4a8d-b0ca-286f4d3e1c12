package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-06-24 10:43
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "wujiong.crack")
@Slf4j
public class WujiongCrackProperties {
    private Map<String, WujiongCrackConfig> channelConfigMap = new HashMap<>();

    //根据渠道号查找配置信心
    public WujiongCrackConfig getConfig(String channel) {
        WujiongCrackConfig wujiongCrackConfig = channelConfigMap.get(channel);
        if (wujiongCrackConfig == null) {
            log.error("渠道号:{}未找到相关配置", channel);
            throw new JeecgBootException("无效的渠道号");
        }
        return wujiongCrackConfig;
    }
}
