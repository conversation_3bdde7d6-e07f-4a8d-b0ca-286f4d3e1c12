package com.eleven.cms.config;

import com.eleven.cms.ad.HeTuFenShengChongQingProperties;
import com.eleven.cms.ad.HeTuFenShengProperties;
import com.eleven.cms.ad.XiZangMobileProperties;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.util.*;
import com.eleven.qycl.config.EnterpriseVrbtConfig;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.DateUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2018/1/20 16:31
 * Desc: OkHttp request配置
 */
@Configuration
@Slf4j
public class OkHttpRequestConfig {

    public static final String DELIMITER_AMP = "&";
    public static final String DELIMITER_EMPYT = "";
    public static final String HTTP_GET_METHOD = "GET";
    public static final String HTTP_POST_METHOD = "POST";
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private static final MediaType mediaType = MediaType.parse("application/json");


    /**
     * 电信认证签名拦截器
     *
     * @param dianxinVrbtProperties
     * @return
     */
//    @Bean
//    public Interceptor dxAuthSignIntercept(DianxinVrbtProperties dianxinVrbtProperties) {
//        return chain -> {
//            Request request = chain.request();
//
////            String deviceId = dianxinVrbtProperties.getDeviceId();
////            String channelId = dianxinVrbtProperties.getChannelId();
////            String timestamp = DateUtils.yyyymmddhhmmss.get().format(new Date());
////
////            List<String> signParamList = Lists.newArrayList(deviceId, channelId, timestamp);
////
////            if (HTTP_GET_METHOD.equals(request.method())) {
////                HttpUrl httpUrl = request.url();
////                for (int i = 0; i < httpUrl.querySize(); i++) {
////                    signParamList.add(httpUrl.queryParameterValue(i));
////                }
////            } else {
////                RequestBody requestBody = request.body();
////                if (requestBody instanceof FormBody) {
////                    FormBody formBody = (FormBody) requestBody;
////                    for (int i = 0; i < formBody.size(); i++) {
////                        signParamList.add(formBody.value(i));
////                    }
////                }
////            }
////
////            String signParam = String.join(DELIMITER_AMP, signParamList);
////            String signature = DianxinAuthUtils.generateMacSignature(dianxinVrbtProperties.getDevicePwd(), signParam);
//
////            Request.Builder requestBuilder = request.newBuilder();
////            requestBuilder.header("auth-deviceid", deviceId);
////            requestBuilder.header("auth-channelid", channelId);
////            requestBuilder.header("auth-timestamp", timestamp);
////            requestBuilder.header("auth-signature-method", "HmacSHA1");
////            requestBuilder.header("auth-signature", signature);
//
//            return chain.proceed(request);
//        };
//
//    }

    /**
     * 联通签名拦截器
     *
     * @param liantongVrbtProperties
     * @return
     */
    @Bean
    public Interceptor ltDigestIntercept(LiantongVrbtProperties liantongVrbtProperties) {
        return chain -> {
            Request request = chain.request();

////            String appkey = liantongVrbtProperties.getAppKey();
////            String secret = liantongVrbtProperties.getSecret();
//            String timestamp = DateUtils.yyyymmddhhmmss.get().format(new Date());
//
//            HttpUrl httpUrl = request.url().newBuilder()
////                    .addQueryParameter("appkey", appkey)
//                    .addQueryParameter("timestamp", timestamp).build();
//            final String queryParams = httpUrl.queryParameterNames()
//                    .stream()
//                    .sorted()
//                    .map(name -> name + String.join(DELIMITER_EMPYT, httpUrl.queryParameterValues(name)))
//                    .collect(Collectors.joining());
//
//            byte[] input = (queryParams).getBytes(StandardCharsets.UTF_8);
//          //  byte[] input = (queryParams + secret).getBytes(StandardCharsets.UTF_8);
//            final String digest = DigestUtils.md5DigestAsHex(input).toUpperCase();
//
//            HttpUrl newHttpUrl = httpUrl.newBuilder()
//                    .addQueryParameter("digest", digest)
//                    .build();
//
//            Request newRequest = request.newBuilder().url(newHttpUrl).build();

            return chain.proceed(request);
        };

    }

    /**
     * 四川移动认证签名拦截器
     *
     * @param sichuanMobileFlowPacketProperties
     * @return
     */
    @Bean
    public Interceptor sichuanMobileSignIntercept(SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties) {
        return chain -> {

            String company = null;
            Request request = chain.request();
            SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = (SichuanMobileFlowPacketConfig) request.tag();
            if (sichuanMobileFlowPacketConfig == null) {
                log.error("四川移动流量包参数加密异常");
                return chain.proceed(request);
            }
            String privateKey = sichuanMobileFlowPacketConfig.getPrivateKey();
            String timestamp = DateUtils.getCurrentTimestamp();
            String appId = sichuanMobileFlowPacketConfig.getAppId();
            String appKey = DigestUtils.md5DigestAsHex((sichuanMobileFlowPacketConfig.getAppKey() + timestamp).getBytes(StandardCharsets.UTF_8));
            String version = "1.0";
            String method = StringUtils.substringAfterLast(request.url().toString(), "/");
            String status = "1";
            String alias = "123456";
            FormBody.Builder builder = new FormBody.Builder();
            List<String> signParamList = Lists.newArrayList();
            if (HTTP_GET_METHOD.equals(request.method())) {
                HttpUrl httpUrl = request.url();
                for (int i = 0; i < httpUrl.querySize(); i++) {
                    signParamList.add(httpUrl.queryParameterValue(i));
                }
            } else {
                RequestBody requestBody = request.body();
                builder.add("app_id", appId)
                        .add("app_key", appKey)
                        .add("timestamp", timestamp)
                        .add("version", version)
                        .add("method", method)
                        .add("status", status)
                        .add("alias", alias);
                if (requestBody instanceof FormBody) {
                    FormBody formBody = (FormBody) requestBody;
                    for (int i = 0; i < formBody.size(); i++) {
                        if (!"company".equals(formBody.name(i))) {
                            builder.add(formBody.name(i), formBody.value(i));
                        }
                    }
                }
                FormBody body = builder.build();
                for (int i = 0; i < body.size(); i++) {
                    signParamList.add(body.name(i) + body.value(i));
                }
                Collections.sort(signParamList);
                String signParam = String.join(DELIMITER_EMPYT, signParamList);
                try {
                    String sign = SichunMobileSignUtil.generateSign(privateKey, signParam);
                    builder.add("sign", sign);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("四川移动流量包参数加密异常");
                }
            }
            return chain.proceed(request.newBuilder().post(builder.build()).build());
        };

    }


    /**
     * 四川电信视频彩铃签名拦截器
     *
     * @return
     */
    @Bean
    public Interceptor sichuanDianxinVrbtIntercept() {
        return chain -> {
            Request request = chain.request();
            HttpUrl httpUrl = request.url().newBuilder().build();
            final String queryParams = httpUrl.queryParameterNames()
                    .stream()
                    .map(name -> name + String.join(DELIMITER_AMP, httpUrl.queryParameterValues(name)))
                    .collect(Collectors.joining());
            String sign = DigestUtils.md5DigestAsHex(queryParams.getBytes(StandardCharsets.UTF_8.name()));
            HttpUrl newHttpUrl = httpUrl.newBuilder()
                    .addQueryParameter("sign", sign)
                    .build();
            Request newRequest = request.newBuilder().url(newHttpUrl).build();
            return chain.proceed(newRequest);
        };
    }

    /**
     * 快马四川移动业务签名拦截器
     *
     * @return
     */
    @Bean
    public Interceptor kuaimaYidongIntercept(KuaimaProperties kuaimaProperties) {
        return chain -> {
            Request request = chain.request();
            Map<String, Object> dataMap = (HashMap) request.tag();
            dataMap.put("appId", kuaimaProperties.getAppId());
            dataMap.put("timestamp", System.currentTimeMillis());
            dataMap.put("version", kuaimaProperties.getVersion());
            String parameterStr = dataMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(entry -> entry.toString()).collect(Collectors.joining(DELIMITER_AMP));
            parameterStr += "&key=" + kuaimaProperties.getAppKey();
            log.info("参数:" + parameterStr);
            String sign = DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8.name()));
            dataMap.put("sign", sign);
            RequestBody requestBody = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
            Request newRequest = request.newBuilder().post(requestBody).build();
            return chain.proceed(newRequest);
        };
    }

    /**
     * 企业视频彩铃平台签名拦截器
     *
     * @return
     */
    @Bean
    public Interceptor enterpriseVrbtIntercept() {
        return chain -> {
            Request request = chain.request();
            EnterpriseVrbtConfig enterpriseVrbtConfig = (EnterpriseVrbtConfig) request.tag();
            final String uniqueAccId = enterpriseVrbtConfig.getUniqueAccId();
            final String accSeq = DateUtil.formatFullTime(LocalDateTime.now()) + RandomStringUtils.randomNumeric(6);
            final String accPassword = enterpriseVrbtConfig.getAccPassword();
            String digest = DigestUtils.md5DigestAsHex((uniqueAccId+accSeq+accPassword).getBytes(StandardCharsets.UTF_8.name()))
                    .toUpperCase();
            Request newRequest = request.newBuilder()
                    .addHeader("uniqueAccId",uniqueAccId)
                    .addHeader("accSeq",accSeq)
                    .addHeader("digest",digest)
                    .build();
            return chain.proceed(newRequest);
        };
    }

    /**
     * 西藏移动签名拦截器
     * @return
     */
    @Bean
    public Interceptor xiZangMobileIntercept(XiZangMobileProperties xiZangMobileProperties){
        return chain -> {
            Request request = chain.request();
            HttpUrl httpUrl = request.url();
            if(httpUrl.url().toString().contains(xiZangMobileProperties.getTokenUrl())){
                return chain.proceed(request);
            }

            RequestBody requestBody = request.body();
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            //编码设为UTF-8
            String busiParam = buffer.readString(StandardCharsets.UTF_8);

            Map<String, String> sysParam = httpUrl.queryParameterNames().stream().collect(Collectors.toMap(name -> name, value -> httpUrl.queryParameter(value)));
            String sign=SignatureUtils.signWithSHA256(sysParam,busiParam.toString(), xiZangMobileProperties.getAppKey());

            RequestBody body = RequestBody.create(mediaType,busiParam.toString());
            final HttpUrl url =  httpUrl.newBuilder().addQueryParameter("sign",sign).build();
            Request newRequest = request.newBuilder().url(url).post(body).build();
            return chain.proceed(newRequest);
        };
    }




    /**
     * 河图分省签名拦截器
     * @return
     */
    @Bean
    public Interceptor hetuFenShengIntercept(HeTuFenShengProperties heTuFenShengProperties){
        return chain -> {
            Request request = chain.request();
            HttpUrl httpUrl = request.url();
            RequestBody requestBody = request.body();
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            //编码设为UTF-8
            Map<String, Object> sysParam = mapper.readValue(buffer.readString(StandardCharsets.UTF_8).replaceAll(" ", ""), Map.class);
            String mobile=sysParam.get("mobile").toString();
            String channel=sysParam.get("channel").toString();
            sysParam.remove("mobile");
            sysParam.remove("channel");
            // 将Map转换为JSON字符串
            String busiParam = mapper.writeValueAsString(sysParam);
            final HeTuFenShengChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(channel);
            String sign= null;
            try {
                sign = SignatureUtils.signByPrivateKey(busiParam.getBytes("UTF-8"),heTuFenShengChannel.getPrivateKey());
            } catch (Exception e) {
                e.printStackTrace();
            }
            RequestBody body = RequestBody.create(mediaType,busiParam);
            Sm2Util sm2Util=new Sm2Util(heTuFenShengChannel.getMobilePublicKey());
            String tel=sm2Util.encrypt(mobile);
            Request newRequest = request.newBuilder()
                    .addHeader("signature",sign)
                    .addHeader("timestamp",DateUtil.formatForMiguGroupApi(LocalDateTime.now()))
                    .addHeader("appId",heTuFenShengChannel.getAppId())
                    .addHeader("tel",tel)
                    .url(httpUrl).post(body).build();
            return chain.proceed(newRequest);
        };
    }



    /**
     * 河图分省重庆签名拦截器
     * @return
     */
    @Bean
    public Interceptor hetuFenShengChongQingIntercept(HeTuFenShengChongQingProperties heTuFenShengProperties){
        return chain -> {
            Request request = chain.request();
            HttpUrl httpUrl = request.url();
            RequestBody requestBody = request.body();
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            //编码设为UTF-8
            Map<String, Object> sysParam = mapper.readValue(buffer.readString(StandardCharsets.UTF_8).replaceAll(" ", ""), Map.class);
            String mobile=sysParam.get("mobile").toString();
            String channel=sysParam.get("channel").toString();
            sysParam.remove("mobile");
            sysParam.remove("channel");
            // 将Map转换为JSON字符串
            String busiParam = mapper.writeValueAsString(sysParam);
            final HeTuFenShengChongQingChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(channel);
            String sign= null;
            try {
                sign = SignatureUtils.signByPrivateKey(busiParam.getBytes("UTF-8"),heTuFenShengChannel.getPrivateKey());
            } catch (Exception e) {
                e.printStackTrace();
            }
            RequestBody body = RequestBody.create(mediaType,busiParam);
            Sm2Util sm2Util=new Sm2Util(heTuFenShengChannel.getMobilePublicKey());
            String tel=sm2Util.encrypt(mobile);
            Request newRequest = request.newBuilder()
                    .addHeader("signature",sign)
                    .addHeader("timestamp",DateUtil.formatForMiguGroupApi(LocalDateTime.now()))
                    .addHeader("appId",heTuFenShengChannel.getAppId())
                    .addHeader("mobile",tel)
                    .url(httpUrl).post(body).build();
            return chain.proceed(newRequest);
        };
    }




    /**
     * 四川移动Api认证签名拦截器
     * @return
     */
    @Bean
    public Interceptor sichuanMobileApiSignIntercept() {
        return chain -> {
            Request request = chain.request();
            PortCrackConfig portCrackConfig = (PortCrackConfig) request.tag();
            if (portCrackConfig == null) {
                log.error("四川移动业务加密异常");
                return chain.proceed(request);
            }
            String privateKey = portCrackConfig.getPrivateKey();
            String timestamp = DateUtils.getCurrentTimestamp();
            String appId = portCrackConfig.getAppId();
            String appKey = DigestUtils.md5DigestAsHex((portCrackConfig.getSecretKey() + timestamp).getBytes(StandardCharsets.UTF_8));
            String version = "1.0";
            String method = StringUtils.substringAfterLast(request.url().toString(), "/");
            String status = "1";
            String alias = "123456";
            FormBody.Builder builder = new FormBody.Builder();
            List<String> signParamList = Lists.newArrayList();
            if (HTTP_GET_METHOD.equals(request.method())) {
                HttpUrl httpUrl = request.url();
                for (int i = 0; i < httpUrl.querySize(); i++) {
                    signParamList.add(httpUrl.queryParameterValue(i));
                }
            } else {
                RequestBody requestBody = request.body();
                builder.add("app_id", appId)
                        .add("app_key", appKey)
                        .add("timestamp", timestamp)
                        .add("version", version)
                        .add("method", method)
                        .add("status", status)
                        .add("alias", alias);
                if (requestBody instanceof FormBody) {
                    FormBody formBody = (FormBody) requestBody;
                    for (int i = 0; i < formBody.size(); i++) {
                        if (!"company".equals(formBody.name(i))) {
                            builder.add(formBody.name(i), formBody.value(i));
                        }
                    }
                }
                FormBody body = builder.build();
                for (int i = 0; i < body.size(); i++) {
                    signParamList.add(body.name(i) + body.value(i));
                }
                Collections.sort(signParamList);
                String signParam = String.join(DELIMITER_EMPYT, signParamList);
                try {
                    String sign = SichunMobileSignUtil.generateSign(privateKey, signParam);
                    builder.add("sign", sign);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("四川移动业务参数加密异常");
                }
            }
            return chain.proceed(request.newBuilder().post(builder.build()).build());
        };

    }

}
