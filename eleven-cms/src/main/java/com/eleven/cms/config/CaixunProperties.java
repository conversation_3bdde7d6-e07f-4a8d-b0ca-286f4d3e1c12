package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "caixun")
@Slf4j
public class CaixunProperties {
    private String getTokenUrl;
    private String getSmsCodeUrl;
    private String smsCodeUrl;
    private String callbackUrl;
    private String appId;
    private String key;
    private Map<String,String> channelCodeMap;
    private Map<String,String> channelSourceMap;
}

