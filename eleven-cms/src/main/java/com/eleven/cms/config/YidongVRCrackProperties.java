package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.vr")
@Slf4j
public class YidongVRCrackProperties {
    private String getSmsUrl;
    private String smsValidUrl;
    //渠道彩铃参数配置
    private Map<String, YidongVRCrackConfig> channelConfigMap = new HashMap<>();

    //根据公司获取VR竞盟配置
    public YidongVRCrackConfig getYidongVRConfig(String channel) {
        YidongVRCrackConfig yidongVRCrackConfig = channelConfigMap.get(channel);
        if (yidongVRCrackConfig == null) {
            log.error("渠道号:{}未找到VR竞盟相关配置", channel);
            throw new JeecgBootException("无效的渠道号");
        }
        return yidongVRCrackConfig;
    }

}
