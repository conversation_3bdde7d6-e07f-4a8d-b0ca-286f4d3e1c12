package com.eleven.cms.config;

import com.github.wxpay.sdk.IWXPayDomain;
import com.github.wxpay.sdk.QyclWXPay;
import com.github.wxpay.sdk.WXPayConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.slf4j.Logger;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import java.io.InputStream;

/**
 * Author: <EMAIL>
 * Date: 2020/7/15 10:19
 * Desc:微信支付配置
 */

@Slf4j
@Service
public class QyclWXPayPropertiesConfig extends WXPayConfig {

    private String appID;
    private String mchID;
    private String key;
    private String notifyUrl;
    private String returnUrl;
    private String authDomainName;
    private String spbillCreateIp;
    private String tradeType;
    private String sceneInfo;
    private String appSecret;
    private String publicName;
    /**支付金额*/
    private String totalAmount;

    @Override
    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    @Override
    public String getMchID() {
        return mchID;
    }

    public void setMchID(String mchID) {
        this.mchID = mchID;
    }

    @Override
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getAuthDomainName() {
        return authDomainName;
    }

    public void setAuthDomainName(String authDomainName) {
        this.authDomainName = authDomainName;
    }

    public String getSpbillCreateIp() {
        return spbillCreateIp;
    }

    public void setSpbillCreateIp(String spbillCreateIp) {
        this.spbillCreateIp = spbillCreateIp;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getSceneInfo() {
        return sceneInfo;
    }

    public void setSceneInfo(String sceneInfo) {
        this.sceneInfo = sceneInfo;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    @Override
    public InputStream getCertStream() {
        return null;
    }

    public String getPublicName() {
        return publicName;
    }

    public void setPublicName(String publicName) {
        this.publicName = publicName;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Override
    public IWXPayDomain getWXPayDomain() {
        return new IWXPayDomain() {

            @Override
            public void report(String domain, long elapsedTimeMillis, Exception ex) {
            }

            @Override
            public DomainInfo getDomain(WXPayConfig config) {
                return new DomainInfo("api.mch.weixin.qq.com", true);
            }
        };
    }

    @Bean
    public QyclWXPay qyclWXPay(QyclWXPayPropertiesConfig wxPayPropertiesConfig) throws Exception {
        QyclWXPay qyclWXPay = new QyclWXPay(wxPayPropertiesConfig);
        //经测试MD5和HMACSHA256都可以,默认是沙箱环境是MD5,否则是HMACSHA256
        //wxpay.setSignType(WXPayConstants.SignType.MD5);
        return qyclWXPay;
    }

}
