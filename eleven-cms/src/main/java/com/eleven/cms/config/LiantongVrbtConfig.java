package com.eleven.cms.config;

import lombok.Data;

import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-11-26 10:29
 */
@Data
public class LiantongVrbtConfig {
    /**
     * appKey
     */
    private String appKey;
    /**
     * 秘钥
     */
    private String secret;
    /**
     * CPID
     */
    private String cpId;
    /**
     * 产品Id
     */
    private String produtcId;
    /**
     * 产品名称
     */
    private String produtcName;
    /**
     * 视频彩铃订购包月业务短信下发接口：发送短信（联通）短信模板ID(订购)
     */
    private String orderMonSmsTemplateId;
    /**
     * 视频彩铃订购包月业务短信下发接口：发送短信（联通）短信模板ID(退订)
     */
    private String unSubMonSmsTemplateId;
    /**
     * 短信模板变量Map
     */
    private Map<String,String> smsTemplateVariableMap;
    /**
     * 包月订购二次确认后跳转页面
     */
    private String orderMonRedirectUrl;

    /**
     * LOG_TAG
     */
    private String logTag;
}
