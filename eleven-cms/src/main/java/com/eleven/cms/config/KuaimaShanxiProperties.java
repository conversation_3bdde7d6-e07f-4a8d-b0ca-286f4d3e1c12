package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-06-07 14:42
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "kuaima.shanxi")
@Slf4j
public class KuaimaShanxiProperties {
    private String appId;
    private String channelNo;
    private String key;
    private String getSmsUrl;
    private String smsCodeUrl;

    //渠道业务代码配置
    private Map<String, String> channelConfig = new HashMap<>();


    //根据渠道号获取业务代码
    public String getProductCodeByChannel(String channel) {
        String productCode = channelConfig.get(channel);
        if (StringUtils.isEmpty(productCode)) {
            log.error("渠道号:{}未找到相关产品编码", channel);
            throw new JeecgBootException("无效的快马山西产品编码");
        }
        return productCode;
    }

}
