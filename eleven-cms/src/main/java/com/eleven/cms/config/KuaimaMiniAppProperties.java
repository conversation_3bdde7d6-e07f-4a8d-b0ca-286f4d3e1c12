package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2022-06-07 14:42
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "kuaima.miniapp")
public class KuaimaMiniAppProperties {
    private String action;
    private String channel;
    private String appId;
    private String appKey;
    private String prepareOrderUrl;
    private String callbackUrl;
}
