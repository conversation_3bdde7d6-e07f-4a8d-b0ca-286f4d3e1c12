package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2023-6-9 11:05:55
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "woread")
@Slf4j
public class WoReadNodeProperties {


    //公司沃悦读配置
    private Map<String, WoReadProperties> companyConfigMap = new HashMap<>();


    //根据公司获取沃悦读配置
    public WoReadProperties getWoReadConfig(String company) {
        WoReadProperties woReadProperties = companyConfigMap.get(company);
        return woReadProperties;
    }

    //根据公司获取沃悦读配置
    public WoReadProperties getWoReadConfigByChannelId(String channelId) {
        for (String key : companyConfigMap.keySet()) {
            WoReadProperties woReadProperties = companyConfigMap.get(key);
            WoReadWxProperties wx = woReadProperties.getWx();
            WoReadAliProperties ali = woReadProperties.getAli();
            if(wx.getChannelId().equals(channelId) || ali.getChannelId().equals(channelId)){
                return woReadProperties;
            }
        }
        return null;
    }

}
