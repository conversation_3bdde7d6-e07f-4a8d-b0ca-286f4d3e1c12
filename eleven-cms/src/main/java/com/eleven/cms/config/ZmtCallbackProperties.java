package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2024-10-15 18:04
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "callback.zmt")
@Slf4j
public class ZmtCallbackProperties {
    private String userName;
    private String password;
    private String commodityId;
    private String mobileOfferId;
    private String getTokenUrl;
    private String callbackUrl;
}
