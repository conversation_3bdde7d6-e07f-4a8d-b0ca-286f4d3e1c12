package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2023-7-14 10:44:54
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "henan")
@Slf4j
public class HenanYidongProperties {


    //公司沃悦读配置
    private Map<String, HenanYidongConfig> channelConfigMap = new HashMap<>();


    //根据公司获取河南移动配置
    public HenanYidongConfig getHenanConfigByChannel(String channel) {
        HenanYidongConfig henanYidongConfig = channelConfigMap.get(channel);
        return henanYidongConfig;
    }
}

