package com.eleven.cms.config;

import com.aliyuncs.CommonRequest;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

/**
 * Author: <EMAIL>
 * Date: 2020/2/28 16:16
 * Desc:阿里云短信配置
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "aliyun.sms",ignoreUnknownFields = true)
public class AliyunSmsProperties {
    private String regionId;
    private String accessKeyId;
    private String secret;
    private String queryParameterRegionId;
    private String queryParameterSignName;
    //验证码短信模板
    private String validateTemplateCode;
    //验证码短信模板变量key
    private String validateTemplateParamKey;
    @NestedConfigurationProperty
    private CommonRequest request;
}
