package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "wensheng")
@Slf4j
public class WenshengVideoProperties {
  /*token*/
  private String token;
  /*提交⽂⽣视频任务路径*/
  private String commitTaskUrl;
  /*获取⽂⽣视频结果路径*/
  private String pullResultUrl;
  private String notifyUrl;
}
