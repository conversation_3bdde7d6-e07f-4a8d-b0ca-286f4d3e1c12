package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2024-3-27 16:12:35
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.hubei")
@Slf4j
public class HubeiYidongProperties {
  /*基础路径*/
  private String baseUrl;
  /*appid*/
  private String appId;
  /*secret*/
  private String secret;

  //渠道业务代码配置
  private Map<String, Map<String,String>> channelConfig = new HashMap<>();


  //根据渠道号获取goodsid
  public String getGoodsIdByChannel(String channel) {
    Map<String,String> map = channelConfig.get(channel);
    if (map == null) {
      log.error("渠道号:{}未找到相关业务代码", channel);
      throw new JeecgBootException("无效的贵州移动渠道号");
    }
    return map.get("goodsId");
  }
  //根据渠道号获取itemId
  public String getItemIdByChannel(String channel) {
    Map<String,String> map = channelConfig.get(channel);
    if (map == null) {
      log.error("渠道号:{}未找到相关业务代码", channel);
      throw new JeecgBootException("无效的贵州移动渠道号");
    }
    return map.get("itemId");
  }
}
