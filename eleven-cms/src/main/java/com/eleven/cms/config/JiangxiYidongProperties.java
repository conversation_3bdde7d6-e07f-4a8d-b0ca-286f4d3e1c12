package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-08-12 11:18
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.jiangxi")
@Slf4j
public class JiangxiYidongProperties {
    private String queryPdfUrl;
    private String getSmsCodeUrlV2;
    private String smsCodeUrlV2;
    //江西移动银联双V会员回调地址
    private String jxydYinlianMemberCallbackUrl;
    //江西移动银联双V会员回调地址
    private String jxydYinlianMemberVsCallbackUrl;

    //公司信息配置
    private Map<String, JiangxiCompanyConfig> companyConfigMap = new HashMap<>();
    //产品信息信息配置
    private Map<String, JiangxiProductConfig> channelBizCodeMap = new HashMap<>();

    //根据公司获取电信视频彩铃配置
    public JiangxiCompanyConfig getJiangxiCompanyConfig(String company) {
        JiangxiCompanyConfig jiangxiCompanyConfig = companyConfigMap.get(company);
        if (jiangxiCompanyConfig == null) {
            log.error("渠道号:{}未找到江西移动相关配置", company);
            throw new JeecgBootException("无效的江西移动渠道号");
        }
        return jiangxiCompanyConfig;
    }
    //根据渠道号获取产品配置
    public JiangxiProductConfig getJiangxiProductConfig(String channel) {
        JiangxiProductConfig jiangxiProductConfig = channelBizCodeMap.get(channel);
        if (jiangxiProductConfig == null) {
            log.error("渠道号:{}未找到江西相关产品配置", channel);
            throw new JeecgBootException("无效的江西移动渠道号");
        }
        return jiangxiProductConfig;
    }
}
