package com.eleven.cms.config;

import com.github.wxpay.sdk.IWXPayDomain;
import com.github.wxpay.sdk.QyclWXPay;
import com.github.wxpay.sdk.WXPayConfig;
import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;

import java.io.InputStream;

/**
 * Author: <EMAIL>
 * Date: 2020/7/15 10:19
 * Desc:微信支付配置
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:qycldypay.properties"})
@ConfigurationProperties(prefix = "qycldypay")
public class QyclDYPayPropertiesConfig {
    private String appID;
    private String appSecret;
    private String openIdUrl;
    private String payUrl;
    private String payNotifyToken;
    private String notifyUrl;
    private String key;
    private String refundUrl;
    private String fulfillPushStatusUrl;
    private String image;
    private String path;
    private String tradeRefundUrl;
    private String tradeRefundNotifyUrl;
    private String clientTokenUrl;
//    private String createRefundUrl;
}
