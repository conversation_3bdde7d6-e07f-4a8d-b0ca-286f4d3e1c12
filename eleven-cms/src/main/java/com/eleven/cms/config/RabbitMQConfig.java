package com.eleven.cms.config;

import com.eleven.cms.douyinduanju.constant.DuanjuConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.PostConstruct;

/**
 * Author: <EMAIL>
 * Date: 2023/5/10 11:36
 * Desc: Rabbitmq配置
 *  queue,exchange,binding的创建看图片"rabbitmq架构规划.png"和"rabbitmq延时队列架构.png"
 */
@Configuration
@Slf4j
public class RabbitMQConfig {

    public static final String SUBSCRIBE_EVENT_EXCHANGE_NAME = "subscribe.event.exchange";
    //游戏
    //海艺AI
    public static final String HY_SEND_SMS_CODE_QUEUE_NAME = "miniapp.hy.sendcode.queue";
    //海艺设置铃声
    public static final String HY_SET_RING_QUEUE_NAME = "miniapp.hy.set.ring.queue";

    //腾讯云穿越前世人脸ai
    public static final String TX_FACE_ACROSS_QUEUE_NAME = "tx.swapping.face.across.queue";

    //腾讯云人脸ai
    public static final String TX_FACE_SEND_SMS_CODE_QUEUE_NAME = "miniapp.txface.sendcode.queue";
    //腾讯云穿越前世人脸ai
    public static final String TX_FACE_KP_ACROSS_QUEUE_NAME = "txface.kpacross.queue";

    /**
     * mini小程序订单同步到app
     */
    public static final String MINI_ORDER_SYNC_APP_QUEUE_NAME = "app.miniOrder.sync.appOrder.queue";

    /**
     * 广告上报结果回调队列
     */
    public static final String AD_REPORT_RESULT_CALLBACK_QUEUE_NAME = "miniapp.ad.report.result.callback.queue";
    public static final String AD_REPORT_RESULT_CALLBACK_EXCHANGE_NAME = "miniapp.ad.report.result.callback.exchange";

    /**
     * 小程序订阅事件队列
     */
    public static final String MINI_APP_SUBSCRIBE_EVENT_QUEUE_NAME = "miniApp.subscribe.event.queue";


    @Autowired
    @Lazy
    private RabbitTemplate rabbitTemplate;

    /**
     * 使用Jackson来序列化消息
     *
     * @return
     */
    @Bean
    public MessageConverter messageConverter(){
        return new Jackson2JsonMessageConverter();
    }


    @PostConstruct
    public void initRabbitTemplate() {
        // 服务端收到消息就会回调,只要消息抵达broker就ack=true
        // correlationData需要使用如下方式发送消息才会有,否则都是null
        //   rabbitTemplate.convertAndSend(MqConstants.exchange, "routingKey", "msg", new CorrelationData(UUID.randomUUID().toString()));
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                log.info("confirm ack send failed: " + correlationData + "|" + cause);
            }
        });
        // 消息未正确投递到queue退回模式,只要消息没有投递给指定的队列就会触发这个回调
        rabbitTemplate.setReturnCallback((Message message, int replyCode, String replyText, String exchange, String routingKey) -> {
            log.info("fail message:{},replyCode:{},replyText:{},exchange:{},routingKey:{}",message,replyCode,replyText,exchange,routingKey);
        });

    }
    //---------------------------以下为按业务对象创建一个公用的事件交换机----------------------------------------

    // 创建subscribe事件Exchange
    @Bean("subscribeEventExchange")
    public TopicExchange subscribeEventExchange(){
        return new TopicExchange(SUBSCRIBE_EVENT_EXCHANGE_NAME);
    }


    //-----------------------------------------以下为创建普通的出口队列-----------------------------------------------

    /**
     * 海艺AI滤镜创作队列
     *
     * @return Queue
     */
    @Bean("hyTaskCreateQueue")
    public Queue hyTaskCreateQueue(){
        return QueueBuilder.durable(HY_SEND_SMS_CODE_QUEUE_NAME).build();
    }

    @Bean("txFaceAcrossQueueName")
    public Queue txFaceAcrossQueueName() {
        return QueueBuilder.durable(TX_FACE_ACROSS_QUEUE_NAME).build();
    }

    @Bean("txFaceKpAcrossQueueName")
    public Queue txFaceKpAcrossQueueName() {
        return QueueBuilder.durable(TX_FACE_KP_ACROSS_QUEUE_NAME).build();
    }

    /**
     * 腾讯ai换脸创作队列
     *
     * @return Queue
     */
    @Bean("txAiFaceTaskCreateQueue")
    public Queue txAiFaceTaskCreateQueue(){
        return QueueBuilder.durable(TX_FACE_SEND_SMS_CODE_QUEUE_NAME).build();
    }
    /**
     * 海艺设置铃声
     *
     * @return Queue
     */
    @Bean("hySetRingQueue")
    public Queue hySetRingQueue(){
        return QueueBuilder.durable(HY_SET_RING_QUEUE_NAME).build();
    }

    /**
     * 广告上报结果回调队列
     *
     * @return Queue
     */
    @Bean("adReportResultCallbackQueue")
    public Queue adReportResultCallbackQueue() {
        return QueueBuilder.durable(AD_REPORT_RESULT_CALLBACK_QUEUE_NAME).build();
    }

    @Bean("miniAppSubscribeNotifyQueue")
    public Queue miniAppSubscribeNotifyQueue() {
        return QueueBuilder.durable(DuanjuConstant.MINI_APP_SUBSCRIBE_EVENT_QUEUE_NAME).build();
    }
}
