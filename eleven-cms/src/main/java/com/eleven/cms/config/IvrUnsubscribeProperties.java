package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "ivr.unsubscribe")
public class IvrUnsubscribeProperties {

    /*短信通知手机号配置*/
    private List<String> mobileList;

    //Ivr退订接口限制配置
    private Map<String, IvrUnsubscribeConfig> ivrUnsubscribeMap = new HashMap<>();

    public IvrUnsubscribeConfig getIvrUnsubscribeConfig(String methodName){
        return ivrUnsubscribeMap.get(methodName);
    }

}
