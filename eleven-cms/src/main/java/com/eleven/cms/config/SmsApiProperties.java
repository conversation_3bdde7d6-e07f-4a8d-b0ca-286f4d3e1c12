package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:权益配置白名单
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "sms-api")
public class SmsApiProperties {
    private Map<String, Map<String, Object>> smsConfigMap = new HashMap<>();

}
