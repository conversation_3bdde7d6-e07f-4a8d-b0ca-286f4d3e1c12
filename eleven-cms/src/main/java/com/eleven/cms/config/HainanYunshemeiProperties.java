package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-08-12 11:18
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.hainan-yunshemei")
@Slf4j
public class HainanYunshemeiProperties {
    private String apiBaseUrl;
    private String appId;
    private String appKey;
    private String provinceCode;
    private String channelTypeId;
    private String tradeEparchyCode;
    private String tradeCityCode;
    private String tradeDepartId;
    private String tradeStaffId;

    //渠道业务代码配置
    private Map<String, HainanYunshemeiConfig> channelConfigMap = new HashMap<>();

    //根据渠道号获取业务代码
    public HainanYunshemeiConfig getConfigByChannel(String channel) {
        HainanYunshemeiConfig hainanYunshemeiConfig = channelConfigMap.get(channel);
        if (hainanYunshemeiConfig == null) {
            log.error("渠道号:{}未找到相关业务代码", channel);
            throw new JeecgBootException("无效的新疆移动渠道号");
        }
        return hainanYunshemeiConfig;
    }
}
