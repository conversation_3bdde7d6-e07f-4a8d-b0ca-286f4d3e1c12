package com.eleven.cms.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.util.yunnan.MD5Util;
import com.eleven.cms.util.yunnan.RSAUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.yunnan.gaojie")
@Slf4j
public class YunnanYidongGaojieProperties {


    //请求地址
    private String getSmsurl;
    private String smsCodeurl;
    //渠道业务代码配置
    private Map<String, String> channelConfig = new HashMap<>();
    //根据渠道号获取业务代码
    public String getBizCodeByChannel(String channel) {
        String bizCode = channelConfig.get(channel);
        if (StringUtils.isEmpty(bizCode)) {
            log.error("渠道号:{}未找到相关业务代码", channel);
            throw new JeecgBootException("无效的贵州移动渠道号");
        }
        return bizCode;
    }
}
