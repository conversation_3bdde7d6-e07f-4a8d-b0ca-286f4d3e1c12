package com.eleven.cms.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@Data
@PropertySource(value = "classpath:alipay.properties")
public class AliPayConfig {

	@Value("${alipay.APP_ID}")
	private String appId;
	@Value("${alipay.RSA2_PRIVATE_KEY}")
	private String rsa2PrivateKey;
	@Value("${alipay.ALIPAY_RSA2_PUBLIC_KEY}")
	private String alipayRsa2PublicKey;
	@Value("${alipay.NOTIFY_URL}")
	private String notifyUrl;
	@Value("${alipay.RETURN_URL}")
	private String returnUrl;
	@Value("${alipay.ALIPAY_GATEWAY_URL}")
	private String alipayGatewayUrl;
	@Value("${alipay.CHARSET}")
	private String charset;
	@Value("${alipay.FORMAT}")
	private String format;
	@Value("${alipay.SIGN_TYPE}")
	private String signType;
	
	
	
	public static class AlipayTradeStatus{
		public static String WAIT_BUYER_PAY="WAIT_BUYER_PAY";
		public static String TRADE_CLOSED="TRADE_CLOSED";
		public static String TRADE_SUCCESS="TRADE_SUCCESS";
		public static String TRADE_FINISHED="TRADE_FINISHED";
	}

	@Bean
	public AlipayClient alipayClient(AliPayConfig aliPayConfig){
        AlipayClient alipayClient = new DefaultAlipayClient(aliPayConfig.getAlipayGatewayUrl(),aliPayConfig.getAppId(),
                                                            aliPayConfig.getRsa2PrivateKey(),aliPayConfig.getFormat(),aliPayConfig.getCharset(),aliPayConfig.getAlipayRsa2PublicKey(),aliPayConfig.getSignType()); //获得初始化的AlipayClient
        return alipayClient;
    }

}
