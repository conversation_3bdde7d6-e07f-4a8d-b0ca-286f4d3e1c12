package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 江西移动视频彩铃
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "jiangxi-vrbt")
public class JiangXiVrbtApiProperties {
   //发送短信验证码
    private String sendSmsUrl;
    //校验短信验证码
    private String checkSmsUrl;
    //下单接口
    private String subOrderUrl;
    //订单查询接口
    private String queryOrderUrl;
    //订单通知地址
    private String orderNotifyUrl;
    //无纸化协议查询地址
    private String queryAgreementUrl;
    //投放地址
    private String launchUrl;

    private Map<String, JiangXiVrbtConfig> channelMap = new HashMap<>();

}
