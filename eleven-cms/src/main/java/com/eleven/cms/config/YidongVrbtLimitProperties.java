package com.eleven.cms.config;

import com.eleven.cms.service.ISmsValidateService;
import com.eleven.cms.service.impl.SmsNotifyService;
import com.eleven.cms.util.BizConstant;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.limit")
@Slf4j
public class YidongVrbtLimitProperties {

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    SmsNotifyService smsNotifyService;

    private List<String> mobiles = new ArrayList<>();

    //渠道彩铃参数配置
    private Map<String, Map<String, Integer>> channelProvinceMap = new HashMap<>();

    //根据公司获取电信视频彩铃配置
    public Integer getLimit(String channel, String province) {
        Map<String, Integer> limitMap = channelProvinceMap.get(channel);
        if (limitMap == null) {
            return 0;
        }
        return limitMap.get(province) != null ? limitMap.get(province) : 0;
    }

    public void sendSms(String channel, String province, Integer limitAmount) {
        if (mobiles.size() == 0) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_LIMIT + channel + ":" + province;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        mobiles.forEach(mobile -> {
            smsNotifyService.sendNotify(mobile, "渠道号:" + channel + ",省份:" + province + "已经到达限量,限量数" + limitAmount);
        });
    }

    public void sendVergeLimitSms(String channel, String province, Integer limitAmount, Integer currentCount) {
        if (mobiles.size() == 0) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_VERGE_LIMIT + channel + ":" + province;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        mobiles.forEach(mobile -> {
            if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
                log.error("手机号格式错误:{}", mobile);
            } else {
                smsNotifyService.sendNotify(mobile, "渠道号:" + channel + ",省份:" + province + "即将到达限量,限量数" + limitAmount + "，现在总户数" + currentCount);
            }
        });
    }


}
