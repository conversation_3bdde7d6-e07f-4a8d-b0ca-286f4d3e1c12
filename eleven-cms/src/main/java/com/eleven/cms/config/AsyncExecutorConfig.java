
package com.eleven.cms.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/**
 * Author: <EMAIL>
 * Date: 2019/12/20 12:01
 * Desc:应用级别重写Executor,pring还提供方法级别重写： 开启Async并自定义Executor:
 */
@Configuration
@EnableAsync
public class AsyncExecutorConfig implements AsyncConfigurer {
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(4000);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("MyExecutor-");
        executor.initialize();
        return (Executor)executor;
    }

    /**
     * 自定义异步编排的线程池
     * @return
     */
    @Bean("threadPoolExecutor")
    public ThreadPoolExecutor threadPoolExecutor() {
        return new ThreadPoolExecutor(20, 100, 10, TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(10),
                new ThreadFactoryBuilder().setNameFormat("BIZ-async-%d").setDaemon(true).build(),
                //线程池满了就同步执行
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean("outSidecallbackExecutor")
    public Executor outSidecallbackExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(2000);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("outSidecallbackExecutor-");
        executor.initialize();
        return (Executor)executor;
    }



}