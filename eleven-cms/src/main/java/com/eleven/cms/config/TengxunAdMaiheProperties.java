package com.eleven.cms.config;

import com.aliyuncs.CommonRequest;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2020/2/28 16:16
 * Desc:阿里云短信配置
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "tengxun.ad",ignoreUnknownFields = true)
public class TengxunAdMaiheProperties {
    private String accessToken;
    private String accountId;
    private String userActionSetId;
    private String url;
    private String actionType;
}
