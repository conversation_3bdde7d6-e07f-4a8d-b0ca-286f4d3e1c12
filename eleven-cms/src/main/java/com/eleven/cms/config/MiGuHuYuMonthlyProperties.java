package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 咪咕互娱包月查询
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5 10:12
 **/
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "miguhuyu.monthly")
public class MiGuHuYuMonthlyProperties {


    //渠道业务代码配置
    private Map<String, MiGuHuYuMonthlyConfig> channelMap = new HashMap<>();
    public MiGuHuYuMonthlyConfig getMiGuHuYuMonthlyConfigByChannel(String channel){
        return channelMap.get(channel);
    }
}
