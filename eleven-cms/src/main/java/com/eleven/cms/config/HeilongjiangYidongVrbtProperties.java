package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.heilongjiang")
@Slf4j
public class HeilongjiangYidongVrbtProperties {
    private String getSmsUrl;
    private String sendSmsUrl;

    //渠道业务代码配置
    private Map<String, HeilongjiangYidongVrbtConfig> channelConfigMap = new HashMap<>();

    public HeilongjiangYidongVrbtConfig getHeilongjiangYidongVrbtConfig(String channel) {
        return channelConfigMap.get(channel);
    }


}

