package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:电信视频彩铃配置
 */
@Data
public class DianxinVrbtConfig {
    private String company;
    /**
     * 合作商账号，爱音乐分配，请向商务人员获取。又名deviceid或AppKey
     */
    private String deviceId;
    /**
     * 爱音乐分配的秘钥，请向商务人员获取，又名devicePwd或AppSecret；
     */
    private String devicePwd;
    /**
     * 渠道号，爱音乐分配，请向商务人员获取。 又名channelid
     */
    private String channelId;
    /**
     * 包月套餐ID
     */
    private String packageId;

    /**
     * LOG_TAG
     */
    private String logTag;

    /**
     * 下单接口上限后跳转到到官方试营销地址
     */
    private String apiLimitRedirectPage;
    /**
     * 推送服务id
     */
    private String pushServiceId;
    /**
     * 省份列表
     */
    private List<String> provinceList;

    /**
     * 计费认证页面操作后的返回地
     */
    private String returnUrl;
    //默认铃音
    private String ringId;
}
