package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:阿里云投诉处理配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "alipay-complain")
public class AliPayComplainProperties {

    private String notifySms;
    private List<String> notifyMobileList;
    private List<String> warnMobileList;

}
