package com.eleven.cms.config;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:业务相关配置类
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "ali.verifyphone")
public class AliVerifyPhoneProperties {
    private String accessKeyId;
    private String accessKeySecret;
    private String endpoint;
    private String url;
    private String origin;
}
