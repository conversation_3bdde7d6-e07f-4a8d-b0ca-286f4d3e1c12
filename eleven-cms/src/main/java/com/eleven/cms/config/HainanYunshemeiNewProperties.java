package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-08-12 11:18
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.hainan-yunshemei-new")
@Slf4j
public class HainanYunshemeiNewProperties {
    private String apiBaseUrl;

    //渠道业务代码配置
    private Map<String, HainanYunshemeiNewConfig> channelConfigMap = new HashMap<>();

    //根据渠道号获取业务代码
    public HainanYunshemeiNewConfig getConfigByChannel(String channel) {
        HainanYunshemeiNewConfig hainanYunshemeiNewConfig = channelConfigMap.get(channel);
        if (hainanYunshemeiNewConfig == null) {
            log.error("渠道号:{}未找到相关业务代码", channel);
            throw new JeecgBootException("无效的新疆移动渠道号");
        }
        return hainanYunshemeiNewConfig;
    }
}
