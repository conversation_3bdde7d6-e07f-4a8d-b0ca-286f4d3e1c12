package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Author: lhb
 * Date: 2022-5-24 14:59:21
 * Desc: 抖音小程序微信支付配置
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "miniapi.wxpay",ignoreUnknownFields = true)
public class MiniApiWXPayProperties {
    /*小程序 ID*/
    private String appid;
   /* *//*支付价格。单位为[分]*//*
    private String price;*/
    /*回调通知地址*/
    private String notifyUrl;
    /*小程序微信支付接口地址*/
    private String ttAppletPayUrl;
    /**
     *  抖音小程序支付回调通知验签token
     */
    private String payNotifyToken;
}
