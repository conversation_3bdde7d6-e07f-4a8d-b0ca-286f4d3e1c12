package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.guizhougaojie")
@Slf4j
public class GuizhouYidongGaojieProperties {
  /*获取短信验证码接口路径*/
  private String smsUrl;
  /*开通接口路径*/
  private String sendSmsUrl;
  /*查询接口路径*/
  private String querySmsUrl;
  /*获取token接口接口路径*/
  private String tokenUrl;
  /*appkey*/
  private String appkey;
  /*sign*/
  private String secretKey;
  /*渠道映射ID*/
  private String channelMapId;
  private String channelId;
  private String chnlIp;
  private String websiteName;

  //渠道业务代码配置
  private Map<String, String> channelConfig = new HashMap<>();


  //根据渠道号获取业务代码
  public String getBizCodeByChannel(String channel) {
    String bizCode = channelConfig.get(channel);
    if (StringUtils.isEmpty(bizCode)) {
      log.error("渠道号:{}未找到相关业务代码", channel);
      throw new JeecgBootException("无效的贵州移动渠道号");
    }
    return bizCode;
  }
}
