package com.eleven.cms.config;

import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:业务相关配置类
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "biz.migu")
public class BizProperties {

    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;

    private String apiBaseUrl;
    private String apiBaseUrlCentrality;
    private String miguLoginUrl;
    private String miguLoginUrlCentrality;
    private String miguQueryStrategyUrl;
    private String vrbtOpenUrl;
    private String vrbtCancelUrl;
    private String vrbtOpenUrlCentrality;
    private String vrbtCancelUrlCentrality;
    private String vrbtMonthStatusQueryUrl;
    private String vrbtMonthStatusQueryUrlCentrality;
    private String vrbtToneFreeMonthOrderUrl;
    private String vrbtToneFreeMonthOrderUrlCentrality;
    //以下为新增的服务端安全接口
    private String vrbtStatusQueryUrl;
    private String vrbtStatusQueryUrlCentrality;
    private String vrbtVoLTEStatusQueryUrl;
    private String vrbtToneQueryUrl;
    private String vrbtToneQueryUrlCentrality;
    private String vrbtToneDeleteUrl;
    private String vrbtToneDeleteUrlCentrality;
    private String vrbtTryToSeeUrl;
    private String vrbtTryToSeeUrlCentrality;
    private String vrbtProductQueryUrl;
    //视频彩铃退订接口
    private String vrbtUnsubscribeUrl;
    private String vrbtUnsubscribeUrlCentrality;
    //0元固定时长订购接口
    private String vrbtZeroOrderUrl;
    private String vrbtZeroOrderUrlCentrality;
    //查询视频彩铃业务策略(暂只支持彩铃中心,查询是否支持免费订购视频彩铃内容,有无免费策略)
    private String queryVrbtBusinessPolicyUrl;
    private String queryVrbtBusinessPolicyUrlCentrality;

    //主/被叫视频彩铃功能状态查询接口
    private String vrbtInitiativeStatusQueryUrl;
    private String vrbtInitiativeStatusQueryUrlCentrality;
    //被叫视频彩铃功能开通
    private String vrbtInitiativeSubscribeUrl;
    private String vrbtInitiativeSubscribeUrlCentrality;
    //查询主叫视频彩铃设置 (安全类接口)
    private String vrbtInitiativeQuerySettingUrl;
    private String vrbtInitiativeQuerySettingUrlCentrality;
    //新增主叫视频彩铃设置 (安全类接口)
    private String vrbtInitiativeSettingUrl;
    private String vrbtInitiativeSettingUrlCentrality;

    //以下为音频彩铃相关服务端接口
    private String crbtStatusQueryUrl;

    private String crbtMonthQueryUrl;
    private String crbtMonthCancelUrl;
    //以下为振铃相关服务端接口
    private String rtMonthStatusQueryUrl;
    //以下为白金会员相关服务端接口
    private String bjhyQueryUrl;
    private String bjhyCancelUrl;
    //以下为联合会员(藕粉联合会员,不是以前的百度联合会员)相关服务端接口
    private String asMemberQueryUrl;
    private String asMemberCancelUrl;
    //咪咕前置校验服务端接口
    private String filerCheckUrl;
    
    //1.1.1.1.1.	三方支付连续包月订购(四川0元开通视频彩铃功能)
    private String provinceBjhyOrderUrl;
    //1.1.1.1.2.	三方支付连续包月退订
    private String provinceBjhyCancelUrl;

    //3.3.1.2.3.	白金会员三方支付固定时长(用于发放北岸唐唱的白金会员权益发放)
    private String bjhyOrderUrl;
    private String bjhyAuditionUrl;
    private String bjhyDownlinkUrl;
    //一下为白金会员5元相关服务端接口
    private String bjhy5AuditionUrl;
    private String bjhy5DownlinkUrl;
    //以下为渠道包月相关服务端接口[注意渠道包月本来没有服务端接口,以下接口未从js端抓取改造过来的]
    private String cpmbQueryUrl;
    private String cpmbCancelUrl;
    //歌曲在线听
    private String streamQueryUrl;
    //#视宣号2.3.2.2.2 查询商户信息
    private String sxhGetCircleListUrl;

    //#视宣号2.3.2.2.3 商户成员查询接口
    private String sxhGetMemberListUrl;
    //#视宣号2.3.2.2.4 铃音上传
    private String sxhRingUploadUrl;
    //#视宣号2.3.2.2.6  成员/商户生效内容查询
    private String sxhGetCircleFunctionUrl;
    //#视宣号2.3.2.2.7  商户铃音内容查询
    private String sxhGetCircleRingListUrl;

    //#视宣号2.3.2.2.4 铃音内容设置和删除
    private String sxhRingOperateUrl;
    //1.1.1.1.	特殊内容权益领取接口P23A3 (用于ai视频彩铃)
    private String specialProductSubUrl;
    //1.4.3.4.	特殊内容权益领取接口P25G9 (用于ai视频彩铃)
    private String specialProductSubUrlCentrality;

    //视彩号订购关系查询接口
    private String schQueryUrl;
    //视彩号订购关系查询接口
    private String bjhyAndCpmbQueryUrl;

    //2.3.1.4.8.	主叫视频彩铃功能查询接口P23A5
    private String activeVrbtQueryUrl;
    //2.3.1.4.9.	主叫视频彩铃内容免费订购接口P23A6
    private String activeVrbtFreeOrderUrl;

    //private String serviceId;
    private String mobileRegionServerUrl;
    private String mobileRegionApiKey;

    private String subscribeVrbtOrderPage;
    private String subscribeVrbtCentralityOrderPage;
    private String subscribeRtOrderPage;
    private String subscribeBjhyOrderPage;
    private String subscribeLiantongVrbtOrderPage;
    private String subscribeDianxinVrbtOrderPage;
    private String subscribeDianxinVrbtOrderReportPage;
    private String subscribeCpmbOrderPage;
    private String pushSubscibeServerUrl;
    private String pushSmsCodeServerUrl;

    private String toutiaoAdTrackApi;

    private String tuiaAdEffectApi;
    private String tuiaAdAdvertKey;
    private String tuiaAdAdvertKeyHongsheng;
    private String tuiaAdAdvertKeyMaihe;
    private String tuiaAdAdvertKeyMaiheCijin;

    private String bianxianmaoAdEffectApi;

    private String doumengAdDeepTranslateApi;
    private String doumengAdOrderDeepTranslateApi;
    private String doumengAdAccoundId;
    private String doumengAdSecret;
    private String doumengAdAccoundIdHongsheng;
    private String doumengAdSecretHongsheng;
    private String doumengAdAccoundIdHongshengCpmb;
    private String doumengAdSecretHongshengCpmb;

    private String hudongtuiAdEffectApi;

    private String tengxunAdTrackApi;

    //    private Map<String, MiguChannelConfig> miguChannelConfigMap = new HashMap<>();
    private Map<String, String> vrbtChannelConfigMap = new HashMap<>();
    private Map<String, String> mixedChannelOldNewConfigMap = new HashMap<>();
    private String randomVrbtToneFreeOrderFailNotifySms;
    private List<String> randomVrbtToneFreeOrderFailNotifyPhoneNumberList;

    private BiMap<String, String> vrbtChannelConfigReverseMap;

    private Map<String, SchSpecialProductConfig> schChannelSpecialProductConfigMap = new HashMap<>();

//    @PostConstruct
    public void init() {
        this.vrbtChannelConfigReverseMap = HashBiMap.create(vrbtChannelConfigMap).inverse();
    }

    public List<String> getChannelByServiceId(String serviceId) {
//        Map<String, List<String>> mapInversed =
//            miguChannelConfigMap.entrySet()
//                .stream()
//                .collect(Collectors.groupingBy(entry -> entry.getValue().getServiceId(), Collectors.mapping(Map.Entry::getKey, Collectors.toList())));
//
//        final List<String> channelCodeList = mapInversed.get(serviceId);
//
//        if (channelCodeList != null && channelCodeList.size() > 0) {
//            Collections.reverse(channelCodeList);
//        }
        return cmsCrackConfigService.getChannnelByServiceId(serviceId);
    }

    /**
     * 根据任意渠道获取 渠道包的渠道号
     *
     * @param anyChannel 订阅包/渠道包的渠道号
     * @return
     */
    public String getChannelQudao(String anyChannel) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(anyChannel);
        if (cmsCrackConfig == null) {
            return null;
        }
        String qudaoChannel = vrbtChannelConfigMap.get(anyChannel);
        return StringUtils.isEmpty(qudaoChannel) ? anyChannel : qudaoChannel;
    }

    /**
     * 根据任意渠道获取 订阅包的渠道号
     *
     * @param anyChannel 订阅包/渠道包的渠道号
     * @return
     */
    public String getChannelDingyue(String anyChannel) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(anyChannel);
        if (cmsCrackConfig == null) {
            return null;
        }
        String dingyueChannel = vrbtChannelConfigReverseMap.get(anyChannel);
        return StringUtils.isEmpty(dingyueChannel) ? anyChannel : dingyueChannel;
    }

    /**
     * 根据任意渠道 判定是否为订阅包的渠道号
     *
     * @param anyChannel 订阅包/渠道包的渠道号
     * @return
     */
    public boolean isChannelDingyue(String anyChannel) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(anyChannel);
        if (cmsCrackConfig == null) {
            throw new IllegalArgumentException("渠道号不存在");
        }
        return vrbtChannelConfigMap.containsKey(anyChannel);
    }

    public String genForwardDomainUrl(String miguOriginUrl, CmsCrackConfig config) {
        final String forwardDomain = config.getForwardDomain();
        if (StringUtils.isEmpty(miguOriginUrl) || StringUtils.isEmpty(forwardDomain)) {
            return miguOriginUrl;
        }
        //http://hz.migu.cn/order/rest/login/secret/url.do
        //https://crbt.cdyrjygs.com/order/rest/login/secret/url.do
        return miguOriginUrl.replaceFirst("http(s)?://(m\\.12530\\.com|crbt\\.cdyrjygs\\.com|hz\\.migu\\.cn)", forwardDomain.trim());
    }

    public String getMixedNewChannel(String oldChannel) {
        return mixedChannelOldNewConfigMap.getOrDefault(oldChannel, oldChannel);
    }
}
