package com.eleven.cms.config;

import com.aliyuncs.CommonRequest;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

/**
 * Author: lhb
 * Date: 2022-3-15 14:59:21
 * Desc:小程序配置
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "miniapi",ignoreUnknownFields = true)
public class MiniApiProperties {
    /*小程序 ID*/
    private String appid;
    /*小程序的 APP Secret，可以在开发者后台获取*/
    private String secret;

    /*权益中心小程序 ID*/
    private String appidForRights;
    /*权益中心小程序的 APP Secret，可以在开发者后台获取*/
    private String secretForRights;
}
