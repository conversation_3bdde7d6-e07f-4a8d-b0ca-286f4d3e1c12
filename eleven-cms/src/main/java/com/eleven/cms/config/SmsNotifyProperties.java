package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Author: <EMAIL>
 * Date: 2020/2/28 16:16
 * Desc:阿里云短信配置
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "sms-nofify",ignoreUnknownFields = true)
public class SmsNotifyProperties {
    //短信签名
    private String smsCommonSign;
    //企业彩铃1小时校验超限发送短信间隔
    private String qyclVerifyLimitRedisKeyPrefix;
    //企业彩铃1小时校验超限发送短信间隔
    private Long qyclVerifyLimitRedisTtl;
    //企业彩铃1小时校验超限通知手机号
    private List<String> qyclVerifyLimitNotifyMobileList;
    //告警短信通知手机号
    private List<String> alertNotifyMobileList;
    //只要微信公众号消息
    private List<String> onlyWxMsgMobileList;


}
