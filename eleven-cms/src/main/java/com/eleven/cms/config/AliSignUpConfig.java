package com.eleven.cms.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@Data
@PropertySource(value = "classpath:aliSignUp.properties")
public class AliSignUpConfig {

	@Value("${aliSignUp.APP_ID}")
	private String appId;
	@Value("${aliSignUp.RSA2_PRIVATE_KEY}")
	private String rsa2PrivateKey;
	@Value("${aliSignUp.ALIPAY_RSA2_PUBLIC_KEY}")
	private String alipayRsa2PublicKey;
	@Value("${aliSignUp.NOTIFY_URL}")
	private String notifyUrl;
	@Value("${aliSignUp.RETURN_URL}")
	private String returnUrl;

	@Value("${aliSignUp.VRBT_RETURN_URL}")
	private String vrbtReturnUrl;


	@Value("${aliSignUp.ALIPAY_GATEWAY_URL}")
	private String alipayGatewayUrl;
	@Value("${aliSignUp.CHARSET}")
	private String charset;
	@Value("${aliSignUp.FORMAT}")
	private String format;
	@Value("${aliSignUp.SIGN_TYPE}")
	private String signType;
	@Value("${aliSignUp.TRADE_PAY_NOTIFY_URL}")
	private String tradePayNotifyUrl;



	public static class AlipayTradeStatus{
		public static String WAIT_BUYER_PAY="WAIT_BUYER_PAY";
		public static String TRADE_CLOSED="TRADE_CLOSED";
		public static String TRADE_SUCCESS="TRADE_SUCCESS";
		public static String TRADE_FINISHED="TRADE_FINISHED";
	}

}
