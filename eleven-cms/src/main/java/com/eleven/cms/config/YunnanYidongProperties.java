package com.eleven.cms.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.util.yunnan.MD5Util;
import com.eleven.cms.util.yunnan.RSAUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.*;

/**
 *
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.yunnan")
@Slf4j
public class YunnanYidongProperties {


    //渠道业务代码配置
    private Map<String, YunnanYidongConfig> channelConfig = new HashMap<>();

    @Data
    public static class YunnanYidongConfig {

        /*接口路径*/
        private String baseUrl;
        /*appId*/
        private String appId;
        /*appKey*/
        private String appKey;
        /*rsaPublicKey*/
        private String rsaPublicKey;
        /*scenarioCode*/
        private String scenarioCode;
        private String productCode;

        private String tradeCityCode;
        private String tradeDepartId;
        private String channelTypeId;
        private String inModeCode;
        private String tradeStaffId;
        private String provinceCode;

        public ObjectNode formatParam(ObjectMapper mapper, String phone, String tradeEparchyCode,ObjectNode content) throws Exception {
            //公共参数
            ObjectNode param = mapper.createObjectNode();
            ObjectNode head = mapper.createObjectNode();
            
            head.put("appId", appId);//必填
            head.put("appKey",appKey);//必填
            head.put("flowId", IdWorker.get32UUID());
            head.put("tradeCityCode", tradeCityCode);
            head.put("tradeDepartId", tradeDepartId);
            head.put("channelTypeId", channelTypeId);
            head.put("inModeCode", inModeCode);
            head.put("tradeStaffId", tradeStaffId); //必填
            head.put("provinceCode", provinceCode);
            head.put("tradeEparchyCode", tradeEparchyCode);
            param.put("head",head);
            param.put("content", content);
            head.put("sign", pruSign(param));//sign 签名字段必填
            param.put("head",head);

            return param;
        }


        public String pruSign(ObjectNode fullParam) throws Exception {
            Map<String, String> requestContent = new HashMap<>();
            requestContent.put("content", JSON.parseObject(fullParam.toString(), LinkedHashMap.class, Feature.OrderedField).toString());
            String[] paramArr = requestContent.keySet().toArray(new String[requestContent.size()]);
            Arrays.sort(paramArr);
            //StringBuilder keyBuf = new StringBuilder();
            StringBuilder buf = new StringBuilder();
            for (String param : paramArr) {
                String value = requestContent.get(param.trim());
                if (StringUtils.isNotBlank(value)) {
                    //keyBuf.append(param).append("|");
                    buf.append(value.trim());
                }
            }
            String md5Str = "";
            String rsaSign = "";
            if (buf.length() > 0) {
                // 根据报文参数生成签名
                md5Str = MD5Util.MD5(buf.toString());//使用标准MD5加密API
                rsaSign = RSAUtils.encryptByPublicKey(md5Str, rsaPublicKey);
            } else {
                System.out.println(" 缺少必须参数！ ");
            }
            return rsaSign;
        }

    }


    //根据渠道号获取业务代码
}
