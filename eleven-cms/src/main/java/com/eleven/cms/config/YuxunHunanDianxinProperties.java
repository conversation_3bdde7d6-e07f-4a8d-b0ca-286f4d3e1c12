package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2022-11-03 14:15
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yuxun.hunan")
@Slf4j
public class YuxunHunanDianxinProperties {
//    private String sid;
//    private String sidPwd;
    private String acceptOrderUrl;
//    private String privateKey;
    private String userId;
    private String secretKey;
}
