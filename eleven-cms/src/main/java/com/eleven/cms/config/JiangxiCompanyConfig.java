package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-08-12 11:18
 */
@Data
public class JiangxiCompanyConfig {
    private String getSmsCodeUrl;
    private String smsCodeUrl;
    private String orderUrl;
    private String noPageWriteUrl;
    private String queryPdfUrl;
    private String fetchEncryptionUrl;
    private String getEncryptionUrl;
    private String apiKey;
    private String secretKey;
    private String userId;
    
    private String particle;
    private Map<String,String> channelReportPageMap = new HashMap<>();

    private String logTag;
    private String channelName;
}
