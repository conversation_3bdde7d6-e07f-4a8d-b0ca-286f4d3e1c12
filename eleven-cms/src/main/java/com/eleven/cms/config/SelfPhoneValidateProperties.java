package com.eleven.cms.config;

import com.aliyuncs.CommonRequest;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

/**
 * Author: lhb
 * Date: 2022-4-26 12:05:46
 * Desc:本机校验
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "phone.validate",ignoreUnknownFields = true)
public class SelfPhoneValidateProperties {
    private String secretId;
    private String secretKey;
    private String businessId;
    private String selfPhoneValidateUrl;
}
