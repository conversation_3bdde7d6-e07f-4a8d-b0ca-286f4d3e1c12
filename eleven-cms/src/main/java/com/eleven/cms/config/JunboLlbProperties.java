package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-07-07 10:28
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "junbo.llb")
public class JunboLlbProperties {
    private String pid;
    private String productCode;
    private String getSmsUrl;
    private String smsCodeUrl;
    private String handleOrderUrl;

    //渠道业务代码配置
    private Map<String, JunboLlbConfig> channelJunboLlbMap = new HashMap<>();

    public JunboLlbConfig getJunboLlbConfigByBizCode(String bizCode){
        return channelJunboLlbMap.get(bizCode);
    }



}
