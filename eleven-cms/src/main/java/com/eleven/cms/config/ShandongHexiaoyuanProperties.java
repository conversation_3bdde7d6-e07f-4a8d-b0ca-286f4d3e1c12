package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2024-3-14 14:55:21
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "shandonghxy")
@Slf4j
public class ShandongHexiaoyuanProperties {
  /*基础路径*/
  private String baseUrl;
  /*要订购的产品编号，参数需加密*/
  private String bCode;
  /*sign*/
  private String secretKey;
}
