package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:电信视频彩铃配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "zywhvrbt.diy.ftp")
@Slf4j
public class FtpProperties {

    private String hostname;
    private Integer port;
    private String username;
    private String password;

}
