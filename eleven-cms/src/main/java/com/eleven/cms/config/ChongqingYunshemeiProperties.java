package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-08-12 11:18
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.chongqing-yunshemei")
@Slf4j
public class ChongqingYunshemeiProperties {
    private String getSmsUrl;
    private String smsCodeUrl;
    private String channelId;
    private String operatorId;
    //渠道业务代码配置
    private Map<String, String> channelCodeMap = new HashMap<>();

}
