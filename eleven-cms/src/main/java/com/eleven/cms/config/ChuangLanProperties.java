package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 创蓝携号转网配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/29 11:05
 **/
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "chuang-lan")
@Slf4j
public class ChuangLanProperties {
    //创蓝携号转网appId
    private String appId;
    //创蓝携号转网appKey
    private String appKey;
    //请求地址
    private String mobileRegionServerUrl;

}
