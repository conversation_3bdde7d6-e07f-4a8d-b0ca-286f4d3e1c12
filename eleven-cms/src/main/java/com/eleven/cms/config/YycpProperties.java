package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * Author: lihb
 * Date: 2023-6-20 15:28:26
 * Desc:视频彩铃DIY配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yycp.diy")
public class YycpProperties {

    private String reportUrl;
    private String dId;
    private String accessPlatformId;
    private String channel;
    private String cpId;
    private String key;
    private List<String> ossRingList;

}
