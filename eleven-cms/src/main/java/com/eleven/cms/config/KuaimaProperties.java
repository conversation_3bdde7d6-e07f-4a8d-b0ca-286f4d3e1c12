package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2022-06-07 14:42
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "kuaima.scmcc")
public class KuaimaProperties {
    private String appId;
    private String version;
    private String appKey;
    private String getSmsUrl;
    private String smsCodeUrl;
    private String channelNo;
    private String prepareOrderUrl;
    private String callbackUrl;
}
