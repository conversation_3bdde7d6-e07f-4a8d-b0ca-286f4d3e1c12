package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "xiaogao.crack")
@Slf4j
public class XiaogaoCrackProperties {
    private String getSmsUrl;
    private String smsValidUrl;
    //渠道彩铃参数配置
    private Map<String, YidongVrbtCrackConfig> channelConfigMap = new HashMap<>();

    //根据公司获取电信视频彩铃配置
    public YidongVrbtCrackConfig getYidongVrbtConfig(String channel) {
        YidongVrbtCrackConfig yidongVrbtCrackConfig = channelConfigMap.get(channel);
        if (yidongVrbtCrackConfig == null) {
            log.error("渠道号:{}未找到视频彩铃相关配置", channel);
            throw new JeecgBootException("无效的渠道号");
        }
        return yidongVrbtCrackConfig;
    }

}
