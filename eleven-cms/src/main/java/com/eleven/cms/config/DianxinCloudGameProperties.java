package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 电信云游戏配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/7 17:06
 **/
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "dianxin-cloudgame")
@Slf4j
public class DianxinCloudGameProperties {
    private String mchId;
    private String key;
    private String getSmsCodeUrl;
    private String smsCodeUrl;
    //电信云游戏通知地址
    private String callbackUrl;
}
