package com.eleven.cms.config;

import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.util.OkHttpClientUtils;
import com.moczul.ok2curl.CurlInterceptor;
import com.wechat.pay.java.core.AbstractRSAConfig;
import com.wechat.pay.java.core.certificate.CertificateProvider;
import com.wechat.pay.java.core.cipher.AeadAesCipher;
import com.wechat.pay.java.core.cipher.AeadCipher;
import com.wechat.pay.java.core.cipher.RSAVerifier;
import com.wechat.pay.java.core.cipher.Verifier;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

import static com.wechat.pay.java.core.notification.Constant.AES_CIPHER_ALGORITHM;
import static com.wechat.pay.java.core.notification.Constant.RSA_SIGN_TYPE;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/10 17:15
 **/
@Slf4j
@Service
public class WechatRSAConfig extends AbstractRSAConfig implements NotificationConfig {
    private String mchId;
    private String mchSerialNo;
    private String apiKey;
    private String appId;
    private final CertificateProvider certificateProvider;
    private final AeadCipher aeadCipher;
    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getMchSerialNo() {
        return mchSerialNo;
    }

    public void setMchSerialNo(String mchSerialNo) {
        this.mchSerialNo = mchSerialNo;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    private OkHttpClient client;
    @Autowired
    private Environment environment;


//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
    }

    public WechatRSAConfig(WechatRSABuilder builder) {
        super(
                builder.merchantId,
                builder.privateKey,
                builder.merchantSerialNumber,
                builder.certificateProvider);
        this.certificateProvider = builder.certificateProvider;
        this.aeadCipher = new AeadAesCipher(builder.apiV3Key);
    }

    /**
     * 获取签名类型
     *
     * @return 签名类型
     */
    @Override
    public String getSignType() {
        return RSA_SIGN_TYPE;
    }

    /**
     * 获取认证加解密器类型
     *
     * @return 认证加解密器类型
     */
    @Override
    public String getCipherType() {
        return AES_CIPHER_ALGORITHM;
    }

    /**
     * 创建验签器
     *
     * @return 验签器
     */
    @Override
    public Verifier createVerifier() {
        return new RSAVerifier(certificateProvider);
    }

    /**
     * 创建认证加解密器
     *
     * @return 认证加解密器
     */
    @Override
    public AeadCipher createAeadCipher() {
        return aeadCipher;
    }




    public JsapiServiceExtension jsAPIWechatPay(WechatConfigLog wechatConfigLog)throws Exception{

        this.setMchId(wechatConfigLog.getMchId());
        this.setMchSerialNo(wechatConfigLog.getMchSerialNo());
        this.setApiKey(wechatConfigLog.getApiKey());
        this.setAppId(wechatConfigLog.getAppId());

        ClassPathResource classPathResource = new ClassPathResource(this.getMchId()+".pem");
        WechatRSAConfig config = new WechatRSABuilder()
                .privateKeyFromInputStream(classPathResource.getInputStream())
                .merchantId(this.getMchId())
                .merchantSerialNumber(this.getMchSerialNo())
                .apiV3Key(this.getApiKey())
                .build();
        // 初始化服务
        // 不填默认为RSA
        JsapiServiceExtension service =new JsapiServiceExtension.Builder().config(config).signType("RSA").build();
        return service;
    }

//


    public String httpGet(String url,String auth) {
        Request request = new Request.Builder()
                .addHeader("Authorization",auth)
                .addHeader("Accept","application/json")
                .addHeader("User-Agent","https://zh.wikipedia.org/wiki/User_agent").url(url).get().build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("获取平台证书列表,响应数据=>地址:{},响应参数:{}",url,content);
            return content;
        } catch (IOException e) {
            log.error("获取平台证书列表,请求异常=>地址:{}",url,e);
            return null;
        }
    }
}
