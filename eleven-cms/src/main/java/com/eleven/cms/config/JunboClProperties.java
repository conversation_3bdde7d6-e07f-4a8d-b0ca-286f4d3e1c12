package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 骏伯存量
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "junbo.cunliang")
public class JunboClProperties {
    //获取验证码
    private String getSmsUrl;
    //校验验证码
    private String checkSmsUrl;
    //下单
    private String smsCodeUrl;
    //权益查询
    private String queryRightsUrl;
    //权益发送验证码
    private String smsRightsCodeUrl;
    //权益下单
    private String rightsOrderUrl;

    //渠道业务代码配置
    private Map<String, JunboClConfig> channelJunboClMap = new HashMap<>();

    public JunboClConfig getJunboClConfigByChannel(String channel){
        return channelJunboClMap.get(channel);
    }



}
