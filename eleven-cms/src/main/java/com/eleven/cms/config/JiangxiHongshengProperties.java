package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-08-12 11:18
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.jiangxihs")
@Slf4j
public class JiangxiHongshengProperties {
    private String apiKey;
    private String secretKey;
    private String userId;
    private String offerId;
    private String goodsId;
    private String planId;
    private String getSmsCodeUrl;
    private String smsCodeUrl;
    private String orderUrl;
    private String noPageWriteUrl;
}
