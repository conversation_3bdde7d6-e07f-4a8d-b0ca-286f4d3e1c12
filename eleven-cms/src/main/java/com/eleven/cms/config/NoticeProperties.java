package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "notice",ignoreUnknownFields = true)
public class NoticeProperties {
    private Map<String, MobileMap> mobileMap = new HashMap<>();
    private Map<String, MobileMap> mobileAlipayMap= new HashMap<>();
}
