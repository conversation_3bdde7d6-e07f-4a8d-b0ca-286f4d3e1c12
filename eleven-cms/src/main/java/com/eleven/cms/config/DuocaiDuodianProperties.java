package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 多彩多点商超权益配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/29 11:05
 **/
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "duocai-duodian")
@Slf4j
public class DuocaiDuodianProperties {
    //多彩多点权益appKey
    private String appKey;
    //多彩多点权益appKey
    private String appId;
    //库存
    private String stockAmount;
    //充值类型: 充值类型特有充值类型: 1. 厂家直冲2.卖家直发3.官方直充4.扫码直充5.卖家代充6.卡密
    private String delivery;
    //品牌
    private String brand;
    //权益描述
    private String description;
    //使用规则
    private String useRule;

}
