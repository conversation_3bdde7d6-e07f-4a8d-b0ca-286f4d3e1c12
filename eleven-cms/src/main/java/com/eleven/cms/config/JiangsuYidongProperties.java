package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2022-11-03 14:15
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "xunfei.jsyd")
@Slf4j
public class JiangsuYidongProperties {
    private String apiKey;
    private String secretKey;
    private String authUrl;
    private String secInterface;
    private String createOrderUrl;
    private String goodCode;
    private String smsValidateUrl;
    private String queryOrderUrl;
    private String queryAgeUrl;
    private String orderCheckUrl;
}
