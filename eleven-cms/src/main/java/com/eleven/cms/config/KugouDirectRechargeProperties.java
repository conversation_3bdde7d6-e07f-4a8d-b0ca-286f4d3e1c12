package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Author: lihb
 * Date: 2022-5-17 11:31:35
 * Desc: 酷狗直充配置信息
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "kugou.directrecharge")
public class KugouDirectRechargeProperties {

    /*接口基础url*/
    private String baseUrl;
    /*直充接口url*/
    private String orderUrl;
    /*联合会员直充url*/
    private String orderUnionUrl;
    /*直充订单查询url*/
    private String queryOrderUrl;
    /*获取验证码接口url*/
    private String verifyCodeUrl;
    /*回收酷狗音乐的豪华vip或音乐包url*/
    private String recoverOrderUrl;
    /*请求账号信息接口url*/
    private String openIdListUrl;
    /*直充配额主key，同一个主key下可申请多个直充配额*/
    private String mainKey;
    /*参数签名*/
    private String key;
}
