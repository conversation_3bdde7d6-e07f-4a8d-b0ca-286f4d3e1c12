package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.heilongjiangxqy")
@Slf4j
public class HeilongjiangYidongXqyVrbtProperties {
    private String getSmsUrl;
    private String sendSmsUrl;
    private String pId;
    private String prcId;
    private String channel;
    private String pageUrl;
    private String logTag;


}

