package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "ypcy")
@Slf4j
public class YunpanchangyingProperties {
  private String orderUrl;
  private String channelCode;
  private String payType;
  private String payCode;
  private String secretKey;
}
