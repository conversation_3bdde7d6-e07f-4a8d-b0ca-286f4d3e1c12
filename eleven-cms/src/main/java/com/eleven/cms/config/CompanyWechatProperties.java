package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 企业微信配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/9 12:00
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "company-wechat",ignoreUnknownFields = true)
public class CompanyWechatProperties {
    private String webhookUrl;
    private Map<String, CompanyWechatGroup> groupMap;
}
