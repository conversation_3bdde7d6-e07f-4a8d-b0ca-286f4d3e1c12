package com.eleven.cms.config;

import com.github.wxpay.sdk.IWXPayDomain;
import com.github.wxpay.sdk.WXPay;
import com.github.wxpay.sdk.WXPayConfig;
import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;

import java.io.InputStream;

/**
 * Author: <EMAIL>
 * Date: 2020/7/15 10:19
 * Desc:微信支付配置
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:wxpay.properties"})
@ConfigurationProperties(prefix = "wxpay")
public class WXPayPropertiesConfig extends WXPayConfig {

    private String appID;
    private String mchID;
    private String key;
    private String notifyUrl;
    private String returnUrl;
    private String authDomainName;
    private String spbillCreateIp;
    private String tradeType;
    private String sceneInfo;
    private String sendWeiXinCode;
    private String weiXinActivateCode;
//    private String stockIdCode;
//    private String appIdCode;
//    private String mchIdCode;
    private String stocksIsValid;
    @Override
    public String getAppID() {
        return this.appID;
    }

    @Override
    public String getMchID() {
        return this.mchID;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public InputStream getCertStream() {
        return null;
    }

    @Override
    public IWXPayDomain getWXPayDomain() {
        return new IWXPayDomain() {

            @Override
            public void report(String domain, long elapsedTimeMillis, Exception ex) {
            }

            @Override
            public DomainInfo getDomain(WXPayConfig config) {
                return new DomainInfo("api.mch.weixin.qq.com", true);
            }
        };
    }

    @Bean
    public WXPay wxPay(WXPayPropertiesConfig wxPayPropertiesConfig) throws Exception {
        WXPay wxPay = new WXPay(wxPayPropertiesConfig);
        //经测试MD5和HMACSHA256都可以,默认是沙箱环境是MD5,否则是HMACSHA256
        //wxpay.setSignType(WXPayConstants.SignType.MD5);
        return wxPay;
    }

}
