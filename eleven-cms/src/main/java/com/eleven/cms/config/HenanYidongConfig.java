package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2023-7-14 10:44:54
 */
@Data
@SpringBootConfiguration
@Slf4j
public class HenanYidongConfig {

    private String apiId;
    private String appId;
    private String appKey;
    private String offerId;
    private String idSign;
    private String baseUrl;
    private String tokenUrl;
}

