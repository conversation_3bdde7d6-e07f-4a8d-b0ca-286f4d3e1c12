package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:联通视频彩铃配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "liantong.vrbt")
public class LiantongVrbtProperties {

    /**
     * api基地址
     */
    private String apiBaseUrl;
    /**
     * 发送登录验证码 GET
     */
    private String sendLoginCodeUrl;
    /**
     * 校验登录验证码 GET
     */
    private String codeLoginUrl;
    /**
     * 查询用户订购关系-查询已订购产品(免用户授权) GET
     */
    private String qrySubedProductsNoTokenUrl;
    /**
     * 视频彩铃包月业务订购接口：彩铃收费产品订购（包月）POST
     */
    private String onePointProductMonUrl;
    /**
     * 视频彩铃订购包月业务短信下发接口：发送短信（联通）POST
     */
    private String sendMsgUrl;
    /**
     * 视音频设置接口：彩铃一键设置（包月用户）POST
     */
    private String settingRingOnePointMonUrl;
    /**
     * 查询用户铃音库情况：获取用户订购铃音列表 POST
     */
    private String orderRingListUrl;
    /**
     * 发送短信验证码（退订产品需用户验证时用到）GET
     */
    private String sendVerifyCodeUrl;
    /**
     * 退订产品接口：退订产品(需短信验证码) GET
     */
    private String unSubProductWithVCodeUrl;
    /**
     * 退订产品接口：退订产品(免用户授权) GET
     */
    private String unSubProductNoTokenUrl;
    //以下为三方支付新接口
    /**
     * 查询是否可以开通视频彩铃
     */
    private String isCanOpenVideoRingUrl;
    /**
     * 联通视频彩铃能力兑换
     */
    private String exchangeRingUrl;
    /**
     * 联通企业视频彩铃内容ID生成
     */
    private String nextIdUrl;
    /**
     * 联通企业视频彩铃彩铃上传
     */
    private String uploadUrl;

    /**
     * 联通企业视频彩铃铃音删除
     */
    private String delOrderUrl;

    /**
     * 联通企业视频彩铃视频彩铃能力有效期查询
     */
    private String licenseQueryUrl;
    /**
     * 联通企业视频彩铃视频彩铃能力有效期查询
     */
    private String settingRingOnePointMonUrlDiy;

    /**
     * 联通企业视频彩铃 用户铃音库查询
     */
    private String listRingLibraryUrl;
    /**
     * 联通企业视频彩铃 默认图片
     */
    private String videoImg;
    //公司视频彩铃配置
    private Map<String, LiantongVrbtConfig> companyConfigMap = new HashMap<>();

    //根据公司获取联通视频彩铃配置
    public LiantongVrbtConfig getVrbtConfig(String company){
        return companyConfigMap.get(company);
    }

    public String getCompanyByProductId(String productId){
        final Optional<Map.Entry<String, LiantongVrbtConfig>> find = this.companyConfigMap.entrySet().stream().filter(
                entry -> productId.equals(entry.getValue().getProdutcId())).findAny();
        return find.map(Map.Entry::getKey).orElse(null);
    }

}
