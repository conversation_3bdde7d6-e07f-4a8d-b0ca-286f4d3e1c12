package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2022-06-16 15:56
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "wangyiyunmm")
@Slf4j
public class WangyiyunMmProperties {


  /*创建订单地址*/
  private String createOrderUrl;
  /*验证码回填地址*/
  private String verConfirmUrl;
  /*退订地址*/
  private String unsubscribeUrl;
  /*商户密钥*/
  private String mercKey;
  /*商户编号*/
  private String mercId;
  /*应用编号*/
  private String appId;
  /*计费点代码*/
  private String payCode;
  /*资费*/
  private String amount;
  /*扣费通知地址*/
  private String notiUrl;
}
