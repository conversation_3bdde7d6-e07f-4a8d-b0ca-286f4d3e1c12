package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 电信天翼空间配置
 *
 * @author: cai lei
 * @create: 2021-08-04 10:28
 */


@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "tianyi")
public class TianyiSpaceProperties {

    private String payUrl;
    private String getSmsUrl;
    private String confirmUrl;
    private String queryOrderUrl;
    private String relationShipUrl;
    private String queryConsumeByUserUrl;
    private String unsubscribeUrl;
    private String version;
    private String spId;
    private String payId;
    private String goodsName;
    private String rebackUrl;
    private String callbackUrl;
    private String notifyUrl;
    private String extData;
    private String alterPhone;
    private String signKey;
}
