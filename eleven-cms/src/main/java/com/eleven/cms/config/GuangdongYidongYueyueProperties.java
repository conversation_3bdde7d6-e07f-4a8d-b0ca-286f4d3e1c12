package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-08-12 11:18
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.guangdongyueyue")
@Slf4j
public class GuangdongYidongYueyueProperties {
    private String getSmsUrl;
    private String smsCodeUrl;
    private String appId;
    private String appKey;
    private String prodName;
    private String prodId;
}
