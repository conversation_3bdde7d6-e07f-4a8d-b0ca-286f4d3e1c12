package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2022-07-21 11:28
 */

@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "inner.unsub")
public class InnerUnsubProperties {
    private String unionQueryUrl;
    private String unionCancelUrl;
    private String readQueryUrl;
    private String readCancelUrl;
    private String tykjQueryUrl;
    private String tykjCancelUrl;
    private String xmlyCancelUrl;
    private String queryRemoteOrderStatusUrl;
    private String cancelRemoteOrderUrl;
}
