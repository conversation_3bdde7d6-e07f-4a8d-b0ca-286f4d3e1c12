package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "huoshan")
@Slf4j
public class HuoshanProperties {
  /*接口路径*/
  private String url;
  /*appId*/
  private String appId;
  /*appKey*/
  private String appKey;
  /*公钥*/
  private String publicKey;
  /*私钥*/
  private String privateKey;

}
