package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:联通pojie视频彩铃配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "liantong.crack")
public class LiantongVrbtCrackProperties {
    private String a;
    private String vacCode;
    private String getSmsUrl;
    private String smsValidUrl;
}
