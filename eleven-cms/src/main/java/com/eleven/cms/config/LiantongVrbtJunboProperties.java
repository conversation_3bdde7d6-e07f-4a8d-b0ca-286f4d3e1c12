package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: lihb
 * Date: 2022-5-11 15:08:25
 * Desc:骏伯联通沃音乐计费点配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "liantongjunbo.vrbt")
public class LiantongVrbtJunboProperties {

    /**
     * api基地址
     */
    private String apiBaseUrl;
    /**
     * 订阅接口 POST
     */
    private String orderUrl;
    /**
     * 退订验证码获取接口 POST
     */
    private String verifyCodeUrl;
    /**
     * 退订接口 POST
     */
    private String unsubscribeUrl;
    /**
     * 订阅套餐查询接口 POST
     */
    private String querySubInfoUrl;
    /**
     * 订阅结果回调接口 POST
     */
    private String callbackUrl;

    /*
    *  订阅成功跳转链接
    * */
    private String redirectUrl;

    /**
     * 渠道号 骏伯提供
     */
    private String partnerNo;

    /**
     * 产品编码 骏伯提供
     */
    private String productId;

    /**
     * 签名 骏伯提供
     */
    private String key;



}
