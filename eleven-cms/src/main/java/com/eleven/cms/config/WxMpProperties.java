package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2024/5/20 10:42
 * Desc: 微信公众号配置(使用服务号发消息)
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "wx.mp")
public class WxMpProperties {
    /**
     * 设置微信公众号的appid
     */
    private String appId;

    /**
     * 设置微信公众号的app secret
     */
    private String secret;

    /**
     * 获取access_token接口
     */
    private String accessTokenUrl;

    /**
     * 发送模板消息接口
     */
    private String sendTemplateMessageUrl;

    /**
     * 模板消息id
     */
    private String templateMessageId;

    /**
     * 手机号与openId的映射关系
     */
    private Map<String, String> mobileOpenIdMap = new HashMap<>();

}

