package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.hebei")
@Slf4j
public class HebeiYidongVrbtProperties {
    private String getSmsUrl;
    private String sendSmsUrl;

    //公司视频彩铃配置
    private Map<String, HebeiYidongVrbtConfig> companyConfigMap = new HashMap<>();

    //根据公司获取电信视频彩铃配置
    public HebeiYidongVrbtConfig getHebeiMobileConfig(String company) {
        HebeiYidongVrbtConfig hebeiYidongVrbtConfig = companyConfigMap.get(company);
        if (hebeiYidongVrbtConfig == null) {
            log.error("渠道号:{}未找到河北视频彩铃相关配置", company);
            throw new JeecgBootException("无效的河北移动渠道号");
        }
        return hebeiYidongVrbtConfig;
    }
}

