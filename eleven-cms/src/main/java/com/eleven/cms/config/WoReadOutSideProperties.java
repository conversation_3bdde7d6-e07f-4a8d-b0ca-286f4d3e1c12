package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "woread.outside")
@Slf4j
public class WoReadOutSideProperties {
  /*获取token接口路径*/
  private String tokenUrl;
  /*客户端Id；业务对接人申请*/
  private String clientId;
  /*客户端密码；业务对接人申请*/
  private String clientSecret;
  /*话费订购接口路径*/
  private String orderUrl;
  /*必传，套餐索引*/
  private String productpkgid;
  private String notifyUrl;//必传，通知url
  private String returl;//选填，支付完成后的页面跳转地址
  private String channelId;//必传，业务经理申请的渠道ID
  private String pkgOrderedStatusUrl;//必传，查询订单地址

}
