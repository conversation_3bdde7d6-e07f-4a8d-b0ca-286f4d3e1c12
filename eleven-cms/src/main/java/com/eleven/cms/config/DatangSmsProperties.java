package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/2/28 16:16
 * Desc:阿里云短信配置
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "datang.sms",ignoreUnknownFields = true)
public class DatangSmsProperties {
    ////第3版
    private String sendSmsV3ApiUrl;
    //用户名即cpid
    private String usernameV3;
    private String passwordV3;
    private String apiKeyV3;

    //签名扩展映射
    private Map<String,String> signExcodeMap = new HashMap<>();

    //新版使用
    //用户名
    private String username;
    //密码
    private String password;
    //发送短信接口地址
    private String sendSmsNewApiUrl;

    //发送短信接口地址(平台升级临时用)
    //private String sendSmsApiUrlTemp;
    ////用户编号，接入方信息唯一标识(平台升级临时用)
    //private String userIdTemp;
    ////apikey: 由平台方分配(平台升级临时用)
    //private String apiKeyTemp;
    
    //发送短信接口地址
    private String sendSmsApiUrl;
    //用户编号，接入方信息唯一标识
    private String userId;
    //apikey: 由平台方分配
    private String apiKey;

    //用户编号，接入方信息唯一标识(闪信用)
    private String userIdForFlashSms;
    //apikey: 由平台方分配(闪信用)
    private String apiKeyForFlashSms;
    //闪信内容
    private String flashSmsContent;

    //验证码短信模板
    private String validateTemplate;
    //验证码短信模板变量key
    private String validateTemplateParamKey;

    // 联通退订短信模板
    private String unicomUnsubTemplate;
    //pptv权益发送短信模板
    private String pptvRightsTemplate;

    //pptv权益短信模板（视频彩铃）
    private String pptvRightsVrbtTemplate;


    //天翼空间短信模板
    private String validateTemplateTianyi;
    private String validateTemplateTianyiParamKey;

    //短信通知公用模板,只有一个短信签名
    private String notifyTemplate;


    private String validateTemplateUnify;
    private String validateTemplateUnifyParamKey;


    private String validateTemplateEleme;
    private String validateTemplateElemeParamKey;


    //白金会员[随心听]赠送会员权益短信模板(报备)
    private String checkBaijinMemberTemplate;

    //白金会员[随心听]赠送会员权益短信模板(破解)
    private String crackBaijinMemberTemplate;

    //白金会员[宜搜]赠送会员权益短信模板(报备)
    private String checkBaijinysMemberTemplate;

    //白金会员[宜搜]赠送会员权益短信模板(破解)
    private String crackBaijinysMemberTemplate;

    //白金会员[动意]赠送会员权益短信模板(报备)
    private String checkBaijindyMemberTemplate;

    //白金会员[动意]赠送会员权益短信模板(破解)
    private String crackBaijindyMemberTemplate;

    //20元渠道包月包赠送会员未领取权益短信模板
    private String crackBatcMemberTemplate;
    //20元渠道包月包赠送会员未领取权益短信模板
    private String crackBatcNotMemberTemplate;


    //视频彩铃权益赠送通知短信模板
    private String vrbtRightsTemplate;

    //统一短信模板
    private String validateUnifyBizTemplate;
    private String validateUnifyBizTemplateParamKey;


    //视频彩铃订购通知短信模板
    private String vrbtTemplate;


    //上海移动炫视视频彩铃验证码短信模板
    private String shanghaiValidateTemplate;
    private String shanghaiValidateTemplateParamKey;


}
