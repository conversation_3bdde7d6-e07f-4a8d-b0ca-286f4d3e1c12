package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 联联分销产品配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/12 15:46
 **/
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "lianlian-fenxiao")
@Slf4j
public class LianLianFenXiaoProperties {
    //渠道号
    private String channelId;
    //密钥
    private String key;
    //验证-渠道订单创建条件
    private String checkOrderUrl;
    //创建渠道订单
    private String createOrderUrl;
    //订单退款(回收券码)
    private String refundOrderUrl;
    //查询订单详情
    private String queryOrderInfoUrl;
    //订单回调-重发
    private String sendCodeUrl;
    //查询发货订单物流信息
    private String queryLogisticsUrl;
    //短信重发
    private String sendMsgUrl;
    //查询产品信息
    private String queryProductDetailUrl;
    //查询产品列表
    private String queryProductListUrl;
    //查询产品图文详情
    private String queryProductHtmlUrl;
    //查询城市店铺
    private String queryCityShopUrl;
    //查询产品分类
    private String queryProductClassUrl;
    //获取渠道账户信息
    private String queryAccountInfoUrl;
    //查询商家信息
    private String queryShopUrl;
}
