package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:电信视频彩铃配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "dianxin.vrbt")
@Slf4j
public class DianxinVrbtProperties {
    /**
     * api基地址
     */
    private String apiBaseUrl;
    /**
     * 包月产品订购关系查询
     */
    private String queryPackageListUrl;
    /**
     * 包月产品退订
     */
    private String unSubscribeByempUrl;
    /**
     * 一键开通视频彩铃订购包月产品(音乐盒代计费)验证码下发
     */
    private String asyncOpenOrderSendRandomUrl;
    /**
     * 一键开通视频彩铃订购包月产品(音乐盒代计费)下单
     */
    private String asyncOpenAccountOrderPackageBycrbtUrl;
    /**
     * 视频彩铃一键接口订单详情查询
     */
    private String queryOrderUrl;
    /**
     * 使用包月产品权益免费订购视频铃音
     */
    private String addToneFreeOnProductUrl;
    /**
     * 视频彩铃信息查询接口
     */
    private String videoQueryUrl;

    /**
     * H5计费下单发起接口
     */
    private String confirmOrderLaunchedExUrl;

    /**
     * 查询h5订单接口
     */
    private String queryH5OrderUrl;

    /**
     * H5 计费下单发起接口（一键开户订购）
     */
    private String confirmOpenOrderLaunchedExUrl;
    /**
     * H5 计费下单发起接口（EMP 计费）
     */
    private String confirmOrderLaunchedEmpUrl;


    //##########################以下为三方支付新加的接口###################################
    /**
     * 用户信息查询  查询用户的信息，可通过该接口，判断用户是否开通了视频彩铃功能
     */
    private String queryAccountInfoUrl;
    /**
     * H5 计费下单发起接口（第三方校验） 调用该接口生成订单（第三方校验专用）返回订单号、风控状态、产品订购关系、非彩 铃功能用户（含功能费信息）等。
     */
    private String ismpOrderLaunchedThirdUrl;
    /**
     * H5 计费验证码下发接口 调用该接口发送 H5 计费验证码。
     */
    private String ismpVerifyCodeSendUrl;
    /**
     * H5 计费订单确认接口 调用该接口确定 H5 计费订单，回填验证码 验证码鉴权
     */
    private String ismpConfirmOrderUrl;
    /**
     * H5 计费支付确认接口（第三方校验） 订单支付确认，视频彩铃功能开户+0元视频彩铃合作包订购（给权益）
     */
    private String ismpConfirmOrderThirdUrl;
    /**
     * 根据专题 ID 查询相应的视频合成模板内容
     */
    private String queryTemplateVideoUrl;

    /**
     * 获取 CMS 模板合成以及设置 H5 页面的跳转地址，用于给用户完成视频合成以及设置流程
     */
    private String getAiTemplateUrlUrl;

    /**
     * h5下单 计费认证页面操作后的返回地址
     */
    private String returnUrl;

    /**
     * 下发短信手机号
     */
    private List<String> mobiles;

    //公司视频彩铃配置
    private Map<String, DianxinVrbtConfig> companyConfigMap = new HashMap<>();

    //根据公司获取电信视频彩铃配置
    public DianxinVrbtConfig getDianxinVrbtConfig(String company) {
        DianxinVrbtConfig dianxinVrbtConfig = companyConfigMap.get(company);
        if (dianxinVrbtConfig == null) {
            log.error("渠道号:{}未找到电信视频彩铃相关配置", company);
            throw new JeecgBootException("无效的电信渠道号");
        }
        return dianxinVrbtConfig;
    }

    public String getCompanyByPackageId(String packageId) {
       return companyConfigMap.entrySet().stream().filter(entry -> StringUtils.equals(entry.getValue().getPackageId(),packageId))
               .findAny()
               .map(Map.Entry::getKey)
               .orElse(null);
    }
}
