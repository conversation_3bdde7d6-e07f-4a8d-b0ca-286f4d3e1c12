package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 骏伯存量
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "miguhuyu")
public class MiGuHuYuQueryMemberProperties {


    //渠道业务代码配置
    private Map<String, MiGuHuYuChannelConfig> channelConfigMap = new HashMap<>();

    public MiGuHuYuChannelConfig getMiGuHuYuConfigByChannel(String channel){
        return channelConfigMap.get(channel);
    }
}
