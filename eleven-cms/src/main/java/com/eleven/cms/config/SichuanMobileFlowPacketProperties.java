package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: cai lei
 * @create: 2021-09-17 16:19
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "sichuan.flowpacket")
@Slf4j
public class SichuanMobileFlowPacketProperties {



    private String sendRandomCodeUrl; //随机码下发接口
    private String bizOrderUrl; //流量套餐订购接口
    private String contractHandleUrl; //合约办理接口
    private String queryNoticeUrl; //查询通知接口
    private String prepareOrderUrl; //查询通知接口
    private String queryOrderUrl; //查询订单接口
    private String qryPointAnalysiUrl; //投放业务留存率分析能力查询(不需要授权)
    private List<String> mobiles; //通知下发短信手机号
    private String smsSignDatang; //短信签名
    private String smsSuffix; //短信后缀
    private String junboCallbackUrl; //骏伯回传地址


    //公司视频彩铃配置
    private Map<String, SichuanMobileFlowPacketConfig> companyConfigMap = new HashMap<>();
    //渠道业务代码配置
    private Map<String, String> channelBizCodeMap = new HashMap<>();


    //根据公司获取电信视频彩铃配置
    public SichuanMobileFlowPacketConfig getSichuanMobileConfig(String company) {
        SichuanMobileFlowPacketConfig sichuanMobileConfig = companyConfigMap.get(company);
        if (sichuanMobileConfig == null) {
            log.error("渠道号:{}未找到四川移动流量包相关配置", company);
            throw new JeecgBootException("无效的四川移动渠道号");
        }
        return sichuanMobileConfig;
    }

    //根据渠道号获取业务代码
    public String getBizCodeByChannel(String channel) {
        String bizCode = channelBizCodeMap.get(channel);
        if (StringUtils.isEmpty(bizCode)) {
            log.error("渠道号:{}未找到相关业务代码", channel);
            throw new JeecgBootException("无效的四川移动渠道号");
        }
        return bizCode;
    }

    //根据渠道号获取业务代码
    public List<String> getChannelList() {
       return channelBizCodeMap.keySet().stream().collect(Collectors.toList());
    }



}
