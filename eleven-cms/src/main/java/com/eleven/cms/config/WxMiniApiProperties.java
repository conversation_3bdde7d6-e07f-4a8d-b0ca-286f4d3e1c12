package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2021-12-27 10:07
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "wx.miniapi")
@Slf4j
public class WxMiniApiProperties {

    private String appid;
    private String secret;
    private String accessTokenUrl;
    private String userPhonenumberUrl;
    private String amusingIncomingCallAppId;
    private String amusingIncomingCallSecret;
    private String coolIncomingCallAppId;
    private String coolIncomingCallSecret;
    private String coolIncomingCallWxMiniAppId;
    private String coolIncomingCallWxMiniSecret;

}
