package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2022-10-28 10:36:47
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "yidong.vrbt.guangdong")
@Slf4j
public class GuangdongYidongVrbtProperties {
    private String baseUrl;
    private String smsUrl;
    private String sendSmsUrl;
    private String productCode;
    private String operateId;
}
