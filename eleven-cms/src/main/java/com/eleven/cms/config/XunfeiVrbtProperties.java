package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 讯飞视频彩铃开通接口回调参数
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "xunfei.vrbt")
public class XunfeiVrbtProperties {


    private String callbackUrl; //回调接口地址
    private String queryVrbtRingUrl; //查询铃音接口地址
    private String proId;
    private String secretKey;
    private String bid;
    private String pwd;
    private String a;
    private String sourceId;
    private String sourceIdTemp;

    
    private String zeroOrderUrl;
    private String zeroOrderBid;
    private String zeroOrderBpwd;
    private String zeroOrderA;
    private String zeroOrderATemp;

    //渠道业务代码配置
    private Map<String, String> channelOperateTypeIdMap = new HashMap<>();
    private Map<String, String> channelChargeIdMap = new HashMap<>();
    private Map<String, String> vrbtColumnMap = new HashMap<>();


}
