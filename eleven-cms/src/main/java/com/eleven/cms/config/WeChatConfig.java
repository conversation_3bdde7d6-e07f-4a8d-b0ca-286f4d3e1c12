package com.eleven.cms.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import lombok.Data;
import org.apache.shiro.codec.Base64;
import org.apache.http.conn.ssl.DefaultHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.ssl.SSLContexts;
import org.springframework.core.io.ClassPathResource;

import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.util.Enumeration;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeChatConfig {
    private byte[] certData;

//    public static SSLConnectionSocketFactory initCert(String mchId) throws Exception {
//        ClassPathResource classPathResource = new ClassPathResource("apiclient_cert.p12");
//        InputStream certStream = classPathResource.getInputStream();
//        KeyStore keyStore = KeyStore.getInstance("PKCS12");
//        keyStore.load(certStream, mchId.toCharArray());
//        certStream.close();
//        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore,mchId.toCharArray()).build();
//        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, new DefaultHostnameVerifier());
//        return sslsf;
//    }
    public static SSLConnectionSocketFactory initCert(String mchId) throws Exception {
        ClassPathResource classPathResource = new ClassPathResource(mchId+".p12");
        InputStream certStream = classPathResource.getInputStream();
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(certStream, mchId.toCharArray());
        certStream.close();
        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore,mchId.toCharArray()).build();
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, new DefaultHostnameVerifier());
        return sslsf;
    }

    /**
     * 获取  p12证书 私钥
     * @param mchId
     * @return
     * @throws Exception
     */
    public static PrivateKey getPriKeyByP12(String mchId) throws Exception {
        ClassPathResource classPathResource = new ClassPathResource(mchId+".pem");
        InputStream certStream = classPathResource.getInputStream();
        PrivateKey privateKey = PemUtil.loadPrivateKey(certStream);
        return privateKey;
    }
}
