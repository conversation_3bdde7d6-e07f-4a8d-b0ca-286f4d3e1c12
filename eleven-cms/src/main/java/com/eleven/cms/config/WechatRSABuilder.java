package com.eleven.cms.config;

import com.wechat.pay.java.core.AbstractRSAConfigBuilder;
import com.wechat.pay.java.core.certificate.CertificateProvider;
import com.wechat.pay.java.core.certificate.RSAAutoCertificateProvider;
import com.wechat.pay.java.core.http.AbstractHttpClientBuilder;
import com.wechat.pay.java.core.http.HttpClient;
import com.wechat.pay.java.core.util.IOUtil;
import com.wechat.pay.java.core.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;

import static java.util.Objects.requireNonNull;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/11 15:01
 **/
@Slf4j
@Service
public class WechatRSABuilder extends AbstractRSAConfigBuilder<WechatRSABuilder> {
    protected HttpClient httpClient;
    protected byte[] apiV3Key;
    protected CertificateProvider certificateProvider;
    protected AbstractHttpClientBuilder<?> httpClientBuilder;
    protected String merchantId;
    protected PrivateKey privateKey;
    protected String merchantSerialNumber;

    public WechatRSABuilder apiV3Key(String apiV3key) {
        this.apiV3Key = apiV3key.getBytes(StandardCharsets.UTF_8);
        return self();
    }

    public WechatRSABuilder httpClient(HttpClient httpClient) {
        this.httpClient = httpClient;
        return this;
    }

    public WechatRSABuilder httpClientBuilder(AbstractHttpClientBuilder<?> builder) {
        httpClientBuilder = builder;
        return this;
    }

    //使用input流加载key
    public WechatRSABuilder privateKeyFromInputStream(InputStream inputStream) throws IOException {
        try {
            String s = IOUtil.toString(inputStream);
            super.privateKey = PemUtil.loadPrivateKeyFromString(s);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close(); // 关闭
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return self();
    }


    @Override
    protected WechatRSABuilder self() {
        return this;
    }

    public WechatRSAConfig build() {
        this.merchantId = super.merchantId;
        this.merchantSerialNumber = super.merchantSerialNumber;
        this.privateKey = super.privateKey;
        RSAAutoCertificateProvider.Builder providerBuilder =
                new RSAAutoCertificateProvider.Builder()
                        .merchantId(requireNonNull(merchantId))
                        .apiV3Key(requireNonNull(apiV3Key))
                        .privateKey(requireNonNull(privateKey))
                        .merchantSerialNumber(requireNonNull(merchantSerialNumber));

        if (httpClient != null) {
            providerBuilder.httpClient(httpClient);
        }

        if (httpClientBuilder != null) {
            providerBuilder.httpClientBuilder(httpClientBuilder);
        }

        certificateProvider = providerBuilder.build();

        return new WechatRSAConfig(this);
    }
}
