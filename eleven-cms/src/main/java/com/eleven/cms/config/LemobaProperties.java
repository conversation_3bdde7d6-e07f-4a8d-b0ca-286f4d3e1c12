package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:联通视频彩铃配置
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "lemoba")
public class LemobaProperties {

    /**
     * h5个人中心跳链
     */
    private String userCenterUrl;
    /**
     * 退款接口
     */
    private String refundUrl;
    /**
     * aes密钥
     */
    private String aesKey;
//    /**
//     * 权益id 6分钟
//     */
//    private String rightsId6Min;
//    /**
//     * 权益id 12分钟
//     */
//    private String rightsId12Min;

    private Map<String,Channel> channelMap;
}
