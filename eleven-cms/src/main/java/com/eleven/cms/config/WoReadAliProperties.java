package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@Slf4j
public class WoReadAliProperties {
  /*微信支付配置*/
  private String body;//必传，产品名
  private String aliNotifyUrl;//必传，通知url
  private String aliReturnUrl;//必传，回跳页面地址
  private String channelId;//必传，业务经理申请的渠道ID
  private String aliSingleNotifyUrl;//必传，业务经理申请的渠道ID
  private String fee;//选传，总金额，单位分

}
