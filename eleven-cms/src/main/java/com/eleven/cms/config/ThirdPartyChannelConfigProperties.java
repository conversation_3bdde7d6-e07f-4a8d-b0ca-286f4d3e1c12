package com.eleven.cms.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2020/8/6 11:22
 * Desc: 咪咕安全key封装
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "third.party")
public class ThirdPartyChannelConfigProperties {

    private Map<String, ThirdPartyChannelConfig> miguChannelConfigMap = new HashMap<>();

    public boolean isThirdPartyChannel(String channelCode) {
        return miguChannelConfigMap.containsKey(channelCode);
    }


    public ThirdPartyChannelConfig getThirdPartyChannelConfig(String channelCode) {
        ThirdPartyChannelConfig thirdPartyChannelConfig = miguChannelConfigMap.get(channelCode);
        if (thirdPartyChannelConfig == null) {
            throw new IllegalArgumentException("渠道号错误");
        }
        return thirdPartyChannelConfig;
    }
}
