package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: lihb
 * @create: 2022-9-21 16:52:40
 */
@Data
@SpringBootConfiguration
@Slf4j
public class WoReadWxProperties {
  /*微信支付配置*/
  private String body;//必传，产品名
  private String attach;//必传，附加信息
  private String wxNotifyUrl;//必传，通知url
  private String tradeType;//必传，交易类型JSAPI,NATIVE,APP,MWEB
  private String planId;//协议模板id,连续订购时必传
  private String wxCallbackurl;//选填，支付完成后的页面跳转地址
  private String channelId;//必传，业务经理申请的渠道ID
  private String wxSingleNotifyUrl;//必传，业务经理申请的渠道ID

}
