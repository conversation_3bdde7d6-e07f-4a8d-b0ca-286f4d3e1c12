package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "test",ignoreUnknownFields = true)
public class SubscribeServiceTestProperties {
    private Map<String, TestAccount> testAccountMap = new HashMap<>();
}
