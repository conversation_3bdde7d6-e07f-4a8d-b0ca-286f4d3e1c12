package com.eleven.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "pptv.config",ignoreUnknownFields = true)
public class PPTVConfigProperties {
    private String key;
    private String channel;
    private String productId;
    private String pptvUrl;
    private Map<String, PPTVRechargeMap> pptvRechargeMap = new HashMap<>();
}
