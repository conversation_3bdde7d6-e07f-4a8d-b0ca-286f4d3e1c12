package com.eleven.cms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: cai lei
 * @create: 2022-01-05 10:48
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "huanqiuwang.dianxin.vrbt")
@Slf4j
public class HuanqiuwangDianxinVrbtProperties {
    private String channel;
    private String appid;
    private String feeid;
    private String chnl;
    private String sid;
    private String orderUrl;
    private String bindUrl;
    private String returnUrl;
}
