package com.eleven.cms.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: yang tao
 * @create: 2025-02-19 14:26
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiLinMobileConfig implements Serializable {


    /**
     * channelId : 01
     * routeKey : 10
     * tenantId : 22
     * busiCode : 1104
     * effEctive : 0
     * channelType : 01
     * loginNo : ssXX7km73
     * traceId :
     * opCode :
     * opNote :
     * attrValue :
     * attrId :
     * attrName :
     * attrType :
     */

    @JsonProperty("channelId")
    private String channelId;
    @JsonProperty("routeKey")
    private String routeKey;
    @JsonProperty("tenantId")
    private String tenantId;
    @JsonProperty("busiCode")
    private String busiCode;
    @JsonProperty("effEctive")
    private String effEctive;
    @JsonProperty("channelType")
    private String channelType;
    @JsonProperty("loginNo")
    private String loginNo;
    @JsonProperty("traceId")
    private String traceId;
    @JsonProperty("opCode")
    private String opCode;
    @JsonProperty("opNote")
    private String opNote;
    @JsonProperty("attrValue")
    private String attrValue;
    @JsonProperty("attrValueShow")
    private String attrValueShow;
    @JsonProperty("attrId")
    private String attrId;
    @JsonProperty("attrName")
    private String attrName;
    @JsonProperty("attrType")
    private String attrType;
    @JsonProperty("pwdType")
    private String pwdType;
    @JsonProperty("contactId")
    private String contactId;
    @JsonProperty("regionId")
    private String regionId;
    @JsonProperty("chnFlag")
    private String chnFlag;

}
