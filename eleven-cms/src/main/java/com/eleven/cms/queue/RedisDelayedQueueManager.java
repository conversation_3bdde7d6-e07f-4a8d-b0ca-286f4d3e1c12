package com.eleven.cms.queue;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @desc 延时队列管理
 */
@Slf4j
@Component
public class RedisDelayedQueueManager {

    //队列名称
    public static final String DELAY_MESSAG_QUEUE_NAME = "delayMessagQueue";
    public static final String CALLBACK_NOTIFY_MESSAG_QUEUE_NAME = "callbackNotifyMessagQueue";
    public static final String BACT_DELAY_MESSAG_QUEUE_NAME = "bactDelayeMessagQueue";
    public static final String BJHY_DELAY_MESSAG_QUEUE_NAME = "bjhyDelayeMessagQueue";
    public static final String QYCL_DELAY_MESSAG_QUEUE_NAME = "qyclDelayeMessagQueue";
    public static final String JSYD_DELAY_MESSAG_QUEUE_NAME = "jsydDelayeMessagQueue";
    public static final String DATA_NOTIFY_DELAY_MESSAG_QUEUE_NAME = "dataNotifyDelayeMessagQueue";
    public static final String UN_SIGN_NOTIF_DELAY_MESSAG_QUEUE_NAME = "unSignNotifyDelayeMessagQueue";
    public static final String PAY_NOTIFY_DELAY_MESSAG_QUEUE_NAME = "payNotifyDelayeMessagQueue";
    public static final String REFUND_NOTIFY_DELAY_MESSAG_QUEUE_NAME = "refundNotifyDelayeMessagQueue";
    //消息tag
    public static final String MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY = "SUBSCRIBE_DELAY_VERIFY";
    public static final String MESSAG_TAG_SUBSCRIBE_DELAY_10S_VRBT_ORDER = "SUBSCRIBE_DELAY_10S_VRBT_ORDER";
    public static final String MESSAG_TAG_SUBSCRIBE_DELAY_1DAY_VRBT_ORDER = "SUBSCRIBE_DELAY_1DAY_VRBT_ORDER";
    public static final String MESSAG_TAG_SUBSCRIBE_DELAY_VRBT_RECHECK = "SUBSCRIBE_DELAY_VRBT_RECHECK";
    //消息extra
    public static final String MESSAG_EXTRA_30_MIN = "30_MIN";
    public static final String MESSAG_EXTRA_60_MIN = "60_MIN";
    public static final String MESSAG_EXTRA_3_DAY = "1_DAY";


    //退订上报消息extra
    public static final String MESSAG_EXTRA_UNSUB_1_DAY = "UNSUB_1_DAY";

    //延迟通知时间(5分钟)
    public static Integer DELAY_NOTICE_TIME = 5;



    @Autowired
    RedissonClient redissonClient;
    @Autowired
    private DelayedTaskService delayedTaskService;
    @Autowired
    private CallbackNotifyTaskService callbackNotifyTaskService;

    @Autowired
    private BactDelayedTaskService bactDelayedTaskService;

    @Autowired
    private BjhyDelayedTaskService bjhyDelayedTaskService;

    @Autowired
    private QyclDelayedTaskService qyclDelayedTaskService;

    @Autowired
    private JsydDelayedTaskService jsydDelayedTaskService;

    @Autowired
    private DataNotifyDelayedService dataNotifyDelayedService;
    @Autowired
    private UnSignNotifyDelayedService unSignNotifyDelayedService;
    @Autowired
    private PayNotifyDelayedService payNotifyDelayedService;
    @Autowired
    private RefundNotifyDelayedService refundNotifyDelayedService;

    private RBlockingQueue<DelayedMessage> blockingFairQueue;
    private RDelayedQueue<DelayedMessage> delayedQueue;


    private RBlockingQueue<CallbackNotifyMessage> callbackNotifyBlockingFairQueue;
    private RDelayedQueue<CallbackNotifyMessage> callbackNotifyQueue;


    private RBlockingQueue<BactDelayedMessage> bactDelayeFairQueue;
    private RDelayedQueue<BactDelayedMessage> bactDelayeQueue;

    private RBlockingQueue<BjhyDelayedMessage> bjhyDelayeFairQueue;
    private RDelayedQueue<BjhyDelayedMessage> bjhyDelayeQueue;

    private RBlockingQueue<QyclDelayedMessage> qyclBlockingFairQueue;
    private RDelayedQueue<QyclDelayedMessage> qyclNotifyQueue;

    private RBlockingQueue<JsydDelayedMessage> jsydBlockingFairQueue;
    private RDelayedQueue<JsydDelayedMessage> jsydNotifyQueue;


    private RBlockingQueue<DataNotifyDelayedMessage> dataNotifyDelayeFairQueue;
    private RDelayedQueue<DataNotifyDelayedMessage> dataNotifyDelayeQueue;


    private RBlockingQueue<UnSignNotifyDelayedMessage> unSignNotifyDelayeFairQueue;
    private RDelayedQueue<UnSignNotifyDelayedMessage> unSignNotifyDelayeQueue;


    private RBlockingQueue<PayNotifyDelayedMessage> payNotifyDelayeFairQueue;
    private RDelayedQueue<PayNotifyDelayedMessage> payNotifyDelayeQueue;


    private RBlockingQueue<RefundNotifyDelayedMessage> refundNotifyDelayeFairQueue;
    private RDelayedQueue<RefundNotifyDelayedMessage> refundNotifyDelayeQueue;




    @PostConstruct
    @DependsOn(value = {"redissonClient"})
    public void init(){
//        this.blockingFairQueue = redissonClient.getBlockingQueue(DELAY_MESSAG_QUEUE_NAME);
//        this.delayedQueue = redissonClient.getDelayedQueue(blockingFairQueue);
//
//        this.callbackNotifyBlockingFairQueue = redissonClient.getBlockingQueue(CALLBACK_NOTIFY_MESSAG_QUEUE_NAME);
//        this.callbackNotifyQueue = redissonClient.getDelayedQueue(callbackNotifyBlockingFairQueue);
//
//
//        this.bactDelayeFairQueue = redissonClient.getBlockingQueue(BACT_DELAY_MESSAG_QUEUE_NAME);
//        this.bactDelayeQueue = redissonClient.getDelayedQueue(bactDelayeFairQueue);
//
//
//        this.bjhyDelayeFairQueue = redissonClient.getBlockingQueue(BJHY_DELAY_MESSAG_QUEUE_NAME);
//        this.bjhyDelayeQueue = redissonClient.getDelayedQueue(bjhyDelayeFairQueue);
//
//        this.qyclBlockingFairQueue = redissonClient.getBlockingQueue(QYCL_DELAY_MESSAG_QUEUE_NAME);
//        this.qyclNotifyQueue = redissonClient.getDelayedQueue(qyclBlockingFairQueue);
//
//        this.jsydBlockingFairQueue = redissonClient.getBlockingQueue(JSYD_DELAY_MESSAG_QUEUE_NAME);
//        this.jsydNotifyQueue = redissonClient.getDelayedQueue(jsydBlockingFairQueue);
//
//
//        this.dataNotifyDelayeFairQueue = redissonClient.getBlockingQueue(DATA_NOTIFY_DELAY_MESSAG_QUEUE_NAME);
//        this.dataNotifyDelayeQueue = redissonClient.getDelayedQueue(dataNotifyDelayeFairQueue);
//
//        this.unSignNotifyDelayeFairQueue = redissonClient.getBlockingQueue(UN_SIGN_NOTIF_DELAY_MESSAG_QUEUE_NAME);
//        this.unSignNotifyDelayeQueue = redissonClient.getDelayedQueue(unSignNotifyDelayeFairQueue);
//
//        this.payNotifyDelayeFairQueue = redissonClient.getBlockingQueue(PAY_NOTIFY_DELAY_MESSAG_QUEUE_NAME);
//        this.payNotifyDelayeQueue = redissonClient.getDelayedQueue(payNotifyDelayeFairQueue);
//
//
//        this.refundNotifyDelayeFairQueue = redissonClient.getBlockingQueue(REFUND_NOTIFY_DELAY_MESSAG_QUEUE_NAME);
//        this.refundNotifyDelayeQueue = redissonClient.getDelayedQueue(refundNotifyDelayeFairQueue);
//
//        this.processMessage();
    }

    /**
     * 添加元素到延时队列
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void add(DelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            delayedQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * 添加元素到回调延时延时队列
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addCallbcakNotify(CallbackNotifyMessage message, long delay, TimeUnit timeUnit) {
        try {
            callbackNotifyQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加元素到延时队列(北岸唐唱)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addBact(BactDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            bactDelayeQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 添加元素到延时队列(数据回执)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addDataNotify(DataNotifyDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            dataNotifyDelayeQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 添加元素到延时队列(白金会员)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addBjhy(BjhyDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            bjhyDelayeQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加元素到延时队列(企业彩铃)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addQycl(QyclDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            qyclNotifyQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加元素到延时队列(企业彩铃)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addJsyd(JsydDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            jsydNotifyQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 添加元素到延时队列(长沙退款通知)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addRefundNotify(RefundNotifyDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            refundNotifyDelayeQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 添加元素到延时队列(长沙解约通知)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addUnSignNotify(UnSignNotifyDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            unSignNotifyDelayeQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }






    /**
     * 添加元素到延时队列(长沙支付通知)
     *
     * @param message    队列成员
     * @param delay     延时时间
     * @param timeUnit  时间单位
     */
    public void addPayNotify(PayNotifyDelayedMessage message, long delay, TimeUnit timeUnit) {
        try {
            payNotifyDelayeQueue.offer(message, delay, timeUnit);
            //delayedQueue.destroy();  //此处不能destory,否则会一致取不出消息,直到下一条消息进来
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理到期的消息
     *
     */
    public void processMessage() {
        //RBlockingQueue<T> blockingFairQueue = redissonClient.getBlockingQueue(DELAY_MESSAG_QUEUE_NAME);
        ////此处必须初始化一个延迟队列,否则重启jvm后就拿不到之前的消息
        //redissonClient.getDelayedQueue(blockingFairQueue);
        //noinspection InfiniteLoopStatement

        //由于此线程需要常驻，可以新建线程，不用交给线程池管理
        /*((Runnable) () -> {
            while (true) {
                try {
                    Thread.sleep(1000);
                    T t = blockingFairQueue.take();
                    delayedTaskListener.invoke(t);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).run();*/

        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    //delayedTaskListener.invoke(blockingFairQueue.poll(1,TimeUnit.SECONDS));
                    delayedTaskService.handleDelayMessage(blockingFairQueue.take());
                    //TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        ExecutorService callbackNotifyexecutor = Executors.newSingleThreadExecutor();
        callbackNotifyexecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============回调任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    //delayedTaskListener.invoke(blockingFairQueue.poll(1,TimeUnit.SECONDS));
                    callbackNotifyTaskService.handleCallbackNotifyMessage(callbackNotifyBlockingFairQueue.take());
                    //TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });


        ExecutorService bactExecutor = Executors.newSingleThreadExecutor();
        bactExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============回调任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    //delayedTaskListener.invoke(blockingFairQueue.poll(1,TimeUnit.SECONDS));
                    bactDelayedTaskService.handleDelayMessage(bactDelayeFairQueue.take());
                    //TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });


        ExecutorService bjhyExecutor = Executors.newSingleThreadExecutor();
        bjhyExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============回调任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    //delayedTaskListener.invoke(blockingFairQueue.poll(1,TimeUnit.SECONDS));
                    bjhyDelayedTaskService.handleDelayMessage(bjhyDelayeFairQueue.take());
                    TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });


        ExecutorService qyclExecutor = Executors.newSingleThreadExecutor();
        qyclExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============企业彩铃任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    //delayedTaskListener.invoke(blockingFairQueue.poll(1,TimeUnit.SECONDS));
                    qyclDelayedTaskService.handleDelayMessage(qyclBlockingFairQueue.take());
                    TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        ExecutorService jsydExecutor = Executors.newSingleThreadExecutor();
        jsydExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============企业彩铃任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    //delayedTaskListener.invoke(blockingFairQueue.poll(1,TimeUnit.SECONDS));
                    jsydDelayedTaskService.handleDelayMessage(jsydBlockingFairQueue.take());
                    TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });



        ExecutorService dataNotifyExecutor = Executors.newSingleThreadExecutor();
        dataNotifyExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============数据回执任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    dataNotifyDelayedService.handleDataNotifyDelayMessage(dataNotifyDelayeFairQueue.take());
                    TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });



        ExecutorService unSignNotifyExecutor = Executors.newSingleThreadExecutor();
        unSignNotifyExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============数据回执任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    unSignNotifyDelayedService.handleUnSignNotifyDelayMessage(unSignNotifyDelayeFairQueue.take());
                    TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });



        ExecutorService refundNotifyExecutor = Executors.newSingleThreadExecutor();
        refundNotifyExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============数据回执任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    refundNotifyDelayedService.handleRefundNotifyDelayMessage(refundNotifyDelayeFairQueue.take());
                    TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });



        ExecutorService payNotifyExecutor = Executors.newSingleThreadExecutor();
        payNotifyExecutor.submit(() -> {
            try {
                TimeUnit.SECONDS.sleep(20L);
                log.info("===============数据回执任务延时队列监听器启动==============");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!redissonClient.isShutdown()) {
                try {
                    payNotifyDelayedService.handlePayNotifyDelayMessage(payNotifyDelayeFairQueue.take());
                    TimeUnit.SECONDS.sleep(1);
                    //log.info("收到延迟消息");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

    }
}
