package com.eleven.cms.queue;

import com.eleven.cms.remote.ChangShaAliPayNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 数据回执延迟任务处理
 */
@Slf4j
@Component
public class UnSignNotifyDelayedService {
    @Autowired
    ChangShaAliPayNotifyService changShaAliPayNotifyService;

    public void handleUnSignNotifyDelayMessage(UnSignNotifyDelayedMessage unSignNotifyDelayedMessage) {
        log.info("长沙解约通知收到延迟消息:{}",unSignNotifyDelayedMessage);
        changShaAliPayNotifyService.unSignNotify(unSignNotifyDelayedMessage.getExternalAgreementNo(),unSignNotifyDelayedMessage.getBusinessType(),false);
    }

}
