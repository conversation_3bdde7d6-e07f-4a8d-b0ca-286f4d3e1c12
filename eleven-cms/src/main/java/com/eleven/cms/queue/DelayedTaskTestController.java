package com.eleven.cms.queue;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/test")
public class DelayedTaskTestController {

    @Autowired
    private RedisDelayedQueueManager redisDelayedQueueManager;

    @RequestMapping("/message")
    public String addDelayedMessage(Integer delay, String value) {
        log.info("delay:{},message:{}",delay,value);
        redisDelayedQueueManager.add(DelayedMessage.builder().msg(value).build(), delay, TimeUnit.SECONDS);
        return "OK";
    }
}