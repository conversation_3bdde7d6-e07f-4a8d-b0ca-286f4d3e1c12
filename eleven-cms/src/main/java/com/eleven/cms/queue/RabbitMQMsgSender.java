package com.eleven.cms.queue;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.EsInsertCode;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Author: <EMAIL>
 * Date: 2023/5/10 11:36
 * Desc: Rabbitmq消息发送
 */
@Component
@Slf4j
public class RabbitMQMsgSender {
    public static final String LOG_TAG = "RabbitMQ消息发送";
    //    @Autowired
//    @Lazy
//    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;

    /**
     * 添加包月状态延迟校验任务
     *
     * @param subscribe
     */
    public void addDelayedVerifyMessage(Subscribe subscribe) {
    }

    /**
     * 添加包月状态延迟校验任务(企业彩铃专用) 企业彩铃只要1小时和1天的包月状态校验
     *
     * @param subscribe
     */
    public void addQyclDelayedVerifyMessage(Subscribe subscribe) {
    }


    /**
     * 发送1小时包月校验消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeVerifyDelay1hourMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 发送3天包月校验消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeVerifyDelay3DayMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 发送1天退订校验消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeVerifyUnsubDelay1DayMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 发送10秒延迟视频彩铃订购消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeVrbtOrderDelay10secondMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 发送10分钟延迟开通结果判定消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeResultJudgeDelay10minuteMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 发送20分钟延迟开通结果判定消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeResultJudgeDelay20minuteMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 发送30分钟延迟开通结果判定消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeResultJudgeDelay30minuteMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 发送1天延迟视频彩铃订购消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendSubscribeVrbtOrderDelay1dayMessage(DelayedMessage delayedMessage) {
    }

    /**
     * 根据次数发送订购结果延迟查询消息到RabbitMQ延迟队列
     *
     * @param
     */
    public void sendSubscribeVrbtRecheckDelayMessage(String subscribeId, int timesCount) {
    }

    /**
     * 发送回调通知消息到RabbitMQ延迟队列
     *
     * @param callbackNotifyMessage
     */
    public void sendOutsideCallbackMessage(CallbackNotifyMessage callbackNotifyMessage) {
    }

    /**
     * 发送广告平台上报消息到RabbitMQ延迟队列
     *
     * @param adReportMessage
     */
    public void sendAdReportMessage(JsonNode adReportMessage) {
    }

    /**
     * 发送企业微信上报消息到RabbitMQ延迟队列
     *
     * @param adReportMessage
     */
    public void sendQywxMessage(JsonNode adReportMessage) {
    }

    /**
     * 发送支付宝周期扣款消息到RabbitMQ延迟队列
     *
     * @param alipayDeductMessage
     */
    public void sendAlipayDeductMessage(AlipayDeductMessage alipayDeductMessage) {
    }


    /**
     * 企业彩铃数据回执发送1小时包月校验消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendDataNotifyDelay1hourMessage(DataNotifyDelayedMessage delayedMessage) {
    }

    /**
     * 企业彩铃数据回执发送1天包月校验消息到RabbitMQ延迟队列
     *
     * @param delayedMessage
     */
    public void sendDataNotifyDelay1DayMessage(DataNotifyDelayedMessage delayedMessage) {
    }


    /**
     * 发送河图权益发送券码消息到普通队列
     *
     * @param sendCodeDeductMessage
     */
    public void heTuSendCodeQueueMessage(SendCodeDeductMessage sendCodeDeductMessage) {
    }


    /**
     * 发送支付宝视频彩铃充值消息到普通队列
     *
     * @param aliPayVrbtMessage
     */
    public void aliPayVrbtQueueMessage(AliPayVrbtMessage aliPayVrbtMessage) {
    }


    /**
     * 发送支付宝扣款提醒短信消息到普通队列
     *
     * @param alipayDeductSendMessage
     */
    public void sendAlipayDeductSendMessage(AlipayDeductSendMessage alipayDeductSendMessage) {
    }


    /**
     * 发送联通铃音上传消息到普通队列
     *
     * @param liantongUploadRingMessage
     */
    public void liantongUploadRingQueueMessage(LiantongUploadRingMessage liantongUploadRingMessage) {
    }


    /**
     * 发送酷狗权益充值消息到RabbitMQ延迟队列
     *
     * @param kuGouRightsRechargeMessage
     */
    public void sendKuGouRightsRechargeMessage(KuGouRightsRechargeMessage kuGouRightsRechargeMessage) {
    }


    /**
     * 发送思维方阵券码消息到普通队列
     *
     * @param sendSmsCodeDeductMessage
     */
    public void swfzSendCodeQueueMessage(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
    }


    /**
     * 发送大鱼消除券码消息到普通队列
     *
     * @param sendSmsCodeDeductMessage
     */
    public void dyxcSendCodeQueueMessage(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
    }


    /**
     * 发送厨房大逃亡券码消息到普通队列
     *
     * @param sendSmsCodeDeductMessage
     */
    public void cfdtwSendCodeQueueMessage(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
    }

    /**
     * 发送我自为道券码消息到普通队列
     *
     * @param sendSmsCodeDeductMessage
     */
    public void wzwdSendCodeQueueMessage(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
    }


    /**
     * 发送短剧券码消息到普通队列
     *
     * @param sendSmsCodeDeductMessage
     */
    public void duanjuSendCodeQueueMessage(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
    }

    /**
     * 发送水晶传说券码消息到普通队列
     *
     * @param sendSmsCodeDeductMessage
     */
    public void sjcsSendCodeQueueMessage(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
    }

    /**
     * 发送包月续订短信消息到RabbitMQ延迟队列
     *
     * @param monthRenewSubSmsMessage
     */
    public void monthRenewSubSmsMessage(MonthRenewSubSmsMessage monthRenewSubSmsMessage) {
    }


    /**
     * 南山途牛权益发送券码消息到普通队列
     *
     * @param tuniuSendCodeDeductMessage
     */
    public void tuniuSendCodeQueueMessage(TuniuSendCodeDeductMessage tuniuSendCodeDeductMessage) {
    }


    /**
     * 发送贵州移动省包（在线支付）领取权益消息到普通队列
     *
     * @param guiZhouMobilePayRechargeDeductMessage
     */
    public void guiZhouMobilePayRechargeQueueMessage(GuiZhouMobilePayRechargeDeductMessage guiZhouMobilePayRechargeDeductMessage) {
    }

    /**
     * 南山联联权益发送券码消息到普通队列
     *
     * @param lianLianSendCodeDeductMessage
     */
    public void lianlianSendCodeQueueMessage(LianLianSendCodeDeductMessage lianLianSendCodeDeductMessage) {
    }

    /**
     * 影楼铃音上传消息到普通队列
     *
     * @param yinglouRingUploadDeductMessage
     */
    public void yinglouRingUploadQueueMessage(YinglouRingUploadDeductMessage yinglouRingUploadDeductMessage) {
    }

    /**
     * 发送咪咕FTP铃音上传消息
     *
     * @param miguRingFtpUploadMessage
     */
    public void sendMiguRingFtpUploadMessage(MiguRingFtpUploadMessage miguRingFtpUploadMessage) {
    }

    /**
     * 发送app开通事件消息
     *
     * @param subscribe
     */
    public void sendAppSubscribeEventMessage(Subscribe subscribe) {
    }
    /**
     * 转发app海艺回调
     *
     * @param jsonNode
     */
    public void sendAppSubscribeHaiyiNotifyMessage(JsonNode jsonNode) {
    }

    /**
     * 发送暗黑主宰券码消息到普通队列
     *
     * @param sendSmsCodeDeductMessage
     */
    public void ahzzSendCodeQueueMessage(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
    }

    /**
     * 发送领取权益消息消息到RabbitMQ延迟队列
     *
     * @param mobile      mobile
     * @param channelCode channelCode
     */
    public void sendSchSpecialProductSubMsg(String mobile, String channelCode) {
    }


    /**
     * 发送咪咕视频彩铃三方支付包月消息到RabbitMQ延迟队列
     *
     * @param mgVrbtPayDeductMessage
     */
    public void mgVrbtPayDeductMessage(MgVrbtPayDeductMessage mgVrbtPayDeductMessage) {
    }

    /**
     * 发送报备页下单消息
     *
     * @param jsonNode jsonNode
     */
    public void reportPageOrderMessage(JsonNode jsonNode) {
    }

    /**
     * 发送免费包月订购视频彩铃消息到RabbitMQ延迟队列
     *
     * @param subFreeMessage
     */
    public void sendFreeMonthSubVrbtMessage(SubFreeMessage subFreeMessage) {
    }

    /**
     * 发送新增插码消息
     *
     * @param esInsertCode esInsertCode
     */
    public void sendInsertCodeAddMessage(EsInsertCode esInsertCode) {
    }



    /**
     * 发送甘肃移动包月消息到RabbitMQ延迟队列
     *
     * @param jsonNode
     */
    public void gansuMobileDeductMessage(JsonNode jsonNode) {
    }



    /**
     * 多彩多点商超权益发送券码消息到普通队列
     * @param duodianSendCodeDeductMessage
     */
    public void duodianSendCodeQueueMessage(DuodianSendCodeDeductMessage duodianSendCodeDeductMessage){
    }

    /**
     * 发送领取权益消息消息到RabbitMQ延迟队列
     *
     * @param mobile mobile
     * @param channelCode channelCode
     */
    public void sendSchRingPublishMsg(String mobile, String channelCode) {
    }


    /**
     * 发送游戏券码消息到普通队列
     *
     * @param gameSendCodeDeductMessage
     */
    public void gameSendCodeQueueMessage(GameSendCodeDeductMessage gameSendCodeDeductMessage) {
    }



    /**
     * 发送四川移动渝姐权益充值消息到RabbitMQ延迟队列
     *
     * @param jsonNode
     */
    public void sichuanMobileYuJieReChargeDeductMessage(JsonNode jsonNode) {
    }

    /**
     * 发送四川移动包月校验消息到RabbitMQ延迟队列
     *
     * @param jsonNode
     */
    public void sichuanMobileMonthlyVerifyDeductMessage(JsonNode jsonNode) {
    }
}
