package com.eleven.cms.queue;

import com.eleven.cms.remote.ChangShaAliPayNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 长沙支付通知
 */
@Slf4j
@Component
public class PayNotifyDelayedService {
    @Autowired
    ChangShaAliPayNotifyService changShaAliPayNotifyService;
    public void handlePayNotifyDelayMessage(PayNotifyDelayedMessage payNotifyDelayedMessage) {
        log.info("长沙支付通知收到延迟消息:{}",payNotifyDelayedMessage);
        changShaAliPayNotifyService.payNotify(payNotifyDelayedMessage.getOrderId(),payNotifyDelayedMessage.getBusinessType(),false);
    }
}
