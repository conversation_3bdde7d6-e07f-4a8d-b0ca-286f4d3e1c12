package com.eleven.cms.queue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.concurrent.DelayQueue;

/**
 * Author: <EMAIL>
 * Date: 2021/5/26 16:06
 * Desc: 延迟消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DelayedMessage implements Serializable {
    private String id;
    private String tag;
    private String msg;
    private String extra;

    public static void main(String[] args) {
        final DelayedMessage message = DelayedMessage.builder()
                                                   .id("123")
                                                   .msg("456")
                                                   .build();
        System.out.println("message = " + message);

    }
}
