package com.eleven.cms.queue;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.remote.JiangsuYidongService;
import com.eleven.cms.remote.XunfeiJingxianVrbtService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.queue.RedisDelayedQueueManager.*;
import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * 江苏移动订单查询
 */
@Slf4j
@Component
public class JsydDelayedTaskService {

    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IChannelService channelService;
    @Autowired
    XunfeiJingxianVrbtService xunfeiJingxianVrbtService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private ChannelOwnerService channelOwnerService;

    public void handleDelayMessage(JsydDelayedMessage delayMessage) {
        log.info("收到江苏移动订单校验延迟消息:{}", delayMessage);
        String mobile = delayMessage.getMobile();
        String orderId = delayMessage.getOrderId();
        Integer count = delayMessage.getCount();
        Long orderTimestamp = delayMessage.getOrderTimeStamp();
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, orderId);
        //JiangsuResponseQueryInfo jiangsuResponseQueryInfo = jiangsuYidongService.queryOrder(mobile, orderId, jiangsuYidongService.getToken());
        //if (jiangsuResponseQueryInfo.isOk()) {
        boolean isVrbtMember = jiangsuYidongService.queryOrderMigu(mobile);
        if (isVrbtMember) {
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
            subscribe.setResult("交易成功");
            subscribe.setOpenTime(new Date());
            //写入开通成功自增序列
            subscribeService.saveChannelLimit(subscribe);
            //回传到讯飞
            xunfeiJingxianVrbtService.resultNewCallback(subscribe.getMobile(), "014X02E", subscribe.getStatus(), subscribe.getResult());
        } else {
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult("交易失败");
            //失败需要判断状态
            //if (jiangsuResponseQueryInfo.reconfirm()) {
            long nextTimestamp = 0L;
            if (count == 1) {
                nextTimestamp = orderTimestamp + 5 * 60 - System.currentTimeMillis() / 1000;
            } else if (count == 2) {
                nextTimestamp = orderTimestamp + 10 * 60 - System.currentTimeMillis() / 1000;
            } else if (count == 3) {
                nextTimestamp = orderTimestamp + 30 * 60 - System.currentTimeMillis() / 1000;
            }
            redisDelayedQueueManager.addJsyd(JsydDelayedMessage.builder().mobile(mobile).count(count + 1).orderId(orderId).orderTimeStamp(orderTimestamp).build(), nextTimestamp, TimeUnit.SECONDS);
            //}
        }
        //subscribe.setResult(jiangsuResponseQueryInfo.getContent().getStatusMsg());
        subscribeService.updateSubscribeDbAndEs(subscribe);
        //回传到第三方
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
//            outsideCallbackService.outsideCallback(subscribeService.getById(subscribe.getId()),"回调通知");
        }
        //if (jiangsuResponseQueryInfo.isOk()) {
        if (isVrbtMember) {
            rabbitMQMsgSender.sendSubscribeVerifyDelay1hourMessage(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY).id(subscribe.getId()).msg("包月状态延迟60分钟校验").extra(MESSAG_EXTRA_60_MIN).build());
            rabbitMQMsgSender.sendSubscribeVerifyDelay3DayMessage(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY).id(subscribe.getId()).msg("包月状态延迟3天校验").extra(MESSAG_EXTRA_3_DAY).build());
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
        }
    }
}
