package com.eleven.cms.queue;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.remote.SubscribeVerifyService;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2021/5/26 16:48
 * Desc: 延迟任务处理
 */
@Slf4j
@Component
public class CallbackNotifyTaskService {

    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Autowired
    OutsideCallbackService outsideCallbackService;

    //最大回调次数
    public static Integer MAX_COUNT = 5;

    //回调间隔
    public static Integer CALLBACK_INTERVAL = 60;

    //外部渠道号-01
    public static final String OUT_SUB_CHANNEL_01 = "OSVRBT01";
    public static final String OUT_SUB_CHANNEL_02 = "OSVRBT02";
    public static final String OUT_SUB_CHANNEL_03 = "OSVRBT03";

    public void handleCallbackNotifyMessage(CallbackNotifyMessage callbackNotifyMessage) {
        log.info("收到延迟消息:{}", callbackNotifyMessage);
//        Subscribe subscribe = subscribeService.getById(callbackNotifyMessage.getId());
//        if (OUT_SUB_CHANNEL_01.equals(subscribe.getSubChannel())) {
//            boolean result = outsideCallbackService.outsideCallback(subscribe,callbackNotifyMessage.getMsg());
//            if (!result) {
//                if (callbackNotifyMessage.getFailCount() < MAX_COUNT) {
//                    //加入延迟队列
//                    redisDelayedQueueManager.addCallbcakNotify(CallbackNotifyMessage.builder().id(subscribe.getId())
//                            .failCount(callbackNotifyMessage.getFailCount() + 1).msg(callbackNotifyMessage.getMsg()).build(), CALLBACK_INTERVAL, TimeUnit.MINUTES);
//                } else {
//                    log.warn("订单号:{}已经达到最大回调次数，不再处理");
//                }
//            }
//        } else {
//            boolean result = outsideCallbackService.outsideCallback(subscribe, callbackNotifyMessage.getMsg());
//            if (!result) {
//                if (callbackNotifyMessage.getFailCount() < MAX_COUNT) {
//                    //加入延迟队列
//                    redisDelayedQueueManager.addCallbcakNotify(CallbackNotifyMessage.builder().id(subscribe.getId())
//                            .failCount(callbackNotifyMessage.getFailCount() + 1).msg(callbackNotifyMessage.getMsg()).build(), CALLBACK_INTERVAL, TimeUnit.MINUTES);
//                } else {
//                    log.warn("订单号:{}已经达到最大回调次数，不再处理", subscribe.getIspOrderNo());
//                }
//            }
//        }

    }


}
