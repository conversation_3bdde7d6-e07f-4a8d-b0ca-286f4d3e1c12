package com.eleven.cms.queue;

import com.eleven.cms.ad.AdReportService;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.KugouApiService;
import com.eleven.cms.remote.SubscribeVerifyService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.eleven.cms.queue.RedisDelayedQueueManager.*;

/**
 * Author: <EMAIL>
 * Date: 2021/5/26 16:48
 * Desc: 延迟任务处理
 */
@Slf4j
@Component
public class DelayedTaskService {

    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    AdReportService adReportService;
    @Autowired
    private KugouApiService kugouApiService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;

    public void handleDelayMessage(DelayedMessage delayMessage) {
        log.info("收到延迟消息:{}", delayMessage);
        final String tag = delayMessage.getTag();
        if (MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY.equals(tag)) {
            final Subscribe subscribe = subscribeService.getById(delayMessage.getId());
            //不校验联通业务
            if (subscribe == null || MobileRegionResult.ISP_LIANTONG.equals(subscribe.getIsp())) {
                return;
            }
            int verifyStatus = subscribeVerifyService.monthVerify(subscribe);
            if (verifyStatus > BizConstant.SUBSCRIBE_MONTH_VERIFY_INIT) {
                Subscribe sub = new Subscribe();
                sub.setId(delayMessage.getId());
                if (MESSAG_EXTRA_60_MIN.equals(delayMessage.getExtra())) {
                    sub.setVerifyStatus(verifyStatus);
                    //1小时退订外部回调
                    if(BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE == verifyStatus) {
                        //1小时退订也上报广告平台
                        adReportService.unsubReport(subscribe.getDeviceInfo(), subscribe.getSubChannel(), subscribe.getMobile(), subscribe.getSource(), subscribe, delayMessage.getExtra());
                        subscribeService.unsubscribeOutSideCallback(subscribe);
                        junboChargeLogService.recoverKugouOrder(subscribe);
                    }
                    //1小时存量在订上报
                    if(BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS==verifyStatus) {
                        adReportService.subedReport(subscribe.getDeviceInfo(), subscribe.getSubChannel(), subscribe.getMobile());
                    }
                } else if (MESSAG_EXTRA_UNSUB_1_DAY.equals(delayMessage.getExtra())) {
                    //24小时退订上报
                    if (BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE == verifyStatus) {
                        adReportService.unsubReport(subscribe.getDeviceInfo(), subscribe.getSubChannel(), subscribe.getMobile(), subscribe.getSource(), subscribe, delayMessage.getExtra());
                    }
                    return;
                } else{
                    sub.setVerifyStatusDaily(verifyStatus);
                    //将退订号码写入黑名单
                    if(verifyStatus == 0) {
                        try {
                            blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
                        } catch (Exception e) {
                            log.error("写入黑名单错误,手机号:{}", subscribe.getMobile(), e);
                        }
                    }
                }
                subscribeService.updateSubscribeDbAndEs(sub);
            }
        }
    }


}
