package com.eleven.cms.queue;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aiunion.pojo.dto.AiPicAiUnionFuseFaceTaskCreateDTO;
import com.eleven.cms.aiunion.service.IAiUnionService;
import com.eleven.cms.aiunionkp.pojo.dto.KpAiPicAiUnionFuseFaceTaskCreateDTO;
import com.eleven.cms.aiunionkp.service.KpIAiUnionService;
import com.eleven.cms.aivrbt.dto.AIStyleTaskCreateDTO;
import com.eleven.cms.aivrbt.dto.AiPicFuseFaceTaskCreateDTO;
import com.eleven.cms.aivrbt.service.impl.HaiYiAiService;
import com.eleven.cms.aivrbt.service.impl.TencentAiService;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.dto.HyFtpUploadDTO;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.EsInsertCode;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.ReportPageService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * Author: <EMAIL>
 * Date: 2023/5/10 11:36
 * Desc: Rabbitmq消息接收
 */
@Component
@Slf4j
public class RabbitMQMsgReceiver {
    public static final String LOG_TAG = "RabbitMQ消息接收";
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Autowired
    SubscribeResultNotifyService subscribeResultNotifyService;
    @Autowired
    OutsideCallbackService outsideCallbackService;
    @Autowired
    IOrderVrbtService orderVrbtService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    IDataNotifyLogService dataNotifyService;
    @Autowired
    ICouponCodeService couponCodeService;
    @Autowired
    IMemberService memberService;
    @Autowired
    ILiantongRingService liantongRingService;
    @Autowired
    IKugouOrderService kugouOrderService;
    @Autowired
    ISmsModelService smsModelService;
    @Autowired
    private ITuniuCouponCodeChargeLogService tuniuCouponCodeChargeLogService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private ILianlianChargeLogService lianlianChargeLogService;
    @Autowired
    private ICommonCouponService commonCouponService;
    @Autowired
    private IYinglouRingService yinglouRingService;
    @Autowired
    private YycpReportService yycpReportService;
    @Autowired
    private HaiYiAiService haiYiAiService;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    private IAiUnionService aiUnionService;
    @Autowired
    private KpIAiUnionService kpAiUnionService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private TencentAiService tenantAiService;
    @Autowired
    private IMiguVrbtPayOrderService miguVrbtPayOrderService;
    @Autowired
    private ReportPageService reportPageService;
    @Autowired
    private WenshengVideoService wenshengVideoService;

    @Autowired
    private IVrbtZeroOrderService vrbtZeroOrderService;

    @Autowired
    private IDuodianCouponCodeChargeLogService duodianCouponCodeChargeLogService;
    @Autowired
    private IGanSuMobileApiService ganSuMobileApiService;

    @Autowired
    private SiChuanMobileApiService siChuanMobileApiService;

//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "5-10")
    public void subscribeVerifyExpireListener(DelayedMessage delayedMessage) {
        log.info("{}-收到包月校验消息:{}", LOG_TAG, delayedMessage);
        try {
            subscribeVerifyService.verifyMsgHandle(delayedMessage);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, delayedMessage, e);
        }
    }

//    @RabbitListener(queues = RabbitMQConfig.OUTSIDE_CALLBACK_QUEUE_NAME, concurrency = "5-10")
    public void outsideCallbackListener(CallbackNotifyMessage callbackNotifyMessage) {
        log.info("{}-收到回调通知消息:{}", LOG_TAG, callbackNotifyMessage);
        final Subscribe subscribe = subscribeService.getById(callbackNotifyMessage.getId());
        outsideCallbackService.outsideCallbackAsync(subscribe, callbackNotifyMessage.getMsg());
    }

//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_VRBT_ORDER_EXPIRE_QUEUE_NAME, concurrency = "5-10")
    public void subscribeVrbtOrderExpireListener(DelayedMessage delayedMessage) {
        log.info("{}-收到视频彩铃订购消息:{}", LOG_TAG, delayedMessage);
        try {
            orderVrbtService.handleMqOrder(delayedMessage);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, delayedMessage, e);
        }
    }

//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_VRBT_RECHECK_EXPIRE_QUEUE_NAME)
    public void subscribeVrbtRecheckExpireListener(DelayedVrbtRecheckMessage delayedVrbtRecheckMessage) {
        log.info("{}-收到订购结果延迟查询消息:{}", LOG_TAG, delayedVrbtRecheckMessage);
        try {
            subscribeResultNotifyService.handleMqRecheck(delayedVrbtRecheckMessage);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, delayedVrbtRecheckMessage, e);
        }
    }

//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_RESULT_JUDGE_EXPIRE_QUEUE_NAME)
    public void subscribeResultJudgeExpireListener(DelayedMessage delayedMessage) {
        log.info("{}-收到业务开通结果判定消息:{}", LOG_TAG, delayedMessage);
        try {
            subscribeResultNotifyService.resultJudgeMsgHandle(delayedMessage);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, delayedMessage, e);
        }
    }

//    @RabbitListener(queues = RabbitMQConfig.ALIPAY_DEDUCT_QUEUE_NAME, concurrency = "2")
    public void alipayDeductListener(AlipayDeductMessage alipayDeductMessage) {
        log.info("{}-支付宝周期扣款消息:{}", LOG_TAG, alipayDeductMessage);
        AliSignRecord aliSign = aliSignRecordService.lambdaQuery().eq(AliSignRecord::getId, alipayDeductMessage.getId()).one();
        if (aliSign == null) {
            log.info("{}-支付宝周期扣款队列任务-支付宝签约协议查询失败:{}", LOG_TAG, alipayDeductMessage);
            return;
        }
        try {
            //处理收到的扣款消息
            aliSignRecordService.aliSignScheduleDeduct(aliSign);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, alipayDeductMessage, e);
        }
    }

    /**
     * 企业彩铃数据回执普通队列接收消息
     *
     * @param dataNotifyDelayedMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.QYCL_DATA_NOTIFY_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "5")
    public void dataNotifyDelay1hourListener(DataNotifyDelayedMessage dataNotifyDelayedMessage) {
        log.info("{}-数据回执收到延迟消息:{}", LOG_TAG, dataNotifyDelayedMessage);
        try {
            final String msisdn = dataNotifyDelayedMessage.getMsisdn();
            final String id = dataNotifyDelayedMessage.getId();
            final String extra = dataNotifyDelayedMessage.getExtra();
            dataNotifyService.receiveDataNotifyDelayMsg(msisdn, id, extra);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, dataNotifyDelayedMessage, e);
        }

    }

    /**
     * 咪咕互娱VR竞盟普通队列接收消息
     *
     * @param delayedMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_MGHY_VRJINGMENG_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "5")
    public void mghyVRJingMengDelay30MinutesListener(DelayedMessage delayedMessage) {
        log.info("{}-咪咕互娱VR竞盟收到订购状态校验消息:{}", LOG_TAG, delayedMessage);
        try {
            final Subscribe subscribe = subscribeService.getById(delayedMessage.getId());
            if (subscribe != null && subscribe.getMobile() != null) {
                int verifyStatus = subscribeVerifyService.monthVerify(subscribe);
                if (verifyStatus > -1) {
                    Subscribe sub = new Subscribe();
                    sub.setId(delayedMessage.getId());
                    sub.setStatus(verifyStatus);
                    sub.setOpenTime(new Date());
                    subscribeService.updateSubscribeDbAndEs(sub);
                }
            }

        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, delayedMessage, e);
        }
    }


    /**
     * 河图权益发送券码消息队列接收消息
     *
     * @param sendCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.HETU_SENDCODE_QUEUE_NAME, concurrency = "3")
    public void heTuSendCodeQueueListener(SendCodeDeductMessage sendCodeDeductMessage) {
        log.info("{}-河图权益发送券码消息队列接收消息:{}", LOG_TAG, sendCodeDeductMessage);
        try {
            //河图权益发送券码
            couponCodeService.sendCodeScheduleDeduct(sendCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendCodeDeductMessage, e);
        }
    }

    /**
     * 南山途牛权益发送券码消息队列接收消息
     *
     * @param tuniuSendCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.TUNIU_SENDCODE_QUEUE_NAME, concurrency = "3")
    public void tuniuSendCodeQueueListener(TuniuSendCodeDeductMessage tuniuSendCodeDeductMessage) {
        log.info("{}-南山途牛权益发送券码消息队列接收消息:{}", LOG_TAG, tuniuSendCodeDeductMessage);
        try {
            //途牛权益发送券码
            tuniuCouponCodeChargeLogService.sendCodeScheduleDeduct(tuniuSendCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, tuniuSendCodeDeductMessage, e);
        }
    }

    /**
     * 支付宝视频彩铃权益充值消息队列接收消息
     *
     * @param aliPayVrbtMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.ALIPAY_VRBT_QUEUE_NAME, concurrency = "1")
    public void aliPayVrbtRightsQueueListener(AliPayVrbtMessage aliPayVrbtMessage) {
        log.info("{}-支付宝视频彩铃权益充值消息队列接收消息:{}", LOG_TAG, aliPayVrbtMessage);
        try {
            //支付宝视频彩铃权益充值
            memberService.aliPayRechargeVrbt(aliPayVrbtMessage.getOrderId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, aliPayVrbtMessage, e);
        }
    }


    /**
     * 支付宝扣款提醒短信消息队列接收消息
     *
     * @param alipayDeductSendMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.ALIPAY_SEND_MESSAGE_QUEUE_NAME, concurrency = "1")
    public void aliPaySendMessageQueueListener(AlipayDeductSendMessage alipayDeductSendMessage) {
        log.info("{}-支付宝扣款提醒短信消息队列接收消息:{}", LOG_TAG, alipayDeductSendMessage);
        try {

            AliSignRecord aliSign = aliSignRecordService.lambdaQuery().eq(AliSignRecord::getId, alipayDeductSendMessage.getId()).one();
            if (aliSign == null) {
                log.info("{}-支付宝扣款提醒短信队列任务-支付宝签约协议查询失败:{}", LOG_TAG, alipayDeductSendMessage);
                return;
            }
            //支付宝扣款提醒短信
            aliSignRecordService.sendDeductMsg(aliSign);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, alipayDeductSendMessage, e);
        }
    }


    /**
     * 联通上传铃音消息队列接收消息
     *
     * @param liantongUploadRingMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.LINGTONG_UPLOAD_RING_QUEUE_NAME, concurrency = "5")
    public void lingtongUploadRingQueueListener(LiantongUploadRingMessage liantongUploadRingMessage) {
        log.info("{}-联通上传铃音消息队列接收消息:{}", LOG_TAG, liantongUploadRingMessage);
        try {
            //联通上传铃音
            liantongRingService.uploadRing(liantongUploadRingMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, liantongUploadRingMessage, e);
        }
    }


    /**
     * 酷狗权益充值消息队列接收消息
     *
     * @param kuGouRightsRechargeMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.KUGOU_RIGHTS_RECHARGE_QUEUE_NAME, concurrency = "1")
    public void kuGouRightsRechargeQueueListener(KuGouRightsRechargeMessage kuGouRightsRechargeMessage) {
        log.info("{}-酷狗权益充值消息队列接收消息:{}", LOG_TAG, kuGouRightsRechargeMessage);
        try {
            //酷狗权益充值
            kugouOrderService.kugouRecharge(kuGouRightsRechargeMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, kuGouRightsRechargeMessage, e);
        }
    }


    /**
     * 思维方阵券码消息队列接收消息
     *
     * @param sendSmsCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.SWFZ_SEND_SMS_CODE_QUEUE_NAME, concurrency = "3")
    public void swfzSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
        log.info("{}-思维方阵券码消息队列接收消息:{}", LOG_TAG, sendSmsCodeDeductMessage);
        try {
            //思维方阵发送券码
            couponCodeService.swfzSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendSmsCodeDeductMessage, e);
        }
    }


    /**
     * 大鱼消除券码消息队列接收消息
     *
     * @param sendSmsCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.DYXC_SEND_SMS_CODE_QUEUE_NAME, concurrency = "3")
    public void dyxcSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
        log.info("{}-大鱼消除券码消息队列接收消息:{}", LOG_TAG, sendSmsCodeDeductMessage);
        try {
            //大鱼消除发送券码
            couponCodeService.dyxcSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendSmsCodeDeductMessage, e);
        }
    }


    /**
     * 厨房大逃亡券码消息队列接收消息
     *
     * @param sendSmsCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.CFDTW_SEND_SMS_CODE_QUEUE_NAME, concurrency = "3")
    public void cfdtwSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
        log.info("{}-厨房大逃亡券码消息队列接收消息:{}", LOG_TAG, sendSmsCodeDeductMessage);
        try {
            //厨房大逃亡发送券码
            couponCodeService.cfdtwSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendSmsCodeDeductMessage, e);
        }
    }

    /**
     * 我自为道券码消息队列接收消息
     *
     * @param sendSmsCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.WZWD_SEND_SMS_CODE_QUEUE_NAME, concurrency = "3")
    public void wzwdSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
        log.info("{}-我自为道券码消息队列接收消息:{}", LOG_TAG, sendSmsCodeDeductMessage);
        try {
            //我自为道发送券码
            couponCodeService.wzwdSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendSmsCodeDeductMessage, e);
        }
    }


    /**
     * 短剧券码消息队列接收消息
     *
     * @param sendSmsCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.DUANJU_SEND_SMS_CODE_QUEUE_NAME, concurrency = "3")
    public void duanjuSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
        log.info("{}-短剧券码消息队列接收消息:{}", LOG_TAG, sendSmsCodeDeductMessage);
        try {
            //短剧发送券码
            couponCodeService.duanjuSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendSmsCodeDeductMessage, e);
        }
    }

    /**
     * 水晶传说券码消息队列接收消息
     *
     * @param sendSmsCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.SJCS_SEND_SMS_CODE_QUEUE_NAME, concurrency = "3")
    public void sjcsSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
        log.info("{}-水晶传说券码消息队列接收消息:{}", LOG_TAG, sendSmsCodeDeductMessage);
        try {
            //水晶传说发送券码
            couponCodeService.sjcsSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendSmsCodeDeductMessage, e);
        }
    }

    /**
     * 包月续订短信消息队列接收消息
     *
     * @param monthRenewSubSmsMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.MONTH_RENEW_SUB_SMS_QUEUE_NAME, concurrency = "1")
    public void monthRenewSubSmsQueueListener(MonthRenewSubSmsMessage monthRenewSubSmsMessage) {
        log.info("{}-包月续订短信消息队列接收消息:{}", LOG_TAG, monthRenewSubSmsMessage);
        try {
            //包月续订短信消息队列接收消息
            smsModelService.monthRenewSubSendSms(monthRenewSubSmsMessage.getChannel(), monthRenewSubSmsMessage.getMobile(), monthRenewSubSmsMessage.getBizType());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, monthRenewSubSmsMessage, e);
        }
    }


    /**
     * 贵州移动省包（在线支付）领取权益消息队列接收消息
     *
     * @param guiZhouMobilePayRechargeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.GUIZHOU_MOBILEPAY_RECHARGE_QUEUE_NAME, concurrency = "1")
    public void guiZhouMobilePayRechargeQueueListener(GuiZhouMobilePayRechargeDeductMessage guiZhouMobilePayRechargeDeductMessage) {
        log.info("{}-贵州移动省包（在线支付）领取权益消息队列接收消息:{}", LOG_TAG, guiZhouMobilePayRechargeDeductMessage);
        try {
            //贵州移动省包（在线支付）领取权益
            lianlianChargeLogService.guiZhouMobilePayRechargeScheduleDeduct(guiZhouMobilePayRechargeDeductMessage.getId(), guiZhouMobilePayRechargeDeductMessage.getOrderId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, guiZhouMobilePayRechargeDeductMessage, e);
        }
    }


    /**
     * 南山联联权益发送券码消息队列接收消息
     *
     * @param lianLianSendCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.LIANLIAN_SENDCODE_QUEUE_NAME, concurrency = "3")
    public void lianlianSendCodeQueueListener(LianLianSendCodeDeductMessage lianLianSendCodeDeductMessage) {
        log.info("{}-南山联联权益发送券码消息队列接收消息:{}", LOG_TAG, lianLianSendCodeDeductMessage);
        try {
            //联联权益发送券码
            commonCouponService.sendCodeScheduleDeduct(lianLianSendCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, lianLianSendCodeDeductMessage, e);
        }
    }


    /**
     * 影楼铃音上传消息队列接收消息
     *
     * @param yinglouRingUploadDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.YINGLOU_RING_UPLOAD_QUEUE_NAME, concurrency = "1")
    public void yinglouRingUploadQueueListener(YinglouRingUploadDeductMessage yinglouRingUploadDeductMessage) {
        log.info("{}-影楼铃音上传消息队列接收消息:{}", LOG_TAG, yinglouRingUploadDeductMessage);
        try {
            //影楼铃音上传
            yinglouRingService.ringUploadScheduleDeduct(yinglouRingUploadDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, yinglouRingUploadDeductMessage, e);
        }
    }

    /**
     * 咪咕FTP铃音上传队列接收消息
     *
     * @param miguRingFtpUploadMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.MIGU_RING_FTP_UPLOAD_QUEUE_NAME, concurrency = "1")
    public void miguRingFtpUploadQueueListener(MiguRingFtpUploadMessage miguRingFtpUploadMessage) {
        log.info("{}-咪咕FTP铃音上传队列接收消息:{}", LOG_TAG, miguRingFtpUploadMessage);
        try {
            yycpReportService.yycpFtpUploadAndReport(miguRingFtpUploadMessage.getMobile(), miguRingFtpUploadMessage.getTag());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, miguRingFtpUploadMessage, e);
        }
    }

    /**
     * 暗黑主宰券码消息队列接收消息
     *
     * @param sendSmsCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.AHZZ_SEND_SMS_CODE_QUEUE_NAME, concurrency = "3")3
    public void ahzzSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage) {
        log.info("{}-暗黑主宰券码消息队列接收消息:{}", LOG_TAG, sendSmsCodeDeductMessage);
        try {
            //暗黑主宰发送券码
            couponCodeService.ahzzSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, sendSmsCodeDeductMessage, e);
        }
    }

    /**
     * 海艺AI滤镜创作消息队列接收消息
     *
     * @param aiStyleTaskCreateDTO aiStyleTaskCreateDTO
     */
    @RabbitListener(queues = RabbitMQConfig.HY_SEND_SMS_CODE_QUEUE_NAME, concurrency = "2")
    public void hySendCodeQueueListener(AIStyleTaskCreateDTO aiStyleTaskCreateDTO) {
        log.info("{}-海艺AI滤镜创作消息队列接收消息:{}", LOG_TAG, aiStyleTaskCreateDTO);
        try {
            haiYiAiService.handleMQMsg(aiStyleTaskCreateDTO);
        } catch (Exception e) {
            log.info("{}-海艺AI滤镜创作消息队列处理收到的消息:{},异常!", LOG_TAG, aiStyleTaskCreateDTO, e);
        }
    }

    @RabbitListener(queues = RabbitMQConfig.TX_FACE_SEND_SMS_CODE_QUEUE_NAME, concurrency = "2")
    public void txFaceSendCodeQueueListener(AiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        log.info("{}-腾讯云换脸AI创作消息队列接收消息:{}", LOG_TAG, aiPicFuseFaceTaskCreateDTO);
        try {
            tenantAiService.handleMQMsg(aiPicFuseFaceTaskCreateDTO);
        } catch (Exception e) {
            log.info("{}-腾讯云换脸AI创作消息队列处理收到的消息:{},异常!", LOG_TAG, aiPicFuseFaceTaskCreateDTO, e);
        }
    }

    /**
     * 小程序设置铃声消息队列接收消息
     *
     * @param hyFtpUploadDTO hyFtpUploadDTO
     */
    @RabbitListener(queues = RabbitMQConfig.HY_SET_RING_QUEUE_NAME, concurrency = "1")
    public void hySetRingQueueListener(HyFtpUploadDTO hyFtpUploadDTO) {
        log.info("{}-小程序设置铃声消息队列接收消息:{}", LOG_TAG, hyFtpUploadDTO);
        String channelId = hyFtpUploadDTO.getChannelId();
        if(StringUtils.isEmpty(channelId)){
            channelId=MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX;
            hyFtpUploadDTO.setChannelId(channelId);
        }
        try {
            // 1.权益发放
            miguApiService.specialProductSub(hyFtpUploadDTO.getMobile(),channelId);
            // 2.铃声上报
            haiYiAiService.handleSetRingMQMsg(hyFtpUploadDTO);
        } catch (Exception e) {
            log.info("{}-小程序设置铃声消息队列接收消息:{},异常!", LOG_TAG, hyFtpUploadDTO, e);
        }
    }

    /**
     * 新增插码-监听
     *
     * @param esInsertCode esInsertCode
     */
//    @RabbitListener(queues = RabbitMQConfig.INSERT_CODE_ADD_QUEUE, concurrency = "2")
    public void insertCodeAddQueueListener(EsInsertCode esInsertCode) {
        try {
            LocalDateTime now = LocalDateTime.now();

            esInsertCode.setId(IdWorker.getIdStr());
            esInsertCode.setCreateTime(now);
            esInsertCode.setCreateTimestamp(now.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli());
            elasticsearchRestTemplate.save(esInsertCode);
        } catch (Exception e) {
            log.info("{}-接收新增插码消息:{},异常!", LOG_TAG, esInsertCode, e);
        }
    }

    /**
     * 小程序设置铃声消息队列接收消息
     *
     * @param hyFtpUploadDTO hyFtpUploadDTO
     */
//    @RabbitListener(queues = RabbitMQConfig.YYCP_SET_RING_QUEUE_NAME, concurrency = "1")
    public void yycpSetRingQueueListener(HyFtpUploadDTO hyFtpUploadDTO) {
        log.info("{}-一语成片设置铃声消息队列接收消息:{}", LOG_TAG, hyFtpUploadDTO);
        try {
            // 1.权益发放
            miguApiService.specialProductSub(hyFtpUploadDTO.getMobile(), MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX);
            // 2.铃声上报
            wenshengVideoService.handleSetRingMQMsg(hyFtpUploadDTO);
        } catch (Exception e) {
            log.info("{}-一语成片设置铃声消息队列接收消息:{},异常!", LOG_TAG, hyFtpUploadDTO, e);
        }
    }

    /**
     * 报备页下单消息队列接收消息
     *
     * @param jsonNode jsonNode
     */
//    @RabbitListener(queues = RabbitMQConfig.REPORT_PAGE_ORDER_QUEUE_NAME, concurrency = "1")
    public void reportPageOrderQueueListener(JsonNode jsonNode) {
        log.info("{}-报备页下单消息队列接收消息:{}", LOG_TAG, jsonNode);
        try {
            reportPageService.order(jsonNode);
        } catch (Exception e) {
            log.info("{}-报备页下单消息队列接收消息:{},异常!", LOG_TAG, jsonNode, e);
        }
    }

    @RabbitListener(queues = RabbitMQConfig.TX_FACE_ACROSS_QUEUE_NAME,concurrency = "1")
    public void txFaceAcrossQueueListener(AiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO){
        log.info("{}-ai联盟任务创作:{}",LOG_TAG,aiPicFuseFaceTaskCreateDTO);
        try {
            aiUnionService.handleCreateTaskMsg(aiPicFuseFaceTaskCreateDTO);
        } catch (Exception e) {
            log.info("{}-ai联盟任务创作消息队列接收消息:{},异常!",LOG_TAG,aiPicFuseFaceTaskCreateDTO,e);
        }
    }
//    @RabbitListener(queues = RabbitMQConfig.TX_FACE_KP_ACROSS_QUEUE_NAME,concurrency = "1")
    public void txFaceKpAcrossQueueListener(KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO){
        log.info("{}-ai联盟任务创作:{}",LOG_TAG,aiPicFuseFaceTaskCreateDTO);
        try {
            kpAiUnionService.handleCreateTaskMsg(aiPicFuseFaceTaskCreateDTO);
        } catch (Exception e) {
            log.info("{}-ai联盟任务创作消息队列接收消息:{},异常!",LOG_TAG,aiPicFuseFaceTaskCreateDTO,e);
        }
    }

    /**
     * 监听死信队列-领取权益
     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_SCH_SPECIAL_PRODUCT_SUB_EXPIRE_QUEUE_NAME, concurrency = "2")
    public void lqqyExpireQueueListener(ObjectNode objectNode) {
        log.info("{}-监听死信队列-领取权益-收到消息:{}", LOG_TAG, objectNode);
        try {
            String mobile = objectNode.get("mobile").textValue();
            String channelCode = objectNode.get("channelCode").textValue();
            miguApiService.specialProductSub(mobile, channelCode);
        } catch (Exception e) {
            log.info("{}-监听死信队列-领取权益-收到消息:{},异常!", LOG_TAG, objectNode, e);
        }
    }


    /**
     * 咪咕视频彩铃三方支付普通队列接收消息
     *
     * @param mgVrbtPayDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_MGVRBT_PAY_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "1")
    public void mgVrbtPayDelay30SecondsListener(MgVrbtPayDeductMessage mgVrbtPayDeductMessage) {
        log.info("{}-咪咕视频彩铃三方支付收到订购状态校验消息:{}", LOG_TAG, mgVrbtPayDeductMessage);
        try {
            miguVrbtPayOrderService.miGuVrbtPayMQMsg(mgVrbtPayDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, mgVrbtPayDeductMessage, e);
        }
    }


    /**
     * 免费包月订购视频彩铃消息队列接收消息
     * @param subFreeMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.FREE_MONTH_SUB_VRBT_QUEUE_NAME, concurrency = "5-10")
    public void freeMonthSubVrbtQueueListener(SubFreeMessage subFreeMessage) {
        log.info("{}-免费包月订购视频彩铃消息队列接收消息:{}", LOG_TAG, subFreeMessage);
        try {
            //免费包月订购视频彩铃
            vrbtZeroOrderService.freeMonthSubVrbt(subFreeMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, subFreeMessage, e);
        }
    }





    /**
     * 甘肃移动普通队列接收消息
     *
     * @param jsonNode
     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_GANSU_MOBILE_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "1")
    public void gansuMobileDelay60SecondsListener(JsonNode jsonNode) {
        log.info("{}-甘肃移动收到订购状态校验消息:{}", LOG_TAG, jsonNode);
        try {
            ganSuMobileApiService.subMQMsg(jsonNode);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, jsonNode, e);
        }
    }


    /**
     * 多彩多点商超权益发送券码消息队列接收消息
     *
     * @param duodianSendCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.DUODIAN_SENDCODE_QUEUE_NAME, concurrency = "3")
    public void duodianSendCodeQueueListener(DuodianSendCodeDeductMessage duodianSendCodeDeductMessage) {
        log.info("{}-多彩多点商超权益发送券码消息队列接收消息:{}", LOG_TAG, duodianSendCodeDeductMessage);
        try {
            //多点权益发送券码
            duodianCouponCodeChargeLogService.sendCodeScheduleDeduct(duodianSendCodeDeductMessage.getId());
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, duodianSendCodeDeductMessage, e);
        }
    }

    /**
     * 视彩号铃音发布
     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_SCH_RING_PUBLISH_EXPIRE_QUEUE_NAME,concurrency = "1")
    public void schSpecialProductSubMsgExpireQueueListener(ObjectNode objectNode) {
        log.info("{}-收到视彩号铃音发布消息:{}", LOG_TAG, objectNode);
        try {
            String mobile = objectNode.get("mobile").textValue();
            String channelCode = objectNode.get("channelCode").textValue();
            final Result<?> result = orderVrbtService.schRingPublish(mobile, channelCode);
            log.info("视彩号铃音发布结果:{}", result);
        } catch (Exception e) {
            log.info("{}-收到视彩号铃音发布消息:{},异常!", LOG_TAG, objectNode, e);
        }
    }
    /**
     * 游戏券码消息队列接收消息
     * @param gameSendCodeDeductMessage
     */
//    @RabbitListener(queues = RabbitMQConfig.GAME_SEND_CODE_QUEUE_NAME, concurrency = "3")
    public void gameSendCodeQueueListener(GameSendCodeDeductMessage gameSendCodeDeductMessage) {
        log.info("{}-"+gameSendCodeDeductMessage.getLogTag()+"券码消息队列接收消息:{}", LOG_TAG, gameSendCodeDeductMessage);
        try {
            //游戏发送券码
            couponCodeService.gameSendCodeScheduleDeduct(gameSendCodeDeductMessage.getId(),gameSendCodeDeductMessage.getGameName());
        } catch (Exception e) {
            log.info("{}-"+gameSendCodeDeductMessage.getLogTag()+"处理收到的消息:{},异常!", LOG_TAG, gameSendCodeDeductMessage, e);
        }
    }



    /**
     * 四川移动渝姐充值普通队列接收消息
     *
     * @param jsonNode
     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_SICHUAN_MOBILE_YUJIE_RECHARGE_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "1")
    public void sichuanMobileYujieRechargeDelay5MinutesListener(JsonNode jsonNode) {
        log.info("{}-四川移动收到渝姐充值消息:{}", LOG_TAG, jsonNode);
        try {
            siChuanMobileApiService.reChargeConsume(jsonNode);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, jsonNode, e);
        }
    }


    /**
     * 四川移动包月校验普通队列接收消息
     *
     * @param jsonNode
     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_SICHUAN_MOBILE_MONTHLY_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "1")
    public void sichuanMobileMonthlyVerifyDelay60secondsListener(JsonNode jsonNode) {
        log.info("{}-四川移动收到包月校验消息:{}", LOG_TAG, jsonNode);
        try {
            siChuanMobileApiService.receiveSubscribeResult(jsonNode);
        } catch (Exception e) {
            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, jsonNode, e);
        }
    }
}
