package com.eleven.cms.queue;

import com.eleven.cms.service.ISmsModelService;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.entity.QyclCompany;
import com.eleven.qycl.entity.QyclCompanyMember;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.service.IQyclCompanyMemberService;
import com.eleven.qycl.service.IQyclCompanyService;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_3_DAY;

/**
 * 企业彩铃未开通功能通知短信
 */
@Slf4j
@Component
public class QyclDelayedTaskService {

    @Autowired
    IQyclCompanyService qyclCompanyService;
    @Autowired
    IQyclCompanyMemberService qyclCompanyMemberService;
    @Autowired
    EnterpriseVrbtProperties enterpriseVrbtProperties;
    @Autowired
    ISmsModelService smsModelService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;

    public void handleDelayMessage(QyclDelayedMessage delayMessage) {
        log.info("企业彩铃收到延迟消息:{}", delayMessage);
        QyclCompany qyclCompany = qyclCompanyService.lambdaQuery().eq(QyclCompany::getOpenId, delayMessage.getOpenId()).orderByDesc(QyclCompany::getCreateTime).last("limit 1").one();
        QyclCompanyMember qyclCompanyMember = qyclCompanyMemberService.lambdaQuery().eq(QyclCompanyMember::getOpenId, delayMessage.getOpenId()).eq(QyclCompanyMember::getMobile, delayMessage.getMobile()).orderByDesc(QyclCompanyMember::getCreateTime).last("limit 1").one();
        if (MESSAG_EXTRA_3_DAY.equals(delayMessage.getTag())) {
            if (!StringUtils.equals(qyclCompanyMember.getQyclFun(), "1")) {
                qyclMiguSendSms(qyclCompany.getDepartmentId(), qyclCompany.getCompanyOwner(),delayMessage.getMobile(),qyclCompany.getChannel());
            }
        } else {
            if (!StringUtils.equals(qyclCompanyMember.getQyclFun(), "1")) {
                EntVrbtResult entVrbtResult = enterpriseVrbtService.addContentMembersByChannel(qyclCompany.getDepartmentId(),qyclCompany.getCompanyOwner(),qyclCompany.getChannel(), delayMessage.getMobile());
                final List<String> errorMsgList = JsonPath.read(entVrbtResult.getData().toString(), "$.list[?(@.billNum =='" + qyclCompanyMember.getMobile() + "')].errorMsg");
                if (!errorMsgList.isEmpty()) {
                    qyclCompanyMember.setRemark(errorMsgList.get(0));
                }
                qyclCompanyMemberService.updateById(qyclCompanyMember);
//                String smsContentUrl = enterpriseVrbtProperties.getSmsContentUrl();
//                //需要拼接参数 phone=***********&departmentName=%E9%83%A8%E9%97%A8%E5%90%8D%E7%A7%B0&openid=openid
//                smsContentUrl += "&phone=" + delayMessage.getMobile() + "&departmentId=" + URLEncoder.encode(qyclCompany.getDepartmentId()) + "&openid=" + delayMessage.getOpenId();
//                smsModelService.sendSmsAsync(delayMessage.getMobile(), "QYCL", "9527004", BizConstant.BUSINESS_TYPE_NO_RIGHTS, smsContentUrl);
            }
        }
    }

    @Async
    public void qyclMiguSendSms(String departmentId, String mobile,String companyOwner,String channel) {
        try {
            enterpriseVrbtService.deleteContentMembersByChannel(departmentId, companyOwner,channel,mobile);
            TimeUnit.SECONDS.sleep(5L);
            enterpriseVrbtService.addContentMembersByChannel(departmentId, companyOwner,channel,mobile);
        } catch (Exception e) {
            log.error("企业彩铃删除添加用户异常:", e);
        }
    }
}
