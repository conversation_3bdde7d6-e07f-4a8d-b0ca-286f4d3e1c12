package com.eleven.cms.queue;

import com.eleven.cms.service.IMemberService;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 北岸唐唱延迟发送周卡权益
 */
@Slf4j
@Component
public class BactDelayedTaskService {
    @Autowired
    private IMemberService memberService;

    public void handleDelayMessage(BactDelayedMessage delayMessage) {
        log.info("收到延迟消息:{}",delayMessage);
        final String msisdn = delayMessage.getMsisdn();
        if (StringUtils.isNotBlank(msisdn)) {
            Boolean isReceive=memberService.queueIsReceiveRecharge(delayMessage.getServiceId(),msisdn);
            if(isReceive){
                memberService.memberReceiveRights(delayMessage.getServiceId(),delayMessage.getPackName(),delayMessage.getRightsId(),msisdn,msisdn,false,true);
            }
        }
    }
}
