package com.eleven.cms.queue;

import com.eleven.cms.config.RabbitMQConfig;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2021/5/26 16:06
 * Desc: 延迟消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DelayedVrbtRecheckMessage implements Serializable {

    private static final Map<Integer,String> TIMES_COUNT_ROUTING_KEY_MAP = ImmutableMap.of(
//            1,RabbitMQConfig.SUBSCRIBE_VRBT_RECHECK_DELAY_1MINUTE_QUEUE_ROUTING_KEY,
//            2,RabbitMQConfig.SUBSCRIBE_VRBT_RECHECK_DELAY_5MINUTE_QUEUE_ROUTING_KEY,
//            3,RabbitMQConfig.SUBSCRIBE_VRBT_RECHECK_DELAY_30MINUTE_QUEUE_ROUTING_KEY
            );

    private String id;
    private int timesCounter;
    private String routingKey;
    private String tag;
    private String msg;
    private String extra;

    public static DelayedVrbtRecheckMessage getInstance(String subscribeId, int timesCount){
        if(timesCount<1||timesCount>3){
            return null;
        }
        String routingKey = TIMES_COUNT_ROUTING_KEY_MAP.get(timesCount);
        return DelayedVrbtRecheckMessage.builder()
                .id(subscribeId)
                .timesCounter(timesCount)
                .routingKey(routingKey)
                .tag(RedisDelayedQueueManager.MESSAG_TAG_SUBSCRIBE_DELAY_VRBT_RECHECK)
                .msg("订购结果延迟查询")
                .build();
    }

    public static void main(String[] args) {
        DelayedVrbtRecheckMessage message = DelayedVrbtRecheckMessage.getInstance("111", 1);
        System.out.println("message = " + message);
        message = DelayedVrbtRecheckMessage.getInstance("222", 2);
        System.out.println("message = " + message);
        message = DelayedVrbtRecheckMessage.getInstance("333", 3);
        System.out.println("message = " + message);

    }
}
