package com.eleven.cms.queue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 铃声上传到ftp的消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MiguRingFtpUploadMessage implements Serializable {
    //消息id,如果是一语成片用户活跃铃音制作就传subscribeId
    private String id;
    private String mobile;
    private String tag;
    private String msg;
    
}
