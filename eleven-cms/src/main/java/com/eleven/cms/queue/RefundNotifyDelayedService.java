package com.eleven.cms.queue;

import com.eleven.cms.remote.ChangShaAliPayNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 长沙退款通知
 */
@Slf4j
@Component
public class RefundNotifyDelayedService {
    @Autowired
    ChangShaAliPayNotifyService changShaAliPayNotifyService;
    public void handleRefundNotifyDelayMessage(RefundNotifyDelayedMessage refundNotifyDelayedMessage) {
        log.info("长沙退款通知收到延迟消息:{}",refundNotifyDelayedMessage);
        changShaAliPayNotifyService.refundNotify(refundNotifyDelayedMessage.getOrderId(),refundNotifyDelayedMessage.getBusinessType(),false);
    }
}
