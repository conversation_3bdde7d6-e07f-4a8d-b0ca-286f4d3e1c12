package com.eleven.cms.queue;

import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.ISmsModelService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OrderProductEnum;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 白金会员权益领取通知短信
 */
@Slf4j
@Component
public class BjhyDelayedTaskService {
    @Autowired
    private IMemberService memberService;
    @Autowired
    private ISmsModelService smsModelService;

    public void handleDelayMessage(BjhyDelayedMessage delayMessage) {
        log.info("收到延迟消息:{}",delayMessage);
        final String msisdn = delayMessage.getMsisdn();
        final String serviceId = delayMessage.getServiceId();
        final String channelCode = delayMessage.getChannelCode();
        if (StringUtils.isNotBlank(msisdn)) {
            Boolean isReceive=memberService.queueIsReceiveRecharge(serviceId,msisdn);
            if(isReceive){
                smsModelService.sendSmsAsync(msisdn,channelCode,serviceId, BizConstant.BUSINESS_TYPE_YES_RIGHTS);
            }else{
                smsModelService.sendSmsAsync(msisdn,channelCode,serviceId, BizConstant.BUSINESS_TYPE_NO_RIGHTS);
            }
        }
    }
}
