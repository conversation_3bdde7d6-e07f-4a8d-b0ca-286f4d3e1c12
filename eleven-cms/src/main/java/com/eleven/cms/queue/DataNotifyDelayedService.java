package com.eleven.cms.queue;

import com.alibaba.fastjson.JSON;
import com.eleven.cms.dto.DataNotifyVrbtResult;
import com.eleven.cms.entity.DataNotifyLog;
import com.eleven.cms.service.IDataNotifyLogService;
import com.eleven.cms.util.BizConstant;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_3_DAY;
import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_60_MIN;

/**
 * 数据回执延迟任务处理
 */
@Slf4j
@Component
public class DataNotifyDelayedService {
    @Autowired
    IDataNotifyLogService dataNotifyService;

    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;

    public void handleDataNotifyDelayMessage(DataNotifyDelayedMessage dataNotifyDelayedMessage) {
        log.info("数据回执收到延迟消息:{}",dataNotifyDelayedMessage);
        final String msisdn = dataNotifyDelayedMessage.getMsisdn();
        final String id = dataNotifyDelayedMessage.getId();
        final String extra= dataNotifyDelayedMessage.getExtra();
        DataNotifyLog dataNotifyLog=dataNotifyService.lambdaQuery().eq(DataNotifyLog::getId,id).orderByDesc(DataNotifyLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(dataNotifyLog==null){
            return;
        }
        Integer verifyStatus=0;
        final EntVrbtResult entVrbtResult = enterpriseVrbtService.queryContentMembersImmediateByChannel("",msisdn,dataNotifyLog.getCompanyOwner(),dataNotifyLog.getChannel());
        if (entVrbtResult.isOK() && entVrbtResult.getData()!=null) {
            List<DataNotifyVrbtResult> dataNotifyVrbtResultList= null;
            try {
                dataNotifyVrbtResultList = new ObjectMapper().readValue(entVrbtResult.getData().toString(), List.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            if(dataNotifyVrbtResultList.size()>0 && dataNotifyVrbtResultList!=null){
                List<DataNotifyVrbtResult> list= JSON.parseArray(JSON.toJSONString(dataNotifyVrbtResultList), DataNotifyVrbtResult.class);
                Optional<DataNotifyVrbtResult> dataNotifyVrbtResult=list.stream().collect(Collectors.toList()).stream().max(Comparator.comparing(DataNotifyVrbtResult::getBillNum));
                if(dataNotifyVrbtResult.isPresent() && StringUtils.equals("06",dataNotifyVrbtResult.get().getUserStatus())){
                    verifyStatus=1;
                }
            }
        }
        if (StringUtils.equals(MESSAG_EXTRA_60_MIN,extra)) {
            dataNotifyLog.setVerifyStatus(verifyStatus);
            dataNotifyLog.setUpdateTime(new Date());
        } else if (StringUtils.equals(MESSAG_EXTRA_3_DAY,extra)) {
            dataNotifyLog.setVerifyStatusDaily(verifyStatus);
            dataNotifyLog.setUpdateTime(new Date());
        }
        dataNotifyService.lambdaUpdate().eq(DataNotifyLog::getId, dataNotifyLog.getId()).update(dataNotifyLog);
    }


}
