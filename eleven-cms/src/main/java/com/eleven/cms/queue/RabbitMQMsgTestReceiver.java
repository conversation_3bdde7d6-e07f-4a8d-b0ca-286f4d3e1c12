package com.eleven.cms.queue;

import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.remote.SiChuanMobileApiService;
import com.eleven.cms.remote.SubscribeVerifyService;
import com.eleven.cms.service.*;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * Author: <EMAIL>
 * Date: 2023/5/10 11:36
 * Desc: Rabbitmq消息接收(该类专用于测试接收,因为消息接收类只在prod环境生效,如果去掉会收到线上环境的消息)
 */
@Component
@Profile("dev")
@Slf4j
public class RabbitMQMsgTestReceiver {
    public static final String LOG_TAG = "RabbitMQ消息接收测试";
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    OutsideCallbackService outsideCallbackService;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Autowired
    ICouponCodeService couponCodeService;
    @Autowired
    IMemberService memberService;
    @Autowired
    IAliSignRecordService aliSignRecordService;
    @Autowired
    ILiantongRingService liantongRingService;
    @Autowired
    ISmsModelService smsModelService;
    @Autowired
    private ITuniuCouponCodeChargeLogService tuniuCouponCodeChargeLogService;
    @Autowired
    private ICommonCouponService commonCouponService;
    @Autowired
    private IVrbtZeroOrderService vrbtZeroOrderService;

    @Autowired
    private IGanSuMobileApiService ganSuMobileApiService;
    @Autowired
    private SiChuanMobileApiService siChuanMobileApiService;
//    @Autowired
//    IKugouOrderService kugouOrderService;
//    @RabbitListener(queues = RabbitMQConfig.OUTSIDE_CALLBACK_QUEUE_NAME, concurrency = "5")
//    public void outsideCallbackListener(CallbackNotifyMessage callbackNotifyMessage) {
//        log.info("{}-收到回调通知消息:{}", LOG_TAG, callbackNotifyMessage);
////        final Subscribe subscribe = subscribeService.getById(callbackNotifyMessage.getId());
////        outsideCallbackService.outsideCallback(subscribe, callbackNotifyMessage.getMsg());
//    }
//    @Autowired
//    IDataNotifyLogService dataNotifyService;
//    /**
//     * 企业彩铃数据回执普通队列接收消息
//     * @param dataNotifyDelayedMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.QYCL_DATA_NOTIFY_VERIFY_EXPIRE_QUEUE_NAME,concurrency = "5")
//    public void  sendDataNotifyDelay1hourMessage(DataNotifyDelayedMessage dataNotifyDelayedMessage){
//        log.info("{}-数据回执收到延迟消息:{}",LOG_TAG,dataNotifyDelayedMessage);
//        final String msisdn = dataNotifyDelayedMessage.getMsisdn();
//        final String id = dataNotifyDelayedMessage.getId();
//        final String extra= dataNotifyDelayedMessage.getExtra();
//        dataNotifyService.receiveDataNotifyDelayMsg(msisdn,id,extra);
//    }

//
//    /**
//     * 咪咕互娱VR竞盟普通队列接收消息
//     * @param delayedMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_MGHY_VRJINGMENG_VERIFY_EXPIRE_QUEUE_NAME,concurrency = "5")
//    public void  sendMghyVRJingMengDelay1hourMessage(DelayedMessage delayedMessage){
//        log.info("{}-咪咕互娱VR竞盟收到订购状态校验消息:{}",LOG_TAG,delayedMessage);
//        final Subscribe subscribe = subscribeService.getById(delayedMessage.getId());
//        int verifyStatus = subscribeVerifyService.monthVerify(subscribe);
//        if (verifyStatus > -1) {
//            Subscribe sub = new Subscribe();
//            sub.setId(delayedMessage.getId());
//            sub.setStatus(verifyStatus);
//            subscribeService.updateSubscribeDbAndEs(sub);
//        }
//    }

//    /**
//     * 河图权益发送券码消息队列接收消息
//     * @param sendCodeDeductMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.HETU_SENDCODE_QUEUE_NAME,concurrency = "1")
//    public void  heTuSendCodeQueueListener(SendCodeDeductMessage sendCodeDeductMessage){
//        log.info("{}-河图权益发送券码消息队列接收消息:{}",LOG_TAG,sendCodeDeductMessage);
//        try {
//            //河图权益发送券码
//            couponCodeService.sendCodeScheduleDeduct(sendCodeDeductMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,sendCodeDeductMessage,e);
//        }
//    }

//    /**
//     * 支付宝视频彩铃权益充值消息队列接收消息
//     * @param aliPayVrbtMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.ALIPAY_VRBT_QUEUE_NAME,concurrency = "1")
//    public void  aliPayVrbtRightsQueueListener(AliPayVrbtMessage aliPayVrbtMessage){
//        log.info("{}-支付宝视频彩铃权益充值消息队列接收消息:{}",LOG_TAG,aliPayVrbtMessage);
//        try {
//            //支付宝视频彩铃权益充值
//            memberService.aliPayRechargeVrbt(aliPayVrbtMessage.getOrderId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,aliPayVrbtMessage,e);
//        }
//    }

//    /**
//     * 支付宝扣款提醒短信消息队列接收消息
//     * @param alipayDeductSendMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.ALIPAY_SEND_MESSAGE_QUEUE_NAME,concurrency = "1")
//    public void  aliPaySendMessageQueueListener(AlipayDeductSendMessage alipayDeductSendMessage){
//        log.info("{}-支付宝扣款提醒短信消息队列接收消息:{}",LOG_TAG,alipayDeductSendMessage);
//        try {
//
//            AliSignRecord aliSign = aliSignRecordService.lambdaQuery().eq(AliSignRecord::getId,alipayDeductSendMessage.getId()).one();
//            if(aliSign==null){
//                log.info("{}-支付宝扣款提醒短信队列任务-支付宝签约协议查询失败:{}",LOG_TAG,alipayDeductSendMessage);
//                return;
//            }
//            //支付宝扣款提醒短信
//            aliSignRecordService.sendDeductMsg(aliSign);
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,alipayDeductSendMessage,e);
//        }
//    }


//    /**
//     * 联通上传铃音消息队列接收消息
//     * @param liantongUploadRingMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.LINGTONG_UPLOAD_RING_QUEUE_NAME,concurrency = "5")
//    public void  lingtongUploadRingQueueListener(LiantongUploadRingMessage liantongUploadRingMessage){
//        log.info("{}-联通上传铃音消息队列接收消息:{}",LOG_TAG,liantongUploadRingMessage);
//        try {
//            //联通上传铃音
//            liantongRingService.uploadRing(liantongUploadRingMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,liantongUploadRingMessage,e);
//        }
//    }


//    /**
//     * 酷狗权益充值消息队列接收消息
//     * @param kuGouRightsRechargeMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.KUGOU_RIGHTS_RECHARGE_QUEUE_NAME,concurrency = "1")
//    public void  kuGouRightsRechargeQueueListener(KuGouRightsRechargeMessage kuGouRightsRechargeMessage){
//        log.info("{}-酷狗权益充值消息队列接收消息:{}",LOG_TAG,kuGouRightsRechargeMessage);
//        try {
//            //酷狗权益充值
//            kugouOrderService.kugouRecharge(kuGouRightsRechargeMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,kuGouRightsRechargeMessage,e);
//        }
//    }

//
//    /**
//     * 包月续订短信消息队列接收消息
//     * @param monthRenewSubSmsMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.MONTH_RENEW_SUB_SMS_QUEUE_NAME,concurrency = "1")
//    public void monthRenewSubSmsQueueListener(MonthRenewSubSmsMessage monthRenewSubSmsMessage){
//        log.info("{}-包月续订短信消息队列接收消息:{}",LOG_TAG,monthRenewSubSmsMessage);
//        try {
//            //酷狗权益充值
//            smsModelService.monthRenewSubSendSms(monthRenewSubSmsMessage.getChannel(),monthRenewSubSmsMessage.getMobile(),monthRenewSubSmsMessage.getBizType());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,monthRenewSubSmsMessage,e);
//        }
//    }

//    /**
//     * 南山途牛权益发送券码消息队列接收消息
//     * @param tuniuSendCodeDeductMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.TUNIU_SENDCODE_QUEUE_NAME,concurrency = "3")
//    public void  tuniuSendCodeQueueListener(TuniuSendCodeDeductMessage tuniuSendCodeDeductMessage){
//        log.info("{}-南山途牛权益发送券码消息队列接收消息:{}",LOG_TAG,tuniuSendCodeDeductMessage);
//        try {
//            //途牛权益发送券码
//            tuniuCouponCodeChargeLogService.sendCodeScheduleDeduct(tuniuSendCodeDeductMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,tuniuSendCodeDeductMessage,e);
//        }
//    }

//
//    /**
//     * 短剧券码消息队列接收消息
//     * @param sendSmsCodeDeductMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.DUANJU_SEND_SMS_CODE_QUEUE_NAME,concurrency = "3")
//    public void  duanjuSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage){
//        log.info("{}-短剧券码消息队列接收消息:{}",LOG_TAG,sendSmsCodeDeductMessage);
//        try {
//            //短剧发送券码
//            couponCodeService.duanjuSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,sendSmsCodeDeductMessage,e);
//        }
//    }
//
//    /**
//     * 水晶传说券码消息队列接收消息
//     * @param sendSmsCodeDeductMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.SJCS_SEND_SMS_CODE_QUEUE_NAME,concurrency = "3")
//    public void  sjcsSendCodeQueueListener(SendSmsCodeDeductMessage sendSmsCodeDeductMessage){
//        log.info("{}-水晶传说券码消息队列接收消息:{}",LOG_TAG,sendSmsCodeDeductMessage);
//        try {
//            //水晶传说发送券码
//            couponCodeService.sjcsSendCodeScheduleDeduct(sendSmsCodeDeductMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,sendSmsCodeDeductMessage,e);
//        }
//    }
//
//
//    /**
//     * 南山联联权益发送券码消息队列接收消息
//     * @param lianLianSendCodeDeductMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.LIANLIAN_SENDCODE_QUEUE_NAME,concurrency = "3")
//    public void  lianlianSendCodeQueueListener(LianLianSendCodeDeductMessage lianLianSendCodeDeductMessage){
//        log.info("{}-南山联联权益发送券码消息队列接收消息:{}",LOG_TAG,lianLianSendCodeDeductMessage);
//        try {
//            //联联权益发送券码
//            commonCouponService.sendCodeScheduleDeduct(lianLianSendCodeDeductMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!",LOG_TAG,lianLianSendCodeDeductMessage,e);
//        }
//    }

//    /**
//     * 免费包月订购视频彩铃消息队列接收消息
//     * @param subFreeMessage
//     */
//    @RabbitListener(queues = RabbitMQConfig.FREE_MONTH_SUB_VRBT_QUEUE_NAME, concurrency = "5-10")
//    public void freeMonthSubVrbtQueueListener(SubFreeMessage subFreeMessage) {
//        log.info("{}-免费包月订购视频彩铃消息队列接收消息:{}", LOG_TAG, subFreeMessage);
//        try {
//            //免费包月订购视频彩铃
//            vrbtZeroOrderService.freeMonthSubVrbt(subFreeMessage.getId());
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, subFreeMessage, e);
//        }
//    }
//
//
//    /**
//     * 甘肃移动普通队列接收消息
//     *
//     * @param jsonNode
//     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_GANSU_MOBILE_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "1")
//    public void gansuMobileDelay60SecondsListener(JsonNode jsonNode) {
//        log.info("{}-甘肃移动收到订购状态校验消息:{}", LOG_TAG, jsonNode);
//        try {
//            ganSuMobileApiService.subMQMsg(jsonNode);
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, jsonNode, e);
//        }
//    }

//
//    /**
//     * 四川移动渝姐充值普通队列接收消息
//     *
//     * @param jsonNode
//     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_SICHUAN_MOBILE_YUJIE_RECHARGE_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "1")
//    public void sichuanMobileYujieRechargeDelay5MinutesListener(JsonNode jsonNode) {
//        log.info("{}-四川移动收到渝姐充值消息:{}", LOG_TAG, jsonNode);
//        try {
//            siChuanMobileApiService.reChargeConsume(jsonNode);
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, jsonNode, e);
//        }
//    }
//    /**
//     * 四川移动包月校验普通队列接收消息
//     *
//     * @param jsonNode
//     */
//    @RabbitListener(queues = RabbitMQConfig.SUBSCRIBE_SICHUAN_MOBILE_MONTHLY_VERIFY_EXPIRE_QUEUE_NAME, concurrency = "1")
//    public void sichuanMobileMonthlyVerifyDelay1MinutesListener(JsonNode jsonNode) {
//        log.info("{}-四川移动收到包月校验消息:{}", LOG_TAG, jsonNode);
//        try {
//            siChuanMobileApiService.receiveSubscribeResult(jsonNode);
//        } catch (Exception e) {
//            log.info("{}-处理收到的消息:{},异常!", LOG_TAG, jsonNode, e);
//        }
//    }
}
