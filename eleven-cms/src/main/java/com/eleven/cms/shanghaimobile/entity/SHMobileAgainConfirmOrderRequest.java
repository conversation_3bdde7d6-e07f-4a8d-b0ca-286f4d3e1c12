package com.eleven.cms.shanghaimobile.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SHMobileAgainConfirmOrderRequest implements Serializable {

    /**
     * Request : {"BusiParams":{"randCode":"123456","billid":"18837426693"," offerId1":"390020010915"," offerId2":"111000750170","VasOfferInfo":[{"OfferId":"390020010915","OperType":"0","EffectiveType":"1"},{"OfferId":"111000750170","OperType":"0","EffectiveType":"1"}],"NeedSendMsg":"Y","sPrivData":{"m_iOpEntityId":"0","m_iVestOrgId":"0"},"ServiceNum":"13501619565"},"BusiCode":"PT-SH-FS-OI3066"}
     * PubInfo : {"InterfaceId":"25","TransactionId":"20191028155940TiABGB","InterfaceType":"05","OpId":"10017099","CountyCode":"86","OrgId":"580610","ClientIP":"***************","TransactionTime":"20191028155940","RegionCode":"210"}
     */

    @JsonProperty("Request")
    @JSONField(name="Request")
    private Request Request;
    @JsonProperty("PubInfo")
    @JSONField(name="PubInfo")
    private PubInfo PubInfo;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Request implements Serializable {
        /**
         * BusiParams : {"randCode":"123456","billid":"18837426693"," offerId1":"390020010915"," offerId2":"111000750170","VasOfferInfo":[{"OfferId":"390020010915","OperType":"0","EffectiveType":"1"},{"OfferId":"111000750170","OperType":"0","EffectiveType":"1"}],"NeedSendMsg":"Y","sPrivData":{"m_iOpEntityId":"0","m_iVestOrgId":"0"},"ServiceNum":"13501619565"}
         * BusiCode : PT-SH-FS-OI3066
         */

        @JsonProperty("BusiParams")
        @JSONField(name="BusiParams")
        private BusiParams BusiParams;
        @JsonProperty("BusiCode")
        @JSONField(name="BusiCode")
        private String BusiCode;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class BusiParams implements Serializable {
            /**
             * randCode : 123456
             * billid : 18837426693
             *  offerId1 : 390020010915
             *  offerId2 : 111000750170
             * VasOfferInfo : [{"OfferId":"390020010915","OperType":"0","EffectiveType":"1"},{"OfferId":"111000750170","OperType":"0","EffectiveType":"1"}]
             * NeedSendMsg : Y
             * sPrivData : {"m_iOpEntityId":"0","m_iVestOrgId":"0"}
             * ServiceNum : 13501619565
             */

            @JsonProperty("randCode")
            @JSONField(name="randCode")
            private String randCode;
            @JsonProperty("billid")
            @JSONField(name="billid")
            private String billid;
            @JsonProperty(" offerId1")
            @JSONField(name="offerId1")
            private String offerId1;
            @JsonProperty(" offerId2")
            @JSONField(name="offerId2")
            private String offerId2;
            @JsonProperty("offerId3")
            @JSONField(name="offerId3")
            private String offerId3;
            @JsonProperty("offerId4")
            @JSONField(name="offerId4")
            private String offerId4;
            @JsonProperty("offerId5")
            @JSONField(name="offerId5")
            private String offerId5;

            @JsonProperty("NeedSendMsg")
            @JSONField(name="NeedSendMsg")
            private String NeedSendMsg;
            @JsonProperty("sPrivData")
            @JSONField(name="sPrivData")
            private SPrivData sPrivData;
            @JsonProperty("ServiceNum")
            @JSONField(name="ServiceNum")
            private String ServiceNum;
            @JsonProperty("VasOfferInfo")
            @JSONField(name="VasOfferInfo")
            private List<VasOfferInfo> VasOfferInfo;

            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class SPrivData implements Serializable {
                /**
                 * m_iOpEntityId : 0
                 * m_iVestOrgId : 0
                 */

                @JsonProperty("m_iOpEntityId")
                @JSONField(name="m_iOpEntityId")
                private String mIOpEntityId;
                @JsonProperty("m_iVestOrgId")
                @JSONField(name="m_iVestOrgId")
                private String mIVestOrgId;
            }

            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class VasOfferInfo implements Serializable {
                /**
                 * OfferId : 390020010915
                 * OperType : 0
                 * EffectiveType : 1
                 */

                @JsonProperty("OfferId")
                @JSONField(name="OfferId")
                private String OfferId;
                @JsonProperty("OperType")
                @JSONField(name="OperType")
                private String OperType;
                @JsonProperty("EffectiveType")
                @JSONField(name="EffectiveType")
                private String EffectiveType;
            }
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class PubInfo implements Serializable {
        /**
         * InterfaceId : 25
         * TransactionId : 20191028155940TiABGB
         * InterfaceType : 05
         * OpId : 10017099
         * CountyCode : 86
         * OrgId : 580610
         * ClientIP : ***************
         * TransactionTime : 20191028155940
         * RegionCode : 210
         */

        @JsonProperty("InterfaceId")
        @JSONField(name="InterfaceId")
        private String InterfaceId;
        @JsonProperty("TransactionId")
        @JSONField(name="TransactionId")
        private String TransactionId;
        @JsonProperty("InterfaceType")
        @JSONField(name="InterfaceType")
        private String InterfaceType;
        @JsonProperty("OpId")
        @JSONField(name="OpId")
        private String OpId;
        @JsonProperty("CountyCode")
        @JSONField(name="CountyCode")
        private String CountyCode;
        @JsonProperty("OrgId")
        @JSONField(name="OrgId")
        private String OrgId;
        @JsonProperty("ClientIP")
        @JSONField(name="ClientIP")
        private String ClientIP;
        @JsonProperty("TransactionTime")
        @JSONField(name="TransactionTime")
        private String TransactionTime;
        @JsonProperty("RegionCode")
        @JSONField(name="RegionCode")
        private String RegionCode;
    }
}
