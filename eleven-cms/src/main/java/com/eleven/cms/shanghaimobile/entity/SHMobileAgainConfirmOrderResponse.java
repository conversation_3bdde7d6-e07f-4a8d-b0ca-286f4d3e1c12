package com.eleven.cms.shanghaimobile.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SHMobileAgainConfirmOrderResponse implements Serializable {


    /**
     * Response : {"RetInfo":{"bizCode":"1","DoneCode":0,"CHECKRSLTLIST":[{"CHECKRSLTINFO":{"ERRORLIST":[],"SUCCESSLIST":[],"WARNINGLIST":[],"CONFIRMLIST":[]}}]},"ErrorInfo":{"Message":"成功","Hint":"成功","Code":"0000"}}
     */

    @JsonProperty("Response")
    private Response Response;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Response implements Serializable {
        /**
         * RetInfo : {"bizCode":"1","DoneCode":0,"CHECKRSLTLIST":[{"CHECKRSLTINFO":{"ERRORLIST":[],"SUCCESSLIST":[],"WARNINGLIST":[],"CONFIRMLIST":[]}}]}
         * ErrorInfo : {"Message":"成功","Hint":"成功","Code":"0000"}
         */

        @JsonProperty("RetInfo")
        private RetInfo RetInfo;
        @JsonProperty("ErrorInfo")
        private ErrorInfo ErrorInfo;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class RetInfo implements Serializable {
            /**
             * bizCode : 1
             * DoneCode : 0
             * CHECKRSLTLIST : [{"CHECKRSLTINFO":{"ERRORLIST":[],"SUCCESSLIST":[],"WARNINGLIST":[],"CONFIRMLIST":[]}}]
             */

            @JsonProperty("bizCode")
            private String bizCode;
            @JsonProperty("DoneCode")
            private Integer DoneCode;
            @JsonProperty("CHECKRSLTLIST")
            private List<Checkrsltlist> checkrsltlist;

            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class Checkrsltlist implements Serializable {
                /**
                 * CHECKRSLTINFO : {"ERRORLIST":[],"SUCCESSLIST":[],"WARNINGLIST":[],"CONFIRMLIST":[]}
                 */

                @JsonProperty("CHECKRSLTINFO")
                private Checkrsltinfo checkrsltinfo;

                @JsonIgnoreProperties(ignoreUnknown = true)
                @Data
                public static class Checkrsltinfo implements Serializable {
                    @JsonProperty("ERRORLIST")
                    private List<?> errorlist;
                    @JsonProperty("SUCCESSLIST")
                    private List<?> successlist;
                    @JsonProperty("WARNINGLIST")
                    private List<?> warninglist;
                    @JsonProperty("CONFIRMLIST")
                    private List<?> confirmlist;
                }
            }
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ErrorInfo implements Serializable {
            /**
             * Message : 成功
             * Hint : 成功
             * Code : 0000
             */

            @JsonProperty("Message")
            private String Message;
            @JsonProperty("Hint")
            private String Hint;
            @JsonProperty("Code")
            private String Code;
        }
    }
}
