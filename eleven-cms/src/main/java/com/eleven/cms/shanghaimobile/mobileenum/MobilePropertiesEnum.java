package com.eleven.cms.shanghaimobile.mobileenum;
/**
 * 上海移动特惠包套餐配置枚举
 */
public enum MobilePropertiesEnum {
    //白名单号码判断
    WHITE_LIST_NUMBER_JUDG("06","210","CRM3903","96","PT-SH-FS-OI3903"),
    //业务授权
    BUSINESS_AUTHORIZATION("06","021","CRM3067","138","PT-SH-FS-OI3067"),
    //用户业务受理
    USER_BUSINESS_ACCEPTANCE("05","210","CRM3066","25","PT-SH-FS-OI3066"),
    //验证码短信下发
    VERIFICATIO_CODE_SHORT_MESSAGE_SEND("05","210","CRM5956","25","PT-SH-FS-OI5956"),
    //业务办理二次确认接口
    BUSINESS_ORDER_TWO_CONFIRM("05","210","CRM306602","25","PT-SH-FS-OI3066"),
    //权益办理二次确认接口
    RIGHTS_ORDER_TWO_CONFIRM("","","purchaseNew02","","");



    //接口类别
    private String interfaceType;
    //区域编码
    private String regionCode;
    //能力编码
    private String apiCode;
    //接口编号
    private String interfaceId;
    //业务编码
    private String busiCode;
    MobilePropertiesEnum(String interfaceType, String regionCode, String apiCode, String interfaceId,String busiCode) {
        this.interfaceType= interfaceType;
        this.regionCode= regionCode;
        this.apiCode= apiCode;
        this.interfaceId= interfaceId;
        this.busiCode= busiCode;
    }

    public String getInterfaceType() {
        return interfaceType;
    }

    public void setInterfaceType(String interfaceType) {
        this.interfaceType = interfaceType;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getApiCode() {
        return apiCode;
    }

    public void setApiCode(String apiCode) {
        this.apiCode = apiCode;
    }

    public String getInterfaceId() {
        return interfaceId;
    }

    public void setInterfaceId(String interfaceId) {
        this.interfaceId = interfaceId;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    @Override
    public String toString() {
        return "MobilePropertiesEnum{" +
                "interfaceType='" + interfaceType + '\'' +
                ", regionCode='" + regionCode + '\'' +
                ", apiCode='" + apiCode + '\'' +
                ", interfaceId='" + interfaceId + '\'' +
                ", busiCode='" + busiCode + '\'' +
                '}';
    }
}
