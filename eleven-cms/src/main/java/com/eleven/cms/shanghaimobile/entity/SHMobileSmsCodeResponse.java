package com.eleven.cms.shanghaimobile.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;



@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SHMobileSmsCodeResponse implements Serializable {


    /**
     * Response : {"RetInfo":{"bizDesc":"下发成功","bizCode":"0000"},"ErrorInfo":{"Message":"成功","Hint":"成功","Code":"0000"}}
     */

    @JsonProperty("Response")
    private Response Response;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Response implements Serializable {
        /**
         * RetInfo : {"bizDesc":"下发成功","bizCode":"0000"}
         * ErrorInfo : {"Message":"成功","Hint":"成功","Code":"0000"}
         */

        @JsonProperty("RetInfo")
        private RetInfo RetInfo;
        @JsonProperty("ErrorInfo")
        private ErrorInfo ErrorInfo;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class RetInfo implements Serializable {
            /**
             * bizDesc : 下发成功
             * bizCode : 0000
             */

            @JsonProperty("bizDesc")
            private String bizDesc;
            @JsonProperty("bizCode")
            private String bizCode;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ErrorInfo implements Serializable {
            /**
             * Message : 成功
             * Hint : 成功
             * Code : 0000
             */

            @JsonProperty("Message")
            private String Message;
            @JsonProperty("Hint")
            private String Hint;
            @JsonProperty("Code")
            private String Code;
        }
    }
}
