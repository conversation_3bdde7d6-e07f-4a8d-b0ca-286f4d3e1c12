package com.eleven.cms.shanghaimobile.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SHMobileSmsCodeRequest implements Serializable {

    /**
     * Request : {"BusiCode":"PT-SH-FS-OI5956","BusiParams":{"billId":"15812341234","offerId1":"350390208001"}}
     * PubInfo : {"InterfaceId":"61","TransactionId":"282cdf42e68c4cd083d4f5e1f3a25be7","OrgId":"568057","ClientIP":"**********","TransactionTime":"1634093688325","RegionCode":"210","InterfaceType":"06","ChannelId":"1","OpId":"780107"}
     */

    @JsonProperty("Request")
    @JSONField(name="Request")
    private Request Request;
    @JsonProperty("PubInfo")
    @JSONField(name="PubInfo")
    private PubInfo PubInfo;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Request implements Serializable {
        /**
         * BusiCode : PT-SH-FS-OI5956
         * BusiParams : {"billId":"15812341234","offerId1":"350390208001"}
         */

        @JsonProperty("BusiCode")
        @JSONField(name="BusiCode")
        private String BusiCode;
        @JsonProperty("BusiParams")
        @JSONField(name="BusiParams")
        private BusiParams BusiParams;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class BusiParams implements Serializable {
            /**
             * billId : 15812341234
             * offerId1 : 350390208001
             */

            @JsonProperty("billId")
            @JSONField(name="billId")
            private String billId;
            @JsonProperty("offerId1")
            @JSONField(name="offerId1")
            private String offerId1;
            @JsonProperty("offerId2")
            @JSONField(name="offerId2")
            private String offerId2;
            @JsonProperty("offerId3")
            @JSONField(name="offerId3")
            private String offerId3;
            @JsonProperty("offerId4")
            @JSONField(name="offerId4")
            private String offerId4;
            @JsonProperty("offerId5")
            @JSONField(name="offerId5")
            private String offerId5;


        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class PubInfo implements Serializable {
        /**
         * InterfaceId : 61
         * TransactionId : 282cdf42e68c4cd083d4f5e1f3a25be7
         * OrgId : 568057
         * ClientIP : **********
         * TransactionTime : 1634093688325
         * RegionCode : 210
         * InterfaceType : 06
         * ChannelId : 1
         * OpId : 780107
         */

        @JsonProperty("InterfaceId")
        @JSONField(name="InterfaceId")
        private String InterfaceId;
        @JsonProperty("TransactionId")
        @JSONField(name="TransactionId")
        private String TransactionId;
        @JsonProperty("OrgId")
        @JSONField(name="OrgId")
        private String OrgId;
        @JsonProperty("ClientIP")
        @JSONField(name="ClientIP")
        private String ClientIP;
        @JsonProperty("TransactionTime")
        @JSONField(name="TransactionTime")
        private String TransactionTime;
        @JsonProperty("RegionCode")
        @JSONField(name="RegionCode")
        private String RegionCode;
        @JsonProperty("InterfaceType")
        @JSONField(name="InterfaceType")
        private String InterfaceType;
        @JsonProperty("ChannelId")
        @JSONField(name="ChannelId")
        private String ChannelId;
        @JsonProperty("OpId")
        @JSONField(name="OpId")
        private String OpId;
    }
}
