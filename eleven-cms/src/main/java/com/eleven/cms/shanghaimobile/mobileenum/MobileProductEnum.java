package com.eleven.cms.shanghaimobile.mobileenum;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 上海移动特惠包套餐产品枚举
 */
public enum MobileProductEnum {
    //套餐5GB+10GB
    MEAL_DISCOUNT_FIVE_TEN_GB("12个月",BigDecimal.valueOf(3000L),BigDecimal.valueOf(2000L),"5GB+10GB","本地","5G特惠包限时特惠活动（10元专款+10GB流量）","390020010915","111000744954","101200002110","MEAL_DISCOUNT_FIVE_TEN_GB"),
    //套餐10GB+10G
    MEAL_DISCOUNT_TEN_TEN_GB("12个月",BigDecimal.valueOf(5000L),BigDecimal.valueOf(4000L),"10GB+10GB","本地","5G特惠包限时特惠活动（10元专款+10GB流量）","390020010917","111000744954","101200002110","MEAL_DISCOUNT_TEN_TEN_GB"),
    //套餐5GB+5GB
    MEAL_DISCOUNT_FIVE_FIVE_GB("12个月",BigDecimal.valueOf(3000L),BigDecimal.valueOf(1000L),"5GB+5GB","本地","5G特惠包限时特惠活动（20元专款+5GB流量）","390020010915","111000750170","101200002155","MEAL_DISCOUNT_FIVE_FIVE_GB"),
    //套餐10GB+5GB
    MEAL_DISCOUNT_TEN_FIVE_GB("12个月",BigDecimal.valueOf(5000L),BigDecimal.valueOf(3000L),"10GB+5GB","本地","5G特惠包限时特惠活动（20元专款+5GB流量）","390020010917","111000750170","101200002155","MEAL_DISCOUNT_TEN_FIVE_GB");
    //优惠期
    private String discountTerm;
    //5G特惠包档次
    private BigDecimal originalPrice;
    //优惠价
    private BigDecimal discountPrice;
    //资源
    private String resources;
    //套餐归属
    private String genusAddress;
    //对应活动
    private String activityType;
    //5G特惠包资费ID
    private String productId;
    //限时特惠活动资费ID
    private String activityId;
    //白名单ID
    private String openId;
    //枚举类型名字
    private String name;

    MobileProductEnum(String discountTerm, BigDecimal originalPrice, BigDecimal discountPrice, String resources, String genusAddress, String activityType, String productId, String activityId, String openId,String name) {
        this.discountTerm= discountTerm;
        this.originalPrice= originalPrice;
        this.discountPrice= discountPrice;
        this.resources= resources;
        this.genusAddress= genusAddress;
        this.activityType= activityType;
        this.productId= productId;
        this.activityId= activityId;
        this.openId= openId;
        this.name= name;
    }

    public String getDiscountTerm() {
        return discountTerm;
    }

    public void setDiscountTerm(String discountTerm) {
        this.discountTerm = discountTerm;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = discountPrice;
    }

    public String getResources() {
        return resources;
    }

    public void setResources(String resources) {
        this.resources = resources;
    }

    public String getGenusAddress() {
        return genusAddress;
    }

    public void setGenusAddress(String genusAddress) {
        this.genusAddress = genusAddress;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "MobileProductEnum{" +
                "discountTerm='" + discountTerm + '\'' +
                ", originalPrice=" + originalPrice +
                ", discountPrice=" + discountPrice +
                ", resources='" + resources + '\'' +
                ", genusAddress='" + genusAddress + '\'' +
                ", activityType='" + activityType + '\'' +
                ", productId='" + productId + '\'' +
                ", activityId='" + activityId + '\'' +
                ", openId='" + openId + '\'' +
                ", name='" + name + '\'' +
                '}';
    }

    public static MobileProductEnum getInstance(String productId, String openId) {
        for (MobileProductEnum mobileProductEnum : MobileProductEnum.values()) {
            if (mobileProductEnum.getProductId().equals(productId) && mobileProductEnum.getOpenId().equals(openId)) {
                return mobileProductEnum;
            }
        }
        return null;
    }
    public static String getInstance(BigDecimal originalPrice,String[] productIds, String openId) {
        MobileProductEnum productEnum=null;
        for (MobileProductEnum mobileProductEnum : MobileProductEnum.values()) {
            if (mobileProductEnum.getOriginalPrice().equals(originalPrice)  && Arrays.asList(productIds).contains(mobileProductEnum.getProductId()) && mobileProductEnum.getOpenId().equals(openId)) {
                productEnum=mobileProductEnum;
                break;
            }
        }
        if(productEnum!=null){
            return productEnum.getName();
        }else{
             for (MobileProductEnum mobileProductEnum : MobileProductEnum.values()) {
                if (Arrays.asList(productIds).contains(mobileProductEnum.getProductId()) && mobileProductEnum.getOpenId().equals(openId)) {
                    return mobileProductEnum.getName();
                }
            }
        }
        return null;
    }
    public static String getProductIds(String openId) {
        String productId="";
        for(MobileProductEnum mobileProductEnum:MobileProductEnum.values()){
            if(mobileProductEnum.getOpenId().equals(openId)){
                productId+=mobileProductEnum.getProductId()+",";
            }
        }
        return productId.substring(0,productId.length()-1);
    }

}
