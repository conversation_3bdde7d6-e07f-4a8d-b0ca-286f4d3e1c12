package com.eleven.cms.shanghaimobile.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SHMobileRightsConfirmRequest implements Serializable {

    /**
     * idType : 1
     * phone : 18721000001
     * randCode : 123456
     * opid : 01
     * offerId : 111000714217
     * channelOrderNo : O20210032902
     * orgid : 001
     * channelCode : CRM
     */

    @JsonProperty("idType")
    @JSONField(name="idType")
    private int idType;
    @JsonProperty("phone")
    @JSONField(name="phone")
    private String phone;
    @JsonProperty("randCode")
    @JSONField(name="randCode")
    private String randCode;
    @JsonProperty("opid")
    @JSONField(name="opid")
    private String opid;
    @JsonProperty("offerId")
    @JSONField(name="offerId")
    private String offerId;
    @JsonProperty("channelOrderNo")
    @JSONField(name="channelOrderNo")
    private String channelOrderNo;
    @JsonProperty("orgid")
    @JSONField(name="orgid")
    private String orgid;
    @JsonProperty("channelCode")
    @JSONField(name="channelCode")
    private String channelCode;
}
