package com.eleven.cms.shanghaimobile.entity;

import com.eleven.cms.shanghaimobile.util.JsonUtil;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR> createTime：2014年7月22日
 * 
 *         签名密钥模型
 */
@Data
@ToString
public class Ask {

	/**
	 * 公钥值
	 */
	private String publicKeyStr;

	/**
	 * 私钥值
	 */
	private String privateKeyStr;

	/**
	 * 剩余有效期时间 单位毫秒
	 */
	private Long restMills;

	/**
	 * 失效时间
	 */
	private Date endTime;

	/**
	 * 获得失效时间，第一次获取时通过剩余时间生成
	 * 
	 * @return
	 */
	public Date getEndTime() {
		if (endTime == null) {
			endTime = new Date(System.currentTimeMillis() + restMills);
		}
		return endTime;
	}

	public String toString() {
		return JsonUtil.toJsonString(this);
	}



}
