package com.eleven.cms.shanghaimobile.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BlackCheck implements Serializable {
    public static final String RETURN_CODE_SUCC = "200";
    /**
     * code : 200
     * data : {"result":false,"crtTime":""}
     * message : success
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("message")
    private String message;
    public boolean isOK() {
        return RETURN_CODE_SUCC.equals(this.getCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * result : false
         * crtTime :
         */

        @JsonProperty("result")
        private Boolean result;
        @JsonProperty("crtTime")
        private String crtTime;
    }
}
