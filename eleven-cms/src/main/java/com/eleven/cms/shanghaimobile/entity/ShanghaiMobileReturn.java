package com.eleven.cms.shanghaimobile.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ShanghaiMobileReturn implements Serializable{
    @JsonProperty("methodWhiteListReturn")
    @JSONField(name="methodWhiteListReturn")
    private MethodWhiteListReturn methodWhiteListReturn;
    @JsonProperty("businessJudgeReturn")
    @JSONField(name="businessJudgeReturn")
    private BusinessJudgeReturn businessJudgeReturn;
    @JsonProperty("businessHandleReturn")
    @JSONField(name="businessHandleReturn")
    private BusinessHandleReturn businessHandleReturn;
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class MethodWhiteListReturn implements Serializable {
        @JsonProperty("Response")
        @JSONField(name="Response")
        private ResponseBean Response;
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ResponseBean implements Serializable {
            @JsonProperty("ErrorInfo")
            @JSONField(name="ErrorInfo")
            private ErrorInfoBean ErrorInfo;
            @JsonProperty("RetInfo")
            @JSONField(name="RetInfo")
            private RetInfoBean RetInfo;
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class ErrorInfoBean implements Serializable {
                @JsonProperty("Code")
                @JSONField(name="Code")
                private String Code;
                @JsonProperty("Hint")
                @JSONField(name="Hint")
                private String Hint;
                @JsonProperty("Message")
                @JSONField(name="Message")
                private String Message;
            }
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class RetInfoBean implements Serializable {
                @JsonProperty("flag")
                @JSONField(name="flag")
                private int flag;
            }
        }
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class BusinessJudgeReturn implements Serializable {
        @JsonProperty("Response")
        @JSONField(name="Response")
        private ResponseBean Response;
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ResponseBean implements Serializable {
            @JsonProperty("ErrorInfo")
            @JSONField(name="ErrorInfo")
            private ErrorInfoBean ErrorInfo;
            @JsonProperty("RetInfo")
            @JSONField(name="RetInfo")
            private RetInfoBean RetInfo;
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class ErrorInfoBean implements Serializable {
                @JsonProperty("Code")
                @JSONField(name="Code")
                private String Code;
                @JsonProperty("Hint")
                @JSONField(name="Hint")
                private String Hint;
                @JsonProperty("Message")
                @JSONField(name="Message")
                private String Message;
            }
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class RetInfoBean implements Serializable {
                private List<CheckResultInfoBean> CheckResultInfo;
                @JsonIgnoreProperties(ignoreUnknown = true)
                @Data
                public static class CheckResultInfoBean implements Serializable {
                    @JsonProperty("Result")
                    @JSONField(name="Result")
                    private String Result;
                    @JsonProperty("OfferId")
                    @JSONField(name="OfferId")
                    private String OfferId;
                    @JsonProperty("Code")
                    @JSONField(name="Code")
                    private String Code;
                    @JsonProperty("Reason")
                    @JSONField(name="Reason")
                    private String Reason;
                }
            }
        }
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class BusinessHandleReturn implements Serializable {
        @JsonProperty("Response")
        @JSONField(name="Response")
        private ResponseBean Response;
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ResponseBean implements Serializable {
            @JsonProperty("ErrorInfo")
            @JSONField(name="ErrorInfo")
            private ErrorInfoBean ErrorInfo;
            @JsonProperty("RetInfo")
            @JSONField(name="RetInfo")
            private RetInfoBean RetInfo;
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class ErrorInfoBean implements Serializable {
                @JsonProperty("Code")
                @JSONField(name="Code")
                private String Code;
                @JsonProperty("Hint")
                @JSONField(name="Hint")
                private String Hint;
                @JsonProperty("Message")
                @JSONField(name="Message")
                private String Message;
            }
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class RetInfoBean implements Serializable {
                @JsonProperty("DoneCode")
                @JSONField(name="DoneCode")
                private long DoneCode;
            }
        }
    }

}
