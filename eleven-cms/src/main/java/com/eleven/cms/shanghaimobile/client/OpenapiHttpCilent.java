package com.eleven.cms.shanghaimobile.client;

import com.eleven.cms.service.IHttpRequestService;
import com.eleven.cms.shanghaimobile.entity.OpenapiResponse;
import com.eleven.cms.shanghaimobile.manage.SecurityI;
import com.eleven.cms.shanghaimobile.mobileenum.ResponseStatus;
import com.eleven.cms.shanghaimobile.util.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.core.util.ApplicationContextUtil;
import org.springframework.http.MediaType;
@Slf4j
@Data
public class OpenapiHttpCilent {
	private SecurityI securiytManager;
	/**
	 * @param apiCode 能力编码
	 * @param transactionId 业务编码
	 * @param requestBody 请求
	 * @param openapiUrl 接口请求地址
	 * @return
	 * @throws Exception
	 */
	public String call(String apiCode, String transactionId, String requestBody,String appCode,String openapiUrl,String ip,String msg,String mobile) throws Exception {
		// 1.对报文签名
		String publiceKey = securiytManager.getAsk(ip).getPublicKeyStr();
		Long aedkId = securiytManager.getAedk(ip).getId();
		requestBody = securiytManager.encrypt(requestBody,ip);
		String signValue = securiytManager.sign(requestBody);
		RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.TEXT_PLAIN_VALUE), requestBody);
		IHttpRequestService httpRequestService = ApplicationContextUtil.getContext().getBean(IHttpRequestService.class);
		String jsonData =httpRequestService.postRequest(openapiUrl,body,appCode,apiCode,transactionId,String.valueOf(aedkId),signValue,publiceKey,msg,mobile);
		try {
			if(StringUtils.isNotBlank(jsonData)){
				OpenapiResponse sr = JsonUtil.toBean(jsonData, OpenapiResponse.class);
				if (ResponseStatus.SUCCESS.toString().equals(sr.getStatus())) {
					String result = sr.getResult();
					if (securiytManager.verify(result, sr.getSignValue(), sr.getPublicKey())) {
						String otext = securiytManager.decrypt(result,ip);
						return otext;
					} else {
						return jsonData;
					}
				} else {
					return jsonData;
				}
			}
			return null;
		} catch (Exception e) {
			log.error("上海移动http请求异常",e);
			throw new Exception(e);
		}
	}
}
