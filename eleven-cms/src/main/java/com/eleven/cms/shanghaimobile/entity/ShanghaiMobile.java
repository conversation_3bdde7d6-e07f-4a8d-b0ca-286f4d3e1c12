package com.eleven.cms.shanghaimobile.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class ShanghaiMobile implements Serializable {

    @JsonProperty("methodWhiteList")
    @JSONField(name="methodWhiteList")
    private MethodWhiteList methodWhiteList;
    @JsonProperty("businessHandle")
    @JSONField(name="methodWhiteList")
    private BusinessHandle businessHandle;
    @JsonProperty("businessJudge")
    @JSONField(name="methodWhiteList")
    private BusinessJudge businessJudge;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class MethodWhiteList implements Serializable {
        @JsonProperty("Request")
        @JSONField(name="Request")
        private RequestBean Request;
        @JsonProperty("PubInfo")
        @JSONField(name="PubInfo")
        private PubInfoBean PubInfo;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class RequestBean implements Serializable {
            @JsonProperty("BusiParams")
            @JSONField(name="BusiParams")
            private BusiParamsBean BusiParams;
            @JsonProperty("BusiCode")
            @JSONField(name="BusiCode")
            private String BusiCode;
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class BusiParamsBean implements Serializable {
                @JsonProperty("billId")
                @JSONField(name="billId")
                private String billId;
                @JsonProperty("limitGrpId")
                @JSONField(name="limitGrpId")
                private String limitGrpId;
            }
        }
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class PubInfoBean implements Serializable {
            @JsonProperty("TransactionTime")
            @JSONField(name="TransactionTime")
            private String TransactionTime;
            @JsonProperty("OrgId")
            @JSONField(name="OrgId")
            private String OrgId;
            @JsonProperty("InterfaceType")
            @JSONField(name="InterfaceType")
            private String InterfaceType;
            @JsonProperty("RegionCode")
            @JSONField(name="RegionCode")
            private String RegionCode;
            @JsonProperty("OpId")
            @JSONField(name="OpId")
            private String OpId;
            @JsonProperty("InterfaceId")
            @JSONField(name="InterfaceId")
            private String InterfaceId;
            @JsonProperty("ChannelId")
            @JSONField(name="ChannelId")
            private String ChannelId;
            @JsonProperty("ClientIP")
            @JSONField(name="ClientIP")
            private String ClientIP;
            @JsonProperty("TransactionId")
            @JSONField(name="TransactionId")
            private String TransactionId;
        }
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class BusinessHandle implements Serializable {
        @JsonProperty("Request")
        @JSONField(name="Request")
        private RequestBean Request;
        @JsonProperty("PubInfo")
        @JSONField(name="PubInfo")
        private PubInfoBean PubInfo;
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class RequestBean implements Serializable {
            @JsonProperty("BusiParams")
            @JSONField(name="BusiParams")
            private BusiParamsBean BusiParams;
            @JsonProperty("BusiCode")
            @JSONField(name="BusiCode")
            private String BusiCode;
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class BusiParamsBean implements Serializable {
                @JsonProperty("sPrivData")
                @JSONField(name="sPrivData")
                private SPrivDataBean sPrivData;
                @JsonProperty("ServiceNum")
                @JSONField(name="ServiceNum")
                private String ServiceNum;
                @JsonProperty("Xopid")
                @JSONField(name="Xopid")
                private String Xopid;
                @JsonProperty("NeedSendMsg")
                @JSONField(name="NeedSendMsg")
                private String NeedSendMsg;
                @JsonProperty("Xorgid")
                @JSONField(name="Xorgid")
                private String Xorgid;
                @JsonProperty("VasOfferInfo")
                @JSONField(name="VasOfferInfo")
                private List<VasOfferInfoBean> VasOfferInfo;
                @JsonIgnoreProperties(ignoreUnknown = true)
                @Data
                public static class SPrivDataBean implements Serializable {
                    @JsonProperty("m_iOpEntityId")
                    @JSONField(name="m_iOpEntityId")
                    private String m_iOpEntityId;
                    @JsonProperty("m_iVestOrgId")
                    @JSONField(name="m_iVestOrgId")
                    private String m_iVestOrgId;
                }
                @JsonIgnoreProperties(ignoreUnknown = true)
                @Data
                public static class VasOfferInfoBean implements Serializable {
                    @JsonProperty("OfferId")
                    @JSONField(name="OfferId")
                    private String OfferId;
                    @JsonProperty("EffectiveType")
                    @JSONField(name="EffectiveType")
                    private String EffectiveType;
                    @JsonProperty("OperType")
                    @JSONField(name="OperType")
                    private String OperType;
                }
            }
        }
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class PubInfoBean implements Serializable {
            @JsonProperty("CountyCode")
            @JSONField(name="CountyCode")
            private String CountyCode;
            @JsonProperty("OrgId")
            @JSONField(name="OrgId")
            private String OrgId;
            @JsonProperty("TransactionTime")
            @JSONField(name="TransactionTime")
            private String TransactionTime;
            @JsonProperty("InterfaceType")
            @JSONField(name="InterfaceType")
            private String InterfaceType;
            @JsonProperty("RegionCode")
            @JSONField(name="RegionCode")
            private String RegionCode;
            @JsonProperty("OpId")
            @JSONField(name="OpId")
            private String OpId;
            @JsonProperty("InterfaceId")
            @JSONField(name="InterfaceId")
            private String InterfaceId;
            @JsonProperty("ClientIP")
            @JSONField(name="ClientIP")
            private String ClientIP;
            @JsonProperty("TransactionId")
            @JSONField(name="TransactionId")
            private String TransactionId;
        }
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class BusinessJudge implements Serializable {
        @JsonProperty("Request")
        @JSONField(name="Request")
        private RequestBean Request;
        @JsonProperty("PubInfo")
        @JSONField(name="PubInfo")
        private PubInfoBean PubInfo;
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class RequestBean implements Serializable {
            @JsonProperty("BusiParams")
            @JSONField(name="BusiParams")
            private BusiParamsBean BusiParams;
//            @JsonProperty("apiCode")
//            @JSONField(name="apiCode")
//            private String apiCode;
            @JsonProperty("BusiCode")
            @JSONField(name="BusiCode")
            private String BusiCode;
            @JsonIgnoreProperties(ignoreUnknown = true)
            @Data
            public static class BusiParamsBean implements Serializable {
                @JsonProperty("ServiceNum")
                @JSONField(name="ServiceNum")
                private String ServiceNum;
                @JsonProperty("sPrivData")
                @JSONField(name="sPrivData")
                private SPrivDataBean sPrivData;
                @JsonProperty("VasOfferInfo")
                @JSONField(name="VasOfferInfo")
                private List<VasOfferInfoBean> VasOfferInfo;
                @JsonIgnoreProperties(ignoreUnknown = true)
                @Data
                public static class SPrivDataBean implements Serializable {
                    @JsonProperty("m_iOrgId")
                    @JSONField(name="m_iOrgId")
                    private String m_iOrgId;
                    @JsonProperty("m_iOpId")
                    @JSONField(name="m_iOpId")
                    private String m_iOpId;
                    @JsonProperty("m_iVestOrgId")
                    @JSONField(name="m_iVestOrgId")
                    private String m_iVestOrgId;
                    @JsonProperty("m_iOpEntityId")
                    @JSONField(name="m_iOpEntityId")
                    private String m_iOpEntityId;
                }
                @JsonIgnoreProperties(ignoreUnknown = true)
                @Data
                public static class VasOfferInfoBean implements Serializable {
                    @JsonProperty("OfferId")
                    @JSONField(name="OfferId")
                    private String OfferId;
                    @JsonProperty("EffectiveType")
                    @JSONField(name="EffectiveType")
                    private String EffectiveType;
                    @JsonProperty("OperType")
                    @JSONField(name="OperType")
                    private String OperType;
                }
            }
        }
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class PubInfoBean implements Serializable {
            @JsonProperty("CountyCode")
            @JSONField(name="CountyCode")
            private String CountyCode;
            @JsonProperty("OrgId")
            @JSONField(name="OrgId")
            private String OrgId;
            @JsonProperty("TransactionTime")
            @JSONField(name="TransactionTime")
            private String TransactionTime;
            @JsonProperty("InterfaceType")
            @JSONField(name="InterfaceType")
            private String InterfaceType;
            @JsonProperty("RegionCode")
            @JSONField(name="RegionCode")
            private String RegionCode;
            @JsonProperty("OpId")
            @JSONField(name="OpId")
            private String OpId;
            @JsonProperty("InterfaceId")
            @JSONField(name="InterfaceId")
            private String InterfaceId;
            @JsonProperty("ClientIP")
            @JSONField(name="ClientIP")
            private String ClientIP;
            @JsonProperty("TransactionId")
            @JSONField(name="TransactionId")
            private String TransactionId;
        }
    }
}
