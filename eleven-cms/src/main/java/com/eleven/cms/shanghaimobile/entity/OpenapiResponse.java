package com.eleven.cms.shanghaimobile.entity;

import com.eleven.cms.shanghaimobile.util.JsonUtil;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class OpenapiResponse extends ServerResponse {
	private String signValue;
	private String publicKey;

	public OpenapiResponse() {

	}

	public OpenapiResponse(String result, String signValue, String publicKey) {
		super(result);
		this.signValue = signValue;
		this.publicKey = publicKey;
	}

	public OpenapiResponse(String result, String exceptionCode) {
		super(result, exceptionCode);
	}
	public String toString() {
		return JsonUtil.toJsonString(this);
	}
}
