package com.eleven.cms.shanghaimobile.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IHttpRequestService;
import com.eleven.cms.service.IShydChargeOrderService;
import com.eleven.cms.shanghaimobile.client.OpenapiHttpCilent;
import com.eleven.cms.shanghaimobile.entity.SHMobileAgainConfirmOrderRequest;
import com.eleven.cms.shanghaimobile.entity.SHMobileSmsCodeRequest;
import com.eleven.cms.shanghaimobile.manage.DefalutSecurity;
import com.eleven.cms.shanghaimobile.mobileenum.MobilePropertiesEnum;
import com.eleven.cms.shanghaimobile.properties.ShanghaiConfigMap;
import com.eleven.cms.shanghaimobile.properties.ShanghaiMobileProperties;
import com.eleven.cms.shanghaimobile.service.IShanghaiMobileService;
import com.eleven.cms.shanghaimobile.util.DateUtil;
import com.eleven.cms.shanghaimobile.util.ShanghaiMobileConstant;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class ShanghaiMobileServiceImpl implements IShanghaiMobileService {
    public static final String GET_SMS_CODE_MSG = "响应异常，无法判断验证码发送结果！";
    public static final String SUBMIT_SMS_CODE_MSG = "响应异常，无法判断开通结果！";
    public static final String SUB_SUCCESS = "开通成功";
    public static final String SMS_CODE_SUCCESS = "发送验证码成功";
    public static final String GET_SMS_LOG_TAG ="上海移动[发送验证码]";
    public static final String SUBMIT_SMS_LOG_TAG ="上海移动[业务办理二次确认]";
    public static final String RESPON_CODE = "0000";
    public static final String RESPON_BIZ_CODE="0";
    public static final String RESPON_BIZ_CODE_SUCCESS="1";
    @Autowired
    private ShanghaiMobileProperties shanghaiMobileProperties;
    //    @Autowired
//    private IShydPayLogService shydPayLogService;
    @Autowired
    private IShydChargeOrderService shydChargeOrderService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IHttpRequestService httpRequestService;
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//    private ShanghaiMobileReturn.MethodWhiteListReturn postMethodWhiteListNumberJudge(String phone, String orderId, String ip, String limitGrpId) {
//        //创建一个http客户端，参数为appCode,apk
//        OpenapiHttpCilent client = OpenapiHttpCilent();
//        try {
//            ShanghaiMobile.MethodWhiteList.RequestBean request=new ShanghaiMobile.MethodWhiteList.RequestBean();
//            ShanghaiMobile.MethodWhiteList.PubInfoBean pubInfo=new ShanghaiMobile.MethodWhiteList.PubInfoBean();
//            ShanghaiMobile.MethodWhiteList.RequestBean.BusiParamsBean busiParams=new ShanghaiMobile.MethodWhiteList.RequestBean.BusiParamsBean();
//            ShanghaiMobile.MethodWhiteList requestBody=new ShanghaiMobile.MethodWhiteList();
//
//            busiParams.setBillId(phone);
//            busiParams.setLimitGrpId(limitGrpId);
//            request.setBusiParams(busiParams);
//            request.setBusiCode(MobilePropertiesEnum.WHITE_LIST_NUMBER_JUDG.getBusiCode());
//            pubInfo.setTransactionTime(DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
//            pubInfo.setTransactionId(orderId);
//            pubInfo.setRegionCode(MobilePropertiesEnum.WHITE_LIST_NUMBER_JUDG.getRegionCode());
//            pubInfo.setOrgId(shanghaiMobileProperties.getOrgId());
//            pubInfo.setInterfaceId(MobilePropertiesEnum.WHITE_LIST_NUMBER_JUDG.getInterfaceId());
//            pubInfo.setInterfaceType(MobilePropertiesEnum.WHITE_LIST_NUMBER_JUDG.getInterfaceType());
//            pubInfo.setClientIP(ip);
//            pubInfo.setOpId(shanghaiMobileProperties.getOpId());
//            pubInfo.setChannelId(shanghaiMobileProperties.getChannelId());
//            requestBody.setRequest(request);
//            requestBody.setPubInfo(pubInfo);
//            String json= JSON.toJSONString(requestBody);
//            // 使用 HTTP方式调用openapi
//            log.info("上海移动[白名单号码判断请求]=>手机号:{},请求:{}",phone,json);
//            String response = client.call(MobilePropertiesEnum.WHITE_LIST_NUMBER_JUDG.getApiCode(), orderId,json,shanghaiMobileProperties.getAppCode(),shanghaiMobileProperties.getOpenapiUrl(),shanghaiMobileProperties.getWhiteIp());
//            log.info("上海移动[白名单号码判断响应]=>手机号:{},响应:{}",phone,response);
//            return JsonUtil.toBean(response, ShanghaiMobileReturn.MethodWhiteListReturn.class);
//        } catch (Exception e) {
//            log.error("上海移动[白名单号码判断异常]=>手机号:{},订单号:{}",phone,orderId,e);
//            return null;
//        }
//    }

//    /**
//     * 业务授权判断
//     * @param phone
//     * @param orderId
//     * @param ip
//     * @param offerIds
//     * @return
//     */
//    private ShanghaiMobileReturn.BusinessJudgeReturn postMethodBusinessJudge(String phone,String orderId,String ip,String offerIds) {
//        //创建一个http客户端，参数为appCode,apk
//        OpenapiHttpCilent client = OpenapiHttpCilent();
//        try {
//            ShanghaiMobile.BusinessJudge.RequestBean request=new ShanghaiMobile.BusinessJudge.RequestBean();
//            ShanghaiMobile.BusinessJudge.PubInfoBean pubInfo=new ShanghaiMobile.BusinessJudge.PubInfoBean();
//            ShanghaiMobile.BusinessJudge.RequestBean.BusiParamsBean busiParams=new ShanghaiMobile.BusinessJudge.RequestBean.BusiParamsBean();
//            ShanghaiMobile.BusinessJudge.RequestBean.BusiParamsBean.SPrivDataBean sPrivData=new ShanghaiMobile.BusinessJudge.RequestBean.BusiParamsBean.SPrivDataBean();
//            ShanghaiMobile.BusinessJudge requestBody=new ShanghaiMobile.BusinessJudge();
//            List<ShanghaiMobile.BusinessJudge.RequestBean.BusiParamsBean.VasOfferInfoBean> vasOfferInfo= Lists.newArrayList();
//            if (StringUtils.isNotBlank(offerIds)) {
//                Arrays.stream(offerIds.split(",")).forEach(offerId -> {
//                    ShanghaiMobile.BusinessJudge.RequestBean.BusiParamsBean.VasOfferInfoBean vasOfferInfoBean=new ShanghaiMobile.BusinessJudge.RequestBean.BusiParamsBean.VasOfferInfoBean();
//                    vasOfferInfoBean.setOfferId(offerId);
//                    vasOfferInfoBean.setEffectiveType(shanghaiMobileProperties.getEffectiveType());
//                    vasOfferInfoBean.setOperType(shanghaiMobileProperties.getOperType());
//                    vasOfferInfo.add(vasOfferInfoBean);
//                });
//            }
//            sPrivData.setM_iOrgId("");
//            sPrivData.setM_iVestOrgId("");
//            sPrivData.setM_iOpId("");
//            sPrivData.setM_iOpEntityId("");
//            pubInfo.setOrgId(shanghaiMobileProperties.getOrgId());
//            pubInfo.setTransactionTime(DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
//            pubInfo.setInterfaceType(MobilePropertiesEnum.BUSINESS_AUTHORIZATION.getInterfaceType());
//            pubInfo.setRegionCode(MobilePropertiesEnum.BUSINESS_AUTHORIZATION.getRegionCode());
//            pubInfo.setOpId(shanghaiMobileProperties.getOpId());
//            pubInfo.setInterfaceId(MobilePropertiesEnum.BUSINESS_AUTHORIZATION.getInterfaceId());
//            pubInfo.setClientIP(ip);
//            pubInfo.setTransactionId(orderId);
//            pubInfo.setCountyCode(shanghaiMobileProperties.getCountyCode());
//            busiParams.setServiceNum(phone);
//            busiParams.setSPrivData(sPrivData);
//            busiParams.setVasOfferInfo(vasOfferInfo);
//            request.setBusiParams(busiParams);
//            request.setBusiCode(MobilePropertiesEnum.BUSINESS_AUTHORIZATION.getBusiCode());
////            request.setApiCode(MobilePropertiesEnum.BUSINESS_AUTHORIZATION.getApiCode());
//            requestBody.setPubInfo(pubInfo);
//            requestBody.setRequest(request);
//            String json= JSONObject.toJSONString(requestBody);
//            // 使用 HTTP方式调用openapi
//            log.info("上海移动[业务授权判断请求]=>手机号:{},请求:{}",phone,json);
//            String response = client.call(MobilePropertiesEnum.BUSINESS_AUTHORIZATION.getApiCode(), orderId,json,shanghaiMobileProperties.getAppCode(),shanghaiMobileProperties.getOpenapiUrl(),shanghaiMobileProperties.getWhiteIp());
//            log.info("上海移动[业务授权判断响应]=>手机号:{},响应:{}",phone,response);
//            return JsonUtil.toBean(response, ShanghaiMobileReturn.BusinessJudgeReturn.class);
//        } catch (Exception e) {
//            log.error("上海移动[业务授权判断异常]=>手机号:{},订单号:{}",phone,orderId,e);
//            return null;
//        }
//
//    }

    private OpenapiHttpCilent OpenapiHttpCilent() {
        DefalutSecurity defalutSecurity = new DefalutSecurity();
        defalutSecurity.setUrl(shanghaiMobileProperties.getSecurityUrl() + "/1.0/KEY/" + shanghaiMobileProperties.getAppCode() + "/");
        defalutSecurity.setApk(shanghaiMobileProperties.getAppApk());
        OpenapiHttpCilent client = new OpenapiHttpCilent();
        client.setSecuriytManager(defalutSecurity);
        return client;
    }

//    /**
//     * 用户业务受理
//     * @param phone
//     * @param orderId
//     * @param ip
//     * @param offerIds
//     * @return
//     */
//    private ShanghaiMobileReturn.BusinessHandleReturn postMethodBusinessHandle(String phone,String orderId,String ip,String offerIds) {
//        //创建一个http客户端，参数为appCode,apk
//        OpenapiHttpCilent client = OpenapiHttpCilent();
//        try {
//            ShanghaiMobile.BusinessHandle.RequestBean request=new ShanghaiMobile.BusinessHandle.RequestBean();
//            ShanghaiMobile.BusinessHandle.RequestBean.BusiParamsBean busiParams=new ShanghaiMobile.BusinessHandle.RequestBean.BusiParamsBean();
//            ShanghaiMobile.BusinessHandle.RequestBean.BusiParamsBean.SPrivDataBean sPrivData=new ShanghaiMobile.BusinessHandle.RequestBean.BusiParamsBean.SPrivDataBean();
//            ShanghaiMobile.BusinessHandle.PubInfoBean pubInfo=new ShanghaiMobile.BusinessHandle.PubInfoBean();
//            ShanghaiMobile.BusinessHandle requestBody=new ShanghaiMobile.BusinessHandle();
//            List<ShanghaiMobile.BusinessHandle.RequestBean.BusiParamsBean.VasOfferInfoBean> vasOfferInfo= Lists.newArrayList();
//            if (StringUtils.isNotBlank(offerIds)) {
//                Arrays.stream(offerIds.split(",")).forEach(offerId -> {
//                    ShanghaiMobile.BusinessHandle.RequestBean.BusiParamsBean.VasOfferInfoBean vasOfferInfoBean=new ShanghaiMobile.BusinessHandle.RequestBean.BusiParamsBean.VasOfferInfoBean();
//                    vasOfferInfoBean.setOfferId(offerId);
//                    vasOfferInfoBean.setEffectiveType(shanghaiMobileProperties.getEffectiveType());
//                    vasOfferInfoBean.setOperType(shanghaiMobileProperties.getOperType());
//                    vasOfferInfo.add(vasOfferInfoBean);
//                });
//            }
//            busiParams.setVasOfferInfo(vasOfferInfo);
//            busiParams.setNeedSendMsg(shanghaiMobileProperties.getNeedSendMsg());
//            sPrivData.setM_iOpEntityId("");
//            sPrivData.setM_iVestOrgId("");
//            busiParams.setSPrivData(sPrivData);
//            busiParams.setServiceNum(phone);
//            busiParams.setXorgid(shanghaiMobileProperties.getOrgId());
//            busiParams.setXopid(shanghaiMobileProperties.getOpId());
//            request.setBusiParams(busiParams);
//            request.setBusiCode(MobilePropertiesEnum.USER_BUSINESS_ACCEPTANCE.getBusiCode());
//            pubInfo.setOrgId(shanghaiMobileProperties.getOrgId());
//            pubInfo.setTransactionTime(DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
//            pubInfo.setInterfaceType(MobilePropertiesEnum.USER_BUSINESS_ACCEPTANCE.getInterfaceType());
//            pubInfo.setRegionCode(MobilePropertiesEnum.USER_BUSINESS_ACCEPTANCE.getRegionCode());
//            pubInfo.setOpId(shanghaiMobileProperties.getOpId());
//            pubInfo.setInterfaceId(MobilePropertiesEnum.USER_BUSINESS_ACCEPTANCE.getInterfaceId());
//            pubInfo.setClientIP(ip);
//            pubInfo.setTransactionId(orderId);
//            pubInfo.setCountyCode(shanghaiMobileProperties.getCountyCode());
//            requestBody.setRequest(request);
//            requestBody.setPubInfo(pubInfo);
//            String  json= JSONObject.toJSONString(requestBody);
//            // 使用 HTTP方式调用openapi
//            log.info("上海移动[用户业务受理请求]=>手机号:{},订单号:{},请求:{},",phone,orderId,json);
//            String response = client.call(MobilePropertiesEnum.USER_BUSINESS_ACCEPTANCE.getApiCode(), orderId,json,shanghaiMobileProperties.getAppCode(),shanghaiMobileProperties.getOpenapiUrl(),shanghaiMobileProperties.getWhiteIp());
//            log.info("上海移动[用户业务受理响应]=>手机号:{},订单号:{},响应:{}",phone,orderId,response);
//            return JsonUtil.toBean(response, ShanghaiMobileReturn.BusinessHandleReturn.class);
//        } catch (Exception e) {
//            log.error("上海移动[用户业务受理异常]=>手机号:{},订单号:{}",phone,orderId,e);
//            return null;
//        }
//    }
//
//    @Override
//    public Result<?> shanghaiMobileHandle(String phone, String business, String ip,Integer isRight) {
//        //号码归属地判断
//        Result<?> result = mobileHomeAddress(phone);
//        if(!result.isOK()){
//            return result;
//        }
//        //流量包业务订阅
//        return  businessSubscriptionDataPack(phone, business, ip,isRight);
//    }

//    private Result<?> businessSubscriptionDataPack(String phone, String business, String ip,Integer isRight) {
//
//            ShanghaiConfigMap shanghaiConfigMap=shanghaiMobileProperties.getShanghaiConfigMap().get(business);
//            if(shanghaiConfigMap==null){
//                return Result.error("业务错误！");
//            }
//            String serviceId = ShanghaiBusinessTypeEnum.getServiceId(shanghaiConfigMap.getBusinessType());
//            String orderId = new StringBuffer()
//                    .append(serviceId)
//                    .append(phone)
//                    .append("_")
//                    .append(System.currentTimeMillis()).toString();
//            String businessName = ShanghaiBusinessTypeEnum.getBusinessName(shanghaiConfigMap.getBusinessType());
//            if(shydChargeOrderService.isMember(phone,businessName,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED)){
//                return Result.error(ShanghaiMobileConstant.BUSINESS_REPEAT_HANDLING_MSG,orderId);
//            }
//            //黑名单校验
//            Result<?> result=this.blackCheak( phone,  ip, business);
//            if(!result.isOK()){
//                return result;
//            }
//            //白名单号码判断
//            ShanghaiMobileReturn.MethodWhiteListReturn methodWhiteListReturn = this.postMethodWhiteListNumberJudge(phone, orderId, ip, shanghaiConfigMap.getOpenId());
//            if(methodWhiteListReturn==null || methodWhiteListReturn.getResponse()==null || methodWhiteListReturn.getResponse().getErrorInfo()==null || methodWhiteListReturn.getResponse().getRetInfo()==null){
//                return Result.error("上海移动[白名单号码判断失败]",orderId);
//            }
//            if(!StringUtils.equals(methodWhiteListReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED) || StringUtils.equals(methodWhiteListReturn.getResponse().getRetInfo().getFlag()+"",ShanghaiMobileConstant.IS_FLAG_FAIL+"")){
//                return Result.error(methodWhiteListReturn.getResponse().getErrorInfo().getMessage());
//            }
//            //业务授权
//            ShanghaiMobileReturn.BusinessJudgeReturn businessJudgeReturn = this.postMethodBusinessJudge(phone, orderId, ip, shanghaiConfigMap.getProductId());
//            if (businessJudgeReturn==null || businessJudgeReturn.getResponse()==null || businessJudgeReturn.getResponse().getRetInfo()==null || businessJudgeReturn.getResponse().getErrorInfo()==null || businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()==null || businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()<=0) {
//                return Result.error("上海移动[业务授权判断失败]",orderId);
//            }
//            if(!StringUtils.equals(businessJudgeReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED) && businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()!=null && businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()>0){
//                return Result.error(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().get(0).getReason(),orderId);
//            }
//            if(!StringUtils.equals(businessJudgeReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED)){
//                return Result.error(businessJudgeReturn.getResponse().getErrorInfo().getMessage(),orderId);
//            }
//            //判断是否有错误状态
//            Boolean hasSuccess = businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().stream().anyMatch(item-> !ShanghaiMobileConstant.BUSINESS_SUCCEED.equals(item.getCode()));
//            if (hasSuccess) {
//                return Result.error(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().get(0).getReason(),orderId);
//            }
//            //用户业务受理
//            ShanghaiMobileReturn.BusinessHandleReturn businessHandleReturn = this.postMethodBusinessHandle(phone, orderId, ip, shanghaiConfigMap.getProductId());
//            if (businessHandleReturn==null || businessHandleReturn.getResponse()==null || businessHandleReturn.getResponse().getErrorInfo()==null || businessHandleReturn.getResponse().getRetInfo()==null) {
//                return Result.error("上海移动[用户业务受理失败]",orderId);
//            }
//            if(!StringUtils.equals(businessHandleReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED)){
//                return Result.error(businessHandleReturn.getResponse().getErrorInfo().getMessage(),orderId);
//            }
//            //创建上海移动订购记录
//            shydChargeOrderService.saveShydChargeOrder(phone, orderId, Integer.valueOf(shanghaiConfigMap.getProductPrice()),shanghaiConfigMap.getProductId(),shanghaiConfigMap.getBusinessName(),businessName,isRight);
//            return Result.ok("开通成功",orderId);
//    }

//
//    @Override
//    public Result<?> shanghaiMobileDOUYIN(String phone, String ip,Integer isRight,String business) {
//        //号码归属地判断
//        Result<?> result = mobileHomeAddress(phone);
//        if(!result.isOK()){
//            return result;
//        }
//        //视频彩铃业务订阅
//        return businessSubscriptionDOUYIN(phone, ip,isRight,business);
//    }

//    private Result<?> mobileHomeAddress(String phone) {
//        MobileRegionResult regionResult=mobileRegionService.query(phone);
//        if (regionResult == null) {
//            return Result.noauth("系统繁忙,请稍后再试");
//        }else {
//            if(!ShanghaiMobileConstant.PROVINCE_SHANGHAI.equals(regionResult.getProvince())){
//                return Result.noauth("只支持上海移动用户办理业务,其他地区敬请期待");
//            }
//        }
//        if (regionResult.isIspDianxin()) {
//            return Result.noauth("暂不支持电信用户,敬请期待");
//        }
//        return Result.ok();
//    }

//    @Override
//    public Result<?> businessSubscriptionDOUYIN(String phone, String ip,Integer isRight,String business) {
//
//
//
//        ShanghaiConfigMap shanghaiConfigMap=shanghaiMobileProperties.getShanghaiConfigMap().get(business);
//        if(shanghaiConfigMap==null){
//            return Result.error("业务错误！");
//        }
//        String serviceId = ShanghaiBusinessTypeEnum.getServiceId(shanghaiConfigMap.getBusinessType());
//        String orderId = new StringBuffer()
//                .append(serviceId)
//                .append(phone)
//                .append("_")
//                .append(System.currentTimeMillis()).toString();
//        String businessName = ShanghaiBusinessTypeEnum.getBusinessName(shanghaiConfigMap.getBusinessType());
//        if(shydChargeOrderService.isMember(phone,businessName,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED)){
//            return Result.error(ShanghaiMobileConstant.BUSINESS_REPEAT_HANDLING_MSG,orderId);
//        }
//        //黑名单校验
//        Result<?> result=this.blackCheak( phone,  ip, business);
//        if(!result.isOK()){
//            return result;
//        }
//
//        //业务授权
//        ShanghaiMobileReturn.BusinessJudgeReturn businessJudgeReturn = this.postMethodBusinessJudge(phone, orderId, ip,  shanghaiConfigMap.getProductId());
//        if (businessJudgeReturn==null || businessJudgeReturn.getResponse()==null || businessJudgeReturn.getResponse().getRetInfo()==null || businessJudgeReturn.getResponse().getErrorInfo()==null || businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()==null || businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()<=0) {
//            return Result.error("上海移动[业务授权判断失败]",orderId);
//        }
//        if(!StringUtils.equals(businessJudgeReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED) && businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()!=null && businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()>0){
//            return Result.error(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().get(0).getReason(),orderId);
//        }
//        if(!StringUtils.equals(businessJudgeReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED)){
//            return Result.error(businessJudgeReturn.getResponse().getErrorInfo().getMessage(),orderId);
//        }
//        //判断是否有错误状态
//        Boolean hasSuccess = businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().stream().anyMatch(item-> !ShanghaiMobileConstant.BUSINESS_SUCCEED.equals(item.getCode()));
//        if (hasSuccess) {
//            return Result.error(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().get(0).getReason(),orderId);
//        }
//        //用户业务受理
//        ShanghaiMobileReturn.BusinessHandleReturn businessHandleReturn = this.postMethodBusinessHandle(phone, orderId, ip, shanghaiConfigMap.getProductId() + (StringUtils.isEmpty(shanghaiConfigMap.getPlanId())?"":"," + shanghaiConfigMap.getPlanId()));
//        if (businessHandleReturn==null || businessHandleReturn.getResponse()==null || businessHandleReturn.getResponse().getErrorInfo()==null || businessHandleReturn.getResponse().getRetInfo()==null) {
//            return Result.error("上海移动[用户业务受理失败]",orderId);
//        }
//        if(!StringUtils.equals(businessHandleReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED)){
//            return Result.error(businessHandleReturn.getResponse().getErrorInfo().getMessage(),orderId);
//        }
//        //创建移动特惠包订购订单记录
//        shydChargeOrderService.saveShydChargeOrder(phone, orderId, Integer.valueOf(shanghaiConfigMap.getProductPrice()),shanghaiConfigMap.getProductId(),shanghaiConfigMap.getBusinessName(),businessName,isRight,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED);
//        return Result.ok("开通成功",orderId);
//    }

//    private Result<?> blackCheak(String phone, String ip,String business) {
//        ShanghaiConfigMap shanghaiConfigMap=shanghaiMobileProperties.getShanghaiConfigMap().get(business);
//        if(shanghaiConfigMap==null){
//            return Result.error("业务错误！");
//        }
//        String orderId = new StringBuffer().append("SHYD").append(phone).append(System.currentTimeMillis()).toString();
//        //黑名单校验
//        BlackCheck blackCheck = this.blackCheakRequest(phone, orderId);
//        if (!blackCheck.isOK()) {
//            return Result.error("黑名单校验失败",orderId);
//        }
//        Boolean result=blackCheck.getData().getResult();
//        if(result){
//            return Result.error("您非指定用户，暂不能参与活动。",orderId);
//        }
//        return Result.ok("非黑名单",orderId);
//    }
//    private BlackCheck blackCheakRequest(String phone, String orderId) {
//        //创建一个http客户端，参数为appCode,apk
//        OpenapiHttpCilent client = OpenapiHttpCilent();
//        try {
//            ObjectNode dataNode = mapper.createObjectNode();
//            dataNode.put("checkNumber", phone);
//            String  json= dataNode.toString();
//            // 使用 HTTP方式调用openapi
//            log.info("上海移动[黑名单校验请求]=>手机号:{},请求:{},",phone,json);
//            String response = client.call("checkNumber", orderId,json,shanghaiMobileProperties.getAppCode(),shanghaiMobileProperties.getOpenapiUrl(),shanghaiMobileProperties.getWhiteIp());
//            log.info("上海移动[黑名单校验响应]=>手机号:{},订单号:{},响应:{}",phone,orderId,response);
//            return JsonUtil.toBean(response, BlackCheck.class);
//        } catch (Exception e) {
//            log.error("上海移动[黑名单校验异常]=>手机号:{},订单号:{}",phone,orderId,e);
//            return null;
//        }
//    }

//    @Override
//    public Result<?> businessLiuLiangBao(String phone, String ip,Integer isRight,String business) {
////        //关停上海移动5G流量特惠包
////        if(true){
////            return Result.error("下单失败");
////        }
//        String[] bus=business.split(",");
//        String productIds="";
//        String businessType="";
//        String planId="";
//        String productPrice="";
//        if(bus.length>0){
//            ShanghaiConfigMap shanghaiConfigMap=shanghaiMobileProperties.getShanghaiConfigMap().get(bus[0]);
//            if(shanghaiConfigMap!=null){
//                productIds+=shanghaiConfigMap.getProductId()+",";
//                businessType=shanghaiConfigMap.getBusinessType();
//                planId=shanghaiConfigMap.getPlanId();
//                productPrice=shanghaiConfigMap.getProductPrice();
//            }
//        }
//        if(bus.length>1){
//            ShanghaiConfigMap shanghaiConfigMap=shanghaiMobileProperties.getShanghaiConfigMap().get(bus[1]);
//            if(shanghaiConfigMap!=null){
//                productIds+=shanghaiConfigMap.getProductId();
//                businessType=shanghaiConfigMap.getBusinessType();
//                planId=shanghaiConfigMap.getPlanId();
//                productPrice=shanghaiConfigMap.getProductPrice();
//            }
//        }
//        String serviceId = ShanghaiBusinessTypeEnum.getServiceId(businessType);
//        String orderId = new StringBuffer()
//                .append(serviceId)
//                .append(phone)
//                .append("_")
//                .append(System.currentTimeMillis()).toString();
//        String businessName = ShanghaiBusinessTypeEnum.getBusinessName(businessType);
//        if(shydChargeOrderService.isMember(phone,businessName,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED)){
//            return Result.error(ShanghaiMobileConstant.BUSINESS_REPEAT_HANDLING_MSG,orderId);
//        }
//        //黑名单校验
//        Result<?> result=this.blackCheak( phone,  ip, bus[0]);
//        if(!result.isOK()){
//            return result;
//        }
//
//        //业务授权
//        ShanghaiMobileReturn.BusinessJudgeReturn businessJudgeReturn = this.postMethodBusinessJudge(phone, orderId, ip, productIds.split(",")[0]);
//        if (businessJudgeReturn==null || businessJudgeReturn.getResponse()==null || businessJudgeReturn.getResponse().getRetInfo()==null || businessJudgeReturn.getResponse().getErrorInfo()==null || businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()==null || businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()<=0) {
//            return Result.error("上海移动[业务授权判断失败]",orderId);
//        }
//        if(!StringUtils.equals(businessJudgeReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED) && businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo()!=null && businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().size()>0){
//            return Result.error(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().get(0).getReason(),orderId);
//        }
//        if(!StringUtils.equals(businessJudgeReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED)){
//            return Result.error(businessJudgeReturn.getResponse().getErrorInfo().getMessage(),orderId);
//        }
//        //判断是否有错误状态
//        Boolean hasSuccess = businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().stream().anyMatch(item-> !ShanghaiMobileConstant.BUSINESS_SUCCEED.equals(item.getCode()));
//        if (hasSuccess) {
//            return Result.error(businessJudgeReturn.getResponse().getRetInfo().getCheckResultInfo().get(0).getReason(),orderId);
//        }
//        //用户业务受理
//        ShanghaiMobileReturn.BusinessHandleReturn businessHandleReturn = this.postMethodBusinessHandle(phone, orderId, ip, productIds + (StringUtils.isEmpty(planId)?"":"," + planId));
//        if (businessHandleReturn==null || businessHandleReturn.getResponse()==null || businessHandleReturn.getResponse().getErrorInfo()==null || businessHandleReturn.getResponse().getRetInfo()==null) {
//            return Result.error("上海移动[用户业务受理失败]",orderId);
//        }
//        if(!StringUtils.equals(businessHandleReturn.getResponse().getErrorInfo().getCode(),ShanghaiMobileConstant.BUSINESS_SUCCEED)){
//            return Result.error(businessHandleReturn.getResponse().getErrorInfo().getMessage(),orderId);
//        }
//        //创建移动特惠包订购订单记录
//        shydChargeOrderService.saveShydChargeOrder(phone, orderId, Integer.valueOf(productPrice),productIds,businessName,businessName,isRight,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED);
//        return Result.ok("开通成功",orderId);
//    }


    /**
     * 发送验证码
     * @param phone
     * @param orderId
     * @param ip
     * @param offerIds
     * @return
     */
    private JsonNode shydSmsCode(String phone, String orderId, String ip, String offerIds) {
        //创建一个http客户端，参数为appCode,apk
        OpenapiHttpCilent client = OpenapiHttpCilent();
        try {
            SHMobileSmsCodeRequest smsRequest=new SHMobileSmsCodeRequest();
            SHMobileSmsCodeRequest.PubInfo pubInfo=new SHMobileSmsCodeRequest.PubInfo();
            pubInfo.setInterfaceId(MobilePropertiesEnum.VERIFICATIO_CODE_SHORT_MESSAGE_SEND.getInterfaceId());
            pubInfo.setTransactionId(orderId);
            pubInfo.setOrgId(shanghaiMobileProperties.getOrgId());
            pubInfo.setClientIP(ip);
            pubInfo.setTransactionTime(DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
            pubInfo.setRegionCode(MobilePropertiesEnum.VERIFICATIO_CODE_SHORT_MESSAGE_SEND.getRegionCode());
            pubInfo.setInterfaceType(MobilePropertiesEnum.VERIFICATIO_CODE_SHORT_MESSAGE_SEND.getInterfaceType());
            pubInfo.setChannelId(shanghaiMobileProperties.getChannelId());
            pubInfo.setOpId(shanghaiMobileProperties.getOpId());
            SHMobileSmsCodeRequest.Request request=new SHMobileSmsCodeRequest.Request();
            request.setBusiCode(MobilePropertiesEnum.VERIFICATIO_CODE_SHORT_MESSAGE_SEND.getBusiCode());
            SHMobileSmsCodeRequest.Request.BusiParams busiParams=new SHMobileSmsCodeRequest.Request.BusiParams();


//            if (StringUtils.isNotBlank(offerIds)) {
//                List<String> offerIdList = Arrays.stream(offerIds.split(",")).collect(Collectors.toList());
//                Map map = Maps.newHashMap();
//                IntStream.range(0, offerIdList.size()).forEach(index -> {
//                    String offerId = offerIdList.get(index);
//                    map.put("offerId" + (index+1), offerId);
//                });
//                busiParams = mapper.convertValue(map, SHMobileSmsCodeRequest.Request.BusiParams.class);
//            }

            if (StringUtils.isNotBlank(offerIds)) {
                String[] offerId=offerIds.split(",");
                if(offerId.length==1){
                    busiParams.setOfferId1(offerId[0]);
                }else if(offerId.length==2){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                }else if(offerId.length==3){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                    busiParams.setOfferId3(offerId[2]);
                }else if(offerId.length==4){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                    busiParams.setOfferId3(offerId[2]);
                    busiParams.setOfferId4(offerId[3]);
                }else if(offerId.length==5){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                    busiParams.setOfferId3(offerId[2]);
                    busiParams.setOfferId4(offerId[3]);
                    busiParams.setOfferId5(offerId[4]);
                }
            }
            busiParams.setBillId(phone);
            request.setBusiParams(busiParams);
            smsRequest.setRequest(request);
            smsRequest.setPubInfo(pubInfo);
            String json= JSONObject.toJSONString(smsRequest);
            // 使用 HTTP方式调用openapi
            log.info("{}-手机号:{},请求:{}",GET_SMS_LOG_TAG,phone,json);
            String response =client.call(MobilePropertiesEnum.VERIFICATIO_CODE_SHORT_MESSAGE_SEND.getApiCode(), orderId,json,shanghaiMobileProperties.getAppCode(),shanghaiMobileProperties.getOpenapiUrl(),shanghaiMobileProperties.getWhiteIp(),GET_SMS_LOG_TAG,phone);
            log.info("{}-手机号:{},响应:{}",GET_SMS_LOG_TAG,phone,response);
            final JsonNode jsonNode = mapper.readTree(response);
            return jsonNode;
        } catch (Exception e) {
            log.error("{}-异常,手机号:{},订单号:{}",GET_SMS_LOG_TAG,phone,orderId,e);
            return null;
        }

    }

    /**
     * 业务办理二次确认
     * @param phone
     * @param orderId
     * @param ip
     * @param offerIds
     * @return
     */
    private JsonNode againConfirmOrder(String phone, String orderId, String ip, String offerIds,String randCode) {
        //创建一个http客户端，参数为appCode,apk
        OpenapiHttpCilent client = OpenapiHttpCilent();
        try {
            SHMobileAgainConfirmOrderRequest confirmRequest=new SHMobileAgainConfirmOrderRequest();

            SHMobileAgainConfirmOrderRequest.Request request=new SHMobileAgainConfirmOrderRequest.Request();
            request.setBusiCode(MobilePropertiesEnum.BUSINESS_ORDER_TWO_CONFIRM.getBusiCode());
            SHMobileAgainConfirmOrderRequest.Request.BusiParams busiParams=new SHMobileAgainConfirmOrderRequest.Request.BusiParams();
            busiParams.setRandCode(randCode);
            busiParams.setBillid(phone);

//            if (StringUtils.isNotBlank(offerIds)) {
//                List<String> offerIdList = Arrays.stream(offerIds.split(",")).collect(Collectors.toList());
//                Map map = Maps.newHashMap();
//                IntStream.range(0, offerIdList.size()).forEach(index -> {
//                    String offerId = offerIdList.get(index);
//                    map.put("offerId" + (index+1), offerId);
//                });
//                busiParams = mapper.convertValue(map, SHMobileAgainConfirmOrderRequest.Request.BusiParams.class);
//            }
            if (StringUtils.isNotBlank(offerIds)) {
                String[] offerId=offerIds.split(",");
                if(offerId.length==1){
                    busiParams.setOfferId1(offerId[0]);
                }else if(offerId.length==2){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                }else if(offerId.length==3){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                    busiParams.setOfferId3(offerId[2]);
                }else if(offerId.length==4){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                    busiParams.setOfferId3(offerId[2]);
                    busiParams.setOfferId4(offerId[3]);
                }else if(offerId.length==5){
                    busiParams.setOfferId1(offerId[0]);
                    busiParams.setOfferId2(offerId[1]);
                    busiParams.setOfferId3(offerId[2]);
                    busiParams.setOfferId4(offerId[3]);
                    busiParams.setOfferId5(offerId[4]);
                }
            }
//            Y：需要；N：不需要
//            注意：策划配置的时候如果没有配置crm退订短信，此时接口传了Y，也不会下发退订短信。配置了退订短信，但是传了N，也是不会下发退订短信的。
            busiParams.setNeedSendMsg(shanghaiMobileProperties.getNeedSendMsg());
            SHMobileAgainConfirmOrderRequest.Request.BusiParams.SPrivData sPrivData=new SHMobileAgainConfirmOrderRequest.Request.BusiParams.SPrivData();
            sPrivData.setMIVestOrgId("0");
            sPrivData.setMIOpEntityId("0");
            busiParams.setServiceNum(phone);
            List<SHMobileAgainConfirmOrderRequest.Request.BusiParams.VasOfferInfo> vasOfferInfoList=Lists.newArrayList();
            if (StringUtils.isNotBlank(offerIds)) {
                Arrays.stream(offerIds.split(",")).forEach(offerId -> {
                    SHMobileAgainConfirmOrderRequest.Request.BusiParams.VasOfferInfo vasOfferInfo=new SHMobileAgainConfirmOrderRequest.Request.BusiParams.VasOfferInfo();
                    vasOfferInfo.setOfferId(offerId);
                    vasOfferInfo.setEffectiveType(shanghaiMobileProperties.getEffectiveType());
                    vasOfferInfo.setOperType(shanghaiMobileProperties.getOperType());
                    vasOfferInfoList.add(vasOfferInfo);
                });
            }
            busiParams.setVasOfferInfo(vasOfferInfoList);
            request.setBusiParams(busiParams);
            confirmRequest.setRequest(request);
            SHMobileAgainConfirmOrderRequest.PubInfo pubInfo=new SHMobileAgainConfirmOrderRequest.PubInfo();
            pubInfo.setInterfaceId(MobilePropertiesEnum.BUSINESS_ORDER_TWO_CONFIRM.getInterfaceId());
            pubInfo.setTransactionId(orderId);
            pubInfo.setInterfaceType(MobilePropertiesEnum.BUSINESS_ORDER_TWO_CONFIRM.getInterfaceType());
            pubInfo.setOpId(shanghaiMobileProperties.getOpId());
            pubInfo.setCountyCode(shanghaiMobileProperties.getCountyCode());
            pubInfo.setOrgId(shanghaiMobileProperties.getOrgId());
            pubInfo.setClientIP(ip);
            pubInfo.setTransactionTime(DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
            pubInfo.setRegionCode(MobilePropertiesEnum.BUSINESS_ORDER_TWO_CONFIRM.getRegionCode());
            confirmRequest.setPubInfo(pubInfo);
            String json= JSONObject.toJSONString(confirmRequest);
            // 使用 HTTP方式调用openapi
            log.info("{}-手机号:{},请求:{}",SUBMIT_SMS_LOG_TAG,phone,json);
            String response =client.call(MobilePropertiesEnum.BUSINESS_ORDER_TWO_CONFIRM.getApiCode(), orderId,json,shanghaiMobileProperties.getAppCode(),shanghaiMobileProperties.getOpenapiUrl(),shanghaiMobileProperties.getWhiteIp(),SUBMIT_SMS_LOG_TAG,phone);
            log.info("{}-手机号:{},响应:{}",SUBMIT_SMS_LOG_TAG,phone,response);
            final JsonNode jsonNode = mapper.readTree(response);
            return jsonNode;
        } catch (Exception e) {
            log.error("{}-异常,手机号:{},订单号:{}",SUBMIT_SMS_LOG_TAG,phone,orderId,e);
            return null;
        }

    }

//
//    /**
//     * 权益办理二次确认
//     * @param phone
//     * @param orderId
//     * @param offerIds
//     * @param randCode
//     * @return
//     */
//    private SHMobileRightsConfirmResponse rightsConfirmOrder(String phone, String orderId, String offerIds,String randCode) {
//        //创建一个http客户端，参数为appCode,apk
//        OpenapiHttpCilent client = OpenapiHttpCilent();
//        try {
//            SHMobileRightsConfirmRequest request=new SHMobileRightsConfirmRequest();
//            request.setIdType(1);
//            request.setPhone(phone);
//            request.setRandCode(randCode);
//            request.setOpid(shanghaiMobileProperties.getOpId());
//            request.setOfferId(offerIds);
//            request.setChannelOrderNo(orderId);
//            request.setOrgid(shanghaiMobileProperties.getOrgId());
//            request.setChannelCode("H5");
//            String json= JSONObject.toJSONString(request);
//            // 使用 HTTP方式调用openapi
//            log.info("上海移动[权益办理二次确认请求]=>手机号:{},请求:{}",phone,json);
//            String response = client.call(MobilePropertiesEnum.RIGHTS_ORDER_TWO_CONFIRM.getApiCode(), orderId,json,shanghaiMobileProperties.getAppCode(),shanghaiMobileProperties.getOpenapiUrl(),shanghaiMobileProperties.getWhiteIp());
//            log.info("上海移动[权益办理二次确认响应]=>手机号:{},响应:{}",phone,response);
//            return JsonUtil.toBean(response,SHMobileRightsConfirmResponse.class);
//        } catch (Exception e) {
//            log.error("上海移动[权益办理二次确认异常]=>手机号:{},订单号:{}",phone,orderId,e);
//            return null;
//        }
//    }

    /**
     * 上海移动业务办理新接口
     * @param phone
     * @param ip
     * @param isRight
     * @param business
     * @return
     */
    @Override
    public Result<?> shangHaiMobileBusinessOrder(String phone, String ip,Integer isRight,String business,String randCode,String orderId){
        //关停上海移动5G流量特惠包
//        if(true){
//            return Result.error("暂未开通");
//        }
        String[] bus=business.split(",");
        String productIds="";
        String businessType="";
        String planId="";
        String productPrice="";
        String productName="";


        if(bus.length>0){
            ShanghaiConfigMap shanghaiConfigMap=shanghaiMobileProperties.getShanghaiConfigMap().get(bus[0]);
            if(shanghaiConfigMap!=null){
                productIds+=shanghaiConfigMap.getProductId()+",";
                businessType=shanghaiConfigMap.getBusinessType();
                planId=shanghaiConfigMap.getPlanId();
                productPrice=shanghaiConfigMap.getProductPrice();
                productName=shanghaiConfigMap.getBusinessName();
            }
        }
        if(bus.length>1){
            ShanghaiConfigMap shanghaiConfigMap=shanghaiMobileProperties.getShanghaiConfigMap().get(bus[1]);
            if(shanghaiConfigMap!=null){
                productIds+=shanghaiConfigMap.getProductId();
                businessType=shanghaiConfigMap.getBusinessType();
                planId=shanghaiConfigMap.getPlanId();
                productPrice=shanghaiConfigMap.getProductPrice();
                productName=productName+":"+shanghaiConfigMap.getBusinessName();
            }
        }
        if(StringUtils.isBlank(orderId)){
            orderId = new StringBuffer().append("SHYD").append(phone) .append(System.currentTimeMillis()).toString();
        }
        if(shydChargeOrderService.isMember(phone,productName,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED)){
            return Result.error(ShanghaiMobileConstant.BUSINESS_REPEAT_HANDLING_MSG,orderId);
        }
//        //黑名单校验
//        Result<?> result=this.blackCheak( phone,  ip, bus[0]);
//        if(!result.isOK()){
//            return result;
////        }
//        Optional<String> offerId = Optional.ofNullable(StringUtils.isEmpty(planId) ? "" : null);
//        String offerIds = productIds+offerId.orElse("," + planId);

        if(StringUtils.isBlank(randCode)){
            //发送验证码
            JsonNode jsonNode= this.shydSmsCode(phone,orderId,ip,productIds + (StringUtils.isEmpty(planId)?"":"," + planId));

            if(jsonNode!=null){
                String code=jsonNode.at("/Response/ErrorInfo/Code").asText("");
                String message=jsonNode.at("/Response/ErrorInfo/Message").asText("");
                String bizCode=jsonNode.at("/Response/RetInfo/bizCode").asText("");
                String bizDesc=jsonNode.at("/Response/RetInfo/bizDesc").asText("");
                String remark=GET_SMS_LOG_TAG+"=>{\"code\":\""+code+"\",\"message\":\""+message+"\",\"bizCode\":\""+bizCode+"\",\"bizDesc\":\""+bizDesc+"\"}";
                if(StringUtils.equalsAny(RESPON_CODE,code)){
                    shydChargeOrderService.saveShydChargeOrder(phone, orderId, Integer.valueOf(productPrice),productIds,productName,remark,isRight,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SMS_CODE_SUCCEED);
                    return Result.ok(remark,orderId);
                }
                shydChargeOrderService.saveShydChargeOrder(phone, orderId, Integer.valueOf(productPrice),productIds,productName,remark,isRight,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SMS_CODE_FAIL);
                return Result.error(StringUtils.isNotBlank(bizDesc)?bizDesc:message,orderId);
            }
            shydChargeOrderService.saveShydChargeOrder(phone, orderId, Integer.valueOf(productPrice),productIds,productName,GET_SMS_CODE_MSG,isRight,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SMS_CODE_FAIL);
            return Result.error(GET_SMS_CODE_MSG,orderId);
        }else{
            //业务办理二次确认
            JsonNode jsonNode=this.againConfirmOrder(phone,orderId,ip,productIds + (StringUtils.isEmpty(planId)?"":"," + planId),randCode);
            if(jsonNode!=null){
                String code=jsonNode.at("/Response/ErrorInfo/Code").asText("");
                String message=jsonNode.at("/Response/ErrorInfo/Message").asText("");
                String Hint=jsonNode.at("/Response/ErrorInfo/Hint").asText("");
                String bizCode=jsonNode.at("/Response/RetInfo/bizCode").asText("");
                String doneCode=jsonNode.at("/Response/RetInfo/DoneCode").asText("");
                String remark=SUBMIT_SMS_LOG_TAG+"=>{\"code\":\""+code+"\",\"message\":\""+message+"\",\"Hint\":\""+Hint+"\",\"bizCode\":\""+bizCode+"\",\"doneCode\":\""+doneCode+"\"}";
                //创建移动特惠包订购订单记录
                if(RESPON_CODE.equals(code) && (RESPON_BIZ_CODE.equals(bizCode) || RESPON_BIZ_CODE_SUCCESS.equals(bizCode)|| RESPON_CODE.equals(bizCode))){
                    shydChargeOrderService.updateShydChargeOrder(orderId,remark,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED);
                    return Result.ok(remark,orderId);
                }
                shydChargeOrderService.updateShydChargeOrder(orderId,remark,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_FAIL);
                return Result.error(StringUtils.isNotBlank(Hint)?Hint:message,orderId);
            }
            shydChargeOrderService.updateShydChargeOrder(orderId,SUBMIT_SMS_CODE_MSG,ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_FAIL);
            return Result.error(SUBMIT_SMS_CODE_MSG,orderId);
        }
    }
}
