package com.eleven.cms.shanghaimobile.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SHMobileRightsConfirmResponse implements Serializable {

    /**
     * code : 200
     * msg : 成功
     * timestamp : 2020-08-12 14:31:32
     * data : {"orderNo":"O202100329021111111","channelOrderNo":"O20210032902","status":0}
     */

    @JsonProperty("code")
    @JSONField(name="code")
    private int code;
    @JsonProperty("msg")
    @JSONField(name="msg")
    private String msg;
    @JsonProperty("timestamp")
    @JSONField(name="timestamp")
    private String timestamp;
    @JsonProperty("data")
    @JSONField(name="data")
    private Data data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * orderNo : O202100329021111111
         * channelOrderNo : O20210032902
         * status : 0
         */

        @JsonProperty("orderNo")
        @JSONField(name="orderNo")
        private String orderNo;
        @JsonProperty("channelOrderNo")
        @JSONField(name="channelOrderNo")
        private String channelOrderNo;
        @JsonProperty("status")
        @JSONField(name="status")
        private int status;
    }
}
