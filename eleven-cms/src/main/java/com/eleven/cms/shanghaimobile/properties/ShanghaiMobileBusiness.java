package com.eleven.cms.shanghaimobile.properties;

import com.eleven.cms.util.BizConstant;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Desc:上海移动配置
 */
@Data
public class ShanghaiMobileBusiness {
    //视频彩铃订阅-炫视专属6元包
    public final static String SHANGHAI_MOBILE_VRBTXS= "vrbtxs";
    //抖音视频彩铃
    public final static String SHANGHAI_MOBILE_VRBTDY= "vrbtdy";
    //5G特惠包限时特惠活动（10元专款+10GB流量）[5GB+10GB]
    public final static String SHANGHAI_MOBILE_LLB510= "llb510";
    //5G特惠包限时特惠活动（10元专款+10GB流量）[10GB+10GB]
    public final static String SHANGHAI_MOBILE_LLB110= "llb110";
    //5G特惠包限时特惠活动（20元专款+5GB流量）[5GB+5GB]
    public final static String SHANGHAI_MOBILE_LLB55= "llb55";
    //5G特惠包限时特惠活动（20元专款+5GB流量）[10GB+5GB]
    public final static String SHANGHAI_MOBILE_LLB105= "llb105";
    //5G特惠包限时特惠活动（20元专款+5GB流量）[10GB+5GB]
    public final static String SHANGHAI_MOBILE_5GTHB= "5gthb,sh5gthb";
    //视频彩铃订阅-炫视专属9.9元包
    public final static String SHANGHAI_MOBILE_VRBTXS_VS= "vrbtxs9";
    //随心选会员
    public final static String SHANGHAI_MOBILE_SXXHY= "sxxhy";

    //流量版家庭产品
    public final static String SHANGHAI_MOBILE_LLBJT= "jkqyb,djjs";
    //炫视专属9.9元包&咕视频钻石会员（流量版 ）
    public final static String SHANGHAI_MOBILE_XSMGSP= "vrbtxs9,mgsp";
    //炫视流量版合约年包
    public final static String SHANGHAI_MOBILE_XSNB= "xsnb";
    //铂金会员PRO流量版
    public final static String SHANGHAI_MOBILE_BJHY= "bjhy";
    //黄金会员PRO流量优享版+随心选(上海流量版)
    public final static String SHANGHAI_MOBILE_HJSXX= "hjhy,sxxhy";
    //随心选(上海流量版)+随心选生活包(上海7天流量版)
    public final static String SHANGHAI_MOBILE_SXXSHB= "sxxhy,sxxshb";

    public static Map<String, String> productIdMap = new ImmutableMap.Builder<String, String>()
            .put(BizConstant.BIZ_CHANNEL_SHYD_DYSP,SHANGHAI_MOBILE_VRBTDY)
            .put(BizConstant.BIZ_CHANNEL_SHYD_XSSP,SHANGHAI_MOBILE_VRBTXS)
            .put(BizConstant.BIZ_CHANNEL_SHYD_LLB,SHANGHAI_MOBILE_5GTHB)
            .put(BizConstant.BIZ_CHANNEL_SHYD_XSSP_VS, SHANGHAI_MOBILE_VRBTXS_VS)
            .put(BizConstant.BIZ_CHANNEL_SHYD_SXXHY,SHANGHAI_MOBILE_SXXHY)
            .put(BizConstant.BIZ_CHANNEL_SHYD_LLBJT,SHANGHAI_MOBILE_LLBJT)
            .put(BizConstant.BIZ_CHANNEL_SHYD_XSMGSP,SHANGHAI_MOBILE_XSMGSP)
            .put(BizConstant.BIZ_CHANNEL_SHYD_XSNB,SHANGHAI_MOBILE_XSNB)
            .put(BizConstant.BIZ_CHANNEL_SHYD_BJHY,SHANGHAI_MOBILE_BJHY)
            .put(BizConstant.BIZ_CHANNEL_SHYD_HJSXX,SHANGHAI_MOBILE_HJSXX)
            .put(BizConstant.BIZ_CHANNEL_SHYD_SXXSHB,SHANGHAI_MOBILE_SXXSHB)
            .build();

}
