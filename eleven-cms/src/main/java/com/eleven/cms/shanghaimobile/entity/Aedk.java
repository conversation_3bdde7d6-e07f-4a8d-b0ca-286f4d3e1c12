package com.eleven.cms.shanghaimobile.entity;

import com.eleven.cms.shanghaimobile.util.JsonUtil;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR> createTime：2014年7月22日
 * 
 *         加解密密钥模型
 */
@Data
@ToString
public class Aedk {

	/**
	 * 加解密密钥id
	 */
	private Long id;

	/**
	 * 应用编码
	 */
	private String appCode;

	/**
	 * 密钥算法
	 */
	private String algorithm;

	/**
	 * 密钥值
	 */
	private String value;

	/**
	 * 失效时间
	 */
	private Date endTime;

	/**
	 * 有效期剩余时间 ＝ expireTime - currentTime
	 */
	private Long restTime;

	public Aedk() {

	}
	public String toString() {
		return JsonUtil.toJsonString(this);
	}

	public Date getEndTime() {
		if (endTime == null) {
			endTime = new Date(System.currentTimeMillis() + restTime);
		}
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
}