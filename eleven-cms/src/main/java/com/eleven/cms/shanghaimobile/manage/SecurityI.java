package com.eleven.cms.shanghaimobile.manage;

import com.eleven.cms.shanghaimobile.entity.Aedk;
import com.eleven.cms.shanghaimobile.entity.Ask;

public interface SecurityI {
	public Ask getAsk(String ip) throws Exception;

	public Aedk getAedk(String ip) throws Exception;

	public String encrypt(String text,String ip) throws Exception;

	public String decrypt(String text,String ip) throws Exception;

	public String sign(String text) throws Exception;

	public boolean verify(String text, String signValue, String publicKey) throws Exception;
}
