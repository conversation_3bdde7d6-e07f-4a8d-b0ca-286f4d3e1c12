package com.eleven.cms.shanghaimobile.manage;

import com.eleven.cms.service.IHttpRequestService;
import com.eleven.cms.shanghaimobile.entity.Aedk;
import com.eleven.cms.shanghaimobile.entity.Ask;
import com.eleven.cms.shanghaimobile.entity.GroupKey;
import com.eleven.cms.shanghaimobile.entity.ServerResponse;
import com.eleven.cms.shanghaimobile.mobileenum.ResponseStatus;
import com.eleven.cms.shanghaimobile.util.Base64Util;
import com.eleven.cms.shanghaimobile.util.CryptoUtil;
import com.eleven.cms.shanghaimobile.util.JsonUtil;
import com.eleven.cms.shanghaimobile.util.RandomUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecgframework.core.util.ApplicationContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
@Slf4j
@Data
public class DefalutSecurity implements SecurityI {
	private GroupKey groupKey;
	private CloseableHttpClient client = HttpClients.createDefault();
	private String url;
	private String apk;
	private PrivateKey privateKey;
	private static Map<String, Class<?>> map = new HashMap<String, Class<?>>();
	static {
		map.put("ask", Ask.class);
		map.put("aedk", Aedk.class);
	}

//	public DefalutSecurity(String url, String appCode, String apk) {
//		this.url = url + "/1.0/KEY/" + appCode + "/";
//		this.apk = apk;
//	}

	private void flushKey(String ip) throws Exception {
		groupKey = apply(ip);
		groupKey.getAedk().getEndTime();
		groupKey.getAsk().getEndTime();
		privateKey = getPrivateKey(groupKey.getAsk().getPrivateKeyStr());
	}

	@Override
	public Ask getAsk(String ip) throws Exception {
		if (groupKey == null || groupKey.getAsk().getEndTime().getTime() < System.currentTimeMillis()) {
			flushKey(ip);
		}
		return groupKey.getAsk();
	}

	@Override
	public Aedk getAedk(String ip) throws Exception {
		if (groupKey == null || groupKey.getAedk().getEndTime().getTime() < System.currentTimeMillis()) {
			flushKey(ip);
		}
		return groupKey.getAedk();
	}

	@Override
	public String encrypt(String text,String ip) throws Exception {
		byte[] secretByte = Base64Util.decode(getAedk(ip).getValue());
		SecretKey secretKey = new SecretKeySpec(secretByte, "DESede");
		String otext = CryptoUtil.encrypt(text, "DESede", secretKey);
		return otext;
	}

	@Override
	public String decrypt(String text,String ip) throws Exception {
		byte[] secretByte = Base64Util.decode(getAedk(ip).getValue());
		SecretKey secretKey = new SecretKeySpec(secretByte, "DESede");
		String otext = CryptoUtil.decrypt(text, "DESede", secretKey);
		return otext;
	}

	@SuppressWarnings("deprecation")
	private GroupKey apply(String ip) throws Exception {
		String getUrl = url + URLDecoder.decode(createSalt(apk),"UTF-8");
		try {
			IHttpRequestService httpRequestService = ApplicationContextUtil.getContext().getBean(IHttpRequestService.class);
			String jsonData =httpRequestService.implementHttpGetRequest(getUrl,"上海移动",ip);
			if(StringUtils.isNotBlank(jsonData)){
				ServerResponse sr = JsonUtil.toBean(jsonData, ServerResponse.class);
				if (sr.getStatus().equals(ResponseStatus.SUCCESS.toString())) {
					byte[] secretByte = Base64Util.decode(apk);
					SecretKey secretKey = new SecretKeySpec(secretByte, "DESede");
					String otext = CryptoUtil.decrypt(sr.getResult(), "DESede", secretKey);
					GroupKey gk = JsonUtil.toBean(otext, GroupKey.class);
					return gk;
				}
			}
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		}
	}

	/**
	 * 创建盐值
	 *
	 * @param apk
	 * @return
	 * @throws EnumConstantNotPresentException
	 */
	private String createSalt(String apk) {
		String securitySalt = null;
		String randomSalt = RandomUtil.getRandomString(30, 2);
		byte[] apkByte = Base64Util.decode(apk);
		SecretKey secretKey = new SecretKeySpec(apkByte, "DESede");
		securitySalt = CryptoUtil.encrypt(randomSalt, "DESede", secretKey);
		if (securitySalt.contains("/")||
				securitySalt.contains("+")||
				securitySalt.contains(" ")||
				securitySalt.contains("?")||
				securitySalt.contains("%")||
				securitySalt.contains("#")||
				securitySalt.contains("&")
		) {
			securitySalt = createSalt(apk);

		}
		return securitySalt;
	}

	@Override
	public String sign(String text) throws Exception {
		java.security.Signature signaturer = java.security.Signature.getInstance("MD5withRSA");
		signaturer.initSign(privateKey);
		signaturer.update(text.getBytes());
		String signValue = Base64Util.encodeString(signaturer.sign());
		return signValue;
	}

	@Override
	public boolean verify(String text, String signValue, String publicKey) throws Exception {
		java.security.Signature signaturer = java.security.Signature.getInstance("MD5withRSA");
		signaturer.initVerify(getPublicKey(publicKey));
		signaturer.update(text.getBytes());
		if (!signaturer.verify(Base64Util.decode(signValue))) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 通过公钥值还原成公钥
	 *
	 * @param publicKeyStr
	 * @return
	 * @throws InvalidKeySpecException
	 * @throws NoSuchAlgorithmException
	 */
	private static PublicKey getPublicKey(String publicKeyStr) throws InvalidKeySpecException, NoSuchAlgorithmException {
		X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64Util.decode(publicKeyStr));
		KeyFactory keyFactory = KeyFactory.getInstance("RSA");
		PublicKey pubKey = keyFactory.generatePublic(keySpec);
		return pubKey;
	}

	/**
	 * 通过私钥还原成私钥
	 *
	 * @param privateKeyStr
	 * @return
	 * @throws InvalidKeySpecException
	 * @throws NoSuchAlgorithmException
	 */
	private static PrivateKey getPrivateKey(String privateKeyStr) throws InvalidKeySpecException, NoSuchAlgorithmException {
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64Util.decode(privateKeyStr));
		KeyFactory keyFactory = KeyFactory.getInstance("RSA");
		PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
		return priKey;
	}

}
