package com.eleven.cms.shanghaimobile.service;

import com.eleven.cms.shanghaimobile.entity.ShanghaiMobileReturn;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface IShanghaiMobileService {
//
//    Result<?> shanghaiMobileHandle(String mobile, String business, String ip,Integer isRight);

//    Result<?> shanghaiMobileDOUYIN(String mobile, String ip,Integer isRight,String business);

//    Result<?> businessSubscriptionDOUYIN(String phone, String ip,Integer isRight,String business);

//    Result<?> businessLiuLiangBao(String phone, String ip,Integer isRight,String business);

    Result<?> shangHaiMobileBusinessOrder(String phone, String ip,Integer isRight,String business,String randCode,String orderId);
}
