package com.eleven.cms.shanghaimobile.mobileenum;

import java.math.BigDecimal;

/**
 * 上海移动彩铃PLUS产品枚举
 */
public enum MobileVideoSoundProductEnum {
    //彩铃PLUS
    MOBILE_VIDEO_SOUND_VIP("111000788275", "彩铃PLUS", BigDecimal.valueOf(990L), BigDecimal.valueOf(900L));
    //彩铃PLUSID
    private String productId;
    //彩铃PLUS名称
    private String productName;
    //原价
    private BigDecimal originalPrice;
    //优惠价
    private BigDecimal discountPrice;

    MobileVideoSoundProductEnum(String productId, String productName, BigDecimal originalPrice, BigDecimal discountPrice) {
        this.productId = productId;
        this.productName = productName;
        this.originalPrice = originalPrice;
        this.discountPrice = discountPrice;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = discountPrice;
    }

    @Override
    public String toString() {
        return "MobileVideoSoundProductEnum{" +
                "productId='" + productId + '\'' +
                ", productName='" + productName + '\'' +
                ", originalPrice=" + originalPrice +
                ", discountPrice=" + discountPrice +
                '}';
    }
}
