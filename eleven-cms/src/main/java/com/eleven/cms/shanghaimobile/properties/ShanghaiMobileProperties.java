package com.eleven.cms.shanghaimobile.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Desc:上海移动配置
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "shanghai.mobile",ignoreUnknownFields = true)
public class ShanghaiMobileProperties {
    //密钥校验地址
    private String securityUrl;
    //接口调用地址
    private String openapiUrl;
    //白名单类型编号
    private String limitGrpId;
    //厂商工号
    private String opId;
    //厂商编号
    private String orgId;
    //应用编号
    private String appCode;
    //apk密钥
    private String appApk;
    //县市编号
    private String countyCode;
    //生效类型
    private String effectiveType;
    //操作类型
    private String operType;
    //是否需要发送短信
    private String needSendMsg;
    //渠道Id
    private String channelId;
    //白名单
    private String whiteIp;
    //优先办理套餐
    private String amountMoney;

    private Map<String, ShanghaiConfigMap> shanghaiConfigMap = new HashMap<>();
}
