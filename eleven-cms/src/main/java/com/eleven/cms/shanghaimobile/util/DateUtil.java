package com.eleven.cms.shanghaimobile.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Locale;

import static java.time.temporal.ChronoField.*;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtil {

    public static final String FULL_TIME_PATTERN = "yyyyMMddHHmmss";
    
    public static final String FULL_TIME_PATTERN_WITH_MILL = "yyyyMMddHHmmssSSS";

    public static final String FULL_TIME_SPLIT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    
    public static final String YEAR_MONTH_PATTERN = "yyyyMM";

    public static final String CST_TIME_PATTERN = "EEE MMM dd HH:mm:ss zzz yyyy";

    public static final String DATE_START_TIME = " 00:00:00";
    public static final String DATE_END_TIME = " 23:59:59";

    public static String formatFullTime(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(dateTimeFormatter);
    }
    
    public static String formatFullTime(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, FULL_TIME_PATTERN);
    }

    public static String formatSplitTime(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, FULL_TIME_SPLIT_PATTERN);
    }

    public static String formatForMiguGroupApi(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, FULL_TIME_PATTERN_WITH_MILL);
    }

    public static String formatYearMonth(LocalDateTime localDateTime) {
        return formatFullTime(localDateTime, YEAR_MONTH_PATTERN);
    }

    public static String getDateFormat(Date date, String dateFormatType) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormatType, Locale.CHINA);
        return simpleDateFormat.format(date);
    }

    public static String formatCSTTime(String date, String format) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(CST_TIME_PATTERN, Locale.US);
        Date usDate = simpleDateFormat.parse(date);
        return DateUtil.getDateFormat(usDate, format);
    }

    public static String formatJava8IsoTime(String date) throws ParseException {
        LocalDateTime localDateTime = LocalDateTime.parse(date, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        return formatSplitTime(localDateTime);
    }

    public static String formatInstant(Instant instant, String format) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return localDateTime.format(DateTimeFormatter.ofPattern(format));
    }

    public static LocalDateTime parseString(String dateTimeStr,String pattern){
        DateTimeFormatter df = DateTimeFormatter.ofPattern(FULL_TIME_SPLIT_PATTERN);
        return LocalDateTime.parse(dateTimeStr,df);
    }

    /**
     * 为sql日期添加开始时间
     * @param dateWithoutTime 如 2020-03-28
     * @return 如 2020-03-28 00:00:00
     */
    public static String handleSqlDateStartTime(String dateWithoutTime){
        return dateWithoutTime+DATE_START_TIME;
    }

    /**
     * 为sql日期添加结束时间
     * @param dateWithoutTime 如 2020-03-28
     * @return 如 2020-03-28 23:59:59
     */
    public static String handleSqlDateEndTime(String dateWithoutTime){
        return dateWithoutTime+DATE_END_TIME;
    }


    /**
     * 为日期设置最小时间
     *
     * @param date
     *         如 2020-03-28
     * @return 如 2020-03-28 00:00:00
     */
    public static LocalDateTime dayMinTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MIN);
    }

    /**
     * 为日期设置最大时间
     *
     * @param date
     *         如 2020-03-28
     * @return 如 2020-03-28 23:59:59
     */
    public static LocalDateTime dayMaxTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MAX);
    }

    /**
     * 获取当月第一天的最小时间
     * @return
     */
    public static LocalDateTime getFirstDayOfMonthWithMinTime() {
        LocalDate firstDayThisMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        return LocalDateTime.of(firstDayThisMonth, LocalTime.MIN);
    }

    /**
     * 获取和指定时间相同月份的第一天的最小时间
     * @return
     */
    public static LocalDateTime getFirstDayOfMonthWithMinTime(LocalDateTime targetTime) {
        LocalDate firstDayThisMonth = targetTime.toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
        return LocalDateTime.of(firstDayThisMonth, LocalTime.MIN);
    }

    /**
     * 获取当月最后一天的最大时间
     * @return
     */
    public static LocalDateTime getLastDayOfMonthWithMaxTime() {
        LocalDate lastDayThisMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        return LocalDateTime.of(lastDayThisMonth, LocalTime.MAX);
    }

    /**
     * 获取和指定时间相同月份的最后一天的最大时间
     * @return
     */
    public static LocalDateTime getLastDayOfMonthWithMaxTime(LocalDateTime targetTime) {
        LocalDate lastDayThisMonth = targetTime.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        return LocalDateTime.of(lastDayThisMonth, LocalTime.MAX);
    }

    public static boolean isInCurrentYearMonth(LocalDateTime createTime) {
        LocalDate now = LocalDate.now();
        return createTime.getYear() == now.getYear() && createTime.getMonthValue() == now.getMonthValue();
    }

    public static void main(String[] args) {
        System.out.println("getFirstDayOfMonthWithMinTime() = " + getFirstDayOfMonthWithMinTime());
        System.out.println("getLastDayOfMonthWithMaxTime() = " + getLastDayOfMonthWithMaxTime());

        System.out.println(LocalDateTime.parse("2020-04-02T01:44:52",DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        LocalDate now = LocalDate.now();
        String currentYearMonth =  String.valueOf(now.getYear())+String.valueOf(now.getMonthValue());
        System.out.println("currentYearMonth = " + currentYearMonth);
        System.out.println(now.getMonth());

        LocalDateTime lastMonthFirstDay = LocalDate.of(2020,1,1).withDayOfMonth(1).minusMonths(1).atTime(LocalTime.MIN);
        System.out.println("currentYearMonth = " + formatYearMonth(lastMonthFirstDay));

        DateTimeFormatter formatter = new DateTimeFormatterBuilder().parseCaseInsensitive()
                                                            .append(DateTimeFormatter.ISO_LOCAL_DATE)
                                                            .appendLiteral(' ')
                                                            .appendValue(HOUR_OF_DAY, 2)
                                                            .appendLiteral(':')
                                                            .appendValue(MINUTE_OF_HOUR, 2)
                                                            .optionalStart()
                                                            .appendLiteral(':')
                                                            .appendValue(SECOND_OF_MINUTE, 2)
                                                            .optionalStart()
                                                            .toFormatter();
        String format = formatter.format(LocalDateTime.now());
        System.out.println("format = " + format);

        LocalDateTime localDate = LocalDate.of(2020, 5, 31).atTime(LocalTime.MAX).plusMonths(1);
        System.out.println("localDate = " + localDate);

    }




}
