package com.eleven.cms.shanghaimobile.entity;

import com.eleven.cms.shanghaimobile.mobileenum.ResponseStatus;
import com.eleven.cms.shanghaimobile.util.JsonUtil;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
public class ServerResponse {
	private Date sendDate;
	private String status;
	private String result;
	private String exceptionCode;
	private String errorCode;

	public ServerResponse() {

	}

	public ServerResponse(String result) {
		sendDate = new Date();
		this.status = ResponseStatus.SUCCESS.toString();
		this.result = result;
	}

	public ServerResponse(String result, String exceptionCode) {
		sendDate = new Date();
		this.result = result;
		this.status = ResponseStatus.ERROR.toString();
		this.exceptionCode = exceptionCode;
		this.errorCode = this.exceptionCode;
	}
	public String toString() {
		return JsonUtil.toJsonString(this);
	}
}
