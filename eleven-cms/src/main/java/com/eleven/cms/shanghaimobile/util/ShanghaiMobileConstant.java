package com.eleven.cms.shanghaimobile.util;

import com.baomidou.mybatisplus.annotation.TableField;

public class ShanghaiMobileConstant {
    // 上海移动短信验证码 Redis Key
    public static final String SHANGHAI_MOBILE_SMS_CODE_KEY_PREFIX = "sms_code_";
    // 上海移动短信验证码 Redis 过期时间 5分钟
    public static final Long   SHANGHAI_MOBILE_SMS_CODE_TTL = 5*60L;

    /**
     * 办理状态:0,初始状态已下单;1,办理成功;2,办理失败
     */
    public static final Integer HANDLE_STATUS_SUCCEED=1;
    public static final Integer HANDLE_STATUS_FAIL=2;
    /**
     * 流程:    0,初始状态已下单;1,白名单查询成功;2,白名单查询失败;3,业务鉴权成功;4,业务鉴权失败;5,用户业务受理成功;6,用户业务受理失败
     */
    public static final Integer FLOW_INITIAL_STATE_ORDER=0;
    public static final Integer FLOW_WHITE_QUERY_SUCCEED=1;
    public static final Integer FLOW_WHITE_QUERY_FAIL=2;
    public static final Integer FLOW_BUSINESS_AUTHENTICATION_SUCCEED=3;
    public static final Integer FLOW_BUSINESS_AUTHENTICATION_FAIL=4;
    public static final Integer FLOW_BUSINESS_HANDLE_SUCCEED=5;
    public static final Integer FLOW_BUSINESS_HANDLE_FAIL=6;
    //业务状态 0000 成功 500 失败
    public static final String BUSINESS_SUCCEED="0000";
    public static final String BUSINESS_FAIL="500";

    /**
     * 备注(类型:1=移动特惠包,2=彩铃PLUS)
     */
    public static final String MOBILE_SPECIAL_PACKAGE_TYPE="1";
    public static final String POLYPHONIC_RINGTONE_PLUS_TYPE="2";
    /**
     * 支付渠道:PHONEBILL
     */
    public static final String PAY_WAY="PHONEBILL";

    /**
     * 消息描述
     */
    public static final String MOBILE_SPECIAL_PACKAGE_MSG_SUCCEED="办理移动5G特惠包成功";
    public static final String MOBILE_SPECIAL_PACKAGE_MSG="移动5G特惠包已办理，请勿重复办理";
    public static final String BUSINESS_REPEAT_HANDLING_MSG="业务已办理，请勿重复办理";

    public static final String FLOW_INITIAL_STATE_ORDER_MSG="初始状态已下单";
    public static final String FLOW_WHITE_QUERY_SUCCEED_MSG="白名单查询成功";
    public static final String FLOW_WHITE_QUERY_FAIL_MSG="白名单查询失败";
    public static final String FLOW_BUSINESS_AUTHENTICATION_SUCCEED_MSG="业务鉴权成功";
    public static final String FLOW_BUSINESS_AUTHENTICATION_FAIL_MSG="业务鉴权失败";
    public static final String FLOW_BUSINESS_HANDLE_SUCCEED_MSG="用户业务受理成功";
    public static final String FLOW_BUSINESS_HANDLE_FAIL_MSG="用户业务受理失败";

    public static final String CHARGE_STATUS_NOT_HANDLE_MSG="未办理";
    public static final String CHARGE_STATUS_HANDLE_SUCCEED_MSG="办理成功";
    public static final String CHARGE_STATUS_HANDLE_FAIL_MSG="办理失败";

    /**
     * 办理状态:0,未办理;1,办理成功;2,办理失败;3,发送验证码成功;4,发送验证码失败
     */
    public static final Integer CHARGE_STATUS_NOT_HANDLE=0;
    public static final Integer CHARGE_STATUS_HANDLE_SUCCEED=1;
    public static final Integer CHARGE_STATUS_HANDLE_FAIL=2;
    public static final Integer CHARGE_STATUS_HANDLE_SMS_CODE_SUCCEED=3;
    public static final Integer CHARGE_STATUS_HANDLE_SMS_CODE_FAIL=4;

    /**
     * 结果办理是否成功:0=失败,1=成功
     */
    public static final Integer CALLBACK_STATUS_FAIL=0;
    public static final Integer CALLBACK_STATUS_SUCCEED=1;

    /**
     * 是否白名单:1=是,2=否
     */
    public static final Integer IS_FLAG_SUCCEED=1;
    public static final Integer IS_FLAG_FAIL=2;

    public static final String PROVINCE_SHANGHAI ="上海";

    /**
     * 是否拥有权益:0=否,1=是
     */
    public static final Integer IS_NOT_RIGHT=0;
    public static final Integer IS_YES_RIGHT=1;
}
