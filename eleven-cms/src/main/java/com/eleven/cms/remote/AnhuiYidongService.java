package com.eleven.cms.remote;

import com.eleven.cms.config.AnhuiProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.AnhuiResult;
import com.eleven.cms.vo.LiantongCrackResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 联通视频彩铃pojie
 *
 * @author: cai lei
 * @create: 2022-04-07 09:51
 */
@Slf4j
@Service
public class AnhuiYidongService {

    @Autowired
    private AnhuiProperties anhuiProperties;
    @Autowired
    private Environment environment;
    @Autowired
    RedisUtil redisUtil;

    public static final String LOG_TAG = "安徽移动api";

    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 请求短信验证码
     *
     * @param phone 19556033250
     * @return
     */
    public @Nonnull
    AnhuiResult getSms(String phone) {

        final HttpUrl httpUrl = HttpUrl.parse(anhuiProperties.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("code", anhuiProperties.getCode())
                .addQueryParameter("mobile", phone)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, AnhuiResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return AnhuiResult.FAIL_RESULT;
        }
    }

    /**
     * 提交短信验证码
     *
     * @param mobile
     * @param smsCode
     * @return
     */
    public @Nonnull
    AnhuiResult smsCode(String mobile, String smsCode) throws JsonProcessingException {

        AnhuiResult anhuiResult = (AnhuiResult) redisUtil.get(CacheConstant.CMS_CACHE_ANHUI_SMS_DATA + mobile);
        if (anhuiResult == null) {
            return AnhuiResult.FAIL_RESULT;
        }
        final HttpUrl httpUrl = HttpUrl.parse(anhuiProperties.getSmsCodeUrl())
                .newBuilder()
                .addQueryParameter("keyid", anhuiResult.getKeyId())
                .addQueryParameter("prod_prcid", anhuiResult.getProdPrcid())
                .addQueryParameter("smscode", smsCode)
                .addQueryParameter("exp_date", anhuiResult.getExpDate())
                .addQueryParameter("bff_date", anhuiResult.getBffDate())
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},请求:{}", LOG_TAG, mobile, smsCode, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},响应:{}", LOG_TAG, mobile, smsCode, content);
            return mapper.readValue(content, AnhuiResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},异常:", LOG_TAG, mobile, smsCode, e);
            return AnhuiResult.FAIL_RESULT;
        }
    }


}
