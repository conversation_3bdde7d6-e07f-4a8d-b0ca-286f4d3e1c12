package com.eleven.cms.remote;

import com.eleven.cms.config.HubeiYidongProperties;
import com.eleven.cms.util.MD5;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @author: lihb
 * @create: 2024年3月27日15:02:21
 */
@Slf4j
@Service
public class HubeiYidongService {

    public static final String LOG_TAG = "湖北移动api";
    @Autowired
    private HubeiYidongProperties hubeiYidongProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     *  短信验证码前获取参数拼接
     * @param phone 手机号
     * @return
     */
    public HubeiMobileGetSmsParamResult getSmsParam(String phone,String channel,String ip,String userAgent) {
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            String dateFormat = df.format(new Date());
            String url = hubeiYidongProperties.getBaseUrl() + "/api/getcodev2";
            String appId = hubeiYidongProperties.getAppId();
            String secret = hubeiYidongProperties.getSecret();
            String goodsId = hubeiYidongProperties.getGoodsIdByChannel(channel);
            String itemId = hubeiYidongProperties.getItemIdByChannel(channel);
            ObjectNode bodyMap = mapper.createObjectNode();
            bodyMap.put("appid",hubeiYidongProperties.getAppId());
            bodyMap.put("timestamp",dateFormat);
            bodyMap.put("sign", MD5.GetMD5Code(appId + dateFormat + secret));
            bodyMap.put("mobile",phone);
            bodyMap.put("busitype","vap");
            bodyMap.put("goodsid",goodsId);
            bodyMap.put("itemid",itemId);
            bodyMap.put("userip",ip);
            bodyMap.put("useragent",userAgent);
            RequestBody body = RequestBody.create(mediaType, bodyMap.toString());
            Request request = new Request.Builder().url(url)
                    .addHeader("Content-Type","application/json")
                    .post(body)
                    .build();
            log.info("{}-短信验证码前获取参数拼接-手机号:{},渠道号:{},body参数:{},请求:{}", LOG_TAG, phone,channel,bodyMap.toString(), request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-短信验证码前获取参数拼接-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            HubeiMobileGetSmsParamResult smsParamResult = mapper.readValue(result, HubeiMobileGetSmsParamResult.class);
            return smsParamResult;
        } catch (Exception e) {
            log.info("{}-短信验证码前获取参数拼接-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return HubeiMobileGetSmsParamResult.fail();
        }
    }

    /**
     *  校验验证码
     * @param phone 手机号
     * @return
     */
    public HubeiMobileVerifySmsResult verifySms(String phone,String channel,String checkNum,String smsCode) {
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            String dateFormat = df.format(new Date());
            String url = hubeiYidongProperties.getBaseUrl() + "/api/verifycode";
            String appId = hubeiYidongProperties.getAppId();
            String secret = hubeiYidongProperties.getSecret();
            ObjectNode bodyMap = mapper.createObjectNode();

            bodyMap.put("appid",hubeiYidongProperties.getAppId());
            bodyMap.put("timestamp",dateFormat);
            bodyMap.put("sign", MD5.GetMD5Code(appId + dateFormat + secret));
            bodyMap.put("mobile",phone);
            bodyMap.put("checknum",checkNum);
            bodyMap.put("code",smsCode);
            RequestBody body = RequestBody.create(mediaType, bodyMap.toString());
            Request request = new Request.Builder().url(url)
                    .addHeader("Content-Type","application/json")
                    .post(body)
                    .build();
            log.info("{}-校验验证码-手机号:{},验证码:{},渠道号:{},body参数:{},请求:{}", LOG_TAG, phone,smsCode,channel,bodyMap.toString(), request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-校验验证码-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            HubeiMobileVerifySmsResult verifySmsResult = mapper.readValue(result, HubeiMobileVerifySmsResult.class);
            return verifySmsResult;
        } catch (Exception e) {
            log.info("{}-校验验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return HubeiMobileVerifySmsResult.fail();
        }
    }

    /**
     *  业务校验
     * @param phone 手机号
     * @return
     */
    public HubeiMobileBusicheckResult busiCheck(String phone,String channel,String sessionId,String usessionId) {
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            String dateFormat = df.format(new Date());
            String url = hubeiYidongProperties.getBaseUrl() + "/api/busicheck";
            String appId = hubeiYidongProperties.getAppId();
            String secret = hubeiYidongProperties.getSecret();
            String goodsId = hubeiYidongProperties.getGoodsIdByChannel(channel);
            String itemId = hubeiYidongProperties.getItemIdByChannel(channel);
            ObjectNode bodyMap = mapper.createObjectNode();

            bodyMap.put("appid",hubeiYidongProperties.getAppId());
            bodyMap.put("timestamp",dateFormat);
            bodyMap.put("sign", MD5.GetMD5Code(appId + dateFormat + secret));
            bodyMap.put("mobile",phone);
            bodyMap.put("busitype","vap");
            bodyMap.put("goodsid",goodsId);
            bodyMap.put("itemid",itemId);
            bodyMap.put("sessionid",sessionId);
            bodyMap.put("usessionid",usessionId);
            RequestBody body = RequestBody.create(mediaType, bodyMap.toString());
            Request request = new Request.Builder().url(url)
                    .addHeader("Content-Type","application/json")
                    .post(body)
                    .build();
            log.info("{}-业务校验-手机号:{},渠道号:{},body参数:{},请求:{}", LOG_TAG, phone,channel,bodyMap.toString(), request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-业务校验-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            HubeiMobileBusicheckResult busicheckResult = mapper.readValue(result, HubeiMobileBusicheckResult.class);
            return busicheckResult;
        } catch (Exception e) {
            log.info("{}-业务校验-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return HubeiMobileBusicheckResult.fail();
        }
    }


    /**
     *  业务办理
     * @param phone 手机号
     * @return
     */
    public HubeiMobileSmsCodeResult smsCode(String phone,String channel,String eordersn) {
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            String dateFormat = df.format(new Date());
            String url = hubeiYidongProperties.getBaseUrl() + "/api/saveorder";
            String appId = hubeiYidongProperties.getAppId();
            String secret = hubeiYidongProperties.getSecret();
            ObjectNode bodyMap = mapper.createObjectNode();

            bodyMap.put("appid",hubeiYidongProperties.getAppId());
            bodyMap.put("timestamp",dateFormat);
            bodyMap.put("sign", MD5.GetMD5Code(appId + dateFormat + secret));
            bodyMap.put("eordersn",eordersn);
            RequestBody body = RequestBody.create(mediaType, bodyMap.toString());
            Request request = new Request.Builder().url(url)
                    .addHeader("Content-Type","application/json")
                    .post(body)
                    .build();
            log.info("{}-业务办理-手机号:{},渠道号:{},body参数:{},请求:{}", LOG_TAG, phone,channel,bodyMap.toString(), request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-业务办理-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            HubeiMobileSmsCodeResult smsCodeResult = mapper.readValue(result, HubeiMobileSmsCodeResult.class);
            return smsCodeResult;
        } catch (Exception e) {
            log.info("{}-业务办理-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return HubeiMobileSmsCodeResult.fail();
        }
    }
}
