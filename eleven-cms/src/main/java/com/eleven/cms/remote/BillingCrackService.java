package com.eleven.cms.remote;

import com.eleven.cms.config.BillingCrackProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.BillingResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:21
 * Desc:破解计费
 */
@Slf4j
@Service
public class BillingCrackService {
    public static final String LOG_TAG = "计费api";
    @Autowired
    private BillingCrackProperties billingCrackProperties;
    @Autowired
    private Environment environment;
    
    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        //if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
        //    this.client = this.client.newBuilder()
        //                             .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("**************", 9999)))
        //                             .build();
        //}
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                                           .compressed()
                                           .insecure()  //不检查自签名证书
                                           //.connectTimeout(120)
                                           //.retry(5)
                                           .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println,options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }

    /**
     * 请求短信验证码
     * @param phone
     * @return
     */
    public @Nonnull BillingResult getSms(String phone){
        final HttpUrl httpUrl = HttpUrl.parse(billingCrackProperties.getFetchSmsUrl())
                                     .newBuilder()
                                     .addQueryParameter("paycode", billingCrackProperties.getPaycode())
                                     .addQueryParameter("fee", billingCrackProperties.getFee())
                                     .addQueryParameter("cpid", billingCrackProperties.getCpid())
                                     .addQueryParameter("cpparm", "")
                                     .addQueryParameter("phone", phone)
                                     .build();
        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG ,phone, httpUrl.toString());
        Request request = new Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG ,phone, content);
            return mapper.readValue(content,BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return BillingResult.fail();
        }
    }

    /**
     * 提交短信验证码
     * @param transId
     * @param smsCode
     * @return
     */
    public @Nonnull BillingResult smsCode(String transId,String smsCode){
        final HttpUrl httpUrl = HttpUrl.parse(billingCrackProperties.getSmsCodeUrl())
                                       .newBuilder()
                                       .addQueryParameter("type", "1")
                                       .addQueryParameter("transId", transId)
                                       .addQueryParameter("smsCode", smsCode)
                                       .build();
        log.info("{}-提交短信-交易id:{},请求:{}", LOG_TAG ,transId, httpUrl.toString());
        Request request = new Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-交易id:{},响应:{}", LOG_TAG ,transId, content);
            return mapper.readValue(content,BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-交易id:{},异常:", LOG_TAG, transId, e);
            return BillingResult.fail();
        }
    }
}
