package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.VrbtDiyProperties;
import com.eleven.cms.entity.VrbtDiyVideo;
import com.eleven.cms.service.IVrbtDiyVideoService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.guizhou.Md5Utils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * Author: lihb
 * Date: 2023-6-20 15:06:29
 * Desc:视频彩铃DIY
 */
@Slf4j
@Service
public class VrbtDiyService {

    public static final String LOG_TAG = "视频彩铃DIY-api";

    @Autowired
    private VrbtDiyProperties vrbtDiyProperties;
    @Autowired
    private IVrbtDiyVideoService vrbtDiyVideoService;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().readTimeout(240L, TimeUnit.SECONDS).writeTimeout(240L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            //this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }

        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }


    /**
     * 接入平台调用“中音CMS”内容自动化申报接口进行视频彩铃内容信息上报（支持全网上线及分省定向上线）；
     * @param
     * @return
     */
    public String report(String phone,String mvUrl,String mvName){

        ObjectNode rootNode = mapper.createObjectNode();
        ObjectNode bodyNode = mapper.createObjectNode();
        ObjectNode headNode = mapper.createObjectNode();
        ArrayNode toneNode = mapper.createArrayNode();
        ArrayNode sprParametersNode = mapper.createArrayNode();
        String transactionId = "zy" + IdWorker.getId();
        String seq = System.currentTimeMillis() + "" + vrbtDiyProperties.getAccessPlatformId();

        try {
            //保存订单信息
            VrbtDiyVideo vrbtDiyVideo = new VrbtDiyVideo();
            vrbtDiyVideo.setMobile(phone);
            vrbtDiyVideo.setMvUrl(mvUrl);
            vrbtDiyVideo.setMvName(mvName);
            vrbtDiyVideo.setSeq(seq);
            vrbtDiyVideo.setTransactionId(transactionId);
            vrbtDiyVideoService.save(vrbtDiyVideo);
            toneNode.add("M");
            sprParametersNode.add(mapper.createObjectNode().put("paraName","type").put("paraValue","9"));
            sprParametersNode.add(mapper.createObjectNode().put("paraName","libraryType").put("paraValue","0"));//内容库分类
            sprParametersNode.add(mapper.createObjectNode().put("paraName","phone").put("paraValue",phone));//用户手机号码
            sprParametersNode.add(mapper.createObjectNode().put("paraName","provinceID").put("paraValue","88"));//分发手机号所属省份ID
            //构建body
            bodyNode.put("transactionID",transactionId);//事务ID,每次请求事务ID不能一样。同一个平台内值不能相同,必填
            bodyNode.put("cPID",vrbtDiyProperties.getCpId());
            bodyNode.set("toneType",toneNode);//制作产品类型,M：视频彩铃
            bodyNode.put("mvName",mvName);//视频名称，新增类型必填
            bodyNode.put("singerType","未分类");//歌手类型，新增类型必填, 值为空
            bodyNode.put("issuedArea","其他");//发行地区,新增类型必填
//            bodyNode.put("actor","");//表演者，新增类型必填
            bodyNode.put("maker","1");//制片者名称，新增类型必填
            bodyNode.put("makerRate","100");//制片者拥有比例
//            bodyNode.put("businessType","");//业务类型，新增类型必填
//            bodyNode.put("wordAuthor","");//词作者，新增类型必填
//            bodyNode.put("songAuthor","");//曲作者，新增类型必填
            bodyNode.put("validPeriod","60");//有效期，新增类型必填，单位：月有效期填写说明
            bodyNode.put("singleRight","非独家授权");//授权方式，新增类型必填
            bodyNode.put("language","中文");//语言，新增类型必填
            bodyNode.put("origCopyrightSide","1");//原始版权方，新增类型必填
            bodyNode.put("mvUrl",mvUrl);//素材文件路径(横屏或者竖屏)
//            bodyNode.put("description","");//描述信息，新增必填
            bodyNode.set("sprParameters",sprParametersNode);//描述信息，新增必填
            bodyNode.put("generalAuthorization","/userDiy/咪咕音乐有限公司数字音视频内容版权授权书.pdf");
            //构建head
            headNode.put("dID",vrbtDiyProperties.getDId());
            headNode.put("sEQ",seq);
            headNode.put("dIDPwd", Md5Utils.hash(seq + vrbtDiyProperties.getKey()));
            headNode.put("accessPlatformID",vrbtDiyProperties.getAccessPlatformId());
            headNode.put("syncType","00");

            rootNode.set("head",headNode);
            rootNode.set("body",bodyNode);

            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
            HttpUrl httpUrl = HttpUrl.parse(vrbtDiyProperties.getReportUrl())
                    .newBuilder()
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();
            log.info("{}-请求:{},手机号:{},body:\n{}", LOG_TAG,request.toString(),phone,mapper.writeValueAsString(rootNode));

            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-手机号:{},响应:{}", LOG_TAG,phone,content);
                return vrbtDiyVideo.getId();
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("{}-手机号:{},异常:", LOG_TAG,phone, e);
            return "";
        }
    }
}
