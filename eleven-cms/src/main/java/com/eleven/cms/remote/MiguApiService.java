package com.eleven.cms.remote;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aivrbt.utils.PatternUtil;
import com.eleven.cms.annotation.UnsubscribeLimit;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.MiguChannelConfig;
import com.eleven.cms.config.SchSpecialProductConfig;
import com.eleven.cms.config.XunfeiVrbtProperties;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IMusicService;
import com.eleven.cms.service.IOrderVrbtService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.util.RawValue;
import com.google.common.base.Strings;
import com.google.common.collect.Streams;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.eleven.cms.vo.VrbtCombinResult.*;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:46
 * Desc:咪咕sdk相关api(开放平台)
 */
@Slf4j
@Service
public class MiguApiService {
    public static final String MIGU_TOKEN_KEY_PREFIX = "miguToken:";
    //token 缓存时间 30分钟
    public static final long MIGU_TOKEN_CATCHE_TTL_SECONDS = 1800L;
    //默认渠道号使用订阅包的渠道号
    public  final static String CH_DYB_DEFAULT = "00210OC";
    //渠道包-渠道包
    public  final static String CH_QDB = "00210OW";

    //贝乐虎订阅包
    public  final static String CH_BLH_DYB = "00210QZ";


    //视频彩铃统一认证登录订阅侧渠道号
    public  final static String CH_DYB_ULOGIN = "00210K7";
    //视频彩铃统一认证登录渠道侧渠道号
    public  final static String CH_QDB_ULOGIN = "00210NX";
    //开放平台渠道号前缀
    public  final static String OPEN_CHANNEL_CODE_PREFFIX = "0021";
    //彩铃中心渠道号前缀
    public  final static String CENTRALITY_CHANNEL_CODE_PREFFIX = "014X";
    public  final static String CENTRALITY_CHANNEL_CODE_DEFAULT = "014X04C";
    //彩铃中心所有包统一使用的serviceId
    public  final static String CENTRALITY_SERVICE_ID = "698039035100000057";
    //音乐包主叫视频彩铃serviceId
    public  final static String MIXED_VRBT_FUN_ACTIVE_SERVICE_ID = "600923018000000007";
    //音乐包四川要开的彩铃包月特惠包serviceId
    public  final static String MIXED_SICHUAN_CRBT_MONTH_SERVICE_ID = "698039020100000134";
    //音乐包主叫视频彩铃serviceId
    public  final static String CPMB_VRBT_FUN_ACTIVE_SERVICE_ID = "600923018000000007";
    //音乐包四川要开的彩铃包月特惠包serviceId
    public  final static String CPMB_CRBT_MONTH_SERVICE_ID = "698039020100000134";
    //彩铃中心-订阅包
    public  final static String CENTRALITY_CHANNEL_CODE = "014X04F";
    //彩铃中心-订阅包 04E
    public  final static String CENTRALITY_CHANNEL_CODE_04E = "014X04E";
    //彩铃中心-订阅包 04C
    public  final static String CENTRALITY_CHANNEL_CODE_04C = "014X04C";
    //彩铃中心-订阅包 04D
    public  final static String CENTRALITY_CHANNEL_CODE_04D = "014X04D";
    //超炫XR-订阅包
    public  final static String CHAOXUAN_CHANNEL_CODE = "014X05A";
    //悦动专属6元包-订阅包
    public  final static String CHUANWANG_CHANNEL_CODE = "014X09P";
    public  final static String CHUANWANG_CHANNEL_CODE_9T = "014X09T";
    //讯飞-订阅包
    public  final static String XUNFEI_CHANNEL_CODE = "014X02D";
    public  final static String XUNFEI_CHANNEL_CODE_02F = "014X02F";
    public  final static String XUNFEI_CHANNEL_CODE_02G = "014X02G";

    //qiyin-订阅包
    public  final static String QIYIN_CHANNEL_CODE_04N = "014X04N";
    public  final static String QIYIN_CHANNEL_CODE_04O = "014X04O";
    public  final static String QIYIN_CHANNEL_CODE_04P = "014X04P";

    //振铃渠道号
    public static final String BIZ_RT_CHANNEL_CODE = "00210OA";
    //白金会员渠道号
    public static final String BIZ_BJHY_CHANNEL_CODE = "00210PP";
    //白金会员渠道号[备用]
    public static final String BIZ_BJHY_CHANNEL_CODE_U1 = "00210U1";
    //白金会员渠道号[备用]
    public static final String BIZ_BJHY_CHANNEL_CODE_U2 = "00210U2";
    //白金会员渠道号[备用]
    public static final String BIZ_BJHY_CHANNEL_CODE_U3 = "00210U3";
    //白金会员渠道号[备用]
    public static final String BIZ_BJHY_CHANNEL_CODE_U4 = "00210U4";
    //白金会员畅听版+视频彩铃(组合包10元)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE = "00210VB";
    //白金会员畅听版+视频彩铃(组合包15元)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_W0 = "00210W0";
    //白金会员畅听版+视频彩铃(组合包15元)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_W6 = "00210W6";
    //白金会员畅听版+视频彩铃(组合包15元)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_W7 = "00210W7";
    //白金会员畅听版+视频彩铃(组合包15元)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_XE = "00210XE";
    //白金会员畅听版+视频彩铃(组合包15元)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_XF = "00210XF";
    //白金会员畅听版+视频彩铃(组合包15元)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_XG = "00210XG";
    //白金会员畅听版+视频彩铃(组合包15元_讯飞)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XB = "00210XB";
    //白金会员畅听版+视频彩铃(组合包15元_讯飞)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XC = "00210XC";
    //白金会员畅听版+视频彩铃(组合包15元_讯飞)
    public static final String BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XD = "00210XD";
    //白金会员[宜搜]渠道号
    public static final String BIZ_BJHYYS_CHANNEL_CODE = "00210H0";
    //白金会员[宜搜]渠道号
    public static final String BIZ_BJHYYS_CHANNEL_CODE_TY = "00210TY";
    //白金会员[宜搜]渠道号
    public static final String BIZ_BJHYYS_CHANNEL_CODE_TZ = "00210TZ";
    //白金会员[动易]渠道号
    public static final String BIZ_BJHYDY_CHANNEL_CODE = "00210T7";
    //10元渠道包月包 趣享歌曲10元包（音乐全曲包）
    public static final String BIZ_CPMB_10_CHANNEL_CODE = "002103L";
    //10元渠道包月包 七彩歌曲10元包（音乐全曲包）
    public static final String BIZ_CPMB_10_CHANNEL_CODE_VO = "00210VO";
    //20元渠道包月包
    public static final String BIZ_CPMB_20_CHANNEL_CODE_1 = "00210Q6";
    public static final String BIZ_CPMB_20_CHANNEL_CODE_2 = "00210MK";
    public static final String BIZ_CPMB_20_CHANNEL_CODE_3 = "00210XZ";
    public static final String BIZ_CPMB_20_CHANNEL_CODE_4 = "00210Y0";
    //音乐全曲包-七彩音乐30元包
    public static final String BIZ_CPMB_30_CHANNEL_CODE = "002112O";
    public static final String BIZ_CPMB_30_CHANNEL_CODE_2S = "002112S";
    public static final String BIZ_CPMB_30_CHANNEL_CODE_2T = "002112T";
    public static final String BIZ_CPMB_30_CHANNEL_CODE_WU = "00210WU";
    public static final String BIZ_CPMB_30_CHANNEL_CODE_X1 = "00210X1";
    public static final String BIZ_CPMB_30_CHANNEL_CODE_X2 = "00210X2";
    //藕粉咪咕同享会10元包
    public static final String BIZ_AS_MEMBER_CHANNEL_CODE = "002105C";
    //藕粉咪咕同享会20元包
    public static final String BIZ_AS_MEMBER_CHANNEL_CODE_174 ="0021174";

    //20元渠道包月包 酷狗音乐北岸唐唱
    public static final String BIZ_CPMB_20_CHANNEL_CODE_KUGOU = "00210VV";
    //音乐全曲包-光明网15元包
    public static final String BIZ_CPMB_20_CHANNEL_CODE_W5 = "00210W5";
    //音乐全曲包-光明网15元包
    public static final String BIZ_CPMB_20_CHANNEL_CODE_09 = "0021109";
    //音乐全曲包-光明网15元包
    public static final String BIZ_CPMB_20_CHANNEL_CODE_0A = "002110A";
    //音乐全曲包-龙腾10元包
    public static final String BIZ_CPMB_20_CHANNEL_CODE_07 = "0021107";
    //音乐全曲包-龙腾10元包
    public static final String BIZ_CPMB_20_CHANNEL_CODE_08 = "0021108";
    //网易云音乐(乐趣音乐包不包含视频彩铃被叫功能)
    public static final String BIZ_CPMB_20_CHANNEL_CODE_D2 = "00211D2";
    //网易云音乐(乐趣音乐包不包含视频彩铃被叫功能)
    public static final String BIZ_CPMB_20_CHANNEL_CODE_D3 = "00211D3";
    //咪咕阅读渠道号
    public static final String BIZ_MIGU_RD_CHANNEL_CODE = "M3G60001";
    //视宣号-圈子彩铃-公众版10元包
    public static final String BIZ_SXH_CHANNEL_CODE = "002115U";
    //测试视宣号渠道辰文试视听-上海辰文信息技术有限公司  一个渠道号对应多个servicId,所以业务类型暂定为VRBT
    public static final String BIZ_SXH_CHANNEL_CODE_TEST = "00211A0";
    //讯飞-视宣号-圈子彩铃-彩铃小帮手-声动视铃A 10元
    public static final String BIZ_SXH_CHANNEL_CODE_XF = "0021180";
    //讯飞-视宣号-圈子彩铃-彩铃小帮手-声动视铃C 10元
    public static final String BIZ_SXH_CHANNEL_CODE_XF_AH = "00211AH";
    //讯飞-视宣号-圈子彩铃-彩铃小帮手-声动视铃B 10元
    public static final String BIZ_SXH_CHANNEL_CODE_XF_AI = "00211AI";
    //讯飞-视宣号-圈子彩铃-彩铃小帮手-飞扬视铃A 30元
    public static final String BIZ_SXH_CHANNEL_CODE_XF_8T = "002118T";
    /**
     * 视彩号 数智精品状态彩铃
     */
    public static final String BIZ_SCH_CHANNEL_CODE_SZR = "002118U";
    /**
     * AI视频彩铃-数智人-吱声
     */
    public static final String BIZ_SCH_CHANNEL_CODE_ZHISHENG = "00211H1";
    //00211M0    --AI视频彩铃-体验版-AI智绘模版 9.9
    //00211M1    --AI视频彩铃-标准版-智绘模板C 19.9
    //00211M3   --AI视频彩铃-标准版-智绘模板B 19.9
    //00211M2    --AI视频彩铃-标准版-智绘模板A 19.9
    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M0 = "00211M0";
    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M1 = "00211M1";
    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M2 = "00211M2";
    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M3 = "00211M3";
    /**
     * 视彩号 一语成片
     */
    public static final String BIZ_SCH_CHANNEL_CODE_YYCP = "00211DQ";
//    /**
//     * AI视频彩铃-数智人-吱声
//     */
//    public static final String BIZ_SCH_CHANNEL_CODE_ZHISHENG = "00211H1";
//    //00211M0    --AI视频彩铃-体验版-AI智绘模版 9.9
//    //00211M1    --AI视频彩铃-标准版-智绘模板C 19.9
//    //00211M3   --AI视频彩铃-标准版-智绘模板B 19.9
//    //00211M2    --AI视频彩铃-标准版-智绘模板A 19.9
//    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M0 = "00211M0";
//    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M1 = "00211M1";
//    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M2 = "00211M2";
//    public static final String BIZ_SCH_CHANNEL_CODE_ZHMB_M3 = "00211M3";
    /**
     * 视彩号 一语成片小程序用
     */
    public static final String BIZ_SCH_CHANNEL_CODE_YYCP_XCX = "00211GY";
    /**
     * 视彩号 一图穿越内部版
     *  00211MM、00211MN、00211MO
     */
    public static final String BIZ_SCH_CHANNEL_CODE_YTCY_KP_MM = "00211MM";
    /**
     * 视彩号 一图穿越内部版
     *  00211MM、00211MN、00211MO
     */
    public static final String BIZ_SCH_CHANNEL_CODE_YTCY_KP_MN = "00211MN";
    /**
     * 视彩号 一图穿越内部版
     *  00211MM、00211MN、00211MO
     */
    public static final String BIZ_SCH_CHANNEL_CODE_YTCY_KP_MO = "00211MO";
    public static final String BIZ_SCH_CHANNEL_CODE_YTCY_KP_W = "00211W4";
    public final static String AI_RING_SUB_CHIANNEL = "014X0JZ";

    /**
     * 视彩号 一图穿越内部版
     *  00211MM、00211MN、00211MO
     */
    public static final String BIZ_SCH_CHANNEL_CODE_YTCY_KP_MP = "00211MP";
    /**
     * 视彩号 一语成片I9
     */
    public static final String BIZ_SCH_CHANNEL_CODE_YYCP_SXJB = "014X0I9";
    //网易云渠道号
    public static final String BIZ_WYY_CHANNEL_CODE = "00210QL";

    //铃音产品id前缀
    public final static String VRBT_PRODUCT_ID_PREFFIX = "600";

    //视频彩铃功能类型  0：被叫个人视频彩铃功能
    public static final String  VRBT_FUN_PRODUCT_TYPE_PASSIVE = "0";
    //视频彩铃功能类型  1：主叫视频彩铃功能
    public static final String  VRBT_FUN_PRODUCT_TYPE_ACTIVE = "1";
    //包月业务类型 1：白金会员
    public static final String  MONTH_SERVICE_TYPE_BJHY = "1";
    //包月业务类型 2：音乐包
    public static final String  MONTH_SERVICE_TYPE_CPMB = "2";

    public static final String VRBT_TONE_ORDER_SETFLAG_NONE = "0";  //0----内容订购后不设置为在播；------即他人看和自己看两种都没有选择
    public static final String VRBT_TONE_ORDER_SETFLAG_BOTH_ZBJ = "1";  //1----同时设置主叫和被叫在播；------即他人看和自己看都选择了
    public static final String VRBT_TONE_ORDER_SETFLAG_BEIJIAO = "3"; //3----只设置为被叫在播； -----即用户只选择了他人看
    public static final String VRBT_TONE_ORDER_SETFLAG_ZHUJIAO = "9"; //9----只设置为主叫在播。 -----即用户只选择了自己看

    public static final String CHANNEL_SWTICH_PERCENT_KEY = "channel_swtich_percent_key";

    public static final String MOBILE_FOR_TEST = "13608044510";

    //需要开通视频彩铃内容服务2元包的省份
    public static final String[] CRBT_TEHUI_PACK_PROVINCES = {"四川"};
    //使用被叫视频彩铃功能的省份
    //public static final String[] VRBT_PASSIVE_PROVINCES = {"四川", "广东", "浙江", "重庆"};

    public static final okhttp3.MediaType JSON
            = okhttp3.MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);


    @Autowired
    private BizProperties bizProperties;
    @Autowired
    XunfeiVrbtProperties xunfeiVrbtProperties;
    @Autowired
    private IMusicService musicService;
    @Lazy
    @Autowired
    private IOrderVrbtService orderVrbtService;
    @Autowired
    private SignatureService signatureService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private Environment environment;
    @Qualifier("threadPoolExecutor")
    private ThreadPoolExecutor executor;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private MobileRegionService mobileRegionService;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder()
                //设置超时时间
                .connectTimeout(20L, TimeUnit.SECONDS)
                .readTimeout(20L, TimeUnit.SECONDS)
                .build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                                     //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                                     // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.testProxyHost, OkHttpClientUtils.testProxyPort)))
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper();
        this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }

    /**
     * 判断是否为彩铃运营中心方的渠道号
     * @param channelCode
     * @return
     */
    public static boolean isCentralityChannel(String channelCode){
        return org.apache.commons.lang3.StringUtils.startsWith(channelCode,CENTRALITY_CHANNEL_CODE_PREFFIX);
    }

    /**
     * 判断是否为产品id
     * @param copyrightOrProductId
     * @return
     */
    public static boolean isProductId(String copyrightOrProductId){
        return org.apache.commons.lang3.StringUtils.startsWith(copyrightOrProductId,VRBT_PRODUCT_ID_PREFFIX)&&copyrightOrProductId.length()==18;
    }

    /**
     * 登陆接口
     *
     * @param msisdn
     * @return
     */
    public @Nonnull RemoteResult miguLogin(String msisdn, String channelCode) {

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕服务端登录=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("渠道号未配置:"+channelCode);
        }
        String miguTokenKey = MIGU_TOKEN_KEY_PREFIX+ msisdn +"_"+channelCode;
        RemoteResult cachedToken = (RemoteResult) redisUtil.get(miguTokenKey);
        if(cachedToken!=null){
            return cachedToken;
        }

        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        //登陆类型:
        //1:获取URL地址登陆
        //2:集团token登陆
        //3:秘钥登陆
        //注:登陆类型需向移动运营人员索要
        dataNode.put("loginType", "3");
        //dataNode.put("callBackUrl", "https://crbt.cdyrjygs.com/vrbt_v9/");
        //dataNode.put("miguToken", "");
        dataNode.put("key", cmsCrackConfig.getLoginSecretKey());
        dataNode.put("msisdn", msisdn);
        //dataNode.put("autoLogin", "0");
        String data = dataNode.toString();
        String miguLoginUrl = isCentralityChannel(channelCode) ? bizProperties.getMiguLoginUrlCentrality() : bizProperties.getMiguLoginUrl();
        if(xunfeiVrbtProperties.getChannelChargeIdMap().containsKey(channelCode)){
            if(miguLoginUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                miguLoginUrl=miguLoginUrl.replace("https://crbt.kunpengtn.com/order/rest", "https://crbt.cdyrjygs.com/order/rest");
            }else{
                miguLoginUrl=miguLoginUrl.replace("http://hz.migu.cn/order/rest", "https://crbt.cdyrjygs.com/order/rest");
            }
        }

        final String url = bizProperties.genForwardDomainUrl(miguLoginUrl, cmsCrackConfig);
        log.info("咪咕服务端登录请求=>手机号:{},渠道号:{},请求url:{},参数data:{}",msisdn,channelCode,url,data);

        HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕服务端登录响应=>手机号:{},渠道号:{},响应:{}",msisdn,channelCode,content);

            RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.LoginView.class).readValue(
                    content);
            if(result.isOK()){
                redisUtil.set(miguTokenKey,result, MIGU_TOKEN_CATCHE_TTL_SECONDS);
            }

            return result;

        } catch (IOException e) {
            log.warn("咪咕服务端登录=>手机号:{},渠道号:{},异常!",msisdn,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 可以根据手机号或者token来取得token,如果是token就直接返回,如果是手机号就登录获取token
     */
    public String fetchToken(String mobileOrToken,String channelCode){
        if(StringUtils.isEmpty(mobileOrToken)){
            return null;
        }
        //如果是手机号就登录获取token
        if(isMobile(mobileOrToken)){
            final RemoteResult remoteResult = this.miguLogin(mobileOrToken, channelCode);
            if(remoteResult==null||!remoteResult.isOK()){
                return null;
            }
            return remoteResult.getToken();
        }
        //否则应该本来就是toekn,直接返回
        return mobileOrToken;
    }

    private boolean isMobile(String mobileOrToken) {
        return mobileOrToken.matches(BizConstant.MOBILE_REG);
    }

    /**
     * 咪咕统一登录
     *
     * @param utoken 集团统一认证token
     * @return
     */
    public @Nonnull RemoteResult miguULogin(String utoken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕服务端登录=>统一认证token:{},渠道号:{},未配置的渠道号!",utoken,channelCode);
            return RemoteResult.fail("渠道号未配置:"+channelCode);
        }
        String miguTokenKey = MIGU_TOKEN_KEY_PREFIX+ utoken +"_"+channelCode;
        RemoteResult cachedToken = (RemoteResult) redisUtil.get(miguTokenKey);
        if(cachedToken!=null){
            return cachedToken;
        }

        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        //登陆类型:
        //1:获取URL地址登陆
        //2:集团token登陆
        //3:秘钥登陆
        //注:登陆类型需向移动运营人员索要
        //dataNode.put("loginType", "3");
        dataNode.put("loginType", "2");
        //dataNode.put("callBackUrl", "");
        //dataNode.put("miguToken", "");
        dataNode.put("miguToken", utoken );
        //dataNode.put("msisdn", msisdn);
        //dataNode.put("autoLogin", "0");
        String data = dataNode.toString();
        log.info("咪咕服务端登录请求=>统一认证token:{},渠道号:{},请求参数data:{}",utoken,channelCode,data);

        final String miguLoginUrl = isCentralityChannel(channelCode) ? bizProperties.getMiguLoginUrlCentrality() : bizProperties.getMiguLoginUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(channelCode, cmsCrackConfig)).newBuilder().addQueryParameter("data",
                data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕服务端登录响应=>统一认证token:{},渠道号:{},响应:{}",utoken,channelCode,content);

            RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.LoginView.class).readValue(
                    content);
            if(result.isOK()){
                redisUtil.set(miguTokenKey,result, MIGU_TOKEN_CATCHE_TTL_SECONDS);
            }

            return result;

        } catch (IOException e) {
            log.warn("咪咕服务端登录=>utoken:{},渠道号:{},异常!",utoken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 开通视频彩铃功能
     *
     * @param mobileOrToken
     * @return
     */
    // {"resCode":"301003","resMsg":"用户已经开通被叫个人视频彩铃功能"}
    public @Nonnull RemoteResult vrbtOpen(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕开通视频彩铃功能=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        //dataNode.put("autoLogin", "0");
        String data = dataNode.toString();
        log.info("咪咕开通视频彩铃功能请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtOpenUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtOpenUrlCentrality() : bizProperties.getVrbtOpenUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtOpenUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕开通视频彩铃功能响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕开通视频彩铃功能=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 退订视频彩铃功能
     *
     * @param mobileOrToken
     * @return
     */
    //{"resCode":"301002","resMsg":"用户未开通或已取消被叫个人视频彩铃功能"}
    public @Nonnull RemoteResult vrbtCancel(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕退订视频彩铃功能=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        //dataNode.put("autoLogin", "0");
        String data = dataNode.toString();
        log.info("咪咕退订视频彩铃功能请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtCancelUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtCancelUrlCentrality() : bizProperties.getVrbtCancelUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtCancelUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕退订视频彩铃功能响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕退订视频彩铃功能=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 查询视频彩铃包月状态
     * RemoteResult.status
     *  0:未订购视频彩铃包月或渠道视频彩铃包；
     *  1:已订购视频彩铃订阅包；
     *  2:已订购渠道视频彩铃包；
     *  3:已订购视频彩铃订阅包和渠道视频彩铃包
     *
     *  彩铃运营中心需要传入serviceId,返回status:0:未订购 1:已订购
     *
     *  note:彩铃中心三方支付包月状态也使用这个接口
     *
     * @param mobileOrToken
     * @param isLog
     * @return
     */
    public @Nonnull RemoteResult vrbtMonthStatusQuery(String mobileOrToken, String channelCode, boolean isLog) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视频彩铃包月状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        // 彩铃运营中心需要传入serviceId
        if(isCentralityChannel(channelCode)){
            dataNode.put("serviceId", cmsCrackConfig.getServiceId());
        }
        String data = dataNode.toString();
        if(isLog) {
            log.info("咪咕查询视频彩铃包月状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}", mobileOrToken, channelCode, data);
        }
        final String vrbtMonthStatusQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtMonthStatusQueryUrlCentrality() : bizProperties.getVrbtMonthStatusQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtMonthStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            if(isLog) {
                log.info("咪咕查询视频彩铃包月状态响应=>mobileOrToken:{},渠道号:{},响应:{}", mobileOrToken, channelCode, content);
            }
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃包月状态=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 视频彩铃包月状态校验
     *
     * @return
     */
    public Integer verifyMiguVrbtMonthStatus(String mobile, String channelCode,boolean isLog) {
        try {
            final RemoteResult remoteResult = this.vrbtMonthStatusQuery(mobile, channelCode, isLog);
            if(remoteResult!=null&&remoteResult.isOK()){
                return Integer.parseInt(remoteResult.getStatus());
            }
            return -1;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * 查询视频彩铃业务策略(暂只支持彩铃中心,查询是否支持免费订购视频彩铃内容,有无免费策略)
     *
     * @param mobileOrToken
     * @param channelCode
     * //@param contentId  内容id[即视频彩铃产品id],(彩铃运营中心可以使用版权id或者产品id,而不需要下面的copyRightID参数)
     * @param copyRightID 版权id (开放平台需要同时传contentId和版权id)
     * @return
     */
    public @Nonnull RemoteResult queryVrbtBusinessPolicy(String mobileOrToken, String channelCode, String copyRightID) {
        //只允许设置我们自己cp的视频彩铃  渠道全部699052、订阅佐悦638799
        if(!BizConstant.isLegalVrbtCopyrightId(copyRightID)){
            return RemoteResult.fail("视频彩铃版权ID错误");
        }
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视频彩铃业务策略=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();

        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        //dataNode.put("serviceId", serviceId);
        if(isCentralityChannel(channelCode)){
            dataNode.put("vrbtId", copyRightID);
        }else {
            dataNode.put("vrbtId", copyRightID);
            // 开放平台要求传入这个参数,但是内容id传固定的也可以订购成功
            //dataNode.put("contentId", "600926000001000000");
            String contentId = musicService.getContentIdByCopyrightId(copyRightID);
            dataNode.put("contentId", StringUtils.isEmpty(contentId) ? "600926000001000000" : contentId);
        }
        String data = dataNode.toString();
        log.info("咪咕查询视频彩铃业务策略请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String queryVrbtBusinessPolicyUrl = isCentralityChannel(channelCode) ? bizProperties.getQueryVrbtBusinessPolicyUrlCentrality() : bizProperties.getQueryVrbtBusinessPolicyUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(queryVrbtBusinessPolicyUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            //if (!response.isSuccessful()) {
            //    throw new IOException("Unexpected code " + response);
            //}

            String content  = response.body().string();
            log.info("咪咕查询视频彩铃业务策略响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.ToneFreeMonthOrderView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃业务策略=>手机号:{},渠道号:{},copyRightID:{},异常!",mobileOrToken,channelCode,copyRightID,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 视频彩铃包月内内容免费订购
     *
     * @param mobileOrToken
     * @param channelCode
     * //@param contentId  内容id[即视频彩铃产品id],(彩铃运营中心可以使用版权id或者产品id,而不需要下面的copyRightID参数)
     * @param copyRightID 版权id (开放平台需要同时传contentId和版权id)
     * @param setFlag 0不设置默认铃音 1设置默认铃音(这是老的设置参数,新参数如下)
     *           订购接口的setflag增加了枚举值，定义如下：
     *          0----内容订购后不设置为在播；------即他人看和自己看两种都没有选择
     *          1----同时设置主叫和被叫在播；------即他人看和自己看都选择了
     *          3----只设置为被叫在播； -----即用户只选择了他人看
     *          9----只设置为主叫在播。 -----即用户只选择了自己看
     * @return
     */
    public @Nonnull RemoteResult vrbtToneFreeMonthOrder(String mobileOrToken, String channelCode, String copyRightID,String setFlag) {
        //只允许设置我们自己cp的视频彩铃  渠道全部699052、订阅佐悦638799
        //if(!BizConstant.isLegalVrbtCopyrightId(copyRightID) && !isXunfeiChannelCode(channelCode)){
        //    log.info("咪咕视频彩铃包月内内容免费订购=>mobileOrToken:{},渠道号:{},copyRightID:{},视频彩铃版权ID错误!",mobileOrToken,channelCode,copyRightID);
        //    return RemoteResult.fail("视频彩铃版权ID错误");
        //}
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕视频彩铃包月内内容免费订购=>mobileOrToken:{},渠道号:{},copyRightID:{},未配置的渠道号!",mobileOrToken,channelCode,copyRightID);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        if(BizConstant.BIZ_TYPE_SCH.equals(cmsCrackConfig.getType())){
            final SchSpecialProductConfig schSpecialProductConfig = bizProperties.getSchChannelSpecialProductConfigMap().get(channelCode);
            serviceId = null != schSpecialProductConfig ? schSpecialProductConfig.getServiceId() : serviceId;
        }

        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            log.info("咪咕视频彩铃包月内内容免费订购=>mobileOrToken:{},渠道号:{},copyRightID:{},咪咕登录失败!",mobileOrToken,channelCode,copyRightID);
            return RemoteResult.fail("咪咕登录失败!");
        }
        //if(org.apache.commons.lang3.StringUtils.containsAny(setFlag,VRBT_TONE_ORDER_SETFLAG_ZHUJIAO,VRBT_TONE_ORDER_SETFLAG_BOTH_ZBJ)){
        //
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        dataNode.put("serviceId", serviceId);
        //dataNode.put("serviceId", "698039049105618391");
        if(isCentralityChannel(channelCode)){
            dataNode.put("name", "miguOrderFreeVrbt");
            dataNode.put("contentId", copyRightID);
        }else {
            dataNode.put("copyRightID", copyRightID);
            String contentId = musicService.getContentIdByCopyrightId(copyRightID);
            //if(StringUtils.isEmpty(contentId)){
            //    contentId = orderVrbtService.getContentIdByCopyrightId(copyRightID);
            //}
            //如果找不到就去查询视频彩铃产品
            if(StringUtils.isEmpty(contentId)){
                contentId = SpringContextUtils.getBean(MiguApiService.class).fetchVrbtProduct(copyRightID).getVrbtProductId();
            }
            //开放平台要求传入这个参数,但是内容id传固定的也可以订购成功
            dataNode.put("contentId", StringUtils.isEmpty(contentId) ? "600926000001000000" : contentId);
        }
        dataNode.put("setFlag", setFlag);
        String data = dataNode.toString();
        log.info("咪咕视频彩铃包月内内容免费订购请求=>mobileOrToken:{},渠道号:{},copyRightID:{},请求参数data:{}",mobileOrToken,channelCode,copyRightID,data);

        String vrbtToneFreeMonthOrderUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtToneFreeMonthOrderUrlCentrality() : bizProperties.getVrbtToneFreeMonthOrderUrl();
        if(xunfeiVrbtProperties.getChannelChargeIdMap().containsKey(channelCode)){
            if(vrbtToneFreeMonthOrderUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                vrbtToneFreeMonthOrderUrl=vrbtToneFreeMonthOrderUrl.replace("https://crbt.kunpengtn.com/order/rest", "https://crbt.cdyrjygs.com/order/rest");
            }else{
                vrbtToneFreeMonthOrderUrl=vrbtToneFreeMonthOrderUrl.replace("http://hz.migu.cn/order/rest", "https://crbt.cdyrjygs.com/order/rest");
            }
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtToneFreeMonthOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            //if (!response.isSuccessful()) {
            //    throw new IOException("Unexpected code " + response);
            //}

            String content  = response.body().string();
            log.info("咪咕视频彩铃包月内内容免费订购响应=>mobileOrToken:{},渠道号:{},copyRightID:{},响应:{}",mobileOrToken,channelCode,copyRightID,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.ToneFreeMonthOrderView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕视频彩铃包月内内容免费订购=>手机号:{},渠道号:{},copyRightID:{},异常!",mobileOrToken,channelCode,copyRightID,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }


    //---------------------------------------以下为新增的服务端安全接口---------------------------------------------
    /**
     * 视频彩铃功能状态查询接口(被叫)
     * RemoteResult.status
     *  当返回结果为000000时，该字段不可空。
     *  1:未开通视频彩铃功能。
     *  2：已开通视频彩铃功能，但视频彩铃播放功能处于关闭状态。
     *  3：已开通视频彩铃功能，且视频彩铃播放功能处于开启状态。
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult vrbtStatusQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视频彩铃功能状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        // 彩铃运营中心此处必须带上token,开放那边同时支持手机号或者token
        if(isCentralityChannel(channelCode)){
            final String token = this.fetchToken(mobileOrToken, channelCode);
            if(StringUtils.isEmpty(token)){
                return RemoteResult.fail("咪咕登录失败!");
            }
            dataNode.put("token", token);
        }else {
            dataNode.put(this.isMobile(mobileOrToken)?"msisdn":"token", mobileOrToken);
        }
        String data = dataNode.toString();
        log.info("咪咕查询视频彩铃功能状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtStatusQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtStatusQueryUrlCentrality() : bizProperties.getVrbtStatusQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询视频彩铃功能状态响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃功能状态=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 视频彩铃VoLTE功能状态查询接口
     * RemoteResult.status
     *  当返回结果为000000时，该字段不可空。
     *  1:未开通VoLTE功能;
     *  2:已开通VoLTE功能。
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult vrbtVoLTEStatusQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视频彩铃VoLTE功能状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put(this.isMobile(mobileOrToken) ? "msisdn" : "token", mobileOrToken);

        String data = dataNode.toString();
        log.info("咪咕查询视频彩铃VoLTE功能状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtVoLTEStatusQueryUrl = bizProperties.getVrbtVoLTEStatusQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtVoLTEStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询视频彩铃VoLTE功能状态响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃VoLTE功能状态=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 个人铃音库视频彩铃内容查询接口
     * startRecordNum	false	String	开始记录标识，取值范围：【1－999999999999999】，默认为1
     * endRecordNum	false	String	结束记录标识，取值范围：【1－999999999999999】，假如此项为空，系统将最多一次返回50条记录，
     * 建议开始记录标识和结束记录标识间隔不超过50条记录。
     * queryType	false	String	返回结果类型：默认为2
     * 1 返回查询结果总数
     * 2 返回查询结果集
     * toneType	false	String	铃音类型：默认为3
     * 1 单首铃音
     * 2 铃音盒
     * 3 所有
     *
     * @param mobileOrToken
     * @return
     */
    public String vrbtToneQuery(String mobileOrToken, String channelCode,String startRecordNum,String endRecordNum,String queryType,String toneType) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询个人铃音库视频彩铃内容=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            throw new JeecgBootException("未配置的渠道号");
        }
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.failJson("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        if(!StringUtils.isEmpty(startRecordNum)){
            dataNode.put("startRecordNum", startRecordNum);
        }
        if(!StringUtils.isEmpty(endRecordNum)){
            dataNode.put("endRecordNum", endRecordNum);
        }
        if(!StringUtils.isEmpty(queryType)){
            dataNode.put("queryType", queryType);
        }
        if(!StringUtils.isEmpty(toneType)){
            dataNode.put("toneType", toneType);
        }

        String data = dataNode.toString();
        log.info("咪咕查询个人铃音库视频彩铃内容请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtToneQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtToneQueryUrlCentrality() : bizProperties.getVrbtToneQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtToneQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询个人铃音库视频彩铃内容响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

            return content;

        } catch (IOException e) {
            log.warn("咪咕查询个人铃音库视频彩铃内容=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            throw new JeecgBootException("咪咕接口通信异常");
        }
    }

    /**
     * 视频彩铃内容删除接口 (彩铃中心使用本接口作为包月业务退订接口,需要传入的toneID为业务serviceId)
     * @param mobileOrToken
     * @param toneID 资源编码：铃音编码或者铃音盒编码
     * @return
     *         咪咕视频彩铃内容删除响应=>mobileOrToken:13438828200,渠道号:014X04C,响应:{"resCode":"000000","resMsg":"成功"}
     *         咪咕视频彩铃内容删除响应=>mobileOrToken:13438828200,渠道号:014X04F,响应:{"resCode":"302002","resMsg":"该铃音或铃音盒不存在"}
     */
    public @Nonnull RemoteResult vrbtToneDelete(String mobileOrToken, String channelCode, String toneID) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕视频彩铃内容删除=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        String vrbtProductId = transToneId(toneID);
        dataNode.put("toneID", vrbtProductId);
        String data = dataNode.toString();
        log.info("咪咕视频彩铃内容删除请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtToneDeleteUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtToneDeleteUrlCentrality() : bizProperties.getVrbtToneDeleteUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtToneDeleteUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕视频彩铃内容删除响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕视频彩铃内容删除=>手机号:{},渠道号:{},toneID:{},异常!",mobileOrToken,channelCode,toneID,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 铃音库清理
     * @param mobile
     * @param channelCode
     */
    public void clearUserTone(String mobile,String channelCode) {
        String vrbtToneQueryResult = vrbtToneQuery(mobile, channelCode,null,null,null,null);
        try {
            mapper.readTree(vrbtToneQueryResult).at("/toneInfos").spliterator().forEachRemaining(jsonNode -> {
                final String toneID = jsonNode.get("toneID").asText();
                //排除订阅库serviceId
                if (!StringUtils.equalsAny(toneID, "698039035100000014", "698039042105792434", "698039035103445177", "698039035100000043", "698039035100000057", "698039035100000071", "698039035100000140")) {
                    vrbtToneDelete(mobile, channelCode, toneID);
                }
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将版权id转换为视频彩铃产品id(如果已经是18位的产品id或者是彩铃中心的serviceId就直接返回)
     * @param toneID 版权id或者视频彩铃产品id或者彩铃中心的serviceId
     * @return
     */
    private String transToneId(String toneID){
        if(org.apache.commons.lang3.StringUtils.length(toneID)==18){
            return toneID;
        }
        return musicService.getContentIdByCopyrightId(toneID);
    }

    /**
     * 获取视频彩铃试看地址Json
     * @param mobileOrToken  开放的可以不用传手机号或者token,彩铃中心这边必须传
     * @param vrbtId  18位内容ID,11位版权ID,12位版权ID
     * @return
     * {"resCode":"999002","resMsg":"【OPEN】您请求的音源不存在"}
     */
    @Cacheable(cacheNames = CacheConstant.CMS_MIGU_API_CACHE,key = "#channelCode + '-' + #vrbtId",unless = "#result==null")
    public String vrbtTryToSee(String mobileOrToken, String channelCode,String vrbtId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕获取视频彩铃试看地址=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            throw new JeecgBootException("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        if(isCentralityChannel(channelCode)){
            if(StringUtils.isEmpty(mobileOrToken)){
                mobileOrToken = MOBILE_FOR_TEST;
            }
            final String token = this.fetchToken(mobileOrToken, channelCode);
            if(StringUtils.isEmpty(token)){
                return RemoteResult.failJson("咪咕登录失败!");
            }
            dataNode.put("token", token);
        }else {
            if(!StringUtils.isEmpty(mobileOrToken)){
                dataNode.put(this.isMobile(mobileOrToken) ? "msisdn" : "token", mobileOrToken);
            }
        }
        dataNode.put("vrbtId", vrbtId);

        String data = dataNode.toString();
        log.info("咪咕获取视频彩铃试看地址请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtTryToSeeUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtTryToSeeUrlCentrality() : bizProperties.getVrbtTryToSeeUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtTryToSeeUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕获取视频彩铃试看地址响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

            return content;

        } catch (IOException e) {
            log.warn("咪咕获取视频彩铃试看地址=>手机号:{},渠道号:{},vrbtId:{},异常!",mobileOrToken,channelCode,vrbtId,e);
            throw new JeecgBootException("咪咕接口通信异常");
        }
    }

    /**
     * 获取视频彩铃试看地址
     * @param mobileOrToken  开放的可以不用传手机号或者token,彩铃中心这边必须传
     * @param vrbtId  18位内容ID,11位版权ID,12位版权ID
     * @return
     */
    public String vrbtTryToSeeUrl(String mobileOrToken, String channelCode, String vrbtId) {
        try {
            //使用缓存
            String jsonResult =  SpringContextUtils.getBean(MiguApiService.class).vrbtTryToSee(mobileOrToken, channelCode, vrbtId);
            JsonNode tree = mapper.readTree(jsonResult);
            //data/0/status
            if(!RemoteResult.CODE_OK.equals(tree.at("/resCode").asText())){
              return null;
            }
            Iterator<JsonNode> vrbtFiles = tree.at("/vrbtFiles").elements();
            //return StreamSupport.stream(Spliterators.spliteratorUnknownSize(vrbtFiles, Spliterator.ORDERED), false)
            return Streams.stream(vrbtFiles)
                    .sorted(Comparator.comparing(jsonNode -> jsonNode.get("type").asText()))
                    .findFirst()
                    .map(jsonNode -> jsonNode.get("filePath").asText())
                    .orElse(null);

        } catch (Exception e) {
            log.warn("获取视频彩铃试看地址=>手机号:{},渠道号:{},vrbtId:{},异常!",mobileOrToken,channelCode,vrbtId,e);
            return null;
        }
    }

    /**
     * 查询视频彩铃信息
     * 注意:遗憾的是彩铃中心没有这个查询产品的接口,彩铃中心订阅库的版权id也支持用渠道的渠道号"00210OW 或 00210OC来查询
     * @param mobileOrToken
     * @param vrbtId  18位内容ID,11位版权ID,12位版权ID
     * @return
     */
    public String vrbtProductQuery(String mobileOrToken, String channelCode,String vrbtId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕视频彩铃信息=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            throw new JeecgBootException("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put(this.isMobile(mobileOrToken) ? "msisdn" : "token", mobileOrToken);
        dataNode.put("vrbtId", vrbtId);

        String data = dataNode.toString();
        log.info("咪咕视频彩铃信息请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtProductQueryUrl = bizProperties.getVrbtProductQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtProductQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕视频彩铃信息响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

            return content;

        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃信息=>手机号:{},渠道号:{},vrbtId:{},异常!",mobileOrToken,channelCode,vrbtId,e);
            throw new JeecgBootException("咪咕接口通信异常");
        }
    }

    /**
     *  copyrightId 18位内容ID,11位版权ID,12位版权ID
     * 使用咪咕服务端安全接口来抓取图片地址  查找分别率为9:16或者接近这个分辨率的图片
     * @param vrbtId
     * @return
     */
    public String fetchImg(String vrbtId){

        final String vrbtProductJson = vrbtProductQuery(MiguApiService.MOBILE_FOR_TEST, MiguApiService.CH_QDB, vrbtId);

        try {
            final JsonNode arrayNode = mapper.readTree(vrbtProductJson).at("/vrbtProduct/fileInfos");
            String candidateImgUrl = null;
            if (arrayNode.isArray()) {
                for (JsonNode jsonNode : arrayNode) {
                    String type = jsonNode.get("type").asText();
                    //先找110：JPG(720x1280)
                    if("110".equals(type)){
                        return jsonNode.get("filePath").asText();
                    }
                    //如果没有找到符合条件的就找109：JPG(522x927)
                    if("109".equals(type)){
                        candidateImgUrl = jsonNode.get("filePath").asText();
                    }
                }
            }
            return candidateImgUrl;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *  copyrightId 18位内容ID,11位版权ID,12位版权ID
     * 使用咪咕服务端安全接口来抓取图片地址  查找分别率为9:16或者接近这个分辨率的图片
     * @param vrbtId
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.CMS_MIGU_API_CACHE,key = "'VrbtProduct-' + #vrbtId",unless = "#result.status==0")
    public @Nonnull VrbtProduct fetchVrbtProduct(String vrbtId){
        final String vrbtProductJson = vrbtProductQuery(MiguApiService.MOBILE_FOR_TEST, MiguApiService.CH_QDB, vrbtId);
        VrbtProduct vp = new VrbtProduct();
        try {
            final JsonNode tree = mapper.readTree(vrbtProductJson);
            if(!RemoteResult.CODE_OK.equals(tree.get("resCode").asText())){
                vp.setStatus(0);
                return vp;
            }
            final JsonNode vrbtProduct = tree.get("vrbtProduct");
            vp.setMusicName(vrbtProduct.get("toneName").asText());
            vp.setCopyrightId(vrbtProduct.get("musicId").asText());
            vp.setVrbtProductId(vrbtProduct.get("contentId").asText());
            vp.setExpiryDate(vrbtProduct.get("toneValidDay").asText());
            final JsonNode arrayNode = tree.at("/vrbtProduct/fileInfos");
            final Map<String, String> typeFilePathMap = StreamSupport.stream(arrayNode.spliterator(), false)
                    .collect(Collectors.toMap(
                            jsonNode -> jsonNode.at("/type").asText(),
                            jsonNode -> jsonNode.at("/filePath").asText(),
                            (value1, value2) -> Stream.of(value1, value2).filter(filePath -> StringUtils.contains(filePath,".mp4")).findAny().orElse(value1)));
            //找预览图地址 先找110：JPG(720x1280),如果没有找到符合条件的就找109：JPG(522x927)
            String imgUrl = typeFilePathMap.getOrDefault("110", typeFilePathMap.get("109"));
            // //找视频播放地址 先找3 mp4(720x1280),如果没有找到符合条件的就找109：JPG(522x927)
            String videoUrl = typeFilePathMap.getOrDefault("3", typeFilePathMap.get("4"));
            vp.setVrbtImg(imgUrl);
            vp.setVrbtVideo(videoUrl);
            vp.setStatus(1);

        } catch (Exception e) {
            log.info("解析视频彩铃产品信息异常-vrbtId:{}",vrbtId,e);
        }

        return vp;
    }

    /**
     * 视频彩铃功能状态和视频彩铃包月状态二合一查询接口
     *   {"resCode":"000000","resMsg":"成功"}
     *   {"resCode":"999047","resMsg":"【OPEN】不存在有效订购关系"}
     * @param mobileOrToken
     * @return
     */
    public @Nonnull VrbtCombinResult vrbtFunAndMonthStatusQuery(String mobileOrToken, String channelCode) {

        final VrbtCombinResult failResult = VrbtCombinResult.fail("查询视频彩铃功能和包月状态失败");

        //先登录,不然两个线程都会同时登录,怕后期会导致一个token失效
        if(this.isMobile(mobileOrToken)){
            RemoteResult loginResult = this.miguLogin(mobileOrToken, channelCode);
            if(!loginResult.isOK()){
                return failResult;
            }
            mobileOrToken = loginResult.getToken();
        }

        final String token = mobileOrToken;
        VrbtFunAndMonthStatus combinStatus = new VrbtFunAndMonthStatus();
        final CompletableFuture<Void> funStatusFuture = CompletableFuture.runAsync(() -> {
            final RemoteResult remoteResult = this.vrbtStatusQuery(token, channelCode);
            combinStatus.setVrbtFunStatus(remoteResult);
        }, executor);
        final CompletableFuture<Void> monthStatusFuture = CompletableFuture.runAsync(()->{
            final RemoteResult remoteResult = this.vrbtMonthStatusQuery(token, channelCode, true);
            combinStatus.setVrbtMonthStatus(remoteResult);
        },executor);

        try {
            CompletableFuture.allOf(funStatusFuture,monthStatusFuture).get();
            RemoteResult funStatusResult = combinStatus.getVrbtFunStatus();
            RemoteResult monthStatusResult = combinStatus.getVrbtMonthStatus();
            if(!funStatusResult.isOK()||!monthStatusResult.isOK()){
                return failResult;
            }
            //return VrbtCombinResult.success(funStatusResult.getStatus(), monthStatusResult.getStatus());
            // funStatus 1:未开通视频彩铃功能。2：已开通视频彩铃功能，但视频彩铃播放功能处于关闭状态。3：已开通视频彩铃功能，且视频彩铃播放功能处于开启状态。
            String funStatus = funStatusResult.getStatus();
            //monthStatus 0:未订购视频彩铃包月或渠道视频彩铃包；1:已订购视频彩铃订阅包；2:已订购渠道视频彩铃包；3:已订购视频彩铃订阅包和渠道视频彩铃包。
            //[彩铃运营中心]monthStatus 0:未订购 1:已订购
            String monthStatus = monthStatusResult.getStatus();
            String channelCodeNew;
            // 彩铃运营中心无需判断渠道号
            if(isCentralityChannel(channelCode)){
                channelCodeNew = channelCode;
            }else {
                //如果已有包月,根据包月确定渠道号并登录
                if(!VRBT_MONTH_STATUS_NONE.equals(monthStatus)) {
                    //已有视频包月,直接免费订购(除开订阅包以外,都使用渠道包serviceId来免费订购视频彩铃)
                    channelCodeNew =  VRBT_MONTH_STATUS_DYB.equals(monthStatus) ? bizProperties.getChannelDingyue(channelCode) :  bizProperties.getChannelQudao(channelCode);
                }else {
                    //未包月,根据视频彩铃功能状态确定渠道号并登录
                    channelCodeNew =  VRBT_FUN_STATUS_NONE.equals(funStatus) ? bizProperties.getChannelDingyue(channelCode) :  bizProperties.getChannelQudao(channelCode);
                }
            }
            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCodeNew).getServiceId();
            return VrbtCombinResult.success(funStatus, monthStatus, channelCodeNew, serviceId,null);

        } catch (Exception e) {
            e.printStackTrace();
            return failResult;
        }
    }

    /**
     * 视频彩铃功能状态和视频彩铃包月状态,并根据功能状态及包月状态确定渠道号并登录
     *  电信和联通查询视频彩铃功能会返回{"resCode":"100002","resMsg":"[PE]获取省DID为返回为空"}
     * @param mobile
     * @return
     */
    public @Nonnull VrbtCombinResult vrbtCombinQuery(String mobile, String channelCode) {

        final VrbtCombinResult failResult = VrbtCombinResult.fail("视频彩铃功能及包月状态复合查询失败");
        try {
            VrbtCombinResult result = this.vrbtFunAndMonthStatusQuery(mobile, channelCode);
            if(!result.isOK()){
                return result;
            }

            //渠道侧可能会因为有无视频彩铃变更渠道号,此处重新登录一下(已登录的会从缓存取回)
            RemoteResult loginResult = this.miguLogin(mobile, result.getChannelCode());
            if(!loginResult.isOK()){
                return failResult;
            }
            result.setToken(loginResult.getToken());

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return failResult;
        }
    }

    /**
     * 1.1.1.1.1.	渠道视频彩铃包月包退订,该接口允许退订视频彩铃订阅包和渠道视频彩铃包
     *
     * @param mobileOrToken
     * @return
     */

    @UnsubscribeLimit(count = 100,period = 3600)
    public @Nonnull RemoteResult vrbtUnsubscribe(String mobileOrToken,String channelCode) {
        //彩铃中心退订接口使用铃音库内容删除接口
        if(isCentralityChannel(channelCode)){
            log.info("视频彩铃包月包退订=>彩铃中心包月退订,mobileOrToken:{},渠道号:{}",mobileOrToken,channelCode);
            RemoteResult result = vrbtToneDelete(mobileOrToken, channelCode, cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId());
            //{"resCode":"302002","resMsg":"该铃音或铃音盒不存在"}
            if("302002".equals(result.getResCode())){
                result.setResMsg("未订购视频彩铃订阅业务");
            }
            return result;
        }

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("视频彩铃包月包退订=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        dataNode.put("serviceId", serviceId);
        String data = dataNode.toString();
        log.info("视频彩铃包月包退订请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtUnsubscribeUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtUnsubscribeUrlCentrality() : bizProperties.getVrbtUnsubscribeUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtUnsubscribeUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("视频彩铃包月包退订响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);

        } catch (IOException e) {
            e.printStackTrace();
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 查询振铃包月状态  (此接口比较特殊,根据resCode判定是否有包月)
     * {"mobile":"134*****200","name":"手机铃声-4元包","efftTime":"20201217104335","validTime":"20991231235959","resCode":"000000","resMsg":"已订购"}
     * {"mobile":"182*****242","name":"手机铃声-4元包","resCode":"300002","resMsg":"未订购"}
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult rtMonthStatusQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询振铃包月状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        //String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        //dataNode.put("timestamp", timestamp);
        //String signature = signatureService.signature(channelCode, timestamp,cmsCrackConfig.getSignatureSecretKey());
        //dataNode.put("signature", signature);

        String data = dataNode.toString();
        log.info("咪咕查询振铃包月状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String rtMonthStatusQueryUrl = bizProperties.getRtMonthStatusQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(rtMonthStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询振铃包月状态响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);

        } catch (IOException e) {
            e.printStackTrace();
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 白金会员获取试听地址
     *
     * @param mobileOrToken
     * @param contentId 歌曲ID；
     * 11位的版权ID
     * 18位的全曲内容ID
     * @return  {"streamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","lcStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","pqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_128_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=15cad3a8-a72a-47d3-801d-797d3fa37aa4&k=f8e856689fc8203b&t=1615175297071","hqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_320_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=376c5577-1bba-4a42-8959-b125121c9a6e&k=6bcdfcc07a11d430&t=1615175297069","sqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/flac/6005759Z0N9.flac?channelid=08&msisdn=1781f55b-3941-47a3-b03c-eec97a834816&k=31ae4d2cae8b4b19&t=1615175297070","resCode":"000000","resMsg":"【OPEN】操作成功"}
     *  未开白金会员返回: {"resCode":"999001","resMsg":"【OPEN】接口权限未开通[包月未订购]"}
     */
    public @Nonnull String bjhyAudition(String mobileOrToken, String channelCode, String contentId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕白金会员获取试听地址=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.failJson("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.failJson("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("contentId", contentId);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);

        String data = dataNode.toString();
        log.info("咪咕白金会员获取试听地址请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhyAuditionUrl = bizProperties.getBjhyAuditionUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyAuditionUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕白金会员获取试听地址响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);
            return content;

        } catch (IOException e) {
            log.warn("咪咕白金会员获取试听地址=>手机号:{},渠道号:{},contentId:{},异常!",mobileOrToken,channelCode,contentId,e);
            return RemoteResult.failJson("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 白金会员获取下载地址
     *
     * @param mobileOrToken
     * @param contentId 歌曲ID；
     * 11位的版权ID
     * 18位的全曲内容ID
     * @return  {"streamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","lcStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","pqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_128_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=15cad3a8-a72a-47d3-801d-797d3fa37aa4&k=f8e856689fc8203b&t=1615175297071","hqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_320_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=376c5577-1bba-4a42-8959-b125121c9a6e&k=6bcdfcc07a11d430&t=1615175297069","sqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/flac/6005759Z0N9.flac?channelid=08&msisdn=1781f55b-3941-47a3-b03c-eec97a834816&k=31ae4d2cae8b4b19&t=1615175297070","resCode":"000000","resMsg":"【OPEN】操作成功"}
     * 未开白金会员返回: {"resCode":"999001","resMsg":"【OPEN】接口权限未开通[包月未订购]"}
     */
    public @Nonnull String bjhyDownlink(String mobileOrToken, String channelCode, String contentId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕白金会员获取下载地址=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.failJson("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.failJson("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("contentId", contentId);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);

        String data = dataNode.toString();
        log.info("咪咕白金会员获取下载地址请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhyDownlinkUrl = bizProperties.getBjhyDownlinkUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyDownlinkUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕白金会员获取下载地址响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);
            return content;

        } catch (IOException e) {
            log.warn("咪咕白金会员获取下载地址=>手机号:{},渠道号:{},contentId:{},异常!",mobileOrToken,channelCode,contentId,e);
            return RemoteResult.failJson("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 5元白金会员获取试听地址
     *
     * @param mobileOrToken
     * @param contentId 歌曲ID；
     * 11位的版权ID
     * 18位的全曲内容ID
     * @return  {"streamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","lcStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","pqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_128_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=15cad3a8-a72a-47d3-801d-797d3fa37aa4&k=f8e856689fc8203b&t=1615175297071","hqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_320_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=376c5577-1bba-4a42-8959-b125121c9a6e&k=6bcdfcc07a11d430&t=1615175297069","sqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/flac/6005759Z0N9.flac?channelid=08&msisdn=1781f55b-3941-47a3-b03c-eec97a834816&k=31ae4d2cae8b4b19&t=1615175297070","resCode":"000000","resMsg":"【OPEN】操作成功"}
     *  未开白金会员返回: {"resCode":"999001","resMsg":"【OPEN】接口权限未开通[包月未订购]"}
     */
    public @Nonnull String bjhy5Audition(String mobileOrToken, String channelCode, String contentId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕5元白金会员获取试听地址=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.failJson("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.failJson("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("contentId", contentId);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);

        String data = dataNode.toString();
        log.info("咪咕5元白金会员获取试听地址请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhy5AuditionUrl = bizProperties.getBjhy5AuditionUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhy5AuditionUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕5元白金会员获取试听地址响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);
            return content;

        } catch (IOException e) {
            log.warn("咪咕5元白金会员获取试听地址=>手机号:{},渠道号:{},contentId:{},异常!",mobileOrToken,channelCode,contentId,e);
            return RemoteResult.failJson("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 5元白金会员获取下载地址
     *
     * @param mobileOrToken
     * @param contentId 歌曲ID；
     * 11位的版权ID
     * 18位的全曲内容ID
     * @return  {"streamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","lcStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/MP3_40_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=c416300f-638e-42c9-8f9b-617d9d8f6fc0&k=26bd52085206585e&t=1615175297071","pqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_128_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=15cad3a8-a72a-47d3-801d-797d3fa37aa4&k=f8e856689fc8203b&t=1615175297071","hqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%A0%87%E6%B8%85%E9%AB%98%E6%B8%85/MP3_320_16_Stero/6005759Z0N9.mp3?channelid=08&msisdn=376c5577-1bba-4a42-8959-b125121c9a6e&k=6bcdfcc07a11d430&t=1615175297069","sqStreamUrl":"http://open.tyst.migu.cn/public/product23/2018/11/15/2018%E5%B9%B405%E6%9C%8804%E6%97%A518%E7%82%B900%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B36%E9%A6%96/%E6%AD%8C%E6%9B%B2%E4%B8%8B%E8%BD%BD/flac/6005759Z0N9.flac?channelid=08&msisdn=1781f55b-3941-47a3-b03c-eec97a834816&k=31ae4d2cae8b4b19&t=1615175297070","resCode":"000000","resMsg":"【OPEN】操作成功"}
     * 未开白金会员返回: {"resCode":"999001","resMsg":"【OPEN】接口权限未开通[包月未订购]"}
     */
    public @Nonnull String bjhy5Downlink(String mobileOrToken, String channelCode, String contentId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕5元白金会员获取下载地址=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.failJson("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.failJson("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("contentId", contentId);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);

        String data = dataNode.toString();
        log.info("咪咕5元白金会员获取下载地址请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhy5DownlinkUrl = bizProperties.getBjhy5DownlinkUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhy5DownlinkUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕5元白金会员获取下载地址响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);
            return content;

        } catch (IOException e) {
            log.warn("咪咕5元白金会员获取下载地址=>手机号:{},渠道号:{},contentId:{},异常!",mobileOrToken,channelCode,contentId,e);
            return RemoteResult.failJson("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 查询渠道专属包月状态
     * @param mobileOrToken
     * @param channelCode
     * @return
     *            {mobile: "135*****280", name: "趣享歌曲10元包（音乐全曲包）", resCode: "000000", resMsg: "已订购"}
     *            {mobile: "135*****280", name: "趣享歌曲10元包（音乐全曲包）", resCode: "300002", resMsg: "未订购"}
     */
    public @Nonnull RemoteResult cpmbQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询渠道专属包月状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        //String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        //dataNode.put("timestamp", timestamp);
        //String signature = signatureService.signature(channelCode, timestamp,cmsCrackConfig.getSignatureSecretKey());
        //dataNode.put("signature", signature);

        String data = dataNode.toString();
        log.info("咪咕查询渠道专属包月状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String cpmbQueryUrl = bizProperties.getCpmbQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(cpmbQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询渠道专属包月状态响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询渠道专属包月状态=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

//    /**
//     * 查询渠道专属包月状态
//     * @param mobileOrToken
//     * @return
//     *            {mobile: "135*****280", name: "趣享歌曲10元包（音乐全曲包）", resCode: "000000", resMsg: "已订购"}
//     *            {mobile: "135*****280", name: "趣享歌曲10元包（音乐全曲包）", resCode: "300002", resMsg: "未订购"}
//     */
//    public @Nonnull RemoteResult cpmbQuery(String mobileOrToken) {
//        return cpmbQuery(mobileOrToken, BIZ_CPMB_20_CHANNEL_CODE_1);
//    }

    /**
     * 渠道专属包月退订
     * @param mobileOrToken
     * @param channelCode
     * @return  不管有没有订购过,始终返回:  {"resCode":"000000","resMsg":"成功"}
     */
    @UnsubscribeLimit(count = 100,period = 3600)
    public @Nonnull RemoteResult cpmbCancel(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕渠道专属包月退订=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        dataNode.put("byb", "byb");
        dataNode.put("youCallbackName", "cancelCPMBMiguSdkCallback");

        String data = dataNode.toString();
        log.info("咪咕渠道专属包月退订请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String cpmbCancelUrl = bizProperties.getCpmbCancelUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(cpmbCancelUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕渠道专属包月退订响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕渠道专属包月退订=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 3.3.1.5 在线听歌接口-获取歌曲在线听地址
     * @param mobileOrToken  开放的可以不用传手机号或者token,彩铃中心这边必须传
     * @param contentId  18位内容ID,11位版权ID,12位版权ID
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.CMS_MIGU_API_CACHE,key = "'stream' + #channelCode + '-' + #contentId",unless = "#result==null")
    public String streamQuery(String mobileOrToken, String channelCode,String contentId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕获取歌曲在线听地址=>mobileOrToken:{},渠道号:{},未配置的渠道号!", mobileOrToken, channelCode);
            throw new JeecgBootException("未配置的渠道号");
        }

        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("contentId", contentId);
        if(!StringUtils.isEmpty(mobileOrToken)){
            dataNode.put(this.isMobile(mobileOrToken) ? "auditMsisdn" : "token", mobileOrToken);
        }

        String data = dataNode.toString();
        log.info("咪咕获取歌曲在线听地址请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String streamQueryUrl = bizProperties.getStreamQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(streamQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕获取歌曲在线听地址响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            //RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

            return content;

        } catch (IOException e) {
            log.warn("咪咕获取歌曲在线听地址=>手机号:{},渠道号:{},contentId:{},异常!",mobileOrToken,channelCode,contentId,e);
            throw new JeecgBootException("咪咕接口通信异常");
        }
    }

    /**
     * 获取开放平台的订阅包切换到渠道包的比例
     * @return
     */
    public int getChannelSwtichPercent() {
        int defaultVal = 1;
        final Object percent = redisUtil.get(CHANNEL_SWTICH_PERCENT_KEY);
        if (percent == null) {
            return defaultVal;
        }
        try {
            return (Integer) percent;
        } catch (Exception e) {
            try {
                return Integer.parseInt((String) percent);
            } catch (Exception ex) {
            }
        }

        return defaultVal;
    }
    /**
     * 设置开放平台的订阅包切换到渠道包的比例
     * @param percent
     */
    public void setChannelSwtichPercent(Integer percent) {
        redisUtil.set(CHANNEL_SWTICH_PERCENT_KEY, percent);
    }




    /**
     * 登陆接口
     *
     * @param miguToken
     * @return
     */
    public @Nonnull RemoteResult miguLoginByToken(String miguToken) {
        miguToken = "ABFfsfZtBTHPoc6fo+dUD8OKL2wSQLv7j57Q00FHhxo=";
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(CH_QDB);
        if (null == cmsCrackConfig) {
            log.info("咪咕token服务端登录=>miguToken:{},渠道号:{},未配置的渠道号!",miguToken,CH_QDB);
            return RemoteResult.fail("渠道号未配置:"+CH_QDB);
        }
        String miguTokenKey = MIGU_TOKEN_KEY_PREFIX + miguToken + "_" + CH_QDB;
        RemoteResult cachedToken = (RemoteResult) redisUtil.get(miguTokenKey);
        if(cachedToken!=null){
            return cachedToken;
        }

        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", CH_QDB);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(CH_QDB, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        //登陆类型:
        //1:获取URL地址登陆
        //2:集团token登陆
        //3:秘钥登陆
        //注:登陆类型需向移动运营人员索要
        dataNode.put("loginType", "2");
        //dataNode.put("callBackUrl", "");
        dataNode.put("miguToken", miguToken);
//        dataNode.put("key", cmsCrackConfig.getLoginSecretKey());
//        dataNode.put("msisdn", msisdn);
        //dataNode.put("autoLogin", "0");
        String data = dataNode.toString();
        log.info("咪咕token服务端登录请求=>miguToken:{},渠道号:{},请求参数data:{}",miguToken,CH_QDB,data);

        final String miguLoginUrl = isCentralityChannel(CH_QDB) ? bizProperties.getMiguLoginUrlCentrality() : bizProperties.getMiguLoginUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(miguLoginUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕token服务端登录响应=>miguToken:{},渠道号:{},响应:{}",miguToken,CH_QDB,content);

            RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.LoginView.class).readValue(
                    content);
            if(result.isOK()){
                redisUtil.set(miguTokenKey,result, MIGU_TOKEN_CATCHE_TTL_SECONDS);
            }

            return result;

        } catch (IOException e) {
            log.warn("咪咕token服务端登录=>miguToken:{},异常!",miguToken,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 查询白金会员包月状态
     *
     * @param mobileOrToken
     * @return   {"status":"1","resCode":"000000","resMsg":"成功"}
     *    status  0已订购 1未订购
     */
    public @Nonnull RemoteResult bjhyQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询白金会员状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        dataNode.put("youCallbackName", "");
        String data = dataNode.toString();
        log.info("咪咕查询白金会员状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhyQueryUrl = bizProperties.getBjhyQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕查询白金会员状态响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕查询白金会员状态=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }


    /**
     * 白金会员退订
     *
     * @param mobileOrToken
     * @return   {"resCode":"000000","resMsg":"成功"}
     */
    @UnsubscribeLimit(count = 100,period = 3600)
    public @Nonnull RemoteResult bjhyCancel(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕白金会员退订=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        dataNode.put("youCallbackName", "");
        String data = dataNode.toString();
        log.info("咪咕白金会员退订请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhyCancelUrl = bizProperties.getBjhyCancelUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyCancelUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕白金会员退订请求响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕白金会员退订=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 3.3.1.2.3.	白金会员三方支付固定时长(用于发放北岸唐唱的白金会员权益发放)
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult bjhyOrder(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕白金会员三方支付固定时长=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        //dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        dataNode.put("count", "1");
        dataNode.put("countType", "M");
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        String data = dataNode.toString();
        log.info("咪咕白金会员三方支付固定时长请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhyOrderUrl = bizProperties.getBjhyOrderUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕白金会员三方支付固定时长响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕白金会员三方支付固定时长=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 1.1.1.1.1.	三方支付连续包月订购(四川0元开通视频彩铃功能)
     *    新增使用渠道号 00211DG来开功能  留存使用渠道号:00211DJ来开功能
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult provinceBjhyOrder(String mobileOrToken, boolean isNewUser) {
        String channelCode =  isNewUser ? "00211DG" : "00211DJ";
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕三方支付连续包月订购=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        //dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        String data = dataNode.toString();
        log.info("咪咕三方支付连续包月订购请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhyOrderUrl = bizProperties.getProvinceBjhyOrderUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕三方支付连续包月订购响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕三方支付连续包月订购=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 1.1.1.1.1.	三方支付连续包月订购退订
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult provinceBjhyCancel(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕三方支付连续包月退订=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        //dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        String data = dataNode.toString();
        log.info("咪咕三方支付连续包月退订请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String bjhyOrderUrl = bizProperties.getProvinceBjhyCancelUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕三方支付连续包月退订响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕三方支付连续包月退订=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }




    /**
     * 三方支付: 彩铃包月-0元固定时长订购接口 (默认订购一个月)
     *   {"resCode":"000000","resMsg":"成功"}
     *   {"resCode":"999047","resMsg":"【OPEN】不存在有效订购关系"}
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult vrbtZeroOrder(String mobileOrToken, String channelCode, Integer nMonth) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕0元订购视频彩铃包月=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", cmsCrackConfig.getServiceId());
        dataNode.put("type", "M"); //订购类型类型: M：按月订购 D：按天订购
        dataNode.put("amount", String.valueOf(nMonth)); //订购时长
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        // 彩铃运营中心此处必须带上token,开放那边同时支持手机号或者token
        if(isCentralityChannel(channelCode)){
            final String token = this.fetchToken(mobileOrToken, channelCode);
            if(StringUtils.isEmpty(token)){
                return RemoteResult.fail("咪咕登录失败!");
            }
            dataNode.put("token", token);
        }else {
            dataNode.put(this.isMobile(mobileOrToken)?"msisdn":"token", mobileOrToken);
        }
        String data = dataNode.toString();
        log.info("咪咕0元订购视频彩铃包月请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);
        final String vrbtZeroOrderUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtZeroOrderUrlCentrality() : bizProperties.getVrbtZeroOrderUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtZeroOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕0元订购视频彩铃包月响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕0元订购视频彩铃包月=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }

    }

    /**
     * 解密咪咕AES加密手机号
     * 解密合作支撑平台合约订购用户在咪咕app登录信息同步的AES加密手机号
     * @param encryptedMobile
     * @param channelCode
     * @return
     */
    public String decryptMobile(String encryptedMobile, String channelCode){
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("解密手机号=>encryptedMobile:{},渠道号:{},未配置的渠道号!",encryptedMobile,channelCode);
            return null;
        }
        String aesKey = cmsCrackConfig.getSignatureSecretKey();

        try {
            return new AES(Mode.ECB, Padding.PKCS5Padding, aesKey.getBytes(StandardCharsets.UTF_8)).decryptStr(Base64.decodeBase64(encryptedMobile));
        } catch (Exception e) {
            log.info("解密手机号=>encryptedMobile:{},渠道号:{},异常!",encryptedMobile,channelCode,e);
            return null;
        }
    }

    /**
     *  4.2.2	曲库内容一点下线回执接口
     * *******	接口描述
     * 渠道侧通过该接口把一点下线结果回执给合作支撑平台
     * 渠道商ID	渠道商名称
     * 0100864	成都市悠然荐音科技有限公司
     * 0100537	成都麦禾文化传播有限公司
     * 回执签名秘钥：
     * 悠然：4a7018e8e78049ff8c4dbcc1532a469b
     * 麦禾：7fe76102949b42ae8331263fd01f4922
     *
     * @param channelId
     * @param channelId
     * @return
     */
    public @Nonnull RemoteResult clickoffReceipt(String channelId, String signKey, String transactionId) {
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelBizCode", channelId);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        //请求签名 请求参数签名，算法如下： MD5（channelBizCode + timestamp参数值 + 秘钥） 说明： 1、渠道商id 2、timestamp参数值：请求时时间戳  3、安全校验签名秘钥：签名密钥，双方线下约定，且需要保密
        String signature = signatureService.signature(channelId, timestamp, signKey);
        dataNode.put("signature", signature);
        dataNode.put("transactionId", transactionId);
        dataNode.put("code", "0"); //处理结果状态码	string 枚举值：0：成功 1：失败
        //dataNode.put("msg", "");  //处理原因	string	非必传 处理失败原因
        String data = dataNode.toString();
        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, data);
        //log.info("曲库内容一点下线回执请求=>渠道商id:{},transactionId:{},请求参数data:{}",channelId,transactionId,data);

        final String clickoffReceiptUrl = "http://hz.migu.cn/clickoffnotify/rest/click/offline/receipt.do";
        Request request = new Request.Builder().url(clickoffReceiptUrl).post(okhttp3.RequestBody.create(JSON, data)).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            //log.info("曲库内容一点下线回执响应=>渠道商id:{},transactionId:{},响应:{}",channelId,transactionId,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).readValue(content);
        } catch (Exception e) {
            log.warn("曲库内容一点下线回执=>渠道商id:{},transactionId:{},异常!",channelId,transactionId,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 音频彩铃包月查询
     *
     * @param mobileOrToken
     * @param serviceId
     * @return status    String	包月状态： 0:未包月 1:已包月
     * {"status":"1","payType":"0","resCode":"000000","resMsg":"【OPEN】操作成功"}
     */
    public @Nonnull RemoteResult crbtMonthQuery(String mobileOrToken, String channelCode, String serviceId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕音频彩铃包月查询=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        serviceId = StringUtils.isEmpty(serviceId) ? cmsCrackConfig.getServiceId() : serviceId;
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        //dataNode.put("youCallbackName", "");
        String data = dataNode.toString();
        log.info("咪咕音频彩铃包月查询请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String crbtMonthQueryUrl = bizProperties.getCrbtMonthQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(crbtMonthQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕音频彩铃包月查询请求响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕音频彩铃包月查询=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 音频彩铃包月退订
     *
     * @param mobileOrToken
     * @return   {"resCode":"000000","resMsg":"成功"}
     */
    public @Nonnull RemoteResult crbtMonthCancel(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕音频彩铃包月退订=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        //dataNode.put("youCallbackName", "");
        String data = dataNode.toString();
        log.info("咪咕音频彩铃包月退订请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String crbtMonthCancelUrl = bizProperties.getCrbtMonthCancelUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(crbtMonthCancelUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕音频彩铃包月退订请求响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕音频彩铃包月退订=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }


    /**
     * 联合会员查询(藕粉联合会员,不是之前的网盘联合会员)
     *   https://hz.migu.cn/order/rest/cp/associate/member/query.do?callback=queryMember_asMberMiguSdkCallback&data=%7B%22channelCode%22%3A%22002105C%22%2C%22serviceId%22%3A%22698039034100000066%22%2C%22token%22%3A%2255dd50582a204736963826651d1ede2d%22%2C%22youCallbackName%22%3A%22queryMember_asMberMiguSdkCallback%22%7D&0.08423759620964089v4s.0a.d79b92e2f5a64a4fbc05e52770117371n.7eca79ba2a6d4ac6962555c785d133cfRNum0.5040290218736958
     * @param mobileOrToken
     * @return  {"mobile":"134*****200","name":"藕粉咪咕同享会","resCode":"300002","resMsg":"未订购"}
     *          {"mobile":"134*****200","name":"藕粉咪咕同享会","resCode":"000000","resMsg":"已订购"}
     */
    public @Nonnull RemoteResult asMemberQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询联会员状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        dataNode.put("youCallbackName", "");
        String data = dataNode.toString();
        log.info("咪咕查询联合会员状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String asMemberQueryUrl = bizProperties.getAsMemberQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(asMemberQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕查询联合会员状态响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕查询联合会员状态=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 联合会员退订(藕粉联合会员,不是之前的网盘联合会员)
     *     https://hz.migu.cn/order/rest/cp/associate/member/cancel.do?callback=cancel_asMberYes&data=%7B%22channelCode%22%3A%22002105C%22%2C%22serviceId%22%3A%22698039034100000066%22%2C%22token%22%3A%2290c5b53a971f47989616baf6ed8bb2c1%22%2C%22youCallbackName%22%3A%22cancel_asMberMiguSdkCallback%22%2C%22byb%22%3A%22byb%22%7D&0.305057069716961v4s.0a.d79b92e2f5a64a4fbc05e52770117371n.7eca79ba2a6d4ac6962555c785d133cfRNum0.6397501147308189
     * @param mobileOrToken
     * @return  不管有没有订购都会返回这个退订结果 {"resCode":"000000","resMsg":"成功"}
     */
    public @Nonnull RemoteResult asMemberCancel(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕联合会员退订=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        dataNode.put("youCallbackName", "");
        String data = dataNode.toString();
        log.info("咪咕联合会员退订请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String asMemberCancelUrl = bizProperties.getAsMemberCancelUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(asMemberCancelUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕联合会员退订请求响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕联合会员退订=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 前置过滤校验
     *     http://hz.migu.cn/order/rest/filter/check.do
     *     {"youCallbackName":"yewutuiguangBack","interfaceCode":"932","msisdn":"13608182353","channelCode":"002100A","copyrightId":"","contentId":"","bizCode":"698039020050006680","prodType":"0","price":"3000","extJsLog":{"initMiguOrderId":"initId0.9853714280677395","logId":"RNum0.9925536382250995","sdkPayId":"afc52723778140bca28234b51e1db462"}}
     * @param mobile
     * @return  true:过滤器校验放行,false:过滤器校验失败
     *  {"resCode":"999999","resMsg":"【OPEN】过滤器校验失败"}
     *  {"resCode":"000000","resMsg":"【OPEN】过滤器校验成功"}
     */
    public boolean filerCheck(String mobile, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕前置过滤校验=>mobile:{},渠道号:{},未配置的渠道号!", mobile, channelCode);
            return Boolean.TRUE;
        }
        //final String bizType = BizConstant.getBizTypeByMiguChannel(channelCode);
        final String bizType = cmsCrackConfig.getBizType();
//        //暂只支持渠道包和白金会员和视宣号
        if (!StringUtils.equalsAny(bizType, BizConstant.BIZ_TYPE_CPMB, BizConstant.BIZ_TYPE_BJHY, BizConstant.BIZ_TYPE_SXH, BizConstant.BIZ_TYPE_SCH)) {
            return Boolean.TRUE;
        }
        String serviceId = cmsCrackConfig.getServiceId();
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("youCallbackName", "filerCheckBack");
        dataNode.put("interfaceCode", "932");
        dataNode.put("msisdn", mobile);
        dataNode.put("channelCode", channelCode);
        dataNode.put("copyrightId", "");
        dataNode.put("contentId", "");
        dataNode.put("bizCode", serviceId);
        dataNode.put("prodType", "0");
        dataNode.put("price", "1000");  //暂时固定使用10元资费,这个参数不用也能查
        dataNode.putRawValue("extJsLog",new RawValue("{\"initMiguOrderId\":\"initId0.9853714280677395\",\"logId\":\"RNum0.9925536382250995\",\"sdkPayId\":\"afc52723778140bca28234b51e1db462\"}"));
        String data = dataNode.toString();
        log.info("咪咕前置过滤校验请求=>mobile:{},渠道号:{},请求参数data:{}",mobile,channelCode,data);

        final String asMemberCancelUrl = bizProperties.getFilerCheckUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(asMemberCancelUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("咪咕前置过滤校验请求响应=>mobile:{},渠道号:{},响应:{}", mobile, channelCode, content);
            return !"999999".equals(mapper.readTree(content).get("resCode").asText());
        } catch (Exception e) {
            log.warn("咪咕前置过滤校验=>mobile:{},渠道号:{},异常!", mobile, channelCode, e);
            return Boolean.TRUE;
        }
    }


    /**
     * 前置过滤校验
     * http://hz.migu.cn/order/rest/filter/check.do
     * {"youCallbackName":"yewutuiguangBack","interfaceCode":"932","msisdn":"13608182353","channelCode":"002100A","copyrightId":"","contentId":"","bizCode":"698039020050006680","prodType":"0","price":"3000","extJsLog":{"initMiguOrderId":"initId0.9853714280677395","logId":"RNum0.9925536382250995","sdkPayId":"afc52723778140bca28234b51e1db462"}}
     *
     * @param mobile
     * @return true:过滤器校验放行,false:过滤器校验失败
     * {"resCode":"999999","resMsg":"【OPEN】过滤器校验失败"}
     * {"resCode":"000000","resMsg":"【OPEN】过滤器校验成功"}
     */
    public boolean wangyiyunFilerCheck(String mobile) {
        String url = "https://musmh.uebilling.com/member/order/rest/filter/check.do";
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(BIZ_WYY_CHANNEL_CODE);
        if (null == cmsCrackConfig) {
            log.info("咪咕前置过滤校验=>mobile:{},渠道号:{},未配置的渠道号!", mobile, "");
            return Boolean.TRUE;
        }
        String serviceId = cmsCrackConfig.getServiceId();
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("youCallbackName", "filerCheckBack");
        dataNode.put("interfaceCode", "932");
        dataNode.put("msisdn", mobile);
        dataNode.put("channelCode", BIZ_WYY_CHANNEL_CODE);
        dataNode.put("copyrightId", "");
        dataNode.put("contentId", "");
        dataNode.put("bizCode", serviceId);
        dataNode.put("prodType", "0");
        dataNode.put("price", "1000");  //暂时固定使用10元资费,这个参数不用也能查
        dataNode.putRawValue("extJsLog", new RawValue("{\"initMiguOrderId\":\"initId0.9853714280677395\",\"logId\":\"RNum0.9925536382250995\",\"sdkPayId\":\"afc52723778140bca28234b51e1db462\"}"));
        String data = dataNode.toString();
        log.info("咪咕前置过滤校验请求=>mobile:{},渠道号:{},请求参数data:{}", mobile, BIZ_WYY_CHANNEL_CODE, data);
        HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("咪咕前置过滤校验请求响应=>mobile:{},渠道号:{},响应:{}", mobile, BIZ_WYY_CHANNEL_CODE, content);
            return !"999999".equals(mapper.readTree(content).get("resCode").asText());
        } catch (Exception e) {
            log.warn("咪咕前置过滤校验=>mobile:{},渠道号:{},异常!", mobile, BIZ_WYY_CHANNEL_CODE, e);
            return Boolean.TRUE;
        }
    }


    /**
     * 主叫视频彩铃功能状态查询接口
     *  当返回结果为000000时，该字段不可空。
     * @param productType 功能类型  0：被叫个人视频彩铃功能 1：主叫视频彩铃功能
     *  {"vrbtStatus":"1","resCode":"000000","resMsg":"成功"}
     *  返回结果类型， 当返回结果为000000时，该字段不可空。
     *                    1:未开通所查询的视频彩铃功能。
     *                    2：已开通所查询的视频彩铃功能，但所对应视频彩铃播放能力处于关闭状态。
     *                    3：已开通所查询的视频彩铃功能，且所对应的视频彩铃播放能力处于开启状态。
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult vrbtInitiativeStatusQuery(String mobileOrToken, String channelCode, String productType) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询主叫视频彩铃功能状态=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        dataNode.put("productType", StringUtils.isEmpty(productType) ? VRBT_FUN_PRODUCT_TYPE_ACTIVE : productType); //0：被叫个人视频彩铃功能 1：主叫视频彩铃功能
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        // 彩铃运营中心此处必须带上token,开放那边同时支持手机号或者token
        if(isCentralityChannel(channelCode)){
            final String token = this.fetchToken(mobileOrToken, channelCode);
            if(StringUtils.isEmpty(token)){
                return RemoteResult.fail("咪咕登录失败!");
            }
            dataNode.put("token", token);
        }else {
            dataNode.put(this.isMobile(mobileOrToken)?"msisdn":"token", mobileOrToken);
        }
        String data = dataNode.toString();
        log.info("咪咕查询主叫视频彩铃功能状态请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtStatusQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtInitiativeStatusQueryUrlCentrality() : bizProperties.getVrbtInitiativeStatusQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询主叫视频彩铃功能状态响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询主叫视频彩铃功能状态=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 主叫视频彩铃功能开通
     *  当返回结果为000000时，该字段不可空。
     *  {"resCode":"000000","resMsg":"成功"}
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult vrbtInitiativeSubscribe(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕主叫视频彩铃功能开通=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        dataNode.put("productType", "1"); //0：被叫个人视频彩铃功能 1：主叫视频彩铃功能
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        // 彩铃运营中心此处必须带上token,开放那边同时支持手机号或者token
        if(isCentralityChannel(channelCode)){
            final String token = this.fetchToken(mobileOrToken, channelCode);
            if(StringUtils.isEmpty(token)){
                return RemoteResult.fail("咪咕登录失败!");
            }
            dataNode.put("token", token);
        }else {
            dataNode.put(this.isMobile(mobileOrToken)?"msisdn":"token", mobileOrToken);
        }
        String data = dataNode.toString();
        log.info("咪咕主叫视频彩铃功能开通请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtStatusQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtInitiativeSubscribeUrlCentrality() : bizProperties.getVrbtInitiativeSubscribeUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕主叫视频彩铃功能开通响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕主叫视频彩铃功能开通=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 主叫视频彩铃设置
     * 当返回结果为000000时，该字段不可空。
     * {"resCode":"000000","resMsg":"成功"}
     *
     * @param mobileOrToken
     * @param copyRightID   版权id
     * @return
     */
    public @Nonnull
    RemoteResult vrbtInitiativeSetting(String mobileOrToken, String channelCode, String copyRightID) {
        //只允许设置我们自己cp的视频彩铃  渠道全部699052、订阅佐悦638799
        if (!BizConstant.isLegalVrbtCopyrightId(copyRightID) && !isXunfeiChannelCode(channelCode)) {
            log.info("咪咕视频彩铃包月内内容免费订购=>mobileOrToken:{},渠道号:{},copyRightID:{},视频彩铃版权ID或产品ID错误!", mobileOrToken, channelCode, copyRightID);
            return RemoteResult.fail("视频彩铃版权ID或产品ID错误");
        }
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕主叫视频彩铃设置=>mobileOrToken:{},渠道号:{},copyRightID:{},未配置的渠道号!", mobileOrToken, channelCode, copyRightID);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        dataNode.put("settingType", "1");
        dataNode.put("callerID", "0"); //settingType为1、3时，填写0；(settingType默认为0)
        ArrayNode contentIDAsNode = dataNode.putArray("contentIDs");
        contentIDAsNode.add(copyRightID);
        dataNode.put("sceneName", "aa");//标识情景名称，当settingType为3时，可携带该字段，其余场景该字段无效。
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        // 彩铃运营中心此处必须带上token,开放那边同时支持手机号或者token
        if (isCentralityChannel(channelCode)) {
            final String token = this.fetchToken(mobileOrToken, channelCode);
            if (StringUtils.isEmpty(token)) {
                return RemoteResult.fail("咪咕登录失败!");
            }
            dataNode.put("token", token);
        } else {
            dataNode.put(this.isMobile(mobileOrToken) ? "msisdn" : "token", mobileOrToken);
        }
        String data = dataNode.toString();
        log.info("咪咕主叫视频彩铃设置请求=>mobileOrToken:{},渠道号:{},copyRightID:{},请求参数data:{}", mobileOrToken, channelCode, copyRightID, data);

        final String vrbtStatusQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtInitiativeSettingUrlCentrality() : bizProperties.getVrbtInitiativeSettingUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content = response.body().string();
            log.info("咪咕主叫视频彩铃设置响应=>mobileOrToken:{},渠道号:{},copyRightID:{},响应:{}", mobileOrToken, channelCode, copyRightID, content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕主叫视频彩铃设置=>手机号:{},渠道号:{},copyRightID:{},异常!", mobileOrToken, channelCode, copyRightID, e);
            return RemoteResult.fail("咪咕接口通信异常:" + e.getMessage());
        }
    }

    /**
     * 查询视彩号包月订购关系
     *
     * @param mobileOrToken
     * @return   {"status":"1","resCode":"000000","resMsg":"成功"}
     *    status  0已订购 1未订购
     */
    public @Nonnull RemoteResult schQuery(String mobileOrToken, String channelCode) {
        //兼容老的sch渠道,数智人视彩号未接口权限,先走白金会员查询接口
        //if(MiguApiService.BIZ_SCH_CHANNEL_CODE_SZR.equals(channelCode)) {
        //    return bjhyQuery(mobileOrToken,channelCode);
        //}
        // 判断是否有注入sql
        if(PatternUtil.hasInjectionSql(mobileOrToken)){
            return RemoteResult.fail("必要参数错误");
        }
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视彩号包月订购关系=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        String data = dataNode.toString();
        log.info("咪咕查询视彩号包月订购关系请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

//        final String bjhyQueryUrl = bizProperties.getSchQueryUrl();
        final String bjhyQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtMonthStatusQueryUrlCentrality() : bizProperties.getSchQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕查询视彩号包月订购关系响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕查询视彩号包月订购关系=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }


    /**
     *     # 1.1.1.1.	视彩号特殊内容权益领取接口P23A3 (用于ai视频彩铃,视彩号)
     * 接口请求方式为post raw json
     *
     * @param mobileOrToken
     * @param channelCode   渠道号
     //* @param contentId   产品id
     //* @param copyRightId   版权id
     * @return
     */
    public @Nonnull
    RemoteResult specialProductSub(String mobileOrToken, String channelCode/*, String contentId, String copyRightId*/) {
        //只允许设置我们自己cp的视频彩铃  渠道全部699052、订阅佐悦638799
        //if (!BizConstant.isLegalVrbtCopyrightId(copyRightID) && !isXunfeiChannelCode(channelCode)) {
        //    log.info("咪咕视频彩铃包月内内容免费订购=>mobileOrToken:{},渠道号:{},copyRightID:{},视频彩铃版权ID或产品ID错误!", mobileOrToken, channelCode, copyRightID);
        //    return RemoteResult.fail("视频彩铃版权ID或产品ID错误");
        //}
        final SchSpecialProductConfig schSpecialProductConfig = bizProperties.getSchChannelSpecialProductConfigMap().get(channelCode);
        String serviceId = schSpecialProductConfig.getServiceId();
        //String serviceId = "698039049105618391";
        String contentId = schSpecialProductConfig.getContentId();
        //String contentId = "600926000980000024";
        //String contentId = "600926000012738363";
        //String copyRightId = "600000T00001";
        String copyRightId = schSpecialProductConfig.getCopyRightId();
        //String copyRightId = "600000T0001Y";
        //String copyRightId = "600000T0104";



        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕特殊内容权益领取=>mobileOrToken:{},渠道号:{},contentId:{},copyRightId:{},未配置的渠道号!", mobileOrToken, channelCode, contentId, copyRightId);
            return RemoteResult.fail("未配置的渠道号");
        }
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        //dataNode.put("serviceId", "698039049105618391");
        dataNode.put("token", token);
        dataNode.put("contentId", contentId);
        dataNode.put("copyRightId", copyRightId);
        String data = dataNode.toString();
        log.info("咪咕特殊内容权益领取请求=>mobileOrToken:{},渠道号:{},contentId:{},copyRightId:{},请求参数data:{}", mobileOrToken, channelCode, contentId, copyRightId, data);

        final String specialProductSubUrl = isCentralityChannel(channelCode) ? bizProperties.getSpecialProductSubUrlCentrality() : bizProperties.getSpecialProductSubUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(specialProductSubUrl, cmsCrackConfig))/*.newBuilder().addQueryParameter("data", data).build()*/;
        Request request = new Request.Builder().url(httpUrl).post(RequestBody.create(JSON,data)).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content = response.body().string();
            log.info("咪咕特殊内容权益领取响应=>mobileOrToken:{},渠道号:{},contentId:{},copyRightId:{},响应:{}", mobileOrToken, channelCode, contentId, copyRightId, content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕特殊内容权益领取=>手机号:{},渠道号:{},contentId:{},copyRightId:{},异常!", mobileOrToken, channelCode, contentId, copyRightId, e);
            return RemoteResult.fail("咪咕接口通信异常:" + e.getMessage());
        }
    }


    /**
     * 查询主叫视频彩铃设置
     *  当返回结果为000000时，该字段不可空。
     *  {"resCode":"000000","resMsg":"成功"}
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult vrbtInitiativeQuerySetting(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询主叫视频彩铃设置=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        dataNode.put("startRecordNum", "1"); //开始记录标识
        dataNode.put("endRecordNum", "50"); //结束记录标识 建议开始记录标识和结束记录标识间隔不超过50条记录
        dataNode.put("queryType", "2"); //返回查询结果集
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        // 彩铃运营中心此处必须带上token,开放那边同时支持手机号或者token
        if(isCentralityChannel(channelCode)){
            final String token = this.fetchToken(mobileOrToken, channelCode);
            if(StringUtils.isEmpty(token)){
                return RemoteResult.fail("咪咕登录失败!");
            }
            dataNode.put("token", token);
        }else {
            dataNode.put(this.isMobile(mobileOrToken)?"msisdn":"token", mobileOrToken);
        }
        String data = dataNode.toString();
        log.info("咪咕查询主叫视频彩铃设置请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String vrbtStatusQueryUrl = isCentralityChannel(channelCode) ? bizProperties.getVrbtInitiativeQuerySettingUrlCentrality() : bizProperties.getVrbtInitiativeQuerySettingUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询主叫视频彩铃设置响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询主叫视频彩铃设置=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 视宣号*******.2 查询商户ID
     * 视宣号订购关系查询也复用此方法,调用RemoteResult.isSxhMember()方法判定
     *  {
     *     "code": "000000",
     *     "msg": "成功",
     *     "data": [
     *         {
     *             "circleId": "100001119928277",
     *             "circleName": "商户名称",
     *             "description": "商户说明",
     *             "circleOwnerMobile": "18200394809",
     *             "status": "01",
     *             "createTime": "2024-03-06 17:51:01",
     *             "memberCount": "10"
     *         }
     *     ]
     * }
     * {"code":"200000","info":"client不存在"}
     *
     *   status String 必填 状态 00创建中/未订购 01正常/已订购 03订购中 04已退订 05已失效/已过期 06订购失败 07已暂停
     * @param mobile
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.CMS_MIGU_API_SHORT_TERM_CACHE,key = "#root.methodName + ':' + #channelCode + '-' + #mobile",unless = "#result.resCode != '000000'")
    public @Nonnull RemoteResult sxhGetCircleId(String mobile, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("视宣号查询商户=>mobile:{},渠道号:{},未配置的渠道号!",mobile,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }

        String data = mapper.createObjectNode()
                .put("circleOwnerMobile", mobile)
                .toString();
        String seq = DateUtil.formatFullTime(LocalDateTime.now());
        String digest = signatureService.sxhDigest(seq, cmsCrackConfig.getSignatureSecretKey());

        log.info("视宣号查询商户=>mobile:{},渠道号:{},请求参数data:{}",mobile,channelCode,data);
        final Request request = new Request.Builder().url(bizProperties.getSxhGetCircleListUrl())
                .addHeader("seq",seq)
                .addHeader("client",channelCode)
                .addHeader("digest",digest)
                .post(RequestBody.create(JSON, data))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("视宣号查询商户=>mobile:{},渠道号:{},响应:{}",mobile,channelCode,content);
            final JsonNode tree = mapper.readTree(content);
            if(!RemoteResult.CODE_OK.equals(tree.at("/code").asText())){
                return RemoteResult.fail(tree.at("/info").asText());
            }
            final JsonNode dataNode = tree.at("/data");
            if(null==dataNode||!dataNode.isArray()){
               return RemoteResult.fail("查询商户信息失败");
            }

            Pair<String, String> circleIdAndStatus = Streams.stream(dataNode.elements())
                    //status String 必填 状态 00创建中/未订购 01正常/已订购 03订购中 04已退订 05已失效/已过期 06订购失败 07已暂停
                    .filter(jsonNode -> StringUtils.equalsAny(jsonNode.get("status").asText(), "01", "03"))
                    .findAny()
                    .map(jsonNode -> Pair.of(jsonNode.get("circleId").asText(), jsonNode.get("status").asText()) )
                    .orElse(null);

            if(circleIdAndStatus == null){
                return RemoteResult.fail("未查询到商户信息");
            }

            final RemoteResult result = RemoteResult.success();
            result.setToken(circleIdAndStatus.getLeft());
            result.setStatus(circleIdAndStatus.getRight());

            return result;

        } catch (IOException e) {
            log.warn("视宣号查询商户=>mobile:{},渠道号:{},异常!",mobile,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * *******.3 商户成员查询接口
     * 接口描述：该接口用户商户成员查询，可查询成员的订购状态、以及功能开通情况等。
     * 请求参数
     *      *若未传参数，查询对应商户ID下的全部数据，超出1000，只能返回1000条；
     *
     *   status String 必填 状态 00创建中/未订购 01正常/已订购 03订购中 04已退订 05已失效/已过期 06订购失败 07已暂停
     * @param circleId 商户id
     * @param mobile 手机号 (只用作日志输出,不按该手机号查询商户成员)
     * @param channelCode 渠道号
     * @return
     */
    public @Nonnull Result<Object> sxhGetMemberList(String circleId, String mobile, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("视宣号商户成员查询=>mobile:{},渠道号:{},未配置的渠道号!",mobile,channelCode);
            return Result.error("未配置的渠道号");
        }

        String data = mapper.createObjectNode()
                .put("circleId", circleId)
                .toString();
        String seq = DateUtil.formatFullTime(LocalDateTime.now());
        String digest = signatureService.sxhDigest(seq, cmsCrackConfig.getSignatureSecretKey());

        log.info("视宣号商户成员查询=>mobile:{},渠道号:{},请求参数data:{}",mobile,channelCode,data);
        final Request request = new Request.Builder().url(bizProperties.getSxhGetMemberListUrl())
                .addHeader("seq",seq)
                .addHeader("client",channelCode)
                .addHeader("digest",digest)
                .post(RequestBody.create(JSON, data))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("视宣号商户成员查询=>mobile:{},渠道号:{},响应:{}",mobile,channelCode,content);
            final JsonNode tree = mapper.readTree(content);
            if(!RemoteResult.CODE_OK.equals(tree.at("/code").asText())){
                return Result.error(tree.at("/info").asText());
            }
            final JsonNode dataNode = tree.at("/data");
            if(null==dataNode||!dataNode.isArray()){
                return Result.error("商户成员查询失败");
            }

            return Result.okAndSetData(dataNode);

        } catch (IOException e) {
            log.warn("视宣号商户成员查询=>mobile:{},渠道号:{},异常!", mobile,channelCode,e);
            return Result.error("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * *******.3 商户成员查询接口封装
     *
     * @param mobile
     * @param channelCode
     * @return
     */
    public @Nonnull Result<Object> sxhQueryMemberList(String mobile, String channelCode) {
        //先获取商户id
        final RemoteResult getCircleIdResult = sxhGetCircleId(mobile, channelCode);
        if(!getCircleIdResult.isOK()) {
            return Result.error(getCircleIdResult.getResMsg());
        }
        final String circleId = getCircleIdResult.getToken();

        return sxhGetMemberList(circleId,mobile,channelCode);
    }

    /**
     *    商户铃音内容查询
     * @param mobile
     * @param circleId
     * @param channelCode
     * @return
     */
    public @Nonnull Result<Object> sxhGetRingList(String mobile, String circleId, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("视宣号商户铃音内容查询=>mobile:{},渠道号:{},未配置的渠道号!",mobile,channelCode);
            return Result.error("未配置的渠道号");
        }

        String data = mapper.createObjectNode()
                .put("circleId", circleId)
                .toString();
        String seq = DateUtil.formatFullTime(LocalDateTime.now());
        String digest = signatureService.sxhDigest(seq, cmsCrackConfig.getSignatureSecretKey());

        log.info("视宣号商户铃音内容查询=>mobile:{},渠道号:{},请求参数data:{}",mobile,channelCode,data);
        final Request request = new Request.Builder().url(bizProperties.getSxhGetCircleRingListUrl())
                .addHeader("seq",seq)
                .addHeader("client",channelCode)
                .addHeader("digest",digest)
                .post(RequestBody.create(JSON, data))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("视宣号商户铃音内容查询=>mobile:{},渠道号:{},响应:{}",mobile,channelCode,content);
            final JsonNode tree = mapper.readTree(content);
            if(!RemoteResult.CODE_OK.equals(tree.at("/code").asText())){
                return Result.error(tree.at("/info").asText());
            }
            final JsonNode dataNode = tree.at("/data");
            if(null==dataNode||!dataNode.isArray()){
                return Result.error("商户铃音内容查询失败");
            }

            return Result.okAndSetData(dataNode);

        } catch (IOException e) {
            log.warn("视宣号商户铃音内容查询=>mobile:{},渠道号:{},异常!", mobile,channelCode,e);
            return Result.error("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * *******.3 商户成员查询接口封装
     *
     * @param mobile
     * @param channelCode
     * @return
     */
    public @Nonnull Result<Object> sxhQueryRingList(String mobile, String channelCode) {
        //先获取商户id
        final RemoteResult getCircleIdResult = sxhGetCircleId(mobile, channelCode);
        if(!getCircleIdResult.isOK()) {
            return Result.error(getCircleIdResult.getResMsg());
        }
        final String circleId = getCircleIdResult.getToken();

        return sxhGetRingList(mobile,circleId,channelCode);
    }

    /**
     * 视宣号*******.4 铃音内容设置和删除
     * 大家注意，铃音设置，对于无功能的，要先设置（设置接口，参数：0），然后判断是否开通功能，如果没功能，则需要再调用一次取消设置（设置接口，参数：2）
     * 这样做的原因是：设置内容，会直接设置为在播状态，这样目前用户进入商户中心没有办法开通功能，所以再调用一次取消设置，把内容从在播状态变为闲置状态，这样用户进入商户中心看到的就是闲置的内容，用户点击设为在播时，会给用户开通功能
     *  {
     *     "code": "000000",
     *     "msg": "成功"
     * }
     * {"code":"200000","info":"client不存在"}
     *
     *
     * @param mobile 手机号
     * @param circleId 商户id
     * @param setType
     * String
     * 必填
     * 设置方式
     * 0：追加设置
     * 1：覆盖设置
     * 2：取消设置（若未传铃音ID，表示取消圈子的全部铃音设置）
     * 3：删除铃音
     * @param ringId
     * 铃音ID（取消设置时非必填）
     *
     * timeType
     * String
     * 非必填
     * 全天设置/分时设置    (1全天 2分时，追加设置和覆盖设置必填)
     * @return
     */
    public @Nonnull RemoteResult sxhRingOperate(String mobile,String circleId, String channelCode, String setType,String ringId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("视宣号铃音内容设置和删除=>mobile:{},circleId:{},渠道号:{},setType:{},ringId:{},未配置的渠道号!",mobile,circleId,channelCode,setType,ringId);
            return RemoteResult.fail("未配置的渠道号");
        }

        final ArrayNode ringIdArrayNode = mapper.createArrayNode();
        if(StringUtils.isNotEmpty(ringId)){
            ringIdArrayNode.add(ringId);
        }
        String data = mapper.createObjectNode()
                .put("circleId", circleId)
                .put("setType", setType)
                .put("timeType", "1")
                .set("ringId", ringIdArrayNode)
                .toString();
        String seq = DateUtil.formatFullTime(LocalDateTime.now());
        String digest = signatureService.sxhDigest(seq, cmsCrackConfig.getSignatureSecretKey());

        log.info("视宣号铃音内容设置和删除=>mobile:{},circleId:{},渠道号:{},setType:{},ringId:{},请求参数data:{}",mobile,circleId,channelCode,setType,ringId,data);
        final Request request = new Request.Builder().url(bizProperties.getSxhRingOperateUrl())
                .addHeader("seq",seq)
                .addHeader("client",channelCode)
                .addHeader("digest",digest)
                .post(RequestBody.create(JSON, data))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("视宣号铃音内容设置和删除=>mobile:{},circleId:{},渠道号:{},setType:{},ringId:{},响应:{}",mobile,circleId,channelCode,setType,ringId,content);

            final JsonNode tree = mapper.readTree(content);
            if(!RemoteResult.CODE_OK.equals(tree.at("/code").asText())){
                return RemoteResult.fail(tree.at("/info").asText());
            }

            return RemoteResult.success();

        } catch (IOException e) {
            log.warn("视宣号铃音内容设置和删除=>mobile:{},circleId:{},渠道号:{},setType:{},ringId:{},异常!",mobile,circleId,channelCode,setType,ringId,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 视宣号*******.4 铃音设置接口封装
     * 大家注意，铃音设置，对于无功能的，要先设置（设置接口，参数：0），然后判断是否开通功能，如果没功能，则需要再调用一次取消设置（设置接口，参数：2）
     * 这样做的原因是：设置内容，会直接设置为在播状态，这样目前用户进入商户中心没有办法开通功能，所以再调用一次取消设置，把内容从在播状态变为闲置状态，这样用户进入商户中心看到的就是闲置的内容，用户点击设为在播时，会给用户开通功能
     * @param mobile
     * @param channelCode
     * @param ringId
     * @return
     */
    public @Nonnull RemoteResult sxhRingSet(String mobile, String channelCode, String ringId) {
        //先获取商户id
        final RemoteResult getCircleIdResult = sxhGetCircleId(mobile, channelCode);
        if(!getCircleIdResult.isOK()) {
            return getCircleIdResult;
        }
        //然后设置铃音 ,要先设置（设置接口，参数：0）
        final String circleId = getCircleIdResult.getToken();
        final RemoteResult ringOperateResult = sxhRingOperate(mobile, circleId, channelCode, "0", ringId);
        //判断有无功能
        final RemoteResult funStatusResult = vrbtStatusQuery(mobile, channelCode);
        //将功能状态返回
        ringOperateResult.setStatus(funStatusResult.getStatus());
        //如果没功能，则需要再调用一次取消设置（设置接口，参数：2）
        if(funStatusResult.isOK() && VRBT_FUN_STATUS_NONE.equals(funStatusResult.getStatus())) {
            sxhRingOperate(mobile, circleId, channelCode,"2",null);
        }

        //返回铃音设置的结果
        return ringOperateResult;
    }



    /**
     * *******.4 视宣号铃音上传
     * 接口描述：该接口用于上传铃音，只支持上传视频彩铃，该接口不做自动设置，需要第三方再次调用设置接口。
     *  {
     *     "code": "000000",
     *     "msg": "成功"
     * }
     * {"code":"200000","info":"client不存在"}
     *
     *
     * @param mobile 手机号
     * @param circleId 商户id
     * ringName String 必填 铃音名称（50个字符以内，如50个字符以内还是报错，则可能为后台会在铃音名称上加随机值，防止铃音名称重复）
     * ringFile file 必填 铃音文件（时长为7~48s，大小≤80M，格式为mp4,mpeg,vob,avi,mpg,wmv或mov）
     * transactionId String 必填 铃音标识（渠道号 + uuid）
     * @return
     */
    public @Nonnull RemoteResult sxhRingUpload(String mobile, String circleId, String channelCode, RequestBody ringFileBody) {
        String uuid = IdWorker.get32UUID();
        return sxhRingUpload( mobile,  circleId,  channelCode, channelCode+uuid, uuid,  ringFileBody);
    }




    /**
     * 判断是否为讯飞的视频彩铃渠道号
     * @param channelCode
     * @return
     */
    public boolean isXunfeiChannelCode(String channelCode){
        return xunfeiVrbtProperties.getChannelChargeIdMap().containsKey(channelCode);
    }



    /**
     * *******.4 视宣号铃音上传
     * 接口描述：该接口用于上传铃音，只支持上传视频彩铃，该接口不做自动设置，需要第三方再次调用设置接口。
     *  {
     *     "code": "000000",
     *     "msg": "成功"
     * }
     * {"code":"200000","info":"client不存在"}
     *
     *
     * @param mobile 手机号
     * @param circleId 商户id
     * ringName String 必填 铃音名称（50个字符以内，如50个字符以内还是报错，则可能为后台会在铃音名称上加随机值，防止铃音名称重复）
     * ringFile file 必填 铃音文件（时长为7~48s，大小≤80M，格式为mp4,mpeg,vob,avi,mpg,wmv或mov）
     * transactionId String 必填 铃音标识（渠道号 + uuid）
     * @return
     */
    public @Nonnull RemoteResult sxhRingUpload(String mobile, String circleId, String channelCode,String transactionId,String uuid, RequestBody ringFileBody) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("视宣号铃音上传=>mobile:{},circleId:{},transactionId:{},ringName:{},渠道号:{}未配置的渠道号!",mobile,circleId,transactionId,uuid,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        MultipartBody multipartBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("circleId", circleId)
                .addFormDataPart("ringName", uuid)
                .addFormDataPart("ringFile", uuid + ".mp4", ringFileBody)
                .addFormDataPart("transactionId",transactionId)
                .build();

        String seq = DateUtil.formatFullTime(LocalDateTime.now());
        String digest = signatureService.sxhDigest(seq, cmsCrackConfig.getSignatureSecretKey());

        log.info("视宣号铃音上传=>mobile:{},circleId:{},transactionId:{},渠道号:{},ringName:{}",mobile,circleId,transactionId,channelCode,uuid);
        final Request request = new Request.Builder().url(bizProperties.getSxhRingUploadUrl())
                .addHeader("seq",seq)
                .addHeader("client",channelCode)
                .addHeader("digest",digest)
                .post(multipartBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("视宣号铃音上传=>mobile:{},circleId:{},transactionId:{},渠道号:{},ringName:{},响应:{}",mobile,circleId,transactionId,channelCode,uuid,content);

            final JsonNode tree = mapper.readTree(content);
            if(!RemoteResult.CODE_OK.equals(tree.at("/code").asText())){
                return RemoteResult.fail(tree.at("/info").asText());
            }

            return RemoteResult.success();

        } catch (Exception e) {
            log.warn("视宣号铃音上传=>mobile:{},circleId:{},transactionId:{},渠道号:{},ringName:{},异常!",mobile,circleId,transactionId,channelCode,uuid,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 2.3.1.4.8.	主叫视频彩铃功能查询接口P23A5
     * vrbtStatus
     *  返回结果类型， 当返回结果为000000时，该字段不可空。
     *  1:未开通所查询的视频彩铃功能。
     *  2：已开通所查询的视频彩铃功能，但所对应视频彩铃播放能力处于关闭状态。
     *  3：已开通所查询的视频彩铃功能，且所对应的视频彩铃播放能力处于开启状态。
     *
     * @param mobileOrToken
     * @return
     */
    public @Nonnull RemoteResult activeVrbtQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕主叫视频彩铃功能查询=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        dataNode.put("token", token);
        String data = dataNode.toString();
        log.info("咪咕主叫视频彩铃功能查询请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String activeVrbtQueryUrl = bizProperties.getActiveVrbtQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(activeVrbtQueryUrl, cmsCrackConfig));
        Request request = new Request.Builder().url(httpUrl)
                .post(RequestBody.create(JSON, data))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕主叫视频彩铃功能查询响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕主叫视频彩铃功能查询=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 2.3.1.4.9.	主叫视频彩铃内容免费订购接口P23A6
     *
     * @param mobileOrToken
     * @param channelCode
     * //@param contentId  内容id[即视频彩铃产品id],(彩铃运营中心可以使用版权id或者产品id,而不需要下面的copyRightID参数)
     * @param vrbtId 视频彩铃铃音id (支持18位产品id或者版权id)
     * @param setFlag 是否添加到个人默认铃音播放设置：默认为1 ；
     * 0：不添加设置 ；
     * 1订购且设置加入当前个人主叫默认铃音播放设置中；
     * 2订购且设置为当前唯一个人主叫视频默认铃音播放设置铃音；
     * @param ifSendSMS 是否需要发送内容订购提醒短信，默认为0
     * 0：发送 1：不发送
     * @return
     */
    public @Nonnull RemoteResult activeVrbtFreeOrder(String mobileOrToken, String channelCode, String vrbtId, String setFlag,String ifSendSMS) {
        ////只允许设置我们自己cp的视频彩铃  渠道全部699052、订阅佐悦638799
        //if(!BizConstant.isLegalVrbtCopyrightId(copyRightID) && !isXunfeiChannelCode(channelCode)){
        //    log.info("咪咕主叫视频彩铃内容免费订购=>mobileOrToken:{},渠道号:{},copyRightID:{},视频彩铃版权ID错误!",mobileOrToken,channelCode,copyRightID);
        //    return RemoteResult.fail("视频彩铃版权ID错误");
        //}
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕主叫视频彩铃内容免费订购=>mobileOrToken:{},渠道号:{},vrbtId:{},未配置的渠道号!",mobileOrToken,channelCode,vrbtId);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        if(BizConstant.BIZ_TYPE_SCH.equals(cmsCrackConfig.getType())){
            final SchSpecialProductConfig schSpecialProductConfig = bizProperties.getSchChannelSpecialProductConfigMap().get(channelCode);
            serviceId = null != schSpecialProductConfig ? schSpecialProductConfig.getServiceId() : serviceId;
        }

        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            log.info("咪咕主叫视频彩铃内容免费订购=>mobileOrToken:{},渠道号:{},vrbtId:{},咪咕登录失败!",mobileOrToken,channelCode,vrbtId);
            return RemoteResult.fail("咪咕登录失败!");
        }
        //if(org.apache.commons.lang3.StringUtils.containsAny(setFlag,VRBT_TONE_ORDER_SETFLAG_ZHUJIAO,VRBT_TONE_ORDER_SETFLAG_BOTH_ZBJ)){
        //
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        dataNode.put("serviceId", serviceId);
        String toneID = StringUtils.length(vrbtId)==18 ? vrbtId : SpringContextUtils.getBean(MiguApiService.class).fetchVrbtProduct(vrbtId).getVrbtProductId();
        dataNode.put("toneID", toneID);
        dataNode.put("setFlag", Strings.isNullOrEmpty(setFlag)?"1":setFlag);
        dataNode.put("ifSendSMS", Strings.isNullOrEmpty(setFlag)?"0":ifSendSMS);
        String data = dataNode.toString();
        log.info("咪咕主叫视频彩铃内容免费订购请求=>mobileOrToken:{},渠道号:{},vrbtId:{},请求参数data:{}",mobileOrToken,channelCode,vrbtId,data);

        final String activeVrbtFreeOrderUrl = bizProperties.getActiveVrbtFreeOrderUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(activeVrbtFreeOrderUrl, cmsCrackConfig));
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(RequestBody.create(JSON, data))
                .build();
        try (Response response = client.newCall(request).execute()) {
            //if (!response.isSuccessful()) {
            //    throw new IOException("Unexpected code " + response);
            //}

            String content  = response.body().string();
            log.info("咪咕主叫视频彩铃内容免费订购响应=>mobileOrToken:{},渠道号:{},vrbtId:{},响应:{}",mobileOrToken,channelCode,vrbtId,content);

            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.ToneFreeMonthOrderView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕主叫视频彩铃内容免费订购=>手机号:{},渠道号:{},vrbtId:{},异常!",mobileOrToken,channelCode,vrbtId,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 渠道包复合查询,会查询包月状态和用户归属省份,并根据省份判定是走被动还是主动视频彩铃功能开通和是否开通对应的视频彩铃功能
     * @param mobile
     * @return
     */
    public @Nonnull CpmbCombinResult cpmbCombinQuery(String mobile, String channelCode) {

        final CpmbCombinResult failResult = CpmbCombinResult.fail("音乐包查询信息失败");

        try {
            RemoteResult loginResult = this.miguLogin(mobile, channelCode);
            if(!loginResult.isOK()){
                return failResult;
            }
            String token = loginResult.getToken();
            final CompletableFuture<MobileRegionResult> mobileRegionFuture = CompletableFuture.supplyAsync(() -> mobileRegionService.query(mobile),executor);
            final CompletableFuture<RemoteResult> cpmbQueryFuture = CompletableFuture.supplyAsync(() -> cpmbQuery(token, channelCode),executor);
            return mobileRegionFuture.thenCombine(cpmbQueryFuture,
                    (mobileRegionResult, cpmbQueryResult) -> {
                        boolean isMonth = cpmbQueryResult.isCpmbMember();
                        //boolean isVrbtFunPassive = mobileRegionResult != null && StringUtils.equalsAny(mobileRegionResult.getProvince(), VRBT_PASSIVE_PROVINCES);
                        //String vrbtFunStatus = isVrbtFunPassive? vrbtStatusQuery(token, channelCode).getStatus() :  activeVrbtQuery(token, channelCode).getVrbtStatus();
                        String vrbtFunStatus = activeVrbtQuery(mobile, channelCode).getVrbtStatus();
                        boolean isVrbtFun = vrbtFunStatus!=null && !VRBT_FUN_STATUS_NONE.equals(vrbtFunStatus);
                        boolean needCrbtTehuiPack = mobileRegionResult != null && StringUtils.equalsAny(mobileRegionResult.getProvince(), CRBT_TEHUI_PACK_PROVINCES);
                        Boolean isCrbtTehuiPack = needCrbtTehuiPack ?  crbtMonthQuery(mobile, channelCode, CPMB_CRBT_MONTH_SERVICE_ID).isCrbtMember() : false;
                        return CpmbCombinResult.success(token, isMonth, isVrbtFun, needCrbtTehuiPack, isCrbtTehuiPack);
                    }).get();

        } catch (Exception e) {
            e.printStackTrace();
            return failResult;
        }
    }
    /**
     * 登陆接口
     *
     * @param msisdn
     * @return
     */
    public @Nonnull RemoteResult miguLoginVrbt(String msisdn, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕服务端登录=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("渠道号未配置:"+channelCode);
        }
        String miguTokenKey = MIGU_TOKEN_KEY_PREFIX+ msisdn +"_"+channelCode;
        RemoteResult cachedToken = (RemoteResult) redisUtil.get(miguTokenKey);
        if(cachedToken!=null){
            return cachedToken;
        }

        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        //登陆类型:
        //1:获取URL地址登陆
        //2:集团token登陆
        //3:秘钥登陆
        //注:登陆类型需向移动运营人员索要
        dataNode.put("loginType", "3");
        //dataNode.put("callBackUrl", "https://crbt.cdyrjygs.com/vrbt_v9/");
        //dataNode.put("miguToken", "");
        dataNode.put("key", cmsCrackConfig.getLoginSecretKey());
        dataNode.put("msisdn", msisdn);
        //dataNode.put("autoLogin", "0");
        String data = dataNode.toString();

        String miguLoginUrl = bizProperties.getMiguLoginUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(miguLoginUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                miguLoginUrl=miguLoginUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                miguLoginUrl=miguLoginUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }
        }
        final String url = bizProperties.genForwardDomainUrl(miguLoginUrl, cmsCrackConfig);
        log.info("咪咕服务端登录请求=>手机号:{},渠道号:{},请求url:{},参数data:{}",msisdn,channelCode,url,data);

        HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕服务端登录响应=>手机号:{},渠道号:{},响应:{}",msisdn,channelCode,content);

            RemoteResult result = mapper.readerFor(RemoteResult.class).withView(RemoteResult.LoginView.class).readValue(
                    content);
            if(result.isOK()){
                redisUtil.set(miguTokenKey,result, MIGU_TOKEN_CATCHE_TTL_SECONDS);
            }

            return result;

        } catch (IOException e) {
            log.warn("咪咕服务端登录=>手机号:{},渠道号:{},异常!",msisdn,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }
    /**
     * 查询视频彩铃包月状态
     * RemoteResult.status
     *  0:未订购视频彩铃包月或渠道视频彩铃包；
     *  1:已订购视频彩铃订阅包；
     *  2:已订购渠道视频彩铃包；
     *  3:已订购视频彩铃订阅包和渠道视频彩铃包
     *
     *  彩铃运营中心需要传入serviceId,返回status:0:未订购 1:已订购
     *
     *  note:彩铃中心三方支付包月状态也使用这个接口
     *
     * @param msisdn
     * @param channelCode
     * @return
     */
    public @Nonnull RemoteResult vrbtMonthStatusQuery(String msisdn, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视频彩铃包月状态=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        RemoteResult remoteResult = this.miguLoginVrbt(msisdn, channelCode);
        if(remoteResult==null||!remoteResult.isOK()){
            return RemoteResult.fail("咪咕登录失败!");
        }
        String token=remoteResult.getToken();
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        // 彩铃运营中心需要传入serviceId
        if(isCentralityChannel(channelCode)){
            dataNode.put("serviceId", cmsCrackConfig.getServiceId());
        }
        String data = dataNode.toString();
        log.info("咪咕查询视频彩铃包月状态请求=>手机号:{},渠道号:{},请求参数data:{}", msisdn, channelCode, data);
        String vrbtMonthStatusQueryUrl =bizProperties.getVrbtMonthStatusQueryUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(vrbtMonthStatusQueryUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                vrbtMonthStatusQueryUrl=vrbtMonthStatusQueryUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                vrbtMonthStatusQueryUrl=vrbtMonthStatusQueryUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtMonthStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("咪咕查询视频彩铃包月状态响应=>手机号:{},渠道号:{},响应:{}", msisdn, channelCode, content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃包月状态=>手机号:{},渠道号:{},异常!",msisdn,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }



    /**
     * 三方支付: 彩铃包月-0元固定时长订购接口 (默认订购一个月)
     *   {"resCode":"000000","resMsg":"成功"}
     *   {"resCode":"999047","resMsg":"【OPEN】不存在有效订购关系"}
     * @param msisdn
     * @return
     */
    public @Nonnull RemoteResult vrbtZeroOrder(String msisdn, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕0元订购视频彩铃包月=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", cmsCrackConfig.getServiceId());
        dataNode.put("type", "M"); //订购类型类型: M：按月订购 D：按天订购
        dataNode.put("amount", "2"); //订购时长
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        RemoteResult remoteResult = this.miguLoginVrbt(msisdn, channelCode);
        if(remoteResult==null||!remoteResult.isOK()){
            return RemoteResult.fail("咪咕登录失败!");
        }
        String token=remoteResult.getToken();
        dataNode.put("token", token);
        String data = dataNode.toString();
        log.info("咪咕0元订购视频彩铃包月请求=>手机号:{},渠道号:{},请求参数data:{}",msisdn,channelCode,data);
        String vrbtZeroOrderUrl = bizProperties.getVrbtZeroOrderUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(vrbtZeroOrderUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                vrbtZeroOrderUrl=vrbtZeroOrderUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                vrbtZeroOrderUrl=vrbtZeroOrderUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtZeroOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕0元订购视频彩铃包月响应=>手机号:{},渠道号:{},响应:{}",msisdn,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕0元订购视频彩铃包月=>手机号:{},渠道号:{},异常!",msisdn,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }




    /**
     * 视频彩铃包月内内容免费订购
     *
     * @param msisdn
     * @param channelCode
     * @param copyRightID 版权id (开放平台需要同时传contentId和版权id)
     * @param ringId  内容id[即视频彩铃产品id],(彩铃运营中心可以使用版权id或者产品id,而不需要下面的copyRightID参数)
     * @param setFlag 0不设置默认铃音 1设置默认铃音(这是老的设置参数,新参数如下)
     *           订购接口的setflag增加了枚举值，定义如下：
     *          0----内容订购后不设置为在播；------即他人看和自己看两种都没有选择
     *          1----同时设置主叫和被叫在播；------即他人看和自己看都选择了
     *          3----只设置为被叫在播； -----即用户只选择了他人看
     *          9----只设置为主叫在播。 -----即用户只选择了自己看
     * @return
     */
    public @Nonnull RemoteResult vrbtToneFreeMonthOrder(String msisdn, String channelCode, String copyRightID,String ringId,String setFlag) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕视频彩铃包月内内容免费订购=>手机号:{},渠道号:{},copyRightID:{},未配置的渠道号!",msisdn,channelCode,copyRightID);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        RemoteResult remoteResult = this.miguLoginVrbt(msisdn, channelCode);
        if(remoteResult==null||!remoteResult.isOK()){
            return RemoteResult.fail("咪咕登录失败!");
        }
        String token=remoteResult.getToken();
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        dataNode.put("serviceId", serviceId);
        //dataNode.put("serviceId", "698039049105618391");
        dataNode.put("copyRightID", copyRightID);
        //开放平台要求传入这个参数,但是内容id传固定的也可以订购成功
        dataNode.put("contentId",ringId);
        dataNode.put("setFlag", setFlag);
        String data = dataNode.toString();
        log.info("咪咕视频彩铃包月内内容免费订购请求=>mobileOrToken:{},渠道号:{},copyRightID:{},请求参数data:{}",msisdn,channelCode,copyRightID,data);

        String vrbtToneFreeMonthOrderUrl = bizProperties.getVrbtToneFreeMonthOrderUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(vrbtToneFreeMonthOrderUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                vrbtToneFreeMonthOrderUrl=vrbtToneFreeMonthOrderUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                vrbtToneFreeMonthOrderUrl=vrbtToneFreeMonthOrderUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtToneFreeMonthOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            //if (!response.isSuccessful()) {
            //    throw new IOException("Unexpected code " + response);
            //}

            String content  = response.body().string();
            log.info("咪咕视频彩铃包月内内容免费订购响应=>手机号:{},渠道号:{},copyRightID:{},响应:{}",msisdn,channelCode,copyRightID,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.ToneFreeMonthOrderView.class).readValue(content);
        } catch (IOException e) {
            log.warn("咪咕视频彩铃包月内内容免费订购=>手机号:{},渠道号:{},copyRightID:{},异常!",msisdn,channelCode,copyRightID,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }
    /**
     * 查询视频彩铃包月状态
     * RemoteResult.status
     *  0:未订购视频彩铃包月或渠道视频彩铃包；
     *  1:已订购视频彩铃订阅包；
     *  2:已订购渠道视频彩铃包；
     *  3:已订购视频彩铃订阅包和渠道视频彩铃包
     *
     *  彩铃运营中心需要传入serviceId,返回status:0:未订购 1:已订购
     *
     *  note:彩铃中心三方支付包月状态也使用这个接口
     *
     * @param mobileOrToken
     * @param isLog
     * @return
     */
    public @Nonnull RemoteResult vrbtMonthStatusQuerys(String msisdn, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视频彩铃包月状态=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        RemoteResult remoteResult = this.miguLoginVrbt(msisdn, channelCode);
        if(remoteResult==null||!remoteResult.isOK()){
            return RemoteResult.fail("咪咕登录失败!");
        }
        String token=remoteResult.getToken();
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        // 彩铃运营中心需要传入serviceId
        if(isCentralityChannel(channelCode)){
            dataNode.put("serviceId", cmsCrackConfig.getServiceId());
        }
        String data = dataNode.toString();
        log.info("咪咕查询视频彩铃包月状态请求=>手机号:{},渠道号:{},请求参数data:{}", msisdn, channelCode, data);
        String vrbtMonthStatusQueryUrl =bizProperties.getVrbtMonthStatusQueryUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(vrbtMonthStatusQueryUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                vrbtMonthStatusQueryUrl=vrbtMonthStatusQueryUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                vrbtMonthStatusQueryUrl=vrbtMonthStatusQueryUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtMonthStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕查询视频彩铃包月状态响应=>手机号:{},渠道号:{},响应:{}", msisdn, channelCode, content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃包月状态=>手机号:{},渠道号:{},异常!",msisdn,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 三方支付: 彩铃包月-0元固定时长订购接口 (默认订购一个月)
     *   {"resCode":"000000","resMsg":"成功"}
     *   {"resCode":"999047","resMsg":"【OPEN】不存在有效订购关系"}
     * @param msisdn
     * @return
     */
    public @Nonnull RemoteResult vrbtZeroOrderTask(String msisdn, String channelCode,String amount) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕0元订购视频彩铃包月=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", cmsCrackConfig.getServiceId());
        dataNode.put("type", "M"); //订购类型类型: M：按月订购 D：按天订购
        dataNode.put("amount",amount); //订购时长
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        RemoteResult remoteResult = this.miguLoginVrbt(msisdn, channelCode);
        if(remoteResult==null||!remoteResult.isOK()){
            return RemoteResult.fail("咪咕登录失败!");
        }
        String token=remoteResult.getToken();
        dataNode.put("token", token);
        String data = dataNode.toString();
        log.info("咪咕0元订购视频彩铃包月请求=>手机号:{},渠道号:{},请求参数data:{}",msisdn,channelCode,data);
        String vrbtZeroOrderUrl = bizProperties.getVrbtZeroOrderUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(vrbtZeroOrderUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                vrbtZeroOrderUrl=vrbtZeroOrderUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                vrbtZeroOrderUrl=vrbtZeroOrderUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }

        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtZeroOrderUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕0元订购视频彩铃包月响应=>手机号:{},渠道号:{},响应:{}",msisdn,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);

        } catch (IOException e) {
            log.warn("咪咕0元订购视频彩铃包月=>手机号:{},渠道号:{},异常!",msisdn,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }


    /**
     * 查询视频彩铃业务策略(暂只支持彩铃中心,查询是否支持免费订购视频彩铃内容,有无免费策略)
     *
     * @param mobileOrToken
     * @param channelCode
     * //@param contentId  内容id[即视频彩铃产品id],(彩铃运营中心可以使用版权id或者产品id,而不需要下面的copyRightID参数)
     * @param copyRightID 版权id (开放平台需要同时传contentId和版权id)
     * @return
     */
    public @Nonnull RemoteResult queryVrbtBusinessPolicy(String msisdn, String channelCode, String copyRightID,String ringId) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视频彩铃业务策略=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        RemoteResult remoteResult = this.miguLoginVrbt(msisdn, channelCode);
        if(remoteResult==null||!remoteResult.isOK()){
            return RemoteResult.fail("咪咕登录失败!");
        }
        String token=remoteResult.getToken();
        ObjectNode dataNode = mapper.createObjectNode();
        //String channelCode = bizProperties.getChannelCode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        dataNode.put("token", token);
        dataNode.put("vrbtId", copyRightID);
        dataNode.put("contentId",ringId);
        String data = dataNode.toString();
        log.info("咪咕查询视频彩铃业务策略请求=>手机号:{},渠道号:{},请求参数data:{}",msisdn,channelCode,data);
        String queryVrbtBusinessPolicyUrl = bizProperties.getQueryVrbtBusinessPolicyUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(queryVrbtBusinessPolicyUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                queryVrbtBusinessPolicyUrl=queryVrbtBusinessPolicyUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                queryVrbtBusinessPolicyUrl=queryVrbtBusinessPolicyUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(queryVrbtBusinessPolicyUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            String content  = response.body().string();
            log.info("咪咕查询视频彩铃业务策略响应=>手机号:{},渠道号:{},响应:{}",msisdn,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.ToneFreeMonthOrderView.class).readValue(content);
        } catch (IOException e) {
            log.warn("咪咕查询视频彩铃业务策略=>手机号:{},渠道号:{},copyRightID:{},异常!",msisdn,channelCode,copyRightID,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }



    /**
     * 主叫视频彩铃功能状态查询接口
     *  当返回结果为000000时，该字段不可空。
     *  {"vrbtStatus":"1","resCode":"000000","resMsg":"成功"}
     *  返回结果类型， 当返回结果为000000时，该字段不可空。
     *                    1:未开通所查询的视频彩铃功能。
     *                    2：已开通所查询的视频彩铃功能，但所对应视频彩铃播放能力处于关闭状态。
     *                    3：已开通所查询的视频彩铃功能，且所对应的视频彩铃播放能力处于开启状态。
     *
     * @param msisdn
     * @return
     */
    public @Nonnull RemoteResult vrbtInitiativeStatusQuery(String msisdn, String channelCode) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询主叫视频彩铃功能状态=>手机号:{},渠道号:{},未配置的渠道号!",msisdn,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        //final String token = this.fetchToken(mobileOrToken, channelCode);
        //if(StringUtils.isEmpty(token)){
        //    return RemoteResult.fail("咪咕登录失败!");
        //}
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        dataNode.put("productType",VRBT_FUN_PRODUCT_TYPE_ACTIVE); //0：被叫个人视频彩铃功能 1：主叫视频彩铃功能
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        RemoteResult remoteResult = this.miguLoginVrbt(msisdn, channelCode);
        if(remoteResult==null||!remoteResult.isOK()){
            return RemoteResult.fail("咪咕登录失败!");
        }
        String token=remoteResult.getToken();
        dataNode.put("token", token);
        String data = dataNode.toString();
        log.info("咪咕查询主叫视频彩铃功能状态请求=>手机号:{},渠道号:{},请求参数data:{}",msisdn,channelCode,data);
        String vrbtStatusQueryUrl =bizProperties.getVrbtInitiativeStatusQueryUrlCentrality();
        if(BizConstant.CHANNEL_ID_J7.equals(channelCode)){
            if(vrbtStatusQueryUrl.contains("https://crbt.kunpengtn.com/order/rest")){
                vrbtStatusQueryUrl=vrbtStatusQueryUrl.replace("https://crbt.kunpengtn.com/order/rest", "http://172.29.182.203:8090/order/rest");
            }else{
                vrbtStatusQueryUrl=vrbtStatusQueryUrl.replace("http://hz.migu.cn/order/rest", "http://172.29.182.203:8090/order/rest");
            }
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(vrbtStatusQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕查询主叫视频彩铃功能状态响应=>手机号:{},渠道号:{},响应:{}",msisdn,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (IOException e) {
            log.warn("咪咕查询主叫视频彩铃功能状态=>手机号:{},渠道号:{},异常!",msisdn,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 查询白金会员/音乐包/视彩号包月订购关系
     *  最早是从视彩号的接口文旦发出来的查询视彩号的订购关系,后面白金会员的接口文档发出来,支持按serviceType查询白金会员或音乐包订购关系
     * @param mobileOrToken
     * @param channelCode
     * @param serviceType  包月业务类型 1：白金会员 2：音乐包 默认为白金会员类型
     * @return {"status":"1","resCode":"000000","resMsg":"成功"}
     * status  0已订购 1未订购
     */
    public @Nonnull RemoteResult bjhyAndCpmbQuery(String mobileOrToken, String channelCode, String serviceType) {
        //兼容老的sch渠道,数智人视彩号未接口权限,先走白金会员查询接口
        //if(MiguApiService.BIZ_SCH_CHANNEL_CODE_SZR.equals(channelCode)) {
        //    return bjhyQuery(mobileOrToken,channelCode);
        //}
        final String monthType = StringUtils.isEmpty(serviceType) ? MONTH_SERVICE_TYPE_BJHY : serviceType; //包月业务类型 1：白金会员 2：音乐包 默认为白金会员类型
        final String monthTypeText = MONTH_SERVICE_TYPE_BJHY.equals(monthType) ? "白金会员/视彩号" : "音乐包";
//        MiguChannelConfig miguChannelConfig = cmsBizChannelService.getBizConfigClass(channelCode, new TypeReference<MiguChannelConfig>() {
//        });
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == cmsCrackConfig) {
            log.info("咪咕查询视彩号包月订购关系=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        String serviceId = cmsCrackConfig.getServiceId();
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("serviceId", serviceId);
        dataNode.put("serviceType", monthType);
        dataNode.put("token", token);
        String timestamp = dateTimeFormatter.format(LocalDateTime.now());
        dataNode.put("timestamp", timestamp);
        String signature = signatureService.signature(channelCode, timestamp, cmsCrackConfig.getSignatureSecretKey());
        dataNode.put("signature", signature);
        String data = dataNode.toString();
        log.info("咪咕查询{}包月订购关系请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",monthTypeText,mobileOrToken,channelCode,data);

        final String bjhyAndCpmbQueryUrl = bizProperties.getBjhyAndCpmbQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(bjhyAndCpmbQueryUrl, cmsCrackConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕查询{}包月订购关系响应=>mobileOrToken:{},渠道号:{},响应:{}",monthTypeText,mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕查询{}包月订购关系=>手机号:{},渠道号:{},异常!",monthTypeText,mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }

    /**
     * 全国/四川业务复合查询,会查询包月状态和用户归属省份,并根据省份判定是走被动还是主动视频彩铃功能开通和是否开通对应的视频彩铃功能
     * 目前只支持音乐包和藕粉联合会员
     * @param mobile
     * @return
     */
    public @Nonnull BizMixedResult bizMixedQuery(String mobile, String channelCode, String bizType) {

        final BizMixedResult failResult = BizMixedResult.fail("全国/四川业务复合查询失败");
        BizMixedResult tempResult = new BizMixedResult();

        try {
            RemoteResult loginResult = this.miguLogin(mobile, channelCode);
            if(!loginResult.isOK()){
                return failResult;
            }
            String token = loginResult.getToken();
            final CompletableFuture<Void> mobileRegionFuture = CompletableFuture.runAsync(() -> {
                MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                tempResult.setNeedCrbtTehuiPack(StringUtils.equalsAny(mobileRegionResult.getProvince(), CRBT_TEHUI_PACK_PROVINCES));
            },executor);
            final CompletableFuture<Void> monthQueryFuture = CompletableFuture.runAsync(() -> {
                if(BizConstant.BIZ_TYPE_UNION_MEMBER.equals(bizType)){
                    tempResult.setMonth(asMemberQuery(mobile, channelCode).isAsMember());
                }else if(BizConstant.BIZ_TYPE_CPMB.equals(bizType)){
                    tempResult.setMonth(cpmbQuery(mobile, channelCode).isCpmbMember());
                }
            },executor);
            final CompletableFuture<Void> activeVrbtQueryFuture = CompletableFuture.runAsync(() -> tempResult.setVrbtFun(activeVrbtQuery(mobile, channelCode).isVrbtFun()),executor);
            CompletableFuture.allOf(mobileRegionFuture,monthQueryFuture,activeVrbtQueryFuture).get();
            boolean isMonth = tempResult.isMonth();
            boolean isVrbtFunActive = tempResult.isVrbtFun();
            boolean needCrbtTehuiPack = tempResult.isNeedCrbtTehuiPack();
            //1、来了四川的号码，先判断用户是否有被叫视频彩铃功能/音频彩铃功能：
            //2、若有则用全国渠道号+2购物车订购，，，，若无则用四川渠道号调用+2购物车订购且当得知第一个包月成功后立即调用被叫视频彩铃开通功能接口
            //3、若有已经开过2音包月，建议走老渠道号
            //四川渠道号=我提到的关联了主叫的HV,全国渠道号=其他没关联主叫的渠道号HU、HT
            if(needCrbtTehuiPack){
                boolean isVrbtFunPassive  = vrbtStatusQuery(mobile, channelCode).isVrbtFun();
                boolean isCrbtFun = crbtStatusQuery(mobile, channelCode).isCrbtFun();
                boolean isCrbtTehuiPack = crbtMonthQuery(mobile, channelCode, MIXED_SICHUAN_CRBT_MONTH_SERVICE_ID).isCrbtMember();
                String finalChannel = (isVrbtFunPassive||isCrbtFun||isCrbtTehuiPack) ? channelCode : bizProperties.getMixedNewChannel(channelCode);
                String cartSecondServiceId = isCrbtTehuiPack ? null : MiguApiService.MIXED_SICHUAN_CRBT_MONTH_SERVICE_ID;
                return BizMixedResult.success(token, finalChannel, cartSecondServiceId, isMonth, isVrbtFunActive, needCrbtTehuiPack, isCrbtTehuiPack);
            }else {
                //非四川号码使用全国渠道号
                String finalChannel = channelCode;
                //根据有无主叫设置购物车附加的serviceId
                String cartSecondServiceId = isVrbtFunActive ? null : MiguApiService.MIXED_VRBT_FUN_ACTIVE_SERVICE_ID;
                return BizMixedResult.success(token,finalChannel, cartSecondServiceId, isMonth, isVrbtFunActive, needCrbtTehuiPack, false);
            }
        } catch (Exception e) {
            log.error("全国/四川业务复合查询异常",e);
            return failResult;
        }
    }

    /**
     * 音频彩铃功能查询
     *
     * @param mobileOrToken
     * @return status    彩铃状态： 0:未开通彩铃 1:已开通彩铃
     * {"status":"1","payType":"0","resCode":"000000","resMsg":"【OPEN】操作成功"}
     */
    public @Nonnull RemoteResult crbtStatusQuery(String mobileOrToken, String channelCode) {
        CmsCrackConfig miguChannelConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (null == miguChannelConfig) {
            log.info("咪咕音频彩铃功能查询=>mobileOrToken:{},渠道号:{},未配置的渠道号!",mobileOrToken,channelCode);
            return RemoteResult.fail("未配置的渠道号");
        }
        final String token = this.fetchToken(mobileOrToken, channelCode);
        if(StringUtils.isEmpty(token)){
            return RemoteResult.fail("咪咕登录失败!");
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channelCode", channelCode);
        dataNode.put("token", token);
        //dataNode.put("youCallbackName", "");
        String data = dataNode.toString();
        log.info("咪咕音频彩铃功能查询请求=>mobileOrToken:{},渠道号:{},请求参数data:{}",mobileOrToken,channelCode,data);

        final String crbtStatusQueryUrl = bizProperties.getCrbtStatusQueryUrl();
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.genForwardDomainUrl(crbtStatusQueryUrl, miguChannelConfig)).newBuilder().addQueryParameter("data", data).build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content  = response.body().string();
            log.info("咪咕音频彩铃功能查询请求响应=>mobileOrToken:{},渠道号:{},响应:{}",mobileOrToken,channelCode,content);
            return mapper.readerFor(RemoteResult.class).withView(RemoteResult.StatusQueryView.class).readValue(content);
        } catch (Exception e) {
            log.warn("咪咕音频彩铃功能查询=>手机号:{},渠道号:{},异常!",mobileOrToken,channelCode,e);
            return RemoteResult.fail("咪咕接口通信异常:"+e.getMessage());
        }
    }
}
