package com.eleven.cms.remote;

import com.eleven.cms.config.DianxinVrbtConfig;
import com.eleven.cms.config.DianxinVrbtProperties;
import com.eleven.cms.util.DianxinAuthUtils;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.DianxinResp;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.net.URLCodec;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:46
 * Desc: 电信视频彩铃相关api
 */
@Slf4j
@Service
public class DianxinVrbtService {
    @Autowired
    private DianxinVrbtProperties dianxinVrbtProperties;
    @Autowired
    private Environment environment;


    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();

        //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
        this.client = this.client.newBuilder()
                //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                .build();


        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                    .compressed()
                    .insecure()  //不检查自签名证书
                    //.connectTimeout(120)
                    //.retry(5)
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println,options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 查询包月套餐是否存在，即查询用户订购了包月计费产品
     * NOTE: 此接口使用GET方式请求,其余接口大都是POST
     *
     * @param mdn 手机号
     *            {"UserPackageListResp":{"res_code":"0004","res_message":"无相关包月套餐列表","mdn":***********}}
     *            //{"UserPackageListResp":{"res_code":"0000","res_message":"查询包月套餐列表成功","user_package_list":{"user_package":{"package_id":"135999999999999000021","order_time":"2020-11-26 12:06:03","unsubscribe_time":"","status":0,"valid_time":"2060-12-31 23:59:59"}},"mdn":***********}}
     *            {"code":"0000","message":"成功","data":[{"package_id":"135999999999999000032","order_time":"2021-05-06 14:13:00","unsubscribe_time":"","valid_time":"2060-12-31 23:59:59","status":0}]}
     *            //退订后再查询   套餐状态, 0未退订, 1等待, 2退订
     *            {"UserPackageListResp":{"res_code":"0000","res_message":"查询包月套餐列表成功","user_package_list":{"user_package":{"package_id":"135999999999999000021","order_time":"2020-11-26 12:06:03","unsubscribe_time":"2020-11-26 12:18:59","status":2,"valid_time":"2060-12-31 23:59:59"}},"mdn":***********}}
     */
    public boolean queryPackageExist(String mdn,String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();
        HttpUrl httpUrl = HttpUrl.parse(dianxinVrbtProperties.getQueryPackageListUrl())
                .newBuilder()
                .addQueryParameter("mdn", mdn)
                .addQueryParameter("package_id", packageId)
//                                 .addQueryParameter("is_count_down_num", "0")  //新版本去掉该参数
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-查询是否已包月请求-手机号:{},packageId:{}", dianxinVrbtConfig.getLogTag(), mdn, packageId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询是否已包月响应-手机号:{},packageId:{},内容:{}", dianxinVrbtConfig.getLogTag(), mdn, packageId,content);
            JsonNode tree = mapper.readTree(content);
            //如果没有这个值,视作退订(未订购)
//            int status = tree.at("/UserPackageListResp/user_package_list/user_package/status").asInt(2);
            if(!tree.get("code").asText().equals("0000")){
                return false;
            }
            int status = tree.at("/data/0/status").asInt(2);
            return status == 0;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-查询是否已包月-手机号:{},packageId:{},异常:", dianxinVrbtConfig.getLogTag(), mdn, packageId, e);
            return false;
        }

    }

    /**
     * 包月产品退订
     *
     * @param mdn 手机号
     *            {"BasicJTResponse":{"res_code":0,"res_message":"退订成功"}}
     *            //再次退订返回:
     *            {"BasicJTResponse":{"res_code":0,"res_message":"重复退订!"}}
     *            //没有包月会返回:
     *            {"BasicJTResponse":{"res_code":82,"res_message":"产品不存在"}}
     */
    public String unSubscribeByemp(String mdn,String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();
        RequestBody formBody = new FormBody.Builder()
                .add("mdn", mdn)
                .add("package_id", packageId)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getUnSubscribeByempUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-包月产品退订请求-手机号:{},packageId:{}", dianxinVrbtConfig.getLogTag(), mdn, packageId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-包月产品退订响应-手机号:{},packageId:{},内容:{}", dianxinVrbtConfig.getLogTag(), mdn, packageId, content);
            //return mapper.readerFor(DianxinResp.class).withRootName("BasicJTResponse").readValue(content);
            return content;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-包月产品退订-手机号:{},异常:", dianxinVrbtConfig.getLogTag(), mdn, e);
            return "包月产品退订异常";
        }

    }

    /**
     * 一键开通视频彩铃订购包月产品(音乐盒代计费)验证码下发,3分钟内有效,此时间内反复调用会多次下发相同的验证码
     * 此接口不会校验要订购的包月是否存在,已订购也会继续下发短信
     *
     * @param phoneNumber 手机号
     *                    {"res_message":"成功","res_code":"0000"}
     */
    public DianxinResp asyncOpenOrderSendRandom(String phoneNumber,String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();
        RequestBody formBody = new FormBody.Builder()
                .add("phoneNumber", phoneNumber)
                .add("packageId", packageId)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getAsyncOpenOrderSendRandomUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-一键开通验证码下发请求-手机号:{},packageId:{},响应:{}", dianxinVrbtConfig.getLogTag(), phoneNumber, packageId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-一键开通验证码下发响应-手机号:{},packageId:{},内容:{}", dianxinVrbtConfig.getLogTag(), phoneNumber, packageId, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-一键开通验证码下发-手机号:{},packageId:{},异常:", dianxinVrbtConfig.getLogTag(), phoneNumber, packageId, e);
            return DianxinResp.fail();
        }
    }

    /**
     * 一键开通视频彩铃订购包月产品(音乐盒代计费)下单
     * 此接口不会校验要订购的包月是否存在,已订购也会返回下单成功
     *
     * @param phoneNumber 手机号
     *                    {"res_message":"未配置视频彩铃开户类型","res_code":"0037"}
     *                    {"res_message":"验证码错误","res_code":"0013"}
     *                    {"order_no":"20201126120439612006","res_message":"成功","res_code":"0000"}
     *                    {"order_no":"20201126121414611930","res_message":"成功","res_code":"0000"}
     */
    public DianxinResp asyncOpenAccountOrderPackageBycrbt(String phoneNumber, String randomKey,String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();
        RequestBody formBody = new FormBody.Builder()
                .add("phoneNumber", phoneNumber)
                .add("packageId", packageId)
                .add("randomKey", randomKey)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getAsyncOpenAccountOrderPackageBycrbtUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-一键开通下单请求-手机号:{},packageId:{}", dianxinVrbtConfig.getLogTag(), phoneNumber,packageId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-一键开通下单响应-手机号:{},packageId:{},内容:{}", dianxinVrbtConfig.getLogTag(), phoneNumber,packageId, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-一键开通下单-手机号:{},packageId:{},异常:", dianxinVrbtConfig.getLogTag(), phoneNumber, packageId, e);
            return DianxinResp.fail();
        }

    }

    /**
     * 视频彩铃一键接口订单详情查询
     *
     * @param orderNo 一键下单返回的订单号
     *                {"order_no":"20201126120439612006","res_message":"成功","open_vrbt_code":"300000","open_vrbt_flag":1,"open_vrbt_desc":"success","mdn":"***********","order_package_desc":"成功","package_id":"135999999999999000021","state":2,"order_package_flag":1,"order_package_code":"0","res_code":"0000"}
     *                已订购包月再次订购会返回:
     *                {"order_no":"20201126121414611930","res_message":"成功","open_vrbt_code":"9001","open_vrbt_flag":1,"open_vrbt_desc":"用户已开户","mdn":"***********","order_package_desc":"用户已订购产品","package_id":"135999999999999000021","state":2,"order_package_flag":1,"order_package_code":"9001","res_code":"0000"}
     */
    public String queryOrder(String orderNo,String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("orderNo", orderNo)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getQueryOrderUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-一键开通订单查询穹丘-orderNo:{}", dianxinVrbtConfig.getLogTag(), orderNo);

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-一键开通订单查询响应-orderNo:{},内容:{}", dianxinVrbtConfig.getLogTag(), orderNo, content);
            return content;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-一键开通订单查询-orderNo:{},异常:", dianxinVrbtConfig.getLogTag(), orderNo, e);
            return "一键开通订单查询异常";
        }
    }

    /**
     * 使用包月产品权益免费订购视频铃音
     * 在订购了合作方APP所属的视频彩铃合作包后，用户可以0元的价格订购该合作方预先入库到彩铃平台中的铃音。订购成功后将自动为用户设置为默认铃音。
     *
     * @param phoneNumber 手机号
     * @param toneCode    铃音编码
     *                    <p>
     *                    {"res_message":"成功","packageId":"135444444444444000001,135999999999999000052,135999999999999000021","res_code":"0"}
     *                    //已订购包月再次订购会返回:
     *                    {"res_message":"该铃音或铃音盒已经存在","packageId":"135444444444444000001,135999999999999000052,135999999999999000021","res_code":"302001"}
     *                    //铃音没有绑定到包月产品返回:
     *                    {"res_message":"该铃音没有对应的包月产品","res_code":"400003"}
     *                    //不是测试号码
     *                    {"res_message":"查询结果为空","res_code":"13804"}
     *                    //被关闭了视频彩铃功能
     *                    {"res_message":"用户不存在","res_code":"107206"}
     */
    public @Nonnull
    DianxinResp addToneFreeOnProduct(String phoneNumber, String toneCode,String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("phoneNumber", phoneNumber)
                .add("toneCode", toneCode)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getAddToneFreeOnProductUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-使用包月免费订购视频铃音请求-手机号:{},toneCode:{}", dianxinVrbtConfig.getLogTag(), phoneNumber, toneCode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-使用包月免费订购视频铃音响应-手机号:{},toneCode:{},内容:{}", dianxinVrbtConfig.getLogTag(), phoneNumber, toneCode, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-使用包月免费订购视频铃音-手机号:{},toneCode:{},异常:", dianxinVrbtConfig.getLogTag(), phoneNumber, toneCode, e);
            return DianxinResp.fail();
        }
    }

    /**
     * 视频彩铃信息查询接口
     *
     * @param resourceId 可以根据彩铃 ID 或者资源 ID 查询到视频彩铃的详细信息。
     */
    public String videoQuery(String resourceId,String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("resourceId", resourceId)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getVideoQueryUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-视频彩铃信息查询请求-resourceId:{}", dianxinVrbtConfig.getLogTag(), resourceId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-视频彩铃信息查询响应-resourceId:{},内容:{}", dianxinVrbtConfig.getLogTag(), resourceId, content);
            return content;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-视频彩铃信息查询-resourceId:{},异常:", dianxinVrbtConfig.getLogTag(), resourceId, e);
            return "视频彩铃信息查询异常";
        }
    }

    /**
     * 根据专题 ID 查询相应的视频合成模板内容
     */
    public String queryTemplateVideo(String topicId, String node, String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("topicId", topicId)
                .add("node", node)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getQueryTemplateVideoUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-视频合成模板内容查询请求-topicId:{}", dianxinVrbtConfig.getLogTag(), topicId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-视频合成模板内容查询响应-topicId:{},内容:{}", dianxinVrbtConfig.getLogTag(), topicId, content);
            return content;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-视频合成模板内容查询-topicId:{},异常:", dianxinVrbtConfig.getLogTag(), topicId, e);
            return "视频合成模板内容查询异常";
        }
    }

    /**
     * 获取 CMS 模板合成以及设置 H5 页面的跳转地址，用于给用户完成视频合成以及设置流程
     */
    public String getAiTemplateUrl(String productId, String templateId, String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("productId", productId)
                .add("templateId", templateId)
                .add("return_url", dianxinVrbtConfig.getReturnUrl())
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getGetAiTemplateUrlUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-获取 CMS AI 模板链接地址请求-productId:{},templateId:{}", dianxinVrbtConfig.getLogTag(), productId, templateId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取 CMS AI 模板链接地址响应-productId:{},templateId:{},内容:{}", dianxinVrbtConfig.getLogTag(), productId, templateId, content);
            return content;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-获取 CMS AI 模板链接地址-productId:{},templateId:{},异常:", dianxinVrbtConfig.getLogTag(), productId, templateId, e);
            return "获取 CMS AI 模板链接地址异常";
        }
    }

    /**
     * H5计费下单发起接口(已废弃)
     *
     * @param mobile     手机号
     * @param returnUrl  计费认证页面操作后的返回地址
     * @param verifyType 校验类型 0：免密登录 1：短信验证码
     * @param isMessage  是否下发订购通知短信 1：是
     * @return
     */
    //@Deprecated
    //public DianxinResp confirmOrderLaunchedEx(String mobile, String returnUrl, String verifyType, String isMessage,String company) {
    //    DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
    //    String packageId = dianxinVrbtConfig.getPackageId();
    //    RequestBody formBody = new FormBody.Builder()
    //            .add("mobile", mobile)
    //            .add("product_id", packageId)
    //            .add("return_url", returnUrl)
    //            .add("verify_type", verifyType)
    //            .add("column", "0")
    //            .add("remark", "0")
    //            .add("is_message", isMessage)
    //            .build();
    //
    //    Request request = new Request.Builder()
    //            .url(dianxinVrbtProperties.getConfirmOrderLaunchedExUrl())
    //            .post(formBody)
    //            .build();
    //
    //    request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
    //    log.info("{}-使用H5计费下单发起请求-手机号:{},packageId:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId);
    //    try (Response response = client.newCall(request).execute()) {
    //        if (!response.isSuccessful()) {
    //            throw new IOException("Unexpected code " + response);
    //        }
    //        String content = response.body().string();
    //        log.info("{}-使用H5计费下单发起响应-手机号:{},packageId:{},内容:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId, content);
    //        return mapper.readValue(content, DianxinResp.class);
    //    } catch (IOException e) {
    //        //e.printStackTrace();
    //        log.info("{}-使用 H5计费下单发起接口-手机号:{},packageId:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, packageId, e);
    //        return DianxinResp.fail();
    //    }
    //
    //
    //}

    /**
     * H5 计费下单发起接口（一键开户订购）
     * (下单接口带半个小时缓存,避免重复下单导致接口次数的浪费)
     *
     * @param mobile
     *         手机号
     * @param isMessage
     *         是否下发订购通知短信 1：是
     * @param returnUrl
     * @return {"order_no":"965089be4dfe40b68d004a3e72df146c","res_message":"成功","res_code":"0","fee_url":"https://m.imusic.cn/paycenter/#/openauth/confirm?order_no=965089be4dfe40b68d004a3e72df146c"}
     */
    @Cacheable(cacheNames = CacheConstant.CMS_DIANXIN_VRBT_API_CACHE,key = "#root.methodName + '-' + #company + '-' + #mobile",unless = "#result.resCode != '0000'")
    public @Nonnull DianxinResp confirmOpenOrderLaunchedEx(String mobile, String isMessage, String returnUrl, String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();
        RequestBody formBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("product_id", packageId)
                .add("return_url", StringUtils.firstNonEmpty(returnUrl,dianxinVrbtConfig.getReturnUrl()))
                .add("verify_type", "1")
                .add("column", "0")
                .add("remark", "0")
                .add("is_message", isMessage)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getConfirmOpenOrderLaunchedExUrl())
                .post(formBody)
                .build();
        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-使用H5计费下单发起请求-手机号:{},packageId:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-使用H5计费下单发起响应-手机号:{},packageId:{},content:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId,content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-使用 H5计费下单发起接口-手机号:{},packageId:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, packageId, e);
            return DianxinResp.fail();
        }


    }

    /**
     * H5 计费下单发起接口（EMP 计费）
     *
     * @param mobile     手机号
     * @param returnUrl  计费认证页面操作后的返回地址
     * @param isMessage  是否下发订购通知短信 1：是
     * @return
     */
    public DianxinResp confirmOrderLaunchedEmp(String mobile, String returnUrl, String isMessage,String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();

        RequestBody formBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("product_id", packageId)
                .add("return_url", returnUrl)
                .add("column", "0")
                .add("remark", "0")
                .add("is_message", isMessage)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getConfirmOrderLaunchedEmpUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-使用H5计费下单发起请求-手机号:{},packageId:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-使用H5计费下单发起响应-手机号:{},packageId:{},响应:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-使用 H5计费下单发起接口-手机号:{},packageId:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, packageId, e);
            return DianxinResp.fail();
        }


    }

    /**
     * h5 订单查询接口
     *
     * @param mobile
     * @param orderNo
     * @return
     */
    public DianxinResp queryH5Order(String mobile, String orderNo, String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        HttpUrl httpUrl = HttpUrl.parse(dianxinVrbtProperties.getQueryH5OrderUrl())
                .newBuilder()
                .addQueryParameter("order_no", orderNo)
                .addQueryParameter("mdn", mobile)
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-使用H5订单查询请求-手机号:{},订单号:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-使用H5订单查询响应-手机号:{},订单号:{},内容:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-使用H5订单查询接口-手机号:{},单号:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, orderNo, e);
            return DianxinResp.fail();
        }
    }

    //##########################以下为三方支付新加的接口###################################

    /**
     * 用户信息查询  查询用户的信息，可通过该接口，判断用户是否开通了视频彩铃功能
     *  {"res_message":"成功","phoneNumber":"***********","userStatus":1,"chargeType":0,"ringStatus":1,"orderStatus":"3","openTime":"2023-05-29 00:00:00","res_code":"0","lastUpdateTime":"2023-05-29 00:00:00"}
     * @param mobile
     * @return
     */
    public String queryAccountInfo(String mobile,String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("phoneNumber", mobile)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getQueryAccountInfoUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-用户信息查询请求-手机号:{}", dianxinVrbtConfig.getLogTag(), mobile);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-用户信息查询响应-手机号:{},内容:{}", dianxinVrbtConfig.getLogTag(), mobile, content);
            return content;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-用户信息查询-手机号:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, e);
            return null;
        }
    }

    /**
     * H5 计费下单发起接口（第三方校验） 调用该接口生成订单（第三方校验专用）返回订单号、风控状态、产品订购关系、非彩 铃功能用户（含功能费信息）等。
     * {"order_no":"e0e71cffe6c6479d9c5b347bbaeda80f","res_message":"成功","res_code":"0","url":"This api has not fee url","is_open":1}
     * {"res_code":"10131","res_message":"订购关系已存在","success":false}
     * @param mobile
     * @return
     */
    public DianxinResp ismpOrderLaunchedThird(String mobile,String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();
        RequestBody formBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("product_id", packageId)
                .add("referrer", "https://crbt.cdyrjygs.com/")
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getIsmpOrderLaunchedThirdUrl())
                .post(formBody)
                .build();

        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-H5计费下单发起（第三方校验）生成订单请求-手机号:{}", dianxinVrbtConfig.getLogTag(), mobile);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-H5计费下单发起（第三方校验）生成订单响应-手机号:{},订单号:{}", dianxinVrbtConfig.getLogTag(), mobile, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-H5计费下单发起（第三方校验）生成订单-手机号:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, e);
            return DianxinResp.fail();
        }
    }

    /**
     * H5 计费验证码下发接口 调用该接口发送 H5 计费验证码。
     *   {"res_code":"0","res_message":"成功","success":true}
     * @param orderNo
     * @param mobile
     * @return
     */
    public DianxinResp ismpVerifyCodeSend(String mobile, String orderNo, String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("order_no", orderNo)
                .add("channel_id", dianxinVrbtConfig.getChannelId())
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getIsmpVerifyCodeSendUrl())
                .post(formBody)
                .build();
        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-H5计费鉴权验证码下发请求-手机号:{},订单号:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-H5计费验鉴权证码下发响应-手机号:{},订单号:{},内容:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-H5计费鉴权验证码下发-手机号:{},订单号:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, orderNo, e);
            return DianxinResp.fail();
        }
    }

    /**
     * H5计费订单确认接口 调用该接口确定 H5 计费订单，回填验证码 验证码鉴权
     *   {"res_code":"89","res_message":"验证码已失效","success":false}
     *   {"delayed":false,"res_code":"0","res_message":"成功"}
     * @param orderNo
     * @param mobile
     * @return
     */
    public DianxinResp ismpConfirmOrder(String mobile, String orderNo, String verifyCode, String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("order_no", orderNo)
                .add("verify_code", verifyCode)
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getIsmpConfirmOrderUrl())
                .post(formBody)
                .build();
        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-H5计费订单确认-验证码鉴权请求-手机号:{},订单号:{},验证码:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo,verifyCode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-H5计费订单确认-验证码鉴权响应-手机号:{},订单号:{},验证码:{},内容:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo, verifyCode, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-H5计费订单确认-验证码鉴权-手机号:{},订单号:{},验证码:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, orderNo, verifyCode, e);
            return DianxinResp.fail();
        }
    }

    /**
     * H5 计费支付确认接口（第三方校验） 订单支付确认，视频彩铃功能开户+0元视频彩铃合作包订购（给权益）
     *  {"delayed":false,"res_code":"0","res_message":"成功"}
     *  {"res_code":"0012","res_message":"不满足订单状态条件"} //未走短信验证
     * @param mobile
     * @param orderNo
     * @return
     */
    public DianxinResp ismpConfirmOrderThird(String mobile,String orderNo,String thirdPayNo,String company) {

        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        RequestBody formBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("order_no", orderNo)
                .add("third_pay_code", "0") // 第三方支付返回码  0-成功
                .add("third_pay_message", "支付成功")  // 第三方支付结果描述 非必传 不传会说签名错误
                .add("third_pay_no", thirdPayNo)  //  第三方支付订单号 非必传  不传会说签名错误
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getIsmpConfirmOrderThirdUrl())
                .post(formBody)
                .build();
        request = DianxinAuthUtils.generateSign(request,dianxinVrbtConfig);
        log.info("{}-H5计费支付确认（第三方校验）订单支付确认-给权益请求-手机号:{},订单号:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-H5计费支付确认（第三方校验）订单支付确认-给权益响应-手机号:{},订单号:{},内容:{}", dianxinVrbtConfig.getLogTag(), mobile, orderNo, content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-H5计费支付确认（第三方校验）订单支付确认-给权益-手机号:{},-单号:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, orderNo, e);
            return DianxinResp.fail();
        }
    }








    public static void main(String[] args) throws JsonProcessingException, DecoderException, UnsupportedEncodingException, ClassNotFoundException {
        String raw = "{\"UserPackageListResp\":{\"res_code\":\"0004\",\"res_message\":\"无相关包月套餐列表\",\"mdn\":***********}}";
        //String raw = "{\"UserPackageListResp\":{\"res_code\":\"0000\",\"res_message\":\"查询包月套餐列表成功\",\"user_package_list\":{\"user_package\":{\"package_id\":\"135999999999999000021\",\"order_time\":\"2020-11-26 12:06:03\",\"unsubscribe_time\":\"2020-11-26 12:18:59\",\"status\":2,\"valid_time\":\"2060-12-31 23:59:59\"}},\"mdn\":***********}}";
        final ObjectMapper mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        final JsonNode tree = mapper.readTree(raw);
        final String resCode = tree.at("/UserPackageListResp/res_code").asText();

        final String notExistsPathText = tree.at("/not_exists/not_exists/not_exists").asText();


        final String notExistsText = tree.at("/UserPackageListResp/not_exists").asText();
        final String notExistsTextWithDefault = tree.at("/UserPackageListResp/not_exists").asText(null);
        final int notExistsInteger = tree.at("/UserPackageListResp/not_exists").asInt();
        final int notExistsIntegerWithDefault = tree.at("/UserPackageListResp/not_exists").asInt(0);

        System.out.println("resCode = " + resCode);
        //路径全部不存在
        System.out.println("notExistsPathText = " + notExistsPathText);
        //不存在就返回空串
        System.out.println("notExistsText = " + notExistsText);
        //返回指定的默认值
        System.out.println("notExistsTextWithDefault = " + notExistsTextWithDefault);
        //不存在就返回0
        System.out.println("notExistsInteger = " + notExistsInteger);
        //返回指定的默认值
        System.out.println("notExistsIntegerWithDefault = " + notExistsIntegerWithDefault);


        //final Class<?> dianxinRespClass = Class.forName("com.eleven.cms.vo.DianxinResp");
        //System.out.println(dianxinRespClass);
        //final Object resp = mapper.readerFor(dianxinRespClass)
        //                                         .withRootName("UserPackageListResp")
        //                                         .readValue(raw);
        //System.out.println(resp);
        //final DianxinResp resp = mapper.readerFor(dianxinRespClass)
        //                               .withRootName("UserPackageListResp")
        //                               .readValue(raw);

        //忽略根节点
        final DianxinResp resp = mapper.readerFor(DianxinResp.class)
                .withRootName("UserPackageListResp")
                .readValue(raw);
        System.out.println("resp = " + resp);

        System.out.println(URLDecoder.decode("2020-11-26+14%3A57%3A43", StandardCharsets.UTF_8.name()));
        System.out.println(URLDecoder.decode("2020-11-26 14:57:43", StandardCharsets.UTF_8.name()));

        System.out.println(new URLCodec().decode("2020-11-26+14%3A57%3A43", StandardCharsets.UTF_8.name()));
        System.out.println(new URLCodec().decode("2020-11-26 14:57:43", StandardCharsets.UTF_8.name()));


    }

    /**
     * H5 计费下单发起接口（一键开户订购）
     * (下单接口带半个小时缓存,避免重复下单导致接口次数的浪费)
     *
     * @param mobile
     *         手机号
     * @param isMessage
     *         是否下发订购通知短信 1：是
     * @param returnUrl
     * @return {"order_no":"965089be4dfe40b68d004a3e72df146c","res_message":"成功","res_code":"0","fee_url":"https://m.imusic.cn/paycenter/#/openauth/confirm?order_no=965089be4dfe40b68d004a3e72df146c"}
     */
    @Cacheable(cacheNames = CacheConstant.CMS_DIANXIN_VRBT_API_CACHE,key = "#root.methodName + '-' + #company + '-' + #mobile",unless = "#result.resCode != '0000'")
    public @Nonnull DianxinResp newConfirmOpenOrderLaunchedEx(String mobile, String isMessage, String returnUrl, String company) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(company);
        String packageId = dianxinVrbtConfig.getPackageId();
        RequestBody formBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("product_id", packageId)
                .add("return_url", StringUtils.firstNonEmpty(returnUrl,dianxinVrbtConfig.getReturnUrl()))
                .add("verify_type", "1")
                .add("column", "0")
                .add("remark", "0")
                .add("is_message", isMessage)
                .add("ring_id", dianxinVrbtConfig.getRingId())
                .build();

        Request request = new Request.Builder()
                .url(dianxinVrbtProperties.getConfirmOpenOrderLaunchedExUrl())
                .post(formBody)
                .build();
        request = DianxinAuthUtils.newGenerateSign(request,dianxinVrbtConfig);
        log.info("{}-使用H5计费下单发起请求-手机号:{},packageId:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-使用H5计费下单发起响应-手机号:{},packageId:{},content:{}", dianxinVrbtConfig.getLogTag(), mobile, packageId,content);
            return mapper.readValue(content, DianxinResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-使用 H5计费下单发起接口-手机号:{},packageId:{},异常:", dianxinVrbtConfig.getLogTag(), mobile, packageId, e);
            return DianxinResp.fail();
        }


    }
}
