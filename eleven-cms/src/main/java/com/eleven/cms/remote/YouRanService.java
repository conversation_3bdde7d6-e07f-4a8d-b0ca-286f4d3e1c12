package com.eleven.cms.remote;

import com.eleven.cms.config.YouRanProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HenanMobileResult;
import com.eleven.cms.vo.YouRanResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 悠然接口
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/12 11:45
 **/
@Slf4j
@Service
public class YouRanService {
    private OkHttpClient client;
    private ObjectMapper mapper;


    public static final String LOG_TAG = "悠然API";

    @Autowired
    private YouRanProperties youRanProperties;
    @Autowired
    private Environment environment;

    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }



    public YouRanResult getSms(String mobile, String channel, String userAgent, String appPackage, String ip) {
        RequestBody formBody = new FormBody.Builder().add("mobile", mobile)
                    .add("subChannel", youRanProperties.getChannelCodeMap().get(channel))
                    .add("userAgent", StringUtils.isNotBlank(userAgent)?userAgent:"")
                    .add("appPackage",StringUtils.isNotBlank(appPackage)?appPackage:"")
                    .add("ip",ip )
                    .build();

        log.info("{}-获取短信-手机号:{},渠道号:{},请求:{}", LOG_TAG, mobile, channel, youRanProperties.getGetSmsCodeUrl());
        Request request = new Request.Builder().url(youRanProperties.getGetSmsCodeUrl()).post(formBody).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            YouRanResult youRanResult = mapper.readValue(content, YouRanResult.class);
            log.info("{}-获取短信-手机号:{},渠道号:{},响应:{}", LOG_TAG, mobile, channel, youRanResult);
            return youRanResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},渠道号:{},异常:", LOG_TAG, mobile, channel, e);
            return YouRanResult.FAIL_RESULT;
        }
    }

    public YouRanResult smsCode(String mobile, String channel, String orderNo, String code) {
        RequestBody formBody = new FormBody.Builder()
                            .add("mobile", mobile)
                            .add("code", code)
                            .add("orderNo",orderNo)
                            .add("subChannel", youRanProperties.getChannelCodeMap().get(channel))
                            .build();
        log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},请求:{}", LOG_TAG, mobile, code, channel, youRanProperties.getSmsCodeUrl());
        Request request = new Request.Builder().url(youRanProperties.getSmsCodeUrl()).post(formBody).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            YouRanResult youRanResult = mapper.readValue(content, YouRanResult.class);
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},响应:{}", LOG_TAG, mobile, code, channel,youRanResult);
            return youRanResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},异常:", LOG_TAG, mobile, code, channel, e);
            return YouRanResult.FAIL_RESULT;
        }
    }
}
