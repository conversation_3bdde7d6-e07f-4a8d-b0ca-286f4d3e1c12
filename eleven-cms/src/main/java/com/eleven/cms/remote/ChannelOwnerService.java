package com.eleven.cms.remote;

import com.eleven.cms.config.BlackListProperties;
import com.eleven.cms.config.ChannelOwnerProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-01-19 15:00
 */
@Slf4j
@Service
public class ChannelOwnerService {

    private static final String LOG_TAG = "查询子渠道归属";
    private OkHttpClient client;
    private ObjectMapper mapper;

    @Autowired
    private ChannelOwnerProperties channelOwnerProperties;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 查询归属
     *
     * @param subChannel
     * @return xiaohong为向红 xiaofeng峰
     */

    @Cacheable(cacheNames = CacheConstant.CMS_SUBCHANNEL_OWNER_CACHE,key = "#root.methodName + ':' + #p0", condition = "#p0!=null",unless = "#result==null")
    public String queryOwner(String subChannel) {
        final HttpUrl httpUrl = HttpUrl.parse(channelOwnerProperties.getQueryUrl())
                .newBuilder()
                .addQueryParameter("subChannel", subChannel)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            return result;
        } catch (Exception e) {
            log.error("{}-查询子渠道号归属-子渠道号:{},异常:", LOG_TAG, subChannel, e);
            return "";
        }
    }
}
