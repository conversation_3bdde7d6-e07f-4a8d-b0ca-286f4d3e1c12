package com.eleven.cms.remote;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.config.OtherRecharge;
import com.eleven.cms.entity.AliSignChargingOrder;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.queue.PayNotifyDelayedMessage;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.queue.RefundNotifyDelayedMessage;
import com.eleven.cms.queue.UnSignNotifyDelayedMessage;
import com.eleven.cms.service.IAliSignChargingOrderService;
import com.eleven.cms.service.IAliSignRecordService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 长沙支付宝支付订单同步
 */
@Slf4j
@Service
public class ChangShaAliPayNotifyService {
    public static final String DELIMITER_AMP = "&";
    public static final String CHANGSHA_ALIPAY_NOTIFY_URL ="https://open-api.ucdaili.com/api/notice/open-order/sync?sign=";
    public static final String MESSAG_EXTRA_30_MIN = "30_MIN";
    @Autowired
    private Environment environment;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    private JunboApiProperties junboApiProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON= MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 长沙支付宝解约通知
     * @param externalAgreementNo
     * @return
     */
    public void unSignNotify(String externalAgreementNo,String businessType,Boolean isCreateQueue) {
        if(BizConstant.ROAD_ONE_LIST.contains(businessType)){
            AliSignRecord order =aliSignRecordService.lambdaQuery().eq(AliSignRecord::getSignStatus, 3).eq(AliSignRecord::getExternalAgreementNo,externalAgreementNo).eq(AliSignRecord::getBusinessType,businessType).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
            try {
                if(order==null){
                    log.error("长沙支付宝解约通知签约号错误-->签约号:{},业务类型:{},是否创建队列:{}",externalAgreementNo,businessType,isCreateQueue);
                    return;
                }
                Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(order), Map.class);
                final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("csgmapp");
                if(otherRecharge==null){
                    log.error("长沙支付宝解约通知渠道号错误-->渠道号:{},签约号:{},业务类型:{},是否创建队列:{}","csgmapp",externalAgreementNo,businessType,isCreateQueue);
                    return;
                }
                String sign=getSign(map,otherRecharge);
                String content=this.implementHttpPostResult(CHANGSHA_ALIPAY_NOTIFY_URL+sign,map,"长沙支付宝支付订单同步,解约通知");
                if(StringUtils.isEmpty(content) && isCreateQueue){
                    //队列
                    this.addUnSignNotifyDelay( externalAgreementNo, businessType);
                    return;
                }
                final ObjectNode objectNode = mapper.readValue(content, ObjectNode.class);
                if(!objectNode.has("Code") && isCreateQueue){
                    //队列
                    this.addUnSignNotifyDelay( externalAgreementNo, businessType);
                    return;
                }
                String code = objectNode.get("Code").asText();
                if(!StringUtils.equals(code,"0")  && isCreateQueue){
                    //队列
                    this.addUnSignNotifyDelay( externalAgreementNo, businessType);
                    return;
                }
            } catch (JsonProcessingException e) {
                e.printStackTrace();
                log.error("长沙支付宝解约通知异常-->签约号:{},业务类型:{},是否创建队列:{}",externalAgreementNo,businessType,isCreateQueue,e);
            }

        }

    }

    /**
     * 长沙支付宝支付通知
     *
     * @param orderId
     * @return
     */
    public void payNotify(String orderId,String businessType,Boolean isCreateQueue) {
        if(BizConstant.ROAD_ONE_LIST.contains(businessType)){
            AliSignChargingOrder order = aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getOrderStatus, 1).eq(AliSignChargingOrder::getOrderNo,orderId).eq(AliSignChargingOrder::getBusinessType,businessType).orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
            try {
                if(order==null){
                    log.error("长沙支付宝支付通知订单号错误-->订单号:{},业务类型:{},是否创建队列:{}",orderId,businessType,isCreateQueue);
                    return;
                }
                Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(order), Map.class);
                final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("csgmapp");
                if(otherRecharge==null){
                    log.error("长沙支付宝支付通知渠道号错误-->渠道号:{},订单号:{},业务类型:{},是否创建队列:{}","csgmapp",orderId,businessType,isCreateQueue);
                    return;
                }
                String sign=getSign(map,otherRecharge);
                String content=this.implementHttpPostResult(CHANGSHA_ALIPAY_NOTIFY_URL+sign,map,"长沙支付宝支付订单同步,支付通知");
                if(StringUtils.isEmpty(content)  && isCreateQueue){
                    //队列
                    this.addPayNotifyDelay(orderId,businessType);
                    return;
                }
                final ObjectNode objectNode = mapper.readValue(content, ObjectNode.class);
                if(!objectNode.has("Code") && isCreateQueue){
                    //队列
                    this.addPayNotifyDelay(orderId,businessType);
                    return;
                }
                String code = objectNode.get("Code").asText();
                if(!StringUtils.equals(code,"0")  && isCreateQueue){
                    //队列
                    this.addPayNotifyDelay(orderId,businessType);
                    return;
                }
            } catch (JsonProcessingException e) {
                e.printStackTrace();
                log.error("长沙支付宝支付通知异常-->订单号:{},业务类型:{},是否创建队列:{}",orderId,businessType,isCreateQueue,e);
            }
        }
    }

    /**
     * 长沙支付宝退款通知
     *
     * @param orderId
     * @return
     */
    public void refundNotify(String orderId,String businessType,Boolean isCreateQueue) {
        if(BizConstant.ROAD_ONE_LIST.contains(businessType)){
            AliSignChargingOrder order = aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getOrderStatus, 1).eq(AliSignChargingOrder::getRefundStatus, 1).eq(AliSignChargingOrder::getOrderNo,orderId).eq(AliSignChargingOrder::getBusinessType,businessType).orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
            try {
                if(order==null){
                    log.error("长沙支付宝退款通知订单号错误-->订单号:{},业务类型:{},是否创建队列:{}",orderId,businessType,isCreateQueue);
                    return;
                }
                Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(order), Map.class);
                final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("csgmapp");
                if(otherRecharge==null){
                    log.error("长沙支付宝退款通知渠道号错误-->渠道号:{},订单号:{},业务类型:{},是否创建队列:{}","csgmapp",orderId,businessType,isCreateQueue);
                    return;
                }
                String sign=getSign(map,otherRecharge);
                String content=this.implementHttpPostResult(CHANGSHA_ALIPAY_NOTIFY_URL+sign,map,"长沙支付宝支付订单同步,退款通知");
                if(StringUtils.isEmpty(content)  && isCreateQueue){
                    //队列
                    this.addRefundNotifyDelay( orderId, businessType);
                    return;
                }
                final ObjectNode objectNode = mapper.readValue(content, ObjectNode.class);
                if(!objectNode.has("Code") && isCreateQueue){
                    //队列
                    this.addRefundNotifyDelay(orderId,businessType);
                    return;
                }
                String code = objectNode.get("Code").asText();
                if(!StringUtils.equals(code,"0")  && isCreateQueue){
                    //队列
                    this.addRefundNotifyDelay( orderId, businessType);
                    return;
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("长沙支付宝退款通知异常-->订单号:{},业务类型:{},是否创建队列:{}",orderId,businessType,isCreateQueue,e);
            }
        }

    }


    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url, Object fromValue,String msg) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg);
    }
    public String push(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }


    /**
     * 生成签名
     * @param dataMap
     * @param otherRecharge
     * @return
     */
    private String getSign(Map<String, Object> dataMap, OtherRecharge otherRecharge) {
        dataMap = dataMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,(oleValue, newValue) -> oleValue, LinkedHashMap::new));
        String parameterStr = dataMap.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining(DELIMITER_AMP));
        parameterStr += DELIMITER_AMP+"key=" + otherRecharge.getKey();
        try {
            String sign = DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8.name()));
            log.info("支付宝权益充值,加密参数:{},密钥:{}",parameterStr, sign);
            return sign;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.error("支付宝权益充值,加密参数:{}",parameterStr,e);
        }
        return null;
    }

    /**
     * 添加支付宝支付通知延迟任务
     * @param orderNo
     * @param businessType
     */
    public void addPayNotifyDelay(String orderNo,String businessType) {
        log.info("添加支付宝支付通知延迟任务==>订单号:{},业务类型:{}", orderNo,businessType);
        //延迟队列
        redisDelayedQueueManager.addPayNotify(PayNotifyDelayedMessage.builder().orderId(orderNo).businessType(businessType).msg("支付宝支付通知").extra(MESSAG_EXTRA_30_MIN).build(), 30, TimeUnit.MINUTES);
    }


    /**
     * 添加支付宝退款通知延迟任务
     * @param orderNo
     * @param businessType
     */
    public void addRefundNotifyDelay(String orderNo,String businessType) {
        log.info("添加支付宝退款通知延迟任务==>订单号:{},业务类型:{}", orderNo,businessType);
        //延迟队列
        redisDelayedQueueManager.addRefundNotify(RefundNotifyDelayedMessage.builder().orderId(orderNo).businessType(businessType).msg("支付宝退款通知").extra(MESSAG_EXTRA_30_MIN).build(), 30, TimeUnit.MINUTES);

    }

    /**
     * 添加支付宝解约通知延迟任务
     * @param externalAgreementNo
     * @param businessType
     */
    public void addUnSignNotifyDelay(String externalAgreementNo,String businessType) {
        log.info("添加支付宝解约通知延迟任务==>签约号:{},业务类型:{}", externalAgreementNo,businessType);
        //延迟队列
        redisDelayedQueueManager.addUnSignNotify(UnSignNotifyDelayedMessage.builder().externalAgreementNo(externalAgreementNo).businessType(businessType).msg("支付宝解约通知").extra(MESSAG_EXTRA_30_MIN).build(), 30, TimeUnit.MINUTES);
    }

}
