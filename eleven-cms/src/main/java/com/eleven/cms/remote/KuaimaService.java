package com.eleven.cms.remote;

import com.eleven.cms.config.KuaimaProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.KuaimaResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class KuaimaService {

    public static final String LOG_TAG = "快马四川移动业务api";

    @Autowired
    KuaimaProperties kuaimaProperties;

    @Autowired
    private Environment environment;
    @Autowired
    private Interceptor kuaimaYidongIntercept;

    private OkHttpClient client;

    private ObjectMapper mapper;


    private static final MediaType mediaType = MediaType.parse("application/json");


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public KuaimaResult getSms(String phone, String bizCode) {

        String url = kuaimaProperties.getGetSmsUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("mobile", phone);
        dataMap.put("productCode", bizCode);
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-随机码下发-手机号:{},业务编码:{},请求:{}", LOG_TAG, phone, bizCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},业务编码:{},返回结果:{}", LOG_TAG, phone, bizCode, result);
            return mapper.readValue(result, KuaimaResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-随机码下发-手机号:{},业务编码:{},异常:", LOG_TAG, phone, bizCode, e);
            return KuaimaResult.FAIL_RESULT;

        }
    }

    public KuaimaResult smsCode(String phone, String bizCode, String outTradeNo, String smsCode) {

        String url = kuaimaProperties.getSmsCodeUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("mobile", phone);
        dataMap.put("productCode", bizCode);
        dataMap.put("outTradeNo", outTradeNo);
        dataMap.put("smsCode", smsCode);
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-提交验证码-手机号:{},业务编码:{},短信验证码:{},请求:{}", LOG_TAG, phone, bizCode,smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},业务编码:{},返回结果:{}", LOG_TAG, phone, bizCode, result);
            return mapper.readValue(result, KuaimaResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-提交验证码-手机号:{},业务编码:{},异常:", LOG_TAG, phone, bizCode, e);
            return KuaimaResult.FAIL_RESULT;

        }
    }

    public KuaimaResult prepareOrder(String phone, String bizCode) throws Exception {
        String url = kuaimaProperties.getPrepareOrderUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("phone", phone);
        dataMap.put("channelNo", kuaimaProperties.getChannelNo());
        dataMap.put("product", bizCode);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-预下单-手机号:{},业务编码:{},请求:{}", LOG_TAG, phone, bizCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-预下单-手机号:{},业务编码:{},返回结果:{}", LOG_TAG, phone, bizCode, result);
            return mapper.readValue(result, KuaimaResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-预下单-手机号:{},业务编码:{},异常:", LOG_TAG, phone, bizCode, e);
            return KuaimaResult.FAIL_RESULT;
        }
    }





}
