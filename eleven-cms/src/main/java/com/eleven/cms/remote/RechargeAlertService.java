package com.eleven.cms.remote;

import com.eleven.cms.config.MobileMap;
import com.eleven.cms.config.NoticeProperties;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

/**
 * 定义常用的 Redis操作
 *
 * <AUTHOR>
 */
@Service
public class RechargeAlertService {

    @Autowired
    private NoticeProperties noticeProperties;
    //骏伯每分钟充值频率
    private static final Integer JUNBO_EVERY_MINUTE_COUNT=60;
    //骏伯每日充值总量
    private static final Integer JUNBO_EVERY_DAY_COUNT=1500;

    //每分钟秒数
    private static final Integer EVERY_MINUTE_SECOND_COUNT=60;
    //每日秒数
    private static final Integer EVERY_DAY_SECOND_COUNT=60 * 60 * 24;
    //过期时间
    private static final Long EXPIRE_TIME=30L * 60L;
    //饿了么每分钟充值频率
    private static final Integer ELEME_EVERY_MINUTE_COUNT=100;
    //饿了么每日充值总量
    private static final Integer ELEME_EVERY_DAY_COUNT=5000;

    //白金会员每分钟充值频率
    private static final Integer BJHY_EVERY_MINUTE_COUNT=60;
    //白金会员每日充值总量
    private static final Integer BJHY_EVERY_DAY_COUNT=1000;
    //白金会员和视频彩铃单个手机号每日充值总量
    private static final Integer BJHY_VRBT_EVERY_DAY_COUNT_ACCOUNT = 5;

    //酷狗直充每分钟充值频率
    private static final Integer KGZC_EVERY_MINUTE_COUNT=60;
    //酷狗直充每日充值总量
    private static final Integer KGZC_EVERY_DAY_COUNT=1000;

    //咪咕视频彩铃每分钟充值频率
    private static final Integer MGSPCL_EVERY_MINUTE_COUNT=60;
    //咪咕视频彩铃每日充值总量
    private static final Integer MGSPCL_EVERY_DAY_COUNT=1000;

    //微信代金券每分钟充值频率
    private static final Integer WXDJQ_EVERY_MINUTE_COUNT=60;
    //微信代金券每日充值总量
    private static final Integer WXDJQ_EVERY_DAY_COUNT=1000;
    //华逸每分钟充值频率
    private static final Integer HUAYI_EVERY_MINUTE_COUNT=60;
    //华逸每日充值总量
    private static final Integer HUAYI_EVERY_DAY_COUNT=1000;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IDatangSmsService datangSmsService;


    public static void main(String[] args) {
        Long dailyExpire = ChronoUnit.HOURS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        System.out.println("dailyExpire = " + dailyExpire);
        dailyExpire = ChronoUnit.MINUTES.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        System.out.println("dailyExpire = " + dailyExpire);
        dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        System.out.println("dailyExpire = " + dailyExpire);
    }



    /**
     * 骏伯充值告警
     * @param serviceId
     * @param account
     */
    public void watchJUNBORecharge(String account, String serviceId, String packName) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::junbo::vrbt:" + serviceId + "-" + account+ "-" + packName;
        String mobileDailyWarnRedisKey = "alertCounter::mobileDaily::warn::junbo::vrbt:" + serviceId + "-" + account+ "-" + packName;
        //每分钟预警key
        String minuteRedisKey = "alertCounter::minute::junbo:vrbt";
        String minuteWarnRedisKey = "alertCounter::minute::warn::junbo:vrbt";
        String dailyRedisKey = "alertCounter::daily::junbo:vrbt";
        String dailyWarnRedisKey = "alertCounter::daily::warn::junbo:vrbt";

        String mobileWarnSmsMsg = "【爱休闲集社】手机号：%s骏伯权益每日充值告警";
        String minuteSmsMsg = "【爱休闲集社】每分钟骏伯权益充值%s次数超出告警";
        String dailySmsMsg = "【爱休闲集社】每天骏伯权益充值%s次数超出告警";

        //每个手机号每天的充值频率告警(联合会员同一手机号一个自然天超过一次直充;集运权益采购同一手机号订单号同一自然天超过一次直充)
        if (!redisUtil.isExpired(mobileDailyCounterRedisKey, dailyExpire) && redisUtil.isExpired(mobileDailyWarnRedisKey, dailyExpire)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(mobileWarnSmsMsg, account));
            }
        }
        //每分钟充值频率告警
        if (redisUtil.rateLimit(minuteRedisKey, JUNBO_EVERY_MINUTE_COUNT, EVERY_MINUTE_SECOND_COUNT) && redisUtil.isExpired(minuteWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(minuteSmsMsg, JUNBO_EVERY_MINUTE_COUNT));
            }
            redisUtil.del(minuteRedisKey);
        }
        //每日充值总量告警
        if (redisUtil.rateLimit(dailyRedisKey, JUNBO_EVERY_DAY_COUNT, EVERY_DAY_SECOND_COUNT) && redisUtil.isExpired(dailyWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(dailySmsMsg, JUNBO_EVERY_DAY_COUNT));
            }
            redisUtil.del(dailyRedisKey);
        }
    }



    /**
     * 券码充值告警
     * @param serviceId
     * @param account
     */
    public void watchElemeRecharge(String account, String serviceId, String packName) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::eleme::vrbt:" + serviceId + "-" + account+ "-" + packName;
        String mobileDailyWarnRedisKey = "alertCounter::mobileDaily::warn::eleme::vrbt:" + serviceId + "-" + account+ "-" + packName;
        //每分钟预警key
        String minuteRedisKey = "alertCounter::minute::eleme:vrbt";
        String minuteWarnRedisKey = "alertCounter::minute::warn::eleme:vrbt";
        String dailyRedisKey = "alertCounter::daily::eleme:vrbt";
        String dailyWarnRedisKey = "alertCounter::daily::warn::eleme:vrbt";

        String mobileWarnSmsMsg = "【爱休闲集社】券码手机号：%s权益每日充值告警";
        String minuteSmsMsg = "【爱休闲集社】券码每分钟权益充值%s次数超出告警";
        String dailySmsMsg = "【爱休闲集社】券码每天权益充值%s次数超出告警";

        //每个手机号每天的充值频率告警(联合会员同一手机号一个自然天超过一次直充;集运权益采购同一手机号订单号同一自然天超过一次直充)
        if (!redisUtil.isExpired(mobileDailyCounterRedisKey, dailyExpire) && redisUtil.isExpired(mobileDailyWarnRedisKey, dailyExpire)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(mobileWarnSmsMsg, account));
            }
        }
        //每分钟充值频率告警
        if (redisUtil.rateLimit(minuteRedisKey, ELEME_EVERY_MINUTE_COUNT, EVERY_MINUTE_SECOND_COUNT) && redisUtil.isExpired(minuteWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(minuteSmsMsg, ELEME_EVERY_MINUTE_COUNT));
            }
            redisUtil.del(minuteRedisKey);
        }
        //每日充值总量告警
        if (redisUtil.rateLimit(dailyRedisKey, ELEME_EVERY_DAY_COUNT, EVERY_DAY_SECOND_COUNT) && redisUtil.isExpired(dailyWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(dailySmsMsg, ELEME_EVERY_DAY_COUNT));
            }
            redisUtil.del(dailyRedisKey);
        }
    }


    /**
     * 白金会员充值告警
     * @param serviceId
     * @param account
     */
    public void watchBjhyRecharge(String account, String serviceId, String packName) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::bjhy::vrbt:" + serviceId + "-" + account+ "-" + packName;
        String mobileDailyWarnRedisKey = "alertCounter::mobileDaily::warn::bjhy::vrbt:" + serviceId + "-" + account+ "-" + packName;
        //每分钟预警key
        String minuteRedisKey = "alertCounter::minute::bjhy:vrbt";
        String minuteWarnRedisKey = "alertCounter::minute::warn::bjhy:vrbt";
        String dailyRedisKey = "alertCounter::daily::bjhy:vrbt";
        String dailyWarnRedisKey = "alertCounter::daily::warn::bjhy:vrbt";

        String mobileWarnSmsMsg = "【爱休闲集社】白金会员权益手机号：%s权益每日充值告警";
        String minuteSmsMsg = "【爱休闲集社】白金会员权益每分钟权益充值%s次数超出告警";
        String dailySmsMsg = "【爱休闲集社】白金会员权益每天权益充值%s次数超出告警";

        //每个手机号每天的充值频率告警(联合会员同一手机号一个自然天超过一次直充;集运权益采购同一手机号订单号同一自然天超过一次直充)
        if (!redisUtil.isExpired(mobileDailyCounterRedisKey, dailyExpire) && redisUtil.isExpired(mobileDailyWarnRedisKey, dailyExpire)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(mobileWarnSmsMsg, account));
            }
        }
        //每分钟充值频率告警
        if (redisUtil.rateLimit(minuteRedisKey, BJHY_EVERY_MINUTE_COUNT, EVERY_MINUTE_SECOND_COUNT) && redisUtil.isExpired(minuteWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(minuteSmsMsg, BJHY_EVERY_MINUTE_COUNT));
            }
            redisUtil.del(minuteRedisKey);
        }
        //每日充值总量告警
        if (redisUtil.rateLimit(dailyRedisKey, BJHY_EVERY_DAY_COUNT, EVERY_DAY_SECOND_COUNT) && redisUtil.isExpired(dailyWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(dailySmsMsg, BJHY_EVERY_DAY_COUNT));
            }
            redisUtil.del(dailyRedisKey);
        }
    }


    /**
     * 白金会员和视频彩铃包月充值限制
     * @param serviceId
     * @param account
     */
    public FebsResponse bjhyVrbtRechargeLimit(String account, String serviceId, String packName, String channelId, String day) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::bjhy::vrbt:" + serviceId + "-" + account+ "-" + packName+ "-" + channelId+ "-" + day;
        final long count = redisUtil.incr(mobileDailyCounterRedisKey, 1L);
        redisUtil.expire(mobileDailyCounterRedisKey, dailyExpire);
        if(count>BJHY_VRBT_EVERY_DAY_COUNT_ACCOUNT){
            //超出后回退多余的1次
            redisUtil.decr(mobileDailyCounterRedisKey, 1L);
            return new FebsResponse().fail().message("充值号码充值次数超过当日限制次数");
        }
        return new FebsResponse().success();
    }



    /**
     * 酷狗直充充值告警
     * @param serviceId
     * @param account
     */
    public void watchKGZCRecharge(String account, String serviceId, String packName) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::kgzc::vrbt:" + serviceId + "-" + account+ "-" + packName;
        String mobileDailyWarnRedisKey = "alertCounter::mobileDaily::warn::kgzc::vrbt:" + serviceId + "-" + account+ "-" + packName;
        //每分钟预警key
        String minuteRedisKey = "alertCounter::minute::kgzc:vrbt";
        String minuteWarnRedisKey = "alertCounter::minute::warn::kgzc:vrbt";
        String dailyRedisKey = "alertCounter::daily::kgzc:vrbt";
        String dailyWarnRedisKey = "alertCounter::daily::warn::kgzc:vrbt";

        String mobileWarnSmsMsg = "【爱休闲集社】酷狗直充权益手机号：%s权益每日充值告警";
        String minuteSmsMsg = "【爱休闲集社】酷狗直充权益每分钟权益充值%s次数超出告警";
        String dailySmsMsg = "【爱休闲集社】酷狗直充权益每天权益充值%s次数超出告警";

        //每个手机号每天的充值频率告警(联合会员同一手机号一个自然天超过一次直充;集运权益采购同一手机号订单号同一自然天超过一次直充)
        if (!redisUtil.isExpired(mobileDailyCounterRedisKey, dailyExpire) && redisUtil.isExpired(mobileDailyWarnRedisKey, dailyExpire)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(mobileWarnSmsMsg, account));
            }
        }
        //每分钟充值频率告警
        if (redisUtil.rateLimit(minuteRedisKey, KGZC_EVERY_MINUTE_COUNT, EVERY_MINUTE_SECOND_COUNT) && redisUtil.isExpired(minuteWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(minuteSmsMsg, KGZC_EVERY_MINUTE_COUNT));
            }
            redisUtil.del(minuteRedisKey);
        }
        //每日充值总量告警
        if (redisUtil.rateLimit(dailyRedisKey, KGZC_EVERY_DAY_COUNT, EVERY_DAY_SECOND_COUNT) && redisUtil.isExpired(dailyWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(dailySmsMsg, KGZC_EVERY_DAY_COUNT));
            }
            redisUtil.del(dailyRedisKey);
        }
    }

    /**
     * 支付宝业务预警
     * @param businessType
     * @param businessName
     * @param isSwitch
     */
    public void alipayCacheWarn(String businessType,String businessName,String isSwitch) {
        String alipayCacheWarnRedisKey = "alipay::cache::warn:" + businessType;
        String alipaySwitchCacheWarnRedisKey = "alipay::cache::warn::switch:" + businessType;
        String smsMsg = "【"+businessName+"】30分钟未入单告警,渠道号:"+businessType;
        String mobileRedisKey = "alipay::cache::warn::mobile" + businessType;
        //过期了
        if(!redisUtil.hasKey(alipayCacheWarnRedisKey) && !redisUtil.hasKey(mobileRedisKey) && redisUtil.hasKey(alipaySwitchCacheWarnRedisKey) && isSwitch.equals(String.valueOf(redisUtil.get(alipaySwitchCacheWarnRedisKey)))){
            redisUtil.set(mobileRedisKey,mobileRedisKey, BizConstant.ALIPAY_TIME);
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileAlipayMap().entrySet()) {
                String mobile=entry.getValue().getMobile();
                datangSmsService.sendSms(mobile, smsMsg);
            }

        }
    }

    /**
     * 咪咕视频彩铃充值告警
     * @param serviceId
     * @param account
     */
    public void watchMiGuShipCaiLingRecharge(String account, String serviceId, String packName) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::bjhy::vrbt:" + serviceId + "-" + account+ "-" + packName;
        String mobileDailyWarnRedisKey = "alertCounter::mobileDaily::warn::bjhy::vrbt:" + serviceId + "-" + account+ "-" + packName;
        //每分钟预警key
        String minuteRedisKey = "alertCounter::minute::bjhy:vrbt";
        String minuteWarnRedisKey = "alertCounter::minute::warn::bjhy:vrbt";
        String dailyRedisKey = "alertCounter::daily::bjhy:vrbt";
        String dailyWarnRedisKey = "alertCounter::daily::warn::bjhy:vrbt";

        String mobileWarnSmsMsg = "【爱休闲集社】咪咕视频彩铃会员权益手机号：%s权益每日充值告警";
        String minuteSmsMsg = "【爱休闲集社】咪咕视频彩铃权益每分钟权益充值%s次数超出告警";
        String dailySmsMsg = "【爱休闲集社】咪咕视频彩铃权益每天权益充值%s次数超出告警";

        //每个手机号每天的充值频率告警(联合会员同一手机号一个自然天超过一次直充;集运权益采购同一手机号订单号同一自然天超过一次直充)
        if (!redisUtil.isExpired(mobileDailyCounterRedisKey, dailyExpire) && redisUtil.isExpired(mobileDailyWarnRedisKey, dailyExpire)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(mobileWarnSmsMsg, account));
            }
        }
        //每分钟充值频率告警
        if (redisUtil.rateLimit(minuteRedisKey, MGSPCL_EVERY_MINUTE_COUNT, EVERY_MINUTE_SECOND_COUNT) && redisUtil.isExpired(minuteWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(minuteSmsMsg, MGSPCL_EVERY_MINUTE_COUNT));
            }
            redisUtil.del(minuteRedisKey);
        }
        //每日充值总量告警
        if (redisUtil.rateLimit(dailyRedisKey, MGSPCL_EVERY_DAY_COUNT, EVERY_DAY_SECOND_COUNT) && redisUtil.isExpired(dailyWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(dailySmsMsg, MGSPCL_EVERY_DAY_COUNT));
            }
            redisUtil.del(dailyRedisKey);
        }
    }


    /**
     * 微信代金券充值告警
     * @param serviceId
     * @param account
     */
    public void watchWechatCouponCodeRecharge(String account, String serviceId, String packName) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::bjhy::vrbt:" + serviceId + "-" + account+ "-" + packName;
        String mobileDailyWarnRedisKey = "alertCounter::mobileDaily::warn::bjhy::vrbt:" + serviceId + "-" + account+ "-" + packName;
        //每分钟预警key
        String minuteRedisKey = "alertCounter::minute::bjhy:vrbt";
        String minuteWarnRedisKey = "alertCounter::minute::warn::bjhy:vrbt";
        String dailyRedisKey = "alertCounter::daily::bjhy:vrbt";
        String dailyWarnRedisKey = "alertCounter::daily::warn::bjhy:vrbt";

        String mobileWarnSmsMsg = "【爱休闲集社】微信代金券会员权益手机号：%s权益每日充值告警";
        String minuteSmsMsg = "【爱休闲集社】微信代金券权益每分钟权益充值%s次数超出告警";
        String dailySmsMsg = "【爱休闲集社】微信代金券权益每天权益充值%s次数超出告警";

        //每个手机号每天的充值频率告警(联合会员同一手机号一个自然天超过一次直充;集运权益采购同一手机号订单号同一自然天超过一次直充)
        if (!redisUtil.isExpired(mobileDailyCounterRedisKey, dailyExpire) && redisUtil.isExpired(mobileDailyWarnRedisKey, dailyExpire)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(mobileWarnSmsMsg, account));
            }
        }
        //每分钟充值频率告警
        if (redisUtil.rateLimit(minuteRedisKey, WXDJQ_EVERY_MINUTE_COUNT, EVERY_MINUTE_SECOND_COUNT) && redisUtil.isExpired(minuteWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(minuteSmsMsg, WXDJQ_EVERY_MINUTE_COUNT));
            }
            redisUtil.del(minuteRedisKey);
        }
        //每日充值总量告警
        if (redisUtil.rateLimit(dailyRedisKey, WXDJQ_EVERY_DAY_COUNT, EVERY_DAY_SECOND_COUNT) && redisUtil.isExpired(dailyWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(dailySmsMsg, WXDJQ_EVERY_DAY_COUNT));
            }
            redisUtil.del(dailyRedisKey);
        }
    }

    /**
     * 华逸充值告警
     * @param serviceId
     * @param account
     */
    public void watchHuaYiRecharge(String account, String serviceId, String packName) {
        Long dailyExpire = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDate.now().atTime(LocalTime.MAX));
        String mobileDailyCounterRedisKey = "alertCounter::mobileDaily::junbo::vrbt:" + serviceId + "-" + account+ "-" + packName;
        String mobileDailyWarnRedisKey = "alertCounter::mobileDaily::warn::junbo::vrbt:" + serviceId + "-" + account+ "-" + packName;
        //每分钟预警key
        String minuteRedisKey = "alertCounter::minute::junbo:vrbt";
        String minuteWarnRedisKey = "alertCounter::minute::warn::junbo:vrbt";
        String dailyRedisKey = "alertCounter::daily::junbo:vrbt";
        String dailyWarnRedisKey = "alertCounter::daily::warn::junbo:vrbt";

        String mobileWarnSmsMsg = "【爱休闲集社】手机号：%s华逸权益每日充值告警";
        String minuteSmsMsg = "【爱休闲集社】每分钟华逸权益充值%s次数超出告警";
        String dailySmsMsg = "【爱休闲集社】每天华逸权益充值%s次数超出告警";

        //每个手机号每天的充值频率告警(联合会员同一手机号一个自然天超过一次直充;集运权益采购同一手机号订单号同一自然天超过一次直充)
        if (!redisUtil.isExpired(mobileDailyCounterRedisKey, dailyExpire) && redisUtil.isExpired(mobileDailyWarnRedisKey, dailyExpire)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(mobileWarnSmsMsg, account));
            }
        }
        //每分钟充值频率告警
        if (redisUtil.rateLimit(minuteRedisKey, HUAYI_EVERY_MINUTE_COUNT, EVERY_MINUTE_SECOND_COUNT) && redisUtil.isExpired(minuteWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(minuteSmsMsg, HUAYI_EVERY_MINUTE_COUNT));
            }
            redisUtil.del(minuteRedisKey);
        }
        //每日充值总量告警
        if (redisUtil.rateLimit(dailyRedisKey, HUAYI_EVERY_DAY_COUNT, EVERY_DAY_SECOND_COUNT) && redisUtil.isExpired(dailyWarnRedisKey, EXPIRE_TIME)) {
            for (Map.Entry<String, MobileMap> entry : noticeProperties.getMobileMap().entrySet()) {
                datangSmsService.sendSms(entry.getValue().getMobile(), String.format(dailySmsMsg, HUAYI_EVERY_DAY_COUNT));
            }
            redisUtil.del(dailyRedisKey);
        }
    }
}
