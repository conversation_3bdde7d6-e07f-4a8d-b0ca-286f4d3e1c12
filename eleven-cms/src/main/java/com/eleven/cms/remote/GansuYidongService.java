package com.eleven.cms.remote;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.GansuYidongProperties;
import com.eleven.cms.config.GuangxiYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.GansuMobileResult;
import com.eleven.cms.vo.GuangxiMobileResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class GansuYidongService {

    public static final String LOG_TAG = "甘肃移动流量包业务api";
    @Autowired
    GansuYidongProperties gansuYidongProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;
    public static final String DELIMITER_AMP = "&";
    private static final MediaType mediaType = MediaType.parse("application/json");


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public GansuMobileResult getSms(String phone) throws Exception {
        String url = gansuYidongProperties.getRequestUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("header", getHeader("gs.activity.sms.v2"));
        dataMap.put("body", getSmsBody(phone));
        generateSign(dataMap);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-随机码下发-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, GansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return GansuMobileResult.fail();

        }
    }

    public GansuMobileResult smsCode(String phone, String smsCode,String identifyingKey) throws Exception {
        String url = gansuYidongProperties.getRequestUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("header", getHeader("gs.activity.order"));
        dataMap.put("body", smsCodeBody(phone, smsCode,identifyingKey));
        generateSign(dataMap);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-提交验证码-手机号:{},短信验证码:{},请求:{}", LOG_TAG, phone, smsCode,request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},短信验证码:{},返回结果:{}", LOG_TAG, phone,smsCode, result);
            return mapper.readValue(result, GansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},短信验证码:{},异常:", LOG_TAG, phone,smsCode, e);
            return GansuMobileResult.fail();

        }
    }

    private Map<String, Object> getHeader(String businessCode) {
        Map<String, Object> headerMap = new TreeMap<>();
        headerMap.put("productCode", gansuYidongProperties.getProductCode());
        headerMap.put("businessCode", businessCode);
        headerMap.put("sequence", IdWorker.get32UUID());
        headerMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        headerMap.put("appid", gansuYidongProperties.getAppId());
        return headerMap;
    }

    private String getSmsBody(String mobile) throws JsonProcessingException {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("mobile", mobile);
        bodyMap.put("busiType", gansuYidongProperties.getBusiType());
        bodyMap.put("offerCode", gansuYidongProperties.getOfferCode());
        return mapper.writeValueAsString(bodyMap);
    }

    private String smsCodeBody(String mobile, String smsCode,String identifyingKey) throws JsonProcessingException {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("mobile", mobile);
        bodyMap.put("offerCode", gansuYidongProperties.getOfferCode());
        bodyMap.put("busiCode", gansuYidongProperties.getBusiCode());
        bodyMap.put("actType", gansuYidongProperties.getActType());
        bodyMap.put("sms", smsCode);
        bodyMap.put("identifyingKey", identifyingKey);
        return mapper.writeValueAsString(bodyMap);
    }


    private void generateSign(Map<String, Object> dataMap) throws Exception {
        Map<String, Object> headerMap = (TreeMap<String, Object>) dataMap.get("header");
        headerMap.put("body", dataMap.get("body"));
        String paramStr = headerMap.entrySet().stream().map(key -> key.toString()).collect(Collectors.joining(DELIMITER_AMP));
        String sign = SecureUtil.hmacSha256(gansuYidongProperties.getSecret()).digestHex(MD5.create().digest(paramStr, StandardCharsets.UTF_8));
        headerMap.remove("body");
        headerMap.put("sign", sign);
    }

}
