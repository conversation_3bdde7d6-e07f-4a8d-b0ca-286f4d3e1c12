package com.eleven.cms.remote;

import com.eleven.cms.config.DianxinVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.DianxinResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Streams;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.DecoderException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:46
 * Desc: 电信官方视频彩铃订购
 */
@Slf4j
@Service
public class DianxinVrbtOfficialService {
    private static final String LOG_TAG = "电信官方视频彩铃api";
    private static final String CHANNEL_ID = "7370";
    private static final String AUTH_PREFIX = "Bearer ";
    @Autowired
    private Environment environment;


    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;

    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        //if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
        //    this.client = this.client.newBuilder()
        //            .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("**************", 9999)))
        //            .build();
        //}
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                    .compressed()
                    .insecure()  //不检查自签名证书
                    //.connectTimeout(120)
                    //.retry(5)
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println, options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }



    /**
     * 包月产品退订
     *
     * @param mdn 手机号
     *            
     */
    public String unSubscribeByemp(String mdn) {
        //RequestBody formBody = new FormBody.Builder()
        //        .add("mdn", mdn)
        //        .add("package_id", dianxinVrbtProperties.getPackageId())
        //        .build();
        //
        //Request request = new Request.Builder()
        //        .url(dianxinVrbtProperties.getUnSubscribeByempUrl())
        //        .post(formBody)
        //        .build();
        //
        //try (Response response = client.newCall(request).execute()) {
        //    if (!response.isSuccessful()) {
        //        throw new IOException("Unexpected code " + response);
        //    }
        //    String content = response.body().string();
        //    log.info("{}-包月产品退订-手机号:{},响应:{}", LOG_TAG, mdn, content);
        //    //return mapper.readerFor(DianxinResp.class).withRootName("BasicJTResponse").readValue(content);
        //    return content;
        //} catch (IOException e) {
        //    //e.printStackTrace();
        //    log.info("{}-包月产品退订-手机号:{},异常:", LOG_TAG, mdn, e);
        //    return "包月产品退订异常";
        //}
       return null;
    }

    /**
     *  下发短信验证码
     *
     * @param mobile 手机号
     *   { "desc": "成功", "code": "0000", "resp": null, "result": "0" }
     *    { "desc": null, "code": null, "resp": null, "result": "0" } ip受限
     */
    @Nonnull
    public DianxinResult sendTemplateSms(String mobile) {
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url("https://m.imusic.cn/vapi/vue_login/send_templatesms?channelId="+CHANNEL_ID+"&portal=45&mobile="+mobile+"&templateId=91001941&sendSmsOrder=0")
                .method("POST", body)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", "0")
                .addHeader("Access-Control-Allow-Origin", "*")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Access-Control-Allow-Credentials", "true")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36")
                .addHeader("Origin", "https://m.imusic.cn")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Referer", "https://m.imusic.cn/h5v/vmusic/loginPhone")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-下发短信验证码-手机号:{},响应:{}", LOG_TAG, mobile, content);
            return mapper.readValue(content, DianxinResult.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-下发短信验证码-手机号:{},异常:", LOG_TAG, mobile, e);
            return DianxinResult.fail();
        }
    }

    /**
     *  一键订购
     *
     * @param mobile 手机号
     */
    @Nonnull
    public DianxinResult onKeyOrderPackage(String mobile,String validCode) {
        //短信登录
        DianxinResult dianxinResult = this.matchValidCode(mobile, validCode);
        //包含短信验证码错误
        if(!dianxinResult.isOK()){
           return dianxinResult;
        }
        String tmpToken = dianxinResult.getResult();
        //token兑换authorization
        dianxinResult = this.getUserInfo(mobile, tmpToken);
        if(!dianxinResult.isOK()){
            return dianxinResult;
        }
        String authorization = dianxinResult.getResult();
        return this.orderBoxLoadIsmp(mobile, authorization);
    }

    /**
     *  短信验证码登录获取token
     *
     * @param mobile 手机号
     *  {
     *     "loginType": "2",
     *     "backUrl": "https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081713",
     *     "code": "0000",
     *     "result": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
     * }
     *  {"loginType":"2","backUrl":"https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711","code":"0001","desc":"验证码不匹配"}
     */
    @Nonnull
    public DianxinResult matchValidCode(String mobile,String validCode) {
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url("https://m.imusic.cn/vapi/vue_login/match_validcode?channelId="+CHANNEL_ID+"&portal=45&mobile="+mobile+"&validCode="+validCode+"&backUrl=https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711&loginType=2")
                .method("POST", body)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", "0")
                .addHeader("Access-Control-Allow-Origin", "*")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Access-Control-Allow-Credentials", "true")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36")
                .addHeader("Origin", "https://m.imusic.cn")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Referer", "https://m.imusic.cn/h5v/vmusic/loginPhone")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                //.addHeader("Cookie", "JSESSIONID=B84BD64054677899A13F34613EBE6E32; user118100cn=8fa51b6caaae8800733bd5c1fc0735605ba81d313b4c2d01f54a7c9c2304690820f016f7ecc14fa2778e5c4b2251d54389fe2a1316c78f08e7876f25602d3030")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-短信验证码登录获取token-手机号:{},响应:{}", LOG_TAG, mobile, content);
            return mapper.readValue(content, DianxinResult.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-短信验证码登录获取token-手机号:{},异常:", LOG_TAG, mobile, e);
            return DianxinResult.fail();
        }
    }

    /**
     *  token兑换Authoritarian,放在响应header的Authorization字段
     *
     * @param mobile 手机号
     *  {
     *     "loginType": "2",
     *     "backUrl": "https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081713",
     *     "code": "0000",
     *     "result": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
     * }
     *  {"loginType":"2","backUrl":"https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711","code":"0001","desc":"验证码不匹配"}
     */
    @Nonnull
    public DianxinResult getUserInfo(String mobile,String tmpToken) {
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url("https://m.imusic.cn/vapi/new_member/get_user_info?channelId="+CHANNEL_ID+"&portal=45&mobile="+mobile)
                .method("POST", body)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", "0")
                .addHeader("Access-Control-Allow-Origin", "*")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Access-Control-Allow-Credentials", "true")
                .addHeader("Authorization", AUTH_PREFIX +tmpToken)
                .addHeader("sec-ch-ua-mobile", "?1")
                .addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36 Edg/91.0.864.54")
                .addHeader("sec-ch-ua", "\" Not;A Brand\";v=\"99\", \"Microsoft Edge\";v=\"91\", \"Chromium\";v=\"91\"")
                .addHeader("Origin", "https://m.imusic.cn")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Referer", "https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                //.addHeader("Cookie", "cc=7370; 3sWez06c8ptAO=5owCKLd9aR1ryDmJsSn1_e8j9aatbI8MDYq_2aJbRJkZ02xzNxgwdSxxJmunVDnBw3i_cJxG0o5STamseCE6Guq; imusic=1181001624255578867831; Province=hn; City=744; loginState=true; user118100cn=339b08d774cfbb86451000c397005d803bb744d3df31b6a8b033576c362c30f4f31243607782ab0bb3e40f18d26628c1a0a3e74bc010b39b79d839a453e792d7; JSESSIONID=860A9E7295F97C97AA33E0A17DAF2014; JSESSIONID=B84BD64054677899A13F34613EBE6E32")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-短信验证码登录获取token-手机号:{},响应:{}", LOG_TAG, mobile, content);
            DianxinResult dianxinResult = mapper.readValue(content, DianxinResult.class);
            String authorization = response.header("Authorization");
            dianxinResult.setResult(authorization);
            return dianxinResult;
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-短信验证码登录获取token-手机号:{},异常:", LOG_TAG, mobile, e);
            return DianxinResult.fail();
        }
    }

    /**
     *  开通包月
     *
     * @param mobile 手机号
     *
     *   {"feeProductId":null,"feeType":1,"exeTime":1112,"backorder":0,"desc":"成功","code":"0000","resp":null,"result":"0"}
     *   {"feeProductId":null,"feeType":1,"exeTime":97,"backorder":0,"desc":"订购关系已存在","code":"10131","resp":null,"result":"0"}
     */
    @Nonnull
    public DianxinResult orderBoxLoadIsmp(String mobile,String authorization) {
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url("https://m.imusic.cn/vapi/ismp_package/order_box_load_ismp?channelId="+CHANNEL_ID+"&productId=135999999999999000052&mobile="+mobile+"&isMessage=1&portal=45")
                .method("POST", body)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", "0")
                .addHeader("Access-Control-Allow-Origin", "*")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Access-Control-Allow-Credentials", "true")
                .addHeader("Authorization", AUTH_PREFIX +authorization)
                .addHeader("sec-ch-ua-mobile", "?1")
                .addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36 Edg/91.0.864.54")
                .addHeader("sec-ch-ua", "\" Not;A Brand\";v=\"99\", \"Microsoft Edge\";v=\"91\", \"Chromium\";v=\"91\"")
                .addHeader("Origin", "https://m.imusic.cn")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Referer", "https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                //.addHeader("Cookie", "cc=7370; 3sWez06c8ptAO=5owCKLd9aR1ryDmJsSn1_e8j9aatbI8MDYq_2aJbRJkZ02xzNxgwdSxxJmunVDnBw3i_cJxG0o5STamseCE6Guq; imusic=1181001624255578867831; loginState=true; Province=hn; City=744; user118100cn=b26fc8e781d391e737bb71067cf923923c3f30eea61754c83e634a833d88bafec297a4e95fbe9d92ec3eeda08d775bf052aba2adea28a5dc994434118d5e8429; JSESSIONID=0B47390507DBBE10DD5EF2653FAFD7F5; JSESSIONID=B84BD64054677899A13F34613EBE6E32; user118100cn=8fa51b6caaae8800733bd5c1fc0735605ba81d313b4c2d01f54a7c9c2304690820f016f7ecc14fa2778e5c4b2251d54389fe2a1316c78f08e7876f25602d3030")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-开通包月-手机号:{},响应:{}", LOG_TAG, mobile, content);
            return mapper.readValue(content, DianxinResult.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-开通包月-手机号:{},异常:", LOG_TAG, mobile, e);
            return DianxinResult.fail();
        }
    }

    /**
     * 使用包月产品权益免费订购视频铃音
     *
     * @param mobile 手机号
     * @param toneId    铃音编码
     *     {"description":"视频彩铃订购成功！可在【我的彩铃】里进行查询设置。","status":null,"taskId":null,"returnCode":"0000","randomNum":null,"uuid":"775bc9ee-e1dc-4d8a-8f8b-fb6c251733de","executeTimeMs":79,"result":null}
     */
    @Nonnull
    public DianxinResult orderRingByPackage(String mobile, String authorization, String toneId) {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "mobile="+mobile+"&productId=135999999999999000052&toneId="+toneId+"&toneFee=0&flag=1&channelId="+CHANNEL_ID+"&portal=45");
        Request request = new Request.Builder()
                .url("https://m.imusic.cn/vapi/vrbt/order_ring_by_package")
                .method("POST", body)
                .addHeader("Connection", "keep-alive")
                .addHeader("sec-ch-ua", "\" Not;A Brand\";v=\"99\", \"Microsoft Edge\";v=\"91\", \"Chromium\";v=\"91\"")
                .addHeader("sec-ch-ua-mobile", "?1")
                .addHeader("Authorization", AUTH_PREFIX +authorization)
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Access-Control-Allow-Origin", "*")
                .addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36 Edg/91.0.864.54")
                .addHeader("Access-Control-Allow-Credentials", "true")
                .addHeader("Origin", "https://m.imusic.cn")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Referer", "https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                //.addHeader("Cookie", "cc=7370; 3sWez06c8ptAO=5owCKLd9aR1ryDmJsSn1_e8j9aatbI8MDYq_2aJbRJkZ02xzNxgwdSxxJmunVDnBw3i_cJxG0o5STamseCE6Guq; imusic=1181001624255578867831; Province=hn; City=744; JSESSIONID=860A9E7295F97C97AA33E0A17DAF2014; JSESSIONID=B84BD64054677899A13F34613EBE6E32")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-使用包月免费订购视频铃音-手机号:{},toneCode:{},响应:{}", LOG_TAG, mobile, toneId, content);
            return mapper.readValue(content, DianxinResult.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-使用包月免费订购视频铃音-手机号:{},toneCode:{},异常:", LOG_TAG, mobile, toneId, e);
            return DianxinResult.fail();
        }
    }

    /**
     * 查询包月套餐是否存在
     *
     * {
     *     "orderPackageRecordItemList": [
     *         {
     *             "mobile": "17342680919",
     *             "productId": "135999999999999000052",
     *             "price": 600,
     *             "productName": null,
     *             "orderTime": 1624258797000,
     *             "validTime": 2871734399000,
     *             "unUseTime": null,
     *             "packageType": 2,
     *             "feeStatus": 0,
     *             "state": 0
     *         },
     *         {
     *             "mobile": "17342680919",
     *             "productId": "135999999999999000032",
     *             "price": 600,
     *             "productName": null,
     *             "orderTime": 1623393565000,
     *             "validTime": 2871734399000,
     *             "unUseTime": 1624264187000,
     *             "packageType": 2,
     *             "feeStatus": 0,
     *             "state": 2
     *         },
     *         {
     *             "mobile": "17342680919",
     *             "productId": "7360110000100255",
     *             "price": 0,
     *             "productName": null,
     *             "orderTime": 1624266474000,
     *             "validTime": 2255418473000,
     *             "unUseTime": null,
     *             "packageType": 2,
     *             "feeStatus": 0,
     *             "state": 0
     *         }
     *     ],
     *     "desc": "查询成功",
     *     "code": "0000",
     *     "resp": null,
     *     "result": "0"
     * }
     *
     * @param mobile 手机号
     */
    public boolean queryPackageExist(String mobile,String authorization) {
        Request request = new Request.Builder()
                .url("https://m.imusic.cn/vapi/ismp_package/find_all_packages?channelId="+CHANNEL_ID+"&mobile="+mobile+"&portal=45")
                .method("GET", null)
                .addHeader("Connection", "keep-alive")
                .addHeader("Access-Control-Allow-Origin", "*")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Access-Control-Allow-Credentials", "true")
                .addHeader("Authorization", AUTH_PREFIX +authorization)
                .addHeader("sec-ch-ua-mobile", "?1")
                .addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36 Edg/91.0.864.54")
                .addHeader("sec-ch-ua", "\" Not;A Brand\";v=\"99\", \"Microsoft Edge\";v=\"91\", \"Chromium\";v=\"91\"")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Referer", "https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                //.addHeader("Cookie", "JSESSIONID=B84BD64054677899A13F34613EBE6E32")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询是否已包月-手机号:{},响应:{}", LOG_TAG, mobile, content);
            JsonNode tree = mapper.readTree(content);
            //如果没有这个值,视作退订(未订购)
            //            int status = tree.at("/UserPackageListResp/user_package_list/user_package/status").asInt(2);
            if(!tree.get("code").asText().equals("0000")){
                return false;
            }
            return Streams.stream(tree.get("orderPackageRecordItemList").elements()).anyMatch(item -> "135999999999999000052".equals(item.get("productId").asText()) && item.get("state").asInt(2)==0);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-查询是否已包月-手机号:{},异常:", LOG_TAG, mobile, e);
            return false;
        }
    }


    public static void main(String[] args) throws JsonProcessingException, DecoderException, UnsupportedEncodingException, ClassNotFoundException {
        String raw = "{\"orderPackageRecordItemList\":[{\"mobile\":\"17342680919\",\"productId\":\"135999999999999000052\",\"price\":600,\"productName\":null,\"orderTime\":1624258797000,\"validTime\":2871734399000,\"unUseTime\":null,\"packageType\":2,\"feeStatus\":0,\"state\":0},{\"mobile\":\"17342680919\",\"productId\":\"135999999999999000032\",\"price\":600,\"productName\":null,\"orderTime\":1623393565000,\"validTime\":2871734399000,\"unUseTime\":1624264187000,\"packageType\":2,\"feeStatus\":0,\"state\":2},{\"mobile\":\"17342680919\",\"productId\":\"7360110000100255\",\"price\":0,\"productName\":null,\"orderTime\":1624266474000,\"validTime\":2255418473000,\"unUseTime\":null,\"packageType\":2,\"feeStatus\":0,\"state\":0}],\"desc\":\"查询成功\",\"code\":\"0000\",\"resp\":null,\"result\":\"0\"}";
        //String raw = "{\"UserPackageListResp\":{\"res_code\":\"0000\",\"res_message\":\"查询包月套餐列表成功\",\"user_package_list\":{\"user_package\":{\"package_id\":\"135999999999999000021\",\"order_time\":\"2020-11-26 12:06:03\",\"unsubscribe_time\":\"2020-11-26 12:18:59\",\"status\":2,\"valid_time\":\"2060-12-31 23:59:59\"}},\"mdn\":18080928200}}";
        final ObjectMapper mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        final JsonNode tree = mapper.readTree(raw);
        Streams.stream(tree.get("orderPackageRecordItemList").elements()).forEach(System.out::println);
        boolean anyMatch = Streams.stream(tree.get("orderPackageRecordItemList").elements()).anyMatch(
                item -> "135999999999999000052".equals(item.get("productId").asText()) && item.get("state").asInt(
                        2) == 0);

        System.out.println(anyMatch);


    }
}
