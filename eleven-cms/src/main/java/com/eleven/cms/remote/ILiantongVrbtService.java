package com.eleven.cms.remote;

import com.eleven.cms.vo.LiantongResp;
import com.eleven.cms.vo.LiantongSubedProductsResp;
import com.eleven.cms.vo.LiantongUserRingDepotResp;
import okhttp3.MediaType;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;

public interface ILiantongVrbtService {
    MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    static void main(String[] args) {
        System.out.println(System.currentTimeMillis());
    }

    //   @PostConstruct
    void init();

    LiantongSubedProductsResp qrySubedProductsNoToken(String callNumber);

    boolean isSubedMon(String callNumber);

    boolean sendLoginCode(String callNumber);

    boolean codeLogin(String callNumber, String verifyCode);

    LiantongResp onePointProductMon(String phoneNumber, String ringId, String redirectUrl);

    boolean sendMsg(String callNumber);

    @Nonnull
    LiantongResp orderRingOnePointMon(String phoneNumber, String ringId);

    LiantongUserRingDepotResp userVideoRingDepotList(String phoneNumber);

    LiantongResp sendVerifyCode(String callNumber);

    LiantongResp unSubProductWithVCode(String callNumber, String verifyCode);

    LiantongResp unSubProductNoToken(String callNumber);

    String postRaw(String url, String raw) throws IOException;

    void testOrderNotify();
}
