package com.eleven.cms.remote;

import com.eleven.cms.config.HebeiYidongCityVrbtConfig;
import com.eleven.cms.config.HebeiYidongCityVrbtProperties;
import com.eleven.cms.config.HebeiYidongVrbtConfig;
import com.eleven.cms.config.HebeiYidongVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HebeiMobileResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 河北移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class HebeiYidongCityVrbtService {

    @Autowired
    private Environment environment;
    @Autowired
    private HebeiYidongCityVrbtProperties hebeiYidongCityVrbtProperties;


    private OkHttpClient client;
    private ObjectMapper mapper;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     * returnCode 1000成功 1005失败
     * @param phone
     * @return
     */
    public @Nonnull
    HebeiMobileResult getSms(String phone, String city, String ip) {
        HebeiYidongCityVrbtConfig hebeiYidongCityVrbtConfig = hebeiYidongCityVrbtProperties.getHebeiYidongCityVrbtConfig(city);
        FormBody.Builder build = new FormBody.Builder()
                .add("tel", phone)
                .add("ip", ip);
        FormBody body = build.build();
        Request request = new Request.Builder()
                .url(hebeiYidongCityVrbtConfig.getGetSmsUrl())
                .post(body)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", hebeiYidongCityVrbtConfig.getLogTag(), phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", hebeiYidongCityVrbtConfig.getLogTag(), phone, content);
            return mapper.readValue(content, HebeiMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", hebeiYidongCityVrbtConfig.getLogTag(), phone, e);
            return HebeiMobileResult.fail();
        }
    }


    // {"code":0,"msg":"办理成功","data":{"res_code":"0","res_desc":"Processing the request succeeded!","result":{"TRADEFORMNUM":"311220217268806262","DETAILINFO":[],"retInnerMsg":"Processing the request succeeded!"}}}
    // 0成功 -1失败

    /**
     * 提交短信验证码
     * returnCode 1000成功 1005失败
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    HebeiMobileResult smsCode(String phone, String smsCode, String city, String ip) {
        HebeiYidongCityVrbtConfig hebeiYidongCityVrbtConfig = hebeiYidongCityVrbtProperties.getHebeiYidongCityVrbtConfig(city);
        FormBody.Builder build = new FormBody.Builder()
                .add("tel", phone)
                .add("code", smsCode)
                .add("pid", hebeiYidongCityVrbtConfig.getPId())
                .add("url", hebeiYidongCityVrbtConfig.getPageUrl())
                .add("channel", hebeiYidongCityVrbtConfig.getChannel())
                .add("ip", ip);
        FormBody body = build.build();
        Request request = new Request.Builder()
                .url(hebeiYidongCityVrbtConfig.getSendSmsUrl())
                .post(body)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},请求:{}", hebeiYidongCityVrbtConfig.getLogTag(), phone, smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},响应:{}", hebeiYidongCityVrbtConfig.getLogTag(), phone, smsCode, content);
            return mapper.readValue(content, HebeiMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},异常:", hebeiYidongCityVrbtConfig.getLogTag(), phone, smsCode, e);
            return HebeiMobileResult.fail();
        }
    }
}
