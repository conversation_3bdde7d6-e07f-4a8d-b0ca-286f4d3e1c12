package com.eleven.cms.remote;

import com.eleven.cms.vo.HenanMobileCheckResult;
import com.eleven.cms.vo.HenanMobileMarketResult;
import com.eleven.cms.vo.HenanMobileQueryResult;
import com.eleven.cms.vo.HenanMobileSmsResult;

public interface IHenanYidongService {
    HenanMobileMarketResult queryMarket(String phone, String channel);

    HenanMobileCheckResult queryActive(String phone, String channel);

    HenanMobileCheckResult queryLlbActive(String phone, String channel);

    HenanMobileSmsResult sendSms(String phone, String channel);

    HenanMobileSmsResult sendLlbSms(String phone, String channel);

    HenanMobileSmsResult smsCode(String phone, String orderId, String smsCode, String channel, String referer);

    HenanMobileQueryResult queryRecord(String orderId, String channel, String phone);

    Integer orderResultJudge(String orderId, String channel, String phone);
}
