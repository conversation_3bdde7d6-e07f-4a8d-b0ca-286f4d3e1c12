package com.eleven.cms.remote;

import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.MiguSecurityUtils;
import com.eleven.cms.util.QiedaoAIAPIUtils;
import com.eleven.cms.util.TencentCloudAPIUtils;
import com.eleven.qycl.service.AliMediaService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @datetime 2024/12/21 15:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonSecurityService {
    private final AliMediaService aliMediaService;
    private final RedisUtil redisUtil;

    public static final String PREFIX_KEY = "migu:security:";

    public Boolean picSecurity(Boolean securityDetection,Boolean faceDetection, String picUrl) {
        if (faceDetection){
            Boolean result = QiedaoAIAPIUtils.parseFacesDetectionResponse(QiedaoAIAPIUtils.facesDetection(picUrl));
            if (!result){
                aliMediaService.deleteObject(picUrl);
                throw new JeecgBootException("请使人脸在正中，且避免人脸太小");
            }
        }

        if (securityDetection){
            Boolean result = TencentCloudAPIUtils.parsePicContentSecurityResponse(TencentCloudAPIUtils.picContentSecurity(picUrl));
            if (!result){
                aliMediaService.deleteObject(picUrl);
                throw new JeecgBootException("图片审核未通过，请重新上传");
            }
        }

        return Boolean.TRUE;
    }

    public String picSecurityPush(Boolean securityDetection,Boolean faceDetection, String picUrl) {
        if (faceDetection){
            Boolean result = QiedaoAIAPIUtils.parseFacesDetectionResponse(QiedaoAIAPIUtils.facesDetection(picUrl));
            if (!result){
                aliMediaService.deleteObject(picUrl);
                throw new JeecgBootException("请使人脸在正中，且避免人脸太小");
            }
        }

        if (securityDetection){
            String result = MiguSecurityUtils.pushDataCheck(0, picUrl);
            JsonNode jsonNode = JacksonUtils.readTree(result);
            if ("000000".equals(jsonNode.at("/code").asText())) {
                String dataId = jsonNode.at("/data").get(0).at("/dataId").asText();
                String key = PREFIX_KEY + dataId;
                redisUtil.set(key, 0, 90);
                return dataId;
            } else {
                throw new JeecgBootException("提交失败!");
            }
        }
        throw new JeecgBootException("提交失败!");
    }

    /**
     * securityResult
     *
     * @param dataId
     * @return -1:失败 0:初始 1:成功
     */
    public Object securityResult(String dataId) {
        String key = PREFIX_KEY + dataId;
        Object o = redisUtil.get(key);
        if (o == null) {
            return -1;
        }
        if (0 != ((Integer) o)) {
            redisUtil.del(key);
        }
        return o;
    }
}
