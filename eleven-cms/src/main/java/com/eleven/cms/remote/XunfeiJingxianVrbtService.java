package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.XunfeiVrbtProperties;
import com.eleven.cms.job.VrbtZeroOrderTask;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.DesUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.XunfeiVrbtZeroOrderResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.codec.Base64;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Random;
import java.util.Set;

/**
 * 讯飞泾县视频彩铃开通成功数据同步
 * proId：01000005
 * operateTypeId：5063
 * chargeID：10060016
 * secretKey:iflytekcti
 */
@Slf4j
@Service
public class XunfeiJingxianVrbtService {

    @Autowired
    XunfeiVrbtProperties xunfeiVrbtProperties;
    @Autowired
    private Environment environment;

    private String url;
    private String proId;
    private String secretKey;

    public static final String DES = "DES";

    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.testProxyHost, OkHttpClientUtils.testProxyPort)))
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);

       url = xunfeiVrbtProperties.getCallbackUrl();
       proId = xunfeiVrbtProperties.getProId();
       secretKey = xunfeiVrbtProperties.getSecretKey();
    }


    /**
     * 讯飞泾县视频彩铃开通成功数据同步
     *  请求参数
     * 参数名称	是否必选	类型	说明
     * proId	是	String	产品ID，01000001（酷音铃音+），04000001（酷音铃音）
     * operateTypeId	是	String	操作渠道标识ID（渠道ID）
     * ts	是	String	时间戳 格式yyyyMMddHHmmssSSS
     * token	是	String	鉴权密文  MD5(proId + ts + s ecretKey)，secretKey：iflytekcti
     * pNo	是	String	加密后的用户号码
     * chargeID	是	String	计费编码
     * operatetime	是	String	操作时间，格式yyyy-MM-dd HH:mm:ss
     * status	是	String	用户状态 2-订购 4-退订
     * result	否	String	操作结果，1-成功 其他-失败，默认成功
     * description	否	String	操作返回描述
     * retcode	否	String	运营商返回码
     * retdesc	否	String	运营商返回描述
     * orderId	否	String	订单ID
     * @param mobile
     * @param channelCode
     * @param status
     * @param result
     */
    //@Async
    public void resultCallback(String mobile, String channelCode, Integer status, String result) {
        //只上报成功的数据
        if(status != 1){
            return;
        }

        final LocalDateTime now = LocalDateTime.now();
        String ts = DateUtil.formatFullTime(now, DateUtil.FULL_TIME_PATTERN_WITH_MILL);
        String operatetime = DateUtil.formatFullTime(now, DateUtil.FULL_TIME_SPLIT_PATTERN);
        String token = DigestUtils.md5DigestAsHex((proId + ts + secretKey).getBytes(StandardCharsets.UTF_8));
        String pNo = null;
        try {
            pNo = kypEncrypt(mobile,genEncodeKeyt(secretKey,ts));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String operateTypeId = xunfeiVrbtProperties.getChannelOperateTypeIdMap().get(channelCode);
        String chargeId = xunfeiVrbtProperties.getChannelChargeIdMap().get(channelCode);
        final HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .addQueryParameter("proId", proId)
                .addQueryParameter("operateTypeId", operateTypeId)
                .addQueryParameter("chargeID", chargeId)
                .addQueryParameter("ts", ts)
                .addQueryParameter("operatetime", operatetime)
                .addQueryParameter("token", token)
                .addQueryParameter("status", "2")
                .addQueryParameter("pNo", pNo)
                .addQueryParameter("retdesc", result)
                .build();

        log.info("讯飞泾县视频彩铃开通成功数据同步=>手机号:{},channelCode:{},请求:{}",  mobile, channelCode, httpUrl);
        
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("讯飞泾县视频彩铃开通成功数据同步=>手机号:{},channelCode:{},响应:{}", mobile, channelCode, content);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("讯讯飞泾县视频彩铃开通成功数据同步=>手机号:{},channelCode:{},异常:", mobile, channelCode, e);
        }
    }


    public void resultNewCallback(String mobile, String channelCode, Integer status, String result) {
        //只上报成功的数据
        if(status != 1){
            return;
        }
        final LocalDateTime now = LocalDateTime.now();
        String t = DateUtil.formatFullTime(LocalDateTime.now(), DateUtil.FULL_TIME_PATTERN);
        String f = IdWorker.get32UUID();
        String bid = xunfeiVrbtProperties.getZeroOrderBid();
        String bpwd = xunfeiVrbtProperties.getZeroOrderBpwd();
        String sign = DigestUtils.md5DigestAsHex((bpwd+f+t).getBytes(StandardCharsets.UTF_8));

        String operateTypeId = xunfeiVrbtProperties.getChannelOperateTypeIdMap().get(channelCode);
        String chargeId = xunfeiVrbtProperties.getChannelChargeIdMap().get(channelCode);
        String phone = DesUtil.encryptCBC(bpwd, bpwd, mobile+"#"+t);


        final ObjectNode dataNode = mapper.createObjectNode()
                .put("a", operateTypeId)
                .put("phone", phone)
                .put("result", status)
                .put("chargeid", chargeId)
                .put("desc", result);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .header("t", t)
                .header("f", f)
                .header("sid", IdWorker.get32UUID())
                .header("bid", bid)
                .header("sign", sign)
                .build();

        log.info("讯飞泾县视频彩铃开通成功数据同步(新接口)=>手机号:{},channelCode:{},请求参数:{}",  mobile, channelCode, dataNode);

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("讯飞泾县视频彩铃开通成功数据同步(新接口)=>手机号:{},channelCode:{},响应:{}", mobile, channelCode, content);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("讯讯飞泾县视频彩铃开通成功数据同步(新接口)=>手机号:{},channelCode:{},异常:", mobile, channelCode, e);
        }
    }

    public XunfeiVrbtZeroOrderResult vrbtZeroOrder(String mobile) {
        String zeroOrderA = xunfeiVrbtProperties.getZeroOrderA();
        String chargeid = xunfeiVrbtProperties.getChannelChargeIdMap().get(VrbtZeroOrderTask.CHANNEL_XUNFEI);

        return vrbtZeroOrderByChannel(mobile, zeroOrderA, chargeid);
    }

    public XunfeiVrbtZeroOrderResult vrbtZeroOrderTemp(String mobile) {
        String zeroOrderATemp = xunfeiVrbtProperties.getZeroOrderATemp();
        String chargeid = xunfeiVrbtProperties.getChannelChargeIdMap().get(VrbtZeroOrderTask.CHANNEL_XUNFEI_TEMP);

        return vrbtZeroOrderByChannel(mobile, zeroOrderATemp, chargeid);
    }

    /**
     * 讯飞三方支付0元开通视频彩铃
     *  a	应用渠道号	String	是	5274
     * tc	请求会话ID	String	否	用于定位每一次请求，每次调用都不一样。 可以使用uuid/guid等随机字符串。
     * phone	号码	String	是	DES加密(手机号+#+yyyyMMddHHmmss 时间戳)，详见附录
     * chargeid	计费编码	String	否	计费编码
     * 
     * retcode	否	String	运营商返回码
     * retdesc	否	String	运营商返回描述
     * orderId	否	String	订单ID
     * @param mobile
     *
     * return {"retcode":"2107","retdesc":"网络超时，请稍后再试！","desc":"视频彩铃功能未开通"} desc字段是正确的描述
     * 0000	成功
     * 1000	参数验证失败
     * 1029	接口未开启
     * 2107
     * 	视频彩铃功能未开通
     * 4101	黑名单用户
     * 9999	内部异常
     */
    public XunfeiVrbtZeroOrderResult vrbtZeroOrderByChannel(String mobile,String zeroOrderA,String chargeid) {
        String t = DateUtil.formatFullTime(LocalDateTime.now(), DateUtil.FULL_TIME_PATTERN);
        String f = IdWorker.get32UUID();
        String bid = xunfeiVrbtProperties.getZeroOrderBid();
        String bpwd = xunfeiVrbtProperties.getZeroOrderBpwd();
        String sign = DigestUtils.md5DigestAsHex((bpwd+f+t).getBytes(StandardCharsets.UTF_8));
        
        String phone = DesUtil.encryptCBC(bpwd, bpwd, mobile+"#"+t);

        final ObjectNode dataNode = mapper.createObjectNode()
                .put("a", zeroOrderA)
                .put("phone", phone)
                .put("chargeid", chargeid);


        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        
        Request request = new Request.Builder()
                .url(xunfeiVrbtProperties.getZeroOrderUrl())
                .post(body)
                .header("t", t)
                .header("f", f)
                .header("sid", IdWorker.get32UUID())
                .header("bid", bid)
                .header("sign", sign)
                .build();
        
        log.info("讯飞三方支付0元开通视频彩铃=>请求:{}", dataNode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("讯飞三方支付0元开通视频彩铃=>响应:{}", content);
            return mapper.readValue(content, XunfeiVrbtZeroOrderResult.class);
        } catch (Exception e) {
            log.error("讯飞三方支付0元开通视频彩铃异常", e);
            return XunfeiVrbtZeroOrderResult.fail();
        }
    }

    public String queryVrbtRing(String columnId,Integer pageNum) {
        int px = pageNum > 0 ? pageNum - 1 : 0;
        final LocalDateTime now = LocalDateTime.now();
        String t = DateUtil.formatFullTime(now, DateUtil.FULL_TIME_PATTERN);
        String f = UUIDGenerator.generate();
        String pwd = xunfeiVrbtProperties.getPwd();
        String bid = xunfeiVrbtProperties.getBid();
        String queryVrbtRingUrl = xunfeiVrbtProperties.getQueryVrbtRingUrl();
        final HttpUrl httpUrl = HttpUrl.parse(queryVrbtRingUrl)
                .newBuilder()
                .addQueryParameter("a", xunfeiVrbtProperties.getA())
                .addQueryParameter("id", columnId)
                .addQueryParameter("px", String.valueOf(px))
//                .addQueryParameter("ps", ts)
                .addQueryParameter("ct", "1")
                .build();
        //sign = MD5(bpwd+f+t)，32位小写
        String sign = DigestUtils.md5DigestAsHex((pwd + f + t).getBytes(StandardCharsets.UTF_8));
        Request request = new Request.Builder()
                .header("t", t)
                .header("f", f)
                .header("sid", UUIDGenerator.generate())
                .header("bid", bid)
                .header("bpwd", pwd)
                .header("sign", sign)
                .url(httpUrl).build();
        log.info("讯飞泾县视频彩铃铃音查询=>请求:{}", httpUrl);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("讯飞泾县视频彩铃铃音查询=>响应:OK,内容太长不输出");
            return content;
        } catch (Exception e) {
            log.error("讯飞泾县视频彩铃铃音查询异常", e);
            return "";
        }
    }

    public Set<Map.Entry<String, String>> queryVrbtColumn() {
        return xunfeiVrbtProperties.getVrbtColumnMap().entrySet();
    }

    /**
     * 讯飞随机获取一首视频彩铃的版权id
     * @return
     */
    public String getRandomVrbtRing() {
        String columnId = xunfeiVrbtProperties.getSourceId();
        return fetchRandomVrbtRingByColumnId(columnId);
    }

    /**
     * 讯飞随机获取一首视频彩铃的版权id(临时刷量任务)
     * @return
     */
    public String getRandomVrbtRingTemp() {
        String columnId = xunfeiVrbtProperties.getSourceIdTemp();
        return fetchRandomVrbtRingByColumnId(columnId);
    }

    /**
     * 讯飞随机获取一首视频彩铃的版权id
     * @return
     */
    private String fetchRandomVrbtRingByColumnId(String columnId) {
        String result = this.queryVrbtRing(columnId,1);
        if (StringUtils.isEmpty(result)) {
            log.error("讯飞泾县视频彩铃获取铃音失败");
            return "";
        }
        JsonNode jsonNode = mapper.createObjectNode();
        try {
            jsonNode = mapper.readTree(result);
        } catch (JsonProcessingException e) {
            log.error("讯飞泾县视频彩铃获取铃音失败",e);
            return "";
        }
        String code = jsonNode.at("/retcode").asText();
        if (!"0000".equals(code)) {
            log.error("讯飞泾县视频彩铃获取铃音失败");
            return "";
        }
        ArrayNode arrayNode = (ArrayNode) jsonNode.get("data");
        jsonNode = arrayNode.get(new Random().nextInt(arrayNode.size()));
        return jsonNode.at("/mbcrid").asText();
    }

    

    // 生成解密密钥算法，keyt：secretKey，ts：请求参数中ts
    public static String genEncodeKeyt(String keyt, String ts) {
        int keytLen = keyt.length();
        int tsLen = ts.length();
        return StringUtils.join(keyt.substring(keytLen - 3),
                ts.substring(tsLen - 5));
    }

    // 加密算法，text：用户号码，rawKey：密钥
    public static String kypEncrypt(String text, String rawKey) throws Exception {
        StringBuilder ret = new StringBuilder();
        String oristr = encrypt(text, rawKey);
        String base64str = oristr.replaceAll("\r|\n", "");
        int strlen = base64str.getBytes().length;
        for (int i = 0; i < strlen; i++) {
            char a = base64str.charAt(i);
            String binstr = Integer.toBinaryString(a);
            int mod4 = binstr.length() % 4;
            StringBuilder head = new StringBuilder();
            if (mod4 != 0) {
                int rest = 4 - mod4;
                for (int m = 0; m < rest; m++) {
                    head.append("0");
                }
                binstr = head + binstr;
            }
            int binstrlen = binstr.length() / 4;
            for (int j = 0; j < binstrlen; j++) {
                ret.append(Integer.toHexString(Integer.parseInt(
                        binstr.substring(j * 4, j * 4 + 4), 2)));
            }
        }
        return ret.toString();
    }

    public static String encrypt(String data, String key) throws Exception {
        byte[] bt = encrypt(data.getBytes(), key.getBytes());
        String strs = Base64.encodeToString(bt);
        return strs;
    }

    public static byte[] encrypt(byte[] data, byte[] key) throws Exception {
        SecureRandom sr = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(key);

        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey secureKey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance(DES);
        cipher.init(Cipher.ENCRYPT_MODE, secureKey, sr);
        return cipher.doFinal(data);
    }


}
