package com.eleven.cms.remote;

import com.eleven.cms.config.JiangsuYidongProperties;
import com.eleven.cms.config.YuxunHunanDianxinProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 江苏业务类（江苏移动视频彩铃酷电秀6元包）
 *
 * @author: cai lei
 * @create: 2022-07-07 10:29
 */

@Slf4j
@Service
public class JiangsuYidongService {

    public static final String LOG_TAG = "江苏移动api";
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    @Autowired
    private JiangsuYidongProperties jiangsuYidongProperties;
    @Autowired
    private MiguApiService miguApiService;

    public static final MediaType XMLTYPE = MediaType.parse(org.springframework.http.MediaType.APPLICATION_XML_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                    .compressed()
                    .insecure()  //不检查自签名证书
                    //.connectTimeout(120)
                    //.retry(5)
                    .build();
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .addNetworkInterceptor(new CurlInterceptor(System.out::println, options)).build();
        }
        this.mapper = new XmlMapper().configure(ToXmlGenerator.Feature.WRITE_XML_DECLARATION, true).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    @SneakyThrows
    @Cacheable(cacheNames = CacheConstant.CMS_JSYD_TOKEN_CACHE, key = "#root.methodName", unless = "#result==null")
    public String getToken() {
        String url = jiangsuYidongProperties.getAuthUrl();
        String apiKey = jiangsuYidongProperties.getApiKey();
        String secretKey = jiangsuYidongProperties.getSecretKey();
        String secInterface = jiangsuYidongProperties.getSecInterface();

        JiangsuRequestToken jiangsuRequestToken = new JiangsuRequestToken();
        String time = String.valueOf(System.currentTimeMillis());
        jiangsuRequestToken.setDatetime(time);
        String sign = DigestUtils.md5DigestAsHex((apiKey + secretKey + time).getBytes(StandardCharsets.UTF_8));
        JiangsuRequestToken.Authorization authorization = JiangsuRequestToken.Authorization.builder().appKey(apiKey).secInterface(secInterface).sign(sign).build();
        jiangsuRequestToken.setAuthorization(authorization);

        RequestBody body = RequestBody.create(XMLTYPE, mapper.writeValueAsString(jiangsuRequestToken));
        log.info("{}-获取token", LOG_TAG);
        Request request = new Request.Builder()
                .header("HTTP-X-4GGOGO-Signature", sign)
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取token,响应:{}", LOG_TAG, org.apache.commons.lang3.StringUtils.normalizeSpace(content));
            JiangsuResponseToken jiangsuResponseToken = mapper.readValue(content, JiangsuResponseToken.class);
            if (jiangsuResponseToken.getAuthorization() != null) {
                return jiangsuResponseToken.getAuthorization().getToken();
            } else {
                return null;
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取token,异常:", LOG_TAG, e);
            return null;
        }
    }

    @SneakyThrows
    public JiangsuResponseCreateOrder createOrder(String phone, String sourceAppName, String sourceAdvId, String sourceUrl, String token) {
        String url = jiangsuYidongProperties.getCreateOrderUrl();
        String apiKey = jiangsuYidongProperties.getApiKey();
        String secretKey = jiangsuYidongProperties.getSecretKey();
        String goodCode = jiangsuYidongProperties.getGoodCode();
        String bookingId = UUIDGenerator.generate();
        JiangsuRequestCreateOrder jiangsuRequestCreateOrder = new JiangsuRequestCreateOrder();
        String time = String.valueOf(System.currentTimeMillis());
        jiangsuRequestCreateOrder.setDatetime(time);
        String sign = DigestUtils.md5DigestAsHex((apiKey + secretKey + time).getBytes(StandardCharsets.UTF_8));
        JiangsuRequestCreateOrder.Content content = JiangsuRequestCreateOrder.Content.builder()
                .telnum(phone).goodCode(goodCode).sourceAppName("穿山甲|"+sourceAppName).sourceAdvId(StringUtils.isEmpty(sourceAdvId) ? "test" : sourceAdvId).sourceUrl(sourceUrl.indexOf("?") > 0 ? sourceUrl.substring(0, sourceUrl.indexOf("?")) : sourceUrl).bookingId(bookingId).authchktype("AuthCheckR").build();
        jiangsuRequestCreateOrder.setContent(content);

        RequestBody body = RequestBody.create(XMLTYPE, mapper.writeValueAsString(jiangsuRequestCreateOrder));
        log.info("{}-创建订单-手机号:{}", LOG_TAG, phone);
        Request request = new Request.Builder()
                .header("HTTP-X-4GGOGO-Signature", sign)
                .header("4GGOGO-Auth-Token", token)
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-创建订单-手机号:{}-响应:{}", LOG_TAG, phone, org.apache.commons.lang3.StringUtils.normalizeSpace(result));
            return mapper.readValue(result, JiangsuResponseCreateOrder.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-创建订单-手机号:{},异常:", LOG_TAG, phone, e);
            return JiangsuResponseCreateOrder.FAIL_RESULT;
        }
    }

    @SneakyThrows
    public JiangsuResponseSmsValidate smsValidate(String phone, String orderId, String code, String token) {
        String url = jiangsuYidongProperties.getSmsValidateUrl();
        String apiKey = jiangsuYidongProperties.getApiKey();
        String secretKey = jiangsuYidongProperties.getSecretKey();
        JiangsuRequestSmsValidate jiangsuRequestSmsValidate = new JiangsuRequestSmsValidate();
        String time = String.valueOf(System.currentTimeMillis());
        jiangsuRequestSmsValidate.setDatetime(time);
        String sign = DigestUtils.md5DigestAsHex((apiKey + secretKey + time).getBytes(StandardCharsets.UTF_8));
        JiangsuRequestSmsValidate.Content content = JiangsuRequestSmsValidate.Content.builder()
                .orderId(orderId).randomCode(code).build();
        jiangsuRequestSmsValidate.setContent(content);

        RequestBody body = RequestBody.create(XMLTYPE, mapper.writeValueAsString(jiangsuRequestSmsValidate));
        log.info("{}-订单验证码校验及办理接口-手机号:{}-订单号:{},短信验证码:{}", LOG_TAG, phone, orderId,code);
        Request request = new Request.Builder()
                .header("HTTP-X-4GGOGO-Signature", sign)
                .header("4GGOGO-Auth-Token", token)
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-订单验证码校验及办理接口-手机号:{}-订单号:{}-响应:{}", LOG_TAG, phone, orderId, org.apache.commons.lang3.StringUtils.normalizeSpace(result));
            return mapper.readValue(result, JiangsuResponseSmsValidate.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-订单验证码校验及办理接口-手机号:{}-订单号:{},异常:", LOG_TAG, phone, orderId, e);
            return JiangsuResponseSmsValidate.FAIL_RESULT;
        }
    }

    @SneakyThrows
    public JiangsuResponseQueryInfo queryOrder(String phone, String orderId, String token) {
        String url = jiangsuYidongProperties.getQueryOrderUrl();
        String apiKey = jiangsuYidongProperties.getApiKey();
        String secretKey = jiangsuYidongProperties.getSecretKey();
        JiangsuRequestQueryInfo jiangsuRequestQueryInfo = new JiangsuRequestQueryInfo();
        String time = String.valueOf(System.currentTimeMillis());
        jiangsuRequestQueryInfo.setDatetime(time);
        String sign = DigestUtils.md5DigestAsHex((apiKey + secretKey + time).getBytes(StandardCharsets.UTF_8));
        JiangsuRequestQueryInfo.Content content = JiangsuRequestQueryInfo.Content.builder()
                .orderId(orderId).build();
        jiangsuRequestQueryInfo.setContent(content);

        RequestBody body = RequestBody.create(XMLTYPE, mapper.writeValueAsString(jiangsuRequestQueryInfo));
        log.info("{}-查询订单接口-手机号:{}-订单号:{}", LOG_TAG, phone, orderId);
        Request request = new Request.Builder()
                .header("HTTP-X-4GGOGO-Signature", sign)
                .header("4GGOGO-Auth-Token", token)
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询订单接口-手机号:{}-订单号:{}-响应:{}", LOG_TAG, phone, orderId, org.apache.commons.lang3.StringUtils.normalizeSpace(result));
            return mapper.readValue(result, JiangsuResponseQueryInfo.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-查询订单接口-手机号:{}-订单号:{},异常:", LOG_TAG, phone, orderId, e);
            return JiangsuResponseQueryInfo.FAIL_RESULT;
        }
    }

    /**
     * 由于江苏移动的订购关系接口不准,改为从咪咕侧查询
     * @param phone
     * @return
     */
    public boolean queryOrderMigu(String phone) {
        final RemoteResult remoteResult = miguApiService.vrbtMonthStatusQuery(phone,MiguApiService.XUNFEI_CHANNEL_CODE_02G,true);
        return remoteResult.isVrbtMember();
    }

    

    @SneakyThrows
    public void queryAge(String phone, String token) {
        String url = jiangsuYidongProperties.getQueryAgeUrl();
        String apiKey = jiangsuYidongProperties.getApiKey();
        String secretKey = jiangsuYidongProperties.getSecretKey();
        JiangsuRequestQueryAge jiangsuRequestQueryAge = new JiangsuRequestQueryAge();
        String time = String.valueOf(System.currentTimeMillis());
        jiangsuRequestQueryAge.setDatetime(time);
        String sign = DigestUtils.md5DigestAsHex((apiKey + secretKey + time).getBytes(StandardCharsets.UTF_8));
        JiangsuRequestQueryAge.Content content = JiangsuRequestQueryAge.Content.builder().mobile(phone).build();
        jiangsuRequestQueryAge.setContent(content);

        RequestBody body = RequestBody.create(XMLTYPE, mapper.writeValueAsString(jiangsuRequestQueryAge));
        log.info("{}-查询年龄-手机号:{}", LOG_TAG, phone);
        Request request = new Request.Builder()
                .header("HTTP-X-4GGOGO-Signature", sign)
                .header("4GGOGO-Auth-Token", token)
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询年龄-手机号:{}-响应:{}", LOG_TAG, phone, org.apache.commons.lang3.StringUtils.normalizeSpace(result));
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-查询年龄-手机号:{},异常:", LOG_TAG, phone, e);
        }
    }

    @SneakyThrows
    public void orderCheck(String phone, String token) {
        String url = jiangsuYidongProperties.getOrderCheckUrl();
        String apiKey = jiangsuYidongProperties.getApiKey();
        String secretKey = jiangsuYidongProperties.getSecretKey();
        String goodCode = jiangsuYidongProperties.getGoodCode();
        JiangsuRequestCreateOrder jiangsuRequestCreateOrder = new JiangsuRequestCreateOrder();
        String time = String.valueOf(System.currentTimeMillis());
        jiangsuRequestCreateOrder.setDatetime(time);
        String sign = DigestUtils.md5DigestAsHex((apiKey + secretKey + time).getBytes(StandardCharsets.UTF_8));
        JiangsuRequestCreateOrder.Content content = JiangsuRequestCreateOrder.Content.builder()
                .telnum(phone).goodCode(goodCode).build();
        jiangsuRequestCreateOrder.setContent(content);


        RequestBody body = RequestBody.create(XMLTYPE, mapper.writeValueAsString(jiangsuRequestCreateOrder));
        log.info("{}-订购校验-手机号:{}", LOG_TAG, phone);
        Request request = new Request.Builder()
                .header("HTTP-X-4GGOGO-Signature", sign)
                .header("4GGOGO-Auth-Token", token)
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-订购校验-手机号:{}-响应:{}", LOG_TAG, phone, org.apache.commons.lang3.StringUtils.normalizeSpace(result));
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-订购校验-手机号:{},异常:", LOG_TAG, phone, e);
        }
    }
}