package com.eleven.cms.remote;

import com.eleven.cms.config.HainanYunshemeiConfig;
import com.eleven.cms.config.HainanYunshemeiNewConfig;
import com.eleven.cms.config.HainanYunshemeiNewProperties;
import com.eleven.cms.config.HainanYunshemeiProperties;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HainanYunshemeiNewResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;

/**
 * 海南移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class HainanYidongYunshemeiNewService {

    public static final String LOG_TAG = "云摄美海南移动new-api";


    @Autowired
    private Environment environment;
    @Autowired
    private HainanYunshemeiNewProperties hainanYunshemeiNewProperties;

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
//            this.client = this.client.newBuilder()
//                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
//                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }

    /**
     * 营销活动校验与短信验证码下发接口
     *
     * @param phone
     * @param channel
     * @return
     */
    private @Nonnull
    HainanYunshemeiNewResult sendValidateCodeByOut(String phone, String channel) {
        HainanYunshemeiNewConfig hainanYunshemeiNewConfig = hainanYunshemeiNewProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("sendSms");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("operatorId", "3");
        dataNode.put("productId", hainanYunshemeiNewConfig.getProductId());
        dataNode.put("channelId", hainanYunshemeiNewConfig.getChannelId());
        dataNode.put("packageId", hainanYunshemeiNewConfig.getPackageId());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-营销活动校验与短信验证码下发接口-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-营销活动校验与短信验证码下发接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, HainanYunshemeiNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-营销活动校验与短信验证码下发接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return HainanYunshemeiNewResult.fail();
        }
    }

    /**
     * 营销活动校验与办理接口
     *
     * @param phone
     * @param channel
     * @param code
     * @return
     */
    private @Nonnull
    HainanYunshemeiNewResult checkValidateCode(String phone, String channel, String code) {
        HainanYunshemeiNewConfig hainanYunshemeiNewConfig = hainanYunshemeiNewProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("openBusiness");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("operatorId", "3");
        dataNode.put("productId", hainanYunshemeiNewConfig.getProductId());
        dataNode.put("channelId", hainanYunshemeiNewConfig.getChannelId());
        dataNode.put("packageId", hainanYunshemeiNewConfig.getPackageId());
        dataNode.put("code", code);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-营销活动校验与办理接口-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, channel, phone, code, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-营销活动校验与办理接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, HainanYunshemeiNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-营销活动校验与办理接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return HainanYunshemeiNewResult.fail();
        }
    }

    /**
     * 产品变更校验接口
     *
     * @param phone
     * @param channel
     * @return
     */
    private @Nonnull
    HainanYunshemeiNewResult checkBusinessRule(String phone, String channel) {
        HainanYunshemeiNewConfig hainanYunshemeiNewConfig = hainanYunshemeiNewProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("checkBusinessRule");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("operatorId", "3");
        dataNode.put("productId", hainanYunshemeiNewConfig.getProductId());
        dataNode.put("channelId", hainanYunshemeiNewConfig.getChannelId());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-产品变更校验接口-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-产品变更校验接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, HainanYunshemeiNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-产品变更校验接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return HainanYunshemeiNewResult.fail();
        }
    }

    /**
     * 产品变更短信验证码下发接口
     *
     * @param phone
     * @param channel
     * @return
     */
    private @Nonnull
    HainanYunshemeiNewResult sendSmsVerifyCodeByOut(String phone, String channel) {
        HainanYunshemeiNewConfig hainanYunshemeiNewConfig = hainanYunshemeiNewProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("sendSmsVerifyCodeByOut");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("operatorId", "3");
        dataNode.put("productId", hainanYunshemeiNewConfig.getProductId());
        dataNode.put("channelId", hainanYunshemeiNewConfig.getChannelId());
        dataNode.put("offerEffectTime", DateUtils.date_sdf.get().format(new Date()));
        dataNode.put("offerType", hainanYunshemeiNewConfig.getOfferType());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-产品变更短信验证码下发接口-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-产品变更短信验证码下发接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, HainanYunshemeiNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-产品变更短信验证码下发接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return HainanYunshemeiNewResult.fail();
        }
    }

    /**
     * 产品变更办理接口
     *
     * @param phone
     * @param channel
     * @param code
     * @return
     */
    private @Nonnull
    HainanYunshemeiNewResult authCheckChangeProductReg(String phone, String channel, String code) {
        HainanYunshemeiNewConfig hainanYunshemeiNewConfig = hainanYunshemeiNewProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("authCheckChangeProductReg");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("operatorId", "3");
        dataNode.put("productId", hainanYunshemeiNewConfig.getProductId());
        dataNode.put("channelId", hainanYunshemeiNewConfig.getChannelId());
        dataNode.put("code", code);
        dataNode.put("modifyTag", "0");
        dataNode.put("bookingTag", "0");
        dataNode.put("startDate", DateUtils.date_sdf.get().format(new Date()));
        dataNode.put("offerType", hainanYunshemeiNewConfig.getOfferType());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-产品变更办理接口-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, channel, phone, code, httpUrl.toString());
        ObjectNode resultNode = mapper.createObjectNode();
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-产品变更办理接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, HainanYunshemeiNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-产品变更办理接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return HainanYunshemeiNewResult.fail();
        }
    }

    public HainanYunshemeiNewResult getSms(String phone, String channel) {
        if (isMarketProduct(channel)) {
            return sendValidateCodeByOut(phone, channel);
        }
        return checkBusinessRule(phone, channel).isOK() ? sendSmsVerifyCodeByOut(phone, channel) : HainanYunshemeiNewResult.fail();
    }

    public HainanYunshemeiNewResult smsCode(String phone, String channel, String code) {
        if (isMarketProduct(channel)) {
            return checkValidateCode(phone, channel, code);
        }
        return authCheckChangeProductReg(phone, channel, code);
    }

    /**
     * 判断是否为营销产品
     *
     * @param channel
     * @return
     */
    private boolean isMarketProduct(String channel) {
        return BizConstant.BIZ_CHANNEL_HNYD_YDSXX_PLUS.equals(channel);
    }


    private HttpUrl getCommonParam(String method) {
        final HttpUrl httpUrl = HttpUrl.parse(hainanYunshemeiNewProperties.getApiBaseUrl() + "/" + method)
                .newBuilder()
                .build();
        return httpUrl;
    }
}
