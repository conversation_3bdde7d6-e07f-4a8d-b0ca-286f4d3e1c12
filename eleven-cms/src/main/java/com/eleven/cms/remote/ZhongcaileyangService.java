package com.eleven.cms.remote;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.eleven.cms.config.YidongVrbtCrackConfig;
import com.eleven.cms.config.ZhongcaiLeyangSichuanYidongConfig;
import com.eleven.cms.config.ZhongcaiLeyangSichuanYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2024-08-22 09:28
 */
@Slf4j
@Service
public class ZhongcaileyangService {

    private OkHttpClient client;

    private ObjectMapper mapper;
    @Autowired
    private Environment environment;
    @Autowired
    private ZhongcaiLeyangSichuanYidongProperties zhongcaiLeyangSichuanYidongProperties;


    public static final String LOG_TAG = "中财乐扬api";

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                .compressed()
                .insecure()  //不检查自签名证书
                //.connectTimeout(120)
                //.retry(5)
                .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println, options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public boolean queryOrder(String channel, String mobile) {
        ZhongcaiLeyangSichuanYidongConfig zhongcaiLeyangSichuanYidongConfig = zhongcaiLeyangSichuanYidongProperties.getChannelConfigMap().get(channel);
        final HttpUrl httpUrl = HttpUrl.parse(zhongcaiLeyangSichuanYidongProperties.getQueryOrderUrl())
            .newBuilder()
            .addQueryParameter("phone", aesEncryptHex(zhongcaiLeyangSichuanYidongConfig.getAesKey(), zhongcaiLeyangSichuanYidongConfig.getAesIv(), mobile))
            .addQueryParameter("uProvinceCode", zhongcaiLeyangSichuanYidongConfig.getUprovinceCode())
            .addQueryParameter("channelPlatform", zhongcaiLeyangSichuanYidongConfig.getPlatformId())
            .build();
        log.info("{}-查询订购记录-渠道号:{},手机号:{},渠道号:{},请求:{}", LOG_TAG, channel, mobile, channel, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询订购记录-渠道号:{},手机号:{},渠道号:{},响应:{}", LOG_TAG, channel, mobile, channel, result);
            JsonNode jsonNode = mapper.readTree(result);
            return 1 == jsonNode.at("/data/orderExistStatus").asInt();
        } catch (Exception e) {
            log.info("{}-查询订购记录-渠道号:{},手机号:{},渠道号:{},异常:", LOG_TAG, channel, mobile, channel, e);
            return false;
        }

    }

    /**
     * 中财乐扬手机号加密
     *
     * @param aes_key
     * @param aes_iv
     * @param content
     * @return
     */
    public static String aesEncryptHex(String aes_key,
                                       String aes_iv, String content) {
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, aes_key.getBytes(CharsetUtil.CHARSET_UTF_8),
            aes_iv.getBytes(CharsetUtil.CHARSET_UTF_8));
        return aes.encryptHex(content);
    }
}
