package com.eleven.cms.remote;

import com.eleven.cms.config.HebeiYidongConfig;
import com.eleven.cms.config.HebeiYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HebeiYidongQueryResult;
import com.eleven.cms.vo.HebeiYidongResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 河北移动业务
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class HebeiYidongService {

    @Autowired
    private Environment environment;
    @Autowired
    private HebeiYidongProperties hebeiYidongProperties;
    @Autowired
    @Lazy
    private HebeiYidongService hebeiYidongService;

    public static final String LOG_TAG = "河北移动api";

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);


    private OkHttpClient client;
    private ObjectMapper mapper;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance()
                .newBuilder()
                .connectTimeout(20L, TimeUnit.SECONDS)
                .readTimeout(25L, TimeUnit.SECONDS)
                .writeTimeout(20L, TimeUnit.SECONDS)
                .build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                    .compressed()
                    .insecure()  //不检查自签名证书
                    //.connectTimeout(120)
                    //.retry(5)
                    .build();
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .addNetworkInterceptor(new CurlInterceptor(System.out::println, options))
                    .build();
        }
        this.client.newBuilder().connectTimeout(20, TimeUnit.SECONDS).build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败


    /**
     * 获取token
     *
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.HBYD_TOKEN_CACHE, key = "#root.methodName", unless = "#result==null")
    @Retryable(maxAttempts = 3, backoff = @Backoff(delay = 1000L, multiplier = 1.5))
    public @Nonnull
    String getToken() {
        FormBody formBody = new FormBody.Builder()
                .add("grant_type", "client_credentials")
                .add("client_id", hebeiYidongProperties.getAppId())
                .add("client_secret", hebeiYidongProperties.getAppKey()).build();

        Request request = new Request.Builder()
                .url(hebeiYidongProperties.getTokenUrl())
                .header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
                .post(formBody)
                .build();
        log.info("{}-获取token,请求:{}", LOG_TAG, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取token,响应:{}", LOG_TAG, content);
            JsonNode jsonNode = mapper.readTree(content);
            //{"access_token":"0e5c9c55025ac9ea0098hc99f2106fba708d7135","expires_in":3600}
            String accessToken = jsonNode.at("/access_token").asText();
            return accessToken;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-获取token,异常:", LOG_TAG, e);
            throw new RuntimeException("网络异常");
        }
    }

    /**
     * 请求短信验证码
     * {"res_code":"0","res_desc":"Processing the request succeeded!","IsYD":"1","ProvName":"�ӱ�","Region":"319","RegionName":"��̨","UserSatus":"US10"}
     *
     * @param phone
     * @return
     */
    public @Nonnull
    JsonNode queryRegion(String phone) {
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("TelNum", phone);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder()
                .url(getCommonParam("QryUserInfoFW", hebeiYidongService.getToken()))
                .header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
                .post(body)
                .build();
        log.info("{}-查询用户信息-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            JsonNode jsonNode = mapper.readTree(new InputStreamReader(response.body().byteStream(), "GBK"));
            log.info("{}-查询用户信息-手机号:{},响应:{}", LOG_TAG, phone, jsonNode);
            return jsonNode;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-查询用户信息-手机号:{},异常:", LOG_TAG, phone, e);
            return null;
        }
    }


    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    HebeiYidongResult getSms(String phone, String channel) {
        ObjectNode dataNode = mapper.createObjectNode();
        HebeiYidongConfig hebeiYidongConfig = hebeiYidongProperties.getChannelConfigMap().get(channel);
        dataNode.put("TELNUM", phone);
        String goodsId = hebeiYidongConfig.getGoodsId();
        dataNode.put("GOODSID", goodsId);
        if (StringUtils.startsWith(goodsId, "OFFER")) {
            dataNode.put("CMDID", "PTCommonOrderCommit");
        } else {
            dataNode.put("CMDID", "IntAppendProductRec");
        }
        dataNode.put("TEMPLATENO", "RNDPWD_BUSIINFO");
        dataNode.put("CHANNELID", hebeiYidongConfig.getChannelId());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder()
                .url(getCommonParam("SendSmsRandomPassForGoods", hebeiYidongService.getToken()))
                .post(body)
                .build();
        log.info("{}-获取短信-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone, channel, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            HebeiYidongResult hebeiYidongResult = mapper.readValue(new InputStreamReader(response.body().byteStream(), "GBK"), HebeiYidongResult.class);
            log.info("{}-获取短信-手机号:{},渠道号:{},响应:{}", LOG_TAG, phone, channel, hebeiYidongResult.toString());
            return hebeiYidongResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},渠道号:{},异常:", LOG_TAG, phone, channel, e);
            return HebeiYidongResult.fail();
        }
    }


    // {"code":0,"msg":"办理成功","data":{"res_code":"0","res_desc":"Processing the request succeeded!","result":{"TRADEFORMNUM":"311220217268806262","DETAILINFO":[],"retInnerMsg":"Processing the request succeeded!"}}}
    // 0成功 -1失败

    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    HebeiYidongResult smsCode(String phone, String smsCode, String channel) {
        JsonNode jsonNode = queryRegion(phone);
        if (jsonNode == null) {
            log.error("手机号:{}获取用户信息失败", phone);
            return HebeiYidongResult.fail();
        }

        HebeiYidongConfig hebeiYidongConfig = hebeiYidongProperties.getChannelConfigMap().get(channel);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("CMDID", "PTCommonOrderCommit");
        dataNode.put("log", true);
        dataNode.put("CHANNELID", hebeiYidongConfig.getChannelId());
        dataNode.put("seqid", "");
        dataNode.put("outTime", "30000");
        ObjectNode customerOrder = mapper.createObjectNode();
        customerOrder.put("supportProdNotLoadNcodeMap", "1");
        customerOrder.put("SMSRANDOMPASS", smsCode);
        customerOrder.put("operID", cityOperIdMap.get(jsonNode.at("/RegionName").asText()));
        customerOrder.put("serverNumber", phone);
        customerOrder.put("outerOrderID", String.valueOf(System.currentTimeMillis()));
        customerOrder.put("accessType", hebeiYidongConfig.getChannelId());
        customerOrder.put("region", jsonNode.at("/Region").asText());
        customerOrder.put("isNotify", "1");

        ArrayNode orderRecListNode = mapper.createArrayNode();
        ObjectNode orderRecNode = mapper.createObjectNode();
        orderRecNode.put("recType", "ChangeProduct");
        ArrayNode offeringListNode = mapper.createArrayNode();
        ObjectNode offeringNode = mapper.createObjectNode();
        offeringNode.put("actionType", "A");
        offeringNode.put("offerType", "prod");
        offeringNode.put("offerCode", hebeiYidongConfig.getGoodsId());
        offeringNode.put("effectType", "2");
        offeringListNode.add(offeringNode);
        orderRecNode.put("offeringList", offeringListNode);
        orderRecListNode.add(orderRecNode);
        customerOrder.put("orderRecList", orderRecListNode);
        dataNode.put("customerOrder", customerOrder);


        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder()
                .url(getCommonParam("PTCommonOrderCommitForCP", hebeiYidongService.getToken()))
                .post(body)
                .build();
        log.info("{}-提交短信-手机号:{},渠道号:{},验证码:{},请求:{}", LOG_TAG, phone, channel, smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            HebeiYidongResult hebeiYidongResult = mapper.readValue(new InputStreamReader(response.body().byteStream(), "GBK"), HebeiYidongResult.class);
            log.info("{}-提交短信-手机号:{},渠道号:{},验证码:{},响应:{}", LOG_TAG, phone, channel, smsCode, hebeiYidongResult.toString());
            return hebeiYidongResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},渠道号:{},验证码:{},异常:", LOG_TAG, phone, channel, smsCode, e);
            return HebeiYidongResult.fail();
        }
    }

    public HttpUrl getCommonParam(String method, String token) {
        final HttpUrl httpUrl = HttpUrl.parse(hebeiYidongProperties.getBaseServiceUrl())
                .newBuilder()
                .addQueryParameter("access_token", token)
                .addQueryParameter("method", method)
                .addQueryParameter("format", "json").build();
        return httpUrl;
    }



    public static final Map<String, String> cityOperIdMap = new ImmutableMap.Builder<String, String>()
        .put("秦皇岛", "qhyouran")
        .put("沧州", "czyouran")
        .put("廊坊", "lfyouran")
        .put("邯郸", "hdyouran")
        .put("石家庄", "sjyouran")
        .put("唐山", "tsyouran")
        .put("张家口", "ggy744")
        .put("承德", "hb_d8121")
        .put("雄安", "xayouran")
        .put("保定", "bdyouran")
        .put("衡水", "hsyouran")
        .put("邢台", "xtyouran")
        .build();

    public @Nonnull
    HebeiYidongQueryResult getRecommendedOfferNew(String phone, String channel) {
        JsonNode jsonNode = queryRegion(phone);
        if (jsonNode == null) {
            log.error("手机号:{}获取用户信息失败", phone);
            return HebeiYidongQueryResult.fail();
        }

        HebeiYidongConfig hebeiYidongConfig = hebeiYidongProperties.getChannelConfigMap().get(channel);
        ObjectNode dataNode = mapper.createObjectNode();
        ObjectNode eventBodyNode = mapper.createObjectNode();
        ObjectNode requestHeaderNode = mapper.createObjectNode();

        requestHeaderNode.put("accessChannel", "102188");
        requestHeaderNode.put("beId", "101");
        requestHeaderNode.put("language", "2");
        requestHeaderNode.put("operator", "Campaign");
        requestHeaderNode.put("password", "q3geiItxj4ljNLkI6OINDA==");
        requestHeaderNode.put("transactionId", "112211");

        eventBodyNode.put("msisdn", phone);
        eventBodyNode.put("eventCode", "AND_LOGIN");

        dataNode.put("CHANNELID", hebeiYidongConfig.getChannelId());
        dataNode.putPOJO("requestHeader",requestHeaderNode);
        dataNode.putPOJO("eventBody",eventBodyNode);

        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        final HttpUrl httpUrl = HttpUrl.parse(hebeiYidongProperties.getGetRecommendedOfferNewUrl())
                .newBuilder()
                .addQueryParameter("access_token", hebeiYidongService.getToken())
                .addQueryParameter("method", "getRecommendedOfferNew")
                .addQueryParameter("format", "json").build();
        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .build();
        log.info("{}-营销推荐接口-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone, channel, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = new String(response.body().bytes(), "GBK");
            HebeiYidongQueryResult hebeiYidongResult = mapper.readValue(result, HebeiYidongQueryResult.class);
            log.info("{}-营销推荐接口-手机号:{},渠道号:{},响应:{}", LOG_TAG, phone, channel, result);
            return hebeiYidongResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-营销推荐接口-手机号:{},渠道号:{},异常:", LOG_TAG, phone, e);
            return HebeiYidongQueryResult.fail();
        }
    }

    public boolean verify(String phone, String channel){
        HebeiYidongQueryResult hebeiYidongQueryResult = this.getRecommendedOfferNew(phone, channel);
        if(hebeiYidongQueryResult.isOK()
                && CollectionUtils.isEmpty(hebeiYidongQueryResult.getResult())){
            return true;
        }
        if(hebeiYidongQueryResult.isOK()
                && !CollectionUtils.isEmpty(hebeiYidongQueryResult.getResult())
                && hebeiYidongQueryResult.getResult().get(0).getOfferAttrMap() != null){
            for (Map<String, String> map : hebeiYidongQueryResult.getResult().get(0).getOfferAttrMap()) {
                //话费大于30
                if("campaignId".equals(map.get("key")) && "20241009484676".equals(map.get("value"))){
                    return true;
                }
            }
        }
        return false;
    }
}
