package com.eleven.cms.remote;
import com.eleven.cms.config.HenanYidongConfig;
import com.eleven.cms.config.HenanYidongProperties;
import com.eleven.cms.entity.AdSiteBusinessConfig;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.FileUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.henan.SecurityUtils;
import com.eleven.cms.util.henan.SignUtil;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import com.wechat.pay.java.core.http.UrlEncoder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023-9-20 17:14:54
 */
@Component
@Slf4j
public class HenanYidongGaojieService  implements IHenanYidongService {

    public static final String LOG_TAG = "河南移动-api";
    public static final String API_ID = "1126";
    public static final String LANDINGPAGE_URL = "https://crbt.cdyrjygs.com/mobile_provinces/henan_5gllb";
    public static final String GAOJIE_LANDINGPAGE_URL = "https://videoring.sczyhl.cn/event/henanchezhufuli/index";

    @Autowired
    private HenanYidongProperties henanYidongProperties;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private String drainageImgFileToBase64;
    private String landingPageImgFileToBase64;
    private String confirmPageImgFileToBase64;

    private String gaojieDrainageImgFileToBase64;
    private String gaojieLandingPageImgFileToBase64;
    private String gaojieConfirmPageImgFileToBase64;

    private String gaojieDrainageImgFileToBase64Llb;
    private String gaojieLandingPageImgFileToBase64Llb;
    private String gaojieConfirmPageImgFileToBase64Llb;

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder()
                .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.mirrorProxyHost, OkHttpClientUtils.mirrorProxyPort)))
                .connectTimeout(20, TimeUnit.SECONDS)
                .build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        drainageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/drainageImg.jpg"));
        landingPageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/landingPageImg.jpg"));
        confirmPageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/confirmPageImg.png"));
        //高姐图片
        gaojieDrainageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/gaojieDrainageImg.jpg"));
        gaojieLandingPageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/gaojieLandingPageImg.jpg"));
        gaojieConfirmPageImgFileToBase64 = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/gaojieConfirmPageImg.png"));

        gaojieDrainageImgFileToBase64Llb = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/gaojie/gaojieDrainageImg.jpg"));
        gaojieLandingPageImgFileToBase64Llb = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/gaojie/gaojieLandingPageImg.jpg"));
        gaojieConfirmPageImgFileToBase64Llb = FileUtil.getImgFileToBase64(new ClassPathResource("biz_images/gaojie/gaojieConfirmPageImg.png"));
    }

    @Override
    public HenanMobileMarketResult queryMarket(String phone, String channel) {

        try{
            HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
            ObjectNode body = mapper.createObjectNode();
            body.put("svcNum", phone);
            HenanMobileResult henanMobileResult = request("UIP_FX_QUERY_MARKET", body, channel,phone);
            if(henanMobileResult != null && henanMobileResult.isOK()){
                String result = SecurityUtils.decodeAES256HexUpper(henanMobileResult.getResult(),
                        SecurityUtils.decodeHexUpper(config.getAppKey()));
                if(StringUtils.isNotBlank(result)){
                    log.info("{}-通过手机号查询用户可办理的套餐列表和活动列表,手机号:{},result:{}",LOG_TAG,phone,result);
                    return mapper.readValue(result, HenanMobileMarketResult.class);
                }
            }
        }catch (Exception e){
            log.error("{}-通过手机号查询用户可办理的套餐列表和活动列表出错,手机号:{},异常:",LOG_TAG,phone,e);
        }
        return HenanMobileMarketResult.fail();
    }

    @Override
    public HenanMobileCheckResult queryActive(String phone, String channel) {

        try{
            HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
            ObjectNode body = mapper.createObjectNode();
            body.put("svcNum", phone);
            body.put("offerId", config.getOfferId());
            HenanMobileResult henanMobileResult = request("UIP_FX_ACTIVITY_CHECK", body, channel,phone);
            if(henanMobileResult != null && henanMobileResult.isOK()){
                String result = SecurityUtils.decodeAES256HexUpper(henanMobileResult.getResult(),
                        SecurityUtils.decodeHexUpper(config.getAppKey()));
                if(StringUtils.isNotBlank(result)){
                    log.info("{}-活动办理前置校验,手机号:{},result:{}",LOG_TAG,phone,result);
                    return mapper.readValue(result, HenanMobileCheckResult.class);
                }
            }
        }catch (Exception e){
            log.error("{}-活动办理前置校验出错,手机号:{},异常:",LOG_TAG,phone,e);
        }
        return HenanMobileCheckResult.fail();
    }

    @Override
    public HenanMobileCheckResult queryLlbActive(String phone, String channel) {

        try{
            HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
            ObjectNode body = mapper.createObjectNode();
            body.put("svcNum", phone);
            body.put("offerId", config.getOfferId());
            HenanMobileResult henanMobileResult = request("UIP_FX_ZZCPLLB_CHECK", body, channel,phone);
            if(henanMobileResult != null && henanMobileResult.isOK()){
                String result = SecurityUtils.decodeAES256HexUpper(henanMobileResult.getResult(),
                        SecurityUtils.decodeHexUpper(config.getAppKey()));
                if(StringUtils.isNotBlank(result)){
                    log.info("{}-流量包活动办理前置校验,手机号:{},result:{}",LOG_TAG,phone,result);
                    return mapper.readValue(result, HenanMobileCheckResult.class);
                }
            }
        }catch (Exception e){
            log.error("{}-流量包活动办理前置校验出错,手机号:{},异常:",LOG_TAG,phone,e);
        }
        return HenanMobileCheckResult.fail();
    }

    @Override
    public HenanMobileSmsResult sendSms(String phone,String channel) {

        try{
            HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
            ObjectNode body = mapper.createObjectNode();
            body.put("svcNum", phone);
            body.put("offerId", config.getOfferId());
            HenanMobileResult henanMobileResult = request("UIP_FX_ACTIVITY_GETCODE", body, channel,phone);
            if(henanMobileResult != null && henanMobileResult.isOK()){
                String result = SecurityUtils.decodeAES256HexUpper(henanMobileResult.getResult(),
                        SecurityUtils.decodeHexUpper(config.getAppKey()));
                if(StringUtils.isNotBlank(result)){
                    log.info("{}-获取办理营销活动的短信验证码,手机号:{},result:{}",LOG_TAG,phone,result);
                    return mapper.readValue(result, HenanMobileSmsResult.class);
                }
            }
        }catch (Exception e){
            log.error("{}-获取办理营销活动的短信验证码出错,手机号:{},异常:",LOG_TAG,phone,e);
        }
        return HenanMobileSmsResult.fail();
    }

    @Override
    public HenanMobileSmsResult sendLlbSms(String phone,String channel) {

        try{
            HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
            ObjectNode body = mapper.createObjectNode();
            body.put("svcNum", phone);
            body.put("offerId", config.getOfferId());
            HenanMobileResult henanMobileResult = request("UIP_FX_LLB_GETCODE", body, channel,phone);
            if(henanMobileResult != null && henanMobileResult.isOK()){
                String result = SecurityUtils.decodeAES256HexUpper(henanMobileResult.getResult(),
                        SecurityUtils.decodeHexUpper(config.getAppKey()));
                if(StringUtils.isNotBlank(result)){
                    log.info("{}-流量包获取办理营销活动的短信验证码,手机号:{},result:{}",LOG_TAG,phone,result);
                    return mapper.readValue(result, HenanMobileSmsResult.class);
                }
            }
        }catch (Exception e){
            log.error("{}-流量包获取办理营销活动的短信验证码出错,手机号:{},异常:",LOG_TAG,phone,e);
        }
        return HenanMobileSmsResult.fail();
    }

    @Override
    public HenanMobileSmsResult smsCode(String phone, String orderId, String smsCode, String channel,String referer) {
        String landingPageUrl = LANDINGPAGE_URL;
        try{
            //通过app包名查询app名称
            if(StringUtils.isNotBlank(referer)){
                List<AdSiteBusinessConfig> list = adSiteBusinessConfigService.lambdaQuery().eq(AdSiteBusinessConfig::getAdSite, referer).list();
                for (AdSiteBusinessConfig adSiteBusinessConfig : list) {
                    if(StringUtils.isNotBlank(adSiteBusinessConfig.getAppName())){
                        referer = adSiteBusinessConfig.getAppName();
                    }
                }
            }
            HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
            if(config.getApiId().equals(API_ID)){
                if(BizConstant.BIZ_TYPE_HNYZ_50GLLB.equals(channel)){
                    drainageImgFileToBase64 = gaojieDrainageImgFileToBase64Llb;
                    landingPageImgFileToBase64 = gaojieLandingPageImgFileToBase64Llb;
                    confirmPageImgFileToBase64 = gaojieConfirmPageImgFileToBase64Llb;
                }else{
                    drainageImgFileToBase64 = gaojieDrainageImgFileToBase64;
                    landingPageImgFileToBase64 = gaojieLandingPageImgFileToBase64;
                    confirmPageImgFileToBase64 = gaojieConfirmPageImgFileToBase64;
                }
                landingPageUrl = GAOJIE_LANDINGPAGE_URL;
            }
            ObjectNode body = mapper.createObjectNode();
            body.put("svcNum", phone);
            body.put("orderId", orderId);
            body.put("smsCode", smsCode);
            body.put("contactPoint", StringUtils.isBlank(referer) ? "com.tencent.mm" : referer);//办理触点
            body.put("landingPageUrl", UrlEncoder.urlEncode(landingPageUrl));//落地页Url
            body.put("drainageImg", UrlEncoder.urlEncode(drainageImgFileToBase64));//图片凭证-引流图图片
            body.put("landingPageImg", UrlEncoder.urlEncode(landingPageImgFileToBase64));//图片凭证-落地页图片
            body.put("confirmPageImg", UrlEncoder.urlEncode(confirmPageImgFileToBase64));//图片凭证-确认页面图片
            HenanMobileResult henanMobileResult = request("UIP_FX_BUSI_CONFIRM", body, channel,phone);
            if(henanMobileResult != null && henanMobileResult.isOK()){
                String result = SecurityUtils.decodeAES256HexUpper(henanMobileResult.getResult(),
                        SecurityUtils.decodeHexUpper(config.getAppKey()));
                if(StringUtils.isNotBlank(result)){
                    log.info("{}-通过短信验证码确认办理业务,手机号:{},smsCode:{},orderId:{},result:{},contactPoint:{}",LOG_TAG,phone,smsCode,orderId,result,referer);
                    return mapper.readValue(result, HenanMobileSmsResult.class);
                }
            }
        }catch (Exception e){
            log.error("{}-通过短信验证码确认办理业务出错,手机号:{},异常:",LOG_TAG,phone,e);
        }
        return HenanMobileSmsResult.fail();
    }

    private HenanMobileResult request(String methodName, ObjectNode busiParam, String channel,String mobile) {

        ObjectNode body = mapper.createObjectNode();
        ObjectNode sysParam = mapper.createObjectNode();
        HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
        sysParam.put("busiCode", methodName);
        sysParam.put("sourceId", config.getApiId());
        sysParam.put("format", "json");
        body.set("sysparams", sysParam);
        body.set("busiparams", busiParam);
        try {
            String text = SignUtil.sign(token(channel), body, sysParam, config);
            String baseUrl = config.getBaseUrl();
            String query = body.get("query").asText();
            RequestBody requestBody = RequestBody.create(JSON, text);
            HttpUrl httpUrl = HttpUrl.parse(baseUrl + query)
                    .newBuilder()
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .post(requestBody)
                    .build();
            log.info("{}-" + methodName + "-手机号:{},请求:{}", LOG_TAG,mobile, httpUrl.toString());
            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                HenanMobileResult henanMobileResult = mapper.readValue(content, HenanMobileResult.class);
                log.info("{}-" + methodName + "-手机号:{},响应:{}", LOG_TAG,mobile, content);
                return henanMobileResult;
            }
        } catch (Exception e) {
            log.info("{}-" + methodName + "-手机号:{},异常:", LOG_TAG,mobile, e);
        }
        return null;
    }


    private String token(String channel) {
        String token = (String) redisUtil.get("henan-access-token-" + channel);
        if (StringUtils.isEmpty(token)) {
            try {
                HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
                String url = config.getTokenUrl();
                HttpUrl httpUrl = HttpUrl.parse(url)
                        .newBuilder()
                        .addQueryParameter("app_id", config.getAppId())
                        .addQueryParameter("app_key", config.getAppKey())
                        .addQueryParameter("grant_type", "client_credentials")
                        .build();

                log.info("{}-获取token接口-请求:{}", LOG_TAG, httpUrl.toString());
                Request request = new Request.Builder().url(httpUrl)
                        .build();
                try (Response response = client.newCall(request)
                        .execute()) {
                    if (!response.isSuccessful()) {
                        throw new IOException("Unexpected code " + response);
                    }
                    String content = response.body().string();
                    JsonNode tree = mapper.readTree(content);
                    token = tree.at("/access_token").asText();
                    //{"access_token":"7d9eaca8-c33e-48ba-a7a2-723857bef37e","token_type":"bearer","expires_in":26505}
                    long expiresTime = tree.at("/expires_in").asLong(0);
                    if(StringUtils.isNotBlank(token) && expiresTime > 0){
                        redisUtil.setIfAbsent("henan-access-token-" + channel,token,expiresTime);
                    }
                    log.info("{}-获取token接口-响应:{}", LOG_TAG, content);
                }
            } catch (IOException e) {
                log.warn("{}-获取token接口-异常:", LOG_TAG, e);
            }
        }

        return token;
    }

    /**
     *  查询订单信息
     * @param orderId 订单号
     * @param channel 渠道号
     * @param phone 手机号
     * @return
     */
    @Override
    public HenanMobileQueryResult queryRecord(String orderId,String channel,String phone) {

        HenanYidongConfig config = henanYidongProperties.getHenanConfigByChannel(channel);
        try{
            ObjectNode body = mapper.createObjectNode();
            body.put("orderId", orderId);
            body.put("pageNo", 1);
            body.put("idSign", config.getIdSign());
            HenanMobileResult henanMobileResult = request("UIP_MARKET_ORDER_INFO_QUERY", body, channel, phone);
            if(henanMobileResult != null && henanMobileResult.isOK()){
                String result = SecurityUtils.decodeAES256HexUpper(henanMobileResult.getResult(),
                        SecurityUtils.decodeHexUpper(config.getAppKey()));
                if(StringUtils.isNotBlank(result)){
                    log.info("{}-查询订单信息,手机号:{},result:{}",LOG_TAG,phone,result);
                    return mapper.readValue(result, HenanMobileQueryResult.class);
                }
            }
        }catch (Exception e){
            log.error("{}-查询订单信息出错,手机号:{},异常:",LOG_TAG,phone,e);
        }
        return HenanMobileQueryResult.fail();
    }

    /**
     *  查询订单状态是否为正常开通
     * @param orderId 订单号
     * @param channel 渠道号
     * @param phone 手机号
     * @return
     */
    @Override
    public Integer orderResultJudge(String orderId, String channel, String phone) {

        HenanMobileQueryResult henanMobileQueryResult = queryRecord(orderId, channel, phone);
        if(henanMobileQueryResult.isOK()
                && henanMobileQueryResult.getData() != null
                && henanMobileQueryResult.getData().getOrders() != null
                && henanMobileQueryResult.getData().getOrders().size() > 0){
            HenanMobileQueryResult.Order order = henanMobileQueryResult.getData().getOrders().get(0);
            if(order != null && "2".equals(order.getOrderStatus())){
                return BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS;
            }
        }
        return BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
    }
}