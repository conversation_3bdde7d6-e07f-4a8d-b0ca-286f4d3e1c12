package com.eleven.cms.remote;

import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.IRightsPackService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.RightBusinessEnum;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.Product;
import com.eleven.cms.vo.RightsPackDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MemberRightsExecutionThreadService {
    @Autowired
    IMemberService memberService;
    @Qualifier("threadPoolExecutor")
    ThreadPoolExecutor executor;
    @Autowired
    private IRightsPackService rightsPackService;
    /**
     * 线程执行查询是否会员功能
     * @param account
     * @return
     */
    public FebsResponse thisThreadCheckMemberRights(String account,String token)  {
//        List<FebsResponse> febsResponse = Lists.newArrayList();
//        for (RightBusinessEnum val : RightBusinessEnum.values()) {
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,val.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        }

        List<FebsResponse> febsResponse = Lists.newArrayList();
        //白金会员畅听版
//        final CompletableFuture<Void> bjhyctb = CompletableFuture.runAsync(() -> {
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account, RightBusinessEnum.BJHYCTB.getServiceId(),token);
//            febsResponse.add(response);
//        }, executor);
        //贝壳视听会员
//        final CompletableFuture<Void> bkst = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.BKST.getServiceId(),token,false);
//            febsResponse.add(response);
//        },executor);
        List<CompletableFuture<Void>> runAsyncList =Lists.newArrayList();
        for (RightBusinessEnum val : RightBusinessEnum.values()) {
            final CompletableFuture<Void> runAsync = CompletableFuture.runAsync(()->{
                FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,val.getServiceId(),token,false,0L);
                febsResponse.add(response);
            },executor);
            runAsyncList.add(runAsync);
        }
//        //趣享音乐包
//        final CompletableFuture<Void> qxyyb = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.QXYYB.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//        //炫酷来电
//        final CompletableFuture<Void> xkld = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.XKLD.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//        //白金会员
//        final CompletableFuture<Void> bjhy = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.BJHY.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//
//        //白金会员[宜搜]
//        final CompletableFuture<Void> bjhyys = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.BJHYYS.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//
//        //北岸唐唱音乐包
//        final CompletableFuture<Void> batcyyb = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.BATCYYB.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//        //音乐全曲包-七彩歌曲10元包
//        final CompletableFuture<Void> yyqqb = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.YYQQB.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//        //沃音乐
//        final CompletableFuture<Void> wyy = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.WYY.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//        //10元电信视频彩铃
//        final CompletableFuture<Void> ddxspcl = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.DDXSPCL.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//
//        //藕粉咪咕同享会10元包
//        final CompletableFuture<Void> ofmgtxh = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.OFMGTXH.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//
////        //音乐全曲包-趣享音乐20元包
////        final CompletableFuture<Void> yyqqbqxyy = CompletableFuture.runAsync(()->{
////            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.YYQQBQXYY.getServiceId(),token,false,0L);
////            febsResponse.add(response);
////        },executor);
//
//        //音乐全曲包-经典音乐15元包
//        final CompletableFuture<Void> yyqqbjdyy = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.YYQQBJDYY.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//
//
//        //休闲10元包
//        final CompletableFuture<Void> hy10xx = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.HY_10_XX.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);
//
//        //畅玩25元包
//        final CompletableFuture<Void> hy25cw = CompletableFuture.runAsync(()->{
//            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,RightBusinessEnum.HY_25_CW.getServiceId(),token,false,0L);
//            febsResponse.add(response);
//        },executor);

        try {
            CompletableFuture.allOf(runAsyncList.toArray(new CompletableFuture[runAsyncList.size()])).get();
//              CompletableFuture.allOf(/*bjhyctb,*//*bkst,*/qxyyb,xkld,bjhy,bjhyys,batcyyb,yyqqb,wyy,ddxspcl,ofmgtxh/*,yyqqbqxyy*/,yyqqbjdyy,hy10xx,hy25cw).get();
            //判断本月是否会员并且本月没有领取权益
            boolean hasSuccess = febsResponse.stream().anyMatch(febs-> febs.isOK());
            if(hasSuccess){
                //返回本月满足会员并且本月没有领取权益数据
                return febsResponse.stream().filter(febs->febs.isOK()).collect(Collectors.toList()).get(0);
            }
            boolean isCode = febsResponse.stream().anyMatch(febs-> febs.get("code").toString().equals(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())));
            if(isCode){
                return febsResponse.stream().filter(febs-> febs.get("code").toString().equals(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())) ).collect(Collectors.toList()).get(0);
            }
            Map map= Maps.newHashMap();
            map.put("token",token);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG).data(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();
    }


    /**
     * 查询产品列表和产品订阅数据
     * @param account
     * @return
     */
    public List<Product> queryProductAndSubscribe(String account,String token)  {
        List<Product> productList = Lists.newArrayList();
        for (RightBusinessEnum val : RightBusinessEnum.values()) {
            FebsResponse  response=memberService.isMemberAndIsReceiveRights(account,val.getServiceId(),token,false,0L);
            Optional<RightsPackDto> miguPackOptional =rightsPackService.wholeMemberRightsList(val.getServiceId()).stream().max(Comparator.comparing(
                    RightsPackDto::getId));
            if(response.isOK()){
                Product product=new Product();
                product.setIsReceiveRights(0);
                product.setReceiveRightsMsg("可领取");
                if(miguPackOptional.isPresent()){
                    RightsPackDto pack=miguPackOptional.get();
                    product.setTitleName(pack.getTitleName()==null?"":pack.getTitleName());
                    product.setProductImg(pack.getProductImg()==null?"":pack.getProductImg());
                }
                product.setServiceId(val.getServiceId());
                productList.add(product);
            }else{
                //您订购视频彩铃业务未满3个月，暂时无法领取
                if(response.get("code").toString().equals(String.valueOf(HttpStatus.I_AM_A_TEAPOT.value()))){
                    Product product=new Product();
                    product.setIsReceiveRights(1);
                    product.setReceiveRightsMsg(BizConstant.NOT_YET_MSG);
                    if(miguPackOptional.isPresent()){
                        RightsPackDto pack=miguPackOptional.get();
                        product.setTitleName(pack.getTitleName()==null?"":pack.getTitleName());
                        product.setProductImg(pack.getProductImg()==null?"":pack.getProductImg());
                    }
                    product.setServiceId(val.getServiceId());
                    productList.add(product);
                }
                //会员权益已领取
                if(response.get("code").toString().equals(String.valueOf(HttpStatus.PRECONDITION_FAILED.value()))){
                    Product product=new Product();
                    product.setIsReceiveRights(1);
                    product.setReceiveRightsMsg(BizConstant.YETGET_MSG);
                    if(miguPackOptional.isPresent()){
                        RightsPackDto pack=miguPackOptional.get();
                        product.setTitleName(pack.getTitleName()==null?"":pack.getTitleName());
                        product.setProductImg(pack.getProductImg()==null?"":pack.getProductImg());
                    }
                    product.setServiceId(val.getServiceId());
                    productList.add(product);
                }
                //本月会员权益你已领取,请下月再来领取
                if(response.get("code").toString().equals(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value()))){
                    Product product=new Product();
                    product.setIsReceiveRights(1);
                    product.setReceiveRightsMsg(BizConstant.RECEIVE_MSG);
                    if(miguPackOptional.isPresent()){
                        RightsPackDto pack=miguPackOptional.get();
                        product.setTitleName(pack.getTitleName()==null?"":pack.getTitleName());
                        product.setProductImg(pack.getProductImg()==null?"":pack.getProductImg());
                    }
                    product.setServiceId(val.getServiceId());
                    productList.add(product);
                }
            }
        }
        return productList;
    }

}
