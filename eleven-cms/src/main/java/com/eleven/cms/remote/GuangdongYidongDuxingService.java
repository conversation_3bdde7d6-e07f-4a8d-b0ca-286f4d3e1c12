package com.eleven.cms.remote;

import com.eleven.cms.config.GuangdongYidongDuxingProperties;
import com.eleven.cms.config.GuangdongYidongYueyueProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.GuangdongYidongDuxingResult;
import com.eleven.cms.vo.GuangdongYidongYueyueResult;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2022-08-12 10:58
 */
@Slf4j
@Service
public class GuangdongYidongDuxingService {

    public static final String LOG_TAG = "广东移动笃行视频彩铃api";


    @Autowired
    private Environment environment;
    @Autowired
    private GuangdongYidongDuxingProperties guangdongYidongDuxingProperties;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private MiguApiService miguApiService;

    public static final MediaType JSONTYPE
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }

    //{"status":11,"msg":"成功","ordernum":"4028827c84c2c4950184c2c4958d0000","sysordernum":"15b10c40c36d3048e8e87f79ce089dce","dataobj":{"phonenum":"15915401656"}}
    public @Nonnull
    GuangdongYidongDuxingResult getSms(String phone) {
        final HttpUrl httpUrl = HttpUrl.parse(guangdongYidongDuxingProperties.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("phone", phone)
                .addQueryParameter("type", "400")
                .addQueryParameter("jointoken", guangdongYidongDuxingProperties.getJointoken())
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, GuangdongYidongDuxingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return GuangdongYidongDuxingResult.fail();
        }
    }

    public @Nonnull
    GuangdongYidongDuxingResult smsCode(String phone, String code)  {
        final HttpUrl httpUrl = HttpUrl.parse(guangdongYidongDuxingProperties.getSmsCodeUrl())
                .newBuilder()
                .addQueryParameter("phone", phone)
                .addQueryParameter("code", code)
                .addQueryParameter("type", "400")
                .addQueryParameter("jointoken", guangdongYidongDuxingProperties.getJointoken())
                .build();
        log.info("{}-提交验证码-手机号:{},短信验证码:{},请求:{}", LOG_TAG, phone,code, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-手机号:{},短信验证码:{},响应:{}", LOG_TAG, phone, code,content);
            return mapper.readValue(content, GuangdongYidongDuxingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-手机号:{},短信验证码:{},异常:", LOG_TAG, phone,code, e);
            return GuangdongYidongDuxingResult.fail();
        }
    }
    
}
