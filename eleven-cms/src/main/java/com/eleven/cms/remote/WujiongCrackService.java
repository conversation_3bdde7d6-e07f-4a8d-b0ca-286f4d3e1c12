package com.eleven.cms.remote;

import com.eleven.cms.config.TianyiSpaceProperties;
import com.eleven.cms.config.WujiongCrackConfig;
import com.eleven.cms.config.WujiongCrackProperties;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.ITelecomOrderService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.KuaimaMiniAppResult;
import com.eleven.cms.vo.WujiongCrackResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2022-06-24 10:28
 */
@Slf4j
@Service
public class WujiongCrackService {
    public static final String LOG_TAG = "wujiong专属api";

    @Autowired
    WujiongCrackProperties wujiongCrackProperties;

    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    ISubscribeService subscribeService;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public WujiongCrackResult getSms(String phone, String channel) {
        WujiongCrackConfig wujiongCrackConfig = wujiongCrackProperties.getConfig(channel);
        String url = wujiongCrackConfig.getGetSmsUrl();
        HttpUrl.Builder builder = HttpUrl.parse(url)
                .newBuilder()
                .addQueryParameter("tel", phone);
        if (StringUtils.isNotBlank(wujiongCrackConfig.getA())) {
            builder.addQueryParameter("a", wujiongCrackConfig.getA());
        } else {
            builder.addQueryParameter("userid", wujiongCrackConfig.getUserId());
        }
        Request request = new Request.Builder().url(builder.build()).build();
        log.info("{}-获取验证码-手机号:{},渠道号:{},请求:{}", wujiongCrackConfig.getLogTag(), phone, channel, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取验证码-手机号:{},渠道号:{},返回结果:{}", wujiongCrackConfig.getLogTag(), phone, channel, result);
            return mapper.readValue(result, WujiongCrackResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-获取验证码-手机号:{},渠道号:{},异常:", wujiongCrackConfig.getLogTag(), phone, channel, e);
            return WujiongCrackResult.FAIL_RESULT;
        }
    }


    public WujiongCrackResult smsCode(String phone, String orderId, String code, String channel){
        WujiongCrackConfig wujiongCrackConfig = wujiongCrackProperties.getConfig(channel);
        String url = wujiongCrackConfig.getSmsCodeUrl();
        HttpUrl.Builder builder = HttpUrl.parse(url).newBuilder();
        if(url.contains("dx/music")){
            builder.addQueryParameter("tradeid",orderId);
            builder.addQueryParameter("vcode",code);
        }else {
            builder.addQueryParameter("verifycode", code);
            if (StringUtils.isNotBlank(wujiongCrackConfig.getA())) {
                builder.addQueryParameter("sid", orderId);
            } else {
                builder.addQueryParameter("orderid", orderId);
            }
        }
        Request request = new Request.Builder().url(builder.build()).build();
        log.info("{}-提交验证码-手机号:{},订单号:{},渠道号:{},请求:{}", wujiongCrackConfig.getLogTag(), phone, orderId, channel, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},订单号:{},渠道号:{},返回结果:{}", wujiongCrackConfig.getLogTag(), phone, orderId, channel, result);
            return mapper.readValue(result, WujiongCrackResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-提交验证码-手机号:{},订单号:{},渠道号:{},异常:", wujiongCrackConfig.getLogTag(), phone, orderId, channel, e);
            return WujiongCrackResult.FAIL_RESULT;
        }
    }


}
