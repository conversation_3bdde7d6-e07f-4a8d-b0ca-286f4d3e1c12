package com.eleven.cms.remote;

import com.eleven.cms.config.HebeiYidongVrbtConfig;
import com.eleven.cms.config.HebeiYidongVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HebeiMobileResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 河北易动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class HebeiYidongVrbtService {

    @Autowired
    private Environment environment;
    @Autowired
    private HebeiYidongVrbtProperties hebeiYidongVrbtProperties;


    private OkHttpClient client;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }



    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    HebeiMobileResult getSms(String phone,String company) {
        HebeiYidongVrbtConfig hebeiYidongVrbtConfig = hebeiYidongVrbtProperties.getHebeiMobileConfig(company);
        FormBody.Builder build = new FormBody.Builder()
                .add("mobile", phone);
        if(StringUtils.isNotEmpty(hebeiYidongVrbtConfig.getBusCode())){
            build.add("busCode", hebeiYidongVrbtConfig.getBusCode());
        }
        build.add("businame", "视频彩铃抖音流量合约年包");
        FormBody body = build.build();
        Request request = new Request.Builder()
                .url(hebeiYidongVrbtProperties.getGetSmsUrl())
                .post(body)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", hebeiYidongVrbtConfig.getLogTag(), phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", hebeiYidongVrbtConfig.getLogTag(), phone, content);
            return mapper.readValue(content, HebeiMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", hebeiYidongVrbtConfig.getLogTag(), phone, e);
            return HebeiMobileResult.fail();
        }
    }


   // {"code":0,"msg":"办理成功","data":{"res_code":"0","res_desc":"Processing the request succeeded!","result":{"TRADEFORMNUM":"311220217268806262","DETAILINFO":[],"retInnerMsg":"Processing the request succeeded!"}}}
    // 0成功 -1失败
    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    HebeiMobileResult smsCode(String phone, String smsCode,String company,String app,String ip) {
        HebeiYidongVrbtConfig hebeiYidongVrbtConfig = hebeiYidongVrbtProperties.getHebeiMobileConfig(company);
        FormBody.Builder build = new FormBody.Builder()
                .add("mobile", phone)
                .add("code", smsCode)
                .add("pid", hebeiYidongVrbtConfig.getPId())
                .add("platform", hebeiYidongVrbtConfig.getChannel() + "\\" + app)
                .add("ip", ip)
                .add("url", hebeiYidongVrbtConfig.getUrl());
        if(StringUtils.isNotEmpty(hebeiYidongVrbtConfig.getBusCode())){
            build.add("zfCode", hebeiYidongVrbtConfig.getBusCode());
        }
        FormBody body = build.build();
//        busCode	是	int	业务编码 14163
//        p_id	是	int	13
        Request request = new Request.Builder()
                .url(hebeiYidongVrbtProperties.getSendSmsUrl())
                .post(body)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},请求:{}", hebeiYidongVrbtConfig.getLogTag(), phone, smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},响应:{}", hebeiYidongVrbtConfig.getLogTag(), phone, smsCode, content);
            return mapper.readValue(content, HebeiMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},异常:", hebeiYidongVrbtConfig.getLogTag(), phone, smsCode, e);
            return HebeiMobileResult.fail();
        }
    }
}
