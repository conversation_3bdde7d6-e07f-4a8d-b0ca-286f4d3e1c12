package com.eleven.cms.remote;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 记录输入手机号数量
 */
@Slf4j
@Service
public class SubscribeLogService {
    @Autowired
    private RedisUtil redisUtil;

    public void subscribeLog(String channel,String extra) {
        String key="cms:visitLog:"+channel+":"+extra+":"+ DateUtils.formatDate();
        //30天有效期
        Long dailyExpire =30*24*3600L;
        if(redisUtil.hasKey(key)){
            redisUtil.incr(key,1);
        }else{
            redisUtil.set(key,1,dailyExpire);
        }
    }
}
