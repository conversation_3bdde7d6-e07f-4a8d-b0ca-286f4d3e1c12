package com.eleven.cms.remote;

import com.eleven.cms.config.*;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.JiangXiVrbtBusinessServiceImpl;
import com.eleven.cms.shanghaimobile.properties.ShanghaiMobileBusiness;
import com.eleven.cms.shanghaimobile.service.IShanghaiMobileService;
import com.eleven.cms.shanghaimobile.util.ShanghaiMobileConstant;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.util.BizConstant.*;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:46
 * Desc: 推送数据至开通服务器,或将订单数据放至redis队列,同步给一东刷歌
 */
@Slf4j
@Service
public class PushSubscribeService {
    @Autowired
    private BizProperties bizProperties;
    @Autowired
    private ISubscribeService subscribeService;
    //@Autowired
    //private RedisService redisService;
    //@Autowired
    //private SignatureService signatureService;
    @Autowired
    private Environment environment;
    @Autowired
    private IOrderVrbtService orderVrbtService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ThirdPartyChannelConfigProperties thirdPartyChannelConfigProperties;
    @Autowired
    LiantongVrbtService liantongVrbtService;
    @Autowired
    ISmsValidateService smsValidateService;
    @Autowired
    DianxinVrbtProperties dianxinVrbtProperties;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    IShanghaiMobileService shanghaiMobileService;
    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    JunboHunanYidongService junboHunanYidongService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;

    private static final String reportUrl = "https://crbt.cdyrjygs.com/vrbt_jiangsu";

    @Autowired
    JunBoLiuLiangBaoProperties junBoLiuLiangBaoProperties;
    @Autowired
    GuizhouYidongService guizhouYidongService;
    @Autowired
    JiangxiYidongService jiangxiYidongService;
    @Autowired
    JiangxiYidongProperties jiangxiYidongProperties;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;

    public static final String REDIS_KEY_ORDER_PUSH_QUEUE = "baidu_member_order_queue";

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);


    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().readTimeout(20L, TimeUnit.SECONDS).build();
        //if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
        //    this.client = this.client.newBuilder().proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 10086))).build();
        //}
        //this.client = OkHttpClientUtils.getNewInstance().newBuilder().addNetworkInterceptor(new CurlInterceptor(
        //        message -> System.out.println(message))).build();
        this.mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }

    /**
     *  推送号码至即时开通服务器(方法会判断是否有包月,有包月的就不推送,有包月的需要订购视频彩铃的就直接订购)
     *    流程:   1.先看是否有包月(并根据包月和有无视频彩铃功能确定渠道号),有包月就改状态并订彩铃
     *           2.判断当天是否已推送开通,此时给开通链接带上渠道号,已推送过的不再推送
     *           3.在收到开通服务器开通结果后,如果开通成功的,根据渠道号去开默认彩铃,并将这个时间点之后的要订彩铃的依次订购上
     *           4.在开通之后来的订彩铃会根据包月情况决定是否订购
     * @param subscribe
     * @return
     */
    @Async
    public void pushWithMonthlyExistsCheck(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        //查询包月和功能状态,确定渠道号,处理已有包月的,将状态改为已有包月,需要订购彩铃的就在服务端异步订购,不再推送开通
        VrbtCombinResult result = handleVrbtStatusAndMonthlyExists(subscribe);
        if (!result.isOK()||result.isMonth()) {
            return;
        }
        //判断当天是否已推送过
        if(isPushExistsToday(subscribe)){
            log.info("当日已推送开通过的号码[视频彩铃]=>mobile:{},不再推送开通",mobile);
            return;
        }

        //使用合并查询结果的渠道号
        String channelCode = result.getChannelCode();
        final boolean isCentrality = MiguApiService.isCentralityChannel(subscribe.getChannel());
        long delaySeconds = 0L;
        //如果没有开视频彩铃功能,就在此处开通
//        if(!result.isFun()){
//            delaySeconds = 30L;
//            final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
//            final RemoteResult vrbtOpenResult = miguApiService.vrbtOpen(mobile, channelCode);
//            //"订阅包单独开通视频彩铃功能失败=>{""ok"":false,""resCode"":""000001"",""resMsg"":""成功受理""}"
//            //针对异步省份返回码000001指定60秒延迟开包月
//            if("000001".equals(vrbtOpenResult.getResCode())){
//                delaySeconds = 60L;
//            }else {
//                //如果开通视频彩铃功能失败,就保存结果,不再推送开通订阅包
//                if(!vrbtOpenResult.isOK()){
//                    Subscribe upd = new Subscribe();
//                    upd.setId(subscribe.getId());
//                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
//                    String openFailResult = "单独开通视频彩铃功能失败=>";
//                    try {
//                        final String json = mapper.writerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).writeValueAsString(vrbtOpenResult);
//                        openFailResult = openFailResult + json;
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                    upd.setResult(openFailResult);
//                    upd.setBizTime(new Date());
//                    SpringContextUtils.getBean(ISubscribeService.class).updateSubscribeDbAndEs(upd);
//                    subscribeService.addCallbackNotifyMessage(subscribe,openFailResult);
//                    return;
//                }
//            }
            //现在剩下的就是 开放平台:视频彩铃功能开成功的,彩铃中心:视频彩铃开通成功/失败
            //现在要求将开放平台70%的订阅包切换到渠道包
//            final int channelSwtichPercent = miguApiService.getChannelSwtichPercent();
//            if(bizProperties.isChannelDingyue(channelCode) && RandomUtils.createRandom(1,100) <= channelSwtichPercent){
//                String oldChannelCode = channelCode;
//                log.info("切换开放平台的订阅包到渠道包[视频彩铃]=>mobile:{}",mobile);
//                channelCode = bizProperties.getChannelQudao(channelCode);
//                Subscribe upd = new Subscribe();
//                upd.setId(subscribe.getId());
//                upd.setChannel(channelCode);
//                upd.setServiceId(oldChannelCode);
//                upd.setRemark("订阅切换渠道");
//                SpringContextUtils.getBean(ISubscribeService.class).updateSubscribeDbAndEs(upd);
//            }
//        }

        //不再在开通页去订彩铃了,在收到开通结果再去订
        String orderPage = (isCentrality ?  bizProperties.getSubscribeVrbtCentralityOrderPage() : bizProperties.getSubscribeVrbtOrderPage())+"?mobile="+mobile+"&channelCode="+channelCode;
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", "698039035100000014"); //因为订购页取结果的方式是一样的,所以在开通那边可以视作同一业务
        //如果是订阅包,就延迟30秒开通(等待视频彩铃功能异步开通成功)
        dataNode.put("delay", delaySeconds);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[视频彩铃]=>raw: {}",raw);
        push(subscribe, raw);
    }

    /**
     *  推送联通视频彩铃号码至即时开通服务器
     * @param subscribe
     * @return
     */
    public Result<?> pushLtVrbt(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        //if(isPushExistsToday(subscribe)){
        //    log.info("当日已推送开通过的号码[振铃]=>mobile:{},不再推送开通",mobile);
        //    return;
        //}
        //使用合并查询结果的渠道号
        final String serviceId = StringUtils.repeat(MobileRegionResult.ISP_LIANTONG,18);
        String orderPage = bizProperties.getSubscribeLiantongVrbtOrderPage()+"?orderId="+subscribe.getIspOrderNo();
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", serviceId);
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[联通视频彩铃]=>raw: {}",raw);

        return push(subscribe, raw);
    }

    /**
     *  推送电信视频彩铃号码至即时开通服务器
     * @param subscribe
     * @return
     */
    public Result<?> pushDxVrbt(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        //if(isPushExistsToday(subscribe)){
        //    log.info("当日已推送开通过的号码[振铃]=>mobile:{},不再推送开通",mobile);
        //    return;
        //}
        //使用合并查询结果的渠道号
        String company = subscribe.getServiceId();
        String serviceId = dianxinVrbtProperties.getDianxinVrbtConfig(company).getPushServiceId();

        // https://m.imusic.cn/openauth/confirm?order_no=0fca98e654284b3ea8a1a0f44c6814e4
        String orderPage = bizProperties.getSubscribeDianxinVrbtOrderPage()+"?order_no="+subscribe.getIspOrderNo();
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", StringUtils.isNotBlank(serviceId) ? serviceId : "444444444444444444");
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[电信视频彩铃]=>raw: {}",raw);

        return push(subscribe, raw);
    }


    /**
     *  推送电信视频彩铃号码至即时开通服务器
     * @param subscribe
     * @return
     */
    public Result<?> pushHuanqiuwangDxVrbt(Subscribe subscribe,String orderPage){
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        //if(isPushExistsToday(subscribe)){
        //    log.info("当日已推送开通过的号码[振铃]=>mobile:{},不再推送开通",mobile);
        //    return;
        //}
        //使用合并查询结果的渠道号
        final String serviceId = "400000000000000002";
        // https://m.imusic.cn/openauth/confirm?order_no=0fca98e654284b3ea8a1a0f44c6814e4
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", serviceId);
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[电信视频彩铃]=>raw: {}",raw);

        return push(subscribe, raw);
    }


    /**
     *  推送电信视频彩铃号码至即时开通服务器
     * @param subscribe
     * @return
     */
    public Result<?> pushDxVrbtOfficial(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        //if(isPushExistsToday(subscribe)){
        //    log.info("当日已推送开通过的号码[振铃]=>mobile:{},不再推送开通",mobile);
        //    return;
        //}
        //使用合并查询结果的渠道号
        final String serviceId = StringUtils.repeat("5",18);
        // https://m.imusic.cn/openauth/confirm?order_no=0fca98e654284b3ea8a1a0f44c6814e4
        //String orderPage = "https://m.imusic.cn/h5v/vrbtjx/?cc=7370";
        //String orderPage = "https://m.imusic.cn/h5v/vrbtjx/?cc=7370&subId=8815&proId=spclxkb";
//        String orderPage = "https://m.imusic.cn/h5v/vrbtjx/?cc=7530&subId=8815&proId=spclxxc";
        String orderPage = "https://m.imusic.cn/h5v/vrbtjx/?cc=7120&subId=9017&proId=gmwspcl";
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", serviceId);
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[电信官方视频彩铃]=>raw: {}",raw);

        return push(subscribe, raw);
    }

    /**
     *  推送号码至即时开通服务器(白金会员)
     * @param subscribe
     * @return
     */
    @Async
    public Result<?> pushBjhy(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        if(isPushExistsToday(subscribe)){
            log.info("当日已推送开通过的号码[振铃]=>mobile:{},不再推送开通",mobile);
            return Result.ok();
        }
        //使用合并查询结果的渠道号
        final String channelCode = subscribe.getChannel();
        final String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
        //https://crbt.cdyrjygs.com/bjhy_open/#/?mobile=13438828200&channelCode=00210PP
        String orderPage = bizProperties.getSubscribeBjhyOrderPage()+"?mobile="+mobile+"&channelCode="+channelCode;
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", serviceId);
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[白金会员]=>raw: {}",raw);
        return push(subscribe, raw);
    }

    /**
     *  推送号码至即时开通服务器(渠道专属包月)
     * @param subscribe
     * @return
     */
    public Result<?> pushCpmb(Subscribe subscribe) {
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        //if(isPushExistsToday(subscribe)){
        //    log.info("当日已推送开通过的号码[渠道专属包月]=>mobile:{},不再推送开通",mobile);
        //    return Result.ok();
        //}
        //使用合并查询结果的渠道号
        final String channelCode = subscribe.getChannel();
        final String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
        //https://crbt.cdyrjygs.com/cpmb_open/#/?mobile=13438828200&channelCode=002103L&serviceId=698039020050006172
        String orderPage = bizProperties.getSubscribeCpmbOrderPage()+"?mobile="+mobile+"&channelCode="+channelCode+"&serviceId="+serviceId;
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", serviceId);
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[渠道专属包月]=>raw: {}",raw);
        return push(subscribe, raw);
    }

    /**
     *  推送号码至即时开通服务器(方法会判断是否有包月,有包月的就不推送,有包月的需要订购视频彩铃的就直接订购)
     *    流程:   1.先看是否有包月(并根据包月和有无视频彩铃功能确定渠道号),有包月就改状态并订彩铃
     *           2.判断当天是否已推送开通,此时给开通链接带上渠道号,已推送过的不再推送
     *           3.在收到开通服务器开通结果后,如果开通成功的,根据渠道号去开默认彩铃,并将这个时间点之后的要订彩铃的依次订购上
     *           4.在开通之后来的订彩铃会根据包月情况决定是否订购
     * @param subscribe
     * @return
     */
    @Async
    public Result<?> pushRt(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        //if(isPushExistsToday(subscribe)){
        //    log.info("当日已推送开通过的号码[振铃]=>mobile:{},不再推送开通",mobile);
        //    return;
        //}
        //使用合并查询结果的渠道号
        final String channelCode = subscribe.getChannel();
        final String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
        String orderPage = bizProperties.getSubscribeRtOrderPage()+"?mobile="+mobile+"&channelCode="+channelCode+"&serviceId="+serviceId;
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", orderPage);
        dataNode.put("serviceId", serviceId);
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[振铃]=>raw: {}",raw);
        return push(subscribe, raw);
    }

    public Result<?> push(Subscribe subscribe, String raw) {

        String mobile = subscribe.getMobile();
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(bizProperties.getPushSubscibeServerUrl())
                                               .post(body)
                                               .build();
        try (Response response = client.newCall(request)
                                       .execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String jsonResp = response.body().string();
            log.info("即时开通服务器响应=>mobile:{},响应:{}", mobile, StringUtils.normalizeSpace(jsonResp));
            //JsonNode tree = mapper.readTree(jsonResp);
            //int code = tree.at("/code").asInt();
            //String message = tree.at("/message").asText();

            Result<?> result = mapper.readValue(jsonResp,Result.class);
//            int code = result.getCode();
//            String message = result.getMessage();
//
//            Subscribe upd = new Subscribe();
//            upd.setId(subscribe.getId());
//            upd.setStatus(/*isMonthly ? SUBSCRIBE_STATUS_MONTHLY_EXISTS :*/
//                    SUBSCRIBE_STATUS_PUSHED);//状态2表示已推送开通(已有包月此处不改状态,只发过去开彩铃)
//            upd.setPushRespCode(code);
//            upd.setPushRespMessage(message);
//            if (code == HttpStatus.NOT_ACCEPTABLE.value()) {
//                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
//                upd.setOpenTime(new Date());
//                upd.setResult(message);
//            }
//            final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
////            subscribeService.updateById(upd);
//            subscribeService.updateSubscribeDbAndEs(upd);

             return result;

        } catch (IOException e) {
            //e.printStackTrace();
            log.info("推送号码至即时开通服务器异常", e);
            return Result.error("系统繁忙,请稍后再试!");
        }
    }

    /**
     * 判断当天是否已推送过
     * @param subscribe
     * @return
     */
    private boolean isPushExistsToday(Subscribe subscribe) {
        String existsKey = "subscribe::todayPushed:"+subscribe.getMobile();
        final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
        boolean isPushExistsToday = !redisUtil.setIfAbsent(existsKey, existsKey, expire);
        if(isPushExistsToday){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_DUPLICATE_TODAY);
            upd.setOpenTime(new Date());
            upd.setResult("当日重复");
            final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        return isPushExistsToday;
    }

    /**
     * 根据有无包月有无铃音编码决定是否订购(或延迟订购)视频彩铃
     * @param subscribe
     * @return
     */
    public Result<Object> dianxinHandleVrbtPackageExists(Subscribe subscribe) {
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("订阅成功");

        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        String mobile = subscribe.getMobile();
        final String dxToneCode = subscribe.getDxToneCode();
        //查询是否已开通了包月
        final DianxinVrbtService dianxinVrbtService = SpringContextUtils.getBean(DianxinVrbtService.class);
        final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile,subscribe.getVrbtDxChannel());
        //如果已有包月
        if(packageExist){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            //免费订购视频彩铃
            //if(StringUtils.isNotEmpty(dxToneCode)){
            //    final DianxinResp toneOrderResp = dianxinVrbtService.addToneFreeOnProduct(mobile, dxToneCode);
            //    subscribeService.dianxinUpdateToneOrderExtra(mobile,dxToneCode,toneOrderResp);
            //}
            return bizExistsResult;
        }
        return okResult;
    }


    /**
     * 最新版本 根据有无包月有无铃音编码决定是否订购(或延迟订购)视频彩铃
     * @param subscribe
     * @return
     */
    public Result<?> dianxinHandleVrbtPackageExistsV1(Subscribe subscribe) {

        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);


        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        String mobile = subscribe.getMobile();
        final String dxToneCode = subscribe.getDxToneCode();
        //查询是否已开通了包月
        final DianxinVrbtService dianxinVrbtService = SpringContextUtils.getBean(DianxinVrbtService.class);

        final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile,subscribe.getVrbtDxChannel());
        //如果已有包月
        if(packageExist){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            //免费订购视频彩铃
            //if(StringUtils.isNotEmpty(dxToneCode)){
            //    final DianxinResp toneOrderResp = dianxinVrbtService.addToneFreeOnProduct(mobile, dxToneCode);
            //    subscribeService.dianxinUpdateToneOrderExtra(mobile,dxToneCode,toneOrderResp);
            //}
            return bizExistsResult;
        }
        DianxinResp dianxinResp = dianxinVrbtService.confirmOpenOrderLaunchedEx(mobile, "0", null, subscribe.getVrbtDxChannel());
        if (!dianxinResp.isOK()) {
            log.warn("电信视频彩铃包月业务订购失败,渠道:{},手机号:{},resCode:{},resMessage:{}", subscribe.getVrbtDxChannel(), mobile, dianxinResp.getResCode(), dianxinResp.getResMessage());
            if (BIZ_DIANXIN_CHANNEL_GUANGMINGWANG.equals(subscribe.getVrbtDxChannel()) && StringUtils.contains(dianxinResp.getResMessage(), "超出")) {
                //如果为光明网，就切换渠道
                Optional<String> optional = DIANXIN_CHANNEL_LIST.stream().filter(dianxinChannel -> !subscribe.getVrbtDxChannel().equals(dianxinChannel) && dianxinVrbtProperties.getDianxinVrbtConfig(dianxinChannel).getProvinceList().contains(subscribe.getProvince())).findFirst();
                if (optional.isPresent()) {
                    subscribe.setVrbtDxChannel(optional.get());
                } else {
                    return Result.error("暂未开放,敬请期待!");
                }
                dianxinResp = dianxinVrbtService.confirmOpenOrderLaunchedEx(mobile, "0", null, subscribe.getVrbtDxChannel());
                if (!dianxinResp.isOK()) {
                    log.warn("电信视频彩铃包月业务二次订购失败,渠道:{},手机号:{},resCode:{},resMessage:{}", subscribe.getVrbtDxChannel(), mobile, dianxinResp.getResCode(), dianxinResp.getResMessage());
                    Subscribe upd = new Subscribe();
                    upd.setId(id);
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                    upd.setResult(dianxinResp.getResCode() + "@" + dianxinResp.getResMessage());
                    upd.setServiceId(subscribe.getVrbtDxChannel());
                    subscribeService.updateSubscribeDbAndEs(upd);
                    return errorResult;
                }
            } else {
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(dianxinResp.getResCode() + "@" + dianxinResp.getResMessage());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
        //电信视频彩铃包月业务订购失败,手机号:18099514500,resCode:91,resMessage:非常抱歉，本号码暂不支持开通本业务，建议用其他手机号码重新尝试。
        //电信视频彩铃包月业务订购失败,手机号:13309102583,resCode:92,resMessage:该接口当日超出调用上限
        //电信视频彩铃包月业务订购失败,手机号:13309102583,res_message:该接口当日超出调用上限,res_code:201
        //201@该接口当日调用已超出上限
//        if(!dianxinResp.isOK()){
//            log.warn("电信视频彩铃包月业务订购失败,手机号:{},resCode:{},resMessage:{}",mobile,dianxinResp.getResCode(),dianxinResp.getResMessage());
//            Subscribe upd = new Subscribe();
//            upd.setId(id);
//            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
//            upd.setResult(dianxinResp.getResCode()+"@"+dianxinResp.getResMessage());
//            subscribeService.updateSubscribeDbAndEs(upd);
//            if (StringUtils.contains(dianxinResp.getResMessage(), "超出") || StringUtils.equals(dianxinResp.getResCode(), "201")) {
//                log.warn("电信视频彩铃包月业务下单接口已达上限,跳转到电信官方试营销页面,手机号:{}", mobile);
////                String dianxinSmsKey = CacheConstant.SMS_DIANXIN_CHANNEL_KEY + ":" + subscribe.getVrbtDxChannel();
////                if (redisUtil.get(dianxinSmsKey) == null) {
////                    LocalDateTime now = LocalDateTime.now();
////                    LocalDateTime oneClock = now.with(LocalTime.of(1, 0));
////                    //当天超过1点再发送短信
////                    if (now.isAfter(oneClock)) {
////                        dianxinVrbtProperties.getMobiles().forEach(phone -> {
////                            smsValidateService.createSichuanMobile(phone, "电信渠道号：" + subscribe.getVrbtDxChannel() + "->" + dianxinResp.getResMessage());
////                        });
////                        redisUtil.set(dianxinSmsKey, subscribe.getVrbtDxChannel(), DateUtil.getSecondByNowDiffEndOfDay());
////                    }
////                }
////                //中午12点后切换为hstj
////                if (!DateUtil.isAm()) {
////                    String currentChannelCode = (String) redisUtil.get(CacheConstant.DIANXIN_CURRENT_CHANNEL_KEY);
////                    if(!BIZ_DIANXIN_CHANNEL_HSTJ.equals(currentChannelCode)) {
////                        redisUtil.set(CacheConstant.DIANXIN_CURRENT_CHANNEL_KEY, BIZ_DIANXIN_CHANNEL_HSTJ, DateUtil.getSecondByNowDiffEndOfDay());
////                    }
////                }
////                return subscribeService.dianxinRedirectOfficial(subscribe);
//            }
////            if (StringUtils.equals(dianxinResp.getResCode(), "92")) {
////                log.warn("电信视频彩铃包月业务下单接口已达上限,跳转到电信官方试营销页面,手机号:{}", mobile);
////                String dianxinSmsKey = CacheConstant.SMS_DIANXIN_CHANNEL_KEY + ":" + subscribe.getVrbtDxChannel();
////                if (redisUtil.get(dianxinSmsKey) == null) {
////                    LocalDateTime now = LocalDateTime.now();
////                    LocalDateTime oneClock = now.with(LocalTime.of(1, 0));
////                    //当天超过1点再发送短信
////                    if (now.isAfter(oneClock)) {
////                        dianxinVrbtProperties.getMobiles().forEach(phone -> {
////                            smsValidateService.createSichuanMobile(phone, "电信渠道号：" + subscribe.getVrbtDxChannel() + "->" + dianxinResp.getResMessage());
////                        });
////                        redisUtil.set(dianxinSmsKey, subscribe.getVrbtDxChannel(), DateUtil.getSecondByNowDiffEndOfDay());
////                    }
////                }
////                return subscribeService.dianxinRedirectOfficial(subscribe);
////            }
//            //省份限制
//            if (StringUtils.equals(dianxinResp.getResCode(), "201")) {
//
//
////                log.warn("电信视频彩铃接口调用省份限制,渠道:{},省份:{}", subscribe.getServiceId(), subscribe.getProvince());
////                String dianxinProvinceKey = CacheConstant.PROVINCE_DIANXIN_CHANNEL_KEY + ":" + subscribe.getProvince() + ":" + subscribe.getVrbtDxChannel();
////                if (redisUtil.get(dianxinProvinceKey) == null) {
////                    dianxinVrbtProperties.getMobiles().forEach(phone -> {
////                        smsValidateService.createSichuanMobile(phone, "电信省份：" + subscribe.getProvince() + "渠道号：" + subscribe.getVrbtDxChannel() + "->" + dianxinResp.getResMessage());
////                        redisUtil.set(dianxinProvinceKey, subscribe.getProvince(), DateUtil.getSecondByNowDiffEndOfDay());
////                    });
////                }
//            }
//            return  errorResult;
//        }
        //更新运营商生成的订单号,后续接收运营商回调通知来更新钉够结果需要这个订单号
        subscribe.setIspOrderNo(dianxinResp.getOrderNo());
        Subscribe upd = new Subscribe();
        upd.setId(id);
        upd.setIspOrderNo(dianxinResp.getOrderNo());
        upd.setServiceId(subscribe.getVrbtDxChannel());
        subscribeService.updateSubscribeDbAndEs(upd);
        //return Result.bizConfirm(liantongResp.getUrl());
        Result<?> result = pushDxVrbt(subscribe);
        return CommonConstant.SC_OK_200.equals(result.getCode()) ? noauthResult : errorResult;
    }

    /**
     * 下发开通短信
     * @param subscribe
     * @return
     */
    public Result<Object> dianxinHandleVrbtOfficial(Subscribe subscribe) {

        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);

        //return Result.bizConfirm(liantongResp.getUrl());
        Result<?> result = pushDxVrbtOfficial(subscribe);
        return CommonConstant.SC_OK_200.equals(result.getCode()) ? noauthResult : errorResult;
    }


    public Result<?> liantongHandleVrbtPackageExists(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);

        final String mobile = subscribe.getMobile();
        final String ltRingId = subscribe.getLtRingId();

//        if(BizConstant.BIZ_LT_SERVICE_ID.equals(subscribe.getServiceId())){
//            liantongVrbtService = SpringContextUtils.getBean(LiantongVrbtService.class);
//        }else{
//            liantongVrbtService = SpringContextUtils.getBean(LiantongRuijinVrbtService.class);
//        }
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final boolean isSubedMon = liantongVrbtService.isSubedMon(mobile,subscribe.getVrbtLtChannel());
        //如果有包月
        if(isSubedMon){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
            upd.setResult("已有包月");
            //如果还需要订购铃音,就单独单独订购铃音
            if(StringUtils.isNotEmpty(ltRingId)){
                LiantongResp liantongResp = liantongVrbtService.settingRingOnePointMon(mobile, ltRingId,subscribe.getVrbtLtChannel());
                upd.setExtra(liantongResp.getDescription());
            }
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }
        //final LiantongResp liantongResp = liantongVrbtService.onePointProductMon(mobile,ltRingId);
        //未包月,需要生成订单,并传入铃音id(因为开包月同时订已存在的铃音不会包月成功,收到的回调也无法判定包月结果,所以这里单开包月)
        final LiantongResp liantongResp = liantongVrbtService.onePointProductMon(mobile,null,null,subscribe.getVrbtLtChannel());
        // 联通视频彩铃包月业务订购失败,手机号:13187174721,resCode:1033,resMessage:预校验未通过,规则组:全局规则,规则集:全局(基本规则),规则:号码状态(停机)
        if(!liantongResp.isOK()){
            log.warn("联通视频彩铃包月业务订购失败,手机号:{},resCode:{},resMessage:{}",mobile,liantongResp.getReturnCode(),liantongResp.getDescription());
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(liantongResp.getReturnCode()+"@"+liantongResp.getDescription());
            subscribeService.updateSubscribeDbAndEs(upd);
            return  errorResult;
        }

        //更新运营商生成的订单号,后续接收运营商回调通知来更新钉够结果需要这个订单号
        subscribe.setIspOrderNo(liantongResp.getOrderId());
        Subscribe upd = new Subscribe();
        upd.setId(id);
        upd.setIspOrderNo(liantongResp.getOrderId());
        subscribeService.updateSubscribeDbAndEs(upd);

        //return Result.bizConfirm(liantongResp.getUrl());
        Result<?> result = pushLtVrbt(subscribe);

        return CommonConstant.SC_OK_200.equals(result.getCode()) ? noauthResult : errorResult;
    }

    public Result<?> handleCpmbExists(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.cpmbQuery(mobile, subscribe.getChannel());

        //返回码为000000表示已订购
        if(remoteResult.isCpmbMember()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        Result<?> result = pushCpmb(subscribe);

        return CommonConstant.SC_OK_200.equals(result.getCode()) ? noauthResult : errorResult;
    }

    public Result<?> handleCpmbExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.cpmbQuery(mobile, subscribe.getChannel());

        //返回码为000000表示已订购
        if(remoteResult.isCpmbMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                            .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);

            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
           if("402".equals(billingResult.getCode())){
               subscribeService.removeSubscribeDbAndEs(id);
           }
           return errorResult;
        }
    }

    public Result<?> handleCpmbBizFuseExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.cpmbQuery(mobile, subscribe.getChannel());

        //返回码为000000表示已订购
        if(remoteResult.isCpmbMember()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);

            return okResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }


    public Result<?> handleCpmbBizFuseExistsNewBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //调用音乐包复合查询接口
        final CpmbCombinResult cpmbCombinResult = miguApiService.cpmbCombinQuery(mobile, subscribe.getChannel());

        //返回码为000000表示已订购
        if(cpmbCombinResult.isMonth()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }
        //判定购物车第二个serviceId
        //获取验证码接口新增一个serviceId字段
        //1、若不传则跟现在一样，单独订购权益包包月
        //2、若传了serviceId则会通过购物车打包订购  权益包包月+传入的serviceId
        //这样的话就会更灵活，你那根据业务需求控制是否单个或者多个id
        //举例，华岸权益包：
        //四川用户就这样传http://open.hxypw.cn/crack/getsms?phone=13608182222&paycode=HYM00211DI&serviceId=698039020100000134
        //全国（非四川广东浙江重庆）就这样传
        //http://open.hxypw.cn/crack/getsms?phone=13608182222&paycode=HYM00211DI&serviceId=600923018000000007
        //1.主叫只能购物车（购物车就你传主叫的serviceId我们这打包开通)，
        //2.四川因为要购物车打包开通两元特惠包，只有你那调用现有开通接口开被叫
        //3.广东浙江重庆，你传serviceId或者直接开通接口开，都可以
        String serviceId = null;
        if(cpmbCombinResult.isNeedCrbtTehuiPack()){
            serviceId = cpmbCombinResult.isCrbtTehuiPack() ? null : MiguApiService.CPMB_CRBT_MONTH_SERVICE_ID;
        }else {
            serviceId = cpmbCombinResult.isVrbtFun() ? null : MiguApiService.CPMB_VRBT_FUN_ACTIVE_SERVICE_ID;
        }
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtNewCrackService.class).getSmsCpmb(mobile,subscribe.getChannel(), serviceId);
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return okResult;
        } else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if ("402".equals(billingResult.getCode())) {
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }

    public Result<?> handleCpmbXiaogaoExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.cpmbQuery(mobile, subscribe.getChannel());

        //返回码为000000表示已订购
        if (remoteResult.isCpmbMember()) {
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                            .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(XiaogaoCrackService.class).getSms(mobile, subscribe.getChannel());
        if (billingResult.isOK()) {
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);

            return okResult;
        } else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if ("402".equals(billingResult.getCode())) {
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }


    public Result<?> handleVrbtExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        VrbtCombinResult result = handleVrbtStatusAndMonthlyExists(subscribe);
        if (!result.isOK() || result.isMonth()) {
            //3表示已有包月
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        //关闭彩铃中心单独开视频彩铃功能,走二合一购物车接口
        //if (MiguApiService.isCentralityChannel(subscribe.getChannel()) && !result.isFun()) {
        //    //开通视频彩铃功能
        //    final RemoteResult remoteResult = miguApiService.vrbtOpen(subscribe.getMobile(), subscribe.getChannel());
        //    if (!remoteResult.isOK()) {
        //        Subscribe upd = new Subscribe();
        //        upd.setId(subscribe.getId());
        //        upd.setStatus(SUBSCRIBE_STATUS_FAIL);
        //        String openFailResult = "单独开通视频彩铃功能失败=>";
        //        try {
        //            final String json = mapper.writerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).writeValueAsString(remoteResult);
        //            openFailResult = openFailResult + json;
        //        } catch (Exception e) {
        //            e.printStackTrace();
        //        }
        //        upd.setResult(openFailResult);
        //        upd.setBizTime(new Date());
        //        SpringContextUtils.getBean(ISubscribeService.class).updateSubscribeDbAndEs(upd);
        //        return Result.error("获取验证码失败");
        //    }
        //}

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile, subscribe.getChannel());
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }


    public Result<?> handleVrbtBizFuseExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
//        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        VrbtCombinResult result = handleVrbtStatusAndMonthlyExists(subscribe);
        if (!result.isOK() || result.isMonth()) {
            //3表示已有包月
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        //关闭彩铃中心单独开视频彩铃功能,走二合一购物车接口
        //if (MiguApiService.isCentralityChannel(subscribe.getChannel()) && !result.isFun()) {
        //    //开通视频彩铃功能
        //    final RemoteResult remoteResult = miguApiService.vrbtOpen(subscribe.getMobile(), subscribe.getChannel());
        //    if (!remoteResult.isOK()) {
        //        Subscribe upd = new Subscribe();
        //        upd.setId(subscribe.getId());
        //        upd.setStatus(SUBSCRIBE_STATUS_FAIL);
        //        String openFailResult = "单独开通视频彩铃功能失败=>";
        //        try {
        //            final String json = mapper.writerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).writeValueAsString(remoteResult);
        //            openFailResult = openFailResult + json;
        //        } catch (Exception e) {
        //            e.printStackTrace();
        //        }
        //        upd.setResult(openFailResult);
        //        upd.setBizTime(new Date());
        //        SpringContextUtils.getBean(ISubscribeService.class).updateSubscribeDbAndEs(upd);
        //        return Result.error("获取验证码失败");
        //    }
        //}

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile, subscribe.getChannel());
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return okResult;
        }else {
//            //{"code":"402","message":"请求验证码过快!"}
//            //验证码请求失败,丢弃此条记录
//            if("402".equals(billingResult.getCode())){
//                subscribeService.removeSubscribeDbAndEs(id);
//            }
            return Result.errorSystemMsg(billingResult.getMessage());
        }
    }


    public Result<?> handleVrbtBizFuseExistsNewBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        VrbtCombinResult result = handleVrbtStatusAndMonthlyExists(subscribe);
        if (!result.isOK() || result.isMonth()) {
            //3表示已有包月
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtNewCrackService.class).getSms(mobile, subscribe.getChannel());
        if (billingResult.isOK()) {
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);

            return okResult;
        } else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if ("402".equals(billingResult.getCode())) {
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }



    public Result<?> handleOutsideVrbtExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.error("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        VrbtCombinResult result = handleVrbtStatusAndMonthlyExists(subscribe);
        if (!result.isOK() || result.isMonth()) {
            //3表示已有包月
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }
//        if (!result.isFun()) {
//            //开通视频彩铃功能
//            final RemoteResult remoteResult = miguApiService.vrbtOpen(subscribe.getMobile(), subscribe.getChannel());
//            if (!remoteResult.isOK()) {
//                Subscribe upd = new Subscribe();
//                upd.setId(subscribe.getId());
//                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
//                String openFailResult = "单独开通视频彩铃功能失败=>";
//                try {
//                    final String json = mapper.writerFor(RemoteResult.class).withView(RemoteResult.BasicView.class).writeValueAsString(remoteResult);
//                    openFailResult = openFailResult + json;
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                upd.setResult(openFailResult);
//                upd.setBizTime(new Date());
//                SpringContextUtils.getBean(ISubscribeService.class).updateSubscribeDbAndEs(upd);
//                return Result.error("获取验证码失败");
//            }
//        }
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile, subscribe.getChannel());
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;

        }
    }


    /**
     * 外部渠道获取上海移动炫视视频彩铃验证码
     *
     * @param subscribe
     * @return
     */
    public Result<?> getSmsCodeByOutsideShydXssp(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        Result<?> result = shanghaiMobileService.shangHaiMobileBusinessOrder(mobile, subscribe.getIp(), ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS, "", "");
        Subscribe upd = new Subscribe();
        upd.setId(id);
        if (result.isOK()) {
            upd.setIspOrderNo((String) result.getResult());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }

    /**
     * 外部渠道提交上海移动炫视视频彩铃验证码
     *
     * @param subscribe
     * @return
     */
    public Result<?> smsCodeByOutsideShydXssp(Subscribe subscribe) {
        String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        return shanghaiMobileService.shangHaiMobileBusinessOrder(mobile, subscribe.getIp(), ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS, smsCode, subscribe.getIspOrderNo());
    }

    /**
     * 外部渠道获取江苏移动视频彩铃验证码
     *
     * @param subscribe
     * @return
     */
    public Result<?> getSmsCodeByOutsideJsyd(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        JiangsuResponseCreateOrder jiangsuResponseCreateOrder = jiangsuYidongService.createOrder(mobile, subscribe.getReferer(), "", reportUrl, jiangsuYidongService.getToken());
        Subscribe upd = new Subscribe();
        upd.setId(id);
        if (jiangsuResponseCreateOrder.isOk()) {
            String orderId = jiangsuResponseCreateOrder.getContent().getOrderId();
            upd.setIspOrderNo(orderId);
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }

    /**
     * 外部渠道获取四川移动炫视视频彩铃验证码
     * @param subscribe
     * @return
     */
    public Result<?> getSmsCodeByOutsideScyd(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        subscribe.setServiceId(BIZ_SCMCC_CHANNEL_YRJY);
        subscribe.setSource("https://crbt.cdyrjygs.com/new_sc_mobile/991");
        Result<?> result = pushScyd(subscribe);
        Subscribe upd = new Subscribe();
        upd.setId(id);
        upd.setServiceId(BIZ_SCMCC_CHANNEL_YRJY);
        if(CommonConstant.SC_OK_200.equals(result.getCode())){
            upd.setIspOrderNo((String) result.getResult());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else{
            return errorResult;
        }
    }

    /**
     * 外部渠道获取湖南移动视频彩铃验证码
     *
     * @param subscribe
     * @return
     */
    public Result<?> getSmsCodeByOutsideHnyd(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        String sysOrderId = JunboHunanYidongService.getSysOrderId();
        JunboLlbResult junboLlbResult = junboHunanYidongService.getSms(mobile, sysOrderId, subscribe.getChannel());
        Subscribe upd = new Subscribe();
        upd.setId(id);
        if (junboLlbResult.isOperationOk()) {
            upd.setIspOrderNo(sysOrderId);
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }

    public Result getSmsCodeByOutsideHetu(Subscribe subscribe){
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final WujiongCrackResult wujiongCrackResult = wujiongCrackService.getSms(mobile,subscribe.getChannel());
        Subscribe upd = new Subscribe();
        upd.setId(id);
        if(wujiongCrackResult.isHetuGetOk()) {
            upd.setResult(wujiongCrackResult.getMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(wujiongCrackResult.getMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }

    /**
     * 外部渠道获取贵州移动视频彩铃验证码
     *
     * @param subscribe
     * @return
     */
    public Result getSmsCodeByOutsideGzyd(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        GuizhouMobileCodeResult guizhouMobileCodeResult = guizhouYidongService.getSms(mobile, UUID.randomUUID().toString().replace("-", ""), subscribe.getChannel(), "https://crbt.cdyrjygs.com", id);
        Subscribe upd = new Subscribe();
        upd.setId(id);
        if (guizhouMobileCodeResult != null) {
            final String serialNumber = guizhouMobileCodeResult.getSerialNumber();
            upd.setId(subscribe.getId());
            upd.setIspOrderNo(serialNumber);
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        } else {
            return errorResult;
        }
    }

    /**
     * 外部渠道获取江西移动视频彩铃验证码
     *
     * @param subscribe
     * @return
     */
    public Result getSmsCodeByOutsideJxyd(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        if (StringUtils.isEmpty(subscribe.getJcmccChannel())) {
            subscribe.setJcmccChannel(BIZ_DIANXIN_CHANNEL_HSTJ);
        }
        String orderId = RandomStringUtils.randomNumeric(16);
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(subscribe.getJcmccChannel());
        String busiSerialNumber = jiangxiCompanyConfig.getUserId() + DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(4);
        JiangxiYidongResult jiangxiYidongResult = jiangxiYidongService.getSms(mobile, subscribe.getChannel(), subscribe.getJcmccChannel(), busiSerialNumber, orderId);
        Subscribe udp = new Subscribe();
        if (jiangxiYidongResult.isOk()) {
            udp.setId(id);
            udp.setIspOrderNo(busiSerialNumber);
            udp.setPushRespMessage(orderId);
            udp.setServiceId(subscribe.getJcmccChannel());
            subscribeService.updateSubscribeDbAndEs(udp);
            //写渠道订阅日志
            return noauthResult;
        } else {
            return Result.error("获取验证码失败");
        }
    }





    public Result<?> handleOutsideBjhyExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, subscribe.getChannel());

        //返回码为000000 状态为"0"表示已有包月
        if(remoteResult.isBjhyMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);

            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
                return Result.error("请求验证码过快!");
            }else{
                return errorResult;
            }
        }
    }


    public Result<?> handleOutsideCpmbExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.cpmbQuery(mobile, subscribe.getChannel());

        //返回码为resCode 000000表示已有包月
        if(remoteResult.isCpmbMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);

            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
                return Result.error("请求验证码过快!");
            }else{
                return errorResult;
            }
        }
    }



    public Result<?> handleBjhyExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        String serviceId= "";

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel());
        if (cmsCrackConfig != null) {
            serviceId = cmsCrackConfig.getServiceId();
        }

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, subscribe.getChannel());

        //返回码为000000 状态为"0"表示已有包月
        if(remoteResult.isBjhyMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);

            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
           // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            noauthResult.setServiceId(serviceId);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }

    public Result<?> handleBjhyBizFuseExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, subscribe.getChannel());

        //返回码为000000 状态为"0"表示已有包月
        if(remoteResult.isBjhyMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);

            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return okResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }

    public Result<?> handleOutsideBjhyWujiongExistsBillingCrack(Subscribe subscribe) {
//        final String id = subscribe.getId();
//        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
//        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
//        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
//        final String mobile = subscribe.getMobile();
//        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
//        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
//
//        //查询是否已开通了包月
//        final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, subscribe.getChannel());
//
//        //返回码为000000 状态为"0"表示已有包月
//        if(remoteResult.isOK() && "0".equals(remoteResult.getStatus())){
//            //3表示已有包月
////            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
////                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();
//
//            Subscribe upd = new Subscribe();
//            upd.setId(id);
//            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
//            upd.setResult("已有包月");
//            subscribeService.updateSubscribeDbAndEs(upd);
//            return bizExistsResult;
//        }
//
//        final WujiongCrackResult wujiongCrackResult = SpringContextUtils.getBean(WujiongCrackService.class).getSms(mobile,subscribe.getChannel());
//        if(wujiongCrackResult.isGetOk()) {
//            // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
//            Subscribe upd = new Subscribe();
//            upd.setId(id);
//            upd.setIspOrderNo(wujiongCrackResult.getOrderId());
//            subscribeService.updateSubscribeDbAndEs(upd);
//            return noauthResult;
//        }else {
//            Subscribe upd = new Subscribe();
//            upd.setId(id);
//            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
//            upd.setResult(wujiongCrackResult.getResultMsg());
//            upd.setIspOrderNo(wujiongCrackResult.getOrderId());
//            subscribeService.updateSubscribeDbAndEs(upd);
//            return errorResult;
//        }
        return null;
    }

    public Result<?> handleBjhyWujiongExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, subscribe.getChannel());

        //返回码为000000 状态为"0"表示已有包月
        if(remoteResult.isBjhyMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        final WujiongCrackResult wujiongCrackResult = SpringContextUtils.getBean(WujiongCrackService.class).getSms(mobile,subscribe.getChannel());
        if(wujiongCrackResult.isGetOk()) {
            // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(wujiongCrackResult.getOrderId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(wujiongCrackResult.getResultMsg());
            upd.setIspOrderNo(wujiongCrackResult.getOrderId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }


    /**
     * 处理已有包月的,将状态改为已有包月,如果有视频彩铃功能,开放平台切换到渠道包的渠道号,需要订购彩铃的就在服务端异步订购,不再推送开通
     * @param subscribe
     * @return
     */
    private VrbtCombinResult handleVrbtStatusAndMonthlyExists(Subscribe subscribe) {
        String mobile = subscribe.getMobile();
        final String miguChannel = subscribe.getChannel();
        //查询是否已开通了包月
        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        //final boolean isCentrality = MiguApiService.isCentralityChannel(miguChannel);

        //VrbtCombinResult result = miguApiService.vrbtFunAndMonthStatusQuery(mobile, isCentrality ? miguChannel : CH_DYB_DEFAULT);
        VrbtCombinResult result = miguApiService.vrbtFunAndMonthStatusQuery(mobile, miguChannel);
        log.info("手机号功能及包月状态合并查询结果:号码[{}],结果[{}]",mobile,result);
        //失败重试一次
        if(!result.isOK()){
            //result = miguApiService.vrbtFunAndMonthStatusQuery(mobile, isCentrality ? miguChannel : CH_DYB_DEFAULT);
            result = miguApiService.vrbtFunAndMonthStatusQuery(mobile, miguChannel);
            log.info("手机号功能及包月状态合并查询结果:号码[{}],结果[{}]",mobile,result);
        }
        if(!result.isOK()){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult("查询视频彩铃功能和包月状态失败");
            final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
            subscribeService.updateSubscribeDbAndEs(upd);
            return result;
        }
        //复合查询判定应该使用的渠道号(开放平台这边有视频彩铃功能应该使用渠道包的渠道号)
        final String channelCode = result.getChannelCode();


        //如果渠道号和复合查询判定的渠道号不一致(渠道包需要更新渠道号),就更新一下
        if(!channelCode.equals(subscribe.getChannel())){
            //设置正确的渠道号以保证可以用包月的渠道号来订购视频彩铃
            subscribe.setChannel(channelCode);
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setChannel(channelCode);
            SpringContextUtils.getBean(ISubscribeService.class).updateById(upd);
        }

        //如果已有包月,就不再推送开通
        if(result.isMonth()){
            //有包月就用包月的渠道号
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setChannel(channelCode);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
            upd.setResult("已有包月,无需开通");
            final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
            subscribeService.updateSubscribeDbAndEs(upd);
            //注意,订购视频彩铃之前需要依赖上一步复合查询判定的包月渠道号
            //如果有包月,切选择了要订购的视频彩铃,就在此处订购
            //如果是彩铃中心订阅库的彩铃订购需要换成彩铃中心的版权id
            final String channelCopyrightId = SpringContextUtils.getBean(IMusicService.class).getChannelCopyrightId(subscribe.getChannel(), subscribe.getCopyrightId());
            if(!Strings.isNullOrEmpty(channelCopyrightId)){
                orderVrbtService.vrbtToneFreeOrder(subscribe,channelCopyrightId,MiguApiService.VRBT_TONE_ORDER_SETFLAG_BEIJIAO, "0");
            }
        }

        return result;
    }

    public Result<?> pushSmsCode(String transactionId, String smsCode, String mobile) {
        final ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("transactionId", transactionId);
        objectNode.put("smsCode", smsCode);
        return this.pushSmsCodeWithRaw(objectNode.toString(), mobile);
    }

    public Result<?> pushSmsCodeWithRaw(String raw, String mobile) {
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(bizProperties.getPushSmsCodeServerUrl())
                .post(body)
                .build();
        log.info("提交短信验证码至即时开通服务器-手机号:{},请求:{}", mobile, raw);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String jsonResp = response.body().string();
            log.info("提交短信验证码至即时开通服务器响应=>手机号:{},响应:{}", mobile, StringUtils.normalizeSpace(jsonResp));
            //JsonNode tree = mapper.readTree(jsonResp);
            //int code = tree.at("/code").asInt();
            //String message = tree.at("/message").asText();

            return mapper.readValue(jsonResp,Result.class);

        } catch (IOException e) {
            //e.printStackTrace();
            log.info("提交短信验证码至即时开通服务器异常-手机号:{},请求:{}", mobile, raw, e);
            return Result.error("系统繁忙,请稍后再试!");
        }
    }

    ///**
    // * 将订单数据发送至redis消息队列
    // * @param order
    // * @return
    // */
    //@Async
    //public void push2Redis(Order order) {
    //    try {
    //        final String collect = String.join(",", order.getMsisdn(), order.getChannelCode(), order.getServiceId());
    //        redisService.lSet(REDIS_KEY_ORDER_PUSH_QUEUE, collect);
    //    } catch (Exception e) {
    //        log.info("将订单数发送至redis消息队列异常",e);
    //    }
    //}
    //
    //public static void main(String[] args) throws JsonProcessingException {
    //    String orderPage = "http://bj.cdyrjygs.com/subMember/#/?serviceId=698039034107404530"+"&channelCode="+"aaa"+"&mobile="+"bbb";
    //    System.out.println(orderPage);
    //}


    @Async
    public void pushThirdParty(Subscribe subscribe){


        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
        if(isPushExistsToday(subscribe)){
            log.info("当日已推送开通过的号码[视频彩铃]=>mobile:{},不再推送开通",mobile);
            return;
        }
        final String channelCode = subscribe.getChannel();
        try {
            ThirdPartyChannelConfig thirdPartyChannelConfig = thirdPartyChannelConfigProperties.getThirdPartyChannelConfig(channelCode);
            String serviceId = thirdPartyChannelConfig.getServiceId();
            String orderPage = thirdPartyChannelConfig.getOpenUrl() + mobile;
            ObjectNode dataNode = mapper.createObjectNode();
            dataNode.put("id", subscribe.getId());
            dataNode.put("mobile", mobile);
            dataNode.put("orderPage", orderPage);
            dataNode.put("serviceId", serviceId);
            dataNode.put("delay", 0L);
            String raw = dataNode.toString();
            log.info("推送号码至即时开通服务器[第三方]=>raw: {}", raw);
            push(subscribe, raw);
        }catch (Exception e){
            log.error("系统繁忙,请稍后再试!");
        }
    }


    /**
     *  推送号码至即时开通服务器(四川移动业务)
     * @param subscribe
     * @return
     */
    public Result<?> pushScyd(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        //判断当天是否已推送过
//        if(isPushExistsToday(subscribe)){
//            log.info("当日已推送开通过的号码[振铃]=>mobile:{},不再推送开通",mobile);
//            return Result.ok();
//        }
        //使用合并查询结果的渠道号
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(subscribe.getChannel());
        if (portCrackConfig == null) {
            log.info("{}-预下单接口-手机号:{},渠道号:{}", "四川移动业务-PortCrackConfig-配置错误", subscribe.getMobile(), subscribe.getChannel());
            return Result.error("四川移动业务-PortCrackConfig-配置错误!");
        }
        final String channelCode = subscribe.getChannel();
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("id", subscribe.getId());
        dataNode.put("mobile", mobile);
        dataNode.put("orderPage", StringUtils.isNotBlank(subscribe.getSource())?subscribe.getSource():portCrackConfig.getSourceUrl());
        dataNode.put("serviceId", channelCode+"@"+portCrackConfig.getCompanyName());
        dataNode.put("delay", 0L);
        String raw = dataNode.toString();
        log.info("推送号码至即时开通服务器[四川移动业务]=>raw: {}",raw);
        return push(subscribe, raw);
    }

    /**
     * 处理四川移动业务
     * @param subscribe
     * @return
     */
    public Result<?> handleScyd(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        Result<?> result = pushScyd(subscribe);
        return CommonConstant.SC_OK_200.equals(result.getCode()) ? noauthResult : errorResult;
    }


    public static void main(String[] args) {
        String json = "{\n" + "  \"code\" : 200,\n" + "  \"message\" : \"success\"\n" + "}";
        System.out.println(json);
        System.out.println(StringUtils.normalizeSpace(json));

        final String repeat = StringUtils.repeat(MobileRegionResult.ISP_LIANTONG, 18);
        System.out.println("repeat = " + repeat);

        final ObjectNode objectNode = new ObjectMapper().createObjectNode();
        objectNode.put("transactionId","111");
        objectNode.put("smsCode","666666");
        System.out.println("objectNode = " + objectNode.toString());
    }

    public Result<?> handleUnionMemberExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.asMemberQuery(mobile, subscribe.getChannel());

        //返回码为000000 状态为"0"表示已有包月
        if(remoteResult.isAsMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);

            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);

            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }

    public Result<?> handleOutsideUnionMemberExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);

        final String mobile = subscribe.getMobile();

        final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        final RemoteResult remoteResult = miguApiService.asMemberQuery(mobile, subscribe.getChannel());

        //返回码为000000 状态为"0"表示已有包月
        if(remoteResult.isAsMember()){
            //3表示已有包月
//            subscribeService.lambdaUpdate().eq(Subscribe::getId,id)
//                    .set(Subscribe::getStatus,SUBSCRIBE_STATUS_MONTHLY_EXISTS).set(Subscribe::getResult,"已有包月").update();

            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);

            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);

            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return errorResult;
        }
    }





    /**
     * vr破解
     * @param subscribe
     * @return
     */
    public Result<?> handleVRWujiongExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final IMiGuKuaiYouVRJingMengService vrJingMengService = SpringContextUtils.getBean(IMiGuKuaiYouVRJingMengService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        //查询是否已开通了包月
        final FebsResponse febsResponse = vrJingMengService.vrJingMengGetUser(mobile,subscribe.getChannel());
        //状态为"200"表示已有包月
        if(febsResponse.isOK()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }
        final WujiongCrackResult wujiongCrackResult = SpringContextUtils.getBean(WujiongCrackService.class).getSms(mobile,subscribe.getChannel());
        if(wujiongCrackResult.isSubOk()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setResult(wujiongCrackResult.getResultMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(wujiongCrackResult.getResultMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }

    /**
     * vr破解
     * @param subscribe
     * @return
     */
    public Result<?> handleBizFuseVRWujiongExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final IMiGuKuaiYouVRJingMengService vrJingMengService = SpringContextUtils.getBean(IMiGuKuaiYouVRJingMengService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        //查询是否已开通了包月
        final FebsResponse febsResponse = vrJingMengService.vrJingMengGetUser(mobile,subscribe.getChannel());
        //状态为"200"表示已有包月
        if(febsResponse.isOK()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }
        final WujiongCrackResult wujiongCrackResult = SpringContextUtils.getBean(WujiongCrackService.class).getSms(mobile,subscribe.getChannel());
        if(wujiongCrackResult.isSubOk()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setResult(wujiongCrackResult.getResultMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(wujiongCrackResult.getResultMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }

    /**
     * 河图破解
     * @param subscribe
     * @return
     */
    public Result<?> handleHetuWujiongExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final WujiongCrackResult wujiongCrackResult = SpringContextUtils.getBean(WujiongCrackService.class).getSms(mobile,subscribe.getChannel());
        if(wujiongCrackResult.isHetuGetOk()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setResult(wujiongCrackResult.getMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(wujiongCrackResult.getMsg());
            upd.setIspOrderNo(wujiongCrackResult.getSid());
            subscribeService.updateSubscribeDbAndEs(upd);
            return errorResult;
        }
    }

    public Result<?> handleHetuCaoWeiExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
                return Result.error("请求验证码过快!");
            }
            return errorResult;
        }
    }


    public Result<?> handleKjcjhyExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVRCaoWeiCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
                return Result.error("请求验证码过快!");
            }
            return errorResult;
        }
    }


    /**
     * 骏伯流量包发送验证码
     * @param subscribe
     * @return
     */
    public Result<?> handleJunBoLiuLiangBaoExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final String ispOrderNo = subscribe.getIspOrderNo();
        final String channel = subscribe.getChannel();
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        Subscribe sub=subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, channel)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .between(Subscribe::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .last("limit 1")
                .one();
        //状态为"200"表示已有包月
        if(sub!=null){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        ObjectNode smsCode =mapper.createObjectNode();
        JunBoLiuLiangBaoConfig junBoLiuLiangBaoConfig=junBoLiuLiangBaoProperties.getJunBoLiuLiangBaoMap().get(channel);
        if(junBoLiuLiangBaoConfig==null){
            return errorResult;
        }
        smsCode.put("pid",junBoLiuLiangBaoConfig.getPid());
        smsCode.put("sysOrderId",ispOrderNo);
        smsCode.put("productCode",junBoLiuLiangBaoConfig.getProductCode());
        smsCode.put("url",junBoLiuLiangBaoConfig.getUrl());
        smsCode.put("contactNumber",mobile);
        try {
            final String content =pushJunBoLiuLiang(junBoLiuLiangBaoConfig.getSmsCodeUrl(), smsCode,"骏伯流量包发送验证码");
            if(StringUtils.isBlank(content)){
                return errorResult;
            }
            JsonNode tree = mapper.readTree(content);
            String code = tree.at("/code").asText();
            String message = tree.at("/message").asText();
            if(!"0000".equals(code)){
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(message);
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error(message);
            }
//            String orderCode = tree.at("/data/orderCode").asText();
            String responseCode = tree.at("/data/responseCode").asText();
            String msg = tree.at("/data/msg").asText();

            if(!"0".equals(responseCode)) {
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(msg);
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error(msg);
            }
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setResult(msg);
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return errorResult;
    }
    /**
     * 发起http请求
     */
    public String pushJunBoLiuLiang(String url, Object fromValue,String msg) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return pushJunBoLiuLiangPostJson(url, raw,msg);
    }
    public String pushJunBoLiuLiangPostJson(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }


    /**
     * 骏伯流量包校验验证码
     * @param subscribe
     * @return
     */
    public Result<?> handleJunBoLiuLiangBaoCheckSmsCode(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final String mobile = subscribe.getMobile();
        final String ispOrderNo = subscribe.getIspOrderNo();
        final String channel = subscribe.getChannel();
        final String smsCode = subscribe.getSmsCode();
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        Subscribe sub=subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, channel)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .between(Subscribe::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .last("limit 1")
                .one();
        //状态为"200"表示已有包月
        if(sub!=null){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        ObjectNode checkSmsCode =mapper.createObjectNode();
        JunBoLiuLiangBaoConfig junBoLiuLiangBaoConfig=junBoLiuLiangBaoProperties.getJunBoLiuLiangBaoMap().get(channel);
        if(junBoLiuLiangBaoConfig==null){
            return errorResult;
        }
        checkSmsCode.put("pid",junBoLiuLiangBaoConfig.getPid());
        checkSmsCode.put("sysOrderId",ispOrderNo);
        checkSmsCode.put("productCode",junBoLiuLiangBaoConfig.getProductCode());
        checkSmsCode.put("url",junBoLiuLiangBaoConfig.getUrl());
        checkSmsCode.put("contactNumber",mobile);
        checkSmsCode.put("authCode",smsCode);

        try {
            final String content =pushJunBoLiuLiang(junBoLiuLiangBaoConfig.getCheckSmsCodeUrl(), checkSmsCode,"骏伯流量包校验验证码");
            if(StringUtils.isBlank(content)){
                return errorResult;
            }
            JsonNode tree = mapper.readTree(content);
            String code = tree.at("/code").asText();
            String message = tree.at("/message").asText();
            if(!"0000".equals(code)){
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setResult(message);
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error(message);
            }
            String responseCode = tree.at("/data/responseCode").asText();
            String msg = tree.at("/data/msg").asText();
            if(!"0".equals(responseCode)) {
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setResult(msg);
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error(msg);
            }
            return Result.ok();
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return errorResult;
    }


    /**
     * 骏伯流量包提交验证码
     * @param subscribe
     * @return
     */
    public Result<?> handleJunBoLiuLiangBaoSubmit(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final String mobile = subscribe.getMobile();
        final String ispOrderNo = subscribe.getIspOrderNo();
        final String channel = subscribe.getChannel();
        final String smsCode = subscribe.getSmsCode();
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        //查询是否已开通了包月
        Subscribe sub=subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, channel)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .between(Subscribe::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .last("limit 1")
                .one();
        //状态为"200"表示已有包月
        if(sub!=null){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        ObjectNode checkSmsCode =mapper.createObjectNode();
        JunBoLiuLiangBaoConfig junBoLiuLiangBaoConfig=junBoLiuLiangBaoProperties.getJunBoLiuLiangBaoMap().get(channel);
        if(junBoLiuLiangBaoConfig==null){
            return errorResult;
        }
        checkSmsCode.put("pid",junBoLiuLiangBaoConfig.getPid());
        checkSmsCode.put("sysOrderId",ispOrderNo);
        checkSmsCode.put("productCode",junBoLiuLiangBaoConfig.getProductCode());
        checkSmsCode.put("url",junBoLiuLiangBaoConfig.getUrl());
        checkSmsCode.put("contactNumber",mobile);
        checkSmsCode.put("authCode",smsCode);
        try {
            final String content =pushJunBoLiuLiang(junBoLiuLiangBaoConfig.getSubmitCodeUrl(), checkSmsCode,"骏伯流量包提交验证码");
            if(StringUtils.isBlank(content)){
                return errorResult;
            }
            JsonNode tree = mapper.readTree(content);
            String code = tree.at("/code").asText();
            String message = tree.at("/message").asText();
            if(!"0000".equals(code)){
                return Result.error(message);
            }
            String responseCode = tree.at("/data/responseCode").asText();
            String msg = tree.at("/data/msg").asText();
            if(!"0".equals(responseCode)) {
                return Result.error(msg);
            }
            return Result.ok();
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return errorResult;
    }



    public Result<?> handleVRCaoWeiExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final IMiGuKuaiYouVRJingMengService vrJingMengService = SpringContextUtils.getBean(IMiGuKuaiYouVRJingMengService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        //查询是否已开通了包月
        final FebsResponse febsResponse = vrJingMengService.vrJingMengGetUser(mobile,subscribe.getChannel());
        //状态为"200"表示已有包月
        if(febsResponse.isOK()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVRCaoWeiCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return noauthResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
                return Result.error("请求验证码过快!");
            }
            return errorResult;
        }
    }


    public Result<?> handleBizFuseVRCaoWeiExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> okResult = Result.ok("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final IMiGuKuaiYouVRJingMengService vrJingMengService = SpringContextUtils.getBean(IMiGuKuaiYouVRJingMengService.class);
        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        //查询是否已开通了包月
        final FebsResponse febsResponse = vrJingMengService.vrJingMengGetUser(mobile,subscribe.getChannel());
        //状态为"200"表示已有包月
        if(febsResponse.isOK()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }

        final BillingResult billingResult = SpringContextUtils.getBean(YidongVRCaoWeiCrackService.class).getSms(mobile,subscribe.getChannel());
        if(billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return okResult;
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
                return Result.error("请求验证码过快!");
            }
            return errorResult;
        }
    }


    /**
     * 电信支付宝开通接口
     * @param subscribe
     * @return
     */
    public Result<?> handleDianXinAliPayVrbtSubmit(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> noauthResult = Result.ok("验证码已发送", id);
        final DianxinVrbtService dianxinVrbtService = SpringContextUtils.getBean(DianxinVrbtService.class);

        DianxinResp dianxinOrder=dianxinVrbtService.ismpOrderLaunchedThird(subscribe.getMobile(), BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
        if(!dianxinOrder.isOK()){
            return Result.error(dianxinOrder.getResMessage());
        }

        DianxinResp dianxinSms=dianxinVrbtService.ismpVerifyCodeSend(subscribe.getMobile(),dianxinOrder.getOrderNo(), BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
        if(!dianxinSms.isOK()){
            return Result.error(dianxinSms.getResMessage());
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setExtra(dianxinOrder.getOrderNo());
        subscribeService.updateSubscribeDbAndEs(upd);
        return noauthResult;
    }


    public Result<?> handleJiangXiVrbtExistsBillingCrack(Subscribe subscribe) {
        final String id = subscribe.getId();
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final Result<Object> noauthResult = Result.noauth("验证码已发送", id);
        final String mobile = subscribe.getMobile();
        final JiangXiVrbtBusinessServiceImpl jiangXiVrbtService = SpringContextUtils.getBean(JiangXiVrbtBusinessServiceImpl.class);

        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
        //查询是否已开通了包月
        final boolean isSubMon = jiangXiVrbtService.isSubMon(subscribe.getMobile(),subscribe.getChannel());
        //true表示已有包月
        if(isSubMon){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            return bizExistsResult;
        }
        final Result<?> result = jiangXiVrbtService.getSms(mobile,subscribe.getChannel(),id);
        return result;
    }
}
