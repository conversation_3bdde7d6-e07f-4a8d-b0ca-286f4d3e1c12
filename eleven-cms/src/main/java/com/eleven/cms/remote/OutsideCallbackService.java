package com.eleven.cms.remote;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.*;
import com.eleven.cms.entity.OutsideConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyTaskService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.*;
import com.eleven.cms.util.sign.MCJSignUtils;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.utils.UuidUtils;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2021-10-14 15:55
 */
@Service
@Slf4j
public class OutsideCallbackService {

    public static final String LOG_TAG = "外部订购回调api";
    public static final String LOG_TAG_JUNBO = "四川移动(骏伯)回调api";
    public static final String LOG_TAG_QIYIN = "视频彩铃(祺音)回调api";
    public static final String LOG_TAG_KUAIMA = "四川移动(快马)回调api";
    public static final String LOG_TAG_KUAIMA_MINIAPP = "四川移动(快马小程序)回调api";
    public static final String LOG_TAG_LBTX = "四川移动(联保天下)回调api";
    public static final String LOG_TAG_LBTX_TLVRBTLX = "四川移动(联保天下)同旅视频彩铃乐享包专用回调api";
    public static final String LOG_TAG_STP_MAIDANGLAO = "四川移动(斯特普)麦当劳专用回调api";
    public static final String LOG_TAG_WANDA_GYQYB = "四川移动(万达)观影权益包专用回调api";
    public static final String LOG_TAG_HN_HDLHHY = "河南移动横店联合会员专用回调api";
    public static final String LOG_TAG_FENGZHUSHOU = "蜂助手回调api";
    public static final String LOG_TAG_JXYD_YINLIAN_MEMBER = "江西移动银联双V会员专用回调api";
    public static final String LOG_TAG_KUAIMA_SRQY = "四川移动(快马生日权益)回调api";
    public static final String LOG_TAG_ZSY = "中视游专用回调api";
    public static final String LOG_TAG_MCJ = "萌宠集回调api";
    public static final String LOG_UNSUB_TAG = "外部订购退订回调api";
    public static final String LOG_TAG_LH_HJX = "老霍呼叫秀回调api";
    public static final String LOG_TAG_ZC_LY = "中财乐扬四川移动5G新通话-明星来电白银组合包C专用回调api";
    public static final String LOG_TAG_CAIXUN = "彩讯呼叫秀专用回调api";
    public static final String LOG_TAG_ZTM = "枣米糖专用回调api";
    public static final String LOG_TAG_SHUZIREN = "数字人api";
    public static final String LOG_TAG_AZT = "安众图江西移动专用回调api";




    public static final String DELIMITER_AMP = "&";
    private static final MediaType mediaType = MediaType.parse("application/json");

    public static final String CALLBACK_URL_ZSY = "http://103.44.245.5:3001/Notity/NotiftyInterface100.aspx";
    public static final String CALLBACK_URL_MCJ = "http://8.134.120.170:8087/api/channel/notice";
    public static final String CALLBACK_LH_HJX = "https://www.etcervice.com:9443/lr-server-api/hjx/yrjy/order";

    //安众图回调地址
    public static final String CALLBACK_URL_AZT = "https://a.cdddy.cn//user/server/push-data";



    public static final String CALLBACK_URL_ZMT = "https://zaomt.com/api-business/channelApiZMT/synchronousOrder";
    public static final String TOKEN_URL_ZMT = "https://zaomt.com/api-auth/auth/token/getToken";

    public static final Integer STATUS_SUCC= 1; //成功


    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;

    @Autowired
    PushSubscribeService pushSubscribeService;

    @Autowired
    ISubscribeService subscribeService;

    @Autowired
    OutsideProperties outsideProperties;

    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;

    @Autowired
    LbtxSichuanYidongProperties lbtxSichuanYidongProperties;

    @Autowired
    LbtxSichuanTlvrbtlxYidongProperties lbtxSichuanTlvrbtlxYidongProperties;
    @Autowired
    StpSichuanYidongProperties stpSichuanYidongProperties;
    @Autowired
    WandaSichuanYidongProperties wandaSichuanYidongProperties;
    @Autowired
    WandaHenanYidongProperties wandaHenanYidongProperties;
    @Autowired
    FengzhushouSichuanYidongProperties fengzhushouSichuanYidongProperties;
    @Autowired
    JiangxiYidongProperties jiangxiYidongProperties;

    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    KuaimaCallbackProperties kuaimaCallbackProperties;
    @Autowired
    ZhongcaiLeyangSichuanYidongProperties zhongcaiLeyangSichuanYidongProperties;
    @Autowired
    CaixunProperties caixunProperties;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ZmtCallbackProperties zmtCallbackProperties;
    @Autowired
    ShuzirenCallbackProperties shuzirenCallbackProperties;


    //回调返回成功
    public static final String RESULT_SUCCESS = "SUCCESS";


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                .compressed()
                .insecure()  //不检查自签名证书
                //.connectTimeout(120)
                //.retry(5)
                .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println, options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Async("outSidecallbackExecutor")
    public void outsideCallbackAsync(Subscribe subscribe, String msg) {
        outsideCallback(subscribe, msg);
    }

    public void outsideCallback(Subscribe subscribe, String msg) {
        //        String orderId = subscribe.getDeviceInfo();
        Subscribe lastSubscribe = subscribeService.getById(subscribe.getId());
        //防止重复回调
        String SUCCESS_KEY = CacheConstant.CMS_OUTSIDE_NOTIFY_SUCCESS_CACHE + ":" + subscribe.getSubChannel() + ":" + subscribe.getMobile();
        if (SUBSCRIBE_STATUS_SUCCESS.equals(lastSubscribe.getStatus()) && redisUtil.hasKey(SUCCESS_KEY)) {
            log.warn("{}-跳过当日成功订单,手机号:{},渠道号:{}", LOG_TAG, subscribe.getMobile(), subscribe.getChannel());
            return;
        }
        FormBody.Builder builder = new FormBody.Builder();
        builder.add("orderNo", CallbackNotifyTaskService.OUT_SUB_CHANNEL_03.equals(subscribe.getSubChannel()) ? subscribe.getIspOrderNo() : subscribe.getId());
        builder.add("mobile", subscribe.getMobile());
        //扣量的错误原因
        String errorMsg = "计费开通结果=>F500:电信视频彩铃包月业务下单失败:非常抱歉，本号码暂不支持开通本业务，建议用其他手机号码重新尝试。";
        String yidongErrorMsg = "计费开通结果=>你已开通,请勿重复开通。";
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subscribe.getSubChannel());
        if (outsideConfig == null) {
            return;
        }
        String notifyUrl = outsideConfig.getNotifyUrl();
        if (StringUtils.isEmpty(notifyUrl)) {
            log.warn("外部渠道回调链接为空,渠道号{},手机号:{}", outsideConfig.getOutChannel(), subscribe.getMobile());
            return;
        }
        //获取扣量比例
        //        Integer deduction = outsideProperties.getDeduction(subscribe.getSubChannel());
        Integer deduction = outsideConfig.getDeductionRatio() == null ? 0 : outsideConfig.getDeductionRatio();
        boolean isDeduction = false;
        String deductionTag = "1";
        //扣量
        if (BizConstant.SUBSCRIBE_STATUS_SUCCESS.equals(lastSubscribe.getStatus()) && deduction > 0) {
            //provinceCode为1直接扣量
            isDeduction = deductionTag.equals(lastSubscribe.getProvinceCode()) || RandomUtils.isInRatio(deduction);
        }
        switch (lastSubscribe.getStatus()) {
            case 1:
                builder.add("status", !isDeduction ? "1" : "0");
                builder.add("msg", !isDeduction ? "开通成功" : MobileRegionResult.ISP_DIANXIN.equals(subscribe.getIsp()) ? errorMsg : yidongErrorMsg);
                break;
            default:
                builder.add("status", "0");
                builder.add("msg", StringUtils.isNotBlank(subscribe.getResult()) ? subscribe.getResult() : "开通失败");
        }
        if (isDeduction && !deductionTag.equals(lastSubscribe.getProvinceCode())) {
            Subscribe upd = new Subscribe();
            upd.setId(lastSubscribe.getId());
            upd.setProvinceCode(deductionTag);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        FormBody formBody = builder.build();
        try {
            if (StringUtils.isEmpty(notifyUrl)) {
                log.warn("{}-未配置回调地址-ded:{},手机号:{},id:{},notifyUrl:{},请求参数: orderNo:{},status:{},msg:{}", LOG_TAG, isDeduction, subscribe.getMobile(), subscribe.getId(), notifyUrl, formBody.value(0), formBody.value(2), formBody.value(3));
                return;
            }
            log.info("{}-ded:{},手机号:{},id:{},子渠道号:{},notifyUrl:{},请求参数: orderNo:{},status:{},msg:{}", LOG_TAG, isDeduction, subscribe.getMobile(), subscribe.getId(), subscribe.getSubChannel(), notifyUrl, formBody.value(0), formBody.value(2), formBody.value(3));
            Request request = new Request.Builder().url(notifyUrl).post(formBody).build();
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-手机号:{},id:{},子渠道号:{},开通结果:{},回调响应:{}", LOG_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getSubChannel(), lastSubscribe.getStatus(), result);
                //            return RESULT_SUCCESS.equalsIgnoreCase(result);
                //成功数据回调成功写入redis
                if (SUBSCRIBE_STATUS_SUCCESS.equals(lastSubscribe.getStatus())) {
                    redisUtil.set(SUCCESS_KEY, 1, ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX));
                }
            } catch (Exception e) {
                log.info("{}-手机号:{},id:{},子渠道号:{},开通结果:{},回调异常:", LOG_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getSubChannel(), lastSubscribe.getStatus(), e);
                //            return false;
            }
        } catch (Exception e) {
            log.error("外部回调异常", e);
        }
    }

    /**
     * 骏伯1小时退订
     *
     * @param subscribe
     */
    @Async("outSidecallbackExecutor")
    public void unsubscribeNotifyAsync(Subscribe subscribe) {
        unsubscribeNotify(subscribe);
    }

    public void unsubscribeNotify(Subscribe subscribe) {
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subscribe.getSubChannel());
        if (outsideConfig == null) {
            return;
        }
        String notifyUrl = outsideConfig.getNotifyUrl();
        //Subscribe lastSubscribe = subscribeService.getById(subscribe.getId());
        FormBody.Builder builder = new FormBody.Builder();
        builder.add("orderNo", subscribe.getId());
        builder.add("status", "2");
        builder.add("msg", "1小时退订");

        FormBody formBody = builder.build();
        log.info("{}-退订回调-手机号:{},id:{},渠道号:{},子渠道号:{}", LOG_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel());

        Request request = new Request.Builder().url(notifyUrl).post(formBody).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-退订回调-手机号:{},id:{},渠道号:{},子渠道号:{},回调响应:{}", LOG_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel(), result);
        } catch (Exception e) {
            log.info("{}-退订回调-手机号:{},id:{},渠道号:{},子渠道号:{},回调异常:", LOG_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel(), e);
        }
    }

    @Async("outSidecallbackExecutor")
    public void junboCallbackAsync(Subscribe subscribe, String callbackUrl) {
        junboCallback(subscribe,callbackUrl);
    }

    public void junboCallback(Subscribe subscribe, String callbackUrl) {
        JSONObject json = new JSONObject();
        json.put("pid", getJunboPid(subscribe));
        json.put("orderCode", subscribe.getIspOrderNo());
        json.put("contactNumber", subscribe.getMobile());
        json.put("sysOrderStatusStr", subscribe.getStatus().equals(1) ? "0" : "1");
        json.put("remark", subscribe.getResult());
        json.put("url", subscribe.getSource());
        json.put("createTime", DateUtils.datetimeFormat.get().format(subscribe.getCreateTime()));
        String requestBody = String.valueOf(json);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), requestBody);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-订单号:{},手机号:{},请求:{},参数:{}", LOG_TAG_JUNBO, subscribe.getIspOrderNo(), subscribe.getMobile(), callbackUrl, requestBody);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-订单号:{},手机号:{},回调响应:{}", LOG_TAG_JUNBO, subscribe.getIspOrderNo(), subscribe.getMobile(), result);
        } catch (Exception e) {
            log.error("{}-订单号:{},手机号:{},开通结果:{},回调异常:", LOG_TAG_JUNBO, subscribe.getIspOrderNo(), subscribe.getMobile(), e);
        }
    }

    /**
     * 视频彩铃祺音回调(只回调成功的)
     *
     * @param subscribe
     * @return
     */
    public boolean qiyinCallback(Subscribe subscribe) {
        if (!SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            return false;
        }
        final String timeSpan = DateUtils.getCurrentTimestamp();
        final String mobile = subscribe.getMobile();
        final String sign = DigestUtils.md5DigestAsHex((mobile + outsideProperties.getVrbtQiyinChannel() + timeSpan + outsideProperties.getVrbtQiyinSignKey()).getBytes(StandardCharsets.UTF_8));
        final HttpUrl httpUrl = HttpUrl.parse(outsideProperties.getVrbtQiyinNotifyUrl()).newBuilder()
            .addQueryParameter("phone", mobile)
            .addQueryParameter("channelName", outsideProperties.getVrbtQiyinChannel())
            .addQueryParameter("cmccCode", subscribe.getChannel())
            .addQueryParameter("orderTime", subscribe.getOpenTime().getTime() / 1000L + "")
            //.addQueryParameter("contentId","")
            //.addQueryParameter("contentName","")
            .addQueryParameter("timeSpan", timeSpan)
            .addQueryParameter("sign", sign)
            .build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-手机号:{},回调响应:{}", LOG_TAG_QIYIN, mobile, result);
            return RESULT_SUCCESS.equalsIgnoreCase(result);
        } catch (Exception e) {
            log.error("{}-手机号:{},开通结果:{},回调异常:", LOG_TAG_QIYIN, mobile, e);
            return false;
        }
    }

    public boolean kuaimaCallback(String mobile, String fullResult, String callbackUrl) {
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), fullResult);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-mobile:{},请求:{},参数:{}", LOG_TAG_KUAIMA, mobile, callbackUrl, fullResult);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},回调响应:{}", LOG_TAG_KUAIMA, mobile, result);
            JsonNode jsonNode = mapper.readTree(result);
            return HttpStatus.OK.value() == jsonNode.get("code").asInt();
        } catch (Exception e) {
            log.error("{}-mobile:{},回调异常:", LOG_TAG_KUAIMA, mobile, e);
            return false;
        }
    }

    public boolean kuaimaMiniAppCallback(String mobile, String orderId, String fullResult, String callbackUrl) {
        final HttpUrl httpUrl = HttpUrl.parse(callbackUrl)
            .newBuilder()
            .addQueryParameter("orderid", orderId)
            .addQueryParameter("param", fullResult)
            .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-mobile:{},请求:{},参数:{}", LOG_TAG_KUAIMA_MINIAPP, mobile, callbackUrl, fullResult);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},回调响应:{}", LOG_TAG_KUAIMA_MINIAPP, mobile, result);
            JsonNode jsonNode = mapper.readTree(result);
            return HttpStatus.OK.value() == jsonNode.get("code").asInt();
        } catch (Exception e) {
            log.error("{}-mobile:{},回调异常:", LOG_TAG_KUAIMA_MINIAPP, mobile, e);
            return false;
        }
    }

    public boolean lbtxCallback(String mobile, String orderId, String fullResult, String callbackUrl) {
        final HttpUrl httpUrl = HttpUrl.parse(callbackUrl)
            .newBuilder()
            .addQueryParameter("tranId", orderId)
            .addQueryParameter("result", fullResult)
            .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-mobile:{},请求:{},参数:{}", LOG_TAG_LBTX, mobile, callbackUrl, fullResult);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},回调响应:{}", LOG_TAG_LBTX, mobile, result);
            JsonNode jsonNode = mapper.readTree(result);
            return HttpStatus.OK.value() == jsonNode.get("statusCode").asInt();
        } catch (Exception e) {
            log.error("{}-mobile:{},回调异常:", LOG_TAG_LBTX, mobile, e);
            return false;
        }
    }


    @Async("outSidecallbackExecutor")
    public void lbtxScydCallbackAsync(String mobile, String channelCode, String orderCode, String serialNumber, Integer status, String orderTime, String des) throws JsonProcessingException {

        String callbackUrl = lbtxSichuanYidongProperties.getRequestUrl();
        final HttpUrl httpUrl = HttpUrl.parse(callbackUrl).newBuilder().build();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("header", getHeader("chinamobile.order.callback"));
        dataMap.put("body", getCallbcakBody(mobile, channelCode, orderCode, serialNumber, status, orderTime, des));
        generateSign(dataMap);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(callbackUrl)
            .post(body)
            .tag(dataMap)
            .build();
        log.info("{}-四川移动开通结果回传-手机号:{},请求:{}", LOG_TAG_LBTX, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-四川移动开通结果回传-手机号:{},返回结果:{}", LOG_TAG_LBTX, mobile, result);
        } catch (Exception e) {
            log.info("{}-四川移动开通结果回传-手机号:{},异常:", LOG_TAG_LBTX, mobile, e);
        }
    }

    @Async("outSidecallbackExecutor")
    public void lbtxScydTlvrbtlxCallbackAsync(String mobile, String channelCode, String orderCode, String serialNumber, Integer status, String orderTime, String des) throws JsonProcessingException {

        String callbackUrl = lbtxSichuanTlvrbtlxYidongProperties.getRequestUrl();
        //final HttpUrl httpUrl = HttpUrl.parse(callbackUrl).newBuilder().build();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("header", getTlvrbtlxHeader("chinamobile.order.callback"));
        dataMap.put("body", getCallbcakBody(mobile, channelCode, orderCode, serialNumber, status, orderTime, des));
        generateTlvrbtlxSign(dataMap);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(callbackUrl)
            .post(body)
            .tag(dataMap)
            .build();
        log.info("{}-四川移动开通结果回传-手机号:{},请求:{}", LOG_TAG_LBTX_TLVRBTLX, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-四川移动开通结果回传-手机号:{},返回结果:{}", LOG_TAG_LBTX_TLVRBTLX, mobile, result);
        } catch (Exception e) {
            log.info("{}-四川移动开通结果回传-手机号:{},异常:", LOG_TAG_LBTX_TLVRBTLX, mobile, e);
        }
    }

    /**
     * 四川移动(斯特普)麦当劳专用回调
     *
     * @param
     */
    @Async("outSidecallbackExecutor")
    public void stpCallbackAsync(String mobile, String ispOrderNo, Date orderTime, String appPackage, String result) {
       stpCallback(mobile, ispOrderNo, orderTime, appPackage, result);
    }

    public void stpCallback(String mobile, String ispOrderNo, Date orderTime, String appPackage, String result) {
        String callbackUrl = stpSichuanYidongProperties.getCallbackUrl();
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("apiUser", stpSichuanYidongProperties.getApiUser());
        paramNode.put("cid", stpSichuanYidongProperties.getCid());
        paramNode.put("mobile", mobile);
        final TreeMap<String, String> treeMap = mapper.convertValue(paramNode, new TypeReference<TreeMap<String, String>>() {
        });
        String queryString = Joiner.on("&")
                // 用指定符号代替空值,key 或者value 为null都会被替换
                .useForNull("")
                .withKeyValueSeparator("=")
                .join(treeMap);
        final String sign = DigestUtils.md5DigestAsHex((queryString + "&apikey=" + stpSichuanYidongProperties.getApiKey()).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        paramNode.put("sign", sign);
        paramNode.set("extra", mapper.createObjectNode()
                .put("order_sn", ispOrderNo)
                .put("order_time", DateFormatUtils.format(orderTime, DateUtils.FULL_TIME_SPLIT_PATTERN))
                .put("package_name", Strings.nullToEmpty(appPackage))
                .put("order_tag", result)
        );
        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-四川移动订单号:{},手机号:{},请求:{},参数:{}", LOG_TAG_STP_MAIDANGLAO, ispOrderNo, mobile, callbackUrl, paramNode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-四川移动订单号:{},手机号:{},回调响应:{}", LOG_TAG_STP_MAIDANGLAO, ispOrderNo, mobile, content);
        } catch (Exception e) {
            log.error("{}-四川移动订单号:{},手机号:{},开通结果:{},回调异常:", LOG_TAG_STP_MAIDANGLAO, ispOrderNo, mobile, e);
        }
    }

    /**
     * 四川移动(万达)观影权益专用回调
     *
     * @param
     * @param channel
     */
    @Async("outSidecallbackExecutor")
    public void wandaCallbackAsync(String mobile, String ispOrderNo, Date orderTime, String channel) {
        wandaCallback(mobile, ispOrderNo, orderTime, channel);
    }

    public void wandaCallback(String mobile, String ispOrderNo, Date orderTime, String channel) {
        String callbackUrl = wandaSichuanYidongProperties.getCallbackUrl();
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("apiUser", wandaSichuanYidongProperties.getApiUser());
        paramNode.put("cid", wandaSichuanYidongProperties.getChannelCidMap().get(channel));
        paramNode.put("mobile", mobile);
        final TreeMap<String, String> treeMap = mapper.convertValue(paramNode, new TypeReference<TreeMap<String, String>>() {
        });
        String queryString = Joiner.on("&")
                // 用指定符号代替空值,key 或者value 为null都会被替换
                .useForNull("")
                .withKeyValueSeparator("=")
                .join(treeMap);
        final String sign = DigestUtils.md5DigestAsHex((queryString + "&apikey=" + wandaSichuanYidongProperties.getApiKey()).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        paramNode.put("sign", sign);
        paramNode.set("extra", mapper.createObjectNode()
                .put("order_sn", ispOrderNo)
                .put("order_time", DateFormatUtils.format(orderTime, DateUtils.FULL_TIME_SPLIT_PATTERN))
        );
        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-四川移动订单号:{},手机号:{},请求:{},参数:{}", LOG_TAG_WANDA_GYQYB, ispOrderNo, mobile, callbackUrl, paramNode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-四川移动订单号:{},手机号:{},回调响应:{}", LOG_TAG_WANDA_GYQYB, ispOrderNo, mobile, content);
        } catch (Exception e) {
            log.error("{}-四川移动订单号:{},手机号:{},开通结果:{},回调异常:", LOG_TAG_WANDA_GYQYB, ispOrderNo, mobile, e);
        }
    }


    /**
     * 四川移动(万达)观影权益专用回调
     *
     * @param
     * @param channel
     */
    @Async("outSidecallbackExecutor")
    public void henanWandaCallbackAsync(String mobile, String ispOrderNo, Date orderTime, String channel) {
        henanWandaCallback(mobile, ispOrderNo, orderTime, channel);
    }

    public void henanWandaCallback(String mobile, String ispOrderNo, Date orderTime, String channel) {
        String callbackUrl = wandaHenanYidongProperties.getCallbackUrl();
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("apiUser", wandaHenanYidongProperties.getApiUser());
        paramNode.put("cid", wandaHenanYidongProperties.getChannelCidMap().get(channel));
        paramNode.put("mobile", mobile);
        final TreeMap<String, String> treeMap = mapper.convertValue(paramNode, new TypeReference<TreeMap<String, String>>() {
        });
        String queryString = Joiner.on("&")
            // 用指定符号代替空值,key 或者value 为null都会被替换
            .useForNull("")
            .withKeyValueSeparator("=")
            .join(treeMap);
        final String sign = DigestUtils.md5DigestAsHex((queryString + "&apikey=" + wandaHenanYidongProperties.getApiKey()).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        paramNode.put("sign", sign);
        paramNode.set("extra", mapper.createObjectNode()
            .put("order_sn", ispOrderNo)
            .put("order_time", DateFormatUtils.format(orderTime, DateUtils.FULL_TIME_SPLIT_PATTERN))
        );
        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-四川移动订单号:{},手机号:{},请求:{},参数:{}", LOG_TAG_HN_HDLHHY, ispOrderNo, mobile, callbackUrl, paramNode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-四川移动订单号:{},手机号:{},回调响应:{}", LOG_TAG_HN_HDLHHY, ispOrderNo, mobile, content);
        } catch (Exception e) {
            log.error("{}-四川移动订单号:{},手机号:{},开通结果:{},回调异常:", LOG_TAG_HN_HDLHHY, ispOrderNo, mobile, e);
        }
    }


    /**
     * 四川移动(快马)生日权益专用回调
     *
     * @param
     */
    @Async("outSidecallbackExecutor")
    public void kuaimaSrqyCallbackAsync(String mobile, String ispOrderNo, String channel, String msg, Date orderTime, Integer status) throws Exception {
        String callbackUrl = kuaimaCallbackProperties.getCallbackUrl();
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("trade_no", UuidUtils.getUUID());
        paramNode.put("phone", mobile);
        paramNode.put("product_no", sichuanMobileFlowPacketProperties.getBizCodeByChannel(channel));
        paramNode.put("channel_no", "125");
        paramNode.put("order_msg", msg);
        paramNode.put("handle_time", DateFormatUtils.format(orderTime, DateUtils.FULL_TIME_SPLIT_PATTERN));
        paramNode.put("pass_flag", SUBSCRIBE_STATUS_SUCCESS.equals(status) ? "Y" : "N");

        String bizContent = KuaimaMd5.aesEncrypt(paramNode.toString(), kuaimaCallbackProperties.getSecretKey());
        paramNode.removeAll();
        paramNode.put("app_id", kuaimaCallbackProperties.getAppId());
        paramNode.put("version", "1.0");
        paramNode.put("time_stamp", System.currentTimeMillis() / 1000);
        paramNode.put("sign_type", "MD5");
        paramNode.put("biz_content", bizContent);
        final TreeMap<String, Object> treeMap = mapper.convertValue(paramNode, new TypeReference<TreeMap<String, Object>>() {
        });
//        String queryString = Joiner.on("&")
//                // 用指定符号代替空值,key 或者value 为null都会被替换
//                .useForNull("")
//                .withKeyValueSeparator("=")
//                .join(treeMap);
//        final String sign = DigestUtils.md5DigestAsHex((queryString + kuaimaCallbackProperties.getSecretKey()).getBytes(StandardCharsets.UTF_8));
        String sign = KuaimaMd5.createSign(treeMap, kuaimaCallbackProperties.getSecretKey());
        paramNode.put("sign", sign);
        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-四川移动订单号:{},手机号:{},开通状态:{},请求:{},参数:{}", LOG_TAG_KUAIMA_SRQY, ispOrderNo, mobile, status, callbackUrl, paramNode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-四川移动订单号:{},手机号:{},开通状态:{},回调响应:{}", LOG_TAG_KUAIMA_SRQY, ispOrderNo, mobile, status, content);
        } catch (Exception e) {
            log.error("{}-四川移动订单号:{},手机号:{},开通状态:{},开通结果:{},回调异常:", LOG_TAG_KUAIMA_SRQY, ispOrderNo, mobile, status, e);
        }
    }


    //53249-【悠然】四川移动19.9随心看
    //53250-【悠然】四川移动30元延长壳牌加油包
    //53251-【悠然】四川移动30元石油黄金会员月包
    public static String getJunboPid(Subscribe subscribe) {
        String pid = "";
        switch (subscribe.getChannel()) {
            case BizConstant.BIZ_CHANNEL_SCYD_HSH:
                pid = "27897";
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_BJHY:
                pid = "27895";
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_LLB_30:
                pid = "27894";
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_SXK:
                pid = "53249";
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_KPJYB:
                pid = "53250";
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_ZSYHJYB:
                pid = "53251";
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_XSVRBTYL:
                pid = "61924";
                break;
            case BizConstant.BIZ_CHANNEL_SCYD_YHSZD:
                pid = "68997";
                break;
        }
        return pid;
    }

    private Map<String, Object> getHeader(String businessCode) {
        Map<String, Object> headerMap = new TreeMap<>();
        headerMap.put("productCode", "CMSC01");
        headerMap.put("businessCode", businessCode);
        headerMap.put("sequence", IdWorker.get32UUID());
        headerMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        headerMap.put("appid", lbtxSichuanYidongProperties.getAppId());
        return headerMap;
    }

    private Map<String, Object> getTlvrbtlxHeader(String businessCode) {
        Map<String, Object> headerMap = new TreeMap<>();
        headerMap.put("productCode", "CMSC01");
        headerMap.put("businessCode", businessCode);
        headerMap.put("sequence", IdWorker.get32UUID());
        headerMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        headerMap.put("appid", lbtxSichuanTlvrbtlxYidongProperties.getAppId());
        return headerMap;
    }

    private String getCallbcakBody(String mobile, String channelCode, String orderCode, String serialNumber, Integer status, String orderTime, String des) throws JsonProcessingException {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("mobile", mobile);
        bodyMap.put("productCode", sichuanMobileFlowPacketProperties.getBizCodeByChannel(channelCode));
        bodyMap.put("orderCode", orderCode);
        bodyMap.put("serialNumber", serialNumber);
        bodyMap.put("status", status);
        bodyMap.put("orderTime", orderTime);
        bodyMap.put("des", des);
        return mapper.writeValueAsString(bodyMap);
    }

    private void generateSign(Map<String, Object> dataMap) {
        Map<String, Object> headerMap = (TreeMap<String, Object>) dataMap.get("header");
        headerMap.put("body", dataMap.get("body"));
        String paramStr = headerMap.entrySet().stream().map(key -> key.toString()).collect(Collectors.joining(DELIMITER_AMP));
        String sign = SecureUtil.hmacSha256(lbtxSichuanYidongProperties.getSecret()).digestHex(MD5.create().digest(paramStr, StandardCharsets.UTF_8));
        headerMap.remove("body");
        headerMap.put("sign", sign);
    }

    private void generateTlvrbtlxSign(Map<String, Object> dataMap) {
        Map<String, Object> headerMap = (TreeMap<String, Object>) dataMap.get("header");
        headerMap.put("body", dataMap.get("body"));
        String paramStr = headerMap.entrySet().stream().map(key -> key.toString()).collect(Collectors.joining(DELIMITER_AMP));
        String sign = SecureUtil.hmacSha256(lbtxSichuanTlvrbtlxYidongProperties.getSecret()).digestHex(MD5.create().digest(paramStr, StandardCharsets.UTF_8));
        headerMap.remove("body");
        headerMap.put("sign", sign);
    }


    @Async("outSidecallbackExecutor")
    public void fengshushouCallbackAsync(String mobile, String code, String msg, String result) {
        fengshushouCallback(mobile, code, msg, result);
    }

    public void fengshushouCallback(String mobile, String code, String msg, String result) {
        String callbackUrl = fengzhushouSichuanYidongProperties.getCallbackUrl();
        ObjectNode data = mapper.createObjectNode();
        data.put("result", result);
        data.put("res_msg", msg);
        data.put("res_code", code);
        log.info("{}-手机号:{},code:{},msg:{}", LOG_TAG_FENGZHUSHOU, mobile, code, msg);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), data.toString());
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String resultStr = response.body().string();
            log.info("{}-手机号:{},code:{},msg:{},回调响应:{}", LOG_TAG_FENGZHUSHOU, mobile, code, msg, resultStr);
        } catch (Exception e) {
            log.info("{}-手机号:{},code:{},msg:{},,回调异常:", LOG_TAG_FENGZHUSHOU, mobile, code, msg, e);
        }
    }


    /**
     * 江西移动银联双V会员专用回调
     *
     * @param
     */
    @Async("outSidecallbackExecutor")
    public void jxydYinlianMemberCallbackAsync(String mobile, String originId, String channelCode, Integer state, String result, String extra, Date openTime) {
        jxydYinlianMemberCallback(mobile, originId, channelCode, state, result, extra, openTime);
    }

    public void jxydYinlianMemberCallback(String mobile, String originId, String channelCode, Integer state, String result, String extra, Date openTime) {
        String callbackUrl = jiangxiYidongProperties.getJxydYinlianMemberCallbackUrl();
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("mobile", mobile)
                .put("originId", originId)
                .put("product", channelCode)
                .put("state", state)
                .put("result", result)
                .put("extra", extra)
                .put("openTime", DateFormatUtils.format(openTime, DateUtils.FULL_TIME_SPLIT_PATTERN));
        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-订单号:{},手机号:{},请求:{},参数:{}", LOG_TAG_JXYD_YINLIAN_MEMBER, originId, mobile, callbackUrl, json);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-订单号:{},手机号:{},回调响应:{}", LOG_TAG_JXYD_YINLIAN_MEMBER, originId, mobile, content);
        } catch (Exception e) {
            log.error("{}-订单号:{},手机号:{},回调异常:", LOG_TAG_JXYD_YINLIAN_MEMBER, originId, mobile, e);
        }
    }

    /**
     * 外部渠道中视游专用回调
     *
     * @param subscribe
     * @param offerId
     */
    @Async("outSidecallbackExecutor")
    public void zysCallbackAsync(Subscribe subscribe, String offerId) {
        zysCallback(subscribe, offerId);
    }

    public void zysCallback(Subscribe subscribe, String offerId) {
        FormBody.Builder builder = new FormBody.Builder();
        builder.add("orderNo", subscribe.getId());
        builder.add("status", "1");
        builder.add("msg", Strings.nullToEmpty(subscribe.getResult()));
        builder.add("ispOrderNo", subscribe.getIspOrderNo());
        builder.add("mobile", subscribe.getMobile());
        builder.add("orderTime", DateFormatUtils.format(subscribe.getOpenTime(), DateUtils.FULL_TIME_SPLIT_PATTERN));
        builder.add("offerId", offerId);
        FormBody formBody = builder.build();
        try {
            log.info("{},手机号:{},orderNo:{},status:{},msg:{}", LOG_TAG_ZSY, subscribe.getMobile(), subscribe.getId(), subscribe.getStatus(), subscribe.getResult());
            Request request = new Request.Builder().url(CALLBACK_URL_ZSY).post(formBody).build();
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-订单号:{},手机号:{},回调响应:{}", LOG_TAG_ZSY, subscribe.getId(), subscribe.getMobile(), result);
            } catch (Exception e) {
                log.info("{}-订单号:{},手机号:{},回调异常:", LOG_TAG_ZSY, subscribe.getId(), subscribe.getMobile(), e);
            }
        } catch (Exception e) {
            log.error("外部回调异常", e);
        }
    }

    /**
     * 1小时退订
     *
     * @param subscribe
     */
    @Async("outSidecallbackExecutor")
    public void unsubscribeCallbackAsync(Subscribe subscribe) {
        unsubscribeCallback(subscribe);
    }

    public void unsubscribeCallback(Subscribe subscribe) {
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subscribe.getSubChannel());
        if (outsideConfig == null) {
            return;
        }
        String notifyUrl = outsideConfig.getUnsubNotifyUrl();
        if (StringUtils.isEmpty(notifyUrl)) {
            log.warn("外部渠道1小时退订回调链接为空,渠道号{},手机号:{}", outsideConfig.getOutChannel(), subscribe.getMobile());
            return;
        }
        FormBody.Builder builder = new FormBody.Builder();
        builder.add("orderNo", subscribe.getId());
        builder.add("mobile", subscribe.getMobile());
        //易尊退订回调
        if (notifyUrl.contains("op.ejcop.com")) {
            builder.add("status", "2");
            builder.add("msg", "业务退订");
        } else {
            builder.add("unsubscribeTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        }
        FormBody formBody = builder.build();
        log.info("{}-1小时退订回调-手机号:{},id:{},渠道号:{},子渠道号:{}", LOG_UNSUB_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel());
        Request request = new Request.Builder().url(notifyUrl).post(formBody).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-1小时退订回调-手机号:{},id:{},渠道号:{},子渠道号:{},回调响应:{}", LOG_UNSUB_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel(), result);
        } catch (Exception e) {
            log.info("{}-1小时退订回调-手机号:{},id:{},渠道号:{},子渠道号:{},回调异常:", LOG_UNSUB_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel(), e);
        }
    }

    /**
     * 次日退订
     *
     * @param subscribe
     */
    @Async("outSidecallbackExecutor")
    public void dayUnsubscribeCallbackAsync(Subscribe subscribe) {
        dayUnsubscribeCallback(subscribe);
    }

    public void dayUnsubscribeCallback(Subscribe subscribe) {
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subscribe.getSubChannel());
        if (outsideConfig == null) {
            return;
        }
        String notifyUrl = outsideConfig.getDayUnsubNotifyUrl();
        if (StringUtils.isEmpty(notifyUrl)) {
            log.warn("外部渠道次日退订回调链接为空,渠道号{},手机号:{}", outsideConfig.getOutChannel(), subscribe.getMobile());
            return;
        }
        FormBody.Builder builder = new FormBody.Builder();
        builder.add("orderNo", subscribe.getId());
        builder.add("mobile", subscribe.getMobile());
        //易尊退订回调
        if (notifyUrl.contains("op.ejcop.com")) {
            builder.add("status", "2");
            builder.add("msg", "业务退订");
        } else {
            builder.add("unsubscribeTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        }
        FormBody formBody = builder.build();
        log.info("{}-外部渠道次日退订回调-手机号:{},id:{},渠道号:{},子渠道号:{}", LOG_UNSUB_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel());
        Request request = new Request.Builder().url(notifyUrl).post(formBody).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-外部渠道次日退订回调-手机号:{},id:{},渠道号:{},子渠道号:{},回调响应:{}", LOG_UNSUB_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel(), result);
        } catch (Exception e) {
            log.info("{}-外部渠道次日退订回调-手机号:{},id:{},渠道号:{},子渠道号:{},回调异常:", LOG_UNSUB_TAG, subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), subscribe.getSubChannel(), e);
        }
    }

    /**
     * 老霍呼叫秀专用回调
     *
     * @param id
     */
    @Async("outSidecallbackExecutor")
    public void lhHjxCallbackAsync(String id) {
        lhHjxCallback(id);
    }
    public void lhHjxCallback(String id) {
        Subscribe subscribe = subscribeService.getById(id);
        final HttpUrl httpUrl = HttpUrl.parse(CALLBACK_LH_HJX)
                .newBuilder()
                .addQueryParameter("mobile", subscribe.getMobile())
                .addQueryParameter("getCodeTime", DateFormatUtils.format(subscribe.getCreateTime(), DateUtils.FULL_TIME_SPLIT_PATTERN))
                .addQueryParameter("submitCodeTime", DateFormatUtils.format(subscribe.getBizTime(), DateUtils.FULL_TIME_SPLIT_PATTERN))
                .addQueryParameter("orderTime", DateFormatUtils.format(subscribe.getOpenTime(), DateUtils.FULL_TIME_SPLIT_PATTERN))
                .addQueryParameter("code", subscribe.getExtra())
                .addQueryParameter("msg", subscribe.getResult())
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-mobile:{},请求:{},参数:{}", LOG_TAG_LH_HJX, subscribe.getMobile(), CALLBACK_LH_HJX);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},回调响应:{}", LOG_TAG_LH_HJX, subscribe.getMobile(), result);
        } catch (Exception e) {
            log.error("{}-mobile:{},回调异常:", LOG_TAG_LH_HJX, subscribe.getMobile(), e);
        }
    }

    /**
     * 中财-乐扬5G新通话-明星来电白银组合包C专用回调
     *
     * @param id
     */
    @Async("outSidecallbackExecutor")
    public void zclyCallbackAsync(String id) {
        zclyCallback(id);
    }
    public void zclyCallback(String id) {
        Subscribe subscribe = subscribeService.getById(id);
        final ObjectNode paramNode = mapper.createObjectNode();
        ZhongcaiLeyangSichuanYidongConfig zhongcaiLeyangSichuanYidongConfig = zhongcaiLeyangSichuanYidongProperties.getChannelConfigMap().get(subscribe.getChannel());
        paramNode.put("type", "1")
                .put("phone", ZhongcaileyangService.aesEncryptHex(zhongcaiLeyangSichuanYidongConfig.getAesKey(), zhongcaiLeyangSichuanYidongConfig.getAesIv(), subscribe.getMobile()))
                .put("orderId", subscribe.getIspOrderNo())
                .put("subcribe_time", DateUtils.getMillis(subscribe.getOpenTime()))
                .put("user_ip", subscribe.getIp())
                .put("package_name", subscribe.getReferer())
                .put("product_code", zhongcaiLeyangSichuanYidongConfig.getProductCode())
                .put("channel_platform", zhongcaiLeyangSichuanYidongConfig.getPlatformId())
                .put("province_code", zhongcaiLeyangSichuanYidongConfig.getProvinceCode());
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), paramNode.toString());
        Request request = new Request.Builder().url(zhongcaiLeyangSichuanYidongProperties.getCallbackUrl()).post(body).build();
        log.info("{}-mobile:{},请求:{},参数:{}", LOG_TAG_ZC_LY, subscribe.getMobile(), zhongcaiLeyangSichuanYidongProperties.getCallbackUrl());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},回调响应:{}", LOG_TAG_ZC_LY, subscribe.getMobile(), result);
        } catch (Exception e) {
            log.error("{}-mobile:{},回调异常:", LOG_TAG_ZC_LY, subscribe.getMobile(), e);
        }
    }





    /**
     * 彩讯呼叫秀回调
     *
     * @param id
     */
    @Async("outSidecallbackExecutor")
    public void caixunCallbackAsync(String id) {
        caixunCallback(id);
    }
    public void caixunCallback(String id) {
        Subscribe subscribe = subscribeService.getById(id);
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("mobile", subscribe.getMobile())
                .put("smsTime", DateFormatUtils.format(subscribe.getCreateTime(), DateUtils.FULL_TIME_SPLIT_PATTERN))
                .put("code", subscribe.getExtra())
                .put("orderTime", DateFormatUtils.format(subscribe.getOpenTime(), DateUtils.FULL_TIME_SPLIT_PATTERN))
                .put("orderStatus", "0")
                .put("orderMsg", "成功")
                .put("channelCode", caixunProperties.getChannelCodeMap().get(subscribe.getChannel()))
                .put("channelSource", subscribe.getChannel());
        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(caixunProperties.getCallbackUrl()).post(body).build();
        log.info("{}-mobile:{},请求:{},参数:{}", LOG_TAG_CAIXUN, subscribe.getMobile(), caixunProperties.getCallbackUrl());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},回调响应:{}", LOG_TAG_CAIXUN, subscribe.getMobile(), result);
        } catch (Exception e) {
            log.error("{}-mobile:{},回调异常:", LOG_TAG_CAIXUN, subscribe.getMobile(), e);
        }
    }

    /**
     * 江西萌宠集回调
     * quDoCode：P558199988
     * secret：dkuqin39guwrqymj1epchgwuveeu74y6
     * productCode：jxmcj20
     *
     * @param subscribe
     * @param offerId
     */
    @Async("outSidecallbackExecutor")
    public void mcjCallbackAsync(Subscribe subscribe, String offerId) {
        mcjCallback(subscribe, offerId);
    }
    public void mcjCallback(Subscribe subscribe, String offerId) {
        String quDaoCode = "P558199988";
        String bCode = "jxmcj20";
        String key = "dkuqin39guwrqymj1epchgwuveeu74y6";

        HashMap<String, String> map = new HashMap<>();
        map.put("mobile", subscribe.getMobile());
        map.put("quDaoCode", quDaoCode);
        map.put("bCode", bCode);
        map.put("authCode", subscribe.getExtra());
        map.put("orderDevice", "");
        map.put("optSource", "");
        map.put("orderIp", "");
        map.put("sendCodeTime", "");
        map.put("sign", MCJSignUtils.generateHttpParamSign(map, key));

        try {
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(map));

            log.info("{},手机号:{},orderNo:{},status:{},msg:{}", LOG_TAG_MCJ, subscribe.getMobile(), subscribe.getId(), subscribe.getStatus(), subscribe.getResult());
            Request request = new Request.Builder().url(CALLBACK_URL_MCJ).post(body).build();
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-订单号:{},手机号:{},回调响应:{}", LOG_TAG_MCJ, subscribe.getId(), subscribe.getMobile(), result);
            } catch (Exception e) {
                log.info("{}-订单号:{},手机号:{},回调异常:", LOG_TAG_MCJ, subscribe.getId(), subscribe.getMobile(), e);
            }
        } catch (Exception e) {
            log.error("外部回调异常", e);
        }
    }


    /**
     * 江西萌宠鸿安众图(鸿盛)回调
     * 	产品ID（CID）：4603 萌宠AI联合会员月包
     * 	接口账号：schstj
     * 	接口密钥：88c0882aa23246d5ab9dda5dcfd33a8c
     *
     * @param subscribe
     */
    @Async("outSidecallbackExecutor")
    public void mcAztCallbackAsync(Subscribe subscribe) {
        mcAztCallback(subscribe);
    }
    public void mcAztCallback(Subscribe subscribe) {
        String mobile = subscribe.getMobile();
        String ispOrderNo = subscribe.getIspOrderNo();
        String apiKey = "88c0882aa23246d5ab9dda5dcfd33a8c";
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("apiUser", "schstj");
        paramNode.put("mobile", subscribe.getMobile());
        paramNode.put("cid", "4603");
        paramNode.put("order_sn", subscribe.getIspOrderNo());
        paramNode.put("order_time", DateFormatUtils.format(subscribe.getOpenTime(), DateUtils.FULL_TIME_SPLIT_PATTERN));
        paramNode.put("ip", subscribe.getIp());
        paramNode.put("package", subscribe.getReferer());
        final TreeMap<String, String> treeMap = mapper.convertValue(paramNode, new TypeReference<TreeMap<String, String>>() {
        });
        String queryString = treeMap.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getValue()))
                .map(entry -> String.join("=", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining("&"));
        final String sign = DigestUtils.md5DigestAsHex((queryString + "&apikey=" + apiKey).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        paramNode.put("sign", sign);
        paramNode.put("source", Base64.getEncoder().encodeToString(subscribe.getSource().getBytes(StandardCharsets.UTF_8)));
        paramNode.set("extra", mapper.createObjectNode()
                .put("sms_code", subscribe.getExtra())
        );

        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(CALLBACK_URL_AZT).post(body).build();
        log.info("{}-安众图江西移动订单号:{},手机号:{},请求:{},参数:{}", LOG_TAG_AZT, ispOrderNo, mobile, CALLBACK_URL_AZT, paramNode);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-安众图江西移动订单号:{},手机号:{},回调响应:{}", LOG_TAG_AZT, ispOrderNo, mobile, content);
        } catch (Exception e) {
            log.error("{}-安众图江西移动订单号:{},手机号:{},开通结果:{},回调异常:", LOG_TAG_AZT, ispOrderNo, mobile, e);
        }
    }




    /**
     * 枣米糖四川移动知乎盐选专用回调
     *
     * @param id
     */
    @Async("outSidecallbackExecutor")
    public void zmtCallbackAsync(String id) {
        zmtCallback(id);
    }
    public void zmtCallback(String id) {
        Subscribe subscribe = subscribeService.getById(id);
        String token = SpringUtil.getBean(OutsideCallbackService.class).zmtToken();
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("commodityId", zmtCallbackProperties.getCommodityId())
                .put("mobileType", "四川移动")
                .put("mobileOfferId", sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel()))
                .put("mobileOrderId", subscribe.getIspOrderNo())
                .put("orderTime", DateFormatUtils.format(subscribe.getOpenTime(), DateUtils.FULL_TIME_SPLIT_PATTERN))
                .put("handlePhone", subscribe.getMobile())
                .put("partnerOrderId", subscribe.getId());
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), paramNode.toString());
        Request request = new Request.Builder().url(zmtCallbackProperties.getCallbackUrl()).addHeader("token", token).post(body).build();
        log.info("{}-mobile:{},请求:{},参数:{}", LOG_TAG_ZTM, subscribe.getMobile(), zhongcaiLeyangSichuanYidongProperties.getCallbackUrl());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},回调响应:{}", LOG_TAG_ZTM, subscribe.getMobile(), result);
        } catch (Exception e) {
            log.error("{}-mobile:{},回调异常:", LOG_TAG_ZTM, subscribe.getMobile(), e);
        }
    }


    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_ZMT_TOKEN, key = "#root.methodName", unless = "#result==null")
    public String zmtToken() {
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("userName", zmtCallbackProperties.getUserName());
        paramNode.put("password", MD5.create().digestHex(zmtCallbackProperties.getPassword()));
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), paramNode.toString());
        Request request = new Request.Builder().url(zmtCallbackProperties.getGetTokenUrl()).post(body).build();
        log.info("{}-获取token,请求:{}", LOG_TAG_ZTM, TOKEN_URL_ZMT);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取token:响应:{}", LOG_TAG_ZTM, result);
            JsonNode tokenNode = mapper.readTree(result);
            return HttpStatus.OK.value() == tokenNode.at("/code").asInt() ? tokenNode.at("/data/token").asText() : null;
        } catch (Exception e) {
            log.error("{}-获取token:异常:", LOG_TAG_ZTM, e);
            return null;
        }
    }

    /**
     * 数智人开通成功结果回调
     * @param subscribe
     */
    @Async("outSidecallbackExecutor")
    public void shuzirenCallbackAsync(Subscribe subscribe) {
        shuzirenCallback(subscribe);
    }
    public void shuzirenCallback(Subscribe subscribe) {
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("phone", subscribe.getMobile());
        paramNode.put("serviceId", "698039049108383471");
        paramNode.put("orderCompletionTime", DateUtils.datetimeFormat.get().format(subscribe.getOpenTime()));
        paramNode.put("province", subscribe.getProvince());
        paramNode.put("channelId", shuzirenCallbackProperties.getChannelId());
        paramNode.put("ua", subscribe.getUserAgent());
        paramNode.put("ip", subscribe.getIp());
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), paramNode.toString());
        final String token = SpringUtil.getBean(OutsideCallbackService.class).shuzirenToken();
        Request request = new Request
                .Builder()
                .url(shuzirenCallbackProperties.getOrderUrl())
                .addHeader("Authorization", "Bearer " + token)
                .post(body)
                .build();
        log.info("{}-mobile:{},请求:{}", LOG_TAG_SHUZIREN, subscribe.getMobile(),request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-mobile:{},响应:{}", LOG_TAG_SHUZIREN, subscribe.getMobile(), result);
        } catch (Exception e) {
            log.error("{}-mobile:{},异常:", LOG_TAG_SHUZIREN, subscribe.getMobile(), e);
        }
    }

    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_SZR_TOKEN, key = "#root.methodName", unless = "#result==null")
    public String shuzirenToken() {
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("appid", shuzirenCallbackProperties.getAppid());
        paramNode.put("secret", shuzirenCallbackProperties.getSecret());
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), paramNode.toString());
        Request request = new Request.Builder().url(shuzirenCallbackProperties.getTokenUrl()).post(body).build();
        log.info("{}-获取token,请求:{}", LOG_TAG_SHUZIREN, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取token:响应:{}", LOG_TAG_SHUZIREN, result);
            JsonNode tokenNode = mapper.readTree(result);
            return STATUS_SUCC == tokenNode.at("/code").asInt() ? tokenNode.at("/data/access_token").asText() : null;
        } catch (Exception e) {
            log.error("{}-获取token:异常:", LOG_TAG_SHUZIREN, e);
            return null;
        }
    }






    public static void main(String[] args) {
        System.out.println(DateFormatUtils.format(new Date(), DateUtils.FULL_TIME_SPLIT_PATTERN));
        boolean a = StringUtils.equalsAny("00012", SubscribeResultNotifyService.RESULT_CODE_NEED_RECHECK_ARRAY);
        System.out.println(a);
        boolean b = StringUtils.equalsAny("-8001", SubscribeResultNotifyService.RESULT_CODE_NEED_RECHECK_ARRAY);
        System.out.println(b);

        System.out.println(MD5.create().digestHex("!zmt7381youranjianyin01"));

        boolean equals = HttpStatus.OK.value() == 200;
        System.out.println(equals);


        final ObjectNode paramNode = new ObjectMapper().createObjectNode();
        paramNode.put("apiUser", "schstj");
        paramNode.put("cid", "4603");
        paramNode.put("package", "");
        final TreeMap<String, String> treeMap = new ObjectMapper().convertValue(paramNode, new TypeReference<TreeMap<String, String>>() {
        });
//        String queryString = Joiner.on("&")
//            .withKeyValueSeparator("=")
//            .join(treeMap);

        String queryString = treeMap.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getValue()))
            .map(entry -> String.join("=", entry.getKey(), entry.getValue()))
            .collect(Collectors.joining("&"));

        System.out.println(queryString);
        final String sign = DigestUtils.md5DigestAsHex((queryString + "&apikey=" + "").getBytes(StandardCharsets.UTF_8)).toLowerCase();




    }
    public void jxydYinlianMemberCallbackVs(String mobile, String originId, Integer state, String result, String smsCode, Date openTime, String app, String userAgent, String ip) {
        String callbackUrl = jiangxiYidongProperties.getJxydYinlianMemberVsCallbackUrl();
        final ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("product", "2224")
                .put("status", state)
                .put("msg", result)
                .put("tradeid", originId)
                .put("phone", mobile)
                .put("reqtime", DateFormatUtils.format(openTime, DateUtils.FULL_TIME_SPLIT_PATTERN))
                .put("subtime", DateFormatUtils.format(openTime, DateUtils.FULL_TIME_SPLIT_PATTERN))
                .put("succtime", DateFormatUtils.format(openTime, DateUtils.FULL_TIME_SPLIT_PATTERN))
                .put("vcode", smsCode)
                .put("app", app)
                .put("userAgent", userAgent)
                .put("ip", ip);
        final String json = paramNode.toString();
        RequestBody body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), json);
        Request request = new Request.Builder().url(callbackUrl).post(body).build();
        log.info("{}-订单号:{},手机号:{},请求:{},参数:{}", LOG_TAG_JXYD_YINLIAN_MEMBER, originId, mobile, callbackUrl, json);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-订单号:{},手机号:{},回调响应:{}", LOG_TAG_JXYD_YINLIAN_MEMBER, originId, mobile, content);
        } catch (Exception e) {
            log.error("{}-订单号:{},手机号:{},回调异常:", LOG_TAG_JXYD_YINLIAN_MEMBER, originId, mobile, e);
        }
    }


}

