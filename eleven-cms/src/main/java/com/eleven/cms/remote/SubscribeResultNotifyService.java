package com.eleven.cms.remote;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.OutsideProperties;
import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.config.ThirdPartyChannelConfigProperties;
import com.eleven.cms.dto.YCJXADLDOrderResultNotifyDTO;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.JunboSub;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.TelecomOrder;
import com.eleven.cms.queue.*;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.OrderProductEnum;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.*;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.eleven.qycl.service.IQyclOrderPayLogService;
import com.eleven.qycl.service.IQyclQywxService;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_TAG_SUBSCRIBE_DELAY_10S_VRBT_ORDER;
import static com.eleven.cms.remote.MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_D3;
import static com.eleven.cms.remote.MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_SXJB;
import static com.eleven.cms.util.BizConstant.*;

/**
 * Author: <EMAIL>
 * Date: 2023/5/15 16:07
 * Desc: 业务开通结果处理类
 */
@Service
@Slf4j
public class SubscribeResultNotifyService {

    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private BizProperties bizProperties;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private OutsideProperties outsideProperties;
    @Autowired
    private OutsideCallbackService outsideCallbackService;
    @Autowired
    IOrderVrbtService orderVrbtService;
    @Autowired
    private IPPTVApiService pptvApiService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    XunfeiJingxianVrbtService xunfeiJingxianVrbtService;
    @Autowired
    private SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    AppOpenResultSyncService appOpenResultSyncService;
    @Autowired
    ITelecomOrderService telecomOrderService;
    @Autowired
    IHttpRequestService httpRequestService;
    @Autowired
    private ThirdPartyChannelConfigProperties thirdPartyChannelConfigProperties;
    @Autowired
    BjhyDataService bjhyDataService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    private ChannelOwnerService channelOwnerService;
    @Autowired
    EnterpriseVrbtProperties enterpriseVrbtProperties;
    @Autowired
    SmsNotifyService smsNotifyService;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IMusicHotLevelService musicHotLevelService;
    @Autowired
    IQyclOrderPayLogService qyclOrderPayLogService;
    @Autowired
    private IVrbtAppUserPointsService vrbtAppUserPointsService;
    @Autowired
    private IQyclQywxService qyclQywxService;
    @Autowired
    SiChuanMobileApiService siChuanMobileApiService;

    private final ObjectMapper mapper = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    //-8001:=>延时查询用户已包月,45 -8001:2=>延时查询用户已包月,1 -8001:9=>延时查询用户已包月,2 -8001:5=>延时查询用户已包月,1
    //F4902:业务受理确认=>延时查询用户已包月,1
    //S172002:支付已过期,请刷新重试=>延时查询用户已包月,2
    //F4109:用户状态非法=>延时查询用户已包月,26
    //S111001:支付安全校验失败,请稍后重试=>延时查询用户已包月,73
    //999004:【OPEN】系统繁忙=>延时查询用户已包月,1
    //S171001:支付安全校验失败,请稍后重试=>延时查询用户已包月,1
    //229999:系统繁忙,请稍后尝试.=>延时查询用户已包月,1
    //G1215:答案错误，请重试=>延时查询用户已包月,7
    //I9999:数据库操作异常=>延时查询用户已包月,1
    //223009:系统繁忙，请稍后再试 =>延时查询用户已包月,1
    //0001:业务受理中，请稍后。=>延时查询用户已包月,68                             :
    //000001:
    public static final String[] RESULT_CODE_NEED_RECHECK_ARRAY = {"0001", "000001", "-8001", "F4902", "S172002", "F4109", "S111001", "999004", "S171001", "229999", "G1215", "I9999", "223009"};
    //骏伯流量包 订购成功=7 订购失败=8,6 退订=14
    private static final String JUNBO_LLB_STATUS_SUCCESS = "7";
    private static final String JUNBO_LLB_STATUS_FAIL = "8";
    private static final String JUNBO_CPA_STATUS_FAIL = "6";
    private static final String JUNBO_LLB_STATUS_UNSUB = "14";

    private static final Map<String, String> DEFAULT_VRBT_PRODUCT_IDS = new HashMap<String, String>() {{
        put("0", "600050TA9SCM");
        put("1", "600050TAQGVM");
        put("2", "600050TAQG0M");
        put("3", "600050TA9S9M");
        put("4", "600050TAQFMM");
        put("5", "600050TAQGLM");
        put("6", "600050TAQGPM");
        put("7", "600050TAQG3M");
        put("8", "600050T0180M");
    }};

    private static final List<String> DEFAULT_VRBT_PRODUCT_IDS_LIST = ListUtil.toList(DEFAULT_VRBT_PRODUCT_IDS.values());

    @Autowired
    MessageNotifyService messageNotifyService;
    @Autowired
    AlarmService alarmService;
    @Autowired
    MiGuHuYuService miGuHuYuService;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    IJunboSubService junboSubService;

    /**
     * 破解计费开通结果接收
     *
     * @param mobile   手机号
     * @param transId  计费破解方交易id
     * @param resCode  错误代码：0000表示计费成功
     * @param resMsg   错误描述
     * @param openTime 处理时间
     * @return
     */
    public boolean receiveBillingResult(String mobile, String transId, String resCode, String resMsg, Date openTime, String ydOrderId) {

        //新老pj的开通成功码0000,森越pj开通成功码00000
        Integer status = "0000".equals(resCode) || "00000".equals(resCode) ? 1 : 0;
        String result = "计费开通结果=>" + resCode + ":" + resMsg;
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, transId);
        if (subscribe == null) {
            log.error("计费更新订购结果失败,不存在该id的订购通知记录,mobile:{},transId:{}", mobile, transId);
            return false;
        }
        if((BIZ_TYPE_SCYD_HS.equals(subscribe.getBizType()) && StringUtils.isNotBlank(ydOrderId)) || (BIZ_CHANNEL_SC_HTXXJ_HS.equals(subscribe.getChannel())  && StringUtils.isNotBlank(ydOrderId))){
            subscribe.setIspOrderNo(ydOrderId);
        }
        //四川移动处理
        if (BIZ_TYPE_SCYD.equals(subscribe.getBizType())) {
            receiveSubscribeResult(subscribe.getId(), subscribe.getMobile(), status, result, ydOrderId);
            return true;
        }

        //pj业务告警监控
        alarmService.monitorCrackOrderResult(subscribe.getChannel(), resCode);
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            boolean isPushExistsLastOrder = !redisUtil.setIfAbsent(mobile + subscribe.getChannel(), mobile + subscribe.getChannel(), 600);
            //最近已有处理的订单将跳过通知
            if (isPushExistsLastOrder) {
                log.warn("10分钟内已有成功数据,跳过订单处理,mobile:{},channel:{},transId:{}", mobile, subscribe.getChannel(), transId);
                return false;
            }
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},transId:{}", mobile, transId);
            return false;
        }

        final String channelCode = subscribe.getChannel();
        //咪咕互娱鸿盛渠道 特定渠道号收到破解通知后必须马上调用包月查询接口
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status) && HONGSHENG_CHANNEL_LIST.contains(channelCode)) {
            miGuHuYuService.queryMonthly(mobile, channelCode);
        }


        //彩铃中心视频彩铃业务包月失败结果重新核实
        //计费开通结果=>G1202:短信验证码输入错误，请重新获取
        //计费开通结果=>9028:短信验证码超时未提交
        if (status == 0 && StringUtils.equalsAny(resCode, RESULT_CODE_NEED_RECHECK_ARRAY)) {
            //延迟校验同时支持视频彩铃和视彩号
            if (StringUtils.equalsAny(subscribe.getBizType(), BIZ_TYPE_VRBT, BIZ_TYPE_SCH)) {
                //视频彩铃1,5,10,30分钟包月查询(之前做讯飞的,改为所有视频彩铃都需要做开通结果延迟查询,只是讯飞的回调是单独的)
                rabbitMQMsgSender.sendSubscribeVrbtRecheckDelayMessage(subscribe.getId(), 1);


                if (!BizConstant.isMiguNotifySuccessBizType(subscribe.getBizType())) {
                    rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                }
            }
            //组合包业务包月失败结果重新核实
            if (BizConstant.isBjhyZhbChannel(channelCode)) {
                if (miguApiService.bjhyQuery(mobile, channelCode).isBjhyMember()) {
                    status = 1;
                    result = result + "=>查询用户已包月";
                }
            }
        }

        //判断是否为指定渠道
        final String bizType = subscribe.getBizType();
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status) && !MobileRegionResult.ISP_LIANTONG.equals(subscribe.getIsp())) {
            //发送业务开通成功提醒短信
            CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
            String serviceId = cmsCrackConfig != null ? cmsCrackConfig.getServiceId() : "";
            smsModelService.sendSmsAsync(subscribe.getMobile(), channelCode, serviceId, BUSINESS_TYPE_CRACK);
            //添加包月状态延迟校验消息到队列(企业彩铃收到通知再加入包月校验)
            if (!BizConstant.isMiguNotifySuccessBizType(bizType)) {
                rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            }
            //移动视频彩铃/白金会员畅听版+视频彩铃(组合包) /视宣号/视彩号/渠道包(音乐包)/贵州移动炫视视频彩铃 订购铃音
            if ((BIZ_TYPE_VRBT.equals(bizType) || BizConstant.isBjhyZhbChannel(channelCode) || BIZ_TYPE_SXH.equals(bizType) || BIZ_TYPE_SCH.equals(bizType) || BIZ_TYPE_CPMB.equals(bizType) || XUANSHI_RPOVINCE_CHANNEL_LIST.contains(channelCode))
                    && MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())
                    && !BizConstant.isVrbtGaojiePassthroughChannel(channelCode)) {
                //为开通之后要订视频彩铃的做彩铃开通
                final String channelCopyrightId = SpringContextUtils.getBean(IMusicService.class).getChannelCopyrightId(channelCode, subscribe.getCopyrightId());
                log.info("收到订购开通包月成功通知后订之后的视频彩铃进行延时订购=>订单号:{},渠道号:{},手机号:{}:channelCopyrightId:{}", subscribe.getId(), subscribe.getChannel(), mobile, channelCopyrightId);
                rabbitMQMsgSender.sendSubscribeVrbtOrderDelay10secondMessage(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_10S_VRBT_ORDER).id(subscribe.getId()).msg("延迟10秒视频彩铃订购").extra(channelCopyrightId).build());

            }
            //权益包开通成功的"四川", "广东", "浙江", "重庆"用户如无视频彩铃功能就调用0元开功能,排除00211D2,00211D3
            //if (BIZ_TYPE_CPMB.equals(bizType) && (!StringUtils.equalsAny(channelCode, BIZ_CPMB_20_CHANNEL_CODE_D2, BIZ_CPMB_20_CHANNEL_CODE_D3))
            //        &&  StringUtils.equalsAny(subscribe.getProvince(), VRBT_PASSIVE_PROVINCES) && "1".equals(miguApiService.vrbtStatusQuery(mobile,channelCode).getStatus())) {
            //    miguApiService.provinceBjhyOrder(mobile, true);
            //}

            //破解发送网易云权益
            if (StringUtils.equals(channelCode, BIZ_CPMB_20_CHANNEL_CODE_D3)) {
                if (cmsCrackConfig != null) {
                    IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(BeiAnTangChangMusicRightsServiceImpl.class);
                    businessRightsSubService.webCreateScheduledRecharge(mobile, mobile, cmsCrackConfig.getServiceId(), "wyygs_rights_pack_4", WYY_RIGHTS_ID, BIZ_CPMB_20_CHANNEL_CODE_D3);
                }
            }
            //视彩号需要发送特殊权益
            if (BIZ_TYPE_SCH.equals(bizType)) {
                rabbitMQMsgSender.sendSchSpecialProductSubMsg(mobile, channelCode);
                //视彩号铃音发布
                if(channelCode.startsWith("00211M")){
                    rabbitMQMsgSender.sendSchRingPublishMsg(mobile,channelCode);
                }
                //发送一语成片铃音制作咪咕FTP铃音上传消息
                if (MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP.equals(channelCode)) {
                    rabbitMQMsgSender.sendMiguRingFtpUploadMessage(MiguRingFtpUploadMessage.builder().id(subscribe.getId()).mobile(mobile).tag(channelCode).build());
                }

            }

            //彩铃中心特定视频彩铃渠道号发送pptv权益
            if (CAILING_CHANNEL_LIST.contains(channelCode)) {
                try {
                    pptvApiService.pptvRightsRecharge(subscribe.getMobile(), IdWorker.get32UUID(), serviceId, channelCode);
                } catch (Exception e) {
                    log.error("pptv权益下发异常!", e);
                }
            }
        }
        //如果为电信并且开通不成功再更新状态(人民视讯除外)
        if (MobileRegionResult.ISP_DIANXIN.equals(subscribe.getIsp()) && !BIZ_CHANNEL_LH_HJX.equals(subscribe.getChannel()) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            return true;
        }

        //业务类型为企业彩铃和河图并且开通失败的需要更新状态
        if (!BizConstant.isMiguNotifySuccessBizType(bizType) || (BizConstant.isMiguNotifySuccessBizType(bizType) && SUBSCRIBE_STATUS_FAIL.equals(status))) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(status);
            upd.setResult(result);
            upd.setOpenTime(openTime);
            upd.setModifyTime(new Date());
            if(BIZ_TYPE_SCYD_HS.equals(subscribe.getBizType()) && StringUtils.isNotBlank(ydOrderId)){
                upd.setIspOrderNo(ydOrderId);
            }
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        //特定渠道号充值
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status) ) {
            siChuanMobileApiService.reCharge(subscribe.getIspOrderNo(),subscribe.getChannel(),subscribe.getMobile(), DateUtils.now());
        }
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status) && !BizConstant.isMiguNotifySuccessBizType(bizType)) {
            //写入自增序列
            subscribeService.saveChannelLimit(subscribe);
        }
        //gy渠道号开通成功更新后再发送开通成功事件
        if (MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX.equals(channelCode)&&SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            rabbitMQMsgSender.sendAppSubscribeEventMessage(subscribe);
        }

        //如果是外部渠道 并且是河图和企业彩铃开通失败的加入回调队列
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            if (BizConstant.isMiguNotifySuccessBizType(bizType)) {
                if (SUBSCRIBE_STATUS_FAIL.equals(status)) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            } else {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
        } else {
            if (BizConstant.isMiguNotifySuccessBizType(bizType)) {
                if (SUBSCRIBE_STATUS_FAIL.equals(status)) {
                    channelService.AdEffectFeedbackNew(subscribe, status);
                }
            } else {
                channelService.AdEffectFeedbackNew(subscribe, status);
            }
        }

        // 讯飞视频彩铃回调
        if (miguApiService.isXunfeiChannelCode(channelCode) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            xunfeiJingxianVrbtService.resultNewCallback(subscribe.getMobile(), channelCode, status, resMsg);
            if (BizConstant.BIZ_TYPE_JUNBOLLB.equals(bizType)) {
                //设置铃音
                subscribe.setChannel("014X02H");
                orderVrbtService.orderByRandomXunfeiWithRetry(subscribe);
            }
        }
        //数智人开通成功同步数据
        if (MiguApiService.BIZ_SCH_CHANNEL_CODE_SZR.equals(channelCode) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            outsideCallbackService.shuzirenCallbackAsync(subscribeService.getById(subscribe.getId()));
        }

        //老霍呼叫秀回调
        if (StringUtils.equals(subscribe.getChannel(), BIZ_CHANNEL_LH_HJX) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            outsideCallbackService.lhHjxCallbackAsync(subscribe.getId());
        }

        //彩讯呼叫秀回调
        if (StringUtils.equals(subscribe.getBizType(), BIZ_TYPE_CAIXUN) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            outsideCallbackService.caixunCallbackAsync(subscribe.getId());
        }

        //qiyin视频彩铃回调
        if (QIYIN_CHANNEL_LIST.contains(subscribe.getChannel()) && MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp()) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            outsideCallbackService.qiyinCallback(subscribeService.getById(subscribe.getId()));
        }
        return true;
    }

    /**
     * 渠道订阅回调(前端开通结果回调)
     *
     * @param subscribe
     * @return
     */
    public Result<?> receiveOrderCallback(Subscribe subscribe, Map<String, String> extraMap) {

        if (StringUtils.isBlank(subscribe.getMobile()) || !subscribe.getMobile().matches(Regexp.MOBILE_REG)) {
            return Result.error("无效的手机号");
        }
        if (subscribe.getStatus() == null || !(subscribe.getStatus() == 1 || subscribe.getStatus() == 0)) {
            return Result.error("无效的订购状态");
        }


        //不保存取消支付的{"resCode":"044001","resMsg":"用户取消支付"}
        //if (StringUtils.contains(subscribe.getResult(),"\"044001\"")){
        //    return Result.error("已收到");
        //}
        final String channelCode = subscribe.getChannel();
        String mobile = subscribe.getMobile();
        String subChannel = subscribe.getSubChannel();
        subChannel = StringUtils.isEmpty(subChannel) ? BizConstant.SUB_CHANNEL_DEFAULT : subChannel;
        final String bizType = getBizTypeByMiguChannel(channelCode);
        subscribe.setBizType(bizType);

        if (BIZ_TYPE_READ.equals(bizType)) {
            subscribe.setServiceId(BIZ_MIGU_READ_SERVICE_ID);
        }
        //if (BIZ_TYPE_CPMB.equals(bizType)) {
        //    subscribe.setServiceId(BIZ_TYPE_CPBY_SERVICE_ID);
        //}
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            String isp = mobileRegionResult.getOperator();
            subscribe.setIsp(isp);
        }
        //电信鸿盛-AI视频彩铃智玩包-特殊处理-需要通过 H5 计费订单号查询订单详情
        if (BizConstant.BIZ_TYPE_DX_AIVRBT.equals(bizType) && StringUtils.isNotBlank(subscribe.getIspOrderNo())) {
            DianxinVrbtService dianxinVrbtService = SpringContextUtils.getBean(DianxinVrbtService.class);
            DianxinResp dianxinResp = dianxinVrbtService.queryH5Order(subscribe.getMobile(), subscribe.getIspOrderNo(), subscribe.getChannel());
            if (dianxinResp.isOK()) {
                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
            } else {
                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
            }
        }

        subscribe.setOpenTime(new Date());
        subscribeService.createSubscribeDbAndEs(subscribe);
        //推送宜搜订购状态
        if (MiguApiService.BIZ_BJHYYS_CHANNEL_CODE.equals(channelCode)) {
            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
            pushYisouSubscribeStatus(channelCode, serviceId, mobile, subscribe.getStatus(), subscribe.getIp());
        }
        //权益包开通成功的"四川", "广东", "浙江", "重庆"用户如无视频彩铃功能就调用0元开功能,排除00211D2,00211D3
        //if (BIZ_TYPE_CPMB.equals(bizType) && (!StringUtils.equalsAny(channelCode, BIZ_CPMB_20_CHANNEL_CODE_D2, BIZ_CPMB_20_CHANNEL_CODE_D3))
        //        &&  StringUtils.equalsAny(subscribe.getProvince(), VRBT_PASSIVE_PROVINCES) && "1".equals(miguApiService.vrbtStatusQuery(mobile,channelCode).getStatus())) {
        //    miguApiService.provinceBjhyOrder(mobile, true);
        //}

        //信息流广告转化上报
        channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
        //移动开通成功
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()) && !thirdPartyChannelConfigProperties.isThirdPartyChannel(channelCode)) {
            //发送权益
            if (StringUtils.equals(channelCode, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_KUGOU)) {
                memberService.memberReceiveRights("698039020108689345kg", "kg_rights_pack_1", KG_RIGHTS_ID, mobile, mobile, true, false);
            }
            // 视频彩铃app发送酷狗权益和赠送订购积分
            if (StringUtils.equals(channelCode, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174)) {

                CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
                if (cmsCrackConfig != null) {
                    IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(MiGuOuFenAppRightsServiceImpl.class);
                    businessRightsSubService.createScheduledRecharge(mobile, mobile, cmsCrackConfig.getServiceId(), "vrbt_app_rights_pack_2", KG_RIGHTS_ID, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
                }
                vrbtAppUserPointsService.points(mobile, BizConstant.SUB_POINTS);
            }

            //开通发送网易云权益
            if (StringUtils.equals(channelCode, BIZ_CPMB_20_CHANNEL_CODE_D3)) {
                CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
                if (cmsCrackConfig != null) {
                    IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(BeiAnTangChangMusicRightsServiceImpl.class);
                    businessRightsSubService.webCreateScheduledRecharge(mobile, mobile, cmsCrackConfig.getServiceId(), "wyygs_rights_pack_4", WYY_RIGHTS_ID, BIZ_CPMB_20_CHANNEL_CODE_D3);
                }
            }


            //视彩号特殊权益发放
            if (BIZ_TYPE_SCH.equals(bizType)) {
                rabbitMQMsgSender.sendSchSpecialProductSubMsg(mobile, channelCode);

                if (channelCode.equals(MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX)) {
                    if (StrUtil.isNotBlank(extraMap.get("x-requested-fattr"))
                            && StrUtil.isNotBlank(DEFAULT_VRBT_PRODUCT_IDS.get(extraMap.get("x-requested-fattr")))) {
                        orderVrbtService.vrbtToneFreeOrderDelay(subscribe, DEFAULT_VRBT_PRODUCT_IDS.get(extraMap.get("x-requested-fattr"))
                                , MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                    } else {
                        //随机从配置中随机获取一个
                        orderVrbtService.vrbtToneFreeOrderDelay(subscribe, DEFAULT_VRBT_PRODUCT_IDS_LIST.get(RandomUtil.randomInt(0, DEFAULT_VRBT_PRODUCT_IDS_LIST.size() - 1))
                                , MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);

                    }
                    if (MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX.equals(channelCode)&&(subscribe.getStatus() == 1)) {
                        rabbitMQMsgSender.sendAppSubscribeEventMessage(subscribe);
                    }
                }
            }
            //包月状态校验60分钟和次日异步校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //报备发送短信
            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
            smsModelService.sendSmsAsync(subscribe.getMobile(), channelCode, serviceId, BUSINESS_TYPE_OFFICIAL);
            //
            //            if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHY_CHANNEL_CODE)*/BJHY_CHANNEL_LIST.contains(subscribe.getChannel())) {
            //                datangSmsService.sendCrackBaijinMemberNotice(subscribe.getMobile());
            //
            ////                String serviceId = bizProperties.getMiguChannelConfigMap().get(miguChannel).getServiceId();
            ////                redisDelayedQueueManager.addBjhy(BjhyDelayedMessage.builder().msisdn(subscribe.getMobile()).serviceId(serviceId).channelCode(subscribe.getChannel()).msg("权益领取领取提醒短信").build(), DELAY_NOTICE_TIME, TimeUnit.MINUTES);
            //
            //            }else if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHYYS_CHANNEL_CODE) */YISHOU_CHANNEL_LIST.contains(subscribe.getChannel())) {
            //                datangSmsService.sendCrackBaijinysMemberNotice(subscribe.getMobile());
            //            }else if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHYDY_CHANNEL_CODE)*/DONGYI_CHANNEL_LIST.contains(subscribe.getChannel())) {
            ////                datangSmsService.sendCrackBaijindyMemberNotice(subscribe.getMobile());
            //                String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
            //                smsModelService.sendSms(subscribe.getMobile(),subscribe.getChannel(),serviceId,"3");
            //            }else if (YYQQB_CHANNEL_LIST.contains(subscribe.getChannel())) {
            //                String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
            //                smsModelService.sendSms(subscribe.getMobile(),subscribe.getChannel(),serviceId,"3");
            //            }
            //QY QZ不下发PPTV
            if (StringUtils.equalsAny(channelCode, MiguApiService.BIZ_MIGU_RD_CHANNEL_CODE, MiguApiService.CENTRALITY_CHANNEL_CODE)) {
                String outOrderId = IdWorker.get32UUID();
                //发送pptv权益
                try {
                    pptvApiService.pptvRightsRecharge(subscribe.getMobile(), outOrderId, serviceId, channelCode);
                } catch (Exception e) {
                    log.error("pptv,发送会员权益,异常信息=>phone:{},outOrderId:{},channelCode:{},msg:{}", subscribe.getMobile(), outOrderId, channelCode, e.getMessage(), e);
                }
            }
        }

        //订购成功的移动视频彩铃业务/白金会员畅听版+视频彩铃(组合包)免费订视频彩铃
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())
                && (BIZ_TYPE_VRBT.equals(subscribe.getBizType()) || BizConstant.isBjhyZhbChannel(channelCode))
                && MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())
                && !thirdPartyChannelConfigProperties.isThirdPartyChannel(channelCode)) {

            //为开通之后要订视频彩铃的做彩铃开通
            log.info("收到订购开通包月成功通知后订之后的视频彩铃进行延时订购=>订单号:{},手机号:{}", subscribe.getId(), mobile);
            final String channelCopyrightId = SpringContextUtils.getBean(IMusicService.class).getChannelCopyrightId(channelCode, subscribe.getCopyrightId());
            //如果用户选择了要订购的视频彩铃就直接订购,否则按比例随机订购一首默认彩铃
            if (StringUtils.isNotEmpty(channelCopyrightId)) {
                orderVrbtService.vrbtToneFreeOrderDelay(subscribe, channelCopyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_BEIJIAO);
            } else {
                boolean isOutsideChannel = outsideConfigService.isOutsideChannel(subscribe.getSubChannel());
                //(非外部渠道)没有设置视频彩铃的需要按比例随机订一首默认的视频彩铃
                if (!isOutsideChannel && BIZ_TYPE_VRBT.equals(subscribe.getBizType())) {
                    //如果订单extraMap中有来源属性字段fattr,且fattr对应的值在默认彩铃产品id集合中,就按fattr的值订,否则按比例随机订
                    orderVrbtService.orderByRandomDelay(subscribe);
                }
            }
        }

        return Result.ok("成功");
    }

    /**
     * 渠道订阅回调(前端开通结果回调)
     *
     * @param subscribe
     * @return
     */
    public Result<?> receiveAiVrbtMiniAppSubCallback(Subscribe subscribe, Map<String, String> extraMap) {

        if (StringUtils.isBlank(subscribe.getMobile()) || !subscribe.getMobile().matches(Regexp.MOBILE_REG)) {
            return Result.error("无效的手机号");
        }
        if (subscribe.getStatus() == null || !(subscribe.getStatus() == -1 || subscribe.getStatus() == 1 || subscribe.getStatus() == 0)) {
            return Result.error("无效的订购状态");
        }

        if (StringUtils.isNotBlank(subscribe.getChannel()) && !subscribe.getChannel().equals(MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX)) {
            log.error("无效的渠道号订阅回调:{},{}", subscribe.getChannel(), JacksonUtils.toJson(subscribe));
            return Result.error("无效的渠道号");
        }

        final String channelCode = subscribe.getChannel();
        String mobile = subscribe.getMobile();
        String subChannel = subscribe.getSubChannel();
        subChannel = StringUtils.isEmpty(subChannel) ? BizConstant.SUB_CHANNEL_DEFAULT : subChannel;
        final String bizType = getBizTypeByMiguChannel(channelCode);
        subscribe.setBizType(bizType);

        subscribe.setOpenTime(new Date());
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()) || SUBSCRIBE_STATUS_FAIL.equals(subscribe.getStatus())) {
            if (StrUtil.isNotBlank(subscribe.getId())) {
                subscribeService.updateSubscribeDbAndEs(subscribe);
            } else {
                log.warn("没有subscribeId,不做更新处理:{}", JacksonUtils.toJson(subscribe));
            }
//            if (MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX.equals(channelCode)&&(Objects.equals(subscribe.getStatus(), SUBSCRIBE_STATUS_SUCCESS))) {
//                rabbitMQMsgSender.sendAppSubscribeEventMessage(subscribe);
//            }
        } else if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            subscribe.setId(null);
            subscribeService.createSubscribeDbAndEs(subscribe);
        }


        //信息流广告转化上报
        channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
        //移动开通成功
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()) && !thirdPartyChannelConfigProperties.isThirdPartyChannel(channelCode)) {
            //视彩号特殊权益发放
            if (BIZ_TYPE_SCH.equals(bizType)) {
                rabbitMQMsgSender.sendSchSpecialProductSubMsg(mobile, channelCode);

                if (channelCode.equals(MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX)) {
                    if (StrUtil.isNotBlank(extraMap.get("x-requested-fattr"))
                            && StrUtil.isNotBlank(DEFAULT_VRBT_PRODUCT_IDS.get(extraMap.get("x-requested-fattr")))) {
                        orderVrbtService.vrbtToneFreeOrderDelay(subscribe, DEFAULT_VRBT_PRODUCT_IDS.get(extraMap.get("x-requested-fattr"))
                                , MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                    } else {
                        //随机从配置中随机获取一个
                        orderVrbtService.vrbtToneFreeOrderDelay(subscribe, DEFAULT_VRBT_PRODUCT_IDS_LIST.get(RandomUtil.randomInt(0, DEFAULT_VRBT_PRODUCT_IDS_LIST.size() - 1))
                                , MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                    }
                }
            }
            //包月状态校验60分钟和次日异步校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //报备发送短信
            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
            smsModelService.sendSmsAsync(subscribe.getMobile(), channelCode, serviceId, BUSINESS_TYPE_OFFICIAL);
        }

        return Result.ok(subscribe.getId());
    }
    /**
     * 渠道订阅回调(前端开通结果回调)
     *
     * @param subscribe
     * @return
     */
    public Result<?> receiveAiVrbtAppSubCallback(Subscribe subscribe, Map<String, String> extraMap) {

        if (StringUtils.isBlank(subscribe.getMobile()) || !subscribe.getMobile().matches(Regexp.MOBILE_REG)) {
            return Result.error("无效的手机号");
        }
        if (subscribe.getStatus() == null || !(subscribe.getStatus() == -1 || subscribe.getStatus() == 1 || subscribe.getStatus() == 0)) {
            return Result.error("无效的订购状态");
        }

        if (StringUtils.isNotBlank(subscribe.getChannel()) && !subscribe.getChannel().equals(MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX)) {
            log.error("无效的渠道号订阅回调:{},{}", subscribe.getChannel(), JacksonUtils.toJson(subscribe));
            return Result.error("无效的渠道号");
        }

        final String channelCode = subscribe.getChannel();
        String mobile = subscribe.getMobile();
//        String subChannel = StringUtils.isEmpty(subscribe.getSubChannel()) ? BizConstant.SUB_CHANNEL_DEFAULT : subscribe.getSubChannel();
        final String bizType = getBizTypeByMiguChannel(channelCode);
        subscribe.setBizType(bizType);

        subscribe.setOpenTime(new Date());
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()) || SUBSCRIBE_STATUS_FAIL.equals(subscribe.getStatus())) {
            if (StrUtil.isNotBlank(subscribe.getId())) {
                subscribeService.updateSubscribeDbAndEs(subscribe);
            } else {
                log.warn("没有subscribeId,不做更新处理:{}", JacksonUtils.toJson(subscribe));
            }
            if (MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX.equals(channelCode)&&(Objects.equals(subscribe.getStatus(), SUBSCRIBE_STATUS_SUCCESS))) {
                rabbitMQMsgSender.sendAppSubscribeEventMessage(subscribe);
            }
        } else if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            subscribe.setId(null);
            subscribeService.createSubscribeDbAndEs(subscribe);
        }


        //信息流广告转化上报
        channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
        //移动开通成功
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()) && !thirdPartyChannelConfigProperties.isThirdPartyChannel(channelCode)) {
            //视彩号特殊权益发放
            if (BIZ_TYPE_SCH.equals(bizType)) {
                rabbitMQMsgSender.sendSchSpecialProductSubMsg(mobile, channelCode);

                if (channelCode.equals(MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX)) {
                    if (StrUtil.isNotBlank(extraMap.get("x-requested-fattr"))
                            && StrUtil.isNotBlank(DEFAULT_VRBT_PRODUCT_IDS.get(extraMap.get("x-requested-fattr")))) {
                        orderVrbtService.vrbtToneFreeOrderDelay(subscribe, DEFAULT_VRBT_PRODUCT_IDS.get(extraMap.get("x-requested-fattr"))
                                , MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                    } else {
                        //随机从配置中随机获取一个
                        orderVrbtService.vrbtToneFreeOrderDelay(subscribe, DEFAULT_VRBT_PRODUCT_IDS_LIST.get(RandomUtil.randomInt(0, DEFAULT_VRBT_PRODUCT_IDS_LIST.size() - 1))
                                , MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                    }
                }
            }
            //包月状态校验60分钟和次日异步校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //报备发送短信
//            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
//            smsModelService.sendSmsAsync(subscribe.getMobile(), channelCode, serviceId, BUSINESS_TYPE_OFFICIAL);
            // TODO 暂时不发短信 smsmodel需要改造
        }

        return Result.ok(subscribe.getId());
    }
    /**
     * 渠道订阅回调(前端开通结果回调)
     *
     * @param subscribe
     * @return
     */
    public Result<?> receiveDouyinVrbtCallback(Subscribe subscribe) {

        if (StringUtils.isBlank(subscribe.getMobile()) || !subscribe.getMobile().matches(Regexp.MOBILE_REG)) {
            return Result.error("无效的手机号");
        }
        if (subscribe.getStatus() == null || !(subscribe.getStatus() == 1 || subscribe.getStatus() == 0)) {
            return Result.error("无效的订购状态");
        }
        final String channelCode = subscribe.getChannel();
        String mobile = subscribe.getMobile();
        String subChannel = subscribe.getSubChannel();
        subChannel = StringUtils.isEmpty(subChannel) ? BizConstant.SUB_CHANNEL_DEFAULT : subChannel;
        final String bizType = getBizTypeByMiguChannel(channelCode);
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            String isp = mobileRegionResult.getOperator();
            subscribe.setIsp(isp);
        }
        subscribe.setOpenTime(new Date());
        subscribeService.createSubscribeDbAndEs(subscribe);
        //移动开通成功
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            //包月状态校验60分钟和次日异步校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //为开通之后要订视频彩铃的做彩铃开通
            log.info("收到订购开通包月成功通知后订之后的视频彩铃进行延时订购=>订单号:{},手机号:{}", subscribe.getId(), mobile);
            final String channelCopyrightId = SpringContextUtils.getBean(IMusicService.class).getChannelCopyrightId(channelCode, subscribe.getCopyrightId());
            //如果用户选择了要订购的视频彩铃就直接订购,否则按比例随机订购一首默认彩铃
            if (StringUtils.isNotEmpty(channelCopyrightId)) {
//                orderVrbtService.vrbtToneFreeOrderDelay(subscribe, channelCopyrightId, "1");
                musicHotLevelService.setVrbtMusic(mobile, channelCode, channelCopyrightId);
            }

        }
        return Result.ok("成功");
    }

    /**
     * 处理消息队列的视频彩铃订购结果重新查询 (支持视频彩铃,组合包,视彩号业务)
     *
     * @param delayedVrbtRecheckMessage
     */
    public void handleMqRecheck(DelayedVrbtRecheckMessage delayedVrbtRecheckMessage) {
        final Subscribe subscribe = subscribeService.getById(delayedVrbtRecheckMessage.getId());
        final String channelCode = subscribe.getChannel();
        //判断该手机号在后续1个小时内有无成功开通记录,如果有成功的,本条就应保持开通失败
        boolean hasSuccSubAfter = subscribeService.count(
                Wrappers.<Subscribe>lambdaQuery()
                        .eq(Subscribe::getMobile, subscribe.getMobile())
                        .between(Subscribe::getCreateTime, subscribe.getCreateTime(), org.apache.commons.lang3.time.DateUtils.addHours(subscribe.getCreateTime(), 1))
                        .eq(Subscribe::getChannel, channelCode)
                        .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
        ) > 0;
        if (hasSuccSubAfter) {
            return;
        }
        //boolean hasMonth = false;
        ////同时支持组合包的异步查询
        //if(BizConstant.isBjhyZhbChannel(channelCode)) {
        //    hasMonth = miguApiService.bjhyQuery(subscribe.getMobile(), channelCode).isBjhyMember();
        //}else {
        //    hasMonth = miguApiService.vrbtMonthStatusQuery(subscribe.getMobile(), channelCode,true).isVrbtMember();
        //}
        //boolean hasMonth = miguApiService.vrbtMonthStatusQuery(subscribe.getMobile(), channelCode,true).isVrbtMember();

        //现在支持多种业务的延迟查询(会同时做铃音订购,需注意)
        boolean hasMonth = SUBSCRIBE_MONTH_VERIFY_EXISTS.equals(subscribeVerifyService.monthVerify(subscribe));
        //如果本次查询没有包月,放入下一个延时队列
        if (!hasMonth) {
            rabbitMQMsgSender.sendSubscribeVrbtRecheckDelayMessage(delayedVrbtRecheckMessage.getId(), delayedVrbtRecheckMessage.getTimesCounter() + 1);
            return;
        }
        //如果发现已包月,就更新状态,然后回调,并设置铃音
        int status = BizConstant.SUBSCRIBE_STATUS_SUCCESS;
        String resMsg = subscribe.getResult() + "=>延时查询用户已包月";
        subscribe.setStatus(status);
        subscribe.setResult(resMsg);
        //更新状态
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult(resMsg);
        upd.setModifyTime(new Date());
        subscribeService.updateSubscribeDbAndEs(upd);

        //写入限量自增序列
        subscribeService.saveChannelLimit(subscribe);
        //信息流广告转化上报
        if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            channelService.AdEffectFeedbackNew(subscribe, status);
        } else {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }

        // 讯飞视频彩铃回调并设置铃音
        if (miguApiService.isXunfeiChannelCode(channelCode)) {
            //回调讯飞
            xunfeiJingxianVrbtService.resultNewCallback(subscribe.getMobile(), channelCode, status, resMsg);
            //设置铃音
            orderVrbtService.orderByRandomXunfeiWithRetry(subscribe);
        } else {
            //我方视频彩铃铃音设置
            orderVrbtService.orderByRandomWithRetry(subscribe);
        }

    }


    /**
     * 开通结果接收
     *
     * @param id
     * @param mobile
     * @param status
     * @param result
     * @return
     */
    public boolean receiveSubscribeResult(String id, String mobile, Integer status, String result, String extra) {
        Subscribe subscribe = subscribeService.getById(id);
        if (subscribe == null) {
            log.error("更新订购结果失败,不存在该id的订购通知记录,id:{}", id);
            return false;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},transId:{}", mobile, extra);
            return false;
        }
        Date now = new Date();
        //如果出现网络异常，手动查询订单状态
        if (StringUtils.contains(result, "http") || StringUtils.contains(result, "网络") || StringUtils.contains(result, "X002")) {
//            SichuanMobileQueryOrderResult sichuanMobileQueryOrderResult= siChuanMobileApiService.queryOrder(extra,subscribe.getChannel(),subscribe.getMobile());
//            if (sichuanMobileQueryOrderResult.isOK() && sichuanMobileQueryOrderResult.getResult()!=null && sichuanMobileQueryOrderResult.getResult().isOK()) {
//                result = "开通成功";
//                status = SUBSCRIBE_STATUS_SUCCESS;
//            } else {
//                result = "开通失败";
//                status = SUBSCRIBE_STATUS_FAIL;
//            }
            //四川移动特殊状态延迟查询订单
            ObjectNode jsonNode = mapper.createObjectNode();
            jsonNode.put("id",id);
            jsonNode.put("mobile", mobile);
            jsonNode.put("status", status);
            jsonNode.put("result", result);
            jsonNode.put("extra", extra);
            rabbitMQMsgSender.sichuanMobileMonthlyVerifyDeductMessage(jsonNode);
            return true;
        }
        Subscribe upd = new Subscribe();
        upd.setId(id);
        upd.setStatus(status);
        upd.setResult(result);
        upd.setOpenTime(now);
        upd.setModifyTime(now);
        upd.setIspOrderNo(extra);
        subscribeService.updateSubscribeDbAndEs(upd);

        if (SUBSCRIBE_STATUS_SUCCESS.equals(status) ) {
            //特定渠道号充值
            siChuanMobileApiService.reCharge(subscribe.getIspOrderNo(),subscribe.getChannel(),subscribe.getMobile(), DateUtils.now());
            //加入1天3天校验队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //成功计数
            subscribeService.saveChannelLimit(subscribe);
        }
        //信息流广告转化上报
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribe, status);
        }
//        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//            if (BizConstant.isMiguNotifySuccessBizType(subscribe.getBizType())) {
//                if (SUBSCRIBE_STATUS_FAIL.equals(status)) {
//                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
//                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
//                }
//            } else {
//                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
//                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
//            }
//        } else {
//            if (BizConstant.isMiguNotifySuccessBizType(subscribe.getBizType())) {
//                if (SUBSCRIBE_STATUS_FAIL.equals(status)) {
//                    channelService.AdEffectFeedbackNew(subscribe, status);
//                }
//            } else {
//                channelService.AdEffectFeedbackNew(subscribe, status);
//            }
//        }
        //四川移动数据回调
        if (BizConstant.BIZ_SCMCC_CHANNEL_LBTX.equals(subscribe.getServiceId())) {
            try {
                log.info("四川移动联保天下订单，手机号:{},订单号:{},开通状态:{},开通结果:{}", mobile, extra, status, result);
                outsideCallbackService.lbtxScydCallbackAsync(subscribe.getMobile(), subscribe.getChannel(), subscribe.getId(), extra, status, DateUtils.datetimeFormat.get().format(subscribe.getCreateTime()), result);
            } catch (JsonProcessingException e) {
                log.error("四川移动联保天下订单回调异常,手机号:{}", mobile, e);
            }
        } else if (BIZ_SCMCC_CHANNEL_JUNBO.equals(subscribe.getServiceId())) {
            subscribe = subscribeService.getById(id);
            log.info("四川移动骏伯订单，手机号:{},订单号:{},开通状态:{},开通结果:{}", mobile, extra, status, result);
            outsideCallbackService.junboCallbackAsync(subscribe, sichuanMobileFlowPacketProperties.getJunboCallbackUrl());
            if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_SCYD_TLVRBTLXB_PLUS, BIZ_CHANNEL_SCYD_TLVRBTLXB)) {
                try {
                    outsideCallbackService.lbtxScydTlvrbtlxCallbackAsync(subscribe.getMobile(), subscribe.getChannel(), subscribe.getId(), extra, status, DateUtils.datetimeFormat.get().format(subscribe.getCreateTime()), result);
                } catch (JsonProcessingException e) {
                    log.error("四川移动联保天下订单回调异常(同旅视频彩铃乐享包专用),手机号:{}", mobile, e);
                }
            }
        } else if (BIZ_SCMCC_CHANNEL_YRJY.equals(subscribe.getServiceId())) {
            //log.info("四川移动悠然订单，手机号:{},订单号:{},开通状态:{},开通结果:{}", mobile, extra, status, result);
            if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_SCYD_TLVRBTLXB_PLUS, BIZ_CHANNEL_SCYD_TLVRBTLXB, BIZ_CHANNEL_SCYD_XYLCW)) {
                try {
                    outsideCallbackService.lbtxScydTlvrbtlxCallbackAsync(subscribe.getMobile(), subscribe.getChannel(), subscribe.getId(), extra, status, DateUtils.datetimeFormat.get().format(subscribe.getCreateTime()), result);
                } catch (JsonProcessingException e) {
                    log.error("四川移动联保天下订单回调异常(同旅视频彩铃乐享包专用),手机号:{}", mobile, e);
                }
            }
        }
        //四川移动(斯特普)麦当劳专用回调(移麦相承)
        if (BIZ_CHANNEL_SCYD_YMXC.equals(subscribe.getChannel()) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            try {
                outsideCallbackService.stpCallbackAsync(mobile, extra, now, subscribe.getReferer(), result);
            } catch (Exception e) {
                log.error("四川移动(斯特普)麦当劳专用回调异常,手机号:{}", mobile, e);
            }
        }
        //四川移动(万达)观影权益包专用回调

        if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_SCYD_GYQY, BIZ_CHANNEL_SCYD_WDYYB) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            try {
                outsideCallbackService.wandaCallbackAsync(mobile, extra, now, subscribe.getChannel());
            } catch (Exception e) {
                log.error("四川移动(万达)观影权益包专用回调异常,手机号:{}", mobile, e);
            }
        }

        //四川移动(生日权益)快马回调
        if (BIZ_CHANNEL_SCYD_SRQY.equals(subscribe.getChannel())) {
            try {
                outsideCallbackService.kuaimaSrqyCallbackAsync(mobile, extra, subscribe.getChannel(), result, new Date(), status);
            } catch (Exception e) {
                log.error("四川移动(快马)生日权益包专用回调异常,手机号:{}", mobile, e);
            }
        }

        //四川移动(中财乐扬)5G新通话-明星来电白银组合包C
        if (ZYCL_CHANNEL_LIST.contains(subscribe.getChannel()) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            try {
                outsideCallbackService.zclyCallbackAsync(subscribe.getId());
            } catch (Exception e) {
                log.error("四川移动(中财乐扬)5G新通话-明星来电白银组合包C专用回调异常,手机号:{}", mobile, e);
            }
        }

        //四川移动(枣米糖)知乎盐选会员回调
        if ("SCYD_ZHYX".equals(subscribe.getChannel()) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            try {
                outsideCallbackService.zmtCallbackAsync(subscribe.getId());
            } catch (Exception e) {
                log.error("四川移动(枣米糖)知乎盐选会员回调异常,手机号:{}", mobile, e);
            }
        }

        //四川移动中石化加油包回调到中视游(只回调成功)
        if (BIZ_CHANNEL_SCYD_ZSHJYB.equals(subscribe.getChannel()) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            final String bizCode = sichuanMobileFlowPacketProperties.getChannelBizCodeMap().get(subscribe.getChannel());
            outsideCallbackService.zysCallbackAsync(subscribeService.getById(id), bizCode);
        }

        //权益商城充值
        if (BIZ_DO_CHANNEL_RECHARGE.equals(subscribe.getChannel()) && SUBSCRIBE_STATUS_SUCCESS.equals(status)) {

        }


        final String subChannel = subscribe.getSubChannel();
        //视频彩铃小程序渠道号开通结果同步
        if ("WXCX".equals(subChannel)) {
            appOpenResultSyncService.appOpenResultCallback(mobile, status, result, extra);
        }

//        String msg = result;
//        if (SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
//            msg = "订购成功";
//        }
//        addCallbackNotifyMessage(subscribe, msg);
        return true;
    }

    /**
     * 第三方渠道开通回调
     *
     * @param subscribe
     * @return
     */
    public Result<?> receiveOutsideNotify(Subscribe subscribe) {
        if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            return Result.error("无效的外部渠道号");
        }
        String bizType = getBizTypeByMiguChannel(subscribe.getChannel());
        subscribe.setBizType(bizType);
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            String isp = mobileRegionResult.getOperator();
            subscribe.setIsp(isp);
        }
        subscribe.setOpenTime(new Date());
        subscribeService.createSubscribeDbAndEs(subscribe);
        Result result = Result.ok("成功接收订单");
        if (BIZ_TYPE_BJHY.equals(subscribe.getBizType())) {
            result.setResult(outsideProperties.getBjhyJunboRightsUrl() + subscribe.getMobile());
        } else if (BIZ_TYPE_CPMB.equals(subscribe.getBizType())) {
            result.setResult(outsideProperties.getCpmb20RightsUrl() + subscribe.getMobile());
        }
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            //包月状态校验60分钟和次日异步校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);

            //破解发送短信
            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel()).getServiceId();
            smsModelService.sendSmsAsync(subscribe.getMobile(), subscribe.getChannel(), serviceId, BUSINESS_TYPE_CRACK);
            //            if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHYDY_CHANNEL_CODE,"00210U3","00210U4")*/DONGYI_CHANNEL_LIST.contains(subscribe.getChannel())) {
            ////                datangSmsService.sendCrackBaijindyMemberNotice(subscribe.getMobile());
            //                String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
            //                smsModelService.sendSms(subscribe.getMobile(),subscribe.getChannel(),serviceId,"2");
            //            }
        }

        return result;
    }

    /**
     * 业务开通结果判定消息处理
     * 判定业务开通结果入库到开通结果字段并上报广告平台
     *
     * @param delayedMessage
     */
    public void resultJudgeMsgHandle(DelayedMessage delayedMessage) {
        final String subscribeId = delayedMessage.getId();
        final Subscribe subscribe = subscribeService.getById(subscribeId);
        final Integer monthStatus = subscribeVerifyService.monthVerify(subscribe);
        Subscribe sub = new Subscribe();
        sub.setId(subscribeId);
        sub.setStatus(monthStatus);
        sub.setResult(monthStatus == SUBSCRIBE_MONTH_VERIFY_EXISTS ? "开通成功" : "开通失败");
        //sub.setOpenTime(new Date());
        subscribeService.updateSubscribeDbAndEs(sub);
        //上报广告平台
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            if (!(BIZ_TYPE_HNYZ_MGYD_DJJP.equals(subscribe.getChannel()) || BIZ_TYPE_HENYD_YR_DJB.equals(subscribe.getChannel()) || BIZ_TYPE_HENYD_YR_5GXTH.equals(subscribe.getChannel()))) {
                channelService.AdEffectFeedbackNew(subscribe, monthStatus);
            }
        }

        //如果开通成功,需要加入延迟校验队列
        if (SUBSCRIBE_STATUS_SUCCESS.equals(monthStatus)) {
            //包月状态校验60分钟和次日异步校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            subscribeService.saveChannelLimit(subscribe);
            if (ZYCL_CHANNEL_LIST.contains(subscribe.getChannel())) {
                outsideCallbackService.zclyCallbackAsync(subscribe.getId());
            }
            if (BIZ_TYPE_HENYD_YR_HDDY.equals(subscribe.getChannel())) {
                outsideCallbackService.henanWandaCallbackAsync(subscribe.getMobile(), subscribe.getIspOrderNo(), subscribe.getOpenTime(), subscribe.getChannel());
            }
        }
    }

    /**
     * 电信爱音乐ISMP订购退订消息回调处理,上报广告信息流渠道及更新状态
     *
     * @param mobile 用户手机号
     * @param state  状态 0 订购 1 退订(短代点播产品 0 成功 1 失败) 2 下单成功（未计费)
     * @param time   操作时间
     * @return
     */
    public void receiveDianxinIsmpNotify(String mobile, String state, Date time) {
        //只处理订购成功通知

        Subscribe subscribe = subscribeService.dianxinFindByMobile(mobile);


        JunboSub junboSub = null;
        try {
            junboSub = mapper.treeToValue(mapper.valueToTree(subscribe), JunboSub.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if (!DIANXIN_ISMP_STATE_SUBSCRIBE.equals(state)) {
            log.info("电信爱音乐ISMP订购退订消息回调-退订,手机号:{}", mobile);
            junboSub.setId(null);
            junboSub.setPrice(0);
            junboSubService.save(junboSub);
            return;
        }
        Integer status = SUBSCRIBE_STATUS_SUCCESS;
        if (subscribe == null) {
            log.error("电信爱音乐ISMP订购退订消息回调-更新订购结果失败,不存在该手机号的订购通知记录,手机号:{}", mobile);
            return;
        }
        Date now = new Date();
        //所属公司
        final String company = subscribe.getServiceId();
        orderVrbtService.orderByRandomDianxin(subscribe.getId(), mobile, company);

        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult("电信爱音乐ISMP订购成功消息回调");
//        upd.setTuiaFeedbackStatus(subscribe.getTuiaFeedbackStatus());
        upd.setOpenTime(time);
        upd.setModifyTime(now);
        subscribeService.updateSubscribeDbAndEs(upd);
        junboSub.setId(null);
        junboSub.setStatus(status);
        junboSubService.save(junboSub);
        //信息流广告转化上报
        if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            channelService.AdEffectFeedbackNew(subscribe, status);
        } else {
            //加入延迟队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }
        //pp会员发送
//        pptvApiService.pptvRightsRecharge(subscribe.getMobile(), IdWorker.get32UUID(), OrderProductEnum.GUANGMINGWANG.getServiceId(), OrderProductEnum.GUANGMINGWANG.getChannelCode());
    }

    /**
     * 联通订购回调通知处理,上报广告信息流渠道及更新状态
     *
     * @return
     */
    public void receiveLiantongOrderNotify(String jsonData, String company) throws JsonProcessingException {
        final LiantongNotice liantongNotice = this.mapper.readValue(jsonData, LiantongNotice.class);
        //只处理订购通知
        if (!LiantongNotice.NOTICE_TYPE_ORDER.equals(liantongNotice.getNoticeType())) {
            log.error("未知类型的联通回调通知,订单号:{}", liantongNotice.getNotice().getOrderId());
            return;
        }
        String orderId = liantongNotice.getNotice().getOrderId();
        String returnCode = liantongNotice.getNotice().getReturnCode();
        String orderRingResult = "";
        //判断订购并设置铃音成功的
        //处理单独开通包月的notify
        LiantongNotice.Notice.Notify notify = null;
        if (orderId != null) {
            if (!LiantongResp.isCodeOK(returnCode)) {
                //订购包月非成功回调,跳过
                log.warn("联通沃音乐订购异步通知-订购包月并设置铃音回调-非成功跳过,jsonData:{}", jsonData);
                return;
            } else {
                orderRingResult = liantongNotice.getNotice().getDescription();
            }
        } else {
            notify = liantongNotice.getNotice().getNotify();
            if (notify == null) {
                throw new JeecgBootException("未知结构的回调通知");
            }
            orderId = notify.getId();
            if (!LiantongResp.isCodeOK(notify.getReturnCode())) {
                //订购包月非成功回调,跳过
                log.warn("联通沃音乐订购异步通知-订购包月回调-非成功跳过,jsonData:{}", jsonData);
                return;
            }
        }
        if (StringUtils.isEmpty(orderId)) {
            //订购包月回调未找到订单id,跳过
            log.warn("联通沃音乐订购异步通知-未找到订单id跳过,jsonData:{}", jsonData);
            return;
        }
        if (notify == null) {
            throw new JeecgBootException("未知结构的回调通知");
        }
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(notify.getCallNumber(), orderId);
        if (subscribe == null) {
            log.error("联通沃音乐订购异步通知-更新订购结果失败,不存在该ISP订单号的订购通知记录,订单号:{}", orderId);
            return;
        }
        try {
            //跳过重复收到的通知
            if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
                log.warn("联通沃音乐订购异步通知-跳过重复通知");
                return;
            }
            subscribe.setVrbtLtChannel(company);
            //发送联通给定的中音短信
            LiantongVrbtService liantongVrbtService = SpringContextUtils.getBean(LiantongVrbtService.class);
            //瑞金 睿梅 不下发短信
            if (!BIZ_LT_CHANNEL_RUIJIN.equals(subscribe.getVrbtLtChannel()) || !BIZ_LT_CHANNEL_RUIMEI.equals(subscribe.getVrbtLtChannel())) {
                liantongVrbtService.sendMsg(subscribe.getMobile(), subscribe.getVrbtLtChannel());
            }
            //订购铃音
            final String ltRingId = subscribe.getLtRingId();
            if (StringUtils.isNoneEmpty(ltRingId)) {
                final LiantongResp liantongResp = liantongVrbtService.settingRingOnePointMon(subscribe.getMobile(), ltRingId, subscribe.getVrbtLtChannel());
                orderRingResult = liantongResp.getDescription();
            }

            Integer status = SUBSCRIBE_STATUS_SUCCESS;
            Date now = new Date();

            //更新结果
            //            this.lambdaUpdate().eq(Subscribe::getId,subscribe.getId())
            //                .set(Subscribe::getStatus, status)
            //                //.set(Subscribe::getVerifyStatus, verifyStatus)
            //                .set(Subscribe::getResult, "联通沃音订购异步通知包月成功")
            //                .set(Subscribe::getExtra,orderRingResult)
            //                .set(Subscribe::getOpenTime,now)
            //                .set(Subscribe::getModifyTime,now)
            //                .update();

            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(status);
            upd.setResult("联通沃音订购异步通知包月成功");
            upd.setExtra(orderRingResult);
            upd.setOpenTime(now);
            upd.setModifyTime(now);
            subscribeService.updateSubscribeDbAndEs(upd);

            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, status);

            //注意,因为这里和异步订购铃声都更新了extra,导致冲突,所以放到后面
            //随机订一首默认的视频彩铃
            orderVrbtService.orderByRandomLiantong(subscribe);
            //为开通之后要订视频彩铃的做彩铃开通
            //final ISubscribeService currentProxy = (ISubscribeService) AopContext.currentProxy();
            //currentProxy.orderOtherVrbtDelay(subscribe,channelCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 电信天翼回调
     *
     * @param ispOrderNo
     * @param mobile
     * @param resCode
     * @param resMsg
     * @param type
     * @param processTime
     * @param telecomOrder
     * @return
     * @throws Exception
     */
    public boolean receiveTianyiResult(String ispOrderNo, String mobile, String resCode, String resMsg, String type, Date processTime, TelecomOrder telecomOrder) throws Exception {

        if (StringUtils.isEmpty(type)) {
            throw new Exception("无效的订购类型");
        }
        Integer status = "200".equals(resCode) ? 1 : 0;
        String result = "电信回调结果=>" + resCode + ":" + resMsg;
        //0：退订；1：订购； 2：续订
        telecomOrder.setStatus(Integer.parseInt(type));
        telecomOrderService.save(telecomOrder);
        if ("0".equals(type)) { //退订
            telecomOrderService.lambdaUpdate().eq(TelecomOrder::getPhone, mobile).set(TelecomOrder::getStatus, Integer.parseInt(type)).update();
            return true;
        } else if ("2".equals(type)) { //续订
            return true;
        }
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, ispOrderNo);
        if (subscribe == null) {
            log.warn("计费更新订购结果失败,不存在的订购通知记录,ispOrderNo:{},mobile:{}", ispOrderNo, mobile);
            //throw new Exception("不存在的订购通知记录");
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(status);
            upd.setResult(result);
            upd.setIspOrderNo(ispOrderNo);
            upd.setOpenTime(processTime);
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        //信息流广告转化上报
        //        channelService.AdEffectFeedback(subscribe, status);
        //        if (BizConstant.SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
        //            //延迟队列
        //            addDelayedVerifyMessage    (subscribe);
        //        }
        if (status == 1) {
            pptvApiService.pptvRightsRecharge(mobile, telecomOrder.getOrderid(), BizConstant.BIZ_TIANYI_SERVICE_ID, OrderProductEnum.TIANYI.getChannelCode());
        }
        return true;
    }

    /**
     * wujiong 破界开通结果接收
     *
     * @param orderid
     * @param tel
     * @param time
     */
    public void receiveWujiongNotify(String orderid, String tel, String time) {
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(tel, orderid);
        if (subscribe == null) {
            throw new JeecgBootException("无效的订单号");
        }
        subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
        subscribe.setResult("计费开通成功");
        if (StringUtils.isNotBlank(time) && time.length() > 14) {
            time = time.substring(0, 14);
            try {
                subscribe.setOpenTime(new Date(Long.parseLong(time)));
            } catch (Exception e) {
                log.warn("回调日期转换错误");
            }
        }
        subscribeService.updateSubscribeDbAndEs(subscribe);
        //信息流广告转化上报
        channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());

        //破解发送短信
        String serviceId = cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel()).getServiceId();
        smsModelService.sendSmsAsync(subscribe.getMobile(), subscribe.getChannel(), serviceId, BUSINESS_TYPE_CRACK);

        //        if (/*BJHY_CHANNEL_CODE_PP.equals(subscribe.getChannel()) && MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())*/BJHY_CHANNEL_LIST.contains(subscribe.getChannel())) {
        //            //白金会员破解开通成功发送短信
        //            datangSmsService.sendCrackBaijinMemberNotice(subscribe.getMobile());
        //
        ////            String serviceId = bizProperties.getMiguChannelConfigMap().get(miguChannel).getServiceId();
        ////            redisDelayedQueueManager.addBjhy(BjhyDelayedMessage.builder().msisdn(subscribe.getMobile()).serviceId(serviceId).channelCode(subscribe.getChannel()).msg("权益领取领取提醒短信").build(), DELAY_NOTICE_TIME, TimeUnit.MINUTES);
        //        } else if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHYYS_CHANNEL_CODE,"00210TY","00210TZ")*/YISHOU_CHANNEL_LIST.contains(subscribe.getChannel())) {
        //            datangSmsService.sendCrackBaijinysMemberNotice(subscribe.getMobile());
        //        } else if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHYDY_CHANNEL_CODE,"00210U3","00210U4")*/DONGYI_CHANNEL_LIST.contains(subscribe.getChannel())) {
        ////            datangSmsService.sendCrackBaijindyMemberNotice(subscribe.getMobile());
        //            String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
        //            smsModelService.sendSms(subscribe.getMobile(),subscribe.getChannel(),serviceId,"2");
        //        }else if (YYQQB_CHANNEL_LIST.contains(subscribe.getChannel())) {
        //            String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
        //            smsModelService.sendSms(subscribe.getMobile(),subscribe.getChannel(),serviceId,"2");
        //        }
        //调用接口保存白金会员数据到235
        if (BJHY_CHANNEL_CODE_PP.equals(subscribe.getChannel()) && MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) {
            try {
                CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel());
                bjhyDataService.saveData(tel, subscribe.getChannel(), cmsCrackConfig.getServiceId());
            } catch (Exception e) {
                log.error("写入白金会员数据到235异常");
            }
        }
        //包月状态校验60分钟和次日异步校验
        rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
        //加入业务自增序列
//        subscribeService.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//            subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//        } else {
//            String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//            if (StringUtils.isNotBlank(owner)) {
//                subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//            }
//        }
        //写入自增序列
        subscribeService.saveChannelLimit(subscribe);
        //外部渠道加入回调延迟队列(暂时不使用队列)
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            outsideCallbackService.outsideCallbackAsync(subscribeService.getById(subscribe.getId()), "回调通知");
            //            redisDelayedQueueManager.addCallbcakNotify(CallbackNotifyMessage.builder().id(subscribe.getId())
            //                    .failCount(1).msg("回调通知").build(), 0, TimeUnit.MINUTES);
        }
    }

    /**
     * 白金会员前端开通结果回调
     *
     * @param subscribe
     * @return
     */
    public Result<?> receiveBjhyOrderCallback(Subscribe subscribe) {
        if (StringUtils.isBlank(subscribe.getMobile()) || !subscribe.getMobile().matches(Regexp.MOBILE_REG)) {
            return Result.error("无效的手机号");
        }
        if (subscribe.getStatus() == null || !(subscribe.getStatus() == 1 || subscribe.getStatus() == 0)) {
            return Result.error("无效的订购状态");
        }
        Subscribe sub = subscribeService.getById(subscribe.getTransactionId());
        if (sub == null || !subscribe.getMobile().equals(sub.getMobile())) {
            return Result.error("无效的订单号");
        }
        String mobile = subscribe.getMobile();
        final String channelCode = subscribe.getChannel();
        subscribe.setOpenTime(new Date());
        subscribe.setId(subscribe.getTransactionId());
        subscribeService.updateSubscribeDbAndEs(subscribe);
        //推送宜搜订购状态
        if (MiguApiService.BIZ_BJHYYS_CHANNEL_CODE.equals(channelCode)) {
            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
            pushYisouSubscribeStatus(channelCode, serviceId, mobile, subscribe.getStatus(), subscribe.getIp());
        }

        //信息流广告转化上报
        channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
        //移动开通成功
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()) && MobileRegionResult.ISP_YIDONG.equals(sub.getIsp())) {
            //包月状态校验60分钟和次日异步校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //报备发送短信
            String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelCode).getServiceId();
            smsModelService.sendSmsAsync(subscribe.getMobile(), channelCode, serviceId, BUSINESS_TYPE_OFFICIAL);

            //            if (/*StringUtils.equalsAny(subscribe.getChannel(), MiguApiService.BIZ_BJHY_CHANNEL_CODE)*/BJHY_CHANNEL_LIST.contains(subscribe.getChannel())) {
            //                datangSmsService.sendCrackBaijinMemberNotice(subscribe.getMobile());
            //
            ////                String serviceId = bizProperties.getMiguChannelConfigMap().get(miguChannel).getServiceId();
            ////                redisDelayedQueueManager.addBjhy(BjhyDelayedMessage.builder().msisdn(subscribe.getMobile()).serviceId(serviceId).channelCode(subscribe.getChannel()).msg("权益领取领取提醒短信").build(), DELAY_NOTICE_TIME, TimeUnit.MINUTES);
            //            } else if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHYYS_CHANNEL_CODE) */YISHOU_CHANNEL_LIST.contains(subscribe.getChannel())) {
            //                datangSmsService.sendCrackBaijinysMemberNotice(subscribe.getMobile());
            //            } else if (/*StringUtils.equalsAny(subscribe.getChannel(),MiguApiService.BIZ_BJHYDY_CHANNEL_CODE)*/DONGYI_CHANNEL_LIST.contains(subscribe.getChannel())) {
            ////                datangSmsService.sendCrackBaijindyMemberNotice(subscribe.getMobile());
            //                String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
            //                smsModelService.sendSms(subscribe.getMobile(),subscribe.getChannel(),serviceId,"3");
            //            }else if (YYQQB_CHANNEL_LIST.contains(subscribe.getChannel())) {
            //                String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
            //                smsModelService.sendSms(subscribe.getMobile(),subscribe.getChannel(),serviceId,"3");
            //            }
            //QY QZ不下发PPTV
            if (StringUtils.equalsAny(channelCode, MiguApiService.BIZ_MIGU_RD_CHANNEL_CODE, MiguApiService.CENTRALITY_CHANNEL_CODE)) {
                String outOrderId = IdWorker.get32UUID();
                //发送pptv权益
                try {
                    //                    String serviceId = bizProperties.getMiguChannelConfigMap().get(subscribe.getChannel()).getServiceId();
                    pptvApiService.pptvRightsRecharge(subscribe.getMobile(), outOrderId, serviceId, channelCode);
                } catch (Exception e) {
                    log.error("pptv,发送会员权益,异常信息=>phone:{},outOrderId:{},channelCode:{},msg:{}", subscribe.getMobile(), outOrderId, channelCode, e.getMessage(), e);
                }
            }
        }

        //订购成功的 移动视频彩铃业务/白金会员畅听版+视频彩铃(组合包) 免费订视频彩铃
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())
                && (BIZ_TYPE_VRBT.equals(subscribe.getBizType()) || BizConstant.isBjhyZhbChannel(channelCode))
                && MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())
                && !thirdPartyChannelConfigProperties.isThirdPartyChannel(channelCode)
                && !BizConstant.isVrbtGaojiePassthroughChannel(channelCode)) {

            //为开通之后要订视频彩铃的做彩铃开通
            log.info("收到订购开通包月成功通知后订之后的视频彩铃进行延时订购=>订单号:{},手机号:{}", subscribe.getId(), mobile);
            final String channelCopyrightId = SpringContextUtils.getBean(IMusicService.class).getChannelCopyrightId(channelCode, subscribe.getCopyrightId());
            //如果用户选择了要订购的视频彩铃就直接订购,否则按比例随机订购一首默认彩铃
            if (StringUtils.isNotEmpty(channelCopyrightId)) {
                orderVrbtService.vrbtToneFreeOrderDelay(subscribe, channelCopyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_BEIJIAO);
            } else {
                boolean isOutsideChannel = outsideConfigService.isOutsideChannel(subscribe.getSubChannel());
                //(非外部渠道)没有设置视频彩铃的需要按比例随机订一首默认的视频彩铃
                if (!isOutsideChannel && BIZ_TYPE_VRBT.equals(subscribe.getBizType())) {
                    orderVrbtService.orderByRandomDelay(subscribe);
                }
            }
        }

        return Result.ok("成功");
    }

    /**
     * 推送宜搜订购状态
     *
     * @param channel
     * @param serviceId
     * @param mobile
     * @param status
     * @param ip
     */
    private void pushYisouSubscribeStatus(String channel, String serviceId, String mobile, Integer status, String ip) {
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("channel", channel);
        dataNode.put("feecode", serviceId);
        dataNode.put("mobile", mobile);
        dataNode.put("status", status == 1 ? 0 : 2);
        dataNode.put("cpparam", mobile);
        dataNode.put("ip", ip);
        log.info("推送宜搜订购状态,请求参数=>渠道号:{},业务ID:{},手机号:{},订购状态:{},IP:{}", channel, serviceId, mobile, status == 1 ? 0 : 2, ip);
        httpRequestService.implementHttpGetRequest("http://mp3.heyule.net:9070/rdo/NotifyServlet", dataNode, "推送宜搜订购状态");
    }

    public void qyclNotifyHandle(String mobile, boolean isThirdParty, boolean isSuccess, String desc) {
        try {
            Subscribe subscribe = subscribeService.lambdaQuery()
                    .eq(Subscribe::getMobile, mobile)
                    .eq(Subscribe::getBizType, !isThirdParty ? BIZ_TYPE_QYCL : BIZ_TYPE_OUTSIDE_QY)
                    .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SMS_CODE_SUBMITED)
                    .orderByDesc(Subscribe::getCreateTime)
                    .last(BizConstant.SQL_LIMIT_ONE).one();
            if (subscribe == null) {
                log.error("企业彩铃计费更新订购结果失败,不存在该手机号的订购通知记录,mobile:{}", mobile);
                //如果在subscribe表中未找到数据,则可以认定为是客服通过后台添加的成员
                qyclQywxService.qyclQywxNotify(mobile);
                return;
            }
            //跳过成功的重复通知
            if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
                log.warn("企业彩铃跳过成功订单处理,mobile:{}", mobile);
                return;
            }
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(isSuccess ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
            upd.setResult(isSuccess ? "成功" : desc);
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (isSuccess) {
                //开通成功写入自增序列
                subscribeService.saveChannelLimit(subscribe);
                //添加包月状态延迟校验消息到队列
                if (!isThirdParty) {
                    rabbitMQMsgSender.addQyclDelayedVerifyMessage(subscribe);
                    qyclUnionLimitSms(subscribe.getChannel());
                }

                if ("source:aivrbtApp".equals(subscribe.getRemark())) {
                    //订购成功通知炫酷来电App
                    rabbitMQMsgSender.sendAppSubscribeEventMessage(subscribeService.getById(subscribe.getId()));
                }
            }
            //判断是否上报
            if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_QYCL_DY, BIZ_QYCL_DY_MH)) {
                //BIZ_QYCL_DY, BIZ_QYCL_DY_MH 需要支付成功才上报
                if (qyclOrderPayLogService.queryOrderPaySuccess(subscribe.getId())) {
                    channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), upd.getStatus());
                }
            } else {
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                } else {
                    channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), upd.getStatus());
                }
            }
        } catch (Exception e) {
            log.error("企业彩铃回调修改sub数据异常:", e);
        }
    }

    public void qyclNotifyHandleNoAdFeedback(String mobile) {
        try {
            Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getBizType, BIZ_TYPE_QYCL).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        } catch (Exception e) {
            log.error("企业彩铃回调修改sub数据异常:", e);
        }
    }

    public void qyclUnionLimitSms(String channel) {
        if (enterpriseVrbtProperties.getUnionLimitChannel().contains(channel)) {
            //Integer currentUnionNumber = enterpriseVrbtProperties.getUnionLimitProvince().stream().map(province -> subscribeService.getIncrChannelProvinceLimit(channel, province)).collect(Collectors.summingInt(Integer::intValue));
            Integer currentUnionNumber = subscribeService.getIncrChannelLimit(channel);
            Integer unionLimit = enterpriseVrbtProperties.getUnionLimit();
            if (currentUnionNumber >= unionLimit) {
                log.warn("企业彩铃渠道号:{},联合省份已经达量，限量数:{},当前总户数:{}", channel, unionLimit, currentUnionNumber);
                String key = CacheConstant.CMS_CACHE_SMS_VERGE_LIMIT + channel + ":" + unionLimit;
                if (!redisUtil.hasKey(key)) {
                    final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
                    redisUtil.set(key, 1, expire);
                } else {
                    return;
                }
                enterpriseVrbtProperties.getUnionLimitMobile().forEach(mobile -> {
                    messageNotifyService.sendTotalVergeNotify(mobile, channel, unionLimit, currentUnionNumber);
                    //smsNotifyService.sendNotify(mobile, "渠道号:" + channel + ",限量省份总和即将到达量,限量数" + unionLimit + "，现在总户数" + currentUnionNumber);
                });
            }
        }
    }

    /**
     * @param mobile
     * @param orderId 暂时无orderId返回
     * @param resCode
     * @param resMsg
     */
    public void heyuNotify(String mobile, String orderId, String resCode, String resMsg) {
        //查询订购数据
        Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getRemark, mobile).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if (subscribe == null) {
            log.error("未查询到该用户数据,手机号:{}", mobile);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},orderId:{}", mobile, orderId);
            return;
        }
//        //写入自增序列
//        subscribeService.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//            subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//        } else {
//            String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//            if (StringUtils.isNotBlank(owner)) {
//                subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//            }
//        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus("000000".equals(resCode) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        upd.setResult(resMsg);
        upd.setOpenTime(new Date());
        upd.setModifyTime(new Date());
        subscribeService.updateSubscribeDbAndEs(upd);
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), "000000".equals(resCode) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        }
    }

    public void aidoulaidianNotify(String mobile, String cpparam, String status) {
        //查询订购数据
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, cpparam);
        if (subscribe == null) {
            log.error("未查询到该用户数据,手机号:{}", mobile);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},orderId:{}", mobile, cpparam);
            return;
        }
        if ("DG".equals(status)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("订购成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            } else {
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }
        } else if ("TD".equals(status)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult("已退订");
            upd.setVerifyStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
    }

    public void shoujizixunNotify(String mobile, String cpparam, String status, String msg) {
        //查询订购数据
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, cpparam);
        if (subscribe == null) {
            log.error("未查询到该用户数据,手机号:{}", mobile);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},orderId:{}", mobile, cpparam);
            return;
        }
        if ("DELIVRD".equals(status)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("订购成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            } else {
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(msg);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
    }

    public void chongqingQycsNotify(String status, String mobile, String tradeid, String mrtime) {
        //查询订购数据
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, tradeid);
        if (subscribe == null) {
            log.error("未查询到该用户数据,手机号:{}", mobile);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},orderId:{}", mobile, tradeid);
            return;
        }
        if ("1".equals(status)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("订购成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            } else {
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult("订购失败");
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
    }

    public void beijiyihuiNotify(String mobile, String status, String linkId, String msg) {
        //查询订购数据
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, linkId);
        if (subscribe == null) {
            log.error("未查询到该用户数据,手机号:{}", mobile);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},orderId:{}", mobile, linkId);
            return;
        }
        if ("1".equals(status)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("订购成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            } else {
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(msg);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
    }

    public void fengzhushouSichuanYidongReceive(String mobile, String code, String msg, String transactionId, String result) {
        //查询订购数据
        Subscribe subscribe = subscribeService.getById(transactionId);
        if (subscribe == null) {
            log.error("未查询到该用户数据,手机号:{}", mobile);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},orderId:{}", mobile, transactionId);
            return;
        }
        if ("0000".equals(code)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("订购成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            } else {
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(msg);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        outsideCallbackService.fengshushouCallbackAsync(mobile, code, msg, result);
    }

    public void stpJiangsuyidongNotify(String ispOrderNo, String code) {
        //查询订购数据
        Subscribe subscribe = subscribeService.findByIspOrderId(ispOrderNo);
        if (subscribe == null) {
            log.error("未查询到该用户数据,ispOrderNo:{}", ispOrderNo);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},orderId:{}", subscribe.getMobile(), subscribe.getIspOrderNo());
            return;
        }
        if ("20".equals(code)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("订购成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            } else {
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult("订购失败");
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
    }

    /**
     * 骏伯流量包通知处理
     *
     * @param sysOrderId
     */
    public void junboLlbNotify(String sysOrderId, String orderStatus, String orderStatusMsg) {
        //骏伯贵州移动特殊处理
        JunboSub junboSub = junboSubService.lambdaQuery().eq(JunboSub::getIspOrderNo, sysOrderId).last(BizConstant.SQL_LIMIT_ONE).one();
        if (junboSub != null) {
            JunboSub upd = new JunboSub();
            upd.setId(junboSub.getId());
            upd.setResult(orderStatusMsg);
            if (JUNBO_LLB_STATUS_SUCCESS.equals(orderStatus)) {
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setModifyTime(new Date());
                junboSubService.updateById(upd);
            } else if (JUNBO_LLB_STATUS_FAIL.equals(orderStatus)) {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setPrice(SUBSCRIBE_STATUS_FAIL);
                upd.setModifyTime(new Date());
                junboSubService.updateById(upd);
            } else if (JUNBO_LLB_STATUS_UNSUB.equals(orderStatus)) {
                upd.setVerifyStatus(SUBSCRIBE_MONTH_VERIFY_NONE);
                upd.setVerifyStatusDaily(SUBSCRIBE_MONTH_VERIFY_NONE);
                upd.setPrice(SUBSCRIBE_STATUS_FAIL);
                upd.setModifyTime(new Date());
                junboSubService.updateById(upd);
            }
            return;
        }
        Subscribe subscribe = subscribeService.findByIspOrderId(sysOrderId);
        //查询订购数据
        if (subscribe == null) {
            log.error("骏伯流量包未查询到该用户数据,订单号:{}", sysOrderId);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("骏伯流量包跳过成功订单处理,mobile:{},orderId:{}", subscribe.getMobile(), sysOrderId);
            return;
        }

        //订购状态是开通成功并且是互娱游戏业务类型不更新订购状态，只标记
        if (JUNBO_LLB_STATUS_SUCCESS.equals(orderStatus) && BizConstant.isMiguNotifySuccessBizType(subscribe.getBizType())) {
            log.warn("骏伯流量包订购状态是开通成功并且是互娱游戏业务类型不更新订购状态,mobile:{},orderId:{}", subscribe.getMobile(), sysOrderId);
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setExtra(orderStatusMsg);
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return;
        }
        log.warn("骏伯业务订购通知-mobile:{},orderId:{},orderStatus:{},orderStatusMsg:{}", subscribe.getMobile(), sysOrderId,orderStatus,orderStatusMsg);
        if (JUNBO_LLB_STATUS_SUCCESS.equals(orderStatus)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(orderStatusMsg);
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);


//            Subscribe upd = new Subscribe();
//            upd.setId(subscribe.getId());
//            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//            upd.setResult(orderStatusMsg);
//            subscribeService.updateSubscribeAndEs(upd);

            subscribeService.saveChannelLimit(subscribe);
            if ("SXYD_JBAICL".equals(subscribe.getChannel())) {
                // 延迟校验是否退订
                rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                String miguChannelCode = BIZ_SCH_CHANNEL_CODE_YYCP_SXJB;
                // 领取特殊权益
                rabbitMQMsgSender.sendSchSpecialProductSubMsg(subscribe.getMobile(), miguChannelCode);
                // 铃音上传
                rabbitMQMsgSender.sendMiguRingFtpUploadMessage(MiguRingFtpUploadMessage.builder().id(subscribe.getId()).mobile(subscribe.getMobile()).tag(miguChannelCode).build());
            }
        } else if (JUNBO_LLB_STATUS_FAIL.equals(orderStatus) || JUNBO_CPA_STATUS_FAIL.equals(orderStatus)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(orderStatusMsg);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        } else if (JUNBO_LLB_STATUS_UNSUB.equals(orderStatus)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setExtra("退订订单");
            subscribeService.updateSubscribeDbAndEs(upd);
            log.warn("骏伯流量包退订订单,mobile:{},orderId:{}", subscribe.getMobile(), sysOrderId);
            return;
        }
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), "7".equals(orderStatus) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        }

    }

    /**
     * 聚杰业务通知处理
     *
     * @param ispOrderNo
     * @param orderStatus
     * @param msg
     */
    public void jujieNotify(String ispOrderNo, Integer orderStatus, String msg) {
        Subscribe subscribe = subscribeService.findByIspOrderId(ispOrderNo);
        //查询订购数据
        if (subscribe == null) {
            log.error("聚杰业务未查询到该用户数据,订单号:{}", ispOrderNo);
            return;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("聚杰业务跳过成功订单处理,mobile:{},orderId:{}", subscribe.getMobile(), ispOrderNo);
            return;
        }
        if (GuangdongJujieResult.CODE_HANDLE_SUCCESS.equals(orderStatus)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(msg);
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            subscribeService.saveChannelLimit(subscribe);
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(msg);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), GuangdongJujieResult.CODE_HANDLE_SUCCESS.equals(orderStatus) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        }

    }

    /**
     * 彦成江西爱豆来电订购结果通知处理
     *
     * @param orderResultNotify orderResultNotify
     * @return String
     */
    public String ycjxadldOrderResultNotify(YCJXADLDOrderResultNotifyDTO orderResultNotify) {
        String cpparam = orderResultNotify.getCpparam();
        Subscribe subscribe = subscribeService.findByIspOrderId(cpparam);
        //查询订购数据
        if (subscribe == null) {
            log.error("彦成江西移动爱豆来电业务未查询到该用户数据,{}", orderResultNotify);
            return "fail";
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("彦成江西移动爱豆来电业务跳过成功订单处理,mobile:{},orderId:{}", subscribe.getMobile(), cpparam);
            return "fail";
        }
        final boolean isOrderSucc = "DG".equals(orderResultNotify.getStatus());
        if (isOrderSucc) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult("订购成功");
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            subscribeService.saveChannelLimit(subscribe);
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(orderResultNotify.getStatus());
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), isOrderSucc ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        }
        return "ok";
    }

    /**
     * 电信开通结果处理
     *
     * @param mobile
     * @param orderId
     * @param resCode
     * @param msg
     * @return
     */
    public String dianxinVrbtNotify(String mobile, String orderId, String resCode, String msg) {
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, orderId);
        //查询订购数据
        if (subscribe == null) {
            log.error("电信视频彩铃业务未查询到该用户数据,手机号:{},订单号:{}", mobile, orderId);
            return "fail";
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("电信视频彩铃业务跳过成功订单处理,mobile:{},orderId:{}", mobile, orderId);
            return "fail";
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        if ("0".equals(resCode)) {
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(msg);
            upd.setOpenTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            subscribeService.saveChannelLimit(subscribe);
        } else {
            upd.setResult(msg);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), "0".equals(resCode) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        }
        return "ok";
    }

    /**
     * 联通沃阅读开通结果处理
     *
     * @param mobile
     * @param orderId
     * @return
     */
    public String liantongWydNotify(String mobile, String orderId) {
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, orderId);
        //查询订购数据
        if (subscribe == null) {
            log.error("联通沃阅读业务未查询到该用户数据,手机号:{},订单号:{}", mobile, orderId);
            return "fail";
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("电信视频彩铃业务跳过成功订单处理,mobile:{},orderId:{}", mobile, orderId);
            return "fail";
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
        upd.setResult("成功");
        upd.setOpenTime(new Date());
        upd.setModifyTime(new Date());
        subscribeService.updateSubscribeDbAndEs(upd);
        subscribeService.saveChannelLimit(subscribe);
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
        }
        return "ok";
    }
}
