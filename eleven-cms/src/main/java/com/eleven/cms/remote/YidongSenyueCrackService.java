package com.eleven.cms.remote;

import com.eleven.cms.config.YidongSenyueCrackProperties;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.BillingResult;
import com.eleven.qycl.config.EnterpriseVrbtConfig;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 *  森越PJ(是森越这家公司介绍的互娱pj)
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class YidongSenyueCrackService {

    @Autowired
    private Environment environment;
    @Autowired
    private YidongSenyueCrackProperties yidongSenyueCrackProperties;

    @Autowired
    private EnterpriseVrbtProperties enterpriseVrbtProperties;

    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;

    private OkHttpClient client;
    private ObjectMapper mapper;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    BillingResult getSms(String phone, String channel,String subscribeId) {

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channel);
        String getSmsUrl = yidongSenyueCrackProperties.getGetSmsUrl();
        //http://47.100.131.180:8080/xcode/biz/request?productId=0d6e640a&phone=18695285917&outTradeNo=a552123d1e7bcbef2127653&channelId=7b2f0e5b8e
        final HttpUrl httpUrl = HttpUrl.parse(getSmsUrl)
            .newBuilder()
                .addQueryParameter("phone", phone)
                .addQueryParameter("productId", cmsCrackConfig.getSenyueProductId())
                .addQueryParameter("outTradeNo", subscribeId)
                .addQueryParameter("channelId", cmsCrackConfig.getSenyueChannelId())
            .build();
        log.info("{}-获取短信-渠道号:{},手机号:{},subscribeId:{},请求:{}", "森越PJ" + cmsCrackConfig.getLogTag(), channel, phone, subscribeId, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-渠道号:{},手机号:{},subscribeId:{},响应:{}", "森越PJ" + cmsCrackConfig.getLogTag(), channel, phone, subscribeId,content);
            final JsonNode tree = mapper.readTree(content);
            //{"code":"00000","data":{"linkId":"1833764854331080705"},"message":"success"}
            return new BillingResult(tree.at("/code").asText(), tree.at("/message").asText(), tree.at("/data/linkId").asText());
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-渠道号:{},手机号:{},异常:", "森越PJ" + cmsCrackConfig.getLogTag(), channel, phone, e);
            return BillingResult.fail();
        }
    }


    /**
     * 提交短信验证码
     *
     * @param orderId
     * @param smsCode
     * @return
     */
    public @Nonnull
    BillingResult smsCode(String orderId, String smsCode, String channel, String mobile) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channel);
        String smsValidUrl = yidongSenyueCrackProperties.getSmsValidUrl();
        //http://47.100.131.180:8080/xcode/biz/submit?linkId=1433307022261153794&smsCode=642941
        final HttpUrl httpUrl = HttpUrl.parse(smsValidUrl)
            .newBuilder()
            .addQueryParameter("linkId", orderId)
            .addQueryParameter("smsCode", smsCode)
            .build();
        log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},短信验证码:{},请求:{}", "森越PJ" + cmsCrackConfig.getLogTag(), channel, orderId, mobile, smsCode, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},响应:{}", "森越PJ" + cmsCrackConfig.getLogTag(), channel, orderId, mobile, content);
            final JsonNode tree = mapper.readTree(content);
            return new BillingResult(tree.at("/code").asText(), tree.at("/message").asText(), tree.at("/data/linkId").asText());
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},异常:", "森越PJ" + cmsCrackConfig.getLogTag(), channel, orderId, mobile, e);
            return BillingResult.fail();
        }
    }

    public static void main(String[] args) throws JsonProcessingException {
        //{"code":"00000","message":"success","data":{"linkId": 00000006079}}
        String content = "{\"code\":\"00000\",\"data\":{\"linkId\":\"1833764854331080705\"},\"message\":\"success\"}";
        final JsonNode tree = new ObjectMapper().readTree(content);
        System.out.println(new BillingResult(tree.at("/code").asText(), tree.at("/message").asText(),
                tree.at("/data/linkId").asText()));
    }

}
