package com.eleven.cms.remote;

import com.eleven.cms.config.KuaimaMiniAppProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.KuaimaMiniAppResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class KuaimaMiniAppService {

    public static final String LOG_TAG = "快马小程序四川移动业务api";
    @Autowired
    KuaimaMiniAppProperties kuaimaMiniAppProperties;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private ObjectMapper mapper;

    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    public KuaimaMiniAppResult prepareOrder(String phone,String feeId) throws Exception {
        String url = kuaimaMiniAppProperties.getPrepareOrderUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .addQueryParameter("action", kuaimaMiniAppProperties.getAction())
                .addQueryParameter("channel", kuaimaMiniAppProperties.getChannel())
                .addQueryParameter("appid", kuaimaMiniAppProperties.getAppId())
                .addQueryParameter("feeid", feeId)
                .addQueryParameter("phone", phone)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-预下单-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-预下单-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, KuaimaMiniAppResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-预下单-手机号:{},异常:", LOG_TAG, phone, e);
            return KuaimaMiniAppResult.FAIL_RESULT;
        }
    }


}
