package com.eleven.cms.remote;

import com.eleven.cms.config.XinjiangProperties;
import com.eleven.cms.config.XinjiangYouranProperties;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.XinjiangYidongResult;
import com.eleven.cms.vo.XinjiangYidongYouranResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2022-08-12 10:58
 */
@Slf4j
@Service
public class XinjiangYidongYouranService {

    public static final String LOG_TAG = "新疆移动悠然api";


    @Autowired
    private Environment environment;
    @Autowired
    private XinjiangYouranProperties xinjiangYouranProperties;

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }

    /**
     * @param phone
     * @param channel XINJ_UNHT_checkVerifyCodeNew 获取验证码 :XINJ_UNHT_checkVerifyCode 验证码校验
     *                XINJ_UNHT_checkCustAge 年龄校验 XINJ_UNHQ_CheckPersonOpenDate 入网年龄校验
     *                XINJ_UNHT_offerOrder 业务办理
     * @return
     */

    public @Nonnull
    boolean getSms(String phone, String channel) {
        String bizCode = xinjiangYouranProperties.getBizCodeByChannel(channel);
        //8位产品编码前加 1300，例如，产品编码为：20220734，发送短信接口中的 offer_id 参数传入：130020220734
        if (StringUtils.length(bizCode) == 8 && !StringUtils.startsWith(bizCode, "999")) {
            bizCode = "1300" + bizCode;
        }
        HttpUrl httpUrl = getCommonParam("XINJ_UNHT_checkVerifyCodeNew");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("ACCESS_NUM", phone);
        dataNode.put("OFFER_ID", bizCode);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-获取验证码-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            String respCode = jsonNode.at("/respCode").asText();
            String resultCode = jsonNode.at("/result/OUTDATA/DATAS/RESULT_CODE").asText();
            return "0".equals(respCode) && "0".equals(resultCode);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取验证码-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return false;
        }
    }

    /**
     * {
     * "respCode": "0",
     * "result": {
     * "X_NODE_NAME": "****",
     * "X_RESULTINFO": "ok",
     * "OUTDATA": {
     * "DATAS": {
     * "RESULT_CODE": "0",
     * "RESULT_INFO": "验证码校验通过"
     * }
     * },
     * "X_RESULTCODE": "0",
     * "X_RECORDNUM": "1"
     * },
     * "respDesc": "ok"
     * }
     * 校验失败：
     * {
     * "respCode": "0",
     * "result": {
     * "X_NODE_NAME": "****",
     * "X_RESULTINFO": "ok",
     * "OUTDATA": {
     * "DATAS": {
     * "RESULT_CODE": "1",
     * "RESULT_INFO": "验证码校验超时"
     * }
     * },
     * "X_RESULTCODE": "0",
     * "X_RECORDNUM": "1"
     * },
     * "respDesc": "ok"
     * }
     * 短信验证码校验
     * DATAS RESULT_CODE String 操作结果 0：校验通过，1：校验失败
     *
     * @param phone
     * @param channel
     * @param code
     * @return
     */

    public @Nonnull
    boolean validateSmsCode(String phone, String channel, String code) {
        HttpUrl httpUrl = getCommonParam("XINJ_UNHT_checkVerifyCode");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("ACCESS_NUM", phone);
        dataNode.put("VERIFY_CODE", code);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, channel, phone, code, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},响应:{}", LOG_TAG, channel, phone, code, content);
            JsonNode jsonNode = mapper.readTree(content);
            String respCode = jsonNode.at("/respCode").asText();
            String resultCode = jsonNode.at("/result/OUTDATA/DATAS/RESULT_CODE").asText();
            return "0".equals(respCode) && "0".equals(resultCode);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},异常:", LOG_TAG, channel, phone, code, e);
            return false;
        }
    }

    /**
     * {
     * "respCode": "0",
     * "respDesc": "ok",
     * "result": {
     * "OUTDATA": {
     * "BIZ_CODE": "0000",
     * "CHECK_RESULT": "1",
     * "CHECK_RESULT_DESC": "校验成功"
     * },
     * "X_NODE_NAME": "app-node16-srv08:***",
     * "X_RECORDNUM": "1",
     * "X_RESULTCODE": "0",
     * "X_RESULTINFO": "ok"
     * }
     * 年龄校验 年龄校验值，此值大于等于客户年龄值时返回 1，此值小于客户年龄值时返回 0. BIZ_CODE 为 0000 时，必然返回
     *
     * @param phone
     * @param channel
     * @return
     */
    public @Nonnull
    boolean checkAge(String phone, String channel, Integer age) {
        HttpUrl httpUrl = getCommonParam("XINJ_UNHT_checkCustAge");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("ACCESS_NUM", phone);
        dataNode.put("CHECK_AGE_VALUE", age);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-年龄校验-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-年龄校验-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            String respCode = jsonNode.at("/respCode").asText();
            String checkResult = jsonNode.at("/result/OUTDATA/CHECK_RESULT").asText();
            if (age == 18) {
                return "0".equals(respCode) && "0".equals(checkResult);
            } else {
                return "0".equals(respCode) && "1".equals(checkResult);

            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-年龄校验-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return false;
        }
    }

    /**
     * {
     * "respCode": "0",
     * "respDesc": "ok",
     * "result": {
     * "OUTDATA": {
     * "CHECK_RESULT": "TRUE"
     * },
     * "X_NODE_NAME": "app-node03-srv08:***",
     * "X_RECORDNUM": "1",
     * "X_RESULTCODE": "0",
     * "X_RESULTINFO": "ok"
     * }
     * }
     * 入网天数校验
     * 入网时长(单位：天)，校验该号码入网时长是否大于等于指定天数，如果大于则返回成功，否则返回失败。
     *
     * @param phone
     * @param channel
     * @return
     */
    public @Nonnull
    boolean checkPersonOpenDate(String phone, String channel) {
        HttpUrl httpUrl = getCommonParam("XINJ_UNHQ_CheckPersonOpenDate");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("ACCESS_NUM", phone);
        dataNode.put("OPEN_DAYS", "60");
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-入网天数校验-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-入网天数校验-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            String respCode = jsonNode.at("/respCode").asText();
            String checkResult = jsonNode.at("/result/OUTDATA/CHECK_RESULT").asText();
            return "0".equals(respCode) && "TRUE".equals(checkResult);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-入网天数校验-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return false;
        }
    }


    /**
     * 业务办理接口
     *
     * @param phone
     * @param channel
     * @return
     */
    public @Nonnull
    XinjiangYidongYouranResult offerOrder(String phone, String channel) {
        String bizCode = xinjiangYouranProperties.getBizCodeByChannel(channel);
        HttpUrl httpUrl = getCommonParam("XINJ_UNHT_offerOrder");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("ACCESS_NUM", phone);
        ObjectNode offerNode = mapper.createObjectNode();
        offerNode.put("ACTION", "0");
        offerNode.put("OFFER_CODE", bizCode);
        offerNode.put("OFFER_TYPE", "D");
        ArrayNode arrayNode = mapper.createArrayNode();
        arrayNode.add(offerNode);
        dataNode.put("OFFER_LIST", arrayNode);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-业务订购-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-业务订购-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, XinjiangYidongYouranResult.class);
//            JsonNode jsonNode = mapper.readTree(content);
//            String respCode = jsonNode.at("/respCode").asText();
//            String resultCode = jsonNode.at("/result/OUTDATA/X_RESULTCODE").asText();
//            return "0".equals(respCode) && "0".equals(resultCode);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-业务订购-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return XinjiangYidongYouranResult.FAIL_RESULT;
        }
    }

    /**
     * 业务办理接口
     *
     * @param phone
     * @param channel
     * @return
     */
    public @Nonnull
    XinjiangYidongYouranResult unionCaseAcceptAcceptOrder(String phone, String channel) {
        String bizCode = xinjiangYouranProperties.getBizCodeByChannel(channel);
        String bizName = xinjiangYouranProperties.getBizCodeByName(channel);
        HttpUrl httpUrl = getCommonParam("XINJ_UNHQ_unionCaseAcceptAcceptOrder");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("ACCESS_NUM", phone);
        dataNode.put("UNION_TYPE", "0");
        dataNode.put("UNION_OFFER_ID", bizCode);
        dataNode.put("UNION_OFFER_NAME", bizName);
        dataNode.put("SALE_OFFER_CODE", queryDetailUnionInfo(phone, channel));
        dataNode.put("CHANNEL_TAG", "W");

        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-一事一案业务订购-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-一事一案业务订购-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, XinjiangYidongYouranResult.class);
//            JsonNode jsonNode = mapper.readTree(content);
//            String respCode = jsonNode.at("/respCode").asText();
//            String resultCode = jsonNode.at("/result/OUTDATA/X_RESULTCODE").asText();
//            return "0".equals(respCode) && "0".equals(resultCode);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-一事一案业务订购-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return XinjiangYidongYouranResult.FAIL_RESULT;
        }
    }

    public String queryDetailUnionInfo(String phone, String channel) {
        String bizCode = xinjiangYouranProperties.getBizCodeByChannel(channel);
        HttpUrl httpUrl = getCommonParam("XINJ_UNHQ_queryDetailUnionInfo");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("ACCESS_NUM", phone);
        dataNode.put("OFFER_ID", bizCode);

        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-一事一案商品详情查询-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-一事一案商品详情查询-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            return jsonNode.at("/result/OUTDATA/SALE_OFFER_DATA/OFFER_CODE").asText();
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-一事一案商品详情查询-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return "";
        }


    }


    /**
     * cpparam	业务受理标志：
     * XINJ_UNHT_sendIdentifyCode：短信验证码发送
     * XINJ_UNHT_checkVerifyCode：短信验证码效验
     * XINJ_UNHT_offerOrder：资费编码202打头(视频彩铃类套餐)
     * XINJ_UNHQ_unionCaseAcceptAcceptOrder：资费编码999打头
     *
     * @param phone
     * @param channel
     * @return
     */
    public XinjiangYidongYouranResult order(String phone, String channel) {
        String bizCode = xinjiangYouranProperties.getBizCodeByChannel(channel);
        if (StringUtils.startsWith(bizCode, "20")) {
            return offerOrder(phone, channel);
        } else if (StringUtils.startsWith(bizCode, "999")) {
            return unionCaseAcceptAcceptOrder(phone, channel);
        } else {
            return XinjiangYidongYouranResult.FAIL_RESULT;
        }
    }

    public HttpUrl getCommonParam(String method) {
        final HttpUrl httpUrl = HttpUrl.parse(xinjiangYouranProperties.getApiBaseUrl())
                .newBuilder()
                .addQueryParameter("method", method)
                .addQueryParameter("format", "json")
                .addQueryParameter("appId", xinjiangYouranProperties.getAppId())
                .addQueryParameter("appKey", xinjiangYouranProperties.getAppKey())
                .addQueryParameter("status", "1") //0沙箱 1正式
                .addQueryParameter("timestamp", DateUtil.formatFullTime(LocalDateTime.now()))
                .addQueryParameter("provinceCode", xinjiangYouranProperties.getProvinceCode())
                .addQueryParameter("tradeStaffId", xinjiangYouranProperties.getTradeStaffId())
                .addQueryParameter("tradeEparchyCode", xinjiangYouranProperties.getTradeEparchyCode())
                .addQueryParameter("tradeCityCode", xinjiangYouranProperties.getTradeCityCode())
                .addQueryParameter("tradeDepartId", xinjiangYouranProperties.getTradeDepartId())
                .addQueryParameter("channelTypeId", xinjiangYouranProperties.getChannelTypeId())
                .addQueryParameter("flowId", DateUtil.formatForMiguGroupApi(LocalDateTime.now()))
                .addQueryParameter("tradeDepartName", xinjiangYouranProperties.getTradeDepartName())
                .addQueryParameter("tradeStaffName", xinjiangYouranProperties.getTradeStaffName())
                .addQueryParameter("routeEparchyCode", xinjiangYouranProperties.getRouteEparchyCode())
                .addQueryParameter("tradeDepartPassWd", xinjiangYouranProperties.getTradeDepartPassWd())
                .build();
        return httpUrl;
    }

}
