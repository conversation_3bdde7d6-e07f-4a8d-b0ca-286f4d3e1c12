package com.eleven.cms.remote;

import com.eleven.cms.config.KuaimaShanxiProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.shanxi.AesUtil;
import com.eleven.cms.util.shanxi.MD5;
import com.eleven.cms.vo.KuaimaShanxiResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class KuaimaShanxiService {

    public static final String LOG_TAG = "快马山西业务api";

    @Autowired
    KuaimaShanxiProperties kuaimaShanxiProperties;

    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;


    private static final MediaType mediaType = MediaType.parse("application/json");


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public KuaimaShanxiResult getSms(String phone, String channel) {
        String url = kuaimaShanxiProperties.getGetSmsUrl();
        Map<String, Object> bizContentMap = new HashMap<>();
        bizContentMap.put("phone", phone);
        bizContentMap.put("product_code", kuaimaShanxiProperties.getProductCodeByChannel(channel));
        bizContentMap.put("channel_no", kuaimaShanxiProperties.getChannelNo());
        RequestBody body = RequestBody.create(mediaType, reqBody(bizContentMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();
        log.info("{}-随机码下发-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            KuaimaShanxiResult kuaimaShanxiResult = mapper.readValue(result, KuaimaShanxiResult.class);
            if(kuaimaShanxiResult.isOK() && StringUtils.isNotBlank(kuaimaShanxiResult.getBizContent())){
                String decrypt = AesUtil.decrypt(kuaimaShanxiResult.getBizContent(), kuaimaShanxiProperties.getKey());
                log.info("{}-提交验证码-手机号:{},明文结果:{}", LOG_TAG, phone, decrypt);
                KuaimaShanxiResult.KuaimaShanxiBizContent kuaimaShanxiBizContent = mapper.readValue(decrypt, KuaimaShanxiResult.KuaimaShanxiBizContent.class);
                kuaimaShanxiResult.setBizContentResult(kuaimaShanxiBizContent);
            }
            return kuaimaShanxiResult;
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return KuaimaShanxiResult.fail();

        }
    }

    public KuaimaShanxiResult smsCode(String phone, String smsCode,String orderId) {
        String url = kuaimaShanxiProperties.getSmsCodeUrl();
        Map<String, Object> bizContentMap = new HashMap<>();
        bizContentMap.put("phone", phone);
        bizContentMap.put("order_id", orderId);
        bizContentMap.put("sms_code", smsCode);
        RequestBody body = RequestBody.create(mediaType, reqBody(bizContentMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();
        log.info("{}-提交验证码-手机号:{},短信验证码:{},请求:{}", LOG_TAG, phone,smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            KuaimaShanxiResult kuaimaShanxiResult = mapper.readValue(result, KuaimaShanxiResult.class);
            if(kuaimaShanxiResult.isOK() && StringUtils.isNotBlank(kuaimaShanxiResult.getBizContent())){
                String decrypt = AesUtil.decrypt(kuaimaShanxiResult.getBizContent(), kuaimaShanxiProperties.getKey());
                log.info("{}-提交验证码-手机号:{},明文结果:{}", LOG_TAG, phone, decrypt);
                KuaimaShanxiResult.KuaimaShanxiBizContent kuaimaShanxiBizContent = mapper.readValue(decrypt, KuaimaShanxiResult.KuaimaShanxiBizContent.class);
                kuaimaShanxiResult.setBizContentResult(kuaimaShanxiBizContent);
            }
            return kuaimaShanxiResult;
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},异常:", LOG_TAG, phone, e);
            return KuaimaShanxiResult.fail();

        }
    }

    public String generateSign(Map<String, Object> dataMap, String appKey) {
        return MD5.createSign(dataMap,appKey);
    }

    private String reqBody(Map<String,Object> bizContentMap){
        try{
            String bizContent = AesUtil.encrypt(mapper.writeValueAsString(bizContentMap), kuaimaShanxiProperties.getKey());;
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("app_id", kuaimaShanxiProperties.getAppId());
            dataMap.put("biz_content", bizContent);
            dataMap.put("sign_type", "MD5");
            dataMap.put("time_stamp", System.currentTimeMillis()/1000 + "");
            dataMap.put("version", "1.0");
            dataMap.put("sign", generateSign(dataMap, kuaimaShanxiProperties.getKey()));
            return mapper.writeValueAsString(dataMap);
        }catch (Exception e){
            log.info("{}-加密出错：", LOG_TAG,e);
            return null;
        }
    }
}
