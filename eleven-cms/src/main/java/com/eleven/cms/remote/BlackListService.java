package com.eleven.cms.remote;

import com.eleven.cms.config.BlackListProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: cai lei
 * @create: 2022-01-19 15:00
 */
@Slf4j
@Service
public class BlackListService {

    private static final String LOG_TAG = "黑名单工具API";

    @Autowired
    private BlackListProperties blackListProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;

    private static final MediaType mediaType = MediaType.parse("application/json");


//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public void saveBlackList(String phone, String reason,String bizType) throws JsonProcessingException {
        String url = blackListProperties.getSaveUrl();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("mobile", phone);
        dataMap.put("reason", reason);
        dataMap.put("bizType", bizType);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-加入黑名单-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-加入黑名单-手机号:{},返回结果:{}", LOG_TAG, phone, result);
        } catch (Exception e) {
            log.info("{}-加入黑名单-手机号:{},异常:", LOG_TAG, phone, e);
        }
    }

    /**
     * 查询是否为黑名单用户 code=509 是黑名单
     *
     * @param phone
     * @return true为黑名单 false不为黑名单
     */
    public boolean isBlackList(String phone) {
        final HttpUrl httpUrl = HttpUrl.parse(blackListProperties.getQueryBlackUrl())
                .newBuilder()
                .addQueryParameter("mobile", phone)
                .build();
        //log.info("{}-查询黑名单-手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString().toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            //log.info("{}-查询黑名单-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            JsonNode tree = mapper.readTree(result);
            return tree.at("/code").asInt() == HttpStatus.BANDWIDTH_LIMIT_EXCEEDED.value();
        } catch (Exception e) {
            log.info("{}-查询黑名单-手机号:{},异常:", LOG_TAG, phone, e);
            return false;
        }
    }

    /**
     * 查询是否为红名单用户 code=509 是红名单
     *
     * @param phone
     * @return true为红名单 false不为红名单
     */
    public boolean isRedList(String phone) {
        final HttpUrl httpUrl = HttpUrl.parse(blackListProperties.getQueryRedUrl())
                .newBuilder()
                .addQueryParameter("mobile", phone)
                .build();
        //log.info("{}-查询红名单-手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString().toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            //log.info("{}-查询红名单-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            JsonNode tree = mapper.readTree(result);
            return tree.at("/code").asInt() == HttpStatus.BANDWIDTH_LIMIT_EXCEEDED.value();
        } catch (Exception e) {
            log.info("{}-查询红名单-手机号:{},异常:", LOG_TAG, phone, e);
            return false;
        }
    }
}
