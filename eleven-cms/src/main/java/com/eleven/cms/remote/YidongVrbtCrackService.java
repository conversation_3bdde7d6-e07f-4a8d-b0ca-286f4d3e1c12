package com.eleven.cms.remote;

import com.eleven.cms.config.YidongVrbtCrackConfig;
import com.eleven.cms.config.YidongVrbtCrackProperties;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.YouranGansuMobileResult;
import com.eleven.qycl.config.EnterpriseVrbtConfig;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 移动视频彩铃破解
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class YidongVrbtCrackService {

    @Autowired
    private Environment environment;
    @Autowired
    private YidongVrbtCrackProperties yidongVrbtCrackProperties;

    @Autowired
    private EnterpriseVrbtProperties enterpriseVrbtProperties;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    private OkHttpClient client;
    private ObjectMapper mapper;

    public static final int FILTER_DENY_CODE = 9051;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return  {"code":9051,"message":"业务失败","resCode":"999999","resMsg":"【OPEN】过滤器校验失败","transId":"2024042318260520203"}
     *          {"code":0,"message":"success","transId":"2024042318280177303"}
     *          {"code":-200,"message":"没有查询权限，请联系管理员"}
     *          true:过滤器校验放行,false:过滤器校验失败
     */
    public boolean filter(String phone, String channel) {

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(yidongVrbtCrackProperties.getFilterUrl())
                .newBuilder()
            .addQueryParameter("paycode", cmsCrackConfig.getPayCode())
            .addQueryParameter("fee", cmsCrackConfig.getFee())
                //.addQueryParameter("cpid", yidongVrbtCrackConfig.getCpid())
                .addQueryParameter("phone", phone)
                .build();
        log.info("{}-前置过滤-渠道号:{},手机号:{},请求:{}", cmsCrackConfig.getLogTag(), channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-前置过滤-渠道号:{},手机号:{},响应:{}", cmsCrackConfig.getLogTag(), channel, phone, content);
            return mapper.readTree(content).get("code").asInt() != FILTER_DENY_CODE;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-前置过滤-渠道号:{},手机号:{},异常:", cmsCrackConfig.getLogTag(), channel, phone, e);
            return Boolean.TRUE;
        }
    }


    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    BillingResult getSms(String phone, String channel) {

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(yidongVrbtCrackProperties.getGetSmsUrl())
                .newBuilder()
            .addQueryParameter("paycode", cmsCrackConfig.getPayCode())
            .addQueryParameter("fee", cmsCrackConfig.getFee())
            .addQueryParameter("cpid", cmsCrackConfig.getCpid())
                .addQueryParameter("phone", phone)
                .build();
        log.info("{}-获取短信-渠道号:{},手机号:{},请求:{}", cmsCrackConfig.getLogTag(), channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-渠道号:{},手机号:{},响应:{}", cmsCrackConfig.getLogTag(), channel, phone, content);
            return mapper.readValue(content, BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-渠道号:{},手机号:{},异常:", cmsCrackConfig.getLogTag(), channel, phone, e);
            return BillingResult.fail();
        }
    }

    /**
     * 请求短信验证码
     *
     * @param phone   手机号
     * @param channel 渠道号
     * @param addDepartment 是否新建部门 1=新建部门 0=加入部门
     * @param departmentName 部门名称  addDepartment=1时必传
     * @param departmentId  部门id addDepartment=0时必传
     * @param defSeq 透传参数
     * @param qyclCompanyOwner
     * @return
     */
    public @Nonnull
    BillingResult getSmsQycl(String phone, String channel, int addDepartment, String departmentName, String departmentId, String defSeq, String qyclCompanyOwner) {


//        EnterpriseVrbtConfig enterpriseVrbtConfig =  enterpriseVrbtProperties.getEnterpriseVrbtConfigByCompanyOwner(qyclCompanyOwner);


        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfig(channel,qyclCompanyOwner);
        if (portCrackConfig == null) {
            log.info("获取短信(企业视频彩铃)-配置错误-渠道号:{},手机号:{},addDepartment:{},departmentName:{},departmentId:{},defSeq:{}", channel, phone, addDepartment, departmentName, departmentId, defSeq);
            return BillingResult.fail();
        }
        EnterpriseVrbtConfig enterpriseVrbtConfig= null;
        try {
            enterpriseVrbtConfig = mapper.readValue(portCrackConfig.getRemark(), EnterpriseVrbtConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        //如果是新建部门,就规范化部门称
        if(addDepartment==1) {
            departmentName = departmentName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
            departmentName = StringUtils.substring(departmentName, 0, 19) + phone;
        }

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(yidongVrbtCrackProperties.getGetSmsUrl())
                .newBuilder()
            .addQueryParameter("paycode", cmsCrackConfig.getPayCode())
            .addQueryParameter("fee", cmsCrackConfig.getFee())
            .addQueryParameter("cpid", cmsCrackConfig.getCpid())
                .addQueryParameter("phone", phone)
                .addQueryParameter("addDepartment", String.valueOf(addDepartment))
                .addQueryParameter("departmentName", departmentName)
                .addQueryParameter("departmentId", Strings.nullToEmpty(departmentId))
                .addQueryParameter("cpparm", defSeq)
                .addQueryParameter("adminMsisdn", enterpriseVrbtConfig.getAdminMsisdn())
                .addQueryParameter("firstExtendedField", enterpriseVrbtConfig.getContactPhone())
                .addQueryParameter("ib", "1")
                .addQueryParameter("fmt", "json")
                .build();
        log.info("{}-获取短信(企业视频彩铃)-渠道号:{},手机号:{},addDepartment:{},departmentName:{},departmentId:{},defSeq:{},请求:{}", cmsCrackConfig.getLogTag(), channel, phone, addDepartment, departmentName, departmentId, defSeq, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信(企业视频彩铃)-渠道号:{},手机号:{},addDepartment:{},departmentName:{},departmentId:{},defSeq:{},响应:{}", cmsCrackConfig.getLogTag(), channel, phone, addDepartment, departmentName, departmentId, defSeq, content);
            return mapper.readValue(content, BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信(企业视频彩铃)-渠道号:{},手机号:{},addDepartment:{},departmentName:{},departmentId:{},defSeq:{},异常:", cmsCrackConfig.getLogTag(), channel, phone, addDepartment, departmentName, departmentId, defSeq, e);
            return BillingResult.fail();
        }
    }

    /**
     * 提交短信验证码
     *
     * @param orderId
     * @param smsCode
     * @return
     */
    public @Nonnull
    BillingResult smsCode(String orderId, String smsCode, String channel, String mobile) {
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(yidongVrbtCrackProperties.getSmsValidUrl())
                .newBuilder()
            .addQueryParameter("type", cmsCrackConfig.getType())
                .addQueryParameter("transId", orderId)
                .addQueryParameter("smsCode", smsCode)
            .addQueryParameter("paycode", cmsCrackConfig.getPayCode())
                .build();
        log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},短信验证码:{},请求:{}", cmsCrackConfig.getLogTag(), channel, orderId, mobile, smsCode, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},响应:{}", cmsCrackConfig.getLogTag(), channel, orderId, mobile, content);
            return mapper.readValue(content, BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},异常:", cmsCrackConfig.getLogTag(), channel, orderId, mobile, e);
            return BillingResult.fail();
        }
    }

    /**
     * 请求短信验证码
     *
     * @param phone   手机号
     * @param channel 渠道号
     * @param addDepartment 是否新建部门 1=新建部门 0=加入部门
     * @param departmentName 部门名称  addDepartment=1时必传
     * @return
     */
    public @Nonnull
    BillingResult getSmsZyhlQycl(String phone, String channel) {

        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(yidongVrbtCrackProperties.getGetSmsUrl())
                .newBuilder()
            .addQueryParameter("paycode", cmsCrackConfig.getPayCode())
            .addQueryParameter("fee", cmsCrackConfig.getFee())
            .addQueryParameter("cpid", cmsCrackConfig.getCpid())
                .addQueryParameter("phone", phone)
                .addQueryParameter("addDepartment", "1")
                .addQueryParameter("departmentName", RandomStringUtils.randomAlphabetic(6) + phone)
                .addQueryParameter("adminMsisdn", "15196602239")
                .addQueryParameter("firstExtendedField", "4001129488")
                .addQueryParameter("ib", "1")
                .addQueryParameter("fmt", "json")
                .build();
        log.info("{}-获取短信(企业视频彩铃)-渠道号:{},手机号:{},请求:{}", cmsCrackConfig.getLogTag(), channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信(企业视频彩铃)-渠道号:{},手机号:{},响应:{}", cmsCrackConfig.getLogTag(), channel, phone, content);
            return mapper.readValue(content, BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信(企业视频彩铃)-渠道号:{},手机号:{},异常:", cmsCrackConfig.getLogTag(), channel, phone, e);
            return BillingResult.fail();
        }
    }
}
