package com.eleven.cms.remote;
import com.eleven.cms.config.YunnanYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * @author: lihb
 * @create: 2022-9-20 11:40:24
 */
@Slf4j
@Service
public class YunnanYidongService {

    public static final String LOG_TAG = "云南移动流量包业务api";
    public static final String YN_CITY_CODE_KEY_PREFIX = "yunnan::cityCode:";
    public static final long YN_CITY_CODE_CACHE_SECONDS = 600;
    @Autowired
    private YunnanYidongProperties yunnanYidongProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");

    public final static Map cityCodeMap = new HashMap() {{
        put("昆明", "0871");
        put("临沧", "0883");
        put("曲靖", "0874");
        put("文山", "0876");
        put("德宏", "0692");
        put("迪庆", "0887");
        put("普洱", "0879");
        put("楚雄", "0878");
        put("玉溪", "0877");
        put("怒江", "0886");
        put("红河", "0873");
        put("丽江", "0888");
        put("昭通", "0870");
        put("西双版纳", "0691");
        put("保山", "0875");
        put("大理", "0872");
    }};

    public static final String YN_AUTH_COMPONENT_KEY_PREFIX = "yn::authComponent:";
    public static final long YN_AUTH_COMPONENT_CACHE_SECONDS = 300;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 营销活动校验
     * phone 手机号
     * channel 渠道号
     * serialNumber 流水号
     * @param phone
     * @return
     */
    public YunnanMobileCheckSaleActiveResult checkSaleActive(String phone, String channel,String tradeEparchyCode) {
        try {
            YunnanYidongProperties.YunnanYidongConfig config = yunnanYidongProperties.getChannelConfig().get(channel);
            if(null==config){
                log.info("{}-营销活动校验-手机号:{},渠道号:{},渠道号配置不存在", LOG_TAG, phone,channel);
                return YunnanMobileCheckSaleActiveResult.fail();
            }

            ObjectNode content = mapper.createObjectNode();
            //业务参数
            content.put("ACCESS_NUM", phone);
            content.put("CAMPN_ID", "0");
            content.put("OFFER_ID", config.getProductCode());

            ObjectNode param = config.formatParam(mapper,phone, tradeEparchyCode,content);

            String url = config.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(config.getScenarioCode())
                    .addQueryParameter("abilityCode","YUNN_UNHQ_checkSaleActive")
                    .build();
            RequestBody body = RequestBody.create(mediaType, param.toString());
            Request request = new Request.Builder().url(httpUrl)
                    //.addHeader("Content-Type","text/json;charset=UTF-8")
                    .post(body)
                    .build();
            log.info("{}-营销活动校验-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-营销活动校验-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            YunnanMobileCheckSaleActiveResult yunnanMobileCheckSaleActiveResult = mapper.readValue(result, YunnanMobileCheckSaleActiveResult.class);
            return yunnanMobileCheckSaleActiveResult;
        } catch (Exception e) {
            log.info("{}-营销活动校验-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileCheckSaleActiveResult.fail();

        }
    }

    /**
     * 验证码下发
     * phone 手机号
     * channel 渠道号
     * serialNumber 流水号
     *
     * @param phone
     * @return
     */
    public YunnanMobileCheckSaleActiveResult getSms(String phone, String channel, String tradeEparchyCode) {
        try {
            YunnanYidongProperties.YunnanYidongConfig config = yunnanYidongProperties.getChannelConfig().get(channel);
            if(null==config){
                log.info("{}-验证码下发-手机号:{},渠道号:{},渠道号配置不存在", LOG_TAG, phone,channel);
                return YunnanMobileCheckSaleActiveResult.fail();
            }

            ObjectNode content = mapper.createObjectNode();
            //业务参数
            content.put("ACCESS_NUM", phone);
            content.put("CAMPN_ID", "0");
            content.put("OFFER_ID", config.getProductCode());

            ObjectNode param = config.formatParam(mapper,phone,tradeEparchyCode,content);

            String url = config.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(config.getScenarioCode())
                    .addQueryParameter("abilityCode","YUNN_UNHQ_sendSingleSms2Q")
                    .build();
            RequestBody body = RequestBody.create(mediaType, param.toString());
            Request request = new Request.Builder().url(httpUrl)
                    //.addHeader("Content-Type","text/json;charset=UTF-8")
                    .post(body)
                    .build();
            log.info("{}-验证码下发-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-验证码下发-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            YunnanMobileCheckSaleActiveResult yunnanMobileCheckSaleActiveResult = mapper.readValue(result, YunnanMobileCheckSaleActiveResult.class);
            return yunnanMobileCheckSaleActiveResult;
        } catch (Exception e) {
            log.info("{}-验证码下发-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileCheckSaleActiveResult.fail();

        }
    }

    /**
     * 业务提交
     * phone 手机号
     * channel 渠道号
     * serialNumber 流水号
     * @param phone
     * @return
     */
    public YunnanMobileCheckSaleActiveResult saleActiveIntfOrder(String phone, String channel,String city,String authInstanceToken) {
        try {
            YunnanYidongProperties.YunnanYidongConfig config = yunnanYidongProperties.getChannelConfig().get(channel);
            if(null==config){
                log.info("{}-业务提交-手机号:{},渠道号:{},渠道号配置不存在", LOG_TAG, phone,channel);
                return YunnanMobileCheckSaleActiveResult.fail();
            }

            ObjectNode content = mapper.createObjectNode();
            String authcomponentkey = YN_AUTH_COMPONENT_KEY_PREFIX + phone;
            YunnanMobileQrymatchAuthSceneResult qrymatchAuthSceneResult = (YunnanMobileQrymatchAuthSceneResult) redisUtil.get(authcomponentkey);
            if(qrymatchAuthSceneResult == null){
                return YunnanMobileCheckSaleActiveResult.fail();
            }
            //业务参数
            content.put("ACCESS_NUM", phone);
            content.put("CAMPN_ID", "0");
            content.put("OFFER_ID", config.getProductCode());
            content.put("AUTH_INSTANCE_TOKEN", authInstanceToken);
            content.put("AUTH_INSTANCE_ID", qrymatchAuthSceneResult.getResult().getAuthInstanceId());

            ObjectNode param = config.formatParam(mapper,phone, getTradeEparchyCode(phone,channel,city),content);
            
            String url = config.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(config.getScenarioCode())
                    .addQueryParameter("abilityCode","YUNN_UNHT_saleActiveIntfOrder")
                    .build();
            RequestBody body = RequestBody.create(mediaType, param.toString());
            Request request = new Request.Builder().url(httpUrl)
                    //.addHeader("Content-Type","text/json;charset=UTF-8")
                    .post(body)
                    .build();
            log.info("{}-业务提交-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-业务提交-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            YunnanMobileCheckSaleActiveResult yunnanMobileCheckSaleActiveResult = mapper.readValue(result, YunnanMobileCheckSaleActiveResult.class);
            return yunnanMobileCheckSaleActiveResult;
        } catch (Exception e) {
            log.info("{}-业务提交-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileCheckSaleActiveResult.fail();

        }
    }

    /**
     * 全国号码归属地查询
     * phone 手机号
     * channel 渠道号
     * @return
     */
    public YunnanMobileChechIsYnydNumberResult chechIsYnydNumber(String phone, String channel) {
        try {
            YunnanYidongProperties.YunnanYidongConfig config = yunnanYidongProperties.getChannelConfig().get(channel);
            if(null==config){
                log.info("{}-全国号码归属地查询-手机号:{},渠道号:{},渠道号配置不存在", LOG_TAG, phone,channel);
                return YunnanMobileChechIsYnydNumberResult.fail();
            }

            ObjectNode content = mapper.createObjectNode();
            //业务参数
            content.put("ACCESS_NUM", phone);
            content.put("CAMPN_ID", "0");
            content.put("OFFER_ID", config.getProductCode());

            ObjectNode param = config.formatParam(mapper,phone, "",content);

            String url = config.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(config.getScenarioCode())
                    .addQueryParameter("abilityCode","YUNN_UNHT_chechIsYnydNumber")
                    .build();
            RequestBody body = RequestBody.create(mediaType, param.toString());
            Request request = new Request.Builder().url(httpUrl)
                    //.addHeader("Content-Type","text/json;charset=UTF-8")
                    .post(body)
                    .build();
            log.info("{}-全国号码归属地查询-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-全国号码归属地查询-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);

            YunnanMobileChechIsYnydNumberResult yunnanMobileChechIsYnydNumberResult = mapper.readValue(result, YunnanMobileChechIsYnydNumberResult.class);
            return yunnanMobileChechIsYnydNumberResult;
        } catch (Exception e) {
            log.info("{}-全国号码归属地查询-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileChechIsYnydNumberResult.fail();
        }
    }

    public String getTradeEparchyCode(String phone, String channel,String city) {
        String ynCityCodeKey = YN_CITY_CODE_KEY_PREFIX + phone;
        String tradeEparchyCode = redisUtil.get(ynCityCodeKey) == null ? null : redisUtil.get(ynCityCodeKey).toString();
        if(StringUtils.isNotBlank(tradeEparchyCode)){
            return tradeEparchyCode;
        }
        YunnanMobileChechIsYnydNumberResult yunnanMobileChechIsYnydNumberResult = chechIsYnydNumber(phone, channel);
        if(yunnanMobileChechIsYnydNumberResult.isOK()
                && yunnanMobileChechIsYnydNumberResult.getResult() != null
                && StringUtils.isNotBlank(yunnanMobileChechIsYnydNumberResult.getResult().getRegionId())){
            tradeEparchyCode = yunnanMobileChechIsYnydNumberResult.getResult().getRegionId();
            redisUtil.set(ynCityCodeKey, tradeEparchyCode, YN_CITY_CODE_CACHE_SECONDS);
        }
        if(StringUtils.isBlank(tradeEparchyCode)){
            tradeEparchyCode = cityCodeMap.get(city) == null ? "" : cityCodeMap.get(city).toString();
        }
        return tradeEparchyCode;
    }

    /**
     * 鉴权查询
     * phone 手机号
     * channel 渠道号
     * @return
     */
    public YunnanMobileQrymatchAuthSceneResult qrymatchAuthScene(String phone, String channel, String tradeEparchyCode) {
        try {
            YunnanYidongProperties.YunnanYidongConfig config = yunnanYidongProperties.getChannelConfig().get(channel);
            if(null==config){
                log.info("{}-鉴权查询-手机号:{},渠道号:{},渠道号配置不存在", LOG_TAG, phone,channel);
                return YunnanMobileQrymatchAuthSceneResult.fail();
            }

            ObjectNode content = mapper.createObjectNode();
            //业务参数
            content.put("AUTH_SRC_TYPE", "DG");
            content.put("appId", config.getAppId());
            content.put("AUTHENTICATION_METHODS", "YUNN_UNHT_saleActiveIntfOrder");
            content.put("CAMPN_ID", "0");
            content.put("OFFER_ID", config.getProductCode());
            content.put("BUSI_ITEM_CODE", "240");

            ObjectNode param = config.formatParam(mapper,phone, tradeEparchyCode,content);

            String url = config.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(config.getScenarioCode())
                    .addQueryParameter("abilityCode","YUNN_UNHQ_qrymatchAuthScene")
                    .build();
            RequestBody body = RequestBody.create(mediaType, param.toString());
            Request request = new Request.Builder().url(httpUrl)
                    //.addHeader("Content-Type","text/json;charset=UTF-8")
                    .post(body)
                    .build();
            log.info("{}-鉴权查询-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-鉴权查询-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);

            YunnanMobileQrymatchAuthSceneResult yunnanMobileQrymatchAuthSceneResult = mapper.readValue(result, YunnanMobileQrymatchAuthSceneResult.class);
            //参数存入redis，提交短信验证码后会用到
            if(yunnanMobileQrymatchAuthSceneResult.isOK()
                    && yunnanMobileQrymatchAuthSceneResult.getResult() != null
                    && yunnanMobileQrymatchAuthSceneResult.getResult().isOK()){
                String authcomponentkey = YN_AUTH_COMPONENT_KEY_PREFIX + phone;
                redisUtil.set(authcomponentkey, yunnanMobileQrymatchAuthSceneResult, YN_AUTH_COMPONENT_CACHE_SECONDS);
            }
            return yunnanMobileQrymatchAuthSceneResult;
        } catch (Exception e) {
            log.info("{}-鉴权查询-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileQrymatchAuthSceneResult.fail();
        }
    }

    /**
     * 下发验证码
     * phone 手机号
     * channel 渠道号
     * @return
     */
    public YunnanMobileSendAuthSmsCodeResult sendAuthSmsCode(String phone, String channel, String tradeEparchyCode, YunnanMobileQrymatchAuthSceneResult qrymatchAuthSceneResult) {
        try {
            YunnanMobileQrymatchAuthSceneResult.Result.AuthComponent component = null;
            YunnanYidongProperties.YunnanYidongConfig config = yunnanYidongProperties.getChannelConfig().get(channel);
            if(null==config){
                log.info("{}-下发验证码-手机号:{},渠道号:{},渠道号配置不存在", LOG_TAG, phone,channel);
                return YunnanMobileSendAuthSmsCodeResult.fail();
            }

            ObjectNode content = mapper.createObjectNode();
            //业务参数
            List<YunnanMobileQrymatchAuthSceneResult.Result.AuthComponent> authComponentList = qrymatchAuthSceneResult.getResult().getAuthComponents();
            for (YunnanMobileQrymatchAuthSceneResult.Result.AuthComponent authComponent : authComponentList) {
                if(authComponent.getComponentId().equals("authSmsCode")){
                    component = authComponent;
                }
            }
            if(component == null){
                component = authComponentList.get(0);
            }
            content.put("AUTH_INSTANCE_ID", qrymatchAuthSceneResult.getResult().getAuthInstanceId());
            content.put("AUTH_METHOD_ID", component.getAuthMethodId());
            content.put("AUTH_METHOD_CODE", component.getAuthMethodCode());
            content.put("ACCESS_NUM", phone);
            content.put("BUSI_SMS_FLAG", "1");
            content.put("CAMPN_ID", "0");
            content.put("OFFER_ID", config.getProductCode());
            content.put("BUSI_ITEM_CODE", "240");

            ObjectNode param = config.formatParam(mapper,phone, tradeEparchyCode,content);

            String url = config.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(config.getScenarioCode())
                    .addQueryParameter("abilityCode","YUNN_UNHT_sendAuthSmsCode")
                    .build();
            RequestBody body = RequestBody.create(mediaType, param.toString());
            Request request = new Request.Builder().url(httpUrl)
                    //.addHeader("Content-Type","text/json;charset=UTF-8")
                    .post(body)
                    .build();
            log.info("{}-下发验证码-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-下发验证码-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);

            YunnanMobileSendAuthSmsCodeResult yunnanMobileSendAuthSmsCodeResult = mapper.readValue(result, YunnanMobileSendAuthSmsCodeResult.class);
            return yunnanMobileSendAuthSmsCodeResult;
        } catch (Exception e) {
            log.info("{}-下发验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileSendAuthSmsCodeResult.fail();
        }
    }

    /**
     * 鉴权
     * phone 手机号
     * channel 渠道号
     * @return
     */
    public YunnanMobileAuthenticationResult authentication(String phone, String channel, String smsCode, String tradeEparchyCode, String authSmsCodeSendFlag) {
        try {
            YunnanMobileQrymatchAuthSceneResult.Result.AuthComponent authSmsCodeComponent = null;
            YunnanMobileQrymatchAuthSceneResult.Result.AuthComponent twoSmsCheckComponent = null;
            List<ObjectNode> list = new ArrayList<>();
            ObjectNode twoSmsCheckMap = mapper.createObjectNode();
            ObjectNode authSmsCodeMap = mapper.createObjectNode();

            YunnanYidongProperties.YunnanYidongConfig config = yunnanYidongProperties.getChannelConfig().get(channel);
            if(null==config){
                log.info("{}-鉴权-手机号:{},渠道号:{},短信验证码:{},渠道号配置不存在", LOG_TAG, phone,channel,smsCode);
                return YunnanMobileAuthenticationResult.fail();
            }

            String authcomponentkey = YN_AUTH_COMPONENT_KEY_PREFIX + phone;
            YunnanMobileQrymatchAuthSceneResult qrymatchAuthSceneResult = (YunnanMobileQrymatchAuthSceneResult) redisUtil.get(authcomponentkey);
            if(qrymatchAuthSceneResult == null){
                return YunnanMobileAuthenticationResult.fail();
            }
            HashMap<String, Object> content = new HashMap<>();
            //业务参数
            List<YunnanMobileQrymatchAuthSceneResult.Result.AuthComponent> authComponentList = qrymatchAuthSceneResult.getResult().getAuthComponents();
            for (YunnanMobileQrymatchAuthSceneResult.Result.AuthComponent authComponent : authComponentList) {
                if(authComponent.getComponentId().equals("authSmsCode")){
                    authSmsCodeComponent = authComponent;
                }else{
                    twoSmsCheckComponent = authComponent;
                }
            }
            if(authSmsCodeComponent == null){
                authSmsCodeComponent = authComponentList.get(0);
            }
            if(twoSmsCheckComponent == null){
                twoSmsCheckComponent = authComponentList.get(1);
            }
            twoSmsCheckMap.put("AUTH_METHOD_CODE","APPID_SMS_TWO_CHECK");
            twoSmsCheckMap.put("AUTH_METHOD_ID",twoSmsCheckComponent.getAuthMethodId());
            twoSmsCheckMap.put("IS_CHECKED","0");
            twoSmsCheckMap.put("ACCESS_NUM",phone);
            list.add(twoSmsCheckMap);
            authSmsCodeMap.put("AUTH_METHOD_CODE","APPID_SMS_CODE");
            authSmsCodeMap.put("AUTH_METHOD_ID",authSmsCodeComponent.getAuthMethodId());
            authSmsCodeMap.put("IS_CHECKED","1");
            authSmsCodeMap.put("ACCESS_NUM",phone);
            authSmsCodeMap.put("AUTH_SMS_CODE",smsCode);
            authSmsCodeMap.put("AUTH_SMS_CODE_SEND_FLAG",authSmsCodeSendFlag);
            list.add(authSmsCodeMap);
            content.put("AUTH_OBJ_NUM", phone);
            content.put("AUTH_OBJ_TYPE", "PERSON");
            content.put("AUTH_INSTANCE_ID", qrymatchAuthSceneResult.getResult().getAuthInstanceId());
            content.put("CAMPN_ID","0");
            content.put("OFFER_ID",config.getProductCode());
            content.put("AUTH_SUBMIT_DATA", list);

            ObjectNode param = config.formatParam(mapper,phone, tradeEparchyCode,mapper.valueToTree(content));

            String url = config.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(config.getScenarioCode())
                    .addQueryParameter("abilityCode","YUNN_UNHT_authentication")
                    .build();
            RequestBody body = RequestBody.create(mediaType, param.toString());
            Request request = new Request.Builder().url(httpUrl)
                    //.addHeader("Content-Type","text/json;charset=UTF-8")
                    .post(body)
                    .build();
            log.info("{}-鉴权-手机号:{},渠道号:{},短信验证码:{},请求:{}", LOG_TAG, phone,channel,smsCode, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-鉴权-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", LOG_TAG, phone,channel,smsCode, result);

            YunnanMobileAuthenticationResult yunnanMobileAuthenticationResult = mapper.readValue(result, YunnanMobileAuthenticationResult.class);
            return yunnanMobileAuthenticationResult;
        } catch (Exception e) {
            log.info("{}-鉴权-手机号:{},渠道号:{},短信验证码:{},异常:", LOG_TAG, phone,channel,smsCode, e);
            return YunnanMobileAuthenticationResult.fail();
        }
    }



}
