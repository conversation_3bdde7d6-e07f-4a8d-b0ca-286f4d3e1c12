package com.eleven.cms.remote;

import cn.hutool.crypto.SecureUtil;
import com.eleven.cms.config.CaixunProperties;
import com.eleven.cms.config.ShoujizixunConfig;
import com.eleven.cms.config.ShoujizixunProperties;
import com.eleven.cms.es.util.IdUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.CaixunResult;
import com.eleven.cms.vo.ShoujizixunResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.hibernate.annotations.Cache;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.Instant;
import java.util.Arrays;

/**
 * 爱豆来电接口
 *
 * @author: cai lei
 * @create: 2023-11-13 10:28
 */
@Slf4j
@Service
public class CaixunService {

    private OkHttpClient client;
    private ObjectMapper mapper;

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    public static final String LOG_TAG = "彩讯呼叫秀API";

    @Autowired
    private CaixunProperties caixunProperties;
    @Autowired
    private Environment environment;


//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    }


    public String getToken(String channel) {
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("appid", caixunProperties.getAppId());
        dataNode.put("timeStamp", timestamp);
        dataNode.put("channelCode", caixunProperties.getChannelCodeMap().get(channel));
        dataNode.put("verifyKey", SecureUtil.md5(caixunProperties.getChannelCodeMap().get(channel) + caixunProperties.getKey() + timestamp).toUpperCase());
        log.info("{}-获取token-渠道号:{},请求:{}", LOG_TAG, channel, caixunProperties.getGetSmsCodeUrl());
        Request request = new Request.Builder().url(caixunProperties.getGetTokenUrl()).post(RequestBody.create(JSON, dataNode.toString())).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取token-渠道号:{},响应:{}", LOG_TAG, channel, content);
            JsonNode jsonNode = mapper.readTree(content);
            return "200".equals(jsonNode.at("/retCode").asText()) ? jsonNode.at("/data/jsToken").asText() : null;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取token-渠道号:{},异常:", LOG_TAG, channel, e);
            return null;
        }
    }

    public CaixunResult getSms(String mobile, String channel, String userAgent, String reportTicket) {
        String guid = RandomStringUtils.randomAlphanumeric(32);
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("guid", guid);
        dataNode.put("appid", caixunProperties.getAppId());
        dataNode.put("timeStamp", timestamp);
        dataNode.put("verifyKey", SecureUtil.md5(guid + mobile + caixunProperties.getKey() + timestamp).toUpperCase());
        dataNode.put("channelCode", caixunProperties.getChannelCodeMap().get(channel));
        dataNode.put("channelSource", caixunProperties.getChannelSourceMap().get(channel));
        dataNode.put("mobile", mobile);
        dataNode.put("userAgent", userAgent);
        dataNode.put("reportTicket", reportTicket);
        log.info("{}-获取短信-手机号:{},渠道号:{},请求:{}", LOG_TAG, mobile, channel, caixunProperties.getGetSmsCodeUrl());
        Request request = new Request.Builder().url(caixunProperties.getGetSmsCodeUrl()).post(RequestBody.create(JSON, dataNode.toString())).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},渠道号:{},响应:{}", LOG_TAG, mobile, channel, content);
            return mapper.readValue(content, CaixunResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},渠道号:{},异常:", LOG_TAG, mobile, channel, e);
            return CaixunResult.fail();
        }
    }

    public CaixunResult smsCode(String mobile, String channel, String guid, String orderId, String code, String userAgent, String token, String reportTicket) {
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("guid", guid);
        dataNode.put("appid", caixunProperties.getAppId());
        dataNode.put("timeStamp", timestamp);
        dataNode.put("verifyKey", SecureUtil.md5(guid + mobile + caixunProperties.getKey() + timestamp).toUpperCase());
        dataNode.put("channelCode", caixunProperties.getChannelCodeMap().get(channel));
        dataNode.put("channelSource", caixunProperties.getChannelSourceMap().get(channel));
        dataNode.put("mobile", mobile);
        dataNode.put("orderId", orderId);
        dataNode.put("code", code);
        dataNode.put("userAgent", userAgent);
        dataNode.put("token", token);
        dataNode.put("reportTicket", reportTicket);
        log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},请求:{}", LOG_TAG, mobile, code, channel, caixunProperties.getSmsCodeUrl());
        Request request = new Request.Builder().url(caixunProperties.getSmsCodeUrl()).post(RequestBody.create(JSON, dataNode.toString())).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},响应:{}", LOG_TAG, mobile, code, channel, content);
            return mapper.readValue(content, CaixunResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},异常:", LOG_TAG, mobile, code, channel, e);
            return CaixunResult.fail();
        }
    }
}
