package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.*;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.guizhou.AesEncryptUtil;
import com.eleven.cms.util.guizhou.MD5Util;
import com.eleven.cms.util.guizhou.Md5Utils;
import com.eleven.cms.util.guizhou.RSA2Signer;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @author: lihb
 * @create: 2023-5-25 14:08:32
 * Desc: 触点（支付能力）相关接口
 */
@Slf4j
@Service
public class WoReadApiService {

    public static final String LOG_TAG = "沃悦读支付业务api";

    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private WoReadNodeProperties woReadNodeProperties;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");
    public static final String SOURCE_CODE = "11";


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 10086))).build();
        }
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().addNetworkInterceptor(new CurlInterceptor(
                message -> System.out.println(message))).build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }





    /**
     * (非微信容器）微信预订购接口
     * mobile
     * openId
     * ip
     * @return
     */
    public WoReadWxBeforeOrderResult wxBeforeorder(String mobile,String ip,String contractCode,String businessType,String company){

        try{
            WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfig(company);
            WoReadWxProperties woReadWxProperties = woReadProperties.getWx();
            Map<String, Object> map = new HashMap<>();
            //构建请求body参数
            map.put("productpkgid", woReadProperties.getProductpkgid());//必传，套餐索引
            map.put("body",woReadWxProperties.getBody());//必传，产品名
            map.put("attach",woReadWxProperties.getAttach());//必传，附加信息
            map.put("notify_url",woReadWxProperties.getWxNotifyUrl());//必传，通知url
            map.put("trade_type",woReadWxProperties.getTradeType());//必传，交易类型JSAPI,NATIVE,APP,MWEB
//            map.put("quantity",1);//选传，月数，默认1
//            map.put("fee",Double.parseDouble(singleAmount)*100);//选传，总金额，单位分
            map.put("isOpenContinuePackage","1");//是否连续订购 1连续订购,为1时必须传签约相关字段
            map.put("plan_id",woReadWxProperties.getPlanId());//协议模板id,连续订购时必传
            map.put("contract_code",contractCode);//签约协议号，保持唯一性,连续订购时必传
            map.put("contract_display_account",mobile);//签约用户的名称,用于页面展示,连续订购时必传
            map.put("callbackurl",woReadWxProperties.getWxCallbackurl()+"?serviceId="+businessType);//选填，支付完成后的页面跳转地址
            map.put("ip",ip);//订购用户终端ip

            String url = woReadProperties.getWxBeforeorderUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(SOURCE_CODE)
                    .addPathSegment(mobile)
                    .addPathSegment(woReadWxProperties.getChannelId())
                    .build();
            log.info("{}-微信预订购接口,手机号:{},body参数:{}", LOG_TAG, mobile,mapper.writeValueAsString(map));
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(map));
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("AuthorizationClient",getToken(woReadProperties))
                    .post(body)
                    .build();
            log.info("{}-微信预订购接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-微信预订购接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
                WoReadWxBeforeOrderResult woReadWxBeforeOrderResult = mapper.readValue(result, WoReadWxBeforeOrderResult.class);
                JsonNode jsonNode = mapper.readTree(result);
                String message = jsonNode.at("/message").toString();
                if(woReadWxBeforeOrderResult.isOK()){
                    woReadWxBeforeOrderResult.setData(mapper.readValue(message,WoReadWxBeforeOrderResult.WoReadWxBeforeOrderMessage.class));
                }else{
                    woReadWxBeforeOrderResult.setResMessage(message);
                }
                return woReadWxBeforeOrderResult;
            }
        }catch (Exception e){
            log.info("{}-微信预订购接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadWxBeforeOrderResult.fail();
        }
    }

    /**
     * H5支付宝预订购接口
     * mobile
     * ip
     * @return
     */
    public WoReadAliBeforeOrderResult aliBeforeorder(String mobile,String ip,String businessType,String company){

        try{
            WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfig(company);
            WoReadAliProperties woReadAliProperties = woReadProperties.getAli();
            Map<String, Object> map = new HashMap<>();
            //构建请求body参数
            map.put("productpkgid",woReadProperties.getProductpkgid());//必传，套餐索引
            map.put("body",woReadAliProperties.getBody());//必传，产品名
            map.put("notify_url",woReadAliProperties.getAliNotifyUrl());//必传，通知url
            map.put("isOpenContinuePackage","1");//是否连续订购 1连续订购,为1时必须传签约相关字段
//            map.put("quantity",1);//选传，月数，默认1
//            map.put("fee",Double.parseDouble(singleAmount)*100);//选传，总金额，单位分
            map.put("ip",ip);//订购用户终端ip
            map.put("returnurl",woReadAliProperties.getAliReturnUrl()+"?serviceId="+businessType);//必传，回跳页面地址

            String url = woReadProperties.getAliBeforeorderUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(SOURCE_CODE)
                    .addPathSegment(mobile)
                    .addPathSegment(woReadAliProperties.getChannelId())
                    .build();
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(map));
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("AuthorizationClient",getToken(woReadProperties))
                    .post(body)
                    .build();
            log.info("{}-H5支付宝预订购接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-H5支付宝预订购接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
                WoReadAliBeforeOrderResult woReadAliBeforeOrderResult = mapper.readValue(result, WoReadAliBeforeOrderResult.class);
                JsonNode jsonNode = mapper.readTree(result);
                String message = jsonNode.at("/message").toString();
                if(woReadAliBeforeOrderResult.isOK()){
                    woReadAliBeforeOrderResult.setData(mapper.readValue(message,WoReadAliBeforeOrderResult.WoReadAliBeforeOrderMessage.class));
                }else{
                    woReadAliBeforeOrderResult.setResMessage(message);
                }
                return woReadAliBeforeOrderResult;
            }
        }catch (Exception e){
            log.info("{}-H5支付宝预订购接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadAliBeforeOrderResult.fail();
        }
    }

    /**
     * 获取用户包月产品的订购状态
     * mobile
     * @return
     */
    public WoReadPkgOrderedStatusResult getPkgOrderedStatus(String mobile,String channelId){

        WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfigByChannelId(channelId);
        String url = woReadProperties.getPkgOrderedStatusUrl();
        HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .addPathSegment(SOURCE_CODE)
                .addPathSegment(channelId)
                .addPathSegment(mobile)
                .addPathSegment(woReadProperties.getProductpkgid())
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .addHeader("Content-Type","application/json")
                .addHeader("AuthorizationClient",getToken(woReadProperties))
                .build();
        log.info("{}-获取用户包月产品的订购状态接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取用户包月产品的订购状态接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
            WoReadPkgOrderedStatusResult woReadPkgOrderedStatusResult = mapper.readValue(result, WoReadPkgOrderedStatusResult.class);
            JsonNode jsonNode = mapper.readTree(result);
            String message = jsonNode.at("/message").toString();
            if(woReadPkgOrderedStatusResult.isOK()){
                woReadPkgOrderedStatusResult.setData(mapper.readValue(message, WoReadPkgOrderedStatusResult.WoReadPkgOrderedStatusMessage.class));
            }else{
                woReadPkgOrderedStatusResult.setResMessage(message);
            }
            return woReadPkgOrderedStatusResult;
        } catch (Exception e) {
            log.info("{}-获取用户包月产品的订购状态接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadPkgOrderedStatusResult.fail();
        }
    }

    /**
     * 包月退订接口
     * mobile
     * @return
     */
    public WoReadUnsubscribeResult unsubscribe(String mobile,String channelId){

        try {
            WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfigByChannelId(channelId);
            String url = woReadProperties.getUnsubscribeUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(SOURCE_CODE)
                    .addPathSegment(mobile)
                    .addQueryParameter("channelid",channelId)
                    .addQueryParameter("productid",woReadProperties.getProductpkgid())
                    .addQueryParameter("reason","退订")
                    .addQueryParameter("reasoncode","unsubscribe")
                    .addQueryParameter("remarks","退订")
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("AuthorizationClient",getToken(woReadProperties))
                    .build();
            log.info("{}-包月退订接口接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-包月退订接口接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
                WoReadUnsubscribeResult woReadUnsubscribeResult = mapper.readValue(result, WoReadUnsubscribeResult.class);
                return woReadUnsubscribeResult;
            }
        }catch (Exception e){
            log.info("{}-包月退订接口接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadUnsubscribeResult.fail();
        }
    }

    /**
     * 获取token,从Redis获取，获取不到就调用接口
     * @param
     * @return
     */
    public String getToken(WoReadProperties woReadProperties){

        String token = (String) redisUtil.get("woRead-token-" + woReadProperties.getClientId());
        if(StringUtils.isEmpty(token)){
            try {
                String url = woReadProperties.getTokenUrl();
                HttpUrl httpUrl = HttpUrl.parse(url)
                        .newBuilder()
                        .addQueryParameter("clientSource", SOURCE_CODE)
                        .addQueryParameter("clientId", woReadProperties.getClientId())
                        .addQueryParameter("clientSecret", woReadProperties.getClientSecret())
                        .build();

                log.info("{}-获取token接口-请求:{}", LOG_TAG, httpUrl.toString());
                Request request = new Request.Builder().url(httpUrl)
                        .build();
                try (Response response = client.newCall(request)
                        .execute()) {
                    if (!response.isSuccessful()) {
                        throw new IOException("Unexpected code " + response);
                    }

                    String content = response.body().string();
                    WoReadTokenResult result = mapper.readValue(content, WoReadTokenResult.class);
                    if(result.isOK()){
                        String key = result.getKey();
                        String keyType = result.getKey_type();
                        token = keyType + " " + key;
                        redisUtil.setIfAbsent("woRead-token-" + woReadProperties.getClientId(),token,604000);
                    }
                    log.info("{}-获取token接口-响应:{}", LOG_TAG, content);
                }
            } catch (IOException e) {
                log.warn("{}-获取token接口-异常:", LOG_TAG, e);
            }
        }
        return token;
    }

    /**
     * 一键登录
     * mobile
     * openId
     * ip
     * @return
     */
    public WoReadLoginResult shouTingLogin(String mobile,String company){

        try{
            WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfig(company);

            Map<String, Object> map = new HashMap<>();
            //构建请求body参数
            map.put("phone", mobile);//必传，套餐索引

            String url = woReadProperties.getShouTingLoginUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .build();
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(map));
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("AuthorizationClient",getToken(woReadProperties))
                    .post(body)
                    .build();
            log.info("{}-一键登录接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-一键登录接口接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
                WoReadLoginResult woReadLoginResult = mapper.readValue(result, WoReadLoginResult.class);
                JsonNode jsonNode = mapper.readTree(result);
                String message = jsonNode.at("/message").toString();
                if(woReadLoginResult.isOK()){
                    woReadLoginResult.setData(mapper.readValue(message,WoReadLoginResult.WoReadLoginMessage.class));
                }else{
                    woReadLoginResult.setResMessage(message);
                }
                return woReadLoginResult;
            }
        }catch (Exception e){
            log.info("{}-一键登录接口接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadLoginResult.fail();
        }
    }

    /**
     * 微信预订购接口-单次扣款
     * mobile
     * openId
     * ip
     * @return
     */
    public WoReadWxBeforeOrderResult wxBeforeSinglePay(String mobile,String ip,String businessType,String company){

        try{
            WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfig(company);
            WoReadWxProperties woReadWxProperties = woReadProperties.getWx();
            Map<String, Object> map = new HashMap<>();
            //构建请求body参数
            map.put("productpkgid", woReadProperties.getProductpkgid());//必传，套餐索引
            map.put("body",woReadWxProperties.getBody());//必传，产品名
            map.put("attach",woReadWxProperties.getAttach());//必传，附加信息
            map.put("notify_url",woReadWxProperties.getWxSingleNotifyUrl());//必传，通知url
            map.put("trade_type",woReadWxProperties.getTradeType());//必传，交易类型JSAPI,NATIVE,APP,MWEB
            map.put("isOpenContinuePackage","0");//是否连续订购 1连续订购,为1时必须传签约相关字段
            map.put("callbackurl",woReadWxProperties.getWxCallbackurl()+"?serviceId="+businessType);//选填，支付完成后的页面跳转地址
            map.put("ip",ip);//订购用户终端ip
//            map.put("fee",19.9*100);//选传，总金额，单位分

            String url = woReadProperties.getWxBeforeorderUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(SOURCE_CODE)
                    .addPathSegment(mobile)
                    .addPathSegment(woReadWxProperties.getChannelId())
                    .build();
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(map));
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("AuthorizationClient",getToken(woReadProperties))
                    .post(body)
                    .build();
            log.info("{}-微信预订购接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-微信预订购接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
                WoReadWxBeforeOrderResult woReadWxBeforeOrderResult = mapper.readValue(result, WoReadWxBeforeOrderResult.class);
                JsonNode jsonNode = mapper.readTree(result);
                String message = jsonNode.at("/message").toString();
                if(woReadWxBeforeOrderResult.isOK()){
                    woReadWxBeforeOrderResult.setData(mapper.readValue(message,WoReadWxBeforeOrderResult.WoReadWxBeforeOrderMessage.class));
                }else{
                    woReadWxBeforeOrderResult.setResMessage(message);
                }
                return woReadWxBeforeOrderResult;
            }
        }catch (Exception e){
            log.info("{}-微信预订购接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadWxBeforeOrderResult.fail();
        }
    }
    /**
     * H5支付宝单次扣款
     * mobile
     * ip
     * @return
     */
    public WoReadAliBeforeOrderResult aliBeforeSinglePay(String mobile,String ip,String businessType,String company){

        try{
            WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfig(company);
            WoReadAliProperties woReadAliProperties = woReadProperties.getAli();
            Map<String, Object> map = new HashMap<>();
            //构建请求body参数
            map.put("productpkgid",woReadProperties.getProductpkgid());//必传，套餐索引
            map.put("body",woReadAliProperties.getBody());//必传，产品名
            map.put("notify_url",woReadAliProperties.getAliSingleNotifyUrl());//必传，通知url
            map.put("isOpenContinuePackage","0");//是否连续订购 1连续订购,为1时必须传签约相关字段
//            map.put("quantity",1);//选传，月数，默认1
            map.put("fee",woReadAliProperties.getFee());//选传，总金额，单位分
            map.put("ip",ip);//订购用户终端ip
            map.put("returnurl",woReadAliProperties.getAliReturnUrl()+"?serviceId="+businessType);//必传，回跳页面地址

            String url = woReadProperties.getAliBeforeorderUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(SOURCE_CODE)
                    .addPathSegment(mobile)
                    .addPathSegment(woReadAliProperties.getChannelId())
                    .build();
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(map));
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("AuthorizationClient",getToken(woReadProperties))
                    .post(body)
                    .build();
            log.info("{}-H5支付宝预订购接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-H5支付宝预订购接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
                WoReadAliBeforeOrderResult woReadAliBeforeOrderResult = mapper.readValue(result, WoReadAliBeforeOrderResult.class);
                JsonNode jsonNode = mapper.readTree(result);
                String message = jsonNode.at("/message").toString();
                if(woReadAliBeforeOrderResult.isOK()){
                    woReadAliBeforeOrderResult.setData(mapper.readValue(message,WoReadAliBeforeOrderResult.WoReadAliBeforeOrderMessage.class));
                }else{
                    woReadAliBeforeOrderResult.setResMessage(message);
                }
                return woReadAliBeforeOrderResult;
            }
        }catch (Exception e){
            log.info("{}-H5支付宝预订购接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadAliBeforeOrderResult.fail();
        }
    }

}
