package com.eleven.cms.remote;

import com.eleven.cms.config.FengzhushouSichuanYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.FengzhushouSichuanYidongGetSmsResult;
import com.eleven.cms.vo.FengzhushouSichuanYidongSmsCodeResult;
import com.eleven.cms.vo.HeilongjiangMobileResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 河北移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class FengzhushouSichuanYidongService {

    @Autowired
    private Environment environment;
    @Autowired
    private FengzhushouSichuanYidongProperties fengzhushouSichuanYidongVrbtProperties;

    private static final MediaType mediaType = MediaType.parse("application/json");
    private OkHttpClient client;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    FengzhushouSichuanYidongGetSmsResult getSms(String phone, String ip, String app, String url){
        ObjectNode data = mapper.createObjectNode();
        data.put("mobile", phone);
        data.put("ip", ip);
        data.put("zfCode", fengzhushouSichuanYidongVrbtProperties.getZfCode());
        data.put("platform", app);
        data.put("url", url);
        data.put("pid", fengzhushouSichuanYidongVrbtProperties.getPId());
        RequestBody body = RequestBody.create(mediaType, data.toString());
        Request request = new Request.Builder()
                .url(fengzhushouSichuanYidongVrbtProperties.getGetSmsUrl())
                .post(body)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", fengzhushouSichuanYidongVrbtProperties.getLogTag(), phone, data.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", fengzhushouSichuanYidongVrbtProperties.getLogTag(), phone, content);
            return mapper.readValue(content, FengzhushouSichuanYidongGetSmsResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", fengzhushouSichuanYidongVrbtProperties.getLogTag(), phone, e);
            return FengzhushouSichuanYidongGetSmsResult.fail();
        }
    }


    // {"code":0,"msg":"办理成功","data":{"res_code":"0","res_desc":"Processing the request succeeded!","result":{"TRADEFORMNUM":"311220217268806262","DETAILINFO":[],"retInnerMsg":"Processing the request succeeded!"}}}
    // 0成功 -1失败

    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    FengzhushouSichuanYidongSmsCodeResult smsCode(String phone, String smsCode, String orderNo, String app, String url, String ip) {
        ObjectNode data = mapper.createObjectNode();
        data.put("mobile", phone);
        data.put("code", smsCode);
        data.put("serial_number", orderNo);
        data.put("smstime", String.valueOf(System.currentTimeMillis()));
        data.put("zfCode", fengzhushouSichuanYidongVrbtProperties.getZfCode());
        data.put("platform", app);
        data.put("url", url);
        data.put("pid", fengzhushouSichuanYidongVrbtProperties.getPId());
        data.put("ip", ip);
        RequestBody body = RequestBody.create(mediaType, data.toString());
        Request request = new Request.Builder()
                .url(fengzhushouSichuanYidongVrbtProperties.getSendSmsUrl())
                .post(body)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},请求:{}", fengzhushouSichuanYidongVrbtProperties.getLogTag(), phone, smsCode, data.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},响应:{}", fengzhushouSichuanYidongVrbtProperties.getLogTag(), phone, smsCode, content);
            return mapper.readValue(content,FengzhushouSichuanYidongSmsCodeResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},异常:", fengzhushouSichuanYidongVrbtProperties.getLogTag(), phone, smsCode, e);
            return FengzhushouSichuanYidongSmsCodeResult.fail();
        }
    }
}
