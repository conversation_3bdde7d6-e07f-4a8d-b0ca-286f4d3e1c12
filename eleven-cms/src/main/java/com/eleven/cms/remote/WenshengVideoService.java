package com.eleven.cms.remote;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.config.WenshengVideoProperties;
import com.eleven.cms.dto.HyFtpUploadDTO;
import com.eleven.cms.dto.ReportDTO;
import com.eleven.cms.entity.VrbtDiyVideo;
import com.eleven.cms.entity.WenshengRecord;
import com.eleven.cms.enums.ReportTypeEnum;
import com.eleven.cms.service.IVrbtDiyVideoService;
import com.eleven.cms.service.IWenshengRecordService;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.WenshengVideoResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.RING_SETTING_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.RING_SETTING_STATUS_SUCC;

/**
 * @author: lihb
 * @create: 2022-9-20 11:40:24
 */
@Slf4j
@Service
public class WenshengVideoService {

    public static final String LOG_TAG = "文生视频api";
    @Autowired
    private WenshengVideoProperties wenshengVideoProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");

    @Autowired
    private IWenshengRecordService wenshengRecordService;

    @Autowired
    private MiguApiService miguApiService;

    @Autowired
    private IVrbtDiyVideoService vrbtDiyVideoService;

    @Autowired
    private YycpReportService yycpReportService;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 提交⽂⽣视频任务
     * <p>
     * ⽣成图⽚的提示词 prompt
     *
     * @return
     * @throws Exception
     */
    public WenshengVideoResult commitTask(String prompt, String duration) {

        try {
            duration = "15".equals(duration) ? "15" : "9";
            String url = wenshengVideoProperties.getCommitTaskUrl();
            ObjectNode bodyNode = mapper.createObjectNode();
            bodyNode.put("prompt", prompt);
            bodyNode.put("aspect_ratio", "9:16");
            bodyNode.put("duration", duration);
            bodyNode.put("notify_url", wenshengVideoProperties.getNotifyUrl());
            bodyNode.put("channel_id", "hjvxi");
            RequestBody body = RequestBody.create(mediaType, bodyNode.toString());
            Request request = new Request.Builder().url(url)
                    .addHeader("Authorization", wenshengVideoProperties.getToken())
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            log.info("{}-提交⽂⽣视频任务-请求:{},body:{}", LOG_TAG, request.toString(), bodyNode);

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交⽂⽣视频任务,返回结果:{}", LOG_TAG, result);
            WenshengVideoResult wenshengVideoResult = mapper.readValue(result, WenshengVideoResult.class);
            return wenshengVideoResult;
        } catch (Exception e) {
            log.info("{}-提交⽂⽣视频任务,异常:", LOG_TAG, e);
            return WenshengVideoResult.fail();
        }
    }

    /**
     * 获取⽂⽣视频结果
     * <p>
     * 任务id taskId
     *
     * @return
     * @throws Exception
     */
    public WenshengVideoResult pullResult(String taskId) {

        try {
            String url = wenshengVideoProperties.getPullResultUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addQueryParameter("task_id", taskId)
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Authorization", wenshengVideoProperties.getToken())
                    .build();
            log.info("{}-获取⽂⽣视频结果-请求:{},taskId:{}", LOG_TAG, request.toString(), taskId);

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取⽂⽣视频结果,返回结果:{}", LOG_TAG, result);
            WenshengVideoResult wenshengVideoResult = mapper.readValue(result, WenshengVideoResult.class);
            return wenshengVideoResult;
        } catch (Exception e) {
            log.info("{}-获取⽂⽣视频结果,异常:", LOG_TAG, e);
            return WenshengVideoResult.fail();
        }
    }

    /**
     * 设置铃声
     *
     * @param hyFtpUploadDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public String setRing(HyFtpUploadDTO hyFtpUploadDTO) {
        WenshengRecord record = wenshengRecordService.getById(hyFtpUploadDTO.getId());
        if (record == null) {
            return null;
        }

        //已设置无需重复上传
        if (StrUtil.isNotBlank(record.getVrbtDiyVideoId())) {
            VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.getOne(Wrappers.<VrbtDiyVideo>lambdaQuery().select(VrbtDiyVideo::getSettingStatus).eq(VrbtDiyVideo::getId, record.getVrbtDiyVideoId()));
            if (vrbtDiyVideo != null && vrbtDiyVideo.getSettingStatus() == 1) {
                RemoteResult result = miguApiService.vrbtToneFreeMonthOrder(hyFtpUploadDTO.getMobile(), MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX, vrbtDiyVideo.getCopyrightId(), MiguApiService.VRBT_TONE_ORDER_SETFLAG_BOTH_ZBJ);
                VrbtDiyVideo update = new VrbtDiyVideo();
                update.setId(vrbtDiyVideo.getId());
                update.setSettingStatus(result.isOK() ? RING_SETTING_STATUS_SUCC : RING_SETTING_STATUS_FAIL);
                update.setSettingResult(JacksonUtils.toJson(result));
                vrbtDiyVideoService.updateById(update);
                return vrbtDiyVideo.getId();
            }
        }


        // 1.生成订单
        VrbtDiyVideo vrbtDiyVideo = new VrbtDiyVideo();
        vrbtDiyVideoService.save(vrbtDiyVideo);
        // 2.主表关联
        wenshengRecordService.update(new LambdaUpdateWrapper<WenshengRecord>()
                .set(WenshengRecord::getVrbtDiyVideoId, vrbtDiyVideo.getId())
                .eq(WenshengRecord::getId, hyFtpUploadDTO.getId()));
        // 3.异步上报
        hyFtpUploadDTO.setVrbtDiyVideoId(vrbtDiyVideo.getId());

//        rabbitTemplate.convertAndSend(RabbitMQConfig.YYCP_SET_RING_QUEUE_NAME, hyFtpUploadDTO, new CorrelationData(hyFtpUploadDTO.getId()));
        log.info("{}-设置铃声消息发送成功,手机号:{}", "YYCP", hyFtpUploadDTO.getMobile());
        return vrbtDiyVideo.getId();
    }

    /**
     * 设置铃声消息处理
     *
     * @param hyFtpUploadDTO hyFtpUploadDTO
     */
    public void handleSetRingMQMsg(HyFtpUploadDTO hyFtpUploadDTO) {
        String id = hyFtpUploadDTO.getId();
        String mobile = hyFtpUploadDTO.getMobile();
        String filePath = hyFtpUploadDTO.getFilePath();
        String mvName = StrUtil.isBlank(hyFtpUploadDTO.getMvName()) ? String.valueOf(IdWorker.getId()) : hyFtpUploadDTO.getMvName();

        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        String pathName = "/yycp" + df.format(new Date());
        String fileName = "video" + IdWorker.getId() + ".mp4";
        boolean flag = false;
        flag = vrbtDiyVideoService.uploadRemoteUrlVideoToFtp(pathName, filePath, fileName);

        if (flag) {
            ReportDTO reportDTO = new ReportDTO();
            reportDTO.setPhone(mobile);
            reportDTO.setMvUrl(pathName + "/" + fileName);
            reportDTO.setMvName(mvName);
            reportDTO.setTransactionId(ReportTypeEnum.HY.getCode() + IdWorker.getId());
            reportDTO.setId(id);
            reportDTO.setChannelCode(MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX);
            yycpReportService.report(reportDTO, hyFtpUploadDTO.getVrbtDiyVideoId());
        }
    }
}
