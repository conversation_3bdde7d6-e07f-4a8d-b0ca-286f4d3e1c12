package com.eleven.cms.remote;

import com.eleven.cms.config.LiantongWydCrackProperties;
import com.eleven.cms.service.impl.AlarmService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.DianxinVrbtResult;
import com.eleven.cms.vo.LiantongCrackResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 移动视频彩铃破解
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class LianTongWydCrackService {

    @Autowired
    private Environment environment;
    @Autowired
    private LiantongWydCrackProperties liantongWydCrackProperties;
    @Autowired
    AlarmService alarmService;

    private OkHttpClient client;
    private ObjectMapper mapper;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    LiantongCrackResult getSms(String phone, String channel) {


        final HttpUrl httpUrl = HttpUrl.parse(liantongWydCrackProperties.getGetSmsUrl())
            .newBuilder()
            .addQueryParameter("a", liantongWydCrackProperties.getA())
            .addQueryParameter("vaccode", liantongWydCrackProperties.getVacCode())
            .addQueryParameter("tel", phone)
            .build();
        log.info("{}-获取短信-渠道号:{},手机号:{},请求:{}", liantongWydCrackProperties.getLogTag(), channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-渠道号:{},手机号:{},响应:{}", liantongWydCrackProperties.getLogTag(), channel, phone, content);
            return mapper.readValue(content, LiantongCrackResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-渠道号:{},手机号:{},异常:", liantongWydCrackProperties.getLogTag(), channel, phone, e);
            return LiantongCrackResult.fail();
        }
    }


    /**
     * 提交短信验证码
     *
     * @param orderId
     * @param smsCode
     * @return
     */
    public @Nonnull
    LiantongCrackResult smsCode(String orderId, String smsCode, String channel, String mobile) {
        final HttpUrl httpUrl = HttpUrl.parse(liantongWydCrackProperties.getSmsValidUrl())
            .newBuilder()
            .addQueryParameter("sid", orderId)
            .addQueryParameter("vcode", smsCode)
            .build();
        log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},短信验证码:{},请求:{}", liantongWydCrackProperties.getLogTag(), channel, orderId, mobile, smsCode, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},响应:{}", liantongWydCrackProperties.getLogTag(), channel, orderId, mobile, content);
            final LiantongCrackResult liantongCrackResult = mapper.readValue(content, LiantongCrackResult.class);
            //检测联通业务是否达到上限
            alarmService.monitorLiantongBusiness(channel, liantongCrackResult.getMsg());
            return liantongCrackResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},异常:", liantongWydCrackProperties.getLogTag(), channel, orderId, mobile, e);
            return LiantongCrackResult.fail();
        }
    }

}
