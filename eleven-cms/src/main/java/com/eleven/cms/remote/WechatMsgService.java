package com.eleven.cms.remote;

import com.eleven.cms.dto.WechatComplainNotify;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.service.IQyclWxpayService;
import com.eleven.cms.service.IWechatConfigLogService;
import com.eleven.cms.util.AESUtils;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信投诉通知
 */
@Slf4j
@RestController
@RequestMapping("/api/wechat")
public class WechatMsgService {
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private WechatComplainService wechatComplainService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    /**
     * 微信支付投诉通知回调
     * @param mchId
     * @param requestBody
     * @return
     */
    @RequestMapping(value = "/complain/notify")
    @ResponseBody
    public FebsResponse complainNotify(@RequestParam(value = "mchId", required = false, defaultValue = "") String mchId, @RequestBody String requestBody){
        log.info("微信支付投诉通知回调=>商户号:{},响应参数:{}",mchId,requestBody);
        try {
            final WechatComplainNotify wechatComplainNotify = mapper.readValue(requestBody, WechatComplainNotify.class);
            WechatConfigLog wechatConfigLog = wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId, mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String resource= AESUtils.setDecryptData(wechatComplainNotify.getResource().getAssociatedData(),wechatComplainNotify.getResource().getNonce(),wechatComplainNotify.getResource().getCiphertext(),wechatConfigLog.getApiKey());
            ObjectNode node = mapper.readValue(resource, ObjectNode.class);
            String complaintId = node.at("/complaint_id").asText();
            String actionType = node.at("/action_type").asText();
            wechatComplainService.wechatQueryComplainDetail(complaintId,actionType,mchId,wechatComplainNotify);
            return new FebsResponse().code("SUCCESS").message("成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().code("FAIL").message("失败");
    }

    /**
     * 创建微信支付投诉通知回调地址
     * @param url
     * @return
     */
    @RequestMapping(value = "/create/complain/url")
    @ResponseBody
    public FebsResponse createComplainUrl(@RequestParam(value = "url", required = false, defaultValue = "") String url,
                                          @RequestParam(value = "mchId", required = false, defaultValue = "") String mchId){
        log.info("创建微信支付投诉通知回调地址=>地址:{},商户号：{}",url,mchId);
        return qyclWxpayService.createComplainUrl(url,mchId);
    }


    /**
     * 查询微信投诉通知回调地址
     * @param mchId
     * @return
     */
    @RequestMapping(value = "/query/complain/url")
    @ResponseBody
    public FebsResponse queryComplainUrl(@RequestParam(value = "mchId", required = false, defaultValue = "") String mchId){
        log.info("查询微信投诉通知回调地址=>地址:{},商户号：{}",mchId);
        return qyclWxpayService.queryComplainUrl(mchId);
    }


    /**
     * 删除微信投诉通知回调地址
     * @param mchId
     * @return
     */
    @RequestMapping(value = "/delete/complain/url")
    @ResponseBody
    public FebsResponse deleteComplainUrl(@RequestParam(value = "mchId", required = false, defaultValue = "") String mchId){
        log.info("删除微信投诉通知回调地址=>商户号：{}",mchId);
        return qyclWxpayService.deleteComplainUrl(mchId);
    }


    /**
     * 更新微信投诉通知回调地址
     * @param url
     * @return
     */
    @RequestMapping(value = "/update/complain/url")
    @ResponseBody
    public FebsResponse updateComplainUrl(@RequestParam(value = "url", required = false, defaultValue = "") String url,
                                          @RequestParam(value = "mchId", required = false, defaultValue = "") String mchId){
        log.info("更新微信投诉通知回调地址=>地址:{},商户号：{}",url,mchId);
        return qyclWxpayService.updateComplainUrl(url,mchId);
    }

    /**
     * 查询投诉单列表
     * @param limit
     * @param offset
     * @param beginDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "/query/complain/list")
    @ResponseBody
    public FebsResponse queryComplainList(@RequestParam(value = "limit", required = false, defaultValue = "10") String limit,
                                          @RequestParam(value = "offset", required = false, defaultValue = "0") String offset,
                                          @RequestParam(value = "beginDate", required = false, defaultValue = "") String beginDate,
                                          @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate){
        log.info("查询投诉单列表=>条数:{},分页：{},开始时间：{},结束时间：{}",limit,offset,beginDate,endDate);
        return wechatComplainService.queryComplainList(Integer.valueOf(limit), Integer.valueOf(offset),beginDate,endDate);
    }
}
