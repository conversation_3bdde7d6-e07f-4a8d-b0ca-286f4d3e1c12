package com.eleven.cms.remote;

import com.eleven.cms.config.YunpanchangyingProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @author: lihb
 * @create: 2022-9-20 11:40:24
 */
@Slf4j
@Service
public class YunpanchangyingService {

    public static final String LOG_TAG = "云盘畅影4K会员api";
    @Autowired
    private YunpanchangyingProperties yunpanchangyingProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 创建支付
     *
     * @param phone     手机号
     * @return
     * @throws Exception
     */
    public YunpanchangyingResult createPaymentEasy(String phone) {
        Long timestamp = System.currentTimeMillis()/1000;
        try {
            ObjectNode dataMap = mapper.createObjectNode();
            dataMap.put("channelCode", yunpanchangyingProperties.getChannelCode());
            dataMap.put("timestamp", timestamp);
            dataMap.put("payType", yunpanchangyingProperties.getPayType());
            dataMap.put("payCode", yunpanchangyingProperties.getPayCode());
            dataMap.put("phone", phone);
            dataMap.put("sign", sign(phone,timestamp));
            RequestBody body = RequestBody.create(mediaType, dataMap.toString());
            Request request = new Request.Builder().url(yunpanchangyingProperties.getOrderUrl())
                    .addHeader("Content-Type","application/json")
                    .post(body)
                    .build();
            log.info("{}-创建支付-手机号:{},请求:{},body:{}", LOG_TAG, phone, request.toString(),dataMap);

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-创建支付-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            YunpanchangyingResult yunpanchangyingResult = mapper.readValue(result, YunpanchangyingResult.class);
            return yunpanchangyingResult;
        } catch (Exception e) {
            log.info("{}-创建支付-手机号:{},异常:", LOG_TAG, phone, e);
            return YunpanchangyingResult.fail();
        }
    }

    private String sign(String phone,long timestamp){
        String data = phone + yunpanchangyingProperties.getChannelCode() + timestamp + yunpanchangyingProperties.getSecretKey();
        String sha1 = DigestUtils.sha1Hex(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(sha1.toUpperCase().getBytes()).toUpperCase();
    }
}
