package com.eleven.cms.remote;

import com.eleven.cms.config.HainanYunshemeiConfig;
import com.eleven.cms.config.HainanYunshemeiProperties;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonArray;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 海南移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class HainanYidongYunshemeiService {

    public static final String LOG_TAG = "云摄美海南移动api";


    @Autowired
    private Environment environment;
    @Autowired
    private HainanYunshemeiProperties hainanYunshemeiProperties;

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }

    /**
     * 营销活动校验与短信验证码下发接口
     * @param phone
     * @param channel
     * @return
     */
    private @Nonnull
    boolean sendValidateCodeByOut(String phone, String channel) {
        HainanYunshemeiConfig hainanYunshemeiConfig = hainanYunshemeiProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("HAIN_UNHC_sendValidateCodeByOut");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("SERIAL_NUMBER", phone);
        dataNode.put("PRODUCT_ID", hainanYunshemeiConfig.getProductId());
        dataNode.put("PACKAGE_ID", hainanYunshemeiConfig.getPackageId());
        dataNode.put("X_GETFEE", "1");
        dataNode.put("OFFER_TYPE", hainanYunshemeiConfig.getOfferType());
        dataNode.put("OFFER_CODE", hainanYunshemeiConfig.getPackageId());
        dataNode.put("OFFER_EFFECT_TIME", DateUtils.date_sdf.get().format(new Date()));
        dataNode.put("TRADE_TYPE_CODE", "240");
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-营销活动校验与短信验证码下发接口-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-营销活动校验与短信验证码下发接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            String resultCode = jsonNode.at("/result/parambody_sendValidateCode/RESULT_CODE").asText();
            return "0".equals(resultCode);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-营销活动校验与短信验证码下发接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return false;
        }
    }

    /**
     * 营销活动校验与办理接口
     * @param phone
     * @param channel
     * @param code
     * @return
     */
    private @Nonnull
    ObjectNode checkValidateCode(String phone, String channel, String code) {
        HainanYunshemeiConfig hainanYunshemeiConfig = hainanYunshemeiProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("HAIN_UNHC_checkValidateCode");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("SERIAL_NUMBER", phone);
        dataNode.put("PRODUCT_ID", hainanYunshemeiConfig.getProductId()); //
        dataNode.put("PACKAGE_ID", hainanYunshemeiConfig.getPackageId());
        dataNode.put("CHECK_MODE", "7");
        dataNode.put("VERIFY_CODE", code);
        dataNode.put("TRADE_TYPE_CODE", "240");
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-营销活动校验与办理接口-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, channel, phone, code, httpUrl.toString());
        ObjectNode resultNode = mapper.createObjectNode();
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-营销活动校验与办理接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            String resuInfo = jsonNode.at("/result/parambody_checkValidateCode/RESULT_INFO").asText();
            String resultCode = jsonNode.at("/result/parambody_checkValidateCode/RESULT_CODE").asText();
            resultNode.put("code", resultCode);
            resultNode.put("msg", resuInfo);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-营销活动校验与办理接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            resultNode.put("code", "999");
        } finally {
            return resultNode;
        }
    }

    /**
     * 产品变更校验接口
     * @param phone
     * @param channel
     * @return
     */
    private @Nonnull
    boolean checkBusinessRule(String phone, String channel) {
        HttpUrl httpUrl = getCommonParam("HAIN_UNHC_checkBusinessRule");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("SERIAL_NUMBER", phone);
        dataNode.put("X_CHOICE_TAG", "0");
        dataNode.put("TRADE_TYPE_CODE", "110");
        dataNode.put("ROUTE_EPARCHY_CODE", "0898");
        dataNode.put("EPARCHY_CODE", "0898");
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-产品变更校验接口-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-产品变更校验接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content).at("/result/TIPS_TYPE_ERROR");
            return jsonNode.isArray() && jsonNode.size() == 0;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-产品变更校验接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return false;
        }
    }

    /**
     * 产品变更短信验证码下发接口
     * @param phone
     * @param channel
     * @return
     */
    private @Nonnull
    boolean sendSmsVerifyCodeByOut(String phone, String channel) {
        HainanYunshemeiConfig hainanYunshemeiConfig = hainanYunshemeiProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("HAIN_UNHT_sendSmsVerifyCodeByOut");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("OFFER_TYPE", hainanYunshemeiConfig.getOfferType());
        dataNode.put("OFFER_CODE", hainanYunshemeiConfig.getOfferCode());
        dataNode.put("OFFER_EFFECT_TIME", DateUtils.date_sdf.get().format(new Date()));
        dataNode.put("SERIAL_NUMBER", phone);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-产品变更短信验证码下发接口-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-产品变更短信验证码下发接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            String resultCode = mapper.readTree(content).at("/result/RESULT_CODE").asText();
            return "0".equals(resultCode);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-产品变更短信验证码下发接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return false;
        }
    }

    /**
     * 产品变更办理接口
     * @param phone
     * @param channel
     * @param code
     * @return
     */
    private @Nonnull
    ObjectNode authCheckChangeProductReg(String phone, String channel, String code) {
        HainanYunshemeiConfig hainanYunshemeiConfig = hainanYunshemeiProperties.getConfigByChannel(channel);
        HttpUrl httpUrl = getCommonParam("HAIN_UNHT_authCheckChangeProductReg");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("SERIAL_NUMBER", phone);
        dataNode.put("CHECK_MODE", "7");
        dataNode.put("VERIFY_CODE", code);
        dataNode.put("ELEMENT_ID", hainanYunshemeiConfig.getOfferCode());
        dataNode.put("ELEMENT_TYPE_CODE", "D");
        dataNode.put("MODIFY_TAG", "0");
        dataNode.put("TRADE_TYPE_CODE", "110");
        dataNode.put("BOOKING_TAG", "0");
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-产品变更办理接口-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, channel, phone, code, httpUrl.toString());
        ObjectNode resultNode = mapper.createObjectNode();
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-产品变更办理接口-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            String resuInfo = jsonNode.at("/result/ChangeProduct_RESULT_INFO").asText();
            String resultCode = jsonNode.at("/result/ChangeProduct_RESULT_CODE").asText();
            resultNode.put("code", resultCode);
            resultNode.put("msg", resuInfo);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-产品变更办理接口-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            resultNode.put("code", "999");
        } finally {
            return resultNode;
        }
    }

    public boolean getSms(String phone, String channel) {
        if (isMarketProduct(channel)) {
            return sendValidateCodeByOut(phone, channel);
        }
        return checkBusinessRule(phone,channel) ? sendSmsVerifyCodeByOut(phone, channel) : false;
    }

    public ObjectNode smsCode(String phone, String channel, String code) {
        if (isMarketProduct(channel)) {
            return checkValidateCode(phone, channel, code);
        }
        return authCheckChangeProductReg(phone, channel, code);
    }

    /**
     * 判断是否为营销产品
     * @param channel
     * @return
     */
    private boolean isMarketProduct(String channel){
        return BizConstant.BIZ_CHANNEL_HNYD_YDSXX_PLUS.equals(channel);
    }





    private HttpUrl getCommonParam(String method) {
        final HttpUrl httpUrl = HttpUrl.parse(hainanYunshemeiProperties.getApiBaseUrl())
                .newBuilder()
                .addQueryParameter("flowId", DateUtil.formatForMiguGroupApi(LocalDateTime.now()))
                .addQueryParameter("provinceCode", hainanYunshemeiProperties.getProvinceCode())
                .addQueryParameter("channelTypeId", hainanYunshemeiProperties.getChannelTypeId())
                .addQueryParameter("tradeEparchyCode", hainanYunshemeiProperties.getTradeEparchyCode())
                .addQueryParameter("tradeCityCode", hainanYunshemeiProperties.getTradeCityCode())
                .addQueryParameter("tradeDepartId", hainanYunshemeiProperties.getTradeDepartId())
                .addQueryParameter("tradeStaffId", hainanYunshemeiProperties.getTradeStaffId())
                .addQueryParameter("appId", hainanYunshemeiProperties.getAppId())
                .addQueryParameter("appKey", hainanYunshemeiProperties.getAppKey())
                .addQueryParameter("method", method)
                .addQueryParameter("format", "json")
                .addQueryParameter("timestamp", DateUtil.formatFullTime(LocalDateTime.now()))
                .build();
        return httpUrl;
    }
}
