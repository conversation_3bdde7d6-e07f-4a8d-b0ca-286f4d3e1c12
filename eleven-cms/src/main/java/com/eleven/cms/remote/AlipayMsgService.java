package com.eleven.cms.remote;

import com.alipay.api.msg.AlipayMsgClient;
import com.eleven.cms.service.IAlipayConfigService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * Author: <EMAIL>
 * Date: 2023/4/4 11:38
 * Desc: 支付宝蚂蚁消息服务
 */
@Slf4j
@Service
@Profile("prod")
public class AlipayMsgService {
    public static final String LOG_TAG = "支付宝蚂蚁消息服务";
    // 目标支付宝服务端地址，线上环境为 openchannel.alipay.com
    public static final String SERVER_HOST =  "openchannel.alipay.com" ;
    // 数据签名方式，请与应用设置的默认签名方式保持一致
    public static final String SIGN_TYPE =  "RSA2" ;

    @Autowired
    IAlipayConfigService alipayConfigService;
    @Autowired
    AliComplainService aliComplainService;

    private ObjectMapper mapper;

//    @PostConstruct
    public void init(){
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public void connetAll() {
        alipayConfigService.listValid().forEach(conf -> {
            final String appId = conf.getAppId();
            final String businessType = conf.getBusinessType();
            try {
                // 获取client对象，一个appId对应一个实例
                final AlipayMsgClient alipayMsgClient = AlipayMsgClient.getInstance(appId); // 应用私钥
                log.info("{}-初始化消息客户端websocket开始连接,mchName:{},appId:{},businessType:{}", LOG_TAG, conf.getMchName(), appId, businessType);
                alipayMsgClient.setConnector(SERVER_HOST);
                alipayMsgClient.setSecurityConfig(SIGN_TYPE, conf.getPrivateKey(), conf.getPublicKey());  // 支付宝公钥
                alipayMsgClient.setMessageHandler(new AlipayMsgHandler(appId, businessType, aliComplainService));
                alipayMsgClient.connect();
                log.info("{}-初始化消息客户端websocket连接成功,mchName:{},appId:{},businessType:{}", LOG_TAG, conf.getMchName(), appId,businessType);
            } catch (Exception e) {
                log.info("{}-初始化消息客户端websocket连接异常,mchName:{},appId:{},businessType:{}", LOG_TAG, conf.getMchName(), appId,businessType, e);
            }
        });
    }
}
