package com.eleven.cms.remote;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.JunboLlbConfig;
import com.eleven.cms.config.JunboLlbProperties;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.JunboLlbResult;
import com.eleven.cms.vo.KuaimaGansuResult;
import com.eleven.cms.vo.KuaimaMiniAppResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.events.Event;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 骏伯湖南移动业务类
 * @author: cai lei
 * @create: 2022-07-07 10:29
 */

@Slf4j
@Service
public class JunboHunanYidongService {

    public static final String LOG_TAG = "湖南移动视频彩铃api";
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    @Autowired
    private JunboLlbProperties junboLlbProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils
                .getNewInstance()
                .newBuilder()
                .connectTimeout(20L, TimeUnit.SECONDS)
                .readTimeout(20L, TimeUnit.SECONDS)
                .writeTimeout(20L, TimeUnit.SECONDS)
                .build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    //{"success":true,"code":"0000","message":"执行成功","data":{"msg":"","responseCode":"0","orderCode":"","subMediaCode":"0","url":"","routeCode":"","sellerId":""}}
    public JunboLlbResult getSms(String phone, String sysOrderId, String channel) {
        JunboLlbConfig junboLlbConfig = junboLlbProperties.getJunboLlbConfigByBizCode(channel);
        String url = junboLlbProperties.getGetSmsUrl();
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("pid", junboLlbConfig.getPid());
        objectNode.put("sysOrderId", sysOrderId);
        objectNode.put("productCode", junboLlbConfig.getProductCode());
        objectNode.put("url", junboLlbConfig.getUrl());
        objectNode.put("userAgent", "");
        objectNode.put("contactNumber", phone);
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();
        log.info("{}-获取验证码-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone, channel, objectNode.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取验证码-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone, channel, result);
            return mapper.readValue(result, JunboLlbResult.class);
        } catch (Exception e) {
            log.info("{}-获取验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone, channel, e);
            return JunboLlbResult.FAIL_RESULT;
        }
    }

    //{"success":true,"code":"0000","message":"执行成功","data":{"msg":"下单成功","responseCode":"0","orderCode":"","subMediaCode":"0","url":"","routeCode":"flow_yd_hunan","sellerId":""}}
    public JunboLlbResult smsCode(String phone, String sysOrderId, String code, String channel) {
        JunboLlbConfig junboLlbConfig = junboLlbProperties.getJunboLlbConfigByBizCode(channel);
        String url = junboLlbProperties.getSmsCodeUrl();
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("pid", junboLlbConfig.getPid());
        objectNode.put("sysOrderId", sysOrderId);
        objectNode.put("productCode", junboLlbConfig.getProductCode());
        objectNode.put("url", junboLlbConfig.getUrl());
        objectNode.put("userAgent", "");
        objectNode.put("contactNumber", phone);
        objectNode.put("authCode", code);
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();
        log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},请求:{}", LOG_TAG, phone, channel,code, objectNode.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", LOG_TAG, phone, channel,code, result);
            return mapper.readValue(result, JunboLlbResult.class);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},异常:", LOG_TAG, phone, channel,code, e);
            return JunboLlbResult.FAIL_RESULT;
        }
    }

    public static String getSysOrderId() {
        return "CC" + DateUtils.yyyyMMdd.get().format(new Date()) + IdWorker.getId();
    }

    public static void main(String[] args) {
        JunboLlbResult junboLlbResult =  JunboLlbResult.FAIL_RESULT;
        if(junboLlbResult.isOperationOk()){
            System.out.println("1");
        }else{
            System.out.println(2);
        }
    }
}