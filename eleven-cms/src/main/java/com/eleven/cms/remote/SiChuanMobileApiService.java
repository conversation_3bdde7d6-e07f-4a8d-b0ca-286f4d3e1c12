package com.eleven.cms.remote;

import com.eleven.cms.config.SichuanMobileRechargeConfig;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.SichuanMobilePointAnalysiResult;
import com.eleven.cms.vo.SichuanMobilePrepareOrderResult;
import com.eleven.cms.vo.SichuanMobileQueryOrderResult;
import com.eleven.cms.vo.SichuanMobileRechargeResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.binary.Hex;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.util.BizConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * 四川移动流量包service
 *
 * @author: cai lei
 * @create: 2021-09-17 16:24
 */

@Slf4j
@Service
public class SiChuanMobileApiService {
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    private OkHttpClient client;
    //四川移动订单号码长度
    static final Integer ORDER_ID_LENGTH =28;
    //预下单接口增加投放点位参数,参数限制数字（1-30），前期为兼容老版本可为空，逐步取代后，后期上线将不能为空；
    public static final String POINT_NUM = "1";
    private ObjectMapper mapper;
    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    @Autowired
    private Environment environment;
    @Autowired
    private Interceptor sichuanMobileApiSignIntercept;
    @Autowired
    RedisUtil redisUtil;

    private OkHttpClient reChargeClient;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    //      @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().addInterceptor(sichuanMobileApiSignIntercept).build();
        this.reChargeClient = OkHttpClientUtils.getNewInstance().newBuilder().connectTimeout(20L, TimeUnit.SECONDS).readTimeout(20L, TimeUnit.SECONDS).writeTimeout(20L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(message -> System.out.println(message))).build();
            this.reChargeClient = this.reChargeClient.newBuilder().addNetworkInterceptor(new CurlInterceptor(message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper();
    }
    /**
     * 预下单接口
     * @param phone
     * @param channelCode
     * @return
     */
    public SichuanMobilePrepareOrderResult prepareOrder(String phone, String channelCode){
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-预下单接口-手机号:{},渠道号:{}", "四川移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return SichuanMobilePrepareOrderResult.fail();
        }
        String url = portCrackConfig.getSmsCodeUrl();
        Map<String, String> paramInfo = new HashMap<>();
        String serialNumber=generateOrderNo(portCrackConfig.getAppId());
        paramInfo.put("serial_number",  (serialNumber.length() >ORDER_ID_LENGTH)?serialNumber.substring(0, ORDER_ID_LENGTH):serialNumber);
        paramInfo.put("phone_no", phone);
        paramInfo.put("prod_prcid", portCrackConfig.getOfferCode());
        paramInfo.put("point", POINT_NUM); //预下单接口增加投放点位参数,参数限制数字（1-30），前期为兼容老版本可为空，逐步取代后，后期上线将不能为空；
        String data = null;
        try {
            data = mapper.writeValueAsString(paramInfo);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        data = generateSign(data, portCrackConfig.getSecretKey());
        RequestBody body = new FormBody.Builder()
                .add("paramInfo", data)
                .build();
        Request request = new Request.Builder().url(url)
                .post(body).tag(portCrackConfig)
                .build();
        log.info("{}-预下单-手机号:{},渠道号:{},请求:{},paramInfo:{}", portCrackConfig.getLogTag(), phone,channelCode, request.toString(),paramInfo);
        try (Response response =client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-预下单-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, SichuanMobilePrepareOrderResult.class);
        } catch (Exception e) {
            log.info("{}-预下单-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return SichuanMobilePrepareOrderResult.fail();
        }
    }


    /**
     * 订单状态查询接口
     * @param orderNo
     * @param channelCode
     * @param phone
     * @return
     */

    //{"result":{"time":"2021-12-24 09:40:40","phone":"151****7190","status":"false","orderId":"1000039020211224094014252349"},"res_code":"0000","res_msg":"成功"}
    //{"result":{"time":"2023-08-25 14:56:56","phone":"139****6117","status":"true","orderId":"1000039720230825145632250228"},"res_code":"0000","res_msg":"成功"}
    public SichuanMobileQueryOrderResult queryOrder(String orderNo, String channelCode, String phone){
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-订单状态查询-手机号:{},渠道号:{}", "四川移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return SichuanMobileQueryOrderResult.fail();
        }
        String url = portCrackConfig.getQueryProductUrl();
        RequestBody body = new FormBody.Builder()
                .add("order_no", orderNo)
                .add("means_id", portCrackConfig.getOfferCode())
                .add("order_type", "B").build();
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(portCrackConfig)
                .build();
        log.info("{}-查询订单-订单号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), orderNo,channelCode, request.toString());
        try (Response response =client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询订单-手机号:{},订单号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(),phone, orderNo, channelCode, result);
            return mapper.readValue(result, SichuanMobileQueryOrderResult.class);
        } catch (Exception e) {
            log.info("{}-查询订单-手机号:{},订单号:{},渠道号:{},异常:", portCrackConfig.getLogTag(),phone, orderNo, channelCode, e);
            return SichuanMobileQueryOrderResult.fail();
        }
    }


    /**
     *  投放业务留存率分析能力查询(不需要授权)
     *
     * 1)	该接口是为外部互联网渠道商家提供分触点留存数据查询的功能，该功能强关联预下单接口prepareOrder里的point字段。如渠道侧下单未传入point字段，该接口则无法查询数据。
     * 2)	请求参数的时间是指的查询月份，查询当月截至最新当天该渠道该point的所有业务办理留存情况。往月则是往月全月的当时留存情况。
     * 举例，假设今天是2024年4月29日：
     * 传入4月，返回4月1日-4月28日的全部业务办理号码，在4月28日的留存情况；
     * 传入3月，返回3月1日-3月31日的全部业务办理号码，在3月全月的留存情况；
     * 3)	只能查询最近3个月的留存情况。
     * 4)	该接口数据为日更新，同一入参当日查询结果不会变化，请勿重复调用
     *
     * @param yearMonth  查询年月
     * @param channelCode  公司
     */
    public SichuanMobilePointAnalysiResult qryPointAnalysi(String yearMonth, String channelCode){
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-投放业务留存率分析能力查询-yearMonth:{},渠道号:{}", "四川移动业务-PortCrackConfig-配置错误", yearMonth, channelCode);
            return SichuanMobilePointAnalysiResult.fail();
        }
        String url = portCrackConfig.getQueryServiceUrl();
        RequestBody body = new FormBody.Builder()
                .add("opDate", yearMonth)
                .add("point", POINT_NUM)
                .build();
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(portCrackConfig)
                .build();
        log.info("{}-查询留存率-yearMonth:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), yearMonth, channelCode, request.toString());
        try (Response response =client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询留存率-yearMonth:{},渠道号:{},,返回结果:{}",  portCrackConfig.getLogTag(), yearMonth, channelCode, result);

            return mapper.readValue(result, SichuanMobilePointAnalysiResult.class);
        } catch (Exception e) {
            log.info("{}-查询留存率-yearMonth:{},渠道号:{},异常:",  portCrackConfig.getLogTag(), yearMonth, channelCode, e);
            return SichuanMobilePointAnalysiResult.fail();
        }
    }


    public String generateSign(String data,String appKey){
        byte[] appKeyByte =appKey.getBytes();
        String appKeyStr = Hex.encodeHexString(appKeyByte) + Hex.encodeHexString(appKeyByte);
        appKeyStr = appKeyStr.substring(0, 32);
        try {
            return AESUtils.aesEncrypt(data, appKeyStr);
        } catch (Exception e) {
            log.error("四川移动生成签名异常-data:{},appKey:{}",data,appKey,e);
            return null;
        }
    }
        public String generateOrderNo(String prefix) {
            //** 自增序列 *//*
            long sequence  = redisUtil.incr(CacheConstant.SERIAL_REDIS_KEY, 1);
            if(String.valueOf(sequence).length() > 6){
                redisUtil.del(CacheConstant.SERIAL_REDIS_KEY);
                sequence = redisUtil.incr(CacheConstant.SERIAL_REDIS_KEY, 1);
            }
            String seq = SequenceUtils.getSequence(sequence);
            StringBuilder sb = new StringBuilder();
            sb.append(prefix).append(DateUtil.formatFullTime(LocalDateTime.now())).append(seq);
            String orderNo = sb.toString();
            return orderNo;
        }


    public String decryptResult(String result,String channelCode) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-订单号解密-result:{},渠道号:{}", "四川移动业务-PortCrackConfig-配置错误", result, channelCode);
            return null;
        }
        byte[] appKeyByte = portCrackConfig.getSecretKey().getBytes();
        String appKeyStr = org.apache.commons.codec.binary.Hex.encodeHexString(appKeyByte) + Hex.encodeHexString(appKeyByte);
        appKeyStr = appKeyStr.substring(0, 32);
        return AESUtils.aesDecrypt(result, appKeyStr);
    }

    /**
     * 渝姐讲故事会员充值
     * @param orderNo
     * @param channelCode
     * @param phone
     * @return
     */
    public void reCharge(String orderNo, String channelCode, String phone,String saletime){
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null || StringUtils.isEmpty(portCrackConfig.getRemark())) {
            log.info("{}-渝姐讲故事会员充值-消息队列-手机号:{},渠道号:{}", "四川移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return;
        }
        SichuanMobileRechargeConfig rechargeConfig= null;
        try {
            rechargeConfig = mapper.readValue(portCrackConfig.getRemark(), SichuanMobileRechargeConfig.class);
        } catch (Exception e) {
            log.info("{}-渝姐讲故事会员充值-消息队列-手机号:{},渠道号:{}", "四川移动业务-PortCrackConfig-异常", phone, channelCode,e);
            return;
        }
        ObjectNode jsonNode = mapper.createObjectNode();
        jsonNode.put("orderNo",orderNo);
        jsonNode.put("channelCode", channelCode);
        jsonNode.put("phone", phone);
        jsonNode.put("saletime", saletime);
        rabbitMQMsgSender.sichuanMobileYuJieReChargeDeductMessage(jsonNode);
    }

    /**
     * 渝姐讲故事会员充值
     * @param jsonNode
     * @return
     */
    public SichuanMobileRechargeResult reChargeConsume(JsonNode jsonNode){
        String orderNo = jsonNode.at("/orderNo").asText("");
        String channelCode = jsonNode.at("/channelCode").asText("");
        String phone = jsonNode.at("/phone").asText("");
        String saletime = jsonNode.at("/saletime").asText("");
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null || StringUtils.isEmpty(portCrackConfig.getRemark())) {
            log.info("{}-渝姐讲故事会员充值-手机号:{},渠道号:{}", "四川移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return SichuanMobileRechargeResult.fail();
        }
        SichuanMobileRechargeConfig rechargeConfig= null;
        try {
            rechargeConfig = mapper.readValue(portCrackConfig.getRemark(), SichuanMobileRechargeConfig.class);
        } catch (Exception e) {
            log.info("{}-渝姐讲故事会员充值-手机号:{},渠道号:{}", "四川移动业务-PortCrackConfig-异常", phone, channelCode,e);
            return SichuanMobileRechargeResult.fail();
        }
        String url = rechargeConfig.getReChargeUrl();
        long timestamp=System.currentTimeMillis();
        String sign = DigestUtils.md5DigestAsHex((rechargeConfig.getAppId()+rechargeConfig.getAppSecret()+timestamp+orderNo).getBytes(StandardCharsets.UTF_8));
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("appid", rechargeConfig.getAppId());
        objectNode.put("orderno", orderNo);
        objectNode.put("timestamp", timestamp);
        objectNode.put("sign", sign);
        objectNode.put("phoneno", phone);
        objectNode.put("saletime", saletime);
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();
        log.info("{}-充值-订单号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), orderNo,channelCode, objectNode.toString());
        try (Response response =reChargeClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-充值-手机号:{},订单号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(),phone, orderNo, channelCode, result);
            return mapper.readValue(result, SichuanMobileRechargeResult.class);
        } catch (Exception e) {
            log.info("{}-充值-手机号:{},订单号:{},渠道号:{},异常:", portCrackConfig.getLogTag(),phone, orderNo, channelCode, e);
            return SichuanMobileRechargeResult.fail();
        }
    }


    /**
     * 开通结果接收
     * @param jsonNode
     * @return
     */
    public boolean receiveSubscribeResult(JsonNode jsonNode) {
        String id = jsonNode.at("/id").asText("");
        String mobile = jsonNode.at("/mobile").asText("");
        Integer status =SUBSCRIBE_STATUS_FAIL;
        String result = jsonNode.at("/result").asText("");
        String extra = jsonNode.at("/extra").asText("");
        Subscribe subscribe = subscribeService.getById(id);
        if (subscribe == null) {
            log.error("更新订购结果失败,不存在该id的订购通知记录,id:{}", id);
            return false;
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},transId:{}", mobile, extra);
            return false;
        }
        SichuanMobileQueryOrderResult sichuanMobileQueryOrderResult= this.queryOrder(extra,subscribe.getChannel(),subscribe.getMobile());
        if (sichuanMobileQueryOrderResult.isOK() && sichuanMobileQueryOrderResult.getResult()!=null && sichuanMobileQueryOrderResult.getResult().isOK()) {
            result = "开通成功";
            status = SUBSCRIBE_STATUS_SUCCESS;
        } else {
            result = "开通失败";
            status = SUBSCRIBE_STATUS_FAIL;
        }
        Date now = new Date();
        Subscribe upd = new Subscribe();
        upd.setId(id);
        upd.setStatus(status);
        upd.setResult(result);
        upd.setOpenTime(now);
        upd.setModifyTime(now);
        upd.setIspOrderNo(extra);
        subscribeService.updateSubscribeDbAndEs(upd);
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status) ) {
            //特定渠道号充值
            this.reCharge(subscribe.getIspOrderNo(),subscribe.getChannel(),subscribe.getMobile(), DateUtils.now());
            //加入1天3天校验队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //成功计数
            subscribeService.saveChannelLimit(subscribe);
        }
        //信息流广告转化上报
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(subscribe, status);
        }
        return true;
    }
}
