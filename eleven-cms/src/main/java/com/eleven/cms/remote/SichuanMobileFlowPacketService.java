package com.eleven.cms.remote;

import com.eleven.cms.config.SichuanMobileFlowPacketConfig;
import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.util.AESUtils;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.SequenceUtils;
import com.eleven.cms.vo.SichuanMobilePrepareOrderResult;
import com.eleven.cms.vo.SichuanMobileResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 四川移动流量包service
 *
 * @author: cai lei
 * @create: 2021-09-17 16:24
 */

@Slf4j
@Service
public class SichuanMobileFlowPacketService {


    //业务名称：抖音视频彩铃全年包   活动代码：AZ440203
    //业务名称：和生活Plus全年会员促销活动   活动代码：AZ440217
    //业务名称：5G优享流量包-20元4GB    资费代码：ACAZ43767
    //业务名称：5G极速流量包-30元10GB   资费代码：ACAZ43768
    //优先先开通和生活plus


    private OkHttpClient client;
    private OkHttpClient junboClient;
    //四川移动订单号码长度
    static final Integer ORDER_ID_LENGTH =28;
    //预下单接口增加投放点位参数,参数限制数字（1-30），前期为兼容老版本可为空，逐步取代后，后期上线将不能为空；
    public static final String POINT_NUM = "1";
    public static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    private ObjectMapper mapper;

    @Autowired
    private Environment environment;

    public static final String LOG_TAG = "四川移动流量包api";

    @Autowired
    private Interceptor sichuanMobileSignIntercept;

    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;


    //      @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().addInterceptor(sichuanMobileSignIntercept).build();
        this.junboClient = OkHttpClientUtils.getNewInstance().newBuilder().addInterceptor(sichuanMobileSignIntercept).build();
        Map<String,PasswordAuthentication> authMap = new HashMap<>();
        this.junboClient = this.junboClient.newBuilder()
                .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.junboProxyHost, OkHttpClientUtils.junboProxyPort)))
                .build();

        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();

            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();

            this.junboClient = this.junboClient.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();


        }
        this.mapper = new ObjectMapper();
    }

    private OkHttpClient getClient(String company) {
        if (BizConstant.BIZ_SCMCC_CHANNEL_JUNBO.equals(company)) {
            return junboClient;
        }
        return client;
    }

    public SichuanMobileResult sendRandomCode(String phone, String bizCode) throws Exception {

        String url = sichuanMobileFlowPacketProperties.getSendRandomCodeUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        RequestBody body = new FormBody.Builder()
                .add("phone_no", phone)
                .add("prod_prcid", bizCode)
                .add("pass_mode", "0")
                .build();

        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-随机码下发-手机号:{},业务编码:{},请求:{}", LOG_TAG, phone, bizCode, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},业务编码:{},返回结果:{}", LOG_TAG, phone, bizCode, result);
            return mapper.readValue(result, SichuanMobileResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-随机码下发-手机号:{},业务编码:{},异常:", LOG_TAG, phone, bizCode, e);
            return SichuanMobileResult.fail();

        }
    }


    /**
     * 业务订购
     *
     * @param randomCode
     * @throws Exception
     */
    public SichuanMobileResult bizOrder(String phone, String randomCode, String bizCode) throws Exception {

        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = null;
        String url = sichuanMobileFlowPacketProperties.getBizOrderUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        String timestamp = DateUtils.getCurrentTimestamp();
        RequestBody body = new FormBody.Builder()
                .add("phone_no", phone)
                .add("order_id", generateOrderNo(sichuanMobileFlowPacketConfig.getAppId() + timestamp,""))
                .add("prod_prcid", bizCode)
                .add("sms_pwd", randomCode)
                .build();

        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-流量套餐订购-手机号:{},业务编码:{},请求:{}", LOG_TAG, phone, bizCode, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-流量套餐订购-手机号:{},业务编码:{},返回结果:{}", LOG_TAG, phone, bizCode, result);
            return mapper.readValue(result, SichuanMobileResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-流量套餐订购-手机号:{},业务编码:{},异常:", LOG_TAG, phone, bizCode, e);
            return SichuanMobileResult.fail();

        }
    }

    /**
     * 合约办理
     *
     * @param randomCode
     * @throws Exception
     */
    public SichuanMobileResult contractHandle(String phone, String randomCode, String bizCode) throws Exception {

        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = null;
        String url = sichuanMobileFlowPacketProperties.getContractHandleUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        String timestamp = DateUtils.getCurrentTimestamp();

        RequestBody body = new FormBody.Builder()
                .add("phone_no", phone)
                .add("order_id", generateOrderNo(sichuanMobileFlowPacketConfig.getAppId() + timestamp,""))
                .add("means_id", bizCode)
                .add("sms_pwd", randomCode)
                .build();

        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-合约办理-手机号:{},业务编码:{},请求:{}", LOG_TAG, phone, bizCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-流量套餐订购-手机号:{},业务编码:{},返回结果:{}", LOG_TAG, phone, bizCode, result);
            return mapper.readValue(result, SichuanMobileResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-流量套餐订购-手机号:{},业务编码:{},异常:", LOG_TAG, phone, bizCode, e);
            return SichuanMobileResult.fail();
        }
    }

    /**
     * 查询通知
     *
     * @throws Exception
     */
    public List<String> queryNotice() throws Exception {

        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = sichuanMobileFlowPacketProperties.getSichuanMobileConfig(BizConstant.BIZ_SCMCC_CHANNEL_YRJY);
        List<String> notices = new ArrayList();
        String url = sichuanMobileFlowPacketProperties.getQueryNoticeUrl();
        RequestBody body = new FormBody.Builder().build();
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(sichuanMobileFlowPacketConfig)
                .build();
        log.info("{}-查询通知", LOG_TAG, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询通知,返回结果:{}", LOG_TAG, result);
            JsonNode tree = mapper.readTree(result);
            JsonNode nodes = tree.get("result");
            for (int i = 0; i < nodes.size(); i++) {
                notices.add(nodes.get(i).get("upgradeContent").asText());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-查询通知,异常:", LOG_TAG, e);
        }
        return notices;
    }

    /**
     * 预下单接口
     *
     * @param phone
     * @param bizCode
     * @throws Exception
     */
    public SichuanMobilePrepareOrderResult prepareOrder(String phone, String bizCode,String company) throws Exception {
        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = sichuanMobileFlowPacketProperties.getSichuanMobileConfig(company);
        String url = sichuanMobileFlowPacketProperties.getPrepareOrderUrl();
        String timestamp = DateUtils.getCurrentTimestamp();
        Map<String, String> paramInfo = new HashMap<>();
        String serialNumber=generateOrderNo(sichuanMobileFlowPacketConfig.getAppId() + timestamp, company);
        paramInfo.put("serial_number",  (serialNumber.length() >ORDER_ID_LENGTH)?serialNumber.substring(0, ORDER_ID_LENGTH):serialNumber);
        paramInfo.put("phone_no", phone);
        paramInfo.put("prod_prcid", bizCode);
        paramInfo.put("point", POINT_NUM); //预下单接口增加投放点位参数,参数限制数字（1-30），前期为兼容老版本可为空，逐步取代后，后期上线将不能为空；
        String data = mapper.writeValueAsString(paramInfo);
        data = generateSign(data, sichuanMobileFlowPacketConfig);
        RequestBody body = new FormBody.Builder()
                .add("paramInfo", data)
                .build();
        Request request = new Request.Builder().url(url)
                .post(body).tag(sichuanMobileFlowPacketConfig)
                .build();
        log.info("{}-预下单-手机号:{},业务编码:{},请求:{},paramInfo:{}", sichuanMobileFlowPacketConfig.getLogTag(), phone,
                bizCode, request.toString(),paramInfo);
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-预下单-手机号:{},业务编码:{},返回结果:{}", sichuanMobileFlowPacketConfig.getLogTag(), phone, bizCode, result);
            return mapper.readValue(result, SichuanMobilePrepareOrderResult.class);
        } catch (Exception e) {
            log.info("{}-预下单-手机号:{},业务编码:{},异常:", sichuanMobileFlowPacketConfig.getLogTag(), phone, bizCode, e);
            return SichuanMobilePrepareOrderResult.fail();
        }
    }

    /**
     * 查询订单接口
     *
     * @param orderNo
     * @param bizCode
     * @throws Exception
     */

    //{"result":{"time":"2021-12-24 09:40:40","phone":"151****7190","status":"false","orderId":"1000039020211224094014252349"},"res_code":"0000","res_msg":"成功"}
    //{"result":{"time":"2023-08-25 14:56:56","phone":"139****6117","status":"true","orderId":"1000039720230825145632250228"},"res_code":"0000","res_msg":"成功"}
    public boolean queryOrder(String orderNo, String bizCode,String company){
        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = sichuanMobileFlowPacketProperties.getSichuanMobileConfig(company);
        String url = sichuanMobileFlowPacketProperties.getQueryOrderUrl();
        RequestBody body = new FormBody.Builder()
                .add("order_no", orderNo)
                .add("means_id", bizCode)
                .add("order_type", "B").build();
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(sichuanMobileFlowPacketConfig)
                .build();
        log.info("{}-查询订单-订单号:{},业务编码:{},请求:{}", LOG_TAG, orderNo,
                bizCode, request.toString());
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询订单-订单号:{},业务编码:{},返回结果:{}", LOG_TAG, orderNo, bizCode, result);

            return StringUtils.equals(mapper.readTree(result).at("/result/status").asText("false"),"true");
        } catch (Exception e) {
            log.info("{}-查询订单-订单号:{},业务编码:{},异常:", LOG_TAG, orderNo, bizCode, e);
            return false;
        }
    }


    /**
     *  投放业务留存率分析能力查询(不需要授权)
     *  
     * 1)	该接口是为外部互联网渠道商家提供分触点留存数据查询的功能，该功能强关联预下单接口prepareOrder里的point字段。如渠道侧下单未传入point字段，该接口则无法查询数据。
     * 2)	请求参数的时间是指的查询月份，查询当月截至最新当天该渠道该point的所有业务办理留存情况。往月则是往月全月的当时留存情况。
     * 举例，假设今天是2024年4月29日：
     * 传入4月，返回4月1日-4月28日的全部业务办理号码，在4月28日的留存情况；
     * 传入3月，返回3月1日-3月31日的全部业务办理号码，在3月全月的留存情况；
     * 3)	只能查询最近3个月的留存情况。
     * 4)	该接口数据为日更新，同一入参当日查询结果不会变化，请勿重复调用
     *
     * @param yearMonth  查询年月
     * @param company  公司
     */
    public JsonNode qryPointAnalysi(YearMonth yearMonth, String company){
        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = sichuanMobileFlowPacketProperties.getSichuanMobileConfig(company);
        String url = sichuanMobileFlowPacketProperties.getQryPointAnalysiUrl();
        ;
        RequestBody body = new FormBody.Builder()
                .add("opDate", yearMonth.format(YEAR_MONTH_FORMATTER))
                .add("point", POINT_NUM)
                .build();
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(sichuanMobileFlowPacketConfig)
                .build();
        log.info("{}-查询留存率-yearMonth:{},company:{},请求:{}", LOG_TAG, yearMonth, company, request.toString());
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询留存率-yearMonth:{},company:{},,返回结果:{}", LOG_TAG, yearMonth, company, result);

            return mapper.readTree(result);
        } catch (Exception e) {
            log.info("{}-查询留存率-yearMonth:{},company:{},异常:", LOG_TAG, yearMonth, company, e);
            return mapper.createObjectNode();
        }
    }


    public String generateSign(String data,SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig) throws Exception {
        byte[] appKeyByte = sichuanMobileFlowPacketConfig.getAppKey().getBytes();
        String appKeyStr = org.apache.commons.codec.binary.Hex.encodeHexString(appKeyByte) + Hex.encodeHexString(appKeyByte);
        appKeyStr = appKeyStr.substring(0, 32);
        return AESUtils.aesEncrypt(data, appKeyStr);
    }

    public String decryptResult(String result,String company) {
        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = sichuanMobileFlowPacketProperties.getSichuanMobileConfig(company);
        byte[] appKeyByte = sichuanMobileFlowPacketConfig.getAppKey().getBytes();
        String appKeyStr = org.apache.commons.codec.binary.Hex.encodeHexString(appKeyByte) + Hex.encodeHexString(appKeyByte);
        appKeyStr = appKeyStr.substring(0, 32);
        return AESUtils.aesDecrypt(result, appKeyStr);
    }

    public String generateOrderNo(String prefix,String company) {
        //** 自增序列 *//*
        long sequence = 0;
        if (BizConstant.BIZ_SCMCC_CHANNEL_JUNBO.equals(company)) {
            sequence = redisUtil.incr(CacheConstant.SERIAL_REDIS_JUNBO_KEY, 1);
            if(String.valueOf(sequence).length() > 6){
                redisUtil.del(CacheConstant.SERIAL_REDIS_JUNBO_KEY);
                sequence = redisUtil.incr(CacheConstant.SERIAL_REDIS_JUNBO_KEY, 1);
            }
        } else {
            sequence = redisUtil.incr(CacheConstant.SERIAL_REDIS_KEY, 1);
            if(String.valueOf(sequence).length() > 6){
                redisUtil.del(CacheConstant.SERIAL_REDIS_KEY);
                sequence = redisUtil.incr(CacheConstant.SERIAL_REDIS_KEY, 1);
            }
        }
        String seq = SequenceUtils.getSequence(sequence);
        StringBuilder sb = new StringBuilder();
        sb.append(prefix).append(seq);
        String orderNo = sb.toString();
        return orderNo;
    }





    public static void main(String[] args) throws Exception {

//        Map<String, String> paramInfo = new HashMap<>();
//        paramInfo.put("serial_number", "001");
//        paramInfo.put("phone_no", "173");
//        paramInfo.put("prod_prcid", "aaa");
////        String data = new ObjectMapper().writeValueAsString(paramInfo);
//
////        String data = "a216f94a0dda1db122169ecb86b6bb17f155eed21c46195c6b69cf0b58a9259ae36c6f5ee263965c8a8bac94fe38c422cdeabfb6da0b240aa823310d60376a6a";
//        String data = "6d6920b5e824d8d8afcb65a3e6f0dcfb88c6a47770289eec2f3b3f61513d82fddd8d4703b9f065fe40c397d4b36f65cf234aa6bfd2cdc1b6ad1e8878d4120e6e2d0437547f2a20eae197fa71529df325a8e999df4f0758378a4c2ece2ef609be15239f416829e829ef268e467af13938af52eff6b3aa47b3f33e16150fdfcf6e443dff1de35d629b109889a9b5f4ca09331dff32c079c8c3a42548c4a28f142488a55745fcf7936900b6aaf14eddcd98bb3c0f4840db9ecea2794bf37dbcec2f";
//        byte[] appKeyByte = "$22*0$5&2%2#".getBytes();
//        String appKeyStr = org.apache.commons.codec.binary.Hex.encodeHexString(appKeyByte) + Hex.encodeHexString(appKeyByte);
//        appKeyStr = appKeyStr.substring(0, 32);
//        System.out.println(AESUtils.aesDecrypt(data, appKeyStr));

        String json = "{\"result\":{\"time\":\"2023-08-25 14:56:56\",\"phone\":\"139****6117\",\"status\":\"false\",\"orderId\":\"1000039720230825145632250228\"},\"res_code\":\"0000\",\"res_msg\":\"成功\"}\n";
        boolean equals = StringUtils.equals(new ObjectMapper().readTree(json).at("/result/status").asText("false"), "true");
        System.out.println(equals);

//        System.out.println(decryptResult("a216f94a0dda1db122169ecb86b6bb171299896c52c242cb2474f28a9247452f315a5756818c0f5af13b4aa441076a88cdeabfb6da0b240aa823310d60376a6a"));


//        ObjectMapper objectMapper = new ObjectMapper();
////        String jsonStr = "{\"result\":[{\"effetive\":true,\"beginTime\":\"2021-07-31 22:00:00\",\"upgradeContent\":\"尊敬的客户，2021年7月31日22:00至8月1日08:00系统例行维护，期间将影响费用查询、业务办理、充值缴费、实时开停机、提醒服务等业务，带来的不便敬请谅解。\",\"endTime\":\"2021-08-01 08:00:00\",\"upgradeId\":\"SC-100-20210727-55301\"}],\"res_code\":\"0000\",\"res_msg\":\"成功\"}";
//        String jsonStr = "{\"result\":[],\"res_code\":\"0000\",\"res_msg\":\"成功\"}";
//        JsonNode tree = objectMapper.readTree(jsonStr);
//        JsonNode notices = tree.get("result");
//        for (int i = 0; i < notices.size(); i++) {
//            System.out.println(notices.get(i).get("upgradeContent").asText());
//        }
    }


}
