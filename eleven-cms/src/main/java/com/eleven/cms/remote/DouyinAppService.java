package com.eleven.cms.remote;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.DouyinAppProperties;
import com.eleven.cms.config.MiguChannelConfig;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.IMusicService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Cryptos;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.VrbtCombinResult;
import com.eleven.cms.vo.VrbtFunAndMonthStatus;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Streams;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

import static com.eleven.cms.vo.VrbtCombinResult.*;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:46
 * Desc: 抖音小程序相关接口
 */
@Slf4j
@Service
public class DouyinAppService {
    private static final String LOG_TAG = "抖音小程序";

    @Autowired
    private DouyinAppProperties douyinAppProperties;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private Environment environment;
    @Qualifier("threadPoolExecutor")
    private ThreadPoolExecutor executor;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;

    @Autowired
    private IMemberService memberService;

    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                                     //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                                     // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper();
        this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }



    /**
     * 通过login接口获取到登录凭证后，开发者可以通过服务器发送请求的方式获取 session_key 和 openId。
     *
     * @param code 抖音js登录返回的code
     * @return
     */
    public @Nonnull Result<?> code2Session(String code) {
        HttpUrl httpUrl = HttpUrl.parse(douyinAppProperties.getCode2SessionUrl()).newBuilder()
                .addQueryParameter("appid", douyinAppProperties.getAppId())
                .addQueryParameter("secret", douyinAppProperties.getAppSecret())
                .addQueryParameter("code", code)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}code2Session请求=>{}", LOG_TAG, request);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String content  = response.body().string();
            log.info("{}code2Session响应=>{}", LOG_TAG, content);

            JsonNode tree = mapper.readTree(content);

            if(tree.at("/error").asLong()!=0){
                return Result.error(tree.at("/message").asText());
            }

            return Result.ok(tree.at("/openid").asText(),tree.at("/session_key").asText());

        } catch (IOException e) {
            log.warn("{}code2Session接口调用异常:",LOG_TAG,e);
            return Result.error(LOG_TAG + "接口调用异常");
        }
    }



    /**
     * https://microapp.bytedance.com/docs/zh-CN/mini-app/develop/component/acquire-phone-number-acquire/
     * js获取用户绑定的手机号以后到服务端解密
     *  解密敏感数据
     * 对称解密使用的算法为AES-128-CBC，数据采用PKCS#7填充。
     * 对称解密的目标密文为encryptedData。
     * 对称解密秘钥aeskey = Base64_Decode(session_key), aeskey长度为 16Byte。
     * 对称解密算法初始向量为Base64_Decode(iv)。
     *
     *
     * 参考敏感数据处理在开发者后台解密。解密后获取得到的数据形式如下：
     * {
     *   "phoneNumber": "138xxxxxxxx", // 用户绑定的手机号（国外手机号会有区号）
     *   "purePhoneNumber": "138xxxxxxxx", // 没有区号的手机号
     *   "countryCode": "86", // 区号
     *   "watermark": {
     *     "appid": "ttxxxxxxxxxxxxxxxx",
     *     "timestamp": 15000000000000000
     *   }
     * }
     *
     * @param encryptedData 加密的数据
     * @param code js前端登录后获取的code,可以通过请求接口换成session_key来作为aes的密钥
     * @param iv 加密向量
     * @return
     */
    public @Nonnull Result<?> phoneNumber(String encryptedData, String code, String iv) {

        /*Result<?> result = code2Session(code);
        if(!result.isOK()){
            return result;
        }*/
        try {
            String sessionKey = code;

            String decryptedData = Cryptos.aesDecrypt(Base64.getDecoder().decode(encryptedData), Base64.getDecoder().decode(sessionKey), Base64.getDecoder().decode(iv));
            JsonNode tree = mapper.readTree(decryptedData);

            return Result.ok("处理成功", tree.at("/purePhoneNumber"));

        } catch (Exception e) {
            log.warn("{}数据解密异常:",LOG_TAG,e);
            return Result.error(LOG_TAG + "数据解密异常");
        }
    }

    /**
     *
     * @param encryptedData 加密的数据
     * @param code js前端登录后获取的code,可以通过请求接口换成session_key来作为aes的密钥
     * @param iv 加密向量
     * @return
     */
    public @Nonnull FebsResponse phoneNumberForRights(String encryptedData, String code, String iv) {

        try {
            Map<String, String> map = new HashMap<>();
            String sessionKey = code;

            String decryptedData = Cryptos.aesDecrypt(Base64.getDecoder().decode(encryptedData), Base64.getDecoder().decode(sessionKey), Base64.getDecoder().decode(iv));
            JsonNode tree = mapper.readTree(decryptedData);
            //调用接口获取token
            String phone = tree.at("/purePhoneNumber").asText();
            String token = memberService.douyinMemberLogin(phone,BizConstant.BIZ_QYLI_ALL_CHANNEL_CODE).get("data").toString();
            map.put("phone",phone);
            map.put("token",token);
            return new FebsResponse().success().data(map);
        } catch (Exception e) {
            log.warn("{}数据解密异常:",LOG_TAG,e);
            return new FebsResponse().fail().error("数据解密异常:" + e);
        }
    }


}
