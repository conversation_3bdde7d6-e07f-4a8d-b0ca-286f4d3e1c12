package com.eleven.cms.remote;

import com.eleven.cms.annotation.IvrAlipayUnsubscribeLimit;
import com.eleven.cms.annotation.IvrUnsubscribeLimit;
import com.eleven.cms.config.LiantongVrbtProperties;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.service.IAliSignRecordService;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.LiantongResp;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.IvrResult;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Stream;

/**
 * Author: <EMAIL>
 * Date: 2021/7/9 16:26
 * Desc: 业务退订
 */
@Service
@Slf4j
public class BizUnsubscribeService {

    public static final String LOG_TAG = "业务退订";

    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private DianxinVrbtService dianxinVrbtService;
    @Autowired
    private MobileRegionService mobileRegionService;
    @Autowired
    private LiantongVrbtService liantongVrbtService;
    @Autowired
    private LiantongVrbtProperties liantongVrbtProperties;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    IEsDataService esDataService;
    @Autowired
    IAliSignRecordService aliSignRecordService;

    public static final ObjectMapper MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 发送联通退订网址短信[FOR IVR]
     *
     * @param callNo
     *         手机号
     * @return
     */
    public Result<?> sendUnicomUnsubNotice(String callNo) {
        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        datangSmsService.sendUnicomUnsubNotice(callNo);
        return Result.bizConfirm("退订短信已发送,请按短信提示操作");
    }

    /**
     * 联通视频彩铃退订[FOR IVR]
     *
     * @param callNo
     *         手机号
     * @return
     */
    public Result<?> cuccCancelVrbtPack(String callNo) {
        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        if (!liantongVrbtService.isSubedMon(callNo,BizConstant.BIZ_LT_CHANNEL_DEFAULT)) {
            return Result.error("您未订购酷炫彩铃包月业务,无需退订");
        }
        LiantongResp liantongResp = liantongVrbtService.unSubProductNoToken(callNo,BizConstant.BIZ_LT_CHANNEL_DEFAULT);
        return liantongResp.isOK() ? Result.ok("退订成功") : Result.error("退订失败");
    }

    /**
     * 电信视频彩铃退订[FOR IVR]
     *
     * @param callNo
     *         手机号
     * @return
     */
    @IvrUnsubscribeLimit(msgContent = "【休闲集舍】电信视频彩铃退订", count = 200)
    public Result<?> ctccCancelVrbtPack(String callNo) {
        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        /*String content = */dianxinVrbtService.unSubscribeByemp(callNo,BizConstant.BIZ_DIANXIN_CHANNEL_HSTJ);
        /*String content = */dianxinVrbtService.unSubscribeByemp(callNo,BizConstant.BIZ_DIANXIN_CHANNEL_GUANGMINGWANG);
        /*String content = */dianxinVrbtService.unSubscribeByemp(callNo,BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_6);
        //try {
        //    JsonNode tree = MAPPER.readTree(content);
        //    int resCode = tree.at("/BasicJTResponse/res_code").asInt(99);
        //    String resMessage = tree.at("/BasicJTResponse/res_message").asText();
        //    return resCode == 0 ? Result.ok(resMessage) : Result.error(resMessage);
        //} catch (JsonProcessingException e) {
        //    e.printStackTrace();
        //    return Result.error(content);
        //}
        return Result.ok("已完成退订操作");
    }

    /**
     * 移动视频彩铃退订,含开放平台的订阅/渠道,以及彩铃中心的订阅[FOR IVR]
     *
     * @param callNo
     *         手机号
     * @return
     */
    @IvrUnsubscribeLimit(msgContent = "【休闲集舍】移动视频彩铃退订,含开放平台的订阅/渠道,以及彩铃中心的订阅", count = 750)
    public Result<?> cmccCancelVrbtPack(String callNo) {

        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }

        //退彩铃中心这边的包月(炫视)
        RemoteResult statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CENTRALITY_CHANNEL_CODE, true);
//        if (!statusResultCentrality.isOK()) {
//            return Result.error("接口通讯失败");
//        }
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.CENTRALITY_CHANNEL_CODE);
//            return Result.ok("[订阅侧业务]退订成功");
            //return Result.ok("暂不支持订阅侧业务退订");
        }
        //退彩铃中心这边的包月(川网)
        statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CHUANWANG_CHANNEL_CODE, true);
//        if (!statusResultCentrality.isOK()) {
//            return Result.error("接口通讯失败");
//        }
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.CHUANWANG_CHANNEL_CODE);
//            return Result.ok("[订阅侧业务]退订成功");
            //return Result.ok("暂不支持订阅侧业务退订");
        }
        //退彩铃中心这边的包月(高姐超炫)
        statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CHAOXUAN_CHANNEL_CODE, true);
//        if (!statusResultCentrality.isOK()) {
//            return Result.error("接口通讯失败");
//        }
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.CHAOXUAN_CHANNEL_CODE);
//            return Result.ok("[订阅侧业务]退订成功");
            //return Result.ok("暂不支持订阅侧业务退订");
        }
        //退彩铃中心这边的包月(科大讯飞)
        statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.XUNFEI_CHANNEL_CODE, true);
        //        if (!statusResultCentrality.isOK()) {
        //            return Result.error("接口通讯失败");
        //        }
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.XUNFEI_CHANNEL_CODE);
            //            return Result.ok("[订阅侧业务]退订成功");
            //return Result.ok("暂不支持订阅侧业务退订");
        }

        //再退订开放平台这边的包月
        RemoteResult statusResult = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CH_DYB_DEFAULT, true);
//        if (!statusResult.isOK()) {
//            return Result.error("接口通讯失败");
//        }
        //如果有包月就退
        switch (statusResult.getStatus()) {
            case "1":
                Stream.of("00210QZ","00210OC").anyMatch(ch-> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                break;
            case "2":
                Stream.of("00210QY","00210OW").anyMatch(ch-> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                break;
            case "3":
                Stream.of("00210QZ","00210OC").anyMatch(ch-> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                Stream.of("00210QY","00210OW").anyMatch(ch-> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                break;
            //上述开放平台侧已包月的就直接返回了,不用退订彩铃中心的了,因为两边的视频彩铃包月业务是互斥的
            case "0":
                break;
        }
        return Result.ok("退订完成");
    }

    /**
     * 联通,电信,移动 公用退订入口
     *
     * @param callNo
     *         手机号
     * @return
     */
    @IvrUnsubscribeLimit(msgContent = "【休闲集舍】联通,电信,移动视频彩铃包月公用退订", count = 750)
    public Result<?> commonCancelVrbtPack(String callNo) {
        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(callNo);
        if(mobileRegionResult==null||!mobileRegionResult.isSuccess()){
            return Result.error("查询手机号运营商失败");
        }
        //联通直接退订
        if(mobileRegionResult.isIspLiantong()){
            return this.cuccCancelVrbtPack(callNo);
        }else if(mobileRegionResult.isIspDianxin()) {
            return this.ctccCancelVrbtPack(callNo);
        } else {
            return this.cmccCancelVrbtPack(callNo);
        }
    }

//    /**
//     * 退订所有业务
//     * @param callNo
//     * @param requestWay 请求方式 ivr、用户大唐短信上行、客服手动操作
//     * @return
//     */
//    @IvrUnsubscribeLimit
//    public Result allBizCancel(String callNo, String requestWay) {
//        log.info("{}-退订所有业务-手机号:{},请求方式:{}", LOG_TAG ,callNo, requestWay);
//        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
//            return Result.noauth("请输入正确格式手机号");
//        }
//        MobileRegionResult mobileRegionResult = mobileRegionService.query(callNo);
//        switch (mobileRegionResult.getOperator()){
//            case MobileRegionResult.ISP_YIDONG:
//                cmccCancel(callNo);
//                break;
//            case MobileRegionResult.ISP_LIANTONG:
//                unicomCancel(callNo);
//                break;
//            case MobileRegionResult.ISP_DIANXIN:
//                telecomCancel(callNo);
//                break;
//        }
//        return Result.ok("退订完成");
//    }

    /**
     * 退订传入esSubscribes的所有业务(根据es查询订购记录)
     * @param callNo
     * @param requestWay 请求方式 ivr、用户大唐短信上行、客服手动操作
     * @return
     */
    @IvrUnsubscribeLimit(msgContent = "【休闲集舍】联通,电信,移动全业务包月公用退订",count = 1500)
    public Result unsubscribeAllBizByHistory(String callNo, List<EsSubscribe> esSubscribes, String requestWay) {
        log.info("{}-退订所有业务(根据订购记录)-手机号:{},请求方式:{}", LOG_TAG ,callNo, requestWay);
        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }

        for (EsSubscribe esSubscribe : esSubscribes) {
            final String isp = esSubscribe.getIsp();
            final String bizType = esSubscribe.getBizType();
            final String channelCode = esSubscribe.getChannel();
            final String serviceId = esSubscribe.getServiceId();
            if (MobileRegionResult.ISP_YIDONG.equals(isp)) {
                if (BizConstant.BIZ_TYPE_VRBT.equals(bizType)) {
                    //视频彩铃退订
                    RemoteResult vrbtStatusResult = miguApiService.vrbtMonthStatusQuery(callNo, channelCode, true);
                    if (vrbtStatusResult.isOK() && !"0".equals(vrbtStatusResult.getStatus())) {
                        final RemoteResult result = miguApiService.vrbtUnsubscribe(callNo, channelCode);
                        if (result.isOK()) {
                            esDataService.saveUnsubLog(esSubscribe,result.expr(), requestWay);
                        }
                    }
                } else if (BizConstant.BIZ_TYPE_BJHY.equals(bizType)) {
                    //白金会员退订
                    RemoteResult bjhyResult = miguApiService.bjhyQuery(callNo, channelCode);
                    if (bjhyResult.isBjhyMember()) {
                        final RemoteResult result = miguApiService.bjhyCancel(callNo, channelCode);
                        if (result.isOK()) {
                            esDataService.saveUnsubLog(esSubscribe,result.expr(), requestWay);
                        }
                    }
                } else if (BizConstant.BIZ_TYPE_CPMB.equals(bizType)) {
                    //渠道包退订
                    RemoteResult cpmbResult = miguApiService.cpmbQuery(callNo, channelCode);
                    if (cpmbResult.isCpmbMember()) {
                        final RemoteResult result = miguApiService.cpmbCancel(callNo, channelCode);
                        if (result.isOK()) {
                            esDataService.saveUnsubLog(esSubscribe,result.expr(), requestWay);
                        }
                    }
                } else if (BizConstant.BIZ_TYPE_UNION_MEMBER.equals(bizType)) {
                    //藕粉同享会联合会员退订
                    RemoteResult asMemberResult = miguApiService.asMemberQuery(callNo, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE);
                    if(asMemberResult.isAsMember()) {
                        final RemoteResult result = miguApiService.asMemberCancel(callNo, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE);
                        if (result.isOK()) {
                            esDataService.saveUnsubLog(esSubscribe,result.expr(), requestWay);
                        }
                    }
                } else if (BizConstant.BIZ_TYPE_BAIDU.equals(bizType) || BizConstant.BIZ_TYPE_WANGYIYUN.equals(bizType)) {
                    //百度联合退订/网易云联合会员退订
                    RemoteResult unionMemberResult = innerUnionMemberService.unionQuery(callNo, channelCode);
                    if (unionMemberResult.isBjhyMember()) {
                        final RemoteResult result = innerUnionMemberService.unionCancel(callNo, channelCode);
                        if (result.isOK()) {
                            esDataService.saveUnsubLog(esSubscribe,result.expr(), requestWay);
                        }
                    }
                    //咪咕阅读暂时无法直接通过接口退订
                }/*else if(BizConstant.BIZ_TYPE_READ.equals(bizType)){
                    //咪咕阅读退订
                    Result result = innerUnionMemberService.readQuery(callNo);
                    if (result.isOK() && "已订购".equals(result.getMessage())) {
                        innerUnionMemberService.readCancel(callNo);
                    }
                }*/
            } else if (MobileRegionResult.ISP_DIANXIN.equals(isp)) {
                //电信视频彩铃退订
                if (dianxinVrbtService.queryPackageExist(callNo, serviceId)) {
                    final String resp = dianxinVrbtService.unSubscribeByemp(callNo, serviceId);
                    esDataService.saveUnsubLog(esSubscribe, resp, requestWay);
                }
                //天翼空间20元退订 业务已停止合作
                //Result result = innerUnionMemberService.tykjQuery(callNo);
                //if (result.isOK()) {
                //    innerUnionMemberService.tykjCancel(callNo);
                //}
            } else if (MobileRegionResult.ISP_LIANTONG.equals(isp)) {
                //联通视频彩铃退订
                final String company = liantongVrbtProperties.getCompanyByProductId(serviceId);
                if (liantongVrbtService.isSubedMon(callNo, company)) {
                    final LiantongResp liantongResp = liantongVrbtService.unSubProductNoToken(callNo, company);
                    esDataService.saveUnsubLog(esSubscribe, liantongResp.expr(), requestWay);
                }
                //喜马拉雅视频彩铃退订  业务已停止合作
                //innerUnionMemberService.xmlyCancel(callNo);
            }
        }
        return Result.ok("退订完成");
    }

    /**
     * 退订最近90
     * @param mobile
     * @param requestWay
     * @return
     */
    public Result unsubscribeRecent90Days(String mobile, String requestWay) {
        //退订最近90天的
        final List<EsSubscribe> esSubscribes = esDataService.queryRecentAllBizByMobile(mobile, 90L);
        return unsubscribeAllBizByHistory(mobile, esSubscribes, requestWay);
    }

    /**
     * 取消移动业务
     *
     * @param callNo
     */
    public void cmccCancel(String callNo) {

        //视频彩铃退订
        //退彩铃中心这边的包月(炫视)
        RemoteResult statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CENTRALITY_CHANNEL_CODE, true);
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.CENTRALITY_CHANNEL_CODE);
        }
        //退彩铃中心这边的包月(川网)
        statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CHUANWANG_CHANNEL_CODE, true);
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.CHUANWANG_CHANNEL_CODE);
        }
        //退彩铃中心这边的包月(高姐超炫)
        statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CHAOXUAN_CHANNEL_CODE, true);
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.CHAOXUAN_CHANNEL_CODE);
        }
        //退彩铃中心这边的包月(科大讯飞)
        statusResultCentrality = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.XUNFEI_CHANNEL_CODE, true);
        if (!"0".equals(statusResultCentrality.getStatus())) {
            miguApiService.vrbtUnsubscribe(callNo, MiguApiService.XUNFEI_CHANNEL_CODE);
        }

        //再退订开放平台这边的包月
        RemoteResult statusResult = miguApiService.vrbtMonthStatusQuery(callNo, MiguApiService.CH_DYB_DEFAULT, true);
        //如果有包月就退
        switch (statusResult.getStatus()) {
            case "1":
                Stream.of("00210QZ", "00210OC").anyMatch(ch -> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                break;
            case "2":
                Stream.of("00210QY", "00210OW").anyMatch(ch -> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                break;
            case "3":
                Stream.of("00210QZ", "00210OC").anyMatch(ch -> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                Stream.of("00210QY", "00210OW").anyMatch(ch -> miguApiService.vrbtUnsubscribe(callNo, ch).isOK());
                break;
            //上述开放平台侧已包月的就直接返回了,不用退订彩铃中心的了,因为两边的视频彩铃包月业务是互斥的
            case "0":
                break;
        }

        //白金会员退订
        RemoteResult bjhyResult = miguApiService.bjhyQuery(callNo, BizConstant.BJHY_CHANNEL_CODE_PP);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, BizConstant.BJHY_CHANNEL_CODE_PP);
        }
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHYYS_CHANNEL_CODE);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHYYS_CHANNEL_CODE);
        }
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHYDY_CHANNEL_CODE);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHYDY_CHANNEL_CODE);
        }
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1);
        }
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHY_CHANNEL_CODE_U2);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHY_CHANNEL_CODE_U2);
        }
        //组合包
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
        }
        //20元cpmb包月退订
        RemoteResult cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1);
        }
        //20元酷狗渠道包退订
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_KUGOU);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_KUGOU);
        }
        //10元cpmb包月退订 七彩歌曲10元包（音乐全曲包）
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_10_CHANNEL_CODE_VO);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_10_CHANNEL_CODE_VO);
        }
        //15元渠道包月包 光明网渠道包m
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_W5);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_W5);
        }
        //音乐全曲包-光明网15元包
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_0A);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_0A);
        }
        //音乐全曲包-七彩音乐30元包
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_30_CHANNEL_CODE);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_30_CHANNEL_CODE);
        }
        //20元渠道包
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_3);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_3);
        }

        //藕粉同享会联合会员退订
        RemoteResult asMemberResult = miguApiService.asMemberQuery(callNo, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE);
        if(asMemberResult.isAsMember()) {
            miguApiService.asMemberCancel(callNo, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE);
        }
        //联合会员退订
        RemoteResult unionMemberResult = innerUnionMemberService.unionQuery(callNo,"00210M5");
        if (unionMemberResult.isBjhyMember()) {
            innerUnionMemberService.unionCancel(callNo,"00210M5");
        }
        //网易云会员退订
        unionMemberResult = innerUnionMemberService.unionQuery(callNo,"00210QL");
        if (unionMemberResult.isBjhyMember()) {
            innerUnionMemberService.unionCancel(callNo,"00210QL");
        }
        //咪咕阅读 阅读已不支持直接退订,需要短信
        //Result result = innerUnionMemberService.readQuery(callNo);
        //if (result.isOK() && "已订购".equals(result.getMessage())) {
        //    innerUnionMemberService.readCancel(callNo);
        //}

    }

    /**
     * 取消联通业务
     *
     * @param callNo
     */
    public void unicomCancel(String callNo) {
        //视频彩铃退订
        if (liantongVrbtService.isSubedMon(callNo, BizConstant.BIZ_LT_CHANNEL_HONGSHENG)) {
            liantongVrbtService.unSubProductNoToken(callNo, BizConstant.BIZ_LT_CHANNEL_HONGSHENG);
        }
        if (liantongVrbtService.isSubedMon(callNo, BizConstant.BIZ_LT_CHANNEL_RUIJIN)) {
            liantongVrbtService.unSubProductNoToken(callNo, BizConstant.BIZ_LT_CHANNEL_RUIJIN);
        }
        if (liantongVrbtService.isSubedMon(callNo, BizConstant.BIZ_LT_CHANNEL_RUIMEI)) {
            liantongVrbtService.unSubProductNoToken(callNo, BizConstant.BIZ_LT_CHANNEL_RUIMEI);
        }
        //喜马拉雅视频彩铃退订 业务已停止合作
        //innerUnionMemberService.xmlyCancel(callNo);
    }

    /**
     * 取消电信业务
     *
     * @param callNo
     */
    public void telecomCancel(String callNo) {
        //电信视频彩铃退订
        if (dianxinVrbtService.queryPackageExist(callNo, BizConstant.BIZ_DIANXIN_CHANNEL_HSTJ)) {
            dianxinVrbtService.unSubscribeByemp(callNo, BizConstant.BIZ_DIANXIN_CHANNEL_HSTJ);
        }
        if (dianxinVrbtService.queryPackageExist(callNo, BizConstant.BIZ_DIANXIN_CHANNEL_GUANGMINGWANG)) {
            dianxinVrbtService.unSubscribeByemp(callNo, BizConstant.BIZ_DIANXIN_CHANNEL_GUANGMINGWANG);
        }
        if (dianxinVrbtService.queryPackageExist(callNo, BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE)) {
            dianxinVrbtService.unSubscribeByemp(callNo, BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE);
        }
        //天翼空间20元退订 业务已停止合作
        //Result result = innerUnionMemberService.tykjQuery(callNo);
        //if (result.isOK()) {
        //    innerUnionMemberService.tykjCancel(callNo);
        //}
    }

    /**
     * 咪咕音乐业务退订(北岸唐唱+白金会员退订+咪咕同享会联合会员)[FOR IVR]
     *
     * @param callNo 手机号
     * @return
     */
    @IvrUnsubscribeLimit(msgContent = "【休闲集舍】咪咕音乐业务退订",count = 750)
    public Result<?> cmccCancelMiguMusicBiz(String callNo) {
        if (Strings.isNullOrEmpty(callNo) || !callNo.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        //渠道包退订
        RemoteResult cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1);
        }
        //20元酷狗渠道包退订
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_KUGOU);
        if (cpmbResult.isOK()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_KUGOU);
        }
        //10元渠道包月包 七彩歌曲10元包（音乐全曲包）
        cpmbResult = miguApiService.cpmbQuery(callNo, MiguApiService.BIZ_CPMB_10_CHANNEL_CODE_VO);
        if (cpmbResult.isCpmbMember()) {
            miguApiService.cpmbCancel(callNo, MiguApiService.BIZ_CPMB_10_CHANNEL_CODE_VO);
        }
        //白金会员退订
        RemoteResult bjhyResult = miguApiService.bjhyQuery(callNo, BizConstant.BJHY_CHANNEL_CODE_PP);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, BizConstant.BJHY_CHANNEL_CODE_PP);
        }
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHYYS_CHANNEL_CODE);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHYYS_CHANNEL_CODE);
        }
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHYDY_CHANNEL_CODE);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHYDY_CHANNEL_CODE);
        }
        //组合包
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
        }
        //讯飞组合包
        bjhyResult = miguApiService.bjhyQuery(callNo, MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XD);
        if (bjhyResult.isBjhyMember()) {
            miguApiService.bjhyCancel(callNo, MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_XUNFEI_XD);
        }
        //藕粉同享会联合会员退订
        RemoteResult asMemberResult = miguApiService.asMemberQuery(callNo, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE);
        if(asMemberResult.isAsMember()) {
            miguApiService.asMemberCancel(callNo, MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE);
        }

        return Result.ok("业务退订成功");
    }

    /**
     * 支付宝根据手机号解约
     * @param mobile
     * @return
     */
    @IvrAlipayUnsubscribeLimit(msgContent = "【休闲集舍】支付宝业务退订",count = 750)
    public Result<?> alipayAgreementUnsign(String mobile) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        return aliSignRecordService.aliUnSign(mobile);
    }
    /**
     * 支付宝根据手机号查询解约和退款
     * @param mobile
     * @return
     */
    public IvrResult alipayQuerySignAndRefund(String mobile,String bizType) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return IvrResult.response(500,"请输入正确格式手机号");
        }

        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(!StringUtils.equals(bizType,BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspYidong()){
            bizType=BizConstant.BIZ_TYPE_VRBT_YD_ALIPAY;
        }else if(!StringUtils.equals(bizType,BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspDianxin()){
            bizType=BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY;
        }else if(!StringUtils.equals(bizType,BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspLiantong()){
            bizType=BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY;
        }
        if (Strings.isNullOrEmpty(bizType)) {
            log.info("ivr查询号码归属地失败-->手机号:{},业务类型:{},结果:{}",mobile,bizType,mobileRegionResult);
            return IvrResult.response(500,"系统繁忙,请稍后再试!");
        }
        return aliSignRecordService.alipayQuerySignAndRefund(mobile,bizType);
    }
    /**
     * 支付宝根据手机号解约并退款
     * @param mobile
     * @return
     */
    @IvrAlipayUnsubscribeLimit(msgContent = "【休闲集舍】支付宝业务退订",count = 750)
    public IvrResult alipayUnSignAndRefund(String mobile,String bizType) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return IvrResult.response(500,"请输入正确格式手机号");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(!StringUtils.equals(bizType,BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspYidong()){
            bizType=BizConstant.BIZ_TYPE_VRBT_YD_ALIPAY;
        }else if(!StringUtils.equals(bizType,BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspDianxin()){
            bizType=BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY;
        }else if(!StringUtils.equals(bizType,BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspLiantong()){
            bizType=BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY;
        }
        if (Strings.isNullOrEmpty(bizType)) {
            log.info("ivr查询号码归属地失败-->手机号:{},业务类型:{},结果:{}",mobile,bizType,mobileRegionResult);
            return IvrResult.response(500,"系统繁忙,请稍后再试!");
        }
        return aliSignRecordService.alipayUnSignAndRefund(mobile,bizType);
    }

}
