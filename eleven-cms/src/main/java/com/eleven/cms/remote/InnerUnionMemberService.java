package com.eleven.cms.remote;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.config.InnerUnsubProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.InnerUnionMemberResult;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.SichuanDianxinVrbtResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * @author: cai lei
 * @create: 2022-06-06 14:46
 */
@Slf4j
@Service
public class InnerUnionMemberService {
    public static final String LOG_TAG = "内部联和会员转发-api";
    @Value("${innerForwardUrl}")
    private String innerForwardUrl;
    @Value("${innerOutsideForwardUrl}")
    private String innerOutsideForwardUrl;
    @Value("${innerOutsideForwardOrderUrl}")
    private String innerOutsideForwardOrderUrl;
    @Value("${commonFilterCheckUrl}")
    private String commonFilterCheckUrl;
    @Value("${commonGetSmsCodeUrl}")
    private String commonGetSmsCodeUrl;
    @Value("${commonSubmitCodeUrl}")
    private String commonSubmitCodeUrl;

    @Autowired
    private InnerUnsubProperties innerUnsubProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public Result forword(Subscribe subscribe, String channel) throws JsonProcessingException {
        subscribe.setCreateTime(null); //将值设置为NULL,防止两边字段类型不一致报错
        subscribe.setChannel(channel); //设置渠道号为LXYT
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(subscribe));
        Request request = new Request.Builder().url(innerForwardUrl)
                .post(body)
                .build();
        log.info("{}-手机号:{},请求:{}", LOG_TAG, subscribe.getMobile(), request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-手机号:{},返回结果:{}", LOG_TAG, subscribe.getMobile(), result);
            JsonNode dataNode = mapper.readTree(result);
            String data = dataNode.at("/data").asText();
            Result returnResult = mapper.readValue(result, Result.class);
            returnResult.setResult(data);
            return returnResult;
        } catch (Exception e) {
            log.info("{}-手机号:{},异常:", LOG_TAG, subscribe.getMobile(), e);
            return Result.error("系统错误");
        }
    }

    public Result outsideForword(String mobile, String subChannel) throws JsonProcessingException {

        RequestBody requestBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("subChannel", subChannel)
                .build();
        Request request = new Request.Builder().url(innerOutsideForwardUrl)
                .post(requestBody)
                .build();
        log.info("外部调用-{}-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("外部调用-{}-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            Result returnResult = mapper.readValue(result, Result.class);
            if (returnResult.isOK()) {
                JsonNode jsonNode = mapper.readTree(result);
                returnResult.setResult(jsonNode.at("/data").asLong());
            }
            return returnResult;
        } catch (Exception e) {
            log.info("外部调用-{}-手机号:{},异常:", LOG_TAG, mobile, e);
            return Result.error("系统错误");
        }
    }

    public Result outsideForwordOrder(String mobile, String code, String orderNo, String subChannel) {

        RequestBody requestBody = new FormBody.Builder()
                .add("mobile", mobile)
                .add("code", code)
                .add("orderNo", orderNo)
                .add("subChannel", subChannel)
                .build();
        Request request = new Request.Builder().url(innerOutsideForwardOrderUrl)
                .post(requestBody)
                .build();
        log.info("外部调用提交验证码-{}-手机号:{},订单号:{},请求:{}", LOG_TAG, mobile, orderNo, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("外部调用提交验证码-{}-手机号:{},订单号:{},返回结果:{}", LOG_TAG, mobile, orderNo, result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("外部调用提交验证码-{}-手机号:{},订单号:{},异常:", LOG_TAG, mobile, orderNo, e);
            return Result.error("系统错误");
        }
    }


    public RemoteResult unionQuery(String mobile, String channel) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getUnionCancelUrl())
                .newBuilder()
                .addQueryParameter("mobile", mobile)
                .addQueryParameter("channel", channel)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-查询联合会员包月状态-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询联合会员包月状态-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, RemoteResult.class);
        } catch (Exception e) {
            log.info("{}-查询联合会员包月状态-手机号:{},异常:", LOG_TAG, mobile, e);
            return RemoteResult.fail("系统错误");
        }
    }

    public RemoteResult unionCancel(String mobile, String channel) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getUnionQueryUrl())
                .newBuilder()
                .addQueryParameter("mobile", mobile)
                .addQueryParameter("channel", channel)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-联合会员退订-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-联合会员退订-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, RemoteResult.class);
        } catch (Exception e) {
            log.info("{}-联合会员退订-手机号:{},异常:", LOG_TAG, mobile, e);
            return RemoteResult.fail("系统错误");
        }
    }

    public Result tykjQuery(String mobile) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getTykjQueryUrl())
                .newBuilder()
                .addQueryParameter("phone", mobile)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-查询天翼空间20元包月状态-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询天翼空间20元包月状态-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-查询天翼空间20元包月状态-手机号:{},异常:", LOG_TAG, mobile, e);
            return Result.error("系统错误");
        }
    }

    public Result tykjCancel(String mobile) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getTykjCancelUrl())
                .newBuilder()
                .addQueryParameter("phone", mobile)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-天翼空间20元包月退订-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-天翼空间20元包月退订-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-天翼空间20元包月退订-手机号:{},异常:", LOG_TAG, mobile, e);
            return Result.error("系统错误");
        }
    }

    public Result readQuery(String mobile) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getReadQueryUrl())
                .newBuilder()
                .addQueryParameter("mobile", mobile)
                .build();
        Request request = new Request.Builder().url(httpUrl).post(RequestBody.create(null, new byte[0])).build();
        log.info("{}-查询咪咕阅读包月状态-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询咪咕阅读包月状态-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-查询咪咕阅读包月状态-手机号:{},异常:", LOG_TAG, mobile, e);
            return Result.error("系统错误");
        }
    }

    public Result readCancel(String mobile) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getReadCancelUrl())
                .newBuilder()
                .addQueryParameter("mobile", mobile)
                .build();
        Request request = new Request.Builder().url(httpUrl).post(RequestBody.create(null, new byte[0])).build();
        log.info("{}-咪咕阅读包月退订-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-咪咕阅读包月退订-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-咪咕阅读包月退订-手机号:{},异常:", LOG_TAG, mobile, e);
            return Result.error("系统错误");
        }
    }


    public Result xmlyCancel(String mobile) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getXmlyCancelUrl())
                .newBuilder()
                .addQueryParameter("phone", mobile)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-喜马拉雅会员包月退订-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-喜马拉雅会员包月退订-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-喜马拉雅会员包月退订-手机号:{},异常:", LOG_TAG, mobile, e);
            return Result.error("系统错误");
        }
    }

    public Integer queryRemoteOrderStatus(String mobile, String channel, String bizType) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getQueryRemoteOrderStatusUrl())
                .newBuilder()
                .addQueryParameter("mobile", mobile)
                .addQueryParameter("channel", channel)
                .addQueryParameter("bizType", bizType)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-远程业务订购状态查询-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-远程业务订购状态查询-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            JsonNode tree = mapper.readTree(result);
            return tree.at("/data").asInt();
        } catch (Exception e) {
            log.info("{}-远程业务订购状态查询-手机号:{},异常:", LOG_TAG, mobile, e);
            return -1;
        }
    }


    public String cancelRemoteOrder(String mobile, String channel, String bizType) {
        final HttpUrl httpUrl = HttpUrl.parse(innerUnsubProperties.getCancelRemoteOrderUrl())
                .newBuilder()
                .addQueryParameter("mobile", mobile)
                .addQueryParameter("channel", channel)
                .addQueryParameter("bizType", bizType)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-远程业务退订-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-远程业务退订-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            JsonNode tree = mapper.readTree(result);
            return tree.at("/message").asText();
        } catch (Exception e) {
            log.info("{}-远程业务退订-手机号:{},异常:", LOG_TAG, mobile, e);
            return "退订失败，请稍后再试";
        }
    }


    public Result callFilterCheck(Subscribe subscribe, String beanName) throws JsonProcessingException {
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(subscribe));
        Request request = new Request.Builder().url(commonFilterCheckUrl)
                .header("x-requested-with", StringUtils.isNotBlank(subscribe.getReferer()) ? subscribe.getReferer() : "")
                .post(body)
                .header("beanName", beanName)
                .build();
        log.info("{}-前置校验,手机号:{},请求:{}", LOG_TAG, subscribe.getMobile(), request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-前置校验,手机号:{},返回结果:{}", LOG_TAG, subscribe.getMobile(), result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-前置校验,手机号:{},异常:", LOG_TAG, subscribe.getMobile(), e);
            return Result.error("系统错误");
        }
    }


    @IpLimit
    public Result callGetSmsCode(Subscribe subscribe, String beanName) throws JsonProcessingException {
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(subscribe));
        Request request = new Request.Builder().url(commonGetSmsCodeUrl)
                .header("x-requested-with", StringUtils.isNotBlank(subscribe.getReferer()) ? subscribe.getReferer() : "")
                .post(body)
                .header("beanName", beanName)
                .build();
        log.info("{}-获取验证码,手机号:{},请求:{}", LOG_TAG, subscribe.getMobile(), request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取验证码,手机号:{},返回结果:{}", LOG_TAG, subscribe.getMobile(), result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-获取验证码,手机号:{},异常:", LOG_TAG, subscribe.getMobile(), e);
            return Result.error("系统错误");
        }
    }

    public Result callSumbitSmsCode(String mobile,ObjectNode objectNode, String beanName) throws JsonProcessingException {
        RequestBody body = RequestBody.create(mediaType, objectNode.toString());
        Request request = new Request.Builder().url(commonSubmitCodeUrl)
                .post(body)
                .header("beanName", beanName)
                .build();
        log.info("{}-提交验证码,手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码,手机号:{},返回结果:{}", LOG_TAG, mobile, result);
            return mapper.readValue(result, Result.class);
        } catch (Exception e) {
            log.info("{}-提交验证码,手机号:{},异常:", LOG_TAG, mobile, e);
            return Result.error("系统错误");
        }
    }
}
