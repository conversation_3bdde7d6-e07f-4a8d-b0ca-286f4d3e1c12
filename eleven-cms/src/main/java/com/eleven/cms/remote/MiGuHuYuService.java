package com.eleven.cms.remote;

import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.eleven.cms.config.MiGuHuYuMonthlyConfig;
import com.eleven.cms.config.MiGuHuYuMonthlyProperties;
import com.eleven.cms.dto.MiGuKuaiYouVRJingMengGetUserResult;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.Sm4Util;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MiGuHuYuMonthlyResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.api.client.util.Lists;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 咪咕互娱接口
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5 10:03
 **/
@Slf4j
@Service
public class MiGuHuYuService {
    public static final String LOG_TAG = "咪咕互娱渠道包月查询";
    private OkHttpClient client;
    private ObjectMapper mapper;
    @Autowired
    private Environment environment;
    @Autowired
    private MiGuHuYuMonthlyProperties miGuHuYuMonthlyProperties;
    private MediaType JSON;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 咪咕互娱查询包月状态
     * @param mobile
     * @return
     */
    public boolean queryMonthly(String mobile,String channel) {
        MiGuHuYuMonthlyConfig miGuHuYuMonthlyConfig=miGuHuYuMonthlyProperties.getMiGuHuYuMonthlyConfigByChannel(channel);
        if(miGuHuYuMonthlyConfig==null){
            log.warn("{}-渠道号未配置=>手机号:{},渠道号:{}",LOG_TAG+miGuHuYuMonthlyConfig.getChannelName(),mobile,channel);
            return false;
        }
        SymmetricCrypto  des=new SymmetricCrypto(Sm4Util.SM4_ECB_PADDING, miGuHuYuMonthlyConfig.getKey().getBytes());
        ObjectNode node = mapper.createObjectNode();
        List<String> appChannelList= Lists.newArrayList();
        appChannelList.add(miGuHuYuMonthlyConfig.getChannelId());
        node.putPOJO("appChannelList",appChannelList);
        node.put("mobile", des.encryptHex(mobile));
        log.info("{}-请求数据=>手机号:{},请求参数:{}",LOG_TAG+miGuHuYuMonthlyConfig.getChannelName(),mobile,node);
        RequestBody body = RequestBody.create(JSON, node.toString());
        Request request = new Request.Builder().url(miGuHuYuMonthlyConfig.getMonthlyUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-响应数据=>手机号:{},请求参数:{},响应参数:{}",LOG_TAG+miGuHuYuMonthlyConfig.getChannelName(),mobile,node,content);
            final MiGuHuYuMonthlyResult result = mapper.readValue(content, MiGuHuYuMonthlyResult.class);
            if(!result.isOK()){
                return false;
            }
            Optional<MiGuHuYuMonthlyResult.ResultData> data=result.getResultData().stream().filter(item-> miGuHuYuMonthlyConfig.getChannelId().equals(item.getAppChannel())).collect(Collectors.toList()).stream().max(Comparator.comparing(MiGuHuYuMonthlyResult.ResultData::getAppChannel));
            if(data.isPresent()){
                MiGuHuYuMonthlyResult.ResultData resultData=data.get();
                if(StringUtils.isNotBlank(resultData.getExpireTime()) && LocalDateTime.now().isBefore(DateUtil.parseString(resultData.getExpireTime(),DateUtil.FULL_TIME_SPLIT_PATTERN))){
                    return true;
                }
            }
            return false;
        } catch (IOException e) {
            log.error("{}-请求异常=>手机号:{},请求参数:{}",LOG_TAG+miGuHuYuMonthlyConfig.getChannelName(),mobile,node,e);
            return false;
        }
    }
}
