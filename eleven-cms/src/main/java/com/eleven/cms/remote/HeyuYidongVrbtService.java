package com.eleven.cms.remote;

import com.eleven.cms.config.HeyuYidongVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HeyuYidongVrbtResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 和裕视频彩铃服务类
 */
@Slf4j
@Service
public class HeyuYidongVrbtService {

    public static final String LOG_TAG = "和裕视频-api";

    @Autowired
    private HeyuYidongVrbtProperties heyuYidongVrbtProperties;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    //   @PostConstruct
    public void init() throws Exception {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().readTimeout(240L, TimeUnit.SECONDS).writeTimeout(240L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
//            this.client = this.client.newBuilder()
//                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
//                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
//                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
//                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }

        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }


    /**
     * 短信验证码下发
     *
     * @param phone
     * @return
     */
    public HeyuYidongVrbtResult getSms(String phone) {

        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("mobile", phone);
        dataNode.put("linkId", heyuYidongVrbtProperties.getLinkId());
        log.info("{}-短信验证码下发-手机号:{},请求:{}", LOG_TAG, phone, heyuYidongVrbtProperties.getGetSmsCodeUrl());
        Request request = new Request.Builder().url(heyuYidongVrbtProperties.getGetSmsCodeUrl()).post(RequestBody.create(JSON, dataNode.toString())).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-短信验证码下发-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, HeyuYidongVrbtResult.class);
        } catch (Exception e) {
            log.info("{}-短信验证码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return HeyuYidongVrbtResult.fail();
        }
    }

    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public HeyuYidongVrbtResult smsCode(String phone, String smsCode, String orderId,String source) {
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("mobile", phone);
        dataNode.put("linkId", heyuYidongVrbtProperties.getLinkId());
        if(StringUtils.isNotBlank(orderId)) {
            dataNode.put("orderId", orderId);
        }
        dataNode.put("verificationCode", smsCode);
        dataNode.put("openAddress", source);
        log.info("{}-短信验证码提交-手机号:{},验证码:{},请求:{}", LOG_TAG, phone, smsCode, dataNode.toString());
        Request request = new Request.Builder().url(heyuYidongVrbtProperties.getSmsCodeUrl()).post(RequestBody.create(JSON, dataNode.toString())).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-短信验证码提交-手机号:{},验证码:{},响应:{}", LOG_TAG, phone, smsCode, content);
            return mapper.readValue(content, HeyuYidongVrbtResult.class);
        } catch (Exception e) {
            log.info("{}-短信验证码提交-手机号:{},验证码:{},异常:", LOG_TAG, phone, smsCode, e);
            return HeyuYidongVrbtResult.fail();
        }
    }

    /**
     * 查询是否订购
     *
     * @param phone
     * @param orderId
     * @return
     */
    public boolean queryOrder(String phone, String orderId) {
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("orderId", orderId);
        log.info("{}-订购结果查询-手机号:{},订单号:{},请求:{}", LOG_TAG, phone, orderId, dataNode.toString());
        Request request = new Request.Builder().url(heyuYidongVrbtProperties.getQueryOrderUrl()).post(RequestBody.create(JSON, dataNode.toString())).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-订购结果查询-手机号:{},订单号:{},响应:{}", LOG_TAG, phone, orderId, content);
            JsonNode jsonNode = mapper.readTree(content);
            return jsonNode.at("/order/status").asText().equals(1);
        } catch (Exception e) {
            log.info("{}-订购结果查询-手机号:{},订单号:{},异常:", LOG_TAG, phone, orderId, e);
            return false;
        }
    }

}
