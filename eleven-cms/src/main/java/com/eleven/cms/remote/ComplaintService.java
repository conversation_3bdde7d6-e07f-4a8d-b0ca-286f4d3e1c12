package com.eleven.cms.remote;

import com.eleven.cms.service.IAliSignRecordService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.LiantongResp;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2022-07-22 11:36
 */
@Slf4j
@Service
public class ComplaintService {

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    LiantongVrbtService liantongVrbtService;
    @Autowired
    DianxinVrbtService dianxinVrbtService;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    IAliSignRecordService aliSignRecordService;

    private static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 业务订购状态查询  1=有订购关系
     *
     * @param mobile
     * @param channelCode
     * @return
     */
    public Integer queryOrderStatus(String mobile, String channelCode, String bizType, String serviceId) {
        //三方支付会员业务支付宝签约关系
        if(StringUtils.startsWith(channelCode,"MEMBER_")){
            return aliSignRecordService.isAliSignExist(mobile) ? 1 : 0;
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (BizConstant.isLocalBizType(bizType)) {
            if (mobileRegionResult.isIspLiantong()) {
                return queryLiantongLocalBizOrderStatus(mobile, serviceId, bizType);
            } else if (mobileRegionResult.isIspDianxin()) {
                return queryDianxinLocalBizOrderStatus(mobile, serviceId, bizType);
            } else {
                return queryYidongLocalBizOrderStatus(mobile, channelCode, bizType);
            }
        } else {
            return innerUnionMemberService.queryRemoteOrderStatus(mobile, channelCode, bizType);
        }
    }


    /**
     * 业务退订
     *
     * @param mobile
     * @param channelCode
     * @return
     */
    public String cancelOrder(String mobile, String channelCode, String bizType, String serviceId) throws JsonProcessingException {
        //三方支付会员业务支付宝签约关系
        if(StringUtils.startsWith(channelCode,"MEMBER_")){
            return aliSignRecordService.aliUnSign(mobile).getMessage();
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (BizConstant.isLocalBizType(bizType)) {
            if (mobileRegionResult.isIspLiantong()) {
                return cancelLiantongLocalBizOrder(mobile, serviceId, bizType);
            } else if (mobileRegionResult.isIspDianxin()) {
                return cancelDianxinLocalOrder(mobile, serviceId, bizType);
            } else {
                return cancelYidongLocalBizOrder(mobile, channelCode, bizType);
            }
        } else {
            return innerUnionMemberService.cancelRemoteOrder(mobile, channelCode, bizType);
        }
    }


    /**
     * 本地移动再订业务状态查询
     *
     * @param mobile
     * @param channelCode
     * @param bizType
     * @return
     */
    private Integer queryYidongLocalBizOrderStatus(String mobile, String channelCode, String bizType) {
        if (BizConstant.BIZ_TYPE_VRBT.equals(bizType)) {
            RemoteResult vrbtStatus = miguApiService.vrbtMonthStatusQuery(mobile, channelCode, true);
            if (vrbtStatus.isOK()) {
                return Integer.parseInt(vrbtStatus.getStatus()) > 0 ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
            }
            return BizConstant.SUBSCRIBE_MONTH_VERIFY_INIT;
        } else if (BizConstant.BIZ_TYPE_BJHY.equals(bizType)) {
            RemoteResult bjhyStatus = miguApiService.bjhyQuery(mobile, channelCode);
            return bjhyStatus.isBjhyMember()? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
        } else if (BizConstant.BIZ_TYPE_CPMB.equals(bizType)) {
            RemoteResult cpmbStatus = miguApiService.cpmbQuery(mobile, channelCode);
            return cpmbStatus.isCpmbMember() ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
        } else if (BizConstant.BIZ_TYPE_UNION_MEMBER.equals(bizType)) {
            RemoteResult unionMember = miguApiService.asMemberQuery(mobile, channelCode);
            return unionMember.isAsMember() ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
        }
        return BizConstant.SUBSCRIBE_MONTH_VERIFY_INIT;
    }

    /**
     * 本地联通再订业务状态查询
     *
     * @param mobile
     * @param serviceId
     * @param bizType
     * @return
     */
    private Integer queryLiantongLocalBizOrderStatus(String mobile, String serviceId, String bizType) {
        if (BizConstant.BIZ_TYPE_VRBT.equals(bizType)) {
            String companyCode = BizConstant.BIZ_LT_CHANNEL_HONGSHENG;
            if (StringUtils.equals(serviceId, BizConstant.BIZ_LT_RM_SERVICE_ID)) {
                companyCode = BizConstant.BIZ_LT_CHANNEL_RUIMEI;
            } else if (StringUtils.equals(serviceId, BizConstant.BIZ_LT_RJ_SERVICE_ID)) {
                companyCode = BizConstant.BIZ_LT_CHANNEL_RUIJIN;
            } else if (StringUtils.equals(serviceId, BizConstant.BIZ_LT_BLH_SERVICE_ID)) {
                companyCode = BizConstant.BIZ_LT_CHANNEL_LEQING;
            }
            return liantongVrbtService.isSubedMon(mobile, companyCode) ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
        }
        return BizConstant.SUBSCRIBE_MONTH_VERIFY_INIT;
    }

    /**
     * 本地电信再订业务状态查询
     *
     * @param mobile
     * @param serviceId
     * @param bizType
     * @return
     */
    private Integer queryDianxinLocalBizOrderStatus(String mobile, String serviceId, String bizType) {
        if (BizConstant.BIZ_TYPE_VRBT.equals(bizType)) {
            if (StringUtils.isEmpty(serviceId) || !BizConstant.DIANXIN_QUERY_CHANNEL_LIST.contains(serviceId)) {
                serviceId = BizConstant.BIZ_DIANXIN_CHANNEL_GUANGMINGWANG;
            }
            return dianxinVrbtService.queryPackageExist(mobile, serviceId) ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
        }
        return BizConstant.SUBSCRIBE_MONTH_VERIFY_INIT;
    }

    /**
     * 本地电信业务退订
     *
     * @param mobile
     * @param serviceId
     * @param bizType
     * @return
     */
    private String cancelDianxinLocalOrder(String mobile, String serviceId, String bizType) throws JsonProcessingException {
        if (BizConstant.BIZ_TYPE_VRBT.equals(bizType)) {
            if (StringUtils.isEmpty(serviceId) || !BizConstant.DIANXIN_QUERY_CHANNEL_LIST.contains(serviceId)) {
                serviceId = BizConstant.BIZ_DIANXIN_CHANNEL_GUANGMINGWANG;
            }
            String content = dianxinVrbtService.unSubscribeByemp(mobile, serviceId);
            JsonNode jsonNode = mapper.readTree(content);
            jsonNode = mapper.readTree(jsonNode.get("/BasicJTResponse").asText());
            return jsonNode.get("/res_message").asText();
        }
        return "无法退订该业务";
    }

    /**
     * 本地联通业务退订
     *
     * @param mobile
     * @param serviceId
     * @param bizType
     * @return
     */
    private String cancelLiantongLocalBizOrder(String mobile, String serviceId, String bizType) {
        if (BizConstant.BIZ_TYPE_VRBT.equals(bizType)) {
            String companyCode = BizConstant.BIZ_LT_CHANNEL_HONGSHENG;
            if (StringUtils.equals(serviceId, BizConstant.BIZ_LT_RM_SERVICE_ID)) {
                companyCode = BizConstant.BIZ_LT_CHANNEL_RUIMEI;
            } else if (StringUtils.equals(serviceId, BizConstant.BIZ_LT_RJ_SERVICE_ID)) {
                companyCode = BizConstant.BIZ_LT_CHANNEL_RUIJIN;
            } else if (StringUtils.equals(serviceId, BizConstant.BIZ_LT_BLH_SERVICE_ID)) {
                companyCode = BizConstant.BIZ_LT_CHANNEL_LEQING;
            }
            LiantongResp liantongResp = liantongVrbtService.unSubProductNoToken(mobile, companyCode);
            return liantongResp.getDescription();
        }
        return "无法退订该业务";
    }


    /**
     * 本地移动业务退订
     *
     * @param mobile
     * @param channelCode
     * @param bizType
     * @return
     */
    private String cancelYidongLocalBizOrder(String mobile, String channelCode, String bizType) {
        if (BizConstant.BIZ_TYPE_VRBT.equals(bizType)) {
            RemoteResult remoteResult = miguApiService.vrbtUnsubscribe(mobile, channelCode);
            return remoteResult.getResMsg();
        } else if (BizConstant.BIZ_TYPE_BJHY.equals(bizType)) {
            RemoteResult remoteResult = miguApiService.bjhyCancel(mobile, channelCode);
            return remoteResult.getResMsg();
        } else if (BizConstant.BIZ_TYPE_CPMB.equals(bizType)) {
            RemoteResult remoteResult = miguApiService.cpmbCancel(mobile, channelCode);
            return remoteResult.getResMsg();
        } else if (BizConstant.BIZ_TYPE_UNION_MEMBER.equals(bizType)) {
            RemoteResult remoteResult = miguApiService.asMemberCancel(mobile, channelCode);
            return remoteResult.getResMsg();
        }
        return "无法退订该业务";
    }

}
