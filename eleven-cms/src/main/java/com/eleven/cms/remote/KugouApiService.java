package com.eleven.cms.remote;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.config.KugouDirectRechargeProperties;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.KugouOrder;
import com.eleven.cms.service.IKugouOrderService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RSA256Utils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *  酷狗直充平台调用接口
 */
@Slf4j
@Service
public class KugouApiService {

    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;

    @Autowired
    private IKugouOrderService kugouOrderService;
    @Autowired
    private KugouDirectRechargeProperties kugouDirectRechargeProperties;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper();
        this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }
    /**
     *  酷狗直充接口
     *
     * @param outTradeNo 订单号
     * @param phoneNo 手机号
     * @return
     */
    public KugouOrderResult order(String outTradeNo, String phoneNo) {
        KugouOrderResult kugouOrderResult = new KugouOrderResult();
        Map<String, Object> map = new HashMap<>();
        KugouOrder kugouOrder = new KugouOrder();
        String url = kugouDirectRechargeProperties.getBaseUrl() + kugouDirectRechargeProperties.getOrderUrl();
        String key = kugouDirectRechargeProperties.getKey();
        String mainKey = kugouDirectRechargeProperties.getMainKey();
        long time = new Date().getTime();

        map.put("main_key",mainKey);	     //        string	直充配额主key，同一个主key下可申请多个直充配额
        map.put("out_trade_no",outTradeNo);	 //        string	你方订单号，必须唯一，且不能超过40字节长度
        map.put("out_user_id",phoneNo);	     //非必填   string	用户id。不填酷狗会把用户手机号记录为用户id
        map.put("user_type",2);	             //        number	充值的用户id类型，2表示通过手机号，4表示开放平台openid
        map.put("user",phoneNo);	         //        string	手机号或者openid
        map.put("bind_type","2");	         //非必填   string	user_type为2的时候，选择给哪个绑定账号充值。1最近绑定的账号，2最近登录的账号。默认2
        map.put("product_type","svip");	     //        string	充值的产品类型，music表示音乐包，svip表示酷狗音乐豪华vip
        map.put("ret_user",0);               //非必填   number	是否返回用户头像等数据。0否， 1是， 默认0
        map.put("days",31);                  //        number	充值的天数，与申请的直充配额要匹配，一个月的是31天
        map.put("sign_type","sha1");	     //        string	签名的方式，目前支持 sha1
        map.put("timestamp",time);	//number	请求的时间戳
        map.put("openappid","");	         //非必填   string	第三方业务appid， 在user_type=4的时候传
        String sign = RSA256Utils.generateSign(map, key);
        map.put("sign", sign);	        //        string	参数签名，签名方法见签名一节

        //持久化订单数据
        kugouOrder.setMainKey(mainKey);
        kugouOrder.setOutTradeNo(outTradeNo);
        kugouOrder.setOutUserId(phoneNo);
        kugouOrder.setUserType(2);
        kugouOrder.setUser(phoneNo);
        kugouOrder.setBindType(2);
        kugouOrder.setProductType("svip");
        kugouOrder.setRetUser(0);
        kugouOrder.setDays(31);
        kugouOrder.setSignType("sha1");
        kugouOrder.setTimestamp(time + "");
        kugouOrder.setSign(sign);
        kugouOrder.setMobile(phoneNo);
        kugouOrderService.save(kugouOrder);

        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .addHeader("Content-Type","application/json")
                .post(body)
                .build();
        log.info("酷狗直充接口-手机号:{},请求body: {}", phoneNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("酷狗直充接口-手机号:{},响应: {}", phoneNo, content);
            kugouOrderResult = mapper.readValue(content,KugouOrderResult.class);
            if(kugouOrderResult.isOK() && kugouOrderResult.getData()!=null  && StringUtils.isNotBlank(kugouOrderResult.getData().getKgOrderNumber())){
                kugouOrder.setStatus(1);
                kugouOrder.setKgOrderNumber(kugouOrderResult.getData().getKgOrderNumber());
                kugouOrderService.lambdaUpdate().eq(KugouOrder::getId,kugouOrder.getId()).update(kugouOrder);
            }else{
                kugouOrder.setStatus(-1);
                kugouOrderService.lambdaUpdate().eq(KugouOrder::getId,kugouOrder.getId()).update(kugouOrder);
            }
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("酷狗直充接口-手机号:{},错误信息: {}", phoneNo, e.getMessage());
            kugouOrder.setStatus(-1);
            kugouOrderService.lambdaUpdate().eq(KugouOrder::getId,kugouOrder.getId()).update(kugouOrder);
            return kugouOrderResult.fail();
        }
        return kugouOrderResult;
    }

//    /**
//     *  酷狗直充接口(联合会员)
//     *
//     * @param outTradeNo 订单号
//     * @param phoneNo 手机号
//     * @return
//     */
//    public FebsResponse orderUnion(String outTradeNo, String phoneNo,String partner) {
//        FebsResponse febsResponse = new FebsResponse();
//        Map<String, Object> map = new HashMap<>();
//        KugouOrder kugouOrder = new KugouOrder();
//        String url = kugouDirectRechargeProperties.getBaseUrl() + kugouDirectRechargeProperties.getOrderUnionUrl();
//        String key = kugouDirectRechargeProperties.getKey();
//        String mainKey = kugouDirectRechargeProperties.getMainKey();
//        long time = new Date().getTime();
//
//        map.put("main_key",mainKey);	     //        string	直充配额主key，同一个主key下可申请多个直充配额
//        map.put("out_trade_no",outTradeNo);	 //        string	你方订单号，必须唯一，且不能超过40字节长度
//        map.put("out_user_id",phoneNo);	     //非必填   string	用户id。不填酷狗会把用户手机号记录为用户id
//        map.put("user_type",2);	             //        number	充值的用户id类型，2表示通过手机号，4表示开放平台openid
//        map.put("user",phoneNo);	         //        string	手机号或者openid
//        map.put("bind_type","2");	         //非必填   string	user_type为2的时候，选择给哪个绑定账号充值。1最近绑定的账号，2最近登录的账号。默认2
//        map.put("product_type","svip");	     //        string	充值的产品类型，music表示音乐包，svip表示酷狗音乐豪华vip
//        map.put("ret_user",0);               //非必填   number	是否返回用户头像等数据。0否， 1是， 默认0
//        map.put("days",31);                  //        number	充值的天数，与申请的直充配额要匹配，一个月的是31天
//        map.put("sign_type","sha1");	     //        string	签名的方式，目前支持 sha1
//        map.put("timestamp",time);	//number	请求的时间戳
//        map.put("openappid","");	         //非必填   string	第三方业务appid， 在user_type=4的时候传
//        map.put("partner",partner);	         //   string	直充联合会员id，目前支持（京东：，网易严选：）
//        String sign = RSA256Utils.generateSign(map, key);
//        map.put("sign", sign);	        //        string	参数签名，签名方法见签名一节
//
//        //持久化订单数据
//        kugouOrder.setMainKey(mainKey);
//        kugouOrder.setOutTradeNo(outTradeNo);
//        kugouOrder.setOutUserId(phoneNo);
//        kugouOrder.setUserType(2);
//        kugouOrder.setUser(phoneNo);
//        kugouOrder.setBindType(2);
//        kugouOrder.setProductType("svip");
//        kugouOrder.setRetUser(0);
//        kugouOrder.setDays(31);
//        kugouOrder.setSignType("sha1");
//        kugouOrder.setTimestamp(time + "");
//        kugouOrder.setPartner(partner);
//        kugouOrder.setSign(sign);
//        kugouOrderService.save(kugouOrder);
//
//
//        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
//        HttpUrl httpUrl = HttpUrl.parse(url)
//                .newBuilder()
//                .build();
//        Request request = new Request.Builder().url(httpUrl)
//                .addHeader("Content-Type","application/json")
//                .post(body)
//                .build();
//        log.info("酷狗直充接口(联合会员)-手机号:{},请求body: {}", phoneNo, JSONObject.toJSONString(map));
//        try (Response response = client.newCall(request).execute()) {
//            if (!response.isSuccessful()) {
//                throw new IOException("Unexpected code " + response);
//            }
//            String content = response.body().string();
//            febsResponse.success().data(content);
//            log.info("酷狗直充接口(联合会员)-手机号:{},响应: {}", phoneNo, content);
//        } catch (IOException e) {
//            febsResponse.fail().message(e.getMessage());
//            //e.printStackTrace();
//            log.info("酷狗直充接口(联合会员)-手机号:{},错误信息: {}", phoneNo, e.getMessage());
//        }
//        return febsResponse;
//    }

    /**
     *  酷狗查询直充订单充值状态
     *
     * @param outTradeNo 订单号
     * @param phoneNo 手机号
     * @return
     */
    public KugouQueryResult queryOrder(String phoneNo, String outTradeNo) {
        KugouQueryResult kugouQueryResult = new KugouQueryResult();
        Map<String, Object> map = new HashMap<>();
        String url = kugouDirectRechargeProperties.getBaseUrl() + kugouDirectRechargeProperties.getQueryOrderUrl();
        String key = kugouDirectRechargeProperties.getKey();
        String mainKey = kugouDirectRechargeProperties.getMainKey();

        map.put("main_key",mainKey);	     //        string	直充配额主key，同一个主key下可申请多个直充配额
        map.put("out_trade_no",outTradeNo);	 //        string	你方订单号，必须唯一，且不能超过40字节长度
        map.put("sign_type","sha1");	     //        string	签名的方式，目前支持 sha1
        map.put("timestamp",new Date().getTime());	//number	请求的时间戳
        map.put("sign", RSA256Utils.generateSign(map,key));	        //        string	参数签名，签名方法见签名一节
        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .addHeader("Content-Type","application/json")
                .post(body)
                .build();
        log.info("酷狗查询直充订单充值状态-手机号:{},订单号:{},请求body: {}", phoneNo,outTradeNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("酷狗查询直充订单充值状态-手机号:{},订单号:{},响应: {}", phoneNo,outTradeNo, content);
            kugouQueryResult = mapper.readValue(content, KugouQueryResult.class);
            //修改订单状态
            KugouQueryDataResult data = kugouQueryResult.getData();
            if(data != null && StringUtils.isNotBlank(data.getStatus())){
                KugouOrder kugouOrder = kugouOrderService.lambdaQuery()
                        .eq(KugouOrder::getOutTradeNo, outTradeNo)
                        .one();
                if(kugouOrder != null){
                    kugouOrder.setStatus(Integer.parseInt(data.getStatus()));
                    kugouOrder.setKgOrderNumber(data.getOrderNumber());
                    kugouOrderService.updateById(kugouOrder);
                }
            }
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("酷狗查询直充订单充值状态-手机号:{},订单号:{},错误信息: {}", phoneNo,outTradeNo, e.getMessage());
            return kugouQueryResult.fail();
        }
        return kugouQueryResult;
    }

//    /**
//     *  用户自主选择账号充值-获取验证码接口
//     *
//     * @param phoneNo 手机号
//     * @return
//     */
//    public FebsResponse verifyCode(String phoneNo) {
//        FebsResponse febsResponse = new FebsResponse();
//        Map<String, Object> map = new HashMap<>();
//        String url = kugouDirectRechargeProperties.getBaseUrl() + kugouDirectRechargeProperties.getVerifyCodeUrl();
//        String key = kugouDirectRechargeProperties.getKey();
//        String mainKey = kugouDirectRechargeProperties.getMainKey();
//
//        map.put("main_key",mainKey);	     //        string	直充配额主key，同一个主key下可申请多个直充配额
//        map.put("mobilenumber",phoneNo);	 //        string	手机号
//        map.put("sign_type","sha1");	     //        string	签名的方式，目前支持 sha1
//        map.put("timestamp",new Date().getTime());	//number	请求的时间戳
//        map.put("sign", RSA256Utils.generateSign(map,key));	        //        string	参数签名，签名方法见签名一节
//        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
//        HttpUrl httpUrl = HttpUrl.parse(url)
//                .newBuilder()
//                .build();
//        Request request = new Request.Builder().url(httpUrl)
//                .addHeader("Content-Type","application/json")
//                .post(body)
//                .build();
//        log.info("用户自主选择账号充值-获取验证码接口-手机号:{},请求body: {}", phoneNo,JSONObject.toJSONString(map));
//        try (Response response = client.newCall(request).execute()) {
//            if (!response.isSuccessful()) {
//                throw new IOException("Unexpected code " + response);
//            }
//            String content = response.body().string();
//            febsResponse.success().data(content);
//            log.info("用户自主选择账号充值-获取验证码接口-手机号:{},响应: {}", phoneNo,content);
//        } catch (IOException e) {
//            febsResponse.fail().message(e.getMessage());
//            //e.printStackTrace();
//            log.info("用户自主选择账号充值-获取验证码接口-手机号:{},错误信息: {}", phoneNo,e.getMessage());
//        }
//        return febsResponse;
//    }
//
//    /**
//     *  用户自主选择账号充值-请求账号信息接口
//     *
//     * @param phoneNo 手机号
//     * @return
//     */
//    public FebsResponse openidList(String phoneNo,String verifycode,String appId) {
//        FebsResponse febsResponse = new FebsResponse();
//        Map<String, Object> map = new HashMap<>();
//        String url = kugouDirectRechargeProperties.getBaseUrl() + kugouDirectRechargeProperties.getOpenIdListUrl();
//        String key = kugouDirectRechargeProperties.getKey();
//        String mainKey = kugouDirectRechargeProperties.getMainKey();
//
//        map.put("main_key",mainKey);	     //        string	直充配额主key，同一个主key下可申请多个直充配额
//        map.put("mobilenumber",phoneNo);	 //        string	手机号
//        map.put("verifycode",verifycode);	 //        string	验证码
//        map.put("openappid",appId);	 //            string	第三方业务appid
//        map.put("sign_type","sha1");	     //        string	签名的方式，目前支持 sha1
//        map.put("timestamp",new Date().getTime());	//number	请求的时间戳
//        map.put("sign", RSA256Utils.generateSign(map,key));	        //        string	参数签名，签名方法见签名一节
//        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
//        HttpUrl httpUrl = HttpUrl.parse(url)
//                .newBuilder()
//                .build();
//        Request request = new Request.Builder().url(httpUrl)
//                .addHeader("Content-Type","application/json")
//                .post(body)
//                .build();
//        log.info("用户自主选择账号充值-请求账号信息接口-手机号:{},请求body: {}", phoneNo,JSONObject.toJSONString(map));
//        try (Response response = client.newCall(request).execute()) {
//            if (!response.isSuccessful()) {
//                throw new IOException("Unexpected code " + response);
//            }
//            String content = response.body().string();
//            febsResponse.success().data(content);
//            log.info("用户自主选择账号充值-请求账号信息接口-手机号:{},响应: {}", phoneNo,content);
//        } catch (IOException e) {
//            febsResponse.fail().message(e.getMessage());
//            //e.printStackTrace();
//            log.info("用户自主选择账号充值-请求账号信息接口-手机号:{},错误信息: {}", phoneNo,e.getMessage());
//        }
//        return febsResponse;
//    }

    /**
     *  回收酷狗音乐的豪华vip或音乐包， 只能回收24小时内开通的订单
     *
     * @param outTradeNo 订单号
     * @param phoneNo 手机号
     * @return
     */
    public KugouRecoverOrderResult recoverOrder(String phoneNo, String outTradeNo) {
        KugouRecoverOrderResult kugouRecoverOrderResult = new KugouRecoverOrderResult();
        Map<String, Object> map = new HashMap<>();
        String url = kugouDirectRechargeProperties.getBaseUrl() + kugouDirectRechargeProperties.getRecoverOrderUrl();
        String key = kugouDirectRechargeProperties.getKey();
        String mainKey = kugouDirectRechargeProperties.getMainKey();
        long time = new Date().getTime();

        map.put("main_key",mainKey);	     //        string	直充配额主key，同一个主key下可申请多个直充配额
        map.put("out_trade_no",outTradeNo);	 //        string	你方订单号，必须唯一，且不能超过40字节长度
        map.put("out_user_id",phoneNo);	     //非必填   string	用户id。不填酷狗会把用户手机号记录为用户id
        map.put("user_type",2);	             //        number	充值的用户id类型，2表示通过手机号，4表示开放平台openid
        map.put("user",phoneNo);	         //        string	手机号或者openid
        map.put("product_type","svip");	     //        string	充值的产品类型，music表示音乐包，svip表示酷狗音乐豪华vip
        map.put("days",31);                  //        number	充值的天数，与申请的直充配额要匹配，一个月的是31天
        map.put("sign_type","sha1");	     //        string	签名的方式，目前支持 sha1
        map.put("timestamp",time);	//number	请求的时间戳
        String sign = RSA256Utils.generateSign(map, key);
        map.put("sign", sign);	        //        string	参数签名，签名方法见签名一节
        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .addHeader("Content-Type","application/json")
                .post(body)
                .build();
        log.info("回收酷狗音乐的豪华vip或音乐包-手机号:{},订单号:{},请求body: {}", phoneNo,outTradeNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("回收酷狗音乐的豪华vip或音乐包-手机号:{},订单号:{},响应: {}", phoneNo,outTradeNo, content);
            kugouRecoverOrderResult = mapper.readValue(content, KugouRecoverOrderResult.class);

            //修改订单状态
            KugouOrder kugouOrder = kugouOrderService.lambdaQuery()
                    .eq(KugouOrder::getOutTradeNo, outTradeNo)
                    .one();
            if(kugouOrder != null && kugouRecoverOrderResult.isOK()){
                kugouOrder.setStatus(2);
                kugouOrderService.updateById(kugouOrder);
            }

        } catch (IOException e) {
            log.info("回收酷狗音乐的豪华vip或音乐包-手机号:{},订单号:{},错误信息: {}", phoneNo,outTradeNo, e.getMessage());
            return kugouRecoverOrderResult.fail();
            //e.printStackTrace();
        }
        return kugouRecoverOrderResult;
    }

    /**
     *  二次校验回调
     *
     * @param outTradeNo 订单号
     * @param phoneNo 手机号
     * @return
     */
    public KugouDoubleVerifyDto doubleVerifyCallback(String outTradeNo, String phoneNo) {

        log.info("酷狗二次校验回调请求--->out_trade_no:{}--out_user_id:{}",outTradeNo,phoneNo);
        KugouDoubleVerifyDto kugouDoubleVerifyDto = new KugouDoubleVerifyDto();

        if(StringUtils.isBlank(outTradeNo)){
            return kugouDoubleVerifyDto.existFalse();
        }
        if(StringUtils.isBlank(phoneNo)){
            return kugouDoubleVerifyDto.existFalse();
        }
        try{
            //查询订单是否存在
            boolean b = kugouOrderService.lambdaQuery()
                    .eq(KugouOrder::getOutTradeNo, outTradeNo)
                    .eq(KugouOrder::getOutUserId, phoneNo)
                    .one() != null;
            return b ? kugouDoubleVerifyDto.existTrue() : kugouDoubleVerifyDto.existFalse();
        }catch (Exception e){
            log.error("酷狗二次校验回调出错：",e);
            return kugouDoubleVerifyDto.existFalse();
        }
    }


    /**
     *  酷狗直充接口
     * @param outTradeNo 订单号
     * @param phoneNo 手机号
     * @return
     */
    public KugouOrderResult order(String outTradeNo, String phoneNo,String serviceId,String packName,String channel) {
        KugouOrderResult kugouOrderResult = new KugouOrderResult();
        Map<String, Object> map = new HashMap<>();
        KugouOrder kugouOrder = new KugouOrder();
        String url = kugouDirectRechargeProperties.getBaseUrl() + kugouDirectRechargeProperties.getOrderUrl();
        String key = kugouDirectRechargeProperties.getKey();
        String mainKey = kugouDirectRechargeProperties.getMainKey();
        long time = new Date().getTime();

        map.put("main_key",mainKey);	     //        string	直充配额主key，同一个主key下可申请多个直充配额
        map.put("out_trade_no",outTradeNo);	 //        string	你方订单号，必须唯一，且不能超过40字节长度
        map.put("out_user_id",phoneNo);	     //非必填   string	用户id。不填酷狗会把用户手机号记录为用户id
        map.put("user_type",2);	             //        number	充值的用户id类型，2表示通过手机号，4表示开放平台openid
        map.put("user",phoneNo);	         //        string	手机号或者openid
        map.put("bind_type","2");	         //非必填   string	user_type为2的时候，选择给哪个绑定账号充值。1最近绑定的账号，2最近登录的账号。默认2
        map.put("product_type","svip");	     //        string	充值的产品类型，music表示音乐包，svip表示酷狗音乐豪华vip
        map.put("ret_user",0);               //非必填   number	是否返回用户头像等数据。0否， 1是， 默认0
        map.put("days",31);                  //        number	充值的天数，与申请的直充配额要匹配，一个月的是31天
        map.put("sign_type","sha1");	     //        string	签名的方式，目前支持 sha1
        map.put("timestamp",time);	//number	请求的时间戳
        map.put("openappid","");	         //非必填   string	第三方业务appid， 在user_type=4的时候传
        String sign = RSA256Utils.generateSign(map, key);
        map.put("sign", sign);	        //        string	参数签名，签名方法见签名一节

        //持久化订单数据
        kugouOrder.setMainKey(mainKey);
        kugouOrder.setOutTradeNo(outTradeNo);
        kugouOrder.setOutUserId(phoneNo);
        kugouOrder.setUserType(2);
        kugouOrder.setUser(phoneNo);
        kugouOrder.setBindType(2);
        kugouOrder.setProductType("svip");
        kugouOrder.setRetUser(0);
        kugouOrder.setDays(31);
        kugouOrder.setSignType("sha1");
        kugouOrder.setTimestamp(time + "");
        kugouOrder.setSign(sign);
        kugouOrder.setMobile(phoneNo);
        kugouOrder.setServiceId(serviceId);
        kugouOrder.setPackName(packName);
        kugouOrder.setChannel(channel);
        kugouOrderService.save(kugouOrder);

        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .addHeader("Content-Type","application/json")
                .post(body)
                .build();
        log.info("酷狗直充接口-手机号:{},请求body: {}", phoneNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("酷狗直充接口-手机号:{},响应: {}", phoneNo, content);
            kugouOrderResult = mapper.readValue(content,KugouOrderResult.class);
            if(kugouOrderResult.isOK() && kugouOrderResult.getData()!=null  && StringUtils.isNotBlank(kugouOrderResult.getData().getKgOrderNumber())){
                kugouOrder.setStatus(1);
                kugouOrder.setKgOrderNumber(kugouOrderResult.getData().getKgOrderNumber());
                kugouOrderService.lambdaUpdate().eq(KugouOrder::getId,kugouOrder.getId()).update(kugouOrder);
            }else{
                kugouOrder.setStatus(-1);
                kugouOrderService.lambdaUpdate().eq(KugouOrder::getId,kugouOrder.getId()).update(kugouOrder);
            }
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("酷狗直充接口-手机号:{},错误信息: {}", phoneNo, e.getMessage());
            kugouOrder.setStatus(-1);
            kugouOrderService.lambdaUpdate().eq(KugouOrder::getId,kugouOrder.getId()).update(kugouOrder);
            return kugouOrderResult.fail();
        }
        return kugouOrderResult;
    }
}
