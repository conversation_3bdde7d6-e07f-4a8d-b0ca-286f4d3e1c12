package com.eleven.cms.remote;

import cn.hutool.crypto.SecureUtil;
import com.eleven.cms.config.GuangdongjujieProperties;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.GuangdongJujieResult;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 河北移动业务
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class GuangdongJujieService {

    @Autowired
    private Environment environment;
    @Autowired
    private GuangdongjujieProperties guangdongJujieProperties;
    @Autowired
    private RedisUtil redisUtil;


    public static final String LOG_TAG = "广东聚杰api";

    public static final MediaType JSON
        = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);


    private OkHttpClient client;
    private ObjectMapper mapper;


    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                .compressed()
                .insecure()  //不检查自签名证书
                //.connectTimeout(120)
                //.retry(5)
                .build();
            this.client = this.client.newBuilder()
                .addNetworkInterceptor(new CurlInterceptor(System.out::println, options))
                .build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);
    }


    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败


    /**
     * 查询可办理商品
     *
     * @return
     */
    public @Nonnull
    String queryGoods(String mobile, String isp) {
        ObjectNode bizNode = mapper.createObjectNode();
        bizNode.put("method", "jujie.common.auth");
        bizNode.put("userAccount", mobile);
        bizNode.put("operator", isp);
        bizNode.put("bizType", "mobile");
        bizNode.put("goodsTypes", "3");
        ObjectNode headNode = getHeader("jujie.common.auth");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("biz", bizNode);
        dataNode.put("head", headNode);
        String sign = generateSign(bizNode, headNode);
        headNode.put("sign", sign);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder()
            .url(guangdongJujieProperties.getRequestUrl())
            .post(body)
            .build();
        log.info("{}-查询可办理商品,手机号:{},请求:{}", LOG_TAG, mobile, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询可办理商品,手机号:{},响应:{}", LOG_TAG, mobile, content);
            return content;
        } catch (Exception e) {
            log.info("{}-查询可办理商品,手机号:{},异常:", LOG_TAG, mobile, e);
            return null;
        }
    }

    /**
     * 获取验证码
     * 1002015_101898_7888 (联通) 1002018_101902_7892（移动）1002016_101899_7889（电信）
     *
     * @return
     */
    public @Nonnull
    GuangdongJujieResult getSms(String mobile, String goodsId, String sourceAdvertisingPlatform, String app, String ip, String ua) {
        ObjectNode bizNode = mapper.createObjectNode();
        bizNode.put("method", "jujie.common.sendSmsCode");
        bizNode.put("phoneNo", mobile);
        bizNode.put("goodsId", goodsId);
        bizNode.put("userAccount", mobile);
        bizNode.put("sourceAdvertisingPlatform", sourceAdvertisingPlatform);
        bizNode.put("sourceApp", app);
        bizNode.put("sourceAppPackage", app);
        bizNode.put("clientIp", ip);
        bizNode.put("userAgent", ua);
        ObjectNode headNode = getHeader("jujie.common.sendSmsCode");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("biz", bizNode);
        dataNode.put("head", headNode);
        String sign = generateSign(bizNode, headNode);
        headNode.put("sign", sign);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder()
            .url(guangdongJujieProperties.getRequestUrl())
            .post(body)
            .build();
        log.info("{}-获取短信,手机号:{},请求:{}", LOG_TAG, mobile, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信,手机号:{},响应:{}", LOG_TAG, mobile, content);
            return mapper.readValue(content, GuangdongJujieResult.class);
        } catch (Exception e) {
            log.info("{}-获取短信,手机号:{},异常:", LOG_TAG, mobile, e);
            return GuangdongJujieResult.FAIL_RESULT;
        }
    }

    /**
     * 获取验证码
     * 1002015_101898_7888 (联通) 1002018_101902_7892（移动）1002016_101899_7889（电信）
     *
     * @return
     */
    public @Nonnull
    GuangdongJujieResult order(String mobile, String goodsId, String ispOrderNo, String code, String sourceAdvertisingPlatform, String app, String ip, String ua) {
        ObjectNode bizNode = mapper.createObjectNode();
        bizNode.put("method", "jujie.common.orderSubmit");
        bizNode.put("bizType", "mobile");
        bizNode.put("goodsId", goodsId);
        bizNode.put("goodsType", "3");
        bizNode.put("outOrderId", ispOrderNo);
        bizNode.put("phoneNo", mobile);
        bizNode.put("userAccount", mobile);
        String smsSerialNumKey = CacheConstant.CMS_CACHE_GDJJ_SMSSERIALNUM_INFO + ":" + mobile;
        if (redisUtil.hasKey(smsSerialNumKey)) {
            bizNode.put("smsSerialNum", (String) redisUtil.get(smsSerialNumKey));
        }
        bizNode.put("smsValidCode", code);
        bizNode.put("sourceAdvertisingPlatform", sourceAdvertisingPlatform);
        bizNode.put("sourceApp", app);
        bizNode.put("sourceAppPackage", app);
        bizNode.put("clientIp", ip);
        bizNode.put("userAgent", ua);
        ObjectNode headNode = getHeader("jujie.common.orderSubmit");
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("biz", bizNode);
        dataNode.put("head", headNode);
        String sign = generateSign(bizNode, headNode);
        headNode.put("sign", sign);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder()
            .url(guangdongJujieProperties.getRequestUrl())
            .post(body)
            .build();
        log.info("{}-提交短信,手机号:{},验证码:{},请求:{}", LOG_TAG, mobile, code, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信,手机号:{},验证码:{},响应:{}", LOG_TAG, mobile, code, content);
            return mapper.readValue(content, GuangdongJujieResult.class);
        } catch (Exception e) {
            log.info("{}-提交短信,手机号:{},验证码:{},异常:", LOG_TAG, mobile, code, e);
            return GuangdongJujieResult.FAIL_RESULT;
        }
    }



    private ObjectNode getHeader(String method) {
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("method", method);
        objectNode.put("version", "1.0");
        objectNode.put("appId", guangdongJujieProperties.getAppId());
        objectNode.put("requestTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        objectNode.put("transactionId", String.valueOf(System.currentTimeMillis()));
        return objectNode;
    }

    @SneakyThrows
    private String generateSign(JsonNode bizNode, JsonNode headerNode) {
        TreeMap<String, String> headTreeMap = mapper.treeToValue(headerNode, TreeMap.class);
        TreeMap<String, String> bizTreeMap = mapper.treeToValue(bizNode, TreeMap.class);
        String biz = bizTreeMap.entrySet().stream().filter(entry -> entry.getValue() != null).map(entry -> entry.toString()).collect(Collectors.joining(","));
        String head = headTreeMap.entrySet().stream().filter(entry -> entry.getValue() != null).map(entry -> entry.toString()).collect(Collectors.joining(","));
        String sortStr = String.format("biz={%s},head={%s}", biz, head);
        return SecureUtil.hmacSha256(guangdongJujieProperties.getAppSecret()).digestHex(sortStr);
    }

    @SneakyThrows
    public static void main(String[] args) {

        ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ObjectNode bizNode = mapper.createObjectNode();
        bizNode.put("method", "jujie.common.auth");
        bizNode.put("userAccount", "***********");
        bizNode.put("operator", "4");
        bizNode.put("bizType", "mobile");
        bizNode.put("goodsTypes", "3");

        ObjectNode headerNode = mapper.createObjectNode();
        headerNode.put("method", "jujie.common.auth");
        headerNode.put("version", "1.0");
        headerNode.put("appId", "2000090");
        headerNode.put("requestTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        headerNode.put("transactionId", String.valueOf(System.currentTimeMillis()));

        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("biz", bizNode);
        dataNode.put("head", headerNode);
        System.out.println(dataNode);
        TreeMap<String, String> headTreeMap = mapper.treeToValue(headerNode, TreeMap.class);
        TreeMap<String, String> bizTreeMap = mapper.treeToValue(bizNode, TreeMap.class);

        String biz = bizTreeMap.entrySet().stream().filter(entry -> entry.getValue() != null).map(entry -> entry.toString()).collect(Collectors.joining(","));
        String head = headTreeMap.entrySet().stream().filter(entry -> entry.getValue() != null).map(entry -> entry.toString()).collect(Collectors.joining(","));

        String sortStr = String.format("biz={%s},head={%s}", biz, head);
        System.out.println(SecureUtil.hmacSha256("A9oLU6fvqm8dA9eC").digestHex(sortStr));
        // biz={bizType=mobile,operator=1,userAccount=***********},head={appId=2000032,method=jujie.common.auth,requestTime=2023-06-19 17:04:52,transactionId=2K4IYAcoQUc8CO2guomwIs,version=1.0}
    }
}
