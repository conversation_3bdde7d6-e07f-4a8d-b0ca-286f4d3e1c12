package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.JiangxiCompanyConfig;
import com.eleven.cms.config.JiangxiProductConfig;
import com.eleven.cms.config.JiangxiYidongProperties;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.JiangxiYidongNoPageWriteResult;
import com.eleven.cms.vo.JiangxiYidongOrderResult;
import com.eleven.cms.vo.JiangxiYidongPdfResult;
import com.eleven.cms.vo.JiangxiYidongResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * @author: cai lei
 * @create: 2022-08-12 10:58
 */
@Slf4j
@Service
public class JiangxiYidongService {

    public static final String LOG_TAG = "江西移动api";
    @Autowired
    private Environment environment;
    @Autowired
    private JiangxiYidongProperties jiangxiYidongProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().readTimeout(30L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
//            this.client = this.client.newBuilder()
//                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
//                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * {"ret":0,"msg":"操作成功","data":{"callTime":"340ms","responseCode":"","resultMsg":{"result":"","respDesc":"短信验证码发送成功！","respCode":"0"}}}
     * 获取验证码
     *
     * @param phone
     * @param channel
     * @param company
     * @return
     */
    public @Nonnull
    JiangxiYidongResult getSms(String phone, String channel, String company, String busiSerialNumber, String orderId) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        JiangxiProductConfig jiangxiProductConfig = jiangxiYidongProperties.getJiangxiProductConfig(channel);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("serialNumber", phone);
        dataMap.put("orderId", orderId);
        dataMap.put("busiSerialNumber", busiSerialNumber);
        dataMap.put("busiName", jiangxiProductConfig.getBusiName());
        dataMap.put("goodsId", jiangxiProductConfig.getGoodsId());
        dataMap.put("planId", jiangxiProductConfig.getPlanId());
        //dataMap.put("offerId", jiangxiProductConfig.getOfferId());  //江西移动技术要求发短信请求体移除该参数
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getGetSmsCodeUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-获取验证码-渠道号:{},手机号:{}", jiangxiCompanyConfig.getLogTag(), channel, phone);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码-渠道号:{},手机号:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, content);
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String respCode = jsonNode.at("/data/resultMsg/respCode").asText();
//            return "0".equals(ret) && "0".equals(respCode);
            return mapper.readValue(content, JiangxiYidongResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取验证码-渠道号:{},手机号:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, e);
            return JiangxiYidongResult.FAIL_RESULT;
        }
    }

    /**
     * "ret":0,"msg":"操作成功","data":{"callTime":"26ms","responseCode":"","resultMsg":{"result":"","respDesc":"验证码正确","respCode":"0"}}}
     * 提交验证码
     *
     * @param phone
     * @param channel
     * @param code
     * @param orderId
     * @param busiSerialNumber
     * @param company
     * @return
     */
    public @Nonnull
    JiangxiYidongResult smsCode(String phone, String channel, String code, String orderId, String busiSerialNumber, String company) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("serialNumber", phone);
        dataMap.put("orderId", orderId);
        dataMap.put("busiSerialNumber", busiSerialNumber);
        dataMap.put("randomCode", code);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getSmsCodeUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, code);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, code, content);
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String respCode = jsonNode.at("/data/resultMsg/respCode").asText();
//            return "0".equals(ret) && "0".equals(respCode);
            return mapper.readValue(content, JiangxiYidongResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, code, e);
            return JiangxiYidongResult.FAIL_RESULT;
        }
    }

    /**
     * {"ret":0,"msg":"操作成功","data":{"callTime":"5214ms","responseCode":"1","resultMsg":{"code":"1","orderId":"22023052504290162629082570531306","resultCode":"1","message":"成功"}}}
     *
     * @param phone
     * @param channel
     * @param busiSerialNumber
     * @param company
     * @return
     */
    public @Nonnull
    JiangxiYidongOrderResult order(String phone, String channel, String busiSerialNumber, String company) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        JiangxiProductConfig jiangxiProductConfig = jiangxiYidongProperties.getJiangxiProductConfig(channel);
        String serialNumber = RandomStringUtils.randomAlphanumeric(16);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("phone", phone);
        dataMap.put("planId", jiangxiProductConfig.getPlanId());
        dataMap.put("goodsId", jiangxiProductConfig.getGoodsId());
//        dataMap.put("channelType", "126");
        dataMap.put("isSync", "1");
        dataMap.put("serialNumber", serialNumber);
        dataMap.put("busiSerialNumber", busiSerialNumber);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getOrderUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-业务订购-渠道号:{},手机号:{}", jiangxiCompanyConfig.getLogTag(), channel, phone);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-业务订购-渠道号:{},手机号:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, content);
            return mapper.readValue(content, JiangxiYidongOrderResult.class);
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String resultCode = jsonNode.at("/data/resultMsg/resultCode").asText();
//            String code = jsonNode.at("/data/resultMsg/code").asText();
//            return "0".equals(ret) && "1".equals(resultCode) && "1".equals(code);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-业务订购-渠道号:{},手机号:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, e);
            return JiangxiYidongOrderResult.FAIL_RESULT;
        }
    }

    /**
     * @param phone
     * @param channel
     * @param busiSerialNumber
     * @param company
     * @param needSign         Y-无纸化预览 N-无纸化保存
     * @param orderId
     * @return
     */
    public @Nonnull
    JiangxiYidongNoPageWriteResult noPageWrite(String phone, String channel, String busiSerialNumber, String company, String needSign, String orderId) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        JiangxiProductConfig jiangxiProductConfig = jiangxiYidongProperties.getJiangxiProductConfig(channel);
        String busiInfo = jiangxiProductConfig.getOfferId() + "@@" + ("Y".equals(needSign) ? IdWorker.getIdStr() : orderId);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("NeedSign", needSign);
        dataMap.put("PhoneNo", phone);
        dataMap.put("OpTime", DateUtil.formatFullTime(LocalDateTime.now()));
        dataMap.put("CertType", "1");
        dataMap.put("channel", "1");
        dataMap.put("channelName", jiangxiCompanyConfig.getChannelName());
        dataMap.put("busiInfo", busiInfo);
        dataMap.put("busiSerialNumber", busiSerialNumber);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getNoPageWriteUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-无纸化-渠道号:{},手机号:{},无纸化类型:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, needSign);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            JiangxiYidongNoPageWriteResult jiangxiYidongNoPageWriteResult = mapper.readValue(content, JiangxiYidongNoPageWriteResult.class);
            log.info("{}-无纸化-渠道号:{},手机号:{},无纸化类型:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, needSign, jiangxiYidongNoPageWriteResult);
            return jiangxiYidongNoPageWriteResult;
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String resultCode = jsonNode.at("/data/resultMsg/resultCode").asText();
//            String code = jsonNode.at("/data/resultMsg/code").asText();
//            return "0".equals(ret) && "1".equals(resultCode) && "1".equals(code);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-无纸化-渠道号:{},手机号:{},无纸化类型:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, e);
            return JiangxiYidongNoPageWriteResult.FAIL_RESULT;
        }
    }

    /**
     * @param company
     * @param sysAccept
     * @return
     */
    public @Nonnull
    JiangxiYidongPdfResult getPdf(String company, String sysAccept) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("sysAccept", sysAccept);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiYidongProperties.getQueryPdfUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-无纸化查询-业务流水号:{}", jiangxiCompanyConfig.getLogTag(), sysAccept);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            JiangxiYidongPdfResult jiangxiYidongNoPageWriteResult = mapper.readValue(content, JiangxiYidongPdfResult.class);
            log.info("{}-无纸化查询-业务流水号:{},响应:{}", jiangxiCompanyConfig.getLogTag(), sysAccept, jiangxiYidongNoPageWriteResult);
            return jiangxiYidongNoPageWriteResult;
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String resultCode = jsonNode.at("/data/resultMsg/resultCode").asText();
//            String code = jsonNode.at("/data/resultMsg/code").asText();
//            return "0".equals(ret) && "1".equals(resultCode) && "1".equals(code);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-无纸化查询-业务流水号:{},异常:", jiangxiCompanyConfig.getLogTag(), sysAccept, e);
            return JiangxiYidongPdfResult.FAIL_RESULT;
        }
    }

    private void getCommonParam(Request.Builder builder, String requestId, String sign, JiangxiCompanyConfig jiangxiCompanyConfig) {
        builder.addHeader("apiKey", jiangxiCompanyConfig.getApiKey());
        builder.addHeader("flag", "0");
        builder.addHeader("userId", jiangxiCompanyConfig.getUserId());
        builder.addHeader("requestId", requestId);
        builder.addHeader("Authorization", "basic YWFhOmJiYg==");
        builder.addHeader("sign", sign);
    }

    private String generateSign(String apiKey, String flag, String requestId, TreeMap<String, String> data, JiangxiCompanyConfig jiangxiCompanyConfig) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("apiKey" + apiKey);
        stringBuilder.append("flag" + flag);
        stringBuilder.append("userId" + jiangxiCompanyConfig.getUserId());
        stringBuilder.append("requestId" + requestId);
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String mapKey = entry.getKey();
            String mapValue = entry.getValue();
            stringBuilder.append(mapKey + mapValue);
        }
        stringBuilder.append(jiangxiCompanyConfig.getSecretKey());
        return DigestUtils.md5DigestAsHex(stringBuilder.toString().getBytes(StandardCharsets.UTF_8));
    }
}
