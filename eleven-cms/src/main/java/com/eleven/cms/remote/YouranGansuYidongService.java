package com.eleven.cms.remote;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import com.eleven.cms.config.YouranGansuYidongProperties;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.YouranGansuMobileResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.TreeMap;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class YouranGansuYidongService {

    public static final String LOG_TAG = "悠然甘肃移动业务api";
    @Autowired
    YouranGansuYidongProperties youranGansuYidongProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;
    public static final String DELIMITER_AMP = "&";
    private static final MediaType mediaType = MediaType.parse("application/json");


    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public YouranGansuMobileResult getSms(String channelCode, String phone, String ip) throws Exception {
        String url = youranGansuYidongProperties.getRequestUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(youranGansuYidongProperties.getChannelOfferMap().get(channelCode).getGetSmsCodeCmd(), ip));
        dataMap.put("body", getSmsBody(channelCode, phone));
        generateSign(dataMap);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-随机码下发-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone, channelCode, result);
            return mapper.readValue(result, YouranGansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},渠道号:{},异常:", LOG_TAG, phone, channelCode, e);
            return YouranGansuMobileResult.fail();

        }
    }

    public YouranGansuMobileResult smsCode(String channelCode, String phone, String smsCode, String identifyingKey, String ip) throws Exception {
        String url = youranGansuYidongProperties.getRequestUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(youranGansuYidongProperties.getChannelOfferMap().get(channelCode).getSmsCodeCmd(), ip));
        dataMap.put("body", smsCodeBody(channelCode, phone, smsCode, identifyingKey));
        generateSign(dataMap);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},请求:{}", LOG_TAG, phone, channelCode, smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", LOG_TAG, phone, channelCode, smsCode, result);
            return mapper.readValue(result, YouranGansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},异常:", LOG_TAG, phone, channelCode, smsCode, e);
            return YouranGansuMobileResult.fail();

        }
    }

    public YouranGansuMobileResult queryOrder(String channelCode, String phone, String ip) throws Exception {
        String url = youranGansuYidongProperties.getRequestUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(youranGansuYidongProperties.getChannelOfferMap().get(channelCode).getQueryOrderCmd(), ip));
        dataMap.put("body", orderBody(channelCode, phone));
        generateSign(dataMap);
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-查询订购状态-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询订购状态-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone, channelCode, result);
            return mapper.readValue(result, YouranGansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-查询订购状态-手机号:{},渠道号:{},异常:", LOG_TAG, phone, channelCode, e);
            return YouranGansuMobileResult.fail();

        }
    }

    private Map<String, Object> getHeader(String cmd, String userIp) {
        Map<String, Object> headerMap = new TreeMap<>();
        headerMap.put("appId", youranGansuYidongProperties.getAppId());
        headerMap.put("cmd", cmd);
        headerMap.put("reqTime", DateUtil.formatFullTime(LocalDateTime.now()));
        headerMap.put("userIp", userIp);
        return headerMap;
    }

    private Map<String, Object> getSmsBody(String channelCode, String mobile) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("busiType", youranGansuYidongProperties.getBusiType());
        bodyMap.put("offerCode", youranGansuYidongProperties.getChannelOfferMap().get(channelCode).getPid());
        return bodyMap;
    }

    private Map<String, Object> smsCodeBody(String channelCode, String mobile, String smsCode, String identifyingKey) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("offerCode", youranGansuYidongProperties.getChannelOfferMap().get(channelCode).getPid());
        bodyMap.put("operCode", "1");
        bodyMap.put("identifyingKey", identifyingKey);
        bodyMap.put("identifyingCode", smsCode);
        return bodyMap;
    }

    private Map<String, Object> orderBody(String channelCode, String mobile) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("offerCode", youranGansuYidongProperties.getChannelOfferMap().get(channelCode).getPid());
        return bodyMap;
    }


    private void generateSign(Map<String, Object> dataMap) throws Exception {
        Map<String, Object> headerMap = (TreeMap<String, Object>) dataMap.get("header");
        String sign = SecureUtil.hmacSha256(DigestUtils.md5(youranGansuYidongProperties.getSecret())).digestHex(MD5.create().digest(mapper.writeValueAsString(dataMap), StandardCharsets.UTF_8));
        headerMap.put("sign", sign);
    }
}
