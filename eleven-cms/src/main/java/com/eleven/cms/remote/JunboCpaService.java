package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.util.hunan.AESUtil;
import com.eleven.cms.vo.JunboLlbResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 骏伯Cpa业务
 *
 * @author: yang tao
 * @create: 2022-07-07 10:29
 */

@Slf4j
@Service
public class JunboCpaService {

    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils
            .getNewInstance()
            .newBuilder()
            .connectTimeout(20L, TimeUnit.SECONDS)
            .readTimeout(20L, TimeUnit.SECONDS)
            .writeTimeout(20L, TimeUnit.SECONDS)
            .build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 获取验证码
     *
     * @param phone
     * @param sysOrderId
     * @param channel
     * @return
     */
    //{"success":true,"code":"0000","message":"执行成功","data":{"msg":"","responseCode":"0","orderCode":"","subMediaCode":"0","url":"","routeCode":"","sellerId":""}}
    public JunboLlbResult getSms(String phone, String sysOrderId, String channel, String ua, String sourceAppName, String source) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-获取验证码-手机号:{},渠道号:{}", "骏伯业务-PortCrackConfig-配置错误", phone, channel);
            return JunboLlbResult.FAIL_RESULT;
        }
        String url = portCrackConfig.getGetSmsUrl();
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("pid", portCrackConfig.getAppId());
        objectNode.put("sysOrderId", sysOrderId);
        objectNode.put("productCode", portCrackConfig.getOfferCode());
        objectNode.put("url", StringUtils.isNotEmpty(portCrackConfig.getSourceUrl())? portCrackConfig.getSourceUrl() : source);
        objectNode.put("userAgent", StringUtils.isNotEmpty(ua)?ua:"");
        String mobile= null;
        try {
            mobile = AESUtil.encrypt(portCrackConfig.getSecretKey(), phone);
        } catch (Exception e) {
            log.info("{}-获取验证码-手机号:{},渠道号:{}", "骏伯业务-手机号加密异常", phone, channel,e);
            return JunboLlbResult.FAIL_RESULT;
        }
        objectNode.put("contactNumber", mobile);
        objectNode.put("sourceAppName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName:BizConstant.APP_NAME);
        objectNode.put("appChineseName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName:BizConstant.APP_NAME);
        objectNode.put("mediaName", StringUtils.isNotEmpty(portCrackConfig.getRemark())? portCrackConfig.getRemark() : "今日头条");
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        Request request = new Request.Builder().url(url)
            .post(body)
            .build();
        log.info("{}-获取验证码-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channel, objectNode.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取验证码-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channel, result);
            return mapper.readValue(result, JunboLlbResult.class);
        } catch (Exception e) {
            log.info("{}-获取验证码-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channel, e);
            return JunboLlbResult.FAIL_RESULT;
        }
    }

    /**
     * 校验验证码
     *
     * @param phone
     * @param sysOrderId
     * @param code
     * @param channel
     * @return
     */
    //{"success":true,"code":"0000","message":"执行成功","data":{"msg":"下单成功","responseCode":"0","orderCode":"","subMediaCode":"0","url":"","routeCode":"flow_yd_hunan","sellerId":""}}
    public JunboLlbResult smsCode(String phone, String sysOrderId, String code, String channel, String ua, String sourceAppName, String source) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-校验验证码-手机号:{},渠道号:{}", "骏伯业务-PortCrackConfig-配置错误", phone, channel);
            return JunboLlbResult.FAIL_RESULT;
        }
        String url =portCrackConfig.getCheckProductSubUrl();
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("pid",portCrackConfig.getAppId());
        objectNode.put("sysOrderId", sysOrderId);
        objectNode.put("productCode",  portCrackConfig.getOfferCode());
        objectNode.put("url", StringUtils.isNotEmpty(portCrackConfig.getSourceUrl())? portCrackConfig.getSourceUrl() : source);
        objectNode.put("userAgent", StringUtils.isNotEmpty(ua)?ua:"");
        String mobile= null;
        try {
            mobile = AESUtil.encrypt(portCrackConfig.getSecretKey(),phone);
        } catch (Exception e) {
            log.info("{}-校验验证码-手机号:{},渠道号:{}", "骏伯业务-手机号加密异常", phone, channel,e);
            return JunboLlbResult.FAIL_RESULT;
        }
        objectNode.put("contactNumber", mobile);
        objectNode.put("authCode", code);
        objectNode.put("sourceAppName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName:BizConstant.APP_NAME);
        objectNode.put("appChineseName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName:BizConstant.APP_NAME);
        objectNode.put("mediaName", StringUtils.isNotEmpty(portCrackConfig.getRemark())? portCrackConfig.getRemark() : "今日头条");
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        Request request = new Request.Builder().url(url)
            .post(body)
            .build();
        log.info("{}-验证码校验-手机号:{},渠道号:{},短信验证码:{},请求:{}", portCrackConfig.getLogTag(), phone, channel, code, objectNode.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-验证码校验-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channel, code, result);
            return mapper.readValue(result, JunboLlbResult.class);
        } catch (Exception e) {
            log.info("{}-验证码校验-手机号:{},渠道号:{},短信验证码:{},异常:", portCrackConfig.getLogTag(), phone, channel, code, e);
            return JunboLlbResult.FAIL_RESULT;
        }
    }

    /**
     * 下单接口
     *
     * @param phone
     * @param sysOrderId
     * @param code
     * @param channel
     * @return
     */
    public JunboLlbResult handleOrder(String phone, String sysOrderId, String code, String channel, String ua, String sourceAppName, String source) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-下单接口-手机号:{},渠道号:{}", "骏伯业务-PortCrackConfig-配置错误", phone, channel);
            return JunboLlbResult.FAIL_RESULT;
        }
        if(StringUtils.isNotEmpty(portCrackConfig.getCheckProductSubUrl())){
            JunboLlbResult result=smsCode(phone, sysOrderId, code, channel, ua, sourceAppName, source);
            if(!result.isOperationOk()){
                return result;
            }
        }
        String url = portCrackConfig.getSmsCodeUrl();
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("pid", portCrackConfig.getAppId());
        objectNode.put("sysOrderId", sysOrderId);
        objectNode.put("productCode", portCrackConfig.getOfferCode());
        objectNode.put("url",StringUtils.isNotEmpty(portCrackConfig.getSourceUrl())? portCrackConfig.getSourceUrl() : source);
        objectNode.put("userAgent", StringUtils.isNotEmpty(ua)?ua:"");
        String mobile= null;
        try {
            mobile = AESUtil.encrypt(portCrackConfig.getSecretKey(), phone);
        } catch (Exception e) {
            log.info("{}-下单接口-手机号:{},渠道号:{}", "骏伯业务-手机号加密异常", phone, channel,e);
            return JunboLlbResult.FAIL_RESULT;
        }
        objectNode.put("contactNumber", mobile);
        objectNode.put("authCode", code);
        objectNode.put("sourceAppName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName: BizConstant.APP_NAME);
        objectNode.put("appChineseName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName:BizConstant.APP_NAME);
        objectNode.put("mediaName", StringUtils.isNotEmpty(portCrackConfig.getRemark())? portCrackConfig.getRemark() : "今日头条");
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        Request request = new Request.Builder().url(url)
            .post(body)
            .build();
        log.info("{}-下单接口-手机号:{},渠道号:{},短信验证码:{},请求:{}", portCrackConfig.getLogTag(), phone, channel, code, objectNode.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-下单接口-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channel, code, result);
            return mapper.readValue(result, JunboLlbResult.class);
        } catch (Exception e) {
            log.info("{}-下单接口-手机号:{},渠道号:{},短信验证码:{},异常:", portCrackConfig.getLogTag(), phone, channel, code, e);
            return JunboLlbResult.FAIL_RESULT;
        }
    }


    /**
     * 贵州移动特殊下单接口（不校验验证码）
     *
     * @param phone
     * @param sysOrderId
     * @param code
     * @param channel
     * @return
     */
    public JunboLlbResult handleOrderUrl(String phone, String sysOrderId, String code, String channel, String ua, String sourceAppName, String source) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-下单接口-手机号:{},渠道号:{}", "骏伯业务-PortCrackConfig-配置错误", phone, channel);
            return JunboLlbResult.FAIL_RESULT;
        }
        String url = portCrackConfig.getSmsCodeUrl();
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("pid", portCrackConfig.getAppId());
        objectNode.put("sysOrderId", sysOrderId);
        objectNode.put("productCode", portCrackConfig.getOfferCode());
        objectNode.put("url",StringUtils.isNotEmpty(portCrackConfig.getSourceUrl())? portCrackConfig.getSourceUrl() : source);
        objectNode.put("userAgent", StringUtils.isNotEmpty(ua)?ua:"");
        String mobile= null;
        try {
            mobile = AESUtil.encrypt(portCrackConfig.getSecretKey(), phone);
        } catch (Exception e) {
            log.info("{}-下单接口-手机号:{},渠道号:{}", "骏伯业务-手机号加密异常", phone, channel,e);
            return JunboLlbResult.FAIL_RESULT;
        }
        objectNode.put("contactNumber", mobile);
        objectNode.put("authCode", RandomUtils.randomCode());
        objectNode.put("sourceAppName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName: BizConstant.APP_NAME);
        objectNode.put("appChineseName",StringUtils.isNotEmpty(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotEmpty(sourceAppName)?sourceAppName:BizConstant.APP_NAME);
        objectNode.put("mediaName", StringUtils.isNotEmpty(portCrackConfig.getRemark())? portCrackConfig.getRemark() : "今日头条");
        RequestBody body = RequestBody.create(JSON, objectNode.toString());
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();
        log.info("{}-下单接口-手机号:{},渠道号:{},短信验证码:{},请求:{}", portCrackConfig.getLogTag(), phone, channel, code, objectNode.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-下单接口-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channel, code, result);
            return mapper.readValue(result, JunboLlbResult.class);
        } catch (Exception e) {
            log.info("{}-下单接口-手机号:{},渠道号:{},短信验证码:{},异常:", portCrackConfig.getLogTag(), phone, channel, code, e);
            return JunboLlbResult.FAIL_RESULT;
        }
    }

}
