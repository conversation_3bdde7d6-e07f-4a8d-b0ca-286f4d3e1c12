package com.eleven.cms.remote;

import com.eleven.cms.config.HeilongjiangYidongVrbtConfig;
import com.eleven.cms.config.HeilongjiangYidongVrbtProperties;
import com.eleven.cms.config.HeilongjiangYidongXqyVrbtProperties;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HeilongjiangMobileResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;
import java.util.TreeMap;

/**
 * 河北移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class HeilongjiangYidongXqyVrbtService {

    @Autowired
    private Environment environment;
    @Autowired
    private HeilongjiangYidongXqyVrbtProperties heilongjiangYidongXqyVrbtProperties;

    private static final MediaType mediaType = MediaType.parse("application/json");
    private OkHttpClient client;
    private ObjectMapper mapper;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    HeilongjiangMobileResult getSms(String phone, String ip, String channel) throws JsonProcessingException {
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("mobile", phone);
        dataMap.put("ip", ip);
        dataMap.put("zfCode", heilongjiangYidongXqyVrbtProperties.getPrcId());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder()
                .url(heilongjiangYidongXqyVrbtProperties.getGetSmsUrl())
                .post(body)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", heilongjiangYidongXqyVrbtProperties.getLogTag(), phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", heilongjiangYidongXqyVrbtProperties.getLogTag(), phone, content);
            return mapper.readValue(content, HeilongjiangMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", heilongjiangYidongXqyVrbtProperties.getLogTag(), phone, e);
            return HeilongjiangMobileResult.fail();
        }
    }


    // {"code":0,"msg":"办理成功","data":{"res_code":"0","res_desc":"Processing the request succeeded!","result":{"TRADEFORMNUM":"311220217268806262","DETAILINFO":[],"retInnerMsg":"Processing the request succeeded!"}}}
    // 0成功 -1失败

    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    HeilongjiangMobileResult smsCode(String phone, String smsCode, String city,String channel) throws JsonProcessingException {
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("mobile", phone);
        dataMap.put("code", smsCode);
        dataMap.put("region", BizConstant.HLJ_CITY_CODE_LIST.get(city));
        dataMap.put("zfCode", heilongjiangYidongXqyVrbtProperties.getPrcId());
        dataMap.put("zfName", "视频彩铃抖音包");
        dataMap.put("url", heilongjiangYidongXqyVrbtProperties.getPageUrl());
        dataMap.put("pid", heilongjiangYidongXqyVrbtProperties.getPId());
        dataMap.put("platform", heilongjiangYidongXqyVrbtProperties.getChannel());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder()
                .url(heilongjiangYidongXqyVrbtProperties.getSendSmsUrl())
                .post(body)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},请求:{}", heilongjiangYidongXqyVrbtProperties.getLogTag(), phone, smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},响应:{}", heilongjiangYidongXqyVrbtProperties.getLogTag(), phone, smsCode, content);
            JsonNode jsonNode = mapper.readTree(content);
            HeilongjiangMobileResult heilongjiangMobileResult = new HeilongjiangMobileResult();
            heilongjiangMobileResult.setResCode(jsonNode.at("/code").asInt());
            heilongjiangMobileResult.setResMsg(jsonNode.at("/msg").asText());
            return heilongjiangMobileResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},异常:", heilongjiangYidongXqyVrbtProperties.getLogTag(), phone, smsCode, e);
            return HeilongjiangMobileResult.fail();
        }
    }
}
