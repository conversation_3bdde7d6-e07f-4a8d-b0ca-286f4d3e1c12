package com.eleven.cms.remote;

import com.eleven.cms.config.ChongqingYidongVrbtNewProperties;
import com.eleven.cms.config.HainanYidongVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ChongqingMobileNewResult;
import com.eleven.cms.vo.HainanMobileResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 海南移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class HainanYidongVrbtService {

    @Autowired
    private Environment environment;
    @Autowired
    private HainanYidongVrbtProperties hainanYidongVrbtProperties;

    public static final MediaType JSONTYPE
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    public static final String LOG_TAG = "海南移动视频彩铃api";
    private OkHttpClient client;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    HainanMobileResult getSms(String phone) {
        ObjectNode data = mapper.createObjectNode();
        data.put("channelId", hainanYidongVrbtProperties.getChannelId());
        data.put("operatorId", hainanYidongVrbtProperties.getOperatorId());
        data.put("phone", phone);
        RequestBody body = RequestBody.create(JSONTYPE, data.toString());
        log.info("{}-获取短信-手机号:{}", LOG_TAG, phone);
        Request request = new Request.Builder()
                .url(hainanYidongVrbtProperties.getGetSmsUrl())
                .post(body)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, HainanMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return HainanMobileResult.fail();
        }
    }


    //{"errNo":0,"message":"success","tz":"Asia\/Shanghai","data":{"respCode":"0000","respMsg":"success","data":null}}
    // 0成功 -1失败

    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    HainanMobileResult smsCode(String phone, String smsCode) {
        ObjectNode data = mapper.createObjectNode();
        data.put("channelId", hainanYidongVrbtProperties.getChannelId());
        data.put("operatorId", hainanYidongVrbtProperties.getOperatorId());
        data.put("phone", phone);
        data.put("code", smsCode);
        RequestBody body = RequestBody.create(JSONTYPE, data.toString());
        log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{}", LOG_TAG, phone, smsCode);
        Request request = new Request.Builder().url(hainanYidongVrbtProperties.getSendSmsUrl()).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{},响应:{}", LOG_TAG, phone, smsCode, content);
            return mapper.readValue(content, HainanMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{},异常:", LOG_TAG, phone, smsCode, e);
            return HainanMobileResult.fail();
        }
    }
}
