package com.eleven.cms.remote;

import cn.hutool.Hutool;
import cn.hutool.core.util.RandomUtil;
import com.eleven.cms.config.YuxunHunanDianxinProperties;
import com.eleven.cms.util.HunanDianxinMD5;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RSAEncrypt;
import com.eleven.cms.vo.YuxunHunanDianxinResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 豫讯湖南电信业务类
 *
 * @author: cai lei
 * @create: 2022-07-07 10:29
 */

@Slf4j
@Service
public class YuxunHunanDianxinService {

    public static final String LOG_TAG = "豫讯湖南电信api";
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    @Autowired
    private YuxunHunanDianxinProperties yuxunHunanDianxinProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;

    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    //{"code":"0","msg":"成功","orderNum":"2022110315172127809","orderPage":"http://servicemp.114zhan.cn/UnifiedOrder/OrderView?orderNo=2022110315172127809"}
    //910899910252
    //受理成功跳转页面 https://www.baidu.com/?name=XW_01&timestamp=20221103152037&accNum=19976960046&sign=BC02EF0751FD26A14FD99750C582546C&orderNo=2022110315172127809&orderState=1
    public YuxunHunanDianxinResult accpectOrder(String phone, String source) throws Exception {
        Map<String, String> data = new HashMap<>();
        String nonce = RandomUtil.randomString(16);
        data.put("userid", yuxunHunanDianxinProperties.getUserId());
        data.put("nonce",nonce);
        final HttpUrl httpUrl = HttpUrl.parse(yuxunHunanDianxinProperties.getAcceptOrderUrl())
                .newBuilder()
                .addQueryParameter("phone", phone)
                .addQueryParameter("userid", yuxunHunanDianxinProperties.getUserId())
                .addQueryParameter("nonce", nonce)
                .addQueryParameter("backUrl", source.indexOf("?") > 0 ? source.substring(0, source.indexOf("?")) : source)
                .addQueryParameter("sign", HunanDianxinMD5.md5("userid=" + yuxunHunanDianxinProperties.getUserId() + "&secretKey=" + yuxunHunanDianxinProperties.getSecretKey() + "&nonce=" + nonce))
                .build();
        log.info("{}-业务手受理接口,手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-业务手受理接口,手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, YuxunHunanDianxinResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-业务手受理接口,手机号:{},响应,异常:", LOG_TAG, phone, e);
            return YuxunHunanDianxinResult.FAIL_RESULT;
        }

    }


//    private String generateSign(Map<String, String> map, String privateKey) throws Exception {
//        String signStr = "";
//        map = HunanDianxinMD5.sortMapByKey(map);
//        for (Map.Entry<String, String> entry : map.entrySet()) {
//            signStr += (entry.getKey() + entry.getValue());
//        }
//        map.put("sign", HunanDianxinMD5.md5Up(signStr));
//        return RSAEncrypt.encrypt(mapper.writeValueAsString(map), yuxunHunanDianxinProperties.getPrivateKey());
//    }
}