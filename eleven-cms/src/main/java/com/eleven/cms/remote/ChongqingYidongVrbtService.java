package com.eleven.cms.remote;

import com.eleven.cms.config.ChongqingYidongVrbtProperties;
import com.eleven.cms.config.HebeiYidongVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ChongqingMobileResult;
import com.eleven.cms.vo.HebeiMobileResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 河北移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class ChongqingYidongVrbtService {

    @Autowired
    private Environment environment;
    @Autowired
    private ChongqingYidongVrbtProperties chongqingYidongVrbtProperties;

    public static final String LOG_TAG = "重庆移动视频彩铃api";
    private OkHttpClient client;
    private ObjectMapper mapper;


    //  @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }



    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    ChongqingMobileResult getSms(String phone,String bizCode) {
        final HttpUrl httpUrl = HttpUrl.parse(chongqingYidongVrbtProperties.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("phoneNumber", phone)
                .addQueryParameter("ncode", bizCode)
                .addQueryParameter("cId", chongqingYidongVrbtProperties.getCId())
                .build();
        log.info("{}-获取短信-手机号:{},业务代码:{},请求:{}", LOG_TAG, phone, bizCode, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},业务代码:{},响应:{}", LOG_TAG, phone, bizCode, content);
            return mapper.readValue(content, ChongqingMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},业务代码:{},异常:", LOG_TAG, phone, bizCode, e);
            return ChongqingMobileResult.fail();
        }
    }


    //{"errNo":0,"message":"success","tz":"Asia\/Shanghai","data":{"respCode":"0000","respMsg":"success","data":null}}
    // 0成功 -1失败
    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    ChongqingMobileResult smsCode(String phone, String smsCode,String orderId,String bizCode) {
        final HttpUrl httpUrl = HttpUrl.parse(chongqingYidongVrbtProperties.getSendSmsUrl())
                .newBuilder()
                .addQueryParameter("cId", chongqingYidongVrbtProperties.getCId())
                .addQueryParameter("randomOrderId", orderId)
                .addQueryParameter("telnum", phone)
                .addQueryParameter("ncode", bizCode)
                .addQueryParameter("stype", "ADD")
                .addQueryParameter("random", smsCode)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{},请求:{}", LOG_TAG, phone, smsCode, bizCode, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{},响应:{}", LOG_TAG, phone, smsCode, bizCode, content);
            return mapper.readValue(content, ChongqingMobileResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{},异常:", LOG_TAG, phone, smsCode, bizCode, e);
            return ChongqingMobileResult.fail();
        }
    }
}
