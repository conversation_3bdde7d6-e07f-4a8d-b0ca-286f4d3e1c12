package com.eleven.cms.remote;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.config.TianyiSpaceProperties;
import com.eleven.cms.entity.TelecomOrder;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.ITelecomOrderService;
import com.eleven.cms.util.MD5;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class TianyiSpaceService {

    public static final String LOG_TAG = "天翼空间api";

    @Autowired
    TianyiSpaceProperties tianyiSpaceProperties;

    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    ITelecomOrderService telecomOrderService;

    public static final String SERVICE_ID = "90030426";

    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 生成支付链接
     */
    public String generatePayUrl(String phone) throws Exception {

        //计算sign
        Map<String, Object> sigMap = new HashMap<String, Object>();
        sigMap.put("version", tianyiSpaceProperties.getVersion());
        sigMap.put("sp_id", tianyiSpaceProperties.getSpId());
        sigMap.put("payid", tianyiSpaceProperties.getPayId());
        sigMap.put("goods_name", tianyiSpaceProperties.getGoodsName());
        String cp_order_id = tianyiSpaceProperties.getSpId() + "_" + phone + "_" + System.currentTimeMillis();
        if (cp_order_id.length() > 32) {
            cp_order_id = cp_order_id.substring(0, 32);
        }
        sigMap.put("cp_order_id", cp_order_id);
        sigMap.put("phone", phone);
        sigMap.put("alter_phone", tianyiSpaceProperties.getAlterPhone());
        sigMap.put("timestamp", System.currentTimeMillis() / 1000L);
        sigMap.put("reback_url", URLEncoder.encode(tianyiSpaceProperties.getRebackUrl(), "utf-8"));
//        sigMap.put("paytype_url", URLEncoder.encode(paytype_url, "utf-8"));
        sigMap.put("callback_url", URLEncoder.encode(tianyiSpaceProperties.getCallbackUrl(), "utf-8"));
        sigMap.put("notify_url", URLEncoder.encode(tianyiSpaceProperties.getNotifyUrl(), "utf-8"));
        sigMap.put("ext_data", tianyiSpaceProperties.getExtData());
        System.out.println("sigMap:" + sigMap);
        log.info("sigMap:{}", sigMap);
        String sign = generateSign(sigMap, tianyiSpaceProperties.getSignKey());
        List<String> keys = new ArrayList<String>();
        Iterator<String> it = sigMap.keySet().iterator();
        while (it.hasNext()) {
            String key = String.valueOf(it.next());
            if (!"sign".equals(key)) {
                keys.add(key);
            }
        }
        StringBuffer sb = new StringBuffer();
        for (String key : keys) {
            String value = String.valueOf(sigMap.get(key));
            sb.append("&" + key + "=" + value.trim());
        }
        sb.append("&sign=" + sign);
        String params = sb.toString();
        log.info("{}-生成交易链接-手机号:{},链接:{}", LOG_TAG, phone, tianyiSpaceProperties.getPayUrl() + "?" + params.substring(1));
        redisUtil.set(CacheConstant.MOBILE_ORDER_CACHE + ":" + phone, cp_order_id, 3600 * 24L);
        return tianyiSpaceProperties.getPayUrl() + "?" + params.substring(1);
    }

    public void createOrderNo() {


    }


    /**
     * 访问支付链接获取cookie
     *
     * @param phone
     * @param url
     * @throws Exception
     */
    // @Cacheable(cacheNames = RedisCacheConfig.MOBILE_COOKIE_CACHE, key = "#p0", condition = "#p0!=null", unless = "#result==null")
    public void visitPayUrl(String phone, String url) throws Exception {
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-获取获取cookie-手机号:{},请求:{}", LOG_TAG, phone, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            Headers headers = response.headers();
            String cookie = headers.get("Set-Cookie");
            log.info("{}-获取cookie-手机号:{},cookie:{}", LOG_TAG, phone, cookie);
            redisUtil.set(CacheConstant.MOBILE_COOKIE_CACHE + ":" + phone, cookie, 1800L);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-获取cookie-手机号:{},异常:", LOG_TAG, phone, e);

        }
        //  return null;
    }

    /**
     * 获取短信
     *
     * @param phone
     */
    //{"msg":"短信验证码已发送成功","a":"803817190000002702","code":0,"t":"6ee720e60563451ebc74341338dbaf47","price":"￥1","fc":"贝壳闲话10贝壳币","sms_num":"R83","ft":"按次计费","ts":1628477904}
    public boolean getSms(String phone) throws Exception {
        String cookie = (String) redisUtil.get(CacheConstant.MOBILE_COOKIE_CACHE + ":" + phone);
        if (!StringUtils.isNotBlank(cookie)) {
            log.error("手机号:{},获取验证码错误,会话超时", phone);
            throw new Exception("获取验证码错误,会话超时");
        }

        log.info("{}-手机号:{},cookie:{}", LOG_TAG, phone, cookie);

        String getSmsUrl = tianyiSpaceProperties.getGetSmsUrl();
        RequestBody body = new FormBody.Builder().add("mobile_phone", phone).build();
        Request request = new Request.Builder().url(getSmsUrl).post(body)
                .addHeader("Cookie", cookie)
                .build();

        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG, phone, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, phone, result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (0 == jsonObject.getInteger("code")) {
                String t = jsonObject.getString("t");
                String a = jsonObject.getString("a");
                String ts = jsonObject.getString("ts");
                redisUtil.set(CacheConstant.MOBILE_SMS_CACHE + ":" + phone, jsonObject, 600L);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return false;
        }

    }


    /**
     * 提交验证码
     *
     * @param phone
     * @param code
     * @throws Exception
     */

    //{"msg":"计费确认成功","code":0,"reback_url":"http%3A%2F%2Fwww.baidu.com"}
    //{"msg":"验证码错误","code":1062}
    public JsonNode confirm(String phone, String code) throws Exception {

        String cookie = (String) redisUtil.get(CacheConstant.MOBILE_COOKIE_CACHE + ":" + phone);
        if (!StringUtils.isNotBlank(cookie)) {
            log.error("手机号:{},提交验证码错误,会话超时", phone);
            throw new Exception("提交验证码错误,会话超时");
        }

        log.info("{}-手机号:{},cookie:{}", LOG_TAG, phone, cookie);
        String confirmUrl = tianyiSpaceProperties.getConfirmUrl();
        JSONObject jsonObject = (JSONObject) redisUtil.get(CacheConstant.MOBILE_SMS_CACHE + ":" + phone);
        if (jsonObject == null) {
            log.error("手机号:{},提交验证码错误,无效的数据", phone);
            throw new Exception("手机号:" + phone + "提交验证码错误,无效的数据");
        }
        String t = jsonObject.getString("t");
        String a = jsonObject.getString("a");
        String ts = jsonObject.getString("ts");
        RequestBody body = new FormBody.Builder()
                .add("t", t)
                .add("a", a)
                .add("ts", ts)
                .add("v", code)
                .build();
        Request request = new Request.Builder().url(confirmUrl).post(body)
                .addHeader("Cookie", cookie)
                .build();


        log.info("{}-提交短信-手机号:{},请求:{}", LOG_TAG, phone, request.toString());

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交短信-手机号:{},响应:{}", LOG_TAG, phone, result);
            return mapper.readTree(result);
        } catch (Exception e) {
            log.info("{}-提交短信-手机号:{},异常:", LOG_TAG, phone, e);
            throw new Exception("提交验证码错误");
        }
    }


    public String generateSign(Map<String, Object> map, Object versionKey) throws Exception {
        String newSig = "";
        try {
            List<String> keys = new ArrayList<String>();
            Iterator<String> it = map.keySet().iterator();
            while (it.hasNext()) {
                String key = String.valueOf(it.next());
                if (!"sign".equals(key)) {
                    keys.add(key);
                }
            }
            //按照key升序排列进行组装字符串
            Collections.sort(keys);
            StringBuffer sb = new StringBuffer();
            for (String key : keys) {
                String value = String.valueOf(map.get(key));
                if (value != null && !"".equals(value.trim())) {
                    sb.append(key + value.trim());
                }
            }
            sb.append(versionKey);
            newSig = MD5.GetMD5Code(sb.toString());
            return newSig;
        } catch (Exception e) {
            throw e;
        }
    }


    /**
     * 查询是否已有订购
     *
     * @param phone
     * @return
     * @throws Exception
     */
    public boolean queryRelationShip(String phone) throws Exception {

        String relationShipUrl = tianyiSpaceProperties.getRelationShipUrl();
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

        Map<String, Object> sigMap = new HashMap<String, Object>();
        sigMap.put("sp_id", tianyiSpaceProperties.getSpId());
        sigMap.put("phone", phone);
        sigMap.put("payid", tianyiSpaceProperties.getPayId());
        sigMap.put("timestamp", timestamp);
        sigMap.put("version", 1);
        String sign = generateSign(sigMap, tianyiSpaceProperties.getSignKey());

        RequestBody body = new FormBody.Builder().add("sp_id", tianyiSpaceProperties.getSpId())
                .add("phone", phone)
                .add("payid", tianyiSpaceProperties.getPayId())
                .add("timestamp", timestamp)
                .add("version", "1")
                .add("sig", sign)
                .build();

        Request request = new Request.Builder().url(relationShipUrl).post(body).build();

        log.info("{}-查询是否已有订购-手机号:{},请求:{}", LOG_TAG, phone, request.toString());


        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询是否已有订购-手机号:{},响应:{}", LOG_TAG, phone, result);
            JsonNode tree = mapper.readTree(result);
            return tianyiSpaceProperties.getPayId().equals(tree.at("/result/0/payid").asText());
        } catch (Exception e) {
            log.info("{}-查询是否已有订购-手机号:{},异常:", LOG_TAG, phone, e);
            return false;
        }
    }


    /**
     * 退订
     *
     * @param phone
     * @return
     * @throws Exception
     */
    public boolean unsubscribe(String phone) throws Exception {


//        QueryWrapper<TelecomOrder> queryWrapper = new QueryWrapper();
//        queryWrapper.eq("phone",phone).eq("type","1").eq("res_code","200");
//        queryWrapper.orderByDesc("create_time");
//        queryWrapper.last("limit 1");
//
//
//        TelecomOrder telecomOrder = telecomOrderMapper.selectOne(queryWrapper);

        TelecomOrder telecomOrder = telecomOrderService.lambdaQuery().eq(TelecomOrder::getPhone, phone).eq(TelecomOrder::getResCode, "200").
                eq(TelecomOrder::getType, "1").orderByDesc(TelecomOrder::getCreateTime).last("limit 1").one();
        //查询最近的订单号
        if (telecomOrder == null || StringUtils.isEmpty(telecomOrder.getOrderid())) {
            log.warn("手机号:{}未找到订购数据", phone);
            telecomOrder = new TelecomOrder();
            telecomOrder.setOrderid(tianyiSpaceProperties.getSpId() + "_" + phone + "_" + System.currentTimeMillis() / 1000L);
        }

        String unsubscribeUrl = tianyiSpaceProperties.getUnsubscribeUrl();
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

        //TODO 查询用户订单号
        Map<String, Object> sigMap = new HashMap<String, Object>();
        sigMap.put("sp_id", tianyiSpaceProperties.getSpId());
        sigMap.put("phone", phone);
        sigMap.put("payid", tianyiSpaceProperties.getPayId());
        sigMap.put("cp_order_id", telecomOrder.getOrderid());
        sigMap.put("cp_user_id", phone);
        sigMap.put("timestamp", timestamp);
        sigMap.put("version", 1);
        String sign = generateSign(sigMap, tianyiSpaceProperties.getSignKey());


        RequestBody body = new FormBody.Builder().add("sp_id", tianyiSpaceProperties.getSpId())
                .add("phone", phone)
                .add("payid", tianyiSpaceProperties.getPayId())
                .add("cp_order_id", telecomOrder.getOrderid())
                .add("cp_user_id", phone)
                .add("timestamp", timestamp)
                .add("version", "1")
                .add("sig", sign)
                .build();

        Request request = new Request.Builder().url(unsubscribeUrl).post(body).build();
        log.info("{}-用户业务退订-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-用户业务退订-手机号:{},响应:{}", LOG_TAG, phone, result);
            JsonNode tree = mapper.readTree(result);
            if (0 == tree.at("/code").asInt()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.info("{}-用户业务退订-手机号:{},异常:", LOG_TAG, phone, e);
            return false;
        }

    }

//
//    /**
//     * 查询用户是否已有包月,未包月获取验证码
//     * @param subscribe
//     * @return
//     * @throws Exception
//     */
//    public FebsResponse handleDianxinExists(Subscribe subscribe) throws Exception {
//        final Long id = subscribe.getId();
//        final FebsResponse errorResult = new FebsResponse().error("系统繁忙,请稍后再试!");
//        final FebsResponse bizExistsResult = new FebsResponse().bizExists("你已开通,请勿重复开通");
//        final FebsResponse noauthResult = new FebsResponse().noauth("验证码已发送", id);
//
//        final String mobile = subscribe.getMobile();
//
//        final TianyiSpaceService tianyiSpaceService = SpringContextUtils.getBean(TianyiSpaceService.class);
//        final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
//
//        //查询是否已开通了包月
//        boolean exists = tianyiSpaceService.queryRelationShip(mobile);
//        if(exists){
//            //已有包月
//            Subscribe sub = new Subscribe();
//            sub.setId(id);
//            sub.setStatus(BizConstant.SUBSCRIBE_STATUS_MONTHLY_EXISTS);
//            sub.setResult("已有包月");
//            subscribeService.updateSubscribeDbAndEs(sub);
//            return bizExistsResult;
//        }
//
//        boolean flag = tianyiSpaceService.getSms(mobile);
//        if(flag){
//            return noauthResult;
//        }else{
//            return errorResult;
//        }
//    }

    public static void main(String[] args) throws JsonProcessingException {
        String result = "{\"result\":[],\"sig\":\"D2455CE190E7414EB77CF4BEE5DE6A88\",\"code\":\"0\",\"phone\":\"18080928200\",\"message\":\"查询成功\"}";
        JsonNode tree = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readTree(result);
        System.out.println(tree.at("/result/0/payid"));
//        System.out.println(tree.at("result/0/payid").asText());

    }

}
