package com.eleven.cms.remote;

import com.eleven.cms.captcha.CaptchaService;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.ShandongHexiaoyuanProperties;
import com.eleven.cms.service.impl.IMobileRegionResultService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;


/**
 * Author: <EMAIL>
 * Date: 2020/4/20 15:33
 * Desc:号码归属地服务
 */
@Slf4j
@Service
public class MobileRegionService {
    private static final Long MOBILE_TIME =86400L;
    @Autowired
    private BizProperties bizProperties;

    OkHttpClient client = OkHttpClientUtils.getSingletonInstance();
    ObjectMapper mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    @Autowired
    private Environment environment;

    private boolean isDevProfile=false;
    @Autowired
    private IMobileRegionResultService mobileRegionResultService;
    @Autowired
    private RedisUtil redisUtil;


    @PostConstruct
    public void init() {
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            isDevProfile=true;
        }
    }
    public MobileRegionResult query(String mobile){
            //判断是否测试环境
//            if(isDevProfile){
                return mobileRegionResultService.query(mobile);
//            }else{
//                return queryMobile(mobile);
//            }
    }
    public MobileRegionResult queryMobile(String mobile){
        String realKey = CacheConstant.CMS_MOBILE_REGION_CACHE+":"+mobile;
        if(redisUtil.hasKey(realKey)){
            MobileRegionResult result = null;
            try {
                result = mapper.readValue(redisUtil.get(realKey).toString(), MobileRegionResult.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return result;
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.getMobileRegionServerUrl())
                .newBuilder()
                .addQueryParameter("phone",mobile)
                .addQueryParameter("key",bizProperties.getMobileRegionApiKey())
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            String jsonData = response.body().string();
            log.info("号码{},归属地查询结果:{}",mobile, StringUtils.trim(jsonData));
            MobileRegionResult regionResult=mapper.readerFor(MobileRegionResult.class).readValue(jsonData);
            ObjectNode objectNode = mapper.valueToTree(regionResult);
            redisUtil.set(realKey,objectNode.toString(), MOBILE_TIME);
            return regionResult;

        } catch (Exception e) {
            log.warn("手机号归属地查询失败=>号码:{},异常!",mobile,e);
            return null;
        }
    }

}
