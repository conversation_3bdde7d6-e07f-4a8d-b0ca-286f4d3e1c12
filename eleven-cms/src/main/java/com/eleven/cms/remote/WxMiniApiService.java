package com.eleven.cms.remote;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.aivrbt.dto.WxMiniUserInfoDTO;
import com.eleven.cms.aivrbt.dto.WxSessionInfoDTO;
import com.eleven.cms.aivrbt.enums.MiniAppChannelEnum;
import com.eleven.cms.aivrbt.vo.WxMiniGetUserInfoVO;
import com.eleven.cms.config.WxMiniApiProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.wxpay.sdk.WXPayConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 联通视频彩铃pojie
 *
 * @author: cai lei
 * @create: 2022-04-07 09:51
 */
@Slf4j
@Service
public class WxMiniApiService {

    @Autowired
    private Environment environment;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private WxMiniApiProperties wxMiniApiProperties;

    public static final String LOG_TAG = "微信小程序api";

    public static final String WX_CHANNEL_ACCESS_TOKEN_CHECK = "wx_channel_access_token_";

    private static final Integer WX_ACCESS_TOKEN_INVALID_CODE = 40001;

    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

         @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 获取手机号
     *
     * @param code
     * @return
     */
    public String getPhoneNum(String code,MiniAppChannelEnum miniAppChannelEnum) {
        String channelCode = Objects.isNull(miniAppChannelEnum) ? "default" : miniAppChannelEnum.getCode();
        String phoneNumber = "";
        Map<String, Object> map = new HashMap<>();
        String redisKey = WX_CHANNEL_ACCESS_TOKEN_CHECK+channelCode;
        //从缓存取出access_token
        String accessToken = redisUtil.get(redisKey) == null ? "" : redisUtil.get(redisKey).toString();
        if(StringUtils.isBlank(accessToken)){
           accessToken = getWxAccessToken(miniAppChannelEnum,redisKey);
        }
        try {
            map.put("code",code);
            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(map));
            HttpUrl httpUrl = HttpUrl.parse(wxMiniApiProperties.getUserPhonenumberUrl())
                    .newBuilder()
                    .addQueryParameter("access_token", accessToken)
                    .build();
            log.info("{}-获取手机号,请求:{}", LOG_TAG,httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();
            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-获取手机号,响应:{}", LOG_TAG,content);
                JsonNode jsonNode = mapper.readTree(content);
                // 添加accesstoken重试机制，因为如果同时申请了多个token(比如我们需要测试时候)，微信会把最开始的token设置失效，
                int wxErrCode = jsonNode.at("/errcode").asInt();
                if(WX_ACCESS_TOKEN_INVALID_CODE == wxErrCode){
                    redisUtil.del(redisKey);
                   return getPhoneNum(code,miniAppChannelEnum);
                }
                phoneNumber = jsonNode.at("/phone_info").at("/phoneNumber").asText();
            }
        } catch (Exception e) {
            log.info("{}-获取手机号,出错:", LOG_TAG,e);
        }
        return phoneNumber;
    }

    public String getWxAccessToken( MiniAppChannelEnum miniAppChannelEnum,String redisKey){
        String accessToken = StringUtils.EMPTY;
        try {

            HttpUrl httpUrl = HttpUrl.parse(wxMiniApiProperties.getAccessTokenUrl())
                    .newBuilder()
                    .addQueryParameter("grant_type", "client_credential")
                    .addQueryParameter("appid",getWxMiniAppId(miniAppChannelEnum))
                    .addQueryParameter("secret",getWxMiniSecret(miniAppChannelEnum))
                    .build();
            log.info("{}-获取access_token,请求:{}", LOG_TAG,httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                    .build();
            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                accessToken = mapper.readTree(content).at("/access_token").asText();
                redisUtil.set(redisKey,accessToken,3600L);
                log.info("{}-获取access_token,响应:{}", LOG_TAG,content);
            }
        } catch (Exception e) {
            log.info("{}-获取access_token,出错:", LOG_TAG,e);
        }
        return accessToken;
    }

    private String getWxMiniAppId(MiniAppChannelEnum miniAppChannelEnum) {
        if(Objects.isNull(miniAppChannelEnum)){
            return wxMiniApiProperties.getAppid();
        }
        if(Objects.equals(miniAppChannelEnum.getCode(),MiniAppChannelEnum.AMUSING_INCOMING_CALL.getCode())){
            return wxMiniApiProperties.getAmusingIncomingCallAppId();
        } else if(Objects.equals(miniAppChannelEnum.getCode(),MiniAppChannelEnum.COOL_INCOMING_CALL.getCode())){
            return wxMiniApiProperties.getCoolIncomingCallAppId();
        } else if (Objects.equals(miniAppChannelEnum.getCode(), MiniAppChannelEnum.COOL_INCOMING_CALL_WX_MINI_APP.getCode())) {
            return wxMiniApiProperties.getCoolIncomingCallWxMiniAppId();
        }
        return StringUtils.EMPTY;
    }

    private String getWxMiniSecret(MiniAppChannelEnum miniAppChannelEnum) {
        if(Objects.isNull(miniAppChannelEnum)){
            return wxMiniApiProperties.getSecret();
        }
        if(Objects.equals(miniAppChannelEnum.getCode(),MiniAppChannelEnum.AMUSING_INCOMING_CALL.getCode())){
            return wxMiniApiProperties.getAmusingIncomingCallSecret();
        } else if(Objects.equals(miniAppChannelEnum.getCode(),MiniAppChannelEnum.COOL_INCOMING_CALL.getCode())){
            return wxMiniApiProperties.getCoolIncomingCallSecret();
        } else if (Objects.equals(miniAppChannelEnum.getCode(), MiniAppChannelEnum.COOL_INCOMING_CALL_WX_MINI_APP.getCode())) {
            return wxMiniApiProperties.getCoolIncomingCallWxMiniSecret();
        }
        return StringUtils.EMPTY;
    }

    private WxSessionInfoDTO getSessionKey(String code, MiniAppChannelEnum miniAppChannelEnum) {

        String url = WXPayConstants.WECHAT_APPLETS_OPEN_ID_URL + "?" +
                "appid=" + getWxMiniAppId(miniAppChannelEnum) +
                "&secret=" + getWxMiniSecret(miniAppChannelEnum) +
                "&js_code=" + code +
                "&grant_type=authorization_code";
        Request request = new Request.Builder().url(url)
                .build();
        JSONObject jsonObject = new JSONObject();
        try (Response response = client.newCall(request)
                .execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            jsonObject = JSONObject.parseObject(content);
            // redisUtil.set(redisKey,accessToken,3600L);
            log.info("{}-获取getSessionKey access_token,响应:{}", LOG_TAG, content);
        } catch (Exception e) {
            log.error("getSessionKey error", e);
            throw new JeecgBootException("getSessionKey获取session_key失败");
        }

        if (jsonObject.containsKey("errcode")) {
            throw new JeecgBootException("获取session_key失败: " + jsonObject.getString("errmsg"));
        }

        // 提取session_key、openid和unionid
        String sessionKey = jsonObject.getString("session_key");
        String openId = jsonObject.getString("openid");
        String unionId = jsonObject.getString("unionid"); // 可能为空

        return WxSessionInfoDTO.builder().sessionKey(sessionKey).unionId(unionId).openId(openId).build();
    }

    public Result<Object> wxMiniGetPhone(String code, String source) {
        MiniAppChannelEnum miniAppChannelEnum = MiniAppChannelEnum.getWxByCode(source);
        if(Objects.isNull(miniAppChannelEnum)){
            return Result.msgWxMiniNotSupport();
        }
        String phoneNum = getPhoneNum(code,miniAppChannelEnum);

        if(StringUtils.isEmpty(phoneNum)){
            return Result.error("获取手机号失败，请重试");
        }
        return Result.okAndSetData(phoneNum);
    }

    // 解密手机号，同时返回OpenID
    public String decryptPhoneNumber(String encryptedData, String iv, String sessionKey) {

        // 2. 解密手机号
        byte[] sessionKeyBytes = Base64.getDecoder().decode(sessionKey);
        byte[] encryptedDataBytes = Base64.getDecoder().decode(encryptedData);
        byte[] ivBytes = Base64.getDecoder().decode(iv);

        // AES解密
        Cipher cipher = null;
        try {
            cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(sessionKeyBytes, "AES");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec);

            byte[] decryptedBytes = cipher.doFinal(encryptedDataBytes);
            String decryptedData = new String(decryptedBytes, StandardCharsets.UTF_8);

            // 解析手机号
            JSONObject jsonObject = JSONObject.parseObject(decryptedData);

            // 返回包含手机号和OpenID的结果
            return jsonObject.getString("phoneNumber");
        } catch (Exception e) {
            log.error("decryptPhoneNumber error", e);
            throw new JeecgBootException("获取手机号码失败");
        }

    }

    public WxMiniGetUserInfoVO wxMiniGetUserInfo(WxMiniUserInfoDTO wxMiniUserInfoDTO) {
        MiniAppChannelEnum miniAppChannelEnum = MiniAppChannelEnum.getWxByCode(wxMiniUserInfoDTO.getChannelId());
        if (Objects.isNull(miniAppChannelEnum)) {
            log.error("wxMiniGetUserInfo error 获取手机号码失败，请先配置");
            throw new JeecgBootException("获取手机号码失败，请先配置");
        }
        WxSessionInfoDTO sessionInfoDTO = getSessionKey(wxMiniUserInfoDTO.getCode(), miniAppChannelEnum);
        String phoneNumber = decryptPhoneNumber(wxMiniUserInfoDTO.getEncryptedData(), wxMiniUserInfoDTO.getIv(), sessionInfoDTO.getSessionKey());
        return WxMiniGetUserInfoVO.builder().phoneNumber(phoneNumber).openId(sessionInfoDTO.getOpenId()).build();
    }
}
