package com.eleven.cms.remote;

import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * @author: cai lei
 * @create: 2022-01-19 15:00
 */
@Slf4j
@Service
public class BjhyDataService {

    private static final String LOG_TAG = "白金会员数据保存API";

    private OkHttpClient client;
    private ObjectMapper mapper;

    @Value("${saveBjhyUrl}")
    private String saveBjhyUrl;


//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Async
    public void saveData(String mobile, String channel, String serviceId){
        final HttpUrl httpUrl = HttpUrl.parse(saveBjhyUrl)
                .newBuilder()
                .addQueryParameter("mobile", mobile)
                .addQueryParameter("channel", channel)
                .addQueryParameter("serviceId", serviceId)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-手机号:{},请求:{}", LOG_TAG, mobile, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-手机号:{},返回结果:{}", LOG_TAG, mobile, result);
        } catch (Exception e) {
            log.info("{}-手机号:{},异常:", LOG_TAG, mobile, e);
        }
    }
}
