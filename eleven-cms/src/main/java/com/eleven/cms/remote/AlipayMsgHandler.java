package com.eleven.cms.remote;

import com.alipay.api.msg.MsgHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.nio.MappedByteBuffer;

/**
 * Author: <EMAIL>
 * Date: 2023/4/4 17:13
 * Desc: 阿里云蚂蚁消息服务处理实现类
 */
@Slf4j
public class AlipayMsgHandler implements MsgHandler {

    public static final String LOG_TAG = "支付宝蚂蚁消息处理";

    private String appId;
    private String businessType;
    private AliComplainService aliComplainService;

    public AlipayMsgHandler(String appId, String businessType, AliComplainService aliComplainService) {
        this.appId = appId;
        this.businessType = businessType;
        this.aliComplainService = aliComplainService;
    }

    /**
     * 客户端接收到消息后回调此方法
     *  @param  msgApi 接收到的消息的消息api名
     *  @param  msgId 接收到的消息的消息id
     *  @param  bizContent 接收到的消息的内容，json格式
     */
    @Override
    public void onMessage(String msgApi, String msgId, String bizContent) {
        try {
            //收到消息. msgApi:alipay.merchant.tradecomplain.changed,msgId:2023040400262170605018684259106069,bizContent:{"complain_event_id":"2023040400102000000012362275"}
            log.info( "{}-收到消息. msgApi:{},msgId:{},bizContent:{}",LOG_TAG, msgApi, msgId, bizContent);
            final String complainEventId = new ObjectMapper().readTree(bizContent).at("/complain_event_id").asText();
            aliComplainService.alipayQueryComplainDetail(complainEventId, this.appId, businessType);
        } catch (Exception e) {
            log.info("{}-消息处理,msgApi:{},msgId:{},bizContent:{},异常:", LOG_TAG, msgApi, msgId, bizContent, e);
        }
    }

    public AliComplainService getAliComplainService() {
        return aliComplainService;
    }

    public void setAliComplainService(AliComplainService aliComplainService) {
        this.aliComplainService = aliComplainService;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public static void main(String[] args) throws JsonProcessingException {
        String bizContent = "{\"complain_event_id\":\"2023040400102000000012362275\"}";
        final String complainEventId = new ObjectMapper().readTree(bizContent).at("/complain_event_id").asText();
        System.out.println("complainEventId = " + complainEventId);
    }
}
