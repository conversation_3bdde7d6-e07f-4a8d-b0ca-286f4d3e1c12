package com.eleven.cms.remote;

import com.alipay.api.FileItem;
import com.alipay.api.response.AlipayMerchantImageUploadResponse;
import com.alipay.api.response.AlipayMerchantTradecomplainQueryResponse;
import com.eleven.cms.config.AliPayComplainProperties;
import com.eleven.cms.entity.AliSignChargingOrder;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.entity.AlipayComplain;
import com.eleven.cms.service.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;

@Slf4j
@Service
public class AliComplainService {
    @Autowired
    private IAlipayService alipayService;
    @Autowired
    private IAlipayComplainService alipayComplainService;
    @Autowired
    private AliPayComplainProperties aliPayComplainProperties;
    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    private IWarnMobileService warnMobileService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
            false);

    /**
     * 查询投诉详细
     * @param complainEventId
     * @param appId
     */
    @Async
    public Result<?> alipayQueryComplainDetail(String complainEventId, String appId,String businessType){
        return alipayQueryComplaiDetailMethod(complainEventId, appId, businessType);
    }

    /**
     * 查询投诉详细
     * @param complainEventId
     * @param appId
     */
    public Result<?> alipayQueryComplainDetail(String complainEventId,String appId){
        return alipayQueryComplaiDetailMethod(complainEventId, appId, null);
    }

    private Result<Object> alipayQueryComplaiDetailMethod(String complainEventId, String appId, String businessType) {
        AlipayMerchantTradecomplainQueryResponse response=alipayService.alipayQueryComplainDetail(complainEventId, appId);
        AlipayComplain alipayComplain=alipayComplainService.lambdaQuery().eq(AlipayComplain::getComplainEventId, complainEventId).orderByDesc(AlipayComplain::getCreateTime).last("limit 1").one();
        if(alipayComplain==null){
            alipayComplain=new AlipayComplain();
        }
        /**商户号appId*/
        alipayComplain.setAppId(appId);
        /**支付宝侧投诉单号*/
        alipayComplain.setComplainEventId(complainEventId);

        if(response.isSuccess()){
            /**状态*/
//          String complainMsg=ComplainEnum.getByStatus(response.getStatus());
//          alipayComplain.setComplainStatus(StringUtils.isEmpty(complainMsg)?"未知状态":complainMsg);
            alipayComplain.setComplainStatus(response.getStatus());
            /**支付宝交易号*/
            alipayComplain.setTradeNo(response.getTradeNo());
            /**商家订单号*/
            alipayComplain.setMerchantOrderNo(response.getMerchantOrderNo());
            /**投诉单创建时间*/
            alipayComplain.setGmtCreate(response.getGmtCreate());
            /**投诉单修改时间*/
            alipayComplain.setGmtModified(response.getGmtModified());
            /**投诉单完结时间*/
            alipayComplain.setGmtFinished(response.getGmtFinished());
            /**用户投诉诉求*/
            alipayComplain.setLeafCategoryName(response.getLeafCategoryName());
            /**用户投诉原因*/
            alipayComplain.setComplainReason(response.getComplainReason());
            /**用户投诉内容*/
            alipayComplain.setComplainContent(response.getContent());
            /**投诉人电话号码*/
            alipayComplain.setPhoneNo(response.getPhoneNo());
            /**交易金额*/
            alipayComplain.setTradeAmount(response.getTradeAmount());

            //设置签约状态
            AliSignChargingOrder orderPay=aliSignChargingOrderService.lambdaQuery()
                    .eq(AliSignChargingOrder::getOrderNo, response.getMerchantOrderNo())
                    .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
            if(orderPay!=null){
                AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo,orderPay.getExternalAgreementNo()).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
                if(aliSignRecord!=null){
                    alipayComplain.setSignStatus(aliSignRecord.getSignStatus());
                }
                alipayComplain.setRefundStatus(aliSignRecord.getRefundStatus());
                alipayComplain.setBusinessType(orderPay.getBusinessType());
                alipayComplain.setBusinessName(orderPay.getBusinessName());
                alipayComplain.setPayTime(orderPay.getPayTime());
                alipayComplain.setMobile(orderPay.getMobile());
            }
            try {
                //投诉图片
                if(response.getImages()!=null){
                    String images = mapper.writeValueAsString(response.getImages());
                    alipayComplain.setImages(images);
                }
//                alipayComplain.setImages(response.getImages());
//                投诉消息
                if(response.getReplyDetailInfos()!=null){
                    String replyDetailInfos = mapper.writeValueAsString(response.getReplyDetailInfos());
                    alipayComplain.setReplyDetailInfos(replyDetailInfos);
                }
//                alipayComplain.setReplyDetailInfos(response.getReplyDetailInfos());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        alipayComplain.setUpdateTime(new Date());
        alipayComplain.setComplainRemark("投诉明细==>{"+response.getCode()+":"+response.getMsg()+","+response.getSubCode()+":"+response.getSubMsg()+"}");
        if(alipayComplain.getId()==null){
            alipayComplainService.save(alipayComplain);
            //待处理 渠道号正常发送提醒短信
            if(businessType!=null && StringUtils.equals("MERCHANT_PROCESSING",alipayComplain.getComplainStatus())){
                smsNotify(businessType,complainEventId);
            }
            return Result.ok("投诉明细查询成功！",alipayComplain);
        }
        alipayComplainService.lambdaUpdate().eq(AlipayComplain::getComplainEventId, complainEventId).update(alipayComplain);
        return Result.ok("投诉明细查询成功！",alipayComplain);
    }

    public void smsNotify(String businessType,String complainEventId) {
        String msgContent = aliPayComplainProperties.getNotifySms() + ",会员渠道号:"+businessType+",支付宝投诉ID:"+complainEventId;
//        aliPayComplainProperties.getNotifyMobileList().forEach(mobile->{
//            datangSmsService.sendSms(mobile, msgContent);
//        });
        warnMobileService.smsWarn("4",businessType).forEach(mobile->{
            datangSmsService.sendSms(mobile, msgContent);
        });
    }

    public Result<?> alipayUploadImg(MultipartFile file,String appId) {
        File targetFile=MultipartFileToFile(file);
        // 先保存到临时文件夹下,操作完成之后进行删除
        String imageType=file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        FileItem fileItem=new FileItem(targetFile);
        AlipayMerchantImageUploadResponse response=alipayService.alipayUploadImg(appId,fileItem,imageType.substring(1));
        if(response.isSuccess()){
            return Result.ok(response.getImageId());
        }
        return Result.error("上传失败！");
    }
    //将MultipartFile转化为File类的方法
    public static File MultipartFileToFile(MultipartFile multipartFile) {
        File toFile = null;
        if (!multipartFile.equals("") && !(multipartFile.getSize() <= 0)) {
            try {
                InputStream ins = multipartFile.getInputStream();
                toFile = new File(multipartFile.getOriginalFilename());
                inputStreamToFile(ins, toFile);
                ins.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return toFile;
    }

    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
