package com.eleven.cms.remote;

import com.eleven.cms.config.XiaogaoCrackProperties;
import com.eleven.cms.config.YidongVrbtCrackConfig;
import com.eleven.cms.config.YidongVrbtCrackProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.BillingResult;
import com.eleven.qycl.config.EnterpriseVrbtConfig;
import com.eleven.qycl.config.EnterpriseVrbtProperties;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 移动视频彩铃破解
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class XiaogaoCrackService {

    @Autowired
    private Environment environment;
    @Autowired
    private XiaogaoCrackProperties xiaogaoCrackProperties;

    @Autowired
    private EnterpriseVrbtProperties enterpriseVrbtProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;

    public static final int FILTER_DENY_CODE = 9051;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    BillingResult getSms(String phone, String channel) {

        YidongVrbtCrackConfig yidongVrbtCrackConfig = xiaogaoCrackProperties.getYidongVrbtConfig(channel);
        final HttpUrl httpUrl = HttpUrl.parse(xiaogaoCrackProperties.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("paycode", yidongVrbtCrackConfig.getPaycode())
                .addQueryParameter("fee", yidongVrbtCrackConfig.getFee())
                .addQueryParameter("cpid", yidongVrbtCrackConfig.getCpid())
                .addQueryParameter("phone", phone)
                .build();
        log.info("{}-获取短信-渠道号:{},手机号:{},请求:{}", yidongVrbtCrackConfig.getLogTag(), channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-渠道号:{},手机号:{},响应:{}", yidongVrbtCrackConfig.getLogTag(), channel, phone, content);
            return mapper.readValue(content, BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-渠道号:{},手机号:{},异常:", yidongVrbtCrackConfig.getLogTag(), channel, phone, e);
            return BillingResult.fail();
        }
    }


    /**
     * 提交短信验证码
     *
     * @param orderId
     * @param smsCode
     * @return
     */
    public @Nonnull
    BillingResult smsCode(String orderId, String smsCode, String channel, String mobile) {
        YidongVrbtCrackConfig yidongVrbtCrackConfig = xiaogaoCrackProperties.getYidongVrbtConfig(channel);
        final HttpUrl httpUrl = HttpUrl.parse(xiaogaoCrackProperties.getSmsValidUrl())
                .newBuilder()
                .addQueryParameter("type", yidongVrbtCrackConfig.getType())
                .addQueryParameter("transId", orderId)
                .addQueryParameter("smsCode", smsCode)
                .build();
        log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},短信验证码:{},请求:{}", yidongVrbtCrackConfig.getLogTag(), channel, orderId, mobile, smsCode, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},响应:{}", yidongVrbtCrackConfig.getLogTag(), channel, orderId, mobile, content);
            return mapper.readValue(content, BillingResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-渠道号:{},交易id:{},手机号:{},异常:", yidongVrbtCrackConfig.getLogTag(), channel, orderId, mobile, e);
            return BillingResult.fail();
        }
    }

}
