package com.eleven.cms.remote;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.WangyiyunMmProperties;
import com.eleven.cms.util.MD5Util;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.GuangxiMobileResult;
import com.eleven.cms.vo.WangyiyunMmCreateOrderResult;
import com.eleven.cms.vo.WangyiyunMmSmsCodeResult;
import com.eleven.cms.vo.WangyiyunMmUnsubscribeResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lihb
 * @create: 2023-5-11 15:39:11
 */
@Slf4j
@Service
public class WangyiyunMmService {

    public static final String LOG_TAG = "网易云MM业务api";
    @Autowired
    WangyiyunMmProperties wangyiyunMmProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;
    public static final String DELIMITER_AMP = "&";
    private static final MediaType mediaType = MediaType.parse("application/json");


    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public WangyiyunMmCreateOrderResult createOrder(String phone, String ip,String appOrderId){

        try{
            Request request = getCreateOrderRequest(phone, ip,appOrderId);
            log.info("{}-创建订单-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-创建订单-手机号:{},响应体:{}", LOG_TAG, phone, result);
                return mapper.readValue(result, WangyiyunMmCreateOrderResult.class);
            }
        }catch (Exception e){
            log.info("{}-创建订单-手机号:{},异常:", LOG_TAG, phone, e);
            return WangyiyunMmCreateOrderResult.fail();
        }

    }

    public WangyiyunMmSmsCodeResult smsCode(String phone, String smsCode, String orderId) {

        Request request = getSmsCodeRequest(smsCode, orderId);
        log.info("{}-提交验证码-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, WangyiyunMmSmsCodeResult.class);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},异常:", LOG_TAG, phone, e);
            return WangyiyunMmSmsCodeResult.fail();

        }
    }

    public WangyiyunMmUnsubscribeResult unsubscribe(String phone, String userId, String orderId) {

        Request request = getUnsubscribeRequest(phone,userId, orderId);
        log.info("{}-退订-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-退订-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, WangyiyunMmUnsubscribeResult.class);
        } catch (Exception e) {
            log.info("{}-退订-手机号:{},异常:", LOG_TAG, phone, e);
            return WangyiyunMmUnsubscribeResult.fail();

        }
    }

    private Request getCreateOrderRequest(String phone,String ip,String appOrderId){
        StringBuilder builder = new StringBuilder();
        long time = new Date().getTime()/1000;
        try{
            RequestBody body = RequestBody.create(mediaType,mapper.writeValueAsString(new HashMap<>()));
            StringBuilder append = builder.append("amount=").append(wangyiyunMmProperties.getAmount())
                    .append("&").append("app_id=").append(wangyiyunMmProperties.getAppId())
                    .append("&").append("app_orderid=").append(appOrderId)
                    .append("&").append("corp_type=").append(1)
                    .append("&").append("ip=").append(ip)
                    .append("&").append("is_monthly=").append(2)
                    .append("&").append("merc_id=").append(wangyiyunMmProperties.getMercId())
                    .append("&").append("noti_url=").append(URLEncoder.encode(wangyiyunMmProperties.getNotiUrl()))
                    .append("&").append("pay_code=").append(wangyiyunMmProperties.getPayCode())
                    .append("&").append("phone=").append(phone)
                    .append("&").append("scheme=").append(1)
                    .append("&").append("time=").append(time)
                    .append("&").append("ver=").append("3.0");
            String sign = getCreateOrderSign(phone,ip,appOrderId,time);
            append.append("&").append("sign=").append(sign);
            String url = wangyiyunMmProperties.getCreateOrderUrl() + "?" + append.toString();
            Request request = new Request.Builder().url(url)
                    .post(body)
                    .build();
            return request;
        }catch (Exception e){
            return null;
        }
    }
    private Request getSmsCodeRequest(String smsCode,String orderId){
        StringBuilder builder = new StringBuilder();
        try{
            RequestBody body = RequestBody.create(mediaType,mapper.writeValueAsString(new HashMap<>()));
            StringBuilder append = builder.append("merc_id=").append(wangyiyunMmProperties.getMercId())
                    .append("&").append("app_id=").append(wangyiyunMmProperties.getAppId())
                    .append("&").append("orderid=").append(orderId)
                    .append("&").append("verify_code=").append(smsCode)
                    .append("&").append("ver=").append("3.0");
            String sign = getSmsCodeSign(smsCode,orderId);
            append.append("&").append("sign=").append(sign);
            String url = wangyiyunMmProperties.getVerConfirmUrl() + "?" + append.toString();
            Request request = new Request.Builder().url(url)
                    .post(body)
                    .build();
            return request;
        }catch (Exception e){
            return null;
        }
    }

    private Request getUnsubscribeRequest(String phone,String userId,String orderId){
        StringBuilder builder = new StringBuilder();
        try{
            RequestBody body = RequestBody.create(mediaType,mapper.writeValueAsString(new HashMap<>()));
            StringBuilder append = builder.append("&").append("merc_id=").append(wangyiyunMmProperties.getMercId())
                    .append("&").append("app_id=").append(wangyiyunMmProperties.getAppId())
                    .append("&").append("order_id=").append(orderId)
                    .append("&").append("phone=").append(phone)
                    .append("&").append("user_id=").append(userId);
            String sign = getUnsubscribeSign(phone,userId,orderId);
            append.append("&").append("sign=").append(sign);
            String url = wangyiyunMmProperties.getUnsubscribeUrl() + "?" + append.toString();
            Request request = new Request.Builder().url(url)
                    .post(body)
                    .build();
            return request;
        }catch (Exception e){
            return null;
        }
    }

    private String getCreateOrderSign(String phone, String ip, String appOrderId,long time){
        Map<String, Object> map = new HashMap<>();
        map.put("app_id",wangyiyunMmProperties.getAppId());
        map.put("app_orderid",appOrderId);
        map.put("corp_type",1);
        map.put("ip",ip);
        map.put("is_monthly",2);
        map.put("merc_id",wangyiyunMmProperties.getMercId());
        map.put("noti_url",wangyiyunMmProperties.getNotiUrl());
        map.put("pay_code",wangyiyunMmProperties.getPayCode());
        map.put("phone",phone);
        map.put("scheme",1);
        map.put("time", time);
        map.put("ver","3.0");
        map.put("amount",wangyiyunMmProperties.getAmount());
        map.put("merc_key",wangyiyunMmProperties.getMercKey());
        return MD5Util.getWangyiyunSign(map);
    }
    private String getSmsCodeSign(String smsCode,String orderId){
        Map<String, Object> map = new HashMap<>();
        map.put("merc_id",wangyiyunMmProperties.getMercId());
        map.put("app_id",wangyiyunMmProperties.getAppId());
        map.put("orderid",orderId);
        map.put("verify_code",smsCode);
        map.put("ver","3.0");
        map.put("merc_key",wangyiyunMmProperties.getMercKey());
        return MD5Util.getWangyiyunSign(map);
    }

    private String getUnsubscribeSign(String phone,String userId,String orderId){
        Map<String, Object> map = new HashMap<>();
        map.put("merc_id",wangyiyunMmProperties.getMercId());
        map.put("app_id",wangyiyunMmProperties.getAppId());
        map.put("order_id",orderId);
        map.put("phone",phone);
        map.put("user_id",userId);
        map.put("merc_key",wangyiyunMmProperties.getMercKey());
        return MD5Util.getWangyiyunSign(map);
    }

}
