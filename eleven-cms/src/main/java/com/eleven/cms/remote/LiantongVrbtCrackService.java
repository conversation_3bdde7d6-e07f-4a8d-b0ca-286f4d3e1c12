package com.eleven.cms.remote;

import com.eleven.cms.config.LiantongVrbtCrackProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.LiantongCrackResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 联通视频彩铃pojie
 *
 * @author: cai lei
 * @create: 2022-04-07 09:51
 */
@Slf4j
@Service
public class LiantongVrbtCrackService {

    @Autowired
    private LiantongVrbtCrackProperties liantongVrbtCrackProperties;
    @Autowired
    private Environment environment;

    public static final String LOG_TAG = "联通视频彩铃api";

    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 请求短信验证码
     *
     * @param phone
     * @param orderNo
     * @param payUrl
     * @return
     */
    public @Nonnull
    LiantongCrackResult getSms(String phone, String orderNo, String payUrl) {

        final HttpUrl httpUrl = HttpUrl.parse(liantongVrbtCrackProperties.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("a", liantongVrbtCrackProperties.getA())
                .addQueryParameter("vaccode", liantongVrbtCrackProperties.getVacCode())
                .addQueryParameter("orderid", orderNo)
                .addQueryParameter("tel", phone)
                .addQueryParameter("payurl", payUrl)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, LiantongCrackResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return LiantongCrackResult.fail();
        }
    }

    /**
     * 提交短信验证码
     *
     * @param sId
     * @param smsCode
     * @return
     */
    public @Nonnull
    LiantongCrackResult smsCode(String sId, String smsCode) {
        final HttpUrl httpUrl = HttpUrl.parse(liantongVrbtCrackProperties.getSmsValidUrl())
                .newBuilder()
                .addQueryParameter("sid", sId)
                .addQueryParameter("vcode", smsCode)
                .build();
        log.info("{}-提交短信-交易id:{},请求:{}", LOG_TAG, sId, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-交易id:{},响应:{}", LOG_TAG, sId, content);
            return mapper.readValue(content, LiantongCrackResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-交易id:{},异常:", LOG_TAG, sId, e);
            return LiantongCrackResult.fail();
        }
    }


}
