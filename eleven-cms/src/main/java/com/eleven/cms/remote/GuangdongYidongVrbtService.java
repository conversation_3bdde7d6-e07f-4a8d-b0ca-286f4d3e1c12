package com.eleven.cms.remote;

import com.eleven.cms.config.GuangdongYidongVrbtProperties;
import com.eleven.cms.config.XinjiangProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.GuangdongYidongVrbtDataResult;
import com.eleven.cms.vo.GuangdongYidongVrbtResult;
import com.eleven.cms.vo.XinjiangYidongResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;

/**
 * @author: lihb
 * @create: 2022-10-28 10:30:13
 */
@Slf4j
@Service
public class GuangdongYidongVrbtService {

    public static final String LOG_TAG = "广东超炫XR视频彩铃api";


    @Autowired
    private Environment environment;
    @Autowired
    private GuangdongYidongVrbtProperties guangdongYidongVrbtProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;


    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;
    }

    /**
     * 获取短信验证码
     * @param phone 手机号
     * @return
     */
    public GuangdongYidongVrbtDataResult getSms(String phone){
        final HttpUrl httpUrl = HttpUrl.parse(guangdongYidongVrbtProperties.getBaseUrl()
                + guangdongYidongVrbtProperties.getSmsUrl())
                .newBuilder()
                .addQueryParameter("mobile", phone)
                .addQueryParameter("productCode", guangdongYidongVrbtProperties.getProductCode())
                .build();
        log.info("{}-获取验证码-渠道号:{},手机号:{},请求:{}", LOG_TAG, guangdongYidongVrbtProperties.getProductCode(), phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码-渠道号:{},手机号:{},响应:{}", LOG_TAG, guangdongYidongVrbtProperties.getProductCode(), phone, content);
            String data = mapper.readValue(content, GuangdongYidongVrbtResult.class).getData();
            return mapper.readValue(data, GuangdongYidongVrbtDataResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取验证码-渠道号:{},手机号:{},异常:", LOG_TAG, guangdongYidongVrbtProperties.getProductCode(), phone, e);
            return GuangdongYidongVrbtDataResult.fail();
        }
    }

    public GuangdongYidongVrbtDataResult validateSmsCode(String phone,String code){
        final HttpUrl httpUrl = HttpUrl.parse(guangdongYidongVrbtProperties.getBaseUrl()
                + guangdongYidongVrbtProperties.getSendSmsUrl())
                .newBuilder()
                .addQueryParameter("mobile", phone)
                .addQueryParameter("productCode", guangdongYidongVrbtProperties.getProductCode())
                .addQueryParameter("code", code)
                .addQueryParameter("operateId", guangdongYidongVrbtProperties.getOperateId())
                .build();
        log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, guangdongYidongVrbtProperties.getProductCode(), phone, code,httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},响应:{}", LOG_TAG, guangdongYidongVrbtProperties.getProductCode(), phone,code, content);
            String data = mapper.readValue(content, GuangdongYidongVrbtResult.class).getData();
            return mapper.readValue(data, GuangdongYidongVrbtDataResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},异常:", LOG_TAG, guangdongYidongVrbtProperties.getProductCode(), phone,code, e);
            return GuangdongYidongVrbtDataResult.fail();
        }
    }

}
