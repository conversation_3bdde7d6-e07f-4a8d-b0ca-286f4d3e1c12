package com.eleven.cms.remote;

import com.eleven.cms.config.XinjiangProperties;
import com.eleven.cms.config.YidongVrbtCrackConfig;
import com.eleven.cms.config.YidongVrbtCrackProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.XinjiangYidongResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2022-08-12 10:58
 */
@Slf4j
@Service
public class XinjiangYidongService {

    public static final String LOG_TAG = "新疆移动api";


    @Autowired
    private Environment environment;
    @Autowired
    private XinjiangProperties xinjiangProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;
    }


    public @Nonnull XinjiangYidongResult getSms(String phone, String channel){
        String bizCode = xinjiangProperties.getBizCodeByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(xinjiangProperties.getOrderUrl())
                .newBuilder()
                .addQueryParameter("mobile", phone)
                .addQueryParameter("code", bizCode)
                .addQueryParameter("cpparam", "XINJ_UNHT_checkVerifyCodeNew")
                .addQueryParameter("cp", xinjiangProperties.getCp())
                .build();
        log.info("{}-获取验证码-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, XinjiangYidongResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取验证码-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return XinjiangYidongResult.FAIL_RESULT;
        }
    }

    public @Nonnull boolean validateSmsCode(String phone, String channel,String code){
        String bizCode = xinjiangProperties.getBizCodeByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(xinjiangProperties.getOrderUrl())
                .newBuilder()
                .addQueryParameter("mobile", phone)
                .addQueryParameter("code", bizCode)
                .addQueryParameter("cpparam", "XINJ_UNHT_checkVerifyCode")
                .addQueryParameter("smscode", code)
                .addQueryParameter("cp", xinjiangProperties.getCp())
                .build();
        log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, channel, phone,code, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            JsonNode jsonNode = mapper.readTree(content);
            String respCode = jsonNode.at("/respCode").asText();
            String resultCode = jsonNode.at("/result/OUTDATA/DATAS/RESULT_CODE").asText();
            return "0".equals(respCode) && "0".equals(resultCode);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return false;
        }
    }


    /**
     * 请求短信验证码
     *
     * (一事一案,权益类)
     * 注：号卡类标志暂时不提供
     *
     * @param phone
     * @return
     */
    public @Nonnull
    XinjiangYidongResult order(String phone, String channel) {

        String bizCode = xinjiangProperties.getBizCodeByChannel(channel);
        final HttpUrl httpUrl = HttpUrl.parse(xinjiangProperties.getOrderUrl())
                .newBuilder()
                .addQueryParameter("mobile", phone)
                .addQueryParameter("code", bizCode)
                .addQueryParameter("cpparam", getCpparamByCode(bizCode))
                .addQueryParameter("cp", xinjiangProperties.getCp())
                .build();
        log.info("{}-业务订购-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-业务订购-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, XinjiangYidongResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-业务订购-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return XinjiangYidongResult.FAIL_RESULT;
        }
    }

    /**
     * cpparam	业务受理标志：
     * XINJ_UNHT_sendIdentifyCode：短信验证码发送
     * XINJ_UNHT_checkVerifyCode：短信验证码效验
     * XINJ_UNHT_offerOrder：资费编码202打头(视频彩铃类套餐)
     * XINJ_UNHQ_unionCaseAcceptAcceptOrder：资费编码999打头
     *
     * @param code
     * @return
     */
    public static String getCpparamByCode(String code) {
        if (code.startsWith("202")) {
            return "XINJ_UNHT_offerOrder";
        } else if (code.startsWith("999")) {
            return "XINJ_UNHQ_unionCaseAcceptAcceptOrder";
        } else {
            return "";
        }
    }

}
