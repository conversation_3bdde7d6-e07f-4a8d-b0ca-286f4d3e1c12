package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.LiantongVrbtConfig;
import com.eleven.cms.config.LiantongVrbtProperties;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.*;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/12/28 15:46
 * Desc: 联通视频彩铃相关api
 */
@Slf4j
@Service
public class LiantongVrbtService {
    @Autowired
    private LiantongVrbtProperties liantongVrbtProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    public static final MediaType FORM_DATA
            = MediaType.parse(org.springframework.http.MediaType.MULTIPART_FORM_DATA_VALUE);

    //   @PostConstruct
    public void init() {

        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }

        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }

    /**
     * 查询用户订购关系-查询已订购产品(免用户授权)
     * NOTE: 此接口使用GET方式请求,其余接口大都是POST
     *
     * @param callNumber 手机号
     *                   {"returnCode":"000000","description":"","subedProducts":[{"productId":"4900711100","productType":"0","cantUnSubscribeReason":"渠道未授权操作该产品","status":"1","subTime":"20201026151436","unSubscribeable":false}]}
     *                   {"returnCode":"000000","description":"","subedProducts":[{"productId":"4900711100","productType":"0","cantUnSubscribeReason":"渠道未授权操作该产品","status":"1","subTime":"20201026151436","unSubscribeable":false},{"productId":"4900718300","productType":"0","cantUnSubscribeReason":"渠道未授权操作该产品","status":"4","subTime":"20201229105129","unSubTime":"20201229113600","unSubscribeable":false},{"productId":"4900720600","productName":"酷炫彩铃6元包月","productType":"0","status":"4","subTime":"20201229104014","unSubTime":"20201229113524","unSubscribeable":true}]}
     */
    public LiantongSubedProductsResp qrySubedProductsNoToken(String callNumber, String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtProperties.getQrySubedProductsNoTokenUrl())
                .newBuilder()
                .addQueryParameter("callNumber", callNumber)
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        HttpUrl newHttpUrl = LiantongAuthUtils.generateSign(request, liantongVrbtConfig);
        Request newRequest = request.newBuilder().url(newHttpUrl).build();
        try (Response response = client.newCall(newRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询用户订购关系-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongSubedProductsResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-查询用户订购关系-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return LiantongSubedProductsResp.error();
        }
    }


    /**
     * 查询是否在订购状态
     * status 订购状态 1: 正式订购（正常） 2: 暂停（隐藏） 4: 订购关系停止（等待删除）
     *
     * @param callNumber 手机号
     */
    public boolean isSubedMon(String callNumber, String company) {
        LiantongSubedProductsResp resp = this.qrySubedProductsNoToken(callNumber, company);
        if (!resp.isOK()) {
            return false;
        }
        final String produtcId = getLiantongVrbtConfigByCompany(company).getProdutcId();
        final List<LiantongSubedProductsResp.SubedProducts> subedProducts = resp.getSubedProducts();
        if (subedProducts == null) {
            return false;
        }
        return subedProducts
                .stream()
                .anyMatch(item -> produtcId.equals(item.getProductId()) && "1".equals(item.getStatus()));
    }

    /**
     * 发送登录验证码 GET
     *
     * @param callNumber 手机号
     *                   {"returnCode":"000000","description":"请求成功"}
     */
    public boolean sendLoginCode(String callNumber,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtProperties.getSendLoginCodeUrl())
                .newBuilder()
                .addQueryParameter("callNumber", callNumber)
                //短信提示 非必填参数
                //.addQueryParameter("content", "")
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        HttpUrl newHttpUrl = LiantongAuthUtils.generateSign(request, liantongVrbtConfig);
        Request newRequest = request.newBuilder().url(newHttpUrl).build();
        try (Response response = client.newCall(newRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-发送登录验证码-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class).isOK();
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-发送登录验证码-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return false;
        }
    }

    /**
     * 校验登录验证码 GET
     *
     * @param callNumber 手机号
     *                   {"returnCode":"000000","description":""}
     *                   {"returnCode":"770002","description":"[OA]验证码错误或已过时"}
     */
    public boolean codeLogin(String callNumber, String verifyCode,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtProperties.getCodeLoginUrl())
                .newBuilder()
                .addQueryParameter("callNumber", callNumber)
                //短信提示 非必填参数
                .addQueryParameter("verifyCode", verifyCode)
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        HttpUrl newHttpUrl = LiantongAuthUtils.generateSign(request, liantongVrbtConfig);
        Request newRequest = request.newBuilder().url(newHttpUrl).build();
        try (Response response = client.newCall(newRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-校验登录验证码-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class).isOK();
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-校验登录验证码-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return false;
        }
    }

    /**
     * 视频彩铃包月业务订购接口：彩铃收费产品订购（包月）POST
     * 注意,如果开通包月同时订一首已经订购过的铃音回调是这样的{"noticeType":"order","notice":{"returnCode":"0000","description":"success","orderId":"1210104162501315327"}}
     * 结果开通包月不成功,无法判定包月结果,所以只好单独包月,收到回调后再订彩铃
     *
     * @param phoneNumber 手机号
     *                    {"returnCode":"0000","description":"success","orderId":"1201229122501834981","url":"https://open.10155.com/confirm/showPage?orderId=1201229122501834981"}
     */
    public LiantongResp onePointProductMon(String phoneNumber, String ringId, String redirectUrl,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phoneNumber", phoneNumber);
        dataNode.put("productId", liantongVrbtConfig.getProdutcId());
        dataNode.put("orderType", "1");
        dataNode.put("orderMethod", "01");
        //Appkey=新手机平台渠道ID=channelID=渠道ID
        dataNode.put("channelId", liantongVrbtConfig.getAppKey());
        dataNode.put("redirectUrl", StringUtils.isNotEmpty(redirectUrl) ? redirectUrl : liantongVrbtConfig.getOrderMonRedirectUrl());
        if (StringUtils.isNotEmpty(ringId)) {
            dataNode.put("ringId", ringId);
        }
        String raw = dataNode.toString();
        log.info("{}-包月业务订购-手机号:{},raw: {}", liantongVrbtConfig.getLogTag(), phoneNumber, raw);
        try {
            String content = this.postRaw(liantongVrbtProperties.getOnePointProductMonUrl(), raw, liantongVrbtConfig);
            log.info("{}-包月业务订购-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), phoneNumber, content);
            return mapper.readValue(content, LiantongResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-包月业务订购-手机号:{},异常:", liantongVrbtConfig.getLogTag(), phoneNumber, e);
            return LiantongResp.error();
        }
    }


    /**
     * 视频彩铃订购包月业务短信下发接口：发送短信（联通）
     *
     * @param callNumber 手机号
     *                   {"returnCode":"000000","description":""}
     */
    public boolean sendMsg(String callNumber,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        //注意此处变量名文档叫cellNumber
        dataNode.put("cellNumber", callNumber);
        dataNode.put("templateId", liantongVrbtConfig.getOrderMonSmsTemplateId());
        Map<String, String> variableMap = liantongVrbtConfig.getSmsTemplateVariableMap();
        if(variableMap.containsKey("effectiveTime")){
            variableMap.put("effectiveTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        }
        dataNode.putPOJO("variableMap", variableMap);
/*        if(StringUtils.isNotEmpty(ringId)){
            dataNode.put("ringId", ringId);
        }*/
        String raw = dataNode.toString();
        log.info("{}-包月业务订购成功后短信下发-手机号:{},raw: {}", liantongVrbtConfig.getLogTag(), callNumber, raw);
        try {
            String content = this.postRaw(liantongVrbtProperties.getSendMsgUrl(), raw,liantongVrbtConfig);
            log.info("{}-包月业务订购成功后短信下发-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class).isOK();
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-包月业务订购成功后短信下发-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return false;
        }
    }


    /**
     * 视音频设置接口：彩铃一键设置（包月用户）
     *
     * @param phoneNumber 手机号
     * @param ringId      铃音ID/铃音组ID
     *                    {"returnCode":"0000","description":"订购并设置成功"}
     */
    public @Nonnull
    LiantongResp settingRingOnePointMon(String phoneNumber, String ringId, String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        String settingRingOnePointMonUrl="";
        if(BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF.equals(company)){
            settingRingOnePointMonUrl=liantongVrbtProperties.getSettingRingOnePointMonUrlDiy();
        }else{
            settingRingOnePointMonUrl=liantongVrbtProperties.getSettingRingOnePointMonUrl();
        }
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phoneNumber", phoneNumber);
        dataNode.put("ringId", ringId);
        dataNode.put("isSms", Boolean.TRUE.toString());
        String raw = dataNode.toString();
        log.info("{}-彩铃一键设置（包月用户）-手机号:{},raw: {}", liantongVrbtConfig.getLogTag(), phoneNumber, raw);
        try {
            String content = this.postRaw(settingRingOnePointMonUrl, raw,liantongVrbtConfig);
            log.info("{}-彩铃一键设置（包月用户）-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), phoneNumber, content);
            return mapper.readValue(content, LiantongResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-彩铃一键设置（包月用户）-手机号:{},异常:", liantongVrbtConfig.getLogTag(), phoneNumber, e);
            return LiantongResp.error();
        }
    }

    /**
     * 查询用户铃音库情况：获取用户订购铃音列表
     *
     * @param phoneNumber 手机号
     *                    <p>
     *                    {"returnCode":"0000","description":"查询铃音订购成功","userDepotList":[{"ringId":"80768000202011189336520","ringName":"如果我学了电焊是不是就能让你眼前一亮","ringType":"1","type":"1","settingFlag":"1","orderTime":"20201230103702","ringIndex":1556618},{"ringId":"80768000202011189337270","ringName":"哈哈哈哈中秋人太多了！","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201229172832","ringIndex":1556583},{"ringId":"80768000202011189337450","ringName":"舞者除了舞姿还要一双摄人心魂的眼睛","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201229170451","ringIndex":1556590},{"ringId":"80768000202011189337470","ringName":"不要偷偷喜欢我","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201229163313","ringIndex":1556604},{"ringId":"80768000202011189337500","ringName":"有趣的友情胜过敷衍的爱情","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201229145742","ringIndex":1556637},{"ringId":"80768000202011189337510","ringName":"听说跳舞减肥都是骗人的","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201229135817","ringIndex":1556689},{"ringId":"80768000202011189337520","ringName":"全民吃鸡","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201229120621","ringIndex":1556610},{"ringId":"87272000202006102526170","ringName":"风吹柳花满店香","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201023110448","ringIndex":983063},{"ringId":"91789000202002130685720","ringName":"雾海风景SPRING","ringType":"1","type":"1","settingFlag":"0","orderTime":"20201016164716","ringIndex":707397}]}
     */
    public LiantongUserRingDepotResp userVideoRingDepotList(String phoneNumber,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phoneNumber", phoneNumber);
        //0：音频  1：视频
        dataNode.put("isVideo", "1");
        String raw = dataNode.toString();
        log.info("{}-获取用户订购视频铃音列表-手机号:{},raw: {}", liantongVrbtConfig.getLogTag(), phoneNumber, raw);
        try {
            String content = this.postRaw(liantongVrbtProperties.getOrderRingListUrl(), raw,liantongVrbtConfig);
            log.info("{}-获取用户订购视频铃音列表-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), phoneNumber, content);
            return mapper.readValue(content, LiantongUserRingDepotResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-获取用户订购视频铃音列表-手机号:{},异常:", liantongVrbtConfig.getLogTag(), phoneNumber, e);
            return LiantongUserRingDepotResp.error();
        }
    }

    /**
     * 发送短信验证码（退订产品需用户验证时用到）GET
     *
     * @param callNumber 手机号
     *                   <p>
     *                   {"returnCode":"000000","description":"请求成功"}
     */
    public LiantongResp sendVerifyCode(String callNumber,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtProperties.getSendVerifyCodeUrl())
                .newBuilder()
                .addQueryParameter("callNumber", callNumber)
                //verifyType可选值: 10001 开通炫铃 10002 注销炫铃 10010 订购炫铃 10021 订购产品 10022 退订产品 10023 购买产品(单次计费)10031 查询下载链接
                .addQueryParameter("verifyType", "10022")
                //verifyParam可选值: verifyType=10010时必须填炫铃Id verifyType=10021或10022时填49开头的产品Id verifyType=10031时填歌曲Id
                .addQueryParameter("verifyParam", liantongVrbtConfig.getProdutcId())
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        HttpUrl newHttpUrl = LiantongAuthUtils.generateSign(request, liantongVrbtConfig);
        Request newRequest = request.newBuilder().url(newHttpUrl).build();
        try (Response response = client.newCall(newRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-发送短信验证码（退订产品）-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-发送短信验证码（退订产品）-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return LiantongResp.error();
        }
    }

    /**
     * 退订产品接口：退订产品(需短信验证码)
     *
     * @param callNumber 手机号
     *                   <p>
     *                   {"returnCode":"000000"}
     */
    public LiantongResp unSubProductWithVCode(String callNumber, String verifyCode,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtProperties.getUnSubProductWithVCodeUrl())
                .newBuilder()
                .addQueryParameter("callNumber", callNumber)
                .addQueryParameter("verifyCode", verifyCode)
                .addQueryParameter("productId", liantongVrbtConfig.getProdutcId())
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        HttpUrl newHttpUrl = LiantongAuthUtils.generateSign(request, liantongVrbtConfig);
        Request newRequest = request.newBuilder().url(newHttpUrl).build();
        try (Response response = client.newCall(newRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-退订产品(需短信验证码)-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-退订产品(需短信验证码)-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return LiantongResp.error();
        }
    }

    /**
     * 退订产品接口：退订产品(免用户授权) GET
     *
     * @param callNumber 手机号
     *                   <p>
     *                   {"returnCode":"000000"}
     */
    public LiantongResp unSubProductNoToken(String callNumber,String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtProperties.getUnSubProductNoTokenUrl())
                .newBuilder()
                .addQueryParameter("callNumber", callNumber)
                .addQueryParameter("productId", liantongVrbtConfig.getProdutcId())
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        HttpUrl newHttpUrl = LiantongAuthUtils.generateSign(request, liantongVrbtConfig);
        Request newRequest = request.newBuilder().url(newHttpUrl).build();
        try (Response response = client.newCall(newRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-退订产品(免用户授权)-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-退订产品(免用户授权)-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return LiantongResp.error();
        }
    }

    /**
     * 查询是否可以开通视频彩铃
     *
     * @param callNumber 手机号 {"returnCode":"000000","description":"校验通过"}
     */
    public boolean isCanOpenVideoRing(String callNumber,String company) {
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("callNumber", callNumber);
        try {
            String content = this.postRaw(liantongVrbtProperties.getIsCanOpenVideoRingUrl(), dataNode.toString(), liantongVrbtConfig);
            log.info("{}-查询是否可以开通视频彩铃-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class).isOK();
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-查询是否可以开通视频彩铃-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return false;
        }
    }


    /**
     * 联通视频彩铃兑换
     * @param callNumber 手机号
     * @param company
     * @return {"returnCode": "000000",    "description": "success"}
     */
    public LiantongResp exchangeRing(String callNumber,String company) {
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("caller", callNumber);
        //Appkey与channelId相同
        dataNode.put("channelId", liantongVrbtConfig.getAppKey());
        dataNode.put("sign", getSign(dataNode,liantongVrbtConfig.getSecret()));
        try {
            String content = this.postRaw(liantongVrbtProperties.getExchangeRingUrl(), dataNode.toString(), liantongVrbtConfig);
            log.info("{}-联通视频彩铃兑换-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-联通视频彩铃兑换-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return null;
        }
    }

    /**
     * 联通企业视频彩铃内容ID生成
     * @param callNumber
     * @param company
     * @return
     */
    public LiantongCompanyResp nextId(String callNumber,String company) {
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        //业务类型 1:MV 下载，3:MV点播，5:整曲点播，6:整曲下载，7:振铃,8:炫铃，9:多媒体炫铃，10:视频炫铃
        dataNode.put("subServiceType", 10);
        //Appkey与channelId相同
        dataNode.put("channelId", liantongVrbtConfig.getAppKey());
        dataNode.put("cpid", liantongVrbtConfig.getCpId());
        try {
            String content = this.postRaw(liantongVrbtProperties.getNextIdUrl(), dataNode.toString(), liantongVrbtConfig);
            log.info("{}-联通企业视频彩铃内容ID生成-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongCompanyResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-联通企业视频彩铃内容ID生成-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return null;
        }
    }

    /**
     * 联通企业视频彩铃彩铃上传
     *
     * @param callNumber 手机号 {"returnCode":"000000","description":"校验通过"}
     */
    public LiantongCompanyResp ringUpload(String contentId, String callNumber, InputStream ringMp4FileIs, InputStream coverJpgFileIs, String company) {

        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        //ObjectNode dataNode = mapper.createObjectNode();
        //dataNode.putPOJO("files", files);
        //Appkey与channelId相同
        ObjectNode dataNode = mapper.createObjectNode()
        //操作类型	1：新增，2：删除，3：修改， 默认 1；（2，3 未实现）
        .put("operType", "1")
        .put("channelid", liantongVrbtConfig.getAppKey())
        .put("contentid", contentId)
        .put("contname", callNumber)
        .put("publishtype", "MV")
        .put("label", "组合")
        .put("cpid", liantongVrbtConfig.getCpId())
        .put("nickName", callNumber)
        .put("conttype", 8)
        .put("accountid", callNumber)
        .put("subServiceType", 10);
        try {
            RequestBody ringMp4FileBody = OkHttpClientUtils.createInputStreamRequestBody(ringMp4FileIs);
            RequestBody coverJpgFileBody = OkHttpClientUtils.createInputStreamRequestBody(coverJpgFileIs);
            MultipartBody multipartBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    //.addFormDataPart("ringFile", DigestUtils.md5Hex(ringName) +".mp4", ringFileBody)
                    .addFormDataPart("files", contentId+"-ring.mp4" , ringMp4FileBody)
                    .addFormDataPart("files", contentId+"-cover.jpg", coverJpgFileBody)
                    .addFormDataPart("data", dataNode.toString()) //1-视频  2-音频
                    .build();
            Request request = new Request.Builder().url(liantongVrbtProperties.getUploadUrl()).post(multipartBody).build();
            HttpUrl httpUrl = LiantongAuthUtils.generateSign(request,liantongVrbtConfig);
            Request newRequest = request.newBuilder().url(httpUrl).build();
            Response response = client.newCall(newRequest).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-联通企业视频彩铃彩铃上传-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongCompanyResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-联通企业视频彩铃彩铃上传-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return null;
        }
    }

    /**
     * 联通企业视频彩铃铃音删除
     * @param callNumber
     * @param company
     * @return
     */
    public LiantongResp delRing(String callNumber,String company,String ringId) {
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phoneNumber", callNumber);
        //Appkey与channelId相同
        dataNode.put("ringId", ringId);
        dataNode.put("channelId", liantongVrbtConfig.getAppKey());
        try {
            String content = this.postRaw(liantongVrbtProperties.getDelOrderUrl(), dataNode.toString(), liantongVrbtConfig);
            log.info("{}-联通企业视频彩铃铃音删除-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-联通企业视频彩铃铃音删除-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return null;
        }
    }

    /**
     * 联通企业视频彩铃视频彩铃能力有效期查询
     * @param callNumber
     * @param company
     * @return
     */
    public LiantongLicenseResp licenseQuery(String callNumber,String company) {
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("caller", callNumber);
        //Appkey与channelId相同
        dataNode.put("channelId", liantongVrbtConfig.getAppKey());

        dataNode.put("sign", getSign(dataNode,liantongVrbtConfig.getSecret()));
        try {
            String content = this.postRaw(liantongVrbtProperties.getLicenseQueryUrl(), dataNode.toString(), liantongVrbtConfig);
            log.info("{}-联通企业视频彩铃视频彩铃能力有效期查询-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LiantongLicenseResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-联通企业视频彩铃视频彩铃能力有效期查询-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return null;
        }
    }

    /**
     * 视频彩铃用户铃音库查询
     * @param callNumber
     * @param company
     * @return
     */
    public LianTongRingLibraryResp listRingLibrary(String callNumber,String ringId,String company) {
        LiantongVrbtConfig liantongVrbtConfig = getLiantongVrbtConfigByCompany(company);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("callNumber", callNumber);
        dataNode.put("ringId", ringId);
        try {
            String content = this.postRaw(liantongVrbtProperties.getListRingLibraryUrl(), dataNode.toString(), liantongVrbtConfig);
            log.info("{}-视频彩铃用户铃音库查询-手机号:{},响应:{}", liantongVrbtConfig.getLogTag(), callNumber, content);
            return mapper.readValue(content, LianTongRingLibraryResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("{}-视频彩铃用户铃音库查询-手机号:{},异常:", liantongVrbtConfig.getLogTag(), callNumber, e);
            return null;
        }
    }

    public String postRaw(String url, String raw,LiantongVrbtConfig liantongVrbtConfig) throws IOException {
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        HttpUrl httpUrl = LiantongAuthUtils.generateSign(request,liantongVrbtConfig);
        Request newRequest = request.newBuilder().url(httpUrl).build();
        try (Response response = client.newCall(newRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return response.body().string();
        }
    }

    public LiantongVrbtConfig getLiantongVrbtConfigByCompany(String company) {
        LiantongVrbtConfig liantongVrbtConfig = liantongVrbtProperties.getVrbtConfig(company);
        if (liantongVrbtConfig == null) {
            log.error("渠道号:{}未找到联通视频彩铃相关配置", company);
            throw new JeecgBootException("无效的联通渠道号");
        }
        return liantongVrbtConfig;
    }
    public static String getSign(ObjectNode dataNode,String keys) {
        Iterator<Map.Entry<String, JsonNode>> jsonNode = dataNode.fields();
        StringBuffer valueStr = new StringBuffer();
        while (jsonNode.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNode.next();
            String value =  entry.getValue().asText();
            String key =  entry.getKey();
            valueStr.append(key+"="+value+"&");
        }
        String md5 =valueStr.toString()+"key="+keys;
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return sign;
    }
//    public void testOrderNotify() {
//        ObjectNode dataNode = mapper.createObjectNode();
//        dataNode.put("test", "foo");
//        String raw = dataNode.toString();
//        log.info("{}-回调测试,raw: {}", LOG_TAG, raw);
//        try {
//            RequestBody body = RequestBody.create(JSON, raw);
//            //String nofityUrl = "https://crbt.cdyrjygs.com/cms-vrbt/api/liantongVrbt/orderNotify";
//            String nofityUrl = "http://crbt.cdyrjygs.com:9527/cms-vrbt/api/liantongVrbt/orderNotify";
//            Request request = new Request.Builder().url(nofityUrl)
//                    .addHeader("Authorization", "3000008270:test")
//                    .addHeader("timestamp", "1609207718338")
//                    .post(body).build();
//            try (Response response = client.newCall(request)
//                    .execute()) {
//                if (!response.isSuccessful()) {
//                    throw new IOException("Unexpected code " + response);
//                }
//                String content = response.body().string();
//                log.info("{}-回调测试,响应:{}", LOG_TAG, content);
//            }
//        } catch (IOException e) {
//            //e.printStackTrace();
//            log.info("{}-回调测试,异常:", LOG_TAG, e);
//        }
//    }


}
