package com.eleven.cms.remote;

import com.eleven.cms.config.KuaimaGansuProperties;
import com.eleven.cms.config.KuaimaProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.KuaimaGansuResult;
import com.eleven.cms.vo.KuaimaResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class KuaimaGansuService {

    public static final String LOG_TAG = "快马甘肃业务api";

    @Autowired
    KuaimaGansuProperties kuaimaGansuProperties;

    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;


    private static final MediaType mediaType = MediaType.parse("application/json");


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public KuaimaGansuResult getSms(String phone) throws JsonProcessingException {
        String url = kuaimaGansuProperties.getGetSmsUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("phone", phone);
        dataMap.put("actCode", kuaimaGansuProperties.getActCode());
        dataMap.put("userCode", kuaimaGansuProperties.getUserCode());
        dataMap.put("sourceUrl", kuaimaGansuProperties.getSourceUrl());
        dataMap.put("sign", generateSign(dataMap, kuaimaGansuProperties.getApiKey()));
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-随机码下发-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, KuaimaGansuResult.class);
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return KuaimaGansuResult.FAIL_RESULT;

        }
    }

    public KuaimaGansuResult smsCode(String phone, String smsCode) throws JsonProcessingException {
        String url = kuaimaGansuProperties.getSmsCodeUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("phone", phone);
        dataMap.put("actCode", kuaimaGansuProperties.getActCode());
        dataMap.put("userCode", kuaimaGansuProperties.getUserCode());
        dataMap.put("sourceUrl", kuaimaGansuProperties.getSourceUrl());
        dataMap.put("smsVode", smsCode);
        dataMap.put("sign", generateSign(dataMap, kuaimaGansuProperties.getApiKey()));
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-提交验证码-手机号:{},短信验证码:{},请求:{}", LOG_TAG, phone,smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, KuaimaGansuResult.class);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},异常:", LOG_TAG, phone, e);
            return KuaimaGansuResult.FAIL_RESULT;

        }
    }

    public String generateSign(Map<String, Object> dataMap, String appKey) throws JsonProcessingException {
        return DigestUtils.md5DigestAsHex((mapper.writeValueAsString(dataMap) + appKey).getBytes(StandardCharsets.UTF_8));
    }
}
