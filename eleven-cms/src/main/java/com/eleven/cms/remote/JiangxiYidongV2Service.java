package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.JiangxiCompanyConfig;
import com.eleven.cms.config.JiangxiProductConfig;
import com.eleven.cms.config.JiangxiYidongProperties;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * @author: cai lei
 * @create: 2022-08-12 10:58
 */
@Slf4j
@Service
public class JiangxiYidongV2Service {

    public static final String LOG_TAG = "江西移动V2api";
    public static final String ENCRYPTION_APP_CODE = "aweme";
    public static final String ENCRYPTION_TOKEN = "WZjy1d/0UNAwxsOmScXeIdJO4fnQQsXJy+2dkXDA6Idz+qXsUJPNhip/B0g6av1UZr3nVhKp83fIsvgl38bWRZ6T56oXTITABffAAI0RMpNxYbdza0Vt7iSDGMe2vZT8krqy3WwuiNfu4/adQR4Yv3pntBOxGfqpI3kYZgPiwMByx2CauknRX0rq6yV4WCbNP9KJQjWvGoupqxgP0lelErzPVP7QkgnIFNUIsn+8wnRBPcHva6eU/j5bkNLOIGU3ccz8JGlat2zfJt3oGuhOaQscR0QTPdDXH2vsmhmZSMET+T/LGmUBZabkDF/ysR9Lr9MdkMYUZDkvn3FKyNLD4Q==";
    public static final String UA = "Mozilla/5.0 (Linux; Android 13; RMO-AN00 Build/HONORRMO-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.88 Mobile Safari/537.36 open_news open_news_u_s/5615/com.avalancblund.defini";

    @Autowired
    private Environment environment;
    @Autowired
    private JiangxiYidongProperties jiangxiYidongProperties;

    private OkHttpClient client;
    private OkHttpClient gaojieClient;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.gaojieClient = OkHttpClientUtils.getNewInstance().newBuilder()
                .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.gaojieProxyHost, OkHttpClientUtils.gaojieProxyPort)))
                .readTimeout(30L, TimeUnit.SECONDS)
                .build();
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().readTimeout(30L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
//            this.client = this.client.newBuilder()
//                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
//                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
            this.gaojieClient = this.gaojieClient.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private OkHttpClient getClient(String company) {
        if ("zyhl".equals(company)) {
            return gaojieClient;
        }
        return client;
    }

    /**
     * {"ret":0,"msg":"操作成功","data":{"callTime":"340ms","responseCode":"","resultMsg":{"result":"","respDesc":"短信验证码发送成功！","respCode":"0"}}}
     * 获取验证码
     *
     * @param phone
     * @param channel
     * @param company
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.CMS_JXYD_GETSMS_CACHE, key = "#root.methodName+ ':' + #p0+ ':' + #p2 +':' + #p1", unless = "#result==null")
    public @Nonnull
    JiangxiYidongResult getSms(String phone, String channel, String company, String busiSerialNumber) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        JiangxiProductConfig jiangxiProductConfig = jiangxiYidongProperties.getJiangxiProductConfig(channel);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("billId", phone);
        dataMap.put("planId", jiangxiProductConfig.getPlanId());
        dataMap.put("goodsId", jiangxiProductConfig.getGoodsId());
        dataMap.put("busiType", "1");
        dataMap.put("businessSerialNumber", busiSerialNumber);
        //短信校验开关  0 全校验 1 互斥校验  2 余额校验
        dataMap.put("checkType", "0");
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();

        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getGetSmsCodeUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-获取验证码-渠道号:{},手机号:{}", jiangxiCompanyConfig.getLogTag(), channel, phone);
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码-渠道号:{},手机号:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, content);
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String respCode = jsonNode.at("/data/resultMsg/respCode").asText();
//            return "0".equals(ret) && "0".equals(respCode);
            JiangxiYidongResult jiangxiYidongResult = mapper.readValue(content, JiangxiYidongResult.class);
            jiangxiYidongResult.setBusiSerialNumber(busiSerialNumber);
            return jiangxiYidongResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取验证码-渠道号:{},手机号:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, e);
            return JiangxiYidongResult.FAIL_RESULT;
        }
    }

    /**
     * "ret":0,"msg":"操作成功","data":{"callTime":"26ms","responseCode":"","resultMsg":{"result":"","respDesc":"验证码正确","respCode":"0"}}}
     * 提交验证码
     *
     * @param phone
     * @param channel
     * @param code
     * @param busiSerialNumber
     * @param company
     * @return
     */
    public @Nonnull
    JiangxiYidongResult smsCode(String phone, String channel, String code, String encryption, String busiSerialNumber, String company) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        JiangxiProductConfig jiangxiProductConfig = jiangxiYidongProperties.getJiangxiProductConfig(channel);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("billId", phone);
        dataMap.put("planId", jiangxiProductConfig.getPlanId());
        dataMap.put("goodsId", jiangxiProductConfig.getGoodsId());
        dataMap.put("busiType", "1");
        dataMap.put("randomCode", code);
        dataMap.put("isSync", "1");
        dataMap.put("businessSerialNumber", busiSerialNumber);
        //zyhl调用接口获取encryption
        if (StringUtils.equals(company, "zyhl")) {
            encryption = getEncryptionGaojie(company);
        } else {
            encryption = fetchEncryption(channel, company);
        }
        if (StringUtils.isBlank(encryption)) {
            log.warn("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},能力原子化字段为空", jiangxiCompanyConfig.getLogTag(), channel, phone, code);
            return JiangxiYidongResult.INSERT_CODE_FAIL_RESULT;
        }
        dataMap.put("encryption", encryption);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getSmsCodeUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, code);
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, code, content);
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String respCode = jsonNode.at("/data/resultMsg/respCode").asText();
//            return "0".equals(ret) && "0".equals(respCode);
            return mapper.readValue(content, JiangxiYidongResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-渠道号:{},手机号:{},短信验证码:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, code, e);
            return JiangxiYidongResult.FAIL_RESULT;
        }
    }

    /**
     * {"ret":0,"msg":"操作成功","data":{"callTime":"5214ms","responseCode":"1","resultMsg":{"code":"1","orderId":"22023052504290162629082570531306","resultCode":"1","message":"成功"}}}
     *
     * @param phone
     * @param channel
     * @param busiSerialNumber
     * @param company
     * @return
     */
    public @Nonnull
    JiangxiYidongOrderResult order(String phone, String channel, String busiSerialNumber, String company) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        JiangxiProductConfig jiangxiProductConfig = jiangxiYidongProperties.getJiangxiProductConfig(channel);
        String serialNumber = RandomStringUtils.randomAlphanumeric(16);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("phone", phone);
        dataMap.put("planId", jiangxiProductConfig.getPlanId());
        dataMap.put("goodsId", jiangxiProductConfig.getGoodsId());
//        dataMap.put("channelType", "126");
        dataMap.put("isSync", "1");
        dataMap.put("serialNumber", serialNumber);
        dataMap.put("busiSerialNumber", busiSerialNumber);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getOrderUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-业务订购-渠道号:{},手机号:{}", jiangxiCompanyConfig.getLogTag(), channel, phone);
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-业务订购-渠道号:{},手机号:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, content);
            return mapper.readValue(content, JiangxiYidongOrderResult.class);
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String resultCode = jsonNode.at("/data/resultMsg/resultCode").asText();
//            String code = jsonNode.at("/data/resultMsg/code").asText();
//            return "0".equals(ret) && "1".equals(resultCode) && "1".equals(code);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-业务订购-渠道号:{},手机号:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, e);
            return JiangxiYidongOrderResult.FAIL_RESULT;
        }
    }

    /**
     * @param phone
     * @param channel
     * @param busiSerialNumber
     * @param company
     * @param needSign         Y-无纸化预览 N-无纸化保存
     * @param orderId
     * @return
     */
    public @Nonnull
    JiangxiYidongNoPageWriteResult noPageWrite(String phone, String channel, String busiSerialNumber, String company, String needSign, String orderId) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        JiangxiProductConfig jiangxiProductConfig = jiangxiYidongProperties.getJiangxiProductConfig(channel);
        String busiInfo = jiangxiProductConfig.getOfferId() + "@@" + ("Y".equals(needSign) ? IdWorker.getIdStr() : orderId);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("NeedSign", needSign);
        dataMap.put("PhoneNo", phone);
        dataMap.put("OpTime", DateUtil.formatFullTime(LocalDateTime.now()));
        dataMap.put("CertType", "1");
        dataMap.put("channel", "1");
        dataMap.put("channelName", jiangxiCompanyConfig.getChannelName());
        dataMap.put("busiInfo", busiInfo);
        dataMap.put("busiSerialNumber", busiSerialNumber);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();
        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getNoPageWriteUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-无纸化-渠道号:{},手机号:{},无纸化类型:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, needSign);
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            JiangxiYidongNoPageWriteResult jiangxiYidongNoPageWriteResult = mapper.readValue(content, JiangxiYidongNoPageWriteResult.class);
            log.info("{}-无纸化-渠道号:{},手机号:{},无纸化类型:{},响应:{}", jiangxiCompanyConfig.getLogTag(), channel, phone, needSign, jiangxiYidongNoPageWriteResult);
            return jiangxiYidongNoPageWriteResult;
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String resultCode = jsonNode.at("/data/resultMsg/resultCode").asText();
//            String code = jsonNode.at("/data/resultMsg/code").asText();
//            return "0".equals(ret) && "1".equals(resultCode) && "1".equals(code);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-无纸化-渠道号:{},手机号:{},无纸化类型:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, phone, e);
            return JiangxiYidongNoPageWriteResult.FAIL_RESULT;
        }
    }

    /**
     * @param company
     * @param sysAccept
     * @return
     */
    public @Nonnull
    JiangxiYidongPdfResult getPdf(String company, String sysAccept) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        TreeMap<String, String> dataMap = new TreeMap();
        dataMap.put("sysAccept", sysAccept);
        String requestId = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomAlphanumeric(15);
        final FormBody.Builder formBodyBuilder = new FormBody.Builder();
        dataMap.forEach(formBodyBuilder::add);
        RequestBody formBody = formBodyBuilder.build();

        String sign = generateSign(jiangxiCompanyConfig.getApiKey(), "0", requestId, dataMap, jiangxiCompanyConfig);
        Request.Builder builder = new Request.Builder().url(jiangxiCompanyConfig.getQueryPdfUrl());
        getCommonParam(builder, requestId, sign, jiangxiCompanyConfig);
        Request request = builder.post(formBody).build();
        log.info("{}-无纸化查询-业务流水号:{}", jiangxiCompanyConfig.getLogTag(), sysAccept);
        try (Response response = getClient(company).newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            JiangxiYidongPdfResult jiangxiYidongNoPageWriteResult = mapper.readValue(content, JiangxiYidongPdfResult.class);
            log.info("{}-无纸化查询-业务流水号:{},响应:{}", jiangxiCompanyConfig.getLogTag(), sysAccept, jiangxiYidongNoPageWriteResult);
            return jiangxiYidongNoPageWriteResult;
//            JsonNode jsonNode = mapper.readTree(content);
//            String ret = jsonNode.at("/ret").asText();
//            String resultCode = jsonNode.at("/data/resultMsg/resultCode").asText();
//            String code = jsonNode.at("/data/resultMsg/code").asText();
//            return "0".equals(ret) && "1".equals(resultCode) && "1".equals(code);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-无纸化查询-业务流水号:{},异常:", jiangxiCompanyConfig.getLogTag(), sysAccept, e);
            return JiangxiYidongPdfResult.FAIL_RESULT;
        }
    }

    private void getCommonParam(Request.Builder builder, String requestId, String sign, JiangxiCompanyConfig jiangxiCompanyConfig) {
        builder.addHeader("apiKey", jiangxiCompanyConfig.getApiKey());
        builder.addHeader("flag", "0");
        builder.addHeader("userId", jiangxiCompanyConfig.getUserId());
        builder.addHeader("requestId", requestId);
        builder.addHeader("Authorization", "basic YWFhOmJiYg==");
        builder.addHeader("sign", sign);
    }

    private String generateSign(String apiKey, String flag, String requestId, TreeMap<String, String> data, JiangxiCompanyConfig jiangxiCompanyConfig) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("apiKey" + apiKey);
        stringBuilder.append("flag" + flag);
        stringBuilder.append("userId" + jiangxiCompanyConfig.getUserId());
        stringBuilder.append("requestId" + requestId);
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String mapKey = entry.getKey();
            String mapValue = entry.getValue();
            stringBuilder.append(mapKey + mapValue);
        }
        stringBuilder.append(jiangxiCompanyConfig.getSecretKey());
        return DigestUtils.md5DigestAsHex(stringBuilder.toString().getBytes(StandardCharsets.UTF_8));
    }

    public String getEncryptionGaojie(String company) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        final HttpUrl httpUrl = HttpUrl.parse(jiangxiCompanyConfig.getGetEncryptionUrl());
        log.info("{}-原子能力化,请求:{}", LOG_TAG, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-原子能力化,响应:{}", LOG_TAG, content);
            JsonNode jsonNode = mapper.readTree(content);
            return jsonNode.at("/data").asText();
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-原子能力化,异常:", LOG_TAG, e);
            return "";
        }
    }

    public String fetchEncryption(String channel, String company) {
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(company);
        String fetchEncryptionUrl = jiangxiCompanyConfig.getFetchEncryptionUrl();
        String particle = jiangxiCompanyConfig.getParticle();
        String reportPage = jiangxiCompanyConfig.getChannelReportPageMap().get(channel);
        if (StringUtils.isAnyEmpty(particle, reportPage)) {
            return null;
        }
        HttpUrl httpUrl = HttpUrl.parse(fetchEncryptionUrl)
                .newBuilder()
                .addQueryParameter("callback", "jQuery_" + System.currentTimeMillis())
                .addQueryParameter("appCode", ENCRYPTION_APP_CODE) //限定为抖音平台
                .addQueryParameter("url", reportPage)
                .addQueryParameter("other", "true")
                .addQueryParameter("particle", particle)
                .addQueryParameter("platformCode", "jl") //鸿盛报备过这个可以不传,但是悠然不传就会报错,所以统一传jl表示巨量
                .addQueryParameter("token", ENCRYPTION_TOKEN)
                .addQueryParameter("_", String.valueOf(System.currentTimeMillis()))
                .build();

        try (Response response = client.newCall(new Request.Builder().url(httpUrl).header("User-Agent", UA).build())
                .execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
            String pageContent = response.body().string();
            return StringUtils.substringBetween(pageContent, "'", "'");

        } catch (Exception e) {
            log.info("{}-原子能力化-渠道号:{},company:{},异常:", jiangxiCompanyConfig.getLogTag(), channel, company, e);
            return null;
        }
    }

    public static void main(String[] args) throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url("https://wap.jx.10086.cn/bsi/encryption/encrypt?callback=jQuery37104159569871090827_1720686639924&appCode=aweme&url=https%3A%2F%2Fvip.cdcxtc.com%2Fmobile_jx%2Fjiangxi_yd_eleven&other=true&particle=1500791961010&platformCode=&token=WZjy1d%2F0UNAwxsOmScXeIdJO4fnQQsXJy%2B2dkXDA6Idz%2BqXsUJPNhip%2FB0g6av1UZr3nVhKp83fIsvgl38bWRZ6T56oXTITABffAAI0RMpNxYbdza0Vt7iSDGMe2vZT8krqy3WwuiNfu4%2FadQR4Yv3pntBOxGfqpI3kYZgPiwMByx2CauknRX0rq6yV4WCbNP9KJQjWvGoupqxgP0lelErzPVP7QkgnIFNUIsn%2B8wnRBPcHva6eU%2Fj5bkNLOIGU3ccz8JGlat2zfJt3oGuhOaQscR0QTPdDXH2vsmhmZSMET%2BT%2FLGmUBZabkDF%2FysR9Lr9MdkMYUZDkvn3FKyNLD4Q%3D%3D&_=1720686639926")
                .addHeader("Connection", "keep-alive")
                //.addHeader("Cookie", "JSESSIONID=E55466C7C670D3553BBDFFD52BC310C1; FSSBBIl1UgzbN7NO=5OovV.MXYKY89LxjNmVeQgEUJDFzUDFSIhWvUjUwJb6YKu1zSzNNk6oBy76oSRyEhdnhsz10RjoAyoejBfyQoJq")
                .build();
        Response response = client.newCall(request).execute();
        System.out.println(response.body().string());
    }

}
