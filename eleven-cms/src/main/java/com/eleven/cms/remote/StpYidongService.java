package com.eleven.cms.remote;

import com.eleven.cms.config.JiangsuYidongProperties;
import com.eleven.cms.config.StpJiangsuYidongProperties;
import com.eleven.cms.config.StpYidongConfig;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;
import com.google.common.base.Joiner;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.TreeMap;

/**
 * 江苏业务类（斯特普江苏移动）
 *
 * @author: cai lei
 * @create: 2022-07-07 10:29
 */

@Slf4j
@Service
public class StpYidongService {

    public static final String LOG_TAG = "斯特普移动api";
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    @Autowired
    private StpJiangsuYidongProperties stpJiangsuYidongProperties;
    @Autowired
    private MiguApiService miguApiService;

    public static final MediaType XMLTYPE = MediaType.parse(org.springframework.http.MediaType.APPLICATION_XML_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                    .compressed()
                    .insecure()  //不检查自签名证书
                    //.connectTimeout(120)
                    //.retry(5)
                    .build();
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .addNetworkInterceptor(new CurlInterceptor(System.out::println, options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    public StpJiangsuYidongResult getSms(String phone, String channel, String sourceUrl, String app, String ip) {
        String url = stpJiangsuYidongProperties.getGetSmsUrl();
        StpYidongConfig stpYidongConfig = stpJiangsuYidongProperties.getChannelConfigMap().get(channel);
        String apiKey = stpYidongConfig.getApiKey();
        String apiUser = stpYidongConfig.getApiUser();
        String cid = stpYidongConfig.getCid();

        ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("apiUser", apiUser);
        paramNode.put("mobile", phone);
        paramNode.put("cid", cid);
        final TreeMap<String, String> treeMap = mapper.convertValue(paramNode, new TypeReference<TreeMap<String, String>>() {
        });
        String queryString = Joiner.on("&")
                // 用指定符号代替空值,key 或者value 为null都会被替换
                .useForNull("")
                .withKeyValueSeparator("=")
                .join(treeMap);
        log.info(queryString);
        final String sign = DigestUtils.md5DigestAsHex((queryString + "&apikey=" + apiKey).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        paramNode.put("sign", sign);
        paramNode.set("extra", mapper.createObjectNode()
                .put("source_url", sourceUrl)
                .put("package_name", app)
                .put("user_ip", ip));

        RequestBody body = RequestBody.create(JSON, paramNode.toString());
        log.info("{}-获取验证码-手机号:{},渠道号:{}", LOG_TAG, phone, channel);
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取验证码-手机号:{},渠道号:{},响应:{}", LOG_TAG, phone, channel, result);
            return mapper.readValue(result, StpJiangsuYidongResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone, channel, e);
            return StpJiangsuYidongResult.fail();
        }
    }

    public StpJiangsuYidongResult smsCode(String phone, String channel, String code, String ispOrderNo, String appEncryption, String sourceUrl, String ip, String ua, String app) {
        String url = stpJiangsuYidongProperties.getSmsCodeUrl();
        StpYidongConfig stpYidongConfig = stpJiangsuYidongProperties.getChannelConfigMap().get(channel);
        String apiKey = stpYidongConfig.getApiKey();
        String apiUser = stpYidongConfig.getApiUser();
        String cid = stpYidongConfig.getCid();
        ObjectNode paramNode = mapper.createObjectNode();
        paramNode.put("apiUser", apiUser);
        paramNode.put("mobile", phone);
        paramNode.put("cid", cid);
        paramNode.put("source", sourceUrl);
        paramNode.put("orderSn", ispOrderNo);

        final TreeMap<String, String> treeMap = mapper.convertValue(paramNode, new TypeReference<TreeMap<String, String>>() {});
        String queryString = Joiner.on("&")
                // 用指定符号代替空值,key 或者value 为null都会被替换
                .useForNull("")
                .withKeyValueSeparator("=")
                .join(treeMap);
        final String sign = DigestUtils.md5DigestAsHex((queryString + "&apikey=" + apiKey).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        paramNode.put("sign", sign);
        ObjectNode extraNode = mapper.createObjectNode().put("sms_code", code);
        if (StringUtils.isNotBlank(appEncryption)) {
            extraNode.put("appEncryption", appEncryption);
        }
        extraNode.put("ip", ip);
        extraNode.put("sourceUrl", sourceUrl);
        extraNode.put("userAgent", ua);
        extraNode.put("packageName", app);
        paramNode.set("extra", extraNode);
        RequestBody body = RequestBody.create(JSON, paramNode.toString());
        log.info("{}-提交验证码-手机号:{},渠道号:{},验证码:{}", LOG_TAG, phone, channel, code);
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},验证码:{}-响应:{}", LOG_TAG, phone, channel, code, result);
            return mapper.readValue(result, StpJiangsuYidongResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-手机号:{},渠道号:{},验证码:{},异常:", LOG_TAG, phone, channel, code, e);
            return StpJiangsuYidongResult.fail();
        }
    }
}