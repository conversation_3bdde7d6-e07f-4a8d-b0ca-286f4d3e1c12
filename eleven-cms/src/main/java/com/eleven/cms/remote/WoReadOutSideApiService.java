package com.eleven.cms.remote;

import com.eleven.cms.config.*;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2023-5-25 14:08:32
 * Desc: 触点（支付能力）相关接口
 */
@Slf4j
@Service
public class WoReadOutSideApiService {

    public static final String LOG_TAG = "沃悦读外部支付业务api";

    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private WoReadOutSideProperties woReadOutSideProperties;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");
    public static final String SOURCE_CODE = "11";


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.testProxyHost, OkHttpClientUtils.testProxyPort)))
//                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 获取token,从Redis获取，获取不到就调用接口
     * @param
     * @return
     */
    public String getToken(WoReadOutSideProperties woReadOutSideProperties){

        String token = (String) redisUtil.get("woRead-token-" + woReadOutSideProperties.getClientId());
        if(StringUtils.isEmpty(token)){
            try {
                String url = woReadOutSideProperties.getTokenUrl();
                HttpUrl httpUrl = HttpUrl.parse(url)
                        .newBuilder()
                        .addQueryParameter("clientSource", SOURCE_CODE)
                        .addQueryParameter("clientId", woReadOutSideProperties.getClientId())
                        .addQueryParameter("clientSecret", woReadOutSideProperties.getClientSecret())
                        .build();

                log.info("{}-获取token接口-请求:{}", LOG_TAG, httpUrl.toString());
                Request request = new Request.Builder().url(httpUrl)
                        .build();
                try (Response response = client.newCall(request)
                        .execute()) {
                    if (!response.isSuccessful()) {
                        throw new IOException("Unexpected code " + response);
                    }

                    String content = response.body().string();
                    WoReadTokenResult result = mapper.readValue(content, WoReadTokenResult.class);
                    if(result.isOK()){
                        String key = result.getKey();
                        String keyType = result.getKey_type();
                        token = keyType + " " + key;
                        redisUtil.setIfAbsent("woRead-token-" + woReadOutSideProperties.getClientId(),token,604000);
                    }
                    log.info("{}-获取token接口-响应:{}", LOG_TAG, content);
                }
            } catch (IOException e) {
                log.warn("{}-获取token接口-异常:", LOG_TAG, e);
            }
        }
        return token;
    }

    /**
     * 话费支付
     * mobile
     * ip
     * @return
     */
    public WoReadMobilePayResult mobilePay(String mobile,String ip,String source){

        try{
            String wokey = "";
            Map<String, Object> map = new HashMap<>();
            if(StringUtils.isNotEmpty(source)){
                wokey = HttpUrl.parse(
                        source.replace("#/","")).queryParameter("wokey");
            }
            //构建请求body参数
            map.put("productpkgid",woReadOutSideProperties.getProductpkgid());//必传，套餐索引
            map.put("returl",woReadOutSideProperties.getReturl());//必填。订单完成以后，同步调度回跳地址
            map.put("bakurl",woReadOutSideProperties.getNotifyUrl());//必填。订单完成以后，同步调度通知地址
            map.put("ip",ip);//订购用户终端ip
            map.put("channlidkey",wokey);//推广页面每次打开动态生成的参数

            String url = woReadOutSideProperties.getOrderUrl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .addPathSegment(SOURCE_CODE)
                    .addPathSegment(mobile)
                    .addPathSegment(woReadOutSideProperties.getChannelId())
                    .build();
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(map));
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("AuthorizationClient",getToken(woReadOutSideProperties))
                    .post(body)
                    .build();
            log.info("{}-话费支付接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-话费支付接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
                WoReadMobilePayResult woReadMobilePayResult = mapper.readValue(result, WoReadMobilePayResult.class);
                JsonNode jsonNode = mapper.readTree(result);
                String message = jsonNode.at("/message").toString();
                if(woReadMobilePayResult.isOK()){
                    woReadMobilePayResult.setData(mapper.readValue(message,WoReadMobilePayResult.WoReadMobilePayMessage.class));
                }else{
                    woReadMobilePayResult.setResMessage(message);
                }
                return woReadMobilePayResult;
            }
        }catch (Exception e){
            log.info("{}-话费支付接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadMobilePayResult.fail();
        }
    }

    public WoReadPkgOrderedStatusResult getPkgOrderedStatus(String mobile,String channelId){

        String url = woReadOutSideProperties.getPkgOrderedStatusUrl();
        HttpUrl httpUrl = HttpUrl.parse(url)
                .newBuilder()
                .addPathSegment(SOURCE_CODE)
                .addPathSegment(channelId)
                .addPathSegment(mobile)
                .addPathSegment(woReadOutSideProperties.getProductpkgid())
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .addHeader("Content-Type","application/json")
                .addHeader("AuthorizationClient",getToken(woReadOutSideProperties))
                .build();
        log.info("{}-获取用户包月产品的订购状态接口,手机号:{},请求:{}", LOG_TAG, mobile,request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取用户包月产品的订购状态接口,手机号:{},返回结果:{}", LOG_TAG,mobile, result);
            WoReadPkgOrderedStatusResult woReadPkgOrderedStatusResult = mapper.readValue(result, WoReadPkgOrderedStatusResult.class);
            JsonNode jsonNode = mapper.readTree(result);
            String message = jsonNode.at("/message").toString();
            if(woReadPkgOrderedStatusResult.isOK()){
                woReadPkgOrderedStatusResult.setData(mapper.readValue(message, WoReadPkgOrderedStatusResult.WoReadPkgOrderedStatusMessage.class));
            }else{
                woReadPkgOrderedStatusResult.setResMessage(message);
            }
            return woReadPkgOrderedStatusResult;
        } catch (Exception e) {
            log.info("{}-获取用户包月产品的订购状态接口,手机号:{},异常:", LOG_TAG,mobile, e);
            return WoReadPkgOrderedStatusResult.fail();
        }
    }

}
