package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.eleven.cms.ad.AdReportService;
import com.eleven.cms.config.ThirdPartyChannelConfigProperties;
import com.eleven.cms.entity.JunboSub;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WyyMmOrder;
import com.eleven.cms.es.entity.EsSubStatistics;
import com.eleven.cms.es.repository.EsSubStatisticsRepository;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.mapper.MemberCommonMapper;
import com.eleven.cms.mapper.SubscribeMapper;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.service.*;
import com.eleven.cms.service.impl.SmsNotifyService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.*;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.util.DateUtils;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.queue.RedisDelayedQueueManager.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * Author: <EMAIL>
 * Date: 2021/8/10 15:28
 * Desc:包月业务校验
 */
@Service
@Slf4j
public class SubscribeVerifyService {
    @Autowired
    SubscribeMapper subscribeMapper;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    MemberCommonMapper memberCommonMapper;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    IEsDataService esDataService;
    @Autowired
    IJunboChargeLogService junboChargeLogService;
    @Autowired
    ThirdPartyChannelConfigProperties thirdPartyChannelConfigProperties;
    @Autowired
    DianxinVrbtService dianxinVrbtService;
    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    AdReportService adReportService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    IWyyMmOrderService wyyMmOrderService;
    @Autowired
    IMiGuKuaiYouVRJingMengService vrJingMengService;
    @Autowired
    HenanYidongService henanYidongService;
    @Autowired
    HenanYidongGaojieService henanYidongGaojieService;
    @Autowired
    GuangdongYidongDuxingService guangdongYidongDuxingService;
    @Autowired
    EsSubStatisticsRepository esSubStatisticsRepository;
    @Autowired
    SmsNotifyService smsNotifyService;
    @Lazy
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Autowired
    OutsideCallbackService outsideCallbackService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    IHetuCouponCodeService hetuCouponCodeService;
    @Autowired
    MiGuHuYuService miGuHuYuService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    private IHeTuFenShengChongQingService huyuOrderService;
    @Autowired
    private IJunboSubService junboSubService;
    @Autowired
    private IGanSuMobileApiService ganSuMobileApiService;
    @Autowired
    SiChuanMobileApiService siChuanMobileApiService;
    //视频彩铃侧特定渠道号报表
    public static final Set<String> VRBT_SIDE_CHANNEL_CODES = Sets.newHashSet(
//            "014X04C", //佐悦视频彩铃
//            "014X04D",//佐悦视频彩铃
//            "014X04E",//佐悦视频彩铃
//            "014X04G",//佐悦视频彩铃
//            "00210Q6",//华岸权益包
//            "00210XZ",//华岸权益包
//            "00210Y0",//华岸权益包
//            "00211DI",//华岸权益包
//            "00211DJ",//华岸权益包
//            "002110A",//光明网权益包
//            "00210W5",//光明网权益包
//            "00211DF",//光明网权益包
//            "00211DG",//光明网权益包
//            "00210W6",
//            "00210W7",
//            "00210W0",
//            "0021107",//龙腾权益包
//            "0021108",//龙腾权益包
//            "00211DC",//龙腾权益包
//            "00211DD",//龙腾权益包
//            "002112O",//七彩音乐30元包
//            "002112S",//七彩音乐30元包
//            "002112T",//七彩音乐30元包
//            "00211D2",//乐趣音乐20元包
//            "00211D3",//乐趣音乐20元包
//            "014X05A",//高姐视频彩铃
//            "014X056",//高姐视频彩铃
//            "002105C",//藕粉咪咕同享会10元包
//            "002115U",//圈子彩铃-公众版10元包(酷炫来电-悠然视铃)
//            "00211DL",//圈子彩铃-公众版10元包(酷炫来电-悠然视铃)
//            "00211DK",//圈子彩铃-公众版10元包(酷炫来电-悠然视铃)
//            "002118U",//新一-视彩号-数智人
//            "HY_25_CW",//悠然互娱VR
//            "HYQY_CF_YR912", //悠然互娱-厨房
//            "HYQY_YR_DQHY", //悠然互娱-第七幻城

            "014X02E",//讯飞-视频彩铃订阅-酷电秀专属6元包
            "014X02D",//讯飞-视频彩铃订阅-酷电秀专属6元包
            "014X02F",//讯飞-视频彩铃订阅-酷电秀专属6元包
            "014X02H",//讯飞-视频彩铃订阅-酷电秀专属6元包
            "014X02G",//讯飞-视频彩铃订阅-酷电秀专属6元包
            "0021180",//讯飞-视宣号-圈子彩铃-彩铃小帮手-声动视铃A 10元
            "00211AI",//讯飞-视宣号-圈子彩铃-彩铃小帮手-声动视铃B 10元
            "00211AH",//讯飞-视宣号-圈子彩铃-彩铃小帮手-声动视铃C 10元
            "00210PP",//白金会员
            "00211DQ",//视彩号-一语成片-AI视频彩铃标准版
            "00211GY",//视彩号-AI视频彩铃
            "HYQY_YR",//悠然互娱-河图寻仙记高级礼包
            "HYQY_YR_20",//悠然互娱-河图寻仙记高级礼包
            "HYQY_HTJB15_927", //骏伯互娱-河图寻仙记升级礼包
            "HYQY_HTJB20_924", //骏伯互娱-河图寻仙记高级礼包
            "HYQY_HTJB20_925", //骏伯互娱-河图寻仙记高级礼包
            "HYQY_HTJB20_926", //骏伯互娱-河图寻仙记高级礼包
            "HYQY_HTJB20_927", //骏伯互娱-河图寻仙记高级礼包
            "HYQY_HTJB30_927", //骏伯互娱-河图寻仙记黄金礼包
            "HYQY_JUNBO", //骏伯互娱-厨房大逃亡经营礼包
            "HYQY_JUNBO_20", //骏伯互娱-厨房大逃亡经营礼包
            "HYQY_SJCS_JB925", //骏伯互娱-水晶传说命定特权礼包
            "HYQY_SJCS_JB926", //骏伯互娱-水晶传说命定特权礼包
            "HYQY_SJCS_JB171", //骏伯互娱-水晶传说命定特权礼包
            "HYQY_JBDQHY", //骏伯互娱-第七幻域钻石专享礼包
            "HYQY_ZUOYUE", //佐悦互娱-大鱼消除休闲礼包
            "HYQY_AHZZ_YR_20", //悠然互娱-暗黑主宰20元礼包
            "HYQD_HS", //鸿盛互娱-咪咕快游钻石会员
            "HYQD_O_HS", //鸿盛互娱-咪咕快游钻石会员
            "HYQD_MAIHE", //麦禾互娱-咪咕快游钻石会员
            "HETU_CQ", //河图重庆分省-咪咕快游道具大礼包-河图寻仙记手游礼包
            "HETU_HB",  //河图河北分省-河图寻仙记手游礼包
            "AYY_AISPCL_HS",  //电信鸿盛-AI视频彩铃智玩包渠道号
            "QYCL",
            "QYCL_GR",
            "QYCL_MH",
            "QYCL_GR_MH",
            "GSYD_HS_DKJY", //鸿盛甘肃移动-29.9元抖快加油权益包
            "GSYD_QN_DKJY", //北京青牛甘肃移动-29.9元抖快加油权益包
            "00210U2",
            "HNYZ_SXX",  //高姐河南移动
            "HNYZ_MGYD_DJJP", //高姐河南移动
            "KMSX_VRBT_DYB", //快马山西移动视频彩铃
            "GDYD_LLB", //广东移动流量包实际是炫视
            "WANGYIYUN_MM",
            "JSYD_VRBT",
            "SCYD_XSVRBTYL",
            "SCYD_XSVRBTCW",
            "SCYD_YLCWB",
            "SHYD_XSSP",
            "SHYD_XSSP_VS",
            "SHYD_XSMGSP",
            "SHYD_XSNB",
            "HN_VRBT",
            "HN_VRBT_DY_LLB",
            "GZYD_VRBT_XSZS",
            "JXYD_VRBT",
            "JXYD_VRBT_20",
            "GDDX_VRBT",
            "00211M4",
            "00211M5",
            "00211M6",
            "00211MM",
            "00211MN",
            "00211MO",
            "00211MP"
            //"MH_QYHY",//三方支付-特惠省钱包
            //"GZYD_MGZQ_QQB",//贵州移动-咪咕足球全通包包月40元
            //"GZYD_QXX_BJHY",//贵州移动-黔心选铂金会员
            //"GZYD_QXX_PLUS",//贵州移动-黔心选PLUS会员
            //"GZYD_XSZS",//贵州移动-炫视专属包
            //"GZYD_KDX_ZSB",//贵州移动-酷电秀专属包
            //"GZYD_QYJXB",//贵州移动-权益嘉享包-20元档
            //"GZYD_NSXK",//贵州移动-贵州随心看
            //"GZYD_TSQY",//贵州移动-提速包20元档
            //"GZYD_ZSHY_SYTH",//贵州移动-咪咕视频钻石会员双月特惠产品
            //"YN_BJHY_HS",//云南移动-权益超市铂金会员（云南版）--鸿盛
            //"YN_SZSH_HS",//云南移动-数智生活权益包-6元版
            //"JXYD_MSHYLLB",//江西移动-权益超市美食会员_流量畅享版——鸿盛
            //"JXYD_MCAI_HS",//江西移动-萌宠AI联合会员月包
            //"HBYD_YLFD",//河北移动-20元娱乐福袋（24个月）
            //"HBYD_LLB",//河北移动-30元20G流量月包
            //"HBYD_THYB",//河北移动-30元30G特惠月包
            //"HBYD_SHYB",//河北移动-20元数字生活月包
            //"HBYD_HSTFD",//河北移动-移动惠试听福袋（24个月）
    );

    //网盘会员侧特定渠道号报表
    public static final Set<String> MEMBER_SIDE_CHANNEL_CODES = Sets.newHashSet(
            "COMIC",//咪咕圈圈-咪咕西班牙人月包
            "COMIC_JUNBO",//咪咕圈圈-咪咕西班牙人月包
            "COMIC_217",//咪咕圈圈-咪咕西班牙人月包
            "COMIC_107",//咪咕圈圈-咪咕西班牙人月包
            "MIGU_READ",//咪咕阅读畅享会员
            "MIGU_READ_JUNBO",//咪咕阅读畅享会员
            "MG_READ_JUNJUE",//咪咕悦读黄金会员
            "MIGU_READ_DJJP",//咪咕阅读短剧精品
            "READ_DJJP_JUNJUE",//咪咕阅读短剧精品
            "READ_DJXP_JUNJUE",//咪咕阅读短剧新品
            "MIGU_READ_DJXP",//咪咕阅读短剧新品
            "READ_DJXP_JUNJUE",//咪咕阅读短剧新品
            "READ_JUNJUE_DJXP",//咪咕阅读短剧新品
            "00210QL", //网易云联合会员
            "00211B8", //网易云联合会员
            "00211B9",  //网易云联合会员
            "MIGU_DJCJHY",  //咪咕短剧超级会员
            "MIGU_DJBZHY"  //咪咕短剧标准会员
    );

    //视频彩铃和网盘会员两侧合并特定渠道号报表
    public static final Set<String> COMBIN_CHANNEL_CODES = Sets.union(VRBT_SIDE_CHANNEL_CODES, MEMBER_SIDE_CHANNEL_CODES);


    public static final Date BIZ_QYCL_VERIFY_START_DATE = DateUtils.str2Date("2023-07-01 00:00:00", DateUtils.datetimeFormat.get());

    public Integer monthVerify(Subscribe subscribe) {
        Integer status = SUBSCRIBE_MONTH_VERIFY_INIT;
        switch (subscribe.getBizType()){
            case BIZ_TYPE_VRBT:
                if(MobileRegionResult.ISP_DIANXIN.equals(subscribe.getIsp())) {
                    status = dianxinVrbtService.queryPackageExist(subscribe.getMobile(), subscribe.getServiceId()) ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }else {
                    status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), subscribe.getChannel(),false) > 0 ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }
                break;
            case BIZ_TYPE_QYCL:
                final boolean verifyMonth = enterpriseVrbtService.verifyMonth(subscribe.getMobile(), subscribe.getChannel());
                status = verifyMonth ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_CPMB:
                final RemoteResult result = miguApiService.cpmbQuery(subscribe.getMobile(), subscribe.getChannel());
                status = result.isCpmbMember() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_BJHY:
            case BIZ_TYPE_SXH: //视宣号(圈子彩铃)包月查询方法和白金会员一样
                final RemoteResult bjhyResult = miguApiService.bjhyQuery(subscribe.getMobile(), subscribe.getChannel());
                status = bjhyResult.isBjhyMember() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_SCH:
                final RemoteResult schResult = miguApiService.schQuery(subscribe.getMobile(), subscribe.getChannel());
                status = schResult.isSchMember() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_UNION_MEMBER:
                final RemoteResult asMemberResult = miguApiService.asMemberQuery(subscribe.getMobile(), subscribe.getChannel());
                status = asMemberResult.isAsMember() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_MGHY:
                final FebsResponse febsResponse = vrJingMengService.vrJingMengGetUser(subscribe);
                status = febsResponse.isOK() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_JSYD_VRBT:
                //JiangsuResponseQueryInfo jiangsuResponseQueryInfo = jiangsuYidongService.queryOrder(subscribe.getMobile(), subscribe.getIspOrderNo(), jiangsuYidongService.getToken());
                //status = jiangsuResponseQueryInfo.isOk() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                boolean isVrbtMemberJsyd = jiangsuYidongService.queryOrderMigu(subscribe.getMobile());
                status = isVrbtMemberJsyd ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            //省移动和咪咕音乐基地合作的炫视视频彩铃,使用基地的渠道号来查询订购关系
            case BIZ_TYPE_SHANGHAI: //上海移动炫视视频彩铃
                if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_SHYD_XSSP, BIZ_CHANNEL_SHYD_XSSP_VS, BIZ_CHANNEL_SHYD_XSMGSP, BIZ_CHANNEL_SHYD_XSNB)) {
                    status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, false) > 0 ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }
                break;
            case BIZ_TYPE_HN_VRBT: //湖南移动炫视视频彩铃
            case BIZ_TYPE_HN_VRBT_DY_LLB: //湖南移动炫视视频彩铃
            case BIZ_TYPE_GDDX_VRBT://广东笃行视频彩铃
            case BIZ_TYPE_GZYD_VRBT_XSZS: //贵州移动炫视视频彩铃
            case BIZ_TYPE_GZYD_XSZS: //贵州移动炫视专属包
            case BIZ_TYPE_KMSX_VRBT_DYB: //快马江西视频彩铃炫视专属订阅包
            //case BIZ_TYPE_HBYD_VRBT: //河北移动视频彩铃,河北移动视频彩铃不支持查询,故移除
                status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT,false) > 0 ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_SCYD: // 四川移动
                //四川移动业务只验证炫视视频彩铃业务
                if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_SCYD_XSVRBTYL, BIZ_CHANNEL_SCYD_XSVRBTCW, BIZ_CHANNEL_SCYD_YLCWB)) {
                    status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, false) > 0 ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }
                break;
            case BIZ_TYPE_JXYD: //江西移动炫视视频彩铃
                //江西移动业务只验证炫视视频彩铃业务
                if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEl_JXYD_VRBT, BIZ_CHANNEl_JXYD_VRBT_20, BIZ_CHANNEL_SCYD_YLCWB)) {
                    status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, false) > 0 ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }
                break;
            case BIZ_TYPE_GUANGDONG: //广东移动炫视视频彩铃
                //广东移动业务只验证炫视视频彩铃业务
                if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_GDYD_LLB)) {
                    status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, false) > 0 ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }
                break;
            case BIZ_TYPE_XIZANG: //西藏移动炫视视频彩铃
                //广东移动业务只验证炫视视频彩铃业务
                if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_XZ_XUANSHI)) {
                    status = miguApiService.verifyMiguVrbtMonthStatus(subscribe.getMobile(), MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT, false) > 0 ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }
                break;
            case BIZ_TYPE_WANGYIYUN_MM:
                WyyMmOrder wyyMmOrder=wyyMmOrderService.wyyMMIsMember(subscribe.getMobile());
                status = wyyMmOrder!=null ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_JUNBOLLB:
                if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_SXYD_JBAICL)) {
                    final RemoteResult sxydSchResult = miguApiService.schQuery(subscribe.getMobile(), MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP);
                    status = sxydSchResult.isSchMember() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                }
                break;
            case BIZ_TYPE_JUNBO_LLB:
                String extra=subscribe.getExtra();
                status = StringUtils.isBlank(extra) || !StringUtils.equals("退订订单",extra) ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            //河南移动
            case BIZ_TYPE_HNYZ_SHFW_HYB:
            case BIZ_TYPE_HNYZ_SPMS_ZHB:
            case BIZ_TYPE_HNYZ_VRBT_SXX:
            case BIZ_TYPE_HENYD_YR_HYB:
            case BIZ_TYPE_HENYD_YR_SXX:
            case BIZ_TYPE_HENYD_YR_MGB:
            case BIZ_TYPE_HENYD_YR_40GLLB:
            case BIZ_TYPE_HENYD_YR_ZSHY:
            case BIZ_TYPE_HENYD_YR_5GLLB:
            case BIZ_TYPE_HNYZ_SXX:
            case BIZ_TYPE_HNYZ_MGYD_DJJP:
            case BIZ_TYPE_HENYD_YR_5GXTH:
            case BIZ_TYPE_HENYD_YR_DJB:
            case BIZ_TYPE_HENYD_YR_DYLLB:
            case BIZ_TYPE_HENYD_YR_50GLLB:
            case BIZ_TYPE_HNYZ_DYLLB:
            case BIZ_TYPE_HNYZ_50GLLB:
                IHenanYidongService service = subscribe.getChannel().startsWith("HNYZ_") ? henanYidongGaojieService : henanYidongService;
                status = service.orderResultJudge(subscribe.getIspOrderNo(), subscribe.getChannel(),subscribe.getMobile());
                break;
            case BIZ_TYPE_HETU:
            case BIZ_TYPE_CFDTW:
            case BIZ_TYPE_DYXC:
            case BIZ_TYPE_DUANJU:
            case BIZ_TYPE_SJCS:
            case BIZ_TYPE_DQHY:
            case BIZ_TYPE_AHZZ:
                String channel=subscribe.getChannel();
                //鲲鹏短剧特定渠道号查询包月状态
                if (StringUtils.equalsAny(subscribe.getBizType(), BIZ_TYPE_DUANJU)) {
                    channel=BIZ_CHANNEl_HYQY_ZUOYUE;
                }
                //四川移动河图单独处理
                if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEL_SC_HTXXJ_HS)) {
                    SichuanMobileQueryOrderResult sichuanMobileQueryOrderResult= siChuanMobileApiService.queryOrder(subscribe.getIspOrderNo(),subscribe.getChannel(),subscribe.getMobile());
                    status = sichuanMobileQueryOrderResult.isOK() && sichuanMobileQueryOrderResult.getResult()!=null && sichuanMobileQueryOrderResult.getResult().isOK() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                    break;
                }
                boolean isMember=hetuCouponCodeService.isMember(subscribe.getMobile(),channel);
                status = isMember? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_HYQD:
                boolean isMonthlyMember=miGuHuYuService.queryMonthly(subscribe.getMobile(),subscribe.getChannel());
                status = isMonthlyMember? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_MEMBER_ALIPAY:
                boolean unSub=aliSignRecordService.unSub(subscribe.getMobile(),subscribe.getChannel(),subscribe.getBizType());
                status = unSub? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_HETU_FENSHENG:
                boolean isChongQingMember=huyuOrderService.isMember(subscribe.getMobile(),subscribe.getChannel());
                status = isChongQingMember? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_DX_AIVRBT:
                JunboSub junboSub = junboSubService.lambdaQuery()
                        .eq(JunboSub::getMobile,subscribe.getMobile())
                        .eq(JunboSub::getChannel, subscribe.getChannel())
                        .eq(JunboSub::getIsp, MobileRegionResult.ISP_DIANXIN) //只验证电信的
                        .eq(JunboSub::getStatus, SUBSCRIBE_STATUS_SUCCESS).orderByDesc(JunboSub::getCreateTime).last("limit 1").one();
                status = junboSub !=null && (SUBSCRIBE_STATUS_SUCCESS.equals(junboSub.getPrice()) || SUBSCRIBE_STATUS_INIT.equals(junboSub.getPrice()))? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_GANSU_MOBILE:
                GansuMobileServiceResult gansuMobileResult= null;
                try {
                    gansuMobileResult = ganSuMobileApiService.queryOrderService(subscribe.getChannel(),subscribe.getMobile(),subscribe.getIp());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                status = gansuMobileResult !=null && gansuMobileResult.isOK() && gansuMobileResult.getData()!=null ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            case BIZ_TYPE_SCYD_HS:
                SichuanMobileQueryOrderResult sichuanMobileQueryOrderResult= siChuanMobileApiService.queryOrder(subscribe.getIspOrderNo(),subscribe.getChannel(),subscribe.getMobile());
                status = sichuanMobileQueryOrderResult.isOK() && sichuanMobileQueryOrderResult.getResult()!=null && sichuanMobileQueryOrderResult.getResult().isOK() ? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
            default:
                boolean gameIsMember=hetuCouponCodeService.isMember(subscribe.getMobile(),subscribe.getChannel());
                status = gameIsMember? SUBSCRIBE_MONTH_VERIFY_EXISTS : SUBSCRIBE_MONTH_VERIFY_NONE;
                break;
        }
        return status;
    }

    /**
     * 6个月校验
     * @param subscribe
     * @return
     */
    public void verify6Month(Subscribe subscribe) {
        //只核实移动业务
        if(subscribe==null|| !MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) {
            return;
        }
        int verifyStatus;
        //外部渠道则不需要校验,直接设定为退订状态
        if(thirdPartyChannelConfigProperties.isThirdPartyChannel(subscribe.getChannel())){
            verifyStatus = SUBSCRIBE_MONTH_VERIFY_NONE;
         //企业彩铃排除7月之前的
        }else if(BIZ_TYPE_QYCL.equals(subscribe.getBizType())&&subscribe.getCreateTime()!=null&&subscribe.getCreateTime().before(BIZ_QYCL_VERIFY_START_DATE)){
            verifyStatus = SUBSCRIBE_MONTH_VERIFY_EXISTS;
        } else{
            verifyStatus = this.monthVerify(subscribe);
        }
        if (verifyStatus > SUBSCRIBE_MONTH_VERIFY_INIT) {
            subscribeService.updateVerifyStatusDbAndEs(subscribe.getId(),verifyStatus);
        }
    }

    public void verifyMsgHandle(DelayedMessage delayedMessage){
        final String tag = delayedMessage.getTag();
        if (MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY.equals(tag)) {
            final Subscribe subscribe = subscribeService.getById(delayedMessage.getId());
            //找不到要校验的对象
            if (subscribe == null) {
                return;
            }
            int verifyStatus = monthVerify(subscribe);
            if (verifyStatus > SUBSCRIBE_MONTH_VERIFY_INIT) {
                //                subscribeService.lambdaUpdate().eq(Subscribe::getId,delayedMessage.getId())
                //                                .set(MESSAG_EXTRA_3_DAY.equals(delayedMessage.getExtra()) ? Subscribe::getVerifyStatusDaily : Subscribe::getVerifyStatus, verifyStatus)
                //                                .update();
                Subscribe sub = new Subscribe();
                sub.setId(delayedMessage.getId());
                if (MESSAG_EXTRA_UNSUB_1_DAY.equals(delayedMessage.getExtra())) {
                    //24小时退订上报
                    if (verifyStatus == SUBSCRIBE_MONTH_VERIFY_NONE) {
                        adReportService.unsubReport(subscribe.getDeviceInfo(), subscribe.getSubChannel(), subscribe.getMobile(), subscribe.getSource(), subscribe, delayedMessage.getExtra());
                    }
                    //企业彩铃需要将24小时(1天退订)包月状态保存到3天订购状态,此处不返回
                    if(!BizConstant.BIZ_TYPE_QYCL.equals(subscribe.getBizType())){
                        return;
                    }
                }
                //vr竞盟30分钟查询是否订购成功
                if (MESSAG_EXTRA_60_MIN.equals(delayedMessage.getExtra())) {
                    sub.setVerifyStatus(verifyStatus);
                    //1小时退订外部回调
                    if (BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE == verifyStatus) {
                        //1小时退订也上报广告平台
                        adReportService.unsubReport(subscribe.getDeviceInfo(), subscribe.getSubChannel(), subscribe.getMobile(), subscribe.getSource(), subscribe, delayedMessage.getExtra());
                        subscribeService.unsubscribeOutSideCallback(subscribe);
                        junboChargeLogService.recoverKugouOrder(subscribe);
                    }
                    //1小时存量在订上报
                    if(BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS==verifyStatus) {
                        adReportService.subedReport(subscribe.getDeviceInfo(), subscribe.getSubChannel(), subscribe.getMobile());
                    }
                } else {
                    sub.setVerifyStatusDaily(verifyStatus);
                    //将退订号码写入黑名单
                    if (verifyStatus == SUBSCRIBE_MONTH_VERIFY_NONE) {
                        try {
                            blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
                        } catch (Exception e) {
                            log.error("写入黑名单错误,手机号:{}", subscribe.getMobile(), e);
                        }
                    }
                }
                subscribeService.updateSubscribeDbAndEs(sub);
                //如果1小时退订校验为企业彩铃的退订,就查询是否超出当日退订率
                if (MESSAG_EXTRA_60_MIN.equals(delayedMessage.getExtra()) && BIZ_TYPE_QYCL.equals(subscribe.getBizType()) && verifyStatus == SUBSCRIBE_MONTH_VERIFY_NONE) {
                      subscribeVerifyService.qyclDailyVerifyLimit(subscribe);
                }
            }
        }
    }

    @Async
    public void qyclDailyVerifyLimit(Subscribe subscribe) {
        final String channel = subscribe.getChannel();
        final Map<Long, Long> verifyStatusToday = esDataService.subscribeStatisticsQyclVerifyStatusToday(channel);

        final Long unSubCount = verifyStatusToday.getOrDefault(SUBSCRIBE_MONTH_VERIFY_NONE.longValue(), 0L);
        final Long subCount = verifyStatusToday.getOrDefault(SUBSCRIBE_MONTH_VERIFY_EXISTS.longValue(), 0L);
        final long total = unSubCount + subCount;
        //如果当日退订率超过阈值,则发送短信通知
        if (total > 0) {
            double unSubRate = unSubCount.doubleValue() / total;
            if (unSubRate > 0.16) {
                smsNotifyService.sendQyclVerifyLimitNotify(channel,String.format("%.3f", unSubRate));
            }
        }

    }

    public Set<String> transLast6MonthToVerifyIncludeBizType(){
        return VRBT_SIDE_CHANNEL_CODES.stream().map(BizConstant::getBizTypeByMiguChannel).collect(Collectors.toSet());
    }

    /**
     * 查找前6个月需要校验的(注意:需要先手动将前6个月的包月状态1的置为-1表示这些需要重新校验)
     * 同时对1小时和日校验已经退订的,直接把包月状态置为0
     *
     * @param verifyDay
     * @return
     */
    public List<String> findLast6MonthToVerify(LocalDate verifyDay) {
        LocalDateTime start = verifyDay.atTime(LocalTime.MIN);
        LocalDateTime end = verifyDay.atTime(LocalTime.MAX);
        Integer nonMonthStatus = SUBSCRIBE_MONTH_VERIFY_NONE;
        return subscribeService.lambdaQuery().select(Subscribe::getId, Subscribe::getVerifyStatus, Subscribe::getVerifyStatusDaily)
                .between(Subscribe::getCreateTime, start, end)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .eq(Subscribe::getIsp, MobileRegionResult.ISP_YIDONG) //只验证移动的
                .in(Subscribe::getBizType, transLast6MonthToVerifyIncludeBizType())
                .and(i -> i.isNull(Subscribe::getPrice).or().eq(Subscribe::getPrice, SUBSCRIBE_MONTH_VERIFY_INIT))
                //.last("LIMIT 2000")
                .list()
                .stream()
                .filter(item -> {
                    //预处理,如果1小时或次日校验未包月,就视为未包月
                    boolean nonMonth = nonMonthStatus.equals(item.getVerifyStatus()) || nonMonthStatus.equals(item.getVerifyStatusDaily());
                    if (nonMonth) {
                        subscribeService.updateVerifyStatusDbAndEs(item.getId(),SUBSCRIBE_MONTH_VERIFY_NONE);
                    }
                    return !nonMonth;
                }).map(Subscribe::getId)
                .collect(Collectors.toList());
    }

    /**
     * 手动将前6个月的包月状态1的置为-1表示这些需要重新校验
     *
     * @return
     */
    public int resetLast6MonthToVerify() {

        LocalDateTime start = YearMonth.now().minusMonths(6L).atDay(1).atTime(LocalTime.MIN);
        LocalDateTime end = YearMonth.now().minusMonths(1L).atEndOfMonth().atTime(LocalTime.MAX);
        LambdaUpdateWrapper<Subscribe> lambdaUpdate = Wrappers.<Subscribe>lambdaUpdate()
                .between(Subscribe::getCreateTime, start, end)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .eq(Subscribe::getIsp, MobileRegionResult.ISP_YIDONG) //只验证移动的
                //.eq(Subscribe::getPrice, SUBSCRIBE_MONTH_VERIFY_EXISTS)  //全量统计,因为有一个月未同步到es,导致es不同步
                .eq(Subscribe::getPrice, SUBSCRIBE_MONTH_VERIFY_EXISTS)
                .in(Subscribe::getBizType, transLast6MonthToVerifyIncludeBizType())
                .set(Subscribe::getPrice, SUBSCRIBE_MONTH_VERIFY_INIT);

        return subscribeService.getBaseMapper().update(null, lambdaUpdate);
    }

    public void genSubStatistics() {
        for (String channelCode : COMBIN_CHANNEL_CODES) {
            genSubStatisticsByChannel(channelCode);
        }
    }

    private void genSubStatisticsByChannel(String channelCode) {
        log.info("生成渠道号统计数据开始:{}", channelCode);
        final YearMonth currYearMonth = YearMonth.now();
        //先判断当月是否已生成数据
        //List<EsSubStatistics> statisticsList = esSubStatisticsRepository.findByStatsMonthAndChannel(currYearMonth.toString(),channelCode);
        int count = esSubStatisticsRepository.countByStatsMonthAndChannel(currYearMonth.toString(), channelCode);
        if (count > 0) {
            log.info("生成渠道号统计数据已存在,跳过:{}", channelCode);
            return;
        }
        for (long i=1; i<=6; i++){
            final YearMonth targetYearMonth = currYearMonth.minusMonths(i);
            final String title = targetYearMonth.toString();
            //final List<SubscribeStatistics> monthStats = isVrbtDb ? subscribeMapper.statisticsByChannel(targetYearMonth.atDay(1).atTime(LocalTime.MIN),targetYearMonth.atEndOfMonth().atTime(LocalTime.MAX), channelCode) : memberCommonMapper.statisticsByChannel(targetYearMonth.atDay(1).atTime(LocalTime.MIN),targetYearMonth.atEndOfMonth().atTime(LocalTime.MAX), channelCode);
            final List<EsSubStatistics> subStatsList = esDataService.subscribeStatisticsLast6MonthByChannel(targetYearMonth.atDay(1),targetYearMonth.atEndOfMonth(),
                    channelCode);
            esSubStatisticsRepository.saveAll(subStatsList);
        }
        log.info("生成渠道号统计数据完成:{}", channelCode);
    }

    /**
     * 生成最近6个月存量数据报表
     *
     * @return
     */
    public void genLast6MonthVerifyReport() {
        //生成全部渠道号报表
        genAllChannelReport();

        for (String channelCode : COMBIN_CHANNEL_CODES) {
            genChannelReport(channelCode);
        }

        //生成特定渠道号报表（百度网盘会员库）
        //for (String channelCode:MEMBER_SIDE_CHANNEL_CODES) {
        //    genChannelReport(channelCode);
        //}

    }

    public void genChannelReport(String channelCode) {
        final YearMonth currYearMonth = YearMonth.now();
        final String excelFileName = "半年存量统计_按月_" + channelCode + "_分子渠道分省_截至" + currYearMonth.minusMonths(1L);
        log.info("生成渠道号报表开始:{}",excelFileName);
        List<Map<String, Object>> allChannelData = new ArrayList<>();
        for (long i=1; i<=6; i++){
            final String targetYearMonthStr = currYearMonth.minusMonths(i).toString();
            //final List<SubscribeStatistics> monthStats = isVrbtDb ? subscribeMapper.statisticsByChannel(targetYearMonth.atDay(1).atTime(LocalTime.MIN),targetYearMonth.atEndOfMonth().atTime(LocalTime.MAX), channelCode) : memberCommonMapper.statisticsByChannel(targetYearMonth.atDay(1).atTime(LocalTime.MIN),targetYearMonth.atEndOfMonth().atTime(LocalTime.MAX), channelCode);
            //final List<EsSubStatistics> monthStats = esDataService.subscribeStatisticsLast6MonthByChannel(targetYearMonth.atDay(1),targetYearMonth.atEndOfMonth(),channelCode);
            List<EsSubStatistics> statisticsList = esSubStatisticsRepository.findByStatsMonthAndSubMonthAndChannel(currYearMonth.toString(),targetYearMonthStr,channelCode);
            Map<String, Object> sheetDataMap = new HashMap<>();
            sheetDataMap.put("title",new ExportParams(null,targetYearMonthStr, ExcelType.XSSF));//表格title
            sheetDataMap.put("entity", EsSubStatistics.class);//表格对应实体
            sheetDataMap.put("data", statisticsList);
            allChannelData.add(sheetDataMap);
        }
        genExcelFile(allChannelData, excelFileName);
        log.info("生成渠道号报表完成:{}",excelFileName);
    }

    /**
     * 针对已经校验过的数据生成统计数据和excel报表
     * @param channelCode
     */
    public void genChannelSubStatisticsAndReport(String channelCode) {
        genSubStatisticsByChannel(channelCode);
        genChannelReport(channelCode);
    }



    public void genAllChannelReport() {
        final YearMonth currYearMonth = YearMonth.now();
        final String excelFileName = "半年存量统计_按月_截至" + currYearMonth.minusMonths(1L);
        log.info("生成全部渠道号报表开始:{}",excelFileName);
        List<Map<String, Object>> allChannelData = new ArrayList<>();
        for (long i=1; i<=6; i++){
            final YearMonth targetYearMonth = currYearMonth.minusMonths(i);
            final String title = targetYearMonth.toString();
            //final List<SubscribeStatistics> monthStats = subscribeMapper.statisticsAllChannel(targetYearMonth.atDay(1).atTime(LocalTime.MIN),targetYearMonth.atEndOfMonth().atTime(LocalTime.MAX));
            final List<EsSubStatistics> monthStats = esDataService.subscribeStatisticsLast6MonthAllChannel(targetYearMonth.atDay(1),targetYearMonth.atEndOfMonth());
            Map<String, Object> sheetDataMap = new HashMap<>();
            sheetDataMap.put("title",new ExportParams(null,title, ExcelType.XSSF));//表格title
            sheetDataMap.put("entity", EsSubStatistics.class);//表格对应实体
            sheetDataMap.put("data", monthStats);
            allChannelData.add(sheetDataMap);
        }
        genExcelFile(allChannelData, excelFileName);
        log.info("生成全部渠道号报表完成:{}",excelFileName);
    }

    private void genExcelFile(List<Map<String, Object>> allChannelData,String excelFileName) {
        String fileDir = SystemUtils.IS_OS_LINUX ? "/db/subscribe_statistics" : "F:/Download";
        String fileNameNoExt = FilenameUtils.concat(fileDir, excelFileName);
        final Workbook workbook = ExcelExportUtil.exportExcel(allChannelData,ExcelType.XSSF.name());
        //File outFile = new File("D:\\歌曲校验_精选内容_校验结果"+ (workbook instanceof HSSFWorkbook ?".xls" :".xlsx"));
        File outFile = new File(fileNameNoExt + (workbook instanceof HSSFWorkbook ?".xls" :".xlsx"));
        try (FileOutputStream fileOutputStream = new FileOutputStream(outFile)) {
            workbook.write(fileOutputStream);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 按天查找需要临时包月校验的数据
     * 同时对1小时和日校验已经退订的,直接把包月状态置为0
     *
     * @param verifyDay
     * @return
     */
    public List<String> findTempVerifyDataByDate(LocalDate verifyDay, String... channels) {
        LocalDateTime start = verifyDay.atTime(LocalTime.MIN);
        LocalDateTime end = verifyDay.atTime(LocalTime.MAX);
        Integer nonMonthStatus = SUBSCRIBE_MONTH_VERIFY_NONE;
        return subscribeService.lambdaQuery().select(Subscribe::getId, Subscribe::getVerifyStatus, Subscribe::getVerifyStatusDaily)
                .between(Subscribe::getCreateTime, start, end)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .eq(Subscribe::getIsp, MobileRegionResult.ISP_YIDONG) //只验证移动的
                .in(Subscribe::getChannel, channels)
                //.last("LIMIT 2000")
                .list()
                .stream()
                .filter(item -> {
                    //预处理,如果1小时或次日校验未包月,就视为未包月
                    boolean nonMonth = nonMonthStatus.equals(item.getVerifyStatus()) || nonMonthStatus.equals(item.getVerifyStatusDaily());
                    if (nonMonth) {
                        updateTempVerifyResult(item.getId(),SUBSCRIBE_MONTH_VERIFY_NONE);
                    }
                    return !nonMonth;
                }).map(Subscribe::getId)
                .collect(Collectors.toList());
    }

    /**
     * 将临时包月校验结果保存到ContentId字段
     * @param subscribeId
     * @param monthStatus
     */
    public void updateTempVerifyResult(String subscribeId, Integer monthStatus){
        subscribeService.lambdaUpdate().eq(Subscribe::getId,subscribeId).set(Subscribe::getContentId, String.valueOf(monthStatus)).update();
    }

    /**
     * 临时校验
     * @param subscribe
     */
    public void verifyTempData(Subscribe subscribe){
        //只核实移动业务
        if(subscribe==null|| !MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) {
            return;
        }
        int verifyStatus = this.monthVerify(subscribe);
        updateTempVerifyResult(subscribe.getId(),verifyStatus);
    }


    public static void main(String[] args) {
        System.out.println(DateUtils.datetimeFormat.get().format(BIZ_QYCL_VERIFY_START_DATE));
        System.out.println(YearMonth.now().toString());
    }


}
