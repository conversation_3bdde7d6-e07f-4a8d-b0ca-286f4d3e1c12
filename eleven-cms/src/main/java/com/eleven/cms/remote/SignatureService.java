package com.eleven.cms.remote;

import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * Author: <EMAIL>
 * Date: 2020/3/4 9:49
 * Desc:远程接口签名校验
 */
@Component
public class SignatureService {
    //@Autowired
    //private BizProperties bizProperties;

    //public boolean validate(Order order){
    //    String channelCode = order.getChannelCode();
    //    String timestamp = order.getTimestamp();
    //    String digest = signature(channelCode,timestamp);
    //
    //    return digest.equals(order.getSignature());
    //}

    public String signature(String channelCode,String timestamp,String signatureSecretKey) {

        byte[] input = (channelCode+timestamp+signatureSecretKey).getBytes(StandardCharsets.UTF_8);
        return DigestUtils.md5DigestAsHex(input);
    }

    public String sxhDigest(String timestamp,String signatureSecretKey) {
        byte[] input = (timestamp+signatureSecretKey).getBytes(StandardCharsets.UTF_8);
        return DigestUtils.md5DigestAsHex(input);
    }

}
