package com.eleven.cms.remote;

import com.eleven.cms.config.YuxunHunanDianxinMindProperties;
import com.eleven.cms.config.YuxunHunanDianxinProperties;
import com.eleven.cms.util.HunanDianxinMD5;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RSAEncrypt;
import com.eleven.cms.vo.YuxunHunanDianxinMindResult;
import com.eleven.cms.vo.YuxunHunanDianxinResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * 豫讯湖南电信智能业务类
 *
 * @author: cai lei
 * @create: 2022-07-07 10:29
 */

@Slf4j
@Service
public class YuxunHunanDianxinMindService {

    public static final String LOG_TAG = "豫讯湖南电信智能api";
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    @Autowired
    private YuxunHunanDianxinMindProperties yuxunHunanDianxinMindProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;

    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    //{"msg":"操作成功","code":0,"data":{"ret":"创建订单成功","orderNo":"20221221151039319925","URL":"http://servicemp.114zhan.cn/UnifiedOrder/OrderView?orderNo=20221221151039319925"}}
    @Cacheable(cacheNames = CacheConstant.CMS_MIND_ORDER_CACHE,key = "#root.methodName + '-' + #phone + '-' + #busProductType",unless = "#result==null")
    public YuxunHunanDianxinMindResult accpectOrder(String phone, String busProductType,String pagebackUrl) {

        pagebackUrl = StringUtils.contains(pagebackUrl,"hunan_dx_xms/res") ? pagebackUrl : yuxunHunanDianxinMindProperties.getPagebackUrl();
        String nonce = UUID.randomUUID().toString();

        log.info("{}-业务手受理接口,手机号:{},pagebackUrl:{}", LOG_TAG, phone, pagebackUrl);
        final HttpUrl httpUrl = HttpUrl.parse(yuxunHunanDianxinMindProperties.getCreateOrderUrl())
                .newBuilder()
                .addQueryParameter("nonce", nonce)
                .addQueryParameter("sign", generateSign(nonce))
                .addQueryParameter("userid", yuxunHunanDianxinMindProperties.getUserId())
                .build();
        
        RequestBody body = new FormBody.Builder()
                .add("phone", phone)
                .add("busProductType", busProductType)
                .add("pagebackUrl", pagebackUrl)
                .build();

        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-业务手受理接口,手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, YuxunHunanDianxinMindResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-业务手受理接口,手机号:{},响应,异常:", LOG_TAG, phone, e);
            return YuxunHunanDianxinMindResult.FAIL_RESULT;
        }

    }

    private String generateSign(String nonce) {
        String signStr = String.format("userid=%s&secretKey=%s&nonce=%s",yuxunHunanDianxinMindProperties.getUserId(),yuxunHunanDianxinMindProperties.getSecretKey(),nonce);
        return HunanDianxinMD5.md5(signStr);
    }
}