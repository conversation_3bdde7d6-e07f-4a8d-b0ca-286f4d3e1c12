package com.eleven.cms.remote;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.config.LiantongVrbtJunboProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.junbo.LiantongJunboUtils;
import com.eleven.cms.vo.LiantongJunboProductResp;
import com.eleven.cms.vo.LiantongJunboResp;
import com.eleven.cms.vo.LiantongSubedProductsResp;
import com.eleven.cms.vo.LiantongVrbtJunboProduct;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Author: lihb
 * Date: 2022-5-11 15:28:27
 * Desc: 骏伯联通沃音乐计费点相关api
 */
@Slf4j
@Service
public class LiantongVrbtJunboService {
    @Autowired
    private LiantongVrbtJunboProperties liantongVrbtJunboProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                    .compressed()
                    .insecure()  //不检查自签名证书
                    //.connectTimeout(120)
                    //.retry(5)
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println, options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 订阅接口
     *
     * @param phoneNo  手机号
     * @param orderSEQ 商户订单号
     * @return
     */
    public LiantongJunboResp subScribe(String phoneNo, String orderSEQ) {
        String content = null;
        Map<String, Object> map = new HashMap<>();
        long timestamp = new Date().getTime();

        map.put("phoneNo", phoneNo);
        map.put("orderSEQ", orderSEQ);
        map.put("redirectUrl", liantongVrbtJunboProperties.getRedirectUrl());
        map.put("productId", liantongVrbtJunboProperties.getProductId());
        map.put("timeStamp", timestamp);
        map.put("partnerNo", liantongVrbtJunboProperties.getPartnerNo());
        map.put("backUrl", liantongVrbtJunboProperties.getCallbackUrl());
        map.put("sign", LiantongJunboUtils.generateOrderSign(phoneNo, orderSEQ, timestamp));
        RequestBody body = RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtJunboProperties.getApiBaseUrl() + liantongVrbtJunboProperties.getOrderUrl())
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .post(body)
                .build();
        log.info("骏伯联通沃音乐计费点订阅-手机号:{},请求body: {}", phoneNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            content = response.body().string();
            log.info("骏伯联通沃音乐计费点订阅-手机号:{},响应: {}", phoneNo, content);
            return mapper.readValue(content, LiantongJunboResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("骏伯联通沃音乐计费点订阅出错-手机号:{},错误信息: {}", phoneNo, e.getMessage());
            return LiantongJunboResp.ERROR_RESP;
        }
    }

    /**
     * 退订验证码获取接口
     *
     * @param phoneNo 手机号
     * @return
     */
    public LiantongJunboResp verifyCode(String phoneNo) {
        Map<String, Object> map = new HashMap<>();

        map.put("phoneNo", phoneNo);
        map.put("productId", liantongVrbtJunboProperties.getProductId());
        map.put("partnerNo", liantongVrbtJunboProperties.getPartnerNo());
        map.put("sign", LiantongJunboUtils.generateverifyCodeSign(phoneNo));
        RequestBody body = RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtJunboProperties.getApiBaseUrl() + liantongVrbtJunboProperties.getVerifyCodeUrl())
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .post(body)
                .build();
        log.info("骏伯联通沃音乐计费点退订验证码获取接口-手机号:{},请求body: {}", phoneNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("骏伯联通沃音乐计费点退订验证码获取接口-手机号:{},响应: {}", phoneNo, content);
            return mapper.readValue(content, LiantongJunboResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("骏伯联通沃音乐计费点退订验证码获取接口-手机号:{},错误信息: {}", phoneNo, e.getMessage());
            return LiantongJunboResp.ERROR_RESP;
        }
    }

    /**
     * 退订接口
     *
     * @param phoneNo 手机号
     * @return
     */
    public LiantongJunboResp unsubscribe(String phoneNo, String verifyCode, String thirdOrderId) {
        Map<String, Object> map = new HashMap<>();
        map.put("phoneNo", phoneNo);
        map.put("productId", liantongVrbtJunboProperties.getProductId());
        map.put("thirdOrderId", thirdOrderId);
        map.put("partnerNo", liantongVrbtJunboProperties.getPartnerNo());
        map.put("verifyCode", verifyCode);
        map.put("sign", LiantongJunboUtils.generateUnsubscribeSign(phoneNo, thirdOrderId, verifyCode));
        RequestBody body = RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtJunboProperties.getApiBaseUrl() + liantongVrbtJunboProperties.getUnsubscribeUrl())
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .post(body)
                .build();
        log.info("骏伯联通沃音乐计费点退订接口-手机号:{},请求body: {}", phoneNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("骏伯联通沃音乐计费点退订接口-手机号:{},响应: {}", phoneNo, content);
            return mapper.readValue(content, LiantongJunboResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("骏伯联通沃音乐计费点退订接口-手机号:{},错误信息: {}", phoneNo, e.getMessage());
            return LiantongJunboResp.ERROR_RESP;
        }
    }

    /**
     * 订阅套餐查询接口
     *
     * @param phoneNo 手机号
     * @return
     */
    public LiantongJunboProductResp querySubInfo(String phoneNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("phoneNo", phoneNo);
        map.put("partnerNo", liantongVrbtJunboProperties.getPartnerNo());
        map.put("sign", LiantongJunboUtils.generateQuerySubInfoSign(phoneNo));
        RequestBody body = RequestBody.create(JSON, JSONObject.toJSONString(map));
        HttpUrl httpUrl = HttpUrl.parse(liantongVrbtJunboProperties.getApiBaseUrl() + liantongVrbtJunboProperties.getQuerySubInfoUrl())
                .newBuilder()
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .post(body)
                .build();
        log.info("骏伯联通沃音乐计费点订阅套餐查询接口-手机号:{},请求body: {}", phoneNo, JSONObject.toJSONString(map));
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("骏伯联通沃音乐计费点订阅套餐查询接口-手机号:{},响应: {}", phoneNo, content);
            return mapper.readValue(content, LiantongJunboProductResp.class);
        } catch (IOException e) {
            //e.printStackTrace();
            log.info("骏伯联通沃音乐计费点订阅套餐查询接口-手机号:{},错误信息: {}", phoneNo, e.getMessage());
            return LiantongJunboProductResp.ERROR_RESP;
        }
    }

    public boolean isSubedMon(String mobile) {
        LiantongJunboProductResp resp = this.querySubInfo(mobile);
        if (!resp.isOK()) {
            return false;
        }
        List<LiantongVrbtJunboProduct> subedProducts = resp.getResult();
        if (subedProducts == null) {
            return false;
        }
        String produtcId = liantongVrbtJunboProperties.getProductId();
        return subedProducts
                .stream()
                .anyMatch(item -> produtcId.equals(item.getProductId()) && "1".equals(item.getStatus()));
    }
}
