package com.eleven.cms.remote;

import com.eleven.cms.captcha.CaptchaService;
import com.eleven.cms.captcha.fateadm.Util;
import com.eleven.cms.config.ShandongHexiaoyuanProperties;
import com.eleven.cms.util.DesUtils;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ShandongHexiaoyuanResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @author: lihb
 * @create: 2024-3-14 15:02:01
 */
@Slf4j
@Service
public class ShandongHexiaoyuanService {

    public static final String LOG_TAG = "山东和校园业务api";
    @Autowired
    private ShandongHexiaoyuanProperties shandongHexiaoyuanProperties;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     *  获取图形验证码接口
     * @param phone 手机号
     * @return
     * @throws Exception
     */
    public Response getImgVerifyCode(String phone) {
        try {
            String url = shandongHexiaoyuanProperties.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url).newBuilder()
                    .addQueryParameter("action", "getImgVerifyCode")
                    .addQueryParameter("mobile", phone)
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .build();
            log.info("{}-获取图形验证码接口-手机号:{},请求:{}", LOG_TAG, phone, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
//            httpResponse.getOutputStream()
//                    .write(response.body().bytes());
//            httpResponse.getOutputStream()
//                    .flush();
//            httpResponse.getOutputStream()
//                    .close();
            return response;
        } catch (Exception e) {
            log.info("{}-获取图形验证码接口-手机号:{},渠道号:{},异常:", LOG_TAG, phone, e);
            return null;
        }
    }

    /**
     *  获取图形验证码接口
     * @param phone 手机号
     * @return
     * @throws Exception
     */
    public void getImgVerifyCodeForReporting(String phone, HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
        try {
            String url = shandongHexiaoyuanProperties.getBaseUrl();
            HttpUrl httpUrl = HttpUrl.parse(url).newBuilder()
                    .addQueryParameter("action", "getImgVerifyCode")
                    .addQueryParameter("mobile", phone)
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .build();
            log.info("{}-获取图形验证码接口-手机号:{},请求:{}", LOG_TAG, phone, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            httpResponse.getOutputStream()
                    .write(response.body().bytes());
            httpResponse.getOutputStream()
                    .flush();
            httpResponse.getOutputStream()
                    .close();
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},渠道号:{},异常:", LOG_TAG, phone, e);
        }
    }

    /**
     * 短信验证码下发
     *
     * @param phone
     * @return
     */
    public ShandongHexiaoyuanResult getSms(String phone, String imgCode) {

        try{
            String url = shandongHexiaoyuanProperties.getBaseUrl();
            String key = shandongHexiaoyuanProperties.getSecretKey();
            String bCode = shandongHexiaoyuanProperties.getBCode();
            HttpUrl httpUrl = HttpUrl.parse(url).newBuilder()
                    .addQueryParameter("action", "getCode")
                    .addQueryParameter("mobile", DesUtils.encryptByte(phone.getBytes(),key))
                    .addQueryParameter("bCode", DesUtils.encryptByte(bCode.getBytes(),key))
                    .addQueryParameter("imgCode", imgCode)
                    .build();

            log.info("{}-短信验证码下发-手机号:{},图形验证码:{},请求:{}", LOG_TAG,phone,imgCode, httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-短信验证码下发-手机号:{},图形验证码:{},响应:{}", LOG_TAG, phone,imgCode, content);
                ShandongHexiaoyuanResult shandongHexiaoyuanResult = mapper.readValue(content, ShandongHexiaoyuanResult.class);
                return shandongHexiaoyuanResult;
            } catch (Exception e) {
                log.info("{}-短信验证码下发-手机号:{},图形验证码:{},异常:", LOG_TAG, phone,imgCode, e);
                return ShandongHexiaoyuanResult.fail();
            }
        }catch (Exception e){
            log.info("{}-短信验证码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return ShandongHexiaoyuanResult.fail();
        }
    }

    /**
     * 提交短信验证码
     *
     * @param phone
     * @return
     */
    public ShandongHexiaoyuanResult smsCode(String phone, String smsCode) {

        try{
            String url = shandongHexiaoyuanProperties.getBaseUrl();
            String key = shandongHexiaoyuanProperties.getSecretKey();
            String bCode = shandongHexiaoyuanProperties.getBCode();
            HttpUrl httpUrl = HttpUrl.parse(url).newBuilder()
                    .addQueryParameter("action", "orderCPToThird")
                    .addQueryParameter("phone", DesUtils.encryptByte(phone.getBytes(),key))
                    .addQueryParameter("authCode", DesUtils.encryptByte(smsCode.getBytes(),key))
                    .addQueryParameter("bCode", DesUtils.encryptByte(bCode.getBytes(),key))
                    .build();

            log.info("{}-提交短信验证码-手机号:{},短信验证码:{},请求:{}", LOG_TAG,phone,smsCode, httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-提交短信验证码-手机号:{},短信验证码:{},响应:{}", LOG_TAG, phone,smsCode, content);
                ShandongHexiaoyuanResult shandongHexiaoyuanResult = mapper.readValue(content, ShandongHexiaoyuanResult.class);
                return shandongHexiaoyuanResult;
            } catch (Exception e) {
                log.info("{}-提交短信验证码-手机号:{},异常:", LOG_TAG, phone, e);
                return ShandongHexiaoyuanResult.fail();
            }
        }catch (Exception e){
            log.info("{}-提交短信验证码-手机号:{},异常:", LOG_TAG, phone, e);
            return ShandongHexiaoyuanResult.fail();
        }
    }

    /**
     * 短信验证码下发
     *
     * @param phone
     * @return
     */
    public ShandongHexiaoyuanResult getImgVerifyCodeAndSms(String phone) {

        try{
            //获取图形验证码
            Response  imgVerifyCodeResponse = getImgVerifyCode(phone);
            if(imgVerifyCodeResponse == null){
                return ShandongHexiaoyuanResult.fail();
            }
            //解析图形验证码
            final Util.HttpResp predict = captchaService.Predict(CaptchaService.PRED_TYPE_SHANDONG_HEXIAOYUAN, imgVerifyCodeResponse.body().bytes(), CaptchaService.SRC_URL_SHANDONG_HEXIAOYUAN);
            if(!predict.isOK()){
                return ShandongHexiaoyuanResult.fail();
            }
            String url = shandongHexiaoyuanProperties.getBaseUrl();
            String key = shandongHexiaoyuanProperties.getSecretKey();
            String bCode = shandongHexiaoyuanProperties.getBCode();
            HttpUrl httpUrl = HttpUrl.parse(url).newBuilder()
                    .addQueryParameter("action", "getCode")
                    .addQueryParameter("mobile", DesUtils.encryptByte(phone.getBytes(),key))
                    .addQueryParameter("bCode", DesUtils.encryptByte(bCode.getBytes(),key))
                    .addQueryParameter("imgCode", predict.pred_resl)
                    .build();

            log.info("{}-短信验证码下发-手机号:{},图形验证码:{},请求:{}", LOG_TAG,phone,predict.pred_resl, httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-短信验证码下发-手机号:{},图形验证码:{},响应:{}", LOG_TAG, phone,predict.pred_resl, content);
                ShandongHexiaoyuanResult shandongHexiaoyuanResult = mapper.readValue(content, ShandongHexiaoyuanResult.class);
                return shandongHexiaoyuanResult;
            } catch (Exception e) {
                log.info("{}-短信验证码下发-手机号:{},图形验证码:{},异常:", LOG_TAG, phone,predict.pred_resl, e);
                return ShandongHexiaoyuanResult.fail();
            }
        }catch (Exception e){
            log.info("{}-短信验证码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return ShandongHexiaoyuanResult.fail();
        }
    }

}
