package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.ad.HuaYiApiProperties;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.dto.HuaYiResp;
import com.eleven.cms.dto.JunboResp;
import com.eleven.cms.dto.JunboRespon;
import com.eleven.cms.dto.JunboResult;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.junbo.Sender;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * Author: <EMAIL>
 * Date: 2020/6/17 14:26
 * Desc:骏伯api封装
 */
@Slf4j
@Service
public class JunboApiService {

    @Autowired
    private JunboApiProperties junboApiProperties;
    @Autowired
    private HuaYiApiProperties huaYiApiProperties;
    @Autowired
    RechargeAlertService rechargeAlertService;
    @Autowired
    private Environment environment;

    private OkHttpClient client;
    private ObjectMapper mapper;
    //private DateTimeFormatter dateTimeFormatter;
    private static final MediaType mediaType = MediaType.parse("application/json");
    @PostConstruct
    public void init() {

        this.client = OkHttpClientUtils.getNewInstance();

        if (Arrays.stream(environment.getActiveProfiles()).anyMatch(profile -> (profile.contains("dev")||profile.contains("test")))) {
            //this.client = this.client.newBuilder()
            //                         .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 10086)))
            //                         //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("localhost", 8888)))
            //                         .build();

            final Options options = Options.builder()
                                           .compressed()
                                           .insecure()  //不检查自签名证书
                                           //.connectTimeout(120)
                                           //.retry(5)
                                           .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println,options)).build();
        }

        this.mapper = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN);
    }


    private String requestApi(String apiUrl,JsonNode rcNode) throws Exception {
        final ObjectNode rootNode = mapper.createObjectNode();
        rootNode.put("T",System.currentTimeMillis());
        rootNode.put("UId",junboApiProperties.getUid());
        rootNode.put("F",IdWorker.getIdStr());
        rootNode.putPOJO("RC", rcNode);
        String jsonBody = rootNode.toString();
        log.info("骏伯api请求=>url:{},reqJson:{}",apiUrl,jsonBody);
        final String version = junboApiProperties.getVersion();
        String raw = version + jsonBody;
        //byte[] aseKeyBytes = DigestUtils.sha256(raw);
        Sender sender = new Sender();
        final Pair<String, String> pair = sender.sendByPrive(junboApiProperties.getRsaPrivateKeyPkcs8(), version, jsonBody);
        //Form表单格式的参数传递
        FormBody formBody = new FormBody
                .Builder()
                .add("V", version)//版本号
                .add("K",pair.getLeft())//加密后的AESKey
                .add("C",pair.getRight())//加密后的业务内容
                .build();
        Request request = new Request.Builder().url(apiUrl)
                                               .post(formBody)
                                               .build();
        //String urlWithParam = apiUrl+"?"+senderTxt;
        //System.out.println("urlWithParam = " + urlWithParam);
        //Request request = new Request.Builder().url(urlWithParam).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException(" Unexpected response code " + response);
            }
            String responseBody = response.body().string();
            log.info("骏伯api响应=>url:{},respJson:{}",apiUrl,responseBody);

            return sender.aesDecrypt(responseBody);

        } catch (Exception e) {
            log.info("骏伯api请求异常",e);
            throw e;
        }
    }

    /**
     * 多会员充值
     * @param couponId 产品编码
     * @param orderSEQ 我方订单号(唯一)
     * @param couponId account 充值账号
     * @param couponId mobile 通知手机号(非必传)
     * @return  {"T":*************,"S":"100","F":"1349270325406330882","C":{"code":"100","msg":"接收成功","orderId":"20210113162129294300014"}}
     * {"T":*************,"S":"100","F":"1349273995275464705","C":{"code":"1025","msg":"充值号码充值次数超过当日限制次数","orderId":null}}
     */
    public JunboResult rechargeVIP(String couponId, String orderSEQ, String account, String mobile,String serviceId,String packName){

        final ObjectNode rcNode = mapper.createObjectNode();
        rcNode.put("orderSEQ", orderSEQ);
        rcNode.put("account", account);
        rcNode.put("mobile", account); //手机号同账号
        rcNode.put("couponId", couponId);
        rcNode.put("partnerNo", junboApiProperties.getPartnerNo());
        rcNode.put("backUrl", junboApiProperties.getCallbackUrl());
        try {
            final String rechargeVipUrl = junboApiProperties.getRechargeVipUrl();
            final String data = requestApi(rechargeVipUrl, rcNode);
            log.info("骏伯api响应=>url:{},解密后数据:{}",rechargeVipUrl,data);
            final JunboResp junboResp = mapper.readValue(data, JunboResp.class);

            //骏伯充值告警
            if(junboResp!=null && junboResp.getJunboResult()!=null && "100".equals(junboResp.getJunboResult().getCode())){
                rechargeAlertService.watchJUNBORecharge(mobile,serviceId,packName);
            }

            return junboResp.getJunboResult();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 多会员订单查询
     * @param orderSEQ 我方订单号(唯一)
     * @return  {"T":1610601411231,"S":"100","F":"1349586247278944258","C":{"orderSEQ":"9d840bde26fa4e1288d637ea45c03482","code":"1111","msg":"失败"}}
     */
    public JunboResult orderQuery(String orderSEQ){
        final ObjectNode rcNode = mapper.createObjectNode();
        rcNode.put("orderSEQ", orderSEQ);
        try {
            final String orderQueryUrl = junboApiProperties.getOrderQueryUrl();
            final String data = requestApi(orderQueryUrl, rcNode);
            log.info("骏伯api响应=>url:{},解密后数据:{}",orderQueryUrl,data);
            final JunboResp junboResp = mapper.readValue(data, JunboResp.class);
            return junboResp.getJunboResult();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }




    /**
     * 多会员充值
     * @param couponId 产品编码
     * @param mobile 通知手机号(非必传)
     * @return  //状态码，返回000000为充值成功，000005需要等待我方回传，其它为异常状态
     */
    public HuaYiResp rechargeVIPHuaYi(String couponId,String mobile, String serviceId, String packName){
        final ObjectNode rcNode = mapper.createObjectNode();
        rcNode.put("channelCode", huaYiApiProperties.getChannelCode());
        rcNode.put("phone", mobile);
        rcNode.put("cardType", couponId);
        String sign = DigestUtils.md5DigestAsHex((couponId + huaYiApiProperties.getChannelCode() + mobile +huaYiApiProperties.getKey()).getBytes(StandardCharsets.UTF_8));
        rcNode.put("sign",sign);
        rcNode.put("notifyUrl", huaYiApiProperties.getCallbackUrl());
        StringBuffer valueStr = new StringBuffer();
        valueStr.append(huaYiApiProperties.getRechargeVipUrl());
        valueStr.append("?channelCode=" + huaYiApiProperties.getChannelCode());
        valueStr.append("&phone=" + mobile);
        valueStr.append("&cardType=" + couponId);
        valueStr.append("&notifyUrl=" +  huaYiApiProperties.getCallbackUrl());
        valueStr.append("&sign=" +  sign);
        try {
            String content=pushGet(valueStr.toString(),"华逸权益充值接口");
            final HuaYiResp huaYiResp = mapper.readValue(content, HuaYiResp.class);
            //充值告警
            if(huaYiResp!=null && ("000005".equals(huaYiResp.getCode()) || "000005".equals(huaYiResp.getCode()))){
                rechargeAlertService.watchHuaYiRecharge(mobile,serviceId,packName);
            }
            return huaYiResp;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("华逸权益充值,请求异常=>手机号:{},权益ID:{},业务ID:{},业务类型:{}",mobile,couponId,serviceId,packName,e);
            return new HuaYiResp("500","系统异常","");
        }
    }


    private String pushGet(String url,String msg) {
        log.info(msg+",请求数据=>地址:{}",url);
        Request request = new Request.Builder().url(url).get().build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},响应参数:{}",url,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{}",url,e);
            return null;
        }
    }


    /**
     * 多会员充值
     * @param couponId 产品编码
     * @param orderSEQ 我方订单号(唯一)
     * @param couponId account 充值账号
     * @param couponId mobile 通知手机号(非必传)
     * @return  {"T":*************,"S":"100","F":"1349270325406330882","C":{"code":"100","msg":"接收成功","orderId":"20210113162129294300014"}}
     * {"T":*************,"S":"100","F":"1349273995275464705","C":{"code":"1025","msg":"充值号码充值次数超过当日限制次数","orderId":null}}
     */
    public JunboRespon rechargeVIPJunBoMD5(String couponId, String orderSEQ, String account, String mobile,String serviceId,String packName){
        final ObjectNode rcNode = mapper.createObjectNode();
        rcNode.put("orderSEQ", orderSEQ);
        rcNode.put("phoneNo", account); //手机号同账号
        rcNode.put("couponId", couponId);
        rcNode.put("partnerNo", junboApiProperties.getPartnerNo());
        rcNode.put("backUrl", junboApiProperties.getCallbackUrl());
        String sign = DigestUtils.md5DigestAsHex(("orderSEQ" + orderSEQ + "phoneNo" + account + "couponId" + couponId + "partnerNo" + junboApiProperties.getPartnerNo() + "key" + junboApiProperties.getKey()).getBytes(StandardCharsets.UTF_8));
        rcNode.put("sign", sign);
        rcNode.put("desc", "desc"); //手机号同账号
        log.info("骏伯三方权益充值MD5加密-请求参数:{}",rcNode);
        RequestBody body = RequestBody.create(mediaType, rcNode.toString());
        Request request = new Request.Builder().url(junboApiProperties.getRechargeVipUrl()).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("骏伯三方权益充值MD5加密-请求参数:{},响应参数:{}",rcNode,content);
            final JunboRespon junboResp = mapper.readValue(content, JunboRespon.class);
            if(junboResp!=null && junboResp.isOK()){
                rechargeAlertService.watchJUNBORecharge(mobile,serviceId,packName);
            }
            return junboResp;
        } catch (Exception e) {
            log.info("骏伯三方权益充值MD5加密-请求异常:{},响应参数:{}",rcNode,e);
            return JunboRespon.FAIL_RESPON;
        }
    }
}
