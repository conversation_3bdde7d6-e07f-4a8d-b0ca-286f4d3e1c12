package com.eleven.cms.remote;

import com.eleven.cms.config.HebeiYidongVrbtConfig;
import com.eleven.cms.config.HebeiYidongVrbtProperties;
import com.eleven.cms.config.ShandongDianxinProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HebeiMobileResult;
import com.eleven.cms.vo.ShandongDianxinResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 河北移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class ShandongDianxinService {

    private static final String LOG_TAG = "山东电信API";


    @Autowired
    private Environment environment;
    @Autowired
    private ShandongDianxinProperties shandongDianxinProperties;


    private OkHttpClient client;
    private ObjectMapper mapper;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }



    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    ShandongDianxinResult getSms(String phone, String ip) {
        FormBody.Builder build = new FormBody.Builder()
                .add("phone", phone)
                .add("ip", ip);
        FormBody body = build.build();
        Request request = new Request.Builder()
                .url(shandongDianxinProperties.getGetSmsUrl())
                .post(body)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, ShandongDianxinResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return ShandongDianxinResult.fail();
        }
    }


   // {"code":0,"msg":"办理成功","data":{"res_code":"0","res_desc":"Processing the request succeeded!","result":{"TRADEFORMNUM":"311220217268806262","DETAILINFO":[],"retInnerMsg":"Processing the request succeeded!"}}}
    // 0成功 -1失败
    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    ShandongDianxinResult smsCode(String phone, String smsCode,String ip) {
        FormBody.Builder build = new FormBody.Builder()
                .add("phone", phone)
                .add("code", smsCode)
                .add("pid", shandongDianxinProperties.getPid())
                .add("channel", shandongDianxinProperties.getChannel())
                .add("url", shandongDianxinProperties.getUrl())
                .add("ip", ip);
        FormBody body = build.build();
//        busCode	是	int	业务编码 14163
//        p_id	是	int	13
        Request request = new Request.Builder()
                .url(shandongDianxinProperties.getSendSmsUrl())
                .post(body)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},请求:{}", LOG_TAG, phone, smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},响应:{}", LOG_TAG, phone, smsCode, content);
            return mapper.readValue(content, ShandongDianxinResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},异常:", LOG_TAG, phone, smsCode, e);
            return ShandongDianxinResult.fail();
        }
    }
}
