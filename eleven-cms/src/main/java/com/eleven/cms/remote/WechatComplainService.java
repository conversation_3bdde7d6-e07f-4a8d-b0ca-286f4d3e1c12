package com.eleven.cms.remote;


import com.eleven.cms.config.WeChatConfig;
import com.eleven.cms.config.WechatComplainProperties;
import com.eleven.cms.dto.WechatComplainDetail;
import com.eleven.cms.dto.WechatComplainNotify;
import com.eleven.cms.dto.WechatComplainResponse;
import com.eleven.cms.entity.WechatComplain;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.qycl.entity.QyclOrderPay;
import com.eleven.qycl.service.IQyclOrderPayService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.PrivateKey;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *  咪咕音乐白金联合会员平台调用接口
 */
@Slf4j
@Service
public class WechatComplainService {
    @Autowired
    private IWechatComplainService wechatComplainService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    @Autowired
    private WechatComplainProperties wechatComplainProperties;
    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    private IQyclOrderPayService qyclOrderPayService;
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private IWarnMobileService warnMobileService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);

    /**
     * 查询投诉详细
     * @param complaintId
     * @param actionType
     * @param mchId
     * @return
     */
    @Async
    public Result<?> wechatQueryComplainDetail(String complaintId, String actionType, String mchId,WechatComplainNotify wechatComplainNotify){
        return wechatQueryComplaiDetailMethod(complaintId, actionType, mchId,wechatComplainNotify);
    }

    public Result<?> wechatQueryComplainDetail(String complaintId,String mchId){
        return wechatQueryComplaiDetailMethod(complaintId, null, mchId,null);
    }

    private Result<Object> wechatQueryComplaiDetailMethod(String complaintId, String actionType, String mchId,WechatComplainNotify wechatComplainNotify) {
        FebsResponse response=qyclWxpayService.wechatQueryComplainDetail(complaintId,mchId);
        WechatComplain wechatComplain=wechatComplainService.lambdaQuery().eq(WechatComplain::getComplaintId, complaintId).orderByDesc(WechatComplain::getCreateTime).last("limit 1").one();
        if(wechatComplain==null){
            wechatComplain=new WechatComplain();
        }
        if(response.isOK()){
            WechatComplainDetail wechatComplainDetail =(WechatComplainDetail)response.get("data");
            /**微信投诉单号*/
            wechatComplain.setComplaintId(wechatComplainDetail.getComplaintId());
            /**投诉单状态 PENDING：待处理 PROCESSING：处理中 PROCESSED：已处理完成*/
            wechatComplain.setComplainStatus(wechatComplainDetail.getComplaintState());
            /**投诉的具体描述*/
            wechatComplain.setComplaintDetail(wechatComplainDetail.getComplaintDetail());
            /**投诉时间*/
            wechatComplain.setComplaintTime(wechatComplainDetail.getComplaintTime());
            /**商户号*/
            wechatComplain.setComplaintedMchid(mchId);
            /**投诉人联系方式*/ //需要解密
            try {
                if(StringUtils.isNotBlank(wechatComplainDetail.getPayerPhone())){
                    wechatComplain.setPayerPhone(rsaDecryptOAEP(wechatComplainDetail.getPayerPhone(), WeChatConfig.getPriKeyByP12(mchId)));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            /**投诉人openid*/
            wechatComplain.setPayerOpenid(wechatComplainDetail.getPayerOpenid());
            /**投诉单是否已全额退款:0=否,1=是*/
            wechatComplain.setComplaintFullRefunded(wechatComplainDetail.getComplaintFullRefunded()?1:0);
            /**是否有待回复的用户留言:0=否,1=是*/
            wechatComplain.setIncomingUserResponse(wechatComplainDetail.getIncomingUserResponse()?1:0);
            /**问题描述*/
            wechatComplain.setProblemDescription(wechatComplainDetail.getProblemDescription());
            /**用户投诉次数*/
            wechatComplain.setUserComplaintTimes(wechatComplainDetail.getUserComplaintTimes());
            /**问题类型 REFUND：申请退款 SERVICE_NOTWORK：服务权益未生效 OTHERS：其他类型*/
            wechatComplain.setProblemType(wechatComplainDetail.getProblemType());
            /**申请退款金额*/
            if(wechatComplainDetail.getApplyRefundAmount()!=null){
                String applyRefundAmount = new BigDecimal(wechatComplainDetail.getApplyRefundAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
                wechatComplain.setApplyRefundAmount(applyRefundAmount);
            }


            try {
                if(wechatComplainDetail.getComplaintMediaList()!=null){
                    String complaintMediaList = mapper.writeValueAsString(wechatComplainDetail.getComplaintMediaList());
                    wechatComplain.setComplaintMediaList(complaintMediaList);
                }

                if(wechatComplainDetail.getComplaintOrderInfo()!=null){
                    String complaintOrderInfo = mapper.writeValueAsString(wechatComplainDetail.getComplaintOrderInfo());
                    wechatComplain.setComplaintOrderInfo(complaintOrderInfo);
                }


                if(wechatComplainDetail.getServiceOrderInfo()!=null){
                    String serviceOrderInfo = mapper.writeValueAsString(wechatComplainDetail.getServiceOrderInfo());
                    wechatComplain.setServiceOrderInfo(serviceOrderInfo);
                }


                if(wechatComplainDetail.getAdditionalInfo()!=null){
                    String additionalInfo = mapper.writeValueAsString(wechatComplainDetail.getAdditionalInfo());
                    wechatComplain.setAdditionalInfo(additionalInfo);
                }

            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }


            List<WechatComplainDetail.ComplaintOrderInfo> complaintOrderInfo=wechatComplainDetail.getComplaintOrderInfo();
            if(complaintOrderInfo!=null && complaintOrderInfo.size()>0) {
                /**微信订单号*/
                wechatComplain.setTransactionId(complaintOrderInfo.get(0).getTransactionId());
                /**商户订单号*/
                wechatComplain.setOutTradeNo(complaintOrderInfo.get(0).getOutTradeNo());
                /**订单金额*/
                if(complaintOrderInfo.get(0).getAmount()!=null){
                    String amount = new BigDecimal(complaintOrderInfo.get(0).getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();//分转元
                    wechatComplain.setAmount(amount);
                }

            }
        }
        if(wechatComplainNotify!=null){
            /**通知类型 COMPLAINT.CREATE：产生新投诉 COMPLAINT.STATE_CHANGE：投诉状态变化*/
            wechatComplain.setEventType(wechatComplainNotify.getEventType());

            /**通知的资源数据类型，支付成功通知为encrypt-resource*/
            wechatComplain.setResourceType(wechatComplainNotify.getResourceType());
            /**回调摘要*/
            wechatComplain.setSummary(wechatComplainNotify.getSummary());
        }
        if(StringUtils.isNotBlank(actionType)){
            /**动作类型 CREATE_COMPLAINT：用户提交投诉 CONTINUE_COMPLAINT：用户继续投诉 USER_RESPONSE：用户新留言 RESPONSE_BY_PLATFORM：平台新留言 SELLER_REFUND：商户发起全额退款 MERCHANT_RESPONSE：商户新回复 MERCHANT_CONFIRM_COMPLETE：商户反馈处理完成 MERCHANT_APPROVE_REFUND：商户同意退款 MERCHANT_REJECT_REFUND：商户驳回退款 REFUND_SUCCESS：退款到账*/
            wechatComplain.setActionType(actionType);
        }
        /**备注*/
        wechatComplain.setComplainRemark("投诉明细==>{"+response.get("code")+":"+response.get("message")+"}");
        /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/

        QyclOrderPay order = qyclOrderPayService.lambdaQuery().eq(QyclOrderPay::getId, wechatComplain.getOutTradeNo()).orderByDesc(QyclOrderPay::getCreateTime).last("limit 1").one();;
        if(order!=null){
            wechatComplain.setRefundStatus(order.getRefundStatus());
        }
        wechatComplain.setUpdateTime(new Date());
        if(wechatComplain.getId()==null){
            wechatComplainService.save(wechatComplain);
            //待处理 商户号正常发送提醒短信
            if(mchId!=null && wechatComplainNotify!=null && StringUtils.equals("COMPLAINT.CREATE",wechatComplainNotify.getEventType())){
                smsNotify(mchId);
            }
            return Result.ok("投诉明细查询成功！",wechatComplain);
        }
        wechatComplainService.lambdaUpdate().eq(WechatComplain::getComplaintId, complaintId).update(wechatComplain);
        return Result.ok("投诉明细查询成功！",wechatComplain);
    }

    public void smsNotify(String mchId) {
        String msgContent = wechatComplainProperties.getNotifySms() + ",商户号:"+mchId;
//        wechatComplainProperties.getNotifyMobileList().forEach(mobile->{
//            datangSmsService.sendSms(mobile, msgContent);
//        });
        warnMobileService.smsWarn("2", BizConstant.BIZ_TYPE_QYCL).forEach(mobile->{
            datangSmsService.sendSms(mobile, msgContent);
        });
    }

    public Result<?> wechatSolveComplain(String complaintId, String mchId, String responseContent, String responseImages, String jumpUrl, String jumpUrlText) {
        FebsResponse febsResponse=qyclWxpayService.wechatSolveComplain( complaintId,  mchId,  responseContent,  responseImages,  jumpUrl,  jumpUrlText);
        if(febsResponse.isOK()){
           return Result.ok();
        }
        return Result.error(String.valueOf(febsResponse.get("message")));
    }
    public String rsaDecryptOAEP(String ciphertext, PrivateKey privateKey){
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-1AndMGF1Padding");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] data = Base64.getDecoder().decode(ciphertext);
            return new String(cipher.doFinal(data), "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public FebsResponse queryComplainList(Integer limit, Integer offset, String beginDate, String endDate) {
        List<WechatConfigLog> wechatList=wechatConfigLogService.lambdaQuery().isNotNull(WechatConfigLog::getMchSerialNo).groupBy(WechatConfigLog::getMchId).orderByDesc(WechatConfigLog::getCreateTime).list();
        for (int i = 0; i < wechatList.size(); i++) {
            FebsResponse response=qyclWxpayService.queryComplainList(limit,  offset,  wechatList.get(i).getMchId(),  beginDate,  endDate);
            if(response.isOK()){
                WechatComplainResponse wechatComplainResponse =(WechatComplainResponse)response.get("data");
                if(wechatComplainResponse.getTotalCount()!=null){
                    Double count=Double.valueOf(wechatComplainResponse.getTotalCount())/limit;
                    if(count>offset){
                        offset=offset+limit;
                        List<WechatComplainResponse.Data> dataList=wechatComplainResponse.getData();
                        if(dataList!=null && dataList.size()>0){
                            for (int k = 0; k < dataList.size(); k++) {
                                WechatComplain wechatComplain=wechatComplainService.lambdaQuery().eq(WechatComplain::getComplaintId,dataList.get(k).getComplaintId()).orderByDesc(WechatComplain::getCreateTime).last("limit 1").one();
                                WechatComplainResponse.Data wechatComplainDetail=dataList.get(k);
                                if(wechatComplain==null){
                                    wechatComplain=new WechatComplain();
                                }
                                /**微信投诉单号*/
                                wechatComplain.setComplaintId(wechatComplainDetail.getComplaintId());
                                /**投诉单状态 PENDING：待处理 PROCESSING：处理中 PROCESSED：已处理完成*/
                                wechatComplain.setComplainStatus(wechatComplainDetail.getComplaintState());
                                /**投诉的具体描述*/
                                wechatComplain.setComplaintDetail(wechatComplainDetail.getComplaintDetail());
                                /**投诉时间*/
                                wechatComplain.setComplaintTime(wechatComplainDetail.getComplaintTime());
                                /**商户号*/
                                wechatComplain.setComplaintedMchid(wechatList.get(i).getMchId());
                                /**投诉人联系方式*/ //需要解密
                                try {
                                    if(StringUtils.isNotBlank(wechatComplainDetail.getPayerPhone())){
                                        wechatComplain.setPayerPhone(rsaDecryptOAEP(wechatComplainDetail.getPayerPhone(), WeChatConfig.getPriKeyByP12(wechatList.get(i).getMchId())));
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                /**投诉单是否已全额退款:0=否,1=是*/
                                wechatComplain.setComplaintFullRefunded(wechatComplainDetail.getComplaintFullRefunded()?1:0);
                                /**是否有待回复的用户留言:0=否,1=是*/
                                wechatComplain.setIncomingUserResponse(wechatComplainDetail.getIncomingUserResponse()?1:0);
                                /**问题描述*/
                                wechatComplain.setProblemDescription(wechatComplainDetail.getProblemDescription());
                                /**用户投诉次数*/
                                wechatComplain.setUserComplaintTimes(wechatComplainDetail.getUserComplaintTimes());
                                /**问题类型 REFUND：申请退款 SERVICE_NOTWORK：服务权益未生效 OTHERS：其他类型*/
                                wechatComplain.setProblemType(wechatComplainDetail.getProblemType());
                                /**申请退款金额*/
                                if(wechatComplainDetail.getApplyRefundAmount()!=null){
                                    String applyRefundAmount = new BigDecimal(wechatComplainDetail.getApplyRefundAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
                                    wechatComplain.setApplyRefundAmount(applyRefundAmount);
                                }

                                List<WechatComplainResponse.Data.ComplaintOrderInfo> complaintOrderInfo=wechatComplainDetail.getComplaintOrderInfo();
                                if(complaintOrderInfo!=null && complaintOrderInfo.size()>0) {
                                    /**微信订单号*/
                                    wechatComplain.setTransactionId(complaintOrderInfo.get(0).getTransactionId());
                                    /**商户订单号*/
                                    wechatComplain.setOutTradeNo(complaintOrderInfo.get(0).getOutTradeNo());
                                    /**订单金额*/
                                    if(complaintOrderInfo.get(0).getAmount()!=null){
                                        String amount = new BigDecimal(complaintOrderInfo.get(0).getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();//分转元
                                        wechatComplain.setAmount(amount);
                                    }

                                }

                                /**备注*/
                                wechatComplain.setComplainRemark("投诉明细==>{"+response.get("code")+":"+response.get("message")+"}");
                                /**退款状态:-1=退款中,0=未退款,1=退款成功,2=退款失败*/

                                QyclOrderPay order = qyclOrderPayService.lambdaQuery().eq(QyclOrderPay::getId, wechatComplain.getOutTradeNo()).orderByDesc(QyclOrderPay::getCreateTime).last("limit 1").one();
                                if(order!=null){
                                    wechatComplain.setRefundStatus(order.getRefundStatus());
                                }
                                wechatComplain.setUpdateTime(new Date());
                                if(wechatComplain.getId()==null){
                                    wechatComplainService.save(wechatComplain);
                                }else{
                                    wechatComplainService.lambdaUpdate().eq(WechatComplain::getComplaintId, dataList.get(k).getComplaintId()).update(wechatComplain);
                                }
                            }
                        }
                        return queryComplainList( limit,  offset,  beginDate,  endDate) ;
                    }
                }

            }
        }
        return new FebsResponse().success();
    }
    public Result<?> feedbackOverComplain(String complaintId, String mchId) {
        FebsResponse febsResponse=qyclWxpayService.feedbackOverComplain( complaintId,  mchId);
        if(febsResponse.isOK()){
            return Result.ok();
        }
        return Result.error(String.valueOf(febsResponse.get("message")));
    }
}
