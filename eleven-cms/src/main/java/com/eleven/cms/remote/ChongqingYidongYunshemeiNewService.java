package com.eleven.cms.remote;

import com.eleven.cms.config.ChongqingYunshemeiProperties;
import com.eleven.cms.config.HainanYunshemeiNewConfig;
import com.eleven.cms.config.HainanYunshemeiNewProperties;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.HainanYunshemeiNewResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

/**
 * 海南移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class ChongqingYidongYunshemeiNewService {

    public static final String LOG_TAG = "云摄美重庆移动new-api";


    @Autowired
    private Environment environment;
    @Autowired
    private ChongqingYunshemeiProperties chongqingYunshemeiProperties;

    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
//            this.client = this.client.newBuilder()
//                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
//                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }

    /**
     * 营销活动校验与短信验证码下发接口
     *
     * @param phone
     * @param channel
     * @return
     */
    public @Nonnull
    HainanYunshemeiNewResult getSms(String phone, String channel, String app, String url) {
        String getSmsUrl = chongqingYunshemeiProperties.getGetSmsUrl();
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("operatorId", chongqingYunshemeiProperties.getOperatorId());
        dataNode.put("productId", chongqingYunshemeiProperties.getChannelCodeMap().get(channel));
        dataNode.put("channelId", chongqingYunshemeiProperties.getChannelId());
        dataNode.put("appName", app);
        dataNode.put("chargeUrl", url);

        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-获取验证码-渠道号:{},手机号:{},请求:{}", LOG_TAG, channel, phone, getSmsUrl);
        Request request = new Request.Builder().url(getSmsUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, HainanYunshemeiNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取验证码-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return HainanYunshemeiNewResult.fail();
        }
    }

    /**
     * 营销活动校验与办理接口
     *
     * @param phone
     * @param channel
     * @param code
     * @return
     */
    public @Nonnull
    HainanYunshemeiNewResult smsCode(String phone, String channel, String code) {
        String smsCodeUrl = chongqingYunshemeiProperties.getSmsCodeUrl();
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("operatorId", chongqingYunshemeiProperties.getOperatorId());
        dataNode.put("productId", chongqingYunshemeiProperties.getChannelCodeMap().get(channel));
        dataNode.put("channelId", chongqingYunshemeiProperties.getChannelId());
        dataNode.put("code", code);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        log.info("{}-业务开通-渠道号:{},手机号:{},短信验证码:{},请求:{}", LOG_TAG, channel, phone, code, smsCodeUrl);
        Request request = new Request.Builder().url(smsCodeUrl).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-业务开通-渠道号:{},手机号:{},响应:{}", LOG_TAG, channel, phone, content);
            return mapper.readValue(content, HainanYunshemeiNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-业务开通-渠道号:{},手机号:{},异常:", LOG_TAG, channel, phone, e);
            return HainanYunshemeiNewResult.fail();
        }
    }
}
