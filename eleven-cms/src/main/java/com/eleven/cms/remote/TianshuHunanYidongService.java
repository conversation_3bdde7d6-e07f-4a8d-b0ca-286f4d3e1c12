package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.JunboLlbConfig;
import com.eleven.cms.config.JunboLlbProperties;
import com.eleven.cms.config.TianshuHunanYidongVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.JunboLlbResult;
import com.eleven.cms.vo.TianshuHunanYidongResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.jsoup.nodes.DataNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

/**
 * 骏伯湖南移动业务类
 * @author: cai lei
 * @create: 2022-07-07 10:29
 */

@Slf4j
@Service
public class TianshuHunanYidongService {

    public static final String LOG_TAG = "天枢湖南移动视频彩铃api";
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private Environment environment;
    @Autowired
    private TianshuHunanYidongVrbtProperties tianshuHunanYidongVrbtProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;

    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public TianshuHunanYidongResult getSms(String phone,String channel) {

        final HttpUrl httpUrl = HttpUrl.parse(tianshuHunanYidongVrbtProperties.getGetSmsCodeUrl())
                .newBuilder()
                .addQueryParameter("phone", phone)
                .addQueryParameter("discnt_code", tianshuHunanYidongVrbtProperties.getDiscntCode()).build();

        log.info("{}-获取验证码-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone, channel, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-获取验证码-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone, channel, result);
            return mapper.readValue(result, TianshuHunanYidongResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-获取验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone, channel, e);
            return TianshuHunanYidongResult.FAIL_RESULT;
        }
    }

    //{"success":true,"code":"0000","message":"执行成功","data":{"msg":"下单成功","responseCode":"0","orderCode":"","subMediaCode":"0","url":"","routeCode":"flow_yd_hunan","sellerId":""}}
    public TianshuHunanYidongResult smsCode(String phone, String code, String channel) {

        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("phone", phone);
        objectNode.put("smsCode", code);
        objectNode.put("type", tianshuHunanYidongVrbtProperties.getType());
        objectNode.put("channel", tianshuHunanYidongVrbtProperties.getChannel());

        RequestBody body = RequestBody.create(JSON, objectNode.toString());

        final HttpUrl httpUrl = HttpUrl.parse(tianshuHunanYidongVrbtProperties.getSmsCodeUrl());
        log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},请求:{}", LOG_TAG, phone, channel,code, httpUrl.toString());

        Request request = new Request.Builder()
                .url(httpUrl)
                .post(body)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", LOG_TAG, phone, channel,code, result);
            return mapper.readValue(result, TianshuHunanYidongResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},异常:", LOG_TAG, phone, channel,code, e);
            return TianshuHunanYidongResult.FAIL_RESULT;
        }
    }

}