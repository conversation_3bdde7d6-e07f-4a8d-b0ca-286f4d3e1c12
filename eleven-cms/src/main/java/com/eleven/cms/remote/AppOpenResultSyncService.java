package com.eleven.cms.remote;

import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

@Slf4j
@Service
public class AppOpenResultSyncService {
    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        //if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
        //    this.client = this.client.newBuilder().proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 10086))).build();
        //}
        //this.client = OkHttpClientUtils.getNewInstance().newBuilder().addNetworkInterceptor(new CurlInterceptor(
        //        message -> System.out.println(message))).build();
        this.mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }

    /**
     * "视频彩铃小程序渠道号开通结果同步
     * @param mobile
     * @param status
     * @param result
     * @param extra
     */
    @Async
    public void appOpenResultCallback(String mobile, Integer status, String result, String extra) {
        String url = "https://h5.vs.w4ff.com/api/video/month/callback";
        final ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("mobile", mobile);
        dataNode.put("status", status);
        dataNode.put("result", result);
        dataNode.put("extra", extra);
        String raw = dataNode.toString();
        log.info("视频彩铃小程序渠道号开通结果同步通知=>raw: {}",raw);
        push(url, raw, mobile);
    }
    
    public Result<?> push(String url, String raw, String mobile) {
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();
        try (Response response = client.newCall(request)
                .execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String jsonResp = response.body().string();
            log.info("视频彩铃小程序渠道号开通结果同步响应=>mobile:{},响应:{}", mobile, StringUtils.normalizeSpace(jsonResp));
            return mapper.readValue(jsonResp,Result.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("视频彩铃小程序渠道号开通结果同步异常", e);
            return Result.error("系统繁忙,请稍后再试!");
        }
    }

}
