package com.eleven.cms.remote;

import com.eleven.cms.config.ChongqingYidongQycsProperties;
import com.eleven.cms.config.ShoujizixunConfig;
import com.eleven.cms.config.ShoujizixunProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ChongqingYidongQycsResult;
import com.eleven.cms.vo.ShoujizixunResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * 爱豆来电接口
 *
 * @author: cai lei
 * @create: 2023-11-13 10:28
 */
@Slf4j
@Service
public class ChongqingYidongQycsService {

    private OkHttpClient client;
    private ObjectMapper mapper;

    private static final MediaType mediaType = MediaType.parse("application/json");
    public static final String LOG_TAG = "重庆移动API";

    @Autowired
    private ChongqingYidongQycsProperties chongqingYidongQycsProperties;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public ChongqingYidongQycsResult getSms(String mobile, String channel) {
        final HttpUrl httpUrl = HttpUrl.parse(chongqingYidongQycsProperties.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("phone", mobile)
                .addQueryParameter("content", chongqingYidongQycsProperties.getContent())
                .addQueryParameter("cpparam", RandomStringUtils.randomNumeric(18))
                .build();
        log.info("{}-获取短信-手机号:{},渠道号:{},请求:{}", LOG_TAG, mobile, channel, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},渠道号:{},响应:{}", LOG_TAG, mobile, channel, content);
            return mapper.readValue(content, ChongqingYidongQycsResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},渠道号:{},异常:", LOG_TAG, mobile, channel, e);
            return ChongqingYidongQycsResult.fail();
        }
    }

    public ChongqingYidongQycsResult smsCode(String mobile, String orderNo, String code, String channel) {
        final HttpUrl httpUrl = HttpUrl.parse(chongqingYidongQycsProperties.getSendSmsUrl())
                .newBuilder()
                .addQueryParameter("vcode", code)
                .addQueryParameter("tradeid", orderNo)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},请求:{}", LOG_TAG, mobile, code, channel, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},响应:{}", LOG_TAG, mobile, code, channel, content);
            return mapper.readValue(content, ChongqingYidongQycsResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},异常:", LOG_TAG, mobile, code, channel, e);
            return ChongqingYidongQycsResult.fail();
        }
    }
}
