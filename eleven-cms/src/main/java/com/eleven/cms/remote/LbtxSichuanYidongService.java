package com.eleven.cms.remote;

import com.eleven.cms.config.LbtxSichuanYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.LbtxSichuanResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2021-08-04 14:32
 */
@Slf4j
@Service
public class LbtxSichuanYidongService {

    public static final String LOG_TAG = "联保天下四川移动业务api";
    @Autowired
    LbtxSichuanYidongProperties lbtxSichuanYidongProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private Interceptor kuaimaYidongIntercept;

    private OkHttpClient client;

    private ObjectMapper mapper;


    private static final MediaType mediaType = MediaType.parse("application/json");


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public LbtxSichuanResult prepareOrder(String phone, String bizCode) throws Exception {
        String prepareOrderUrl = lbtxSichuanYidongProperties.getPrepareOrderUrl();
        final HttpUrl httpUrl = HttpUrl.parse(prepareOrderUrl).newBuilder()
                .addQueryParameter("mobile", phone)
                .addQueryParameter("code", bizCode)
                .addQueryParameter("channel", "ot1")
                .build();

        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-预下单-手机号:{},业务编码:{},请求:{}", LOG_TAG, phone, bizCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-预下单-手机号:{},业务编码:{},返回结果:{}", LOG_TAG, phone, bizCode, result);
            return mapper.readValue(result, LbtxSichuanResult.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}-预下单-手机号:{},业务编码:{},异常:", LOG_TAG, phone, bizCode, e);
            return LbtxSichuanResult.FAIL_RESULT;
        }
    }
}
