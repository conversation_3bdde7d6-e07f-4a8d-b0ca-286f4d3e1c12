package com.eleven.cms.remote;

import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.eleven.cms.config.*;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.huoshan.AES;
import com.eleven.cms.util.huoshan.LySign;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2023-5-25 14:08:32
 * Desc: 触点（支付能力）相关接口
 */
@Slf4j
@Service
public class HuoshanApiService {

    public static final String LOG_TAG = "火山业务api";

    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private HuoshanProperties huoshanProperties;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");
    private static final String version = "1.0";


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort))).build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     *  校验token，获取到加密手机号
     * token
     * traceId
     * userInformation
     * @return
     */
    public HuoshanResult tokenValidate(String token,String traceId,String userInformation){

        try{
            LySign lySign = LySign.of(SignAlgorithm.SHA256withRSA, huoshanProperties.getPrivateKey(), null, false);
            String timestamp = DateUtil.formatForMiguGroupApi(LocalDateTime.now());
            Map<String, Object> map = new HashMap<>();

            //构建请求body参数
            map.put("token", token);
            map.put("sign", lySign.sign(huoshanProperties.getAppId() + traceId + timestamp + token + version));
            //map.put("userInformation", URLEncoder.encode(userInformation));
            map.put("userInformation", userInformation);

            HttpUrl httpUrl = HttpUrl.parse(huoshanProperties.getUrl())
                    .newBuilder()
                    .build();
            final String bodyJson = mapper.writeValueAsString(map);
            RequestBody body = RequestBody.create(mediaType, bodyJson);
            Request request = new Request.Builder().url(httpUrl)
                    .addHeader("Content-Type","application/json")
                    .addHeader("interfaceVersion",version)
                    .addHeader("appId", huoshanProperties.getAppId())
                    .addHeader("traceId", traceId)
                    .addHeader("timestamp", timestamp)
                    .addHeader("businessType", "8")
                    .post(body)
                    .build();
            log.info("{}-一键登录接口,traceId:{},请求:{},bodyJson:{},", LOG_TAG, traceId,request.toString(),bodyJson);
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String result = response.body().string();
                log.info("{}-一键登录接口,traceId:{},返回结果:{}", LOG_TAG,traceId, result);
                HuoshanResult huoshanResult = mapper.readValue(result, HuoshanResult.class);
                if(huoshanResult.isOK()){
                    String msisdn = huoshanResult.getData().getMsisdn();
                    String key = huoshanProperties.getAppKey().substring(0,16);//截取appkey前16位进行手机号解密
                    AES aes = new AES(key);
                    String phone = new String(aes.decryptBase64(msisdn));
                    huoshanResult.getData().setPhone(phone);
                }
                return huoshanResult;
            }
        }catch (Exception e){
            log.info("{}-一键登录接口接口,traceId:{},异常:", LOG_TAG,traceId, e);
            return HuoshanResult.fail();
        }
    }



}
