package com.eleven.cms.remote;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.HebeiYidongVrbtConfig;
import com.eleven.cms.config.HunanYidongProperties;
import com.eleven.cms.config.VrbtDiyProperties;
import com.eleven.cms.entity.VrbtDiyVideo;
import com.eleven.cms.service.IVrbtDiyVideoService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.guizhou.Md5Utils;
import com.eleven.cms.util.hunan.AESUtil;
import com.eleven.cms.util.hunan.RSAUtils;
import com.eleven.cms.vo.HebeiMobileResult;
import com.eleven.cms.vo.HunanYidongResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Author: lihb
 * Date: 2023-6-20 15:06:29
 * Desc:视频彩铃DIY
 */
@Slf4j
@Service
public class HunanYidongService {

    public static final String LOG_TAG = "湖南移动-api";

    @Autowired
    private HunanYidongProperties hunanYidongProperties;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    //   @PostConstruct
    public void init() throws Exception {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().readTimeout(20L, TimeUnit.SECONDS).writeTimeout(20L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            //this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }

        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }


    /**
     * 外部渠道精准营销推荐
     *
     * @param phone
     * @return
     */
//    public HunanYidongResult iopTtTriggerext(String phone) {
//
//        try{
//            ObjectNode rootNode = mapper.createObjectNode();
//            rootNode.put("eventCode","");
//            rootNode.put("msisdn",phone);
//            rootNode.put("slotIds","");
//
//            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
//            Request request = getRequest(body,"IOP_HT_TriggerExt");
//            log.info("{}-外部渠道精准营销推荐-手机号:{},请求:{},body:\n{}", LOG_TAG,phone,request.toString(),mapper.writeValueAsString(rootNode));
//
//            try (Response response = client.newCall(request).execute()) {
//                if (!response.isSuccessful()) {
//                    throw new IOException("Unexpected code " + response);
//                }
//                String content = response.body().string();
//                log.info("{}-外部渠道精准营销推荐-手机号:{},响应:{}", LOG_TAG, phone, content);
//                return mapper.readValue(content, HunanYidongResult.class);
//            } catch (Exception e) {
//                log.info("{}-外部渠道精准营销推荐-手机号:{},异常:", LOG_TAG, phone, e);
//                return HunanYidongResult.fail();
//            }
//        }catch (Exception e){
//            log.info("{}-外部渠道精准营销推荐-手机号:{},异常:", LOG_TAG, phone, e);
//            return HunanYidongResult.fail();
//        }
//    }

    /**
     * 产品元素推荐
     *
     * @param phone
     * @return
     */
    public HunanYidongResult queryProducts(String phone) {

        try{
            ObjectNode rootNode = mapper.createObjectNode();
            String key = hunanYidongProperties.getKey();
            rootNode.put("SERIAL_NUMBER",RSAUtils.encryptByPublicKey(phone,key));

            ObjectNode requestBody = mapper.createObjectNode();
            requestBody.put("SERIAL_NUMBER",phone);

            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
            Request request = getRequest(body,"DQ_HQ_HNAN_QueryDiscntOut");
            log.info("{}-产品元素推荐-手机号:{},请求:{},明文body:\n{}", LOG_TAG,phone,request.toString(),mapper.writeValueAsString(requestBody));

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-产品元素推荐-手机号:{},响应:{}", LOG_TAG, phone, content);
                return mapper.readValue(content, HunanYidongResult.class);
            } catch (Exception e) {
                log.info("{}-产品元素推荐-手机号:{},异常:", LOG_TAG, phone, e);
                return HunanYidongResult.fail();
            }
        }catch (Exception e){
            log.info("{}-产品元素推荐-手机号:{},异常:", LOG_TAG, phone, e);
            return HunanYidongResult.fail();
        }
    }

    /**
     * 产品/优惠预受理接口(办理资格校验)
     *
     * @param phone
     * @param channel
     * @return
     */
    public HunanYidongResult productCheck(String phone, String channel) {

        try{
            ObjectNode rootNode = mapper.createObjectNode();
            String key = hunanYidongProperties.getKey();
            rootNode.put("SERIAL_NUMBER",phone);
            rootNode.put("DISCNT_CODE",hunanYidongProperties.getBizCodeByChannel(channel));

            ObjectNode requestBody = mapper.createObjectNode();
            requestBody.put("SERIAL_NUMBER",phone);
            requestBody.put("DISCNT_CODE",hunanYidongProperties.getBizCodeByChannel(channel));

            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
            Request request = getRequest(body,"DQ_HT_HNAN_ProductChangeOutPre");
            log.info("{}-办理资格校验-手机号:{},请求:{},明文body:\n{}", LOG_TAG,phone,request.toString(),mapper.writeValueAsString(requestBody));

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-办理资格校验-手机号:{},响应:{}", LOG_TAG, phone, content);
                return mapper.readValue(content, HunanYidongResult.class);
            } catch (Exception e) {
                log.info("{}-办理资格校验-手机号:{},异常:", LOG_TAG, phone, e);
                return HunanYidongResult.fail();
            }
        }catch (Exception e){
            log.info("{}-办理资格校验-手机号:{},异常:", LOG_TAG, phone, e);
            return HunanYidongResult.fail();
        }
    }

    /**
     * 短信验证码下发
     *
     * @param phone
     * @param channel
     * @return
     */
    public HunanYidongResult getSms(String phone, String channel) {

        try{
            ObjectNode rootNode = mapper.createObjectNode();
            ObjectNode requestBody = mapper.createObjectNode();
            String key = hunanYidongProperties.getKey();
            rootNode.put("SERIAL_NUMBER",RSAUtils.encryptByPublicKey(phone,key));
            rootNode.put("TRADE_TYPE_CODE",RSAUtils.encryptByPublicKey(hunanYidongProperties.getTradeTypeCode(),key));
            rootNode.put("DISCNT_CODE",RSAUtils.encryptByPublicKey(hunanYidongProperties.getBizCodeByChannel(channel),key));

            requestBody.put("SERIAL_NUMBER",phone);
            requestBody.put("TRADE_TYPE_CODE",hunanYidongProperties.getTradeTypeCode());
            requestBody.put("DISCNT_CODE",hunanYidongProperties.getBizCodeByChannel(channel));

            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
            Request request = getRequest(body,"DQ_HT_HNAN_sendSmsVerifyCodeOut");
            log.info("{}-短信验证码下发-手机号:{},请求:{},body明文:\n{}", LOG_TAG,phone,request.toString(),mapper.writeValueAsString(requestBody));

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-短信验证码下发-手机号:{},响应:{}", LOG_TAG, phone, content);
                HunanYidongResult hunanYidongResult = mapper.readValue(content, HunanYidongResult.class);
                if(hunanYidongResult.isOK()){
                    JsonNode tree = mapper.readTree(content);
                    JsonNode result = tree.at("/result");
                    JavaType javaType = mapper.getTypeFactory().constructParametricType(ArrayList.class,HunanYidongResult.ResultData.class);
                    List<HunanYidongResult.ResultData> list =  mapper.readValue(result.toString(), javaType);
                    hunanYidongResult.setResult(list);
                }
                return hunanYidongResult;
            } catch (Exception e) {
                log.info("{}-短信验证码下发-手机号:{},异常:", LOG_TAG, phone, e);
                return HunanYidongResult.fail();
            }
        }catch (Exception e){
            log.info("{}-短信验证码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return HunanYidongResult.fail();
        }
    }

    /**
     * 产品/优惠受理-提交短信验证码
     *
     * @param phone
     * @param channel
     * @return
     */
    public HunanYidongResult smsCode(String phone, String smsCode, String channel) {

        try{
            String outTradeId = "yr" + IdWorker.getId();
            ObjectNode rootNode = mapper.createObjectNode();
            ObjectNode requestBody = mapper.createObjectNode();
            String key = hunanYidongProperties.getKey();
            rootNode.put("SERIAL_NUMBER", RSAUtils.encryptByPublicKey(phone,key));
            rootNode.put("DISCNT_CODE",RSAUtils.encryptByPublicKey(hunanYidongProperties.getBizCodeByChannel(channel),key));
            rootNode.put("SMS_CODE",RSAUtils.encryptByPublicKey(smsCode,key));
            rootNode.put("OUT_TRADE_ID",RSAUtils.encryptByPublicKey(outTradeId,key));
            /*rootNode.put("SIGN_DATA","MD5签名");*/
            requestBody.put("SERIAL_NUMBER",phone);
            requestBody.put("DISCNT_CODE",hunanYidongProperties.getBizCodeByChannel(channel));
            requestBody.put("SMS_CODE",smsCode);
            requestBody.put("OUT_TRADE_ID",outTradeId);

            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
            Request request = getRequest(body,"DQ_HT_HNAN_ProductChangeOut");
            log.info("{}-提交短信验证码-手机号:{},请求:{},body明文:\n{}", LOG_TAG,phone,request.toString(),mapper.writeValueAsString(requestBody));

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                log.info("{}-提交短信验证码-手机号:{},响应:{}", LOG_TAG, phone, content);
                HunanYidongResult hunanYidongResult = mapper.readValue(content, HunanYidongResult.class);
                if(hunanYidongResult.isOK()){
                    JsonNode tree = mapper.readTree(content);
                    JsonNode result = tree.at("/result");
                    JavaType javaType = mapper.getTypeFactory().constructParametricType(ArrayList.class,HunanYidongResult.ResultData.class);
                    List<HunanYidongResult.ResultData> list =  mapper.readValue(result.toString(), javaType);
                    hunanYidongResult.setResult(list);
                }
                return hunanYidongResult;
            } catch (Exception e) {
                log.info("{}-提交短信验证码-手机号:{},异常:", LOG_TAG, phone, e);
                return HunanYidongResult.fail();
            }
        }catch (Exception e){
            log.info("{}-提交短信验证码-手机号:{},异常:", LOG_TAG, phone, e);
            return HunanYidongResult.fail();
        }
    }

    private Request getRequest(RequestBody body, String method) {
        HttpUrl httpUrl = HttpUrl.parse(hunanYidongProperties.getBaseUrl())
                .newBuilder()
                .addQueryParameter("method",method)
                .addQueryParameter("format","json")
                .addQueryParameter("timestamp", DateUtil.formatFullTime(LocalDateTime.now(), DateUtil.FULL_TIME_PATTERN_WITH_MILL))
                .addQueryParameter("appId",hunanYidongProperties.getAppId())
                .addQueryParameter("status","1")
                .addQueryParameter("flowdId",IdWorker.getIdStr())
                .addQueryParameter("PROVINCE_CODE",hunanYidongProperties.getProvinceCode())
                .addQueryParameter("IN_MODE_CODE",hunanYidongProperties.getInModeCode())
                .addQueryParameter("TRADE_EPARCHY_CODE",hunanYidongProperties.getTradeEparchyCode())
                .addQueryParameter("TRADE_CITY_CODE",hunanYidongProperties.getTradeCityCode())
                .addQueryParameter("TRADE_DEPART_ID",hunanYidongProperties.getTradeDepartId())
                .addQueryParameter("TRADE_STAFF_ID",hunanYidongProperties.getTradeStaffId())
                .addQueryParameter("TRADE_DEPART_PASSWD",hunanYidongProperties.getTradeDepartPasswd())
                .addQueryParameter("TRADE_TERMINAL_ID","39.106.113.162")
                .build();
        Request request = new Request.Builder().url(httpUrl)
                .post(body)
                .build();
        return request;
    }
}
