package com.eleven.cms.remote;

import com.eleven.cms.config.ChongqingYidongVrbtNewProperties;
import com.eleven.cms.config.ChongqingYidongVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ChongqingMobileNewResult;
import com.eleven.cms.vo.ChongqingMobileResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * 河北移动视频彩铃
 *
 * @author: cai lei
 * @create: 2021-12-27 10:03
 */
@Service
@Slf4j
public class ChongqingYidongVrbtNewService {

    @Autowired
    private Environment environment;
    @Autowired
    private ChongqingYidongVrbtNewProperties chongqingYidongVrbtNewProperties;

    public static final MediaType JSONTYPE
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    public static final String LOG_TAG = "新重庆移动视频彩铃api";
    private OkHttpClient client;
    private ObjectMapper mapper;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }


    //{"code":0,"msg":"发送成功","data":""} 0成功 -1失败

    /**
     * 请求短信验证码
     *
     * @param phone
     * @return
     */
    public @Nonnull
    ChongqingMobileNewResult getSms(String phone, String bizCode) {
        ObjectNode data = mapper.createObjectNode();
        data.put("channelId", chongqingYidongVrbtNewProperties.getChannelId());
        data.put("operatorId", chongqingYidongVrbtNewProperties.getOperatorId());
        data.put("phone", phone);
        data.put("ncode", bizCode);
        RequestBody body = RequestBody.create(JSONTYPE, data.toString());
        log.info("{}-获取短信-手机号:{},业务代码:{}", LOG_TAG, phone, bizCode);
        Request request = new Request.Builder()
                .url(chongqingYidongVrbtNewProperties.getGetSmsUrl())
                .post(body)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},业务代码:{},响应:{}", LOG_TAG, phone, bizCode, content);
            return mapper.readValue(content, ChongqingMobileNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},业务代码:{},异常:", LOG_TAG, phone, bizCode, e);
            return ChongqingMobileNewResult.fail();
        }
    }


    //{"errNo":0,"message":"success","tz":"Asia\/Shanghai","data":{"respCode":"0000","respMsg":"success","data":null}}
    // 0成功 -1失败

    /**
     * 提交短信验证码
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public @Nonnull
    ChongqingMobileNewResult smsCode(String phone, String smsCode, String bizCode) {
        ObjectNode data = mapper.createObjectNode();
        data.put("channelId", chongqingYidongVrbtNewProperties.getChannelId());
        data.put("operatorId", chongqingYidongVrbtNewProperties.getOperatorId());
        data.put("phone", phone);
        data.put("code", smsCode);
        data.put("ncode", bizCode);
        RequestBody body = RequestBody.create(JSONTYPE, data.toString());
        log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{}", LOG_TAG, phone, smsCode, bizCode);
        Request request = new Request.Builder().url(chongqingYidongVrbtNewProperties.getSendSmsUrl()).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{},响应:{}", LOG_TAG, phone, smsCode, bizCode, content);
            return mapper.readValue(content, ChongqingMobileNewResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},业务代码:{},异常:", LOG_TAG, phone, smsCode, bizCode, e);
            return ChongqingMobileNewResult.fail();
        }
    }
}
