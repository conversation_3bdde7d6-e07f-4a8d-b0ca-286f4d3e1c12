package com.eleven.cms.remote;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.entity.*;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RightsPackDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权益领取业务接口
 */
@Slf4j
@Service
public class RightsRechargeService {


    private static final String BIZ_TYPE_COMIC_BIZ_TYPE = "COMIC";
    private static final String BIZ_TYPE_READ_BIZ_TYPE = "READ";
    private static final String BIZ_TYPE_VRBT_CW_BIZ_TYPE = "VRBT_CW";
    private static final String BIZ_TYPE_MEMBER_ALIPAY = "MEMBER_ALIPAY";
    private static final String BIZ_TYPE_WO_READ_SHOP="WO_READ_SHOP";
    //特殊处理
    private static final String CHANNEL_BEAN="BEAN";
    //通用登录接口排除特定业务类型
    private static final List<String> NOT_BIZ_TYPE_LIST= Lists.newArrayList(BIZ_TYPE_WO_READ_SHOP,CHANNEL_BEAN);
    @Autowired
    ElasticsearchRestTemplate restTemplate;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IEsDataService esDataService;
    @Autowired
    private ILianlianProductService lianlianProductService;
    @Autowired
    private IProvinceRightsService provinceRightsService;
    /**
     * 查询业务列表
     * @param mobile
     * @return
     */
    public FebsResponse getServiceList(String mobile)  {
        try {
            List<EsSubscribe> esSubscribeList=this.findByMobile(mobile);
            return queryServiceList(mobile, esSubscribeList);
        } catch (Exception e) {
            log.error("查询业务列表=>手机号:{}",mobile,e);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }
    /**
     * 咪咕动漫查询业务列表
     * @param mobile
     * @return
     */
    public FebsResponse miguComicGetServiceList(String mobile)  {
        try {
            List<EsSubscribe> esSubscribeList=this.miguComicFindByMobile(mobile);
            return queryServiceList(mobile, esSubscribeList);
        } catch (Exception e) {
            log.error("咪咕动漫查询业务列表=>手机号:{}",mobile,e);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }

    /**
     * 咪咕阅读查询业务列表
     * @param mobile
     * @return
     */
    public FebsResponse miguReadGetServiceList(String mobile)  {
        try {
            List<EsSubscribe> esSubscribeList=this.miguReadFindByMobile(mobile);
            return queryServiceList(mobile, esSubscribeList);
        } catch (Exception e) {
            log.error("咪咕阅读查询业务列表=>手机号:{}",mobile,e);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }

    /**
     * 悦动专属6元包-川网查询业务列表
     * @param mobile
     * @return
     */
    public FebsResponse yueDongCaiLingChuanWangGetServiceList(String mobile)  {
        try {
            List<EsSubscribe> esSubscribeList=this.yueDongCaiLingChuanWangFindByMobile(mobile);
            return queryServiceList(mobile, esSubscribeList);
        } catch (Exception e) {
            log.error("悦动专属6元包-川网查询业务列表=>手机号:{}",mobile,e);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }

    private FebsResponse queryServiceList(String mobile, List<EsSubscribe> esSubscribeList) {
        if (esSubscribeList == null || esSubscribeList.isEmpty()) {
            log.info("查询业务列表-ES订单暂无数据=>手机号:{}", mobile);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        for (EsSubscribe esSubscribe : esSubscribeList) {
            //渠道配置主键ID
            final BusinessChannelRights businessChannel = businessChannelRightsService.lambdaQuery().select(BusinessChannelRights::getId).eq(BusinessChannelRights::getIsEffect, 1).eq(BusinessChannelRights::getBusinessChannel, esSubscribe.getChannel()).orderByDesc(BusinessChannelRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(businessChannel==null){
                log.info("查询业务列表-ES订单渠道号未匹配业务渠道权益=>手机号:{},ES渠道号:{}", mobile, esSubscribe.getChannel());
                continue;
            }
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getBusinessId, businessChannel.getId()).select(BusinessPack::getServiceApiBeanName,BusinessPack::getServiceId).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.info("查询业务列表-渠道号-业务渠道权益关联未匹配=>手机号:{},ES渠道号:{}", mobile, esSubscribe.getChannel());
                continue;
            }
            final String serviceApiBeanName = businessPack.getServiceApiBeanName();
            final String serviceId = businessPack.getServiceId();
            log.info("通用权益登录-ApiBean配置=>手机号:{},ApiBean:{},权益领取业务ID:{},ES渠道号:{}", mobile, serviceApiBeanName, serviceId, esSubscribe.getChannel());
            if (StringUtils.isBlank(serviceApiBeanName) || StringUtils.isBlank(serviceId)) {
                log.info("查询业务列表-渠道号-业务渠道权益关联未配置=>手机号:{},ES渠道号:{}", mobile, esSubscribe.getChannel());
                continue;
            }
            //校验权益是否包月以及领取
            final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessRightsSubService.class);

            FebsResponse memberVerifyFebs = businessRightsSubService.memberVerify(mobile, serviceId);

            FebsResponse rechargRecordVerifyFebs = businessRightsSubService.rechargRecordVerify(mobile, serviceId);

            if(memberVerifyFebs.isOK() && rechargRecordVerifyFebs.isOK()) {

                EsSubscribe subscribe=esDataService.findEsSubscribeByMobileAndChannel(mobile,esSubscribe.getChannel());
                if(subscribe==null){
                    return new FebsResponse().success().data(esSubscribe.getChannel());
                }
                ProvinceRights provinceRights=provinceRightsService.lambdaQuery().select(ProvinceRights::getRightsChannel).eq(ProvinceRights::getChannel,esSubscribe.getChannel()).eq(ProvinceRights::getProvince,subscribe.getProvince()).le(ProvinceRights::getSubTime,DateUtil.getDateFormat(subscribe.getCreateTime(),DateUtil.FULL_TIME_SPLIT_PATTERN)).orderByDesc(ProvinceRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(provinceRights!=null){
                    return new FebsResponse().success().data(provinceRights.getRightsChannel());
                }
                return new FebsResponse().success().data(esSubscribe.getChannel());
            }
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }




    /**
     * 通用权益列表查询（包含支付宝）
     * @param channel
     * @return
     */
    public FebsResponse queryRightsListByChannel(String channel, HttpServletRequest request){
        final String mobiles = request.getHeader("mobile");
        log.info("通用权益列表查询=>手机号:{},渠道号:{}",mobiles,channel);
        //特定省份特定权益配置
        if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
            EsSubscribe subscribe=esDataService.findEsSubscribeByMobileAndChannel(mobiles,channel);
            if(subscribe!=null){
                ProvinceRights provinceRights=provinceRightsService.lambdaQuery().select(ProvinceRights::getRightsChannel).eq(ProvinceRights::getChannel,channel).eq(ProvinceRights::getProvince,subscribe.getProvince()).le(ProvinceRights::getSubTime,DateUtil.getDateFormat(subscribe.getCreateTime(),DateUtil.FULL_TIME_SPLIT_PATTERN)).orderByDesc(ProvinceRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(provinceRights!=null){
                    channel=provinceRights.getRightsChannel();
                }
            }
        }
        //查询有效渠道权益
        final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getBusinessChannel,channel).list();
        if(businessList==null || businessList.isEmpty()){
            log.info("通用权益列表查询-指定渠道权益未正确配置=>手机号:{},渠道号:{}",mobiles,channel);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        final List<String> businessIdList=businessList.stream().map(BusinessChannelRights::getId).collect(Collectors.toList());
        //业务类型去重
        final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().select(BusinessPack::getServiceId,BusinessPack::getPackId).in(BusinessPack::getBusinessId,businessIdList).list();
        if(businessPackList==null || businessPackList.isEmpty()){
            log.info("通用权益列表查询-业务渠道权益关联未正确配置=>手机号:{},渠道号:{}",mobiles,channel);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        final List<String> businessPackIdList=businessPackList.stream().map(BusinessPack::getPackId).collect(Collectors.toList());
        if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
            return rightsSubService.queryWebRightsList(businessPackList.get(0).getServiceId(),businessPackIdList);
        }
        return rightsSubService.queryRightsList(businessPackList.get(0).getServiceId(),businessPackIdList);
    }


    /**
     * 查询领取记录
     * @param mobile
     * @return
     */
    public FebsResponse queryRechargeList(String mobile){
        return rightsSubService.queryRechargeList(mobile);
    }
    /**
     * 通用权益领取接口（包含支付宝）
     * @param mobile
     * @return
     */
    public FebsResponse createScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel, HttpServletRequest request)  {
        log.info("通用权益领取=>手机号:{},权益领取业务ID:{},账号:{},权益包:{},产品ID:{},渠道号:{}",mobile,serviceId,account,packName,rightsId,channel);

//        if(!this.isLegal(channel,rightsId,request)){
//            log.warn("通用权益领取-权益领取非法=>手机号:{},权益领取业务ID:{},账号:{},权益包:{},产品ID:{},渠道号:{}",mobile,serviceId,account,packName,rightsId,channel);
//            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//        };

        try {
            if(StringUtils.isBlank(serviceId)){
                log.warn("通用权益领取-渠道号-业务ID错误=>手机号:{},权益领取业务ID:{},渠道号:{}", mobile, serviceId, channel);
                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
            }
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, serviceId).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(businessPack==null){
                log.warn("通用权益领取-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",mobile,serviceId,channel);
                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
            }
            final String serviceApiBeanName =businessPack.getServiceApiBeanName();
            log.info("通用权益领取-ApiBean配置=>手机号:{},ApiBean:{},权益领取业务ID:{},账号:{},权益包:{},产品ID:{},渠道号:{}",mobile,serviceApiBeanName,serviceId,account,packName,rightsId,channel);
            if(StringUtils.isBlank(serviceApiBeanName)){
                log.warn("通用权益领取-渠道号-业务渠道权益关联未配置=>手机号:{},权益领取业务ID:{},渠道号:{}", mobile, serviceId,channel);
                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
            }
            final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessRightsSubService.class);
            final String mobiles = request.getHeader("mobile");
            if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
                FebsResponse createScheduledRechargeFebs=businessRightsSubService.webCreateScheduledRecharge(mobile,account,serviceId,packName,rightsId, channel);
                return createScheduledRechargeFebs;
            }
            FebsResponse createScheduledRechargeFebs=businessRightsSubService.createScheduledRecharge(mobile,account,serviceId,packName,rightsId, channel);
            return createScheduledRechargeFebs;
        } catch (Exception e) {
            log.error("通用权益领取异常=>手机号:{}",mobile,e);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }





    /**
     * 根据手机号查询es订购数据
     * @param mobile
     * @return
     */
    public List<EsSubscribe> findByMobile(String mobile) {
        final List<BusinessChannelRights> businessChannelList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).notIn(BusinessChannelRights::getRemark,NOT_BIZ_TYPE_LIST).select(BusinessChannelRights::getBusinessChannel).list();
        if(businessChannelList==null || businessChannelList.isEmpty()){
            log.info("业务渠道权益未配置=>手机号:{}",mobile);
            return null;
        }
        final List<String> channelList=businessChannelList.stream().map(BusinessChannelRights::getBusinessChannel).collect(Collectors.toList());
        List<EsSubscribe> esSubscribeList=esDataService.findEsSubscribeByMobileAndChannels(mobile, channelList);
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.info("ES查询数据不存在=>手机号:{},渠道号:{}",mobile,channelList);
            return null;
        }
        return esSubscribeList;
    }



    /**
     * 咪咕阅读订购根据手机号查询es订购数据
     * @param mobile
     * @return
     */
    public List<EsSubscribe> miguReadFindByMobile(String mobile) {

        final List<String> channelList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).eq(BusinessChannelRights::getRemark,BIZ_TYPE_READ_BIZ_TYPE).select(BusinessChannelRights::getBusinessChannel).list().stream().map(BusinessChannelRights::getBusinessChannel).collect(Collectors.toList());
        if(channelList==null || channelList.isEmpty()){
            log.info("咪咕动漫业务渠道权益未配置=>手机号:{}",mobile);
            return null;
        }
        List<EsSubscribe> esSubscribeList=esDataService.findEsSubscribeByMobileAndChannels(mobile, channelList);
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.info("咪咕阅读指定渠道权益未正确配置=>手机号:{},渠道号:{}",mobile,channelList);
            return null;
        }
        return esSubscribeList;
    }

    /**
     * 咪咕动漫订购根据手机号查询es订购数据
     * @param mobile
     * @return
     */
    public List<EsSubscribe> miguComicFindByMobile(String mobile) {

        final List<String> channelList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).eq(BusinessChannelRights::getRemark,BIZ_TYPE_COMIC_BIZ_TYPE).select(BusinessChannelRights::getBusinessChannel).list().stream().map(BusinessChannelRights::getBusinessChannel).collect(Collectors.toList());
        if(channelList==null || channelList.isEmpty()){
            log.info("咪咕动漫业务渠道权益未配置=>手机号:{}",mobile);
            return null;
        }
        List<EsSubscribe> esSubscribeList=esDataService.findEsSubscribeByMobileAndChannels(mobile, channelList);
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.info("咪咕动漫指定渠道权益未正确配置=>手机号:{},渠道号:{}",mobile,channelList);
            return null;
        }
        return esSubscribeList;
    }


    /**
     * 悦动专属6元包-川网订购根据手机号查询es订购数据
     * @param mobile
     * @return
     */
    public List<EsSubscribe> yueDongCaiLingChuanWangFindByMobile(String mobile) {
        final List<String> channelList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).eq(BusinessChannelRights::getRemark,BIZ_TYPE_VRBT_CW_BIZ_TYPE).select(BusinessChannelRights::getBusinessChannel).list().stream().map(BusinessChannelRights::getBusinessChannel).collect(Collectors.toList());
        if(channelList==null || channelList.isEmpty()){
            log.info("悦动专属6元包-川网业务渠道权益未配置=>手机号:{}",mobile);
            return null;
        }
        List<EsSubscribe> esSubscribeList=esDataService.findEsSubscribeByMobileAndChannels(mobile, channelList);
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.info("悦动专属6元包-川网指定渠道权益未正确配置=>手机号:{},渠道号:{}",mobile,channelList);
            return null;
        }
        return esSubscribeList;
    }







    /**
     * 联通沃悦读单次扣款查询业务列表
     * @param mobile
     * @return
     */
    public FebsResponse woReadShopGetServiceList(String mobile)  {
        try {
            List<EsSubscribe> esSubscribeList=this.woReadShopFindByMobile(mobile);
            return woReadShopQueryServiceList(mobile, esSubscribeList);
        } catch (Exception e) {
            log.error("联通沃悦读查询业务列表=>手机号:{}",mobile,e);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }


    /**
     * 联通沃悦读单次扣款根据手机号查询es订购数据
     * @param mobile
     * @return
     */
    public List<EsSubscribe> woReadShopFindByMobile(String mobile) {
        final List<String> businessIdList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).eq(BusinessChannelRights::getRemark,BIZ_TYPE_WO_READ_SHOP).select(BusinessChannelRights::getId).list().stream().map(BusinessChannelRights::getId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("联通沃悦读单次扣款业务未配置=>手机号:{}",mobile);
            return null;
        }

        final List<String> channelList= businessPackService.lambdaQuery().select(BusinessPack::getServiceId).in(BusinessPack::getBusinessId,businessIdList).list().stream().map(BusinessPack::getServiceId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("联通沃悦读单次扣款业务渠道权益未配置=>手机号:{}",mobile);
            return null;
        }
        List<EsSubscribe> esSubscribeList=esDataService.findEsSubscribeByMobileAndChannels(mobile, channelList);
        if(esSubscribeList==null || esSubscribeList.isEmpty()){
            log.info("联通沃悦读单次扣款指定渠道权益未正确配置=>手机号:{},联通沃悦读业务类型:{}",mobile,BIZ_TYPE_WO_READ_SHOP);
            return null;
        }
        return esSubscribeList;
    }
    private FebsResponse woReadShopQueryServiceList(String mobile, List<EsSubscribe> esSubscribeList) {
        if (esSubscribeList == null || esSubscribeList.isEmpty()) {
            log.info("联通沃悦读单次扣款查询业务列表-ES订单暂无数据=>手机号:{}", mobile);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        for (EsSubscribe esSubscribe : esSubscribeList) {
            BusinessPack businessPack = businessPackService.lambdaQuery().select(BusinessPack::getServiceApiBeanName,BusinessPack::getServiceId).eq(BusinessPack::getServiceId,esSubscribe.getChannel()).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.info("联通沃悦读单次扣款查询业务列表-渠道号-业务渠道权益关联未匹配=>手机号:{},ES渠道号:{}", mobile, esSubscribe.getChannel());
                continue;
            }
            final String serviceApiBeanName = businessPack.getServiceApiBeanName();
            final String serviceId = businessPack.getServiceId();
            log.info("联通沃悦读单次扣款查询业务列表-ApiBean配置=>手机号:{},ApiBean:{},权益领取业务ID:{},ES渠道号:{}", mobile, serviceApiBeanName, serviceId, esSubscribe.getChannel());
            if (StringUtils.isBlank(serviceApiBeanName) || StringUtils.isBlank(serviceId)) {
                log.info("联通沃悦读单次扣款查询业务列表-渠道号-业务渠道权益关联未配置=>手机号:{},ES渠道号:{}", mobile, esSubscribe.getChannel());
                continue;
            }
            //校验权益是否包月以及领取
            final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessRightsSubService.class);
            FebsResponse memberVerifyFebs = businessRightsSubService.memberVerify(mobile, serviceId);
            FebsResponse rechargRecordVerifyFebs = businessRightsSubService.rechargRecordVerify(mobile, serviceId);
            if(memberVerifyFebs.isOK() && rechargRecordVerifyFebs.isOK()) {
                return new FebsResponse().success().data(esSubscribe.getChannel());
            }
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }

    public FebsResponse queryRightsListById(String id) {
        return rightsSubService.queryRightsListById(id);
    }



    /**
     * 通用权益列表查询（包含支付宝）
     * @param channel
     * @return
     */
    public FebsResponse queryLianLianRightsListByChannel(String channel, HttpServletRequest request){
        final String mobiles = request.getHeader("mobile");
        log.info("通用权益列表查询=>手机号:{},渠道号:{}",mobiles,channel);
        //查询有效渠道权益
        final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getBusinessChannel,channel).eq(BusinessChannelRights::getRemark,CHANNEL_BEAN).list();
        if(businessList==null || businessList.isEmpty()){
            log.info("通用权益列表查询-指定渠道权益未正确配置=>手机号:{},渠道号:{}",mobiles,channel);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        final List<String> businessIdList=businessList.stream().map(BusinessChannelRights::getId).collect(Collectors.toList());
        //业务类型去重
        final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().select(BusinessPack::getServiceId,BusinessPack::getPackId).in(BusinessPack::getBusinessId,businessIdList).list();
        if(businessPackList==null || businessPackList.isEmpty()){
            log.info("通用权益列表查询-业务渠道权益关联未正确配置=>手机号:{},渠道号:{}",mobiles,channel);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        List<LianlianProduct> lianlianProductList=Lists.newArrayList();
        final List<String> businessPackIdList=businessPackList.stream().map(BusinessPack::getPackId).collect(Collectors.toList());
        if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
            List<RightsPackDto> list= rightsSubService.queryLianLianWebRightsList(businessPackList.get(0).getServiceId(),businessPackIdList);
            list.forEach(item -> {
                    item.getRightsPackList().forEach(product -> {
                        Integer itemId=Integer.valueOf(product.getRightsId());
                        Integer productId=Integer.valueOf(product.getCouponId());
                        LianlianProduct lianlianProduct=lianlianProductService.lambdaQuery().select(LianlianProduct::getOnlyName,LianlianProduct::getFaceImg,LianlianProduct::getOriginPrice, LianlianProduct::getProductId,LianlianProduct::getItemId, LianlianProduct::getCodeType, LianlianProduct::getStock, LianlianProduct::getBookingType, LianlianProduct::getBookingShowAddress, LianlianProduct::getOrderShowIdCard, LianlianProduct::getOrderShowDate, LianlianProduct::getBeginTime, LianlianProduct::getEndTime, LianlianProduct::getValidBeginDate, LianlianProduct::getValidEndDate,LianlianProduct::getBookingBeginDate, LianlianProduct::getReleaseTime).eq(LianlianProduct::getProductId,productId).eq(LianlianProduct::getItemId,itemId).orderByDesc(LianlianProduct::getCreateTime).last("limit 1").one();
                        if(lianlianProduct!=null){
                            lianlianProduct.setPackName(item.getPackName());
                            lianlianProduct.setServiceId(item.getServiceId());
                            lianlianProductList.add(lianlianProduct);
                        }
                    });
            });
            return new FebsResponse().success().data(lianlianProductList);
        }
        List<RightsPackDto> list=rightsSubService.queryLianLianRightsList(businessPackList.get(0).getServiceId(),businessPackIdList);
        list.forEach(item -> {
            item.getRightsPackList().forEach(product -> {
                Integer itemId=Integer.valueOf(product.getRightsId());
                Integer productId=Integer.valueOf(product.getCouponId());
                LianlianProduct lianlianProduct=lianlianProductService.lambdaQuery().select(LianlianProduct::getOnlyName,LianlianProduct::getFaceImg,LianlianProduct::getOriginPrice, LianlianProduct::getProductId,LianlianProduct::getItemId, LianlianProduct::getCodeType, LianlianProduct::getStock, LianlianProduct::getBookingType, LianlianProduct::getBookingShowAddress, LianlianProduct::getOrderShowIdCard, LianlianProduct::getOrderShowDate, LianlianProduct::getBeginTime, LianlianProduct::getEndTime, LianlianProduct::getValidBeginDate, LianlianProduct::getValidEndDate,LianlianProduct::getBookingBeginDate, LianlianProduct::getReleaseTime).eq(LianlianProduct::getProductId,productId).eq(LianlianProduct::getProductId,productId).eq(LianlianProduct::getItemId,itemId).orderByDesc(LianlianProduct::getCreateTime).last("limit 1").one();
                if(lianlianProduct!=null){
                    lianlianProduct.setPackName(item.getPackName());
                    lianlianProduct.setServiceId(item.getServiceId());
                    lianlianProductList.add(lianlianProduct);
                }
            });
        });
        return new FebsResponse().success().data(lianlianProductList);
    }



    /**
     * 联联分销通用权益领取接口（包含支付宝）
     * @param mobile
     * @return
     */
    public FebsResponse createLianLianScheduledRecharge(String orderId,String serviceId,String packName,String channel,String mobile,String productId,String itemId,String idCard,String customerName,String address,String memo,String travelDate, HttpServletRequest request)  {
        log.info("联联分销通用权益领取=>手机号:{},权益领取业务ID:{},权益包:{},产品ID:{},套餐ID:{},渠道号:{}",mobile,serviceId,packName,productId,itemId,channel);
//        if(!this.isLegal(channel,itemId,request)){
//            log.warn("联联分销通用权益领取-权益领取非法=>手机号:{},权益领取业务ID:{},权益包:{},产品ID:{},套餐ID:{},渠道号:{}",mobile,serviceId,packName,productId,itemId,channel);
//            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//        };
        try {
            if(StringUtils.isEmpty(serviceId)){
                log.warn("联联分销通用权益领取-渠道号-业务ID错误=>手机号:{},权益领取业务ID:{},渠道号:{}", mobile, serviceId, channel);
                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
            }
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, serviceId).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(businessPack==null){
                log.info("联联分销通用权益领取-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",mobile,serviceId,channel);
                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
            }
            final String serviceApiBeanName =businessPack.getServiceApiBeanName();
            log.info("联联分销通用权益领取-ApiBean配置=>手机号:{},ApiBean:{},权益领取业务ID:{},权益包:{},产品ID:{},套餐ID:{},渠道号:{}",mobile,serviceApiBeanName,serviceId,packName,productId,itemId,channel);
            if(StringUtils.isBlank(serviceApiBeanName) || StringUtils.isBlank(serviceId)){
                log.info("联联分销通用权益领取-渠道号-业务渠道权益关联未配置=>手机号:{},权益领取业务ID:{},渠道号:{}", mobile, serviceId,channel);
                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
            }
            final IBusinessLianLianRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessLianLianRightsSubService.class);
            final String mobiles = request.getHeader("mobile");
            if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
                FebsResponse createScheduledRechargeFebs=businessRightsSubService.webCreateScheduledRecharge(orderId, serviceId, packName, channel, mobile, productId, itemId, idCard, customerName, address, memo,travelDate);
                return createScheduledRechargeFebs;
            }
            FebsResponse createScheduledRechargeFebs=businessRightsSubService.createScheduledRecharge(orderId,serviceId, packName, channel, mobile, productId, itemId, idCard, customerName, address, memo,travelDate);
            return createScheduledRechargeFebs;
        } catch (Exception e) {
            log.error("通用权益领取异常=>手机号:{}",mobile,e);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }



    /**
     * 查询权益是否非法
     * @param channel
     * @return
     */
//    public boolean isLegal(String channel,String rightsId, HttpServletRequest request){
//        final String mobiles = request.getHeader("mobile");
//        log.info("查询权益是否非法=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//        //特定省份特定权益配置
//        if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
//            EsSubscribe subscribe=esDataService.findEsSubscribeByMobileAndChannel(mobiles,channel);
//            if(subscribe!=null){
//                ProvinceRights provinceRights=provinceRightsService.lambdaQuery().select(ProvinceRights::getRightsChannel).eq(ProvinceRights::getChannel,channel).eq(ProvinceRights::getProvince,subscribe.getProvince()).le(ProvinceRights::getSubTime,DateUtil.getDateFormat(subscribe.getCreateTime(),DateUtil.FULL_TIME_SPLIT_PATTERN)).orderByDesc(ProvinceRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//                if(provinceRights!=null){
//                    channel=provinceRights.getRightsChannel();
//                }
//            }
//        }
//        //查询有效渠道权益
//        final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getBusinessChannel,channel).list();
//        if(businessList==null || businessList.isEmpty()){
//            //支付宝周期扣款权益礼包和联通沃悦读单次扣款特定业务特殊判断
//            final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().select(BusinessPack::getServiceId,BusinessPack::getPackId).eq(BusinessPack::getServiceId,channel).list();
//            if(!businessPackList.isEmpty()){
//                final List<String> businessPackIdList=businessPackList.stream().map(BusinessPack::getPackId).collect(Collectors.toList());
//                if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
//                    FebsResponse febsResponse= rightsSubService.queryWebRightsList(channel,businessPackIdList);
//                    if(!febsResponse.isOK() || febsResponse.get("data")==null){
//                        log.info("支付宝网页权益配置错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                        return false;
//                    }
//                    List<RightsPackDto> rightsList = JSONObject.parseArray(febsResponse.get("data").toString(),RightsPackDto.class);
//                    if(rightsList==null || rightsList.isEmpty()){
//                        log.info("支付宝网页权益查询错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                        return false;
//                    }
//                    boolean hasSuccess = rightsList.stream().anyMatch(item->  item.getRightsPackList().stream().anyMatch( items->rightsId.equals(items.getRightsId()) ) );
//                    if (hasSuccess){
//                        return true;
//                    }
//                    log.info("支付宝网页权益非法=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                    return false;
//                }
//                FebsResponse febsResponse=rightsSubService.queryRightsList(channel,businessPackIdList);
//                if(!febsResponse.isOK() || febsResponse.get("data")==null){
//                    log.info("支付宝登录权益配置错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                    return false;
//                }
//                List<RightsPackDto> rightsList = JSONObject.parseArray(febsResponse.get("data").toString(),RightsPackDto.class);
//                if(rightsList==null || rightsList.isEmpty()){
//                    log.info("支付宝登录权益查询错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                    return false;
//                }
//                boolean hasSuccess = rightsList.stream().anyMatch(item->  item.getRightsPackList().stream().anyMatch( items->rightsId.equals(items.getRightsId()) ) );
//                if (hasSuccess){
//                    return true;
//                }
//                log.info("支付宝登录权益非法=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                return false;
//            }
//            log.info("通用权益列表查询-指定渠道权益未正确配置=>手机号:{},渠道号:{}",mobiles,channel);
//            return false;
//        }
//        final List<String> businessIdList=businessList.stream().map(BusinessChannelRights::getId).collect(Collectors.toList());
//        //业务类型去重
//        final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().select(BusinessPack::getServiceId,BusinessPack::getPackId).in(BusinessPack::getBusinessId,businessIdList).list();
//        if(businessPackList==null || businessPackList.isEmpty()){
//            log.info("查询权益是否非法-业务渠道权益关联未正确配置=>手机号:{},渠道号:{}",mobiles,channel);
//            return false;
//        }
//        final List<String> businessPackIdList=businessPackList.stream().map(BusinessPack::getPackId).collect(Collectors.toList());
//        if (StringUtils.isNotBlank(mobiles) && mobiles.matches(BizConstant.MOBILE_REG)) {
//            FebsResponse febsResponse=rightsSubService.queryWebRightsList(businessPackList.get(0).getServiceId(),businessPackIdList);
//            if(!febsResponse.isOK() || febsResponse.get("data")==null){
//                log.info("网页权益配置错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                return false;
//            }
//            List<RightsPackDto> rightsList = JSONObject.parseArray(febsResponse.get("data").toString(),RightsPackDto.class);
//            if(rightsList==null || rightsList.isEmpty()){
//                log.info("网页权益查询错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//                return false;
//            }
//            boolean hasSuccess = rightsList.stream().anyMatch(item->  item.getRightsPackList().stream().anyMatch( items->rightsId.equals(items.getRightsId()) ) );
//            if (hasSuccess){
//                return true;
//            }
//            log.info("网页权益非法=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//            return false;
//        }
//        FebsResponse febsResponse=rightsSubService.queryRightsList(businessPackList.get(0).getServiceId(),businessPackIdList);
//        if(!febsResponse.isOK() || febsResponse.get("data")==null){
//            log.info("网页权益配置错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//            return false;
//        }
//        List<RightsPackDto> rightsList = JSONObject.parseArray(febsResponse.get("data").toString(),RightsPackDto.class);
//        if(rightsList==null || rightsList.isEmpty()){
//            log.info("网页权益查询错误=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//            return false;
//        }
//        boolean hasSuccess = rightsList.stream().anyMatch(item->  item.getRightsPackList().stream().anyMatch( items->rightsId.equals(items.getRightsId()) ) );
//        if (hasSuccess){
//            return true;
//        }
//        log.info("网页权益非法=>手机号:{},渠道号:{},权益ID:{}",mobiles,channel,rightsId);
//        return false;
//
//    }
}
