package com.eleven.cms.remote;

import com.eleven.cms.config.BeijingYihuiShandongProperties;
import com.eleven.cms.config.BeijingYihuiYidongProperties;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.BeijingYihuiResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Map;

/**
 * 爱豆来电接口
 *
 * @author: cai lei
 * @create: 2023-11-13 10:28
 */
@Slf4j
@Service
public class BeijingYihuiYidongService {

    private OkHttpClient client;
    private ObjectMapper mapper;

    private static final MediaType mediaType = MediaType.parse("application/json");
    public static final String LOG_TAG = "北京亿汇移动API";

    @Autowired
    private BeijingYihuiYidongProperties beijingYihuiYidongProperties;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public BeijingYihuiResult getSms(String mobile, String ip, String province) {
        final HttpUrl httpUrl = HttpUrl.parse(beijingYihuiYidongProperties.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("channelNo", beijingYihuiYidongProperties.getChannelNo())
                .addQueryParameter("price", "1500")
                .addQueryParameter("phone", mobile)
                .addQueryParameter("ip", ip)
                .addQueryParameter("sfcode", BizConstant.provinceCodeMap.get(province))
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG, mobile, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, mobile, content);
            return mapper.readValue(content, BeijingYihuiResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, mobile, e);
            return BeijingYihuiResult.fail();
        }
    }

    public BeijingYihuiResult smsCode(String mobile, String orderNo, String code) {
        final HttpUrl httpUrl = HttpUrl.parse(beijingYihuiYidongProperties.getSmsCodeUrl())
                .newBuilder()
                .addQueryParameter("vcode", code)
                .addQueryParameter("linkid", orderNo)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},请求:{}", LOG_TAG, mobile, code, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},响应:{}", LOG_TAG, mobile, code, content);
            return mapper.readValue(content, BeijingYihuiResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},异常:", LOG_TAG, mobile, code, e);
            return BeijingYihuiResult.fail();
        }
    }
}
