package com.eleven.cms.remote;

import com.eleven.cms.config.AidoulaidianProperties;
import com.eleven.cms.config.ShoujizixunConfig;
import com.eleven.cms.config.ShoujizixunProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ShoujizixunResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * 爱豆来电接口
 *
 * @author: cai lei
 * @create: 2023-11-13 10:28
 */
@Slf4j
@Service
public class ShoujizixunService {

    private OkHttpClient client;
    private ObjectMapper mapper;

    private static final MediaType mediaType = MediaType.parse("application/json");
    public static final String LOG_TAG = "手机资讯API";

    @Autowired
    private ShoujizixunProperties shoujizixunProperties;


    //     @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public ShoujizixunResult getSms(String mobile, String ip, String cpparam, String channel) {
        ShoujizixunConfig shoujizixunConfig = shoujizixunProperties.getChannelConfigMap().get(channel);
        final HttpUrl httpUrl = HttpUrl.parse(shoujizixunConfig.getGetSmsUrl())
                .newBuilder()
                .addQueryParameter("msg", shoujizixunConfig.getMsg())
                .addQueryParameter("phone", mobile)
                .addQueryParameter("spnumber", shoujizixunConfig.getSpnumber())
                .addQueryParameter("price", shoujizixunConfig.getPrice())
                .addQueryParameter("orderIp", ip)
                .addQueryParameter("cpparam", cpparam)
                .build();
        log.info("{}-获取短信-手机号:{},渠道号:{},请求:{}", LOG_TAG, mobile, channel, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},渠道号:{},响应:{}", LOG_TAG, mobile, channel, content);
            return mapper.readValue(content, ShoujizixunResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},渠道号:{},异常:", LOG_TAG, mobile, channel, e);
            return ShoujizixunResult.fail();
        }
    }

    public ShoujizixunResult smsCode(String mobile, String orderNo, String code, String channel) {
        ShoujizixunConfig shoujizixunConfig = shoujizixunProperties.getChannelConfigMap().get(channel);
        final HttpUrl httpUrl = HttpUrl.parse(shoujizixunConfig.getSmsCodeUrl())
                .newBuilder()
                .addQueryParameter("vcode", code)
                .addQueryParameter("order_no", orderNo)
                .build();
        log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},请求:{}", LOG_TAG, mobile, code, channel, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},响应:{}", LOG_TAG, mobile, code, channel, content);
            return mapper.readValue(content, ShoujizixunResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交短信-手机号:{},验证码:{},渠道号:{},异常:", LOG_TAG, mobile, code, channel, e);
            return ShoujizixunResult.fail();
        }
    }
}
