package com.eleven.cms.remote;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.eleven.cms.config.LemobaProperties;
import com.eleven.cms.entity.LemobaChargeLog;
import com.eleven.cms.service.ILemobaChargeLogService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.LemobaResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:46
 * Desc:咪咕sdk相关api(开放平台)
 */
@Slf4j
@Service
public class LemobaApiService {

    public static final String LOG_TAG = "乐摩吧权益api";


    @Autowired
    private LemobaProperties lemobaProperties;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private Environment environment;
    @Autowired
    private ILemobaChargeLogService cmsLemobaChargeLogService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private DateTimeFormatter dateTimeFormatter;
    private AES aes;
    private static final String IV_STRING = "A-16-Byte-String";

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
//        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
//            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
//            final String proxyHost = "**************";
//            final int proxyPort = 9999;
//            java.net.Authenticator.setDefault(new java.net.Authenticator() {
//                @Override
//                protected PasswordAuthentication getPasswordAuthentication() {
//                    if (getRequestingHost().equalsIgnoreCase(proxyHost)) {
//                        return new PasswordAuthentication("s5", "rkxXolZ_kvQx0df3lW7AnqlK".toCharArray());
//                    }
//                    return null;
//                }
//            });
//            this.client = this.client.newBuilder()
//                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
//                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
//                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(proxyHost, proxyPort)))
//                    .build();
//            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
//        }
        this.mapper = new ObjectMapper();
        this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);


//        byte[] initParam = IV_STRING.getBytes(StandardCharsets.UTF_8);
//        IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
        aes = new AES(Mode.CBC, Padding.PKCS5Padding, lemobaProperties.getAesKey().getBytes(StandardCharsets.UTF_8), IV_STRING.getBytes(StandardCharsets.UTF_8));
    }

    public Result<?> buildUserCenterUrl(String orderId, String phone, String channelCode) {
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        //查询本月是否发送乐摩吧权益
        Integer count = cmsLemobaChargeLogService.lambdaQuery().eq(LemobaChargeLog::getMobile, phone).eq(LemobaChargeLog::getRightsMonth, rightsMonth).eq(LemobaChargeLog::getChannelCode, channelCode).count();
        if(count<=0){
            String rightsId=lemobaProperties.getChannelMap().get(channelCode).getRightsId();
            ObjectNode rawData = mapper.createObjectNode();
            rawData.put("guid", orderId);
            rawData.put("phone", phone);
            rawData.put("gift_no", rightsId);
            String encryptBase64 = aes.encryptBase64(rawData.toString());
            log.info("{}-权益发放,guid:{},phone:{},gift_no:{}", LOG_TAG, orderId, phone, rightsId);
            LemobaChargeLog cmsLemobaChargeLog=new LemobaChargeLog();
            cmsLemobaChargeLog.setMobile(phone);
            cmsLemobaChargeLog.setOrderId(orderId);
            cmsLemobaChargeLog.setRightsMonth(rightsMonth);
            cmsLemobaChargeLog.setChannelCode(channelCode);
            cmsLemobaChargeLog.setCreateTime(new Date());
            cmsLemobaChargeLog.setModifyTime(new Date());
            cmsLemobaChargeLogService.save(cmsLemobaChargeLog);
            String userCenterUrl=new StringBuilder()
                    .append(lemobaProperties.getUserCenterUrl())
                    .append(encryptBase64)
                    .toString();
            log.info("咪咕白金会员回调，乐摩吧权益领取地址->:mobile:{},userCenterUrl:{}", phone,userCenterUrl);
            return Result.okObj(userCenterUrl);
        }else{
            return Result.ok("乐摩吧权益已领取！");
        }
    }

    //fail {"code":10099,"msg":"不存在该领取记录","data":null}
    //success {"code":1,"msg":"操作成功","data":null}
    public LemobaResult unSubscribe(String orderId, String phone, String channelCode) {
        String rightsId=lemobaProperties.getChannelMap().get(channelCode).getRightsId();
        ObjectNode rawData = mapper.createObjectNode();
        rawData.put("guid", orderId);
        rawData.put("phone", phone);
        rawData.put("gift_no", rightsId);
        String encryptBase64 = aes.encryptBase64(rawData.toString());
        RequestBody formBody = new FormBody.Builder()
//                .add("guid", orderId)
//                .add("phone", phone)
//                .add("gift_no", rightsId)
                .add("encrypdata", encryptBase64)
                .build();

        Request request = new Request.Builder()
                .url(lemobaProperties.getRefundUrl())
                .post(formBody)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-产品退订-guid:{},phone:{},gift_no:{},响应:{}", LOG_TAG, orderId, phone, rightsId, content);
            return mapper.readValue(content, LemobaResult.class);
        } catch (IOException e) {
            log.info("{}-产品退订-guid:{},phone:{},gift_no:{},异常:", LOG_TAG, orderId, phone, rightsId, e);
            return LemobaResult.fail();
        }

    }


}
