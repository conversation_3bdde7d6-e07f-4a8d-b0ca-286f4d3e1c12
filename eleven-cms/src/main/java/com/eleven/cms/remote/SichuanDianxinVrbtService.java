package com.eleven.cms.remote;

import com.eleven.cms.config.SichuanDianxinVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.SichuanDianxinVrbtResult;
import com.eleven.cms.vo.SichuanMobileResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: cai lei
 * @create: 2021-12-14 14:21
 */

@Slf4j
@Service
public class SichuanDianxinVrbtService {

    public static final String LOG_TAG = "四川电信视频彩铃流量包api";
    public static final String DELIMITER_AMP = "&";
    private OkHttpClient client;
    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");


    @Autowired
    private Environment environment;
    @Autowired
    SichuanDianxinVrbtProperties sichuanDianxinVrbtProperties;


    //    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();

            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper();
    }


    /**
     * 获取验证码
     *
     * @param phone
     */
    public SichuanDianxinVrbtResult getSmsCode(String phone, String ecpId) throws Exception {
        String url = sichuanDianxinVrbtProperties.getSmsCodeUrl();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("accNbr", phone);
        dataMap.put("offerCombId", ecpId);
        dataMap.put("activityCode", sichuanDianxinVrbtProperties.getActivityCode());
        dataMap.put("channelId", sichuanDianxinVrbtProperties.getChannelId());
        dataMap.put("key", "8271B0F856E64126948FE42B8452B315");
        String sign = generateSign(dataMap, new ArrayList<>());
        dataMap.put("sign", sign);
        String requestBody = mapper.writeValueAsString(dataMap);
        RequestBody body = RequestBody.create(mediaType, requestBody);
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-验证码下发-手机号:{},请求:{},requestBody:{}", LOG_TAG, phone, request.toString(), requestBody);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-验证码下发-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, SichuanDianxinVrbtResult.class);
        } catch (Exception e) {
            log.info("{}-验证码下发-手机号:{},异常:", LOG_TAG, phone, e);
            return SichuanDianxinVrbtResult.fail();

        }
    }

    /**
     * 订购接口
     *
     * @param phone
     * @param code
     * @param ticket
     */
    public SichuanDianxinVrbtResult order(String phone, String code, String ticket) throws Exception {
        String url = sichuanDianxinVrbtProperties.getOrderUrl();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("accNbr", phone);
        dataMap.put("randomStr", code);
        dataMap.put("ticket", ticket);
        dataMap.put("channelId", sichuanDianxinVrbtProperties.getChannelId());
        dataMap.put("subChannelId", sichuanDianxinVrbtProperties.getSubChannelId());
        dataMap.put("key", "AA5E94D69E7C4EA7B7BD53AE5DBFBFA6");
        String sign = generateSign(dataMap, Arrays.asList("subChannelId"));
        dataMap.put("sign", sign);
        String requestBody = mapper.writeValueAsString(dataMap);
        RequestBody body = RequestBody.create(mediaType, requestBody);
        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-订购-手机号:{},ticket:{},请求:{},requestBody:{}", LOG_TAG, phone, ticket, request.toString(), requestBody);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-订购-手机号:{},ticket:{},返回结果:{}", LOG_TAG, phone, ticket, result);
            return mapper.readValue(result, SichuanDianxinVrbtResult.class);
        } catch (Exception e) {
            log.info("{}-订购-手机号:{},ticket:{},异常:", LOG_TAG, phone, ticket, e);
            return SichuanDianxinVrbtResult.fail();
        }
    }

    /**
     * 白名单查询
     *
     * @param phone
     */
    public SichuanDianxinVrbtResult queryWhite(String phone) {
        String url = sichuanDianxinVrbtProperties.getQueryWhiteUrl();
        final HttpUrl httpUrl = HttpUrl.parse(url).newBuilder().build();
        RequestBody body = new FormBody.Builder()
                .add("accNbr", phone)
                .add("channelId", sichuanDianxinVrbtProperties.getChannelId())
                .add("activityCode", sichuanDianxinVrbtProperties.getActivityCode())
                .add("key", "AA5E94D69E7C4EA7B7BD53AE5DBFBFA6")
                .build();

        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-白名单查询-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-白名单查询-手机号:{},返回结果:{}", LOG_TAG, phone, result);
            return mapper.readValue(result, SichuanDianxinVrbtResult.class);
        } catch (Exception e) {
            log.info("{}-白名单查询-手机号:{},异常:", LOG_TAG, phone, e);
            return SichuanDianxinVrbtResult.fail();
        }
    }

    /**
     * 订单查询
     *
     * @param otherNo
     */
    public SichuanDianxinVrbtResult queryOrder(String otherNo) {
        String url = sichuanDianxinVrbtProperties.getQueryOrderUrl();
        RequestBody body = new FormBody.Builder()
                .add("otherNo", otherNo)
                .add("channelId", sichuanDianxinVrbtProperties.getChannelId())
                .add("key", "AA5E94D69E7C4EA7B7BD53AE5DBFBFA6")
                .build();

        Request request = new Request.Builder().url(url)
                .post(body)
                .build();

        log.info("{}-订单查询-三方订单号:{},请求:{}", LOG_TAG, otherNo, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-订单查询-三方订单号:{},返回结果:{}", LOG_TAG, otherNo, result);
            return mapper.readValue(result, SichuanDianxinVrbtResult.class);
        } catch (Exception e) {
            log.info("{}-订单查询-三方订单号:{},异常:", LOG_TAG, otherNo, e);
            return SichuanDianxinVrbtResult.fail();
        }
    }

    /**
     * @param dataMap     源map
     * @param excludeList 需要排除字段的集合
     * @return
     * @throws UnsupportedEncodingException
     */
    private static String generateSign(Map<String, Object> dataMap, List<String> excludeList) throws UnsupportedEncodingException {
        String params = dataMap.entrySet().stream().filter(key -> !excludeList.contains(key.getKey())).map(key -> key + DELIMITER_AMP).collect(Collectors.joining());
        if (params.endsWith(DELIMITER_AMP)) {
            params = params.substring(0, params.lastIndexOf(DELIMITER_AMP));
        }
        System.out.println(params);
        return DigestUtils.md5DigestAsHex(params.getBytes(StandardCharsets.UTF_8.name()));
    }

    public static void main(String[] args) {
        List<String> list = Arrays.asList("a");
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("a", "1");
        dataMap.put("b", "2");
        String params = dataMap.entrySet().stream().filter(key -> !list.contains(key.getKey())).map(key -> key + DELIMITER_AMP).collect(Collectors.joining());
        System.out.println(params);


    }
}
