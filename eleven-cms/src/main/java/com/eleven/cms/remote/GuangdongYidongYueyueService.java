package com.eleven.cms.remote;

import com.eleven.cms.config.GuangdongYidongYueyueProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.GuangdongYidongYueyueResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @author: cai lei
 * @create: 2022-08-12 10:58
 */
@Slf4j
@Service
public class GuangdongYidongYueyueService {

    public static final String LOG_TAG = "广东移动悦悦视频彩铃api";


    @Autowired
    private Environment environment;
    @Autowired
    private GuangdongYidongYueyueProperties guangdongYidongYueyueProperties;
    @Autowired
    RedisUtil redisUtil;

    public static final MediaType JSONTYPE
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    private OkHttpClient client;
    private ObjectMapper mapper;


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ;
    }

    //{"status":11,"msg":"成功","ordernum":"4028827c84c2c4950184c2c4958d0000","sysordernum":"15b10c40c36d3048e8e87f79ce089dce","dataobj":{"phonenum":"15915401656"}}
    public @Nonnull
    GuangdongYidongYueyueResult getSms(String phone, String orderNum) throws UnsupportedEncodingException {
        String signStr = guangdongYidongYueyueProperties.getProdId() + orderNum + guangdongYidongYueyueProperties.getAppId() + phone + guangdongYidongYueyueProperties.getAppKey();
        ObjectNode data = mapper.createObjectNode();
        data.put("phonenum", phone);
        data.put("appid", guangdongYidongYueyueProperties.getAppId());
        data.put("ordernum", orderNum);
        data.put("prodid", guangdongYidongYueyueProperties.getProdId());
        data.put("sign", DigestUtils.md5DigestAsHex(signStr.getBytes(StandardCharsets.UTF_8.name())));  //签名MD5(prodid+ordernum+appid+phonenum+业务方KEY)转小写
        RequestBody body = RequestBody.create(JSONTYPE, data.toString());
        Request request = new Request.Builder()
                .url(guangdongYidongYueyueProperties.getGetSmsUrl())
                .post(body)
                .build();
        log.info("{}-获取短信-手机号:{},请求:{}", LOG_TAG, phone, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取短信-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, GuangdongYidongYueyueResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return GuangdongYidongYueyueResult.fail();
        }
    }

    public @Nonnull
    GuangdongYidongYueyueResult smsCode(String phone, String code) throws UnsupportedEncodingException {

        GuangdongYidongYueyueResult guangdongYidongYueyueResult = (GuangdongYidongYueyueResult) redisUtil.get(CacheConstant.CMS_CACHE_GUANGDONG_SMS_DATA + phone);
        if (guangdongYidongYueyueResult == null) {
            return GuangdongYidongYueyueResult.fail();
        }

        String signStr = guangdongYidongYueyueProperties.getAppId() + guangdongYidongYueyueResult.getSysordernum() + code + guangdongYidongYueyueProperties.getAppKey();
        ObjectNode data = mapper.createObjectNode();
        data.put("sysordernum", guangdongYidongYueyueResult.getSysordernum());
        data.put("appid", guangdongYidongYueyueProperties.getAppId());
        data.put("dataobj", guangdongYidongYueyueResult.getDataobj());
        data.put("smscode", code);
        data.put("sign", DigestUtils.md5DigestAsHex(signStr.getBytes(StandardCharsets.UTF_8.name()))); //签名MD5(appid+sysordernum+smscode+业务方KEY)转小写
        RequestBody body = RequestBody.create(JSONTYPE, data.toString());
        Request request = new Request.Builder()
                .url(guangdongYidongYueyueProperties.getSmsCodeUrl())
                .post(body)
                .build();
        log.info("{}-提交验证码-手机号:{},短信验证码:{},请求:{}", LOG_TAG, phone,code, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-手机号:{},短信验证码:{},响应:{}", LOG_TAG, phone, code,content);
            return mapper.readValue(content, GuangdongYidongYueyueResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-提交验证码-手机号:{},短信验证码:{},异常:", LOG_TAG, phone,code, e);
            return GuangdongYidongYueyueResult.fail();
        }
    }
}
