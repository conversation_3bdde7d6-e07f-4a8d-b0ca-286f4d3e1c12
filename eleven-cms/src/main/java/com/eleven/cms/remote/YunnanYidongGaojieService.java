package com.eleven.cms.remote;
import com.eleven.cms.config.YunnanYidongGaojieProperties;
import com.eleven.cms.config.YunnanYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * @author: lihb
 * @create: 2022-9-20 11:40:24
 */
@Slf4j
@Service
public class YunnanYidongGaojieService {

    public static final String LOG_TAG = "云南移动高姐业务api";

    @Autowired
    private YunnanYidongGaojieProperties yunnanYidongGaojieProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");

    public static final String YN_GAOJIE_DATA_KEY_PREFIX = "yn::gaojie:";
    public static final long YN_GAOJIE_DATA_CACHE_SECONDS = 300;



    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }



    /**
     * 验证码下发
     * phone 手机号
     * channel 渠道号
     * @param phone
     * @return
     */
    public YunnanMobileGaojieResult getSms(String phone, String channel) {
        try {

            ObjectNode content = mapper.createObjectNode();
            //业务参数
            content.put("phone", phone);
            content.put("fluent", "1");
            content.put("subChannel", "yr");
            content.put("pushChannel", "yr");
            content.put("offerId", yunnanYidongGaojieProperties.getBizCodeByChannel(channel));
            content.put("channel", "yunnan_bojing_yr");

            String url = yunnanYidongGaojieProperties.getGetSmsurl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .build();
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(content));
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();
            log.info("{}-验证码下发-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-验证码下发-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            YunnanMobileGaojieResult yunnanMobileGaojieResult = mapper.readValue(result, YunnanMobileGaojieResult.class);
            if(yunnanMobileGaojieResult.isOK()){
                String key = YN_GAOJIE_DATA_KEY_PREFIX + phone;
                redisUtil.set(key, yunnanMobileGaojieResult.getData(), YN_GAOJIE_DATA_CACHE_SECONDS);
            }
            return yunnanMobileGaojieResult;
        } catch (Exception e) {
            log.info("{}-验证码下发-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileGaojieResult.fail();

        }
    }

    /**
     * 提交验证码
     * phone 手机号
     * channel 渠道号
     * smsCode 验证码
     * @param phone
     * @return
     */
    public YunnanMobileGaojieResult smsCode(String phone, String channel,String smsCode) {
        try {
            String key = YN_GAOJIE_DATA_KEY_PREFIX + phone;
            YunnanMobileGaojieResult.Result data = (YunnanMobileGaojieResult.Result) redisUtil.get(key);
            if(data == null){
                return YunnanMobileGaojieResult.fail();
            }
            ObjectNode content = mapper.createObjectNode();
            //业务参数
            content.put("channel", "yunnan_bojing_yr");
            content.put("offerId", yunnanYidongGaojieProperties.getBizCodeByChannel(channel));
            content.put("pushChannel", "yr");
            content.put("subChannel", "yr");
            content.put("phone", phone);
            content.put("orderId", data.getOrderId());
            content.put("code", smsCode);
            content.put("smsId", data.getSmsId());
            content.put("flowId", data.getFlowId());
            content.put("fluent", "1");

            String url = yunnanYidongGaojieProperties.getSmsCodeurl();
            HttpUrl httpUrl = HttpUrl.parse(url)
                    .newBuilder()
                    .build();
            RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(content));
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();
            log.info("{}-提交验证码-手机号:{},渠道号:{},请求:{}", LOG_TAG, phone,channel, request.toString());

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},响应:{}", LOG_TAG, phone,channel, result);
            YunnanMobileGaojieResult yunnanMobileGaojieResult = mapper.readValue(result, YunnanMobileGaojieResult.class);
            return yunnanMobileGaojieResult;
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return YunnanMobileGaojieResult.fail();
        }
    }

}
