package com.eleven.cms.remote;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aivrbt.service.IAiRingService;
import com.eleven.cms.config.YycpProperties;
import com.eleven.cms.dto.ReportDTO;
import com.eleven.cms.entity.VrbtDiyVideo;
import com.eleven.cms.enums.ReportTypeEnum;
import com.eleven.cms.service.IVrbtDiyVideoService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.util.guizhou.Md5Utils;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Author: lihb
 * Date: 2023-6-20 15:06:29
 * Desc:视频彩铃DIY
 */
@Slf4j
@Service
public class YycpReportService {

    public static final String LOG_TAG = "一语成片上报-api";

    @Autowired
    private YycpProperties yycpProperties;
    @Autowired
    private IVrbtDiyVideoService vrbtDiyVideoService;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private ObjectMapper mapper;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private IAiRingService ringService;
    private DateTimeFormatter dateTimeFormatter;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    public final List<String> PROVINCES = Lists.newArrayList("88");
    private static final Map<String, String> PROVINCES_CODE_MAP = new HashMap<String, String>(){
        {
            put("北京","01");put("天津","02");put("河北","03");put("山西","04");put("内蒙古","05");put("辽宁","06");put("吉林","07");put("黑龙江","08");put("上海","09");put("江苏","10");
            put("浙江","11");put("安徽","12");put("福建","13");put("江西","14");put("山东","15");put("河南","16");put("湖北","17");put("湖南","18");put("广东","19");put("海南","20");
            put("广西","21");put("重庆","22");put("四川","23");put("贵州","24");put("云南","25");put("陕西","26");put("甘肃","27");put("青海","28"); put("宁夏","29");put("新疆","30");
            put("西藏","31");
        }
    };

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().readTimeout(240L, TimeUnit.SECONDS).writeTimeout(240L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            //this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }

        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.dateTimeFormatter = DateTimeFormatter.ofPattern(BizConstant.DF_PATTERN_MIGU_API);
    }



    /**
     * 接入平台调用“中音CMS”内容自动化申报接口进行视频彩铃内容信息上报（支持全网上线及分省定向上线）；
     * @param
     * @return
     */
    public String report(ReportDTO reportDTO){
        String phone = reportDTO.getPhone();
        String mvName = reportDTO.getMvName();
        String mvUrl = reportDTO.getMvUrl();
        String transactionId = reportDTO.getTransactionId();

        ObjectNode rootNode = mapper.createObjectNode();
        ObjectNode bodyNode = mapper.createObjectNode();
        ObjectNode headNode = mapper.createObjectNode();
        ArrayNode toneNode = mapper.createArrayNode();
        ArrayNode sprParametersNode = mapper.createArrayNode();
        String seq = System.currentTimeMillis() + "" + yycpProperties.getAccessPlatformId();

        try {
            //保存订单信息
            VrbtDiyVideo vrbtDiyVideo = new VrbtDiyVideo();
            vrbtDiyVideo.setMobile(phone);
            vrbtDiyVideo.setMvUrl(mvUrl);
            vrbtDiyVideo.setMvName(mvName);
            vrbtDiyVideo.setSeq(seq);
            vrbtDiyVideo.setTransactionId(transactionId);
            vrbtDiyVideo.setChannel(reportDTO.getChannelCode());
            vrbtDiyVideoService.save(vrbtDiyVideo);

            toneNode.add("M");
            sprParametersNode.add(mapper.createObjectNode().put("paraName","mvType").put("paraValue","7"));
            sprParametersNode.add(mapper.createObjectNode().put("paraName","type").put("paraValue","9"));
            sprParametersNode.add(mapper.createObjectNode().put("paraName","libraryType").put("paraValue","2"));//内容库分类
            sprParametersNode.add(mapper.createObjectNode().put("paraName","phone").put("paraValue",phone));//用户手机号码
            sprParametersNode.add(mapper.createObjectNode().put("paraName","provinceID").put("paraValue",provincesCode(phone)));//分发手机号所属省份ID
            sprParametersNode.add(mapper.createObjectNode().put("paraName","provinces").putPOJO("paraValue",PROVINCES));//分发手机号所属省份ID
            //构建body
            bodyNode.put("transactionID",transactionId);//事务ID,每次请求事务ID不能一样。同一个平台内值不能相同,必填
            bodyNode.put("cPID",yycpProperties.getCpId());
            bodyNode.set("toneType",toneNode);//制作产品类型,M：视频彩铃
            bodyNode.put("mvName",mvName);//视频名称，新增类型必填
            bodyNode.put("singerType","未分类");//歌手类型，新增类型必填, 值为空
            bodyNode.put("issuedArea","其他");//发行地区,新增类型必填
            bodyNode.put("actor",phone);//表演者，新增类型必填
            bodyNode.put("maker","1");//制片者名称，新增类型必填
            bodyNode.put("makerRate","100");//制片者拥有比例
            bodyNode.put("businessType","其他");//业务类型，新增类型必填
            bodyNode.put("wordAuthor","暂无");//词作者，新增类型必填
            bodyNode.put("songAuthor","");//曲作者，新增类型必填
            bodyNode.put("validPeriod","60");//有效期，新增类型必填，单位：月有效期填写说明
            bodyNode.put("singleRight","非独家授权");//授权方式，新增类型必填
            bodyNode.put("language","中文");//语言，新增类型必填
            bodyNode.put("origCopyrightSide","暂无");//原始版权方，新增类型必填
            bodyNode.put("mvUrl",mvUrl);//素材文件路径(横屏或者竖屏)
            bodyNode.put("description","暂无");//描述信息，新增必填
            bodyNode.set("sprParameters",sprParametersNode);//描述信息，新增必填
            bodyNode.put("generalAuthorization","/yycp/咪咕音乐有限公司数字音视频内容版权授权书【智象未来】.pdf");
            //构建head
            headNode.put("dID",yycpProperties.getDId());
            headNode.put("sEQ",seq);
            headNode.put("dIDPwd", Md5Utils.hash(seq + yycpProperties.getKey()));
            headNode.put("accessPlatformID",yycpProperties.getAccessPlatformId());
            headNode.put("syncType","00");

            rootNode.set("head",headNode);
            rootNode.set("body",bodyNode);

            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
            HttpUrl httpUrl = HttpUrl.parse(yycpProperties.getReportUrl())
                    .newBuilder()
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();
            log.info("{}-请求:{},手机号:{},body:\n{}", LOG_TAG,request.toString(),phone,mapper.writeValueAsString(rootNode));

            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-手机号:{},响应:{}", LOG_TAG,phone,content);
                return vrbtDiyVideo.getId();
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("{}-手机号:{},异常:", LOG_TAG,phone, e);
            return "";
        }
    }

    /**
     * 小程序异步处理-专用
     * 接入平台调用“中音CMS”内容自动化申报接口进行视频彩铃内容信息上报（支持全网上线及分省定向上线）；
     * @param
     * @return
     */
    public String report(ReportDTO reportDTO, String vrbtDiyVideoId){
        String phone = reportDTO.getPhone();
        String mvName = reportDTO.getMvName();
        String mvUrl = reportDTO.getMvUrl();
        String transactionId = reportDTO.getTransactionId();

        ObjectNode rootNode = mapper.createObjectNode();
        ObjectNode bodyNode = mapper.createObjectNode();
        ObjectNode headNode = mapper.createObjectNode();
        ArrayNode toneNode = mapper.createArrayNode();
        ArrayNode sprParametersNode = mapper.createArrayNode();
        String seq = System.currentTimeMillis() + "" + yycpProperties.getAccessPlatformId();

        try {
            //保存订单信息
            VrbtDiyVideo vrbtDiyVideo = new VrbtDiyVideo();
            vrbtDiyVideo.setId(vrbtDiyVideoId);
            vrbtDiyVideo.setMobile(phone);
            vrbtDiyVideo.setMvUrl(mvUrl);
            vrbtDiyVideo.setMvName(mvName);
            vrbtDiyVideo.setSeq(seq);
            vrbtDiyVideo.setTransactionId(transactionId);
            vrbtDiyVideo.setChannel(reportDTO.getChannelCode());
            vrbtDiyVideoService.updateById(vrbtDiyVideo);

            toneNode.add("M");
            sprParametersNode.add(mapper.createObjectNode().put("paraName","mvType").put("paraValue","7"));
            sprParametersNode.add(mapper.createObjectNode().put("paraName","type").put("paraValue","9"));
            sprParametersNode.add(mapper.createObjectNode().put("paraName","libraryType").put("paraValue","2"));//内容库分类
            sprParametersNode.add(mapper.createObjectNode().put("paraName","phone").put("paraValue",phone));//用户手机号码
            sprParametersNode.add(mapper.createObjectNode().put("paraName","provinceID").put("paraValue",provincesCode(phone)));//分发手机号所属省份ID
            sprParametersNode.add(mapper.createObjectNode().put("paraName","provinces").putPOJO("paraValue",PROVINCES));//分发手机号所属省份ID
            //构建body
            bodyNode.put("transactionID",transactionId);//事务ID,每次请求事务ID不能一样。同一个平台内值不能相同,必填
            bodyNode.put("cPID",yycpProperties.getCpId());
            bodyNode.set("toneType",toneNode);//制作产品类型,M：视频彩铃
            bodyNode.put("mvName",mvName);//视频名称，新增类型必填
            bodyNode.put("singerType","未分类");//歌手类型，新增类型必填, 值为空
            bodyNode.put("issuedArea","其他");//发行地区,新增类型必填
            bodyNode.put("actor",phone);//表演者，新增类型必填
            bodyNode.put("maker","1");//制片者名称，新增类型必填
            bodyNode.put("makerRate","100");//制片者拥有比例
            bodyNode.put("businessType","其他");//业务类型，新增类型必填
            bodyNode.put("wordAuthor","暂无");//词作者，新增类型必填
            bodyNode.put("songAuthor","");//曲作者，新增类型必填
            bodyNode.put("validPeriod","60");//有效期，新增类型必填，单位：月有效期填写说明
            bodyNode.put("singleRight","非独家授权");//授权方式，新增类型必填
            bodyNode.put("language","中文");//语言，新增类型必填
            bodyNode.put("origCopyrightSide","暂无");//原始版权方，新增类型必填
            bodyNode.put("mvUrl",mvUrl);//素材文件路径(横屏或者竖屏)
            bodyNode.put("description","暂无");//描述信息，新增必填
            bodyNode.set("sprParameters",sprParametersNode);//描述信息，新增必填
            bodyNode.put("generalAuthorization","/yycp/咪咕音乐有限公司数字音视频内容版权授权书【智象未来】.pdf");
            //构建head
            headNode.put("dID",yycpProperties.getDId());
            headNode.put("sEQ",seq);
            headNode.put("dIDPwd", Md5Utils.hash(seq + yycpProperties.getKey()));
            headNode.put("accessPlatformID",yycpProperties.getAccessPlatformId());
            headNode.put("syncType","00");

            rootNode.set("head",headNode);
            rootNode.set("body",bodyNode);

            RequestBody body = RequestBody.create(JSON, mapper.writeValueAsString(rootNode));
            HttpUrl httpUrl = HttpUrl.parse(yycpProperties.getReportUrl())
                    .newBuilder()
                    .build();
            Request request = new Request.Builder().url(httpUrl)
                    .post(body)
                    .build();
            log.info("{}-请求:{},手机号:{},body:\n{}", LOG_TAG,request.toString(),phone,mapper.writeValueAsString(rootNode));

            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-手机号:{},响应:{}", LOG_TAG,phone,content);
                return vrbtDiyVideo.getId();
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("{}-手机号:{},异常:", LOG_TAG,phone, e);
            return "";
        }
    }

    public FebsResponse yycpFtpUploadAndReport(String mobile, String channelCode) {
        FebsResponse febsResponse = new FebsResponse();
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        String id = IdWorker.getId() + "";
        if (StringUtils.isBlank(mobile)) {
            return febsResponse.fail().message("参数错误");
        }
        String pathName = "/yycp" + df.format(new Date());
        String fileName = "video" + id + ".mp4";
        //抖音小程序不走这里设置铃声
        if (StrUtil.isNotBlank(channelCode)&&channelCode.equals(MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_XCX)){
            return febsResponse.success().data(Boolean.TRUE);
        }
        //随机一条视频
        List<String> ossRingList = yycpProperties.getOssRingList();
        String filePath = ossRingList.get(RandomUtils.getRondom(1,21)-1);
        boolean b = vrbtDiyVideoService.uploadVideoToFtp(pathName, filePath, fileName);
        if (b) {
            ReportDTO reportDTO = new ReportDTO();
            reportDTO.setPhone(mobile);
            reportDTO.setMvUrl(pathName + "/" + fileName);
            reportDTO.setMvName(id);
            reportDTO.setTransactionId(ReportTypeEnum.ZX.getCode() + IdWorker.getId());
            reportDTO.setChannelCode(channelCode);
            this.report(reportDTO);
            febsResponse.success().message("上传成功");
        } else {
            febsResponse.fail().message("上传失败");
        }
        return febsResponse.success().data(b);
    }
    private String provincesCode(String mobile){
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(mobileRegionResult != null && StringUtils.isNotBlank(mobileRegionResult.getProvince())){
            return PROVINCES_CODE_MAP.get(mobileRegionResult.getProvince());
        }
        return null;
    }

}
