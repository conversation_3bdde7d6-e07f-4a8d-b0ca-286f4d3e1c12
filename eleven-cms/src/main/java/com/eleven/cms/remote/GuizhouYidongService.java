package com.eleven.cms.remote;

import com.eleven.cms.config.GuizhouYidongProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.guizhou.AesEncryptUtil;
import com.eleven.cms.util.guizhou.MD5Util;
import com.eleven.cms.util.guizhou.Md5Utils;
import com.eleven.cms.util.guizhou.RSA2Signer;
import com.eleven.cms.vo.GuizhouMobileCodeResult;
import com.eleven.cms.vo.GuizhouMobileResult;
import com.eleven.cms.vo.GuizhouMobileResultInfo;
import com.eleven.cms.vo.GuizhouMobileTokenResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URLDecoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @author: lihb
 * @create: 2022-9-20 11:40:24
 */
@Slf4j
@Service
public class GuizhouYidongService {

    public static final String LOG_TAG = "贵州移动流量包业务api";
    @Autowired
    private GuizhouYidongProperties guizhouYidongProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;

    private OkHttpClient client;

    private ObjectMapper mapper;
    private static final MediaType mediaType = MediaType.parse("application/json");


    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 请求短信验证码
     *
     * @param phone     手机号
     * @param messageId UUID.randomUUID().toString().replace("-","")
     * @param channel   渠道号
     * @param busiLink  业务链接
     * @param id
     * @return
     * @throws Exception
     */
//    @Cacheable(cacheNames = CacheConstant.GZYD_GETSMS_API_CACHE,key = "#channel + '-' + #phone",unless = "#result==null")
    public GuizhouMobileCodeResult getSms(String phone, String messageId, String channel, String busiLink, String id) {
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        GuizhouMobileCodeResult guizhouMobileCodeResult = null;
        try {
            String dateFormat = df.format(new Date());
            String url = guizhouYidongProperties.getSmsUrl();
            ObjectNode dataMap = mapper.createObjectNode();
            ObjectNode bodyMap = mapper.createObjectNode();
            ObjectNode headMap = mapper.createObjectNode();

            bodyMap.put("BillId",phone);
            bodyMap.put("channelMapId",guizhouYidongProperties.getChannelMapId());
            bodyMap.put("businessMapId",guizhouYidongProperties.getBizCodeByChannel(channel));
            bodyMap.put("chnlIp",guizhouYidongProperties.getChnlIp());
            if(StringUtils.isBlank(id)){
                bodyMap.put("busiLink",guizhouYidongProperties.getLinkByChannel(channel));
            }else{
                bodyMap.put("busiLink",HttpUrl.parse(busiLink).newBuilder().addQueryParameter("mobile",phone).addQueryParameter("ispNo", id).toString());
            }
            bodyMap.put("stepCode","3");
            bodyMap.put("websiteName",guizhouYidongProperties.getWebsiteName());
//            log.info("{}-随机码下发-手机号:{},请求body参数:{}", LOG_TAG, phone,bodyMap);
            headMap.put("appkey","netChnl@yrjy");
            headMap.put("sign",getSign());
            headMap.put("channelId","centerGroup");
//            log.info("{}-随机码下发-手机号:{},head参数:{}",LOG_TAG,phone, headMap);
            dataMap.put("body", encrypt(bodyMap.toString()));
            dataMap.set("head", headMap);

//            log.info("{}-随机码下发-手机号:{},请求json-:{}", LOG_TAG,phone, dataMap);
            RequestBody body = RequestBody.create(mediaType, dataMap.toString());
            Request request = new Request.Builder().url(url)
                    .addHeader("activityCode","getSmsCodeService")
                    .addHeader("processCode","getSmsCode")
                    .addHeader("sceneCode","internetBusi")
                    .addHeader("appId","CDYRJY_TEC")
                    .addHeader("accessToken",getToken(dateFormat))
                    .addHeader("timestamp",dateFormat)
                    .addHeader("messageId",messageId)
                    .addHeader("version","0")
                    .addHeader("Content-Type","application/json")
                    .post(body)
                    .build();
            log.info("{}-随机码下发-手机号:{},渠道号:{},请求:{},body:{}", LOG_TAG, phone,channel, request.toString(),bodyMap);

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            GuizhouMobileResult guizhouMobileResult = mapper.readValue(result, GuizhouMobileResult.class);
            if(guizhouMobileResult.getResult() != null){
                String resultCode = guizhouMobileResult.getResult().getResultCode();
                if("0000".equals(resultCode)){
                    String desEncrypt = AesEncryptUtil.desEncrypt(guizhouMobileResult.getResult().getResultInfo().toString(), "asiainfoIIS20182", "asiainfoIIS20182");
                    guizhouMobileCodeResult = mapper.readValue(desEncrypt, GuizhouMobileCodeResult.class);
                    log.info("{}-随机码下发-手机号:{},渠道号:{},resultInfo:{}", LOG_TAG, phone,channel, guizhouMobileCodeResult.toString());
                }else if("1005".equals(resultCode)){
                    log.info("{}-随机码下发-CRM业务受理规则校验不通过！-手机号:{},渠道号:{}", LOG_TAG, phone,channel);
                }else{
                    log.info("{}-随机码下发-贵州移动随机码下发出错-手机号:{},渠道号:{}", LOG_TAG, phone,channel);
                }
            }
            return guizhouMobileCodeResult;
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return guizhouMobileCodeResult;

        }
    }

    /**
     * 业务办理
     * phone 手机号
     * smsCode 短信验证码
     * serialNumber 流水号
     *
     * @param phone
     * @param applyUrl
     * @param callback
     * @return
     */
    public GuizhouMobileResult.Result smsCode(String phone, String authCode, String messageId, String serialNumber, String channel, String busiLink, String callback){
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateFormat = df.format(new Date());
        String url = guizhouYidongProperties.getSendSmsUrl();
        ObjectNode dataMap = mapper.createObjectNode();
        ObjectNode bodyMap = mapper.createObjectNode();
        ObjectNode headMap = mapper.createObjectNode();
        Request request = null;

        try {
            bodyMap.put("BillId",phone);
            bodyMap.put("channelMapId",guizhouYidongProperties.getChannelMapId());
            bodyMap.put("businessMapId",guizhouYidongProperties.getBizCodeByChannel(channel));
            bodyMap.put("chnlIp",guizhouYidongProperties.getChnlIp());
            bodyMap.put("busiLink",busiLink);
            bodyMap.put("busiLink",StringUtils.isBlank(busiLink) ? guizhouYidongProperties.getLinkByChannel(channel) : busiLink);
            bodyMap.put("websiteName",guizhouYidongProperties.getWebsiteName());
            bodyMap.put("stepCode","8");
            bodyMap.put("serialNumber",serialNumber);
            bodyMap.put("authCode",authCode);
            bodyMap.put("channelPaySN","yrjy" + messageId);//外部渠道流水
            bodyMap.put("actionDate",dateFormat.substring(0,8));//交易日期
            bodyMap.put("actionTime",dateFormat);//交易时间
            bodyMap.put("Apply_url",StringUtils.isBlank(busiLink) ? guizhouYidongProperties.getLinkByChannel(channel) : busiLink);//受理链接	1	STR	V400	巨量引擎配置推广链接
            bodyMap.put("callback",StringUtils.isBlank(callback) ? "" : callback);//广告流水号	1	STR	V400	智投平台投放携带广告流水号

//            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},body参数:{}", LOG_TAG, phone,channel,authCode,bodyMap);
            headMap.put("appkey","netChnl@yrjy");
            headMap.put("sign",getSign());
            headMap.put("channelId","centerGroup");
//            log.info("{}-提交验证码-手机号:{},body参数:{}", LOG_TAG, phone,bodyMap);
            dataMap.put("body", encrypt(bodyMap.toString()));
            dataMap.set("head", headMap);

            RequestBody body = RequestBody.create(mediaType, dataMap.toString());
            request = new Request.Builder().url(url)
                    .addHeader("activityCode","dealBusiService")
                    .addHeader("processCode","dealBusiService")
                    .addHeader("sceneCode","internetBusi")
                    .addHeader("appId","CDYRJY_TEC")
                    .addHeader("accessToken",getToken(dateFormat))
                    .addHeader("timestamp",dateFormat)
                    .addHeader("messageId",messageId)
                    .addHeader("version","0")
                    .addHeader("Content-Type","application/json")
                    .post(body)
                    //.tag(dataMap)
                    .build();
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},请求:{},body:{}", LOG_TAG, phone,channel,authCode, request.toString(),bodyMap);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return GuizhouMobileResult.Result.fail();
        }
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},返回结果:{}", LOG_TAG, phone,channel, result);
            GuizhouMobileResult guizhouMobileResult = mapper.readValue(result, GuizhouMobileResult.class);
            return guizhouMobileResult.getResult();
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},渠道号:{},异常:", LOG_TAG, phone,channel, e);
            return GuizhouMobileResult.Result.fail();
        }
    }

    /**
     * 外部渠道业务信息获取接口
     * chnlIp
     * busiLink
     * websiteName
     * @return
     */
    public List<GuizhouMobileResultInfo> querySms(String busiLink) throws Exception {
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        List<GuizhouMobileResultInfo> resultInfoList = null;
        String dateFormat = df.format(new Date());
        String url = guizhouYidongProperties.getSendSmsUrl();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        Map<String, Object> bodyMap = new LinkedHashMap<>();
        Map<String, Object> headMap = new LinkedHashMap<>();

        bodyMap.put("channelMapId",guizhouYidongProperties.getChannelMapId());
        bodyMap.put("stepCode","3");
        bodyMap.put("chnlIp",guizhouYidongProperties.getChnlIp());
        bodyMap.put("busiLink",busiLink);
        bodyMap.put("websiteName",guizhouYidongProperties.getWebsiteName());

        log.info("{}-外部渠道业务信息获取接口-body参数:{}", LOG_TAG,bodyMap.toString());
        headMap.put("appkey","netChnl@yrjy");
        headMap.put("sign",getSign());
        headMap.put("channelId","centerGroup");
        log.info("head参数:" + headMap.toString());
        dataMap.put("body", encrypt(mapper.writeValueAsString(bodyMap)));
        dataMap.put("head", headMap);

        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .addHeader("activityCode","getBusiInfoService")
                .addHeader("processCode","getBusiInfoService")
                .addHeader("sceneCode","internetBusi")
                .addHeader("appId","CDYRJY_TEC")
                .addHeader("accessToken",getToken(dateFormat))
                .addHeader("timestamp",dateFormat)
                .addHeader("messageId",UUID.randomUUID().toString().replace("-",""))
                .addHeader("version","0")
                .addHeader("Content-Type","application/json")
                .post(body)
                //.tag(dataMap)
                .build();
        log.info("{}-外部渠道业务信息获取接口,请求:{}", LOG_TAG, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            GuizhouMobileResult guizhouMobileResult = mapper.readValue(result, GuizhouMobileResult.class);
            log.info("{}-外部渠道业务信息获取接口,返回结果:{}", LOG_TAG, result);
            if("0000".equals(guizhouMobileResult.getResult().getResultCode())){
                String desEncrypt = AesEncryptUtil.desEncrypt(guizhouMobileResult.getResult().getResultInfo().toString(), "asiainfoIIS20182", "asiainfoIIS20182");
                log.info("{}-外部渠道业务信息获取接口，resultInfo:{}", LOG_TAG, desEncrypt);
                JsonNode tree = mapper.readTree(desEncrypt);
                JsonNode busiInfo = tree.at("/busiInfo");
                JavaType javaType = mapper.getTypeFactory().constructParametricType(ArrayList.class,GuizhouMobileResultInfo.class);
                resultInfoList =  mapper.readValue(busiInfo.toString(), javaType);
                for (GuizhouMobileResultInfo guizhouMobileResultInfo : resultInfoList) {
                    guizhouMobileResultInfo.setBusiDec(URLDecoder.decode( guizhouMobileResultInfo.getBusiDec(),"UTF-8"));
                    guizhouMobileResultInfo.setBusiName(URLDecoder.decode( guizhouMobileResultInfo.getBusiName(),"UTF-8"));
                }
                log.info("{}-外部渠道业务信息获取接口，resultInfoList:{}", LOG_TAG, resultInfoList.toString());
            }else{
                log.info("{}-外部渠道业务信息获取接口出错，错误码:{}", LOG_TAG, guizhouMobileResult.getResult().getResultCode());
            }
            return resultInfoList;
        } catch (Exception e) {
            log.info("{}-外部渠道业务信息获取接口,异常:", LOG_TAG, e);
            return resultInfoList;
        }
    }

    /**
     * 获取token,从Redis获取，获取不到就调用接口
     * @param
     * @return
     */
    private String getToken(String dateFormat){

        String token = (String) redisUtil.get("gzyd-token");
        if(StringUtils.isEmpty(token)){
            try {
                String url = getBocAccessTokenUrl(1, dateFormat);
                HttpUrl httpUrl = HttpUrl.parse(url);

                log.info("{}-获取token接口-请求:{}", LOG_TAG, httpUrl.toString());
                Request request = new Request.Builder().url(httpUrl)
                        .build();
                try (Response response = client.newCall(request)
                        .execute()) {
                    if (!response.isSuccessful()) {
                        throw new IOException("Unexpected code " + response);
                    }

                    String content = response.body().string();
                    GuizhouMobileTokenResult result = mapper.readValue(content, GuizhouMobileTokenResult.class);
                    if(StringUtils.isNotBlank(result.getToken())){
                        token = result.getToken();
                        redisUtil.setIfAbsent("gzyd-token",token,2*58*60);
                    }
                    log.info("{}-获取token接口-响应:{}", LOG_TAG, content);
                }
            } catch (IOException e) {
                //e.printStackTrace();
                log.warn("{}-获取token接口--异常:", LOG_TAG, e);
            }
        }
        return token;
    }

    private  String getBocAccessTokenUrl(int signType,String dateFormat) {
        String getTokenUrl = guizhouYidongProperties.getTokenUrl(); //
        HashMap map = new HashMap();
        map.put("appId","CDYRJY_TEC"); //由业务能力运营中心分配
        map.put("appKey",guizhouYidongProperties.getAppkey());//由业务能力运营中心分配
        map.put("signType", String.valueOf(signType));
        map.put("applyTime",dateFormat); //获取当前系统时间串
        String paramString = getSignContent(map);
        String sign = "";
        if(signType==2) {
            try {
                /*sign = rsaSign(paramString, guizhouYidongProperties.getPrivateKey());*/
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else if(signType==1){
            sign = md5Sign(paramString, guizhouYidongProperties.getSecretKey());
        }

        return new StringBuffer(getTokenUrl).append("?appId=").append("CDYRJY_TEC")
                .append("&appKey=").append(guizhouYidongProperties.getAppkey())
                .append("&signType=").append(signType)
                .append("&applyTime=").append(dateFormat)
                .append("&sign=").append(sign).toString();
    }

    private  String md5Sign(String paramString, String privateKey) {
        String sign = null;
        try {
            sign = MD5Util.encode(paramString + privateKey, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sign;
    }
    private String rsaSign(String paramString, String privateKey) throws Exception {
        String sign = RSA2Signer.signBySHA256WithRSA(paramString,privateKey);
        return sign;
    }
    private  String getSignContent(Map<String, String> params) {
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        int index = 0;
        for (String key : keys) {
            String value = params.get(key);
            if (StringUtils.isNotBlank(value)) {
                content.append(index == 0 ? "" : "&").append(key).append("=").append(value);
                index++;
            }
        }
        return content.toString();
    }
    private String getSign(){
        String secretKey = "901130c62cc43d99553e7f3503ac0349";
        String appkey = "netChnl@yrjy";
        String channelId = "centerGroup";
        return Md5Utils.hash(secretKey + appkey + channelId);
    }
    private String encrypt(String body){
        String result = "";
        try {
            result = AesEncryptUtil.encrypt(body,"asiainfoIIS20182","asiainfoIIS20182");
        } catch (Exception e) {
            log.error("贵州移动getBody出错：",e);
        }
        return result;
    }
}
