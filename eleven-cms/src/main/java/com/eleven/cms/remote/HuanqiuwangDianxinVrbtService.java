package com.eleven.cms.remote;

import com.eleven.cms.config.Hu<PERSON>qiuwangDianxinVrbtProperties;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.Hu<PERSON>qiuwangDianxinVrbtResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import com.moczul.ok2curl.Options;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import javax.swing.tree.TreeNode;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.UUID;

/**
 * @author: cai lei
 * @create: 2022-01-05 10:50
 */
@Slf4j
@Service
public class HuanqiuwangDianxinVrbtService {

    public static final String LOG_TAG = "环球网-电信爱音乐api";
    @Autowired
    private Environment environment;
    @Autowired
    private HuanqiuwangDianxinVrbtProperties huanqiuwangDianxinVrbtProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;


    //   @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();

        this.client = this.client.newBuilder()
                //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                .build();


        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            final Options options = Options.builder()
                    .compressed()
                    .insecure()  //不检查自签名证书
                    //.connectTimeout(120)
                    //.retry(5)
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println,options)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 环球网下单接口
     * @param phone
     * @return
     */

//    {"result":"00070","msg":"该通道下所有产品今日订购数量超限"}
//    {"result":"00022","msg":"亲，暂时无法订购(月限)，请您耐心等待哟！"}
//    {"res_code":"0","res_message":"成功","order_no":"8225fd9d3d7646238be3594d47a4fed2","fee_url":"https://crbt.qimingwenhua.net/videoring/ct/detail/submitorder.html?id=8225fd9d3d7646238be3594d47a4fed2"}


    @Cacheable(cacheNames = CacheConstant.CMS_DIANXIN_VRBT_API_CACHE,key = "#root.methodName + '-huanqiuwang' + '-' + #phone",unless = "#result==null")
    public HuanqiuwangDianxinVrbtResult order(String phone) {
        final HttpUrl httpUrl = HttpUrl.parse(huanqiuwangDianxinVrbtProperties.getOrderUrl())
                .newBuilder()
//                .addQueryParameter("action", "pay")
//                .addQueryParameter("channel", huanqiuwangDianxinVrbtProperties.getChannel())
//                .addQueryParameter("appid", huanqiuwangDianxinVrbtProperties.getAppid())
//                .addQueryParameter("feeid", huanqiuwangDianxinVrbtProperties.getFeeid())
//                .addQueryParameter("phone", phone)
//                .addQueryParameter("data", huanqiuwangDianxinVrbtProperties.getReturnUrl())
                .addQueryParameter("phone", phone)
                .addQueryParameter("return_url", huanqiuwangDianxinVrbtProperties.getReturnUrl())
                .addQueryParameter("sid", huanqiuwangDianxinVrbtProperties.getSid())
                .addQueryParameter("t", huanqiuwangDianxinVrbtProperties.getChnl())
                .build();
        log.info("{}-下单-手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString());
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-下单-手机号:{},响应:{}", LOG_TAG, phone, content);
            return mapper.readValue(content, HuanqiuwangDianxinVrbtResult.class);
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-获取短信-手机号:{},异常:", LOG_TAG, phone, e);
            return HuanqiuwangDianxinVrbtResult.fail();
        }
    }

    /**
     * 手机号 渠道号绑定
     *
     * @param phone
     * @return
     * @throws UnsupportedEncodingException
     */
    public boolean bind(String phone) throws UnsupportedEncodingException {
//        http://47.104.2.83/videoring/cm/ds.jsp?reqcode=bind&mp=13552957942&chnl=AAAC&sign=4223e3da7355de08078165da38289a00

        String session = String.valueOf(UUID.randomUUID());
        String signStr = new StringBuilder().append(phone).append(huanqiuwangDianxinVrbtProperties.getChnl()).append(session).toString();
        String sign = DigestUtils.md5DigestAsHex(signStr.getBytes(StandardCharsets.UTF_8.name()));

        final HttpUrl httpUrl = HttpUrl.parse(huanqiuwangDianxinVrbtProperties.getBindUrl())
                .newBuilder()
                .addQueryParameter("mp", phone)
                .addQueryParameter("chnl", huanqiuwangDianxinVrbtProperties.getChnl())
                .addQueryParameter("sign", sign)
                .build();
        log.info("{}-绑定-手机号:{},请求:{}", LOG_TAG, phone, httpUrl.toString());
        Request request = new Request.Builder().addHeader("Cookie", "_crbt_session_=" + session).url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = StringUtils.trim(response.body().string());
            log.info("{}-绑定-手机号:{},响应:{}", LOG_TAG, phone, content);
            JsonNode tree = mapper.readTree(content);
            return "000000".equals(tree.at("/resCode").asText());
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-绑定-手机号:{},异常:", LOG_TAG, phone, e);
            return false;
        }

    }
}
