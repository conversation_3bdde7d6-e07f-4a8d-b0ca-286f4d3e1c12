package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户剧卡权益表
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@TableName("mini_app_user_drama_card")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户剧卡权益", description = "用户剧卡权益信息")
public class UserDramaCard implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 订单id
     */
    @Excel(name = "订单id", width = 15)
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * 短剧id
     */
    @Excel(name = "短剧id", width = 15)
    @ApiModelProperty(value = "短剧id")
    private String dramaId;

    /**
     * 短剧名称
     */
    @Excel(name = "短剧名称", width = 15)
    @ApiModelProperty(value = "短剧名称")
    private String albumName;

    /**
     * 抖音短剧albumId
     */
    @Excel(name = "抖音短剧albumId", width = 15)
    @ApiModelProperty(value = "抖音短剧albumId")
    private String albumId;

    /**
     * 抖音剧集ID
     */
    @Excel(name = "抖音剧集ID", width = 15)
    @ApiModelProperty(value = "抖音剧集ID")
    private String douYinEpisodeId;

    /**
     * 权益范围: 1全剧 2单集
     */
    private Integer rangeType;

    /**
     * 购买时间
     */
    @Excel(name = "购买时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "购买时间")
    private Date purchaseTime;

    /**
     * 到期时间（永久有效可为空）
     */
    @Excel(name = "到期时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "到期时间")
    private Date expireTime;

    /**
     * 状态: 0未生效 1生效中 2已过期 3已取消
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态: 0未生效 1生效中 2已过期 3已取消")
    private Integer status;

    /**
     * 是否永久有效: 0否 1是
     */
    @Excel(name = "是否永久有效", width = 15)
    @ApiModelProperty(value = "是否永久有效: 0否 1是")
    private Integer isPermanent;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    /**
     * 是否删除: 0否 1是
     */
    @ApiModelProperty(value = "是否删除: 0否 1是")
    private Integer isDeleted;
}
