package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.DuanJuBanner;
import com.eleven.cms.douyinduanju.mapper.DuanJuBannerMapper;
import com.eleven.cms.douyinduanju.service.IDuanJuBannerService;
import org.springframework.stereotype.Service;

/**
 * @Description: duan_ju_banner
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Service
public class DuanJuBannerServiceImpl extends ServiceImpl<DuanJuBannerMapper, DuanJuBanner> implements IDuanJuBannerService {

    @Override
    public int updateByIdWithNull(DuanJuBanner duanJuBanner) {
        return this.baseMapper.updateByIdWithNull(duanJuBanner);
    }

}
