package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: duan_ju_banner
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Data
@TableName("duan_ju_banner")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "duan_ju_banner对象", description = "duan_ju_banner")
public class DuanJuBanner implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * banner类型
     */
    @Excel(name = "banner类型", width = 15)
    @ApiModelProperty(value = "banner类型")
    private Integer type;
    /**
     * banner位置路径
     */
    @Excel(name = "banner位置路径", width = 15)
    @ApiModelProperty(value = "banner位置路径")
    private String location;
    /**
     * banner描述
     */
    @Excel(name = "banner描述", width = 15)
    @ApiModelProperty(value = "banner描述")
    private String description;
    /**
     * banner图片
     */
    @Excel(name = "banner图片", width = 15)
    @ApiModelProperty(value = "banner图片")
    private String image;
    /**
     * banner跳转资源
     */
    @Excel(name = "banner跳转资源", width = 15)
    @ApiModelProperty(value = "banner跳转资源")
    private String jumpSource;
    /**
     * banner跳转类型  1:h5 2:剧集
     */
    @Excel(name = "banner跳转类型", width = 15)
    @ApiModelProperty(value = "banner跳转类型")
    private Integer jumpType;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private String orderBy;
    private String albumId;
    private String albumName;
    private String douYinEpisodeId;
    private String episodeSeq;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15, dicCode = "valid_status")
    @Dict(dicCode = "valid_status")
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
