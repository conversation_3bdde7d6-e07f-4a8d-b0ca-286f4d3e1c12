package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.UserDramaCard;

import java.util.List;

/**
 * 用户剧卡权益服务接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IUserDramaCardService extends IService<UserDramaCard> {

    /**
     * 根据用户ID和短剧ID获取有效的剧卡
     *
     * @param userId  用户ID
     * @param dramaId 短剧ID
     * @return 剧卡信息
     */
    UserDramaCard getValidDramaCard(String userId, String dramaId);

    /**
     * 根据用户ID获取所有有效的剧卡
     *
     * @param userId 用户ID
     * @return 剧卡列表
     */
    List<UserDramaCard> getValidDramaCardsByUserId(String userId);

    /**
     * 开通剧卡权益
     *
     * @param userId       用户ID
     * @param orderId      订单ID
     * @param albumName    短剧名称
     * @param albumId      抖音短剧albumId
     * @param dramaId      短剧ID
     * @param packageType  订单套餐类型（用于确定权益卡类型）
     * @param isPermanent  是否永久有效
     * @param durationDays 有效天数（永久有效时可为null）
     * @return 是否成功
     */
    boolean activateDramaCard(String userId, String orderId, String albumName,
                              String albumId, String dramaId, Integer packageType,
                              Boolean isPermanent, Integer durationDays);

    /**
     * 取消剧卡权益
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean cancelDramaCard(String userId, String orderId);

    /**
     * 刷新用户剧卡状态
     *
     * @param userId 用户ID
     */
    void refreshDramaCardStatus(String userId);


}
