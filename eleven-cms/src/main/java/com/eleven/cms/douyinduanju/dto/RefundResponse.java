package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

/**
 * 退款响应类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class RefundResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 退款单号
     */
    private String outRefundNo;

    /**
     * 抖音退款ID
     */
    private String refundId;

    /**
     * 错误码
     */
    private String errorCode;

    public RefundResponse() {
    }

    public RefundResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public RefundResponse(boolean success, String message, String outRefundNo, String refundId) {
        this.success = success;
        this.message = message;
        this.outRefundNo = outRefundNo;
        this.refundId = refundId;
    }

    /**
     * 成功响应
     */
    public static RefundResponse success(String message) {
        return new RefundResponse(true, message);
    }

    /**
     * 成功响应（带退款信息）
     */
    public static RefundResponse success(String message, String outRefundNo, String refundId) {
        return new RefundResponse(true, message, outRefundNo, refundId);
    }

    /**
     * 失败响应
     */
    public static RefundResponse error(String message) {
        return new RefundResponse(false, message);
    }

    /**
     * 失败响应（带错误码）
     */
    public static RefundResponse error(String message, String errorCode) {
        RefundResponse response = new RefundResponse(false, message);
        response.setErrorCode(errorCode);
        return response;
    }
}
