package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.UserKCoinAccount;

import java.math.BigDecimal;

/**
 * 用户K币账户服务接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IUserKCoinAccountService extends IService<UserKCoinAccount> {

    /**
     * 根据用户ID获取K币账户
     *
     * @param userId 用户ID
     * @return K币账户信息
     */
    UserKCoinAccount getByUserId(String userId);

    /**
     * 创建K币账户
     *
     * @param userId 用户ID
     * @return K币账户信息
     */
    UserKCoinAccount createAccount(String userId);

    /**
     * 充值K币
     *
     * @param userId      用户ID
     * @param amount      充值金额
     * @param orderId     订单ID
     * @param description 描述
     * @return 是否成功
     */
    boolean rechargeKCoin(String userId, BigDecimal amount, String orderId, String description);

    /**
     * 消费K币
     *
     * @param userId       用户ID
     * @param amount       消费金额
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param description  描述
     * @return 是否成功
     */
    boolean consumeKCoin(String userId, BigDecimal amount, Integer businessType,
                         String businessId, String description);

    /**
     * 退款K币
     *
     * @param userId      用户ID
     * @param amount      退款金额
     * @param orderId     订单ID
     * @param description 描述
     * @return 是否成功
     */
    boolean refundKCoin(String userId, BigDecimal amount, String orderId, String description);

    /**
     * 赠送K币
     *
     * @param userId      用户ID
     * @param amount      赠送金额
     * @param description 描述
     * @return 是否成功
     */
    boolean giftKCoin(String userId, BigDecimal amount, String description);

    /**
     * 检查K币余额是否足够
     *
     * @param userId 用户ID
     * @param amount 需要的金额
     * @return 是否足够
     */
    boolean checkBalance(String userId, BigDecimal amount);
}
