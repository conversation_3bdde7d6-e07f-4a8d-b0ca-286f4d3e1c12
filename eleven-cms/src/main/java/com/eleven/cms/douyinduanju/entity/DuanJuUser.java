package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: duan_ju_user
 * @Author: jeecg-boot
 * @Date: 2025-06-05
 * @Version: V1.0
 */
@Data
@TableName("duan_ju_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "duan_ju_user对象", description = "duan_ju_user")
public class DuanJuUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;
    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称", width = 15)
    @ApiModelProperty(value = "用户昵称")
    private String nickName;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 抖音openId
     */
    @Excel(name = "抖音openId", width = 15)
    @ApiModelProperty(value = "抖音openId")
    private String openId;
    /**
     * 注册时间
     */
    @Excel(name = "注册时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "注册时间")
    private Date registerTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 手机订阅会员过期状态  0: 未过期  1:已过期
     */
    private Integer phoneSubExpireStatus;
    /**
     * 第三方会员订阅过期状态  0: 未过期  1:已过期
     */
    private Integer thirdPartySubExpireStatus;
    /**
     * 会员状态 1:非会员 2:订阅会员  3:三分支付会员
     */
    @Excel(name = "会员状态 1:非会员 2:订阅会员  3:三方支付会员", width = 15)
    @ApiModelProperty(value = "会员状态 1:非会员 2:订阅会员")
    private Integer memberStatus;

    /**
     * 会员类型 1:非会员 2:订阅会员  3:三方支付会员
     */
    private Integer memberType;

    /**
     * 是否付费 1: 付费  0未付费
     */
    @Excel(name = "是否付费 1: 付费  0未付费", width = 15)
    @ApiModelProperty(value = "是否付费 1: 付费  0未付费")
    private Integer payFlat;
    /**
     * 最近启动时间
     */
    @Excel(name = "最近启动时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最近启动时间")
    private Date lastOperateTime;
    /**
     * 用户来源
     */
    @Excel(name = "用户来源", width = 15)
    @ApiModelProperty(value = "用户来源")
    private String source;

    /**
     * 来源小程序编码
     */
    @Dict(dicCode = "duanJu_mini_app_code")
    private String resource;

    private String subChannel;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    private Integer isDeleted;


    private String templateId;
}
