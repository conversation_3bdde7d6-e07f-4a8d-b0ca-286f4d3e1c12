package com.eleven.cms.douyinduanju.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.stream.Stream;

/**
 * NAS服务器文件读取工具类
 * 用于读取 \\192.168.6.160\短剧图书内容 目录下的文件和文件夹
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Component
@Slf4j
public class NasFileReader {

    /**
     * NAS服务器路径
     */
    private static final String NAS_BASE_PATH = "\\\\192.168.6.160\\短剧图书内容\\产品线短剧百度网盘自动同步";

    /**
     * 支持的视频文件扩展名
     */
    private static final Set<String> VIDEO_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v"
    ));

    /**
     * 支持的图片文件扩展名
     */
    private static final Set<String> IMAGE_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"
    ));

    /**
     * 获取NAS根目录下的所有文件夹
     *
     * @return 文件夹列表
     */
    public List<NasDirectory> getRootDirectories() {
        List<NasDirectory> directories = new ArrayList<>();

        try {
            Path nasPath = Paths.get(NAS_BASE_PATH);

            if (!Files.exists(nasPath)) {
                log.error("NAS路径不存在: {}", NAS_BASE_PATH);
                return directories;
            }

            try (Stream<Path> paths = Files.list(nasPath)) {
                paths.filter(Files::isDirectory)
                        .forEach(path -> {
                            try {
                                NasDirectory directory = createNasDirectory(path);
                                directories.add(directory);
                            } catch (Exception e) {
                                log.error("读取目录失败: {}", path, e);
                            }
                        });
            }

            // 按名称排序
            directories.sort(Comparator.comparing(NasDirectory::getName));

        } catch (IOException e) {
            log.error("读取NAS根目录失败", e);
        }

        return directories;
    }

    /**
     * 获取指定目录下的所有文件和子目录
     *
     * @param relativePath 相对于NAS根目录的路径
     * @return 目录内容
     */
    public NasDirectoryContent getDirectoryContent(String relativePath) {
        NasDirectoryContent content = new NasDirectoryContent();

        try {
            String fullPath = relativePath == null || relativePath.isEmpty()
                    ? NAS_BASE_PATH
                    : NAS_BASE_PATH + File.separator + relativePath;

            Path dirPath = Paths.get(fullPath);

            if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
                log.error("目录不存在或不是目录: {}", fullPath);
                return content;
            }

            try (Stream<Path> paths = Files.list(dirPath)) {
                paths.forEach(path -> {
                    try {
                        if (Files.isDirectory(path)) {
                            content.getSubDirectories().add(createNasDirectory(path));
                        } else if (Files.isRegularFile(path)) {
                            content.getFiles().add(createNasFile(path));
                        }
                    } catch (Exception e) {
                        log.error("处理文件/目录失败: {}", path, e);
                    }
                });
            }

            // 排序
            content.getSubDirectories().sort(Comparator.comparing(NasDirectory::getName));
            content.getFiles().sort(Comparator.comparing(NasFile::getName));

        } catch (IOException e) {
            log.error("读取目录内容失败: {}", relativePath, e);
        }

        return content;
    }

    /**
     * 递归获取目录下的所有视频文件
     *
     * @param relativePath 相对路径
     * @return 视频文件列表
     */
    public List<NasFile> getAllVideoFiles(String relativePath) {
        List<NasFile> videoFiles = new ArrayList<>();

        try {
            String fullPath = relativePath == null || relativePath.isEmpty()
                    ? NAS_BASE_PATH
                    : NAS_BASE_PATH + File.separator + relativePath;

            Path startPath = Paths.get(fullPath);

            if (!Files.exists(startPath)) {
                log.error("路径不存在: {}", fullPath);
                return videoFiles;
            }

            Files.walkFileTree(startPath, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    if (isVideoFile(file)) {
                        try {
                            videoFiles.add(createNasFile(file));
                        } catch (Exception e) {
                            log.error("处理视频文件失败: {}", file, e);
                        }
                    }
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult visitFileFailed(Path file, IOException exc) throws IOException {
                    log.warn("访问文件失败: {}", file, exc);
                    return FileVisitResult.CONTINUE;
                }
            });

        } catch (IOException e) {
            log.error("递归读取视频文件失败: {}", relativePath, e);
        }

        return videoFiles;
    }

    /**
     * 搜索文件（支持模糊匹配）
     *
     * @param keyword      搜索关键词
     * @param relativePath 搜索路径
     * @return 匹配的文件列表
     */
    public List<NasFile> searchFiles(String keyword, String relativePath) {
        List<NasFile> results = new ArrayList<>();

        if (keyword == null || keyword.trim().isEmpty()) {
            return results;
        }

        String searchKeyword = keyword.toLowerCase();

        try {
            String fullPath = relativePath == null || relativePath.isEmpty()
                    ? NAS_BASE_PATH
                    : NAS_BASE_PATH + File.separator + relativePath;

            Path startPath = Paths.get(fullPath);

            if (!Files.exists(startPath)) {
                return results;
            }

            Files.walkFileTree(startPath, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    String fileName = file.getFileName().toString().toLowerCase();
                    if (fileName.contains(searchKeyword)) {
                        try {
                            results.add(createNasFile(file));
                        } catch (Exception e) {
                            log.error("处理搜索结果文件失败: {}", file, e);
                        }
                    }
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult visitFileFailed(Path file, IOException exc) throws IOException {
                    log.warn("搜索时访问文件失败: {}", file, exc);
                    return FileVisitResult.CONTINUE;
                }
            });

        } catch (IOException e) {
            log.error("搜索文件失败: {}", keyword, e);
        }

        return results;
    }

    /**
     * 创建NAS目录对象
     */
    private NasDirectory createNasDirectory(Path path) throws IOException {
        NasDirectory directory = new NasDirectory();
        directory.setName(path.getFileName().toString());
        directory.setFullPath(path.toString());
        directory.setRelativePath(getRelativePath(path));
        directory.setLastModified(Files.getLastModifiedTime(path).toMillis());

        // 统计子文件数量
//        try (Stream<Path> children = Files.list(path)) {
//            long[] counts = children.mapToLong(child -> {
//                if (Files.isDirectory(child)) return 1000000; // 目录标记
//                else return 1; // 文件标记
//            }).toArray();
//
//            directory.setSubDirectoryCount((int) Arrays.stream(counts).filter(c -> c >= 1000000).count());
//            directory.setFileCount((int) Arrays.stream(counts).filter(c -> c < 1000000).count());
//        } catch (Exception e) {
//            log.warn("统计目录内容失败: {}", path, e);
//        }

        return directory;
    }

    /**
     * 创建NAS文件对象
     */
    private NasFile createNasFile(Path path) throws IOException {
        NasFile file = new NasFile();
        file.setName(path.getFileName().toString());
        file.setFullPath(path.toString());
        file.setRelativePath(getRelativePath(path));
        file.setSize(Files.size(path));
        file.setLastModified(Files.getLastModifiedTime(path).toMillis());
        file.setExtension(getFileExtension(path.getFileName().toString()));
        file.setFileType(determineFileType(path));

        return file;
    }

    /**
     * 获取相对路径
     */
    private String getRelativePath(Path path) {
        try {
            Path basePath = Paths.get(NAS_BASE_PATH);
            return basePath.relativize(path).toString();
        } catch (Exception e) {
            return path.toString();
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot).toLowerCase() : "";
    }

    /**
     * 判断是否为视频文件
     */
    private boolean isVideoFile(Path path) {
        String extension = getFileExtension(path.getFileName().toString());
        return VIDEO_EXTENSIONS.contains(extension);
    }

    /**
     * 判断文件类型
     */
    private String determineFileType(Path path) {
        String extension = getFileExtension(path.getFileName().toString());

        if (VIDEO_EXTENSIONS.contains(extension)) {
            return "video";
        } else if (IMAGE_EXTENSIONS.contains(extension)) {
            return "image";
        } else {
            return "other";
        }
    }

    /**
     * NAS目录信息
     */
    public static class NasDirectory {
        private String name;
        private String fullPath;
        private String relativePath;
        private long lastModified;
        private int subDirectoryCount;
        private int fileCount;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getFullPath() {
            return fullPath;
        }

        public void setFullPath(String fullPath) {
            this.fullPath = fullPath;
        }

        public String getRelativePath() {
            return relativePath;
        }

        public void setRelativePath(String relativePath) {
            this.relativePath = relativePath;
        }

        public long getLastModified() {
            return lastModified;
        }

        public void setLastModified(long lastModified) {
            this.lastModified = lastModified;
        }

        public int getSubDirectoryCount() {
            return subDirectoryCount;
        }

        public void setSubDirectoryCount(int subDirectoryCount) {
            this.subDirectoryCount = subDirectoryCount;
        }

        public int getFileCount() {
            return fileCount;
        }

        public void setFileCount(int fileCount) {
            this.fileCount = fileCount;
        }
    }

    /**
     * NAS文件信息
     */
    public static class NasFile {
        private String name;
        private String fullPath;
        private String relativePath;
        private long size;
        private long lastModified;
        private String extension;
        private String fileType;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getFullPath() {
            return fullPath;
        }

        public void setFullPath(String fullPath) {
            this.fullPath = fullPath;
        }

        public String getRelativePath() {
            return relativePath;
        }

        public void setRelativePath(String relativePath) {
            this.relativePath = relativePath;
        }

        public long getSize() {
            return size;
        }

        public void setSize(long size) {
            this.size = size;
        }

        public long getLastModified() {
            return lastModified;
        }

        public void setLastModified(long lastModified) {
            this.lastModified = lastModified;
        }

        public String getExtension() {
            return extension;
        }

        public void setExtension(String extension) {
            this.extension = extension;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        /**
         * 获取格式化的文件大小
         */
        public String getFormattedSize() {
            if (size < 1024) return size + " B";
            if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
            if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024));
            return String.format("%.1f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 目录内容
     */
    public static class NasDirectoryContent {
        private List<NasDirectory> subDirectories = new ArrayList<>();
        private List<NasFile> files = new ArrayList<>();

        public List<NasDirectory> getSubDirectories() {
            return subDirectories;
        }

        public void setSubDirectories(List<NasDirectory> subDirectories) {
            this.subDirectories = subDirectories;
        }

        public List<NasFile> getFiles() {
            return files;
        }

        public void setFiles(List<NasFile> files) {
            this.files = files;
        }
    }
}
