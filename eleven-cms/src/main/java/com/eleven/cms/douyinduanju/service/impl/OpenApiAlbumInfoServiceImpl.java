package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.AlbumInfo;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.mapper.OpenApiAlbumInfoMapper;
import com.eleven.cms.douyinduanju.service.IOpenApiAlbumInfoService;
import com.eleven.cms.douyinduanju.util.CoverUtilList;
import com.eleven.cms.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


@Service
public class OpenApiAlbumInfoServiceImpl extends ServiceImpl<OpenApiAlbumInfoMapper, OpenApiAlbumInfo> implements IOpenApiAlbumInfoService {


    @Override
    public OpenApiAlbumInfo getByAlbumId(String albumId) {
        return lambdaQuery().eq(OpenApiAlbumInfo::getAlbumId, albumId).last("limit 1").one();
    }

    @Override
    public void updateReviewStatusByAlbumId(String albumId, Integer version) {
        OpenApiAlbumInfo apiAlbumInfo = getByAlbumId(albumId);
        if (Objects.nonNull(apiAlbumInfo)) {
            OpenApiAlbumInfo update = new OpenApiAlbumInfo();
            if (Objects.nonNull(version)) {
                update.setVersion(version);
            }
            update.setReviewStatus(2);
            update.setId(apiAlbumInfo.getId());
            updateById(update);
        }
    }

    @Override
    public void updateAuthorizeStatusByAlbumId(String albumId) {
        OpenApiAlbumInfo apiAlbumInfo = getByAlbumId(albumId);
        if (Objects.nonNull(apiAlbumInfo)) {
            OpenApiAlbumInfo update = new OpenApiAlbumInfo();
            update.setAuthorizeStatus(2);
            update.setId(apiAlbumInfo.getId());
            updateById(update);
        }
    }

    @Override
    public void updateOnlineStatusByAlbumId(String albumId, Integer onlineStatus) {
        OpenApiAlbumInfo apiAlbumInfo = getByAlbumId(albumId);
        if (Objects.nonNull(apiAlbumInfo)) {
            OpenApiAlbumInfo update = new OpenApiAlbumInfo();
            update.setOnlineStatus(onlineStatus);
            update.setId(apiAlbumInfo.getId());
            updateById(update);
        }
    }

    @Override
    public AlbumInfo getAlbumInfoByAlbumInfoId(String id) {
        OpenApiAlbumInfo openApiAlbumInfo = this.getById(id);
        return getAlbumInfo(openApiAlbumInfo);
    }

    @Override
    public AlbumInfo getAlbumInfoByAlbumId(String albumId) {
        OpenApiAlbumInfo openApiAlbumInfo = this.getByAlbumId(albumId);
        return getAlbumInfo(openApiAlbumInfo);
    }

    private AlbumInfo getAlbumInfo(OpenApiAlbumInfo openApiAlbumInfo) {
        if (Objects.isNull(openApiAlbumInfo)) {
            throw new BusinessException("未找到对应数据");
        }
        List<Integer> tagList = intCoverList(openApiAlbumInfo.getTagList());
        List<String> coverList = strCoverList(openApiAlbumInfo.getCoverList());
        List<String> directorList = strCoverList(openApiAlbumInfo.getDirector());
        List<String> producerList = strCoverList(openApiAlbumInfo.getProducer());
        List<String> actorList = strCoverList(openApiAlbumInfo.getActor());
        List<String> screenWriterList = strCoverList(openApiAlbumInfo.getScreenWriter());
        AlbumInfo.RecordMaterial material = AlbumInfo.RecordMaterial.builder()
                .name(openApiAlbumInfo.getName())
                .producer(producerList)
                .actor(actorList)
                .duration(openApiAlbumInfo.getDuration())
                .seqsCount(openApiAlbumInfo.getSeqCount())
                .costDistributionUri(openApiAlbumInfo.getCostDistributionUri())
                .summary(openApiAlbumInfo.getSummary())
                .playletProductionCost(openApiAlbumInfo.getPlayletProductionCost())
                .screenWriter(screenWriterList)
                .assuranceUri(openApiAlbumInfo.getAssuranceUri())
                .productionOrganisation(openApiAlbumInfo.getProductionOrganisation())
                .director(directorList).build();
        AlbumInfo.BroadcastRecordInfo broadcastRecordInfo = null;
        if (Objects.nonNull(openApiAlbumInfo.getRecordType()) || StringUtils.isNotEmpty(openApiAlbumInfo.getBroadcastRecordNumber())) {
            broadcastRecordInfo = AlbumInfo.BroadcastRecordInfo.builder()
                    .recordType(openApiAlbumInfo.getRecordType())
                    .broadcastRecordNumber(openApiAlbumInfo.getAssuranceUri())
                    .build();
        }


        AlbumInfo.RecordAuditInfo recordAuditInfo = AlbumInfo.RecordAuditInfo.builder()
                .recordMaterial(material)
                .build();

        if (Objects.nonNull(broadcastRecordInfo)) {
            recordAuditInfo.setBroadcastRecordInfo(broadcastRecordInfo);
        }

        AlbumInfo.RecordInfo recordInfo = AlbumInfo.RecordInfo.builder()
                .licenseNum(openApiAlbumInfo.getLicenseNum())
                .registrationNum(openApiAlbumInfo.getRegistrationNum())
                .ordinaryRecordNum(openApiAlbumInfo.getOrdinaryRecordNum())
                .keyRecordNum(openApiAlbumInfo.getKeyRecordNum())
                .build();


        return AlbumInfo.builder()
                .title(openApiAlbumInfo.getTitle())
                .albumId(openApiAlbumInfo.getAlbumId())
                .coverList(coverList)
                .desp(openApiAlbumInfo.getDesp())
                .tagList(tagList)
                .seqNum(openApiAlbumInfo.getSeqNum())
                .qualification(openApiAlbumInfo.getQualification())
                .year(openApiAlbumInfo.getYear())
                .albumStatus(openApiAlbumInfo.getAlbumStatus())
                .recommendation(openApiAlbumInfo.getRecommendation())
                .recordInfo(recordInfo)
                .recordAuditInfo(recordAuditInfo)
                .build();
    }


    private static List<String> strCoverList(String str) {
        return CoverUtilList.strCoverList(str);
    }

    private static List<Integer> intCoverList(String str) {
        return CoverUtilList.intCoverList(str);
    }

    @Override
    public int updateByIdWithNull(OpenApiAlbumInfo openApiAlbumInfo) {
        return this.baseMapper.updateByIdWithNull(openApiAlbumInfo);
    }
}
