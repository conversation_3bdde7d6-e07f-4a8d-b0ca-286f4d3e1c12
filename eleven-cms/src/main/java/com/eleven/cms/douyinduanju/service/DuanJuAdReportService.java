package com.eleven.cms.douyinduanju.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.douyinduanju.dto.AdReportResultCallbackDTO;
import com.eleven.cms.douyinduanju.dto.MiniAppAdEntity;
import com.eleven.cms.douyinduanju.entity.MiniAppAdvertisement;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.enums.AppReportEventEnum;
import com.eleven.cms.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 广告平台上报工具类
 */
@Slf4j
@Service
public class DuanJuAdReportService {


    private static final String LOG_TAG = "广告平台上报工具API";

    @Resource
    IMiniAppAdvertisementService miniAppAdvertisementService;

    @Resource
    @Lazy
    private RabbitTemplate rabbitTemplate;

    /**
     * 调用广告平台微服务上报接口
     */
    public void sendAppReportMqMessage(MiniAppAdvertisement appAdvertisement, HttpServletRequest request) {


        /**
         *  channel 必传   projectId promotionId callback 这三个绝大部分情况下你能拿到
         *  deviceId ua remoteAddr 也是必传的 作为兜底情况去匹配归因  adExtraMap relayRouteKey relayExchange 这三个结合使用 用来回复你上报情况
         */

        MiniAppAdEntity appAdEntity = new MiniAppAdEntity();

        //赋值IP
        String clientIp = getClientIp(request);
        appAdEntity.setRemoteAddr(clientIp);
        appAdvertisement.setClientIp(clientIp);

        //赋值代理
        String userAgent = request.getHeader("User-Agent");
        appAdEntity.setUa(userAgent);
        appAdvertisement.setUserAgent(userAgent);
        appAdEntity.setEventType(AppReportEventEnum.ACTIVE.getDescription());

        MiniAppAdvertisement advertisement = miniAppAdvertisementService.lambdaQuery().eq(MiniAppAdvertisement::getUserId, appAdvertisement.getUserId()).last("limit 1").one();
        if (Objects.nonNull(advertisement)) {
            log.info("用户:{}已经上报过", appAdvertisement.getUserId());
            return;
        }

        //保存数据
        miniAppAdvertisementService.save(appAdvertisement);
        convertToAdEntity(appAdvertisement, appAdEntity);

        Map<String, String> adExtraMap = new HashMap<>();
        adExtraMap.put("adId", appAdvertisement.getId());
        appAdEntity.setAdExtraMap(adExtraMap);
        //上报
        sendAppAdReportMessage(appAdEntity);
    }

    public String getClientIp(HttpServletRequest request) {
        String xffHeader = request.getHeader("X-Forwarded-For");
        if (xffHeader == null) {
            // 没有经过代理，直接返回客户端IP
            return request.getRemoteAddr();
        }
        // 经过代理，解析X-Forwarded-For头
        // 格式：client_ip, proxy1_ip, proxy2_ip...
        String[] ips = xffHeader.split(",");
        return ips[0].trim(); // 返回第一个IP（客户端原始IP）
    }

    /**
     * 将MiniAppAdvertisement对象转换为MiniAppAdEntity对象
     *
     * @param advertisement 广告实体对象
     */
    private void convertToAdEntity(MiniAppAdvertisement advertisement, MiniAppAdEntity appAdEntity) {

        // 基础字段赋值
        appAdEntity.setCallback(advertisement.getClickId());
        appAdEntity.setPromotionId(advertisement.getPromotionId());
        appAdEntity.setProjectId(advertisement.getProjectId());
        appAdEntity.setPlatform(advertisement.getPlatform());
        appAdEntity.setDeviceId(advertisement.getDeviceId());
        appAdEntity.setChannel(advertisement.getChannel());

        appAdEntity.setCallbackSource(advertisement.getCallbackSource());
        appAdEntity.setMiniAppCallback(advertisement.getMiniAppCallback());
        HashMap<String, String> adExtraMap = new HashMap<>();
        adExtraMap.put("adId", advertisement.getId());
        appAdEntity.setAdExtraMap(adExtraMap);

        // 设置回复路由键
        appAdEntity.setReplayRouteKey(RabbitMQConfig.AD_REPORT_RESULT_CALLBACK_QUEUE_NAME);
//        appAdEntity.setReplayExchange(RabbitMQConfig.AD_REPORT_RESULT_CALLBACK_EXCHANGE_NAME);
    }


    @Resource
    private IMiniAppDuanJuOrderService miniAppDuanJuOrderService;

    /**
     * 三方支付上报
     */
    public void sendAppAdReportMessage(MiniAppDuanJuOrder duanJuOrder, AppReportEventEnum eventType) {
        try {
            String userId = duanJuOrder.getUserId();

            //增加激活当日 才需要上报
            MiniAppAdvertisement advertisement = miniAppAdvertisementService.lambdaQuery().eq(MiniAppAdvertisement::getUserId, userId)
                    .between(MiniAppAdvertisement::getCreateTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                    .orderByDesc(MiniAppAdvertisement::getCreateTime).last("limit 1").one();
            if (Objects.isNull(advertisement)) {
                log.info("用户:{}没有找到对应的广告信息", userId);
                return;
            }

            //校验是否已经支付超过两次
            Integer count = miniAppDuanJuOrderService.lambdaQuery().eq(MiniAppDuanJuOrder::getUserId, userId).eq(MiniAppDuanJuOrder::getPayStatus, 1).count();
            if (count > 1) {
                log.info("用户:{}已支超过一次,不再上报,支付次数{}", userId, count);
                return;
            }

            MiniAppAdEntity appAdEntity = new MiniAppAdEntity();
            convertToAdEntity(advertisement, appAdEntity);
            Map<String, String> adExtraMap = appAdEntity.getAdExtraMap();
            if (adExtraMap == null) {
                adExtraMap = new HashMap<>();
            }
            adExtraMap.put("orderNo", duanJuOrder.getOrderNo());
            if (Objects.nonNull(duanJuOrder.getOrderAmount())) {
                BigDecimal orderAmount = duanJuOrder.getOrderAmount();
                BigDecimal amount = orderAmount.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
                adExtraMap.put("purchaseAmount", amount.toString());
                appAdEntity.setPurchaseAmount(amount.toString());
            }
            appAdEntity.setEventType(eventType.getDescription());
            appAdEntity.setUa(advertisement.getUserAgent());
            appAdEntity.setRemoteAddr(advertisement.getClientIp());
            sendAppAdReportMessage(appAdEntity);
        } catch (Exception e) {
            log.info("{}-用户:{}支付成功,发送广告平台上报消息-adReportMessage:{},异常!", LOG_TAG, duanJuOrder, eventType, e);
        }
    }


    public void sendAppAdReportMessage(MiniAppAdEntity adReportMessage) {
        try {
            String jsonString = JacksonUtils.toJson(adReportMessage);
            rabbitTemplate.convertAndSend("miniApp.ad.report.queue", jsonString);
            log.info("{}-广告平台上报消息-adReportMessage:{}", LOG_TAG, adReportMessage);
        } catch (Exception e) {
            log.info("{}-广告平台上报消息-adReportMessage:{},异常!", LOG_TAG, adReportMessage, e);
        }
    }

    public Map<String, Object> miniAppAdEntityToMap(MiniAppAdEntity entity) {
        if (entity == null) {
            return null;
        }

        BeanWrapper beanWrapper = new BeanWrapperImpl(entity);
        java.beans.PropertyDescriptor[] propertyDescriptors = beanWrapper.getPropertyDescriptors();

        Map<String, Object> resultMap = new HashMap<>();
        for (java.beans.PropertyDescriptor descriptor : propertyDescriptors) {
            String propertyName = descriptor.getName();
            if (!"class".equals(propertyName)) {
                Object value = beanWrapper.getPropertyValue(propertyName);
                resultMap.put(propertyName, value);
            }
        }
        return resultMap;
    }

    @RabbitListener(queues = RabbitMQConfig.AD_REPORT_RESULT_CALLBACK_QUEUE_NAME, concurrency = "1")
    public void handleAdReportResultCallback(String mqMessage) {
        log.info("{}-接收到广告上报结果回调消息: {}", LOG_TAG, mqMessage);
        try {
            AdReportResultCallbackDTO callbackDTO = JSONObject.parseObject(mqMessage, AdReportResultCallbackDTO.class);
            // 参数校验
            if (callbackDTO == null) {
                log.warn("{}-接收到空的回调消息", LOG_TAG);
                return;
            }
            //处理回调消息
            Map<String, String> adExtraMap = callbackDTO.getAdExtraMap();
            if (adExtraMap != null) {
                String adId = adExtraMap.get("adId");
                MiniAppAdvertisement miniAppAdvertisement = miniAppAdvertisementService.getById(adId);
                miniAppAdvertisement.setReportStatus(callbackDTO.getReportStatus());
                miniAppAdvertisement.setContent(callbackDTO.getContent());
                miniAppAdvertisementService.updateById(miniAppAdvertisement);

                String orderNo = adExtraMap.get("orderNo");
                MiniAppDuanJuOrder order = miniAppDuanJuOrderService.getByOrderNo(orderNo);
                if (order != null) {
                    order.setReportStatus(callbackDTO.getReportStatus());
                    if (callbackDTO.getReportStatus() == 1) {
                        order.setAdReportCallBackStatusStr("回传成功");
                    } else if (callbackDTO.getReportStatus() == 2) {
                        order.setAdReportCallBackStatusStr("扣量不上传");
                    } else {
                        order.setAdReportCallBackStatusStr("上报失败");
                    }
                    order.setChannel(miniAppAdvertisement.getChannel());
                    miniAppDuanJuOrderService.updateById(order);
                }
            }
            log.info("{}-广告上报结果回调处理成功, 状态: {}, 事件类型: {}",
                    LOG_TAG, callbackDTO.getReportStatus(), callbackDTO.getEventType());

        } catch (Exception e) {
            log.error("{}-处理广告上报结果回调消息异常: {}", LOG_TAG, mqMessage, e);
        }
    }
}
