package com.eleven.cms.douyinduanju.service.impl;

import com.eleven.cms.douyinduanju.entity.OpenApiUploadPicRecord;
import com.eleven.cms.douyinduanju.mapper.OpenApiUploadPicRecordMapper;
import com.eleven.cms.douyinduanju.service.IOpenApiUploadPicRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: openApi_upload_pic_record
 * @Author: jeecg-boot
 * @Date:   2025-05-23
 * @Version: V1.0
 */
@Service
public class OpenApiUploadPicRecordServiceImpl extends ServiceImpl<OpenApiUploadPicRecordMapper, OpenApiUploadPicRecord> implements IOpenApiUploadPicRecordService {

}
