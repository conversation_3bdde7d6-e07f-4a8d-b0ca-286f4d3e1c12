package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

import java.util.List;

@Data
public class DramaReq {
    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String columnId;
    private String dramaId;
    private String name;
    /**
     * 筛选条件
     */
    private String selections;

    /**
     * 短剧生效状态 1:生效中 0: 未生效
     */
    private Integer status;

    /**
     * 送审状态:1:未送审,2:审核中 3:未通过审核  4:审核通过
     */
    private Integer reviewStatus;

    /**
     * 上下架状态：1:未上线,2:上线,3:下线
     */
    private Integer onlineStatus;

    private Integer authorizeStatus;

    private Integer title;
    private List<String> miniDramaIdList;
}
