package com.eleven.cms.douyinduanju.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.annotation.TokenRequired;
import com.eleven.cms.douyinduanju.dto.*;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaEpisode;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;
import com.eleven.cms.douyinduanju.entity.OpenApiUploadVideoRecord;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaEpisodeService;
import com.eleven.cms.douyinduanju.service.IMiniAppMiniDramaService;
import com.eleven.cms.douyinduanju.service.IOpenApiUploadVideoRecordService;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import com.eleven.cms.douyinduanju.util.TokenUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description: mini_app_drama_episode
 * @Author: jeecg-boot
 * @Date: 2025-06-13
 * @Version: V1.0
 */
@Api(tags = "mini_app_drama_episode")
@RestController
@RequestMapping("/douyinduanju/miniAppDramaEpisode")
@Slf4j
public class MiniAppDramaEpisodeController extends JeecgController<MiniAppDramaEpisode, IMiniAppDramaEpisodeService> {
    @Autowired
    private IMiniAppDramaEpisodeService miniAppDramaEpisodeService;

    @Resource
    private IMiniAppMiniDramaService miniAppMiniDramaService;

    /**
     * 分页列表查询
     *
     * @param miniAppDramaEpisode
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "mini_app_drama_episode-分页列表查询")
    @ApiOperation(value = "mini_app_drama_episode-分页列表查询", notes = "mini_app_drama_episode-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppDramaEpisode miniAppDramaEpisode,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<MiniAppDramaEpisode> queryWrapper = QueryGenerator.initQueryWrapper(miniAppDramaEpisode, req.getParameterMap());
        queryWrapper.eq("is_deleted", 0);
        Page<MiniAppDramaEpisode> page = new Page<MiniAppDramaEpisode>(pageNo, pageSize);
        IPage<MiniAppDramaEpisode> pageList = miniAppDramaEpisodeService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    @GetMapping("/pageEpisode")
    public Result<?> pageEpisode(EpisodeReq req, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<EpisodeVO> page = new Page<>(pageNo, pageSize);
        IPage<EpisodeVO> miniAppMiniDramaVOIPage = miniAppDramaEpisodeService.pageEpisode(req, page);
        return Result.ok(miniAppMiniDramaVOIPage);
    }
    /**
     * 添加
     *
     * @param miniAppDramaEpisode
     * @return
     */
    @AutoLog(value = "mini_app_drama_episode-添加")
    @ApiOperation(value = "mini_app_drama_episode-添加", notes = "mini_app_drama_episode-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniAppDramaEpisode miniAppDramaEpisode) {
        setPayFlag(miniAppDramaEpisode);
        miniAppDramaEpisodeService.save(miniAppDramaEpisode);
        return Result.ok("添加成功！");
    }

    private void setPayFlag(MiniAppDramaEpisode miniAppDramaEpisode) {
        MiniAppMiniDrama miniDrama = miniAppMiniDramaService.getByAlbumInfoId(miniAppDramaEpisode.getMiniDramaId());
        if (Objects.isNull(miniDrama)) {
            miniDrama = miniAppMiniDramaService.getByAlbumId(miniAppDramaEpisode.getAlbumId());
        }
        if (Objects.nonNull(miniDrama)) {
            Integer freeNum = miniDrama.getFreeNum();
            if (Objects.isNull(freeNum)) {
                miniAppDramaEpisode.setPayFlag(1);
            } else {
                Integer episodeSeq = miniAppDramaEpisode.getEpisodeSeq();
                if (episodeSeq <= freeNum) {
                    miniAppDramaEpisode.setPayFlag(0);
                }
            }
        }
    }

    /**
     * 编辑
     *
     * @param miniAppDramaEpisode
     * @return
     */
    @AutoLog(value = "mini_app_drama_episode-编辑")
    @ApiOperation(value = "mini_app_drama_episode-编辑", notes = "mini_app_drama_episode-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniAppDramaEpisode miniAppDramaEpisode) {

        setPayFlag(miniAppDramaEpisode);
        miniAppDramaEpisodeService.updateById(miniAppDramaEpisode);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_drama_episode-通过id删除")
    @ApiOperation(value = "mini_app_drama_episode-通过id删除", notes = "mini_app_drama_episode-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniAppDramaEpisodeService.lambdaUpdate().set(MiniAppDramaEpisode::getIsDeleted, 1).eq(MiniAppDramaEpisode::getId, id).update();
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "mini_app_drama_episode-批量删除")
    @ApiOperation(value = "mini_app_drama_episode-批量删除", notes = "mini_app_drama_episode-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> list = Arrays.asList(ids.split(","));
        if (CollectionUtil.isEmpty(list)) {
            return Result.ok();
        }

        miniAppDramaEpisodeService.lambdaUpdate().set(MiniAppDramaEpisode::getIsDeleted, 1).in(MiniAppDramaEpisode::getId, list).update();
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_drama_episode-通过id查询")
    @ApiOperation(value = "mini_app_drama_episode-通过id查询", notes = "mini_app_drama_episode-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppDramaEpisode miniAppDramaEpisode = miniAppDramaEpisodeService.getById(id);
        if (miniAppDramaEpisode == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppDramaEpisode);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppDramaEpisode
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniAppDramaEpisode miniAppDramaEpisode) {
        return super.exportXls(request, miniAppDramaEpisode, MiniAppDramaEpisode.class, "mini_app_drama_episode");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppDramaEpisode.class);
    }

    @Resource
    private DouYinHttpUtil douYinHttpUtil;

    @GetMapping("/getPlayInfo")
    public Result<?> getPlayInfo(Long albumId, Long douYinEpisodeId) {
        if (Objects.isNull(douYinEpisodeId) || Objects.isNull(albumId)) {
            return Result.error("参数不能为空");
        }
        OpenApiRes result = douYinHttpUtil.getPlayInfo(douYinEpisodeId, albumId);
        if (!result.isSuccess()) {
            return Result.error(result.getErrMsg());
        }
        OpenApiRes.PlayInfo playInfo = result.getPlayInfo();
        String playUrl = playInfo.getPlayUrl();
        String urlExpire = playInfo.getUrlExpire();
        Date date = new Date(Long.parseLong(urlExpire) * 1000);
        miniAppDramaEpisodeService.lambdaUpdate().set(MiniAppDramaEpisode::getDouYinVideoUrl, playUrl).set(MiniAppDramaEpisode::getDouYinVideoUrlExpire, date).eq(MiniAppDramaEpisode::getDouYinEpisodeId, douYinEpisodeId).update();
        return Result.ok("获取成功");
    }

    @GetMapping("/syncEpisode")
    public Result<?> syncEpisode() {
        List<String> recordIdList = miniAppDramaEpisodeService.getByVideoRecordId();
        for (String id : recordIdList) {
            miniAppDramaEpisodeService.syncOpenApiUploadVideoRecord(id);
        }
        return Result.ok(recordIdList);
    }


    @GetMapping("/syncEpisodeToDoyYin")
    public Result<?> syncEpisodeToDoyYin(String albumId) {
        String[] split = albumId.split(",");
        for (String id : split) {
            List<MiniAppDramaEpisode> miniAppDramaEpisodes = miniAppDramaEpisodeService.lambdaQuery().eq(MiniAppDramaEpisode::getAlbumId, id).isNull(MiniAppDramaEpisode::getDouYinEpisodeId).list();
            for (MiniAppDramaEpisode miniAppDramaEpisode : miniAppDramaEpisodes) {
                addEpisodeByEpisode(miniAppDramaEpisode.getId());
            }
        }
        return Result.ok("新增成功");
    }

    @Resource
    private IOpenApiUploadVideoRecordService uploadVideoRecordService;

    @GetMapping("/addEpisodeByEpisode")
    public Result<?> addEpisodeByEpisode(String id) {
        List<AlbumInfo.EpisodeInfo> episodeInfoList = new ArrayList<>();
        MiniAppDramaEpisode episode = miniAppDramaEpisodeService.getById(id);
        if (Objects.isNull(episode)) {
            return Result.error("剧集不存在");
        }
        if (StringUtils.isEmpty(episode.getCoverId())) {
            return Result.error("封面id不存在");
        }
        if (StringUtils.isNotEmpty(episode.getDouYinEpisodeId())) {
            return Result.error("该剧集已存在");
        }
        OpenApiUploadVideoRecord videoRecord = uploadVideoRecordService.getById(episode.getVideoRecordId());
        String coverId = episode.getCoverId();

        ArrayList<String> coverList = new ArrayList<>(Arrays.asList(coverId.split(",")));
        AlbumInfo.EpisodeInfo episodeInfo = new AlbumInfo.EpisodeInfo();
        episodeInfo.setTitle(episode.getDramaTitle());
        episodeInfo.setOpenVideoId(videoRecord.getOpenVideoId());
        episodeInfo.setCoverList(coverList);
        episodeInfo.setSeq(episode.getEpisodeSeq());
        episodeInfoList.add(episodeInfo);
        OpenApiRes openApiRes = douYinHttpUtil.addEpisode(episode.getAlbumId(), episodeInfoList);
        if (!openApiRes.isSuccess()) {
            return Result.error(openApiRes.getErrMsg());
        }
        OpenApiRes.EpisodeData episodeData = openApiRes.getEpisodeData();
        Map<String, String> episodeIdMap = episodeData.getEpisodeIdMap();
        String douYinEpisodeId = episodeIdMap.get(String.valueOf(videoRecord.getSeq()));
        videoRecord.setEpisodeId(douYinEpisodeId);
        episode.setDouYinEpisodeId(douYinEpisodeId);
        uploadVideoRecordService.updateById(videoRecord);
        miniAppDramaEpisodeService.updateById(episode);

//        openApiRes.getEpisodeData().getEpisodeIdMap().forEach((key, value) -> {
//            miniAppDramaEpisodeService.lambdaUpdate().set(MiniAppDramaEpisode::getDouYinEpisodeId, value)
//                    .eq(MiniAppDramaEpisode::getEpisodeSeq, key)
//                    .eq(MiniAppDramaEpisode::getAlbumId, videoRecord.getAlbumId()).update();
//
//            uploadVideoRecordService.lambdaUpdate().set(OpenApiUploadVideoRecord::getEpisodeId, value)
//                    .eq(OpenApiUploadVideoRecord::getAlbumId, videoRecord.getAlbumId())
//                    .eq(OpenApiUploadVideoRecord::getSeq, key);
//        });

        return Result.ok("新增剧集成功");
    }

    /**
     * 根据albumId获取剧集列表
     */
    @AutoLog(value = "mini_app_drama_episode-根据albumId获取剧集列表")
    @ApiOperation(value = "根据albumId获取剧集列表", notes = "根据albumId获取剧集列表")
    @GetMapping("/getByAlbumId")
    public Result<?> getByAlbumId(@RequestParam(name = "albumId", required = true) String albumId) {
        try {
            List<MiniAppDramaEpisodeResponse> episodes = miniAppDramaEpisodeService.getByAlbumId(albumId);

            return Result.ok(episodes);
        } catch (Exception e) {
            log.error("根据albumId获取剧集列表异常", e);
            return Result.error("获取剧集列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据albumId获取剧集列表（带收藏状态）
     */
    @AutoLog(value = "mini_app_drama_episode-根据albumId获取剧集列表（带收藏状态）")
    @ApiOperation(value = "根据albumId获取剧集列表（带收藏状态）", notes = "根据albumId获取剧集列表（带收藏状态）")
    @GetMapping("/getByAlbumIdWithFavorite")
    @TokenRequired
    public Result<?> getByAlbumIdWithFavorite(@RequestParam(name = "albumId", required = true) String albumId) {
        try {
            Integer currentUserId = TokenUtils.getCurrentUserId();
            List<MiniAppDramaEpisodeResponse> episodes = miniAppDramaEpisodeService
                    .getByAlbumIdWithFavorite(albumId, currentUserId);
            return Result.ok(episodes);
        } catch (Exception e) {
            log.error("根据albumId获取剧集列表（带收藏状态）异常", e);
            return Result.error("获取剧集列表失败: " + e.getMessage());
        }
    }
}
