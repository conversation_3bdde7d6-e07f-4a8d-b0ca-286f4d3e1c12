package com.eleven.cms.douyinduanju.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * 抖音支付回调验签工具类
 */
@Slf4j
public class PaymentCallbackVerifyUtil {

    /**
     * 验证抖音支付回调签名
     *
     * @param body      请求体内容
     * @param timestamp 时间戳
     * @param nonce     随机字符串
     * @param signature 签名
     * @param publicKey 抖音平台公钥
     * @return 验签是否通过
     */
    public static boolean verifySignature(String body, String timestamp, String nonce, String signature, String publicKey) {
        try {
            // 参数验证
            if (StringUtils.isAnyBlank(body, timestamp, nonce, signature, publicKey)) {
                log.error("验签参数不能为空");
                return false;
            }

            // 构建待签名字符串
            StringBuilder buffer = new StringBuilder();
            buffer.append(timestamp).append("\n");
            buffer.append(nonce).append("\n");
            buffer.append(body).append("\n");
            String message = buffer.toString();

            // 验证签名
            Signature sign = Signature.getInstance("SHA256withRSA");
            sign.initVerify(string2PublicKey(publicKey));
            sign.update(message.getBytes(StandardCharsets.UTF_8));
            boolean result = sign.verify(Base64.getDecoder().decode(signature.getBytes(StandardCharsets.UTF_8)));

            log.info("支付回调验签结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("支付回调验签失败", e);
            return false;
        }
    }

    /**
     * 字符串转公钥
     *
     * @param publicKey 公钥字符串
     * @return PublicKey对象
     * @throws Exception 转换异常
     */
    private static PublicKey string2PublicKey(String publicKey) throws Exception {
        byte[] decoded = Base64.getDecoder().decode(publicKey);
        return KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
    }
}
