package com.eleven.cms.douyinduanju.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.entity.OpenApiUploadVideoRecord;
import com.eleven.cms.douyinduanju.service.IOpenApiUploadVideoRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: openApi_upload_video_record
 * @Author: jeecg-boot
 * @Date: 2025-06-09
 * @Version: V1.0
 */
@Api(tags = "openApi_upload_video_record")
@RestController
@RequestMapping("/douyinduanju/openApiUploadVideoRecord")
@Slf4j
public class OpenApiUploadVideoRecordController extends JeecgController<OpenApiUploadVideoRecord, IOpenApiUploadVideoRecordService> {
    @Autowired
    private IOpenApiUploadVideoRecordService openApiUploadVideoRecordService;

    /**
     * 分页列表查询
     *
     * @param openapiUploadVideoRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "openApi_upload_video_record-分页列表查询")
    @ApiOperation(value = "openApi_upload_video_record-分页列表查询", notes = "openApi_upload_video_record-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(OpenApiUploadVideoRecord openapiUploadVideoRecord,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<OpenApiUploadVideoRecord> queryWrapper = QueryGenerator.initQueryWrapper(openapiUploadVideoRecord, req.getParameterMap());
        Page<OpenApiUploadVideoRecord> page = new Page<OpenApiUploadVideoRecord>(pageNo, pageSize);
        IPage<OpenApiUploadVideoRecord> pageList = openApiUploadVideoRecordService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param openapiUploadVideoRecord
     * @return
     */
    @AutoLog(value = "openApi_upload_video_record-添加")
    @ApiOperation(value = "openApi_upload_video_record-添加", notes = "openApi_upload_video_record-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody OpenApiUploadVideoRecord openapiUploadVideoRecord) {
        openApiUploadVideoRecordService.save(openapiUploadVideoRecord);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param openapiUploadVideoRecord
     * @return
     */
    @AutoLog(value = "openApi_upload_video_record-编辑")
    @ApiOperation(value = "openApi_upload_video_record-编辑", notes = "openApi_upload_video_record-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody OpenApiUploadVideoRecord openapiUploadVideoRecord) {
        openApiUploadVideoRecordService.updateById(openapiUploadVideoRecord);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "openApi_upload_video_record-通过id删除")
    @ApiOperation(value = "openApi_upload_video_record-通过id删除", notes = "openApi_upload_video_record-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        openApiUploadVideoRecordService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "openApi_upload_video_record-批量删除")
    @ApiOperation(value = "openApi_upload_video_record-批量删除", notes = "openApi_upload_video_record-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.openApiUploadVideoRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "openApi_upload_video_record-通过id查询")
    @ApiOperation(value = "openApi_upload_video_record-通过id查询", notes = "openApi_upload_video_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        OpenApiUploadVideoRecord openapiUploadVideoRecord = openApiUploadVideoRecordService.getById(id);
        if (openapiUploadVideoRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(openapiUploadVideoRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param openapiUploadVideoRecord
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OpenApiUploadVideoRecord openapiUploadVideoRecord) {
        return super.exportXls(request, openapiUploadVideoRecord, OpenApiUploadVideoRecord.class, "openApi_upload_video_record");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OpenApiUploadVideoRecord.class);
    }

}
