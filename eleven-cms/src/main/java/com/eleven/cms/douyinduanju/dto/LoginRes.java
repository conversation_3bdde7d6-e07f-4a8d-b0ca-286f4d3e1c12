package com.eleven.cms.douyinduanju.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户登录响应DTO
 */
@Data
@ApiModel(value = "用户登录响应", description = "用户登录成功后返回的用户信息")
public class LoginRes {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;
    private String openId;

    /**
     * 会员状态 1:非会员 2:订阅会员 3:三分支付会员
     */
    @ApiModelProperty(value = "会员状态：1-非会员，2-订阅会员，3-三分支付会员")
    private Integer memberStatus;

    /**
     * 是否付费 1: 付费 0未付费
     */
    @ApiModelProperty(value = "是否付费：1-付费，0-未付费")
    private Integer payFlat;

    /**
     * 用户来源
     */
    @ApiModelProperty(value = "用户来源")
    private String source;
    private String resource;

    /**
     * 注册时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "注册时间")
    private Date registerTime;

    /**
     * 最近启动时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近启动时间")
    private Date lastOperateTime;

    private String token;
    private String purePhoneNumber;
    private String phoneNumber;
    private String sessionKey;
    private String anonymousOpenid;

    /**
     * 会员类型 0:非会员 2:订阅会员  3:三方支付会员
     */
    private Integer memberType;
}
