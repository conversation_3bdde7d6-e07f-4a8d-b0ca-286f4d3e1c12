package com.eleven.cms.douyinduanju.controller;

import com.eleven.cms.douyinduanju.service.IUserMembershipService;
import com.eleven.cms.douyinduanju.task.UserExpirationCheckTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户过期检查控制器
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Api(tags = "用户过期检查管理")
@RestController
@RequestMapping("/api/user/expiration")
@Slf4j
public class UserExpirationController {

    @Autowired
    private IUserMembershipService userMembershipService;

    @Autowired
    private UserExpirationCheckTask userExpirationCheckTask;

    /**
     * 检查单个用户过期状态
     */
    @AutoLog(value = "检查单个用户过期状态")
    @ApiOperation(value = "检查单个用户过期状态", notes = "检查指定用户的过期状态并进行相应处理")
    @PostMapping(value = "/check/{userId}")
    public Result<?> checkUserExpiration(@ApiParam("用户ID") @PathVariable String userId) {
        try {
            log.info("收到用户过期检查请求，用户ID: {}", userId);

            if (userId == null || userId.trim().isEmpty()) {
                return Result.error("用户ID不能为空");
            }

            boolean result = userMembershipService.checkAndHandleUserExpiration(userId);

            if (result) {
                return Result.ok("用户过期状态检查完成");
            } else {
                return Result.error("用户过期状态检查失败");
            }

        } catch (Exception e) {
            log.error("检查用户过期状态异常，用户ID: {}", userId, e);
            return Result.error("检查用户过期状态失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发批量用户过期检查
     */
    @AutoLog(value = "手动触发批量用户过期检查")
    @ApiOperation(value = "手动触发批量用户过期检查", notes = "手动触发所有过期用户的检查和处理")
    @PostMapping(value = "/check/batch")
    public Result<?> batchCheckUserExpiration() {
        try {
            log.info("收到批量用户过期检查请求");

            userExpirationCheckTask.manualCheckUserExpiration();

            return Result.ok("批量用户过期检查已触发");

        } catch (Exception e) {
            log.error("批量用户过期检查异常", e);
            return Result.error("批量用户过期检查失败: " + e.getMessage());
        }
    }


    /**
     * 批量处理过期用户
     */
    @AutoLog(value = "批量处理过期用户")
    @ApiOperation(value = "批量处理过期用户", notes = "批量处理所有过期用户")
    @PostMapping(value = "/process/batch")
    public Result<?> batchProcessExpiredUsers() {
        try {
            log.info("收到批量处理过期用户请求");

            int processedCount = userExpirationCheckTask.processExpiredUsers();

            return Result.ok("批量处理完成，处理用户数: " + processedCount);

        } catch (Exception e) {
            log.error("批量处理过期用户异常", e);
            return Result.error("批量处理过期用户失败: " + e.getMessage());
        }
    }
}
