<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.MiniAppDramaViewingHistoryMapper">


    <!-- 查询用户每个剧目的最新观看记录的albumId -->
    <select id="getLastHistory" resultType="java.lang.String">
        SELECT album_id
        FROM mini_app_drama_viewing_history
        WHERE user_id = #{userId}
        GROUP BY album_id
        ORDER BY MAX(update_time) DESC LIMIT 20
    </select>
    <!-- 查询用户每个剧目的最新观看记录 -->
    <select id="getHistoryByAlbumId" resultType="com.eleven.cms.douyinduanju.dto.LookHistoryVO">
        SELECT h1.id,
        h1.drama_id as dramaId,
        h1.album_id as albumId,
        h1.album_name as albumName,
        h1.episode_num as episodeNum,
        h1.watch_duration as watchDuration,
        h1.dou_yin_episode_id as douYinEpisodeId,
        h1.cover_url as coverUrl,
        h1.seq_count as seqCount,
        h1.update_time as lastUpdateTime
        FROM mini_app_drama_viewing_history h1
        INNER JOIN (
        SELECT album_id, MAX(update_time) as max_update_time
        FROM mini_app_drama_viewing_history
        WHERE album_id IN
        <foreach item="item" index="index" collection="albumIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY album_id
        ) h2 ON h1.album_id = h2.album_id AND h1.update_time = h2.max_update_time
        ORDER BY h1.update_time DESC
    </select>
    <!-- 查询用户每个剧目的最大观看集数 -->
    <select id="getMaxSeqHistory" resultType="com.eleven.cms.douyinduanju.dto.LookHistoryVO">
        select distinct h1.*
        from mini_app_drama_viewing_history h1
                 inner join (SELECT album_id,
                                    MAX(episode_num) as episodeNum,
                                    MAX(update_time) as lastUpdateTime
                             FROM mini_app_drama_viewing_history
                             WHERE user_id = #{userId}
                               AND episode_num IS NOT NULL
                             GROUP BY album_id
                             ORDER BY lastUpdateTime) h2
                            on h1.album_id = h2.album_id and h1.episode_num = h2.episodeNum and user_id = #{userId}

    </select>

    <!-- 获取用户每个剧目的最新观看历史记录 -->
    <select id="getLatestHistoryForEachDrama" resultType="com.eleven.cms.douyinduanju.dto.LookHistoryVO">
        SELECT h1.id,
               h1.drama_id           as dramaId,
               h1.album_id           as albumId,
               h1.album_name         as albumName,
               h1.episode_num        as episodeNum,
               h1.watch_duration     as watchDuration,
               h1.dou_yin_episode_id as douYinEpisodeId,
               h1.cover_url          as coverUrl,
               h1.seq_count          as seqCount,
               h1.update_time        as lastUpdateTime
        FROM mini_app_drama_viewing_history h1
                 INNER JOIN (SELECT album_id, MAX(update_time) as max_update_time
                             FROM mini_app_drama_viewing_history
                             WHERE user_id = #{userId}
                             GROUP BY album_id) h2 ON h1.album_id = h2.album_id AND h1.update_time = h2.max_update_time
        WHERE h1.user_id = #{userId}
        ORDER BY h1.update_time DESC LIMIT 50
    </select>

    <select id="getFinishDrama" resultType="com.eleven.cms.douyinduanju.dto.LookHistoryVO">
        SELECT * from mini_app_drama_viewing_history h1 where episode_num = seq_count and user_id = #{userId};
    </select>

</mapper>
