package com.eleven.cms.douyinduanju.annotation;

import java.lang.annotation.*;

/**
 * Token校验注解
 * 用于标记需要进行token校验的方法或类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TokenRequired {

    /**
     * 是否必须校验token，默认为true
     */
    boolean required() default true;

    /**
     * token类型，用于区分不同业务场景的token
     */
    String tokenType() default "default";

    /**
     * 错误提示信息
     */
    String message() default "Token校验失败";
}
