package com.eleven.cms.douyinduanju.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.entity.DuanJuPackage;
import com.eleven.cms.douyinduanju.service.IDuanJuPackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: duan_ju_package
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Api(tags = "duan_ju_package")
@RestController
@RequestMapping("/douyinduanju/duanJuPackage")
@Slf4j
public class DuanJuPackageController extends JeecgController<DuanJuPackage, IDuanJuPackageService> {
    @Autowired
    private IDuanJuPackageService duanJuPackageService;

    /**
     * 分页列表查询
     *
     * @param duanJuPackage
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "duan_ju_package-分页列表查询", notes = "duan_ju_package-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DuanJuPackage duanJuPackage, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String name = duanJuPackage.getName();
        if (StringUtils.isNotEmpty(name)) {
            name = name.replace("*", "");
        }
        QueryWrapper<DuanJuPackage> queryWrapper = new QueryWrapper<>();
        Page<DuanJuPackage> page = new Page<DuanJuPackage>(pageNo, pageSize);
        queryWrapper.eq("is_deleted", 0);
        queryWrapper.orderByAsc("order_no");
        queryWrapper.orderByDesc("create_time");
        queryWrapper.like(StringUtils.isNotEmpty(duanJuPackage.getModel()), "model", duanJuPackage.getModel());
        queryWrapper.eq(Objects.nonNull(duanJuPackage.getType()), "type", duanJuPackage.getType());
        queryWrapper.like(StringUtils.isNotEmpty(name), "name", name);
        queryWrapper.eq(Objects.nonNull(duanJuPackage.getPayType()), "pay_type", duanJuPackage.getPayType());
        IPage<DuanJuPackage> pageList = duanJuPackageService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param duanJuPackage
     * @return
     */
    @ApiOperation(value = "duan_ju_package-添加", notes = "duan_ju_package-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DuanJuPackage duanJuPackage) {
        duanJuPackageService.save(duanJuPackage);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param duanJuPackage
     * @return
     */
    @ApiOperation(value = "duan_ju_package-编辑", notes = "duan_ju_package-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DuanJuPackage duanJuPackage) {
        duanJuPackageService.updateByIdWithNull(duanJuPackage);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "duan_ju_package-通过id删除", notes = "duan_ju_package-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("参数id不能为空");
        }
        duanJuPackageService.lambdaUpdate().set(DuanJuPackage::getIsDeleted, 1).eq(DuanJuPackage::getId, id).update();
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "duan_ju_package-批量删除", notes = "duan_ju_package-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        if (CollectionUtil.isEmpty(idList)) {
            return Result.error("参数id不能为空");
        }
        duanJuPackageService.lambdaUpdate().set(DuanJuPackage::getIsDeleted, 1).eq(DuanJuPackage::getId, idList).update();
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "duan_ju_package-通过id查询", notes = "duan_ju_package-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DuanJuPackage duanJuPackage = duanJuPackageService.getById(id);
        if (duanJuPackage == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(duanJuPackage);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param duanJuPackage
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DuanJuPackage duanJuPackage) {
        return super.exportXls(request, duanJuPackage, DuanJuPackage.class, "duan_ju_package");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DuanJuPackage.class);
    }

}
