package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.MiniAppCouponRecord;

/**
 * @Description: mini_app_coupon_record
 * @Author: jeecg-boot
 * @Date: 2025-07-03
 * @Version: V1.0
 */
public interface IMiniAppCouponRecordService extends IService<MiniAppCouponRecord> {

    boolean add(String json);

    MiniAppCouponRecord getByCouponId(String couponId);

    void consumeCoupon(String couponId, Integer userId, String douYinEpisodeId);

    void rollbackConsumeCoupon(String couponId, String openId);
}
