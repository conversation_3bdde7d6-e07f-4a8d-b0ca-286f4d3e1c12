package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: mini_app_pay_template
 * @Author: jeecg-boot
 * @Date: 2025-07-28
 * @Version: V1.0
 */
@Data
@TableName("mini_app_pay_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_pay_template对象", description = "mini_app_pay_template")
public class MiniAppPayTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 手机系统 ios,安卓
     */
    @Excel(name = "手机系统 ios,安卓", width = 15)
    @ApiModelProperty(value = "手机系统 ios,安卓")
    private String phoneSystem;
    /**
     * 模板名称
     */
    @Excel(name = "模板名称", width = 15)
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    /**
     * 默认状态 1:默认 0:非默认
     */
    @Excel(name = "默认状态 1:默认 0:非默认", width = 15)
    @ApiModelProperty(value = "默认状态 1:默认 0:非默认")
    private Integer defaultStatus;
    /**
     * 上下架状态  1:上架 0:下架
     */
    @Excel(name = "上下架状态  1:上架 0:下架", width = 15)
    @ApiModelProperty(value = "上下架状态  1:上架 0:下架")
    private Integer status;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 保存入参 便于复制
     */
    @Excel(name = "保存入参 便于复制", width = 15)
    @ApiModelProperty(value = "保存入参 便于复制")
    private String stashData;
    /**
     * 创建时间
     */
    @Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改时间
     */
    @Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 删除标识 1:已删除 0未删除
     */
    @Excel(name = "删除标识 1:已删除 0未删除", width = 15)
    @ApiModelProperty(value = "删除标识 1:已删除 0未删除")
    private Integer isDeleted;


    @TableField(exist = false)
    @ApiModelProperty(value = "套餐列表")
    private List<DuanJuPackage> duanJuPackageList;
}
