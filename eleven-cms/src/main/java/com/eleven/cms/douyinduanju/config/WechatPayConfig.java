package com.eleven.cms.douyinduanju.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wechat.pay")
public class WechatPayConfig {

    /**
     * 小程序AppID
     */
    private String appId;

    /**
     * 小程序AppSecret
     */
    private String appSecret;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 证书路径
     */
    private String certPath;

    /**
     * 私钥路径
     */
    private String keyPath;

    /**
     * 签约回调通知地址
     */
    private String notifyUrl;

    /**
     * 签约成功跳转地址
     */
    private String returnUrl;

    /**
     * 默认模板ID
     */
    private String defaultPlanId;

    /**
     * 签约显示账户名称
     */
    private String contractDisplayAccount = "短剧会员服务";

    /**
     * 签约显示名称
     */
    private String contractDisplayName = "短剧会员自动续费";

    /**
     * 是否启用签约功能
     */
    private boolean enabled = true;

    /**
     * 签约环境：sandbox-沙箱环境，production-生产环境
     */
    private String environment = "sandbox";

    /**
     * 获取签约API地址
     */
    public String getSigningApiUrl() {
        if ("production".equals(environment)) {
            return "https://api.mch.weixin.qq.com/papay/entrustweb";
        } else {
            return "https://api.mch.weixin.qq.com/sandboxnew/papay/entrustweb";
        }
    }

    /**
     * 获取查询签约API地址
     */
    public String getQueryApiUrl() {
        if ("production".equals(environment)) {
            return "https://api.mch.weixin.qq.com/papay/querycontract";
        } else {
            return "https://api.mch.weixin.qq.com/sandboxnew/papay/querycontract";
        }
    }

    /**
     * 获取解约API地址
     */
    public String getUnsignApiUrl() {
        if ("production".equals(environment)) {
            return "https://api.mch.weixin.qq.com/papay/deletecontract";
        } else {
            return "https://api.mch.weixin.qq.com/sandboxnew/papay/deletecontract";
        }
    }
}
