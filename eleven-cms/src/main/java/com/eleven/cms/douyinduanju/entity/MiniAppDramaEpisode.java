package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: mini_app_drama_episode
 * @Author: jeecg-boot
 * @Date: 2025-06-06
 * @Version: V1.0
 */
@Data
@TableName("mini_app_drama_episode")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_drama_episode对象", description = "mini_app_drama_episode")
public class MiniAppDramaEpisode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 短剧信息主键
     */
    @Excel(name = "短剧信息主键", width = 15)
    @ApiModelProperty(value = "短剧信息主键")
    private String miniDramaId;
    /**
     * 剧集id
     */
    @Excel(name = "剧集id", width = 15)
    @ApiModelProperty(value = "剧集id")
    private String douYinEpisodeId;
    private String douYinVideoUrl;
    private Date douYinVideoUrlExpire;

    private String dramaTitle;
    private String episodeTitle;
    /**
     * 抖音云剧目id
     */
    private String albumId;
    /**
     * 视频上传记录主键
     */
    @Excel(name = "视频上传记录主键", width = 15)
    @ApiModelProperty(value = "视频上传记录主键")
    private String videoRecordId;
    /**
     * 是否付费 0:免费  1:付费
     */
    @Excel(name = "是否付费 0:免费  1:付费", width = 15)
    @ApiModelProperty(value = "是否付费 0:免费  1:付费")
    private Integer payFlag;
    /**
     * 生效状态 0 未生效 1 生效
     */
    @Excel(name = "生效状态 0 未生效 1 生效", width = 15)
    @ApiModelProperty(value = "生效状态 0 未生效 1 生效")
    private Integer validStatus;
    /**
     * 封面id
     */
    @Excel(name = "封面id", width = 15)
    @ApiModelProperty(value = "封面id")
    private String coverId;
    /**
     * 封面图
     */
    @Excel(name = "封面图", width = 15)
    @ApiModelProperty(value = "封面图")
    private String coverUrl;

    /**
     * 剧集顺序
     */
    private Integer episodeSeq;

    private Integer isDeleted;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

    private Integer auditStatus;

    private String auditMsg;
}
