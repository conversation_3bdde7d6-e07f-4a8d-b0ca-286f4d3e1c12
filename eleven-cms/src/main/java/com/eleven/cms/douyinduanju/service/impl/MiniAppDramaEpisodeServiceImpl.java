package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.EpisodeReq;
import com.eleven.cms.douyinduanju.dto.EpisodeVO;
import com.eleven.cms.douyinduanju.dto.MiniAppDramaEpisodeResponse;
import com.eleven.cms.douyinduanju.entity.*;
import com.eleven.cms.douyinduanju.mapper.MiniAppDramaEpisodeMapper;
import com.eleven.cms.douyinduanju.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class MiniAppDramaEpisodeServiceImpl extends ServiceImpl<MiniAppDramaEpisodeMapper, MiniAppDramaEpisode> implements IMiniAppDramaEpisodeService {

    @Override
    public IPage<EpisodeVO> pageEpisode(EpisodeReq req, IPage<EpisodeVO> iPage) {

        return this.baseMapper.pageEpisode(iPage, req);
    }


    @Override
    public MiniAppDramaEpisode getByVideoRecordId(String videoRecordId) {
        return lambdaQuery().eq(MiniAppDramaEpisode::getVideoRecordId, videoRecordId).last("limit 1").one();
    }

    @Resource
    private IOpenApiUploadVideoRecordService videoRecordService;

    @Resource
    private IMiniAppMiniDramaService miniAppMiniDramaService;

    @Resource
    private IOpenApiAlbumInfoService albumInfoService;

    @Resource
    private IMiniAppMiniDramaFavoriteService miniAppMiniDramaFavoriteService;

    @Override
    public void syncOpenApiUploadVideoRecord(String videoRecordId) {
        MiniAppDramaEpisode episode = new MiniAppDramaEpisode();

        OpenApiUploadVideoRecord videoRecord = videoRecordService.getById(videoRecordId);
        if (Objects.isNull(videoRecord)) {
            return;
        }
        if (videoRecord.getStatus() != 4) {
            return;
        }
        if (StringUtils.isEmpty(videoRecord.getAlbumId())) {
            return;
        }
        MiniAppMiniDrama drama = miniAppMiniDramaService.getByAlbumId(videoRecord.getAlbumId());
        if (Objects.nonNull(drama)) {
            episode.setMiniDramaId(drama.getId());
            episode.setDramaTitle(drama.getName());
            episode.setAlbumId(drama.getAlbumId());
        }
        OpenApiAlbumInfo albumInfo = albumInfoService.getByAlbumId(videoRecord.getAlbumId());

        episode.setCoverUrl(drama.getCoverUrl());
        episode.setCoverId(albumInfo.getCoverList());
        episode.setDouYinEpisodeId(videoRecord.getEpisodeId());
        episode.setEpisodeSeq(videoRecord.getSeq());
        episode.setVideoRecordId(videoRecordId);
        episode.setEpisodeTitle(videoRecord.getTitle());
        save(episode);
    }

    @Override
    public List<String> getByVideoRecordId() {
        return this.baseMapper.getByVideoRecordId();

    }

    @Override
    public List<MiniAppDramaEpisodeResponse> getByAlbumId(String albumId) {
        List<MiniAppDramaEpisode> episodes = lambdaQuery()
                .eq(MiniAppDramaEpisode::getAlbumId, albumId)
                .eq(MiniAppDramaEpisode::getIsDeleted, 0)
                .orderByAsc(MiniAppDramaEpisode::getEpisodeSeq)
                .list();

        return episodes.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<MiniAppDramaEpisodeResponse> getByAlbumIdWithFavorite(String albumId, Integer userId) {
        List<MiniAppDramaEpisode> episodes = lambdaQuery()
                .eq(MiniAppDramaEpisode::getAlbumId, albumId)
                .eq(MiniAppDramaEpisode::getIsDeleted, 0)
                .orderByAsc(MiniAppDramaEpisode::getEpisodeSeq)
                .list();

        return episodes.stream()
                .map(episode -> convertToResponseWithFavorite(episode, userId))
                .collect(Collectors.toList());
    }

    /**
     * 转换MiniAppDramaEpisode为MiniAppDramaEpisodeResponse
     */
    private MiniAppDramaEpisodeResponse convertToResponse(MiniAppDramaEpisode episode) {
        MiniAppDramaEpisodeResponse response = new MiniAppDramaEpisodeResponse();
        BeanUtils.copyProperties(episode, response);

        // 设置权限相关信息
        response.setHasPermissionView(true); // 默认有权限，实际应根据用户权限判断
        response.setPermissionType(episode.getPayFlag() == 0 ? 1 : 4); // 免费或付费
        response.setIsFavorited(false); // 默认未收藏

        return response;
    }

    /**
     * 转换MiniAppDramaEpisode为MiniAppDramaEpisodeResponse（带收藏状态）
     */
    private MiniAppDramaEpisodeResponse convertToResponseWithFavorite(MiniAppDramaEpisode episode, Integer userId) {
        MiniAppDramaEpisodeResponse response = convertToResponse(episode);

        if (userId != null) {
            // 检查收藏状态
            MiniAppMiniDramaFavorite favorite = miniAppMiniDramaFavoriteService
                    .getFavoriteByUserIdAndDramaId(userId, episode.getMiniDramaId());

            if (favorite != null && favorite.getStatus() == 1) {
                response.setIsFavorited(true);
                response.setFavoriteId(favorite.getId());
                response.setFavoriteTime(favorite.getFavoriteTime());
            } else {
                response.setIsFavorited(false);
            }
        }

        return response;
    }
}
