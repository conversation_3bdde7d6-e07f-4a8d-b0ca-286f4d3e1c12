package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.constant.DuanjuConstant;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaColumn;
import com.eleven.cms.douyinduanju.mapper.MiniAppDramaColumnMapper;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaColumnService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class MiniAppDramaColumnServiceImpl extends ServiceImpl<MiniAppDramaColumnMapper, MiniAppDramaColumn> implements IMiniAppDramaColumnService {

    @Cacheable(cacheNames = DuanjuConstant.COLUMN_CACHE, key = "#root.methodName +':'+ #id")
    @Override
    public List<MiniAppDramaColumn> listColumnById(String id) {
        return lambdaQuery().eq(MiniAppDramaColumn::getStatus, 1)
                .eq(StringUtils.isNotEmpty(id), MiniAppDramaColumn::getId, id)
                .eq(MiniAppDramaColumn::getIsDeleted, 0)
                .orderByAsc(MiniAppDramaColumn::getOrderNo)
                .list();
    }
}
