package com.eleven.cms.douyinduanju.aspect;

import com.eleven.cms.douyinduanju.annotation.TokenRequired;
import com.eleven.cms.douyinduanju.entity.DuanJuUser;
import com.eleven.cms.douyinduanju.service.ITokenService;
import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * Token校验切面
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Aspect
@Component
@Order(1) // 设置切面执行顺序，数字越小优先级越高
public class TokenValidationAspect {

    @Resource
    private ITokenService tokenService;

    // Token请求头名称
    private static final String TOKEN_HEADER = "Authorization";
    private static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 定义切点：标注了@TokenRequired注解的方法或类
     */
    @Pointcut("@annotation(com.eleven.cms.douyinduanju.annotation.TokenRequired) || " +
            "@within(com.eleven.cms.douyinduanju.annotation.TokenRequired)")
    public void tokenRequiredPointcut() {
    }

    /**
     * 环绕通知：在方法执行前进行token校验
     */
    @Around("tokenRequiredPointcut()")
    public Object validateToken(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取注解信息
        TokenRequired tokenRequired = getTokenRequiredAnnotation(method);

        if (tokenRequired == null || !tokenRequired.required()) {
            // 不需要校验token，直接执行方法
            return joinPoint.proceed();
        }

        // 获取HTTP请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new BusinessException("无法获取HTTP请求上下文");
        }

        HttpServletRequest request = attributes.getRequest();

        // 从请求头获取token
        String token = extractTokenFromRequest(request);

        if (StringUtils.isEmpty(token)) {
            log.warn("Token为空，请求路径: {}", request.getRequestURI());
            throw new BusinessException(tokenRequired.message() + ": Token不能为空");
        }

        // 校验token
        String tokenType = tokenRequired.tokenType();
        boolean isValid = tokenService.validateToken(token, tokenType);

        if (!isValid) {
            log.warn("Token校验失败，token: {}, tokenType: {}, 请求路径: {}",
                    token, tokenType, request.getRequestURI());
            throw new BusinessException(tokenRequired.message() + ": Token无效或已过期");
        }

        // 获取用户信息并设置到请求属性中，供后续使用
        DuanJuUser user = tokenService.getUserFromToken(token, tokenType);
        if (user == null) {
            log.warn("无法从token获取用户信息，token: {}, tokenType: {}", token, tokenType);
            throw new BusinessException(tokenRequired.message() + ": 用户信息无效");
        }

        // 将用户信息设置到请求属性中
        request.setAttribute("currentUser", user);
        request.setAttribute("currentToken", token);

        log.debug("Token校验成功，用户ID: {}, 请求路径: {}", user.getId(), request.getRequestURI());

        // 执行目标方法
        return joinPoint.proceed();
    }

    /**
     * 获取TokenRequired注解
     */
    private TokenRequired getTokenRequiredAnnotation(Method method) {
        // 先从方法上获取注解
        TokenRequired annotation = method.getAnnotation(TokenRequired.class);
        if (annotation != null) {
            return annotation;
        }

        // 如果方法上没有，从类上获取注解
        return method.getDeclaringClass().getAnnotation(TokenRequired.class);
    }

    /**
     * 从请求中提取token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 1. 从Authorization请求头获取
        String authHeader = request.getHeader(TOKEN_HEADER);
        if (StringUtils.isNotEmpty(authHeader)) {
            return authHeader;
        }

        // 2. 从X-Token请求头获取
        String xToken = request.getHeader("X-Token");
        if (StringUtils.isNotEmpty(xToken)) {
            return xToken;
        }

        // 3. 从请求参数获取
        String paramToken = request.getParameter("token");
        if (StringUtils.isNotEmpty(paramToken)) {
            return paramToken;
        }

        return null;
    }
}
