package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.dto.AlbumInfo;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;


public interface IOpenApiAlbumInfoService extends IService<OpenApiAlbumInfo> {


    AlbumInfo getAlbumInfoByAlbumInfoId(String id);

    AlbumInfo getAlbumInfoByAlbumId(String id);

    void updateReviewStatusByAlbumId(String albumId, Integer version);

    void updateAuthorizeStatusByAlbumId(String albumId);

    void updateOnlineStatusByAlbumId(String albumId, Integer onlineStatus);

    OpenApiAlbumInfo getByAlbumId(String albumId);

    /**
     * 更新短剧专辑信息（包括空值字段）
     *
     * @param openApiAlbumInfo 短剧专辑对象
     * @return 更新行数
     */
    int updateByIdWithNull(OpenApiAlbumInfo openApiAlbumInfo);
}
