package com.eleven.cms.douyinduanju.controller;

import com.eleven.cms.douyinduanju.service.INasFileRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * NAS文件记录控制器
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Api(tags = "NAS文件记录管理")
@RestController
@RequestMapping("/api/nas/record")
@Slf4j
public class NasFileRecordController {

    @Autowired
    private INasFileRecordService nasFileRecordService;

    /**
     * 扫描并保存NAS目录
     */
    @ApiOperation(value = "扫描并保存NAS目录", notes = "扫描指定NAS目录并将文件信息保存到数据库")
    @PostMapping(value = "/scan")
    public Result<?> scanNasDirectory(@RequestBody ScanRequest request) {
        try {
            log.info("收到NAS目录扫描请求: {}", request);

            if (request == null) {
                return Result.error("请求参数不能为空");
            }

            String nasPath = request.getNasPath();
            if (nasPath != null && nasPath.trim().isEmpty()) {
                nasPath = null; // 使用默认路径
            }

            INasFileRecordService.NasScanResult result = nasFileRecordService.scanAndSaveNasDirectory(nasPath, request.getIncludeParentName());

            if (result.isSuccess()) {
                return Result.ok(result);
            } else {
                return Result.error(result.getMessage());
            }

        } catch (Exception e) {
            log.error("扫描NAS目录异常", e);
            return Result.error("扫描NAS目录失败: " + e.getMessage());
        }
    }

    /**
     * 根据扫描批次删除记录
     */
    @AutoLog(value = "根据扫描批次删除记录")
    @ApiOperation(value = "根据扫描批次删除记录", notes = "删除指定扫描批次的所有文件记录")
    @DeleteMapping(value = "/batch/{scanBatch}")
    public Result<?> deleteByScanBatch(@ApiParam("扫描批次号") @PathVariable String scanBatch) {
        try {
            log.info("删除扫描批次记录，批次: {}", scanBatch);

            if (scanBatch == null || scanBatch.trim().isEmpty()) {
                return Result.error("扫描批次号不能为空");
            }

            int deleteCount = nasFileRecordService.deleteByScanBatch(scanBatch);

            return Result.ok("删除成功，删除记录数: " + deleteCount);

        } catch (Exception e) {
            log.error("删除扫描批次记录异常，批次: {}", scanBatch, e);
            return Result.error("删除记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据路径前缀删除记录
     */
    @AutoLog(value = "根据路径前缀删除记录")
    @ApiOperation(value = "根据路径前缀删除记录", notes = "删除指定路径前缀的所有文件记录")
    @DeleteMapping(value = "/path")
    public Result<?> deleteByPathPrefix(@RequestBody DeleteByPathRequest request) {
        try {
            log.info("删除路径前缀记录，请求: {}", request);

            if (request == null || request.getPathPrefix() == null || request.getPathPrefix().trim().isEmpty()) {
                return Result.error("路径前缀不能为空");
            }

            int deleteCount = nasFileRecordService.deleteByPathPrefix(request.getPathPrefix());

            return Result.ok("删除成功，删除记录数: " + deleteCount);

        } catch (Exception e) {
            log.error("删除路径前缀记录异常，前缀: {}", request != null ? request.getPathPrefix() : null, e);
            return Result.error("删除记录失败: " + e.getMessage());
        }
    }

    /**
     * 扫描请求参数
     */
    @Data
    public static class ScanRequest {

        @ApiParam("NAS目录路径")
        private String nasPath;

        @ApiParam("包含目录名")
        private List<String> includeParentName;

        @Override
        public String toString() {
            return "ScanRequest{" +
                    "nasPath='" + nasPath + '\'' +
                    '}';
        }
    }

    /**
     * 根据路径删除请求参数
     */
    public static class DeleteByPathRequest {

        @ApiParam("路径前缀")
        private String pathPrefix;

        public String getPathPrefix() {
            return pathPrefix;
        }

        public void setPathPrefix(String pathPrefix) {
            this.pathPrefix = pathPrefix;
        }

        @Override
        public String toString() {
            return "DeleteByPathRequest{" +
                    "pathPrefix='" + pathPrefix + '\'' +
                    '}';
        }
    }
}
