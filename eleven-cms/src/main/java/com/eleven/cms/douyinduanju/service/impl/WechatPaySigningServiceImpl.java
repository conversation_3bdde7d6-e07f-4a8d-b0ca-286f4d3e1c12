package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.WechatPaySigningDTO;
import com.eleven.cms.douyinduanju.dto.WechatPaySigningResponse;
import com.eleven.cms.douyinduanju.entity.WechatPaySigning;
import com.eleven.cms.douyinduanju.mapper.WechatPaySigningMapper;
import com.eleven.cms.douyinduanju.service.WechatPaySigningService;
import com.eleven.cms.douyinduanju.util.WechatPaySigningUtil;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.service.IDouyinAppConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 微信支付小程序签约服务实现
 */
@Service
@Slf4j
public class WechatPaySigningServiceImpl extends ServiceImpl<WechatPaySigningMapper, WechatPaySigning> 
        implements WechatPaySigningService {

    @Autowired
    private WechatPaySigningUtil wechatPaySigningUtil;

    @Autowired
    private IDouyinAppConfigService douyinAppConfigService;

    /**
     * 创建签约请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSigningRequest(String userId, String openid, String planId, String notifyUrl, String returnWeb) {
        try {
            log.info("开始创建微信支付签约请求，用户ID: {}, openid: {}", userId, openid);

            // 1. 获取微信支付配置
            DouyinAppConfig appConfig = douyinAppConfigService.getById("wechat_pay_config");
            if (appConfig == null) {
                throw new RuntimeException("微信支付配置不存在");
            }

            // 2. 检查用户是否已经签约
            WechatPaySigning existingSigning = lambdaQuery()
                    .eq(WechatPaySigning::getUserId, userId)
                    .eq(WechatPaySigning::getSigningStatus, 1) // 已签约
                    .eq(WechatPaySigning::getIsDeleted, 0)
                    .one();

            if (existingSigning != null) {
                log.warn("用户{}已经签约，无需重复签约", userId);
                throw new RuntimeException("用户已经签约，无需重复签约");
            }

            // 3. 构建签约请求参数
            WechatPaySigningDTO signingDTO = new WechatPaySigningDTO();
            signingDTO.setAppid(appConfig.getAppId());
            signingDTO.setMchid(appConfig.getMchId());
            signingDTO.setPlanId(planId);
            signingDTO.setContractCode(wechatPaySigningUtil.generateContractCode(userId));
            signingDTO.setOutContractCode(wechatPaySigningUtil.generateOutContractCode(userId));
            signingDTO.setOpenid(openid);
            signingDTO.setContractDisplayAccount("短剧会员服务");
            signingDTO.setContractDisplayName("短剧会员自动续费");
            signingDTO.setNotifyUrl(notifyUrl);
            signingDTO.setReturnWeb(returnWeb);

            // 4. 生成签约URL
            String signingUrl = wechatPaySigningUtil.createSigningRequest(signingDTO, appConfig.getApiKey());

            // 5. 保存签约记录
            WechatPaySigning signing = new WechatPaySigning();
            signing.setUserId(userId);
            signing.setOpenid(openid);
            signing.setAppid(signingDTO.getAppid());
            signing.setMchid(signingDTO.getMchid());
            signing.setPlanId(planId);
            signing.setContractCode(signingDTO.getContractCode());
            signing.setOutContractCode(signingDTO.getOutContractCode());
            signing.setSigningStatus(0); // 未签约
            signing.setContractDisplayAccount(signingDTO.getContractDisplayAccount());
            signing.setContractDisplayName(signingDTO.getContractDisplayName());
            signing.setNotifyUrl(notifyUrl);
            signing.setReturnWeb(returnWeb);
            signing.setSigningUrl(signingUrl);
            signing.setCreateTime(new Date());
            signing.setUpdateTime(new Date());
            signing.setIsDeleted(0);

            save(signing);

            log.info("微信支付签约请求创建成功，用户ID: {}, 签约协议号: {}", userId, signingDTO.getContractCode());
            return signingUrl;

        } catch (Exception e) {
            log.error("创建微信支付签约请求失败，用户ID: {}", userId, e);
            throw new RuntimeException("创建签约请求失败: " + e.getMessage());
        }
    }

    /**
     * 处理签约回调
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleSigningCallback(Map<String, String> callbackParams) {
        try {
            log.info("开始处理微信支付签约回调，参数: {}", callbackParams);

            // 1. 获取微信支付配置
            DouyinAppConfig appConfig = douyinAppConfigService.getById("wechat_pay_config");
            if (appConfig == null) {
                log.error("微信支付配置不存在");
                return false;
            }

            // 2. 验证签名
            boolean signValid = wechatPaySigningUtil.verifyCallbackSign(callbackParams, appConfig.getApiKey());
            if (!signValid) {
                log.error("微信支付签约回调签名验证失败");
                return false;
            }

            // 3. 解析回调响应
            WechatPaySigningResponse response = wechatPaySigningUtil.parseCallbackResponse(callbackParams);

            // 4. 查找签约记录
            WechatPaySigning signing = lambdaQuery()
                    .eq(WechatPaySigning::getContractCode, response.getContractCode())
                    .eq(WechatPaySigning::getIsDeleted, 0)
                    .one();

            if (signing == null) {
                log.error("未找到对应的签约记录，签约协议号: {}", response.getContractCode());
                return false;
            }

            // 5. 更新签约状态
            signing.setContractId(response.getContractId());
            signing.setUpdateTime(new Date());

            if (response.isSuccess() && response.isSigned()) {
                // 签约成功
                signing.setSigningStatus(1);
                signing.setContractSignTime(parseDateTime(response.getContractSignTime()));
                signing.setContractExpiredTime(parseDateTime(response.getContractExpiredTime()));
                signing.setContractBankType(response.getContractBankType());
                log.info("用户签约成功，用户ID: {}, 签约ID: {}", signing.getUserId(), response.getContractId());
            } else {
                // 签约失败
                signing.setSigningStatus(3);
                signing.setErrCode(response.getErrCode());
                signing.setErrCodeDes(response.getErrCodeDes());
                log.warn("用户签约失败，用户ID: {}, 错误代码: {}, 错误描述: {}", 
                        signing.getUserId(), response.getErrCode(), response.getErrCodeDes());
            }

            updateById(signing);

            log.info("微信支付签约回调处理完成，用户ID: {}, 签约状态: {}", signing.getUserId(), signing.getSigningStatus());
            return true;

        } catch (Exception e) {
            log.error("处理微信支付签约回调失败", e);
            return false;
        }
    }

    /**
     * 查询签约状态
     */
    @Override
    public WechatPaySigningResponse querySigningStatus(String userId, String contractCode) {
        try {
            log.info("查询用户签约状态，用户ID: {}, 签约协议号: {}", userId, contractCode);

            WechatPaySigning signing = lambdaQuery()
                    .eq(WechatPaySigning::getUserId, userId)
                    .eq(WechatPaySigning::getContractCode, contractCode)
                    .eq(WechatPaySigning::getIsDeleted, 0)
                    .one();

            if (signing == null) {
                log.warn("未找到用户签约记录，用户ID: {}", userId);
                return null;
            }

            // 构建响应对象
            WechatPaySigningResponse response = new WechatPaySigningResponse();
            response.setAppid(signing.getAppid());
            response.setMchid(signing.getMchid());
            response.setPlanId(signing.getPlanId());
            response.setContractCode(signing.getContractCode());
            response.setContractId(signing.getContractId());
            response.setOutContractCode(signing.getOutContractCode());
            response.setOpenid(signing.getOpenid());
            response.setContractDisplayAccount(signing.getContractDisplayAccount());
            response.setContractSignTime(formatDateTime(signing.getContractSignTime()));
            response.setContractExpiredTime(formatDateTime(signing.getContractExpiredTime()));
            response.setContractBankType(signing.getContractBankType());

            // 设置签约状态
            switch (signing.getSigningStatus()) {
                case 1:
                    response.setContractState("ADD");
                    response.setReturnCode("SUCCESS");
                    response.setResultCode("SUCCESS");
                    break;
                case 2:
                    response.setContractState("DELETE");
                    response.setReturnCode("SUCCESS");
                    response.setResultCode("SUCCESS");
                    break;
                case 3:
                    response.setReturnCode("FAIL");
                    response.setResultCode("FAIL");
                    response.setErrCode(signing.getErrCode());
                    response.setErrCodeDes(signing.getErrCodeDes());
                    break;
                default:
                    response.setContractState("PENDING");
                    response.setReturnCode("SUCCESS");
                    response.setResultCode("SUCCESS");
                    break;
            }

            return response;

        } catch (Exception e) {
            log.error("查询用户签约状态失败，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 解约
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelSigning(String userId, String contractCode) {
        try {
            log.info("开始解约，用户ID: {}, 签约协议号: {}", userId, contractCode);

            WechatPaySigning signing = lambdaQuery()
                    .eq(WechatPaySigning::getUserId, userId)
                    .eq(WechatPaySigning::getContractCode, contractCode)
                    .eq(WechatPaySigning::getSigningStatus, 1) // 已签约
                    .eq(WechatPaySigning::getIsDeleted, 0)
                    .one();

            if (signing == null) {
                log.warn("未找到有效的签约记录，用户ID: {}", userId);
                return false;
            }

            // 更新签约状态为已解约
            signing.setSigningStatus(2);
            signing.setUpdateTime(new Date());
            updateById(signing);

            log.info("解约成功，用户ID: {}, 签约协议号: {}", userId, contractCode);
            return true;

        } catch (Exception e) {
            log.error("解约失败，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 检查用户是否已签约
     */
    @Override
    public boolean isUserSigned(String userId) {
        try {
            WechatPaySigning signing = lambdaQuery()
                    .eq(WechatPaySigning::getUserId, userId)
                    .eq(WechatPaySigning::getSigningStatus, 1) // 已签约
                    .eq(WechatPaySigning::getIsDeleted, 0)
                    .one();

            return signing != null;

        } catch (Exception e) {
            log.error("检查用户签约状态失败，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 获取用户签约信息
     */
    @Override
    public WechatPaySigningResponse getUserSigningInfo(String userId) {
        try {
            WechatPaySigning signing = lambdaQuery()
                    .eq(WechatPaySigning::getUserId, userId)
                    .eq(WechatPaySigning::getSigningStatus, 1) // 已签约
                    .eq(WechatPaySigning::getIsDeleted, 0)
                    .orderByDesc(WechatPaySigning::getCreateTime)
                    .last("LIMIT 1")
                    .one();

            if (signing == null) {
                return null;
            }

            return querySigningStatus(userId, signing.getContractCode());

        } catch (Exception e) {
            log.error("获取用户签约信息失败，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 解析日期时间字符串
     */
    private Date parseDateTime(String dateTimeStr) {
        if (!StringUtils.hasText(dateTimeStr)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            return sdf.parse(dateTimeStr);
        } catch (ParseException e) {
            log.warn("解析日期时间失败: {}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(Date dateTime) {
        if (dateTime == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(dateTime);
    }
}
