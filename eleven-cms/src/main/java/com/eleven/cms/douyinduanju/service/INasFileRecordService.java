package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.NasFileRecord;

import java.util.List;

/**
 * NAS文件记录服务接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface INasFileRecordService extends IService<NasFileRecord> {

    /**
     * 扫描并保存NAS目录
     *
     * @param nasPath NAS目录路径
     * @return 扫描结果
     */
    NasScanResult scanAndSaveNasDirectory(String nasPath, List<String> includeParentName);

    /**
     * 批量保存文件记录
     *
     * @param records 文件记录列表
     * @return 保存结果
     */
    boolean batchSaveRecords(List<NasFileRecord> records);

    /**
     * 根据扫描批次删除记录
     *
     * @param scanBatch 扫描批次号
     * @return 删除数量
     */
    int deleteByScanBatch(String scanBatch);

    /**
     * 根据路径前缀删除记录
     *
     * @param pathPrefix 路径前缀
     * @return 删除数量
     */
    int deleteByPathPrefix(String pathPrefix);

    /**
     * NAS扫描结果
     */
    class NasScanResult {
        private boolean success;
        private String scanBatch;
        private int totalCount;
        private int directoryCount;
        private int fileCount;
        private long totalSize;
        private String message;
        private long scanDuration;

        public NasScanResult() {
        }

        public NasScanResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public static NasScanResult success(String scanBatch, int totalCount, int directoryCount, int fileCount, long totalSize, long scanDuration) {
            NasScanResult result = new NasScanResult();
            result.setSuccess(true);
            result.setScanBatch(scanBatch);
            result.setTotalCount(totalCount);
            result.setDirectoryCount(directoryCount);
            result.setFileCount(fileCount);
            result.setTotalSize(totalSize);
            result.setScanDuration(scanDuration);
            result.setMessage("扫描完成");
            return result;
        }

        public static NasScanResult error(String message) {
            return new NasScanResult(false, message);
        }

        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getScanBatch() {
            return scanBatch;
        }

        public void setScanBatch(String scanBatch) {
            this.scanBatch = scanBatch;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getDirectoryCount() {
            return directoryCount;
        }

        public void setDirectoryCount(int directoryCount) {
            this.directoryCount = directoryCount;
        }

        public int getFileCount() {
            return fileCount;
        }

        public void setFileCount(int fileCount) {
            this.fileCount = fileCount;
        }

        public long getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(long totalSize) {
            this.totalSize = totalSize;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public long getScanDuration() {
            return scanDuration;
        }

        public void setScanDuration(long scanDuration) {
            this.scanDuration = scanDuration;
        }

        @Override
        public String toString() {
            return "NasScanResult{" +
                    "success=" + success +
                    ", scanBatch='" + scanBatch + '\'' +
                    ", totalCount=" + totalCount +
                    ", directoryCount=" + directoryCount +
                    ", fileCount=" + fileCount +
                    ", totalSize=" + totalSize +
                    ", message='" + message + '\'' +
                    ", scanDuration=" + scanDuration +
                    '}';
        }
    }
}
