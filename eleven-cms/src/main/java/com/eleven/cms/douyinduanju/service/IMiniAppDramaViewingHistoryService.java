package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.dto.LookHistoryVO;
import com.eleven.cms.douyinduanju.dto.ViewingHistoryReq;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaViewingHistory;

import java.util.List;


public interface IMiniAppDramaViewingHistoryService extends IService<MiniAppDramaViewingHistory> {

    String saveViewingHistoryByAlbumId(String albumId);

    MiniAppDramaViewingHistory addOrUpdateViewingHistory(ViewingHistoryReq req);

    List<LookHistoryVO> getLastHistory(Page<LookHistoryVO> page, Integer userId);

    List<LookHistoryVO> getMaxSeqHistory(Integer userId);

    List<LookHistoryVO> getFinishDrama(Integer userId);

    /**
     * 获取用户每个剧目的最新观看历史记录
     *
     * @param userId 用户ID
     * @return 每个剧目的最新观看历史记录列表
     */
    List<LookHistoryVO> getLatestHistoryForEachDrama(Integer userId);

}
