package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

import java.util.Date;

@Data
public class EpisodeVO {

    private String id;


    /**
     * 抖音云剧集id
     */
    private String douYinEpisodeId;

    /**
     * 视频阿里云地址
     */
    private String resourceVideoUrl;

    private Date douYinVideoUrlExpire;

    private String douYinVideoUrl;

    /**
     * 抖音开放平台视频id
     */
    private String openVideoId;

    private String picUrl;

    /**
     * 抖音云的视频id
     */
    private String dyCloudId;

    /**
     * 视频标题
     */
    private String title;

    private String dramaTitle;
    private String episodeTitle;
    /**
     * 抖音云剧目id
     */
    private String albumId;
    private Integer episodeSeq;

    /**
     * 是否付费 0:免费  1:付费
     */
    private Integer payFlag;

    /**
     * 生效状态 0 未生效 1 生效
     */
    private Integer validStatus;

    /**
     * 封面id
     */
    private String coverId;
}
