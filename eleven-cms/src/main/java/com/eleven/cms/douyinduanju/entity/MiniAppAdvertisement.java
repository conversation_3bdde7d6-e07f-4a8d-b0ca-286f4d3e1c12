package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: mini_app_advertisement
 * @Author: jeecg-boot
 * @Date: 2025-07-09
 * @Version: V1.0
 */
@Data
@TableName("mini_app_advertisement")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_advertisement对象", description = "mini_app_advertisement")
public class MiniAppAdvertisement implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 回传参数 clickId
     */
    private String clickId;
    /**
     * 回传参数 广告id
     */
    private String promotionId;
    /**
     * 回传参数 项目id
     */
    private String projectId;
    /**
     * 平台, android,ios
     */
    private String platform = "android";
    /**
     * 设备id ios:idfa android: oaid
     */
    private String deviceId;


    private String creativeType;
    /**
     * 渠道号
     */
    private String channel;


    /**
     * 剧目id
     */
    private String albumId;
    /**
     * 剧集id
     */
    private String episodeId;

    /**
     * userId
     */
    @Excel(name = "userId", width = 15)
    @ApiModelProperty(value = "userId")
    private String userId;

    /**
     * 回传参数
     */
    @Excel(name = "回传参数", width = 15)
    @ApiModelProperty(value = "回传参数")
    private String param;


    /**
     * 来源
     */
    private String callbackSource;

    /**
     * 监测链接回调
     */
    private String miniAppCallback;


    /**
     * 购买金额
     */
    private String purchaseAmount;

    private String clientIp;

    private String userAgent;

    /**
     * 1:激活  2:订购  3:失败 4:成功
     */
    private String status;

    /**
     * 上报状态
     */
    private Integer reportStatus;

    @ApiModelProperty(value = "上报结果内容")
    private String content;
    private String panelId;

    private Date createTime;
    private Date updateTime;


}
