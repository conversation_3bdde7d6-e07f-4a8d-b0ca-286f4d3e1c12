package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.MiniAppPayTemplate;
import com.eleven.cms.douyinduanju.mapper.MiniAppPayTemplateMapper;
import com.eleven.cms.douyinduanju.service.IMiniAppPayTemplateService;
import org.springframework.stereotype.Service;

/**
 * @Description: mini_app_pay_template
 * @Author: jeecg-boot
 * @Date: 2025-07-28
 * @Version: V1.0
 */
@Service
public class MiniAppPayTemplateServiceImpl extends ServiceImpl<MiniAppPayTemplateMapper, MiniAppPayTemplate> implements IMiniAppPayTemplateService {

}
