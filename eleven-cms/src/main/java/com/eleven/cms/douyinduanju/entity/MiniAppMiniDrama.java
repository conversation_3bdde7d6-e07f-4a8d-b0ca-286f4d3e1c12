package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@TableName("mini_app_mini_drama")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_mini_drama对象", description = "mini_app_mini_drama")
public class MiniAppMiniDrama implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 栏目id
     */
    @Excel(name = "栏目id", width = 15)
    @ApiModelProperty(value = "栏目id")
    private String columnId;
    /**
     * 剧集名称
     */
    @ApiModelProperty(value = "剧集名称")
    private String name;
    /**
     * 状态 0未生效  1:已生效
     */
    @Excel(name = "状态 0未生效  1:已生效", width = 15)
    @ApiModelProperty(value = "状态 0未生效  1:已生效")
    private Integer status;

    /**
     * 短剧信息主键
     */
    @Excel(name = "短剧信息主键", width = 15)
    @ApiModelProperty(value = "短剧信息主键")
    private String albumInfoId;
    /**
     * 抖音开放平台短剧id
     */
    @Excel(name = "抖音开放平台短剧id", width = 15)
    @ApiModelProperty(value = "抖音开放平台短剧id")
    private String albumId;

    /**
     * 免费集数
     */
    @Excel(name = "免费集数", width = 15)
    @ApiModelProperty(value = "免费集数")
    private Integer freeNum;
    /**
     * 每集付费金额
     */
    private Integer perEpisodeCost;
    /**
     * 封面图
     */
    private String coverUrl;

    /**
     * 排序编码 正序
     */
    @Excel(name = "排序编码 正序", width = 15)
    @ApiModelProperty(value = "排序编码 正序")
    private Integer orderNo;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;

    /**
     * 播放量
     */
    private Long viewCount;

    private String icon;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

    @TableField(exist = false)
    private Integer seqCount;

    @TableField(exist = false)
    private String firstEpisodeId;

    @TableField(exist = false)
    private Boolean favoriteStatus;

    private String rating;

    private BigDecimal hotValue;
}
