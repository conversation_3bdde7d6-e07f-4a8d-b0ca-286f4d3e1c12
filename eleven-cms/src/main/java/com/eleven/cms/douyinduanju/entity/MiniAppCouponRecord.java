package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: mini_app_coupon_record
 * @Author: jeecg-boot
 * @Date: 2025-07-03
 * @Version: V1.0
 */
@Data
@TableName("mini_app_coupon_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_coupon_record对象", description = "mini_app_coupon_record")
public class MiniAppCouponRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 券ID，由抖音平台生成，全局唯一
     */
    @Excel(name = "券ID，由抖音平台生成，全局唯一", width = 15)
    @ApiModelProperty(value = "券ID，由抖音平台生成，全局唯一")
    private String couponId;
    private String douYinEpisodeId;
    /**
     * 小程序appid
     */
    @Excel(name = "小程序appid", width = 15)
    @ApiModelProperty(value = "小程序appid")
    private String appId;
    /**
     * 用户的小程序open id
     */
    @Excel(name = "用户的小程序open id", width = 15)
    @ApiModelProperty(value = "用户的小程序open id")
    private String openId;
    /**
     * 用户的union id
     */
    @Excel(name = "用户的union id", width = 15)
    @ApiModelProperty(value = "用户的union id")
    private String unionId;
    /**
     * 券状态：10-已领取，20-已使用，30-已过期，40-已撤销
     */
    @Excel(name = "券状态：10-已领取，20-已使用，30-已过期，40-已撤销", width = 15)
    @ApiModelProperty(value = "券状态：10-已领取，20-已使用，30-已过期，40-已撤销")
    private Integer couponStatus;
    /**
     * 领券时间，单位秒
     */
    @Excel(name = "领券时间，单位秒", width = 15)
    @ApiModelProperty(value = "领券时间，单位秒")
    private Date receiveTime;
    /**
     * 外部券模板编号
     */
    @Excel(name = "外部券模板编号", width = 15)
    @ApiModelProperty(value = "外部券模板编号")
    private String merchantMetaNo;
    /**
     * 券有效期开始时间，单位秒
     */
    @Excel(name = "券有效期开始时间，单位秒", width = 15)
    @ApiModelProperty(value = "券有效期开始时间，单位秒")
    private Date validBeginTime;
    /**
     * 券有效期结束时间，单位秒
     */
    @Excel(name = "券有效期结束时间，单位秒", width = 15)
    @ApiModelProperty(value = "券有效期结束时间，单位秒")
    private Date validEndTime;
    /**
     * 发券人的小程序open id
     */
    @Excel(name = "发券人的小程序open id", width = 15)
    @ApiModelProperty(value = "发券人的小程序open id")
    private String talentOpenId;
    /**
     * 发券人的抖音号
     */
    @Excel(name = "发券人的抖音号", width = 15)
    @ApiModelProperty(value = "发券人的抖音号")
    private String talentAccount;
    /**
     * 使用时间，单位秒
     */
    @Excel(name = "使用时间，单位秒", width = 15)
    @ApiModelProperty(value = "使用时间，单位秒")
    private Integer useTime;
    /**
     * 关联订单ID
     */
    @Excel(name = "关联订单ID", width = 15)
    @ApiModelProperty(value = "关联订单ID")
    private String orderId;
    /**
     * 优惠金额（元）
     */
    @Excel(name = "优惠金额（元）", width = 15)
    @ApiModelProperty(value = "优惠金额（元）")
    private BigDecimal discountAmount;
    /**
     * 最低消费金额（元）
     */
    @Excel(name = "最低消费金额（元）", width = 15)
    @ApiModelProperty(value = "最低消费金额（元）")
    private BigDecimal minConsumeAmount;
    /**
     * 来源渠道：live-直播间，im-私信等
     */
    @Excel(name = "来源渠道：live-直播间，im-私信等", width = 15)
    @ApiModelProperty(value = "来源渠道：live-直播间，im-私信等")
    private String sourceChannel;
    /**
     * 回调原始数据
     */
    @Excel(name = "回调原始数据", width = 15)
    @ApiModelProperty(value = "回调原始数据")
    private String callbackData;
    /**
     * 处理状态：0-未处理，1-处理成功，2-处理失败
     */
    @Excel(name = "处理状态：0-未处理，1-处理成功，2-处理失败", width = 15)
    @ApiModelProperty(value = "处理状态：0-未处理，1-处理成功，2-处理失败")
    private Integer processStatus;
    /**
     * 处理结果
     */
    @Excel(name = "处理结果", width = 15)
    @ApiModelProperty(value = "处理结果")
    private String processResult;
    /**
     * 重试次数
     */
    @Excel(name = "重试次数", width = 15)
    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 逻辑删除：0-正常，1-删除
     */
    @Excel(name = "逻辑删除：0-正常，1-删除", width = 15)
    @ApiModelProperty(value = "逻辑删除：0-正常，1-删除")
    private Integer deleted;
}
