<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.MiniAppMiniDramaFavoriteMapper">

    <!-- 分页查询收藏记录 -->
    <select id="selectFavoritePage" resultType="com.eleven.cms.douyinduanju.entity.MiniAppMiniDramaFavorite">
        SELECT
        fa.*,dr.view_count
        FROM mini_app_mini_drama_favorite fa left join mini_app_mini_drama dr on fa.album_id = dr.album_id
        WHERE fa.is_deleted = 0
        <if test="userId != null">
            AND fa.user_id = #{userId}
        </if>
        <if test="status != null">
            AND fa.status = #{status}
        </if>
        ORDER BY fa.favorite_time DESC, fa.create_time DESC
    </select>
</mapper>
