package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: openApi_album_info
 * @Author: jeecg-boot
 * @Date: 2025-05-23
 * @Version: V1.0
 */
@Data
@TableName("openApi_album_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "openApi_album_info对象", description = "openApi_album_info")
public class OpenApiAlbumInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 抖音开放平台短剧id
     */
    @Excel(name = "抖音开放平台短剧id", width = 15)
    @ApiModelProperty(value = "抖音开放平台短剧id")
    private String albumId;
    /**
     * 短剧标题
     */
    @Excel(name = "短剧标题", width = 15)
    @ApiModelProperty(value = "短剧标题")
    private String title;
    /**
     * 总集数
     */
    @Excel(name = "总集数", width = 15)
    @ApiModelProperty(value = "总集数")
    private Integer seqNum;
    /**
     * 封面图列表
     */
    @Excel(name = "封面图列表", width = 15)
    @ApiModelProperty(value = "封面图列表")
    private String coverList;
    /**
     * 发行年份
     */
    @Excel(name = "发行年份", width = 15)
    @ApiModelProperty(value = "发行年份")
    private Integer year;
    /**
     * 短剧更新状态 - 1：未上映，- 2：更新中， - 3：已完结
     */
    @Excel(name = "短剧更新状态 - 1：未上映，- 2：更新中， - 3：已完结", width = 15)
    @ApiModelProperty(value = "短剧更新状态 - 1：未上映，- 2：更新中， - 3：已完结")
    private Integer albumStatus;

    @Excel(name = "1:未送审,2:审核中 3:未通过审核  4:审核通过", width = 15)
    private Integer reviewStatus;

    @Excel(name = "1:未授权,2:已授权", width = 15)
    private Integer authorizeStatus;

    @ApiModelProperty(value = "上下架状态:1:初始,2:上架,3:下架")
    private Integer onlineStatus;

    private Integer version;
    /**
     * 短剧推荐语（12 汉字以内）
     */
    @Excel(name = "短剧推荐语（12 汉字以内）", width = 15)
    @ApiModelProperty(value = "短剧推荐语（12 汉字以内）")
    private String recommendation;
    /**
     * 短剧简介（200 汉字以内
     */
    @Excel(name = "短剧简介（200 汉字以内", width = 15)
    @ApiModelProperty(value = "短剧简介（200 汉字以内")
    private String desp;
    /**
     * 短剧类目标签（1-3 个）
     */
    @Excel(name = "短剧类目标签（1-3 个）", width = 15)
    @ApiModelProperty(value = "短剧类目标签（1-3 个）")
    private String tagList;
    /**
     * 资质状态 - 1：未报审  - 2：报审通过 - 3：报审不通过  - 4：不建议报审
     */
    @Excel(name = "资质状态 - 1：未报审  - 2：报审通过 - 3：报审不通过  - 4：不建议报审", width = 15)
    @ApiModelProperty(value = "资质状态 - 1：未报审  - 2：报审通过 - 3：报审不通过  - 4：不建议报审")
    private Integer qualification;
    /**
     * 状态 0:未创建，1:创建
     */
    @Excel(name = "状态 0:未创建，1:创建", width = 15)
    @ApiModelProperty(value = "状态 0:未创建，1:创建")
    private Integer status;
    /**
     * 剧名
     */
    @Excel(name = "剧名", width = 15)
    @ApiModelProperty(value = "剧名")
    private String name;
    /**
     * 平均单集时长，单位分钟
     */
    @Excel(name = "平均单集时长，单位分钟", width = 15)
    @ApiModelProperty(value = "平均单集时长，单位分钟")
    private Integer duration;
    /**
     * 集数
     */
    @Excel(name = "集数", width = 15)
    @ApiModelProperty(value = "集数")
    private Integer seqCount;
    private Integer seqsCount;
    /**
     * 制作机构
     */
    @Excel(name = "制作机构", width = 15)
    @ApiModelProperty(value = "制作机构")
    private String productionOrganisation;
    /**
     * 导演
     */
    @Excel(name = "导演", width = 15)
    @ApiModelProperty(value = "导演")
    private String director;
    /**
     * 制作人
     */
    @Excel(name = "制作人", width = 15)
    @ApiModelProperty(value = "制作人")
    private String producer;
    /**
     * 演员
     */
    @Excel(name = "演员", width = 15)
    @ApiModelProperty(value = "演员")
    private String actor;
    /**
     * 内容梗概（1000 汉字以内）
     */
    @Excel(name = "内容梗概（1000 汉字以内）", width = 15)
    @ApiModelProperty(value = "内容梗概（1000 汉字以内）")
    private String summary;
    /**
     * 成本配置比例情况
     */
    @Excel(name = "成本配置比例情况", width = 15)
    @ApiModelProperty(value = "成本配置比例情况")
    private String costDistributionUri;

    private String costUrl;
    /**
     * 承诺书
     */
    @Excel(name = "承诺书", width = 15)
    @ApiModelProperty(value = "承诺书")
    private String assuranceUri;
    /**
     * 制作成本类型- 10：30万以下- 20：30～100万- 30：100万以上
     */
    @Excel(name = "制作成本类型- 10：30万以下- 20：30～100万- 30：100万以上", width = 15)
    @ApiModelProperty(value = "制作成本类型- 10：30万以下- 20：30～100万- 30：100万以上")
    private Integer playletProductionCost;
    /**
     * 编剧
     */
    @Excel(name = "编剧", width = 15)
    @ApiModelProperty(value = "编剧")
    private String screenWriter;
    /**
     * 许可证号
     */
    @Excel(name = "许可证号", width = 15)
    @ApiModelProperty(value = "许可证号")
    private String licenseNum;
    /**
     * 登记号
     */
    @Excel(name = "登记号", width = 15)
    @ApiModelProperty(value = "登记号")
    private String registrationNum;
    /**
     * 普通备案号
     */
    @Excel(name = "普通备案号", width = 15)
    @ApiModelProperty(value = "普通备案号")
    private String ordinaryRecordNum;
    /**
     * 重点备案号
     */
    @Excel(name = "重点备案号", width = 15)
    @ApiModelProperty(value = "重点备案号")
    private String keyRecordNum;
    /**
     * 备案号类型 - 10：普通备案号 - 20：重点备案号
     */
    @Excel(name = "备案号类型 - 10：普通备案号 - 20：重点备案号", width = 15)
    @ApiModelProperty(value = "备案号类型 - 10：普通备案号 - 20：重点备案号")
    private Integer recordType;
    /**
     * 备案号类型 - 10：普通备案号 - 20：重点备案号
     */
    @Excel(name = "备案号类型 - 10：普通备案号 - 20：重点备案号", width = 15)
    @ApiModelProperty(value = "备案号类型 - 10：普通备案号 - 20：重点备案号")
    private String broadcastRecordNumber;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

    private String auditMsg;
    private String nickName;
}
