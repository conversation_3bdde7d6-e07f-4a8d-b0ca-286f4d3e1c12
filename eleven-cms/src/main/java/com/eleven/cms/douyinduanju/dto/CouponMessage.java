package com.eleven.cms.douyinduanju.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CouponMessage {

    @JsonProperty("type")
    private String type;

    @JsonProperty("msg")
    private MsgData msg;

    @Data
    public static class MsgData {

        @JsonProperty("coupon_id")
        private String couponId;

        @JsonProperty("app_id")
        private String appId;

        @JsonProperty("open_id")
        private String openId;

        @JsonProperty("coupon_status")
        private Integer couponStatus;

        @JsonProperty("receive_time")
        private Long receiveTime;

        @JsonProperty("merchant_meta_no")
        private String merchantMetaNo;

        @JsonProperty("valid_begin_time")
        private Long validBeginTime;

        @JsonProperty("valid_end_time")
        private Long validEndTime;

        @JsonProperty("talent_open_id")
        private String talentOpenId;

        @JsonProperty("talent_account")
        private String talentAccount;

        @JsonProperty("union_id")
        private String unionId;

        /**
         * 获取券状态描述
         */
        public String getCouponStatusDesc() {
            if (couponStatus == null) {
                return "未知";
            }
            switch (couponStatus) {
                case 10:
                    return "已领取";
                case 20:
                    return "已使用";
                case 30:
                    return "已过期";
                case 40:
                    return "已撤销";
                default:
                    return "未知状态";
            }
        }

        /**
         * 判断券是否可用
         */
        public boolean isAvailable() {
            return couponStatus != null && couponStatus == 10;
        }
    }

    /**
     * 测试JSON序列化和反序列化
     */
    public static void main(String[] args) {
        // 测试JSON数据
        String json = "{\"type\":\"send_coupon\",\"msg\":{\"coupon_id\":\"***************\",\"app_id\":\"ttxxxxx\",\"open_id\":\"********\",\"coupon_status\":10,\"receive_time\":**********,\"merchant_meta_no\":\"70908135687********\",\"valid_begin_time\":**********,\"valid_end_time\":**********,\"talent_open_id\":\"687********\",\"talent_account\":\"************\",\"union_id\":\"3d5f4913-xxxx-443d-b7ab-538db3f4e237\"}}";

        System.out.println("原始JSON: " + json);
        try {
            // 使用FastJSON反序列化
            CouponMessage couponMessage = JSONObject.parseObject(json, CouponMessage.class);
            // 序列化回JSON
            String serializedJson = JSONObject.toJSONString(couponMessage);
            System.out.println("序列化后的JSON: " + serializedJson);

        } catch (Exception e) {
            System.err.println("JSON处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
