package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class LookHistoryVO {

    /**
     * 主键
     */

    private String id;

    /**
     * 短剧主键
     */
    private String dramaId;

    /**
     * 抖音开放平台剧目id
     */
    private String albumId;
    private String albumName;

    /**
     * 集数
     */
    private Integer episodeNum;

    /**
     * 观看视频时间
     */
    private Integer watchDuration;

    /**
     * 剧集id
     */
    private String douYinEpisodeId;

    private String coverUrl;

    /**
     * 总集数
     */
    private Integer seqCount;

    private BigDecimal viewCount;
    private Integer viewMaxSeq;
    private String viewMaxSeqEpisodeId;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
}
