package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: openApi_upload_video_record
 * @Author: jeecg-boot
 * @Date: 2025-05-22
 * @Version: V1.0
 */
@Data
@TableName("openApi_upload_video_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "openApi_upload_video_record对象", description = "openApi_upload_video_record")
public class OpenApiUploadVideoRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 抖音云剧目id
     */
    private String albumId;

    /**
     * 抖音云剧集id
     */
    private String episodeId;

    /**
     * 视频阿里云地址
     */
    @Excel(name = "视频阿里云地址", width = 15)
    @ApiModelProperty(value = "视频阿里云地址")
    private String resourceVideoUrl;

    private String notyMsg;
    /**
     * 抖音开放平台视频id
     */
    @Excel(name = "抖音开放平台视频id", width = 15)
    @ApiModelProperty(value = "抖音开放平台视频id")
    private String openVideoId;
    /**
     * 抖音云的视频id
     */
    @Excel(name = "抖音云的视频id", width = 15)
    @ApiModelProperty(value = "抖音云的视频id")
    private String dyCloudId;
    /**
     * 视频标题
     */
    @Excel(name = "视频标题", width = 15)
    @ApiModelProperty(value = "视频标题")
    private String title;
    /**
     * 视频内容描述
     */
    @Excel(name = "视频内容描述", width = 15)
    @ApiModelProperty(value = "视频内容描述")
    private String description;
    /**
     * 资源类型：默认为1
     */
    @Excel(name = "资源类型：默认为1", width = 15)
    @ApiModelProperty(value = "资源类型：默认为1")
    private Integer resourceType;
    /**
     * 顺序
     */
    private Integer seq;

    private String albumName;
    /**
     * 1:未上传，2:上传中 3:上传失败  4:上传成功
     */
    @Excel(name = "1:未上传，2:上传中 3:上传失败  4:上传成功", width = 15)
    @ApiModelProperty(value = "1:未上传，2:上传中 3:上传失败 4:上传成功")
    private Integer status;

    private Date uploadTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
