package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDramaFavorite;

import java.util.List;

/**
 * MiniAppMiniDrama收藏记录服务接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IMiniAppMiniDramaFavoriteService extends IService<MiniAppMiniDramaFavorite> {

    /**
     * 添加收藏
     *
     * @param userId  用户ID
     * @param albumId 抖音开放平台短剧ID
     * @return 是否成功
     */
    boolean addFavorite(Integer userId, String albumId);

    /**
     * 取消收藏
     *
     * @param userId  用户ID
     * @param albumId 短剧ID
     * @return 是否成功
     */
    boolean removeFavorite(Integer userId, String albumId);


    /**
     * 检查是否已收藏
     *
     * @param userId  用户ID
     * @param dramaId 短剧ID
     * @return 是否已收藏
     */
    boolean isFavorited(Integer userId, String dramaId);

    /**
     * 分页查询收藏记录
     *
     * @param page   分页参数
     * @param userId 用户ID
     * @param status 收藏状态
     * @return 收藏记录分页列表
     */
    IPage<MiniAppMiniDramaFavorite> getFavoritePage(IPage<MiniAppMiniDramaFavorite> page,
                                                    Integer userId,
                                                    Integer status);

    /**
     * 根据ID查询收藏记录
     *
     * @param id 收藏记录ID
     * @return 收藏记录
     */
    MiniAppMiniDramaFavorite getFavoriteById(String id);

    /**
     * 根据用户ID和短剧ID查询收藏记录
     *
     * @param userId  用户ID
     * @param dramaId 短剧ID
     * @return 收藏记录
     */
    MiniAppMiniDramaFavorite getFavoriteByUserIdAndDramaId(Integer userId, String dramaId);

    List<MiniAppMiniDramaFavorite> getFavoriteByUserId(Integer userId);


    /**
     * 删除收藏记录（物理删除）
     *
     * @param id 收藏记录ID
     * @return 是否成功
     */
    boolean deleteFavorite(String id);

    /**
     * 批量删除收藏记录（逻辑删除）
     *
     * @param ids 收藏记录ID列表
     * @return 是否成功
     */
    boolean batchDeleteFavorites(List<String> ids);

}
