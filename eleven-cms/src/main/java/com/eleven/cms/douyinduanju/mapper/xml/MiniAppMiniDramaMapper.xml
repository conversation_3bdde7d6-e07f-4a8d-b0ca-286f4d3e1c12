<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.MiniAppMiniDramaMapper">

    <select id="pageDrama" resultType="com.eleven.cms.douyinduanju.dto.AlbumInfVO">

        SELECT
        ma.id,
        ma.id as miniDramaId,
        ma.column_id,
        ma.name,
        ma.status,
        ma.album_info_id,
        ma.free_num,
        ma.order_no,
        ma.is_deleted,
        ma.create_time,
        ma.update_time,
        ma.cover_url,
        oa.id as openApiAlbumInfoId,
        oa.title,
        oa.year,
        oa.album_status,
        oa.online_status as onlineStatus,
        oa.recommendation,
        oa.desp,
        oa.seq_num,
        oa.seq_count,
        oa.cover_list,
        oa.tag_list,
        oa.qualification,
        oa.duration,
        oa.seqs_count,
        oa.production_organisation,
        oa.director,
        oa.producer,
        oa.actor,
        oa.album_id,
        oa.summary,
        oa.cost_distribution_uri,
        oa.assurance_uri,
        oa.playlet_production_cost,
        oa.screen_writer,
        oa.license_num,
        oa.record_type,
        oa.registration_num,
        oa.authorize_status as authorizeStatus,
        oa.review_status as reviewStatus,
        oa.record_type,
        oa.key_record_num,
        oa.ordinary_record_num,
        oa.broadcast_record_number,
        oa.audit_msg,
        oa.cost_url,
        ma.per_episode_cost,
        ma.hot_value,
        ma.rating
        FROM
        mini_app_mini_drama ma left join openApi_album_info oa on ma.album_info_id = oa.id
        WHERE
        is_deleted = 0
        <if test="req !=null and req.columnId != null">
            and column_id like concat#('%', #{req.columnId}, '%')
        </if>
        <if test="req !=null and req.name != null and req.name != ''">
            and ma.name like concat('%', #{req.name}, '%')
        </if>
        <if test="req !=null and req.status != null">
            and ma.status = #{req.status}
        </if>
        <if test="req.reviewStatus !=null and req.reviewStatus != null">
            and oa.review_status = #{req.reviewStatus}
        </if>
        <if test="req.onlineStatus !=null and req.onlineStatus != null">
            and oa.online_status = #{req.onlineStatus}
        </if>
        <if test="req !=null and req.authorizeStatus != null">
            and oa.authorize_status = #{req.authorizeStatus}
        </if>
        <if test="req !=null and req.albumId != null">
            and oa.album_id = #{req.albumId}
        </if>
        <if test="req !=null and req.title != null and req.title != ''">
            and oa.title = #{req.title}
        </if>
        <if test="req !=null and req.miniDramaIdList != null and req.miniDramaIdList.size() > 0">
            and ma.id in
            <foreach item="item" collection="req.miniDramaIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        ORDER BY ma.order_no asc,ma.create_time desc

    </select>

    <select id="listDrama" resultType="com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama">

        select ma.*,info.seq_count,ep.dou_yin_episode_id as firstEpisodeId
        from mini_app_mini_drama ma left join openApi_album_info info on ma.album_info_id = info.id
        left join mini_app_drama_episode ep on ep.album_id = ma.album_id and ep.episode_seq = 1
        where ma.status = 1 and ma.is_deleted = 0
        <if test="columnId != null and columnId != ''">
            and column_id = #{columnId}
        </if>
        <if test="ids!=null and ids.size()>0">
            and ma.id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

        </if>
        order by order_no desc
    </select>

    <!-- 更新短剧信息（包括空值字段） -->
    <update id="updateByIdWithNull" parameterType="com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama">
        UPDATE mini_app_mini_drama
        SET column_id        = #{drama.columnId},
            name             = #{drama.name},
            status           = #{drama.status},
            free_num         = #{drama.freeNum},
            per_episode_cost = #{drama.perEpisodeCost},
            cover_url        = #{drama.coverUrl},
            hot_value = #{drama.hotValue},
            rating    = #{drama.rating},
            order_no = #{drama.orderNo}
        WHERE id = #{drama.id}
    </update>

    <select id="listHostDrama" resultType="com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama">

        select ma.*,info.seq_count,ep.dou_yin_episode_id as firstEpisodeId
        from mini_app_mini_drama ma left join openApi_album_info info on ma.album_info_id = info.id
        left join mini_app_drama_episode ep on ep.album_id = ma.album_id and ep.episode_seq = 1
        where ma.status = 1 and ma.is_deleted = 0
        <if test="columnId != null and columnId != ''">
            and column_id = #{columnId}
        </if>
        <if test="ids!=null and ids.size()>0">
            and ma.id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by hot_value desc
    </select>

</mapper>
