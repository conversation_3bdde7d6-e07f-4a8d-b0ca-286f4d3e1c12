package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.UserKCoinTransaction;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.mapper.UserKCoinTransactionMapper;
import com.eleven.cms.douyinduanju.service.IUserKCoinTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 用户K币交易记录服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class UserKCoinTransactionServiceImpl extends ServiceImpl<UserKCoinTransactionMapper, UserKCoinTransaction>
        implements IUserKCoinTransactionService {

    @Override
    public UserKCoinTransaction createTransaction(String userId, Integer transactionType, BigDecimal amount,
                                                  BigDecimal balanceBefore, BigDecimal balanceAfter, String description,
                                                  Integer businessType, String businessId, String orderId) {
        try {
            UserKCoinTransaction transaction = new UserKCoinTransaction();

            // 基本信息
            transaction.setUserId(userId);
            transaction.setTransactionNo(generateTransactionNo());
            transaction.setOrderId(orderId);

            // 交易信息
            transaction.setTransactionType(transactionType);
            transaction.setAmount(amount);
            transaction.setBalanceBefore(balanceBefore);
            transaction.setBalanceAfter(balanceAfter);
            transaction.setDescription(description);

            // 业务信息
            transaction.setBusinessType(businessType);
            transaction.setBusinessId(businessId);

            // 状态和时间
            transaction.setStatus(OrderEnums.TransactionStatus.SUCCESS.getCode());
            transaction.setTransactionTime(new Date());
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());

            boolean saveResult = save(transaction);
            if (saveResult) {
                log.info("创建K币交易记录成功，用户ID: {}, 交易流水号: {}, 交易类型: {}, 金额: {}",
                        userId, transaction.getTransactionNo(), transactionType, amount);
                return transaction;
            } else {
                log.error("创建K币交易记录失败，用户ID: {}, 交易类型: {}, 金额: {}", userId, transactionType, amount);
                return null;
            }

        } catch (Exception e) {
            log.error("创建K币交易记录异常，用户ID: {}, 交易类型: {}, 金额: {}", userId, transactionType, amount, e);
            return null;
        }
    }

    @Override
    public List<UserKCoinTransaction> getTransactionsByUserId(String userId, Integer limit) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }

        if (limit == null || limit <= 0) {
            limit = 20; // 默认20条
        }

        return lambdaQuery()
                .eq(UserKCoinTransaction::getUserId, userId)
                .orderByDesc(UserKCoinTransaction::getTransactionTime)
                .last("limit " + limit)
                .list();
    }

    @Override
    public List<UserKCoinTransaction> getTransactionsByOrderId(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return null;
        }

        return lambdaQuery()
                .eq(UserKCoinTransaction::getOrderId, orderId)
                .orderByDesc(UserKCoinTransaction::getTransactionTime)
                .list();
    }

    /**
     * 生成交易流水号
     */
    private String generateTransactionNo() {
        return "KC" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
