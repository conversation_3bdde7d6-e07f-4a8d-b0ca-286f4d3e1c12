package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanjuTagRelate;
import com.eleven.cms.douyinduanju.mapper.MiniAppDuanjuTagRelateMapper;
import com.eleven.cms.douyinduanju.service.IMiniAppDuanjuTagRelateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 标签和剧目关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class MiniAppDuanjuTagRelateServiceImpl extends ServiceImpl<MiniAppDuanjuTagRelateMapper, MiniAppDuanjuTagRelate> implements IMiniAppDuanjuTagRelateService {

    @Resource
    private MiniAppDuanjuTagRelateMapper miniAppDuanjuTagRelateMapper;

    @Override
    public List<MiniAppDuanjuTagRelate> getByDramaId(String dramaId) {
        return miniAppDuanjuTagRelateMapper.selectByDramaId(dramaId);
    }

    @Override
    public List<MiniAppDuanjuTagRelate> getByTagId(String tagId) {
        return miniAppDuanjuTagRelateMapper.selectByTagId(tagId);
    }

    @Override
    public List<MiniAppDuanjuTagRelate> getByAlbumId(String albumId) {
        return miniAppDuanjuTagRelateMapper.selectByAlbumId(albumId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddTagsForDrama(String dramaId, String albumId, List<String> tagIds, List<String> tagNames) {
        if (tagIds == null || tagIds.isEmpty() || tagNames == null || tagNames.isEmpty()) {
            log.warn("标签ID或标签名称列表为空");
            return false;
        }

        if (tagIds.size() != tagNames.size()) {
            log.error("标签ID和标签名称列表长度不一致");
            return false;
        }

        try {
            List<MiniAppDuanjuTagRelate> relationList = new ArrayList<>();
            for (int i = 0; i < tagIds.size(); i++) {
                // 检查是否已存在关联关系
                if (!isRelationExists(dramaId, tagIds.get(i))) {
                    MiniAppDuanjuTagRelate relation = new MiniAppDuanjuTagRelate();
                    relation.setDramaId(dramaId);
                    relation.setAlbumId(albumId);
                    relation.setTagId(tagIds.get(i));
                    relation.setTagName(tagNames.get(i));
                    relationList.add(relation);
                }
            }

            if (!relationList.isEmpty()) {
                return miniAppDuanjuTagRelateMapper.batchInsert(relationList) > 0;
            }
            return true;
        } catch (Exception e) {
            log.error("批量添加剧目标签关联失败", e);
            throw new RuntimeException("批量添加剧目标签关联失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTagsByDramaId(String dramaId) {
        try {
            return miniAppDuanjuTagRelateMapper.deleteByDramaId(dramaId) >= 0;
        } catch (Exception e) {
            log.error("删除剧目标签关联失败，dramaId: {}", dramaId, e);
            throw new RuntimeException("删除剧目标签关联失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDramasByTagId(String tagId) {
        try {
            return miniAppDuanjuTagRelateMapper.deleteByTagId(tagId) >= 0;
        } catch (Exception e) {
            log.error("删除标签剧目关联失败，tagId: {}", tagId, e);
            throw new RuntimeException("删除标签剧目关联失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRelation(String dramaId, String tagId) {
        try {
            return miniAppDuanjuTagRelateMapper.deleteByDramaIdAndTagId(dramaId, tagId) > 0;
        } catch (Exception e) {
            log.error("删除剧目标签关联失败，dramaId: {}, tagId: {}", dramaId, tagId, e);
            throw new RuntimeException("删除剧目标签关联失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDramaTagRelations(String dramaId, String albumId, List<String> tagIds, List<String> tagNames) {
        try {
            // 先删除旧的关联关系
            removeTagsByDramaId(dramaId);

            // 再添加新的关联关系
            if (tagIds != null && !tagIds.isEmpty()) {
                return batchAddTagsForDrama(dramaId, albumId, tagIds, tagNames);
            }
            return true;
        } catch (Exception e) {
            log.error("更新剧目标签关联失败，dramaId: {}", dramaId, e);
            throw new RuntimeException("更新剧目标签关联失败", e);
        }
    }

    @Override
    public boolean isRelationExists(String dramaId, String tagId) {
        QueryWrapper<MiniAppDuanjuTagRelate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drama_id", dramaId).eq("tag_id", tagId);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public List<String> getTagNamesByDramaId(String dramaId) {
        List<MiniAppDuanjuTagRelate> relations = getByDramaId(dramaId);
        return relations.stream()
                .map(MiniAppDuanjuTagRelate::getTagName)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getDramaIdsByTagId(String tagId) {
        List<MiniAppDuanjuTagRelate> relations = getByTagId(tagId);
        return relations.stream()
                .map(MiniAppDuanjuTagRelate::getDramaId)
                .collect(Collectors.toList());
    }
}
