package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

/**
 * 抖音退款API响应类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class RefundApiResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 抖音退款ID
     */
    private String refundId;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 错误码
     */
    private Integer errorCode;

    public RefundApiResponse() {
    }

    public RefundApiResponse(boolean success, String refundId) {
        this.success = success;
        this.refundId = refundId;
    }

    public RefundApiResponse(boolean success, String errorMessage, Integer errorCode) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    /**
     * 成功响应
     */
    public static RefundApiResponse success(String refundId) {
        return new RefundApiResponse(true, refundId);
    }

    /**
     * 失败响应
     */
    public static RefundApiResponse error(String errorMessage) {
        return new RefundApiResponse(false, errorMessage, null);
    }

    /**
     * 失败响应（带错误码）
     */
    public static RefundApiResponse error(String errorMessage, Integer errorCode) {
        return new RefundApiResponse(false, errorMessage, errorCode);
    }
}
