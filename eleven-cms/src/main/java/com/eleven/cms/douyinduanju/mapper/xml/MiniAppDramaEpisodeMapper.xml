<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.MiniAppDramaEpisodeMapper">
    <select id="pageEpisode" resultType="com.eleven.cms.douyinduanju.dto.EpisodeVO">
        SELECT
        vr.resource_video_url AS resourceVideoUrl,
        vr.open_video_id AS openVideoId,
        vr.dy_cloud_id AS dyCloudId,
        de.drama_title,
        de.episode_title,
        de.episode_seq,
        de.pay_flag AS payFlag,
        de.valid_status AS validStatus,
        de.cover_id AS coverId,
        de.album_id AS albumId,
        de.dou_yin_episode_id AS douYinEpisodeId,
        de.id AS id,
        de.dou_yin_video_url as douYinVideoUrl,
        de.dou_yin_video_url_expire as douYinVideoUrlExpire,
        pr.pic_url
        FROM
        mini_app_drama_episode de left join openApi_upload_video_record vr on de.video_record_id= vr.id
        left join openApi_upload_pic_record pr on pr.open_pic_id = de.cover_id
        where de.is_deleted=0
        <if test="req !=null and req.dramaTitle != null and req.dramaTitle != ''">
            and de.drama_title like concat('%', #{req.dramaTitle}, '%')
        </if>
        <if test="req !=null and req.albumId != null and req.albumId != ''">
            and de.album_id = #{req.albumId}
        </if>
        order by de.create_time desc
    </select>

    <select id="getByVideoRecordId" resultType="java.lang.String">
        select ovr.id
        from openApi_upload_video_record ovr
                 left join mini_app_drama_episode made on ovr.id = made.video_record_id and made.is_deleted = 0
        where ovr.status = 4
          and ovr.open_video_id is not null
          and made.video_record_id is null
    </select>

</mapper>
