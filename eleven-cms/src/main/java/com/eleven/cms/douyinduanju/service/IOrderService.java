package com.eleven.cms.douyinduanju.service;

import com.eleven.cms.douyinduanju.dto.CreateOrderRequest;
import com.eleven.cms.douyinduanju.dto.OrderPaymentRequest;
import com.eleven.cms.douyinduanju.dto.OrderResponse;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;

/**
 * 订单服务接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IOrderService {

    /**
     * 创建订单
     *
     * @param request 创建订单请求
     * @return 订单响应
     */
    OrderResponse createOrder(CreateOrderRequest request);

    /**
     * 支付订单
     *
     * @param request 支付请求
     * @return 支付结果
     */
    OrderResponse payOrder(OrderPaymentRequest request);

    /**
     * 查询订单详情
     *
     * @param orderNo 订单号
     * @param userId  用户ID
     * @return 订单详情
     */
    MiniAppDuanJuOrder getOrderDetail(String orderNo, String userId);

    /**
     * 取消订单
     *
     * @param orderNo 订单号
     * @param userId  用户ID
     * @return 是否成功
     */
    boolean cancelOrder(String orderNo, String userId);

    /**
     * 订单支付回调处理
     *
     * @param orderNo       订单号
     * @param transactionId 交易流水号
     * @param payStatus     支付状态
     * @return 是否处理成功
     */
    boolean handlePaymentCallback(String orderNo, String transactionId, Integer payStatus);

    /**
     * 订单退款
     *
     * @param orderNo 订单号
     * @param userId  用户ID
     * @param reason  退款原因
     * @return 是否成功
     */
    boolean refundOrder(String orderNo, String userId, String reason);
}
