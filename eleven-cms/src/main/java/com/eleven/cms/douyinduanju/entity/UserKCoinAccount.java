package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户K币账户表
 *
 * <AUTHOR>
 */
@Data
@TableName("mini_app_user_kcoin_account")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户K币账户", description = "用户K币账户信息")
public class UserKCoinAccount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 可用K币余额
     */
    @Excel(name = "可用K币余额", width = 15)
    @ApiModelProperty(value = "可用K币余额")
    private BigDecimal availableBalance;

    /**
     * 冻结K币余额
     */
    @Excel(name = "冻结K币余额", width = 15)
    @ApiModelProperty(value = "冻结K币余额")
    private BigDecimal frozenBalance;

    /**
     * 总充值金额
     */
    @Excel(name = "总充值金额", width = 15)
    @ApiModelProperty(value = "总充值金额")
    private BigDecimal totalRecharge;

    /**
     * 总消费金额
     */
    @Excel(name = "总消费金额", width = 15)
    @ApiModelProperty(value = "总消费金额")
    private BigDecimal totalConsume;

    /**
     * 账户状态: 0正常 1冻结 2注销
     */
    @Excel(name = "账户状态", width = 15)
    @ApiModelProperty(value = "账户状态: 0正常 1冻结 2注销")
    private Integer status;

    /**
     * 最后交易时间
     */
    @Excel(name = "最后交易时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后交易时间")
    private Date lastTransactionTime;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 是否删除: 0否 1是
     */
    @ApiModelProperty(value = "是否删除: 0否 1是")
    private Integer isDeleted;

    /**
     * 版本号（乐观锁）
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;
}
