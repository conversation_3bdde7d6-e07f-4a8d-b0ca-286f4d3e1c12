package com.eleven.cms.douyinduanju.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * MiniAppDramaEpisode响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(value = "MiniAppDramaEpisode响应", description = "MiniAppDramaEpisode响应数据")
public class MiniAppDramaEpisodeResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 短剧信息主键
     */
    @ApiModelProperty(value = "短剧信息主键")
    private String miniDramaId;

    /**
     * 抖音云剧集id
     */
    @ApiModelProperty(value = "抖音云剧集id")
    private String douYinEpisodeId;

    /**
     * 抖音视频播放地址
     */
    @ApiModelProperty(value = "抖音视频播放地址")
    private String douYinVideoUrl;

    /**
     * 抖音视频地址过期时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "抖音视频地址过期时间")
    private Date douYinVideoUrlExpire;

    /**
     * 短剧标题
     */
    @ApiModelProperty(value = "短剧标题")
    private String dramaTitle;

    /**
     * 是否有观看权限
     */
    @ApiModelProperty(value = "是否有观看权限")
    private Boolean hasPermissionView;

    /**
     * 权限类型 1免费 2会员 3剧卡 4付费
     */
    @ApiModelProperty(value = "权限类型 1免费 2会员 3剧卡  4付费")
    private Integer permissionType;

    /**
     * 锁定状态 0 免费 1 未解锁 2:已解锁
     */
    private Integer lockStatus;
    /**
     * 权限类型描述
     */
    @ApiModelProperty(value = "权限类型描述")
    private String permissionTypeDesc;

    /**
     * 剧集标题
     */
    @ApiModelProperty(value = "剧集标题")
    private String episodeTitle;

    /**
     * 抖音云剧目id
     */
    @ApiModelProperty(value = "抖音云剧目id")
    private String albumId;

    /**
     * 视频上传记录主键
     */
    @ApiModelProperty(value = "视频上传记录主键")
    private String videoRecordId;


    /**
     * 生效状态 0 未生效 1 生效
     */
    @ApiModelProperty(value = "生效状态 0 未生效 1 生效")
    private Integer validStatus;


    /**
     * 封面id
     */
    @ApiModelProperty(value = "封面id")
    private String coverId;

    /**
     * 封面图
     */
    @ApiModelProperty(value = "封面图")
    private String coverUrl;

    /**
     * 剧集顺序
     */
    @ApiModelProperty(value = "剧集顺序")
    private Integer episodeSeq;

    /**
     * 是否已收藏
     */
    @ApiModelProperty(value = "是否已收藏")
    private Boolean isFavorited;

    /**
     * 收藏记录ID
     */
    @ApiModelProperty(value = "收藏记录ID")
    private String favoriteId;

    /**
     * 每集付费金额
     */
    private Integer perEpisodeCost;

    /**
     * 收藏时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "收藏时间")
    private Date favoriteTime;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private List<String> tagNameList;

    /**
     * 获取权限类型描述
     */
    public String getPermissionTypeDesc() {
        if (permissionType == null) {
            return "";
        }
        switch (permissionType) {
            case 1:
                return "免费";
            case 2:
                return "会员";
            case 3:
                return "剧卡";
            case 4:
                return "付费";
            default:
                return "未知";
        }
    }
}
