package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.UserDramaCard;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.mapper.UserDramaCardMapper;
import com.eleven.cms.douyinduanju.service.IUserDramaCardService;
import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 用户剧卡权益服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class UserDramaCardServiceImpl extends ServiceImpl<UserDramaCardMapper, UserDramaCard>
        implements IUserDramaCardService {

    @Override
    public UserDramaCard getValidDramaCard(String userId, String dramaId) {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(dramaId)) {
            return null;
        }

        try {
            UserDramaCard dramaCard = lambdaQuery()
                    .eq(UserDramaCard::getUserId, userId)
                    .eq(UserDramaCard::getDramaId, dramaId)
                    .eq(UserDramaCard::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                    .eq(UserDramaCard::getIsDeleted, 0)
                    .one();

            if (dramaCard != null) {
                // 检查是否过期
                if (isExpired(dramaCard)) {
                    // 更新状态为已过期
                    dramaCard.setStatus(OrderEnums.BenefitStatus.EXPIRED.getCode());
                    dramaCard.setUpdateTime(new Date());
                    updateById(dramaCard);

                    log.info("剧卡已过期，用户ID: {}, 短剧ID: {}, 剧卡ID: {}", userId, dramaId, dramaCard.getId());
                    return null;
                }
            }

            return dramaCard;

        } catch (Exception e) {
            log.error("获取有效剧卡异常，用户ID: {}, 短剧ID: {}", userId, dramaId, e);
            return null;
        }
    }

    @Override
    public List<UserDramaCard> getValidDramaCardsByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }

        try {
            List<UserDramaCard> dramaCards = lambdaQuery()
                    .eq(UserDramaCard::getUserId, userId)
                    .eq(UserDramaCard::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                    .eq(UserDramaCard::getIsDeleted, 0)
                    .orderByDesc(UserDramaCard::getPurchaseTime)
                    .list();

            // 检查并更新过期的剧卡
            if (dramaCards != null && !dramaCards.isEmpty()) {
                List<UserDramaCard> expiredCards = dramaCards.stream()
                        .filter(this::isExpired)
                        .collect(java.util.stream.Collectors.toList());

                if (!expiredCards.isEmpty()) {
                    // 批量更新过期状态
                    expiredCards.forEach(card -> {
                        card.setStatus(OrderEnums.BenefitStatus.EXPIRED.getCode());
                        card.setUpdateTime(new Date());
                    });
                    updateBatchById(expiredCards);

                    // 从结果中移除过期的剧卡
                    dramaCards.removeAll(expiredCards);

                    log.info("批量更新过期剧卡，用户ID: {}, 过期数量: {}", userId, expiredCards.size());
                }
            }

            return dramaCards;

        } catch (Exception e) {
            log.error("获取用户剧卡列表异常，用户ID: {}", userId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateDramaCard(String userId, String orderId, String albumName,
                                     String albumId, String dramaId, Integer packageType,
                                     Boolean isPermanent, Integer durationDays) {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(dramaId)) {
            throw new BusinessException("用户ID和短剧ID不能为空");
        }

        try {
            // 检查是否已存在有效的剧卡
            UserDramaCard existingCard = getValidDramaCard(userId, dramaId);
            if (existingCard != null) {
                log.info("用户已拥有该短剧的有效剧卡，用户ID: {}, 短剧ID: {}", userId, dramaId);
                return true; // 已存在有效剧卡，视为成功
            }

            // 检查是否有已取消或过期的剧卡，如果有则更新，否则创建新的
            UserDramaCard dramaCard = lambdaQuery()
                    .eq(UserDramaCard::getUserId, userId)
                    .eq(UserDramaCard::getAlbumId, dramaId)
                    .eq(UserDramaCard::getIsDeleted, 0)
                    .one();

            if (dramaCard == null) {
                // 创建新的剧卡
                dramaCard = new UserDramaCard();
                dramaCard.setUserId(userId);
                dramaCard.setOrderId(orderId);
                dramaCard.setDramaId(dramaId);
                dramaCard.setAlbumName(albumName);
                dramaCard.setAlbumId(albumId);
                dramaCard.setPurchaseTime(new Date());
                dramaCard.setCreateTime(new Date());
            } else {
                // 更新现有剧卡
                dramaCard.setOrderId(orderId);
                dramaCard.setAlbumName(albumName);
                dramaCard.setAlbumId(albumId);
                dramaCard.setPurchaseTime(new Date());
            }

            // 设置有效期
            if (isPermanent != null && isPermanent) {
                dramaCard.setIsPermanent(1);
                dramaCard.setExpireTime(null);
            } else {
                dramaCard.setIsPermanent(0);
                if (durationDays != null && durationDays > 0) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_MONTH, durationDays);
                    dramaCard.setExpireTime(calendar.getTime());
                } else {
                    // 默认永久有效
                    dramaCard.setIsPermanent(1);
                    dramaCard.setExpireTime(null);
                }
            }

            // 设置状态
            dramaCard.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());
            dramaCard.setUpdateTime(new Date());
            dramaCard.setIsDeleted(0);
            dramaCard.setRangeType(1);
            boolean result;
            if (dramaCard.getId() == null) {
                result = save(dramaCard);
            } else {
                result = updateById(dramaCard);
            }

            if (result) {
                log.info("剧卡权益开通成功，用户ID: {}, 短剧ID: {}, 订单ID: {}, 是否永久: {}",
                        userId, dramaId, orderId, isPermanent);
                return true;
            } else {
                log.error("剧卡权益开通失败，用户ID: {}, 短剧ID: {}, 订单ID: {}", userId, dramaId, orderId);
                throw new BusinessException("剧卡权益开通失败");
            }

        } catch (Exception e) {
            log.error("开通剧卡权益异常，用户ID: {}, 短剧ID: {}, 订单ID: {}", userId, dramaId, orderId, e);
            throw new BusinessException("开通剧卡权益失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelDramaCard(String userId, String orderId) {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(orderId)) {
            throw new BusinessException("用户ID和订单ID不能为空");
        }

        try {
            // 查找对应的剧卡
            UserDramaCard dramaCard = lambdaQuery()
                    .eq(UserDramaCard::getUserId, userId)
                    .eq(UserDramaCard::getOrderId, orderId)
                    .eq(UserDramaCard::getIsDeleted, 0)
                    .one();

            if (dramaCard == null) {
                log.warn("未找到对应的剧卡，用户ID: {}, 订单ID: {}", userId, orderId);
                return false;
            }

            // 更新状态为已取消
            dramaCard.setStatus(OrderEnums.BenefitStatus.CANCELLED.getCode());
            dramaCard.setUpdateTime(new Date());

            boolean result = updateById(dramaCard);

            if (result) {
                log.info("剧卡权益取消成功，用户ID: {}, 短剧ID: {}, 订单ID: {}",
                        userId, dramaCard.getDramaId(), orderId);
                return true;
            } else {
                log.error("剧卡权益取消失败，用户ID: {}, 订单ID: {}", userId, orderId);
                throw new BusinessException("剧卡权益取消失败");
            }

        } catch (Exception e) {
            log.error("取消剧卡权益异常，用户ID: {}, 订单ID: {}", userId, orderId, e);
            throw new BusinessException("取消剧卡权益失败: " + e.getMessage());
        }
    }

    @Override
    public void refreshDramaCardStatus(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return;
        }

        try {
            // 获取所有生效中的剧卡
            List<UserDramaCard> activeCards = lambdaQuery()
                    .eq(UserDramaCard::getUserId, userId)
                    .eq(UserDramaCard::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                    .eq(UserDramaCard::getIsDeleted, 0)
                    .list();

            if (activeCards == null || activeCards.isEmpty()) {
                return;
            }

            // 检查过期的剧卡
            List<UserDramaCard> expiredCards = activeCards.stream()
                    .filter(this::isExpired)
                    .collect(java.util.stream.Collectors.toList());

            if (!expiredCards.isEmpty()) {
                // 批量更新过期状态
                expiredCards.forEach(card -> {
                    card.setStatus(OrderEnums.BenefitStatus.EXPIRED.getCode());
                    card.setUpdateTime(new Date());
                });

                boolean updateResult = updateBatchById(expiredCards);

                if (updateResult) {
                    log.info("刷新用户剧卡状态成功，用户ID: {}, 过期剧卡数量: {}", userId, expiredCards.size());
                } else {
                    log.error("刷新用户剧卡状态失败，用户ID: {}", userId);
                }
            } else {
                log.debug("用户无过期剧卡，用户ID: {}", userId);
            }

        } catch (Exception e) {
            log.error("刷新用户剧卡状态异常，用户ID: {}", userId, e);
        }
    }


    /**
     * 检查剧卡是否过期
     *
     * @param dramaCard 剧卡信息
     * @return 是否过期
     */
    private boolean isExpired(UserDramaCard dramaCard) {
        if (dramaCard == null) {
            return true;
        }

        // 永久有效的剧卡不会过期
        if (dramaCard.getIsPermanent() != null && dramaCard.getIsPermanent() == 1) {
            return false;
        }

        // 检查过期时间
        Date expireTime = dramaCard.getExpireTime();
        if (expireTime == null) {
            // 没有过期时间，视为永久有效
            return false;
        }

        return expireTime.before(new Date());
    }

    /**
     * 根据短剧ID获取所有有效的剧卡用户
     *
     * @param dramaId 短剧ID
     * @return 用户ID列表
     */
    public List<String> getValidUsersByDramaId(String dramaId) {
        if (StringUtils.isEmpty(dramaId)) {
            return null;
        }

        try {
            List<UserDramaCard> dramaCards = lambdaQuery()
                    .eq(UserDramaCard::getDramaId, dramaId)
                    .eq(UserDramaCard::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                    .eq(UserDramaCard::getIsDeleted, 0)
                    .list();

            if (dramaCards == null || dramaCards.isEmpty()) {
                return null;
            }

            // 过滤掉过期的剧卡，返回有效用户ID列表
            return dramaCards.stream()
                    .filter(card -> !isExpired(card))
                    .map(UserDramaCard::getUserId)
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());

        } catch (Exception e) {
            log.error("根据短剧ID获取有效用户异常，短剧ID: {}", dramaId, e);
            return null;
        }
    }



    /**
     * 延长剧卡有效期
     *
     * @param userId     用户ID
     * @param dramaId    短剧ID
     * @param extendDays 延长天数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean extendDramaCard(String userId, String dramaId, Integer extendDays) {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(dramaId) ||
                extendDays == null || extendDays <= 0) {
            throw new BusinessException("延长剧卡有效期参数无效");
        }

        try {
            UserDramaCard dramaCard = lambdaQuery()
                    .eq(UserDramaCard::getUserId, userId)
                    .eq(UserDramaCard::getDramaId, dramaId)
                    .eq(UserDramaCard::getIsDeleted, 0)
                    .one();

            if (dramaCard == null) {
                throw new BusinessException("剧卡不存在");
            }

            // 如果是永久有效的剧卡，无需延长
            if (dramaCard.getIsPermanent() != null && dramaCard.getIsPermanent() == 1) {
                log.info("剧卡已是永久有效，无需延长，用户ID: {}, 短剧ID: {}", userId, dramaId);
                return true;
            }

            // 计算新的过期时间
            Date newExpireTime;
            if (dramaCard.getExpireTime() != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(dramaCard.getExpireTime());
                calendar.add(Calendar.DAY_OF_MONTH, extendDays);
                newExpireTime = calendar.getTime();
            } else {
                // 如果原来没有过期时间，从当前时间开始计算
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_MONTH, extendDays);
                newExpireTime = calendar.getTime();
            }

            // 更新过期时间和状态
            dramaCard.setExpireTime(newExpireTime);
            dramaCard.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());
            dramaCard.setUpdateTime(new Date());

            boolean result = updateById(dramaCard);

            if (result) {
                log.info("剧卡有效期延长成功，用户ID: {}, 短剧ID: {}, 延长天数: {}, 新过期时间: {}",
                        userId, dramaId, extendDays, newExpireTime);
                return true;
            } else {
                throw new BusinessException("剧卡有效期延长失败");
            }

        } catch (Exception e) {
            log.error("延长剧卡有效期异常，用户ID: {}, 短剧ID: {}, 延长天数: {}", userId, dramaId, extendDays, e);
            throw new BusinessException("延长剧卡有效期失败: " + e.getMessage());
        }
    }
}
