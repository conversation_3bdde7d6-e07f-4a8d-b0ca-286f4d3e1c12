package com.eleven.cms.douyinduanju.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 抖音退款回调消息
 * 根据抖音开放平台文档定义
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RefundNotifyMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 小程序ID
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 退款状态：SUCCESS-成功，FAIL-失败
     */
    @JsonProperty("status")
    private String status;

    /**
     * 抖音订单ID
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 商户扩展字段
     */
    @JsonProperty("cp_extra")
    private String cpExtra;

    /**
     * 退款失败原因
     */
    @JsonProperty("message")
    private String message;

    /**
     * 事件时间戳（毫秒）
     */
    @JsonProperty("event_time")
    private Long eventTime;

    /**
     * 抖音退款ID
     */
    @JsonProperty("refund_id")
    private String refundId;

    /**
     * 商户退款单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 退款总金额（分）
     */
    @JsonProperty("refund_total_amount")
    private Integer refundTotalAmount;

    /**
     * 是否全部结算
     */
    @JsonProperty("is_all_settled")
    private Boolean isAllSettled;

    /**
     * 退款商品详情
     */
    @JsonProperty("refund_item_detail")
    private RefundItemDetail refundItemDetail;

    /**
     * 退款商品详情
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class RefundItemDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 商品订单数量
         */
        @JsonProperty("item_order_quantity")
        private Integer itemOrderQuantity;

        /**
         * 商品订单详情列表
         */
        @JsonProperty("item_order_detail")
        private List<ItemOrderDetail> itemOrderDetail;

        /**
         * 商品订单详情
         */
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class ItemOrderDetail implements Serializable {

            private static final long serialVersionUID = 1L;

            /**
             * 退款金额（分）
             */
            @JsonProperty("refund_amount")
            private Integer refundAmount;

            /**
             * 商品订单ID
             */
            @JsonProperty("item_order_id")
            private String itemOrderId;
        }
    }
}
