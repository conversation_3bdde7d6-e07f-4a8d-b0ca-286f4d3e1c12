package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: openApi_upload_pic_record
 * @Author: jeecg-boot
 * @Date: 2025-05-23
 * @Version: V1.0
 */
@Data
@TableName("openApi_upload_pic_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "openApi_upload_pic_record对象", description = "openApi_upload_pic_record")
public class OpenApiUploadPicRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 图片阿里云地址
     */
    @Excel(name = "图片阿里云地址", width = 15)
    @ApiModelProperty(value = "图片阿里云地址")
    private String picUrl;
    /**
     * 抖音开放平台图片id
     */
    @Excel(name = "抖音开放平台图片id", width = 15)
    @ApiModelProperty(value = "抖音开放平台图片id")
    private String openPicId;
    /**
     * 图片标题
     */
    @Excel(name = "图片标题", width = 15)
    @ApiModelProperty(value = "图片标题")
    private String title;
    /**
     * 视频内容描述
     */
    @Excel(name = "视频内容描述", width = 15)
    @ApiModelProperty(value = "视频内容描述")
    private String description;
    /**
     * 资源类型：默认为2
     */
    @Excel(name = "资源类型：默认为2", width = 15)
    @ApiModelProperty(value = "资源类型：默认为2")
    private Integer resourceType;
    /**
     * 1未上传 2:上传失败  3:上传成功
     */
    @Excel(name = "1未上传 2:上传失败  3:上传成功", width = 15)
    @ApiModelProperty(value = "1未上传 2:上传失败  3:上传成功")
    private Integer status;
    /**
     * 第几集
     */
    @Excel(name = "第几集", width = 15)
    @ApiModelProperty(value = "第几集")
    private Integer seq;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
