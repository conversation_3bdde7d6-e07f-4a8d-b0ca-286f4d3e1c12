<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.OpenApiAlbumInfoMapper">

    <!-- 更新短剧专辑信息（包括空值字段） -->
    <update id="updateByIdWithNull" parameterType="com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo">
        UPDATE openApi_album_info
        SET
            title                   = #{albumInfo.title},
            seq_num                 = #{albumInfo.seqNum},
            cover_list              = #{albumInfo.coverList},
            year                    = #{albumInfo.year},
            album_status            = #{albumInfo.albumStatus},
            review_status           = #{albumInfo.reviewStatus},
            authorize_status        = #{albumInfo.authorizeStatus},
            online_status           = #{albumInfo.onlineStatus},
            recommendation          = #{albumInfo.recommendation},
            desp                    = #{albumInfo.desp},
            tag_list                = #{albumInfo.tagList},
            qualification           = #{albumInfo.qualification},
            status                  = #{albumInfo.status},
            name                    = #{albumInfo.name},
            duration                = #{albumInfo.duration},
            seq_count               = #{albumInfo.seqCount},
            seqs_count              = #{albumInfo.seqsCount},
            production_organisation = #{albumInfo.productionOrganisation},
            director                = #{albumInfo.director},
            producer                = #{albumInfo.producer},
            actor                   = #{albumInfo.actor},
            summary                 = #{albumInfo.summary},
            cost_distribution_uri   = #{albumInfo.costDistributionUri},
            cost_url                = #{albumInfo.costUrl},
            assurance_uri           = #{albumInfo.assuranceUri},
            playlet_production_cost = #{albumInfo.playletProductionCost},
            screen_writer           = #{albumInfo.screenWriter},
            license_num             = #{albumInfo.licenseNum},
            registration_num        = #{albumInfo.registrationNum},
            ordinary_record_num     = #{albumInfo.ordinaryRecordNum},
            key_record_num          = #{albumInfo.keyRecordNum},
            record_type             = #{albumInfo.recordType},
            broadcast_record_number = #{albumInfo.broadcastRecordNumber},
            cost_url = #{albumInfo.costUrl}
        WHERE id = #{albumInfo.id}
    </update>

</mapper>
