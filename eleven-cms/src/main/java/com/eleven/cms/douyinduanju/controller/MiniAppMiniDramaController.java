package com.eleven.cms.douyinduanju.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.dto.*;
import com.eleven.cms.douyinduanju.entity.MiniAppAlbumAuditMsg;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;
import com.eleven.cms.douyinduanju.entity.MiniAppReviewRelate;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.service.*;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.service.IDouyinAppConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: mini_app_mini_drama
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Api(tags="mini_app_mini_drama")
@RestController
@RequestMapping("/douyinduanju/miniAppMiniDrama")
@Slf4j
public class MiniAppMiniDramaController extends JeecgController<MiniAppMiniDrama, IMiniAppMiniDramaService> {
	@Autowired
	private IMiniAppMiniDramaService miniAppMiniDramaService;


	@Resource
	DouYinHttpUtil douYinHttpUtil;

	@Resource
	IOpenApiAlbumInfoService openApiAlbumInfoService;

	@Resource
	private IMiniAppDramaEpisodeService episodeService;

	@Resource
	private IOpenApiUploadVideoRecordService uploadVideoRecordService;
	/**
	 * 分页列表查询
	 *
	 * @param miniAppMiniDrama
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "mini_app_mini_drama-分页列表查询")
	@ApiOperation(value="mini_app_mini_drama-分页列表查询", notes="mini_app_mini_drama-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(MiniAppMiniDrama miniAppMiniDrama,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MiniAppMiniDrama> queryWrapper = QueryGenerator.initQueryWrapper(miniAppMiniDrama, req.getParameterMap());
		Page<MiniAppMiniDrama> page = new Page<MiniAppMiniDrama>(pageNo, pageSize);
		IPage<MiniAppMiniDrama> pageList = miniAppMiniDramaService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	@GetMapping("/pageAlbumInfo")
	public Result<Object> pageDrama(@SpringQueryMap DramaReq req, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
									@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
		IPage<AlbumInfVO> page = new Page<>(pageNo, pageSize);
//		Map<String, String[]> parameterMap = request.getParameterMap();
//		DramaReq req = new DramaReq();
//		if (Objects.nonNull(parameterMap.get("name"))) {
//			String[] names = parameterMap.get("name");
//			req.setName(names[0]);
//		}
		IPage<AlbumInfVO> miniAppMiniDramaVOIPage = miniAppMiniDramaService.pageAlbumInfo(req, page);
		return Result.ok(miniAppMiniDramaVOIPage);
	}

	/**
	 * 调用抖音开放瓶体,创建短剧
	 */
	@GetMapping("/createAlbumInfo")
	public Result<?> createDuanJuForAlbumInfo(@RequestParam("id") String id) {
		AlbumInfo albumInfo = openApiAlbumInfoService.getAlbumInfoByAlbumInfoId(id);
		OpenApiRes openApiRes = douYinHttpUtil.createDuanJu(albumInfo);
		if (!openApiRes.isSuccess()) {
			return Result.error(openApiRes.getErrMsg());
		}
		String albumId = openApiRes.getAlbumId();
		OpenApiAlbumInfo update = new OpenApiAlbumInfo();
		update.setId(id);
		update.setAlbumId(albumId);
		openApiAlbumInfoService.updateById(update);

		MiniAppMiniDrama drama = miniAppMiniDramaService.getByAlbumInfoId(id);
		drama.setAlbumId(albumId);
		miniAppMiniDramaService.updateById(drama);
		return Result.ok(albumId);
	}

	/**
	 * 短剧送审 更新送审状态
	 */
	@GetMapping("/reviewDrama")
	public Result<?> review(@RequestParam("albumId") String albumId) {
		OpenApiAlbumInfo apiAlbumInfo = openApiAlbumInfoService.getByAlbumId(albumId);
		if (apiAlbumInfo.getReviewStatus() == 4) {
			return Result.error("短剧已审核通过");
		}
		if (apiAlbumInfo.getReviewStatus() == 2) {
			return Result.error("短剧在审核中");
		}
		OpenApiRes reviewResult = douYinHttpUtil.review(albumId);
		if (!reviewResult.isSuccess()) {
			return Result.error(reviewResult.getErrMsg());
		}
		Integer version = null;
		try {
			JSONObject jsonObject = JSONObject.parseObject(reviewResult.getData());
			version = jsonObject.getInteger("version");
		} catch (Exception e) {
			log.info("短剧送审-解析json异常", e);
		}
		openApiAlbumInfoService.updateReviewStatusByAlbumId(albumId, version);
		return Result.ok(reviewResult.getData());
	}

	/**
	 * 短剧授权
	 */
	@GetMapping("/authorizeDrama")
	public Result<?> authorizeDrama(@RequestParam("albumId") String albumId, @RequestParam(defaultValue = "mhjc", required = false) String resource) {

		OpenApiAlbumInfo apiAlbumInfo = openApiAlbumInfoService.getByAlbumId(albumId);
		if (apiAlbumInfo.getAuthorizeStatus() == 2) {
			return Result.error("短剧已授权");
		}

		OpenApiRes openApiRes = douYinHttpUtil.authorize(albumId, resource, null, null);
		if (openApiRes.isSuccess()) {
			openApiAlbumInfoService.updateAuthorizeStatusByAlbumId(albumId);
			return Result.ok("短剧授权成功");
		} else {
			return Result.error(openApiRes.getErrMsg());
		}
	}

	/**
	 * 短剧上下线
	 */
	@ApiOperation("短剧上下线")
	@GetMapping("/toggleOnlineStatus")
	public Result<?> toggleOnlineStatus(@RequestParam("albumId") String albumId, Integer onlineStatus, @RequestParam(required = false, defaultValue = "mhjc") String businessType) {
		OpenApiAlbumInfo apiAlbumInfo = openApiAlbumInfoService.getByAlbumId(albumId);
		OpenApiRes openApiRes = douYinHttpUtil.online(albumId, onlineStatus, apiAlbumInfo.getVersion(), businessType);
		if (openApiRes.isSuccess() && "mhjc".equals(businessType)) {
			openApiAlbumInfoService.updateOnlineStatusByAlbumId(albumId, onlineStatus);
			return Result.ok("操作成功");
		} else {
			return Result.error(openApiRes.getErrMsg());
		}
	}
	/**
	 * 编辑短剧信息
	 */
	@GetMapping("/editAlbumInfo")
	public Result<?> editAlbumInfo(String id) {
		AlbumInfo albumInfo = openApiAlbumInfoService.getAlbumInfoByAlbumInfoId(id);
		return Result.ok(douYinHttpUtil.editAlbumInfo(albumInfo));
	}


	@Resource
	private IDouyinAppConfigService douyinAppConfigService;

	@Resource
	private IMiniAppReviewRelateService iMiniAppReviewRelateService;

	@ApiOperation("批量授权")
	@GetMapping("/batchAuthorize")
	public Result<?> batchAuthorize(@RequestParam("albumIds") String albumIds, @RequestParam("appIds") String appIds) {
		String[] albumIdsArr = albumIds.split(",");
		List<String> appIdsList = Arrays.stream(appIds.split(",")).collect(Collectors.toList());
		List<DouyinAppConfig> appConfigs = douyinAppConfigService.lambdaQuery().in(DouyinAppConfig::getId, appIdsList).list();
		AuthorizeRespon authorizeRespon = new AuthorizeRespon();
		int successCount = 0;
		int failCount = 0;
		for (String albumId : albumIdsArr) {
			for (DouyinAppConfig appConfig : appConfigs) {
				try {
					OpenApiRes openApiRes = douYinHttpUtil.authorize(albumId, "mhjc", null, appConfig.getAppId());
					MiniAppReviewRelate relate = new MiniAppReviewRelate();
					relate.setAppId(appConfig.getAppId());
					relate.setAlbumId(albumId);
					relate.setAuthorizeStatus(openApiRes.isSuccess() ? 2 : -1);
					if (openApiRes.isSuccess()) {
						successCount = successCount + 1;
					} else {
						failCount = failCount + 1;
					}
					iMiniAppReviewRelateService.save(relate);
				} catch (Exception e) {
					log.error("授权失败", e);
					failCount = failCount + 1;

				}
			}
		}
		authorizeRespon.setSuccessCount(successCount);
		authorizeRespon.setFailCount(failCount);
		return Result.ok(authorizeRespon);
	}

	@Data
	public static class AuthorizeRespon {
		private Integer successCount;
		private Integer failCount;
	}
	/**
	 *   添加
	 *
	 * @param req
	 * @return
	 */
	@AutoLog(value = "mini_app_mini_drama-添加")
	@ApiOperation(value="mini_app_mini_drama-添加", notes="mini_app_mini_drama-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody MiniAppMiniDramaReq req) {
		MiniAppMiniDrama miniAppMiniDrama = miniAppMiniDramaService.addDramaAndAlbumInfo(req);
		return Result.ok(miniAppMiniDrama);
	}


	 /**
	 *  编辑
	 *
	 * @param miniAppMiniDrama
	 * @return
	 */
	@AutoLog(value = "mini_app_mini_drama-编辑")
	@ApiOperation(value="mini_app_mini_drama-编辑", notes="mini_app_mini_drama-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody MiniAppMiniDramaReq miniAppMiniDrama) {
		miniAppMiniDramaService.editDramaAndAlbumInfo(miniAppMiniDrama);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "mini_app_mini_drama-通过id删除")
	@ApiOperation(value="mini_app_mini_drama-通过id删除", notes="mini_app_mini_drama-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		miniAppMiniDramaService.lambdaUpdate().set(MiniAppMiniDrama::getIsDeleted, 1).eq(MiniAppMiniDrama::getId, id).update();
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "mini_app_mini_drama-批量删除")
	@ApiOperation(value="mini_app_mini_drama-批量删除", notes="mini_app_mini_drama-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids", required = true) String ids) {
		List<String> idList = Arrays.asList(ids.split(","));
		if (CollectionUtil.isEmpty(idList)) {
			return Result.error("参数id不能为空");
		}
		miniAppMiniDramaService.lambdaUpdate().set(MiniAppMiniDrama::getIsDeleted, 1).in(MiniAppMiniDrama::getId, idList).update();
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "mini_app_mini_drama-通过id查询")
	@ApiOperation(value="mini_app_mini_drama-通过id查询", notes="mini_app_mini_drama-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		MiniAppMiniDrama miniAppMiniDrama = miniAppMiniDramaService.getById(id);
		if(miniAppMiniDrama==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(miniAppMiniDrama);
	}

//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param miniAppMiniDrama
//    */
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, MiniAppMiniDrama miniAppMiniDrama) {
//        return super.exportXls(request, miniAppMiniDrama, MiniAppMiniDrama.class, "mini_app_mini_drama");
//    }


	/**
	 * 导出短剧信息Excel
	 *
	 * @param req      查询条件
	 * @param response 响应对象
	 * @return ModelAndView
	 */
	@GetMapping(value = "/exportXls")
	@ApiOperation(value = "导出短剧信息Excel", notes = "导出短剧信息Excel")
	public Result<?> exportXls(@SpringQueryMap DramaReq req, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
							   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletResponse response) {
		try {


			// 获取所有数据用于导出，设置页面大小为最大值以获取全部数据
			IPage<AlbumInfVO> page = new Page<>(pageNo, Integer.MAX_VALUE);
			IPage<AlbumInfVO> albumInfVOIPage = miniAppMiniDramaService.pageAlbumInfo(req, page);
			List<AlbumInfVO> albumList = albumInfVOIPage.getRecords();
			if (Objects.nonNull(req) && StringUtils.isNotEmpty(req.getSelections())) {
				List<String> selections = Arrays.asList(req.getSelections().split(","));
				req.setMiniDramaIdList(selections);
			}

//			 检查是否有数据可导出
			if (albumList == null || albumList.isEmpty()) {
				log.warn("没有找到要导出的数据");
				response.setStatus(HttpServletResponse.SC_NO_CONTENT);
				try {
					response.getWriter().write("没有找到要导出的数据");
				} catch (Exception ex) {
					log.error("写入响应失败", ex);
				}
				return null;
			}

			// 设置导出参数，使用英文标题避免编码问题
			// 注意：这里使用英文是为了避免Excel导出时的字符编码问题
			ExportParams exportParams = new ExportParams();
			log.info("导出短剧信息Excel成功，共导出{}条记录", albumList.size());

			// 创建ModelAndView对象，用于Excel导出
			ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
			// 设置导出文件名，添加时间戳避免重名
			mv.addObject(NormalExcelConstants.FILE_NAME, "短剧信息列表_" + System.currentTimeMillis());
			// 设置导出的实体类
			mv.addObject(NormalExcelConstants.CLASS, AlbumInfVO.class);
			// 设置导出参数
			mv.addObject(NormalExcelConstants.PARAMS, exportParams);
			// 设置要导出的数据列表
			mv.addObject(NormalExcelConstants.DATA_LIST, albumList);
			Workbook workbook = ExcelExportUtil.exportExcel(exportParams, AlbumInfVO.class, albumList);
			ByteArrayOutputStream bos = new ByteArrayOutputStream();

			// 5. 写出文件流
			try {
				workbook.write(bos);
				return Result.ok(bos.toByteArray());
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		} catch (Exception e) {
			log.error("导出短剧信息Excel失败", e);
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			try {
				response.setCharacterEncoding("UTF-8");
				response.getWriter().write("导出失败：" + e.getMessage());
			} catch (Exception ex) {
				log.error("写入错误响应失败", ex);
			}
		}
		return Result.error("到处文件异常");
	}

	@Resource
	IMiniAppAlbumAuditMsgService miniAppAlbumAuditMsgService;


	@GetMapping(value = "/exportAuditMsg")
	@ApiOperation(value = "导出拒审信息", notes = "导出拒审信息")
	public Result<?> exportXls() {
		List<OpenApiAlbumInfo> albumInfoList = openApiAlbumInfoService.lambdaQuery().eq(OpenApiAlbumInfo::getReviewStatus, 3).list();
		List<String> albumIdList = albumInfoList.stream().map(OpenApiAlbumInfo::getAlbumId).collect(Collectors.toList());
		List<MiniAppAlbumAuditMsg> list = miniAppAlbumAuditMsgService.lambdaQuery().eq(MiniAppAlbumAuditMsg::getAuditStatus, 3).in(MiniAppAlbumAuditMsg::getAlbumId, albumIdList).list();
		for (MiniAppAlbumAuditMsg auditMsg : list) {
			if (Objects.equals(auditMsg.getAuditStatus(), 3)) {
				auditMsg.setAuditStatusStr("审核失败");
			}
		}
		ExportParams exportParams = new ExportParams();
		Workbook workbook = ExcelExportUtil.exportExcel(exportParams, MiniAppAlbumAuditMsg.class, list);
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			workbook.write(bos);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		return Result.ok(bos.toByteArray());
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request 请求对象
	 * @return 导入结果
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	@ApiOperation(value = "导入短剧信息Excel", notes = "导入短剧信息Excel")
	public Result<?> importExcel(HttpServletRequest request) {
		log.info("开始导入短剧信息");

		try {
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

			if (fileMap.isEmpty()) {
				return Result.error("请选择要导入的Excel文件");
			}

			int successCount = 0;
			int failCount = 0;
			StringBuilder errorMsg = new StringBuilder();

			for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
				MultipartFile file = entity.getValue();

				// 验证文件
				if (file.isEmpty()) {
					return Result.error("上传的文件为空");
				}

				String fileName = file.getOriginalFilename();
				if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
					return Result.error("请上传Excel格式文件（.xlsx或.xls）");
				}

				log.info("开始处理文件: {}, 大小: {} bytes", fileName, file.getSize());

				// 设置导入参数
				ImportParams params = new ImportParams();
				params.setTitleRows(0);  // 标题行数
				params.setHeadRows(0);   // 表头行数
				params.setNeedSave(false); // 不需要保存验证失败的数据
				params.setSheetNum(1); // 从第一个sheet开始

				try {
					// 导入Excel数据
					List<MiniAppMiniDramaReq> listMiniAppMiniDrama = ExcelImportUtil.importExcel(
							file.getInputStream(),
							MiniAppMiniDramaReq.class,
							params
					);

					log.info("Excel解析完成，共读取到 {} 条数据", listMiniAppMiniDrama != null ? listMiniAppMiniDrama.size() : 0);

					if (listMiniAppMiniDrama == null || listMiniAppMiniDrama.isEmpty()) {
						return Result.error("Excel文件中没有读取到有效数据，请检查文件格式和内容");
					}

					// 调试：打印前几条数据的关键字段
					for (int i = 0; i < Math.min(3, listMiniAppMiniDrama.size()); i++) {
						MiniAppMiniDramaReq item = listMiniAppMiniDrama.get(i);
						log.info("第{}条数据调试信息 - 剧集名称: [{}], 短剧标题: [{}], 状态: [{}]",
								i + 1, item.getName(), item.getTitle(), item.getStatus());
					}

					// 处理每条数据
					for (int i = 0; i < listMiniAppMiniDrama.size(); i++) {
						MiniAppMiniDramaReq miniAppMiniDrama = listMiniAppMiniDrama.get(i);
						try {
							// 验证必填字段
							if (StringUtils.isBlank(miniAppMiniDrama.getName())) {
								failCount++;
								errorMsg.append("第").append(i + 2).append("行：剧集名称不能为空; ");
								continue;
							}

							if (StringUtils.isBlank(miniAppMiniDrama.getTitle())) {
								failCount++;
								errorMsg.append("第").append(i + 2).append("行：短剧标题不能为空; ");
								continue;
							}

							// 保存数据
							miniAppMiniDramaService.addDramaAndAlbumInfo(miniAppMiniDrama);
							successCount++;
							log.info("成功导入第{}条数据: {}", i + 1, miniAppMiniDrama.getName());

						} catch (Exception e) {
							failCount++;
							log.error("导入第{}条数据失败: {}", i + 1, miniAppMiniDrama.getName(), e);
							errorMsg.append("第").append(i + 2).append("行：").append(e.getMessage()).append("; ");
						}
					}

				} catch (Exception e) {
					log.error("Excel文件解析失败: {}", fileName, e);
					return Result.error("Excel文件解析失败：" + e.getMessage());
				}
			}

			// 返回导入结果
			String resultMsg = String.format("导入完成！成功：%d条，失败：%d条", successCount, failCount);
			if (failCount > 0) {
				resultMsg += "。失败原因：" + errorMsg.toString();
			}

			log.info("导入短剧信息完成，成功：{}条，失败：{}条", successCount, failCount);

			if (failCount > 0 && successCount == 0) {
				return Result.error(resultMsg);
			} else {
				return Result.ok(resultMsg);
			}

		} catch (Exception e) {
			log.error("导入短剧信息异常", e);
			return Result.error("导入失败：" + e.getMessage());
		}
	}
}
