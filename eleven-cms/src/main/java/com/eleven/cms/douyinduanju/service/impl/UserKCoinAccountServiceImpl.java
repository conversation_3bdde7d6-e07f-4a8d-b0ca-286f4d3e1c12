package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.UserKCoinAccount;
import com.eleven.cms.douyinduanju.entity.UserKCoinTransaction;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.mapper.UserKCoinAccountMapper;
import com.eleven.cms.douyinduanju.service.IUserKCoinAccountService;
import com.eleven.cms.douyinduanju.service.IUserKCoinTransactionService;
import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户K币账户服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class UserKCoinAccountServiceImpl extends ServiceImpl<UserKCoinAccountMapper, UserKCoinAccount>
        implements IUserKCoinAccountService {

    @Resource
    private IUserKCoinTransactionService userKCoinTransactionService;

    // 乐观锁重试次数
    private static final int MAX_RETRY_TIMES = 3;

    @Override
    public UserKCoinAccount getByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }

        UserKCoinAccount account = lambdaQuery()
                .eq(UserKCoinAccount::getUserId, userId)
                .eq(UserKCoinAccount::getIsDeleted, 0)
                .one();

        // 如果账户不存在，自动创建
        if (account == null) {
            account = createAccount(userId);
        }

        return account;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserKCoinAccount createAccount(String userId) {
        if (StringUtils.isEmpty(userId)) {
            throw new BusinessException("用户ID不能为空");
        }

        try {
            // 检查是否已存在账户
            UserKCoinAccount existingAccount = lambdaQuery()
                    .eq(UserKCoinAccount::getUserId, userId)
                    .eq(UserKCoinAccount::getIsDeleted, 0)
                    .one();

            if (existingAccount != null) {
                log.info("K币账户已存在，用户ID: {}", userId);
                return existingAccount;
            }

            // 创建新账户
            UserKCoinAccount account = new UserKCoinAccount();
            account.setUserId(userId);
            account.setAvailableBalance(BigDecimal.ZERO);
            account.setFrozenBalance(BigDecimal.ZERO);
            account.setTotalRecharge(BigDecimal.ZERO);
            account.setTotalConsume(BigDecimal.ZERO);
            account.setStatus(0); // 正常状态
            account.setLastTransactionTime(new Date());
            account.setCreateTime(new Date());
            account.setUpdateTime(new Date());
            account.setIsDeleted(0);
            account.setVersion(0);

            boolean saveResult = save(account);
            if (saveResult) {
                log.info("K币账户创建成功，用户ID: {}", userId);
                return account;
            } else {
                throw new BusinessException("K币账户创建失败");
            }

        } catch (Exception e) {
            log.error("创建K币账户异常，用户ID: {}", userId, e);
            throw new BusinessException("创建K币账户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rechargeKCoin(String userId, BigDecimal amount, String orderId, String description) {
        if (StringUtils.isEmpty(userId) || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("充值参数无效");
        }

        try {
            // 获取账户信息
            UserKCoinAccount account = getByUserId(userId);
            if (account == null) {
                throw new BusinessException("K币账户不存在");
            }

            // 记录交易前余额
            BigDecimal balanceBefore = account.getAvailableBalance();
            BigDecimal balanceAfter = balanceBefore.add(amount);

            // 使用乐观锁更新账户余额
            boolean updateResult = updateAccountBalance(userId, amount, BigDecimal.ZERO, amount, BigDecimal.ZERO, account.getVersion());

            if (updateResult) {
                // 创建交易记录
                UserKCoinTransaction transaction = userKCoinTransactionService.createTransaction(
                        userId,
                        OrderEnums.KCoinTransactionType.RECHARGE.getCode(),
                        amount,
                        balanceBefore,
                        balanceAfter,
                        description != null ? description : "K币充值",
                        OrderEnums.BusinessType.RECHARGE_KCOIN.getCode(),
                        null,
                        orderId
                );

                if (transaction != null) {
                    log.info("K币充值成功，用户ID: {}, 充值金额: {}, 订单ID: {}", userId, amount, orderId);
                    return true;
                } else {
                    log.error("K币充值交易记录创建失败，用户ID: {}, 充值金额: {}", userId, amount);
                    throw new BusinessException("交易记录创建失败");
                }
            } else {
                log.error("K币充值账户更新失败，用户ID: {}, 充值金额: {}", userId, amount);
                throw new BusinessException("账户余额更新失败");
            }

        } catch (Exception e) {
            log.error("K币充值异常，用户ID: {}, 充值金额: {}", userId, amount, e);
            throw new BusinessException("K币充值失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean consumeKCoin(String userId, BigDecimal amount, Integer businessType,
                                String businessId, String description) {
        if (StringUtils.isEmpty(userId) || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("消费参数无效");
        }

        try {
            // 获取账户信息
            UserKCoinAccount account = getByUserId(userId);
            if (account == null) {
                throw new BusinessException("K币账户不存在");
            }

            // 检查余额是否足够
            if (account.getAvailableBalance().compareTo(amount) < 0) {
                throw new BusinessException("K币余额不足");
            }

            // 记录交易前后余额
            BigDecimal balanceBefore = account.getAvailableBalance();
            BigDecimal balanceAfter = balanceBefore.subtract(amount);

            // 使用乐观锁更新账户余额（减少可用余额，增加总消费）
            boolean updateResult = updateAccountBalance(userId, amount.negate(), BigDecimal.ZERO, BigDecimal.ZERO, amount, account.getVersion());

            if (updateResult) {
                // 创建交易记录
                UserKCoinTransaction transaction = userKCoinTransactionService.createTransaction(
                        userId,
                        OrderEnums.KCoinTransactionType.CONSUME.getCode(),
                        amount.negate(), // 消费记录为负数
                        balanceBefore,
                        balanceAfter,
                        description != null ? description : "K币消费",
                        businessType,
                        businessId,
                        null
                );

                if (transaction != null) {
                    log.info("K币消费成功，用户ID: {}, 消费金额: {}, 业务类型: {}, 业务ID: {}",
                            userId, amount, businessType, businessId);
                    return true;
                } else {
                    log.error("K币消费交易记录创建失败，用户ID: {}, 消费金额: {}", userId, amount);
                    throw new BusinessException("交易记录创建失败");
                }
            } else {
                log.error("K币消费账户更新失败，用户ID: {}, 消费金额: {}", userId, amount);
                throw new BusinessException("账户余额更新失败");
            }

        } catch (Exception e) {
            log.error("K币消费异常，用户ID: {}, 消费金额: {}", userId, amount, e);
            throw new BusinessException("K币消费失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refundKCoin(String userId, BigDecimal amount, String orderId, String description) {
        if (StringUtils.isEmpty(userId) || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("退款参数无效");
        }

        try {
            // 获取账户信息
            UserKCoinAccount account = getByUserId(userId);
            if (account == null) {
                throw new BusinessException("K币账户不存在");
            }

            // 记录交易前后余额
            BigDecimal balanceBefore = account.getAvailableBalance();
            BigDecimal balanceAfter = balanceBefore.add(amount);

            // 使用乐观锁更新账户余额（增加可用余额，减少总消费）
            boolean updateResult = updateAccountBalance(userId, amount, BigDecimal.ZERO, BigDecimal.ZERO, amount.negate(), account.getVersion());

            if (updateResult) {
                // 创建交易记录
                UserKCoinTransaction transaction = userKCoinTransactionService.createTransaction(
                        userId,
                        OrderEnums.KCoinTransactionType.REFUND.getCode(),
                        amount,
                        balanceBefore,
                        balanceAfter,
                        description != null ? description : "K币退款",
                        OrderEnums.BusinessType.RECHARGE_KCOIN.getCode(),
                        null,
                        orderId
                );

                if (transaction != null) {
                    log.info("K币退款成功，用户ID: {}, 退款金额: {}, 订单ID: {}", userId, amount, orderId);
                    return true;
                } else {
                    log.error("K币退款交易记录创建失败，用户ID: {}, 退款金额: {}", userId, amount);
                    throw new BusinessException("交易记录创建失败");
                }
            } else {
                log.error("K币退款账户更新失败，用户ID: {}, 退款金额: {}", userId, amount);
                throw new BusinessException("账户余额更新失败");
            }

        } catch (Exception e) {
            log.error("K币退款异常，用户ID: {}, 退款金额: {}", userId, amount, e);
            throw new BusinessException("K币退款失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean giftKCoin(String userId, BigDecimal amount, String description) {
        if (StringUtils.isEmpty(userId) || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("赠送参数无效");
        }

        try {
            // 获取账户信息
            UserKCoinAccount account = getByUserId(userId);
            if (account == null) {
                throw new BusinessException("K币账户不存在");
            }

            // 记录交易前后余额
            BigDecimal balanceBefore = account.getAvailableBalance();
            BigDecimal balanceAfter = balanceBefore.add(amount);

            // 使用乐观锁更新账户余额（增加可用余额，不计入总充值）
            boolean updateResult = updateAccountBalance(userId, amount, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, account.getVersion());

            if (updateResult) {
                // 创建交易记录
                UserKCoinTransaction transaction = userKCoinTransactionService.createTransaction(
                        userId,
                        OrderEnums.KCoinTransactionType.GIFT.getCode(),
                        amount,
                        balanceBefore,
                        balanceAfter,
                        description != null ? description : "K币赠送",
                        OrderEnums.BusinessType.SYSTEM_GIFT.getCode(),
                        null,
                        null
                );

                if (transaction != null) {
                    log.info("K币赠送成功，用户ID: {}, 赠送金额: {}", userId, amount);
                    return true;
                } else {
                    log.error("K币赠送交易记录创建失败，用户ID: {}, 赠送金额: {}", userId, amount);
                    throw new BusinessException("交易记录创建失败");
                }
            } else {
                log.error("K币赠送账户更新失败，用户ID: {}, 赠送金额: {}", userId, amount);
                throw new BusinessException("账户余额更新失败");
            }

        } catch (Exception e) {
            log.error("K币赠送异常，用户ID: {}, 赠送金额: {}", userId, amount, e);
            throw new BusinessException("K币赠送失败: " + e.getMessage());
        }
    }

    @Override
    public boolean checkBalance(String userId, BigDecimal amount) {
        if (StringUtils.isEmpty(userId) || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        try {
            UserKCoinAccount account = getByUserId(userId);
            if (account == null) {
                return false;
            }

            return account.getAvailableBalance().compareTo(amount) >= 0;

        } catch (Exception e) {
            log.error("检查K币余额异常，用户ID: {}, 需要金额: {}", userId, amount, e);
            return false;
        }
    }

    /**
     * 使用乐观锁更新账户余额
     *
     * @param userId                 用户ID
     * @param availableBalanceChange 可用余额变化量
     * @param frozenBalanceChange    冻结余额变化量
     * @param totalRechargeChange    总充值变化量
     * @param totalConsumeChange     总消费变化量
     * @param currentVersion         当前版本号
     * @return 是否更新成功
     */
    private boolean updateAccountBalance(String userId, BigDecimal availableBalanceChange,
                                         BigDecimal frozenBalanceChange, BigDecimal totalRechargeChange,
                                         BigDecimal totalConsumeChange, Integer currentVersion) {

        int retryCount = 0;
        while (retryCount < MAX_RETRY_TIMES) {
            try {
                // 使用Mapper的乐观锁更新方法
                int updateRows = baseMapper.updateBalanceWithVersion(
                        userId, availableBalanceChange, frozenBalanceChange,
                        totalRechargeChange, totalConsumeChange, currentVersion);

                if (updateRows > 0) {
                    log.debug("账户余额更新成功，用户ID: {}, 可用余额变化: {}, 版本号: {}",
                            userId, availableBalanceChange, currentVersion);
                    return true;
                } else {
                    // 更新失败，可能是版本号冲突，重新获取账户信息
                    retryCount++;
                    if (retryCount < MAX_RETRY_TIMES) {
                        UserKCoinAccount account = getByUserId(userId);
                        if (account != null) {
                            currentVersion = account.getVersion();
                            log.warn("账户余额更新冲突，重试第{}次，用户ID: {}, 新版本号: {}",
                                    retryCount, userId, currentVersion);
                            Thread.sleep(50); // 短暂等待后重试
                        } else {
                            log.error("重试时无法获取账户信息，用户ID: {}", userId);
                            break;
                        }
                    }
                }

            } catch (Exception e) {
                log.error("更新账户余额异常，用户ID: {}, 重试次数: {}", userId, retryCount, e);
                retryCount++;
                if (retryCount < MAX_RETRY_TIMES) {
                    try {
                        Thread.sleep(100); // 等待后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("账户余额更新失败，已达到最大重试次数，用户ID: {}", userId);
        return false;
    }

    /**
     * 冻结K币
     *
     * @param userId      用户ID
     * @param amount      冻结金额
     * @param description 描述
     * @return 是否成功
     */
    public boolean freezeKCoin(String userId, BigDecimal amount, String description) {
        if (StringUtils.isEmpty(userId) || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("冻结参数无效");
        }

        try {
            UserKCoinAccount account = getByUserId(userId);
            if (account == null) {
                throw new BusinessException("K币账户不存在");
            }

            // 检查可用余额是否足够
            if (account.getAvailableBalance().compareTo(amount) < 0) {
                throw new BusinessException("可用余额不足，无法冻结");
            }

            // 减少可用余额，增加冻结余额
            boolean updateResult = updateAccountBalance(userId, amount.negate(), amount, BigDecimal.ZERO, BigDecimal.ZERO, account.getVersion());

            if (updateResult) {
                log.info("K币冻结成功，用户ID: {}, 冻结金额: {}", userId, amount);
                return true;
            } else {
                throw new BusinessException("账户余额更新失败");
            }

        } catch (Exception e) {
            log.error("K币冻结异常，用户ID: {}, 冻结金额: {}", userId, amount, e);
            throw new BusinessException("K币冻结失败: " + e.getMessage());
        }
    }

    /**
     * 解冻K币
     *
     * @param userId      用户ID
     * @param amount      解冻金额
     * @param description 描述
     * @return 是否成功
     */
    public boolean unfreezeKCoin(String userId, BigDecimal amount, String description) {
        if (StringUtils.isEmpty(userId) || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("解冻参数无效");
        }

        try {
            UserKCoinAccount account = getByUserId(userId);
            if (account == null) {
                throw new BusinessException("K币账户不存在");
            }

            // 检查冻结余额是否足够
            if (account.getFrozenBalance().compareTo(amount) < 0) {
                throw new BusinessException("冻结余额不足，无法解冻");
            }

            // 减少冻结余额，增加可用余额
            boolean updateResult = updateAccountBalance(userId, amount, amount.negate(), BigDecimal.ZERO, BigDecimal.ZERO, account.getVersion());

            if (updateResult) {
                log.info("K币解冻成功，用户ID: {}, 解冻金额: {}", userId, amount);
                return true;
            } else {
                throw new BusinessException("账户余额更新失败");
            }

        } catch (Exception e) {
            log.error("K币解冻异常，用户ID: {}, 解冻金额: {}", userId, amount, e);
            throw new BusinessException("K币解冻失败: " + e.getMessage());
        }
    }
}
