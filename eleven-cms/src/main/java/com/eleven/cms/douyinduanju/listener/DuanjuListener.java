package com.eleven.cms.douyinduanju.listener;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.douyinduanju.constant.DuanjuConstant;
import com.eleven.cms.douyinduanju.entity.Subscribe;
import com.eleven.cms.douyinduanju.service.IMiniAppDuanJuOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@RequiredArgsConstructor
@Slf4j
public class DuanjuListener {


    @Resource
    IMiniAppDuanJuOrderService duanJuOrderService;

    @RabbitListener(queues = DuanjuConstant.MINI_APP_SUBSCRIBE_EVENT_QUEUE_NAME)
    public void handlePhoneSubService(String message) {
        try {
            log.info("话费订购处理消息 json {}", message);
            Subscribe subscribe = JSONObject.parseObject(message, Subscribe.class);
            duanJuOrderService.handlePhoneSubService(subscribe);
        } catch (Exception e) {
            log.info("话费订购处理消息失败 json {}", message, e);
        }
    }
}
