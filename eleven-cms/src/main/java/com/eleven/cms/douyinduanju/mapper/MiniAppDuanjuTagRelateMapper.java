package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanjuTagRelate;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 标签和剧目关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
public interface MiniAppDuanjuTagRelateMapper extends BaseMapper<MiniAppDuanjuTagRelate> {

    /**
     * 根据剧目ID查询关联的标签列表
     *
     * @param dramaId 剧目ID
     * @return 标签关联列表
     */
    @Select("SELECT * FROM mini_app_duanju_tag_relate WHERE drama_id = #{dramaId}")
    List<MiniAppDuanjuTagRelate> selectByDramaId(@Param("dramaId") String dramaId);

    /**
     * 根据标签ID查询关联的剧目列表
     *
     * @param tagId 标签ID
     * @return 剧目关联列表
     */
    @Select("SELECT * FROM mini_app_duanju_tag_relate WHERE tag_id = #{tagId}")
    List<MiniAppDuanjuTagRelate> selectByTagId(@Param("tagId") String tagId);

    /**
     * 根据抖音剧目ID查询关联的标签列表
     *
     * @param albumId 抖音剧目ID
     * @return 标签关联列表
     */
    @Select("SELECT * FROM mini_app_duanju_tag_relate WHERE album_id = #{albumId}")
    List<MiniAppDuanjuTagRelate> selectByAlbumId(@Param("albumId") String albumId);

    /**
     * 删除剧目的所有标签关联
     *
     * @param dramaId 剧目ID
     * @return 删除数量
     */
    @Delete("DELETE FROM mini_app_duanju_tag_relate WHERE drama_id = #{dramaId}")
    int deleteByDramaId(@Param("dramaId") String dramaId);

    /**
     * 删除标签的所有剧目关联
     *
     * @param tagId 标签ID
     * @return 删除数量
     */
    @Delete("DELETE FROM mini_app_duanju_tag_relate WHERE tag_id = #{tagId}")
    int deleteByTagId(@Param("tagId") String tagId);

    /**
     * 删除特定的剧目-标签关联
     *
     * @param dramaId 剧目ID
     * @param tagId   标签ID
     * @return 删除数量
     */
    @Delete("DELETE FROM mini_app_duanju_tag_relate WHERE drama_id = #{dramaId} AND tag_id = #{tagId}")
    int deleteByDramaIdAndTagId(@Param("dramaId") String dramaId, @Param("tagId") String tagId);

    /**
     * 批量插入剧目标签关联
     *
     * @param list 关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<MiniAppDuanjuTagRelate> list);
}
