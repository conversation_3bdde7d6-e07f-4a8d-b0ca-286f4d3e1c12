package com.eleven.cms.douyinduanju.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class CoverUtilList {

    public static List<String> strCoverList(String str) {

        if (StringUtils.isEmpty(str)) {
            return Collections.emptyList();
        }
        return Arrays.asList(str.split(","));
    }

    public static List<Integer> intCoverList(String str) {

        if (StringUtils.isEmpty(str)) {
            return Collections.emptyList();
        }
        return Arrays.stream(str.split(",")).map(Integer::valueOf).collect(Collectors.toList());
    }
}
