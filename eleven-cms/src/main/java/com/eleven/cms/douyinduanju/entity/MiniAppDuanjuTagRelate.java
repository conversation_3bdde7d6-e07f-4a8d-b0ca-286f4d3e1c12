package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 标签和剧目关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
@Data
@TableName("mini_app_duanju_tag_relate")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_duanju_tag_relate对象", description = "标签和剧目关联表")
public class MiniAppDuanjuTagRelate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * mini_app_drama主键id
     */
    @Excel(name = "剧目ID", width = 15)
    @ApiModelProperty(value = "mini_app_drama主键id")
    private String dramaId;

    /**
     * 标签id
     */
    @Excel(name = "标签ID", width = 15)
    @ApiModelProperty(value = "标签id")
    private String tagId;

    /**
     * 标签名称
     */
    @Excel(name = "标签名称", width = 15)
    @ApiModelProperty(value = "标签名称")
    private String tagName;

    /**
     * 抖音剧目id
     */
    @Excel(name = "抖音剧目ID", width = 15)
    @ApiModelProperty(value = "抖音剧目id")
    private String albumId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
