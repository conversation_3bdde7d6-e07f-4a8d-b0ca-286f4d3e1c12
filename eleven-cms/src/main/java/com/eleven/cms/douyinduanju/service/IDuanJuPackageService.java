package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.DuanJuPackage;

import java.util.List;


public interface IDuanJuPackageService extends IService<DuanJuPackage> {

    List<DuanJuPackage> listPackageByProvince(String provinceName, String model, List<Integer> typeList);

    List<DuanJuPackage> getPackageByProvince(String provinceName, String model, List<Integer> typeList);

    /**
     * 更新套餐信息（包括空值字段）
     *
     * @param duanJuPackage 套餐对象
     * @return 更新行数
     */
    int updateByIdWithNull(DuanJuPackage duanJuPackage);
}
