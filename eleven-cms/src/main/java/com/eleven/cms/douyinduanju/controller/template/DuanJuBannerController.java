package com.eleven.cms.douyinduanju.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.entity.DuanJuBanner;
import com.eleven.cms.douyinduanju.service.IDuanJuBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: duan_ju_banner
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Api(tags = "duan_ju_banner")
@RestController
@RequestMapping("/douyinduanju/duanJuBanner")
@Slf4j
public class DuanJuBannerController extends JeecgController<DuanJuBanner, IDuanJuBannerService> {
    @Autowired
    private IDuanJuBannerService duanJuBannerService;

    /**
     * 分页列表查询
     *
     * @param duanJuBanner
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "duan_ju_banner-分页列表查询")
    @ApiOperation(value = "duan_ju_banner-分页列表查询", notes = "duan_ju_banner-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DuanJuBanner duanJuBanner,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DuanJuBanner> queryWrapper = QueryGenerator.initQueryWrapper(duanJuBanner, req.getParameterMap());
        Page<DuanJuBanner> page = new Page<DuanJuBanner>(pageNo, pageSize);
        IPage<DuanJuBanner> pageList = duanJuBannerService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param duanJuBanner
     * @return
     */
    @AutoLog(value = "duan_ju_banner-添加")
    @ApiOperation(value = "duan_ju_banner-添加", notes = "duan_ju_banner-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DuanJuBanner duanJuBanner) {
        duanJuBannerService.save(duanJuBanner);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param duanJuBanner
     * @return
     */
    @AutoLog(value = "duan_ju_banner-编辑")
    @ApiOperation(value = "duan_ju_banner-编辑", notes = "duan_ju_banner-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DuanJuBanner duanJuBanner) {
        duanJuBannerService.updateByIdWithNull(duanJuBanner);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "duan_ju_banner-通过id删除")
    @ApiOperation(value = "duan_ju_banner-通过id删除", notes = "duan_ju_banner-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        duanJuBannerService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "duan_ju_banner-批量删除")
    @ApiOperation(value = "duan_ju_banner-批量删除", notes = "duan_ju_banner-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.duanJuBannerService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "duan_ju_banner-通过id查询")
    @ApiOperation(value = "duan_ju_banner-通过id查询", notes = "duan_ju_banner-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DuanJuBanner duanJuBanner = duanJuBannerService.getById(id);
        if (duanJuBanner == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(duanJuBanner);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param duanJuBanner
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DuanJuBanner duanJuBanner) {
        return super.exportXls(request, duanJuBanner, DuanJuBanner.class, "duan_ju_banner");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DuanJuBanner.class);
    }

}
