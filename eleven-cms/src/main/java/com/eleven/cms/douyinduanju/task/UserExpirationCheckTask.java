package com.eleven.cms.douyinduanju.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eleven.cms.douyinduanju.entity.DuanJuUser;
import com.eleven.cms.douyinduanju.service.IDuanJuUserService;
import com.eleven.cms.douyinduanju.service.IUserMembershipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 用户过期检查定时任务
 * 定期检查DuanJuUser的过期状态，并进行相应处理
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Component
@Slf4j
public class UserExpirationCheckTask {

    @Autowired
    private IDuanJuUserService duanJuUserService;

    @Autowired
    private IUserMembershipService userMembershipService;

    /**
     * 每小时检查一次用户过期状态
     * cron表达式: 秒 分 时 日 月 周
     * 0 30 * * * ? 表示每小时的30分执行
     */
    @Scheduled(cron = "0 30 * * * ?")
    public void checkUserExpiration() {
        log.info("开始执行用户过期检查定时任务");

        try {
            long startTime = System.currentTimeMillis();

            // 执行过期检查
            int processedCount = processExpiredUsers();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("用户过期检查定时任务执行完成，处理用户数: {}，耗时: {} ms", processedCount, duration);

        } catch (Exception e) {
            log.error("用户过期检查定时任务执行异常", e);
        }
    }

    /**
     * 处理过期的用户
     *
     * @return 处理的用户数量
     */
    public int processExpiredUsers() {
        int processedCount = 0;
        int successCount = 0;
        int failCount = 0;

        try {
            // 查询所有可能过期的用户（有过期时间且过期时间小于当前时间）
            Date currentTime = new Date();
            LambdaQueryWrapper<DuanJuUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.
                    lt(DuanJuUser::getExpireTime, currentTime)
                    .in(DuanJuUser::getMemberStatus, 2, 3)
                    .eq(DuanJuUser::getIsDeleted, 0);

            List<DuanJuUser> expiredUsers = duanJuUserService.list(queryWrapper);

            if (expiredUsers.isEmpty()) {
                log.info("没有需要处理的过期用户");
                return 0;
            }

            log.info("发现 {} 个可能过期的用户需要检查", expiredUsers.size());

            // 处理每个过期用户
            for (DuanJuUser user : expiredUsers) {
                try {
                    processedCount++;

                    boolean result = userMembershipService.checkAndHandleUserExpiration(String.valueOf(user.getId()));

                    if (result) {
                        successCount++;
                        log.debug("用户过期处理成功，用户ID: {}", user.getId());
                    } else {
                        failCount++;
                        log.warn("用户过期处理失败，用户ID: {}", user.getId());
                    }

                } catch (Exception e) {
                    failCount++;
                    log.error("处理用户过期异常，用户ID: {}", user.getId(), e);
                    // 继续处理下一个用户，不影响整体流程
                }
            }

            log.info("用户过期处理完成，总计: {}, 成功: {}, 失败: {}", processedCount, successCount, failCount);

        } catch (Exception e) {
            log.error("查询过期用户异常", e);
            throw e;
        }

        return processedCount;
    }

    /**
     * 手动触发用户过期检查（用于测试或紧急处理）
     */
    public void manualCheckUserExpiration() {
        log.info("手动触发用户过期检查");
        checkUserExpiration();
    }

    @Resource
    private RestTemplate restTemplate;

    @Scheduled(cron = "0 0 0 * * ?")
    public void callApiAtMidnight() {
        String spaceId = "7533195063594058283,7533195119860646434,7533250144071615003,7533195151687025186,7533195247262630410,7533195278833975834,7533195548367520310,7533195612385182227,7533195642676445736,7533195699820462643,7533250051552838179,7533250319975711274,7533250782210621971";
        String[] split = spaceId.split(",");
        for (String param : split) {
            String url = "https://xkld.kunpengtn.com/backend-vrbt-miniapp/douyinduanju/miniAppMiniDrama/reviewDrama?albumId=" + param;
            try {
                // 发送GET请求并获取响应结9
                String response = restTemplate.getForObject(url, String.class);
                System.out.println("接口调用成功，响应结果：" + response);
            } catch (Exception e) {
                System.err.println("接口调用失败，错9误信息：" + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    int i = 0;

    @Scheduled(cron = "0 0 0 * * ?")
    public void uploadVideoOpenApiByRecord() {

        String spaceId = "";
        String[] split = spaceId.split(",");
        String url = "https://xkld.kunpengtn.com/backend-vrbt-miniapp/duanju/uploadVideoOpenApiByRecord?type=all&id=" + split[i];
        try {
            // 发送GET请求并获取响应结9
            String response = restTemplate.getForObject(url, String.class);
            System.out.println("接口调用成功，响应结果：" + response);
        } catch (Exception e) {
            System.err.println("接口调用失败，错9误信息：" + e.getMessage());
            e.printStackTrace();
        }
        i = i + 1;
    }
}
