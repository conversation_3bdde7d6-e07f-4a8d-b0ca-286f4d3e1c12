package com.eleven.cms.douyinduanju.controller.template;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.entity.DuanJuPackage;
import com.eleven.cms.douyinduanju.entity.MiniAppPayTemplate;
import com.eleven.cms.douyinduanju.entity.MiniAppPayTemplatePackageRelate;
import com.eleven.cms.douyinduanju.service.IDuanJuPackageService;
import com.eleven.cms.douyinduanju.service.IMiniAppPayTemplatePackageRelateService;
import com.eleven.cms.douyinduanju.service.IMiniAppPayTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: mini_app_pay_template
 * @Author: jeecg-boot
 * @Date: 2025-07-28
 * @Version: V1.0
 */
@Api(tags = "mini_app_pay_template")
@RestController
@ApiOperation(value = "支付面板模板-管理")
@RequestMapping("/douyinduanju/miniAppPayTemplate")
@Slf4j
public class MiniAppPayTemplateController extends JeecgController<MiniAppPayTemplate, IMiniAppPayTemplateService> {
    @Autowired
    private IMiniAppPayTemplateService miniAppPayTemplateService;

    @Resource
    private IDuanJuPackageService duanJuPackageService;

    @Resource
    private IMiniAppPayTemplatePackageRelateService duanJuPackageRelateService;

    /**
     * 分页列表查询
     *
     * @param miniAppPayTemplate
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "mini_app_pay_template-分页列表查询")
    @ApiOperation(value = "mini_app_pay_template-分页列表查询", notes = "mini_app_pay_template-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppPayTemplate miniAppPayTemplate, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<MiniAppPayTemplate> queryWrapper = QueryGenerator.initQueryWrapper(miniAppPayTemplate, req.getParameterMap());
        Page<MiniAppPayTemplate> page = new Page<MiniAppPayTemplate>(pageNo, pageSize);
        queryWrapper.eq("is_deleted", 0);
        IPage<MiniAppPayTemplate> pageList = miniAppPayTemplateService.page(page, queryWrapper);
        List<MiniAppPayTemplate> records = pageList.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (MiniAppPayTemplate record : records) {
                List<MiniAppPayTemplatePackageRelate> list = duanJuPackageRelateService.lambdaQuery().eq(MiniAppPayTemplatePackageRelate::getTemplateId, record.getId()).list();
                if (CollectionUtils.isNotEmpty(list)) {
                    List<String> packageIds = list.stream().map(MiniAppPayTemplatePackageRelate::getPackageId).collect(Collectors.toList());
                    List<DuanJuPackage> packageList = duanJuPackageService.lambdaQuery().in(DuanJuPackage::getId, packageIds).eq(DuanJuPackage::getIsDeleted, 0).list();
                    record.setDuanJuPackageList(packageList);
                }
            }
        }
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param miniAppPayTemplate
     * @return
     */
    @ApiOperation(value = "mini_app_pay_template-添加", notes = "mini_app_pay_template-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniAppPayTemplate miniAppPayTemplate) {
        miniAppPayTemplate.setId(null);
        String jsonString = JSONObject.toJSONString(miniAppPayTemplate);
        miniAppPayTemplate.setStashData(jsonString);
        log.info("保存入参:{}", jsonString);
        List<DuanJuPackage> duanJuPackageList = miniAppPayTemplate.getDuanJuPackageList();
        //清空主键
        if (CollectionUtils.isNotEmpty(duanJuPackageList)) {
            duanJuPackageList.forEach(duanJuPackage -> {
                duanJuPackage.setId(null);
            });
        }
        miniAppPayTemplateService.save(miniAppPayTemplate);
        String templateId = miniAppPayTemplate.getId();
        saveRelateAndPackage(miniAppPayTemplate, templateId);
        return Result.ok("添加成功！");
    }

    private void saveRelateAndPackage(MiniAppPayTemplate miniAppPayTemplate, String templateId) {
        List<DuanJuPackage> duanJuPackageList = miniAppPayTemplate.getDuanJuPackageList();
        if (CollectionUtils.isNotEmpty(duanJuPackageList)) {
            duanJuPackageList = duanJuPackageList.stream().map(vo -> vo.setId(null)).collect(Collectors.toList());
            duanJuPackageService.saveOrUpdateBatch(duanJuPackageList);
            for (DuanJuPackage duanJuPackage : duanJuPackageList) {
                MiniAppPayTemplatePackageRelate relate = new MiniAppPayTemplatePackageRelate();
                relate.setTemplateId(templateId);
                relate.setPackageId(duanJuPackage.getId());
                duanJuPackageRelateService.save(relate);
            }
        }
    }

    /**
     * 编辑
     *
     * @param miniAppPayTemplate
     * @return
     */
    @AutoLog(value = "mini_app_pay_template-编辑")
    @ApiOperation(value = "mini_app_pay_template-编辑", notes = "mini_app_pay_template-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniAppPayTemplate miniAppPayTemplate) {
        List<String> packageIdList = miniAppPayTemplate.getDuanJuPackageList().stream().map(DuanJuPackage::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageIdList)) {
            //删除这次入参没有的套餐
            List<MiniAppPayTemplatePackageRelate> deletePackageIds = duanJuPackageRelateService.lambdaQuery()
                    .eq(MiniAppPayTemplatePackageRelate::getTemplateId, miniAppPayTemplate.getId())
                    .notIn(MiniAppPayTemplatePackageRelate::getPackageId, packageIdList).list();
            if (CollectionUtils.isNotEmpty(deletePackageIds)) {
                List<String> ids = deletePackageIds.stream().map(MiniAppPayTemplatePackageRelate::getPackageId).collect(Collectors.toList());
                duanJuPackageService.lambdaUpdate().set(DuanJuPackage::getIsDeleted, 1).in(DuanJuPackage::getId, ids).update();
            }
        }
        //删除关联关系
        duanJuPackageRelateService.remove(new QueryWrapper<MiniAppPayTemplatePackageRelate>().eq("template_id", miniAppPayTemplate.getId()));

        //重新保存关联关系,  套餐更新或新增
        List<DuanJuPackage> duanJuPackageList = miniAppPayTemplate.getDuanJuPackageList();
        if (CollectionUtils.isNotEmpty(duanJuPackageList)) {
            duanJuPackageService.saveOrUpdateBatch(duanJuPackageList);
            for (DuanJuPackage duanJuPackage : duanJuPackageList) {
                MiniAppPayTemplatePackageRelate relate = new MiniAppPayTemplatePackageRelate();
                relate.setTemplateId(miniAppPayTemplate.getId());
                relate.setPackageId(duanJuPackage.getId());
                duanJuPackageRelateService.save(relate);
            }
        }

        miniAppPayTemplateService.updateById(miniAppPayTemplate);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_pay_template-通过id删除")
    @ApiOperation(value = "mini_app_pay_template-通过id删除", notes = "mini_app_pay_template-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniAppPayTemplateService.lambdaUpdate().set(MiniAppPayTemplate::getIsDeleted, 1).eq(MiniAppPayTemplate::getId, id).update();
        return Result.ok("删除成功!");
    }

    @GetMapping(value = "/changeStatus")
    public Result<?> changeStatus(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "status", required = true) Integer status) {
        deleteTemplate(id);
        return Result.ok("更新成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "mini_app_pay_template-批量删除")
    @ApiOperation(value = "mini_app_pay_template-批量删除", notes = "mini_app_pay_template-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {

        if (StringUtils.isNotEmpty(ids)) {
            String[] idList = ids.split(",");
            for (String id : idList) {
                deleteTemplate(id);
            }
        }
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_pay_template-通过id查询")
    @ApiOperation(value = "mini_app_pay_template-通过id查询", notes = "mini_app_pay_template-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppPayTemplate miniAppPayTemplate = miniAppPayTemplateService.getById(id);
        if (Objects.nonNull(miniAppPayTemplate)) {
            List<MiniAppPayTemplatePackageRelate> list = duanJuPackageRelateService.lambdaQuery().eq(MiniAppPayTemplatePackageRelate::getTemplateId, miniAppPayTemplate.getId()).list();
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> packageIds = list.stream().map(MiniAppPayTemplatePackageRelate::getPackageId).collect(Collectors.toList());
                List<DuanJuPackage> packageList = duanJuPackageService.lambdaQuery().in(DuanJuPackage::getId, packageIds).eq(DuanJuPackage::getIsDeleted, 0).list();
                miniAppPayTemplate.setDuanJuPackageList(packageList);
            }
        }
        if (miniAppPayTemplate == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppPayTemplate);
    }

    private void deleteTemplate(String id) {
        miniAppPayTemplateService.lambdaUpdate().set(MiniAppPayTemplate::getIsDeleted, 1).in(MiniAppPayTemplate::getId, id).update();
        List<MiniAppPayTemplatePackageRelate> deletePackageIds = duanJuPackageRelateService.lambdaQuery()
                .eq(MiniAppPayTemplatePackageRelate::getTemplateId, id).list();
        if (CollectionUtils.isNotEmpty(deletePackageIds)) {
            List<String> deleteIds = deletePackageIds.stream().map(MiniAppPayTemplatePackageRelate::getPackageId).collect(Collectors.toList());
            duanJuPackageService.lambdaUpdate().set(DuanJuPackage::getIsDeleted, 1).in(DuanJuPackage::getId, deleteIds).update();
        }
        //删除关联关系
        duanJuPackageRelateService.remove(new QueryWrapper<MiniAppPayTemplatePackageRelate>().eq("template_id", id));
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppPayTemplate
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniAppPayTemplate miniAppPayTemplate) {
        return super.exportXls(request, miniAppPayTemplate, MiniAppPayTemplate.class, "mini_app_pay_template");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppPayTemplate.class);
    }

}
