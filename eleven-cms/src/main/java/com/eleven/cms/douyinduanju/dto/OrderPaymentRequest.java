package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 订单支付请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@ApiModel(value = "订单支付请求", description = "订单支付请求参数")
public class OrderPaymentRequest {

    @ApiModelProperty(value = "订单号", required = true)
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @ApiModelProperty(value = "支付方式: 1抖音支付 2话费支付", required = true)
    @NotNull(message = "支付方式不能为空")
    private Integer payType;

    @ApiModelProperty(value = "支付密码（话费支付时需要）")
    private String payPassword;

    @ApiModelProperty(value = "验证码（话费支付时需要）")
    private String verifyCode;

    @ApiModelProperty(value = "客户端IP")
    private String clientIp;

    @ApiModelProperty(value = "设备信息")
    private String deviceInfo;

    @ApiModelProperty(value = "扩展参数（JSON格式）")
    private String extParams;
}
