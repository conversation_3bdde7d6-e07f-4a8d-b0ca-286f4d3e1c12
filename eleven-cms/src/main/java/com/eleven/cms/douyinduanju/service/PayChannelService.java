package com.eleven.cms.douyinduanju.service;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.eleven.cms.util.JacksonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;

@Slf4j
@Service
public class PayChannelService {

    public List<ChannelSubResult> queryAllServiceByMobile(String mobile, List<String> channel) {
        QueryChannelResult parsedObject = null;
        try {
            //前置校验  获取能开通的渠道
            HashMap<Object, Object> map = new HashMap<>();
            map.put("channel", channel);
            map.put("mobile", mobile);
            HttpUrl url = HttpUrl.parse("https://crbt.kunpengtn.com/cms-vrbt/api/queryAllServiceByMobile");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(null, JacksonUtils.toJson(map));
            Headers headers = new Headers.Builder()
                    .add("Content-Type", "application/json").build();

            String resultV2 = HttpUtils.post(COMMON_CLIENT, url, body, headers, "查询订购的渠道号", "查询订购的渠道号");
            parsedObject = JSONObject.parseObject(resultV2, QueryChannelResult.class);
            return parsedObject.result;
        } catch (Exception e) {
            log.info("查询能开通的渠道号失败,手机号:{},channel:{}", mobile, channel, e);

        }
        return Collections.emptyList();
    }

    public JSONObject getCanSubChannel(String mobile, List<String> channel) {
        try {
            //前置校验  获取能开通的渠道
            log.info("查询能开通的渠道号,手机号:{},channel:{}", mobile, channel);
            HashMap<Object, Object> map = new HashMap<>();
            map.put("subChannel", channel);
            map.put("mobile", mobile);
            HttpUrl url = HttpUrl.parse("https://crbt.kunpengtn.com/cms-vrbt/api/outside/filerCheck/v2");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(null, JacksonUtils.toJson(map));
            Headers headers = new Headers.Builder()
                    .add("Content-Type", "application/json").build();

            String resultV2 = HttpUtils.post(COMMON_CLIENT, url, body, headers, "查询能开通的渠道号", "查询能开通的渠道号");
            Result result = JSONObject.parseObject(resultV2, Result.class);
            log.info("查询能开通的渠道号结果mobile:{},{}", mobile, resultV2);
            return JSONObject.parseObject(result.getResult().toString());
        } catch (Exception e) {
            log.info("查询能开通的渠道号失败,手机号:{},channel:{}", mobile, channel, e);
        }
        return new JSONObject();
    }

    @Data
    public static class QueryChannelResult {
        private String code;
        private String message;
        private String success;
        private List<ChannelSubResult> result;
    }

    @Data
    public static class ChannelSubResult {
        private String bizType;
        private String province;
        private String city;
        private String isp;
        private String mobile;
        private String channel;
        private String orderStatus;
        private String serviceId;
        private Date createTime;
    }
}
