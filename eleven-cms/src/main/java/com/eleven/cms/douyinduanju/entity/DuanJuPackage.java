package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: duan_ju_package
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Data
@TableName("duan_ju_package")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "duan_ju_package对象", description = "duan_ju_package")
public class DuanJuPackage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 套餐名称
     */
    @Excel(name = "套餐名称", width = 15)
    @ApiModelProperty(value = "套餐名称")
    private String name;
    /**
     * 套餐类型: 1会员  2:剧卡
     */
    @Excel(name = "套餐类型: 1会员  2:剧卡", width = 15)
    @ApiModelProperty(value = "套餐类型: 1会员  2:剧卡 3:K币")
    private Integer type;
    /**
     * 付费方式: 1话费支付  2:抖音支付
     */
    private Integer payType;

    private Integer underlineFlag;

    /**
     * 权益描述
     */
    private String rightDesc;

    /**
     * 赠送k币数
     */
    private Integer complimentaryNum;


    /**
     * 底部标题
     */
    private String bottomHeading;


    /**
     * 充值k币数
     */
    private Integer rechargeAmount;

    /**
     * 商品链接
     */
    private String productLink;

    /**
     * 渠道号
     */

    private String channelNo;

    /**
     * 1:PJ 0:非PJ
     */
    private String pjFlag;

    /**
     * 渠道类型 订阅包 渠道包
     */
    @Excel(name = "渠道类型", width = 15)
    @ApiModelProperty(value = "渠道类型")
    private String channelType;

    /**
     * 全国代码
     */
    @Excel(name = "全国代码", width = 15)
    @ApiModelProperty(value = "全国代码")
    private Integer countryCodeFlag;


    /**
     * 支持省份
     */
    @Excel(name = "支持省份", width = 15)
    @ApiModelProperty(value = "支持省份")
    private String supportProvince;


    /**
     * 套餐原价
     */
    @Excel(name = "套餐原价", width = 15)
    @ApiModelProperty(value = "套餐原价")
    private BigDecimal originalPrice;
    /**
     * 实际价格
     */
    @Excel(name = "实际价格", width = 15)
    @ApiModelProperty(value = "实际价格")
    private BigDecimal actualPrice;

    /**
     * k币比例
     */
    private Integer kbRatio;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderNo;
    /**
     * 1:上架 0:下架
     */
    @Excel(name = "1:上架 0:下架", width = 15)
    @ApiModelProperty(value = "1:上架 0:下架")
    private Integer status;

    private Integer isDeleted;
    /**
     * 套餐描述
     */
    @Excel(name = "套餐描述", width = 15)
    @ApiModelProperty(value = "套餐描述")
    private String description;
    /**
     * 标签文案内容
     */
    @Excel(name = "标签文案内容", width = 15)
    @ApiModelProperty(value = "标签文案内容")
    private String labelContent;
    /**
     * 角标
     */
    @Excel(name = "角标", width = 15)
    @ApiModelProperty(value = "角标")
    private String icon;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    private String orderPayLink;

    /**
     * 模型 ios, android
     */
    private String model;
    /**
     * 会员天数
     */
    private Integer durationDays;

    private Integer showPriceFlag;

    /**
     * 关联支付渠道配置id    AppMobilePayChannelConfig 的主键
     */
    private String appMobilePayLinkConfigId;

    /**
     * 页面类型 0-破解页 1-非破解
     */
    private Integer pageType;

    /**
     * 配置项排序
     */
    private Integer payLinkConfigOrderNo;

    /**
     * 引导去充值 0-否 1-是
     */
    private Integer guideToRechargeFlag;
}
