package com.eleven.cms.douyinduanju.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaColumn;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: mini_app_drama_column
 * @Author: jeecg-boot
 * @Date: 2025-05-23
 * @Version: V1.0
 */
@Api(tags = "mini_app_drama_column")
@RestController
@RequestMapping("/douyinduanju/miniAppDramaColumn")
@Slf4j
public class MiniAppDramaColumnController extends JeecgController<MiniAppDramaColumn, IMiniAppDramaColumnService> {
    @Autowired
    private IMiniAppDramaColumnService miniAppDramaColumnService;

    /**
     * 分页列表查询
     *
     * @param miniAppDramaColumn
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "mini_app_drama_column-分页列表查询")
    @ApiOperation(value = "mini_app_drama_column-分页列表查询", notes = "mini_app_drama_column-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppDramaColumn miniAppDramaColumn,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<MiniAppDramaColumn> queryWrapper = QueryGenerator.initQueryWrapper(miniAppDramaColumn, req.getParameterMap());
        Page<MiniAppDramaColumn> page = new Page<MiniAppDramaColumn>(pageNo, pageSize);
        IPage<MiniAppDramaColumn> pageList = miniAppDramaColumnService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param miniAppDramaColumn
     * @return
     */
    @AutoLog(value = "mini_app_drama_column-添加")
    @ApiOperation(value = "mini_app_drama_column-添加", notes = "mini_app_drama_column-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniAppDramaColumn miniAppDramaColumn) {
        miniAppDramaColumnService.save(miniAppDramaColumn);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miniAppDramaColumn
     * @return
     */
    @AutoLog(value = "mini_app_drama_column-编辑")
    @ApiOperation(value = "mini_app_drama_column-编辑", notes = "mini_app_drama_column-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniAppDramaColumn miniAppDramaColumn) {
        miniAppDramaColumnService.updateById(miniAppDramaColumn);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_drama_column-通过id删除")
    @ApiOperation(value = "mini_app_drama_column-通过id删除", notes = "mini_app_drama_column-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniAppDramaColumnService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "mini_app_drama_column-批量删除")
    @ApiOperation(value = "mini_app_drama_column-批量删除", notes = "mini_app_drama_column-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.miniAppDramaColumnService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_app_drama_column-通过id查询")
    @ApiOperation(value = "mini_app_drama_column-通过id查询", notes = "mini_app_drama_column-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppDramaColumn miniAppDramaColumn = miniAppDramaColumnService.getById(id);
        if (miniAppDramaColumn == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppDramaColumn);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppDramaColumn
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniAppDramaColumn miniAppDramaColumn) {
        return super.exportXls(request, miniAppDramaColumn, MiniAppDramaColumn.class, "mini_app_drama_column");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppDramaColumn.class);
    }

}
