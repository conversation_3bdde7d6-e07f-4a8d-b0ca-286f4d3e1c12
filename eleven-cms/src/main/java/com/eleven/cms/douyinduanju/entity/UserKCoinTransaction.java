package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户K币交易记录表
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@TableName("mini_app_user_kcoin_transaction")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户K币交易记录", description = "用户K币交易记录信息")
public class UserKCoinTransaction implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 交易流水号
     */
    @Excel(name = "交易流水号", width = 15)
    @ApiModelProperty(value = "交易流水号")
    private String transactionNo;

    /**
     * 关联订单id
     */
    @Excel(name = "关联订单id", width = 15)
    @ApiModelProperty(value = "关联订单id")
    private String orderId;

    /**
     * 交易类型: 1充值 2消费 3退款 4赠送 5过期扣除
     */
    @Excel(name = "交易类型", width = 15)
    @ApiModelProperty(value = "交易类型: 1充值 2消费 3退款 4赠送 5过期扣除")
    private Integer transactionType;

    /**
     * 交易金额（正数为收入，负数为支出）
     */
    @Excel(name = "交易金额", width = 15)
    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * 交易前余额
     */
    @Excel(name = "交易前余额", width = 15)
    @ApiModelProperty(value = "交易前余额")
    private BigDecimal balanceBefore;

    /**
     * 交易后余额
     */
    @Excel(name = "交易后余额", width = 15)
    @ApiModelProperty(value = "交易后余额")
    private BigDecimal balanceAfter;

    /**
     * 交易描述
     */
    @Excel(name = "交易描述", width = 15)
    @ApiModelProperty(value = "交易描述")
    private String description;

    /**
     * 业务类型: 1购买剧集 2购买会员 3充值K币 4系统赠送
     */
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型: 1购买剧集 2购买会员 3充值K币 4系统赠送")
    private Integer businessType;

    /**
     * 业务关联id（如剧集id、会员套餐id等）
     */
    @Excel(name = "业务关联id", width = 15)
    @ApiModelProperty(value = "业务关联id")
    private String businessId;

    /**
     * 交易状态: 0处理中 1成功 2失败 3已撤销
     */
    @Excel(name = "交易状态", width = 15)
    @ApiModelProperty(value = "交易状态: 0处理中 1成功 2失败 3已撤销")
    private Integer status;

    /**
     * 交易时间
     */
    @Excel(name = "交易时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交易时间")
    private Date transactionTime;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
}
