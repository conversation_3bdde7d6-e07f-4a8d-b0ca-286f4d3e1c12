package com.eleven.cms.douyinduanju.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlbumInfo {

    @JsonProperty("title")
    private String title;

    @JsonProperty("album_id")
    private String albumId;

    @JsonProperty("seq_num")
    private Integer seqNum;

    @JsonProperty("qualification")
    private Integer qualification;

    @JsonProperty("cover_list")
    private List<String> coverList;

    @JsonProperty("year")
    private Integer year;

    @JsonProperty("album_status")
    private Integer albumStatus;

    @JsonProperty("recommendation")
    private String recommendation;

    @JsonProperty("tag_list")
    private List<Integer> tagList;

    @JsonProperty("desp")
    private String desp;

    @JsonProperty("record_info")
    private RecordInfo recordInfo;

    @JsonProperty("record_audit_info")
    private RecordAuditInfo recordAuditInfo;

    @JsonProperty("episode_info_list")
    private List<EpisodeInfo> episodeInfoList;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecordAuditInfo {
        @JsonProperty("record_material")
        private RecordMaterial recordMaterial;

        @JsonProperty("broadcast_record_info")
        private BroadcastRecordInfo broadcastRecordInfo;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecordInfo {
        @JsonProperty("license_num")
        private String licenseNum;

        @JsonProperty("registration_num")
        private String registrationNum;

        @JsonProperty("ordinary_record_num")
        private String ordinaryRecordNum;

        @JsonProperty("key_record_num")
        private String keyRecordNum;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecordMaterial {
        @JsonProperty("name")
        private String name;

        @JsonProperty("duration")
        private Integer duration;

        @JsonProperty("seqs_count")
        private Integer seqsCount;

        @JsonProperty("production_organisation")
        private String productionOrganisation;

        @JsonProperty("director")
        private List<String> director;

        @JsonProperty("producer")
        private List<String> producer;

        @JsonProperty("actor")
        private List<String> actor;

        @JsonProperty("screen_writer")
        private List<String> screenWriter;

        @JsonProperty("summary")
        private String summary;

        @JsonProperty("cost_distribution_uri")
        private String costDistributionUri;

        @JsonProperty("assurance_uri")
        private String assuranceUri;

        @JsonProperty("playlet_production_cost")
        private Integer playletProductionCost;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BroadcastRecordInfo {
        @JsonProperty("record_type")
        private Integer recordType;

        @JsonProperty("broadcast_record_number")
        private String broadcastRecordNumber;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EpisodeInfo{
        @JsonProperty("title")
        private String title;

        @JsonProperty("seq")
        private Integer seq;

        @JsonProperty("cover_list")
        private List<String> coverList;

        @JsonProperty("open_video_id")
        private String openVideoId;
        public static List<Map<String, Object>> coverMap(List<EpisodeInfo> episodeInfoList) {
            List<Map<String,Object>> mapList = new ArrayList<>();
            for (EpisodeInfo episodeInfo : episodeInfoList) {
                Map<String, Object> map = new HashMap<>();
                map.put("title", episodeInfo.getTitle());
                map.put("seq", episodeInfo.getSeq());
                map.put("open_video_id", episodeInfo.getOpenVideoId());
                map.put("cover_list", episodeInfo.getCoverList());
                mapList.add(map);
            }
            return mapList;
        }
    }
}
