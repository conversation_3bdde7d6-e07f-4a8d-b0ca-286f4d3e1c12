package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.dto.*;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.entity.Subscribe;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: miniApp_duan_ju_order
 * @Author: jeecg-boot
 * @Date: 2025-06-06
 * @Version: V1.0
 */
public interface IMiniAppDuanJuOrderService extends IService<MiniAppDuanJuOrder> {

    MiniAppDuanJuOrder getByOrderNo(String orderNo);

    void updateOrderPayStatus(String orderNo, String transactionId, Date payTime);

    PaymentSignatureRes createOrder(OrderReq orderReq, Integer userId);

    void handleExternalPhoneSubService(ExternalMonthlySubCallbackDTO dto);

    Boolean refreshSubscribeStatus(RefreshStatusDTO dto);

    /**
     * 生成抖音支付签名参数
     * 根据套餐类型生成对应的商品类型：剧集商品-404，整剧商品-405
     *
     * @param packageId  套餐ID
     * @param userId     用户ID
     * @param albumId    短剧ID（可选，用于剧集商品）
     * @param episodeIds 集ID列表（可选，用于剧集商品）
     * @return 包含data和byteAuthorization的Map
     */
    Map<String, String> generatePaymentSignature(String packageId, String userId, String albumId, List<String> episodeIds);

    /**
     * 创建退款订单
     *
     * @param orderNo      原订单号
     * @param refundAmount 退款金额（分）
     * @param refundReason 退款原因
     * @param businessType 业务类型
     * @return 退款结果
     */
    RefundResponse createRefund(String orderNo, Integer refundAmount, Map<String, Object> refundReason, String businessType);

    /**
     * 处理退款回调
     *
     * @param refundNotifyMsg 退款回调消息
     * @return 处理结果
     */
    boolean handleRefundCallback(RefundNotifyMsg refundNotifyMsg);

    /**
     * 处理抖音支付回调
     * 退款后回收权益
     */
    void handleBenefitRevocation(MiniAppDuanJuOrder order);
    /**
     * 处理抖音支付回调
     *
     * @param callbackMsg 支付回调消息
     * @return 处理结果
     */
    boolean handlePaymentCallback(PaymentCallbackMsg callbackMsg);

    void handlePhoneSubService(Subscribe subscribe);


}
