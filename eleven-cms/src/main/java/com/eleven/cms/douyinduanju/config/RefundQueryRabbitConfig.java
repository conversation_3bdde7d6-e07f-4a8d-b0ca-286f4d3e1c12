package com.eleven.cms.douyinduanju.config;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 退款查询RabbitMQ配置
 * 配置延迟队列用于退款状态查询
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Configuration
public class RefundQueryRabbitConfig {


    // 队列名称
    public static final String REFUND_QUERY_QUEUE = "refund.query.queue";

    /**
     * 绑定死信队列
     */
    @Bean
    public Queue refundQuerQueue() {
        return QueueBuilder.durable(REFUND_QUERY_QUEUE).build();
    }
}
