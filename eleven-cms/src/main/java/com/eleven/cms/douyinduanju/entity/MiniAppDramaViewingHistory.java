package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: mini_app_drama_viewing_history
 * @Author: jeecg-boot
 * @Date: 2025-06-03
 * @Version: V1.0
 */
@Data
@TableName("mini_app_drama_viewing_history")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_drama_viewing_history对象", description = "mini_app_drama_viewing_history")
public class MiniAppDramaViewingHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private Integer userId;
    /**
     * 短剧主键
     */
    @Excel(name = "短剧主键", width = 15)
    @ApiModelProperty(value = "短剧主键")
    private String dramaId;

    /**
     * 抖音开放平台剧目id
     */
    private String albumId;
    private String albumName;
    /**
     * 集数编号
     */
    @Excel(name = "集数编号", width = 15)
    @ApiModelProperty(value = "集数编号")
    private Integer episodeNum;
    /**
     * 观看视频时间
     */
    @Excel(name = "观看视频时间", width = 15)
    @ApiModelProperty(value = "观看视频时间")
    private Integer watchDuration;
    /**
     * 剧集id
     */
    @Excel(name = "剧集id", width = 15)
    @ApiModelProperty(value = "剧集id")
    private String douYinEpisodeId;

    private String coverUrl;

    private String openId;

    private Integer seqCount;

    private Integer isDeleted;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
