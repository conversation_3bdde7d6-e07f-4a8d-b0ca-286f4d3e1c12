package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eleven.cms.douyinduanju.dto.AlbumInfVO;
import com.eleven.cms.douyinduanju.dto.DramaReq;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: mini_app_mini_drama
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
public interface MiniAppMiniDramaMapper extends BaseMapper<MiniAppMiniDrama> {

    //    IPage<MiniAppMiniDramaVO> pageDramaByColumnId(IPage<MiniAppMiniDramaVO> page, String columnId);
    IPage<AlbumInfVO> pageDrama(IPage<AlbumInfVO> page, DramaReq req);

    List<MiniAppMiniDrama> listDrama(String columnId, List<String> ids);

    IPage<MiniAppMiniDrama> listHostDrama(IPage<MiniAppMiniDrama> page, String columnId, List<String> ids);

    /**
     * 更新短剧信息（包括空值字段）
     *
     * @param miniAppMiniDrama 短剧对象
     * @return 更新行数
     */
    int updateByIdWithNull(@Param("drama") MiniAppMiniDrama miniAppMiniDrama);

}
