package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户会员权益表
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@TableName("mini_app_user_membership")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户会员权益", description = "用户会员权益信息")
public class UserMembership implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 订单id
     */
    @Excel(name = "订单id", width = 15)
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * 会员类型: 1普通会员
     */
    @Excel(name = "会员类型", width = 15)
    @ApiModelProperty(value = "会员类型: 1:话费会员 4:三方会员")
    private Integer membershipType;

    /**
     * 会员等级名称
     */
    @Excel(name = "会员等级名称", width = 15)
    @ApiModelProperty(value = "会员等级名称")
    private String membershipName;

    /**
     * 开始时间
     */
    @Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 到期时间
     */
    @Excel(name = "到期时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "到期时间")
    private Date expireTime;

    /**
     * 状态: 0未生效 1生效中 2已过期 3已取消
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态: 0未生效 1生效中 2已过期 3已取消")
    private Integer status;

    private String packageId;
    private String packageName;

    /**
     * 会员时长（天数）
     */
    @Excel(name = "会员时长（天数）", width = 15)
    @ApiModelProperty(value = "会员时长（天数）")
    private Integer durationDays;

    /**
     * 是否自动续费: 0否 1是
     */
    @Excel(name = "是否自动续费", width = 15)
    @ApiModelProperty(value = "是否自动续费: 0否 1是")
    private Integer autoRenew;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 是否删除: 0否 1是
     */
    @ApiModelProperty(value = "是否删除: 0否 1是")
    private Integer isDeleted;
}
