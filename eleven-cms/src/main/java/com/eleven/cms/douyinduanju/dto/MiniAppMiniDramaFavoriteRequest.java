package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * MiniAppMiniDrama收藏记录请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(value = "MiniAppMiniDrama收藏记录请求", description = "MiniAppMiniDrama收藏记录请求参数")
public class MiniAppMiniDramaFavoriteRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 收藏记录ID（更新时使用）
     */
    @ApiModelProperty(value = "收藏记录ID（更新时使用）")
    private String id;


    /**
     * 短剧ID
     */
    @NotBlank(message = "抖音开放平台短剧ID不能为空")
    @ApiModelProperty(value = "短剧ID", required = true)
    private String albumId;

    private String douYinEpisodeId;


}
