package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.douyinduanju.entity.NasFileRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * NAS文件记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface NasFileRecordMapper extends BaseMapper<NasFileRecord> {

    /**
     * 批量插入文件记录
     *
     * @param records 文件记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("records") List<NasFileRecord> records);

    /**
     * 根据扫描批次删除记录
     *
     * @param scanBatch 扫描批次号
     * @return 删除数量
     */
    int deleteByScanBatch(@Param("scanBatch") String scanBatch);

    /**
     * 根据路径前缀删除记录
     *
     * @param pathPrefix 路径前缀
     * @return 删除数量
     */
    int deleteByPathPrefix(@Param("pathPrefix") String pathPrefix);
}
