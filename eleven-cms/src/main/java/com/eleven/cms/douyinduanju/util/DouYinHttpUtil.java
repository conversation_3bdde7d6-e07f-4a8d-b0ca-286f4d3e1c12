package com.eleven.cms.douyinduanju.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.douyinduanju.dto.AlbumInfo;
import com.eleven.cms.douyinduanju.dto.MiniAppDramaEpisodeResponse;
import com.eleven.cms.douyinduanju.dto.OpenApiRes;
import com.eleven.cms.douyinduanju.dto.UploadVideoParam;
import com.eleven.cms.douyinduanju.entity.OpenApiUploadVideoRecord;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaEpisodeService;
import com.eleven.cms.douyinduanju.service.IOpenApiUploadVideoRecordService;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.service.IDouyinAppConfigService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import java.io.IOException;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

@Slf4j
@Service
public class DouYinHttpUtil {

    // 移除硬编码的敏感信息，改为从数据库获取
    private static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    // Token缓存过期时间常量
    private static final int TOKEN_EXPIRE_SECONDS = 60 * 60 * 2;
    private static final String TOKEN_URL = "https://open.douyin.com/oauth/client_token/";
    private static final String RESOURCE_UPLOAD_URL = "https://open.douyin.com/api/playlet/v2/resource/upload/";  //上传抖音短剧资源
    private static final String VIDEO_UPLOAD_QUERY = "https://open.douyin.com/api/playlet/v2/video/query/";  //查询上传状态
    private static final String DUAN_JU_CREATE = "https://open.douyin.com/api/playlet/v2/video/create/";  //查询上传状态
    private static final String DUAN_JU_AUTHORIZE = "https://open.douyin.com/api/playlet/v2/auth/authorize/";  //短剧授权
    private static final String DUAN_JU_REVIEW = "https://open.douyin.com/api/playlet/v2/video/review/";  //短剧送审
    private static final String DUAN_JU_EDIT = "https://open.douyin.com/api/playlet/v2/video/edit/";  //新增或编辑
    private static final String DUAN_JU_FETCH = "https://open.douyin.com/api/playlet/v2/album/fetch/";  //查询短剧信息
    private static final String DUAN_JU_PLAY_INFO = "https://open.douyin.com/api/playlet/v2/video/play_info/";  //获取播放链接
    private static final String DUAN_JU_ONLINE = "https://open.douyin.com/api/playlet/v2/album/online/";  //短剧上下线
    private static final String GET_COUPON = "https://open.douyin.com/api/promotion/v1/coupon/get_coupon_receive_info/";  //查询用户优惠券
    private static final String ConsumeCoupon = "https://open.douyin.com/api/promotion/v1/coupon/batch_consume_coupon/";  //查询用户优惠券
    private static final String GET_OPEN_ID_URL = "https://developer.toutiao.com/api/apps/v2/jscode2session";  //获取用户openId
    private static final String GET_PHONE_NUMBER_INFO = "https://open.douyin.com/api/apps/v1/get_phonenumber_info/";  //获取用户手机号
    private static final String QUERY_ORDER_URL = "https://open.douyin.com/api/trade_basic/v1/developer/order_query/";  //查询订单
    public static final String generateSchemaUrl = "https://open.douyin.com/api/apps/v1/url/generate_schema/";
    private static final String QUERY_BINDED_USER_URL = "https://open.douyin.com/api/promotion/v2/activity/query_binded_user/";  //查询用户是否领取过
    private static final String BATCH_ROLLBACK_CONSUME_COUPON_URL = "https://open.douyin.com/api/promotion/v1/coupon/batch_rollback_consume_coupon/";  //撤销核销

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IDouyinAppConfigService douYinAppConfigService;

    /**
     * 获取应用配置
     *
     * @param businessType 业务类型，如果为null则获取默认配置
     * @return DouyinAppConfig
     */
    private DouyinAppConfig getAppConfig(String businessType) {
        DouyinAppConfig appConfig;
        if (StringUtils.isNotEmpty(businessType)) {
            appConfig = douYinAppConfigService.getByBusinessType(businessType);
        } else {
            appConfig = douYinAppConfigService.getDefaultConfig();
        }

        if (appConfig == null) {
            throw new BusinessException("未找到对应的小程序配置，businessType: " + businessType);
        }

        return appConfig;
    }

    public OpenApiRes queryBindUser(String activityId, String openId) {
        return queryBindUser(activityId, openId, null);
    }

    public OpenApiRes queryBindUser(String activityId, String openId, String businessType) {
        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("activity_id", activityId);
        paramBody.put("open_id", openId);
        try {
            String result = doPost(paramBody, QUERY_BINDED_USER_URL, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("查询绑定用户失败,activityId:{},openId:{}", activityId, openId, e);
        }
        return OpenApiRes.fail("查询绑定用户异常");
    }
    public String getToken() {
        return getToken(null);
    }

    public String getToken(String businessType) {
        DouyinAppConfig appConfig = getAppConfig(businessType);
        String redisKey = "miniApp:duanju:douyin_token:" + (businessType != null ? businessType : "default");
        String token = (String) redisUtil.get(redisKey);
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }

        JSONObject paramBody = new JSONObject();
        paramBody.put("client_key", appConfig.getAppId());
        paramBody.put("client_secret", appConfig.getAppSecret());
        paramBody.put("grant_type", "client_credential");
        try {
            String result = doPost(paramBody, TOKEN_URL);
            OpenApiRes openApiRes = JSONObject.parseObject(result, OpenApiRes.class);
            if (openApiRes.getTokenIsSuccess()) {
                return openApiRes.getToken();
            }
        } catch (Exception e) {
            log.error("获取token失败，发生未知异常", e);
            throw new BusinessException("获取token失败");
        }
        return "";
    }


    /**
     * 获取用户的openId
     */
    public OpenApiRes getOpenId(String code) {
        return getOpenId(code, null);
    }

    public OpenApiRes getOpenId(String code, String businessType) {
        if (StringUtils.isEmpty(code)) {
            throw new BusinessException("code不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("appid", appConfig.getAppId());
        paramBody.put("code", code);
        paramBody.put("secret", appConfig.getAppSecret());
        try {
            String result = doPost(paramBody, GET_OPEN_ID_URL);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("获取openId失败,code:{}", code, e);
            throw new BusinessException("获取openId失败");
        }
    }


    public OpenApiRes getPhoneNumberInfo(String code, String businessType) {
        if (StringUtils.isEmpty(code)) {
            throw new BusinessException("code不能为空");
        }
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("code", code);
        try {
            String result = doPost(paramBody, GET_PHONE_NUMBER_INFO, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("获取手机号失败,code:{}", code, e);
            return OpenApiRes.fail("获取手机号异常");
        }
    }


    /**
     * 查询用户是否有优惠券
     */
    public OpenApiRes queryCoupon(String openId) {
        return queryCoupon(openId, null);
    }

    public OpenApiRes queryCoupon(String openId, String businessType) {
        if (StringUtils.isEmpty(openId)) {
            throw new BusinessException("openId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("app_id", appConfig.getAppId());
        paramBody.put("open_id", openId);
        Map<String, Object> couponStatusMap = new HashMap<>();
        couponStatusMap.put("ToBeUsed", 10);
        paramBody.put("coupon_status", 10);
        try {
            String result = doPost(paramBody, GET_COUPON, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("查询优惠券失败,openId:{}", openId, e);
            return OpenApiRes.builder().errNo(-1).errMsg("查询异常").build();
        }
    }


    public OpenApiRes rollbackConsumeCoupon(String consumeOutNo, String openId, String couponId, String orderId) {
        return rollbackConsumeCoupon(consumeOutNo, openId, couponId, orderId, null);
    }

    public OpenApiRes rollbackConsumeCoupon(String consumeOutNo, String openId, String couponId, String orderId, String businessType) {
        if (StringUtils.isEmpty(consumeOutNo) || StringUtils.isEmpty(openId) || StringUtils.isEmpty(couponId)) {
            throw new BusinessException("参数不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("app_id", appConfig.getAppId());
        paramBody.put("open_id", openId);
        paramBody.put("consume_out_no", consumeOutNo);
        paramBody.put("order_id", orderId);
        paramBody.put("rollback_consume_out_no", consumeOutNo);
        paramBody.put("rollback_consume_time", System.currentTimeMillis());
        Map<String, Object> couponIdList = new HashMap<>();
        couponIdList.put("coupon_id", couponId);
        paramBody.put("coupon_id_list", couponIdList);
        try {
            String result = doPost(paramBody, BATCH_ROLLBACK_CONSUME_COUPON_URL, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("撤销核销优惠券失败,consumeOutNo:{},openId:{},couponId:{}", consumeOutNo, openId, couponId, e);
            return OpenApiRes.builder().errNo(-1).errMsg("撤销核销异常").build();
        }
    }

    /**
     * 核销优惠券
     */
    public OpenApiRes consumeCoupon(String consumeOutNo, String openId, String couponId) {
        return consumeCoupon(consumeOutNo, openId, couponId, null);
    }

    public OpenApiRes consumeCoupon(String consumeOutNo, String openId, String couponId, String businessType) {
        if (StringUtils.isEmpty(consumeOutNo) || StringUtils.isEmpty(openId) || StringUtils.isEmpty(couponId)) {
            throw new BusinessException("参数不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("app_id", appConfig.getAppId());
        paramBody.put("open_id", openId);
        paramBody.put("consume_time", System.currentTimeMillis() / 1000);
        paramBody.put("consume_out_no", consumeOutNo);

        List<String> couponIdList = new ArrayList<>();
        couponIdList.add(couponId);
        paramBody.put("coupon_id_list", couponIdList);
        try {
            String result = doPost(paramBody, ConsumeCoupon, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("核销优惠券失败,consumeOutNo:{},openId:{},couponId:{}", consumeOutNo, openId, couponId, e);
            throw new BusinessException("核销优惠券失败");
        }
    }


    /**
     * 调用抖音开放平台创建短剧
     */
    public OpenApiRes createDuanJu(AlbumInfo albumInfo) {
        return createDuanJu(albumInfo, null);
    }

    public OpenApiRes createDuanJu(AlbumInfo albumInfo, String businessType) {
        if (albumInfo == null) {
            throw new BusinessException("短剧信息不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("album_info", albumInfo);
        try {
            String result = doPost(paramBody, DUAN_JU_CREATE, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("创建短剧失败{}", JSONObject.toJSONString(albumInfo), e);
            throw new BusinessException("创建短剧失败");
        }
    }

    /**
     * 编辑短剧信息
     */
    public String editAlbumInfo(AlbumInfo albumInfo) {
        return editAlbumInfo(albumInfo, null);
    }

    @Resource
    IMiniAppDramaEpisodeService miniAppDramaEpisodeService;

    @Resource
    IOpenApiUploadVideoRecordService uploadVideoRecordService;

    public String editAlbumInfo(AlbumInfo albumInfo, String businessType) {
        if (albumInfo == null || StringUtils.isEmpty(albumInfo.getAlbumId())) {
            throw new BusinessException("短剧信息或albumId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());

        try {
            paramBody.put("album_id", BigInteger.valueOf(Long.parseLong(albumInfo.getAlbumId())));
        } catch (NumberFormatException e) {
            throw new BusinessException("albumId格式不正确: " + albumInfo.getAlbumId());
        }

//        Map<String, Object> converMap = converMap(albumInfo);
        paramBody.put("album_info", albumInfo);
        List<Map<String, Object>> episodeInfoList = new ArrayList<>();
        List<MiniAppDramaEpisodeResponse> episodeResponseList = miniAppDramaEpisodeService.getByAlbumId(albumInfo.getAlbumId());
        for (MiniAppDramaEpisodeResponse episodeResponse : episodeResponseList) {
            Map<String, Object> episodeInfo = new HashMap<>();
            episodeInfo.put("episode_id", episodeResponse.getDouYinEpisodeId());
            episodeInfo.put("title", episodeResponse.getEpisodeTitle());
            episodeInfo.put("seq", episodeResponse.getEpisodeSeq());
            OpenApiUploadVideoRecord record = uploadVideoRecordService.getById(episodeResponse.getVideoRecordId());
            episodeInfo.put("open_video_id", record.getOpenVideoId());
            episodeInfo.put("cover_list", StringUtils.split(episodeResponse.getCoverId(), ","));
//            episodeInfoList.add(episodeInfo);
        }
        if (CollectionUtil.isNotEmpty(episodeInfoList)) {
            paramBody.put("episode_info_list", episodeInfoList);
        }
        try {
            String result = doPost(paramBody, DUAN_JU_EDIT, token);
            JsonNode jsonNode = mapper.readTree(result);
            if (jsonNode.get("data") != null && jsonNode.get("err_no").asInt() == 0) {
                return jsonNode.get("data").asText();
            } else {
                log.error("编辑短剧失败:{}", JSONObject.toJSONString(albumInfo));
                throw new BusinessException("编辑短剧失败:" + result);
            }
        } catch (IOException e) {
            log.error("编辑短剧失败{}", JSONObject.toJSONString(albumInfo), e);
            throw new BusinessException("编辑短剧失败");
        }
    }

    /**
     * 新增剧集
     */
    public OpenApiRes addEpisode(String albumId, List<AlbumInfo.EpisodeInfo> episodeInfoList) {
        return addEpisode(albumId, episodeInfoList, null);
    }

    public OpenApiRes addEpisode(String albumId, List<AlbumInfo.EpisodeInfo> episodeInfoList, String businessType) {
        if (StringUtils.isEmpty(albumId)) {
            throw new BusinessException("albumId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());

        try {
            paramBody.put("album_id", BigInteger.valueOf(Long.parseLong(albumId)));
        } catch (NumberFormatException e) {
            throw new BusinessException("albumId格式不正确: " + albumId);
        }

        if (CollectionUtil.isNotEmpty(episodeInfoList)) {
            paramBody.put("episode_info_list", AlbumInfo.EpisodeInfo.coverMap(episodeInfoList));
        }
        try {
            String result = doPost(paramBody, DUAN_JU_EDIT, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (IOException e) {
            log.error("新增剧集失败{}", episodeInfoList, e);
            throw new BusinessException("新增剧集失败");
        }
    }


    public String tradeDataQuery(List<String> dataList, String type) {

        String token = getToken(type);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("date_list", dataList);
        String url = "https://open.douyin.com/api/resource_library/v1/iaa/trade_data/query/";
        String result = null;
        try {
            result = doPost(paramBody, url, token);
        } catch (IOException e) {
            log.info("获取交易信息异常");
        }
        return result;
    }

    /**
     * 获取短剧信息
     */
    public String fetch(String albumId, Integer type, Integer offset, Integer limit, Integer version) {
        return fetch(albumId, type, null, offset, limit, version);
    }

    public String fetch(String albumId, Integer type, String businessType, Integer offset, Integer limit, Integer version) {
        if (type == null || (type != 1 && type != 2 && type != 3)) {
            throw new BusinessException("查询类型不正确，type必须为1或2");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("query_type", type);

        Map<String, Object> query = new HashMap<>();

        if (type == 1) {
            query.put("offset", 0);
            query.put("limit", 20);
            paramBody.put("batch_query", query);
        }
        if (type == 2) {
            if (StringUtils.isEmpty(albumId)) {
                throw new BusinessException("单个查询时albumId不能为空");
            }
            query.put("offset", 0);
            query.put("limit", 20);
            query.put("album_id", albumId);
            paramBody.put("single_query", query);
        }
        if (type == 3) {
            query.put("offset", offset);
            query.put("limit", limit);
            query.put("version", version);
            query.put("album_id", albumId);
            paramBody.put("detail_query", query);
        }

        try {
            return doPost(paramBody, DUAN_JU_FETCH, token);
        } catch (IOException e) {
            log.error("获取短剧信息失败，albumId:{}, type:{}", albumId, type, e);
            throw new BusinessException("获取短剧信息失败");
        }
    }

    /**
     * 获取播放链接
     */
    public OpenApiRes getPlayInfo(Long episodeId, Long albumId) {
        return getPlayInfo(episodeId, albumId, null);
    }

    public OpenApiRes getPlayInfo(Long episodeId, Long albumId, String businessType) {
        if (episodeId == null || albumId == null) {
            throw new BusinessException("episodeId和albumId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("episode_id", episodeId);
        paramBody.put("album_id", albumId);
        try {
            String result = doPost(paramBody, DUAN_JU_PLAY_INFO, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (IOException e) {
            log.error("获取播放链接失败，episodeId:{}, albumId:{}", episodeId, albumId, e);
            return OpenApiRes.fail("获取播放链接失败");
        }
    }

    /**
     * 上下线短剧 -
     * operate  1:  查询 2: 上线 3: 下线
     */
    public OpenApiRes online(String albumId, Integer operate, Integer version) {
        return online(albumId, operate, version, null);
    }

    public OpenApiRes online(String albumId, Integer operate, Integer version, String businessType) {
        if (StringUtils.isEmpty(albumId) || operate == null) {
            throw new BusinessException("albumId和operate不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("album_id", albumId);
        paramBody.put("operate", operate);
        paramBody.put("version", version);
        try {
            String result = doPost(paramBody, DUAN_JU_ONLINE, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("上下线短剧失败albumId:{}", albumId, e);
        }
        return OpenApiRes.fail("上下架短剧失败");
    }

    /**
     * 上传视频
     */
    public String uploadVideo(UploadVideoParam param) {
        return uploadVideo(param, null);
    }

    public String uploadVideo(UploadVideoParam param, String businessType) {
        if (param == null || StringUtils.isEmpty(param.getUrl())) {
            throw new BusinessException("上传参数或视频URL不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);

        Map<String, Object> videoMeta = new HashMap<>();
        videoMeta.put("url", param.getUrl());
        videoMeta.put("title", param.getTitle());
        videoMeta.put("description", param.getDescription());
        videoMeta.put("format", param.getFormat());
        videoMeta.put("use_dy_cloud", param.getUseDyCloud());

        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("resource_type", param.getResourceType());
        paramBody.put("video_meta", videoMeta);
        try {
            String result = doPost(paramBody, RESOURCE_UPLOAD_URL, token);
            OpenApiRes openApiRes = JSONObject.parseObject(result, OpenApiRes.class);
            if (openApiRes != null && openApiRes.isSuccess()) {
                return openApiRes.getOpenVideoId();
            }
        } catch (IOException e) {
            log.error("上传视频失败{}", JSONObject.toJSONString(param), e);
            throw new BusinessException("上传视频失败");
        }
        return "";
    }

    /**
     * 上传图片
     */
    public OpenApiRes uploadPic(String picUrl) {
        return uploadPic(picUrl, null);
    }

    public OpenApiRes uploadPic(String picUrl, String businessType) {
        if (StringUtils.isEmpty(picUrl)) {
            throw new BusinessException("图片URL不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        Map<String, String> imageMetaMap = new HashMap<>();
        imageMetaMap.put("url", picUrl);
        paramBody.put("image_meta", imageMetaMap);
        paramBody.put("resource_type", 2);
        try {
            String result = doPost(paramBody, RESOURCE_UPLOAD_URL, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (IOException e) {
            log.error("上传图片失败{}", JSONObject.toJSONString(paramBody), e);
        }
        return OpenApiRes.fail("上传图片失败");
    }


    /**
     * 授权短剧
     */
    public OpenApiRes authorize(String albumId, Boolean remove, String appId) {
        return authorize(albumId, null, remove, appId);
    }


    public OpenApiRes authorize(String albumId, String businessType, Boolean remove, String appId) {
        if (StringUtils.isEmpty(albumId)) {
            throw new BusinessException("albumId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("album_id", albumId);
        if (Objects.nonNull(remove)) {
            paramBody.put("remove", remove);
        }
        if (StringUtils.isNotEmpty(appId)) {
            paramBody.put("app_id_list", Lists.newArrayList(appId));
        } else {
            paramBody.put("app_id_list", Lists.newArrayList(appConfig.getAppId()));
        }
        try {
            String result = doPost(paramBody, DUAN_JU_AUTHORIZE, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("授权失败:{}", albumId, e);
            return OpenApiRes.fail("授权失败");
        }
    }

    /**
     * 送审 审核通过后才能获取视频链接
     */
    public OpenApiRes review(String albumId) {
        return review(albumId, null);
    }

    public OpenApiRes review(String albumId, String businessType) {
        if (StringUtils.isEmpty(albumId)) {
            throw new BusinessException("albumId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);

        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("album_id", albumId);
        try {
            String result = doPost(paramBody, DUAN_JU_REVIEW, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.error("送审失败:{}", albumId, e);
        }
        return OpenApiRes.fail("送审失败");
    }


    public OpenApiRes queryUploadStatus(String openVideoId) {
        return queryUploadStatus(openVideoId, null);
    }

    public OpenApiRes queryUploadStatus(String openVideoId, String businessType) {
        if (StringUtils.isEmpty(openVideoId)) {
            throw new BusinessException("openVideoId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("ma_app_id", appConfig.getAppId());
        paramBody.put("open_video_id", openVideoId);
        paramBody.put("video_id_type", 1);
        try {
            String result = doPost(paramBody, VIDEO_UPLOAD_QUERY, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (IOException e) {
            log.error("查询上传状态失败{}", openVideoId, e);
        }
        return OpenApiRes.fail("查询上传结果失败");
    }

    public OpenApiRes queryOrderByOrderNo(String orderNo) {
        return queryOrderByOrderNo(orderNo, null);
    }

    public OpenApiRes queryOrderByOrderNo(String orderNo, String businessType) {
        if (StringUtils.isEmpty(orderNo)) {
            throw new BusinessException("orderNo不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("out_order_no", orderNo);
        paramBody.put("app_id", appConfig.getAppId());
        try {
            String s = doPost(paramBody, QUERY_ORDER_URL, token);
            return JSONObject.parseObject(s, OpenApiRes.class);
        } catch (Exception e) {
            log.error("查询订单失败{}", orderNo, e);
        }
        return OpenApiRes.fail("查询订单异常");
    }

    public OpenApiRes queryRefundOrderByOrderNo(String orderNo, String businessType) {
        if (StringUtils.isEmpty(orderNo)) {
            throw new BusinessException("orderNo不能为空");
        }
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("out_refund_no", orderNo);
        try {
            String s = doPost(paramBody, "https://open.douyin.com/api/trade_basic/v1/developer/refund_query/", token);
            return JSONObject.parseObject(s, OpenApiRes.class);
        } catch (Exception e) {
            log.error("查询订单失败{}", orderNo, e);
        }
        return OpenApiRes.fail("查询订单异常");
    }

    public OpenApiRes queryOrderByTractionId(String tractionId) {
        return queryOrderByTractionId(tractionId, null);
    }

    public OpenApiRes queryOrderByTractionId(String tractionId, String businessType) {
        if (StringUtils.isEmpty(tractionId)) {
            throw new BusinessException("tractionId不能为空");
        }

        DouyinAppConfig appConfig = getAppConfig(businessType);
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();
        paramBody.put("order_id", tractionId);
        paramBody.put("app_id", appConfig.getAppId());
        try {
            String s = doPost(paramBody, QUERY_ORDER_URL, token);
            return JSONObject.parseObject(s, OpenApiRes.class);
        } catch (Exception e) {
            log.error("查询订单失败{}", tractionId, e);
        }
        return OpenApiRes.builder().errNo(-1).errMsg("查询订单异常").build();
    }

    public Map<String, Object> converMap(AlbumInfo param) {
        Map<String, Object> map = new HashMap<>();
        if (param != null) {
            map.put("title", param.getTitle());
            map.put("seq_num", param.getSeqNum());
            map.put("cover_list", param.getCoverList());
            map.put("year", param.getYear());
            map.put("album_status", param.getAlbumStatus());
            map.put("recommendation", param.getRecommendation());
            map.put("tag_list", param.getTagList());
            map.put("desp", param.getDesp());
            map.put("qualification", param.getQualification());

            AlbumInfo.RecordInfo recordInfo = param.getRecordInfo();
            if (recordInfo != null) {
                Map<String, Object> recordInfoMap = new HashMap<>();
                boolean putFlag = false;
                if (StringUtils.isNotEmpty(recordInfo.getLicenseNum())) {
                    putFlag = true;
                    recordInfoMap.put("license_num", recordInfo.getLicenseNum());
                }
                if (StringUtils.isNotEmpty(recordInfo.getRegistrationNum())) {
                    putFlag = true;
                    recordInfoMap.put("registration_num", recordInfo.getRegistrationNum());
                }
                if (StringUtils.isNotEmpty(recordInfo.getOrdinaryRecordNum())) {
                    putFlag = true;
                    recordInfoMap.put("ordinary_record_num", recordInfo.getOrdinaryRecordNum());
                }
                if (StringUtils.isNotEmpty(recordInfo.getKeyRecordNum())) {
                    putFlag = true;
                    recordInfoMap.put("key_record_num", recordInfo.getKeyRecordNum());
                }
                if (putFlag) {
                    map.put("record_info", recordInfoMap);
                }
            }

            AlbumInfo.RecordAuditInfo recordAuditInfo = param.getRecordAuditInfo();
            if (recordAuditInfo != null) {
                Map<String, Object> recordAuditInfoMap = new HashMap<>();
                AlbumInfo.RecordMaterial recordMaterial = recordAuditInfo.getRecordMaterial();
                if (recordMaterial != null) {
                    Map<String, Object> recordMaterialMap = new HashMap<>();
                    recordMaterialMap.put("name", recordMaterial.getName());
                    recordMaterialMap.put("duration", recordMaterial.getDuration());
                    recordMaterialMap.put("seqs_count", recordMaterial.getSeqsCount());
                    recordMaterialMap.put("production_organisation", recordMaterial.getProductionOrganisation());
                    recordMaterialMap.put("director", recordMaterial.getDirector());
                    recordMaterialMap.put("producer", recordMaterial.getProducer());
                    recordMaterialMap.put("actor", recordMaterial.getActor());
                    recordMaterialMap.put("screen_writer", recordMaterial.getScreenWriter());
                    recordMaterialMap.put("summary", recordMaterial.getSummary());
                    recordMaterialMap.put("cost_distribution_uri", recordMaterial.getCostDistributionUri());
                    if (StringUtils.isNotEmpty(recordMaterial.getCostDistributionUri())) {
                        recordMaterialMap.put("assurance_uri", recordMaterial.getAssuranceUri());
                    }
                    recordMaterialMap.put("playlet_production_cost", recordMaterial.getPlayletProductionCost());
                    recordAuditInfoMap.put("record_material", JSONObject.toJSONString(recordMaterialMap));
                    map.put("record_audit_info", recordAuditInfoMap);
                }
                AlbumInfo.BroadcastRecordInfo broadcastRecordInfo = recordAuditInfo.getBroadcastRecordInfo();
                if (broadcastRecordInfo != null && Objects.nonNull(broadcastRecordInfo.getRecordType()) && StringUtils.isNotEmpty(broadcastRecordInfo.getBroadcastRecordNumber())) {
                    Map<String, Object> broadcastRecordInnfo = new HashMap<>();
                    broadcastRecordInnfo.put("record_type", broadcastRecordInfo.getRecordType());
                    broadcastRecordInnfo.put("broadcast_record_number", broadcastRecordInfo.getBroadcastRecordNumber());
                    map.put("broadcast_record_info", recordAuditInfoMap);
                }
            }
        }
        return map;
    }


    public String doPost(Object param, String uri) throws IOException {
        return doPost(param, uri, null);
    }

    public String doPost(Object param, String uri, String token) throws IOException {
        log.info("抖音开放平台请求参数:{},url:{}", param, uri);
        HttpClient httpClient = HttpClients.createDefault();
        HttpPost post = new HttpPost(uri);

        // 使用Jackson ObjectMapper进行序列化，支持@JsonProperty注解
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(param);

        post.setEntity(new StringEntity(jsonString, "UTF-8"));
        post.setHeader("Content-Type", "application/json");
        if (StringUtils.isNotEmpty(token)) {
            post.setHeader("access-token", token);
        }
        HttpResponse execute = httpClient.execute(post);
        String result = EntityUtils.toString(execute.getEntity());
        log.info("抖音开放平台请求结果:{}", result);
        return result;
    }

    public static String decrypt(String encryptedData, String privateKeyString) throws Exception {
        // Decode the private key
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyString);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

        // Decrypt the data
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedDataBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedDataBytes);
    }

    public OpenApiRes generateSchema(String businessType, String path, String query, Long expireTime, Boolean noExpire) {
        DouyinAppConfig appConfig = douYinAppConfigService.getByBusinessType(businessType);
        if (Objects.isNull(appConfig)) {
            throw new BusinessException("未找到对应配置");
        }
        String token = getToken(businessType);
        Map<String, Object> paramBody = new HashMap<>();

        if (Objects.nonNull(noExpire)) {
            paramBody.put("no_expire", noExpire);
        }
        if (StringUtils.isNotEmpty(path)) {
            paramBody.put("path", path);
        }
        if (StringUtils.isNotEmpty(query)) {
            paramBody.put("query", query);
        }
        if (Objects.nonNull(expireTime)) {
            paramBody.put("expire_time", expireTime);
        }
        paramBody.put("app_id", appConfig.getAppId());
        try {
            String result = doPost(paramBody, generateSchemaUrl, token);
            return JSONObject.parseObject(result, OpenApiRes.class);
        } catch (Exception e) {
            log.info("生成schema失败", e);
            throw new BusinessException("生成schema失败");
        }
    }

}
