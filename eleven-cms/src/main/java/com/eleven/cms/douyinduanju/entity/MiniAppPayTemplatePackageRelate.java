package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: mini_app_pay_template_package_relate
 * @Author: jeecg-boot
 * @Date: 2025-07-28
 * @Version: V1.0
 */
@Data
@TableName("mini_app_pay_template_package_relate")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_pay_template_package_relate对象", description = "mini_app_pay_template_package_relate")
public class MiniAppPayTemplatePackageRelate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 模板id
     */
    @Excel(name = "模板id", width = 15)
    @ApiModelProperty(value = "模板id")
    private String templateId;
    /**
     * 套餐id
     */
    @Excel(name = "套餐id", width = 15)
    @ApiModelProperty(value = "套餐id")
    private String packageId;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
