package com.eleven.cms.douyinduanju.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.annotation.TokenRequired;
import com.eleven.cms.douyinduanju.dto.LookHistoryVO;
import com.eleven.cms.douyinduanju.dto.ViewingHistoryReq;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaViewingHistory;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaViewingHistoryService;
import com.eleven.cms.douyinduanju.util.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("miniApi/duanju/api/")
@Slf4j
public class HistoryApiController {

    @Resource
    private IMiniAppDramaViewingHistoryService viewingHistoryService;

    /**
     * 新增观看历史记录
     */
    @TokenRequired
    @PostMapping("/addOrUpdateViewingHistory")
    public Result<Object> addViewHistory(@RequestBody ViewingHistoryReq req) {
        log.info("新增观看历史记录：{}", req);
        MiniAppDramaViewingHistory history = null;
        try {
            history = viewingHistoryService.addOrUpdateViewingHistory(req);
        } catch (Exception e) {
            log.error("新增观看历史记录失败：{}", e.getMessage());
            return Result.ok("新增成功");
        }
        return Result.ok(history.getId());
    }

    /**
     * 获取观看历史记录
     */
    @TokenRequired
    @GetMapping("/getViewingHistory")
    public Result<Object> getViewingHistory(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        List<LookHistoryVO> lastHistory = null;
        try {
            lastHistory = viewingHistoryService.getLatestHistoryForEachDrama(TokenUtils.getCurrentUserId());
        } catch (Exception e) {
            log.info("获取最近观看列表异常", e);
        }
        return Result.ok(lastHistory);
    }

    @TokenRequired
    @GetMapping("/getHistory")
    public Result<Object> getHistoryDetail(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<MiniAppDramaViewingHistory> page1 = null;
        try {
            Page<MiniAppDramaViewingHistory> page = new Page<>(pageNo, pageSize);
            QueryWrapper<MiniAppDramaViewingHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", TokenUtils.getCurrentUserId());
            queryWrapper.eq("is_deleted", 0);
            queryWrapper.orderByDesc("update_time");
            page1 = viewingHistoryService.page(page, queryWrapper);
        } catch (Exception e) {
            log.info("获取观看记录失败", e);
        }
        return Result.ok(page1);
    }

    @TokenRequired
    @GetMapping("/getLastView")
    public Result<?> getLastView(String albumId) {
        MiniAppDramaViewingHistory history = viewingHistoryService.lambdaQuery().eq(MiniAppDramaViewingHistory::getAlbumId, albumId).orderByDesc(MiniAppDramaViewingHistory::getUpdateTime).last("limit 1").one();
        return Result.ok(history);
    }

}
