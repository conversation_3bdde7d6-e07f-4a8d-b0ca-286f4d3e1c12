package com.eleven.cms.douyinduanju.util;

import com.eleven.cms.douyinduanju.entity.DuanJuUser;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Token工具类
 * 用于在Controller中方便地获取当前用户信息
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public class TokenUtils {

    /**
     * 获取当前请求的用户信息
     *
     * @return 当前用户信息，如果未登录或token无效则返回null
     */
    public static DuanJuUser getCurrentUser() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }

        HttpServletRequest request = attributes.getRequest();
        Object user = request.getAttribute("currentUser");

        if (user instanceof DuanJuUser) {
            return (DuanJuUser) user;
        }

        return null;
    }

    /**
     * 获取当前请求的token
     *
     * @return 当前token，如果未提供token则返回null
     */
    public static String getCurrentToken() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }

        HttpServletRequest request = attributes.getRequest();
        Object token = request.getAttribute("currentToken");

        if (token instanceof String) {
            return (String) token;
        }

        return null;
    }

    /**
     * 获取当前用户ID
     *
     * @return 当前用户ID，如果未登录则返回null
     */
    public static Integer getCurrentUserId() {
        DuanJuUser user = getCurrentUser();
        return user != null ? user.getId() : null;
    }

    /**
     * 获取当前用户OpenID
     *
     * @return 当前用户OpenID，如果未登录则返回null
     */
    public static String getCurrentOpenId() {
        DuanJuUser user = getCurrentUser();
        return user != null ? user.getOpenId() : null;
    }

    /**
     * 获取当前用户手机号
     *
     * @return 当前用户手机号，如果未登录则返回null
     */
    public static String getCurrentMobile() {
        DuanJuUser user = getCurrentUser();
        return user != null ? user.getMobile() : null;
    }

    /**
     * 检查当前用户是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isLoggedIn() {
        return getCurrentUser() != null;
    }
}
