package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OrderReq {
    /**
     * 套餐包id
     */
    private String packageId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 短剧ID（可选，用于剧集商品）
     */
    @ApiModelProperty(value = "短剧ID，用于剧集商品")
    private String albumId;

    /**
     * 集ID列表（可选，用于剧集商品）
     */
    @ApiModelProperty(value = "集ID列表，用于剧集商品")
    private List<String> episodeIds;

    /**
     * 业务类型
     */
    private String resource;

    /**
     * 剧ID（可选，用于剧集商品）
     */
    private String dramaId;

    /**
     * 机型 安卓，ios
     */
    private String model;


}
