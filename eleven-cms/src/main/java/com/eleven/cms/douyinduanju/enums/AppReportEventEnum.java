package com.eleven.cms.douyinduanju.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum AppReportEventEnum {
    /**
     * 激活
     */
    ACTIVE("1", "激活", "active", true, "active", "1"),
    /**
     * 注册
     */
    ACTIVE_REGISTER("2", "注册", "active_register", false, "active_register", "2"),
    /**
     * 付费
     */
    ACTIVE_PAY("3", "付费", "active_pay", false, "active_pay", "3"),
    /**
     * 关键行为
     */
    GAME_ADDICTION("4", "关键行为", "game_addiction", false, "game_addiction", "143"),
    /**
     * 次日留存
     */
    NEXT_DAY_OPEN("5", "次日留存", "next_day_open", false, "next_day_open", "7"),
    /**
     * 话费付费
     */
    ACTIVE_PHONE_BILL_PAY("6", "话费付费", "active_phone_bill_pay", false, "active_pay", "3");
    /**
     * 系统code
     */
    private final String code;
    /**
     * 中文定义
     */
    private final String defineCN;
    /**
     * 描述
     */
    private final String description;
    /**
     * 是否必须
     */
    private final boolean need;
    /**
     * 巨量事件名定义
     */
    private final String juLiangDefine;
    /**
     * 快手事件名定义
     */
    private final String kuaiShouDefine;

    public static AppReportEventEnum[] VALUES = AppReportEventEnum.values();

    public static AppReportEventEnum getByCode(String code) {
        for (AppReportEventEnum eventEnum : VALUES) {
            if (Objects.equals(eventEnum.getCode(), code)) {
                return eventEnum;
            }
        }
        return null;
    }

    public static AppReportEventEnum getByDescription(String description) {
        for (AppReportEventEnum eventEnum : VALUES) {
            if (eventEnum.getDescription().equals(description)) {
                return eventEnum;
            }
        }
        return null;
    }


}
