package com.eleven.cms.douyinduanju.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.entity.DuanJuUser;
import com.eleven.cms.douyinduanju.service.IDuanJuUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * @Description: duan_ju_user
 * @Author: jeecg-boot
 * @Date: 2025-06-05
 * @Version: V1.0
 */
@Api(tags = "duan_ju_user")
@RestController
@RequestMapping("/douyinduanju/duanJuUser")
@Slf4j
public class DuanJuUserController extends JeecgController<DuanJuUser, IDuanJuUserService> {
    @Autowired
    private IDuanJuUserService duanJuUserService;

    /**
     * 分页列表查询
     *
     * @param duanJuUser
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "duan_ju_user-分页列表查询", notes = "duan_ju_user-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DuanJuUser duanJuUser,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {


        QueryWrapper<DuanJuUser> queryWrapper = QueryGenerator.initQueryWrapper(duanJuUser, req.getParameterMap());
        Date registerTimeBegin = parseDateTime(req.getParameter("registerTimeBegin"), "registerTimeBegin");
        Date registerTimeEnd = parseDateTime(req.getParameter("registerTimeEnd"), "registerTimeEnd");
        queryWrapper.ge(Objects.nonNull(registerTimeBegin), "register_time", registerTimeBegin);
        queryWrapper.le(Objects.nonNull(registerTimeEnd), "register_time", registerTimeEnd);

        Page<DuanJuUser> page = new Page<DuanJuUser>(pageNo, pageSize);
        IPage<DuanJuUser> pageList = duanJuUserService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    private Date parseDateTime(String timeStr, String fieldName) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            log.debug("{}为空，跳过解析", fieldName);
            return null;
        }

        try {
            // 尝试多种时间格式
            String[] patterns = {
                    "yyyy-MM-dd HH:mm:ss",    // 标准格式
                    "yyyy-MM-dd",             // 日期格式
                    "yyyy/MM/dd HH:mm:ss",    // 斜杠格式
                    "yyyy/MM/dd",             // 斜杠日期格式
                    "yyyy-MM-dd'T'HH:mm:ss",  // ISO格式（不带时区）
                    "yyyy-MM-dd'T'HH:mm:ss.SSS", // ISO格式（带毫秒）
                    "yyyy-MM-dd'T'HH:mm:ss'Z'",   // ISO格式（带Z时区）
                    "yyyy-MM-dd HH:mm",       // 不带秒
                    "MM/dd/yyyy HH:mm:ss",    // 美式格式
                    "dd/MM/yyyy HH:mm:ss"     // 欧式格式
            };

            for (String pattern : patterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    sdf.setLenient(false); // 严格解析
                    Date parsedDate = sdf.parse(timeStr.trim());

                    log.debug("{}解析成功: {} -> {}", fieldName, timeStr,
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(parsedDate));
                    return parsedDate;

                } catch (ParseException ignored) {
                    // 继续尝试下一个格式
                }
            }

            // 所有格式都失败，记录警告
            log.warn("{}解析失败，不支持的时间格式: {}", fieldName, timeStr);
            return null;

        } catch (Exception e) {
            log.error("{}解析异常: {}", fieldName, timeStr, e);
            return null;
        }
    }
    /**
     * 添加
     *
     * @param duanJuUser
     * @return
     */
    @AutoLog(value = "duan_ju_user-添加")
    @ApiOperation(value = "duan_ju_user-添加", notes = "duan_ju_user-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DuanJuUser duanJuUser) {
        duanJuUserService.save(duanJuUser);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param duanJuUser
     * @return
     */
    @AutoLog(value = "duan_ju_user-编辑")
    @ApiOperation(value = "duan_ju_user-编辑", notes = "duan_ju_user-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DuanJuUser duanJuUser) {
        duanJuUserService.updateById(duanJuUser);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "duan_ju_user-通过id删除")
    @ApiOperation(value = "duan_ju_user-通过id删除", notes = "duan_ju_user-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        duanJuUserService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "duan_ju_user-批量删除")
    @ApiOperation(value = "duan_ju_user-批量删除", notes = "duan_ju_user-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.duanJuUserService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "duan_ju_user-通过id查询")
    @ApiOperation(value = "duan_ju_user-通过id查询", notes = "duan_ju_user-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DuanJuUser duanJuUser = duanJuUserService.getById(id);
        if (duanJuUser == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(duanJuUser);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param duanJuUser
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DuanJuUser duanJuUser) {
        return super.exportXls(request, duanJuUser, DuanJuUser.class, "duan_ju_user");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DuanJuUser.class);
    }

}
