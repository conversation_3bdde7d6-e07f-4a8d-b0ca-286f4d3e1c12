package com.eleven.cms.douyinduanju.controller;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.douyinduanju.dto.AlbumInfo;
import com.eleven.cms.douyinduanju.dto.DouYinOpenApiReq;
import com.eleven.cms.douyinduanju.dto.OpenApiRes;
import com.eleven.cms.douyinduanju.dto.UploadVideoParam;
import com.eleven.cms.douyinduanju.entity.MiniAppAlbumAuditMsg;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaEpisode;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.entity.OpenApiUploadVideoRecord;
import com.eleven.cms.douyinduanju.service.IMiniAppAlbumAuditMsgService;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaEpisodeService;
import com.eleven.cms.douyinduanju.service.IOpenApiAlbumInfoService;
import com.eleven.cms.douyinduanju.service.IOpenApiUploadVideoRecordService;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;

@RestController
@RequestMapping("/duanju")
@Slf4j
public class DouYinApiController {


    @Resource
    IOpenApiUploadVideoRecordService uploadRecordService;

    @Resource
    IOpenApiAlbumInfoService openApiAlbumInfoService;

    @Resource
    DouYinHttpUtil douYinHttpUtil;
    @Resource
    private IMiniAppDramaEpisodeService episodeService;

    @Resource
    private IMiniAppAlbumAuditMsgService appAlbumAuditMsgService;

    @GetMapping("/getToken")
    public Result<?> getToken() {
        return Result.ok(douYinHttpUtil.getToken());
    }


    @PostMapping("/uploadVideo")
    public Result<?> uploadVideo(@RequestBody UploadVideoParam uploadVideoParam) {
        log.info("uploadVideoParam:{}", uploadVideoParam);


        OpenApiUploadVideoRecord uploadRecord = uploadRecordService.uploadVideo(uploadVideoParam);
        try {
            String openVideoId = douYinHttpUtil.uploadVideo(uploadVideoParam);
            if (StringUtils.isEmpty(openVideoId)) {
                return Result.error("上传视频失败");
            }
            OpenApiUploadVideoRecord update = new OpenApiUploadVideoRecord();
            update.setId(uploadRecord.getId());
            update.setOpenVideoId(openVideoId);
            uploadRecordService.updateById(update);
            return Result.ok("上传视频成功");
        } catch (Exception e) {
            return Result.error("上传视频失败");
        }
    }

    @PostMapping("/notify_callback")
    public Map<String, Object> notifyCallback(HttpServletRequest request) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("err_no", 0);
            result.put("err_tips", "success");
            ServletInputStream inputStream = request.getInputStream();
            String notifyJson = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("抖音内容库回调通知:{}", notifyJson);
            JSONObject jsonObject = JSONObject.parseObject(notifyJson);
            String type = (String) jsonObject.get("type");
            if (StringUtils.equals(type, "upload_video")) {
                String msg = (String) jsonObject.get("msg");
                JSONObject msgJson = JSONObject.parseObject(msg);
                Boolean success = (Boolean) msgJson.get("success");
                String dyCloudId = (String) msgJson.get("dy_cloud_id");
                String openVideoId = (String) msgJson.get("open_video_id");
                OpenApiUploadVideoRecord update = new OpenApiUploadVideoRecord();

                OpenApiUploadVideoRecord videoRecord = uploadRecordService.lambdaQuery().eq(OpenApiUploadVideoRecord::getOpenVideoId, openVideoId).last("limit 1").one();
                if (Objects.isNull(videoRecord)) {
                    log.info("抖音内容库回调通知:{}", "视频不存在");
                    return result;
                }
                if (!Objects.equals(videoRecord.getStatus(), 2) && StringUtils.isNotEmpty(videoRecord.getDyCloudId())) {
                    return result;
                }
                if (Objects.nonNull(msgJson.get("success_to_dy_cloud"))) {
                    Boolean successToDyCloud = (Boolean) msgJson.get("success_to_dy_cloud");
                    if (successToDyCloud) {
                        update.setDyCloudId(dyCloudId);
                    }
                }

                if (success) {
                    update.setStatus(4);
                } else {
                    update.setStatus(3);
                }
                uploadRecordService.lambdaUpdate().eq(OpenApiUploadVideoRecord::getOpenVideoId, openVideoId)
                        .set(OpenApiUploadVideoRecord::getStatus, update.getStatus())
                        .set(StringUtils.isNotEmpty(dyCloudId), OpenApiUploadVideoRecord::getDyCloudId, dyCloudId)
                        .set(OpenApiUploadVideoRecord::getUpdateTime, new Date())
                        .update();
            }

            if (StringUtils.equals(type, "album_audit")) {
                JSONObject msg = jsonObject.getJSONObject("msg");
                String albumId = msg.getString("album_id");
                Integer version = msg.getInteger("version");
                Integer auditStatus = msg.getInteger("audit_status");
                OpenApiAlbumInfo apiAlbumInfo = openApiAlbumInfoService.getByAlbumId(albumId);
                try {
                    MiniAppAlbumAuditMsg auditMsg = new MiniAppAlbumAuditMsg();
                    auditMsg.setAlbumId(albumId);
                    auditMsg.setAuditMsg(msg.getString("audit_msg"));
                    auditMsg.setAuditDate(new Date());

                    if (auditStatus == 1) {
                        auditMsg.setAuditStatus(3);
                    }
                    if (auditStatus == 2) {
                        auditMsg.setAuditStatus(4);
                    }
                    auditMsg.setVersion(version);
                    if (Objects.nonNull(apiAlbumInfo)) {
                        auditMsg.setTitle(apiAlbumInfo.getTitle());
                    }
                    appAlbumAuditMsgService.save(auditMsg);
                } catch (Exception e) {
                    log.info("保存审核信息失败", e);
                }
                if (apiAlbumInfo == null) {
                } else {
                    OpenApiAlbumInfo update = new OpenApiAlbumInfo();
                    update.setId(apiAlbumInfo.getId());
                    if (auditStatus == 1) {
                        update.setReviewStatus(3);
                    }
                    if (auditStatus == 2) {
                        update.setReviewStatus(4);
                    }
                    update.setAuditMsg(msg.getString("audit_msg"));
                    openApiAlbumInfoService.updateById(update);
                }
            }
            if (StringUtils.equals(type, "episode_audit")) {
                JSONObject msg = jsonObject.getJSONObject("msg");
                String episodeId = msg.getString("episode_id");
                MiniAppDramaEpisode miniAppDramaEpisode = episodeService.lambdaQuery().eq(MiniAppDramaEpisode::getDouYinEpisodeId, episodeId).last("limit 1").one();
                if (Objects.nonNull(miniAppDramaEpisode)) {
                    miniAppDramaEpisode.setAuditStatus(msg.getInteger("audit_status"));
                    miniAppDramaEpisode.setAuditMsg(msg.getString("audit_msg"));
                    episodeService.updateById(miniAppDramaEpisode);
                }
            }
            return result;
        } catch (Exception e) {
            log.info("回调通知错误", e);
        }
        return null;
    }

    @Resource
    private RedisUtil redisUtil;
    /**
     * 提前准备好上传内容，根据记录批量上传至抖音内容库
     */
    @GetMapping("/uploadVideoOpenApiByRecord")
    public Result<?> uploadVideoOpenApiByRecord(@RequestParam(name = "id", required = false) String id, @RequestParam(name = "type") String type) {

        if (StringUtils.isEmpty(type)) {
            return Result.error("上传类型不能为空");
        }
        List<OpenApiUploadVideoRecord> list = new ArrayList<>();
        if (StringUtils.equals(type, "all")) {
            String[] split = id.split(",");
            list = uploadRecordService.lambdaQuery().eq(OpenApiUploadVideoRecord::getStatus, 1).in(OpenApiUploadVideoRecord::getAlbumId, split).isNull(OpenApiUploadVideoRecord::getOpenVideoId).list();
        }
        if (StringUtils.equals(type, "single")) {
            list = uploadRecordService.lambdaQuery().eq(OpenApiUploadVideoRecord::getId, id).list();
        }
        for (OpenApiUploadVideoRecord record : list) {
            String openVideoId = record.getOpenVideoId();
            if (StringUtils.isNotEmpty(openVideoId)) {
                continue;
            }
            if (record.getStatus() == 4 || record.getStatus() == 2) {
                continue;
            }
            UploadVideoParam uploadVideoParam = new UploadVideoParam();
            uploadVideoParam.setUrl(record.getResourceVideoUrl());
            uploadVideoParam.setTitle(record.getTitle());
            uploadVideoParam.setDescription(record.getDescription());
            uploadVideoParam.setFormat("mp4");
            uploadVideoParam.setUseDyCloud(true);
            uploadVideoParam.setResourceType(1);
            boolean rateLimit = redisUtil.rateLimitV2("miniApp:douYinUploadVideo", 10, 1);
            if (rateLimit) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.info("等待上传视频异常:");
                }
            }
            String result = douYinHttpUtil.uploadVideo(uploadVideoParam);
            if (StringUtils.isNotEmpty(result)) {
                OpenApiUploadVideoRecord update = new OpenApiUploadVideoRecord();
                update.setId(record.getId());
                update.setOpenVideoId(result);
                update.setUploadTime(new Date());
                update.setStatus(2);
                uploadRecordService.updateById(update);
            }
        }
        return Result.ok();
    }

    @GetMapping("/test")
    public Result<?> test(@RequestParam("id") String id) {

        OpenApiRes openApiRes = douYinHttpUtil.queryUploadStatus(id);
        return Result.ok(openApiRes.getData());

    }


    @GetMapping("/queryStatus")
    public Result<?> queryStatus(@RequestParam("id") String id) {
        OpenApiUploadVideoRecord duanJuUploadRecord = uploadRecordService.getById(id);
        if (Objects.equals(duanJuUploadRecord.getStatus(), 4)) {
            return Result.ok("上传成功");
        }
        if (Objects.equals(duanJuUploadRecord.getStatus(), 3)) {
            return Result.ok("上传失败");
        }
        OpenApiRes openApiRes = douYinHttpUtil.queryUploadStatus(duanJuUploadRecord.getOpenVideoId());
        if (openApiRes.isSuccess()) {
            String result = openApiRes.getData();
            String openVideoStatus = openApiRes.getOpenVideoStatus();
            String dyCloudId = openApiRes.getDyCloudId();

            OpenApiUploadVideoRecord update = new OpenApiUploadVideoRecord();
            update.setId(duanJuUploadRecord.getId());
            if (StringUtils.equals(openVideoStatus, "INIT")) {
                return Result.ok("上传中");
            }
            if (StringUtils.equals(openVideoStatus, "OK")) {
                update.setStatus(4);
            }
            if (StringUtils.equals(openVideoStatus, "FAIL")) {
                update.setStatus(3);
            }
            if (StringUtils.isNotEmpty(dyCloudId)) {
                update.setDyCloudId(dyCloudId);
            }
            uploadRecordService.updateById(update);
            return Result.ok(result);
        } else {
            return Result.ok(openApiRes.getErrMsg());
        }
    }

    @PostMapping("/createDuanJu")
    public Result<?> createDuanJu(@RequestBody AlbumInfo albumInfo) {
        log.info("albumInfo:{}", albumInfo);
        return Result.ok(douYinHttpUtil.createDuanJu(albumInfo));
    }

    /**
     * 编辑短剧信息
     */
    @GetMapping("/editAlbumInfo")
    public Result<?> editAlbumInfo(String albumId) {
        AlbumInfo albumInfo = openApiAlbumInfoService.getAlbumInfoByAlbumId(albumId);
        return Result.ok(douYinHttpUtil.editAlbumInfo(albumInfo));
    }


    /**
     * 短剧授权
     */
    @GetMapping("/authorize")
    public Result<?> authorize(@RequestParam("albumId") String albumId, @RequestParam(required = false) Boolean remove,
                               @RequestParam(required = false) String authorizeAppId) {
        String[] split = albumId.split(",");
        OpenApiRes openApiRes = null;
        for (String id : split) {
            openApiRes = douYinHttpUtil.authorize(id, remove, authorizeAppId);
        }

        return Result.ok(openApiRes.getData());
    }


    /**
     * 短剧送审
     */

    @GetMapping("/review")
    public Result<?> review(@RequestParam("albumId") String albumId) {
        OpenApiRes authorize = douYinHttpUtil.review(albumId);
        return Result.ok(authorize.getData());
    }

    /**
     * 获取短剧信息
     *
     * @param albumId
     * @return
     */
    @GetMapping("/fetch")
    public Result<?> fetch(@RequestParam("albumId") String albumId, Integer type,
                           @RequestParam(name = "offset", defaultValue = "0") Integer offset,
                           @RequestParam(name = "limit", defaultValue = "50") Integer limit,
                           @RequestParam(name = "version", defaultValue = "1") Integer version
    ) {

        String result = douYinHttpUtil.fetch(albumId, type, offset, limit, version);
        return Result.ok(result);
    }

    @ApiOperation(value = "交易数据查询")
    @PostMapping("/tradeDataQuery")
    public Result<?> tradeDataQuery(@RequestBody DouYinOpenApiReq.TradeDataQueryReq req) {

        return Result.ok(douYinHttpUtil.tradeDataQuery(req.getDataList(), req.getType()));

    }

    /**
     * 获取播放信息
     */
    @GetMapping("/getPlayInfo")
    public Result<?> getPlayInfo(Long albumId, Long episodeId, @RequestParam String businessType) {
        OpenApiRes result = douYinHttpUtil.getPlayInfo(episodeId, albumId, businessType);
        log.info("result:{}", result);
        return Result.ok(result.getPlayInfo());
    }

}
