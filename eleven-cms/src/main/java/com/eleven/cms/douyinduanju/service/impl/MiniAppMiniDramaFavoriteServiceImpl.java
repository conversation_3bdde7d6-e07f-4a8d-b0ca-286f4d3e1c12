package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.MiniAppDramaEpisodeVO;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaEpisode;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDramaFavorite;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.mapper.MiniAppMiniDramaFavoriteMapper;
import com.eleven.cms.douyinduanju.service.IMiniAppMiniDramaFavoriteService;
import com.eleven.cms.douyinduanju.service.IMiniAppMiniDramaService;
import com.eleven.cms.douyinduanju.service.IOpenApiAlbumInfoService;
import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * MiniAppMiniDrama收藏记录服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Service
public class MiniAppMiniDramaFavoriteServiceImpl extends ServiceImpl<MiniAppMiniDramaFavoriteMapper, MiniAppMiniDramaFavorite>
        implements IMiniAppMiniDramaFavoriteService {

    @Resource
    private IMiniAppMiniDramaService miniAppMiniDramaService;

    @Resource
    private IOpenApiAlbumInfoService openApiAlbumInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addFavorite(Integer userId, String albumId) {
        if (Objects.isNull(userId) || StringUtils.isEmpty(albumId)) {
            throw new BusinessException("用户ID和短剧albumId不能为空");
        }

        try {
            // 通过albumId获取短剧信息
            MiniAppMiniDrama drama = miniAppMiniDramaService.getByAlbumId(albumId);
            if (drama == null) {
                throw new BusinessException("短剧不存在");
            }

            // 检查是否已收藏
            MiniAppMiniDramaFavorite existingFavorite = lambdaQuery().eq(MiniAppMiniDramaFavorite::getUserId, userId)
                    .eq(MiniAppMiniDramaFavorite::getAlbumId, albumId)
                    .eq(MiniAppMiniDramaFavorite::getIsDeleted, 0)
                    .last("limit 1")
                    .orderByDesc(MiniAppMiniDramaFavorite::getCreateTime)
                    .one();
            OpenApiAlbumInfo apiAlbumInfo = openApiAlbumInfoService.getByAlbumId(albumId);
            if (existingFavorite != null) {
                existingFavorite.setAlbumName(drama.getName());
                existingFavorite.setFavoriteTime(new Date());
                existingFavorite.setUpdateTime(new Date());
                existingFavorite.setSeqCount(apiAlbumInfo.getSeqCount());
                existingFavorite.setCoverUrl(drama.getCoverUrl());
                if (existingFavorite.getStatus() == 1) {
                    existingFavorite.setStatus(0);
                } else {
                    existingFavorite.setStatus(1);
                }
                return updateById(existingFavorite);
            }

            // 创建收藏记录
            MiniAppMiniDramaFavorite favorite = new MiniAppMiniDramaFavorite();
            favorite.setUserId(userId);
            favorite.setFavoriteTime(new Date());
            favorite.setSeqCount(apiAlbumInfo.getSeqCount());
            favorite.setAlbumId(drama.getAlbumId());
            favorite.setAlbumName(drama.getName());
            favorite.setCoverUrl(drama.getCoverUrl());
            favorite.setFavoriteTime(new Date());
            favorite.setStatus(1); // 已收藏
            favorite.setIsDeleted(0);
            favorite.setCreateTime(new Date());
            favorite.setUpdateTime(new Date());

            boolean result = save(favorite);

            if (result) {
                log.info("添加收藏成功，用户ID: {}, 短剧ID: {}", userId, drama.getId());
            }

            return result;

        } catch (Exception e) {
            log.error("添加收藏异常，用户ID: {}, albumId: {}", userId, albumId, e);
            throw new BusinessException("添加收藏失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeFavorite(Integer userId, String dramaId) {
        if (Objects.isNull(userId) || StringUtils.isEmpty(dramaId)) {
            throw new BusinessException("用户ID和短剧ID不能为空");
        }

        try {
            MiniAppMiniDramaFavorite favorite = getFavoriteByUserIdAndDramaId(userId, dramaId);

            if (favorite == null) {
                log.warn("收藏不存在，用户ID: {}, 短剧ID: {}", userId, dramaId);
                return true; // 不存在视为成功
            }

            // 软删除
            favorite.setStatus(0); // 已取消收藏
            favorite.setUpdateTime(new Date());

            boolean result = updateById(favorite);

            if (result) {
                log.info("取消收藏成功，用户ID: {}, 短剧ID: {}", userId, dramaId);
            }

            return result;

        } catch (Exception e) {
            log.error("取消收藏异常，用户ID: {}, 短剧ID: {}", userId, dramaId, e);
            throw new BusinessException("取消收藏失败: " + e.getMessage());
        }
    }


    @Override
    public boolean isFavorited(Integer userId, String dramaId) {
        if (Objects.isNull(userId) || StringUtils.isEmpty(dramaId)) {
            return false;
        }

        try {
            MiniAppMiniDramaFavorite favorite = getFavoriteByUserIdAndDramaId(userId, dramaId);
            return favorite != null && favorite.getStatus() == 1;

        } catch (Exception e) {
            log.error("检查收藏状态异常，用户ID: {}, 短剧ID: {}", userId, dramaId, e);
            return false;
        }
    }

    @Override
    public IPage<MiniAppMiniDramaFavorite> getFavoritePage(IPage<MiniAppMiniDramaFavorite> page,
                                                           Integer userId,
                                                           Integer status) {
        return baseMapper.selectFavoritePage(page, userId, status);
    }

    @Override
    public MiniAppMiniDramaFavorite getFavoriteById(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        return getById(id);
    }

    @Override
    public MiniAppMiniDramaFavorite getFavoriteByUserIdAndDramaId(Integer userId, String albumId) {
        if (Objects.isNull(userId) || StringUtils.isEmpty(albumId)) {
            return null;
        }
        return lambdaQuery().eq(MiniAppMiniDramaFavorite::getUserId, userId)
                .eq(MiniAppMiniDramaFavorite::getAlbumId, albumId)
                .eq(MiniAppMiniDramaFavorite::getIsDeleted, 0)
                .eq(MiniAppMiniDramaFavorite::getStatus, 1)
                .last("limit 1")
                .orderByDesc(MiniAppMiniDramaFavorite::getCreateTime)
                .one();
    }

    @Override
    public List<MiniAppMiniDramaFavorite> getFavoriteByUserId(Integer userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        return lambdaQuery().eq(MiniAppMiniDramaFavorite::getUserId, userId)
                .eq(MiniAppMiniDramaFavorite::getIsDeleted, 0)
                .eq(MiniAppMiniDramaFavorite::getStatus, 1)
                .orderByDesc(MiniAppMiniDramaFavorite::getCreateTime)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFavorite(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new BusinessException("收藏记录ID不能为空");
        }

        try {
            boolean result = removeById(id);
            if (result) {
                log.info("删除收藏成功，收藏ID: {}", id);
            }
            return result;

        } catch (Exception e) {
            log.error("删除收藏异常，收藏ID: {}", id, e);
            throw new BusinessException("删除收藏失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteFavorites(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("收藏记录ID列表不能为空");
        }

        try {
            // 逻辑删除
            boolean result = lambdaUpdate()
                    .set(MiniAppMiniDramaFavorite::getIsDeleted, 1)
                    .set(MiniAppMiniDramaFavorite::getUpdateTime, new Date())
                    .in(MiniAppMiniDramaFavorite::getId, ids)
                    .update();

            if (result) {
                log.info("批量删除收藏成功，删除数量: {}", ids.size());
            }

            return result;

        } catch (Exception e) {
            log.error("批量删除收藏异常", e);
            throw new BusinessException("批量删除收藏失败: " + e.getMessage());
        }
    }


    /**
     * 转换MiniAppDramaEpisode为MiniAppDramaEpisodeVO
     */
    private MiniAppDramaEpisodeVO convertToEpisodeVO(MiniAppDramaEpisode episode) {
        MiniAppDramaEpisodeVO vo = new MiniAppDramaEpisodeVO();
        BeanUtils.copyProperties(episode, vo);

        // 设置权限相关信息（这里可以根据业务逻辑设置）
        vo.setHasPermissionView(true); // 默认有权限，实际应根据用户权限判断
        vo.setPermissionType(episode.getPayFlag() == 0 ? 1 : 4); // 免费或付费
        vo.setIsFavorited(true); // 在收藏列表中，默认已收藏

        return vo;
    }
}
