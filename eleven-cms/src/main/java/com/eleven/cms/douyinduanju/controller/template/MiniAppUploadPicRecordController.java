package com.eleven.cms.douyinduanju.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.dto.OpenApiRes;
import com.eleven.cms.douyinduanju.entity.OpenApiUploadPicRecord;
import com.eleven.cms.douyinduanju.service.IOpenApiUploadPicRecordService;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;

/**
 * @Description: openApi_upload_pic_record
 * @Author: jeecg-boot
 * @Date: 2025-05-23
 * @Version: V1.0
 */
@Api(tags = "openApi_upload_pic_record")
@RestController
@RequestMapping("/douyinduanju/openApiUploadPicRecord")
@Slf4j
public class MiniAppUploadPicRecordController extends JeecgController<OpenApiUploadPicRecord, IOpenApiUploadPicRecordService> {
    @Autowired
    private IOpenApiUploadPicRecordService miniAppUploadPicRecordService;

    /**
     * 分页列表查询
     *
     * @param openApiUploadPicRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "openApi_upload_pic_record-分页列表查询")
    @ApiOperation(value = "openApi_upload_pic_record-分页列表查询", notes = "openApi_upload_pic_record-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(OpenApiUploadPicRecord openApiUploadPicRecord, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<OpenApiUploadPicRecord> queryWrapper = new QueryWrapper<>();
        String[] titles = req.getParameterMap().get("title");
        if (titles != null && titles.length > 0) {
            queryWrapper.like("title", titles[0]);
        }
        String[] descriptions = req.getParameterMap().get("description");
        if (descriptions != null && descriptions.length > 0) {
            queryWrapper.like("description", descriptions[0]);
        }
        queryWrapper.orderByDesc("create_time");
        Page<OpenApiUploadPicRecord> page = new Page<OpenApiUploadPicRecord>(pageNo, pageSize);
        IPage<OpenApiUploadPicRecord> pageList = miniAppUploadPicRecordService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param openApiUploadPicRecord
     * @return
     */
    @AutoLog(value = "openApi_upload_pic_record-添加")
    @ApiOperation(value = "openApi_upload_pic_record-添加", notes = "openApi_upload_pic_record-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody OpenApiUploadPicRecord openApiUploadPicRecord) {

        if (StringUtils.isEmpty(openApiUploadPicRecord.getPicUrl())
                || StringUtils.isEmpty(openApiUploadPicRecord.getTitle())
                || StringUtils.isEmpty(openApiUploadPicRecord.getDescription())
        ) {
            return Result.error("图片地址，标题，描述不能为空");
        }
        miniAppUploadPicRecordService.save(openApiUploadPicRecord);
        return uploadPic(openApiUploadPicRecord.getId());
    }

    /**
     * 编辑
     *
     * @param openApiUploadPicRecord
     * @return
     */
    @AutoLog(value = "openApi_upload_pic_record-编辑")
    @ApiOperation(value = "openApi_upload_pic_record-编辑", notes = "openApi_upload_pic_record-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody OpenApiUploadPicRecord openApiUploadPicRecord) {
        miniAppUploadPicRecordService.updateById(openApiUploadPicRecord);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "openApi_upload_pic_record-通过id删除")
    @ApiOperation(value = "openApi_upload_pic_record-通过id删除", notes = "openApi_upload_pic_record-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniAppUploadPicRecordService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "openApi_upload_pic_record-批量删除")
    @ApiOperation(value = "openApi_upload_pic_record-批量删除", notes = "openApi_upload_pic_record-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.miniAppUploadPicRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "openApi_upload_pic_record-通过id查询")
    @ApiOperation(value = "openApi_upload_pic_record-通过id查询", notes = "openApi_upload_pic_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        OpenApiUploadPicRecord openApiUploadPicRecord = miniAppUploadPicRecordService.getById(id);
        if (openApiUploadPicRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(openApiUploadPicRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param openApiUploadPicRecord
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OpenApiUploadPicRecord openApiUploadPicRecord) {
        return super.exportXls(request, openApiUploadPicRecord, OpenApiUploadPicRecord.class, "openApi_upload_pic_record");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OpenApiUploadPicRecord.class);
    }

    @Resource
    private DouYinHttpUtil douYinHttpUtil;

    @GetMapping("/uploadPic")
    public Result<?> uploadPic(String id) {
        OpenApiUploadPicRecord picRecord = miniAppUploadPicRecordService.getById(id);
        if (Objects.equals(3, picRecord.getStatus())) {
            return Result.error("图片已上传");
        }
        if (StringUtils.isEmpty(picRecord.getPicUrl())) {
            return Result.error("图片地址不能为空");
        }
        OpenApiRes openApiRes = douYinHttpUtil.uploadPic(picRecord.getPicUrl());
        if (openApiRes.isSuccess()) {
            String openPicId = openApiRes.getOpenPicId();
            OpenApiUploadPicRecord update = new OpenApiUploadPicRecord();
            update.setId(id);
            update.setOpenPicId(openPicId);
            update.setStatus(3);
            miniAppUploadPicRecordService.updateById(update);
        } else {
            OpenApiUploadPicRecord update = new OpenApiUploadPicRecord();
            update.setId(id);
            update.setStatus(2);
            miniAppUploadPicRecordService.updateById(update);
            return Result.error(openApiRes.getErrMsg());
        }
        return Result.ok("上传成功");
    }

}
