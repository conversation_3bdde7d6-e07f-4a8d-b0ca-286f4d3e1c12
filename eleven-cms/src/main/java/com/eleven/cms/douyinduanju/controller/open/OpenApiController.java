package com.eleven.cms.douyinduanju.controller.open;

import cn.hutool.core.util.StrUtil;
import com.eleven.cms.douyinduanju.constant.DuanjuConstant;
import com.eleven.cms.douyinduanju.dto.ExternalMonthlySubCallbackDTO;
import com.eleven.cms.douyinduanju.dto.RefreshStatusDTO;
import com.eleven.cms.douyinduanju.service.IMiniAppDuanJuOrderService;
import com.eleven.cms.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisDistributeLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * 开放接口回调
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/openApi")
@Slf4j
public class OpenApiController {

    @Autowired
    private IMiniAppDuanJuOrderService miniAppDuanJuOrderService;

    @Autowired
    private RedisDistributeLock redisDistributeLock;

    /**
     * 外接计费代码回调
     */
    @PostMapping(value = "/order/external/monthly/package/callback")
    public Result<?> packageCallback(@RequestBody ExternalMonthlySubCallbackDTO dto) {
        log.info("{}-外接计费代码回调结果:{}", "外接计费代码", JacksonUtils.toJson(dto));
        if (StrUtil.isEmpty(dto.getOrderNo())) {
            return Result.ok("订单号不能为空");
        }

        redisDistributeLock.tryLock(DuanjuConstant.SUBSCRIBE_MEMBERSHIP_STATUS_LOCK, dto.getOrderNo(), () -> {

             miniAppDuanJuOrderService.handleExternalPhoneSubService(dto);
             return null;
        }, () -> {
            throw new JeecgBootException("订单处理错误,请重试");
        }, 10, TimeUnit.SECONDS);

        return Result.ok();
    }

    /**
     * 刷新开通状态
     */
    @PostMapping(value = "/refreshOpenStatus")
    public Result<?> refreshOpenStatus(@RequestBody RefreshStatusDTO dto) {
        log.info("{}-刷新开通状态:{}", "外接计费代码", JacksonUtils.toJson(dto));
        if (StrUtil.isEmpty(dto.getOrderNo())) {
            return Result.ok("外部订单号不能为空");
        }

        if (StrUtil.isEmpty(dto.getOrderNo())) {

            return Result.ok("订单号不能为空");
        }
        return Result.ok(redisDistributeLock.tryLock(DuanjuConstant.SUBSCRIBE_MEMBERSHIP_STATUS_LOCK, dto.getOrderNo(),
                () -> miniAppDuanJuOrderService.refreshSubscribeStatus(dto), () -> {
            throw new JeecgBootException("订单处理错误,请重试");
        }, 10, TimeUnit.SECONDS));
    }


}
