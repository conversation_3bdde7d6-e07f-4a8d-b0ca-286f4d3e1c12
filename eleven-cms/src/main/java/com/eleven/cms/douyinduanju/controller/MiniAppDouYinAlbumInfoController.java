package com.eleven.cms.douyinduanju.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.service.IOpenApiAlbumInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: openApi_album_info
 * @Author: jeecg-boot
 * @Date:   2025-05-22
 * @Version: V1.0
 */
@Api(tags="openApi_album_info")
@RestController
@RequestMapping("/cms.douyinduanjuan/miniAppDouYinAlbumInfo")
@Slf4j
public class MiniAppDouYinAlbumInfoController extends JeecgController<OpenApiAlbumInfo, IOpenApiAlbumInfoService> {
	@Autowired
	private IOpenApiAlbumInfoService miniAppDouYinAlbumInfoService;

	/**
	 * 分页列表查询
	 *
	 * @param openApiAlbumInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "openApi_album_info-分页列表查询")
	@ApiOperation(value="openApi_album_info-分页列表查询", notes="openApi_album_info-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(OpenApiAlbumInfo openApiAlbumInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<OpenApiAlbumInfo> queryWrapper = QueryGenerator.initQueryWrapper(openApiAlbumInfo, req.getParameterMap());
		Page<OpenApiAlbumInfo> page = new Page<OpenApiAlbumInfo>(pageNo, pageSize);
		IPage<OpenApiAlbumInfo> pageList = miniAppDouYinAlbumInfoService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 *   添加
	 *
	 * @param openApiAlbumInfo
	 * @return
	 */
	@AutoLog(value = "openApi_album_info-添加")
	@ApiOperation(value="openApi_album_info-添加", notes="openApi_album_info-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody OpenApiAlbumInfo openApiAlbumInfo) {
		miniAppDouYinAlbumInfoService.save(openApiAlbumInfo);
		return Result.ok("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param openApiAlbumInfo
	 * @return
	 */
	@AutoLog(value = "openApi_album_info-编辑")
	@ApiOperation(value="openApi_album_info-编辑", notes="openApi_album_info-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody OpenApiAlbumInfo openApiAlbumInfo) {
		miniAppDouYinAlbumInfoService.updateById(openApiAlbumInfo);
		return Result.ok("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "openApi_album_info-通过id删除")
	@ApiOperation(value="openApi_album_info-通过id删除", notes="openApi_album_info-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		miniAppDouYinAlbumInfoService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "openApi_album_info-批量删除")
	@ApiOperation(value="openApi_album_info-批量删除", notes="openApi_album_info-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.miniAppDouYinAlbumInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "openApi_album_info-通过id查询")
	@ApiOperation(value="openApi_album_info-通过id查询", notes="openApi_album_info-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		OpenApiAlbumInfo openApiAlbumInfo = miniAppDouYinAlbumInfoService.getById(id);
		if(openApiAlbumInfo ==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(openApiAlbumInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param openApiAlbumInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OpenApiAlbumInfo openApiAlbumInfo) {
        return super.exportXls(request, openApiAlbumInfo, OpenApiAlbumInfo.class, "openApi_album_info");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OpenApiAlbumInfo.class);
    }

}
