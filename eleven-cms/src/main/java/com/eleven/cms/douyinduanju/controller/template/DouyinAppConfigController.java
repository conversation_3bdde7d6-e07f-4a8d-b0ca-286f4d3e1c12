package com.eleven.cms.douyinduanju.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.dto.DouYinAppConfigVO;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.service.IDouyinAppConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: douyin_app_config
 * @Author: jeecg-boot
 * @Date: 2025-07-29
 * @Version: V1.0
 */
@Api(tags = "douyin_app_config")
@RestController
@RequestMapping("/douyinduanju/douyinAppConfig")
@Slf4j
public class DouyinAppConfigController extends JeecgController<DouyinAppConfig, IDouyinAppConfigService> {
    @Autowired
    private IDouyinAppConfigService douyinAppConfigService;

    /**
     * 分页列表查询
     *
     * @param douyinAppConfig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "douyin_app_config-分页列表查询")
    @ApiOperation(value = "douyin_app_config-分页列表查询", notes = "douyin_app_config-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DouyinAppConfig douyinAppConfig,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DouyinAppConfig> queryWrapper = QueryGenerator.initQueryWrapper(douyinAppConfig, req.getParameterMap());
        Page<DouyinAppConfig> page = new Page<DouyinAppConfig>(pageNo, pageSize);
        IPage<DouyinAppConfig> pageList = douyinAppConfigService.page(page, queryWrapper);

        List<DouYinAppConfigVO> douYinAppConfigList = new ArrayList<>();
        pageList.getRecords().forEach(item -> {
            DouYinAppConfigVO vo = new DouYinAppConfigVO();
            BeanUtils.copyProperties(item, vo);
            douYinAppConfigList.add(vo);
        });
        Page<DouYinAppConfigVO> pageData = new Page<>(pageNo, pageSize, pageList.getTotal());
        pageData.setRecords(douYinAppConfigList);
        return Result.ok(pageData);
    }

    /**
     * 添加
     *
     * @param douyinAppConfig
     * @return
     */
    @AutoLog(value = "douyin_app_config-添加")
    @ApiOperation(value = "douyin_app_config-添加", notes = "douyin_app_config-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DouyinAppConfig douyinAppConfig) {
        douyinAppConfigService.save(douyinAppConfig);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param douyinAppConfig
     * @return
     */
    @AutoLog(value = "douyin_app_config-编辑")
    @ApiOperation(value = "douyin_app_config-编辑", notes = "douyin_app_config-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DouyinAppConfig douyinAppConfig) {
        douyinAppConfigService.updateById(douyinAppConfig);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "douyin_app_config-通过id删除")
    @ApiOperation(value = "douyin_app_config-通过id删除", notes = "douyin_app_config-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        douyinAppConfigService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "douyin_app_config-批量删除")
    @ApiOperation(value = "douyin_app_config-批量删除", notes = "douyin_app_config-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.douyinAppConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "douyin_app_config-通过id查询")
    @ApiOperation(value = "douyin_app_config-通过id查询", notes = "douyin_app_config-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DouyinAppConfig douyinAppConfig = douyinAppConfigService.getById(id);
        if (douyinAppConfig == null) {
            return Result.error("未找到对应数据");
        }
        DouYinAppConfigVO vo = new DouYinAppConfigVO();
        BeanUtils.copyProperties(douyinAppConfig, vo);
        return Result.ok(vo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param douyinAppConfig
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DouyinAppConfig douyinAppConfig) {
        return super.exportXls(request, douyinAppConfig, DouyinAppConfig.class, "douyin_app_config");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DouyinAppConfig.class);
    }

}
