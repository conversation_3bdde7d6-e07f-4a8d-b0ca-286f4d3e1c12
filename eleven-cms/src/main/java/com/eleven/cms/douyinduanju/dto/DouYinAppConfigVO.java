package com.eleven.cms.douyinduanju.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class DouYinAppConfigVO {
    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 抖音appId
     */
    @Excel(name = "抖音appId", width = 15)
    @ApiModelProperty(value = "抖音appId")
    private String appId;

    @Excel(name = "app名称", width = 15)
    @ApiModelProperty(value = "app名称")
    private String appName;
    /**
     * 回调地址
     */
    private String callBackUrl;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "套餐模板id")
    private String packageTemplateId;

    @ApiModelProperty(value = "会员订单封面图")
    private String orderCoverUrl;

    @ApiModelProperty(value = "平台")
    @Dict(dicCode = "duanJu_mini_app_platform")
    private String platform;

}
