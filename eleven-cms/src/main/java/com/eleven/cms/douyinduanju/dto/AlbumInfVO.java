package com.eleven.cms.douyinduanju.dto;

import com.eleven.cms.douyinduanju.entity.MiniAppDuanjuTagRelate;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class AlbumInfVO {

    private String id;

    private String openApiAlbumInfoId;

    private String miniDramaId;


    /**
     * 栏目id
     */
    @ApiModelProperty(value = "栏目id")
    private String columnId;
    /**
     * 剧集名称
     */
    @Excel(name = "剧目名称", width = 20)
    @ApiModelProperty(value = "剧集名称")
    private String name;
    /**
     * 状态 0未生效  1:已生效
     */
    @Excel(name = "状态", width = 10, replace = {"未生效_0", "已生效_1"})
    @ApiModelProperty(value = "状态 0未生效  1:已生效")
    private Integer status;

    /**
     * 短剧信息主键
     */
    @ApiModelProperty(value = "短剧信息主键")
    private String albumInfoId;
    /**
     * 抖音开放平台短剧id
     */
    @Excel(name = "抖音短剧ID", width = 20)
    @ApiModelProperty(value = "抖音开放平台短剧id")
    private String albumId;

    /**
     * 免费集数
     */
    @Excel(name = "免费集数", width = 10)
    @ApiModelProperty(value = "免费集数")
    private Integer freeNum;

    /**
     * 排序编码 正序
     */
    @ApiModelProperty(value = "排序编码 正序")
    @Excel(name = "排序序号", width = 10)
    private Integer orderNo;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

    /**
     * 短剧标题
     */
    @Excel(name = "短剧标题", width = 15)
    @ApiModelProperty(value = "短剧标题")
    private String title;
    /**
     * 总集数
     */
    @Excel(name = "总集数", width = 15)
    @ApiModelProperty(value = "总集数")
    private Integer seqNum;
    private Integer perEpisodeCost;
    /**
     * 封面图列表
     */
    @Excel(name = "封面图列表", width = 15)
    @ApiModelProperty(value = "封面图列表")
    private String coverList;
    private String coverUrl;
    /**
     * 发行年份
     */
    @Excel(name = "发行年份", width = 12)
    @ApiModelProperty(value = "发行年份")
    private Integer year;
    /**
     * 短剧更新状态 - 1：未上映，- 2：更新中， - 3：已完结
     */
    @Excel(name = "短剧更新状态 - 1：未上映，- 2：更新中， - 3：已完结", width = 15)
    @ApiModelProperty(value = "短剧更新状态 - 1：未上映，- 2：更新中， - 3：已完结")
    private Integer albumStatus;

    @Excel(name = "上下架状态", width = 12, replace = {"未上下架_1", "上架_2", "下架_3"})
    @ApiModelProperty(value = "上下架状态:1:未上下架,2:上架,3:下架")
    private Integer onlineStatus;

    @Excel(name = "审核状态", width = 12)
    private Integer reviewStatus;

    @Excel(name = "1:未授权,2:已授权", width = 12)
    private Integer authorizeStatus;
    /**
     * 短剧推荐语（12 汉字以内）
     */
    @Excel(name = "短剧推荐语（12 汉字以内）", width = 15)
    @ApiModelProperty(value = "短剧推荐语（12 汉字以内）")
    private String recommendation;
    /**
     * 短剧简介（200 汉字以内
     */
    @Excel(name = "短剧简介（200 汉字以内", width = 15)
    @ApiModelProperty(value = "短剧简介（200 汉字以内")
    private String desp;
    /**
     * 短剧类目标签（1-3 个）
     */
    @Excel(name = "短剧类目标签（1-3 个）", width = 15)
    @ApiModelProperty(value = "短剧类目标签（1-3 个）")
    private String tagList;
    /**
     * 资质状态 - 1：未报审  - 2：报审通过 - 3：报审不通过  - 4：不建议报审
     */
    @Excel(name = "资质状态 - 1：未报审  - 2：报审通过 - 3：报审不通过  - 4：不建议报审", width = 15)
    @ApiModelProperty(value = "资质状态 - 1：未报审  - 2：报审通过 - 3：报审不通过  - 4：不建议报审")
    private Integer qualification;

    /**
     * 平均单集时长，单位分钟
     */
    @Excel(name = "平均单集时长，单位分钟", width = 15)
    @ApiModelProperty(value = "平均单集时长，单位分钟")
    private Integer duration;
    /**
     * 集数
     */
    @Excel(name = "集数", width = 10)
    @ApiModelProperty(value = "集数")
    private Integer seqCount;

    @Excel(name = "集数2", width = 10)
    private Integer seqsCount;
    /**
     * 制作机构
     */
    @Excel(name = "制作机构", width = 20)
    @ApiModelProperty(value = "制作机构")
    private String productionOrganisation;
    /**
     * 导演
     */
    @Excel(name = "导演", width = 15)
    @ApiModelProperty(value = "导演")
    private String director;
    /**
     * 制作人
     */
    @Excel(name = "制作人", width = 15)
    @ApiModelProperty(value = "制作人")
    private String producer;
    /**
     * 演员
     */
    @Excel(name = "演员", width = 25)
    @ApiModelProperty(value = "演员")
    private String actor;
    /**
     * 内容梗概（1000 汉字以内）
     */
    @Excel(name = "内容梗概", width = 25)
    @ApiModelProperty(value = "内容梗概（1000 汉字以内）")
    private String summary;
    /**
     * 成本配置比例情况
     */
    @Excel(name = "成本配置比例情况", width = 25)
    @ApiModelProperty(value = "成本配置比例情况")
    private String costDistributionUri;
    /**
     * 承诺书
     */
    @Excel(name = "承诺书", width = 25)
    @ApiModelProperty(value = "承诺书")
    private String assuranceUri;
    /**
     * 制作成本类型- 10：30万以下- 20：30～100万- 30：100万以上
     */
    @ApiModelProperty(value = "制作成本类型- 10：30万以下- 20：30～100万- 30：100万以上")
    @Excel(name = "制作成本类型", width = 25)
    private Integer playletProductionCost;
    /**
     * 编剧
     */
    @Excel(name = "编剧", width = 25)
    @ApiModelProperty(value = "编剧")
    private String screenWriter;
    /**
     * 许可证号
     */
    @Excel(name = "许可证号", width = 25)
    @ApiModelProperty(value = "许可证号")
    private String licenseNum;
    /**
     * 登记号
     */
    @ApiModelProperty(value = "登记号")
    @Excel(name = "登记号", width = 25)
    private String registrationNum;
    /**
     * 普通备案号
     */
    @Excel(name = "普通备案号", width = 25)
    @ApiModelProperty(value = "普通备案号")
    private String ordinaryRecordNum;
    /**
     * 重点备案号
     */
    @ApiModelProperty(value = "重点备案号")
    @Excel(name = "重点备案号", width = 25)
    private String keyRecordNum;
    /**
     * 备案号类型 - 10：普通备案号 - 20：重点备案号
     */
    @Excel(name = "备案号类型 - 10：普通备案号 - 20：重点备案号", width = 25)
    @ApiModelProperty(value = "备案号类型 - 10：普通备案号 - 20：重点备案号")
    private Integer recordType;
    /**
     * 备案号类型 - 10：普通备案号 - 20：重点备案号
     */
    @ApiModelProperty(value = "备案号类型 - 10：普通备案号 - 20：重点备案号")
    private String broadcastRecordNumber;

    /**
     * 所用的栏目
     */
    private String usageInfo;
    private String costUrl;
    private String rating;
    private BigDecimal hotValue;
    private List<MiniAppDuanjuTagRelate> duanJuTagList;

    private List<String> authorizeAppNameList;
}
