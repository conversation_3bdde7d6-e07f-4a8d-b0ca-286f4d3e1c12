package com.eleven.cms.douyinduanju.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 抖音支付回调消息内容DTO
 */
@Data
@ApiModel(value = "抖音支付回调消息内容", description = "支付回调msg字段解析后的内容")
public class PaymentCallbackMsg {

    /**
     * 小程序app_id
     */
    @JsonProperty("app_id")
    @ApiModelProperty(value = "小程序app_id")
    private String appId;

    /**
     * 开发者系统生成的订单号
     */
    @JsonProperty("out_order_no")
    @ApiModelProperty(value = "开发者系统生成的订单号")
    private String outOrderNo;

    /**
     * 抖音开放平台侧订单id
     */
    @JsonProperty("order_id")
    @ApiModelProperty(value = "抖音开放平台侧订单id")
    private String orderId;

    /**
     * 支付结果状态
     */
    @ApiModelProperty(value = "支付结果状态：SUCCESS-支付成功，CANCEL-支付取消")
    private String status;

    /**
     * 订单总金额，单位分
     */
    @JsonProperty("total_amount")
    @ApiModelProperty(value = "订单总金额，单位分")
    private Long totalAmount;

    /**
     * 订单优惠金额，单位分
     */
    @JsonProperty("discount_amount")
    @ApiModelProperty(value = "订单优惠金额，单位分")
    private Long discountAmount;

    /**
     * 支付渠道枚举
     */
    @JsonProperty("pay_channel")
    @ApiModelProperty(value = "支付渠道：1-微信，2-支付宝，10-抖音支付，20-抖音钻石")
    private Integer payChannel;

    /**
     * 渠道支付单号
     */
    @JsonProperty("channel_pay_id")
    @ApiModelProperty(value = "渠道支付单号，如微信/支付宝的支付单号")
    private String channelPayId;

    /**
     * 该笔交易的卖家商户号
     */
    @JsonProperty("merchant_uid")
    @ApiModelProperty(value = "该笔交易的卖家商户号")
    private String merchantUid;

    /**
     * 该笔交易取消原因
     */
    @ApiModelProperty(value = "该笔交易取消原因")
    private String message;

    /**
     * 用户支付成功/支付取消时间戳，单位为毫秒
     */
    @JsonProperty("event_time")
    @ApiModelProperty(value = "用户支付成功/支付取消时间戳，单位为毫秒")
    private Long eventTime;

    /**
     * 对应用户抖音账单里的"支付单号"
     */
    @JsonProperty("user_bill_pay_id")
    @ApiModelProperty(value = "对应用户抖音账单里的支付单号")
    private String userBillPayId;
}
