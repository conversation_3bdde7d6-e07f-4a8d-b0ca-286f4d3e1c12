package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: miniApp_duan_ju_order
 * @Author: jeecg-boot
 * @Date: 2025-06-06
 * @Version: V1.0
 */
@Data
@TableName("miniApp_duan_ju_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "miniapp_duan_ju_order对象", description = "miniApp_duan_ju_order")
public class MiniAppDuanJuOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 订单编号
     */
    @Excel(name = "订单编号", width = 15)
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @Excel(name = "小程序名称", width = 15)
    @Dict(dicCode = "duanJu_mini_app_code")
    private String businessType;

    private String model;

    /**
     * 订单流水id
     */
    @Excel(name = "支付流水号", width = 15)
    @ApiModelProperty(value = "订单流水id")
    private String transactionId;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "支付渠道：1-微信，2-支付宝，10-抖音支付，20-抖音钻石")
    private Integer payChannel;
    private String dramaId;
    private String albumId;
    private String albumName;
    private String extParams;
    private String coverUrl;

    /**
     * 套餐id
     */
    @Excel(name = "套餐id", width = 15)
    @ApiModelProperty(value = "套餐id")
    private String packageId;
    /**
     * 套餐名称
     */
    @Excel(name = "套餐名称", width = 15)
    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    /**
     * 订单类型   1:购买会员 2:购买剧集 3:充值K币
     */
    @Excel(name = "模板类型", width = 15)
    private Integer orderType;

    /**
     * 套餐解锁内容
     */
    @Excel(name = "解锁内容", width = 15)
    @ApiModelProperty(value = "套餐解锁内容")
    private String packageContent;
    /**
     * 订单金额
     */
    @Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;
    /**
     * 支付方式 1:抖音支付  2话费支付
     */
    @Excel(name = "付费方式: 1话费支付 2:抖音支付", width = 15)
    @ApiModelProperty(value = "付费方式: 1话费支付 2:抖音支付")
    private Integer payType;
    private Integer packageType;
    /**
     * 下单时间
     */
    @Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    /**
     * 支付时间
     */
    @Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
    /**
     * -1(支付失败),0(未支付),1(已支付),2(已取消)
     */
    @Excel(name = "-1(支付失败),0(未支付),1(已支付),2(已退款)", width = 15)
    @ApiModelProperty(value = "-1(支付失败),0(未支付),1(已支付),2(已取消),3(退款中),4(已退款)")
    private Integer payStatus;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;


    /**
     * 退款状态：0-未退款，1-退款中，2-退款成功，3-退款失败
     */
    @Excel(name = "退款状态", width = 15)
    @ApiModelProperty(value = "退款状态：0-未退款，1-退款中，2-退款成功，3-退款失败")
    private Integer refundStatus;

    /**
     * 退款金额
     */
    @Excel(name = "退款金额", width = 15)
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 退款原因
     */
    @Excel(name = "退款原因", width = 15)
    @ApiModelProperty(value = "退款原因")
    private String refundReason;

    /**
     * 退款时间
     */
    @Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退款时间")
    private Date refundTime;

    /**
     * 退款完成时间
     */
    @Excel(name = "退款完成时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退款完成时间")
    private Date refundCompleteTime;

    /**
     * 商户退款单号
     */
    @Excel(name = "商户退款单号", width = 15)
    @ApiModelProperty(value = "商户退款单号")
    private String outRefundNo;

    /**
     * 抖音退款ID
     */
    @ApiModelProperty(value = "抖音退款ID")
    private String refundId;

    /**
     * 退款失败原因
     */
    @ApiModelProperty(value = "退款失败原因")
    private String refundFailReason;

    private Integer reportStatus;
    private String adReportCallBackStatusStr;

    private String channel;

    private String adPromotionId;

    private String adProjectId;

    /**
     * 素材ID
     */
    private String creativeId;

    /**
     * 素材名称
     */
    private String cidName;

    /**
     * 是否首次付费
     */
    private Integer firstPay;
}
