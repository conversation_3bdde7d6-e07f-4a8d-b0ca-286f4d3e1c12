package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.dto.EpisodeReq;
import com.eleven.cms.douyinduanju.dto.EpisodeVO;
import com.eleven.cms.douyinduanju.dto.MiniAppDramaEpisodeResponse;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaEpisode;

import java.util.List;

/**
 * @Description: mini_app_drama_episode
 * @Author: jeecg-boot
 * @Date: 2025-06-06
 * @Version: V1.0
 */
public interface IMiniAppDramaEpisodeService extends IService<MiniAppDramaEpisode> {
    IPage<EpisodeVO> pageEpisode(EpisodeReq req, IPage<EpisodeVO> iPage);

    MiniAppDramaEpisode getByVideoRecordId(String videoRecordId);

    void syncOpenApiUploadVideoRecord(String id);

    List<String> getByVideoRecordId();

    /**
     * 根据albumId获取剧集列表
     *
     * @param albumId 抖音开放平台短剧ID
     * @return 剧集响应列表
     */
    List<MiniAppDramaEpisodeResponse> getByAlbumId(String albumId);

    /**
     * 根据albumId获取剧集列表（带用户收藏状态）
     *
     * @param albumId 抖音开放平台短剧ID
     * @param userId  用户ID
     * @return 剧集响应列表
     */
    List<MiniAppDramaEpisodeResponse> getByAlbumIdWithFavorite(String albumId, Integer userId);
}
