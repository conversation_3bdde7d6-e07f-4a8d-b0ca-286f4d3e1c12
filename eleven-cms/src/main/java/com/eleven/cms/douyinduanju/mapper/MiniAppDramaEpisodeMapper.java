package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eleven.cms.douyinduanju.dto.EpisodeReq;
import com.eleven.cms.douyinduanju.dto.EpisodeVO;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaEpisode;

import java.util.List;

/**
 * @Description: mini_app_drama_episode
 * @Author: jeecg-boot
 * @Date: 2025-06-06
 * @Version: V1.0
 */
public interface MiniAppDramaEpisodeMapper extends BaseMapper<MiniAppDramaEpisode> {
    IPage<EpisodeVO> pageEpisode(IPage<EpisodeVO> iPage, EpisodeReq req);

    List<String> getByVideoRecordId();

}
