package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * NAS文件记录实体类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@TableName("nas_file_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class NasFileRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;

    /**
     * 文件/目录名称
     */
    @Excel(name = "文件名称", width = 30)
    private String name;

    /**
     * 完整路径
     */
    @Excel(name = "完整路径", width = 50)
    private String fullPath;

    /**
     * 相对路径
     */
    @Excel(name = "相对路径", width = 40)
    private String relativePath;

    /**
     * 父级路径
     */
    @Excel(name = "父级路径", width = 40)
    private String parentPath;

    /**
     * 文件类型：directory-目录，file-文件
     */
    @Excel(name = "文件类型", width = 15)
    private String fileType;

    /**
     * 文件大小（字节）
     */
    @Excel(name = "文件大小", width = 15)
    private Long fileSize;

    /**
     * 文件扩展名
     */
    @Excel(name = "扩展名", width = 10)
    private String extension;

    /**
     * 最后修改时间
     */
    @Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date lastModified;

    /**
     * 扫描批次号
     */
    @Excel(name = "扫描批次", width = 20)
    private String scanBatch;

    /**
     * 扫描时间
     */
    @Excel(name = "扫描时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date scanTime;

    /**
     * 目录层级
     */
    @Excel(name = "目录层级", width = 10)
    private Integer level;
    /**
     * 父级路径名称
     */
    private String parentPathName;

    /**
     * 是否可读
     */
    private Integer readable;

    /**
     * 是否可写
     */
    private Integer writable;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedSize() {
        if (fileSize == null || fileSize == 0) {
            return "0 B";
        }

        if (fileSize < 1024) {
            return fileSize + " B";
        }
        if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        }
        if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024));
        }
        return String.format("%.1f GB", fileSize / (1024.0 * 1024 * 1024));
    }

    /**
     * 是否为目录
     */
    public boolean isDirectory() {
        return "directory".equals(fileType);
    }

    /**
     * 是否为文件
     */
    public boolean isFile() {
        return "file".equals(fileType);
    }
}
