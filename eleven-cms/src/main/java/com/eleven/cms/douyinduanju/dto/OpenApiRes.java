package com.eleven.cms.douyinduanju.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenApiRes {
    @SerializedName("err_no")
    private Integer errNo;
    @SerializedName("err_msg")
    private String errMsg;

    @SerializedName("log_id")
    private String logId;
    private String data;
    private String success;
    private String message;


    public boolean isSuccess() {
        return Objects.equals(0, errNo);
    }

    public static OpenApiRes fail(String errMsg) {
        return OpenApiRes.builder()
                .errMsg(errMsg)
                .build();
    }


    public boolean getTokenIsSuccess() {
        return StringUtils.equals(message, "success");
    }

    public String getToken() {
        JSONObject jsonObject = JSONObject.parseObject(data);
        return jsonObject.getString("access_token");
    }

    @Data
    public static class PlayInfo {
        private String definition;
        private String format;
        @SerializedName("play_url")
        private String playUrl;
        private Long size;

        @SerializedName("url_expire")
        private String urlExpire;
        private Integer bitrate;
        private String codec;
    }

    @Data
    public static class DouYinSessionRes {
        private String openid;
        private String sessionKey;
        private String unionId;
        private String anonymousOpenid;
    }

    public DouYinSessionRes getDouYinSessionRes() {
        JSONObject jsonObject = JSONObject.parseObject(data);
        OpenApiRes.DouYinSessionRes session = new OpenApiRes.DouYinSessionRes();
        session.setOpenid(jsonObject.getString("openid"));
        session.setSessionKey(jsonObject.getString("session_key"));
        session.setUnionId(jsonObject.getString("unionid"));
        session.setAnonymousOpenid(jsonObject.getString("anonymous_openid"));
        return session;
    }

    public OpenApiRes.PlayInfo getPlayInfo() {
        return JSONObject.parseObject(data, OpenApiRes.PlayInfo.class);
    }

    public OpenApiRes.EpisodeData getEpisodeData() {
        return JSONObject.parseObject(data, OpenApiRes.EpisodeData.class);
    }

    public String getAlbumId() {
        return JSONObject.parseObject(data).getString("album_id");
    }

    public String getOpenPicId() {
        return JSONObject.parseObject(data).getJSONObject("image_result").getString("open_pic_id");
    }

    public String getOpenVideoId() {
        return JSONObject.parseObject(data).getJSONObject("video_result").getString("open_video_id");
    }

    public String getOpenVideoStatus() {
        return JSONObject.parseObject(data).getString("status");
    }

    public String getDyCloudId() {
        return JSONObject.parseObject(data).getString("dy_cloud_id");
    }

    public OpenApiRes.OrderInfo getOrderInfo() {
        OpenApiRes.OrderInfo orderInfo = new OrderInfo();
        JSONObject orderJson = JSONObject.parseObject(data);
        orderInfo.setOrderId(orderJson.getString("order_id"));
        orderInfo.setOutOrderNo(orderJson.getString("out_order_no"));
        orderInfo.setAppId(orderJson.getString("app_id"));
        orderInfo.setPayStatus(orderJson.getString("pay_status"));
        orderInfo.setPayTime(orderJson.getString("pay_time"));
        orderInfo.setPayChannel(orderJson.getString("pay_channel"));
        orderInfo.setChannelPayId(orderJson.getString("channel_pay_id"));
        orderInfo.setTradeTime(orderJson.getLong("trade_time"));
        orderInfo.setTotalAmount(orderJson.getInteger("total_amount"));
        orderInfo.setDiscountAmount(orderJson.getInteger("discount_amount"));
        orderInfo.setMerchantUid(orderJson.getString("merchant_uid"));
        JSONArray itemOrderJsonList = orderJson.getJSONArray("item_order_list");
        List<OpenApiRes.OrderInfo.ItemOrder> itemOrderList = new ArrayList<>();
        for (int i = 0; i < itemOrderJsonList.size(); i++) {
            OpenApiRes.OrderInfo.ItemOrder itemOrder = new OpenApiRes.OrderInfo.ItemOrder();
            itemOrder.setItemOrderId(itemOrderJsonList.getJSONObject(i).getString("item_order_id"));
            itemOrder.setSkuId(itemOrderJsonList.getJSONObject(i).getString("sku_id"));
            itemOrder.setItemOrderAmount(itemOrderJsonList.getJSONObject(i).getInteger("item_order_amount"));
            itemOrderList.add(itemOrder);
        }
        orderInfo.setItemOrderList(itemOrderList);
        return orderInfo;
    }

    public OpenApiRes.CouponReceiveResult getCouponReceiveResult() {
        return JSONObject.parseObject(data, OpenApiRes.CouponReceiveResult.class);
    }


    @Data
    public static class EpisodeData {
        private String albumId;
        private String version;
        private Map<String, String> episodeIdMap;
    }


    @Data
    public static class OrderInfo {
        private String orderId;
        private String outOrderNo;
        private String appId;

        /**
         * PROCESS： 订单处理中 支付处理中
         * SUCCESS：成功 支付成功
         * FAIL：失败 支付失败 暂无该情况会支付失败
         * TIMEOUT：用户超时未支付
         */
        private String payStatus;
        private String payTime;
        private String payChannel;

        private String channelPayId;
        private Long tradeTime;
        private Integer totalAmount;
        private Integer discountAmount;
        private String merchantUid;
        private List<ItemOrder> itemOrderList;

        @Data
        public static class ItemOrder {
            private String itemOrderId;
            private String skuId;
            private Integer itemOrderAmount;
        }
    }

    /**
     * 抖音云优惠券领取结果
     */
    @Data
    public static class CouponReceiveResult {
        @SerializedName("coupon_receive_list")
        private List<CouponReceiveInfo> couponReceiveList;
    }

    /**
     * 抖音云优惠券信息
     */
    @Data
    public static class CouponReceiveInfo {
        /**
         * 优惠类型：1-满减券，2-折扣券
         */
        @SerializedName("discount_type")
        private Integer discountType;

        /**
         * 领取描述
         */

        @SerializedName("receive_desc")
        private String receiveDesc;

        /**
         * 有效结束时间（时间戳）
         */

        @SerializedName("valid_end_time")
        private Long validEndTime;

        /**
         * 达人开放ID
         */

        @SerializedName("talent_open_id")
        private String talentOpenId;

        /**
         * 有效开始时间（时间戳）
         */
        @SerializedName("valid_begin_time")
        private Long validBeginTime;

        /**
         * 消费路径
         */

        @SerializedName("consume_path")
        private String consumePath;

        /**
         * 优惠券ID
         */
        @SerializedName("coupon_id")
        private String couponId;

        /**
         * 优惠券状态：10-可使用，20-已使用，30-已过期
         */
        @SerializedName("coupon_status")
        private Integer couponStatus;

        /**
         * 优惠金额（分）
         */
        @SerializedName("discount_amount")
        private Integer discountAmount;

        /**
         * 最小支付金额（分）
         */
        @SerializedName("min_pay_amount")
        private Integer minPayAmount;

        /**
         * 使用描述
         */
        @SerializedName("consume_desc")
        private String consumeDesc;

        /**
         * 优惠券名称
         */
        @SerializedName("coupon_name")
        private String couponName;

        /**
         * 商户元数据编号
         */
        @SerializedName("merchant_meta_no")
        private String merchantMetaNo;

        /**
         * 达人账号
         */
        @SerializedName("talent_account")
        private String talentAccount;

        /**
         * 领取时间（时间戳）
         */
        @SerializedName("receive_time")
        private Long receiveTime;

        /**
         * 获取优惠类型描述
         */
        public String getDiscountTypeDesc() {
            if (discountType == null) {
                return "未知";
            }
            switch (discountType) {
                case 1:
                    return "满减券";
                case 2:
                    return "折扣券";
                default:
                    return "未知类型";
            }
        }

        /**
         * 获取优惠券状态描述
         */
        public String getCouponStatusDesc() {
            if (couponStatus == null) {
                return "未知";
            }
            switch (couponStatus) {
                case 10:
                    return "可使用";
                case 20:
                    return "已使用";
                case 30:
                    return "已过期";
                default:
                    return "未知状态";
            }
        }

        /**
         * 判断优惠券是否可用
         */
        public boolean isAvailable() {
            return couponStatus != null && couponStatus == 10;
        }

        /**
         * 获取优惠金额（元）
         */
        public Double getDiscountAmountInYuan() {
            return discountAmount != null ? discountAmount / 100.0 : 0.0;
        }

        /**
         * 获取最小支付金额（元）
         */
        public Double getMinPayAmountInYuan() {
            return minPayAmount != null ? minPayAmount / 100.0 : 0.0;
        }
    }


}
