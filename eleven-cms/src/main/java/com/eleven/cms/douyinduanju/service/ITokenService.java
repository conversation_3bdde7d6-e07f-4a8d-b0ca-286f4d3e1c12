package com.eleven.cms.douyinduanju.service;

import com.eleven.cms.douyinduanju.entity.DuanJuUser;

/**
 * Token服务接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface ITokenService {

    /**
     * 生成token
     *
     * @param user      用户信息
     * @param tokenType token类型
     * @return token字符串
     */
    String generateToken(DuanJuUser user, String tokenType);

    /**
     * 生成token（使用默认类型）
     *
     * @param user 用户信息
     * @return token字符串
     */
    String generateToken(DuanJuUser user);

    /**
     * 校验token有效性
     *
     * @param token     token字符串
     * @param tokenType token类型
     * @return 是否有效
     */
    boolean validateToken(String token, String tokenType);

    /**
     * 校验token有效性（使用默认类型）
     *
     * @param token token字符串
     * @return 是否有效
     */
    boolean validateToken(String token);

    /**
     * 从token中获取用户信息
     *
     * @param token     token字符串
     * @param tokenType token类型
     * @return 用户信息
     */
    DuanJuUser getUserFromToken(String token, String tokenType);

    /**
     * 从token中获取用户信息（使用默认类型）
     *
     * @param token token字符串
     * @return 用户信息
     */
    DuanJuUser getUserFromToken(String token);

    /**
     * 刷新token
     *
     * @param token     原token
     * @param tokenType token类型
     * @return 新token
     */
    String refreshToken(String token, String tokenType);

    /**
     * 刷新token（使用默认类型）
     *
     * @param token 原token
     * @return 新token
     */
    String refreshToken(String token);

    /**
     * 删除token（登出）
     *
     * @param token     token字符串
     * @param tokenType token类型
     */
    void removeToken(String token, String tokenType);

    /**
     * 删除token（登出，使用默认类型）
     *
     * @param token token字符串
     */
    void removeToken(String token);

    /**
     * 清除指定用户的所有token
     *
     * @param userId    用户ID
     * @param tokenType token类型
     */
    void removeUserAllTokens(String userId, String tokenType);

    /**
     * 清除指定用户的所有token（使用默认类型）
     *
     * @param userId 用户ID
     */
    void removeUserAllTokens(String userId);
}
