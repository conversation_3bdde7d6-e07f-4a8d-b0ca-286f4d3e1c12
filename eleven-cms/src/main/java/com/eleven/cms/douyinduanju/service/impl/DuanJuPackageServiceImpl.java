package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.DuanJuPackage;
import com.eleven.cms.douyinduanju.mapper.DuanJuPackageMapper;
import com.eleven.cms.douyinduanju.service.IDuanJuPackageService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class DuanJuPackageServiceImpl extends ServiceImpl<DuanJuPackageMapper, DuanJuPackage> implements IDuanJuPackageService {

    @Override
    public List<DuanJuPackage> listPackageByProvince(String provinceName, String model, List<Integer> typeList) {
        return this.baseMapper.listPackageByProvince(provinceName, model, typeList);
    }

    @Override
    public List<DuanJuPackage> getPackageByProvince(String provinceName, String model, List<Integer> typeList) {
        return this.baseMapper.getPackageByProvince(provinceName, model, typeList);
    }

    @Override
    public int updateByIdWithNull(DuanJuPackage duanJuPackage) {
        return this.baseMapper.updateByIdWithNull(duanJuPackage);
    }
}
