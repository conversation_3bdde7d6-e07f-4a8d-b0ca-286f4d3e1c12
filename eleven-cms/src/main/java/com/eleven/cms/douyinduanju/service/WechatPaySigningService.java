package com.eleven.cms.douyinduanju.service;

import com.eleven.cms.douyinduanju.dto.WechatPaySigningResponse;

import java.util.Map;

/**
 * 微信支付小程序签约服务接口
 */
public interface WechatPaySigningService {

    /**
     * 创建签约请求
     *
     * @param userId    用户ID
     * @param openid    用户openid
     * @param planId    模板ID
     * @param notifyUrl 回调通知地址
     * @param returnWeb 签约完成跳转页面
     * @return 签约跳转URL
     */
    String createSigningRequest(String userId, String openid, String planId, String notifyUrl, String returnWeb);

    /**
     * 处理签约回调
     *
     * @param callbackParams 回调参数
     * @return 处理结果
     */
    boolean handleSigningCallback(Map<String, String> callbackParams);

    /**
     * 查询签约状态
     *
     * @param userId       用户ID
     * @param contractCode 签约协议号
     * @return 签约状态信息
     */
    WechatPaySigningResponse querySigningStatus(String userId, String contractCode);

    /**
     * 解约
     *
     * @param userId       用户ID
     * @param contractCode 签约协议号
     * @return 解约结果
     */
    boolean cancelSigning(String userId, String contractCode);

    /**
     * 检查用户是否已签约
     *
     * @param userId 用户ID
     * @return 是否已签约
     */
    boolean isUserSigned(String userId);

    /**
     * 获取用户签约信息
     *
     * @param userId 用户ID
     * @return 签约信息
     */
    WechatPaySigningResponse getUserSigningInfo(String userId);
}
