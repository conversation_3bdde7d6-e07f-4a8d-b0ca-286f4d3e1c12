package com.eleven.cms.douyinduanju.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.LookHistoryVO;
import com.eleven.cms.douyinduanju.dto.ViewingHistoryReq;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaEpisode;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaViewingHistory;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.mapper.MiniAppDramaViewingHistoryMapper;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaEpisodeService;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaViewingHistoryService;
import com.eleven.cms.douyinduanju.service.IMiniAppMiniDramaService;
import com.eleven.cms.douyinduanju.service.IOpenApiAlbumInfoService;
import com.eleven.cms.douyinduanju.util.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: mini_app_drama_viewing_history
 * @Author: jeecg-boot
 * @Date: 2025-06-03
 * @Version: V1.0
 */
@Slf4j
@Service
public class MiniAppDramaViewingHistoryServiceImpl extends ServiceImpl<MiniAppDramaViewingHistoryMapper, MiniAppDramaViewingHistory> implements IMiniAppDramaViewingHistoryService {

    @Resource
    IMiniAppDramaEpisodeService episodeService;

    @Resource
    IMiniAppMiniDramaService miniAppMiniDramaService;

    @Resource
    IOpenApiAlbumInfoService openApiAlbumInfoService;

    @Override
    public String saveViewingHistoryByAlbumId(String albumId) {
        return "";
    }

    @Override
    public MiniAppDramaViewingHistory addOrUpdateViewingHistory(ViewingHistoryReq req) {
        Integer currentUserId = TokenUtils.getCurrentUserId();
        MiniAppDramaEpisode episode = episodeService.lambdaQuery().eq(MiniAppDramaEpisode::getDouYinEpisodeId, req.getDouYinEpisodeId()).last("limit 1").one();

        OpenApiAlbumInfo apiAlbumInfo = openApiAlbumInfoService.getByAlbumId(episode.getAlbumId());
        MiniAppMiniDrama drama = miniAppMiniDramaService.getByAlbumId(episode.getAlbumId());

        MiniAppDramaViewingHistory miniAppDramaViewingHistory = new MiniAppDramaViewingHistory();
        miniAppDramaViewingHistory.setDouYinEpisodeId(req.getDouYinEpisodeId());
        miniAppDramaViewingHistory.setAlbumId(req.getAlbumId());
        miniAppDramaViewingHistory.setEpisodeNum(episode.getEpisodeSeq());
        miniAppDramaViewingHistory.setUserId(currentUserId);
        miniAppDramaViewingHistory.setOpenId(req.getOpenId());
        miniAppDramaViewingHistory.setWatchDuration(req.getWatchDuration());
        miniAppDramaViewingHistory.setCoverUrl(drama.getCoverUrl());
        miniAppDramaViewingHistory.setSeqCount(apiAlbumInfo.getSeqCount());
        miniAppDramaViewingHistory.setAlbumName(apiAlbumInfo.getName());

        MiniAppDramaViewingHistory history = lambdaQuery().eq(MiniAppDramaViewingHistory::getUserId, currentUserId)
                .eq(MiniAppDramaViewingHistory::getDouYinEpisodeId, req.getDouYinEpisodeId())
                .last("limit 1").one();

        if (Objects.nonNull(history)) {
            miniAppDramaViewingHistory.setId(history.getId());
            updateById(miniAppDramaViewingHistory);
        } else {
            save(miniAppDramaViewingHistory);
        }
        return miniAppDramaViewingHistory;
    }


    @Override
    public List<LookHistoryVO> getLastHistory(Page<LookHistoryVO> page, Integer userId) {
        List<String> albumIdList = this.baseMapper.getLastHistory(userId);
        if (CollectionUtil.isEmpty(albumIdList)) {
            return new ArrayList<>();
        }
        List<LookHistoryVO> lastHistory = this.baseMapper.getHistoryByAlbumId(albumIdList);
        try {
            // 1. 批量查询短剧信息
            List<MiniAppMiniDrama> dramaList = miniAppMiniDramaService.lambdaQuery()
                    .in(MiniAppMiniDrama::getAlbumId, albumIdList)
                    .list();
            Map<String, MiniAppMiniDrama> dramaMap = dramaList.stream()
                    .filter(drama -> drama != null && StringUtils.isNotEmpty(drama.getAlbumId()))
                    .collect(Collectors.toMap(
                            MiniAppMiniDrama::getAlbumId,
                            drama -> drama,
                            (existing, replacement) -> existing
                    ));

            List<LookHistoryVO> maxSeqHistory = this.baseMapper.getMaxSeqHistory(userId);
            Map<String, LookHistoryVO> maxSeqMap = maxSeqHistory.stream()
                    .filter(history -> history != null && StringUtils.isNotEmpty(history.getAlbumId()))
                    .collect(Collectors.toMap(
                            LookHistoryVO::getAlbumId,
                            history -> history,
                            (existing, replacement) -> existing
                    ));

            // 6. 填充历史记录信息
            fillHistoryDetails(lastHistory, dramaMap, maxSeqMap);

        } catch (Exception e) {
            log.error("获取观看历史详情异常，用户ID: {}", userId, e);
            // 发生异常时返回基础历史记录，不影响主要功能
        }

        return lastHistory;
    }


    /**
     * 填充历史记录详细信息
     */
    private void fillHistoryDetails(List<LookHistoryVO> lastHistory,
                                    Map<String, MiniAppMiniDrama> dramaMap,
                                    Map<String, LookHistoryVO> maxSeqMap) {

        for (LookHistoryVO history : lastHistory) {
            if (history == null || StringUtils.isEmpty(history.getAlbumId())) {
                continue;
            }

            try {
                // 设置短剧信息
                MiniAppMiniDrama drama = dramaMap.get(history.getAlbumId());
                if (drama != null && drama.getHotValue() != null) {
                    history.setViewCount(drama.getHotValue());
                    history.setAlbumName(drama.getName());
                    history.setCoverUrl(drama.getCoverUrl());
                }
                // 设置最大观看集数
                LookHistoryVO maxSeqHistory = maxSeqMap.get(history.getAlbumId());
                if (maxSeqHistory != null && maxSeqHistory.getEpisodeNum() != null) {
                    history.setViewMaxSeq(maxSeqHistory.getEpisodeNum());
                    history.setViewMaxSeqEpisodeId(maxSeqHistory.getDouYinEpisodeId());
                }

            } catch (Exception e) {
                log.warn("填充历史记录详情失败，albumId: {}", history.getAlbumId(), e);

            }
        }
    }

    /**
     * 获取观看最新列表
     */
    public List<LookHistoryVO> getMaxSeqHistory(Integer userId) {
        return this.baseMapper.getMaxSeqHistory(userId);
    }

    @Override
    public List<LookHistoryVO> getLatestHistoryForEachDrama(Integer userId) {
        if (userId == null) {
            log.warn("用户ID不能为空");
            return new ArrayList<>();
        }

        try {
            // 获取每个剧目的最新观看历史记录
            List<LookHistoryVO> latestHistoryList = this.baseMapper.getLatestHistoryForEachDrama(userId);

            if (CollectionUtil.isEmpty(latestHistoryList)) {
                return new ArrayList<>();
            }

            // 提取albumId列表
            List<String> albumIdList = latestHistoryList.stream()
                    .map(LookHistoryVO::getAlbumId)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(albumIdList)) {
                return latestHistoryList;
            }

            // 批量查询短剧信息
            List<MiniAppMiniDrama> dramaList = miniAppMiniDramaService.lambdaQuery()
                    .in(MiniAppMiniDrama::getAlbumId, albumIdList)
                    .list();

            Map<String, MiniAppMiniDrama> dramaMap = dramaList.stream()
                    .filter(drama -> drama != null && StringUtils.isNotEmpty(drama.getAlbumId()))
                    .collect(Collectors.toMap(
                            MiniAppMiniDrama::getAlbumId,
                            drama -> drama,
                            (existing, replacement) -> existing
                    ));

            // 获取每个剧目的最大观看集数
            List<LookHistoryVO> maxSeqHistory = this.baseMapper.getMaxSeqHistory(userId);
            Map<String, LookHistoryVO> maxSeqMap = maxSeqHistory.stream()
                    .filter(history -> history != null && StringUtils.isNotEmpty(history.getAlbumId()))
                    .collect(Collectors.toMap(
                            LookHistoryVO::getAlbumId,
                            history -> history,
                            (existing, replacement) -> existing
                    ));

            // 填充详细信息
            fillHistoryDetails(latestHistoryList, dramaMap, maxSeqMap);

            return latestHistoryList;

        } catch (Exception e) {
            log.error("获取用户每个剧目最新观看历史记录异常，用户ID: {}", userId, e);
            return new ArrayList<>();
        }
    }
}
