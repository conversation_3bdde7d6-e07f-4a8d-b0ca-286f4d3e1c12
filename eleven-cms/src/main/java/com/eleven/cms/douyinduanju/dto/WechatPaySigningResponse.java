package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信支付小程序签约响应DTO
 */
@Data
@ApiModel(value = "微信支付小程序签约响应", description = "微信支付小程序签约响应结果")
public class WechatPaySigningResponse {

    @ApiModelProperty(value = "返回状态码")
    private String returnCode;

    @ApiModelProperty(value = "返回信息")
    private String returnMsg;

    @ApiModelProperty(value = "小程序AppID")
    private String appid;

    @ApiModelProperty(value = "商户号")
    private String mchid;

    @ApiModelProperty(value = "设备号")
    private String deviceInfo;

    @ApiModelProperty(value = "随机字符串")
    private String nonceStr;

    @ApiModelProperty(value = "签名")
    private String sign;

    @ApiModelProperty(value = "业务结果")
    private String resultCode;

    @ApiModelProperty(value = "错误代码")
    private String errCode;

    @ApiModelProperty(value = "错误代码描述")
    private String errCodeDes;

    @ApiModelProperty(value = "模板ID")
    private String planId;

    @ApiModelProperty(value = "签约协议号")
    private String contractCode;

    @ApiModelProperty(value = "请求签约的用户openid")
    private String openid;

    @ApiModelProperty(value = "签约ID")
    private String contractId;

    @ApiModelProperty(value = "商户签约号")
    private String outContractCode;

    @ApiModelProperty(value = "签约状态")
    private String contractState;

    @ApiModelProperty(value = "签约显示账户")
    private String contractDisplayAccount;

    @ApiModelProperty(value = "签约时间")
    private String contractSignTime;

    @ApiModelProperty(value = "签约到期时间")
    private String contractExpiredTime;

    @ApiModelProperty(value = "签约银行类型")
    private String contractBankType;

    @ApiModelProperty(value = "通知时间")
    private String timeStamp;

    /**
     * 判断签约是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(returnCode) && "SUCCESS".equals(resultCode);
    }

    /**
     * 判断签约状态是否为已签约
     */
    public boolean isSigned() {
        return "ADD".equals(contractState);
    }
}
