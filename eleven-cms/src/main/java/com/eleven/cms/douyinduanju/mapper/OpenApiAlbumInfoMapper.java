package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import org.apache.ibatis.annotations.Param;


public interface OpenApiAlbumInfoMapper extends BaseMapper<OpenApiAlbumInfo> {

    /**
     * 更新短剧专辑信息（包括空值字段）
     *
     * @param openApiAlbumInfo 短剧专辑对象
     * @return 更新行数
     */
    int updateByIdWithNull(@Param("albumInfo") OpenApiAlbumInfo openApiAlbumInfo);

}
