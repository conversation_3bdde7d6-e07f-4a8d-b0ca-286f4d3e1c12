package com.eleven.cms.douyinduanju.controller;

import com.eleven.cms.douyinduanju.annotation.TokenRequired;
import com.eleven.cms.douyinduanju.dto.WechatPaySigningResponse;
import com.eleven.cms.douyinduanju.service.WechatPaySigningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付小程序签约控制器
 */
@Api(tags = "微信支付小程序签约管理")
@RestController
@RequestMapping("/douyinduanju/wechat-pay-signing")
@Slf4j
public class WechatPaySigningController {

    @Autowired
    private WechatPaySigningService wechatPaySigningService;

    /**
     * 创建签约请求
     */
    @ApiOperation(value = "创建签约请求", notes = "为用户创建微信支付小程序签约请求")
    @PostMapping("/create")
    @TokenRequired
    public Result<String> createSigningRequest(
            @ApiParam(value = "用户openid", required = true)
            @RequestParam String openid,
            @ApiParam(value = "模板ID", required = true)
            @RequestParam String planId,
            @ApiParam(value = "回调通知地址", required = true)
            @RequestParam String notifyUrl,
            @ApiParam(value = "签约完成跳转页面", required = true)
            @RequestParam String returnWeb) {

        try {
            // 从Token中获取当前用户ID（需要根据实际的Token解析逻辑调整）
            String userId = getCurrentUserId();

            if (userId == null) {
                return Result.error("用户未登录");
            }

            String signingUrl = wechatPaySigningService.createSigningRequest(userId, openid, planId, notifyUrl, returnWeb);

            Map<String, Object> result = new HashMap<>();
            result.put("signingUrl", signingUrl);
            result.put("userId", userId);
            result.put("openid", openid);

            log.info("创建签约请求成功，用户ID: {}, 签约URL: {}", userId, signingUrl);
            return Result.ok("创建签约请求成功", result);

        } catch (Exception e) {
            log.error("创建签约请求失败", e);
            return Result.error("创建签约请求失败: " + e.getMessage());
        }
    }

    /**
     * 签约回调处理
     */
    @ApiOperation(value = "签约回调处理", notes = "处理微信支付签约回调通知")
    @PostMapping("/callback")
    public String handleSigningCallback(HttpServletRequest request) {
        try {
            log.info("收到微信支付签约回调通知");

            // 获取回调参数
            Map<String, String> callbackParams = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String paramName = parameterNames.nextElement();
                String paramValue = request.getParameter(paramName);
                callbackParams.put(paramName, paramValue);
            }

            log.info("签约回调参数: {}", callbackParams);

            // 处理回调
            boolean success = wechatPaySigningService.handleSigningCallback(callbackParams);

            if (success) {
                log.info("签约回调处理成功");
                return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
            } else {
                log.error("签约回调处理失败");
                return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>";
            }

        } catch (Exception e) {
            log.error("处理签约回调异常", e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>";
        }
    }

    /**
     * 查询签约状态
     */
    @ApiOperation(value = "查询签约状态", notes = "查询用户的签约状态")
    @GetMapping("/status")
    @TokenRequired
    public Result<WechatPaySigningResponse> querySigningStatus(
            @ApiParam(value = "签约协议号")
            @RequestParam(required = false) String contractCode) {

        try {
            String userId = getCurrentUserId();

            if (userId == null) {
                return Result.error("用户未登录");
            }

            WechatPaySigningResponse signingInfo;
            if (contractCode != null && !contractCode.trim().isEmpty()) {
                // 查询指定签约协议号的状态
                signingInfo = wechatPaySigningService.querySigningStatus(userId, contractCode);
            } else {
                // 查询用户最新的签约信息
                signingInfo = wechatPaySigningService.getUserSigningInfo(userId);
            }

            if (signingInfo == null) {
                return Result.error("未找到签约信息");
            }

            log.info("查询签约状态成功，用户ID: {}, 签约状态: {}", userId, signingInfo.getContractState());
            return Result.ok("查询签约状态成功", signingInfo);

        } catch (Exception e) {
            log.error("查询签约状态失败", e);
            return Result.error("查询签约状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否已签约
     */
    @ApiOperation(value = "检查签约状态", notes = "检查用户是否已签约")
    @GetMapping("/check")
    @TokenRequired
    public Result<Object> checkSigningStatus() {

        try {
            String userId = getCurrentUserId();

            if (userId == null) {
                return Result.error("用户未登录");
            }

            boolean isSigned = wechatPaySigningService.isUserSigned(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("isSigned", isSigned);
            result.put("status", isSigned ? "已签约" : "未签约");

            log.info("检查签约状态完成，用户ID: {}, 是否已签约: {}", userId, isSigned);
            return Result.ok("检查签约状态成功", result);

        } catch (Exception e) {
            log.error("检查签约状态失败", e);
            return Result.error("检查签约状态失败: " + e.getMessage());
        }
    }

    /**
     * 解约
     */
    @ApiOperation(value = "解约", notes = "用户解约操作")
    @PostMapping("/cancel")
    @TokenRequired
    public Result<Object> cancelSigning(
            @ApiParam(value = "签约协议号", required = true)
            @RequestParam String contractCode) {

        try {
            String userId = getCurrentUserId();

            if (userId == null) {
                return Result.error("用户未登录");
            }

            boolean success = wechatPaySigningService.cancelSigning(userId, contractCode);

            if (success) {
                Map<String, Object> result = new HashMap<>();
                result.put("userId", userId);
                result.put("contractCode", contractCode);
                result.put("status", "解约成功");

                log.info("解约成功，用户ID: {}, 签约协议号: {}", userId, contractCode);
                return Result.ok("解约成功", result);
            } else {
                return Result.error("解约失败，请检查签约状态");
            }

        } catch (Exception e) {
            log.error("解约失败", e);
            return Result.error("解约失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户签约信息
     */
    @ApiOperation(value = "获取签约信息", notes = "获取用户的详细签约信息")
    @GetMapping("/info")
    @TokenRequired
    public Result<WechatPaySigningResponse> getUserSigningInfo() {

        try {
            String userId = getCurrentUserId();

            if (userId == null) {
                return Result.error("用户未登录");
            }

            WechatPaySigningResponse signingInfo = wechatPaySigningService.getUserSigningInfo(userId);

            if (signingInfo == null) {
                return Result.error("用户未签约或签约信息不存在");
            }

            log.info("获取用户签约信息成功，用户ID: {}", userId);
            return Result.ok("获取签约信息成功", signingInfo);

        } catch (Exception e) {
            log.error("获取用户签约信息失败", e);
            return Result.error("获取签约信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID（需要根据实际的Token解析逻辑实现）
     */
    private String getCurrentUserId() {
        // TODO: 实现从Token中获取用户ID的逻辑
        // 这里需要根据项目中实际的Token解析方式来实现
        // 示例：
        // return TokenUtil.getCurrentUserId();

        // 临时返回示例用户ID，实际需要替换
        return "user123";
    }
}
