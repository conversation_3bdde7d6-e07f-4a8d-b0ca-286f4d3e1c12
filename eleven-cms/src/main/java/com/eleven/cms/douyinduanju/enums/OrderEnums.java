package com.eleven.cms.douyinduanju.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 订单相关枚举类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public class OrderEnums {

    @Getter
    @AllArgsConstructor
    public enum MembershipStatus {
        NON_MEMBER(1, "非会员"),
        SUB(2, "订阅会员");

        public MembershipStatus getByCode(Integer code) {
            return Arrays.stream(values())
                    .filter(type -> type.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }

        private final Integer code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum PayChnnelEnum {

        ALI_PAY(1, "微信"),
        WECHAT(2, "支付宝"),
        DOU_YIN(10, "抖音支付"),
        DOU_YIN_ZONE(20, "抖音砖石"),
        MOBILE(80, "话费");


        public static PayChnnelEnum getByCode(Integer code) {
            return Arrays.stream(values())
                    .filter(type -> type.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }

        private final Integer code;
        private final String desc;
    }

    /**
     * 套餐类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PackageType {
        MEMBERSHIP(1, "会员"),
        DRAMA_CARD(2, "剧卡"),
        K_COIN(3, "K币");

        private final Integer code;
        private final String desc;

        public static PackageType getByCode(Integer code) {
            if (code == null) return null;
            for (PackageType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 支付方式枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PayType {
        DOUYIN_PAY(1, "抖音支付"),
        PHONE_PAY(2, "话费支付");

        private final Integer code;
        private final String desc;

        public static PayType getByCode(Integer code) {
            if (code == null) return null;
            for (PayType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 支付状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PayStatus {
        PAID(1, "已支付"),
        FAILED(-1, "支付失败"),
        UNPAID(0, "未支付"),
        CANCEL(2, "已取消"),
        REFUNDING(3, "退款中"),
        REFUNDED(4, "已退款");

        private final Integer code;
        private final String desc;

        public static PayStatus getByCode(Integer code) {
            if (code == null) return null;
            for (PayStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 会员类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum MembershipType {
        THIRD_PARTY_PAY(3, 4, "三方支付会员"),
        SUB(2, 1, "订阅会员");

        private final Integer code;
        private final Integer packageTypeCode;
        private final String desc;

        public static MembershipType getByCode(Integer code) {
            if (code == null) return null;
            for (MembershipType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }

        public static MembershipType getByPackageTypeCode(Integer code) {
            if (code == null) return null;
            for (MembershipType type : values()) {
                if (type.getPackageTypeCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 权益状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum BenefitStatus {
        INACTIVE(0, "未生效"),
        ACTIVE(1, "生效中"),
        EXPIRED(2, "已过期"),
        CANCELLED(3, "已取消"),
        REFUNDED(4, "已退款"),
        USED(6, "已使用");

        private final Integer code;
        private final String desc;

        public static BenefitStatus getByCode(Integer code) {
            if (code == null) return null;
            for (BenefitStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * K币交易类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum KCoinTransactionType {
        RECHARGE(1, "充值"),
        CONSUME(2, "消费"),
        REFUND(3, "退款"),
        GIFT(4, "赠送"),
        EXPIRE_DEDUCT(5, "过期扣除");

        private final Integer code;
        private final String desc;

        public static KCoinTransactionType getByCode(Integer code) {
            if (code == null) return null;
            for (KCoinTransactionType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 业务类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum BusinessType {
        BUY_EPISODE(1, "购买剧集"),
        BUY_MEMBERSHIP(2, "购买会员"),
        RECHARGE_KCOIN(3, "充值K币"),
        SYSTEM_GIFT(4, "系统赠送"),
        GENERATE_EPISODE_CARD(5, "生成单集权益卡");

        private final Integer code;
        private final String desc;

        public static BusinessType getByCode(Integer code) {
            if (code == null) return null;
            for (BusinessType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 交易状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum TransactionStatus {
        PROCESSING(0, "处理中"),
        SUCCESS(1, "成功"),
        FAILED(2, "失败"),
        CANCELLED(3, "已撤销");

        private final Integer code;
        private final String desc;

        public static TransactionStatus getByCode(Integer code) {
            if (code == null) return null;
            for (TransactionStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
