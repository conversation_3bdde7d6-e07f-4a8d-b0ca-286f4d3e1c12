package com.eleven.cms.douyinduanju.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.CouponMessage;
import com.eleven.cms.douyinduanju.dto.OpenApiRes;
import com.eleven.cms.douyinduanju.entity.*;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.mapper.MiniAppCouponRecordMapper;
import com.eleven.cms.douyinduanju.service.*;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
public class MiniAppCouponRecordServiceImpl extends ServiceImpl<MiniAppCouponRecordMapper, MiniAppCouponRecord> implements IMiniAppCouponRecordService {


    public boolean add(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        String msgJsonStr = (String) jsonObject.get("msg");
        CouponMessage.MsgData msg = JSONObject.parseObject(msgJsonStr, CouponMessage.MsgData.class);


        MiniAppCouponRecord couponRecord = new MiniAppCouponRecord();
        BeanUtils.copyProperties(msg, couponRecord);
        couponRecord.setCallbackData(json);

        MiniAppCouponRecord byCouponId = getByCouponId(msg.getCouponId());
        if (Objects.nonNull(byCouponId)) {
            log.warn("优惠券记录已存在:{}", msg.getCouponId());
            return false;
        }


        try {
            Long receiveTime = msg.getReceiveTime();
            Date receiveTimeDate = new Date(receiveTime * 1000L);
            couponRecord.setReceiveTime(receiveTimeDate);
        } catch (Exception e) {
            log.info("时间转换异常{}", json, e);
        }

        try {
            Long validBeginTime = msg.getValidBeginTime();
            Date validBeginTimeDate = new Date(validBeginTime * 1000L);
            couponRecord.setValidBeginTime(validBeginTimeDate);
        } catch (Exception e) {
            log.info("时间转换异常{}", json, e);
        }

        try {
            Long validEndTime = msg.getValidEndTime();
            Date validEndTimeDate = new Date(validEndTime * 1000L);
            couponRecord.setValidEndTime(validEndTimeDate);
        } catch (Exception e) {
            log.info("时间转换异常{}", json, e);
        }

        return save(couponRecord);
    }

    @Override
    public MiniAppCouponRecord getByCouponId(String couponId) {

        return lambdaQuery().eq(MiniAppCouponRecord::getCouponId, couponId).last("limit 1").one();

    }


    @Resource
    DouYinHttpUtil douYinHttpUtil;

    @Resource
    IUserDramaCardService userDramaCardService;

    @Resource
    IDuanJuUserService duanJuUserService;

    @Resource
    IMiniAppDramaEpisodeService miniAppDramaEpisodeService;

    @Resource
    IMiniAppMiniDramaService miniAppMiniDramaService;


    @Override
    public void consumeCoupon(String couponId, Integer userId, String douYinEpisodeId) {
        MiniAppCouponRecord coupon = getByCouponId(couponId);
        DuanJuUser user = duanJuUserService.getById(userId);
        String openId = user.getOpenId();
        if (Objects.isNull(coupon)) {
            throw new BusinessException("优惠券记录不存在");
        }
        if (coupon.getCouponStatus() != 10) {
            throw new BusinessException("优惠券状态异常");
        }
        OpenApiRes openApiRes = douYinHttpUtil.consumeCoupon(coupon.getId(), openId, couponId, user.getResource());
        if (openApiRes.isSuccess()) {
            coupon.setCouponStatus(20);
            coupon.setDouYinEpisodeId(douYinEpisodeId);
            updateById(coupon);

            // 1:消费成功保存一条UserDramaCard记录 rangeType是2 (剧卡类型)
            try {
                createUserDramaCardFromCoupon(coupon, user.getId(), douYinEpisodeId);
            } catch (Exception e) {
                log.error("创建用户剧卡权益失败，优惠券ID: {}, openId: {}, douYinEpisodeId: {}",
                        couponId, openId, douYinEpisodeId, e);
            }
        } else {
            log.warn("核销异常:{}", openApiRes.getErrMsg());
            throw new BusinessException("核销异常");
        }
    }

    public void rollbackConsumeCoupon(String couponId, String openId) {
        MiniAppCouponRecord coupon = getByCouponId(couponId);
        if (Objects.isNull(coupon)) {
            throw new BusinessException("优惠券记录不存在");
        }
        if (coupon.getCouponStatus() != 20) {
            throw new BusinessException("优惠券状态异常");
        }
        String consumeOutNo = coupon.getId();
        String orderId = coupon.getOrderId();
        OpenApiRes openApiRes = douYinHttpUtil.rollbackConsumeCoupon(consumeOutNo, openId, couponId, orderId);
        if (openApiRes.isSuccess()) {
            String data = openApiRes.getData();
            JSONObject jsonObject = JSONObject.parseObject(data);
            JSONArray results = jsonObject.getJSONArray("results");
            if (!results.isEmpty()) {
                JSONObject result = results.getJSONObject(0);
                if (result.getIntValue("err_no") == 1) {
                    coupon.setCouponStatus(40);
                    updateById(coupon);
                }
            }
        } else {
            log.warn("优惠券回滚异常:{}", openApiRes.getErrMsg());
            throw new BusinessException("优惠券撤销异常");
        }
    }

    /**
     * 根据优惠券消费信息创建用户剧卡权益
     *
     * @param coupon          优惠券记录
     * @param userId          用户openId
     * @param douYinEpisodeId 抖音剧集ID
     */
    private void createUserDramaCardFromCoupon(MiniAppCouponRecord coupon, Integer userId, String douYinEpisodeId) {
        try {
            // 2. 通过douYinEpisodeId获取剧集信息
            MiniAppDramaEpisode episode = miniAppDramaEpisodeService.lambdaQuery()
                    .eq(MiniAppDramaEpisode::getDouYinEpisodeId, douYinEpisodeId)
                    .eq(MiniAppDramaEpisode::getIsDeleted, 0)
                    .one();

            if (episode == null) {
                log.error("无法找到剧集信息，douYinEpisodeId: {}", douYinEpisodeId);
                return;
            }

            // 3. 获取短剧信息
            MiniAppMiniDrama drama = miniAppMiniDramaService.getById(episode.getMiniDramaId());
            if (drama == null) {
                log.error("无法找到短剧信息，dramaId: {}", episode.getMiniDramaId());
                return;
            }

            // 4. 创建UserDramaCard记录
            UserDramaCard userDramaCard = new UserDramaCard();
            userDramaCard.setOrderId(coupon.getId()); // 使用优惠券记录ID作为订单ID
            userDramaCard.setDramaId(drama.getId());
            userDramaCard.setUserId(String.valueOf(userId));
            userDramaCard.setAlbumName(drama.getName());
            userDramaCard.setAlbumId(drama.getAlbumId());
            userDramaCard.setRangeType(2);
            userDramaCard.setDouYinEpisodeId(douYinEpisodeId);

            // 设置状态和时间
            userDramaCard.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());
            userDramaCard.setPurchaseTime(new Date());
            userDramaCard.setIsPermanent(1); // 设置为永久有效
            userDramaCard.setCreateTime(new Date());
            userDramaCard.setUpdateTime(new Date());
            userDramaCard.setIsDeleted(0);

            // 5. 保存记录
            boolean success = userDramaCardService.save(userDramaCard);
            if (success) {
                log.info("优惠券消费成功，创建用户剧卡权益成:userId:{},couponId:{},douYinEpisodeId:{}", userId, coupon.getCouponId(), douYinEpisodeId);
            } else {
                log.error("创建用户剧卡权益失败，:userId:{},couponId:{},douYinEpisodeId:{}", userId, coupon.getCouponId(), douYinEpisodeId);

            }
        } catch (Exception e) {
            log.error("创建用户剧卡权益异常，userId: {}, douYinEpisodeId: {}", userId, douYinEpisodeId, e);
            throw e;
        }
    }
}
