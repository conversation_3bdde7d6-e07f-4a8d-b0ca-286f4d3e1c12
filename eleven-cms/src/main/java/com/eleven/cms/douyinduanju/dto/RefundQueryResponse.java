package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

/**
 * 退款查询响应类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class RefundQueryResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 退款查询API响应数据
     */
    private RefundQueryApiResponse data;

    /**
     * 错误码
     */
    private String errorCode;

    public RefundQueryResponse() {
    }

    public RefundQueryResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public RefundQueryResponse(boolean success, String message, RefundQueryApiResponse data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static RefundQueryResponse success(String message) {
        return new RefundQueryResponse(true, message);
    }

    /**
     * 成功响应（带数据）
     */
    public static RefundQueryResponse success(String message, RefundQueryApiResponse data) {
        return new RefundQueryResponse(true, message, data);
    }

    /**
     * 失败响应
     */
    public static RefundQueryResponse error(String message) {
        return new RefundQueryResponse(false, message);
    }

    /**
     * 失败响应（带错误码）
     */
    public static RefundQueryResponse error(String message, String errorCode) {
        RefundQueryResponse response = new RefundQueryResponse(false, message);
        response.setErrorCode(errorCode);
        return response;
    }
}
