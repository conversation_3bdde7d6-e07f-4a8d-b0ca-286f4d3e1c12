package com.eleven.cms.douyinduanju.dto;

import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 订单响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@ApiModel(value = "订单响应", description = "订单操作响应数据")
public class OrderResponse {

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "订单信息")
    private MiniAppDuanJuOrder order;

    @ApiModelProperty(value = "支付参数（用于前端调起支付）")
    private Map<String, Object> paymentParams;

    @ApiModelProperty(value = "支付跳转URL")
    private String paymentUrl;

    @ApiModelProperty(value = "扩展数据")
    private Map<String, Object> extData;

    public static OrderResponse success(String message) {
        OrderResponse response = new OrderResponse();
        response.setSuccess(true);
        response.setMessage(message);
        return response;
    }

    public static OrderResponse success(String message, MiniAppDuanJuOrder order) {
        OrderResponse response = success(message);
        response.setOrder(order);
        return response;
    }

    public static OrderResponse error(String message) {
        OrderResponse response = new OrderResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
}
