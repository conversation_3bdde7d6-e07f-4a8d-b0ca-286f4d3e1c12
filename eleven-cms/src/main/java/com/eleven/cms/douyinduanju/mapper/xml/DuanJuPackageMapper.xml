<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.DuanJuPackageMapper">

    <!-- 根据省份获取套餐列表 -->
    <select id="listPackageByProvince" resultType="com.eleven.cms.douyinduanju.entity.DuanJuPackage">

        SELECT *
        FROM duan_ju_package
        where status = 1
        and is_deleted = 0
        and (pay_type = 2 or pay_type = 1 and country_code_flag = 1
        <if test="provinceName != null and provinceName != ''">
            OR (country_code_flag = 0 AND support_province LIKE CONCAT('%', #{provinceName}, '%'))
        </if>
        )
        <if test="model != null and model != ''">
            and model LIKE CONCAT('%', #{model}, '%')
        </if>
        <if test="typeList != null and typeList.size() > 0">
            and type IN
            <foreach item="item" collection="typeList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY order_no asc;
    </select>


    <select id="getPackageByProvince" resultType="com.eleven.cms.douyinduanju.entity.DuanJuPackage">
        SELECT *
        FROM duan_ju_package
        where status = 1
        and is_deleted = 0
        and pay_type = 1
        and ( country_code_flag = 1
        <if test="provinceName != null and provinceName != ''">
            OR (country_code_flag = 0 AND support_province LIKE CONCAT('%', #{provinceName}, '%'))
        </if>
        )
        <if test="model != null and model != ''">
            and model LIKE CONCAT('%', #{model}, '%')
        </if>
        <if test="typeList != null and typeList.size() > 0">
            and type IN
            <foreach item="item" collection="typeList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY order_no asc;
    </select>

    <!-- 更新套餐信息（包括空值字段） -->
    <update id="updateByIdWithNull" parameterType="com.eleven.cms.douyinduanju.entity.DuanJuPackage">
        UPDATE duan_ju_package
        SET name              = #{package.name},
            type              = #{package.type},
            pay_type          = #{package.payType},
            underline_flag    = #{package.underlineFlag},
            right_desc        = #{package.rightDesc},
            complimentary_num = #{package.complimentaryNum},
            bottom_heading    = #{package.bottomHeading},
            recharge_amount   = #{package.rechargeAmount},
            product_link      = #{package.productLink},
            channel_no        = #{package.channelNo},
            pj_flag           = #{package.pjFlag},
            channel_type      = #{package.channelType},
            country_code_flag = #{package.countryCodeFlag},
            support_province  = #{package.supportProvince},
            original_price    = #{package.originalPrice},
            actual_price      = #{package.actualPrice},
            kb_ratio          = #{package.kbRatio},
            order_no          = #{package.orderNo},
            status            = #{package.status},
            description       = #{package.description},
            label_content     = #{package.labelContent},
            icon              = #{package.icon},
            update_by         = #{package.updateBy},
            update_time       = #{package.updateTime},
            order_pay_link    = #{package.orderPayLink},
            duration_days   = #{package.durationDays},
            show_price_flag               = #{package.showPriceFlag},
            page_type                     = #{package.pageType},
            app_mobile_pay_link_config_id = #{package.appMobilePayLinkConfigId},
            pay_link_config_order_no = #{package.payLinkConfigOrderNo},
            guide_to_recharge_flag        = #{package.guideToRechargeFlag},
            model                         = #{package.model}
        WHERE id = #{package.id}
    </update>

</mapper>
