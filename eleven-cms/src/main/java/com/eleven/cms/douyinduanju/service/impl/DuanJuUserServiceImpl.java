package com.eleven.cms.douyinduanju.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.constant.DuanjuConstant;
import com.eleven.cms.douyinduanju.dto.UserRightVO;
import com.eleven.cms.douyinduanju.entity.DuanJuUser;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.entity.UserDramaCard;
import com.eleven.cms.douyinduanju.entity.UserMembership;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.mapper.DuanJuUserMapper;
import com.eleven.cms.douyinduanju.service.*;
import com.eleven.cms.douyinduanju.util.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: duan_ju_user
 * @Author: jeecg-boot
 * @Date: 2025-06-05
 * @Version: V1.0
 */
@Service
@Slf4j
public class DuanJuUserServiceImpl extends ServiceImpl<DuanJuUserMapper, DuanJuUser> implements IDuanJuUserService {


    @Resource
    private IUserMembershipService userMembershipService;

    @Resource
    private IUserDramaCardService userDramaCardService;

    @Resource
    PayChannelService payChannelService;

    @Resource
    IMiniAppDuanJuOrderService miniAppDuanJuOrderService;

    @Override
    public DuanJuUser loginOrRegister(String openId, String nickName, String mobile, String source, String subChannel, String resource) {
        if (StringUtils.isBlank(openId)) {
            throw new RuntimeException("openId不能为空");
        }

        // 根据openId查询用户是否存在
        QueryWrapper<DuanJuUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("open_id", openId);
        DuanJuUser existingUser = this.getOne(queryWrapper);

        if (existingUser != null) {
            // 用户已存在，更新最近启动时间
            existingUser.setLastOperateTime(new Date());
            this.updateById(existingUser);

            //校验用户订阅状态
            validateChannelSub(existingUser.getId());

            log.info("用户登录成功，用户ID: {}, openId: {}", existingUser.getId(), openId);
            return existingUser;
        } else {
            // 用户不存在，创建新用户
            DuanJuUser newUser = new DuanJuUser();
            newUser.setOpenId(openId);
            newUser.setNickName(StringUtils.isNotBlank(nickName) ? nickName : "用户" + System.currentTimeMillis());
            newUser.setMobile(mobile);
            newUser.setSource(StringUtils.isNotBlank(source) ? source : "mhjc");

            Date now = new Date();
            newUser.setRegisterTime(now);
            newUser.setCreateTime(now);
            newUser.setUpdateTime(now);
            newUser.setLastOperateTime(now);
            newUser.setResource(resource);
            newUser.setSubChannel(subChannel);
            // 设置默认值
            newUser.setMemberStatus(1); // 1:非会员
            newUser.setPayFlat(0); // 0:未付费
            this.save(newUser);
            log.info("新用户注册成功，用户ID: {}, openId: {}", newUser.getId(), openId);
            return newUser;
        }
    }

    @Resource
    RedisUtil redisUtil;

    public void validateChannelSub(Integer userId) {

        try {
            DuanJuUser duanJuUser = getById(userId);
            if (Objects.isNull(duanJuUser)) {
                return;
            }
            Object result = redisUtil.get(DuanjuConstant.SUBSCRIBE_MEMBERSHIP_STATUS_PREFIX + duanJuUser.getOpenId());
            if (Objects.nonNull(result)) {
                return;
            }

            //用户的会员类型是话费会员
            if (Objects.equals(OrderEnums.MembershipStatus.SUB.getCode(), duanJuUser.getMemberStatus()) && Objects.equals(OrderEnums.MembershipType.SUB.getCode(), duanJuUser.getMemberType())) {
                UserMembership userMembership = userMembershipService.getValidMembershipByUserId(String.valueOf(userId));
                String orderId = userMembership.getOrderId();
                MiniAppDuanJuOrder byOrderNo = miniAppDuanJuOrderService.getByOrderNo(orderId);
                String channel = byOrderNo.getChannel();
                List<String> channelList = new ArrayList<>();
                if (StringUtils.isNotBlank(channel)) {
                    channelList.add(channel);
                }
                List<PayChannelService.ChannelSubResult> channelSubResults = payChannelService.queryAllServiceByMobile(duanJuUser.getMobile(), channelList);
                String orderStatus = channelSubResults.get(0).getOrderStatus();
                if (!Objects.equals("1", orderStatus)) {
                    //取消会员
                    userMembership.setStatus(3);
                    userMembershipService.updateById(userMembership);

                    //生效下一个会员
                    userMembershipService.activateNextInactiveMembership(String.valueOf(userId));

                    //更新会员信息
                    userMembershipService.updateUserMemberInfo(String.valueOf(userId));
                }
            }
        } catch (Exception e) {
            log.error("话费订阅会员校验异常", e);
        }
    }


    /**
     * 获取用户权益
     */
    @Override
    public UserRightVO getUserRight(Integer userId, String albumId) {

        //获取用户之前,校验用户的订阅状态
        validateChannelSub(userId);


        UserRightVO userRightVO = new UserRightVO();
        userRightVO.setMembershipStatus(0);
        //判断用户是不是会员
        Integer currentUserId = TokenUtils.getCurrentUserId();
        if (Objects.isNull(currentUserId)) {
            return userRightVO;
        }
        DuanJuUser currentUser = this.getById(currentUserId);
        if (Objects.equals(currentUser.getMemberStatus(), 2) || Objects.equals(currentUser.getMemberStatus(), 3)) {
            //获取会员权益
            userRightVO.setMembershipStatus(1);
        } else {
            userRightVO.setMembershipStatus(0);
        }
        List<String> albumIdList = new ArrayList<>();
        List<String> episodeIdList = new ArrayList<>();
        List<UserDramaCard> dramaCards = userDramaCardService.getValidDramaCardsByUserId(String.valueOf(currentUserId));
        if (CollectionUtil.isNotEmpty(dramaCards)) {
            for (UserDramaCard dramaCard : dramaCards) {
                if (Objects.equals(1, dramaCard.getRangeType())) {
                    albumIdList.add(dramaCard.getAlbumId());
                }
                if (Objects.equals(2, dramaCard.getRangeType())) {
                    episodeIdList.add(dramaCard.getDouYinEpisodeId());
                }
            }
        }
        userRightVO.setAlbumIdList(albumIdList);
        userRightVO.setEpisodeIdList(episodeIdList);
        return userRightVO;
    }

}
