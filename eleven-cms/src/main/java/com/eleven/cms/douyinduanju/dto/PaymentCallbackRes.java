package com.eleven.cms.douyinduanju.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 抖音支付回调响应DTO
 */
@Data
@ApiModel(value = "抖音支付回调响应", description = "支付回调接口的响应格式")
public class PaymentCallbackRes {

    /**
     * 错误码，0表示成功
     */
    @JsonProperty("err_no")
    @ApiModelProperty(value = "错误码，0表示成功")
    private Integer errNo;

    /**
     * 错误提示，成功时为success
     */
    @JsonProperty("err_tips")
    @ApiModelProperty(value = "错误提示，成功时为success")
    private String errTips;

    /**
     * 创建成功响应
     */
    public static PaymentCallbackRes success() {
        PaymentCallbackRes response = new PaymentCallbackRes();
        response.setErrNo(0);
        response.setErrTips("success");
        return response;
    }

    /**
     * 创建失败响应
     */
    public static PaymentCallbackRes error(String message) {
        PaymentCallbackRes response = new PaymentCallbackRes();
        response.setErrNo(1);
        response.setErrTips(message);
        return response;
    }
}
