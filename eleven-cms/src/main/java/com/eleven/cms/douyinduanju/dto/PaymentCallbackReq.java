package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 抖音支付回调请求DTO
 */
@Data
@ApiModel(value = "抖音支付回调请求", description = "抖音支付结果回调请求参数")
public class PaymentCallbackReq {

    /**
     * 订单相关信息的JSON字符串
     */
    @ApiModelProperty(value = "订单相关信息的JSON字符串", required = true)
    private String msg;

    /**
     * 回调类型
     */
    @ApiModelProperty(value = "回调类型，支付结果回调为payment", required = true)
    private String type;

    /**
     * 回调版本
     */
    @ApiModelProperty(value = "回调版本，固定值3.0", required = true)
    private String version;
}
