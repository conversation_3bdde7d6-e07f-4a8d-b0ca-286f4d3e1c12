package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * MiniAppMiniDrama收藏记录实体类
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@TableName("mini_app_mini_drama_favorite")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MiniAppMiniDrama收藏记录", description = "MiniAppMiniDrama收藏记录表")
public class MiniAppMiniDramaFavorite implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    /**
     * 短剧ID
     */
    @Excel(name = "短剧ID", width = 15)
    @ApiModelProperty(value = "短剧ID")
    private String albumId;
    private String douYinEpisodeId;

    /**
     * 短剧名称
     */
    @Excel(name = "短剧名称", width = 20)
    @ApiModelProperty(value = "短剧名称")
    private String albumName;


    /**
     * 短剧封面图
     */
    @ApiModelProperty(value = "短剧封面图")
    private String coverUrl;

    /**
     * 收藏时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收藏时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "收藏时间")
    private Date favoriteTime;

    /**
     * 收藏状态：0-已取消收藏 1-已收藏
     */
    @Excel(name = "收藏状态", width = 15)
    @ApiModelProperty(value = "收藏状态：0-已取消收藏 1-已收藏")
    private Integer status;

    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(exist = false)
    private Long viewCount;

    @TableField(exist = false)
    private String firstDouYinEpisodeId;

    private Integer seqCount;
    private Integer viewMaxSeq;
}
