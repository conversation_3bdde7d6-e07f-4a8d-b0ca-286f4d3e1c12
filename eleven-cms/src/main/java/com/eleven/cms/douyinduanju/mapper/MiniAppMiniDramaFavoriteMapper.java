package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDramaFavorite;
import org.apache.ibatis.annotations.Param;

/**
 * MiniAppMiniDrama收藏记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface MiniAppMiniDramaFavoriteMapper extends BaseMapper<MiniAppMiniDramaFavorite> {

    /**
     * 分页查询收藏记录
     *
     * @param page   分页参数
     * @param userId 用户ID
     * @param status 收藏状态
     * @return 收藏记录分页列表
     */
    IPage<MiniAppMiniDramaFavorite> selectFavoritePage(IPage<MiniAppMiniDramaFavorite> page,
                                                       @Param("userId") Integer userId,
                                                       @Param("status") Integer status);

}
