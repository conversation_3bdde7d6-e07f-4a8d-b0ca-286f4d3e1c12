package com.eleven.cms.douyinduanju.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdReportResultCallbackDTO {
    /**
     * 上报结果
     * SUCCESS(1, "上报成功"),
     * FAILURE(-1, "上报失败");
     */
    private Integer reportStatus;
    /**
     * 原因描述
     */
    private String content;
    /**
     * 广告事件类型
     */
    private String eventType;

    private String callback;
    /**
     * 其他附加参数
     */
    private Map<String, String> adExtraMap;
}
