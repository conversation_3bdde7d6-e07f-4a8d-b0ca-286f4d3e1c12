package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支付签名生成响应DTO
 */
@Data
@ApiModel(value = "支付签名生成响应", description = "抖音支付签名生成成功后返回的参数")
public class PaymentSignatureRes {

    /**
     * 支付数据JSON字符串
     */
    @ApiModelProperty(value = "支付数据JSON字符串，用于调用抖音支付接口")
    private String data;

    /**
     * 签名授权字符串
     */
    @ApiModelProperty(value = "签名授权字符串，用于请求头Authorization")
    private String byteAuthorization;

    private String orderNo;
}
