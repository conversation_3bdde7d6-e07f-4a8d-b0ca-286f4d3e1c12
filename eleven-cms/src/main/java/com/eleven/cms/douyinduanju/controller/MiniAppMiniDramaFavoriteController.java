package com.eleven.cms.douyinduanju.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.annotation.TokenRequired;
import com.eleven.cms.douyinduanju.dto.LookHistoryVO;
import com.eleven.cms.douyinduanju.dto.MiniAppMiniDramaFavoriteRequest;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaEpisode;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDramaFavorite;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaEpisodeService;
import com.eleven.cms.douyinduanju.service.IMiniAppDramaViewingHistoryService;
import com.eleven.cms.douyinduanju.service.IMiniAppMiniDramaFavoriteService;
import com.eleven.cms.douyinduanju.service.IOpenApiAlbumInfoService;
import com.eleven.cms.douyinduanju.util.TokenUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * MiniAppMiniDrama收藏记录控制器
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Api(tags = "MiniAppMiniDrama收藏记录管理")
@RestController
@RequestMapping("/miniApi/duanju/api/favorite")
@Slf4j
@TokenRequired(required = true, message = "请先登录")
public class MiniAppMiniDramaFavoriteController {

    @Resource
    private IMiniAppMiniDramaFavoriteService miniAppMiniDramaFavoriteService;

    @Resource
    private IOpenApiAlbumInfoService openApiAlbumInfoService;

    @Resource
    private IMiniAppDramaViewingHistoryService miniAppDramaViewingHistoryService;
    /**
     * 添加收藏
     */
    @ApiOperation(value = "添加收藏", notes = "添加MiniAppMiniDrama收藏记录")
    @PostMapping("/addOrUpdate")
    @TokenRequired
    public Result<?> addFavorite(@Valid @RequestBody MiniAppMiniDramaFavoriteRequest request) {
        try {
            Integer currentUserId = TokenUtils.getCurrentUserId();
            boolean result = miniAppMiniDramaFavoriteService.addFavorite(
                    currentUserId,
                    request.getAlbumId()
            );

            if (result) {
                return Result.ok("添加收藏成功");
            } else {
                return Result.error("添加收藏失败");
            }

        } catch (Exception e) {
            log.error("添加收藏异常", e);
            return Result.error("添加收藏失败: " + e.getMessage());
        }
    }

    /**
     * 取消收藏
     */
    @ApiOperation(value = "取消收藏", notes = "取消MiniAppMiniDrama收藏")
    @GetMapping("/delete")
    @TokenRequired
    public Result<?> removeFavorite(String albumId) {
        try {
            Integer userId = TokenUtils.getCurrentUserId();
            boolean result = miniAppMiniDramaFavoriteService.removeFavorite(userId, albumId);
            if (result) {
                return Result.ok("取消收藏成功");
            } else {
                return Result.error("取消收藏失败");
            }
        } catch (Exception e) {
            log.error("取消收藏异常", e);
            return Result.error("取消收藏失败");
        }
    }


    @Resource
    private IMiniAppDramaEpisodeService miniAppDramaEpisodeService;
    /**
     * 分页查询收藏记录
     */
    @ApiOperation(value = "分页查询收藏记录", notes = "分页查询MiniAppMiniDrama收藏记录")
    @GetMapping("/list")
    public Result<?> queryPageList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页大小") @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            IPage<MiniAppMiniDramaFavorite> page = new Page<>(pageNo, pageSize);

            // 修复：确保 userId 类型正确
            Integer currentUserId = TokenUtils.getCurrentUserId();
            IPage<MiniAppMiniDramaFavorite> favoriteList = miniAppMiniDramaFavoriteService
                    .getFavoritePage(page, currentUserId, 1);

            // 获取用户观看历史
            List<LookHistoryVO> maxSeqHistory = miniAppDramaViewingHistoryService.getMaxSeqHistory(currentUserId);
            Map<String, LookHistoryVO> maxSeqMap = maxSeqHistory.stream()
                    .filter(history -> history != null && StringUtils.isNotEmpty(history.getAlbumId()))
                    .collect(Collectors.toMap(
                            LookHistoryVO::getAlbumId,
                            history -> history,
                            (existing, replacement) -> existing // 如果有重复的albumId，保留第一个
                    ));

            List<MiniAppMiniDramaFavorite> records = favoriteList.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                // 过滤掉 albumId 为空的记录
                List<String> albumIds = records.stream()
                        .map(MiniAppMiniDramaFavorite::getAlbumId)
                        .filter(StringUtils::isNotEmpty)
                        .distinct()
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(albumIds)) {
                    List<OpenApiAlbumInfo> albumInfoList = openApiAlbumInfoService.lambdaQuery()
                            .in(OpenApiAlbumInfo::getAlbumId, albumIds)
                            .list();

                    Map<String, OpenApiAlbumInfo> albumInfoMap = albumInfoList.stream()
                            .filter(info -> info != null && StringUtils.isNotEmpty(info.getAlbumId()))
                            .collect(Collectors.toMap(
                                    OpenApiAlbumInfo::getAlbumId,
                                    info -> info,
                                    (existing, replacement) -> existing // 如果有重复的albumId，保留第一个
                            ));

                    List<MiniAppDramaEpisode> miniAppDramaEpisodes = miniAppDramaEpisodeService.lambdaQuery().in(MiniAppDramaEpisode::getAlbumId, albumIds).eq(MiniAppDramaEpisode::getIsDeleted, 0).eq(MiniAppDramaEpisode::getEpisodeSeq, 1).list();
                    Map<String, MiniAppDramaEpisode> miniAppDramaEpisodeMap = miniAppDramaEpisodes.stream().collect(Collectors.toMap(MiniAppDramaEpisode::getAlbumId, episode -> episode, (existing, replacement) -> existing));

                    // 填充收藏记录的详细信息
                    for (MiniAppMiniDramaFavorite favorite : records) {
                        if (favorite != null && StringUtils.isNotEmpty(favorite.getAlbumId())) {
                            // 设置剧集总数
                            OpenApiAlbumInfo albumInfo = albumInfoMap.get(favorite.getAlbumId());
                            if (albumInfo != null && albumInfo.getSeqCount() != null) {
                                favorite.setSeqCount(albumInfo.getSeqCount());
                            } else {
                                favorite.setSeqCount(0); // 设置默认值
                            }

                            // 设置观看进度
                            LookHistoryVO lookHistory = maxSeqMap.get(favorite.getAlbumId());
                            if (lookHistory != null && lookHistory.getEpisodeNum() != null) {
                                favorite.setViewMaxSeq(lookHistory.getEpisodeNum());
                            } else {
                                favorite.setViewMaxSeq(0); // 设置默认值
                            }

                            MiniAppDramaEpisode episode = miniAppDramaEpisodeMap.get(favorite.getAlbumId());
                            if (Objects.nonNull(episode)) {
                                favorite.setFirstDouYinEpisodeId(episode.getDouYinEpisodeId());
                            }
                        }
                    }
                }
            }
            return Result.ok(favoriteList);

        } catch (Exception e) {
            log.error("分页查询收藏记录异常", e);
            return Result.error("分页查询收藏记录失败: " + e.getMessage());
        }
    }


    /**
     * 删除收藏记录（物理删除）
     */
    @ApiOperation(value = "删除收藏记录", notes = "删除MiniAppMiniDrama收藏记录（物理删除）")
    @DeleteMapping("/delete")
    public Result<?> deleteFavorite(
            @ApiParam(value = "收藏记录ID", required = true) @RequestParam String id) {
        try {
            boolean result = miniAppMiniDramaFavoriteService.deleteFavorite(id);

            if (result) {
                return Result.ok("删除收藏记录成功");
            } else {
                return Result.error("删除收藏记录失败");
            }

        } catch (Exception e) {
            log.error("删除收藏记录异常", e);
            return Result.error("删除收藏记录失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除收藏记录（逻辑删除）
     */
    @ApiOperation(value = "批量删除收藏记录", notes = "批量删除MiniAppMiniDrama收藏记录（逻辑删除）")
    @DeleteMapping("/batchDelete")
    public Result<?> batchDeleteFavorites(
            @ApiParam(value = "收藏记录ID列表", required = true) @RequestParam List<String> ids) {
        try {
            boolean result = miniAppMiniDramaFavoriteService.batchDeleteFavorites(ids);

            if (result) {
                return Result.ok("批量删除收藏记录成功");
            } else {
                return Result.error("批量删除收藏记录失败");
            }

        } catch (Exception e) {
            log.error("批量删除收藏记录异常", e);
            return Result.error("批量删除收藏记录失败: " + e.getMessage());
        }
    }
}
