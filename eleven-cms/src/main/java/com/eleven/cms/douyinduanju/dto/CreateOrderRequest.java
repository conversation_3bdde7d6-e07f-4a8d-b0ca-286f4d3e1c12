package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 创建订单请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@ApiModel(value = "创建订单请求", description = "创建订单请求参数")
public class CreateOrderRequest {

    @ApiModelProperty(value = "用户ID", required = true)
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @ApiModelProperty(value = "套餐ID", required = true)
    @NotBlank(message = "套餐ID不能为空")
    private String packageId;

    @ApiModelProperty(value = "套餐类型: 1会员 2剧卡 3K币", required = true)
    @NotNull(message = "套餐类型不能为空")
    private Integer packageType;

    @ApiModelProperty(value = "支付方式: 1抖音支付 2话费支付", required = true)
    @NotNull(message = "支付方式不能为空")
    private Integer payType;

    @ApiModelProperty(value = "短剧ID（购买剧卡时必填）")
    private String dramaId;

    @ApiModelProperty(value = "短剧名称（购买剧卡时必填）")
    private String dramaName;

    @ApiModelProperty(value = "抖音短剧albumId（购买剧卡时必填）")
    private String albumId;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "用户手机号")
    private String mobile;

    @ApiModelProperty(value = "业务类型标识")
    private String businessType;

    @ApiModelProperty(value = "扩展参数（JSON格式）")
    private String extParams;
}
