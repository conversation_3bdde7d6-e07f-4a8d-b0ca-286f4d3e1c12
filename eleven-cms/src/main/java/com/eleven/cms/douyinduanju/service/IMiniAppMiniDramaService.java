package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.dto.AlbumInfVO;
import com.eleven.cms.douyinduanju.dto.DramaReq;
import com.eleven.cms.douyinduanju.dto.MiniAppMiniDramaReq;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;

import java.util.List;


public interface IMiniAppMiniDramaService extends IService<MiniAppMiniDrama> {

    //    IPage<MiniAppMiniDramaVO> pageDramaByColumnId(String columnId, IPage<MiniAppMiniDramaVO> iPage);
    IPage<AlbumInfVO> pageAlbumInfo(DramaReq req, IPage<AlbumInfVO> iPage);

    MiniAppMiniDrama getByAlbumId(String albumId);

    MiniAppMiniDrama getByAlbumInfoId(String albumInfoId);

    List<MiniAppMiniDrama> listDrama(String columnId, List<String> ids);

    IPage<MiniAppMiniDrama> listHostDrama(String columnId, IPage<MiniAppMiniDrama> iPage, List<String> ids);

    MiniAppMiniDrama addDramaAndAlbumInfo(MiniAppMiniDramaReq req);

    MiniAppMiniDrama editDramaAndAlbumInfo(MiniAppMiniDramaReq req);

    /**
     * 获取所有短剧信息用于导出
     *
     * @param req 查询条件
     * @return 短剧信息列表
     */
    List<AlbumInfVO> getAllAlbumInfoForExport(DramaReq req);

    /**
     * 更新短剧信息（包括空值字段）
     *
     * @param miniAppMiniDrama 短剧对象
     * @return 更新行数
     */
    int updateByIdWithNull(MiniAppMiniDrama miniAppMiniDrama);

    List<MiniAppMiniDrama> getRecommendAlbum(String albumId, Integer currentUserId, String catLogId);
}
