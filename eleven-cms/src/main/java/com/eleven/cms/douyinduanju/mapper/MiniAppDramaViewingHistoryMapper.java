package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.douyinduanju.dto.LookHistoryVO;
import com.eleven.cms.douyinduanju.entity.MiniAppDramaViewingHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: mini_app_drama_viewing_history
 * @Author: jeecg-boot
 * @Date: 2025-06-03
 * @Version: V1.0
 */
public interface MiniAppDramaViewingHistoryMapper extends BaseMapper<MiniAppDramaViewingHistory> {


    List<String> getLastHistory(@Param("userId") Integer userId);

    List<LookHistoryVO> getHistoryByAlbumId(@Param("albumIdList") List<String> albumIdList);

    List<LookHistoryVO> getMaxSeqHistory(@Param("userId") Integer userId);

    /**
     * 获取用户每个剧目的最新观看历史记录
     *
     * @param userId 用户ID
     * @return 每个剧目的最新观看历史记录列表
     */
    List<LookHistoryVO> getLatestHistoryForEachDrama(@Param("userId") Integer userId);
}
