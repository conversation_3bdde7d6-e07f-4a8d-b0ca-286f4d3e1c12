package com.eleven.cms.douyinduanju.controller.template;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.douyinduanju.controller.DuanJuOrderController;
import com.eleven.cms.douyinduanju.dto.MiniAppDuanJuOrderExcelVO;
import com.eleven.cms.douyinduanju.dto.RefundResponse;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.service.IMiniAppDuanJuOrderService;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.service.IDouyinAppConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: miniApp_duan_ju_order
 * @Author: jeecg-boot
 * @Date: 2025-06-06
 * @Version: V1.0
 */
@Api(tags = "miniApp_duan_ju_order")
@RestController
@RequestMapping("/douyinduanju/miniAppDuanJuOrder")
@Slf4j
public class MiniappDuanJuOrderController extends JeecgController<MiniAppDuanJuOrder, IMiniAppDuanJuOrderService> {
    @Autowired
    private IMiniAppDuanJuOrderService miniappDuanJuOrderService;

    /**
     * 分页列表查询
     *
     * @param miniappDuanJuOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "miniApp_duan_ju_order-分页列表查询", notes = "miniApp_duan_ju_order-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppDuanJuOrder miniappDuanJuOrder,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<MiniAppDuanJuOrder> queryWrapper = QueryGenerator.initQueryWrapper(miniappDuanJuOrder, req.getParameterMap());
        Page<MiniAppDuanJuOrder> page = new Page<MiniAppDuanJuOrder>(pageNo, pageSize);
        // 获取时间参数
        String orderTimeBegin = req.getParameter("orderTimeBegin");
        String orderTimeEnd = req.getParameter("orderTimeEnd");
        String payTimeBegin = req.getParameter("payTimeBegin"); // 修复：原来错误地获取了orderTimeEnd
        String payTimeEnd = req.getParameter("payTimeEnd");

        // 格式化时间参数
        Date orderTimeBeginDate = parseDateTime(orderTimeBegin, "订单开始时间");
        Date orderTimeEndDate = parseDateTime(orderTimeEnd, "订单结束时间");
        Date payTimeBeginDate = parseDateTime(payTimeBegin, "支付开始时间");
        Date payTimeEndDate = parseDateTime(payTimeEnd, "支付结束时间");


        queryWrapper.le(Objects.nonNull(orderTimeBeginDate), "order_time", orderTimeEnd);
        queryWrapper.ge(Objects.nonNull(orderTimeEndDate), "order_time", orderTimeBegin);
        queryWrapper.le(Objects.nonNull(payTimeBeginDate), "pay_time", payTimeEnd);
        queryWrapper.ge(Objects.nonNull(payTimeEndDate), "pay_time", payTimeBegin);

        IPage<MiniAppDuanJuOrder> pageList = miniappDuanJuOrderService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param miniappDuanJuOrder
     * @return
     */
    @ApiOperation(value = "miniApp_duan_ju_order-添加", notes = "miniApp_duan_ju_order-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniAppDuanJuOrder miniappDuanJuOrder) {
        miniappDuanJuOrderService.save(miniappDuanJuOrder);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miniappDuanJuOrder
     * @return
     */
    @ApiOperation(value = "miniApp_duan_ju_order-编辑", notes = "miniApp_duan_ju_order-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniAppDuanJuOrder miniappDuanJuOrder) {
        miniappDuanJuOrderService.updateById(miniappDuanJuOrder);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "miniApp_duan_ju_order-通过id删除", notes = "miniApp_duan_ju_order-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniappDuanJuOrderService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "miniApp_duan_ju_order-批量删除")
    @ApiOperation(value = "miniApp_duan_ju_order-批量删除", notes = "miniApp_duan_ju_order-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.miniappDuanJuOrderService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "miniApp_duan_ju_order-通过id查询", notes = "miniApp_duan_ju_order-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppDuanJuOrder miniappDuanJuOrder = miniappDuanJuOrderService.getById(id);
        if (miniappDuanJuOrder == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniappDuanJuOrder);
    }

    @Resource
    private IDouyinAppConfigService douYinAppConfigService;
    /**
     * 导出excel
     *
     * @param request
     * @param miniappDuanJuOrder
     */
    @RequestMapping(value = "/exportXls")
    public Result<?> exportXls(HttpServletRequest request, MiniAppDuanJuOrder miniappDuanJuOrder) {
//        byte[] bytes = super.exportXlsRemote(request, miniappDuanJuOrder, MiniAppDuanJuOrder.class, "miniApp_duan_ju_order");

        QueryWrapper<MiniAppDuanJuOrder> queryWrapper = QueryGenerator.initQueryWrapper(miniappDuanJuOrder, request.getParameterMap());
        LoginUser sysUser = HttpUtil.getCurrUser();

        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            //获取带TableId注解的字段
            String idName = Arrays.stream(MiniAppDuanJuOrder.class.getDeclaredFields()).filter(field -> field.getAnnotation(TableId.class) != null).findFirst().get().getName();
            queryWrapper.in(idName, selectionList);
        }

        List<DouyinAppConfig> list = douYinAppConfigService.lambdaQuery().eq(DouyinAppConfig::getIsDeleted, 0).list();
        Map<String, String> appMap = list.stream().collect(Collectors.toMap(DouyinAppConfig::getBusinessType, DouyinAppConfig::getAppName, (v1, v2) -> v1));
        //System.out.println("开始执行查询");
        // Step.2 获取导出数据
        List<MiniAppDuanJuOrder> pageList = miniappDuanJuOrderService.list(queryWrapper);

        List<MiniAppDuanJuOrderExcelVO> duanJuOrderExcelVOList = new ArrayList<>();
        for (MiniAppDuanJuOrder miniAppDuanJuOrder : pageList) {
            MiniAppDuanJuOrderExcelVO vo = new MiniAppDuanJuOrderExcelVO();
            BeanUtils.copyProperties(miniAppDuanJuOrder, vo);
            String appName = appMap.get(miniAppDuanJuOrder.getBusinessType());
            if (StringUtils.isNotEmpty(appName)) {
                vo.setMiniAppName(appName);
            }
            OrderEnums.PayStatus payStatueEnum = OrderEnums.PayStatus.getByCode(miniAppDuanJuOrder.getPayStatus());
            if (Objects.nonNull(payStatueEnum)) {
                vo.setPayStatusStr(payStatueEnum.getDesc());
            }

            Integer payType = miniAppDuanJuOrder.getPayType();
            OrderEnums.PayChnnelEnum payTypeEnum = OrderEnums.PayChnnelEnum.getByCode(payType);
            if (Objects.nonNull(payTypeEnum)) {
                vo.setPayTypeStr(payTypeEnum.getDesc());
            }

            Integer orderType = miniAppDuanJuOrder.getOrderType();
            if (Objects.equals(orderType, 1)) {
                vo.setOrderTypeStr("会员");
            }
            if (Objects.equals(orderType, 2)) {
                vo.setOrderTypeStr("剧卡");
            }


            duanJuOrderExcelVOList.add(vo);
        }

        //System.out.println("已完成报表设置");
        ExportParams exportParams = new ExportParams();
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, MiniAppDuanJuOrderExcelVO.class, duanJuOrderExcelVOList);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return Result.ok(bos.toByteArray());
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppDuanJuOrder.class);
    }

    @Resource
    IMiniAppDuanJuOrderService orderService;

    @PostMapping(value = "/refundOrder")
    public Result<?> refundOrder(@RequestBody DuanJuOrderController.RefundRequest request) {

        try {
            log.info("收到退款请求: {}", request);
            // 参数验证
            if (request == null) {
                return Result.error("请求参数不能为空");
            }
            if (request.getOrderNo() == null || request.getOrderNo().trim().isEmpty()) {
                return Result.error("订单号不能为空");
            }
            if (request.getRefundAmount() == null || BigDecimal.valueOf(0).compareTo(request.getRefundAmount()) > 0) {
                return Result.error("退款金额必须大于0");
            }
            Integer refundAmount = request.getRefundAmount().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP).intValue();

            // 调用退款服务
            RefundResponse refundResponse = orderService.createRefund(
                    request.getOrderNo(),
                    refundAmount,
                    request.getRefundReason(),
                    request.getBusinessType()
            );

            if (refundResponse.isSuccess()) {
                return Result.ok(refundResponse);
            } else {
                return Result.error(refundResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("创建退款异常", e);
            return Result.error("创建退款失败: " + e.getMessage());
        }
    }

    /**
     * 解析时间字符串为Date对象
     *
     * @param timeStr   时间字符串
     * @param fieldName 字段名称（用于日志）
     * @return Date对象，解析失败返回null
     */
    private Date parseDateTime(String timeStr, String fieldName) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            log.debug("{}为空，跳过解析", fieldName);
            return null;
        }

        try {
            // 尝试多种时间格式
            String[] patterns = {
                    "yyyy-MM-dd HH:mm:ss",    // 标准格式
                    "yyyy-MM-dd",             // 日期格式
                    "yyyy/MM/dd HH:mm:ss",    // 斜杠格式
                    "yyyy/MM/dd",             // 斜杠日期格式
                    "yyyy-MM-dd'T'HH:mm:ss",  // ISO格式（不带时区）
                    "yyyy-MM-dd'T'HH:mm:ss.SSS", // ISO格式（带毫秒）
                    "yyyy-MM-dd'T'HH:mm:ss'Z'",   // ISO格式（带Z时区）
                    "yyyy-MM-dd HH:mm",       // 不带秒
                    "MM/dd/yyyy HH:mm:ss",    // 美式格式
                    "dd/MM/yyyy HH:mm:ss"     // 欧式格式
            };

            for (String pattern : patterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    sdf.setLenient(false); // 严格解析
                    Date parsedDate = sdf.parse(timeStr.trim());

                    log.debug("{}解析成功: {} -> {}", fieldName, timeStr,
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(parsedDate));
                    return parsedDate;

                } catch (ParseException ignored) {
                    // 继续尝试下一个格式
                }
            }

            // 所有格式都失败，记录警告
            log.warn("{}解析失败，不支持的时间格式: {}", fieldName, timeStr);
            return null;

        } catch (Exception e) {
            log.error("{}解析异常: {}", fieldName, timeStr, e);
            return null;
        }
    }

    /**
     * 格式化Date对象为标准时间字符串
     *
     * @param date Date对象
     * @return 格式化后的时间字符串，null返回空字符串
     */
    private String formatDateTime(Date date) {
        if (date == null) {
            return "";
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (Exception e) {
            log.error("时间格式化异常", e);
            return "";
        }
    }

    /**
     * 验证时间范围的合理性
     *
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @param fieldPrefix 字段前缀（用于日志）
     * @return 验证结果消息，null表示验证通过
     */
    private String validateTimeRange(Date beginTime, Date endTime, String fieldPrefix) {
        if (beginTime == null && endTime == null) {
            return null; // 都为空，跳过验证
        }

        if (beginTime != null && endTime != null) {
            if (beginTime.after(endTime)) {
                return fieldPrefix + "开始时间不能晚于结束时间";
            }

            // 检查时间范围是否过大（比如超过1年）
            long diffDays = (endTime.getTime() - beginTime.getTime()) / (24 * 60 * 60 * 1000);
            if (diffDays > 365) {
                return fieldPrefix + "时间范围不能超过365天";
            }
        }

        // 检查时间是否在合理范围内（比如不能是未来时间）
        Date now = new Date();
        if (beginTime != null && beginTime.after(now)) {
            return fieldPrefix + "开始时间不能是未来时间";
        }
        if (endTime != null && endTime.after(now)) {
            return fieldPrefix + "结束时间不能是未来时间";
        }

        return null; // 验证通过
    }

}
