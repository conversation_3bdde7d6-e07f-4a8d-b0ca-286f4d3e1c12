package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.douyinduanju.entity.DuanJuPackage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: duan_ju_package
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
public interface DuanJuPackageMapper extends BaseMapper<DuanJuPackage> {

    /**
     * 根据省份获取套餐列表
     *
     * @param provinceName 省份名称
     * @return 套餐列表
     */
    List<DuanJuPackage> listPackageByProvince(@Param("provinceName") String provinceName, @Param("model") String model, @Param("typeList") List<Integer> typeList);

    List<DuanJuPackage> getPackageByProvince(@Param("provinceName") String provinceName, @Param("model") String model, @Param("typeList") List<Integer> typeList);

    /**
     * 更新套餐信息（包括空值字段）
     *
     * @param duanJuPackage 套餐对象
     * @return 更新行数
     */
    int updateByIdWithNull(@Param("package") DuanJuPackage duanJuPackage);

}
