package com.eleven.cms.douyinduanju.controller;

import com.eleven.cms.douyinduanju.util.NasFileReader;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * NAS文件管理控制器
 * 用于读取和管理NAS服务器上的文件
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Api(tags = "NAS文件管理")
@RestController
@RequestMapping("/api/nas")
@Slf4j
public class NasFileController {

    @Autowired
    private NasFileReader nasFileReader;

    /**
     * 获取NAS根目录下的所有文件夹
     */
    @ApiOperation(value = "获取NAS根目录", notes = "获取NAS服务器根目录下的所有文件夹")
    @GetMapping(value = "/directories")
    public Result<?> getRootDirectories() {
        try {
            log.info("获取NAS根目录列表");

            List<NasFileReader.NasDirectory> directories = nasFileReader.getRootDirectories();

            log.info("获取NAS根目录成功，共 {} 个目录", directories.size());
            return Result.ok(directories);

        } catch (Exception e) {
            log.error("获取NAS根目录失败", e);
            return Result.error("获取目录列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定目录的内容
     */
    @ApiOperation(value = "获取目录内容", notes = "获取指定目录下的文件和子目录")
    @GetMapping(value = "/content")
    public Result<?> getDirectoryContent(
            @ApiParam("相对路径") @RequestParam(required = false) String path) {
        try {
            log.info("获取目录内容，路径: {}", path);

            NasFileReader.NasDirectoryContent content = nasFileReader.getDirectoryContent(path);

            log.info("获取目录内容成功，子目录: {}, 文件: {}",
                    content.getSubDirectories().size(), content.getFiles().size());
            return Result.ok(content);

        } catch (Exception e) {
            log.error("获取目录内容失败，路径: {}", path, e);
            return Result.error("获取目录内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有视频文件
     */
    @ApiOperation(value = "获取视频文件", notes = "递归获取指定目录下的所有视频文件")
    @GetMapping(value = "/videos")
    public Result<?> getAllVideoFiles(
            @ApiParam("相对路径") @RequestParam(required = false) String path) {
        try {
            log.info("获取视频文件列表，路径: {}", path);

            List<NasFileReader.NasFile> videoFiles = nasFileReader.getAllVideoFiles(path);

            log.info("获取视频文件成功，共 {} 个视频文件", videoFiles.size());
            return Result.ok(videoFiles);

        } catch (Exception e) {
            log.error("获取视频文件失败，路径: {}", path, e);
            return Result.error("获取视频文件失败: " + e.getMessage());
        }
    }

    /**
     * 搜索文件
     */
    @ApiOperation(value = "搜索文件", notes = "在指定目录下搜索包含关键词的文件")
    @GetMapping(value = "/search")
    public Result<?> searchFiles(
            @ApiParam("搜索关键词") @RequestParam String keyword,
            @ApiParam("搜索路径") @RequestParam(required = false) String path) {
        try {
            log.info("搜索文件，关键词: {}, 路径: {}", keyword, path);

            List<NasFileReader.NasFile> results = nasFileReader.searchFiles(keyword, path);

            log.info("搜索文件完成，找到 {} 个匹配文件", results.size());
            return Result.ok(results);

        } catch (Exception e) {
            log.error("搜索文件失败，关键词: {}, 路径: {}", keyword, path, e);
            return Result.error("搜索文件失败: " + e.getMessage());
        }
    }

    /**
     * 测试NAS连接
     */
    @ApiOperation(value = "测试NAS连接", notes = "测试与NAS服务器的连接状态")
    @GetMapping(value = "/test-connection")
    public Result<?> testConnection() {
        try {
            log.info("测试NAS连接");

            List<NasFileReader.NasDirectory> directories = nasFileReader.getRootDirectories();

            ConnectionStatus status = new ConnectionStatus();
            status.setConnected(true);
            status.setMessage("连接成功");
            status.setRootDirectoryCount(directories.size());
            status.setTestTime(System.currentTimeMillis());

            log.info("NAS连接测试成功");
            return Result.ok(status);

        } catch (Exception e) {
            log.error("NAS连接测试失败", e);

            ConnectionStatus status = new ConnectionStatus();
            status.setConnected(false);
            status.setMessage("连接失败: " + e.getMessage());
            status.setRootDirectoryCount(0);
            status.setTestTime(System.currentTimeMillis());

            return Result.ok(status);
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024));
        return String.format("%.1f GB", size / (1024.0 * 1024 * 1024));
    }

    /**
     * 目录统计信息
     */
    public static class DirectoryStats {
        private int directoryCount;
        private int fileCount;
        private int videoCount;
        private long totalSize;
        private String formattedTotalSize;
        private long videoSize;
        private String formattedVideoSize;

        // Getters and Setters
        public int getDirectoryCount() {
            return directoryCount;
        }

        public void setDirectoryCount(int directoryCount) {
            this.directoryCount = directoryCount;
        }

        public int getFileCount() {
            return fileCount;
        }

        public void setFileCount(int fileCount) {
            this.fileCount = fileCount;
        }

        public int getVideoCount() {
            return videoCount;
        }

        public void setVideoCount(int videoCount) {
            this.videoCount = videoCount;
        }

        public long getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(long totalSize) {
            this.totalSize = totalSize;
        }

        public String getFormattedTotalSize() {
            return formattedTotalSize;
        }

        public void setFormattedTotalSize(String formattedTotalSize) {
            this.formattedTotalSize = formattedTotalSize;
        }

        public long getVideoSize() {
            return videoSize;
        }

        public void setVideoSize(long videoSize) {
            this.videoSize = videoSize;
        }

        public String getFormattedVideoSize() {
            return formattedVideoSize;
        }

        public void setFormattedVideoSize(String formattedVideoSize) {
            this.formattedVideoSize = formattedVideoSize;
        }
    }

    /**
     * 连接状态
     */
    public static class ConnectionStatus {
        private boolean connected;
        private String message;
        private int rootDirectoryCount;
        private long testTime;

        // Getters and Setters
        public boolean isConnected() {
            return connected;
        }

        public void setConnected(boolean connected) {
            this.connected = connected;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getRootDirectoryCount() {
            return rootDirectoryCount;
        }

        public void setRootDirectoryCount(int rootDirectoryCount) {
            this.rootDirectoryCount = rootDirectoryCount;
        }

        public long getTestTime() {
            return testTime;
        }

        public void setTestTime(long testTime) {
            this.testTime = testTime;
        }
    }
}
