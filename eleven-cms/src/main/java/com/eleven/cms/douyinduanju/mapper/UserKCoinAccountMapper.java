package com.eleven.cms.douyinduanju.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.douyinduanju.entity.UserKCoinAccount;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 用户K币账户Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface UserKCoinAccountMapper extends BaseMapper<UserKCoinAccount> {

    /**
     * 乐观锁更新账户余额
     *
     * @param userId                 用户ID
     * @param availableBalanceChange 可用余额变化量（正数为增加，负数为减少）
     * @param frozenBalanceChange    冻结余额变化量
     * @param totalRechargeChange    总充值变化量
     * @param totalConsumeChange     总消费变化量
     * @param version                当前版本号
     * @return 更新行数
     */
    int updateBalanceWithVersion(@Param("userId") String userId,
                                 @Param("availableBalanceChange") BigDecimal availableBalanceChange,
                                 @Param("frozenBalanceChange") BigDecimal frozenBalanceChange,
                                 @Param("totalRechargeChange") BigDecimal totalRechargeChange,
                                 @Param("totalConsumeChange") BigDecimal totalConsumeChange,
                                 @Param("version") Integer version);
}
