package com.eleven.cms.douyinduanju.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信支付签约记录实体
 */
@Data
@TableName("wechat_pay_signing")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "微信支付签约记录", description = "微信支付小程序签约记录")
public class WechatPaySigning implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户openid
     */
    @ApiModelProperty(value = "用户openid")
    private String openid;

    /**
     * 小程序AppID
     */
    @ApiModelProperty(value = "小程序AppID")
    private String appid;

    /**
     * 商户号
     */
    @ApiModelProperty(value = "商户号")
    private String mchid;

    /**
     * 模板ID
     */
    @ApiModelProperty(value = "模板ID")
    private String planId;

    /**
     * 签约协议号
     */
    @ApiModelProperty(value = "签约协议号")
    private String contractCode;

    /**
     * 商户签约号
     */
    @ApiModelProperty(value = "商户签约号")
    private String outContractCode;

    /**
     * 微信签约ID
     */
    @ApiModelProperty(value = "微信签约ID")
    private String contractId;

    /**
     * 签约状态：0-未签约，1-已签约，2-已解约，3-签约失败
     */
    @ApiModelProperty(value = "签约状态：0-未签约，1-已签约，2-已解约，3-签约失败")
    private Integer signingStatus;

    /**
     * 签约显示账户
     */
    @ApiModelProperty(value = "签约显示账户")
    private String contractDisplayAccount;

    /**
     * 签约显示名称
     */
    @ApiModelProperty(value = "签约显示名称")
    private String contractDisplayName;

    /**
     * 签约时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签约时间")
    private Date contractSignTime;

    /**
     * 签约到期时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签约到期时间")
    private Date contractExpiredTime;

    /**
     * 签约银行类型
     */
    @ApiModelProperty(value = "签约银行类型")
    private String contractBankType;

    /**
     * 回调通知地址
     */
    @ApiModelProperty(value = "回调通知地址")
    private String notifyUrl;

    /**
     * 签约跳转页面
     */
    @ApiModelProperty(value = "签约跳转页面")
    private String returnWeb;

    /**
     * 签约请求URL
     */
    @ApiModelProperty(value = "签约请求URL")
    private String signingUrl;

    /**
     * 错误代码
     */
    @ApiModelProperty(value = "错误代码")
    private String errCode;

    /**
     * 错误描述
     */
    @ApiModelProperty(value = "错误描述")
    private String errCodeDes;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 是否删除：0-否，1-是
     */
    @ApiModelProperty(value = "是否删除：0-否，1-是")
    private Integer isDeleted;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 渠道来源
     */
    @ApiModelProperty(value = "渠道来源")
    private String channel;
}
