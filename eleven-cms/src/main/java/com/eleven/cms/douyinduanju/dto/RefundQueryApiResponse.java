package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

/**
 * 抖音退款查询API响应类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class RefundQueryApiResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 抖音退款ID
     */
    private String refundId;

    /**
     * 退款状态
     * SUCCESS: 退款成功
     * FAIL: 退款失败
     * PROCESSING: 退款处理中
     */
    private String refundStatus;

    /**
     * 退款金额（分）
     */
    private Integer refundAmount;

    /**
     * 退款时间（时间戳，毫秒）
     */
    private Long refundTime;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 错误码
     */
    private Integer errorCode;

    public RefundQueryApiResponse() {
    }

    public RefundQueryApiResponse(boolean success) {
        this.success = success;
    }

    /**
     * 成功响应
     */
    public static RefundQueryApiResponse success() {
        return new RefundQueryApiResponse(true);
    }

    /**
     * 失败响应
     */
    public static RefundQueryApiResponse error(String errorMessage) {
        RefundQueryApiResponse response = new RefundQueryApiResponse(false);
        response.setErrorMessage(errorMessage);
        return response;
    }

    /**
     * 失败响应（带错误码）
     */
    public static RefundQueryApiResponse error(String errorMessage, Integer errorCode) {
        RefundQueryApiResponse response = new RefundQueryApiResponse(false);
        response.setErrorMessage(errorMessage);
        response.setErrorCode(errorCode);
        return response;
    }

    /**
     * 获取退款状态描述
     */
    public String getRefundStatusDesc() {
        if (refundStatus == null) {
            return "未知状态";
        }

        switch (refundStatus) {
            case "SUCCESS":
                return "退款成功";
            case "FAIL":
                return "退款失败";
            case "PROCESSING":
                return "退款处理中";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取格式化的退款金额
     */
    public String getFormattedRefundAmount() {
        if (refundAmount == null) {
            return "0.00";
        }
        return String.format("%.2f", refundAmount / 100.0);
    }

    @Override
    public String toString() {
        return "RefundQueryApiResponse{" +
                "success=" + success +
                ", refundId='" + refundId + '\'' +
                ", refundStatus='" + refundStatus + '\'' +
                ", refundAmount=" + refundAmount +
                ", refundTime=" + refundTime +
                ", refundReason='" + refundReason + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", errorCode=" + errorCode +
                '}';
    }
}
