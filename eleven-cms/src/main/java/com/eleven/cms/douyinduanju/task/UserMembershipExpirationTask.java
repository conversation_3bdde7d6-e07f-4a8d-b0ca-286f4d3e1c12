package com.eleven.cms.douyinduanju.task;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eleven.cms.douyinduanju.entity.UserMembership;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.service.IUserMembershipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员过期检查定时任务
 * 定期检查会员记录的过期时间，更新过期状态，并激活下一个未生效的会员
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Component
@Slf4j
public class UserMembershipExpirationTask {

    @Autowired
    private IUserMembershipService userMembershipService;

    /**
     * 每小时检查一次会员过期状态
     * cron表达式: 秒 分 时 日 月 周
     * 0 0 * * * ? 表示每小时整点执行
     */
    @Scheduled(cron = "0 29 * * * ?")
    public void checkMembershipExpiration() {
        log.info("开始执行会员过期检查定时任务");

        try {
            long startTime = System.currentTimeMillis();

            // 执行过期检查
            int expiredCount = processExpiredMemberships();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("会员过期检查定时任务执行完成，处理过期会员数: {}，耗时: {} ms", expiredCount, duration);


        } catch (Exception e) {
            log.error("会员过期检查定时任务执行异常", e);
        }
    }

    /**
     * 处理过期的会员记录
     *
     * @return 处理的过期会员数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int processExpiredMemberships() {
        Date currentTime = new Date();
        int expiredCount = 0;
        int activatedCount = 0;

        try {
            // 1. 查询所有已过期但状态未更新的会员记录
            LambdaQueryWrapper<UserMembership> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserMembership::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                    .lt(UserMembership::getExpireTime, currentTime)
                    .eq(UserMembership::getIsDeleted, 0);

            List<UserMembership> expiredMemberships = userMembershipService.list(queryWrapper);

            if (expiredMemberships.isEmpty()) {
                log.info("没有需要处理的过期会员记录");
                return 0;
            }

            log.info("发现 {} 个已过期的会员记录需要处理", expiredMemberships.size());

            // 2. 处理每个过期的会员记录
            for (UserMembership membership : expiredMemberships) {
                try {
                    // 更新会员状态为已过期
                    UserMembership updateMembership = new UserMembership();
                    updateMembership.setId(membership.getId());
                    updateMembership.setStatus(OrderEnums.BenefitStatus.EXPIRED.getCode());
                    updateMembership.setUpdateTime(currentTime);
                    updateMembership.setUpdateBy("system");

                    boolean updateResult = userMembershipService.updateById(updateMembership);

                    if (updateResult) {
                        expiredCount++;
                        log.info("会员记录已更新为过期状态，ID: {}, 用户ID: {}, 过期时间: {}",
                                membership.getId(), membership.getUserId(), membership.getExpireTime());

                        // 3. 尝试激活该用户的下一个未生效会员
                        boolean activated = userMembershipService.activateNextInactiveMembership(membership.getUserId());

                        if (activated) {
                            activatedCount++;
                            log.info("已自动激活用户的下一个会员，用户ID: {}", membership.getUserId());
                        }
                    } else {
                        log.warn("更新会员过期状态失败，ID: {}", membership.getId());
                    }

                } catch (Exception e) {
                    log.error("处理过期会员记录异常，ID: {}", membership.getId(), e);
                }
            }


            //更新会员状态
            if (CollectionUtil.isNotEmpty(expiredMemberships)) {
                List<String> userIdList = expiredMemberships.stream().map(UserMembership::getUserId).distinct().collect(Collectors.toList());
                for (String userId : userIdList) {
                    userMembershipService.updateUserMemberInfo(userId);
                }
            }
            log.info("会员过期处理完成，共处理 {} 个过期会员，激活 {} 个新会员", expiredCount, activatedCount);

        } catch (Exception e) {
            log.error("查询过期会员记录异常", e);
            throw e;
        }

        return expiredCount;
    }


}
