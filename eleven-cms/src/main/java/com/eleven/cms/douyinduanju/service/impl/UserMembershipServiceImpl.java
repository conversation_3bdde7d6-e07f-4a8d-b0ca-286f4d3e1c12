package com.eleven.cms.douyinduanju.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.MembershipStatusInfo;
import com.eleven.cms.douyinduanju.entity.DuanJuPackage;
import com.eleven.cms.douyinduanju.entity.DuanJuUser;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.entity.UserMembership;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.mapper.UserMembershipMapper;
import com.eleven.cms.douyinduanju.service.IDuanJuUserService;
import com.eleven.cms.douyinduanju.service.IUserMembershipService;
import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 用户会员权益服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class UserMembershipServiceImpl extends ServiceImpl<UserMembershipMapper, UserMembership>
        implements IUserMembershipService {

    @Resource
    private IDuanJuUserService duanJuUserService;

    @Override
    public UserMembership getValidMembershipByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }

        try {
            // 查询用户当前有效的会员信息（按会员等级降序，优先返回最高等级）
            UserMembership membership = lambdaQuery()
                    .eq(UserMembership::getUserId, userId)
                    .eq(UserMembership::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                    .eq(UserMembership::getIsDeleted, 0)
                    .orderByAsc(UserMembership::getMembershipType)
                    .orderByDesc(UserMembership::getExpireTime)
                    .last("limit 1")
                    .one();
            return membership;

        } catch (Exception e) {
            log.error("获取有效会员信息异常，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 激活会员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateMembership(MiniAppDuanJuOrder order, DuanJuPackage packageInfo) {
        String userId = order.getUserId();
        String orderId = order.getOrderNo();
        Integer packageInfoType = packageInfo.getType(); // 1会员  2:剧卡 3:K币
        Integer durationDays = packageInfo.getDurationDays();

        if (Objects.equals(packageInfo.getPayType(), OrderEnums.PackageType.MEMBERSHIP.getCode()) && Objects.equals(packageInfo.getPayType(), 1)) {
            log.info("话费套餐不做校验会员订单");
        } else if (StringUtils.isEmpty(userId) || packageInfoType == null || durationDays == null || durationDays <= 0) {
            throw new BusinessException("开通会员参数无效");
        }

        try {
            log.info("开始激活会员，用户ID: {}, 套餐类型: {}, 支付类型: {}, 时长: {}天",
                    userId, packageInfoType, packageInfo.getPayType(), durationDays);

            // 检查会员类型是否有效
            OrderEnums.MembershipType memberType = OrderEnums.MembershipType.getByPackageTypeCode(packageInfoType);
            if (memberType == null) {
                throw new BusinessException("无效的会员类型");
            }

            // 获取当前用户的所有会员记录
            List<UserMembership> existingMemberships = getUserAllMemberships(userId);

            // 创建新的会员记录
            UserMembership newMembership = createNewMembership(order, packageInfo);

            // 根据支付类型处理激活逻辑
            if (Objects.equals(packageInfo.getPayType(), 1)) {
                // 话费支付 - 优先激活
                return activatePhoneMembership(newMembership, existingMemberships, userId);
            } else if (Objects.equals(packageInfo.getPayType(), 2)) {
                // 三方支付 - 检查是否有话费会员
                return activateThirdPartyMembership(newMembership, userId);
            } else {
                throw new BusinessException("不支持的支付类型: " + packageInfo.getPayType());
            }

        } catch (Exception e) {
            log.error("开通会员权益异常，用户ID: {}, 会员类型: {}, 订单ID: {}", userId, packageInfoType, orderId, e);
            throw new BusinessException("开通会员权益失败: " + e.getMessage());
        }
    }

    /**
     * 创建新的会员记录
     */
    private UserMembership createNewMembership(MiniAppDuanJuOrder order, DuanJuPackage packageInfo) {
        UserMembership membership = new UserMembership();
        membership.setUserId(order.getUserId());
        membership.setOrderId(order.getOrderNo());
        membership.setMembershipName(packageInfo.getName());
        membership.setDurationDays(packageInfo.getDurationDays());
        membership.setAutoRenew(0); // 默认不自动续费
        membership.setCreateTime(new Date());
        membership.setUpdateTime(new Date());
        membership.setCreateBy("system");
        membership.setIsDeleted(0);

        // 根据支付类型设置会员类型
        if (Objects.equals(packageInfo.getPayType(), 1)) {
            membership.setExpireTime(DateUtil.endOfMonth(new Date()));
            membership.setMembershipType(1); // 话费支付
        } else if (Objects.equals(packageInfo.getPayType(), 2)) {
            membership.setMembershipType(4); // 三方支付
        }

        return membership;
    }

    /**
     * 激活话费会员套餐（优先激活）
     */
    private boolean activatePhoneMembership(UserMembership newMembership, List<UserMembership> existingMemberships, String userId) {
        log.info("激活话费会员套餐，用户ID: {}", userId);

        // 查找当前生效的会员
        UserMembership activeMembership = getValidMembershipByUserId(userId);

        if (activeMembership == null) {
            // 没有生效的会员，直接激活
            Date startTime = new Date();
            Date expireTime = DateUtil.endOfMonth(new Date());

            newMembership.setStartTime(startTime);
            newMembership.setExpireTime(expireTime);
            newMembership.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());

            boolean result = save(newMembership);
            log.info("话费会员直接激活成功，用户ID: {}, 过期时间: {}", userId, expireTime);
            return result;

        } else {
            // 有生效的会员，新得权益会员设置为生效
            if (activeMembership.getMembershipType() == 1) {
                newMembership.setStatus(OrderEnums.BenefitStatus.INACTIVE.getCode()); // 设置为未生效状态
                return save(newMembership);
            } else if (activeMembership.getMembershipType() == 4) {
                // 当前是三方会员，暂停三方会员并激活话费会员
                return suspendThirdPartyAndActivatePhone(activeMembership, newMembership, userId);
            } else {
                // 其他类型会员，设置为未生效状态
                newMembership.setStatus(OrderEnums.BenefitStatus.INACTIVE.getCode());
                boolean result = save(newMembership);
                log.info("话费会员设置为未生效状态，用户ID: {}", userId);
                return result;
            }
        }
    }

    /**
     * 激活三方支付会员套餐
     */
    private boolean activateThirdPartyMembership(UserMembership newMembership,  String userId) {
        log.info("激活三方支付会员套餐，用户ID: {}", userId);

        // 查找当前生效的会员
        UserMembership activeMembership = getValidMembershipByUserId(userId);

        if (activeMembership == null) {
            // 没有生效的会员，直接激活
            Date startTime = new Date();
            Date expireTime = DateUtil.offsetDay(startTime, newMembership.getDurationDays());

            newMembership.setStartTime(startTime);
            newMembership.setExpireTime(expireTime);
            newMembership.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());

            boolean result = save(newMembership);
            log.info("三方会员直接激活成功，用户ID: {}, 过期时间: {}", userId, expireTime);
            return result;

        } else {
            if (activeMembership.getMembershipType() == 1) {
                // 当前是话费会员，三方会员设置为未生效
                newMembership.setStatus(OrderEnums.BenefitStatus.INACTIVE.getCode());
                boolean result = save(newMembership);
                log.info("存在话费会员，三方会员设置为未生效状态，用户ID: {}", userId);
                return result;
            } else {
                // 当前是其他会员，设置未生效
                newMembership.setStatus(OrderEnums.BenefitStatus.INACTIVE.getCode());
                boolean result = save(newMembership);
                log.info("已存在权益套餐，用户ID:  {}", userId);
                return result;
            }
        }
    }

    /**
     * 获取用户所有会员记录
     */
    private List<UserMembership> getUserAllMemberships(String userId) {
        return lambdaQuery()
                .eq(UserMembership::getUserId, userId)
                .eq(UserMembership::getIsDeleted, 0)
                .eq(UserMembership::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                .orderByDesc(UserMembership::getCreateTime)
                .list();
    }

    /**
     * 暂停三方会员并激活话费会员
     */
    private boolean suspendThirdPartyAndActivatePhone(UserMembership thirdPartyMembership, UserMembership phoneMembership, String userId) {
        log.info("暂停三方会员并激活话费会员，用户ID: {}", userId);

        try {
            // 计算三方会员已使用的天数
            Date now = new Date();
            Date startTime = thirdPartyMembership.getStartTime();
            long usedDays = 0;

            if (startTime != null && startTime.before(now)) {
                long diffInMillies = now.getTime() - startTime.getTime();
                usedDays = diffInMillies / (24 * 60 * 60 * 1000);
            }

            // 计算三方会员剩余天数
            long totalDays = thirdPartyMembership.getDurationDays();
            long remainingDays = Math.max(0, totalDays - usedDays);

            log.info("三方会员使用情况，用户ID: {}, 总天数: {}, 已使用: {}天, 剩余: {}天",
                    userId, totalDays, usedDays, remainingDays);

            // 暂停三方会员
            thirdPartyMembership.setStatus(OrderEnums.BenefitStatus.INACTIVE.getCode());
            thirdPartyMembership.setUpdateTime(now);
            thirdPartyMembership.setDurationDays((int) remainingDays);
            // 保存剩余天数到备注字段
            thirdPartyMembership.setRemark("暂停时剩余天数: " + remainingDays + ", 已使用天数: " + usedDays);

            boolean suspendResult = updateById(thirdPartyMembership);

            if (!suspendResult) {
                log.error("暂停三方会员失败，用户ID: {}", userId);
                return false;
            }

            // 激活话费会员
            Date phoneStartTime = new Date();
            Date phoneExpireTime = DateUtil.offsetDay(phoneStartTime, phoneMembership.getDurationDays());

            phoneMembership.setStartTime(phoneStartTime);
            phoneMembership.setExpireTime(phoneExpireTime);
            phoneMembership.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());

            boolean activateResult = save(phoneMembership);

            if (activateResult) {
                log.info("话费会员激活成功，三方会员已暂停，用户ID: {}, 话费会员过期时间: {}", userId, phoneExpireTime);
            } else {
                log.error("话费会员激活失败，用户ID: {}", userId);
                // 回滚三方会员状态
                thirdPartyMembership.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());
                updateById(thirdPartyMembership);
            }

            return activateResult;

        } catch (Exception e) {
            log.error("暂停三方会员并激活话费会员异常，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 检查并处理DuanJuUser过期状态
     * 1. 检查DuanJuUser是否过期
     * 2. 如果过期，检查是否有生效的会员
     * 3. 如果没有生效会员，尝试激活下一个会员
     * 4. 如果没有可用会员，设置会员状态为1（非会员）
     *
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean checkAndHandleUserExpiration(String userId) {
        try {
            log.info("开始检查用户过期状态，用户ID: {}", userId);

            if (StringUtils.isBlank(userId)) {
                log.warn("用户ID为空，跳过检查");
                return false;
            }

            // 1. 获取DuanJuUser信息
            DuanJuUser duanJuUser = duanJuUserService.getById(userId);
            if (duanJuUser == null) {
                log.warn("用户不存在，用户ID: {}", userId);
                return false;
            }

            // 2. 检查DuanJuUser是否过期
            boolean isUserExpired = checkUserExpired(duanJuUser);
            if (!isUserExpired) {
                log.debug("用户未过期，无需处理，用户ID: {}", userId);
                return true;
            }

            log.info("用户已过期，开始处理，用户ID: {}, 过期时间: {}", userId, duanJuUser.getExpireTime());

            // 3. 检查是否有生效的会员
            MembershipStatusInfo membershipStatus = getUserMembershipStatus(userId);
            if (membershipStatus.getHasActiveMembership()) {
                log.info("用户有生效的会员，更新用户状态，用户ID: {}", userId);
                updateUserMemberInfo(userId);
                return true;
            }

            // 4. 没有生效会员，尝试激活下一个会员
            boolean activateResult = activateNextInactiveMembership(userId);
            if (activateResult) {
                log.info("成功激活下一个会员，重新获取会员状态，用户ID: {}", userId);
                // 更新用户会员状态信息
                updateUserMemberInfo(userId);
                return true;
            }

            // 5. 没有可用会员，设置为非会员状态
            log.info("没有可用会员，设置为非会员状态，用户ID: {}", userId);
            return setUserAsNonMember(duanJuUser);

        } catch (Exception e) {
            log.error("检查用户过期状态异常，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 检查DuanJuUser是否过期
     */
    private boolean checkUserExpired(DuanJuUser duanJuUser) {
        Date expireTime = duanJuUser.getExpireTime();
        if (expireTime == null) {
            log.debug("用户过期时间为空，视为未过期，用户ID: {}", duanJuUser.getId());
            return false;
        }

        Date now = new Date();
        boolean isExpired = expireTime.before(now);

        log.debug("用户过期检查结果，用户ID: {}, 过期时间: {}, 当前时间: {}, 是否过期: {}",
                duanJuUser.getId(), expireTime, now, isExpired);

        return isExpired;
    }

    /**
     * 根据会员状态更新用户信息
     */
    @Override
    public void updateUserMemberInfo(String userId) {
        DuanJuUser duanJuUser = duanJuUserService.getById(userId);

        //获取会员状态
        MembershipStatusInfo userMembershipStatus = getUserMembershipStatus(userId);
        if (userMembershipStatus.getHasActiveMembership()) {
            UserMembership currentMembership = userMembershipStatus.getCurrentMembership();

            if (Objects.equals(currentMembership.getMembershipType(), 4)) {
                duanJuUser.setMemberStatus(2);
                duanJuUser.setThirdPartySubExpireStatus(0);
                duanJuUser.setMemberType(3);
            } else {
                duanJuUser.setThirdPartySubExpireStatus(1);
            }
            if (Objects.equals(currentMembership.getMembershipType(), 1)) {
                duanJuUser.setMemberStatus(2);
                duanJuUser.setPhoneSubExpireStatus(0);
                duanJuUser.setMemberType(2);
            } else {
                duanJuUser.setPhoneSubExpireStatus(1);
            }
            duanJuUser.setExpireTime(userMembershipStatus.getTotalExpireTime());
            duanJuUser.setPayFlat(1);
        } else {
            List<UserMembership> expiredMemberships = userMembershipStatus.getExpiredAndCancelMemberships();
            if (CollectionUtil.isNotEmpty(expiredMemberships)) {
                duanJuUser.setMemberStatus(3);
            } else {
                duanJuUser.setMemberStatus(1);
            }
            duanJuUser.setPhoneSubExpireStatus(1);
            duanJuUser.setThirdPartySubExpireStatus(1);
        }

        //赋值支付标识

        duanJuUser.setId(Integer.valueOf(userId));
        duanJuUserService.updateById(duanJuUser);
    }

    /**
     * 设置用户为非会员状态
     */
    private boolean setUserAsNonMember(DuanJuUser duanJuUser) {
        try {
            DuanJuUser updateUser = new DuanJuUser();
            updateUser.setId(duanJuUser.getId());
            updateUser.setMemberStatus(3); // 会员过期
            updateUser.setPhoneSubExpireStatus(1); // 已过期
            updateUser.setThirdPartySubExpireStatus(1); // 已过期
            updateUser.setExpireTime(null); // 清空过期时间
            boolean result = duanJuUserService.updateById(updateUser);

            if (result) {
                log.info("用户设置为非会员状态成功，用户ID: {}", duanJuUser.getId());
            } else {
                log.error("用户设置为非会员状态失败，用户ID: {}", duanJuUser.getId());
            }
            return result;

        } catch (Exception e) {
            log.error("设置用户为非会员状态异常，用户ID: {}", duanJuUser.getId(), e);
            return false;
        }
    }
    /**
     * 获取用户未生效的会员记录
     *
     * @param userId 用户ID
     * @return 未生效的会员记录列表
     */
    @Override
    public List<UserMembership> getInactiveMembershipsByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        try {
            return lambdaQuery()
                    .eq(UserMembership::getUserId, userId)
                    .eq(UserMembership::getStatus, OrderEnums.BenefitStatus.INACTIVE.getCode())
                    .eq(UserMembership::getIsDeleted, 0)
                    .orderByAsc(UserMembership::getCreateTime) // 按创建时间升序，优先生效早购买的
                    .list();

        } catch (Exception e) {
            log.error("获取用户未生效会员记录异常，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 获取用户已生效的会员记录
     *
     * @param userId 用户ID
     * @return 已生效的会员记录列表
     */
    @Override
    public List<UserMembership> getActiveMembershipsByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        try {
            return lambdaQuery()
                    .eq(UserMembership::getUserId, userId)
                    .eq(UserMembership::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                    .eq(UserMembership::getIsDeleted, 0)
                    .orderByDesc(UserMembership::getExpireTime) // 按过期时间降序
                    .list();

        } catch (Exception e) {
            log.error("获取用户已生效会员记录异常，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 计算用户的实际过期时间（基于生效和未生效的会员记录）
     *
     * @param userId 用户ID
     * @return 计算后的过期时间，如果没有会员则返回null
     */
    @Override
    public Date calculateUserExpireTime(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }

        try {
            // 获取当前生效的会员
            List<UserMembership> activeMemberships = getActiveMembershipsByUserId(userId);
            // 获取未生效的会员
            List<UserMembership> inactiveMemberships = getInactiveMembershipsByUserId(userId);

            Date currentTime = new Date();
            Date finalExpireTime = null;

            // 1. 先处理已生效的会员，找到最晚的过期时间
            if (activeMemberships != null && !activeMemberships.isEmpty()) {
                for (UserMembership membership : activeMemberships) {
                    if (membership.getExpireTime() != null) {
                        if (finalExpireTime == null || membership.getExpireTime().after(finalExpireTime)) {
                            finalExpireTime = membership.getExpireTime();
                        }
                    }
                }
            }

            // 2. 处理未生效的会员，按创建时间顺序累加时长
            if (inactiveMemberships != null && !inactiveMemberships.isEmpty()) {
                // 如果没有生效的会员，从当前时间开始计算
                Date baseTime = finalExpireTime != null ? finalExpireTime : currentTime;

                for (UserMembership membership : inactiveMemberships) {
                    Integer durationDays = membership.getDurationDays();
                    if (durationDays != null && durationDays > 0) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(baseTime);
                        calendar.add(Calendar.DAY_OF_MONTH, durationDays);
                        baseTime = calendar.getTime();
                    }
                }
                finalExpireTime = baseTime;
            }

            log.info("计算用户过期时间完成，用户ID: {}, 过期时间: {}, 生效会员数: {}, 未生效会员数: {}",
                    userId, finalExpireTime,
                    activeMemberships != null ? activeMemberships.size() : 0,
                    inactiveMemberships != null ? inactiveMemberships.size() : 0);

            return finalExpireTime;

        } catch (Exception e) {
            log.error("计算用户过期时间异常，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 激活下一个未生效的会员（当前会员过期时调用）
     *
     * @param userId 用户ID
     * @return 是否成功激活
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateNextInactiveMembership(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return false;
        }


        UserMembership activeMembership = lambdaQuery()
                .eq(UserMembership::getUserId, userId)
                .eq(UserMembership::getStatus, OrderEnums.BenefitStatus.ACTIVE.getCode())
                .eq(UserMembership::getIsDeleted, 0)
                .orderByAsc(UserMembership::getCreateTime) // 按创建时间升序，优先激活早购买的
                .last("limit 1")
                .one();
        if (Objects.nonNull(activeMembership)) {
            log.info("用户存在激活会员，会员ID：{}", activeMembership.getId());
            return true;
        }

        try {
            // 1. 获取最早购买的未生效会员
            UserMembership nextMembership = lambdaQuery()
                    .eq(UserMembership::getUserId, userId)
                    .eq(UserMembership::getStatus, OrderEnums.BenefitStatus.INACTIVE.getCode())
                    .eq(UserMembership::getIsDeleted, 0)
                    .orderByAsc(UserMembership::getMembershipType) //优先机会话费会员
                    .orderByAsc(UserMembership::getCreateTime) // 按创建时间升序，优先激活早购买的
                    .last("limit 1")
                    .one();

            if (nextMembership == null) {
                log.info("用户没有未生效的会员记录，用户ID: {}", userId);
                return false;
            }

            // 2. 激活会员
            Date currentTime = new Date();
            Integer durationDays = nextMembership.getDurationDays();

            if (durationDays == null || durationDays <= 0) {
                log.warn("会员时长无效，跳过激活，用户ID: {}, 会员ID: {}", userId, nextMembership.getId());
                return false;
            }

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentTime);
            calendar.add(Calendar.DAY_OF_MONTH, durationDays);
            Date expireTime = calendar.getTime();

            nextMembership.setStatus(OrderEnums.BenefitStatus.ACTIVE.getCode());
            nextMembership.setStartTime(currentTime);
            nextMembership.setExpireTime(expireTime);
            nextMembership.setUpdateTime(currentTime);
            nextMembership.setUpdateBy("system");

            boolean result = updateById(nextMembership);

            if (result) {
                log.info("激活下一个会员成功，用户ID: {}, 会员ID: {}, 会员类型: {}, 过期时间: {}",
                        userId, nextMembership.getId(), nextMembership.getMembershipType(), expireTime);
                return true;
            } else {
                throw new BusinessException("激活会员失败");
            }

        } catch (Exception e) {
            log.error("激活下一个未生效会员异常，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 获取用户会员的完整状态信息
     *
     * @param userId 用户ID
     * @return 会员状态信息
     */
    @Override
    public MembershipStatusInfo getUserMembershipStatus(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }

        try {
            MembershipStatusInfo statusInfo = new MembershipStatusInfo();
            statusInfo.setUserId(userId);

            // 获取当前生效的会员
            UserMembership currentMembership = getValidMembershipByUserId(userId);
            statusInfo.setCurrentMembership(currentMembership);
            statusInfo.setHasActiveMembership(currentMembership != null);

            // 获取未生效的会员列表
            List<UserMembership> inactiveMemberships = getInactiveMembershipsByUserId(userId);
            statusInfo.setInactiveMemberships(inactiveMemberships);
            statusInfo.setInactiveMembershipCount(inactiveMemberships != null ? inactiveMemberships.size() : 0);

            //获取已过期的会员列表
            List<UserMembership> expiredMemberships = getExpireAndCancelMembershipsByUserId(userId);
            statusInfo.setExpiredAndCancelMemberships(expiredMemberships);

            // 计算总的过期时间
            Date totalExpireTime = calculateUserExpireTime(userId);
            statusInfo.setTotalExpireTime(totalExpireTime);

            // 计算剩余天数
            if (totalExpireTime != null) {
                long diffInMillies = totalExpireTime.getTime() - new Date().getTime();
                long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
                statusInfo.setRemainingDays(Math.max(0, (int) diffInDays));
            } else {
                statusInfo.setRemainingDays(0);
            }

            return statusInfo;

        } catch (Exception e) {
            log.error("获取用户会员状态信息异常，用户ID: {}", userId, e);
            return null;
        }
    }

    private List<UserMembership> getExpireAndCancelMembershipsByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        try {
            return lambdaQuery()
                    .eq(UserMembership::getUserId, userId)
                    .in(UserMembership::getStatus, OrderEnums.BenefitStatus.EXPIRED.getCode(), OrderEnums.BenefitStatus.CANCELLED.getCode())
                    .eq(UserMembership::getIsDeleted, 0)
                    .orderByAsc(UserMembership::getCreateTime) // 按创建时间升序，优先生效早购买的
                    .list();

        } catch (Exception e) {
            log.error("获取用户未生效会员记录异常，用户ID: {}", userId, e);
            return null;
        }
    }
}
