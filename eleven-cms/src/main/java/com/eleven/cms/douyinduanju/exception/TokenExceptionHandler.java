package com.eleven.cms.douyinduanju.exception;

import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Token相关异常处理器
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@RestControllerAdvice
@Order(1) // 设置优先级，确保在其他异常处理器之前执行
public class TokenExceptionHandler {

    /**
     * 处理Token校验失败异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<?> handleTokenException(BusinessException e) {
        String message = e.getMessage();

        // 判断是否为token相关异常
        if (message != null && (message.contains("Token") || message.contains("token"))) {
            log.warn("Token校验异常: {}", message);

            // 返回401状态码和错误信息
            Result<?> result = Result.error(401, message);
            result.setSuccess(false);
            return result;
        }

        // 如果不是token相关异常，继续抛出让其他异常处理器处理
        throw e;
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<?> handleAuthenticationException(AuthenticationException e) {
        log.warn("认证异常: {}", e.getMessage());

        Result<?> result = Result.error(401, e.getMessage());
        result.setSuccess(false);
        return result;
    }

    /**
     * 处理授权异常
     */
    @ExceptionHandler(AuthorizationException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<?> handleAuthorizationException(AuthorizationException e) {
        log.warn("授权异常: {}", e.getMessage());

        Result<?> result = Result.error(403, e.getMessage());
        result.setSuccess(false);
        return result;
    }
}
