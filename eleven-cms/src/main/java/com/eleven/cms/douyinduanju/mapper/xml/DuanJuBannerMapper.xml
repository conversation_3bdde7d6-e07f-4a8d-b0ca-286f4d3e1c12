<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.DuanJuBannerMapper">

    <!-- 更新Banner信息（包括空值字段） -->
    <update id="updateByIdWithNull" parameterType="com.eleven.cms.douyinduanju.entity.DuanJuBanner">
        UPDATE duan_ju_banner
        SET type               = #{banner.type},
            location           = #{banner.location},
            description        = #{banner.description},
            image              = #{banner.image},
            jump_source        = #{banner.jumpSource},
            jump_type          = #{banner.jumpType},
            order_by           = #{banner.orderBy},
            album_id           = #{banner.albumId},
            album_name         = #{banner.albumName},
            dou_yin_episode_id = #{banner.douYinEpisodeId},
            episode_seq        = #{banner.episodeSeq},
            status             = #{banner.status},
            update_by          = #{banner.updateBy},
            update_time        = #{banner.updateTime}
        WHERE id = #{banner.id}
    </update>
</mapper>
