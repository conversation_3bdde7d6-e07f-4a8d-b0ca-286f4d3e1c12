package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.dto.CatalogResUsageInfoDTO;
import com.eleven.cms.aivrbt.entity.MiniAppCatalog;
import com.eleven.cms.aivrbt.entity.MiniAppCatalogResource;
import com.eleven.cms.aivrbt.enums.CatalogSubResourceTypeEnum;
import com.eleven.cms.aivrbt.enums.StatusEnum;
import com.eleven.cms.aivrbt.service.IAppCatalogResourceService;
import com.eleven.cms.aivrbt.service.IAppCatalogService;
import com.eleven.cms.douyinduanju.dto.AlbumInfVO;
import com.eleven.cms.douyinduanju.dto.DramaReq;
import com.eleven.cms.douyinduanju.dto.LookHistoryVO;
import com.eleven.cms.douyinduanju.dto.MiniAppMiniDramaReq;
import com.eleven.cms.douyinduanju.entity.*;
import com.eleven.cms.douyinduanju.mapper.MiniAppMiniDramaMapper;
import com.eleven.cms.douyinduanju.service.*;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.service.IDouyinAppConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Service
public class MiniAppMiniDramaServiceImpl extends ServiceImpl<MiniAppMiniDramaMapper, MiniAppMiniDrama> implements IMiniAppMiniDramaService {

    @Override
    public IPage<AlbumInfVO> pageAlbumInfo(DramaReq req, IPage<AlbumInfVO> iPage) {
        IPage<AlbumInfVO> albumInfVOIPage = this.baseMapper.pageDrama(iPage, req);
        findUsageInfo(albumInfVOIPage.getRecords());
        return albumInfVOIPage;
    }

    @Override
    public MiniAppMiniDrama getByAlbumId(String albumId) {
        return lambdaQuery().eq(MiniAppMiniDrama::getAlbumId, albumId).eq(MiniAppMiniDrama::getIsDeleted, 0).last("limit 1").one();
    }

    @Override
    public MiniAppMiniDrama getByAlbumInfoId(String albumInfoId) {
        return lambdaQuery().eq(MiniAppMiniDrama::getAlbumInfoId, albumInfoId).eq(MiniAppMiniDrama::getIsDeleted, 0).last("limit 1").one();
    }

    @Override
    public List<MiniAppMiniDrama> listDrama(String columnId, List<String> ids) {
        return this.baseMapper.listDrama(columnId, ids);
    }


    @Override
    public IPage<MiniAppMiniDrama> listHostDrama(String columnId, IPage<MiniAppMiniDrama> iPage, List<String> ids) {
        return this.baseMapper.listHostDrama(iPage, columnId, ids);
    }

    @Resource
    private IOpenApiAlbumInfoService openApiAlbumInfoService;

    @Resource
    private IOpenApiUploadPicRecordService picRecordService;



    @Resource
    private IAppCatalogResourceService appCatalogResourceService;

    @Resource
    private IDouyinAppConfigService douyinAppConfigService;

    @Resource
    private IMiniAppMiniDramaFavoriteService miniAppMiniDramaFavoriteService;

    @Resource
    private IMiniAppDuanjuTagRelateService tagRelateService;
    @Override
    @Transactional
    public MiniAppMiniDrama addDramaAndAlbumInfo(MiniAppMiniDramaReq req) {

        OpenApiAlbumInfo albumInfo = getOpenApiAlbumInfo(req);
        openApiAlbumInfoService.save(albumInfo);

        MiniAppMiniDrama drama = getMiniAppMiniDrama(req);
        drama.setAlbumInfoId(albumInfo.getId());

        save(drama);

        //保存标签
        saveTag(req.getDuanJuTagList(), drama.getId(), null);

        return drama;
    }

    private void saveTag(List<MiniAppDuanjuTagRelate> duanJuTagList, String dramaId, String albumId) {
        if (CollectionUtils.isNotEmpty(duanJuTagList)) {
            List<MiniAppDuanjuTagRelate> relateList = new ArrayList<>();
            for (MiniAppDuanjuTagRelate duanJuTag : duanJuTagList) {
                MiniAppDuanjuTagRelate relate = new MiniAppDuanjuTagRelate();
                relate.setDramaId(dramaId);
                relate.setAlbumId(albumId);
                relate.setTagId(duanJuTag.getTagId());
                relate.setTagName(duanJuTag.getTagName());
                relateList.add(relate);
            }
            tagRelateService.saveBatch(relateList);
        }
    }

    private String getCoverUrl(String coverId) {
        OpenApiUploadPicRecord one = picRecordService.lambdaQuery().eq(OpenApiUploadPicRecord::getOpenPicId, coverId).last("limit 1").one();
        return one.getPicUrl();
    }

    @Override
    public MiniAppMiniDrama editDramaAndAlbumInfo(MiniAppMiniDramaReq req) {
        OpenApiAlbumInfo albumInfo = getOpenApiAlbumInfo(req);
        openApiAlbumInfoService.updateByIdWithNull(albumInfo);

        MiniAppMiniDrama drama = getMiniAppMiniDrama(req);
        MiniAppMiniDrama old = getById(drama.getId());
        drama.setAlbumInfoId(albumInfo.getId());
        updateByIdWithNull(drama);
        updateCatalogResourceDataAndCache(drama, old);

        //删除标签
        tagRelateService.removeTagsByDramaId(drama.getId());

        //保存标签
        List<MiniAppDuanjuTagRelate> duanJuTagList = req.getDuanJuTagList();
        if (CollectionUtils.isNotEmpty(duanJuTagList)) {
            saveTag(duanJuTagList, drama.getId(), drama.getAlbumId());
        }
        return drama;
    }

    /**
     * 更新栏目数据跟缓存
     *
     * @param drama
     * @param old
     */
    private void updateCatalogResourceDataAndCache(MiniAppMiniDrama drama, MiniAppMiniDrama old) {
        LoginUser sysUser = HttpUtil.getCurrUser();
        //删除缓存
        appCatalogResourceService.deleteRelationCatalogResCache(CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode(), Arrays.asList(drama.getId()));
        if (Objects.equals(drama.getStatus(), StatusEnum.INVALID.getCode()) || !old.getName().equals(drama.getName())) {
            //更新栏目资源名称
            appCatalogResourceService.updateRelationResourceStatus(CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode()
                    , Arrays.asList(drama.getId()), drama.getStatus(), sysUser.getUsername(), drama.getName());
        }
    }


    private MiniAppMiniDrama getMiniAppMiniDrama(MiniAppMiniDramaReq req) {
        MiniAppMiniDrama drama = new MiniAppMiniDrama();
        drama.setName(req.getName());
        drama.setId(req.getMiniDramaId());
        drama.setColumnId(req.getColumnId());
        drama.setAlbumInfoId(req.getAlbumInfoId());
        drama.setAlbumId(req.getAlbumId());
        drama.setStatus(req.getStatus());
        drama.setOrderNo(req.getOrderNo());
        drama.setFreeNum(req.getFreeNum());
        drama.setCoverUrl(req.getCoverUrl());
        drama.setHotValue(req.getHotValue());
        drama.setRating(req.getRating());
        drama.setPerEpisodeCost(req.getPerEpisodeCost());
        long min = 20;
        long max = 50;
        Long randomNum = ThreadLocalRandom.current().nextLong(min, max);
        drama.setViewCount(randomNum);
        drama.setHotValue(req.getHotValue());

        return drama;
    }

    private OpenApiAlbumInfo getOpenApiAlbumInfo(MiniAppMiniDramaReq req) {
        OpenApiAlbumInfo albumInfo = new OpenApiAlbumInfo();
        albumInfo.setId(req.getAlbumInfoId());
        albumInfo.setName(req.getName());
        albumInfo.setTitle(req.getTitle());
        albumInfo.setSeqNum(req.getSeqNum());
        albumInfo.setSeqCount(req.getSeqsCount());
        albumInfo.setSeqsCount(req.getSeqsCount());
        albumInfo.setAlbumId(req.getAlbumId());
        albumInfo.setAlbumStatus(req.getAlbumStatus());
        albumInfo.setOnlineStatus(req.getOnlineStatus());
        albumInfo.setReviewStatus(req.getReviewStatus());
        albumInfo.setAuthorizeStatus(req.getAuthorizeStatus());
        albumInfo.setTagList(req.getTagList());
        albumInfo.setCoverList(req.getCoverList());
        albumInfo.setStatus(req.getStatus());
        albumInfo.setQualification(req.getQualification());
        albumInfo.setRecordType(req.getRecordType());
        albumInfo.setBroadcastRecordNumber(req.getBroadcastRecordNumber());
        albumInfo.setLicenseNum(req.getLicenseNum());
        albumInfo.setRegistrationNum(req.getRegistrationNum());
        albumInfo.setKeyRecordNum(req.getKeyRecordNum());
        albumInfo.setOrdinaryRecordNum(req.getOrdinaryRecordNum());
        albumInfo.setProductionOrganisation(req.getProductionOrganisation());
        albumInfo.setSummary(req.getSummary());
        albumInfo.setDuration(req.getDuration());
        albumInfo.setScreenWriter(req.getScreenWriter());
        albumInfo.setActor(req.getActor());
        albumInfo.setDirector(req.getDirector());
        albumInfo.setProducer(req.getProducer());
        albumInfo.setCostDistributionUri(req.getCostDistributionUri());
        albumInfo.setAssuranceUri(req.getAssuranceUri());
        albumInfo.setPlayletProductionCost(req.getPlayletProductionCost());
        // 移除重复的 setSeqCount 调用，已在第103行设置
        albumInfo.setYear(req.getYear());
        albumInfo.setRecommendation(req.getRecommendation());
        albumInfo.setDesp(req.getDesp());
        albumInfo.setCostUrl(req.getCostUrl());
        return albumInfo;
    }

    @Override
    public List<AlbumInfVO> getAllAlbumInfoForExport(DramaReq req) {
        // 使用一个大的页面大小来获取所有数据
        IPage<AlbumInfVO> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<AlbumInfVO> result = this.baseMapper.pageDrama(page, req);
        return result.getRecords();
    }

    @Resource
    private IMiniAppReviewRelateService iMiniAppReviewRelateService;
    /**
     * 查询栏目哪些地方用到
     *
     * @param albumInfVOS
     */
    private void findUsageInfo(List<AlbumInfVO> albumInfVOS) {
        List<String> ids = albumInfVOS.stream().map(AlbumInfVO::getId).collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }
        List<DouyinAppConfig> appConfigs = douyinAppConfigService.lambdaQuery().eq(DouyinAppConfig::getIsDeleted, 0).list();

        Map<String, String> appMap = appConfigs.stream().collect(Collectors.toMap(DouyinAppConfig::getAppId, DouyinAppConfig::getAppName, (v1, v2) -> v1));

        List<String> dramaIds = albumInfVOS.stream().map(AlbumInfVO::getMiniDramaId).collect(Collectors.toList());
        Map<String, List<MiniAppDuanjuTagRelate>> miniAppDuanjuTagRelateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dramaIds)) {
            List<MiniAppDuanjuTagRelate> tagRelateList = tagRelateService.lambdaQuery().in(MiniAppDuanjuTagRelate::getDramaId, dramaIds).list();
            if (CollectionUtils.isNotEmpty(tagRelateList)) {
                miniAppDuanjuTagRelateMap = tagRelateList.stream().collect(Collectors.groupingBy(MiniAppDuanjuTagRelate::getDramaId));
            }
        }

        List<CatalogResUsageInfoDTO> catalogResUsageInfoDTOS = appCatalogResourceService.listUsageCatalogByResId(ids, CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode());
        Map<String, List<MiniAppDuanjuTagRelate>> finalMiniAppDuanjuTagRelateMap = miniAppDuanjuTagRelateMap;
        albumInfVOS.forEach(albumInfVO -> {
            Set<String> catalogNames = catalogResUsageInfoDTOS.stream().filter(x -> Objects.equals(x.getResId(), albumInfVO.getId()))
                    .map(CatalogResUsageInfoDTO::getCatalogName).collect(Collectors.toSet());
            if (!catalogNames.isEmpty()) {
                albumInfVO.setUsageInfo("栏目：" + StringUtils.join(catalogNames, ","));
            }
            List<MiniAppDuanjuTagRelate> miniAppDuanjuTagRelate = finalMiniAppDuanjuTagRelateMap.get(albumInfVO.getMiniDramaId());
            albumInfVO.setDuanJuTagList(miniAppDuanjuTagRelate);
            List<MiniAppReviewRelate> list = iMiniAppReviewRelateService.lambdaQuery().eq(MiniAppReviewRelate::getAlbumId, albumInfVO.getAlbumId()).eq(MiniAppReviewRelate::getAuthorizeStatus, 2).list();
            List<String> authorizeAppName = new ArrayList<>();
            for (MiniAppReviewRelate relate : list) {
                if (relate.getAppId() != null) {
                    String appName = appMap.get(relate.getAppId());
                    if (StringUtils.isNotEmpty(appName)) {
                        authorizeAppName.add(appName);
                    }
                }
            }
            albumInfVO.setAuthorizeAppNameList(authorizeAppName);
        });
    }

    @Override
    public int updateByIdWithNull(MiniAppMiniDrama miniAppMiniDrama) {
        return this.baseMapper.updateByIdWithNull(miniAppMiniDrama);
    }

    @Resource
    private IMiniAppDramaColumnService miniAppDramaColumnService;

    @Resource
    private IMiniAppDramaViewingHistoryService dramaViewingHistoryService;


    @Resource
    private IAppCatalogService appCatalogService;

    @Resource
    private IMiniAppMiniDramaService miniAppMiniDramaService;


    @Resource
    RedisUtil redisUtil;

    @Override
    public List<MiniAppMiniDrama> getRecommendAlbum(String albumId, Integer currentUserId, String catLogIdd) {
        String redisKey = String.format("miniApp:duanju:recommend:userId:%s", currentUserId);
        HashSet<String> recommendAlbumSet = (HashSet<String>) redisUtil.get(redisKey);
        if (CollectionUtils.isEmpty(recommendAlbumSet)) {
            recommendAlbumSet = new HashSet<>();
        }
        List<String> recommendList = new ArrayList<>();
        List<String> result = new ArrayList<>();
        MiniAppCatalog appCatalog = appCatalogService.lambdaQuery().eq(MiniAppCatalog::getId, catLogIdd).one();
        List<MiniAppCatalog> appCatalogList = appCatalogService.lambdaQuery().eq(MiniAppCatalog::getPid, appCatalog.getPid()).notIn(MiniAppCatalog::getId, catLogIdd).list();
        for (MiniAppCatalog miniAppCatalog : appCatalogList) {
            recommendList.addAll(getRecommendAlbumIds(albumId, currentUserId, miniAppCatalog.getId()));
        }
        // 需要从recommendAlbumIds列表中每次取6个元素，如果不足6个则从头开始取。
        if (CollectionUtils.isNotEmpty(recommendList)) {
            recommendList = recommendList.stream().distinct().collect(Collectors.toList());
            int size = recommendList.size();
            for (int i = 0; i < recommendList.size(); i++) {
                String s = recommendList.get(i % size);
                if (result.size() == 5) {
                    break;
                }
                boolean add = recommendAlbumSet.add(s);
                if (add) {
                    result.add(recommendList.get(i % size));
                }
            }
            redisUtil.set(redisKey, recommendAlbumSet, 7 * 24 * 60 * 60);
            if (CollectionUtils.isNotEmpty(result)) {
                return miniAppMiniDramaService.lambdaQuery().in(MiniAppMiniDrama::getAlbumId, result).list();
            }
        }
        return new ArrayList<>();
    }


    /**
     * 获取其他栏目下的剧目且未完播的剧目
     */
    private List<String> getRecommendAlbumIds(String albumId, Integer currentUserId, String columnId) {
        List<String> result = new ArrayList<>();

        //获取已完播的剧目
        List<LookHistoryVO> finishDrama = dramaViewingHistoryService.getFinishDrama(currentUserId);
        List<String> finishAlbUmId = finishDrama.stream().map(LookHistoryVO::getAlbumId).collect(Collectors.toList());
        List<MiniAppMiniDrama> finishDramaList = new ArrayList<>();
        List<String> finishDramaIds = new ArrayList<>();
        finishDramaIds.add(albumId);
        if (CollectionUtils.isNotEmpty(finishDrama)) {
            finishDramaList = miniAppMiniDramaService.lambdaQuery().in(MiniAppMiniDrama::getAlbumId, finishAlbUmId).list();
            finishDramaIds = finishDramaList.stream().map(MiniAppMiniDrama::getId).collect(Collectors.toList());
        }


        //获取已收藏的剧目且未完播的剧目
        List<MiniAppMiniDramaFavorite> favoriteByUserId = miniAppMiniDramaFavoriteService.lambdaQuery()
                .notIn(CollectionUtils.isNotEmpty(finishAlbUmId), MiniAppMiniDramaFavorite::getAlbumId, finishAlbUmId)
                .eq(MiniAppMiniDramaFavorite::getUserId, currentUserId).list();

        List<String> favoriteDramaIds = new ArrayList<>();

        //优先推荐追剧中且未完播的剧目
        if (CollectionUtils.isNotEmpty(favoriteByUserId)) {
            List<String> tempList = favoriteByUserId.stream().map(MiniAppMiniDramaFavorite::getAlbumId).collect(Collectors.toList());
            result.addAll(tempList);
            favoriteDramaIds = getDramaIds(tempList);
        }

        //找出当前栏目下未收藏且未完播的剧目
        List<MiniAppCatalogResource> resourceList = appCatalogResourceService.lambdaQuery()
                .eq(MiniAppCatalogResource::getStatus, 1)
                .eq(MiniAppCatalogResource::getIsDeleted, 0)
                .notIn(CollectionUtils.isNotEmpty(finishDramaIds), MiniAppCatalogResource::getResId, finishDramaIds)
                .notIn(CollectionUtils.isNotEmpty(favoriteDramaIds), MiniAppCatalogResource::getResId, favoriteDramaIds)
                .eq(MiniAppCatalogResource::getPid, columnId).list();

        if (CollectionUtils.isNotEmpty(resourceList)) {
            List<String> dramIds = resourceList.stream().map(MiniAppCatalogResource::getResId).collect(Collectors.toList());
            List<MiniAppMiniDrama> tempList = miniAppMiniDramaService.lambdaQuery().in(MiniAppMiniDrama::getId, dramIds).list();
            List<String> tempIds = tempList.stream().map(MiniAppMiniDrama::getAlbumId).collect(Collectors.toList());
            result.addAll(tempIds);
        }
        return result;
    }

    private List<String> getDramaIds(List<String> alumIds) {
        if (CollectionUtils.isEmpty(alumIds)) {
            return Collections.emptyList();
        }
        List<MiniAppMiniDrama> dramaList = miniAppMiniDramaService.lambdaQuery().in(MiniAppMiniDrama::getAlbumId, alumIds).list();
        return dramaList.stream().map(MiniAppMiniDrama::getId).collect(Collectors.toList());
    }
}
