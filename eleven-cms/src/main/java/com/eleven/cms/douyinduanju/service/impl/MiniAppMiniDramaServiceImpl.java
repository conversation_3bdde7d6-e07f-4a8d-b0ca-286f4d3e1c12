package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aivrbt.dto.CatalogResUsageInfoDTO;
import com.eleven.cms.aivrbt.enums.CatalogSubResourceTypeEnum;
import com.eleven.cms.aivrbt.enums.StatusEnum;
import com.eleven.cms.aivrbt.service.IAppCatalogResourceService;
import com.eleven.cms.douyinduanju.dto.AlbumInfVO;
import com.eleven.cms.douyinduanju.dto.DramaReq;
import com.eleven.cms.douyinduanju.dto.MiniAppMiniDramaReq;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanjuTagRelate;
import com.eleven.cms.douyinduanju.entity.MiniAppMiniDrama;
import com.eleven.cms.douyinduanju.entity.OpenApiAlbumInfo;
import com.eleven.cms.douyinduanju.entity.OpenApiUploadPicRecord;
import com.eleven.cms.douyinduanju.mapper.MiniAppMiniDramaMapper;
import com.eleven.cms.douyinduanju.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.HttpUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Service
public class MiniAppMiniDramaServiceImpl extends ServiceImpl<MiniAppMiniDramaMapper, MiniAppMiniDrama> implements IMiniAppMiniDramaService {

    @Override
    public IPage<AlbumInfVO> pageAlbumInfo(DramaReq req, IPage<AlbumInfVO> iPage) {
        IPage<AlbumInfVO> albumInfVOIPage = this.baseMapper.pageDrama(iPage, req);
        findUsageInfo(albumInfVOIPage.getRecords());
        return albumInfVOIPage;
    }

    @Override
    public MiniAppMiniDrama getByAlbumId(String albumId) {
        return lambdaQuery().eq(MiniAppMiniDrama::getAlbumId, albumId).eq(MiniAppMiniDrama::getIsDeleted, 0).last("limit 1").one();
    }

    @Override
    public MiniAppMiniDrama getByAlbumInfoId(String albumInfoId) {
        return lambdaQuery().eq(MiniAppMiniDrama::getAlbumInfoId, albumInfoId).eq(MiniAppMiniDrama::getIsDeleted, 0).last("limit 1").one();
    }

    @Override
    public List<MiniAppMiniDrama> listDrama(String columnId, List<String> ids) {
        return this.baseMapper.listDrama(columnId, ids);
    }


    @Override
    public IPage<MiniAppMiniDrama> listHostDrama(String columnId, IPage<MiniAppMiniDrama> iPage, List<String> ids) {
        return this.baseMapper.listHostDrama(iPage, columnId, ids);
    }

    @Resource
    private IOpenApiAlbumInfoService openApiAlbumInfoService;
    @Resource
    private IOpenApiUploadVideoRecordService videoRecordService;

    @Resource
    private IOpenApiUploadPicRecordService picRecordService;

    @Resource
    private IMiniAppDramaEpisodeService episodeService;

    @Resource
    private IAppCatalogResourceService appCatalogResourceService;


    @Resource
    private IMiniAppDuanjuTagRelateService tagRelateService;
    @Override
    @Transactional
    public MiniAppMiniDrama addDramaAndAlbumInfo(MiniAppMiniDramaReq req) {

        OpenApiAlbumInfo albumInfo = getOpenApiAlbumInfo(req);
        openApiAlbumInfoService.save(albumInfo);

        MiniAppMiniDrama drama = getMiniAppMiniDrama(req);
        drama.setAlbumInfoId(albumInfo.getId());

        save(drama);

        //保存标签
        saveTag(req.getDuanJuTagList(), drama.getId(), null);

        return drama;
    }

    private void saveTag(List<MiniAppDuanjuTagRelate> duanJuTagList, String dramaId, String albumId) {
        if (CollectionUtils.isNotEmpty(duanJuTagList)) {
            List<MiniAppDuanjuTagRelate> relateList = new ArrayList<>();
            for (MiniAppDuanjuTagRelate duanJuTag : duanJuTagList) {
                MiniAppDuanjuTagRelate relate = new MiniAppDuanjuTagRelate();
                relate.setDramaId(dramaId);
                relate.setAlbumId(albumId);
                relate.setTagId(duanJuTag.getTagId());
                relate.setTagName(duanJuTag.getTagName());
                relateList.add(relate);
            }
            tagRelateService.saveBatch(relateList);
        }
    }

    private String getCoverUrl(String coverId) {
        OpenApiUploadPicRecord one = picRecordService.lambdaQuery().eq(OpenApiUploadPicRecord::getOpenPicId, coverId).last("limit 1").one();
        return one.getPicUrl();
    }

    @Override
    public MiniAppMiniDrama editDramaAndAlbumInfo(MiniAppMiniDramaReq req) {
        OpenApiAlbumInfo albumInfo = getOpenApiAlbumInfo(req);
        openApiAlbumInfoService.updateByIdWithNull(albumInfo);

        MiniAppMiniDrama drama = getMiniAppMiniDrama(req);
        MiniAppMiniDrama old = getById(drama.getId());
        drama.setAlbumInfoId(albumInfo.getId());
        updateByIdWithNull(drama);
        updateCatalogResourceDataAndCache(drama, old);

        //删除标签
        tagRelateService.removeTagsByDramaId(drama.getId());

        //保存标签
        List<MiniAppDuanjuTagRelate> duanJuTagList = req.getDuanJuTagList();
        if (CollectionUtils.isNotEmpty(duanJuTagList)) {
            saveTag(duanJuTagList, drama.getId(), drama.getAlbumId());
        }
        return drama;
    }

    /**
     * 更新栏目数据跟缓存
     *
     * @param drama
     * @param old
     */
    private void updateCatalogResourceDataAndCache(MiniAppMiniDrama drama, MiniAppMiniDrama old) {
        LoginUser sysUser = HttpUtil.getCurrUser();
        //删除缓存
        appCatalogResourceService.deleteRelationCatalogResCache(CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode(), Arrays.asList(drama.getId()));
        if (Objects.equals(drama.getStatus(), StatusEnum.INVALID.getCode()) || !old.getName().equals(drama.getName())) {
            //更新栏目资源名称
            appCatalogResourceService.updateRelationResourceStatus(CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode()
                    , Arrays.asList(drama.getId()), drama.getStatus(), sysUser.getUsername(), drama.getName());
        }
    }


    private MiniAppMiniDrama getMiniAppMiniDrama(MiniAppMiniDramaReq req) {
        MiniAppMiniDrama drama = new MiniAppMiniDrama();
        drama.setName(req.getName());
        drama.setId(req.getMiniDramaId());
        drama.setColumnId(req.getColumnId());
        drama.setAlbumInfoId(req.getAlbumInfoId());
        drama.setAlbumId(req.getAlbumId());
        drama.setStatus(req.getStatus());
        drama.setOrderNo(req.getOrderNo());
        drama.setFreeNum(req.getFreeNum());
        drama.setCoverUrl(req.getCoverUrl());
        drama.setHotValue(req.getHotValue());
        drama.setRating(req.getRating());
        drama.setPerEpisodeCost(req.getPerEpisodeCost());
        long min = 20;
        long max = 50;
        Long randomNum = ThreadLocalRandom.current().nextLong(min, max);
        drama.setViewCount(randomNum);
        drama.setHotValue(req.getHotValue());

        return drama;
    }

    private OpenApiAlbumInfo getOpenApiAlbumInfo(MiniAppMiniDramaReq req) {
        OpenApiAlbumInfo albumInfo = new OpenApiAlbumInfo();
        albumInfo.setId(req.getAlbumInfoId());
        albumInfo.setName(req.getName());
        albumInfo.setTitle(req.getTitle());
        albumInfo.setSeqNum(req.getSeqNum());
        albumInfo.setSeqCount(req.getSeqsCount());
        albumInfo.setSeqsCount(req.getSeqsCount());
        albumInfo.setAlbumId(req.getAlbumId());
        albumInfo.setAlbumStatus(req.getAlbumStatus());
        albumInfo.setOnlineStatus(req.getOnlineStatus());
        albumInfo.setReviewStatus(req.getReviewStatus());
        albumInfo.setAuthorizeStatus(req.getAuthorizeStatus());
        albumInfo.setTagList(req.getTagList());
        albumInfo.setCoverList(req.getCoverList());
        albumInfo.setStatus(req.getStatus());
        albumInfo.setQualification(req.getQualification());
        albumInfo.setRecordType(req.getRecordType());
        albumInfo.setBroadcastRecordNumber(req.getBroadcastRecordNumber());
        albumInfo.setLicenseNum(req.getLicenseNum());
        albumInfo.setRegistrationNum(req.getRegistrationNum());
        albumInfo.setKeyRecordNum(req.getKeyRecordNum());
        albumInfo.setOrdinaryRecordNum(req.getOrdinaryRecordNum());
        albumInfo.setProductionOrganisation(req.getProductionOrganisation());
        albumInfo.setSummary(req.getSummary());
        albumInfo.setDuration(req.getDuration());
        albumInfo.setScreenWriter(req.getScreenWriter());
        albumInfo.setActor(req.getActor());
        albumInfo.setDirector(req.getDirector());
        albumInfo.setProducer(req.getProducer());
        albumInfo.setCostDistributionUri(req.getCostDistributionUri());
        albumInfo.setAssuranceUri(req.getAssuranceUri());
        albumInfo.setPlayletProductionCost(req.getPlayletProductionCost());
        // 移除重复的 setSeqCount 调用，已在第103行设置
        albumInfo.setYear(req.getYear());
        albumInfo.setRecommendation(req.getRecommendation());
        albumInfo.setDesp(req.getDesp());
        albumInfo.setCostUrl(req.getCostUrl());
        return albumInfo;
    }

    @Override
    public List<AlbumInfVO> getAllAlbumInfoForExport(DramaReq req) {
        // 使用一个大的页面大小来获取所有数据
        IPage<AlbumInfVO> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<AlbumInfVO> result = this.baseMapper.pageDrama(page, req);
        return result.getRecords();
    }

    /**
     * 查询栏目哪些地方用到
     *
     * @param albumInfVOS
     */
    private void findUsageInfo(List<AlbumInfVO> albumInfVOS) {
        List<String> ids = albumInfVOS.stream().map(AlbumInfVO::getId).collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }

        List<String> dramaIds = albumInfVOS.stream().map(AlbumInfVO::getMiniDramaId).collect(Collectors.toList());
        Map<String, List<MiniAppDuanjuTagRelate>> miniAppDuanjuTagRelateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dramaIds)) {
            List<MiniAppDuanjuTagRelate> tagRelateList = tagRelateService.lambdaQuery().in(MiniAppDuanjuTagRelate::getDramaId, dramaIds).list();
            if (CollectionUtils.isNotEmpty(tagRelateList)) {
                miniAppDuanjuTagRelateMap = tagRelateList.stream().collect(Collectors.groupingBy(MiniAppDuanjuTagRelate::getDramaId));
            }
        }

        List<CatalogResUsageInfoDTO> catalogResUsageInfoDTOS = appCatalogResourceService.listUsageCatalogByResId(ids, CatalogSubResourceTypeEnum.SHORT_PLAY_DRAMA.getCode());
        Map<String, List<MiniAppDuanjuTagRelate>> finalMiniAppDuanjuTagRelateMap = miniAppDuanjuTagRelateMap;
        albumInfVOS.forEach(albumInfVO -> {
            Set<String> catalogNames = catalogResUsageInfoDTOS.stream().filter(x -> Objects.equals(x.getResId(), albumInfVO.getId()))
                    .map(CatalogResUsageInfoDTO::getCatalogName).collect(Collectors.toSet());
            if (!catalogNames.isEmpty()) {
                albumInfVO.setUsageInfo("栏目：" + StringUtils.join(catalogNames, ","));
            }
            List<MiniAppDuanjuTagRelate> miniAppDuanjuTagRelate = finalMiniAppDuanjuTagRelateMap.get(albumInfVO.getMiniDramaId());
            albumInfVO.setDuanJuTagList(miniAppDuanjuTagRelate);
        });
    }

    @Override
    public int updateByIdWithNull(MiniAppMiniDrama miniAppMiniDrama) {
        return this.baseMapper.updateByIdWithNull(miniAppMiniDrama);
    }
}
