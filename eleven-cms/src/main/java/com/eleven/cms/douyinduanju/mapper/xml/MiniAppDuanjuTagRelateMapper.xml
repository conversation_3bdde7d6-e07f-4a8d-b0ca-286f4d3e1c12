<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.douyinduanju.mapper.MiniAppDuanjuTagRelateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.eleven.cms.douyinduanju.entity.MiniAppDuanjuTagRelate">
        <id column="id" property="id"/>
        <result column="drama_id" property="dramaId"/>
        <result column="tag_id" property="tagId"/>
        <result column="tag_name" property="tagName"/>
        <result column="album_id" property="albumId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , drama_id, tag_id, tag_name, album_id, create_time, update_time
    </sql>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO mini_app_duanju_tag_relate (drama_id, tag_id, tag_name, album_id, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.dramaId},
            #{item.tagId},
            #{item.tagName},
            #{item.albumId},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <!-- 根据剧目ID和标签ID列表查询关联关系 -->
    <select id="selectByDramaIdAndTagIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mini_app_duanju_tag_relate
        WHERE drama_id = #{dramaId}
        <if test="tagIds != null and tagIds.size() > 0">
            AND tag_id IN
            <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>
    </select>

    <!-- 根据标签ID列表查询关联的剧目 -->
    <select id="selectByTagIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mini_app_duanju_tag_relate
        WHERE tag_id IN
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>

    <!-- 根据剧目ID列表查询关联的标签 -->
    <select id="selectByDramaIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mini_app_duanju_tag_relate
        WHERE drama_id IN
        <foreach collection="dramaIds" item="dramaId" open="(" separator="," close=")">
            #{dramaId}
        </foreach>
    </select>

</mapper>
