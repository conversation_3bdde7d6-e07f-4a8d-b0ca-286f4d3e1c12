package com.eleven.cms.douyinduanju.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.config.RefundQueryRabbitConfig;
import com.eleven.cms.douyinduanju.constant.DuanjuConstant;
import com.eleven.cms.douyinduanju.constant.PaymentConstant;
import com.eleven.cms.douyinduanju.dto.*;
import com.eleven.cms.douyinduanju.entity.*;
import com.eleven.cms.douyinduanju.enums.AppReportEventEnum;
import com.eleven.cms.douyinduanju.enums.OrderEnums;
import com.eleven.cms.douyinduanju.mapper.MiniAppDuanJuOrderMapper;
import com.eleven.cms.douyinduanju.service.*;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.service.IDouyinAppConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

/**
 * @Description: miniApp_duan_ju_order
 * @Author: jeecg-boot
 * @Date: 2025-06-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class MiniAppDuanJuOrderServiceImpl extends ServiceImpl<MiniAppDuanJuOrderMapper, MiniAppDuanJuOrder> implements IMiniAppDuanJuOrderService {


    @Resource
    IMiniAppMiniDramaService miniAppMiniDramaService;

    @Resource
    private IUserDramaCardService userDramaCardService;
    @Resource
    private IUserMembershipService userMembershipService;

    @Resource
    private IDuanJuPackageService duanJuPackageService;

    @Resource
    IOpenApiAlbumInfoService iOpenApiAlbumInfoService;


    @Resource
    IDouyinAppConfigService douYinAppConfigService;

    @Resource
    private IDuanJuUserService userService;

    @Resource
    private DouYinHttpUtil douYinHttpUtil;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    DuanJuAdReportService duanJuAdReportService;
    @Resource
    IMiniAppAdvertisementService miniAppAdvertisementService;
    @Override
    public MiniAppDuanJuOrder getByOrderNo(String orderNo) {
        return lambdaQuery().eq(MiniAppDuanJuOrder::getOrderNo, orderNo).last("limit 1").one();
    }

    @Override
    public void updateOrderPayStatus(String orderNo, String transactionId, Date payTime) {
        log.info("更新订单状态");
        List<MiniAppDuanJuOrder> orderList = lambdaQuery().eq(MiniAppDuanJuOrder::getOrderNo, orderNo).list();
        for (MiniAppDuanJuOrder appWallpaperOrder : orderList) {
            MiniAppDuanJuOrder update = new MiniAppDuanJuOrder();
            update.setId(appWallpaperOrder.getId());
            update.setPayTime(payTime);
            update.setPayStatus(OrderEnums.PayStatus.PAID.getCode());
            update.setTransactionId(transactionId);
            updateById(update);
        }
    }


    @Override
    public PaymentSignatureRes createOrder(OrderReq orderReq, Integer userId) {
        MiniAppAdvertisement advertisement = miniAppAdvertisementService.getLast(String.valueOf(userId));
        // 1. 参数验证
        validateCreateOrderRequest(orderReq);

        DuanJuUser duanJuUser = userService.getById(userId);
        if (Objects.isNull(duanJuUser)) {
            throw new BusinessException("用户不存在");
        }

        // 2. 获取套餐信息
        DuanJuPackage packageInfo = duanJuPackageService.getById(orderReq.getPackageId());
        if (packageInfo == null) {
            throw new BusinessException("套餐不存在");
        }

        // 3. 验证套餐状态
        if (packageInfo.getStatus() != 1) {
            throw new BusinessException("套餐已下架");
        }

        MiniAppDuanJuOrder order = new MiniAppDuanJuOrder();

        // 4. 特殊验证：剧卡套餐需要验证短剧信息
        if (OrderEnums.PackageType.DRAMA_CARD.getCode().equals(packageInfo.getType())) {
            if (StringUtils.isEmpty(orderReq.getAlbumId())) {
                throw new BusinessException("购买剧卡时短剧ID不能为空");
            }
            MiniAppMiniDrama drama = miniAppMiniDramaService.getByAlbumId(orderReq.getAlbumId());
            if (drama == null) {
                throw new BusinessException("短剧不存在");
            } else {
                order.setAlbumId(drama.getAlbumId());
                order.setCoverUrl(drama.getCoverUrl());
                order.setAlbumName(drama.getName());
                order.setDramaId(drama.getId());
                order.setPackageContent(drama.getName());
            }
        }
        if (OrderEnums.PackageType.MEMBERSHIP.getCode().equals(packageInfo.getType())) {
            order.setPackageContent("会员");
            DouyinAppConfig appConfig = douYinAppConfigService.getByBusinessType(orderReq.getResource());
            if (Objects.nonNull(appConfig)) {
                order.setCoverUrl(appConfig.getOrderCoverUrl());
            }
        }
        DuanJuPackage duanJuPackage = duanJuPackageService.getById(orderReq.getPackageId());
        Integer type = duanJuPackage.getType();
        order.setOrderType(type);
        order.setOrderTime(new Date());
        order.setMobile(duanJuUser.getMobile());
        order.setPayStatus(PayStatueEnum.UNPAID.getPayType());
        order.setUserId(String.valueOf(userId));
        order.setPackageId(orderReq.getPackageId());
        order.setPackageName(duanJuPackage.getName());
        order.setOrderAmount(duanJuPackage.getActualPrice());
        order.setPayType(duanJuPackage.getPayType());
        order.setPackageType(type);
        order.setBusinessType(orderReq.getResource());
        order.setChannel(Optional.ofNullable(advertisement).map(MiniAppAdvertisement::getChannel).orElse(null));
        order.setAdPromotionId(Optional.ofNullable( advertisement).map(MiniAppAdvertisement::getPromotionId).orElse( null));
        order.setAdProjectId(Optional.ofNullable( advertisement).map(MiniAppAdvertisement::getProjectId).orElse( null));
        //生成支付参数
        PaymentSignatureRes paymentSignatureRes = generatePaymentSignature(orderReq);
        order.setOrderNo(paymentSignatureRes.getOrderNo());
        save(order);
        return paymentSignatureRes;
    }


    /**
     * 生成抖音支付签名参数
     * 根据套餐类型生成对应的商品类型：剧集商品-404，整剧商品-405
     */
    public PaymentSignatureRes generatePaymentSignature(OrderReq orderReq) {
        try {
            String packageId = orderReq.getPackageId();
            String orderNo = IdWorker.get32UUID();
            String albumId = orderReq.getAlbumId();
            List<String> episodeIds = orderReq.getEpisodeIds();
            // 参数验证
            if (StringUtils.isBlank(orderReq.getPackageId())) {
                throw new RuntimeException("套餐ID不能为空");
            }


            // 获取套餐信息
            DuanJuPackage duanJuPackage = duanJuPackageService.getById(packageId);
            if (duanJuPackage == null) {
                throw new RuntimeException("套餐不存在，packageId: " + packageId);
            }

            // 获取抖音支付配置
            DouyinAppConfig payConfig = douYinAppConfigService.getByBusinessType(orderReq.getResource());
            if (payConfig == null) {
                throw new RuntimeException("抖音支付配置不存在");
            }

            // 构建支付数据
            Map<String, Object> data = buildPaymentData(duanJuPackage, orderNo, albumId, episodeIds, orderReq.getResource());


            // 生成签名
            Map<String, String> stringStringMap = generateSignature(data, payConfig);
            PaymentSignatureRes result = new PaymentSignatureRes();
            result.setData(stringStringMap.get("data"));
            result.setByteAuthorization(stringStringMap.get("byteAuthorization"));
            result.setOrderNo(orderNo);
            return result;
        } catch (Exception e) {
            log.error("生成支付签名失败", e);
            throw new RuntimeException("生成支付签名失败: " + e.getMessage());
        }
    }


    /**
     * 构建支付数据
     */
    private Map<String, Object> buildPaymentData(DuanJuPackage duanJuPackage, String outOrderNo, String albumId, List<String> episodeIds, String businessType) {
        Map<String, Object> data = new HashMap<>();
        DouyinAppConfig appConfig = douYinAppConfigService.getByBusinessType(businessType);
        int orderAmount = duanJuPackage.getActualPrice().multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).intValue();
        // 构建SKU信息
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put("skuId", duanJuPackage.getId());
        // 价格转换为分（抖音支付API要求使用分作为单位）
        skuMap.put("price", orderAmount);
        skuMap.put("quantity", 1);
        skuMap.put("title", duanJuPackage.getName());

        // 商品图片
        List<String> imageList = new ArrayList<>();
        imageList.add(duanJuPackage.getProductLink());
        skuMap.put("imageList", imageList);

        // 根据套餐类型确定商品类型和标签组
        int productType;
        String tagGroupId;
        Map<String, Object> skuAttr = new HashMap<>();

        if (duanJuPackage.getType() == PaymentConstant.PACKAGE_TYPE_DRAMA_CARD) { // 剧卡类型
            if (StringUtils.isBlank(albumId)) {
                throw new RuntimeException("剧卡类型套餐必须提供短剧ID");
            }

            OpenApiAlbumInfo apiAlbumInfo = iOpenApiAlbumInfoService.getByAlbumId(albumId);
            if (apiAlbumInfo == null) {
                throw new RuntimeException("短剧信息不存在，albumId: " + albumId);
            }

            if (episodeIds != null && !episodeIds.isEmpty()) {
                // 剧集商品
                productType = PaymentConstant.PRODUCT_TYPE_EPISODE;
                tagGroupId = PaymentConstant.TAG_GROUP_DRAMA;

                // 构建剧集商品属性
                skuAttr.put("album_name", apiAlbumInfo.getName());
                skuAttr.put("album_id", albumId);
                skuAttr.put("episode_id_list", episodeIds);
                skuAttr.put("can_expire", false);
            } else {
                // 整剧商品
                productType = PaymentConstant.PRODUCT_TYPE_ALBUM;
                tagGroupId = PaymentConstant.TAG_GROUP_DRAMA;

                // 构建整剧商品属性
                skuAttr.put("album_name", apiAlbumInfo.getName());
                skuAttr.put("album_id", albumId);
                skuAttr.put("can_expire", false);
            }
        } else if (Objects.equals(duanJuPackage.getType(), PaymentConstant.PACKAGE_TYPE_VIRTUAL_COIN)) {
            // 验证虚拟币套餐必要字段
            if (duanJuPackage.getKbRatio() == null || duanJuPackage.getKbRatio() <= 0) {
                throw new RuntimeException("虚拟币套餐的K币比例不能为空或小于等于0");
            }

            Integer complimentaryNum = duanJuPackage.getComplimentaryNum();
            BigDecimal actualPrice = duanJuPackage.getActualPrice();
            // 修复除法精度问题，指定精度和舍入模式
            BigDecimal multiply = actualPrice.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            Integer amount = multiply.multiply(new BigDecimal(duanJuPackage.getKbRatio())).intValue();
            productType = PaymentConstant.PRODUCT_TYPE_VIRTUAL_COIN;
            tagGroupId = PaymentConstant.TAG_GROUP_DRAMA;
            skuAttr.put("coin_name", PaymentConstant.COIN_NAME);  //虚拟币名称
            skuAttr.put("coin_type", PaymentConstant.COIN_TYPE);  //类型
            skuAttr.put("amount", amount);  //虚拟币数量
            skuAttr.put("can_expire", false);  //是否过期
            if (Objects.nonNull(complimentaryNum)) {
                Map<String, Object> contentPromotionCoin = new HashMap<>();
                contentPromotionCoin.put("coin_name", PaymentConstant.COIN_NAME);
                contentPromotionCoin.put("coin_type", PaymentConstant.COIN_TYPE);
                contentPromotionCoin.put("amount", complimentaryNum);  //赠送kb
                contentPromotionCoin.put("can_expire", false);
                List<Map<String, Object>> contentPromotionCoinList = new ArrayList<>();
                contentPromotionCoinList.add(contentPromotionCoin);
                skuMap.put("content_promotion_coins", contentPromotionCoinList);
            }
        } else if (Objects.equals(duanJuPackage.getType(), PaymentConstant.PACKAGE_TYPE_MEMBER)) {
            productType = PaymentConstant.PRODUCT_TYPE_MEMBER;
            tagGroupId = PaymentConstant.TAG_GROUP_DRAMA;

            skuMap.put("member_name", duanJuPackage.getName());
            skuMap.put("member_type", "VIP");

            Map<String, Object> benefitTimeMap = new HashMap<>();
            benefitTimeMap.put("num_of_day", duanJuPackage.getDurationDays());
            benefitTimeMap.put("num_of_year", 0);
            benefitTimeMap.put("num_of_month", 0);
            benefitTimeMap.put("num_of_hour", 0);
            benefitTimeMap.put("num_of_minute", 0);
            skuMap.put("benefit_time", benefitTimeMap);

            skuAttr.put("member_name", duanJuPackage.getName());
            skuAttr.put("member_type", "VIP");
            skuAttr.put("benefit_time", benefitTimeMap);

        } else {
            throw new BusinessException("不支持的套餐类型");
        }

        skuMap.put("type", productType);
        skuMap.put("tagGroupId", tagGroupId);

        // 如果有商品属性，添加到SKU中
        if (!skuAttr.isEmpty()) {
            skuMap.put("skuAttr", JSONObject.toJSONString(skuAttr));
        }

        // 构建SKU列表
        List<Map<String, Object>> skuList = new ArrayList<>();
        skuList.add(skuMap);
        data.put("skuList", skuList);

        // 订单信息
        data.put("outOrderNo", outOrderNo);
        data.put("totalAmount", orderAmount);
        data.put("payExpireSeconds", PaymentConstant.PAYMENT_EXPIRE_SECONDS);

        // 订单详情页
        Map<String, String> orderEntrySchema = new HashMap<>();
        orderEntrySchema.put("path", "pages/order/detail");
        orderEntrySchema.put("params", "{\"orderId\":\"" + outOrderNo + "\"}");
        data.put("orderEntrySchema", orderEntrySchema);

        // 支付回调地址
        data.put("payNotifyUrl", appConfig.getCallBackUrl());

        return data;
    }

    /**
     * 验证创建订单请求参数
     */
    private void validateCreateOrderRequest(OrderReq request) {
        if (StringUtils.isEmpty(request.getPackageId())) {
            throw new BusinessException("套餐ID不能为空");
        }
    }

    /**
     * 生成签名
     */
    private Map<String, String> generateSignature(Map<String, Object> data, DouyinAppConfig appConfig) throws Exception {
        // 请求时间戳
        long timestamp = System.currentTimeMillis() / 1000L;
        // 随机字符串
        String nonceStr = UUID.randomUUID().toString();

        String dataJson = JSONObject.toJSONString(data);
        String byteAuthorization = getByteAuthorization(
                appConfig.getPrivateKey(),
                dataJson,
                appConfig.getAppId(),
                nonceStr,
                timestamp,
                appConfig.getKeyVersion()
        );

        Map<String, String> result = new HashMap<>();
        result.put("data", dataJson);
        result.put("byteAuthorization", byteAuthorization);
        return result;
    }


    /**
     * 开通剧卡权益
     */
    private void activateDramaCardBenefits(MiniAppDuanJuOrder order, DuanJuPackage packageInfo) {
        try {
            if (StringUtils.isEmpty(order.getDramaId())) {
                throw new BusinessException("剧卡订单缺少短剧信息");
            }

            // 获取短剧信息
            MiniAppMiniDrama drama = miniAppMiniDramaService.getById(order.getDramaId());
            if (drama == null) {
                throw new BusinessException("短剧不存在");
            }

            boolean success = userDramaCardService.activateDramaCard(
                    order.getUserId(), order.getId(), order.getAlbumName(),
                    order.getAlbumId(), order.getDramaId(), packageInfo.getType(),
                    true, null);

            if (success) {
                log.info("剧卡权益开通成功，订单号: {}, 用户ID: {}, 短剧ID: {}",
                        order.getOrderNo(), order.getUserId(), order.getDramaId());
            } else {
                log.error("剧卡权益开通失败，订单号: {}", order.getOrderNo());
                throw new BusinessException("剧卡权益开通失败");
            }

        } catch (Exception e) {
            log.error("开通剧卡权益异常，订单号: {}", order.getOrderNo(), e);
            throw e;
        }
    }

    @Resource
    private IUserKCoinAccountService userKCoinAccountService;

    /**
     * 开通K币权益
     */
    private void activateKCoinBenefits(MiniAppDuanJuOrder order, DuanJuPackage packageInfo) {
        try {
            // 充值K币数量
            BigDecimal rechargeAmount = BigDecimal.valueOf(packageInfo.getRechargeAmount() != null ?
                    packageInfo.getRechargeAmount() : 0);

            // 赠送K币数量
            BigDecimal giftAmount = BigDecimal.valueOf(packageInfo.getComplimentaryNum() != null ?
                    packageInfo.getComplimentaryNum() : 0);

            // 充值K币
            if (rechargeAmount.compareTo(BigDecimal.ZERO) > 0) {
                boolean rechargeSuccess = userKCoinAccountService.rechargeKCoin(
                        order.getUserId(), rechargeAmount, order.getId(),
                        "购买K币套餐充值: " + packageInfo.getName());

                if (!rechargeSuccess) {
                    throw new BusinessException("K币充值失败");
                }
            }

            // 赠送K币
            if (giftAmount.compareTo(BigDecimal.ZERO) > 0) {
                boolean giftSuccess = userKCoinAccountService.giftKCoin(
                        order.getUserId(), giftAmount,
                        "购买K币套餐赠送: " + packageInfo.getName());

                if (!giftSuccess) {
                    log.warn("K币赠送失败，订单号: {}, 赠送金额: {}", order.getOrderNo(), giftAmount);
                }
            }

            log.info("K币权益开通成功，订单号: {}, 用户ID: {}, 充值: {}, 赠送: {}",
                    order.getOrderNo(), order.getUserId(), rechargeAmount, giftAmount);

        } catch (Exception e) {
            log.error("开通K币权益异常，订单号: {}", order.getOrderNo(), e);
            throw e;
        }
    }

    /**
     * 生成byteAuthorization
     */
    private String getByteAuthorization(String privateKeyStr, String data, String appId, String nonceStr, long timestamp, String keyVersion) {
        try {
            // 生成签名
            String signature = getSignature(privateKeyStr, "POST", "/requestOrder", timestamp, nonceStr, data);

            // 构造byteAuthorization
            StringBuilder sb = new StringBuilder();
            sb.append("SHA256-RSA2048 ")
                    .append("appid=").append(appId).append(",")
                    .append("nonce_str=").append(nonceStr).append(",")
                    .append("timestamp=").append(timestamp).append(",")
                    .append("key_version=").append(keyVersion).append(",")
                    .append("signature=").append(signature);

            return sb.toString();
        } catch (Exception ex) {
            log.error("生成byteAuthorization失败", ex);
            throw new RuntimeException("生成byteAuthorization失败");
        }
    }

    /**
     * 生成签名
     */
    private String getSignature(String privateKeyStr, String method, String uri, long timestamp, String nonce, String data) throws Exception {
        String rawStr = method + "\n" +
                uri + "\n" +
                timestamp + "\n" +
                nonce + "\n" +
                data + "\n";

        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initSign(string2PrivateKey(privateKeyStr));
        sign.update(rawStr.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(sign.sign());
    }

    /**
     * 字符串转私钥
     */
    private PrivateKey string2PrivateKey(String privateKeyStr) {
        try {
            // 清理私钥字符串，移除PEM格式的头部、尾部和换行符
            String cleanedPrivateKey = cleanPrivateKeyString(privateKeyStr);

            byte[] privateBytes = Base64.getDecoder().decode(cleanedPrivateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception ex) {
            log.error("私钥转换失败，原始私钥长度: {}, 错误信息: {}",
                    privateKeyStr != null ? privateKeyStr.length() : 0, ex.getMessage());
            throw new RuntimeException("私钥转换失败: " + ex.getMessage());
        }
    }

    /**
     * 清理私钥字符串，移除PEM格式的头部、尾部和所有空白字符
     */
    private String cleanPrivateKeyString(String privateKeyStr) {
        if (privateKeyStr == null || privateKeyStr.trim().isEmpty()) {
            throw new IllegalArgumentException("私钥字符串不能为空");
        }

        return privateKeyStr
                // 移除PEM格式的头部
                .replaceAll("-----BEGIN.*?-----", "")
                // 移除PEM格式的尾部
                .replaceAll("-----END.*?-----", "")
                // 移除所有换行符、空格、制表符等空白字符
                .replaceAll("\\s+", "")
                // 移除可能存在的其他特殊字符
                .replaceAll("[\\r\\n\\t]", "");
    }

    @Override
    @Transactional
    public boolean handlePaymentCallback(PaymentCallbackMsg callbackMsg) {
        try {
            log.info("处理支付回调，订单号: {}, 状态: {}", callbackMsg.getOutOrderNo(), callbackMsg.getStatus());

            // 参数验证
            if (StringUtils.isBlank(callbackMsg.getOutOrderNo())) {
                log.error("支付回调订单号为空");
                return false;
            }

            // 查询订单
            MiniAppDuanJuOrder order = getByOrderNo(callbackMsg.getOutOrderNo());
            if (order == null) {
                log.error("支付回调订单不存在，订单号: {}", callbackMsg.getOutOrderNo());
                return false;
            }

            // 检查订单状态，避免重复处理
            if (order.getPayStatus() != null && order.getPayStatus() == 1) {
                log.info("订单已支付，跳过处理，订单号: {}", callbackMsg.getOutOrderNo());
                return true;
            }

            // 根据回调状态处理订单
            if ("SUCCESS".equals(callbackMsg.getStatus())) {
                // 支付成功
                handlePaymentSuccess(order, callbackMsg);
                log.info("支付成功处理完成，订单号: {}", callbackMsg.getOutOrderNo());
            } else if ("CANCEL".equals(callbackMsg.getStatus())) {
                // 支付取消
                handlePaymentCancel(order, callbackMsg);
                log.info("支付取消处理完成，订单号: {}", callbackMsg.getOutOrderNo());
            } else {
                log.error("未知的支付状态: {}, 订单号: {}", callbackMsg.getStatus(), callbackMsg.getOutOrderNo());
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("处理支付回调失败，订单号: " + callbackMsg.getOutOrderNo(), e);
            return false;
        }
    }

    /**
     * 处理支付成功
     */

    public void handlePaymentSuccess(MiniAppDuanJuOrder order, PaymentCallbackMsg callbackMsg) {
        // 更新订单状态
        order.setPayStatus(OrderEnums.PayStatus.PAID.getCode()); // 已支付
        order.setTransactionId(callbackMsg.getChannelPayId());
        order.setTransactionId(callbackMsg.getOrderId());
        order.setPayChannel(callbackMsg.getPayChannel());
        try {
            order.setPayTime(new Date(callbackMsg.getEventTime()));
            order.setUpdateTime(new Date());
        } catch (Exception e) {
            log.info("支付回调时间转换失败，使用当前时间", e);
        }

        // 更新订单
        updateById(order);

        //发放权益卡
        activateOrderBenefits(order);

        //更新用户会员信息
        userMembershipService.updateUserMemberInfo(order.getUserId());

        try {
            //广告上报
            duanJuAdReportService.sendAppAdReportMessage(order, AppReportEventEnum.ACTIVE_PAY);
        } catch (Exception e) {
            log.info("短剧广告回调通知异常", e);
        }

        //更新用户付费字段
        DuanJuUser updateUser = new DuanJuUser();
        updateUser.setId(Integer.valueOf(order.getUserId()));
        updateUser.setPayFlat(1);
        userService.updateById(updateUser);

        log.info("订单支付成功，订单号: {}, 支付金额: {} 分",
                callbackMsg.getOutOrderNo(), callbackMsg.getTotalAmount());
    }


    @Override
    public void handlePhoneSubService(Subscribe subscribe) {

        log.info("处理用户手机号订阅:{}", subscribe);
        if (!Objects.equals(subscribe.getStatus(), 1)) {
            log.info("用户手机号订阅状态异常:{}", subscribe);
        }
        HashMap<String, Object> extMap = subscribe.getExtMap();
        String packageId = (String) extMap.get("packageId");
        String resource = (String) extMap.get("resource");
        DuanJuPackage packageInfo = duanJuPackageService.getById(packageId);


        //创建手机号订阅订单
        MiniAppDuanJuOrder phoneSubOrder = createPhoneSubOrder(packageInfo, subscribe.getId(), subscribe.getOpenId(), resource, PayStatueEnum.PAID);


        //发放权益卡
        activateOrderBenefits(phoneSubOrder);
        log.info("用户手机号订阅成功,订单号:{}", phoneSubOrder.getOrderNo());

        //更新用户标识
        userMembershipService.updateUserMemberInfo(phoneSubOrder.getUserId());

        //话费广告上报
        duanJuAdReportService.sendAppAdReportMessage(phoneSubOrder, AppReportEventEnum.ACTIVE_PHONE_BILL_PAY);
    }

    @Override
    public void handleExternalPhoneSubService(ExternalMonthlySubCallbackDTO dto) {
        MiniAppDuanJuOrder order = getOne(new LambdaQueryWrapper<MiniAppDuanJuOrder>().eq(MiniAppDuanJuOrder::getOrderNo, dto.getOrderNo()), false);
        if (order == null) {
            log.warn("订单不存在:{}", dto.getOrderNo());
            return;
        }
        if (order.getPayStatus() != null && (order.getPayStatus() == 1 || order.getPayStatus() == -1)) {
            log.warn("订单已处理:{}", dto.getOrderNo());
            return;
        }

        //开通失败,更新订单状态
        if (StringUtils.equals(dto.getStatus(), "0")) {
            order.setPayStatus(OrderEnums.PayStatus.FAILED.getCode());
            updateById(order);
            log.info("用户开通话费失败,订单支付失败,用户id:{}:{}", order.getUserId(), dto.getOrderNo());
            return;
        }
        //发放权益
        activateOrderBenefits(order);

        //更新会员信息
        userMembershipService.updateUserMemberInfo(order.getUserId());

        log.info("用户手机号订阅成功,订单号:{}", dto.getOrderNo());
        //话费广告上报
        duanJuAdReportService.sendAppAdReportMessage(order, AppReportEventEnum.ACTIVE_PHONE_BILL_PAY);

        //临时会员免校验
        DuanJuUser duanJuUser = userService.getById(order.getUserId());
        redisUtil.setIfAbsent(DuanjuConstant.SUBSCRIBE_MEMBERSHIP_STATUS_PREFIX + duanJuUser.getOpenId(),
                duanJuUser.getMobile(), 60 * 30);
    }

    @Override
    public Boolean refreshSubscribeStatus(RefreshStatusDTO dto) {
        HashMap<String, Object> extMap = dto.getExtMap();
        String packageId = (String) extMap.get("packageId");
        String resource = (String) extMap.get("resource");
        DuanJuPackage packageInfo = duanJuPackageService.getById(packageId);

        DuanJuUser duanJuUser = userService.lambdaQuery().eq(DuanJuUser::getOpenId, dto.getUid()).last("limit 1").one();
        if (duanJuUser == null) {
            log.info("用户不存在");
            return false;
        }
        if (duanJuUser.getPhoneSubExpireStatus() == 0) {
            log.info("用户手机号已订阅");
            return true;
        }

        boolean subscribed = redisUtil.setIfAbsent(DuanjuConstant.SUBSCRIBE_MEMBERSHIP_STATUS_PREFIX + duanJuUser.getOpenId()
                , dto.getMobile(), 60 * 30);
        if (!subscribed) {
            log.info("开通页查询用户:{}的已经薅过羊毛了：{}", duanJuUser.getMobile(), duanJuUser.getOpenId());
            return false;
        } else {
            //体验会员提前3秒过期，防止延时队列执行任务时候截止时间没到
//                appUserMembershipService.handleUserTempSubscribeMembership(appUser, channelCode.getMembershipType(), DateUtil.offset(DateUtil.date(), DateField.SECOND, channelCode.getDelayCheckIntervalSeconds() - 3));
            createPhoneSubOrder(packageInfo, dto.getOrderNo(), dto.getUid(), resource, PayStatueEnum.UNPAID);
            //TODO 发放 两分钟免校验的会员
            return true;
        }
    }

    public MiniAppDuanJuOrder createPhoneSubOrder(DuanJuPackage packageInfo, String orderNo, String OpenId, String businessType, PayStatueEnum payStatus) {
        DuanJuUser duanJuUser = userService.lambdaQuery().eq(DuanJuUser::getOpenId, OpenId).last("limit 1").one();
        MiniAppAdvertisement advertisement = miniAppAdvertisementService.getLast(String.valueOf(duanJuUser.getId()));
        if (Objects.isNull(duanJuUser)) {
            log.info("用户不存在,openId:{}", OpenId);
        }
        String UserId = String.valueOf(duanJuUser.getId());
        MiniAppDuanJuOrder order = new MiniAppDuanJuOrder();
        order.setOrderNo(orderNo);
        order.setUserId(UserId);
        order.setPackageId(packageInfo.getId());
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setPackageContent("会员");
        order.setOrderType(1);
        order.setOrderTime(new Date());
        order.setMobile(duanJuUser.getMobile());
        order.setPayStatus(payStatus.getPayType());
        order.setPackageName(packageInfo.getName());
        order.setOrderAmount(packageInfo.getActualPrice());
        order.setPayType(packageInfo.getPayType());
        order.setBusinessType(businessType);
        order.setPayChannel(80);
        order.setAdPromotionId(Optional.ofNullable( advertisement).map(MiniAppAdvertisement::getPromotionId).orElse( null));
        order.setAdProjectId(Optional.ofNullable( advertisement).map(MiniAppAdvertisement::getProjectId).orElse( null));
        order.setChannel(Optional.ofNullable(advertisement).map(MiniAppAdvertisement::getChannel).orElse(null));
        save(order);
        return order;
    }

    private void activateOrderBenefits(MiniAppDuanJuOrder order) {
        try {
            DuanJuPackage packageInfo = duanJuPackageService.getById(order.getPackageId());
            if (packageInfo == null) {
                log.error("套餐不存在，无法开通权益，订单号: {}, 套餐ID: {}", order.getOrderNo(), order.getPackageId());
                return;
            }

            OrderEnums.PackageType packageType = OrderEnums.PackageType.getByCode(packageInfo.getType());

            switch (packageType) {
                case MEMBERSHIP:
                    userMembershipService.activateMembership(order, packageInfo);
                    break;
                case DRAMA_CARD:
                    activateDramaCardBenefits(order, packageInfo);
                    break;
                case K_COIN:
                    activateKCoinBenefits(order, packageInfo);
                    break;
                default:
                    log.error("未知的套餐类型，订单号: {}, 套餐类型: {}", order.getOrderNo(), packageInfo.getType());
            }

        } catch (Exception e) {
            log.error("开通订单权益异常，订单号: {}", order.getOrderNo(), e);
            throw new BusinessException("开通权益失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付取消
     */
    private void handlePaymentCancel(MiniAppDuanJuOrder order, PaymentCallbackMsg callbackMsg) {
        // 更新订单状态
        order.setPayStatus(OrderEnums.PayStatus.CANCEL.getCode()); // 支付取消
        order.setUpdateTime(new Date());
        order.setPayChannel(callbackMsg.getPayChannel());

        // 更新订单
        updateById(order);

        log.info("订单支付取消，订单号: {}, 取消原因: {}",
                callbackMsg.getOutOrderNo(), callbackMsg.getMessage());
    }

    @Override
    public Map<String, String> generatePaymentSignature(String packageId, String userId, String albumId, List<String> episodeIds) {
        return Collections.emptyMap();
    }


    /**
     * 创建退款订单
     *
     * @param orderNo      原订单号
     * @param refundAmount 退款金额（分）
     * @param refundReason 退款原因
     * @param businessType 业务类型
     * @return 退款结果
     */
    @Override
    @Transactional
    public RefundResponse createRefund(String orderNo, Integer refundAmount, Map<String, Object> refundReason, String businessType) {
        try {
            log.info("开始处理退款请求，订单号: {}, 退款金额: {} 分, 退款原因: {}", orderNo, refundAmount, refundReason);

            // 1. 参数验证
            if (StringUtils.isBlank(orderNo)) {
                return RefundResponse.error("订单号不能为空");
            }
            if (refundAmount == null || refundAmount <= 0) {
                return RefundResponse.error("退款金额必须大于0");
            }
            if (Objects.isNull(refundReason)) {
                refundReason = new HashMap<>();
                refundReason.put("code", 101);
                refundReason.put("text", "用户申请退款");
            }

            // 2. 查询原订单
            MiniAppDuanJuOrder originalOrder = getByOrderNo(orderNo);
            if (originalOrder == null) {
                return RefundResponse.error("原订单不存在");
            }

            // 3. 验证订单状态
            if (originalOrder.getPayStatus() != 1) {
                return RefundResponse.error("订单未支付，无法退款");
            }

            // 4. 验证退款金额
            BigDecimal orderAmount = originalOrder.getOrderAmount();
            BigDecimal refundAmountDecimal = new BigDecimal(refundAmount).divide(new BigDecimal(100));
            if (refundAmountDecimal.compareTo(orderAmount) > 0) {
                return RefundResponse.error("退款金额不能超过订单金额");
            }

            // 5. 检查是否已经退款
            if (originalOrder.getRefundStatus() != null && originalOrder.getRefundStatus() == 1) {
                return RefundResponse.error("订单已退款，不能重复退款");
            }

            // 6. 生成退款订单号
            String outRefundNo = IdWorker.get32UUID();

            // 7. 调用抖音退款API
            RefundApiResponse apiResponse = callDouYinRefundApi(originalOrder, outRefundNo, refundAmount, refundReason, originalOrder.getBusinessType());

            if (!apiResponse.isSuccess()) {
                log.error("调用抖音退款API失败，订单号: {}, 错误信息: {}", orderNo, apiResponse.getErrorMessage());
                return RefundResponse.error("退款申请失败: " + apiResponse.getErrorMessage());
            }

            // 8. 更新订单退款状态
            updateOrderRefundStatus(originalOrder, outRefundNo, refundAmount, refundReason, apiResponse.getRefundId());


            log.info("退款申请成功，订单号: {}, 退款单号: {}, 抖音退款ID: {}",
                    orderNo, outRefundNo, apiResponse.getRefundId());

            //发送mq延迟查询状态
            sendRefundQueryMQ(orderNo);

            return RefundResponse.success("退款申请成功", outRefundNo, apiResponse.getRefundId());

        } catch (Exception e) {
            log.error("处理退款异常，订单号: {}", orderNo, e);
            return RefundResponse.error("退款处理异常: " + e.getMessage());
        }
    }

    /**
     * 调用抖音退款API
     */
    private RefundApiResponse callDouYinRefundApi(MiniAppDuanJuOrder order, String outRefundNo,
                                                  Integer refundAmount, Map<String, Object> refundReason, String businessType) {
        try {
            // 获取抖音配置
            DouyinAppConfig appConfig = douYinAppConfigService.getByBusinessType(businessType);
            String token = douYinHttpUtil.getToken(businessType);
            if (appConfig == null) {
                return RefundApiResponse.error("抖音配置不存在");
            }

            // 构建退款请求参数
            Map<String, Object> refundData = buildRefundRequestData(order, outRefundNo, refundAmount, refundReason, appConfig);

            // 生成签名
            String signature = generateRefundSignature(refundData, appConfig);

            // 调用退款API
            String refundUrl = "https://open.douyin.com/api/trade_basic/v1/developer/refund_create/";
            String response = doRefundPost(refundData, refundUrl, signature, token);
            // 解析响应
            return parseRefundResponse(response);

        } catch (Exception e) {
            log.error("调用抖音退款API异常", e);
            return RefundApiResponse.error("调用退款API异常: " + e.getMessage());
        }
    }

    public String getIemOrderId(String orderNo,String businessType) {
        OpenApiRes openApiRes = douYinHttpUtil.queryOrderByOrderNo(orderNo,businessType);
        if (openApiRes.isSuccess()) {
            return openApiRes.getOrderInfo().getItemOrderList().get(0).getItemOrderId();

        }
        return "";
    }

    /**
     * 构建退款请求数据
     */
    private Map<String, Object> buildRefundRequestData(MiniAppDuanJuOrder order, String outRefundNo,
                                                       Integer refundAmount, Map<String, Object> refundReason, DouyinAppConfig appConfig) {
        Map<String, Object> data = new HashMap<>();
        // 基础参数
        data.put("app_id", appConfig.getAppId());
        Map<String, String> map = new HashMap<>();
        map.put("path", "pages/refund/index");
        map.put("params", "{\"orderId\":\"" + order.getOrderNo() + "\"}");

        List<Map<String, Object>> refundReasonList = new ArrayList<>();
        refundReasonList.add(refundReason);
        data.put("refund_reason", refundReasonList);

        data.put("order_entry_schema", map);
        data.put("order_id", order.getTransactionId());

        data.put("out_refund_no", order.getOrderNo());
        data.put("refund_total_amount", refundAmount);
        data.put("disable_msg", false); // 是否禁用退款消息推送
        data.put("msg_page", "pages/refund/index"); // 退款消息页面路径
        data.put("notify_url", appConfig.getRefundCallBackUrl()); // 退款回调

        String iemOrderId = getIemOrderId(order.getOrderNo(),appConfig.getBusinessType());
        //商品信息
        Map<String, Object> detail = new HashMap<>();
        detail.put("item_order_id", iemOrderId);  //抖音侧用户id
        detail.put("refund_amount", refundAmount);    //退款金额
        List<Map<String, Object>> detailList = new ArrayList<>();
        detailList.add(detail);
        data.put("item_order_detail", detailList);
        return data;
    }

    /**
     * 生成退款签名
     */
    private String generateRefundSignature(Map<String, Object> data, DouyinAppConfig appConfig) {
        try {
            // 按照抖音文档要求生成签名
            String dataJson = JSONObject.toJSONString(data);
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String nonce = IdWorker.get32UUID().substring(0, 16);

            // 构建签名字符串
            String signString = String.format("POST\n/api/apps/trade/v2/refund/create_refund\n%s\n%s\n%s\n%s",
                    timestamp, nonce, dataJson, appConfig.getAppSecret());

            // 使用HMAC-SHA256生成签名
            return generateHmacSha256(signString, appConfig.getAppSecret());

        } catch (Exception e) {
            log.error("生成退款签名失败", e);
            throw new RuntimeException("生成退款签名失败");
        }
    }

    /**
     * 生成HMAC-SHA256签名
     */
    private String generateHmacSha256(String data, String key) throws Exception {
        javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256");
        javax.crypto.spec.SecretKeySpec secretKeySpec = new javax.crypto.spec.SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hash);
    }

    /**
     * 发送退款请求
     */
    private String doRefundPost(Map<String, Object> data, String url, String signature, String token) throws Exception {
        // 使用HttpClient发送POST请求
        org.apache.http.client.HttpClient httpClient = org.apache.http.impl.client.HttpClients.createDefault();
        org.apache.http.client.methods.HttpPost post = new org.apache.http.client.methods.HttpPost(url);

        // 设置请求头
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Authorization", signature);
        post.setHeader("User-Agent", "DouYin-API-Client/1.0");
        post.setHeader("access-token", token);

        // 设置请求体
        String jsonData = JSONObject.toJSONString(data);
        post.setEntity(new org.apache.http.entity.StringEntity(jsonData, StandardCharsets.UTF_8));

        // 执行请求
        org.apache.http.HttpResponse response = httpClient.execute(post);
        String result = org.apache.http.util.EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

        log.info("抖音退款API请求参数: {}", jsonData);
        log.info("抖音退款API响应结果: {}", result);

        return result;
    }

    /**
     * 解析退款响应
     */
    private RefundApiResponse parseRefundResponse(String response) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(response);

            // 检查响应状态
            Integer errNo = jsonResponse.getInteger("err_no");
            String errMsg = jsonResponse.getString("err_msg");

            if (errNo != null && errNo == 0) {
                // 成功
                JSONObject data = jsonResponse.getJSONObject("data");
                String refundId = data != null ? data.getString("refund_id") : null;
                return RefundApiResponse.success(refundId);
            } else {
                // 失败
                return RefundApiResponse.error(errMsg != null ? errMsg : "退款失败");
            }

        } catch (Exception e) {
            log.error("解析退款响应失败", e);
            return RefundApiResponse.error("解析退款响应失败");
        }
    }

    /**
     * 更新订单退款状态
     */
    private void updateOrderRefundStatus(MiniAppDuanJuOrder order, String outRefundNo,
                                         Integer refundAmount, Map<String, Object> refundReason, String refundId) {
        MiniAppDuanJuOrder updateOrder = new MiniAppDuanJuOrder();
        updateOrder.setId(order.getId());
        updateOrder.setRefundStatus(1); // 退款中
        updateOrder.setPayStatus(OrderEnums.PayStatus.REFUNDING.getCode()); // 退款中
        updateOrder.setRefundAmount(new BigDecimal(refundAmount).divide(new BigDecimal(100)));
        updateOrder.setRefundReason(refundReason.get("text").toString());
        updateOrder.setRefundTime(new Date());
        updateOrder.setOutRefundNo(outRefundNo);
        updateOrder.setRefundId(refundId);
        updateOrder.setUpdateTime(new Date());

        updateById(updateOrder);
        log.info("订单退款状态更新成功，订单ID: {}, 退款单号: {}", order.getId(), outRefundNo);
    }

    /**
     * 处理权益回收
     */
    @Override
    public void handleBenefitRevocation(MiniAppDuanJuOrder order) {
        try {
            DuanJuPackage packageInfo = duanJuPackageService.getById(order.getPackageId());
            if (packageInfo == null) {
                log.warn("套餐不存在，跳过权益回收，订单号: {}", order.getOrderNo());
                return;
            }

            OrderEnums.PackageType packageType = OrderEnums.PackageType.getByCode(packageInfo.getType());

            switch (packageType) {
                case MEMBERSHIP:
                    revokeMembershipBenefits(order);
                    break;
                case DRAMA_CARD:
                    revokeDramaCardBenefits(order);
                    break;
                case K_COIN:
                    revokeKCoinBenefits(order);
                    break;
                default:
                    log.warn("未知的套餐类型，跳过权益回收，订单号: {}, 套餐类型: {}",
                            order.getOrderNo(), packageInfo.getType());
            }

        } catch (Exception e) {
            log.error("权益回收异常，订单号: {}", order.getOrderNo(), e);
            // 权益回收失败不影响退款流程，只记录日志
        }
    }

    /**
     * 回收会员权益
     */
    private void revokeMembershipBenefits(MiniAppDuanJuOrder order) {
        try {
            // 查找用户会员记录
            UserMembership membership = userMembershipService.lambdaQuery()
                    .eq(UserMembership::getUserId, order.getUserId())
                    .eq(UserMembership::getOrderId, order.getOrderNo())
                    .one();

            if (membership != null) {
                // 设置会员状态为已退款
                UserMembership updateMembership = new UserMembership();
                updateMembership.setId(membership.getId());
                updateMembership.setStatus(3); // 3-已退款
                updateMembership.setUpdateTime(new Date());
                userMembershipService.updateById(updateMembership);

                log.info("会员权益回收成功，订单号: {}, 会员ID: {}", order.getOrderNo(), membership.getId());
            }
        } catch (Exception e) {
            log.error("回收会员权益失败，订单号: {}", order.getOrderNo(), e);
        }
    }

    /**
     * 回收剧卡权益
     */
    private void revokeDramaCardBenefits(MiniAppDuanJuOrder order) {
        try {
            // 查找用户剧卡记录
            UserDramaCard dramaCard = userDramaCardService.lambdaQuery()
                    .eq(UserDramaCard::getUserId, order.getUserId())
                    .eq(UserDramaCard::getOrderId, order.getId())
                    .eq(UserDramaCard::getStatus, 1)
                    .one();

            if (dramaCard != null) {
                // 设置剧卡状态为已退款
                UserDramaCard updateDramaCard = new UserDramaCard();
                updateDramaCard.setId(dramaCard.getId());
                updateDramaCard.setStatus(3); // 3-已退款
                updateDramaCard.setUpdateTime(new Date());
                userDramaCardService.updateById(updateDramaCard);

                log.info("剧卡权益回收成功，订单号: {}, 剧卡ID: {}", order.getOrderNo(), dramaCard.getId());
            }
        } catch (Exception e) {
            log.error("回收剧卡权益失败，订单号: {}", order.getOrderNo(), e);
        }
    }

    /**
     * 回收K币权益
     */
    private void revokeKCoinBenefits(MiniAppDuanJuOrder order) {
        try {
            // K币权益回收需要扣减用户K币余额
            // 这里需要根据实际的K币服务实现
            log.info("K币权益回收，订单号: {}", order.getOrderNo());
            // TODO: 实现K币扣减逻辑
        } catch (Exception e) {
            log.error("回收K币权益失败，订单号: {}", order.getOrderNo(), e);
        }
    }

    /**
     * 处理退款回调
     *
     * @param refundNotifyMsg 退款回调消息
     * @return 处理结果
     */
    @Override
    @Transactional
    public boolean handleRefundCallback(RefundNotifyMsg refundNotifyMsg) {
        try {
            log.info("处理退款回调，退款单号: {}, 状态: {}", refundNotifyMsg.getOutRefundNo(), refundNotifyMsg.getStatus());

            // 参数验证
            if (StringUtils.isBlank(refundNotifyMsg.getOutRefundNo())) {
                log.error("退款回调退款单号为空");
                return false;
            }

            // 查询订单
            MiniAppDuanJuOrder order = lambdaQuery()
                    .eq(MiniAppDuanJuOrder::getOrderNo, refundNotifyMsg.getOrderId())
                    .one();

            if (order == null) {
                log.error("退款回调订单不存在，退款单号: {}", refundNotifyMsg.getOutRefundNo());
                return false;
            }

            // 检查订单状态，避免重复处理
            if (order.getRefundStatus() != null && order.getRefundStatus() == 2) {
                log.info("订单已退款成功，跳过处理，退款单号: {}", refundNotifyMsg.getOutRefundNo());
                return true;
            }

            // 根据回调状态处理
            if ("SUCCESS".equals(refundNotifyMsg.getStatus())) {
                handleRefundSuccess(order, refundNotifyMsg);
            } else if ("FAIL".equals(refundNotifyMsg.getStatus())) {
                handleRefundFail(order, refundNotifyMsg);
            } else {
                log.warn("未知的退款状态: {}, 退款单号: {}", refundNotifyMsg.getStatus(), refundNotifyMsg.getOutRefundNo());
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("处理退款回调异常，退款单号: {}", refundNotifyMsg.getOutRefundNo(), e);
            return false;
        }
    }

    /**
     * 处理退款成功
     */
    private void handleRefundSuccess(MiniAppDuanJuOrder order, RefundNotifyMsg refundNotifyMsg) {
        // 更新订单退款状态
        MiniAppDuanJuOrder updateOrder = new MiniAppDuanJuOrder();
        updateOrder.setId(order.getId());
        updateOrder.setRefundStatus(2); // 退款成功
        updateOrder.setPayStatus(OrderEnums.PayStatus.REFUNDED.getCode());
        updateOrder.setRefundCompleteTime(new Date(refundNotifyMsg.getEventTime()));
        updateOrder.setUpdateTime(new Date());

        updateById(updateOrder);

        // 回收会员权益
        handleBenefitRevocation(order);
//        revokeMembershipBenefits(order);

        //激活下一个会员权益
        userMembershipService.activateNextInactiveMembership(order.getUserId());

        //更新用户会员信息
        userMembershipService.updateUserMemberInfo(order.getUserId());

        log.info("退款成功处理完成，订单号: {}, 退款单号: {}, 退款金额: {} 分",
                order.getOrderNo(), refundNotifyMsg.getOutRefundNo(), refundNotifyMsg.getRefundTotalAmount());
    }

    /**
     * 处理退款失败
     */
    private void handleRefundFail(MiniAppDuanJuOrder order, RefundNotifyMsg refundNotifyMsg) {
        // 更新订单退款状态
        MiniAppDuanJuOrder updateOrder = new MiniAppDuanJuOrder();
        updateOrder.setId(order.getId());
        updateOrder.setRefundStatus(3); // 退款失败
        updateOrder.setRefundFailReason(refundNotifyMsg.getMessage());
        updateOrder.setUpdateTime(new Date());

        updateById(updateOrder);

        log.info("退款失败处理完成，订单号: {}, 退款单号: {}, 失败原因: {}",
                order.getOrderNo(), refundNotifyMsg.getOutRefundNo(), refundNotifyMsg.getMessage());
    }

    /**
     * 发送退款查询MQ消息
     *
     * @param orderNo 订单号
     */

    @Resource
    @Lazy
    RabbitTemplate rabbitTemplate;

    private void sendRefundQueryMQ(String orderNo) {
        try {
            RefundQueryMessage message = new RefundQueryMessage();
            message.setOrderNo(orderNo);

            // 使用配置类中定义的常量
            rabbitTemplate.convertAndSend(
                    RefundQueryRabbitConfig.REFUND_QUERY_QUEUE,
                    message,
                    msg -> {
                        msg.getMessageProperties().setDelay(3000); // 延迟3秒
                        return msg;
                    });

            log.info("发送退款查询MQ消息成功，订单号: {}",
                    orderNo);

        } catch (Exception e) {
            log.error("发送退款查询MQ消息失败，订单号: {}", orderNo, e);
        }
    }

    /**
     * 查询退款状态
     * 根据抖音开放平台退款查询API实现
     *
     * @param orderNo 订单号
     * @return 查询结果
     */
    public RefundQueryResponse queryRefundStatus(String orderNo) {
        try {
            log.info("开始查询退款状态，订单号: {}, 退款单号: {}, 退款ID: {}", orderNo);

            // 参数验证
            if (StringUtils.isBlank(orderNo)) {
                return RefundQueryResponse.error("订单号和退款单号不能为空");
            }

            // 查询订单信息
            MiniAppDuanJuOrder order = getByOrderNo(orderNo);
            if (order == null) {
                return RefundQueryResponse.error("订单不存在");
            }

            // 调用抖音退款查询API
            RefundQueryApiResponse apiResponse = callDouYinRefundQueryApi(order);

            if (!apiResponse.isSuccess()) {
                log.error("调用抖音退款查询API失败，订单号: {}, 错误信息: {}", orderNo, apiResponse.getErrorMessage());
                return RefundQueryResponse.error("退款查询失败: " + apiResponse.getErrorMessage());
            }
            log.info("退款状态查询成功，订单号: {}, 退款状态: {}", orderNo, apiResponse.getRefundStatus());

            return RefundQueryResponse.success("退款状态查询成功", apiResponse);

        } catch (Exception e) {
            log.error("查询退款状态异常，订单号: {}", orderNo, e);
            return RefundQueryResponse.error("退款状态查询异常: " + e.getMessage());
        }
    }

    /**
     * 调用抖音退款查询API
     */
    private RefundQueryApiResponse callDouYinRefundQueryApi(MiniAppDuanJuOrder order) {
        try {
            // 获取抖音配置
            DouyinAppConfig appConfig = douYinAppConfigService.getByBusinessType("default");
            if (appConfig == null) {
                return RefundQueryApiResponse.error("抖音配置不存在");
            }

            Map<String, Object> data = new HashMap<>();
            // 基础参数
            data.put("out_order_no", order.getOrderNo());

            OpenApiRes openApiRes = douYinHttpUtil.queryRefundOrderByOrderNo(order.getOrderNo(), order.getBusinessType());
            if (!openApiRes.isSuccess()) {
                return RefundQueryApiResponse.error("查询订单异常");
            }
            // 解析响应
            return parseRefundQueryResponse(openApiRes.getData());

        } catch (Exception e) {
            log.error("调用抖音退款查询API异常", e);
            return RefundQueryApiResponse.error("调用退款查询API异常: " + e.getMessage());
        }
    }


    /**
     * 解析退款查询响应
     */
    private RefundQueryApiResponse parseRefundQueryResponse(String response) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(response);
            JSONArray refundList = jsonResponse.getJSONArray("refund_list");
            if (refundList == null || refundList.isEmpty()) {
                return RefundQueryApiResponse.error("查询失败");
            }
            JSONObject jsonObject = refundList.getJSONObject(0);
            RefundQueryApiResponse apiResponse = new RefundQueryApiResponse();
            apiResponse.setSuccess(true);
            apiResponse.setRefundId(jsonObject.getString("refund_id"));
            apiResponse.setRefundStatus(jsonObject.getString("refund_status"));
            apiResponse.setRefundAmount(jsonObject.getInteger("refund_amount"));
            apiResponse.setRefundTime(jsonObject.getLong("refund_time"));
            apiResponse.setRefundReason(jsonObject.getString("refund_reason"));
            return apiResponse;
        } catch (Exception e) {
            log.error("解析退款查询响应失败", e);
            return RefundQueryApiResponse.error("解析退款查询响应失败");
        }
    }
}
