package com.eleven.cms.douyinduanju.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * MiniAppMiniDrama收藏记录响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(value = "MiniAppMiniDrama收藏记录响应", description = "MiniAppMiniDrama收藏记录响应数据")
public class MiniAppMiniDramaFavoriteResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    /**
     * 短剧ID
     */
    @ApiModelProperty(value = "短剧ID")
    private String dramaId;

    /**
     * 短剧名称
     */
    @ApiModelProperty(value = "短剧名称")
    private String dramaName;

    /**
     * 短剧封面图
     */
    @ApiModelProperty(value = "短剧封面图")
    private String coverUrl;

    /**
     * 收藏时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "收藏时间")
    private Date favoriteTime;

    /**
     * 收藏状态：0-已取消收藏 1-已收藏
     */
    @ApiModelProperty(value = "收藏状态：0-已取消收藏 1-已收藏")
    private Integer status;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 剧集列表
     */
    @ApiModelProperty(value = "剧集列表")
    private List<MiniAppDramaEpisodeVO> episodes;


}
