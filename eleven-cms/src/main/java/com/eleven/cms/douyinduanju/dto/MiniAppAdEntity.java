package com.eleven.cms.douyinduanju.dto;

import lombok.Data;

import java.util.Map;

@Data
public class MiniAppAdEntity {

    /**
     * 回传参数 clickId
     */
    private String callback;
    /**
     * 回传参数 广告id
     */
    private String promotionId;
    /**
     * 回传参数 项目id
     */
    private String projectId;
    /**
     * 平台, android,ios
     */
    private String platform = "android";
    /**
     * 设备id ios:idfa android: oaid
     */
    private String deviceId;

    /**
     * 渠道号
     */
    private String channel;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * ip
     */
    private String remoteAddr;

    /**
     * User-Agent
     */
    private String ua;

    /**
     * 其他附加参数
     */
    private Map<String, String> adExtraMap;
    /**
     * 来源
     */
    private String callbackSource;

    /**
     * 监测链接回调
     */
    private String miniAppCallback;

    /**
     * 回复交换机
     */
    private String replayExchange;
    /**
     * 回复路由键
     */
    private String replayRouteKey;
    private String purchaseAmount;
}
