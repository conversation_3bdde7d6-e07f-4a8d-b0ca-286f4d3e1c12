package com.eleven.cms.douyinduanju.util;

import com.eleven.cms.douyinduanju.dto.WechatPaySigningDTO;
import com.eleven.cms.douyinduanju.dto.WechatPaySigningResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 微信支付小程序签约工具类
 * 根据微信支付文档实现：https://pay.weixin.qq.com/doc/global/v3/zh/4013015440
 */
@Slf4j
@Component
public class WechatPaySigningUtil {

    /**
     * 微信支付签约API地址
     */
    private static final String WECHAT_PAY_SIGNING_URL = "https://api.mch.weixin.qq.com/papay/entrustweb";

    /**
     * 微信支付查询签约API地址
     */
    private static final String WECHAT_PAY_QUERY_URL = "https://api.mch.weixin.qq.com/papay/querycontract";

    /**
     * 微信支付解约API地址
     */
    private static final String WECHAT_PAY_UNSIGN_URL = "https://api.mch.weixin.qq.com/papay/deletecontract";

    /**
     * 创建小程序签约请求
     *
     * @param signingDTO 签约请求参数
     * @param apiKey     商户API密钥
     * @return 签约跳转URL
     */
    public String createSigningRequest(WechatPaySigningDTO signingDTO, String apiKey) {
        try {
            log.info("开始创建微信支付小程序签约请求，用户openid: {}", signingDTO.getOpenid());

            // 1. 设置必要参数
            signingDTO.setTimestamp(System.currentTimeMillis() / 1000);
            signingDTO.setNonceStr(generateNonceStr());

            // 2. 生成签名
            String sign = generateSign(signingDTO, apiKey);
            signingDTO.setSign(sign);

            // 3. 构建签约URL
            String signingUrl = buildSigningUrl(signingDTO);

            log.info("微信支付小程序签约URL生成成功: {}", signingUrl);
            return signingUrl;

        } catch (Exception e) {
            log.error("创建微信支付小程序签约请求失败", e);
            throw new RuntimeException("创建签约请求失败: " + e.getMessage());
        }
    }

    /**
     * 生成签名
     *
     * @param signingDTO 签约参数
     * @param apiKey     API密钥
     * @return 签名字符串
     */
    private String generateSign(WechatPaySigningDTO signingDTO, String apiKey) {
        try {
            // 1. 构建签名参数Map
            Map<String, Object> params = new TreeMap<>();
            params.put("appid", signingDTO.getAppid());
            params.put("mch_id", signingDTO.getMchid());
            params.put("plan_id", signingDTO.getPlanId());
            params.put("contract_code", signingDTO.getContractCode());
            params.put("request_serial", signingDTO.getOutContractCode());
            params.put("contract_display_account", signingDTO.getContractDisplayAccount());
            params.put("notify_url", signingDTO.getNotifyUrl());
            params.put("version", "1.0");
            params.put("timestamp", signingDTO.getTimestamp());
            params.put("clientip", "127.0.0.1");
            params.put("deviceid", signingDTO.getOpenid());
            params.put("mobile", "");
            params.put("email", "");
            params.put("qq", "");
            params.put("openid", signingDTO.getOpenid());
            params.put("creid", "");
            params.put("outerid", "");
            params.put("nonce_str", signingDTO.getNonceStr());

            // 2. 过滤空值并排序
            Map<String, Object> filteredParams = new TreeMap<>();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
                    filteredParams.put(entry.getKey(), entry.getValue());
                }
            }

            // 3. 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, Object> entry : filteredParams.entrySet()) {
                if (signStr.length() > 0) {
                    signStr.append("&");
                }
                signStr.append(entry.getKey()).append("=").append(entry.getValue());
            }
            signStr.append("&key=").append(apiKey);

            // 4. MD5签名
            String sign = DigestUtils.md5Hex(signStr.toString()).toUpperCase();
            log.debug("签名字符串: {}", signStr.toString());
            log.debug("生成签名: {}", sign);

            return sign;

        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("生成签名失败: " + e.getMessage());
        }
    }

    /**
     * 构建签约URL
     *
     * @param signingDTO 签约参数
     * @return 签约URL
     */
    private String buildSigningUrl(WechatPaySigningDTO signingDTO) {
        try {
            StringBuilder url = new StringBuilder(WECHAT_PAY_SIGNING_URL);
            url.append("?appid=").append(signingDTO.getAppid());
            url.append("&mch_id=").append(signingDTO.getMchid());
            url.append("&plan_id=").append(signingDTO.getPlanId());
            url.append("&contract_code=").append(signingDTO.getContractCode());
            url.append("&request_serial=").append(signingDTO.getOutContractCode());
            url.append("&contract_display_account=").append(URLEncoder.encode(signingDTO.getContractDisplayAccount(), "UTF-8"));
            url.append("&notify_url=").append(URLEncoder.encode(signingDTO.getNotifyUrl(), "UTF-8"));
            url.append("&version=1.0");
            url.append("&timestamp=").append(signingDTO.getTimestamp());
            url.append("&clientip=127.0.0.1");
            url.append("&deviceid=").append(signingDTO.getOpenid());
            url.append("&mobile=");
            url.append("&email=");
            url.append("&qq=");
            url.append("&openid=").append(signingDTO.getOpenid());
            url.append("&creid=");
            url.append("&outerid=");
            url.append("&nonce_str=").append(signingDTO.getNonceStr());
            url.append("&sign=").append(signingDTO.getSign());
            url.append("&return_web=").append(URLEncoder.encode(signingDTO.getReturnWeb(), "UTF-8"));

            return url.toString();

        } catch (UnsupportedEncodingException e) {
            log.error("构建签约URL失败", e);
            throw new RuntimeException("构建签约URL失败: " + e.getMessage());
        }
    }

    /**
     * 验证回调签名
     *
     * @param params 回调参数
     * @param apiKey API密钥
     * @return 验证结果
     */
    public boolean verifyCallbackSign(Map<String, String> params, String apiKey) {
        try {
            String receivedSign = params.get("sign");
            if (receivedSign == null || receivedSign.isEmpty()) {
                log.warn("回调签名为空");
                return false;
            }

            // 移除sign参数
            Map<String, String> signParams = new TreeMap<>(params);
            signParams.remove("sign");

            // 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, String> entry : signParams.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    if (signStr.length() > 0) {
                        signStr.append("&");
                    }
                    signStr.append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
            signStr.append("&key=").append(apiKey);

            // 计算签名
            String calculatedSign = DigestUtils.md5Hex(signStr.toString()).toUpperCase();

            boolean isValid = calculatedSign.equals(receivedSign);
            log.info("回调签名验证结果: {}, 接收签名: {}, 计算签名: {}", isValid, receivedSign, calculatedSign);

            return isValid;

        } catch (Exception e) {
            log.error("验证回调签名失败", e);
            return false;
        }
    }

    /**
     * 解析回调参数为响应对象
     *
     * @param params 回调参数
     * @return 响应对象
     */
    public WechatPaySigningResponse parseCallbackResponse(Map<String, String> params) {
        WechatPaySigningResponse response = new WechatPaySigningResponse();
        response.setReturnCode(params.get("return_code"));
        response.setReturnMsg(params.get("return_msg"));
        response.setAppid(params.get("appid"));
        response.setMchid(params.get("mch_id"));
        response.setDeviceInfo(params.get("device_info"));
        response.setNonceStr(params.get("nonce_str"));
        response.setSign(params.get("sign"));
        response.setResultCode(params.get("result_code"));
        response.setErrCode(params.get("err_code"));
        response.setErrCodeDes(params.get("err_code_des"));
        response.setPlanId(params.get("plan_id"));
        response.setContractCode(params.get("contract_code"));
        response.setOpenid(params.get("openid"));
        response.setContractId(params.get("contract_id"));
        response.setOutContractCode(params.get("request_serial"));
        response.setContractState(params.get("contract_state"));
        response.setContractDisplayAccount(params.get("contract_display_account"));
        response.setContractSignTime(params.get("contract_sign_time"));
        response.setContractExpiredTime(params.get("contract_expired_time"));
        response.setContractBankType(params.get("contract_bank_type"));
        response.setTimeStamp(params.get("timestamp"));

        return response;
    }

    /**
     * 生成随机字符串
     *
     * @return 随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    /**
     * 生成商户签约号
     *
     * @param userId 用户ID
     * @return 商户签约号
     */
    public String generateOutContractCode(String userId) {
        return "CONTRACT_" + userId + "_" + System.currentTimeMillis();
    }

    /**
     * 生成签约协议号
     *
     * @param userId 用户ID
     * @return 签约协议号
     */
    public String generateContractCode(String userId) {
        return "SIGN_" + userId + "_" + System.currentTimeMillis();
    }
}
