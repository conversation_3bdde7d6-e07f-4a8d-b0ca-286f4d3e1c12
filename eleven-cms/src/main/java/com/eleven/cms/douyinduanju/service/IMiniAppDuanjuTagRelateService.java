package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanjuTagRelate;

import java.util.List;

/**
 * @Description: 标签和剧目关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
public interface IMiniAppDuanjuTagRelateService extends IService<MiniAppDuanjuTagRelate> {

    /**
     * 根据剧目ID查询关联的标签列表
     *
     * @param dramaId 剧目ID
     * @return 标签关联列表
     */
    List<MiniAppDuanjuTagRelate> getByDramaId(String dramaId);

    /**
     * 根据标签ID查询关联的剧目列表
     *
     * @param tagId 标签ID
     * @return 剧目关联列表
     */
    List<MiniAppDuanjuTagRelate> getByTagId(String tagId);

    /**
     * 根据抖音剧目ID查询关联的标签列表
     *
     * @param albumId 抖音剧目ID
     * @return 标签关联列表
     */
    List<MiniAppDuanjuTagRelate> getByAlbumId(String albumId);

    /**
     * 为剧目批量添加标签
     *
     * @param dramaId  剧目ID
     * @param albumId  抖音剧目ID
     * @param tagIds   标签ID列表
     * @param tagNames 标签名称列表
     * @return 操作结果
     */
    boolean batchAddTagsForDrama(String dramaId, String albumId, List<String> tagIds, List<String> tagNames);

    /**
     * 删除剧目的所有标签关联
     *
     * @param dramaId 剧目ID
     * @return 操作结果
     */
    boolean removeTagsByDramaId(String dramaId);

    /**
     * 删除标签的所有剧目关联
     *
     * @param tagId 标签ID
     * @return 操作结果
     */
    boolean removeDramasByTagId(String tagId);

    /**
     * 删除特定的剧目-标签关联
     *
     * @param dramaId 剧目ID
     * @param tagId   标签ID
     * @return 操作结果
     */
    boolean removeRelation(String dramaId, String tagId);

    /**
     * 更新剧目的标签关联（先删除旧的，再添加新的）
     *
     * @param dramaId  剧目ID
     * @param albumId  抖音剧目ID
     * @param tagIds   新的标签ID列表
     * @param tagNames 新的标签名称列表
     * @return 操作结果
     */
    boolean updateDramaTagRelations(String dramaId, String albumId, List<String> tagIds, List<String> tagNames);

    /**
     * 检查剧目和标签是否已关联
     *
     * @param dramaId 剧目ID
     * @param tagId   标签ID
     * @return 是否已关联
     */
    boolean isRelationExists(String dramaId, String tagId);

    /**
     * 根据剧目ID获取标签名称列表
     *
     * @param dramaId 剧目ID
     * @return 标签名称列表
     */
    List<String> getTagNamesByDramaId(String dramaId);

    /**
     * 根据标签ID获取剧目ID列表
     *
     * @param tagId 标签ID
     * @return 剧目ID列表
     */
    List<String> getDramaIdsByTagId(String tagId);
}
