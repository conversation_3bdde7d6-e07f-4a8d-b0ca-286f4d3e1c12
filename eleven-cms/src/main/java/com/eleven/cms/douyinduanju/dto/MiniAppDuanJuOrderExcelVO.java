package com.eleven.cms.douyinduanju.dto;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MiniAppDuanJuOrderExcelVO {


    @Excel(name = "订单编号", width = 15)
    private String orderNo;

    @Excel(name = "小程序名称", width = 15)
    private String miniAppName;

    @Excel(name = "支付流水号", width = 15)
    private String transactionId;


    @Excel(name = "用户id", width = 15)
    private String userId;

    @Excel(name = "手机号", width = 15)
    private String mobile;

    @Excel(name = "套餐id", width = 15)
    private String packageId;

    @Excel(name = "模板名称", width = 15)
    private String packageName;

    @Excel(name = "模板类型", width = 15)
    private String orderTypeStr;


    @Excel(name = "解锁内容", width = 15)
    private String packageContent;

    @Excel(name = "订单金额", width = 15)
    private BigDecimal orderAmount;

    @Excel(name = "支付方式", width = 15)
    private String payTypeStr;


    @Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;


    @Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "支付状态", width = 15)
    private String payStatusStr;

    /**
     * 退款时间
     */
    @Excel(name = "退款时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    @Excel(name = "付费回传状态", width = 15)
    private String adReportCallBackStatusStr;
}
