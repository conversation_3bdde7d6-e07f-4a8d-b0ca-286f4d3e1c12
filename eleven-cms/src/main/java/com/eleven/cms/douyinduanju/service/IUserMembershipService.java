package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.dto.MembershipStatusInfo;
import com.eleven.cms.douyinduanju.entity.DuanJuPackage;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.entity.UserMembership;

import java.util.Date;
import java.util.List;

/**
 * 用户会员权益服务接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IUserMembershipService extends IService<UserMembership> {

    /**
     * 根据用户ID获取有效的会员信息
     *
     * @param userId 用户ID
     * @return 会员信息
     */
    UserMembership getValidMembershipByUserId(String userId);

    /**
     * 开通会员权益
     * @return 是否成功
     */
    boolean activateMembership(MiniAppDuanJuOrder order, DuanJuPackage packageInfo);


    /**
     * 获取用户未生效的会员记录
     *
     * @param userId 用户ID
     * @return 未生效的会员记录列表
     */
    List<UserMembership> getInactiveMembershipsByUserId(String userId);

    /**
     * 获取用户已生效的会员记录
     *
     * @param userId 用户ID
     * @return 已生效的会员记录列表
     */
    List<UserMembership> getActiveMembershipsByUserId(String userId);

    /**
     * 计算用户的实际过期时间（基于生效和未生效的会员记录）
     *
     * @param userId 用户ID
     * @return 计算后的过期时间，如果没有会员则返回null
     */
    Date calculateUserExpireTime(String userId);

    /**
     * 激活下一个未生效的会员（当前会员过期时调用）
     *
     * @param userId 用户ID
     * @return 是否成功激活
     */
    boolean activateNextInactiveMembership(String userId);

    /**
     * 获取用户会员的完整状态信息
     *
     * @param userId 用户ID
     * @return 会员状态信息
     */
    MembershipStatusInfo getUserMembershipStatus(String userId);

    /**
     * 检查并处理DuanJuUser过期状态
     * 1. 检查DuanJuUser是否过期
     * 2. 如果过期，检查是否有生效的会员
     * 3. 如果没有生效会员，尝试激活下一个会员
     * 4. 如果没有可用会员，设置会员状态为1（非会员）
     *
     * @param userId 用户ID
     * @return 处理结果
     */
    boolean checkAndHandleUserExpiration(String userId);

    void updateUserMemberInfo(String userId);

}
