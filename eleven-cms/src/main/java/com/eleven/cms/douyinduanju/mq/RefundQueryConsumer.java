package com.eleven.cms.douyinduanju.mq;

import com.eleven.cms.douyinduanju.config.RefundQueryRabbitConfig;
import com.eleven.cms.douyinduanju.dto.RefundQueryMessage;
import com.eleven.cms.douyinduanju.dto.RefundQueryResponse;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.service.IUserMembershipService;
import com.eleven.cms.douyinduanju.service.impl.MiniAppDuanJuOrderServiceImpl;
import com.eleven.cms.enums.PayStatueEnum;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 退款查询MQ消息消费者
 * 处理延迟退款查询消息
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Component
@Slf4j
public class RefundQueryConsumer {

    @Autowired
    private MiniAppDuanJuOrderServiceImpl orderService;


    @Resource
    private IUserMembershipService userMembershipService;

    /**
     * 处理退款查询消息
     *
     * @param message 退款查询消息
     */
    @RabbitListener(queues = RefundQueryRabbitConfig.REFUND_QUERY_QUEUE)
    public void handleRefundQueryMessage(RefundQueryMessage message, Channel channel, Message amqpMessage) {
        long deliveryTag = amqpMessage.getMessageProperties().getDeliveryTag();


        log.info("收到退款查询消息: {}, deliveryTag: {}", message, deliveryTag);

        // 执行退款状态查询
        RefundQueryResponse response = orderService.queryRefundStatus(
                message.getOrderNo()
        );

        handleQuerySuccess(message, response);

    }

    /**
     * 处理查询成功
     */
    private void handleQuerySuccess(RefundQueryMessage message, RefundQueryResponse response) {
        try {
            log.info("退款查询处理成功，订单号: {}, 退款状态: {}",
                    message.getOrderNo(),
                    response.getData() != null ? response.getData().getRefundStatus() : "未知");

            // 查询订单
            MiniAppDuanJuOrder order = orderService.lambdaQuery()
                    .eq(MiniAppDuanJuOrder::getOrderNo, message.getOrderNo())
                    .one();

            if (order == null) {
                log.error("退款回调订单不存在，订单单号: {}", message.getOrderNo());
                return;
            }
            // 检查订单状态，避免重复处理
            if (order.getRefundStatus() != null && order.getRefundStatus() == 2) {
                log.info("订单已退款成功，跳过处理，订单单号: {}", message.getOrderNo());
            }

            // 更新订单退款状态
            MiniAppDuanJuOrder updateOrder = new MiniAppDuanJuOrder();
            updateOrder.setId(order.getId());
            updateOrder.setRefundStatus(2); // 退款成功
            updateOrder.setPayStatus(PayStatueEnum.REFUNDED.getPayType());
//            updateOrder.setRefundCompleteTime(new Date(response.getData().getRefundTime()));
            updateOrder.setUpdateTime(new Date());
            orderService.updateById(updateOrder);


            //回收权益
            orderService.handleBenefitRevocation(order);


            //激活下一个未生效的会员
            userMembershipService.activateNextInactiveMembership(order.getUserId());

            //更新用户会员信息
            userMembershipService.updateUserMemberInfo(order.getUserId());

        } catch (Exception e) {
            log.error("处理退款查询成功结果异常", e);
        }
    }


}
