package com.eleven.cms.douyinduanju.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunion.service.IPayOrdersService;
import com.eleven.cms.douyinduanju.annotation.TokenRequired;
import com.eleven.cms.douyinduanju.dto.*;
import com.eleven.cms.douyinduanju.entity.MiniAppDuanJuOrder;
import com.eleven.cms.douyinduanju.service.IMiniAppDuanJuOrderService;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import com.eleven.cms.douyinduanju.util.PaymentCallbackVerifyUtil;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.service.IDouyinAppConfigService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.eleven.cms.douyinduanju.util.TokenUtils.getCurrentUserId;

@Data
@Slf4j
@RestController
@RequestMapping("miniApi/duanju/api/")
public class DuanJuOrderController {


    @Resource
    IMiniAppDuanJuOrderService orderService;


    @GetMapping("/listOrder")
    @TokenRequired
    public Result<Object> listOrder(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<MiniAppDuanJuOrder> page1 = null;
        try {
            log.info("listOrder");
            Page<MiniAppDuanJuOrder> page = new Page<>(pageNo, pageSize);
            Integer currentUserId = getCurrentUserId();
            page1 = orderService.lambdaQuery().eq(MiniAppDuanJuOrder::getUserId, currentUserId).orderByDesc(MiniAppDuanJuOrder::getCreateTime).page(page);
        } catch (Exception e) {
            log.error("获取订单列表异常", e);
        }
        return Result.ok(page1);
    }

    /**
     * 生成抖音支付签名参数
     * 根据套餐类型生成对应的商品类型：剧集商品-404，整剧商品-405
     */
    @TokenRequired
    @PostMapping("/createOrder")
    public Result<?> createOrder(@RequestBody OrderReq request) {
        try {
            log.info("支付入参:{}", request);
            Integer currentUserId = getCurrentUserId();
            PaymentSignatureRes order = orderService.createOrder(request, currentUserId);
            return Result.ok(order);
        } catch (Exception e) {
            log.error("生成支付签名失败", e);
            return Result.error("生成支付签名失败: " + e.getMessage());
        }
    }

    /**
     * 抖音支付结果回调接口
     * 接收抖音开放平台的支付结果通知
     */
    @PostMapping("/payment/callback")
    public PaymentCallbackRes paymentCallback(@RequestBody PaymentCallbackReq request, HttpServletRequest httpRequest) {
        try {
            log.info("收到抖音支付回调，type: {}, version: {}", request.getType(), request.getVersion());
            log.info("回调参数:{}", JSONObject.toJSONString(request));
            // 验证回调类型和版本
            if (!"payment".equals(request.getType())) {
                log.error("不支持的回调类型: {}", request.getType());
                return PaymentCallbackRes.error("不支持的回调类型");
            }

            if (!"3.0".equals(request.getVersion())) {
                log.error("不支持的回调版本: {}", request.getVersion());
                return PaymentCallbackRes.error("不支持的回调版本");
            }

            // 获取验签参数
//            String body = getRequestBody(httpRequest);
            String timestamp = httpRequest.getHeader("Byte-Timestamp");
            String nonce = httpRequest.getHeader("Byte-Nonce-Str");
            String signature = httpRequest.getHeader("Byte-Signature");

            log.info("验签参数 - timestamp: {}, nonce: {}, signature: {}", timestamp, nonce, signature);

            // 解析回调消息
            PaymentCallbackMsg callbackMsg = JSONObject.parseObject(request.getMsg(), PaymentCallbackMsg.class);
            log.info("解析回调消息成功，订单号: {}, 状态: {}", callbackMsg.getOutOrderNo(), callbackMsg.getStatus());
//            String outOrderNo = callbackMsg.getOutOrderNo();

//            verifySign(outOrderNo, body, timestamp, nonce, signature);

            // 处理支付回调
            boolean handleResult = orderService.handlePaymentCallback(callbackMsg);
            if (handleResult) {
                log.info("支付回调处理成功，订单号: {}", callbackMsg.getOutOrderNo());
                return PaymentCallbackRes.success();
            } else {
                log.error("支付回调处理失败，订单号: {}", callbackMsg.getOutOrderNo());
                return PaymentCallbackRes.error("处理失败");
            }

        } catch (Exception e) {
            log.error("支付回调处理异常", e);
            return PaymentCallbackRes.error("系统异常");
        }
    }

    @Resource
    private IDouyinAppConfigService douYinAppConfigService;

    private void verifySign(String outOrderNo, String body, String timestamp, String nonce, String signature) {
        MiniAppDuanJuOrder order = orderService.getByOrderNo(outOrderNo);
        String businessType = order.getBusinessType();
        DouyinAppConfig appConfig = douYinAppConfigService.getByBusinessType(businessType);
        String publicKey = appConfig.getPublicKey();

        // 验签（这里需要配置抖音平台公钥）
        if (StringUtils.isNotBlank(publicKey)) {
            boolean verifyResult = PaymentCallbackVerifyUtil.verifySignature(body, timestamp, nonce, signature, publicKey);
            if (!verifyResult) {
                log.error("支付回调验签失败");
            }
            log.info("支付回调验签成功");
        } else {
            log.warn("未配置平台公钥，跳过验签");
        }
    }

    /**
     * 退款回调处理
     */
    @AutoLog(value = "退款回调处理")
    @ApiOperation(value = "退款回调处理", notes = "处理抖音退款回调通知")
    @PostMapping(value = "/refundCallback")
    public Result<?> refundCallback(HttpServletRequest request) {

        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyJson = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("收到退款回调: {}", notifyJson);
            JSONObject jsonObject = JSONObject.parseObject(notifyJson);
            String mgs = (String) jsonObject.get("msg");
            RefundNotifyMsg refundNotifyMsg = JSONObject.parseObject(mgs, RefundNotifyMsg.class);
            boolean result = orderService.handleRefundCallback(refundNotifyMsg);
            if (result) {
                return Result.ok("处理成功");
            } else {
                return Result.error("处理失败");
            }
        } catch (Exception e) {
            log.error("处理退款回调异常", e);
            return Result.error("处理退款回调失败: " + e.getMessage());
        }
    }


    @Resource
    private DouYinHttpUtil douYinHttpUtil;

    @Resource
    IPayOrdersService iPayOrdersService;

    /**
     * 查询订单状态 并更新订单状态
     */
    @TokenRequired
    @GetMapping("/queryOrderStatus")
    public Result<Object> queryOrderStatus(@RequestParam String orderNo) {

        MiniAppDuanJuOrder order = orderService.getByOrderNo(orderNo);
        if (Objects.isNull(order)) {
            return Result.error("订单不存在");
        }
        if (!Objects.equals(order.getPayStatus(), PayStatueEnum.UNPAID.getPayType())) {
            return Result.ok(order);
        }
        OpenApiRes openApiRes = douYinHttpUtil.queryOrderByOrderNo(orderNo);
        if (!openApiRes.isSuccess()) {
            return Result.error(openApiRes.getErrMsg());
        }
        OpenApiRes.OrderInfo orderInfo = openApiRes.getOrderInfo();

        String payStatus = orderInfo.getPayStatus();
        if (Objects.equals(payStatus, "PROCESS")) {
            return Result.ok(order);
        }
        if (Objects.equals(payStatus, "SUCCESS") || Objects.equals(payStatus, "FAIL")) {
            Date payTime = new Date(Long.parseLong(orderInfo.getPayTime()) * 1000);
            iPayOrdersService.updatePayStatus(orderNo, PayStatueEnum.PAID, payTime);
            orderService.updateOrderPayStatus(orderNo, orderInfo.getOrderId(), payTime);
        }
        MiniAppDuanJuOrder result = orderService.getByOrderNo(orderNo);
        return Result.ok(result);
    }

    /**
     * 退款请求参数
     */
    @Data
    public static class RefundRequest {

        @ApiParam("订单号")
        private String orderNo;

        @ApiParam("退款金额（分）")
        private BigDecimal refundAmount;

        @ApiParam("退款原因")
        private Map<String, Object> refundReason;

        @ApiParam("业务类型")
        private String businessType;


        @Override
        public String toString() {
            return "RefundRequest{" +
                    "orderNo='" + orderNo + '\'' +
                    ", refundAmount=" + refundAmount +
                    ", refundReason='" + refundReason + '\'' +
                    ", businessType='" + businessType + '\'' +
                    '}';
        }
    }


}
