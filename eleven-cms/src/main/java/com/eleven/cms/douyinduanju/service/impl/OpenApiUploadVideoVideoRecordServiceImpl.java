package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.dto.UploadVideoParam;
import com.eleven.cms.douyinduanju.entity.OpenApiUploadVideoRecord;
import com.eleven.cms.douyinduanju.mapper.OpenApiUploadVideoRecordMapper;
import com.eleven.cms.douyinduanju.service.IOpenApiUploadVideoRecordService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Objects;

@Service
public class OpenApiUploadVideoVideoRecordServiceImpl extends ServiceImpl<OpenApiUploadVideoRecordMapper, OpenApiUploadVideoRecord> implements IOpenApiUploadVideoRecordService {

    @Override
    public OpenApiUploadVideoRecord uploadVideo(UploadVideoParam uploadVideoParam) {

        OpenApiUploadVideoRecord uploadRecord = new OpenApiUploadVideoRecord();
        uploadRecord.setTitle(uploadVideoParam.getTitle());
        uploadRecord.setDescription(uploadVideoParam.getDescription());
        uploadRecord.setResourceType(uploadVideoParam.getResourceType());
        uploadRecord.setStatus(2);
        uploadRecord.setResourceVideoUrl(uploadVideoParam.getUrl());
        uploadRecord.setDyCloudId(uploadVideoParam.getDyCloudId());
        save(uploadRecord);
        return uploadRecord;
    }

    @Override
    public void updateByAlbumIdAndSeq(String AlbumId, HashMap<String, String> map) {
        map.forEach((k, episodeId) -> {
            OpenApiUploadVideoRecord videoRecord = lambdaQuery().eq(OpenApiUploadVideoRecord::getAlbumId, AlbumId).eq(OpenApiUploadVideoRecord::getSeq, k).last("limit 1").one();
            if (Objects.nonNull(videoRecord)) {
                OpenApiUploadVideoRecord update = new OpenApiUploadVideoRecord();
                update.setId(videoRecord.getId());
                update.setEpisodeId(episodeId);
                updateById(update);
            }
        });
    }
}
