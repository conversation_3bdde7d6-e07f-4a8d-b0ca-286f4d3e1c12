package com.eleven.cms.douyinduanju.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户登录请求DTO
 */
@Data
@ApiModel(value = "用户登录请求", description = "用户登录请求参数")
public class LoginReq {

    /**
     * 用户来源
     */
    @ApiModelProperty(value = "用户来源")
    private String source;

    private String code;

    /**
     * 小程序code
     */
    private String resource;
    private String mobile;
    private String openId;
    private String subChannel;
}
