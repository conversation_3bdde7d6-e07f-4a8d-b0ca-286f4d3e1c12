package com.eleven.cms.douyinduanju.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.douyinduanju.entity.DuanJuUser;
import com.eleven.cms.douyinduanju.service.IDuanJuUserService;
import com.eleven.cms.douyinduanju.service.ITokenService;
import com.eleven.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * Token服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class TokenServiceImpl implements ITokenService {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IDuanJuUserService duanJuUserService;

    // Token前缀
    private static final String TOKEN_PREFIX = "miniApp:duanju:token:";

    // 用户Token映射前缀
    private static final String USER_TOKEN_PREFIX = "miniApp:duanju:user_token:";

    // 默认token类型
    private static final String DEFAULT_TOKEN_TYPE = "default";

    // Token过期时间（秒）- 24小时
    private static final long TOKEN_EXPIRE_TIME = 24 * 60 * 60;

    @Override
    public String generateToken(DuanJuUser user, String tokenType) {
        if (user == null || Objects.isNull(String.valueOf(user.getId()))) {
            throw new BusinessException("用户信息不能为空");
        }

        if (StringUtils.isEmpty(tokenType)) {
            tokenType = DEFAULT_TOKEN_TYPE;
        }

        // 清除用户之前的所有token
        removeUserAllTokens(String.valueOf(user.getId()), tokenType);

        // 生成唯一token
        String token = UUID.randomUUID().toString().replace("-", "");

        // 构建Redis key
        String redisKey = buildRedisKey(token, tokenType);
        String userTokenKey = buildUserTokenKey(String.valueOf(user.getId()), tokenType);

        // 构建token信息
        JSONObject tokenInfo = new JSONObject();
        tokenInfo.put("userId", user.getId());
        tokenInfo.put("openId", user.getOpenId());
        tokenInfo.put("nickName", user.getNickName());
        tokenInfo.put("mobile", user.getMobile());
        tokenInfo.put("source", user.getSource());
        tokenInfo.put("tokenType", tokenType);
        tokenInfo.put("createTime", new Date());
        tokenInfo.put("expireTime", new Date(System.currentTimeMillis() + TOKEN_EXPIRE_TIME * 1000));

        // 存储token信息到Redis
        redisUtil.set(redisKey, tokenInfo.toJSONString(), TOKEN_EXPIRE_TIME);

        // 存储用户与token的映射关系
        redisUtil.set(userTokenKey, token, TOKEN_EXPIRE_TIME);

        log.info("生成token成功，userId: {}, tokenType: {}, token: {}", user.getId(), tokenType, token);
        return token;
    }

    @Override
    public String generateToken(DuanJuUser user) {
        return generateToken(user, DEFAULT_TOKEN_TYPE);
    }

    @Override
    public boolean validateToken(String token, String tokenType) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }

        if (StringUtils.isEmpty(tokenType)) {
            tokenType = DEFAULT_TOKEN_TYPE;
        }

        String redisKey = buildRedisKey(token, tokenType);
        String tokenInfoStr = (String) redisUtil.get(redisKey);

        if (StringUtils.isEmpty(tokenInfoStr)) {
            log.warn("Token不存在或已过期，token: {}, tokenType: {}", token, tokenType);
            return false;
        }

        try {
            JSONObject tokenInfo = JSONObject.parseObject(tokenInfoStr);
            Date expireTime = tokenInfo.getDate("expireTime");

            if (expireTime != null && expireTime.before(new Date())) {
                log.warn("Token已过期，token: {}, tokenType: {}, expireTime: {}", token, tokenType, expireTime);
                // 删除过期token
                redisUtil.del(redisKey);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("Token校验异常，token: {}, tokenType: {}", token, tokenType, e);
            return false;
        }
    }

    @Override
    public boolean validateToken(String token) {
        return validateToken(token, DEFAULT_TOKEN_TYPE);
    }

    @Override
    public DuanJuUser getUserFromToken(String token, String tokenType) {
        if (!validateToken(token, tokenType)) {
            return null;
        }

        if (StringUtils.isEmpty(tokenType)) {
            tokenType = DEFAULT_TOKEN_TYPE;
        }

        String redisKey = buildRedisKey(token, tokenType);
        String tokenInfoStr = (String) redisUtil.get(redisKey);

        if (StringUtils.isEmpty(tokenInfoStr)) {
            return null;
        }

        try {
            JSONObject tokenInfo = JSONObject.parseObject(tokenInfoStr);
            String userId = tokenInfo.getString("userId");

            if (StringUtils.isEmpty(userId)) {
                return null;
            }

            // 从数据库获取最新用户信息
            DuanJuUser user = duanJuUserService.getById(userId);
            if (user == null) {
                log.warn("用户不存在，userId: {}", userId);
                // 删除无效token
                redisUtil.del(redisKey);
                return null;
            }

            return user;
        } catch (Exception e) {
            log.error("从token获取用户信息异常，token: {}, tokenType: {}", token, tokenType, e);
            return null;
        }
    }

    @Override
    public DuanJuUser getUserFromToken(String token) {
        return getUserFromToken(token, DEFAULT_TOKEN_TYPE);
    }

    @Override
    public String refreshToken(String token, String tokenType) {
        DuanJuUser user = getUserFromToken(token, tokenType);
        if (user == null) {
            throw new BusinessException("Token无效，无法刷新");
        }

        // 删除旧token
        removeToken(token, tokenType);

        // 生成新token
        return generateToken(user, tokenType);
    }

    @Override
    public String refreshToken(String token) {
        return refreshToken(token, DEFAULT_TOKEN_TYPE);
    }

    @Override
    public void removeToken(String token, String tokenType) {
        if (StringUtils.isEmpty(token)) {
            return;
        }

        if (StringUtils.isEmpty(tokenType)) {
            tokenType = DEFAULT_TOKEN_TYPE;
        }

        try {
            String redisKey = buildRedisKey(token, tokenType);

            // 先获取token信息，以便删除用户token映射
            String tokenInfoStr = (String) redisUtil.get(redisKey);
            if (StringUtils.isNotEmpty(tokenInfoStr)) {
                JSONObject tokenInfo = JSONObject.parseObject(tokenInfoStr);
                String userId = tokenInfo.getString("userId");

                if (StringUtils.isNotEmpty(userId)) {
                    // 删除用户token映射
                    String userTokenKey = buildUserTokenKey(userId, tokenType);
                    redisUtil.del(userTokenKey);
                }
            }

            // 删除token信息
            redisUtil.del(redisKey);

            log.info("删除token成功，token: {}, tokenType: {}", token, tokenType);
        } catch (Exception e) {
            log.error("删除token异常，token: {}, tokenType: {}", token, tokenType, e);
        }
    }

    @Override
    public void removeToken(String token) {
        removeToken(token, DEFAULT_TOKEN_TYPE);
    }

    @Override
    public void removeUserAllTokens(String userId, String tokenType) {
        if (StringUtils.isEmpty(userId)) {
            return;
        }

        if (StringUtils.isEmpty(tokenType)) {
            tokenType = DEFAULT_TOKEN_TYPE;
        }

        try {
            // 获取用户当前的token
            String userTokenKey = buildUserTokenKey(userId, tokenType);
            String currentToken = (String) redisUtil.get(userTokenKey);

            if (StringUtils.isNotEmpty(currentToken)) {
                // 删除token信息
                String tokenRedisKey = buildRedisKey(currentToken, tokenType);
                redisUtil.del(tokenRedisKey);

                // 删除用户token映射
                redisUtil.del(userTokenKey);

                log.info("清除用户token成功，userId: {}, tokenType: {}, token: {}", userId, tokenType, currentToken);
            }
        } catch (Exception e) {
            log.error("清除用户token异常，userId: {}, tokenType: {}", userId, tokenType, e);
        }
    }

    @Override
    public void removeUserAllTokens(String userId) {
        removeUserAllTokens(userId, DEFAULT_TOKEN_TYPE);
    }

    /**
     * 构建Redis key
     */
    private String buildRedisKey(String token, String tokenType) {
        return TOKEN_PREFIX + token;
    }

    /**
     * 构建用户Token映射Redis key
     */
    private String buildUserTokenKey(String userId, String tokenType) {
        return USER_TOKEN_PREFIX + userId;
    }
}
