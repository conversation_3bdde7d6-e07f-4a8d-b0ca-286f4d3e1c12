package com.eleven.cms.douyinduanju.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aivrbt.entity.MiniAppCatalog;
import com.eleven.cms.aivrbt.entity.MiniAppCatalogResource;
import com.eleven.cms.aivrbt.service.IAppCatalogResourceService;
import com.eleven.cms.aivrbt.service.IAppCatalogService;
import com.eleven.cms.aivrbt.utils.DouYinAPIUtil;
import com.eleven.cms.client.AppMobilePayLinkConfigClient;
import com.eleven.cms.client.entity.AppMobilePayChannelConfig;
import com.eleven.cms.douyinduanju.annotation.TokenRequired;
import com.eleven.cms.douyinduanju.constant.DuanjuConstant;
import com.eleven.cms.douyinduanju.dto.*;
import com.eleven.cms.douyinduanju.entity.*;
import com.eleven.cms.douyinduanju.service.*;
import com.eleven.cms.douyinduanju.util.DouYinHttpUtil;
import com.eleven.cms.douyinduanju.util.TokenUtils;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IDouyinAppConfigService;
import com.eleven.cms.vo.MobileRegionResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.eleven.cms.douyinduanju.util.TokenUtils.getCurrentUser;
import static com.eleven.cms.douyinduanju.util.TokenUtils.getCurrentUserId;

@RestController
@RequestMapping("miniApi/duanju/api/")
@Slf4j
@ApiOperation(value = "抖音短剧api接口")
public class DouYinDuanjuApiController {


    @Resource
    private IMiniAppMiniDramaService miniAppMiniDramaService;

    @Resource
    private IMiniAppDramaColumnService miniAppDramaColumnService;

    @Resource
    private IDuanJuBannerService duanJuBannerService;

    @Resource
    private IDuanJuPackageService duanJuPackageService;

    @Resource
    DouYinHttpUtil douYinHttpUtil;

    @Resource
    IOpenApiAlbumInfoService openApiAlbumInfoService;

    @Resource
    private IMiniAppDramaEpisodeService episodeService;

    @Resource
    private IOpenApiUploadVideoRecordService uploadVideoRecordService;


    @Resource
    private IDuanJuUserService duanJuUserService;

    @Resource
    RedisUtil redisUtil;

    @Resource
    IMiniAppCouponRecordService miniAppCouponRecordService;
    @Resource
    private ITokenService tokenService;

    @Resource
    IMiniAppMiniDramaFavoriteService favoriteService;

    /**
     * 用户登录接口
     * 如果用户不存在则自动创建新用户
     */
    @PostMapping("/login")
    public Result<?> login(@RequestBody LoginReq loginReq) {
        log.info("用户登录入参:{}", loginReq);
        OpenApiRes openApiRes = douYinHttpUtil.getOpenId(loginReq.getCode(), loginReq.getResource());
        if (!openApiRes.isSuccess()) {
            return Result.error(openApiRes.getErrMsg());
        }
        LoginRes result = new LoginRes();


        OpenApiRes.DouYinSessionRes douYinSessionRes = openApiRes.getDouYinSessionRes();
        try {
            DuanJuUser user = duanJuUserService.loginOrRegister(
                    douYinSessionRes.getOpenid(),
                    "",
                    loginReq.getMobile(),
                    loginReq.getSource(),
                    loginReq.getSubChannel(),
                    loginReq.getResource()
            );


            result.setUserId(user.getId());
            result.setNickName(user.getNickName());
            result.setOpenId(user.getOpenId());
            result.setMobile(user.getMobile());
            result.setMemberStatus(user.getMemberStatus());
            result.setPayFlat(user.getPayFlat());
            result.setSource(user.getSource());
            result.setResource(user.getResource());
            result.setMemberType(user.getMemberType());
            result.setRegisterTime(user.getRegisterTime());
            result.setLastOperateTime(user.getLastOperateTime());
            result.setSessionKey(douYinSessionRes.getSessionKey());
            result.setAnonymousOpenid(douYinSessionRes.getAnonymousOpenid());
            // 生成token
            String token = tokenService.generateToken(user, loginReq.getResource());
            result.setToken(token);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("用户登录失败", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    @TokenRequired
    @GetMapping("/getUserInfo")
    public Result<Object> getUserInfo() {
        return Result.ok(TokenUtils.getCurrentUser());
    }

    /**
     * 分页获取栏目下的视频
     */
    @GetMapping("/pageAlbumInfoByColumnId")
    public Result<Object> pageDramaByColumnId(@RequestParam(required = false) String columnId,
                                              @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                              @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        IPage<AlbumInfVO> page = new Page<>(pageNum, pageSize);
        DramaReq req = new DramaReq();
        req.setColumnId(columnId);
        IPage<AlbumInfVO> miniAppMiniDramaVOIPage = miniAppMiniDramaService.pageAlbumInfo(req, page);
        return Result.ok(miniAppMiniDramaVOIPage);
    }

    @GetMapping("/pageAlbumInfo")
    public Result<Object> pageDrama(HttpServletRequest request, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<AlbumInfVO> page = new Page<>(pageNo, pageSize);
        Map<String, String[]> parameterMap = request.getParameterMap();
        DramaReq req = new DramaReq();
        if (Objects.nonNull(parameterMap.get("name"))) {
            String[] names = parameterMap.get("name");
            req.setName(names[0]);
        }
        IPage<AlbumInfVO> miniAppMiniDramaVOIPage = miniAppMiniDramaService.pageAlbumInfo(req, page);
        return Result.ok(miniAppMiniDramaVOIPage);
    }

    @Resource
    IOpenApiUploadPicRecordService uploadPicRecordService;

    @GetMapping("/uploadPic")
    public Result<?> uploadPic(String id) {
        OpenApiUploadPicRecord picRecord = uploadPicRecordService.getById(id);
        if (Objects.equals(3, picRecord.getStatus())) {
            return Result.error("图片已上传");
        }
        if (StringUtils.isEmpty(picRecord.getPicUrl())) {
            return Result.error("图片地址不能为空");
        }
        OpenApiRes openApiRes = douYinHttpUtil.uploadPic(picRecord.getPicUrl());
        if (openApiRes.isSuccess()) {
            String openPicId = openApiRes.getOpenPicId();
            OpenApiUploadPicRecord update = new OpenApiUploadPicRecord();
            update.setId(id);
            update.setOpenPicId(openPicId);
            update.setStatus(3);
            uploadPicRecordService.updateById(update);
        } else {
            OpenApiUploadPicRecord update = new OpenApiUploadPicRecord();
            update.setId(id);
            update.setStatus(2);
            uploadPicRecordService.updateById(update);
            return Result.error(openApiRes.getErrMsg());
        }
        return Result.ok("上传成功");
    }



    @Resource
    private IAppCatalogResourceService appCatalogResourceService;

    @Resource
    private IAppCatalogService appCatalogService;

    @GetMapping("/getColumnIdByAlbumId")
    public Result<?> getColumnByAlbumId(String albumId) {
        MiniAppMiniDrama byAlbumId = miniAppMiniDramaService.getByAlbumId(albumId);
        List<MiniAppCatalogResource> list = appCatalogResourceService.lambdaQuery()
                .eq(MiniAppCatalogResource::getStatus, 1)
                .eq(MiniAppCatalogResource::getIsDeleted, 0)
                .eq(MiniAppCatalogResource::getResId, byAlbumId.getId()).list();

        if (CollectionUtils.isNotEmpty(list)) {
            List<String> pidList = list.stream().map(MiniAppCatalogResource::getPid).collect(Collectors.toList());
            List<MiniAppCatalog> records = appCatalogService.lambdaQuery()
                    .eq(MiniAppCatalog::getStatus, 1)
                    .eq(MiniAppCatalog::getIsDeleted, 0)
                    .in(MiniAppCatalog::getId, pidList).list();
            return Result.ok(records);
        }
        return Result.ok(Collections.emptyList());
    }

    @GetMapping("/listHostDrama")
    public Result<Object> listHostDrama(@RequestParam(name = "columnId", required = false) String columnId, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<MiniAppMiniDrama> page = new Page<>(pageNo, pageSize);
        IPage<MiniAppMiniDrama> result = miniAppMiniDramaService.listHostDrama(columnId, page, Collections.emptyList());

        List<MiniAppMiniDrama> records = result.getRecords();
        Integer currentUserId = getCurrentUserId();
        Map<String, MiniAppMiniDramaFavorite> favoriteMap = new HashMap<>();
        if (Objects.nonNull(currentUserId)) {
            List<MiniAppMiniDramaFavorite> favoriteList = favoriteService.getFavoriteByUserId(currentUserId);
            favoriteMap = favoriteList.stream().collect(Collectors.toMap(MiniAppMiniDramaFavorite::getAlbumId, v -> v));
        }
        for (MiniAppMiniDrama record : records) {
            MiniAppMiniDramaFavorite miniAppMiniDramaFavorite = favoriteMap.get(record.getAlbumId());
            if (Objects.nonNull(miniAppMiniDramaFavorite)) {
                record.setFavoriteStatus(true);
            } else {
                record.setFavoriteStatus(false);
            }
        }
        return Result.ok(result);
    }

    @GetMapping("/pageEpisode")
    public Result<Object> pageEpisode(HttpServletRequest request, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<EpisodeVO> page = new Page<>(pageNo, pageSize);
        Map<String, String[]> parameterMap = request.getParameterMap();
        EpisodeReq req = new EpisodeReq();
        if (Objects.nonNull(parameterMap.get("dramaTitle"))) {
            String[] names = parameterMap.get("dramaTitle");
            req.setDramaTitle(names[0]);
        }
        IPage<EpisodeVO> miniAppMiniDramaVOIPage = episodeService.pageEpisode(req, page);
        return Result.ok(miniAppMiniDramaVOIPage);
    }

    @Resource
    private IMiniAppDuanjuTagRelateService tagRelateService;
    @TokenRequired
    @GetMapping("/listEpisodeByAlbumId")
    public Result<?> listEpisodeByAlbumId(String albumId) {
        Integer currentUserId = TokenUtils.getCurrentUserId();

        MiniAppMiniDrama miniAppMiniDrama = miniAppMiniDramaService.getByAlbumId(albumId);
        if (Objects.isNull(miniAppMiniDrama)) {
            return Result.error("该短剧不存在");
        }
        Integer freeNum = 0;
        if (Objects.nonNull(miniAppMiniDrama.getFreeNum())) {
            freeNum = miniAppMiniDrama.getFreeNum();
        }
        List<String> tagNameList = new ArrayList<>();
        List<MiniAppDuanjuTagRelate> tagRelates = tagRelateService.lambdaQuery().eq(MiniAppDuanjuTagRelate::getDramaId, miniAppMiniDrama.getId()).list();
        if (CollectionUtils.isNotEmpty(tagRelates)) {
            tagNameList = tagRelates.stream().map(MiniAppDuanjuTagRelate::getTagName).collect(Collectors.toList());
        }

        List<MiniAppDramaEpisodeResponse> episodes = episodeService.getByAlbumId(albumId);
        UserRightVO userRight = duanJuUserService.getUserRight(currentUserId, albumId);
        for (MiniAppDramaEpisodeResponse episode : episodes) {
            episode.setLockStatus(1);
            episode.setHasPermissionView(false);
            episode.setTagNameList(tagNameList);
            if (freeNum > 0 && episode.getEpisodeSeq() < freeNum) {
                episode.setHasPermissionView(true);
                episode.setPermissionType(1);
                episode.setLockStatus(0);
            } else if (Objects.equals(userRight.getMembershipStatus(), 1)) {
                episode.setHasPermissionView(Boolean.TRUE);
                episode.setPermissionType(2);
                episode.setLockStatus(2);
            } else {
                if (CollectionUtil.isNotEmpty(userRight.getAlbumIdList())) {
                    boolean contains = userRight.getAlbumIdList().contains(albumId);
                    if (contains) {
                        episode.setHasPermissionView(true);
                        episode.setPermissionType(3);
                        episode.setLockStatus(2);
                    }
                } else if (CollectionUtil.isNotEmpty(userRight.getEpisodeIdList())) {
                    boolean contains = userRight.getEpisodeIdList().contains(episode.getDouYinEpisodeId());
                    if (contains) {
                        episode.setHasPermissionView(true);
                        episode.setPermissionType(4);
                        episode.setLockStatus(2);
                    }
                }
            }
        }
        return Result.ok(episodes);
    }

    @GetMapping("/syncEpisode")
    public Result<?> syncEpisode() {
        List<String> recordIdList = episodeService.getByVideoRecordId();
        for (String id : recordIdList) {
            episodeService.syncOpenApiUploadVideoRecord(id);
        }
        return Result.ok(recordIdList);
    }

    /**
     * 获取栏目
     */
    @GetMapping("/listColumn")
    public Result<Object> listByColumnId(@RequestParam(required = false) String columnId) {
        return Result.ok(miniAppDramaColumnService.listColumnById(columnId));
    }

    /**
     * 获取banner
     */
    @GetMapping("/listBanner")
    public Result<Object> listBanner(@RequestParam(required = false) String columnId) {
        List<DuanJuBanner> list = duanJuBannerService.lambdaQuery().eq(DuanJuBanner::getStatus, 1)
                .orderByAsc(DuanJuBanner::getOrderBy)
                .orderByDesc(DuanJuBanner::getCreateTime)
                .list();
        return Result.ok(list);
    }

    @Resource
    MobileRegionService mobileRegionService;

    /**
     * 获取套餐列表
     */
    @GetMapping("/listPackage")
    public Result<Object> listPackage(
            @RequestParam(required = false) String model,
            @RequestParam(required = false) String types,
            @RequestParam(required = false) String resource,
            @RequestParam(required = false) String excludePhoneSub,
            @RequestParam(required = false) String mobile,
            @RequestParam(required = false) String version,
            @RequestParam(required = false) String userId) {

        log.info("根据模板获取套餐列表,  渠道: {}, 用户id: {},手机号:{}", model, userId, mobile);
        if (StringUtils.isEmpty(version) || !StringUtils.equals("1.2", version)) {
            List<Integer> typeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(types)) {
                typeList = Arrays.stream(types.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            }
            // 根据手机号获取归属地
            String provinceName = getProvinceName(mobile);
            List<DuanJuPackage> list = duanJuPackageService.listPackageByProvince(provinceName, model, typeList);
            return Result.ok(list);
        }
        if (StringUtils.equals("1.2", version)) {
            MiniAppAdvertisement advertisement = miniAppAdvertisementService.lambdaQuery().eq(MiniAppAdvertisement::getUserId, userId)
                    .last("limit 1").orderByDesc(MiniAppAdvertisement::getCreateTime).one();
            String templateId = "";
            if (Objects.isNull(advertisement)) {
                if (StringUtils.isEmpty(templateId)) {
                    DouyinAppConfig appConfig = douyinAppConfigService.getByBusinessType(resource);
                    templateId = appConfig.getPackageTemplateId();
                }
            } else {
                templateId = advertisement.getPanelId();
            }
            MiniAppPayTemplate template = payTemplateService.getById(templateId);
            if (Objects.isNull(template)) {
                Result.error("充值模板不存在");
            }
            Integer status = template.getStatus();

            if (!Objects.equals(status, 1)) {
                return Result.error("充值模板已下架");
            }
            List<MiniAppPayTemplatePackageRelate> packageRelates = packageRelateService.lambdaQuery().eq(MiniAppPayTemplatePackageRelate::getTemplateId, templateId).list();
            if (CollectionUtils.isEmpty(packageRelates)) {
                return Result.error("充值模板未关联充值模板配置");
            }
            List<String> packageIds = packageRelates.stream().map(MiniAppPayTemplatePackageRelate::getPackageId).collect(Collectors.toList());
            List<Integer> typeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(types)) {
                typeList = Arrays.stream(types.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            }
            List<DuanJuPackage> packageList = duanJuPackageService.lambdaQuery().in(DuanJuPackage::getId, packageIds)
                    .like(StringUtils.isNotEmpty(model), DuanJuPackage::getModel, model)
                    .in(CollectionUtil.isNotEmpty(typeList), DuanJuPackage::getType, typeList).notIn(StringUtils.equals(excludePhoneSub, "1"), DuanJuPackage::getPayType, 1).list();
            return Result.ok(packageList);
        }
        return Result.ok();
    }

    private String getProvinceName(String mobile) {
        String provinceName = "";
        try {
            if (StringUtils.isNotEmpty(mobile)) {
                MobileRegionResult mobileRegionResult = mobileRegionService.queryMobile(mobile);
                if (mobileRegionResult.isSuccess()) {
                    provinceName = mobileRegionResult.getProvince();
                }
            }
        } catch (Exception e) {
            log.error("获取归属地失败", e);
        }
        return provinceName;
    }

    private MobileRegionResult getMobileRegionResult(String mobile) {
        try {
            if (StringUtils.isNotEmpty(mobile)) {
                MobileRegionResult mobileRegionResult = mobileRegionService.queryMobile(mobile);
                if (mobileRegionResult.isSuccess()) {
                    return mobileRegionResult;
                }
            }
        } catch (Exception e) {
            log.error("获取归属地失败", e);
        }
        return null;
    }


    @Resource
    private IMiniAppPayTemplateService payTemplateService;

    @Resource
    IMiniAppPayTemplatePackageRelateService packageRelateService;


    @Resource
    AppMobilePayLinkConfigClient appMobilePayLinkConfigClient;


    @Resource
    IDouyinAppConfigService douyinAppConfigService;
    @Resource
    IMiniAppAdvertisementService miniAppAdvertisementService;

    @Resource
    PayChannelService payChannelService;
    @ApiOperation("获取计费代码跳转链接")
    @GetMapping("getPayChannelLink")
    public Result<?> getPayChannelLink(@RequestParam(required = false) String mobile, String resource, String userId) {
        log.info("获取计费代码跳转链接，手机号: {},  资源: {}, 用户id: {}", mobile, resource, userId);

        try {
            MiniAppAdvertisement advertisement = miniAppAdvertisementService.lambdaQuery().eq(MiniAppAdvertisement::getUserId, userId).last("limit 1").orderByDesc(MiniAppAdvertisement::getCreateTime).one();
            String templateId = "";
            if (Objects.isNull(advertisement)) {
                if (StringUtils.isEmpty(templateId)) {
                    DouyinAppConfig appConfig = douyinAppConfigService.getByBusinessType(resource);
                    templateId = appConfig.getPackageTemplateId();
                }
            } else {
                templateId = advertisement.getPanelId();
            }

            //获取模板信息
            MiniAppPayTemplate template = payTemplateService.getById(templateId);
            if (Objects.isNull(template)) {
                log.info("充值模板不存在");
                return Result.ok(Collections.EMPTY_LIST);
            }

            Integer status = template.getStatus();
            if (!Objects.equals(status, 1)) {
                log.info("充值模板已下架");
                return Result.ok(Collections.EMPTY_LIST);

            }

            //获取模板管理的套餐列表
            List<MiniAppPayTemplatePackageRelate> packageRelates = packageRelateService.lambdaQuery().eq(MiniAppPayTemplatePackageRelate::getTemplateId, templateId).list();
            if (CollectionUtils.isEmpty(packageRelates)) {
                log.info("充值模板未关联充值模板配置");
                return Result.ok(Collections.EMPTY_LIST);
            }
            List<String> packageIds = packageRelates.stream().map(MiniAppPayTemplatePackageRelate::getPackageId).collect(Collectors.toList());
            List<DuanJuPackage> duanJuPackages = duanJuPackageService.lambdaQuery().in(DuanJuPackage::getId, packageIds).eq(DuanJuPackage::getIsDeleted, 0).eq(DuanJuPackage::getStatus, 1).orderByAsc(DuanJuPackage::getPayLinkConfigOrderNo).list();

            if (CollectionUtils.isEmpty(duanJuPackages)) {
                log.info("未找到对应的套餐");
                return Result.ok(Collections.EMPTY_LIST);

            }


            // 根据套餐配置的计费代码配置主键  匹配手机支付计费代码
            List<String> configIds = duanJuPackages.stream().map(DuanJuPackage::getAppMobilePayLinkConfigId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

            //查询手机号的省份和运营商
            MobileRegionResult mobileRegionResult = getMobileRegionResult(mobile);

            //匹配计费代码
            List<AppMobilePayChannelConfig> appMobilePayChannelConfigs = appMobilePayLinkConfigClient.filterPayConfig(mobileRegionResult.getOperator(), mobileRegionResult.getProvince(), configIds);
            if (CollectionUtil.isEmpty(appMobilePayChannelConfigs)) {
                log.info("未匹配到对应渠道");
                return Result.ok(Collections.EMPTY_LIST);

            }
            List<DuanJuPackage> result = new ArrayList<>();
            List<String> channelList = appMobilePayChannelConfigs.stream().map(AppMobilePayChannelConfig::getChannel).collect(Collectors.toList());

            //获取当前app的计费代码配置
            if (CollectionUtil.isNotEmpty(appMobilePayChannelConfigs)) {

                //前置校验  获取能开通的渠道
                JSONObject canSubChannel = payChannelService.getCanSubChannel(mobile, channelList);

                //获取渠道开通的状态
                List<PayChannelService.ChannelSubResult> channelSubResults = payChannelService.queryAllServiceByMobile(mobile, channelList);
                Map<String, String> subChannelStatus = channelSubResults.stream().collect(Collectors.toMap(PayChannelService.ChannelSubResult::getChannel, PayChannelService.ChannelSubResult::getOrderStatus));
                Map<String, AppMobilePayChannelConfig> mobilePayChannelConfigMap = appMobilePayChannelConfigs.stream().collect(Collectors.toMap(AppMobilePayChannelConfig::getId, Function.identity()));

                //根据优先级 匹配渠道号
                for (DuanJuPackage duanJuPackage : duanJuPackages) {

                    //根据payChannel主键获取渠道号编码
                    AppMobilePayChannelConfig appMobilePayChannelConfig = mobilePayChannelConfigMap.get(duanJuPackage.getAppMobilePayLinkConfigId());
                    if (Objects.isNull(appMobilePayChannelConfig)) {
                        continue;
                    }
                    //通过计费代码能否开通
                    JSONObject channelJSon = canSubChannel.getJSONObject(appMobilePayChannelConfig.getChannel());

                    Integer code = channelJSon.getInteger("code");

                    String subStatus = subChannelStatus.get(appMobilePayChannelConfig.getChannel());
                    //能开通且没开通过
                    if (Objects.equals(code, 200) && !StringUtils.equals(subStatus, "1")) {
                        Result<?> configByAppNameAndChannel = appMobilePayLinkConfigClient.queryLinkByChannel(resource, appMobilePayChannelConfig.getChannel(), duanJuPackage.getPageType());
                        JSONObject tempJson = JSONObject.parseObject(configByAppNameAndChannel.getResult().toString());
                        duanJuPackage.setOrderPayLink(tempJson.getString("promotionalLink"));
                        result.add(duanJuPackage);
                    }
                }
                return Result.ok(result);
            }
        } catch (Exception e) {
            log.info("获取计费代码跳转链接异常，手机号: {},  资源: {}, 用户id: {}", mobile, resource, userId, e);
        }
        return Result.ok(Collections.EMPTY_LIST);
    }

    /**
     * 获取套餐
     */
    @GetMapping("/getPackage")
    public Result<Object> getPackage(@RequestParam(required = false) String mobile,
                                     @RequestParam(required = false) String model,
                                     @RequestParam(required = false) String types
    ) {
        String provinceName = getProvinceName(mobile);

        List<Integer> typeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(types)) {
            typeList = Arrays.stream(types.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }
        List<DuanJuPackage> list = duanJuPackageService.getPackageByProvince(provinceName, model, typeList);
        if (CollectionUtil.isNotEmpty(list)) {
            return Result.ok(list.get(0));
        }
        return Result.ok();
    }


    @GetMapping("/getOpenId")
    public Result<?> getOpenId(String code, String resource) {
        OpenApiRes openApiRes = douYinHttpUtil.getOpenId(code, resource);
        if (!openApiRes.isSuccess()) {
            return Result.error(openApiRes.getErrMsg());
        }
        OpenApiRes.DouYinSessionRes result = openApiRes.getDouYinSessionRes();
        redisUtil.set(DuanjuConstant.douYin_Session_redis_key + result.getSessionKey(), result);
        return Result.ok(openApiRes.getDouYinSessionRes());
    }

    @Resource
    private IDouyinAppConfigService douYinAppConfigService;

    @GetMapping("/changePhoneNumber")
    @TokenRequired
    public Result<?> changePhoneNumber(String phone) {
        Integer currentUserId = getCurrentUserId();
        if (StringUtils.isEmpty(phone)) {
            return Result.ok("手机号不能为空");
        }
        DuanJuUser update = new DuanJuUser();
        update.setMobile(phone);
        update.setId(currentUserId);
        duanJuUserService.updateById(update);
        return Result.ok("更换完成");
    }

    @GetMapping("/getPhoneNumberInfo")
    @TokenRequired
    public Result<?> getPhoneNumberInfo(String code, String resource) {
        OpenApiRes openApiRes = douYinHttpUtil.getPhoneNumberInfo(code, resource);
        if (!openApiRes.isSuccess()) {
            return Result.error(openApiRes.getErrMsg());
        }
        Integer uid = getCurrentUserId();
        String data = openApiRes.getData();
        DouyinAppConfig config = douYinAppConfigService.getByBusinessType(resource);
        String privateKey = config.getPrivateKey();
        try {
            String decrypt = DouYinHttpUtil.decrypt(data, privateKey);
            JSONObject jsonObject = JSONObject.parseObject(decrypt);
            String mobile = jsonObject.getString("phoneNumber");
            if (StringUtils.isNotEmpty(mobile)) {
                DuanJuUser user = new DuanJuUser();
                user.setMobile(mobile);
                user.setId(uid);
                duanJuUserService.updateById(user);
            }
            return Result.ok(JSONObject.parseObject(decrypt));
        } catch (Exception e) {
            log.error("获取手机号失败", e);
            return Result.error("获取手机号失败");
        }
    }

    @ApiOperation("抖音解密手机号")
    @GetMapping("/encryptedPhone")
    public Result<Object> fastLogin(@RequestParam String encryptedData, @RequestParam String iv, @RequestParam String sessionKey) {
        log.info("解密手机号:encryptedData:{},iv:{},sessionKey:{}", encryptedData, iv, sessionKey);
        return Result.ok(DouYinAPIUtil.exchangePhone(encryptedData, iv, sessionKey));
    }


    @TokenRequired
    @GetMapping("/queryCoupon")
    public Result<Object> queryCoupon(String openId) {
        log.info("查询优惠券入参: openId: {}", openId);
        DuanJuUser currentUser = getCurrentUser();
        OpenApiRes openApiRes = douYinHttpUtil.queryCoupon(openId, currentUser.getResource());
        if (!openApiRes.isSuccess()) {
            return Result.error(openApiRes.getErrMsg());
        }
        return Result.ok(openApiRes.getCouponReceiveResult());
    }


    @PostMapping("couponCallBack")
    public Result<?> couponCallBack(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyJson = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("用户领券结果回调通知: {}", notifyJson);
            miniAppCouponRecordService.add(notifyJson);
        } catch (Exception e) {
            log.error("用户领券结果回调通知异常", e);
        }
        return Result.ok();
    }

    @TokenRequired
    @GetMapping("/queryBindUser")
    public Result<?> queryBindUser(String activityId, String openId) {
        log.info("查询绑定用户入参: activityId: {}, openId: {}", activityId, openId);
        DuanJuUser currentUser = getCurrentUser();
        OpenApiRes openApiRes = douYinHttpUtil.queryBindUser(activityId, openId, currentUser.getResource());
        return openApiRes.isSuccess() ? Result.ok(openApiRes.getData()) : Result.error(openApiRes.getErrMsg());
    }

    @TokenRequired
    @GetMapping("/consumeCoupon")
    public Result<?> consumeCoupon(String couponId, String openId, String albumId) {
        log.info("用户核销入参: couponId: {}, openId: {}", couponId, openId);
        try {
            Integer currentUserId = getCurrentUserId();
            MiniAppMiniDrama drama = miniAppMiniDramaService.getByAlbumId(albumId);
            if (Objects.isNull(drama)) {
                return Result.error("剧目不存在");
            }
            Integer freeNum = drama.getFreeNum();
            if (Objects.isNull(freeNum)) {
                freeNum = 1;
            }
            MiniAppDramaEpisode miniAppDramaEpisode = episodeService.lambdaQuery()
                    .eq(MiniAppDramaEpisode::getIsDeleted, 0)
                    .eq(MiniAppDramaEpisode::getAlbumId, albumId)
                    .eq(MiniAppDramaEpisode::getEpisodeSeq, freeNum).last("limit 1").one();
            if (Objects.isNull(miniAppDramaEpisode)) {
                return Result.error("该剧集不存在");
            }
            String douYinEpisodeId = miniAppDramaEpisode.getDouYinEpisodeId();
            miniAppCouponRecordService.consumeCoupon(couponId, currentUserId, douYinEpisodeId);
        } catch (Exception e) {
            log.info("用户核销异常: {}", e.getMessage());
            return Result.error("核销异常");
        }

        return Result.ok("核销成功");
    }

    @GetMapping("/rollbackConsumeCoupon")
    public Result<?> rollbackConsumeCoupon(String couponId, String openId) {
        log.info("用户撤销核销入参: couponId: {}, openId: {}", couponId, openId);
        miniAppCouponRecordService.rollbackConsumeCoupon(couponId, openId);
        return Result.ok();
    }


    @Resource
    DuanJuAdReportService duanJuAdReportService;


    @PostMapping("/adCallBack")
    public Result<?> adCallBack(HttpServletRequest request) {
        try (ServletInputStream inputStream = request.getInputStream()) {
            String notifyJson = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            Integer currentUserId = getCurrentUserId();
            log.info("短剧广告回调: {}", notifyJson);
            MiniAppAdvertisement advertisement = JSONObject.parseObject(notifyJson, MiniAppAdvertisement.class);
            if (Objects.nonNull(currentUserId)) {
                advertisement.setUserId(String.valueOf(currentUserId));
            }
            advertisement.setParam(notifyJson);
            duanJuAdReportService.sendAppReportMqMessage(advertisement, request);
        } catch (Exception e) {
            log.error("短剧广告回调通知异常", e);
        }
        return Result.ok();
    }

    @Resource
    private INasFileRecordService fileRecordService;

    @ApiOperation(value = "同步nas记录到内容库视频记录", notes = "同步nas记录到内容库视频记录")
    @GetMapping("/syncNasRecordOpenVideo")
    @Transactional
    public Result<?> syncNasRecordOpenVideo(@RequestParam(required = false) String ids, @RequestParam(required = false) String scanBatch) {
        List<NasFileRecord> list = null;
        if (StringUtils.isNotEmpty(scanBatch)) {
            list = fileRecordService.lambdaQuery().eq(NasFileRecord::getScanBatch, scanBatch).list();
        } else if (StringUtils.isNotEmpty(ids)) {
            String[] split = ids.split(",");
            list = fileRecordService.lambdaQuery().in(NasFileRecord::getId, split).list();
        } else {
            return Result.error("参数错误");
        }

        List<String> nameList = list.stream().map(NasFileRecord::getParentPathName).distinct().collect(Collectors.toList());

        List<OpenApiAlbumInfo> albumInfoList = openApiAlbumInfoService.lambdaQuery().in(OpenApiAlbumInfo::getNickName, nameList).list();
        Map<String, List<OpenApiAlbumInfo>> openAlbumInfo = albumInfoList.stream().collect(Collectors.groupingBy(OpenApiAlbumInfo::getNickName));

        for (NasFileRecord record : list) {
            try {
                OpenApiUploadVideoRecord apiUploadVideoRecord = new OpenApiUploadVideoRecord();
                String videoPath = record.getFullPath();
                String name = record.getName();  //文件名
                String parentPathName = record.getParentPathName();
                String seq;
                if (!videoPath.contains("修复剧集")) {
                    seq = extractNumbers(name);
                } else {
                    String[] split1 = name.split("-");
                    seq = split1[0];
                }
                List<OpenApiAlbumInfo> openApiAlbumInfos = openAlbumInfo.get(parentPathName);

                if (CollectionUtils.isNotEmpty(openApiAlbumInfos)) {
                    OpenApiAlbumInfo apiAlbumInfo = openApiAlbumInfos.get(0);
                    apiUploadVideoRecord.setAlbumId(apiAlbumInfo.getAlbumId());
                }
                String resourceVideoUrl = String.format("http://nasmedia.kunpengtn.com:15073/download/shortplaybook/6FXmK5tSOmWRoo3zizttu7JBdGiMPWRG" + videoPath);
                resourceVideoUrl = resourceVideoUrl.replace("/短剧图书内容", "");
                apiUploadVideoRecord.setResourceVideoUrl(resourceVideoUrl);
                apiUploadVideoRecord.setTitle(parentPathName + "-第" + seq + "集");
                apiUploadVideoRecord.setDescription(parentPathName + "-第" + seq + "集");
                apiUploadVideoRecord.setAlbumName(parentPathName);
                apiUploadVideoRecord.setStatus(1);
                apiUploadVideoRecord.setResourceType(2);
                apiUploadVideoRecord.setSeq(Integer.valueOf(seq));
                uploadVideoRecordService.save(apiUploadVideoRecord);
            } catch (NumberFormatException e) {
                log.info("赋值异常", e);
            }
        }
        return Result.ok();
    }


    public static String extractNumbers(String input) {
        input = input.replace("mp4", "");
        input = input.replace("MP4", "");
        // 创建一个Pattern对象，用于匹配数字
        Pattern pattern = Pattern.compile("\\d+");
        // 创建Matcher对象
        Matcher matcher = pattern.matcher(input);
        // 创建一个StringBuilder对象，用于存储匹配到的数字
        StringBuilder numbers = new StringBuilder();
        // 查找所有匹配的数字
        while (matcher.find()) {
            // 将匹配到的数字添加到StringBuilder中
            numbers.append(matcher.group());
        }
        // 返回包含所有数字的字符串
        return numbers.toString();
    }

    @GetMapping("/syncOpenPic")
    public Result<?> syncOpenPic() {
        List<OpenApiAlbumInfo> albumInfoList = openApiAlbumInfoService.lambdaQuery().isNull(OpenApiAlbumInfo::getCoverList).list();
        List<String> infoIds = albumInfoList.stream().map(OpenApiAlbumInfo::getId).collect(Collectors.toList());
        List<MiniAppMiniDrama> list = miniAppMiniDramaService.lambdaQuery().in(MiniAppMiniDrama::getAlbumInfoId, infoIds).list();
        for (MiniAppMiniDrama albumInfo : list) {
            if (StringUtils.isNotEmpty(albumInfo.getCoverUrl())) {
                OpenApiUploadPicRecord apiUploadPicRecord = new OpenApiUploadPicRecord();
                apiUploadPicRecord.setDescription(albumInfo.getName() + "-封面图");
                apiUploadPicRecord.setTitle(albumInfo.getName() + "-封面图");
                apiUploadPicRecord.setResourceType(2);
                apiUploadPicRecord.setPicUrl(albumInfo.getCoverUrl());
                uploadPicRecordService.save(apiUploadPicRecord);
                uploadPic(apiUploadPicRecord.getId());
                OpenApiRes openApiRes = douYinHttpUtil.uploadPic(albumInfo.getCoverUrl());
                if (openApiRes.isSuccess()) {
                    String openPicId = openApiRes.getOpenPicId();
                    OpenApiUploadPicRecord update = new OpenApiUploadPicRecord();
                    update.setId(apiUploadPicRecord.getId());
                    update.setOpenPicId(openPicId);
                    update.setStatus(3);
                    uploadPicRecordService.updateById(update);

                    String albumInfoId = albumInfo.getAlbumInfoId();
                    OpenApiAlbumInfo apiAlbumInfo = new OpenApiAlbumInfo();
                    apiAlbumInfo.setId(albumInfoId);
                    apiAlbumInfo.setCoverList(openPicId);
                    openApiAlbumInfoService.updateById(apiAlbumInfo);
                }
            }
        }
        return Result.ok();
    }

    @GetMapping("/syncCostUri")
    public Result<?> syncCostUri() {
        List<OpenApiAlbumInfo> albumInfoList = openApiAlbumInfoService.lambdaQuery().isNull(OpenApiAlbumInfo::getCostDistributionUri).isNotNull(OpenApiAlbumInfo::getCostUrl).list();
        for (OpenApiAlbumInfo albumInfo : albumInfoList) {
            if (StringUtils.isNotEmpty(albumInfo.getCostUrl())) {
                OpenApiUploadPicRecord apiUploadPicRecord = new OpenApiUploadPicRecord();
                apiUploadPicRecord.setDescription(albumInfo.getName() + "-成本配置比例");
                apiUploadPicRecord.setTitle(albumInfo.getName() + "-成本配置比例");
                apiUploadPicRecord.setResourceType(2);
                apiUploadPicRecord.setPicUrl(albumInfo.getCostUrl());
                uploadPicRecordService.save(apiUploadPicRecord);
                uploadPic(apiUploadPicRecord.getId());
                OpenApiRes openApiRes = douYinHttpUtil.uploadPic(albumInfo.getCostUrl());
                if (openApiRes.isSuccess()) {
                    String openPicId = openApiRes.getOpenPicId();
                    OpenApiUploadPicRecord update = new OpenApiUploadPicRecord();
                    update.setId(apiUploadPicRecord.getId());
                    update.setOpenPicId(openPicId);
                    update.setStatus(3);
                    uploadPicRecordService.updateById(update);

                    OpenApiAlbumInfo apiAlbumInfo = new OpenApiAlbumInfo();
                    apiAlbumInfo.setId(albumInfo.getId());
                    apiAlbumInfo.setCostDistributionUri(openPicId);
                    openApiAlbumInfoService.updateById(apiAlbumInfo);
                }
            }
        }
        return Result.ok();
    }

    @Resource
    IUserMembershipService userMembershipService;

    @GetMapping("/updateUser/{id}")
    public Result<?> updateUser(@PathVariable String id) {
        userMembershipService.updateUserMemberInfo(id);
        return Result.ok();
    }
}
