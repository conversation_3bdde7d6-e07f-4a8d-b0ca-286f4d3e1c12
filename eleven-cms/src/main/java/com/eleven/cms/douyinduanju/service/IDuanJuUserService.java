package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.dto.UserRightVO;
import com.eleven.cms.douyinduanju.entity.DuanJuUser;

/**
 * @Description: duan_ju_user
 * @Author: jeecg-boot
 * @Date: 2025-06-05
 * @Version: V1.0
 */
public interface IDuanJuUserService extends IService<DuanJuUser> {

    /**
     * 用户登录，如果用户不存在则创建新用户
     *
     * @param openId   抖音openId
     * @param nickName 用户昵称（可选）
     * @param mobile   手机号（可选）
     * @param source   用户来源（可选）
     * @return 用户信息
     */
    DuanJuUser loginOrRegister(String openId, String nickName, String mobile, String source, String subChannel, String resource);

    /**
     * 获取用户权益
     *
     * @param userId  用户id
     * @param albumId 抖音剧目id
     * @return 用户权益
     */
    UserRightVO getUserRight(Integer userId, String albumId);


    void validateChannelSub(Integer userId);

}
