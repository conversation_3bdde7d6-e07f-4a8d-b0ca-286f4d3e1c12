package com.eleven.cms.douyinduanju.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.douyinduanju.entity.NasFileRecord;
import com.eleven.cms.douyinduanju.mapper.NasFileRecordMapper;
import com.eleven.cms.douyinduanju.service.INasFileRecordService;
import com.eleven.cms.douyinduanju.util.NasFileReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * NAS文件记录服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
@Slf4j
public class NasFileRecordServiceImpl extends ServiceImpl<NasFileRecordMapper, NasFileRecord> implements INasFileRecordService {

    @Autowired
    private NasFileReader nasFileReader;

    /**
     * NAS服务器路径
     */
    private static final String NAS_BASE_PATH = "\\\\*************\\";

    /**
     * 批量插入大小
     */
    private static final int BATCH_SIZE = 1000;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NasScanResult scanAndSaveNasDirectory(String nasPath, List<String> includeParentName) {
        long startTime = System.currentTimeMillis();
        String scanBatch = generateScanBatch();

        log.info("开始扫描NAS目录，路径: {}, 扫描批次: {}", nasPath, scanBatch);

        try {
            // 验证路径
            if (nasPath == null || nasPath.trim().isEmpty()) {
                nasPath = NAS_BASE_PATH;
            }


            // 扫描文件和目录
            ScanStatistics stats = new ScanStatistics();
            List<NasFileRecord> allRecords = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(includeParentName)) {
                nasPath = NAS_BASE_PATH + nasPath;
                for (String name : includeParentName) {
                    String tempPath = nasPath + "/" + name;
                    Path startPath = Paths.get(tempPath);
                    if (!Files.exists(startPath)) {
                        log.info("路径不存在: {}", tempPath);
                        continue;
                    }
                    scanDirectory(startPath, "", 0, scanBatch, allRecords, stats);
                }
            }
            // 批量保存到数据库
            boolean saveResult = batchSaveRecords(allRecords);
            if (!saveResult) {
                return NasScanResult.error("保存文件记录到数据库失败");
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("NAS目录扫描完成，批次: {}, 总数: {}, 目录: {}, 文件: {}, 总大小: {} bytes, 耗时: {} ms",
                    scanBatch, stats.totalCount, stats.directoryCount, stats.fileCount, stats.totalSize, duration);

            return NasScanResult.success(scanBatch, stats.totalCount, stats.directoryCount, stats.fileCount, stats.totalSize, duration);

        } catch (Exception e) {
            log.error("扫描NAS目录异常，路径: {}, 批次: {}", nasPath, scanBatch, e);
            return NasScanResult.error("扫描NAS目录失败: " + e.getMessage());
        }
    }

    /**
     * 递归扫描目录
     */
    private void scanDirectory(Path currentPath, String relativePath, int level, String scanBatch,
                               List<NasFileRecord> records, ScanStatistics stats) throws IOException {

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(currentPath)) {
            for (Path path : stream) {
                try {
                    BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);

                    // 创建文件记录
                    NasFileRecord record = createFileRecord(path, relativePath, level, scanBatch, attrs);
                    records.add(record);

                    // 更新统计信息
                    stats.totalCount++;
                    if (attrs.isDirectory()) {
                        stats.directoryCount++;

                        // 递归扫描子目录
                        String subRelativePath = relativePath.isEmpty() ?
                                path.getFileName().toString() :
                                relativePath + File.separator + path.getFileName().toString();

                        scanDirectory(path, subRelativePath, level + 1, scanBatch, records, stats);

                    } else if (attrs.isRegularFile()) {
                        stats.fileCount++;
                        stats.totalSize += attrs.size();
                    }

                    // 批量保存（避免内存溢出）
                    if (records.size() >= BATCH_SIZE) {
                        batchSaveRecords(new ArrayList<>(records));
                        records.clear();
                        log.debug("批量保存 {} 条记录", BATCH_SIZE);
                    }

                } catch (Exception e) {
                    log.warn("处理文件失败，跳过: {}, 错误: {}", path, e.getMessage());
                }
            }
        }
    }

    /**
     * 创建文件记录
     */
    private NasFileRecord createFileRecord(Path path, String relativePath, int level, String scanBatch, BasicFileAttributes attrs) {
        NasFileRecord record = new NasFileRecord();

        // 基本信息
        record.setName(path.getFileName().toString());

        // 路径规范化处理
        String fullPath = path.toString();
        fullPath = fullPath.replace("\\\\", "/").replace("\\", "/");
        fullPath = fullPath.replace("*************", ""); // 移除IP地址部分
        fullPath = fullPath.replaceAll("/+", "/"); // 新的多斜杠处理逻辑，确保只保留单个斜杠

        record.setFullPath(fullPath);
        record.setRelativePath(relativePath.isEmpty() ?
                path.getFileName().toString() :
                relativePath + File.separator + path.getFileName().toString());

        String parentPath = path.getParent() != null ? path.getParent().toString() : "";

        record.setParentPath(parentPath);

        String[] split = parentPath.split("\\\\");
        record.setParentPathName(split.length > 0 ? split[split.length - 1] : "");

        record.setLevel(level);

        // 文件类型和大小
        if (attrs.isDirectory()) {
            record.setFileType("directory");
            record.setFileSize(0L);
            record.setExtension("");
        } else {
            record.setFileType("file");
            record.setFileSize(attrs.size());
            record.setExtension(getFileExtension(record.getName()));
        }

        // 时间信息
        record.setLastModified(new Date(attrs.lastModifiedTime().toMillis()));
        record.setScanBatch(scanBatch);
        record.setScanTime(new Date());
        record.setScanTime(new Date());

        record.setParentPathName(path.getParent() != null ? path.getParent().getFileName().toString() : "");

        // 权限信息
        record.setReadable(Files.isReadable(path) ? 1 : 0);
        record.setWritable(Files.isWritable(path) ? 1 : 0);

        // 系统字段
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setCreateBy("system");
        record.setUpdateBy("system");
        record.setIsDeleted(0);

        return record;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }

        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot).toLowerCase() : "";
    }

    /**
     * 生成扫描批次号
     */
    private String generateScanBatch() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return "SCAN_" + sdf.format(new Date()) + "_" + System.currentTimeMillis() % 10000;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveRecords(List<NasFileRecord> records) {
        if (records == null || records.isEmpty()) {
            return true;
        }

        try {
            // 分批保存
            for (int i = 0; i < records.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, records.size());
                List<NasFileRecord> batch = records.subList(i, endIndex);

                boolean result = saveBatch(batch);
                if (!result) {
                    log.error("批量保存文件记录失败，批次范围: {} - {}", i, endIndex);
                    return false;
                }
            }

            log.info("批量保存文件记录成功，总数: {}", records.size());
            return true;

        } catch (Exception e) {
            log.error("批量保存文件记录异常", e);
            return false;
        }
    }

    @Override
    public int deleteByScanBatch(String scanBatch) {
        try {
            int count = baseMapper.deleteByScanBatch(scanBatch);
            log.info("根据扫描批次删除记录成功，批次: {}, 删除数量: {}", scanBatch, count);
            return count;
        } catch (Exception e) {
            log.error("根据扫描批次删除记录失败，批次: {}", scanBatch, e);
            return 0;
        }
    }

    @Override
    public int deleteByPathPrefix(String pathPrefix) {
        try {
            int count = baseMapper.deleteByPathPrefix(pathPrefix);
            log.info("根据路径前缀删除记录成功，前缀: {}, 删除数量: {}", pathPrefix, count);
            return count;
        } catch (Exception e) {
            log.error("根据路径前缀删除记录失败，前缀: {}", pathPrefix, e);
            return 0;
        }
    }

    /**
     * 扫描统计信息
     */
    private static class ScanStatistics {
        int totalCount = 0;
        int directoryCount = 0;
        int fileCount = 0;
        long totalSize = 0L;
    }
}
