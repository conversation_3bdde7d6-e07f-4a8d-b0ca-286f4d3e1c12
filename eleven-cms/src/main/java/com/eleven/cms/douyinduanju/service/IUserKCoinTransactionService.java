package com.eleven.cms.douyinduanju.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.douyinduanju.entity.UserKCoinTransaction;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户K币交易记录服务接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IUserKCoinTransactionService extends IService<UserKCoinTransaction> {

    /**
     * 创建交易记录
     *
     * @param userId          用户ID
     * @param transactionType 交易类型
     * @param amount          交易金额
     * @param balanceBefore   交易前余额
     * @param balanceAfter    交易后余额
     * @param description     交易描述
     * @param businessType    业务类型
     * @param businessId      业务ID
     * @param orderId         订单ID
     * @return 交易记录
     */
    UserKCoinTransaction createTransaction(String userId, Integer transactionType, BigDecimal amount,
                                           BigDecimal balanceBefore, BigDecimal balanceAfter, String description,
                                           Integer businessType, String businessId, String orderId);

    /**
     * 根据用户ID获取交易记录
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 交易记录列表
     */
    List<UserKCoinTransaction> getTransactionsByUserId(String userId, Integer limit);

    /**
     * 根据订单ID获取交易记录
     *
     * @param orderId 订单ID
     * @return 交易记录列表
     */
    List<UserKCoinTransaction> getTransactionsByOrderId(String orderId);
}
