package com.eleven.cms.douyinduanju.dto;

import com.eleven.cms.douyinduanju.entity.UserMembership;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户会员状态信息
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class MembershipStatusInfo {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 当前生效的会员
     */
    private UserMembership currentMembership;

    /**
     * 是否有生效的会员
     */
    private boolean hasActiveMembership;

    /**
     * 未生效的会员列表
     */
    private List<UserMembership> inactiveMemberships;
    private List<UserMembership> expiredMemberships;

    /**
     * 未生效会员数量
     */
    private int inactiveMembershipCount;

    /**
     * 总的过期时间（包含未生效会员的时长）
     */
    private Date totalExpireTime;

    /**
     * 剩余天数
     */
    private int remainingDays;

    /**
     * 当前会员类型
     */
    private Integer currentMembershipType;

    /**
     * 当前会员名称
     */
    private String currentMembershipName;

    /**
     * 当前会员过期时间
     */
    private Date currentExpireTime;

    /**
     * 是否即将过期（7天内）
     */
    private boolean expiringSoon;

    /**
     * 总会员时长（天数）
     */
    private int totalDurationDays;

    public MembershipStatusInfo() {
    }

    public MembershipStatusInfo(String userId) {
        this.userId = userId;
    }

    public boolean getHasActiveMembership() {
        return hasActiveMembership;
    }

    /**
     * 获取当前会员类型
     */
    public Integer getCurrentMembershipType() {
        if (currentMembership != null) {
            return currentMembership.getMembershipType();
        }
        return null;
    }

    /**
     * 获取当前会员名称
     */
    public String getCurrentMembershipName() {
        if (currentMembership != null) {
            return currentMembership.getMembershipName();
        }
        return null;
    }

    /**
     * 获取当前会员过期时间
     */
    public Date getCurrentExpireTime() {
        if (currentMembership != null) {
            return currentMembership.getExpireTime();
        }
        return null;
    }

    /**
     * 判断是否即将过期（7天内）
     */
    public boolean isExpiringSoon() {
        if (currentMembership == null || currentMembership.getExpireTime() == null) {
            return false;
        }

        long diffInMillies = currentMembership.getExpireTime().getTime() - new Date().getTime();
        long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
        return diffInDays <= 7 && diffInDays > 0;
    }

    /**
     * 计算总会员时长（天数）
     */
    public int getTotalDurationDays() {
        int totalDays = 0;

        // 当前生效会员的剩余天数
        if (currentMembership != null && currentMembership.getExpireTime() != null) {
            long diffInMillies = currentMembership.getExpireTime().getTime() - new Date().getTime();
            long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
            totalDays += Math.max(0, (int) diffInDays);
        }

        // 未生效会员的总天数
        if (inactiveMemberships != null) {
            for (UserMembership membership : inactiveMemberships) {
                if (membership.getDurationDays() != null) {
                    totalDays += membership.getDurationDays();
                }
            }
        }

        return totalDays;
    }

}
