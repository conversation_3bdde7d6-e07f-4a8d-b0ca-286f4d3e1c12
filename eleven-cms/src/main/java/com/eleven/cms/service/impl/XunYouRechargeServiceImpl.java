package com.eleven.cms.service.impl;

import com.eleven.cms.ad.XunYouApiProperties;
import com.eleven.cms.dto.XunYouQueryResp;
import com.eleven.cms.dto.XunYouRechargeResp;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.service.IXunYouRechargeService;
import com.eleven.cms.util.MD5Util;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @Description: 迅游直充api
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Slf4j
@Service
public class XunYouRechargeServiceImpl implements IXunYouRechargeService {
    private static final String LOG_TAG = "迅游⼿游加速器VIP卡30天API";
    @Autowired
    private XunYouApiProperties xunYouApiProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    private MediaType FORM;
    @Autowired
    private Environment environment;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
        this.FORM=MediaType.parse(org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }


    @Override
    public XunYouRechargeResp recharge(JunboChargeLog junboChargeLog) {
        ObjectNode dataNode =mapper.createObjectNode();
        dataNode.put("id",junboChargeLog.getMiguOrderId());
        dataNode.put("num",1);
        dataNode.put("phoneNum",junboChargeLog.getMobile());
        dataNode.put("productFlag",junboChargeLog.getCouponId());
        dataNode.put("time",System.currentTimeMillis());
        dataNode.put("sign", MD5Util.getXunYouSign(dataNode,xunYouApiProperties.getKey()));
        log.info("{}-直充-手机号:{},请求数据:{}",LOG_TAG,junboChargeLog.getMobile(),dataNode);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        String rechargeVipUrl=xunYouApiProperties.getRechargeVipUrl();
        if(rechargeVipUrl.contains("partnerName")){
            rechargeVipUrl=rechargeVipUrl.replace("partnerName", xunYouApiProperties.getPartnerName());
        }
        Request request = new Request.Builder().url(rechargeVipUrl).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-直充-手机号:{},响应数据:{}",LOG_TAG,junboChargeLog.getMobile(),content);
            final XunYouRechargeResp xunYouRechargeResp = mapper.readValue(content, XunYouRechargeResp.class);
            return xunYouRechargeResp;
        } catch (Exception e) {
            log.error("{}-直充异常-手机号:{},请求数据:{}",LOG_TAG,junboChargeLog.getMobile(),dataNode,e);
            return XunYouRechargeResp.FAIL_RESULT;
        }
    }

    @Override
    public boolean refund(JunboChargeLog junboChargeLog) {
        ObjectNode dataNode =mapper.createObjectNode();
        dataNode.put("orderId",junboChargeLog.getJunboOrderId());
        dataNode.put("status",6);
        dataNode.put("time",System.currentTimeMillis());
        dataNode.put("sign", MD5Util.getXunYouSign(dataNode,xunYouApiProperties.getRefundkey()));
        dataNode.put("reason","退款");
        dataNode.put("ExplorationOrder",false);
        String orderRefundUrl=xunYouApiProperties.getOrderRefundUrl();
        if(orderRefundUrl.contains("orderId")){
            orderRefundUrl=orderRefundUrl.replace("orderId", junboChargeLog.getJunboOrderId());
        }
        log.info("{}-退款-手机号:{},请求数据:{}",LOG_TAG,junboChargeLog.getMobile(),junboChargeLog);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder().url(orderRefundUrl).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-退款-手机号:{},响应数据:{}",LOG_TAG,junboChargeLog.getMobile(),content);
            return true;

        } catch (Exception e) {
            log.error("{}-退款异常-手机号:{},请求数据:{}",LOG_TAG,junboChargeLog.getMobile(),junboChargeLog,e);
            return false;
        }
    }

    @Override
    public XunYouQueryResp query(JunboChargeLog junboChargeLog) {
        String orderQueryUrl=xunYouApiProperties.getOrderQueryUrl();
        if(orderQueryUrl.contains("orderId")){
            orderQueryUrl=orderQueryUrl.replace("orderId", junboChargeLog.getJunboOrderId());
        }
        String md5 ="orderId="+junboChargeLog.getJunboOrderId()+"&key="+xunYouApiProperties.getQueryKey();
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8));
        orderQueryUrl=orderQueryUrl+"?sign="+sign;
        log.info("{}-查询订单详细-手机号:{},请求数据:{}",LOG_TAG,junboChargeLog.getMobile(),junboChargeLog);
        Request request = new Request.Builder().url(orderQueryUrl).get().build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询订单详细-手机号:{},响应数据:{}",LOG_TAG,junboChargeLog.getMobile(),content);
            final XunYouQueryResp xunYouQueryResp = mapper.readValue(content, XunYouQueryResp.class);
            return xunYouQueryResp;
        } catch (Exception e) {
            log.error("{}-查询订单详细异常-手机号:{},请求数据:{}",LOG_TAG,junboChargeLog.getMobile(),junboChargeLog,e);
            return XunYouQueryResp.FAIL_RESULT;
        }
    }
}
