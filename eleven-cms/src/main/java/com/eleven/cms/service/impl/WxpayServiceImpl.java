package com.eleven.cms.service.impl;

import com.eleven.cms.config.WXPayPropertiesConfig;
import com.eleven.cms.config.WeChatConfig;
import com.eleven.cms.config.WechatRSAConfig;
import com.eleven.cms.dto.WechatComplainNotify;
import com.eleven.cms.dto.WechatPayDecodeNotifyParam;
import com.eleven.cms.dto.WxpayNotifyParam;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.entity.WechatCouponConfig;
import com.eleven.cms.service.*;
import com.eleven.cms.util.AESUtils;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.WechatRSADecodeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.wxpay.sdk.WXPay;
import com.github.wxpay.sdk.WXPayConstants;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/7/14 16:03
 * Desc:微信支付
 */
@Slf4j
@Service
public class WxpayServiceImpl implements IWxpayService {

    @Autowired
    WXPayPropertiesConfig wxPayPropertiesConfig;
    @Autowired
    WXPay wxPay;
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    @Autowired
    private Environment environment;

    @Autowired
    WxMpNotifyService wxMpNotifyService;
    @Autowired
    private IWechatCouponConfigService wechatCouponConfigService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public Map<String, String> pay(String outTradeNo, String totalAmount, String subject) throws Exception {

        Map<String, String> data = new HashMap<>();
        data.put("out_trade_no", outTradeNo);
        data.put("body", subject);
        data.put("total_fee", totalAmount);
        data.put("spbill_create_ip", wxPayPropertiesConfig.getSpbillCreateIp());
        data.put("notify_url", wxPayPropertiesConfig.getNotifyUrl());
        data.put("trade_type", wxPayPropertiesConfig.getTradeType());
        //经测试可以不用这个参数
        data.put("scene_info", wxPayPropertiesConfig.getSceneInfo());
        //data.put("device_info", "");
        //data.put("fee_type", "CNY");
        //data.put("product_id", "12");

        Map<String, String> resp = wxPay.unifiedOrder(data);
        //{nonce_str=vkiSAxfGZZbLTIM2, mweb_url=https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx1511442547333437eef07faa1004983200&package=3430310883, appid=wx8ad568ceec585da2, sign=97B7BD987983C0FB5E37EC219AC494E4B53486271BD2AA68B743B2DECDD7F560, trade_type=MWEB, return_msg=OK, result_code=SUCCESS, mch_id=1565728481, return_code=SUCCESS, prepay_id=wx1511442547333437eef07faa1004983200}
        /*System.out.println("resp = " + resp);*/
        String returnCode = resp.get("return_code");
        if(!StringUtils.equals(returnCode, WXPayConstants.SUCCESS)){
            log.info("微信支付下单失败:订单号:{},下单结果:{}", outTradeNo,resp);
            throw new Exception("支付失败,请稍后再试!");
        }
        return  resp;
    }

    /**
     * 微信支付回调（平台-->用户）,并返回接收消息成功
     * @return   最终此次请求需要返回以下两种
     * String succMsg = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
     * String errMsg = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
     */
    @Override
    public WxpayNotifyParam payResultNotify(String resultNotifyXml) throws Exception {
        log.info("微信支付交易结果通知xml:{}", resultNotifyXml);
        //该方法在支付成功时已验证签名
        Map<String, String> payResultMap = wxPay.processResponseXml(resultNotifyXml);
        WxpayNotifyParam notifyParam = mapper.convertValue(payResultMap, WxpayNotifyParam.class);
        //String trade_status = notifyParam.getTradeStatus();
        // 支付成功
        //if (trade_status.equals(AliPayConfig.AlipayTradeStatus.TRADE_SUCCESS) || trade_status.equals(
        //        AliPayConfig.AlipayTradeStatus.TRADE_FINISHED)) {
        //}
        //需要做状态判断 防止重复收到回调！！！
        log.info("微信支付订单ID={}已收到支付回调，付款成功", notifyParam.getOutTradeNo());

        return notifyParam;

    }

    /**
     * 微信jsapi支付回调（平台-->用户）,并返回接收消息成功
     * @return   最终此次请求需要返回以下两种
     * String succMsg = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
     * String errMsg = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
     */
    @Override
    public WechatPayDecodeNotifyParam jsApiPayResultNotify(HttpServletRequest request) throws Exception{
        //获取微信返回的body中的数据
        String body =ReadAsChars(request);
        log.info("微信支付交易结果通知body:{}", body);
        WechatConfigLog wechatConfig=wechatConfigLogService.lambdaQuery().isNotNull(WechatConfigLog::getApiKey).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        if(wechatConfig==null){
            log.info("微信支付交易结果通知,配置错误,请稍后再试body:{}", body);
            throw new Exception("微信支付交易结果通知,配置错误,请稍后再试!");
        }

        JsonNode jsonNode =mapper.readTree(body).at("/resource");
        //数据密文
        String ciphertext =jsonNode.at("/ciphertext").asText();
        //随机串
        String nonce =jsonNode.at("/nonce").asText();
        //附加数据
        String associatedData =jsonNode.at("/associated_data").asText();

        String resource =WechatRSADecodeUtil.decryptFromResource(ciphertext,nonce,associatedData,wechatConfig.getApiKey());
        if(StringUtils.isNotBlank(resource)){
            WechatPayDecodeNotifyParam decodeNotifyParam = mapper.readValue(resource, WechatPayDecodeNotifyParam.class);
            log.info("微信支付订单ID={}已收到支付回调，付款成功", decodeNotifyParam.getOutTradeNo());
            return decodeNotifyParam;
        }
        log.info("微信支付交易结果通知,解密失败body:{}", body);
        throw new RuntimeException("微信支付交易结果通知，解密失败！");
    }
    /**
     * 获取request中的请求body
     */
    public String ReadAsChars(HttpServletRequest request) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder("");
        try {
            br = request.getReader();
            String str;
            while ((str = br.readLine()) != null) {
                sb.append(str);
            }
            br.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return sb.toString();
    }
//    @Override
//    public boolean weiXinActivateCode(){
//        final ObjectNode weiXinActivateCode = mapper.createObjectNode();
//        //创建批次的商户号
//        weiXinActivateCode.put("stock_creator_mchid",wxPayPropertiesConfig.getMchIdCode());
//        WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,wxPayPropertiesConfig.getMchIdCode()).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
//        if(wechatConfigLog==null){
//            log.error("微信激活代金券-商户证书未配置:{}" ,weiXinActivateCode);
//            return false;
//        }
//        try {
//            CloseableHttpResponse response = wxPay.WxFeedback(wxPayPropertiesConfig.getWeiXinActivateCode().replaceAll("stock_id",wxPayPropertiesConfig.getStockIdCode()),weiXinActivateCode.toString(), WeChatConfig.getPriKeyByP12(wxPayPropertiesConfig.getMchIdCode()), wechatConfigLog.getMchSerialNo(), wxPayPropertiesConfig.getMchIdCode(), wechatConfigLog.getApiKey());
//            if(response==null){
//                log.error("微信发送代金券-接口未响应:{}" ,weiXinActivateCode);
//                return false;
//            }
//            try {
//                int statusCode = response.getStatusLine().getStatusCode();
//                if (statusCode == 200) { //处理成功
//                    return true;
//                } else if (statusCode == 204) { //处理成功，无返回Body
//                    return true;
//                } else {
//                    log.error("微信激活代金券-参数:{},resp_code:{},body:{}" ,weiXinActivateCode,statusCode, EntityUtils.toString(response.getEntity()));
//                    return false;
//                }
//            }finally{
//                if (response != null) {
//                    try {
//                        response.close(); // 确保在最后正确关闭流
//                    } catch (IOException e) {
//                        // 关闭流时的异常处理
//                    }
//                }
//            }
//
//        } catch (Exception e) {
//            log.error("微信激活代金券,请求异常=>请求参数:{}",weiXinActivateCode,e);
//            return false;
//        }
//    }


//    @Override
//    public boolean sendWeiXinCode(String openId, String outRefundNo){
//        final ObjectNode sendWeiXinCode = mapper.createObjectNode();
//        //批次号
//        sendWeiXinCode.put("stock_id",wxPayPropertiesConfig.getStockIdCode());
//        //商户单据号
//        sendWeiXinCode.put("out_request_no", outRefundNo);
//        //公众账号ID
//        sendWeiXinCode.put("appid",wxPayPropertiesConfig.getAppIdCode());
//        //创建批次的商户号
//        sendWeiXinCode.put("stock_creator_mchid",wxPayPropertiesConfig.getMchIdCode());
//
//        WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,wxPayPropertiesConfig.getMchIdCode()).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
//        if(wechatConfigLog==null){
//            log.error("微信发送代金券-商户证书未配置:{}" ,sendWeiXinCode);
//            return false;
//        }
//        try {
//            CloseableHttpResponse response = wxPay.WxFeedback(wxPayPropertiesConfig.getSendWeiXinCode().replaceAll("openid",openId),sendWeiXinCode.toString(), WeChatConfig.getPriKeyByP12(wxPayPropertiesConfig.getMchIdCode()), wechatConfigLog.getMchSerialNo(), wxPayPropertiesConfig.getMchIdCode(), wechatConfigLog.getApiKey());
//            if(response==null){
//                log.error("微信发送代金券-接口未响应:{}" ,sendWeiXinCode);
//                return false;
//            }
//            try {
//                int statusCode = response.getStatusLine().getStatusCode();
//                JsonNode jsonNode =mapper.readTree(EntityUtils.toString(response.getEntity()));
//                String message =jsonNode.at("/message").asText();
//                if (statusCode == 200) { //处理成功
//                    return true;
//                } else if (statusCode == 204) { //处理成功，无返回Body
//                    return true;
//                } else {
//                    log.error("微信发送代金券-异常参数:{},resp_code:{},body:{}" ,sendWeiXinCode,statusCode,message);
//                    wxMpNotifyService.sendAlertMessage("17723327836", BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE, String.valueOf(statusCode),message);
//                    return false;
//                }
//            }finally{
//                if (response != null) {
//                    try {
//                        response.close(); // 确保在最后正确关闭流
//                    } catch (IOException e) {
//                        // 关闭流时的异常处理
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("微信发送代金券,请求异常=>请求参数:{}",sendWeiXinCode,e);
//            return false;
//        }
//    }

    @Override
    public void wechatCancelCodeNotify(String mchId,String requestBody) {
        try {
            log.info("微信代金券核销事件回调通知-商户号:{},Body:{}",mchId,requestBody);
            WechatComplainNotify  wechatComplainNotify = mapper.readValue(requestBody, WechatComplainNotify.class);

            WechatCouponConfig config=wechatCouponConfigService.lambdaQuery().eq(WechatCouponConfig::getIsValid, 1).orderByDesc(WechatCouponConfig::getCreateTime).last("limit 1").one();
            if(config!=null){
                String resource= AESUtils.setDecryptData(wechatComplainNotify.getResource().getAssociatedData(),wechatComplainNotify.getResource().getNonce(),wechatComplainNotify.getResource().getCiphertext(),config.getApiKey());
                log.info("微信代金券核销事件回调通知-商户号:{},Body:{},解密数据:{}",mchId,requestBody,resource);
            }else {
                log.error("微信代金券核销事件回调通知-商户号错误:{},Body:{}",mchId,requestBody);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }


    @Override
    public Result<?> weiXinActivateCode(WechatCouponConfig wechatCouponConfig){
        final ObjectNode weiXinActivateCode = mapper.createObjectNode();
        //创建批次的商户号
        weiXinActivateCode.put("stock_creator_mchid",wechatCouponConfig.getMchId());
        try {
            CloseableHttpResponse response = wxPay.WxFeedback(wxPayPropertiesConfig.getWeiXinActivateCode().replaceAll("stock_id",wechatCouponConfig.getStockId()),weiXinActivateCode.toString(), WeChatConfig.getPriKeyByP12(wechatCouponConfig.getMchId()), wechatCouponConfig.getMchSerialNo(), wechatCouponConfig.getMchId(), wechatCouponConfig.getApiKey());
            if(response==null){
                log.error("微信激活代金券-接口未响应:{}" ,weiXinActivateCode);
                return Result.error("接口未响应!");
            }
            try {
                int statusCode = response.getStatusLine().getStatusCode();
                JsonNode jsonNode =mapper.readTree(EntityUtils.toString(response.getEntity()));
                String message =jsonNode.at("/message").asText();
                if (statusCode == 200) { //处理成功
                    return Result.ok("激活成功!");
                } else if (statusCode == 204) { //处理成功，无返回Body
                    return Result.ok("激活成功!");
                } else {
                    log.error("微信激活代金券异常-请求参数:{},resp_code:{},body:{}",wechatCouponConfig,statusCode,jsonNode);
                    return Result.error(message);
                }
            }finally{
                if (response != null) {
                    try {
                        response.close(); // 确保在最后正确关闭流
                    } catch (IOException e) {
                        // 关闭流时的异常处理
                    }
                }
            }

        } catch (Exception e) {
            log.error("微信激活代金券,请求异常=>请求参数:{}",weiXinActivateCode,e);
            return Result.ok("请求异常!");
        }
    }


    //查询批次是否可用
    @Override
    public boolean stocksIsValid(WechatCouponConfig wechatCouponConfig){
        try {
            CloseableHttpResponse response = wxPay.WxGet(wxPayPropertiesConfig.getStocksIsValid().replaceAll("stock_id",wechatCouponConfig.getStockId())+"?stock_creator_mchid="+wechatCouponConfig.getMchId(), WeChatConfig.getPriKeyByP12(wechatCouponConfig.getMchId()), wechatCouponConfig.getMchSerialNo(), wechatCouponConfig.getMchId(), wechatCouponConfig.getApiKey());
            if(response==null){
                log.error("查询批次是否可用-接口未响应:{}" ,wechatCouponConfig);
                return false;
            }
            try {
                int statusCode = response.getStatusLine().getStatusCode();
                JsonNode jsonNode =mapper.readTree(EntityUtils.toString(response.getEntity()));
                //最大发券数
                String max_coupons =jsonNode.at("/stock_use_rule/max_coupons").asText();
                //已发券数量
                String distributed_coupons =jsonNode.at("/distributed_coupons").asText();
                //unactivated：未激活
                //audit：审核中
                //running：运行中
                //stoped：已停止
                //paused：暂停发放
                //200和204成功，其他失败
                String status =jsonNode.at("/status").asText();
                if (statusCode == 200 && "running".equals(status) && (Integer.valueOf(distributed_coupons)<Integer.valueOf(max_coupons) || Integer.valueOf(distributed_coupons)<=0)) { //处理成功
                    return true;
                } else if (statusCode == 204 && "running".equals(status) && (Integer.valueOf(distributed_coupons)<Integer.valueOf(max_coupons) || Integer.valueOf(distributed_coupons)<=0)) { //处理成功，无返回Body
                    return true;
                } else {
                    log.error("查询批次是否可用异常-请求参数:{},resp_code:{},body:{}",wechatCouponConfig,statusCode,jsonNode);
                    return false;
                }
            }finally{
                if (response != null) {
                    try {
                        response.close(); // 确保在最后正确关闭流
                    } catch (IOException e) {
                        // 关闭流时的异常处理
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询批次是否可用,请求异常=>请求参数:{}",wechatCouponConfig,e);
            return false;
        }
    }

    @Override
    public boolean sendWeiXinCode(String openId, String outRefundNo,WechatCouponConfig wechatCouponConfig){
        final ObjectNode sendWeiXinCode = mapper.createObjectNode();
        //批次号
        sendWeiXinCode.put("stock_id",wechatCouponConfig.getStockId());
        //商户单据号
        sendWeiXinCode.put("out_request_no", outRefundNo);
        //公众账号ID
        sendWeiXinCode.put("appid",wechatCouponConfig.getAppId());
        //创建批次的商户号
        sendWeiXinCode.put("stock_creator_mchid",wechatCouponConfig.getMchId());

        try {
            CloseableHttpResponse response = wxPay.WxFeedback(wxPayPropertiesConfig.getSendWeiXinCode().replaceAll("openid",openId),sendWeiXinCode.toString(), WeChatConfig.getPriKeyByP12(wechatCouponConfig.getMchId()), wechatCouponConfig.getMchSerialNo(), wechatCouponConfig.getMchId(), wechatCouponConfig.getApiKey());
            if(response==null){
                log.error("微信发送代金券-接口未响应:{}" ,sendWeiXinCode);
                return false;
            }
            try {
                int statusCode = response.getStatusLine().getStatusCode();
                JsonNode jsonNode =mapper.readTree(EntityUtils.toString(response.getEntity()));
                String message =jsonNode.at("/message").asText();
                if (statusCode == 200) { //处理成功
                    return true;
                } else if (statusCode == 204) { //处理成功，无返回Body
                    return true;
                } else {
                    log.error("微信发送代金券异常-请求参数:{},resp_code:{},body:{}" ,sendWeiXinCode,statusCode,jsonNode);
                    wxMpNotifyService.sendAlertMessage("17723327836", BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE, String.valueOf(statusCode),message);
                    return false;
                }
            }finally{
                if (response != null) {
                    try {
                        response.close(); // 确保在最后正确关闭流
                    } catch (IOException e) {
                        // 关闭流时的异常处理
                    }
                }
            }
        } catch (Exception e) {
            log.error("微信发送代金券,请求异常=>请求参数:{}",sendWeiXinCode,e);
            return false;
        }
    }
}
