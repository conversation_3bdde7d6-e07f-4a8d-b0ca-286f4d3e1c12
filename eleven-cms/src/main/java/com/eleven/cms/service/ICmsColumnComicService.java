package com.eleven.cms.service;

import com.eleven.cms.entity.CmsComicVideo;
import com.eleven.cms.entity.CmsColumnComic;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: cms_column_comic
 * @Author: jeecg-boot
 * @Date: 2024-08-26
 * @Version: V1.0
 */
public interface ICmsColumnComicService extends IService<CmsColumnComic> {

    /**
     * 添加一对多
     */
    public void saveMain(CmsColumnComic cmsColumnComic, List<CmsComicVideo> cmsComicVideoList);

    /**
     * 修改一对多
     */
    public void updateMain(CmsColumnComic cmsColumnComic, List<CmsComicVideo> cmsComicVideoList);

    /**
     * 删除一对多
     */
    public void delMain(String id);

    /**
     * 批量删除一对多
     */
    public void delBatchMain(Collection<? extends Serializable> idList);

}
