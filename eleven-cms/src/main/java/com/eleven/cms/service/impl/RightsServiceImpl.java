package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.RightsRightsProperties;
import com.eleven.cms.dto.Rights;
import com.eleven.cms.entity.*;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RightsPackDto;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 权益领取校验公共接口
 */
@Slf4j
@Component
public class RightsServiceImpl implements IRightsSubService {

    public static final String SOAP_RESPONSE = "soap接口调用响应";
    public static final String SOAP_ERROR = "soap接口调用异常";
    private static final String TODAY_LOG_TAG = "渠道订购[当日校验]";
    private static final String CHANNEL_LOG_TAG = "渠道订购[渠道号查询]";
    private static final String TODAY_LOG_TAG_ERROR = "渠道订购[当日校验异常]";
    @Autowired
    IEsDataService esDataService;
    @Autowired
    private UnifyRightsFeignClient unifyRightsFeignClient;
    @Autowired
    private IMiguPackService miguPackService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IRightsPackService rightsPackService;
//    @Autowired
//    private ISoapFeedbackService soapFeedbackService;
    @Autowired
    private RightsRightsProperties rightsRightsProperties;
    @Autowired
    private IMemberRightsService memberRightsService;
//    @Autowired
//    private IBusinessChannelRightsService businessChannelRightsService;
//    @Autowired
//    private IBusinessPackService businessPackService;
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    /**
     * 当天是否订购业务
     * @param mobile
     * @param channelList
     * @return
     */
    @Override
    public Boolean everydayIsSub(String mobile,List<String> channelList) {
        try{
            //查询当天最新数据
            List<EsSubscribe> sub=esDataService.findTodayEsSubscribeByMobileAndChannels(mobile,channelList);
            log.info("{}-手机号:{},订购数据:{},渠道号:{}",TODAY_LOG_TAG, mobile,sub,channelList);
            if(sub!=null){
                return true;
            }
        } catch (Exception e) {
            log.error("{}-手机号:{},渠道号:{}",TODAY_LOG_TAG_ERROR, mobile,channelList,e);
        }
        return false;
    }








    /**
     * 判断当月权益是否已全部发放
     * @param mobile
     * @return
     */
    @Override
    public Boolean monthlyIsRecharge(String mobile, String serviceId) {
        //查询业务包
        List<String> packNameList=miguPackService.lambdaQuery().eq(MiguPack::getServiceId,serviceId).eq(MiguPack::getIsValid,1).list().stream().map(MiguPack::getPackName).collect(Collectors.toList());
        if(packNameList==null || packNameList.isEmpty()){
            try {
                FebsResponse febsResponse= unifyRightsFeignClient.queryRightsPackList(serviceId);
                if(!febsResponse.isOK() || febsResponse.get("data")==null){
                    return false;
                }
                List<RightsPackDto> rightsList = JSONObject.parseArray(febsResponse.get("data").toString(),RightsPackDto.class);
                if(rightsList==null || rightsList.isEmpty()){
                    return false;
                }
                packNameList=rightsList.stream().map(RightsPackDto::getPackName).collect(Collectors.toList());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if(packNameList==null || packNameList.isEmpty()){
            return false;
        }
        //查询当月权益已领取权益
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameListAndMonth(mobile,packNameList);
        //判断是否已全部领取
        if(chargeLogList !=null && chargeLogList.size()>=packNameList.size()) {
            return false;
        }
        return true;
    }

    /**
     * 判断当月支付宝权益是否已全部发放
     * @param mobile
     * @return
     */
    @Override
    public Boolean aliPayMonthlyIsRecharge(String mobile, String serviceId) {
        //查询业务包
        List<String> packNameList=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId,serviceId)
                .eq(MiguPack::getIsValid,1)
                .eq(MiguPack::getIsDisplay,1)
                .list().stream().map(MiguPack::getPackName).collect(Collectors.toList());;
        if(packNameList==null || packNameList.isEmpty()){
            return false;
        }
        //查询当月权益已领取权益
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameListAndDate(mobile,packNameList);
        //判断是否已全部领取
        if(chargeLogList !=null && !packNameList.isEmpty() && chargeLogList.size()>=packNameList.size()) {
            return false;
        }
        return true;
    }
    /**
     * 判断当月网易云MM权益是否已全部发放
     * @param mobile
     * @return
     */
    @Override
    public Boolean wangYiYunMMMonthlyIsRecharge(String mobile, String serviceId,Long days) {
        //查询业务包
        List<String> packNameList=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId,serviceId)
                .eq(MiguPack::getIsValid,1)
                .eq(MiguPack::getIsDisplay,1)
                .list().stream().map(MiguPack::getPackName).collect(Collectors.toList());;
        if(packNameList==null || packNameList.isEmpty()){
            return false;
        }
        //查询当月权益已领取权益
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameListAndMonth(mobile,packNameList,days);
        //判断是否已全部领取
        if(chargeLogList !=null && !packNameList.isEmpty() && chargeLogList.size()>=packNameList.size()) {
            return false;
        }
        return true;
    }
    /**
     * 判断商城权益是否已全部发放
     * @param outTradeNo
     * @return
     */
    @Override
    public Boolean shopOrderDayIsRecharge(String outTradeNo, String serviceId) {
        //查询业务包
        List<String> packNameList=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId,serviceId)
                .list().stream().map(MiguPack::getPackName).collect(Collectors.toList());;
        if(packNameList==null || packNameList.isEmpty()){
            return false;
        }
        //查询商城权益已领取权益
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByOrderIdAndPackNameList(outTradeNo,packNameList);
        //判断是否已全部领取
        if(chargeLogList !=null && !packNameList.isEmpty() && chargeLogList.size()>=packNameList.size()) {
            return false;
        }
        return true;
    }
    /**
     * 获取预约充值时间
     * @param serviceId
     * @param packName
     * @return
     */
    @Override
    public LocalDateTime getScheduledTime(String mobile,String serviceId,String packName){
        //红名单用户不查询预约充值时间
        boolean isRad=this.isRedList(mobile);
        if(isRad){
            return LocalDateTime.now().plusMinutes(1);
        }
        MiguPack miguPack=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId, serviceId)
                .eq(MiguPack::getPackName, packName).orderByDesc(MiguPack::getCreateTime).last("limit 1").one();
        if(miguPack==null){
//            //获取百度网盘系统预约充值时间
//            FebsResponse febsResponse=unifyRightsFeignClient.getScheduledTime(serviceId,packName);
//            if(febsResponse.isOK()){
//                Long rechargeDelayMinute=Long.valueOf(febsResponse.get("data").toString());
//                return LocalDateTime.now().plusMinutes(rechargeDelayMinute);
//            }
            return null;
        }
        LocalDateTime effectTime=LocalDateTime.now().plusMinutes(miguPack.getRechargeDelayMinute());
//        //是否执行查询soap接口
//        if(miguPack.getIsSoap().equals(1) && StringUtils.isNotBlank(miguPack.getSoapServiceId())){
//            //获取当月最新订购数据
//            String soapServiceId =miguPack.getSoapServiceId();
//            try {
//                SoapFeedback soapFeedback = soapFeedbackService.findLastOrder(mobile,  soapServiceId);
//                log.info("{}-手机号:{},soap业务ID:{},充值业务ID:{},soap响应:{}",SOAP_RESPONSE, mobile,soapServiceId,serviceId,soapFeedback);
//                if(soapFeedback == null) {
//                    return null;
//                }
//                effectTime=DateUtil.dateToLocalDateTime(soapFeedback.getCreateTime()).plusMinutes(miguPack.getRechargeDelayMinute());
//            } catch (Exception e) {
//                log.error("{}-手机号:{},soap业务ID:{},充值业务ID:{}",SOAP_ERROR, mobile,soapServiceId,serviceId,e);
//                return null;
//            }
//        }

        return effectTime;
    }
    /**
     * 网页获取预约充值时间
     * @param serviceId
     * @param packName
     * @return
     */
    @Override
    public LocalDateTime getWebScheduledTime(String mobile,String serviceId,String packName){
        //红名单用户不查询预约充值时间
        boolean isRad=this.isRedList(mobile);
        if(isRad){
            return LocalDateTime.now().plusMinutes(1);
        }
        MiguPack miguPack=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId, serviceId)
                .eq(MiguPack::getPackName, packName).orderByDesc(MiguPack::getCreateTime).last("limit 1").one();
        if(miguPack==null){
//            //获取百度网盘系统预约充值时间
//            FebsResponse febsResponse=unifyRightsFeignClient.getScheduledTime(serviceId,packName);
//            if(febsResponse.isOK()){
//                Long rechargeDelayMinute=Long.valueOf(febsResponse.get("data").toString());
//                return LocalDateTime.now().plusMinutes(rechargeDelayMinute);
//            }
            return null;
        }
        LocalDateTime effectTime=LocalDateTime.now().plusMinutes(miguPack.getRechargeDelayMinute());
//        //是否执行查询soap接口
//        if(miguPack.getIsSoap().equals(1) && StringUtils.isNotBlank(miguPack.getSoapServiceId())){
//            //获取当月最新订购数据
//            String soapServiceId =miguPack.getSoapServiceId();
//            try {
//                SoapFeedback soapFeedback = soapFeedbackService.findLastOrder(mobile,  soapServiceId);
//                log.info("{}-手机号:{},soap业务ID:{},充值业务ID:{},soap响应:{}",SOAP_RESPONSE, mobile,soapServiceId,serviceId,soapFeedback);
//                if(soapFeedback == null) {
//                    return null;
//                }
//                effectTime=DateUtil.dateToLocalDateTime(soapFeedback.getCreateTime()).plusMinutes(miguPack.getRechargeDelayMinute());
//            } catch (Exception e) {
//                log.error("{}-手机号:{},soap业务ID:{},充值业务ID:{}",SOAP_ERROR, mobile,soapServiceId,serviceId,e);
//                return null;
//            }
//        }
        return effectTime;
    }



    /**
     * 获取预约充值时间
     * @param serviceId
     * @param packName
     * @return
     */
    @Override
    public LocalDateTime getCouponCodeScheduledTime(String mobile,String serviceId,String packName){
        //红名单用户不查询预约充值时间
        boolean isRad=this.isRedList(mobile);
        if(isRad){
            return LocalDateTime.now().plusMinutes(1);
        }
        MiguPack miguPack=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId, serviceId)
                .eq(MiguPack::getPackName, packName).orderByDesc(MiguPack::getCreateTime).last("limit 1").one();
        if(miguPack==null){
            return null;
        }
        LocalDateTime effectTime=LocalDateTime.now().plusMinutes(miguPack.getRechargeDelayMinute());
//        //是否执行查询soap接口
//        if(miguPack.getIsSoap().equals(1) && StringUtils.isNotBlank(miguPack.getSoapServiceId())){
//            //获取当月最新订购数据
//            String soapServiceId =miguPack.getSoapServiceId();
//            try {
//                SoapFeedback soapFeedback = soapFeedbackService.findLastOrder(mobile,soapServiceId);
//                log.info("{}-手机号:{},soap业务ID:{},充值业务ID:{},soap响应:{}",SOAP_RESPONSE, mobile,soapServiceId,serviceId,soapFeedback);
//                if(soapFeedback == null) {
//                    return null;
//                }
//                effectTime=DateUtil.dateToLocalDateTime(soapFeedback.getCreateTime()).plusMinutes(miguPack.getRechargeDelayMinute());
//            } catch (Exception e) {
//                log.error("{}-手机号:{},soap业务ID:{},充值业务ID:{}",SOAP_ERROR, mobile,soapServiceId,serviceId,e);
//                return null;
//            }
//        }

        return effectTime;
    }
    /**
     * 获取预约充值时间
     * @param serviceId
     * @param packName
     * @return
     */
    @Override
    public LocalDateTime getShopOrderScheduledTime(String serviceId,String packName){
        MiguPack miguPack=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId, serviceId)
                .eq(MiguPack::getPackName, packName).orderByDesc(MiguPack::getCreateTime).last("limit 1").one();
        if(miguPack==null){
            return null;
        }
        LocalDateTime effectTime=LocalDateTime.now().plusMinutes(miguPack.getRechargeDelayMinute());
        return effectTime;
    }
//    /**
//     * 特定业务获取预约充值时间(百度网盘和沃音乐视频彩铃)
//     * @param serviceId
//     * @param packName
//     * @return
//     */
//    @Override
//    public LocalDateTime getScheduledTime(String mobile,String serviceId,String packName,LocalDateTime scheduledTime){
//        //红名单用户不查询预约充值时间
//        boolean isRad=this.isRedList(mobile);
//        if(isRad){
//            return LocalDateTime.now().plusMinutes(1);
//        }
//        MiguPack miguPack=miguPackService.lambdaQuery()
//                .eq(MiguPack::getServiceId, serviceId)
//                .eq(MiguPack::getPackName, packName).orderByDesc(MiguPack::getCreateTime).last("limit 1").one();;
//        if(miguPack==null){
////            //获取百度网盘系统预约充值时间
////            FebsResponse febsResponse=unifyRightsFeignClient.getScheduledTime(serviceId,packName);
////            if(febsResponse.isOK()){
////                Long rechargeDelayMinute=Long.valueOf(febsResponse.get("data").toString());
////                return LocalDateTime.now().plusMinutes(rechargeDelayMinute);
////            }
//            return null;
//        }
//        LocalDateTime effectTime=scheduledTime.plusMinutes(miguPack.getRechargeDelayMinute());
////        //是否执行查询soap接口
////        if(miguPack.getIsSoap().equals(1) && StringUtils.isNotBlank(miguPack.getSoapServiceId())){
////            //获取当月最新订购数据
////            String soapServiceId =miguPack.getSoapServiceId();
////            try {
////                SoapFeedback soapFeedback = soapFeedbackService.findLastOrder(mobile,  soapServiceId);
////                log.info("{}-手机号:{},soap业务ID:{},充值业务ID:{},soap响应:{}",SOAP_RESPONSE, mobile,soapServiceId,serviceId,soapFeedback);
////                if(soapFeedback == null) {
////                    return null;
////                }
////                effectTime=DateUtil.dateToLocalDateTime(soapFeedback.getCreateTime()).plusMinutes(miguPack.getRechargeDelayMinute());
////            } catch (Exception e) {
////                log.error("{}-手机号:{},soap业务ID:{},充值业务ID:{}",SOAP_ERROR, mobile,soapServiceId,serviceId,e);
////                return null;
////            }
////        }
//        return effectTime;
//    }

    /**
     * 网页特定业务获取预约充值时间(百度网盘和沃音乐视频彩铃)
     * @param serviceId
     * @param packName
     * @return
     */
    @Override
    public LocalDateTime getWebScheduledTime(String mobile,String serviceId,String packName,LocalDateTime scheduledTime){
        //红名单用户不查询预约充值时间
        boolean isRad=this.isRedList(mobile);
        if(isRad){
            return LocalDateTime.now().plusMinutes(1);
        }
        MiguPack miguPack=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId, serviceId)
                .eq(MiguPack::getPackName, packName).orderByDesc(MiguPack::getCreateTime).last("limit 1").one();;
        if(miguPack==null){
//            //获取百度网盘系统预约充值时间
//            FebsResponse febsResponse=unifyRightsFeignClient.getScheduledTime(serviceId,packName);
//            if(febsResponse.isOK()){
//                Long rechargeDelayMinute=Long.valueOf(febsResponse.get("data").toString());
//                return LocalDateTime.now().plusMinutes(rechargeDelayMinute);
//            }
            return null;
        }
        LocalDateTime effectTime=scheduledTime.plusMinutes(miguPack.getRechargeDelayMinute());
//        //是否执行查询soap接口
//        if(miguPack.getIsSoap().equals(1) && StringUtils.isNotBlank(miguPack.getSoapServiceId())){
//            //获取当月最新订购数据
//            String soapServiceId =miguPack.getSoapServiceId();
//            try {
//                SoapFeedback soapFeedback = soapFeedbackService.findLastOrder(mobile,  soapServiceId);
//                log.info("{}-手机号:{},soap业务ID:{},充值业务ID:{},soap响应:{}",SOAP_RESPONSE, mobile,soapServiceId,serviceId,soapFeedback);
//                if(soapFeedback == null) {
//                    return null;
//                }
//                effectTime=DateUtil.dateToLocalDateTime(soapFeedback.getCreateTime()).plusMinutes(miguPack.getRechargeDelayMinute());
//            } catch (Exception e) {
//                log.error("{}-手机号:{},soap业务ID:{},充值业务ID:{}",SOAP_ERROR, mobile,soapServiceId,serviceId,e);
//                return null;
//            }
//        }
        return effectTime;
    }
    /**
     * 判断是否红名单用户
     * @param mobile
     * @return
     */
    private Boolean isRedList(String mobile) {
        List<String> redMobileList=rightsRightsProperties.getRedMobileList();
        if(redMobileList.contains(mobile)){
            return true;
        }
        return false;
    }

    /**
     * 设置权益配置参数
     * @param rights
     * @return
     */
    @Override
    public String setRightCofig(Rights rights,String rightsId,Date payTime){
        rights.setRightsMonth(DateUtil.formatYearMonth(LocalDateTime.now()));
        rights.setPayTime(payTime);
        //查询可充值权益
        Optional<MemberRights> memberRightsOptional=memberRightsService.queryMemberRightsDetail(rightsId);
        if(memberRightsOptional==null || !memberRightsOptional.isPresent()){
            return null;
        }
        //设置权益产品配置
        MemberRights memberRights=memberRightsOptional.get();
        rights.setCouponId(memberRights.getCouponId());
        rights.setRightsName(memberRights.getRightsName());
        rights.setProductPrice(memberRights.getProductPrice());
        rights.setRechargeState(memberRights.getRechargeState());
        rights.setIsAccount(memberRights.getIsAccount());
        rights.setCompanyOwner(memberRights.getCompanyOwner());
        return memberRights.getCouponId();
    }
    /**
     * 校验当月会员权益是否已发放
     * @param mobile
     * @param packName
     * @param scheduledTime
     * @param couponId
     * @return
     */
    @Override
    public FebsResponse isRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId){
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndMonth(mobile,packName);
        //判断当月是否有已充值成功的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(hasSuccess){
            return new FebsResponse().repeatRecharge();
        }
        //判断当月是否有预约直充的订单
        boolean hasScheduled = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
        if(hasScheduled){
            Optional<JunboChargeLog> junboChargeLogOptional=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime));
            if(junboChargeLogOptional.isPresent()){
                try {
                    JunboChargeLog junboChargeLog=junboChargeLogOptional.get();
                    junboChargeLog.setScheduledTime(Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()));
                    junboChargeLogService.updateById(junboChargeLog);
                } catch (Exception e) {
                    return new FebsResponse().notMember();
                }
            }
            //判断当月领取权益
            boolean hasReceive = chargeLogList.stream().anyMatch(item-> couponId.equals(item.getCouponId()) && packName.equals(item.getPackName()));
            if(hasReceive){
                String couponName=chargeLogList.stream().filter(item-> couponId.equals(item.getCouponId())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
                return new FebsResponse().readyRecharge(couponName);
            }else{
                return new FebsResponse().repeatRecharge();
            }
        }
        //判断当月是否有正在充值的订单
        boolean hasProcessing = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasProcessing){
            String couponName=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().fastRecharge(couponName);
        }
        return new FebsResponse().success();
    }


    /**
     * 校验当月支付宝会员权益是否已发放
     * @param mobile
     * @param packName
     * @param scheduledTime
     * @param couponId
     * @return
     */
    @Override
    public FebsResponse aliPayIsRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId){
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndDate(mobile,packName);
        //判断当月是否有已充值成功的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(hasSuccess){
            return new FebsResponse().repeatRecharge();
        }
        //判断当月是否有预约直充的订单
        boolean hasScheduled = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
        if(hasScheduled){
            Optional<JunboChargeLog> junboChargeLogOptional=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime));
            if(junboChargeLogOptional.isPresent()){
                try {
                    JunboChargeLog junboChargeLog=junboChargeLogOptional.get();
                    junboChargeLog.setScheduledTime(Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()));
                    junboChargeLogService.updateById(junboChargeLog);
                } catch (Exception e) {
                    return new FebsResponse().notMember();
                }
            }
            //判断当月领取权益
            boolean hasReceive = chargeLogList.stream().anyMatch(item-> couponId.equals(item.getCouponId()));
            if(hasReceive){
                String couponName=chargeLogList.stream().filter(item-> couponId.equals(item.getCouponId())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
                return new FebsResponse().readyRecharge(couponName);
            }else{
                return new FebsResponse().repeatRecharge();
            }
        }
        //判断当月是否有正在充值的订单
        boolean hasProcessing = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasProcessing){
            String couponName=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().fastRecharge(couponName);
        }
        return new FebsResponse().success();
    }


    /**
     * 校验当月网易云MM会员权益是否已发放
     * @param mobile
     * @param packName
     * @param scheduledTime
     * @param couponId
     * @return
     */
    @Override
    public FebsResponse wangYiYunMMIsRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId,Long days){
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndDate(mobile,packName,days);
        //判断当月是否有已充值成功的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(hasSuccess){
            return new FebsResponse().repeatRecharge();
        }
        //判断当月是否有预约直充的订单
        boolean hasScheduled = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
        if(hasScheduled){
            Optional<JunboChargeLog> junboChargeLogOptional=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime));
            if(junboChargeLogOptional.isPresent()){
                try {
                    JunboChargeLog junboChargeLog=junboChargeLogOptional.get();
                    junboChargeLog.setScheduledTime(Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()));
                    junboChargeLogService.updateById(junboChargeLog);
                } catch (Exception e) {
                    return new FebsResponse().notMember();
                }
            }
            //判断当月领取权益
            boolean hasReceive = chargeLogList.stream().anyMatch(item-> couponId.equals(item.getCouponId()));
            if(hasReceive){
                String couponName=chargeLogList.stream().filter(item-> couponId.equals(item.getCouponId())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
                return new FebsResponse().readyRecharge(couponName);
            }else{
                return new FebsResponse().repeatRecharge();
            }
        }
        //判断当月是否有正在充值的订单
        boolean hasProcessing = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasProcessing){
            String couponName=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().fastRecharge(couponName);
        }
        return new FebsResponse().success();
    }
    /**
     * 校验商城权益是否已发放
     * @param orderId
     * @param packName
     * @param scheduledTime
     * @param couponId
     * @return
     */
    @Override
    public FebsResponse shopOrderIsRecharge(String orderId, String packName, LocalDateTime scheduledTime, String couponId){
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByOrderIdAndPackName(orderId,packName);
        //判断当月是否有已充值成功的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(hasSuccess){
            return new FebsResponse().repeatRecharge();
        }
        //判断当月是否有预约直充的订单
        boolean hasScheduled = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
        if(hasScheduled){
            Optional<JunboChargeLog> junboChargeLogOptional=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime));
            if(junboChargeLogOptional.isPresent()){
                try {
                    JunboChargeLog junboChargeLog=junboChargeLogOptional.get();
                    junboChargeLog.setScheduledTime(Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()));
                    junboChargeLogService.updateById(junboChargeLog);
                } catch (Exception e) {
                    return new FebsResponse().notMember();
                }
            }
            //判断当月领取权益
            boolean hasReceive = chargeLogList.stream().anyMatch(item-> couponId.equals(item.getCouponId()));
            if(hasReceive){
                String couponName=chargeLogList.stream().filter(item-> couponId.equals(item.getCouponId())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
                return new FebsResponse().readyRecharge(couponName);
            }else{
                return new FebsResponse().repeatRecharge();
            }
        }
        //判断当月是否有正在充值的订单
        boolean hasProcessing = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasProcessing){
            String couponName=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().fastRecharge(couponName);
        }
        return new FebsResponse().success();
    }


    /**
     * 校验当月联通沃悦读单次扣款权益是否已发放
     * @param mobile
     * @param packName
     * @param scheduledTime
     * @param couponId
     * @return
     */
    @Override
    public FebsResponse woReadShopIsRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId,List<String> serviceIdIdList){
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndDate(mobile,serviceIdIdList);
        //判断当月是否有已充值成功的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(hasSuccess){
            return new FebsResponse().repeatRecharge();
        }
        //判断当月是否有预约直充的订单
        boolean hasScheduled = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
        if(hasScheduled){
            Optional<JunboChargeLog> junboChargeLogOptional=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime));
            if(junboChargeLogOptional.isPresent()){
                try {
                    JunboChargeLog junboChargeLog=junboChargeLogOptional.get();
                    junboChargeLog.setScheduledTime(Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()));
                    junboChargeLogService.updateById(junboChargeLog);
                } catch (Exception e) {
                    return new FebsResponse().notMember();
                }
            }
            //判断当月领取权益
            boolean hasReceive = chargeLogList.stream().anyMatch(item-> couponId.equals(item.getCouponId()));
            if(hasReceive){
                String couponName=chargeLogList.stream().filter(item-> couponId.equals(item.getCouponId())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
                return new FebsResponse().readyRecharge(couponName);
            }else{
                return new FebsResponse().repeatRecharge();
            }
        }
        //判断当月是否有正在充值的订单
        boolean hasProcessing = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasProcessing){
            String couponName=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().fastRecharge(couponName);
        }
        return new FebsResponse().success();
    }
    /**
     * 通用权益包列表查询（包含支付宝）
     * @param serviceId
     * @return
     */
    @Override
    public FebsResponse queryRightsList(String serviceId,List<String> businessPackIdList){
        List<RightsPackDto> list = rightsPackService.queryRightsPackList(serviceId,businessPackIdList);
        if(list!=null && !list.isEmpty()){
            return new FebsResponse().success().data(list);
        }
        FebsResponse febsResponse= unifyRightsFeignClient.queryRightsPackList(serviceId);
        return febsResponse;
    }
    /**
     * 网页权益包列表查询（包含支付宝）
     * @param serviceId
     * @return
     */
    @Override
    public FebsResponse queryWebRightsList(String serviceId,List<String> businessPackIdList){
        List<RightsPackDto> list = rightsPackService.queryWebRightsPackList(serviceId,businessPackIdList);
        if(list!=null && !list.isEmpty()){
            return new FebsResponse().success().data(list);
        }
        FebsResponse febsResponse= unifyRightsFeignClient.queryWebRightsPackList(serviceId);
        return febsResponse;
    }

    /**
     * 查询领取记录
     * @param mobile
     * @return
     */
    @Override
    public FebsResponse queryRechargeList(String mobile){
        try {
            List<JunboChargeLog> shiPinCaiLingSystemList = junboChargeLogService.queryRechargeList(mobile);
            FebsResponse febsResponse= unifyRightsFeignClient.queryRechargeList(mobile);
            if(febsResponse.isOK()){
                List<JunboChargeLog> baiDuWangPanSystemList = mapper.readValue(febsResponse.get("data").toString(), List.class);
                if(shiPinCaiLingSystemList==null || shiPinCaiLingSystemList.isEmpty()){
                    log.info("查询领取记录列表=>手机号:{},领取记录列表数据:{}",mobile,shiPinCaiLingSystemList);
                    return new FebsResponse().success().data(shiPinCaiLingSystemList);
                }
                shiPinCaiLingSystemList.addAll(baiDuWangPanSystemList);
            }
            log.info("查询领取记录列表=>手机号:{},领取记录列表数据:{}",mobile,shiPinCaiLingSystemList);
            return new FebsResponse().success().data(shiPinCaiLingSystemList);
        } catch (Exception e) {
            log.error("查询领取记录列表异常=>手机号:{}",mobile,e);
            return new FebsResponse().fail();
        }

    }

    /**
     * 是否需要输入账号
     * @param rightsId
     * @return
     */
    @Override
    public Boolean isAccount(String rightsId,String account){
        MemberRights memberRights=memberRightsService.lambdaQuery()
                .eq(MemberRights::getRightsId, rightsId)
                .orderByDesc(MemberRights::getCreateTime).last("limit 1").one();;
        if(memberRights==null){
            return true;
        }
        //需要输入账号
        if(memberRights.getIsAccount().equals(1) && StringUtils.isBlank(account)){
            return true;
        }
        return false;
    }


    /**
     * 查询最新订购数据子渠道号
     * @param mobile
     * @param channel
     * @return
     */
    @Override
    public String nowIsSub(String mobile,String channel) {
        try{
            if(StringUtils.isBlank(channel) || StringUtils.isBlank(mobile)){
                return null;
            }
            //查询最新订购数据
            EsSubscribe esSubscribe =esDataService.findRecentEsSubscribeByMobileAndChannel(mobile,channel);
            String subChannel = esSubscribe == null ? null : esSubscribe.getSubChannel();
            log.info("{}-手机号:{},子渠道号:{},渠道号:{}",CHANNEL_LOG_TAG, mobile,subChannel,channel);

            return Strings.emptyToNull(subChannel);

        } catch (Exception e) {
            log.error("{}-手机号:{},渠道号:{}",CHANNEL_LOG_TAG, mobile,channel,e);
        }
        return null;
    }
    /**
     * 判断当月联通沃悦读单次扣款权益是否已全部发放
     * @param mobile
     * @return
     */
    @Override
    public Boolean woReadShopMonthlyIsRecharge(String mobile, String serviceId, List<String> serviceIdList) {
        //查询业务包
        List<String> packNameList=miguPackService.lambdaQuery()
                .eq(MiguPack::getServiceId,serviceId)
                .eq(MiguPack::getIsValid,1)
                .eq(MiguPack::getIsDisplay,1)
                .list().stream().map(MiguPack::getPackName).collect(Collectors.toList());;
        if(packNameList==null || packNameList.isEmpty()){
            return false;
        }
        //查询当月权益已领取权益
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndDate(mobile,serviceIdList);
        //判断是否已全部领取
        if(chargeLogList !=null && !packNameList.isEmpty() && chargeLogList.size()>=packNameList.size()) {
            return false;
        }
        return true;
    }

    @Override
    public FebsResponse queryRightsListById(String id) {
        List<RightsPackDto> list = rightsPackService.queryRightsListById(id);
        if(list!=null && !list.isEmpty()){
            return new FebsResponse().success().data(list);
        }
        FebsResponse febsResponse= unifyRightsFeignClient.queryRightsListById(id);
        return febsResponse;
    }



    /**
     * 网页权益包列表查询（包含支付宝）
     * @param serviceId
     * @return
     */
    @Override
    public List<RightsPackDto> queryLianLianWebRightsList(String serviceId,List<String> businessPackIdList){
        return rightsPackService.queryWebRightsPackList(serviceId,businessPackIdList);
    }


    /**
     * 通用权益包列表查询（包含支付宝）
     * @param serviceId
     * @return
     */
    @Override
    public List<RightsPackDto> queryLianLianRightsList(String serviceId,List<String> businessPackIdList){
        return rightsPackService.queryRightsPackList(serviceId,businessPackIdList);
    }

    /**
     * 当天是否订购业务
     * @param orderId
     * @param channelList
     * @return
     */
    @Override
    public boolean lianLianPayEverydayIsSub(String orderId,List<String> channelList) {
        try{
            //查询当天最新数据
            List<EsSubscribe> sub=esDataService.findTodayEsSubscribeByOrderIdAndChannels(orderId,channelList);
            log.info("{}-订单号:{},订购数据:{},渠道号:{}",TODAY_LOG_TAG, orderId,sub,channelList);
            if(sub!=null){
                return true;
            }
        } catch (Exception e) {
            log.error("{}-订单号:{},渠道号:{}",TODAY_LOG_TAG_ERROR, orderId,channelList,e);
        }
        return false;
    }
}
