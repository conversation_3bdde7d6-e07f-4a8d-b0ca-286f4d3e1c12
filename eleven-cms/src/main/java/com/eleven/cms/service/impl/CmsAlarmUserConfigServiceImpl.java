package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.CmsAlarmUserConfig;
import com.eleven.cms.mapper.CmsAlarmUserConfigMapper;
import com.eleven.cms.service.ICmsAlarmUserConfigService;
import org.springframework.stereotype.Service;

/**
 * @Description: cms_alarm_user_config
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
@Service
public class CmsAlarmUserConfigServiceImpl extends ServiceImpl<CmsAlarmUserConfigMapper, CmsAlarmUserConfig> implements ICmsAlarmUserConfigService {

}
