package com.eleven.cms.service.impl;

import com.eleven.cms.entity.ProvinceLimit;
import com.eleven.cms.mapper.ProvinceLimitMapper;
import com.eleven.cms.service.IProvinceLimitService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 省份限制
 * @Author: jeecg-boot
 * @Date:   2021-02-22
 * @Version: V1.0
 */
@Service
public class ProvinceLimitServiceImpl extends ServiceImpl<ProvinceLimitMapper, ProvinceLimit> implements IProvinceLimitService {

    //spring 4.3+支持self inject,从而解决事务和缓存在service内部调用不走代理类的问题
    @Autowired
    IProvinceLimitService provinceLimitService;

    @Cacheable(cacheNames = CacheConstant.CMS_PROVINCE_LIMIT_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    @Override
    public ProvinceLimit selectByCode(String provinceCode) {
        return this.lambdaQuery().eq(ProvinceLimit::getProvinceCode,provinceCode).one();
    }
    @Override
    public boolean isAvailableMobile(String provinceCode) {
        return provinceLimitService.selectByCode(provinceCode).getMobileAvailable().equals(1);
    }
    @Override
    public boolean isAvailableUnicom(String provinceCode) {
        return provinceLimitService.selectByCode(provinceCode).getUnicomAvailable().equals(1);
    }
    @Override
    public boolean isAvailableTelecom(String provinceCode) {
        return provinceLimitService.selectByCode(provinceCode).getTelecomAvailable().equals(1);
    }

}
