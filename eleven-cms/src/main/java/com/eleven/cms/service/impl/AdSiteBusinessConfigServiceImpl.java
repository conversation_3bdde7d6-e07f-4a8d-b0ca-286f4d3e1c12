package com.eleven.cms.service.impl;

import com.eleven.cms.entity.AdSiteBusinessConfig;
import com.eleven.cms.mapper.AdSiteBusinessConfigMapper;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: cms_ad_site_business_config
 * @Author: jeecg-boot
 * @Date:   2023-04-17
 * @Version: V1.0
 */
@Service
public class AdSiteBusinessConfigServiceImpl extends ServiceImpl<AdSiteBusinessConfigMapper, AdSiteBusinessConfig> implements IAdSiteBusinessConfigService {

    public static final List<String> GLOBAL_BLACK_APP_LISTS = Lists.newArrayList(
            //'墨迹天气:'
            "com.moji.mjweather",
            //'墨迹天气极速版:'
            "com.moji.mjweather.light",
            //'天气预报:'
            "com.lu.ashionweather",
            //'中华万年历:'
            "cn.etouch.ecalendar",
            //'准点天气:'
            "com.chif.weather",
            //'15日天气预报:'
            "com.tianqiyubao2345",
            //'2345天气王:'
            "com.tianqi2345",
            //'准时天气:'
            "com.bee.weathesafety",
            //'天天天气:'
            "com.weatherday",
            //'最美天气:'
            "com.icoolme.android.weather",
            //'地震预警:'
            "com.huania.earthquakewarning",
            //'中国地震预警:'
            "com.degal.earthquakewarn",
            //'手机天气预报:'
            "com.yizhiws.weather",
            //'掌上地灾公众版:'
            "com.amap.map3d.dizhizaihai",
            //'精准实时天气预报:'
            "com.weather.gorgeous",
            //'宝宝地震安全:'
            "com.sinyee.babybus.earthquake",
            //'宝宝地震安全2:'
            "com.sinyee.babybus.earthquakeII"
    );

    @Override
    public boolean isGlobalBlackApp(String appPackage){
        if(Strings.isNullOrEmpty(appPackage)){
            return false;
        }
        return GLOBAL_BLACK_APP_LISTS.contains(appPackage);
    }

    @Cacheable(cacheNames = CacheConstant.CMS_CHANNEL_AD_SITE_BUSINESS_CACHE,key = "#channel + '-' + #adSite",unless = "#result==null")
    @Override
    public Boolean isBlack(String channel, String adSite) {
        try{
            /*if(StringUtils.isBlank(channel) || StringUtils.isBlank(adSite)){
                return false;
            }*/
            //全渠道号屏蔽
            if(StringUtils.isNotBlank(adSite)){
                AdSiteBusinessConfig adSiteBusinessConfig = this.lambdaQuery()
                        .eq(AdSiteBusinessConfig::getChannel, "*")
                        .eq(AdSiteBusinessConfig::getAdSite, adSite)
                        .one();
                if(adSiteBusinessConfig != null){
                    return true;
                }
            }
            AdSiteBusinessConfig adSiteBusinessConfig = this.lambdaQuery()
                    .eq(AdSiteBusinessConfig::getChannel, channel)
                    .eq(AdSiteBusinessConfig::getAdSite, adSite == null ? "" : adSite)
                    .one();
            if(adSiteBusinessConfig == null){
                return false;
            }
        }catch (Exception e){
            return false;
        }
        return true;
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CHANNEL_AD_SITE_BUSINESS_OS_CACHE,key = "#channel + '-os-' + #adSite",unless = "#result==null")
    public Boolean isOsVrbtBlack(String channel, String adSite) {
        try{
            //全渠道号屏蔽
            if(StringUtils.isNotBlank(adSite)){
                AdSiteBusinessConfig adSiteBusinessConfig = this.lambdaQuery()
                        .eq(AdSiteBusinessConfig::getChannel, "*")
                        .eq(AdSiteBusinessConfig::getAdSite, adSite)
                        .eq(AdSiteBusinessConfig::getOsVrbtBlack, 1)
                        .one();
                if(adSiteBusinessConfig != null){
                    return true;
                }
            }
            AdSiteBusinessConfig adSiteBusinessConfig = this.lambdaQuery()
                    .eq(AdSiteBusinessConfig::getChannel, channel)
                    .eq(AdSiteBusinessConfig::getAdSite, adSite == null ? "" : adSite)
                    .eq(AdSiteBusinessConfig::getOsVrbtBlack, 1)
                    .one();
            if(adSiteBusinessConfig == null){
                return false;
            }
        }catch (Exception e){
            return false;
        }
        return true;
    }


}
