package com.eleven.cms.service.impl;

import com.eleven.cms.config.DatangSmsProperties;
import com.eleven.cms.entity.SmsSendLog;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.ISmsSendLogService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.DatangSmsResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Date: 2020/1/3 11:27
 * Desc: 阿里云短信下发
 */
@Slf4j
@Service
public class DatangSmsServiceImpl implements IDatangSmsService {
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @Autowired
    private DatangSmsProperties datangSmsProperties;
    @Autowired
    private Environment environment;
    @Autowired
    private ISmsSendLogService smsSendLogService;
    private OkHttpClient client;
    private HttpUrl sendSmsHttpUrl;
    private String userId;
    private String apiKey;
    private ObjectMapper mapper;


    @PostConstruct
    public void init(){
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.testProxyHost, OkHttpClientUtils.testProxyPort)))
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper();
    }

    @Override
    public boolean sendUnicomUnsubNotice(String phoneNumber) {
        log.info("手机号:{},下发联通退订网址短信",phoneNumber);
        String msgContent = datangSmsProperties.getUnicomUnsubTemplate();
        return sendSms(phoneNumber,msgContent);
    }

    @Override
    public boolean sendValidateNotice(String phoneNumber, String smsSign, String smsSuffix, String code) {
        //log.info("手机号:{},签名:{},短信下发验证码:{}",phoneNumber,smsSign,code);
        String template = datangSmsProperties.getValidateTemplate();
        String tplParamKey = datangSmsProperties.getValidateTemplateParamKey();
        return sendSms(phoneNumber,smsSign + template + StringUtils.trimToEmpty(smsSuffix),tplParamKey,code);
    }

    @Override
    public boolean sendSms(String phoneNumber, String template, String tplParamKey, String tplParamValue) {

        String msgContent = template.replace(tplParamKey, tplParamValue);

        return sendSms(phoneNumber, msgContent);
    }

    /**
     * 大唐短信发送新api升级
     * @param phoneNumber
     * @param msgContent
     * @return
     */
    @Override
    public boolean sendSms(String phoneNumber, String msgContent) {
        String cpid = datangSmsProperties.getUsernameV3();
        String apiKey = datangSmsProperties.getApiKeyV3();
        final String rawSign = StringUtils.substringBetween(msgContent, "【", "】");
        String excode = datangSmsProperties.getSignExcodeMap().get("【" + rawSign + "】");
        String sign = DigestUtils.md5DigestAsHex((cpid+msgContent+phoneNumber+excode+apiKey).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        if (StringUtils.isEmpty(excode)){
            log.error("手机号:{},内容:{},大唐短信发送失败,未配置扩展码!",phoneNumber, msgContent);
            return false;
        }
        try {
            ObjectNode dataNode = mapper.createObjectNode()
                    .put("cpid",cpid)
                    .put("msg",msgContent)
                    .put("mobiles", phoneNumber)
                    .put("excode", excode)
                    .put("sign", sign);
            //System.out.println(dataNode.toPrettyString());
            RequestBody body = RequestBody.create(JSON, dataNode.toString());
            Request request = new Request.Builder().url(datangSmsProperties.getSendSmsV3ApiUrl())
                    .post(body)
                    .build();
            Response response = client.newCall(request).execute();
            String content = response.body().string();
            log.info("手机号:{},内容:{},大唐短信发送结果:{}", phoneNumber, msgContent, content);
            //{"resultcode":0,"resultmsg":"成功","taskid":"T9046804257597"}
            final JsonNode tree = mapper.readTree(content);
            boolean isSuccess = tree.at("/resultcode").asInt(999) == 0;
            String msgId = tree.at("/taskid").asText();
            if(!isSuccess){
                log.error("手机号:{},内容:{},大唐短信发送失败!",phoneNumber, msgContent);
            }
            smsSendLogService.saveSendLog(phoneNumber, msgId, msgContent, isSuccess?-1:0);
            
            return isSuccess;

        } catch (Exception e) {
            log.info("大唐短信接口调用异常:", e);
        }

        return false;
    }

    ///**
    // * 大唐短信发送新api升级
    // * @param phoneNumber
    // * @param msgContent
    // * @return
    // */
    //@Deprecated
    //@Override
    //public boolean sendSmsV2(String phoneNumber, String msgContent) {
    //    String username = datangSmsProperties.getUsername();
    //    String password = datangSmsProperties.getPassword();
    //    String timestamp = DateUtil.formatFullTime(LocalDateTime.now(), DateUtil.FULL_TIME_PATTERN_WITH_MILL);
    //    String sign = DigestUtils.md5DigestAsHex((username + password + timestamp).getBytes(StandardCharsets.UTF_8)).toUpperCase();
    //
    //    try {
    //        String  encodeContent = URLEncoder.encode(msgContent, StandardCharsets.UTF_8.toString());
    //        ObjectNode itemNode = mapper.createObjectNode()
    //                .put("mobile",phoneNumber)
    //                .put("content", encodeContent);
    //        ObjectNode dataNode = mapper.createObjectNode()
    //                .put("username", username)
    //                .put("sign", sign)
    //                .put("timestamp", timestamp);
    //        dataNode.putArray("messagelist")
    //                .add(itemNode);
    //        //System.out.println(dataNode.toPrettyString());
    //        RequestBody body = RequestBody.create(JSON, dataNode.toString());
    //        Request request = new Request.Builder().url(datangSmsProperties.getSendSmsNewApiUrl())
    //                .post(body)
    //                .build();
    //        Response response = client.newCall(request).execute();
    //        String content = response.body().string();
    //        log.info("手机号:{},内容:{},大唐短信发送结果:{}", phoneNumber, msgContent, content);
    //        //{"result":"1","msgid":"87202308161636182069","resultdesc":"SUCCESS"}
    //        boolean isSuccess = "1".equals(mapper.readTree(content).at("/result").asText());
    //        if(!isSuccess){
    //            log.error("手机号:{},内容:{},大唐短信发送失败!",phoneNumber, msgContent);
    //        }
    //        return isSuccess;
    //
    //    } catch (Exception e) {
    //        log.info("大唐短信接口调用异常:", e);
    //    }
    //
    //    return false;
    //}

    //@Deprecated
    //@Override
    //public boolean sendSmsV1(String phoneNumber, String msgContent) {
    //    //if(LocalDateTime.now().isAfter(LocalDateTime.of(2023,6,20,22,0,0))){
    //    //    this.sendSmsHttpUrl = HttpUrl.parse(datangSmsProperties.getSendSmsApiUrlTemp());
    //    //    this.userId = datangSmsProperties.getUserIdTemp();
    //    //    this.apiKey = datangSmsProperties.getApiKeyTemp();
    //    //}else {
    //        this.sendSmsHttpUrl = HttpUrl.parse(datangSmsProperties.getSendSmsApiUrl());
    //        this.userId = datangSmsProperties.getUserId();
    //        this.apiKey = datangSmsProperties.getApiKey();
    //    //}
    //
    //    String ts = String.valueOf(System.currentTimeMillis());
    //    String sign = DigestUtils.md5DigestAsHex((userId + ts + apiKey).getBytes(StandardCharsets.UTF_8));
    //    HttpUrl httpUrl = this.sendSmsHttpUrl.newBuilder()
    //                                         .addQueryParameter("userid", userId)
    //                                         .addQueryParameter("ts", ts)
    //                                         .addQueryParameter("sign", sign)
    //                                         .addQueryParameter("mobile", phoneNumber)
    //                                         .addQueryParameter("msgcontent", msgContent)
    //                                         //.addQueryParameter("extnum",extnum)
    //                                         //.addQueryParameter("time",time)
    //                                         //.addQueryParameter("messageid",messageid)
    //                                         .build();
    //    Request request = new Request.Builder().url(httpUrl).build();
    //    try (Response response = client.newCall(request).execute()) {
    //        String content = response.body().string();
    //        DatangSmsResult result = mapper.readerFor(DatangSmsResult.class).readValue(content);
    //        log.info("手机号:{},内容:{},大唐短信发送结果:{}", phoneNumber, msgContent, content);
    //        boolean isSuccess = "0".equals(result.getCode());
    //        if(!isSuccess){
    //            log.error("手机号:{},内容:{},大唐短信发送失败!",phoneNumber, msgContent);
    //        }
    //        return isSuccess;
    //
    //    } catch (Exception e) {
    //        log.info("大唐短信接口调用异常:", e);
    //    }
    //
    //    return false;
    //}

    /**
     * 发送闪信 (闪信内容必须是预先审核过的)
     * @param phoneNumber
     * @return
     */
    @Override
    public boolean sendFlashSms(String phoneNumber) {

        String userId = datangSmsProperties.getUserIdForFlashSms();
        String apiKey = datangSmsProperties.getApiKeyForFlashSms();
        String msgContent = datangSmsProperties.getFlashSmsContent();
        String ts = String.valueOf(System.currentTimeMillis());
        String sign = DigestUtils.md5DigestAsHex((userId + ts + apiKey).getBytes(StandardCharsets.UTF_8));
        HttpUrl httpUrl = this.sendSmsHttpUrl.newBuilder()
                .addQueryParameter("userid", userId)
                .addQueryParameter("ts", ts)
                .addQueryParameter("sign", sign)
                .addQueryParameter("mobile", phoneNumber)
                .addQueryParameter("msgcontent", msgContent)
                //.addQueryParameter("extnum",extnum)
                //.addQueryParameter("time",time)
                //.addQueryParameter("messageid",messageid)
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {
            String content = response.body().string();
            DatangSmsResult result = mapper.readerFor(DatangSmsResult.class).readValue(content);
            log.info("手机号:{},内容:{},大唐闪信发送结果:{}", phoneNumber, msgContent, content);
            boolean isSuccess = "0".equals(result.getCode());
            if(!isSuccess){
                log.error("手机号:{},内容:{},大唐闪信发送失败!",phoneNumber, msgContent);
            }
            return isSuccess;
        } catch (Exception e) {
            log.info("大唐闪信接口调用异常:", e);
        }
        return false;
    }

    public static void main(String[] args) throws JsonProcessingException {
        ImmutableMap<String, String> tmlParamMap = ImmutableMap.of("orderid", "xxxxxxxx");
        String tmlParam =  new ObjectMapper().writeValueAsString(tmlParamMap);
        System.out.println("tmlParam = " + tmlParam);

        String abbreviate = StringUtils.abbreviate("13438828200", 3, 4);
        System.out.println("abbreviate = " + abbreviate);
    }
    @Override
    public boolean sendPPTVRightsNotice(String phoneNumber,Integer type) {
        log.info("手机号:{},下发pptv权益赠送短信",phoneNumber);
        String msgContent="";
        if(type==1){
             msgContent = datangSmsProperties.getPptvRightsVrbtTemplate();
        }else{
             msgContent = datangSmsProperties.getPptvRightsTemplate();
        }

        return sendSms(phoneNumber,msgContent);
    }

    @Override
    public boolean sendTianyiNotice(String phoneNumber, String code) {
        String template = datangSmsProperties.getValidateTemplateTianyi();
        String tplParamKey = datangSmsProperties.getValidateTemplateTianyiParamKey();
        return sendSms(phoneNumber, template, tplParamKey, code);


    }
    //@Override
    //public boolean sendNotify(String phoneNumber, String content) {
    //    String template = datangSmsProperties.getNotifyTemplate();
    //    content = template + content;
    //    return sendSms(phoneNumber,content);
    //}

    @Override
    public boolean sendUnifyValidateNotice(String phoneNumber, String code) {
        log.info("手机号:{},下发统一领取权益登录发送验证码短信",phoneNumber);
        String template = datangSmsProperties.getValidateTemplateUnify();
        String tplParamKey = datangSmsProperties.getValidateTemplateUnifyParamKey();
        return sendSms(phoneNumber, template, tplParamKey, code);
    }
    @Override
    public boolean sendElemeValidateNotice(String phoneNumber, String code) {
        log.info("手机号:{},饿了么权益领取发送激活码",phoneNumber);
        String template = datangSmsProperties.getValidateTemplateEleme();
        String tplParamKey = datangSmsProperties.getValidateTemplateElemeParamKey();
        return sendSms(phoneNumber, template, tplParamKey, code);
    }

    @Override
    public boolean sendCheckBaijinMemberNotice(String phoneNumber) {
        log.info("手机号:{},白金会员[随心听]赠送会员权益短信模板",phoneNumber);
        String template = datangSmsProperties.getCheckBaijinMemberTemplate();
        return sendSms(phoneNumber,template);
    }

    @Override
    @Async
    public void sendCrackBaijinMemberNotice(String phoneNumber) {
        log.info("手机号:{},白金会员[随心听]赠送会员权益短信模板",phoneNumber);
        try {
            //短信延迟5秒
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String template = datangSmsProperties.getCrackBaijinMemberTemplate();
        sendSms(phoneNumber,template);
    }

    @Override
    public boolean sendCheckBaijinysMemberNotice(String phoneNumber) {
        log.info("手机号:{},白金会员[宜搜]赠送会员权益短信模板",phoneNumber);
        String template = datangSmsProperties.getCheckBaijinysMemberTemplate();
        return sendSms(phoneNumber,template);
    }


    @Override
    @Async
    public void sendCrackBaijinysMemberNotice(String phoneNumber) {
        log.info("手机号:{},白金会员[宜搜]赠送会员权益短信模板",phoneNumber);
        try {
            //短信延迟5秒
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String template = datangSmsProperties.getCrackBaijinysMemberTemplate();
        sendSms(phoneNumber,template);
    }

    @Override
    public boolean sendCheckBaijindyMemberNotice(String phoneNumber) {
        log.info("手机号:{},白金会员[动意]赠送会员权益短信模板",phoneNumber);
        String template = datangSmsProperties.getCheckBaijindyMemberTemplate();
        return sendSms(phoneNumber,template);
    }

    @Override
    @Async
    public void sendCrackBaijindyMemberNotice(String phoneNumber) {
        log.info("手机号:{},白金会员[动意]赠送会员权益短信模板", phoneNumber);
        try {
            //短信延迟5秒
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String template = datangSmsProperties.getCrackBaijindyMemberTemplate();
        sendSms(phoneNumber, template);
    }
    @Override
    @Async
    public void sendCrackBactMemberNotice(String phoneNumber) {
        log.info("手机号:{},20元渠道包月包赠送会员领取权益短信模板",phoneNumber);
        try {
            //短信延迟5秒
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String template = datangSmsProperties.getCrackBatcMemberTemplate();
        sendSms(phoneNumber,template);
    }
    @Override
    @Async
    public void sendCrackBactNotMemberNotice(String phoneNumber) {
        log.info("手机号:{},20元渠道包月包赠送会员未领取权益短信模板",phoneNumber);
        try {
            //短信延迟5秒
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String template = datangSmsProperties.getCrackBatcNotMemberTemplate();
        sendSms(phoneNumber,template);
    }

    @Override
    public boolean sendVrbtRightsNotice(String phoneNumber) {
        log.info("手机号:{},视频彩铃权益赠送通知短信模板",phoneNumber);
        String template = datangSmsProperties.getVrbtRightsTemplate();
        return sendSms(phoneNumber,template);
    }

    @Override
    public boolean sendUnifyTemplateNotice(String phoneNumber, String code) {
        log.info("手机号:{},权益领取统一登录短信模板",phoneNumber);
        String template = datangSmsProperties.getValidateUnifyBizTemplate();
        String tplParamKey = datangSmsProperties.getValidateUnifyBizTemplateParamKey();
        return sendSms(phoneNumber, template, tplParamKey, code);
    }

    @Override
    public boolean sendVrbtNotice(String phoneNumber) {
        log.info("手机号:{},视频彩铃订购通知短信模板",phoneNumber);
        String template = datangSmsProperties.getVrbtTemplate();
        return sendSms(phoneNumber,template);
    }

    @Override
    public boolean sendShangHaiXuanShiTemplateNotice(String phoneNumber, String code) {
        log.info("手机号:{},上海移动炫视视频彩铃订购通知短信模板",phoneNumber);
        String template = datangSmsProperties.getShanghaiValidateTemplate();
        String tplParamKey = datangSmsProperties.getShanghaiValidateTemplateParamKey();
        return sendSms(phoneNumber, template, tplParamKey, code);
    }

}
