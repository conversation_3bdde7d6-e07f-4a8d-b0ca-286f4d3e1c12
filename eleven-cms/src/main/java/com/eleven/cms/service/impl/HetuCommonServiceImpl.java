package com.eleven.cms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.JiangsuYidongService;
import com.eleven.cms.remote.WujiongCrackService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.WujiongCrackResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("hetuCommonService")
@Slf4j
public class HetuCommonServiceImpl implements IBizCommonService {

    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;

    //贵州河图限制号段
    public static final List<String> GUIZHOU_HETU_LIMIT_NUM_SEGMENT
            = Arrays.asList("136", "137", "138","139");

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        if (BIZ_CHANNEl_HETU_GZ.equals(subscribe.getChannel()) && subscribe.getMobile().matches("13[6-9]\\d{8}")) {
//            subscribe.setChannel(BIZ_TYPE_GZYD_SXX_HY);
//            subscribe.setBizType(BIZ_TYPE_GZYD_SXX_HY);
//            return SpringUtil.getBean(GuizhouYidongCommonServiceImpl.class).getSmsCode(subscribe);
            return Result.error("暂时无法开通");
        }
        //取消运营商限制
//        if(StringUtils.isEmpty(subscribe.getIsp()) || !MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())){
//            return Result.msgIspRestrict();
//        }
        //同一手机号每月只能开通一次
        Integer subscribeSuccessCount = subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, subscribe.getMobile())
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .eq(Subscribe::getBizType,BIZ_TYPE_HETU)
                .between(Subscribe::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .count();
        if(subscribeSuccessCount>0){
            final Result<Object> bizExistsResult = Result.bizExists("你已开通过,请勿重复开通");
            return bizExistsResult;
        }

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final String id = subscribe.getId();
        final String mobile = subscribe.getMobile();

        final BillingResult billingResult = yidongVrbtCrackService.getSms(mobile, subscribe.getChannel());
        if (billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("验证码已发送", id);
        } else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if ("402".equals(billingResult.getCode())) {
                subscribeService.removeSubscribeDbAndEs(id);
                return Result.error("请求验证码过快!");
            }
//            return Result.error("获取验证码失败,请稍后再试!");

            try {
                String errorMsg="{\"code\":\""+billingResult.getCode()+"\",\"message\":\""+billingResult.getMessage()+"\"}";
                return Result.errorSmsDelayMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsDelayerror();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //同一手机号每月只能开通一次
        Integer subscribeSuccessCount = subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, subscribe.getMobile())
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .eq(Subscribe::getBizType,BIZ_TYPE_HETU)
                .between(Subscribe::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .count();
        if(subscribeSuccessCount>0){
            final Result<Object> bizExistsResult = Result.bizExists("你已开通过,请勿重复开通");
            return bizExistsResult;
        }

        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //此处保存已提交验证码
        if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setBizTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        //曹伟破解
        yidongVrbtCrackService.smsCode(subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
        return Result.ok("订阅成功");
    }
}
