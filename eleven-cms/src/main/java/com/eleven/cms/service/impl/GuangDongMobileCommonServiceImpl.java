package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.*;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * 广东移动存量业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:25
 **/
@Service("guangDongMobileCommonService")
@Slf4j
public class GuangDongMobileCommonServiceImpl implements IBizCommonService {
    public static final String YIDONG_GUANGDONG_STOCK_DUPLICATE_KEY_PREFIX = "gdyd::stock:";
    public static final long YIDONG_GUANGDONG_STOCK_SMS_INVALID_CACHE_SECONDS = 60;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IGuangDongMobileService guangDongMobileService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        if(StringUtils.isEmpty(subscribe.getIsp()) || !MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())){
            return Result.msgIspRestrict();
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        Result<?> result=guangDongMobileService.sendSms(subscribe.getMobile(),subscribe.getChannel());
        if (result.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setIspOrderNo(result.getOrderId());
            upd.setExtra(result.getNumberId());
            subscribeService.updateSubscribeDbAndEs(upd);

            Result resultOrder=new Result();
            resultOrder.setMessage("验证码已发送");
            resultOrder.setOrderId(result.getOrderId());
            resultOrder.setNumberId(result.getNumberId());
            resultOrder.setCode(CommonConstant.SC_OK_200);
            resultOrder.setResult(subscribe.getId());
            return resultOrder;
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+result.getCode()+"\",\"message\":\""+result.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = YIDONG_GUANGDONG_STOCK_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", YIDONG_GUANGDONG_STOCK_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        Result<?> submitResult = guangDongMobileService.submitSms(subscribe.getMobile(),smsCode,subscribe.getIspOrderNo(),subscribe.getExtra(),subscribe.getChannel());
        upd.setResult(submitResult.getMessage());
        upd.setModifyTime(new Date());
        if (submitResult.isOK()) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setOpenTime(new Date());
            upd.setExtra(submitResult.getResult().toString());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }
            subscribeService.saveChannelLimit(subscribe);

            //加入包月延迟校验队列
            if(BIZ_CHANNEL_GDYD_LLB.equals(subscribe.getChannel())) {
                rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            }

        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        //外部渠道加入回调延迟队列(暂时不使用队列)
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }
        return submitResult;
    }
}
