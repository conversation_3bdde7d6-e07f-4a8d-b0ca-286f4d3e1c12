package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.JunboLlbService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.JacksonUtils;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2024-08-13 14:21
 */
@Service("fuJianYiDongLlbServiceImpl")
public class FuJianYiDongLlbServiceImpl implements IBizCommonService {

    @Autowired
    FuJianYiDongService fuJianYiDongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    IChannelService channelService;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        String ispOrderNo = JunboLlbService.getSysOrderId();

        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("mobile", subscribe.getMobile());
        objectNode.put("channel", subscribe.getChannel());

        Result result = fuJianYiDongService.getSmsCode(objectNode);
        if (result.isOK()) {
            subscribe.setIspOrderNo(ispOrderNo);
            subscribeService.createSubscribeDbAndEs(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
            return result;
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        upd.setModifyTime(new Date());

        ObjectNode objectNode = JacksonUtils.createObjectNode();
        objectNode.put("id", subscribe.getId());
        objectNode.put("mobile", subscribe.getMobile());
        objectNode.put("channel", subscribe.getChannel());
        objectNode.put("subChannel", subscribe.getSubChannel());
        objectNode.put("smsCode", subscribe.getSmsCode());
        objectNode.put("source", subscribe.getSource());
        objectNode.put("referer", subscribe.getReferer());

        Result result = fuJianYiDongService.order(objectNode);
        if (result.isOK()) {
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                // 订购成功且非外放CPA则上报广告平台
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }
        }
        // 外放CPA订购结果通知回调
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }
        return result;
    }
}
