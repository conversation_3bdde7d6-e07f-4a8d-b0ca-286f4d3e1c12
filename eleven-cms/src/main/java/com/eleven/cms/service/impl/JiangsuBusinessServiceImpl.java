package com.eleven.cms.service.impl;

import com.eleven.cms.config.AnhuiProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.JsydDelayedMessage;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.JiangsuYidongService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.JiangsuResponseCreateOrder;
import com.eleven.cms.vo.JiangsuResponseSmsValidate;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 江苏移动视频彩铃
 *
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class JiangsuBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    AnhuiProperties anhuiProperties;
    @Autowired
    ISmsValidateService smsValidateService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;

    private static final String reportUrl = "https://crbt.cdyrjygs.com/vrbt_jiangsu";

    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }

//        Integer limitAmount = provinceBusinessChannelConfigService.getLimitByChannelAndProvince(subscribe.getChannel(), subscribe.getProvince());
//        if (limitAmount > 0) {
//            //获取当前序列值
//            Integer currentCount = subscribeService.getIncrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//            if (currentCount >= limitAmount) {
//                log.error("渠道省份开通数量已达到限量,渠道号:{},省份:{}", subscribe.getChannel(), subscribe.getProvince());
//                sendAnhuiLimitSms(limitAmount);
//                return Result.error("特定省份移动用户限量");
//            }
//        }
        return SpringContextUtils.getBean(JiangsuBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    public Result receiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            try {
                JiangsuResponseCreateOrder jiangsuResponseCreateOrder = jiangsuYidongService.createOrder(mobile, subscribe.getReferer(), subscribe.getDeviceInfo(), reportUrl, jiangsuYidongService.getToken());
                if (jiangsuResponseCreateOrder.isOk()) {
                    String orderId = jiangsuResponseCreateOrder.getContent().getOrderId();
                    subscribe.setIspOrderNo(orderId);
                    subscribeService.createSubscribeDbAndEs(subscribe);
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送", subscribe.getId());
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                JiangsuResponseSmsValidate jiangsuResponseSmsValidate = jiangsuYidongService.smsValidate(mobile, target.getIspOrderNo(), smsCode, jiangsuYidongService.getToken());
                if (jiangsuResponseSmsValidate.isOk()) {
                    Subscribe upd = new Subscribe();
                    upd.setId(target.getId());
                    upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                    upd.setResult(jiangsuResponseSmsValidate.getContent().getRetMsg());
                    subscribeService.updateSubscribeDbAndEs(upd);
                    redisDelayedQueueManager.addJsyd(JsydDelayedMessage.builder().mobile(mobile).count(1).orderId(target.getIspOrderNo()).orderTimeStamp(System.currentTimeMillis() / 1000).build(), 1, TimeUnit.MINUTES);
                    return Result.ok("提交验证码成功");
                } else {
                    Subscribe upd = new Subscribe();
                    upd.setId(target.getId());
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                    upd.setResult(jiangsuResponseSmsValidate.getContent().getRetMsg());
                    subscribeService.updateSubscribeDbAndEs(upd);
                    return Result.error("提交验证码失败");
                }
            } catch (Exception e) {
                log.error("系统错误:{}", e);
                return Result.error("系统错误，请稍后再试");
            }
        }
    }
}
