package com.eleven.cms.service.impl.pay;

import com.eleven.cms.douyinduanju.service.IMiniAppDuanJuOrderService;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.service.pay.PayNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class MaiHeDuanJuNotifyImpl implements PayNotifyService {

    @Resource
    IMiniAppDuanJuOrderService iMiniAppDuanJuOrderService;

    @Override
    public void handleNotify(String orderNo, Map<String, String> requestParams) {
        String transactionId = "";
        Date payTime = null;
        if (Objects.nonNull(requestParams)) {
            transactionId = requestParams.get("out_order_no");
            payTime = new Date(Long.parseLong(requestParams.get("event_time")) * 1000);
        }
        iMiniAppDuanJuOrderService.updateOrderPayStatus(orderNo, transactionId, payTime);
    }
    @Override
    public PayBusineesTypeEnum getBusinessType() {
        return PayBusineesTypeEnum.MAI_HE_JU_CHANG_DUAN_JU;
    }
}
