package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.BizPageConfig;
import com.eleven.cms.mapper.BizPageConfigMapper;
import com.eleven.cms.service.IBizPageConfigService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2023-08-10 11:42
 */
@Service
public class BizPageConfigServiceImpl extends ServiceImpl<BizPageConfigMapper, BizPageConfig> implements IBizPageConfigService {
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_PAGE_CONFIG_CACHE, key = "#root.methodName + '-' + #id", unless = "#result==null")
    public BizPageConfig selectById(String id) {
        return this.getById(id);
    }
}

