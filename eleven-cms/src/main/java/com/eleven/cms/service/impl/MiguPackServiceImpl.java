package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.MiguPack;
import com.eleven.cms.entity.RightsPack;
import com.eleven.cms.mapper.MiguPackMapper;
import com.eleven.cms.mapper.RightsPackMapper;
import com.eleven.cms.service.IMiguPackService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 咪咕业务包
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Service
public class MiguPackServiceImpl extends ServiceImpl<MiguPackMapper, MiguPack> implements IMiguPackService {

	@Autowired
	private MiguPackMapper miguPackMapper;
	@Autowired
	private RightsPackMapper rightsPackMapper;
	@Autowired
    private BizProperties bizProperties;
	@Autowired
	private UnifyRightsFeignClient unifyRightsFeignClient;
	public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	@Override
	@Transactional
	public void saveMain(MiguPack miguPack, List<RightsPack> rightsPackList) {
		miguPackMapper.insert(miguPack);
		if(rightsPackList!=null && rightsPackList.size()>0) {
			for(RightsPack entity:rightsPackList) {
				//外键设置
				entity.setPackId(miguPack.getId());
				rightsPackMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void updateMain(MiguPack miguPack,List<RightsPack> rightsPackList) {
		miguPackMapper.updateById(miguPack);

		//1.先删除子表数据
		rightsPackMapper.deleteByMainId(miguPack.getId());

		//2.子表数据重新插入
		if(rightsPackList!=null && rightsPackList.size()>0) {
			for(RightsPack entity:rightsPackList) {
				//外键设置
				entity.setPackId(miguPack.getId());
				rightsPackMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void delMain(String id) {
		rightsPackMapper.deleteByMainId(id);
		miguPackMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			rightsPackMapper.deleteByMainId(id.toString());
			miguPackMapper.deleteById(id);
		}
	}

	@Override
	public List<MiguPack> selectMain(String orderKey) {
		List<String> serviceIds= Lists.newArrayList();
		serviceIds.add(BizConstant.BIZ_TYPE_VRBT_SERVICE_ID);
		LambdaQueryWrapper<MiguPack> queryWrapper = new LambdaQueryWrapper<>();
		if(BizConstant.BIZ_TYPE_VRBT.equals(orderKey)){
			queryWrapper.in(MiguPack::getServiceId, serviceIds);
		}else{
			queryWrapper.notIn(MiguPack::getServiceId, serviceIds);
		}
		queryWrapper.groupBy(MiguPack::getServiceId);
		List<MiguPack> list=this.baseMapper.selectList(queryWrapper);
		if(list!=null && list.size()>0){
			list.forEach(miguPack -> {
				final List<String> channelList = bizProperties.getChannelByServiceId(miguPack.getServiceId());
				if(channelList!=null&&channelList.size()>0){
					miguPack.setChannelCode(channelList.get(0));
				}else{
					if(BizConstant.BIZ_TIANYI_SERVICE_ID.equals(miguPack.getServiceId())){
						miguPack.setChannelCode(BizConstant.BIZ_CHANNEL_CODE);
					}
				}
			});
		}
		return list;
	}
	@Override
	public Optional<MiguPack> queryMiguPackDetail(String serviceId,String packName) {
		return this.lambdaQuery()
				.eq(MiguPack::getServiceId, serviceId)
				.eq(MiguPack::getPackName, packName)
				.list()
				.stream()
				//找到当月最后一个订单
				.max(Comparator.comparing(MiguPack::getCreateTime));
	}

	@Override
	public List<MiguPack> queryPackByBusinessId(MiguPack pack) {
		LambdaQueryWrapper<MiguPack> queryWrapper = new LambdaQueryWrapper<>();
		if(StringUtils.isNotBlank(pack.getTitleName())){
			queryWrapper.eq(MiguPack::getTitleName, pack.getTitleName());
		}
		queryWrapper.orderByDesc(MiguPack::getServiceId);
		List<MiguPack> list=this.baseMapper.selectList(queryWrapper);
		FebsResponse listFebs=unifyRightsFeignClient.queryPackByBusinessId(StringUtils.isNotBlank(pack.getTitleName())?pack.getTitleName():"");
		if(listFebs.isOK()){
			try{
				List<MiguPack> lists = mapper.readValue(listFebs.get("data").toString(), List.class);
				if(lists!=null && lists.size()>0){
					list.addAll(lists);
				}
			}catch (Exception e) {
				e.printStackTrace();
			}
		}
		return list;
	}


	/**
	 * 查询支付宝有效的权益充值列表（排除刷单渠道）
	 * @return
	 */
	@Override
	@Cacheable(cacheNames = CacheConstant.CMS_RIGHTS_MIGU_PACK_LIST_CACHE,key = "#serviceId",unless = "#result==null")
	public List<MiguPack> queryMiguPackList(String serviceId){
		return this.lambdaQuery().eq(MiguPack::getServiceId,serviceId).orderByDesc(MiguPack::getCreateTime).list();
	}
}
