package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.MiguChannelConfig;
import com.eleven.cms.entity.*;
import com.eleven.cms.mapper.VrbtAppUserOrderMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import me.zhyd.oauth.utils.UuidUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

/**
 * @Description: 视频彩铃app用户订单
 * @Author: jeecg-boot
 * @Date:   2024-04-16
 * @Version: V1.0
 */
@Service
public class VrbtAppUserOrderServiceImpl extends ServiceImpl<VrbtAppUserOrderMapper, VrbtAppUserOrder> implements IVrbtAppUserOrderService {
    @Autowired
    IVrbtAppUserService vrbtAppUserService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IJunboChargeLogService junboChargeLogService;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;

    @Override
    @Transactional
    public Result<?> pointsUse(String mobile, String consigneeMobile, String consigneeName, String address, String productName) {
        VrbtAppUser vrbtAppUser=vrbtAppUserService.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).select(VrbtAppUser::getTotalPoints,VrbtAppUser::getVersion,VrbtAppUser::getMobile,VrbtAppUser::getId).orderByDesc(VrbtAppUser::getRegisterTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrbtAppUser==null){
            return Result.needLogin(0);
        }
        if(vrbtAppUser.getTotalPoints()==null || vrbtAppUser.getTotalPoints().intValue()<=0){
            return Result.error("积分不足",0);
        }
        if(vrbtAppUser.getTotalPoints().intValue()<BizConstant.PRODUCT_NAME_POINTS.intValue()){
            return Result.error("积分不足",vrbtAppUser.getTotalPoints());
        }
        //判断用户是否包月
        final RemoteResult remoteResult =miguApiService.asMemberQuery(mobile,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        //判断用户是否有订购记录
        boolean signInSub=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getChannel,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174).eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).count()>0;
        //设置包月状态
        Integer subStatus=(remoteResult.isAsMember() && signInSub)?BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS:BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
        //设置总积分 兑换礼物减少4000积分
        Integer totalPoints=Integer.valueOf(vrbtAppUser.getTotalPoints().intValue()-BizConstant.PRODUCT_NAME_POINTS.intValue());
        vrbtAppUser.setSubStatus(subStatus);
        vrbtAppUser.setTotalPoints(totalPoints);
        vrbtAppUserService.editVrbtAppUser(vrbtAppUser);


        VrbtAppUserOrder vrbtAppUserOrder=new VrbtAppUserOrder();
        /**订单号*/
        vrbtAppUserOrder.setOrderNo(UuidUtils.getUUID());
        /**手机号*/
        vrbtAppUserOrder.setMobile(mobile);
        /**渠道号*/
        vrbtAppUserOrder.setChannel(MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        /**订单积分*/
        vrbtAppUserOrder.setOrderPoints(BizConstant.PRODUCT_NAME_POINTS);
        /**包月状态(-1=初始,0=未包月,1=已包月)*/
        vrbtAppUserOrder.setSubStatus(subStatus);
        /**订单状态:0=兑换中,1=兑换成功,2=兑换失败*/
        vrbtAppUserOrder.setOrderStatus(0);
        /**收货人手机号*/
        vrbtAppUserOrder.setConsigneeMobile(consigneeMobile);
        /**收货人名称*/
        vrbtAppUserOrder.setConsigneeName(consigneeName);
        /**用户地址*/
        vrbtAppUserOrder.setAddress(address);
        /**产品名称*/
        vrbtAppUserOrder.setProductName(productName);
        /**创建时间*/
        vrbtAppUserOrder.setCreateTime(new Date());
        this.save(vrbtAppUserOrder);
        return Result.ok("兑换中",totalPoints);
    }
    @Override
    public Result<?> orderList(String mobile) {
        VrbtAppUser vrbtAppUser=vrbtAppUserService.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).select(VrbtAppUser::getTotalPoints,VrbtAppUser::getMobile,VrbtAppUser::getId).orderByDesc(VrbtAppUser::getRegisterTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrbtAppUser==null){
            return Result.needLogin(0);
        }
        List<VrbtAppUserOrder> orderList=this.lambdaQuery().eq(VrbtAppUserOrder::getMobile,mobile).select(VrbtAppUserOrder::getProductName,VrbtAppUserOrder::getCreateTime,VrbtAppUserOrder::getConsigneeName,VrbtAppUserOrder::getConsigneeMobile,VrbtAppUserOrder::getAddress,VrbtAppUserOrder::getOrderStatus).orderByDesc(VrbtAppUserOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).list();
        return Result.ok("查询成功",orderList);
    }

    @Override
    public Result<?> rechargeList(String mobile) {
        VrbtAppUser vrbtAppUser=vrbtAppUserService.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).select(VrbtAppUser::getTotalPoints,VrbtAppUser::getMobile,VrbtAppUser::getId).orderByDesc(VrbtAppUser::getRegisterTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrbtAppUser==null){
            return Result.needLogin(0);
        }
        List<JunboChargeLog> rechargeList=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getMobile,mobile).eq(JunboChargeLog::getChannel,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174).select(JunboChargeLog::getCouponName,JunboChargeLog::getStatus,JunboChargeLog::getCreateTime).orderByDesc(JunboChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).list();
        return Result.ok("查询成功",rechargeList);
    }

    @Override
    public Result<?> rechargeVrbt(String mobile) {
        VrbtAppUser vrbtAppUser=vrbtAppUserService.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).select(VrbtAppUser::getTotalPoints,VrbtAppUser::getMobile,VrbtAppUser::getId).orderByDesc(VrbtAppUser::getRegisterTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrbtAppUser==null){
            return Result.needLogin(0);
        }
        //判断用户是否包月
        final RemoteResult remoteResult =miguApiService.asMemberQuery(mobile,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        boolean signInSub=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getChannel,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174).eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).count()<=0;
        if(!remoteResult.isAsMember() || signInSub){
            return Result.error("未包月",0);
        }
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        if (cmsCrackConfig == null) {
            return Result.error("系统错误",0);
        }
        IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(MiGuOuFenAppRightsServiceImpl.class);
        FebsResponse febsResponse = businessRightsSubService.createScheduledRecharge(mobile, mobile, cmsCrackConfig.getServiceId(), "vrbt_app_rights_pack_1", "mgyyvrbt", MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        return Result.error(Integer.valueOf(febsResponse.get("code").toString()),febsResponse.get("message").toString());
    }


    @Override
    public Result<?> queryVrbt(String mobile) {
        VrbtAppUser vrbtAppUser=vrbtAppUserService.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).select(VrbtAppUser::getTotalPoints,VrbtAppUser::getMobile,VrbtAppUser::getId).orderByDesc(VrbtAppUser::getRegisterTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrbtAppUser==null){
            return Result.needLogin(0);
        }
        //判断用户是否领取视频彩铃
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        boolean isRecharge = junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getMobile, mobile).eq(JunboChargeLog::getServiceId, cmsCrackConfig.getServiceId()).eq(JunboChargeLog::getPackName, "vrbt_app_rights_pack_1").eq(JunboChargeLog::getCouponId, "mgyyvrbt").count() > 0;
        if(isRecharge){
            return Result.error("已领取",vrbtAppUser.getTotalPoints());
        }
        return Result.ok("未领取",vrbtAppUser.getTotalPoints());
    }
}
