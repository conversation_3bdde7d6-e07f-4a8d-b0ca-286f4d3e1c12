package com.eleven.cms.service;

import com.eleven.cms.vo.HeTuFenShengNotify;
import org.jeecg.common.api.vo.Result;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 11:47
 **/
public interface IHeTuFenShengService {

    Result<?> sendSms(String mobile, String channel);

    Result<?> submitOrder(String mobile,String code,String ispOrderNo,String ip,String channel);

    HeTuFenShengNotify heTuFenShengNotify(String signature,String timestamp,String jsonNode,String transactionId,String  tel,String  busiSerial,String  respCode,String  respMsg,String  orderStatus);



}
