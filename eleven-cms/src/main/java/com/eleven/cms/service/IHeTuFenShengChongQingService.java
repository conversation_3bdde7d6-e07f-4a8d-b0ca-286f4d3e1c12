package com.eleven.cms.service;

import com.eleven.cms.vo.HeTuFenShengNotify;
import org.jeecg.common.api.vo.Result;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 11:47
 **/
public interface IHeTuFenShengChongQingService {

    Result<?> sendSms(String mobile, String channel,String appName);

    Result<?> submitOrder(String mobile,String code,String ispOrderNo,String channel,String appName);

    HeTuFenShengNotify huyuOrderChongQingNotify(String requestBody);

    boolean isMember(String mobile, String channel);
}
