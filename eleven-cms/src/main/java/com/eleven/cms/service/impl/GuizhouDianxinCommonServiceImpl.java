package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.GuizhouDianxinService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.GansuMobileResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("guizhouDianxinCommonService")
@Slf4j
public class GuizhouDianxinCommonServiceImpl implements IBizCommonService {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    GuizhouDianxinService guizhouDianxinService;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        try {
            for (int i = 0; i < GZDX_CHANNEL_LIST.size(); i++) {
                if (guizhouDianxinService.check(subscribe.getMobile(), GZDX_CHANNEL_LIST.get(i)).isOK()) {
                    subscribe.setRemark(GZDX_CHANNEL_LIST.get(i));
                    break;
                }
            }
            //循环完成还没找到可以开通的业务直接返回错误
            if (!GZDX_CHANNEL_LIST.contains(subscribe.getRemark())) {
                return Result.error("暂无订购资格!");
            }
            GansuMobileResult gansuMobileResult = guizhouDianxinService.getSms(subscribe.getMobile(), subscribe.getRemark());
            if (gansuMobileResult.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                subscribeService.createSubscribeDbAndEs(subscribe);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
//                return Result.error("获取验证码失败");

                try {
                    String errorMsg="{\"status\":\""+gansuMobileResult.getStatus()+"\",\"code\":\""+gansuMobileResult.getCode()+"\",\"message\":\""+gansuMobileResult.getResMsg()+"\",\"result\":\""+gansuMobileResult.getResult()+"\"}";
                    return Result.errorSmsMsg(errorMsg);
                } catch (Exception e) {
                    log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                    return Result.msgSmsError();
                }
            }
        } catch (Exception e) {
            return Result.error("获取验证码失败");
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        try {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setBizTime(new Date());
            GansuMobileResult gansuMobileResult = guizhouDianxinService.smsCode(subscribe.getMobile(), subscribe.getSmsCode(), subscribe.getRemark());
            if (gansuMobileResult.isOK()) {
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setResult("订购成功");
                subscribeService.updateSubscribeDbAndEs(upd);
                //信息流广告转化上报
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                } else {
                    channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
                }
                return Result.error("订阅成功");
            } else {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(gansuMobileResult.getResMsg());
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error("订阅失败");
            }
        } catch (Exception e) {
            return Result.error("订阅失败");
        }
    }
}
