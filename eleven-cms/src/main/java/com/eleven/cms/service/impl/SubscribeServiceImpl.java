package com.eleven.cms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.ad.AdReportService;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.*;
import com.eleven.cms.dto.BusinessOrderLog;
import com.eleven.cms.entity.*;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.mapper.SubscribeMapper;
import com.eleven.cms.queue.*;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.shanghaimobile.properties.ShanghaiMobileBusiness;
import com.eleven.cms.shanghaimobile.service.IShanghaiMobileService;
import com.eleven.cms.shanghaimobile.util.HttpContextUtil;
import com.eleven.cms.shanghaimobile.util.ShanghaiMobileConstant;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.eleven.cms.util.BizConstant.*;
import static com.eleven.cms.util.BizConstant.getBizTypeByMiguChannel;

/**
 * @Description: 渠道订阅
 * @Author: jeecg-boot
 * @Date: 2020-09-23
 * @Version: V1.0
 */
@Service
@Slf4j
public class SubscribeServiceImpl extends ServiceImpl<SubscribeMapper, Subscribe> implements ISubscribeService {
    @Autowired
    private BizProperties bizProperties;
    @Autowired
    private DianxinVrbtProperties dianxinVrbtProperties;


    private static final String MOBILE_AUTH_KEY_PREFIX = "mobileAuth:";
    //token 缓存时间 10分钟
    private static final long MOBILE_AUTH_CATCHE_TTL_SECONDS = 600L;
    private static final String MOBILE_AUTH_VALUE = "VALID";

    private static final String MOBILE_SEND_SMS_LIMIT_KEY_PREFIX = "mobileSendSmsLimit:";
    //定义为90秒,和开通等待短信验证码时间保持一致
    private static final long MOBILE_SEND_SMS_CATCHE_TTL_SECONDS = 60;

    //定义为5秒,和开通等待短信验证码时间保持一致
    private static final long SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS = 5;

    private static final String MOBILE_SEND_SMS_LIMIT_VALUE = "SMS_LIMIT";
    //破解计费短息发送间隔周期为2分钟
    //private static final long MOBILE_SEND_SMS_BILLING_CRACK_LIMIT_SECONDS = 120;

    private static final String MOBILE_MONTH_EXISTS_KEY_PREFIX = "mobileMonthExists:";
    private static final long MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS = 90;
    private static final String MOBILE_MONTH_EXISTS_VALUE = "BIZ_EXISTS";

    //private static final String MOBILE_VRBT_MONTH_EXISTS_KEY_PREFIX = "mobileVrbtMonthExists:";
    //private static final long MOBILE_VRBT_MONTH_EXISTS_CATCHE_TTL_SECONDS = 90;
    //private static final String MOBILE_VRBT_MONTH_EXISTS_VALUE = "EXISTS";

    public static final String DIANXIN_DAY_DUPLICATE_KEY_PREFIX = "dianxin::dayDuplicate:";
    public static final String DIANXIN_SMS_INVALID_KEY_PREFIX = "dianxin::smsInvalid:";
    public static final long DIANXIN_SMS_INVALID_CACHE_SECONDS = 60;
    //电信订单由于接口每天有访问限制,一个小时内不再重复下单
    public static final String DIANXIN_ORDER_FREQUENCY_KEY_PREFIX = "dianxin::orderFrequency:";
    public static final long DIANXIN_ORDER_FREQUENCY_CACHE_SECONDS = 3600L;

    public static final String SHANGHAIYIDONG_SMS_INVALID_KEY_PREFIX = "shanghaiyidong::SmsInvalid:";
    //东莞移动
    public static final String DGYD_SMS_INVALID_KEY_PREFIX = "dgyd::SmsInvalid:";

    //省份-四川
    private static String SICHUAN_PROVINCE = "四川";

    //省份-上海
    private static String SHANGHAI_PROVINCE = "上海";

    //省份-河北
    private static String HEBEI_PROVINCE = "河北";

    //省份-甘肃
    private static String GANSU_PROVINCE = "甘肃";

    //省份-甘肃
    private static String GUANGXI_PROVINCE = "广西";

    //省份-甘肃
    private static String HUNAN_PROVINCE = "湖南";

    //省份-重庆
    private static String CHONGQING_PROVINCE = "重庆";

    //省份-河北
    private static String HEILONGJIANG_PROVINCE = "黑龙江";

    //省份-新疆
    private static String XINJIANG_PROVINCE = "新疆";

    //省份-湖北
    private static String HUBEI_PROVINCE = "湖北";


    //省份-江西
    private static String JIANGXI_PROVINCE = "江西";

    //省份-新疆
    private static String NINGXIA_PROVINCE = "宁夏";

    //省份-贵州
    private static String GUIZHOU_PROVINCE = "贵州";

    //省份-广东
    private static String GUANGDONG_PROVINCE = "广东";

    //省份-海南
    private static String HAINAN_PROVINCE = "海南";

    //省份-安徽
    private static String ANHUI_PROVINCE = "安徽";

    //省份-山东
    private static String SHANDONG_PROVINCE = "山东";

    //省份-江苏
    private static String JIANGSU_PROVINCE = "江苏";

    //省份-河南
    private static String HENAN_PROVINCE = "河南";

    //省份-辽宁
    private static String LIAONING_PROVINCE = "辽宁";

    //省份-云南
    private static String YUNAN_PROVINCE = "云南";

    //省份-吉林
    private static String JILIN_PROVINCE = "吉林";

    //省份-山西
    private static String SHANXI_PROVINCE = "山西";


//    final List<String> BJHY_CHANNEL_LIST =Lists.newArrayList("00210PP","00210U1");

//    final List<String> YISHOU_CHANNEL_LIST =Lists.newArrayList("00210H0","00210TY","00210TZ");

    //    final List<String> DONGYI_CHANNEL_LIST =Lists.newArrayList("00210T7","00210U3","00210U4");

//    final List<String> YYQQB_CHANNEL_LIST =Lists.newArrayList("00210VO","002103M","002105C","00210VV","00210W0");


    private static final Interner<String> interner = Interners.newWeakInterner();

    private final ObjectMapper mapper = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Autowired
    private PushSubscribeService pushSubscribeService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    private ISmsValidateLogService smsValidateLogService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    IDgydFlowPackOrderService dgydFlowPackOrderService;
    @Autowired
    private IVisitLogService visitLogService;
    //@Autowired
    //private ToutiaoAdApiService toutiaoAdApiService;
    //@Autowired
    //private TuiaAdService tuiaAdApiService;
    //@Autowired
    //private BianxianmaoAdService bianxianmaoAdService;
    //@Autowired
    //private DoumengAdService doumengAdService;
    //@Autowired
    //private QutoutiaoAdService qutoutiaoAdService;
    //@Autowired
    //private HudongtuiAdService hudongtuiAdService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    DianxinVrbtService dianxinVrbtService;
    @Autowired
    DianxinVrbtOfficialService dianxinVrbtOfficialService;
    //    @Autowired
//    LiantongVrbtProperties liantongVrbtProperties;
//    @Autowired
//    LiantongVrbtService liantongVrbtService;
    @Autowired
    IProvinceLimitService provinceLimitService;
    @Autowired
    AppOpenResultSyncService appOpenResultSyncService;
    @Autowired
    IVrbtProvinceSwitchConfigService vrbtProvinceSwitchConfigService;
    @Autowired
    IEsDataService esDataService;
    @Autowired
    TianyiSpaceService tianyiSpaceService;
    @Autowired
    ITelecomOrderService telecomOrderService;
    @Autowired
    private IPPTVApiService pptvApiService;
    @Autowired
    private SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    private SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    private IShanghaiMobileService shanghaiMobileService;
    @Autowired
    private OutsideCallbackService outsideCallbackService;
    @Autowired
    private ThirdPartyChannelConfigProperties thirdPartyChannelConfigProperties;
    @Autowired
    private IThirdPartyService thirdPartyService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    HuanqiuwangDianxinVrbtService huanqiuwangDianxinVrbtService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    IVrbtChannelProvinceConfigService vrbtChannelProvinceConfigService;
    @Autowired
    HebeiYidongVrbtService hebeiYidongVrbtService;
    @Autowired
    ChongqingYidongVrbtNewService chongqingYidongVrbtNewService;
    @Autowired
    HebeiYidongVrbtProperties hebeiYidongVrbtProperties;
    @Autowired
    IDatangSmsService datangSmsService;

    @Autowired
    ChongqingYidongVrbtNewProperties chongqingYidongVrbtNewProperties;
    @Autowired
    LiantongVrbtCrackService liantongVrbtCrackService;
    @Autowired
    LiantongVrbtService liantongVrbtService;
    @Autowired
    LiantongXdclCrackService liantongXdclCrackService;
    @Autowired
    LiantongJunboCrackService liantongJunboCrackService;
    @Autowired
    LiantongVrbtJunboService liantongVrbtJunboService;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    KuaimaService kuaimaService;
    @Autowired
    KuaimaProperties kuaimaProperties;
    @Autowired
    KuaimaGansuService kuaimaGansuService;
    @Autowired
    HeilongjiangYidongVrbtService heilongjiangYidongVrbtService;
    @Autowired
    HeilongjiangYidongXqyVrbtService heilongjiangYidongXqyVrbtService;
    @Autowired
    SubscribeServiceTestProperties subscribeServiceTestProperties;
    @Autowired
    GuangxiYidongService guangxiYidongService;
    @Autowired
    KuaimaMiniAppService kuaimaMiniAppService;
    @Autowired
    KuaimaMiniAppProperties kuaimaMiniAppProperties;
    @Autowired
    LbtxSichuanYidongService lbtxSichuanYidongService;
    @Autowired
    LbtxSichuanYidongProperties lbtxSichuanYidongProperties;
    @Autowired
    JunboHunanYidongService junboHunanYidongService;
    @Autowired
    GansuYidongService gansuYidongService;
    @Autowired
    IHttpRequestService httpRequestService;
    @Autowired
    XinjiangYidongService xinjiangYidongService;
    @Autowired
    XunfeiJingxianVrbtService xunfeiJingxianVrbtService;
    @Autowired
    NingxiaYidongService ningxiaYidongService;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    SichuanLiantongService sichuanLiantongService;
    @Autowired
    private GuizhouYidongService guizhouYidongService;
    @Autowired
    private GuizhouYidongProperties guizhouYidongProperties;
    @Autowired
    private GuangdongYidongVrbtService guangdongYidongVrbtService;
    @Autowired
    private HainanYidongVrbtService hainanYidongVrbtService;
    @Autowired
    private AnhuiYidongService anhuiYidongService;
    @Autowired
    private ShandongDianxinService shandongDianxinService;
    @Autowired
    private GuangdongYidongYueyueService guangdongYidongYueyueService;

    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private YidongVrbtLimitProperties yidongVrbtLimitProperties;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private WangyiyunMmService wangyiyunMmService;
    @Autowired
    private IWyyMmOrderService wyyMmOrderService;
    @Autowired
    private IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    private HunanYidongService hunanYidongService;
    @Autowired
    JiangxiYidongService jiangxiYidongService;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private HenanYidongService henanYidongService;
    @Autowired
    private GuizhouYidongGaoJieService guizhouYidongGaoJieService;
    @Autowired
    private KuaimaShanxiService kuaimaShanxiService;
    @Autowired
    private ShandongHexiaoyuanService shandongHexiaoyuanService;
    @Autowired
    private HubeiYidongService hubeiYidongService;
    @Autowired
    private IProvinceChannelPhoneConfigService provinceChannelPhoneConfigService;
    @Autowired
    private YunnanYidongService yunnanYidongService;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @SmsDailyLimit
    @Override
    public Result<?> receiveOrder(Subscribe subscribe) {
        final String miguChannel = subscribe.getChannel();
        String mobile = subscribe.getMobile();
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }

        String subChannel = subscribe.getSubChannel();
        subChannel = StringUtils.isEmpty(subChannel) ? BizConstant.SUB_CHANNEL_DEFAULT : subChannel;
        //if(!channelService.isLegal(subChannel)){
        //    return  Result.error("系统繁忙,请稍后再试!");
        //}
        final String bizType = getBizTypeByMiguChannel(miguChannel);
        subscribe.setBizType(bizType);

        //测试账号
        TestAccount testAccount = subscribeServiceTestProperties.getTestAccountMap().get(mobile);
        if (testAccount != null) {
            return subscribeServiceTest(subscribe);
        }
        if (blackListService.isBlackList(mobile)) {
            return Result.msgBlackLimit();
        }
        if (BizConstant.BIZ_TYPE_SCYD_HS.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(SiChuanMobileBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_TYPE_JLYD_HS.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(JiLinMobileBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_CHANNEl_HETU.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null && mobileRegionResult.isIspYidong() && "四川".equals(mobileRegionResult.getProvince())) {
                IBusinessCommonService businessCommonService = SpringContextUtils.getBean(SiChuanMobileBusinessServiceImpl.class);
                return businessCommonService.receiveOrderWithCache(subscribe);
            }
        }

        if (BizConstant.BIZ_TYPE_SCH.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(AiChuangYiZhanBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_TYPE_SCH.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(AiChuangYiZhanBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_TYPE_JUNBOLLB.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(JunboGuiZhouBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (StringUtils.equalsAny(miguChannel,BizConstant.CHANNEL_ID_J6,BizConstant.CHANNEL_ID_J7)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(MiGuVrbtPayBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_CHANNEL_HETU_LN.equals(miguChannel)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(HeTuFenShengBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_CHANNEL_HETU_CQ.equals(miguChannel)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(HeTuFenShengChongQingBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_TYPE_QYCL.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(QyclBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_TYPE_DX_AIVRBT.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(DianxinVrbtBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }


        if (BizConstant.BIZ_TYPE_GUANGDONG.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(GuangDongMobileBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_TYPE_XIJIU_JX_VRBT.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(JiangXiVrbtBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_CHANNEL_TY_COMM.equals(miguChannel)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(TianyiCommAssistBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_CHANNEL_EXCLUSIVE_CARD.equals(miguChannel)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(SiChuanExclusiveCardBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BizConstant.BIZ_CHANNEL_AI_VRBT.equals(miguChannel)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(AiVrbtBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_KYCJHY.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(KycjhyBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }

        if (BizConstant.BIZ_TYPE_KA_SAI_STOCK.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null && mobileRegionResult.isIspYidong()) {
                IBusinessCommonService businessCommonService = SpringContextUtils.getBean(KaSaiStockYiDongBusinessServiceImpl.class);
                return businessCommonService.receiveOrderWithCache(subscribe);
            }
            if (mobileRegionResult != null && mobileRegionResult.isIspLiantong()) {
                IBusinessCommonService businessCommonService = SpringContextUtils.getBean(KaSaiStockLianTongBusinessServiceImpl.class);
                return businessCommonService.receiveOrderWithCache(subscribe);
            }
        }





        if (BIZ_TYPE_MGHY.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(VRJingMengBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_HETU.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(HetuBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_AHTS_HNYD.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(TianshuHunanYidongBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_GDDX_VRBT.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(GuangdongDuxingBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_YR_GSYD.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(YouranGansuBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_HEYU_VRBT.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(HeyuYidongVrbtBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_FZS.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(FengzhushouSichuanYidongBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_BJHY.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
            }
            if (mobileRegionResult.isIspYidong()) {
                IBusinessCommonService businessCommonService = SpringContextUtils.getBean(BjhyBusinessServiceImpl.class);
                return businessCommonService.receiveOrderWithCache(subscribe);
            } else if (mobileRegionResult.isIspLiantong()) {
                return Result.unicomRedirect("TO_XDCL");
            } else if (mobileRegionResult.isIspDianxin()) {
                if (SHANDONG_PROVINCE.equals(subscribe.getProvince())) {
                    subscribe.setChannel(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                    subscribe.setBizType(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                    return receiveShandongDianxinOrder(subscribe);
                }
                if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                    subscribe.setChannel(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                    subscribe.setBizType(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                    return receiveHunanDianxinCommonCrackOrder(subscribe);
                }
                return Result.telecomRedirect("TO_MAIHE");
            }
        }

        if (BIZ_TYPE_CPMB.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
            }
            if (mobileRegionResult.isIspYidong()) {
                IBusinessCommonService businessCommonService = SpringContextUtils.getBean(CpmbBusinessServiceImpl.class);
                return businessCommonService.receiveOrderWithCache(subscribe);
            } else if (mobileRegionResult.isIspLiantong()) {
                return Result.unicomRedirect("TO_XDCL");
            } else if (mobileRegionResult.isIspDianxin()) {
                if (SHANDONG_PROVINCE.equals(subscribe.getProvince())) {
                    subscribe.setChannel(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                    subscribe.setBizType(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                    return receiveShandongDianxinOrder(subscribe);
                }
                return Result.telecomRedirect("TO_MAIHE");
            }
        }
        if (BIZ_TYPE_UNION_MEMBER.equals(bizType)) {
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(UnionMemberBusinessServiceImpl.class);
            return businessCommonService.receiveOrderWithCache(subscribe);
        }

        if (BIZ_TYPE_CHANNEL_PROV_MIX1.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                        subscribe.setChannel(BIZ_TYPE_SHYD_XSSP_VS);
//                        subscribe.setBizType(BIZ_TYPE_SHYD_XSSP_VS);
////                        return shanghaiMobileVrbtPlus(subscribe);
//                        Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                        return result;
                        //潘潘要求关上海
                        return Result.error("暂未开放，敬请期待!");
                    }
                    if (SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
//                        subscribe.setChannel(BIZ_TYPE_SCYD_XSVRBTYL);
//                        subscribe.setBizType(BIZ_TYPE_SCYD_XSVRBTYL);
                        subscribe.setChannel(BIZ_CHANNEL_SCYD_QPJYB);
                        subscribe.setBizType(BIZ_TYPE_SCYD);
                        String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                        subscribe.setBizCode(bizCode);
                        return receiveSichuanMobileCrackOrder(subscribe);
                    }
                    if (CHONGQING_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_CQYD_VRBTHYB);
                        subscribe.setBizType(BIZ_TYPE_CQYD_VRBTHYB);
                        return receiveChongqingMobileOrderFuse(subscribe);
                    }
                    if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_HN_VRBT);
                        subscribe.setBizType(BIZ_TYPE_HN_VRBT);
                        return receiveHunanOrder(subscribe);
                    }
                    if (JIANGXI_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_CHANNEl_JXYD_LLB);
                        subscribe.setBizType(BIZ_TYPE_JXYD);
                        IBusinessCommonService businessCommonService = SpringContextUtils.getBean(JiangxiBusinessServiceImpl.class);
                        return businessCommonService.receiveOrderWithCache(subscribe);
                    }
                    return Result.error("暂未开放，敬请期待!");
                } else {
                    if(mobileRegionResult.isIspDianxin() && HUNAN_PROVINCE.equals(subscribe.getProvince())){
                        subscribe.setChannel(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                        subscribe.setBizType(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                        return receiveHunanDianxinCommonCrackOrder(subscribe);
                    }
                    return Result.error("暂未开放，敬请期待!");
                }
            } else {
                return Result.error("暂未开放，敬请期待!");
            }
        }

        if (BIZ_TYPE_CHANNEL_PROV_MIX2.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_HN_VRBT_DY_LLB);
                        subscribe.setBizType(BIZ_TYPE_HN_VRBT_DY_LLB);
                        return receiveHuNanMobileOrder(subscribe);
                    }
                    return Result.error("暂未开放，敬请期待!");
                } else {
                    if(mobileRegionResult.isIspDianxin() && HUNAN_PROVINCE.equals(subscribe.getProvince())){
                        subscribe.setChannel(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                        subscribe.setBizType(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                        return receiveHunanDianxinCommonCrackOrder(subscribe);
                    }
                    return Result.error("暂未开放，敬请期待!");
                }
            } else {
                return Result.error("暂未开放，敬请期待!");
            }
        }
        if (BIZ_TYPE_CHANNEL_PROV_MIX3.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_CHANNEl_HETU_GZ);
                        subscribe.setBizType(BIZ_TYPE_HETU);
                        return SpringContextUtils.getBean(HetuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                    }
                    if (LIAONING_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_CHANNEl_HETU);
                        subscribe.setBizType(BIZ_TYPE_HETU);
                        return SpringContextUtils.getBean(HetuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                    }
                    return Result.error("暂未开放，敬请期待!");
                } else {
                    return Result.error("暂未开放，敬请期待!");
                }
            } else {
                return Result.error("暂未开放，敬请期待!");
            }
        }

        if (BIZ_TYPE_RT.equals(bizType)) {
            return receiveRtOrderWithCache(subscribe);
        }
        if (BIZ_TYPE_SHANGHAI.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
            }
            if (mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
                Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_YES_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
                return result;
            }
            return Result.error("暂未开放，敬请期待!");
        }
//        if (BIZ_TYPE_SHYD_SXXHY.equals(bizType)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                String isp = mobileRegionResult.getOperator();
//                subscribe.setIsp(isp);
//            }
//            if (mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                return result;
//            }
//            return Result.error("暂未开放，敬请期待!");
//        }
//        if (BIZ_TYPE_SHYD_LLBJT.equals(bizType)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                String isp = mobileRegionResult.getOperator();
//                subscribe.setIsp(isp);
//            }
//            if (mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                return result;
//            }
//            return Result.error("暂未开放，敬请期待!");
//        }

//        if (BIZ_TYPE_SHYD_XSMGSP.equals(miguChannel)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                String isp = mobileRegionResult.getOperator();
//                subscribe.setIsp(isp);
//            }
//            if (mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                return result;
//            }
//            return Result.error("暂未开放，敬请期待!");
//        }

//        if (BIZ_TYPE_SHYD_XSNB.equals(miguChannel)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                String isp = mobileRegionResult.getOperator();
//                subscribe.setIsp(isp);
//            }
//            if (mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                return result;
//            }
//            return Result.error("暂未开放，敬请期待!");
//        }

//        if (BIZ_TYPE_SHYD_BJHY.equals(miguChannel)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                String isp = mobileRegionResult.getOperator();
//                subscribe.setIsp(isp);
//            }
//            if (mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                return result;
//            }
//            return Result.error("暂未开放，敬请期待!");
//        }

        if (BIZ_TYPE_DGYD_LLB.equals(bizType)) {
            return dongguanMobileLLBOpenUp(subscribe);
        }

        if (BIZ_TYPE_TYKJ.equals(bizType)) {
            subscribe.setServiceId(TianyiSpaceService.SERVICE_ID);
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspDianxin())) {
                    return Result.error("当前业务只支持电信用户!");
                }
            } else {
                return Result.error("当前业务只支持电信用户!");
            }
            return receiveTianyiOrderV1(subscribe);
        }

        if (BIZ_TYPE_SCYD.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && SICHUAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持四川移动用户!");
                }
            } else {
                return Result.error("当前业务只支持四川移动用户!");
            }
            String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
            subscribe.setBizCode(bizCode);
            if (StringUtils.equals(subscribe.getCrack(), "1")) {
                return receiveSichuanMobileCrackOrder(subscribe);
            }
            return receiveSichuanMobileOrderV1(subscribe);
        }

//        if (BIZ_TYPE_SHYD_XSSP.equals(miguChannel)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                //移动不支持省份判断
//                if (!(mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(mobileRegionResult.getProvince()))) {
//                    return Result.error("当前业务只支持上海移动用户!");
//                }
//            } else {
//                return Result.error("当前业务只支持上海移动用户!");
//            }
//            subscribe.setProvince(mobileRegionResult.getProvince());
//            subscribe.setCity(mobileRegionResult.getCity());
//            subscribe.setIsp(MobileRegionResult.ISP_YIDONG);
//            subscribe.setChannel(miguChannel);
////            return shanghaiMobileOpenUp(subscribe);
//            return shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//        }
//        if (BIZ_TYPE_SHYD_XSSP_VS.equals(miguChannel)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                //移动不支持省份判断
//                if (!(mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(mobileRegionResult.getProvince()))) {
//                    return Result.error("当前业务只支持上海移动用户!");
//                }
//            } else {
//                return Result.error("当前业务只支持上海移动用户!");
//            }
//            subscribe.setProvince(mobileRegionResult.getProvince());
//            subscribe.setCity(mobileRegionResult.getCity());
//            subscribe.setIsp(MobileRegionResult.ISP_YIDONG);
//            subscribe.setChannel(miguChannel);
////            return shanghaiMobileVrbtPlus(subscribe);
//            Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//            return result;
//        }
        if (BIZ_TYPE_CQYD_VRBT_DX.equals(bizType) || BIZ_TYPE_CQYD_5G_QYB.equals(bizType) || BIZ_TYPE_CQYD_MGHYHYB.equals(bizType) || BIZ_TYPE_CQYD_VRBTHYB.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && CHONGQING_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持重庆移动用户!");
                }
            } else {
                return Result.error("当前业务只支持重庆移动用户!");
            }
            return receiveChongqingMobileOrderFuse(subscribe);
        }
        if (BIZ_TYPE_HBYD_VRBT.equals(bizType)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                String isp = mobileRegionResult.getOperator();
//                subscribe.setIsp(isp);
//                //移动不支持省份判断
//                if (!(mobileRegionResult.isIspYidong() && HEBEI_PROVINCE.equals(mobileRegionResult.getProvince()))) {
//                    return Result.error("当前业务只支持河北移动用户!");
//                }
//            } else {
//                return Result.error("当前业务只支持河北移动用户!");
//            }
            return SpringContextUtils.getBean(HebeiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
//            return receiveHebeiMobileOrderFuse(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }
        if (BIZ_TYPE_GANSU_LLB_30.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && GANSU_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持甘肃移动用户!");
                }
            } else {
                return Result.error("当前业务只支持甘肃移动用户!");
            }
            return receiveGansuMobileOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        if (BIZ_TYPE_GANSU_LLNB_30.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && GANSU_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持甘肃移动用户!");
                }
            } else {
                return Result.error("当前业务只支持甘肃移动用户!");
            }
            return receiveGansuMobileLbtxOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        if (BIZ_TYPE_GUANGXI_LLB_5.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && GUANGXI_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持广西移动用户!");
                }
            } else {
                return Result.error("当前业务只支持广西移动用户!");
            }
            return receiveGuangxiOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }
        if (BIZ_TYPE_HN_VRBT.equals(bizType) || BIZ_TYPE_HN_CWB.equals(bizType) || BIZ_TYPE_HN_GSB.equals(bizType) || BIZ_TYPE_HN_GSBB.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && HUNAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持湖南移动用户!");
                }
            } else {
                return Result.error("当前业务只支持湖南移动用户!");
            }
            return receiveHunanOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        //if (BIZ_TYPE_DX_VRBT.equals(bizType)) {
        //    return receiveDianxinAloneOrder(subscribe);
        //}

        if (BIZ_TYPE_XDCL.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!mobileRegionResult.isIspLiantong()) {
                    return Result.error("当前业务只支持联通用户!");
                }
            } else {
                return Result.error("当前业务只支持联通用户!");
            }
            return receiveLiantongXdclOrderCrack(subscribe);
        }

        if (BIZ_CHANNEL_NX_XYL.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && NINGXIA_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持宁夏移动用户!");
                }
            } else {
                return Result.error("当前业务只支持宁夏移动用户!");
            }
            return receiveNingxiaOrder(subscribe);
        }

        if (XINJIANG_MOBILE_BIZ_LIST.contains(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && XINJIANG_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持新疆移动用户!");
                }
            } else {
                return Result.error("当前业务只支持新疆移动用户!");
            }
            return receiveXinjiangOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        if (BIZ_TYPE_SCLT_SPLLB.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspLiantong() && SICHUAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持四川联通用户!");
                }
            } else {
                return Result.error("当前业务只支持四川联通用户!");
            }
            return receiveSichuanLiantongLbtxOrder(subscribe);
        }

        if (GUIZHOU_MOBILE_BIZ_CODE_LIST.contains(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && GUIZHOU_PROVINCE.equals(mobileRegionResult.getProvince())) && !(mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("暂未开放,敬请期待!");
                }
            } else {
                return Result.error("暂未开放,敬请期待!");
            }
            if (mobileRegionResult.isIspYidong() && GUIZHOU_PROVINCE.equals(mobileRegionResult.getProvince())) {
                return receiveGuiZhouMobileOrder(subscribe);
            } else if (mobileRegionResult.isIspYidong() && SHANGHAI_PROVINCE.equals(mobileRegionResult.getProvince())) {
                //上海移动流量包开通
                subscribe.setBizType(BIZ_CHANNEL_SHYD_LLB);
                subscribe.setChannel(BIZ_TYPE_SHANGHAI);
//                return shanghaiMobileLlbOpenUp(subscribe);
                Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_YES_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
                return result;
            } else {
                return Result.error("暂未开放,敬请期待!");
            }

        }

        if (GUIZHOU_GAOJIE_BIZ_CODE_LIST.contains(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && GUIZHOU_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("暂未开放,敬请期待!");
                }
            } else {
                return Result.error("暂未开放,敬请期待!");
            }
            if (mobileRegionResult.isIspYidong() && GUIZHOU_PROVINCE.equals(mobileRegionResult.getProvince())) {
                return receiveGuiZhouGaoJieOrder(subscribe);
            } else {
                return Result.error("暂未开放,敬请期待!");
            }

        }

        if (StringUtils.equalsAny(bizType, BIZ_TYPE_YNYD)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && YUNAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("暂未开放,敬请期待!");
                }
            } else {
                return Result.error("暂未开放,敬请期待!");
            }
            if (mobileRegionResult.isIspYidong() && YUNAN_PROVINCE.equals(mobileRegionResult.getProvince())) {
                return receiveYunnanMobileOrder(subscribe);
            }else {
                return Result.error("暂未开放,敬请期待!");
            }

        }

        if (BIZ_TYPE_HLJYD_DYVRBT.equals(bizType) || BIZ_TYPE_HLJYD_XQY_DYVRBT.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && HEILONGJIANG_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持黑龙江移动用户!");
                }
            } else {
                return Result.error("当前业务只支持黑龙江移动用户!");
            }
            return receiveHeilongjiangMobileOrderFuse(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }
        if (BIZ_TYPE_GDYD_CXXR_SPCL6.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && GUANGDONG_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持广东移动用户!");
                }
            } else {
                return Result.error("当前业务只支持广东移动用户!");
            }
            return receiveGuangdongOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }
        if (BIZ_TYPE_GDYD_CXXR_SPCL99.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && GUANGDONG_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持广东移动用户!");
                }
            } else {
                return Result.error("当前业务只支持广东移动用户!");
            }
            return receiveGuangdongOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }
        if (BIZ_TYPE_HNYD_VRBT.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && HAINAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持海南移动用户!");
                }
            } else {
                return Result.error("当前业务只支持海南移动用户!");
            }
            return receiveHainanOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }
        if (BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspDianxin() && HUNAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持湖南电信用户!");
                }
            } else {
                return Result.error("当前业务只支持湖南电信用户!");
            }
            return receiveHunanDianxinCommonCrackOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }
        if (BIZ_TYPE_GZDX.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspDianxin() && GUIZHOU_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持贵州电信用户!");
                }
            } else {
                return Result.error("当前业务只支持贵州电信用户!");
            }
            return SpringContextUtils.getBean(GuizhouDianxinBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        if (StringUtils.equalsAny(bizType, BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_13, BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_9)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspDianxin() && HUNAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持湖南电信用户!");
                }
            } else {
                return Result.error("当前业务只支持湖南电信用户!");
            }
            return receiveHunanDianxinCommonCrackOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        if (BIZ_TYPE_CHANNEL_AHYD_VRBT.equals(bizType)) {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                String isp = mobileRegionResult.getOperator();
//                subscribe.setIsp(isp);
//                //移动不支持省份判断
//                if (!(mobileRegionResult.isIspYidong() && ANHUI_PROVINCE.equals(mobileRegionResult.getProvince()))) {
//                    return Result.error("当前业务只支持安徽移动用户!");
//                }
//            } else {
//                return Result.error("当前业务只支持安徽移动用户!");
//            }
            return SpringContextUtils.getBean(AnhuiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        if (BIZ_TYPE_JSYD_VRBT.equals(bizType)) {
            return SpringContextUtils.getBean(JiangsuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        if (BIZ_TYPE_OUTSIDE_QY.equals(bizType)) {
            return SpringContextUtils.getBean(OutsideQyclBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        if (BIZ_TYPE_JXYD.equals(bizType)) {
            return SpringContextUtils.getBean(JiangxiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        if (BIZ_TYPE_XJYD.equals(bizType)) {
            return SpringContextUtils.getBean(XinjiangBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        if (BIZ_TYPE_AIDOU_GZYD.equals(bizType)) {
            return SpringContextUtils.getBean(AidouGuizhouBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        if (BIZ_TYPE_SHOUJIZIXUN_YD.equals(bizType)) {
            return SpringContextUtils.getBean(ShoujizixunYidongBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        if (BIZ_TYPE_BEIJINGYIHUI.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if(mobileRegionResult.isIspDianxin()) {
                return SpringContextUtils.getBean(BeijingYihuiDianxinBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
            }else if(mobileRegionResult.isIspLiantong()){
                return SpringContextUtils.getBean(BeijingYihuiLiantongBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
            }else {
                if(BIZ_CHANNEL_BEIJINGYIHUI_SHANDONGYIDONG.equals(subscribe.getChannel())) {
                    return SpringContextUtils.getBean(BeijingYihuiShandongYidongBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                }else{
                    return SpringContextUtils.getBean(BeijingYihuiYidongBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                }
            }
        }

        if (BIZ_TYPE_CHANNEL_SDDX_SSHY.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspDianxin() && SHANDONG_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持山东电信用户!");
                }
            } else {
                return Result.error("当前业务只支持山东电信用户!");
            }
            return receiveShandongDianxinOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        if (BIZ_TYPE_WANGYIYUN_MM.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveWangyiyunMmOrder(subscribe);
        }

        if (HN_MOBILE_BIZ_CODE_LIST.contains(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && HUNAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("暂未开放,敬请期待!");
                }
            } else {
                return Result.error("暂未开放,敬请期待!");
            }
            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveHuNanMobileOrder(subscribe);
        }

        if (HENAN_MOBILE_BIZ_CODE_LIST.contains(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && HENAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("暂未开放,敬请期待!");
                }
            } else {
                return Result.error("暂未开放,敬请期待!");
            }
            if (mobileRegionResult.isIspYidong() && HENAN_PROVINCE.equals(mobileRegionResult.getProvince())) {
                return receiveHenanMobileOrder(subscribe);
            } else {
                return Result.error("暂未开放,敬请期待!");
            }

        }

        if (BIZ_TYPE_KMSX_VRBT_DYB.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                //移动不支持省份判断
//                if (!(mobileRegionResult.isIspYidong() && SHANXI_PROVINCE.equals(mobileRegionResult.getProvince()))) {
//                    return Result.error("当前业务只支持山西移动用户!");
//                }
            }
            return receiveShanxiMobileOrder(subscribe);
            //return Result.error("暂未开放,敬请期待!");
        }

        if (BIZ_TYPE_SD_HXY.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (!provinceChannelPhoneConfigService.allow(subscribe.getChannel(), mobile)) {
                    log.warn("移动省份号段限制,渠道号:{},手机号:{}", subscribe.getChannel(), mobile);
                    return Result.error("暂未开放,敬请期待!");
                }
            }
            return receiveShandongHexiaoyuanOrder(subscribe);
        }

        if (BIZ_TYPE_HUBYD_CZHY_QXB.equals(bizType) || BIZ_TYPE_HUBYD_CZHY.equals(bizType)) {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                String isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
            }
            return receiveHubeiMobileOrder(subscribe);
        }

        return receiveVrbtOrderWithAuthCache(subscribe);
    }

    @Override
    public Result<?> receiveVrbtOrderWithAuthCache(Subscribe subscribe) {
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        subscribe.setIsp(isp);
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (StringUtils.isEmpty(subscribe.getSmsCode())) {
                    if (QIYIN_CHANNEL_LIST.contains(subscribe.getChannel())) {
                        //014X04N 互动 （02F 优先开通 + 04P）
                        if (MiguApiService.QIYIN_CHANNEL_CODE_04N.equals(subscribe.getChannel())) {
                            if (provinceBusinessChannelConfigService.allow(MiguApiService.XUNFEI_CHANNEL_CODE_02F, subscribe.getProvince())) {
                                subscribe.setChannel(MiguApiService.XUNFEI_CHANNEL_CODE_02F);
                            } else {
                                if (!provinceBusinessChannelConfigService.allow(MiguApiService.QIYIN_CHANNEL_CODE_04N, subscribe.getProvince())) {
                                    subscribe.setChannel(MiguApiService.QIYIN_CHANNEL_CODE_04P);
                                }
                            }
                        }
                        if (MiguApiService.QIYIN_CHANNEL_CODE_04O.equals(subscribe.getChannel())) {
                            if (!provinceBusinessChannelConfigService.allow(MiguApiService.QIYIN_CHANNEL_CODE_04O, subscribe.getProvince())) {
                                subscribe.setChannel(MiguApiService.QIYIN_CHANNEL_CODE_04P);
                            }
                        }
                        //014X04O 信息流 （04P）
                    } else {
                        boolean isOpen = false; //判断当前渠道是否可开通
                        if(GUANGXI_PROVINCE.equals(subscribe.getProvince())){
                            subscribe.setChannel(MiguApiService.BIZ_BJHY_CHANNEL_CODE_U2);
                            subscribe.setBizType(BIZ_TYPE_BJHY);
                            return SpringContextUtils.getBean(BjhyBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                        }
//                        if (MiguApiService.CHAOXUAN_CHANNEL_CODE.equals(subscribe.getChannel()) && !HENAN_PROVINCE.equals(subscribe.getProvince()) && !SHANDONG_PROVINCE.equals(subscribe.getProvince()) && !GUANGXI_PROVINCE.equals(subscribe.getProvince()) && !JIANGSU_PROVINCE.equals(subscribe.getProvince())&& !HUBEI_PROVINCE.equals(subscribe.getProvince())) {
//                            isOpen = true;
//                        }
                        if (!isOpen && provinceBusinessChannelConfigService.allow(MiguApiService.XUNFEI_CHANNEL_CODE, subscribe.getProvince())) {
                            isOpen = true;
                            subscribe.setChannel(MiguApiService.XUNFEI_CHANNEL_CODE);
                        }
                        if (!isOpen && provinceBusinessChannelConfigService.allow(MiguApiService.XUNFEI_CHANNEL_CODE_02G, subscribe.getProvince())) {
                            isOpen = true;
                            subscribe.setChannel(MiguApiService.XUNFEI_CHANNEL_CODE_02G);
                            if (StringUtils.equalsAny(subscribe.getProvince(), HUBEI_PROVINCE, HENAN_PROVINCE, YUNAN_PROVINCE, LIAONING_PROVINCE, JILIN_PROVINCE, SHANDONG_PROVINCE) && RandomUtils.isInRatio(50) && provinceBusinessChannelConfigService.allow(MiguApiService.CENTRALITY_CHANNEL_CODE_04C, subscribe.getProvince())) {
                                subscribe.setChannel(MiguApiService.CENTRALITY_CHANNEL_CODE_04C);
                            }
                        }
                        if (!isOpen && provinceBusinessChannelConfigService.allow(MiguApiService.XUNFEI_CHANNEL_CODE_02F, subscribe.getProvince())) {
                            isOpen = true;
                            subscribe.setChannel(MiguApiService.XUNFEI_CHANNEL_CODE_02F);
                        }
                        if (!isOpen && provinceBusinessChannelConfigService.allow(MiguApiService.CENTRALITY_CHANNEL_CODE_04C, subscribe.getProvince())) {
                            isOpen = true;
                            subscribe.setChannel(MiguApiService.CENTRALITY_CHANNEL_CODE_04C);
                        }
                        if (!isOpen && provinceBusinessChannelConfigService.allow(MiguApiService.CHAOXUAN_CHANNEL_CODE, subscribe.getProvince())) {
//                            isOpen = true;
                            subscribe.setChannel(MiguApiService.CHAOXUAN_CHANNEL_CODE);
                        }
                        if (!isOpen && provinceBusinessChannelConfigService.allow(MiguApiService.CENTRALITY_CHANNEL_CODE_04D, subscribe.getProvince())) {
                            isOpen = true;
                            subscribe.setChannel(MiguApiService.CENTRALITY_CHANNEL_CODE_04D);
                        }
                        //江苏切换百分之50到04D
                        if(StringUtils.equals(subscribe.getProvince(),JIANGSU_PROVINCE) && provinceBusinessChannelConfigService.allow(MiguApiService.CENTRALITY_CHANNEL_CODE_04C, subscribe.getProvince()) && RandomUtils.isInRatio(50)){
                            subscribe.setChannel(MiguApiService.CENTRALITY_CHANNEL_CODE_04C);
                        }

                    }
                }
                if (!mobileRegionResult.isIspDianxin() && StringUtils.equals(subscribe.getOnlyAllowDianxin(), "1")) {
                    return Result.error("该业务只支持电信用户!");
                }

                if (mobileRegionResult.isIspYidong()) {
                    if (VRBT_FUSE.equals(subscribe.getFuse()) && ShanghaiMobileConstant.PROVINCE_SHANGHAI.equals(subscribe.getProvince())) {
//                        上海抖音视频彩铃改成上海9.9元视频彩铃
                        subscribe.setChannel(BIZ_CHANNEL_SHYD_XSSP_VS);
                        subscribe.setBizType(BIZ_TYPE_SHANGHAI);
                        Result<?> result = shanghaiMobileOpenUp(subscribe, ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
                        return result;
                        //潘潘要求关上海
                        //上海切组合包
                        //subscribe.setChannel(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W7);
                        //subscribe.setBizType(BIZ_TYPE_BJHY);
                        //IBusinessCommonService businessCommonService = SpringContextUtils.getBean(BjhyBusinessServiceImpl.class);
                        //return businessCommonService.receiveOrderWithCache(subscribe);
                    }
                    if (redisUtil.hasKey(CacheConstant.CMS_VRBT_SWITCH_SCYD)) {
                        if (VRBT_FUSE.equals(subscribe.getFuse()) && SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
//                          subscribe.setChannel(BIZ_TYPE_SCYD_XSVRBTYL);
//                          subscribe.setBizType(BIZ_TYPE_SCYD_XSVRBTYL);
                            subscribe.setChannel(BIZ_CHANNEL_SCYD_KPJYB);
                            subscribe.setBizType(BIZ_TYPE_SCYD);
                            String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                            subscribe.setBizCode(bizCode);
                            return receiveSichuanMobileCrackOrder(subscribe);
                        }
                    }

                    if (redisUtil.hasKey(CacheConstant.CMS_VRBT_SWITCH_GDYD)) {
                        if (GUANGDONG_PROVINCE.equals(subscribe.getProvince()) && VRBT_FUSE.equals(subscribe.getFuse())) {
                            subscribe.setChannel(BIZ_TYPE_CHANNEL_DGYD_LLB_KDX);
                            subscribe.setBizType(BIZ_TYPE_CHANNEL_DGYD_LLB_KDX);
                            return dongguanMobileLLBOpenUp(subscribe);
                        }
                    }

                    //if (/*MiguApiService.CHAOXUAN_CHANNEL_CODE.equals(subscribe.getChannel()) || */redisUtil.hasKey(CacheConstant.CMS_VRBT_SWITCH_JSYD)) {
                    //    if (JIANGSU_PROVINCE.equals(subscribe.getProvince()) && VRBT_FUSE.equals(subscribe.getFuse())) {
                    //        subscribe.setChannel(BIZ_TYPE_JSYD_VRBT);
                    //        subscribe.setBizType(BIZ_TYPE_JSYD_VRBT);
                    //        return SpringContextUtils.getBean(JiangsuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                    //    }
                    //}


//                    if (VRBT_FUSE.equals(subscribe.getFuse()) && HEBEI_PROVINCE.equals(subscribe.getProvince())) {
////                        return Result.error("暂未开放,敬请期待!");
//                        subscribe.setChannel(BIZ_TYPE_HBYD_VRBT);
//                        return SpringContextUtils.getBean(HebeiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
//                    }
//                    if (VRBT_FUSE.equals(subscribe.getFuse()) && HEILONGJIANG_PROVINCE.equals(subscribe.getProvince())) {
////                        subscribe.setChannel(BIZ_TYPE_HLJYD_DYVRBT);
////                        subscribe.setBizType(BIZ_TYPE_HLJYD_DYVRBT);
////                        return receiveHeilongjiangMobileOrderFuse(subscribe);
//                        if (RandomUtils.isInRatio(50)) {
//                            return Result.cmccRedirect("TO_HLJYD");
//                        } else {
//                            return Result.cmccRedirect("TO_HLJYD_XQY");
//                        }
//                    }
                    if (VRBT_FUSE.equals(subscribe.getFuse()) && HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_HN_VRBT_DY_LLB);
                        subscribe.setBizType(BIZ_TYPE_HN_VRBT_DY_LLB);
                        return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveHuNanMobileOrder(subscribe);
                    }
//                    if (VRBT_FUSE.equals(subscribe.getFuse()) && XINJIANG_PROVINCE.equals(subscribe.getProvince())) {
//                        subscribe.setChannel(BIZ_TYPE_XJYD_SPCL);
//                        subscribe.setBizType(BIZ_TYPE_XJYD_SPCL);
//                        return receiveXinjiangOrder(subscribe);
//                    }
                    //晚上11点到早上8点开通新疆和四川分省
                    //四川开通视频彩铃炫视视频娱乐包

                    //limitSwitch = 1的切换到四川
                    if (StringUtils.equals(subscribe.getLimitSwitch(), "1") && VRBT_FUSE.equals(subscribe.getFuse()) && SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_CHANNEL_SCYD_XSVRBTYL);
                        subscribe.setBizType(getBizTypeByMiguChannel(subscribe.getChannel()));
                        String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                        subscribe.setBizCode(bizCode);
                        return receiveSichuanMobileCrackOrder(subscribe);
                    }
                    //if (VRBT_FUSE.equals(subscribe.getFuse()) && CHONGQING_PROVINCE.equals(subscribe.getProvince())) {
                    //    subscribe.setChannel(BIZ_TYPE_CQYD_VRBT_DX);
                    //    subscribe.setBizType(BIZ_TYPE_CQYD_VRBT_DX);
                    //    return this.receiveChongqingMobileOrderFuse(subscribe);
                    //}

                    if (VRBT_FUSE.equals(subscribe.getFuse()) && GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_GZYD_VRBT_XSZS);
                        subscribe.setBizType(BIZ_TYPE_GZYD_VRBT_XSZS);
                        return this.receiveGuiZhouMobileOrder(subscribe);
                    }

                    if (mobileRegionResult.isIspYidong() && StringUtils.equals(subscribe.getNotAllowCmcc(), "1")) {
                        return Result.error("移动用户暂未开放,敬请期待!");
                    }
                    //提交验证码不做省份限制判断
                    if (StringUtils.isEmpty(subscribe.getSmsCode())) {
//                        Integer limitAmount = yidongVrbtLimitProperties.getLimit(subscribe.getChannel(), mobileRegionResult.getProvince());
//                        //获取当前序列值
//                        Integer currentCount = this.getIncrChannelProvinceLimit(subscribe.getChannel(), mobileRegionResult.getProvince());
//                        //发送即将限量短信
//                        if (currentCount + 20 >= limitAmount && limitAmount > 0) {
//                            log.warn("渠道省份开通数量即将到达限量,渠道号:{},省份:{},限量数:{},当前户数:{}", subscribe.getChannel(), mobileRegionResult.getProvince(), limitAmount, currentCount);
//                            yidongVrbtLimitProperties.sendVergeLimitSms(subscribe.getChannel(), mobileRegionResult.getProvince(), limitAmount, currentCount);
//                        }
//                        if (currentCount >= limitAmount && limitAmount > 0) {
//                            yidongVrbtLimitProperties.sendSms(subscribe.getChannel(), mobileRegionResult.getProvince(), limitAmount);
//                            log.error("渠道省份开通数量已达到限量,渠道号:{},省份:{}", subscribe.getChannel(), mobileRegionResult.getProvince());
//                            return Result.error("特定省份移动用户限量");
//                        }
                        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(),mobileRegionResult.getProvince())) {
                            log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                            return Result.error("暂未开放,敬请期待!");
                        }
                    }
                    //移动视频彩铃业务咪咕支付判定
                    if (CMCC_MIGU_PAY.equals(subscribe.getMiguPay())) {
                        return Result.cmccMiguPay("redirect migu pay");
                    }
                }
                else {
////                    //移动不支持省份判断
////                    if (mobileRegionResult.isIspYidong() && !provinceLimitService.isAvailableMobile(mobileRegionResult.getProvinceId())) {
////                        log.warn("移动省份限制,手机号:{},省份:{}", mobile, mobileRegionResult.getProvince());
////                        return Result.error("暂未开放,敬请期待!");
////                    }
                    //联通不支持省份判断
                    if (mobileRegionResult.isIspLiantong() && !provinceLimitService.isAvailableUnicom(mobileRegionResult.getProvinceId())) {
                        log.warn("联通省份限制,手机号:{},省份:{}", mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
//                    //电信暂时不开放
//                    if (mobileRegionResult.isIspDianxin() && !provinceLimitService.isAvailableTelecom(mobileRegionResult.getProvinceId())) {
//                        return Result.error("暂未开放,敬请期待!");
//                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统繁忙,请稍后再试!");
        }

        //联通单独处理
        if (MobileRegionResult.ISP_LIANTONG.equals(isp)) {
            //return Result.unicomRedirect("redirect to unicom");
            return this.receiveLiantongOrder(subscribe);
        }
        //电信单独处理
        if (MobileRegionResult.ISP_DIANXIN.equals(isp)) {
            //电信官方视频彩铃(使用开通服务器破解http开通)
            //return this.receiveDianxinOrderOfficialV2(subscribe);
//            if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
//                subscribe.setBizType(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
//                subscribe.setChannel(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
//                return this.receiveHunanDianxinCommonCrackOrder(subscribe);
//            }
//            if (SHANDONG_PROVINCE.equals(subscribe.getProvince())) {
//                subscribe.setChannel(BIZ_TYPE_CHANNEL_SDDX_SSHY);
//                subscribe.setBizType(BIZ_TYPE_CHANNEL_SDDX_SSHY);
//                return this.receiveShandongDianxinOrder(subscribe);
//            }
            //电信使用接口方式破解付费页面(如光明网)
//            return this.receiveDianxinOrderV1(subscribe);

            //if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
            //    subscribe.setChannel(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
            //    subscribe.setBizType(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
            //    return receiveHunanDianxinCommonCrackOrder(subscribe);
            //}

//            if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(),subscribe.getProvince())) {
//                log.warn("电信省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, subscribe.getProvince());
//                return Result.error("暂未开放,敬请期待!");
//            }

            if(BIZ_TYPE_VRBT.equals(subscribe.getBizType())){
    //            if (BIZ_DIANXIN_CHANNEL_MAIHE.equals(subscribe.getVrbtDxChannel())) {
    //                return this.receiveDianxinMaiheOrderCrack(subscribe);
    //            }
    //            return this.receiveDianxinOrderCrack(subscribe);
                //电信用户自己在电信页面话费支付
                return receiveDianxinOrderV2(subscribe);

                //跳转电信试营销页面
                //return dianxinRedirectOfficial(subscribe);
            }
            subscribe.setChannel(BIZ_CHANNEL_BEIJINGYIHUI_DIANXIN);
            subscribe.setBizType(BIZ_TYPE_BEIJINGYIHUI);
            return SpringContextUtils.getBean(BeijingYihuiDianxinBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
        }

        //如果外第三方渠道
        if (thirdPartyChannelConfigProperties.isThirdPartyChannel(subscribe.getChannel())) {
            if (StringUtils.isEmpty(subscribe.getSmsCode())) {
                return thirdPartyService.getSmsCode(mobile, subscribe.getChannel(), subscribe.getSubChannel());
            } else {
                return thirdPartyService.order(subscribe);
            }
        }
        return this.receiveVrbtOrder(subscribe);

    }


    @Override
    public Result<?> receiveVrbtOrder(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(BizConstant.BIZ_TYPE_MIGU_MUSIC, subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        if (true) {
            return SpringUtil.getBean(SubscribeServiceImpl.class).receiveVrbtCrackOrder(subscribe);
        }
        //final Result<Object> okResult = Result.ok("订阅成功");
        final String mobile = subscribe.getMobile();
        final String subChannel = subscribe.getSubChannel();
        //String mobileAuthKey = MOBILE_AUTH_KEY_PREFIX + mobile;
        //final String authValue = (String) redisUtil.get(mobileAuthKey);
        ////10分钟内未验证过同时需要验证
        //if(authValue==null && channelService.needSmsValidate(subChannel)) {
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            smsValidateService.create(mobile, subscribe.getChannel());
            smsValidateLogService.logCreate(mobile, subChannel);
            return Result.noauth("验证码已发送");
        } else {
            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
            try {
                smsValidateService.check(mobile, smsCode);
                //redisUtil.set(mobileAuthKey,MOBILE_AUTH_VALUE, MOBILE_AUTH_CATCHE_TTL_SECONDS);
                smsValidateLogService.logValid(mobile, subChannel);
            } catch (JeecgBootException e) {
                //e.printStackTrace();
                return Result.captchaErr(e.getMessage());
            }
        }
//        return miguLogin(subscribe);
//
//        //}
        return publicOpenVrbt(subscribe);
    }

    private Result<?> miguLogin(Subscribe subscribe) {
        VrbtCombinResult vrbtCombinResult = miguApiService.vrbtCombinQuery(subscribe.getMobile(), subscribe.getChannel());
        if (!vrbtCombinResult.isOK()) {
            return Result.error("系统繁忙，请稍后再试！");
        }
        return Result.miguLoginToken(vrbtCombinResult);
    }

    @Override
    public Result<?> receiveLiantongOrder(Subscribe subscribe) {

        //联通将铃音id设置为null
//        subscribe.setLtRingId(null);
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给开通服务器的redis并返回成功
        String ltVrbtChannel = subscribe.getVrbtLtChannel();
        boolean ltSwitch = StringUtils.equals(subscribe.getVrbtLtSwitch(), "1");
        //兼容已上线的联通视频彩铃切换
        if (!ltSwitch) {
            if (StringUtils.isBlank(ltVrbtChannel)) {
                ltVrbtChannel = BIZ_LT_CHANNEL_DEFAULT;
            }
            subscribe.setServiceId(BIZ_LT_SERVICE_ID);
        } else {
            if (StringUtils.isBlank(ltVrbtChannel)) {
                ltVrbtChannel = BIZ_LT_CHANNEL_RUIJIN;
            }
            //不切换使用瑞金serviceId
            subscribe.setServiceId(BIZ_LT_RJ_SERVICE_ID);
        }
        if (StringUtils.isBlank(ltVrbtChannel)) { //如果联通视频彩铃渠道为空，则设置默认的渠道
            ltVrbtChannel = BIZ_LT_CHANNEL_DEFAULT;
        }
        if (BIZ_LT_CHANNEL_LEQING.equals(ltVrbtChannel)) {
            subscribe.setServiceId(BIZ_LT_BLH_SERVICE_ID);
        } else if (BIZ_LT_CHANNEL_RUIJIN.equals(ltVrbtChannel)) {
            subscribe.setServiceId(BIZ_LT_RJ_SERVICE_ID);
        } else if (BIZ_LT_CHANNEL_RUIMEI.equals(ltVrbtChannel)) {
            subscribe.setServiceId(BIZ_LT_RM_SERVICE_ID);
        } else {
            subscribe.setServiceId(BIZ_LT_SERVICE_ID);
        }
        subscribe.setVrbtLtChannel(ltVrbtChannel);
        if (LIANTONG_CHANNEL_CRACK_LIST.contains(subscribe.getVrbtLtChannel())) {
            return receiveLiantongOrderCrack(subscribe);
        }
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.LIANTONG_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            //此处保存已提交验证码
//            Subscribe upd = new Subscribe();
//            upd.setId(transactionId);
//            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
//            upd.setModifyTime(new Date());
//            this.updateById(upd);
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
//            this.updateById(upd);
                this.updateSubscribeDbAndEs(upd);
            }
            return pushSubscribeService.pushSmsCode(transactionId, smsCode, subscribe.getMobile());
        }

        String mobile = subscribe.getMobile();
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + mobile;
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }

            String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + mobile;
            if (redisUtil.get(mobileMonthExistsKey) != null) {
                return Result.bizExists("你已开通,请勿重复开通");
            }
            //        this.save(subscribe);
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);

            final Result<?> result = pushSubscribeService.liantongHandleVrbtPackageExists(subscribe);

            if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            }
            if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
                redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            }

            return result;
        }
    }

    /**
     * 电信官方视频彩铃(使用开通服务器开通破解http开通)
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveDianxinOrderOfficialV2(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给开通服务器的redis并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isBlank(subscribe.getVrbtDxChannel())) {
            subscribe.setVrbtDxChannel(BIZ_DIANXIN_CHANNEL_DEFAULT);
        }

        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);

            //此处保存已提交验证码
//            Subscribe upd = new Subscribe();
//            upd.setId(transactionId);
//            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
//            upd.setModifyTime(new Date());
//            this.updateById(upd);

            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
//            this.updateById(upd);
                this.updateSubscribeDbAndEs(upd);
            }

            return pushSubscribeService.pushSmsCode(transactionId, smsCode, subscribe.getMobile());
        }

        String mobile = subscribe.getMobile();
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }

            //        this.save(subscribe);
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);

            final Result<?> result = pushSubscribeService.dianxinHandleVrbtOfficial(subscribe);

            if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            }

            return result;
        }

    }

    /**
     * 电信使用接口方式开通(如光明网)
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveDianxinOrderV1(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给开通服务器的redis并返回成功
        String smsCode = subscribe.getSmsCode();
        //如果电信渠道号为空
//        if (StringUtils.isEmpty(subscribe.getVrbtDxChannel())) {
//            String dxChannel = (String) redisUtil.get(CacheConstant.DIANXIN_CURRENT_CHANNEL_KEY);
//            subscribe.setVrbtDxChannel(dxChannel != null ? dxChannel : BIZ_DIANXIN_CHANNEL_DEFAULT);
//        }
//        subscribe.setServiceId(subscribe.getVrbtDxChannel());
//        if (BIZ_DIANXIN_CHANNEL_HUANQIUWANG.equals(subscribe.getVrbtDxChannel())) {
//            return receiveDianxinHuanqiuwangOrder(subscribe);
//        }
        //

        //查询所有渠道是否可以开通该手机号省份
        Optional<String> optional = DIANXIN_CHANNEL_LIST.stream().filter(dianxinChannel -> dianxinVrbtProperties.getDianxinVrbtConfig(dianxinChannel).getProvinceList().contains(subscribe.getProvince())).findFirst();
        if (optional.isPresent()) {
            subscribe.setVrbtDxChannel(optional.get());
            subscribe.setServiceId(optional.get());
        } else {
            return Result.error("暂未开放,敬请期待!");
        }
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
//            //此处保存已提交验证码
//            Subscribe upd = new Subscribe();
//            upd.setId(transactionId);
//            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
//            upd.setModifyTime(new Date());
//            this.updateById(upd);
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
//            this.updateById(upd);
                this.updateSubscribeDbAndEs(upd);
            }

            return pushSubscribeService.pushSmsCode(transactionId, smsCode, subscribe.getMobile());
        }

        String mobile = subscribe.getMobile();
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }

            String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
            if (redisUtil.get(mobileMonthExistsKey) != null) {
                return Result.bizExists("你已开通,请勿重复开通");
            }

            //        this.save(subscribe);
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);

            final Result<?> result = pushSubscribeService.dianxinHandleVrbtPackageExistsV1(subscribe);

            if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            }
            if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
                redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            }

            return result;
        }

    }

    @Override
    public Result<?> receiveBjhyCrackOrder(Subscribe subscribe) {

        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = this.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            //此处保存已提交验证码
//            Subscribe upd = new Subscribe();
//            upd.setId(transactionId);
//            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
//            upd.setModifyTime(new Date());
//            this.updateById(upd);
//            lambdaUpdate().eq(Subscribe::getId,transactionId).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_INIT).
//                    set(Subscribe::getStatus,SUBSCRIBE_STATUS_SMS_CODE_SUBMITED).set(Subscribe::getModifyTime,new Date()).update();


            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
//            this.updateById(upd);
                this.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, target.getChannel(), subscribe.getMobile());
            return Result.ok("订阅成功");
        }

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }

        this.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);

        final Result<?> result = pushSubscribeService.handleBjhyExistsBillingCrack(subscribe);

        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }

        return result;


    }

    @Override
    public void createSubscribeDbAndEs(Subscribe subscribe) {
        if (this.save(subscribe)) {
            esDataService.saveOrUpdateSubscribeAsync(subscribe);
        }
    }

    @Override
    public void updateSubscribeDbAndEs(Subscribe subscribe) {
        boolean flag = this.saveOrUpdate(subscribe);
        if (flag) {
            esDataService.saveOrUpdateSubscribe(subscribe);
        }
    }

    @Override
    public void updateSubscribeDbAndEsSync(Subscribe subscribe) {
        boolean flag = this.saveOrUpdate(subscribe);
        if (flag) {
            esDataService.saveOrUpdateSubscribe(subscribe);
        }
    }

    /**
     * 根据id同步更新DB和ES包月校验状态
     * @param subscribeId
     * @param verifyStatus
     */
    @Override
    public void updateVerifyStatusDbAndEs(String subscribeId, Integer verifyStatus) {
        this.lambdaUpdate().eq(Subscribe::getId, subscribeId).set(Subscribe::getPrice, verifyStatus).update();
        esDataService.updateSubscribeVerfyStatusById(subscribeId,verifyStatus);
    }

    /**
     * 根据id更新状态同时更新ES
     * @param subscriberId
     * @param status
     * @param result
     */
    @Override
    public void updateStatusResultByIdAnd2DbAndEs(String subscriberId, Integer status, String result) {
         this.lambdaUpdate().eq(Subscribe::getId,subscriberId).set(Subscribe::getStatus,status).set(Subscribe::getResult,result).update();
         esDataService.updateSubscribeStatusResultById(subscriberId,status, result);
    }


    @Override
    public Result<?> receiveTianyiOrder(Subscribe subscribe) {

        final String mobile = subscribe.getMobile();
        //根据手机号来同步,避免重复生成订单数据
        synchronized (interner.intern(mobile)) {
            //从缓存中获取支付链接
            String payUrl = (String) redisUtil.get(CacheConstant.MOBILE_ORDER_URL_CACHE + ":" + mobile);
            if (StringUtils.isEmpty(payUrl)) {
                try {
                    payUrl = tianyiSpaceService.generatePayUrl(mobile); //生成支付链接
                    String orderNo = (String) redisUtil.get(CacheConstant.MOBILE_ORDER_CACHE + ":" + mobile); //获取订单那号
                    subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
                    subscribe.setIspOrderNo(orderNo);
                    this.createSubscribeDbAndEs(subscribe);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    //缓存支付链接
                    redisUtil.set(CacheConstant.MOBILE_ORDER_URL_CACHE + ":" + mobile, payUrl, 3600L);
                    return Result.createOrder("", payUrl);
                } catch (Exception e) {
                    return Result.error("系统繁忙,请稍后再试");
                }
            } else {
                //直接使用缓存中的支付链接
                return Result.createOrder("", payUrl);
            }
        }
    }


    @Override
    public Result<?> receiveTianyiOrderV1(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                String payUrl = tianyiSpaceService.generatePayUrl(mobile);
                tianyiSpaceService.visitPayUrl(mobile, payUrl);
                if (tianyiSpaceService.getSms(mobile)) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.confirm("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                //提交验证码
                JsonNode node = tianyiSpaceService.confirm(mobile, smsCode);
                String orderNo = (String) redisUtil.get(CacheConstant.MOBILE_ORDER_CACHE + ":" + mobile);
                Integer code = node.at("/code").asInt();
                subscribe.setResult(node.at("/msg").asText());
                if (code == 1062) { //验证码错误
                    //其他错误
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setIspOrderNo(orderNo);
                    this.createSubscribeDbAndEs(subscribe);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.error("验证码错误");
                } else if (code == 0) { //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                    subscribe.setIspOrderNo(orderNo);
                    this.createSubscribeDbAndEs(subscribe);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //其他错误
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setIspOrderNo(orderNo);
                    this.createSubscribeDbAndEs(subscribe);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.error("订阅失败");
                }
            } catch (Exception e) {
                return Result.captchaErr("订阅失败");
            }

        }
    }



    /**
     * 四川移动开通
     *
     * @param subscribe
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> receiveSichuanMobileOrder(Subscribe subscribe) {
        return Result.error("暂无开放");
//        final String mobile = subscribe.getMobile();
//        String smsCode = subscribe.getSmsCode();
//        if (StringUtils.isEmpty(smsCode)) {
//            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
//            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
//                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
//            }
//            try {
//                SichuanMobileResult sichuanMobileResult = sichuanMobileFlowPacketService.sendRandomCode(mobile, subscribe.getBizCode());
//                if (sichuanMobileResult.isOK()) {
//                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
//                    return Result.noauth("验证码已发送");
//                } else {
//                    return Result.error("获取验证码失败");
//                }
//            } catch (Exception e) {
//                return Result.error("获取验证码失败");
//            }
//        } else {
//            //验证短信验证码是否合法
//            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
//                return Result.error("验证码错误");
//            }
//            //避免相同的错误短信验证码反复提交
//            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
//            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
//                return Result.error("请勿重复提交");
//            }
//            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
//            try {
//                //提交验证码
//                SichuanMobileResult sichuanMobileResult = null;
//                if(sichuanMobileFlowPacketProperties.getBizCode().equals(subscribe.getBizCode()) || sichuanMobileFlowPacketProperties.getServiceHouseBizCode().equals(subscribe.getBizCode())) {
//                    //合约办理
//                    sichuanMobileResult = sichuanMobileFlowPacketService.contractHandle(mobile, smsCode, subscribe.getBizCode());
//                    if (sichuanMobileResult.isOK() && sichuanMobileResult.getResult() != null && sichuanMobileResult.getResult().isSuccess()) {
//                        //订阅成功
//                        subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//
//                        //信息流广告转化上报
//                        channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//
//                        //写渠道订阅日志
//                        BizLogUtils.logSubscribe(subscribe);
//                        SichunMobileOrder sichunMobileOrder = new SichunMobileOrder();
//                        sichunMobileOrder.setOrderId(subscribe.getIspOrderNo());
//                        sichunMobileOrder.setCreateTime(subscribe.getOpenTime());
//                        sichunMobileOrder.setPhone(subscribe.getMobile());
//                        //写入四川移动订单表中
//                        sichuanMobileOrderMapper.insert(sichunMobileOrder);
//                        //pptv视频权益
//                        //pptvApiService.sendOutPPTV(subscribe.getMobile(),sichuanMobileResult.getResult().getOrderId(),BizConstant.BIZ_SICHUAN_MOBILE_SERVICE_ID);
//                        return Result.ok("订阅成功");
//                    }
//                    else if(StringUtils.contains(sichuanMobileResult.getResMsg(),"短信码") || StringUtils.contains(sichuanMobileResult.getResMsg(),"验证码")){
//                        return Result.captchaErr(sichuanMobileResult.getResMsg());
//                    } else {
//                        //订阅失败
//                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult() != null ? sichuanMobileResult.getResult().getOrderId() : "");
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//                        //信息流广告转化上报
//                        channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
//                        //写渠道订阅日志
//                        BizLogUtils.logSubscribe(subscribe);
//
//                        return Result.error(sichuanMobileResult.getResMsg());
//                        //开通失败直接开通视频彩铃
////                        return publicOpenVrbt(subscribe);
//                    }
//                }else{
//                    //流量订购
//                    sichuanMobileResult = sichuanMobileFlowPacketService.bizOrder(mobile, smsCode, subscribe.getBizCode());
//                    if(sichuanMobileResult.isOK() && sichuanMobileResult.getResult()!=null && !StringUtils.isEmpty(sichuanMobileResult.getResult().getOrderId())){
//                        //订阅成功
//                        subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//                        //信息流广告转化上报
//                        channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//                        //写渠道订阅日志
//                        BizLogUtils.logSubscribe(subscribe);
//                        SichunMobileOrder sichunMobileOrder = new SichunMobileOrder();
//                        sichunMobileOrder.setOrderId(subscribe.getIspOrderNo());
//                        sichunMobileOrder.setCreateTime(subscribe.getOpenTime());
//                        sichunMobileOrder.setPhone(subscribe.getMobile());
//                        //写入四川移动订单表中
//                        sichuanMobileOrderMapper.insert(sichunMobileOrder);
//                        return Result.ok("订阅成功");
//                    } else if(StringUtils.contains(sichuanMobileResult.getResMsg(),"短信码") || StringUtils.contains(sichuanMobileResult.getResMsg(),"验证码")){
//                        return Result.captchaErr(sichuanMobileResult.getResMsg());
//                    } else{
//                        //订阅失败
//                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult() != null ? sichuanMobileResult.getResult().getOrderId() : "");
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//                        //信息流广告转化上报
//                        channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
//                        //写渠道订阅日志
//                        BizLogUtils.logSubscribe(subscribe);
//                        return Result.error(sichuanMobileResult.getResMsg());
//                        //开通失败直接开通视频彩铃
//                        //return publicOpenVrbt(subscribe);
//                    }
//                }
//            } catch (Exception e) {
//                return Result.captchaErr("订阅失败");
//            }
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> receiveSichuanMobileOrderFuse(Subscribe subscribe) {

        return Result.error("暂无开放");

//        final String mobile = subscribe.getMobile();
//        String smsCode = subscribe.getSmsCode();
//        if (StringUtils.isEmpty(smsCode)) {
//            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
//            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
//                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
//            }
//            try {
//                SichuanMobileResult sichuanMobileResult = sichuanMobileFlowPacketService.sendRandomCode(mobile, subscribe.getBizCode());
//                if (sichuanMobileResult.isOK()) {
//                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
//                    return Result.noauth("验证码已发送", "sichuan");
//                } else {
//                    return Result.error("获取验证码失败");
//                }
//            } catch (Exception e) {
//                return Result.error("获取验证码失败");
//            }
//        } else {
//            //验证短信验证码是否合法
//            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
//                return Result.error("验证码错误");
//            }
//            //避免相同的错误短信验证码反复提交
//            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
//            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
//                return Result.error("请勿重复提交");
//            }
//            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
//            try {
//                //提交验证码
//                SichuanMobileResult sichuanMobileResult = null;
//                if(sichuanMobileFlowPacketProperties.getBizCode().equals(subscribe.getBizCode())) {
//                    subscribe.setChannel(BIZ_TYPE_SCYD_HSH);
//                    subscribe.setBizType(BIZ_TYPE_SCYD_HSH);
//
//                    //合约办理
//                    sichuanMobileResult = sichuanMobileFlowPacketService.contractHandle(mobile, smsCode, subscribe.getBizCode());
//                    if (sichuanMobileResult.isOK() && sichuanMobileResult.getResult() != null && sichuanMobileResult.getResult().isSuccess()) {
//                        //订阅成功
//                        subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//                        //信息流广告转化上报
//                        channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//                        //写渠道订阅日志
//                        BizLogUtils.logSubscribe(subscribe);
//                        SichunMobileOrder sichunMobileOrder = new SichunMobileOrder();
//                        sichunMobileOrder.setOrderId(subscribe.getIspOrderNo());
//                        sichunMobileOrder.setCreateTime(subscribe.getOpenTime());
//                        sichunMobileOrder.setPhone(subscribe.getMobile());
//                        //写入四川移动订单表中
//                        sichuanMobileOrderMapper.insert(sichunMobileOrder);
//                        //pptv视频权益
//                        pptvApiService.pptvRightsRecharge(subscribe.getMobile(),sichuanMobileResult.getResult().getOrderId(),BizConstant.BIZ_SICHUAN_MOBILE_SERVICE_ID,OrderProductEnum.SCVRBT.getChannelCode());
//                        return Result.ok("订阅成功");
//                    }
//                    else if(StringUtils.contains(sichuanMobileResult.getResMsg(),"短信码") || StringUtils.contains(sichuanMobileResult.getResMsg(),"验证码")){
//                        return Result.captchaErr(sichuanMobileResult.getResMsg());
//                    } else {
//                        //订阅失败
//                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult() != null ? sichuanMobileResult.getResult().getOrderId() : "");
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//                        //判断是否为外部渠道
//                        if(thirdPartyChannelConfigProperties.isThirdPartyChannel(subscribe.getChannel())){
//                            return Result.ok("订阅成功");
//                        }
//                        //设置未null,防止id重复
//                        subscribe.setId(null);
//                        subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
//                        subscribe.setResult(null);
//                        //开通失败直接开通视频彩铃
//                        return publicOpenVrbt(subscribe);
//                    }
//                }else{
//                    //流量订购
//                    sichuanMobileResult = sichuanMobileFlowPacketService.bizOrder(mobile, smsCode, subscribe.getBizCode());
//
////                    subscribe.setChannel(BIZ_TYPE_SCYD_HSH);
////                    subscribe.setBizType(BIZ_TYPE_SCYD_HSH);
//
//                    if(sichuanMobileResult.isOK() && sichuanMobileResult.getResult()!=null && !StringUtils.isEmpty(sichuanMobileResult.getResult().getOrderId())){
//                        //订阅成功
//                        subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//                        //信息流广告转化上报
//                        channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//                        //写渠道订阅日志
//                        BizLogUtils.logSubscribe(subscribe);
//                        SichunMobileOrder sichunMobileOrder = new SichunMobileOrder();
//                        sichunMobileOrder.setOrderId(subscribe.getIspOrderNo());
//                        sichunMobileOrder.setCreateTime(subscribe.getOpenTime());
//                        sichunMobileOrder.setPhone(subscribe.getMobile());
//                        //写入四川移动订单表中
//                        sichuanMobileOrderMapper.insert(sichunMobileOrder);
//                        return Result.ok("订阅成功");
//                    } else if(StringUtils.contains(sichuanMobileResult.getResMsg(),"短信码") || StringUtils.contains(sichuanMobileResult.getResMsg(),"验证码")){
//                        return Result.captchaErr(sichuanMobileResult.getResMsg());
//                    } else{
//                        //订阅失败
//                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                        subscribe.setIspOrderNo(sichuanMobileResult.getResult() != null ? sichuanMobileResult.getResult().getOrderId() : "");
//                        subscribe.setOpenTime(new Date());
//                        subscribe.setResult(sichuanMobileResult.getResMsg());
//                        this.createSubscribeDbAndEs(subscribe);
//                        //判断是否为外部渠道
//                        if(thirdPartyChannelConfigProperties.isThirdPartyChannel(subscribe.getChannel())){
//                            return Result.ok("订阅成功");
//                        }
//
//                        //设置未null,防止id重复
//                        subscribe.setId(null);
//                        subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
//                        subscribe.setResult(null);
//                        //开通失败直接开通视频彩铃
//                        return publicOpenVrbt(subscribe);
//                    }
//                }
//            } catch (Exception e) {
//                return Result.captchaErr("订阅失败");
//            }
//        }

    }

//    /**
//     * 上海移动抖音视频彩铃开通
//     *
//     * @param subscribe
//     * @return
//     */
//    private Result<?> shanghaiMobileOpenUp(Subscribe subscribe) {
//        final String mobile = subscribe.getMobile();
//        String smsCode = subscribe.getSmsCode();
//        String ip = IPUtils.getIpAddr(HttpContextUtil.getHttpServletRequest());
//        if (StringUtils.isEmpty(smsCode)) {
//            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
//            if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
//                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
//            }
////            boolean result = smsValidateService.create(mobile, BizConstant.BIZ_TYPE_SHYD_XSSP);
////            if (result) {
////                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
////                return Result.noauth("验证码已发送");
////            }else{
////                return Result.error("获取验证码失败");
////            }
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS, "", "");
//            if (result.isOK()) {
//                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
//                return Result.noauth("验证码已发送", result.getResult());
//            } else {
//                return Result.error("获取验证码失败", result.getResult());
//            }
//        } else {
////            //验证短信验证码是否合法
////            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
////                return Result.error("验证码错误");
////            }
//            //避免相同的错误短信验证码反复提交
//            String shanghaiYidongSmsInvalidKey = SHANGHAIYIDONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
//            if (redisUtil.hasKey(shanghaiYidongSmsInvalidKey)) {
//                return Result.error("请勿重复提交");
//            }
//            redisUtil.set(shanghaiYidongSmsInvalidKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
////            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
////            try {
////                smsValidateService.check(mobile,smsCode);
////            } catch (JeecgBootException e) {
////                //订阅成功
////                subscribe.setResult("验证码错误!");
////                //订阅失败
////                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
////                this.createSubscribeDbAndEs(subscribe);
////                return Result.error(e.getMessage());
////            }
//
//            //抖音视频彩铃9.9元包
//            //Result<?> result=shanghaiMobileService.businessSubscriptionDOUYIN(mobile,ip,ShanghaiMobileConstant.IS_NOT_RIGHT,"vrbtdy");
//            //炫视专属6元包
////            Result<?> result=shanghaiMobileService.businessSubscriptionDOUYIN(mobile,ip,ShanghaiMobileConstant.IS_NOT_RIGHT,"vrbtxs");
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS, smsCode, subscribe.getTransactionId());
////            if(result !=null){
////                if(result.getResult()!=null){
////                    subscribe.setIspOrderNo(result.getResult().toString());
////                }
////                subscribe.setResult(result.getMessage());
////            }
//            subscribe.setIspOrderNo(subscribe.getTransactionId());
//            subscribe.setResult(result.getMessage());
//            subscribe.setOpenTime(new Date());
//            if (result.isOK()) {
//                //订阅成功
//                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//            } else {
//                //订阅失败
//                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
//            }
//
////            if(result.isOK()){
////                //信息流广告转化上报
////                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
////            }else{
////                //信息流广告转化上报
////                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
////            }
//            //写渠道订阅日志
//            BizLogUtils.logSubscribe(subscribe);
//            return result;
//        }
//    }

//    /**
//     * 上海移动抖音视频彩铃开通
//     * <p>
//     * 上海移动抖音视频彩铃开通9.9
//     *
//     * @param subscribe
//     * @return
//     */
//    public Result<?> shanghaiMobileVrbtPlus(Subscribe subscribe) {
//        final String mobile = subscribe.getMobile();
//        String smsCode = subscribe.getSmsCode();
//        String ip = IPUtils.getIpAddr(HttpContextUtil.getHttpServletRequest());
//        if (StringUtils.isEmpty(smsCode)) {
//            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
//            if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
//                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
//            }
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS_VS, "", "");
//            if (result.isOK()) {
//                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
//                return Result.noauth("验证码已发送", result.getResult());
//            } else {
//                return Result.error("获取验证码失败", result.getResult());
//            }
//        } else {
//            //避免相同的错误短信验证码反复提交
//            String shanghaiYidongSmsInvalidKey = SHANGHAIYIDONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
//            if (redisUtil.hasKey(shanghaiYidongSmsInvalidKey)) {
//                return Result.error("请勿重复提交");
//            }
//            redisUtil.set(shanghaiYidongSmsInvalidKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS_VS, smsCode, subscribe.getTransactionId());
//            subscribe.setIspOrderNo(subscribe.getTransactionId());
//            subscribe.setResult(result.getMessage());
//            subscribe.setOpenTime(new Date());
//            if (result.isOK()) {
//                //订阅成功
//                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//            } else {
//                //订阅失败
//                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
//            }
//            //写渠道订阅日志
//            BizLogUtils.logSubscribe(subscribe);
//            return result;
//        }
//    }

//    /**
//     * 上海移动抖音视频彩铃开通
//     *
//     * @param subscribe
//     * @return
//     */
//    public Result<?> shanghaiMobileDouYinVrbt(Subscribe subscribe) {
//        final String mobile = subscribe.getMobile();
//        String smsCode = subscribe.getSmsCode();
//        String ip = IPUtils.getIpAddr(HttpContextUtil.getHttpServletRequest());
//        if (StringUtils.isEmpty(smsCode)) {
//            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
//            if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
//                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
//            }
////            boolean result = smsValidateService.create(mobile, BizConstant.BIZ_TYPE_SHYD_XSSP);
////            if (result) {
////                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
////                return Result.noauth("验证码已发送","shanghai");
////            }else{
////                return Result.error("获取验证码失败");
////            }
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS, "", "");
//            if (result.isOK()) {
//                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
//                return Result.noauth("验证码已发送", result.getResult());
//            } else {
//                return Result.error("获取验证码失败", result.getResult());
//            }
//        } else {
////            //验证短信验证码是否合法
////            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
////                return Result.error("验证码错误");
////            }
//            //避免相同的错误短信验证码反复提交
//            String shanghaiYidongSmsInvalidKey = SHANGHAIYIDONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
//            if (redisUtil.hasKey(shanghaiYidongSmsInvalidKey)) {
//                return Result.error("请勿重复提交");
//            }
//            redisUtil.set(shanghaiYidongSmsInvalidKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
////            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
////            try {
////                smsValidateService.check(mobile,smsCode);
////            } catch (JeecgBootException e) {
////                subscribe.setChannel(BIZ_TYPE_SHYD_XSSP);
////                subscribe.setBizType(BIZ_TYPE_SHYD_XSSP);
////                subscribe.setResult("验证码错误!");
////                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
////                this.createSubscribeDbAndEs(subscribe);
////                return Result.error(e.getMessage());
////            }
//
//            //抖音视频彩铃9.9元包
//            //Result<?> result=shanghaiMobileService.businessSubscriptionDOUYIN(mobile,ip,ShanghaiMobileConstant.IS_NOT_RIGHT,"vrbtdy");
//            //炫视专属6元包
////            Result<?> result=shanghaiMobileService.businessSubscriptionDOUYIN(mobile,ip,ShanghaiMobileConstant.IS_NOT_RIGHT,"vrbtxs");
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_VRBTXS, smsCode, subscribe.getTransactionId());
////            if(result !=null){
////                if(result.getResult()!=null){
////                    subscribe.setIspOrderNo(result.getResult().toString());
////                }
////                subscribe.setResult(result.getMessage());
////            }
//            subscribe.setIspOrderNo(subscribe.getTransactionId());
//            subscribe.setResult(result.getMessage());
//            subscribe.setOpenTime(new Date());
//            subscribe.setChannel(BIZ_TYPE_SHYD_XSSP);
//            subscribe.setBizType(BIZ_TYPE_SHYD_XSSP);
//            if (result.isOK()) {
//                //订阅成功
//                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//            } else {
//                //订阅失败
//                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
//
//            }
//
//            if (result.isOK()) {
////                //信息流广告转化上报
////                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//                //写渠道订阅日志
//                BizLogUtils.logSubscribe(subscribe);
//                //pptv视频权益
//                pptvApiService.pptvRightsRecharge(subscribe.getMobile(), result.getResult().toString(), BizConstant.BIZ_SHANGHAI_MOBILE_SERVICE_ID, OrderProductEnum.VRBTDY.getChannelCode());
//                return Result.ok("订阅成功");
//            } else {
//                //判断是否为外部渠道
//                if (thirdPartyChannelConfigProperties.isThirdPartyChannel(subscribe.getChannel())) {
//                    return Result.ok("订阅成功");
//                }
//                return result;
//            }
//        }
//    }

//    /**
//     * 上海移动流量包开通
//     *
//     * @param subscribe
//     * @return
//     */
//    private Result<?> shanghaiMobileLlbOpenUp(Subscribe subscribe) {
//        final String mobile = subscribe.getMobile();
//        String smsCode = subscribe.getSmsCode();
//        String ip = IPUtils.getIpAddr(HttpContextUtil.getHttpServletRequest());
//        if (StringUtils.isEmpty(smsCode)) {
//            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
//            if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
//                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
//            }
////            boolean result = smsValidateService.create(mobile, BizConstant.BIZ_TYPE_SHYD_LLB);
////            if (result) {
////                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
////                return Result.noauth("验证码已发送");
////            }else{
////                return Result.error("获取验证码失败");
////            }
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_YES_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_5GTHB, "", "");
//            if (result.isOK()) {
//                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
//                return Result.noauth("验证码已发送", result.getResult());
//            } else {
//                return Result.error("获取验证码失败", result.getResult());
//            }
//
//        } else {
////            //验证短信验证码是否合法
////            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
////                return Result.error("验证码错误");
////            }
//            //避免相同的错误短信验证码反复提交
//            String shanghaiYidongSmsInvalidKey = SHANGHAIYIDONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
//            if (redisUtil.hasKey(shanghaiYidongSmsInvalidKey)) {
//                return Result.error("请勿重复提交");
//            }
//            redisUtil.set(shanghaiYidongSmsInvalidKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
////            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
////            try {
////                smsValidateService.check(mobile,smsCode);
////            } catch (JeecgBootException e) {
////                //订阅失败
////                subscribe.setResult("验证码错误!");
////                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
////                this.createSubscribeDbAndEs(subscribe);
////                return Result.error(e.getMessage());
////            }
//            //5G特惠包（30元5GB）
////            Result<?> result=shanghaiMobileService.businessLiuLiangBao(mobile,ip,ShanghaiMobileConstant.IS_YES_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_5GTHB);
////           if(result !=null){
////               if(result.getResult()!=null){
////                   subscribe.setIspOrderNo(result.getResult().toString());
////               }
////               subscribe.setResult(result.getMessage());
////           }
//
//            Result<?> result = shanghaiMobileService.SHYDBusinessOrder(mobile, ip, ShanghaiMobileConstant.IS_YES_RIGHT, ShanghaiMobileBusiness.SHANGHAI_MOBILE_5GTHB, smsCode, subscribe.getTransactionId());
//            subscribe.setIspOrderNo(subscribe.getTransactionId());
//            subscribe.setResult(result.getMessage());
//            subscribe.setOpenTime(new Date());
//            if (result != null && result.isOK()) {
//                //订阅成功
//                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//            } else {
//                //订阅失败
//                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                this.createSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
//
//            }
//            //写渠道订阅日志
//            BizLogUtils.logSubscribe(subscribe);
//            return result;
//        }
//    }




    /**
     * 上海移动业务开通
     *
     * @param subscribe
     * @return
     */
    public Result<?> shanghaiMobileOpenUp(Subscribe subscribe,Integer isRight,String business) {

        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }

        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        String ip = IPUtils.getIpAddr(HttpContextUtil.getHttpServletRequest());
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            Result<?> result = shanghaiMobileService.shangHaiMobileBusinessOrder(mobile, ip,isRight,business, "", "");
            if (result.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.noauth("验证码已发送", result.getResult());
            } else {
                return Result.error("获取验证码失败", result.getResult());
            }

        } else {
            //避免相同的错误短信验证码反复提交
            String shanghaiYidongSmsInvalidKey = SHANGHAIYIDONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(shanghaiYidongSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(shanghaiYidongSmsInvalidKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            Result<?> result = shanghaiMobileService.shangHaiMobileBusinessOrder(mobile, ip,isRight,business, smsCode, subscribe.getTransactionId());
            subscribe.setIspOrderNo(subscribe.getTransactionId());
            subscribe.setResult(result.getMessage());
            subscribe.setOpenTime(new Date());
            if (result != null && result.isOK()) {
                //订阅成功
                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                this.createSubscribeDbAndEs(subscribe);
                //加入包月延迟校验队列
                rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            } else {
                //订阅失败
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                this.createSubscribeDbAndEs(subscribe);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
            }
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return result;
        }
    }

    @Override
    public Result<?> getSichuanMobileRandom(String phone, String bizCode) {
        try {
            if (!phone.matches(Regexp.MOBILE_REG)) {
                return Result.error("请输入正确格式手机号");
            }
            MobileRegionResult mobileRegionResult = mobileRegionService.query(phone);
            if (mobileRegionResult != null) {
                //移动不支持省份判断
                if (!(mobileRegionResult.isIspYidong() && SICHUAN_PROVINCE.equals(mobileRegionResult.getProvince()))) {
                    return Result.error("当前业务只支持四川移动用户!");
                }
            } else {
                return Result.error("当前业务只支持四川移动用户!");
            }

            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + phone;
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            SichuanMobileResult sichuanMobileResult = sichuanMobileFlowPacketService.sendRandomCode(phone, bizCode);
            if (sichuanMobileResult.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.ok("验证码已发送");
            }
            return Result.noauth("发送短信验证码失败,请稍后再试");
        } catch (Exception e) {
            log.error("四川移动流量包获取短信错误:", e);
            return Result.noauth("发送短信验证码失败,请稍后再试");
        }
    }

    @Override
    public Result<?> sichuanMobileOrder(String phone, String code, String bizCode) {
        return Result.error("暂无开放");
//        try {
//            if (!phone.matches(Regexp.MOBILE_REG)) {
//                return Result.error("无效的手机号");
//            }
//            //验证短信验证码是否合法
//            if (!code.matches(Regexp.MIGU_SMS_CODE_REG) || StringUtils.isEmpty(code)) {
//                return Result.error("验证码格式错误");
//            }
//            //避免相同的错误短信验证码反复提交sichuanMobileFlowPacketProperties.getBizCode().equals(bizCode)
//            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + phone + "-" + code;
//            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
//                return Result.error("请勿重复提交");
//            }
//            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
//            if (sichuanMobileFlowPacketProperties.getBizCode().equals(bizCode) || sichuanMobileFlowPacketProperties.getServiceHouseBizCode().equals(bizCode)) {
//                //合约办理
//                SichuanMobileResult sichuanMobileResult = sichuanMobileFlowPacketService.contractHandle(phone, code, bizCode);
//                if (sichuanMobileResult.isOK() && sichuanMobileResult.getResult() != null && sichuanMobileResult.getResult().isSuccess()) {
//                    SichunMobileOrder sichunMobileOrder = new SichunMobileOrder();
//                    sichunMobileOrder.setOrderId(sichuanMobileResult.getResult().getOrderId());
//                    sichunMobileOrder.setCreateTime(new Date());
//                    sichunMobileOrder.setPhone(phone);
//                    //写入四川移动订单表中
//                    sichuanMobileOrderMapper.insert(sichunMobileOrder);
//                    return Result.ok("订阅成功");
//                } else {
//                    return Result.error("订阅失败");
//                }
//            } else {
//                //流量订购
//                SichuanMobileResult sichuanMobileResult = sichuanMobileFlowPacketService.bizOrder(phone, code, bizCode);
//                if (sichuanMobileResult.isOK() && sichuanMobileResult.getResult() != null && !org.apache.commons.lang3.StringUtils.isEmpty(sichuanMobileResult.getResult().getOrderId())) {
//                    SichunMobileOrder sichunMobileOrder = new SichunMobileOrder();
//                    sichunMobileOrder.setOrderId(sichuanMobileResult.getResult().getOrderId());
//                    sichunMobileOrder.setCreateTime(new Date());
//                    sichunMobileOrder.setPhone(phone);
//                    //写入四川移动订单表中
//                    sichuanMobileOrderMapper.insert(sichunMobileOrder);
//                    return Result.ok("订阅成功");
//                } else {
//                    return Result.error("订阅失败");
//                }
//            }
//        } catch (Exception e) {
//            log.error("四川移动流量包订阅:", e);
//            return Result.noauth("订阅失败,请稍后再试");
//        }
    }

    @Override
    public Result<?> getOutsideCode(String mobile, String subChannel) {

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + mobile;
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        try {
            OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subChannel);
            if (outsideConfig == null) {
                log.error("无效的外部渠道号:{}", subChannel);
                return Result.error("发送短信验证码失败,请稍后再试");

            }
            String channelCode = outsideConfig.getChannel();
            smsValidateLogService.logCreate(mobile, subChannel);
            boolean result = smsValidateService.create(mobile, channelCode);
            if (!result) {
                return Result.error("发送短信验证码失败,请稍后再试");
            }
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送至你的手机,5分钟内有效");
        } catch (IllegalArgumentException exception) {
            return Result.error(exception.getMessage());
        } catch (Exception e) {
            return Result.error("发送短信验证码错误");
        }
    }

    @Override
    public Result<?> receiveOrderOutside(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        final String smsCode = subscribe.getSmsCode();
        String subChannel = subscribe.getSubChannel();
        final String miguChannel = subscribe.getChannel();
        subChannel = StringUtils.isEmpty(subChannel) ? BizConstant.SUB_CHANNEL_DEFAULT : subChannel;
        final String bizType = getBizTypeByMiguChannel(miguChannel);
        subscribe.setBizType(bizType);
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        try {
            smsValidateService.check(mobile, smsCode);
            smsValidateLogService.logValid(mobile, subChannel);
        } catch (JeecgBootException e) {
            return Result.error("验证码错误");
        }
        return publicOpenVrbt(subscribe);

    }

    @Override
    public void addCallbackNotifyMessage(Subscribe subscribe, String msg) {
        boolean isOutsideChannel = outsideConfigService.isOutsideChannel(subscribe.getSubChannel());
        if (isOutsideChannel) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
//            outsideCallbackService.outsideCallback(subscribe, msg);
//            boolean outSidecallbackResult = outsideCallbackService.outsideCallback(subscribe, msg);
//            if (!outSidecallbackResult) {
//                redisDelayedQueueManager.addCallbcakNotify(CallbackNotifyMessage.builder().id(subscribe.getId())
//                        .failCount(1).msg(msg).build(), CALLBACK_INTERVAL, TimeUnit.MINUTES);
//            }
        }
    }

    /**
     * 1小时退订外部回调
     *
     * @param subscribe
     */
    @Override
    public void unsubscribeOutSideCallback(Subscribe subscribe) {
        boolean isJunboOutsideChannel = outsideConfigService.isJunboOutsideChannel(subscribe.getSubChannel());
        if (isJunboOutsideChannel) {
            outsideCallbackService.unsubscribeNotifyAsync(subscribe);
        }
    }

    @Override
    public void liantongOrder(Subscribe subscribe) {
        String bizType = getBizTypeByMiguChannel(subscribe.getChannel());
        subscribe.setBizType(bizType);
        subscribe.setServiceId(BIZ_LT_SERVICE_ID);
        this.createSubscribeDbAndEs(subscribe);
    }

    @Override
    public Result receiveDianxinOrderV2(Subscribe subscribe) {
        if (StringUtils.isBlank(subscribe.getVrbtDxChannel())) {
            subscribe.setVrbtDxChannel(BIZ_DIANXIN_CHANNEL_MAIHE);
        }
        subscribe.setServiceId(subscribe.getVrbtDxChannel());
        //if (!dianxinVrbtProperties.getDianxinVrbtConfig(subscribe.getVrbtDxChannel()).getProvinceList().contains(subscribe.getProvince())) {
        //    return Result.error("暂未开放,敬请期待!");
        //}
        String mobile = subscribe.getMobile();
        final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile, subscribe.getVrbtDxChannel());
        if (packageExist) {
            return Result.error("已有包月");
        }
        final DianxinResp dianxinResp = dianxinVrbtService.confirmOpenOrderLaunchedEx(mobile, "1", null, subscribe.getVrbtDxChannel());
        if (!dianxinResp.isOK()) {
            return Result.error("电信视频彩铃包月业务订购失败:" + dianxinResp.getResMessage());
        }
        //if (!dianxinResp.getFeeUrl().contains(bizProperties.getSubscribeDianxinVrbtOrderPage())) {
        //    log.error("电信视频彩铃计费地址变更");
        //}
        //String orderPage = bizProperties.getSubscribeDianxinVrbtOrderPage() + "?order_no=" + dianxinResp.getOrderNo();
        subscribe.setIspOrderNo(dianxinResp.getOrderNo());
        this.createSubscribeDbAndEs(subscribe);
        return Result.redirect("redirect telecom vrbt page", dianxinResp.getFeeUrl());
    }

    /**
     * 跳转到电信试营销页面
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> dianxinRedirectOfficial(Subscribe subscribe) {
        DianxinVrbtConfig dianxinVrbtConfig = dianxinVrbtProperties.getDianxinVrbtConfig(subscribe.getVrbtDxChannel());
        //跳转电信试营销页面
        return Result.redirect("redirect telecom vrbt page", dianxinVrbtConfig.getApiLimitRedirectPage());
    }

    @Override
    public Result<?> receiveSichuanMobileOrderV1(Subscribe subscribe) {

        final String mobile = subscribe.getMobile();
        String scmccChannel = subscribe.getScmccChannel();
        if (StringUtils.isEmpty(scmccChannel)) {
            scmccChannel = BIZ_SCMCC_CHANNEL_YRJY;
        }
//        if (BIZ_SCMCC_CHANNEL_YRJY.equals(scmccChannel) && BIZ_TYPE_SCYD_XSVRBTYL.equals(subscribe.getChannel())) {
//            return receiveSichuanMobileCrackOrder(subscribe);
//        }
        if (BIZ_SCMCC_CHANNEL_KUAIMA.equals(scmccChannel)) {
            return receiveKuaimaOrder(subscribe);
        }
        if (BIZ_SCMCC_CHANNEL_KUAIMA_MINIAPP.equals(scmccChannel)) {
            if (BIZ_CHANNEL_SCYD_BJHY.equals(subscribe.getBizType())) {
                subscribe.setBizCode(BIZ_KUAIMAMINIAPP_BJHY);
            } else if (BIZ_CHANNEL_SCYD_LLB_30.equals(subscribe.getBizType())) {
                subscribe.setBizCode(BIZ_KUAIMAMINIAPP_LLB_30);
            }
            return receiveKuaimaMiniAppOrder(subscribe);
        }
//        if (BIZ_SCMCC_CHANNEL_LBTX.equals(scmccChannel)) {
//            return receiveSichuanLbtxOrder(subscribe);
//        }

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        if (StringUtils.equals(mobile, AdReportService.SCMCC_WHITE_NUMBER)) {
            subscribe.setIspOrderNo(IdWorker.get32UUID());
            this.createSubscribeDbAndEs(subscribe);
            return Result.ok("WHITE_NUMBER", subscribe.getIspOrderNo());
        }
        try {
            SichuanMobilePrepareOrderResult sichuanMobileResult = sichuanMobileFlowPacketService.prepareOrder(mobile, subscribe.getBizCode(), scmccChannel);
            if (sichuanMobileResult.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                String jsonStr = sichuanMobileFlowPacketService.decryptResult(sichuanMobileResult.getResult(), scmccChannel);
                JsonNode jsonNode = mapper.readTree(jsonStr);
                String serialNumber = jsonNode.get("serial_number").asText();
                subscribe.setServiceId(scmccChannel);
                subscribe.setIspOrderNo(serialNumber);
                this.createSubscribeDbAndEs(subscribe);
                return Result.ok("预下单成功", serialNumber);
            } else {
                return Result.error("预下单失败");
            }
        } catch (Exception e) {
            return Result.error("预下单失败");
        }
    }


    @Override
    public Result<?> receiveSichuanMobileCrackOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        final String smsCode = subscribe.getSmsCode();
        if (adSiteBusinessConfigService.isBlack(BIZ_TYPE_SCYD, subscribe.getReferer())) {
            log.error("手机号:{},业务类型:{},app:{}已被屏蔽", subscribe.getMobile(), BIZ_TYPE_SCYD, subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            return pushSubscribeService.pushSmsCode(transactionId, smsCode, subscribe.getMobile());
        }
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String scmccChannel = subscribe.getScmccChannel();
            if (StringUtils.isEmpty(scmccChannel)) {
                scmccChannel = BIZ_SCMCC_CHANNEL_YRJY;
            }
            subscribe.setServiceId(scmccChannel);
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            //写渠道订阅日志
            this.createSubscribeDbAndEs(subscribe);
            BizLogUtils.logSubscribe(subscribe);
            final Result<?> result = pushSubscribeService.handleScyd(subscribe);
            if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
            }
            return result;
        }
    }

    @Override
    public Result<?> receiveLiantongXdclOrderCrack(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.LIANTONG_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            liantongXdclCrackService.smsCode(target.getIspOrderNo(), smsCode);
            return Result.ok("提交验证码成功");
        }
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            final String id = subscribe.getId();
            //查询是否已开通了包月
            final boolean isSubedMon = liantongXdclCrackService.isMember(mobile);
            //如果有包月
            if (isSubedMon) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
                upd.setResult("已有包月");
                this.updateSubscribeDbAndEs(upd);
                return Result.bizExists("你已开通,请勿重复开通");
            }
            LiantongCrackResult liantongCrackResult = liantongXdclCrackService.getSms(mobile);
            if (liantongCrackResult.isGetOK()) {
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setIspOrderNo(liantongCrackResult.getSId());
                upd.setPushRespMessage(liantongCrackResult.getSId());
                this.updateSubscribeDbAndEs(upd);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("系统繁忙,请稍后再试!");
            }
        }
    }

    @Override
    public boolean receiveLiantongXdclNotify(String mobile, String orderNo, String resCode, String resMsg) {
        Integer status = "000000".equals(resCode) ? 1 : 0;
        String result = "计费开通结果=>" + resCode + ":" + resMsg;
        Subscribe subscribe = this.findByMobileAndIspOrderId(mobile, orderNo);
        if (subscribe == null) {
            log.error("计费更新订购结果失败,不存在该id的订购通知记录,mobile:{},transId:{}", mobile, orderNo);
            return false;
        }
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},transId:{}", mobile, orderNo);
            return false;
        }
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            //如果有包月
            if (liantongXdclCrackService.isMember(mobile)) {
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, status);
            } else {
                status = 0;
            }
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult(result);
        upd.setOpenTime(new Date());
        upd.setModifyTime(new Date());
        this.updateSubscribeDbAndEs(upd);
        return true;

    }

    @Override
    public Result<?> receiveLiantongJunboOrderCrack(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.LIANTONG_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            liantongJunboCrackService.smsCode(target.getPushRespMessage(), smsCode);
            return Result.ok("提交验证码成功");
        }
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            final String id = subscribe.getId();
            //查询是否已开通了包月
            final boolean isSubedMon = liantongVrbtJunboService.isSubedMon(mobile);
            //如果有包月
            if (isSubedMon) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
                upd.setResult("已有包月");
                this.updateSubscribeDbAndEs(upd);
                return Result.bizExists("你已开通,请勿重复开通");
            }
            LiantongJunboResp liantongJunboResp = liantongVrbtJunboService.subScribe(mobile, UUIDGenerator.generate());
            if (!liantongJunboResp.isCodeOK()) {
                return Result.error("系统繁忙,请稍后再试!");
            }
            LiantongCrackResult liantongCrackResult = liantongJunboCrackService.getSms(mobile, liantongJunboResp.getResult().getSubUrl());
            if (liantongCrackResult.isGetOK()) {
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setIspOrderNo(liantongJunboResp.getResult().getThirdOrderId());
                upd.setPushRespMessage(liantongCrackResult.getSId());
                this.updateSubscribeDbAndEs(upd);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("系统繁忙,请稍后再试!");
            }
        }
    }

    @Override
    public boolean receiveLiantongJunboNotify(String orderNo, String resCode, String resMsg) {
        Integer status = "0".equals(resCode) ? 1 : 0;
        String result = "计费开通结果=>" + resCode + ":" + resMsg;
        Subscribe subscribe = this.findByIspOrderId(orderNo);
        if (subscribe == null) {
            log.error("计费更新订购结果失败,不存在该id的订购通知记录,transId:{}", orderNo);
            return false;
        }
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,transId:{}", orderNo);
            return false;
        }
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status)) {
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, status);
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult(result);
        upd.setOpenTime(new Date());
        upd.setModifyTime(new Date());
        this.updateSubscribeDbAndEs(upd);
        return true;
    }

    @Override
    public Result<?> receiveKuaimaOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String scmccChannel = BIZ_SCMCC_CHANNEL_KUAIMA;
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        if (StringUtils.equals(mobile, AdReportService.SCMCC_WHITE_NUMBER)) {
            subscribe.setIspOrderNo(IdWorker.get32UUID());
            this.createSubscribeDbAndEs(subscribe);
            return Result.ok("WHITE_NUMBER", subscribe.getIspOrderNo());
        }
        try {
            KuaimaResult kuaimaResult = kuaimaService.prepareOrder(mobile, subscribe.getBizCode());
            if (kuaimaResult.isOk()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                String serialNumber = kuaimaResult.getData().getSerialNumber();
                subscribe.setServiceId(scmccChannel);
                subscribe.setIspOrderNo(serialNumber);
                this.createSubscribeDbAndEs(subscribe);
                return Result.ok("预下单成功", serialNumber);
            } else {
                return Result.error("预下单失败");
            }
        } catch (Exception e) {
            return Result.error("预下单失败");
        }
    }

    @Override
    public Result<?> receiveKuaimaMiniAppOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String scmccChannel = BIZ_SCMCC_CHANNEL_KUAIMA_MINIAPP;
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        if (StringUtils.equals(mobile, AdReportService.SCMCC_WHITE_NUMBER)) {
            subscribe.setIspOrderNo(IdWorker.get32UUID());
            this.createSubscribeDbAndEs(subscribe);
            return Result.ok("WHITE_NUMBER", subscribe.getIspOrderNo());
        }
        try {
            KuaimaMiniAppResult kuaimaMiniAppResult = kuaimaMiniAppService.prepareOrder(mobile, subscribe.getBizCode());
            if (kuaimaMiniAppResult.isOk()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                JsonNode jsonNode = mapper.readTree(kuaimaMiniAppResult.getData());
                String serialNumber = jsonNode.get("serial_number").asText();
                subscribe.setServiceId(scmccChannel);
                subscribe.setIspOrderNo(serialNumber);
                subscribe.setPushRespMessage(kuaimaMiniAppResult.getOrderId());
                this.createSubscribeDbAndEs(subscribe);
                return Result.ok("预下单成功", serialNumber);
            } else {
                return Result.error("预下单失败");
            }
        } catch (Exception e) {
            return Result.error("预下单失败");
        }
    }

    @Override
    public Result<?> receiveGansuMobileOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                KuaimaGansuResult kuaimaGansuResult = kuaimaGansuService.getSms(mobile);
                if (kuaimaGansuResult.isSuccess()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                KuaimaGansuResult kuaimaGansuResult = kuaimaGansuService.smsCode(mobile, smsCode);
                if (kuaimaGansuResult.isSuccess()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(kuaimaGansuResult.getMessage());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(kuaimaGansuResult.getMessage());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(kuaimaGansuResult.getMessage());
                }
            } catch (Exception e) {
                return Result.error("提交验证码失败");
            }
        }
    }


    @Override
    public Result<?> receiveGansuMobileLbtxOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                GansuMobileResult gansuMobileResult = gansuYidongService.getSms(mobile);
                if (gansuMobileResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送", gansuMobileResult.getResult());
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                GansuMobileResult gansuMobileResult = gansuYidongService.smsCode(mobile, smsCode, transactionId);
                if (gansuMobileResult.isOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(gansuMobileResult.getResult());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(gansuMobileResult.getResult());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(gansuMobileResult.getResult());
                }
            } catch (Exception e) {
                return Result.error("提交验证码失败");
            }
        }
    }

    @Override
    public Result<?> receiveXinjiangOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            XinjiangYidongResult xinjiangYidongResult = xinjiangYidongService.getSms(mobile, subscribe.getChannel());
            if (xinjiangYidongResult.isOk()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                this.createSubscribeDbAndEs(subscribe);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("获取验证码失败");
            }
        } else {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = this.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
            boolean validateSms = xinjiangYidongService.validateSmsCode(mobile, subscribe.getChannel(), smsCode);
            if (!validateSms) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult("验证码错误");
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
                return Result.error("验证码错误");
            }
            XinjiangYidongResult xinjiangYidongResult = xinjiangYidongService.order(mobile, subscribe.getChannel());
            if (xinjiangYidongResult.isOk()) {
                //订阅成功
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setResult(xinjiangYidongResult.getRespDesc());
                this.updateSubscribeDbAndEs(upd);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(target);
                return Result.ok("订阅成功");
            } else {
                if (!SUBSCRIBE_STATUS_SUCCESS.equals(target.getStatus())) {
                    //订阅失败
                    Subscribe upd = new Subscribe();
                    upd.setId(transactionId);
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                    upd.setOpenTime(new Date());
                    upd.setResult(xinjiangYidongResult.getRespDesc());
                    this.updateSubscribeDbAndEs(upd);
                }
                return Result.error(xinjiangYidongResult.getRespDesc());
            }
        }
    }

    @Override
    public Result<?> receiveHeilongjiangMobileOrderFuse(Subscribe subscribe) {
        //如果有这个key，就关闭业务
//        if (redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HLJYD)) {
//            return Result.error("暂未开放");
//        }
        //根据渠道号判断开通数量，黑龙江二个业务都只开500
//        Integer count = getIncrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//        Integer dyCount = getIncrChannelProvinceLimit(BIZ_TYPE_HLJYD_DYVRBT, subscribe.getProvince());
//        Integer limitCount = redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_VRBT) != null ? (Integer) redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_VRBT) : 0;
//        Integer dyLimitCount = redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_DYVRBT) != null ? (Integer) redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_DYVRBT) : 0;
//        if (count >= limitCount && dyCount >= dyLimitCount) {
//            return Result.error("特定业务用户限量");
//        }
//        if (count >= limitCount && dyCount < dyLimitCount) {
//            subscribe.setChannel(BIZ_TYPE_HLJYD_DYVRBT);
//            subscribe.setBizType(BIZ_TYPE_HLJYD_DYVRBT);
//        }
        Integer dyCount = getIncrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
        Integer dyLimitCount = redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_DYVRBT) != null ? (Integer) redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_DYVRBT) : 0;
        Integer xqyDyLimitCount = redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_VRBT) != null ? (Integer) redisUtil.get(CacheConstant.CMS_CACHE_BIZ_HLJYD_VRBT) : 0;
        if (BIZ_TYPE_HLJYD_DYVRBT.equals(subscribe.getChannel()) && dyCount >= dyLimitCount) {
            return Result.error("特定业务用户限量");
        }
        if (BIZ_TYPE_HLJYD_XQY_DYVRBT.equals(subscribe.getChannel()) && dyCount >= xqyDyLimitCount) {
            return Result.error("特定业务用户限量");
        }
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                HeilongjiangMobileResult heilongjiangMobileResult;
                if (BIZ_TYPE_HLJYD_DYVRBT.equals(subscribe.getChannel())) {
                    heilongjiangMobileResult = heilongjiangYidongVrbtService.getSms(mobile, subscribe.getIp(), subscribe.getChannel());
                } else {
                    heilongjiangMobileResult = heilongjiangYidongXqyVrbtService.getSms(mobile, subscribe.getIp(), subscribe.getChannel());
                }
                if (heilongjiangMobileResult.isGetOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            try {
                redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
                HeilongjiangMobileResult heilongjiangMobileResult;
                if (BIZ_TYPE_HLJYD_DYVRBT.equals(subscribe.getChannel())) {
                    heilongjiangMobileResult = heilongjiangYidongVrbtService.smsCode(mobile, smsCode, subscribe.getCity(), subscribe.getChannel());
                } else {
                    heilongjiangMobileResult = heilongjiangYidongXqyVrbtService.smsCode(mobile, smsCode, subscribe.getCity(), subscribe.getChannel());
                }
                subscribe.setServiceId(subscribe.getHcmccChannel());
                if (heilongjiangMobileResult.isSubOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                    subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(heilongjiangMobileResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
//                    incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//                    if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//                        this.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//                    } else {
//                        String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//                        if (StringUtils.isNotBlank(owner)) {
//                            this.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//                        }
//                    }
                    //写入自增序列
                    this.saveChannelLimit(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                    subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(heilongjiangMobileResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(heilongjiangMobileResult.getResMsg());
                }
            } catch (Exception e) {
                return Result.error("提交验证码错误");
            }
        }
    }

    @Override
    public Result<?> receiveGuangxiOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                GuangxiMobileResult guangxiMobileResult = guangxiYidongService.getSms(mobile);
                if (guangxiMobileResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                GuangxiMobileResult guangxiMobileResult = guangxiYidongService.smsCode(mobile, smsCode);
                if (guangxiMobileResult.isOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(guangxiMobileResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(guangxiMobileResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(guangxiMobileResult.getResMsg());
                }
            } catch (Exception e) {
                return Result.error("提交验证码失败");
            }
        }
    }

    @Override
    public Result<?> receiveNingxiaOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                GansuMobileResult gansuMobileResult = ningxiaYidongService.getSms(mobile,subscribe.getChannel());
                if (gansuMobileResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                GansuMobileResult gansuMobileResult = ningxiaYidongService.smsCode(mobile, smsCode,subscribe.getChannel());
                if (gansuMobileResult.isOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(gansuMobileResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(gansuMobileResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(gansuMobileResult.getResMsg());
                }
            } catch (Exception e) {
                return Result.error("提交验证码失败");
            }
        }
    }

    @Override
    public Result<?> receiveSichuanLiantongLbtxOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                SichuanLiantongResult sichuanLiantongResult = sichuanLiantongService.getSms(mobile);
                if (sichuanLiantongResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送", sichuanLiantongResult.getResult());
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.LIANTONG_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                SichuanLiantongResult sichuanLiantongResult = sichuanLiantongService.smsCode(mobile, smsCode, transactionId);
                if (sichuanLiantongResult.isOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(sichuanLiantongResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(sichuanLiantongResult.getResMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(sichuanLiantongResult.getResMsg());
                }
            } catch (Exception e) {
                return Result.error("提交验证码失败");
            }
        }

    }

    @Override
    public Result<?> receiveHunanOrder(Subscribe subscribe) {
        //如果有这个key，就关闭业务
        if (redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HNYD)) {
            return Result.error("暂未开放");
        }
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                String sysOrderId = JunboHunanYidongService.getSysOrderId();
                JunboLlbResult junboLlbResult = junboHunanYidongService.getSms(mobile, sysOrderId, subscribe.getChannel());
                if (junboLlbResult.isOperationOk()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("获取验证码成功", sysOrderId);
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                JunboLlbResult junboLlbResult = junboHunanYidongService.smsCode(mobile, transactionId, smsCode, subscribe.getChannel());
                if (junboLlbResult.isOperationOk()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(junboLlbResult.getData().getMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(junboLlbResult.getData() != null ? junboLlbResult.getData().getMsg() : junboLlbResult.getMessage());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(junboLlbResult.getData()!=null ? junboLlbResult.getData().getMsg():junboLlbResult.getMessage());
                }
            } catch (Exception e) {
                log.error("湖南移动保存订购数据异常:",e);
                return Result.error("提交验证码失败");
            }
        }
    }

    @Override
    public Result<?> receiveSichuanLbtxOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String scmccChannel = BIZ_SCMCC_CHANNEL_LBTX;
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        if (StringUtils.equals(mobile, AdReportService.SCMCC_WHITE_NUMBER)) {
            subscribe.setIspOrderNo(IdWorker.get32UUID());
            this.createSubscribeDbAndEs(subscribe);
            return Result.ok("WHITE_NUMBER", subscribe.getIspOrderNo());
        }
        try {
            LbtxSichuanResult lbtxSichuanResult = lbtxSichuanYidongService.prepareOrder(mobile, subscribe.getBizCode());
            if (lbtxSichuanResult.isOk()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                JsonNode jsonNode = mapper.readTree(StringEscapeUtils.unescapeHtml(lbtxSichuanResult.getResult()));
                String serialNumber = jsonNode.get("serial_number").asText();
                subscribe.setServiceId(scmccChannel);
                subscribe.setIspOrderNo(serialNumber);
                this.createSubscribeDbAndEs(subscribe);
                return Result.ok("预下单成功", serialNumber);
            } else {
                return Result.error("预下单失败");
            }
        } catch (Exception e) {
            return Result.error("预下单失败");
        }
    }


    @Override
    public Result<?> receiveOrderSichuanMobileCallback(String mobile, String result, Integer status, String ispOrderNo, String fullResult) {

        if (StringUtils.isBlank(mobile) || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("无效的手机号");
        }
        if (status == null || !(status == 1 || status == 0)) {
            return Result.error("无效的订购状态");
        }
        if (StringUtils.isBlank(ispOrderNo)) {
            return Result.error("无效的订单号");
        }
        Subscribe subscribe = findByMobileAndIspOrderId(mobile, ispOrderNo);
        if (subscribe == null) {
            log.error("未查询到手机号:{},订单号:{}", mobile, ispOrderNo);
            return Result.error("无效的订单号");
        }
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过重复订单,手机号:{},订单号:{}", mobile, ispOrderNo);
            return Result.ok("成功");
        }
        subscribe.setOpenTime(new Date());
        subscribe.setStatus(status);
        subscribe.setResult(result);
        this.updateSubscribeDbAndEs(subscribe);
        //信息流广告转化上报
        channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
        SichuanMobileFlowPacketConfig sichuanMobileFlowPacketConfig = sichuanMobileFlowPacketProperties.getSichuanMobileConfig(BIZ_SCMCC_CHANNEL_JUNBO);
        if (ispOrderNo.startsWith(sichuanMobileFlowPacketConfig.getAppId())) {
            log.info("四川移动骏伯订单，手机号:{},订单号:{},开通状态:{},开通结果:{}", mobile, ispOrderNo, status, result);
            outsideCallbackService.junboCallbackAsync(subscribe, sichuanMobileFlowPacketProperties.getJunboCallbackUrl());
        }
        if (ispOrderNo.startsWith(kuaimaProperties.getAppId()) && StringUtils.isNotBlank(fullResult)) {
            log.info("四川移动快马订单，手机号:{},订单号:{},开通状态:{},开通结果:{}", mobile, ispOrderNo, status, result);
            outsideCallbackService.kuaimaCallback(mobile, fullResult, kuaimaProperties.getCallbackUrl());
        }
        if (ispOrderNo.startsWith(kuaimaMiniAppProperties.getAppId()) && StringUtils.isNotBlank(fullResult)) {
            log.info("四川移动快马小程序订单，手机号:{},订单号:{},开通状态:{},开通结果:{}", mobile, ispOrderNo, status, result);
            outsideCallbackService.kuaimaMiniAppCallback(mobile, subscribe.getPushRespMessage(), fullResult, kuaimaMiniAppProperties.getCallbackUrl());
        }
        if (ispOrderNo.startsWith(lbtxSichuanYidongProperties.getAppId()) && StringUtils.isNotBlank(fullResult)) {
            log.info("四川移动快联保天下订单，手机号:{},订单号:{},开通状态:{},开通结果:{}", mobile, ispOrderNo, status, result);
            if (SUBSCRIBE_STATUS_SUCCESS.equals(status)) { //只处理成功
                try {
                    JsonNode jsonNode = mapper.readTree(fullResult);
                    outsideCallbackService.lbtxCallback(mobile, subscribe.getIspOrderNo(), jsonNode.get("result").asText(), lbtxSichuanYidongProperties.getCallbackUrl());
                } catch (Exception e) {
                    log.error("四川移动快联保天下订单异常,手机号:{},订单号:{}", mobile, subscribe.getIspOrderNo());
                }
            }
        }
        return Result.ok("成功");
    }


    //@Override
    //public Result<?> receiveDianxinAloneOrder(Subscribe subscribe) {
    //    //运营商
    //    String isp = MobileRegionResult.ISP_YIDONG;
    //    String mobile = subscribe.getMobile();
    //    //设置归属地
    //    try {
    //        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
    //        if(mobileRegionResult!=null){
    //            subscribe.setProvince(mobileRegionResult.getProvince());
    //            subscribe.setCity(mobileRegionResult.getCity());
    //            isp = mobileRegionResult.getOperator();
    //            subscribe.setIsp(isp);
    //            if(!mobileRegionResult.isIspDianxin()){
    //                return Result.error("该业务只支持电信用户!");
    //            }
    //        }
    //    } catch (Exception e) {
    //        e.printStackTrace();
    //        return Result.error("系统繁忙,请稍后再试!");
    //    }
    //    //手机号已经在调用方法中校验,此处跳过
    //    //如果是带短信的,就发给开通服务器的redis并返回成功
    //    String smsCode = subscribe.getSmsCode();
    //    if(StringUtils.isNotEmpty(smsCode)){
    //        //验证短信验证码是否合法
    //        if(!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)){
    //            return Result.captchaErr("短信验证码错误");
    //        }
    //        final String transactionId = subscribe.getTransactionId();
    //        if(Strings.isEmpty(transactionId)){
    //            return Result.captchaErr("请求参数错误");
    //        }
    //        //避免相同的错误短信验证码反复提交
    //        String  dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX+subscribe.getMobile()+"-"+smsCode;
    //        if(redisUtil.hasKey(dianxinSmsInvalidKey)){
    //            return Result.captchaErr("请勿重复提交");
    //        }
    //        redisUtil.set(dianxinSmsInvalidKey,"invalidSmsCode",DIANXIN_SMS_INVALID_CACHE_SECONDS);
    //        //此处保存已提交验证码
    //        if(SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
    //            Subscribe upd = new Subscribe();
    //            upd.setId(transactionId);
    //            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
    //            upd.setModifyTime(new Date());
    //            this.updateSubscribeDbAndEs(upd);
    //        }
    //        return pushSubscribeService.pushSmsCode(transactionId,smsCode);
    //    }
    //    //根据手机号来同步,避免重复获取短信验证码
    //    synchronized (interner.intern(mobile)) {
    //        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
    //        if(redisUtil.get(mobileSendSmsLimitKey) != null){
    //            return Result.error("操作频繁,请"+ MOBILE_SEND_SMS_CATCHE_TTL_SECONDS +"秒后再试");
    //        }
    //        this.createSubscribeDbAndEs(subscribe);
    //        //写渠道订阅日志
    //        BizLogUtils.logSubscribe(subscribe);
    //        final Result<?> result = pushSubscribeService.dianxinHandleVrbtOfficial(subscribe);
    //        if(result!=null&& CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())){
    //            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
    //        }
    //        return result;
    //    }
    //}

    /**
     * 接收白金会员开通订单
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveBjhyOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();

//        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + mobile;
//        if(redisUtil.get(mobileMonthExistsKey) != null){
//            return Result.bizExists("你已开通,请勿重复开通");
//        }

        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (StringUtils.isEmpty(subscribe.getSmsCode())) {
                    if (mobileRegionResult.isIspYidong()) {
                        //切换到宜搜
                        if (MiguApiService.BIZ_BJHY_CHANNEL_CODE.equals(subscribe.getChannel()) && vrbtChannelProvinceConfigService.allow(MiguApiService.BIZ_BJHYDY_CHANNEL_CODE, subscribe.getProvince()) && RandomUtils.isInRatio(20)) {
                            subscribe.setChannel(MiguApiService.BIZ_BJHYDY_CHANNEL_CODE);
                        }
                        //切换到PP
                        if (MiguApiService.BIZ_BJHYDY_CHANNEL_CODE.equals(subscribe.getChannel()) && !vrbtChannelProvinceConfigService.allow(MiguApiService.BIZ_BJHYDY_CHANNEL_CODE, subscribe.getProvince()) && vrbtChannelProvinceConfigService.allow(MiguApiService.BIZ_BJHY_CHANNEL_CODE, subscribe.getProvince())) {
                            subscribe.setChannel(MiguApiService.BIZ_BJHY_CHANNEL_CODE);
                        }

                        //白金会员 广西全部切换到U1
                        if (!MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1.equals(subscribe.getChannel()) && GUANGXI_PROVINCE.equals(subscribe.getProvince()) && vrbtChannelProvinceConfigService.allow(MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1, subscribe.getProvince())) {
                            subscribe.setChannel(MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1);
                        }
                    }
                }
                if (mobileRegionResult.isIspYidong()) {
                    List<String> provinceList = vrbtChannelProvinceConfigService.getProvinceListByChannel(subscribe.getChannel());
                    if (!provinceList.contains(mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.cmccRedirect("TO_READ");
//                        return Result.error("暂未开放,敬请期待!");
                    }
                    if (HEBEI_PROVINCE.equals(subscribe.getProvince())) {
//                        return Result.cmccRedirect("TO_HBYD");
                        return SpringContextUtils.getBean(HebeiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                    }
                    if (SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                        return shanghaiMobileDouYinVrbt(subscribe);
//                        subscribe.setChannel(BIZ_TYPE_SHYD_XSSP_VS);
//                        subscribe.setBizType(BIZ_TYPE_SHYD_XSSP_VS);
//                        Result<?> result = shanghaiMobileOpenUp(subscribe,ShanghaiMobileConstant.IS_YES_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                        return result;
                        //潘潘要求关上海
                        return Result.error("暂未开放，敬请期待!");
                    }
                    if (HEILONGJIANG_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_HLJYD_DYVRBT);
                        subscribe.setBizType(BIZ_TYPE_HLJYD_DYVRBT);
                        return receiveHeilongjiangMobileOrderFuse(subscribe);
                    }
                    if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_HN_VRBT);
                        subscribe.setBizType(BIZ_TYPE_HN_VRBT);
                        return receiveHunanOrder(subscribe);
                    }
//                    if(SICHUAN_PROVINCE.equals(subscribe.getProvince())){
//                        return Result.cmccRedirect("TO_SCYD_BJHY");
//                    }


                    if (CHONGQING_PROVINCE.equals(subscribe.getProvince())) {
                        return Result.cmccRedirect("TO_CQYD");
                    }
                } else {
                    if (mobileRegionResult.isIspLiantong()) {
//                        return innerUnionMemberService.forword(subscribe);
                        return Result.unicomRedirect("TO_XDCL");
                    } else if (mobileRegionResult.isIspDianxin()) {
                        return Result.telecomRedirect("TO_MAIHE");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        //白金会员破解
        Result<?> result = this.receiveBjhyCrackOrder(subscribe);
        return result;
    }

    /**
     * 接收白金会员订单
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveBjhyOrder(Subscribe subscribe) {
        final Result<Object> okResult = Result.ok("订阅成功");

        final String mobile = subscribe.getMobile();
        final String subChannel = subscribe.getSubChannel();


        //String mobileAuthKey = MOBILE_AUTH_KEY_PREFIX+ mobile;
        //final String authValue = (String) redisUtil.get(mobileAuthKey);

        //10分钟内未验证过同时需要验证
        //if(authValue==null && channelService.needSmsValidate(subChannel)) {
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            smsValidateService.create(mobile, subscribe.getChannel());
            smsValidateLogService.logCreate(mobile, subChannel);
            return Result.noauth("验证码已发送");
        } else {
            //验证短信验证码,如果错误会抛出异常被全局捕获并返回json
            try {
                smsValidateService.check(mobile, smsCode);
                //redisUtil.set(mobileAuthKey,MOBILE_AUTH_VALUE, MOBILE_AUTH_CATCHE_TTL_SECONDS);
                smsValidateLogService.logValid(mobile, subChannel);
            } catch (JeecgBootException e) {
                //e.printStackTrace();
                return Result.captchaErr(e.getMessage());
            }
        }
        //}

        final RemoteResult remoteResult = miguApiService.bjhyQuery(mobile, subscribe.getChannel());
        //状态为"0"表示已有包月
        if (remoteResult.isBjhyMember()) {
            return Result.bizExists("你已开通,请勿重复开通");
        }

//        this.save(subscribe);
        this.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);

        pushSubscribeService.pushBjhy(subscribe);

        return okResult;
    }

    @Override
    public Result<?> receiveCpmbOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();

        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (!(mobileRegionResult.isIspYidong() || mobileRegionResult.isIspDianxin())) {
                    return Result.error("暂只支持移动和电信用户!");
                }
//                if(!mobileRegionResult.isIspDianxin()){
//                    return Result.error("暂只支持移动用户!");
//                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        if (MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) { //移动
//            if (HEBEI_PROVINCE.equals(subscribe.getProvince())) {
//                return receiveHebeiMobileOrderFuse(subscribe);
//            }
//            if (HEILONGJIANG_PROVINCE.equals(subscribe.getProvince())) {
//                return receiveHeilongjiangMobileOrderFuse(subscribe);
//            }
//            if (subscribe.getChannel().equals(MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1)) {
//                RemoteResult result = miguApiService.miguLogin(mobile, MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1);
//                if (result != null && result.isOK()) {
//                    Result resp = Result.ok("登录成功");
//                    resp.setResult(result.getToken());
//                    return Result.miguLoginToken(result.getToken());
//                } else {
//                    return Result.noauth("登录失败");
//                }
//            }
//            if (GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
//                return Result.error("暂未开放,敬请期待!");
////                subscribe.setBizType(BIZ_TYPE_GZYD_HJHY_TYHD);
////                subscribe.setChannel(BIZ_TYPE_GZYD_HJHY_TYHD);
////                return receiveGuiZhouMobileOrder(subscribe);
//            }
            if (SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
                subscribe.setChannel(BIZ_CHANNEL_SCYD_SXK);
                subscribe.setBizType(BIZ_CHANNEL_SCYD_SXK);
                String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                subscribe.setBizCode(bizCode);
                return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveSichuanMobileCrackOrder(subscribe);
            }
            return this.receiveCpmbOrderBillingCrack(subscribe);
        } else { //电信
            if (MobileRegionResult.ISP_DIANXIN.equals(subscribe.getIsp()) && SHANDONG_PROVINCE.equals(subscribe.getProvince())) {
                subscribe.setChannel(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                subscribe.setBizType(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                return receiveShandongDianxinOrder(subscribe);
            }

//            try {
//                return innerUnionMemberService.forword(subscribe, "COMIC"); //使用咪咕动漫的渠道去开通天翼空间业务
//            } catch (Exception e) {
//                return Result.error("系统错误");
//            }
            return Result.error("暂只支持移动用户");
        }
    }

    /**
     * 接收渠道包月订单
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveCpmbOrder(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给开通服务器的redis并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
//            this.updateById(upd);
                this.updateSubscribeDbAndEs(upd);
            }

            return pushSubscribeService.pushSmsCode(transactionId, smsCode, subscribe.getMobile());
        }

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }

//        this.save(subscribe);
        this.createSubscribeDbAndEs(subscribe);

        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);

        final Result<?> result = pushSubscribeService.handleCpmbExists(subscribe);

        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }

        return result;
    }

    /**
     * 接收渠道包月订单(破解计费)
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveCpmbOrderBillingCrack(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = this.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            //此处保存已提交验证码
//            lambdaUpdate().eq(Subscribe::getId,transactionId).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_INIT).
//                    set(Subscribe::getStatus,SUBSCRIBE_STATUS_SMS_CODE_SUBMITED).set(Subscribe::getModifyTime,new Date()).update();

            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
//            billingCrackService.smsCode(target.getIspOrderNo(),smsCode);

            return Result.ok("订阅成功");
        }

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }

//        this.save(subscribe);
        this.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);

        final Result<?> result = pushSubscribeService.handleCpmbExistsBillingCrack(subscribe);

        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }

        return result;
    }

    /**
     * 接收振铃开通订单(短信方式)
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveRtOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + mobile;
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + mobile;
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }

        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (!mobileRegionResult.isIspYidong()) {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }

        Result<?> result = this.receiveRtOrder(subscribe);

        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            //String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            //String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }

    /**
     * 接收振铃订单
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveRtOrder(Subscribe subscribe) {

        final RemoteResult remoteResult = miguApiService.rtMonthStatusQuery(subscribe.getMobile(), subscribe.getChannel());
        //"000000"表示已有包月
        if (remoteResult.isOK()) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        this.save(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        pushSubscribeService.pushRt(subscribe);

        return Result.noauth("验证码已发送", subscribe.getId());
    }

    @Override
    public List<ChannelReportItem> channelReport(String subChannel, LocalDate start, LocalDate end) {
        return Stream.iterate(start, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(start, end) + 1)
                .map(date -> {
                    final LocalDateTime dayMinTime = LocalDateTime.of(date, LocalTime.MIN);
                    final LocalDateTime dayMaxTime = LocalDateTime.of(date, LocalTime.MAX);
                    int success = this.lambdaQuery().between(Subscribe::getCreateTime, dayMinTime, dayMaxTime).eq(Subscribe::getSubChannel,
                            subChannel).eq(Subscribe::getStatus, 1).count();
                    int pv = visitLogService.lambdaQuery().between(VisitLog::getCreateTime, dayMinTime, dayMaxTime).eq(VisitLog::getSubChannel,
                            subChannel).count();
                    LambdaQueryWrapper<VisitLog> uvQueryWrapper = new QueryWrapper<VisitLog>().select("distinct finger")
                            .lambda()
                            .between(VisitLog::getCreateTime, dayMinTime, dayMaxTime)
                            .eq(VisitLog::getSubChannel, subChannel);
                    int uv = visitLogService.count(uvQueryWrapper);
                    int sendSms = smsValidateLogService.lambdaQuery().between(SmsValidateLog::getCreateTime, dayMinTime, dayMaxTime).eq(SmsValidateLog::getSubChannel,
                            subChannel).eq(SmsValidateLog::getAction, 0).count();
                    int verifySMS = smsValidateLogService.lambdaQuery().between(SmsValidateLog::getCreateTime, dayMinTime, dayMaxTime).eq(SmsValidateLog::getSubChannel,
                            subChannel).eq(SmsValidateLog::getAction, 1).count();
                    ChannelReportItem item = new ChannelReportItem();
                    item.setChannel(subChannel);
                    item.setDate(date);
                    item.setSuccess(success);
                    item.setPageView(pv);
                    item.setUniqueVisitor(uv);
                    item.setSendSMS(sendSms);
                    item.setVerifySMS(verifySMS);
                    return item;
                }).collect(Collectors.toList());
    }

    @Override
    public Subscribe findByMobileAndCopyrightId(String mobile, String copyrightId) {
        return this.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getCopyrightId, copyrightId)
                .gt(Subscribe::getCreateTime, DateUtils.atStartOfDay(new Date()))
                .orderByDesc(Subscribe::getCreateTime)
                .last(BizConstant.SQL_LIMIT_ONE)
                .one();
    }

    @Override
    public Subscribe dianxinFindByMobile(String mobile) {
        return this.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .isNotNull(Subscribe::getIspOrderNo)
                .orderByDesc(Subscribe::getId)
                //.eq(Subscribe::getOprType, MIGU_ORDER_OPR_TYPE_SUBSCRIBE)
                .last(BizConstant.SQL_LIMIT_ONE)
                .one();
    }


    @Override
    public Subscribe dianxinHunanFindByMobile(String mobile,String channelCode) {
        return this.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, channelCode)
                .isNotNull(Subscribe::getIspOrderNo)
                .orderByDesc(Subscribe::getCreateTime)
                .last(BizConstant.SQL_LIMIT_ONE)
                .one();
    }

    //@Override
    //public Subscribe findByIspOrderId(String orderId) {
    //    return this.lambdaQuery()
    //            .eq(Subscribe::getIspOrderNo, orderId)
    //            //.last(BizConstant.SQL_LIMIT_ONE)
    //            .one();
    //}

    @Override
    public Subscribe findByMobileAndIspOrderId(String mobile, String orderId) {
        return this.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getIspOrderNo, orderId)
                .last(BizConstant.SQL_LIMIT_ONE)
                .one();
    }


    @Override
    public Subscribe findByIspOrderId(String orderId) {
        return this.lambdaQuery()
                .eq(Subscribe::getIspOrderNo, orderId)
                .last(BizConstant.SQL_LIMIT_ONE)
                .one();
    }


    /**
     * 判断当时此手机号是否未进行广告转化上报
     *
     * @param mobile
     * @return
     */
    @Override
    public boolean isAdUnFeedbackToday(String mobile) {
        if (subscribeServiceTestProperties.getTestAccountMap().get(mobile) != null) {
            return true;
        }
        return this.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .between(Subscribe::getCreateTime, LocalDate.now().atTime(LocalTime.MIN), LocalDate.now().atTime(LocalTime.MAX))
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .eq(Subscribe::getTuiaFeedbackStatus, 1)
                .count() == 0;
    }

    //@Transactional
    @Override
    public void updateToneOrderExtra(Subscribe subscribe, String copyrightId, RemoteResult remoteResult) {
        if (subscribe == null || remoteResult == null) {
            return;
        }
        try {
            String extra = mapper.writeValueAsString(remoteResult);
            final Date now = new Date();
            Subscribe upd = new Subscribe();
            if (StringUtils.isEmpty(subscribe.getCopyrightId())) {
                upd.setCopyrightId(copyrightId);
            }
            upd.setId(subscribe.getId());
            upd.setExtra(extra);
            upd.setModifyTime(now);
            this.updateSubscribeDbAndEs(upd);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Transactional
    @Override
    public void dianxinUpdateToneOrderExtra(String mobile, String dxToneCode, DianxinResp toneOrderResp) {
        if (toneOrderResp == null) {
            return;
        }
        try {
            String extra = mapper.writeValueAsString(toneOrderResp);
            final Date now = new Date();
            this.lambdaUpdate().eq(Subscribe::getMobile, mobile)
                    .eq(Subscribe::getDxToneCode, dxToneCode)
                    .gt(Subscribe::getCreateTime, DateUtils.atStartOfDay(now))
                    .set(Subscribe::getExtra, extra)
                    .set(Subscribe::getModifyTime, now)
                    .update();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询最近30天内是否有成功订购的记录
     *
     * @param mobile
     * @return
     */
    @Cacheable(cacheNames = CacheConstant.CMS_SUBSCRIBE_CACHE, key = "#root.methodName + ':' + #p0", condition = "#p0!=null", unless = "#result==null")
    @Override
    public boolean hasRecentSucceedOrder(String mobile) {
        Integer count = this.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).gt(Subscribe::getCreateTime, LocalDateTime.now().minusDays(30L)).count();
        return count > 0;
    }

    /**
     * 视频彩铃公共开通方法
     *
     * @param subscribe
     */
    public Result publicOpenVrbt(Subscribe subscribe) {
        final Result<Object> okResult = Result.ok("订阅成功");
//        boolean noneChannelSwitch = StringUtils.equals(subscribe.getVrbtChSwitch(),"0");
//        //根据省份切换(开放和彩铃中心)渠道号
//        String miguChannel = noneChannelSwitch ? subscribe.getChannel() : vrbtProvinceSwitchConfigService.vrbtMiguChannelSwitch(subscribe.getChannel(),subscribe.getProvince());
//        subscribe.setChannel(miguChannel);
        // this.save(subscribe);
        this.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        //先保存得到id后推送至开通服务器,push方法会判断是否有包月,有包月的就不推送,有包月的需要订购视频彩铃的就直接订购
        pushSubscribeService.pushWithMonthlyExistsCheck(subscribe);
        return okResult;
    }


    /**
     * 视频彩铃破解开通
     *
     * @param subscribe
     * @return
     */
    @Override
    @ValidationLimit
    public Result receiveVrbtCrackOrder(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = this.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, target.getChannel(), subscribe.getMobile());
            return Result.ok("订阅成功");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        this.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final Result<?> result = pushSubscribeService.handleVrbtExistsBillingCrack(subscribe);
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        //500错误写入缓存
        if (result != null && CommonConstant.SC_INTERNAL_SERVER_ERROR_500.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }


    @Override
    public Subscribe transformSubscribeFromOrderLog(com.eleven.cms.log.OrderLog orderLog, String ipAddr) {
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(orderLog.getMobile());
        subscribe.setChannel(orderLog.getMiguChannel());
        String serviceId = cmsCrackConfigService.getCrackConfigByChannel(orderLog.getMiguChannel()).getServiceId();
        subscribe.setServiceId(serviceId);
        subscribe.setCopyrightId(orderLog.getExtra());
        subscribe.setResult(orderLog.getMiguResMsg() + "=>" + orderLog.getMiguResCode());
        if (StringUtils.isNotBlank(orderLog.getMiguResCode())) {
            subscribe.setPushRespCode(Integer.valueOf(orderLog.getMiguResCode()));
        }
        subscribe.setPushRespMessage(orderLog.getMiguResMsg());
        subscribe.setProvince(orderLog.getProvince());
        subscribe.setCity(orderLog.getCity());
        ;
        subscribe.setIp(ipAddr);
        subscribe.setIsp(MobileRegionResult.ISP_YIDONG);
        subscribe.setBizType(BizConstant.BIZ_TYPE_VRBT);
        subscribe.setSubChannel(BizConstant.SUB_CHANNEL_BAOBEI);
        subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
        subscribe.setCreateTime(new Date());
        this.save(subscribe);
        return subscribe;
    }


    @Override
    public Subscribe saveBjhyLemobaSubscribe(com.eleven.cms.log.OrderLog orderLog, String ipAddr) {
        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(orderLog.getMobile());
        subscribe.setChannel(orderLog.getMiguChannel());
        subscribe.setSubChannel(orderLog.getSubChannel());
        String serviceId = cmsCrackConfigService.getCrackConfigByChannel(orderLog.getMiguChannel()).getServiceId();
        subscribe.setServiceId(serviceId);
        subscribe.setCopyrightId(orderLog.getExtra());
        subscribe.setResult("初始状态");
        if (StringUtils.isNotBlank(orderLog.getMiguResCode())) {
            subscribe.setPushRespCode(Integer.valueOf(orderLog.getMiguResCode()));
        }
        subscribe.setPushRespMessage(orderLog.getMiguResMsg());
        subscribe.setProvince(orderLog.getProvince());
        subscribe.setCity(orderLog.getCity());
        subscribe.setIp(ipAddr);
        subscribe.setIsp(MobileRegionResult.ISP_YIDONG);
        subscribe.setBizType(BizConstant.BIZ_TYPE_BJHY);
        subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
        subscribe.setCreateTime(new Date());
        this.save(subscribe);
        return subscribe;
    }


    @Override
    public Boolean isSatisfactionMonth(String mobile) {
        Optional<Subscribe> subscribeOpt = this.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getIsp, MobileRegionResult.ISP_YIDONG)
                .gt(Subscribe::getCreateTime, BizConstant.BEGIN_TIME)
                .eq(Subscribe::getBizType, BizConstant.BIZ_TYPE_VRBT)
                .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS)
                .list()
                .stream()
                //找到当月最后一个订单
                .max(Comparator.comparing(Subscribe::getCreateTime));
        if (subscribeOpt.isPresent()) {
            Subscribe subscribe = subscribeOpt.get();
            Calendar cal = Calendar.getInstance();
            cal.setTime(subscribe.getCreateTime());
            cal.add(Calendar.MONTH, BizConstant.MONTHLY_LIMIT); //获取当前时间的后3个月
            if (cal.getTime().getTime() <= new Date().getTime()) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }


    @Override
    public Boolean updateBjhy(Subscribe subscribe) {
        return this.lambdaUpdate()
                .eq(Subscribe::getId, subscribe.getId())
                .eq(Subscribe::getMobile, subscribe.getMobile())
                .eq(Subscribe::getChannel, subscribe.getChannel())
                .set(Subscribe::getStatus, subscribe.getStatus())
                .set(Subscribe::getResult, subscribe.getResult())
                .update();
    }

    @Override
    public Result<?> receiveDianxinHuanqiuwangOrder(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = this.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            return pushSubscribeService.pushSmsCode(transactionId, smsCode, subscribe.getMobile());
        }

        String mobile = subscribe.getMobile();
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
//            //下单之前先绑定渠道号
//            boolean isBind = false;
//            try {
//                isBind = huanqiuwangDianxinVrbtService.bind(mobile);
//            }catch (Exception e){
//                log.error("环球网渠道号绑定异常:{}", e);
//            }
//            if(!isBind){
//                return Result.error("获取验证码失败");
//            }
            HuanqiuwangDianxinVrbtResult huanqiuwangDianxinVrbtResult = huanqiuwangDianxinVrbtService.order(mobile);
            final String id = subscribe.getId();
            if (huanqiuwangDianxinVrbtResult.isOK()) {
                //更新运营商生成的订单号,后续接收运营商回调通知来更新钉够结果需要这个订单号
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setIspOrderNo(huanqiuwangDianxinVrbtResult.getOrderId());
                this.updateSubscribeDbAndEs(upd);
                String orderPage = "https://m.imusic.cn/paycenter/#/openauth/confirm?order_no=" + huanqiuwangDianxinVrbtResult.getOrderId();
                Result<?> result = pushSubscribeService.pushHuanqiuwangDxVrbt(subscribe, orderPage);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return CommonConstant.SC_OK_200.equals(result.getCode()) ? Result.noauth("验证码已发送", id) : Result.error("系统繁忙,请稍后再试!");
            } else {
                //如果接口超出限制
                if ("Z9999".equals(huanqiuwangDianxinVrbtResult.getResult())) {
                    //切换为默认渠道
                    String currentChannelCode = (String) redisUtil.get(CacheConstant.DIANXIN_CURRENT_CHANNEL_KEY);
                    if (BIZ_DIANXIN_CHANNEL_HUANQIUWANG.equals(currentChannelCode)) {
                        redisUtil.set(CacheConstant.DIANXIN_CURRENT_CHANNEL_KEY, BIZ_DIANXIN_CHANNEL_DEFAULT, DateUtil.getSecondByNowDiffEndOfDay());
                    }
                }

                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(huanqiuwangDianxinVrbtResult.getResult() + "@" + huanqiuwangDianxinVrbtResult.getMsg());
                this.updateSubscribeDbAndEs(upd);


            }
            return Result.error("获取验证码失败");
        }
    }

    @Override
    public void receiveDianxinHuanqiuwangNotify(String mobile, String orderId, String state, Date time) {
        Integer status = "1".equals(state) ? 1 : 0;
        //只处理订购成功通知
        Subscribe subscribe = findByMobileAndIspOrderId(mobile, orderId);
        if (subscribe == null) {
            log.error("环球网订购消息回调-更新订购结果失败,不存在该手机号的订购通知记录,手机号:{}", mobile);
            return;
        }
        //跳过重复收到的通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("联环球网订购消异步通知-跳过重复通知");
            return;
        }
        Date now = new Date();
        //信息流广告转化上报
        if (Lists.newArrayList("A6xBX1", "A6xDM1", "E1lCT1").contains(subscribe.getSubChannel())) {
            channelService.AdEffectFeedbackNew(subscribe, status);
        }

        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult("电信爱音乐环球网订购成功消息回调");
        upd.setTuiaFeedbackStatus(subscribe.getTuiaFeedbackStatus());
        upd.setOpenTime(time);
        upd.setModifyTime(now);
        this.updateSubscribeDbAndEs(upd);
    }


    @Override
    public Result<?> receiveHebeiMobileOrderFuse(Subscribe subscribe) {
        if (true) {
            return Result.error("暂未开放");
        }
        if (StringUtils.isEmpty(subscribe.getHcmccChannel())) {
            subscribe.setHcmccChannel(BIZ_HCMCC_CHANNEL_YD);
        }
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                HebeiMobileResult hebeiMobileResult = hebeiYidongVrbtService.getSms(mobile, subscribe.getHcmccChannel());
                if (hebeiMobileResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            HebeiMobileResult hebeiMobileResult = hebeiYidongVrbtService.smsCode(mobile, smsCode, subscribe.getHcmccChannel(),subscribe.getReferer(),subscribe.getIp());
            subscribe.setChannel(BIZ_TYPE_HBYD_VRBT);
            subscribe.setBizType(BIZ_TYPE_HBYD_VRBT);
            subscribe.setServiceId(subscribe.getHcmccChannel());
            if (hebeiMobileResult.isOK()) {
                //订阅成功
                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//                    subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
                subscribe.setOpenTime(new Date());
                subscribe.setResult(hebeiMobileResult.getResMsg());
                this.createSubscribeDbAndEs(subscribe);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                return Result.ok("订阅成功");
            } else {
                //订阅成功
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//                    subscribe.setIspOrderNo(sichuanMobileResult.getResult().getOrderId());
                subscribe.setOpenTime(new Date());
                subscribe.setResult(hebeiMobileResult.getResMsg());
                this.createSubscribeDbAndEs(subscribe);
                return Result.error("订阅失败");
            }
        }
    }

    /**
     * 四川移动开通
     *
     * @param subscribe
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> receiveSichuanMobileOrderFuseV1(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String scmccChannel = subscribe.getScmccChannel();
        if (StringUtils.isEmpty(scmccChannel)) {
            scmccChannel = BIZ_SCMCC_CHANNEL_YRJY;
        }
        if (StringUtils.equals(subscribe.getScmccRedirect(), "1")) {
            return Result.scmccRedirect("redirect to scmcc");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        try {
            SichuanMobilePrepareOrderResult sichuanMobileResult = sichuanMobileFlowPacketService.prepareOrder(mobile, subscribe.getBizCode(), scmccChannel);
            if (sichuanMobileResult.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                String jsonStr = sichuanMobileFlowPacketService.decryptResult(sichuanMobileResult.getResult(), scmccChannel);
                JsonNode jsonNode = mapper.readTree(jsonStr);
                String serialNumber = jsonNode.get("serial_number").asText();
                subscribe.setIspOrderNo(serialNumber);
                subscribe.setServiceId(scmccChannel);
                this.createSubscribeDbAndEs(subscribe);
                return Result.ok("预下单成功", serialNumber);
            } else {
                return Result.error("预下单失败");
            }
        } catch (Exception e) {
            return Result.error("预下单失败");
        }
    }

    @Override
    public Result<?> receiveChongqingMobileOrderFuse(Subscribe subscribe) {
        switch (subscribe.getChannel()) {
            case BIZ_TYPE_CQYD_VRBT_DX:
                subscribe.setBizCode(chongqingYidongVrbtNewProperties.getVrbtDxCode());
                break;
            case BIZ_TYPE_CQYD_5G_QYB:
                subscribe.setBizCode(chongqingYidongVrbtNewProperties.getFgQybCode());
                break;
            case BIZ_TYPE_CQYD_MGHYHYB:
                subscribe.setBizCode(chongqingYidongVrbtNewProperties.getMghyHybCode());
                break;
            case BIZ_TYPE_CQYD_VRBTHYB:
                subscribe.setBizCode(chongqingYidongVrbtNewProperties.getVrbtHybCode());
                break;
        }
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                ChongqingMobileNewResult chongqingMobileNewResult = chongqingYidongVrbtNewService.getSms(mobile, subscribe.getBizCode());
                if (chongqingMobileNewResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            ChongqingMobileNewResult chongqingMobileNewResult = chongqingYidongVrbtNewService.smsCode(mobile, smsCode, subscribe.getBizCode());
            if (chongqingMobileNewResult.isOK()) {
                //订阅成功
                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                subscribe.setOpenTime(new Date());
                subscribe.setResult(chongqingMobileNewResult.getMessage());
                this.createSubscribeDbAndEs(subscribe);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                return Result.ok("订阅成功");
            } else {
                //订阅成功
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribe.setOpenTime(new Date());
                subscribe.setResult(chongqingMobileNewResult.getMessage());
                this.createSubscribeDbAndEs(subscribe);
                return Result.error(chongqingMobileNewResult.getMessage());
            }
        }
    }

    @Override
    @ValidationLimit
    public Result<?> getOutsideCodeCrack(Subscribe subscribe) {
        String id = IdWorker.getIdStr();
        String subChannel = subscribe.getSubChannel();
        final String miguChannel = subscribe.getChannel();
        subChannel = StringUtils.isEmpty(subChannel) ? BizConstant.SUB_CHANNEL_DEFAULT : subChannel;
        final String bizType = getBizTypeByMiguChannel(miguChannel);
        subscribe.setBizType(bizType);
        //移动处理
        if (MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) {
            String mobile = subscribe.getMobile();
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
            if (redisUtil.get(mobileMonthExistsKey) != null) {
                return Result.bizExists("你已开通,请勿重复开通");
            }
            //调用分省业务
            OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subChannel);
            if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince())) {
                String provinceBusiness = outsideConfig.getProvinceBusiness();
                String provinceChannel = "";
                for (String provinceChannelString : provinceBusiness.split(",")) {
                    if (provinceChannelString.contains(subscribe.getProvince())) {
                        provinceChannel = StringUtils.substringAfterLast(provinceChannelString, subscribe.getProvince() + "-");
                        subscribe.setChannel(provinceChannel);
                        subscribe.setBizType(getBizTypeByMiguChannel(provinceChannel));
                        break;
                    }
                }
                this.createSubscribeDbAndEs(subscribe);
                Result fuseResult = outsideFuseProvinceGetSmsCode(subChannel, subscribe);
                if (fuseResult != null) {
                    return fuseResult;
                }
            } else {
                this.createSubscribeDbAndEs(subscribe);
            }
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            if (BIZ_TYPE_BFM.equals(subscribe.getBizType())) {
                final BillingResult billingResult = yidongVrbtCrackService.getSms(mobile, subscribe.getChannel());
                if (billingResult.isOK()) {
                    Subscribe upd = new Subscribe();
                    upd.setId(subscribe.getId());
                    upd.setIspOrderNo(billingResult.getTransId());
                    updateSubscribeDbAndEs(upd);
                    return Result.ok("验证码已发送", subscribe.getId());
                }
            } else if (BIZ_TYPE_BJHY.equals(subscribe.getBizType())) {
                final Result<?> result = pushSubscribeService.handleOutsideBjhyExistsBillingCrack(subscribe);
                if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                }
                if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
                    redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
                }
                return result;
            } else if (BIZ_TYPE_CPMB.equals(subscribe.getBizType())) {
                final Result<?> result = pushSubscribeService.handleOutsideCpmbExistsBillingCrack(subscribe);
                if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                }
                if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
                    redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
                }
                return result;
            } else if (BIZ_TYPE_UNION_MEMBER.equals(subscribe.getBizType())) {
                final Result<?> result = pushSubscribeService.handleOutsideUnionMemberExistsBillingCrack(subscribe);
                if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                }
                if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
                    redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
                }
                return result;
                //贵州移动
            }else if (StringUtils.startsWith(subscribe.getBizType(),"GZYD_")) {
                GuizhouMobileCodeResult guizhouMobileCodeResult = guizhouYidongService.getSms(mobile, UUID.randomUUID().toString().replace("-", ""), subscribe.getChannel(), "https://crbt.cdyrjygs.com", id);
                if (guizhouMobileCodeResult != null) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    final String serialNumber = guizhouMobileCodeResult.getSerialNumber();
                    Subscribe upd = new Subscribe();
                    upd.setId(subscribe.getId());
                    upd.setIspOrderNo(serialNumber);
                    updateSubscribeDbAndEs(upd);
                    return Result.ok("验证码已发送", subscribe.getId());
                } else {
                    Result.error("系统繁忙,请稍后再试!");
                }
            } else {
                final Result<?> result = pushSubscribeService.handleOutsideVrbtExistsBillingCrack(subscribe);
                if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                }
                if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
                    redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
                }
                return result;
            }
        }
        if (MobileRegionResult.ISP_DIANXIN.equals(subscribe.getIsp())) {
            String mobile = subscribe.getMobile();
            OutsideConfig outsideConfig =  outsideConfigService.getOutsideConfigByOutChannel(subChannel);
            String channelCode = outsideConfig.getChannel();
            subscribe.setVrbtDxChannel(channelCode);
            subscribe.setServiceId(channelCode);
            if (!dianxinVrbtProperties.getDianxinVrbtConfig(subscribe.getVrbtDxChannel()).getProvinceList().contains(subscribe.getProvince())) {
                return Result.error("暂未开放,敬请期待!");
            }
            //根据手机号来同步,避免重复获取短信验证码
            synchronized (interner.intern(mobile)) {
                String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
                if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                    return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
                }
                this.createSubscribeDbAndEs(subscribe);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                BillingResult billingResult = yidongVrbtCrackService.getSms(mobile, subscribe.getVrbtDxChannel());
                if (billingResult.isOK()) {
                    Subscribe upd = new Subscribe();
                    upd.setId(subscribe.getId());
                    upd.setIspOrderNo(billingResult.getTransId());
                    this.updateSubscribeDbAndEs(upd);
                    return Result.ok("验证码已发送", billingResult.getTransId());
                } else {
                    return Result.error("系统繁忙,请稍后再试!");
                }
            }
        }
        return Result.error("暂不支持联通用户");
    }


    public Result outsideFuseProvinceGetSmsCode(String subChannel,Subscribe subscribe){
        OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subChannel);
        if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
            return pushSubscribeService.getSmsCodeByOutsideShydXssp(subscribe);
        }
        if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && JIANGSU_PROVINCE.equals(subscribe.getProvince())) {
            return pushSubscribeService.getSmsCodeByOutsideJsyd(subscribe);
        }
        if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
            return pushSubscribeService.getSmsCodeByOutsideScyd(subscribe);
        }
        if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && HUNAN_PROVINCE.equals(subscribe.getProvince())) {
            return pushSubscribeService.getSmsCodeByOutsideHnyd(subscribe);
        }
        if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && LIAONING_PROVINCE.equals(subscribe.getProvince())) {
            return pushSubscribeService.getSmsCodeByOutsideHetu(subscribe);
        }
        if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
            return pushSubscribeService.getSmsCodeByOutsideGzyd(subscribe);
        }
        if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && JIANGXI_PROVINCE.equals(subscribe.getProvince())) {
            return pushSubscribeService.getSmsCodeByOutsideJxyd(subscribe);
        }
       return null;
    }


    @Override
    public Result<?> receiveOrderOutsideCrack(String osOrderId, String smsCode) {
        synchronized (interner.intern(osOrderId)){
            Subscribe subscribe = this.getById(osOrderId);
            subscribe.setSmsCode(smsCode);
            if (subscribe == null) {
                return Result.error("无效的订单号");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subscribe.getSubChannel());
            if(outsideConfig==null){
                log.error("无效的外部渠道号:{}", subscribe.getSubChannel());
                return Result.error("无效的渠道号!");
            }
            if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && SHANGHAI_PROVINCE.equals(
                    subscribe.getProvince())) {
                Result result = pushSubscribeService.smsCodeByOutsideShydXssp(subscribe);
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setResult(result.getMessage());
                if (result.isOK()) {
                    //订阅成功
                    upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    upd.setOpenTime(new Date());
                    if(BizConstant.BIZ_CHANNEL_SHYD_XSSP_VS.equals(subscribe.getChannel())) {
                        rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                    }
                } else {
                    //订阅失败
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                }
                this.updateSubscribeDbAndEs(upd);
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                //            outsideCallbackService.outsideCallback(getById(upd.getId()), "回调通知");
                return Result.ok("短信验证码提交成功");
            }
            if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && JIANGSU_PROVINCE.equals(
                    subscribe.getProvince())) {
                JiangsuResponseSmsValidate jiangsuResponseSmsValidate = jiangsuYidongService.smsValidate(subscribe.getMobile(), subscribe.getIspOrderNo(), smsCode, jiangsuYidongService.getToken());
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setResult(jiangsuResponseSmsValidate.getContent().getRetMsg());
                if (jiangsuResponseSmsValidate.isOk()) {
                    //提交验证码成功
                    upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                    redisDelayedQueueManager.addJsyd(JsydDelayedMessage.builder().mobile(subscribe.getMobile()).count(1).orderId(
                            subscribe.getIspOrderNo()).orderTimeStamp(System.currentTimeMillis() / 1000).build(), 1, TimeUnit.MINUTES);
                } else {
                    //订阅失败
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                }
                this.updateSubscribeDbAndEs(upd);
                if(!jiangsuResponseSmsValidate.isOk()) {
                    //                outsideCallbackService.outsideCallback(getById(upd.getId()), "回调通知");
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
                return Result.ok("短信验证码提交成功");
            }
            if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && SICHUAN_PROVINCE.equals(
                    subscribe.getProvince())) {
                Result result = pushSubscribeService.pushSmsCode(subscribe.getId(), smsCode, subscribe.getMobile());
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setResult(result.getMessage());
                if (result.isOK()) {
                    //订阅成功
                    upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    upd.setOpenTime(new Date());
                } else {
                    //订阅失败
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                }
                this.updateSubscribeDbAndEs(upd);
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                return Result.ok("短信验证码提交成功");
            }
            if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && LIAONING_PROVINCE.equals(
                    subscribe.getProvince())) {
                WujiongCrackResult wujiongCrackResult = wujiongCrackService.smsCode(subscribe.getMobile(), subscribe.getIspOrderNo(), smsCode, subscribe.getChannel());
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                if (wujiongCrackResult.isHetuSubOk()) {
                    upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                    this.updateSubscribeDbAndEs(upd);
                } else {
                    upd.setResult(wujiongCrackResult.getMsg());
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                    this.updateSubscribeDbAndEs(upd);
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
                return Result.ok("短信验证码提交成功");
            }
            if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && HUNAN_PROVINCE.equals(
                    subscribe.getProvince())) {
                JunboLlbResult junboLlbResult = junboHunanYidongService.smsCode(subscribe.getMobile(), subscribe.getIspOrderNo(),smsCode,
                        subscribe.getChannel());
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setResult(junboLlbResult.getData().getMsg());
                if (junboLlbResult.isOperationOk()) {
                    //提交验证码成功
                    upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    upd.setOpenTime(new Date());
                } else {
                    //订阅失败
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                }
                this.updateSubscribeDbAndEs(upd);
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                //            outsideCallbackService.outsideCallback(getById(upd.getId()), "回调通知");
                return Result.ok("短信验证码提交成功");
            }
            //江西移动
            if (StringUtils.contains(outsideConfig.getProvinceBusiness(), subscribe.getProvince()) && JIANGXI_PROVINCE.equals(subscribe.getProvince())) {
                JiangxiYidongResult jiangxiYidongResult = jiangxiYidongService.smsCode(subscribe.getMobile(), subscribe.getIspOrderNo(), smsCode, subscribe.getPushRespMessage(), subscribe.getIspOrderNo(), subscribe.getServiceId());
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                if (jiangxiYidongResult.isOk()) {
                    JiangxiYidongOrderResult jiangxiYidongOrderResult = jiangxiYidongService.order(subscribe.getMobile(), subscribe.getChannel(), subscribe.getIspOrderNo(), subscribe.getJcmccChannel());
                    if (jiangxiYidongOrderResult.isOk()) {
                        upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                        upd.setOpenTime(new Date());
                        upd.setRemark(jiangxiYidongOrderResult.getData().getResultMsg().getOrderId());
                        upd.setResult("订购成功");
                        if(BIZ_CHANNEl_JXYD_VRBT.equals(subscribe.getChannel())) {
                            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                        }
                        jiangxiYidongService.noPageWrite(subscribe.getMobile(), subscribe.getChannel(), subscribe.getIspOrderNo(), subscribe.getJcmccChannel(), "N", jiangxiYidongOrderResult.getData().getResultMsg().getOrderId());
                    } else {
                        upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                        upd.setResult(jiangxiYidongOrderResult.getData() != null && jiangxiYidongOrderResult.getData().getResultMsg() != null ? jiangxiYidongOrderResult.getData().getResultMsg().getMessage() : jiangxiYidongOrderResult.getMsg());
                    }
                }else{
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                    upd.setResult("提交验证码失败");
                }
                this.updateSubscribeDbAndEs(upd);
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                return Result.ok("短信验证码提交成功");
            }
            //贵州移动业务
            if (StringUtils.startsWith(subscribe.getBizType(),"GZYD_")) {
                if(!SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus()) && !StringUtils.contains(subscribe.getResult(),"验证码") ) {
                    return Result.captchaErr("请勿重复提交");
                }
                GuizhouMobileResult.Result result = guizhouYidongService.smsCode(
                        subscribe.getMobile(), smsCode, UUID.randomUUID().toString().replace("-", ""), subscribe.getIspOrderNo(), subscribe.getChannel(), subscribe.getSource(), subscribe.getDeviceInfo());
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setResult(result.getResultMsg());
                upd.setOpenTime(new Date());
                upd.setStatus(result.isOK() ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
                if(result.isOK() && BIZ_TYPE_GZYD_VRBT_XSZS.equals(subscribe.getChannel())) {
                    rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                }
                this.updateSubscribeDbAndEs(upd);
                rabbitMQMsgSender.sendOutsideCallbackMessage(new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知"));
                return Result.ok("短信验证码提交成功");
            }
            if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(subscribe.getIspOrderNo(), smsCode, MobileRegionResult.ISP_YIDONG.equals(
                    subscribe.getIsp()) ? subscribe.getChannel() : BIZ_DIANXIN_CHANNEL_MAIHE, subscribe.getMobile());
            return Result.ok("短信验证码提交成功");
        }
    }

    @Override
    public Result<?> receiveDianxinOrderCrack(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
//        //查询所有渠道是否可以开通该手机号省份
        List<String> satisfyList = DIANXIN_CHANNEL_LIST.stream().filter(dianxinChannel -> dianxinVrbtProperties.getDianxinVrbtConfig(dianxinChannel).getProvinceList().contains(subscribe.getProvince())).collect(Collectors.toList());
        if (satisfyList.size() > 0) {
            String dxChannel = satisfyList.get(new Random().nextInt(satisfyList.size()));
            subscribe.setVrbtDxChannel(dxChannel);
            subscribe.setServiceId(dxChannel);
        } else {
            return Result.error("暂未开放,敬请期待!");
        }

//        Optional<String> optional = DIANXIN_CHANNEL_LIST.stream().filter(dianxinChannel -> dianxinVrbtProperties.getDianxinVrbtConfig(dianxinChannel).getProvinceList().contains(subscribe.getProvince())).findFirst();
//        if (optional.isPresent()) {
//            subscribe.setVrbtDxChannel(optional.get());
//            subscribe.setServiceId(optional.get());
//        } else {
//            return Result.error("暂未开放,敬请期待!");
//        }
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, target.getServiceId(), subscribe.getMobile());
            return Result.ok("提交验证码成功");
        }
        String mobile = subscribe.getMobile();
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            BillingResult billingResult = yidongVrbtCrackService.getSms(mobile, subscribe.getVrbtDxChannel());
            if (billingResult.isOK()) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setIspOrderNo(billingResult.getTransId());
                this.updateSubscribeDbAndEs(upd);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("系统繁忙,请稍后再试!");
            }
        }
    }

    @Override
    public Result<?> receiveDianxinMaiheOrderCrack(Subscribe subscribe) {
        //暂时不破解
        //if (StringUtils.isBlank(subscribe.getVrbtDxChannel())) {
        //    subscribe.setVrbtDxChannel(BIZ_DIANXIN_CHANNEL_MAIHE);
        //}
        //subscribe.setServiceId(subscribe.getVrbtDxChannel());
        //if (!dianxinVrbtProperties.getDianxinVrbtConfig(subscribe.getVrbtDxChannel()).getProvinceList().contains(subscribe.getProvince())) {
        //    return Result.error("暂未开放,敬请期待!");
        //}
        //String mobile = subscribe.getMobile();
        //final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile, subscribe.getVrbtDxChannel());
        //if (packageExist) {
        //    return Result.error("已有包月");
        //}
        //final DianxinResp dianxinResp = dianxinVrbtService.confirmOpenOrderLaunchedEx(mobile, "1", "0", subscribe.getVrbtDxChannel());
        //if (!dianxinResp.isOK()) {
        //    return Result.error("电信视频彩铃包月业务订购失败:" + dianxinResp.getResMessage());
        //}
        //if (!dianxinResp.getFeeUrl().contains(bizProperties.getSubscribeDianxinVrbtOrderPage())) {
        //    log.error("电信视频彩铃计费地址变更");
        //}
        //String orderPage = bizProperties.getSubscribeDianxinVrbtOrderReportPage() + "?od=" + dianxinResp.getOrderNo();
        //subscribe.setIspOrderNo(dianxinResp.getOrderNo());
        //this.createSubscribeDbAndEs(subscribe);
        //return Result.ok("redirect telecom vrbt page", orderPage);
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //查询所有渠道是否可以开通该手机号省份
        if (StringUtils.isEmpty(subscribe.getVrbtDxChannel())) {
            subscribe.setVrbtDxChannel(BIZ_DIANXIN_CHANNEL_MAIHE);
        }
        subscribe.setServiceId(BIZ_DIANXIN_CHANNEL_MAIHE);
        if (!dianxinVrbtProperties.getDianxinVrbtConfig(subscribe.getVrbtDxChannel()).getProvinceList().contains(subscribe.getProvince())) {
            return Result.error("暂未开放,敬请期待!");
        }
        final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile, subscribe.getVrbtDxChannel());
        if (packageExist) {
            return Result.error("已有包月");
        }
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            //yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getVrbtDxChannel(), subscribe.getMobile());
            //切换到吴炯破解
            WujiongCrackResult wujiongCrackResult = wujiongCrackService.smsCode(subscribe.getMobile(), target.getIspOrderNo(), smsCode, subscribe.getChannel());
            if (!wujiongCrackResult.isDxVrbtSubOk()) {
                //只修改状态 已提交短信验证码 的数据
                if (SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(target.getStatus())) {
                    Subscribe upd = new Subscribe();
                    upd.setId(target.getId());
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                    upd.setResult(wujiongCrackResult.getMsg());
                    upd.setModifyTime(new Date());
                    this.updateSubscribeDbAndEs(upd);
                }
                return Result.error("订阅失败");
            }
            return Result.redirect("提交验证码成功", dianxinVrbtProperties.getDianxinVrbtConfig(subscribe.getVrbtDxChannel()).getReturnUrl());
        }
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            //BillingResult billingResult = yidongVrbtCrackService.getSms(mobile, subscribe.getVrbtDxChannel());
            //if (billingResult.isOK()) {
            //    Subscribe upd = new Subscribe();
            //    upd.setId(subscribe.getId());
            //    upd.setIspOrderNo(billingResult.getTransId());
            //    this.updateSubscribeDbAndEs(upd);
            //    return Result.noauth("验证码已发送", subscribe.getId());
            //} else {
            //    return Result.error("系统繁忙,请稍后再试!");
            //}
            //切换到吴炯破解  {"msg":"SUCCESS","sn":"","tradeid":"d373ccbb534b481494c87e4cab4a8305","rescode":"0"}
            final WujiongCrackResult wujiongCrackResult = wujiongCrackService.getSms(mobile,subscribe.getChannel());
            if(wujiongCrackResult.isDxVrbtGetOk()) {
                // subscribeService.lambdaUpdate().eq(Subscribe::getId,id).set(Subscribe::getIspOrderNo,billingResult.getTransId()).update();
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setIspOrderNo(wujiongCrackResult.getTradeid());
                this.updateSubscribeDbAndEs(upd);
                return Result.noauth("验证码已发送", subscribe.getId());
            }else {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(wujiongCrackResult.getMsg());
                upd.setIspOrderNo(wujiongCrackResult.getTradeid());
                this.updateSubscribeDbAndEs(upd);
                return Result.error("系统繁忙,请稍后再试!");
            }

        }
    }

    @Override
    public Result<?> receiveLiantongOrderCrack(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.LIANTONG_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            liantongVrbtCrackService.smsCode(target.getPushRespMessage(), smsCode);
            return Result.ok("提交验证码成功");
        }
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            final String id = subscribe.getId();
            //查询是否已开通了包月
            final boolean isSubedMon = liantongVrbtService.isSubedMon(mobile, subscribe.getVrbtLtChannel());
            //如果有包月
            if (isSubedMon) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);//3表示已有包月
                upd.setResult("已有包月");
                //如果还需要订购铃音,就单独单独订购铃音
                if (StringUtils.isNotEmpty(subscribe.getLtRingId())) {
                    LiantongResp liantongResp = liantongVrbtService.settingRingOnePointMon(mobile, subscribe.getLtRingId(), subscribe.getVrbtLtChannel());
                    upd.setExtra(liantongResp.getDescription());
                }
                this.updateSubscribeDbAndEs(upd);
                return Result.bizExists("你已开通,请勿重复开通");
            }
            final LiantongResp liantongResp = liantongVrbtService.onePointProductMon(mobile, null, null, subscribe.getVrbtLtChannel());
            // 联通视频彩铃包月业务订购失败,手机号:13187174721,resCode:1033,resMessage:预校验未通过,规则组:全局规则,规则集:全局(基本规则),规则:号码状态(停机)
            if (!liantongResp.isOK()) {
                log.warn("联通视频彩铃包月业务订购失败,手机号:{},resCode:{},resMessage:{}", mobile, liantongResp.getReturnCode(), liantongResp.getDescription());
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(liantongResp.getReturnCode() + "@" + liantongResp.getDescription());
                this.updateSubscribeDbAndEs(upd);
                return Result.error("系统繁忙,请稍后再试!");
            }
            String payUrl = liantongResp.getUrl();
            LiantongCrackResult liantongCrackResult = liantongVrbtCrackService.getSms(mobile, liantongResp.getOrderId(), URLEncoder.encode(payUrl));
            if (liantongCrackResult.isGetOK()) {
                Subscribe upd = new Subscribe();
                upd.setId(id);
                upd.setIspOrderNo(liantongResp.getOrderId());
                upd.setPushRespMessage(liantongCrackResult.getSId());
                this.updateSubscribeDbAndEs(upd);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("系统繁忙,请稍后再试!");
            }
        }
    }

    private Result<?> subscribeServiceTest(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        TestAccount testAccount = subscribeServiceTestProperties.getTestAccountMap().get(mobile);
        if (StringUtils.isEmpty(smsCode)) {
            return Result.noauth("验证码已发送");
        } else {

            if (StringUtils.equals(testAccount.getSmsCode(), smsCode)) {
                //测试账号订购成功
                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                subscribe.setOpenTime(new Date());
                subscribe.setResult("测试账号订购成功=>{\"resCode\":\"" + smsCode + "\",\"resMsg\":\"订购成功\"}");
            } else {
                //测试账号订购订购失败
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribe.setResult("测试账号订购订购失败=>{\"resCode\":\"500\",\"resMsg\":\"订购失败\"}");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
            Result<?> result = StringUtils.equals(testAccount.getSmsCode(), smsCode) ? Result.ok("订购成功") : Result.error("订购失败");
            return result;
        }
    }

    /**
     * 根据渠道号和省份设置自增序列
     *
     * @param channel
     * @param province
     */
    @Override
    public void incrChannelProvinceLimit(String channel, String province) {
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + province + ":" + DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            redisUtil.incr(key, 1);
        }
    }

    /**
     * 根据渠道号和省份设置自增序列
     *
     * @param channel
     * @param province
     */
    @Override
    public void incrChannelProvinceOwnerLimit(String channel, String province, String owner) {
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + province + ":" + owner + ":" + DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            redisUtil.incr(key, 1);
        }
    }

    @Override
    public void incrChannelLimit(String channel) {
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            redisUtil.incr(key, 1);
        }
    }

    @Override
    public void incrChannelOwnerLimit(String channel, String owner) {
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + owner + ":" + DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            redisUtil.incr(key, 1);
        }
    }


    /**
     * 根据渠道号和省份获取自增序列值
     *
     * @param channel
     * @param province
     */
    @Override
    public Integer getIncrChannelProvinceLimit(String channel, String province) {
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + province + ":" + DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            return 0;
        } else {
            return (Integer) redisUtil.get(key);
        }
    }

    /**
     * 根据渠道号和省份获取自增序列值
     *
     * @param channel
     * @param province
     */
    @Override
    public Integer getIncrChannelProvinceOwnerLimit(String channel, String province, String owner){
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + province + ":" + owner + ":" +DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            return 0;
        } else {
            return (Integer) redisUtil.get(key);
        }
    }

    @Override
    public Integer getIncrChannelLimit(String channel) {
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            return 0;
        } else {
            return (Integer) redisUtil.get(key);
        }
    }

    @Override
    public Integer getIncrChannelOwnerLimit(String channel, String owner) {
        String key = CacheConstant.BIZ_SERIAL_REDIS_KEY + ":" + channel + ":" + owner + ":" + DateUtils.date_sdf.get().format(new Date());
        if (!redisUtil.hasKey(key)) {
            return 0;
        } else {
            return (Integer) redisUtil.get(key);
        }
    }

    /**
     * 业务订购记录列表
     *
     * @param firstDay
     * @param endDay
     * @param channel
     * @param serviceId
     * @param isp
     * @return
     */
    @Override
    public List<BusinessOrderLog> businessOrderLogList(String firstDay, String endDay, String channel, String serviceId, String isp) {
        BusinessOrderLog businessOrderLog = new BusinessOrderLog();
        businessOrderLog.setCreateTimeFrom(firstDay);
        businessOrderLog.setCreateTimeTo(endDay);
        businessOrderLog.setChannel(channel);
        businessOrderLog.setServiceId(serviceId);
        businessOrderLog.setIsp(isp);
        return this.baseMapper.getBusinessOrderLogList(businessOrderLog);
    }


    /**
     * 业务订购记录列表
     *
     * @param firstDay
     * @param endDay
     * @param channel
     * @param mobile
     * @param isp
     * @return
     */
    @Override
    public Integer businessOrderLogCount(String firstDay, String endDay, String channel, String mobile, String isp) {
        BusinessOrderLog businessOrderLog = new BusinessOrderLog();
        businessOrderLog.setCreateTimeFrom(firstDay);
        businessOrderLog.setCreateTimeTo(endDay);
        businessOrderLog.setChannel(channel);
        businessOrderLog.setMobile(mobile);
        businessOrderLog.setIsp(isp);
        return this.baseMapper.getBusinessOrderLogCount(businessOrderLog);
    }

    @Override
    public Result<?> receiveGuiZhouMobileOrder(Subscribe subscribe) {
        //如果有这个key，就关闭业务
        if (redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_GZYD) && BIZ_TYPE_GZYD_VRBT_XSZS.equals(subscribe.getChannel())) {
            return Result.error("暂未开放");
        }
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        String id = IdWorker.getIdStr();
        if (StringUtils.isEmpty(subscribe.getTransactionId())) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "GUIZHOU");
            }
            try {
                GuizhouMobileCodeResult guizhouMobileCodeResult = guizhouYidongService.getSms(mobile,
                        UUID.randomUUID().toString().replace("-", ""),
                        subscribe.getChannel(), "https://crbt.cdyrjygs.com",id);
                if (guizhouMobileCodeResult != null) {
                    if(StringUtils.isNotEmpty(guizhouMobileCodeResult.getSyncUrl())){
                        subscribe.setId(id);
                        subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
                        subscribe.setResult("跳转移动页面");
                        subscribe.setIspOrderNo(guizhouMobileCodeResult.getSerialNumber());
                        subscribe.setExtra(guizhouMobileCodeResult.getSerialNumber());
                        this.createSubscribeDbAndEs(subscribe);
                        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                        return Result.redirect("跳转移动页面", guizhouMobileCodeResult.getSyncUrl());
                    }else{
                        subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
                        subscribe.setResult("获取验证码成功");
                        subscribe.setIspOrderNo(guizhouMobileCodeResult.getSerialNumber());
                        this.createSubscribeDbAndEs(subscribe);
                        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                        return Result.noauth("验证码已发送", subscribe.getId());
                    }
                } else {
                    return Result.error(500, "获取验证码失败", "GUIZHOU");
                }
            } catch (Exception e) {
                return Result.error(500, "获取验证码失败", "GUIZHOU");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            if(StringUtils.isNotEmpty(smsCode)){
                //验证短信验证码是否合法
                if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                    return Result.error(500, "验证码错误", "GUIZHOU");
                }
                //避免相同的错误短信验证码反复提交
                String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
                if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                    return Result.error(500, "请勿重复提交", "GUIZHOU");
                }
                redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            }else{
                //避免相同的错误短信验证码反复提交
                String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + transactionId;
                if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                    return Result.error(500, "请勿重复提交", "GUIZHOU");
                }
                redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            GuizhouMobileResult.Result result = guizhouYidongService.smsCode(mobile, smsCode, UUID.randomUUID().toString().replace("-", ""), target.getIspOrderNo(), subscribe.getChannel(), subscribe.getSource(), subscribe.getDeviceInfo());
            if (result != null && result.isOK()) {
                //订阅成功
                target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                target.setOpenTime(new Date());
                target.setResult("订阅成功");
                this.updateSubscribeDbAndEs(target);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(target);
                rabbitMQMsgSender.addDelayedVerifyMessage(target);
                return Result.ok("订阅成功", "GUIZHOU");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setOpenTime(new Date());
                target.setResult(result.getResultMsg());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, result.getResultMsg(), "GUIZHOU");
            }
        }
    }

    public Result<?> receiveYunnanMobileOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        String id = IdWorker.getIdStr();
        if (StringUtils.isEmpty(subscribe.getTransactionId())) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "YUNNAN");
            }
            try {
                String tradeEparchyCode = yunnanYidongService.getTradeEparchyCode(mobile, subscribe.getChannel(),subscribe.getCity());
                YunnanMobileQrymatchAuthSceneResult qrymatchAuthSceneResult = yunnanYidongService.qrymatchAuthScene(mobile, subscribe.getChannel(), tradeEparchyCode);
                if(!(qrymatchAuthSceneResult.isOK() && qrymatchAuthSceneResult.getResult() !=null && qrymatchAuthSceneResult.getResult().isOK())){
                    return Result.error("暂无订购资格，请稍后再试!");
                }
                YunnanMobileSendAuthSmsCodeResult sendAuthSmsCodeResult = yunnanYidongService.sendAuthSmsCode(mobile, subscribe.getChannel(), tradeEparchyCode, qrymatchAuthSceneResult);
                if (sendAuthSmsCodeResult.isOK() && sendAuthSmsCodeResult.getResult().isOK()){
                    String authSmsCodeSendFlag = sendAuthSmsCodeResult.getResult().getDatas().get(0).get("AUTH_SMS_CODE_SEND_FLAG").toString();
                    subscribe.setIspOrderNo(authSmsCodeSendFlag);
                    subscribe.setResult("获取验证码成功");
                    this.createSubscribeDbAndEs(subscribe);
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.ok("验证码已发送", subscribe.getId());
                } else {
                    return Result.error(500, "获取验证码失败", "YUNNAN");
                }
            } catch (Exception e) {
                return Result.error(500, "获取验证码失败", "YUNNAN");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            if(StringUtils.isNotEmpty(smsCode)){
                //验证短信验证码是否合法
                if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                    return Result.error(500, "验证码错误", "YUNNAN");
                }
                //避免相同的错误短信验证码反复提交
                String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
                if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                    return Result.error(500, "请勿重复提交", "YUNNAN");
                }
                redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            }else{
                //避免相同的错误短信验证码反复提交
                String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + transactionId;
                if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                    return Result.error(500, "请勿重复提交", "YUNNAN");
                }
                redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            String tradeEparchyCode = yunnanYidongService.getTradeEparchyCode(mobile, subscribe.getChannel(),subscribe.getCity());

            YunnanMobileAuthenticationResult authenticationResult = yunnanYidongService.authentication(mobile, subscribe.getChannel(), smsCode, tradeEparchyCode, target.getIspOrderNo());
            if(!(authenticationResult.isOK() && authenticationResult.getResult() != null && authenticationResult.isOK())){
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(authenticationResult.getRespDesc());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, "订阅失败", "YUNNAN");
            }
            YunnanMobileCheckSaleActiveResult saleActiveIntfOrderResult = yunnanYidongService.saleActiveIntfOrder(mobile, subscribe.getChannel(),subscribe.getCity(),authenticationResult.getResult().getAuthInstanceToken());
            if (saleActiveIntfOrderResult.isOK()
                    && saleActiveIntfOrderResult.getResult() != null
                    && StringUtils.isNotBlank(saleActiveIntfOrderResult.getResult().getOrderId())) {
                //订阅成功
                target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                target.setOpenTime(new Date());
                target.setResult("订阅成功");
                this.updateSubscribeDbAndEs(target);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(target);
                rabbitMQMsgSender.addDelayedVerifyMessage(target);
                return Result.ok("订阅成功", "YUNNAN");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setOpenTime(new Date());
                target.setResult(saleActiveIntfOrderResult.getRespDesc());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, "订阅失败", "YUNNAN");
            }
        }
    }


    @Override
    public Result<?> receiveGuiZhouGaoJieOrder(Subscribe subscribe) {
        //如果有这个key，就关闭业务
//        if (redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_GZYD) && BIZ_TYPE_GZYD_VRBT_XSZS.equals(subscribe.getChannel())) {
////            return Result.error("暂未开放");
////        }
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "GUIZHOU");
            }
            try {
                GuizhouMobileCodeResult guizhouMobileCodeResult = guizhouYidongGaoJieService.getSms(mobile,
                        UUID.randomUUID().toString().replace("-", ""),
                        subscribe.getChannel(), subscribe.getSource());
                if (guizhouMobileCodeResult != null) {
                    subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
                    subscribe.setResult("获取验证码成功");
                    subscribe.setIspOrderNo(guizhouMobileCodeResult.getSerialNumber());
                    this.createSubscribeDbAndEs(subscribe);
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送", subscribe.getId());
                } else {
                    return Result.error(500, "获取验证码失败", "GUIZHOU");
                }
            } catch (Exception e) {
                return Result.error(500, "获取验证码失败", "GUIZHOU");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error(500, "验证码错误", "GUIZHOU");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error(500, "请求参数错误", "GUIZHOU");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error(500, "请勿重复提交", "GUIZHOU");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);

            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            GuizhouMobileResult.Result result = guizhouYidongGaoJieService.smsCode(mobile, smsCode, UUID.randomUUID().toString().replace("-", ""), target.getIspOrderNo(), subscribe.getChannel(), subscribe.getSource());
            if (result != null && result.isOK()) {
                //订阅成功
                target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                target.setOpenTime(new Date());
                target.setResult("订阅成功");
                this.updateSubscribeDbAndEs(target);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(target);
                rabbitMQMsgSender.addDelayedVerifyMessage(target);
                return Result.ok("订阅成功", "GUIZHOU");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setOpenTime(new Date());
                target.setResult(result.getResultMsg());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, result.getResultMsg(), "GUIZHOU");
            }
        }
    }

    /**
     * 湖南电信视频彩铃(铃音盒)和智能接听(小秘书)破界收单公用
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> receiveHunanDianxinCommonCrackOrder(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                this.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
            return Result.ok("提交验证码成功");
        }
        String mobile = subscribe.getMobile();
        //根据手机号来同步,避免重复获取短信验证码
        synchronized (interner.intern(mobile)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            BillingResult billingResult = yidongVrbtCrackService.getSms(mobile, subscribe.getChannel());
            if (billingResult.isOK()) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setIspOrderNo(billingResult.getTransId());
                this.updateSubscribeDbAndEs(upd);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("系统繁忙,请稍后再试!");
            }
        }
    }

    /**
     * 湖南电信铃音盒通知处理
     *
     * @param mobile
     * @param orderNo
     * @param state
     * @param orderTime
     * @param result
     */
    @Override
    public void receiveHunanDianxinVrbtNotify(String mobile, String orderNo, String state, Date orderTime, String result) {
        Subscribe subscribe = dianxinHunanFindByMobile(mobile, BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
//        Subscribe subscribe = dianxinFindByMobile(mobile);
        Integer status = "0".equals(state) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL;
        if (subscribe == null) {
            log.error("湖南豫讯不存在该手机号的订购通知记录,手机号:{}", mobile);
            return;
        }
        //跳过重复收到的通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("湖南豫讯订购异步通知-跳过重复通知");
            return;
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult(result);
        upd.setOpenTime(orderTime);
        upd.setModifyTime(new Date());
        this.updateSubscribeDbAndEs(upd);
        channelService.AdEffectFeedbackNew(subscribe, status);
    }

    /**
     * 湖南电信智能接听(小秘书)通知处理
     *
     * @param mobile
     * @param orderNo
     * @param state
     * @param orderTime
     */
    @Override
    public void receiveHunanDianxinMindNotify(String mobile, String orderNo, Integer state, Date orderTime) {
        Subscribe subscribe = dianxinHunanFindByMobile(mobile, BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_MIND_13);
//        Subscribe subscribe = dianxinFindByMobile(mobile);
        Integer status = SUBSCRIBE_STATUS_SUCCESS.equals(state) ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL;
        String result = SUBSCRIBE_STATUS_SUCCESS.equals(state) ? "订购成功" : "订购失败";
        if (subscribe == null) {
            log.error("湖南豫讯智能版不存在该手机号的订购通知记录,手机号:{}", mobile);
            return;
        }
        //跳过重复收到的通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("湖南豫讯智能版订购异步通知-跳过重复通知");
            return;
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult(result);
        upd.setOpenTime(orderTime);
        upd.setModifyTime(new Date());
        this.updateSubscribeDbAndEs(upd);
        channelService.AdEffectFeedbackNew(subscribe, status);
    }

    @Override
    public Result<?> receiveGuangdongOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                GuangdongYidongYueyueResult guangdongYidongYueyueResult = guangdongYidongYueyueService.getSms(mobile, UUIDGenerator.generate());
                if (guangdongYidongYueyueResult.isCodeOk()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    redisUtil.set(CacheConstant.CMS_CACHE_GUANGDONG_SMS_DATA + mobile, guangdongYidongYueyueResult, 3600L);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            //验证短信验证码是否合法(只有4位数)
            if (!smsCode.matches(Regexp.DIANXIN_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                redisUtil.get(CacheConstant.CMS_CACHE_GUANGDONG_SMS_DATA + mobile);
                GuangdongYidongYueyueResult guangdongYidongYueyueResult = guangdongYidongYueyueService.smsCode(mobile, smsCode);
                if (guangdongYidongYueyueResult.isSubOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(guangdongYidongYueyueResult.getMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(guangdongYidongYueyueResult.getMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(guangdongYidongYueyueResult.getMsg());
                }
            } catch (Exception e) {
                return Result.error("提交验证码失败");
            }
        }
    }

    @Override
    public Result<?> receiveHainanOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                HainanMobileResult hainanMobileResult = hainanYidongVrbtService.getSms(mobile);
                if (hainanMobileResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            HainanMobileResult hainanMobileResult = hainanYidongVrbtService.smsCode(mobile, smsCode);
            if (hainanMobileResult.isOK()) {
                //订阅成功
                subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                subscribe.setOpenTime(new Date());
                subscribe.setResult(hainanMobileResult.getMessage());
                this.createSubscribeDbAndEs(subscribe);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                return Result.ok("订阅成功");
            } else {
                //订阅成功
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribe.setOpenTime(new Date());
                subscribe.setResult(hainanMobileResult.getMessage());
                this.createSubscribeDbAndEs(subscribe);
                return Result.error(hainanMobileResult.getMessage());
            }
        }
    }

    @Override
    public Result<?> receiveAnhuiOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                AnhuiResult anhuiResult = anhuiYidongService.getSms(mobile);
                if (anhuiResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    redisUtil.set(CacheConstant.CMS_CACHE_ANHUI_SMS_DATA + mobile, anhuiResult, 3600L);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                AnhuiResult anhuiResult = anhuiYidongService.smsCode(mobile, smsCode);
                if (anhuiResult.isOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(anhuiResult.getResultMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(anhuiResult.getResultMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error(anhuiResult.getResultMsg());
                }
            } catch (Exception e) {
                log.error("系统错误:{}", e);
                return Result.error("系统错误，请稍后再试");
            }
        }

    }

    @Override
    public Result<?> receiveShandongDianxinOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                ShandongDianxinResult shandongDianxinResult = shandongDianxinService.getSms(mobile, subscribe.getIp());
                if (shandongDianxinResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                ShandongDianxinResult shandongDianxinResult = shandongDianxinService.smsCode(mobile, smsCode, subscribe.getIp());
                if (shandongDianxinResult.isOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult("开通成功");
                    this.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult("开通失败");
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error("开通失败");
                }
            } catch (Exception e) {
                log.error("系统错误:{}", e);
                return Result.error("系统错误，请稍后再试");
            }
        }
    }


    /**
     * 东莞移动流量包开通
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> dongguanMobileLLBOpenUp(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            //发送验证码东莞移动流量包
            Result<?> result = dgydFlowPackOrderService.sendSms(mobile, DGYD_LLB_KDX);
            subscribe.setIspOrderNo(result.getResult().toString());
            subscribe.setResult(result.getMessage());
            if (result.isOK()) {
                subscribe.setStatus(SUBSCRIBE_STATUS_PUSHED);
                this.createSubscribeDbAndEs(subscribe);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                this.createSubscribeDbAndEs(subscribe);
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dgydSmsInvalidKey = DGYD_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dgydSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dgydSmsInvalidKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);


            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //开通东莞移动流量包
            Result<?> result = dgydFlowPackOrderService.submitOrder(mobile, BizConstant.DGYD_LLB_KDX, smsCode, DGYD_LLB_ORDER_TYPE_SUB, target.getIspOrderNo());
            Subscribe upd = new Subscribe();
            upd.setId(transactionId);
            upd.setModifyTime(new Date());
            upd.setResult(result.getMessage());
            if (result.isOK()) {
                if ("已开通".equals(result.getMessage())) {
                    upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
                    this.updateSubscribeDbAndEs(upd);
                } else {
                    upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    this.updateSubscribeDbAndEs(upd);
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
            } else {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                this.updateSubscribeDbAndEs(upd);
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
            }
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return result;
        }
    }



    @SmsDailyLimit
    @Override
    public Result<?> vrReceiveOrder(Subscribe subscribe) {
        final String miguChannel = subscribe.getChannel();
        String mobile = subscribe.getMobile();
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            return Result.error("请正确填写手机号");
        }

        String subChannel = subscribe.getSubChannel();
        subChannel = StringUtils.isEmpty(subChannel) ? BizConstant.SUB_CHANNEL_DEFAULT : subChannel;
        final String bizType = getBizTypeByMiguChannel(miguChannel);
        subscribe.setBizType(bizType);

        //测试账号
        TestAccount testAccount = subscribeServiceTestProperties.getTestAccountMap().get(mobile);
        if (testAccount != null) {
            return subscribeServiceTest(subscribe);
        }
        if (blackListService.isBlackList(mobile)) {
            return Result.msgBlackLimit();
        }
        IMiGuKuaiYouVRJingMengService businessCommonService = SpringContextUtils.getBean(MiGuKuaiYouVRJingMengServiceImpl.class);
        return businessCommonService.vrJingMengReadySub(subscribe);
    }
    @Override
    public void wujiongCrackNotify(String tel,String orderid,String code,String msg) throws Exception {
        if (tel == null || !tel.matches(Regexp.MOBILE_REG)) {
            throw new Exception("手机号错误");
        }
        if(StringUtils.isEmpty(orderid)){
            throw new Exception("订单号错误");
        }
        Subscribe subscribe=this.findByMobileAndIspOrderId(tel,orderid);
        if(subscribe==null){
            throw new Exception("渠道订单不存在");
        }
        String result="VR竞盟开通结果==>{\"resCode\":\""+code+"\",\"resMsg\":\""+msg+"\"}";
        if("200000".equals(code)){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(result);
            this.updateSubscribeDbAndEs(upd);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }else{
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            //添加包月状态延迟校验消息到队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
//            //写入开通成功序列
//            this.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//                this.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//            } else {
//                String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//                if (StringUtils.isNotBlank(owner)) {
//                    this.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//                }
//            }
            //写入自增序列
            this.saveChannelLimit(subscribe);
        }else{
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(result);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            this.updateSubscribeDbAndEs(upd);
        }
    }

    @Override
    public void hetuCrackNotify(String tel, String orderid, String code, String msg) throws Exception {
        if (tel == null || !tel.matches(Regexp.MOBILE_REG)) {
            throw new Exception("手机号错误");
        }
        if (StringUtils.isEmpty(orderid)) {
            throw new Exception("订单号错误");
        }
        Subscribe subscribe = this.findByMobileAndIspOrderId(tel, orderid);
        if (subscribe == null) {
            throw new Exception("渠道订单不存在");
        }
        String result = "开通结果==>{\"resCode\":\"" + code + "\",\"resMsg\":\"" + msg + "\"}";
        if ("200000".equals(code) && !StringUtils.equalsAny(subscribe.getBizType(),BIZ_TYPE_HETU)) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(result);
            this.updateSubscribeDbAndEs(upd);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }
            //添加包月状态延迟校验消息到队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //写入自增序列
            this.saveChannelLimit(subscribe);
//            //写入开通成功序列
//            this.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//                this.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//            } else {
//                String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//                if (StringUtils.isNotBlank(owner)) {
//                    this.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//                }
//            }
        } else {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(result);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            this.updateSubscribeDbAndEs(upd);
        }
        if(outsideConfigService.isOutsideChannel(subscribe.getSubChannel()) && !StringUtils.equalsAny(subscribe.getBizType(),BIZ_TYPE_HETU)) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }
    }

//    @Override
//    public void wujiongBjhyCrackNotify(String tel, String orderid) throws Exception {


//        Subscribe subscribe = this.findByMobileAndIspOrderId(tel, orderid);
//        if (subscribe == null) {
//            log.error("orderid:{}不存在");
//            throw new Exception("渠道订单不存在");
//        }
//        String result = "开通成功";
//        Subscribe upd = new Subscribe();
//        upd.setId(subscribe.getId());
//        upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
//        upd.setResult(result);
//        this.updateSubscribeDbAndEs(upd);
//        String channelCode = subscribe.getChannel();
//        //白金会员畅听版+视频彩铃(组合包) 订购铃音
//        if ((BizConstant.isBjhyZhbChannel(channelCode)) && MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) {
//            //为开通之后要订视频彩铃的做彩铃开通
//            final String channelCopyrightId = SpringContextUtils.getBean(IMusicService.class).getChannelCopyrightId(channelCode, subscribe.getCopyrightId());
//            log.info("收到订购开通包月成功通知后订之后的视频彩铃进行延时订购=>订单号:{},手机号:{}:channelCopyrightId:{}", subscribe.getId(), tel, channelCopyrightId);
//            rabbitMQMsgSender.sendSubscribeVrbtOrderDelay10secondMessage(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_10S_VRBT_ORDER).id(subscribe.getId()).msg("延迟10秒视频彩铃订购").extra(channelCopyrightId).build());
//            //如果用户选择了要订购的视频彩铃就直接订购,否则按比例随机订购一首默认彩铃
//            //if (StringUtils.isNotEmpty(channelCopyrightId)) {
//            //    orderVrbtService.vrbtToneFreeOrderDelay(subscribe, channelCopyrightId, "1");
//            //} else {
//            //    orderVrbtService.orderByRandomDelay(subscribe);
//            //}
//        }
//
//        if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
//        } else {
//            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
//            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
//        }
//        //添加包月状态延迟校验消息到队列
//        rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
//    }


    /**
     * 网易云MM
     *
     * @param subscribe
     * @return
     */
    @Override
    @ValidationLimit
    public Result<?> receiveWangyiyunMmOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            try{
                String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
                if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
                    return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
                }
                //发送验证码网易云MM
                String appOrderId = "hr" + IdWorker.getIdStr();
                WangyiyunMmCreateOrderResult order = wangyiyunMmService.createOrder(mobile, subscribe.getIp(),appOrderId);
                if (order.isOK()) {
                    //保存订单信息
                    WyyMmOrder wyyMmOrder = new WyyMmOrder();
                    wyyMmOrder.setMobile(mobile);
                    wyyMmOrder.setChannel(subscribe.getChannel());
                    wyyMmOrder.setOrderId(order.getRes().getOrderId());
                    wyyMmOrder.setAppOrderId(appOrderId);
                    wyyMmOrder.setStatus(0);
                    wyyMmOrder.setCreateTime(new Date());
                    wyyMmOrderService.save(wyyMmOrder);

                    subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
                    subscribe.setIspOrderNo(order.getRes().getOrderId());
                    subscribe.setResult("获取验证码成功");
                    this.createSubscribeDbAndEs(subscribe);
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    return Result.noauth("验证码已发送", subscribe.getId());
                } else {
                    subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
                    subscribe.setResult(order.getMsg());
                    this.createSubscribeDbAndEs(subscribe);
                    return Result.error("获取验证码失败");
                }
            }catch (Exception e){
                return Result.error("获取验证码失败");
            }
        } else {
            try{
                final String transactionId = subscribe.getTransactionId();
                if (Strings.isEmpty(transactionId)) {
                    return Result.captchaErr("请求参数错误");
                }
                //避免相同的错误短信验证码反复提交
                String dgydSmsInvalidKey = DGYD_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
                if (redisUtil.hasKey(dgydSmsInvalidKey)) {
                    return Result.error("请勿重复提交");
                }
                redisUtil.set(dgydSmsInvalidKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);


                final Subscribe target = getById(transactionId);
                if (Objects.isNull(target)) {
                    return Result.captchaErr("请求参数错误");
                }

                //开通网易云MM
                WangyiyunMmSmsCodeResult wangyiyunMmSmsCodeResult = wangyiyunMmService.smsCode(mobile, smsCode, target.getIspOrderNo());
                if (wangyiyunMmSmsCodeResult.isOK()) {
                    target.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                    target.setResult(wangyiyunMmSmsCodeResult.getRes().getMsg());
                    this.updateSubscribeDbAndEs(target);
                    return Result.ok("已提交短信验证码");
                } else {
                    target.setStatus(SUBSCRIBE_STATUS_FAIL);
                    target.setResult(wangyiyunMmSmsCodeResult.getMsg());
                    this.updateSubscribeDbAndEs(target);
                    channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_FAIL);
                    return Result.error("开通失败");
                }
            }catch (Exception e){
                log.error("系统错误:{}", e);
                return Result.error("系统错误，请稍后再试");
            }
        }
    }



    @Override
    public void junBoLiuLiangBaoCrackNotify(String sysOrderId,String orderStatus,String orderStatusMsg,String contactNumber) throws Exception {
        if (contactNumber == null || !contactNumber.matches(Regexp.MOBILE_REG)) {
            throw new Exception("手机号错误");
        }
        if (StringUtils.isEmpty(sysOrderId)) {
            throw new Exception("订单号错误");
        }
        Subscribe subscribe=this.findByMobileAndIspOrderId(contactNumber,sysOrderId);
        if(subscribe==null){
            throw new Exception("渠道订单不存在");
        }
        String result="骏伯流量包开通结果==>{\"resCode\":\""+orderStatus+"\",\"resMsg\":\""+orderStatusMsg+"\"}";
        if("7".equals(orderStatus)){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(result);
            upd.setOpenTime(new Date());
            this.updateSubscribeDbAndEs(upd);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }else{
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            //添加包月状态延迟校验消息到队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //写入自增序列
            this.saveChannelLimit(subscribe);
        }else{
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            if("14".equals(orderStatus)){
                upd.setExtra("退订订单");
            }else{
                upd.setResult(result);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            }
            this.updateSubscribeDbAndEs(upd);
        }

    }

    @Override
    @ValidationLimit
    public Result<?> receiveHuNanMobileOrder(Subscribe subscribe) {
        //如果有这个key，就关闭业务
        if ((redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HNYD) && (BIZ_TYPE_HN_VRBT_DY_LLB.equals(subscribe.getChannel())))
                ||(redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HNYD_QWZJ) && (BIZ_TYPE_HN_QWZJ_LLB.equals(subscribe.getChannel())))
                ||(redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_HNYD_MGB) && (BIZ_TYPE_HN_DGDD_MGB.equals(subscribe.getChannel())))) {
            return Result.error("暂未开放");
        }
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            //根据手机号来同步,避免重复获取短信验证码
            synchronized (interner.intern(mobile)) {
                String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
                if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                    return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "GUIZHOU");
                }
                try {
                    //调用检验接口校验用户开通资格
                    HunanYidongResult productCheckResult = hunanYidongService.productCheck(mobile,subscribe.getChannel());
                    if(!(productCheckResult.isOK()
                            && productCheckResult.getResult() != null
                            && productCheckResult.getResult().size() > 0
                            && "0".equals(productCheckResult.getResult().get(0).getResultCode()))){
                        return Result.error("暂无订购资格，请稍后再试!");
                    }
                    HunanYidongResult getSmsResult = hunanYidongService.getSms(mobile, subscribe.getChannel());
                    if (getSmsResult.isOK()
                            && getSmsResult.getResult() != null
                            && getSmsResult.getResult().size() > 0
                            && "0".equals(getSmsResult.getResult().get(0).getResultCode())) {
                        subscribe.setResult("获取验证码成功");
                        this.createSubscribeDbAndEs(subscribe);
                        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                        return Result.noauth("验证码已发送", subscribe.getId());
                    } else {
                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                        subscribe.setResult(getSmsResult.getRespDesc());
                        this.createSubscribeDbAndEs(subscribe);
                        return Result.error("获取验证码失败");
                    }
                } catch (Exception e) {
                    return Result.error(500, "获取验证码失败", "HUNAN");
                }
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error(500, "验证码错误", "HUNAN");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error(500, "请求参数错误", "HUNAN");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error(500, "请勿重复提交", "HUNAN");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);

            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            HunanYidongResult smsCodeResult = hunanYidongService.smsCode(mobile, smsCode, subscribe.getChannel());
            if (smsCodeResult.isOK()
                    && smsCodeResult.getResult() != null
                    && smsCodeResult.getResult().size() > 0
                    && (StringUtils.isNotBlank(smsCodeResult.getResult().get(0).getTradeId())
                    || "0".equals(smsCodeResult.getResult().get(0).getResultCode()))) {
                //订阅成功
                target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                target.setOpenTime(new Date());
                target.setResult("订阅成功");
                this.updateSubscribeDbAndEs(target);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(target);
                rabbitMQMsgSender.addDelayedVerifyMessage(target);
                return Result.ok("订阅成功", "HUNAN");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(smsCodeResult.getResult() == null ? "订阅失败" : smsCodeResult.getResult().get(0).getResultInfo());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, smsCodeResult.getResult() == null ? "订阅失败" : smsCodeResult.getResult().get(0).getResultInfo(), "HUNAN");
            }
        }
    }

    @Override
    public Result<?> receiveHenanMobileOrder(Subscribe subscribe) {
        HenanMobileSmsResult getSmsResult = null;
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            //根据手机号来同步,避免重复获取短信验证码
            synchronized (interner.intern(mobile)) {
                String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
                if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                    return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "GUIZHOU");
                }
                try {
                    //调用检验接口校验用户开通资格
                    HenanMobileMarketResult henanMobileMarketResult = henanYidongService.queryMarket(mobile, subscribe.getChannel());
                    if(!(henanMobileMarketResult.isOK())){
                        return Result.error("暂无订购资格，请稍后再试!");
                    }
                    if(BIZ_TYPE_HENYD_YR_MGB.equals(subscribe.getChannel())){
                        HenanMobileCheckResult henanMobileCheckResult = henanYidongService.queryLlbActive(mobile,subscribe.getChannel());
                        if(!(henanMobileCheckResult.isOK())){
                            return Result.error("暂无订购资格，请稍后再试!");
                        }
                        getSmsResult = henanYidongService.sendLlbSms(mobile, subscribe.getChannel());
                    }else{
                        HenanMobileCheckResult henanMobileCheckResult = henanYidongService.queryActive(mobile,subscribe.getChannel());
                        if(!(henanMobileCheckResult.isOK())){
                            return Result.error("暂无订购资格，请稍后再试!");
                        }
                        getSmsResult = henanYidongService.sendSms(mobile, subscribe.getChannel());
                    }
                    if (getSmsResult.isOK()) {
                        subscribe.setResult("获取验证码成功");
                        subscribe.setIspOrderNo(getSmsResult.getData().getOrderId());
                        this.createSubscribeDbAndEs(subscribe);
                        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                        return Result.noauth("验证码已发送", subscribe.getId());
                    } else {
                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                        subscribe.setResult(getSmsResult.getMessage());
                        this.createSubscribeDbAndEs(subscribe);
                        return Result.error("获取验证码失败");
                    }
                } catch (Exception e) {
                    return Result.error(500, "获取验证码失败", "HENAN");
                }
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error(500, "验证码错误", "HENAN");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error(500, "请求参数错误", "HENAN");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error(500, "请勿重复提交", "HENAN");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);

            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            HenanMobileSmsResult smsCodeResult = henanYidongService.smsCode(mobile,target.getIspOrderNo(), smsCode, subscribe.getChannel(),target.getReferer());
            if (smsCodeResult.isOK()) {
                //订阅成功
                target.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                target.setResult("短信验证码提交成功");
                this.updateSubscribeDbAndEs(target);
                rabbitMQMsgSender.sendSubscribeResultJudgeDelay20minuteMessage(DelayedMessage.builder().id(target.getId()).msg("10分钟延迟开通结果判定消息").build());
                return Result.ok("短信验证码提交成功", "HENAN");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(smsCodeResult.getMessage());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, "订阅失败", "HENAN");
            }
        }
    }

    @Override
    public Result<?> receiveShanxiMobileOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            //根据手机号来同步,避免重复获取短信验证码
            synchronized (interner.intern(mobile)) {
                String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
                if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                    return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "GUIZHOU");
                }
                try {
                    KuaimaShanxiResult kuaimaShanxiResult = kuaimaShanxiService.getSms(mobile, subscribe.getChannel());
                    if (kuaimaShanxiResult.isOK()) {
                        subscribe.setResult("获取验证码成功");
                        subscribe.setIspOrderNo(kuaimaShanxiResult.getBizContentResult().getOrderId());
                        this.createSubscribeDbAndEs(subscribe);
                        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                        return Result.noauth("验证码已发送", subscribe.getId());
                    } else {
                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                        subscribe.setResult(kuaimaShanxiResult.getMessage());
                        this.createSubscribeDbAndEs(subscribe);
                        return Result.error("获取验证码失败");
                    }
                } catch (Exception e) {
                    return Result.error(500, "获取验证码失败", "SHANXI");
                }
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error(500, "验证码错误", "SHANXI");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error(500, "请求参数错误", "SHANXI");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error(500, "请勿重复提交", "HENAN");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);

            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            KuaimaShanxiResult kuaimaShanxiResult = kuaimaShanxiService.smsCode(mobile, smsCode, target.getIspOrderNo());
            if (kuaimaShanxiResult.isOK()
                    && kuaimaShanxiResult.getBizContentResult() != null
                    && "Y".equals(kuaimaShanxiResult.getBizContentResult().getPassFlag())) {
                //订阅成功
                target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                target.setOpenTime(new Date());
                target.setResult("订阅成功");
                this.updateSubscribeDbAndEs(target);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                return Result.ok("订阅成功", "SHANXI");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(kuaimaShanxiResult.getMessage());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, "订阅失败", "SHANXI");
            }
        }
    }

    @Override
    public Result<?> receiveShandongHexiaoyuanOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            //根据手机号来同步,避免重复获取短信验证码
            synchronized (interner.intern(mobile)) {
                String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
                if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                    return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "GUIZHOU");
                }
                try {
                    ShandongHexiaoyuanResult shandongHexiaoyuanResult = shandongHexiaoyuanService.getImgVerifyCodeAndSms(mobile);
                    if (shandongHexiaoyuanResult.isOK()) {
                        subscribe.setResult("获取验证码成功");
                        this.createSubscribeDbAndEs(subscribe);
                        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                        return Result.noauth("验证码已发送", subscribe.getId());
                    } else {
                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                        subscribe.setResult(shandongHexiaoyuanResult.getMsg());
                        this.createSubscribeDbAndEs(subscribe);
                        return Result.error("获取验证码失败");
                    }
                } catch (Exception e) {
                    return Result.error(500, "获取验证码失败", "SHANDONG");
                }
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error(500, "验证码错误", "SHANDONG");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error(500, "请求参数错误", "SHANDONG");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error(500, "请勿重复提交", "SHANDONG");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);

            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            ShandongHexiaoyuanResult shandongHexiaoyuanResult = shandongHexiaoyuanService.smsCode(mobile, smsCode);
            if (shandongHexiaoyuanResult.isOK()) {
                target.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                target.setResult("短信验证码提交成功");
                this.updateSubscribeDbAndEs(target);
                return Result.ok("短信验证码提交成功", "SHANDONG");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(shandongHexiaoyuanResult.getMsg());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, "订阅失败", "SHANDONG");
            }
        }
    }


    @Override
    public Result<?> receiveHubeiMobileOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            //根据手机号来同步,避免重复获取短信验证码
            synchronized (interner.intern(mobile)) {
                String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
                if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                    return Result.error(500, "操作频繁,请" + SICHUAN_MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试", "GUIZHOU");
                }
                try {
                    //验证码参数获取
                    HubeiMobileGetSmsParamResult getSmsParamResult = hubeiYidongService.getSmsParam(mobile, subscribe.getChannel(),subscribe.getIp(),subscribe.getUserAgent());
                    if (getSmsParamResult.isOK()) {
                        subscribe.setResult("验证码参数获取成功");
                        this.createSubscribeDbAndEs(subscribe);
                        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                        subscribe.setRemark(mapper.writeValueAsString(getSmsParamResult.getArgs()));
                        return Result.ok("验证码参数获取成功", subscribe);
                    } else {
                        subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                        subscribe.setResult(getSmsParamResult.getRetmsg());
                        this.createSubscribeDbAndEs(subscribe);
                        return Result.error("验证码参数获取失败");
                    }
                } catch (Exception e) {
                    return Result.error(500, "验证码参数获取失败", "HUBEI");
                }
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error(500, "验证码错误", "HUBEI");
            }
            //验证短信验证码是否合法
            if (StringUtils.isEmpty(transactionId)) {
                return Result.error(500, "请求参数错误", "HUBEI");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error(500, "请勿重复提交", "HUBEI");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);

            final Subscribe target = getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //校验验证码
            HubeiMobileVerifySmsResult verifySmsResult = hubeiYidongService.verifySms(mobile,
                    subscribe.getChannel(),
                    subscribe.getRemark(),
                    smsCode);
            if(!verifySmsResult.isOK()){
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(StringUtils.isBlank(verifySmsResult.getRetmsg()) ? "订阅失败" : verifySmsResult.getRetmsg());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, StringUtils.isBlank(verifySmsResult.getRetmsg()) ? "订阅失败" : verifySmsResult.getRetmsg(), "HUBEI");
            }
            //业务校验
            HubeiMobileBusicheckResult busicheckResult = hubeiYidongService.busiCheck(mobile,
                    subscribe.getChannel(),
                    verifySmsResult.getSessionId(),
                    verifySmsResult.getUsessionId());
            if(!busicheckResult.isOK()){
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(StringUtils.isBlank(busicheckResult.getRetmsg()) ? "订阅失败" : busicheckResult.getRetmsg());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, StringUtils.isBlank(busicheckResult.getRetmsg()) ? "订阅失败" : busicheckResult.getRetmsg(), "HUBEI");
            }
            //业务办理
            HubeiMobileSmsCodeResult smsCodeResult = hubeiYidongService.smsCode(mobile,
                    subscribe.getChannel(),
                    busicheckResult.getEordersn());
            if (smsCodeResult.isOK()
                    && "99".equals(smsCodeResult.getStatus())) {
                //订阅成功
                target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                target.setOpenTime(new Date());
                target.setResult("订阅成功");
                this.updateSubscribeDbAndEs(target);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(target);
                rabbitMQMsgSender.addDelayedVerifyMessage(target);
                return Result.ok("订阅成功", "HUBEI");
            } else {
                //订阅失败
                target.setStatus(SUBSCRIBE_STATUS_FAIL);
                target.setResult(StringUtils.isBlank(smsCodeResult.getRetmsg()) ? "订阅失败" : smsCodeResult.getRetmsg());
                this.updateSubscribeDbAndEs(target);
                return Result.error(500, StringUtils.isBlank(smsCodeResult.getRetmsg()) ? "订阅失败" : smsCodeResult.getRetmsg(), "HUBEI");
            }
        }
    }

    @Override
    public void removeSubscribeDbAndEs(String id) {
        boolean flag = this.removeById(id);
        if (flag) {
            esDataService.removeSubscribeById(id);
        }
    }

    @Override
    public void saveChannelLimit(Subscribe subscribe) {
        //写入自增序列
        this.incrChannelLimit(subscribe.getChannel());
        this.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
            incrChannelOwnerLimit(subscribe.getChannel(), CHANNEL_OWNER_CPA);
        } else {
            String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
            if (StringUtils.isNotBlank(owner)) {
                incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
                incrChannelOwnerLimit(subscribe.getChannel(), owner);
            }
        }
    }
    @Override
    public FebsResponse gzdxDchOpenReceive(Subscribe subscribe) {
        FebsResponse febsResponse = new FebsResponse();
        String mobile = subscribe.getMobile();
        Date openTime = subscribe.getOpenTime();
        Integer status = subscribe.getStatus();
        if (StringUtils.isBlank(mobile) || !mobile.matches(Regexp.MOBILE_REG)) {
            return febsResponse.fail().message("无效的手机号");
        }
        if(!(status == 1 || status == 0)){
            return febsResponse.fail().message("无效的开通状态");
        }
        if (openTime == null) {
            subscribe.setOpenTime(new Date());
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            String isp = mobileRegionResult.getOperator();
            subscribe.setIsp(isp);
        }
        if(status == 1){
            subscribe.setResult("订阅成功");
        }else{
            subscribe.setResult("订阅失败");
        }
        subscribe.setChannel("GZDXDCH");
        subscribe.setBizType("GZDXDCH");
        this.createSubscribeDbAndEs(subscribe);
        return febsResponse.success().message("成功");
    }

    @Override
    public void report(Subscribe subscribe, Integer status) {
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        } else {
            channelService.AdEffectFeedbackNew(getById(subscribe.getId()), status);
        }
    }

    @Override
    public boolean checkBizRepeat(String mobile, String channelCode) {
        if (blackListService.isRedList(mobile)) {
            return false;
        }
        List<EsSubscribe> esSubscribeList = esDataService.findLast3MonthEsSubscribeByMobile(mobile, channelCode);
        if (esSubscribeList.size() > 0) {
            return true;
        }
        return false;
    }



    @Override
    public void youranCrackNotify(String orderNo,String status,String msg) throws Exception {
        if (status == null) {
            throw new Exception("状态错误");
        }
        if (StringUtils.isEmpty(orderNo)) {
            throw new Exception("订单号错误");
        }
        Subscribe subscribe=this.findByIspOrderId(orderNo);
        if(subscribe==null){
            throw new Exception("渠道订单不存在");
        }
        String result="悠然开通结果==>{\"resCode\":\""+status+"\",\"resMsg\":\""+msg+"\"}";
        if("1".equals(status) && !isMiguNotifySuccessBizType(subscribe.getBizType())){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(result);
            upd.setOpenTime(new Date());
            this.updateSubscribeDbAndEs(upd);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }else{
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            //添加包月状态延迟校验消息到队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //写入自增序列
            this.saveChannelLimit(subscribe);
        }else{
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setResult(result);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            this.updateSubscribeDbAndEs(upd);
        }

    }


    @Override
    public YiZunCpaNotify yizunNotify(String sourceTradeId,String tradeStatus) {
        if (StringUtils.isBlank(tradeStatus)) {
            return YiZunCpaNotify.fail("状态错误");
        }
        if (StringUtils.isEmpty(sourceTradeId)) {
            return YiZunCpaNotify.fail("订单号错误");
        }
        Subscribe subscribe=this.lambdaQuery().eq(Subscribe::getId, sourceTradeId).last(BizConstant.SQL_LIMIT_ONE).one();
        if(subscribe==null){
            return YiZunCpaNotify.fail("订单不存在");
        }
        String result="易尊开通结果==>{\"tradeStatus\":\""+tradeStatus+"\",\"tradeStatus\":\""+tradeStatus+"\"}";
        if ("17".equals(tradeStatus) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus()) &&!isMiguNotifySuccessBizType(subscribe.getBizType())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(result);
            upd.setOpenTime(new Date());
            this.updateSubscribeDbAndEs(upd);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }else{
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            //写入自增序列
            this.saveChannelLimit(subscribe);
        }else{
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            if("15".equals(tradeStatus)){
                upd.setExtra("退订订单");
                upd.setVerifyStatusDaily(0);
            }else if("18".equals(tradeStatus)){
                upd.setResult(result);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }
            this.updateSubscribeDbAndEs(upd);
        }
        return YiZunCpaNotify.ok();
    }

    @Override
    public void updateSubscribeAndEs(Subscribe subscribe) {
        Date date=new Date();
        boolean flag=this.lambdaUpdate().eq(Subscribe::getId,subscribe.getId()).set(Subscribe::getStatus,subscribe.getStatus()).set(Subscribe::getResult,subscribe.getResult()).set(Subscribe::getOpenTime,date).set(Subscribe::getModifyTime,date).update();
        log.info("数据更新日志-subscribe:{},flag:{}",subscribe,flag);
        if (flag) {
            subscribe.setOpenTime(date);
            subscribe.setModifyTime(date);
            esDataService.updateSubscribeEs(subscribe);
        }
    }
}
