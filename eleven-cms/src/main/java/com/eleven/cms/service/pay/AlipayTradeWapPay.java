package com.eleven.cms.service.pay;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import com.eleven.cms.config.AliPayConfig;
import com.eleven.cms.config.AliPayRefundProperties;
import com.eleven.cms.entity.Alipay;
import com.eleven.cms.service.IAlipayConfigService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AlipayTradeWapPay {


    @Resource
    AliPayConfig aliPayConfig;

    private AlipayClient alipayClient;
    @Resource
    private AliPayRefundProperties aliPayRefundProperties;

    @Resource
    private IAlipayConfigService alipayConfigService;

    private Alipay appPayConfig(String businessType) {
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType, businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if (alipay == null) {
            throw new JeecgBootException("支付参数错误");
        }
        AlipayConfig alipayConfig = new AlipayConfig();
        //设置网关地址
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        //设置应用APPID
        alipayConfig.setAppId(alipay.getAppId());
        //设置应用私钥
        alipayConfig.setPrivateKey(alipay.getPrivateKey());
        //设置应用公钥证书路径
        String certPath = aliPayRefundProperties.getCertPath().replace("appId", alipay.getAppId());
        alipayConfig.setAppCertPath(certPath);
        //设置支付宝公钥证书路径
        String alipayPublicCertPath = aliPayRefundProperties.getAlipayPublicCertPath().replace("appId", alipay.getAppId());
        alipayConfig.setAlipayPublicCertPath(alipayPublicCertPath);
        //设置支付宝根证书路径
        String rootCertPath = aliPayRefundProperties.getRootCertPath().replace("appId", alipay.getAppId());
        alipayConfig.setRootCertPath(rootCertPath);
        //设置请求格式，固定值json
        alipayConfig.setFormat("json");
        //设置字符集
        alipayConfig.setCharset("UTF8");
        //设置签名类型
        alipayConfig.setSignType("RSA2");
        //构造client
        try {
            this.alipayClient = new DefaultAlipayClient(alipayConfig);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return alipay;
    }


    public AlipaySystemOauthTokenResponse getToken(String code, String businessType) throws AlipayApiException {

        appPayConfig(businessType);
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        // 设置授权码
        request.setCode(code);

        // 设置授权方式
        request.setGrantType("authorization_code");
        return alipayClient.certificateExecute(request);
    }

    /**
     * @param outTradeNo  out_trade_no	商户订单号，需保证在商家系统中唯一。
     * @param totalAmount 订单金额。
     * @param subject     商品的标题/交易标题/订单标题/订单关键字等
     * @param body   订单描述
     * @throws AlipayApiException 阿里支付异常
     */
    public AlipayTradeWapPayResponse wapPay(String outTradeNo, String totalAmount, String subject, String body, String businessType) throws AlipayApiException {
        Alipay alipay = this.appPayConfig(businessType);
        AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode bizContentNode = mapper.createObjectNode();
        bizContentNode.put("out_trade_no", outTradeNo);  // 商户订单号，商户网站订单系统中唯一订单号，必填
        bizContentNode.put("total_amount", totalAmount);   // 付款金额，必填
        bizContentNode.put("subject", subject);  // 订单名称，必填
        bizContentNode.put("body", body);  // 订单描述
        bizContentNode.put("product_code", "QUICK_WAP_PAY");
        bizContentNode.put("app_pay", "Y");
        String bizContent = bizContentNode.toString();
        alipayRequest.setBizContent(bizContent);
        alipayRequest.setReturnUrl(alipay.getCallbackUrl()); //设置通知地址
        alipayRequest.setNotifyUrl(alipay.getPayNotifyUrl());//设置回跳
        return pay(alipayRequest);
    }

    public AlipayTradeWapPayResponse pay(AlipayTradeWapPayRequest alipayRequest) throws AlipayApiException {
        log.info("支付入参:{}", JSONObject.toJSONString(JSONObject.toJSON(alipayRequest)));
        AlipayTradeWapPayResponse alipayTradeWapPayResponse = alipayClient.pageExecute(alipayRequest, "POST");
        log.info("支付返回结果:{}", JSONObject.toJSONString(alipayTradeWapPayResponse));
        return alipayTradeWapPayResponse;
    }

    public AlipayTradeQueryResponse queryPayResult(String orderNo,String tradeNo) throws AlipayApiException {
        log.info("查询支付入参:{}", JSONObject.toJSONString(JSONObject.toJSON(orderNo)));
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();
        model.setOutTradeNo(orderNo);
        model.setTradeNo(tradeNo);
        request.setBizModel(model);
        AlipayTradeQueryResponse response = alipayClient.execute(request);
        log.info("查询支付结果:{}", JSONObject.toJSONString(JSONObject.toJSON(response)));
        return response;
    }

    //全部退款
    public AlipayTradeRefundResponse allRefund(String orderNo,String refundAmount,String refundReason) throws AlipayApiException {
        // 构造请求参数以调用接口
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel model = new AlipayTradeRefundModel();

        // 设置商户订单号
        model.setOutTradeNo(orderNo);

        // 设置退款金额
        model.setRefundAmount(refundAmount);

        // 设置退款原因说明
        model.setRefundReason(refundReason);

        request.setBizModel(model);

        AlipayTradeRefundResponse response = alipayClient.execute(request);
        return response;

    }
}