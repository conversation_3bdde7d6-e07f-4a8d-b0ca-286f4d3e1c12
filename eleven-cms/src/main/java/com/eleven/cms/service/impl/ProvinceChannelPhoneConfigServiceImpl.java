package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eleven.cms.entity.ProvinceBusinessChannelConfig;
import com.eleven.cms.entity.ProvinceBusinessChannelInfo;
import com.eleven.cms.entity.ProvinceChannelPhoneConfig;
import com.eleven.cms.entity.ProvinceChannelPhoneInfo;
import com.eleven.cms.mapper.ProvinceChannelPhoneConfigMapper;
import com.eleven.cms.service.IProvinceChannelPhoneConfigService;
import com.eleven.cms.service.IProvinceChannelPhoneInfoService;
import com.eleven.cms.service.IProvinceTreeService;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: cms_province_channel_phone_config
 * @Author: jeecg-boot
 * @Date:   2023-02-06
 * @Version: V1.0
 */
@Service
public class ProvinceChannelPhoneConfigServiceImpl extends ServiceImpl<ProvinceChannelPhoneConfigMapper, ProvinceChannelPhoneConfig> implements IProvinceChannelPhoneConfigService {

    @Autowired
    private IProvinceChannelPhoneInfoService provinceChannelPhoneInfoService;
    @Autowired
    IProvinceTreeService provinceTreeService;
    @Override
    public List<ProvinceChannelPhoneInfo> selectByMainId(String id) {
        return provinceChannelPhoneInfoService.lambdaQuery().eq(ProvinceChannelPhoneInfo::getProvinceChannelConfigId,id).list();
    }

    @Override
    @Transactional
    public void updateMain(ProvinceChannelPhoneConfig provinceChannelPhoneConfig, List<ProvinceChannelPhoneInfo> provinceChannelPhoneInfoList) {
        this.updateById(provinceChannelPhoneConfig);

        QueryWrapper<ProvinceChannelPhoneInfo> queryWrapper = new QueryWrapper<>();
        //1.先删除子表数据
        provinceChannelPhoneInfoService.remove(queryWrapper.lambda().eq(ProvinceChannelPhoneInfo::getProvinceChannelConfigId,provinceChannelPhoneConfig.getId()));
        //2.子表数据重新插入
        if(provinceChannelPhoneInfoList!=null && provinceChannelPhoneInfoList.size()>0) {
            for (ProvinceChannelPhoneInfo entity : provinceChannelPhoneInfoList) {
                //外键设置
                entity.setProvinceChannelConfigId(provinceChannelPhoneConfig.getId());
                provinceChannelPhoneInfoService.save(entity);
            }
        }
    }

    @Override
    public void saveMain(ProvinceChannelPhoneConfig provinceChannelPhoneConfig, List<ProvinceChannelPhoneInfo> provinceChannelPhoneInfoList) {
        this.save(provinceChannelPhoneConfig);
        provinceTreeService.list().forEach(province -> {
            ProvinceChannelPhoneInfo provinceChannelPhoneInfo = new ProvinceChannelPhoneInfo();
            provinceChannelPhoneInfo.setEnable(Boolean.TRUE);
            provinceChannelPhoneInfo.setProvinceChannelConfigId(provinceChannelPhoneConfig.getId());
            provinceChannelPhoneInfo.setProvince(province.getProvinceName());
            provinceChannelPhoneInfoService.save(provinceChannelPhoneInfo);
        });
    }

    @Override
    public boolean phoneValidate(String channelCode, String mobile) {
        ProvinceChannelPhoneConfig provinceChannelPhoneConfig = this.lambdaQuery()
                .eq(ProvinceChannelPhoneConfig::getChannel, channelCode)
                .one();
        if(provinceChannelPhoneConfig != null){
            String phoneConfig = provinceChannelPhoneConfig.getPhoneConfig();
            if(StringUtils.isNotBlank(phoneConfig)){
                for (String s : phoneConfig.split(",")) {
                    if(mobile.startsWith(s)){
                        return false;
                    }
                }
            }
        }
        return true;
    }

    @Override
    public boolean provinceValidate(String channelCode, String province) {
        ProvinceChannelPhoneConfig provinceChannelPhoneConfig = this.lambdaQuery()
                .eq(ProvinceChannelPhoneConfig::getChannel, channelCode)
                .one();
        if(provinceChannelPhoneConfig != null){
            List<ProvinceChannelPhoneInfo> list = provinceChannelPhoneInfoService.lambdaQuery()
                    .eq(ProvinceChannelPhoneInfo::getProvinceChannelConfigId, provinceChannelPhoneConfig.getId())
                    .list();
            for (ProvinceChannelPhoneInfo provinceChannelPhoneInfo : list) {
                if(provinceChannelPhoneInfo.getProvince().equals(province) && !provinceChannelPhoneInfo.isEnable()){
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    @Transactional
    public void deleteById(String id) {
        this.removeById(id);
        //删除子表数据
        QueryWrapper<ProvinceChannelPhoneInfo> queryWrapper = new QueryWrapper<>();
        provinceChannelPhoneInfoService.remove(queryWrapper.lambda().eq(ProvinceChannelPhoneInfo::getProvinceChannelConfigId,id));

    }

    @Override
    public void deleteByIds(List<String> ids) {
        for (String id : ids) {
            this.deleteById(id);
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_PROVINCE_CHANNEL_PHONE_ALLOW, key =  "#root.methodName + ':' + #p0 + ':' + #p1", condition = "#p0!=null", unless = "#result==null")
    public boolean allow(String channelCode, String mobile) {
        ProvinceChannelPhoneConfig provinceChannelPhoneConfig = this.lambdaQuery()
                .eq(ProvinceChannelPhoneConfig::getChannel, channelCode)
                .one();
        if(provinceChannelPhoneConfig != null){
            String phoneConfig = provinceChannelPhoneConfig.getPhoneConfig();
            if(StringUtils.isNotBlank(phoneConfig)){
                for (String s : phoneConfig.split(",")) {
                    if(mobile.startsWith(s)){
                        return false;
                    }
                }
            }
        }
        return true;
    }
}
