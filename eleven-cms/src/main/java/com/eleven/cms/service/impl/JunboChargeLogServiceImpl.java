package com.eleven.cms.service.impl;

import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.MiGuHuYuQueryMemberProperties;
import com.eleven.cms.config.OtherRecharge;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.*;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.mapper.JunboChargeLogMapper;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RightBusinessEnum;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.google.common.collect.Lists;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @Description: 俊博直充记录
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Slf4j
@Service
@DS("master")
public class JunboChargeLogServiceImpl extends ServiceImpl<JunboChargeLogMapper, JunboChargeLog> implements IJunboChargeLogService {
    public static final String DELIMITER_AMP = "&";
    //思维方阵
    public static final String SWFZ_NAME = "swfz";
    //大鱼消除
    public static final String DYXC_NAME = "dyxc";
    //厨房大逃亡
    public static final String CFDTW_NAME = "cfdtw";
    //我自为道
    public static final String WZWD_NAME = "wzwd";
    //短剧
    public static final String DUANJU_NAME = "duanju";
    //水晶传说
    public static final String SJCS_NAME = "sjcs";
    //暗黑主宰
    public static final String AHZZ_NAME = "ahzz";
    //券码使用状态
    //已使用
    public static final String USE_SUCCESS= "1";
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private IMemberRightsService memberRightsService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private JunboApiProperties junboApiProperties;
    @Autowired
    private JunboApiService junboApiService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private ITelecomOrderService telecomOrderService;
    @Autowired
    private ICouponCodeService couponCodeService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IRightsPackService rightsPackService;
    @Autowired
    private RechargeAlertService rechargeAlertService;
    @Autowired
    private IMobileFeeChargeLogService mobileFeeChargeLogService;
    @Autowired
    private KugouApiService kugouApiService;
    @Autowired
    private IKugouOrderService kugouOrderService;
    @Autowired
    private UnifyRightsFeignClient unifyRightsFeignClient;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    @Lazy
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private IMiguPackService miguPackService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private IHetuCouponCodeService hetuCouponCodeService;

    @Autowired
    private ISwfzCouponCodeService swfzCouponCodeService;
    @Autowired
    private IDyxcCouponCodeService dyxcCouponCodeService;
    @Autowired
    private ICfdtwCouponCodeService cfdtwCouponCodeService;
    @Autowired
    private IWzwdCouponCodeService wzwdCouponCodeService;
    @Autowired
    private ISjcsCouponCodeService sjscCouponCodeService;
    @Autowired
    private IAhzzCouponCodeService ahzzCouponCodeService;
    @Autowired
    private MiGuHuYuQueryMemberProperties miGuHuYuQueryMemberProperties;
    @Autowired
    private IWxpayService wxpayService;
    @Autowired
    private IWechatCouponConfigService wechatCouponConfigService;
    @Autowired
    private IGameSendCouponCodeService sendCodeService;
    @Autowired
    private IEsDataService esDataService;


    @Autowired
    private IGameConfigService gameConfigService;
    private static final Interner<String> huyuOrderIdInterner = Interners.newWeakInterner();
//
//    private static final List<RightsDto> rightsDtoList = Lists.newArrayList(
//            new RightsDto("CFDTW010300","厨房大逃亡礼包1"),
//            new RightsDto("CFDTW020500","厨房大逃亡礼包2"),
//            new RightsDto("CFDTW031000","厨房大逃亡礼包3"),
//            new RightsDto("CFDTW042000","厨房大逃亡礼包4"),
//            new RightsDto("CFDTW051500","厨房大逃亡礼包5"),
//            new RightsDto("CFDTW062000","厨房大逃亡礼包6"),
//            new RightsDto("CFDTW073000","厨房大逃亡礼包7"),
//            new RightsDto("MIGUHUYU100001","河图寻仙记手游礼包1"),
//            new RightsDto("MIGUHUYU100002","河图寻仙记手游礼包2"), //分省包
//            new RightsDto("MIGUHUYU100003","河图寻仙记手游礼包3"),
//            new RightsDto("MIGUHUYU100004","河图寻仙记手游礼包4"),
//            new RightsDto("MIGUHUYU100005","河图寻仙记手游礼包5"), //全网包
//            new RightsDto("MIGUHUYU100006","河图寻仙记手游礼包6"), //全网包
//            new RightsDto("DYXC_0300","大鱼消除礼包1"),
//            new RightsDto("DYXC_0500","大鱼消除礼包2"),
//            new RightsDto("DYXC_1000","大鱼消除礼包3"),
//            new RightsDto("DYXC_3000","大鱼消除礼包4"),
//            new RightsDto("DYXC_1500","大鱼消除礼包5"),
//            new RightsDto("DYXC_2000","大鱼消除礼包6"),
//            new RightsDto("DYXC_3001","大鱼消除礼包7"),
//            new RightsDto("SWFZ100001","思维方阵礼包1"),
//            new RightsDto("SWFZ100002","思维方阵礼包2"),
//            new RightsDto("SWFZ100003","思维方阵礼包3"),
//            new RightsDto("SWFZ100004","思维方阵礼包4"), //全网包
//            new RightsDto("100010000042","水晶传说专享钻石礼包"),
//            new RightsDto("100010000043","水晶传说超级水晶礼包"),
//            new RightsDto("100010000044","水晶传说超值升星礼包"),
//            new RightsDto("100010000045","水晶传说命定特权礼包"),
//            new RightsDto("100010000046","水晶传说高级尊享礼包"),
//            new RightsDto("100010000324","爱看鲲鹏优享包"),
//            new RightsDto("100010000325","爱看鲲鹏精品包"),
//            new RightsDto("100010000326","爱看鲲鹏尊享包"),
//            new RightsDto("100010000920","暗黑主宰梦幻神秘礼包"),
//            new RightsDto("100010000921","暗黑主宰璀璨宝石礼包"),
//            new RightsDto("100010000922","暗黑主宰奇迹加速礼包"),
//            new RightsDto("100010000923","暗黑主宰梦想转生礼包"),
//            new RightsDto("100010000924","暗黑主宰黑暗进阶礼包")
//    );

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    @Override
    public List<JunboChargeLog> findByAccountCurrentDay(String mobile) {
        final LocalDateTime firstday = LocalDate.now().atTime(LocalTime.MIN);
        final LocalDateTime lastDay = LocalDate.now().atTime(LocalTime.MAX);
        return  this.lambdaQuery().eq(JunboChargeLog::getMobile,mobile)
                .between(JunboChargeLog::getCreateTime, firstday,lastDay).list();
    }

    /**
     * 创建立即充值的记录
     * @param msisdn
     * @param serviceId
     * @param rights
     * @param packName
     * @return
     */
    @Override
    public JunboChargeLog createImmediateRechargeLog(String msisdn,String account, String serviceId, Rights rights,String packName) {
        return this.buildJunboChargeLog(msisdn,account,serviceId,rights,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,null,null,packName);
    }

    /**
     * 创建预约充值的记录
     * @param msisdn
     * @param serviceId
     * @param rights
     * @param scheduledTime
     * @param packName
     * @return
     */
    @Override
    public JunboChargeLog createScheduleRechargeLog(String msisdn,String account, String serviceId, Rights rights, Date scheduledTime, String packName) {
        return this.buildJunboChargeLog(msisdn,account,serviceId,rights,BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED,scheduledTime,null,packName);
    }

    /**
     * 创建手动充值的记录
     * @param msisdn
     * @param serviceId
     * @param rights
     * @param packName
     * @return
     */
    @Override
    public JunboChargeLog createManualRechargeLog(String msisdn,String account, String serviceId, Rights rights,String packName) {
        return this.buildJunboChargeLog(msisdn,account,serviceId,rights,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING, null,BizConstant.JUNBO_MANUAL_RECHARGE_REMARK,packName);
    }

    @Override
    public JunboChargeLog buildJunboChargeLog(String msisdn,String account, String serviceId, Rights rights, Integer status, Date scheduledTime, String remark,String packName) {
        JunboChargeLog junboChargeLog = new JunboChargeLog();
        junboChargeLog.setMobile(msisdn);
        if(rights.getIsAccount()!=null && rights.getIsAccount().equals(1)){
            junboChargeLog.setAccount(account);
        }else{
            junboChargeLog.setAccount(msisdn);
        }
        //生成uuid作为我方订单号
        String orderSEQ = IdWorker.get32UUID();
        junboChargeLog.setMiguOrderId(orderSEQ);
        junboChargeLog.setStatus(status);
        junboChargeLog.setScheduledTime(scheduledTime);
        junboChargeLog.setCouponId(rights.getCouponId());
        junboChargeLog.setCouponName(rights.getRightsName());
        junboChargeLog.setCouponPrice(rights.getProductPrice());
        if(StringUtils.isBlank(rights.getRightsMonth())){
            String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
            junboChargeLog.setRightsMonth(rightsMonth);
        }else{
            junboChargeLog.setRightsMonth(rights.getRightsMonth());
        }
        junboChargeLog.setCreateTime(new Date());
        junboChargeLog.setServiceId(serviceId);
        junboChargeLog.setRemark(remark);
        junboChargeLog.setPackName(packName);
        junboChargeLog.setRechargeState(rights.getRechargeState());
        junboChargeLog.setPayTime(rights.getPayTime());
        junboChargeLog.setCompanyOwner(rights.getCompanyOwner());
        junboChargeLog.setRechargeSource(rights.getRechargeSource());
        junboChargeLog.setChannel(rights.getChannel());
        junboChargeLog.setSubChannel(rights.getSubChannel());
        MobileRegionResult mobileRegionResult = mobileRegionService.query(msisdn);
        if(mobileRegionResult!=null) {
            junboChargeLog.setProvince(mobileRegionResult.getProvince());
            junboChargeLog.setCity(mobileRegionResult.getCity());
        }
        junboChargeLog.setCouponCode(rights.getCouponCode());
        this.save(junboChargeLog);
        updateAlipayRightsStatus(orderSEQ,status);
        return junboChargeLog;
    }

    @Override
    public List<JunboChargeLog> selectMain(String mobile,String rightsMonth,String sourse) {
        JunboChargeLog junboChargeLog=new JunboChargeLog();
        junboChargeLog.setMobile(mobile);
        junboChargeLog.setRightsMonth(rightsMonth.trim());
        junboChargeLog.setSourse(sourse);
        LambdaQueryWrapper<JunboChargeLog> queryWrapper = this.buildFindLambdaQueryWrapper(junboChargeLog);
        queryWrapper.orderByDesc(JunboChargeLog::getCreateTime);
        List<JunboChargeLog> junboChargeLogList=this.baseMapper.selectList(queryWrapper);
        junboChargeLogList.forEach(item -> {
            Optional<RightsPackDto> miguPackOptional =rightsPackService.getMiguPackList(item.getPackName());
            if(miguPackOptional.isPresent()){
                RightsPackDto pack=miguPackOptional.get();
                item.setPackName(pack.getTitleName());
                item.setProductImg(pack.getProductImg());
            }
        });
        return junboChargeLogList;
    }


    @NotNull
    public LambdaQueryWrapper<JunboChargeLog> buildFindLambdaQueryWrapper(JunboChargeLog junboChargeLog) {
        LambdaQueryWrapper<JunboChargeLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(junboChargeLog.getMobile())) {
            queryWrapper.eq(JunboChargeLog::getMobile,junboChargeLog.getMobile());
        }
        if (StringUtils.isNotBlank(junboChargeLog.getRightsMonth())) {
            queryWrapper.eq(JunboChargeLog::getRightsMonth,junboChargeLog.getRightsMonth());
        }
        if (StringUtils.isNotBlank(junboChargeLog.getServiceId())) {
            queryWrapper.eq(JunboChargeLog::getServiceId,junboChargeLog.getServiceId());
        }
        if (StringUtils.isNotBlank(junboChargeLog.getSourse())) {
            if(StringUtils.equals(junboChargeLog.getSourse(),BizConstant.SOURSE_PUBLIC)){
                queryWrapper.in(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS,BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            }else if(StringUtils.equals(junboChargeLog.getSourse(),BizConstant.SOURSE_DOUYIN)){
                queryWrapper.in(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS,BizConstant.JUNBO_RECHARGE_STATUS_FAIL,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED);
            }
        }
        if (StringUtils.isBlank(junboChargeLog.getSourse()) && junboChargeLog.getStatus()!=null) {
            queryWrapper.eq(JunboChargeLog::getStatus,junboChargeLog.getStatus());
        }
        return queryWrapper;
    }
    /**
     * 接收骏伯回调通知,根据通知更新直充记录
     *
     * orderSEQ是我方的订单号,orderId是骏伯下单后返回的订单号
     * code 0=充值成功
     *  {"code":"0","msg":"成功","orderId":"20210128115252711300768","orderSEQ":"202010000001","info":null}
     *  {"code":"1111","msg":"失败","orderId":"20210113172001571300027","orderSEQ":"9d840bde26fa4e1288d637ea45c03482","info":null}
     *
     * @param junboResult
     */
    @Override
    public void receiveRechargeNotify(JunboResult junboResult) {
        final String code = junboResult.getCode();
        Integer status = "0".equals(code) ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        this.lambdaUpdate()
                .eq(JunboChargeLog::getMiguOrderId, junboResult.getOrderSEQ())
                .set(JunboChargeLog::getStatus, status)
                .set(JunboChargeLog::getCallbackCode, code)
                .set(JunboChargeLog::getCallbackMsg, junboResult.getMsg())
                .set(JunboChargeLog::getUpdateTime,new Date())
                .update();
//        //设置天翼空间视听会员权益发送状态
//        setVideoSendStatus(junboResult.getOrderSEQ(), status,junboResult.getMsg());
//
//        updateAlipayRightsStatus(junboResult.getOrderSEQ(), status);
        JunboChargeLog junboChargeLog=this.lambdaQuery().eq(JunboChargeLog::getMiguOrderId, junboResult.getOrderSEQ()).one();
        if(junboChargeLog!=null){
            updateRechargeState(junboChargeLog);
        }
    }
    private void setVideoSendStatus(String orderSEQ, Integer status,String msg) {
        JunboChargeLog junboChargeLog=this.lambdaQuery().eq(JunboChargeLog::getMiguOrderId, orderSEQ).one();
        if(junboChargeLog==null){
            log.error("权益订单查询失败,订单号:{},状态:{},描述:{}",orderSEQ,status,msg);
            return;
        }
        Optional<TelecomOrder> telecomOrderOpt=telecomOrderService.lambdaQuery()
                .in(TelecomOrder::getStatus, BizConstant.TYKJ_TYPE_BOOK, BizConstant.TYKJ_TYPE_RENEW)
                .in(TelecomOrder::getType,   BizConstant.TYKJ_TYPE_BOOK, BizConstant.TYKJ_TYPE_RENEW)
                .eq(TelecomOrder::getPhone,junboChargeLog.getMobile())
                .list()
                .stream()
                .max(Comparator.comparing(TelecomOrder::getCreateTime));
        if(telecomOrderOpt.isPresent()){
            TelecomOrder telecomOrder = telecomOrderOpt.get();
            telecomOrderService.lambdaUpdate()
                    .set(TelecomOrder::getVideoSendStatus, status ==1?BizConstant.TYKJ_BDWP_SUCCEED:BizConstant.TYKJ_BDWP_FAIL)
                    .eq(TelecomOrder::getId,telecomOrder.getId())
                    .update();
        }
        //话费直充记录更新状态
        mobileFeeChargeLogService.lambdaUpdate()
                .eq(MobileFeeChargeLog::getOrderId,orderSEQ)
                .set(MobileFeeChargeLog::getStatus,status)
                .set(MobileFeeChargeLog::getRemark,msg)
                .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();

    }
    /**
     * 领取状态设置
     * @param junboChargeLog
     * @param junboResult
     * @return
     */
    @Override
    public Result<?> getReceiveState(JunboChargeLog junboChargeLog, JunboResult junboResult) {
        final String resultCode = junboResult.getCode();
        junboChargeLog.setRespCode(resultCode);
        junboChargeLog.setRespMsg(junboResult.getMsg());
        junboChargeLog.setJunboOrderId(junboResult.getOrderId());
        if("1025".equals(resultCode)) {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            this.updateById(junboChargeLog);
            return Result.error(413,"领取次数超过当日限制次数,请明天再试!");
        }
        if(!"100".equals(resultCode)) {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            this.updateById(junboChargeLog);
            return Result.error(410,"领取失败！");
        }
        this.updateById(junboChargeLog);
        return Result.ok(BizConstant.RECHARGE_MSG);
    }


    /**
     * 领取状态设置
     * @param junboChargeLog
     * @param junboRespon
     * @return
     */
    @Override
    public Result<?> getReceiveState(JunboChargeLog junboChargeLog, JunboRespon junboRespon) {
        final String resultCode = junboRespon.getCode();
        junboChargeLog.setRespCode(resultCode);
        junboChargeLog.setRespMsg(junboRespon.getMsg());
        if(!"0".equals(resultCode)) {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            this.updateById(junboChargeLog);
            return Result.error(410,"领取失败！");
        }
        this.updateById(junboChargeLog);
        return Result.ok(BizConstant.RECHARGE_MSG);
    }


    @Override
    public List<JunboChargeLog> findTimeoutSchedule() {
        return this.lambdaQuery()
                   .gt(JunboChargeLog::getScheduledTime, LocalDateTime.now().plusDays(-7))
                   .lt(JunboChargeLog::getScheduledTime, LocalDateTime.now())
                   .eq(JunboChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED)
                   .list();
    }

    @Override
    public void rechargeForSchedule(JunboChargeLog junboChargeLog) {
        String account = junboChargeLog.getAccount();
        String mobile=junboChargeLog.getMobile();
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=null;
        //支付宝订单与其他业务查询不同
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(junboChargeLog.getServiceId());
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(junboChargeLog.getServiceId());
        if(rightBusinessEnum==null && miguPacks!=null && !miguPacks.isEmpty()){
            chargeLogList = this.findByMobileAndPackNameAndDate(mobile,junboChargeLog.getPackName());
        }else{
            chargeLogList = this.findByMobileAndPackNameAndMonth(mobile,junboChargeLog.getPackName());
        }
        //判断当月是否有已充值成功或者充值中的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()) || BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasSuccess){
            return ;
        }

        //是否订购业务功能
        FebsResponse response=memberService.isMemberAndIsReceiveRights(mobile,junboChargeLog.getServiceId(),null,false,0L);
        if(!response.isOK()){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,订单已退订或暂停");
            junboChargeLog.setUpdateTime(new Date());
            this.updateById(junboChargeLog);


            updateAlipayRightsStatus(junboChargeLog.getMiguOrderId(),BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            return;
        }
        //券码充值
        if(String.valueOf(BizConstant.RECHARGE_STATE_CODE).equals(String.valueOf(junboChargeLog.getRechargeState()))){
            this.couponCodeCharge(junboChargeLog, mobile);
            return;
        }
        //白金会员权益（mgyybjhy）
        if(BizConstant.RECHARGE_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res=miguApiService.bjhyOrder(mobile, BizConstant.RECHARGE_BJHY_CHANNEL_ID);
            this.updateJunboChargeBJHYStatus(junboChargeLog, res);
            return;
        }
        //酷狗VIP月卡直充（kgykzhichong）
        if(BizConstant.KUGOU_COUPON_ID.equals(junboChargeLog.getCouponId())){
            KugouOrderResult res=kugouApiService.order(junboChargeLog.getMiguOrderId(),mobile);
            this.updateJunboChargeKGStatus(junboChargeLog, res);
            return;
        }
        //咪咕视频彩铃直充（mgyyvrbt）
        if(BizConstant.VRBT_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res = miguApiService.vrbtZeroOrder(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID,1);
            this.updateJunboChargeMGSPCLStatus(junboChargeLog, res);
            return;
        }
        //微信代金券直充（wechatdjq）
        if(BizConstant.WCDJQ_COUPON_ID.equals(junboChargeLog.getCouponId())){
            WechatCouponConfig wechatCouponConfig=wechatCouponConfigService.queryEffectiveWechatCouponConfig(junboChargeLog.getChannel());
            if(wechatCouponConfig!=null){
                boolean res=wxpayService.sendWeiXinCode(junboChargeLog.getAccount(),junboChargeLog.getMiguOrderId(),wechatCouponConfig);
                this.updateJunboChargeWXDJQStatus(junboChargeLog, res);
                return;
            }else{
                this.updateJunboChargeWXDJQStatus(junboChargeLog, false);
                return;
            }
        }
        if(BizConstant.COMPANY_OWNER_TUNIU.equals(junboChargeLog.getCompanyOwner())){
            Boolean rechargeStatus=false;
            String respCode=null;
            String respMsg=null;
            for(int i=0;i<BizConstant.PRODUCT_ID_LIST.size();i++){
                FebsResponse febs=unifyRightsFeignClient.tuniuSendRights(mobile,BizConstant.PRODUCT_ID_LIST.get(i));
                if(febs.isOK()){
                    rechargeStatus=true;
                }else{
                    respMsg=String.valueOf(febs.get("message"));
                    respCode=String.valueOf(febs.get("code"));
                }
            }
            junboChargeLog.setRespCode(respCode);
            junboChargeLog.setRespMsg(respMsg);
            junboChargeLog.setUpdateTime(new Date());
            if(rechargeStatus){
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            }else{
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            }
            this.updateById(junboChargeLog);
            return;
        }
        if(BizConstant.COMPANY_OWNER_HUAYI.equals(junboChargeLog.getCompanyOwner())){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            this.updateById(junboChargeLog);
            return;
        }

        //开始下单充值
//        final JunboResult junboResult = junboApiService.rechargeVIP(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
        //开始下单充值
        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());


        if(null==junboRespon){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("骏伯多会员下单接口异常");
            junboChargeLog.setUpdateTime(new Date());
            this.updateById(junboChargeLog);
            updateAlipayRightsStatus(junboChargeLog.getMiguOrderId(),BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            return;
        }
        final String resultCode = junboRespon.getCode();
        junboChargeLog.setRespCode(resultCode);
        junboChargeLog.setRespMsg(junboRespon.getMsg());
//        junboChargeLog.setJunboOrderId(junboRespon.getOrderId());
        junboChargeLog.setUpdateTime(new Date());

        //{"T":*************,"S":"100","F":"1349270325406330882","C":{"code":"100","msg":"接收成功","orderId":"20210113162129294300014"}}
        //{"T":*************,"S":"100","F":"1349273995275464705","C":{"code":"1025","msg":"充值号码充值次数超过当日限制次数","orderId":null}}
        //除开100,都视为充值失败,100表示正在充值中
        int status = "0".equals(resultCode) ? BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        junboChargeLog.setStatus(status);

        this.updateById(junboChargeLog);

        updateAlipayRightsStatus(junboChargeLog.getMiguOrderId(),status);
    }

    /**
     * 更新支付订单领取权益状态
     * @param orderNo
     * @param status
     */
    private void updateAlipayRightsStatus(String orderNo, Integer status) {
        JunboChargeLog junboChargeLog=this.lambdaQuery().eq(JunboChargeLog::getMiguOrderId, orderNo).orderByDesc(JunboChargeLog::getCreateTime).last("limit 1").one();
        if(junboChargeLog==null){
            log.error("权益订单查询失败,订单号:{},状态:{}",orderNo,status);
            return;
        }

        //支付宝权益充值不更新状态
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(junboChargeLog.getRemark());
        if(otherRecharge!=null){
            return;
        }
        //支付宝订单与其他业务查询不同
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(junboChargeLog.getServiceId());
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(junboChargeLog.getServiceId());
        if(rightBusinessEnum==null && miguPacks!=null && !miguPacks.isEmpty()){
            //查询最新支付订单
            AliSignChargingOrder orderPay=aliSignChargingOrderService.lambdaQuery()
                    .eq(AliSignChargingOrder::getMobile, junboChargeLog.getMobile())
                    .eq(AliSignChargingOrder::getBusinessType, junboChargeLog.getServiceId())
                    .eq(AliSignChargingOrder::getOrderStatus, 1)
                    .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
            if(orderPay!=null){
                aliSignChargingOrderService.lambdaUpdate().eq(AliSignChargingOrder::getId, orderPay.getId()).set(AliSignChargingOrder::getRightsStatus,status).update();
            }
        }
    }

    /**
     * 券码充值
     * @param junboChargeLog
     * @param mobile
     */
    @Override
    public FebsResponse couponCodeCharge(JunboChargeLog junboChargeLog, String mobile) {
        //查询有效期内未使用的券码
        CouponCode couponCode = couponCodeService.getEffectiveDateCouponCode(junboChargeLog.getCouponId());
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //饿了么需要同步更新券码状态
            if(StringUtils.equals(couponCode.getRightsId(),"eleme")){
                couponCodeService.updateCouponCodeStatus(couponCode.getId(), junboChargeLog.getMiguOrderId(),BizConstant.IS_USE);
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            }else if(couponCode.getRightsId().contains("hetu")){
                couponCodeService.updateCouponCodeStatus(couponCode.getId(), junboChargeLog.getMiguOrderId(),BizConstant.RECHARGE_WAIT);
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            }else{
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            }
            if(StringUtils.isNotBlank(mobile)){
                smsModelService.sendSmsAsync(mobile,couponCode.getRightsId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode.getCouponCode());
            }
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            this.updateById(junboChargeLog);
            return new FebsResponse().success("激活码已发送，请注意查看短信！").data(couponCode.getCouponCode());
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            this.updateById(junboChargeLog);
            return new FebsResponse().fail().message("暂无激活码！");
        }
    }


    /**
     * 立即直充
     * @param junboChargeLog
     * @return
     */
    @Override
    public void rechargeImmediate(JunboChargeLog junboChargeLog) {
        junboChargeLog.setMobile(StringUtils.isNotBlank(junboChargeLog.getMobile())?junboChargeLog.getMobile():junboChargeLog.getAccount());
        junboChargeLog.setAccount(StringUtils.isNotBlank(junboChargeLog.getAccount())?junboChargeLog.getAccount():junboChargeLog.getMobile());
        junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        junboChargeLog.setRightsMonth(rightsMonth);
        junboChargeLog.setCreateTime(new Date());
        MobileRegionResult mobileRegionResult = mobileRegionService.query(junboChargeLog.getMobile());
        if(mobileRegionResult!=null) {
            junboChargeLog.setProvince(mobileRegionResult.getProvince());
            junboChargeLog.setCity(mobileRegionResult.getCity());
        }
        this.save(junboChargeLog);

        if(BizConstant.COMPANY_OWNER_TUNIU.equals(junboChargeLog.getCompanyOwner())){
            Boolean rechargeStatus=false;
            String respCode=null;
            String respMsg=null;
            for(int i=0;i<BizConstant.PRODUCT_ID_LIST.size();i++){
                FebsResponse febs=unifyRightsFeignClient.tuniuSendRights(junboChargeLog.getMobile(),BizConstant.PRODUCT_ID_LIST.get(i));
                if(febs.isOK()){
                    rechargeStatus=true;
                }else{
                    respMsg=String.valueOf(febs.get("message"));
                    respCode=String.valueOf(febs.get("code"));
                }
            }
            junboChargeLog.setRespCode(respCode);
            junboChargeLog.setRespMsg(respMsg);
            junboChargeLog.setUpdateTime(new Date());
            if(rechargeStatus){
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            }else{
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            }
            this.updateById(junboChargeLog);
            return;
        }
        if(StringUtils.equals(junboChargeLog.getCompanyOwner(),BizConstant.COMPANY_OWNER_HUAYI)){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            this.updateById(junboChargeLog);
            return;
        }

        //开始下单充值
//        final JunboResult junboResult = junboApiService.rechargeVIP(junboChargeLog.getCouponId(), String.valueOf(junboChargeLog.getId()), junboChargeLog.getAccount(), junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(), String.valueOf(junboChargeLog.getId()), junboChargeLog.getAccount(), junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());
        if(null==junboRespon){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("骏伯多会员下单接口异常");
            this.updateById(junboChargeLog);
        }
        final String resultCode = junboRespon.getCode();
        junboChargeLog.setRespCode(resultCode);
        junboChargeLog.setRespMsg(junboRespon.getMsg());
//        junboChargeLog.setJunboOrderId(junboResult.getOrderId());
        //{"T":*************,"S":"100","F":"1349273995275464705","C":{"code":"1025","msg":"充值号码充值次数超过当日限制次数","orderId":null}}
        if("1025".equals(resultCode)) {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            this.updateById(junboChargeLog);
        }
        //{"T":*************,"S":"100","F":"1349270325406330882","C":{"code":"100","msg":"接收成功","orderId":"20210113162129294300014"}}
        if(!"0".equals(resultCode)) {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            this.updateById(junboChargeLog);
        }
        this.updateById(junboChargeLog);
    }

    @Override
    public void manualRecharge(JunboChargeLog paramChargeLog) {
        Optional<MemberRights> memberRightsOptional=memberRightsService.queryMemberRightsByName(paramChargeLog.getRightsName());
        if(!memberRightsOptional.isPresent()){
            return;
        }
        MemberRights memberRights=memberRightsOptional.get();
        String mobile=paramChargeLog.getMobile();
        Rights rights=new Rights(memberRights.getCouponId(),memberRights.getRightsName(),memberRights.getProductPrice(),paramChargeLog.getRightsMonth(),memberRights.getCompanyOwner());
        final JunboChargeLog junboChargeLog = this.createManualRechargeLog(mobile,paramChargeLog.getAccount(), paramChargeLog.getServiceId(), rights,paramChargeLog.getPackName());
        String orderSEQ = junboChargeLog.getMiguOrderId();
        //券码充值
        if(memberRights.getRechargeState().equals(BizConstant.RECHARGE_STATE_CODE)){
            this.couponCodeCharge(junboChargeLog, mobile);
            return;
        }
        //白金会员权益（mgyybjhy）
        if(BizConstant.RECHARGE_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res=miguApiService.bjhyOrder(mobile, BizConstant.RECHARGE_BJHY_CHANNEL_ID);
            this.updateJunboChargeBJHYStatus(junboChargeLog, res);
            return;
        }
        //酷狗VIP月卡直充（kgykzhichong）
        if(BizConstant.KUGOU_COUPON_ID.equals(junboChargeLog.getCouponId())){
            KugouOrderResult res=kugouApiService.order(orderSEQ,mobile);
            this.updateJunboChargeKGStatus(junboChargeLog, res);
            return;
        }
        //咪咕视频彩铃直充（mgyyvrbt）
        if(BizConstant.VRBT_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res = miguApiService.vrbtZeroOrder(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID,1);
            this.updateJunboChargeMGSPCLStatus(junboChargeLog, res);
            return;
        }
        //微信代金券直充（wechatdjq）
        if(BizConstant.WCDJQ_COUPON_ID.equals(junboChargeLog.getCouponId())){
            WechatCouponConfig wechatCouponConfig=wechatCouponConfigService.queryEffectiveWechatCouponConfig(junboChargeLog.getChannel());
            if(wechatCouponConfig!=null){
                boolean res=wxpayService.sendWeiXinCode(junboChargeLog.getAccount(),junboChargeLog.getMiguOrderId(),wechatCouponConfig);
                this.updateJunboChargeWXDJQStatus(junboChargeLog, res);
                return;
            }else{
                this.updateJunboChargeWXDJQStatus(junboChargeLog, false);
                return;
            }
        }
        if(BizConstant.COMPANY_OWNER_TUNIU.equals(junboChargeLog.getCompanyOwner())){
            Boolean rechargeStatus=false;
            String respCode=null;
            String respMsg=null;
            for(int i=0;i<BizConstant.PRODUCT_ID_LIST.size();i++){
                FebsResponse febs=unifyRightsFeignClient.tuniuSendRights(mobile,BizConstant.PRODUCT_ID_LIST.get(i));
                if(febs.isOK()){
                    rechargeStatus=true;
                }else{
                    respMsg=String.valueOf(febs.get("message"));
                    respCode=String.valueOf(febs.get("code"));
                }
            }
            junboChargeLog.setRespCode(respCode);
            junboChargeLog.setRespMsg(respMsg);
            junboChargeLog.setUpdateTime(new Date());
            if(rechargeStatus){
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            }else{
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            }
            this.updateById(junboChargeLog);
            return;
        }
        if(BizConstant.COMPANY_OWNER_HUAYI.equals(junboChargeLog.getCompanyOwner())){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            this.updateById(junboChargeLog);
            return;
        }


//        final JunboResult junboResult = junboApiService.rechargeVIP(memberRights.getCouponId(), orderSEQ, junboChargeLog.getAccount(), junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(memberRights.getCouponId(), orderSEQ, junboChargeLog.getAccount(), junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        this.getReceiveState(junboChargeLog,junboRespon);
    }
    @Override
    public Boolean isGetVRBTCLZXRights(String msisdn, String serviceId) {
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,msisdn)
                .eq(JunboChargeLog::getServiceId,serviceId)
                .gt(JunboChargeLog::getCreateTime,BizConstant.BEGIN_TIME)
                .eq(JunboChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS).count()>0;
    }

    /**
     * 根据当月充值月份查询对应业务的权益充值记录
     * @param mobile
     * @param packName
     * @return
     */
    @Override
    public List<JunboChargeLog> findByMobileAndPackNameAndMonth(String mobile,String packName){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .eq(JunboChargeLog::getRightsMonth,DateUtil.formatYearMonth(LocalDateTime.now()))
                .eq(JunboChargeLog::getPackName,packName).list();
    }

    /**
     * 根据支付时间查询对应业务的权益充值记录
     * @param mobile
     * @param packName
     * @return
     */
    @Override
    public List<JunboChargeLog> findByMobileAndPackNameAndDate(String mobile,String packName){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .between(JunboChargeLog::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .eq(JunboChargeLog::getPackName,packName).list();
    }

    /**
     * 根据支付时间查询对应业务的权益充值记录
     * @param mobile
     * @param packName
     * @return
     */
    @Override
    public List<JunboChargeLog> findByMobileAndPackNameAndDate(String mobile,String packName,Long days){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .between(JunboChargeLog::getPayTime, LocalDateTime.now().minusDays(days),LocalDateTime.now())
                .eq(JunboChargeLog::getPackName,packName).list();
    }
    /**
     * 判断当月是否领取视听会员权益
     * @param mobile
     * @param packNameList
     * @return
     */
    @Override
    public List<JunboChargeLog> findByMobileAndPackNameListAndMonth(String mobile,List<String> packNameList){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .eq(JunboChargeLog::getRightsMonth,DateUtil.formatYearMonth(LocalDateTime.now()))
                .notIn(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_FAIL,BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY)
                .in(JunboChargeLog::getPackName,packNameList).list();
    }

    /**
     * 判断支付的自然月是否领取视听会员权益
     * @param mobile
     * @param packNameList
     * @return
     */
    @Override
    public List<JunboChargeLog> findByMobileAndPackNameListAndDate(String mobile,List<String> packNameList){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .between(JunboChargeLog::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .notIn(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_FAIL,BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY)
                .in(JunboChargeLog::getPackName,packNameList).list();
    }
    @Override
    public List<JunboChargeLog> findByServiceIdAndMobileAndMonth(String serviceId,String mobile){
        return this.lambdaQuery()
                   .eq(JunboChargeLog::getMobile,mobile)
                   .eq(JunboChargeLog::getRightsMonth,DateUtil.formatYearMonth(LocalDateTime.now()))
                   .eq(JunboChargeLog::getServiceId,serviceId).list();
    }

    @Override
    public List<JunboChargeLog> findByServiceIdAndMobileAndDate(String serviceId,String mobile){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .between(JunboChargeLog::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .eq(JunboChargeLog::getServiceId,serviceId).list();
    }
    /**
     * 白金会员充值更新充值状态
     * @param junboChargeLog
     * @param res
     */
    @Override
    public void updateJunboChargeBJHYStatus(JunboChargeLog junboChargeLog, RemoteResult res) {
        if(res.isOK()){
            //白金会员充值告警
            rechargeAlertService.watchBjhyRecharge(junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("充值成功");
            String remark=StringUtils.isNotEmpty(junboChargeLog.getRemark())?junboChargeLog.getRemark()+"充值成功":"充值成功";
            junboChargeLog.setRemark(remark);

            this.updateById(junboChargeLog);
        }else{
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("充值失败");
            String remark=StringUtils.isNotEmpty(junboChargeLog.getRemark())?junboChargeLog.getRemark()+"充值失败":"充值失败";
            junboChargeLog.setRemark(remark);
            this.updateById(junboChargeLog);
        }
    }


    /**
     * 酷狗直充充值更新充值状态
     * @param junboChargeLog
     * @param res
     */
    @Override
    public void updateJunboChargeKGStatus(JunboChargeLog junboChargeLog, KugouOrderResult res) {
        if(res.isOK() && res.getData()!=null  && StringUtils.isNotBlank(res.getData().getKgOrderNumber())){
            //酷狗直充充值告警
            rechargeAlertService.watchKGZCRecharge(junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("充值成功");
            junboChargeLog.setRemark("酷狗直充-充值成功");
            this.updateById(junboChargeLog);
        }else{
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("充值失败");
            junboChargeLog.setRemark("酷狗直充-充值失败");
            this.updateById(junboChargeLog);
        }
    }

    /**
     * 咪咕视频彩铃充值更新充值状态
     * @param junboChargeLog
     * @param res
     */
    @Override
    public void updateJunboChargeMGSPCLStatus(JunboChargeLog junboChargeLog, RemoteResult res) {
        if(res.isOK()){
            //咪咕视频彩铃充值告警
            rechargeAlertService.watchMiGuShipCaiLingRecharge(junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("充值成功");
            String remark=StringUtils.isNotEmpty(junboChargeLog.getRemark())?junboChargeLog.getRemark()+"充值成功":"充值成功";
            junboChargeLog.setRemark(remark);

            this.updateById(junboChargeLog);
        }else{
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("充值失败");
            String remark=StringUtils.isNotEmpty(junboChargeLog.getRemark())?junboChargeLog.getRemark()+"充值失败":"充值失败";
            junboChargeLog.setRemark(remark);
            this.updateById(junboChargeLog);
        }
    }

    /**
     * 微信代金券充值更新充值状态
     * @param junboChargeLog
     * @param res
     */
    @Override
    public void updateJunboChargeWXDJQStatus(JunboChargeLog junboChargeLog, boolean res) {
        if(res){
            //微信代金券充值告警
            rechargeAlertService.watchWechatCouponCodeRecharge(junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("充值成功");
            String remark=StringUtils.isNotEmpty(junboChargeLog.getRemark())?junboChargeLog.getRemark()+"充值成功":"充值成功";
            junboChargeLog.setRemark(remark);

            this.updateById(junboChargeLog);
        }else{
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("充值失败");
            String remark=StringUtils.isNotEmpty(junboChargeLog.getRemark())?junboChargeLog.getRemark()+"充值失败":"充值失败";
            junboChargeLog.setRemark(remark);
            this.updateById(junboChargeLog);
        }
    }
    /**
     * 酷狗直充回收更新充值状态
     * @param junboChargeLog
     * @param res
     */
    @Override
    public void updateJunboChargeKugouStatus(JunboChargeLog junboChargeLog, KugouRecoverOrderResult res) {
        if(res.isOK()){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("回收成功");
            junboChargeLog.setRemark("酷狗直充-回收成功");
            this.updateById(junboChargeLog);
        }else{
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("回收失败");
            junboChargeLog.setRemark("酷狗直充-回收失败");
            this.updateById(junboChargeLog);
        }
    }

    /**
     * 回收权益
     * @param subscribe
     */
    @Override
    public void recoverKugouOrder(Subscribe subscribe) {
        if(!StringUtils.equalsAny(subscribe.getChannel(), MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_KUGOU,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174)){
            return;
        }
        //根据手机号查询当月充值成功的酷狗月卡权益
        JunboChargeLog chargeLog = this.findByMobile(subscribe.getMobile());
        if(chargeLog==null){
            return;
        }
        String orderSEQ = chargeLog.getMiguOrderId();
        //查询酷狗月卡是否已经充值到账
        KugouQueryResult queryResult=kugouApiService.queryOrder(subscribe.getMobile(),orderSEQ);
        if(queryResult.isOK() && queryResult.getData()!=null && queryResult.getData().getStatus().equals("1")){
            //回收酷狗月卡
            KugouRecoverOrderResult res=kugouApiService.recoverOrder(subscribe.getMobile(),orderSEQ);
            this.updateJunboChargeKugouStatus(chargeLog,res);
        }
    }




    @Override
    public List<BusinessChargeDto> pageBusinessList(BusinessChargeDto dto) {
        List<BusinessChargeDto> list=this.baseMapper.findByPage(dto);
        FebsResponse febsResponse=unifyRightsFeignClient.findByPage(dto);
        if(febsResponse.isOK()){
            List<BusinessChargeDto> lists = null;
            try {
                lists = mapper.readValue(febsResponse.get("data").toString(), List.class);
                list.addAll(lists);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return list;
    }



    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_RIGHTS_LIST,key = "'all'",unless = "#result==null")
    public  List<RightsDto> queryServiceList(){
        List<RightsDto> list=this.baseMapper.getServiceList();
//        list.addAll(rightsDtoList);
//        List<RightsDto> rightsList = list.stream().collect(//list是需要去重的list，返回值是去重后的list
//                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOptionKey()))), ArrayList::new));
        return list;
    }
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_RIGHTS_DETAILS,key = "#dto",unless = "#result==null")
    public List<RightsDto> queryRightsList(RightsDto dto) {
        List<RightsDto> list=this.baseMapper.findByServiceId(dto);
        if(list!=null && !list.isEmpty()){
//            List<RightsDto> rightsList = list.stream().collect(//list是需要去重的list，返回值是去重后的list
//                    Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOptionKey()))), ArrayList::new));
            return list;
        }
        FebsResponse febsResponse=unifyRightsFeignClient.findByServiceId(dto);
        if(febsResponse.isOK()){
            try{
                List<RightsDto> lists = mapper.readValue(febsResponse.get("data").toString(), List.class);
//                List<RightsDto> rightsList = lists.stream().collect(//list是需要去重的list，返回值是去重后的list
//                        Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOptionKey()))), ArrayList::new));
                return lists;
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }




//
//    @Override
//    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_RIGHTS_LIST_VRVT,key = "'all'",unless = "#result==null")
//    public  List<RightsDto> queryServiceListVrbt(){
//        List<RightsDto> list=this.baseMapper.getServiceList();
//        list.addAll(rightsDtoList);
//
//
//        List<RightsDto> rightsList = list.stream().collect(//list是需要去重的list，返回值是去重后的list
//                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOptionKey()))), ArrayList::new));
//        return rightsList;
//    }
//    @Override
//    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_RIGHTS_DETAILS_VRVT,key = "#dto",unless = "#result==null")
//    public List<RightsDto> getRightsListVrbt(RightsDto dto) {
//        List<RightsDto> list=this.baseMapper.findByServiceId(dto);
//
//        List<RightsDto> rightsList = list.stream().collect(//list是需要去重的list，返回值是去重后的list
//                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOptionKey()))), ArrayList::new));
//        return rightsList;
//    }

    /**
     * 更新酷狗续订
     */
    @Override
    public void updateKuGouStock() {
        LocalDateTime start = LocalDateTime.of( LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        LocalDateTime end=    LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        List<KugouOrder> kugouList = kugouOrderService.lambdaQuery()
                .between(KugouOrder::getCreateTime,start,end)
                .eq(KugouOrder::getStatus,1)
                .eq(KugouOrder::getRechargeStatus,0)
                .orderByAsc(KugouOrder::getCreateTime)
                .last("LIMIT 1000")
                .list();
        while(kugouList!=null && kugouList.size()>0){
            kugouList.forEach(kugou ->  {
                Integer rechargeStatus=0;
                final String KG_CHANNEL_CODE="00210VV";
                final RemoteResult batc =miguApiService.cpmbQuery(kugou.getMobile(),KG_CHANNEL_CODE);
                if(batc.isCpmbMember()){
                    rechargeStatus=1;
                }else{
                    rechargeStatus=2;
                }
                String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
                kugouOrderService.lambdaUpdate().eq(KugouOrder::getId, kugou.getId()).set(KugouOrder::getRechargeStatus, rechargeStatus).set(KugouOrder::getRightsMonth, rightsMonth).update();
            });
            kugouList = kugouOrderService.lambdaQuery()
                    .between(KugouOrder::getCreateTime,start,end)
                    .eq(KugouOrder::getStatus,1)
                    .eq(KugouOrder::getRechargeStatus,0)
                    .orderByAsc(KugouOrder::getCreateTime)
                    .last("LIMIT 1000")
                    .list();
        }
    }

    /**
     * 酷狗续订权益发送
     */
    @Override
    public void kuGouRightsSend() {
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        List<KugouOrder> kugouList = kugouOrderService.lambdaQuery()
                .eq(KugouOrder::getRightsMonth,rightsMonth)
                .eq(KugouOrder::getRechargeStatus,1)
                .orderByAsc(KugouOrder::getCreateTime)
                .last("LIMIT 1000")
                .list();
        while(kugouList!=null && kugouList.size()>0){
            kugouList.forEach(kugou ->  {
                final String KG_CHANNEL_CODE="00210VV";
                final RemoteResult batc =miguApiService.cpmbQuery(kugou.getMobile(),KG_CHANNEL_CODE);
                if(batc.isCpmbMember()){
                    kugouOrderService.lambdaUpdate().eq(KugouOrder::getId, kugou.getId()).set(KugouOrder::getRechargeStatus, 3).update();
                    memberService.memberReceiveRights("698039020108689345kg","kg_rights_pack_1","kgykzhichong",kugou.getMobile(),kugou.getMobile(),false,false);
                }else{
                    kugouOrderService.lambdaUpdate().eq(KugouOrder::getId, kugou.getId()).set(KugouOrder::getRechargeStatus, 2).set(KugouOrder::getRemark, "酷狗包月查询失败(已退订)").update();
                }
            });
            kugouList = kugouOrderService.lambdaQuery()
                    .eq(KugouOrder::getRightsMonth,rightsMonth)
                    .eq(KugouOrder::getRechargeStatus,1)
                    .orderByAsc(KugouOrder::getCreateTime)
                    .last("LIMIT 1000")
                    .list();
        }
    }

    /**
     * 支付宝权益充值
     * @param msisdn
     * @param account
     * @param serviceId
     * @param rights
     * @param status
     * @param remark
     * @param packName
     * @return
     */
    @Override
    public JunboChargeLog alipayRightsRecharge(String msisdn,String account, String serviceId, Rights rights, Integer status, String remark,String packName,String orderId) {
        JunboChargeLog junboChargeLog = new JunboChargeLog();
        junboChargeLog.setMobile(msisdn);
        junboChargeLog.setAccount(StringUtils.isNotBlank(account)?account:msisdn);
        //生成uuid作为我方订单号
        junboChargeLog.setMiguOrderId(orderId);
        junboChargeLog.setStatus(status);
        junboChargeLog.setScheduledTime(null);
        junboChargeLog.setCouponId(rights.getCouponId());
        junboChargeLog.setCouponName(rights.getRightsName());
        junboChargeLog.setCouponPrice(rights.getProductPrice());
        if(StringUtils.isBlank(rights.getRightsMonth())){
            String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
            junboChargeLog.setRightsMonth(rightsMonth);
        }else{
            junboChargeLog.setRightsMonth(rights.getRightsMonth());
        }
        junboChargeLog.setCreateTime(new Date());
        junboChargeLog.setServiceId(serviceId);
        junboChargeLog.setRemark(remark);
        junboChargeLog.setPackName(packName);
        junboChargeLog.setRechargeState(rights.getRechargeState());
        junboChargeLog.setPayTime(rights.getPayTime());
        junboChargeLog.setCompanyOwner(rights.getCompanyOwner());
        MobileRegionResult mobileRegionResult = mobileRegionService.query(msisdn);
        if(mobileRegionResult!=null) {
            junboChargeLog.setProvince(mobileRegionResult.getProvince());
            junboChargeLog.setCity(mobileRegionResult.getCity());
        }
        this.save(junboChargeLog);
        return junboChargeLog;
    }


    /**
     * 商城权益充值
     * @param msisdn
     * @param account
     * @param serviceId
     * @param rights
     * @param scheduledTime
     * @param packName
     * @param orderId
     * @return
     */
    @Override
    public JunboChargeLog shopOrderRightsRecharge(String msisdn,String account, String serviceId, Rights rights, Date scheduledTime, String packName,String orderId,String loginMobile) {
        JunboChargeLog junboChargeLog = new JunboChargeLog();
        junboChargeLog.setMobile(msisdn);
        junboChargeLog.setAccount(StringUtils.isNotBlank(account)?account:msisdn);
        //生成uuid作为我方订单号
        junboChargeLog.setMiguOrderId(orderId);
        junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED);
        junboChargeLog.setScheduledTime(scheduledTime);
        junboChargeLog.setCouponId(rights.getCouponId());
        junboChargeLog.setCouponName(rights.getRightsName());
        junboChargeLog.setCouponPrice(rights.getProductPrice());
        if(StringUtils.isBlank(rights.getRightsMonth())){
            String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
            junboChargeLog.setRightsMonth(rightsMonth);
        }else{
            junboChargeLog.setRightsMonth(rights.getRightsMonth());
        }
        junboChargeLog.setCreateTime(new Date());
        junboChargeLog.setServiceId(serviceId);
        junboChargeLog.setRemark(loginMobile);
        junboChargeLog.setPackName(packName);
        junboChargeLog.setRechargeState(rights.getRechargeState());
        junboChargeLog.setPayTime(rights.getPayTime());
        junboChargeLog.setCompanyOwner(rights.getCompanyOwner());
        MobileRegionResult mobileRegionResult = mobileRegionService.query(msisdn);
        if(mobileRegionResult!=null) {
            junboChargeLog.setProvince(mobileRegionResult.getProvince());
            junboChargeLog.setCity(mobileRegionResult.getCity());
        }
        this.save(junboChargeLog);
        return junboChargeLog;
    }

    @Override
    public  FebsResponse notifyHeTuRechargeResult(HeTuResult heTuResult){
        Integer status=heTuResult.getStatus().equals("1")?BizConstant.IS_USE:BizConstant.RECHARGE_FAIL;
        String orderId="";
        String extrInfo="";
        //查询未使用的券码
        HetuCouponCode hetuCouponCode=hetuCouponCodeService.lambdaQuery()
                .select(HetuCouponCode::getId,HetuCouponCode::getOrderId,HetuCouponCode::getExtrInfo)
                .eq(HetuCouponCode::getCouponCode,heTuResult.getCouponCode())
//                .eq(HetuCouponCode::getRightsId,heTuResult.getType())
                .eq(HetuCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                .ge(HetuCouponCode::getInvalidTime,heTuResult.getUseTime())
                .orderByDesc(HetuCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(hetuCouponCode!=null){
                 hetuCouponCodeService.lambdaUpdate()
                .eq(HetuCouponCode::getId, hetuCouponCode.getId())
                .set(HetuCouponCode::getStatus,status)
                .set(HetuCouponCode::getSendServer,heTuResult.getSendServer())
                .set(HetuCouponCode::getSendRole,heTuResult.getSendRole())
                .set(HetuCouponCode::getSendTime,heTuResult.getUseTime())
                .set(HetuCouponCode::getUpdateTime,new Date())
                .update();
            orderId=hetuCouponCode.getOrderId();
            extrInfo=hetuCouponCode.getExtrInfo();
        }else{
            CouponCode couponCode=couponCodeService.lambdaQuery()
                    .eq(CouponCode::getCouponCode,heTuResult.getCouponCode())
//                    .eq(CouponCode::getRightsId,heTuResult.getType())
                    .eq(CouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                    .ge(CouponCode::getInvalidTime,heTuResult.getUseTime())
                    .orderByDesc(CouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(couponCode!=null){
                couponCodeService.lambdaUpdate()
                        .eq(CouponCode::getId, couponCode.getId())
                        .set(CouponCode::getStatus,status)
                        .set(CouponCode::getSendServer,heTuResult.getSendServer())
                        .set(CouponCode::getSendRole,heTuResult.getSendRole())
                        .set(CouponCode::getSendTime,heTuResult.getUseTime())
                        .set(CouponCode::getUpdateTime,new Date())
                        .update();
                orderId=couponCode.getOrderId();
                extrInfo=couponCode.getExtrInfo();
            }
        }
        if(StringUtils.isEmpty(orderId)){
            return new FebsResponse().fail().message("券码已过期！");
        }
        Integer chargeCount=this.lambdaQuery().eq(JunboChargeLog::getMiguOrderId,orderId).eq(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING).count();
        if(chargeCount<=0){
            return new FebsResponse().fail().message("订单不存在！");
        }
        this.lambdaUpdate().eq(JunboChargeLog::getMiguOrderId, orderId)
                .set(JunboChargeLog::getStatus, heTuResult.getStatus())
                .set(JunboChargeLog::getCallbackMsg, heTuResult.getCallbackMsg())
                .set(JunboChargeLog::getUpdateTime,new Date())
                .update();

        if(heTuResult.getStatus().equals("1")){
            ObjectNode dataNode =mapper.createObjectNode();
            String timestamp= String.valueOf(System.currentTimeMillis());
            final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("MIGUHUYU");
            String sign=getSign(orderId,timestamp,otherRecharge.getKey());
            dataNode.put("orderId",orderId);
            dataNode.put("receiveTime",timestamp);
            dataNode.put("timestamp",timestamp);
            dataNode.put("extrInfo",extrInfo);
            dataNode.put("sendServer",heTuResult.getSendServer());
            dataNode.put("sendRole",heTuResult.getSendRole());
            this.implementHttpPostResult(otherRecharge.getReceivebackUrl(),dataNode.toString(),"咪咕互娱道具领取通知",sign);
        }
        return new FebsResponse().success().message("成功");
    }






    @Override
    public  FebsResponse gamePropNotify(GamePropResult gamePropResult){
        String orderId="";
        String extrInfo="";
        int status=USE_SUCCESS.equals(gamePropResult.getStatus())?BizConstant.IS_USE:BizConstant.RECHARGE_FAIL;
        switch (gamePropResult.getGameName()){
            case SWFZ_NAME:
                //查询未使用的券码
                SwfzCouponCode swfzCouponCode=swfzCouponCodeService.lambdaQuery()
                        .select(SwfzCouponCode::getId,SwfzCouponCode::getOrderId,SwfzCouponCode::getExtrInfo)
                        .eq(SwfzCouponCode::getCouponCode,gamePropResult.getCouponCode())
                        .eq(SwfzCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                        .ge(SwfzCouponCode::getInvalidTime,gamePropResult.getUseTime())
                        .orderByDesc(SwfzCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(swfzCouponCode==null || StringUtils.isEmpty(swfzCouponCode.getOrderId())){
                    return new FebsResponse().fail().message("券码已过期！");
                }
                swfzCouponCodeService.lambdaUpdate()
                        .eq(SwfzCouponCode::getId, swfzCouponCode.getId())
                        .set(SwfzCouponCode::getStatus,status)
                        .set(SwfzCouponCode::getSendServer,gamePropResult.getSendServer())
                        .set(SwfzCouponCode::getSendRole,gamePropResult.getSendRole())
                        .set(SwfzCouponCode::getSendTime,gamePropResult.getUseTime())
                        .set(SwfzCouponCode::getUpdateTime,new Date())
                        .update();
                orderId=swfzCouponCode.getOrderId();
                extrInfo=swfzCouponCode.getExtrInfo();
                break;
            case DYXC_NAME:
                //查询未使用的券码
                DyxcCouponCode dyxcCouponCode=dyxcCouponCodeService.lambdaQuery()
                        .select(DyxcCouponCode::getId,DyxcCouponCode::getOrderId,DyxcCouponCode::getExtrInfo)
                        .eq(DyxcCouponCode::getCouponCode,gamePropResult.getCouponCode())
                        .eq(DyxcCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                        .ge(DyxcCouponCode::getInvalidTime,gamePropResult.getUseTime())
                        .orderByDesc(DyxcCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(dyxcCouponCode==null || StringUtils.isEmpty(dyxcCouponCode.getOrderId())){
                    return new FebsResponse().fail().message("券码已过期！");
                }
                dyxcCouponCodeService.lambdaUpdate()
                        .eq(DyxcCouponCode::getId, dyxcCouponCode.getId())
                        .set(DyxcCouponCode::getStatus,status)
                        .set(DyxcCouponCode::getSendServer,gamePropResult.getSendServer())
                        .set(DyxcCouponCode::getSendRole,gamePropResult.getSendRole())
                        .set(DyxcCouponCode::getSendTime,gamePropResult.getUseTime())
                        .set(DyxcCouponCode::getUpdateTime,new Date())
                        .update();
                orderId=dyxcCouponCode.getOrderId();
                extrInfo=dyxcCouponCode.getExtrInfo();
                break;
            case CFDTW_NAME:
                //查询未使用的券码
                CfdtwCouponCode cfdtwCouponCode=cfdtwCouponCodeService.lambdaQuery()
                        .select(CfdtwCouponCode::getId,CfdtwCouponCode::getOrderId,CfdtwCouponCode::getExtrInfo)
                        .eq(CfdtwCouponCode::getCouponCode,gamePropResult.getCouponCode())
                        .eq(CfdtwCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                        .ge(CfdtwCouponCode::getInvalidTime,gamePropResult.getUseTime())
                        .orderByDesc(CfdtwCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(cfdtwCouponCode==null || StringUtils.isEmpty(cfdtwCouponCode.getOrderId())){
                    return new FebsResponse().fail().message("券码已过期！");
                }
                cfdtwCouponCodeService.lambdaUpdate()
                        .eq(CfdtwCouponCode::getId, cfdtwCouponCode.getId())
                        .set(CfdtwCouponCode::getStatus,status)
                        .set(CfdtwCouponCode::getSendServer,gamePropResult.getSendServer())
                        .set(CfdtwCouponCode::getSendRole,gamePropResult.getSendRole())
                        .set(CfdtwCouponCode::getSendTime,gamePropResult.getUseTime())
                        .set(CfdtwCouponCode::getUpdateTime,new Date())
                        .update();
                orderId=cfdtwCouponCode.getOrderId();
                extrInfo=cfdtwCouponCode.getExtrInfo();
                break;
            case WZWD_NAME:
                //查询未使用的券码
                WzwdCouponCode wzwdCouponCode=wzwdCouponCodeService.lambdaQuery()
                        .select(WzwdCouponCode::getId,WzwdCouponCode::getOrderId,WzwdCouponCode::getExtrInfo)
                        .eq(WzwdCouponCode::getCouponCode,gamePropResult.getCouponCode())
                        .eq(WzwdCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                        .ge(WzwdCouponCode::getInvalidTime,gamePropResult.getUseTime())
                        .orderByDesc(WzwdCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(wzwdCouponCode==null || StringUtils.isEmpty(wzwdCouponCode.getOrderId())){
                    return new FebsResponse().fail().message("券码已过期！");
                }
                wzwdCouponCodeService.lambdaUpdate()
                        .eq(WzwdCouponCode::getId, wzwdCouponCode.getId())
                        .set(WzwdCouponCode::getStatus,status)
                        .set(WzwdCouponCode::getSendServer,gamePropResult.getSendServer())
                        .set(WzwdCouponCode::getSendRole,gamePropResult.getSendRole())
                        .set(WzwdCouponCode::getSendTime,gamePropResult.getUseTime())
                        .set(WzwdCouponCode::getUpdateTime,new Date())
                        .update();
                orderId=wzwdCouponCode.getOrderId();
                extrInfo=wzwdCouponCode.getExtrInfo();
                break;
            case SJCS_NAME:
                //查询未使用的券码
                SjcsCouponCode sjcsCouponCode=sjscCouponCodeService.lambdaQuery()
                        .select(SjcsCouponCode::getId,SjcsCouponCode::getOrderId,SjcsCouponCode::getExtrInfo)
                        .eq(SjcsCouponCode::getCouponCode,gamePropResult.getCouponCode())
                        .eq(SjcsCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                        .ge(SjcsCouponCode::getInvalidTime,gamePropResult.getUseTime())
                        .orderByDesc(SjcsCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(sjcsCouponCode==null || StringUtils.isEmpty(sjcsCouponCode.getOrderId())){
                    return new FebsResponse().fail().message("券码已过期！");
                }
                sjscCouponCodeService.lambdaUpdate()
                        .eq(SjcsCouponCode::getId, sjcsCouponCode.getId())
                        .set(SjcsCouponCode::getStatus,status)
                        .set(SjcsCouponCode::getSendServer,gamePropResult.getSendServer())
                        .set(SjcsCouponCode::getSendRole,gamePropResult.getSendRole())
                        .set(SjcsCouponCode::getSendTime,gamePropResult.getUseTime())
                        .set(SjcsCouponCode::getUpdateTime,new Date())
                        .update();
                orderId=sjcsCouponCode.getOrderId();
                extrInfo=sjcsCouponCode.getExtrInfo();
                break;
            case AHZZ_NAME:
                //查询未使用的券码
                AhzzCouponCode ahzzCouponCode=ahzzCouponCodeService.lambdaQuery()
                        .select(AhzzCouponCode::getId,AhzzCouponCode::getOrderId,AhzzCouponCode::getExtrInfo)
                        .eq(AhzzCouponCode::getCouponCode,gamePropResult.getCouponCode())
                        .eq(AhzzCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                        .ge(AhzzCouponCode::getInvalidTime,gamePropResult.getUseTime())
                        .orderByDesc(AhzzCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(ahzzCouponCode==null || StringUtils.isEmpty(ahzzCouponCode.getOrderId())){
                    return new FebsResponse().fail().message("券码已过期！");
                }
                ahzzCouponCodeService.lambdaUpdate()
                        .eq(AhzzCouponCode::getId, ahzzCouponCode.getId())
                        .set(AhzzCouponCode::getStatus,status)
                        .set(AhzzCouponCode::getSendServer,gamePropResult.getSendServer())
                        .set(AhzzCouponCode::getSendRole,gamePropResult.getSendRole())
                        .set(AhzzCouponCode::getSendTime,gamePropResult.getUseTime())
                        .set(AhzzCouponCode::getUpdateTime,new Date())
                        .update();
                orderId=ahzzCouponCode.getOrderId();
                extrInfo=ahzzCouponCode.getExtrInfo();
                break;
            default:
                //查询未使用的券码
                GameSendCouponCode gameSendCouponCode=sendCodeService.lambdaQuery()
                        .select(GameSendCouponCode::getId,GameSendCouponCode::getOrderId,GameSendCouponCode::getExtrInfo)
                        .eq(GameSendCouponCode::getCouponCode,gamePropResult.getCouponCode())
                        .eq(GameSendCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
                        .ge(GameSendCouponCode::getInvalidTime,gamePropResult.getUseTime())
                        .orderByDesc(GameSendCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(gameSendCouponCode==null || StringUtils.isEmpty(gameSendCouponCode.getOrderId())){
                    return new FebsResponse().fail().message("券码已过期！");
                }
                sendCodeService.lambdaUpdate()
                        .eq(GameSendCouponCode::getId, gameSendCouponCode.getId())
                        .set(GameSendCouponCode::getStatus,status)
                        .set(GameSendCouponCode::getSendServer,gamePropResult.getSendServer())
                        .set(GameSendCouponCode::getSendRole,gamePropResult.getSendRole())
                        .set(GameSendCouponCode::getSendTime,gamePropResult.getUseTime())
                        .set(GameSendCouponCode::getUpdateTime,new Date())
                        .update();
                orderId=gameSendCouponCode.getOrderId();
                extrInfo=gameSendCouponCode.getExtrInfo();
                break;
        }
//        if(SWFZ_NAME.equals(gamePropResult.getGameName())){
//            //查询未使用的券码
//            SwfzCouponCode couponCode=swfzCouponCodeService.lambdaQuery()
//                    .select(SwfzCouponCode::getId,SwfzCouponCode::getOrderId,SwfzCouponCode::getExtrInfo)
//                    .eq(SwfzCouponCode::getCouponCode,gamePropResult.getCouponCode())
//                    .eq(SwfzCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
//                    .ge(SwfzCouponCode::getInvalidTime,gamePropResult.getUseTime())
//                    .orderByDesc(SwfzCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//            if(couponCode==null || StringUtils.isEmpty(couponCode.getOrderId())){
//                return new FebsResponse().fail().message("券码已过期！");
//            }
//            swfzCouponCodeService.lambdaUpdate()
//                    .eq(SwfzCouponCode::getId, couponCode.getId())
//                    .set(SwfzCouponCode::getStatus,status)
//                    .set(SwfzCouponCode::getSendServer,gamePropResult.getSendServer())
//                    .set(SwfzCouponCode::getSendRole,gamePropResult.getSendRole())
//                    .set(SwfzCouponCode::getSendTime,gamePropResult.getUseTime())
//                    .set(SwfzCouponCode::getUpdateTime,new Date())
//                    .update();
//            orderId=couponCode.getOrderId();
//            extrInfo=couponCode.getExtrInfo();
//        }else if(DYXC_NAME.equals(gamePropResult.getGameName())){
//            //查询未使用的券码
//            DyxcCouponCode couponCode=dyxcCouponCodeService.lambdaQuery()
//                    .select(DyxcCouponCode::getId,DyxcCouponCode::getOrderId,DyxcCouponCode::getExtrInfo)
//                    .eq(DyxcCouponCode::getCouponCode,gamePropResult.getCouponCode())
//                    .eq(DyxcCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
//                    .ge(DyxcCouponCode::getInvalidTime,gamePropResult.getUseTime())
//                    .orderByDesc(DyxcCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//            if(couponCode==null || StringUtils.isEmpty(couponCode.getOrderId())){
//                return new FebsResponse().fail().message("券码已过期！");
//            }
//            dyxcCouponCodeService.lambdaUpdate()
//                    .eq(DyxcCouponCode::getId, couponCode.getId())
//                    .set(DyxcCouponCode::getStatus,status)
//                    .set(DyxcCouponCode::getSendServer,gamePropResult.getSendServer())
//                    .set(DyxcCouponCode::getSendRole,gamePropResult.getSendRole())
//                    .set(DyxcCouponCode::getSendTime,gamePropResult.getUseTime())
//                    .set(DyxcCouponCode::getUpdateTime,new Date())
//                    .update();
//            orderId=couponCode.getOrderId();
//            extrInfo=couponCode.getExtrInfo();
//        }else if(CFDTW_NAME.equals(gamePropResult.getGameName())){
//            //查询未使用的券码
//            CfdtwCouponCode couponCode=cfdtwCouponCodeService.lambdaQuery()
//                    .select(CfdtwCouponCode::getId,CfdtwCouponCode::getOrderId,CfdtwCouponCode::getExtrInfo)
//                    .eq(CfdtwCouponCode::getCouponCode,gamePropResult.getCouponCode())
//                    .eq(CfdtwCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
//                    .ge(CfdtwCouponCode::getInvalidTime,gamePropResult.getUseTime())
//                    .orderByDesc(CfdtwCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//            if(couponCode==null || StringUtils.isEmpty(couponCode.getOrderId())){
//                return new FebsResponse().fail().message("券码已过期！");
//            }
//            cfdtwCouponCodeService.lambdaUpdate()
//                    .eq(CfdtwCouponCode::getId, couponCode.getId())
//                    .set(CfdtwCouponCode::getStatus,status)
//                    .set(CfdtwCouponCode::getSendServer,gamePropResult.getSendServer())
//                    .set(CfdtwCouponCode::getSendRole,gamePropResult.getSendRole())
//                    .set(CfdtwCouponCode::getSendTime,gamePropResult.getUseTime())
//                    .set(CfdtwCouponCode::getUpdateTime,new Date())
//                    .update();
//            orderId=couponCode.getOrderId();
//            extrInfo=couponCode.getExtrInfo();
//        }else if(WZWD_NAME.equals(gamePropResult.getGameName())){
//            //查询未使用的券码
//            WzwdCouponCode couponCode=wzwdCouponCodeService.lambdaQuery()
//                    .select(WzwdCouponCode::getId,WzwdCouponCode::getOrderId,WzwdCouponCode::getExtrInfo)
//                    .eq(WzwdCouponCode::getCouponCode,gamePropResult.getCouponCode())
//                    .eq(WzwdCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
//                    .ge(WzwdCouponCode::getInvalidTime,gamePropResult.getUseTime())
//                    .orderByDesc(WzwdCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//            if(couponCode==null || StringUtils.isEmpty(couponCode.getOrderId())){
//                return new FebsResponse().fail().message("券码已过期！");
//            }
//            wzwdCouponCodeService.lambdaUpdate()
//                    .eq(WzwdCouponCode::getId, couponCode.getId())
//                    .set(WzwdCouponCode::getStatus,status)
//                    .set(WzwdCouponCode::getSendServer,gamePropResult.getSendServer())
//                    .set(WzwdCouponCode::getSendRole,gamePropResult.getSendRole())
//                    .set(WzwdCouponCode::getSendTime,gamePropResult.getUseTime())
//                    .set(WzwdCouponCode::getUpdateTime,new Date())
//                    .update();
//            orderId=couponCode.getOrderId();
//            extrInfo=couponCode.getExtrInfo();
//        }else if(SJCS_NAME.equals(gamePropResult.getGameName())){
//            //查询未使用的券码
//            SjcsCouponCode couponCode=sjscCouponCodeService.lambdaQuery()
//                    .select(SjcsCouponCode::getId,SjcsCouponCode::getOrderId,SjcsCouponCode::getExtrInfo)
//                    .eq(SjcsCouponCode::getCouponCode,gamePropResult.getCouponCode())
//                    .eq(SjcsCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
//                    .ge(SjcsCouponCode::getInvalidTime,gamePropResult.getUseTime())
//                    .orderByDesc(SjcsCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//            if(couponCode==null || StringUtils.isEmpty(couponCode.getOrderId())){
//                return new FebsResponse().fail().message("券码已过期！");
//            }
//            sjscCouponCodeService.lambdaUpdate()
//                    .eq(SjcsCouponCode::getId, couponCode.getId())
//                    .set(SjcsCouponCode::getStatus,status)
//                    .set(SjcsCouponCode::getSendServer,gamePropResult.getSendServer())
//                    .set(SjcsCouponCode::getSendRole,gamePropResult.getSendRole())
//                    .set(SjcsCouponCode::getSendTime,gamePropResult.getUseTime())
//                    .set(SjcsCouponCode::getUpdateTime,new Date())
//                    .update();
//            orderId=couponCode.getOrderId();
//            extrInfo=couponCode.getExtrInfo();
//        }else if(AHZZ_NAME.equals(gamePropResult.getGameName())){
//            //查询未使用的券码
//            AhzzCouponCode couponCode=ahzzCouponCodeService.lambdaQuery()
//                    .select(AhzzCouponCode::getId,AhzzCouponCode::getOrderId,AhzzCouponCode::getExtrInfo)
//                    .eq(AhzzCouponCode::getCouponCode,gamePropResult.getCouponCode())
//                    .eq(AhzzCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
//                    .ge(AhzzCouponCode::getInvalidTime,gamePropResult.getUseTime())
//                    .orderByDesc(AhzzCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//            if(couponCode==null || StringUtils.isEmpty(couponCode.getOrderId())){
//                return new FebsResponse().fail().message("券码已过期！");
//            }
//            ahzzCouponCodeService.lambdaUpdate()
//                    .eq(AhzzCouponCode::getId, couponCode.getId())
//                    .set(AhzzCouponCode::getStatus,status)
//                    .set(AhzzCouponCode::getSendServer,gamePropResult.getSendServer())
//                    .set(AhzzCouponCode::getSendRole,gamePropResult.getSendRole())
//                    .set(AhzzCouponCode::getSendTime,gamePropResult.getUseTime())
//                    .set(AhzzCouponCode::getUpdateTime,new Date())
//                    .update();
//            orderId=couponCode.getOrderId();
//            extrInfo=couponCode.getExtrInfo();
//        }else{
//            //查询未使用的券码
//            GameSendCouponCode couponCode=sendCodeService.lambdaQuery()
//                    .select(GameSendCouponCode::getId,GameSendCouponCode::getOrderId,GameSendCouponCode::getExtrInfo)
//                    .eq(GameSendCouponCode::getCouponCode,gamePropResult.getCouponCode())
//                    .eq(GameSendCouponCode::getStatus,BizConstant.RECHARGE_WAIT)
//                    .ge(GameSendCouponCode::getInvalidTime,gamePropResult.getUseTime())
//                    .orderByDesc(GameSendCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
//            if(couponCode==null || StringUtils.isEmpty(couponCode.getOrderId())){
//                return new FebsResponse().fail().message("券码已过期！");
//            }
//            sendCodeService.lambdaUpdate()
//                    .eq(GameSendCouponCode::getId, couponCode.getId())
//                    .set(GameSendCouponCode::getStatus,status)
//                    .set(GameSendCouponCode::getSendServer,gamePropResult.getSendServer())
//                    .set(GameSendCouponCode::getSendRole,gamePropResult.getSendRole())
//                    .set(GameSendCouponCode::getSendTime,gamePropResult.getUseTime())
//                    .set(GameSendCouponCode::getUpdateTime,new Date())
//                    .update();
//            orderId=couponCode.getOrderId();
//            extrInfo=couponCode.getExtrInfo();
//        }
        if(StringUtils.isEmpty(orderId)){
            return new FebsResponse().fail().message("券码已过期！");
        }
        Integer chargeCount=this.lambdaQuery().eq(JunboChargeLog::getMiguOrderId,orderId).eq(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING).count();
        if(chargeCount<=0){
            return new FebsResponse().fail().message("订单不存在！");
        }
        this.lambdaUpdate()
                .eq(JunboChargeLog::getMiguOrderId,orderId)
                .set(JunboChargeLog::getStatus, gamePropResult.getStatus())
                .set(JunboChargeLog::getCallbackMsg,gamePropResult.getCallbackMsg())
                .set(JunboChargeLog::getUpdateTime,new Date())
                .update();

        if(gamePropResult.getStatus().equals("1")){
            ObjectNode dataNode =mapper.createObjectNode();
            String timestamp= String.valueOf(System.currentTimeMillis());
//            final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(gamePropResult.getGameName());
            GameConfig gameConfig=gameConfigService.lambdaQuery().eq(GameConfig::getGameName,gamePropResult.getGameName()).orderByDesc(GameConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            String key=/*otherRecharge != null?otherRecharge.getKey():*/gameConfig.getSignKey();
            String receivebackUrl=/*otherRecharge != null?otherRecharge.getReceivebackUrl():*/gameConfig.getReceivebackUrl();
            String name=/*otherRecharge != null?otherRecharge.getName():*/gameConfig.getLogTag();
            String sign=getSign(orderId,timestamp,key);
            dataNode.put("orderId",orderId);
            dataNode.put("receiveTime",timestamp);
            dataNode.put("timestamp",timestamp);
            dataNode.put("extrInfo",extrInfo);
            dataNode.put("sendServer",gamePropResult.getSendServer());
            dataNode.put("sendRole",gamePropResult.getSendRole());
            this.implementHttpPostResult(receivebackUrl,dataNode.toString(),name+"通知咪咕互娱道具使用状态",sign);
        }
        return new FebsResponse().success().message("成功");
    }

    @Override
    public MiGuHuYuNotifyResp miguPropRecharge(MiGuHuYuResult miGuHuYuResult/*,Map<String, Object> map*/) throws Exception {
       return miGuHuYouRecharge(miGuHuYuResult.getOrderId(),miGuHuYuResult.getUserId(),miGuHuYuResult.getPhoneNum(),miGuHuYuResult.getProductCode(),miGuHuYuResult.getExtrInfo()/*,map*/);
    }


    private MiGuHuYuNotifyResp miGuHuYouRecharge(String orderId, String userId, String phoneNum, String productCode, String extrInfo/*,Map<String, Object> map*/) throws Exception {
        //查询业务包
        MiguPack miguPack=miguPackService.lambdaQuery().select(MiguPack::getRechargeDelayMinute).eq(MiguPack::getServiceId, productCode).last(BizConstant.SQL_LIMIT_ONE).one();
        if(miguPack==null){
            log.warn("咪咕互娱权益下发通知-业务未配置-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{}", "hetu",orderId, userId, phoneNum, productCode, extrInfo);
            return MiGuHuYuNotifyResp.error("业务未配置");
        }
        Optional<RightsPackDto> serviceOptional=  rightsPackService.wholeMemberRightsList(productCode).stream().max(Comparator.comparing(RightsPackDto::getId));
        if(!serviceOptional.isPresent()){
            log.warn("咪咕互娱权益下发通知-业务未关联-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{}", "hetu",orderId, userId, phoneNum, productCode, extrInfo);
            return MiGuHuYuNotifyResp.error("业务未关联");
        }
        RightsPackDto service=serviceOptional.get();
        Optional<RightsPackList> productOptional=service.getRightsPackList().stream().max(Comparator.comparing(RightsPackList::getRightsId));
        if(!productOptional.isPresent()){
            log.warn("咪咕互娱权益下发通知-产品未配置-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{}", "hetu",orderId, userId, phoneNum, productCode, extrInfo);
            return MiGuHuYuNotifyResp.error("产品未配置");
        }
        //按订单号同步处理
        synchronized (huyuOrderIdInterner.intern(orderId)) {
            //限制同一个订单号只能发送一次
            boolean orderRecharge = this.lambdaQuery()
                    .eq(JunboChargeLog::getMiguOrderId, orderId)
                    .eq(JunboChargeLog::getServiceId,productCode)
                    .count()>0;
            if(orderRecharge){
                log.warn("咪咕互娱权益下发通知-订单号重复-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{}", "hetu",orderId, userId, phoneNum, productCode, extrInfo);
                return MiGuHuYuNotifyResp.error("订单号重复");
            }

            //同一用户每月只能发放券码一次
            boolean rechargeSuccess= this.lambdaQuery()
                    .eq(JunboChargeLog::getAccount, userId)
                    .eq(JunboChargeLog::getRightsMonth,DateUtil.formatYearMonth(LocalDateTime.now()))
                    .in(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS)
                    .eq(JunboChargeLog::getServiceId,productCode)
                    .count()>0;

            if(rechargeSuccess){
                log.warn("咪咕互娱权益下发通知-当月已发放券码-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{}", "hetu",orderId, userId, phoneNum, productCode, extrInfo);
                return MiGuHuYuNotifyResp.error("当月已发放券码");
            }

            RightsPackList product=productOptional.get();
            LocalDateTime effectTime=LocalDateTime.now().plusMinutes(miguPack.getRechargeDelayMinute());

            String mobile = MiGuHuYuResult.decodeUserId2Mobile(userId);
            mobile = StringUtils.isNotEmpty(mobile)? mobile:userId;
            String channel=null;
            String subChannel=null;
            List<Subscribe> subscribeList=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                    .eq(Subscribe::getBizType, BIZ_TYPE_HETU)
                    .between(Subscribe::getCreateTime,LocalDateTime.now().minusHours(24),LocalDateTime.now())
                    .in(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS,BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED)
                    .orderByDesc(Subscribe::getCreateTime).list();
            if(subscribeList!=null && subscribeList.size()>0){
                boolean isSuccess=subscribeList.stream().anyMatch(subscribe->BizConstant.SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()));
                if(isSuccess){
                    //查询最新订购数据
                    EsSubscribe esSubscribe =esDataService.findEsSubscribeByMobileAndBizType(mobile,BIZ_TYPE_HETU);
                    if(esSubscribe!=null){
                        channel=esSubscribe.getChannel();
                        subChannel=esSubscribe.getSubChannel();
                    }
                    log.warn("河图寻仙记,24小时已有开通成功订单忽略上报=>手机号:{},用户ID:{},产品ID:{}",mobile,userId,productCode);
                }else{
                    Optional<Subscribe> subscribeOptional=subscribeList.stream().filter(subscribe->BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())).findFirst();
                    if(subscribeOptional.isPresent()){
                        Subscribe subscribeSubmited=subscribeOptional.get();
                        String result = "业务开通成功";
                        Subscribe upd = new Subscribe();
                        upd.setId(subscribeSubmited.getId());
                        upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                        upd.setResult(result);
                        upd.setOpenTime(new Date());
                        upd.setModifyTime(new Date());
                        subscribeService.updateSubscribeDbAndEs(upd);
                        channel=subscribeSubmited.getChannel();
                        subChannel=subscribeSubmited.getSubChannel();
                        //加入包月延迟校验队列
                        if(miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(subscribeSubmited.getChannel())!=null) {
                            rabbitMQMsgSender.addDelayedVerifyMessage(subscribeSubmited);
                        }

                        subscribeService.saveChannelLimit(subscribeSubmited);
                        if (!outsideConfigService.isOutsideChannel(subscribeSubmited.getSubChannel())) {
                            channelService.AdEffectFeedbackNew(subscribeSubmited, SUBSCRIBE_STATUS_SUCCESS);
                            //外部渠道加入回调延迟队列(暂时不使用队列)
                        }else {
                            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribeSubmited.getId(), 0, "回调通知");
                            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                        }
                    }
                }
            }
            if(StringUtils.isBlank(channel)){
                //查询最新订购数据
                EsSubscribe esSubscribe =esDataService.findEsSubscribeByMobileAndBizType(mobile,BIZ_TYPE_HETU);
                if(esSubscribe!=null){
                    channel=esSubscribe.getChannel();
                    subChannel=esSubscribe.getSubChannel();
                }
            }


            if(StringUtils.isBlank(channel)){
                //查询互娱分省接口开通分省最新订购数据
                EsSubscribe chongQingEsSubscribe = esDataService.findEsSubscribeByMobileAndBizType(mobile, BIZ_TYPE_HETU_FENSHENG);
                if (chongQingEsSubscribe != null) {
                    channel = chongQingEsSubscribe.getChannel();
                    subChannel = chongQingEsSubscribe.getSubChannel();
                }
            }
            if(StringUtils.isBlank(channel)){
                //查询互娱分省接口开通最新提交验证码数据
                Subscribe chongQingSubscribeList = subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                        .eq(Subscribe::getBizType, BIZ_TYPE_HETU_FENSHENG)
                        .between(Subscribe::getCreateTime, LocalDateTime.now().minusHours(24), LocalDateTime.now())
                        .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED)
                        .orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if (chongQingSubscribeList != null) {
                    channel = chongQingSubscribeList.getChannel();
                    subChannel = chongQingSubscribeList.getSubChannel();
                }
            }
            final JunboChargeLog junboChargeLog = this.buildMiGuHuYouChargeLog(mobile,userId,orderId,productCode,product.getCouponId(),product.getRightsName(),product.getProductPrice(),null,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,product.getRechargeState(),DateUtil.localDateTimeToDate(effectTime),"咪咕互游",service.getPackName(),null,extrInfo,product.getCompanyOwner(),channel,subChannel);
            couponCodeService.sendCode(junboChargeLog);
            return MiGuHuYuNotifyResp.ok("正在发码");
        }
    }

    private JunboChargeLog buildMiGuHuYouChargeLog(String msisdn, String account, String orderId, String serviceId, String couponId, String couponName, Integer couponPrice, String rightsMonth, Integer status, Integer rechargeState, Date scheduledTime, String remark, String packName, Date payTime, String extrInfo, String companyOwnerName,String channel,String subChannel) {
        JunboChargeLog junboChargeLog = new JunboChargeLog();
        junboChargeLog.setMobile(msisdn);
        junboChargeLog.setAccount(account);
        junboChargeLog.setMiguOrderId(orderId);
        junboChargeLog.setJunboOrderId(extrInfo);
        junboChargeLog.setStatus(status);
        junboChargeLog.setScheduledTime(scheduledTime);
        junboChargeLog.setCouponId(couponId);
        junboChargeLog.setCouponName(couponName);
        junboChargeLog.setCouponPrice(couponPrice);
        junboChargeLog.setPackName(packName);
        junboChargeLog.setServiceId(serviceId);
        if(StringUtils.isBlank(rightsMonth)){
            junboChargeLog.setRightsMonth(DateUtil.formatYearMonth(LocalDateTime.now()));
        }else{
            junboChargeLog.setRightsMonth(rightsMonth);
        }
        junboChargeLog.setCreateTime(new Date());

        junboChargeLog.setRemark(remark);

        junboChargeLog.setRechargeState(rechargeState);
        junboChargeLog.setPayTime(payTime);
        junboChargeLog.setCompanyOwner(companyOwnerName);
        MobileRegionResult mobileRegionResult = mobileRegionService.query(msisdn);
        if(mobileRegionResult!=null) {
            junboChargeLog.setProvince(mobileRegionResult.getProvince());
            junboChargeLog.setCity(mobileRegionResult.getCity());
        }
        junboChargeLog.setChannel(channel);
        junboChargeLog.setSubChannel(subChannel);
        return junboChargeLog;
    }


    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url,String raw,String msg,String sign) {
        return push(url, raw,msg,sign);
    }
    public String push(String url,String raw,String msg,String sign) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).addHeader("sign",sign).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }

    /**
     * 签名
     * @param orderId
     * @param timestamp
     * @param key
     * @return
     */
    private String getSign(String orderId,String timestamp,String key) {
        String parameterStr = orderId+timestamp+key;
        try {
            String sign = DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8.name()));
            return sign;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.error("咪咕互娱道具下发通知,加密参数:{}",parameterStr,e);
        }
        return null;
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_WOREAD_JUNBO_CHARGE_LIST,key = " #p0 + ':' + #p1",unless = "#result==null")
    public List <WoReadJunboChargeLogDto> woReadQueryChargeList(String mobile, String date){
        WoReadQueryDto dto=new WoReadQueryDto();
        dto.setMobile(mobile);
        dto.setDate(date);
        List <WoReadJunboChargeLogDto> list=this.baseMapper.findByMobile(dto);
        list.forEach(woReadJunboChargeLogDto->{
                woReadJunboChargeLogDto.setCreatetime(DateUtil.getDateFormat(woReadJunboChargeLogDto.getCreatetimes(),DateUtil.FULL_TIME_PATTERN));
                woReadJunboChargeLogDto.setStatus("出货成功");
                woReadJunboChargeLogDto.setCreatetimes(null);
        });
        return list;
    }
    @Override
    public JunboChargeLog aliPayVrbtChargeLog(String msisdn,String account,String orderId,String junboOrderId, String serviceId,String couponId, String couponName,Integer couponPrice,String rightsMonth, Integer status,Integer rechargeState, Date scheduledTime, String remark,String packName,Date payTime,String companyOwnerName) {
        JunboChargeLog junboChargeLog = new JunboChargeLog();
        junboChargeLog.setMobile(msisdn);
        junboChargeLog.setAccount(account);
        junboChargeLog.setMiguOrderId(orderId);
        junboChargeLog.setJunboOrderId(junboOrderId);
        junboChargeLog.setStatus(status);
        junboChargeLog.setScheduledTime(scheduledTime);
        junboChargeLog.setCouponId(couponId);
        junboChargeLog.setCouponName(couponName);
        junboChargeLog.setCouponPrice(couponPrice);
        junboChargeLog.setPackName(packName);
        junboChargeLog.setServiceId(serviceId);
        if(StringUtils.isBlank(rightsMonth)){
            junboChargeLog.setRightsMonth(DateUtil.formatYearMonth(LocalDateTime.now()));
        }else{
            junboChargeLog.setRightsMonth(rightsMonth);
        }
        junboChargeLog.setCreateTime(new Date());

        junboChargeLog.setRemark(remark);

        junboChargeLog.setRechargeState(rechargeState);
        junboChargeLog.setPayTime(payTime);
        junboChargeLog.setCompanyOwner(companyOwnerName);
        MobileRegionResult mobileRegionResult = mobileRegionService.query(msisdn);
        if(mobileRegionResult!=null) {
            junboChargeLog.setProvince(mobileRegionResult.getProvince());
            junboChargeLog.setCity(mobileRegionResult.getCity());
        }
        junboChargeLogService.save(junboChargeLog);
        return junboChargeLog;
    }


    /**
     * 接收华逸回调通知,根据通知更新直充记录
     *
     * orderSEQ是我方的订单号,orderId是骏伯下单后返回的订单号
     * code 0=充值成功
     *  通知充值状态，000000为充值成功，000004充值失败。或者其它异常状态
     */
    @Override
    public void receiveRechargeNotify(HuaYiResult huaYiResult) {
        final String code = huaYiResult.getStatus();
        Integer status = "000000".equals(code) ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        this.lambdaUpdate()
                .eq(JunboChargeLog::getJunboOrderId, huaYiResult.getSysOrderId())
                .set(JunboChargeLog::getStatus, status)
                .set(JunboChargeLog::getCallbackCode, huaYiResult.getStatus())
                .set(JunboChargeLog::getCallbackMsg, huaYiResult.getMsg())
                .set(JunboChargeLog::getUpdateTime,new Date())
                .update();
        //设置天翼空间视听会员权益发送状态
        setVideoSendStatusHuaYi(huaYiResult.getSysOrderId(), status,huaYiResult.getMsg());

        updateAlipayRightsStatusHuaYi(huaYiResult.getSysOrderId(), status);

//        JunboChargeLog junboChargeLog=this.lambdaQuery().eq(JunboChargeLog::getJunboOrderId, orderSEQ).one();
//        if(junboChargeLog!=null){
//            updateRechargeState(junboChargeLog);
//        }
    }


    private void setVideoSendStatusHuaYi(String orderSEQ, Integer status,String msg) {
        JunboChargeLog junboChargeLog=this.lambdaQuery().eq(JunboChargeLog::getJunboOrderId, orderSEQ).one();
        if(junboChargeLog==null){
            log.error("权益订单查询失败,订单号:{},状态:{},描述:{}",orderSEQ,status,msg);
            return;
        }
        Optional<TelecomOrder> telecomOrderOpt=telecomOrderService.lambdaQuery()
                .in(TelecomOrder::getStatus, BizConstant.TYKJ_TYPE_BOOK, BizConstant.TYKJ_TYPE_RENEW)
                .in(TelecomOrder::getType,   BizConstant.TYKJ_TYPE_BOOK, BizConstant.TYKJ_TYPE_RENEW)
                .eq(TelecomOrder::getPhone,junboChargeLog.getMobile())
                .list()
                .stream()
                .max(Comparator.comparing(TelecomOrder::getCreateTime));
        if(telecomOrderOpt.isPresent()){
            TelecomOrder telecomOrder = telecomOrderOpt.get();
            telecomOrderService.lambdaUpdate()
                    .set(TelecomOrder::getVideoSendStatus, status ==1?BizConstant.TYKJ_BDWP_SUCCEED:BizConstant.TYKJ_BDWP_FAIL)
                    .eq(TelecomOrder::getId,telecomOrder.getId())
                    .update();
        }
        //话费直充记录更新状态
        mobileFeeChargeLogService.lambdaUpdate()
                .eq(MobileFeeChargeLog::getOrderId,orderSEQ)
                .set(MobileFeeChargeLog::getStatus,status)
                .set(MobileFeeChargeLog::getRemark,msg)
                .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();

    }

    /**
     * 更新支付订单领取权益状态
     * @param orderNo
     * @param status
     */
    private void updateAlipayRightsStatusHuaYi(String orderNo, Integer status) {
        JunboChargeLog junboChargeLog=this.lambdaQuery().eq(JunboChargeLog::getJunboOrderId, orderNo).orderByDesc(JunboChargeLog::getCreateTime).last("limit 1").one();
        if(junboChargeLog==null){
            log.error("权益订单查询失败,订单号:{},状态:{}",orderNo,status);
            return;
        }

        //支付宝权益充值不更新状态
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(junboChargeLog.getRemark());
        if(otherRecharge!=null){
            return;
        }
        //支付宝订单与其他业务查询不同
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(junboChargeLog.getServiceId());
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(junboChargeLog.getServiceId());
        if(rightBusinessEnum==null && miguPacks!=null && !miguPacks.isEmpty()){
            //查询最新支付订单
            AliSignChargingOrder orderPay=aliSignChargingOrderService.lambdaQuery()
                    .eq(AliSignChargingOrder::getMobile, junboChargeLog.getMobile())
                    .eq(AliSignChargingOrder::getBusinessType, junboChargeLog.getServiceId())
                    .eq(AliSignChargingOrder::getOrderStatus, 1)
                    .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
            if(orderPay!=null){
                aliSignChargingOrderService.lambdaUpdate().eq(AliSignChargingOrder::getId, orderPay.getId()).set(AliSignChargingOrder::getRightsStatus,status).update();
            }
        }
    }


    /**
     * 判断当月是否领取视听会员权益
     * @param mobile
     * @param packNameList
     * @return
     */
    @Override
    public List<JunboChargeLog> findByMobileAndPackNameListAndMonth(String mobile,List<String> packNameList,Long days){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .between(JunboChargeLog::getPayTime, LocalDateTime.now().minusDays(days),LocalDateTime.now())
                .notIn(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_FAIL,BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY)
                .in(JunboChargeLog::getPackName,packNameList).list();
    }


    /**
     * 判断商城订单是否领取视听会员权益
     * @param orderId
     * @param packNameList
     * @return
     */
    @Override
    public List<JunboChargeLog> findByOrderIdAndPackNameList(String orderId,List<String> packNameList){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMiguOrderId,orderId)
                .notIn(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_FAIL,BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY)
                .in(JunboChargeLog::getPackName,packNameList).list();
    }


    /**
     * 判断商城订单是否领取视听会员权益
     * @param orderId
     * @param packName
     * @return
     */
    @Override
    public List<JunboChargeLog> findByOrderIdAndPackName(String orderId,String packName){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMiguOrderId,orderId)
                .notIn(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_FAIL,BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY)
                .eq(JunboChargeLog::getPackName,packName).list();
    }
    @Override
    public JunboChargeLog taskRechargeForSchedule(JunboChargeLog junboChargeLog) {
        String account = junboChargeLog.getAccount();
        String mobile=junboChargeLog.getMobile();
        //券码充值
        if(StringUtils.equals(String.valueOf(junboChargeLog.getRechargeState()),String.valueOf(BizConstant.RECHARGE_STATE_CODE))){
            this.couponCodeCharge(junboChargeLog, mobile);
            return junboChargeLog;
        }
        //白金会员权益（mgyybjhy）
        if(BizConstant.RECHARGE_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res=miguApiService.bjhyOrder(mobile, BizConstant.RECHARGE_BJHY_CHANNEL_ID);
            this.updateJunboChargeBJHYStatus(junboChargeLog, res);
            return junboChargeLog;
        }
        //酷狗VIP月卡直充（kgykzhichong）
        if(BizConstant.KUGOU_COUPON_ID.equals(junboChargeLog.getCouponId())){
            KugouOrderResult res=kugouApiService.order(junboChargeLog.getMiguOrderId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName(),junboChargeLog.getChannel());
            this.updateJunboChargeKGStatus(junboChargeLog, res);
            return junboChargeLog;
        }
        //咪咕视频彩铃直充（mgyyvrbt）
        if(BizConstant.VRBT_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res = miguApiService.vrbtZeroOrder(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID,1);
            this.updateJunboChargeMGSPCLStatus(junboChargeLog, res);
            return junboChargeLog;
        }
        //微信代金券直充（wechatdjq）
        if(BizConstant.WCDJQ_COUPON_ID.equals(junboChargeLog.getCouponId())){
            WechatCouponConfig wechatCouponConfig=wechatCouponConfigService.queryEffectiveWechatCouponConfig(junboChargeLog.getChannel());
            if(wechatCouponConfig!=null){
                boolean res=wxpayService.sendWeiXinCode(junboChargeLog.getAccount(),junboChargeLog.getMiguOrderId(),wechatCouponConfig);
                this.updateJunboChargeWXDJQStatus(junboChargeLog, res);
                return junboChargeLog;
            }else{
                this.updateJunboChargeWXDJQStatus(junboChargeLog, false);
                return junboChargeLog;
            }
        }
        if(BizConstant.COMPANY_OWNER_TUNIU.equals(junboChargeLog.getCompanyOwner())){
            Boolean rechargeStatus=false;
            String respCode=null;
            String respMsg=null;
            for(int i=0;i<BizConstant.PRODUCT_ID_LIST.size();i++){
                FebsResponse febs=unifyRightsFeignClient.tuniuSendRights(mobile,BizConstant.PRODUCT_ID_LIST.get(i));
                if(febs.isOK()){
                    rechargeStatus=true;
                }else{
                    respMsg=String.valueOf(febs.get("message"));
                    respCode=String.valueOf(febs.get("code"));
                }
            }
            junboChargeLog.setRespCode(respCode);
            junboChargeLog.setRespMsg(respMsg);
            junboChargeLog.setUpdateTime(new Date());
            if(rechargeStatus){
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS);
            }else{
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            }
            this.updateById(junboChargeLog);
            return junboChargeLog;
        }

        if(BizConstant.COMPANY_OWNER_HUAYI.equals(junboChargeLog.getCompanyOwner())){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            this.updateById(junboChargeLog);
            return junboChargeLog;
        }

        //开始下单充值
//        final JunboResult junboResult = junboApiService.rechargeVIP(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
        if(null==junboRespon){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("骏伯多会员下单接口异常");
            junboChargeLog.setUpdateTime(new Date());
            this.updateById(junboChargeLog);
            return junboChargeLog;
        }
        final String resultCode = junboRespon.getCode();
        junboChargeLog.setRespCode(resultCode);
        junboChargeLog.setRespMsg(junboRespon.getMsg());
//        junboChargeLog.setJunboOrderId(junboResult.getOrderId());
        junboChargeLog.setUpdateTime(new Date());

        //{"T":*************,"S":"100","F":"1349270325406330882","C":{"code":"100","msg":"接收成功","orderId":"20210113162129294300014"}}
        //{"T":*************,"S":"100","F":"1349273995275464705","C":{"code":"1025","msg":"充值号码充值次数超过当日限制次数","orderId":null}}
        //除开100,都视为充值失败,100表示正在充值中
        int status = "0".equals(resultCode) ? BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        junboChargeLog.setStatus(status);
        this.updateById(junboChargeLog);
        return junboChargeLog;
    }

    /**
     * 更新业务充值状态
     * @param junboChargeLog
     */
    public void updateRechargeState(JunboChargeLog junboChargeLog)  {
        log.info("更新业务充值状态=>手机号:{},权益领取业务ID:{},充值状态:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus());
        try {
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, junboChargeLog.getServiceId()).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.info("更新业务充值状态业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},充值状态:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus());
                return;
            }
            final String serviceApiBeanName =businessPack.getServiceApiBeanName();
            log.info("更新业务充值状态ApiBean配置=>手机号:{},权益领取业务ID:{},充值状态:{},ApiBean:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus(),serviceApiBeanName);
            if(StringUtils.isNotBlank(serviceApiBeanName)){
                final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessRightsSubService.class);
                businessRightsSubService.updateRechargeState(junboChargeLog);
            }
        } catch (Exception e) {
            log.error("更新业务充值状态异常=>手机号:{},权益领取业务ID:{},充值状态:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus(),e);
        }
    }


    @Override
    public List<JunboChargeLog> queryRechargeList(String mobile) {
        LambdaQueryWrapper<JunboChargeLog> queryWrapper = this.buildFindLambdaQueryWrapper(mobile);
        queryWrapper.orderByDesc(JunboChargeLog::getCreateTime);
        queryWrapper.select(JunboChargeLog::getAccount,JunboChargeLog::getMobile,JunboChargeLog::getCreateTime,JunboChargeLog::getScheduledTime,JunboChargeLog::getStatus,JunboChargeLog::getCouponName,JunboChargeLog::getRightsMonth,JunboChargeLog::getPackName);
        List<JunboChargeLog> junboChargeLogList=this.baseMapper.selectList(queryWrapper);
        junboChargeLogList.forEach(item -> {
            MiguPack miguPack=miguPackService.lambdaQuery().eq(MiguPack::getServiceId,item.getServiceId()).select(MiguPack::getTitleName).orderByDesc(MiguPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            item.setServiceName(miguPack!=null?miguPack.getTitleName():"");
        });
        return junboChargeLogList;
    }


    @NotNull
    public LambdaQueryWrapper<JunboChargeLog> buildFindLambdaQueryWrapper(String mobile) {
        LambdaQueryWrapper<JunboChargeLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(mobile)) {
            queryWrapper.eq(JunboChargeLog::getMobile,mobile);
        }
        queryWrapper.in(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS,BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
        return queryWrapper;
    }


    /**
     * 新权益充值定时任务
     * @param junboChargeLog
     */
    @Override
    public void newRechargeForSchedule(JunboChargeLog junboChargeLog) {
        log.info("新权益充值定时任务=>手机号:{},权益领取业务ID:{},充值状态:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus());
        try {
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, junboChargeLog.getServiceId()).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.info("新权益充值定时任务业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},充值状态:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus());
                return;
            }
            final String serviceApiBeanName =businessPack.getServiceApiBeanName();
            log.info("新权益充值定时任务ApiBean配置=>手机号:{},权益领取业务ID:{},充值状态:{},ApiBean:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus(),serviceApiBeanName);
            if(StringUtils.isNotBlank(serviceApiBeanName)){
                final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessRightsSubService.class);
                FebsResponse febsResponse=businessRightsSubService.rechargeForScheduleVerify(junboChargeLog);
                if(febsResponse.isOK()){
                    businessRightsSubService.rechargeForSchedule(junboChargeLog);
                }
            }
        } catch (Exception e) {
            log.error("新权益充值定时任务异常=>手机号:{},权益领取业务ID:{},充值状态:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus(),e);
        }
    }


    @Override
    public MiGuHuYuNotifyResp miGuHuYuSendCoupon(MiGuHuYuResult miGuHuYuResult, HttpServletRequest request, String gameName) throws Exception {
//        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(gameName);
        GameConfig gameConfig=gameConfigService.lambdaQuery().eq(GameConfig::getGameName,gameName).orderByDesc(GameConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if (/*otherRecharge == null && */gameConfig==null) {
            log.warn("咪咕互娱权益下发通知-业务渠道未配置-game:{},手机号:{},body:{}", gameName, MiGuHuYuResult.decodeUserId2Mobile(miGuHuYuResult.getUserId()), miGuHuYuResult);
            return MiGuHuYuNotifyResp.error("业务渠道未配置");
        }
        String key=/*otherRecharge != null?otherRecharge.getKey():*/gameConfig.getSignKey();
        String mobileKey=/*otherRecharge != null?otherRecharge.getMobileKey():*/gameConfig.getMobileKey();
        String bizType=/*otherRecharge != null?otherRecharge.getBizType():*/gameConfig.getBizType();
        String sign = request.getHeader("sign");
        if (!checkSign(sign, miGuHuYuResult.getOrderId(), miGuHuYuResult.getUserId(), miGuHuYuResult.getCreateTime(), key)) {
            log.warn("咪咕互娱权益下发通知-签名验证失败-game:{},手机号:{},body:{}", gameName, MiGuHuYuResult.decodeUserId2Mobile(miGuHuYuResult.getUserId()), miGuHuYuResult);
            return MiGuHuYuNotifyResp.error("签名验证失败");
        }
        if(StringUtils.isNotBlank(miGuHuYuResult.getPhoneNum())){
            miGuHuYuResult.setPhoneNum(this.getPhone(miGuHuYuResult.getPhoneNum(),mobileKey));
        }
        return miGuHuYuRecharge(miGuHuYuResult.getOrderId(),miGuHuYuResult.getUserId(),miGuHuYuResult.getPhoneNum(),miGuHuYuResult.getProductCode(),miGuHuYuResult.getExtrInfo()/*,map*/,gameName,bizType);
    }



    private MiGuHuYuNotifyResp miGuHuYuRecharge(String orderId, String userId, String phoneNum, String productCode, String extrInfo/*,Map<String, Object> map*/, String gameName, String bizType) throws Exception {
        //查询业务包
        MiguPack miguPack=miguPackService.lambdaQuery().select(MiguPack::getRechargeDelayMinute).eq(MiguPack::getServiceId, productCode).last(BizConstant.SQL_LIMIT_ONE).one();
        if(miguPack==null){
            log.warn("咪咕互娱权益下发通知-业务未配置-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{},业务类型:{}", gameName, orderId,userId,phoneNum,productCode,extrInfo,bizType);
            return MiGuHuYuNotifyResp.error("业务未配置");
        }
        Optional<RightsPackDto> serviceOptional=  rightsPackService.wholeMemberRightsList(productCode).stream().max(Comparator.comparing(RightsPackDto::getId));
        if(serviceOptional==null || !serviceOptional.isPresent()){
            log.warn("咪咕互娱权益下发通知-业务未关联-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{},业务类型:{}", gameName, orderId,userId,phoneNum,productCode,extrInfo,bizType);
            return MiGuHuYuNotifyResp.error("业务未关联");
        }
        RightsPackDto service=serviceOptional.get();
        Optional<RightsPackList> productOptional=service.getRightsPackList().stream().max(Comparator.comparing(RightsPackList::getRightsId));
        if(productOptional==null || !productOptional.isPresent()){
            log.warn("咪咕互娱权益下发通知-产品未配置-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{},业务类型:{}", gameName, orderId,userId,phoneNum,productCode,extrInfo,bizType);
            return MiGuHuYuNotifyResp.error("产品未配置");
        }
        //按订单号同步处理
        synchronized (huyuOrderIdInterner.intern(orderId)) {
            boolean orderRecharge = this.lambdaQuery()
                    .eq(JunboChargeLog::getMiguOrderId, orderId)
                    .eq(JunboChargeLog::getServiceId,productCode)
                    .count()>0;
            if(orderRecharge){
                log.warn("咪咕互娱权益下发通知-订单号重复-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{},业务类型:{}", gameName, orderId,userId,phoneNum,productCode,extrInfo,bizType);
                return MiGuHuYuNotifyResp.error("订单号重复");
            }
            //同一用户每月只能发放券码一次
            boolean rechargeSuccess= this.lambdaQuery()
                    .eq(JunboChargeLog::getAccount, userId)
                    .eq(JunboChargeLog::getRightsMonth,DateUtil.formatYearMonth(LocalDateTime.now()))
                    .in(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS)
                    .eq(JunboChargeLog::getServiceId,productCode)
                    .count()>0;
            if(rechargeSuccess){
                log.warn("咪咕互娱权益下发通知-当月已发放券码-game:{},订单号:{},用户ID:{},手机号:{},产品ID:{},透传参数:{},业务类型:{}", gameName, orderId,userId,phoneNum,productCode,extrInfo,bizType);
                return MiGuHuYuNotifyResp.error("当月已发放券码");
            }
            RightsPackList product=productOptional.get();
            LocalDateTime effectTime=LocalDateTime.now().plusMinutes(miguPack.getRechargeDelayMinute());

            String mobile = StringUtils.isNotEmpty(phoneNum)?phoneNum:MiGuHuYuResult.decodeUserId2Mobile(userId);
            mobile = StringUtils.isNotEmpty(mobile)? mobile:userId;
            String channel=null;
            String subChannel=null;
            List<Subscribe> subscribeList=subscribeService.lambdaQuery().eq(Subscribe::getMobile, phoneNum)
                    .eq(Subscribe::getBizType, bizType)
                    .between(Subscribe::getCreateTime,LocalDateTime.now().minusHours(24),LocalDateTime.now())
                    .in(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS,BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED)
                    .orderByDesc(Subscribe::getCreateTime).list();
            if(subscribeList!=null && subscribeList.size()>0){
                boolean isSuccess=subscribeList.stream().anyMatch(subscribe->BizConstant.SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()));
                if(isSuccess){
                    //查询最新订购数据
                    EsSubscribe esSubscribe =esDataService.findEsSubscribeByMobileAndBizType(mobile,bizType);
                    if(esSubscribe!=null){
                        channel=esSubscribe.getChannel();
                        subChannel=esSubscribe.getSubChannel();
                    }
                    log.warn("咪咕互娱,24小时已有开通成功订单忽略上报=>手机号:{},用户ID:{},产品ID:{}",mobile,userId,productCode);
                }else{
                    Optional<Subscribe> subscribeOptional=subscribeList.stream().filter(subscribe->BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())).findFirst();
                    if(subscribeOptional.isPresent()){
                        Subscribe subscribeSubmited=subscribeOptional.get();
                        String result = "业务开通成功";
                        Subscribe upd = new Subscribe();
                        upd.setId(subscribeSubmited.getId());
                        upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                        upd.setResult(result);
                        upd.setOpenTime(new Date());
                        upd.setModifyTime(new Date());
                        subscribeService.updateSubscribeDbAndEs(upd);
                        subscribeService.saveChannelLimit(subscribeSubmited);
                        channel=subscribeSubmited.getChannel();
                        subChannel=subscribeSubmited.getSubChannel();
                        //加入包月延迟校验队列
                        if(miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(subscribeSubmited.getChannel())!=null) {
                            rabbitMQMsgSender.addDelayedVerifyMessage(subscribeSubmited);
                        }
                        if (!outsideConfigService.isOutsideChannel(subscribeSubmited.getSubChannel())) {
                            channelService.AdEffectFeedbackNew(subscribeSubmited, SUBSCRIBE_STATUS_SUCCESS);
                        }else{
                            //外部渠道加入回调延迟队列(暂时不使用队列)
                            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribeSubmited.getId(), 0, "回调通知");
                            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                        }
                    }
                }
            }else{
                //查询最新订购数据
                EsSubscribe esSubscribe =esDataService.findEsSubscribeByMobileAndBizType(mobile,bizType);
                if(esSubscribe!=null){
                    channel=esSubscribe.getChannel();
                    subChannel=esSubscribe.getSubChannel();
                }
            }
            final JunboChargeLog junboChargeLog = this.buildMiGuHuYouChargeLog(mobile,userId,orderId,productCode,product.getCouponId(),product.getRightsName(),product.getProductPrice(),null,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,product.getRechargeState(),DateUtil.localDateTimeToDate(effectTime),"咪咕互游",service.getPackName(),null,extrInfo,product.getCompanyOwner(),channel,subChannel);
            switch (gameName){
                case SWFZ_NAME:
                    couponCodeService.swfzSendCode(junboChargeLog);
                    return MiGuHuYuNotifyResp.ok("正在发码");
                case DYXC_NAME:
                    couponCodeService.dyxcSendCode(junboChargeLog);
                    return MiGuHuYuNotifyResp.ok("正在发码");
                case CFDTW_NAME:
                    couponCodeService.cfdtwSendCode(junboChargeLog);
                    return MiGuHuYuNotifyResp.ok("正在发码");
                case WZWD_NAME:
                    couponCodeService.wzwdSendCode(junboChargeLog);
                    return MiGuHuYuNotifyResp.ok("正在发码");
                case DUANJU_NAME:
                    couponCodeService.duanjuSendCode(junboChargeLog);
                    return MiGuHuYuNotifyResp.ok("充值中");
                case SJCS_NAME:
                    couponCodeService.sjcsSendCode(junboChargeLog);
                    return MiGuHuYuNotifyResp.ok("正在发码");
                case AHZZ_NAME:
                    couponCodeService.ahzzSendCode(junboChargeLog);
                    return MiGuHuYuNotifyResp.ok("正在发码");
                default:
                    couponCodeService.gameSendCode(junboChargeLog,gameName);
                    return MiGuHuYuNotifyResp.ok("正在发码");
            }

//            log.error("咪咕互娱权益代码未配置-手机号:{},产品编码:{},游戏标识:{}",mobile,productCode,gameName);
//            return MiGuHuYuNotifyResp.ok("系统错误");
        }
    }

    /**
     * 验证签名
     *
     * @param sign
     * @param orderId
     * @param userId
     * @param createTime
     * @return
     */
    private Boolean checkSign(String sign, String orderId, String userId, String createTime, String key) {
        String parameterStr = orderId + userId + createTime +key;
        try {
            String ourSign = DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8.name()));
            if (StringUtils.equals(sign, ourSign)) {
                return true;
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.error("咪咕互娱权益充值,加密参数:{},密钥:{}", parameterStr, sign, e);
        }
        return false;
    }

    private String getPhone(String data,String key) {
        SymmetricCrypto symmetricCrypto = new SymmetricCrypto("SM4/ECB/PKCS5Padding", key.getBytes());
        String phone=symmetricCrypto.decryptStr(data);
        return phone;
    }


    /**
     * 根据支付时间查询对应业务的权益充值记录
     * @param mobile
     * @param serviceIdIdList
     * @return
     */
    @Override
    public List<JunboChargeLog> findByMobileAndPackNameAndDate(String mobile,List<String> serviceIdIdList){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .between(JunboChargeLog::getPayTime,DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .in(JunboChargeLog::getServiceId,serviceIdIdList).list();
    }


    /**
     * 根据手机号查询当月充值成功的酷狗月卡权益
     * @param mobile
     * @return
     */
    private JunboChargeLog findByMobile(String mobile){
        return this.lambdaQuery()
                .eq(JunboChargeLog::getMobile,mobile)
                .eq(JunboChargeLog::getRightsMonth,DateUtil.formatYearMonth(LocalDateTime.now()))
                .eq(JunboChargeLog::getCouponId,BizConstant.KUGOU_COUPON_ID)
                .eq(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS).orderByDesc(JunboChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
    }

    /**
     * 接收骏伯回调通知,根据通知更新直充记录
     *
     * orderSEQ是我方的订单号,orderId是骏伯下单后返回的订单号
     * code 0=充值成功
     *  {"code":"0","msg":"成功","orderId":"20210128115252711300768","orderSEQ":"202010000001","info":null}
     *  {"code":"1111","msg":"失败","orderId":"20210113172001571300027","orderSEQ":"9d840bde26fa4e1288d637ea45c03482","info":null}
     *
     * @param junboResult
     */
    @Override
    public void receiveRechargeNotify(JunboNotifyResult junboResult) {
        final String code = junboResult.getCode();
        Integer status = "0".equals(code) ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        this.lambdaUpdate()
                .eq(JunboChargeLog::getMiguOrderId, junboResult.getData())
                .set(JunboChargeLog::getStatus, status)
                .set(JunboChargeLog::getCallbackCode, code)
                .set(JunboChargeLog::getCallbackMsg, junboResult.getMsg())
                .set(JunboChargeLog::getUpdateTime,new Date())
                .update();
        JunboChargeLog junboChargeLog=this.lambdaQuery().eq(JunboChargeLog::getMiguOrderId,junboResult.getData()).one();
        if(junboChargeLog!=null){
            updateRechargeState(junboChargeLog);
        }
    }
}
