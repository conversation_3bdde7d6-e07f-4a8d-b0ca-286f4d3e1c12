package com.eleven.cms.service.impl;

import com.eleven.cms.config.CompanyWechatGroup;
import com.eleven.cms.config.CompanyWechatProperties;
import com.eleven.cms.service.ICompanyWechatService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.CompanyWechatRequest;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业微信
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/9 14:14
 **/
@Slf4j
@Service
public class CompanyWechatServiceImpl implements ICompanyWechatService {
    private static final String LOG_TAG = "企业微信API";
    @Autowired
    private CompanyWechatProperties companyWechatProperties;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType mediaType;
    @Autowired
    private Environment environment;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.mediaType = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    /**
     * 企业微信机器人发送消息
     * @param mobile 可以为空，为空@全部成员  根据,拼接可以@多个指定群员
     * @param msg 消息内容
     * @param groupName 群名称
     * @return
     */
    @Override
    public Result<?> sendMsg(String mobile, String msg, String groupName) {
        CompanyWechatGroup group=companyWechatProperties.getGroupMap().get(groupName);
        if(group==null){
            log.error("{}-未配置企业微信机器人,mobile:{},msg:{},groupName:{}",LOG_TAG, mobile,  msg,  groupName);
            return Result.error("未配置企业微信机器人");
        }
        final HttpUrl httpUrl = HttpUrl.parse(companyWechatProperties.getWebhookUrl())
                .newBuilder()
                .addQueryParameter("key", group.getGroupKey())
                .build();
        ObjectNode data = mapper.createObjectNode();
        data.put("msgtype", group.getMsgtype());
        ObjectNode text = mapper.createObjectNode();
        text.put("content", msg);
        if(StringUtil.isNotBlank(mobile)){
            if(mobile.contains(",")){
                List<String> mentionedMobileList = Arrays.stream(mobile.split(",")).collect(Collectors.toList());
                text.putPOJO("mentioned_mobile_list",mentionedMobileList);
            }else{
                List<String> mentionedMobileList = Lists.newArrayList(mobile);
                text.putPOJO("mentioned_mobile_list",mentionedMobileList);
            }
        }else {
            List<String> mentionedMobileList = Lists.newArrayList("@all");
            text.putPOJO("mentioned_mobile_list",mentionedMobileList);
        }
        data.putPOJO("text", text);
        log.info("{}-请求参数=>手机号:{},请求参数:{}",LOG_TAG,mobile,data);
        RequestBody body = RequestBody.create(mediaType, data.toString());
        Request request = new Request.Builder().url(httpUrl).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            CompanyWechatRequest result = mapper.readValue(content, CompanyWechatRequest.class);
            if(result.isOK()){
                return Result.ok("发送消息成功");
            }
            return Result.error(result.getErrmsg());
        } catch (IOException e) {
            log.error("{}-请求异常=>手机号:{},请求参数:{}",LOG_TAG,mobile,data,e);
        }
        return Result.error("发送消息失败");
    }


}

