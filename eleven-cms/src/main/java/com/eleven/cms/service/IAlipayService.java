package com.eleven.cms.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayRequest;
import com.alipay.api.FileItem;
import com.alipay.api.response.*;
import com.eleven.cms.dto.AlipayNotifyParam;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.vo.FebsResponse;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/7/14 15:33
 * Desc:Todo
 */
public interface IAlipayService {

    void wapPay(String outTradeNo, String totalAmount, String subject, String body, HttpServletResponse httpResponse) throws IOException;

    void pagePay(String outTradeNo, String totalAmount, String subject, String body, HttpServletResponse httpResponse) throws IOException;

    void pay(AlipayRequest alipayRequest, HttpServletResponse httpResponse) throws IOException;

    AlipayNotifyParam payResultNotify(Map<String, String> requestParams) throws AlipayApiException;

    FebsResponse aliSignUp(String mobile,
                           String externalAgreementNo,
                           String signValidityPeriod,
                           String periodType,
                           String period,
                           String executeTime,
                           String singleAmount,
                           String totalAmount,
                           String totalPayments, String businessType, Date nextDeductTime,String bizType) throws Exception;

    Boolean aliTradePay(String mobile,
                     String externalAgreementNo,
                     String agreementNo,
                     String outTradeNo,
                     String totalAmount,
                     String subject,String appId,String businessType,String businessName,String subChannel,String bizType);

    AlipayNotifyParam aliTradePayResultNotify(Map<String, String> requestParams) throws AlipayApiException;
    /*
    *  支付宝个人代扣协议查询
    *
    * */
    AlipayUserAgreementQueryResponse aliTradePayQuery(String externalAgreementNo,String businessType);

    void aliTradeBrushSheetPay(String mobile,
                               String outTradeNo,
                               String totalAmount,
                               String subject, String appId, String businessType, String businessName, HttpServletResponse response, Subscribe subscribe);
    AlipayUserAgreementExecutionplanModifyResponse alipayDelay(String agreementNo, String businessType,String nextDeductTime);

    AlipayUserAgreementUnsignResponse alipayRescind(String agreementNo, String businessType);

    AlipayTradeRefundResponse alipayRefund(String outTradeNo,String refundAmount,String outRequestNo,String appId);

    AlipayTradeFastpayRefundQueryResponse alipayQueryRefund(String outTradeNo, String outRequestNo, String appId);

    AlipayMerchantTradecomplainQueryResponse alipayQueryComplainDetail(String complainEventId,String appId);

    AlipayMerchantTradecomplainBatchqueryResponse alipayQueryComplainList(String appId,String status,String beginTime,String endTime,Integer pageSize,Integer pageNum);

    AlipayMerchantImageUploadResponse alipayUploadImg(String appId, FileItem imageContent, String imageType);

    AlipayMerchantTradecomplainFeedbackSubmitResponse alipaySolveComplain(String appId, String complainEventId,String feedbackImages,String feedbackContent,String feedbackCode,String operator);

    AlipayMerchantTradecomplainReplySubmitResponse alipayReplyComplain(String appId, String complainEventId,String replyImages,String replyContent);

    AlipayMerchantTradecomplainSupplementSubmitResponse alipaySupplementComplain(String appId, String complainEventId,String supplementImages,String supplementContent);

    AlipayFundTransToaccountTransferResponse  aliPayTransferFee(String outBizNo,String amount,String payeeType,String payeeAccount,String appId,String payerShowName,String remark);

    AlipayFundTransOrderQueryResponse  aliPayQueryTransferFee(String outBizNo,String appId);

    AlipayFundAccountQueryResponse  aliPayQueryAccountBalance(String businessType);

    AlipayFundTransUniTransferResponse  aliPayTransferFeeNew(String outBizNo,String amount,String identityType,String identity,String name,String businessType,String remark,String payerShowNameUseAlias);

    AlipayFundTransCommonQueryResponse  aliPayQueryTransferFeeNew(String outBizNo,String businessType);


    FebsResponse getOpenId(String code, String tradeType, String businessType)throws Exception;

    FebsResponse decrypt(String response, String sign, String businessType)throws Exception;

    FebsResponse aliSignPay(Subscribe subscribe)throws Exception;

    Result<?> aliSignPayFuse(Subscribe subscribe);

    void unSign(String externalAgreementNo);


    Result<?>  aliPayMiGuVrbt(String mobile,String outTradeNo,String totalAmount,String subject,String tradeType,String appId,String channel,String subChannel,String returnUrl,String notifyUrl,String ringType, String ringId,String copyRightId,String bizType,String ringName);
}
