package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.OrderPay;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.vo.FebsResponse;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: cms_order_pay
 * @Author: jeecg-boot
 * @Date:   2022-11-04
 * @Version: V1.0
 */
public interface IOrderPayService extends IService<OrderPay> {
    FebsResponse queryOrderPayState(String outTradeNo);
    void savePay(String tradeType, String orderId, Double totalFee, String appId, String mchId);

    void updatePayStatus(String outTradeNo,String transactionId,Integer status);

    FebsResponse queryShopOrderList(String mobile);


    FebsResponse shopPay(Subscribe subscribe, String mobile);


    FebsResponse saveOrderPay(String userId, String mobile,String account,String channel,String subChannel,String tradeType,String bizType,String orderId,String rightsId,String loginMobile);

    Result<?> queryShopOrder(String orderId);

    OrderPay createPayOrder(Subscribe subscribe);

    OrderPay queryNotPayOrder(String orderId);

    void modifyPayStatus(String outTradeNo, String transactionId, String resultCode,String mobile);

    Boolean queryOrder(String mobile,String orderId);

    Boolean queryPayStatus(String orderId);

    OrderPay queryNotRefundOrder(String refundOrderNo);

    void modifyRefundStatus(String orderId,String outRefundNo, String refundStatus);
}
