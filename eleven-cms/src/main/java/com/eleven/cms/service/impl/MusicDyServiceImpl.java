package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.dto.MusicDyExcelDto;
import com.eleven.cms.entity.MusicDy;
import com.eleven.cms.mapper.MusicDyMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IMusicDyService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.VrbtProduct;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.DateUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * @Description: 订阅包歌曲
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
@Slf4j
@Service
public class MusicDyServiceImpl extends ServiceImpl<MusicDyMapper, MusicDy> implements IMusicDyService {

    //            "videoUserId": "10000145",
    //            "videoUser": {
    //                "resourceType": "4047",
    //                "videoUserId": "10000145",
    //                "userName": "佐悦文化",
    //                "nickName": "佐悦文化",
    //                "smallPicUrl": "http://pl.nf.migu.cn/group2/M00/00/03/CrUBJGEUGm-ITA4YAAERlmxkYn4AAAG1ANuGs0AARGu83.jpeg",
    //                "bigPicUrl": "http://pl.nf.migu.cn/group2/M00/00/03/CrUBJGEUGm-ITA4YAAERlmxkYn4AAAG1ANuGs0AARGu83.jpeg",
    //                "middlePicUrl": "http://pl.nf.migu.cn/group2/M00/00/03/CrUBJGEUGm-ITA4YAAERlmxkYn4AAAG1ANuGs0AARGu83.jpeg",
    //                "dataStatus": "0",
    //                "userStatus": "1",
    //                "subscribe": "0",
    //                "action": "0",
    //                "platformUserId": "638799",
    //                "isSupportSubscribe": 0,
    //                "userType": "1"
    //            },
    public static final String VIDEO_USER_ID_ZUOYUE = "10000145";
    public static final int FETCH_MUSIC_PAGE_SIZE = 10;
    public static final int FETCH_MUSIC_LIMIT_OFFSET = 20000;
    private static final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    MiguApiService miguApiService;

    public MusicDy findByCopyrigthId(String copyrightId){
         return this.lambdaQuery().eq(MusicDy::getCopyrightId,copyrightId).one();
    }

    /**
     * 导入excel同时抓取播放地址和图片地址
     * @param request
     * @param response
     * @param clazz
     * @return
     */
    @Override
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response, Class<MusicDy> clazz) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<MusicDy> list = ExcelImportUtil.importExcel(file.getInputStream(), clazz, params);
                list.forEach(this::fillProductInfo);
                //update-begin-author:taoyan date:20190528 for:批量插入数据
                long start = System.currentTimeMillis();
                saveBatch(list);
                //400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
                //1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                //update-end-author:taoyan date:20190528 for:批量插入数据
                return Result.ok("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /**
     * 编辑从咪咕音乐后台定期导出订阅库的歌曲excel入库,格式保留原生导出的excel,记得按月的日期筛选最新数据导入
     */
    @Override
    public Result importFromExcelFile(String excelFilePath) {
        //2023年5月1日之前的老数据不要
        Date limitDate = Date.from(LocalDate.of(2023,5,1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        //歌曲名包含如下关键字的儿童不宜的老歌不要
        String[] filterMusicNameKey = {"小6子",
                "山猫学姐",
                "达妮",
                "栗子",
                "张依依",
                "园子呀",
                "林猫儿",
                "沐熙",
                "南方小淇",
                "云小朵",
                "小妖潘妮",
                "狼人杀",
                "熊大小姐",
                "丹萍"};

        //excel格式为1行标题,第一列为版权id,第二列为歌曲名,第三列为视频彩铃产品id
        //String filePath = "D:\\歌曲校验_精选内容.xlsx";
        //final String token = miguApiService.fetchToken(MiguApiService.MOBILE_FOR_TEST, channelCode);
        try (FileInputStream fileInputStream = new FileInputStream(excelFilePath)) {
            ImportParams params = new ImportParams();
            //params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(false);
            List<MusicDyExcelDto> list = ExcelImportUtil.importExcel(fileInputStream, MusicDyExcelDto.class, params);
            list.stream()
                    .filter(item -> item.getCreateTime().after(limitDate) && !StringUtils.equalsAny(item.getMusicName(), filterMusicNameKey))
                    .map(item -> {
                        MusicDy md = new MusicDy();
                        BeanUtils.copyProperties(item, md);
                        md.setVrbtProductId(item.getVrbtProductId().replaceAll("[^0-9]", "")) ;
                        return md;
                    })
                    .forEach(musicDy -> {
                        //判断是否已存在
                        if (findByCopyrigthId(musicDy.getCopyrightId())==null) {
                            fillProductInfo(musicDy);
                            this.save(musicDy);
                        }
                    });
        } catch (Exception e) {
            log.error("订阅库的歌曲excel入库出错:{}",e.getMessage(), e);
            return Result.error(e.getMessage());
        }
        return Result.ok();

    }

    @Override
    public void fillProductInfo(MusicDy musicDy){
        final VrbtProduct vrbtProduct = miguApiService.fetchVrbtProduct(musicDy.getCopyrightId());
        musicDy.setVrbtImg(vrbtProduct.getVrbtImg());
        musicDy.setVrbtVideo(vrbtProduct.getVrbtVideo());
        musicDy.setExpiryDate(DateUtils.str2Date(vrbtProduct.getExpiryDate(), DateUtils.date_sdf.get()));
        musicDy.setStatus(vrbtProduct.getStatus());
        musicDy.setUpdateTime(new Date());
    }

    /**
     * 视频彩铃歌曲同步  (抓的数据不完整,废弃)
     * @return
     */
    @Deprecated
    @Override
    public void syncMusic(){
        List<MusicDy> musicDyList = null;
        int offset = 0;
        do {
            musicDyList = crawlMusic(offset);
            for (MusicDy musicDy : musicDyList) {
                final String copyrightId = musicDy.getCopyrightId();
                //判断是否已存在
                if (findByCopyrigthId(copyrightId)!=null) {
                     continue;
                }
                String vrbtTryToSee = miguApiService.vrbtTryToSeeUrl(null, MiguApiService.CH_DYB_DEFAULT, copyrightId);
                musicDy.setVrbtVideo(vrbtTryToSee);
                this.save(musicDy);
            }
            offset += FETCH_MUSIC_PAGE_SIZE;
        } while (!musicDyList.isEmpty() && offset < FETCH_MUSIC_LIMIT_OFFSET);
    }


    /**
     * 视频彩铃歌曲抓取 (抓的数据不完整,废弃)
     * @param  offset 分页偏移
     * @return
     */
    @Deprecated
    @Override
    public List<MusicDy> crawlMusic(int offset){
        final HttpUrl httpUrl = HttpUrl.parse(
                "https://c.musicapp.migu.cn/bmw/vrbt/list-by-videouid/v2.0")
                .newBuilder()
                .addQueryParameter("videoUserId", VIDEO_USER_ID_ZUOYUE)
                .addQueryParameter("queryType", "2")
                .addQueryParameter("pageSize", String.valueOf(FETCH_MUSIC_PAGE_SIZE))
                .addQueryParameter("offset", String.valueOf(offset))
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = OkHttpClientUtils.getSingletonInstance().newCall(request).execute()) {
            final String content = response.body().string();
            final JsonNode resourceNode = mapper.readTree(content).at("/data/contents");
            final List<MusicDy> collect = StreamSupport.stream(resourceNode.spliterator(), false).map(node -> {
                final MusicDy musicDy = new MusicDy();
                musicDy.setStatus(1);
                musicDy.setMusicName(node.at("/title").asText());
                musicDy.setCopyrightId(node.at("/copyrightId").asText());
                musicDy.setVrbtProductId(node.at("/contentId").asText());
                String vrbtImg = node.at("/showImg").asText();
                if(StringUtils.isEmpty(vrbtImg)){
                    log.info("歌曲抓取未找到预览图:{}",node);
                    musicDy.setStatus(0); //禁用没有预览图的歌曲
                    //如果这里没有拿到图片,调用服务端接口miguApiService.vrbtProductQuery和客服端接口也不会有预览图地址
                    //vrbtImg = SpringContextUtils.getBean(IMusicService.class).fetchImg(musicDy.getCopyrightId());
                }
                musicDy.setVrbtImg(vrbtImg);
                musicDy.setExpiryDate(DateUtils.str2Date(node.at("/validTime").asText(), DateUtils.date_sdf.get()));
                musicDy.setSongProductId(String.valueOf(node.at("/libraryType").asInt()));
                musicDy.setListenProductId(node.at("/user/platformUserId").asText());
                musicDy.setCpId(VIDEO_USER_ID_ZUOYUE);
                musicDy.setCreateTime(new Date());
                return musicDy;
            }).collect(Collectors.toList());
            if(collect.isEmpty()){
                log.info("歌曲抓取完成=>offset:{},url:{},conent:{}", offset, httpUrl, content);
            }
            return collect;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Lists.newArrayList();
    }


    /**
     * 抓取视频预览图
     * @param id
     */
    @Override
    public void updateProductById(String id) {
        MusicDy musicDy = this.getById(id);
        this.fillProductInfo(musicDy);
        this.updateById(musicDy);
    }


    @Override
    public void updateProductByIdList(List<String> idList) {
        idList.stream().filter(StringUtils::isNotBlank).forEach(this::updateProductById);
    }

}
