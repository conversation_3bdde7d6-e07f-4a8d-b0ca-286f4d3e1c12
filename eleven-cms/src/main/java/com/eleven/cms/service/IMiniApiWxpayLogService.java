package com.eleven.cms.service;

import com.eleven.cms.dto.DypayNotifyMsg;
import com.eleven.cms.entity.MiniApiWxpayLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.util.Pair;

/**
 * @Description: cms_miniApi_wxpay_log
 * @Author: jeecg-boot
 * @Date:   2022-05-25
 * @Version: V1.0
 */
public interface IMiniApiWxpayLogService extends IService<MiniApiWxpayLog> {

    Pair<Boolean, DypayNotifyMsg> parsePaymentCallback(String raw) throws JsonProcessingException;

    void douyinRightsRecharge(DypayNotifyMsg notifyMsg);
}
