package com.eleven.cms.service;

import com.eleven.cms.shanghaimobile.entity.Aedk;
import com.eleven.cms.shanghaimobile.entity.Ask;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/8 14:16
 **/
public interface ISecurityService {
     Ask getAsk(String mobile);

     Aedk getAedk(String mobile);

     String encrypt(String text,String mobile);

     String decrypt(String text,String mobile);

     String sign(String text,String mobile);

     boolean verify(String text, String signValue, String publicKey,String mobile);
}
