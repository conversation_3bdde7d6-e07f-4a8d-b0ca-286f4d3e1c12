package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.dto.BusinessOrderLog;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.vo.*;
import org.jeecg.common.api.vo.Result;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * @Description: 渠道订阅
 * @Author: jeecg-boot
 * @Date:   2020-09-23
 * @Version: V1.0
 */
public interface ISubscribeService extends IService<Subscribe> {

    Subscribe findByMobileAndCopyrightId(String mobile,String copyrightId);

    Subscribe dianxinFindByMobile(String mobile);

    Subscribe dianxinHunanFindByMobile(String mobile,String channelCode);

    Subscribe findByMobileAndIspOrderId(String mobile, String orderId);

    Subscribe findByIspOrderId(String orderId);

    /**
     * 更新免费订彩铃extra
     * @param copyrightId
     * @param remoteResult
     */
    void updateToneOrderExtra(Subscribe subscribe, String copyrightId, RemoteResult remoteResult) ;
    /**
     * 电信更新免费订彩铃extra
     */
    void dianxinUpdateToneOrderExtra(String mobile, String dxToneCode, DianxinResp toneOrderResp);

    /**
     * 彩铃订购接收
     * @param subscribe
     * @return
     */
    Result<?> receiveVrbtOrderWithAuthCache(Subscribe subscribe);

    Result<?> receiveLiantongOrder(Subscribe subscribe);

    Result<?> receiveBjhyOrderWithCache(Subscribe subscribe);

    Result<?> receiveCpmbOrderWithCache(Subscribe subscribe);

    Result<?> receiveCpmbOrder(Subscribe subscribe);

    Result<?> receiveCpmbOrderBillingCrack(Subscribe subscribe);

    /**
     * 振铃订阅接收(接收号码并发送开通短信验证码)
     * @param subscribe
     * @return
     */
    Result<?> receiveRtOrderWithCache(Subscribe subscribe);

    Result<?> receiveVrbtOrder(Subscribe subscribe);

    Result<?> receiveBjhyOrder(Subscribe subscribe);

    Result<?> receiveRtOrder(Subscribe subscribe);

    /**
     * 渠道数据实时同步
     * @param subChannel
     * @param start
     * @param end
     * @return
     */
    List<ChannelReportItem> channelReport(String subChannel, LocalDate start, LocalDate end);

    boolean isAdUnFeedbackToday(String mobile);

    //Subscribe findByIspOrderId(String orderId);

    Result<?> receiveOrder(Subscribe subscribe);

    //Result<?> receiveDianxinOrder(Subscribe subscribe);

    //Result<?> receiveDianxinOrderOfficialV1(Subscribe subscribe);

    Result<?> receiveDianxinOrderOfficialV2(Subscribe subscribe);

    Result<?> receiveDianxinOrderV1(Subscribe subscribe);

    Result<?> receiveBjhyCrackOrder(Subscribe subscribe);


    /**
     * 新增
     *
     * @param subscribe subscribe
     */
    void createSubscribeDbAndEs(Subscribe subscribe);

    /**
     * 修改
     *
     * @param subscribe subscribe
     */
    void updateSubscribeDbAndEs(Subscribe subscribe);

    //此事务会导致切换数据库失败,优化师字段查询需要切换数据库
    //@Transactional
    void updateSubscribeDbAndEsSync(Subscribe subscribe);

    void updateVerifyStatusDbAndEs(String subscribeId, Integer verifyStatus);

    void updateStatusResultByIdAnd2DbAndEs(String subscriberId, Integer status, String result);


    Result<?> receiveTianyiOrder(Subscribe subscribe);

    Result<?> receiveTianyiOrderV1(Subscribe subscribe);



    /**
     * 四川移动订购业务
     * @param subscribe
     * @return
     */
    Result<?> receiveSichuanMobileOrder(Subscribe subscribe);


    /**
     * 四川移动订购业务 融合版
     * @param subscribe
     * @return
     */
    Result<?> receiveSichuanMobileOrderFuse(Subscribe subscribe);


    /**
     * 视频彩铃报备创建数据
     * @param orderLog
     * @param ipAddr
     */
    Subscribe transformSubscribeFromOrderLog(com.eleven.cms.log.OrderLog orderLog,String ipAddr);

    Boolean isSatisfactionMonth(String mobile);


    /**
     * 四川移动报备获取短信
     * @param phone
     * @param bizCode
     * @return
     */
    Result<?> getSichuanMobileRandom(String phone,String bizCode);


    /**
     *  四川移动报备开通业务
     * @param phone
     * @param code
     * @param bizCode
     * @return
     */
    Result<?> sichuanMobileOrder(String phone,String code,String bizCode);


    /**
     * 外部渠道获取验证码
     * @param mobile
     * @param subChannel
     * @return
     */
    Result<?> getOutsideCode(String mobile,String subChannel);

    /**
     * 外部渠道订购
     * @param subscribe
     * @return
     */
    Result<?> receiveOrderOutside(Subscribe subscribe);

    /**
     * 添加到外部任务队列中
     * @param subscribe
     */
    void addCallbackNotifyMessage(Subscribe subscribe,String msg);

    ///**
    // * 电信单独订购
    // * @param subscribe
    // * @return
    // */
    //Result<?> receiveDianxinAloneOrder(Subscribe subscribe);

    void unsubscribeOutSideCallback(Subscribe subscribe);

    /**
     * 联通单独订购
     * @param subscribe
     */
    void liantongOrder(Subscribe subscribe);


    Result receiveDianxinOrderV2(Subscribe subscribe);


    boolean hasRecentSucceedOrder(String mobile);

    Result<?> dianxinRedirectOfficial(Subscribe subscribe);

    /**
     * 四川移动订购业务v1
     * @param subscribe
     * @return
     */
    Result<?> receiveSichuanMobileOrderV1(Subscribe subscribe);

    /**
     * 四川移动订阅回调
     * @param mobile
     * @param result
     * @param status
     * @param ispOrderNo
     * @param fullResult
     * @return
     */
    Result<?> receiveOrderSichuanMobileCallback(String mobile,String result,Integer status,String ispOrderNo,String fullResult);

    /**
     * 视频彩铃破解开通
     * @param subscribe
     * @return
     */
    Result<?> receiveVrbtCrackOrder(Subscribe subscribe);


    /**
     * 咪咕白金会员更新状态
     * @param subscribe
     */
    Boolean updateBjhy(Subscribe subscribe);

    /**
     * 电信环球网开通
     * @param subscribe
     */
    Result<?> receiveDianxinHuanqiuwangOrder(Subscribe subscribe);

    /**
     * 环球网电信回调处理
     * @param mobile
     * @param orderId
     * @param state
     * @param time
     */
    void receiveDianxinHuanqiuwangNotify(String mobile, String orderId, String state, Date time);

    /**
     * 乐摩吧创建渠道订单
     * @param orderLog
     * @param ipAddr
     * @return
     */
    Subscribe saveBjhyLemobaSubscribe(com.eleven.cms.log.OrderLog orderLog,String ipAddr);


    /**
     * 河北移动订购业务 融合版
     * @param subscribe
     * @return
     */
    Result<?> receiveHebeiMobileOrderFuse(Subscribe subscribe);


    /**
     * 四川移动订购业务 新接口 融合
     * @param subscribe
     * @return
     */
    Result<?> receiveSichuanMobileOrderFuseV1(Subscribe subscribe);

    /**
     * 河北移动订购业务 融合版
     * @param subscribe
     * @return
     */
    Result<?> receiveChongqingMobileOrderFuse(Subscribe subscribe);

    /**
     * 外部渠道获取验证码 破解
     * @param subscribe
     * @return
     */
    Result<?> getOutsideCodeCrack(Subscribe subscribe);

    /**
     * 外部渠道订购 破解
     * @param osOrderId
     * @param smsCode
     * @return
     */
    Result<?> receiveOrderOutsideCrack(String osOrderId, String smsCode);

    Result<?> receiveDianxinOrderCrack(Subscribe subscribe);

    /**
     * 电信麦禾10元破解
     * @param subscribe
     * @return
     */
    Result<?> receiveDianxinMaiheOrderCrack(Subscribe subscribe);



    Result<?> receiveLiantongOrderCrack(Subscribe subscribe);

    Result<?> receiveSichuanMobileCrackOrder(Subscribe subscribe);



    /**
     * 联通心动彩铃破解
     * @param subscribe
     * @return
     */
    Result<?> receiveLiantongXdclOrderCrack(Subscribe subscribe);


    boolean receiveLiantongXdclNotify(String mobile, String orderNo,String resCode,String resMsg);



    /**
     * 联通骏博破解
     * @param subscribe
     * @return
     */
    Result<?> receiveLiantongJunboOrderCrack(Subscribe subscribe);


    boolean receiveLiantongJunboNotify(String orderNo,String resCode,String resMsg);

    Result<?> receiveKuaimaOrder(Subscribe subscribe);

    Result<?> receiveKuaimaMiniAppOrder(Subscribe subscribe);


//    boolean receiveLiantongJunboNotify(String orderNo);

    /**
     * 河北移动订购业务 融合版
     * @param subscribe
     * @return
     */
    Result<?> receiveGansuMobileOrder(Subscribe subscribe);


    /**
     * 黑龙江移动订购业务 融合版
     * @param subscribe
     * @return
     */
    Result<?> receiveHeilongjiangMobileOrderFuse(Subscribe subscribe);


    /**
     * 广西移动5元流量包
     * @param subscribe
     * @return
     */
    Result<?> receiveGuangxiOrder(Subscribe subscribe);

    /**
     * 联保天下四川移动业务
     * @param subscribe
     * @return
     */
    Result<?> receiveSichuanLbtxOrder(Subscribe subscribe);

    /**
     * 湖南移动视频彩铃流量包
     * @param subscribe
     * @return
     */
    Result<?> receiveHunanOrder(Subscribe subscribe);


    /**
     * 甘肃移动30元年包（联保天下）
     * @param subscribe
     * @return
     */
    Result<?> receiveGansuMobileLbtxOrder(Subscribe subscribe);

    /**
     * 新疆移动业务
     * @param subscribe
     * @return
     */
    Result<?> receiveXinjiangOrder(Subscribe subscribe);


    /**
     * 写入自增序列
     * @param channel
     * @param province
     */
    void incrChannelProvinceLimit(String channel, String province);


    /**
     * 写入自增序列
     * @param channel
     * @param province
     * @param owner
     * @return
     */
    void incrChannelProvinceOwnerLimit(String channel, String province, String owner);

    /**
     * 写入自增序列(根据渠道号)
     * @param channel
     */
    void incrChannelLimit(String channel);

    /**
     * 写入自增序列(根据渠道号和归属)
     * @param channel
     * @param owner
     * @return
     */
    void incrChannelOwnerLimit(String channel, String owner);

    /**
     * 获取自增序列值
     * @param channel
     * @param province
     * @return
     */
    Integer getIncrChannelProvinceLimit(String channel, String province);

    /**
     * 获取自增序列值
     *
     * @param channel
     * @param province
     * @param owner
     * @return
     */
    Integer getIncrChannelProvinceOwnerLimit(String channel, String province, String owner);



    /**
     * 获取自增序列值(根据渠道号)
     *
     * @param channel
     * @return
     */
    Integer getIncrChannelLimit(String channel);


    /**
     * 获取自增序列值(根据渠道号和归属)
     *
     * @param channel
     * @param owner
     * @return
     */
    Integer getIncrChannelOwnerLimit(String channel, String owner);

    /**
     * 宁夏移动19.9元会员
     * @param subscribe
     * @return
     */
    Result<?> receiveNingxiaOrder(Subscribe subscribe);


    /**
     * 业务订购记录列表
     * @param firstDay
     * @param endDay
     * @param serviceId
     * @return
     */
    List<BusinessOrderLog> businessOrderLogList(String firstDay, String endDay,String channel, String serviceId,String isp);

    Integer businessOrderLogCount(String firstDay, String endDay,String channel, String mobile,String isp);

    /**
     * 四川联通业务
     * @param subscribe
     * @return
     */
    Result<?> receiveSichuanLiantongLbtxOrder(Subscribe subscribe);

    /**
     * 贵州移动流量包业务
     * @param subscribe
     * @return
     */
    Result<?> receiveGuiZhouMobileOrder(Subscribe subscribe);

    /**
     * 贵州移动高姐流量包业务
     * @param subscribe
     * @return
     */
    Result<?> receiveGuiZhouGaoJieOrder(Subscribe subscribe);

    /**
     * 湖南电信视频彩铃(铃音盒)和智能接听(小秘书)破界收单公用
     * @param subscribe
     * @return
     */
    Result<?> receiveHunanDianxinCommonCrackOrder(Subscribe subscribe);

    void receiveHunanDianxinVrbtNotify(String mobile, String orderNo, String state, Date orderTime, String result);

    void receiveHunanDianxinMindNotify(String mobile, String orderNo, Integer state, Date orderTime);



    /**
     * 广东超炫XR视频彩铃
     * @param subscribe
     * @return
     */
    Result<?> receiveGuangdongOrder(Subscribe subscribe);



    /**
     * 海南移动视频彩铃
     * @param subscribe
     * @return
     */
    Result<?> receiveHainanOrder(Subscribe subscribe);


    /**
     * 安徽移动铂金会员
     * @param subscribe
     * @return
     */
    Result<?> receiveAnhuiOrder(Subscribe subscribe);



    /**
     * 山东电信
     * @param subscribe
     * @return
     */
    Result<?> receiveShandongDianxinOrder(Subscribe subscribe);


    Result<?> dongguanMobileLLBOpenUp(Subscribe subscribe);

    Result<?> vrReceiveOrder(Subscribe subscribe);

    Result<?> receiveWangyiyunMmOrder(Subscribe subscribe);

    void wujiongCrackNotify(String tel,String orderid,String code,String msg) throws Exception;

    void hetuCrackNotify(String tel,String orderid,String code,String msg) throws Exception;

    void junBoLiuLiangBaoCrackNotify(String sysOrderId,String orderStatus,String orderStatusMsg,String contactNumber) throws Exception;

    Result<?> receiveHuNanMobileOrder(Subscribe subscribe);

    Result<?> receiveHenanMobileOrder(Subscribe subscribe);

    Result<?> receiveShanxiMobileOrder(Subscribe subscribe);

    Result<?> receiveShandongHexiaoyuanOrder(Subscribe subscribe);

    Result<?> receiveHubeiMobileOrder(Subscribe subscribe);

    void removeSubscribeDbAndEs(String id);

    void saveChannelLimit(Subscribe subscribe);

    FebsResponse gzdxDchOpenReceive(Subscribe subscribe);

    void report(Subscribe subscribe, Integer status);

    boolean checkBizRepeat(String mobile, String channelCode);

    void youranCrackNotify(String orderNo,String status,String msg)throws Exception;

    YiZunCpaNotify yizunNotify(String sourceTradeId, String tradeStatus);

    void updateSubscribeAndEs(Subscribe subscribe);
}
