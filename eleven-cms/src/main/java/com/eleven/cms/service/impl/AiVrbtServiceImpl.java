package com.eleven.cms.service.impl;

import com.eleven.cms.ad.AiVrbtProperties;
import com.eleven.cms.config.AiVrbtChannel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IAiVrbtService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.AiVrbtOpenResult;
import com.eleven.cms.vo.AiVrbtResult;
import com.eleven.cms.vo.SiChuanExclusiveCardResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.*;

/**
 * AI视频彩铃
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/16 10:46
 **/
@Slf4j
@Service
public class AiVrbtServiceImpl implements IAiVrbtService {
    public static final String AI_VRBT_STATUS_SUB_FAIL = "-1"; //订购失败
    public static final String AI_VRBT_STATUS_SUB_SUCCESS = "0"; //开通成功
    public static final String AI_VRBT_STATUS_UN_SUB = "1"; //退订
    public static final String AI_VRBT_STATUS_DELAY =  "2"; //延迟扣费
    public static final String AI_VRBT_STATUS_VERIFY =  "3"; //1小时内退订
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    private AiVrbtProperties aiVrbtProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private BlackListService blackListService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    @Override
    public Result<?> sendSms(String mobile,String channel) {
        AiVrbtChannel aiVrbtChannel=aiVrbtProperties.getChannelMap().get(channel);
        if(aiVrbtChannel==null){
            return Result.error("获取产品配置失败");
        }
        ObjectNode node =mapper.createObjectNode();
        node.put("mdn",mobile);
        Map<String, String> header = Maps.newHashMap();
        String timestamp= DateUtil.formatFullTime(LocalDateTime.now());
        header.put("timestamp",timestamp);
        header.put("appKey", aiVrbtChannel.getAppKey());

        StringBuffer sb = new StringBuffer();
        sb.append(mobile);
        sb.append("&");
        sb.append(timestamp);
        sb.append("&");
        sb.append(aiVrbtChannel.getSecret());
        String sign = null;
        try {
            sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        header.put("sign", sign);

        String content =implementHttpPostResult(aiVrbtProperties.getInitiateOrderUrl(), node,"AI视频彩铃发送验证码接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return Result.error("发送验证码失败");
        }
        try {
            AiVrbtResult result = mapper.readValue(content, AiVrbtResult.class);
            if(result.isOK() && result.getData()!=null && StringUtil.isNotBlank(result.getData().getTradeid())){
                return Result.ok("发送验证码成功",result.getData().getTradeid());
            }
            return Result.error(result.getMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("发送验证码失败");

    }

    @Override
    public Result<?> submitSms(String mobile,String orderId,String code,String channel) {
        AiVrbtChannel aiVrbtChannel=aiVrbtProperties.getChannelMap().get(channel);
        if(aiVrbtChannel==null){
            return Result.error("获取产品配置失败");
        }
        ObjectNode node =mapper.createObjectNode();
        node.put("tradeid",orderId);
        node.put("smsCode",code);

        Map<String, String> header = Maps.newHashMap();
        String timestamp= DateUtil.formatFullTime(LocalDateTime.now());
        header.put("timestamp",timestamp);
        header.put("appKey", aiVrbtChannel.getAppKey());
        StringBuffer sb = new StringBuffer();
        sb.append(code);
        sb.append("&");
        sb.append(orderId);
        sb.append("&");
        sb.append(timestamp);
        sb.append("&");
        sb.append(aiVrbtChannel.getSecret());
        String sign = null;
        try {
            sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        header.put("sign", sign);

        String content =implementHttpPostResult(aiVrbtProperties.getSubmitOrderUrl(), node,"AI视频彩铃提交验证码接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return Result.error("提交验证码失败");
        }
        try {
            AiVrbtResult result = mapper.readValue(content, AiVrbtResult.class);
            if(result.isOK()){
                return Result.ok("提交验证码成功");
            }
            return Result.error(result.getMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("提交验证码失败");
    }



    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url, Object fromValue,String msg, Map<String, String> header,String mobile) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,header,mobile);
    }

    private String push(String url,String raw,String msg, Map<String, String> header,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},请求参数:{},响应参数:{}",mobile,url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,raw,e);
            return null;
        }
    }




    @Override
    public Map<String, Object> aiVrbtNotify(String mobile,String status,String orderNo,Map<String, Object> map){
        Integer subscribeCount=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, BIZ_CHANNEL_AI_VRBT)
                .between(Subscribe::getCreateTime,LocalDateTime.now().minusHours(24),LocalDateTime.now())
                .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).count();
        if(subscribeCount>0){

            Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                    .eq(Subscribe::getChannel, BIZ_CHANNEL_AI_VRBT)
                    .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS)
                    .orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
           if(AI_VRBT_STATUS_UN_SUB.equals(status)){
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"退订\"}";
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setModifyTime(new Date());
                upd.setResult(result);
                upd.setVerifyStatusDaily(0);
                subscribeService.updateSubscribeDbAndEs(upd);

               try {
                   blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
               } catch (JsonProcessingException e) {
                   e.printStackTrace();
               }
           }

            log.error("AI视频彩铃,订购成功后退订,防止重复上报=>手机号:{},订购状态:{}",mobile,status);
            map.put("code","0000");
            map.put("description","成功");
            return map;
        }
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, BIZ_CHANNEL_AI_VRBT)
                .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED)
                .orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(subscribe==null) {
            log.error("AI视频彩铃,订单状态已改变=>手机号:{},订购状态:{}",mobile,status);
            map.put("code","0000");
            map.put("description","成功");
            return map;
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setModifyTime(new Date());
        if (subscribe!=null && AI_VRBT_STATUS_SUB_SUCCESS.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
            String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(result);
            upd.setOpenTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }
//            subscribeService.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//                subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//            } else {
//                String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//                if (StringUtils.isNotBlank(owner)) {
//                    subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//                }
//            }
            //写入自增序列
            subscribeService.saveChannelLimit(subscribe);
            //外部渠道加入回调延迟队列(暂时不使用队列)
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
        }else if(AI_VRBT_STATUS_VERIFY.equals(status)){
            String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"1小时内退订\"}";
            upd.setResult(result);
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setVerifyStatus(0);
            subscribeService.updateSubscribeDbAndEs(upd);

            try {
                blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }else if(AI_VRBT_STATUS_DELAY.equals(status)){
            String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"延迟扣费\"}";
            upd.setResult(result);
            subscribeService.updateSubscribeDbAndEs(upd);
        }else if(AI_VRBT_STATUS_SUB_FAIL.equals(status)){
            String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"只发送了短信\"}";
            upd.setResult(result);
            subscribeService.updateSubscribeDbAndEs(upd);
        }else {
            //未知错误
            String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"未知错误\"}";
            upd.setResult(result);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        map.put("code","0000");
        map.put("description","成功");
        return map;

    }




    /**
     * 创建订单
     * @param mobile
     * @return
     */
    @Override
    public Result<?> openOrder(String mobile) {
        ObjectNode node =mapper.createObjectNode();
        node.put("mdn",mobile);
        node.put("returnUrl",aiVrbtProperties.getReturnUrl());
        node.put("remark",aiVrbtProperties.getRemark());
        Map<String, String> header = Maps.newHashMap();
        String timestamp=DateUtil.formatFullTime(LocalDateTime.now());
        header.put("timestamp",timestamp);
        StringBuffer sb = new StringBuffer();
        sb.append(aiVrbtProperties.getAuthChannelid());
        sb.append("&");
        sb.append(aiVrbtProperties.getOpenSecret());
        sb.append("&");
        sb.append(timestamp);
        sb.append("&");
        sb.append(mobile);
        sb.append("&");
        sb.append(aiVrbtProperties.getReturnUrl());
        sb.append("&");
        sb.append(aiVrbtProperties.getRemark());
        String sign = null;
        try {
            sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        header.put("signature", sign);
        header.put("channel",aiVrbtProperties.getChannel());
        String content =implementHttpPostResult(aiVrbtProperties.getOpenOrderUrl(), node,"AI视频彩铃创建订单接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return null;
        }
        try {
            AiVrbtOpenResult result = mapper.readValue(content, AiVrbtOpenResult.class);
            if(result.isOK() && StringUtils.isNotBlank(result.getFeeUrl())){
                return Result.ok("创建订单成功",result.getFeeUrl());
            }
            return Result.error(result.getResMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("创建订单失败");
    }
}
