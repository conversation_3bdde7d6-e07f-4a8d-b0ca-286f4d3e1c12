package com.eleven.cms.service.impl;

import com.eleven.cms.entity.ProvinceBusinessChannelInfo;
import com.eleven.cms.mapper.ProvinceBusinessChannelInfoMapper;
import com.eleven.cms.service.IProvinceBusinessChannelInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: cms_province_business_channel_info
 * @Author: jeecg-boot
 * @Date: 2022-08-17
 * @Version: V1.0
 */
@Service
public class ProvinceBusinessChannelInfoServiceImpl extends ServiceImpl<ProvinceBusinessChannelInfoMapper, ProvinceBusinessChannelInfo> implements IProvinceBusinessChannelInfoService {

    @Autowired
    private ProvinceBusinessChannelInfoMapper provinceBusinessChannelInfoMapper;

    @Override
    public List<ProvinceBusinessChannelInfo> selectByMainId(String mainId) {
        return provinceBusinessChannelInfoMapper.selectByMainId(mainId);
    }

    @Override
    public boolean verifyLimit(ProvinceBusinessChannelInfo provinceBusinessChannelInfo) {
        if (provinceBusinessChannelInfo.getXianghongLimit() == null && provinceBusinessChannelInfo.getXiaofengLimit() == null && provinceBusinessChannelInfo.getCpaLimit() == null) {
            return true;
        } else {
            return false;
        }
    }
}
