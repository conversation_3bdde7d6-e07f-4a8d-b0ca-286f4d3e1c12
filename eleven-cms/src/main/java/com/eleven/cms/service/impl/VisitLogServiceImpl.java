package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.Column;
import com.eleven.cms.entity.VisitLog;
import com.eleven.cms.mapper.ColumnMapper;
import com.eleven.cms.mapper.VisitLogMapper;
import com.eleven.cms.service.IVisitLogService;
import com.eleven.cms.util.DateUtil;
import jodd.util.StringUtil;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * @Description: 页面访问日志
 * @Author: jeecg-boot
 * @Date:   2020-10-26
 * @Version: V1.0
 */
@Service
public class VisitLogServiceImpl extends ServiceImpl<VisitLogMapper, VisitLog> implements IVisitLogService {
    @Autowired
    private VisitLogMapper visitLogMapper;
    @Override
    public IPage<VisitLog> pages(Page<VisitLog> page, QueryWrapper<VisitLog> queryWrapper, Map<String, String[]> parameterMap) {

        Date createTimeBegin = null;
        Date createTimeEnd = null;
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        VisitLog visitLog = new VisitLog();
        try{
            if(parameterMap != null){
                String[] createTimeBegins = parameterMap.get("createTime_begin");
//                String[] createTimeEnds = parameterMap.get("createTime_end");
                String[] pages = parameterMap.get("page");
                String[] extra = parameterMap.get("extra");
                if(createTimeBegins != null && createTimeBegins.length > 0){
                    createTimeBegin = df.parse(createTimeBegins[0]);
                    createTimeEnd = new Date(df.parse(createTimeBegins[0]).getTime() + 24*60*60*1000-1);
                }else{
                    createTimeBegin =df.parse(DateUtil.formatSplitTime(LocalDateTime.now()));
                    createTimeEnd = new Date(df.parse(DateUtil.formatSplitTime(LocalDateTime.now())).getTime() + 24*60*60*1000-1);
                }
                if(pages != null && pages.length > 0){
                    visitLog.setPage(pages[0]);
                }
                if(extra != null && extra.length > 0){
                    visitLog.setExtra(extra[0]);
                }
            }else{
                createTimeBegin =df.parse(DateUtil.formatSplitTime(LocalDateTime.now()));
                createTimeEnd =new Date(df.parse(DateUtil.formatSplitTime(LocalDateTime.now())).getTime() + 24*60*60*1000-1);
            }
        }catch (Exception e){
            log.error("pageNew出错：",e);
        }
        IPage<VisitLog> pageList = visitLogMapper.findByPage(page, visitLog,createTimeBegin,createTimeEnd);
        pageList.setTotal(1);
        return pageList;
    }
}
