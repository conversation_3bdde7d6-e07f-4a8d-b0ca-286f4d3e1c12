package com.eleven.cms.service;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WoReadSinglePayOrder;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: cms_wo_read_single_pay_order
 * @Author: jeecg-boot
 * @Date:   2023-11-01
 * @Version: V1.0
 */
public interface IWoReadSinglePayOrderService extends IService<WoReadSinglePayOrder> {

    void saveSinglePayOrder(Subscribe subscribe, String contractCode, Integer payType, String company);
}
