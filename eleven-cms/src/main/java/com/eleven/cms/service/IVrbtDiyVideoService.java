package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.VrbtDiyVideo;

/**
 * @Description: cms_vrbt_diy_video
 * @Author: jeecg-boot
 * @Date: 2023-06-21
 * @Version: V1.0
 */
public interface IVrbtDiyVideoService extends IService<VrbtDiyVideo> {

    boolean checkHead(String dIdPwd, VrbtDiyVideo vrbtDiyVideo);

    /**
     * oss地址文件上传到ftp
     *
     * @param filePath oss文件地址后缀
     * @return
     */
    boolean uploadToFtp(String pathName, String filePath, String fileName);

    boolean uploadVideoToFtp(String pathName, String filePath, String fileName);

    boolean uploadRemoteUrlVideoToFtp(String pathName, String url, String fileName);

    /**
     * 查询铃音设置状态
     *
     * @param id
     * @return 结果
     */
    Integer querySettingRingStatus(String id);
}
