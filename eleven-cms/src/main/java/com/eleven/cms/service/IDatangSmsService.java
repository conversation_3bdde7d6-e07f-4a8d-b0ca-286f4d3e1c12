package com.eleven.cms.service;

/**
 * Author: <EMAIL>
 * Date: 2020/1/3 11:49
 * Desc:Todo
 */
public interface IDatangSmsService {

    boolean sendUnicomUnsubNotice(String phoneNumber);

    boolean sendValidateNotice(String phoneNumber, String smsSign, String smsSuffix, String code);

    boolean sendSms(String phoneNumber, String template,String tplParamKey,String tplParamValue);

    //@Deprecated
    //boolean sendSmsOld(String phoneNumber,String msgContent);

    boolean sendSms(String phoneNumber, String msgContent);

    boolean sendFlashSms(String phoneNumber);

    boolean sendPPTVRightsNotice(String phoneNumber, Integer type);

    boolean sendTianyiNotice(String phoneNumber, String code);

    //boolean sendNotify(String phoneNumber, String content);

    boolean sendUnifyValidateNotice(String phoneNumber, String code);

    boolean sendCheckBaijinMemberNotice(String phoneNumber);

    void sendCrackBaijinMemberNotice(String phoneNumber);

    boolean sendElemeValidateNotice(String phoneNumber, String code);

    boolean sendCheckBaijinysMemberNotice(String phoneNumber);

    void sendCrackBaijinysMemberNotice(String phoneNumber);

    boolean sendCheckBaijindyMemberNotice(String phoneNumber);

    void sendCrackBaijindyMemberNotice(String phoneNumber);

    void sendCrackBactMemberNotice(String phoneNumber);

    void sendCrackBactNotMemberNotice(String phoneNumber);

    boolean sendVrbtRightsNotice(String phoneNumber);

    boolean sendUnifyTemplateNotice(String phoneNumber, String code);


    boolean sendVrbtNotice(String phoneNumber);

    boolean sendShangHaiXuanShiTemplateNotice(String phoneNumber, String code);
}
