package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class QyclBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    IAlipayService alipayService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    BlackListService blackListService;

    public static final String[] BLACK_CITY_ARRAY = {"大庆", "烟台"};

    public static final String JIANGXI_PROVINCE = "江西";

    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    boolean qyclFirst = StringUtils.isBlank(subscribe.getQyclFirst()) || StringUtils.equals(subscribe.getQyclFirst(), "1");
                    if (qyclFirst) {
                        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
//                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
//                        if (StringUtils.isNotBlank(subscribe.getBusinessType()) && provinceBusinessChannelConfigService.allow(subscribe.getBusinessType(), mobileRegionResult.getProvince())) {
//                            return alipayService.aliSignPayFuse(subscribe);
//                        }
                            if (JIANGXI_PROVINCE.equals(mobileRegionResult.getProvince())) {
                                subscribe.setChannel(BIZ_CHANNEl_JXYD_VRBT);
                                subscribe.setBizType(BIZ_TYPE_JXYD);
                                Result result = SpringContextUtils.getBean(JiangxiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                                result.setChannelCode(BIZ_CHANNEl_JXYD_VRBT);
                                return result;
                            }
                            if (provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0, mobileRegionResult.getProvince())) {
                                subscribe.setChannel(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
                                subscribe.setBizType(BIZ_TYPE_BJHY);
                                Result result = SpringContextUtils.getBean(BjhyBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                                result.setChannelCode(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
                                return result;
                            }
                            return Result.error("暂未开放,敬请期待!");
                        }
                        if (StringUtils.equalsAny(mobileRegionResult.getCity(), BLACK_CITY_ARRAY)) {
                            log.warn("移动城市限制,渠道号:{},手机号:{},城市:{}", subscribe.getChannel(), mobile, mobileRegionResult.getCity());
                            return Result.error("暂未开放,敬请期待!");
                        }
//                    if (SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                        subscribe.setChannel(BIZ_TYPE_SHYD_XSSP_VS);
//                        subscribe.setBizType(BIZ_TYPE_SHYD_XSSP_VS);
//                        return SpringContextUtils.getBean(SubscribeServiceImpl.class).shanghaiMobileVrbtPlus(subscribe);
//                    }
                    } else {
                        if (provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0, mobileRegionResult.getProvince())) {
                            subscribe.setChannel(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
                            subscribe.setBizType(BIZ_TYPE_BJHY);
                            Result result = SpringContextUtils.getBean(BjhyBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                            result.setChannelCode(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W0);
                            return result;
                        } else {
                            if (JIANGXI_PROVINCE.equals(mobileRegionResult.getProvince())) {
                                subscribe.setChannel(BIZ_CHANNEl_JXYD_VRBT);
                                subscribe.setBizType(BIZ_TYPE_JXYD);
                                Result result = SpringContextUtils.getBean(JiangxiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                                result.setChannelCode(BIZ_CHANNEl_JXYD_VRBT);
                                return result;
                            }
                            if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                                return Result.error("暂未开放,敬请期待!");
                            }
                        }
                    }
                } else if (mobileRegionResult.isIspDianxin()) {
//                    if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
//                        subscribe.setChannel(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
//                        subscribe.setBizType(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
//                        return subscribeService.receiveHunanDianxinCommonCrackOrder(subscribe);
//                    }
                    subscribe.setChannel(BIZ_TYPE_DX_VRBT);
                    subscribe.setBizType(BIZ_TYPE_VRBT);
                    return subscribeService.receiveDianxinMaiheOrderCrack(subscribe);
//                    return Result.error("暂未开放,敬请期待!");
//                    if (StringUtils.isNotBlank(subscribe.getBusinessType()) && provinceBusinessChannelConfigService.allow(subscribe.getBusinessType(), mobileRegionResult.getProvince())) {
//                        return alipayService.aliSignPayFuse(subscribe);
//                    }
                } else {
                    return Result.error("暂未开放,敬请期待!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        return SpringContextUtils.getBean(QyclBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    @ValidationLimit
    @IpLimit
    public Result receiveOrder(Subscribe subscribe) {
        boolean isPay = StringUtils.equals(subscribe.getQyclAddDp(), "0");
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            if (isPay) {
                yidongVrbtCrackService.smsCode(transactionId, smsCode, subscribe.getChannel(), subscribe.getMobile());
                return Result.ok("提交验证码成功");
            } else {
                final Subscribe target = subscribeService.getById(transactionId);
                if (Objects.isNull(target)) {
                    return Result.captchaErr("请求参数错误");
                }
                //此处保存已提交验证码
                if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                    Subscribe upd = new Subscribe();
                    upd.setId(transactionId);
                    upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                    upd.setModifyTime(new Date());
                    //            this.updateById(upd);
                    subscribeService.updateSubscribeDbAndEs(upd);
                }
                yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
                Result result = Result.ok("提交验证码成功");
                result.setChannelCode(subscribe.getChannel());
                return result;
            }
        }
        String qyclAddDp = subscribe.getQyclAddDp();
        if (!StringUtils.equalsAny(qyclAddDp, "0", "1")) {
            return Result.error("部门信息参数错误");
        }
        int addDepartment = Integer.parseInt(qyclAddDp);
        String departmentName = subscribe.getQyclDpName();
        String departmentId = subscribe.getQyclDpId();
        if ((addDepartment == 1 && StringUtils.isEmpty(departmentName)) || addDepartment == 0 && StringUtils.isEmpty(departmentId)) {
            return Result.error("部门信息参数错误");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        //限制半年之内只能开一次业务(红名单除外)
        if (subscribeService.checkBizRepeat(subscribe.getMobile(), subscribe.getChannel())) {
            log.warn("手机号:{}3个月内已开通过业务", subscribe.getMobile());
            return Result.error("您好，目前该业务服务正在维护中，建议您稍后再来尝试!");
        }
//
//        //30天内开通过的,不再允许重复开通
//        List<Subscribe> list = subscribeService.lambdaQuery().eq(Subscribe::getMobile, subscribe.getMobile()).eq(Subscribe::getBizType, BIZ_TYPE_QYCL)
//                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).gt(Subscribe::getCreateTime, LocalDate.now().minusDays(30L).atTime(LocalTime.MIN)).list();
//        if (list.size() > 0) {
//            return Result.error("请勿重复开通！");
//        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final String defSeq = subscribe.getExtra();
        final BillingResult billingResult = yidongVrbtCrackService.getSmsQycl(subscribe.getMobile(), subscribe.getChannel(),
                addDepartment, departmentName, departmentId, defSeq, subscribe.getQyclCompanyOwner());
        boolean isOK = billingResult.isOK();
        if (isOK) {
            subscribe.setIspOrderNo(billingResult.getTransId());
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        }
        if (isPay) {
            return isOK ? Result.noauth("验证码已发送", billingResult.getTransId()) : Result.error("短信发送失败,请稍后再试");
        } else {
            subscribeService.createSubscribeDbAndEs(subscribe);
            return isOK ? Result.noauth("验证码已发送", subscribe.getId()) : Result.error("短信发送失败,请稍后再试");
        }
    }
}
