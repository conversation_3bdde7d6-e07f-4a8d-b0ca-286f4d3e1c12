package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.PortCrackConfig;

/**
 * @Description: cms_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
public interface IPortCrackConfigService extends IService<PortCrackConfig> {

    PortCrackConfig getCrackConfigByChannel(String channel);

    PortCrackConfig getCrackConfigByCompanyName(String companyName);


    PortCrackConfig getCrackConfig(String channel,String companyName);

    void updateCrackConfig(PortCrackConfig portCrackConfig);
}
