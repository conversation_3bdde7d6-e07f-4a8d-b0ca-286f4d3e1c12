package com.eleven.cms.service.impl;

import com.eleven.cms.ad.TianYiTongXunZhuLiProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISiChuanExclusiveCardService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.SiChuanExclusiveCardResult;
import com.eleven.cms.vo.SiChuanExclusiveCardSendSmsResult;
import com.eleven.cms.vo.SiChuanExclusiveCardSubmitSmsResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 四川个人名片业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 15:52
 **/
@Slf4j
@Service
public class SiChuanExclusiveCardServiceImpl implements ISiChuanExclusiveCardService {
    public static final Integer SICHUAN_BUSINESS_CARD_STATUS_SUB_SUCCESS = 0; //开通成功
    public static final Integer SICHUAN_BUSINESS_CARD_STATUS_UN_SUB = 1; //退订


    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    TianYiTongXunZhuLiProperties tianYiTongXunZhuLiProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    /**
     * 创建订单
     * @param mobile
     * @return
     */
    @Override
    public Result<?> queryOrderInfoDetails(String mobile) {
        ObjectNode node =mapper.createObjectNode();
        node.put("mobile",mobile);

        Map<String, String> header = Maps.newHashMap();
        String timestamp=DateUtil.formatFullTime(LocalDateTime.now());
        header.put("timestamp",timestamp);
        header.put("appKey", tianYiTongXunZhuLiProperties.getAppKey());

        StringBuffer sb = new StringBuffer();
        sb.append(mobile);
        sb.append("&");
        sb.append(timestamp);
        sb.append("&");
        sb.append(tianYiTongXunZhuLiProperties.getSecret());
        String sign = null;
        try {
            sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        header.put("sign", sign);
        String content =implementHttpPostResult(tianYiTongXunZhuLiProperties.getQueryOrderInfoDetailsUrl(), node,"四川个人名片创建订单接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return null;
        }
        try {
            SiChuanExclusiveCardResult result = mapper.readValue(content, SiChuanExclusiveCardResult.class);
            if(result.isOK()){
                return Result.ok("创建订单成功",result.getData().getFreeUrl());
            }
            return Result.error(result.getMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("创建订单失败");
    }





    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url, Object fromValue,String msg, Map<String, String> header,String mobile) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,header,mobile);
    }

    private String push(String url,String raw,String msg, Map<String, String> header,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},请求参数:{},响应参数:{}",mobile,url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,raw,e);
            return null;
        }
    }



    @Override
    public Map<String, Object> siChuanBusinessCardNotify(String phone,Integer status,String serialnum,Map<String, Object> map){

        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, phone)
                .eq(Subscribe::getChannel, BIZ_CHANNEL_EXCLUSIVE_CARD)
                .eq(Subscribe::getRemark,serialnum)
                .orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (SICHUAN_BUSINESS_CARD_STATUS_SUB_SUCCESS.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
//                subscribeService.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//                    subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//                } else {
//                    String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//                    if (StringUtils.isNotBlank(owner)) {
//                        subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//                    }
//                }
                subscribeService.saveChannelLimit(subscribe);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else if(SICHUAN_BUSINESS_CARD_STATUS_UN_SUB.equals(status)){
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"退订\"}";
                upd.setResult(result);
                upd.setVerifyStatus(0);
                subscribeService.updateSubscribeDbAndEs(upd);
            }else {
                //未知错误
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"未知错误\"}";
                upd.setResult(result);
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }else{
            log.error("四川个人名片,重复上报=>手机号:{},订购状态:{}",phone,status);
        }
        map.put("code","200");
        map.put("msg","成功");
        return map;

    }

    @Override
    public Result<?> sendSms(String mobile,String userName,String channelNumber) {
        ObjectNode node =mapper.createObjectNode();
        node.put("userName",userName);
        node.put("smsCode","sms20230724102957002068");
        node.put("channelNumber",channelNumber);

        Map<String, String> header = Maps.newHashMap();
        header.put("User-Agent","Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36");
        header.put("Origin","https://www.118114.net");
        header.put("Referer","https://www.118114.net/h5/marketing/");
        header.put("sec-ch-ua","\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
        header.put("sec-ch-ua-mobile","?1");
        header.put("sec-ch-ua-platform","Android");

        try {
            String content =implementHttpPostResult("https://www.118114.net/prod-op-open-gateway/open/authentication-service/api/v1/code/sendSmsCodeByEncryptedNumber", node,"四川个人名片获取验证码接口",header,mobile);
            SiChuanExclusiveCardSendSmsResult result = mapper.readValue(content, SiChuanExclusiveCardSendSmsResult.class);
            if(result.isOK()){
                return Result.ok("获取验证码成功");
            }
            String msg="四川个人名片业务发送验证码=>{\"resCode\":\""+result.getCode()+"\",\"resMsg\":\""+result.getMsg()+"\"}";
            return Result.error(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.error("系统异常");
    }


    @Override
    public Result<?> submitSms(String mobile,String userName,String code,String channelNumber) {
        LocalDateTime localDateTime = LocalDateTime.now(); // 获取当前时间
        String serialId="HLW"+localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli()+BigDecimal.valueOf(Math.random()).multiply(BigDecimal.valueOf(1000000000L)).setScale(0, RoundingMode.DOWN).add(BigDecimal.valueOf(1)).stripTrailingZeros().toPlainString();
        ObjectNode node =this.addArgument(userName,code,channelNumber,localDateTime,serialId);
        Map<String, String> header = Maps.newHashMap();
        header.put("User-Agent","Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36");
        header.put("Origin","https://www.118114.net");
        header.put("Referer","https://www.118114.net/h5/marketing/");
        header.put("sec-ch-ua","\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
        header.put("sec-ch-ua-mobile","?1");
        header.put("sec-ch-ua-platform","Android");
        try {
            String content =implementHttpPostResult("https://www.118114.net/personalCardForward/yzfOrder/reverseOrderView", node,"四川个人名片提交验证码接口",header,mobile);
            SiChuanExclusiveCardSubmitSmsResult result = mapper.readValue(content, SiChuanExclusiveCardSubmitSmsResult.class);
            if(result.isOK()){
                return Result.ok("提交验证码成功",serialId);
            }
            String msg="四川个人名片业务提交验证码失败=>{\"resCode\":\""+result.getResultCode()+"\",\"resMsg\":\""+result.getResultMsg()+"\"}";
            return Result.error(msg,serialId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.error("系统异常",serialId);
    }
    public ObjectNode addArgument(String phoneNO,String identifyingCode,String channelNumber, LocalDateTime localDateTime,String serialId) {
        ObjectNode node =mapper.createObjectNode();
        String acceptTime=DateUtil.formatFullTime(localDateTime);
        String md5="internet_user"+acceptTime;
        String sign = DigestUtils.md5DigestAsHex((md5).getBytes(StandardCharsets.UTF_8)).substring(8, 24).toUpperCase();
        node.put("sign",sign);
        node.put("appid","internet_user");
        Map<String,Object> infos =Maps.newHashMap();
        infos.put("amount",900);
        infos.put("orderNO","-1");
        infos.put("originalPrice",900);
        infos.put("billType","2");
        infos.put("acceptTime",acceptTime);
        infos.put("orderStatus","0");
        infos.put("paymentWay","10002032");
        infos.put("operType","1000");
        infos.put("phoneNO",phoneNO);
        infos.put("orderWay","1");
        infos.put("sourceCode",7);
        infos.put("areaCode","");
        infos.put("serialID",serialId);
        infos.put("sourceType","00003");
        infos.put("appclass",channelNumber);
        infos.put("identifyingCode",identifyingCode);
        infos.put("commodityCode","");
        infos.put("contactPhone",phoneNO);
        infos.put("paymentStatus","0");
        List<Map<String,String>> productList= Lists.newArrayList();
        Map<String,String> product =Maps.newHashMap();
        product.put("productCode","7360110000100394");
        productList.add(product);
        infos.put("productList",productList);
        infos.put("remarks","XLYX");
        String json = com.alibaba.fastjson.JSON.toJSONString(infos);
        node.putPOJO("infos",json);
        node.put("timestamp",acceptTime);
        return node;
    }
}
