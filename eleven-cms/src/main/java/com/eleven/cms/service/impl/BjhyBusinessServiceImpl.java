package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.service.IBusinessCommonService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class BjhyBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;

    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {

        if (adSiteBusinessConfigService.isBlack(BizConstant.BIZ_TYPE_MIGU_MUSIC, subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }

        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                        //切换到宜搜
//                    if (MiguApiService.BIZ_BJHY_CHANNEL_CODE.equals(subscribe.getChannel()) && provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHYDY_CHANNEL_CODE, subscribe.getProvince()) && RandomUtils.isInRatio(50)) {
//                        subscribe.setChannel(MiguApiService.BIZ_BJHYDY_CHANNEL_CODE);
//                    }
                    //切换到PP
//                    if (MiguApiService.BIZ_BJHYDY_CHANNEL_CODE.equals(subscribe.getChannel()) && !provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHYDY_CHANNEL_CODE, subscribe.getProvince()) && provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHY_CHANNEL_CODE, subscribe.getProvince())) {
//                        subscribe.setChannel(MiguApiService.BIZ_BJHY_CHANNEL_CODE);
//                    }
//                    //白金会员 广西全部切换到U1(组合包除外)
//                    if (!BizConstant.isBjhyZhbChannel(subscribe.getChannel()) && GUANGXI_PROVINCE.equals(subscribe.getProvince()) && provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1, subscribe.getProvince())) {
//                        subscribe.setChannel(MiguApiService.BIZ_BJHY_CHANNEL_CODE_U1);
//                    }
//                    //白金会员组合包上海广西开w6
//                    if (BizConstant.isBjhyZhbChannel(subscribe.getChannel()) && StringUtils.equalsAny(subscribe.getProvince(), SHANGHAI_PROVINCE, GUANGXI_PROVINCE) && provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W6, subscribe.getProvince())) {
//                        subscribe.setChannel(MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W6);
//                    }

                }
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{},是否尝试开通咪咕阅读:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince(), StringUtils.equals(subscribe.getFuseRead(), "1"));
//                        if (!BizConstant.isBjhyZhbChannel(subscribe.getChannel()) && provinceBusinessChannelConfigService.allow("MIGU_READ", mobileRegionResult.getProvince())) {
//                            //开通咪咕阅读
//                            subscribe.setChannel("MIGU_READ");
//                            Result result = innerUnionMemberService.forword(subscribe, subscribe.getChannel());
//                            result.setServiceId("1610473");
//                            return result;
//                        }
//                        if (BizConstant.isBjhyZhbChannel(subscribe.getChannel()) && provinceBusinessChannelConfigService.allow(MiguApiService.CHAOXUAN_CHANNEL_CODE, mobileRegionResult.getProvince())) {
//                            subscribe.setChannel(MiguApiService.CHAOXUAN_CHANNEL_CODE);
//                            subscribe.setBizType(BIZ_TYPE_VRBT);
//                            subscribe.setFuse("1");
//                            Result result = subscribeService.receiveVrbtOrderWithAuthCache(subscribe);
//                            result.setServiceId("698039035100000071");
//                            return result;
//                        }
                        return Result.error("暂未开放,敬请期待!");
                    }
//                    if (HEBEI_PROVINCE.equals(subscribe.getProvince())) {
//                        return Result.cmccRedirect("TO_HBYD");
//                        return subscribeService.receiveHebeiMobileOrderFuse(subscribe);
//                    }
//                    if (SHANGHAI_PROVINCE.equals(subscribe.getProvince())) {
//                        subscribe.setChannel(BIZ_TYPE_SHYD_XSSP_VS);
//                        subscribe.setBizType(BIZ_TYPE_SHYD_XSSP_VS);
////                        return SpringContextUtils.getBean(SubscribeServiceImpl.class).shanghaiMobileVrbtPlus(subscribe);
//                        Result<?> result = SpringContextUtils.getBean(SubscribeServiceImpl.class).shanghaiMobileOpenUp(subscribe, ShanghaiMobileConstant.IS_NOT_RIGHT,ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()));
//                        return result;
//                    }
                    if (HEILONGJIANG_PROVINCE.equals(subscribe.getProvince())) {
//                        subscribe.setChannel(BIZ_TYPE_HLJYD_VRBT);
//                        subscribe.setBizType(BIZ_TYPE_HLJYD_VRBT);
//                        return subscribeService.receiveHeilongjiangMobileOrderFuse(subscribe);
//                        if(RandomUtils.isInRatio(50)){
//                            return Result.cmccRedirect("TO_HLJYD");
//                        }else{
//                            return Result.cmccRedirect("TO_HLJYD_XQY");
//                        }
                        return Result.error("暂未开放,敬请期待!");
                    }
                    if (BizConstant.isBjhyZhbChannel(subscribe.getChannel()) && HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_HN_VRBT_DY_LLB);
                        subscribe.setBizType(BIZ_TYPE_HN_VRBT_DY_LLB);
                        return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveHuNanMobileOrder(subscribe);
                    }
                    if(redisUtil.hasKey(CacheConstant.CMS_VRBT_SWITCH_SCYD) && !BizConstant.isBjhyZhbChannel(subscribe.getChannel())){
                        if (SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel(BIZ_CHANNEL_SCYD_KPJYB);
                            subscribe.setBizType(BIZ_TYPE_SCYD);
                            String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                            subscribe.setBizCode(bizCode);
                            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveSichuanMobileCrackOrder(subscribe);
                        }
                    }
                    if(redisUtil.hasKey(CacheConstant.CMS_ZHB_SWITCH_SCYD) && BizConstant.isBjhyZhbChannel(subscribe.getChannel())){
                        if (SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel(BIZ_CHANNEL_SCYD_KPJYB);
                            subscribe.setBizType(BIZ_TYPE_SCYD);
                            String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                            subscribe.setBizCode(bizCode);
                            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveSichuanMobileCrackOrder(subscribe);
                        }
                    }
                    if(BizConstant.isBjhyZhbChannel(subscribe.getChannel()) && NINGXIA_PROVINCE.equals(subscribe.getProvince())){
                        subscribe.setChannel(BIZ_CHANNEL_NX_XYL);
                        subscribe.setBizType(BIZ_CHANNEL_NX_XYL);
                        return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveNingxiaOrder(subscribe);
                    }

                //if (CHONGQING_PROVINCE.equals(subscribe.getProvince())) {
                    //    subscribe.setChannel(BIZ_TYPE_CQYD_VRBT_DX);
                    //    subscribe.setBizType(BIZ_TYPE_CQYD_VRBT_DX);
                    //    return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveChongqingMobileOrderFuse(subscribe);
                    //}
                    if (HAINAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_HNYD_VRBT);
                        subscribe.setBizType(BIZ_TYPE_HNYD_VRBT);
                        return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveHainanOrder(subscribe);
                    }
//                    if (GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
//                        return Result.error("暂未开放,敬请期待!");
////                        subscribe.setChannel(BIZ_TYPE_GZYD_HJHY_TYHD);
////                        subscribe.setBizType(BIZ_TYPE_GZYD_HJHY_TYHD);
////                        return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveGuiZhouMobileOrder(subscribe);
//                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        Result result = SpringContextUtils.getBean(BjhyBusinessServiceImpl.class).receiveOrder(subscribe);
        if (StringUtils.equals(subscribe.getFuseRead(), "1")) {
            result.setServiceId("698039020105522717");
        }
        return result;
    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(target.getId());
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
//            this.updateById(upd);
                subscribeService.updateSubscribeDbAndEs(upd);
            }
//            if (StringUtils.equalsAny(subscribe.getChannel(), MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W7)) {
//                WujiongCrackResult wujiongCrackResult = wujiongCrackService.smsCode(subscribe.getMobile(), target.getIspOrderNo(), smsCode, subscribe.getChannel());
//                if (wujiongCrackResult.isSubOk()) {
//                    return Result.ok("订阅成功");
//                } else {
//                    final Subscribe targets = subscribeService.getById(target.getId());
//                    if (targets!=null && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(targets.getStatus())) {
//                        Subscribe upd = new Subscribe();
//                        upd.setId(targets.getId());
//                        upd.setStatus(SUBSCRIBE_STATUS_FAIL);
//                        upd.setResult(wujiongCrackResult.getResultMsg());
//                        upd.setModifyTime(new Date());
//                        subscribeService.updateSubscribeDbAndEs(upd);
//                    }
//                    return Result.error("订阅失败");
//                }
//            } else {
                yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, target.getChannel(), subscribe.getMobile());
                return Result.ok("订阅成功");
//            }
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final Result<?> result;
//        if (StringUtils.equalsAny(subscribe.getChannel(), MiguApiService.BIZ_BJHY_ZHB_CHANNEL_CODE_W7)) {
//            result = pushSubscribeService.handleBjhyWujiongExistsBillingCrack(subscribe);
//        } else {
        result = pushSubscribeService.handleBjhyExistsBillingCrack(subscribe);
//        }
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }
}
