package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.RightsPack;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.RightsPackDto;

import java.util.List;
import java.util.Optional;

/**
 * @Description: 会员权益业务关联
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
public interface IRightsPackService extends IService<RightsPack> {

	public List<RightsPack> selectByMainId(String mainId);

	List<RightsPack> selectRightsPackList(String packId);

	IPage<RightsPack> queryRightsPackPageList(Page<RightsPack> page, RightsPack rightsPack);

    List<RightsPackDto> wholeMemberRightsList(String serviceId);

	Boolean isServiceAssociation(String packId, String memberId);

	Optional<RightsPackDto> getMiguPackList(String packName);

	List<RightsPackDto> packList(String[] ids);

	List<RightsPackDto> aliPayRightsList(List<String> serviceId);


	List<RightsPackDto> queryRightsPackList(String serviceId,List<String> businessPackIdList);

	List<RightsPackDto> queryWebRightsPackList(String serviceId,List<String> businessPackIdList);

    List<RightsPackDto> queryRightsListById(String id);
}
