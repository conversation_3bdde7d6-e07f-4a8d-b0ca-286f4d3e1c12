package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.VrbtAppUser;
import com.eleven.cms.mapper.VrbtAppUserMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IVrbtAppUserService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.TokenUtil;
import com.eleven.cms.vo.RemoteResult;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description: 视频彩铃app用户
 * @Author: jeecg-boot
 * @Date:   2024-04-16
 * @Version: V1.0
 */
@Service
public class VrbtAppUserServiceImpl extends ServiceImpl<VrbtAppUserMapper, VrbtAppUser> implements IVrbtAppUserService {
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    ISubscribeService subscribeService;
    @Override
    public Result<?> vrbtAppLogin(String mobile) {
        TokenUtil.setVrbtAppLoginTime(mobile);
        //判断用户是否包月
        final RemoteResult remoteResult =miguApiService.asMemberQuery(mobile,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        boolean sub=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getChannel,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174).eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).count()>0;
        //判断用户是否已注册
        boolean isLogin=this.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).count()>0;
        if(remoteResult.isAsMember() && sub){
            if(isLogin){
                this.lambdaUpdate().eq(VrbtAppUser::getMobile,mobile).set(VrbtAppUser::getSubStatus,BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS).set(VrbtAppUser::getModifyTime,new Date()).update();
            }else{
                VrbtAppUser user=new VrbtAppUser();
                user.setMobile(mobile);
                /**渠道号*/
                user.setChannel(MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
                /**包月状态(-1=初始,0=未包月,1=已包月)*/
                user.setSubStatus(BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS);
                user.setRegisterTime(new Date());
                this.save(user);
            }
            return Result.ok("登录成功",true);
        }
        if(isLogin){
            this.lambdaUpdate().eq(VrbtAppUser::getMobile,mobile).set(VrbtAppUser::getSubStatus,BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE).set(VrbtAppUser::getModifyTime,new Date()).update();
        }else{
            VrbtAppUser user=new VrbtAppUser();
            user.setMobile(mobile);
            /**渠道号*/
            user.setChannel(MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
            /**包月状态(-1=初始,0=未包月,1=已包月)*/
            user.setSubStatus(BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE);
            user.setRegisterTime(new Date());
            this.save(user);
        }
        return Result.ok("登录成功",false);
    }



    /**
     * 根据主键ID和版本号更新用户数据
     * @param vrbtAppUser
     * @return
     */
    @Override
    public void editVrbtAppUser(VrbtAppUser vrbtAppUser) {
        this.baseMapper.updateVrbtAppUser(vrbtAppUser);
    }
}
