package com.eleven.cms.service.impl;

import com.eleven.cms.config.XiMileProperties;
import com.eleven.cms.dto.XimileRechargeNotify;
import com.eleven.cms.dto.XimileRechargeNotifyResp;
import com.eleven.cms.entity.BusinessPack;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.service.IBusinessPackService;
import com.eleven.cms.service.IBusinessRightsSubService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.IXiMiLeApiService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.XiMiLeQueryBalanceResult;
import com.eleven.cms.vo.XiMiLeQueryOrderResult;
import com.eleven.cms.vo.XiMiLeRechargeResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;

/**
 * 西米乐权益充值
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 15:09
 **/
@Slf4j
@Service
public class XiMiLeApiServiceImpl implements IXiMiLeApiService {
    public static final String LOG_TAG = "西米乐权益充值";
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;

    @Autowired
    XiMileProperties xiMileProperties;
    @Autowired
    private Environment environment;


    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IBusinessPackService businessPackService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();

        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    }
    @Override
    public XiMiLeRechargeResult recharge(String orderId, String skuId,String skuType,String accountId,String faceValue){
        final ObjectNode node = mapper.createObjectNode();
        String dtCreate=DateUtil.formatFullTime(LocalDateTime.now());
        node.put("userId",xiMileProperties.getUserId());
        node.put("orderId",orderId);
        node.put("skuId", skuId);
        node.put("skuType", skuType);
        node.put("accountId", accountId);
        node.put("faceValue", faceValue);
        node.put("dtCreate", dtCreate);
        node.put("version", xiMileProperties.getVersion());
        String sign = DigestUtils.md5DigestAsHex((xiMileProperties.getUserId() + orderId + skuId + skuType + accountId + faceValue + dtCreate + xiMileProperties.getVersion() + xiMileProperties.getKey()).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        node.put("sign", sign);
        RequestBody body = RequestBody.create(JSON,node.toString());
        log.info("{}-下单,请求数据=>手机号:{},请求参数:{}",LOG_TAG,accountId,node);
        Request request = new Request.Builder().url(xiMileProperties.getRechargeUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-下单,响应数据=>手机号:{},响应参数:{}",LOG_TAG,accountId,content);
            return mapper.readValue(content, XiMiLeRechargeResult.class);
        } catch (IOException e) {
            log.error("{}-下单,请求异常=>手机号:{}",LOG_TAG,accountId,e);
        }
        return null;
    }



    @Override
    public XiMiLeQueryBalanceResult queryBalance(){
        final ObjectNode node = mapper.createObjectNode();
        node.put("userId",xiMileProperties.getUserId());
        node.put("version", xiMileProperties.getVersion());
        String sign = DigestUtils.md5DigestAsHex((xiMileProperties.getUserId()+ xiMileProperties.getVersion() + xiMileProperties.getKey()).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        node.put("sign", sign);
        RequestBody body = RequestBody.create(JSON,node.toString());
        log.info("{}-查询余额,请求数据=>请求参数:{}",LOG_TAG,node);
        Request request = new Request.Builder().url(xiMileProperties.getQueryBalanceUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询余额,响应数据=>响应参数:{}",LOG_TAG,content);
            return mapper.readValue(content, XiMiLeQueryBalanceResult.class);
        } catch (IOException e) {
            log.error("{}-查询余额,请求异常",LOG_TAG,e);
        }
        return null;
    }



    @Override
    public XiMiLeQueryOrderResult queryOrder(String orderId){
        final ObjectNode node = mapper.createObjectNode();
        node.put("userId",xiMileProperties.getUserId());
        node.put("orderId",orderId);
        node.put("version", xiMileProperties.getVersion());
        String sign = DigestUtils.md5DigestAsHex((xiMileProperties.getUserId() + orderId + xiMileProperties.getVersion() + xiMileProperties.getKey()).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        node.put("sign", sign);
        RequestBody body = RequestBody.create(JSON,node.toString());
        log.info("{}-查询订单,请求数据=>订单号:{},请求参数:{}",LOG_TAG,orderId,node);
        Request request = new Request.Builder().url(xiMileProperties.getQueryOrderUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询订单,响应数据=>订单号:{},响应参数:{}",LOG_TAG,orderId,content);
            return mapper.readValue(content, XiMiLeQueryOrderResult.class);
        } catch (IOException e) {
            log.error("{}-查询订单,请求异常=>订单号:{}",LOG_TAG,orderId,e);
        }
        return null;
    }

    @Override
    public XimileRechargeNotifyResp ximileRechargeNotify(XimileRechargeNotify ximileRechargeNotify) {
        final String code = ximileRechargeNotify.getRetCode();
        Integer status = ximileRechargeNotify.isOK() ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        junboChargeLogService.lambdaUpdate()
                .eq(JunboChargeLog::getMiguOrderId, ximileRechargeNotify.getOrderId())
                .set(JunboChargeLog::getStatus, status)
                .set(JunboChargeLog::getCallbackCode, code)
                .set(JunboChargeLog::getCallbackMsg, ximileRechargeNotify.getRetMsg())
                .set(JunboChargeLog::getUpdateTime,new Date())
                .update();
        JunboChargeLog junboChargeLog=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getMiguOrderId, ximileRechargeNotify.getOrderId()).one();
        if(junboChargeLog!=null){
            updateRechargeState(junboChargeLog);
        }
        return XimileRechargeNotifyResp.ok("回调成功");
    }
    /**
     * 更新业务充值状态
     * @param junboChargeLog
     */
    public void updateRechargeState(JunboChargeLog junboChargeLog)  {
        log.info("{}-更新业务充值状态=>手机号:{},权益领取业务ID:{},充值状态:{}",LOG_TAG,junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus());
        try {
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, junboChargeLog.getServiceId()).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.info("更新业务充值状态业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},充值状态:{}",junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus());
                return;
            }
            final String serviceApiBeanName =businessPack.getServiceApiBeanName();
            log.info("{}-更新业务充值状态ApiBean配置=>手机号:{},权益领取业务ID:{},充值状态:{},ApiBean:{}",LOG_TAG,junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus(),serviceApiBeanName);
            if(StringUtils.isNotBlank(serviceApiBeanName)){
                final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessRightsSubService.class);
                businessRightsSubService.updateRechargeState(junboChargeLog);
            }
        } catch (Exception e) {
            log.error("{}-更新业务充值状态异常=>手机号:{},权益领取业务ID:{},充值状态:{}",LOG_TAG,junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getStatus(),e);
        }
    }
}
