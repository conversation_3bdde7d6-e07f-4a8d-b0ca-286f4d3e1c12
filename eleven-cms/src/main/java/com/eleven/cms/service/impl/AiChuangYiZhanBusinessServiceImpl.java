package com.eleven.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.eleven.cms.annotation.SuccessLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.AidoulaidianService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.AidouGuizhouResult;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class AiChuangYiZhanBusinessServiceImpl implements IBusinessCommonService {
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    AidoulaidianService aidoulaidianService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    ThreadPoolTaskExecutor aiRingThreadPoolTaskExecutor;
    @Autowired
    @Lazy
    RabbitTemplate rabbitTemplate;

    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        return SpringContextUtils.getBean(AiChuangYiZhanBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    @ValidationLimit
    @SuccessLimit
    public Result receiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            BillingResult billingResult = yidongVrbtCrackService.getSms(subscribe.getMobile(), subscribe.getChannel());
            if (billingResult.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setIspOrderNo(billingResult.getTransId());
                subscribeService.updateSubscribeDbAndEs(upd);
//                aiRingThreadPoolTaskExecutor.execute(() -> rabbitTemplate.convertAndSend(RabbitMQConfig.SUBSCRIBE_EVENT_QUEUE_NAME, subscribeService.getById(subscribe.getId())));
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("获取验证码失败");
            }
        } else {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setBizTime(new Date());
                upd.setExtra(subscribe.getSmsCode());
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
//                aiRingThreadPoolTaskExecutor.execute(() -> rabbitTemplate.convertAndSend(RabbitMQConfig.SUBSCRIBE_EVENT_QUEUE_NAME, subscribe));
            }
            String ispOrderNo = Optional.ofNullable(subscribeService.getById(subscribe.getTransactionId())).map(Subscribe::getIspOrderNo).orElse(null);
            if (StrUtil.isBlank(ispOrderNo)) {
                return Result.ok("提交验证码失败");
            }
            yidongVrbtCrackService.smsCode(ispOrderNo, subscribe.getSmsCode(), subscribe.getChannel(), subscribe.getMobile());


            return Result.ok("提交验证码成功");
        }
    }
}
