package com.eleven.cms.service.impl;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * AI视频彩铃
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 15:30
 **/
@Slf4j
@Service
public class AiVrbtBusinessServiceImpl implements IBusinessCommonService  {
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAiVrbtService aiVrbtService;
    @Autowired
    RedisUtil redisUtil;
    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        String mobile = subscribe.getMobile();
        String channel=subscribe.getChannel();
        if (adSiteBusinessConfigService.isBlack(channel, subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspDianxin()) {
                    if (!provinceBusinessChannelConfigService.allow(channel, mobileRegionResult.getProvince())) {
                        log.warn("电信省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持电信用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }


        Result result = SpringContextUtils.getBean(AiVrbtBusinessServiceImpl.class).receiveOrder(subscribe);
        return result;
    }

        @Override
        public Result receiveOrder(Subscribe subscribe) {
            String mobile = subscribe.getMobile();
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            final Result<?> submitResult = aiVrbtService.openOrder(mobile);
            String result = "AI视频彩铃订购结果==>{\"resCode\":\"" + submitResult.getCode() + "\",\"resMsg\":\"" + submitResult.getMessage() + "\"}";

            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            if (submitResult != null && submitResult.isOK()) {
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setResult(String.valueOf(submitResult.getResult()));
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
            }else{
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            }
            upd.setResult(result);
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return submitResult;
        }
}
