package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.MiGuHuYuChannelConfig;
import com.eleven.cms.config.MiGuHuYuQueryMemberProperties;
import com.eleven.cms.entity.HetuCouponCode;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.mapper.HetuCouponCodeMapper;
import com.eleven.cms.service.IHetuCouponCodeService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.Sm2Util;
import com.eleven.cms.vo.MiGuHuYuQueryMember;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;

/**
 * @Description: cms_hetu_coupon_code
 * @Author: jeecg-boot
 * @Date:   2022-03-22
 * @Version: V1.0
 */
@Slf4j
@Service
public class HetuCouponCodeServiceImpl extends ServiceImpl<HetuCouponCodeMapper, HetuCouponCode> implements IHetuCouponCodeService {
    private static final String LOG_TAG = "咪咕互娱查询包月订购";
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;

    @Autowired
    private MiGuHuYuQueryMemberProperties miGuHuYuQueryMemberProperties;
    @Autowired
    private IPortCrackConfigService portCrackConfigService;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    }
    @Override
    public boolean isMember(String mobile,String channel) {
        MiGuHuYuChannelConfig miGuHuYuChannelConfig= miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(channel);
        if(miGuHuYuChannelConfig==null){
//            log.warn("咪咕互娱查询包月状态未配置-手机号:{},渠道号:{}",LOG_TAG,mobile,channel);
//            return false;
            return gameIsMember( mobile, channel);
        }
        ObjectNode node = mapper.createObjectNode();
        try {
            Sm2Util sm2Util=new Sm2Util(miGuHuYuChannelConfig.getPublicKey(),miGuHuYuChannelConfig.getPrivateKey());
            node.put("userCode", sm2Util.encrypt(mobile));
        } catch (Exception e) {
            log.warn("{}-加密异常-手机号:{},渠道号:{}",mobile,channel);
        }
        node.put("chargeId", miGuHuYuChannelConfig.getChargeId());
        node.put("propsId", miGuHuYuChannelConfig.getPropsId());

        RequestBody body = RequestBody.create(JSON, node.toString());
        Request request = new Request.Builder().url(miGuHuYuChannelConfig.getQuerySubUrl()).post(body).build();
        log.info("{}-请求数据=>手机号:{},请求参数:{}",LOG_TAG+miGuHuYuChannelConfig.getProductName(),mobile,node);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-手机号:{}-响应:{}",LOG_TAG+miGuHuYuChannelConfig.getProductName(), mobile,result);
            MiGuHuYuQueryMember miGuHuYuQueryMember=mapper.readValue(result, MiGuHuYuQueryMember.class);
            if(miGuHuYuQueryMember.isOK() && miGuHuYuQueryMember.getResultData().isOK()){
                return true;
            }
            return false;
        } catch (Exception e) {
            log.info("{}-手机号:{}-请求参数:{},异常:",LOG_TAG+miGuHuYuChannelConfig.getProductName(), mobile, node, e);
            return false;
        }
    }


//
//    @Override
//    public MiGuHuYuQueryMember queryMember(String mobile,String channel) {
//        MiGuHuYuChannelConfig miGuHuYuChannelConfig= miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(channel);
//        if(miGuHuYuChannelConfig==null){
//            log.warn("咪咕互娱查询包月状态未配置-手机号:{},渠道号:{}",LOG_TAG,mobile,channel);
//            return null;
//        }
//        ObjectNode node = mapper.createObjectNode();
//        try {
//            Sm2Util sm2Util=new Sm2Util(miGuHuYuChannelConfig.getPublicKey(),miGuHuYuChannelConfig.getPrivateKey());
//            node.put("userCode", sm2Util.encrypt(mobile));
//        } catch (Exception e) {
//            log.warn("{}-加密异常-手机号:{},渠道号:{}",mobile,channel);
//        }
//        node.put("chargeId", miGuHuYuChannelConfig.getChargeId());
//        node.put("propsId", miGuHuYuChannelConfig.getPropsId());
//
//        RequestBody body = RequestBody.create(JSON, node.toString());
//        Request request = new Request.Builder().url(miGuHuYuChannelConfig.getQuerySubUrl()).post(body).build();
//        log.info("{}-请求数据=>手机号:{},请求参数:{}",LOG_TAG+miGuHuYuChannelConfig.getProductName(),mobile,node);
//        try (Response response = client.newCall(request).execute()) {
//            if (!response.isSuccessful()) {
//                throw new IOException("Unexpected code " + response);
//            }
//            String result = response.body().string();
//            log.info("{}-手机号:{}-响应:{}",LOG_TAG+miGuHuYuChannelConfig.getProductName(), mobile,result);
//            MiGuHuYuQueryMember miGuHuYuQueryMember=mapper.readValue(result, MiGuHuYuQueryMember.class);
//            return miGuHuYuQueryMember;
//        } catch (Exception e) {
//            log.info("{}-手机号:{}-请求参数:{},异常:",LOG_TAG+miGuHuYuChannelConfig.getProductName(), mobile, node, e);
//            return null;
//        }
//    }


    private boolean gameIsMember(String mobile,String channel) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-获取验证码-手机号:{},渠道号:{}", "游戏业务-PortCrackConfig-配置错误", mobile, channel);
            return false;
        }
        ObjectNode node = mapper.createObjectNode();
        try {
            Sm2Util sm2Util=new Sm2Util(portCrackConfig.getPublicKey(),portCrackConfig.getPrivateKey());
            node.put("userCode", sm2Util.encrypt(mobile));
        } catch (Exception e) {
            log.warn("{}-加密异常-手机号:{},渠道号:{}",mobile,channel);
        }
        node.put("chargeId", portCrackConfig.getAppId());
        node.put("propsId", portCrackConfig.getOfferCode());
        RequestBody body = RequestBody.create(JSON, node.toString());
        Request request = new Request.Builder().url(portCrackConfig.getQueryProductUrl()).post(body).build();
        log.info("{}-请求数据=>手机号:{},请求参数:{}",LOG_TAG+portCrackConfig.getLogTag(),mobile,node);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-手机号:{}-响应:{}",LOG_TAG+portCrackConfig.getLogTag(), mobile,result);
            MiGuHuYuQueryMember miGuHuYuQueryMember=mapper.readValue(result, MiGuHuYuQueryMember.class);
            if(miGuHuYuQueryMember.isOK() && miGuHuYuQueryMember.getResultData().isOK()){
                return true;
            }
            return false;
        } catch (Exception e) {
            log.info("{}-手机号:{}-请求参数:{},异常:",LOG_TAG+portCrackConfig.getLogTag(), mobile, node, e);
            return false;
        }
    }
}
