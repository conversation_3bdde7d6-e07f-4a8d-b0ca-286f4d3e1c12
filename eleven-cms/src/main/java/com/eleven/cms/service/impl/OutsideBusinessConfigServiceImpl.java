package com.eleven.cms.service.impl;

import com.eleven.cms.entity.OutsideBusinessConfig;
import com.eleven.cms.mapper.OutsideBusinessConfigMapper;
import com.eleven.cms.service.IOutsideBusinessConfigService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: cms_outside_business_config
 * @Author: jeecg-boot
 * @Date:   2023-10-10
 * @Version: V1.0
 */
@Service
public class OutsideBusinessConfigServiceImpl extends ServiceImpl<OutsideBusinessConfigMapper, OutsideBusinessConfig> implements IOutsideBusinessConfigService {
	
	@Autowired
	private OutsideBusinessConfigMapper outsideBusinessConfigMapper;
	
	@Override
	public List<OutsideBusinessConfig> selectByMainId(String mainId) {
		return outsideBusinessConfigMapper.selectByMainId(mainId);
	}
}
