package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.CmsAlarmUser;
import com.eleven.cms.mapper.CmsAlarmUserMapper;
import com.eleven.cms.service.ICmsAlarmUserService;
import com.eleven.cms.util.BizConstant;
import org.springframework.stereotype.Service;

/**
 * @Description: cms_alarm_user
 * @Author: jeecg-boot
 * @Date:   2024-09-04
 * @Version: V1.0
 */
@Service
public class CmsAlarmUserServiceImpl extends ServiceImpl<CmsAlarmUserMapper, CmsAlarmUser> implements ICmsAlarmUserService {

    @Override
    public String findOpenIdByPhone(String phone) {
        final CmsAlarmUser alarmUser = this.lambdaQuery().select(CmsAlarmUser::getOpenId).eq(CmsAlarmUser::getPhone, phone).last(BizConstant.SQL_LIMIT_ONE).one();
        return alarmUser == null ? null : alarmUser.getOpenId();
    }
}
