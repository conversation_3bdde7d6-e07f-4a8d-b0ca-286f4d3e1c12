package com.eleven.cms.service;

import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.vo.GansuMobileCheckResult;
import com.eleven.cms.vo.GansuMobileOrderResult;
import com.eleven.cms.vo.GansuMobileServiceResult;
import com.eleven.cms.vo.YouranGansuMobileResult;
import com.fasterxml.jackson.databind.JsonNode;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;
import java.util.Map;
import java.util.TreeMap;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/27 16:03
 **/
public interface IGanSuMobileApiService {

    /**
     * 发送验证码
     * @param channelCode
     * @param phone
     * @param ip
     * @return
     * @throws Exception
     */
     YouranGansuMobileResult getSms(String channelCode, String phone, String ip) throws Exception ;

    /**
     * 提交验证码
     * @param channelCode
     * @param phone
     * @param smsCode
     * @param identifyingKey
     * @param ip
     * @return
     * @throws Exception
     */
     YouranGansuMobileResult smsCode(String channelCode, String phone, String smsCode, String identifyingKey, String ip) throws Exception ;
    /**
     * 查询订购状态
     * @param channelCode
     * @param phone
     * @param ip
     * @return
     * @throws Exception
     */
    GansuMobileServiceResult queryOrderService(String channelCode, String phone, String ip) throws Exception ;


    /**
     * 查询用户余额
     * @param channelCode
     * @param phone
     * @param smsCode
     * @param identifyingKey
     * @param ip
     * @return
     * @throws Exception
     */
     YouranGansuMobileResult queryUserFee(String channelCode, String phone, String smsCode, String identifyingKey, String ip) throws Exception ;


    /**
     * 查询用户订单
     * @param channelCode
     * @param phone
     * @param orderId
     * @param ip
     * @return
     * @throws Exception
     */
    GansuMobileOrderResult queryOrder(String channelCode, String phone, String orderId, String ip) throws Exception;


    /**
     * 校验产品能否办理
     * @param channelCode
     * @param phone
     * @param ip
     * @return
     * @throws Exception
     */
    GansuMobileCheckResult checkUser(String channelCode, String phone, String ip)throws Exception;


    /**
     * 订购消息结果记录
     * @param jsonNode
     */
    void subMQMsg(JsonNode jsonNode);

}
