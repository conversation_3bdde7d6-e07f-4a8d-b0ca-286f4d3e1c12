package com.eleven.cms.service;

import com.eleven.cms.entity.Music;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.MusicVo;

import java.util.List;

/**
 * @Description: 歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
public interface IMusicService extends IService<Music> {

    List<Music> listVrbtByColumnId(String columnId);

    //void fetchImgAndVideo(Music music);

    //boolean fetchVideoUrl(Music music);

    void updateProductById(String id);

    void updateProductByIdList(List<String> idList);

    MusicVo findVrbtInfoByVrbtId(String vrbtId);

    MusicVo findVrbtInfoByCopyrightId(String copyrightId);

    String getContentIdByCopyrightId(String copyrightId);

    String getChannelCopyrightId(String channelCode, String copyrightId);
    
    String getDyCopyrightIdByCopyrightId(String copyrightId);

    void fillProductInfo(Music music);

    void clickCount(Music music);

    void offline(String copyrightId);
}
