package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.CouponCode;
import com.eleven.cms.entity.HetuCouponCode;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.vo.FebsResponse;

import java.util.List;

/**
 * @Description: cms_coupon_code
 * @Author: jeecg-boot
 * @Date:   2022-03-22
 * @Version: V1.0
 */
public interface ICouponCodeService extends IService<CouponCode> {
    CouponCode getEffectiveDateCouponCode(String couponId);

    void updateCouponCodeStatus(String id,String orderId,Integer status);


    void updateCouponCodeStatus(String id,String orderId,Integer status,String extrInfo);

    FebsResponse updateCouponCodeStatusByMobile(String code, String mobile,String orderId,String rightsId,String account);

    FebsResponse updateCouponCodeStatusByMobile(String code,String orderId);

    void sendCode(/*String orderId, String userId, String phoneNum, String extrInfo, */JunboChargeLog junboChargeLog);

    void sendCodeScheduleDeduct(String id);

    void addAliPayVrbtQueue(String orderId);

    void swfzSendCode(JunboChargeLog junboChargeLog);

    void dyxcSendCode(JunboChargeLog junboChargeLog);

    void cfdtwSendCode(JunboChargeLog junboChargeLog);

    void wzwdSendCode(JunboChargeLog junboChargeLog);

    void duanjuSendCode(JunboChargeLog junboChargeLog);

    void sjcsSendCode(JunboChargeLog junboChargeLog);

    void ahzzSendCode(JunboChargeLog junboChargeLog);

    void swfzSendCodeScheduleDeduct(String id);

    void dyxcSendCodeScheduleDeduct(String id);

    void cfdtwSendCodeScheduleDeduct(String id);

    void wzwdSendCodeScheduleDeduct(String id);

    void duanjuSendCodeScheduleDeduct(String id);

    void sjcsSendCodeScheduleDeduct(String id);

    void ahzzSendCodeScheduleDeduct(String id);

    String saveCode(String channel,int length,String edId);

    HetuCouponCode fetchCouponFromCache(String rightsId);

    Object fetchCouponFromCache(String rightsId, String gameName);

    //    String saveCode(String channel,int length,String start,String end) throws ParseException;
//
//    String saveCode(String channel,String start,String end) throws ParseException;


    void failReissue(String orderId, String phoneNum, String extrInfo, JunboChargeLog junboChargeLog);
    void updateStatusList(List<String> list);


    void hetuCodeResume(JunboChargeLog junboChargeLog);


    void gameSendCode(JunboChargeLog junboChargeLog,String gameName);


    void gameSendCodeScheduleDeduct(String id,String gameName);
}
