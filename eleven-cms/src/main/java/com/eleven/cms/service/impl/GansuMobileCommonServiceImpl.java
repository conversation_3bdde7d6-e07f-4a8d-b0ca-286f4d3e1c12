package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.service.*;
import com.eleven.cms.vo.GansuMobileCheckResult;
import com.eleven.cms.vo.YouranGansuMobileResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service("gansuMobileCommonService")
public class GansuMobileCommonServiceImpl implements IBizCommonService {

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IGanSuMobileApiService ganSuMobileApiService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    OutsideCallbackService outsideCallbackService;


    @Override
    @ValidationLimit
    public Result filerCheck(Subscribe subscribe) {
        GansuMobileCheckResult gansuMobileResult = null;
        try {
            gansuMobileResult = ganSuMobileApiService.checkUser(subscribe.getChannel(), subscribe.getMobile(),subscribe.getIp());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!gansuMobileResult.isOK()) {
            String errorMsg="{\"retCode\":\""+gansuMobileResult.getRetCode()+"\",\"respMsg\":\""+gansuMobileResult.getRespMsg()+"\"}";
            try {
                return Result.errorDuplicateMsg(errorMsg);
            } catch (Exception e) {
                log.error("前置校验异常!-subscribe:{}",subscribe, e);
                return Result.msgDuplicateLimit();
            }
        }
        return Result.ok();
    }


    @SneakyThrows
    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);

        YouranGansuMobileResult youranGansuMobileResult= ganSuMobileApiService.getSms(subscribe.getChannel(), subscribe.getMobile(),subscribe.getIp());
        if (youranGansuMobileResult.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribe.setIspOrderNo(youranGansuMobileResult.getData().getIdentifyingKey());
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {

            try {
                String errorMsg="{\"retCode\":\""+youranGansuMobileResult.getRetCode()+"\",\"respMsg\":\""+youranGansuMobileResult.getRespMsg()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @SneakyThrows
    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        YouranGansuMobileResult youranGansuMobileResult = ganSuMobileApiService.smsCode(subscribe.getChannel(), subscribe.getMobile(), subscribe.getSmsCode(), subscribe.getIspOrderNo(), subscribe.getIp());
        if (youranGansuMobileResult.isOK()) {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setOpenTime(new Date());
            upd.setResult("订购成功");
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            //订购记录延迟队列
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.valueToTree(subscribe);
            rabbitMQMsgSender.gansuMobileDeductMessage(jsonNode);


            subscribeService.saveChannelLimit(subscribe);
            //加入包月延迟校验队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }else{
                //外部渠道加入回调延迟队列(暂时不使用队列)
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            return Result.ok("订阅成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(youranGansuMobileResult.getRespMsg());
            subscribeService.updateSubscribeDbAndEs(upd);

            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            return Result.error("订阅失败");
        }
    }
}
