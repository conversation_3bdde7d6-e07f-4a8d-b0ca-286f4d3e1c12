package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.KuaimaShanxiService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.KuaimaShanxiResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: lihb
 * @create: 2024-3-4 15:54:45
 */
@Service("kuaimaShanxiCommonService")
@Slf4j
public class KuaimaShanxiCommonServiceImpl implements IBizCommonService {

    @Autowired
    KuaimaShanxiService kuaimaShanxiService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobile = subscribe.getMobile();
        KuaimaShanxiResult kuaimaShanxiResult = kuaimaShanxiService.getSms(mobile, subscribe.getChannel());
        if (kuaimaShanxiResult.isOK()) {
            subscribe.setResult("获取验证码成功");
            subscribe.setIspOrderNo(kuaimaShanxiResult.getBizContentResult().getOrderId());
            subscribeService.createSubscribeDbAndEs(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String result=kuaimaShanxiResult.getBizContentResult()!=null?kuaimaShanxiResult.getBizContentResult().getPassFlag()+"-"+kuaimaShanxiResult.getBizContentResult().getOrderMsg():"";
                String errorMsg="{\"code\":\""+kuaimaShanxiResult.getCode()+"\",\"message\":\""+kuaimaShanxiResult.getMessage()+"\",\"result\":\""+result+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        KuaimaShanxiResult kuaimaShanxiResult = kuaimaShanxiService.smsCode(mobile, smsCode, subscribe.getIspOrderNo());
        if (kuaimaShanxiResult.isOK()
                && kuaimaShanxiResult.getBizContentResult() != null
                && "Y".equals(kuaimaShanxiResult.getBizContentResult().getPassFlag())) {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setOpenTime(new Date());
            upd.setResult("订阅成功");
            subscribeService.updateSubscribeDbAndEs(upd);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            //加入1天3天校验队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            subscribeService.saveChannelLimit(subscribe);
            return Result.ok("订阅成功");
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setOpenTime(new Date());
            upd.setResult(kuaimaShanxiResult.getMessage());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error(kuaimaShanxiResult.getMessage());
        }
    }
}
