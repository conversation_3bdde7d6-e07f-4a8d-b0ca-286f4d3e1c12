package com.eleven.cms.service.impl;

import com.eleven.cms.entity.*;
import com.eleven.cms.queue.GuiZhouMobilePayRechargeDeductMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.LianLianFenXiaoProductDetail;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 贵州移动省包（话费支付）
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/13 15:41
 **/
@Slf4j
@Service
public class GuiZhouMobileFeeRightsServiceImpl implements IBusinessLianLianRightsSubService {
    private static final String LOG_TAG = "贵州移动省包（话费支付）[包月校验]";
    private static final String LOG_TAG_ERROR = "贵州移动省包（话费支付）[包月校验异常]";
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private ILianlianChargeLogService lianlianChargeLogService;
    @Autowired
    private ILianLianFenXiaoService lianLianFenXiaoService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private ICommonCouponService commonCouponService;
    @Autowired
    private ISmsModelService smsModelService;
    @Override
    public FebsResponse memberVerify(String couponCode, String serviceId) {
        boolean status=this.monthlyIsSub(couponCode,serviceId);
        //未包月
        if(!status){
            log.warn("{}-手机号校验=>激活码:{},业务ID:{}",LOG_TAG,couponCode,serviceId);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        return new FebsResponse().success();
    }

    @Override
    public FebsResponse rechargRecordVerify(String couponCode,String serviceId) {
        //权益已全部发放
        boolean monthlyIsRecharge=lianlianChargeLogService.monthlyIsRecharge(couponCode,serviceId);
        if(!monthlyIsRecharge){
            log.warn("{}-权益发放校验=>激活码:{},业务ID:{}",LOG_TAG,couponCode,serviceId);
            return new FebsResponse().repeatRecharge();
        }
        return new FebsResponse().success();
    }

    /**
     * 当月是否订购
     * @param couponCode
     * @param serviceId
     * @return
     */
    private boolean monthlyIsSub(String couponCode,String serviceId) {
        try {
            //业务类型去重
            final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().eq(BusinessPack::getServiceId,serviceId).select(BusinessPack::getBusinessId).list();
            if(businessPackList==null || businessPackList.isEmpty()){
                log.warn("{}-业务渠道权益关联未正确配置=>激活码:{},权益领取业务ID:{}",LOG_TAG,couponCode,serviceId);
                return false;
            }
            final List<String> businessIdList=businessPackList.stream().map(BusinessPack::getBusinessId).collect(Collectors.toList());
            final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getId,businessIdList).select(BusinessChannelRights::getBusinessChannel).list();
            if(businessList==null || businessList.isEmpty()){
                log.warn("{}-指定渠道权益未正确配置=>激活码:{},权益领取业务ID:{}",LOG_TAG,couponCode,serviceId);
                return false;
            }
            boolean codeError=commonCouponService.lambdaQuery().eq(CommonCoupon::getCouponCode,couponCode).in(CommonCoupon::getStatus,BizConstant.NOT_USE,BizConstant.RECHARGE_FAIL).ge(CommonCoupon::getInvalidTime,DateUtil.formatSplitTime(LocalDateTime.now())).count()>0;
            if(!codeError){
                log.warn("{}-激活码已使用=>激活码:{},权益领取业务ID:{}",LOG_TAG,couponCode,serviceId);
                return false;
            }
            //查询包月接口还未对接
            return true;
        } catch (Exception e) {
            log.error("{}-激活码:{},权益领取业务ID:{}",LOG_TAG_ERROR, couponCode,serviceId,e);
        }
        return false;
    }

    /**
     * 创建预约充值记录
     * @param serviceId
     * @param packName
     * @param channel
     * @param mobile
     * @param productId
     * @param itemId
     * @param idCard
     * @param customerName
     * @param address
     * @param memo
     * @return
     */
    @Override
    public FebsResponse createScheduledRecharge(String couponCode,String serviceId,String packName,String channel,String mobile,String productId,String itemId,String idCard,String customerName,String address,String memo,String travelDate) {
//        if(Strings.isNullOrEmpty(couponCode)){
//            return new FebsResponse().fail("订单号错误");
//        }
//        boolean payStatus=this.monthlyIsSub(couponCode,serviceId);
//        //未包月
//        if(!payStatus){
//            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//        }
//        //获取预约充值时间
//        LocalDateTime scheduledTime=rightsSubService.getScheduledTime(mobile,serviceId,packName);
//        if(scheduledTime==null){
//            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
//        }
//        //校验权益是否已发放
//        FebsResponse febsResponse=lianlianChargeLogService.isRecharge(couponCode,packName,scheduledTime,itemId,productId);
//        if(!febsResponse.isOK()){
//            return febsResponse;
//        }
//        Result<?> result=lianLianFenXiaoService.queryProductDetailByProductId(productId, itemId);
//        if(!result.isOK()){
//            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
//        }
//        LianLianFenXiaoProductDetail productDetail=(LianLianFenXiaoProductDetail)result.getResult();
//        Optional<LianLianFenXiaoProductDetail.ItemList> itemListOptional =productDetail.getItemList().stream().filter(item-> itemId.equals(String.valueOf(item.getItemId()))).max(Comparator.comparing(LianLianFenXiaoProductDetail.ItemList::getItemId));
//        if(itemListOptional==null || !itemListOptional.isPresent()){
//            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
//        }
//        LianLianFenXiaoProductDetail.ItemList itemList=itemListOptional.get();
//        if(StringUtils.isEmpty(customerName)){
//            return new FebsResponse().fail("请填写姓名");
//        }
//        if(itemList.getStock()<=0){
//            return new FebsResponse().fail("该权益库存不足，请选择其他权益领取");
//        }
//        if(productDetail.getBookingShowAddress()>0 && StringUtils.isBlank(address)){
//            return new FebsResponse().fail("请填写配送地址");
//        }
//        if(productDetail.getOrderShowIdCard()>0 && StringUtils.isBlank(idCard)){
//            return new FebsResponse().fail("请填写身份证");
//        }
//        if(productDetail.getOrderShowDate()>0 && StringUtils.isBlank(travelDate)){
//            return new FebsResponse().fail("请填写使用日期");
//        }
//        /**抢购开始时间*/
//        if(StringUtils.isNotBlank(productDetail.getBeginTime())){
//            LocalDateTime beginTime= DateUtil.parseString(productDetail.getBeginTime(),null);
//            if (beginTime.isAfter(LocalDateTime.now())) {
//                return new FebsResponse().fail("抢购未开始");
//            }
//        }
//        /**抢购结束时间*/
//        if(StringUtils.isNotBlank(productDetail.getEndTime())){
//            LocalDateTime endTime= DateUtil.parseString(productDetail.getEndTime(),null);
//            if (endTime.isBefore(LocalDateTime.now())) {
//                return new FebsResponse().fail("抢购已结束");
//            }
//        }
//        LianlianChargeLog lianlianChargeLog=new LianlianChargeLog();
//        /**订单号*/
//        lianlianChargeLog.setOrderId(couponCode);
//        /**手机号*/
//        lianlianChargeLog.setMobile(mobile);
//        /**业务id*/
//        lianlianChargeLog.setServiceId(serviceId);
//        /**权益月份*/
//        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
//        lianlianChargeLog.setRightsMonth(rightsMonth);
//        /**权益包名*/
//        lianlianChargeLog.setPackName(packName);
//        /**充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充*/
//        lianlianChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED);
//        /**预约要直充的时间,即订单生效的时间*/
//        Date scheduledDate=Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant());
//        lianlianChargeLog.setScheduledTime(scheduledDate);
//        /**渠道号*/
//        lianlianChargeLog.setChannel(channel);
//        /**产品ID*/
//        lianlianChargeLog.setProductId(productId);
//        /**套餐ID*/
//        lianlianChargeLog.setItemId(itemId);
//        /**产品名称*/
//        lianlianChargeLog.setProductName(productDetail.getOnlyName()+itemList.getSubTitle());
//        /**身份证号码*/
//        lianlianChargeLog.setIdCard(idCard);
//        /**名字*/
//        lianlianChargeLog.setCustomerName(customerName);
//        /**收货号码*/
//        lianlianChargeLog.setCustomerPhoneNumber(mobile);
//        /**收货地址*/
//        lianlianChargeLog.setAddress(address);
//        /**备注*/
//        lianlianChargeLog.setMemo(memo);
//        lianlianChargeLog.setOrderType(1);
//        /**渠道结算价(分)产品表（channel_price）*/
//        lianlianChargeLog.setSettlePrice(itemList.getChannelPrice());
//        /**售价(分)产品表（sale_price）*/
//        lianlianChargeLog.setThirdSalePrice(itemList.getSalePrice());
//        try {
//            if(StringUtils.isNotBlank(travelDate)){
//                lianlianChargeLog.setTravelDate(DateUtil.stringToDate(travelDate));
//            }
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        lianlianChargeLogService.save(lianlianChargeLog);
//        rabbitMQMsgSender.guiZhouMobilePayRechargeQueueMessage(GuiZhouMobilePayRechargeDeductMessage.builder().id(lianlianChargeLog.getId()).orderId(lianlianChargeLog.getOrderId()).build());
//        if(!productDetail.getCodeType().equals(1)){
//            return new FebsResponse().success("您的权益已下发到订购手机对应账户，请登录领取权益的官方APP查看使用");
//        }
        return new FebsResponse().success("您的权益将通过短信的方式下发到您订购手机号上，请注意查收");
    }

    /**
     * 创建网页权益预约充值记录
     * @param serviceId
     * @param packName
     * @param channel
     * @param mobile
     * @param productId
     * @param itemId
     * @param idCard
     * @param customerName
     * @param address
     * @param memo
     * @return
     */
    @Override
    public FebsResponse webCreateScheduledRecharge(String couponCode,String serviceId,String packName,String channel,String mobile,String productId,String itemId,String idCard,String customerName,String address,String memo,String travelDate) {
        if(Strings.isNullOrEmpty(couponCode)){
            log.warn("{}-激活码错误=>激活码:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,couponCode,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail("激活码错误");
        }
        CommonCoupon commonCoupon=commonCouponService.lambdaQuery().select(CommonCoupon::getMobile).eq(CommonCoupon::getCouponCode,couponCode).in(CommonCoupon::getStatus,BizConstant.NOT_USE,BizConstant.RECHARGE_FAIL).ge(CommonCoupon::getInvalidTime,DateUtil.formatSplitTime(LocalDateTime.now())).orderByDesc(CommonCoupon::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(commonCoupon==null){
            log.warn("{}-激活码已使用或者激活码已过期=>激活码:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,couponCode,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail("激活码错误");
        }
        if(!commonCoupon.getMobile().equals(mobile)){
            log.warn("{}-手机号错误=>手机号:{},激活码:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail("手机号错误");
        }
        //未包月
        boolean status=this.monthlyIsSub(couponCode,serviceId);
        if(!status){
            log.warn("{}-当前手机号未包月=>手机号:{},激活码:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //业务类型去重
        final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().eq(BusinessPack::getServiceId,serviceId).select(BusinessPack::getBusinessId).list();
        if(businessPackList==null || businessPackList.isEmpty()){
            log.warn("{}-业务渠道权益关联未正确配置=>手机号:{},激活码:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail().message("业务渠道权益关联未正确配置");
        }
        final List<String> businessIdList=businessPackList.stream().map(BusinessPack::getBusinessId).collect(Collectors.toList());
        final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getId,businessIdList).select(BusinessChannelRights::getBusinessChannel).list();
        if(businessList==null || businessList.isEmpty()){
            log.warn("{}-指定渠道权益未正确配置=>手机号:{},激活码:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail().message("指定渠道权益未正确配置");
        }
        final List<String> channelList=businessList.stream().map(BusinessChannelRights::getBusinessChannel).distinct().collect(Collectors.toList());
//        boolean everydayIsSub=rightsSubService.everydayIsSub(mobile,channelList);
//        //未查询到页面订购数据
//        if(!everydayIsSub){
//            log.warn("{}-未查询到页面订购数据=>手机号:{},激活码:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,itemId,productId,channel,serviceId,packName);
//            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getWebScheduledTime(mobile,serviceId,packName);
        if(scheduledTime==null){
            log.warn("{}-预约充值时间未配置=>手机号:{},激活码:{},预约充值时间:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,scheduledTime,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        //校验权益是否已发放
        FebsResponse febsResponse=lianlianChargeLogService.isRecharge(couponCode,packName,scheduledTime,itemId,productId);
        if(!febsResponse.isOK()){
            log.warn("{}-当前激活码权益已发放=>手机号:{},激活码:{},预约充值时间:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,scheduledTime,itemId,productId,channel,serviceId,packName);
            return febsResponse;
        }
        Result<?> result=lianLianFenXiaoService.queryProductDetailByProductId(productId, itemId);
        if(!result.isOK()){
            log.warn("{}-联联产品查询失败=>手机号:{},激活码:{},预约充值时间:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,scheduledTime,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail().message("产品查询失败");
        }
        LianLianFenXiaoProductDetail productDetail=(LianLianFenXiaoProductDetail)result.getResult();
        if(productDetail==null){
            log.warn("{}-联联套餐产品异常=>手机号:{},激活码:{},预约充值时间:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,scheduledTime,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail().message("产品异常");
        }
        Optional<LianLianFenXiaoProductDetail.ItemList> itemListOptional = productDetail.getItemList().stream().filter(item -> itemId.equals(String.valueOf(item.getItemId()))).findAny();
        LianLianFenXiaoProductDetail.ItemList itemList=itemListOptional.isPresent()?itemListOptional.get():null;
        if(itemList==null){
            log.warn("{}-联联套餐已下架=>手机号:{},激活码:{},预约充值时间:{},套餐ID:{},产品ID:{},渠道号:{},业务ID:{},权益包名:{}",LOG_TAG,mobile,couponCode,scheduledTime,itemId,productId,channel,serviceId,packName);
            return new FebsResponse().fail().message("产品已下架");
        }
        if(StringUtils.isEmpty(customerName)){
            return new FebsResponse().fail("请填写姓名");
        }
        if(productDetail.getItemStock().intValue()>0){
            if(itemList.getStock()==null || itemList.getStock().intValue()<=0){
                return new FebsResponse().fail("该权益库存不足，请选择其他权益领取");
            }
        }else{
            if(productDetail.getStockAmount()==null || productDetail.getStockAmount().intValue()<=0){
                return new FebsResponse().fail("该权益库存不足，请选择其他权益领取");
            }
        }
        if(productDetail.getBookingShowAddress()>0 && StringUtils.isBlank(address)){
            return new FebsResponse().fail("请填写配送地址");
        }
        if(productDetail.getOrderShowIdCard()>0 && StringUtils.isBlank(idCard)){
            return new FebsResponse().fail("请填写身份证");
        }
        if(productDetail.getOrderShowDate()>0 && StringUtils.isBlank(travelDate)){
            return new FebsResponse().fail("请填写使用日期");
        }
        /**抢购开始时间*/
        if(StringUtils.isNotBlank(productDetail.getBeginTime())){
            LocalDateTime beginTime= DateUtil.parseString(productDetail.getBeginTime(),null);
            if (beginTime.isAfter(LocalDateTime.now())) {
                return new FebsResponse().fail("抢购未开始");
            }
        }
        /**抢购结束时间*/
        if(StringUtils.isNotBlank(productDetail.getEndTime())){
            LocalDateTime endTime= DateUtil.parseString(productDetail.getEndTime(),null);
            if (endTime.isBefore(LocalDateTime.now())) {
                return new FebsResponse().fail("抢购已结束");
            }
        }
        LianlianChargeLog lianlianChargeLog=new LianlianChargeLog();
        /**订单号*/
        lianlianChargeLog.setOrderId(couponCode);
        /**手机号*/
        lianlianChargeLog.setMobile(mobile);
        /**业务id*/
        lianlianChargeLog.setServiceId(serviceId);
        /**权益月份*/
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        lianlianChargeLog.setRightsMonth(rightsMonth);
        /**权益包名*/
        lianlianChargeLog.setPackName(packName);
        /**充值状态:0=直充中,1=直充成功,2=直充失败,3=已回收,-1=预约直充*/
        lianlianChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED);
        /**预约要直充的时间,即订单生效的时间*/
        Date scheduledDate=Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant());
        lianlianChargeLog.setScheduledTime(scheduledDate);
        /**渠道号*/
        lianlianChargeLog.setChannel(channel);
        /**产品ID*/
        lianlianChargeLog.setProductId(productId);
        /**套餐ID*/
        lianlianChargeLog.setItemId(itemId);
        /**产品名称*/
        lianlianChargeLog.setProductName(productDetail.getOnlyName()+itemList.getSubTitle());
        /**身份证号码*/
        lianlianChargeLog.setIdCard(idCard);
        /**名字*/
        lianlianChargeLog.setCustomerName(customerName);
        /**收货号码*/
        lianlianChargeLog.setCustomerPhoneNumber(mobile);
        /**收货地址*/
        lianlianChargeLog.setAddress(address);
        /**备注*/
        lianlianChargeLog.setMemo(memo);
        lianlianChargeLog.setOrderType(1);
        /**渠道结算价(分)产品表（channel_price）*/
        lianlianChargeLog.setSettlePrice(itemList.getChannelPrice());
        /**售价(分)产品表（sale_price）*/
        lianlianChargeLog.setThirdSalePrice(itemList.getSalePrice());
        try {
            if(StringUtils.isNotBlank(travelDate)){
                lianlianChargeLog.setTravelDate(DateUtil.stringToDate(travelDate));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        lianlianChargeLogService.save(lianlianChargeLog);
        rabbitMQMsgSender.guiZhouMobilePayRechargeQueueMessage(GuiZhouMobilePayRechargeDeductMessage.builder().id(lianlianChargeLog.getId()).orderId(lianlianChargeLog.getOrderId()).build());
        if(!productDetail.getCodeType().equals(1)){
            return new FebsResponse().success("您的权益已下发到订购手机对应账户，请登录领取权益的官方APP查看使用");
        }
        return new FebsResponse().success("您的权益将通过短信的方式下发到您订购手机号上，请注意查收");
    }



    @Override
    public FebsResponse rechargeForScheduleVerify(LianlianChargeLog lianlianChargeLog) {
        //查询该手机号当月所有订单
        List<LianlianChargeLog> chargeLogList=lianlianChargeLogService.findByOrderIdAndPackName(lianlianChargeLog.getOrderId(),lianlianChargeLog.getPackName());
        //判断当月是否有已充值成功或者充值中的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()) || BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasSuccess){
            lianlianChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            lianlianChargeLog.setRemark("预约直充取消,当月已充值或充值中");
            lianlianChargeLog.setUpdateTime(new Date());
            lianlianChargeLogService.updateById(lianlianChargeLog);
            commonCouponService.lambdaUpdate().eq(CommonCoupon::getCouponCode, lianlianChargeLog.getOrderId()).set(CommonCoupon::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY).set(CommonCoupon::getUpdateTime, new Date()).update();
            return new FebsResponse().fail();
        }
        boolean payStatus=this.monthlyIsSub(lianlianChargeLog.getOrderId(),lianlianChargeLog.getServiceId());
        //未包月
        if(!payStatus){
            lianlianChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            lianlianChargeLog.setRemark("预约直充取消,订单已退订或暂停");
            lianlianChargeLog.setUpdateTime(new Date());
            lianlianChargeLogService.updateById(lianlianChargeLog);
            commonCouponService.lambdaUpdate().eq(CommonCoupon::getCouponCode, lianlianChargeLog.getOrderId()).set(CommonCoupon::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_RECOVERY).set(CommonCoupon::getUpdateTime, new Date()).update();
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }
    @Override
    public void rechargeForSchedule(LianlianChargeLog lianlianChargeLog) {
        LianlianChargeLog  chargeLog=lianlianChargeLogService.taskRechargeForSchedule(lianlianChargeLog);
        updateRechargeState(chargeLog);
    }

    /**
     * 更新订单领取状态
     * @param lianlianChargeLog
     */
    @Override
    public void updateRechargeState(LianlianChargeLog lianlianChargeLog) {
        if(StringUtils.equalsAny(String.valueOf(lianlianChargeLog.getStatus()),"0","1","2")){
            int status=lianlianChargeLog.getStatus().equals(1)?1:lianlianChargeLog.getStatus().equals(0)?4:3;
            commonCouponService.lambdaUpdate().eq(CommonCoupon::getCouponCode, lianlianChargeLog.getOrderId())
                    .set(CommonCoupon::getStatus,status)
                    .set(CommonCoupon::getExchangeRightsName,lianlianChargeLog.getProductName())
                    .set(CommonCoupon::getUpdateTime, new Date())
                    .update();
            if(lianlianChargeLog.getStatus().equals(2)){
                //充值失败发送短信
                Map<String,String> smsMap= Maps.newHashMap();
                smsMap.put("productName",lianlianChargeLog.getProductName());
                smsMap.put("redeemCode",lianlianChargeLog.getOrderId());
                smsModelService.sendSms(lianlianChargeLog.getMobile(),lianlianChargeLog.getChannel(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID,BizConstant.BUSINESS_TYPE_RIGHTS,smsMap);
            }
            commonCouponService.lianlianSendCode(lianlianChargeLog.getOrderId());
        }
    }
}
