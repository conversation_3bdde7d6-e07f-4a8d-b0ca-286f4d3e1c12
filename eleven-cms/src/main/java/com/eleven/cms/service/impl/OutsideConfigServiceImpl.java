package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.OutsideBusinessConfig;
import com.eleven.cms.entity.OutsideConfig;
import com.eleven.cms.mapper.OutsideBusinessConfigMapper;
import com.eleven.cms.mapper.OutsideConfigMapper;
import com.eleven.cms.service.IOutsideBusinessConfigService;
import com.eleven.cms.service.IOutsideConfigService;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import static org.jeecg.common.constant.CacheConstant.CMS_OUTSIDE_CONFIG_CACHE;

/**
 * @Description: cms_outside_config
 * @Author: jeecg-boot
 * @Date:   2023-04-26
 * @Version: V1.0
 */
@Service
public class OutsideConfigServiceImpl extends ServiceImpl<OutsideConfigMapper, OutsideConfig> implements IOutsideConfigService {


    @Autowired
    private OutsideConfigMapper outsideConfigMapper;
    @Autowired
    private OutsideBusinessConfigMapper outsideBusinessConfigMapper;
    @Autowired
    private IOutsideBusinessConfigService outsideBusinessConfigService;

    public static final String CALLBACK_URL_JUNBO_DOMAIN = "gzjunbo.net";
    public static final String CALLBACK_URL_JUNBO_DOMAIN_NEW = "liulianglf.cn";
    public static final String CALLBACK_URL_ZSY_DOMAIN = "************:3001";

    @Override
    @Cacheable(cacheNames = CMS_OUTSIDE_CONFIG_CACHE, key = "#root.methodName + ':' + #p0", condition = "#p0!=null", unless = "#result==null")
    public OutsideConfig getOutsideConfigByOutChannel(String outChannel) {
        return this.lambdaQuery().eq(OutsideConfig::getOutChannel, outChannel).eq(OutsideConfig::getStatus, 1).one();
    }

    @Override
    public boolean isOutsideChannel(String outChannel) {
        return StringUtils.startsWith(outChannel, "OSVRBT") && getOutsideConfigByOutChannel(outChannel) != null;
    }

    @Override
    public boolean isJunboOutsideChannel(String outChannel) {
        OutsideConfig outsideConfig = getOutsideConfigByOutChannel(outChannel);
        return outsideConfig != null && StringUtils.containsAny(outsideConfig.getNotifyUrl(), CALLBACK_URL_JUNBO_DOMAIN, CALLBACK_URL_JUNBO_DOMAIN_NEW);
    }

    @Override
    public boolean isZsyOutsideChannel(String outChannel) {
        OutsideConfig outsideConfig = getOutsideConfigByOutChannel(outChannel);
        return outsideConfig != null && StringUtils.contains(outsideConfig.getNotifyUrl(), CALLBACK_URL_ZSY_DOMAIN);
    }

    @Override
    @Transactional
    public void saveMain(OutsideConfig outsideConfig, List<OutsideBusinessConfig> outsideBusinessConfigList) {
        outsideConfigMapper.insert(outsideConfig);
        if (outsideBusinessConfigList != null && outsideBusinessConfigList.size() > 0) {
            for (OutsideBusinessConfig entity : outsideBusinessConfigList) {
                //外键设置
                entity.setOutsideConfigId(outsideConfig.getId());
                outsideBusinessConfigMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = CMS_OUTSIDE_CONFIG_CACHE, key = "'getOutsideConfigByOutChannel:' + #p0.outChannel")
    public void updateMain(OutsideConfig outsideConfig,List<OutsideBusinessConfig> outsideBusinessConfigList) {
        outsideConfigMapper.updateById(outsideConfig);

        //1.先删除子表数据
        outsideBusinessConfigMapper.deleteByMainId(outsideConfig.getId());

        //2.子表数据重新插入
        if(outsideBusinessConfigList!=null && outsideBusinessConfigList.size()>0) {
            for(OutsideBusinessConfig entity:outsideBusinessConfigList) {
                //外键设置
                entity.setOutsideConfigId(outsideConfig.getId());
                outsideBusinessConfigMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void delMain(String id) {
        outsideBusinessConfigMapper.deleteByMainId(id);
        outsideConfigMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for(Serializable id:idList) {
            outsideBusinessConfigMapper.deleteByMainId(id.toString());
            outsideConfigMapper.deleteById(id);
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_OUTSIDE_CONFIG_BUSINESS_CACHE, key = "#root.methodName + ':' + #p0+ ':'+ #p1", condition = "#p0!=null", unless = "#result==null")
    public OutsideBusinessConfig getOutsideBusinessConfig(String configId, String province) {
        return outsideBusinessConfigService.lambdaQuery().eq(OutsideBusinessConfig::getOutsideConfigId, configId).eq(OutsideBusinessConfig::getProvince, province).one();
    }

}
