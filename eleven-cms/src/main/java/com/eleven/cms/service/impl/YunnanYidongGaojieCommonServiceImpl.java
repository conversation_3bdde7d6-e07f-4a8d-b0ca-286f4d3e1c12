package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.YunnanYidongGaojieService;
import com.eleven.cms.remote.YunnanYidongService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.*;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: lihb
 * @create: 2024-6-20 15:20:34
 */
@Service("yunnanYidongGaojieCommonService")
@Slf4j
public class YunnanYidongGaojieCommonServiceImpl implements IBizCommonService {

    private static final Interner<String> interner = Interners.newWeakInterner();

    @Autowired
    YunnanYidongGaojieService yunnanYidongGaojieService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobile = subscribe.getMobile();
        String channel = subscribe.getChannel();
        //查询归属地-鉴权查询-下发验证码
        synchronized (interner.intern(mobile)) {
            YunnanMobileGaojieResult yunnanMobileGaojieResult = yunnanYidongGaojieService.getSms(mobile, channel);
            if (yunnanMobileGaojieResult.isOK()){
                subscribe.setResult("获取验证码成功");
                subscribeService.createSubscribeDbAndEs(subscribe);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
//                return Result.error(500, "获取验证码失败", "YUNNAN");

                try {
                    String errorMsg="{\"code\":\""+yunnanMobileGaojieResult.getCode()+"\",\"msg\":\""+yunnanMobileGaojieResult.getMsg()+"\"}";
                    return Result.errorSmsMsgYunnan("YUNNAN",errorMsg);
                } catch (Exception e) {
                    log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                    return Result.errorSmsMsgYunnan("YUNNAN");
                }
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        final Subscribe target = subscribeService.getById(subscribe.getId());
        target.setBizTime(new Date());
        if (Objects.isNull(target)) {
            return Result.captchaErr("请求参数错误");
        }
        YunnanMobileGaojieResult yunnanMobileGaojieResult = yunnanYidongGaojieService.smsCode(mobile, subscribe.getChannel(), smsCode);
        if (yunnanMobileGaojieResult.isOK()) {
            //订阅成功
            target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            target.setOpenTime(new Date());
            target.setResult("订阅成功");
            subscribeService.updateSubscribeDbAndEs(target);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(target);
            rabbitMQMsgSender.addDelayedVerifyMessage(target);
            return Result.ok("订阅成功", "YUNNAN");
        } else {
            //订阅失败
            target.setStatus(SUBSCRIBE_STATUS_FAIL);
            target.setOpenTime(new Date());
            target.setResult(yunnanMobileGaojieResult.getMsg());
            subscribeService.updateSubscribeDbAndEs(target);
            return Result.error(500, "订阅失败", "YUNNAN");
        }
    }
}
