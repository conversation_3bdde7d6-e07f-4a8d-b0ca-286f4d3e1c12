package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.mapper.CmsCrackConfigMapper;
import com.eleven.cms.service.ICmsCrackConfigService;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.jeecg.common.constant.CacheConstant.CMS_CACHE_CHANNEL_CRACK_CONFIG;

/**
 * @Description: cms_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Service
public class CmsCrackConfigServiceImpl extends ServiceImpl<CmsCrackConfigMapper, CmsCrackConfig> implements ICmsCrackConfigService {

@Autowired
private RedisUtil redisUtil;


    @Override
    //@Cacheable(cacheNames = CMS_CACHE_CHANNEL_CRACK_CONFIG, key = "#p0", condition = "#p0!=null", unless = "#result==null")
    public CmsCrackConfig getCrackConfigByChannel(String channel) {
      //  return lambdaQuery().eq(CmsCrackConfig::getChannel, channel).one();
        String key = CMS_CACHE_CHANNEL_CRACK_CONFIG +"::"+channel+":vrbtApp";
        Object cache = redisUtil.get(key);
        if(Objects.nonNull(cache)){
            return JSONObject.parseObject(cache.toString(),CmsCrackConfig.class);
        }
        CmsCrackConfig cmsCrackConfig = lambdaQuery().eq(CmsCrackConfig::getChannel, channel).one();
        if(Objects.nonNull(cmsCrackConfig)){
            redisUtil.set(key,JSONObject.toJSONString(cmsCrackConfig),60 * 60 * 24 * 7);
        }
        return cmsCrackConfig;
    }

    @Override
    public List<String> getChannnelByServiceId(String serviceId) {
        return lambdaQuery().eq(CmsCrackConfig::getServiceId, serviceId).list().stream().map(CmsCrackConfig::getChannel).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(cacheNames = CMS_CACHE_CHANNEL_CRACK_CONFIG, key = "#p0.channel")
    public void updateCrackConfig(CmsCrackConfig cmsCrackConfig) {
        this.updateById(cmsCrackConfig);
    }

    /**
     * 查询渠道号列表
     *
     * @return List<String>
     */
    @Override
    public List<String> queryChannelList() {
        return list(new LambdaQueryWrapper<CmsCrackConfig>()
                .select(CmsCrackConfig::getChannel))
                .stream().map(CmsCrackConfig::getChannel).collect(Collectors.toList());
    }
}
