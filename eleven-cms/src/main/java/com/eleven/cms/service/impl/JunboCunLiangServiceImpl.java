package com.eleven.cms.service.impl;

import com.eleven.cms.config.JunboClConfig;
import com.eleven.cms.config.JunboClProperties;
import com.eleven.cms.dto.JunboCunLiangNotify;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IJunboCunLiangService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.*;


/**
 * 骏伯上海移动存量业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/13 16:07
 **/
@Slf4j
@Service
public class JunboCunLiangServiceImpl implements IJunboCunLiangService {
    private static final String LOG_TAG= "骏伯上海移动存量业务";



    @Autowired
    private JunboClProperties junboClProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private BlackListService blackListService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    }

    @Override
    public Result<?> sendSms(Subscribe subscribe) {
        JunboClConfig junboClConfig=junboClProperties.getJunboClConfigByChannel(subscribe.getChannel());
        ObjectNode data = mapper.createObjectNode();
        data.put("pid", Long.valueOf(junboClConfig.getPid()));
        data.put("sysOrderId", subscribe.getIspOrderNo());
        data.put("productCode", junboClConfig.getProductCode());
        data.put("url", subscribe.getSource());
        data.put("userAgent", subscribe.getUserAgent());
        data.put("contactNumber", subscribe.getMobile());
        data.put("sourceAppName", subscribe.getReferer());
        RequestBody body = RequestBody.create(JSON, data.toString());
        log.info("{}-获取验证码,请求数据=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data);
        Request request = new Request.Builder().url(junboClProperties.getGetSmsUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),content);
            JunboCunLiangSendSmsResult result = mapper.readValue(content, JunboCunLiangSendSmsResult.class);
            if(result.isOK() && result.getData().isOK()){
                return Result.ok("获取验证码成功");
            }

            return Result.error(result.getData()!=null?result.getData().getMsg():result.getMessage());
        } catch (IOException e) {
            log.error("{}-获取验证码,请求异常=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data,e);
            return Result.error("系统异常");
        }
    }

    private Result<?> checkSms(Subscribe subscribe) {
        JunboClConfig junboClConfig=junboClProperties.getJunboClConfigByChannel(subscribe.getChannel());
        ObjectNode data = mapper.createObjectNode();
        data.put("pid", Long.valueOf(junboClConfig.getPid()));
        data.put("sysOrderId", subscribe.getIspOrderNo());
        data.put("productCode", junboClConfig.getProductCode());
        data.put("url", subscribe.getSource());
        data.put("userAgent", subscribe.getUserAgent());
        data.put("contactNumber", subscribe.getMobile());
        data.put("authCode", subscribe.getSmsCode());
        data.put("sourceAppName", subscribe.getReferer());
        RequestBody body = RequestBody.create(JSON, data.toString());
        log.info("{}-校验验证码,请求数据=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data);
        Request request = new Request.Builder().url(junboClProperties.getCheckSmsUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-校验验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),content);
            JunboCunLiangCheckSmsResult result = mapper.readValue(content, JunboCunLiangCheckSmsResult.class);
            if(result.isOK() && result.getData().isOK()){
                return Result.ok("校验验证码成功");
            }
            return Result.error(result.getData()!=null?result.getData().getMsg():result.getMessage());
        } catch (IOException e) {
            log.error("{}-校验验证码,请求异常=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data,e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<?> submitSms(Subscribe subscribe) {
        JunboClConfig junboClConfig=junboClProperties.getJunboClConfigByChannel(subscribe.getChannel());
        if(junboClConfig.isCheck()){
            Result<?> result=checkSms(subscribe);
            if(!result.isOK()){
                return result;
            }
        }

        ObjectNode data = mapper.createObjectNode();
        data.put("pid", Long.valueOf(junboClConfig.getPid()));
        data.put("sysOrderId", subscribe.getIspOrderNo());
        data.put("productCode", junboClConfig.getProductCode());
        data.put("url", subscribe.getSource());
        data.put("userAgent", subscribe.getUserAgent());
        data.put("contactNumber", subscribe.getMobile());
        data.put("authCode", subscribe.getSmsCode());
        data.put("sourceAppName", subscribe.getReferer());
        RequestBody body = RequestBody.create(JSON, data.toString());
        log.info("{}-提交验证码,请求数据=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data);
        Request request = new Request.Builder().url(junboClProperties.getSmsCodeUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),content);
            JunboCunLiangSmsCodeResult result = mapper.readValue(content, JunboCunLiangSmsCodeResult.class);
            if(result.isOK() && result.getData().isOK()){
                return Result.ok("提交验证码成功");
            }
            return Result.error(result.getData()!=null?result.getData().getMsg():result.getMessage());
        } catch (IOException e) {
            log.error("{}-提交验证码,请求异常=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data,e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<?> queryRights(Subscribe subscribe) {
        JunboClConfig junboClConfig=junboClProperties.getJunboClConfigByChannel(subscribe.getChannel());
        ObjectNode data = mapper.createObjectNode();
        data.put("pid", Long.valueOf(junboClConfig.getPid()));
        data.put("sysOrderId", subscribe.getIspOrderNo());
        data.put("productCode", junboClConfig.getProductCode());
        data.put("contactNumber", subscribe.getMobile());
        RequestBody body = RequestBody.create(JSON, data.toString());
        log.info("{}-查询权益,请求数据=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data);
        Request request = new Request.Builder().url(junboClProperties.getQueryRightsUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询权益,响应数据=>手机号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),content);
            JunboCunLiangRightsResult result = mapper.readValue(content, JunboCunLiangRightsResult.class);
            if(result.isOK() && result.getData()!=null){
                return Result.ok(result.getData().getData());
            }
            return Result.error(result.getMessage());
        } catch (IOException e) {
            log.error("{}-查询权益,请求异常=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data,e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<?> smsRights(Subscribe subscribe) {
        JunboClConfig junboClConfig=junboClProperties.getJunboClConfigByChannel(subscribe.getChannel());
        ObjectNode data = mapper.createObjectNode();
        data.put("pid", Long.valueOf(junboClConfig.getPid()));
        data.put("sysOrderId", subscribe.getIspOrderNo());
        data.put("productCode", junboClConfig.getProductCode());
        data.put("rightsActId", subscribe.getRightsId());
        data.put("rightsGoodId", subscribe.getServiceId());
        data.put("contactNumber", subscribe.getMobile());
        RequestBody body = RequestBody.create(JSON, data.toString());
        log.info("{}-权益领取获取验证码,请求数据=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data);
        Request request = new Request.Builder().url(junboClProperties.getSmsRightsCodeUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-权益领取获取验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),content);
            JunboCunLiangResult result = mapper.readValue(content, JunboCunLiangResult.class);
            if(result.isOK() && result.getData().isOK()){
                return Result.ok("权益领取获取验证码成功");
            }
            return Result.error(result.getMessage());
        } catch (IOException e) {
            log.error("{}-权益领取获取验证码,请求异常=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data,e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<?> rightsOrder(Subscribe subscribe) {
        JunboClConfig junboClConfig=junboClProperties.getJunboClConfigByChannel(subscribe.getChannel());
        ObjectNode data = mapper.createObjectNode();
        data.put("pid", Long.valueOf(junboClConfig.getPid()));
        data.put("sysOrderId", subscribe.getIspOrderNo());
        data.put("productCode", junboClConfig.getProductCode());
        data.put("rightsActId", subscribe.getRightsId());
        data.put("rightsGoodId", subscribe.getServiceId());
        data.put("authCode",subscribe.getSmsCode());
        data.put("contactNumber", subscribe.getMobile());
        RequestBody body = RequestBody.create(JSON, data.toString());
        log.info("{}-权益领取下单,请求数据=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data);
        Request request = new Request.Builder().url(junboClProperties.getRightsOrderUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-权益领取下单,响应数据=>手机号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),content);
            JunboCunLiangResult result = mapper.readValue(content, JunboCunLiangResult.class);
            if(result.isOK() && result.getData().isOK()){
                return Result.ok("权益领取下单成功");
            }
            return Result.error(result.getMessage());
        } catch (IOException e) {
            log.error("{}-权益领取下单,请求异常=>手机号:{},请求参数:{}",LOG_TAG,subscribe.getMobile(),data,e);
            return Result.error("系统异常");
        }
    }

    @Override
    public String junboCunLiangNotify(JunboCunLiangNotify junboCunLiangNotify) {
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, junboCunLiangNotify.getSysOrderId()).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe==null){
            log.warn("{}-订单不存在=>订单信息:{}",LOG_TAG,junboCunLiangNotify);
            return "ok";
        }
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setModifyTime(new Date());
        if (SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus()) && junboCunLiangNotify.isOK()) {
            String result = "业务开通成功";
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setResult(result);
            upd.setOpenTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            subscribeService.saveChannelLimit(subscribe);
            if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }else{
                //外部渠道加入回调延迟队列(暂时不使用队列)
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            return "ok";
        }else if(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus()) && junboCunLiangNotify.isFail()) {
            //外部渠道加入回调延迟队列(暂时不使用队列)
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            String result = "{\"resCode\":\""+junboCunLiangNotify.getOrderStatus()+"\",\"resMsg\":\""+junboCunLiangNotify.getOrderStatusMsg()+"\"}";
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(result);
            upd.setOpenTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return "ok";
        }else if(junboCunLiangNotify.isUnSub()) {
            String result = "退订";
            upd.setResult(result);
            upd.setVerifyStatusDaily(SUBSCRIBE_MONTH_VERIFY_NONE);
            subscribeService.updateSubscribeDbAndEs(upd);
            try {
                blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
            } catch (JsonProcessingException e) {
                log.error("{}-添加黑名单异常=>手机号:{},订单信息:{}",LOG_TAG,subscribe.getMobile(),junboCunLiangNotify,e);
            }
            return "ok";
        }
        log.warn("{}-订单状态异常=>手机号:{},订单信息:{}",LOG_TAG,subscribe.getMobile(),junboCunLiangNotify);
        return "ok";
    }
}
