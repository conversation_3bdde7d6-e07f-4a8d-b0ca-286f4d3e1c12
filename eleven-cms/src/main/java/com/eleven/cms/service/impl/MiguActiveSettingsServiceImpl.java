package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.MiguActiveSettings;
import com.eleven.cms.mapper.MiguActiveSettingsMapper;
import com.eleven.cms.service.IMiguActiveSettingsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *  Service实现
 *
 * <AUTHOR>
 * @date 2025-01-24 14:07:31
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class MiguActiveSettingsServiceImpl extends ServiceImpl<MiguActiveSettingsMapper, MiguActiveSettings> implements IMiguActiveSettingsService {

}
