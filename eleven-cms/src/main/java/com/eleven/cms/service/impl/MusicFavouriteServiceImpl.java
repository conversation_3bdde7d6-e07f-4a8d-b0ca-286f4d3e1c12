package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.eleven.cms.entity.Column;
import com.eleven.cms.entity.Music;
import com.eleven.cms.entity.MusicFavourite;
import com.eleven.cms.mapper.ColumnMapper;
import com.eleven.cms.mapper.MusicFavouriteMapper;
import com.eleven.cms.service.IColumnService;
import com.eleven.cms.service.IMusicFavouriteService;
import com.eleven.cms.vo.ColumnDetail;
import com.eleven.cms.vo.MusicFavouriteVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: cms_music_favourite
 * @Author: jeecg-boot
 * @Date:   2022-03-01
 * @Version: V1.0
 */
@Service
public class MusicFavouriteServiceImpl extends ServiceImpl<MusicFavouriteMapper, MusicFavourite> implements IMusicFavouriteService {

    @Autowired
    private MusicFavouriteMapper musicFavouriteMapper;
    @Autowired
    private ColumnMapper columnMapper;
    @Autowired
    private IColumnService columnService;
    @Override
    public List<ColumnDetail> matching(String mobile) {
        LambdaQueryChainWrapper<Column> wrapper = null;
        List<String> columnIds = musicFavouriteMapper.matching(mobile);
        if(columnIds == null || columnIds.size() == 0){
           //返回默认歌曲列表
            wrapper = columnService.lambdaQuery().last("limit 1");
        }else{
            wrapper = columnService.lambdaQuery().in(Column::getId,columnIds);
        }
        return wrapper.orderByAsc(Column::getPriority)
                .list()
                .stream()
                .map(column -> {
                    ColumnDetail columnDetail = new ColumnDetail();
                    BeanUtils.copyProperties(column, columnDetail);
                    List<Music> musicList = columnMapper.getMusicListById(column.getId());
                    columnDetail.setMusicList(musicList);
                    return columnDetail;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<MusicFavouriteVo> findByMobile(String mobile) {
        return musicFavouriteMapper.findByMobile(mobile);
    }
}
