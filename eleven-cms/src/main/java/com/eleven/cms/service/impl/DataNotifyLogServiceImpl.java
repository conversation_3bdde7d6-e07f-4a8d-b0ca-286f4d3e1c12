package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.dto.DataNotifyVrbtResult;
import com.eleven.cms.entity.DataNotifyLog;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.DataNotifyLogMapper;
import com.eleven.cms.queue.DataNotifyDelayedMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.service.IDataNotifyLogService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.qycl.entity.EntVrbtResult;
import com.eleven.qycl.entity.QyclCompany;
import com.eleven.qycl.service.EnterpriseVrbtService;
import com.eleven.qycl.service.IQyclCompanyService;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_3_DAY;
import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_60_MIN;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @Description: 数据回执记录
 * @Author: jeecg-boot
 * @Date:   2023-04-23
 * @Version: V1.0
 */
@Slf4j
@Service
public class DataNotifyLogServiceImpl extends ServiceImpl<DataNotifyLogMapper, DataNotifyLog> implements IDataNotifyLogService {
    @Autowired
    IQyclCompanyService qyclCompanyService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    EnterpriseVrbtService enterpriseVrbtService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Override
    public void saveDataNotifyLog(String mobile, String source, String type, String companyOwner, String state,String desc, String operSystem, String finishedTime) {
        QyclCompany company=qyclCompanyService.lambdaQuery().eq(QyclCompany::getMobile,mobile).orderByDesc(QyclCompany::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        DataNotifyLog dataNotifyLog=new DataNotifyLog();
        dataNotifyLog.setMobile(mobile);
        dataNotifyLog.setSource(source);
        if(company!=null && StringUtils.isNotBlank(company.getChannel())){
            dataNotifyLog.setChannel(company.getChannel());
        }
        dataNotifyLog.setState(state);
        dataNotifyLog.setDescMsg(desc);

        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getBizType,BizConstant.BIZ_TYPE_QYCL).eq(Subscribe::getStatus,SUBSCRIBE_STATUS_SUCCESS).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(subscribe!=null && StringUtils.isNotBlank(subscribe.getChannel())){
            if(StringUtils.isBlank(dataNotifyLog.getChannel())){
                dataNotifyLog.setChannel(subscribe.getChannel());
            }
            dataNotifyLog.setProvince(subscribe.getProvince());
            dataNotifyLog.setCity(subscribe.getCity());
        }
        if("000000".equals(state)){
            dataNotifyLog.setStatus(Integer.valueOf(type));
        }else{
            dataNotifyLog.setStatus(0);
            //退订失败数据丢弃,不作保存
            if(QyclConstant.QYCL_FUN_STATUS_UNSUB.equals(type)){
               return;
            }
        }
        dataNotifyLog.setCompanyOwner(companyOwner);
        dataNotifyLog.setOperSystem(operSystem);
        try {
            dataNotifyLog.setFinishedTime(StringUtils.isNotBlank(finishedTime)?DateUtil.stringToDate(finishedTime,DateUtil.FULL_TIME_PATTERN):null);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        this.save(dataNotifyLog);
        if(QyclConstant.QYCL_FUN_STATUS_ORDER.equals(type) && "000000".equals(state)){
            this.addDataNotifyDelay(mobile,dataNotifyLog.getId());
        }
    }


    /**
     * 添加包月状态延迟校验任务
     * @param mobile
     * @param id
     */
    private void addDataNotifyDelay(String mobile,String id) {
        log.info("企业彩铃数据回执添加包月状态延迟校验任务==>手机号:{},主键ID:{}", mobile,id);
        //延迟队列
        rabbitMQMsgSender.sendDataNotifyDelay1hourMessage(DataNotifyDelayedMessage.builder().id(id).msisdn(mobile).msg("包月状态延迟60分钟校验").extra(MESSAG_EXTRA_60_MIN).build());
        rabbitMQMsgSender.sendDataNotifyDelay1DayMessage(DataNotifyDelayedMessage.builder().id(id).msisdn(mobile).msg("包月状态延迟1天校验").extra(MESSAG_EXTRA_3_DAY).build());

    }

    /**
     * 消息队列通知修改数据
     * @param msisdn
     * @param id
     * @param extra
     */
    @Override
    public void receiveDataNotifyDelayMsg(String msisdn,String id,String extra){
        DataNotifyLog dataNotifyLog=this.lambdaQuery().eq(DataNotifyLog::getId,id).orderByDesc(DataNotifyLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(dataNotifyLog==null){
            return;
        }
        Integer verifyStatus=0;
        final EntVrbtResult entVrbtResult = enterpriseVrbtService.queryContentMembersImmediateByChannel("",msisdn,dataNotifyLog.getCompanyOwner(),dataNotifyLog.getChannel());
        if (entVrbtResult.isOK() && entVrbtResult.getData()!=null) {
            List<DataNotifyVrbtResult> dataNotifyVrbtResultList= null;
            try {
                dataNotifyVrbtResultList = new ObjectMapper().readValue(entVrbtResult.getData().toString(), List.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            if(dataNotifyVrbtResultList.size()>0 && dataNotifyVrbtResultList!=null){
                List<DataNotifyVrbtResult> list= JSON.parseArray(JSON.toJSONString(dataNotifyVrbtResultList), DataNotifyVrbtResult.class);
                Optional<DataNotifyVrbtResult> dataNotifyVrbtResult=list.stream().collect(Collectors.toList()).stream().max(Comparator.comparing(DataNotifyVrbtResult::getBillNum));
                if(dataNotifyVrbtResult.isPresent() && StringUtils.equals("02",dataNotifyVrbtResult.get().getUserStatus())){
                    verifyStatus=1;
                }
            }
        }
        if (StringUtils.equals(MESSAG_EXTRA_60_MIN,extra)) {
            dataNotifyLog.setVerifyStatus(verifyStatus);
            dataNotifyLog.setUpdateTime(new Date());
        } else if (StringUtils.equals(MESSAG_EXTRA_3_DAY,extra)) {
            dataNotifyLog.setVerifyStatusDaily(verifyStatus);
            dataNotifyLog.setUpdateTime(new Date());
        }
        this.lambdaUpdate().eq(DataNotifyLog::getId, dataNotifyLog.getId()).update(dataNotifyLog);
    }
}
