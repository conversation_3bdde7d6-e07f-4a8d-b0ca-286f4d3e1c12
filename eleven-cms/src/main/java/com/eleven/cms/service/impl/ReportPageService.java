package com.eleven.cms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.abstracts.AbstractOrder;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.ReportPageChannelRegisterTable;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.qycl.util.QyclConstant;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.IPUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.*;

/**
 * <AUTHOR>
 * @datetime 2024/11/25 11:07
 */
@Service
@RequiredArgsConstructor
public class ReportPageService {

    private final MobileRegionService mobileRegionService;
    private final ISubscribeService subscribeService;

    public Result<Object> getSmsCode(JsonNode jsonNode, HttpServletRequest request) {
        // 1.创建订单
        Subscribe subscribe = createSubScribe(jsonNode, request);
        // 2.获取注册表信息
        Class clazz = ReportPageChannelRegisterTable.getRegisterTable().get(jsonNode.get("channel").asText());
        if (clazz == null) {
            return Result.error("未配置渠道！");
        }
        // 3.发送短信
        Result result = ((AbstractOrder) SpringUtil.getBean(clazz)).getSmsCode(jsonNode);
        return result.isOK()? Result.ok((Object) subscribe.getId()) : Result.error(result.getMessage());
    }

    public Result order(JsonNode jsonNode) {
        // 1.修改订单状态为已提交短信验证码
        Subscribe subscribe = new Subscribe();
        subscribe.setId(jsonNode.get("id").asText());
        subscribe.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
        subscribe.setModifyTime(new Date());
        subscribeService.updateSubscribeDbAndEs(subscribe);
        // 2.下单
        return ((AbstractOrder) SpringUtil.getBean(ReportPageChannelRegisterTable.getRegisterTable()
                .get(jsonNode.get("channel").asText())))
                .order(jsonNode);
    }

    /**
     * 创建订单
     *
     * @param jsonNode jsonNode
     * @param request request
     * @return Subscribe
     */
    private Subscribe createSubScribe(JsonNode jsonNode, HttpServletRequest request) {
        String mobile = jsonNode.get("mobile").asText();
        String channel = jsonNode.get("channel").asText();

        Subscribe subscribe = new Subscribe();
        subscribe.setMobile(mobile);
        subscribe.setChannel(channel);
        subscribe.setReferer(request.getHeader("x-requested-with"));
        subscribe.setIp(IPUtils.getIpAddr(request));
        subscribe.setCreateTime(new Date());
        subscribe.setQyclCompanyOwner(getCompanyOwner(request));
        subscribe.setBizType(getBizTypeByMiguChannel(subscribe.getChannel()));
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        subscribe.setProvince(mobileRegionResult.getProvince());
        subscribe.setCity(mobileRegionResult.getCity());
        subscribe.setIsp(mobileRegionResult.getOperator());
        subscribe.setStatus(SUBSCRIBE_STATUS_INIT);
        subscribeService.updateSubscribeDbAndEs(subscribe);
        return subscribe;
    }

    public String getCompanyOwner(HttpServletRequest request) {
        String companyOwner = request.getHeader(QyclConstant.QYCL_COMPANY_OWNER_HEAD);
        if (org.apache.commons.lang3.StringUtils.isEmpty(companyOwner)) {
            companyOwner = QyclConstant.QYCL_COMPANY_OWNER_YRJY;
        }
        return companyOwner;
    }
}
