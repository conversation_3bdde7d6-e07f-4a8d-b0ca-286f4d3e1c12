package com.eleven.cms.service.impl;

import com.eleven.cms.service.ISecurityService;
import com.eleven.cms.shanghaimobile.entity.Aedk;
import com.eleven.cms.shanghaimobile.entity.Ask;
import com.eleven.cms.shanghaimobile.entity.GroupKey;
import com.eleven.cms.shanghaimobile.entity.ServerResponse;
import com.eleven.cms.shanghaimobile.mobileenum.ResponseStatus;
import com.eleven.cms.shanghaimobile.properties.ShanghaiMobileProperties;
import com.eleven.cms.shanghaimobile.util.Base64Util;
import com.eleven.cms.shanghaimobile.util.CryptoUtil;
import com.eleven.cms.shanghaimobile.util.JsonUtil;
import com.eleven.cms.shanghaimobile.util.RandomUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLDecoder;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/8 14:19
 **/
@Slf4j
@Service
public class SecurityIServiceImpl implements ISecurityService {
    public static final String AUTH_LOG_TAG ="上海移动[业务鉴权]";
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private ShanghaiMobileProperties shanghaiMobileProperties;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder().proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort))).build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
    }

    @Override
    public Ask getAsk(String mobile){
        String shanghaiMobileAppCodeRedisKey = "cms:cache:shyd:mobile:" + shanghaiMobileProperties.getAppCode();
        if (!redisUtil.hasKey(shanghaiMobileAppCodeRedisKey)) {
            return apply(mobile).getAsk();
        }
        GroupKey groupKey=(GroupKey)redisUtil.get(shanghaiMobileAppCodeRedisKey);
        if (groupKey == null || groupKey.getAsk().getEndTime().getTime() < System.currentTimeMillis()) {
            return apply(mobile).getAsk();
        }
        return groupKey.getAsk();
    }

    @Override
    public Aedk getAedk(String mobile){
        String shanghaiMobileAppCodeRedisKey = "cms:cache:shyd:mobile:" + shanghaiMobileProperties.getAppCode();
        if (!redisUtil.hasKey(shanghaiMobileAppCodeRedisKey)) {
            return apply(mobile).getAedk();
        }
        GroupKey groupKey=(GroupKey)redisUtil.get(shanghaiMobileAppCodeRedisKey);
        if(groupKey == null || groupKey.getAedk().getEndTime().getTime() < System.currentTimeMillis()){
            return apply(mobile).getAedk();
        }
        return groupKey.getAedk();
    }

    @Override
    public String encrypt(String text,String mobile){
        byte[] secretByte = Base64Util.decode(getAedk(mobile).getValue());
        SecretKey secretKey = new SecretKeySpec(secretByte, "DESede");
        String otext = CryptoUtil.encrypt(text, "DESede", secretKey);
        return otext;
    }

    @Override
    public String decrypt(String text,String mobile){
        byte[] secretByte = Base64Util.decode(getAedk(mobile).getValue());
        SecretKey secretKey = new SecretKeySpec(secretByte, "DESede");
        String otext = CryptoUtil.decrypt(text, "DESede", secretKey);
        return otext;
    }


    @SuppressWarnings("deprecation")
    private GroupKey apply(String mobile){
        String httpUrl = null;
        try {
            httpUrl = shanghaiMobileProperties.getSecurityUrl() + "/1.0/KEY/" + shanghaiMobileProperties.getAppCode() + "/"+ URLDecoder.decode(createSalt(shanghaiMobileProperties.getAppApk()),"UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.error("{}-请求异常decode,手机号:{},url:{}",AUTH_LOG_TAG,mobile,httpUrl,e);
        }
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-响应成功,手机号:{},jsonResp: {}",AUTH_LOG_TAG,mobile,content);
            if(StringUtils.isBlank(content)){
                return null;
            }
            ServerResponse sr = JsonUtil.toBean(content, ServerResponse.class);
            if (ResponseStatus.SUCCESS.toString().equals(sr.getStatus())) {
                byte[] secretByte = Base64Util.decode(shanghaiMobileProperties.getAppApk());
                SecretKey secretKey = new SecretKeySpec(secretByte, "DESede");
                String otext = CryptoUtil.decrypt(sr.getResult(), "DESede", secretKey);
                GroupKey gk = JsonUtil.toBean(otext, GroupKey.class);
                String shanghaiMobileAppCodeRedisKey = "cms:cache:shyd:mobile:" + shanghaiMobileProperties.getAppCode();
                redisUtil.set(shanghaiMobileAppCodeRedisKey,gk);
                return gk;
            }
        } catch (Exception e) {
            log.error("{}-请求异常,手机号:{},url:{}",AUTH_LOG_TAG,mobile,httpUrl,e);
        }
        return null;
    }
    /**
     * 创建盐值
     *
     * @param apk
     * @return
     * @throws EnumConstantNotPresentException
     */
    private String createSalt(String apk) {
        String securitySalt = null;
        String randomSalt = RandomUtil.getRandomString(30, 2);
        byte[] apkByte = Base64Util.decode(apk);
        SecretKey secretKey = new SecretKeySpec(apkByte, "DESede");
        securitySalt = CryptoUtil.encrypt(randomSalt, "DESede", secretKey);
        if (securitySalt.contains("/") || securitySalt.contains("+") ||  securitySalt.contains(" ")|| securitySalt.contains("?")|| securitySalt.contains("%")|| securitySalt.contains("#")|| securitySalt.contains("&")) {
            securitySalt = createSalt(apk);
        }
        return securitySalt;
    }


    @Override
    public String sign(String text,String mobile){
        try {
            Signature  signaturer = Signature.getInstance("MD5withRSA");
            signaturer.initSign(getPrivateKey(getAsk(mobile).getPrivateKeyStr()));
            signaturer.update(text.getBytes());
            String  signValue = Base64Util.encodeString(signaturer.sign());
            return signValue;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }catch (SignatureException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public boolean verify(String text, String signValue, String publicKey,String mobile){
        try {
            Signature signaturer = Signature.getInstance("MD5withRSA");
            signaturer.initVerify(getPublicKey(publicKey));
            signaturer.update(text.getBytes());
            if (!signaturer.verify(Base64Util.decode(signValue))) {
                return false;
            } else {
                return true;
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }catch (SignatureException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 通过公钥值还原成公钥
     *
     * @param publicKeyStr
     * @return
     * @throws InvalidKeySpecException
     * @throws NoSuchAlgorithmException
     */
    private static PublicKey getPublicKey(String publicKeyStr){
        try {
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64Util.decode(publicKeyStr));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey pubKey = keyFactory.generatePublic(keySpec);
            return pubKey;
        }catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 通过私钥还原成私钥
     *
     * @param privateKeyStr
     * @return
     * @throws InvalidKeySpecException
     * @throws NoSuchAlgorithmException
     */
    private static PrivateKey getPrivateKey(String privateKeyStr){
        try {
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64Util.decode(privateKeyStr));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
            return priKey;
        }catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        }
        return null;

    }
}
