package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PageBusinessConfigSelf;
import com.eleven.cms.mapper.PageBusinessConfigSelfMapper;
import com.eleven.cms.service.IPageBusinessConfigSelfService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2023-08-17 16:07
 */
@Service
public class PageBusinessConfigSelfServiceImpl extends ServiceImpl<PageBusinessConfigSelfMapper, PageBusinessConfigSelf> implements IPageBusinessConfigSelfService {
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_POP_SELF_CACHE, key = "#root.methodName + '-' + #id", unless = "#result==null")
    public PageBusinessConfigSelf selectById(String id) {
        return this.getById(id);
    }
}
