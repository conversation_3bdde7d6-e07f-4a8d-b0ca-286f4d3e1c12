package com.eleven.cms.service.impl.pay;

import com.eleven.cms.aiunion.entity.DigitalRightOrder;
import com.eleven.cms.aiunion.entity.DigitalRightsPackages;
import com.eleven.cms.aiunion.service.IDigitalRightOrderService;
import com.eleven.cms.aiunion.service.IDigitalRightsPackagesService;
import com.eleven.cms.dto.Rights;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.remote.JunboApiService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.pay.PayNotifyService;
import com.eleven.cms.util.BizConstant;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class DigitalRightsNotifyServiceImpl implements PayNotifyService {

    @Resource
    IDigitalRightOrderService digitalRightOrderService;

    @Resource
    IDigitalRightsPackagesService digitalRightsPackagesService;

    @Resource
    JunboApiService junboApiService;

    @Resource
    private IJunboChargeLogService iJunboChargeLogService;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public void handleNotify(String orderNo, Map<String,String> requestParams) {
        paySuccess(orderNo);
    }

    public Integer paySuccess(String orderNo) {
        DigitalRightOrder rightOrder = digitalRightOrderService.getByOrderNo(orderNo);
        if(rightOrder==null){
            return 0;
        }
        //如果状态是已经支付,则返回
        if(Objects.equals(rightOrder.getPayStatus(), PayStatueEnum.PAID.getPayType())){
            return rightOrder.getPayStatus();
        }

        DigitalRightsPackages packages = digitalRightsPackagesService.getById(rightOrder.getPackageId());
        String mobile = rightOrder.getMobile();
        String productName = packages.getProductName();
        String productCode = packages.getProductCode();
        String rechargeDeviceRedisKey = "cms:aiunion:createOrder:pay:antiHeavy:" + rightOrder.getClientId();
        redisUtil.setIfAbsent(rechargeDeviceRedisKey, rightOrder.getClientId(), 60 * 60 * 24 * 30);

        String rechargeMobileRedisKey = "cms:aiunion:createOrder:pay:mobile:" + rightOrder.getMobile();
        redisUtil.setIfAbsent(rechargeMobileRedisKey, rightOrder.getMobile(), 60 * 60 * 24 * 30);

        log.info("handleNotify 设置设备支付校验缓存成功 clientId:{}", rightOrder.getClientId());
        //保存日志
//        Rights rights = buildRights(productCode, packages);
//        JunboChargeLog junboChargeLog = iJunboChargeLogService.buildJunboChargeLog(mobile, mobile, BizConstant.BIZ_DO_CHANNEL_RECHARGE, rights, 0, new Date(), "权益商城充值", productName);

        //调用充值接口
//        JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(productCode, orderNo, mobile, mobile, BizConstant.BIZ_DO_CHANNEL_RECHARGE, productName);
//        log.info("junBo返回结果{}", JSONObject.toJSONString(junboRespon));
//        rightOrder.setRechargeResult(JSONObject.toJSONString(junboRespon));
//        junboChargeLog.setRespCode(junboRespon.getCode());
//        junboChargeLog.setRespMsg(junboRespon.getMsg());
//        iJunboChargeLogService.updateById(junboChargeLog);

        //更新支付状态
        rightOrder.setPayStatus(PayStatueEnum.PAID.getPayType());
        digitalRightOrderService.updateById(rightOrder);
        return rightOrder.getPayStatus();
    }

    @Override
    public PayBusineesTypeEnum getBusinessType() {
        return PayBusineesTypeEnum.DIGITAL_RIGHTS;
    }

    private Rights buildRights(String rightsId,DigitalRightsPackages packages) {
        Rights rights = new Rights();
        rights.setRechargeSource("web");
        rights.setServiceId(BizConstant.BIZ_DO_CHANNEL_RECHARGE);
        rights.setRightsId(rightsId);
        rights.setRechargeState(0);
        rights.setPayTime(new Date());
        if (Objects.nonNull(packages.getProductPrice())) {
            rights.setProductPrice(BigDecimal.valueOf(100).multiply(packages.getProductPrice()).intValue());
        }
        rights.setCouponId(packages.getProductCode());
        rights.setRightsName(packages.getProductName());
        return rights;
    }
}
