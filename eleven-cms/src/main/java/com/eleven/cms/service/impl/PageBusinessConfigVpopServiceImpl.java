package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PageBusinessConfigVpop;
import com.eleven.cms.mapper.PageBusinessConfigVpopMapper;
import com.eleven.cms.service.IPageBusinessConfigVpopService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2023-08-17 16:07
 */
@Service
public class PageBusinessConfigVpopServiceImpl extends ServiceImpl<PageBusinessConfigVpopMapper, PageBusinessConfigVpop> implements IPageBusinessConfigVpopService {
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_POP_CACHE, key = "#root.methodName + '-' + #id", unless = "#result==null")
    public PageBusinessConfigVpop selectById(String id) {
        return this.getById(id);
    }
}
