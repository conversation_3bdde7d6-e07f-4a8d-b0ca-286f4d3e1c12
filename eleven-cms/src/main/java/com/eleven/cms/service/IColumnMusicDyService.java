package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.ColumnMusicDy;
import com.eleven.cms.entity.ColumnMusicDyVo;
import com.eleven.cms.vo.MusicDyVo;

import java.util.List;

/**
 * @Description: 订阅包栏目歌曲
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
public interface IColumnMusicDyService extends IService<ColumnMusicDy> {

	public List<ColumnMusicDy> selectByMainId(String mainId);

	IPage<MusicDyVo> selectColumnMusicDyVoPage(Page<MusicDyVo> page, String columnId);

	List<ColumnMusicDyVo> selectByMainIdNew(String id);

	void importMusic(List<ColumnMusicDyVo> list, String id);
}
