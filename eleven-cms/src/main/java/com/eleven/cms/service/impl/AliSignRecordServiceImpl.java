package com.eleven.cms.service.impl;

import com.alipay.api.response.AlipayUserAgreementExecutionplanModifyResponse;
import com.alipay.api.response.AlipayUserAgreementQueryResponse;
import com.alipay.api.response.AlipayUserAgreementUnsignResponse;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.ad.AdReportService;
import com.eleven.cms.config.AliSignUpConfig;
import com.eleven.cms.entity.*;
import com.eleven.cms.mapper.AliSignRecordMapper;
import com.eleven.cms.queue.AlipayDeductMessage;
import com.eleven.cms.queue.AlipayDeductSendMessage;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.DianxinVrbtService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.IvrResult;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.queue.RedisDelayedQueueManager.*;

/**
 * @Description: cms_ali_sign_record
 * @Author: jeecg-boot
 * @Date:   2022-12-29
 * @Version: V1.0
 */
@Service
@Slf4j
public class AliSignRecordServiceImpl extends ServiceImpl<AliSignRecordMapper, AliSignRecord> implements IAliSignRecordService {
    private static final Integer ORDER_COUNT=5;
    //未支付
    private static final Integer NO_PAY_STATUS=0;
    //已支付
    private static final Integer PAY_STATUS_SUCCESS=1;
    //退款失败
    private static final Integer REFUND_FAIL=2;
    //未退款
    private static final Integer NOT_REFUND=0;
    //退款成功
    private static final Integer REFUND_SUCCESS=1;
    //退款中
    private static final Integer REFUND_PREPARE=-1;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IAlipayService alipayService;
    @Autowired
    private DianxinVrbtService dianxinVrbtService;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    AliSignUpConfig aliSignUpConfig;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IAlipayComplainService alipayComplainService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    AdReportService adReportService;
    private static final Interner<String> interner = Interners.newWeakInterner();
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public Date saveSignRecord(Subscribe subscribe, String externalAgreementNo,String appId,String businessType,String businessName,String bizType) {
        AliSignRecord aliSignRecord = new AliSignRecord();
        aliSignRecord.setMobile(subscribe.getMobile());
        aliSignRecord.setExternalAgreementNo(externalAgreementNo);
        aliSignRecord.setSignStatus(0);
        aliSignRecord.setSignUpTime(new Date());
        aliSignRecord.setAppId(appId);
        aliSignRecord.setBusinessType(businessType);
        aliSignRecord.setBusinessName(businessName);
        aliSignRecord.setSubChannel(subscribe.getSubChannel());
        aliSignRecord.setBizType(bizType);
        this.save(aliSignRecord);

        //保存sub信息
        try{
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if(StringUtils.isNotBlank(subscribe.getSource())){
                subscribe = parseLink(subscribe.getSource(),subscribe);
            }
            subscribe.setProvince(mobileRegionResult!=null?mobileRegionResult.getProvince():"未知");
            subscribe.setCity(mobileRegionResult!=null?mobileRegionResult.getCity():"未知");
            String isp = mobileRegionResult!=null?mobileRegionResult.getOperator():"未知";
            subscribe.setIsp(isp);
            subscribe.setIspOrderNo(externalAgreementNo);
            subscribe.setChannel(businessType);
            subscribe.setBizType(bizType);
            subscribe.setResult("未签约");
            subscribe.setCreateTime(new Date());
            final String transactionId = subscribe.getTransactionId();
            if (StringUtils.isNotBlank(transactionId)) {
                subscribe.setId(transactionId);
                subscribeService.updateSubscribeDbAndEs(subscribe);
                return aliSignRecord.getNextDeductTime();
            }
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
            subscribeService.createSubscribeDbAndEs(subscribe);
        } catch (Exception e) {
            log.error("支付宝签约sub订单出错：",e);
        }
       return aliSignRecord.getNextDeductTime();
    }

    @Override
    public void updateStatus(AliSignRecord aliSignRecord,String outTradeNo) {
        this.updateById(aliSignRecord);
        //修改sub信息
        Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, aliSignRecord.getExternalAgreementNo()).last("limit 1").one();
//        subscribe.setStatus(aliSignRecord.getSignStatus() == 1 ? SUBSCRIBE_STATUS_SUCCESS : SUBSCRIBE_STATUS_FAIL);
        if(subscribe!=null){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            if(aliSignRecord.getSignStatus() == 3){
                upd.setResult("解约成功");
                //退订上报广告平台
//                adReportService.unsubReport(subscribe.getDeviceInfo(), subscribe.getSubChannel(), subscribe.getMobile(), subscribe.getSource(), subscribe, "MESSAG_EXTRA_UNSUB_1_DAY");
            }else if(aliSignRecord.getSignStatus() ==  4){
                upd.setResult("解约失败");
            }else{
                upd.setOpenTime(new Date());
                upd.setResult(aliSignRecord.getSignStatus() == 1 ? "签约成功" : "签约失败");
            }
            if(aliSignRecord.getSignStatus() == 1){
                //签约成功添加包月状态校验的消息队列
                rabbitMQMsgSender.sendSubscribeVerifyDelay1hourMessage(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY).id(subscribe.getId()).msg("包月状态延迟60分钟校验").extra(MESSAG_EXTRA_60_MIN).build());
                rabbitMQMsgSender.sendSubscribeVerifyDelay3DayMessage(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_VERIFY).id(subscribe.getId()).msg("包月状态延迟3天校验").extra(MESSAG_EXTRA_3_DAY).build());
            }
            if(StringUtils.isNotBlank(outTradeNo)){
                upd.setRemark(outTradeNo);
            }
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        //更新订单签约状态
        AliSignChargingOrder aliSignChargingOrder=aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getExternalAgreementNo, aliSignRecord.getExternalAgreementNo()).orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(aliSignChargingOrder!=null){
            if(aliSignRecord.getSignStatus() == 3){
                aliSignChargingOrderService.lambdaUpdate()
                        .eq(AliSignChargingOrder::getExternalAgreementNo, aliSignRecord.getExternalAgreementNo())
                        .set(AliSignChargingOrder::getSignStatus, aliSignRecord.getSignStatus())
                        .set(AliSignChargingOrder::getUnsignTime,aliSignRecord.getUnsignTime())
                        .set(AliSignChargingOrder::getUpdateTime, new Date()).update();
            }else{
                aliSignChargingOrderService.lambdaUpdate()
                        .eq(AliSignChargingOrder::getExternalAgreementNo, aliSignRecord.getExternalAgreementNo())
                        .set(AliSignChargingOrder::getSignStatus, aliSignRecord.getSignStatus())
                        .set(AliSignChargingOrder::getUpdateTime, new Date()).update();
            }
        }
        //更新投诉签约状态
        List<AliSignChargingOrder> aliSignChargingOrderList=aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getExternalAgreementNo, aliSignRecord.getExternalAgreementNo()).list();
        if(aliSignChargingOrderList!=null && aliSignChargingOrderList.size()>0){
            aliSignChargingOrderList.forEach(item -> {
                alipayComplainService.lambdaUpdate()
                        .eq(AlipayComplain::getMerchantOrderNo, item.getOrderNo())
                        .set(AlipayComplain::getSignStatus, aliSignRecord.getSignStatus())
                        .set(AlipayComplain::getUpdateTime, new Date()).update();
            });
        }
    }

    private Subscribe parseLink(String source, Subscribe subscribe) {
        try{
            source = URLDecoder.decode(source, "UTF-8");
            String subChannel = HttpUrl.parse(source.replace("#/", "")).queryParameter("subChannel");
            subscribe.setSubChannel(subChannel);
            subscribe.setSource(source);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return subscribe;
    }

    /**
     * 根据签约号解约
     * @param externalAgreementNo
     * @return
     */
    private Result<?> aliUnSignByExternalAgreementNo(String externalAgreementNo) {
        AliSignRecord aliSignRecord=this.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo,externalAgreementNo).eq(AliSignRecord::getSignStatus,"1").orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
        if(aliSignRecord!=null){
            AlipayUserAgreementUnsignResponse response= alipayService.alipayRescind(aliSignRecord.getAgreementNo(),aliSignRecord.getBusinessType());
            String remark="";
            if(response.isSuccess()){
                remark="解约调用成功=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
            } else {
                remark="解约调用失败=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
            }
            this.lambdaUpdate().eq(AliSignRecord::getId, aliSignRecord.getId()).set(AliSignRecord::getRemark,remark).update();
            //if(response.isSuccess()){
            //    return Result.ok("解约调用成功");
            //}
            //return Result.error("解约调用失败");
            return  Result.ok("已执行解约操作");
        }
        return Result.bizExists("签约数据不存在");
    }
    @Override
    public Result<?> aliUnSign(String mobile) {
        AliSignRecord aliSignRecord=this.lambdaQuery().eq(AliSignRecord::getMobile,mobile).eq(AliSignRecord::getSignStatus,"1").orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
        if(aliSignRecord!=null){
            AlipayUserAgreementUnsignResponse response= alipayService.alipayRescind(aliSignRecord.getAgreementNo(),aliSignRecord.getBusinessType());
            String remark="";
            if(response.isSuccess()){
                remark="解约调用成功=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
            } else {
                remark="解约调用失败=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
            }
            this.lambdaUpdate().eq(AliSignRecord::getId, aliSignRecord.getId()).set(AliSignRecord::getRemark,remark).update();
            //if(response.isSuccess()){
            //    return Result.ok("解约调用成功");
            //}
            //return Result.error("解约调用失败");
            return  Result.ok("已执行解约操作");
        }
        return Result.bizExists("签约数据不存在");
    }

    @Override
    public boolean isAliSignExist(String mobile) {
        return this.lambdaQuery().eq(AliSignRecord::getMobile,mobile).eq(AliSignRecord::getSignStatus,"1").count() > 0;
    }

    //public static void main(String[] args) {
    //    final LocalDate parse = LocalDate.parse("2023-04-01", DateTimeFormatter.ISO_DATE);
    //    System.out.println("parse = " + parse);
    //    final String format = DateTimeFormatter.ISO_DATE.format(LocalDate.now());
    //    System.out.println("format = " + format);
    //    System.out.println(parse.isBefore(LocalDate.now()));
    //    System.out.println(LocalDate.now().isBefore(LocalDate.now()));
    //    System.out.println(LocalDate.now().isAfter(LocalDate.now()));
    //    System.out.println(LocalDate.now().isEqual(LocalDate.now()));
    //
    //}



    /**
     * ivr查询订购和解约信息
     * @param mobile
     * @return
     */
    @Override
    public IvrResult alipayQuerySignAndRefund(String mobile,String bizType){
        final IvrResult error =IvrResult.response(500,"系统繁忙,请稍后再试!");
        Map<Integer, String> IvrMsgMap=BizConstant.IvrMsgMap;
        List<AliSignChargingOrder> aliSignChargingOrderList = queryAlipayOrder(mobile,bizType);
        if(aliSignChargingOrderList==null || aliSignChargingOrderList.isEmpty()){
            log.info("ivr查询订购和解约信息,暂无订单-->手机号:{},状态:{},描述:{}",mobile,201,IvrMsgMap.get(201));
            return IvrResult.response(201,IvrMsgMap.get(201));
        }
        final List<String> businessTypeList=aliSignChargingOrderList.stream().map(AliSignChargingOrder::getBusinessType).collect(Collectors.toList());
        //拒绝接通
        for(String item:businessTypeList){
            if(BizConstant.IvrTurnDownMsgMap.containsKey(item)){
                return IvrResult.response(500,BizConstant.IvrTurnDownMsgMap.get(item));
            }
        }
        boolean isComplain = isComplain(false, aliSignChargingOrderList);
        if(isComplain){
            log.info("ivr查询订购和解约信息,已生成投诉订单-->手机号:{},状态:{},描述:{}",mobile,301,IvrMsgMap.get(301));
            return IvrResult.response(301,IvrMsgMap.get(301));
        }
        Optional<AliSignChargingOrder> aliSignChargingOrderOptional=aliSignChargingOrderList.stream().max(Comparator.comparing(AliSignChargingOrder::getCreateTime));;
        if(aliSignChargingOrderOptional.isPresent()){
            AliSignChargingOrder aliSignChargingOrder= aliSignChargingOrderOptional.get();

            String orderAmount=aliSignChargingOrder.getOrderAmount();
            String businessName=aliSignChargingOrder.getBusinessName();
            if(businessName.contains("(")){
                businessName=businessName.split("\\(")[0];
            }
            String refundTime=aliSignChargingOrder.getRefundTime()==null?"":DateUtil.getDateFormat(aliSignChargingOrder.getRefundTime(),DateUtil.FULL_TIME_HOURS_MINUTES);
            String payTime=aliSignChargingOrder.getPayTime()==null?"":DateUtil.getDateFormat(aliSignChargingOrder.getPayTime(),DateUtil.FULL_TIME_HOURS_MINUTES);
            String businessType=aliSignChargingOrder.getBusinessType();

            if(aliSignChargingOrder.getRefundStatus().equals(NOT_REFUND)){
                String msg=IvrMsgMap.get(101);
                msg=msg.replace("payTime", payTime).replace("businessName", businessName).replace("orderAmount", orderAmount);
                log.info("ivr查询订购和解约信息,未退款-->手机号:{},状态:{},描述:{}",mobile,101,IvrMsgMap.get(101));
                return IvrResult.response(101,msg,payTime,orderAmount,businessName,refundTime);
            }
            if(aliSignChargingOrder.getRefundStatus().equals(REFUND_FAIL)){
                String msg=IvrMsgMap.get(103);
                log.info("ivr查询订购和解约信息,退款失败-->手机号:{},状态:{},描述:{}",mobile,103,IvrMsgMap.get(103));
                return IvrResult.response(103,msg,payTime,orderAmount,businessName,refundTime);
            }

            Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
            if(alipay!=null && alipay.getCustomerService().equals("1") && aliSignChargingOrder.getRefundStatus().equals(REFUND_SUCCESS)){
                alipay.setBizType(bizType);
                //提醒短信
                Map<String,String> smsMap= Maps.newHashMap();
                smsMap.put("refundTime",refundTime);
                smsMap.put("businessName",businessName);
                smsModelService.sendSms(mobile,"IVR_ALIPAY",alipay.getBizType(),BizConstant.BUSINESS_TYPE_RIGHTS,smsMap);
            }
            String msg=IvrMsgMap.get(102);
            msg=msg.replace("payTime", payTime).replace("refundTime", refundTime);
            log.info("ivr查询订购和解约信息,已退款-->手机号:{},状态:{},描述:{}",mobile,102,IvrMsgMap.get(102));
            return IvrResult.response(102,msg,payTime,orderAmount,businessName,refundTime);
        }
        log.info("ivr查询订购和解约信息,订单查询失败,返回系统错误-->手机号:{}",mobile);
        return error;
    }



    /**
     * ivr退款和解约
     * @param mobile
     * @return
     */
    @Override
    public IvrResult alipayUnSignAndRefund(String mobile,String bizType){
        final IvrResult error =IvrResult.response(500,"系统繁忙,请稍后再试!");
        Map<Integer, String> IvrMsgMap=BizConstant.IvrMsgMap;
        List<AliSignChargingOrder> aliSignChargingOrderList = queryAlipayOrder(mobile,bizType);
        if(aliSignChargingOrderList==null || aliSignChargingOrderList.isEmpty()){
            log.info("ivr退款和解约,暂无订单-->手机号:{},状态:{},描述:{}",mobile,610,IvrMsgMap.get(610));
            return IvrResult.response(610,IvrMsgMap.get(610));
        }
        final List<String> businessTypeList=aliSignChargingOrderList.stream().map(AliSignChargingOrder::getBusinessType).collect(Collectors.toList());
        //拒绝接通
        for(String item:businessTypeList){
            if(BizConstant.IvrTurnDownMsgMap.containsKey(item)){
                return IvrResult.response(500,BizConstant.IvrTurnDownMsgMap.get(item));
            }
        }
        boolean isComplain = isComplain(false, aliSignChargingOrderList);
        if(isComplain){
            log.info("ivr退款和解约,已生成投诉订单-->手机号:{},状态:{},描述:{}",mobile,610,IvrMsgMap.get(610));
            return IvrResult.response(610,IvrMsgMap.get(610));
        }
        Optional<AliSignChargingOrder> aliSignChargingOrderOptional=aliSignChargingOrderList.stream().max(Comparator.comparing(AliSignChargingOrder::getCreateTime));
        if(aliSignChargingOrderOptional.isPresent()){
            AliSignChargingOrder aliSignChargingOrder= aliSignChargingOrderOptional.get();
            String payTime=aliSignChargingOrder.getPayTime()==null?"":DateUtil.getDateFormat(aliSignChargingOrder.getPayTime(),DateUtil.FULL_TIME_HOURS_MINUTES);
            String orderAmount=aliSignChargingOrder.getOrderAmount();
            String orderNo=aliSignChargingOrder.getOrderNo();
            String businessName=aliSignChargingOrder.getBusinessName();
            String externalAgreementNo=aliSignChargingOrder.getExternalAgreementNo();
            String businessType=aliSignChargingOrder.getBusinessType();
            if(businessName.contains("(")){
                businessName=businessName.split("\\(")[0];
            }
            Integer refundStatus=aliSignChargingOrder.getRefundStatus();
            String refundTime=aliSignChargingOrder.getRefundTime()==null?"":DateUtil.getDateFormat(aliSignChargingOrder.getRefundTime(),DateUtil.FULL_TIME_HOURS_MINUTES);
            return this.IvrUnSign(refundStatus,mobile,orderNo,orderAmount,businessName,IvrMsgMap,payTime,refundTime,error,externalAgreementNo,businessType,bizType);
        }
        log.info("ivr退款和解约,订单查询失败,返回系统错误-->手机号:{}",mobile);
        return error;
    }

    //ivr调用退订
    private IvrResult IvrUnSign(Integer refundStatus,String mobile,String orderNo,String orderAmount,String businessName,Map<Integer, String> IvrMsgMap,String payTime,String refundTime,IvrResult error,String externalAgreementNo,String businessType,String bizType){
        //未退款
        if(refundStatus.equals(NOT_REFUND)){
            synchronized (interner.intern(externalAgreementNo)){
                //解约
                this.aliUnSignByExternalAgreementNo(externalAgreementNo);
            }
            synchronized (interner.intern(orderNo)) {
                Result<?> result=null;
                Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
                if(alipay!=null && alipay.getCustomerService().equals("1")){
                    //转账
                    result=aliSignChargingOrderService.aliPayTransferFee(orderNo,orderAmount,"ALIPAY_USERID",null,businessType);
                }else{
                    //退款
                    result=aliSignChargingOrderService.aliPayRefund(orderNo,orderAmount);
                }
                if(result.isOK()){
                    //提醒短信 (退款方式转账才发送提醒短信)
                    if(alipay!=null && alipay.getCustomerService().equals("1")){
                        String serviceType=alipay!=null && alipay.getCustomerService().equals("1")?BizConstant.BUSINESS_TYPE_CODE:BizConstant.BUSINESS_TYPE_YES_RIGHTS;
                        String code=alipay!=null && alipay.getCustomerService().equals("1")?"转账-"+businessName+"退款":businessName;
                        alipay.setBizType(bizType);
                        //电信包月彩铃退订
                        if(StringUtils.equals(bizType,BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY)){
                            dianxinVrbtService.unSubscribeByemp(mobile,BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
                        }
                        smsModelService.sendSms(mobile,"IVR_ALIPAY",alipay.getBizType(),serviceType,code);
                    }
                    String msg=IvrMsgMap.get(601);
                    log.info("ivr退款和解约,首次退款,退款成功-->手机号:{},状态:{},描述:{}",mobile,601,IvrMsgMap.get(601));
                    return IvrResult.response(601,msg,payTime,orderAmount,businessName,refundTime);
                }
                String msg=IvrMsgMap.get(603);
                log.info("ivr退款和解约,首次退款,退款失败-->手机号:{},状态:{},描述:{}",mobile,603,IvrMsgMap.get(603));
                return IvrResult.response(603,msg,payTime,orderAmount,businessName,refundTime);
            }
        }
        if(refundStatus.equals(REFUND_SUCCESS)){
            String msg=IvrMsgMap.get(610);
            log.info("ivr退款和解约,再次调用退款,已退款-->手机号:{},状态:{},描述:{}",mobile,610,IvrMsgMap.get(610));
            return IvrResult.response(610,msg,payTime,orderAmount,businessName,refundTime);
        }
        if(refundStatus.equals(REFUND_FAIL)){
            String msg=IvrMsgMap.get(603);
            log.info("ivr退款和解约,再次调用退款,退款失败-->手机号:{},状态:{},描述:{}",mobile,603,IvrMsgMap.get(603));
            return IvrResult.response(603,msg,payTime,orderAmount,businessName,refundTime);
        }
        log.info("ivr退款和解约,状态错误,返回系统错误-->手机号:{}",mobile);
        return error;
    }



    //查询支付宝已支付订单
    private List<AliSignChargingOrder> queryAlipayOrder(String mobile,String bizType) {

        //订购权益大礼包是会员可以登录
        List<String> businessTypeList = alipayConfigService.aliPayRightsRechargeList().stream().map(Alipay::getBusinessType).collect(Collectors.toList());
        //支付宝订单与其他业务查询不同
        if(businessTypeList.isEmpty() ){
            return null;
        }

        return aliSignChargingOrderService.lambdaQuery()
                .eq(AliSignChargingOrder::getMobile, mobile)
                .eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS)
                .eq(AliSignChargingOrder::getBizType, bizType)
                .in(AliSignChargingOrder::getBusinessType, businessTypeList)
                .orderByDesc(AliSignChargingOrder::getCreateTime).list();
    }
    //查询是否创建投诉订单
    private boolean isComplain(boolean isComplain, List<AliSignChargingOrder> aliSignChargingOrderList) {
        for (AliSignChargingOrder signChargingOrder : aliSignChargingOrderList) {
            Integer complainCount = alipayComplainService.lambdaQuery().eq(AlipayComplain::getMerchantOrderNo, signChargingOrder.getOrderNo()).count();
            if (complainCount > 0) {
                isComplain = true;
                break;
            }
        }
        return isComplain;
    }

    @Override
    public void aliSignScheduleDeduct(AliSignRecord aliSignRecord) {
        try{
            Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType, aliSignRecord.getBusinessType()).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
            if(alipay!=null){
                String mobile = aliSignRecord.getMobile();
                String outTradeNo = "AliPay" + IdWorker.getIdStr();
                String agreementNo = aliSignRecord.getAgreementNo();
                String externalAgreementNo = aliSignRecord.getExternalAgreementNo();
                String totalAmount = alipay.getSingleAmount();
                String subject = alipay.getBusinessName();
                //调用支付宝接口查询签约信息
                AlipayUserAgreementQueryResponse response = alipayService.aliTradePayQuery(aliSignRecord.getExternalAgreementNo(),aliSignRecord.getBusinessType());
                log.info("支付宝周期扣款-查询签约信息，手机号:{},agreementNo:{},externalAgreementNo:{},响应:{}",mobile,agreementNo,externalAgreementNo,mapper.writeValueAsString(response));
                if(response.isSuccess() && "NORMAL".equals(response.getStatus())){

                    //响应中如无(NextDeductTime),按签约传入逻辑设置下次扣款时间
                    if(StringUtils.isBlank(response.getNextDeductTime()) && aliSignRecord.getSignUpTime()!=null){
                        LocalDateTime signUpLocalDateTime = DateUtil.dateToLocalDateTime(aliSignRecord.getSignUpTime());
                        LocalDate nextDeductTime= signUpLocalDateTime.getDayOfMonth()>=29?LocalDate.now().plusMonths(1).withDayOfMonth(1):LocalDate.now().plusMonths(1).withDayOfMonth(signUpLocalDateTime.getDayOfMonth());
                        String nextDeductDayString = DateTimeFormatter.ISO_DATE.format(nextDeductTime);
                        AlipayUserAgreementExecutionplanModifyResponse resp = alipayService.alipayDelay(aliSignRecord.getAgreementNo(), aliSignRecord.getBusinessType(), nextDeductDayString);
                        log.info("支付宝周期扣款-执行计划修改，手机号:{},agreementNo:{},externalAgreementNo:{},响应:{}",mobile,agreementNo,externalAgreementNo,mapper.writeValueAsString(resp));
                    }else{
                        //下次扣款时间小于当前时间，延期一个月
                        LocalDate nextDeductDay= LocalDate.parse(response.getNextDeductTime(), DateTimeFormatter.ISO_DATE);
                        if(nextDeductDay.isBefore(LocalDate.now())){
                            LocalDate plusNextDeductDay = nextDeductDay.plusMonths(1);
                            while(plusNextDeductDay.isBefore(LocalDate.now())){
                                plusNextDeductDay = plusNextDeductDay.plusMonths(1);
                            }
                            String nextDeductDayString = DateTimeFormatter.ISO_DATE.format(plusNextDeductDay);
                            AlipayUserAgreementExecutionplanModifyResponse resp = alipayService.alipayDelay(aliSignRecord.getAgreementNo(), aliSignRecord.getBusinessType(), nextDeductDayString);
                            log.info("支付宝周期扣款-执行计划修改，手机号:{},agreementNo:{},externalAgreementNo:{},响应:{}",mobile,agreementNo,externalAgreementNo,mapper.writeValueAsString(resp));
                        }
                    }



                    //当月支付过就不扣了
                    Date start=aliSignRecord.getNextDeductTime();
                    Date end=Date.from(aliSignRecord.getNextDeductTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusMonths(1).atStartOfDay().atZone( ZoneId.systemDefault()).toInstant());
                    AliSignChargingOrder aliSignChargingOrder = aliSignChargingOrderService.lambdaQuery()
                            .eq(AliSignChargingOrder::getExternalAgreementNo, aliSignRecord.getExternalAgreementNo())
                            .between(AliSignChargingOrder::getPayTime, start, end)
                            .eq(AliSignChargingOrder::getOrderStatus, "1")
                            .orderByDesc(AliSignChargingOrder::getCreateTime)
                            .last("limit 1")
                            .one();

                    //如果当月有扣款记录
                    if(aliSignChargingOrder != null){
                        return;
                    }

                    //当月总订单数量不超过5次
                    Integer orderCount = aliSignChargingOrderService.lambdaQuery()
                            .eq(AliSignChargingOrder::getExternalAgreementNo, aliSignRecord.getExternalAgreementNo())
                            .between(AliSignChargingOrder::getCreateTime, start, end)
                            .count();

                    if(orderCount<=ORDER_COUNT){
                        AliSignChargingOrder order = aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getExternalAgreementNo, aliSignRecord.getExternalAgreementNo()).orderByAsc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
                        if(order!=null){
                            totalAmount=order.getOrderAmount();
                        }
                        log.info("支付宝签约协议周期扣款执行:id:{},手机号:{}",aliSignRecord.getId(),aliSignRecord.getMobile());
                        Boolean isAliPay=alipayService.aliTradePay(mobile,externalAgreementNo,agreementNo,outTradeNo,totalAmount,subject,aliSignRecord.getAppId(),aliSignRecord.getBusinessType(),aliSignRecord.getBusinessName(),aliSignRecord.getSubChannel(),aliSignRecord.getBizType());
                        //调用成功
                        if(isAliPay){
                            //修改下次扣款时间
                            Date nextDeductTime=DateUtil.localDateTimeToDate(DateUtil.dateToLocalDateTime(aliSignRecord.getNextDeductTime()).plusMonths(1));
                            aliSignRecord.setNextDeductTime(nextDeductTime);
                            this.updateById(aliSignRecord);
                        }
                    }else{
                        //修改下次扣款时间
                        Date nextDeductTime=DateUtil.localDateTimeToDate(DateUtil.dateToLocalDateTime(aliSignRecord.getNextDeductTime()).plusMonths(1));
                        aliSignRecord.setNextDeductTime(nextDeductTime);
                        this.updateById(aliSignRecord);
                        //电信支付宝视频彩铃扣款失败超过5次执行解约并退订
//                        if(StringUtil.equals(aliSignRecord.getBizType(),BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY)){
//                            //解约
//                            alipayService.unSign(externalAgreementNo);
//                            //退订
//                            dianxinVrbtService.unSubscribeByemp(aliSignRecord.getMobile(),BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
//                        }

                    }
                }else{
                    aliSignRecord.setRemark(response.getSubMsg());
                    //非支付宝账号关闭的subCode
                    if(!StringUtils.contains(response.getSubCode(),"isv.insufficient-isv-permissions")){
                        aliSignRecord.setSignStatus(4);
                    }
                    this.updateById(aliSignRecord);
                }
            }
        }catch (Exception e){
            log.error("id:{},手机号:{},支付宝签约协议周期扣款异常:{}",aliSignRecord.getId(),aliSignRecord.getMobile(),e);

        }
    }

    /**
     * 更新扣款日期并发送扣款消息
     * @param aliSignRecord
     * @param deductDay
     */
    @Transactional
    @Override
    public void updateDeductDayAndSendDeductMessage(AliSignRecord aliSignRecord,String deductDay){
        this.lambdaUpdate().set(AliSignRecord::getDeductDay,deductDay).eq(AliSignRecord::getId,aliSignRecord.getId()).update();
        rabbitMQMsgSender.sendAlipayDeductMessage(AlipayDeductMessage.builder().id(aliSignRecord.getId()).mobile(aliSignRecord.getMobile()).build());
    }



    /**
     * 更新发送扣款短信执行日期并发送提醒扣款短信
     * @param aliSignRecord
     * @param sendDeductMsgDay
     */
    @Transactional
    @Override
    public void updateSendDeductMsgDay(AliSignRecord aliSignRecord, String sendDeductMsgDay){
        this.lambdaUpdate().set(AliSignRecord::getSendDeductMsgDay,sendDeductMsgDay).eq(AliSignRecord::getId,aliSignRecord.getId()).update();
        rabbitMQMsgSender.sendAlipayDeductSendMessage(AlipayDeductSendMessage.builder().id(aliSignRecord.getId()).mobile(aliSignRecord.getMobile()).build());
    }


    @Override
    public void sendDeductMsg(AliSignRecord aliSignRecord){
        Map<String,String> smsMap= Maps.newHashMap();
        smsModelService.sendSms(aliSignRecord.getMobile(),"IVR_ALIPAY",aliSignRecord.getBizType(),"4",smsMap);
    }

    /**
     * 查询用户是否包月
     * @param mobile
     * @param channel
     * @param bizType
     * @return
     */
    @Override
    public boolean unSub(String mobile,String channel,String bizType){
        return this.lambdaQuery().eq(AliSignRecord::getMobile,mobile).eq(AliSignRecord::getBusinessType,channel).eq(AliSignRecord::getBizType,bizType).eq(AliSignRecord::getSignStatus,1).count()>0;
    }

}
