package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.JsydDelayedMessage;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.JiangsuYidongService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.JiangsuResponseCreateOrder;
import com.eleven.cms.vo.JiangsuResponseSmsValidate;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("jiangSuYidongVrbtCommonService")
@Slf4j
public class JiangSuYidongVrbtCommonServiceImpl implements IBizCommonService {

    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    private static final String reportUrl = "https://crbt.cdyrjygs.com/vrbt_jiangsu";

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        try {
            JiangsuResponseCreateOrder jiangsuResponseCreateOrder = jiangsuYidongService.createOrder(subscribe.getMobile(), subscribe.getReferer(), subscribe.getDeviceInfo(), reportUrl, jiangsuYidongService.getToken());
            if (jiangsuResponseCreateOrder.isOk()) {
                String orderId = jiangsuResponseCreateOrder.getContent().getOrderId();
                subscribe.setIspOrderNo(orderId);
                subscribeService.createSubscribeDbAndEs(subscribe);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
//                return Result.error("获取验证码失败");

                try {
                    String errorMsg="{\"retCode\":\""+jiangsuResponseCreateOrder.getContent()!=null?jiangsuResponseCreateOrder.getContent().getRetCode():"500"+"\",\"retMsg\":\""+jiangsuResponseCreateOrder.getContent()!=null?jiangsuResponseCreateOrder.getContent().getRetMsg():"接口返回异常"+"\"}";
                    return Result.errorSmsMsg(errorMsg);
                } catch (Exception e) {
                    log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                    return Result.msgSmsError();
                }
            }
        } catch (Exception e) {
            return Result.error("获取验证码失败");
        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        try {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setBizTime(new Date());
            JiangsuResponseSmsValidate jiangsuResponseSmsValidate = jiangsuYidongService.smsValidate(mobile, subscribe.getIspOrderNo(), smsCode, jiangsuYidongService.getToken());
            if (jiangsuResponseSmsValidate.isOk()) {
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setResult(jiangsuResponseSmsValidate.getContent().getRetMsg());
                subscribeService.updateSubscribeDbAndEs(upd);
                redisDelayedQueueManager.addJsyd(JsydDelayedMessage.builder().mobile(mobile).count(1).orderId(subscribe.getIspOrderNo()).orderTimeStamp(System.currentTimeMillis() / 1000).build(), 1, TimeUnit.MINUTES);
                return Result.ok("提交验证码成功");
            } else {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(jiangsuResponseSmsValidate.getContent().getRetMsg());
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error("提交验证码失败");
            }
        } catch (Exception e) {
            log.error("江苏移动视频彩铃提交验证码系统错误:{}", e);
            return Result.error("系统错误，请稍后再试");
        }
    }
}
