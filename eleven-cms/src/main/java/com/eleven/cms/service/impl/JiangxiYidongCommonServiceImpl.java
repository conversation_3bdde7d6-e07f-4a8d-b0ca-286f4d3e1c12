package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.JiangxiCompanyConfig;
import com.eleven.cms.config.JiangxiYidongProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.JiangxiYidongV2Service;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.JiangxiYidongResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("jiangxiYidongCommonService")
@Slf4j
public class JiangxiYidongCommonServiceImpl implements IBizCommonService {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    JiangxiYidongProperties jiangxiYidongProperties;
    @Autowired
    IChannelService channelService;
    @Autowired
    JiangxiYidongV2Service jiangxiYidongV2Service;
    @Autowired
    private OutsideCallbackService outsideCallbackService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;

    @Override
    @ValidationLimit
    public Result filerCheck(Subscribe subscribe) {
//        //江西移动校验-鸿盛的萌宠AI联合会员月包
//        if ("JXYD_MCAI_HS".equals(subscribe.getChannel())) {
            JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(subscribe.getServiceId());
            String busiSerialNumber = jiangxiCompanyConfig.getUserId() + DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(4);
            JiangxiYidongResult jiangxiYidongResult = jiangxiYidongV2Service.getSms(subscribe.getMobile(), subscribe.getChannel(), subscribe.getServiceId(), busiSerialNumber);
            if(jiangxiYidongResult.isOk()){
                return Result.ok();
            }
            try {
                String errorMsg="{\"ret\":\""+jiangxiYidongResult.getRet()+"\",\"msg\":\""+jiangxiYidongResult.getMsg()+"\"}";
                return Result.errorDuplicateMsg(errorMsg);
            } catch (Exception e) {
                log.error("前置校验异常!-subscribe:{}",subscribe, e);
                return Result.msgDuplicateLimit();
            }
//        }
//        return IBizCommonService.super.filerCheck(subscribe);
    }

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {

        //查询江西移动开通记录
        List<Subscribe> subscribeList = subscribeService.lambdaQuery().eq(Subscribe::getMobile, subscribe.getMobile()).eq(Subscribe::getBizType, BIZ_TYPE_JXYD).eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).list();
        if (subscribeList.size() > 0) {
            return Result.error("请勿重复开通");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        JiangxiCompanyConfig jiangxiCompanyConfig = jiangxiYidongProperties.getJiangxiCompanyConfig(subscribe.getServiceId());
        String busiSerialNumber = jiangxiCompanyConfig.getUserId() + DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(4);
        JiangxiYidongResult jiangxiYidongResult = jiangxiYidongV2Service.getSms(subscribe.getMobile(), subscribe.getChannel(), subscribe.getServiceId(), busiSerialNumber);
        if (jiangxiYidongResult.isOk()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribe.setIspOrderNo(jiangxiYidongResult.getBusiSerialNumber());
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            try {
//                return Result.error(jiangxiYidongResult.getData().getResultMsg().getRespDesc());
//            } catch (Exception e) {
//                return Result.error("获取短信验证码失败");
//            }
            try {
                String result=jiangxiYidongResult.getData()!=null && jiangxiYidongResult.getData().getResultMsg()!=null?jiangxiYidongResult.getData().getResultMsg().getRespCode()+"-"+jiangxiYidongResult.getData().getResultMsg().getRespDesc():"";
                String errorMsg="{\"ret\":\""+jiangxiYidongResult.getRet()+"\",\"message\":\""+jiangxiYidongResult.getMsg()+"\",\"result\":\""+result+"\"}";
                String respDesc=jiangxiYidongResult.getData()!=null && jiangxiYidongResult.getData().getResultMsg()!=null?jiangxiYidongResult.getData().getResultMsg().getRespDesc():"获取短信验证码失败";
                return Result.errorSmsCodeMsg(respDesc,errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsCodeError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        JiangxiYidongResult jiangxiYidongResult = jiangxiYidongV2Service.smsCode(subscribe.getMobile(), subscribe.getChannel(), smsCode, subscribe.getEncryption(), subscribe.getIspOrderNo(), subscribe.getServiceId());
        if (jiangxiYidongResult.isOk()) {
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            final Date openTime = new Date();
            upd.setExtra(smsCode);
            upd.setOpenTime(openTime);
            final String result = "订购成功";
            upd.setResult(result);
            subscribeService.updateSubscribeDbAndEs(upd);
            //江西移动银联双V会员/江西移动商超联合会员专用回调(中屹互联除外)
            if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEl_JXYD_YLSVHY, BIZ_CHANNEl_JXYD_SCLHHY)) {
//                outsideCallbackService.jxydYinlianMemberCallbackAsync(subscribe.getMobile(), subscribe.getId(), subscribe.getChannel(), SUBSCRIBE_STATUS_SUCCESS, result, smsCode, openTime);
                outsideCallbackService.jxydYinlianMemberCallbackVs(subscribe.getMobile(),subscribe.getId(),SUBSCRIBE_STATUS_SUCCESS,  result,  smsCode,  openTime,  subscribe.getReferer(), subscribe.getUserAgent(),subscribe.getIp());
            }
            //江西移动饭票会员回传
            if (StringUtils.equalsAny(subscribe.getChannel(), BIZ_CHANNEl_JXYD_FPLHHY, BIZ_CHANNEl_JXYD_FPLHHY_YR)) {
                final String offerId = jiangxiYidongProperties.getJiangxiProductConfig(subscribe.getChannel()).getOfferId();
                outsideCallbackService.zysCallbackAsync(subscribeService.getById(subscribe.getId()), offerId);
            }

            //江西萌宠集回传
            if (BIZ_CHANNEl_JXYD_MCAI_YR.equals(subscribe.getChannel())) {
                final String offerId = jiangxiYidongProperties.getJiangxiProductConfig(subscribe.getChannel()).getOfferId();
                outsideCallbackService.mcjCallbackAsync(subscribeService.getById(subscribe.getId()), offerId);
            }

            //江西萌宠集回传(鸿盛)
            if ("JXYD_MCAI_HS".equals(subscribe.getChannel())) {
                outsideCallbackService.mcAztCallbackAsync(subscribeService.getById(subscribe.getId()));
            }

            //加入延迟队列校验
            if (BIZ_CHANNEl_JXYD_VRBT.equals(subscribe.getChannel()) || BIZ_CHANNEl_JXYD_VRBT_20.equals(subscribe.getChannel())) {
                rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            }

            //限量
            subscribeService.saveChannelLimit(subscribe);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            return Result.ok("订阅成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(jiangxiYidongResult.getData() != null && jiangxiYidongResult.getData().getResultMsg() != null ? jiangxiYidongResult.getData().getResultMsg().getRespDesc() : jiangxiYidongResult.getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
