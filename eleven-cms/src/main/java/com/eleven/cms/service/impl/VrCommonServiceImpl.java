package com.eleven.cms.service.impl;

import com.eleven.cms.ad.MiGuKuaiYouVRJingMengApiProperties;
import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.KuaiYouVRJingMengChannel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.JiangsuYidongService;
import com.eleven.cms.remote.PushSubscribeService;
import com.eleven.cms.remote.WujiongCrackService;
import com.eleven.cms.remote.YidongVRCaoWeiCrackService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_INIT;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("vrCommonService")
@Slf4j
public class VrCommonServiceImpl implements IBizCommonService {

    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    YidongVRCaoWeiCrackService yidongVRCaoWeiCrackService;
    @Autowired
    private MiGuKuaiYouVRJingMengApiProperties miGuKuaiYouVRJingMengApiProperties;
    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        KuaiYouVRJingMengChannel kuaiYouVRJingMengChannel=miGuKuaiYouVRJingMengApiProperties.getChannelMap().get(subscribe.getChannel());
        if(kuaiYouVRJingMengChannel!=null){
            subscribe.setServiceId(kuaiYouVRJingMengChannel.getChannel());
            subscribe.setExtra(kuaiYouVRJingMengChannel.getProductId());
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        //曹伟破解
        final Result<?> result = pushSubscribeService.handleBizFuseVRCaoWeiExistsBillingCrack(subscribe);
//        //wujiong破解
//        final Result<?> result = pushSubscribeService.handleBizFuseVRWujiongExistsBillingCrack(subscribe);
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //此处保存已提交验证码
        if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setBizTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        yidongVRCaoWeiCrackService.smsCode(subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(),subscribe.getMobile());
        return Result.ok("提交验证码成功");

//        WujiongCrackResult wujiongCrackResult = wujiongCrackService.smsCode(subscribe.getMobile(), subscribe.getIspOrderNo(), smsCode, subscribe.getChannel());
//        if (wujiongCrackResult.isSubOk()) {
//            return Result.ok("订阅成功");
//        } else {
//            final Subscribe targets = subscribeService.getById(subscribe.getId());
//            if (targets!=null && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(targets.getStatus())) {
//                Subscribe upd = new Subscribe();
//                upd.setId(targets.getId());
//                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
//                upd.setResult(wujiongCrackResult.getResultMsg());
//                upd.setModifyTime(new Date());
//                subscribeService.updateSubscribeDbAndEs(upd);
//            }
//            return Result.error("订阅失败");
//        }

    }
}
