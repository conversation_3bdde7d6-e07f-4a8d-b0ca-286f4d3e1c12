package com.eleven.cms.service;

import com.eleven.cms.entity.VrbtZeroOrder;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: cms_vrbt_zero_order
 * @Author: jeecg-boot
 * @Date:   2022-10-20
 * @Version: V1.0
 */
public interface IVrbtZeroOrderService extends IService<VrbtZeroOrder> {

    void vrbtZeroOrderXunfei(Integer fromId, Integer amount, Integer intervalMaxMill);

    void vrbtZeroOrder(Integer fromId, Integer amount, Integer intervalMaxMill);

    void handleOrder(VrbtZeroOrder order);

    void handleOrderXunfei(VrbtZeroOrder order);

    void handleOrderXunfeiTemp(VrbtZeroOrder order);

    void handleOrderProvinceVrbtFun(VrbtZeroOrder order);

    void handleOrder(String mobile,String channel,Integer id,String copyrightId,String contentId,String amount);

    void updateVrbtZeroOrderStatusMessage(VrbtZeroOrder vrbtZeroOrder);

    void freeMonthSubVrbt(String id);
}
