package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.BusinessPack;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.KugouOrder;
import com.eleven.cms.mapper.KugouOrderMapper;
import com.eleven.cms.queue.KuGouRightsRechargeMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IBusinessPackService;
import com.eleven.cms.service.IBusinessRightsSubService;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IKugouOrderService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.RemoteResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

import java.util.List;

import static com.eleven.cms.util.BizConstant.KG_RIGHTS_ID;

/**
 * @Description: cms_kugou_order
 * @Author: jeecg-boot
 * @Date:   2022-11-24
 * @Version: V1.0
 */
@Slf4j
@Service
public class KugouOrderServiceImpl extends ServiceImpl<KugouOrderMapper, KugouOrder> implements IKugouOrderService {

    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    /**
     * 更新权益月份并发送权益充值消息
     * @param kugouOrder
     */
    @Transactional
    @Override
    public void updateRightsMonthAndSendRightsRechargeMessage(KugouOrder kugouOrder){
        this.lambdaUpdate().eq(KugouOrder::getId, kugouOrder.getId()).set(KugouOrder::getRechargeStatus, kugouOrder.getRechargeStatus()).set(KugouOrder::getRightsMonth, kugouOrder.getRightsMonth()).update();
        rabbitMQMsgSender.sendKuGouRightsRechargeMessage(KuGouRightsRechargeMessage.builder().id(String.valueOf(kugouOrder.getId())).mobile(kugouOrder.getMobile()).build());
    }

    /**
     * 酷狗权益充值
     * @param id
     */
    @Override
    public void kugouRecharge(String id) {
        KugouOrder kugou=this.lambdaQuery().eq(KugouOrder::getId, id).one();
        if(kugou==null){
            return;
        }
        CmsCrackConfig config=cmsCrackConfigService.getCrackConfigByChannel(kugou.getChannel());
        if(config==null){
            log.error("定时任务-酷狗存量包月校验Crack未配置-config{},kugou:{}",config,kugou);
            return;
        }
        if(BizConstant.BIZ_TYPE_UNION_MEMBER.equals(config.getBizType())){
            final RemoteResult batc=miguApiService.asMemberQuery(kugou.getMobile(),kugou.getChannel());
            if(batc.isAsMember()){
                //充值改状态
                recharge(kugou);
            }else{
                this.lambdaUpdate().eq(KugouOrder::getId, kugou.getId()).set(KugouOrder::getRechargeStatus, 2).set(KugouOrder::getRemark, "酷狗包月查询失败(已退订)").update();
            }
        }else if(BizConstant.BIZ_TYPE_CPMB.equals(config.getBizType())){
            final RemoteResult batc =miguApiService.cpmbQuery(kugou.getMobile(), kugou.getChannel());
            if(batc.isCpmbMember()){
                //充值改状态
                recharge(kugou);
            }else{
                this.lambdaUpdate().eq(KugouOrder::getId, kugou.getId()).set(KugouOrder::getRechargeStatus, 2).set(KugouOrder::getRemark, "酷狗包月查询失败(已退订)").update();
            }
        }else{
            this.lambdaUpdate().eq(KugouOrder::getId, kugou.getId()).set(KugouOrder::getRechargeStatus, 2).set(KugouOrder::getRemark, "酷狗包月查询失败(已退订)").update();
        }
    }

    private void recharge(KugouOrder kugou) {
        this.lambdaUpdate().eq(KugouOrder::getId, kugou.getId()).set(KugouOrder::getRechargeStatus, 3).update();
        BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, kugou.getServiceId()).select(BusinessPack::getServiceApiBeanName, BusinessPack::getServiceId).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        final IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(businessPack.getServiceApiBeanName(), IBusinessRightsSubService.class);
        businessRightsSubService.createScheduledRecharge(kugou.getMobile(), kugou.getMobile(), kugou.getServiceId(), kugou.getPackName(), KG_RIGHTS_ID, kugou.getChannel());
    }
}
