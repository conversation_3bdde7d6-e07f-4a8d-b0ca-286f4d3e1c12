package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.dto.DuocaiOpenResponse;
import com.eleven.cms.dto.DuocaiProductResponse;
import com.eleven.cms.entity.DuodianCouponCodeChargeLog;
import com.eleven.cms.vo.FebsResponse;

/**
 * @Description: 途牛券码充值记录
 * @Author: jeecg-boot
 * @Date:   2024-03-29
 * @Version: V1.0
 */
public interface IDuodianCouponCodeChargeLogService extends IService<DuodianCouponCodeChargeLog> {

    DuocaiOpenResponse duocaiOpenNotify(String requestBody, String sign, String appId, String timestamp);

    void sendCodeScheduleDeduct(String id);

    FebsResponse duocaiRecharge(String mobile, String couponCode);

    DuocaiProductResponse duocaiProduct(String requestBody, String sign, String appId, String timestamp);
}
