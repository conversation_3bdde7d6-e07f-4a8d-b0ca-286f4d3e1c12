package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.util.RightBusinessEnum;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.WujiongCrackResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class VRJingMengBusinessServiceImpl implements IBusinessCommonService {

    public static final String YIDONG_MYHY_VR_DUPLICATE_KEY_PREFIX = "yidong::myhyvr:";
    public static final long YIDONG_MYHY_VR_SMS_INVALID_CACHE_SECONDS = 60;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVRCaoWeiCrackService yidongVRCaoWeiCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;


    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        if (provinceBusinessChannelConfigService.allow("HETU", mobileRegionResult.getProvince()) && BIZ_CHANNEl_VR_JM_25.equals(subscribe.getChannel())) {
                            subscribe.setChannel("HETU");
                            subscribe.setBizType("HETU");
                            return SpringContextUtils.getBean(HetuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                        }
                        if (provinceBusinessChannelConfigService.allow("HETU_GZ", mobileRegionResult.getProvince()) && BIZ_CHANNEl_VR_JM_25.equals(subscribe.getChannel())) {
                            subscribe.setChannel("HETU_GZ");
                            subscribe.setBizType("HETU");
                            return SpringContextUtils.getBean(HetuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                        }
                        if (redisUtil.hasKey(CacheConstant.CMS_VR_SELECT_SCYD) && SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel((String) redisUtil.get(CacheConstant.CMS_VR_SELECT_SCYD));
                            subscribe.setBizType(BIZ_TYPE_SCYD);
                            String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                            subscribe.setBizCode(bizCode);
                            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveSichuanMobileCrackOrder(subscribe);
                        }
                        if (provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1, mobileRegionResult.getProvince()) && BIZ_CHANNEl_VR_JM_25.equals(subscribe.getChannel())) {
                            subscribe.setChannel(MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1);
                            subscribe.setBizType(BIZ_TYPE_CPMB);
                            return SpringContextUtils.getBean(CpmbBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                        }
                        if (provinceBusinessChannelConfigService.allow(BIZ_CHANNEl_JXYD_VRBT,mobileRegionResult.getProvince())) {
                            subscribe.setChannel(BIZ_CHANNEl_JXYD_VRBT);
                            subscribe.setBizType(BIZ_TYPE_JXYD);
                            return SpringContextUtils.getBean(JiangxiBusinessServiceImpl.class).receiveOrderWithCache(subscribe);

//                            subscribe.setChannel(BIZ_CHANNEL_XIJIU_JIANGXI_VRBT);
//                            subscribe.setBizType(BIZ_TYPE_XIJIU_JX_VRBT);
//                            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(JiangXiVrbtBusinessServiceImpl.class);
//                            return businessCommonService.receiveOrderWithCache(subscribe);
                        }

                        if (GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel(BIZ_TYPE_GZYD_BJHY_10GLLB);
                            subscribe.setBizType(BIZ_TYPE_GZYD_BJHY_10GLLB);
                            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveGuiZhouMobileOrder(subscribe);
                        }
                        if (HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel(BIZ_TYPE_HN_QWZJ_LLB);
                            subscribe.setBizType(BIZ_TYPE_HN_QWZJ_LLB);
                            Result result = SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveHuNanMobileOrder(subscribe);
                            result.setChannelCode(BIZ_TYPE_HN_QWZJ_LLB);
                            return result;
                        }
                        if (HENAN_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel(BIZ_TYPE_HNYZ_SHFW_HYB);
                            subscribe.setBizType(BIZ_TYPE_HNYZ_SHFW_HYB);
                            Result result = SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveHenanMobileOrder(subscribe);
                            result.setChannelCode(BIZ_TYPE_HNYZ_SHFW_HYB);
                            return result;
                        }
                        if (provinceBusinessChannelConfigService.allow(MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_W5, mobileRegionResult.getProvince())) {
                            subscribe.setChannel(MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_W5);
                            subscribe.setBizType(BIZ_TYPE_CPMB);
                            Result result = SpringContextUtils.getBean(CpmbBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
                            result.setChannelCode(MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_W5);
                            result.setServiceId("698039020050006041");
                            return result;
                        }
                        if (provinceBusinessChannelConfigService.allow("00210QL", mobileRegionResult.getProvince())) {
                            subscribe.setChannel("00210QL");
                            Result result = innerUnionMemberService.forword(subscribe, subscribe.getChannel());
                            result.setChannelCode("00210QL");
                            result.setServiceId("698039034105617363");
                            return result;
                        }
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        Result result = SpringContextUtils.getBean(VRJingMengBusinessServiceImpl.class).receiveOrder(subscribe);
        result.setServiceId(RightBusinessEnum.getServiceId(subscribe.getChannel()));
        return result;
    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = YIDONG_MYHY_VR_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", YIDONG_MYHY_VR_SMS_INVALID_CACHE_SECONDS);
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(target.getId());
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
            if (redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_VR_CRACK) && SUBSCRIBE_STATUS_PUSHED.equals(redisUtil.get(CacheConstant.CMS_BIZ_SWITCH_VR_CRACK).toString())) {
                //吴炯破解
                WujiongCrackResult wujiongCrackResult = wujiongCrackService.smsCode(subscribe.getMobile(), target.getIspOrderNo(), smsCode, subscribe.getChannel());
                if (wujiongCrackResult.isSubOk()) {
                    return Result.ok("订阅成功");
                } else {
                    final Subscribe targets = subscribeService.getById(target.getId());
                    if (targets!=null && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(targets.getStatus())) {
                        Subscribe upd = new Subscribe();
                        upd.setId(targets.getId());
                        upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                        upd.setResult(wujiongCrackResult.getResultMsg());
                        upd.setModifyTime(new Date());
                        subscribeService.updateSubscribeDbAndEs(upd);
                    }
                    return Result.error("订阅失败");
                }
            }else{
                //曹伟破解
                yidongVRCaoWeiCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getChannel(),subscribe.getMobile());
                return Result.ok("订阅成功");
            }




        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        Result<?> result=null;
        if (redisUtil.hasKey(CacheConstant.CMS_BIZ_SWITCH_VR_CRACK) && SUBSCRIBE_STATUS_PUSHED.equals(redisUtil.get(CacheConstant.CMS_BIZ_SWITCH_VR_CRACK).toString())) {
            //吴炯破解
            result = pushSubscribeService.handleVRWujiongExistsBillingCrack(subscribe);
        }else{
            //曹伟破解
            result = pushSubscribeService.handleVRCaoWeiExistsBillingCrack(subscribe);
        }
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }



}
