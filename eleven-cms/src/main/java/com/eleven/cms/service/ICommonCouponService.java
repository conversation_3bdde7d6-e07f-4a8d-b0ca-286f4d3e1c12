package com.eleven.cms.service;

import com.eleven.cms.dto.LianlianNanshanOpenResponse;
import com.eleven.cms.dto.NanshanOpenResponse;
import com.eleven.cms.entity.CommonCoupon;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: cms_common_coupon
 * @Author: jeecg-boot
 * @Date:   2024-07-04
 * @Version: V1.0
 */
public interface ICommonCouponService extends IService<CommonCoupon> {

    CommonCoupon sendActiveCode(String mobile, String channel, String orderId,String dataLinkId,String notifyUrl,int num);

//    void addCouponTask();

    LianlianNanshanOpenResponse lianlianNanshanOpenNotify(String requestBody, String sign, String appKey);

    void sendCodeScheduleDeduct(String id);

    void lianlianSendCode(String orderId);
}
