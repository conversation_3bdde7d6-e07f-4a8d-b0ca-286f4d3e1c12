package com.eleven.cms.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.mapper.MemberRightsMapper;
import com.eleven.cms.service.IMemberRightsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 视听会员权益
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Service
@DS("master")
public class MemberRightsServiceImpl extends ServiceImpl<MemberRightsMapper, MemberRights> implements IMemberRightsService {

    @Override
    public List<MemberRights> queryRightsByMemberIds(List<String> memberIds, String rightsName) {
        return this.lambdaQuery().in(MemberRights::getId, memberIds).eq(false,MemberRights::getRightsName, rightsName).list();
    }
    @Override
    public Optional<MemberRights> queryMemberRightsDetail(String rightsId) {
        return this.lambdaQuery()
                .eq(MemberRights::getRightsId, rightsId)
                .list()
                .stream()
                //找到当月最后一个订单
                .max(Comparator.comparing(MemberRights::getCreateTime));
    }
    @Override
    public Optional<MemberRights> queryMemberRightsByName(String rightsName) {
        return this.lambdaQuery()
                .eq(MemberRights::getRightsName, rightsName)
                .list()
                .stream()
                //找到当月最后一个订单
                .max(Comparator.comparing(MemberRights::getCreateTime));
    }
    @Override
    public void updateMemberRightsList(List<MemberRights> list) {
        this.updateBatchById(list);
    }


    @Override
    public List<MemberRights> queryShopByProductId(MemberRights rights) {
        LambdaQueryWrapper<MemberRights> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(rights.getRightsName())){
            queryWrapper.eq(MemberRights::getRightsName, rights.getRightsName());
        }
        queryWrapper.orderByDesc(MemberRights::getProductPrice);
        List<MemberRights> list=this.baseMapper.selectList(queryWrapper);
        return list;
    }
    @Override
    public List<MemberRights> queryRightsList() {
        return this.lambdaQuery().list();
    }

    @Override
    public void addMemberRightsList(List<MemberRights> list) {
        this.saveBatch(list);
    }
}
