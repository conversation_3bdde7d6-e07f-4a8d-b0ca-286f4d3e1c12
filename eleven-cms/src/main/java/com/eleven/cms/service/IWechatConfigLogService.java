package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.dto.DouYinToken;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.vo.FebsResponse;

/**
 * @Description: qycl_wechat_config_log
 * @Author: jeecg-boot
 * @Date:   2022-12-05
 * @Version: V1.0
 */
public interface IWechatConfigLogService extends IService<WechatConfigLog> {

    WechatConfigLog getWechatConfig(String tradeType, String businessType);

    WechatConfigLog getWechatNotifyConfig(String appId,String mchId);

    WechatConfigLog getDyConfig(String tradeType,String businessType);

    WechatConfigLog getDyNotifyConfig(String appId);

    FebsResponse getDyToken(String appid, String secret);

    WechatConfigLog getKsConfig(String tradeType,String businessType);


    DouYinToken getDouYinToken(String appId, String secret);
}
