package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.HunanYidongService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.HunanYidongResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("hunanYidongVrbtCommonService")
@Slf4j
public class HunanYidongVrbtCommonServiceImpl implements IBizCommonService {

    @Autowired
    HunanYidongService hunanYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobile = subscribe.getMobile();
        //调用检验接口校验用户开通资格
        HunanYidongResult productCheckResult = hunanYidongService.productCheck(mobile, subscribe.getChannel());
        if (!(productCheckResult.isOK()
                && productCheckResult.getResult() != null
                && productCheckResult.getResult().size() > 0
                && "0".equals(productCheckResult.getResult().get(0).getResultCode()))) {
            return Result.error("暂无订购资格，请稍后再试!");
        }
        HunanYidongResult getSmsResult = hunanYidongService.getSms(mobile, subscribe.getChannel());
        if (getSmsResult.isOK()
                && getSmsResult.getResult() != null
                && getSmsResult.getResult().size() > 0
                && "0".equals(getSmsResult.getResult().get(0).getResultCode())) {
            subscribe.setResult("获取验证码成功");
            subscribeService.createSubscribeDbAndEs(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
//            subscribe.setResult(getSmsResult.getRespDesc());
//            subscribeService.createSubscribeDbAndEs(subscribe);
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"respCode\":\""+getSmsResult.getRespCode()+"\",\"result\":\""+getSmsResult.getRespDesc()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        HunanYidongResult smsCodeResult = hunanYidongService.smsCode(mobile, smsCode, subscribe.getChannel());
        if (smsCodeResult.isOK()
                && smsCodeResult.getResult() != null
                && smsCodeResult.getResult().size() > 0
                && (StringUtils.isNotBlank(smsCodeResult.getResult().get(0).getTradeId())
                || "0".equals(smsCodeResult.getResult().get(0).getResultCode()))) {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setOpenTime(new Date());
            upd.setResult("订阅成功");
            subscribeService.updateSubscribeDbAndEs(upd);
            subscribeService.saveChannelLimit(subscribe);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            if(BIZ_TYPE_HN_VRBT_DY_LLB.equals(subscribe.getBizType())) {
                rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            }
            return Result.ok("订阅成功");
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(smsCodeResult.getResult() == null ? "订阅失败" : smsCodeResult.getResult().get(0).getResultInfo());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error(500, smsCodeResult.getResult() == null ? "订阅失败" : smsCodeResult.getResult().get(0).getResultInfo());
        }
    }
}
