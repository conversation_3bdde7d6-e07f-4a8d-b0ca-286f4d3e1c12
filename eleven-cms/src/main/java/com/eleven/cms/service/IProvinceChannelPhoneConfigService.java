package com.eleven.cms.service;

import com.eleven.cms.entity.ProvinceChannelPhoneConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.ProvinceChannelPhoneInfo;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * @Description: cms_province_channel_phone_config
 * @Author: jeecg-boot
 * @Date:   2023-02-06
 * @Version: V1.0
 */
public interface IProvinceChannelPhoneConfigService extends IService<ProvinceChannelPhoneConfig> {

    List<ProvinceChannelPhoneInfo> selectByMainId(String id);

    void updateMain(ProvinceChannelPhoneConfig provinceChannelPhoneConfig, List<ProvinceChannelPhoneInfo> provinceChannelPhoneInfoList);

    void saveMain(ProvinceChannelPhoneConfig provinceChannelPhoneConfig, List<ProvinceChannelPhoneInfo> provinceChannelPhoneInfoList);

    boolean phoneValidate(String channelCode, String mobile);

    boolean provinceValidate(String channelCode, String province);

    void deleteById(String id);

    void deleteByIds(List<String> strings);

    boolean allow(String channelCode, String mobile);
}
