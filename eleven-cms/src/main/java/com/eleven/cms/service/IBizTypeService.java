package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.BizType;
import com.eleven.cms.entity.Subscribe;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 *  Service接口
 *
 * <AUTHOR>
 * @date 2023-08-07 11:33:33
 */
public interface IBizTypeService extends IService<BizType> {

    BizType selectById(String id);

    BizType getBizType(JsonNode jsonNodeArray);

    BizType getBizTypeByChannel(String channelCode, String serviceId);

    Result callGetSmsCode(Subscribe subscribe, JsonNode pageConfigJsonNode) throws JsonProcessingException;

    Result callSumbitSmsCode(Subscribe subscribe, String smsCode, String transactionId) throws JsonProcessingException;

    Result outsideCallGetSmsCode(Subscribe subscribe, String bizTypeId) throws JsonProcessingException;

    Result outsideCallSumbitSmsCode(Subscribe subscribe, String smsCode, String transactionId) throws JsonProcessingException;

    Result callPopGetSmsCode(Subscribe subscribe, String bizTypeId) throws JsonProcessingException;

    /**
     * 查询渠道号列表
     *
     * @return List<String>
     */
    List<String> queryChannelList();

    Result outsideSubOrder(Subscribe subscribe);

    Result receiveBillingResult(String mobile,String ydOrderId);
}
