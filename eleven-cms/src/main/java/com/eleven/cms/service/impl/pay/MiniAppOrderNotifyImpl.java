package com.eleven.cms.service.impl.pay;

import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.service.pay.PayNotifyService;
import com.eleven.cms.wallpaper.entity.MiniAppWallpaperOrder;
import com.eleven.cms.wallpaper.service.IMiniAppWallpaperOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class MiniAppOrderNotifyImpl implements PayNotifyService {

    @Resource
    IMiniAppWallpaperOrderService iMiniAppWallpaperOrderService;

    @Override
    public void handleNotify(String orderNo, Map<String, String> requestParams) {
        String transactionId = "";
        if (Objects.nonNull(requestParams)) {
            transactionId = requestParams.get("transaction_id");
        }
        updateOrder(orderNo, transactionId);
    }

    public void updateOrder(String orderNo,String transactionId) {

        log.info("更新订单状态");
        List<MiniAppWallpaperOrder> appWallpaperOrders = iMiniAppWallpaperOrderService.lambdaQuery().eq(MiniAppWallpaperOrder::getOrderNo, orderNo).list();
        for (MiniAppWallpaperOrder appWallpaperOrder : appWallpaperOrders) {
            MiniAppWallpaperOrder update = new MiniAppWallpaperOrder();
            update.setId(appWallpaperOrder.getId());
            update.setPayTime(new Date());
            update.setPayStatus(1);
            update.setTranNo(transactionId);
            iMiniAppWallpaperOrderService.updateById(update);
        }
    }

    @Override
    public PayBusineesTypeEnum getBusinessType() {
        return PayBusineesTypeEnum.WALLPAPER;
    }
}
