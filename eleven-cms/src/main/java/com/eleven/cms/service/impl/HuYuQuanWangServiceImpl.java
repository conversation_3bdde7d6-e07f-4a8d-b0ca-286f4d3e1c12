package com.eleven.cms.service.impl;

import com.eleven.cms.ad.HuYuQuanWangProperties;
import com.eleven.cms.config.HeTuFenShengChannel;
import com.eleven.cms.config.HeTuFenShengChongQingChannel;
import com.eleven.cms.config.HuYuQuanWangChannel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.SignatureUtils;
import com.eleven.cms.util.Sm2Util;
import com.eleven.cms.vo.HeTuFenShengNotify;
import com.eleven.cms.vo.HuYuChongQingOrderNotifyRequest;
import com.eleven.cms.vo.HuYuQuanWangOrderNotifyRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/26 15:32
 **/
@Slf4j
@Service
public class HuYuQuanWangServiceImpl implements IHuYuQuanWangService {
    //通知类型 1-订购成功
    public static final int SUB_SUCCESS = 1;
    //通知类型 2-退订成功
    public static final int UN_SUB_SUCCESS = 2;
    private static final String LOG_TAG = "咪咕互娱全网业务";
    @Autowired
    private HuYuQuanWangProperties huyuQuanWangProperties;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    IChannelService channelService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    /**
     * 手机号加密
     * @param mobile
     * @param channel
     * @return
     */
    @Override
    public Result<?> mobileSm2(String mobile, String channel) {


        final HuYuQuanWangChannel huyuQuanWangChannel=huyuQuanWangProperties.getChannelMap().get(channel);
        if(huyuQuanWangChannel==null){
            log.info("{}-手机号加密-手机号:{},渠道号:{},渠道号错误:", LOG_TAG, mobile,channel);
            return Result.error("渠道号错误");
        }
        Sm2Util sm2Util=new Sm2Util(huyuQuanWangChannel.getPublicKey());
        String tel=sm2Util.encrypt(mobile);


        final ObjectNode objectNode = mapper.createObjectNode()
                .put("userCode", tel)
                .put("propsId", huyuQuanWangChannel.getPropsId())
                .put("chargeId", huyuQuanWangChannel.getChargeId())
                .put("channelId", huyuQuanWangChannel.getChannelId());
        return Result.ok(objectNode);
    }


    /**
     * 手机号t退订查询订单号码
     * @param mobile
     * @return
     */
    @Override
    public Result<?> unSub(String mobile) {
        List<String> channelList = huyuQuanWangProperties.getChannelMap().keySet().stream().collect(Collectors.toList());
        Subscribe subscribe=subscribeService.lambdaQuery().select(Subscribe::getIspOrderNo).eq(Subscribe::getMobile,mobile).in(Subscribe::getChannel,channelList).eq(Subscribe::getStatus,1).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(subscribe==null){
            return Result.error("渠道号错误");
        }
        return Result.ok(subscribe.getIspOrderNo());
    }

    @Override
    public HeTuFenShengNotify huyuOrderNotify(String requestBody){
        HuYuQuanWangOrderNotifyRequest orderNotifyRequest = null;
        try {
            orderNotifyRequest = mapper.readValue(requestBody, HuYuQuanWangOrderNotifyRequest.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("互娱全网订单通知异常-请求参数:{}",requestBody,e);
        }
        String orderId=orderNotifyRequest.getOrderId();
        String chargeId=orderNotifyRequest.getChargeId();
        String sign=orderNotifyRequest.getSign();
        int type=orderNotifyRequest.getType();
        String userCode=orderNotifyRequest.getUserCode();

        Map<String, List<String>> mapInversed =huyuQuanWangProperties.getChannelMap().entrySet().stream().collect(Collectors.groupingBy(entry -> entry.getValue().getChargeId(), Collectors.mapping(Map.Entry::getKey, Collectors.toList())));
        final String channel = mapInversed.get(chargeId).get(0);
        HuYuQuanWangChannel huYuQuanWangChannel=huyuQuanWangProperties.getChannelMap().get(channel);
        Sm2Util sm2Util = new Sm2Util(huYuQuanWangChannel.getPublicKey(), huYuQuanWangChannel.getPrivateKey());
        String mobile=sm2Util.decrypt(userCode);
        log.info("互娱全网订单通知=>手机号:{},通知类型:{},计费点ID:{},订单号:{}",mobile, type,chargeId,orderId);
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getIspOrderNo, orderId).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            try {
                String ourSign = DigestUtils.md5DigestAsHex(("MiGu" + orderId + chargeId + "notice").getBytes(StandardCharsets.UTF_8.name())).toUpperCase();
                if(!ourSign.equals(sign)){
                    log.warn("{}-验证签名错误,通知数据=>手机号:{},通知类型:{},计费点ID:{},订单号:{},ourSign:{},sign:{}",LOG_TAG,mobile, type,chargeId,orderId,ourSign,sign);
                    return new HeTuFenShengNotify("060071","签名校验失败");
                }
            } catch (Exception e) {
                log.error("{}-生成签名错误,通知数据=>手机号:{},通知类型:{},计费点ID:{},订单号:{},sign:{}",LOG_TAG,mobile, type,chargeId,orderId,sign,e);
                return new HeTuFenShengNotify("060071","签名校验失败");
            }
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (SUB_SUCCESS==type && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                //订阅成功
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);

                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);

                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }

            }else if (UN_SUB_SUCCESS==type && SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
                upd.setVerifyStatus(0);
                upd.setVerifyStatusDaily(0);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
        return new HeTuFenShengNotify("000000","处理成功");
    }
}
