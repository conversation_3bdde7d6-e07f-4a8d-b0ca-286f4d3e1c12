package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.MusicHotLevel;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MusicVo;

/**
 * @Description: 人气榜单歌曲
 * @Author: jeecg-boot
 * @Date:   2023-11-21
 * @Version: V1.0
 */
public interface IMusicHotLevelService extends IService<MusicHotLevel> {

    FebsResponse setVrbtMusic(String mobile, String channelCode, String copyrightId);

    IPage<MusicVo> queryMusicHotLevel(Page<MusicVo> page);
}
