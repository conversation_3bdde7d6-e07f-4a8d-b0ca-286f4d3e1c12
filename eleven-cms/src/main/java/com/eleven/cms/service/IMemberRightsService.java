package com.eleven.cms.service;

import com.eleven.cms.entity.MemberRights;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * @Description: 视听会员权益
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
public interface IMemberRightsService extends IService<MemberRights> {

    List<MemberRights> queryRightsByMemberIds(List<String> memberIds, String rightsName);

    Optional<MemberRights> queryMemberRightsDetail(String rightsId);

    Optional<MemberRights> queryMemberRightsByName(String rightsName);

    void updateMemberRightsList(List<MemberRights> list);

    List<MemberRights> queryShopByProductId(MemberRights rights);

    List<MemberRights> queryRightsList();

    void addMemberRightsList(List<MemberRights> list);
}
