package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.*;
import com.eleven.cms.mapper.ColumnMusicMapper;
import com.eleven.cms.mapper.ColumnMapper;
import com.eleven.cms.service.IColumnMusicService;
import com.eleven.cms.service.IColumnService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class ColumnServiceImpl extends ServiceImpl<ColumnMapper, Column> implements IColumnService {
    //视频彩铃抖音小程序首页轮播
    private static final String HOME_CAROUSEL ="HOME_CAROUSEL";
    //视频彩铃抖音小程序每日推荐
    private static final String EVERYDAY_SPREAD ="EVERYDAY_SPREAD";
    //视频彩铃抖音小程序人气榜单
    private static final String HOT_LEVEL ="HOT_LEVEL";
    //视频彩铃抖音小程序趣味视频
    private static final String AMUSING_VIDEO ="AMUSING_VIDEO";
    //视频彩铃抖音小程序中间导航栏
    private static final String NAVIGN_COLUMN ="NAVIGN_COLUMN";
    //视频彩铃抖音小程序为您推荐
    private static final String EXCLUSIVE_SPREAD ="EXCLUSIVE_SPREAD";
    //视频彩铃抖音小程序精彩推荐
    private static final String SPLENDID_SPREAD ="SPLENDID_SPREAD";
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Autowired
    private ColumnMapper columnMapper;
    @Autowired
    private ColumnMusicMapper columnMusicMapper;
    @Autowired
    private IColumnMusicService columnMusicService;
    @Autowired
    RedisUtil redisUtil;

    //10日秒数
    private static final Integer EVERY_DAY_SECOND_COUNT=60 * 60 * 24 * 9;

    @CacheEvict(cacheNames = CacheConstant.CMS_COLUMN_CACHE, allEntries=true, beforeInvocation=true)
    @Override
    @Transactional
    public void saveMain(Column column, List<ColumnMusic> columnMusicList) {
        columnMapper.insert(column);
        if (columnMusicList != null && columnMusicList.size() > 0) {
            for (int i = 0; i < columnMusicList.size(); i++) {
                //外键设置
                ColumnMusic entity = columnMusicList.get(i);
                entity.setColumnId(column.getId());
                entity.setPriority(i+1);
                columnMusicMapper.insert(entity);
            }
        }
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_COLUMN_CACHE, allEntries=true, beforeInvocation=true)
    @Override
    @Transactional
    public void updateMain(Column column, List<ColumnMusic> columnMusicList) {
        columnMapper.updateById(column);

        //1.先删除子表数据
        columnMusicMapper.deleteByMainId(column.getId());

        //2.子表数据重新插入
        if (columnMusicList != null && columnMusicList.size() > 0) {
            for (int i = 0; i < columnMusicList.size(); i++) {
                //外键设置
                ColumnMusic entity = columnMusicList.get(i);
                entity.setColumnId(column.getId());
                entity.setPriority(i+1);
                columnMusicMapper.insert(entity);
            }
        }
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_COLUMN_CACHE, allEntries=true, beforeInvocation=true)
    @Override
    @Transactional
    public void delMain(String id) {
        columnMusicMapper.deleteByMainId(id);
        columnMapper.deleteById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_COLUMN_CACHE, allEntries=true, beforeInvocation=true)
    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            columnMusicMapper.deleteByMainId(id.toString());
            columnMapper.deleteById(id);
        }
    }

    @Cacheable(cacheNames = CacheConstant.CMS_COLUMN_CACHE,key = "'all'",unless = "#result==null")
    @Override
    public List<ColumnDetail> listColumnDetail() {
        return this.lambdaQuery()
                   .orderByAsc(Column::getPriority)
                   .list()
                   .stream()
                   .map(column -> {
                       ColumnDetail columnDetail = new ColumnDetail();
                       BeanUtils.copyProperties(column, columnDetail);
                       List<Music> musicList = columnMapper.getMusicListById(column.getId());
                       columnDetail.setMusicList(musicList);
                       return columnDetail;
                   })
                   .collect(Collectors.toList());
    }

    @Override
    public IPage<Column> pageNew(Page<Column> page, QueryWrapper<Column> queryWrapper, Map<String, String[]> parameterMap) {

        Date createTimeBegin = null;
        Date createTimeEnd = null;
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Column column = new Column();
        try{
            if(parameterMap != null){
                String[] createTimeBegins = parameterMap.get("createTime_begin");
                String[] createTimeEnds = parameterMap.get("createTime_end");
                String[] titles = parameterMap.get("title");
                String[] isCarousels = parameterMap.get("isCarousel");
                String[] columnClassNames = parameterMap.get("columnClassName");
                if(createTimeBegins != null && createTimeBegins.length > 0){
                    createTimeBegin = df.parse(createTimeBegins[0]);
                }
                if(createTimeEnds != null && createTimeEnds.length > 0){
                    createTimeEnd = new Date(df.parse(createTimeEnds[0]).getTime() + 24*60*60*1000-1);
                }
                if(titles != null && titles.length > 0){
                    column.setTitle(titles[0]);
                }
                if(isCarousels != null && isCarousels.length > 0){
                    column.setIsCarousel(isCarousels[0]);
                }
                if(columnClassNames != null && columnClassNames.length > 0){
                    column.setColumnClassName(columnClassNames[0]);
                }
            }
        }catch (Exception e){
            log.error("pageNew出错：",e);
        }
        IPage<Column> pageList = columnMapper.findByPage(page, column,createTimeBegin,createTimeEnd);
        return pageList;
    }

    @Override
    public void copy(String oldColumnClassName,String newColumnClassName) {
        //查询 旧栏目数据
        List<Column> columns = this.lambdaQuery()
                .eq(Column::getColumnClassName, oldColumnClassName).list();
        log.info("该条件下共【" + columns.size() + "】个栏目");
        for (Column column : columns) {
            //查询栏目下栏目歌曲关联关系
            List<ColumnMusic> list = columnMusicService.lambdaQuery().eq(ColumnMusic::getColumnId, column.getId()).list();
            //新增栏目
            String id = IdWorker.getId() + "";
            column.setId(id);
            column.setColumnClassName(newColumnClassName);
            this.save(column);
            //新增栏目歌曲关系
            List<ColumnMusic> insert = new ArrayList<>();
            for (ColumnMusic columnMusic : list) {
                columnMusic.setColumnId(id);
                columnMusic.setId(null);
                insert.add(columnMusic);
            }
            columnMusicService.saveBatch(insert);
            log.info("新增歌曲关联关系【" + insert.size() + "】条");
        }
    }

    @Override
    public List<ColumnDetail> topic() {

        return this.lambdaQuery()
                .eq(Column::getColumnClassName,"XCX_TOPIC")
                .orderByAsc(Column::getPriority)
                .list()
                .stream()
                .map(column -> {
                    ColumnDetail columnDetail = new ColumnDetail();
                    BeanUtils.copyProperties(column, columnDetail);
                    List<Music> musicList = columnMapper.getMusicListById(column.getId());
                    columnDetail.setMusicList(musicList);
                    return columnDetail;
                })
                .collect(Collectors.toList());
    }

    //@Override
    //public IPage<ColumnDetail> queryColumnMusicVoPage(String columnId, HttpServletRequest req, Integer pageNo,
    //                                                 Integer pageSize) {
    //    ColumnMusic columnMusic = new ColumnMusic();
    //    columnMusic.setColumnId(columnId);
    //    QueryWrapper<ColumnMusic> queryWrapper = QueryGenerator.initQueryWrapper(columnMusic, req.getParameterMap());
    //    Page<ColumnMusic> page = new Page<ColumnMusic>(pageNo, pageSize);
    //    IPage<ColumnMusic> pageList = columnMusicService.page(page, queryWrapper);
    //    pageList.getRecords().stream().map(cm -> {
    //
    //    })
    //}
    @Cacheable(cacheNames = CacheConstant.CMS_VRBT_DYXCX_HOME_IMG_CACHE,key = "'all'",unless = "#result==null")
    @Override
    public ColumnObject queryColumnList() {
        List<Column> homeCarouselList = this.lambdaQuery().select(Column::getId,Column::getImgUrl,Column::getPriority,Column::getTitle,Column::getVideoUrl,Column::getBackdropColor,Column::getTextColor,Column::getTextIntroduction).eq(Column::getColumnClassName,HOME_CAROUSEL).orderByAsc(Column::getPriority).list();

        Column everydaySpread =this.lambdaQuery().select(Column::getId,Column::getImgUrl,Column::getTitle,Column::getVideoUrl,Column::getBackdropColor,Column::getTextColor,Column::getTextIntroduction).eq(Column::getColumnClassName,EVERYDAY_SPREAD).orderByDesc(Column::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();

        Column hotLevel = this.lambdaQuery().select(Column::getId,Column::getImgUrl,Column::getTitle,Column::getVideoUrl,Column::getBackdropColor,Column::getTextColor,Column::getTextIntroduction).eq(Column::getColumnClassName,HOT_LEVEL).orderByDesc(Column::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();

        Column amusingVideo = this.lambdaQuery().select(Column::getId,Column::getImgUrl,Column::getTitle,Column::getVideoUrl,Column::getBackdropColor,Column::getTextColor,Column::getTextIntroduction).eq(Column::getColumnClassName,AMUSING_VIDEO).orderByDesc(Column::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();

        List<Column> navignColumnList = this.lambdaQuery().select(Column::getId,Column::getImgUrl,Column::getPriority,Column::getTitle,Column::getVideoUrl,Column::getBackdropColor,Column::getTextColor,Column::getTextIntroduction).eq(Column::getColumnClassName,NAVIGN_COLUMN).orderByAsc(Column::getPriority).list();
        ColumnObject  columnObject= new ColumnObject();
        columnObject.setHomeCarouselList(homeCarouselList);
        columnObject.setEverydaySpread(everydaySpread);
        columnObject.setHotLevel(hotLevel);
        columnObject.setAmusingVideo(amusingVideo);
        columnObject.setNavignColumnList(navignColumnList);
        return columnObject;
    }
    @Cacheable(cacheNames = CacheConstant.CMS_VRBT_DYXCX_MUSIC_CACHE,key = "#columnId+'_'+#columnClassName+'_'+#page.getCurrent()+'_'+#page.getSize()",unless = "#result==null")
    @Override
    public IPage<MusicVo> queryMusicList(String columnId,String columnClassName,Page<MusicVo> page) {
        Column column=null;
        if(StringUtils.isNotEmpty(columnId)){
            column = this.lambdaQuery().select(Column::getId,Column::getImgUrl,Column::getColumnClassName).eq(Column::getId,columnId).orderByDesc(Column::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        }else if(StringUtils.isNotEmpty(columnClassName)){
            column = this.lambdaQuery().select(Column::getId,Column::getImgUrl,Column::getColumnClassName).eq(Column::getColumnClassName,columnClassName).orderByDesc(Column::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        }
        if(column!=null && EVERYDAY_SPREAD.equals(column.getColumnClassName())){
            int day=LocalDate.now().getDayOfMonth()%10;
            if(day==0){
                day=10;
            }
            IPage<MusicVo> pageList=columnMusicMapper.selectHomeColumnMusicVoPage(page,column.getId(),String.valueOf(day));
            return pageList;
        }else if(column!=null){
            IPage<MusicVo> pageList= columnMusicMapper.selectColumnMusicVoPage(page,column.getId());
            return pageList;
        }
        return null;
    }
}
