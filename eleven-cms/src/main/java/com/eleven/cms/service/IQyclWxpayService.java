package com.eleven.cms.service;


import com.eleven.cms.dto.*;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.jeecg.common.api.vo.Result;
import org.springframework.data.util.Pair;

import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/7/14 15:34
 * Desc: 微信支付
 */
public interface IQyclWxpayService {
    Map<String, String> pay(String outTradeNo, String totalAmount, String subject,String notifyUrl,String tradeType,String openId,String businessType) throws Exception;

    Map<String, String> dyPay(String outTradeNo, String totalAmount, String subject,String tradeType,String businessType) throws Exception;

    QyclWxpayNotifyParam payResultNotify(String notifyXml) throws Exception;

//    String weChatCode(String redirectUrl,String businessType);

    FebsResponse weChatAppletsOpenId(String openId, String code,String businessType) throws Exception;
    String getWeChatOpenId( String code,String businessType) throws Exception;

    FebsResponse weChatAuth(String openId,String code,String businessType) throws Exception ;

//    FebsResponse weChatInfo(String openId,String accessToken);

    FebsResponse wechatToken(String businessType) throws Exception;

    FebsResponse weChatScheme(String accessToken,String ipAddr,String query);

    FebsResponse wechatRefund(String outTradeNo,String outRefundNo,String refundAmount,String totalAmount,String appId,String mchId);

    WxInfoNotifyParam refundResultNotify(String resultNotifyXml);


    FebsResponse tiktokAppletsOpenId(String openId, String code,String anonymousCode,String tradeType,String businessType) throws Exception;

    Pair<Boolean, DypayNotifyMsg> parsePaymentCallback(String raw) throws JsonProcessingException;
    Pair<Boolean, DypayRefundMsg> parseRefundCallback(String raw) throws JsonProcessingException;
    FebsResponse dyRefund(String outTradeNo,String outRefundNo,String refundAmount,String appId,String mchId) throws Exception;

    FebsResponse wechatQueryComplainDetail(String complaintId,String mchId);

    FebsResponse createComplainUrl(String url,String mchId);

    FebsResponse queryComplainUrl(String mchId);

    FebsResponse deleteComplainUrl(String mchId);

    FebsResponse updateComplainUrl(String url,String mchId);

    FebsResponse wechatSolveComplain(String complaintId, String mchId, String responseContent, String responseImages, String jumpUrl, String jumpUrlText);

    FebsResponse queryComplainList(Integer limit, Integer offset, String mchId, String beginDate, String endDate);

    FebsResponse feedbackOverComplain(String complaintId, String mchId);


    Map<String, String> shopPay(String outTradeNo, String totalAmount, String subject,String notifyUrl,String tradeType,String openId,String businessType) throws Exception;

    Map<String, String> douYinPay(String outTradeNo, String totalAmount, String subject,String tradeType,String businessType)throws Exception;


    DouYinPayNotifyMsg douyinPaymentCallback(String raw, String contentType,String idEntifyName,String logId,String nonceStr,String signaTure,String timeStamp)throws Exception;

    FebsResponse douYinRefund(String outTradeNo,String outRefundNo,String refundAmount,String appId,String mchId)throws Exception;

    DouYinRefundNotifyMsg douyinTradeRefundNotify(String raw, String contentType,String idEntifyName,String logId,String nonceStr,String signaTure,String timeStamp)throws Exception;

//    FebsResponse douYinCreateRefund(String orderId,String outTradeNo,String outRefundNo,String refundAmount,String appId,String mchId) throws Exception;

    DouYinTokenRequest douYinAuth(String code,String anonymousCode,String tradeType,String businessType)throws Exception;

    FebsResponse wechatRefund(String outTradeNo,String outRefundNo,String refundAmount,String totalAmount,String appId,String mchId,String refundNotifyUrl);


    Result<?> vrbtPay(String mobile, String outTradeNo, String subject, String tradeType, String openId, String channel, String subChannel,String returnUrl,String ringType, String ringId,String copyRightId,String bizType,String ringName);
}


