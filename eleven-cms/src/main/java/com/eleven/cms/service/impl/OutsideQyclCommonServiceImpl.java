package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.JiangxiCompanyConfig;
import com.eleven.cms.config.JiangxiYidongProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.JiangxiYidongService;
import com.eleven.cms.remote.SichuanMobileFlowPacketService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.JiangxiYidongOrderResult;
import com.eleven.cms.vo.JiangxiYidongResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("outsideQyclCommonService")
@Slf4j
public class OutsideQyclCommonServiceImpl implements IBizCommonService {

    @Autowired
    SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    JiangxiYidongService jiangxiYidongService;
    @Autowired
    JiangxiYidongProperties jiangxiYidongProperties;
    @Autowired
    IChannelService channelService;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSmsZyhlQycl(subscribe.getMobile(), subscribe.getChannel());
        if (billingResult.isOK()) {
            subscribe.setIspOrderNo(billingResult.getTransId());
            subscribeService.createSubscribeDbAndEs(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("短信发送失败,请稍后再试");

            try {
                String errorMsg="{\"code\":\""+billingResult.getCode()+"\",\"message\":\""+billingResult.getMessage()+"\"}";
                return Result.errorSendSmsDelayMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSendSmsDelayerror();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //此处保存已提交验证码
        if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setBizTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        SpringContextUtils.getBean(YidongVrbtCrackService.class).smsCode(subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
        return Result.ok("提交验证码成功");
    }
}
