package com.eleven.cms.service;

import com.eleven.cms.dto.PptvApiResult;
import com.eleven.cms.entity.PptvGiveLog;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.jeecg.common.api.vo.Result;

public interface IPPTVApiService {
   void pptvRightsByRechargeData(ObjectNode dataNode);

   Result<?> pptvRightsRecharge(String phone,String outOrderId,String serviceId,String channelCode);

   Result<?> pptvRightsQuery(PptvApiResult pptvApiResult);
}
