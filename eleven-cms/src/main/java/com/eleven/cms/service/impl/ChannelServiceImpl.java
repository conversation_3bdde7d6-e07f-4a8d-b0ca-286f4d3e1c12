package com.eleven.cms.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.ad.AdReportService;
import com.eleven.cms.ad.IAdFeedbackCommonService;
import com.eleven.cms.ad.IAdFeedbackService;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Channel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.ChannelMapper;
import com.eleven.cms.service.IAccountCofigService;
import com.eleven.cms.service.IAdPlatformService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @Description: cms_channel
 * @Author: jeecg-boot
 * @Date:   2020-10-23
 * @Version: V1.0
 */
@Service
@Slf4j
public class ChannelServiceImpl extends ServiceImpl<ChannelMapper, Channel> implements IChannelService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ChannelMapper channelMapper;
    @Autowired
    private IAdPlatformService adPlatformService;
    @Autowired
    private IAccountCofigService accountCofigService;
    @Autowired
    AdReportService adReportService;

    @Cacheable(cacheNames = CacheConstant.CMS_CHANNEL_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    @Override
    public Channel findByTitle(String subChannel){
        return this.lambdaQuery().eq(Channel::getTitle,subChannel).eq(Channel::getStatus,0).one();
    }

    @Cacheable(cacheNames = CacheConstant.CMS_CHANNEL_AD_PLATFORM_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    @Override
    public AdPlatform findAdPlatformByTitle(String subChannel){
        final Channel channel = this.lambdaQuery()
                                .eq(Channel::getTitle, subChannel)
                                .eq(Channel::getStatus, 0)
                                .one();
        if(channel==null){
            return null;
        }
        return adPlatformService.getById(channel.getAdPlatformId());
    }

//    @Override
//    public void AdEffectFeedback(Subscribe subscribe, Integer status) {
//        final String subChannel = subscribe.getSubChannel();
//        //未使用渠道号不需要上报渠道
//        if(StringUtils.isEmpty(subChannel)){
//            return;
//        }
//        String mobile = subscribe.getMobile();
//        //成功开通才上报,需要判断该订单的手机号当天是否已有过成功上报的记录,没有的才上报
//        if(! SpringContextUtils.getBean(ISubscribeService.class).isAdUnFeedbackToday(mobile)) {
//            return;
//        }
//
//        adReportService.report(subscribe.getId(),subscribe.getDeviceInfo(),subscribe.getSubChannel(),mobile,subscribe.getIp(),subscribe.getUserAgent(),subscribe.getSource(),status);
//
//
////        final IChannelService currentProxy = (IChannelService) AopContext.currentProxy();
////        Channel channel = currentProxy.findByTitle(subChannel);
////        if(channel == null){
////            log.info("信息流广告转化上报未找到渠道号或渠道无效=>订单号:{},手机号:{},渠道号:{}",subscribe.getId(),subscribe.getMobile(),
////                    subChannel);
////            return;
////        }
////
////        final String adApiBeanName = channel.getAdApiBeanName();
////        if(StringUtils.isEmpty(adApiBeanName)){
////            log.info("信息流广告转化上报未正确配置上报参数=>订单号:{},手机号:{},渠道号:{}",subscribe.getId(),subscribe.getMobile(),
////                    subChannel);
////            return;
////        }
////        try {
////            final IAdFeedbackService adFeedbackService = SpringContextUtils.getBean(adApiBeanName, IAdFeedbackService.class);
////            adFeedbackService.feedback(subscribe);
////            subscribe.setTuiaFeedbackStatus(1);
////        } catch (Exception e) {
////            log.info("信息流广告转化上报未正确配置上报参数=>订单号:{},手机号:{},渠道号:{}",subscribe.getId(),subscribe.getMobile(), subChannel);
////            e.printStackTrace();
////        }
//
//        //开通成功,头条渠道需要调用线索api上报 (现在已有包月不会再推送开通了,有包月要订彩铃的也不再需要开通了,会再服务端进行开通)
//        //if (SUB_CHANNEL_TOUTIAO.equals(subscribe.getSubChannel())|| SUB_CHANNEL_TUIA.equals(subscribe.getSubChannel()) || SUB_CHANNEL_TA01.equals(subscribe.getSubChannel()) || SUB_CHANNEL_TA02.equals(subscribe.getSubChannel())){
//        //    //暂不给头条回传数据
//        //    //toutiaoAdApiService.trackFeedback(subscribe.getReferer(),new Date());
//        //    //推啊广告转化上报
//        //    tuiaAdApiService.effectFeedback(subscribe.getDeviceInfo(),subscribe.getIp(),subscribe.getUserAgent());
//        //    subscribe.setTuiaFeedbackStatus(1);
//        //}
//        //
//        //if ((SUB_CHANNEL_BXM1.equals(subscribe.getSubChannel())||SUB_CHANNEL_BX01.equals(subscribe.getSubChannel()))) {
//        //    //变现猫广告转化上报
//        //    bianxianmaoAdService.effectFeedback(subscribe.getDeviceInfo());
//        //    subscribe.setTuiaFeedbackStatus(1);
//        //}
//        //
//        //if (SUB_CHANNEL_DM01.equals(subscribe.getSubChannel()) ) {
//        //    doumengAdService.orderDeepTranslate(subscribe.getDeviceInfo());
//        //    subscribe.setTuiaFeedbackStatus(1);
//        //}
//        //
//        //if ((SUB_CHANNEL_JB02.equals(subscribe.getSubChannel())||SUB_CHANNEL_QTT1.equals(subscribe.getSubChannel())||SUB_CHANNEL_QTT2.equals(subscribe.getSubChannel())||SUB_CHANNEL_QTT3.equals(subscribe.getSubChannel()) ||SUB_CHANNEL_QTT4.equals(subscribe.getSubChannel()) )) {
//        //    qutoutiaoAdService.effectFeedback(subscribe.getDeviceInfo());
//        //    subscribe.setTuiaFeedbackStatus(1);
//        //}
//        //
//        //if (SUB_CHANNEL_HUDT.equals(subscribe.getSubChannel())) {
//        //    hudongtuiAdService.effectFeedback(subscribe.getDeviceInfo(),mobile);
//        //    subscribe.setTuiaFeedbackStatus(1);
//        //}
//
//    }

    @Override
    public boolean aopProxyTest(String subChannel){
        final IChannelService currentProxy = (IChannelService) AopContext.currentProxy();
        System.out.println(AopUtils.isAopProxy(currentProxy));
        System.out.println(AopUtils.isJdkDynamicProxy(currentProxy));
        System.out.println(AopUtils.isCglibProxy(currentProxy));
        boolean exists =  currentProxy.findByTitle(subChannel)!=null;
        return exists;
    }

    @Override
    public boolean isLegal(String subChannel) {
        final IChannelService currentProxy = (IChannelService) AopContext.currentProxy();
        return currentProxy.findByTitle(subChannel)!=null;
    }

    @Override
    public boolean needSmsValidate(String subChannel) {
        final IChannelService currentProxy = (IChannelService) AopContext.currentProxy();
        final Channel channel = currentProxy.findByTitle(subChannel);
        if(channel == null) {
            return true;
        }
        return channel.getValidateStatus().equals(1);
    }
    @Override
    @DS("slave")
    public List<Map<String, Object>> selectByCondition() {
        return  jdbcTemplate.queryForList("select * from t_role");
    }

    @Override
    public IPage<Channel> findByPage(Page<Channel> page, QueryWrapper<Channel> queryWrapper) {
        IPage<Channel> resultPage = channelMapper.findByPage(page, queryWrapper.getEntity());
        return resultPage;
    }

    /**
     *  增加新的方式实现
     *
     *
     * @param subscribe
     * @param status
     */
    @Override
    public void AdEffectFeedbackNew(Subscribe subscribe, Integer status) {
        final String subChannel = subscribe.getSubChannel();
        //未使用渠道号不需要上报渠道
        if(StringUtils.isEmpty(subChannel)){
            return;
        }
        String mobile = subscribe.getMobile();
        //成功开通才上报,需要判断该订单的手机号当天是否已有过成功上报的记录,没有的才上报
//        if(!SpringContextUtils.getBean(ISubscribeService.class).isAdUnFeedbackToday(mobile) && !AdReportService.SCMCC_WHITE_NUMBER.equals(mobile)) {
//            return;
//        }
        //判断是否为指定渠道
//        if (BizConstant.UNICOM_UP_CHANNEL_LIST.contains(subChannel)) {
//            //只上报联通
//            if (!MobileRegionResult.ISP_LIANTONG.equals(subscribe.getIsp())) {
//                return;
//            }
//        } else {
//            //不为指定渠道 如果为联通就不上报
//            if (MobileRegionResult.ISP_LIANTONG.equals(subscribe.getIsp())) {
//                return;
//            }
//        }
//        adReportService.report(subscribe.getId(), subscribe.getDeviceInfo(), subscribe.getSubChannel(), mobile, subscribe.getIsp(), subscribe.getIp(), subscribe.getUserAgent(), subscribe.getSource(), status, subscribe.getChannel());
        adReportService.sendReportMqMessage(subscribe.getId(), subscribe.getDeviceInfo(), subscribe.getSubChannel(), mobile, subscribe.getIsp(), subscribe.getIp(), subscribe.getUserAgent(), subscribe.getSource(), status, subscribe.getChannel(),subscribe.getProvince());
    }
}
