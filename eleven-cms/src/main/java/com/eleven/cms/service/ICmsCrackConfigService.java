package com.eleven.cms.service;

import com.eleven.cms.entity.CmsCrackConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: cms_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
public interface ICmsCrackConfigService extends IService<CmsCrackConfig> {

    CmsCrackConfig getCrackConfigByChannel(String channel);

    List<String> getChannnelByServiceId(String serviceId);

    void updateCrackConfig(CmsCrackConfig cmsCrackConfig);

    /**
     * 查询渠道号列表
     *
     * @return List<String>
     */
    List<String> queryChannelList();
}
