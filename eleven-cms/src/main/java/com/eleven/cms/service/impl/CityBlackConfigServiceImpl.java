package com.eleven.cms.service.impl;

import com.eleven.cms.entity.CityBlackConfig;
import com.eleven.cms.mapper.CityBlackConfigMapper;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.ICityBlackConfigService;
import com.eleven.cms.vo.MobileRegionResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: cms_city_black_config
 * @Author: jeecg-boot
 * @Date:   2023-02-13
 * @Version: V1.0
 */
@Service
public class CityBlackConfigServiceImpl extends ServiceImpl<CityBlackConfigMapper, CityBlackConfig> implements ICityBlackConfigService {

    @Autowired
    private MobileRegionService mobileRegionService;
    @Override
    public boolean phoneValidate(String channelCode, String mobile) {
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(mobileRegionResult != null){
            CityBlackConfig cityBlackConfig = this.lambdaQuery()
                    .eq(CityBlackConfig::getChannel, channelCode)
                    .eq(CityBlackConfig::getCity, mobileRegionResult.getCity())
                    .one();
            if (cityBlackConfig != null && StringUtils.isNotBlank(cityBlackConfig.getBlackConfig())){
                for (String config : cityBlackConfig.getBlackConfig().split(",")) {
                    if(mobile.startsWith(config)){
                        return false;
                    }
                }
            }
        }
        return true;
    }
}
