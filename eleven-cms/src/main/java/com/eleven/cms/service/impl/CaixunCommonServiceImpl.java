package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.CaixunService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.CaixunResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("caixunCommonService")
@Slf4j
public class CaixunCommonServiceImpl implements IBizCommonService {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    CaixunService caixunService;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        CaixunResult caixunResult = caixunService.getSms(subscribe.getMobile(), subscribe.getChannel(), subscribe.getUserAgent(), subscribe.getReportTicket());
        if (caixunResult.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribe.setIspOrderNo(caixunResult.getData().getOrderId());
            subscribeService.createSubscribeDbAndEs(subscribe);
            //获取验证码成功将返回数据写入redis保存5分钟
            String resultKey = CacheConstant.CAIXUN_SMS_CODE_RESULT + ":" + subscribe.getChannel() + ":" + subscribe.getMobile();
            redisUtil.set(resultKey, caixunResult.getData(), 300L);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"retCode\":\""+caixunResult.getCode()+"\",\"retMsg\":\""+caixunResult.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        String resultKey = CacheConstant.CAIXUN_SMS_CODE_RESULT + ":" + subscribe.getChannel() + ":" + subscribe.getMobile();
        CaixunResult.Result smsCodeResul = (CaixunResult.Result) redisUtil.get(resultKey);
        CaixunResult caixunResult = caixunService.smsCode(subscribe.getMobile(), subscribe.getChannel(), smsCodeResul.getGuid(), smsCodeResul.getOrderId(), subscribe.getSmsCode(), subscribe.getUserAgent(), smsCodeResul.getToken(), subscribe.getReportTicket());
        if (caixunResult == null) {
            return Result.error("请重新获取验证码");
        }
        if (caixunResult.isOK()) {
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setOpenTime(new Date());
            upd.setResult("订购成功");
            upd.setExtra(subscribe.getSmsCode());
            subscribeService.updateSubscribeDbAndEs(upd);
            subscribeService.saveChannelLimit(subscribe);
            //信息流广告转化上报
            if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            } else {
                channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
            }
            return Result.ok("订阅成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(caixunResult.getMessage());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
