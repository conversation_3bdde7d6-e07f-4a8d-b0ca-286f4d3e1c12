package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.QyclWXPayPropertiesConfig;
import com.eleven.cms.dto.QyclWxpayNotifyParam;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.entity.YinglouOrder;
import com.eleven.cms.entity.YinglouUser;
import com.eleven.cms.mapper.YinglouOrderMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.YinglouOrderDto;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.wxpay.sdk.QyclWXPay;
import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 影楼订单表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
@Slf4j
@Service
public class YinglouOrderServiceImpl extends ServiceImpl<YinglouOrderMapper, YinglouOrder> implements IYinglouOrderService {
    @Autowired
    private IYinglouUserService yinglouUserService;
    @Autowired
    private QyclWXPayPropertiesConfig payConfig;
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private QyclWXPay wxPay;
    @Autowired
    private MiguApiService miguApiService;
    //未支付
    public static final int NOT_PAY_STATUS = -1;
    //支付失败
    public static final int FAIL_PAY_STATUS =0;
    //支付成功
    public static final int SUCCESS_PAY_STATUS =1;
    //未使用
    public static final int NOT_USE_STATUS = -1;
    //开通失败
    public static final int FAIL_OPEN_STATUS =0;
    //开通成功
    public static final int SUCCESS_OPEN_STATUS =1;
    //退款失败
    public static final int FAIL_REFUND_STATUS =5;
    //退款中
    private static final Integer REFUND_PREPARE=6;
    //退款成功
    public static final int SUCCESS_REFUND_STATUS =4;
    @Autowired
    private IYinglouRingService yinglouRingService;
    @Autowired
    private IQyclWxpayService wxpayService;
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    /**
     * 支付设置配置文件
     * @param outTradeNo
     * @param tradeType
     * @param businessType
     * @throws Exception
     */
    private void payConfig(String outTradeNo,String tradeType,String businessType)throws Exception{
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatConfig(tradeType,businessType);
        if(wechatConfigLog==null){
            if(StringUtils.isNotEmpty(outTradeNo)){
                log.info("微信支付下单失败:订单号:{}", outTradeNo);
                throw new Exception("支付失败,请稍后再试!");
            }
            log.info("微信配置查询失败:支付类型:{},业务类型:{}", tradeType,businessType);
            throw new Exception("配置错误,请稍后再试!");
        }else{
            payConfig.setAppID(wechatConfigLog.getAppId());
            payConfig.setMchID(wechatConfigLog.getMchId());
            payConfig.setKey(wechatConfigLog.getMchKey());
            payConfig.setNotifyUrl(wechatConfigLog.getNotifyUrl());
            payConfig.setTotalAmount(wechatConfigLog.getTotalAmount());
            payConfig.setSpbillCreateIp(wechatConfigLog.getServerIp());
            payConfig.setAppSecret(wechatConfigLog.getAppSecret());
            payConfig.setPublicName(wechatConfigLog.getPublicName());
        }
    }

    /**
     * 手机号开通成功更新支付订单的使用状态
     * @param mobile
     * @param status
     * @return
     */
    @Override
    public Result updateStatus(String mobile,int status){
         //查询已支付并且未使用的
         YinglouOrder yinglouOrder=this.lambdaQuery().eq(YinglouOrder::getMobile,mobile).eq(YinglouOrder::getPayStatus,SUCCESS_PAY_STATUS).in(YinglouOrder::getStatus,NOT_USE_STATUS,FAIL_OPEN_STATUS).orderByDesc(YinglouOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
         if(yinglouOrder==null){
             return Result.error("订单错误");
         }
         this.lambdaUpdate().eq(YinglouOrder::getOrderId,yinglouOrder.getOrderId()).set(YinglouOrder::getStatus,status).update();
         return Result.ok("订单更新成功");
    }


    /**
     * 微信支付
     * @param tradeType
     * @param channel
     * @param subject
     * @param openId
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, String> pay(String tradeType,String channel,String subject,String openId,String userName) throws Exception {
        YinglouOrder orderPay=this.createPayOrder(tradeType,channel,userName);
        if(orderPay==null){
            throw new Exception("订单错误!");
        }
        String outTradeNo=orderPay.getOrderId();
        payConfig(outTradeNo,tradeType,channel);
        String totalAmount=String.valueOf(orderPay.getTotalAmount());
        Map<String, String> data = new HashMap<>();
        data.put("out_trade_no", outTradeNo);
        data.put("body",  StringUtil.isNotEmpty(subject)?subject:payConfig.getPublicName());
        data.put("total_fee", totalAmount);
        data.put("spbill_create_ip", payConfig.getSpbillCreateIp());
        data.put("notify_url",payConfig.getNotifyUrl());
        data.put("trade_type", tradeType);
        if(BizConstant.TRADE_TYPE_WECHAT.equals(tradeType)){
            if(StringUtils.isEmpty(openId)){
                throw new Exception("请授权获取openId!");
            }
            data.put("openid", openId);
        }
        Map<String, String> resp = wxPay.unifiedOrder(data);
        String resultCode = resp.get("result_code");
        if(!StringUtils.equals(resultCode, WXPayConstants.SUCCESS)){
            log.info("微信支付下单失败:订单号:{},下单结果:{}", outTradeNo,resp);
            String errCodDes=resp.get("err_code");
            if(resp.containsKey("err_code_des")){
                errCodDes=resp.get("err_code_des");
            }
            //更新订单状态
            this.lambdaUpdate().eq(YinglouOrder::getOrderId,outTradeNo).eq(YinglouOrder::getPayStatus,NOT_PAY_STATUS).set(YinglouOrder::getPayStatus,FAIL_PAY_STATUS).set(YinglouOrder::getPayRemark ,errCodDes).set(YinglouOrder::getPayTime,new Date()).update();
            throw new Exception("支付失败,请稍后再试!");
        }
        if(tradeType.equals(BizConstant.TRADE_TYPE_WECHAT)){
            Map<String, String> payJsAPI = Maps.newHashMap();
            payJsAPI.put("appId",payConfig.getAppID());
            payJsAPI.put("timeStamp",String.valueOf(System.currentTimeMillis()));
            payJsAPI.put("nonceStr", WXPayUtil.generateNonceStr());
            payJsAPI.put("package","prepay_id="+resp.get("prepay_id"));
            payJsAPI.put("signType",WXPayConstants.HMACSHA256);
            payJsAPI.put("paySign",WXPayUtil.generateSignature(payJsAPI, payConfig.getKey(),WXPayConstants.SignType.HMACSHA256));
            payJsAPI.put("orderId",outTradeNo);
            return payJsAPI;
        }
        resp.put("orderId",outTradeNo);
        return  resp;
    }

    /**
     * 创建订单记录
     * @param tradeType
     * @param channel
     * @return
     */
    private YinglouOrder createPayOrder(String tradeType,String channel,String userName) {
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatConfig(tradeType,channel);
        if(wechatConfigLog==null){
            log.error("影楼支付渠道未配置-支付方式:{},渠道号:{},登录账号:{}",tradeType,channel,userName);
            return null;
        }
        String orderId= IdWorker.get32UUID();


        String totalAmount= BigDecimal.valueOf(Double.valueOf(wechatConfigLog.getTotalAmount())).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        YinglouOrder pay=new YinglouOrder();
        pay.setOrderId(orderId);
        pay.setPayUserName(userName);
        YinglouUser user=yinglouUserService.lambdaQuery().select(YinglouUser::getDivideIntoScale,YinglouUser::getMajorUser,YinglouUser::getLevels).eq(YinglouUser::getUserName,userName).orderByDesc(YinglouUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(user==null){
            log.error("影楼用户账号已删除-支付方式:{},渠道号:{},登录账号:{}",tradeType,channel,userName);
            return null;
        }
        if(user.getDivideIntoScale()!=null && user.getDivideIntoScale()>0){
            String divideIntoAmount=BigDecimal.valueOf(Double.valueOf(totalAmount)).multiply(BigDecimal.valueOf(user.getDivideIntoScale())).divide(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
            pay.setDivideIntoAmount(Integer.valueOf(divideIntoAmount));
        }
        pay.setMajorUser(user.getMajorUser());
        pay.setTradeType(tradeType);
        pay.setTotalAmount(Integer.valueOf(totalAmount));
        pay.setPayStatus(NOT_PAY_STATUS);
        pay.setLevels(user.getLevels());
        pay.setAppId(wechatConfigLog.getAppId());
        pay.setMchId(wechatConfigLog.getMchId());
        String rightsMonthDay = DateUtil.formatYearMonthDay(LocalDateTime.now());
        pay.setPayOrderTime(rightsMonthDay);
        this.save(pay);
        return pay;
    }

    /**
     * w微信支付更新订单状态
     * @param notifyParam
     */
    @Override
    public void modifyPayStatus(QyclWxpayNotifyParam notifyParam){
        String outTradeNo= notifyParam.getOutTradeNo();
        boolean isNotPay=this.lambdaQuery().eq(YinglouOrder::getOrderId,outTradeNo).eq(YinglouOrder::getStatus,NOT_PAY_STATUS).count()<=0;
        if(isNotPay){
            log.error("微信回调通知重复通知-notifyParam:{}", notifyParam);
            return;
        }
        String transactionId=notifyParam.getTransactionId();
        String resultCode=notifyParam.getResultCode();
        int status = StringUtil.equals(resultCode, WXPayConstants.SUCCESS)?SUCCESS_PAY_STATUS:FAIL_PAY_STATUS;
        this.lambdaUpdate().eq(YinglouOrder::getOrderId, outTradeNo).eq(YinglouOrder::getPayStatus, NOT_PAY_STATUS).set(YinglouOrder::getPayStatus,status).set(YinglouOrder::getOutTradeNo, transactionId).set(YinglouOrder::getPayTime, new Date()).set(YinglouOrder::getUpdateTime, new Date()).update();
    }

    /**
     * 查询订单是否支付成功
     * @param orderId
     * @return
     */
    @Override
    public Result<?> isPay(String orderId,String userName) {
        //查询已支付并且未使用的
        boolean status=this.lambdaQuery().eq(YinglouOrder::getOrderId,orderId).eq(YinglouOrder::getPayUserName,userName).eq(YinglouOrder::getPayStatus,SUCCESS_PAY_STATUS).count()>0;
        if(status){
            return Result.ok();
        }
        return Result.error("请支付！");
    }


    /**
     * 查询是否还有使用次数
     * @return
     */
    @Override
    public Result<?> isSub(String userName) {
        //查询已支付并且未使用的
        boolean status=this.lambdaQuery().eq(YinglouOrder::getPayUserName,userName).eq(YinglouOrder::getPayStatus,SUCCESS_PAY_STATUS).in(YinglouOrder::getStatus,NOT_USE_STATUS,FAIL_OPEN_STATUS).count()>0;
        if(status){
            return Result.ok();
        }
        return Result.error("暂无次数，请支付！");
    }



    /**
     * 查询是否可以发送短信
     * @return
     */
    @Override
    public Result<?> isSms(String userName,String mobile,String channel) {
         RemoteResult remoteResult=miguApiService.sxhGetCircleId(mobile,channel);
         if(remoteResult.isOK()){
             return Result.error(CommonConstant.SC_JEECG_CAPTCHA_ERR,"已开通");
         }
         boolean isSms=this.lambdaQuery().eq(YinglouOrder::getMobile,mobile).eq(YinglouOrder::getPayUserName,userName).eq(YinglouOrder::getPayStatus,SUCCESS_PAY_STATUS).in(YinglouOrder::getStatus,NOT_USE_STATUS,FAIL_OPEN_STATUS).count()>0;
         if(isSms){
            return Result.ok("可以发送短信");
         }
         YinglouOrder yinglouOrder=this.lambdaQuery().eq(YinglouOrder::getPayUserName,userName).eq(YinglouOrder::getPayStatus,SUCCESS_PAY_STATUS).in(YinglouOrder::getStatus,NOT_USE_STATUS,FAIL_OPEN_STATUS).isNull(YinglouOrder::getMobile).orderByDesc(YinglouOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
         if(yinglouOrder==null){
             return Result.error(CommonConstant.SC_JEECG_NO_AUTH,"请支付");
         }
         this.lambdaUpdate().eq(YinglouOrder::getId,yinglouOrder.getId()).eq(YinglouOrder::getPayStatus,SUCCESS_PAY_STATUS).in(YinglouOrder::getStatus,NOT_USE_STATUS,FAIL_OPEN_STATUS).set(YinglouOrder::getMobile,mobile).update();
         return Result.ok("可以发送短信");
    }

    /**
     * 手机号查询是否绑定订单
     * @param mobile
     * @return
     */
    @Override
    public Result bindOrderByMobile(String mobile,String channel,String circleId){
        mobile=yinglouRingService.aesDecryptMobile(mobile,channel);
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            return Result.noauth("请输入正确格式手机号");
        }
        Result result= new Result();
        result.setMobile(mobile);
        result.setChannelCode(channel);
        result.setCode(CommonConstant.SC_OK_200);
        if(StringUtils.isNotBlank(circleId)){
            Result<Object> resultMember=miguApiService.sxhGetMemberList(circleId,null,channel);
            if(!resultMember.isOK()){
                return resultMember;
            }
            return result;
        }
        //查询已支付并且未使用的
        YinglouOrder yinglouOrder=this.lambdaQuery().eq(YinglouOrder::getMobile,mobile).eq(YinglouOrder::getPayStatus,SUCCESS_PAY_STATUS).in(YinglouOrder::getStatus,NOT_USE_STATUS,FAIL_OPEN_STATUS).orderByDesc(YinglouOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(yinglouOrder==null){
            return Result.error("订单错误");
        }
        return result;
    }

    @Override
    public Result<?> queryPayOrderList(String userName,String payTime) {
        ObjectNode objectNode = mapper.createObjectNode();
        YinglouOrderDto order=new YinglouOrderDto();
        order.setPayUserName(userName);
        order.setPayTime(payTime);
        if(StringUtils.isNotBlank(payTime)){
            String[]  month=payTime.split("-");
            // 使用YearMonth类获取指定年月的最后一天
            YearMonth yearMonth = YearMonth.of(Integer.valueOf(month[0]), Integer.valueOf(month[1]));
            order.setPayTimeBegin(payTime+"-01");
            order.setPayTimeEnd(payTime+"-"+yearMonth.atEndOfMonth().getDayOfMonth());
        }

        List<YinglouOrderDto> majorUserList=this.baseMapper.selectOrderListByUser(order);
        objectNode.putPOJO("majorUserList", majorUserList);
        int majorUserCount=this.baseMapper.selectCountByUser(order);
        objectNode.putPOJO("majorUserCount", majorUserCount);

        YinglouUser majorUser=yinglouUserService.lambdaQuery().select(YinglouUser::getNumberId,YinglouUser::getUserAlias).eq(YinglouUser::getUserName,userName).orderByDesc(YinglouUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        objectNode.put("majorUserNumberId", majorUser.getNumberId());
        objectNode.put("majorUserAlias", majorUser.getUserAlias());

        int majorUserOrderTotal=this.baseMapper.selectMajorUserOrderTotalByUser(order);
        objectNode.put("majorUserOrderTotal", majorUserOrderTotal);

        List<YinglouUser> userNameList=yinglouUserService.lambdaQuery().select(YinglouUser::getUserName).eq(YinglouUser::getMajorUser,userName).orderByAsc(YinglouUser::getNumberId).list();
        if(userNameList.isEmpty()){
            return Result.ok(objectNode);
        }
        final List<String> userList=userNameList.stream().map(YinglouUser::getUserName).collect(Collectors.toList());
        ObjectNode userMap = mapper.createObjectNode();
        userList.forEach(user->{
            ObjectNode userMapList = mapper.createObjectNode();
            YinglouOrderDto userOrder=new YinglouOrderDto();
            userOrder.setPayUserName(user);
            userOrder.setPayTime(payTime);
            if(StringUtils.isNotBlank(payTime)){
                userOrder.setPayTimeBegin(order.getPayTimeBegin());
                userOrder.setPayTimeEnd(order.getPayTimeEnd());
            }
            List<YinglouOrderDto> userOrderList=this.baseMapper.selectOrderListByUser(userOrder);
            userMapList.putPOJO("UserList",userOrderList);
            int userOrderCount=this.baseMapper.selectCountByUser(userOrder);
            userMapList.put("UserCount",userOrderCount);
            YinglouUser userNumberId=yinglouUserService.lambdaQuery().select(YinglouUser::getNumberId,YinglouUser::getUserAlias).eq(YinglouUser::getUserName,user).orderByDesc(YinglouUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            userMapList.put("NumberId", userNumberId.getNumberId());
            userMapList.put("UserAlias", userNumberId.getUserAlias());
            userMap.putPOJO(userNumberId.getUserAlias(),userMapList);
        });
        objectNode.putPOJO("userMap", userMap);
        return Result.ok(objectNode);
    }



    @Override
    public Result wechatRefund(String orderId, String refund){
        String refundOrderNo = IdWorker.get32UUID();
        YinglouOrder orderPay=this.lambdaQuery()
                .eq(YinglouOrder::getOrderId, orderId)
                .in(YinglouOrder::getPayStatus, SUCCESS_PAY_STATUS,FAIL_REFUND_STATUS)
                .orderByDesc(YinglouOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        int refunds=Integer.valueOf(refund);
        int totalFee=orderPay.getTotalAmount();
        if(refunds>totalFee){
            return Result.error("退款金额大于支付金额！");
        }
        if(StringUtils.isBlank(orderPay.getAppId()) || StringUtils.isBlank(orderPay.getMchId()) ){
            return Result.error("退款订单暂不支持！");
        }
        if(StringUtils.equalsAny(orderPay.getTradeType(),BizConstant.TRADE_TYPE_HTML,BizConstant.TRADE_TYPE_WECHAT)){
            FebsResponse febsRefund=wxpayService.wechatRefund(orderId,refundOrderNo,String.valueOf(refunds),String.valueOf(totalFee),orderPay.getAppId(),orderPay.getMchId(),"https://crbt.cdyrjygs.com/cms-vrbt/api/yinglou/jsapi/wechat/refund/notify");
            if(febsRefund.isOK()){
                upadateRefundStatus(orderId, refund, refundOrderNo, "正在退款！", REFUND_PREPARE);
                return Result.ok("正在退款！");
            }
        }
        upadateRefundStatus(orderId, refund, refundOrderNo, "退款失败！", FAIL_REFUND_STATUS);
        return Result.error("退款失败！");
    }



    /**
     * 更新退款状态和备注
     * @param outTradeNo
     * @param refundAmount
     * @param refundOrderNo
     * @param refundRemark
     * @param refundStatus
     */
    private void upadateRefundStatus(String outTradeNo, String refundAmount, String refundOrderNo, String refundRemark, Integer refundStatus) {
        this.lambdaUpdate()
                .eq(YinglouOrder::getOrderId, outTradeNo)
                .set(YinglouOrder::getPayStatus, refundStatus)
                .set(YinglouOrder::getRefundOrderNo, refundOrderNo)
                .set(YinglouOrder::getRefundAmount, refundAmount)
                .set(YinglouOrder::getRefundRemark, refundRemark)
                .set(YinglouOrder::getRefundTime, new Date())
                .set(YinglouOrder::getUpdateTime, new Date()).update();
    }


    @Override
    public YinglouOrder queryNotRefundOrder(String refundOrderNo){
        return this.lambdaQuery().eq(YinglouOrder::getRefundOrderNo,refundOrderNo).eq(YinglouOrder::getPayStatus,REFUND_PREPARE).orderByDesc(YinglouOrder::getCreateTime).last("limit 1").one();
    }


    @Override
    public void modifyRefundStatus(String orderId,String outRefundNo, String refundStatus){
        boolean success =StringUtil.equals(refundStatus, WXPayConstants.SUCCESS);
        Integer status=FAIL_REFUND_STATUS;
        if(success){
            status=SUCCESS_REFUND_STATUS;
        }
        this.lambdaUpdate().eq(YinglouOrder::getRefundOrderNo, outRefundNo).eq(YinglouOrder::getPayStatus,REFUND_PREPARE).set(YinglouOrder::getPayStatus,status).set(YinglouOrder::getUpdateTime, new Date()).update();
    }
}
