package com.eleven.cms.service.impl;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.service.ICmsAlarmUserConfigDetailService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * Author: <EMAIL>
 * Date: 2024/5/28 18:48
 * Desc: 告警服务
 */
@Component
@Slf4j
public class AlarmService {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    MessageNotifyService messageNotifyService;
    @Autowired
    IEsDataService esDataService;
    @Autowired
    ISubscribeService subscribeService;

    public static final String ALARM_MONITOR_CRACK_ORDER_RESULT_KEY_PREFIX = "alarm:monitor:crackOrderResult:";
    public static final String ALERT_NOTIFY_CRACK_ORDER_RESULT_KEY_PREFIX = "alert:notify:crackOrderResult:";
    public static final int ALARM_MONITOR_CRACK_ORDER_RESULT_LIMIT_COUNT = 30;
    public static final int ALARM_MONITOR_CRACK_ORDER_RESULT_LIMIT_PERIOD = 3600;
    public static final long ALERT_NOTIFY_CRACK_ORDER_RESULT_INTERVAL = 1800L;

    public static final String ALARM_MONITOR_QYCL_MEMBER_STATUS_KEY_PREFIX = "alarm:monitor:qyclMemberStatus:";
    public static final String ALERT_NOTIFY_QYCL_MEMBER_STATUS_KEY_PREFIX = "alert:notify:qyclMemberStatus:";
    public static final int ALARM_MONITOR_QYCL_MEMBER_STATUS_LIMIT_COUNT = 10;
    public static final int ALARM_MONITOR_QYCL_MEMBER_STATUS_LIMIT_PERIOD = 3600;
    public static final long ALERT_NOTIFY_QYCL_MEMBER_STATUS_INTERVAL = 1800L;

    public static final long ALARM_MONITOR_SUBSRIBE_STATUS_RECENT_HOURS = 4L;
    public static final int ALARM_MONITOR_SUBSRIBE_STATUS_AMOUNT_LIMIT = 50;
    public static final String ALERT_NOTIFY_SUBSRIBE_STATUS_KEY_PREFIX = "alert:notify:subscibeStatus:";
    public static final long ALERT_NOTIFY_SUBSRIBE_STATUS_INTERVAL = 1800L;


    public static final String ALERT_NOTIFY_BUSINESS_ORDER_RESULT_KEY_PREFIX = "alert:notify:businessOrderResult:";
    public static final long ALARM_NOTIFY_BUSINESS_ORDER_LIMIT_INTERVAL = 1800L;

    //开通流程优化连续30条报错	0
    public static final String ALARM_SCENE_CODE_CRACK = "0";
    //企业彩铃回执（BBOSS报错，达到上限
    public static final String ALARM_SCENE_CODE_QYCL = "1";
    //业务开通近期连续50条失败
    public static final String ALARM_SCENE_CODE_SUBSCRIBE = "2";
    //联通业务达到上限
    public static final String ALARM_SCENE_CODE_LIANTONG_BUSINESS = "3";


    /**
     * 监测破解业务开通结果
     * @param resCode
     */
    public void monitorCrackOrderResult(String channelCode, String resCode) {
        if(!StringUtils.equalsAny(resCode,"-8001","000001")){
             return;
        }
        String limitKey = ALARM_MONITOR_CRACK_ORDER_RESULT_KEY_PREFIX + channelCode +":"+ resCode;
        String alertKey = ALERT_NOTIFY_CRACK_ORDER_RESULT_KEY_PREFIX + channelCode +":"+ resCode;
        if(redisUtil.rateLimitV2(limitKey,ALARM_MONITOR_CRACK_ORDER_RESULT_LIMIT_COUNT,ALARM_MONITOR_CRACK_ORDER_RESULT_LIMIT_PERIOD) && redisUtil.isExpired(alertKey,
                ALERT_NOTIFY_CRACK_ORDER_RESULT_INTERVAL)){
            //messageNotifyService.sendAlert("PJ业务开通", "PJ业务开通结果通知告警","近期频繁报错:" + resCode);
            messageNotifyService.sendWarning(channelCode, ALARM_SCENE_CODE_CRACK, "PJ业务开通结果通知告警","近期频繁报错:" + resCode);
            //同时移除限流key
            redisUtil.del(limitKey);
        }
    }

    /**
     * 监测企业彩铃成员状态回执描述
     * @param memberStatusDesc
     */
    public void monitorQyclMemberStatus(String mobile, String memberStatusDesc) {

        String limitMsg;

        //出现匹配直接告警的消息 悠然荐音用户发展数量已达当日上限/麦禾文化用户发展数量已达当日上限/悠然荐音云南用户发展数量已达当日上限/麦禾文化云南用户发展数量已达当日上限
        String numLimit = "数量已达当日上限";

        //send to patry error_【BBOSS】
        String errorBboss = "error_【BBOSS】";
        //部门群组不存在
        String departmentNotExist = "部门群组不存在";

        Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getBizType, BizConstant.BIZ_TYPE_QYCL).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(subscribe==null){
            return;
        }
        String channelCode = subscribe.getChannel();
        //出现匹配直接告警的消息
        //if(StringUtils.containsAny(memberStatusDesc,yrjyNumLimit,mhwhNumLimit,yrjyYunnanNumLimit,mhwhYunnanNumLimit)){
        if(StringUtils.contains(memberStatusDesc,numLimit)){
            limitMsg = memberStatusDesc;
            String alertKey = ALERT_NOTIFY_QYCL_MEMBER_STATUS_KEY_PREFIX + channelCode +":"+ limitMsg;
            if(redisUtil.isExpired(alertKey, ALERT_NOTIFY_QYCL_MEMBER_STATUS_INTERVAL)){
                messageNotifyService.sendWarning(channelCode, ALARM_SCENE_CODE_QYCL,"企业彩铃开通结果回执告警",limitMsg);
            }
            return;
        }

        if(StringUtils.contains(memberStatusDesc,errorBboss)){
            limitMsg = errorBboss;
        }else if(StringUtils.contains(memberStatusDesc,departmentNotExist)){
            limitMsg = departmentNotExist;
        }else {
            return;
        }
        String limitKey = ALARM_MONITOR_QYCL_MEMBER_STATUS_KEY_PREFIX + channelCode +":"+ limitMsg;
        String alertKey = ALERT_NOTIFY_QYCL_MEMBER_STATUS_KEY_PREFIX + channelCode +":"+ limitMsg;
        if(redisUtil.rateLimitV2(limitKey,ALARM_MONITOR_QYCL_MEMBER_STATUS_LIMIT_COUNT,ALARM_MONITOR_QYCL_MEMBER_STATUS_LIMIT_PERIOD) && redisUtil.isExpired(alertKey, ALERT_NOTIFY_QYCL_MEMBER_STATUS_INTERVAL)){
            messageNotifyService.sendWarning(channelCode, ALARM_SCENE_CODE_QYCL,"企业彩铃开通结果回执告警","近期频繁报错:" + limitMsg);
            //同时移除限流key
            redisUtil.del(limitKey);
        }
    }

    /**
     * 监测es最近4小时的订购数据,按渠道号分组取得最近50条数据都没有订购成功的,就预警
     */
    public void monitorSubscribe(){
        long recentHours = 24L;
        int topNum = 50;
        esDataService.subscribeMonitoAllChannel(ALARM_MONITOR_SUBSRIBE_STATUS_RECENT_HOURS, ALARM_MONITOR_SUBSRIBE_STATUS_AMOUNT_LIMIT).forEach(
                (channel, needAlert) -> {
                    //临时排除悠然的企业彩铃开通失败告警
                    //if (channel.equals("QYCL_GR")) {
                    //    return;
                    //}
                    if (needAlert && redisUtil.isExpired(ALERT_NOTIFY_SUBSRIBE_STATUS_KEY_PREFIX + channel, ALERT_NOTIFY_SUBSRIBE_STATUS_INTERVAL)) {
                        messageNotifyService.sendWarning(channel, ALARM_SCENE_CODE_SUBSCRIBE,"业务开通发生告警", "最近连续" + ALARM_MONITOR_SUBSRIBE_STATUS_AMOUNT_LIMIT + "条订购失败");
                    }
                });
    }

    /**
     * 监测联通业务是否达到上限，达到上线就预警
     */
    public void monitorLiantongBusiness(String channelCode, String liantongResultMsg){
        String limitMsg = "此产品今日已售罄";
        //联通达到上限错误码9999
        if (liantongResultMsg==null || !liantongResultMsg.contains(limitMsg)) {
            return;
        }
        String alertKey =  ALERT_NOTIFY_BUSINESS_ORDER_RESULT_KEY_PREFIX + channelCode +":"+ liantongResultMsg;
        if (redisUtil.isExpired(alertKey, ALARM_NOTIFY_BUSINESS_ORDER_LIMIT_INTERVAL)) {
            messageNotifyService.sendWarning(channelCode, ALARM_SCENE_CODE_LIANTONG_BUSINESS,"联通业务开通警告","报错："+liantongResultMsg);
        }
    }

}
