package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.HubeiYidongService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.HubeiMobileBusicheckResult;
import com.eleven.cms.vo.HubeiMobileGetSmsParamResult;
import com.eleven.cms.vo.HubeiMobileSmsCodeResult;
import com.eleven.cms.vo.HubeiMobileVerifySmsResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("hubeiYidongCommonService")
@Slf4j
public class HubeiYidongCommonServiceImpl implements IBizCommonService {

    @Autowired
    private HubeiYidongService hubeiYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    private ObjectMapper mapper;
    @PostConstruct
    public void init() {
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        try{
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            String mobile = subscribe.getMobile();
            HubeiMobileGetSmsParamResult getSmsParamResult = hubeiYidongService.getSmsParam(mobile, subscribe.getChannel(),subscribe.getIp(),subscribe.getUserAgent());
            if (getSmsParamResult.isOK()) {
                subscribe.setResult("验证码参数获取成功");
                subscribeService.createSubscribeDbAndEs(subscribe);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                subscribe.setRemark(mapper.writeValueAsString(getSmsParamResult.getArgs()));
                return Result.ok("验证码参数获取成功", subscribe);
            } else {
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribe.setResult(getSmsParamResult.getRetmsg());
                subscribeService.createSubscribeDbAndEs(subscribe);
                return Result.error("验证码参数获取失败");
            }
        }catch (Exception e){
            return Result.error("验证码参数获取失败");
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe target = subscribeService.getById(subscribe.getId());
        target.setBizTime(new Date());
        //校验验证码
        HubeiMobileVerifySmsResult verifySmsResult = hubeiYidongService.verifySms(mobile,
                subscribe.getChannel(),
                subscribe.getEncryption(),
                smsCode);
        if(!verifySmsResult.isOK()){
            //订阅失败
            target.setStatus(SUBSCRIBE_STATUS_FAIL);
            target.setResult(StringUtils.isBlank(verifySmsResult.getRetmsg()) ? "订阅失败" : verifySmsResult.getRetmsg());
            subscribeService.updateSubscribeDbAndEs(target);
            return Result.error(500, StringUtils.isBlank(verifySmsResult.getRetmsg()) ? "订阅失败" : verifySmsResult.getRetmsg(), "HUBEI");
        }
        //业务校验
        HubeiMobileBusicheckResult busicheckResult = hubeiYidongService.busiCheck(mobile,
                subscribe.getChannel(),
                verifySmsResult.getSessionId(),
                verifySmsResult.getUsessionId());
        if(!busicheckResult.isOK()){
            //订阅失败
            target.setStatus(SUBSCRIBE_STATUS_FAIL);
            target.setResult(StringUtils.isBlank(busicheckResult.getRetmsg()) ? "订阅失败" : busicheckResult.getRetmsg());
            subscribeService.updateSubscribeDbAndEs(target);
            return Result.error(500, StringUtils.isBlank(busicheckResult.getRetmsg()) ? "订阅失败" : busicheckResult.getRetmsg(), "HUBEI");
        }
        //业务办理
        HubeiMobileSmsCodeResult smsCodeResult = hubeiYidongService.smsCode(mobile,
                subscribe.getChannel(),
                busicheckResult.getEordersn());
        if (smsCodeResult.isOK()
                && "99".equals(smsCodeResult.getStatus())) {
            //订阅成功
            target.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            target.setOpenTime(new Date());
            target.setResult("订阅成功");
            subscribeService.updateSubscribeDbAndEs(target);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(target);
            rabbitMQMsgSender.addDelayedVerifyMessage(target);
            return Result.ok("订阅成功", "HUBEI");
        } else {
            //订阅失败
            target.setStatus(SUBSCRIBE_STATUS_FAIL);
            target.setResult(StringUtils.isBlank(smsCodeResult.getRetmsg()) ? "订阅失败" : smsCodeResult.getRetmsg());
            subscribeService.updateSubscribeDbAndEs(target);
            return Result.error(500, StringUtils.isBlank(smsCodeResult.getRetmsg()) ? "订阅失败" : smsCodeResult.getRetmsg(), "HUBEI");
        }
    }
}
