package com.eleven.cms.service.impl;

import com.eleven.cms.entity.ColumnDy;
import com.eleven.cms.entity.ColumnMusicDy;
import com.eleven.cms.mapper.ColumnMusicDyMapper;
import com.eleven.cms.mapper.ColumnDyMapper;
import com.eleven.cms.service.IColumnDyService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 订阅包栏目
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
@Service
public class ColumnDyServiceImpl extends ServiceImpl<ColumnDyMapper, ColumnDy> implements IColumnDyService {

	@Autowired
	private ColumnDyMapper columnDyMapper;
	@Autowired
	private ColumnMusicDyMapper columnMusicDyMapper;
	
	@Override
	@Transactional
	public void saveMain(ColumnDy columnDy, List<ColumnMusicDy> columnMusicDyList) {
		columnDyMapper.insert(columnDy);
		if(columnMusicDyList!=null && columnMusicDyList.size()>0) {
			for(ColumnMusicDy entity:columnMusicDyList) {
				//外键设置
				entity.setColumnId(columnDy.getId());
				columnMusicDyMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void updateMain(ColumnDy columnDy,List<ColumnMusicDy> columnMusicDyList) {
		columnDyMapper.updateById(columnDy);
		
		//1.先删除子表数据
		columnMusicDyMapper.deleteByMainId(columnDy.getId());
		
		//2.子表数据重新插入
		if(columnMusicDyList!=null && columnMusicDyList.size()>0) {
			for(ColumnMusicDy entity:columnMusicDyList) {
				//外键设置
				entity.setColumnId(columnDy.getId());
				columnMusicDyMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void delMain(String id) {
		columnMusicDyMapper.deleteByMainId(id);
		columnDyMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			columnMusicDyMapper.deleteByMainId(id.toString());
			columnDyMapper.deleteById(id);
		}
	}
	
}
