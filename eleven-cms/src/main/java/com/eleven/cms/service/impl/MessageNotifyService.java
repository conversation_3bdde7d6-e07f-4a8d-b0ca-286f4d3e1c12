package com.eleven.cms.service.impl;

import com.eleven.cms.config.SmsNotifyProperties;
import com.eleven.cms.config.WxMpProperties;
import com.eleven.cms.service.ICmsAlarmUserConfigDetailService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.util.RawValue;
import com.google.common.base.Strings;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 消息服务入口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageNotifyService {

    public static final String VERGE_LIMIT_CONTENT = "即将限量,限%s,当前%s";
    public static final String LIMIT_CONTENT = "已达限量,限%s";
    public static final String ALARM_ENABLE = "1";

    @Autowired
    SmsNotifyService smsNotifyService;

    @Autowired
    WxMpNotifyService wxMpNotifyService;
    
    @Autowired
    SmsNotifyProperties smsNotifyProperties;
    
    @Autowired
    ICmsAlarmUserConfigDetailService cmsAlarmUserConfigDetailService;




    /**
     * 发送限量通知
     *
     * @param mobile      手机号
     * @param channelCode 示例：HY_25_CW
     * @param province    示例：四川
     * @param limitDesc   示例： 已达限量,限250；即将限量,限250,当前230
     * @return
     */
    public void sendLimitNotify(String mobile, String channelCode, String province, String limitDesc) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            log.error("发送限量通知手机号格式错误:{}", mobile);
            return;
        }
        //渠道号:HY_25_CW,省份:吉林已达限量,限250;渠道号:HY_25_CW,省份:吉林即将限量,限250,当前230
        String noSignContent = String.format("渠道号:%s,省份:%s,%s", channelCode, province, limitDesc);
        smsNotifyService.sendNotify(mobile, noSignContent);
        wxMpNotifyService.sendLimitMessage(mobile, channelCode, province, limitDesc);
    }

    /**
     * 发送权益告警通知
     *
     * @param mobile      手机号
     * @param rightsId 示例：HY_25_CW
     * @param rightsType    示例：四川
     * @param rightsDesc   示例： 已达限量,限250；即将限量,限250,当前230
     * @return
     */
    public void sendRightsNotify(String mobile, String rightsId, String rightsType, String rightsDesc) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            log.error("发送限量通知手机号格式错误:{}", mobile);
            return;
        }
        String noSignContent = String.format("权益Id:%s,%s,%s", rightsId, rightsType, rightsDesc);
        smsNotifyService.sendNotify(mobile, noSignContent);
        wxMpNotifyService.sendRightsMessage(mobile, rightsId, rightsType, rightsDesc);
    }

    /**
     * 发送告警通知
     *
     * @param mobile      手机号
     * @param channelCode 示例：HY_25_CW
     * @param alertTypeDesc    示例：四川
     * @param alertMessage   示例： 已达限量,限250；即将限量,限250,当前230
     * @return
     */
    public void sendAlertNotify(String mobile, String channelCode, String alertTypeDesc, String alertMessage) {
        sendSmsWarning(mobile, channelCode, alertTypeDesc, alertMessage);
        wxMpNotifyService.sendAlertMessage(mobile, channelCode, alertTypeDesc, alertMessage);
    }

    /**
     * 发送告警通知
     *
     * @param mobile      手机号
     * @param channelCode 示例：HY_25_CW
     * @param alertTypeDesc    示例：四川
     * @param alertMessage   示例： 已达限量,限250；即将限量,限250,当前230
     * @return
     */
    public void sendSmsWarning(String mobile, String channelCode, String alertTypeDesc, String alertMessage) {
        if (Strings.isNullOrEmpty(mobile) || !mobile.matches(BizConstant.MOBILE_REG)) {
            log.error("发送通知手机号格式错误:{}", mobile);
            return;
        }
        String noSignContent = String.format("渠道号:%s,%s,%s", channelCode, alertTypeDesc, alertMessage);
        smsNotifyService.sendNotify(mobile, noSignContent);
    }

    public void sendTotalNotify(String mobile, String channel, Integer limitAmount) {
        this.sendLimitNotify(mobile, channel, "全域", String.format(LIMIT_CONTENT, limitAmount));
    }

    public void sendTotalVergeNotify(String mobile, String channel, Integer limitAmount, Integer currentCount) {
        this.sendLimitNotify(mobile, channel, "全域", String.format(VERGE_LIMIT_CONTENT, limitAmount, currentCount));
    }


    public void sendProvinceNotify(String mobile, String channel, String province, Integer limitAmount) {
        this.sendLimitNotify(mobile, channel, province, String.format(LIMIT_CONTENT, limitAmount));
    }

    public void sendProvinceVergeNotify(String mobile, String channel, String province, Integer limitAmount, Integer currentCount) {
        this.sendLimitNotify(mobile, channel, province, String.format(VERGE_LIMIT_CONTENT, limitAmount, currentCount));
    }


    public void sendAlert(String channel, String alertTypeDesc, String alertMessage) {
        smsNotifyProperties.getAlertNotifyMobileList().forEach(mobile -> {
            //这些号码只发送微信消息
            if (smsNotifyProperties.getOnlyWxMsgMobileList().contains(mobile)) {
                this.wxMpNotifyService.sendAlertMessage(mobile, channel, alertTypeDesc, alertMessage);
            //同时发送短信和微信消息
            }else {
                this.sendAlertNotify(mobile, channel, alertTypeDesc, alertMessage);
            }
        });
    }

    public void sendRightsAlert(String rightsId, String rightsType, String rightsDesc) {
        smsNotifyProperties.getAlertNotifyMobileList().forEach(mobile -> {
            this.sendRightsNotify(mobile, rightsId, rightsType, rightsDesc);
        });
    }

    public void sendWarning(String channelCode, String alarmSceneCode, String alertTypeDesc, String alertMessage) {
        cmsAlarmUserConfigDetailService.getAlarmUserList(channelCode,alarmSceneCode).forEach(alarmUser -> {
            if(ALARM_ENABLE.equals(alarmUser.getEnableSms())){
                this.sendSmsWarning(alarmUser.getPhone(), channelCode, alertTypeDesc, alertMessage);
            }
            if (ALARM_ENABLE.equals(alarmUser.getEnableVx())){
                this.wxMpNotifyService.sendAlertMessage(alarmUser.getPhone(), channelCode, alertTypeDesc, alertMessage);
            }
        });
    }
}
