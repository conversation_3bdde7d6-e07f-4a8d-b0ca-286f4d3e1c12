package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.MusicHotLevel;
import com.eleven.cms.mapper.MusicHotLevelMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IMusicHotLevelService;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MusicVo;
import com.eleven.cms.vo.RemoteResult;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @Description: 人气榜单歌曲
 * @Author: jeecg-boot
 * @Date:   2023-11-21
 * @Version: V1.0
 */
@Service
public class MusicHotLevelServiceImpl extends ServiceImpl<MusicHotLevelMapper, MusicHotLevel> implements IMusicHotLevelService {
    @Autowired
    private MiguApiService miguApiService;
    @Override
    public FebsResponse setVrbtMusic(String mobile, String channelCode, String copyrightId) {
        MusicHotLevel musicHotLevel=new MusicHotLevel();
        musicHotLevel.setCopyrightId(copyrightId);
        musicHotLevel.setMobile(mobile);
        this.baseMapper.insert(musicHotLevel);
        RemoteResult result= miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_BEIJIAO);
        if(result.isOK()){
            return new FebsResponse().success(result.getResMsg());
        }
        return new FebsResponse().code(result.getResCode()).message(result.getResMsg());
    }

    @Cacheable(cacheNames = CacheConstant.CMS_VRBT_DYXCX_MUSIC_HOT_LEVEL_CACHE,key = "#page.getCurrent()+'_'+#page.getSize()",unless = "#result==null")
    @Override
    public IPage<MusicVo> queryMusicHotLevel(Page<MusicVo> page) {
        IPage<MusicVo> pageList= this.baseMapper.selectMusicHotLevelPage(page);
        return pageList;
    }
}
