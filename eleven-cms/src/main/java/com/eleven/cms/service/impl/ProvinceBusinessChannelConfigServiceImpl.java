package com.eleven.cms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.entity.ProvinceBusinessChannelConfig;
import com.eleven.cms.entity.ProvinceBusinessChannelInfo;
import com.eleven.cms.mapper.ProvinceBusinessChannelInfoMapper;
import com.eleven.cms.mapper.ProvinceBusinessChannelConfigMapper;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.IProvinceTreeService;
import com.eleven.cms.util.BizConstant;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Collection;

/**
 * @Description: cms_province_business_channel_config
 * @Author: jeecg-boot
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Service
public class ProvinceBusinessChannelConfigServiceImpl extends ServiceImpl<ProvinceBusinessChannelConfigMapper, ProvinceBusinessChannelConfig> implements IProvinceBusinessChannelConfigService {

	@Autowired
	private ProvinceBusinessChannelConfigMapper provinceBusinessChannelConfigMapper;
	@Autowired
	private ProvinceBusinessChannelInfoMapper provinceBusinessChannelInfoMapper;
    @Autowired
    IProvinceTreeService provinceTreeService;
	
	@Override
	@Transactional
	public void saveMain(ProvinceBusinessChannelConfig provinceBusinessChannelConfig, List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList) {
		provinceBusinessChannelConfigMapper.insert(provinceBusinessChannelConfig);
        List<ProvinceBusinessChannelInfo> list = new ArrayList<>();
        provinceTreeService.list().forEach(province -> {
            ProvinceBusinessChannelInfo provinceBusinessChannelInfo = new ProvinceBusinessChannelInfo();
            provinceBusinessChannelInfo.setEnable(Boolean.FALSE);
            provinceBusinessChannelInfo.setProvinceChannelConfigId(provinceBusinessChannelConfig.getId());
            provinceBusinessChannelInfo.setProvince(province.getProvinceName());
            provinceBusinessChannelInfo.setLimitAmount(0);
            provinceBusinessChannelInfoMapper.insert(provinceBusinessChannelInfo);
        });
	}

	@Override
	@Transactional
	public void updateMain(ProvinceBusinessChannelConfig provinceBusinessChannelConfig,List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList) {
		provinceBusinessChannelConfigMapper.updateById(provinceBusinessChannelConfig);
		
		//1.先删除子表数据
		provinceBusinessChannelInfoMapper.deleteByMainId(provinceBusinessChannelConfig.getId());
		
		//2.子表数据重新插入
		if(provinceBusinessChannelInfoList!=null && provinceBusinessChannelInfoList.size()>0) {
			for(ProvinceBusinessChannelInfo entity:provinceBusinessChannelInfoList) {
				//外键设置
				entity.setProvinceChannelConfigId(provinceBusinessChannelConfig.getId());
				provinceBusinessChannelInfoMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void delMain(String id) {
		provinceBusinessChannelInfoMapper.deleteByMainId(id);
		provinceBusinessChannelConfigMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			provinceBusinessChannelInfoMapper.deleteByMainId(id.toString());
			provinceBusinessChannelConfigMapper.deleteById(id);
		}
	}

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_PROVINCE_CHANNEL_ALLOW, key =  "#root.methodName + ':' + #p0 + ':' + #p1", condition = "#p0!=null", unless = "#result==null")
    public boolean allow(String channelCode, String province) {
        if (StringUtils.isEmpty(province)) {
            return true;
        }
//        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = this.lambdaQuery().eq(ProvinceBusinessChannelConfig::getChannel, channelCode).one();
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
        if (provinceBusinessChannelConfig == null) {
            return true;
        } else {
            return provinceBusinessChannelInfoMapper.selectByMainId(provinceBusinessChannelConfig.getId()).stream().anyMatch(provinceBusinessChannelChannel -> province.equals(provinceBusinessChannelChannel.getProvince()) && provinceBusinessChannelChannel.isEnable());
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_PROVINCE_CHANNEL_LIMIT, key =  "#root.methodName + ':' + #p0 + ':' + #p1", condition = "#p0!=null", unless = "#result==null")
    public Integer getLimitByChannelAndProvince(String channelCode, String province) {
        if (StringUtils.isEmpty(province)) {
            return 0;
        }
//        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = this.lambdaQuery().eq(ProvinceBusinessChannelConfig::getChannel, channelCode).one();
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
        if (provinceBusinessChannelConfig == null) {
            return 0;
        } else {
            ProvinceBusinessChannelInfo provinceBusinessChannelInfo = provinceBusinessChannelInfoMapper.selectByMainId(provinceBusinessChannelConfig.getId()).stream().filter(provinceBusinessChannelChannel -> province.equals(provinceBusinessChannelChannel.getProvince()) && provinceBusinessChannelChannel.isEnable()).findAny().orElse(null);
            if (provinceBusinessChannelInfo == null) {
                return 0;
            }
            return provinceBusinessChannelInfo.getLimitAmount() == null ? 0 : provinceBusinessChannelInfo.getLimitAmount();
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_PROVINCE_CHANNEL_LIMIT_MOBILE, key = "#root.methodName + ':' + #p0 + ':' + #p1", condition = "#p0!=null", unless = "#result==null")
    public List<String> getLimitSendMobile(String channelCode, String province) {
        List<String> mobiles = new ArrayList();
        if (StringUtils.isEmpty(province)) {
            return mobiles;
        }
//        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = this.lambdaQuery().eq(ProvinceBusinessChannelConfig::getChannel, channelCode).one();
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
        if (provinceBusinessChannelConfig == null) {
            return mobiles;
        } else {
            ProvinceBusinessChannelInfo provinceBusinessChannelInfo = provinceBusinessChannelInfoMapper.selectByMainId(provinceBusinessChannelConfig.getId()).stream().filter(provinceBusinessChannelChannel -> province.equals(provinceBusinessChannelChannel.getProvince()) && provinceBusinessChannelChannel.isEnable()).findAny().orElse(null);
            if (provinceBusinessChannelInfo == null || StringUtils.isBlank(provinceBusinessChannelInfo.getReceiveMobile())) {
                return mobiles;
            }
            return Arrays.asList(provinceBusinessChannelInfo.getReceiveMobile().split(","));
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_PROVINCE_CHANNEL_OWNER_LIMIT, key =  "#root.methodName + ':' + #p0 + ':' + #p1 + ':' + #p2", condition = "#p0!=null", unless = "#result==null")
    public Integer getLimitByChannelAndOwner(String channelCode, String province, String owner) {
        if (StringUtils.isEmpty(owner) || StringUtils.isEmpty(province)) {
            return null;
        }
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
//        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = this.lambdaQuery().eq(ProvinceBusinessChannelConfig::getChannel, channelCode).one();
        if (provinceBusinessChannelConfig == null) {
            return null;
        } else {
            ProvinceBusinessChannelInfo provinceBusinessChannelInfo = provinceBusinessChannelInfoMapper.selectByMainId(provinceBusinessChannelConfig.getId()).stream().filter(provinceBusinessChannelChannel -> province.equals(provinceBusinessChannelChannel.getProvince()) && provinceBusinessChannelChannel.isEnable()).findAny().orElse(null);
            if (provinceBusinessChannelInfo == null) {
                return null;
            }
            if (BizConstant.CHANNEL_OWNER_CPA.equals(owner)) {
                return provinceBusinessChannelInfo.getCpaLimit();
            } else if (BizConstant.CHANNEL_OWNER_XIANGHONG.equals(owner)) {
                return provinceBusinessChannelInfo.getXianghongLimit();
            } else if (BizConstant.CHANNEL_OWNER_DONGSW.equals(owner)){
                return provinceBusinessChannelInfo.getDongswLimit();
            } else {
                return provinceBusinessChannelInfo.getXiaofengLimit();
            }
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CHANNEL_LIMIT_CONFIG, key = "#root.methodName + ':' + #p0", condition = "#p0!=null")
    public ProvinceBusinessChannelConfig getByChannel(String channelCode) {
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = this.lambdaQuery().eq(ProvinceBusinessChannelConfig::getChannel, channelCode).one();
        if (provinceBusinessChannelConfig == null) {
            return null;
        }
        return provinceBusinessChannelConfig;
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_PROVINCE_CHANNEL_LIMIT_CPA_MOBILE, key = "#root.methodName + ':' + #p0 + ':' + #p1", condition = "#p0!=null", unless = "#result==null")
    public List<String> getLimitSendCpaMobile(String channelCode, String province) {
        List<String> mobiles = new ArrayList();
        if (StringUtils.isEmpty(province)) {
            return mobiles;
        }
//        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = this.lambdaQuery().eq(ProvinceBusinessChannelConfig::getChannel, channelCode).one();
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
        if (provinceBusinessChannelConfig == null) {
            return mobiles;
        } else {
            ProvinceBusinessChannelInfo provinceBusinessChannelInfo = provinceBusinessChannelInfoMapper.selectByMainId(provinceBusinessChannelConfig.getId()).stream().filter(provinceBusinessChannelChannel -> province.equals(provinceBusinessChannelChannel.getProvince()) && provinceBusinessChannelChannel.isEnable()).findAny().orElse(null);
            if (provinceBusinessChannelInfo == null || StringUtils.isBlank(provinceBusinessChannelInfo.getCpaReceiveMobile())) {
                return mobiles;
            }
            return Arrays.asList(provinceBusinessChannelInfo.getCpaReceiveMobile().split(","));
        }
    }

    @Override
    public Integer getTotalLimitByChannelAndOwner(String channelCode, String owner) {
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
        if (provinceBusinessChannelConfig == null) {
            return null;
        }
        if (BizConstant.CHANNEL_OWNER_CPA.equals(owner)) {
            return provinceBusinessChannelConfig.getCpaTotalLimit();
        } else if (BizConstant.CHANNEL_OWNER_XIANGHONG.equals(owner)) {
            return provinceBusinessChannelConfig.getXianghongTotalLimit();
        } else if (BizConstant.CHANNEL_OWNER_DONGSW.equals(owner)){
            return provinceBusinessChannelConfig.getDongswTotalLimit();
        } else {
            return provinceBusinessChannelConfig.getXiaofengTotalLimit();
        }
    }

    @Override
    public List<String> getTotalLimitSendMobile(String channelCode) {
        List<String> mobiles = new ArrayList();
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
        if (provinceBusinessChannelConfig == null) {
            return mobiles;
        } else {
            return Arrays.asList(provinceBusinessChannelConfig.getReceiveMobile().split(","));
        }
    }

    @Override
    public List<String> getTotalCpaLimitSendMobile(String channelCode) {
        List<String> mobiles = new ArrayList();
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = SpringUtil.getBean(IProvinceBusinessChannelConfigService.class).getByChannel(channelCode);
        if (provinceBusinessChannelConfig == null) {
            return mobiles;
        } else {
            return Arrays.asList(provinceBusinessChannelConfig.getCpaReceiveMobile().split(","));
        }
    }
}
