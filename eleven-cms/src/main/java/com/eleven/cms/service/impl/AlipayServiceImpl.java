package com.eleven.cms.service.impl;

import com.alipay.api.*;
import com.alipay.api.domain.AlipayFundAccountQueryModel;
import com.alipay.api.domain.AlipayFundTransCommonQueryModel;
import com.alipay.api.domain.AlipayFundTransUniTransferModel;
import com.alipay.api.domain.Participant;
import com.alipay.api.internal.util.AlipayEncrypt;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.eleven.cms.config.AliPayConfig;
import com.eleven.cms.config.AliPayRefundProperties;
import com.eleven.cms.config.AliSignUpConfig;
import com.eleven.cms.dto.AlipayNotifyParam;
import com.eleven.cms.entity.*;
import com.eleven.cms.remote.LiantongVrbtService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/7/14 16:02
 * Desc: 支付宝支付
 */
@Slf4j
@Service
public class AlipayServiceImpl implements IAlipayService {
    public static final Integer ALIPAY_ACCESS_TOKEN_CATCHE_TIME = 3000;
    public static final String ENCRYPT_TYPE = "AES";

    @Autowired
    AliPayConfig aliPayConfig;
    @Autowired
    AlipayClient alipayClient;
    @Autowired
    AliSignUpConfig aliSignUpConfig;
    @Autowired
    AlipayClient aliSignUpClient;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private MobileRegionService mobileRegionService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private AliPayRefundProperties aliPayRefundProperties;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    private IWarnMobileService warnMobileService;
    @Autowired
    private IWoReadOrderService woReadOrderService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    LiantongVrbtService liantongVrbtService;
    @Autowired
    private IMiguVrbtPayOrderService miguVrbtPayOrderService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
            false);
    private Alipay init(String businessType) {
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
       if(alipay==null){
           throw new JeecgBootException("支付参数错误");
       }
        this.aliSignUpClient = new DefaultAlipayClient(aliSignUpConfig.getAlipayGatewayUrl(),
                alipay.getAppId(),
                alipay.getPrivateKey(),
                aliSignUpConfig.getFormat(),
                aliSignUpConfig.getCharset(),
                alipay.getPublicKey(),
                aliSignUpConfig.getSignType());
        return alipay;
    }
    private Alipay initComplain(String appId) {
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getAppId,appId).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if(alipay==null){
            throw new JeecgBootException("支付参数错误");
        }
        this.aliSignUpClient = new DefaultAlipayClient(aliSignUpConfig.getAlipayGatewayUrl(),
                alipay.getAppId(),
                alipay.getPrivateKey(),
                aliSignUpConfig.getFormat(),
                aliSignUpConfig.getCharset(),
                alipay.getPublicKey(),
                aliSignUpConfig.getSignType());
        return alipay;
    }
    private Alipay initRefund(String businessType) {
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if(alipay==null){
            throw new JeecgBootException("支付参数错误");
        }
        this.aliSignUpClient = new DefaultAlipayClient(aliSignUpConfig.getAlipayGatewayUrl(),
                alipay.getAppId(),
                alipay.getPrivateKey(),
                aliSignUpConfig.getFormat(),
                aliSignUpConfig.getCharset(),
                alipay.getPublicKey(),
                aliSignUpConfig.getSignType());
        return alipay;
    }
    private Alipay transferFee(String businessType) {
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if(alipay==null){
            throw new JeecgBootException("支付参数错误");
        }
        CertAlipayRequest alipayConfig = new CertAlipayRequest();
        alipayConfig.setPrivateKey(alipay.getPrivateKey());
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId(alipay.getAppId());
        alipayConfig.setCharset("UTF8");
        alipayConfig.setSignType("RSA2");
        alipayConfig.setEncryptor("");
        alipayConfig.setFormat("json");
        String certPath =aliPayRefundProperties.getCertPath().replace("appId",alipay.getAppId());
        alipayConfig.setCertPath(certPath);
        String alipayPublicCertPath =aliPayRefundProperties.getAlipayPublicCertPath().replace("appId",alipay.getAppId());
        alipayConfig.setAlipayPublicCertPath(alipayPublicCertPath);
        String rootCertPath =aliPayRefundProperties.getRootCertPath().replace("appId",alipay.getAppId());
        alipayConfig.setRootCertPath(rootCertPath);
        try {
            this.aliSignUpClient = new DefaultAlipayClient(alipayConfig);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return alipay;
    }


    /**
     * 开始支付,生成支付表单(手机支付)
     *
     * @param httpResponse
     * @throws IOException
     */
    @Override
    public void wapPay(String outTradeNo, String totalAmount, String subject, String body,
                       HttpServletResponse httpResponse) throws IOException {

        log.info("支付宝交易订单id为:{}", outTradeNo);

        //手机网站支付2.0 使用api生成bizModel
        //AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();
        //// 封装请求支付信息
        //AlipayTradeWapPayModel model=new AlipayTradeWapPayModel();
        //model.setOutTradeNo(outTradeNo); // 商户订单号，商户网站订单系统中唯一订单号，必填
        //model.setSubject("互助平台充值");  // 订单名称，必填
        //model.setTotalAmount("0.01");  // 付款金额，必填
        ////model.setBody("WIDbody"); // 商品描述，可空
        ////model.setTimeoutExpress("2m"); // 超时时间 可空
        //model.setProductCode("QUICK_WAP_PAY"); // 销售产品码 必填
        //model.setPassbackParams("passbackParams"); // 公共回传参数，如果请求时传递了该参数，则返回给商户时会在异步通知时将该参数原样返回。
        //alipayRequest.setBizModel(model);

        //手机网站支付2.0 自己生成bizContent
        AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode bizContentNode = mapper.createObjectNode();
        bizContentNode.put("out_trade_no", outTradeNo);  // 商户订单号，商户网站订单系统中唯一订单号，必填
        bizContentNode.put("total_amount", totalAmount);   // 付款金额，必填
        bizContentNode.put("subject", subject);  // 订单名称，必填
        bizContentNode.put("body", body);  // 订单描述
        bizContentNode.put("product_code", "QUICK_WAP_PAY");
        bizContentNode.put("app_pay", "Y");
        String bizContent = bizContentNode.toString();
        alipayRequest.setBizContent(bizContent);
        alipayRequest.setReturnUrl(aliPayConfig.getReturnUrl()); //设置通知地址
        alipayRequest.setNotifyUrl(aliPayConfig.getNotifyUrl());//设置回跳

        pay(alipayRequest, httpResponse);
    }

    /**
     * 开始支付,生成支付表单(网站支付)
     *
     * @param httpResponse
     * @throws IOException
     */
    @Override
    public void pagePay(String outTradeNo, String totalAmount, String subject, String body,
                        HttpServletResponse httpResponse) throws IOException {

        log.info("支付交易订单id为:{}", outTradeNo);

        //统一收单下单并支付页面接口
        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();

        ObjectMapper mapper = new ObjectMapper();
        ObjectNode bizContentNode = mapper.createObjectNode();
        bizContentNode.put("out_trade_no", outTradeNo);
        bizContentNode.put("total_amount", totalAmount);
        bizContentNode.put("subject", subject);
        bizContentNode.put("body", body);  // 订单描述
        bizContentNode.put("product_code", "FAST_INSTANT_TRADE_PAY");
        //bizContentNode.put("app_pay", "Y");
        String bizContent = bizContentNode.toString();

        alipayRequest.setBizContent(bizContent);//填充业务参数
        alipayRequest.setReturnUrl(aliPayConfig.getReturnUrl()); //设置通知地址
        alipayRequest.setNotifyUrl(aliPayConfig.getNotifyUrl());//设置回跳

        pay(alipayRequest, httpResponse);
    }


    /**
     * 开始支付,生成支付表单
     *
     * @param alipayRequest
     * @param httpResponse
     * @throws IOException
     */
    @Override
    public void pay(AlipayRequest alipayRequest, HttpServletResponse httpResponse) throws IOException {

        String form = "";
        try {
            form = alipayClient.pageExecute(alipayRequest)
                               .getBody(); //调用SDK生成表单
            //System.out.println(form);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        httpResponse.setContentType("text/html;charset=" + aliPayConfig.getCharset());
        httpResponse.getWriter()
                    .write(form);//直接将完整的表单html输出到页面
        httpResponse.getWriter()
                    .flush();
        httpResponse.getWriter()
                    .close();
    }


    /**
     * 支付宝付款回调（平台-->用户）
     * 第一步:验证签名,签名通过后进行第二步
     * 第二步:按一下步骤进行验证
     * 1、商户需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号，
     * 2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额），
     * 3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email），
     * 4、验证app_id是否为该商户本身。上述1、2、3、4有任何一个验证不通过，则表明本次通知是异常通知，务必忽略。
     * 在上述验证通过后商户必须根据支付宝不同类型的业务通知，正确的进行不同的业务处理，并且过滤重复的通知结果数据。
     * 在支付宝的业务通知中，只有交易通知状态为TRADE_SUCCESS或TRADE_FINISHED时，支付宝才会认定为买家付款成功。
     *
     * @param requestParams
     * @return 如果签名验证正确，立即返回success，后续业务另起线程单独处理
     * 业务处理失败，可查看日志进行补偿，跟支付宝已经没多大关系。failure
     */
    @Override
    public AlipayNotifyParam payResultNotify(Map<String, String> requestParams) throws AlipayApiException {
        log.info("支付宝支付交易结果通知Json，{}", requestParams);

        // 调用SDK验证签名
        //boolean signVerified = AlipaySignature.rsaCheckV1(requestParams, aliPayConfig.getAlipayRsa2PublicKey(),
        //        aliPayConfig.getCharset(), aliPayConfig.getSignType());
        //if (signVerified) {
        //    log.info("支付宝支付交易结果通知签名认证失败，signVerified=false");
        //    throw new AlipayApiException("支付宝支付交易结果通知签名认证失败");
        //}

        AlipayNotifyParam notifyParam = mapper.convertValue(requestParams, AlipayNotifyParam.class);
//        if (!aliPayConfig.getAppId()
//                .equals(notifyParam.getAppId())) {
//            throw new AlipayApiException("app_id不一致");
//        }
        // 按照支付结果异步通知中的描述，对支付结果中的业务内容进行1\2\3\4二次校验，校验成功后在response中返回success，校验失败返回failure
        // 4、验证app_id是否为该商户本身。
        //String trade_status = notifyParam.getTradeStatus();
        // 支付成功
        //if (trade_status.equals(AliPayConfig.AlipayTradeStatus.TRADE_SUCCESS) || trade_status.equals(
        //        AliPayConfig.AlipayTradeStatus.TRADE_FINISHED)) {
        //}
        //需要做状态判断 防止重复收到回调！！！
        log.info("支付宝支付订单ID={}已收到支付回调，付款成功", notifyParam.getOutTradeNo());

        return notifyParam;

    }

    @Override
    public FebsResponse aliSignUp(String mobile,
                                  String externalAgreementNo,
                                  String signValidityPeriod,
                                  String periodType,
                                  String period,
                                  String executeTime,
                                  String singleAmount,
                                  String totalAmount,
                                  String totalPayments, String businessType, Date nextDeductTime,String bizType) throws Exception {

        FebsResponse febsResponse = new FebsResponse();
        //匹配支付配置文件
        this.init(businessType);

        ObjectNode signUpContentNode = mapper.createObjectNode();
        ObjectNode accessParamsNode = mapper.createObjectNode();
        ObjectNode periodRuleParamsNode = mapper.createObjectNode();
        AlipayUserAgreementPageSignRequest request = new AlipayUserAgreementPageSignRequest();
        request.setNotifyUrl(aliSignUpConfig.getNotifyUrl());//设置异步通知地址
        if(StringUtils.equals(BizConstant.BIZ_TYPE_MEMBER_ALIPAY,bizType)){
            request.setReturnUrl(aliSignUpConfig.getReturnUrl()+"?serviceId="+businessType+"&mobile="+mobile+"&bizType="+bizType);//签约完成跳转地址
        }else{
            request.setReturnUrl(aliSignUpConfig.getVrbtReturnUrl()+"?serviceId="+businessType+"&mobile="+mobile+"&bizType="+bizType);//签约完成跳转地址
        }
        String form = "";
        accessParamsNode.put("channel","ALIPAYAPP");//接入渠道
        periodRuleParamsNode.put("period_type",periodType);//周期类型枚举值为DAY和MONTH
        periodRuleParamsNode.put("period",period);//周期数
        if(StringUtils.equals("MONTH",periodType)){
            if(LocalDate.now().getDayOfMonth()>=29){
                periodRuleParamsNode.put("execute_time",LocalDate.now().plusMonths(1).withDayOfMonth(1).format(DateTimeFormatter.ISO_LOCAL_DATE));//商户发起首次扣款的时间
            }else{
                periodRuleParamsNode.put("execute_time",executeTime);//商户发起首次扣款的时间
            }
//            executeTime= DateUtil.getDateFormat(nextDeductTime,DateUtil.YEAR_MONTH_DAY);
//            periodRuleParamsNode.put("execute_time",executeTime);//商户发起首次扣款的时间
        }else{
            periodRuleParamsNode.put("execute_time",executeTime);//商户发起首次扣款的时间
        }
        periodRuleParamsNode.put("single_amount",singleAmount);//每次发起扣款时限制的最大金额单位为元
        if(StringUtils.isNotBlank(totalAmount)){
            periodRuleParamsNode.put("total_amount",totalAmount);//周期内允许扣款的总金额，单位为元。如果传入此参数，商家多次扣款的累计金额不允许超过此金额。
        }
        if(StringUtils.isNotBlank(totalPayments)){
            periodRuleParamsNode.put("total_payments",totalPayments);//总扣款次数,如果传入此参数，则商家成功扣款的次数不能超过此次数限制（扣款失败不计入）。
        }

        signUpContentNode.put("external_logon_id", mobile);//用户账号
        signUpContentNode.put("product_code", "CYCLE_PAY_AUTH");//周期扣款场景固定为 CYCLE_PAY_AUTH
        signUpContentNode.put("personal_product_code", "CYCLE_PAY_AUTH_P");//周期扣款产品时必传签约其它产品时无需传入
        signUpContentNode.put("sign_scene", "INDUSTRY|APPSTORE");//签约场景
        signUpContentNode.put("external_agreement_no", externalAgreementNo);//商户签约号
        if(StringUtils.isNotBlank(signValidityPeriod)){
            signUpContentNode.put("sign_validity_period", signValidityPeriod);//当前用户签约请求的协议有效周期 d天m月 如果未传入，默认为长期有效。
        }
        signUpContentNode.putPOJO("access_params", accessParamsNode);
        signUpContentNode.putPOJO("period_rule_params", periodRuleParamsNode);
        request.setBizContent(signUpContentNode.toString());
        try {
            AlipayUserAgreementPageSignResponse response = aliSignUpClient.pageExecute(request, "get");
            //小程序签约支付
            //AlipayUserAgreementPageSignResponse response = alipayClient.sdkExecute(request);
            if(response.isSuccess()){
                log.info("支付宝周期扣款-发起签约成功,手机号:{},externalAgreementNo:{},响应:{}",mobile,externalAgreementNo,mapper.writeValueAsString(response));
                form = response.getBody();
                //小程序签约支付
                //form = URLEncoder.encode(response.getBody(), "UTF-8");
            }else{
                log.info("支付宝周期扣款-发起签约失败,手机号:{},externalAgreementNo:{},响应:{}",mobile,externalAgreementNo,mapper.writeValueAsString(response));
            }
        } catch (AlipayApiException e) {
            log.error("支付宝周期扣款-发起签约失败,手机号:{},externalAgreementNo:{},错误:",mobile,externalAgreementNo,e);
            return febsResponse.fail().message("发起签约异常");
        }
        return febsResponse.success().data(form);
    }

    @Override
    public Boolean aliTradePay(String mobile,
                            String externalAgreementNo,
                            String agreementNo,
                            String outTradeNo,
                            String totalAmount,
                            String subject,String appId,String businessType,String businessName,String subChannel,String bizType) {
        log.info("支付宝周期扣款-发起扣款接口入口,手机号:{},externalAgreementNo:{}",mobile,externalAgreementNo);
        Boolean isAliPay=false;
        //匹配支付配置文件
        this.init(businessType);
        ObjectNode tradePayContentNode = mapper.createObjectNode();
        ObjectNode agreementParamsNode = mapper.createObjectNode();

        agreementParamsNode.put("agreement_no",agreementNo);

        tradePayContentNode.put("out_trade_no",outTradeNo);
        tradePayContentNode.put("total_amount",totalAmount);

        Integer count = aliSignChargingOrderService.lambdaQuery()
                .eq(AliSignChargingOrder::getExternalAgreementNo, externalAgreementNo)
                .eq(AliSignChargingOrder::getOrderStatus, "1")
                .count();
        if(count>0){
            count=count+1;
        }

        tradePayContentNode.put("subject",count>0?subject+count:subject);
        tradePayContentNode.put("product_code","CYCLE_PAY_AUTH");
        tradePayContentNode.putPOJO("agreement_params",agreementParamsNode);
        //创建订单
        AliSignChargingOrder aliSignChargingOrder = new AliSignChargingOrder();
        aliSignChargingOrder.setAgreementNo(agreementNo);
        aliSignChargingOrder.setMobile(mobile);
        aliSignChargingOrder.setOrderNo(outTradeNo);
        aliSignChargingOrder.setOrderStatus(0);
        aliSignChargingOrder.setOrderAmount(totalAmount);
        aliSignChargingOrder.setExternalAgreementNo(externalAgreementNo);
        aliSignChargingOrder.setAppId(appId);
        aliSignChargingOrder.setBusinessType(businessType);
        aliSignChargingOrder.setBusinessName(count>0?businessName+count:businessName);
        aliSignChargingOrder.setSubChannel(subChannel);
        aliSignChargingOrder.setBizType(bizType);
        try {
            log.info("支付宝周期扣款-发起扣款请求开始,手机号:{},externalAgreementNo:{}",mobile,externalAgreementNo);
            AlipayTradePayRequest request = new AlipayTradePayRequest();
            request.setNotifyUrl(aliSignUpConfig.getTradePayNotifyUrl());//支付异步通知地址
            request.setBizContent(tradePayContentNode.toString());
            AlipayTradePayResponse response = aliSignUpClient.execute(request);
            aliSignChargingOrder.setRemark(response.getSubMsg());
            log.info("支付宝周期扣款-发起扣款请求结束,手机号:{},externalAgreementNo:{},响应:{}",mobile,externalAgreementNo,mapper.writeValueAsString(response));
            if(response.isSuccess()){
                isAliPay=true;
            }
            if(StringUtils.isNotBlank(response.getSubCode()) && "aop.ACQ.SYSTEM_ERROR".equals(response.getSubCode())){
//                aliPayComplainProperties.getWarnMobileList().forEach(mobiles->{
//                    smsModelService.sendSms(mobiles,BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE,businessType,BizConstant.BUSINESS_TYPE_WARN,businessType);
//                });
                warnMobileService.smsWarn("3",BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE).forEach(mobiles->{
                    smsModelService.sendSms(mobiles,BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE,businessType,BizConstant.BUSINESS_TYPE_WARN,businessType);
                });
            }
        } catch (Exception e) {
            log.error("支付宝周期扣款-发起扣款,手机号:{},externalAgreementNo:{},错误:",mobile,externalAgreementNo,e);

        }
        aliSignChargingOrderService.save(aliSignChargingOrder);
        return isAliPay;
    }
    @Override
    public void unSign(String externalAgreementNo) {
        AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo,externalAgreementNo).eq(AliSignRecord::getSignStatus,1).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
        if(aliSignRecord!=null){
            AlipayUserAgreementUnsignResponse response= this.alipayRescind(aliSignRecord.getAgreementNo(),aliSignRecord.getBusinessType());
            String remark="";
            if(response.isSuccess()){
                remark="解约调用成功=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
            } else {
                remark="解约调用失败=>{code:"+response.getCode()+",msg:"+response.getMsg()+",sub_code:"+response.getSubCode()+",sub_msg:"+response.getSubMsg()+"}";
            }
            aliSignRecordService.lambdaUpdate()
                    .eq(AliSignRecord::getId, aliSignRecord.getId()).set(AliSignRecord::getRemark,remark).update();
        }
    }
    @Override
    public AlipayNotifyParam aliTradePayResultNotify(Map<String, String> requestParams) throws AlipayApiException {
        log.info("支付宝支付交易结果通知Json，{}", requestParams);
        AlipayNotifyParam notifyParam = mapper.convertValue(requestParams, AlipayNotifyParam.class);
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getAppId,notifyParam.getAppId()).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if (alipay==null) {
            throw new AlipayApiException("app_id不一致");
        }
        // 调用SDK验证签名
        //boolean signVerified = AlipaySignature.rsaCheckV1(requestParams, alipay.getPublicKey(),
        //        aliSignUpConfig.getCharset(), aliSignUpConfig.getSignType());
        //if (signVerified) {
        //    log.info("支付宝支付交易结果通知签名认证失败，signVerified=false");
        //    throw new AlipayApiException("支付宝支付交易结果通知签名认证失败");
        //}
        // 按照支付结果异步通知中的描述，对支付结果中的业务内容进行1\2\3\4二次校验，校验成功后在response中返回success，校验失败返回failure
        // 4、验证app_id是否为该商户本身。
        //String trade_status = notifyParam.getTradeStatus();
        // 支付成功
        //if (trade_status.equals(AliPayConfig.AlipayTradeStatus.TRADE_SUCCESS) || trade_status.equals(
        //        AliPayConfig.AlipayTradeStatus.TRADE_FINISHED)) {
        //}
        //需要做状态判断 防止重复收到回调！！！
        log.info("支付宝支付订单ID={}已收到支付回调，付款成功", notifyParam.getOutTradeNo());
        return notifyParam;

    }

    @Override
    public AlipayUserAgreementQueryResponse aliTradePayQuery(String externalAgreementNo,String businessType){
        //匹配支付配置文件
        this.init(businessType);
        log.info("支付宝个人代扣协议查询,代扣协议中标示用户的唯一签约号externalAgreementNo:{}",externalAgreementNo);
        AlipayUserAgreementQueryRequest request = new AlipayUserAgreementQueryRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("personal_product_code","CYCLE_PAY_AUTH_P");
        bizContent.put("sign_scene","INDUSTRY|APPSTORE");
        bizContent.put("external_agreement_no",externalAgreementNo);
        request.setBizContent(bizContent.toString());
        AlipayUserAgreementQueryResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 刷单支付
     * @param mobile
     * @param outTradeNo
     * @param totalAmount
     * @param subject
     * @param appId
     * @param businessType
     * @param businessName
     */
    @Override
    public void aliTradeBrushSheetPay(String mobile,
                            String outTradeNo,
                            String totalAmount,
                            String subject,String appId,String businessType,String businessName, HttpServletResponse httpResponse,Subscribe subscribe) {
        //匹配支付配置文件
        this.init(businessType);
        //创建订单
        AliSignChargingOrder aliSignChargingOrder = new AliSignChargingOrder();
        aliSignChargingOrder.setMobile(mobile);
        aliSignChargingOrder.setOrderNo(outTradeNo);
        aliSignChargingOrder.setOrderStatus(0);
        aliSignChargingOrder.setOrderAmount(totalAmount);
        aliSignChargingOrder.setAppId(appId);
        aliSignChargingOrder.setBusinessType(businessType);
        aliSignChargingOrder.setBusinessName(businessName);
        //统一收单下单并支付页面接口
        AlipayTradeWapPayRequest  alipayRequest = new AlipayTradeWapPayRequest();
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode bizContentNode = mapper.createObjectNode();
        bizContentNode.put("out_trade_no", outTradeNo);
        bizContentNode.put("total_amount", totalAmount);
        bizContentNode.put("subject", subject);
        bizContentNode.put("product_code", "QUICK_WAP_WAY");
        String bizContent = bizContentNode.toString();
        alipayRequest.setBizContent(bizContent);//填充业务参数
        alipayRequest.setReturnUrl(aliSignUpConfig.getReturnUrl()); //设置通知地址
        alipayRequest.setNotifyUrl(aliSignUpConfig.getTradePayNotifyUrl());//设置回跳
        String form = "";
        try {
            AlipayTradeWapPayResponse response= aliSignUpClient.pageExecute(alipayRequest);
            aliSignChargingOrder.setRemark(response.getSubMsg());
            log.info("刷单WEB支付接口调用返回:{},手机号{}：",response.getMsg(),mobile);
            if(response.isSuccess()){
                log.info("刷单WEB支付接口调用成功,手机号{}：",mobile);
                form = response.getBody();
            }else{
                log.info("刷单WEB支付失败:{}",mobile);
            }
            if(StringUtils.isNotBlank(response.getSubCode()) && "aop.ACQ.SYSTEM_ERROR".equals(response.getSubCode())){
//                aliPayComplainProperties.getWarnMobileList().forEach(mobiles->{
//                    smsModelService.sendSms(mobiles,BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE,businessType,BizConstant.BUSINESS_TYPE_WARN,businessType);
//                });
                warnMobileService.smsWarn("3",BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE).forEach(mobiles->{
                    smsModelService.sendSms(mobiles,BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE,businessType,BizConstant.BUSINESS_TYPE_WARN,businessType);
                });
            }
            httpResponse.setContentType("text/html;charset=" + aliPayConfig.getCharset());
            httpResponse.getWriter()
                    .write(form);//直接将完整的表单html输出到页面
            httpResponse.getWriter()
                    .flush();
            httpResponse.getWriter()
                    .close();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("刷单WEB支付接口调用异常,手机号:{}",mobile,e);
        }
        aliSignChargingOrderService.save(aliSignChargingOrder);
        subscribe.setIspOrderNo(outTradeNo);
        subscribe.setChannel(businessType);
        subscribe.setBizType("MEMBER_ALIPAY");
        saveSubscribe(subscribe);
    }
    private void saveSubscribe(Subscribe subscribe){
        //保存sub信息
        try{
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if(StringUtils.isNotBlank(subscribe.getSource())){
                subscribe = parseLink(subscribe.getSource(),subscribe);
            }
            subscribe.setProvince(mobileRegionResult!=null?mobileRegionResult.getProvince():"未知");
            subscribe.setCity(mobileRegionResult!=null?mobileRegionResult.getCity():"未知");
            String isp = mobileRegionResult!=null?mobileRegionResult.getOperator():"未知";
            subscribe.setIsp(isp);
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
            subscribe.setResult("未支付");
            subscribeService.createSubscribeDbAndEs(subscribe);
        } catch (Exception e) {
            log.error("支付宝签约sub订单出错：",e);
        }
    }
    private Subscribe parseLink(String source, Subscribe subscribe) {
        try{
            source = URLDecoder.decode(source, "UTF-8");
            String subChannel = HttpUrl.parse(source.replace("#/", "")).queryParameter("subChannel");
            subscribe.setSubChannel(subChannel);
            subscribe.setSource(source);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return subscribe;
    }

    /**
     * 延期扣款
     * @param agreementNo
     * @param businessType
     * @return
     * @throws AlipayApiException
     */
    @Override
    public AlipayUserAgreementExecutionplanModifyResponse alipayDelay(String agreementNo,String businessType,String nextDeductTime){
        //匹配支付配置文件
        this.init(businessType);
        log.info("周期性扣款协议执行计划修改，授权免密支付协议号agreementNo:{}",agreementNo);
        AlipayUserAgreementExecutionplanModifyRequest request = new AlipayUserAgreementExecutionplanModifyRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("agreement_no",agreementNo);
//        if(LocalDate.now().getDayOfMonth()>=29){
////            bizContent.put("deduct_time",LocalDate.now().plusMonths(1).withDayOfMonth(1).format(DateTimeFormatter.ISO_LOCAL_DATE));//商户发起首次扣款的时间
////        }else{
////            String deductTime =DateUtil.formatYearMonthDay(LocalDateTime.now());
////            bizContent.put("deduct_time",deductTime);
////        }
        bizContent.put("deduct_time",nextDeductTime);
        bizContent.put("memo","延期扣款时间");
        request.setBizContent(bizContent.toString());
        log.info("bizContent:{}",bizContent);
        AlipayUserAgreementExecutionplanModifyResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }


    /**
     * 解约
     * @param agreementNo
     * @param businessType
     * @return
     */
    @Override
    public AlipayUserAgreementUnsignResponse alipayRescind(String agreementNo,String businessType){
        //匹配支付配置文件
        this.init(businessType);
        log.info("支付宝个人代扣协议解约，代扣协议中标示用户的唯一签约号agreementNo:{}",agreementNo);
        AlipayUserAgreementUnsignRequest request = new AlipayUserAgreementUnsignRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("agreement_no",agreementNo);
        request.setBizContent(bizContent.toString());
        AlipayUserAgreementUnsignResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }


    /**
     * 支付宝退款
     * @param outTradeNo
     * @param refundAmount
     * @param outRequestNo
     * @param appId
     * @return
     */
    @Override
    public AlipayTradeRefundResponse alipayRefund(String outTradeNo,String refundAmount,String outRequestNo,String appId){
        this.initComplain(appId);
        log.info("支付宝统一收单交易退款，订单号:{}，退款金额:{}，退款订单号:{}，appId:{}",outTradeNo,refundAmount,outRequestNo,appId);
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("out_trade_no", outTradeNo);
        bizContent.put("refund_amount", refundAmount);
        bizContent.put("out_request_no", outRequestNo);
        request.setBizContent(bizContent.toString());
        AlipayTradeRefundResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }
    /**
     * 支付宝退款查询
     * @param outTradeNo
     * @param outRequestNo
     * @param appId
     * @return
     */
    @Override
    public AlipayTradeFastpayRefundQueryResponse alipayQueryRefund(String outTradeNo,String outRequestNo,String appId){
        this.initComplain(appId);
        log.info("支付宝统一收单交易退款查询，订单号:{}，退款订单号:{}，商户号:{}",outTradeNo,outRequestNo,appId);
        AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("out_trade_no", outTradeNo);
        bizContent.put("out_request_no", outRequestNo);
        request.setBizContent(bizContent.toString());
        AlipayTradeFastpayRefundQueryResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 查询单条交易投诉详情
     * @param complainEventId 投诉单号
     * @param appId
     * @return
     */
    @Override
    public AlipayMerchantTradecomplainQueryResponse alipayQueryComplainDetail(String complainEventId,String appId){
        this.initComplain(appId);
        log.info("查询单条交易投诉详情，支付宝侧投诉单号:{}，商户号:{}",complainEventId,appId);
        AlipayMerchantTradecomplainQueryRequest request = new AlipayMerchantTradecomplainQueryRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("complain_event_id", complainEventId);
        request.setBizContent(bizContent.toString());
        AlipayMerchantTradecomplainQueryResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 查询单条交易投诉列表
     * @param appId
     * @param status
     * @param beginTime
     * @param endTime
     * @param pageSize
     * @param pageNum
     * @return
     */
    @Override
    public AlipayMerchantTradecomplainBatchqueryResponse alipayQueryComplainList(String appId,String status,String beginTime,String endTime,Integer pageSize,Integer pageNum){
        this.initComplain(appId);
        log.info("查询单条交易投诉列表，商户号:{}",appId);
        AlipayMerchantTradecomplainBatchqueryRequest request = new AlipayMerchantTradecomplainBatchqueryRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        if(StringUtils.isBlank(status)){
            bizContent.put("status", status);
        }
        if(pageSize!=null){
            bizContent.put("page_size", pageSize);
        }
        if(pageNum!=null){
            bizContent.put("page_num", pageNum);
        }
        if(StringUtils.isBlank(beginTime)){
            bizContent.put("begin_time", beginTime);
        }
        if(StringUtils.isBlank(endTime)){
            bizContent.put("end_time", endTime);
        }
        request.setBizContent(bizContent.toString());
        AlipayMerchantTradecomplainBatchqueryResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 商户上传处理图片
     * @param appId
     * @param imageContent 图片
     * @param imageType 图片后缀
     * @return
     */
    @Override
    public AlipayMerchantImageUploadResponse alipayUploadImg(String appId, FileItem imageContent,String imageType){
        this.initComplain(appId);
        log.info("商户上传处理图片，商户号:{},图片后缀:{}",appId,imageType);
        AlipayMerchantImageUploadRequest request = new AlipayMerchantImageUploadRequest();
        request.setImageType(imageType);
        request.setImageContent(imageContent);
        AlipayMerchantImageUploadResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 商家处理交易投诉
     * @param appId
     * @param complainEventId 投诉号
     * @param feedbackImages 商家处理投诉时反馈凭证的图片id，多个逗号隔开（图片id可以通过"商户上传处理图片"接口获取）
     * @param feedbackContent 投诉处理描述 200个字符
     * @param feedbackCode 反馈类目ID 00:使用体验保障金退款； 02:通过其他方式退款; 03:已发货; 04:其他; 05:已完成售后服务; 06:非我方责任范围；
     * @param operator 处理投诉人
     * @return
     */

    @Override
    public AlipayMerchantTradecomplainFeedbackSubmitResponse alipaySolveComplain(String appId, String complainEventId,String feedbackImages,String feedbackContent,String feedbackCode,String operator){
        this.initComplain(appId);
        log.info("商家处理交易投诉，商户号:{}",appId);
        AlipayMerchantTradecomplainFeedbackSubmitRequest request = new AlipayMerchantTradecomplainFeedbackSubmitRequest();
        request.setComplainEventId(complainEventId);
        request.setFeedbackCode(feedbackCode);
        request.setFeedbackContent(feedbackContent);
        if(StringUtils.isNotBlank(feedbackImages)){
            request.setFeedbackImages(feedbackImages);
        }
        if(StringUtils.isNotBlank(operator)){
            request.setOperator(operator);
        }
        AlipayMerchantTradecomplainFeedbackSubmitResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }


    /**
     * 商家留言回复
     * @param appId
     * @param complainEventId 投诉号
     * @param replyImages 商家处理投诉时反馈凭证的图片id，多个逗号隔开（图片id可以通过"商户上传处理图片"接口获取）
     * @param replyContent 投诉处理描述 200个字符
     * @return
     */

    @Override
    public AlipayMerchantTradecomplainReplySubmitResponse alipayReplyComplain(String appId, String complainEventId,String replyImages,String replyContent){
        this.initComplain(appId);
        log.info("商家留言回复，商户号:{}",appId);
        AlipayMerchantTradecomplainReplySubmitRequest request = new AlipayMerchantTradecomplainReplySubmitRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("complain_event_id", complainEventId);
        bizContent.put("reply_content", replyContent);
        bizContent.put("reply_images", replyImages);
        request.setBizContent(bizContent.toString());
        AlipayMerchantTradecomplainReplySubmitResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }
    /**
     * 商家补充凭证
     * @param appId
     * @param complainEventId 投诉号
     * @param supplementContent 商家处理投诉时反馈凭证的图片id，多个逗号隔开（图片id可以通过"商户上传处理图片"接口获取）
     * @param supplementImages 投诉处理描述 200个字符
     * @return
     */

    @Override
    public AlipayMerchantTradecomplainSupplementSubmitResponse alipaySupplementComplain(String appId, String complainEventId,String supplementImages,String supplementContent){
        this.initComplain(appId);
        log.info("商家留言回复，商户号:{}",appId);
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("complain_event_id", complainEventId);
        bizContent.put("supplement_content", supplementContent);
        bizContent.put("supplement_images", supplementImages);
        AlipayMerchantTradecomplainSupplementSubmitRequest request = new AlipayMerchantTradecomplainSupplementSubmitRequest();
        request.setBizContent(bizContent.toString());
        AlipayMerchantTradecomplainSupplementSubmitResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }



    /**
     * 查询转账订单
     * @param outBizNo 转账订单号
     * @param businessType 渠道号
     * @return
     */
    @Override
    public AlipayFundTransOrderQueryResponse  aliPayQueryTransferFee(String outBizNo,String businessType){
        this.initRefund(businessType);
        log.info("单笔转账到支付宝账户，转账订单号:{}，渠道号:{}",outBizNo,businessType);
        AlipayFundTransOrderQueryRequest request = new AlipayFundTransOrderQueryRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("out_biz_no", outBizNo);
        request.setBizContent(bizContent.toString());
        AlipayFundTransOrderQueryResponse  response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 支付宝转账
     * @param outBizNo 转账订单号
     * @param amount 转账金额
     * @param payeeType 、ALIPAY_USERID：支付宝账号对应的支付宝唯一用户号。以2088开头的16位纯数字组成。 2、ALIPAY_LOGONID：支付宝登录号，支持邮箱和手机号格式。 3、ALIPAY_OPENID：支付宝openid
     * @param payeeAccount 账户
     * @param businessType 渠道号
     * @param payerShowName 付款方姓名
     * @param remark 转账备注
     * @return
     */
    @Override
    public AlipayFundTransToaccountTransferResponse  aliPayTransferFee(String outBizNo,String amount,String payeeType,String payeeAccount,String businessType,String payerShowName,String remark){
        Alipay alipay=this.initRefund(businessType);
        log.info("单笔转账到支付宝账户，转账订单号:{}，转账金额:{}，账户类型:{}，账户:{}，渠道号:{}",outBizNo,amount,payeeType,payeeAccount,businessType);
        AlipayFundTransToaccountTransferRequest request = new AlipayFundTransToaccountTransferRequest();
        ObjectNode bizContent = mapper.createObjectNode();
        bizContent.put("out_biz_no", outBizNo);
        bizContent.put("payee_type", payeeType);
        bizContent.put("payee_account", payeeAccount);
        bizContent.put("amount", amount);
        if(StringUtils.isNotBlank(alipay.getRemark())){
            String[] remarks=alipay.getRemark().split("@");
            payerShowName=remarks[0];
            remark=remarks[1]+remark;
        }
        if(StringUtils.isNotBlank(payerShowName)){
            bizContent.put("payer_show_name", payerShowName);
        }
        if(StringUtils.isNotBlank(remark)){
            bizContent.put("remark", remark);
        }
        request.setBizContent(bizContent.toString());
        AlipayFundTransToaccountTransferResponse response = null;
        try {
            response = aliSignUpClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }
    /**
     *
     * @param outBizNo 转账订单号
     * @param amount 转账金额
     * @param identityType ALIPAY_USER_ID：支付宝会员的用户 ID，可通过 获取会员信息 能力获取。
     * ALIPAY_LOGON_ID：支付宝登录号，支持邮箱和手机号格式。
     * @param identity 账户
     * @param name identity_type=ALIPAY_LOGON_ID 时，本字段必填 收款真实姓名
     * @param businessType 渠道号
     * @param remark 业务备注
     * @param payerShowNameUseAlias 展示别名，将展示商家支付宝在商家中心 商户信息 > 商户基本信息 页面配置的 商户别名。
     * @return
     */
    @Override
    public AlipayFundTransUniTransferResponse  aliPayTransferFeeNew(String outBizNo,String amount,String identityType,String identity,String name,String businessType,String remark,String payerShowNameUseAlias){
        Alipay alipay=this.transferFee(businessType);
        log.info("单笔转账到支付宝账户，证书转账，转账订单号:{}，转账金额:{}，账户类型:{}，账户:{}，渠道号:{}",outBizNo,amount,identityType,identity,businessType);
        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        AlipayFundTransUniTransferModel model = new AlipayFundTransUniTransferModel();


        String orderTitle="";
        if(StringUtils.isNotBlank(alipay.getRemark())){
            String[] remarks=alipay.getRemark().split("@");
            orderTitle=remarks[0];
            remark=remarks[1]+remark;
        }
        if(StringUtils.isNotBlank(remark)){
            model.setRemark(remark);
        }
        if(StringUtils.isNotBlank(orderTitle)){
            model.setOrderTitle(orderTitle);
        }
        model.setOutBizNo(outBizNo);
        if(StringUtils.isNotBlank(payerShowNameUseAlias)){
            model.setBusinessParams("{\"payer_show_name_use_alias\":\""+payerShowNameUseAlias+"\"}");
        }

        model.setBizScene("DIRECT_TRANSFER");
        Participant payeeInfo = new Participant();
        payeeInfo.setIdentity(identity);
        payeeInfo.setIdentityType(identityType);
        if(StringUtils.isNotBlank(name)){
            payeeInfo.setName(name);
        }
        model.setPayeeInfo(payeeInfo);
        model.setTransAmount(amount);
        model.setProductCode("TRANS_ACCOUNT_NO_PWD");

        request.setBizModel(model);
        AlipayFundTransUniTransferResponse response = null;
        try {
            response = aliSignUpClient.certificateExecute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 查询转账订单
     * @param outBizNo 转账订单号
     * @param businessType 渠道号
     * @return
     */
    @Override
    public AlipayFundTransCommonQueryResponse  aliPayQueryTransferFeeNew(String outBizNo,String businessType){
        this.transferFee(businessType);
        log.info("单笔转账到支付宝账户，证书转账，转账订单号:{}，渠道号:{}",outBizNo,businessType);
        AlipayFundTransCommonQueryRequest request = new AlipayFundTransCommonQueryRequest();
        AlipayFundTransCommonQueryModel model = new AlipayFundTransCommonQueryModel();
        model.setOutBizNo(outBizNo);
        model.setProductCode("TRANS_ACCOUNT_NO_PWD");
        model.setBizScene("DIRECT_TRANSFER");
        request.setBizModel(model);
        AlipayFundTransCommonQueryResponse response = null;
        try {
            response = aliSignUpClient.certificateExecute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
       return response;
    }




    /**
     * 查询账户余额
     * @param businessType 渠道号
     * @return
     */
    @Override
    public AlipayFundAccountQueryResponse  aliPayQueryAccountBalance(String businessType){
        Alipay alipay=this.transferFee(businessType);
        log.info("支付宝资金账户资产查询，证书转账，渠道号:{}",businessType);
        AlipayFundAccountQueryRequest request = new AlipayFundAccountQueryRequest();
        AlipayFundAccountQueryModel model = new AlipayFundAccountQueryModel();
        model.setAlipayUserId(alipay.getExternalLogonId());
        model.setAccountType("ACCTRANS_ACCOUNT");
        request.setBizModel(model);
        AlipayFundAccountQueryResponse response = null;
        try {
            response = aliSignUpClient.certificateExecute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        System.out.println(response.getBody());
        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
        return response;
    }


    @Override
    public FebsResponse getOpenId(String code, String tradeType, String businessType) throws Exception{
        Alipay alipay=this.initRefund(businessType);
        if(alipay==null){
            log.info("支付宝配置查询失败:授权码:{},交易类型:{},业务类型:{}", code,tradeType,businessType);
            throw new Exception("配置错误,请稍后再试!");
        }
        String alipayAccessToken = "ALIPAY_ACCESS_TOKEN:"+alipay.getAppId()+":"+tradeType+ ":" + businessType;
        if (redisUtil.get(alipayAccessToken) != null) {
            return new FebsResponse().success().data(redisUtil.get(alipayAccessToken));
        }
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setGrantType("authorization_code");
        request.setCode(code);
        AlipaySystemOauthTokenResponse response = aliSignUpClient.execute(request);
        if(response.isSuccess()){
            final ObjectNode objectNode = mapper.createObjectNode()
                    .put("access_token", response.getAccessToken())
                    .put("user_id", response.getUserId());
            redisUtil.set(alipayAccessToken,objectNode.toString(), ALIPAY_ACCESS_TOKEN_CATCHE_TIME);
            return new FebsResponse().success().data(objectNode.toString());
        }
        return new FebsResponse().fail();
    }

    @Override
    public FebsResponse decrypt(String response, String sign, String businessType)  throws Exception{
        Alipay alipay=this.initRefund(businessType);
        if(alipay==null){
            log.info("支付宝配置查询失败:加密数据:{},签名:{},授权码:{}", response,sign,businessType);
            throw new Exception("配置错误,请稍后再试!");
        }
        //验签
        boolean signCheckPass = AlipaySignature.rsaCheck(response, sign, alipay.getPublicKey(), aliSignUpConfig.getCharset(), aliSignUpConfig.getSignType());
        if (!signCheckPass) {
            //验签不通过（异常或者报文被篡改），终止流程（不需要做解密）
            throw new Exception("验签失败");
        }
        //解密
        String plainData = AlipayEncrypt.decryptContent(response, ENCRYPT_TYPE, alipay.getServerIp(), aliSignUpConfig.getCharset());
        JsonNode jsonNode = mapper.readTree(plainData);
        String mobile = jsonNode.at("/mobile").asText();
        return new FebsResponse().success().data(mobile) ;
    }
    @Override
    public FebsResponse aliSignPay(Subscribe subscribe) throws Exception {
        String businessType=subscribe.getBusinessType();
        String mobile = subscribe.getMobile();
        Alipay alipayConfig = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType, businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if(alipayConfig==null){
            return new FebsResponse().fail().message("支付参数错误");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if(!StringUtils.equals(alipayConfig.getBizType(),BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspYidong()){
            alipayConfig.setBizType(BizConstant.BIZ_TYPE_VRBT_YD_ALIPAY);
        }
        if(!StringUtils.equals(alipayConfig.getBizType(),BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspDianxin()){
            alipayConfig.setBizType(BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY);
        }
        if(!StringUtils.equals(alipayConfig.getBizType(),BizConstant.BIZ_TYPE_MEMBER_ALIPAY) && mobileRegionResult!=null && mobileRegionResult.isIspLiantong()){
            alipayConfig.setBizType(BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY);
        }
        if (mobileRegionResult != null && !provinceBusinessChannelConfigService.allow(businessType, mobileRegionResult.getProvince())) {
            log.warn("支付宝权益订购省份限制,渠道号:{},手机号:{},省份:{}",businessType, mobile, mobileRegionResult.getProvince());
            return new FebsResponse().fail().message("暂未开放,敬请期待!");
        }
        String signValidityPeriod = String.valueOf(alipayConfig.getSignValidityPeriod().equals("0")?"":alipayConfig.getSignValidityPeriod());//当前用户签约请求的协议有效周期。d天m月 如果未传入，默认为长期有效。
        String periodType =alipayConfig.getPeriodType();//周期类型枚举值为DAY和MONTH
        String period =String.valueOf(alipayConfig.getPeriod());//周期数
        String singleAmount =alipayConfig.getSingleAmount();//每次发起扣款时限制的最大金额单位为元
        String totalAmount =String.valueOf(alipayConfig.getTotalAmount().equals("0")?"":alipayConfig.getTotalAmount());//周期内允许扣款的总金额，单位为元
        String totalPayments =String.valueOf(alipayConfig.getTotalPayments().equals(0)?"":alipayConfig.getTotalPayments());//总扣款次数
        //referer字段存储指纹

        if(!BizConstant.ALIPAY_LIST.contains(businessType)){
            //同一手机号只能签约一次
            List<AliSignRecord> signList = aliSignRecordService.lambdaQuery()
                    .eq(AliSignRecord::getMobile, mobile)
                    .eq(AliSignRecord::getSignStatus, 1)
                    .eq(AliSignRecord::getBizType, alipayConfig.getBizType())
                    .notIn(AliSignRecord::getBusinessType,BizConstant.ALIPAY_LIST)
                    .list();

            List<WoReadOrder> woReadSignList = woReadOrderService.lambdaQuery()
                    .eq(WoReadOrder::getMobile, mobile)
                    .eq(WoReadOrder::getSubStatus, 2)
                    .eq(WoReadOrder::getBizType, alipayConfig.getBizType())
                    .notIn(WoReadOrder::getBusinessType,BizConstant.ALIPAY_LIST)
                    .list();
            if((signList != null && signList.size() > 0) || (woReadSignList != null && woReadSignList.size() > 0)){
                return new FebsResponse().fail().message("您已是会员，请勿重复订购");
            }
            //同一产品同一手机号解约后半年内不能再次签约
            LocalDateTime start = LocalDateTime.now().plusMonths(-6);
            LocalDateTime end=    LocalDateTime.now();
            List<AliSignRecord> unSignList = aliSignRecordService.lambdaQuery()
                    .eq(AliSignRecord::getMobile, mobile)
                    .between(AliSignRecord::getUpdateTime, start, end)
                    .eq(AliSignRecord::getSignStatus, 3)
                    .eq(AliSignRecord::getBizType, alipayConfig.getBizType())
                    .notIn(AliSignRecord::getBusinessType,BizConstant.ALIPAY_LIST)
                    .list();

            List<WoReadOrder> woReadUnSignList = woReadOrderService.lambdaQuery()
                    .eq(WoReadOrder::getMobile, mobile)
                    .eq(WoReadOrder::getSubStatus, 3)
                    .between(WoReadOrder::getUnSignTime, start, end)
                    .eq(WoReadOrder::getBizType, alipayConfig.getBizType())
                    .notIn(WoReadOrder::getBusinessType,BizConstant.ALIPAY_LIST)
                    .list();

            if((unSignList != null && unSignList.size() > 0) || (woReadUnSignList != null && woReadUnSignList.size() > 0)){
                return new FebsResponse().fail().message("您已是会员，请勿重复订购");
            }
        }
        if(mobileRegionResult != null && mobileRegionResult.isIspDianxin() && StringUtils.equals(alipayConfig.getBizType(),BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY)){
            IBusinessCommonService businessCommonService = SpringContextUtils.getBean(DianXinAlipayVrbtBusinessServiceImpl.class);
            if(StringUtils.isEmpty(subscribe.getSmsCode())){
                Result result=businessCommonService.receiveOrderWithCache(subscribe);
                if(result.isOK()){
                    return new FebsResponse().code(HttpStatus.NOT_EXTENDED).message(result.getMessage()).data(result.getResult());
                }
                return new FebsResponse().fail().message(result.getMessage());
            }
            Result result=businessCommonService.receiveOrderWithCache(subscribe);
            if(!result.isOK()){
                return new FebsResponse().fail().message(result.getMessage());
            }
        }

        if(mobileRegionResult != null && mobileRegionResult.isIspLiantong() && StringUtils.equals(alipayConfig.getBizType(),BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY)){
            final boolean isCanOpenVideoRing = liantongVrbtService.isCanOpenVideoRing(mobile,BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            if(!isCanOpenVideoRing){
                return new FebsResponse().fail().message("暂未开放,敬请期待!");
            }
        }


        //保存签约信息
        String externalAgreementNo = "Ali" + mobile + new Date().getTime();//商户签约号
        Date nextDeductTime=aliSignRecordService.saveSignRecord(subscribe,externalAgreementNo,alipayConfig.getAppId(), businessType,alipayConfig.getBusinessName(),alipayConfig.getBizType());

        String executeTime = DateUtil.formatYearMonthDay(LocalDateTime.now());//商户发起首次扣款的时间--设置成今天
        //发起签约
        return this.aliSignUp(mobile,
                externalAgreementNo,
                signValidityPeriod,
                periodType,
                period,
                executeTime,
                singleAmount,
                totalAmount,
                totalPayments, businessType,nextDeductTime,alipayConfig.getBizType());
    }

    /**
     * 融合开通支付宝视频彩铃
     * @param subscribe
     * @return
     * @throws Exception
     */
    @Override
    public Result<?> aliSignPayFuse(Subscribe subscribe){
        FebsResponse febsResponse= null;
        try {
            febsResponse = this.aliSignPay(subscribe);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("支付宝企业彩铃周期扣款-发起签约失败,手机号:{},渠道号:{},错误:",subscribe.getMobile(),subscribe.getBusinessType(),e);
        }
        if(febsResponse==null){
            return Result.error("系统错误！");
        }
        if(febsResponse.isOK()){
            return Result.okAndSetData(febsResponse.get("data"));
        }
        if(febsResponse.get("code").toString().equals("510")){
            return Result.error(Integer.valueOf(febsResponse.get("code").toString()),febsResponse.get("message").toString(),febsResponse.get("data").toString());
        }
        return Result.error(Integer.valueOf(febsResponse.get("code").toString()),febsResponse.get("message").toString());
    }


    /**
     * 咪咕视频彩铃三方支付
     * @param mobile
     * @param outTradeNo
     * @param totalAmount
     * @param subject
     * @param appId
     * @param channel
     * @param subChannel
     * @return
     */
    @Override
    public  Result<?>  aliPayMiGuVrbt(String mobile,String outTradeNo,String totalAmount,String subject,String tradeType,String appId,String channel,String subChannel,String returnUrl,String notifyUrl,String ringType, String ringId,String copyRightId,String bizType,String ringName) {
        //匹配支付配置文件
        this.init(channel);
        //创建订单
        MiguVrbtPayOrder miguVrbtPayOrder= new MiguVrbtPayOrder();
        miguVrbtPayOrder.setMobile(mobile);
        miguVrbtPayOrder.setOrderNo(outTradeNo);
        miguVrbtPayOrder.setOrderAmount(totalAmount);
        miguVrbtPayOrder.setAppId(appId);
        miguVrbtPayOrder.setChannel(channel);
        miguVrbtPayOrder.setSubChannel(subChannel);
        miguVrbtPayOrder.setRingType(Integer.valueOf(ringType));
        miguVrbtPayOrder.setRingId(ringId);
        miguVrbtPayOrder.setCopyRightId(copyRightId);
        miguVrbtPayOrder.setTradeType(tradeType);
        miguVrbtPayOrder.setBizType(bizType);
        //铃音名称
        miguVrbtPayOrder.setRingName(ringName);
        //统一收单下单并支付页面接口
        AlipayTradeWapPayRequest  alipayRequest = new AlipayTradeWapPayRequest();
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode bizContentNode = mapper.createObjectNode();
        bizContentNode.put("out_trade_no", outTradeNo);
        bizContentNode.put("total_amount", totalAmount);
        bizContentNode.put("subject", subject);
        bizContentNode.put("product_code", "QUICK_WAP_WAY");
        String bizContent = bizContentNode.toString();
        alipayRequest.setBizContent(bizContent);//填充业务参数
        alipayRequest.setReturnUrl(returnUrl+"?orderId="+outTradeNo); //设置回跳
        alipayRequest.setNotifyUrl(notifyUrl);//设置通知地址
        String form = "";
        try {
            AlipayTradeWapPayResponse response= aliSignUpClient.pageExecute(alipayRequest);
            miguVrbtPayOrder.setRemark(response.getSubCode()+":"+response.getSubMsg());
            log.info("咪咕视频彩铃三方支付支付宝创建支付地址接口响应-手机号:{},描述:{},状态:{}",mobile,response.getMsg(),response.isSuccess());
            if(response.isSuccess()){
                form = response.getBody();
                miguVrbtPayOrder.setOrderStatus(-1);
            }else{
                miguVrbtPayOrder.setOrderStatus(0);
            }
            miguVrbtPayOrderService.save(miguVrbtPayOrder);
            if(response.isSuccess()){
                return Result.ok("成功",form);
            }
            return Result.error("咪咕视频彩铃三方支付支付宝创建支付地址失败");
        } catch (Exception e) {
            log.error("咪咕视频彩铃三方支付支付宝创建支付地址接口异常-手机号:{}",mobile,e);
            miguVrbtPayOrder.setOrderStatus(0);
            miguVrbtPayOrderService.save(miguVrbtPayOrder);
            return Result.error("咪咕视频彩铃三方支付支付宝创建支付地址失败");
        }
    }
}

