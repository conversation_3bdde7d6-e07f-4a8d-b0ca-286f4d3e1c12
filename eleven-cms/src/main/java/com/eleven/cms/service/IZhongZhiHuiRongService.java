package com.eleven.cms.service;

import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.Result;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/22 17:04
 **/
public interface IZhongZhiHuiRongService {

    Result<?> sendMessage(Subscribe subscribe);

    Result<?> submitOrder(Subscribe subscribe);

    void zhongZhiHuiRongNotify(String orderId,String mobile, String status, String code,String message);

}
