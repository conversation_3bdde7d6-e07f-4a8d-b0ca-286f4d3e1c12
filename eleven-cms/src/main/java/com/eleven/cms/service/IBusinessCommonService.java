package com.eleven.cms.service;

import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.Result;

public interface IBusinessCommonService {


    public static final String DIANXIN_SMS_INVALID_KEY_PREFIX = "dianxin::SmsInvalid:";
    public static final String YIDONG_SMS_INVALID_KEY_PREFIX = "yidong::SmsInvalid:";
    public static final long DIANXIN_SMS_INVALID_CACHE_SECONDS = 60;
    public static final long YIDONG_SMS_INVALID_CACHE_SECONDS = 60;
    public static final String MOBILE_SEND_SMS_LIMIT_KEY_PREFIX = "mobileSendSmsLimit:";
    //定义为90秒,和开通等待短信验证码时间保持一致
    public static final long MOBILE_SEND_SMS_CATCHE_TTL_SECONDS = 60;
    public static final String MOBILE_MONTH_EXISTS_KEY_PREFIX = "mobileMonthExists:";

    public static final String MOBILE_SEND_SMS_LIMIT_VALUE = "SMS_LIMIT";
    public static final long MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS = 90;
    public static final String MOBILE_MONTH_EXISTS_VALUE = "BIZ_EXISTS";

    //省份-四川
    public static final String SICHUAN_PROVINCE = "四川";

    //省份-上海
    public static final String SHANGHAI_PROVINCE = "上海";

    //省份-河北
    public static final String HEBEI_PROVINCE = "河北";

    //省份-甘肃
    public static final String GANSU_PROVINCE = "甘肃";

    //省份-甘肃
    public static final String GUANGXI_PROVINCE = "广西";

    //省份-甘肃
    public static final String HUNAN_PROVINCE = "湖南";

    //省份-重庆
    public static final String CHONGQING_PROVINCE = "重庆";

    //省份-河北
    public static final String HEILONGJIANG_PROVINCE = "黑龙江";

    //省份-贵州
    public static final String GUIZHOU_PROVINCE = "贵州";

    //省份-上海
    public static final String SHANDONG_PROVINCE = "山东";

    //省份-海南
    public static final String HAINAN_PROVINCE = "海南";

    //省份-海南
    public static final String GUANGDONG_PROVINCE = "广东";

    //省份-海南
    public static final String NINGXIA_PROVINCE = "宁夏";


    //省份-江西
    public static final String JIANGXI_PROVINCE = "江西";


    //省份-江西
    public static final String HENAN_PROVINCE = "河南";


    //城市-成都
    public static final String CHENGDU_CITY = "成都";

    Result receiveOrderWithCache(Subscribe subscribe);

    Result receiveOrder(Subscribe subscribe);

}
