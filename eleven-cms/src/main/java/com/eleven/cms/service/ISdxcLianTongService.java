package com.eleven.cms.service;

import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.Result;

import java.util.Map;

/**
 * 时代星辰联通业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/2 9:39
 **/
public interface ISdxcLianTongService {
    Result<?> sendMessage(String mobile, Subscribe subscribe);

    Result<?> submitOrder(Subscribe subscribe);

    void sdxcLianTongNotify(String phone, String status, String linkid);
}
