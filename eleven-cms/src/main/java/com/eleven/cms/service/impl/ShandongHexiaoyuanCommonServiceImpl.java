package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.GuizhouYidongService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.ShandongHexiaoyuanService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IProvinceChannelPhoneConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.*;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: lihb
 * @create: 2024-4-17 09:51:25
 */
@Service("shandongHexiaoyuanCommonService")
@Slf4j
public class ShandongHexiaoyuanCommonServiceImpl implements IBizCommonService {

    private static final Interner<String> interner = Interners.newWeakInterner();

    @Autowired
    ShandongHexiaoyuanService shandongHexiaoyuanService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private IProvinceChannelPhoneConfigService provinceChannelPhoneConfigService;

    public static final List<String> BLACK_CITY_LIST
            = Arrays.asList("淄博","烟台");

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        HenanMobileSmsResult getSmsResult = null;
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            String isp = mobileRegionResult.getOperator();
            subscribe.setIsp(isp);
            if (!provinceChannelPhoneConfigService.allow(subscribe.getChannel(), subscribe.getMobile())) {
                log.warn("移动省份号段限制,渠道号:{},手机号:{}", subscribe.getChannel(), subscribe.getMobile());
                return Result.error("暂未开放,敬请期待!");
            }
            if(BLACK_CITY_LIST.contains(subscribe.getCity())){
                log.warn("移动省份号段限制,渠道号:{},手机号:{}", subscribe.getChannel(), subscribe.getMobile());
                return Result.error("暂未开放,敬请期待!");
            }
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobile = subscribe.getMobile();
        synchronized (interner.intern(mobile)) {
            ShandongHexiaoyuanResult shandongHexiaoyuanResult = shandongHexiaoyuanService.getImgVerifyCodeAndSms(mobile);
            if (shandongHexiaoyuanResult.isOK()) {
                subscribe.setResult("获取验证码成功");
                subscribeService.createSubscribeDbAndEs(subscribe);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribe.setResult(shandongHexiaoyuanResult.getMsg());
                subscribeService.createSubscribeDbAndEs(subscribe);
//                return Result.error("获取验证码失败");

                try {
                    String errorMsg="{\"state\":\""+shandongHexiaoyuanResult.getState()+"\",\"msg\":\""+shandongHexiaoyuanResult.getMsg()+"\"}";
                    return Result.errorSmsMsg(errorMsg);
                } catch (Exception e) {
                    log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                    return Result.msgSmsError();
                }
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        final String transactionId = subscribe.getId();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        final Subscribe target = subscribeService.getById(transactionId);
        if (Objects.isNull(target)) {
            return Result.captchaErr("请求参数错误");
        }
        target.setBizTime(new Date());
        ShandongHexiaoyuanResult shandongHexiaoyuanResult = shandongHexiaoyuanService.smsCode(mobile, smsCode);
        if (shandongHexiaoyuanResult.isOK()) {
            target.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            target.setResult("短信验证码提交成功");
            subscribeService.updateSubscribeDbAndEs(target);
            return Result.ok("短信验证码提交成功", "SHANDONG");
        } else {
            //订阅失败
            target.setStatus(SUBSCRIBE_STATUS_FAIL);
            target.setResult(shandongHexiaoyuanResult.getMsg());
            subscribeService.updateSubscribeDbAndEs(target);
            return Result.error(500, "订阅失败", "SHANDONG");
        }
    }
}
