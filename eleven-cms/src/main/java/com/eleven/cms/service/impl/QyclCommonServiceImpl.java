package com.eleven.cms.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service("qyclCommonService")
public class QyclCommonServiceImpl implements IBizCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    public static final String DEPARTMENT_NAME_GR_PREFIX = "GR";
    public static final String[] BLACK_CITY_ARRAY = {"大庆", "烟台"};

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        if (StringUtils.equalsAny(subscribe.getCity(), BLACK_CITY_ARRAY)) {
            log.warn("移动城市限制,渠道号:{},手机号:{},城市:{}", subscribe.getChannel(), subscribe.getMobile(), subscribe.getCity());
            return Result.msgProvinceLimit();
        }
        //30天内开通过的,不再允许重复开通
        List<Subscribe> subscribeList = subscribeService.lambdaQuery().eq(Subscribe::getMobile, subscribe.getMobile()).eq(Subscribe::getBizType, BIZ_TYPE_QYCL)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS).gt(Subscribe::getCreateTime, LocalDate.now().minusDays(30L).atTime(LocalTime.MIN)).list();
        if (subscribeList.size() > 0) {
            return Result.error("请勿重复开通！");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        String qyclAddDp = subscribe.getQyclAddDp();
        String departmentId = subscribe.getQyclDpId();
        if (StringUtils.isEmpty(qyclAddDp)) {
            qyclAddDp = "1";
        }
        if (!StringUtils.equalsAny(qyclAddDp, "0", "1")) {
            return Result.error("部门信息参数错误");
        }
        boolean isPay = StringUtils.equals(qyclAddDp, "0");
        int addDepartment = 1;
        String departmentName = DEPARTMENT_NAME_GR_PREFIX + RandomUtil.randomString(6);
        if ((addDepartment == 1 && StringUtils.isEmpty(departmentName)) || addDepartment == 0 && StringUtils.isEmpty(departmentId)) {
            return Result.error("部门信息参数错误");
        }
        String defSeq = subscribe.getExtra();
        if (StringUtils.isEmpty(defSeq)) {
            //透传参数格式
            defSeq = UUID.randomUUID() + "diy";
        }
        final BillingResult billingResult = yidongVrbtCrackService.getSmsQycl(subscribe.getMobile(), subscribe.getChannel(),
                addDepartment, departmentName, departmentId, defSeq, subscribe.getServiceId());
        boolean isOK = billingResult.isOK();
        if (isOK) {
            subscribe.setIspOrderNo(billingResult.getTransId());
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        }
        if (isPay) {


//            return isOK ? Result.ok("验证码已发送", billingResult.getTransId()) : Result.error("短信发送失败,请稍后再试");
            if(isOK){
                return Result.ok("验证码已发送", billingResult.getTransId());
            }

            try {
                String errorMsg="{\"code\":\""+billingResult.getCode()+"\",\"message\":\""+billingResult.getMessage()+"\"}";
                return Result.errorSendSmsDelayMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSendSmsDelayerror();
            }
        } else {
            subscribeService.createSubscribeDbAndEs(subscribe);
//            return isOK ? Result.ok("验证码已发送", subscribe.getId()) : Result.error("短信发送失败,请稍后再试");
            if(isOK){
                return Result.ok("验证码已发送", subscribe.getId());
            }

            try {
                String errorMsg="{\"code\":\""+billingResult.getCode()+"\",\"message\":\""+billingResult.getMessage()+"\"}";
                return Result.errorSendSmsDelayMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSendSmsDelayerror();
            }

        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String qyclAddDp = subscribe.getQyclAddDp();
        if (StringUtils.isEmpty(qyclAddDp)) {
            qyclAddDp = "1";
        }
        boolean isPay = StringUtils.equals(qyclAddDp, "0");
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        if (isPay) {
            yidongVrbtCrackService.smsCode(subscribe.getTransactionId(), smsCode, subscribe.getChannel(), subscribe.getMobile());
            return Result.ok("提交验证码成功");
        } else {
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(subscribe.getId());
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setBizTime(new Date());
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
            return Result.ok("提交验证码成功");
        }
    }
}
