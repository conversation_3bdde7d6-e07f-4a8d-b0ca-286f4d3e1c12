package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.ShydPayLog;
import com.eleven.cms.mapper.ShydPayLogMapper;
import com.eleven.cms.service.IShydPayLogService;
import com.eleven.cms.shanghaimobile.util.ShanghaiMobileConstant;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 上海移动5G特惠包办理日志 Service实现
 *
 * <AUTHOR>
 * @date 2021-04-01 10:41:24
 */
@Service
public class ShydPayLogServiceImpl extends ServiceImpl<ShydPayLogMapper, ShydPayLog> implements IShydPayLogService {

    @Override
    public void updateShydPayLog(ShydPayLog shydPayLog, String code, Integer status, Integer flow, String message) {
        shydPayLog.setStatus(status);
        shydPayLog.setFlow(flow);
        shydPayLog.setModifyTime(new Date());
        shydPayLog.setCode(code);
        shydPayLog.setMessage(message);
        this.saveOrUpdate(shydPayLog);
    }
    @Override
    public ShydPayLog saveShydPayLog(String phone, String orderId, String message, String code, Integer handleStatusSucceed, Integer flowInitialStateOrder, String buyId, String price,String subject,Integer isRight) {
        ShydPayLog shydPayLog = new ShydPayLog();
        shydPayLog.setOutTradeNo(orderId);
        shydPayLog.setMobile(phone);
        shydPayLog.setBuyGoodsId(buyId);
        shydPayLog.setPrice(price);
        shydPayLog.setSubject(subject);
        shydPayLog.setCode(code);
        shydPayLog.setMessage(message);
        shydPayLog.setPayWay(ShanghaiMobileConstant.PAY_WAY);
        shydPayLog.setRemark(ShanghaiMobileConstant.MOBILE_SPECIAL_PACKAGE_TYPE);
        shydPayLog.setFlow(handleStatusSucceed);
        shydPayLog.setStatus(flowInitialStateOrder);
        shydPayLog.setIsRight(isRight);
        this.save(shydPayLog);
        return shydPayLog;
    }
}
