package com.eleven.cms.service.impl;

import com.eleven.cms.ad.MiGuKuaiYouVRJingMengApiProperties;
import com.eleven.cms.config.KuaiYouVRJingMengChannel;
import com.eleven.cms.dto.MiGuKuaiYouVRJingMengGetUserResult;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IMiGuKuaiYouVRJingMengService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RSA256Utils;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 咪咕快游VR竞盟业务接口
 */
@Slf4j
@Service
public class MiGuKuaiYouVRJingMengServiceImpl implements IMiGuKuaiYouVRJingMengService {
    //咪咕快游VR竞盟旧渠道号
    public static final String VRJINGMENG_CHANNEL_OLD ="HY_25_CWS";
    @Autowired
    private MiGuKuaiYouVRJingMengApiProperties miGuKuaiYouVRJingMengApiProperties;
    @Autowired
    @Lazy
    private IMiGuKuaiYouVRJingMengService vrJingMengService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    ISubscribeService subscribeService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public Result<?> vrJingMengReadySub(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        final Result<Object> bizExistsResult = Result.bizExists("你已开通,请勿重复开通");
        final String mobile = subscribe.getMobile();

        //查询是否已开通了包月
        final FebsResponse febsResponse = vrJingMengService.vrJingMengGetUser(mobile,subscribe.getChannel());
        //状态为"200"表示已有包月
        if(febsResponse.isOK()){
            return bizExistsResult;
        }
        FebsResponse token=vrJingMengService.vrJingMengGetToken(mobile,subscribe.getChannel());
        if(!token.isOK()){
            return errorResult;
        }
        FebsResponse fee=vrJingMengService.vrJingMengFee(mobile,token.get("data").toString());
        if(fee.isOK()) {
            return Result.ok(fee.get("data"));
        }else {
            return errorResult;
        }
    }

    /**
     * 查询是否订购vr竞盟
     * @param mobile
     * @param channelCode
     * @return
     */
    @Override
    public FebsResponse vrJingMengGetUser(String mobile,String channelCode){
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getChannel, channelCode).eq(Subscribe::getStatus,BizConstant.SUBSCRIBE_STATUS_SUCCESS).select(Subscribe::getMobile, Subscribe::getServiceId, Subscribe::getChannel, Subscribe::getExtra).orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(subscribe!=null){
            return this.vrJingMengGetUser(subscribe);
        }else{
            return new FebsResponse().fail().message("未订购产品");
        }

    }

    /**
     * 查询是否订购vr竞盟
     * @param subscribe
     * @return
     */
    @Override
    public FebsResponse vrJingMengGetUser(Subscribe subscribe){
        String mobile = subscribe.getMobile();
        String ourChannel = subscribe.getChannel();
        KuaiYouVRJingMengChannel kuaiYouVRJingMengChannel=miGuKuaiYouVRJingMengApiProperties.getChannelMap().get(ourChannel);
        if(kuaiYouVRJingMengChannel==null){
            return new FebsResponse().fail().message("系统异常，请稍后重试");
        }
        //判断是否老渠道号
        final boolean hasChannelProduct = StringUtils.isNotEmpty(subscribe.getServiceId()) && StringUtils.isNotEmpty(subscribe.getExtra());
        final String vrChannelCode = hasChannelProduct ? subscribe.getChannel(): VRJINGMENG_CHANNEL_OLD;
        final String productId = hasChannelProduct ? subscribe.getExtra() : kuaiYouVRJingMengChannel.getProductId();;

        try {

            FebsResponse tokenFebs=vrJingMengService.vrJingMengGetToken(mobile,vrChannelCode);
            if(!tokenFebs.isOK()){
                return new FebsResponse().fail().message("系统异常，请稍后重试");
            }

            MiGuKuaiYouVRJingMengGetUserResult miGuKuaiYouVRJingMengGetUser=vrJingMengService.miGuKuaiYouVRJingMengGetUser(mobile,tokenFebs.get("data").toString());
            if(miGuKuaiYouVRJingMengGetUser==null || !miGuKuaiYouVRJingMengGetUser.isOK() || !miGuKuaiYouVRJingMengGetUser.getResult().isOK()){
                return new FebsResponse().fail().message("系统异常，请稍后重试");
            }
            Optional<MiGuKuaiYouVRJingMengGetUserResult.Data> data=miGuKuaiYouVRJingMengGetUser.getData().stream().filter(item-> productId.equals(item.getProduct())).collect(Collectors.toList()).stream().max(Comparator.comparing( MiGuKuaiYouVRJingMengGetUserResult.Data::getProduct));
            if(data==null || !data.isPresent()){
                return new FebsResponse().fail().message("未订购产品");
            }
            String isLegalUser=data.get().getIsLegalUser();
            Integer status=data.get().getStatus();
            String product =data.get().getProduct();
            String msg = miGuKuaiYouVRJingMengGetUser.getResult().getMsg();
            if(status.equals(100) && "true".equals(isLegalUser)){
                return new FebsResponse().success().data(product).message(msg);
            }
            return new FebsResponse().fail().message(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail().message("系统异常，请稍后重试");
    }


    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_KUAIYOU_VR_TOKEN,key = "#mobile + '-' + #channel",unless = "#result==null")
    public  FebsResponse vrJingMengGetToken(String mobile,String channel){
        KuaiYouVRJingMengChannel huYuChannel=miGuKuaiYouVRJingMengApiProperties.getChannelMap().get(channel);
        if(huYuChannel==null){
            return new FebsResponse().fail().message("系统异常，请稍后重试");
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("appId",miGuKuaiYouVRJingMengApiProperties.getAppId());
        map.put("appSecret",miGuKuaiYouVRJingMengApiProperties.getAppSecret());
        map.put("timestamp",System.currentTimeMillis());
        map.put("mobile",mobile);
        map.put("channel",huYuChannel.getChannel());
        String sign= RSA256Utils.generateSignSort(map,miGuKuaiYouVRJingMengApiProperties.getAppSecret());
        map.put("sign",sign);
        try {
            String content=implementHttpPostResult(miGuKuaiYouVRJingMengApiProperties.getTokenUrl(),mapper.writeValueAsString(map),"VR竞盟获取token令牌接口");
            if(StringUtils.isBlank(content)){
                return new FebsResponse().fail().message("系统异常，请稍后重试");
            }
            JsonNode tree = mapper.readTree(content);
            String token = tree.at("/data/token").asText();
            String code = tree.at("/result/code").asText();
            String msg = tree.at("/result/msg").asText();
            return new FebsResponse().code(code).message(msg).data(token);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail().message("系统异常，请稍后重试");
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_KUAIYOU_VR_PAY_URL,key = "#mobile + '-' + #token",unless = "#result==null")
    public FebsResponse vrJingMengFee(String mobile,String token){
        try {
            String content=implementHttpPostResult(miGuKuaiYouVRJingMengApiProperties.getFeeUrl()+token,null,"VR竞盟获取计费地址",mobile);
            if(StringUtils.isBlank(content)){
                return new FebsResponse().fail().message("系统异常，请稍后重试");
            }
            JsonNode tree = mapper.readTree(content);
            String url = tree.at("/data/url").asText();
            String code = tree.at("/result/code").asText();
            String msg = tree.at("/result/msg").asText();
            return new FebsResponse().code(code).message(msg).data(url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail().message("系统异常，请稍后重试");
    }

    /**
     * 发起http请求get
     */
    private String implementHttpGetResult(String url,String mobile,String msg) {
        return pushGet(url,mobile,msg);
    }

    private String pushGet(String url,String mobile,String msg) {
        log.info(msg+",请求数据=>手机号:{},地址:{}",mobile,url);
        Request request = new Request.Builder().url(url).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},响应参数:{}",mobile,url,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{}",url,e);
            return null;
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_KUAIYOU_VR_USER,key = "#mobile + '-' + #token",unless = "#result==null")
    public MiGuKuaiYouVRJingMengGetUserResult miGuKuaiYouVRJingMengGetUser(String mobile, String token){
        try {
            String content=implementHttpGetResult(miGuKuaiYouVRJingMengApiProperties.getUserUrl()+ token, mobile,"获取VR会员用户数据接口");
            if(StringUtils.isBlank(content)){
                return null;
            }
            MiGuKuaiYouVRJingMengGetUserResult miGuHuYuGetUserResult= mapper.readValue(content, MiGuKuaiYouVRJingMengGetUserResult.class);
            return miGuHuYuGetUserResult;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 发起http请求post
     */
    private String implementHttpPostResult(String url,String raw,String msg) {
        return pushPost(url, raw,msg);
    }
    private String pushPost(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }

    /**
     * 发起http请求post
     */
    private String implementHttpPostResult(String url,String raw,String msg,String mobile) {
        return pushPost(url, raw,msg,mobile);
    }
    private String pushPost(String url,String raw,String msg,String mobile) {
        log.info(msg+",请求数据=>地址:{},请求参数:{},手机号:{}",url,raw,mobile);
        RequestBody body = RequestBody.create(null, "");
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},手机号:{},响应参数:{}",url,raw,mobile,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }
}

