package com.eleven.cms.service;

import com.eleven.cms.entity.VrbtAppUserOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 视频彩铃app用户订单
 * @Author: jeecg-boot
 * @Date:   2024-04-16
 * @Version: V1.0
 */
public interface IVrbtAppUserOrderService extends IService<VrbtAppUserOrder> {

    Result<?> pointsUse(String mobile, String consigneeMobile, String consigneeName, String address, String productName);

    Result<?> orderList(String mobile);

    Result<?> rechargeList(String mobile);

    Result<?> rechargeVrbt(String mobile);

    Result<?> queryVrbt(String mobile);
}
