package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.HenanYidongGaojieService;
import com.eleven.cms.remote.HenanYidongService;
import com.eleven.cms.remote.IHenanYidongService;
import com.eleven.cms.remote.ZhongcaileyangService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.HenanMobileCheckResult;
import com.eleven.cms.vo.HenanMobileMarketResult;
import com.eleven.cms.vo.HenanMobileSmsResult;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("henanYidongVrbtCommonService")
@Slf4j
public class HenanYidongVrbtCommonServiceImpl implements IBizCommonService {

    private static final Interner<String> interner = Interners.newWeakInterner();

    @Autowired
    HenanYidongService henanYidongService;
    @Autowired
    HenanYidongGaojieService henanYidongGaojieService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ZhongcaileyangService zhongcaileyangService;

    @Override
    @ValidationLimit
    public Result filerCheck(Subscribe subscribe) {
        //中财乐扬重复订购校验
        if (ZYCL_CHANNEL_LIST.contains(subscribe.getChannel())) {
            boolean orderStatus = zhongcaileyangService.queryOrder(subscribe.getChannel(), subscribe.getMobile());
            if (orderStatus) {
                return Result.msgDuplicateLimit();
            }
        }
        return Result.ok();
    }


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        //中财乐扬重复订购校验
        if (ZYCL_CHANNEL_LIST.contains(subscribe.getChannel())) {
            boolean orderStatus = zhongcaileyangService.queryOrder(subscribe.getChannel(), subscribe.getMobile());
            if (orderStatus) {
                return Result.msgDuplicateLimit();
            }
        }
        HenanMobileSmsResult getSmsResult = null;
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobile = subscribe.getMobile();
        String channel = subscribe.getChannel();
        IHenanYidongService service = channel.startsWith("HNYZ_") ? henanYidongGaojieService : henanYidongService;
        //调用检验接口校验用户开通资格
        synchronized (interner.intern(mobile)) {
            HenanMobileMarketResult henanMobileMarketResult = service.queryMarket(mobile, subscribe.getChannel());
            if(!(henanMobileMarketResult.isOK())){
//                return Result.error("暂无订购资格，请稍后再试!");
                String errorMsg="{\"code\":\""+henanMobileMarketResult.getCode()+"\",\"message\":\""+henanMobileMarketResult.getMessage()+"\"}";
                try {
                    return Result.errorNotSubMsg(errorMsg);
                } catch (Exception e) {
                    log.error("鉴权异常!-subscribe:{}",subscribe, e);
                    return Result.msgNotSubError();
                }
            }
            if(BIZ_TYPE_HENYD_YR_MGB.equals(subscribe.getChannel())){
                HenanMobileCheckResult henanMobileCheckResult = service.queryLlbActive(mobile,subscribe.getChannel());
                if(!(henanMobileCheckResult.isOK())){
//                    return Result.error("暂无订购资格，请稍后再试!");
                    String errorMsg="{\"code\":\""+henanMobileCheckResult.getCode()+"\",\"message\":\""+henanMobileCheckResult.getMessage()+"\"}";
                    try {
                        return Result.errorNotSubMsg(errorMsg);
                    } catch (Exception e) {
                        log.error("鉴权异常!-subscribe:{}",subscribe, e);
                        return Result.msgNotSubError();
                    }
                }
                getSmsResult = service.sendLlbSms(mobile, subscribe.getChannel());
            }else{
                HenanMobileCheckResult henanMobileCheckResult = service.queryActive(mobile,subscribe.getChannel());
                if(!(henanMobileCheckResult.isOK())){
//                    return Result.error("暂无订购资格，请稍后再试!");

                    String errorMsg="{\"code\":\""+henanMobileCheckResult.getCode()+"\",\"message\":\""+henanMobileCheckResult.getMessage()+"\"}";
                    try {
                        return Result.errorNotSubMsg(errorMsg);
                    } catch (Exception e) {
                        log.error("鉴权异常!-subscribe:{}",subscribe, e);
                        return Result.msgNotSubError();
                    }
                }
                getSmsResult = service.sendSms(mobile, subscribe.getChannel());
            }
            if (getSmsResult.isOK()) {
                subscribe.setResult("获取验证码成功");
                subscribe.setIspOrderNo(getSmsResult.getData().getOrderId());
                subscribeService.createSubscribeDbAndEs(subscribe);
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribe.setResult(getSmsResult.getMessage());
                subscribeService.createSubscribeDbAndEs(subscribe);
//                return Result.error("获取验证码失败");

                try {
                    String errorMsg="{\"code\":\""+getSmsResult.getCode()+"\",\"message\":\""+getSmsResult.getMessage()+"\"}";
                    return Result.errorSmsMsg(errorMsg);
                } catch (Exception e) {
                    log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                    return Result.msgSmsError();
                }
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        String channel = subscribe.getChannel();
        IHenanYidongService service = channel.startsWith("HNYZ_") ? henanYidongGaojieService : henanYidongService;
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        HenanMobileSmsResult smsCodeResult = service.smsCode(mobile,subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(),subscribe.getReferer());
        if (smsCodeResult.isOK()) {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setResult("短信验证码提交成功");
            subscribeService.updateSubscribeDbAndEs(upd);
            rabbitMQMsgSender.sendSubscribeResultJudgeDelay20minuteMessage(DelayedMessage.builder().id(subscribe.getId()).msg("10分钟延迟开通结果判定消息").build());
            if(BIZ_TYPE_HNYZ_MGYD_DJJP.equals(channel) || BIZ_TYPE_HENYD_YR_DJB.equals(channel) || BIZ_TYPE_HENYD_YR_5GXTH.equals(channel)){
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            }
            return Result.ok("短信验证码提交成功");
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(smsCodeResult.getMessage());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error(500, "订阅失败");
        }
    }
}
