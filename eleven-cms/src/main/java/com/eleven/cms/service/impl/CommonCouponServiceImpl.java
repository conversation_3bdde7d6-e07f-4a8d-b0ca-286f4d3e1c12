package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.NanshanLianlianProperties;
import com.eleven.cms.dto.LianlianNanshanOpenResponse;
import com.eleven.cms.dto.NanshanOpenNotify;
import com.eleven.cms.dto.NanshanOpenResponse;
import com.eleven.cms.entity.CommonCoupon;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.CommonCouponMapper;
import com.eleven.cms.queue.LianLianSendCodeDeductMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.service.ICommonCouponService;
import com.eleven.cms.service.ISmsModelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: cms_common_coupon
 * @Author: jeecg-boot
 * @Date:   2024-07-04
 * @Version: V1.0
 */
@Service
@Log4j2
public class CommonCouponServiceImpl extends ServiceImpl<CommonCouponMapper, CommonCoupon> implements ICommonCouponService {
    public static final Integer SUB_DAY = 31;//包月天数
    private static final String ERROR_CODE_SUCCESS = "200"; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String ERROR_CODE_SUCCESS_MSG ="处理成功"; //响应描述
    private static final String ERROR_CODE_STATUS_OK ="OK"; //响应状态

    private static final String ERROR_CODE_FAIL = "500"; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String ERROR_CODE_SIGN_FAIL_MSG ="验签失败"; //响应描述
    private static final String REPEAT_ORDER_MSG ="订单号重复"; //响应描述
    private static final String REPEAT_SUB_MSG ="当月重复通知"; //响应描述
    private static final String ERROR_CODE_FAIL_MSG ="系统错误"; //响应描述
    private static final String ERROR_CODE_STATUS_FAIL ="ERROR"; //响应状态

    //卡券类型 0：二维码；1：条形码；2：条形码和二维码；3：卡券URL地址；4：只包含密码；5：卡号和密码
    private static final String CARD_TYPE = "4";
    private static final String VERSION = "v1.0.0";

    private static final String COUPON_STATUS_INIT = "0";//待核销
    private static final String COUPON_STATUS_SUCCESS = "1"; //已核销
    private static final String COUPON_STATUS_FAIL = "2"; //撤回核销
    private static final String NOT_COUPON_STATUS = "3"; //不进行核销

    private static final String ORDER_STATUS_SUCCESS = "2"; //成功
    private static final String ORDER_STATUS_FAIL = "-1"; //失败

    @Autowired
    private NanshanLianlianProperties nanshanLianlianProperties;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    private ISmsModelService smsModelService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public CommonCoupon sendActiveCode(String mobile, String channel, String orderId,String dataLinkId,String notifyUrl,int num) {
        CommonCoupon commonCoupon = new CommonCoupon();
        commonCoupon.setMobile(mobile);
        commonCoupon.setChannel(channel);
        commonCoupon.setOrderId(orderId);
        commonCoupon.setDataLinkId(dataLinkId);
        commonCoupon.setNotifyUrl(notifyUrl);
        commonCoupon.setInvalidTime(DateUtil.localDateTimeToDate(LocalDateTime.now().plusDays(SUB_DAY)));
        commonCoupon.setNum(num);
        //查询上月是否发送券码
        LocalDateTime start = LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        LocalDateTime end=    LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        Integer renewalCount=this.lambdaQuery().eq(CommonCoupon::getMobile,mobile).between(CommonCoupon::getCreateTime,start,end).count();
        if(renewalCount>0){
            //2续订
            commonCoupon.setNextMonthSend(2);
        }else{
            //查询当月是否订购 1已订购 0 未订购
            LocalDateTime subStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
            LocalDateTime subEnd=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
            boolean sub=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getChannel,channel).eq(Subscribe::getStatus,BizConstant.SUBSCRIBE_STATUS_SUCCESS).between(Subscribe::getCreateTime,subStart,subEnd).count()>0;
            commonCoupon.setNextMonthSend(sub?1:0);
        }
        int success = 0;
        do {
            try {
                String couponCode = RandomStringUtils.randomAlphanumeric(16);
                commonCoupon.setCouponCode(couponCode);
                this.save(commonCoupon);
                success++;
            } catch (Exception e) {
                if(e.getCause().getMessage().contains(commonCoupon.getCouponCode())){
                    log.error("收到订单通知,手机号:{},订单id:{},跳过重复订单保存",mobile,commonCoupon.getCouponCode());
                }
            }
        } while (success < 1);
        return commonCoupon;
    }
//
//    @Override
//    public void addCouponTask() {
//        //查询上月未退订数据
//        List<CommonCoupon> list = this.lambdaQuery()
//                .le(CommonCoupon::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(LocalDateTime.now().minusMonths(1)))
//                .ge(CommonCoupon::getCreateTime, DateUtil.getLastDayOfMonthWithMaxTime(LocalDateTime.now().minusMonths(1)))
//                .eq(CommonCoupon::getNextMonthSend,0)
//                .list();
//        for (CommonCoupon commonCoupon : list) {
//            //查询包月状态
//
////            this.sendActiveCode(commonCoupon.getMobile(), commonCoupon.getChannel(),commonCoupon.getOrderId());
//            commonCoupon.setNextMonthSend(1);
//            this.updateById(commonCoupon);
//        }
//    }


    @Override
    public LianlianNanshanOpenResponse lianlianNanshanOpenNotify(String requestBody, String sign, String appKey) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(requestBody);
            Map<String, Object> params = jsonObject.toJavaObject(Map.class);
            //使用Stream进行排序
            params = params.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new));
            // 将Map转换为JSON字符串
            requestBody = mapper.writeValueAsString(params);
            if(!SignatureUtils.checkSign(requestBody,sign,nanshanLianlianProperties.getPublicKey())){
                log.error("南山下发联联权益通知-验签失败--->参数:{},sign:{},appKey:{}",requestBody,sign,appKey);
                return new LianlianNanshanOpenResponse(ERROR_CODE_FAIL,ERROR_CODE_STATUS_FAIL,ERROR_CODE_SIGN_FAIL_MSG,null);
            }

            final NanshanOpenNotify nanshanOpenNotify = new ObjectMapper().readValue(requestBody, NanshanOpenNotify.class);
            //查询订单号是否重复
            boolean repeatOrder=this.lambdaQuery().eq(CommonCoupon::getOrderId,nanshanOpenNotify.getOrderNo()).count()>0;
            if(repeatOrder){
                log.error("南山下发联联权益通知-订单号重复--->参数:{}",nanshanOpenNotify);
                return new LianlianNanshanOpenResponse(ERROR_CODE_FAIL,ERROR_CODE_STATUS_FAIL,REPEAT_ORDER_MSG,null);
            }
            //查询当月是否重复通知
            LocalDateTime subStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
            LocalDateTime subEnd=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
            boolean repeatSub=this.lambdaQuery().eq(CommonCoupon::getMobile,nanshanOpenNotify.getPhone()).between(CommonCoupon::getCreateTime,subStart,subEnd).count()>0;
            if(repeatSub){
                log.error("南山下发联联权益通知-当月重复通知--->参数:{}",nanshanOpenNotify);
                return new LianlianNanshanOpenResponse(ERROR_CODE_FAIL,ERROR_CODE_STATUS_FAIL,REPEAT_SUB_MSG,null);
            }
            CommonCoupon commonCoupon=this.sendActiveCode(nanshanOpenNotify.getPhone(),nanshanOpenNotify.getGoodsNo(),nanshanOpenNotify.getOrderNo(),nanshanOpenNotify.getDataLinkId(),nanshanOpenNotify.getNotifyUrl(),Integer.valueOf(StringUtils.isNotBlank(nanshanOpenNotify.getNum())?nanshanOpenNotify.getNum():"0"));
            rabbitMQMsgSender.lianlianSendCodeQueueMessage(LianLianSendCodeDeductMessage.builder().id(commonCoupon.getId()).mobile(nanshanOpenNotify.getPhone()).build());
            ObjectNode orderInfo =mapper.createObjectNode();
            orderInfo.put("orderNo",commonCoupon.getOrderId());
            orderInfo.put("status",ORDER_STATUS_SUCCESS);
            return new LianlianNanshanOpenResponse(ERROR_CODE_SUCCESS,ERROR_CODE_STATUS_OK,ERROR_CODE_SUCCESS_MSG,orderInfo);
        } catch (Exception e) {
            log.error("南山下发联联权益通知-发码失败--->参数:{},sign:{},appKey:{}",requestBody,sign,appKey,e);
            return new LianlianNanshanOpenResponse(ERROR_CODE_FAIL,ERROR_CODE_STATUS_FAIL,ERROR_CODE_FAIL_MSG,null);
        }
    }



    @Override
    public void sendCodeScheduleDeduct(String id) {
        CommonCoupon commonCoupon=this.baseMapper.selectById(id);
        if(commonCoupon==null){
            log.error("联联权益发送券码消息队列接收消息-激活码查询失败--->id:{}",id);
            return;
        }
        if(StringUtils.isNotBlank(commonCoupon.getMobile()) && StringUtils.isNotBlank(commonCoupon.getCouponCode())){
            smsModelService.sendSms(commonCoupon.getMobile(),commonCoupon.getChannel(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID,BizConstant.BUSINESS_TYPE_CODE,commonCoupon.getCouponCode());
        }
        String nonce= IdWorker.get32UUID().toUpperCase().substring(0, 16);
        String timestamp=DateUtil.formatForMiguGroupApi(LocalDateTime.now());
        ObjectNode dataNode =mapper.createObjectNode();
        dataNode.put("dataLinkId", commonCoupon.getDataLinkId());
        ObjectNode notifyData =mapper.createObjectNode();
        ObjectNode ciphertextObject =mapper.createObjectNode();
        ObjectNode orderInfo =mapper.createObjectNode();
        orderInfo.put("orderNo",commonCoupon.getOrderId());
        orderInfo.put("status",ORDER_STATUS_SUCCESS);
        List<Map<String,String>>couponInfos= Lists.newArrayList();
        Map<String,String> coupon = Maps.newHashMap();
        coupon.put("cardType",CARD_TYPE);
        coupon.put("coupon",commonCoupon.getCouponCode());
        coupon.put("couponStatus",COUPON_STATUS_INIT);
        coupon.put("modTime",timestamp);
        couponInfos.add(coupon);
        orderInfo.putPOJO("couponInfos",couponInfos);
        ciphertextObject.putPOJO("data",orderInfo);
        ciphertextObject.put("errorCode",ERROR_CODE_SUCCESS);
        ciphertextObject.put("errorMessage",ERROR_CODE_SUCCESS_MSG);
        ciphertextObject.put("status",ERROR_CODE_STATUS_OK);
        String ciphertext=this.decryptECBNoPadding(ciphertextObject.toString(),nanshanLianlianProperties.getCallbackKey(),nonce);
        notifyData.put("ciphertext",ciphertext);
        notifyData.put("nonce",  nonce);
        dataNode.putPOJO("notifyData", notifyData);
        dataNode.put("timestamp",timestamp);
        dataNode.put("version", VERSION);
        String sign=SignatureUtils.sign(dataNode.toString(),nanshanLianlianProperties.getCallbackPrivateKey());
        log.info("南山联联权益交易状态回调-请求数据->手机号:{},请求参数:{}",commonCoupon.getMobile(),dataNode.toString());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder().url(commonCoupon.getNotifyUrl()).post(body).addHeader("sign",sign).addHeader("appKey",nanshanLianlianProperties.getAppKey()).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            commonCoupon.setRemark(content);
            commonCoupon.setExchangeTime(new Date());
            this.baseMapper.updateById(commonCoupon);
            log.info("南山联联权益交易状态回调-响应数据=>手机号:{},地址:{},响应参数:{}",commonCoupon.getMobile(),dataNode.toString(),content);
        } catch (Exception e) {
            log.error("南山联联权益交易状态回调-请求异常=>手机号:{},请求参数:{}",commonCoupon.getMobile(),dataNode.toString(),e);
        }
    }


    /**
     * 加密
     */
    private String decryptECBNoPadding(String notifyData,String key,String nonce){
        byte[] bytes = Sm4Util.encryptCBCPadding(key.getBytes(StandardCharsets.UTF_8),
                nonce.getBytes(StandardCharsets.UTF_8),
                notifyData.getBytes(StandardCharsets.UTF_8));
        String ciphertext =new String(Base64.getEncoder().encode( bytes), StandardCharsets.UTF_8);
        return ciphertext;
    }




    @Override
    public void lianlianSendCode(String couponCode) {
        CommonCoupon commonCoupon=this.lambdaQuery().eq(CommonCoupon::getCouponCode, couponCode).orderByDesc(CommonCoupon::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(commonCoupon==null){
            log.error("南山联联权益充值-激活码查询失败--->code:{}",couponCode);
            return;
        }
        String nonce= IdWorker.get32UUID().toUpperCase().substring(0, 16);
        String timestamp=DateUtil.formatForMiguGroupApi(LocalDateTime.now());
        ObjectNode dataNode =mapper.createObjectNode();
        dataNode.put("dataLinkId", commonCoupon.getDataLinkId());
        ObjectNode notifyData =mapper.createObjectNode();
        ObjectNode ciphertextObject =mapper.createObjectNode();
        ObjectNode orderInfo =mapper.createObjectNode();
        orderInfo.put("orderNo",commonCoupon.getOrderId());
        orderInfo.put("status",ORDER_STATUS_SUCCESS);
        List<Map<String,String>>couponInfos= Lists.newArrayList();
        Map<String,String> coupon = Maps.newHashMap();
        coupon.put("couponStatus",COUPON_STATUS_SUCCESS);
        coupon.put("cardType",CARD_TYPE);
        coupon.put("coupon",commonCoupon.getCouponCode());
        coupon.put("modTime",timestamp);
        couponInfos.add(coupon);
        orderInfo.putPOJO("couponInfos",couponInfos);
        ciphertextObject.putPOJO("data",orderInfo);
        ciphertextObject.put("errorCode",ERROR_CODE_SUCCESS);
        ciphertextObject.put("errorMessage",ERROR_CODE_SUCCESS_MSG);
        ciphertextObject.put("status",ERROR_CODE_STATUS_OK);
        String ciphertext=this.decryptECBNoPadding(ciphertextObject.toString(),nanshanLianlianProperties.getCallbackKey(),nonce);
        notifyData.put("ciphertext",ciphertext);
        notifyData.put("nonce",  nonce);
        dataNode.putPOJO("notifyData", notifyData);
        dataNode.put("timestamp",timestamp);
        dataNode.put("version", VERSION);
        String sign=SignatureUtils.sign(dataNode.toString(),nanshanLianlianProperties.getCallbackPrivateKey());
        log.info("南山联联权益充值交易状态回调-请求数据->手机号:{},请求参数:{}",commonCoupon.getMobile(),dataNode.toString());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder().url(commonCoupon.getNotifyUrl()).post(body).addHeader("sign",sign).addHeader("appKey",nanshanLianlianProperties.getAppKey()).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            commonCoupon.setExtra(content);
            this.baseMapper.updateById(commonCoupon);
            log.info("南山联联权益充值交易状态回调-响应数据=>手机号:{},地址:{},响应参数:{}",commonCoupon.getMobile(),dataNode.toString(),content);
        } catch (Exception e) {
            log.error("南山联联权益充值交易状态回调-请求异常=>手机号:{},请求参数:{}",commonCoupon.getMobile(),dataNode.toString(),e);
        }
    }
}
