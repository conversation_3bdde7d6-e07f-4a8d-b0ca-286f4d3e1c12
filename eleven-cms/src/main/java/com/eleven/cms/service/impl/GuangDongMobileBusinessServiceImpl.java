package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizCommonConstant.MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS;
import static com.eleven.cms.util.BizCommonConstant.MOBILE_MONTH_EXISTS_VALUE;
import static com.eleven.cms.util.BizConstant.*;

/**
 * 广东移动存量业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/26 16:52
 **/
@Slf4j
@Service
public class GuangDongMobileBusinessServiceImpl implements IBusinessCommonService {
    public static final String YIDONG_GUANGDONG_STOCK_DUPLICATE_KEY_PREFIX = "gdyd::stock:";
    public static final long YIDONG_GUANGDONG_STOCK_SMS_INVALID_CACHE_SECONDS = 60;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IGuangDongMobileService guangDongMobileService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }


//        //同一手机号每月只能开通一次
//        Integer subscribeSuccessCount = subscribeService.lambdaQuery()
//                .eq(Subscribe::getMobile, subscribe.getMobile())
//                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
//                .eq(Subscribe::getChannel,BIZ_CHANNEL_GDYD)
//                .between(Subscribe::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
//                .count();
//        if(subscribeSuccessCount>0){
//            final Result<Object> bizExistsResult = Result.bizExists("你已开通过,请勿重复开通");
//            return bizExistsResult;
//        }

        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        Result result = SpringContextUtils.getBean(GuangDongMobileBusinessServiceImpl.class).receiveOrder(subscribe);
        return result;
    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }

            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = YIDONG_GUANGDONG_STOCK_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", YIDONG_GUANGDONG_STOCK_SMS_INVALID_CACHE_SECONDS);

            Result<?> result = guangDongMobileService.submitSms(subscribe.getMobile(),smsCode,target.getIspOrderNo(),target.getExtra(),subscribe.getChannel());
            Subscribe upd = new Subscribe();
            upd.setId(transactionId);
            upd.setResult(result.getMessage());
            upd.setModifyTime(new Date());
            if (result.isOK()) {
                redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setExtra(result.getResult().toString());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(target.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(target, SUBSCRIBE_STATUS_SUCCESS);
                }


                //写入自增序列
                subscribeService.saveChannelLimit(target);
//
//                subscribeService.incrChannelProvinceLimit(target.getChannel(), target.getProvince());
//                if (outsideConfigService.isOutsideChannel(target.getSubChannel())) {
//                    subscribeService.incrChannelProvinceOwnerLimit(target.getChannel(), target.getProvince(), CHANNEL_OWNER_CPA);
//                } else {
//                    String owner = channelOwnerService.queryOwner(target.getSubChannel());
//                    if (StringUtils.isNotBlank(owner)) {
//                        subscribeService.incrChannelProvinceOwnerLimit(target.getChannel(), target.getProvince(), owner);
//                    }
//                }
            } else {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribeService.updateSubscribeDbAndEs(upd);
            }
            //外部渠道加入回调延迟队列(暂时不使用队列)
            if (outsideConfigService.isOutsideChannel(target.getSubChannel())) {
                final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(target.getId(), 0, "回调通知");
                rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
            }
            return result;



        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        Result<?> result=guangDongMobileService.sendSms(subscribe.getMobile(),subscribe.getChannel());
        if (result != null && result.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setIspOrderNo(result.getOrderId());
            upd.setExtra(result.getNumberId());
            subscribeService.updateSubscribeDbAndEs(upd);

            Result resultOrder=new Result();
            resultOrder.setMessage("验证码已发送");
            resultOrder.setOrderId(result.getOrderId());
            resultOrder.setNumberId(result.getNumberId());
            resultOrder.setCode(CommonConstant.SC_JEECG_NO_AUTH);
            resultOrder.setResult(subscribe.getId());
            return resultOrder;
        }
        return result;
    }
}
