package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ColumnMusic;
import com.eleven.cms.entity.ColumnMusicVo;
import com.eleven.cms.entity.Music;
import com.eleven.cms.mapper.ColumnMusicMapper;
import com.eleven.cms.mapper.MusicMapper;
import com.eleven.cms.service.IColumnMusicService;
import com.eleven.cms.vo.MusicVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 栏目歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
@Service
@Slf4j
public class ColumnMusicServiceImpl extends ServiceImpl<ColumnMusicMapper, ColumnMusic> implements IColumnMusicService {
	
	@Autowired
	private ColumnMusicMapper columnMusicMapper;
	@Autowired
	private MusicMapper musicMapper;
	
	@Override
	public List<ColumnMusic> selectByMainId(String mainId) {
		return columnMusicMapper.selectByMainId(mainId);
	}

    @Cacheable(cacheNames = CacheConstant.CMS_COLUMN_CACHE,key = "#columnId+'-'+#page.getCurrent()+'_'+#page.getSize()",unless = "#result==null")
    @Override
    public IPage<MusicVo> selectColumnMusicVoPage(Page<MusicVo> page, String columnId) {
        // 不进行 count sql 优化，解决 MP 无法自动优化 SQL 问题，这时候你需要自己查询 count 部分
        // page.setOptimizeCountSql(false);
        // 当 total 为小于 0 或者设置 setSearchCount(false) 分页插件不会进行 count 查询
        // 要点!! 分页返回的对象与传入的对象是同一个
        return columnMusicMapper.selectColumnMusicVoPage(page, columnId);
    }

	@Override
	public List<ColumnMusicVo> selectByMainIdNew(String id) {
		return columnMusicMapper.selectByMainIdNew(id);
	}

	@Override
	public Music selectByCopyrightId(String copyrightId) {
		QueryWrapper<Music> queryWrapper = new QueryWrapper<>();
		Music music = new Music();
		music.setCopyrightId(copyrightId);
		queryWrapper.setEntity(music);
		return musicMapper.selectOne(queryWrapper);
	}

	@Override
	@Transactional
	public void importMusic(List<ColumnMusicVo> list,String id) {

		try{

			List<ColumnMusic> columnMusics = new ArrayList<>();
			List<String> ids = new ArrayList<>();

			//查询旧数据
			List<ColumnMusicVo> columnMusicList = this.selectByMainIdNew(id);
			if(columnMusicList != null && columnMusicList.size() > 0){
				for (ColumnMusicVo columnMusicVo : columnMusicList) {
					ids.add(columnMusicVo.getId());
				}
			}

			//版权id --> 歌曲id --> 删除 插入
			if(list != null && list.size() > 0){
				for (int i = 0; i < list.size(); i++) {
					if (StringUtils.isNotEmpty(list.get(i).getCopyrightId())){
						Music music = this.selectByCopyrightId(list.get(i).getCopyrightId());
						if(music != null){
							ColumnMusic columnMusic = new ColumnMusic();
							columnMusic.setColumnId(id);
							columnMusic.setMusicId(music.getId());
							columnMusic.setPriority(columnMusics.size() + 1);
							columnMusic.setTitle(music.getMusicName());
							columnMusic.setTag(list.get(i).getTag());
							columnMusic.setExtra(list.get(i).getExtra());
							columnMusics.add(columnMusic);
						}
					}
				}

			}
			if(columnMusics.size() > 0){
				//先刪除旧数据
				if(ids.size() > 0){
					this.removeByIds(ids);
				}
				boolean b = this.saveBatch(columnMusics);
				log.info(b == true ? "importMusic批量插入操作成功" : "importMusic批量插入操作失败");
			}
		}catch (Exception e){
			log.error("importMusic出错：",e);
		}
	}

    @Override
    public MusicVo selectRandomMusicByColumn() {
        return  columnMusicMapper.selectRandomMusicByColumn();
    }
}
