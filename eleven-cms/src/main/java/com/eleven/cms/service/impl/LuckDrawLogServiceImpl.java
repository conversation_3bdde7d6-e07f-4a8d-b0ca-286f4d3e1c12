package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.LuckDrawLog;
import com.eleven.cms.mapper.LuckDrawLogMapper;
import com.eleven.cms.service.ILuckDrawLogService;
import com.eleven.cms.vo.FebsResponse;
import org.springframework.stereotype.Service;

/**
 * @Description: 抽奖记录
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
@Service
public class LuckDrawLogServiceImpl extends ServiceImpl<LuckDrawLogMapper, LuckDrawLog> implements ILuckDrawLogService {

    @Override
    public FebsResponse alipayLuck(String mobile,String channel,String productName,String consignee,String shippingAddress,String detailAddress,String consigneeMobile,String prizeName,String prizeType,String luckDrawStatus) {
        Integer luckCount=this.lambdaQuery().eq(LuckDrawLog::getChannel,channel).eq(LuckDrawLog::getMobile,mobile).count();
        if(luckCount>0){
            return new FebsResponse().fail().message("已抽奖");
        }
        LuckDrawLog luckDrawLog=new LuckDrawLog();
        luckDrawLog.setMobile(mobile);
        luckDrawLog.setProductName(productName);
        luckDrawLog.setChannel(channel);
        luckDrawLog.setPrizeName(prizeName);
        luckDrawLog.setConsignee(consignee);
        luckDrawLog.setConsigneeMobile(consigneeMobile);
        luckDrawLog.setShippingAddress(shippingAddress);
        luckDrawLog.setDetailAddress(detailAddress);
        luckDrawLog.setPrizeType(prizeType);
        luckDrawLog.setLuckDrawStatus(luckDrawStatus);
        this.save(luckDrawLog);
        return new FebsResponse().success();
    }
}
