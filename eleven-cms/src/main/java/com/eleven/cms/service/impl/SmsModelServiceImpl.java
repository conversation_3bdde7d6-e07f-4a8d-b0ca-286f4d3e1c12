package com.eleven.cms.service.impl;

import com.aliyuncs.utils.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.SmsModel;
import com.eleven.cms.mapper.SmsModelMapper;
import com.eleven.cms.queue.MonthRenewSubSmsMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.ISmsModelService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.LogUtils;
import com.eleven.cms.vo.RemoteResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 业务短信模板
 * @Author: jeecg-boot
 * @Date:   2022-09-23
 * @Version: V1.0
 */
@Slf4j
@Service
public class SmsModelServiceImpl extends ServiceImpl<SmsModelMapper, SmsModel> implements ISmsModelService {
    public static final String MONTH_RENEW_SUB_INVALID_KEY_PREFIX = "month::renew::sub:";
    public static final long MONTH_RENEW_SUB_INVALID_CACHE_SECONDS =3600*24;
    //是否开启:0=否,1=是
    private static final Integer SMS_SWITCHS =1;
    //0=主要,1=短信验证码,2=权益短信
    private static final String SERVICE_TYPE ="0";

    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    @Lazy
    private ISmsModelService smsModelService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private BizProperties bizProperties;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_SMS_MODEL_CHANNEL_CACHE,key = "#channel + '-' + #serviceId + '-' + #serviceType",unless = "#result==null")
    public SmsModel getSmsModelbyChannel(String channel, String serviceId, String serviceType) {
        return this.lambdaQuery().eq(SmsModel::getChannel,channel)
                                 .eq(SmsModel::getServiceId, serviceId)
                                 .eq(SmsModel::getServiceType, serviceType)
                                 .eq(SmsModel::getSmsSwitchs, SMS_SWITCHS)
                                 .orderByDesc(SmsModel::getCreateTime)
                                 .last("limit 1").one();
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_SMS_MODEL_CACHE,key = "#serviceId",unless = "#result==null")
    public SmsModel getSmsModelbyServiceId(String serviceId) {
        return this.lambdaQuery().eq(SmsModel::getServiceId, serviceId)
                .eq(SmsModel::getServiceType, SERVICE_TYPE)
                .eq(SmsModel::getSmsSwitchs, SMS_SWITCHS)
                .orderByDesc(SmsModel::getCreateTime)
                .last("limit 1").one();
    }
    @Override
    public Boolean sendSms(String mobile,String channel, String serviceId, String serviceType) {
        SmsModel smsModel=null;
        if(StringUtils.isEmpty(channel)){
             smsModel=smsModelService.getSmsModelbyServiceId(serviceId);
        }else{
             smsModel=smsModelService.getSmsModelbyChannel(channel,serviceId,serviceType);
        }
        if(smsModel!=null){
            String template =smsModel.getSmsModel();
            log.info("手机号:{},短信签名:{}",mobile,smsModel.getServiceName());
            if(StringUtils.isNotEmpty(template)){
                if(smsModel.getDelayTime()!=null && smsModel.getDelayTime()!=0){
                    try {
                        TimeUnit.SECONDS.sleep(smsModel.getDelayTime());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                return datangSmsService.sendSms(mobile,template);
            }
        }
        return false;
    }

    @Override
    public Boolean sendSms(String mobile,String channel, String serviceId, String serviceType,String code) {
        SmsModel smsModel=null;
        if(StringUtils.isEmpty(channel)){
            smsModel=smsModelService.getSmsModelbyServiceId(serviceId);
        }else{
            smsModel=smsModelService.getSmsModelbyChannel(channel,serviceId,serviceType);
        }
        if(smsModel!=null){
            String template =smsModel.getSmsModel();
            String tplParamKey =smsModel.getSmsParameter();
            log.info("手机号:{},短信签名:{}",mobile,smsModel.getServiceName());
            if(StringUtils.isNotEmpty(template)){
                if(smsModel.getDelayTime()!=null && smsModel.getDelayTime()!=0){
                    try {
                        TimeUnit.SECONDS.sleep(smsModel.getDelayTime());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                return datangSmsService.sendSms(mobile,template,tplParamKey,code);
            }
        }
        return false;
    }


    @Override
    @Async
    public void sendSmsAsync(String mobile,String channel, String serviceId, String serviceType) {
        SmsModel smsModel=null;
        if(StringUtils.isEmpty(channel)){
            smsModel=smsModelService.getSmsModelbyServiceId(serviceId);
        }else{
            smsModel=smsModelService.getSmsModelbyChannel(channel,serviceId,serviceType);
        }
        if(smsModel!=null){
            String template =smsModel.getSmsModel();
            log.info("手机号:{},短信签名:{}",mobile,smsModel.getServiceName());
            if(StringUtils.isNotEmpty(template)){
                if(smsModel.getDelayTime()!=null && smsModel.getDelayTime()!=0){
                    try {
                        TimeUnit.SECONDS.sleep(smsModel.getDelayTime());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                datangSmsService.sendSms(mobile,template);
            }
        }
    }

    @Override
    @Async
    public void sendSmsAsync(String mobile,String channel, String serviceId, String serviceType,String code) {
        SmsModel smsModel=null;
        if(StringUtils.isEmpty(channel)){
            smsModel=smsModelService.getSmsModelbyServiceId(serviceId);
        }else{
            smsModel=smsModelService.getSmsModelbyChannel(channel,serviceId,serviceType);
        }
        if(smsModel!=null){
            String template =smsModel.getSmsModel();
            String tplParamKey =smsModel.getSmsParameter();
            log.info("手机号:{},短信签名:{}",mobile,smsModel.getServiceName());
            if(StringUtils.isNotEmpty(template)){
                if(smsModel.getDelayTime()!=null && smsModel.getDelayTime()!=0){
                    try {
                        TimeUnit.SECONDS.sleep(smsModel.getDelayTime());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                datangSmsService.sendSms(mobile,template,tplParamKey,code);
            }
        }
    }


    @Override
    public Boolean sendSms(String mobile,String channel, String serviceId, String serviceType,Map<String,String> smsMap) {
        SmsModel smsModel=null;
        if(StringUtils.isEmpty(channel)){
            smsModel=smsModelService.getSmsModelbyServiceId(serviceId);
        }else{
            smsModel=smsModelService.getSmsModelbyChannel(channel,serviceId,serviceType);
        }
        if(smsModel!=null){
            String template =smsModel.getSmsModel();
            log.info("手机号:{},短信签名:{}",mobile,smsModel.getServiceName());
            if(StringUtils.isNotEmpty(template)){
                if(smsModel.getDelayTime()!=null && smsModel.getDelayTime()!=0){
                    try {
                        TimeUnit.SECONDS.sleep(smsModel.getDelayTime());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                StringBuffer sb=new StringBuffer();
                String msgContent=template;
                if(smsMap!=null && !smsMap.isEmpty()){
                    for(Map.Entry<String, String> it : smsMap.entrySet()){
                        msgContent =msgContent.replace(it.getKey(), it.getValue());
                        sb.append(msgContent);
                    }
                }
                return datangSmsService.sendSms(mobile,msgContent.toString());
            }
        }
        return false;
    }


    /**
     * 包月续订短信消息队列
     * @param channel
     * @param mobile
     */
    @Transactional
    @Override
    public void monthRenewSubSmsMessage(String channel,String mobile,String id,String bizType){
        rabbitMQMsgSender.monthRenewSubSmsMessage(MonthRenewSubSmsMessage.builder().id(id).bizType(bizType).channel(channel).mobile(mobile).build());
    }

    /**
     * 发送包月续订短信
     * @param channel
     * @param mobile
     */
    @Override
    public void monthRenewSubSendSms(String channel,String mobile,String bizType){
        //避免相同的手机号和渠道号重复发送短信
        String monthRenewSubInvalidKey = MONTH_RENEW_SUB_INVALID_KEY_PREFIX + mobile+ "-" + channel;
        if (redisUtil.hasKey(monthRenewSubInvalidKey)) {
            return;
        }
        redisUtil.set(monthRenewSubInvalidKey, "monthRenewSub:", MONTH_RENEW_SUB_INVALID_CACHE_SECONDS);
        String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channel).getServiceId();
        if(BizConstant.BIZ_TYPE_CPMB.equals(bizType)){
            final RemoteResult remoteResult=miguApiService.cpmbQuery(mobile,channel);
            if(remoteResult.isCpmbMember()){
                smsModelService.sendSmsAsync(mobile,channel,serviceId,  BizConstant.BUSINESS_TYPE_RENEW);
            }
        }else if(BizConstant.BIZ_TYPE_BJHY.equals(bizType)){
            final RemoteResult remoteResult=miguApiService.bjhyQuery(mobile,channel);
            if(remoteResult.isBjhyMember()){
                smsModelService.sendSmsAsync(mobile,channel,serviceId,  BizConstant.BUSINESS_TYPE_RENEW);
            }
        }else if(BizConstant.BIZ_TYPE_SCH.equals(bizType)){
//            final RemoteResult remoteResult=miguApiService.bjhyQuery(mobile,channel);
            final RemoteResult schResult=miguApiService.schQuery(mobile,channel);
            if(schResult.isSchMember()){
//                smsModelService.sendSmsAsync(mobile,channel,serviceId,  BizConstant.BUSINESS_TYPE_RENEW);
                LogUtils.info(channel, "包月续订权益发放");
                miguApiService.specialProductSub(mobile,channel);
            }
        }else if(BizConstant.BIZ_TYPE_JUNBOLLB.equals(bizType)){
            final RemoteResult schResult=miguApiService.schQuery(mobile,MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP);
            if(schResult.isSchMember()){
                LogUtils.info(channel, "包月续订权益发放");
                miguApiService.specialProductSub(mobile,MiguApiService.BIZ_SCH_CHANNEL_CODE_YYCP_SXJB);
            }
        }else{
            log.info("包月续订短信-业务类型未配置=>手机号:{},渠道号:{},业务类型:{}", mobile, channel,bizType);
        }
    }
}
