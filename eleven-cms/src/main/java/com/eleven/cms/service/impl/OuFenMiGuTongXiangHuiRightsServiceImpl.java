package com.eleven.cms.service.impl;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.dto.Rights;
import com.eleven.cms.entity.BusinessChannelRights;
import com.eleven.cms.entity.BusinessPack;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.ProvinceRights;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 藕粉咪咕同享会10元包包月校验
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/27 11:07
 **/
@Slf4j
@Service
public class OuFenMiGuTongXiangHuiRightsServiceImpl implements IBusinessRightsSubService{
    private static final String LOG_TAG = "藕粉咪咕同享会10元包[包月校验]";
    private static final String LOG_TAG_ERROR = "藕粉咪咕同享会10元包[包月校验异常]";
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private IProvinceRightsService provinceRightsService;
    @Override
    public FebsResponse memberVerify(String mobile,String serviceId) {
        Boolean monthlyIsSub=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(!monthlyIsSub){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        return new FebsResponse().success();
    }

    @Override
    public FebsResponse rechargRecordVerify(String mobile,String serviceId) {
        //藕粉咪咕同享会10元包权益已全部发放
        Boolean monthlyIsRecharge=rightsSubService.monthlyIsRecharge(mobile,serviceId);
        if(!monthlyIsRecharge){
            return new FebsResponse().repeatRecharge();
        }

        return new FebsResponse().success();
    }

    /**
     * 当月是否订购
     * @param mobile
     * @param serviceId
     * @return
     */
    private Boolean monthlyIsSub(String mobile, String serviceId) {
        try {
//            final List<String> channelList = bizProperties.getChannelByServiceId(serviceId);

            //业务类型去重
            final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().eq(BusinessPack::getServiceId,serviceId).select(BusinessPack::getBusinessId).list();
            if(businessPackList==null || businessPackList.isEmpty()){
                log.info("{}-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{}",LOG_TAG,mobile,serviceId);
                return false;
            }
            final List<String> businessIdList=businessPackList.stream().map(BusinessPack::getBusinessId).collect(Collectors.toList());
            final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getId,businessIdList).select(BusinessChannelRights::getBusinessChannel).list();
            if(businessList==null || businessList.isEmpty()){
                log.info("{}-指定渠道权益未正确配置=>手机号:{},权益领取业务ID:{}",LOG_TAG,mobile,serviceId);
                return false;
            }
            final List<String> channelList=businessList.stream().map(BusinessChannelRights::getBusinessChannel).distinct().collect(Collectors.toList());
            for (String channel:channelList) {
                ProvinceRights provinceRights=provinceRightsService.lambdaQuery().select(ProvinceRights::getChannel).eq(ProvinceRights::getRightsChannel,channel).orderByDesc(ProvinceRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(provinceRights!=null){
                    channel=provinceRights.getChannel();
                }
                final RemoteResult remoteResult =miguApiService.asMemberQuery(mobile,channel);
                log.info("{}-手机号:{},订购状态:{},权益领取业务ID:{}",LOG_TAG, mobile,remoteResult.getResCode(),serviceId);
                if(remoteResult.isAsMember()){
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("{}-手机号:{},权益领取业务ID:{}",LOG_TAG_ERROR, mobile,serviceId,e);

        }
        return false;
    }

    /**
     * 创建预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse createScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {

        Boolean monthlyIsSub=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(!monthlyIsSub){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getScheduledTime(mobile,serviceId,packName);
        if(scheduledTime==null){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        Rights rights=new Rights();
        String couponId=rightsSubService.setRightCofig(rights,rightsId,null);
        if(StringUtils.isBlank(couponId)){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        //校验当月藕粉咪咕同享会权益是否已发放
        FebsResponse febsResponse=rightsSubService.isRecharge(mobile,packName,scheduledTime,couponId);
        if(!febsResponse.isOK()){
            return febsResponse;
        }

        rights.setRechargeSource("login");
        rights.setChannel(channel);
        rights.setSubChannel(rightsSubService.nowIsSub(mobile,channel));
        junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
        return new FebsResponse().readyRecharge(rights.getRightsName());
    }

    /**
     * 创建网页权益预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse webCreateScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {

//        final List<String> channelList = bizProperties.getChannelByServiceId(serviceId);
        //业务类型去重
        //业务类型去重
        final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().eq(BusinessPack::getServiceId,serviceId).select(BusinessPack::getBusinessId).list();
        if(businessPackList==null || businessPackList.isEmpty()){
            log.info("{}-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId,channel);
            return new FebsResponse().fail().message("业务渠道权益关联未正确配置");
        }
        final List<String> businessIdList=businessPackList.stream().map(BusinessPack::getBusinessId).collect(Collectors.toList());
        final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getId,businessIdList).select(BusinessChannelRights::getBusinessChannel).list();
        if(businessList==null || businessList.isEmpty()){
            log.info("{}-指定渠道权益未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId,channel);
            return new FebsResponse().fail().message("指定渠道权益未正确配置");
        }
        final List<String> channelList=businessList.stream().map(BusinessChannelRights::getBusinessChannel).distinct().collect(Collectors.toList());
        boolean isProvince=provinceRightsService.lambdaQuery().eq(ProvinceRights::getChannel,channel).count()>0;
        if(isProvince && !channelList.contains(channel)){
            channelList.add(channel);
        }
        Boolean everydayIsSub=rightsSubService.everydayIsSub(mobile,channelList);
        //未查询到页面订购数据
        if(!everydayIsSub){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getWebScheduledTime(mobile,serviceId,packName);
        if(scheduledTime==null){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        Rights rights=new Rights();
        //获取业务权益配置
        String couponId=rightsSubService.setRightCofig(rights,rightsId,null);
        if(StringUtils.isBlank(couponId)){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        //校验当月藕粉咪咕同享会权益是否已发放
        FebsResponse febsResponse=rightsSubService.isRecharge(mobile,packName,scheduledTime,couponId);
        if(!febsResponse.isOK()){
            return febsResponse;
        }

        rights.setRechargeSource("web");
        rights.setChannel(channel);
        rights.setSubChannel(rightsSubService.nowIsSub(mobile,channel));
        junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
        return new FebsResponse().readyRecharge(rights.getRightsName());
    }



    @Override
    public FebsResponse rechargeForScheduleVerify(JunboChargeLog junboChargeLog) {
        String mobile=junboChargeLog.getMobile();
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndMonth(mobile,junboChargeLog.getPackName());
        //判断当月是否有已充值成功或者充值中的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()) || BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasSuccess){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,当月已充值或充值中");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            return new FebsResponse().fail();
        }

        //是否订购业务功能
        Boolean monthlyIsSub=this.monthlyIsSub(junboChargeLog.getMobile(),junboChargeLog.getServiceId());
        //未包月
        if(!monthlyIsSub){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,订单已退订或暂停");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }
    @Override
    public void rechargeForSchedule(JunboChargeLog junboChargeLog) {
        junboChargeLogService.taskRechargeForSchedule(junboChargeLog);
    }
    /**
     * 更新订单领取状态
     * @param junboChargeLog
     */
    @Override
    public void updateRechargeState(JunboChargeLog junboChargeLog) {

    }
}
