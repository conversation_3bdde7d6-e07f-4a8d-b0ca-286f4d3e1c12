package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.DianxinVrbtService;
import com.eleven.cms.remote.LianTongWydCrackService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.LiantongCrackResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("liantongWydCrackCommonService")
@Slf4j
public class LiantongWydCrackCommonServiceImpl implements IBizCommonService {

    @Autowired
    private LianTongWydCrackService lianTongWydCrackService;
    @Autowired
    private DianxinVrbtService dianxinVrbtService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
//        if (!MobileRegionResult.ISP_LIANTONG.equals(subscribe.getIsp())) {
//            return Result.msgIspRestrict();
//        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        final String channel = subscribe.getChannel();
        subscribe.setServiceId(channel);
        String mobile = subscribe.getMobile();
        LiantongCrackResult liantongCrackResult = lianTongWydCrackService.getSms(mobile, channel);
        if (liantongCrackResult.isGetOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribe.setIspOrderNo(liantongCrackResult.getSId());
            subscribeService.createSubscribeDbAndEs(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+liantongCrackResult.getCode()+"\",\"msg\":\""+liantongCrackResult.getMsg()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //验证短信验证码是否合法
        if (!smsCode.matches(Regexp.LIANTONG_SMS_CODE_REG)) {
            return Result.captchaErr("短信验证码错误");
        }
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //此处保存已提交验证码
        LiantongCrackResult liantongCrackResult = lianTongWydCrackService.smsCode(subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        if (liantongCrackResult.isSubOK()) {
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setOpenTime(new Date());
            upd.setResult("提交验证码成功");
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("提交验证码成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(liantongCrackResult.getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error(liantongCrackResult.getMsg());
        }
    }
}

