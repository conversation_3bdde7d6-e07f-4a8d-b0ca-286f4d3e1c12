package com.eleven.cms.service;

import org.jeecg.common.api.vo.Result;

import java.util.Map;

/**
 * AI视频彩铃
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/16 10:45
 **/
public interface IAiVrbtService {
    Result<?> sendSms(String mobile,String channel);

    Result<?> submitSms(String mobile,String orderId,String code,String channel);


    Map<String, Object> aiVrbtNotify(String mobile, String status, String orderNo, Map<String, Object> map);

    Result<?> openOrder(String mobile);
}
