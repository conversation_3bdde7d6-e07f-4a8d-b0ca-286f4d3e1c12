package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.PageBusinessProvinceConfigVpop;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * @Description: cms_ad_platform
 * @Author: jeecg-boot
 * @Date: 2021-06-03
 * @Version: V1.0
 */
public interface IPageBusinessProvinceConfigVpopService extends IService<PageBusinessProvinceConfigVpop> {

    ObjectNode getPopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province);

    ObjectNode getSparePopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province);

    PageBusinessProvinceConfigVpop getPageBusinessProvinceConfigVpop(String pageId, String province);

}
