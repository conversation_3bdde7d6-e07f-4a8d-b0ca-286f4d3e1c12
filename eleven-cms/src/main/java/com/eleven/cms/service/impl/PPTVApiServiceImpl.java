package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.PPTVConfigProperties;
import com.eleven.cms.dto.PPTVResult;
import com.eleven.cms.dto.PptvApiResult;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.PptvGiveLog;
import com.eleven.cms.queue.BactDelayedMessage;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.service.*;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.queue.CallbackNotifyTaskService.CALLBACK_INTERVAL;
import static com.eleven.cms.util.BizConstant.BUSINESS_TYPE_CRACK;

@Slf4j
@Service
public class PPTVApiServiceImpl implements IPPTVApiService {
    @Autowired
    IHttpRequestService httpRequestService;
    @Autowired
    PPTVConfigProperties pptvConfigProperties;
    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    IPptvGiveLogService pptvGiveLogService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private RedisDelayedQueueManager redisDelayedQueueManager;

    @Autowired
    private ISmsModelService smsModelService;

    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Override
    public void pptvRightsByRechargeData(ObjectNode dataNode){
        String phone = dataNode.at("/msisdn").asText();
        String outOrderId = dataNode.at("/id").asText();
        if (StringUtils.isEmpty(outOrderId)) {
            outOrderId = IdWorker.get32UUID();
        }
        String serviceId = dataNode.at("/serviceId").asText();
        String resCode = dataNode.at("/resCode").asText();
        String orderKey = dataNode.at("/orderKey").asText();
        String price = dataNode.at("/price").asText();
        String channelCode = dataNode.at("/channelCode").asText();
        //20元渠道包月包发送权益领取提醒短信
        if(OrderProductEnum.Q6.getChannelCode().equals(channelCode) && StringUtils.equals("CPBY",orderKey) && StringUtils.equals("000000",resCode)){
//            try {
//                TimeUnit.SECONDS.sleep(5L);
////                Boolean isReceive=memberService.isReceiveRecharge(serviceId,phone);
////                //判断是否充值
////                if(isReceive){
////                    datangSmsService.sendCrackBactNotMemberNotice(phone);
////                    redisDelayedQueueManager.addBact(BactDelayedMessage.builder().msisdn(phone).serviceId(serviceId).rightsId("youkuVIP(zhouka)").packName("cpmb_rights_pack_2").msg("权益未领取").build(), CALLBACK_INTERVAL, TimeUnit.MINUTES);
////                }else{
//                    datangSmsService.sendCrackBactMemberNotice(phone);
////                  smsModelService.sendSms(phone,null,OrderProductEnum.Q6.getChannelCode(),OrderProductEnum.Q6.getServiceId(),BizConstant.SERVICE_TYPE_RIGHTS);
////                }
//            } catch (Exception e) {
//                log.error("20元渠道包月包发送权益领取短信,dataNode:{}",dataNode,e);
//            }
            //权益提醒短信
            log.info("soap回执-20元渠道包月包发送权益领取提醒短信=>dataNode:{}", dataNode);
            smsModelService.sendSmsAsync(phone,channelCode,serviceId,BizConstant.BUSINESS_TYPE_RIGHTS);
        }
        if(oConvertUtils.isEmpty(price) || oConvertUtils.isEmpty(orderKey)){
            return;
        }
        //彩铃运营中心订阅包已发送
        if(!OrderProductEnum.isCLZX(serviceId,orderKey,resCode,channelCode)){
            return;
        }
//        rightsRecharge(phone, outOrderId,serviceId,channelCode);
    }

//    @Override
//    public void pptvRightsReceive(String phone,String outOrderId,String serviceId){
//        pptvRightsRecharge(phone, outOrderId,serviceId);
//    }
    @Override
    public Result<?> pptvRightsRecharge(String phone,String outOrderId,String serviceId,String channelCode){
       return rightsRecharge(phone,outOrderId,serviceId,channelCode);
    }
    private Result<?> rightsRecharge(String phone, String outOrderId,String serviceId,String channelCode) {
        if(!OrderProductEnum.isPptvRights(serviceId,channelCode)){
            return Result.error("");
        }
        ObjectNode responseData = mapper.createObjectNode();
        responseData.put("mobile", phone);
        responseData.put("orderId", outOrderId);
        responseData.put("serviceId", serviceId);

        String pptvMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("phone", phone);
        dataNode.put("channel", pptvConfigProperties.getChannel());
        dataNode.put("outOrderId", outOrderId);
        dataNode.put("productId", pptvConfigProperties.getProductId());
        dataNode.put("orderTime", DateUtil.formatSplitTime(LocalDateTime.now()));

        ObjectNode md5 = mapper.createObjectNode();
        md5.put("phone", phone);
        md5.put("channel", pptvConfigProperties.getChannel());
        md5.put("outOrderId", outOrderId);
        md5.put("key", pptvConfigProperties.getKey());
        dataNode.put("sign", MD5Util.getSign(md5));
        PptvGiveLog pptvGiveLog = new PptvGiveLog();
        pptvGiveLog.setOrderId(outOrderId);
        pptvGiveLog.setServiceId(serviceId);
        pptvGiveLog.setAccount(phone);
        pptvGiveLog.setMobile(phone);
        pptvGiveLog.setProductName(BizConstant.PPTV_PRODUCT_NAME_REMARK);
        pptvGiveLog.setPptvMonth(pptvMonth);
        try {
            log.info("pptv,发送会员权益,请求=>phone:{},outOrderId:{},serviceId:{},dataNode:{}",phone,outOrderId,serviceId,dataNode);
            String response=httpRequestService.implementHttpGetRequest(pptvConfigProperties.getPptvUrl(), dataNode,"pptv,发送会员权益");
            log.info("pptv,发送会员权益,响应=>phone:{},outOrderId:{},serviceId:{},dataNode:{},response:{}",phone,outOrderId,serviceId,dataNode,response);
            if(StringUtils.isNotEmpty(response)){
                PPTVResult result=mapper.readValue(response, PPTVResult.class);
                pptvGiveLog.setStatus(StringUtils.equals(result.getErrorCode(),"0")?BizConstant.PPTV_GIVE_STATUS_SUCCESS:BizConstant.PPTV_GIVE_STATUS_FAIL);
                pptvGiveLog.setRespCode(result.getErrorCode());
                pptvGiveLog.setRespMsg(result.getMessage());
                //是否发送PP会员通知短信

                Object switchs=redisUtil.get(CacheConstant.RIGHT_PPTV_TEST+":"+channelCode);
                if(switchs != null && StringUtils.equals(switchs.toString(),BizConstant.PPTV_TEST_SWITCHS_CLOSE)){
                    pptvGiveLog.setSendSmsState(BizConstant.PPTV_STATUS_RESERVE);
                }else{
                    pptvGiveLog.setSendSmsState(BizConstant.PPTV_STATUS_RESERVE);
                }

                responseData.put("rechargeStatus", StringUtils.equals(result.getErrorCode(),"0")?BizConstant.PPTV_GIVE_STATUS_SUCCESS:BizConstant.PPTV_GIVE_STATUS_FAIL);
            }else{
                pptvGiveLog.setStatus(BizConstant.PPTV_GIVE_STATUS_FAIL);
                pptvGiveLog.setRespCode("500");
                pptvGiveLog.setRespMsg("发送http请求失败");
                responseData.put("rechargeStatus", BizConstant.PPTV_GIVE_STATUS_FAIL);
            }
            pptvGiveLogService.save(pptvGiveLog);
            return Result.ok(responseData);
        } catch (Exception e) {
            log.error("pptv,发送会员权益,异常信息=>phone:{},outOrderId:{},serviceId:{},dataNode:{}",phone,outOrderId,serviceId,dataNode,e);
            responseData.put("rechargeStatus", BizConstant.PPTV_GIVE_STATUS_FAIL);
            return Result.noauth(e.getMessage(),responseData);
        }
    }
    @Override
    public Result<?> pptvRightsQuery(PptvApiResult pptvApiResult){
        Optional<PptvGiveLog>  pptvGiveLogOptional=pptvGiveLogService.lambdaQuery()
                .eq(PptvGiveLog::getOrderId,pptvApiResult.getOrderId())
                .eq(PptvGiveLog::getServiceId,pptvApiResult.getServiceId())
                .eq(PptvGiveLog::getAccount,pptvApiResult.getMobile())
                .list()
                .stream()
                .max(Comparator.comparing(PptvGiveLog::getCreateTime));
        if(pptvGiveLogOptional ==null || !pptvGiveLogOptional.isPresent()){
            return Result.error(401, "未订购pptv会员");
        }
        if(pptvGiveLogOptional.get().getStatus().equals(BizConstant.PPTV_GIVE_STATUS_SUCCESS)){
            return Result.ok("已订购pptv会员");
        }
        if(pptvGiveLogOptional.get().getStatus().equals(BizConstant.PPTV_GIVE_STATUS_FAIL)){
            return Result.error(402, "pptv会员订购失败");
        }
        return Result.error(403, "pptv会员正在充值");
    }
}
