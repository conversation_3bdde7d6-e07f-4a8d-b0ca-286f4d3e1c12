package com.eleven.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aivrbt.entity.AiRing;
import com.eleven.cms.aivrbt.entity.AiRingColumnAiTemplate;
import com.eleven.cms.aivrbt.entity.AiRingImage;
import com.eleven.cms.aivrbt.service.IAiRingColumnAiTemplateService;
import com.eleven.cms.aivrbt.service.IAiRingImageService;
import com.eleven.cms.aivrbt.service.IAiRingService;
import com.eleven.cms.aivrbt.service.impl.HaiYiAiService;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.dto.HaiYiFilterTaskCreateDTO;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.eleven.cms.aivrbt.service.impl.HaiYiAiService.AI_TAG;

/**
 * <AUTHOR>
 * @datetime 2024/10/12 9:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HaiYiMediaNotifyService {

    private final AliMediaService aliMediaService;
    private final IAiRingImageService ringImageService;
    private final IAiRingService ringService;
    private final HaiYiAiService haiYiAiService;
    private final IAiRingColumnAiTemplateService aiRingColumnAiTemplateService;
    private final RedisUtil redisUtil;

    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    /**
     * 海艺AI文档地址：https://a8zjqnaywn.feishu.cn/wiki/PMcnwWMmniiPBakvaywcaO2wnic
     *
     * 任务回调
     *
     * 任务成功示例：
     * {
     *   "id": "co5oenaeeu9ahkb8rjb0",
     *   "type": "task.finish",
     *   "create_at": 1712031640684,
     *   "payload": {
     *     "task_id": "co5oenaeeu9ahkb8rjb0",
     *     "category": 7,
     *     "type": 1,
     *     "status_desc": "finish",
     *     "images": [
     *       {
     *         "url": "https://image.cdn2.seaart.ai/2024-04-02/dev-co5oenaeeu9ahkb8rjb0/b19ef82e9b9a4eb7a488bc6a87cf093df15664bd_high.webp",
     *         "width": 1024,
     *         "height": 768
     *       },
     *       {
     *         "url": "https://image.cdn2.seaart.ai/2024-04-02/dev-co5oenaeeu9ahkb8rjb0/cc84a548b7e71f7579e38afe9759dde22751da67_high.webp",
     *         "width": 1024,
     *         "height": 768
     *       },
     *       {
     *         "url": "https://image.cdn2.seaart.ai/2024-04-02/dev-co5oenaeeu9ahkb8rjb0/91d5f8c80578026ca8ff04ae1ae1d2b85167f089_high.webp",
     *         "width": 1024,
     *         "height": 768
     *       },
     *       {
     *         "url": "https://image.cdn2.seaart.ai/2024-04-02/dev-co5oenaeeu9ahkb8rjb0/99b8fc2f5ce45a74168318ef1cf7be15397e9b8c_high.webp",
     *         "width": 1024,
     *         "height": 768
     *       }
     *     ]
     *   }
     * }
     *
     * 任务取消示例：
     * {
     *         "id": "co5pu5aeeu9c3s6cc0f0",
     *         "type": "task.sys_cancel",
     *         "create_at": 1712037659988,
     *         "payload": {
     *                 "task_id": "co5pu5aeeu9c3s6cc0f0",
     *                 "category": 1,
     *                 "type": 7,
     *                 "status_desc": "sysCancelDesc",
     *                 "images": []
     *         }
     * }
     *
     * @param jsonNode jsonNode
     */
    public void hyTaskCallBack(JsonNode jsonNode) {
        log.info("{}-海艺任务回调,收到的json数据:{}", AI_TAG, jsonNode);
        String taskId = jsonNode.at("/payload/task_id").textValue();
        String source =(String) redisUtil.hget("aivrbtApp:cms:haiyi:taskId:source", taskId);
        if (StrUtil.isNotBlank(source)&& source.equals("source:aivrbtApp")){
            //rabbit路由至app队列处理
            rabbitMQMsgSender.sendAppSubscribeHaiyiNotifyMessage(jsonNode);
            redisUtil.hdel("aivrbtApp:cms:haiyi:taskId:source", taskId);
        }
        int type = jsonNode.at("/payload/type").intValue();

        try {
            String redisKey = "hy:taskId:" + taskId;
            if (!redisUtil.setIfAbsent(redisKey, "", 120L)) {
                log.warn("{}-获取锁失败，数据正在处理中!，taskId:{}", AI_TAG, taskId);
                return;
            }
            // 图生文
            if (type == 9) {
                // 1.根据图生文ID获取AiRingImage
                AiRingImage aiRingImage = ringImageService.getOne(new LambdaQueryWrapper<AiRingImage>()
                        .eq(AiRingImage::getImgToTextTaskId, taskId)
                        .eq(AiRingImage::getFilterCreateTaskId, ""));
                if (aiRingImage == null) {
                    log.warn("{}-图生文任务已处理!,taskId:{}", AI_TAG, taskId);
                    redisUtil.del(redisKey);
                    return;
                } else {
                    log.info("{}-开始处理图生文任务!,taskId:{}", AI_TAG, taskId);
                }
                // 2.获取原图尺寸
                BufferedImage image = ImageIO.read(haiYiAiService.getInputStream(aiRingImage.getSourceImageAliUrl()));
                // 3.滤镜任务创作
                HaiYiFilterTaskCreateDTO haiYiFilterTaskCreateDTO = new HaiYiFilterTaskCreateDTO();
                haiYiFilterTaskCreateDTO.setHyTemplateId(aiRingImage.getHyTemplateId());
                haiYiFilterTaskCreateDTO.setImageUrl(aiRingImage.getSourceImageHyUrl());
                haiYiFilterTaskCreateDTO.setWidth(image.getWidth());
                haiYiFilterTaskCreateDTO.setHeight(image.getHeight());
                haiYiFilterTaskCreateDTO.setPrompt(jsonNode.at("/payload/reverse_prompts").toString());
                String filterCreateTaskId = haiYiAiService.hyCreateFilterTask(haiYiFilterTaskCreateDTO);
                // 4.更新滤镜任务ID
                aiRingImage.setFilterCreateTaskId(filterCreateTaskId);
                ringImageService.update(new LambdaUpdateWrapper<AiRingImage>()
                        .set(AiRingImage::getFilterCreateTaskId, filterCreateTaskId)
                        .eq(AiRingImage::getId, aiRingImage.getId()));
                log.info("{}-图生文任务处理完成,taskId:{}", AI_TAG, taskId);
            }

            // 滤镜任务
            if (type == 20) {
                JsonNode images = jsonNode.at("/payload/images");
                JsonNode oneNode = images.get(0);
                String hyFileUrl = oneNode.at("/url").textValue();

                // 1.通过任务ID查询图片信息
                AiRingImage image = ringImageService.getOne(new LambdaQueryWrapper<AiRingImage>()
                        .eq(AiRingImage::getFilterCreateTaskId, taskId)
                        .eq(AiRingImage::getConvertImageAliUrl, ""));
                if (image == null) {
                    log.warn("{}-滤镜创作任务已处理!,taskId:{}", AI_TAG, taskId);
                    redisUtil.del(redisKey);
                    return;
                } else {
                    log.info("{}-开始处理滤镜创作任务,taskId:{}", AI_TAG, taskId);
                }
                // 2.图片上传阿里云
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
                String aliyunFileUrl = "";
                try (InputStream in = haiYiAiService.getInputStream(hyFileUrl)) {
                    aliyunFileUrl = aliMediaService.putObjectFile("hy" + simpleDateFormat.format(new Date()) + IdWorker.get32UUID() + "_" + image.getFileName(), in);
                } catch (Exception e) {
                    log.error("{}-图片上传阿里云异常！taskId:{}", AI_TAG, taskId);
                    throw new JeecgBootException(e.getMessage());
                }
                // 3.更新任务关联的风格图片
                ringImageService.update(new LambdaUpdateWrapper<AiRingImage>()
                        .set(AiRingImage::getConvertImageHyUrl, hyFileUrl)
                        .set(AiRingImage::getConvertImageAliUrl, aliyunFileUrl)
                        .eq(AiRingImage::getFilterCreateTaskId, taskId));
                // 4.通过转换任务id查询铃音ID
                AiRingImage one = ringImageService.getOne(new LambdaQueryWrapper<AiRingImage>()
                        .select(AiRingImage::getAiRingId)
                        .eq(AiRingImage::getFilterCreateTaskId, taskId));
                String aiRingId = one.getAiRingId();
                // 5.通过铃音ID获取图片
                AiRingImage aiRingImage = ringImageService.getOne(new LambdaQueryWrapper<AiRingImage>()
                        .select(AiRingImage::getSourceImageAliUrl, AiRingImage::getConvertImageAliUrl)
                        .eq(AiRingImage::getAiRingId, aiRingId));
                // 6.通过铃音ID获取templateId
                AiRing aiRing = ringService.getOne(new LambdaQueryWrapper<AiRing>()
                        .select(AiRing::getTemplateId)
                        .eq(AiRing::getId, aiRingId));
                String templateId = aiRing.getTemplateId();
                AiRingColumnAiTemplate aiTemplate = aiRingColumnAiTemplateService.getOne(new LambdaQueryWrapper<AiRingColumnAiTemplate>()
                        .eq(AiRingColumnAiTemplate::getTemplateId, templateId));
                String clipsParamJson = aiTemplate.getClipsParam();
                LinkedHashMap<String, String> clipsParamMap = JacksonUtils.readValue(clipsParamJson, LinkedHashMap.class);
                // 7.获取图片数据构造clipsParam
                int count = 1;
                for (Map.Entry<String, String> entry : clipsParamMap.entrySet()) {
                    if (count == 1) {
                        entry.setValue(aiRingImage.getSourceImageAliUrl());
                    }
                    if (count == 2) {
                        entry.setValue(aiRingImage.getConvertImageAliUrl());
                    }
                    count++;
                }
                // 8.合成视频
                String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_HYAI, templateId, JacksonUtils.toJson(clipsParamMap));
                // 9.通过铃音ID更新JobId
                ringService.update(new LambdaUpdateWrapper<AiRing>()
                        .set(AiRing::getJobId, jobId)
                        .eq(AiRing::getId, aiRingId));
                log.info("{}-滤镜创作任务处理完成,taskId:{}", AI_TAG, taskId);
            }
            redisUtil.del(redisKey);
        } catch (Exception e) {
            AiRingImage aiRingImage = null;
            switch (type) {
                // 图生文
                case 9 :
                    aiRingImage = ringImageService.getOne(new LambdaQueryWrapper<AiRingImage>()
                        .eq(AiRingImage::getImgToTextTaskId, taskId));
                    break;
                case 20 :
                    aiRingImage = ringImageService.getOne(new LambdaQueryWrapper<AiRingImage>()
                            .eq(AiRingImage::getFilterCreateTaskId, taskId));
                    break;
            }
            if (aiRingImage != null) {
                ringService.update(new LambdaUpdateWrapper<AiRing>()
                        .set(AiRing::getRingMakeStatus, -1)
                        .eq(AiRing::getId, aiRingImage.getAiRingId()));
                log.error("{}-海艺任务回调异常!,手机号:{},taskId:{}", AI_TAG, aiRingImage.getMobile(), taskId, e);
            } else {
                log.error("{}-海艺任务回调异常!,taskId:{}", AI_TAG, taskId, e);
            }
        }
    }
}
