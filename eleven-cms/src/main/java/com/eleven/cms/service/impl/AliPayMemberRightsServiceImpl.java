package com.eleven.cms.service.impl;

import com.eleven.cms.dto.Rights;
import com.eleven.cms.entity.*;
import com.eleven.cms.remote.WoReadApiService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.WoReadPkgOrderedStatusResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付宝会员包月校验
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/27 15:19
 **/
@Slf4j
@Service
public class AliPayMemberRightsServiceImpl implements IBusinessRightsSubService {

    private static final String LOG_TAG = "支付宝会员[包月校验]";
    private static final String LOG_TAG_ERROR = "支付宝会员[包月校验异常]";
    @Autowired
    private IAliSignChargingOrderService chargingOrderService;
    @Autowired
    private IWoReadOrderService woReadOrderService;
    @Autowired
    private WoReadApiService woReadApiService;
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Override
    public FebsResponse memberVerify(String mobile,String serviceId) {
        Date payTime=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(payTime==null){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        return new FebsResponse().success();
    }
    @Override
    public FebsResponse rechargRecordVerify(String mobile,String serviceId) {
        //支付宝会员权益已全部发放
        Boolean monthlyIsRecharge=rightsSubService.aliPayMonthlyIsRecharge(mobile,serviceId);
        if(!monthlyIsRecharge){
            return new FebsResponse().repeatRecharge();
        }
        return new FebsResponse().success();
    }

    /**
     * 当月是否订购
     * @param mobile
     * @return
     */
    private Date monthlyIsSub(String mobile,String serviceId) {
        try {
            //查询支付宝签约订单
            Date aliOrderPayTime = this.aliSignRechargeIsMember(mobile,serviceId);
            if(aliOrderPayTime!=null){
                return aliOrderPayTime;
            }
            //查询沃悦读订单
            Date woReadOrderPayTime=this.woReadRechargeIsMember(mobile,serviceId);
            if(woReadOrderPayTime!=null){
                return woReadOrderPayTime;
            }
        } catch (Exception e) {
            log.error("{}-手机号:{},权益领取业务ID:{}",LOG_TAG_ERROR, mobile,serviceId,e);
        }
        return null;
    }


    private Date aliSignRechargeIsMember(String mobile,String serviceId) {
        AliSignChargingOrder aliOrder = chargingOrderService.lambdaQuery()
                .eq(AliSignChargingOrder::getMobile, mobile)
                .eq(AliSignChargingOrder::getOrderStatus,1)
                .eq(AliSignChargingOrder::getBusinessType, serviceId)
                .eq(AliSignChargingOrder::getRefundStatus, 0)
                .between(AliSignChargingOrder::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .eq(AliSignChargingOrder::getBizType, BizConstant.BIZ_TYPE_MEMBER_ALIPAY)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(aliOrder==null){
            return null;
        }
        log.info("{}-手机号:{},订购状态:{}",LOG_TAG, mobile,aliOrder.getOrderStatus());
        return aliOrder.getPayTime();
    }


    /**
     * 校验是否已支付沃阅读订单
     * @param mobile
     * @return
     */
    private Date woReadRechargeIsMember(String mobile,String serviceId) {
        WoReadOrder woReadOrder= woReadOrderService.lambdaQuery()
                .eq(WoReadOrder::getMobile,mobile)
                .eq(WoReadOrder::getOrderStatus,1)
                .eq(WoReadOrder::getSubStatus, 2)
                .eq(WoReadOrder::getBusinessType, serviceId)
                .eq(WoReadOrder::getBizType, BizConstant.BIZ_TYPE_MEMBER_ALIPAY)
                .orderByDesc(WoReadOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(woReadOrder==null){
            return null;
        }
        WoReadPkgOrderedStatusResult sign=woReadApiService.getPkgOrderedStatus(mobile,woReadOrder.getChannelId());
        if(sign==null || !sign.isOK() || sign.getData()==null){
            return null;
        }
        if(StringUtils.isEmpty(sign.getData().getStatus()) || sign.getData().getStatus().equals("3")){
            return null;
        }
        //失效时间不为空
        if(StringUtils.isNotBlank(sign.getData().getExpiretime()) && LocalDateTime.now().isAfter(DateUtil.parseString(sign.getData().getExpiretime(),DateUtil.FULL_TIME_PATTERN))){
            return null;
        }
        //生效时间不能为空
        if(StringUtils.isBlank(sign.getData().getEffectivetime())){
            return null;
        }
        //当前时间是否大于生效时间
        LocalDateTime effectivetime= DateUtil.parseString(sign.getData().getEffectivetime(),DateUtil.FULL_TIME_PATTERN);
        if(LocalDateTime.now().isAfter(effectivetime)){
            String year=String.valueOf(LocalDateTime.now().getYear());
            String month=String.valueOf(LocalDateTime.now().getMonthValue());
            String day=String.valueOf(effectivetime.getDayOfMonth());
            String hour=String.valueOf(effectivetime.getHour());
            String minute=String.valueOf(effectivetime.getMinute());
            String second=String.valueOf(effectivetime.getSecond());
            String orderYear=String.valueOf(effectivetime.getYear());
            String orderMonth=String.valueOf(effectivetime.getMonthValue());
            //判断当前年月与订购年月是否相同（是否续订）
            if(year.equals(orderYear) && month.equals(orderMonth)){
                Date payTime=DateUtil.localDateTimeToDate(DateUtil.parseString(sign.getData().getEffectivetime(),DateUtil.FULL_TIME_PATTERN));
                log.info("{}-手机号:{},订购状态:{},生效时间:{}",LOG_TAG, mobile,woReadOrder.getOrderStatus(),payTime);
                return payTime;
            }
            //续订
            if(month.length()==1){
                month="0"+month;
            }
            if(day.length()==1){
                day="0"+day;
            }
            if(hour.length()==1){
                hour="0"+hour;
            }
            if(minute.length()==1){
                minute="0"+minute;
            }
            if(second.length()==1){
                second="0"+second;
            }
            String effectiveTime=year+"-"+month+"-"+day+" "+hour+":"+minute+":"+second;
            //顺延一天
            Date payTime= DateUtil.localDateTimeToDate(DateUtil.parseString(effectiveTime,null).minusDays(-1));
            log.info("{}-手机号:{},订购状态:{},生效时间:{}",LOG_TAG, mobile,woReadOrder.getOrderStatus(),payTime);
            return payTime;
        }
        return null;
    }

    /**
     * 创建预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse createScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {

        Date payTime=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(payTime==null){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getScheduledTime(mobile,serviceId,packName);
        if(scheduledTime==null){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        Rights rights=new Rights();
        String couponId=rightsSubService.setRightCofig(rights,rightsId,payTime);
        if(StringUtils.isBlank(couponId)){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        //校验当月支付宝权益是否已发放
        FebsResponse febsResponse=rightsSubService.aliPayIsRecharge(mobile,packName,scheduledTime,couponId);
        if(!febsResponse.isOK()){
            return febsResponse;
        }
        rights.setRechargeSource("login");
        rights.setChannel(channel);
        rights.setSubChannel(rightsSubService.nowIsSub(mobile,channel));
        junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
        return new FebsResponse().readyRecharge(rights.getRightsName());
    }

    /**
     * 创建网页权益预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse webCreateScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {
        //业务类型去重
        final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().eq(BusinessPack::getServiceId,serviceId).select(BusinessPack::getBusinessId).list();
        if(businessPackList==null || businessPackList.isEmpty()){
            log.info("{}-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId,channel);
            return new FebsResponse().fail().message("业务渠道权益关联未正确配置");
        }
        final List<String> businessIdList=businessPackList.stream().map(BusinessPack::getBusinessId).collect(Collectors.toList());
        final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getId,businessIdList).select(BusinessChannelRights::getBusinessChannel).list();
        if(businessList==null || businessList.isEmpty()){
            log.info("{}-指定渠道权益未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId,channel);
            return new FebsResponse().fail().message("指定渠道权益未正确配置");
        }
        final List<String> channelList=businessList.stream().map(BusinessChannelRights::getBusinessChannel).distinct().collect(Collectors.toList());
        Boolean everydayIsSub=rightsSubService.everydayIsSub(mobile,channelList);
        //未查询到页面订购数据
        if(!everydayIsSub){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        Date payTime=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(payTime==null){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getWebScheduledTime(mobile,serviceId,packName);
        if(scheduledTime==null){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        Rights rights=new Rights();
        //获取业务权益配置
        String couponId=rightsSubService.setRightCofig(rights,rightsId,payTime);
        if(StringUtils.isBlank(couponId)){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        //校验当月支付宝权益是否已发放
        FebsResponse febsResponse=rightsSubService.aliPayIsRecharge(mobile,packName,scheduledTime,couponId);
        if(!febsResponse.isOK()){
            return febsResponse;
        }
        rights.setRechargeSource("web");
        rights.setChannel(channel);
        rights.setSubChannel(rightsSubService.nowIsSub(mobile,channel));
        junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
        return new FebsResponse().readyRecharge(rights.getRightsName());
    }



    @Override
    public FebsResponse rechargeForScheduleVerify(JunboChargeLog junboChargeLog) {
        String mobile=junboChargeLog.getMobile();
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndDate(mobile,junboChargeLog.getPackName());
        //判断当月是否有已充值成功或者充值中的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()) || BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasSuccess){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,当月已充值或充值中");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            updateAlipayRightsStatus(junboChargeLog);
            return new FebsResponse().fail();
        }
        //是否订购业务功能
        Date payTime=this.monthlyIsSub(junboChargeLog.getMobile(),junboChargeLog.getServiceId());
        //未包月
        if(payTime==null){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,订单已退订或暂停");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            updateAlipayRightsStatus(junboChargeLog);
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }
    @Override
    public void rechargeForSchedule(JunboChargeLog junboChargeLog) {
        junboChargeLog=junboChargeLogService.taskRechargeForSchedule(junboChargeLog);
        updateAlipayRightsStatus(junboChargeLog);
    }
    /**
     * 更新订单领取状态
     * @param junboChargeLog
     */
    @Override
    public void updateRechargeState(JunboChargeLog junboChargeLog) {
        updateAlipayRightsStatus(junboChargeLog);
    }

    /**
     * 更新支付宝订单领取权益状态
     */
    private void updateAlipayRightsStatus(JunboChargeLog junboChargeLog) {
        //查询最新支付订单
        AliSignChargingOrder orderPay=aliSignChargingOrderService.lambdaQuery()
                .eq(AliSignChargingOrder::getMobile, junboChargeLog.getMobile())
                .eq(AliSignChargingOrder::getBusinessType, junboChargeLog.getServiceId())
                .eq(AliSignChargingOrder::getOrderStatus, 1)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay!=null){
            aliSignChargingOrderService.lambdaUpdate().eq(AliSignChargingOrder::getId, orderPay.getId()).set(AliSignChargingOrder::getRightsStatus,junboChargeLog.getStatus()).update();
        }
    }
}
