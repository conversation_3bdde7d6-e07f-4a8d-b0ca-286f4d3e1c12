package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.YinglouOrder;
import com.eleven.cms.entity.YinglouRing;
import com.eleven.cms.mapper.YinglouRingMapper;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.YinglouRingUploadDeductMessage;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IYinglouRingService;
import com.eleven.cms.util.AESUtils;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Description: 影楼铃音表
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
@Slf4j
@Service
public class YinglouRingServiceImpl extends ServiceImpl<YinglouRingMapper, YinglouRing> implements IYinglouRingService {
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    AliMediaService aliMediaService;
    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    @Override
    public void ringUpload(String mobile,String circleId,String channel,String ringUrl){
        String uuid = IdWorker.get32UUID();
        String transactionId = channel + uuid;
        YinglouRing ring = new YinglouRing();
        /**商户ID*/
        ring.setCircleId(circleId);
        ring.setRingUrl(ringUrl);
        ring.setChannel(channel);
        /**铃音标识*/
        ring.setTransactionId(transactionId);
        ring.setMobile(mobile);
        ring.setRingName(uuid);
        this.save(ring);
        rabbitMQMsgSender.yinglouRingUploadQueueMessage(YinglouRingUploadDeductMessage.builder().id(ring.getId()).build());
    }

    @Override
    public void ringUploadScheduleDeduct(String id) {
        YinglouRing yinglouRing=this.lambdaQuery().eq(YinglouRing::getId,id).orderByDesc(YinglouRing::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(yinglouRing==null){
            log.error("影楼铃音上传消息队列接收消息-铃音查询失败--->id:{}",id);
            return;
        }
        yinglouRing.getRingUrl();
        URL url = null;
        try {
            url = new URL(yinglouRing.getRingUrl());
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        InputStream inputStream = null;
        try {
            inputStream = url.openStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        RemoteResult remoteResult=miguApiService.sxhRingUpload(yinglouRing.getMobile(), yinglouRing.getCircleId(), yinglouRing.getChannel(),yinglouRing.getTransactionId(),yinglouRing.getRingName(), OkHttpClientUtils.createInputStreamRequestBody(inputStream));
        if(remoteResult.isOK()){
            this.lambdaUpdate().eq(YinglouRing::getId,id).set(YinglouRing::getStatus,3).update();
        }else{
            this.lambdaUpdate().eq(YinglouRing::getId,id).set(YinglouRing::getStatus,4).update();
        }
    }



    @Override
    public String createTemplateRing(String mobile, String circleId, String templateId, String clipsParam, String channel) {
        //生成铃声
        YinglouRing ring = new YinglouRing();
        String aliVideoJobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_YINGLOU, templateId, clipsParam);
        String uuid = IdWorker.get32UUID();
        String transactionId = channel + uuid;
        ring.setTransactionId(transactionId);
        ring.setCircleId(circleId);
        ring.setMobile(mobile);
        ring.setChannel(channel);
        ring.setRingType(2);
        ring.setImageUrls(clipsParam);
        ring.setTemplateId(templateId);
        ring.setAliVideoJobId(aliVideoJobId);
        this.save(ring);
        return aliVideoJobId;
    }

    @Override
    public void aliRingUpload(String jobId,String ringUrl){
        YinglouRing yinglouRing=this.lambdaQuery().select(YinglouRing::getId,YinglouRing::getChannel).eq(YinglouRing::getAliVideoJobId,jobId).orderByDesc(YinglouRing::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(yinglouRing==null){
            log.error("影楼阿里云制作铃音-铃音查询失败--->id:{},ringUrl:{}",jobId,ringUrl);
            return;
        }
        String uuid = IdWorker.get32UUID();
        String transactionId = yinglouRing.getChannel() + uuid;
        this.lambdaUpdate().eq(YinglouRing::getId,yinglouRing.getId()).set(YinglouRing::getRingName,uuid).set(YinglouRing::getTransactionId,transactionId).set(YinglouRing::getRingUrl,ringUrl).update();;
        rabbitMQMsgSender.yinglouRingUploadQueueMessage(YinglouRingUploadDeductMessage.builder().id(yinglouRing.getId()).build());
    }





    /**
     * 查询铃音是否制作成功
     * @param aliVideoJobId
     * @return
     */
    @Override
    public Result<?> isAliVideoRing(String aliVideoJobId) {
        //查询已支付并且未使用的
        YinglouRing aliVideoRing=this.lambdaQuery().select(YinglouRing::getStatus,YinglouRing::getRingUrl,YinglouRing::getImageUrls).eq(YinglouRing::getAliVideoJobId,aliVideoJobId).orderByDesc(YinglouRing::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(aliVideoRing==null){
            return Result.error("铃音不存在！");
        }
        if(StringUtils.isBlank(aliVideoRing.getRingUrl())){
            return Result.error("铃音制作中！");
        }
        Result result=new Result();
        result.setMessage("铃音制作成功！");
        result.setRingUrl(aliVideoRing.getRingUrl());
        result.setStatus(aliVideoRing.getStatus());
        result.setCode(CommonConstant.SC_OK_200);
        try {
            if(StringUtils.isNotBlank(aliVideoRing.getImageUrls())){
                JsonNode jsonNode = mapper.readTree(aliVideoRing.getImageUrls());
                String pic1 = jsonNode.at("/pic1").asText("");
                result.setRingPic(pic1);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return result;
    }



    /**
     * 查询铃音是否制作成功
     * @param circleId
     * @return
     */
    @Override
    public Result<?> isCircleRing(String circleId) {
        //查询已支付并且未使用的

        YinglouRing circleRing=this.lambdaQuery().select(YinglouRing::getStatus,YinglouRing::getRingUrl,YinglouRing::getImageUrls).eq(YinglouRing::getCircleId,circleId).orderByDesc(YinglouRing::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(circleRing==null){
            return Result.error("暂无铃音！");
        }
        if(StringUtils.isBlank(circleRing.getRingUrl())){
            return Result.error("铃音制作中！");
        }
        Result result=new Result();
        result.setMessage("铃音制作成功！");
        result.setRingUrl(circleRing.getRingUrl());
        result.setStatus(circleRing.getStatus());
        result.setCode(CommonConstant.SC_OK_200);

        try {
            if(StringUtils.isNotBlank(circleRing.getImageUrls())){
                JsonNode jsonNode = mapper.readTree(circleRing.getImageUrls());
                String pic1 = jsonNode.at("/pic1").asText("");
                result.setRingPic(pic1);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return result;
    }


    public static byte[] stringTo16Bytes(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes());
            // 确保结果总是16字节，即128位
            return hash;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public String aesEncryptMobile(String mobile,String channel) {
        try {
            return AESUtils.aesEncrypt(mobile,bytesToHex(stringTo16Bytes(channel)));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("影楼发送开通短信-加密失败--->手机号:{},渠道号:{}",mobile,channel,e);
            return null;
        }
    }
    @Override
    public String aesDecryptMobile(String mobile,String channel) {
        try {
            return AESUtils.aesDecrypt(mobile,bytesToHex(stringTo16Bytes(channel)));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("影楼查询手机号是否绑定订单-解密失败--->手机号:{},渠道号:{}",mobile,channel,e);
            return null;
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
