package com.eleven.cms.service.impl;

import com.alipay.api.domain.TradeComplainQueryResponse;
import com.alipay.api.response.AlipayMerchantTradecomplainBatchqueryResponse;
import com.alipay.api.response.AlipayMerchantTradecomplainFeedbackSubmitResponse;
import com.alipay.api.response.AlipayMerchantTradecomplainReplySubmitResponse;
import com.alipay.api.response.AlipayMerchantTradecomplainSupplementSubmitResponse;
import com.eleven.cms.entity.Alipay;
import com.eleven.cms.entity.AlipayComplain;
import com.eleven.cms.mapper.AlipayComplainMapper;
import com.eleven.cms.service.IAlipayComplainService;
import com.eleven.cms.service.IAlipayConfigService;
import com.eleven.cms.service.IAlipayService;
import com.eleven.cms.util.ComplainEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**
 * @Description: 支付宝投诉明细
 * @Author: jeecg-boot
 * @Date:   2023-04-04
 * @Version: V1.0
 */
@Slf4j
@Service
public class AlipayComplainServiceImpl extends ServiceImpl<AlipayComplainMapper, AlipayComplain> implements IAlipayComplainService {
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private IAlipayService alipayService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
            false);

    @Override
    public Result<?> alipayQueryComplainList(Integer pageSize, Integer pageNum){
        List<Alipay> alipayList=alipayConfigService.lambdaQuery().eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).list();
        for (int i = 0; i < alipayList.size(); i++) {
            AlipayMerchantTradecomplainBatchqueryResponse responseList=alipayService.alipayQueryComplainList(alipayList.get(i).getAppId(),null,null,null,pageSize,pageNum);
            if(responseList.isSuccess()){
                if(pageNum<=Integer.valueOf(responseList.getTotalPageNum().toString())){
                    pageNum++;
                    List<TradeComplainQueryResponse> tradeComplainQueryResponseList=responseList.getTradeComplainInfos();
                    if(tradeComplainQueryResponseList!=null && tradeComplainQueryResponseList.size()>0){
                        for (int k = 0; k < tradeComplainQueryResponseList.size(); k++) {
                            AlipayComplain alipayComplain=this.lambdaQuery().eq(AlipayComplain::getComplainEventId,tradeComplainQueryResponseList.get(k).getComplainEventId()).orderByDesc(AlipayComplain::getCreateTime).last("limit 1").one();
                            TradeComplainQueryResponse response=tradeComplainQueryResponseList.get(k);
                            if(alipayComplain==null){
                                alipayComplain=new AlipayComplain();
                            }
                            /**商户号appId*/
                            alipayComplain.setAppId(alipayList.get(i).getAppId());
                            /**支付宝侧投诉单号*/
                            alipayComplain.setComplainEventId(response.getComplainEventId());
                            /**状态*/
                //          String complainMsg=ComplainEnum.getByStatus(response.getStatus());
                //          alipayComplain.setComplainStatus(StringUtils.isEmpty(complainMsg)?"未知状态":complainMsg);
                            alipayComplain.setComplainStatus(response.getStatus());
                            /**支付宝交易号*/
                            alipayComplain.setTradeNo(response.getTradeNo());
                            /**商家订单号*/
                            alipayComplain.setMerchantOrderNo(response.getMerchantOrderNo());
                            /**投诉单创建时间*/
                            alipayComplain.setGmtCreate(response.getGmtCreate());
                            /**投诉单修改时间*/
                            alipayComplain.setGmtModified(response.getGmtModified());
                            /**投诉单完结时间*/
                            alipayComplain.setGmtFinished(response.getGmtFinished());
                            /**用户投诉诉求*/
                            alipayComplain.setLeafCategoryName(response.getLeafCategoryName());
                            /**用户投诉原因*/
                            alipayComplain.setComplainReason(response.getComplainReason());
                            /**用户投诉内容*/
                            alipayComplain.setComplainContent(response.getContent());
                            /**投诉人电话号码*/
                            alipayComplain.setPhoneNo(response.getPhoneNo());

                            try {
                                //投诉图片
                                if(response.getImages()!=null){
                                    String images = mapper.writeValueAsString(response.getImages());
                                    alipayComplain.setImages(images);
                                }
//                                alipayComplain.setImages(response.getImages());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            alipayComplain.setUpdateTime(new Date());
                            if(alipayComplain.getId()==null){
                                this.save(alipayComplain);
                            }else{
                                this.lambdaUpdate().eq(AlipayComplain::getComplainEventId,tradeComplainQueryResponseList.get(k).getComplainEventId()).update(alipayComplain);
                            }
                        }
                    }
                    return alipayQueryComplainList(pageSize,pageNum);
                }
            }
        }
        return Result.ok("执行完成");
    }

    /**
     * 商家处理交易投诉
     * @param complainEventId
     * @param feedbackImages
     * @param feedbackContent
     * @param feedbackCode
     * @param operator
     * @return
     */
    @Override
    public Result<?> alipaySolveComplain(String complainEventId,String feedbackImages,String feedbackContent,String feedbackCode,String operator){
        AlipayComplain alipayComplain=this.lambdaQuery().eq(AlipayComplain::getComplainEventId,complainEventId).orderByDesc(AlipayComplain::getCreateTime).last("limit 1").one();
        if(alipayComplain==null){
            return Result.error("投诉已删除！");
        }
//        if(StringUtils.equalsAny(alipayComplain.getComplainStatus(),"MERCHANT_FEEDBACKED","FINISHED","CANCELLED","PLATFORM_FINISH","CLOSED")){
//            AlipayMerchantTradecomplainReplySubmitResponse response=alipayService.alipayReplyComplain(alipayComplain.getAppId(),complainEventId,feedbackImages,feedbackContent);
//            if(response.isSuccess()){
//                return Result.ok("投诉处理成功！");
//            }
//            return Result.error(response.getSubMsg());
//        }
//
//        if("PLATFORM_PROCESSING".equals(alipayComplain.getComplainStatus())){
//            AlipayMerchantTradecomplainSupplementSubmitResponse response=alipayService.alipaySupplementComplain(alipayComplain.getAppId(),complainEventId,feedbackImages,feedbackContent);
//            if(response.isSuccess()){
//                return Result.ok("投诉处理成功！");
//            }
//            return Result.error(response.getSubMsg());
//        }
//        if("MERCHANT_PROCESSING".equals(alipayComplain.getComplainStatus())){
            AlipayMerchantTradecomplainFeedbackSubmitResponse response=alipayService.alipaySolveComplain(alipayComplain.getAppId(),complainEventId,feedbackImages,feedbackContent,feedbackCode,operator);
            if(response.isSuccess()){
                return Result.ok("投诉处理成功！");
            }
            return Result.error(response.getSubMsg());
//        }
//        return Result.error("投诉处理失败！");
    }


//
//    /**
//     * 商家留言回复
//     * @param complainEventId
//     * @param feedbackImages
//     * @param feedbackContent
//     * @return
//     */
//    @Override
//    public Result<?> alipayReplyComplain(String complainEventId,String feedbackImages,String feedbackContent){
//        AlipayComplain alipayComplain=this.lambdaQuery().eq(AlipayComplain::getComplainEventId,complainEventId).orderByAsc(AlipayComplain::getCreateTime).last("limit 1").one();
//        if(alipayComplain==null){
//            return Result.error("投诉已删除！");
//        }
//        AlipayMerchantTradecomplainReplySubmitResponse response=alipayService.alipayReplyComplain(alipayComplain.getAppId(),complainEventId,feedbackImages,feedbackContent);
//        if(response.isSuccess()){
//            return Result.ok("投诉处理成功！");
//        }
//        return Result.error("投诉处理失败！");
//
//    }
//
//    /**
//     * 商家补充凭证
//     * @param complainEventId
//     * @param feedbackImages
//     * @param feedbackContent
//     * @return
//     */
//    @Override
//    public Result<?> alipaySupplementComplain(String complainEventId,String feedbackImages,String feedbackContent){
//        AlipayComplain alipayComplain=this.lambdaQuery().eq(AlipayComplain::getComplainEventId,complainEventId).orderByAsc(AlipayComplain::getCreateTime).last("limit 1").one();
//        if(alipayComplain==null){
//            return Result.error("投诉已删除！");
//        }
//        AlipayMerchantTradecomplainSupplementSubmitResponse response=alipayService.alipaySupplementComplain(alipayComplain.getAppId(),complainEventId,feedbackImages,feedbackContent);
//        if(response.isSuccess()){
//            return Result.ok("投诉处理成功！");
//        }
//        return Result.error("投诉处理失败！");
//
//    }
}
