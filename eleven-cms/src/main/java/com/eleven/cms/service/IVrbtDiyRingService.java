package com.eleven.cms.service;

import com.eleven.cms.entity.VrbtDiyRing;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.qycl.entity.BackgroundMusic;

import java.util.List;
import java.util.Map;

/**
 * @Description: cms_vrbt_diy_ring
 * @Author: jeecg-boot
 * @Date:   2023-07-25
 * @Version: V1.0
 */
public interface IVrbtDiyRingService extends IService<VrbtDiyRing> {

    Map<String, String> createDiyRing(String mobile, String[] imageArray, BackgroundMusic backgroundMusic, String subChannel);

    Map<String, String> createTemplateRing(String mobile,String templateId,String clipsParam,String subChannel);

    Map<String, String> createVideoRing(String mobile,String videoPath,String subChannel);

    List<VrbtDiyRing> queryRing(String mobile);

    RemoteResult settingRing(String vrbtDiyVideoId);

    void aliVideoJobFinishHandle(String videoJobId, String videoPath);

}
