package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.KugouOrder;
import com.eleven.cms.entity.ShydChargeOrder;
import com.eleven.cms.entity.TelecomOrder;
import com.eleven.cms.mapper.ShydChargeOrderMapper;
import com.eleven.cms.service.IShydChargeOrderService;
import com.eleven.cms.shanghaimobile.util.ShanghaiMobileConstant;
import com.eleven.cms.util.DateUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 上海移动办理订单,收到办理成功的通知后创建 Service实现
 *
 * <AUTHOR>
 * @date 2021-04-01 10:41:00
 */
@Service
public class ShydChargeOrderServiceImpl extends ServiceImpl<ShydChargeOrderMapper, ShydChargeOrder> implements IShydChargeOrderService {

    @Override
    public void saveShydChargeOrder(String phone, String orderId, Integer price,String activityId,String productName,String remark,Integer isRight,Integer chargeStatus) {
        ShydChargeOrder shydChargeOrder=new ShydChargeOrder();
        shydChargeOrder.setOrderId(orderId);
        shydChargeOrder.setMobile(phone);
        shydChargeOrder.setChargeStatus(chargeStatus);
        shydChargeOrder.setPrice(price);
        shydChargeOrder.setActivityId(activityId);
        shydChargeOrder.setProductName(productName);
        shydChargeOrder.setCallbackStatus(chargeStatus);
        shydChargeOrder.setRemark(remark);
        shydChargeOrder.setIsRight(isRight);
        shydChargeOrder.setCreateTime(LocalDateTime.now());
        shydChargeOrder.setModifyTime(LocalDateTime.now());
        this.save(shydChargeOrder);
    }
    @Override
    public void updateShydChargeOrder(String orderId,String remark,Integer chargeStatus) {
        ShydChargeOrder shydChargeOrder=new ShydChargeOrder();
        shydChargeOrder.setOrderId(orderId);
        shydChargeOrder.setChargeStatus(chargeStatus);
        shydChargeOrder.setCallbackStatus(chargeStatus);
        shydChargeOrder.setRemark(remark);
        shydChargeOrder.setModifyTime(LocalDateTime.now());
        this.lambdaUpdate().eq(ShydChargeOrder::getOrderId,orderId).update(shydChargeOrder);
    }
    @Override
    public Boolean isMember(String phone, String productName, Integer chargeStatus) {
         return this.lambdaQuery()
                .eq(ShydChargeOrder::getMobile, phone)
                .eq(ShydChargeOrder::getProductName, productName)
                .between(ShydChargeOrder::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .eq(ShydChargeOrder::getChargeStatus, chargeStatus).count()>0;
    }
//    @Override
//    public Boolean isMember(String phone) {
//        return this.lambdaQuery()
//                .eq(ShydChargeOrder::getMobile, phone)
//                .eq(ShydChargeOrder::getRemark, ShanghaiMobileConstant.POLYPHONIC_RINGTONE_PLUS_TYPE)
//                .eq(ShydChargeOrder::getChargeStatus, ShanghaiMobileConstant.CHARGE_STATUS_HANDLE_SUCCEED).count()>0;
//    }
}
