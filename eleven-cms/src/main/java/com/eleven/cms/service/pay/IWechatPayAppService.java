package com.eleven.cms.service.pay;


import com.eleven.cms.dto.WechatMiniAppPayParam;
import com.eleven.cms.dto.WechatPayResult;
import com.eleven.cms.dto.WechatRefundNotify;
import com.eleven.cms.dto.WechatpayNotify;
import org.jeecg.common.api.vo.Result;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/23 18:55
 **/
public interface IWechatPayAppService {
    WechatPayResult wechatAppPay(String outTradeNo, String total, String channel);
    WechatPayResult wechatMiniAppPay(WechatMiniAppPayParam payParam);

    WechatRefundNotify appPayRefundConfig(String outTradeNo, String outRequestNo, String refundAmount, String totalAmount, String refundReason, String channel);

    WechatpayNotify payResultNotify(String resultNotifyXml) throws Exception;

    WechatpayNotify refundResultNotify(String resultNotifyXml);
}
