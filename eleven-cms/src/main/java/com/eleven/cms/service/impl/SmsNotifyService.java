package com.eleven.cms.service.impl;

import com.eleven.cms.config.*;
import com.eleven.cms.service.IDatangSmsService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 短信通知服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsNotifyService {

    @Autowired
    private RedisUtil redisUtil;
    //@Autowired
    //private ISmsService smsService;
    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    private SmsNotifyProperties smsNotifyProperties;

    public boolean sendNotify(String phone, String noSignContent) {
        String msgContent = smsNotifyProperties.getSmsCommonSign() + noSignContent;
        return datangSmsService.sendSms(phone, msgContent);
    }

    public boolean sendQyclVerifyLimitNotify(String channelCode,String unsubRate) {
        String key = smsNotifyProperties.getQyclVerifyLimitRedisKeyPrefix() + channelCode;
        final boolean absent = redisUtil.setIfAbsent(key, "1", smsNotifyProperties.getQyclVerifyLimitRedisTtl());
        if(absent){
            String content = smsNotifyProperties.getSmsCommonSign() + "企业彩铃今日1小时退订率超限,渠道号:" + channelCode + ",退订率:" + unsubRate;
            smsNotifyProperties.getQyclVerifyLimitNotifyMobileList().forEach(mobile->{
                datangSmsService.sendSms(mobile, content);
            });
        }
        return absent;
    }
    

}
