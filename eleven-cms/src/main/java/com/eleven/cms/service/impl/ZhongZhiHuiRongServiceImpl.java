package com.eleven.cms.service.impl;

import com.eleven.cms.ad.<PERSON>ongProperties;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ZhongZhiHuiRongSmsCodeResult;
import com.eleven.cms.vo.ZhongZhiHuiRongSubmitOrderResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 众智汇融业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/22 17:08
 **/
@Slf4j
@Service
public class ZhongZhiHuiRongServiceImpl implements IZhongZhiHuiRongService{
    private static final String LOG_TAG = "众智汇融业务开通API";

    public static final String STATUS_SUB_FAIL = "success"; //开通成功
    public static final String STATUS_SUB_SUCCESS = "fail"; //开通失败
    public static final String STATUS_UN_SUB = "unsubscribe"; //退订
    public static final String  CODE_OK ="00000";
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;

    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private ZhongZhiHuiRongProperties zhongZhiHuiRongProperties;
    @Autowired
    private BlackListService blackListService;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    }

    @Override
    public Result<?> sendMessage(Subscribe subscribe) {
        CmsCrackConfig cmsCrackConfig=cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel());
        if(cmsCrackConfig==null || StringUtils.isBlank(cmsCrackConfig.getCpid())){
            log.info("{}-获取验证码-cpid未配置=>手机号:{},渠道号:{}",LOG_TAG,subscribe.getMobile(),subscribe.getChannel());
            return Result.error("获取验证码失败");
        }
        ObjectNode dataNode = mapper.createObjectNode()
                .put("launchId",cmsCrackConfig.getCpid())
                .put("phoneNumber", subscribe.getMobile())
                .put("appName", StringUtils.isNotBlank(subscribe.getReferer())?subscribe.getReferer(): "com.smile.gifmaker");




        log.info("{}-获取验证码-请求数据=>手机号:{},业务参数:{},渠道号:{}",LOG_TAG,subscribe.getMobile(),dataNode,subscribe.getChannel());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder().url(zhongZhiHuiRongProperties.getGetSendSmsUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码-响应数据=>手机号:{},业务参数:{},渠道号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),dataNode,subscribe.getChannel(),content);
            ZhongZhiHuiRongSmsCodeResult result = mapper.readValue(content, ZhongZhiHuiRongSmsCodeResult.class);
            if(result.isOK() && StringUtil.isNotBlank(result.getMsg())){
                JsonNode jsonNode = mapper.readTree(result.getMsg());
//                String status = jsonNode.at("/status").asText("");
                String code = jsonNode.at("/code").asText("");
                String message = jsonNode.at("/message").asText("");
                String orderId = jsonNode.at("/orderId").asText("");

                if(CODE_OK.equals(code) && StringUtil.isNotBlank(orderId)){
                    return Result.ok("获取验证码成功",orderId);
                }
                return Result.error(code+":"+message);
            }
            return Result.error(result.getCode()+":"+result.getMsg());
        } catch (IOException e) {
            log.info("{}-获取验证码-请求异常=>手机号:{},业务参数:{},渠道号:{}",LOG_TAG,subscribe.getMobile(),dataNode,subscribe.getChannel(),e);
            return Result.error("获取验证码失败");
        }
    }

    @Override
    public Result<?> submitOrder(Subscribe subscribe) {
        CmsCrackConfig cmsCrackConfig=cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel());
        if(cmsCrackConfig==null || StringUtils.isBlank(cmsCrackConfig.getCpid())){
            log.info("{}-提交验证码-cpid未配置=>手机号:{},渠道号:{}",LOG_TAG,subscribe.getMobile(),subscribe.getChannel());
            return Result.error("提交验证码失败");
        }
        ObjectNode dataNode = mapper.createObjectNode()
                .put("launchId",cmsCrackConfig.getCpid())
                .put("verifyCode",subscribe.getSmsCode())
                .put("orderId", subscribe.getIspOrderNo());
        log.info("{}-提交验证码-请求数据=>手机号:{},业务参数:{},渠道号:{}",LOG_TAG,subscribe.getMobile(),dataNode,subscribe.getChannel());
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder().url(zhongZhiHuiRongProperties.getSubmitSendSmsUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-响应数据=>手机号:{},业务参数:{},渠道号:{},响应参数:{}",LOG_TAG,subscribe.getMobile(),dataNode,subscribe.getChannel(),content);
            ZhongZhiHuiRongSubmitOrderResult result = mapper.readValue(content, ZhongZhiHuiRongSubmitOrderResult.class);
            if(result.isOK() && StringUtil.isNotBlank(result.getMsg())){
                JsonNode jsonNode = mapper.readTree(result.getMsg());
//                String status = jsonNode.at("/status").asText("");
                String code = jsonNode.at("/code").asText("");
                String message = jsonNode.at("/message").asText("");
                if(CODE_OK.equals(code)){
                    return Result.ok("提交验证码成功");
                }
                return Result.error(code+":"+message);
            }
            return Result.error(result.getCode()+":"+result.getMsg());
        } catch (IOException e) {
            log.info("{}-提交验证码-请求异常=>手机号:{},业务参数:{},渠道号:{}",LOG_TAG,subscribe.getMobile(),dataNode,subscribe.getChannel(),e);
            return Result.error("提交验证码失败");
        }
    }

    @Override
    public void zhongZhiHuiRongNotify(String orderId,String mobile, String status, String code,String message){
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getIspOrderNo, orderId).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            String result = "{\"resCode\":\""+code+"\",\"resMsg\":\""+message+"\"}";
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (STATUS_SUB_FAIL.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);

                subscribeService.saveChannelLimit(subscribe);

                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }else{
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else if (STATUS_UN_SUB.equals(status) && SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
                upd.setResult(result);
                upd.setVerifyStatus(0);
                upd.setVerifyStatusDaily(0);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                try {
                    blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }else{
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }

            }
        }
    }
}
