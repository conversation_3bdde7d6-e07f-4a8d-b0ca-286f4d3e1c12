package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.PushSubscribeService;
import com.eleven.cms.remote.YidongVrbtNewCrackService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.RemoteResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_INIT;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: cai lei
 * @create: 2023-08-01 16:37
 */
@Slf4j
@Service("schCommonNewService")
public class SchCommonNewServiceImpl implements IBizCommonService {

    @Autowired
    YidongVrbtNewCrackService yidongVrbtNewCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    MiguApiService miguApiService;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
//        if (!MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) {
//            return Result.msgIspRestrict();
//        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        RemoteResult remoteResult = miguApiService.schQuery(subscribe.getMobile(), subscribe.getChannel());
        if (remoteResult.isSchMember()) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);

        final BillingResult billingResult = yidongVrbtNewCrackService.getSms(subscribe.getMobile(), subscribe.getChannel());
        if (billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if ("402".equals(billingResult.getCode())) {
                subscribeService.removeSubscribeDbAndEs(subscribe.getId());
            }
//            return Result.error("系统繁忙,请稍后再试!");

            try {
                String errorMsg="{\"code\":\""+billingResult.getCode()+"\",\"message\":\""+billingResult.getMessage()+"\"}";
                return Result.errorSystemMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSystemError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //验证短信验证码是否合法
        if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
            return Result.captchaErr("短信验证码错误");
        }
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //此处保存已提交验证码
        if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setBizTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        yidongVrbtNewCrackService.smsCode(subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
        return Result.ok("提交验证码成功");
    }
}
