package com.eleven.cms.service.impl;

import com.eleven.cms.entity.AliChannelConfig;
import com.eleven.cms.mapper.AliChannelConfigMapper;
import com.eleven.cms.service.IAliChannelConfigService;
import com.eleven.cms.vo.FebsResponse;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: cms_ali_channel_config
 * @Author: jeecg-boot
 * @Date:   2023-04-24
 * @Version: V1.0
 */
@Service
public class AliChannelConfigServiceImpl extends ServiceImpl<AliChannelConfigMapper, AliChannelConfig> implements IAliChannelConfigService {

    @Override
    @Cacheable(cacheNames = CacheConstant.ALI_CHANNEL_CONFIG_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    public FebsResponse getAliChannelConfig(String key) {
        FebsResponse febsResponse = new FebsResponse();
        if(StringUtils.isBlank(key)){
            return febsResponse.fail().message("key不能为空");
        }
        AliChannelConfig aliChannelConfig = this.lambdaQuery().eq(AliChannelConfig::getConfigKey, key).one();
        if(aliChannelConfig != null){
            febsResponse.success().data(aliChannelConfig);
        }else{
            febsResponse.success().data(null);
        }
        return febsResponse;
    }
}
