package com.eleven.cms.service.impl;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.eleven.cms.config.AliyunSmsProperties;
import com.eleven.cms.service.ISmsService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/1/3 11:27
 * Desc: 阿里云短信下发
 */
@Slf4j
@Service
public class SmsServiceImpl implements ISmsService {
    @Autowired
    private AliyunSmsProperties aliyunSmsProperties;
    private IAcsClient client;
    private CommonRequest defaultRequest;

    @PostConstruct
    public void init(){
        DefaultProfile profile = DefaultProfile.getProfile(
                aliyunSmsProperties.getRegionId(), aliyunSmsProperties.getAccessKeyId(), aliyunSmsProperties.getSecret());
        this.client = new DefaultAcsClient(profile);
        this.defaultRequest = aliyunSmsProperties.getRequest();
    }

    @Override
    public boolean sendValidateNotice(String phoneNumber, String code) {
        log.info("手机号:{},短信下发兑换验证码:{}",phoneNumber,code);
        String templateCode = aliyunSmsProperties.getValidateTemplateCode();
        ImmutableMap<String, String> tplParamMap =
                ImmutableMap.of(aliyunSmsProperties.getValidateTemplateParamKey(), code);
        return sendSms(phoneNumber, templateCode,tplParamMap);
        //return true;
    }

    @Override
    public boolean sendSms(String phoneNumber, String templateCode, Map<String, String> tmlParamMap) {

        
        CommonRequest request = new CommonRequest();
        request.setMethod(defaultRequest.getMethod());
        request.setDomain(defaultRequest.getDomain());
        request.setVersion(defaultRequest.getVersion());
        request.setAction(defaultRequest.getAction());
        request.putQueryParameter("RegionId", aliyunSmsProperties.getQueryParameterRegionId());
        request.putQueryParameter("SignName", aliyunSmsProperties.getQueryParameterSignName());
        request.putQueryParameter("PhoneNumbers", phoneNumber);
        request.putQueryParameter("TemplateCode", templateCode);
        try {
            String tplParam =  new ObjectMapper().writeValueAsString(tmlParamMap);
            request.putQueryParameter("TemplateParam", tplParam);
            CommonResponse response = client.getCommonResponse(request);
            //{"Message":"OK","RequestId":"BD6012B1-FE20-407A-B0E1-8EB4817D4FAF","BizId":"446522478022387534^0","Code":"OK"}
            //System.out.println(response.getData());
            String data = response.getData();
            log.info("手机号:{},模板CODE:{},模板参数:{},阿里云短信发送结果:{}",phoneNumber,templateCode,tplParam,data);
            return StringUtils.contains(data,"\"Code\":\"OK\"");
            
        } catch (ClientException | JsonProcessingException e) {
            e.printStackTrace();
        }

        return false;
    }

    public static void main(String[] args) throws JsonProcessingException {
        ImmutableMap<String, String> tmlParamMap = ImmutableMap.of("orderid", "xxxxxxxx");
        String tmlParam =  new ObjectMapper().writeValueAsString(tmlParamMap);
        System.out.println("tmlParam = " + tmlParam);

        String abbreviate = StringUtils.abbreviate("13438828200", 3, 4);
        System.out.println("abbreviate = " + abbreviate);
    }
}
