package com.eleven.cms.service.impl;

import com.eleven.cms.abstracts.AbstractOrder;
import com.eleven.cms.dto.FuJianOrderDTO;
import com.eleven.cms.dto.FuJianSmsCodeGetDTO;
import com.eleven.cms.entity.SubChannel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.ISubChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.fujian.FuJianYiDongConfig;
import com.eleven.cms.util.fujian.FuJianYiDongHttpUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * 福建移动福诺
 *
 * <AUTHOR>
 * @datetime 2024/12/4 15:30
 */
@Service
@RequiredArgsConstructor
public class FuJianYiDongService extends AbstractOrder {

    private final ISubscribeService subscribeService;
    private final ISubChannelService subChannelService;
    private final FuJianYiDongConfig config;
    @Override
    protected Result beforeGetSmsCodeRequest(JsonNode jsonNode) {
        return Result.ok();
    }

    @Override
    protected Result getSmsCodeRequest(JsonNode jsonNode) {
        String channel = jsonNode.get("channel").asText();

        FuJianSmsCodeGetDTO fuJianSmsCodeGetDTO = new FuJianSmsCodeGetDTO();
        fuJianSmsCodeGetDTO.setMobile(jsonNode.get("mobile").asText());
        fuJianSmsCodeGetDTO.setSaleId(config.getProductMap().get(channel).get("productCode"));
        fuJianSmsCodeGetDTO.setLogTag(config.getProductMap().get(channel).get("productName"));

        String result = FuJianYiDongHttpUtils.getSmsCode(fuJianSmsCodeGetDTO, buildExtraInfoMap(jsonNode));
        JsonNode resultJsonNode = JacksonUtils.readTree(result);
        if ("000000".equals(resultJsonNode.at("/returnCode").asText())) {
            return Result.ok();
        } else {
            return Result.errorSystemMsg(resultJsonNode.at("/returnInfo").asText());
        }
    }

    @Override
    protected Result beforeOrderRequest(JsonNode jsonNode) {
        return Result.ok();
    }

    @Override
    protected Result orderRequest(JsonNode jsonNode) {
        String channel = jsonNode.get("channel").asText();
        String subChannelStr = jsonNode.get("subChannel").asText();

        String contact = "";
        SubChannel subChannel = subChannelService.findSubChannel(subChannelStr);
        if (subChannel != null) {
            contact = subChannel.getAdPlatform();
        }
        // 1.调用下单接口
        FuJianOrderDTO fuJianOrderDTO = new FuJianOrderDTO();
        fuJianOrderDTO.setSmsCode(jsonNode.get("smsCode").asText());
        fuJianOrderDTO.setMobile(jsonNode.get("mobile").asText());
        fuJianOrderDTO.setSaleId(config.getProductMap().get(channel).get("productCode"));
        fuJianOrderDTO.setLogTag(config.getProductMap().get(channel).get("productName"));
        fuJianOrderDTO.setAgentCode("");
        fuJianOrderDTO.setOid(jsonNode.get("id").asText());
        fuJianOrderDTO.setProductUrl("");
        fuJianOrderDTO.setFirstContact(contact);
        fuJianOrderDTO.setSecondContact(contact);
        fuJianOrderDTO.setThirdContact(jsonNode.get("referer") != null? jsonNode.get("referer").asText() : "");

        String result = FuJianYiDongHttpUtils.order(fuJianOrderDTO, buildExtraInfoMap(jsonNode));
        JsonNode resultJsonNode = JacksonUtils.readTree(result);

        // 2.根据下单成功或失败修改订单状态
        Subscribe resultSubscribe = new Subscribe();
        resultSubscribe.setId(jsonNode.get("id").asText());
        resultSubscribe.setModifyTime(new Date());
        resultSubscribe.setResult(resultJsonNode.at("/returnInfo").asText());
        if ("000000".equals(resultJsonNode.at("/returnCode").asText())) {
            resultSubscribe.setOpenTime(new Date());
            resultSubscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            subscribeService.updateSubscribeDbAndEs(resultSubscribe);
            return Result.ok();
        } else {
            resultSubscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(resultSubscribe);
            return Result.error(resultJsonNode.at("/returnInfo").asText());
        }
    }

    private Map<String, Object> buildExtraInfoMap(JsonNode jsonNode) {
        Map<String, Object> extraInfoMap = new HashMap<>();
        extraInfoMap.put("手机号", jsonNode.get("mobile").asText());
        extraInfoMap.put("渠道号", jsonNode.get("channel").asText());
        return extraInfoMap;
    }
}
