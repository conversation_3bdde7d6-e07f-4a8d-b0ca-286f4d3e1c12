package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.CmsAlarmUserConfigDetail;
import com.eleven.cms.entity.CmsAlarmUserConfigDetailDto;

import java.util.List;
import java.util.Map;

/**
 * @Description: cms_alarm_user_config_detail
 * @Author: jeecg-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
public interface ICmsAlarmUserConfigDetailService extends IService<CmsAlarmUserConfigDetail> {

    /**
     * 查询分页列表
     * @param page page
     * @param cmsAlarmUserConfigDetail cmsAlarmUserConfigDetail
     * @return IPage<CmsAlarmUserConfigDetail>
     */
    IPage<CmsAlarmUserConfigDetail> queryPageList(IPage<CmsAlarmUserConfigDetail> page, CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail);

    List<CmsAlarmUserConfigDetailDto> getAlarmUserList(String channelCode, String alarmSceneCode);
}
