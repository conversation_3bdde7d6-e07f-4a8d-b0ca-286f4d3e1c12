package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.DuocaiDuodianProperties;
import com.eleven.cms.dto.DuoCaiOpenNotify;
import com.eleven.cms.dto.DuoCaiProduct;
import com.eleven.cms.dto.DuocaiOpenResponse;
import com.eleven.cms.dto.DuocaiProductResponse;
import com.eleven.cms.entity.DuodianCouponCodeChargeLog;
import com.eleven.cms.entity.MemberRights;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.DuodianCouponCodeChargeLogMapper;
import com.eleven.cms.queue.DuodianSendCodeDeductMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.TuniuSendCodeDeductMessage;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.SignatureUtils;
import com.eleven.cms.vo.DuoDianRechargeResult;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 途牛券码充值记录
 * @Author: jeecg-boot
 * @Date:   2024-03-29
 * @Version: V1.0
 */
@Slf4j
@Service
public class DuodianCouponCodeChargeLogServiceImpl extends ServiceImpl<DuodianCouponCodeChargeLogMapper, DuodianCouponCodeChargeLog> implements IDuodianCouponCodeChargeLogService {

    public static final Integer SUB_DAY = 31;//包月天数
    private static final int ERROR_CODE_SUCCESS = 0; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String ERROR_CODE_SUCCESS_MSG ="处理成功"; //响应描述


    private static final int ERROR_CODE_FAIL = 500; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String ERROR_CODE_FAIL_MSG ="验签失败"; //响应描述

    private static final int PRODUCT_ERROR_CODE_FAIL = 501; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String PRODUCT_ERROR_CODE_FAIL_MSG ="产品不存在"; //响应描述

    private static final int SYSTEM_ERROR_CODE_FAIL = 502; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String SYSTEM_ERROR_CODE_FAIL_MSG ="系统异常"; //响应描述

    private static final int APPID_ERROR_CODE_FAIL = 503; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String APPID_ERROR_CODE_FAIL_MSG ="appId异常"; //响应描述

    //核销凭证，根据核销方式填充数据：1、2、3 类型时候会根据内容生产条形码或二维码；4 类型时是卡券URL 地址；5类型是密码；6 是卡号和密码，内容如下的json 字符串：{"cardNum":"12r4f5fy6g4lwf5","password":"dfg3e3423"} （ 无核销凭证可传入券ID与couponId 相同）
    private static final int CARD_TYPE = 2;

    private static final int COUPON_STATUS_INIT = 0;//待发放
    private static final int COUPON_STATUS_AWAIT_USE = 1;//待使用
    private static final int COUPON_STATUS_USE = 2;//已使用
    private static final int COUPON_STATUS_CANCEL = 3;//已取消
    private static final int COUPON_STATUS_EXPIRED = 4;//已过期
    public static final  int COUPON_STATUS_FAIL=6;//使用失败
    public static final int COUPON_STATUS_RECHARGE_WAIT=7;//充值中
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private DuocaiDuodianProperties duocaiDuodianProperties;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    private IMemberRightsService memberRightsService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IDuoDianRechargeApiService duoDianRechargeApiService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public DuocaiOpenResponse duocaiOpenNotify(String requestBody, String sign, String appId, String timestamp) {
        try {
            if(!duocaiDuodianProperties.getAppId().equals(appId)){
                log.error("多彩下发多点商超权益通知-APPID异常--->参数:{},sign:{},appId:{}",requestBody,sign,appId);
                return new DuocaiOpenResponse(APPID_ERROR_CODE_FAIL_MSG,APPID_ERROR_CODE_FAIL,null);
            }
            JSONObject jsonObject = JSONObject.parseObject(requestBody);
            Map<String, Object> params = jsonObject.toJavaObject(Map.class);
            //使用Stream进行排序
            params = params.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new));
            // 将Map转换为JSON字符串
            requestBody = mapper.writeValueAsString(params);
            if(!SignatureUtils.checkSign(params,duocaiDuodianProperties.getAppKey(),timestamp,sign)){
                log.error("多彩下发多点商超权益通知-验签失败--->参数:{},sign:{},appId:{}",requestBody,sign,appId);
                return new DuocaiOpenResponse(ERROR_CODE_FAIL_MSG,ERROR_CODE_FAIL,null);
            }
            final DuoCaiOpenNotify duoCaiOpenNotify = new ObjectMapper().readValue(requestBody, DuoCaiOpenNotify.class);
            DuodianCouponCodeChargeLog duodianCouponCodeChargeLog=new DuodianCouponCodeChargeLog();
            /**事务跟踪id，每次请求保证唯一性*/
            duodianCouponCodeChargeLog.setDataLinkId(duoCaiOpenNotify.getTrxId());
            /**商品标识*/
            duodianCouponCodeChargeLog.setGoodsNo(duoCaiOpenNotify.getGoodsId());
            /**数量*/
            duodianCouponCodeChargeLog.setNum(String.valueOf(duoCaiOpenNotify.getCount()));
            /**手机号*/
            duodianCouponCodeChargeLog.setMobile(duoCaiOpenNotify.getAccountNo());
            /*多彩订单号*/
            duodianCouponCodeChargeLog.setOrderId(duoCaiOpenNotify.getOrderId());
            //订单号
            String channelOrderId= IdWorker.get32UUID();
            duodianCouponCodeChargeLog.setChannelOrderId(channelOrderId);
            /**结算价格*/
            duodianCouponCodeChargeLog.setSettlementPrice(duoCaiOpenNotify.getSettlementPrice());
            /**回调通知地址*/
            duodianCouponCodeChargeLog.setNotifyUrl(duoCaiOpenNotify.getCallbackUrl());
            /**订单状态：0-待发放1-待使用2-已使用3-已取消 4-已过期*/
            duodianCouponCodeChargeLog.setStatus(COUPON_STATUS_INIT);
            /**过期时间*/
            duodianCouponCodeChargeLog.setInvalidTime(DateUtil.localDateTimeToDate(LocalDateTime.now().plusDays(SUB_DAY)));
            LocalDateTime start = LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
            LocalDateTime end=    LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
            //根据上月是否有充值记录判断是否续订
            Integer renewalCount=this.lambdaQuery().eq(DuodianCouponCodeChargeLog::getMobile,duoCaiOpenNotify.getAccountNo()).between(DuodianCouponCodeChargeLog::getCreateTime,start,end).count();
            if(renewalCount>0){
                //2续订
                duodianCouponCodeChargeLog.setRemark("2");
            }else{
                LocalDateTime subStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                LocalDateTime subEnd=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
                //查询当月是否订购 1已订购 0 未订购
                Integer subCount=subscribeService.lambdaQuery().eq(Subscribe::getMobile,duoCaiOpenNotify.getAccountNo()).between(Subscribe::getCreateTime,subStart,subEnd).eq(Subscribe::getChannel,BizConstant.BIZ_TYPE_GZYD_TNLY_HYLB).eq(Subscribe::getStatus,BizConstant.SUBSCRIBE_STATUS_SUCCESS).count();
                duodianCouponCodeChargeLog.setRemark(subCount>0?"1":"0");
            }
            this.baseMapper.insert(duodianCouponCodeChargeLog);
            rabbitMQMsgSender.duodianSendCodeQueueMessage(DuodianSendCodeDeductMessage.builder().id(duodianCouponCodeChargeLog.getId()).mobile(duodianCouponCodeChargeLog.getMobile()).build());

            DuocaiOpenResponse.Result result=new DuocaiOpenResponse.Result();
            //供应商的订单号
            result.setChannelOrderId(channelOrderId);
            //券码的唯一 id
            result.setCouponId(duodianCouponCodeChargeLog.getId());
            //核销方式:1，二维码2，条形码3，二维码和条形码 4，卡券URL 地址5，密码6，卡号和密码（无需接入系统核销可默认选择二维码）
            result.setWriteoffType(CARD_TYPE);
            //核销凭证，根据核销方式填充数据：1、2、3 类型时候会根据内容生产条形码或二维码；
            // 4 类型时是卡券URL 地址；
            // 5类型是密码；
            // 6 是卡号和密码，内容如下的json 字符串：{"cardNum":"12r4f5fy6g4lwf5","password":"dfg3e3423"} （ 无核销凭证可传入券ID与couponId 相同）
            result.setVoucher(channelOrderId);
            //订单状态：0-待发放1-待使用2-已使用3-已取消 4-已过期
            result.setCouponStatus(COUPON_STATUS_INIT);
            return new DuocaiOpenResponse(ERROR_CODE_SUCCESS_MSG,ERROR_CODE_SUCCESS,result);
        } catch (Exception e) {
            log.error("多彩下发多点商超权益通知-系统异常--->requestBody:{}",requestBody,e);
            return new DuocaiOpenResponse(SYSTEM_ERROR_CODE_FAIL_MSG,SYSTEM_ERROR_CODE_FAIL,null);
        }
    }

    @Override
    public void sendCodeScheduleDeduct(String id) {
        DuodianCouponCodeChargeLog duodianCouponCodeChargeLog=this.baseMapper.selectById(id);
        if(duodianCouponCodeChargeLog==null){
            log.error("多点权益发送券码消息队列接收消息-激活码查询失败--->id:{}",id);
            return;
        }
        String sid = RandomStringUtils.randomAlphanumeric(10);
        MemberRights memberRights=memberRightsService.lambdaQuery().eq(MemberRights::getRightsId, duodianCouponCodeChargeLog.getGoodsNo()).orderByDesc(MemberRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
        if(memberRights!=null){
            duodianCouponCodeChargeLog.setRightsId(memberRights.getCouponId());
        }else{
            duodianCouponCodeChargeLog.setRightsId("产品异常");
        }
        duodianCouponCodeChargeLog.setCouponCode(sid);
        duodianCouponCodeChargeLog.setStatus(COUPON_STATUS_AWAIT_USE);
        this.baseMapper.updateById(duodianCouponCodeChargeLog);
        if(StringUtils.isNotBlank(duodianCouponCodeChargeLog.getMobile())){
            smsModelService.sendSmsAsync(duodianCouponCodeChargeLog.getMobile(),duodianCouponCodeChargeLog.getGoodsNo(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_RIGHTS,sid);
        }
        this.duodianSendCodeSync(duodianCouponCodeChargeLog,COUPON_STATUS_AWAIT_USE);
    }


    private void duodianSendCodeSync(DuodianCouponCodeChargeLog duodianCouponCodeChargeLog,int status) {
        Map<String,Object> coupon = Maps.newHashMap();
        coupon.put("channelOrderId",duodianCouponCodeChargeLog.getChannelOrderId());
        coupon.put("couponStatus",status);
        String timestamp=String.valueOf(System.currentTimeMillis());
        String sign=SignatureUtils.sign(coupon,duocaiDuodianProperties.getAppKey(),timestamp);
        ObjectNode dataNode =mapper.createObjectNode();
        dataNode.put("channelOrderId",duodianCouponCodeChargeLog.getChannelOrderId());
        dataNode.put("couponStatus",status);
        log.info("多彩下发多点商超权益交易状态回调-请求数据=>手机号:{},请求参数:{}",duodianCouponCodeChargeLog.getMobile(),dataNode);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder().url(duodianCouponCodeChargeLog.getNotifyUrl()).post(body).addHeader("app-id",duocaiDuodianProperties.getAppId()).addHeader("sign",sign).addHeader("timestamp",timestamp).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("多彩下发多点商超权益交易状态回调-响应数据=>手机号:{},响应参数:{}",duodianCouponCodeChargeLog.getMobile(),content);
        } catch (IOException e) {
            log.error("多彩下发多点商超权益交易状态回调-请求异常=>手机号:{},请求参数:{}",duodianCouponCodeChargeLog.getMobile(),dataNode,e);
        }
    }




    @Override
    public FebsResponse duocaiRecharge(String mobile, String couponCode){
        //校验激活码
        DuodianCouponCodeChargeLog duodianCouponCode=this.lambdaQuery().eq(DuodianCouponCodeChargeLog::getCouponCode,couponCode).orderByDesc(DuodianCouponCodeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duodianCouponCode==null){
            return new FebsResponse().fail().message("激活码错误");
        }
        //校验激活码状态（失败和未使用的才能继续使用）
        //校验激活码状态（失败和未使用的才能继续使用）
        if(!StringUtils.equalsAny(String.valueOf(duodianCouponCode.getStatus()),String.valueOf(COUPON_STATUS_AWAIT_USE),String.valueOf(COUPON_STATUS_FAIL))){
            return new FebsResponse().fail().message("激活码已使用");
        }

        //校验过期时间
        if(duodianCouponCode.getInvalidTime()==null){
            //主动设置优惠券失效
            this.lambdaUpdate().eq(DuodianCouponCodeChargeLog::getCouponCode,couponCode).eq(DuodianCouponCodeChargeLog::getMobile,mobile).set(DuodianCouponCodeChargeLog::getUpdateTime,new Date()).set(DuodianCouponCodeChargeLog::getStatus,COUPON_STATUS_CANCEL).update();
            return new FebsResponse().fail().message("激活码已使用");
        }
        //校验激活码状态（已失效禁止使用）
        if(duodianCouponCode.getStatus().equals(COUPON_STATUS_USE)){
            return new FebsResponse().fail().message("激活码已使用");
        }
        //校验激活码过期时间是否到期
        LocalDateTime invalidTime=duodianCouponCode.getInvalidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        if(invalidTime.isBefore(LocalDateTime.now())){
            //主动设置优惠券失效
            this.lambdaUpdate().eq(DuodianCouponCodeChargeLog::getCouponCode,couponCode).eq(DuodianCouponCodeChargeLog::getMobile,mobile).set(DuodianCouponCodeChargeLog::getUpdateTime,new Date()).set(DuodianCouponCodeChargeLog::getStatus,COUPON_STATUS_EXPIRED).update();
            return new FebsResponse().fail().message("激活码已使用");
        }
        //校验手机号31天内是否已领取途牛会员（充值中和已充值）
        DuodianCouponCodeChargeLog duodianCouponCodeChargeLog=this.lambdaQuery().eq(DuodianCouponCodeChargeLog::getMobile,mobile).in(DuodianCouponCodeChargeLog::getStatus,COUPON_STATUS_USE,COUPON_STATUS_RECHARGE_WAIT).between(DuodianCouponCodeChargeLog::getSendTime,LocalDateTime.now().minusDays(SUB_DAY), LocalDateTime.now()).orderByDesc(DuodianCouponCodeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(duodianCouponCodeChargeLog!=null){
            return new FebsResponse().fail().message("账号已充值");
        }
        //设置激活码状态（使用中）
        this.lambdaUpdate().eq(DuodianCouponCodeChargeLog::getCouponCode,couponCode).eq(DuodianCouponCodeChargeLog::getMobile,mobile).set(DuodianCouponCodeChargeLog::getSendTime,new Date()).set(DuodianCouponCodeChargeLog::getUpdateTime,new Date()).set(DuodianCouponCodeChargeLog::getStatus,COUPON_STATUS_RECHARGE_WAIT).update();


        DuoDianRechargeResult duoDianRechargeResult=duoDianRechargeApiService.receiveCoupon(duodianCouponCode.getChannelOrderId(),duodianCouponCode.getMobile(),duodianCouponCode.getRightsId());
        if(duoDianRechargeResult.isOK()){
            //更新订单状态
            this.lambdaUpdate().eq(DuodianCouponCodeChargeLog::getCouponCode,couponCode).eq(DuodianCouponCodeChargeLog::getMobile,mobile).set(DuodianCouponCodeChargeLog::getSendTime,new Date()).set(DuodianCouponCodeChargeLog::getUpdateTime,new Date()).set(DuodianCouponCodeChargeLog::getStatus,COUPON_STATUS_USE).update();
            this.duodianSendCodeSync(duodianCouponCodeChargeLog,COUPON_STATUS_USE);
            return new FebsResponse().success().message("充值成功");
        }else{
            //更新订单状态
            this.lambdaUpdate().eq(DuodianCouponCodeChargeLog::getCouponCode,couponCode).eq(DuodianCouponCodeChargeLog::getMobile,mobile).set(DuodianCouponCodeChargeLog::getSendTime,new Date()).set(DuodianCouponCodeChargeLog::getUpdateTime,new Date()).set(DuodianCouponCodeChargeLog::getStatus,COUPON_STATUS_FAIL).update();
            return new FebsResponse().fail().message("充值失败");
        }

    }


    @Override
    public DuocaiProductResponse duocaiProduct(String requestBody, String sign, String appId, String timestamp) {
        try {
            if(!duocaiDuodianProperties.getAppId().equals(appId)){
                log.error("多彩查询产品详细-APPID异常--->参数:{},sign:{},appId:{}",requestBody,sign,appId);
                return new DuocaiProductResponse(APPID_ERROR_CODE_FAIL_MSG,APPID_ERROR_CODE_FAIL,null);
            }
            JSONObject jsonObject = JSONObject.parseObject(requestBody);
            Map<String, Object> params = jsonObject.toJavaObject(Map.class);
            //使用Stream进行排序
            params = params.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new));
            // 将Map转换为JSON字符串
            requestBody = mapper.writeValueAsString(params);
            if(!SignatureUtils.checkSign(params,duocaiDuodianProperties.getAppKey(),timestamp,sign)){
                log.error("多彩查询产品详细-验签失败--->参数:{},sign:{},appId:{}",requestBody,sign,appId);
                return new DuocaiProductResponse(ERROR_CODE_FAIL_MSG,ERROR_CODE_FAIL,null);
            }



            final DuoCaiProduct duoCaiProduct = new ObjectMapper().readValue(requestBody, DuoCaiProduct.class);
            MemberRights memberRights=memberRightsService.lambdaQuery().eq(MemberRights::getRightsId, duoCaiProduct.getId()).orderByDesc(MemberRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();;
            if(memberRights==null){
                return new DuocaiProductResponse(PRODUCT_ERROR_CODE_FAIL_MSG,PRODUCT_ERROR_CODE_FAIL,null);
            }
            DuocaiProductResponse.Result result=new DuocaiProductResponse.Result();
            result.setGoodsName(memberRights.getRightsName());
            //商品ID
            result.setGoodsId(memberRights.getRightsId());
            //是 1 商品状态：0-下架1-上架
            result.setGoodsStatus(memberRights.getRightsSwitchs());
            //商品主图
            result.setGoodsImage(memberRights.getRemark());
            //面额
            result.setPrice(new BigDecimal(memberRights.getOriginalPrice()));
            //结算价
            result.setSettlementPrice(new BigDecimal(memberRights.getProductPrice()));

            //库存
            result.setStockAmount(Integer.valueOf(duocaiDuodianProperties.getStockAmount()

            ));

            //核销方式：卡券类型特有核销方式-券码特有：1，二维码2，条形码3，二维码和条形码4，卡券URL地址5，密码6，卡号和密码
            result.setTicketFormatType(CARD_TYPE);
            //充值类型: 充值类型特有充值类型: 1. 厂家直冲2.卖家直发3.官方直充4.扫码直充5.卖家代充6.卡密
            result.setDelivery(Integer.valueOf(duocaiDuodianProperties.getDelivery()));
            //品牌
            result.setBrand(duocaiDuodianProperties.getBrand());
            //权益描述
            result.setDescription(duocaiDuodianProperties.getDescription());
            //使用规则
            result.setUseRule(duocaiDuodianProperties.getUseRule());
            return new DuocaiProductResponse(ERROR_CODE_SUCCESS_MSG,ERROR_CODE_SUCCESS,result);
        } catch (Exception e) {
            log.error("多彩查询产品详细-系统异常--->requestBody:{}",requestBody,e);
            return new DuocaiProductResponse(SYSTEM_ERROR_CODE_FAIL_MSG,SYSTEM_ERROR_CODE_FAIL,null);
        }
    }
}
