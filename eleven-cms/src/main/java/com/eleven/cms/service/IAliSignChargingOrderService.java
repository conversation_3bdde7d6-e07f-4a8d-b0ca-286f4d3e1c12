package com.eleven.cms.service;

import com.eleven.cms.entity.AliSignChargingOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.FebsResponse;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: cms_ali_sign_charging_order
 * @Author: jeecg-boot
 * @Date:   2022-12-30
 * @Version: V1.0
 */
public interface IAliSignChargingOrderService extends IService<AliSignChargingOrder> {

    void updateStatusAdFeedback(AliSignChargingOrder aliSignChargingOrder,String businessType,String outTradeNo);

    Result<?> aliPayRefund(String outTradeNo, String refund);

    Result<?> aliPayQueryRefund(String outTradeNo);


//    Result<?> aliPayRefundByMobile(String mobile, String refund);

    FebsResponse alipayQueryChargingOrders(String startTime,String endTime,Integer orderStatus);

    Result<?> aliPayTransferFee(String outTradeNo, String refund,String payeeType,String mobile,String businessType);

    Result<?> aliPayQueryTransferFee(String outTradeNo,String businessType);


    void aliPayCallback(String outTradeNo, String mobile, String businessType);


    Result<?> aliPayHandMovementTransferFee(String outTradeNo, String refundRemark);


    Result<?> aliPaySetWaiterRemark(String outTradeNo, String waiterRemark);

    Result<?> dianXinAliPayVrbtTransferFee(String mobile);

    Result<?> lianTongAliPayVrbtTransferFee(String mobile);

}
