package com.eleven.cms.service.impl;

import com.eleven.cms.dto.Rights;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.OrderPay;
import com.eleven.cms.service.IBusinessRightsSubService;
import com.eleven.cms.service.IJunboChargeLogService;
import com.eleven.cms.service.IOrderPayService;
import com.eleven.cms.service.IRightsSubService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 权益商城包月校验
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/27 15:19
 **/
@Slf4j
@Service
public class ShopRechargeRightsServiceImpl implements IBusinessRightsSubService {

    private static final String LOG_TAG = "权益商城[订单校验]";
    private static final String LOG_TAG_ERROR = "权益商城[订单校验异常]";
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;

    @Autowired
    private IOrderPayService orderPayService;
    @Override
    public FebsResponse memberVerify(String outTradeNo,String serviceId) {
        Boolean isSub=this.isSub(outTradeNo,serviceId);
        //未订购
        if(!isSub){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        return new FebsResponse().success();
    }
    @Override
    public FebsResponse rechargRecordVerify(String outTradeNo,String serviceId) {
        //权益商城已全部发放
        Boolean monthlyIsRecharge=rightsSubService.shopOrderDayIsRecharge(outTradeNo,serviceId);
        if(!monthlyIsRecharge){
            return new FebsResponse().repeatRecharge();
        }
        return new FebsResponse().success();
    }

    /**
     * 是否订购
     * @param outTradeNo
     * @param serviceId
     * @return
     */
    private Boolean isSub(String outTradeNo,String serviceId) {
        try {
            //查询订单是否支付
            OrderPay order= orderPayService.lambdaQuery().eq(OrderPay::getOrderId,outTradeNo).eq(OrderPay::getStatus,1).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
            if(order==null){
                log.error("{}-未找到支付日志或者订单已支付,订单号:{},权益领取业务ID:{}",LOG_TAG,outTradeNo,serviceId);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("{}-订单号:{},权益领取业务ID:{}",LOG_TAG_ERROR, outTradeNo,serviceId,e);
        }
        return false;
    }







    /**
     * 创建预约充值记录
     * @param orderId
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse createScheduledRecharge(String orderId,String account,String serviceId,String packName,String rightsId,String channel) {

        Boolean isSub=this.isSub(orderId,serviceId);
        //未订购
        if(!isSub){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getShopOrderScheduledTime(serviceId,packName);
        if(scheduledTime==null){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        Rights rights=new Rights();
        String couponId=rightsSubService.setRightCofig(rights,rightsId,null);
        if(StringUtils.isBlank(couponId)){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        //校验商城权益是否已发放
        FebsResponse febsResponse=rightsSubService.shopOrderIsRecharge(orderId,packName,scheduledTime,couponId);
        if(!febsResponse.isOK()){
            return febsResponse;
        }
        rights.setRechargeSource("shop");
        OrderPay order= orderPayService.lambdaQuery().eq(OrderPay::getOrderId,orderId).eq(OrderPay::getStatus,1).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
        if(order==null){
            log.error("{}-未找到支付日志或者订单已支付,订单号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,orderId,serviceId,channel);
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        rights.setChannel(channel);
        rights.setSubChannel(rightsSubService.nowIsSub(order.getLoginMobile() ,channel));
        JunboChargeLog junboChargeLog=junboChargeLogService.shopOrderRightsRecharge(order.getMobile(), account,  serviceId,  rights,Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),packName, orderId,order.getLoginMobile());
        updateShopRightsStatus(junboChargeLog);
        return new FebsResponse().readyRecharge(rights.getRightsName());
    }

    /**
     * 创建网页权益预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse webCreateScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {
        return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
    }



    @Override
    public FebsResponse rechargeForScheduleVerify(JunboChargeLog junboChargeLog) {
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByOrderIdAndPackName(junboChargeLog.getMiguOrderId(),junboChargeLog.getPackName());
        //判断当月是否有已充值成功或者充值中的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()) || BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasSuccess){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,当月已充值或充值中");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            updateShopRightsStatus(junboChargeLog);
            return new FebsResponse().fail();
        }
        //是否订购业务功能
        Boolean isSub=this.isSub(junboChargeLog.getMiguOrderId(),junboChargeLog.getServiceId());
        //未订购
        if(!isSub){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,订单已退订或暂停");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            updateShopRightsStatus(junboChargeLog);
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }
    @Override
    public void rechargeForSchedule(JunboChargeLog junboChargeLog) {
        junboChargeLog=junboChargeLogService.taskRechargeForSchedule(junboChargeLog);
        updateShopRightsStatus(junboChargeLog);
    }
    /**
     * 更新订单领取状态
     * @param junboChargeLog
     */
    @Override
    public void updateRechargeState(JunboChargeLog junboChargeLog) {
        updateShopRightsStatus(junboChargeLog);
    }

    /**
     * 更新商城订单领取权益状态
     */
    private void updateShopRightsStatus(JunboChargeLog junboChargeLog) {
        //查询最新支付订单
        OrderPay order= orderPayService.lambdaQuery().eq(OrderPay::getOrderId,junboChargeLog.getMiguOrderId()).eq(OrderPay::getStatus,1).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
        if(order!=null){
            orderPayService.lambdaUpdate().eq(OrderPay::getId, order.getId()).set(OrderPay::getRightsStatus,junboChargeLog.getStatus()).update();
        }
    }
}
