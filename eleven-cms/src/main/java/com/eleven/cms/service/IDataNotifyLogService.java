package com.eleven.cms.service;

import com.eleven.cms.entity.DataNotifyLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 数据回执记录
 * @Author: jeecg-boot
 * @Date:   2023-04-23
 * @Version: V1.0
 */
public interface IDataNotifyLogService extends IService<DataNotifyLog> {

    void saveDataNotifyLog(String mobile, String source, String type, String companyOwner, String state,String desc, String operSystem, String finishedTime);
//    void saveDataNotifyLog(String mobile, String source, String status,String companyOwner,String state,String operSystem,String finishedTime,String desc);

    void receiveDataNotifyDelayMsg(String msisdn,String id,String extra);
}
