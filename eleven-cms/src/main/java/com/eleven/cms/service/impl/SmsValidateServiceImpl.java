package com.eleven.cms.service.impl;

import com.eleven.cms.config.*;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.SmsModel;
import com.eleven.cms.remote.LiantongVrbtService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.RandomUtils;
import com.eleven.qycl.util.QyclConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 短信验证码服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsValidateServiceImpl implements ISmsValidateService {

    @Autowired
    private RedisUtil redisUtil;
    //@Autowired
    //private ISmsService smsService;
    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private ThirdPartyChannelConfigProperties thirdPartyChannelConfigProperties;





    @Override
    public boolean create(String phone,String channelCode) throws JeecgBootException {
        //检查是否60秒类重复请求
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Long expire = redisUtil.getExpire(key);
        //key不存在返回-2
        if(expire>0 && BizConstant.SMS_CODE_TTL-expire<60){
            throw new JeecgBootException("操作频繁,请1分钟后再试");
        }
        String code = RandomUtils.randomCode();
        //log.info("生成短信验证码:{}",code);
        redisUtil.set(key, code, BizConstant.SMS_CODE_TTL);
//        //上海移动炫视视频彩铃订购单独发送订购短信
//        if(/*StringUtils.equals(channelCode,BizConstant.BIZ_TYPE_SHYD_XSSP)*/channelCode.contains(BizConstant.BIZ_TYPE_SHYD)){
////            return datangSmsService.sendShangHaiXuanShiTemplateNotice(phone,code);
//            String serviceId = BizConstant.getProductIdByChannel(channelCode);
//            if(StringUtils.isEmpty(serviceId)){
//                throw new JeecgBootException("渠道错误,短信发送失败");
//            }
//            return smsModelService.sendSms(phone,channelCode,serviceId,BizConstant.BUSINESS_TYPE_CODE,code);
//        }
        //企业视频彩铃发送订购验证码
        if(channelCode.contains(QyclConstant.QYCL_MIGU_CHANNEL_CODE_YRJY)){
            return smsModelService.sendSms(phone,channelCode,"QYCL",BizConstant.BUSINESS_TYPE_CODE,code);
        }
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (cmsCrackConfig == null) {
            throw new JeecgBootException("渠道错误,短信发送失败");
        }

        //return true;
        return datangSmsService.sendValidateNotice(phone, cmsCrackConfig.getSmsSignDatang(), cmsCrackConfig.getSmsSuffix(), code);
    }

    /**
     * 发送联通登录验证码
     * @param phone
     * @return
     * @throws JeecgBootException
     */
    @Override
    public boolean createLt(String phone) throws JeecgBootException {
        //检查是否60秒类重复请求
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Long expire = redisUtil.getExpire(key);
        //key不存在返回-2
        if(expire>0&& BizConstant.SMS_CODE_TTL-expire<60){
            throw new JeecgBootException("操作频繁,请1分钟后再试");
        }
        redisUtil.set(key, "000000", BizConstant.SMS_CODE_TTL);
        //return true;
        return SpringContextUtils.getBean(LiantongVrbtService.class).sendLoginCode(phone, BizConstant.BIZ_LT_CHANNEL_DEFAULT);
    }


    @Override
    public void check(String phone, String value) throws JeecgBootException {
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Object codeInRedis = redisUtil.get(key);
        //System.out.println("redis中取得的短信验证码 = " + codeInRedis);
        if (StringUtils.isBlank(value)) {
            throw new JeecgBootException("请输入短信验证码");
        }
        if (codeInRedis == null) {
            throw new JeecgBootException("短信验证码已过期,请重新获取");
        }
        if (!StringUtils.equalsIgnoreCase(value, String.valueOf(codeInRedis))) {
            throw new JeecgBootException("短信验证码错误");
        }
    }

    @Override
    public boolean createTy(String phone, String channelCode) {
        //检查是否60秒类重复请求
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Long expire = redisUtil.getExpire(key);
        //key不存在返回-2
        if(expire>0 && BizConstant.SMS_CODE_TTL-expire<60){
            throw new JeecgBootException("操作频繁,请1分钟后再试");
        }
        String code = RandomUtils.randomCode();
        //log.info("生成短信验证码:{}",code);
        redisUtil.set(key, code, BizConstant.SMS_CODE_TTL);
        //return true;
        return datangSmsService.sendTianyiNotice(phone, code);

    }

    @Override
    public boolean createThirdParty(String phone, String channelCode) throws JeecgBootException {
        //检查是否60秒类重复请求
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Long expire = redisUtil.getExpire(key);
        //key不存在返回-2
        if(expire>0 && BizConstant.SMS_CODE_TTL-expire<60){
            throw new JeecgBootException("操作频繁,请1分钟后再试");
        }
        String code = RandomUtils.randomCode();
        //log.info("生成短信验证码:{}",code);
        redisUtil.set(key, code, BizConstant.SMS_CODE_TTL);

        final ThirdPartyChannelConfig thirdPartyChannelConfig = thirdPartyChannelConfigProperties.getMiguChannelConfigMap()
                .get(channelCode);
        if(thirdPartyChannelConfig==null){
            throw new JeecgBootException("渠道错误,短信发送失败");
        }

        //return true;
        return datangSmsService.sendValidateNotice(phone,thirdPartyChannelConfig.getSmsSignDatang(), thirdPartyChannelConfig.getSmsSuffix(),code);

    }

    //@Override
    //public boolean sendNotify(String phone, String content) {
    //    return datangSmsService.sendNotify(phone, content);
    //}


    @Override
    public boolean createUnify(String phone,String channelCode,String serviceId) throws JeecgBootException {
        //检查是否60秒类重复请求
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Long expire = redisUtil.getExpire(key);
        //key不存在返回-2
        if(expire>0 && BizConstant.SMS_CODE_TTL-expire<60){
            throw new JeecgBootException("操作频繁,请1分钟后再试");
        }
        String code = RandomUtils.randomCode();
        redisUtil.set(key, code, BizConstant.SMS_CODE_TTL);
        //爱休闲集社发送短信
        if(StringUtils.equalsAny(channelCode,BizConstant.BIZ_QYLI_ALL_CHANNEL_CODE,BizConstant.BIZ_QYLI_COMIC_CHANNEL_CODE,BizConstant.BIZ_QYLI_READ_CHANNEL_CODE)){
            return smsModelService.sendSms(phone,channelCode,BizConstant.SMS_MODEL_COMMON_SERVICE_ID,BizConstant.BUSINESS_TYPE_CODE,code);
        }



        if(StringUtils.equalsAny(channelCode,BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE)){
            SmsModel smsModel=smsModelService.getSmsModelbyChannel(channelCode,serviceId,BizConstant.BUSINESS_TYPE_CODE);
            if(smsModel==null){
                serviceId=BizConstant.BIZ_LHHY_QYB_990_PLUS_SERVICE_ID;
            }
            return smsModelService.sendSms(phone,channelCode,serviceId,BizConstant.BUSINESS_TYPE_CODE,code);
        }
        if(StringUtils.equalsAny(channelCode,BizConstant.BIZ_TYPE_QYCL)){
            return smsModelService.sendSms(phone, BizConstant.BIZ_TYPE_QYCL, BizConstant.BIZ_QYCL_SERVICE_ID, BizConstant.BUSINESS_TYPE_CODE, code);
        }
        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (cmsCrackConfig == null) {
            throw new JeecgBootException("渠道错误,短信发送失败");
        }
        return datangSmsService.sendValidateNotice(phone, cmsCrackConfig.getSmsSignDatang(), cmsCrackConfig.getSmsSuffix(), code);

    }


    /**
     * 通用短信模板，适用于新疆移动等不需要短信办理的业务生成短信
     *
     * @param phone
     * @return
     */
    @Override
    public boolean createUnifyTemplate(String phone) {
        //检查是否60秒类重复请求
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Long expire = redisUtil.getExpire(key);
        //key不存在返回-2
        if (expire > 0 && BizConstant.SMS_CODE_TTL - expire < 60) {
            throw new JeecgBootException("操作频繁,请1分钟后再试");
        }
        String code = RandomUtils.randomCode();
        //log.info("生成短信验证码:{}",code);
        redisUtil.set(key, code, BizConstant.SMS_CODE_TTL);
        //return true;
        return datangSmsService.sendUnifyTemplateNotice(phone, code);

    }


    @Override
    public boolean rightsCreate(String phone,String channelCode,String serviceId) throws JeecgBootException {
        //检查是否60秒类重复请求
        String key = BizConstant.SMS_CODE_KEY_PREFIX + phone;
        Long expire = redisUtil.getExpire(key);
        //key不存在返回-2
        if(expire>0 && BizConstant.SMS_CODE_TTL-expire<60){
            throw new JeecgBootException("操作频繁,请1分钟后再试");
        }
        String code = RandomUtils.randomCode();
        redisUtil.set(key, code, BizConstant.SMS_CODE_TTL);


        CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(channelCode);
        if (cmsCrackConfig != null) {
            serviceId = cmsCrackConfig.getServiceId();
        }else if(StringUtils.equalsAny(channelCode,BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE)){
            SmsModel smsModel=smsModelService.getSmsModelbyChannel(channelCode,serviceId,BizConstant.BUSINESS_TYPE_CODE);
            if(smsModel==null){
                serviceId=BizConstant.BIZ_LHHY_QYB_990_PLUS_SERVICE_ID;
            }
        }
        return smsModelService.sendSms(phone,channelCode,serviceId,BizConstant.BUSINESS_TYPE_CODE,code);
    }
}
