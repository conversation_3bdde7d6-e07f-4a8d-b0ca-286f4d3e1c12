package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.WyyMmOrder;
import com.eleven.cms.mapper.WyyMmOrderMapper;
import com.eleven.cms.service.IWyyMmOrderService;
import org.springframework.stereotype.Service;

/**
 * @Description: cms_wyy_mm_order
 * @Author: jeecg-boot
 * @Date:   2023-06-07
 * @Version: V1.0
 */
@Service
public class WyyMmOrderServiceImpl extends ServiceImpl<WyyMmOrderMapper, WyyMmOrder> implements IWyyMmOrderService {
    /**
     * 查询最新订购订单
     * @param mobile
     * @return
     */
    @Override
    public WyyMmOrder wyyMMIsMember(String mobile) {
        WyyMmOrder wyyMmOrder=this.lambdaQuery().eq(WyyMmOrder::getMobile,mobile).in(WyyMmOrder::getStatus,1,3).orderByDesc(WyyMmOrder::getCreateTime).last("limit 1").one();
        if(wyyMmOrder!=null && wyyMmOrder.getStatus().equals(1)){
            return wyyMmOrder;
        }
        return null;
    }
}
