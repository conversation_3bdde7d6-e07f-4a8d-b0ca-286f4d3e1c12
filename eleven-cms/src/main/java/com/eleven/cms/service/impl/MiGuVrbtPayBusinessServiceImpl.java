package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Alipay;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.MiguVrbtPayOrder;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.RemoteResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * 咪咕视频彩铃订阅号三方支付业务报备
 * @author: yang tao
 * @create: 2024-08-13 14:21
 */
@Slf4j
@Service
public class MiGuVrbtPayBusinessServiceImpl implements IBusinessCommonService {
    //支付成功
    public static final int SUCCESS_PAY_STATUS =1;
    //退款成功
    public static final int SUCCESS_REFUND_STATUS =4;
    //退款失败
    public static final int FAIL_REFUND_STATUS =5;
    //退款中
    private static final int REFUND_PREPARE=6;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IAlipayService alipayService;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    IMiguVrbtPayOrderService miguVrbtPayOrderService;
    private ObjectMapper mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        return SpringContextUtils.getBean(MiGuVrbtPayBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        String channel = subscribe.getChannel();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel());
            if (null == cmsCrackConfig) {
                log.info("咪咕视频彩铃三方支付发送验证码=>手机号:{},渠道号:{},未配置的渠道号!",mobile,subscribe.getChannel());
                return Result.error("发送短信验证码失败,请稍后再试");
            }
            RemoteResult remoteResult=miguApiService.vrbtMonthStatusQuery(mobile,channel);
            //已包月。不记录订单
            if (remoteResult.isVrbtMember()) {
                boolean result =  smsValidateService.rightsCreate(mobile, subscribe.getChannel(), cmsCrackConfig.getServiceId());
                if (result) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
                    return Result.noauth("短信发送成功",System.currentTimeMillis());
                }
                return Result.error("发送短信验证码失败,请稍后再试");
            }
            String orderId= IdWorker.get32UUID();
            subscribe.setIspOrderNo(orderId);
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            boolean result =  smsValidateService.rightsCreate(mobile, subscribe.getChannel(), cmsCrackConfig.getServiceId());
            if (result) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
                return Result.noauth("短信发送成功",subscribe.getId());
            }
            return Result.error("发送短信验证码失败,请稍后再试");
        } else {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            try {
                smsValidateService.check(mobile, smsCode);
            } catch (JeecgBootException e) {
                return Result.error(e.getMessage());
            }
            RemoteResult remoteResult=miguApiService.vrbtMonthStatusQuery(mobile,channel);
            //已包月直接设置铃音
            if (remoteResult.isVrbtMember()) {
                MiguVrbtPayOrder miguVrbtPayOrder=miguVrbtPayOrderService.lambdaQuery().eq(MiguVrbtPayOrder::getMobile,subscribe.getMobile()).eq(MiguVrbtPayOrder::getChannel,subscribe.getChannel()).in(MiguVrbtPayOrder::getOrderStatus,SUCCESS_PAY_STATUS,SUCCESS_REFUND_STATUS,FAIL_REFUND_STATUS,REFUND_PREPARE).orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
                if(miguVrbtPayOrder!=null && miguVrbtPayOrder.getRingType()!=null){
                    RemoteResult  remoteResultRing=miguApiService.vrbtToneFreeMonthOrder(subscribe.getMobile(),subscribe.getChannel(),subscribe.getCopyrightId(),subscribe.getContentId(),String.valueOf(miguVrbtPayOrder.getRingType()));       //支付成功
                    Integer subStatus=remoteResultRing.isOK()? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
                    miguVrbtPayOrderService.lambdaUpdate().eq(MiguVrbtPayOrder::getId,miguVrbtPayOrder.getId()).set(MiguVrbtPayOrder::getSubStatus,subStatus).set(MiguVrbtPayOrder::getRingId,subscribe.getContentId()).set(MiguVrbtPayOrder::getCopyRightId,subscribe.getCopyrightId()).set(MiguVrbtPayOrder::getUpdateTime, new Date()).update();
                    return Result.ok("设置成功！");
                }
                log.warn("咪咕视频彩铃三方支付开通铃音功能查询订单失败-subscribe:{}",subscribe);
                if(StringUtils.isNotBlank(subscribe.getRingType())){
                    miguApiService.vrbtToneFreeMonthOrder(subscribe.getMobile(),subscribe.getChannel(),subscribe.getCopyrightId(),subscribe.getContentId(),subscribe.getRingType());
                    return Result.ok("设置成功！");
                }
                return Result.bizExists("铃音类型错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = YIDONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", YIDONG_SMS_INVALID_CACHE_SECONDS);
            subscribe.setIspOrderNo(target.getIspOrderNo());
            subscribe.setId(transactionId);
            if (StringUtils.equalsAny(subscribe.getTradeType(), BizConstant.TRADE_TYPE_HTML,BizConstant.TRADE_TYPE_WECHAT)) {
                final Result<?> submitResult =qyclWxpayService.vrbtPay(subscribe.getMobile(),subscribe.getIspOrderNo(),subscribe.getSubject(), subscribe.getTradeType(), subscribe.getOpenId(), subscribe.getChannel(), subscribe.getSubChannel(),subscribe.getReturnUrl(),subscribe.getRingType(),subscribe.getContentId(),subscribe.getCopyrightId(),subscribe.getBizType(),subscribe.getRingName());
                Subscribe upd = new Subscribe();
                upd.setId(target.getId());
                //已提交验证码
                if(submitResult.isOK()){
                    upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                }else{
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                }
                upd.setResult(submitResult.getMessage());
                upd.setModifyTime(new Date());
                upd.setBizTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                return submitResult;
            }else{
                Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType, channel).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
                if (alipay == null) {
                    return Result.error("支付通道错误");
                }
                final Result<?> submitResult = alipayService.aliPayMiGuVrbt(subscribe.getMobile(),
                        target.getIspOrderNo(),
                        alipay.getSingleAmount(),
                        subscribe.getSubject(),subscribe.getTradeType(), alipay.getAppId(), channel, subscribe.getSubChannel(),subscribe.getReturnUrl(),alipay.getPayNotifyUrl(),subscribe.getRingType(),subscribe.getContentId(),subscribe.getCopyrightId(),subscribe.getBizType(),subscribe.getRingName());
                Subscribe upd = new Subscribe();
                upd.setId(target.getId());
                //已提交验证码
                if(submitResult.isOK()){
                    upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                }else{
                    upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                }
                upd.setResult(submitResult.getMessage());
                upd.setModifyTime(new Date());
                upd.setBizTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                submitResult.setOrderId(target.getIspOrderNo());
                return submitResult;
            }
        }
    }
}
