package com.eleven.cms.service.impl;

import com.eleven.cms.config.WxMpProperties;
import com.eleven.cms.service.ICmsAlarmUserService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.util.RawValue;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collection;

/**
 * 微信公众号(服务号)消息服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WxMpNotifyService {
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    @Autowired
    private Environment environment;
    private OkHttpClient client;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private WxMpProperties wxMpProperties;
    @Autowired
    private ICmsAlarmUserService cmsAlarmUserService;

    private ObjectMapper mapper;

    public static final String REDIS_KEY_PREFIX_WX_MP_ACCESS_TOKEN = "wx:mp:accessToken:";


    @PostConstruct
    public void init(){
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator
            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.testProxyHost, OkHttpClientUtils.testProxyPort)))
                    //.proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper();
    }

    /**
     * 获取accessToken
     * @return
     */
    public String fetchAccessToken() {
        final String appId = wxMpProperties.getAppId();
        String redisKey = REDIS_KEY_PREFIX_WX_MP_ACCESS_TOKEN + appId;
        String accessTokenInRedis = (String)redisUtil.get(redisKey);
        if(StringUtils.isNotEmpty(accessTokenInRedis)){
            return accessTokenInRedis;
        }
        try {
            ObjectNode dataNode = mapper.createObjectNode()
                    .put("grant_type","client_credential")
                    .put("appid", appId)
                    .put("secret", wxMpProperties.getSecret())
                    .put("force_refresh", false);
            //System.out.println(dataNode.toPrettyString());
            RequestBody body = RequestBody.create(JSON, dataNode.toString());
            Request request = new Request.Builder().url(wxMpProperties.getAccessTokenUrl())
                    .post(body)
                    .build();
            Response response = client.newCall(request).execute();
            String content = response.body().string();
            log.info("微信公众号获取AccessToken响应:{}", content);
            final JsonNode tree = mapper.readTree(content);
            String accessToken = tree.at("/access_token").asText();
            long expiresIn = tree.at("/expires_in").asLong();
            redisUtil.set(redisKey, accessToken, expiresIn - 5 > 0 ? expiresIn - 5 : expiresIn);

            return accessToken;

        } catch (Exception e) {
            log.info("微信公众号接口调用异常:", e);
        }

        return "";
    }

    /**
     * 发送模板消息
     * @param openId
     * @param templateId 模板id
     * @param paramJson  微信模板变量替换后的json，示例:
     *                   "{\"thing2\":{\"value\":\"渠道号HY_25_CW\"},\"thing7\":{\"value\":\"省份四川\"},\"thing3\":{\"value\":\"已经到达限量,限量数250\"}}";
     * @return
     */
    public boolean sendTemplateMessage(String openId, String templateId, String paramJson) {
        try {
            ObjectNode dataNode = mapper.createObjectNode()
                    .put("touser",openId)
                    .put("template_id",templateId)
                    //.put("url", "")
                    .putRawValue("data", new RawValue(paramJson));
            //System.out.println(dataNode.toPrettyString());
            final String body = dataNode.toString();
            String accessToken = fetchAccessToken();
            Request request = new Request.Builder().url(wxMpProperties.getSendTemplateMessageUrl()+accessToken)
                    .post(RequestBody.create(JSON, body))
                    .build();
            log.info("微信公众号发送模板消息请求->openId:{},body:{}", openId, body);
            Response response = client.newCall(request).execute();
            String content = response.body().string();
            log.info("微信公众号发送模板消息响应->openId:{},响应:{}", openId, content);
            //{
            //    "errcode":0,
            //     "errmsg":"ok",
            //     "msgid":200228332
            //  }
            return  mapper.readTree(content).at("/errcode").asInt(999) == 0;

        } catch (Exception e) {
            log.info("微信公众号接口调用异常:", e);
        }

        return false;
    }

    /**
     * 发送限量通知
     * @param mobile 手机号
     * @param channelCode 示例：HY_25_CW
     * @param province  示例：四川
     * @param limitDesc  示例： 已达限量,限250；即将限量,限250,当前230
     * @return
     */
    public void sendLimitMessage(String mobile, String channelCode, String province, String limitDesc) {
        final String validChannel = StringUtils.substring("渠道号"+channelCode, 0, 20);
        final String validProvince = StringUtils.substring("省份"+province, 0, 20);
        final String validLimitDesc = StringUtils.substring(limitDesc, 0, 20);
        String paramJson = "{\"thing2\":{\"value\":\"" + validChannel  + "\"},\"thing7\":{\"value\":\"" + validProvince + "\"},\"thing3\":{\"value\":\"" + validLimitDesc + "\"}}";
        sendMessage(mobile, paramJson);
    }

    /**
     * 发送权益类告警通知
     * @param mobile 手机号
     * @param rightsId 示例：权益
     * @param rightsType  
     * @param rightsDesc
     * @return
     */
    public void sendRightsMessage(String mobile, String rightsId, String rightsType, String rightsDesc) {
        final String validChannel = StringUtils.substring("权益Id->"+rightsId, 0, 20);
        final String validProvince = StringUtils.substring(rightsType, 0, 20);
        final String validLimitDesc = StringUtils.substring(rightsDesc, 0, 20);
        String paramJson = "{\"thing2\":{\"value\":\"" + validChannel  + "\"},\"thing7\":{\"value\":\"" + validProvince + "\"},\"thing3\":{\"value\":\"" + validLimitDesc + "\"}}";
        sendMessage(mobile, paramJson);
    }

    /**
     * 发送g通知
     * @param mobile 手机号
     * @param channelCode 示例：HY_25_CW
     * @param alertTypeDesc  示例：业务开通发生告警
     * @param alertMessage  示例： 连续N条订购失败
     * @return
     */
    public void sendAlertMessage(String mobile, String channelCode, String alertTypeDesc, String alertMessage) {
        final String validChannel = StringUtils.substring("渠道号"+channelCode, 0, 20);
        final String validAlertTypeDesc = StringUtils.substring(alertTypeDesc, 0, 20);
        final String validAlertMessage = StringUtils.substring(alertMessage, 0, 20);
        String paramJson = "{\"thing2\":{\"value\":\"" + validChannel  + "\"},\"thing7\":{\"value\":\"" + validAlertTypeDesc + "\"},\"thing3\":{\"value\":\"" + validAlertMessage + "\"}}";
        sendMessage(mobile, paramJson);
    }

    private void sendMessage(String mobile, String templateParamJson){
        //final String openId = wxMpProperties.getMobileOpenIdMap().get(mobile);
        final String openId = cmsAlarmUserService.findOpenIdByPhone(mobile);
        if(StringUtils.isNotEmpty(openId)){
            sendTemplateMessage(openId, wxMpProperties.getTemplateMessageId(), templateParamJson);
        }else {
            log.warn("微信公众号发送模板消息,手机号{}未配置openId",mobile);
        }
    }

    /**
     * 批量发送限量通知
     * @param mobileList 手机号列表
     * @param channelCode 示例：HY_25_CW
     * @param province  示例：四川
     * @param limitDesc  示例： 已达限量,限250；即将限量,限250,当前230
     * @return
     */
    public void batchSendLimitMessage(Collection<String> mobileList, String channelCode, String province, String limitDesc) {
        mobileList.stream().forEach(mobile -> {
            sendLimitMessage(mobile, channelCode, province, limitDesc);
        });
    }


}
