package com.eleven.cms.service.impl;

import com.eleven.cms.entity.VrbtProvinceSwitchConfig;
import com.eleven.cms.mapper.VrbtProvinceSwitchConfigMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IVrbtProvinceSwitchConfigService;
import com.eleven.cms.util.BizConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * @Description: cms_vrbt_province_switch_config
 * @Author: jeecg-boot
 * @Date:   2021-08-16
 * @Version: V1.0
 */
@Slf4j
@Service
public class VrbtProvinceSwitchConfigServiceImpl extends ServiceImpl<VrbtProvinceSwitchConfigMapper, VrbtProvinceSwitchConfig> implements IVrbtProvinceSwitchConfigService {

    @Autowired
    IVrbtProvinceSwitchConfigService vrbtProvinceSwitchConfigService;
    
    private static final ObjectMapper MAPPER = new ObjectMapper().disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    /**
     * 根据省份切换要订购的渠道(开放/彩铃中心)
     * @param province
     * @return  开放/彩铃中心的咪咕渠道号
     */
    @Cacheable(cacheNames = CacheConstant.CMS_VRBT_PROVINCE_SWITCH_CONFIG_CACHE,key = "#root.methodName + ':' + #p0 + ':' + #p1",condition = "#p0!=null",unless = "#result==null")
    @Override
    public String vrbtMiguChannelSwitch(String orginChannel, String province) {
//        if(true){
//            return orginChannel;
//        }
        List<VrbtProvinceSwitchConfig> all = vrbtProvinceSwitchConfigService.findAll();
        if (all.size() < 2) {
            log.error("视频彩铃渠道切换配置错误,请去后台配置,默认使用渠道包的渠道号!");
            return orginChannel;
        }
        //优先渠道
        Optional<VrbtProvinceSwitchConfig> priorCfg = all.stream().filter(
                it -> BizConstant.YES.equals(it.getIsPrior())).findFirst();
        //次要渠道
        Optional<VrbtProvinceSwitchConfig> minorCfg = all.stream().filter(
                it -> BizConstant.NO.equals(it.getIsPrior())).findFirst();
        //缺省渠道
        Optional<VrbtProvinceSwitchConfig> defaultCfg = all.stream().filter(
                it -> BizConstant.YES.equals(it.getIsDefault())).findFirst();
        if (!priorCfg.isPresent() || !minorCfg.isPresent() || !defaultCfg.isPresent()) {
            log.error("视频彩铃渠道切换配置错误,请去后台配置,默认使用渠道包的渠道号!");
            return orginChannel;
        }
        TypeReference<Map<String, Boolean>> mapTypeReference = new TypeReference<Map<String, Boolean>>() {
        };
        if(StringUtils.isEmpty(province)){
            return defaultCfg.get().getMiguChannel();
        }
        Predicate<Map.Entry<String, Boolean>> entryPredicate = entry -> Strings.isNotEmpty(entry.getKey()) && province.contains(entry.getKey()) && entry.getValue();
        //先判定是否优先渠道省份
        try {
            boolean priorMatch = MAPPER.readValue(priorCfg.get().getProvinceJson(),
                    mapTypeReference).entrySet().stream().anyMatch(entryPredicate);
            if (priorMatch) {
                return priorCfg.get().getMiguChannel();
            }
            boolean minorMatch = MAPPER.readValue(minorCfg.get().getProvinceJson(),
                    mapTypeReference).entrySet().stream().anyMatch(entryPredicate);
            if (minorMatch) {
                return minorCfg.get().getMiguChannel();
            }
            return defaultCfg.get().getMiguChannel();
        } catch (Exception e) {
            log.error("视频彩铃渠道切换配置JSON错误,默认使用渠道包的渠道号!", e);
        }
        return orginChannel;
    }

    @Cacheable(cacheNames = CacheConstant.CMS_VRBT_PROVINCE_SWITCH_CONFIG_CACHE,key = "#root.methodName",condition = "#p0!=null",unless = "#result==null")
    @Override
    public List<VrbtProvinceSwitchConfig> findAll() {
        return list();
    }
}
