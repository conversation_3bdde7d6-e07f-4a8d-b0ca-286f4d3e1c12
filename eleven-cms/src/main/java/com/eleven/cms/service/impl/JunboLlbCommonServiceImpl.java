package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.remote.JunboLlbService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.BizPromoteUtils;
import com.eleven.cms.vo.JunboLlbResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: cai lei
 * @create: 2024-08-13 14:21
 */
@Slf4j
@Service("junboLlbCommonService")
public class JunboLlbCommonServiceImpl implements IBizCommonService {

    @Autowired
    JunboLlbService junboLlbService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        if (BizPromoteUtils.isSXYDJunBoAICaiLingTimeLimit(subscribe)) {
            return Result.msgTimeRestrict();
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        String ispOrderNo = BizConstant.BIZ_TYPE_HETU.equals(subscribe.getBizType())?JunboLlbService.getSCHTSysOrderId():JunboLlbService.getSysOrderId();
        JunboLlbResult junboLlbResult = junboLlbService.getSms(subscribe.getMobile(), ispOrderNo, subscribe.getChannel(), subscribe.getUserAgent(), subscribe.getReferer(), subscribe.getSource());
        if (junboLlbResult.isOperationOk()) {
            subscribe.setIspOrderNo(ispOrderNo);
            subscribeService.createSubscribeDbAndEs(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+junboLlbResult.getCode()+"\",\"message\":\""+junboLlbResult.getData().getMsg()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Autowired
    IEsDataService esDataService;
    @Autowired
    ISubscribeService iSubscribeService;

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (!redisUtil.setIfAbsent(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS)) {
            return Result.error("请勿重复提交");
        }

        // 记录用户行为-提交短信验证码
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        upd.setModifyTime(new Date());
        upd.setOpenTime(new Date());
        upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
        upd.setResult("成功");
        subscribeService.updateSubscribeDbAndEs(upd);

        // 调用第三方下单接口
        JunboLlbResult junboLlbResult = junboLlbService.handleOrder(mobile, subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getUserAgent(), subscribe.getReferer(), subscribe.getSource());
        if (junboLlbResult.isOperationOk()) {
            return Result.ok("提交验证码成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            if(junboLlbResult.getData()!=null){
                upd.setResult(junboLlbResult.getData().getMsg());
            }else{
                upd.setResult(junboLlbResult.getMessage());
            }

            boolean flag = iSubscribeService.saveOrUpdate(upd);
            log.info("提交验证码失败,手机号：{}, 订单信息：{}, flag：{}", subscribe.getMobile(), upd, flag);
            if (flag) {
                log.info("开始更新ES，手机号：{}", subscribe.getMobile());
                esDataService.saveOrUpdateSubscribe(upd);
            }
//            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("提交验证码失败");
        }
    }
}
