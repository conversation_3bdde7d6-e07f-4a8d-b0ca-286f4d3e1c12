package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PageBusinessProvinceConfig;
import com.eleven.cms.mapper.PageBusinessProvinceConfigMapper;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IPageBusinessProvinceConfigService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2023-08-09 10:12
 */
@Service
public class PageBusinessProvinceConfigServiceImpl extends ServiceImpl<PageBusinessProvinceConfigMapper, PageBusinessProvinceConfig> implements IPageBusinessProvinceConfigService {

    @Autowired
    MobileRegionService mobileRegionService;

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_PAGE_CONFIG_LINK_CACHE, key = "#root.methodName + '-' + #pageId+'-'+ #isp+'-' + #province", unless = "#result==null")
    public String getLinkByPageIdAndIspProvince(String pageId, String isp, String province) {
        PageBusinessProvinceConfig pageBusinessProvinceConfig = lambdaQuery().eq(PageBusinessProvinceConfig::getPageBusinessConfigId, pageId).eq(PageBusinessProvinceConfig::getProvince, province).eq(PageBusinessProvinceConfig::getStatus, 1).one();
        if (pageBusinessProvinceConfig != null) {
            if (MobileRegionResult.ISP_DIANXIN.equals(isp)) {
                return pageBusinessProvinceConfig.getDianxinLink();
            } else if (MobileRegionResult.ISP_LIANTONG.equals(isp)) {
                return pageBusinessProvinceConfig.getLiantongLink();
            } else if (MobileRegionResult.ISP_YIDONG.equals(isp)) {
                return pageBusinessProvinceConfig.getYidongLink();
            } else {
                return null;
            }
        }
        return null;
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_PAGE_CONFIG_LINK_CACHE, key = "#root.methodName + '-' + #pageId+'-'+ #isp+'-' + #province", unless = "#result==null")
    public String getSpareLinkByPageIdAndIspProvince(String pageId, String isp, String province) {
        PageBusinessProvinceConfig pageBusinessProvinceConfig = lambdaQuery().eq(PageBusinessProvinceConfig::getPageBusinessConfigId, pageId).eq(PageBusinessProvinceConfig::getProvince, province).eq(PageBusinessProvinceConfig::getStatus, 1).one();
        if (pageBusinessProvinceConfig != null && MobileRegionResult.ISP_YIDONG.equals(isp)) {
            return pageBusinessProvinceConfig.getYidongSpareLink();
        }
        return null;
    }
}
