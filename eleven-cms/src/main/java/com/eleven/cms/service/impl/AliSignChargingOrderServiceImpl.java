package com.eleven.cms.service.impl;

import com.alipay.api.response.AlipayFundTransOrderQueryResponse;
import com.alipay.api.response.AlipayFundTransToaccountTransferResponse;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.*;
import com.eleven.cms.mapper.AliSignChargingOrderMapper;
import com.eleven.cms.remote.ChangShaAliPayNotifyService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.IvrResult;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: cms_ali_sign_charging_order
 * @Author: jeecg-boot
 * @Date:   2022-12-30
 * @Version: V1.0
 */
@Service
@Slf4j
public class AliSignChargingOrderServiceImpl extends ServiceImpl<AliSignChargingOrderMapper, AliSignChargingOrder> implements IAliSignChargingOrderService {
    //未支付
    private static final Integer NO_PAY_STATUS=0;
    //已支付
    private static final Integer PAY_STATUS_SUCCESS=1;
    //退款失败
    private static final Integer REFUND_FAIL=2;
    //未退款
    private static final Integer NOT_REFUND=0;
    //退款成功
    private static final Integer REFUND_SUCCESS=1;
    //退款中
    private static final Integer REFUND_PREPARE=-1;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private IAlipayService alipayService;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private IAlipayComplainService alipayComplainService;
    @Autowired
    private IAliSignRecordService aliSignRecordService;
    @Autowired
    ChangShaAliPayNotifyService changShaAliPayNotifyService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
//    @Async
    public void updateStatusAdFeedback(AliSignChargingOrder aliSignChargingOrder,String businessType,String outTradeNo) {
        try{
            if(StringUtils.equalsAny(businessType,BizConstant.BIZ_LHHY_QYB_010_SERVICE_ID,BizConstant.BIZ_LHHY_QYB_010_PLUS_SERVICE_ID)){
                //修改渠道订单表状态
                Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo,outTradeNo).last("limit 1").one();
                if(subscribe!=null){
                    Subscribe upd = new Subscribe();
                    upd.setId(subscribe.getId());
                    upd.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
                    upd.setResult("付费成功");
                    subscribeService.updateSubscribeDbAndEs(upd);
                }

                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
            }else{
                List<AliSignChargingOrder> aliSignChargingOrders = this.lambdaQuery()
                        .eq(AliSignChargingOrder::getExternalAgreementNo, aliSignChargingOrder.getExternalAgreementNo())
                        .eq(AliSignChargingOrder::getOrderStatus,1)
                        .list();
                if(aliSignChargingOrders != null){
                    Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, aliSignChargingOrders.get(0).getExternalAgreementNo()).last("limit 1").one();
                    if(subscribe!=null){
                        Subscribe upd = new Subscribe();
                        upd.setId(subscribe.getId());
                        upd.setResult("付费成功" + aliSignChargingOrders.size());
                        if(aliSignChargingOrders.size() == 1){
                            upd.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
                            //信息流广告转化上报
                            channelService.AdEffectFeedbackNew(subscribe, upd.getStatus());
                        }
                        //更新渠道订单订购结果以及订单状态
                        subscribeService.updateSubscribeDbAndEs(upd);
                    }
                }
            }

        }catch (Exception e){
            log.error("支付宝签约首次扣款回传出错,手机号:{},订单号:{}",aliSignChargingOrder.getMobile(),aliSignChargingOrder.getOrderNo(),e);
        }
    }
    @Override
    public Result<?> aliPayRefund(String outTradeNo, String refund){
        String outRequestNo = IdWorker.get32UUID();
        AliSignChargingOrder orderPay=this.lambdaQuery()
                .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
                .eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS)
                .eq(AliSignChargingOrder::getRefundStatus,NOT_REFUND)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        Double refunds=Double.valueOf(refund);
        Double totalFee=Double.valueOf(orderPay.getOrderAmount());
        if(refunds>totalFee){
            return Result.error("退款金额大于支付金额！");
        }
        if(StringUtils.isBlank(orderPay.getAppId())){
            return Result.error("退款订单暂不支持！");
        }
        AlipayTradeRefundResponse response= alipayService.alipayRefund(orderPay.getOrderNo(),refund,outRequestNo,orderPay.getAppId());
        String refundRemark="退款中！";
        if(response!=null){
            String fundChange=StringUtils.isNotBlank(response.getFundChange())?response.getFundChange():"Z";
            refundRemark=StringUtils.isNotBlank(response.getSubMsg())?response.getSubMsg()+":"+fundChange:response.getMsg()+":"+fundChange;
        }
        if(response.isSuccess() && response.getFundChange().equals("Y")){
            upadateRefundStatus(outTradeNo, refund, outRequestNo, "退款成功！", REFUND_SUCCESS,orderPay.getExternalAgreementNo());
            return Result.ok("退款成功！");
        }
        upadateRefundStatus(outTradeNo, refund, outRequestNo, refundRemark, REFUND_FAIL,orderPay.getExternalAgreementNo());
        return Result.error(refundRemark);
    }
//    @Override
//    public Result<?> aliPayRefundByMobile(String mobile, String refund){
//        String outRequestNo = IdWorker.get32UUID();
//        LocalDateTime start = LocalDateTime.of( LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
//        LocalDateTime end=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
//        AliSignChargingOrder orderPay=this.lambdaQuery()
//                .eq(AliSignChargingOrder::getMobile, mobile)
//                .between(AliSignChargingOrder::getCreateTime, start, end)
//                .in(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS)
//                .in(AliSignChargingOrder::getRefundStatus, REFUND_FAIL,NOT_REFUND)
//                .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
//        if(orderPay==null){
//            return Result.error("本月订单不存在！");
//        }
//        Double refunds=Double.valueOf(refund);
//        Double totalFee=Double.valueOf(orderPay.getOrderAmount());
//        if(refunds>totalFee){
//            return Result.error("退款金额大于支付金额！");
//        }
//        if(StringUtils.isBlank(orderPay.getAppId())){
//            return Result.error("退款订单暂不支持！");
//        }
//        AlipayTradeRefundResponse response= alipayService.alipayRefund(orderPay.getOrderNo(),refund,outRequestNo,orderPay.getAppId());
//        if(response.isSuccess()){
//            if(response.getFundChange().equals("N")){
//                upadateRefundStatus(orderPay.getOrderNo(), refund, outRequestNo, "正在退款！", REFUND_PREPARE,orderPay.getExternalAgreementNo());
//                return Result.ok("正在退款！");
//            }else  if(response.getFundChange().equals("Y")){
//                upadateRefundStatus(orderPay.getOrderNo(), refund, outRequestNo, "退款成功！", REFUND_SUCCESS,orderPay.getExternalAgreementNo());
//                return Result.error("退款成功！");
//            }
//        }
//        upadateRefundStatus(orderPay.getOrderNo(), refund, outRequestNo, "退款失败！", REFUND_FAIL,orderPay.getExternalAgreementNo());
//        return Result.error("退款失败！");
//    }
    @Override
    public Result<?> aliPayQueryRefund(String outTradeNo){
        AliSignChargingOrder orderPay=this.lambdaQuery()
                .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
                .eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS)
                .in(AliSignChargingOrder::getRefundStatus, REFUND_FAIL,REFUND_PREPARE)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        AlipayTradeFastpayRefundQueryResponse response= alipayService.alipayQueryRefund(orderPay.getOrderNo(),orderPay.getRefundOrderNo(),orderPay.getAppId());
        String refundRemark="退款失败！";
        if(response!=null){
            refundRemark=StringUtils.isNotBlank(response.getSubMsg())?response.getSubMsg():response.getMsg();
        }
        if(response.isSuccess() && response.getRefundStatus().equals("REFUND_SUCCESS")){
            upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(), REFUND_SUCCESS,orderPay.getExternalAgreementNo());
            return Result.error("退款成功！");
        }
        upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(), REFUND_FAIL,orderPay.getExternalAgreementNo());
        return Result.error(refundRemark);
    }

    /**
     * 更新退款状态和备注
     * @param outTradeNo
     * @param refundAmount
     * @param refundOrderNo
     * @param refundRemark
     * @param refundStatus
     */
    private void upadateRefundStatus(String outTradeNo, String refundAmount, String refundOrderNo, String refundRemark, Integer refundStatus,String externalAgreementNo) {
        this.lambdaUpdate()
                .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
                .set(AliSignChargingOrder::getRefundStatus, refundStatus)
                .set(AliSignChargingOrder::getRefundOrderNo, refundOrderNo)
                .set(AliSignChargingOrder::getRefundAmount, refundAmount)
                .set(AliSignChargingOrder::getRefundRemark, refundRemark)
                .set(AliSignChargingOrder::getRefundTime, new Date())
                .set(AliSignChargingOrder::getUpdateTime, new Date()).update();
        //更新投诉退款状态
        AlipayComplain alipayComplain=alipayComplainService.lambdaQuery().eq(AlipayComplain::getMerchantOrderNo, outTradeNo).orderByDesc(AlipayComplain::getCreateTime).last("limit 1").one();
        if(alipayComplain!=null){
            alipayComplainService.lambdaUpdate()
                    .eq(AlipayComplain::getMerchantOrderNo, outTradeNo)
                    .set(AlipayComplain::getRefundStatus, refundStatus)
                    .set(AlipayComplain::getUpdateTime, new Date()).update();
        }
        //更新签约退款状态
        AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, externalAgreementNo).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
        if(aliSignRecord!=null){
            StringBuffer sb = new StringBuffer();
            sb.append(aliSignRecord.getRemark());
            sb.append("订单号："+outTradeNo);
            aliSignRecordService.lambdaUpdate()
                    .eq(AliSignRecord::getExternalAgreementNo, externalAgreementNo)
                    .set(AliSignRecord::getRefundStatus, refundStatus)
                    .set(AliSignRecord::getRemark, sb.toString())
                    .set(AliSignRecord::getUpdateTime, new Date()).update();
        }
        //长沙支付宝退款通知接口
//        changShaAliPayNotifyService.refundNotify(outTradeNo,aliSignRecord.getBusinessType(),true);
    }

    @Override
    public FebsResponse alipayQueryChargingOrders(String startTime,String endTime,Integer orderStatus) {

        log.info("长沙权益对接(支付宝三方支付)请求参数：startTime:{},endTime:{},orderStatus:{}",startTime,endTime,orderStatus);
        FebsResponse febsResponse = new FebsResponse();
        List<AliSignChargingOrder> list = null;
//        List<String> businessTypes = new ArrayList<>();
        try {
            Date startDateTime = DateUtils.parseDate(startTime, "yyyy-MM-dd HH:mm:ss");
            Date endDateTime = DateUtils.parseDate(endTime, "yyyy-MM-dd HH:mm:ss");
            if(startDateTime.after(endDateTime)){
                return febsResponse.fail().message("起始时间不能大于结束时间");
            }
//            businessTypes.add(BizConstant.BIZ_LHHY_QYB_990_SERVICE_ID);
//            businessTypes.add(BizConstant.BIZ_LHHY_QYB_1880_SERVICE_ID);
//            businessTypes.add(BizConstant.BIZ_MEMBER_1990_VRBT_SERVICE_ID);
            //支付成功
            if(orderStatus == 1){
                list = this.lambdaQuery().eq(AliSignChargingOrder::getOrderStatus, 1).between(AliSignChargingOrder::getPayTime, startDateTime, endDateTime).in(AliSignChargingOrder::getBusinessType,BizConstant.ROAD_ONE_LIST).list();
                febsResponse.success().data(list);
            }
            //已退款
            if(orderStatus == 2){
                list = this.lambdaQuery().eq(AliSignChargingOrder::getRefundStatus, 1).between(AliSignChargingOrder::getRefundTime, startDateTime, endDateTime).in(AliSignChargingOrder::getBusinessType,BizConstant.ROAD_ONE_LIST).list();
                febsResponse.success().data(list);
            }
            //已解约
            if(orderStatus == 3){
                list = this.lambdaQuery().eq(AliSignChargingOrder::getSignStatus, 3).between(AliSignChargingOrder::getUnsignTime, startDateTime, endDateTime).in(AliSignChargingOrder::getBusinessType,BizConstant.ROAD_ONE_LIST).list();
                febsResponse.success().data(list);
            }
            log.info("长沙权益对接(支付宝三方支付)响应数据条数：" + (list == null ? 0 : list.size()));
        }catch (Exception e){
            febsResponse.fail().message("系统错误");
            log.error("长沙权益对接(支付宝三方支付)出错：",e);
        }
        return febsResponse;
    }

    /**
     * 支付宝转账
     * @param outTradeNo
     * @param refund
     * @return
     */
    @Override
    public Result<?> aliPayTransferFee(String outTradeNo, String refund,String payeeType,String mobile,String businessType){
        String outBizNo = IdWorker.get32UUID();
        AliSignChargingOrder orderPay=this.lambdaQuery()
                .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
                .eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS)
                .eq(AliSignChargingOrder::getRefundStatus,NOT_REFUND)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        Double refunds=Double.valueOf(refund);
        Double totalFee=Double.valueOf(orderPay.getOrderAmount());
        if(refunds>totalFee){
            return Result.error("转账金额大于支付金额！");
        }
        if(StringUtils.isBlank(orderPay.getAppId())){
            return Result.error("转账订单暂不支持！");
        }


        AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, orderPay.getExternalAgreementNo()).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
        if(aliSignRecord==null){
            return Result.error("用户未签约！");
        }
        String payeeAccount="";
        if(StringUtils.equals("ALIPAY_USERID",payeeType)){
            payeeAccount=aliSignRecord.getAlipayUserId();
        }else if(StringUtils.equals("ALIPAY_LOGONID",payeeType)){
            payeeAccount=aliSignRecord.getMobile();
        }
        if(StringUtils.isNotBlank(mobile)){
            payeeType="ALIPAY_LOGONID";
            payeeAccount=mobile;
        }
        if(StringUtils.isEmpty(payeeAccount)){
            return Result.error("用户未签约！");
        }

        //查询订单渠道对应的支付的商户号名字
        Optional<Alipay> alipayOptional=alipayConfigService.aliPayRightsRechargeList().stream().filter(item-> orderPay.getBusinessType().equals(item.getBusinessType())).collect(Collectors.toList()).stream().max(Comparator.comparing(Alipay::getCreateTime));
        String remark="";
        if(alipayOptional.isPresent()){
            remark=getChannelMark(alipayOptional.get().getMchName());
        }
        AlipayFundTransToaccountTransferResponse response= alipayService.aliPayTransferFee(outBizNo,refund,payeeType,payeeAccount,businessType,"",remark);
//        AlipayFundTransUniTransferResponse response=alipayService.aliPayTransferFeeNew( outBizNo, refund,"ALIPAY_USER_ID",payeeAccount,"",businessType,remark,"true");
        String refundRemark="转账中！";
        if(response!=null){
            refundRemark=StringUtils.isNotBlank(response.getSubMsg())?response.getSubMsg():response.getMsg();
        }
        if(response.isSuccess() /*&& response.getStatus().equals("SUCCESS")*/){
            upadateRefundStatus(outTradeNo, refund, outBizNo, "转账成功！", REFUND_SUCCESS,orderPay.getExternalAgreementNo());
            return Result.ok("转账成功！");
        }
        upadateRefundStatus(outTradeNo, refund, outBizNo, refundRemark, REFUND_FAIL,orderPay.getExternalAgreementNo());
        return Result.error(refundRemark);
    }
    private String getChannelMark(String mchName) {
       try{
           //用正则表达式
           String reg = "[^0-9]";
           //Pattern类的作用在于编译正则表达式后创建一个匹配模式.
           Pattern p = Pattern.compile(reg);
           //Matcher类使用Pattern实例提供的模式信息对正则表达式进行匹配
           Matcher m = p.matcher(mchName);
           return m.replaceAll("").trim();
       }catch (Exception e){
           log.error("支付宝获取通道异常,商户号名字:{}",mchName);
       }
       return null;
    }




    @Override
    public Result<?> aliPayQueryTransferFee(String outTradeNo,String businessType){
        AliSignChargingOrder orderPay=this.lambdaQuery()
                .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
                .eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS)
                .in(AliSignChargingOrder::getRefundStatus, REFUND_FAIL,REFUND_PREPARE)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }

        AlipayFundTransOrderQueryResponse response= alipayService.aliPayQueryTransferFee(orderPay.getRefundOrderNo(),businessType);
//        AlipayFundTransCommonQueryResponse response=alipayService.aliPayQueryTransferFeeNew(orderPay.getRefundOrderNo(),businessType);
        String refundRemark="转账失败！";
        if(response!=null){
            refundRemark=StringUtils.isNotBlank(response.getSubMsg())?response.getSubMsg():response.getMsg();
        }
        if(response.isSuccess()){
            if(response.getStatus().equals("SUCCESS")){
                upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(), REFUND_SUCCESS,orderPay.getExternalAgreementNo());
                return Result.error("转账成功！");
            }else if(response.getStatus().equals("FAIL")){
                upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(), REFUND_FAIL,orderPay.getExternalAgreementNo());
                return Result.error(refundRemark);
            }else{
                upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(), REFUND_PREPARE,orderPay.getExternalAgreementNo());
                return Result.error(refundRemark);
            }
        }
        upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(), REFUND_FAIL,orderPay.getExternalAgreementNo());
        return Result.error(refundRemark);
    }



    /**
     * 更新退款状态和备注
     * @param outTradeNo
     * @param refundAmount
     * @param refundOrderNo
     * @param refundStatus
     */
    private void upadateRefundStatus(String outTradeNo, String refundAmount, String refundOrderNo, Integer refundStatus,String externalAgreementNo) {
        this.lambdaUpdate()
                .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
                .set(AliSignChargingOrder::getRefundStatus, refundStatus)
                .set(AliSignChargingOrder::getRefundOrderNo, refundOrderNo)
                .set(AliSignChargingOrder::getRefundAmount, refundAmount)
                .set(AliSignChargingOrder::getRefundTime, new Date())
                .set(AliSignChargingOrder::getUpdateTime, new Date()).update();
        //更新投诉退款状态
        AlipayComplain alipayComplain=alipayComplainService.lambdaQuery().eq(AlipayComplain::getMerchantOrderNo, outTradeNo).orderByDesc(AlipayComplain::getCreateTime).last("limit 1").one();
        if(alipayComplain!=null){
            alipayComplainService.lambdaUpdate()
                    .eq(AlipayComplain::getMerchantOrderNo, outTradeNo)
                    .set(AlipayComplain::getRefundStatus, refundStatus)
                    .set(AlipayComplain::getUpdateTime, new Date()).update();
        }
        //更新签约退款状态
        AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, externalAgreementNo).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
        if(aliSignRecord!=null){
            aliSignRecordService.lambdaUpdate().eq(AliSignRecord::getExternalAgreementNo, externalAgreementNo).set(AliSignRecord::getRefundStatus, refundStatus).set(AliSignRecord::getUpdateTime, new Date()).update();
        }
    }

    /**
     * 支付宝通知回传
     * @param outTradeNo
     * @param mobile
     * @param businessType
     */
    @Override
    public void aliPayCallback(String outTradeNo, String mobile, String businessType) {
        try{
            Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,businessType).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
            if(alipay==null){
                log.info("支付渠道已下架==>订单号:{},手机号:{},渠道号:{}",outTradeNo,mobile,businessType);
                return;
            }
            if(StringUtils.isBlank(alipay.getCallbackUrl())){
                log.info("回传地址未配置==>订单号:{},手机号:{},渠道号:{}",outTradeNo,mobile,businessType);
                return;
            }
            AliSignChargingOrder aliSignChargingOrder=this.lambdaQuery().eq(AliSignChargingOrder::getOrderNo, outTradeNo).eq(AliSignChargingOrder::getMobile, mobile).eq(AliSignChargingOrder::getCallbackStatus, 0).orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();;
            if(aliSignChargingOrder==null){
                log.info("支付订单不存在==>订单号:{},手机号:{},渠道号:{}",outTradeNo,mobile,businessType);
                return;
            }
            AliSignRecord aliSignRecord=aliSignRecordService.lambdaQuery().eq(AliSignRecord::getExternalAgreementNo, aliSignChargingOrder.getExternalAgreementNo()).eq(AliSignRecord::getMobile, mobile).orderByDesc(AliSignRecord::getCreateTime).last("limit 1").one();
            if(aliSignRecord==null){
                log.info("签约订单不存在==>订单号:{},手机号:{},渠道号:{},签约号:{}",outTradeNo,mobile,businessType,aliSignChargingOrder.getExternalAgreementNo());
                return;
            }

            String orderCreateTime= DateUtil.formatYearMonth(DateUtil.dateToLocalDateTime(aliSignChargingOrder.getCreateTime()));
            String signCreateTime=DateUtil.formatYearMonth(DateUtil.dateToLocalDateTime(aliSignRecord.getCreateTime()));
            Integer ratio=0;
            if(orderCreateTime.equals(signCreateTime)){
                ratio=alipay.getUpRatio();
                if(alipay.getIsCallback().equals(0)){
                    log.info("首月支付订单不回传==>订单号:{},手机号:{},渠道号:{},签约号:{},订单月份:{},签约月份:{}",outTradeNo,mobile,businessType,aliSignChargingOrder.getExternalAgreementNo(),orderCreateTime,signCreateTime);
                    return;
                }
            }else{
                ratio=alipay.getDownMonthUpRatio();
                if(alipay.getDownMonthIsCallback().equals(0)){
                    log.info("次月支付订单不回传==>订单号:{},手机号:{},渠道号:{},签约号:{},订单月份:{},签约月份:{}",outTradeNo,mobile,businessType,aliSignChargingOrder.getExternalAgreementNo(),orderCreateTime,signCreateTime);
                    return;
                }
            }
            ObjectNode info =mapper.createObjectNode();
            info.put("outTradeNo",outTradeNo);
            info.put("mobile",mobile);
            info.put("payStatus",aliSignChargingOrder.getOrderStatus());
            //支付成功扣量
            if(RandomUtils.isInRatio(ratio) && aliSignChargingOrder.getOrderStatus().equals(1)){
                log.info("支付通知回传成功==>订单号:{},手机号:{},渠道号:{},支付状态:{}",outTradeNo,mobile,businessType,aliSignChargingOrder.getOrderStatus());
                this.lambdaUpdate().eq(AliSignChargingOrder::getId, aliSignChargingOrder.getId()).set(AliSignChargingOrder::getCallbackStatus, 1).update();
                this.implementHttpPostResult(alipay.getCallbackUrl(),info,"支付通知回传");
                return;
            }else{
                if(!aliSignChargingOrder.getOrderStatus().equals(1)){
                    log.info("支付通知回传成功==>订单号:{},手机号:{},渠道号:{},支付状态:{}",outTradeNo,mobile,businessType,aliSignChargingOrder.getOrderStatus());
                    this.lambdaUpdate().eq(AliSignChargingOrder::getId, aliSignChargingOrder.getId()).set(AliSignChargingOrder::getCallbackStatus, 1).update();
                    this.implementHttpPostResult(alipay.getCallbackUrl(),info,"支付通知回传");
                    return;
                }
            }
        } catch (Exception e) {
            log.error("支付通知回传异常==>订单号:{},手机号:{},渠道号:{}",outTradeNo,mobile,businessType,e);
            return;
        }


    }


    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url, Object fromValue,String msg) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg);
    }
    public String push(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }


    /**
     * 支付宝人工转账
     * @param outTradeNo
     * @param refundRemark
     * @return
     */
    @Override
    public Result<?> aliPayHandMovementTransferFee(String outTradeNo, String refundRemark){
        String outBizNo = IdWorker.get32UUID();
        AliSignChargingOrder orderPay=this.lambdaQuery()
                .eq(AliSignChargingOrder::getOrderNo, outTradeNo)
                .eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS)
                .eq(AliSignChargingOrder::getRefundStatus,NOT_REFUND)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        upadateRefundStatus(outTradeNo, orderPay.getOrderAmount(), outBizNo, refundRemark, REFUND_SUCCESS,orderPay.getExternalAgreementNo());
        return Result.ok();
    }


    /**
     * 添加客服备注
     * @param outTradeNo
     * @param waiterRemark
     * @return
     */
    @Override
    public Result<?> aliPaySetWaiterRemark(String outTradeNo, String waiterRemark){
        this.lambdaUpdate().eq(AliSignChargingOrder::getOrderNo, outTradeNo).set(AliSignChargingOrder::getWaiterRemark, waiterRemark).set(AliSignChargingOrder::getUpdateTime, new Date()).update();
        return Result.ok();
    }

    /**
     * 电信视频彩铃退款
     * @param mobile
     * @return
     */
    @Override
    public Result<?> dianXinAliPayVrbtTransferFee(String mobile){
        AliSignChargingOrder orderPay=this.lambdaQuery().eq(AliSignChargingOrder::getMobile, mobile).eq(AliSignChargingOrder::getBizType, BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY).eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS).orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在");
        }
        if(!orderPay.getRefundStatus().equals(NOT_REFUND)){
            return Result.error("订单已退款");
        }
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,orderPay.getBusinessType()).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if(alipay!=null && alipay.getCustomerService().equals("1")){
            //转账
            return this.aliPayTransferFee(orderPay.getOrderNo(),orderPay.getOrderAmount(),"ALIPAY_USERID",null,orderPay.getBusinessType());
        }else{
            //退款
            return this.aliPayRefund(orderPay.getOrderNo(),orderPay.getOrderAmount());
        }
    }

    /**
     * 联通视频彩铃退款
     * @param mobile
     * @return
     */
    @Override
    public Result<?> lianTongAliPayVrbtTransferFee(String mobile){
        AliSignChargingOrder orderPay=this.lambdaQuery().eq(AliSignChargingOrder::getMobile, mobile).eq(AliSignChargingOrder::getBizType, BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY).eq(AliSignChargingOrder::getOrderStatus, PAY_STATUS_SUCCESS).orderByDesc(AliSignChargingOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在");
        }
        if(!orderPay.getRefundStatus().equals(NOT_REFUND)){
            return Result.error("订单已退款");
        }
        Alipay alipay = alipayConfigService.lambdaQuery().eq(Alipay::getBusinessType,orderPay.getBusinessType()).eq(Alipay::getIsValid, 1).orderByDesc(Alipay::getCreateTime).last("limit 1").one();
        if(alipay!=null && alipay.getCustomerService().equals("1")){
            //转账
            return this.aliPayTransferFee(orderPay.getOrderNo(),orderPay.getOrderAmount(),"ALIPAY_USERID",null,orderPay.getBusinessType());
        }else{
            //退款
            return this.aliPayRefund(orderPay.getOrderNo(),orderPay.getOrderAmount());
        }
    }
}
