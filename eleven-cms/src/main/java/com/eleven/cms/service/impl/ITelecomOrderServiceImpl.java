package com.eleven.cms.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.TelecomOrder;
import com.eleven.cms.mapper.TelecomOrderMapper;
import com.eleven.cms.service.ITelecomOrderService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class ITelecomOrderServiceImpl extends ServiceImpl<TelecomOrderMapper, TelecomOrder> implements ITelecomOrderService {

    @Override
    public Optional<TelecomOrder> getTelecomOrderByPhone(String phone) {
        Optional<TelecomOrder> telecomOrder=this.lambdaQuery()
                .eq(TelecomOrder::getPhone, phone)
                .between(TelecomOrder::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .list()
                .stream()
                //找到当月最后一个订单
                .max(Comparator.comparing(TelecomOrder::getCreateTime))
                //排除订单状态为退订
                .filter(order -> BizConstant.TYKJ_TYPE_BOOK != order.getType());
        return  telecomOrder;
    }

}
