package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.PushSubscribeService;
import com.eleven.cms.remote.SichuanMobileFlowPacketService;
import com.eleven.cms.remote.ZhongcaileyangService;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("sichuanYidongCommonService")
@Slf4j
public class SichuanYidongCommonServiceImpl implements IBizCommonService {

    @Autowired
    SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    ZhongcaileyangService zhongcaileyangService;


    @Override
    @ValidationLimit
    public Result filerCheck(Subscribe subscribe) {
        //中财乐扬重复订购校验
        if (ZYCL_CHANNEL_LIST.contains(subscribe.getChannel())) {
            boolean orderStatus = zhongcaileyangService.queryOrder(subscribe.getChannel(), subscribe.getMobile());
            if (orderStatus) {
                return Result.msgDuplicateLimit();
            }
        }
        return Result.ok();
    }

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(BIZ_TYPE_SCYD, subscribe.getReferer())) {
            log.error("手机号:{},业务类型:{},app:{}已被屏蔽", subscribe.getMobile(), BIZ_TYPE_SCYD, subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        //中财乐扬重复订购校验
        if (ZYCL_CHANNEL_LIST.contains(subscribe.getChannel())) {
            boolean orderStatus = zhongcaileyangService.queryOrder(subscribe.getChannel(), subscribe.getMobile());
            if (orderStatus) {
                return Result.msgDuplicateLimit();
            }
        }
        String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
        subscribe.setBizCode(bizCode);

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        subscribeService.createSubscribeDbAndEs(subscribe);
        BizLogUtils.logSubscribe(subscribe);
        final String id = subscribe.getId();
        final Result<Object> okResult = Result.ok("验证码已发送", id);
        Result<?> result=pushSubscribeService.pushScyd(subscribe);
        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);


        try {
            String errorMsg="{\"code\":\""+result.getCode()+"\",\"message\":\""+result.getMessage()+"\"}";
            return Result.okMsg("验证码已发送", id,errorMsg);
        } catch (Exception e) {
            log.error("获取验证码异常!-subscribe:{}",subscribe, e);
            return okResult;
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //此处保存已提交验证码
        if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setBizTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        return pushSubscribeService.pushSmsCode(subscribe.getId(), smsCode, subscribe.getMobile());
    }
}
