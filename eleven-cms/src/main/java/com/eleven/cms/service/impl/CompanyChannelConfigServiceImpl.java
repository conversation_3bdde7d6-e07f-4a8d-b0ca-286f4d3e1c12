package com.eleven.cms.service.impl;

import com.eleven.cms.entity.CompanyChannelConfig;
import com.eleven.cms.mapper.CompanyChannelConfigMapper;
import com.eleven.cms.service.ICompanyChannelConfigService;
import com.eleven.cms.vo.FebsResponse;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: cms_company_channel_config
 * @Author: jeecg-boot
 * @Date:   2023-06-09
 * @Version: V1.0
 */
@Service
public class CompanyChannelConfigServiceImpl extends ServiceImpl<CompanyChannelConfigMapper, CompanyChannelConfig> implements ICompanyChannelConfigService {

    @Override
    @Cacheable(cacheNames = CacheConstant.COMPANY_CHANNEL_CONFIG_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    public FebsResponse getCompanyChannelConfig(String key) {
        FebsResponse febsResponse = new FebsResponse();
        if(StringUtils.isBlank(key)){
            return febsResponse.fail().message("key不能为空");
        }
        CompanyChannelConfig companyChannelConfig = this.lambdaQuery().eq(CompanyChannelConfig::getConfigKey, key).one();
        return febsResponse.success().data(companyChannelConfig);
    }
}
