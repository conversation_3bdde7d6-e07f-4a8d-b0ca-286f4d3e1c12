package com.eleven.cms.service.impl;

import com.eleven.cms.config.LianLianFenXiaoProperties;
import com.eleven.cms.service.ILianLianFenXiaoService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.shanxi.AesUtil;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 联联分销充值产品
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/12 14:22
 **/
@Slf4j
@Service
public class LianLianFenXiaoServiceImpl implements ILianLianFenXiaoService {
    //购买套餐数量
    public static final Integer QUANTITY = 1;
    //支付方式 0:余额 1:授信
    public static final Integer PAY_TYPE=1;
    //配送筛选 (1:筛选,0或不填:不筛选)
    public static final Integer BOOKING_SHOW_ADDRESS=0;
    //是否需要身份证筛选 (1:筛选,0或不填:不筛选)
    public static final Integer ORDER_SHOW_ID_CARD=0;
    //是否需要填写日期筛选 (1:筛选,0或不填:不筛选)
    public static final Integer ORDER_SHOW_DATE=0;
    //排序 0.默认排序 1.根据开始时间排序
    public static final Integer PRODUCT_SORT_TYPE=0;

    @Autowired
    private LianLianFenXiaoProperties lianLianFenXiaoProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().connectTimeout(20L, TimeUnit.SECONDS).readTimeout(20L, TimeUnit.SECONDS).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    /**
     * 根据城市ID查询产品列表
     * @param cityCode 城市ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return
     */
    @Override
    public Result<?> queryProductListByCityCode(Integer cityCode,Integer pageNum,Integer pageSize) {
        ObjectNode node = mapper.createObjectNode();
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();

        contentNode.put("channelId",channelId);
        contentNode.put("cityCode",cityCode);
        contentNode.put("bookingShowAddress",BOOKING_SHOW_ADDRESS);
        contentNode.put("orderShowIdCard",ORDER_SHOW_ID_CARD);
        contentNode.put("orderShowDate",ORDER_SHOW_DATE);
        contentNode.put("productSortType",PRODUCT_SORT_TYPE);
        contentNode.put("pageNum",pageNum);
        contentNode.put("pageSize",pageSize);
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getQueryProductListUrl(), node,"联联分销查询产品列表","",contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销查询产品列表失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoProduct product = mapper.readValue(productJson, LianLianFenXiaoProduct.class);
                if(product!=null && product.getList()!=null && product.getList().size()>0){
                    return Result.ok("联联分销查询产品列表成功",product);
                }
                return Result.error("暂无产品");
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销查询产品列表失败");
    }

    /**
     * 根据产品ID和套餐ID(套餐ID可以不传)查询产品详细
     * @param productId 产品ID
     * @param itemId 套餐ID
     * @return
     */
    @Override
    public Result<?> queryProductDetailByProductId(String productId, String itemId) {
        ObjectNode node = mapper.createObjectNode();
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        contentNode.put("channelId",channelId);
        contentNode.put("productId",productId);
        if(StringUtil.isNotBlank(itemId) && itemId.contains("@")){
            contentNode.putPOJO("itemIdList",Arrays.asList(itemId.split("@")));
        }else if(StringUtil.isNotBlank(itemId) && !itemId.contains("@")){
            List<String> itemIdList= Lists.newLinkedList();
            itemIdList.add(itemId);
            contentNode.putPOJO("itemIdList",itemIdList);
        }
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getQueryProductDetailUrl(), node,"联联分销查询产品详细",productId,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销查询产品详细失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoProductDetail productDetail = mapper.readValue(productJson, LianLianFenXiaoProductDetail.class);
                return Result.ok("联联分销查询产品详细成功",productDetail);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销查询产品详细失败");
    }

    /**
     * 根据产品ID查询产品图文详情
     * @param productId 产品ID
     * @return
     */
    @Override
    public Result<?> queryProductImgByProductId(String productId) {
        ObjectNode node = mapper.createObjectNode();
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        contentNode.put("productId",productId);
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getQueryProductHtmlUrl(), node,"联联分销查询产品图文详情","",contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销查询产品图文详情失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoProductImg productImg = mapper.readValue(productJson, LianLianFenXiaoProductImg.class);
                return Result.ok("联联分销查询产品图文详情成功",productImg);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销查询产品图文详情失败");
    }
    /**
     * 根据城市ID查询城市店铺
     * @param cityCode 城市ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return
     */
    @Override
    public Result<?> queryShopListByByCityCode(Integer cityCode, Integer pageNum, Integer pageSize) {
        ObjectNode node = mapper.createObjectNode();
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        contentNode.put("cityCode",cityCode);
        contentNode.put("pageNum",pageNum);
        contentNode.put("pageSize",pageSize);

        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getQueryCityShopUrl(), node,"联联分销查询城市店铺","",contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销查询城市店铺失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoCityShop productImg = mapper.readValue(productJson, LianLianFenXiaoCityShop.class);
                return Result.ok("联联分销查询城市店铺成功",productImg);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销查询城市店铺失败");
    }
    /**
     * 根据（是否需要查询出层级关系：0-不需要,1-需要）查询产品分类
     * @param isTree 是否需要查询出层级关系：0-不需要,1-需要
     * @return
     */
    @Override
    public Result<?> queryProductClassByIsTree(Integer isTree) {
        ObjectNode node = mapper.createObjectNode();
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        contentNode.put("isTree",isTree);
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getQueryProductClassUrl(), node,"联联分销查询产品分类","",contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销查询产品分类失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoProductClass productImg = mapper.readValue(productJson, LianLianFenXiaoProductClass.class);
                return Result.ok("联联分销查询产品分类成功",productImg);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销查询产品分类失败");
    }

    /**
     * 验证-渠道订单创建条件
     * @param mobile 用户手机号
     * @param orderNo 第三方订单id(调用方自己的订单id,方便核对订单信息)
     * @param productId 产品编号id
     * @param itemId 套餐编号id
     * @param settlePrice 结算价(单位为分)
     * @param travelDate 游玩日期(yyyy-MM-dd HH:mm:ss)
     * @param idCard 身份证号
     * @param customerName 用户姓名
     * @param customerPhoneNumber 客户电话
     * @param address 地址
     * @param thirdSalePrice 第三方系统-销售价(单位为分)
     * @param memo 备注-下单客户备注信息
     * @return
     */
    @Override
    public Result<?> checkCreateOrder(String mobile,String orderNo, String productId, Integer itemId, Integer settlePrice, String travelDate, String idCard, String customerName, String customerPhoneNumber, String address, Integer thirdSalePrice, String memo) {
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();

        //第三方订单id(调用方自己的订单id,方便核对订单信息)
        contentNode.put("thirdPartyOrderNo",orderNo);
        //产品编号id
        contentNode.put("productId",productId);
        //套餐编号id
        contentNode.put("itemId",itemId);
        //结算价(单位为分)
        contentNode.put("settlePrice",settlePrice);
        //游玩日期(yyyy-MM-dd HH:mm:ss)
        if(StringUtil.isNotBlank(travelDate)){
            contentNode.put("travelDate",travelDate);
        }
        //身份证号
        if(StringUtil.isNotBlank(idCard)){
            contentNode.put("idCard",idCard);
        }
        //用户姓名
        contentNode.put("customerName",customerName);
        //客户电话
        contentNode.put("customerPhoneNumber",customerPhoneNumber);
        //地址
        if(StringUtil.isNotBlank(address)){
            contentNode.put("address",address);
        }
        //支付方式 0:余额 1:授信
        contentNode.put("payType",PAY_TYPE);
        //购买套餐数量
        contentNode.put("quantity",QUANTITY);
        //购买时间(yyyy-MM-dd HH:mm:ss)
        contentNode.put("purchaseTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        //第三方系统-销售价(单位为分)
        contentNode.put("thirdSalePrice",thirdSalePrice);
        //备注-下单客户备注信息
        if(StringUtil.isNotBlank(memo)){
            contentNode.put("memo",memo);
        }
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        ObjectNode node = mapper.createObjectNode();
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getCheckOrderUrl(), node,"联联分销验证渠道订单创建条件",mobile,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销验证渠道订单创建条件失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoCheckCreateOrder checkCreateOrder = mapper.readValue(productJson, LianLianFenXiaoCheckCreateOrder.class);
                log.info("联联分销验证渠道订单创建条件,响应数据=>手机号:{},请求参数:{},响应参数:{}",mobile,contentNode,checkCreateOrder);
                return Result.ok("联联分销验证渠道订单创建条件成功",checkCreateOrder);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销验证渠道订单创建条件失败");
    }

    /**
     * 渠道订单创建
     * @param mobile 用户手机号
     * @param validToken 核验token，调用“验证-渠道订单创建条件接口”返回
     * @param orderNo 第三方订单id(调用方自己的订单id,方便核对订单信息)
     * @param productId 产品编号id
     * @param itemId 套餐编号id
     * @param settlePrice 结算价(单位为分)
     * @param travelDate 游玩日期(yyyy-MM-dd HH:mm:ss)
     * @param idCard 身份证号
     * @param customerName 用户姓名
     * @param customerPhoneNumber 客户电话
     * @param address 地址
     * @param thirdSalePrice 第三方系统-销售价(单位为分)
     * @param memo 备注-下单客户备注信息
     * @return
     */
    @Override
    public Result<?> createOrder(String mobile,String validToken, String orderNo, String productId, Integer itemId, Integer settlePrice, String travelDate, String idCard, String customerName, String customerPhoneNumber, String address, Integer thirdSalePrice, String memo) {
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        //调用“验证-渠道订单创建条件接口”返回
        contentNode.put("validToken",validToken);
        //第三方订单id(调用方自己的订单id,方便核对订单信息)
        contentNode.put("thirdPartyOrderNo",orderNo);
        //产品编号id
        contentNode.put("productId",productId);
        //套餐编号id
        contentNode.put("itemId",itemId);
        //结算价(单位为分)
        contentNode.put("settlePrice",settlePrice);
        //游玩日期(yyyy-MM-dd HH:mm:ss)
        if(StringUtil.isNotBlank(travelDate)){
            contentNode.put("travelDate",travelDate);
        }
        //身份证号
        if(StringUtil.isNotBlank(idCard)){
            contentNode.put("idCard",idCard);
        }
        //用户姓名
        contentNode.put("customerName",customerName);
        //客户电话
        contentNode.put("customerPhoneNumber",customerPhoneNumber);
        //地址
        if(StringUtil.isNotBlank(address)){
            contentNode.put("address",address);
        }
        //支付方式 0:余额 1:授信
        contentNode.put("payType",PAY_TYPE);
        //购买套餐数量
        contentNode.put("quantity",QUANTITY);
        //购买时间(yyyy-MM-dd HH:mm:ss)
        contentNode.put("purchaseTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        //第三方系统-销售价(单位为分)
        contentNode.put("thirdSalePrice",thirdSalePrice);
        //备注-下单客户备注信息
        if(StringUtil.isNotBlank(memo)){
            contentNode.put("memo",memo);
        }
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        ObjectNode node = mapper.createObjectNode();
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getCreateOrderUrl(), node,"联联分销创建渠道订单",mobile,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销创建渠道订单失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoCreateOrder createOrder = mapper.readValue(productJson, LianLianFenXiaoCreateOrder.class);
                log.info("联联分销创建渠道订单,响应数据=>手机号:{},请求参数:{},响应参数:{}",mobile,contentNode,createOrder);
                return Result.ok("联联分销创建渠道订单成功",createOrder);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销创建渠道订单失败");
    }

    /**
     * 退款
     * @param mobile 用户手机号
     * @param orderNo 渠道订单号
     * @param orderList 小订单号列表
     * @return
     */
    @Override
    public Result<?> refundOrder(String mobile,String orderNo, List<String> orderList) {


        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        //渠道订单号
        contentNode.put("channelOrderNo",orderNo);
        //退款小订单号列表
        contentNode.putPOJO("orderNoList",orderList);

        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());

        ObjectNode node = mapper.createObjectNode();
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getRefundOrderUrl(), node,"联联分销订单退款",mobile,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销订单退款失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoRefundOrder refundOrder = mapper.readValue(productJson, LianLianFenXiaoRefundOrder.class);
                log.info("联联分销订单退款,响应数据=>手机号:{},请求参数:{},响应参数:{}",mobile,contentNode,refundOrder);
                if(refundOrder!=null && refundOrder.getRefundAmount()<=0){
                    if(!refundOrder.getRefundList().isEmpty()){
                        String refundReason=refundOrder.getRefundList().stream().filter(refund->refund.getRejectReasonType()!=-1).collect(Collectors.toList()).get(0).getRefundReason();
                        String rejectReason=refundOrder.getRefundList().stream().filter(refund->refund.getRejectReasonType()!=-1).collect(Collectors.toList()).get(0).getRejectReason();
                        return Result.error(refundReason+rejectReason);
                    }else  if(!refundOrder.getRejectRefundList().isEmpty()){
                        String refundReason=refundOrder.getRejectRefundList().stream().filter(refund->refund.getRejectReasonType()!=-1).collect(Collectors.toList()).get(0).getRefundReason();
                        String rejectReason=refundOrder.getRejectRefundList().stream().filter(refund->refund.getRejectReasonType()!=-1).collect(Collectors.toList()).get(0).getRejectReason();
                        return Result.error(refundReason+rejectReason);
                    }
                    return Result.error(result.getMessage());
                }
                return Result.ok("联联分销订单退款成功",refundOrder);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销订单退款失败");
    }

    /**
     * 刷新二维码
     * @param mobile 用户手机号
     * @param orderNo 渠道订单号
     * @return
     */
    @Override
    public Result<?> refreshCode(String mobile,String orderNo) {
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        //渠道编号
        contentNode.put("channelId",channelId);
        //渠道订单号
        contentNode.put("channelOrderId",orderNo);
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        ObjectNode node = mapper.createObjectNode();
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getSendCodeUrl(), node,"联联分销二维码刷新",mobile,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销二维码刷新失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoRefreshCode refreshCode = mapper.readValue(productJson, LianLianFenXiaoRefreshCode.class);
                log.info("联联分销二维码刷新,响应数据=>手机号:{},请求参数:{},响应参数:{}",mobile,contentNode,refreshCode);
                return Result.ok("联联分销二维码刷新成功",refreshCode);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销二维码刷新失败");
    }

    /**
     * 查询物流信息
     * @param mobile 用户手机号
     * @param orderId 小订单号
     * @return
     */
    @Override
    public Result<?> queryExpress(String mobile,String orderId) {
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        //渠道编号
        contentNode.put("channelId",channelId);
        //小订单号
        contentNode.put("orderId",orderId);
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        ObjectNode node = mapper.createObjectNode();
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getQueryLogisticsUrl(), node,"联联分销查询发货订单物流信息",mobile,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销查询发货订单物流信息失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoLogistics logistics = mapper.readValue(productJson, LianLianFenXiaoLogistics.class);
                log.info("联联分销查询发货订单物流信息,响应数据=>手机号:{},请求参数:{},响应参数:{}",mobile,contentNode,logistics);
                return Result.ok("联联分销查询发货订单物流信息成功",logistics);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销查询发货订单物流信息失败");
    }

    /**
     * 重发短信
     * @param mobile 用户手机号
     * @param orderNo 渠道订单号
     * @param orderList 小订单号列表
     * @return
     */
    @Override
    public Result<?> refreshSms(String mobile,String orderNo, List<String> orderList) {
        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        //渠道订单号
        contentNode.put("channelOrderId",orderNo);
        //小订单号列表
        contentNode.putPOJO("orderIdList",orderList);

        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        ObjectNode node = mapper.createObjectNode();
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getSendMsgUrl(), node,"联联分销短信重发",mobile,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销短信重发失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoSendMsg sendMsg = mapper.readValue(productJson, LianLianFenXiaoSendMsg.class);
                log.info("联联分销短信重发,响应数据=>手机号:{},请求参数:{},响应参数:{}",mobile,contentNode,sendMsg);
                return Result.ok("联联分销短信重发成功",sendMsg);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销短信重发失败");
    }

    /**
     * 查询订单详情
     * @param mobile 用户手机号
     * @param orderNo 渠道订单号
     * @return
     */
    @Override
    public Result<?> queryOrderDetail(String mobile, String orderNo) {

        String channelId=lianLianFenXiaoProperties.getChannelId();
        Long timestamp=System.currentTimeMillis();
        ObjectNode contentNode = mapper.createObjectNode();
        //渠道编号
        contentNode.put("channelId",channelId);
        //渠道订单号
        contentNode.put("channelOrderId",orderNo);
        String encryptedData=AesUtil.aesEncrypt(contentNode.toString(),lianLianFenXiaoProperties.getKey());
        ObjectNode node = mapper.createObjectNode();
        node.put("encryptedData",encryptedData);
        node.put("channelId",channelId);
        node.put("timestamp",timestamp);
        node.put("sign", DigestUtils.md5DigestAsHex((encryptedData+channelId+timestamp).getBytes(StandardCharsets.UTF_8)));
        String content =this.implementHttpPostResult(lianLianFenXiaoProperties.getQueryOrderInfoUrl(), node,"联联分销查询订单详情",mobile,contentNode);
        if(StringUtil.isEmpty(content)){
            return Result.error("联联分销查询订单详情失败");
        }
        try {
            LianLianFenXiaoResult result = mapper.readValue(content, LianLianFenXiaoResult.class);
            if(result.isOK()){
                String productJson=AesUtil.aesDecrypt(result.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
                LianLianFenXiaoOrderInfo orderInfo = mapper.readValue(productJson, LianLianFenXiaoOrderInfo.class);
                return Result.ok("联联分销查询订单详情成功",orderInfo);
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("联联分销查询订单详情失败");
    }


    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url, Object fromValue, String msg, String mobile,ObjectNode contentNode) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,mobile,contentNode.toString());
    }

    private String push(String url,String raw,String msg,String mobile,String contentNode) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,contentNode);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,contentNode,e);
            return null;
        }
    }
}
