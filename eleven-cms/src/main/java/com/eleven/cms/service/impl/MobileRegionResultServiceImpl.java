package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.ChuangLanProperties;
import com.eleven.cms.entity.MobileRegion;
import com.eleven.cms.mapper.MobileRegionResultMapper;
import com.eleven.cms.util.MobileRegionEnum;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.MobileRegionChuangLanResult;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: cms_ad_site
 * @Author: jeecg-boot
 * @Date:   2022-03-25
 * @Version: V1.0
 */
@Service
@Slf4j
public class MobileRegionResultServiceImpl extends ServiceImpl<MobileRegionResultMapper, MobileRegion> implements IMobileRegionResultService {
    @Autowired
    private BizProperties bizProperties;

    @Autowired
    private ChuangLanProperties chuangLanProperties;
    private static final Long MOBILE_TIME =86400L;
    @Autowired
    private RedisUtil redisUtil;
    OkHttpClient client = OkHttpClientUtils.getSingletonInstance();
    ObjectMapper mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    @Override
    public MobileRegionResult query(String mobile){
        MobileRegionResult mobileRegionResult=this.queryMobileRegionServer(mobile);
        if(mobileRegionResult!=null && mobileRegionResult.isIspYidong()){
            return mobileRegionResult;
        }
        MobileRegionResult mobileRegionSql=this.querySql(mobile);
        if(mobileRegionSql!=null && mobileRegionSql.isIspYidong()){
            return mobileRegionSql;
        }
        MobileRegionResult mobileRegionChuangLan=this.queryMobileChuangLan(mobile,mobileRegionResult);
        return mobileRegionChuangLan;
    }




    private MobileRegionResult queryMobileRegionServer(String mobile){
        String realKey = CacheConstant.CMS_MOBILE_REGION_CACHE+":"+mobile;
        if(redisUtil.hasKey(realKey)){
            MobileRegionResult result = null;
            try {
                result = mapper.readValue(redisUtil.get(realKey).toString(), MobileRegionResult.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return result;
        }
        HttpUrl httpUrl = HttpUrl.parse(bizProperties.getMobileRegionServerUrl())
                .newBuilder()
                .addQueryParameter("phone",mobile)
                .addQueryParameter("key",bizProperties.getMobileRegionApiKey())
                .build();
        Request request = new Request.Builder().url(httpUrl).build();
        try (Response response = client.newCall(request).execute()) {

            String jsonData = response.body().string();

            log.info("号码{},归属地查询结果:{}",mobile, StringUtils.trim(jsonData));

            MobileRegionResult regionResult=mapper.readerFor(MobileRegionResult.class).readValue(jsonData);
            ObjectNode objectNode = mapper.valueToTree(regionResult);
            redisUtil.set(realKey,objectNode.toString(), MOBILE_TIME);
            return regionResult;

        } catch (Exception e) {
            log.warn("手机号归属地查询失败=>号码:{},异常!",mobile,e);
            return null;
        }
    }


    private MobileRegionResult querySql(String mobile){
        String realKey = CacheConstant.CMS_MOBILE_REGION_SQL_CACHE+":"+mobile;
        if(redisUtil.hasKey(realKey)){
            MobileRegionResult result = null;
            try {
                result = mapper.readValue(redisUtil.get(realKey).toString(), MobileRegionResult.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return result;
        }
        boolean region=this.lambdaQuery().eq(MobileRegion::getPhoneCode,mobile).count()<=0;
        if(region){
            return null;
        }
        MobileRegion mobileRegion=this.lambdaQuery().select(MobileRegion::getPhoneCode,MobileRegion::getProvince,MobileRegion::getProvinceId,MobileRegion::getCity,MobileRegion::getCityCode,MobileRegion::getAreaCode,MobileRegion::getOperator,MobileRegion::getOptDesc).eq(MobileRegion::getPhoneCode,mobile).orderByDesc(MobileRegion::getCreateTime).last("limit 1").one();
        if (mobileRegion!=null){
            MobileRegionResult mobileRegionResult=new MobileRegionResult();
            mobileRegionResult.setCode("0000");
            mobileRegionResult.setMsg("成功");
            mobileRegionResult.setPhoneCode(mobile);
            mobileRegionResult.setProvince(mobileRegion.getProvince());
            mobileRegionResult.setProvinceId(mobileRegion.getProvinceId());
            mobileRegionResult.setCity(mobileRegion.getCity());
            mobileRegionResult.setCityCode(mobileRegion.getCityCode());
            mobileRegionResult.setAreaCode(mobileRegion.getAreaCode());
            mobileRegionResult.setOperator(mobileRegion.getOperator());
            mobileRegionResult.setOptDesc(mobileRegion.getOptDesc());

            ObjectNode objectNode = mapper.valueToTree(mobileRegionResult);
            redisUtil.set(realKey,objectNode.toString(), MOBILE_TIME);
            return mobileRegionResult;
        }
        return null;
    }



    private MobileRegionResult queryMobileChuangLan(String mobile,MobileRegionResult regionResult){
        String realKey = CacheConstant.CMS_MOBILE_REGION_CHUANGLAN_CACHE+":"+mobile;
        if(redisUtil.hasKey(realKey)){
            MobileRegionResult result = null;
            try {
                result = mapper.readValue(redisUtil.get(realKey).toString(), MobileRegionResult.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return result;
        }
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("appId",chuangLanProperties.getAppId())
                .addFormDataPart("appKey",chuangLanProperties.getAppKey())
                .addFormDataPart("mobile",mobile)
                .build();
        Request request = new Request.Builder()
                .url(chuangLanProperties.getMobileRegionServerUrl())
                .method("POST", body)
                .build();
        try (Response response = client.newCall(request).execute();) {
            String content = response.body().string();
            log.info("创蓝手机号归属地查询-号码{},归属地查询结果:{}",mobile, content);
            MobileRegionChuangLanResult result=mapper.readValue(content,MobileRegionChuangLanResult.class);
            if(result.isOK()){
                regionResult.setCode("0000");
                regionResult.setMsg("成功");
                regionResult.setPhoneCode(mobile);
                MobileRegionEnum mobileRegionEnum=MobileRegionEnum.getByAfter(result.getData().getQueryResult().get(0).getAfter());
//                if(mobileRegionEnum!=null){
//                    regionResult.setOperator(mobileRegionEnum.getOperator());
//                    regionResult.setOptDesc(mobileRegionEnum.getOptDesc());
//                }
                regionResult.setOperator(regionResult.getOperator());
                regionResult.setOptDesc(regionResult.getOptDesc());
                //判断数据是否存在
                boolean region=this.lambdaQuery().eq(MobileRegion::getPhoneCode,mobile).count()<=0;
                if(region){
                    MobileRegion mobileRegion=new MobileRegion();
                    mobileRegion.setPhoneCode(mobile);
                    if(regionResult!=null){
                        mobileRegion.setProvince(regionResult.getProvince());
                        mobileRegion.setProvinceId(regionResult.getProvinceId());
                        mobileRegion.setCity(regionResult.getCity());
                        mobileRegion.setCityCode(regionResult.getCityCode());
                        mobileRegion.setAreaCode(regionResult.getAreaCode());
                    }
                    if(mobileRegionEnum!=null){
                        mobileRegion.setReverseOperator(mobileRegionEnum.getOperator());
                        mobileRegion.setReverseOptDesc(mobileRegionEnum.getOptDesc());

                    }
                    mobileRegion.setOperator(regionResult.getOperator());
                    mobileRegion.setOptDesc(regionResult.getOptDesc());
                    mobileRegion.setIsResult(result.getData().getQueryResult().get(0).isResult()?1:0);
                    this.save(mobileRegion);
                }
                ObjectNode objectNode = mapper.valueToTree(regionResult);
                redisUtil.set(realKey,objectNode.toString(), MOBILE_TIME);
                return regionResult;
            }
            return null;
        } catch (Exception e) {
            log.warn("创蓝手机号归属地查询异常-号码:{}",mobile,e);
            return null;
        }
    }
}
