package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.VrbtChannelProvinceConfig;
import com.eleven.cms.entity.VrbtProvinceSwitchConfig;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * @Description: cms_vrbt_province_switch_config
 * @Author: jeecg-boot
 * @Date:   2021-08-16
 * @Version: V1.0
 */
public interface IVrbtChannelProvinceConfigService extends IService<VrbtChannelProvinceConfig> {

    List<String> getProvinceListByChannel(String channelCode);

    List<VrbtChannelProvinceConfig> findAll();

    boolean allow(String channelCode, String province);
}
