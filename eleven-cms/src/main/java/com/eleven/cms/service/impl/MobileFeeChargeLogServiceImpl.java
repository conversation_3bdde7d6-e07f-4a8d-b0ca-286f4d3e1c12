package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.ad.FeeChargeHeBaoProperties;
import com.eleven.cms.ad.FeeChargePlusProperties;
import com.eleven.cms.ad.FeeChargeProperties;
import com.eleven.cms.config.FeeChargeProduct;
import com.eleven.cms.dto.FeeChargeOrderHaoDangResult;
import com.eleven.cms.entity.MobileFeeChargeLog;
import com.eleven.cms.mapper.MobileFeeChargeLogMapper;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IMobileFeeChargeLogService;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 话费直充记录
 * @Author: jeecg-boot
 * @Date:   2022-09-28
 * @Version: V1.0
 */
@Slf4j
@Service
public class MobileFeeChargeLogServiceImpl extends ServiceImpl<MobileFeeChargeLogMapper, MobileFeeChargeLog> implements IMobileFeeChargeLogService {
    private static final String KEY="ccd8ac1507dd44c2";
    private static final String IV="_zhinkflowivs000";
    private static final String QING_HAI_WEN_HANG_PRIVATE_KEY="W12340opiERddabMb2134125Eio09";
    @Autowired
    private FeeChargeProperties feeChargeProperties;

    @Autowired
    private FeeChargePlusProperties feeChargePlusProperties;

    @Autowired
    private FeeChargeHeBaoProperties feeChargeHeBaoProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    private MediaType mediaType;
    @Autowired
    private Environment environment;
    @Autowired
    private IMobileFeeChargeLogService mobileFeeChargeLogService;
    @Autowired
    MobileRegionService mobileRegionService;
    static {
        if(null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)){
            Security.addProvider(new BouncyCastleProvider());
        }
    }
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
        this.mediaType = MediaType.parse(org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }

    /**
     * 话费充值
     * @param mobileFeeChargeLog
     */
    @Override
    public Result<?> feeCharge(MobileFeeChargeLog mobileFeeChargeLog) {
        Result<?> feeQueryBalanceResult=feeQueryBalance();
        if(!feeQueryBalanceResult.isOK()){
            return feeQueryBalanceResult;
        }
        MobileFeeChargeLog feeCharge=  this.lambdaQuery().eq(MobileFeeChargeLog::getMobile, mobileFeeChargeLog.getMobile()).eq(MobileFeeChargeLog::getStatus, 0).select(MobileFeeChargeLog::getOrderId,MobileFeeChargeLog::getMobile).orderByDesc(MobileFeeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(feeCharge!=null){
            //充值中
            Result<?> feeChargeOrderQueryResult=feeChargeOrderQuery(feeCharge.getOrderId(),feeCharge.getMobile());
            if(!feeChargeOrderQueryResult.isOK()){
                return feeChargeOrderQueryResult;
            }
        }
        FeeChargeProduct feeChargeProduct=feeChargeProperties.getFeeChargeProductMap().get(mobileFeeChargeLog.getCouponId());
        if(feeChargeProduct==null){
            log.error("产品已下线,手机号:{}", mobileFeeChargeLog.getMobile());
            return Result.error("产品已下线!");
        }
        /**订单号*/
        String orderId = IdWorker.get32UUID();

        mobileFeeChargeLog.setOrderId(orderId);
        /**订购月份*/
        String feeChargeMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        mobileFeeChargeLog.setOrderMonth(feeChargeMonth);
        /**权益月份*/
        mobileFeeChargeLog.setRightsMonth(feeChargeMonth);
        /**业务id*/
        mobileFeeChargeLog.setServiceId("9527001");
        /**充值状态:0=直充中,1=直充成功,2=直充失败,-1=预约直充*/
        mobileFeeChargeLog.setStatus(0);
        /**产品名称*/
        mobileFeeChargeLog.setCouponName(feeChargeProduct.getProductName());
        /**产品价格*/
        mobileFeeChargeLog.setProductPrice(feeChargeProduct.getProductPrice());

        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobileFeeChargeLog.getMobile());
        if(mobileRegionResult!=null) {
            /**省份*/
            mobileFeeChargeLog.setProvince(mobileRegionResult.getProvince());
            /**城市*/
            mobileFeeChargeLog.setCity(mobileRegionResult.getCity());
        }

        /**账号*/
        mobileFeeChargeLog.setAccount(mobileFeeChargeLog.getMobile());

        /**权益包名*/
        mobileFeeChargeLog.setPackName("test_rights_pack_1");

        /**充值方式:0=直充,1=券码*/
        mobileFeeChargeLog.setRechargeState("0");
        /**子渠道号*/
//        mobileFeeChargeLog.setSubChannel(rightsSubService.nowIsSub(mobileFeeChargeLog.getMobile(),mobileFeeChargeLog.getChannel()));
        /**创建时间*/
        mobileFeeChargeLog.setCreateTime(new Date());
        this.baseMapper.insert(mobileFeeChargeLog);
        Map<String, Object> node = Maps.newHashMap();
        node.put("salerId",feeChargeProperties.getSalerId());
        node.put("orderId",orderId);
        node.put("transAmount",Integer.valueOf(feeChargeProduct.getProductPrice()));
        node.put("productCode",feeChargeProduct.getProductCode());
        node.put("mobile",mobileFeeChargeLog.getMobile());
        node.put("callBack_Url",feeChargeProperties.getCallbackUrl());
        node.put("remark","话费充值");
        node.put("sign",MD5Util.getJiangXiVrbtSigns(node,feeChargeProperties.getKey()));
        String content =implementHttpPostResult(feeChargeProperties.getFeeChargeUrl(), node,"话费充值接口",mobileFeeChargeLog.getMobile());
        if(StringUtils.isBlank(content)){
            return Result.error("系统异常!");
        }
        try {
            final FeeChargeOrderRequest feeChargeOrderRequest =mapper.readValue(content, FeeChargeOrderRequest.class);
            if(feeChargeOrderRequest.isOK()){

                return Result.ok("充值中");
            }
            this.lambdaUpdate().eq(MobileFeeChargeLog::getId,mobileFeeChargeLog.getId()).set(MobileFeeChargeLog::getStatus,2).set(MobileFeeChargeLog::getRemark,feeChargeOrderRequest.getMsg()).update();
            return Result.error(feeChargeOrderRequest.getMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return Result.error("系统异常!");
        }
    }



    /**
     * 话费充值订单查询
     * @param orderId
     * @param mobile
     * @return
     */
    private Result<?> feeChargeOrderQuery(String orderId,String mobile) {
        Map<String, Object> node = Maps.newHashMap();
        node.put("salerId",feeChargeProperties.getSalerId());
        node.put("orderId",orderId);
        node.put("sign",MD5Util.getJiangXiVrbtSigns(node,feeChargeProperties.getKey()));
        String content =implementHttpPostResult(feeChargeProperties.getFeeChargeOrderQueryUrl(), node,"话费充值订单查询接口",mobile);
        if(StringUtils.isBlank(content)){
            return Result.error("系统异常!");
        }
        try {
            final FeeChargeOrderQueryRequest feeChargeOrderQueryRequest =mapper.readValue(content, FeeChargeOrderQueryRequest.class);
            if(!feeChargeOrderQueryRequest.isOK()){
                return Result.error(feeChargeOrderQueryRequest.getMsg());
            }
            if(feeChargeOrderQueryRequest.isOK() && "0".equals(feeChargeOrderQueryRequest.getPayStatus())){
                return Result.error("号码正在充值中!");
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return Result.error("系统异常!");
        }
        return Result.ok("成功");
    }

    /**
     * 话费余额查询
     * @return
     */
    private Result<?> feeQueryBalance() {
        Map<String, Object> node = Maps.newHashMap();
        node.put("salerId",feeChargeProperties.getSalerId());
        node.put("sign",MD5Util.getJiangXiVrbtSigns(node,feeChargeProperties.getKey()));
        String content =implementHttpPostResult(feeChargeProperties.getFeeQueryBalanceUrl(), node,"话费余额查询接口","");
        if(StringUtils.isBlank(content)){
            return Result.error("系统异常!");
        }
        try {
            final FeeQueryBalanceRequest feeQueryBalanceRequest =mapper.readValue(content, FeeQueryBalanceRequest.class);
            if(!feeQueryBalanceRequest.isOK()){
                return Result.error(feeQueryBalanceRequest.getMsg());
            }
            if(feeQueryBalanceRequest.isOK() && Double.valueOf(feeQueryBalanceRequest.getCanBalance())<=Double.valueOf(0)){
                return Result.error("商户可用余额不足!");
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return Result.error("系统异常!");
        }
        return Result.ok("成功");
    }
    /**
     * 发起http请求
     */
    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url,Object fromValue,String msg,String mobile) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,mobile);
    }
    private String push(String url,String raw,String msg,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},请求参数:{},响应参数:{}",mobile,url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,raw,e);
            return null;
        }
    }

    @Override
    public String feeChargeNotify(FeeChargeRequest feeChargeRequest) {
        Integer feeChargeCount=  this.lambdaQuery().eq(MobileFeeChargeLog::getOrderId,feeChargeRequest.getOrderId()).eq(MobileFeeChargeLog::getStatus, 1).count();
        if(feeChargeCount>0){
            log.error("枣米糖重复通知,已充值成功,请求参数:{}", feeChargeRequest);
            return "ok";
        }
        Map<String, Object> node = Maps.newHashMap();
        node.put("orderId",feeChargeRequest.getOrderId());
        node.put("productCode",feeChargeRequest.getProductCode());
        node.put("tradeNo",feeChargeRequest.getTradeNo());
        node.put("salerId",feeChargeProperties.getSalerId());
        node.put("transAmount",feeChargeRequest.getTransAmount());
        node.put("mobile",feeChargeRequest.getMobile());
        node.put("actualAmount",feeChargeRequest.getActualAmount());
        node.put("salerDiscount",feeChargeRequest.getSalerDiscount());
        node.put("payStatus",feeChargeRequest.getPayStatus());
        node.put("payTime",feeChargeRequest.getPayTime());
        if(StringUtils.isNotBlank(feeChargeRequest.getPzOrderNo())){
            node.put("pzOrderNo",feeChargeRequest.getPzOrderNo());
        }
        String sign=MD5Util.getJiangXiVrbtSigns(node,feeChargeProperties.getKey());
        if(!sign.equals(feeChargeRequest.getSign())){
            log.error("枣米糖签名异常,签名参数:{}", node);
            return "fail";
        }
        String result = "{\"充值状态\":\""+feeChargeRequest.getPayStatus()+"\",\"充值时间\":\""+feeChargeRequest.getPayTime()+"\",\"充值金额\":\""+feeChargeRequest.getTransAmount()+"\",\"实际支付金额\":\""+feeChargeRequest.getActualAmount()+"\"}";
        Date payTime=null;
        if(StringUtils.isNotBlank(feeChargeRequest.getPayTime())){
            try {
                payTime=DateUtil.stringToDate(feeChargeRequest.getPayTime());
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        this.lambdaUpdate().eq(MobileFeeChargeLog::getOrderId,feeChargeRequest.getOrderId()).set(MobileFeeChargeLog::getStatus,feeChargeRequest.getPayStatus()).set(MobileFeeChargeLog::getResult,result).set(MobileFeeChargeLog::getRemark,result).set(MobileFeeChargeLog::getPayTime,payTime).update();
        return "ok";
    }

    @Override
    public Result<?> detail(String id) {
        MobileFeeChargeLog feeCharge=  this.lambdaQuery().eq(MobileFeeChargeLog::getId, id).orderByDesc(MobileFeeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(feeCharge==null){
            return Result.error("订单不存在!");
        }
        return Result.ok(feeCharge);
    }


    /**
     * 话费充值
     * @param mobileFeeChargeLog
     */
    @Override
    public Result<?> feeChargePlus(MobileFeeChargeLog mobileFeeChargeLog) {
        String token=mobileFeeChargeLogService.feeTokenPlus();
        if(StringUtils.isBlank(token)){
            return Result.error("token获取失败!");
        }
//        Result<?> feeQueryBalanceResult=feeQueryBalancePlus();
//        if(!feeQueryBalanceResult.isOK()){
//            return feeQueryBalanceResult;
//        }
        MobileFeeChargeLog feeCharge=  this.lambdaQuery().select(MobileFeeChargeLog::getOrderId,MobileFeeChargeLog::getMobile).eq(MobileFeeChargeLog::getMobile, mobileFeeChargeLog.getMobile()).eq(MobileFeeChargeLog::getStatus, 0).orderByDesc(MobileFeeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(feeCharge!=null){
            //充值中
            return Result.error("产品充值中!");
        }
        FeeChargeProduct feeChargeProduct=feeChargePlusProperties.getFeeChargeProductMap().get(mobileFeeChargeLog.getCouponId());
        if(feeChargeProduct==null){
            log.error("枣米糖产品已下线,手机号:{}", mobileFeeChargeLog.getMobile());
            return Result.error("产品已下线!");
        }
        /**订单号*/
        String orderId = IdWorker.get32UUID();
        mobileFeeChargeLog.setOrderId(orderId);
        /**订购月份*/
        String feeChargeMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        mobileFeeChargeLog.setOrderMonth(feeChargeMonth);
        /**权益月份*/
        mobileFeeChargeLog.setRightsMonth(feeChargeMonth);
        /**业务id*/
        mobileFeeChargeLog.setServiceId("9527002");
        /**权益包名*/
        mobileFeeChargeLog.setPackName("test_plus_rights_pack_1");
        /**充值状态:0=直充中,1=直充成功,2=直充失败,-1=预约直充*/
        mobileFeeChargeLog.setStatus(0);
        /**产品名称*/
        mobileFeeChargeLog.setCouponName(feeChargeProduct.getProductName());
        /**产品价格*/
        mobileFeeChargeLog.setProductPrice(feeChargeProduct.getProductPrice());

        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobileFeeChargeLog.getMobile());
        if(mobileRegionResult!=null) {
            /**省份*/
            mobileFeeChargeLog.setProvince(mobileRegionResult.getProvince());
            /**城市*/
            mobileFeeChargeLog.setCity(mobileRegionResult.getCity());
        }

        /**账号*/
        mobileFeeChargeLog.setAccount(mobileFeeChargeLog.getMobile());
        /**充值方式:0=直充,1=券码*/
        mobileFeeChargeLog.setRechargeState("0");
        /**创建时间*/
        mobileFeeChargeLog.setCreateTime(new Date());
        this.baseMapper.insert(mobileFeeChargeLog);
        Map<String, Object> node = Maps.newHashMap();
        node.put("productCode",feeChargeProduct.getProductCode());
        node.put("phone", mobileFeeChargeLog.getMobile());
        node.put("nonce",IdWorker.get32UUID().toUpperCase());
        node.put("distributorOrderId",orderId);
        node.put("timestamp",System.currentTimeMillis());
        node.put("sign",MD5Util.getSign(node,feeChargePlusProperties.getKey()));
        log.info("枣米糖话费充值接口-请求=>请求参数:{}",node);
        String json = null;
        try {
            json = mapper.writeValueAsString(node);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        RequestBody body = RequestBody.create(JSON,json);
        Request request = new Request.Builder().url(feeChargePlusProperties.getFeeChargeUrl()).post(body).addHeader("token", token).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("枣米糖话费充值接口-响应=>请求参数:{},响应参数:{}",node,content);
            final FeeChargeOrderPlusRequest feeChargeOrderPlusRequest =mapper.readValue(content, FeeChargeOrderPlusRequest.class);
            if(feeChargeOrderPlusRequest.isOK()){
                this.lambdaUpdate().eq(MobileFeeChargeLog::getId,mobileFeeChargeLog.getId()).set(MobileFeeChargeLog::getRemark,feeChargeOrderPlusRequest.getData()).update();
                return Result.ok("充值中");
            }
            this.lambdaUpdate().eq(MobileFeeChargeLog::getId,mobileFeeChargeLog.getId()).set(MobileFeeChargeLog::getStatus,2).set(MobileFeeChargeLog::getRemark,feeChargeOrderPlusRequest.getMsg()).update();
            return Result.error(feeChargeOrderPlusRequest.getMsg());
        } catch (IOException e) {
            log.error("枣米糖话费充值接口-请求异常=>请求参数:{}",node,e);
            return Result.error("话费充值异常");
        }
    }



    /**
     * 话费余额查询
     * @return
     */
    private Result<?> feeQueryBalancePlus() {
        String token=mobileFeeChargeLogService.feeTokenPlus();
        if(StringUtils.isBlank(token)){
            return Result.error("token获取失败!");
        }
        log.info("枣米糖话费余额查询接口-请求=>请求参数:{}",token);
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url(feeChargePlusProperties.getFeeQueryBalanceUrl())
                .method("POST", body)
                .addHeader("token", token)
                .build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("枣米糖话费余额查询接口-响应=>请求参数:{},响应参数:{}",token,content);
            final FeeQueryBalancePlusRequest feeQueryBalancePlusRequest =mapper.readValue(content, FeeQueryBalancePlusRequest.class);
            if(feeQueryBalancePlusRequest.isOK() && feeQueryBalancePlusRequest.getData()!=null && feeQueryBalancePlusRequest.getData()>Double.valueOf(0)){
                return Result.ok("成功");
            }
            return Result.error(feeQueryBalancePlusRequest.getMsg());
        } catch (IOException e) {
            log.error("枣米糖话费余额查询接口-请求异常=>请求参数:{}",token,e);
            return Result.error("话费余额查询异常");
        }
    }


    /**
     * 获取token
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_FEE_RECHARGE_TOKEN,key = "'all'",unless = "#result==null")
    public String feeTokenPlus() {
        Map<String, Object> node = Maps.newHashMap();
        node.put("userName",feeChargePlusProperties.getUserName());
        String sign = DigestUtils.md5DigestAsHex((feeChargePlusProperties.getPassWord()).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        node.put("password",sign);
        node.put("nonce",IdWorker.get32UUID().toUpperCase());
        node.put("timestamp",System.currentTimeMillis());
        log.info("枣米糖话费获取token接口-请求=>请求参数:{}",node);
        String json = null;
        try {
            json = mapper.writeValueAsString(node);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        RequestBody body = RequestBody.create(JSON,json);
        Request request = new Request.Builder().url(feeChargePlusProperties.getFeeChargeTokenUrl()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("枣米糖话费获取token接口-响应=>请求参数:{},响应参数:{}",node,content);
            final FeeTokenRequest feeTokenRequest =mapper.readValue(content, FeeTokenRequest.class);
            if(feeTokenRequest.isOK()){
                return feeTokenRequest.getData().getToken();
            }
            return null;
        } catch (IOException e) {
            log.error("枣米糖话费获取token接口异常=>请求参数:{}",node,e);
            return null;
        }
    }


    @Override
    public String feeChargeNotifyPlus(String requestBody) {
       FeeChargePlusRequest feeChargePlusRequest = null;
        try {
            feeChargePlusRequest = mapper.readValue(requestBody, FeeChargePlusRequest.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("枣米糖话费充值通知异常-请求参数:{}",requestBody,e);
        }
        boolean feeChargeCount=  this.lambdaQuery().eq(MobileFeeChargeLog::getOrderId,feeChargePlusRequest.getSerialno()).count()<=0;
        if(feeChargeCount){
            log.error("枣米糖订单查询失败-请求参数:{}", feeChargePlusRequest);
            return "ok";
        }
        Integer status="0".equals(feeChargePlusRequest.getOrderStatus())?1:2;
        this.lambdaUpdate().eq(MobileFeeChargeLog::getOrderId,feeChargePlusRequest.getSerialno()).set(MobileFeeChargeLog::getStatus,status).set(MobileFeeChargeLog::getResult,feeChargePlusRequest.getErrDesc()).set(MobileFeeChargeLog::getPayTime,new Date()).update();
        return "ok";
    }



    /**
     * 话费充值
     * @param mobileFeeChargeLog
     */
    @Override
    public Result<?> heBaoAddPlus(MobileFeeChargeLog mobileFeeChargeLog) {
        MobileFeeChargeLog feeCharge=  this.lambdaQuery().select(MobileFeeChargeLog::getOrderId,MobileFeeChargeLog::getMobile).eq(MobileFeeChargeLog::getMobile, mobileFeeChargeLog.getMobile()).eq(MobileFeeChargeLog::getStatus, 0).orderByDesc(MobileFeeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(feeCharge!=null){
            //充值中
            return Result.error("产品充值中!");
        }
        FeeChargeProduct feeChargeProduct=feeChargeHeBaoProperties.getFeeChargeProductMap().get(mobileFeeChargeLog.getCouponId());
        if(feeChargeProduct==null){
            log.error("和包产品已下线,手机号:{}", mobileFeeChargeLog.getMobile());
            return Result.error("产品已下线!");
        }
        /**订单号*/
        String orderId = IdWorker.get32UUID();
        mobileFeeChargeLog.setOrderId(orderId);
        /**订购月份*/
        String feeChargeMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        mobileFeeChargeLog.setOrderMonth(feeChargeMonth);
        /**权益月份*/
        mobileFeeChargeLog.setRightsMonth(feeChargeMonth);
        /**业务id*/
        mobileFeeChargeLog.setServiceId("9527003");
        /**权益包名*/
        mobileFeeChargeLog.setPackName("test_hebao_rights_pack_1");
        /**充值状态:0=直充中,1=直充成功,2=直充失败,-1=预约直充*/
        mobileFeeChargeLog.setStatus(0);
        /**产品名称*/
        mobileFeeChargeLog.setCouponName(feeChargeProduct.getProductName());
        /**产品价格*/
        mobileFeeChargeLog.setProductPrice(feeChargeProduct.getProductPrice());

        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobileFeeChargeLog.getMobile());
        if(mobileRegionResult!=null) {
            /**省份*/
            mobileFeeChargeLog.setProvince(mobileRegionResult.getProvince());
            /**城市*/
            mobileFeeChargeLog.setCity(mobileRegionResult.getCity());
        }

        /**账号*/
        mobileFeeChargeLog.setAccount(mobileFeeChargeLog.getMobile());
        /**充值方式:0=直充,1=券码*/
        mobileFeeChargeLog.setRechargeState("0");
        /**创建时间*/
        mobileFeeChargeLog.setCreateTime(new Date());
        this.baseMapper.insert(mobileFeeChargeLog);
        Map<String, Object> node = Maps.newHashMap();
        node.put("szAgentId",feeChargeHeBaoProperties.getSzAgentId());
        node.put("szOrderId",orderId);
        node.put("szPhoneNum", mobileFeeChargeLog.getMobile());
        node.put("nMoney",Integer.valueOf(feeChargeProduct.getProductPrice()));
//        1	移动
//        2	联通
//        3	电信
        if(mobileFeeChargeLog.getCouponId().contains("YD")){
            node.put("nSortType","1");
        }else if(mobileFeeChargeLog.getCouponId().contains("DX")){
            node.put("nSortType","2");
        }else if(mobileFeeChargeLog.getCouponId().contains("LT")){
            node.put("nSortType","3");
        }
        node.put("nProductClass",1);
        node.put("nProductType","1");
        node.put("szProductId",feeChargeProduct.getProductCode());
        node.put("szTimeStamp",DateUtil.formatSplitTime(LocalDateTime.now()));
        node.put("szVerifyString",MD5Util.getFeeSign(node,feeChargeHeBaoProperties.getKey()));
        node.put("szNotifyUrl",feeChargeHeBaoProperties.getCallbackUrl());
        log.info("和包话费充值接口-请求=>请求参数:{}",node);
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : node.keySet()) {
            builder.add(key,String.valueOf( node.get(key)));
        }
        RequestBody formBody=builder.build();
        Request request = new Request.Builder().url(feeChargeHeBaoProperties.getFeeChargeUrl())
                .method("POST", formBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("和包话费充值接口-响应=>请求参数:{},响应参数:{}",node,content);
            final FeeChargeOrderHeBaoRequest feeChargeOrderHeBaoRequest =mapper.readValue(content, FeeChargeOrderHeBaoRequest.class);
            if(feeChargeOrderHeBaoRequest.isOK()){
                this.lambdaUpdate().eq(MobileFeeChargeLog::getId,mobileFeeChargeLog.getId()).set(MobileFeeChargeLog::getRemark,feeChargeOrderHeBaoRequest.getSzRtnCode()).update();
                return Result.ok("充值中");
            }
            this.lambdaUpdate().eq(MobileFeeChargeLog::getId,mobileFeeChargeLog.getId()).set(MobileFeeChargeLog::getStatus,2).set(MobileFeeChargeLog::getRemark,feeChargeOrderHeBaoRequest.getSzRtnCode()).update();
            return Result.error(feeChargeOrderHeBaoRequest.getSzRtnCode());
        } catch (IOException e) {
            log.error("和包话费充值接口-请求异常=>请求参数:{}",node,e);
            return Result.error("话费充值异常");
        }
    }


    @Override
    public String hebaoFeeChargeNotifyPlus(String szAgentId,String szOrderId ,String szPhoneNum,String nDemo,String fSalePrice,String nFlag,String szRtnMsg,String szVerifyString){
        Integer feeChargeCount=  this.lambdaQuery().eq(MobileFeeChargeLog::getOrderId,szOrderId).eq(MobileFeeChargeLog::getStatus, 1).count();
        if(feeChargeCount>0){
            log.error("和包重复通知,已充值成功,订单号:{},手机号:{},充值状态:{},充值描述:{}", szOrderId,szPhoneNum,nFlag,szRtnMsg);
            return "ok";
        }
        Map<String, Object> node = Maps.newHashMap();
        node.put("szAgentId",szAgentId);
        node.put("szOrderId",szOrderId);
        node.put("szPhoneNum",szPhoneNum);
        node.put("nDemo",nDemo);
        node.put("fSalePrice",fSalePrice);
        node.put("nFlag",nFlag);
        String sign=MD5Util.getFeeSign(node,feeChargeHeBaoProperties.getKey());
        if(!sign.equals(szVerifyString)){
            log.error("和包签名异常,签名参数:{}", node);
            return "fail";
        }
        String payStatus="-2";
        if("2".equals(nFlag)){
            payStatus="1";
        }else if("3".equals(nFlag)){
            payStatus="2";
        }
        this.lambdaUpdate().eq(MobileFeeChargeLog::getOrderId,szOrderId).set(MobileFeeChargeLog::getStatus,payStatus).set(MobileFeeChargeLog::getResult,szRtnMsg).set(MobileFeeChargeLog::getRemark,szRtnMsg).set(MobileFeeChargeLog::getPayTime,new Date()).update();
        return "ok";
    }
    @Override
    public String zhongheFeeChargeNotify(String requestBody) {
        FeeChargeZhongHeRequest feeChargePlusRequest = null;
        try {
            feeChargePlusRequest = mapper.readValue(requestBody, FeeChargeZhongHeRequest.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("中核话费充值通知异常-请求参数:{}",requestBody,e);
        }
        try {
            String content=Cryptos.aesDecrypt(Hex.decodeHex(feeChargePlusRequest.getData()), KEY.getBytes(StandardCharsets.UTF_8), IV.getBytes(StandardCharsets.UTF_8));
            log.info("中核话费充值通知=>参数:{}", content);
            List<ZhongHeData> zhongHeDataList = mapper.readValue(content, new TypeReference<List<ZhongHeData>>() {});
            zhongHeDataList.forEach(zhongHe->{
                boolean feeChargeCount=  this.lambdaQuery().eq(MobileFeeChargeLog::getOrderId,zhongHe.getCPOrderNo()).count()<=0;
                if(feeChargeCount){
                    log.error("中核订单查询失败-请求参数:{}", zhongHe);
                }else{
                    int status=zhongHe.getStatusCode()==0?1:2;
                    this.lambdaUpdate().eq(MobileFeeChargeLog::getOrderId,zhongHe.getCPOrderNo()).set(MobileFeeChargeLog::getStatus,status).set(MobileFeeChargeLog::getResult,zhongHe.getStatusMessage()).set(MobileFeeChargeLog::getPayTime,new Date()).update();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "ok";
    }



    @Override
    public String qingHaiWenHangChargeNotify(String userId,String orderId,String serialno,String orderStatus,String errDesc,String sign){
        String succRespXml = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n" +
                "<response>\n" +
                "\t<status>success</status>\n" +
                "<code>00</code>\n" +
                "</response>\n";
        String failRespXml = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n" +
                "<response>\n" +
                "\t<status>fail</status>\n" +
                "<code>500</code>\n" +
                "</response>\n";
        Integer feeChargeCount=  this.lambdaQuery().eq(MobileFeeChargeLog::getOrderId,serialno).eq(MobileFeeChargeLog::getStatus, 1).count();
        if(feeChargeCount>0){
            log.error("青海文航重复通知,已充值成功-渠道方用户编号:{},充值平台方订单号:{},渠道方商户系统的流水号:{},订单状态:{},失败原因描述:{}", userId,orderId,serialno,orderStatus,errDesc);
            return succRespXml;
        }
        String szVerifyString = DigestUtils.md5DigestAsHex((orderId+orderStatus+serialno+userId+QING_HAI_WEN_HANG_PRIVATE_KEY).getBytes(StandardCharsets.UTF_8)).toLowerCase();
        if(!sign.equals(szVerifyString)){
            log.error("青海文航签名异常-渠道方用户编号:{},充值平台方订单号:{},渠道方商户系统的流水号:{},订单状态:{},失败原因描述:{},渠道方密钥:{},密钥:{}", userId,orderId,serialno,orderStatus,errDesc,szVerifyString,sign);
            return failRespXml;
        }
        String payStatus="0";
        if("0".equals(orderStatus)){
            payStatus="1";
        }else if("3".equals(orderStatus)){
            payStatus="2";
        }
        this.lambdaUpdate().eq(MobileFeeChargeLog::getOrderId,serialno).set(MobileFeeChargeLog::getStatus,payStatus).set(MobileFeeChargeLog::getResult,errDesc).set(MobileFeeChargeLog::getRemark,errDesc).set(MobileFeeChargeLog::getPayTime,new Date()).update();
        return succRespXml;
    }


    @Override
    public FeeChargeOrderHaoDangResult haoDangChargeNotify(String orderId,String respCode,String respMsg,String transNo){
        Integer feeChargeCount=  this.lambdaQuery().eq(MobileFeeChargeLog::getOrderId,transNo).eq(MobileFeeChargeLog::getStatus, 1).count();
        if(feeChargeCount>0){
            log.error("浩荡重复通知,已充值成功,平台订单号:{},订单状态:{},失败原因描述:{},订单号:{}", orderId,respCode,respMsg,transNo);
            return FeeChargeOrderHaoDangResult.OK_RESULT;
        }
        String payStatus="-2";
        if("10003".equals(respCode)){
            payStatus="1";
        }else{
            payStatus="2";
        }
        this.lambdaUpdate().eq(MobileFeeChargeLog::getOrderId,transNo).set(MobileFeeChargeLog::getStatus,payStatus).set(MobileFeeChargeLog::getResult,respMsg).set(MobileFeeChargeLog::getRemark,respMsg).set(MobileFeeChargeLog::getPayTime,new Date()).update();
        return FeeChargeOrderHaoDangResult.OK_RESULT;
    }
}
