package com.eleven.cms.service;

import com.eleven.cms.entity.OrderVrbt;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.queue.DelayedVrbtRecheckMessage;
import com.eleven.cms.vo.RemoteResult;
import org.jeecg.common.api.vo.Result;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Nonnull;

/**
 * @Description: cms_order_vrbt
 * @Author: jeecg-boot
 * @Date:   2020-11-16
 * @Version: V1.0
 */
public interface IOrderVrbtService extends IService<OrderVrbt> {

    String getContentIdByCopyrightId(String copyrightId);

    /**
     * 对于未设置彩铃的包月订购,成功后随机免费订购一首彩铃
     * @param subscribe
     */
    void orderByRandomDelay(Subscribe subscribe);

    RemoteResult orderByRandom(Subscribe subscribe);

    @Nonnull RemoteResult orderCpmbByRandom(Subscribe subscribe);

    @Nonnull RemoteResult orderDiyByRandom(String mobile);

    @Nonnull
    RemoteResult orderByRandomXunfei(Subscribe subscribe);

    void orderByRandomLiantong(Subscribe subscribe);

    void orderByRandomDianxin(String subscribeId, String mobile, String company);

    @Async
    void vrbtToneFreeOrderDelay(String mobile, String channelCode, String copyRightID, String setFlag);

    @Async
    void vrbtToneFreeOrderDelay(Subscribe subscribe, String channelCopyrightId, String setFlag);

    RemoteResult vrbtToneFreeOrder(Subscribe subscribe, String channelCopyrightId, String setFlag, String randomFlag);

    @Nonnull RemoteResult activeVrbtFreeOrder(Subscribe subscribe, String copyrightId, String vrbtProductId,
                                              String randomFlag);

    int getOrderDefaultVrbtPercent();

    void alertRandomVrbtToneFreeOrderFail(String mobile, String channelCode, String channelCopyrightId, String failMsg);

    void setOrderDefaultVrbtPercent(Integer percent);

    void plusOrderCount(String orderVrbtId);

    RemoteResult orderByRandomXunfeiWithRetry(Subscribe subscribe);

    RemoteResult orderByRandomWithRetry(Subscribe subscribe);

    void handleMqOrder(DelayedMessage delayedMessage);

    Result<?> schRingPublish(String mobile, String channelCode);
}
