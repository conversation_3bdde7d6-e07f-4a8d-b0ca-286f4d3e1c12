package com.eleven.cms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.BizType;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.OutsideConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.repository.EsSubscribeRepository;
import com.eleven.cms.mapper.BizTypeMapper;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.queue.MiguRingFtpUploadMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.SichuanMobileQueryOrderResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-11 14:13
 */
@Service
@Slf4j
public class BizTypeServiceImpl extends ServiceImpl<BizTypeMapper, BizType> implements IBizTypeService {
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    public static final Integer VRBT_SERVER = 1; //视频服务器
    public static final Integer NOT_CRACK = 0; //是否破解：1=是，0=否

    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    EsSubscribeRepository esSubscribeRepository;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    @Lazy
    private IBizTypeService bizTypeService;
    @Autowired
    private IOutsideConfigService outsideConfigService;
    @Autowired
    SiChuanMobileApiService siChuanMobileApiService;
    @Autowired
    SubscribeVerifyService subscribeVerifyService;
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_TYPE_CHANEL_CACHE, key = "#root.methodName + '-' + #id", unless = "#result==null")
    public BizType selectById(String id) {
        return this.getById(id);
    }

    @Override
    public BizType getBizType(JsonNode jsonNodeArray) {
        if (jsonNodeArray.size() > 1) {
            Integer random = RandomUtils.getRondom(1, 100);
            ArrayNode arrayNode = sortJsonNode(jsonNodeArray);
            int start = 1;
            int end = 0;
            String bizTypeId = null;
            for (int i = 0; i < arrayNode.size(); i++) {
                JsonNode jsonNode = arrayNode.get(i);
                end += jsonNode.at("/rate").asInt();
                if (rangeInDefined(random, start, end)) {
                    bizTypeId = jsonNode.at("/bizTypeId").asText();
                    break;
                }
                start = end;
            }
            return SpringUtil.getBean(BizTypeServiceImpl.class).selectById(bizTypeId);
        } else {
            String bizTypeId = jsonNodeArray.at("/0/bizTypeId").asText();
            return bizTypeService.selectById(bizTypeId);
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_TYPE_CHANEL_CACHE, key = "#root.methodName + '-' + #channelCode+'-' + #serviceId", unless = "#result==null")
    public BizType getBizTypeByChannel(String channelCode, String serviceId) {
        return this.lambdaQuery().eq(BizType::getChannelCode, channelCode).eq(StringUtils.isNotBlank(serviceId), BizType::getCompanyOwner, serviceId).one();
    }

    @Override
    public Result callGetSmsCode(Subscribe subscribe, JsonNode pageConfigJsonNode) throws JsonProcessingException {
        BizType bizType = this.getBizType(pageConfigJsonNode.at("/bizTypeList"));
        subscribe.setChannel(bizType.getChannelCode());
        String bizTypeName = BizConstant.getBizTypeByMiguChannel(subscribe.getChannel());
        subscribe.setBizType(bizTypeName);
        subscribe.setServiceId(bizType.getCompanyOwner());
        subscribe.setContentId(bizType.getId());
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            subscribe.setIsp(mobileRegionResult.getOperator());
        }
        if (blackListService.isBlackList(subscribe.getMobile())) {
            return Result.msgBlackLimit();
        }
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.warn("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.msgAppLimit();
        }
        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), subscribe.getProvince())) {
            log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), subscribe.getMobile(), subscribe.getProvince());
            return Result.msgProvinceLimit();
        }
        //限制半年之内只能开一次业务(红名单除外)
        if ((!"JXYD_ADLD_YR".equals(subscribe.getChannel())) && subscribeService.checkBizRepeat(subscribe.getMobile(), subscribe.getChannel())) {
            log.warn("手机号:{}3个月内已开通过业务", subscribe.getMobile());
            return Result.msgDuplicateLimit();
        }
        if (VRBT_SERVER.equals(bizType.getServiceAddrType())) {
            IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
            Result result = bizCommonService.getSmsCode(subscribe);
            result.setChannelCode(bizType.getChannelCode());
            return result;
        } else {
            Result result = innerUnionMemberService.callGetSmsCode(subscribe, bizType.getBeanName());
            result.setChannelCode(bizType.getChannelCode());
            return result;
        }
    }

    @Override
    public Result callSumbitSmsCode(Subscribe subscribe, String smsCode, String transactionId) throws JsonProcessingException {
        EsSubscribe esSubscribe = esSubscribeRepository.findById(transactionId).get();
        //本次transactionId已开通业成功不可再次提交验证码
        if (SUBSCRIBE_STATUS_SUCCESS.equals(esSubscribe.getStatus())) {
            return Result.error("请勿重复提交验证码");
        }
        BizType bizType;
        if (StringUtils.isNotBlank(esSubscribe.getContentId())) {
            bizType = bizTypeService.selectById(esSubscribe.getContentId());
        } else {
            bizType = SpringUtil.getBean(BizTypeServiceImpl.class).getBizTypeByChannel(esSubscribe.getChannel(), esSubscribe.getServiceId());
        }
        if (VRBT_SERVER.equals(bizType.getServiceAddrType())) {
            IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
            Result result = bizCommonService.submitSmsCode(subscribe);
            result.setChannelCode(bizType.getChannelCode());
            return result;
        } else {
            ObjectNode objectNode = mapper.createObjectNode();
            objectNode.put("id", transactionId);
            objectNode.put("smsCode", smsCode);
            Result result = innerUnionMemberService.callSumbitSmsCode(esSubscribe.getMobile(), objectNode, bizType.getBeanName());
            result.setChannelCode(bizType.getChannelCode());
            return result;
        }
    }

    @Override
    public Result outsideCallGetSmsCode(Subscribe subscribe, String bizTypeId) {
        try {
            BizType bizType = bizTypeService.selectById(bizTypeId);
            subscribe.setServiceId(bizType.getCompanyOwner());
            subscribe.setContentId(bizType.getId());
            if (VRBT_SERVER.equals(bizType.getServiceAddrType())) {
                IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
                return bizCommonService.getSmsCode(subscribe);
            } else {
                return innerUnionMemberService.callGetSmsCode(subscribe, bizType.getBeanName());
            }
        } catch (Exception e) {
            log.error("外部渠道获取短验异常-手机号:{},channel:{},subChannel:{}",subscribe.getMobile(),subscribe.getChannel(),subscribe.getSubChannel(),e);
            return Result.msgSystemError();
        }
    }

    @Override
    public Result outsideCallSumbitSmsCode(Subscribe subscribe, String smsCode, String transactionId) throws JsonProcessingException {
        EsSubscribe esSubscribe = esSubscribeRepository.findById(transactionId).get();
        BizType bizType;
        if (StringUtils.isNotBlank(esSubscribe.getContentId())) {
            bizType = bizTypeService.selectById(esSubscribe.getContentId());
        } else {
            bizType = SpringUtil.getBean(BizTypeServiceImpl.class).getBizTypeByChannel(esSubscribe.getChannel(), esSubscribe.getServiceId());
        }
        Result result;
        if (VRBT_SERVER.equals(bizType.getServiceAddrType())) {
            IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
            result = bizCommonService.submitSmsCode(subscribe);
        } else {
            ObjectNode objectNode = mapper.createObjectNode();
            objectNode.put("id", transactionId);
            objectNode.put("smsCode", smsCode);
            result = innerUnionMemberService.callSumbitSmsCode(esSubscribe.getMobile(), objectNode, bizType.getBeanName());
        }
        EsSubscribe newEsSubscribe = esSubscribeRepository.findById(transactionId).get();
        //提交完验证码就可以获取开通结果的，当时就回传
        if (subscribe != null && !BizConstant.BIZ_TYPE_SCYD.equals(newEsSubscribe.getBizType()) && (SUBSCRIBE_STATUS_SUCCESS.equals(newEsSubscribe.getStatus()) || SUBSCRIBE_STATUS_FAIL.equals(newEsSubscribe.getStatus()))) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(newEsSubscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }
        return result;
    }

    @Override
    public Result callPopGetSmsCode(Subscribe subscribe, String bizTypeId) throws JsonProcessingException {
        BizType bizType = bizTypeService.selectById(bizTypeId);
        subscribe.setChannel(bizType.getChannelCode());
        String bizTypeName = BizConstant.getBizTypeByMiguChannel(subscribe.getChannel());
        subscribe.setBizType(bizTypeName);
        subscribe.setServiceId(bizType.getCompanyOwner());
        subscribe.setContentId(bizType.getId());
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if (mobileRegionResult != null) {
            subscribe.setProvince(mobileRegionResult.getProvince());
            subscribe.setCity(mobileRegionResult.getCity());
            subscribe.setIsp(mobileRegionResult.getOperator());
        }
        if (blackListService.isBlackList(subscribe.getMobile())) {
            return Result.msgBlackLimit();
        }
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.warn("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.msgAppLimit();
        }
        if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), subscribe.getProvince())) {
            log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), subscribe.getMobile(), subscribe.getProvince());
            return Result.msgProvinceLimit();
        }
        //限制半年之内只能开一次业务(红名单除外)
        if ((!"JXYD_ADLD_YR".equals(subscribe.getChannel())) && subscribeService.checkBizRepeat(subscribe.getMobile(), subscribe.getChannel())) {
            log.warn("手机号:{}3个月内已开通过业务", subscribe.getMobile());
            return Result.msgDuplicateLimit();
        }
        if (VRBT_SERVER.equals(bizType.getServiceAddrType())) {
            IBizCommonService bizCommonService = SpringUtil.getBean(bizType.getBeanName());
            Result result = bizCommonService.getSmsCode(subscribe);
            result.setChannelCode(bizType.getChannelCode());
            return result;
        } else {
            Result result = innerUnionMemberService.callGetSmsCode(subscribe, bizType.getBeanName());
            result.setChannelCode(bizType.getChannelCode());
            return result;
        }
    }

    public static boolean rangeInDefined(int current, int min, int max) {
        return Math.max(min, current) == Math.min(current, max);
    }

    public static ArrayNode sortJsonNode(JsonNode node) {
        List<JsonNode> dataNodes = node.findParents("bizTypeId");
        List<JsonNode> sortedDataNodes = dataNodes
            .stream()
            .sorted(Comparator.comparing(o -> o.get("bizTypeId").asText()))
            .collect(Collectors.toList());
        ArrayNode arrayNode = mapper.createObjectNode().arrayNode().addAll(sortedDataNodes);
        return arrayNode;
    }

    /**
     * 查询渠道号列表
     *
     * @return List<String>
     */
    @Override
    public List<String> queryChannelList() {
        return lambdaQuery()
                .select(BizType::getChannelCode).groupBy(BizType::getChannelCode)
                .list()
                .stream().map(BizType::getChannelCode).collect(Collectors.toList());
    }


    @Override
    public Result outsideSubOrder(Subscribe subscribe) {
        try {
            OutsideConfig outsideConfig = outsideConfigService.getOutsideConfigByOutChannel(subscribe.getSubChannel());
            if (outsideConfig == null || StringUtils.isBlank(outsideConfig.getBizTypeId())) {
                log.error("无效的外部渠道号-手机号:{},channel:{},subChannel:{}",subscribe.getMobile(),subscribe.getChannel(),subscribe.getSubChannel());
                return Result.error("无效的渠道号!");
            }
            //报备页面开通
            if (NOT_CRACK.equals(outsideConfig.getIsCrack()) && StringUtils.isNotBlank(outsideConfig.getReportBean())) {
                IBusinessCommonService businessCommonService = SpringUtil.getBean(outsideConfig.getReportBean());
                return businessCommonService.receiveOrderWithCache(subscribe);
            }
            log.error("外部渠道业务订购报备配置异常-配置:{},手机号:{},channel:{},subChannel:{}", outsideConfig,subscribe.getMobile(),subscribe.getChannel(),subscribe.getSubChannel());
            return Result.msgSystemError();
        } catch (Exception e) {
            log.error("外部渠道业务订购异常-手机号:{},channel:{},subChannel:{}",subscribe.getMobile(),subscribe.getChannel(),subscribe.getSubChannel(),e);
            return Result.msgSystemError();
        }
    }




    /**
     * 破解计费开通结果接收
     *
     * @param mobile   手机号
     * @param ydOrderId  订单号码
     * @param resCode  错误代码：0000表示计费成功
     * @param resMsg   错误描述
     * @return
     */
    @Override
    public Result receiveBillingResult(String mobile,String ydOrderId) {
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, ydOrderId);
        if (subscribe == null) {
            log.error("计费更新订购结果失败,不存在该id的订购通知记录,mobile:{},transId:{}", mobile, ydOrderId);
            return Result.error("订单不存在");
        }
        //跳过成功的重复通知
        if (SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
            log.warn("跳过成功订单处理,mobile:{},transId:{}", mobile, ydOrderId);
            return Result.error("重复访问");
        }
        Integer status=subscribeVerifyService.monthVerify(subscribe);
        String result = "计费开通结果=>" +(SUBSCRIBE_STATUS_SUCCESS.equals(status)?"成功":"失败");
        Date date=new Date();
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult(result);
        upd.setOpenTime(date);
        upd.setModifyTime(date);
        subscribeService.updateSubscribeDbAndEs(upd);
        if (SUBSCRIBE_STATUS_SUCCESS.equals(status) ) {
            //特定渠道号充值
            siChuanMobileApiService.reCharge(subscribe.getIspOrderNo(),subscribe.getChannel(),subscribe.getMobile(), DateUtils.now());
            //写入自增序列
            subscribeService.saveChannelLimit(subscribe);
            //包月延迟队列
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
        }
        //如果是外部渠道通知
        if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }
        return Result.ok("请求成功");
    }
}
