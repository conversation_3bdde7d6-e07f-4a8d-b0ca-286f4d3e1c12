package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.SmsModel;

import java.util.Map;

/**
 * @Description: 业务短信模板
 * @Author: jeecg-boot
 * @Date:   2022-09-23
 * @Version: V1.0
 */
public interface ISmsModelService extends IService<SmsModel> {
    SmsModel getSmsModelbyServiceId(String serviceId);

    SmsModel getSmsModelbyChannel(String channel,String serviceId,String serviceType);

    Boolean sendSms(String mobile,String channel, String serviceId, String serviceType);

    Boolean sendSms(String mobile,String channel, String serviceId, String serviceType, String code);

    void sendSmsAsync(String mobile,String channel, String serviceId, String serviceType);

    void sendSmsAsync(String mobile,String channel, String serviceId, String serviceType, String code);


    Boolean sendSms(String mobile,String channel, String serviceId, String serviceType, Map<String,String> smsMap);

    void monthRenewSubSmsMessage(String channel,String mobile,String id,String bizType);

    void monthRenewSubSendSms(String channel,String mobile,String bizType);
}
