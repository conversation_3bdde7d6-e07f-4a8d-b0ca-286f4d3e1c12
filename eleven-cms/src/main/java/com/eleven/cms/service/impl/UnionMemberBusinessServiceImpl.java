package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.IBusinessCommonService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class UnionMemberBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    WujiongCrackService wujiongCrackService;


    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("渠道省份限制");
                    }
                    if (GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
                        return Result.error("暂未开放,敬请期待!");
//                        subscribe.setBizType(BIZ_TYPE_GZYD_HJHY_TYHD);
//                        subscribe.setChannel(BIZ_TYPE_GZYD_HJHY_TYHD);
//                        return subscribeService.receiveGuiZhouMobileOrder(subscribe);
                    }
                } else {
                    if (MobileRegionResult.ISP_DIANXIN.equals(subscribe.getIsp()) && SHANDONG_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setChannel(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                        subscribe.setBizType(BIZ_TYPE_CHANNEL_SDDX_SSHY);
                        return subscribeService.receiveShandongDianxinOrder(subscribe);
                    }
                    if (MobileRegionResult.ISP_DIANXIN.equals(subscribe.getIsp()) && HUNAN_PROVINCE.equals(subscribe.getProvince())) {
                        subscribe.setBizType(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                        subscribe.setChannel(BIZ_TYPE_CHANNEL_HUNAN_DIANXIN_VRBT);
                        return subscribeService.receiveHunanDianxinCommonCrackOrder(subscribe);
                    }
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        return SpringContextUtils.getBean(UnionMemberBusinessServiceImpl.class).receiveOrder(subscribe);
    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
//            this.updateById(upd);
                subscribeService.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, target.getChannel(), subscribe.getMobile());
            return Result.ok("订阅成功");
//            }
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final Result<?> result = pushSubscribeService.handleUnionMemberExistsBillingCrack(subscribe);
//        }
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }
}
