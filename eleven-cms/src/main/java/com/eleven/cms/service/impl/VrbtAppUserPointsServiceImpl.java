package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.dto.VrbtAppUserPointsDto;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.VrbtAppUser;
import com.eleven.cms.entity.VrbtAppUserPoints;
import com.eleven.cms.mapper.VrbtAppUserPointsMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IVrbtAppUserPointsService;
import com.eleven.cms.service.IVrbtAppUserService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.RemoteResult;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.IntStream;
/**
 * @Description: 视频彩铃app用户积分
 * @Author: jeecg-boot
 * @Date:   2024-04-16
 * @Version: V1.0
 */
@Slf4j
@Service
public class VrbtAppUserPointsServiceImpl extends ServiceImpl<VrbtAppUserPointsMapper, VrbtAppUserPoints> implements IVrbtAppUserPointsService {
    public static final String SIGN_IN_SUCCEED="2";//已签到
    public static final String NOT_SIGN_IN="1";//未签到
    public static final String NO_TO_DAY="1";//是否当天(1=否,2=是)
    public static final String YES_TO_DAY="2";//是否当天(1=否,2=是)
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IVrbtAppUserService vrbtAppUserService;
    @Override
    public Result<?> points(String mobile, String pointsType) {
        VrbtAppUser vrbtAppUser=vrbtAppUserService.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).select(VrbtAppUser::getTotalPoints,VrbtAppUser::getSignInDay,VrbtAppUser::getVersion,VrbtAppUser::getMobile,VrbtAppUser::getId).orderByDesc(VrbtAppUser::getRegisterTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrbtAppUser==null){
            return Result.needLogin(0);
        }
        if(BizConstant.INITB_POINTS.equals(pointsType)){
            return Result.ok("查询成功",vrbtAppUser.getTotalPoints());
        }
        //查询订购积分是否已赠送
        LocalDateTime subPointsStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        LocalDateTime subPointsEnd=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        //查询当月订购积分是否已发放
        boolean subPoints=this.lambdaQuery().eq(VrbtAppUserPoints::getMobile,mobile).eq(VrbtAppUserPoints::getPointsType,BizConstant.SUB_POINTS).between(VrbtAppUserPoints::getPointsTime,subPointsStart,subPointsEnd).count()>0;
        //判断用户是否包月
        final RemoteResult remoteResult =miguApiService.asMemberQuery(mobile,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
        //查询是否当月订购
        boolean sub=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getChannel,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174).eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).between(Subscribe::getCreateTime,subPointsStart,subPointsEnd).count()>0;

        if(!subPoints && BizConstant.SUB_POINTS.equals(pointsType) && remoteResult.isAsMember()  && sub){
            VrbtAppUserPoints points=new VrbtAppUserPoints();
            /**手机号*/
            points.setMobile(mobile);
            /**渠道号*/
            points.setChannel(MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
            /**包月状态(-1=初始,0=未包月,1=已包月)*/
            points.setSubStatus(BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS);
            /**积分*/
            points.setPoints(BizConstant.SUB_POINTS_NUMBER);
            /**积分类型(0=初始,1=订购,2=签到)*/
            points.setPointsType(Integer.valueOf(BizConstant.SUB_POINTS));
            /**积分获取时间*/
            points.setPointsTime(new Date());
            this.save(points);
            //设置总积分 新用户订购赠送2000积分
            Integer totalPoints=Integer.valueOf(vrbtAppUser.getTotalPoints().intValue()+BizConstant.SUB_POINTS_NUMBER.intValue());
            vrbtAppUser.setSubStatus(BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS);
            vrbtAppUser.setTotalPoints(totalPoints);
            vrbtAppUserService.editVrbtAppUser(vrbtAppUser);
            return Result.ok("订购积分赠送成功",totalPoints);
        }
        //查询签到积分是否已赠送
        LocalDateTime signInPointsStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime signInPointsEnd=    LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        //查询当日签到积分是否已发放
        boolean signInPoints=this.lambdaQuery().eq(VrbtAppUserPoints::getMobile,mobile).eq(VrbtAppUserPoints::getPointsType,BizConstant.SIGN_IN_POINTS).between(VrbtAppUserPoints::getPointsTime,signInPointsStart,signInPointsEnd).count()>0;
        if(!signInPoints && BizConstant.SIGN_IN_POINTS.equals(pointsType)){
            VrbtAppUserPoints points=new VrbtAppUserPoints();
            /**手机号*/
            points.setMobile(mobile);
            /**渠道号*/
            points.setChannel(MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174);
            /**包月状态(-1=初始,0=未包月,1=已包月)*/

            //判断用户是否有订购记录
            boolean signInSub=subscribeService.lambdaQuery().eq(Subscribe::getMobile,mobile).eq(Subscribe::getChannel,MiguApiService.BIZ_AS_MEMBER_CHANNEL_CODE_174).eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS).count()>0;
            //设置包月状态
            Integer subStatus=(remoteResult.isAsMember() && signInSub)?BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS:BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;

            points.setSubStatus(subStatus);
            /**积分*/
            points.setPoints(BizConstant.SIGN_IN_POINTS_NUMBER);
            /**积分类型(0=初始,1=订购,2=签到)*/
            points.setPointsType(Integer.valueOf(BizConstant.SIGN_IN_POINTS));
            /**积分获取时间*/
            points.setPointsTime(new Date());
            this.save(points);

            //判断昨天是否签到
            int signInDay=0;
            LocalDateTime yesterdayStart = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN);
            LocalDateTime yesterdayEnd=    LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MAX);
            //获取签到次数
            boolean yesterdayPoints=this.lambdaQuery().eq(VrbtAppUserPoints::getMobile,mobile).eq(VrbtAppUserPoints::getPointsType,BizConstant.SIGN_IN_POINTS).between(VrbtAppUserPoints::getPointsTime,yesterdayStart,yesterdayEnd).count()>0;
            if(yesterdayPoints){
                signInDay=vrbtAppUser.getSignInDay()+1;
            }else{
                signInDay=1;
            }
            //设置总积分 签到赠送50积分
            Integer totalPoints=Integer.valueOf(vrbtAppUser.getTotalPoints().intValue()+BizConstant.SIGN_IN_POINTS_NUMBER.intValue());

            vrbtAppUser.setSubStatus(subStatus);
            vrbtAppUser.setSignInDay(signInDay);
            vrbtAppUser.setTotalPoints(totalPoints);
            vrbtAppUserService.editVrbtAppUser(vrbtAppUser);
            return Result.ok("签到成功",totalPoints);
        }
        if(BizConstant.SIGN_IN_POINTS.equals(pointsType)){
            return Result.ok("已签到",vrbtAppUser.getTotalPoints());
        }else if(BizConstant.SUB_POINTS.equals(pointsType)){
            return Result.ok("订购积分已赠送",vrbtAppUser.getTotalPoints());
        }
        return Result.error("系统错误",vrbtAppUser.getTotalPoints());
    }

    @Override
    public Result<?> pointsList(String mobile) {
        VrbtAppUser vrbtAppUser=vrbtAppUserService.lambdaQuery().eq(VrbtAppUser::getMobile,mobile).select(VrbtAppUser::getSignInDay,VrbtAppUser::getTotalPoints).orderByDesc(VrbtAppUser::getRegisterTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrbtAppUser==null){
            return Result.needLogin(0);
        }
        List<VrbtAppUserPointsDto> vrbtAppUserPointsDtoList= Lists.newArrayList();
        //获取包含当天的5天之前的全部日期
        List<LocalDateTime> minusDays = minusDays(5);
        //签到的第几天
        int signInDays=1;
        for (int i=0;i<minusDays.size();i++){
            LocalDateTime yesterdayStart=minusDays.get(i);
            LocalDateTime yesterdayEnd=LocalDateTime.of(yesterdayStart.toLocalDate(), LocalTime.MAX);
            //查询最近5天的签到记录（根据当日开始时间和当日结束时间查询）
            Boolean isSignIn=this.lambdaQuery().eq(VrbtAppUserPoints::getMobile,mobile).eq(VrbtAppUserPoints::getPointsType,BizConstant.SIGN_IN_POINTS).between(VrbtAppUserPoints::getPointsTime,yesterdayStart,yesterdayEnd).count()>0;
            if(isSignIn){
                VrbtAppUserPointsDto dto=new VrbtAppUserPointsDto();
                /**签到日期*/
                dto.setSignInDays("第"+signInDays+"天");
                /**签到类型(1=未签到,2=签到) */
                dto.setSignInType(SIGN_IN_SUCCEED);
                //是否当天(1=否,2=是)
                if(yesterdayStart.toLocalDate().equals(LocalDate.now())){
                    dto.setIsSignInDays(YES_TO_DAY);
                }else{
                    dto.setIsSignInDays(NO_TO_DAY);
                }
                vrbtAppUserPointsDtoList.add(dto);
                signInDays++;
            }else{
                //当日未签到，手动添加数据
                if(yesterdayStart.toLocalDate().equals(LocalDate.now())){
                    VrbtAppUserPointsDto dto=new VrbtAppUserPointsDto();
                    /**签到日期*/
                    dto.setSignInDays("第"+signInDays+"天");
                    /**签到类型(1=未签到,2=签到) */
                    dto.setSignInType(NOT_SIGN_IN);
                    //是否当天(1=否,2=是)
                    dto.setIsSignInDays(YES_TO_DAY);
                    vrbtAppUserPointsDtoList.add(dto);
                }else{
                    //签到断开，清除列表数据，并重置签到天数
                    vrbtAppUserPointsDtoList.clear();
                    signInDays=1;
                }
            }

        }
        if(vrbtAppUserPointsDtoList.size()<5){
            int signInDay=vrbtAppUserPointsDtoList.size()+1;
            //获取不包含当天的之后的差额全部日期
            List<LocalDateTime> plusDays = plusDays(5-vrbtAppUserPointsDtoList.size());
            for (int i=0;i<plusDays.size();i++) {
                VrbtAppUserPointsDto dto = new VrbtAppUserPointsDto();
                /**签到日期*/
                dto.setSignInDays("第" + signInDay + "天");
                /**签到类型(1=未签到,2=签到) */
                dto.setSignInType(NOT_SIGN_IN);
                //是否当天(1=否,2=是)
                dto.setIsSignInDays(NO_TO_DAY);
                vrbtAppUserPointsDtoList.add(dto);
                signInDay++;
            }
        }

        Map<String,Object> map= Maps.newLinkedHashMap();
        map.put("signInDay",vrbtAppUser.getSignInDay().toString());
        map.put("signInList",vrbtAppUserPointsDtoList);
        map.put("totalPoints",vrbtAppUser.getTotalPoints());
        return Result.ok("查询成功",map);
    }

    /**
     * 包含当天之前的天数
     * @param days
     * @return
     */
    public static List<LocalDateTime> minusDays(int days) {
        LocalDateTime now = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        List<LocalDateTime> minusDays=IntStream.rangeClosed(1, days).mapToObj(num -> now.minusDays(num - 1)).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        Collections.sort(minusDays);
        return minusDays;
    }
    /**
     * 不包含当天之后的天数
     * @param days
     * @return
     */
    public static List<LocalDateTime> plusDays(int days) {
        LocalDateTime now = LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MIN);
        List<LocalDateTime> plusDays=IntStream.rangeClosed(1, days).mapToObj(num -> now.plusDays(num - 1)).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        Collections.sort(plusDays);
        return plusDays;
    }
}
