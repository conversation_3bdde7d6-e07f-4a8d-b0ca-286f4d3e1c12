package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ColumnMusic;
import com.eleven.cms.entity.Column;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.*;
import org.jeecg.common.api.vo.Result;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: 栏目
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
public interface IColumnService extends IService<Column> {

	/**
	 * 添加一对多
	 *
	 */
	public void saveMain(Column column,List<ColumnMusic> columnMusicList) ;

	/**
	 * 修改一对多
	 *
	 */
	public void updateMain(Column column,List<ColumnMusic> columnMusicList);

	/**
	 * 删除一对多
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

    List<ColumnDetail> listColumnDetail();

    IPage<Column> pageNew(Page<Column> page, QueryWrapper<Column> queryWrapper, Map<String, String[]> parameterMap);

    void copy(String oldColumnClassName,String newColumnClassName);

    List<ColumnDetail> topic();

    //IPage<ColumnDetail> queryColumnDetailPage(String columnId, HttpServletRequest req, Integer pageNo, Integer pageSize);

	ColumnObject queryColumnList();

	IPage<MusicVo> queryMusicList(String columnId,String columnClassName,Page<MusicVo> page);
}
