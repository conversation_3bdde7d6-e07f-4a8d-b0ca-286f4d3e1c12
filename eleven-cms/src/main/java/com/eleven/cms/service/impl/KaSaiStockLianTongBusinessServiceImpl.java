package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * 卡塞存量业务联通
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:24
 **/
@Slf4j
@Service
public class KaSaiStockLianTongBusinessServiceImpl implements IBusinessCommonService {
    public static final String LIANTONG_SMS_INVALID_KEY_PREFIX = "liantong::SmsInvalid:";
    public static final long LIANTONG_SMS_INVALID_CACHE_SECONDS = 60;
    @Autowired
    private IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    private MobileRegionService mobileRegionService;
    @Autowired
    private IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IKaSaiStockService kaSaiStockService;
    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        String mobile = subscribe.getMobile();
        String channel=subscribe.getChannel();
        if (adSiteBusinessConfigService.isBlack(channel, subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspLiantong()) {
                    if (!provinceBusinessChannelConfigService.allow(channel, mobileRegionResult.getProvince())) {
                        log.warn("联通省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持联通用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        Result result = SpringContextUtils.getBean(KaSaiStockLianTongBusinessServiceImpl.class).receiveOrder(subscribe);
        return result;
    }

    @Override
    public Result receiveOrder(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }

        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = LIANTONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", LIANTONG_SMS_INVALID_CACHE_SECONDS);
            subscribe.setIspOrderNo(target.getIspOrderNo());
            subscribe.setId(transactionId);
            final Result<?> submitResult = kaSaiStockService.liantongSubmitSub(subscribe);
            String result = "卡塞存量业务联通订购结果==>{\"resCode\":\"" + submitResult.getCode() + "\",\"resMsg\":\"" + submitResult.getMessage() + "\"}";

            Subscribe upd = new Subscribe();
            upd.setId(target.getId());
            //已提交验证码
            if(submitResult.isOK()){
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setExtra(submitResult.getResult().toString());
                redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            }else if(!submitResult.isOK()){
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            }
            upd.setResult(result);
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return submitResult;
        }

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        String orderId= IdWorker.get32UUID();
        subscribe.setIspOrderNo(orderId);
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final Result<?> result = kaSaiStockService.liantongSendSms(subscribe);
        if (result != null && result.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
            return Result.noauth("短信发送成功",subscribe.getId());
        }
        return result;
    }
}
