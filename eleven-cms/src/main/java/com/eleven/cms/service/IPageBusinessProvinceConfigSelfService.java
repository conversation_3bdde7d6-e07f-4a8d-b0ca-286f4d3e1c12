package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.PageBusinessProvinceConfigSelf;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * 落地页业务省份配置自有 Service接口
 *
 * <AUTHOR>
 * @date 2024-07-09 15:34:01
 */
public interface IPageBusinessProvinceConfigSelfService extends IService<PageBusinessProvinceConfigSelf> {

    ObjectNode getPopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province);

    ObjectNode getSparePopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province);

    PageBusinessProvinceConfigSelf getPageBusinessProvinceConfigSelf(String pageId, String province);

}
