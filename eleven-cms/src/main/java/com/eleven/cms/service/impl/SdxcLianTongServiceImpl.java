package com.eleven.cms.service.impl;

import com.eleven.cms.ad.SdxcLianTongProperties;
import com.eleven.cms.config.SdxcLianTongProduct;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISdxcLianTongService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.SdxcLianTongResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 时代星辰联通业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/2 10:07
 **/
@Slf4j
@Service
public class SdxcLianTongServiceImpl implements ISdxcLianTongService {
    //是否必填 0 否 1 是
    public static final String IS_REQUIRED="1";
    public static final String SDXC_LIANTONG_STATUS_SUB_SUCCESS = "1"; //联通开通成功
    public static final String SDXC_YIDONG_STATUS_SUB_SUCCESS = "delivrd"; //移动开通成功
    public static final String PROVINCE_LT_AH="安徽";
    public static final String PROVINCE_LT_ZJ="浙江";
    public static Map<String, String> SHIDAIXINGCHEN_LT = new ImmutableMap.Builder<String, String>()
            .put(PROVINCE_LT_AH, "SDXCLT26")
            .put(PROVINCE_LT_ZJ, "SDXCLT25")
            .build();


    public static final String PROVINCE_YD_NMG="内蒙古";

    public static Map<String, String> SHIDAIXINGCHEN_YD = new ImmutableMap.Builder<String, String>()
            .put(PROVINCE_YD_NMG, "SDXCYD19")
            .build();

    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    SdxcLianTongProperties sdxcLianTongProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public Result<?> sendMessage(String mobile, Subscribe subscribe) {
        SdxcLianTongProduct sdxcLianTongProduct=null;
        if(BizConstant.BIZ_CHANNEL_SDXC_LT_VRBT.equals(subscribe.getChannel())){
            sdxcLianTongProduct=sdxcLianTongProperties.getProductMap().get(SHIDAIXINGCHEN_LT.get(subscribe.getProvince()));
        }else if(BizConstant.BIZ_CHANNEL_SDXC_YD_VRBT.equals(subscribe.getChannel())){
            sdxcLianTongProduct=sdxcLianTongProperties.getProductMap().get(SHIDAIXINGCHEN_YD.get(subscribe.getProvince()));
        }
        if(sdxcLianTongProduct==null){
            return Result.error("获取产品配置失败");
        }
        StringBuffer valueStr = new StringBuffer();
        valueStr.append("?channelNo=" + sdxcLianTongProduct.getChannelNo());
        valueStr.append("&price=" + sdxcLianTongProduct.getPrice());
        valueStr.append("&phone=" + subscribe.getMobile());
        try {
            if(IS_REQUIRED.equals(sdxcLianTongProduct.getAppPackage())){
                valueStr.append("&appPackage=" + URLEncoder.encode(StringUtil.isEmpty(subscribe.getReferer())?"":subscribe.getReferer(), "utf-8"));
            }
            if(IS_REQUIRED.equals(sdxcLianTongProduct.getAppName())){
                valueStr.append("&appName="+URLEncoder.encode(StringUtil.isEmpty(subscribe.getReferer())?"":subscribe.getReferer(), "utf-8"));
            }
            if(IS_REQUIRED.equals(sdxcLianTongProduct.getUserAgent())){
                valueStr.append("&userAgent="+URLEncoder.encode(StringUtil.isEmpty(subscribe.getUserAgent())?"":subscribe.getUserAgent(), "utf-8"));
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }


        valueStr.append("&ip=" + subscribe.getIp());
        String content =implementHttpGetResult(sdxcLianTongProperties.getGetSendSmsUrl()+valueStr.toString(),"时代星辰联通业务"+sdxcLianTongProduct.getProductName()+"获取验证码接口",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("获取验证码失败");
        }
        try {
            SdxcLianTongResult result = mapper.readValue(content, SdxcLianTongResult.class);
            if(result.isOK() && StringUtil.isNotBlank(result.getLinkid())){
                return Result.ok("获取验证码成功",result.getLinkid());
            }
            return Result.error(result.getMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("获取验证码失败");
    }

    @Override
    public Result<?> submitOrder(Subscribe subscribe) {
        SdxcLianTongProduct sdxcLianTongProduct=null;
        if(BizConstant.BIZ_CHANNEL_SDXC_LT_VRBT.equals(subscribe.getChannel())){
            sdxcLianTongProduct=sdxcLianTongProperties.getProductMap().get(SHIDAIXINGCHEN_LT.get(subscribe.getProvince()));
        }else if(BizConstant.BIZ_CHANNEL_SDXC_YD_VRBT.equals(subscribe.getChannel())){
            sdxcLianTongProduct=sdxcLianTongProperties.getProductMap().get(SHIDAIXINGCHEN_YD.get(subscribe.getProvince()));
        }
        if(sdxcLianTongProduct==null){
            return Result.error("获取产品配置失败");
        }
        StringBuffer valueStr = new StringBuffer();
        valueStr.append("?linkid=" + subscribe.getIspOrderNo());
        valueStr.append("&vcode=" + subscribe.getSmsCode());
        String content =implementHttpGetResult(sdxcLianTongProperties.getSubmitSendSmsUrl()+valueStr.toString(),"时代星辰联通业务"+sdxcLianTongProduct.getProductName()+"提交验证码接口",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("提交验证码失败");
        }
        try {
            SdxcLianTongResult result = mapper.readValue(content, SdxcLianTongResult.class);
            if(result.isOK()){
                return Result.ok("提交验证码成功");
            }
            return Result.error(result.getMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("提交验证码失败");
    }

    @Override
    public void sdxcLianTongNotify(String phone, String status, String linkid) {
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, phone).eq(Subscribe::getIspOrderNo, linkid).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (BIZ_CHANNEL_SDXC_LT_VRBT.equals(subscribe.getChannel()) && SDXC_LIANTONG_STATUS_SUB_SUCCESS.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else if(BIZ_CHANNEL_SDXC_YD_VRBT.equals(subscribe.getChannel()) && SDXC_YIDONG_STATUS_SUB_SUCCESS.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else{
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\"开通失败\"}";
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
    }


    /**
     * 发起http请求get
     */
    public String implementHttpGetResult(String url,String msg,String mobile) {
        return pushGet(url,msg,mobile);
    }

    public String pushGet(String url,String msg,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{}",mobile,url);
        Request request = new Request.Builder().url(url).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},响应参数:{}",mobile,url,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{}",mobile,url,e);
            return null;
        }
    }

}
