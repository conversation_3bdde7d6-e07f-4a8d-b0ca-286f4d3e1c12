package com.eleven.cms.service.impl;

import com.eleven.cms.config.*;
import com.eleven.cms.entity.Alipay;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WoReadOrder;
import com.eleven.cms.mapper.WoReadOrderMapper;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IWoReadOrderService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.net.URLDecoder;
import java.util.Date;

/**
 * @Description: cms_wo_read_order
 * @Author: jeecg-boot
 * @Date:   2023-05-29
 * @Version: V1.0
 */
@Service
public class WoReadOrderServiceImpl extends ServiceImpl<WoReadOrderMapper, WoReadOrder> implements IWoReadOrderService {

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private WoReadNodeProperties woReadNodeProperties;
    @Autowired
    private WoReadOutSideProperties woReadOutSideProperties;

    @Override
    public void saveOrder(Subscribe subscribe,String contractCode,Integer payType,String company) {

        WoReadOrder woReadOrder = new WoReadOrder();
        WoReadProperties woReadProperties = woReadNodeProperties.getWoReadConfig(company);
        try{
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if(StringUtils.isNotBlank(subscribe.getSource())){
                subscribe = parseLink(subscribe.getSource(),subscribe);
            }

            //保存订单信息
            woReadOrder.setMobile(subscribe.getMobile());
            woReadOrder.setBusinessType(subscribe.getBusinessType());
            woReadOrder.setBusinessName(subscribe.getBusinessType());
            woReadOrder.setChannel(subscribe.getChannel());
            woReadOrder.setSubChannel(subscribe.getSubChannel());
            woReadOrder.setOrderAmount("9.9");
            woReadOrder.setPayType(payType);
            woReadOrder.setOrderNo(contractCode);
            woReadOrder.setOrderStatus(0);
            woReadOrder.setOrderTime(new Date());
            woReadOrder.setOutTradeNo(subscribe.getIspOrderNo());
            if(1==payType){
                woReadOrder.setChannelId(woReadProperties.getWx().getChannelId());
            }else if(2==payType){
                woReadOrder.setChannelId(woReadProperties.getAli().getChannelId());
            }
            this.save(woReadOrder);

            //保存sub信息
            subscribe.setProvince(mobileRegionResult!=null?mobileRegionResult.getProvince():"未知");
            subscribe.setCity(mobileRegionResult!=null?mobileRegionResult.getCity():"未知");
            String isp = mobileRegionResult!=null?mobileRegionResult.getOperator():"未知";
            subscribe.setIsp(isp);
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
            subscribe.setChannel(subscribe.getBusinessType());
            subscribe.setBizType("MEMBER_ALIPAY");
            subscribe.setResult("未签约");
            subscribeService.createSubscribeDbAndEs(subscribe);
        } catch (Exception e) {
            log.error("联通沃悦读签约sub订单出错：",e);
        }
    }

    public void saveOutSideOrder(Subscribe subscribe,String contractCode,Integer payType) {

        WoReadOrder woReadOrder = new WoReadOrder();
        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if(StringUtils.isNotBlank(subscribe.getSource())){
            subscribe = parseLink(subscribe.getSource(),subscribe);
        }

        //保存订单信息
        woReadOrder.setMobile(subscribe.getMobile());
        woReadOrder.setBusinessType(subscribe.getBusinessType());
        woReadOrder.setBusinessName(subscribe.getBusinessType());
        woReadOrder.setChannel(subscribe.getChannel());
        woReadOrder.setSubChannel(subscribe.getSubChannel());
        woReadOrder.setOrderAmount("15");
        woReadOrder.setPayType(payType);
        woReadOrder.setOrderNo(contractCode);
        woReadOrder.setOrderStatus(0);
        woReadOrder.setOrderTime(new Date());
        woReadOrder.setOutTradeNo(subscribe.getExtra());
        woReadOrder.setChannelId(woReadOutSideProperties.getChannelId());
        woReadOrder.setBizType("WO_READ_FEE");
        this.save(woReadOrder);

        //保存sub信息
        //subscribe.setProvince(mobileRegionResult!=null?mobileRegionResult.getProvince():"未知");
        //subscribe.setCity(mobileRegionResult!=null?mobileRegionResult.getCity():"未知");
        //String isp = mobileRegionResult!=null?mobileRegionResult.getOperator():"未知";
        //subscribe.setIsp(isp);
        //subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
        //subscribe.setChannel(subscribe.getChannel());
        //subscribe.setBizType(subscribe.getBusinessType());
        //subscribe.setResult("订单发起");
        //subscribeService.createSubscribeDbAndEs(subscribe);
    }

    private Subscribe parseLink(String source, Subscribe subscribe) {
        try{
            source = URLDecoder.decode(source, "UTF-8");
            String subChannel = HttpUrl.parse(source.replace("#/", "")).queryParameter("subChannel");
            subscribe.setSubChannel(subChannel);
            subscribe.setSource(source);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return subscribe;
    }
}
