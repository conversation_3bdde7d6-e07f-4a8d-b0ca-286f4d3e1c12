package com.eleven.cms.service.impl.pay;

import com.alibaba.fastjson.JSONObject;

import com.eleven.cms.config.QyclWXPayPropertiesConfig;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.service.IWechatConfigLogService;
import com.eleven.cms.service.pay.IWechatPayAppService;
import com.eleven.cms.util.AESUtils;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.wxpay.sdk.WXPayUtil;
import com.moczul.ok2curl.CurlInterceptor;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import com.wechat.pay.java.core.util.ShaUtil;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.util.*;

import static com.eleven.cms.util.MD5Util.bytesToHex;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/23 18:55
 **/
@Slf4j
@Service
public class WechatPayAppServiceImpl implements IWechatPayAppService {
    private static final String APP_PAY_METHOD_URL="https://api.mch.weixin.qq.com/v3/pay/transactions/app";
    private static final String MINI_APP_PAY_METHOD_URL=" https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";
    private static final String APP_PAY_METHOD="/v3/pay/transactions/app";
    private static final String MIniAPP_PAY_METHOD="/v3/pay/transactions/jsapi";

    private static final String APP_REFUND_METHOD_URL="https://api.mch.weixin.qq.com/v3/refund/domestic/refunds";
    private static final String APP_REFUND_METHOD="/v3/refund/domestic/refunds";

    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private QyclWXPayPropertiesConfig payConfig;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    public static JsapiServiceExtension SERVICE = null ;
    static {
        if(null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)){
            Security.addProvider(new BouncyCastleProvider());
        }

    }



    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    /**
     * 微信app支付
     * @param outTradeNo 订单号
     * @param total 金额
     * @param channel 渠道号
     * @return
     */
    @Override
    public WechatPayResult wechatAppPay(String outTradeNo, String total, String channel){
        WechatConfigLog wechatConfigLog = wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getBusinessType, channel).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appid",wechatConfigLog.getAppId());
        jsonObject.put("mchid",wechatConfigLog.getMchId());
        jsonObject.put("description",wechatConfigLog.getPublicName());
        jsonObject.put("out_trade_no",outTradeNo);
        jsonObject.put("notify_url",wechatConfigLog.getNotifyUrl());
        String totalAmount= BigDecimal.valueOf(Double.valueOf(total)).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        JSONObject amount = new JSONObject();
        amount.put("total",Integer.valueOf(totalAmount));
        amount.put("currency","CNY");
        jsonObject.put("amount",amount);

        String token = getToken("POST", APP_PAY_METHOD, jsonObject.toString(),wechatConfigLog.getMchId(),wechatConfigLog.getMchSerialNo());
        Headers headers = new Headers.Builder()
                .add("Authorization", "WECHATPAY2-SHA256-RSA2048 " + token)
                .add("Content-Type", "application/json")
                .add("Accept", "application/json")
                .build();
        RequestBody body = RequestBody.create(JSON, jsonObject.toString());
        Request request = new Request.Builder().url(APP_PAY_METHOD_URL).post(body).headers(headers).build();
        try (Response response =client.newCall(request).execute()){
            String content = response.body().string();
            JsonNode jsonNode = null;
            try {
                jsonNode = mapper.readTree(content);
            } catch (JsonProcessingException e) {
                log.error("微信app下单-系统异常-content:{}",content, e);
            }
            String nonceStr= WXPayUtil.generateNonceStr();
            long timestamp = System.currentTimeMillis() / 1000;
            String prepayId = jsonNode.get("prepay_id").asText("");
            JSONObject payObject = new JSONObject();

            WechatPayResult wechatPayResult = new WechatPayResult();
            wechatPayResult.setAppId(wechatConfigLog.getAppId());
            wechatPayResult.setPrepayId(prepayId);
            wechatPayResult.setPackageValue("Sign=WXPay");
            wechatPayResult.setNonceStr(nonceStr);
            wechatPayResult.setTimestamp(timestamp);
            wechatPayResult.setPartnerId(wechatConfigLog.getMchId());
            wechatPayResult.setSign(getSign(wechatConfigLog.getAppId(),timestamp,nonceStr,prepayId,wechatConfigLog.getMchId()));
            payObject.put("sign",getSign(wechatConfigLog.getAppId(),timestamp,nonceStr,prepayId,wechatConfigLog.getMchId()));
            return wechatPayResult;
        } catch (IOException e) {
            log.error("微信app支付-请求失败-系统异常:{}",outTradeNo,e);
            throw new BusinessException("微信app支付异常");
        }
    }




    @Override
    public WechatPayResult wechatMiniAppPay(WechatMiniAppPayParam payParam) {
        WechatConfigLog wechatConfigLog = wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getBusinessType, payParam.getBusinessType()).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appid",wechatConfigLog.getAppId());
        jsonObject.put("mchid",wechatConfigLog.getMchId());
        jsonObject.put("description",wechatConfigLog.getPublicName());
        jsonObject.put("out_trade_no",payParam.getOrderNo());
        jsonObject.put("notify_url",wechatConfigLog.getNotifyUrl());
        JSONObject amount = new JSONObject();

        amount.put("total",payParam.getAmount());
        amount.put("currency","CNY");
        jsonObject.put("amount",amount);
        JSONObject payer = new JSONObject();
        payer.put("openid",payParam.getOpenId());
        jsonObject.put("payer",payer);
        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr= WXPayUtil.generateNonceStr();

        String token = getToken("POST", MIniAPP_PAY_METHOD,nonceStr, jsonObject.toString(),wechatConfigLog.getMchId(),wechatConfigLog.getMchSerialNo());
        Headers headers = new Headers.Builder()
                .add("Authorization", "WECHATPAY2-SHA256-RSA2048 " + token)
                .add("Content-Type", "application/json")
                .add("Accept", "application/json")
                .build();
        RequestBody body = RequestBody.create(JSON, jsonObject.toString());
        log.info("小程序支付入参:{}",jsonObject.toString());
        Request request = new Request.Builder().url(MINI_APP_PAY_METHOD_URL).post(body).headers(headers).build();
        try (Response response =client.newCall(request).execute()){
            String content = response.body().string();
            log.info("小程序支付返回:{}",content);
            JsonNode jsonNode = null;
            try {
                jsonNode = mapper.readTree(content);
            } catch (JsonProcessingException e) {
                log.error("微信app下单-系统异常-content:{}",content, e);
            }
            String prepayId = jsonNode.get("prepay_id").asText("");
            WechatPayResult wechatPayResult = new WechatPayResult();
            wechatPayResult.setAppId(wechatConfigLog.getAppId());
            wechatPayResult.setPrepayId(prepayId);
            String  packageValue = "prepay_id="+prepayId;
            wechatPayResult.setPackageValue(packageValue);
            wechatPayResult.setNonceStr(nonceStr);
            wechatPayResult.setTimestamp(timestamp);
            wechatPayResult.setPartnerId(wechatConfigLog.getMchId());
            wechatPayResult.setSign(getSign(wechatConfigLog.getAppId(),timestamp,nonceStr,packageValue,wechatConfigLog.getMchId()));
            return wechatPayResult;
        } catch (IOException e) {
            log.error("微信app支付-请求失败-系统异常:{}",payParam.getOrderNo(),e);
            throw new BusinessException("微信app支付异常");
        }
    }

    private String getSign(String appId, long timestamp, String nonceStr, String prepayId, String mchId) {
        String message = appId + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + prepayId + "\n";
        String signature = null;
        try {
            signature = sign(message.getBytes("utf-8"),mchId);
        } catch (SignatureException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return signature;
    }


    private String getToken(String method, String url, String nonceStr, String body, String mchid, String mchSerialNo) {
        long timestamp = System.currentTimeMillis() / 1000;
        String message = method + "\n"
                + url + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + body + "\n";
        String signature = null;
        try {
            signature = sign(message.getBytes("utf-8"), mchid);
        } catch (SignatureException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "mchid=\"" + mchid + "\","
                + "nonce_str=\"" + nonceStr + "\","
                + "timestamp=\"" + timestamp + "\","
                + "serial_no=\"" + mchSerialNo + "\","
                + "signature=\"" + signature + "\"";
    }


    private String getToken(String method, String url, String body,String mchid,String mchSerialNo) {
        String nonceStr= WXPayUtil.generateNonceStr();
        long timestamp = System.currentTimeMillis() / 1000;
        String message = method + "\n"
                + url + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + body + "\n";
        String signature = null;
        try {
            signature = sign(message.getBytes("utf-8"),mchid);
        } catch (SignatureException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "mchid=\"" + mchid + "\","
                + "nonce_str=\"" + nonceStr + "\","
                + "timestamp=\"" + timestamp + "\","
                + "serial_no=\"" + mchSerialNo+ "\","
                + "signature=\"" + signature + "\"";
    }
    //生成签名的方法
    private static String sign(byte[] data, String mchid) throws SignatureException, NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException {
        PrivateKey privateKey =getPriKeyByP12(mchid);
        // 用私钥对信息进行数字签名
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data);
        return encryptBASE64(signature.sign());

    }

    /**
     *
     * BASE64Encoder 加密
     *
     * @param data 要加密的数据
     *
     * @return 加密后的字符串
     *
     */
    private static String encryptBASE64(byte[] data) {
        return new String(org.apache.commons.codec.binary.Base64.encodeBase64(data));
    }

    public static PrivateKey getPriKeyByP12(String mchId) {
        ClassPathResource classPathResource = new ClassPathResource("apiclient_key_"+mchId+".pem");
        InputStream certStream = null;
        try {
            certStream = classPathResource.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        PrivateKey privateKey = PemUtil.loadPrivateKey(certStream);
        return privateKey;
    }


    /**
     * 微信支付回调（平台-->用户）,并返回接收消息成功
     * @return   最终此次请求需要返回以下两种
     * String succMsg = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
     * String errMsg = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
     */
    @Override
    public WechatpayNotify payResultNotify(String resultNotifyXml) throws Exception {
        log.info("微信支付交易结果通知xml:{}", resultNotifyXml);
        //该方法在支付成功时已验证签名

        WechatpayNotifyParam notifyParam = mapper.readValue(resultNotifyXml, WechatpayNotifyParam.class);
        String resource= AESUtils.setDecryptData(notifyParam.getResource().getAssociatedData(),notifyParam.getResource().getNonce(),notifyParam.getResource().getCiphertext(),"xYBbiD7NTFGL73iua4bvYituW9mtVpDN");
        //需要做状态判断 防止重复收到回调！！！
        log.info("微信支付订单ID={}已收到支付回调，付款成功", resource);
        WechatpayNotify wechatpayNotify = mapper.readValue(resource, WechatpayNotify.class);
        return wechatpayNotify;

    }


    /**
     * 微信退款
     * @param outTradeNo
     * @param outRequestNo
     * @param refundAmount
     * @param totalAmount
     * @param refundReason
     * @param channel
     * @return
     */
    @Override
    public WechatRefundNotify appPayRefundConfig(String outTradeNo, String outRequestNo, String refundAmount, String totalAmount, String refundReason, String channel){
        WechatConfigLog wechatConfigLog = wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getBusinessType, channel).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("out_trade_no",outTradeNo);
        jsonObject.put("out_refund_no",outRequestNo);
        jsonObject.put("reason",refundReason);
        String refund= BigDecimal.valueOf(Double.valueOf(refundAmount)).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        String total= BigDecimal.valueOf(Double.valueOf(totalAmount)).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        JSONObject amount = new JSONObject();
        amount.put("refund",Integer.valueOf(refund));
        amount.put("total",Integer.valueOf(total));
        amount.put("currency","CNY");
        jsonObject.put("amount",amount);
        jsonObject.put("notify_url",wechatConfigLog.getNotifyUrl());
        String token = getToken("POST", APP_REFUND_METHOD, jsonObject.toString(),wechatConfigLog.getMchId(),wechatConfigLog.getMchSerialNo());
        Headers headers = new Headers.Builder()
                .add("Authorization", "WECHATPAY2-SHA256-RSA2048 " + token)
                .add("Content-Type", "application/json")
                .add("Accept", "application/json")
                .build();
        RequestBody body = RequestBody.create(JSON, jsonObject.toString());
        Request request = new Request.Builder().url(APP_REFUND_METHOD_URL).post(body).headers(headers).build();
        try (Response response =client.newCall(request).execute()){
            String content = response.body().string();
            log.info("微信app退款-content:{}",content);
            WechatRefundNotify refundNotify = mapper.readValue(content, WechatRefundNotify.class);
            return refundNotify;
        } catch (IOException e) {
            log.error("微信app退款-请求失败-系统异常:{}",outTradeNo,e);
            return null;
        }

    }

//    public static void main(String[] args) {
//        String content="{\"code\":\"NOT_ENOUGH\",\"message\":\"基本账户余额不足，请充值后重新发起\"}";
//        WechatRefundNotify refundNotify = JacksonUtils.readValue(content, WechatRefundNotify.class);
//        System.out.println(refundNotify);
//    }
    /**
     * 微信退款回调（平台-->用户）,并返回接收消息成功
     * @return   最终此次请求需要返回以下两种
     * String succMsg = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
     * String errMsg = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
     */
    @Override
    public WechatpayNotify refundResultNotify(String resultNotifyXml) {
        log.info("微信退款交易结果通知xml:{}", resultNotifyXml);
        //该方法在支付成功时已验证签名
        try {
            WechatpayNotifyParam notifyParam = mapper.readValue(resultNotifyXml, WechatpayNotifyParam.class);
            String resource= AESUtils.setDecryptData(notifyParam.getResource().getAssociatedData(),notifyParam.getResource().getNonce(),notifyParam.getResource().getCiphertext(),"xYBbiD7NTFGL73iua4bvYituW9mtVpDN");
            //需要做状态判断 防止重复收到回调！！！
            log.info("微信支付订单ID={}已收到退款回调，退款成功", resource);
            WechatpayNotify wechatpayNotify = mapper.readValue(resource, WechatpayNotify.class);
            return wechatpayNotify;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
