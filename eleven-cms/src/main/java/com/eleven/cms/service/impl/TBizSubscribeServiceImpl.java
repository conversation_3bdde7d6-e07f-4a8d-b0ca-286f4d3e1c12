package com.eleven.cms.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.TBizSubscribe;
import com.eleven.cms.mapper.TBizSubscribeMapper;
import com.eleven.cms.service.ITBizSubscribeService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @datetime 2025/2/18 15:39
 */
@DS("member")
@Service
public class TBizSubscribeServiceImpl extends ServiceImpl<TBizSubscribeMapper, TBizSubscribe> implements ITBizSubscribeService {

}
