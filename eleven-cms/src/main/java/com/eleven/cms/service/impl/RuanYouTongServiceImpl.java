package com.eleven.cms.service.impl;

import com.eleven.cms.ad.RuanYouTongProperties;
import com.eleven.cms.config.RuanYouTongProduct;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.IRuanYouTongService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.RuanYouTongSmsCodeResult;
import com.eleven.cms.vo.RuanYouTongSubmitCodeResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 软游通业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/22 17:08
 **/
@Slf4j
@Service
public class RuanYouTongServiceImpl implements IRuanYouTongService {
    public static final String RUANYOUTONG_STATUS_SUB_SUCCESS = "1"; //开通成功
    public static final String RUANYOUTONG_IS_PARAMETER = "1"; //是否传参
    //联通省份和业务
    public static final String PROVINCE_LT_GD="广东";
    public static final String PROVINCE_LT_GZ="贵州";
    public static Map<String, String> RUANYOUTONG_LT = new ImmutableMap.Builder<String, String>()
            .put(PROVINCE_LT_GD, "RYT_LT_JIASUBAO")
            .put(PROVINCE_LT_GZ, "RYT_LT_CANGYOU")
            .build();
    //移动省份和业务
    public static final String PROVINCE_YD_HB="河北";
    public static final String PROVINCE_YD_HNCZ="河南";
    public static final String PROVINCE_YD_CQ="重庆";
    public static final String PROVINCE_YD_JX="江西";
    public static final String PROVINCE_YD_JS="江苏";
    public static final String PROVINCE_YD_NX="宁夏";
    public static final String PROVINCE_YD_SC="四川";
    public static final String PROVINCE_YD_HN="海南";
    public static final String PROVINCE_YD_NM="内蒙古";
    public static final String PROVINCE_YD_GD="广东";
    public static final String PROVINCE_YD_GZ="贵州";
    public static final String PROVINCE_YD_SH="上海";

    public static Map<String, String> RUANYOUTONG_YD = new ImmutableMap.Builder<String, String>()
            .put(PROVINCE_YD_HB, "RYT_YD_READ")
            .put(PROVINCE_YD_CQ, "RYT_YD_DIANFEI")
            .put(PROVINCE_YD_JX, "RYT_YD_VRBT")
            .put(PROVINCE_YD_JS, "RYT_YD_SXVRBT")
            .put(PROVINCE_YD_NX, "RYT_YD_MGSP")
            .put(PROVINCE_YD_SC, "RYT_YD_CHEZHU")
            .put(PROVINCE_YD_HN, "RYT_YD_HNFP")
            .put(PROVINCE_YD_HNCZ, "RYT_YD_HNCZ")
            .put(PROVINCE_YD_NM, "RYT_YD_DYVRBT")
            .put(PROVINCE_YD_GD, "RYT_YD_GDVRBT")
            .put(PROVINCE_YD_GZ, "RYT_YD_BJX")
            .put(PROVINCE_YD_SH, "RYT_YD_CHK")
            .build();
    public static final String PROVINCE_DX_YN="云南";

    public static Map<String, String> RUANYOUTONG_DX = new ImmutableMap.Builder<String, String>()
            .put(PROVINCE_DX_YN, "RYT_DX_YHY")
            .build();

    public static Map<Integer,String> RUANYOUTONG_MSG = ImmutableMap.<Integer,String>builder()
            .put(1,"成功")
            .put(2,"系统后台错误")
            .put(3,"参数错误")
            .put(4,"业务未开通")
            .put(5,"渠道不存在")
            .put(6,"订单号错误")
            .put(7,"手机号码错误")
            .put(8,"无法区分号码运营商")
            .put(9,"支持网关不同")
            .put(10,"验证码获取失败")
            .put(11,"验证码提交失败")
            .put(12,"您还未参与活动")
            .put(13,"日志未匹配")
            .put(14,"重复回调")
            .put(15,"IP鉴权失败")
            .put(16,"CODE_TOKEN获取失败")
            .put(17,"您的号码不支持_黑名单号码")
            .put(18,"CODE_已重新获取TOKEN")
            .put(19,"CODE_不在开通时间段")
            .put(1001,"CODE_省份到达日限量")
            .put(1002,"CODE_省份到达月限量").build();
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    RuanYouTongProperties ruanYouTongProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public Result<?> sendMessage(Subscribe subscribe) {
        RuanYouTongProduct ruanYouTongProduct=null;
        if(BizConstant.BIZ_CHANNEL_RUANYOUTONG_YD.equals(subscribe.getChannel())){
           String channelNo=RUANYOUTONG_YD.get(subscribe.getProvince());
           ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(channelNo);
        }else if(BizConstant.BIZ_CHANNEL_RYT_YD_XIANYU.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_YD_XIANYU);
        }else if(BizConstant.BIZ_CHANNEL_RYT_LT_JIAYOU.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_LT_JIAYOU);
        }else if(BizConstant.BIZ_CHANNEL_RYT_LT_GECAI.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_LT_GECAI);
        }else if(BizConstant.BIZ_CHANNEL_RUANYOUTONG_DX.equals(subscribe.getChannel())){
            String channelNo=RUANYOUTONG_DX.get(subscribe.getProvince());
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(channelNo);
        }else if(BizConstant.BIZ_CHANNEL_RYT_YD_QCYYB.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_YD_QCYYB);
        }else if(BizConstant.BIZ_CHANNEL_RYT_YD_SRQY.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_YD_SRQY);
        }else if(BizConstant.BIZ_CHANNEL_RYT_YD_YSYDH.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_YD_YSYDH);
        }else if(BizConstant.BIZ_CHANNEL_RYT_YD_HJX.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_YD_HJX);
        }else if(BizConstant.BIZ_CHANNEL_RYT_YD_CHIHUO.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_YD_CHIHUO);
        }else{
           String channelNo=RUANYOUTONG_LT.get(subscribe.getProvince());
           ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(channelNo);
        }
        if(ruanYouTongProduct==null){
            return Result.error("获取产品配置失败");
        }
        String ispOrderNo =ruanYouTongProduct.getCpparam()+RandomStringUtils.randomNumeric(20);
        StringBuffer valueStr = new StringBuffer();
        valueStr.append("?biz=" + ruanYouTongProduct.getBiz());
        valueStr.append("&ditch=" +ruanYouTongProduct.getDitch());
        valueStr.append("&mobile=" +subscribe.getMobile());
        valueStr.append("&cpparam=" + ispOrderNo);
        valueStr.append("&ip_address=" + subscribe.getIp());
        //客户下单浏览器
        if(RUANYOUTONG_IS_PARAMETER.equals(ruanYouTongProduct.getUserAgent())){
            valueStr.append("&user-agent=" + subscribe.getUserAgent());
        }
        //app包名
        if(RUANYOUTONG_IS_PARAMETER.equals(ruanYouTongProduct.getAppPackag())){
            valueStr.append("&x-app-packag=" + subscribe.getReferer());
        }
        //app名称
        if(RUANYOUTONG_IS_PARAMETER.equals(ruanYouTongProduct.getAppName())){
            valueStr.append("&x-app-name=" +subscribe.getReferer());
        }
        String content =implementHttpGetResult(ruanYouTongProperties.getGetSendSmsUrl()+valueStr.toString(),"软游通业务"+ruanYouTongProduct.getProductName()+"获取验证码接口",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("获取验证码失败");
        }
        try {
            RuanYouTongSmsCodeResult result = mapper.readValue(content, RuanYouTongSmsCodeResult.class);
            if(result.isOK() && StringUtil.isNotBlank(result.getOrderid())){
                return Result.ok("获取验证码成功",result.getOrderid()+"#"+ispOrderNo);
            }
            if(StringUtils.isBlank(result.getErrmsg())){
                return Result.error(RUANYOUTONG_MSG.get(result.getCode()));
            }else{
                return Result.error(result.getErrmsg());
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("获取验证码失败");
    }

    @Override
    public Result<?> submitOrder(Subscribe subscribe) {
        RuanYouTongProduct ruanYouTongProduct=null;
        if(BizConstant.BIZ_CHANNEL_RUANYOUTONG_YD.equals(subscribe.getChannel())){
            String channelNo=RUANYOUTONG_YD.get(subscribe.getProvince());
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(channelNo);
        }else if(BizConstant.BIZ_CHANNEL_RYT_YD_XIANYU.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_YD_XIANYU);
        }else if(BizConstant.BIZ_CHANNEL_RYT_LT_JIAYOU.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_LT_JIAYOU);
        }else if(BizConstant.BIZ_CHANNEL_RYT_LT_GECAI.equals(subscribe.getChannel())){
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(BizConstant.BIZ_CHANNEL_RYT_LT_GECAI);
        }else if(BizConstant.BIZ_CHANNEL_RUANYOUTONG_DX.equals(subscribe.getChannel())){
            String channelNo=RUANYOUTONG_DX.get(subscribe.getProvince());
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(channelNo);
        }else{
            String channelNo=RUANYOUTONG_LT.get(subscribe.getProvince());
            ruanYouTongProduct=ruanYouTongProperties.getProductMap().get(channelNo);
        }
        if(ruanYouTongProduct==null){
            return Result.error("获取产品配置失败");
        }

        StringBuffer valueStr = new StringBuffer();
        valueStr.append("?orderid=" + subscribe.getExtra());
        valueStr.append("&vcode=" + subscribe.getSmsCode());
        String content =implementHttpGetResult(ruanYouTongProperties.getSubmitSendSmsUrl()+valueStr.toString(),"软游通业务"+ruanYouTongProduct.getProductName()+"提交验证码接口",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("提交验证码失败");
        }
        try {
            RuanYouTongSubmitCodeResult result = mapper.readValue(content, RuanYouTongSubmitCodeResult.class);
            if(result.isOK()){
                return Result.ok("提交验证码成功");
            }
            if(StringUtils.isBlank(result.getErrmsg())){
                return Result.error(RUANYOUTONG_MSG.get(result.getCode()));
            }else{
                return Result.error(result.getErrmsg());
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("提交验证码失败");
    }

    @Override
    public void ruanYouTongNotify(String mobile, String status, String orderNo) {
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getIspOrderNo, orderNo).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (RUANYOUTONG_STATUS_SUB_SUCCESS.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
//                subscribeService.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//                    subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//                } else {
//                    String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//                    if (StringUtils.isNotBlank(owner)) {
//                        subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//                    }
//                }
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else{
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\""+RUANYOUTONG_MSG.get(status)+"\"}";
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
    }

    /**
     * 发起http请求get
     */
    public String implementHttpGetResult(String url,String msg,String mobile) {
        return pushGet(url,msg,mobile);
    }

    public String pushGet(String url,String msg,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{}",mobile,url);
        Request request = new Request.Builder().url(url).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},响应参数:{}",mobile,url,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{}",mobile,url,e);
            return null;
        }
    }
}
