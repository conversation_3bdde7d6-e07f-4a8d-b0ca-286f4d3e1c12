package com.eleven.cms.service.impl;

import com.eleven.cms.config.JiLinMobileConfig;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.service.IJiLinMobileService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.SignatureUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.moczul.ok2curl.CurlInterceptor;
import com.wechat.pay.java.core.http.UrlEncoder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/8 14:42
 **/
@Slf4j
@Service
public class JiLinMobileServiceImpl implements IJiLinMobileService {
    @Autowired
    private Environment environment;

    private OkHttpClient client;

    private ObjectMapper mapper;
    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    private static final MediaType mediaType = MediaType.parse("application/json");
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    @Override
    public Result<?> sendSms(String mobile, String channel) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-获取短信验证码接口-手机号:{},渠道号:{}", "吉林业务-PortCrackConfig-配置错误", mobile, channel);
            return null;
        }
        JiLinMobileConfig jiLinMobileConfig= null;
        try {
            jiLinMobileConfig = mapper.readValue(portCrackConfig.getRemark(), JiLinMobileConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode data = mapper.createObjectNode();
        ObjectNode root = mapper.createObjectNode();
        ObjectNode header = mapper.createObjectNode();
        header.put("CHANNEL_ID", jiLinMobileConfig.getChannelId());
        ObjectNode routing = mapper.createObjectNode();
        routing.put("ROUTE_KEY", jiLinMobileConfig.getRouteKey());
        routing.put("ROUTE_VALUE", mobile);
        header.putPOJO("ROUTING", routing);
        header.put("TENANT_ID", jiLinMobileConfig.getTenantId());
        ObjectNode body = mapper.createObjectNode();
        ObjectNode busiInfo = mapper.createObjectNode();
        busiInfo.put("BUSI_CODE", jiLinMobileConfig.getBusiCode());
        busiInfo.put("EFF_ECTIVE", jiLinMobileConfig.getEffEctive());
        busiInfo.put("PROD_CODE", portCrackConfig.getOfferCode());
        busiInfo.put("PHONE_NO", mobile);
        body.putPOJO("BUSI_INFO", busiInfo);
        ObjectNode oprInfo = mapper.createObjectNode();
        oprInfo.put("CHANNEL_TYPE", jiLinMobileConfig.getChannelType());
        oprInfo.put("LOGIN_NO", jiLinMobileConfig.getLoginNo());
        body.putPOJO("OPR_INFO", oprInfo);
        root.putPOJO("BODY", body);
        root.putPOJO("HEADER", header);
        data.putPOJO("ROOT", root);
        String  sign= null;
        try {
            sign = SignatureUtils.signByPrivateKey(DigestUtils.md5DigestAsHex((UrlEncoder.urlEncode(data.toString())).getBytes(StandardCharsets.UTF_8)).getBytes(),portCrackConfig.getPrivateKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody bodyData = RequestBody.create(mediaType, data.toString());
        Request request = new Request.Builder().url(portCrackConfig.getSmsCodeUrl()+"?sign="+sign).post(bodyData).build();
        log.info("{}-随机码下发-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), mobile, channel, data.toString());
        log.info("{}-随机码下发-手机号:{},渠道号:{},request:{}", portCrackConfig.getLogTag(), mobile, channel, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), mobile, channel, result);
            return null;
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), mobile, channel, e);
            return null;
        }
    }



    private Result<?> checkSms(String mobile, String channel,String code) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-获取短信验证码接口-手机号:{},渠道号:{}", "吉林业务-PortCrackConfig-配置错误", mobile, channel);
            return null;
        }
        JiLinMobileConfig jiLinMobileConfig= null;
        try {
            jiLinMobileConfig = mapper.readValue(portCrackConfig.getRemark(), JiLinMobileConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        ObjectNode data = mapper.createObjectNode();
        ObjectNode root = mapper.createObjectNode();

        ObjectNode header = mapper.createObjectNode();
        header.put("CHANNEL_ID", jiLinMobileConfig.getChannelId());

        ObjectNode routing = mapper.createObjectNode();
        routing.put("ROUTE_KEY", jiLinMobileConfig.getRouteKey());
        routing.put("ROUTE_VALUE", mobile);

        header.putPOJO("ROUTING", routing);
        header.put("TENANT_ID", jiLinMobileConfig.getTenantId());

        ObjectNode body = mapper.createObjectNode();

        ObjectNode busiInfo = mapper.createObjectNode();
        busiInfo.put("CHK_PWD", code);
        busiInfo.put("PHONE_NO", mobile);
        busiInfo.put("pwdType", jiLinMobileConfig.getPwdType());
        body.putPOJO("BUSI_INFO", busiInfo);
        ObjectNode oprInfo = mapper.createObjectNode();
        oprInfo.put("CHANNEL_TYPE", jiLinMobileConfig.getChannelType());
        oprInfo.put("CONTACT_ID", jiLinMobileConfig.getContactId());
        oprInfo.put("OP_CODE", code);
        oprInfo.put("REGION_ID", jiLinMobileConfig.getRegionId());
        oprInfo.put("LOGIN_NO", jiLinMobileConfig.getLoginNo());

        body.putPOJO("OPR_INFO", oprInfo);
        root.putPOJO("BODY", body);
        root.putPOJO("HEADER", header);
        data.putPOJO("ROOT", root);
        String  sign= null;
        try {
            sign = SignatureUtils.signByPrivateKey(DigestUtils.md5DigestAsHex((UrlEncoder.urlEncode(data.toString())).getBytes(StandardCharsets.UTF_8)).getBytes(),portCrackConfig.getPrivateKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody bodyData = RequestBody.create(mediaType, data.toString());
        Request request = new Request.Builder().url(portCrackConfig.getCheckProductSubUrl()+"?sign="+sign).post(bodyData).build();
        log.info("{}-校验验证码-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), mobile, channel, data.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-校验验证码-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), mobile, channel, result);
            return null;
        } catch (Exception e) {
            log.info("{}-校验验证码-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), mobile, channel, e);
            return null;
        }
    }

    @Override
    public Result<?> submitOrder(String mobile, String channel,String code){
        this.checkSms( mobile,channel,code);
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channel);
        if (portCrackConfig == null) {
            log.info("{}-获取短信验证码接口-手机号:{},渠道号:{}", "吉林业务-PortCrackConfig-配置错误", mobile, channel);
            return null;
        }
        JiLinMobileConfig jiLinMobileConfig= null;
        try {
            jiLinMobileConfig = mapper.readValue(portCrackConfig.getRemark(), JiLinMobileConfig.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        ObjectNode data = mapper.createObjectNode();
        ObjectNode root = mapper.createObjectNode();
        ObjectNode header = mapper.createObjectNode();
        header.put("CHANNEL_ID", jiLinMobileConfig.getChannelId());
        ObjectNode routing = mapper.createObjectNode();
        routing.put("ROUTE_KEY", jiLinMobileConfig.getRouteKey());
        routing.put("ROUTE_VALUE", mobile);
        header.putPOJO("ROUTING", routing);
        header.put("TRACE_ID", jiLinMobileConfig.getTraceId());
        ObjectNode body = mapper.createObjectNode();
        ObjectNode busiInfo = mapper.createObjectNode();
        ObjectNode goods = mapper.createObjectNode();
        goods.put("OPERATE_TYPE", "A");//必传，A：订购，D：退订
        goods.put("PRC_ID", portCrackConfig.getOfferCode());
        List<ObjectNode> attrList= Lists.newArrayList();
        ObjectNode attr = mapper.createObjectNode();
        attr.put("ATTR_ID", jiLinMobileConfig.getAttrId());
        attr.put("ATTR_NAME", jiLinMobileConfig.getAttrName());
        attr.put("ATTR_TYPE", jiLinMobileConfig.getAttrType());
        attr.put("OPERATE_TYPE", "A");//必传，A：订购，D：退订
        List<ObjectNode> attrValueList= Lists.newArrayList();
        ObjectNode attrValue = mapper.createObjectNode();
        attrValue.put("ATTR_VALUE", jiLinMobileConfig.getAttrValue());
        attrValue.put("attrValueShow", jiLinMobileConfig.getAttrValueShow());
        attrValueList.add(attrValue);
        attr.putPOJO("ATTR_VALUE_LIST", attrValueList);
        attrList.add(attr);
        goods.putPOJO("ATTR_LIST", attrList);
        List<ObjectNode> goodsList= Lists.newArrayList();
        goodsList.add(goods);
        busiInfo.putPOJO("GOODS_LIST", goodsList);
        busiInfo.put("CHN_FLAG", jiLinMobileConfig.getChnFlag());
        busiInfo.put("PHONE_NO", mobile);
        body.putPOJO("BUSI_INFO", busiInfo);
        ObjectNode oprInfo = mapper.createObjectNode();
        oprInfo.put("CHANNEL_TYPE", jiLinMobileConfig.getChannelType());
        oprInfo.put("OP_CODE",code);
        oprInfo.put("OP_NOTE", "");
        oprInfo.put("LOGIN_NO", jiLinMobileConfig.getLoginNo());
        body.putPOJO("OPR_INFO", oprInfo);
        root.putPOJO("BODY", body);
        root.putPOJO("HEADER", header);
        data.putPOJO("ROOT", root);
        String  sign= null;
        try {
            sign = SignatureUtils.signByPrivateKey(DigestUtils.md5DigestAsHex((UrlEncoder.urlEncode(data.toString())).getBytes(StandardCharsets.UTF_8)).getBytes(),portCrackConfig.getPrivateKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody bodyData = RequestBody.create(mediaType, data.toString());
        Request request = new Request.Builder().url(portCrackConfig.getSmsCodeUrl()+"?sign="+sign).post(bodyData).build();
        log.info("{}-下单接口-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), mobile, channel, data.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-下单接口-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), mobile, channel, result);
            return null;
        } catch (Exception e) {
            log.info("{}-下单接口-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), mobile, channel, e);
            return null;
        }
    }
}
