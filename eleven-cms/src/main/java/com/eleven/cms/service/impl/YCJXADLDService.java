package com.eleven.cms.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.eleven.cms.ad.YCJXADLDProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.AdPlatformUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 彦成江西移动爱豆来电业务
 *
 * <AUTHOR>
 */
@Slf4j
@Service("yCJXADLDService")
@RequiredArgsConstructor
public class YCJXADLDService {

    private final YCJXADLDProperties yCJXADLDProperties;

    /**
     * 发送短信
     *
     * @param subscribe subscribe
     * @return Result<?>
     */
    public Result<?> sendSms(Subscribe subscribe) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mobile", subscribe.getMobile());
        paramMap.put("message", "5013");
        paramMap.put("adPlatform", AdPlatformUtils.getAdPlatform(subscribe.getSubChannel()));
        paramMap.put("appName", subscribe.getReferer());
        paramMap.put("appMark", subscribe.getReferer());
        paramMap.put("launchUrl", subscribe.getReturnUrl());
        paramMap.put("userIp", subscribe.getIp());
        paramMap.put("cpparam", subscribe.getIspOrderNo());

        log.info("手机号:{}, 渠道号:{}, 彦成江西爱豆来电发送短信请求参数：{}", subscribe.getMobile(), subscribe.getChannel(), paramMap);
        String content = HttpUtil.get(yCJXADLDProperties.getSendSmsUrl(), paramMap);
        log.info("手机号:{}, 渠道号:{}, 彦成江西爱豆来电发送短信响应：{}", subscribe.getMobile(), subscribe.getChannel(), content);
        cn.hutool.json.JSON json = JSONUtil.parse(content);
        return json.getByPath("code", Integer.class) == 0? Result.ok("短信发送成功", subscribe.getId())
                : Result.error("短信发送失败：" + json.getByPath("msg", String.class), subscribe.getId());
    }

    /**
     * 提交订购
     *
     * @param subscribe subscribe
     * @return Result<?>
     */
    public Result<?> submitOrder(Subscribe subscribe) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mobile", subscribe.getMobile());
        paramMap.put("message", "5013");
        paramMap.put("code", subscribe.getSmsCode());

        log.info("手机号:{}, 渠道号:{}, 彦成江西爱豆来电提交订购请求参数：{}", subscribe.getMobile(), subscribe.getChannel(), paramMap);
        String content = HttpUtil.get(yCJXADLDProperties.getSubmitOrderUrl(), paramMap);
        log.info("手机号:{}, 渠道号:{}, 彦成江西爱豆来电提交订购响应：{}", subscribe.getMobile(), subscribe.getChannel(), content);
        cn.hutool.json.JSON json = JSONUtil.parse(content);
        return json.getByPath("code", Integer.class) == 0? Result.ok("提交订单成功", subscribe.getId())
                : Result.error("提交订单失败：" + json.getByPath("msg", String.class), subscribe.getId());
    }
}

