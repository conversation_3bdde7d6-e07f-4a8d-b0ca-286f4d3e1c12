package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.StpJiangsuYidongProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.PushSubscribeService;
import com.eleven.cms.remote.StpYidongService;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.StpJiangsuYidongResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("stpJiangsuYidongCommonService")
@Slf4j
public class StpJiangsuYidongCommonServiceImpl implements IBizCommonService {

    @Autowired
    StpYidongService stpYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    StpJiangsuYidongProperties stpJiangsuYidongProperties;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        StpJiangsuYidongResult result = stpYidongService.getSms(subscribe.getMobile(), subscribe.getChannel(), subscribe.getSource(), subscribe.getReferer(), subscribe.getIp());
        if (result.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String results=result.getData()!=null?result.getData().getCode()+"-"+result.getData().getMsg():"";
                String errorMsg="{\"code\":\""+result.getCode()+"\",\"msg\":\""+result.getMsg()+"\",\"result\":\""+results+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        String ispOrderNo = DateUtil.formatForMiguGroupApi(LocalDateTime.now()) + RandomStringUtils.randomNumeric(8);
        subscribe.setIspOrderNo(ispOrderNo);
        StpJiangsuYidongResult result = stpYidongService.smsCode(subscribe.getMobile(), subscribe.getChannel(), subscribe.getSmsCode(), ispOrderNo, subscribe.getEncryption(), subscribe.getSource(), subscribe.getIp(), subscribe.getUserAgent(), subscribe.getReferer());
        if (result.isOK()) {
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setIspOrderNo(ispOrderNo);
            upd.setOpenTime(new Date());
            upd.setResult("提交验证码成功");
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("提交验证码成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(result.getData().getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
