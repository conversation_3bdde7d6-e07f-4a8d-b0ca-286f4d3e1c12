package com.eleven.cms.service.impl;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.entity.EsOrderFailLog;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.service.IOrderFailLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/10 15:30
 **/
@Slf4j
@Service
public class OrderFailLogServiceImpl implements IOrderFailLogService {
    @Autowired
    IEsDataService esDataService;
    @Override
    public void createOrderFailLog(Subscribe subscribe,String message) {
        EsOrderFailLog esOrderFailLog = new EsOrderFailLog();
        BeanUtils.copyProperties(subscribe, esOrderFailLog);
        esOrderFailLog.setResult(message);
        esOrderFailLog.setCreateTime(new Date());
        esDataService.saveEsOrderFailLog(esOrderFailLog);
    }
}
