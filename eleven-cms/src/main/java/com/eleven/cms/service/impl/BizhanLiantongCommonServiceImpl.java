package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.BizhanLiantongService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.vo.SichuanLiantongResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service("bizhanLiantongCommonService")
public class BizhanLiantongCommonServiceImpl implements IBizCommonService {

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    BizhanLiantongService bizhanLiantongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        try {
            SichuanLiantongResult result = bizhanLiantongService.getSms(subscribe.getMobile());
            if (result.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                subscribe.setIspOrderNo(result.getResult());
                subscribeService.createSubscribeDbAndEs(subscribe);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
//                return Result.error("获取验证码失败");

                try {
                    String errorMsg="{\"status\":\""+result.getStatus()+"\",\"code\":\""+result.getCode()+"\",\"message\":\""+result.getResMsg()+"\",\"result\":\""+result.getResult()+"\"}";
                    return Result.errorSmsMsg(errorMsg);
                } catch (Exception e) {
                    log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                    return Result.msgSmsError();
                }
            }
        } catch (Exception e) {
            log.error("b站联通会员获取验证码错误:", e);
            return Result.error("系统错误，请稍后再试");

        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        try {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setBizTime(new Date());
            SichuanLiantongResult result = bizhanLiantongService.smsCode(subscribe.getMobile(), subscribe.getSmsCode(), subscribe.getIspOrderNo());
            if (result.isOK()) {
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setResult("订阅成功");
                subscribeService.updateSubscribeDbAndEs(upd);
                //信息流广告转化上报
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                } else {
                    channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
                }
                return Result.ok("订阅成功");
            } else {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result.getResMsg());
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error("订阅失败");
            }
        }catch (Exception e){
            log.error("b站联通会员提交验证码错误:", e);
            return Result.error("系统错误，请稍后再试");
        }
    }
}
