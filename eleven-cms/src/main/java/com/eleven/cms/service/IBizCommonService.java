package com.eleven.cms.service;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MiguApiService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.SpringContextUtils;

public interface IBizCommonService {
    @ValidationLimit
    default Result filerCheck(Subscribe subscribe) {
//        String mobile, String channelCode, String subChannel, String province
//        boolean isFilerCheck = SpringContextUtils.getBean(ISubscribeService.class).validationLimit(channelCode, province, subChannel);
//        if (isFilerCheck) {
        boolean isFilerCheck = SpringContextUtils.getBean(MiguApiService.class).filerCheck(subscribe.getMobile(), subscribe.getChannel());
        return isFilerCheck ? Result.ok() : Result.error("前置校验失败");
    }
    default Result getSmsCode(Subscribe subscribe) {
        return Result.ok();
    }

    Result submitSmsCode(Subscribe subscribe);

}

