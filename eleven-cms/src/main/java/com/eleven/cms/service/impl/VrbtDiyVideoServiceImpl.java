package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.FtpProperties;
import com.eleven.cms.config.VrbtDiyProperties;
import com.eleven.cms.config.YycpFtpProperties;
import com.eleven.cms.entity.VrbtDiyVideo;
import com.eleven.cms.mapper.VrbtDiyVideoMapper;
import com.eleven.cms.service.IVrbtDiyVideoService;
import com.eleven.cms.util.FtpUtil;
import com.eleven.cms.util.guizhou.Md5Utils;
import com.eleven.qycl.service.AliMediaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;

/**
 * @Description: cms_vrbt_diy_video
 * @Author: jeecg-boot
 * @Date:   2023-06-21
 * @Version: V1.0
 */
@Service
@Slf4j
public class VrbtDiyVideoServiceImpl extends ServiceImpl<VrbtDiyVideoMapper, VrbtDiyVideo> implements IVrbtDiyVideoService {

    @Autowired
    private VrbtDiyProperties vrbtDiyProperties;
    @Autowired
    private AliMediaService aliMediaService;
    @Autowired
    private FtpProperties ftpProperties;
    @Autowired
    private YycpFtpProperties yycpFtpProperties;

    @Override
    public boolean checkHead(String dIdPwd,VrbtDiyVideo vrbtDiyVideo) {
        if(dIdPwd.equals(Md5Utils.hash(vrbtDiyVideo.getSeq() + vrbtDiyProperties.getKey()))){
            return true;
        }
        return false;
    }

    @Override
    public boolean uploadToFtp(String pathName,String filePath,String fileName) {
        try{
            InputStream inputStream = aliMediaService.getObjectInputStream(filePath);
            boolean isSuccess = FtpUtil.uploadFileToFtp(pathName,
                    fileName,
                    inputStream,ftpProperties.getHostname(),
                    ftpProperties.getPort(),
                    ftpProperties.getUsername(),
                    ftpProperties.getPassword());
            return isSuccess;
        }catch (Exception e){
           log.error("上传文件到ftp出错,文件filePath:{},错误:",filePath,e);
        }
        return false;
    }

    @Override
    public boolean uploadVideoToFtp(String pathName,String filePath,String fileName) {
        try{
            InputStream inputStream = aliMediaService.getObjectInputStream(filePath);
            boolean isSuccess = FtpUtil.uploadFileToFtp(pathName,
                    fileName,
                    inputStream,yycpFtpProperties.getHostname(),
                    yycpFtpProperties.getPort(),
                    yycpFtpProperties.getUsername(),
                    yycpFtpProperties.getPassword());
            return isSuccess;
        }catch (Exception e){
            log.error("上传文件到ftp出错,文件filePath:{},错误:",filePath,e);
        }
        return false;
    }
    @Override
    public boolean uploadRemoteUrlVideoToFtp(String pathName, String url, String fileName) {
        try{
            boolean isSuccess = FtpUtil.uploadFileToFtp(pathName,
                    fileName,
                    new URL(url).openStream(),yycpFtpProperties.getHostname(),
                    yycpFtpProperties.getPort(),
                    yycpFtpProperties.getUsername(),
                    yycpFtpProperties.getPassword());
            return isSuccess;
        }catch (Exception e){
            log.error("上传文件到ftp出错,文件url:{},错误:",url,e);
        }
        return false;
    }

    @Override
    public Integer querySettingRingStatus(String id) {
   return this.baseMapper.querySettingRingStatus(id);
    }
}
