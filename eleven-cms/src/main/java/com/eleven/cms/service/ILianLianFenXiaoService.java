package com.eleven.cms.service;

import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * 联联分销充值产品接口
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/12 14:15
 **/
public interface ILianLianFenXiaoService {
    //查询产品列表
    Result<?> queryProductListByCityCode(Integer cityCode, Integer pageNum, Integer pageSize);
    //查询产品信息
    Result<?> queryProductDetailByProductId(String productId, String itemId);
    //查询产品图文详情
    Result<?> queryProductImgByProductId(String productId);
    //查询城市店铺
    Result<?> queryShopListByByCityCode(Integer cityCode, Integer pageNum, Integer pageSize);
    //查询产品分类
    Result<?> queryProductClassByIsTree(Integer isTree);
    //验证-渠道订单创建条件
    Result<?> checkCreateOrder(String mobile,String orderNo,String productId,Integer itemId,Integer settlePrice,String travelDate,String idCard,String customerName,String customerPhoneNumber,String address,Integer thirdSalePrice,String memo);
    //渠道订单创建
    Result<?> createOrder(String mobile,String validToken,String orderNo,String productId,Integer itemId,Integer settlePrice,String travelDate,String idCard,String customerName,String customerPhoneNumber,String address,Integer thirdSalePrice,String memo);
    //退款 渠道订单号和（小订单号列表）
    Result<?> refundOrder(String mobile,String orderNo, List<String> orderList);
    //刷新二维码
    Result<?> refreshCode(String mobile,String orderNo);
    //查询物流信息（小订单号）
    Result<?> queryExpress(String mobile,String orderId);
    //重发短信
    Result<?> refreshSms(String mobile,String orderNo, List<String> orderList);
    //查询订单详情
    Result<?> queryOrderDetail(String mobile,String orderNo);
}
