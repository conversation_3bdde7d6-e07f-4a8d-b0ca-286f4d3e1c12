package com.eleven.cms.service.impl;

import com.eleven.cms.config.KuaiShouProperties;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.service.IKuaiShouService;
import com.eleven.cms.service.IWechatConfigLogService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Map;

/**
 * 快手接口实现
 */
@Slf4j
@Service
public class KuaiShouServiceImpl implements IKuaiShouService {
    @Autowired
    private KuaiShouProperties kuaiShouProperties;
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    private OkHttpClient client;
    private ObjectMapper mapper;
//    private MediaType FORM;
    @Autowired
    private Environment environment;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        this.FORM = MediaType.parse(org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }

    /**
     * 快手获取OpenId
     * @param code
     * @return
     */
    @Override
    public FebsResponse getOpenId(String code,String tradeType,String businessType)throws Exception{
        WechatConfigLog payConfig=wechatConfigLogService.getKsConfig(tradeType,businessType);
        if(payConfig==null){
            log.info("快手配置查询失败:授权码:{}", code);
            throw new Exception("配置错误,请稍后再试!");
        }
        Map<String,String> data= Maps.newHashMap();
        data.put("js_code",code);
        data.put("app_id",payConfig.getAppId());
        data.put("app_secret",payConfig.getAppSecret());
        try {
            final ObjectNode objectNode = mapper.readValue(implementHttpPostResult(kuaiShouProperties.getOpenIdUrl(), data,"快手小程序获取openId"), ObjectNode.class);
            return new FebsResponse().success().data(objectNode);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();
    }

    /**
     * 快手小程序用户信息解码
     * @param sessionKey
     * @param encryptedData
     * @param iv
     * @return
     */
    @Override
    public FebsResponse decrypt(String sessionKey, String encryptedData, String iv) {
        // Base64解码数据
        byte[] aesKey = Base64.getDecoder().decode(sessionKey);
        byte[] ivBytes = Base64.getDecoder().decode(iv);
        byte[] cipherBytes = Base64.getDecoder().decode(encryptedData);
        byte[] plainBytes = decrypt(aesKey, ivBytes, cipherBytes);
        try {
            JsonNode jsonNode = mapper.readTree(new String(plainBytes, StandardCharsets.UTF_8));
            String phoneNumber = jsonNode.at("/phoneNumber").asText();
            return new FebsResponse().success().data(phoneNumber) ;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return new FebsResponse().fail();
        }
    }
    /**
     * AES解密函数. 使用 AES/CBC/PKCS5Padding 模式
     *
     * @param aesKey      密钥，长度16
     * @param iv          偏移量，长度16
     * @param cipherBytes 密文信息
     * @return 明文
     */
    private static byte[] decrypt(byte[] aesKey, byte[] iv, byte[] cipherBytes) {
        SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            return cipher.doFinal(cipherBytes);
        } catch (Exception e) {
            log.error("快手用户信息解密出错", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url, Map<String,String> data,String msg) {
        return push(url, data,msg);
    }
    public String push(String url,Map<String,String> data,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,data);
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : data.keySet()) {
            builder.add(key, data.get(key));
        }
        RequestBody formBody=builder.build();
        Request request = new Request.Builder().url(url).post(formBody).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,data,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,data,e);
            return null;
        }
    }


}

