package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.shanghaimobile.properties.ShanghaiMobileBusiness;
import com.eleven.cms.shanghaimobile.service.IShanghaiMobileService;
import com.eleven.cms.shanghaimobile.util.ShanghaiMobileConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("shanghaiYidongVrbtCommonService")
@Slf4j
public class ShanghaiYidongVrbtCommonServiceImpl implements IBizCommonService {

    @Autowired
    IShanghaiMobileService shanghaiMobileService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    private static final List<String> bizChannelList= Arrays.asList(BIZ_CHANNEL_SHYD_XSSP,BIZ_CHANNEL_SHYD_XSSP_VS,BIZ_CHANNEL_SHYD_XSMGSP,BIZ_CHANNEL_SHYD_XSNB);
    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        if(StringUtils.isEmpty(subscribe.getIsp()) || !MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())){
            return Result.msgIspRestrict();
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobile = subscribe.getMobile();
        Result<?> result = shanghaiMobileService.shangHaiMobileBusinessOrder(mobile, subscribe.getIp(), ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()), "", "");
        subscribe.setResult(result.getMessage());
        if (result.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribe.setIspOrderNo((String) result.getResult());
            subscribeService.createSubscribeDbAndEs(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+result.getCode()+"\",\"message\":\""+result.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        Result<?> result = shanghaiMobileService.shangHaiMobileBusinessOrder(mobile, subscribe.getIp(), ShanghaiMobileConstant.IS_NOT_RIGHT, ShanghaiMobileBusiness.productIdMap.get(subscribe.getChannel()), smsCode, subscribe.getIspOrderNo());
        upd.setResult(result.getMessage());
        upd.setOpenTime(new Date());
        if (result != null && result.isOK()) {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            subscribeService.updateSubscribeDbAndEs(upd);
            //加入包月延迟校验队列
            if(bizChannelList.contains(subscribe.getChannel())) {
                rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            }
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            return Result.ok("订阅成功");
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
