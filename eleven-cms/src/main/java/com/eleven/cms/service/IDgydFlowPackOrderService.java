package com.eleven.cms.service;

import com.eleven.cms.entity.DgydFlowPackOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 东莞移动流量包订单
 * @Author: jeecg-boot
 * @Date:   2023-01-11
 * @Version: V1.0
 */
public interface IDgydFlowPackOrderService extends IService<DgydFlowPackOrder> {

    Result<?> sendSms(String mobile, String productCode);

    Result<?> submitOrder(String mobile, String productCode, String code,String orderType,String id);

}
