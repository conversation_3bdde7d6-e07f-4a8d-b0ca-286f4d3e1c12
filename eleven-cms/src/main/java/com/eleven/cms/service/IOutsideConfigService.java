package com.eleven.cms.service;

import com.eleven.cms.entity.OutsideBusinessConfig;
import com.eleven.cms.entity.OutsideConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: cms_outside_config
 * @Author: jeecg-boot
 * @Date:   2023-04-26
 * @Version: V1.0
 */
public interface IOutsideConfigService extends IService<OutsideConfig> {

    OutsideConfig getOutsideConfigByOutChannel(String outChannel);

    boolean isOutsideChannel(String outChannel);

    boolean isJunboOutsideChannel(String outChannel);

    boolean isZsyOutsideChannel(String outChannel);

    /**
     * 添加一对多
     */
    public void saveMain(OutsideConfig outsideConfig, List<OutsideBusinessConfig> outsideBusinessConfigList);

    /**
     * 修改一对多
     *
     */
    public void updateMain(OutsideConfig outsideConfig,List<OutsideBusinessConfig> outsideBusinessConfigList);

    /**
     * 删除一对多
     */
    public void delMain (String id);

    /**
     * 批量删除一对多
     */
    public void delBatchMain (Collection<? extends Serializable> idList);

    /**
     *
     * @param configId
     * @param province
     * @return
     */
    OutsideBusinessConfig getOutsideBusinessConfig(String configId,String province);



}
