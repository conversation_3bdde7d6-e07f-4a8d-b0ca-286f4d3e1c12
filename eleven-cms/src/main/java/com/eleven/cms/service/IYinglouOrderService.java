package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.dto.QyclWxpayNotifyParam;
import com.eleven.cms.entity.YinglouOrder;
import org.jeecg.common.api.vo.Result;

import java.util.Map;

/**
 * @Description: 影楼订单表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
public interface IYinglouOrderService extends IService<YinglouOrder> {
    Result updateStatus(String mobile,int status);

    Map<String, String> pay(String tradeType, String channel, String subject, String openId,String userName) throws Exception;

    void modifyPayStatus(QyclWxpayNotifyParam notifyParam);

    Result<?> isPay(String orderId,String userName);

    Result<?> isSub(String userName);

    Result<?> isSms(String userName,String mobile,String channel);

    Result<?> bindOrderByMobile(String mobile,String channel,String circleId);

    Result<?> queryPayOrderList(String userName,String payTime);

    Result wechatRefund(String orderId, String refund);

    YinglouOrder queryNotRefundOrder(String refundOrderNo);

    void modifyRefundStatus(String orderId,String outRefundNo, String refundStatus);
}
