package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.Qywx;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.mapper.QywxMapper;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.service.IQywxService;
import org.springframework.beans.factory.annotation.Autowired;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description: cms_qywx_state
 * @Author: jeecg-boot
 * @Date: 2023-04-17
 * @Version: V1.0
 */
@Service
@Slf4j
public class QywxServiceImpl extends ServiceImpl<QywxMapper, Qywx> implements IQywxService {

    @Autowired
    IEsDataService esDataService;

    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    private static final String LOG_TAG = "企业微信上报工具API";

    public static final MediaType JSONTYPE
        = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    public static ObjectMapper mapper = new ObjectMapper();

    @Override
    public void saveOrUpdateData(Qywx qywx) {
        boolean flag = this.saveOrUpdate(qywx);
        if (flag) {
            esDataService.saveOrUpdateQywxAsync(qywx);
        }
    }



    /**
     * 调用广告平台微服务上报接口
     *
     * @param qywx
     * @return
     */
    @Override
    public void sendReportMqMessage(Qywx qywx) {

        Integer result = -1;

        try {
            ObjectNode objectNode = createAdReportEntityJsonNode(qywx.getId(), qywx.getSubChannel(), qywx.getPageUrl());
            rabbitMQMsgSender.sendQywxMessage(objectNode);
            result = 1;
        } catch (Exception e) {
            result = 0;
            log.info("{}-发送企业微信上报消息到队列异常:subChannel:{},pageUrl:{}", LOG_TAG, qywx.getSubChannel(), qywx.getPageUrl(), e);
        } finally {
            log.info("{}-发送企业微信上报消息到队列后更新状态:subChannel:{},pageUrl:{}", LOG_TAG, qywx.getSubChannel(), qywx.getPageUrl());
            //subscribeService.lambdaUpdate().eq(Subscribe::getId, subscribeId).set(Subscribe::getTuiaFeedbackStatus, result).update();
//            qywx.setReportStatus(result);
            this.updateById(qywx);
        }

    }

    @Override
    public void clickLinkAddContactNotify(String hkLinkId, String customerChannel) {
        Qywx qywx = lambdaQuery().eq(Qywx::getHkLinkId, hkLinkId).eq(Qywx::getCustomerChannel, customerChannel).one();
        qywx.setStatus(1);
        qywx.setCustomerAddTime(new Date());
        this.saveOrUpdateData(qywx);
        this.sendReportMqMessage(qywx);
    }

    @Override
    public void addContactNotify(String userId, String customerChannel) {
        Qywx qywx = lambdaQuery().eq(Qywx::getCustomerChannel, customerChannel).one();
        qywx.setUserId(userId);
        this.saveOrUpdateData(qywx);
    }

    private ObjectNode createAdReportEntityJsonNode(String id, String subChannel, String pageUrl) {
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("orderNo", id);
        objectNode.put("subChannel", subChannel);
        objectNode.put("adLink", pageUrl);
        return objectNode;
    }
}
