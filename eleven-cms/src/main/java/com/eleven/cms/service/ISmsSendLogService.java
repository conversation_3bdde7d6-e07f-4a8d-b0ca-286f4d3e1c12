package com.eleven.cms.service;

import com.eleven.cms.entity.SmsSendLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;

/**
 * @Description: cms_sms_send_log
 * @Author: jeecg-boot
 * @Date:   2023-09-25
 * @Version: V1.0
 */
public interface ISmsSendLogService extends IService<SmsSendLog> {

    void saveSendLog(String mobile, String msgId, String msgContent, Integer sendStatus);

    void receiveSendStateNotify(String mobile, String msgId, String state, Date time);
}
