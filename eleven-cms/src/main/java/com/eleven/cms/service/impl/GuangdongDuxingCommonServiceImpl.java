package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.*;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.FengzhushouSichuanYidongService;
import com.eleven.cms.remote.GuangdongYidongDuxingService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.FengzhushouSichuanYidongGetSmsResult;
import com.eleven.cms.vo.FengzhushouSichuanYidongSmsCodeResult;
import com.eleven.cms.vo.GuangdongYidongDuxingResult;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizCommonConstant.DIANXIN_SMS_INVALID_CACHE_SECONDS;
import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service("guangdongDuxingCommonService")
public class GuangdongDuxingCommonServiceImpl implements IBizCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    GuangdongYidongDuxingService guangdongYidongDuxingService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        GuangdongYidongDuxingResult guangdongYidongDuxingResult = guangdongYidongDuxingService.getSms(subscribe.getMobile());
        if (guangdongYidongDuxingResult.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribeService.createSubscribeDbAndEs(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+guangdongYidongDuxingResult.getCode()+"\",\"msg\":\""+guangdongYidongDuxingResult.getMsg()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        GuangdongYidongDuxingResult guangdongYidongDuxingResult = guangdongYidongDuxingService.smsCode(subscribe.getMobile(), subscribe.getSmsCode());
        if (guangdongYidongDuxingResult.isOK()) {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setOpenTime(new Date());
            upd.setResult(guangdongYidongDuxingResult.getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            //加入延迟队列校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("订阅成功");
        } else {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setOpenTime(new Date());
            upd.setResult(guangdongYidongDuxingResult.getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error(guangdongYidongDuxingResult.getMsg());
        }
    }
}
