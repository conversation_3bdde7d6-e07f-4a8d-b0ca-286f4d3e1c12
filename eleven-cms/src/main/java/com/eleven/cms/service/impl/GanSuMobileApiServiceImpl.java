package com.eleven.cms.service.impl;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.JunboSub;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.service.IGanSuMobileApiService;
import com.eleven.cms.service.IJunboSubService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Slf4j
@Service
public class GanSuMobileApiServiceImpl implements IGanSuMobileApiService {
//    public static final String LOG_TAG = "鸿盛甘肃移动业务api";
    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    @Autowired
    private Environment environment;

    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final String DELIMITER_AMP = "&";
    private static final MediaType mediaType = MediaType.parse("application/json");
    @Autowired
    IJunboSubService junboSubService;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 发送验证码
     * @param channelCode
     * @param phone
     * @param ip
     * @return
     * @throws Exception
     */
    @Override
    public YouranGansuMobileResult getSms(String channelCode, String phone, String ip) throws Exception {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-发送验证码-手机号:{},渠道号:{}", "甘肃移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return YouranGansuMobileResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(portCrackConfig.getGetSmsUrl(), ip,portCrackConfig.getAppId()));
        dataMap.put("body", getSmsBody(phone,portCrackConfig.getBusiType(),portCrackConfig.getOfferCode()));
        generateSign(dataMap,portCrackConfig.getSecretKey());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-随机码下发-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-随机码下发-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, YouranGansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-随机码下发-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return YouranGansuMobileResult.fail();

        }
    }

    /**
     * 提交验证码
     * @param channelCode
     * @param phone
     * @param smsCode
     * @param identifyingKey
     * @param ip
     * @return
     * @throws Exception
     */
    @Override
    public YouranGansuMobileResult smsCode(String channelCode, String phone, String smsCode, String identifyingKey, String ip) throws Exception {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-提交验证码-手机号:{},渠道号:{}", "甘肃移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return YouranGansuMobileResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(portCrackConfig.getSmsCodeUrl(), ip,portCrackConfig.getAppId()));
        dataMap.put("body", smsCodeBody(phone, smsCode, identifyingKey,portCrackConfig.getOfferCode()));
        generateSign(dataMap,portCrackConfig.getSecretKey());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, smsCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, smsCode, result);
            return mapper.readValue(result, YouranGansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},渠道号:{},短信验证码:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, smsCode, e);
            return YouranGansuMobileResult.fail();

        }
    }

    /**
     * 查询订购状态
     * @param channelCode
     * @param phone
     * @param ip
     * @return
     * @throws Exception
     */
    @Override
    public GansuMobileServiceResult queryOrderService(String channelCode, String phone, String ip) throws Exception {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-查询订购状态-手机号:{},渠道号:{}", "甘肃移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return GansuMobileServiceResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(portCrackConfig.getQueryServiceUrl(), ip,portCrackConfig.getAppId()));
        dataMap.put("body", orderServiceBody(phone, portCrackConfig.getOfferCode()));
        generateSign(dataMap,portCrackConfig.getSecretKey());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-查询订购状态-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询订购状态-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, GansuMobileServiceResult.class);
        } catch (Exception e) {
            log.info("{}-查询订购状态-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return GansuMobileServiceResult.fail();

        }
    }


    /**
     * 查询用户余额
     * @param channelCode
     * @param phone
     * @param smsCode
     * @param identifyingKey
     * @param ip
     * @return
     * @throws Exception
     */
    @Override
    public YouranGansuMobileResult queryUserFee(String channelCode, String phone, String smsCode, String identifyingKey, String ip) throws Exception {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-查询用户余额-手机号:{},渠道号:{}", "甘肃移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return YouranGansuMobileResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(portCrackConfig.getQueryServiceUrl(), ip,portCrackConfig.getAppId()));
        dataMap.put("body", userFeeBody(phone,smsCode,identifyingKey));
        generateSign(dataMap,portCrackConfig.getSecretKey());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-查询用户余额-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询用户余额-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, YouranGansuMobileResult.class);
        } catch (Exception e) {
            log.info("{}-查询用户余额-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return YouranGansuMobileResult.fail();

        }
    }


    /**
     * 查询用户订单
     * @param channelCode
     * @param phone
     * @param orderId
     * @param ip
     * @return
     * @throws Exception
     */
    @Override
    public GansuMobileOrderResult queryOrder(String channelCode, String phone, String orderId, String ip) throws Exception {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-查询用户订单-手机号:{},渠道号:{}", "甘肃移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return GansuMobileOrderResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(portCrackConfig.getQueryServiceUrl(), ip,portCrackConfig.getAppId()));
        dataMap.put("body", orderBody(phone,portCrackConfig.getOfferCode(),orderId));
        generateSign(dataMap,portCrackConfig.getSecretKey());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-查询用户订单-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-查询用户订单-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, GansuMobileOrderResult.class);
        } catch (Exception e) {
            log.info("{}-查询用户订单-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return GansuMobileOrderResult.fail();

        }
    }


    /**
     * 校验产品能否办理
     * @param channelCode
     * @param phone
     * @param ip
     * @return
     * @throws Exception
     */
    @Override
    public GansuMobileCheckResult checkUser(String channelCode, String phone, String ip) throws Exception {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-校验产品能否办理-手机号:{},渠道号:{}", "甘肃移动业务-PortCrackConfig-配置错误", phone, channelCode);
            return GansuMobileCheckResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("header", getHeader(portCrackConfig.getQueryServiceUrl(), ip,portCrackConfig.getAppId()));
        dataMap.put("body", checkUserBody(phone,portCrackConfig.getOfferCode()));
        generateSign(dataMap,portCrackConfig.getSecretKey());
        RequestBody body = RequestBody.create(mediaType, mapper.writeValueAsString(dataMap));
        Request request = new Request.Builder().url(url)
                .post(body)
                .tag(dataMap)
                .build();
        log.info("{}-校验产品能否办理-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-校验产品能否办理-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, GansuMobileCheckResult.class);
        } catch (Exception e) {
            log.info("{}-校验产品能否办理-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return GansuMobileCheckResult.fail();

        }
    }

    /**
     * 订购消息结果记录
     *
     * @param jsonNode
     */
    @Override
    public void subMQMsg(JsonNode jsonNode) {
        String mobile = jsonNode.at("/mobile").asText("");
        String ispOrderNo = jsonNode.at("/ispOrderNo").asText("");
        String channel = jsonNode.at("/channel").asText("");
        String ip = jsonNode.at("/ip").asText("");
        try {
            GansuMobileOrderResult result=this.queryOrder(channel,mobile,ispOrderNo,ip);
            JunboSub junboSub = mapper.treeToValue(jsonNode, JunboSub.class);
            junboSub.setId(null);
            junboSub.setStatus(result!=null && result.isOK() && result.getData()!=null && "0".equals(result.getData().getOrderStatus()) ?SUBSCRIBE_STATUS_SUCCESS:SUBSCRIBE_STATUS_FAIL);
            junboSubService.save(junboSub);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private Map<String, Object> checkUserBody(String mobile, String offerCode) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("offerCode", offerCode);
        return bodyMap;
    }



    private Map<String, Object> orderBody(String mobile, String offerCode, String orderId) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("offerCode", offerCode);
        bodyMap.put("orderId", orderId);
        return bodyMap;
    }







    private Map<String, Object> userFeeBody(String mobile, String smsCode, String identifyingKey) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("identifyingKey", identifyingKey);
        bodyMap.put("identifyingCode", smsCode);
        return bodyMap;
    }

    private Map<String, Object> getHeader(String cmd, String userIp,String appId) {
        Map<String, Object> headerMap = new TreeMap<>();
        headerMap.put("appId", appId);
        headerMap.put("cmd", cmd);
        headerMap.put("reqTime", DateUtil.formatFullTime(LocalDateTime.now()));
        headerMap.put("userIp", userIp);
        return headerMap;
    }

    private Map<String, Object> getSmsBody(String mobile,String busiType,String offerCode) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("busiType", busiType);
        bodyMap.put("offerCode", offerCode);
        return bodyMap;
    }

    private Map<String, Object> smsCodeBody(String mobile, String smsCode, String identifyingKey,String offerCode) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("offerCode", offerCode);
        bodyMap.put("operCode", "1");
        bodyMap.put("identifyingKey", identifyingKey);
        bodyMap.put("identifyingCode", smsCode);
        return bodyMap;
    }

    private Map<String, Object> orderServiceBody(String mobile,String offerCode) {
        Map<String, Object> bodyMap = new TreeMap<>();
        bodyMap.put("phoneNum", mobile);
        bodyMap.put("offerCode", offerCode);
        return bodyMap;
    }


    private void generateSign(Map<String, Object> dataMap,String secretKey) throws Exception {
        Map<String, Object> headerMap = (TreeMap<String, Object>) dataMap.get("header");
        String sign = SecureUtil.hmacSha256(DigestUtils.md5(secretKey)).digestHex(MD5.create().digest(mapper.writeValueAsString(dataMap), StandardCharsets.UTF_8));
        headerMap.put("sign", sign);
    }
}
