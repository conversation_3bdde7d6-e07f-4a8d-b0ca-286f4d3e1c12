package com.eleven.cms.service.impl;

import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.service.IJunboSubService;
import com.eleven.cms.service.IPortCrackConfigService;
import com.eleven.cms.service.IYiZunCpaApiService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.MD5Util;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.YiZunCpaGetSmsResult;
import com.eleven.cms.vo.YiZunCpaLoginResult;
import com.eleven.cms.vo.YiZunCpaSmsCodeResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;
import java.util.TreeMap;

/**
 * @author: yang tao
 * @create: 2025-01-14 15:46
 */
@Slf4j
@Service
public class YiZunCpaApiServiceImpl implements IYiZunCpaApiService {
    @Autowired
    private IPortCrackConfigService portCrackConfigService;
    @Autowired
    private Environment environment;

    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final String DELIMITER_AMP = "&";
    private static final MediaType mediaType = MediaType.parse("application/json");
    @Autowired
    IJunboSubService junboSubService;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 发送验证码
     * @param channelCode
     * @param phone
     * @param ip
     * @param userAgent
     * @param appPackage
     * @param sourceUrl
     * @return
     */
    @Override
    public YiZunCpaGetSmsResult getSms(String channelCode, String phone, String ip,String userAgent,String appPackage,String sourceUrl) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-获取验证码-手机号:{},渠道号:{}", "易尊业务-PortCrackConfig-配置错误", phone, channelCode);
            return YiZunCpaGetSmsResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("appKey", portCrackConfig.getAppId());
        dataMap.put("method",portCrackConfig.getGetSmsUrl());
        dataMap.put("messageFormat","json");
        dataMap.put("v","1.0");
        dataMap.put("locale","zh_CN");
        dataMap.put("goodsId",portCrackConfig.getOfferCode());
        dataMap.put("mobile",phone);
        dataMap.put("ip",ip);
        dataMap.put("userAgent",StringUtils.isNotBlank(userAgent)?userAgent:"");
        dataMap.put("appPackage",StringUtils.isNotBlank(portCrackConfig.getAppName())?portCrackConfig.getAppName():StringUtils.isNotBlank(appPackage)?appPackage: BizConstant.APP_NAME);
        dataMap.put("promoteChannelName",StringUtils.isNotBlank(portCrackConfig.getRemark())?portCrackConfig.getRemark():"64");
        dataMap.put("sourceUrl", StringUtils.isNotBlank(portCrackConfig.getSourceUrl())?portCrackConfig.getSourceUrl():sourceUrl);
        dataMap.put("sign",MD5Util.getYiZunSignSHA(dataMap,portCrackConfig.getSecretKey()));

        HttpUrl.Builder builder = HttpUrl.parse(url).newBuilder();
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString();
            builder.addQueryParameter(key, value);
            // 处理key和value
        }
        HttpUrl httpUrl = builder.build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-验证码下发-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-验证码下发-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, YiZunCpaGetSmsResult.class);
        } catch (Exception e) {
            log.info("{}-验证码下发-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return YiZunCpaGetSmsResult.fail();
        }
    }


    /**
     * 登录
     * @param channelCode
     * @param phone
     * @param smsCode
     * @param bizToken
     * @return
     */
    private YiZunCpaLoginResult login(String channelCode, String phone, String smsCode,String bizToken) {
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-登录-手机号:{},渠道号:{}", "易尊业务-PortCrackConfig-配置错误", phone, channelCode);
            return YiZunCpaLoginResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("appKey", portCrackConfig.getAppId());
        dataMap.put("method",portCrackConfig.getCheckProductSubUrl());
        dataMap.put("messageFormat","json");
        dataMap.put("v","1.0");
        dataMap.put("locale","zh_CN");
        dataMap.put("goodsId",portCrackConfig.getOfferCode());
        dataMap.put("smsCode",smsCode);
        dataMap.put("bizToken",bizToken);
        dataMap.put("sign",MD5Util.getYiZunSignSHA(dataMap,portCrackConfig.getSecretKey()));
        HttpUrl.Builder builder = HttpUrl.parse(url).newBuilder();
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString();
            builder.addQueryParameter(key, value);
            // 处理key和value
        }
        HttpUrl httpUrl = builder.build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-验证码下发-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-登录-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, YiZunCpaLoginResult.class);
        } catch (Exception e) {
            log.info("{}-登录-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return YiZunCpaLoginResult.fail();
        }
    }

    /**
     * 提交验证码
     * @param channelCode
     * @param phone
     * @param smsCode
     * @param channelSeqId
     * @param bizToken
     * @return
     */
    @Override
    public YiZunCpaSmsCodeResult smsCode(String channelCode, String phone, String smsCode, String channelSeqId, String bizToken){
        YiZunCpaLoginResult loginResult=this.login(channelCode,phone,smsCode,bizToken);
        if(!loginResult.isOK()){
            return YiZunCpaSmsCodeResult.fail(loginResult.getMessage());
        }
        PortCrackConfig portCrackConfig=portCrackConfigService.getCrackConfigByChannel(channelCode);
        if (portCrackConfig == null) {
            log.info("{}-提交验证码-手机号:{},渠道号:{}", "易尊业务-PortCrackConfig-配置错误", phone, channelCode);
            return YiZunCpaSmsCodeResult.fail();
        }
        String url = portCrackConfig.getRequestUrl();
        Map<String, Object> dataMap = new TreeMap<>();
        dataMap.put("appKey", portCrackConfig.getAppId());
        dataMap.put("method",portCrackConfig.getSmsCodeUrl());
        dataMap.put("messageFormat","json");
        dataMap.put("v","1.0");
        dataMap.put("locale","zh_CN");

        dataMap.put("goodsId",portCrackConfig.getOfferCode());
        dataMap.put("channelSeqId",channelSeqId);
        dataMap.put("bizToken",bizToken);
        dataMap.put("promoteChannelName",StringUtils.isNotBlank(portCrackConfig.getRemark())?portCrackConfig.getRemark():"64");
        dataMap.put("sign",MD5Util.getYiZunSignSHA(dataMap,portCrackConfig.getSecretKey()));
        HttpUrl.Builder builder = HttpUrl.parse(url).newBuilder();
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString();
            builder.addQueryParameter(key, value);
            // 处理key和value
        }
        HttpUrl httpUrl = builder.build();
        Request request = new Request.Builder().url(httpUrl).build();
        log.info("{}-提交验证码-手机号:{},渠道号:{},请求:{}", portCrackConfig.getLogTag(), phone, channelCode, request.toString());
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String result = response.body().string();
            log.info("{}-提交验证码-手机号:{},渠道号:{},返回结果:{}", portCrackConfig.getLogTag(), phone, channelCode, result);
            return mapper.readValue(result, YiZunCpaSmsCodeResult.class);
        } catch (Exception e) {
            log.info("{}-提交验证码-手机号:{},渠道号:{},异常:", portCrackConfig.getLogTag(), phone, channelCode, e);
            return YiZunCpaSmsCodeResult.fail();
        }
    }
}
