package com.eleven.cms.service;


import com.eleven.cms.dto.WechatPayDecodeNotifyParam;
import com.eleven.cms.dto.WxpayNotifyParam;
import com.eleven.cms.entity.WechatCouponConfig;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2020/7/14 15:34
 * Desc: 微信支付
 */
public interface IWxpayService {
    Map<String, String> pay(String outTradeNo, String totalAmount, String subject) throws Exception;

    WxpayNotifyParam payResultNotify(String notifyXml) throws Exception;

    WechatPayDecodeNotifyParam jsApiPayResultNotify(HttpServletRequest request)throws Exception;

//    boolean weiXinActivateCode();

//    boolean sendWeiXinCode(String openId, String outRefundNo);

    void wechatCancelCodeNotify(String mchId,String requestBody);

    Result<?> weiXinActivateCode(WechatCouponConfig wechatCouponConfig);

    boolean stocksIsValid(WechatCouponConfig wechatCouponConfig);

    boolean sendWeiXinCode(String openId, String outRefundNo,WechatCouponConfig wechatCouponConfig);
}
