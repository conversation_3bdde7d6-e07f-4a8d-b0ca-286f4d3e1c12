package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.VisitLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * @Description: 页面访问日志
 * @Author: jeecg-boot
 * @Date:   2020-10-26
 * @Version: V1.0
 */
public interface IVisitLogService extends IService<VisitLog> {

    IPage<VisitLog> pages(Page<VisitLog> page, QueryWrapper<VisitLog> queryWrapper, Map<String, String[]> parameterMap);
}
