package com.eleven.cms.service;

import com.eleven.cms.entity.Alipay;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WoReadOrder;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: cms_wo_read_order
 * @Author: jeecg-boot
 * @Date:   2023-05-29
 * @Version: V1.0
 */
public interface IWoReadOrderService extends IService<WoReadOrder> {

    void saveOrder(Subscribe subscribe,String contractCode,Integer payType,String company);
    void saveOutSideOrder(Subscribe subscribe,String contractCode,Integer payType);
}
