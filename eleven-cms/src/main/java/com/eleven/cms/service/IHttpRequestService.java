package com.eleven.cms.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import okhttp3.RequestBody;

public interface IHttpRequestService {
    /**
     * 发起http请求
     */
    String implementHttpGetRequest(String url, ObjectNode dataNode,String msg);

    String implementHttpGetRequest(String url,String msg,String mobile);

    String postRequest(String url, RequestBody body, String appCode, String apiCode, String transactionId, String aedkId, String signValue, String publiceKey,String msg,String mobile);

}
