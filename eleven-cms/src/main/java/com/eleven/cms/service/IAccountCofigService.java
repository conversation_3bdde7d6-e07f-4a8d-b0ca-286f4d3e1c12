package com.eleven.cms.service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.AccountCofig;

import java.util.List;

/**
 * 账号配置表 Service接口
 *
 * <AUTHOR>
 * @date 2021-06-02 16:57:09
 */
public interface IAccountCofigService extends IService<AccountCofig> {
    /**
     * 查询（分页）
     *
     * @param
     * @param
     * @return IPage<AccountCofig>
     */
    IPage<AccountCofig> findByPage(Page<AccountCofig> page, QueryWrapper<AccountCofig> queryWrapper);

    /**
     * 查询（所有）
     *
     * @param accountCofig accountCofig
     * @return List<AccountCofig>
     */
    List<AccountCofig> findAccountCofigs(AccountCofig accountCofig);

    /**
     * 新增
     *
     * @param accountCofig accountCofig
     */
    void createAccountCofig(AccountCofig accountCofig);

    /**
     * 修改
     *
     * @param accountCofig accountCofig
     */
    void updateAccountCofig(AccountCofig accountCofig);

    /**
     * 删除
     *
     * @param accountCofig accountCofig
     */
    void deleteAccountCofig(AccountCofig accountCofig);

    List<AccountCofig> findByAdPlatformId(AccountCofig accountCofig);
}
