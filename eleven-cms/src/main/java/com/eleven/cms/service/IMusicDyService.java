package com.eleven.cms.service;

import com.eleven.cms.entity.MusicDy;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: 订阅包歌曲
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
public interface IMusicDyService extends IService<MusicDy> {

    Result<?> importExcel(HttpServletRequest request, HttpServletResponse response, Class<MusicDy> clazz);

    Result importFromExcelFile(String excelFilePath);

    void fillProductInfo(MusicDy musicDy);

    void syncMusic();

    List<MusicDy> crawlMusic(int offset);

    void updateProductById(String id);

    void updateProductByIdList(List<String> asList);
}
