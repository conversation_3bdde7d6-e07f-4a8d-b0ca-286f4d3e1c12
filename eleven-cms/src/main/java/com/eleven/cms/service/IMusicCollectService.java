package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.MusicCollect;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MusicVo;

/**
 * @Description: 歌曲收藏
 * @Author: jeecg-boot
 * @Date:   2023-11-21
 * @Version: V1.0
 */
public interface IMusicCollectService extends IService<MusicCollect> {

    FebsResponse musicCollect(String copyrightId, String mobile);

    FebsResponse musicIsCollect(String copyrightId, String mobile);

    FebsResponse queryMusicCollect(String mobile, Page<MusicVo> page);
}
