package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * @Description: 俊博直充记录
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
public interface IJunboChargeLogService extends IService<JunboChargeLog> {


    List<JunboChargeLog> findByAccountCurrentDay(String mobile);

    JunboChargeLog createImmediateRechargeLog(String msisdn,String account, String serviceId, Rights rights, String packName);

    JunboChargeLog createScheduleRechargeLog(String msisdn,String account, String serviceId, Rights rights, Date scheduledTime,String packName);

    JunboChargeLog createManualRechargeLog(String msisdn,String account, String serviceId, Rights rights,String packName);

    JunboChargeLog buildJunboChargeLog(String msisdn,String account, String serviceId, Rights rights, Integer status,
                                       Date scheduledTime, String remark,String packName);

    List<JunboChargeLog> selectMain(String phone,String rightsMonth,String sourse);

    /**
     * 接收骏伯回调通知,根据通知更新直充记录
     * @param junboResult
     */
    void receiveRechargeNotify(JunboResult junboResult);

    Result<?> getReceiveState(JunboChargeLog junboChargeLog, JunboResult junboResult);

    Result<?> getReceiveState(JunboChargeLog junboChargeLog, JunboRespon junboRespon);

    List<JunboChargeLog> findTimeoutSchedule();

    void rechargeForSchedule(JunboChargeLog junboChargeLog);

    void manualRecharge(JunboChargeLog junboChargeLog);

    void rechargeImmediate(JunboChargeLog junboChargeLog);

    Boolean isGetVRBTCLZXRights(String msisdn, String serviceId);

    List<JunboChargeLog> findByMobileAndPackNameAndMonth(String mobile,String packName);

    List<JunboChargeLog> findByMobileAndPackNameAndDate(String mobile,String packName);

    List<JunboChargeLog> findByMobileAndPackNameAndDate(String mobile,String packName,Long days);

    List<JunboChargeLog> findByMobileAndPackNameListAndMonth(String mobile,List<String> packNameList);

    List<JunboChargeLog> findByMobileAndPackNameListAndDate(String mobile,List<String> packNameList);

    List<JunboChargeLog> findByMobileAndPackNameListAndMonth(String mobile,List<String> packNameList,Long days);

    FebsResponse couponCodeCharge(JunboChargeLog junboChargeLog, String mobile);

    void updateJunboChargeBJHYStatus(JunboChargeLog junboChargeLog, RemoteResult res);
    void updateJunboChargeKGStatus(JunboChargeLog junboChargeLog, KugouOrderResult res);
    void updateJunboChargeMGSPCLStatus(JunboChargeLog junboChargeLog, RemoteResult res);
    void updateJunboChargeWXDJQStatus(JunboChargeLog junboChargeLog, boolean res);

    void updateJunboChargeKugouStatus(JunboChargeLog junboChargeLog, KugouRecoverOrderResult res);


    List<JunboChargeLog> findByServiceIdAndMobileAndMonth(String serviceId,String mobile);

    List<JunboChargeLog> findByServiceIdAndMobileAndDate(String serviceId,String mobile);

    void recoverKugouOrder(Subscribe subscribe);

    List<BusinessChargeDto> pageBusinessList(BusinessChargeDto dto);

    List<RightsDto> queryRightsList(RightsDto dto);

    void updateKuGouStock();

    void kuGouRightsSend();

    JunboChargeLog alipayRightsRecharge(String msisdn,String account, String serviceId, Rights rights, Integer status, String remark,String packName,String orderId);

    List<RightsDto> queryServiceList();

//    List<RightsDto> queryServiceListVrbt();


//    List<RightsDto> getRightsListVrbt(RightsDto dto);

    FebsResponse notifyHeTuRechargeResult(HeTuResult heTuResult);



    MiGuHuYuNotifyResp miguPropRecharge(MiGuHuYuResult miGuHuYuResult) throws Exception;



    List <WoReadJunboChargeLogDto> woReadQueryChargeList(String mobile, String date);

    JunboChargeLog aliPayVrbtChargeLog(String msisdn,String account,String orderId,String junboOrderId, String serviceId,String couponId, String couponName,Integer couponPrice,String rightsMonth, Integer status,Integer rechargeState, Date scheduledTime, String remark,String packName,Date payTime,String companyOwnerName);

    /**
     * 接收华逸回调通知,根据通知更新直充记录
     * @param huaYiResult
     */
    void receiveRechargeNotify(HuaYiResult huaYiResult);


    JunboChargeLog taskRechargeForSchedule(JunboChargeLog junboChargeLog);


    List<JunboChargeLog> queryRechargeList(String mobile);

    void newRechargeForSchedule(JunboChargeLog junboChargeLog);

    List<JunboChargeLog> findByOrderIdAndPackNameList(String orderId,List<String> packNameList);

    List<JunboChargeLog> findByOrderIdAndPackName(String orderId,String packName);

    JunboChargeLog shopOrderRightsRecharge(String msisdn,String account, String serviceId, Rights rights, Date scheduledTime, String packName,String orderId,String loginMobile);

    MiGuHuYuNotifyResp miGuHuYuSendCoupon(MiGuHuYuResult miGuHuYuResult, HttpServletRequest request, String gameName) throws Exception;


    FebsResponse gamePropNotify(GamePropResult gamePropResult);

    List<JunboChargeLog> findByMobileAndPackNameAndDate(String mobile,List<String> serviceIdIdList);


    /**
     * 接收骏伯回调通知,根据通知更新直充记录
     * @param junboResult
     */
    void receiveRechargeNotify(JunboNotifyResult junboResult);
}
