package com.eleven.cms.service;

import com.eleven.cms.entity.Music;
import com.eleven.cms.entity.MusicFavourite;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.ColumnDetail;
import com.eleven.cms.vo.MusicFavouriteVo;

import java.util.List;

/**
 * @Description: cms_music_favourite
 * @Author: jeecg-boot
 * @Date:   2022-03-01
 * @Version: V1.0
 */
public interface IMusicFavouriteService extends IService<MusicFavourite> {

    List<ColumnDetail> matching(String mobile);

    List<MusicFavouriteVo> findByMobile(String mobile);
}
