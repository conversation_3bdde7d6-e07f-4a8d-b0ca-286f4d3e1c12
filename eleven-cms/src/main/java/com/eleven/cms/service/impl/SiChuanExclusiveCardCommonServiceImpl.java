package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * 四川个人名片
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 15:30
 **/
@Service("siChuanExclusiveCardCommonService")
@Slf4j
public class SiChuanExclusiveCardCommonServiceImpl implements IBizCommonService {
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    ISiChuanExclusiveCardService siChuanExclusiveCardService;
    @Autowired
    RedisUtil redisUtil;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        Result<?> resultDetails = siChuanExclusiveCardService.queryOrderInfoDetails(subscribe.getMobile());
        if (resultDetails.isOK()) {
            HttpUrl httpUrl=HttpUrl.parse(resultDetails.getResult().toString().replace("/#",""));
            String userName=httpUrl.queryParameter("phone");
            String channelNumber=httpUrl.queryParameter("channel");
            subscribe.setExtra(channelNumber);
            subscribe.setIspOrderNo(userName);
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            Result<?>  resultSendSms=siChuanExclusiveCardService.sendSms(subscribe.getMobile(),userName,channelNumber);
            if(resultSendSms.isOK()){
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.ok("验证码已发送", subscribe.getId());
            }
            return resultSendSms;
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+resultDetails.getCode()+"\",\"message\":\""+resultDetails.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        Result<?> result=siChuanExclusiveCardService.submitSms(subscribe.getMobile(),subscribe.getIspOrderNo(), smsCode,subscribe.getExtra());
        upd.setRemark(result.getResult().toString());
        if (result.isOK()) {
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setOpenTime(new Date());
            upd.setResult("提交验证码成功");
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("提交验证码成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(result.getMessage());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
