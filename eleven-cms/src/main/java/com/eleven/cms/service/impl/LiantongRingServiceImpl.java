package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.LiantongRing;
import com.eleven.cms.mapper.LiantongRingMapper;
import com.eleven.cms.queue.LiantongUploadRingMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.LiantongVrbtService;
import com.eleven.cms.service.ILiantongRingService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.LiantongCompanyResp;
import com.eleven.cms.vo.LiantongResp;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

/**
 * @Description: 联通彩铃diy
 * @Author: jeecg-boot
 * @Date:   2023-08-22
 * @Version: V1.0
 */
@Slf4j
@Service
public class LiantongRingServiceImpl extends ServiceImpl<LiantongRingMapper, LiantongRing> implements ILiantongRingService {
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private LiantongVrbtService liantongVrbtService;
    @Override
    public FebsResponse liantongUploadRingQueue(String mobile, String videoUrl,String videoImg) {
        LiantongRing liantongRing=new LiantongRing();
        liantongRing.setMobile(mobile);
        liantongRing.setVideoUrl(videoUrl);
        liantongRing.setVideoImg(videoImg);
        this.save(liantongRing);
        rabbitMQMsgSender.liantongUploadRingQueueMessage(LiantongUploadRingMessage.builder().id(liantongRing.getId()).build());
        return new FebsResponse().success();
    }

    @Override
    public void uploadRing(String id) {
        LiantongRing liantongRing=this.lambdaQuery().eq(LiantongRing::getId,id).one();
        if(liantongRing==null){
            log.info("联通视频彩铃上传消息-铃音查询失败-id:{}",id);
            return;
        }
        try {
            LiantongCompanyResp nextIdResp=liantongVrbtService.nextId(liantongRing.getMobile(), BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            String contentId=nextIdResp==null || nextIdResp.getResult()==null?"":nextIdResp.getResult().getContentId();
            if(StringUtils.isBlank(contentId)){
                String contentResult="创建内容ID结果=>{\"code\":\""+nextIdResp.getCode()+"\",\"msg\":\""+nextIdResp.getMsg()+"\",\"detail\":\""+nextIdResp.getDetail()+"\"}";
                this.lambdaUpdate().eq(LiantongRing::getId,id).set(LiantongRing::getContentResult, contentResult).set(LiantongRing::getRingStatus,"07").update();
                log.info("联通视频彩铃上传消息-创建内容ID失败-id:{},手机号:{}",id,liantongRing.getMobile());
                return;
            }
            HttpURLConnection httpUrlMp4 = (HttpURLConnection) new URL(liantongRing.getVideoUrl()).openConnection();
            httpUrlMp4.connect();
            InputStream ringMp4FileIs=httpUrlMp4.getInputStream();



            HttpURLConnection httpUrlJpg = (HttpURLConnection) new URL(liantongRing.getVideoImg()).openConnection();
            httpUrlJpg.connect();
            InputStream coverJpgFileIs=httpUrlJpg.getInputStream();
            log.info("联通视频彩铃上传消息-铃音上传-id:{},手机号:{},内容ID:{}",id,liantongRing.getMobile(),contentId);
            LiantongCompanyResp uploadResp=liantongVrbtService.ringUpload(contentId,liantongRing.getMobile(),ringMp4FileIs,coverJpgFileIs,BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            httpUrlJpg.disconnect();
            httpUrlMp4.disconnect();
            String ringResult="铃音上传结果=>{\"code\":\""+uploadResp.getCode()+"\",\"msg\":\""+uploadResp.getMsg()+"\",\"detail\":\""+uploadResp.getDetail()+"\"}";
            String ringStatus="007".equals(uploadResp.getCode())?"02":"01";
            this.lambdaUpdate().eq(LiantongRing::getId,id).set(LiantongRing::getContentId, contentId).set(LiantongRing::getRingResult, ringResult).set(LiantongRing::getRingStatus,ringStatus).update();
            if(!"007".equals(uploadResp.getCode())){
                log.info("联通视频彩铃上传消息-铃音上传失败-id:{},手机号:{}",id,liantongRing.getMobile());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("联通视频彩铃上传消息异常-id:{},手机号:{}",id,liantongRing.getMobile(),e);
        }
        return;
    }

    @Override
    public void vrbtNotify(JsonNode jsonNode) {
        String contentid = jsonNode.at("/notice/data/contentid").asText();
        String code = jsonNode.at("/notice/code").asText();
        String msg = jsonNode.at("/notice/msg").asText();
        String detail = jsonNode.at("/notice/detail").asText();

        LiantongRing liantongRing=this.lambdaQuery().eq(LiantongRing::getContentId,contentid).orderByDesc(LiantongRing::getCreateTime).last("limit 1").one();
        if(liantongRing==null){
            log.info("联通视频彩铃内容ID为空-id:{}",contentid);
            return;
        }
        String remark="铃音上传通知结果=>{\"code\":\""+code+"\",\"msg\":\""+msg+"\",\"detail\":\""+detail+"\"}";
        if("000".equals(code)){
            //设置状态审核通过
            this.lambdaUpdate().eq(LiantongRing::getMobile,liantongRing.getMobile()).eq(LiantongRing::getRingStatus,"05").set(LiantongRing::getRingStatus,"04").update();

            //审核通过重新设置铃音
            LiantongResp liantongResp=liantongVrbtService.settingRingOnePointMon(liantongRing.getMobile(),contentid, BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
            if(liantongResp.isOK()){

                this.lambdaUpdate().eq(LiantongRing::getContentId,contentid).set(LiantongRing::getRingId,contentid).set(LiantongRing::getRemark,remark).set(LiantongRing::getRingStatus,"05").update();
                return;
            }
            this.lambdaUpdate().eq(LiantongRing::getContentId,contentid).set(LiantongRing::getRingId,contentid).set(LiantongRing::getRemark,remark).set(LiantongRing::getRingStatus,"06").update();
            return;

        }
        this.lambdaUpdate().eq(LiantongRing::getContentId,contentid).set(LiantongRing::getRingId,contentid).set(LiantongRing::getRemark,remark).update();
    }

    @Override
    public FebsResponse queryLianTongRingList(String mobile) {
        List<LiantongRing> liantongRingList=this.lambdaQuery().eq(LiantongRing::getMobile,mobile).eq(LiantongRing::getStatus,1).select(LiantongRing::getRingId,LiantongRing::getRingStatus,LiantongRing::getVideoUrl,LiantongRing::getCreateTime).orderByDesc(LiantongRing::getCreateTime).list();
        return new FebsResponse().success().data(liantongRingList);
    }
}


