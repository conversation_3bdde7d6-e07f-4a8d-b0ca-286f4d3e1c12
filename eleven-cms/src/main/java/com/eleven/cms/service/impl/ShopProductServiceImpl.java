package com.eleven.cms.service.impl;

import com.eleven.cms.entity.ShopProduct;
import com.eleven.cms.mapper.ShopProductMapper;
import com.eleven.cms.service.IShopProductService;
import com.eleven.cms.vo.FebsResponse;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 商城产品列表
 * @Author: jeecg-boot
 * @Date:   2023-09-19
 * @Version: V1.0
 */
@Service
public class ShopProductServiceImpl extends ServiceImpl<ShopProductMapper, ShopProduct> implements IShopProductService {

    @Override
    public FebsResponse queryShopProductList(String productClass) {
        return new FebsResponse().success().data(this.baseMapper.queryShopProductList(productClass));
    }
}
