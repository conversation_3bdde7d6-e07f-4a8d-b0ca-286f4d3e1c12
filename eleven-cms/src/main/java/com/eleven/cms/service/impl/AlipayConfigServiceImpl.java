package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.QyclDYPayPropertiesConfig;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.entity.Alipay;
import com.eleven.cms.mapper.AlipayMapper;
import com.eleven.cms.service.IAlipayConfigService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;


/**
 * @Description: 支付宝支付配置
 * @Author: jeecg-boot
 * @Date:   2022-12-29
 * @Version: V1.0
 */
@Slf4j
@Service
public class AlipayConfigServiceImpl extends ServiceImpl<AlipayMapper, Alipay> implements IAlipayConfigService {
    @Autowired
    QyclDYPayPropertiesConfig dyPayPropertiesConfig;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public List<Alipay> listValid(){
        return this.lambdaQuery().eq(Alipay::getIsValid, 1).list();
    }

    /**
     * 查询支付宝有效的权益充值列表（排除刷单渠道）
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.ALI_PAY_RIGHTS_CHANNEL_CONFIG_CACHE,key = "'all'",unless = "#result==null")
    public List<Alipay> aliPayRightsRechargeList(){
        return this.lambdaQuery().eq(Alipay::getIsValid, 1).notIn(Alipay::getBusinessType,BizConstant.ALIPAY_LIST).list();
    }

    /**
     * 支付宝投诉断开连接重新连接
     */
    @Override
    public void destroyAll(){
        this.implementHttpPostResult("http://**************:9528/migu-crbt/api/destroyAll","支付宝投诉断开连接重新连接");
    }

    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url,String msg) {
        return push(url,msg);
    }
    public String push(String url,String msg) {
        log.info(msg+",请求数据=>地址:{}",url);
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("msg", msg);
        RequestBody body = RequestBody.create(JSON, dataNode.toString());
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},响应参数:{}",url,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{}",url,e);
            return null;
        }
    }
}
