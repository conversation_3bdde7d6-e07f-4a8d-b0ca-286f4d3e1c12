package com.eleven.cms.service.impl;

import com.eleven.cms.entity.SmsMoLog;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.mapper.SmsMoLogMapper;
import com.eleven.cms.remote.BizUnsubscribeService;
import com.eleven.cms.service.ISmsMoLogService;
import com.eleven.cms.util.BizConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: cms_sms_mo_log
 * @Author: jeecg-boot
 * @Date:   2022-10-27
 * @Version: V1.0
 */
@Service
public class SmsMoLogServiceImpl extends ServiceImpl<SmsMoLogMapper, SmsMoLog> implements ISmsMoLogService {

    @Autowired
    BizUnsubscribeService bizUnsubscribeService;
    @Autowired
    IEsDataService esDataService;
    /**
     * 处理大唐短信上行通知
     * 对接大唐上行短信内容，针对短信内容含有“TD、QX、退、取消、不需要、10086901”的号码调用退订接口退订该号码订购的我们的所有业务
     * @param smsMoLog
     */
    @Override
    public void handleMo(SmsMoLog smsMoLog) {
        final String msgContent = smsMoLog.getMsgContent();
        //final boolean needCancel = StringUtils.containsAny(msgContent.toUpperCase(),
        //        "TD", "QX","T", "退", "取消", "不需要","不办","不要办理", "请删除", "10086901", "0000",
        //        "关闭","删除","不订购","不订","不要", "不想要了","取了","撤销","撤消","注销","消掉");
        final boolean needCancel = StringUtils.containsAny(msgContent.toUpperCase(),
                "TD", "QX","T", "退", "取消", "不需要", "不办", "不要办理", "请删除",
                "关闭","删除", "不订购","不订","不要", "不想要了", "取了", "撤销", "撤消", "注销", "消掉");
        //final boolean needCancel = true; //修改为回复任意内容退订
        smsMoLog.setCancel(needCancel?1:0);
        //退订最近3天的
        final List<EsSubscribe> esSubscribes = esDataService.queryRecentAllBizByMobile(smsMoLog.getMobile(),3L);
        if(!esSubscribes.isEmpty()){
            final EsSubscribe esSubscribe = esSubscribes.get(0);
            smsMoLog.setProvince(esSubscribe.getProvince());
            smsMoLog.setBizType(esSubscribe.getBizType());
            smsMoLog.setChannel(esSubscribe.getChannel());
            smsMoLog.setSubChannel(esSubscribe.getSubChannel());
        }
        this.save(smsMoLog);
        if(needCancel && !esSubscribes.isEmpty()){
            bizUnsubscribeService.unsubscribeAllBizByHistory(smsMoLog.getMobile(), esSubscribes, BizConstant.BIZ_UNSUBSCRIBE_REQUEST_WAY_SMS_MO);
        }
    }
}
