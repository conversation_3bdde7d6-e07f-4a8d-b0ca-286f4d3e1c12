package com.eleven.cms.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.SubChannel;
import com.eleven.cms.mapper.SubChannelMapper;
import com.eleven.cms.service.IAdPlatformService;
import com.eleven.cms.service.ISubChannelService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *  Service实现
 *
 * <AUTHOR>
 * @date 2021-06-16 10:11:00
 */
@Service
@RequiredArgsConstructor
@DS("xxl")
public class SubChannelServiceImpl extends ServiceImpl<SubChannelMapper, SubChannel> implements ISubChannelService {

    @Autowired
    private IAdPlatformService adPlatformService;

    @Cacheable(cacheNames = CacheConstant.SUB_CHANNEL_CONFIG_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    @Override
    public SubChannel findSubChannel(String subChannel){
        Pair<String,String> nullPair = Pair.of(null,null);
        if(StringUtils.isBlank(subChannel)){
            return null;
        }
//        if(subChannel.startsWith("OS")){
//            return "cpa";
//        }
        final SubChannel channel = this.lambdaQuery().eq(SubChannel::getTitle, subChannel).eq(SubChannel::getStatus, 0).one();
        if(channel != null && StringUtils.isNotBlank(channel.getAdPlatformId())){
            AdPlatform adPlatform = adPlatformService.getById(channel.getAdPlatformId());
            channel.setAdPlatform(adPlatform == null ? null : adPlatform.getAdPlatform());
        }

        return channel;
    }
}
