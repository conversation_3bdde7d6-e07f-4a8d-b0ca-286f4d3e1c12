package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PageBusinessConfig;
import com.eleven.cms.mapper.PageBusinessConfigMapper;
import com.eleven.cms.service.IPageBusinessConfigService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2023-08-17 16:07
 */
@Service
public class PageBusinessConfigServiceImpl extends ServiceImpl<PageBusinessConfigMapper, PageBusinessConfig> implements IPageBusinessConfigService {
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_PAGE_CACHE, key = "#root.methodName + '-' + #id", unless = "#result==null")
    public PageBusinessConfig selectById(String id) {
        return this.getById(id);
    }
}
