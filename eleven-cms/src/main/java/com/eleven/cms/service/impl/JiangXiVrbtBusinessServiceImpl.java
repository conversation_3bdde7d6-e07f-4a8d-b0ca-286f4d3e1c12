package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.*;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class JiangXiVrbtBusinessServiceImpl implements IBusinessCommonService {

    public static final String YIDONG_JIANGXI_VRBT_DUPLICATE_KEY_PREFIX = "yidong::jxvrbt:";
    public static final long YIDONG_JIANGXI_VRBT_SMS_INVALID_CACHE_SECONDS = 60;



    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    JiangXiVrbtApiProperties jiangXiVrbtApiProperties;
    @Autowired
    private IChannelService channelService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private Environment environment;

    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }


    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        Result result = SpringContextUtils.getBean(JiangXiVrbtBusinessServiceImpl.class).receiveOrder(subscribe);
        return result;
    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String smsDuplicateKey = YIDONG_JIANGXI_VRBT_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(smsDuplicateKey)) {
                return Result.captchaErr("请勿重复提交");
            }
            redisUtil.set(smsDuplicateKey, "invalidSmsCode", YIDONG_JIANGXI_VRBT_SMS_INVALID_CACHE_SECONDS);
            String orderId = IdWorker.get32UUID();
            Result<?> checkSmsResult = this.getCheckSms(subscribe.getMobile(), subscribe.getChannel(), smsCode);
            if(!checkSmsResult.isOK()){
                Subscribe upd = new Subscribe();
                upd.setId(target.getId());
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setModifyTime(new Date());
                upd.setResult(checkSmsResult.getMessage());
                upd.setIspOrderNo(orderId);
                subscribeService.updateSubscribeDbAndEs(upd);
                return checkSmsResult;
            }

            Result<?> subOrderResult = this.getSubOrder(subscribe.getMobile(), subscribe.getChannel(), smsCode,orderId,subscribe.getIp(),subscribe.getReferer());
            Subscribe upd = new Subscribe();
            upd.setId(target.getId());
            upd.setModifyTime(new Date());
            upd.setResult(subOrderResult.getMessage());
            upd.setIspOrderNo(orderId);
            upd.setExtra(subOrderResult.getResult().toString());
            if(subOrderResult.isOK()){
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                subscribeService.updateSubscribeDbAndEs(upd);
                return subOrderResult;
            }else{
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribeService.updateSubscribeDbAndEs(upd);
                return subOrderResult;
            }
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);



        //查询是否已开通了包月
        final boolean isSubMon = this.isSubMon(subscribe.getMobile(),subscribe.getChannel());
        //true表示已有包月
        if(isSubMon){
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            return Result.bizExists("你已开通,请勿重复开通");
        }
        Result<?> result = this.getSms(subscribe.getMobile(),subscribe.getChannel(),subscribe.getId());
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        return result;
    }


    /**
     * 查询是否当月是否订购江西移动视频彩铃
     * @param mobile
     * @param channel
     * @return
     */
    public boolean isSubMon(String mobile, String channel) {
        Integer jiangXiVrbt=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                                      .eq(Subscribe::getChannel, channel)
                                      .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS)
                                      .between(Subscribe::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                                      .count();
        if (jiangXiVrbt>0) {
            return true;
        }
        return false;
    }
    /**
     * 江西移动视频彩铃发送验证码
     * @param mobile
     * @param channel
     * @return
     */
    public Result<?> getSms(String mobile,String channel,String id) {
        Map<String, Object> map = Maps.newHashMap();
        JiangXiVrbtConfig jiangXiVrbtConfig=jiangXiVrbtApiProperties.getChannelMap().get(channel);
        map.put("customerCode",jiangXiVrbtConfig.getCustomerCode());
        map.put("productCode",jiangXiVrbtConfig.getProductCode());
        map.put("accountVal",mobile);
        map.put("ts",System.currentTimeMillis());
        map.put("sign",MD5Util.getJiangXiVrbtSigns(map,jiangXiVrbtConfig.getVrbtkey()));
        try {
            String content=this.implementHttpPostResult(jiangXiVrbtApiProperties.getSendSmsUrl(),map,"江西移动视频彩铃发送验证码");
            JiangXiVrbtSmsResult jiangXiVrbtSmsResult = mapper.readValue(content, JiangXiVrbtSmsResult.class);
            if(jiangXiVrbtSmsResult.isOK()){
                return Result.noauth(jiangXiVrbtSmsResult.getMessage(),id);
            }
            return  Result.error(jiangXiVrbtSmsResult.getMessage(),id);
        } catch (Exception e) {
            log.error("江西移动视频彩铃发送验证码异常,手机号:{},渠道号:{},主键Id:{}" ,mobile,channel,id, e.getMessage(),e);
            return Result.error("系统繁忙,请稍后再试!", id);
        }

    }
    /**
     * 江西移动视频彩铃校验验证码
     * @param mobile
     * @param channel
     * @return
     */
    public Result<?> getCheckSms(String mobile,String channel,String smsCode) {
        Map<String, Object> map = Maps.newHashMap();
        JiangXiVrbtConfig jiangXiVrbtConfig=jiangXiVrbtApiProperties.getChannelMap().get(channel);
        map.put("customerCode",jiangXiVrbtConfig.getCustomerCode());
        map.put("productCode",jiangXiVrbtConfig.getProductCode());
        map.put("accountVal",mobile);
        map.put("randomCode",smsCode);
        map.put("ts",System.currentTimeMillis());
        map.put("sign",MD5Util.getJiangXiVrbtSigns(map,jiangXiVrbtConfig.getVrbtkey()));
        try {
            String content=this.implementHttpPostResult(jiangXiVrbtApiProperties.getCheckSmsUrl(),map,"江西移动视频彩铃校验验证码");
            JiangXiVrbtSmsResult jiangXiVrbtSmsResult = mapper.readValue(content, JiangXiVrbtSmsResult.class);
            if(jiangXiVrbtSmsResult.isOK()){
                return Result.ok();
            }
            return Result.error(jiangXiVrbtSmsResult.getMessage());
        } catch (Exception e) {
            log.error("江西移动视频彩铃校验验证码异常,手机号:{},渠道号:{},验证码:{}" ,mobile,channel,smsCode, e.getMessage(),e);
            return Result.error("系统繁忙,请稍后再试!");
        }

    }


    /**
     * 江西移动视频彩铃发送验证码
     * @param mobile
     * @param channel
     * @return
     */
    public Result<?> getSubOrder(String mobile,String channel,String smsCode,String orderId,String ip,String app) {
        Map<String, Object> map = Maps.newHashMap();
        JiangXiVrbtConfig jiangXiVrbtConfig=jiangXiVrbtApiProperties.getChannelMap().get(channel);
        map.put("customerCode",jiangXiVrbtConfig.getCustomerCode());
        map.put("productCode",jiangXiVrbtConfig.getProductCode());
        map.put("accountVal",mobile);
        map.put("randomCode",smsCode);
        map.put("ts",System.currentTimeMillis());
        map.put("customerOrder",orderId);
        map.put("callbackUrl",jiangXiVrbtApiProperties.getOrderNotifyUrl());
        map.put("userIp",ip);
        map.put("launchUrl",jiangXiVrbtApiProperties.getLaunchUrl());
        map.put("launchApp",app);
        map.put("price",jiangXiVrbtConfig.getPrice());
        map.put("launchPlatform",jiangXiVrbtConfig.getLaunchPlatform());

        map.put("sign",MD5Util.getJiangXiVrbtSigns(map,jiangXiVrbtConfig.getVrbtkey()));

        try {
            String content=this.implementHttpPostResult(jiangXiVrbtApiProperties.getSubOrderUrl(),map,"江西移动视频彩铃下单");
            JiangXiVrbtSubOrderResult jiangXiVrbtSubOrderResult = mapper.readValue(content, JiangXiVrbtSubOrderResult.class);
            if(jiangXiVrbtSubOrderResult.isOK() && jiangXiVrbtSubOrderResult.getData().isOK()){
                return Result.ok(jiangXiVrbtSubOrderResult.getMessage(),jiangXiVrbtSubOrderResult.getData().getPlatOrderNo());
            }
            return Result.error(500,jiangXiVrbtSubOrderResult.getData()!=null?jiangXiVrbtSubOrderResult.getData().getMsg():jiangXiVrbtSubOrderResult.getMessage(),jiangXiVrbtSubOrderResult.getData()!=null?jiangXiVrbtSubOrderResult.getData().getPlatOrderNo():"");
        } catch (Exception e) {
            log.error("江西移动视频彩铃下单异常,手机号:{},渠道号:{},验证码:{},订单号:{}" ,mobile,channel,smsCode,orderId, e.getMessage(),e);
            return Result.error("系统繁忙,请稍后再试!",orderId);
        }
    }

    /**
     * 江西移动视频彩铃查询订单
     * @param transactionId
     * @return
     */
    public Result<?> queryOrder(String transactionId) {
        final Subscribe target = subscribeService.getById(transactionId);
        if(target==null){
            return Result.error("订单查询失败");
        }

        Map<String, Object> map = Maps.newHashMap();
        JiangXiVrbtConfig jiangXiVrbtConfig=jiangXiVrbtApiProperties.getChannelMap().get(target.getChannel());
        map.put("customerCode",jiangXiVrbtConfig.getCustomerCode());
        map.put("customerOrderNo",target.getIspOrderNo());
        map.put("ts",System.currentTimeMillis());
        map.put("sign",MD5Util.getJiangXiVrbtSigns(map,jiangXiVrbtConfig.getVrbtkey()));
        try {
            String content=this.implementHttpPostResult(jiangXiVrbtApiProperties.getSendSmsUrl(),map,"江西移动视频彩铃查询订单");
            JiangXiVrbtQueryOrderResult jiangXiVrbtQueryOrderResult = mapper.readValue(content, JiangXiVrbtQueryOrderResult.class);
            if(jiangXiVrbtQueryOrderResult.isOK() && BizConstant.JIANGXI_VRBT_ORDER_CODE_SUCCESS.equals(jiangXiVrbtQueryOrderResult.getOrderStatus())){
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setResult("开通成功");
                subscribeService.updateSubscribeDbAndEs(upd);
                channelService.AdEffectFeedbackNew(upd, SUBSCRIBE_STATUS_SUCCESS);
                return Result.bizExists("开通成功");
            }

            if(jiangXiVrbtQueryOrderResult.isOK() && BizConstant.JIANGXI_VRBT_ORDER_CODE_FAILED.equals(jiangXiVrbtQueryOrderResult.getOrderStatus())){
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setOpenTime(new Date());
                upd.setResult("开通失败");
                subscribeService.updateSubscribeDbAndEs(upd);
                channelService.AdEffectFeedbackNew(upd, SUBSCRIBE_STATUS_FAIL);
                return Result.bizExists("开通失败");
            }
            return Result.bizExists("开通中");
        } catch (Exception e) {
            log.error("江西移动视频彩铃查询订单异常,手机号:{},渠道号:{},主键Id:{}" ,target.getMobile(),target.getChannel(),transactionId, e.getMessage(),e);
            return Result.error("系统繁忙,请稍后再试!");
        }

    }



    /**
     * 江西移动视频彩铃无纸化协议查询
     * @param mobile
     * @param channel
     * @return
     */
    public Result<?> getAgreement(String mobile,String channel) {
        Map<String, Object> map = Maps.newHashMap();
        JiangXiVrbtConfig jiangXiVrbtConfig=jiangXiVrbtApiProperties.getChannelMap().get(channel);
        map.put("customerCode",jiangXiVrbtConfig.getCustomerCode());
        map.put("productCode",jiangXiVrbtConfig.getProductCode());
        map.put("accountVal",mobile);
        map.put("ts",System.currentTimeMillis());
        map.put("sign",MD5Util.getJiangXiVrbtSigns(map,jiangXiVrbtConfig.getVrbtkey()));
        try {
            String content=this.implementHttpPostResult(jiangXiVrbtApiProperties.getQueryAgreementUrl(),map,"江西移动视频彩铃无纸化协议查询");
            JiangXiVrbtAgreementResult jiangXiVrbtAgreementResult = mapper.readValue(content, JiangXiVrbtAgreementResult.class);
            if(jiangXiVrbtAgreementResult.isOK()){
                return Result.ok(jiangXiVrbtAgreementResult.getMessage(),jiangXiVrbtAgreementResult.getData());
            }
            return Result.error(500,jiangXiVrbtAgreementResult.getMessage());
        } catch (Exception e) {
            log.error("江西移动视频彩铃无纸化协议查询,手机号:{},渠道号:{}" ,mobile,channel, e.getMessage(),e);
            return Result.error("系统繁忙,请稍后再试!");
        }
    }

    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url, Object fromValue,String msg) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg);
    }
    public String push(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }



}
