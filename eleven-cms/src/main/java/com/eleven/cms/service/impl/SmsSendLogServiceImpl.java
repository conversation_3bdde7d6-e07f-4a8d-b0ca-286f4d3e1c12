package com.eleven.cms.service.impl;

import com.eleven.cms.entity.SmsSendLog;
import com.eleven.cms.mapper.SmsSendLogMapper;
import com.eleven.cms.service.ISmsSendLogService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;

/**
 * @Description: cms_sms_send_log
 * @Author: jeecg-boot
 * @Date:   2023-09-25
 * @Version: V1.0
 */
@Service
public class SmsSendLogServiceImpl extends ServiceImpl<SmsSendLogMapper, SmsSendLog> implements ISmsSendLogService {

    public static final String SMS_SEND_STATE_SUCCESS = "0";


    @Override
    public void saveSendLog(String mobile, String msgId, String msgContent, Integer sendStatus){
        SmsSendLog smsSendLog = new SmsSendLog();
        smsSendLog.setMobile(mobile);
        smsSendLog.setMsgId(msgId);
        smsSendLog.setMsgContent(msgContent);
        smsSendLog.setSendStatus(sendStatus);
        smsSendLog.setCreateTime(new Date());
        this.save(smsSendLog);
    }

    /**
     * 大唐短信推送状态报告通知(V3)
     * @param mobile
     * @param msgId
     * @param state
     * @param time
     */
    @Override
    public void receiveSendStateNotify(String mobile, String msgId, String state, Date time) {
        Integer sendStatus = SMS_SEND_STATE_SUCCESS.equals(state) ?  1 : 0;
        this.lambdaUpdate()
                .eq(SmsSendLog::getMsgId, msgId)
                .eq(SmsSendLog::getMobile, mobile)
                .set(SmsSendLog::getSendStatus, sendStatus)
                .set(SmsSendLog::getNotifyState, state)
                .set(SmsSendLog::getUpdateTime, time)
                .update();
    }
}
