package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.ColumnMusicDy;
import com.eleven.cms.entity.ColumnMusicDyVo;
import com.eleven.cms.entity.MusicDy;
import com.eleven.cms.mapper.ColumnMusicDyMapper;
import com.eleven.cms.mapper.MusicDyMapper;
import com.eleven.cms.service.IColumnMusicDyService;
import com.eleven.cms.vo.MusicDyVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 订阅包栏目歌曲
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
@Slf4j
@Service
public class ColumnMusicDyServiceImpl extends ServiceImpl<ColumnMusicDyMapper, ColumnMusicDy> implements IColumnMusicDyService {

	@Autowired
	private ColumnMusicDyMapper columnMusicDyMapper;
	@Autowired
	private MusicDyMapper musicDyMapper;
	@Override
	public List<ColumnMusicDy> selectByMainId(String mainId) {
		return columnMusicDyMapper.selectByMainId(mainId);
	}

	@Cacheable(cacheNames = CacheConstant.CMS_COLUMNDY_CACHE,key = "#columnId+'-'+#page.getCurrent()+'_'+#page.getSize()",unless = "#result==null")
	@Override
	public IPage<MusicDyVo> selectColumnMusicDyVoPage(Page<MusicDyVo> page, String columnId) {
		return columnMusicDyMapper.selectColumnMusicDyVoPage(page, columnId);
	}
	@Override
	public List<ColumnMusicDyVo> selectByMainIdNew(String id) {
		return columnMusicDyMapper.selectByMainIdNew(id);
	}
	@Override
	@Transactional
	public void importMusic(List<ColumnMusicDyVo> list,String id) {

		try{

			List<ColumnMusicDy> columnMusicDys = new ArrayList<>();
			List<String> ids = new ArrayList<>();

			//查询旧数据
			List<ColumnMusicDyVo> columnMusicDyList = this.selectByMainIdNew(id);
			if(columnMusicDyList != null && columnMusicDyList.size() > 0){
				for (ColumnMusicDyVo columnMusicDyVo : columnMusicDyList) {
					ids.add(columnMusicDyVo.getId());
				}
			}

			//版权id --> 歌曲id --> 删除 插入
			if(list != null && list.size() > 0){
				for (int i = 0; i < list.size(); i++) {
					if (StringUtils.isNotEmpty(list.get(i).getCopyrightId())){
						MusicDy musicDy = this.selectByCopyrightId(list.get(i).getCopyrightId());
						if(musicDy != null){
							ColumnMusicDy columnMusicDy = new ColumnMusicDy();
							columnMusicDy.setColumnId(id);
							columnMusicDy.setMusicId(musicDy.getId());
							columnMusicDy.setPriority(columnMusicDys.size() + 1);
							columnMusicDy.setTitle(musicDy.getMusicName());
							columnMusicDy.setTag(list.get(i).getTag());
							columnMusicDy.setExtra(list.get(i).getExtra());
							columnMusicDys.add(columnMusicDy);
						}
					}
				}

			}
			if(columnMusicDys.size() > 0){
				//先刪除旧数据
				if(ids.size() > 0){
					this.removeByIds(ids);
				}
				boolean b = this.saveBatch(columnMusicDys);
				log.info(b == true ? "importMusic批量插入操作成功" : "importMusic批量插入操作失败");
			}
		}catch (Exception e){
			log.error("importMusic出错：",e);
		}
	}
	private MusicDy selectByCopyrightId(String copyrightId) {
		QueryWrapper<MusicDy> queryWrapper = new QueryWrapper<>();
		MusicDy musicDy = new MusicDy();
		musicDy.setCopyrightId(copyrightId);
		queryWrapper.setEntity(musicDy);
		return musicDyMapper.selectOne(queryWrapper);
	}
}
