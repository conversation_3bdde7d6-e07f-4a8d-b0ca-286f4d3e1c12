package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.SuccessLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.DianxinVrbtService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IBusinessCommonService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.DianxinResp;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 电信视频彩铃报备接口
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/7 17:10
 **/
@Slf4j
@Service
public class DianxinVrbtBusinessServiceImpl  implements IBusinessCommonService {
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;

    @Autowired
    RedisUtil redisUtil;


    @Autowired
    private DianxinVrbtService dianxinVrbtService;
    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspDianxin()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("电信省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持电信用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        return SpringContextUtils.getBean(DianxinVrbtBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    @ValidationLimit
    @SuccessLimit
    public Result receiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            final boolean packageExist = dianxinVrbtService.queryPackageExist(mobile, subscribe.getChannel());
            if (packageExist) {
                return Result.ok("已有包月");
            }
            final DianxinResp dianxinResp = dianxinVrbtService.newConfirmOpenOrderLaunchedEx(mobile, "0", subscribe.getSource(), subscribe.getChannel());
            if (!dianxinResp.isOK()) {
                return Result.error("电信视频彩铃业务下单失败:" + dianxinResp.getResMessage());
            }
            subscribe.setIspOrderNo(dianxinResp.getOrderNo());
            subscribeService.createSubscribeDbAndEs(subscribe);
            return Result.redirect("redirect telecom vrbt page", dianxinResp.getFeeUrl());
        } else {
            return Result.error("电信视频彩铃无需提交短信验证码");
        }
    }
}
