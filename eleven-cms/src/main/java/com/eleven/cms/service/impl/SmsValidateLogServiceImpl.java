package com.eleven.cms.service.impl;

import com.eleven.cms.entity.SmsValidateLog;
import com.eleven.cms.mapper.SmsValidateLogMapper;
import com.eleven.cms.service.ISmsValidateLogService;
import com.eleven.cms.util.BizConstant;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;

/**
 * @Description: cms_sms_validate_log
 * @Author: jeecg-boot
 * @Date:   2020-10-26
 * @Version: V1.0
 */
@Service
public class SmsValidateLogServiceImpl extends ServiceImpl<SmsValidateLogMapper, SmsValidateLog> implements ISmsValidateLogService {

    @Override
    public void logCreate(String mobile, String subChannel) {
        log(mobile, subChannel, BizConstant.SMS_VALIDATE_LOG_ACTION_CREATED);
    }

    @Override
    public void logValid(String mobile, String subChannel) {
        log(mobile, subChannel, BizConstant.SMS_VALIDATE_LOG_ACTION_VALID);
    }

    private void log(String mobile, String subChannel,Integer action) {
        SmsValidateLog smsValidateLog = new SmsValidateLog();
        smsValidateLog.setMobile(mobile);
        smsValidateLog.setSubChannel(subChannel);
        smsValidateLog.setCreateTime(new Date());
        smsValidateLog.setAction(action);
        save(smsValidateLog);
    }
}
