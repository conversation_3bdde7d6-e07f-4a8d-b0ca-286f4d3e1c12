package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.AliPayComplainProperties;
import com.eleven.cms.config.QyclDYPayPropertiesConfig;
import com.eleven.cms.dto.DouYinToken;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.mapper.WechatConfigLogMapper;
import com.eleven.cms.service.IWarnMobileService;
import com.eleven.cms.service.IWechatConfigLogService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

/**
 * @Description: qycl_wechat_config_log
 * @Author: jeecg-boot
 * @Date:   2022-12-05
 * @Version: V1.0
 */
@Slf4j
@Service
public class WechatConfigLogServiceImpl extends ServiceImpl<WechatConfigLogMapper, WechatConfigLog> implements IWechatConfigLogService {
    @Autowired
    private AliPayComplainProperties aliPayComplainProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    public  MediaType FORM_DATA;
    @Autowired
    private Environment environment;
    @Autowired
    QyclDYPayPropertiesConfig dyPayPropertiesConfig;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
        this.FORM_DATA= MediaType.parse(org.springframework.http.MediaType.MULTIPART_FORM_DATA_VALUE);
    }
    /**
     * 微信支付配置
     * @param tradeType
     * @param businessType
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_WECHAT_PAY_CACHE,key = "#tradeType + '-' + #businessType",unless = "#result==null")
    public WechatConfigLog getWechatConfig(String tradeType,String businessType) {
        WechatConfigLog wechatConfigLog = this.lambdaQuery()
                .eq(WechatConfigLog::getIsValid, BizConstant.IS_VALID)
                .eq(WechatConfigLog::getTradeType, tradeType)
                .eq(WechatConfigLog::getBusinessType, businessType).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        return wechatConfigLog;
    }


    /**
     * 微信支付回调配置
     * @param appId
     * @param mchId
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_WECHAT_NOTIFY_CACHE,key = "#appId + '-' + #mchId",unless = "#result==null")
    public WechatConfigLog getWechatNotifyConfig(String appId,String mchId) {
        WechatConfigLog wechatConfigLog = this.lambdaQuery()
                .eq(WechatConfigLog::getAppId, appId)
                .eq(WechatConfigLog::getMchId, mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        return wechatConfigLog;
    }

    /**
     * 抖音支付配置
     * @param businessType
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_DY_PAY_CACHE,key = "#tradeType + '-' + #businessType",unless = "#result==null")
    public WechatConfigLog getDyConfig(String tradeType,String businessType) {
        WechatConfigLog wechatConfigLog = this.lambdaQuery()
                .eq(WechatConfigLog::getIsValid, BizConstant.IS_VALID)
                .eq(WechatConfigLog::getTradeType, tradeType)
                .eq(WechatConfigLog::getBusinessType, businessType).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        return wechatConfigLog;
    }


    /**
     * 抖音支付回调配置
     * @param appId
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_DY_NOTIFY_CACHE,key = "#appId",unless = "#result==null")
    public WechatConfigLog getDyNotifyConfig(String appId) {
        WechatConfigLog wechatConfigLog = this.lambdaQuery()
                .eq(WechatConfigLog::getAppId, appId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        return wechatConfigLog;
    }

    /**
     * 抖音获取token
     * @param appid
     * @param secret
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_DY_TOKEN_CACHE,key = "#appid + '-' + #secret",unless = "#result==null")
    public FebsResponse getDyToken(String appid,String secret) {
        ObjectNode auth =mapper.createObjectNode();
        auth.put("appid",appid);
        auth.put("secret",secret);
        auth.put("grant_type","client_credential");
        try {
            final ObjectNode objectNode = mapper.readValue(implementHttpPostResult("https://developer.toutiao.com/api/apps/v2/token", auth,"抖音小程序获取token"), ObjectNode.class);
            return new FebsResponse().success().data(objectNode);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();
    }
    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url, Object fromValue,String msg) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg);
    }
    public String push(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }

    /**
     * 快手支付配置
     * @param businessType
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.QYCL_CACHE_KS_PAY_CACHE,key = "#tradeType + '-' + #businessType",unless = "#result==null")
    public WechatConfigLog getKsConfig(String tradeType,String businessType) {
        WechatConfigLog wechatConfigLog = this.lambdaQuery()
                .eq(WechatConfigLog::getIsValid, BizConstant.IS_VALID)
                .eq(WechatConfigLog::getTradeType, tradeType)
                .eq(WechatConfigLog::getBusinessType, businessType).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        return wechatConfigLog;
    }



    /**
     * 抖音小程序新版本获取token
     * @param appId
     * @param secret
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.CACHE_DOUYIN_TOKEN_CACHE,key = "#appId + '-' + #secret",unless = "#result==null")
    public DouYinToken getDouYinToken(String appId,String secret) {
        Map<String, String> auth =Maps.newHashMap();
        auth.put("appid",appId);
        auth.put("secret",secret);
        auth.put("grant_type","client_credential");
        try {
            final DouYinToken douYinToken = mapper.readValue(implementHttpPostResult(dyPayPropertiesConfig.getClientTokenUrl(), auth,"抖音小程序新版本获取token"), DouYinToken.class);
            return douYinToken;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url,  Map<String, String> map,String msg) {
        return push(url, map,msg);
    }

    private String push(String url,Map<String, String> map,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,map);
        FormBody.Builder builder = new FormBody.Builder();
        map.forEach((key, value) -> {
            builder.add(key,value);
        });
        RequestBody body = builder.build();
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,map,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,map,e);
            return null;
        }
    }
}
