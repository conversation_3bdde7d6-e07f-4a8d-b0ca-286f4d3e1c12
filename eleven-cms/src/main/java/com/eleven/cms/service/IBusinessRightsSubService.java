package com.eleven.cms.service;

import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.vo.FebsResponse;

import java.util.List;

public interface IBusinessRightsSubService {
//    1.有没有资格，查订购关系，有没有领取记录
//    2.返回给页面权益列表
//    3.创建预约充值的记录，领取

    //查订购关系
    FebsResponse memberVerify(String mobile,String serviceId);
    //有没有领取记录
    FebsResponse rechargRecordVerify(String mobile,String serviceId);

    //创建权益预约充值记录
    FebsResponse createScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel);

    //创建网页权益预约充值记录
    FebsResponse webCreateScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel);

    //定时任务权益充值校验
    FebsResponse rechargeForScheduleVerify(JunboChargeLog junboChargeLog);

    //定时任务权益充值
    void rechargeForSchedule(JunboChargeLog junboChargeLog);

    //根据充值通知更新订单领取状态
    void updateRechargeState(JunboChargeLog junboChargeLog);
}
