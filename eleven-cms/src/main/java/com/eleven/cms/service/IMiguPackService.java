package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.MiguPack;
import com.eleven.cms.entity.RightsPack;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 咪咕业务包
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
public interface IMiguPackService extends IService<MiguPack> {

	/**
	 * 添加一对多
	 *
	 */
	public void saveMain(MiguPack miguPack,List<RightsPack> rightsPackList) ;

	/**
	 * 修改一对多
	 *
	 */
	public void updateMain(MiguPack miguPack,List<RightsPack> rightsPackList);

	/**
	 * 删除一对多
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	public List<MiguPack> selectMain(String orderKey);

	Optional<MiguPack> queryMiguPackDetail(String serviceId, String packName);

	List<MiguPack> queryPackByBusinessId(MiguPack pack);

	List<MiguPack> queryMiguPackList(String serviceId);

}
