package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.XinjiangYouranProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.remote.HebeiYidongService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.HebeiYidongResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("youranHebeiYidongCommonService")
@Slf4j
public class YouranHebeiYidongCommonServiceImpl implements IBizCommonService {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    HebeiYidongService hebeiYidongService;
    @Autowired
    XinjiangYouranProperties xinjiangYouranProperties;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;

    @Override
    @ValidationLimit
    public Result filerCheck(Subscribe subscribe) {
        if(BizConstant.BIZ_TYPE_HBYD_LLB.equals(subscribe.getChannel())){
            return hebeiYidongService.verify(subscribe.getMobile(),subscribe.getChannel()) ? Result.ok() : Result.error("前置校验失败");
        }
        return Result.ok();
    }

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        HebeiYidongResult hebeiYidongResult = hebeiYidongService.getSms(subscribe.getMobile(), subscribe.getChannel());
        if (hebeiYidongResult.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"res_code\":\""+hebeiYidongResult.getResCode()+"\",\"res_msg\":\""+hebeiYidongResult.getResMsg()+"\",\"res_desc\":\""+hebeiYidongResult.getResDesc()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        HebeiYidongResult hebeiYidongResult = hebeiYidongService.smsCode(subscribe.getMobile(), smsCode, subscribe.getChannel());
        if (hebeiYidongResult.isOK()) {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            upd.setOpenTime(new Date());
            upd.setResult(hebeiYidongResult.getResCode() + ":" + hebeiYidongResult.getResDesc());
            subscribeService.updateSubscribeDbAndEs(upd);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            //加入延迟队列校验
            rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
            subscribeService.saveChannelLimit(subscribe);
            return Result.ok("订阅成功");
        } else {
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setOpenTime(new Date());
            upd.setResult(hebeiYidongResult.getResDesc());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }

    }
}
