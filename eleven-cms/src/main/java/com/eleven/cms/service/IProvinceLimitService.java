package com.eleven.cms.service;

import com.eleven.cms.entity.ProvinceLimit;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 省份限制
 * @Author: jeecg-boot
 * @Date:   2021-02-22
 * @Version: V1.0
 */
public interface IProvinceLimitService extends IService<ProvinceLimit> {
    ProvinceLimit selectByCode(String provinceCode);

    boolean isAvailableMobile(String provinceCode);

    boolean isAvailableUnicom(String provinceCode);

    boolean isAvailableTelecom(String provinceCode);
}
