package com.eleven.cms.service.impl;

import com.eleven.cms.ad.KaSaiStockLianTongProperties;
import com.eleven.cms.ad.KaSaiStockYidongProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IKaSaiStockService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;

/**
 * 卡塞存量业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:43
 **/
@Slf4j
@Service
public class KaSaiStockServiceServiceImpl implements IKaSaiStockService {
    public static final Integer KASAI_STOCK_YD_STATUS_SUB_SUCCESS = 302; //卡塞存量移动开通成功
    public static final Integer KASAI_STOCK_LT_STATUS_SUB_SUCCESS = 1; //卡塞存量联通开通成功
    @Autowired
    private KaSaiStockYidongProperties kaSaiStockYidongProperties;
    @Autowired
    private KaSaiStockLianTongProperties kaSaiStockLianTongProperties;
    public static final String KASAI_STOCK_YIDONG_TOKEN_KEY_PREFIX = "kaSaiStock::yidong::token";
    public static final String KASAI_STOCK_LIANTONG_TOKEN_KEY_PREFIX = "kaSaiStock::liantong::token";
    //token 卡塞存量移动缓存时间 24小时
    public static final Long KASAI_STOCK_YIDONG_CATCHE_TTL_SECONDS = 86400L;
    //token 卡塞存量联通缓存时间 24小时
    public static final Long KASAI_STOCK_LIANTONG_CATCHE_TTL_SECONDS = 86400L;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    /**
     * 卡塞存量业务移动发送验证码
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> yidongSendSms(Subscribe subscribe) {
        String token =(String)redisUtil.get(KASAI_STOCK_YIDONG_TOKEN_KEY_PREFIX);
        if(StringUtils.isBlank(token)){
            token=this.getYiDongToken(subscribe.getMobile());
        }
        if(StringUtils.isBlank(token)){
            return Result.error("短信发送失败",subscribe.getId());
        }

        ObjectNode node =mapper.createObjectNode();
        node.put("sourceUrl",kaSaiStockYidongProperties.getSourceUrl());
        node.put("linkNum",kaSaiStockYidongProperties.getLinkNum());
        node.put("mobile",subscribe.getMobile());
        node.put("orderNum",subscribe.getIspOrderNo());
        Map<String, String> header = Maps.newHashMap();
        header.put("token",token);
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpPostResult(kaSaiStockYidongProperties.getSendSmsUrl(), node,"卡塞存量业务移动短信发送接口",header,subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.ok("短信发送成功",subscribe.getId());
        }
        try {
            KaSaiStockYiDongSmsResult result = mapper.readValue(content, KaSaiStockYiDongSmsResult.class);
            if(result.isOK()){
                return Result.ok("短信发送成功",subscribe.getId());
            }
            return Result.error(result.getMessage().toString(),subscribe.getId());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("短信发送失败",subscribe.getId());
    }
    /**
     * 卡塞存量业务移动获取token
     * @return
     */
    private String getYiDongToken(String mobile) {
        Map<String, String> header = Maps.newHashMap();
        header.put("token",kaSaiStockYidongProperties.getToken());
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpGetResult(kaSaiStockYidongProperties.getLoadTokenUrl()+"?linkNum="+kaSaiStockYidongProperties.getLinkNum(),"卡塞存量业务移动获取token接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return null;
        }
        redisUtil.set(KASAI_STOCK_YIDONG_TOKEN_KEY_PREFIX,content, KASAI_STOCK_YIDONG_CATCHE_TTL_SECONDS);
        return content;
    }

    /**
     * 卡塞存量业务移动提交订单
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> yidongSubmitSub(Subscribe subscribe) {
        String token =(String)redisUtil.get(KASAI_STOCK_YIDONG_TOKEN_KEY_PREFIX);
        if(StringUtils.isBlank(token)){
            token=this.getYiDongToken(subscribe.getMobile());
        }
        if(StringUtils.isBlank(token)){
            return Result.error("提交订单失败");
        }
        Result<?> resultLogin=this.getYiDongLogin(subscribe,token);
        if(!resultLogin.isOK()){
            return resultLogin;
        }
        ObjectNode node =mapper.createObjectNode();
        node.put("sourceUrl",kaSaiStockYidongProperties.getSourceUrl());
        node.put("linkNum",kaSaiStockYidongProperties.getLinkNum());
        node.put("mobile",subscribe.getMobile());
        node.put("orderNum",subscribe.getIspOrderNo());
        node.put("smsCode",subscribe.getSmsCode());
        Map<String, String> header = Maps.newHashMap();
        header.put("token",token);
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpPostResult(kaSaiStockYidongProperties.getBookProductUrl(), node,"卡塞存量业务移动提交订单接口",header,subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.ok("提交订单成功","");
        }
        try {
            KaSaiStockYiDongSubmitResult result = mapper.readValue(content, KaSaiStockYiDongSubmitResult.class);
            if(result.isOK()){
                return Result.ok("提交订单成功",result.getData()!=null?result.getData().getOrderNo():"");
            }
            return Result.error(result.getMessage());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("提交订单失败");
    }
    /**
     * 卡塞存量业务移动登录
     * @return
     */
    private Result<?> getYiDongLogin(Subscribe subscribe,String token) {
        ObjectNode node =mapper.createObjectNode();
        node.put("sourceUrl",kaSaiStockYidongProperties.getSourceUrl());
        node.put("linkNum",kaSaiStockYidongProperties.getLinkNum());
        node.put("smsCode",subscribe.getSmsCode());
        node.put("mobile",subscribe.getMobile());
        node.put("orderNum",subscribe.getIspOrderNo());

        Map<String, String> header = Maps.newHashMap();
        header.put("token",token);
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpPostResult(kaSaiStockYidongProperties.getLoginUrl(), node,"卡塞存量业务移动登录接口",header,subscribe.getMobile());
        if(StringUtil.isEmpty(content) || content.contains("\"code\":200")){
            return Result.ok("登录成功");
        }
        return Result.error("登录失败");
    }
    /**
     * 卡塞存量业务联通发送验证码
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> liantongSendSms(Subscribe subscribe) {
        String token =(String)redisUtil.get(KASAI_STOCK_LIANTONG_TOKEN_KEY_PREFIX);
        if(StringUtils.isBlank(token)){
            token=this.getLianTongToken(subscribe.getMobile());
        }
        if(StringUtils.isBlank(token)){
            return Result.error("短信发送失败",subscribe.getId());
        }
        ObjectNode node =mapper.createObjectNode();
        node.put("linkNum",kaSaiStockLianTongProperties.getLinkNum());
        node.put("mobile",subscribe.getMobile());
        node.put("orderNum",subscribe.getIspOrderNo());
        String timestamp=DateUtil.formatFullTime(LocalDateTime.now());
        node.put("pageSn",timestamp);
        Map<String, String> header = Maps.newHashMap();
        header.put("token",token);
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpPostResult(kaSaiStockLianTongProperties.getSendSmsUrl(), node,"卡塞存量业务联通短信发送接口",header,subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.ok("短信发送成功",subscribe.getId());
        }
        try {
            KaSaiStockLianTongSmsResult result = mapper.readValue(content, KaSaiStockLianTongSmsResult.class);
            if(result.isOK()){
                return Result.ok("短信发送成功",subscribe.getId());
            }
            return Result.error(result.getMessage(),subscribe.getId());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("短信发送失败",subscribe.getId());
    }
    /**
     * 卡塞存量业务联通获取token
     * @return
     */
    private String getLianTongToken(String mobile) {
        Map<String, String> header = Maps.newHashMap();
        header.put("token",kaSaiStockLianTongProperties.getToken());
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpGetResult(kaSaiStockLianTongProperties.getLoadTokenUrl()+"?linkNum="+kaSaiStockLianTongProperties.getLinkNum(),"卡塞存量业务联通获取token接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return null;
        }
        redisUtil.set(KASAI_STOCK_LIANTONG_TOKEN_KEY_PREFIX,content, KASAI_STOCK_LIANTONG_CATCHE_TTL_SECONDS);
        return content;
    }

    /**
     * 卡塞存量业务联通提交订单
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> liantongSubmitSub(Subscribe subscribe) {
        String token =(String)redisUtil.get(KASAI_STOCK_LIANTONG_TOKEN_KEY_PREFIX);
        if(StringUtils.isBlank(token)){
            token=this.getLianTongToken(subscribe.getMobile());
        }
        if(StringUtils.isBlank(token)){
            return Result.error("提交订单失败");
        }
        Result<?> resultLogin=this.getLianTongLogin(subscribe,token);
        if(!resultLogin.isOK()){
            return resultLogin;
        }

        ObjectNode node =mapper.createObjectNode();
        node.put("linkNum",kaSaiStockLianTongProperties.getLinkNum());
        node.put("smsCode",subscribe.getSmsCode());
        node.put("mobile",subscribe.getMobile());
        node.put("orderNum",subscribe.getIspOrderNo());
        Map<String, String> header = Maps.newHashMap();
        header.put("token",token);
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpPostResult(kaSaiStockLianTongProperties.getBookProductUrl(), node,"卡塞存量业务联通提交订单接口",header,subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.ok("提交订单成功","");
        }
        try {
            KaSaiStockLianTongSubmitResult result = mapper.readValue(content, KaSaiStockLianTongSubmitResult.class);
            if(result.isOK()){
                return Result.ok("提交订单成功",result.getData()!=null?result.getData().getTorder():"");
            }
            return Result.error(result.getMessage().toString());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("提交订单失败");
    }
    /**
     * 卡塞存量业务联通登录
     * @return
     */
    private Result<?> getLianTongLogin(Subscribe subscribe,String token) {
        ObjectNode node =mapper.createObjectNode();
        node.put("linkNum",kaSaiStockLianTongProperties.getLinkNum());
        node.put("smsCode",subscribe.getSmsCode());
        node.put("mobile",subscribe.getMobile());
        node.put("orderNum",subscribe.getIspOrderNo());
        Map<String, String> header = Maps.newHashMap();
        header.put("token",token);
        header.put("Content-Type", "application/json;charset=UTF-8");
        String content =implementHttpPostResult(kaSaiStockLianTongProperties.getLoginUrl(), node,"卡塞存量业务联通登录接口",header,subscribe.getMobile());
        if(StringUtil.isEmpty(content) || content.contains("\"code\":200")){
            return Result.ok("登录成功");
        }
        return Result.error("登录失败");
    }
    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url, Object fromValue,String msg, Map<String, String> header,String mobile) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,header,mobile);
    }

    private String push(String url,String raw,String msg, Map<String, String> header,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},请求参数:{},响应参数:{}",mobile,url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,raw,e);
            return null;
        }
    }
    /**
     * 发起http请求get
     */
    public String implementHttpGetResult(String url,String msg,Map<String, String> header,String mobile) {
        return pushGet(url,msg,header,mobile);
    }

    public String pushGet(String url,String msg,Map<String, String> header,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{}",mobile,url);
        Request request = new Request.Builder().url(url).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            Headers headers=response.headers();
            log.info(msg+",响应数据=>手机号:{},地址:{},响应参数:{}",mobile,url,content);
            if(content.contains("\"code\":200")){
                return headers.get("token");
            }
            return null;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{}",mobile,url,e);
            return null;
        }
    }



    @Override
    public Map<String, Object> upSyncMessage(Map<String, Object> map,KaSaiStockRequest kaSaiStockRequest){
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, kaSaiStockRequest.getOrderNum()).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe==null){
             subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, kaSaiStockRequest.getMobile()).eq(Subscribe::getExtra, kaSaiStockRequest.getOrderNo()).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        }
        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if ((KASAI_STOCK_LT_STATUS_SUB_SUCCESS.equals(kaSaiStockRequest.getStatus()) || KASAI_STOCK_YD_STATUS_SUB_SUCCESS.equals(kaSaiStockRequest.getStatus())) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result =kaSaiStockRequest.getName() +"开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else{
                String result = kaSaiStockRequest.getName() +"开通结果==>{\"resCode\":\""+kaSaiStockRequest.getStatus()+"\",\"resMsg\":\""+kaSaiStockRequest.getStatusDesc()+"\"}";
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
        map.put("data",null);
        map.put("code","200");
        map.put("msg","成功");
        return map;

    }
}
