package com.eleven.cms.service;

import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.Result;

import java.util.Map;

/**
 * 天翼通讯助理
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 15:47
 **/
public interface ITianyiCommAssistService {

    Result<?> sendMessage(String mobile,Subscribe subscribe);

    Result<?> submitOrder(String mobile,String orderUrl,String verificationCode);

    Map<String, Object> tianyiCommAssistNotify(String phone, String orderStatus,String orderId,Map<String, Object> map);
}
