package com.eleven.cms.service.impl;

import com.eleven.cms.abstracts.AbstractOrder;
import com.eleven.cms.dto.ShanDongCountCodeQueryDTO;
import com.eleven.cms.dto.ShanDongGetSmsCodeDTO;
import com.eleven.cms.dto.ShanDongOrderDTO;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.cms.util.shandong.ShanDongYiDongConfig;
import com.eleven.cms.util.shandong.ShanDongYiDongHttpUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * 山东移动项目权益超市铂金会员（家庭流量版）
 *
 * <AUTHOR>
 * @datetime 2024/11/20 14:48
 */
@Service
@RequiredArgsConstructor
public class ShanDongYiDongService extends AbstractOrder {

    private final ISubscribeService subscribeService;
    private final ShanDongYiDongConfig config;

    @Override
    protected Result beforeGetSmsCodeRequest(JsonNode jsonNode) {
        String channel = jsonNode.get("channel").asText();

        ShanDongCountCodeQueryDTO shanDongCountCodeQueryDTO = new ShanDongCountCodeQueryDTO();
        shanDongCountCodeQueryDTO.setTelnum(jsonNode.get("mobile").asText());
        shanDongCountCodeQueryDTO.setCountCode(config.getProductMap().get(channel).get("productCode"));
        shanDongCountCodeQueryDTO.setLogTag(config.getProductMap().get(channel).get("productName"));
        String result = ShanDongYiDongHttpUtils.countCodeQuery(shanDongCountCodeQueryDTO, buildExtraInfoMap(jsonNode));
        JsonNode resultJsonNode = JacksonUtils.readTree(result);
        if ("10000".equals(resultJsonNode.at("/code").asText())) {
            return Result.ok();
        } else {
            String message = resultJsonNode.at("/message").asText();
            StringBuilder sb = new StringBuilder();
            sb.append(message);

            JsonNode blackListJsonNode = resultJsonNode.at("/blackList");
            if (blackListJsonNode.isArray()) {
                sb.append(": ").append(blackListJsonNode);
            }
            return Result.errorSystemMsg(sb.toString());
        }
    }

    @Override
    protected Result getSmsCodeRequest(JsonNode jsonNode) {
        String channel = jsonNode.get("channel").asText();

        ShanDongGetSmsCodeDTO shanDongGetSmsCodeDTO = new ShanDongGetSmsCodeDTO();
        shanDongGetSmsCodeDTO.setTelnum(jsonNode.get("mobile").asText());
        shanDongGetSmsCodeDTO.setCountCode(config.getProductMap().get(channel).get("productCode"));
        shanDongGetSmsCodeDTO.setLogTag(config.getProductMap().get(channel).get("productName"));
        String result = ShanDongYiDongHttpUtils.getSmsCode(shanDongGetSmsCodeDTO, buildExtraInfoMap(jsonNode));
        JsonNode resultJsonNode = JacksonUtils.readTree(result);
        if ("10000".equals(resultJsonNode.at("/code").asText())) {
            return Result.ok();
        } else {
            return Result.errorSystemMsg(resultJsonNode.at("/message").asText() + ": "
                    + resultJsonNode.at("/data/errorMessage").asText());
        }
    }

    @Override
    protected Result beforeOrderRequest(JsonNode jsonNode) {
        return Result.ok();
    }

    @Override
    protected Result orderRequest(JsonNode jsonNode) {
        String channel = jsonNode.get("channel").asText();

        // 1.调用下单接口
        ShanDongOrderDTO shanDongOrderDTO = new ShanDongOrderDTO();
        shanDongOrderDTO.setTelnum(jsonNode.get("mobile").asText());
        shanDongOrderDTO.setSmsCode(jsonNode.get("smsCode").asText());
        shanDongOrderDTO.setHandleUrl(jsonNode.get("source").asText());
        shanDongOrderDTO.setDeviceCode(jsonNode.get("deviceCode").asText());
        shanDongOrderDTO.setPlatformCode(jsonNode.get("platformCode").asText());
        shanDongOrderDTO.setCountCode(config.getProductMap().get(channel).get("productCode"));
        shanDongOrderDTO.setLogTag(config.getProductMap().get(channel).get("productName"));

        String result = ShanDongYiDongHttpUtils.order(shanDongOrderDTO, buildExtraInfoMap(jsonNode));
        JsonNode resultJsonNode = JacksonUtils.readTree(result);

        // 2.根据下单成功或失败修改订单状态
        Subscribe resultSubscribe = new Subscribe();
        resultSubscribe.setId(jsonNode.get("id").asText());
        resultSubscribe.setModifyTime(new Date());
        resultSubscribe.setResult(resultJsonNode.at("/message").asText());
        if ("10000".equals(resultJsonNode.at("/code").asText())) {
            resultSubscribe.setOpenTime(new Date());
            resultSubscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
            subscribeService.updateSubscribeDbAndEs(resultSubscribe);
            return Result.ok();
        } else {
            resultSubscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(resultSubscribe);
            return Result.error(resultJsonNode.at("/userMessage").asText());
        }
    }

    protected Map<String, Object> buildExtraInfoMap(JsonNode jsonNode) {
        Map<String, Object> extraInfoMap = new HashMap<>();
        extraInfoMap.put("手机号", jsonNode.get("mobile").asText());
        extraInfoMap.put("渠道号", jsonNode.get("channel").asText());
        return extraInfoMap;
    }
}
