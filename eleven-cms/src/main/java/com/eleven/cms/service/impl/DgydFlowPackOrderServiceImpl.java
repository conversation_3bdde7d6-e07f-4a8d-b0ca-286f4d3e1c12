package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.DgydFlowPackOrder;
import com.eleven.cms.mapper.DgydFlowPackOrderMapper;
import com.eleven.cms.service.IDgydFlowPackOrderService;
import com.eleven.cms.service.IHttpRequestService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * @Description: 东莞移动流量包订单
 * @Author: jeecg-boot
 * @Date:   2023-01-11
 * @Version: V1.0
 */
@Slf4j
@Service
public class DgydFlowPackOrderServiceImpl extends ServiceImpl<DgydFlowPackOrderMapper, DgydFlowPackOrder> implements IDgydFlowPackOrderService {
    public static final String SUCCESS_MSG = "订购成功";
    @Autowired
    IHttpRequestService httpRequestService;
    private static final String SEND_SMS_URL="http://vote.gdsanhe.cn/putNumber/order/sendOrderConfirmSms";
    private static final String SUBMIT_ORDER_URL="http://vote.gdsanhe.cn/putNumber/order/productOrderCheckRandom";
    private static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    //产品编码
    public static Map<String, String> productMap = new ImmutableMap.Builder<String, String>()
            .put("CW", "畅玩流量包")
            .put("CXXR", "超炫XR流量包")
            .put("KDX", "酷电秀流量包")
            .build();
    //订购类型
    public static Map<String, String> orderTypeMap = new ImmutableMap.Builder<String, String>()
            .put("1", "办理")
            .put("2", "取消")
            .put("3", "修改")
            .build();
    /**
     * 发送验证码
     */
    @Override
    public Result<?> sendSms(String mobile, String productCode) {
        ObjectNode dataRequest = mapper.createObjectNode();
        dataRequest.put("mobile", mobile);
        dataRequest.put("productCode", productCode);
        log.info("东莞移动流量包,发送验证码=>手机号:{},产品编码:{}",mobile,productCode);
        DgydFlowPackOrder order=new DgydFlowPackOrder();
        order.setMobile(mobile);
        order.setProductCode(productCode);
        order.setProductName(productMap.get(productCode));
        order.setCreateTime(new Date());
        String content=httpRequestService.implementHttpGetRequest(SEND_SMS_URL, dataRequest,"东莞移动流量包,发送验证码");
        try {
            ObjectNode dataNode = mapper.readValue(content, ObjectNode.class);
            if(!dataNode.has("data")){
                order.setRemark("东莞移动流量包,发送验证码,系统错误！");
                this.save(order);
                return Result.error("系统错误！",order.getId());
            }
            ObjectNode data = mapper.readValue(dataNode.at("/data").asText(), ObjectNode.class);
            String respcode= data.at("/respcode").asText();
            String respdesc= data.at("/respdesc").asText();
            String resptype= data.at("/resptype").asText();
            order.setRespCode(respcode);
            order.setRespDesc(respdesc);
            order.setRespType(resptype);
            if("成功".equals(respdesc)){
                order.setRemark("发送验证码成功");
                this.save(order);
                return Result.ok("发送验证码成功",order.getId());
            }
            order.setRemark(respdesc);
            this.save(order);
            return Result.error(respdesc,order.getId());
        } catch (Exception e) {
            log.error("东莞移动流量包,发送验证码,数据转换异常:",e);
            order.setRemark("东莞移动流量包,发送验证码,数据转换异常");
            this.save(order);
            return Result.error("请求错误！",order.getId());
        }
    }
    /**
     * 订购业务
     */
    @Override
    public Result<?> submitOrder(String mobile, String productCode, String code,String orderType,String id) {
        ObjectNode dataRequest = mapper.createObjectNode();
        dataRequest.put("mobile", mobile);
        dataRequest.put("productCode", productCode);
        dataRequest.put("code", code);
        dataRequest.put("orderType", orderType);
        log.info("东莞移动流量包,订购业务=>手机号:{},产品编码:{},验证码:{},订购类型:{}",code,productCode,code,orderType);
        String content=httpRequestService.implementHttpGetRequest(SUBMIT_ORDER_URL, dataRequest,"东莞移动流量包,订购业务");
        try {
            ObjectNode dataNode = mapper.readValue(content, ObjectNode.class);
            if(!dataNode.has("data")){
                this.lambdaUpdate().eq(DgydFlowPackOrder::getId,id)
                        .set(DgydFlowPackOrder::getOrderType,orderTypeMap.get(orderType))
                        .set(DgydFlowPackOrder::getRemark,"东莞移动流量包,订购业务,系统错误！")
                        .set(DgydFlowPackOrder::getModifyTime,new Date()).update();
                return Result.error("系统错误！");
            }
            ObjectNode data = mapper.readValue(dataNode.at("/data").asText(), ObjectNode.class);
            String respcode= data.at("/respcode").asText();
            String respdesc= data.at("/respdesc").asText();
            String resptype= data.at("/resptype").asText();
            if("成功".equals(respdesc)){
                this.lambdaUpdate().eq(DgydFlowPackOrder::getId,id)
                        .set(DgydFlowPackOrder::getOrderType,orderTypeMap.get(orderType))
                        .set(DgydFlowPackOrder::getRespCode,respcode)
                        .set(DgydFlowPackOrder::getRespDesc,respdesc)
                        .set(DgydFlowPackOrder::getRespType,resptype)
                        .set(DgydFlowPackOrder::getRemark,SUCCESS_MSG)
                        .set(DgydFlowPackOrder::getModifyTime,new Date()).update();
                return Result.ok(SUCCESS_MSG);
            }
            if("已开通".equals(respdesc)){
                this.lambdaUpdate().eq(DgydFlowPackOrder::getId,id)
                        .set(DgydFlowPackOrder::getOrderType,orderTypeMap.get(orderType))
                        .set(DgydFlowPackOrder::getRespCode,respcode)
                        .set(DgydFlowPackOrder::getRespDesc,respdesc)
                        .set(DgydFlowPackOrder::getRespType,resptype)
                        .set(DgydFlowPackOrder::getRemark,"已开通")
                        .set(DgydFlowPackOrder::getModifyTime,new Date()).update();
                return Result.ok("已开通");
            }
            this.lambdaUpdate().eq(DgydFlowPackOrder::getId,id)
                    .set(DgydFlowPackOrder::getOrderType,orderTypeMap.get(orderType))
                    .set(DgydFlowPackOrder::getRespCode,respcode)
                    .set(DgydFlowPackOrder::getRespDesc,respdesc)
                    .set(DgydFlowPackOrder::getRespType,resptype)
                    .set(DgydFlowPackOrder::getRemark,respdesc)
                    .set(DgydFlowPackOrder::getModifyTime,new Date()).update();
            return Result.error(respdesc);

        } catch (Exception e) {
            log.error("东莞移动流量包,订购业务,数据转换异常:",e);
            this.lambdaUpdate().eq(DgydFlowPackOrder::getId,id)
                    .set(DgydFlowPackOrder::getOrderType,orderTypeMap.get(orderType))
                    .set(DgydFlowPackOrder::getRemark,"东莞移动流量包,订购业务,数据转换异常！")
                    .set(DgydFlowPackOrder::getModifyTime,new Date()).update();
            return Result.error("请求错误！");
        }
    }

}
