package com.eleven.cms.service.impl;

import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.AliPayConfig;
import com.eleven.cms.entity.AlipayComplain;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.MiguVrbtPayOrder;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.MiguVrbtPayOrderMapper;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.MgVrbtPayDeductMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.RemoteResult;
import com.github.wxpay.sdk.WXPayConstants;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * @Description: cms_migu_vrbt_pay_order
 * @Author: jeecg-boot
 * @Date:   2022-12-30
 * @Version: V1.0
 */
@Service
@Slf4j
public class MiguVrbtPayOrderServiceImpl extends ServiceImpl<MiguVrbtPayOrderMapper, MiguVrbtPayOrder> implements IMiguVrbtPayOrderService {
    //未支付
    public static final int NOT_PAY_STATUS = -1;
    //支付失败
    public static final int FAIL_PAY_STATUS =0;
    //支付成功
    public static final int SUCCESS_PAY_STATUS =1;

    //退款成功
    public static final int SUCCESS_REFUND_STATUS =4;
    //退款失败
    public static final int FAIL_REFUND_STATUS =5;
    //退款中
    private static final int REFUND_PREPARE=6;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;
    @Autowired
    private IAlipayService alipayService;
    @Autowired
    private IAlipayComplainService alipayComplainService;
    @Override
    public MiguVrbtPayOrder queryNotPayOrder(String orderId){
        return this.lambdaQuery().eq(MiguVrbtPayOrder::getOrderNo,orderId).eq(MiguVrbtPayOrder::getOrderStatus,NOT_PAY_STATUS).orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
    }
    @Override
    public MiguVrbtPayOrder queryOrder(String orderId){
        return this.lambdaQuery().eq(MiguVrbtPayOrder::getOrderNo,orderId).eq(MiguVrbtPayOrder::getOrderStatus,SUCCESS_PAY_STATUS).orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
    }
    @Override
    public void wechatModifyPayStatus(String outTradeNo, String transactionId, String resultCode,String mobile){
        boolean isPaySucc = StringUtil.equals(resultCode, WXPayConstants.SUCCESS);
        Integer status=FAIL_PAY_STATUS;
        if(isPaySucc){
            status=SUCCESS_PAY_STATUS;
        }
        this.lambdaUpdate().eq(MiguVrbtPayOrder::getOrderNo, outTradeNo).eq(MiguVrbtPayOrder::getOrderStatus, NOT_PAY_STATUS).set(MiguVrbtPayOrder::getOrderStatus,status).set(MiguVrbtPayOrder::getOutTradeNo, transactionId).set(MiguVrbtPayOrder::getPayTime, new Date()).set(MiguVrbtPayOrder::getUpdateTime, new Date()).update();
        paySubNotify( mobile,outTradeNo,  isPaySucc);
        MiguVrbtPayOrder order=this.lambdaQuery().eq(MiguVrbtPayOrder::getOrderNo,outTradeNo).orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
        if(isPaySucc){
            openVrbtMonth(order);
        }

    }

    @Override
    public void aliPayModifyPayStatus(String outTradeNo, String transactionId, String resultCode,String mobile){
        boolean isPaySucc = AliPayConfig.AlipayTradeStatus.TRADE_SUCCESS.equals(resultCode);
        Integer status=FAIL_PAY_STATUS;
        if(isPaySucc){
            status=SUCCESS_PAY_STATUS;
        }
        this.lambdaUpdate().eq(MiguVrbtPayOrder::getOrderNo, outTradeNo).eq(MiguVrbtPayOrder::getOrderStatus, NOT_PAY_STATUS).set(MiguVrbtPayOrder::getOrderStatus,status).set(MiguVrbtPayOrder::getOutTradeNo, transactionId).set(MiguVrbtPayOrder::getPayTime, new Date()).set(MiguVrbtPayOrder::getUpdateTime, new Date()).update();
        paySubNotify( mobile,outTradeNo,  isPaySucc);
        MiguVrbtPayOrder order=this.lambdaQuery().eq(MiguVrbtPayOrder::getOrderNo,outTradeNo).orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
        if(isPaySucc){
            openVrbtMonth(order);
        }
    }

    /**
     * es订购记录同步更新
     * @param mobile
     * @param transId
     * @param isPaySucc
     */
    private void paySubNotify(String mobile ,String transId,boolean isPaySucc) {
        Subscribe subscribe = subscribeService.findByMobileAndIspOrderId(mobile, transId);
        //如果发现已包月,就更新状态,然后回调,并设置铃音
        int status = isPaySucc?BizConstant.SUBSCRIBE_STATUS_SUCCESS:BizConstant.SUBSCRIBE_STATUS_FAIL;
        String resMsg = isPaySucc?"支付成功":"支付失败";

        //更新状态
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setStatus(status);
        upd.setResult(resMsg);
        upd.setModifyTime(new Date());
        subscribeService.updateSubscribeDbAndEs(upd);
        //写入限量自增序列
        subscribeService.saveChannelLimit(subscribe);
        //信息流广告转化上报
        if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
            channelService.AdEffectFeedbackNew(subscribe, status);
        } else {
            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
        }
    }
    private void openVrbtMonth(MiguVrbtPayOrder order){
        miguApiService.vrbtZeroOrder(order.getMobile(),order.getChannel());
        //延迟30秒查询是否包月
        rabbitMQMsgSender.mgVrbtPayDeductMessage(MgVrbtPayDeductMessage.builder().id(order.getId()).mobile(order.getMobile()).build());
    }

    @Override
    public void miGuVrbtPayMQMsg(String id){
        MiguVrbtPayOrder order=this.lambdaQuery().eq(MiguVrbtPayOrder::getId,id).orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
        if(order==null){
            log.info("咪咕视频彩铃三方支付消息队列查询订单失败-id:{}",id);
            return;
        }
        RemoteResult remoteResultMonth= miguApiService.vrbtMonthStatusQuerys(order.getMobile(),order.getChannel());
        Integer monthStatus = remoteResultMonth.isVrbtMember()? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        this.lambdaUpdate().eq(MiguVrbtPayOrder::getId,id).set(MiguVrbtPayOrder::getMonthStatus,monthStatus).set(MiguVrbtPayOrder::getUpdateTime, new Date()).update();
        if(remoteResultMonth.isVrbtMember()){
            CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(order.getChannel());
            if (null == cmsCrackConfig) {
                log.info("咪咕视频彩铃三方支付消息队列=>手机号:{},渠道号:{},未配置的渠道号!",order.getMobile(),order.getChannel());
                return;
            }
            Map<String,String> smsMap= Maps.newHashMap();
            smsMap.put("payTime", DateUtil.getDateFormat(new Date(),DateUtil.FULL_TIME_SPLIT_PATTERN));
            smsModelService.sendSms(order.getMobile(),order.getChannel(),cmsCrackConfig.getServiceId(),BizConstant.BUSINESS_TYPE_OFFICIAL,smsMap);
            //开通主被叫功能
            RemoteResult  remoteResult=miguApiService.vrbtToneFreeMonthOrder(order.getMobile(),order.getChannel(),order.getCopyRightId(),order.getRingId(),String.valueOf(order.getRingType()));
            Integer subStatus=remoteResult.isOK()? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            this.lambdaUpdate().eq(MiguVrbtPayOrder::getId,id).set(MiguVrbtPayOrder::getSubStatus,subStatus).set(MiguVrbtPayOrder::getUpdateTime, new Date()).update();
        }
    }

    @Override
    public Result<?> aliPayRefund(String outTradeNo){
        String outRequestNo = IdWorker.get32UUID();
        MiguVrbtPayOrder orderPay=this.lambdaQuery()
                .eq(MiguVrbtPayOrder::getOrderNo, outTradeNo)
                .in(MiguVrbtPayOrder::getOrderStatus, SUCCESS_PAY_STATUS,FAIL_REFUND_STATUS)
                .orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        if(StringUtils.isBlank(orderPay.getAppId())){
            return Result.error("退款订单暂不支持！");
        }
        AlipayTradeRefundResponse response= alipayService.alipayRefund(orderPay.getOrderNo(),orderPay.getOrderAmount(),outRequestNo,orderPay.getAppId());
        String refundRemark="退款中！";
        if(response!=null){
            String fundChange=StringUtils.isNotBlank(response.getFundChange())?response.getFundChange():"Z";
            refundRemark=StringUtils.isNotBlank(response.getSubMsg())?response.getSubMsg()+":"+fundChange:response.getMsg()+":"+fundChange;
        }
        if(response.isSuccess() && response.getFundChange().equals("Y")){
            upadateRefundStatus(outTradeNo, orderPay.getOrderAmount(), outRequestNo, "退款成功！", SUCCESS_REFUND_STATUS);
            return Result.ok("退款成功！");
        }
        upadateRefundStatus(outTradeNo, orderPay.getOrderAmount(), outRequestNo, refundRemark, FAIL_REFUND_STATUS);
        return Result.error(refundRemark);
    }

    @Override
    public Result<?> aliPayQueryRefund(String outTradeNo){
        MiguVrbtPayOrder orderPay=this.lambdaQuery()
                .eq(MiguVrbtPayOrder::getOrderNo, outTradeNo)
                .in(MiguVrbtPayOrder::getOrderStatus, FAIL_REFUND_STATUS,REFUND_PREPARE)
                .orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在！");
        }
        AlipayTradeFastpayRefundQueryResponse response= alipayService.alipayQueryRefund(orderPay.getOrderNo(),orderPay.getRefundOrderNo(),orderPay.getAppId());
        String refundRemark="退款失败！";
        if(response!=null){
            refundRemark= StringUtils.isNotBlank(response.getSubMsg())?response.getSubMsg():response.getMsg();
        }
        if(response.isSuccess() && response.getRefundStatus().equals("REFUND_SUCCESS")){
            upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(),"退款成功！", SUCCESS_REFUND_STATUS);
            return Result.error("退款成功！");
        }
        upadateRefundStatus(outTradeNo, orderPay.getRefundAmount(), orderPay.getRefundOrderNo(),refundRemark, FAIL_REFUND_STATUS);
        return Result.error(refundRemark);
    }


    /**
     * 更新退款状态和备注
     * @param outTradeNo
     * @param refundAmount
     * @param refundOrderNo
     * @param refundRemark
     * @param refundStatus
     */
    private void upadateRefundStatus(String outTradeNo, String refundAmount, String refundOrderNo, String refundRemark, Integer refundStatus) {
        this.lambdaUpdate()
                .eq(MiguVrbtPayOrder::getOrderNo, outTradeNo)
                .set(MiguVrbtPayOrder::getOrderStatus, refundStatus)
                .set(MiguVrbtPayOrder::getRefundOrderNo, refundOrderNo)
                .set(MiguVrbtPayOrder::getRefundAmount, refundAmount)
                .set(MiguVrbtPayOrder::getRefundRemark, refundRemark)
                .set(MiguVrbtPayOrder::getRefundTime, new Date())
                .set(MiguVrbtPayOrder::getUpdateTime, new Date()).update();
        //更新投诉退款状态
        AlipayComplain alipayComplain=alipayComplainService.lambdaQuery().eq(AlipayComplain::getMerchantOrderNo, outTradeNo).orderByDesc(AlipayComplain::getCreateTime).last("limit 1").one();
        if(alipayComplain!=null){
            alipayComplainService.lambdaUpdate()
                    .eq(AlipayComplain::getMerchantOrderNo, outTradeNo)
                    .set(AlipayComplain::getRefundStatus, refundStatus)
                    .set(AlipayComplain::getUpdateTime, new Date()).update();
        }
    }


    @Override
    public Result<?> setRing(Subscribe subscribe){
        boolean isPay=this.lambdaQuery().eq(MiguVrbtPayOrder::getMobile,subscribe.getMobile()).eq(MiguVrbtPayOrder::getChannel,subscribe.getChannel()).eq(MiguVrbtPayOrder::getOrderStatus,SUCCESS_PAY_STATUS).count()>0;
        if(!isPay){
           return Result.error("未包月！");
        }
        RemoteResult remoteResultMonth= miguApiService.vrbtMonthStatusQuerys(subscribe.getMobile(),subscribe.getChannel());
        if(!remoteResultMonth.isVrbtMember()){
            return Result.error("未包月！");
        }
        //开通主被叫功能
        MiguVrbtPayOrder miguVrbtPayOrder=this.lambdaQuery().select(MiguVrbtPayOrder::getRingType).eq(MiguVrbtPayOrder::getMobile,subscribe.getMobile()).eq(MiguVrbtPayOrder::getChannel,subscribe.getChannel()).eq(MiguVrbtPayOrder::getOrderStatus,SUCCESS_PAY_STATUS).orderByDesc(MiguVrbtPayOrder::getCreateTime).last("limit 1").one();
        if(miguVrbtPayOrder!=null){
            RemoteResult  remoteResult=miguApiService.vrbtToneFreeMonthOrder(subscribe.getMobile(),subscribe.getChannel(),subscribe.getCopyrightId(),subscribe.getContentId(),String.valueOf(miguVrbtPayOrder.getRingType()));
            Integer subStatus=remoteResult.isOK()? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            this.lambdaUpdate().eq(MiguVrbtPayOrder::getId,miguVrbtPayOrder.getId()).set(MiguVrbtPayOrder::getSubStatus,subStatus).set(MiguVrbtPayOrder::getRingId,subscribe.getContentId()).set(MiguVrbtPayOrder::getCopyRightId,subscribe.getCopyrightId()).set(MiguVrbtPayOrder::getUpdateTime, new Date()).update();
            if(remoteResult.isOK()){
                return Result.ok("设置铃音成功！");
            }
            return Result.error("设置铃音失败！");
        }else{
            return Result.error("未包月！");
        }
    }
}
