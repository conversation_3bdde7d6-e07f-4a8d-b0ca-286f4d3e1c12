package com.eleven.cms.service.impl;

import com.eleven.cms.ad.HeTuFenShengChongQingProperties;
import com.eleven.cms.config.HeTuFenShengChongQingChannel;
import com.eleven.cms.entity.HuyuOrder;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.Sm2Util;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 14:24
 **/
@Slf4j
@Service
public class HeTuFenShengChongQingServiceImpl implements IHeTuFenShengChongQingService {
    private static final String LOG_TAG = "咪咕互娱分省重庆业务";
    //通知类型 1-订购成功
    public static final int SUB_SUCCESS = 1;
    //通知类型 2-退订成功
    public static final int UN_SUB_SUCCESS = 2;
    @Autowired
    private HeTuFenShengChongQingProperties heTuFenShengProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private Interceptor hetuFenShengChongQingIntercept;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private IHuyuOrderService huyuOrderService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().addInterceptor(hetuFenShengChongQingIntercept).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    }

    @Override
    public Result<?> sendSms(String mobile, String channel,String appName) {
        HeTuFenShengChongQingChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(channel);
        if(heTuFenShengChannel==null){
            log.warn("{}-获取验证码,渠道号未配置=>手机号:{},渠道号:{}",LOG_TAG,mobile,channel);
            return Result.error("渠道号未配置");
        }
        ObjectNode input = mapper.createObjectNode();
        input.put("chargeId", heTuFenShengChannel.getChargeId());
        input.put("propsId", heTuFenShengChannel.getPropsId());
        input.put("appName",   StringUtil.isNotBlank(appName)?appName:heTuFenShengChannel.getAppName());
        input.put("chargeUrl",heTuFenShengChannel.getSnapshotUrl());
        input.put("mobile", mobile);
        input.put("channel", channel);
        RequestBody body = RequestBody.create(JSON,input.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(heTuFenShengProperties.getSendSmsUrl()).newBuilder();
        log.info("{}-获取验证码,请求数据=>手机号:{},请求参数:{}",LOG_TAG,mobile,input);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            HeTuFenShengChongQingSendSmsResult heTuFenShengChongQingSendSmsResult = mapper.readValue(content, HeTuFenShengChongQingSendSmsResult.class);
            if(heTuFenShengChongQingSendSmsResult.isOK()){
                Result result= new Result();
                result.setMessage("获取验证码成功");
                result.setOrderId(heTuFenShengChongQingSendSmsResult.getResultData()!=null?heTuFenShengChongQingSendSmsResult.getResultData().getOrderId():"");
                result.setCode(CommonConstant.SC_OK_200);
                return result;
            }
//            return Result.error(heTuFenShengChongQingSendSmsResult.getMessage());


            Result results= new Result();
            results.setMessage(heTuFenShengChongQingSendSmsResult.getMessage());
            results.setReturnCode(heTuFenShengChongQingSendSmsResult.getReturnCode());
            results.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
            return results;
        } catch (IOException e) {
            log.error("{}-获取验证码,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        return Result.error("系统异常");
    }

    @Override
    public Result<?> submitOrder(String mobile,String code, String ispOrderNo,String channel,String appName) {
        HeTuFenShengChongQingChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(channel);
        if(heTuFenShengChannel==null){
            log.warn("{}-提交验证码,渠道号未配置=>手机号:{},渠道号:{}",LOG_TAG,mobile,channel);
            return Result.error("渠道号未配置");
        }
        ObjectNode input = mapper.createObjectNode();
        input.put("chargeId", heTuFenShengChannel.getChargeId());
        input.put("propsId", heTuFenShengChannel.getPropsId());
        input.put("orderId", ispOrderNo);
        input.put("msgCode", code);
        input.put("appName", StringUtil.isNotBlank(appName)?appName:heTuFenShengChannel.getAppName());
        input.put("chargeUrl",heTuFenShengChannel.getSnapshotUrl());
        input.put("mobile", mobile);
        input.put("channel", channel);
        RequestBody body = RequestBody.create(JSON,input.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(heTuFenShengProperties.getCreateOrderUrl()).newBuilder();
        log.info("{}-提交验证码,请求数据=>手机号:{},请求参数:{}",LOG_TAG,mobile,input);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            HeTuFenShengChongQingSubmitOrderResult result = mapper.readValue(content, HeTuFenShengChongQingSubmitOrderResult.class);
            if(result.isOK()){
                String orderId=result.getResultData()!=null?result.getResultData().getOrderId():ispOrderNo;
                return Result.ok(result.getMessage(),orderId);
            }
//            return Result.error(result.getMessage());
            Result results= new Result();
            results.setMessage(result.getMessage());
            results.setReturnCode(result.getReturnCode());
            results.setCode(CommonConstant.SC_INTERNAL_SERVER_ERROR_500);
            return results;
        } catch (IOException e) {
            log.error("{}-提交验证码,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        return Result.error("系统异常");
    }



    @Override
    public HeTuFenShengNotify huyuOrderChongQingNotify(String requestBody){
        HuYuChongQingOrderNotifyRequest orderNotifyRequest = null;
        try {
            orderNotifyRequest = mapper.readValue(requestBody, HuYuChongQingOrderNotifyRequest.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("互娱重庆分省订单通知异常-请求参数:{}",requestBody,e);
            return new HeTuFenShengNotify("060072","互娱重庆分省订单通知异常");
        }
        String orderId=orderNotifyRequest.getOrderId();
        String chargeId=orderNotifyRequest.getChargeId();
        String sign=orderNotifyRequest.getSign();
        int type=orderNotifyRequest.getType();
        String userCode=orderNotifyRequest.getUserCode();
        Map<String, List<String>> mapInversed =heTuFenShengProperties.getChannelMap().entrySet().stream().collect(Collectors.groupingBy(entry -> entry.getValue().getNotifyChargeId(), Collectors.mapping(Map.Entry::getKey, Collectors.toList())));
        if(!mapInversed.containsKey(chargeId)){
            log.error("互娱重庆分省订单通知异常-渠道号未配置-请求参数:{}",requestBody);
            return new HeTuFenShengNotify("060073","互娱重庆分省订单通知异常");
        }
        final String channel = mapInversed.get(chargeId).get(0);
        HeTuFenShengChongQingChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(channel);
        Sm2Util sm2Util=new Sm2Util(heTuFenShengChannel.getMobilePublicKey(),heTuFenShengChannel.getMobilePrivateKey());
        String mobile=sm2Util.decrypt(userCode);
        log.info("互娱重庆分省订单通知=>手机号:{},通知类型:{},计费点ID:{},订单号:{}",mobile, type,chargeId,orderId);
        try {
            String ourSign = DigestUtils.md5DigestAsHex(("MiGu" + orderId + chargeId + "notice").getBytes(StandardCharsets.UTF_8.name())).toUpperCase();
            if(!ourSign.equals(sign)){
                log.warn("{}-验证签名错误,通知数据=>手机号:{},通知类型:{},计费点ID:{},订单号:{},ourSign:{},sign:{}",LOG_TAG,mobile, type,chargeId,orderId,ourSign,sign);
                return new HeTuFenShengNotify("060071","签名校验失败");
            }
        } catch (Exception e) {
            log.error("{}-生成签名错误,通知数据=>手机号:{},通知类型:{},计费点ID:{},订单号:{},sign:{}",LOG_TAG,mobile, type,chargeId,orderId,sign,e);
            return new HeTuFenShengNotify("060071","签名校验失败");
        }
        List<Subscribe> subscribeList=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, BIZ_CHANNEL_HETU_CQ)
                .between(Subscribe::getCreateTime,LocalDateTime.now().minusHours(24),LocalDateTime.now())
                .in(Subscribe::getStatus,BizConstant.SUBSCRIBE_STATUS_SUCCESS,BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED)
                .orderByDesc(Subscribe::getCreateTime).list();
        if(subscribeList!=null && subscribeList.size()>0){
            boolean isSuccess=subscribeList.stream().anyMatch(subscribe->BizConstant.SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()));
            if(isSuccess){
                log.warn("互娱重庆分省订单通知,24小时已有开通成功订单忽略上报=>手机号:{},订单ID:{},计费ID:{},订购状态:{}",mobile,orderId,chargeId,type);
                Optional<Subscribe> subscribeOptional=subscribeList.stream().filter(subscribe->BizConstant.SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())).findFirst();
                if(subscribeOptional.isPresent()){
                    Subscribe subscribe=subscribeOptional.get();
                    if (UN_SUB_SUCCESS==type && SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())) {
                        HuyuOrder huyuOrder=new HuyuOrder();
                        /**手机号*/
                        huyuOrder.setMobile(mobile);
                        /**渠道号*/
                        huyuOrder.setChannel(subscribe.getChannel());
                        /**省份*/
                        huyuOrder.setProvince(subscribe.getProvince());
                        /**城市*/
                        huyuOrder.setCity(subscribe.getCity());
                        /**订单类型:1订购 2退订*/
                        huyuOrder.setOprType(2);
                        huyuOrderService.save(huyuOrder);
                    }
                }
            }else{
                Optional<Subscribe> subscribeOptional=subscribeList.stream().filter(subscribe->BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())).findFirst();
                if(subscribeOptional.isPresent()){
                    Subscribe subscribe=subscribeOptional.get();
                    Subscribe upd = new Subscribe();
                    upd.setId(subscribe.getId());
                    upd.setModifyTime(new Date());

                    if (SUB_SUCCESS==type && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                        //订阅成功
                        upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                        upd.setOpenTime(new Date());
                        subscribeService.updateSubscribeDbAndEs(upd);
                        subscribeService.saveChannelLimit(subscribe);


                        HuyuOrder huyuOrder=new HuyuOrder();
                        /**手机号*/
                        huyuOrder.setMobile(mobile);
                        /**渠道号*/
                        huyuOrder.setChannel(subscribe.getChannel());
                        /**省份*/
                        huyuOrder.setProvince(subscribe.getProvince());
                        /**城市*/
                        huyuOrder.setCity(subscribe.getCity());
                        /**订单类型:1订购 2退订*/
                        huyuOrder.setOprType(1);
                        huyuOrderService.save(huyuOrder);

                        //包月校验
                        rabbitMQMsgSender.addDelayedVerifyMessage(subscribe);
                        if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                        }else{
                            //外部渠道加入回调延迟队列(暂时不使用队列)
                            final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                            rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                        }
                    }
                }
            }
        }else{
            //查询重庆分省最新订购数据
            Subscribe subscribe= subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile)
                    .eq(Subscribe::getChannel, BIZ_CHANNEL_HETU_CQ)
                    .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS)
                    .orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(subscribe!=null && UN_SUB_SUCCESS==type && SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus())){
                HuyuOrder huyuOrder=new HuyuOrder();
                /**手机号*/
                huyuOrder.setMobile(mobile);
                /**渠道号*/
                huyuOrder.setChannel(subscribe.getChannel());
                /**省份*/
                huyuOrder.setProvince(subscribe.getProvince());
                /**城市*/
                huyuOrder.setCity(subscribe.getCity());
                /**订单类型:1订购 2退订*/
                huyuOrder.setOprType(2);
                huyuOrderService.save(huyuOrder);
            }
        }
        return new HeTuFenShengNotify("000000","处理成功");
    }
    @Override
    public boolean isMember(String mobile, String channel) {
        HuyuOrder huyuOrder=huyuOrderService.lambdaQuery().select(HuyuOrder::getOprType).eq(HuyuOrder::getMobile, mobile).eq(HuyuOrder::getChannel, channel).orderByDesc(HuyuOrder::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(huyuOrder==null){
            return false;
        }
        if(huyuOrder.getOprType().equals(2)){
            return false;
        }
        return true;
    }
}
