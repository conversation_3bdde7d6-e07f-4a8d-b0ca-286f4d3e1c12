package com.eleven.cms.service.impl;

import com.eleven.cms.ad.TianYiTongXunZhuLiProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.ITianyiCommAssistService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.TianyiCommAssistResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 天翼通讯助理业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 15:52
 **/
@Slf4j
@Service
public class TianyiCommAssistServiceImpl implements ITianyiCommAssistService {
    public static final String TIANYI_COMM_SUBSCRIBE_STATUS = "0"; //未知错误
    public static final String TIANYI_COMM_SUBSCRIBE_STATUS_SUB_SUCCESS = "1"; //开通成功
    public static final String TIANYI_COMM_SUBSCRIBE_STATUS_UN_SUB = "2"; //退订
    public static final String TIANYI_COMM_SUBSCRIBE_STATUS_SUB_FAIL  = "3";//开通失败
    public static final String TIANYI_COMM_SUBSCRIBE_STATUS_UN_SUB_FAIL  = "4";//退订失败

    public static final String PROVINCE_YN="云南";
    public static final String PROVINCE_SX="山西";

    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    TianYiTongXunZhuLiProperties tianYiTongXunZhuLiProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    BlackListService blackListService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    /**
     * 创建订单
     * @param mobile
     * @param province
     * @return
     */
    private TianyiCommAssistResult exposedInterface(String mobile,String province) {
        ObjectNode node =mapper.createObjectNode();
        node.put("phone",mobile);
        String business="";
        if(PROVINCE_SX.equals(province)){
            business=tianYiTongXunZhuLiProperties.getBusinessSx();
            node.put("business",business);
        }else{
            business=tianYiTongXunZhuLiProperties.getBusinessYn();
            node.put("business",business);
        }
        Map<String, String> header = Maps.newHashMap();
        String timestamp=DateUtil.formatFullTime(LocalDateTime.now());
        header.put("timestamp",timestamp);
        header.put("appKey", tianYiTongXunZhuLiProperties.getAppKey());

        StringBuffer sb = new StringBuffer();
        sb.append(business);
        sb.append("&");
        sb.append(mobile);
        sb.append("&");
        sb.append(timestamp);
        sb.append("&");
        sb.append(tianYiTongXunZhuLiProperties.getSecret());


        String sign = null;
        try {
            sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        header.put("sign", sign);
        String content =implementHttpPostResult(tianYiTongXunZhuLiProperties.getExposedInterfaceUrl(), node,"天翼通讯助理订单开通接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return null;
        }
        try {
            TianyiCommAssistResult result = mapper.readValue(content, TianyiCommAssistResult.class);
            return result;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 发送验证码
     * @param mobile
     * @param subscribe
     * @return
     */
    @Override
    public Result<?> sendMessage(String mobile,Subscribe subscribe) {

        TianyiCommAssistResult tianyiCommAssistResult=this.exposedInterface(mobile,subscribe.getProvince());
        if(tianyiCommAssistResult==null){
            return Result.error("系统繁忙,请稍后再试!",subscribe.getId());
        }
        if(!tianyiCommAssistResult.isOK()){
            return Result.error(tianyiCommAssistResult.getMsg(),subscribe.getId());
        }
        if(tianyiCommAssistResult.isOK()) {
            if(StringUtils.isBlank(tianyiCommAssistResult.getData().getOrderUrl())){
                return Result.error("系统繁忙,请稍后再试!",subscribe.getId());
            }
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setIspOrderNo(tianyiCommAssistResult.getData().getId());
            upd.setExtra(tianyiCommAssistResult.getData().getOrderUrl());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        ObjectNode node =mapper.createObjectNode();
        node.put("orderUrl",tianyiCommAssistResult.getData().getOrderUrl());

        Map<String, String> header = Maps.newHashMap();
        String timestamp=DateUtil.formatFullTime(LocalDateTime.now());
        header.put("timestamp",timestamp);
        header.put("appKey", tianYiTongXunZhuLiProperties.getAppKey());
        StringBuffer sb = new StringBuffer();
        sb.append(tianyiCommAssistResult.getData().getOrderUrl());
        sb.append("&");
        sb.append(timestamp);
        sb.append("&");
        sb.append(tianYiTongXunZhuLiProperties.getSecret());

        String sign = null;
        try {
            sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        header.put("sign", sign);
        String content =implementHttpPostResult(tianYiTongXunZhuLiProperties.getSendMessageUrl(), node,"天翼通讯助理短信发送接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return Result.error("短信发送失败",subscribe.getId());
        }
        try {
            TianyiCommAssistResult result = mapper.readValue(content, TianyiCommAssistResult.class);
            if(result.isOK()){
                return Result.ok("短信发送成功",subscribe.getId());
            }
            return Result.error(result.getMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("短信发送失败",subscribe.getId());
    }

    /**
     * 提交订单
     * @param mobile
     * @param orderUrl
     * @param verificationCode
     * @return
     */
    @Override
    public Result<?> submitOrder(String mobile,String orderUrl, String verificationCode) {
        ObjectNode node =mapper.createObjectNode();
        node.put("orderUrl",orderUrl);
        node.put("verificationCode",verificationCode);

        Map<String, String> header = Maps.newHashMap();
        String timestamp=DateUtil.formatFullTime(LocalDateTime.now());
        header.put("timestamp",timestamp);
        header.put("appKey", tianYiTongXunZhuLiProperties.getAppKey());


        StringBuffer sb = new StringBuffer();
        sb.append(orderUrl);
        sb.append("&");
        sb.append(verificationCode);
        sb.append("&");
        sb.append(timestamp);
        sb.append("&");
        sb.append(tianYiTongXunZhuLiProperties.getSecret());
        String sign = null;
        try {
            sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        header.put("sign", sign);
        String content=this.implementHttpPostResult(tianYiTongXunZhuLiProperties.getSubmitOrderUrl(), node,"天翼通讯助理订购提交接口",header,mobile);
        if(StringUtil.isEmpty(content)){
            return Result.error("200","系统异常");
        }
        try {
            TianyiCommAssistResult result = mapper.readValue(content, TianyiCommAssistResult.class);
            if(result.isOK()){
                return Result.ok("验证码提交成功");
            }
            return Result.error(Integer.valueOf(result.getCode()),result.getMsg());
        } catch (Exception e) {
            return Result.error("200","系统异常");
        }
    }

    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url, Object fromValue,String msg, Map<String, String> header,String mobile) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,header,mobile);
    }

    private String push(String url,String raw,String msg, Map<String, String> header,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},请求参数:{},响应参数:{}",mobile,url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,raw,e);
            return null;
        }
    }



    @Override
    public Map<String, Object> tianyiCommAssistNotify(String phone,String orderStatus,String orderId,Map<String, Object> map){

        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, phone)
                .eq(Subscribe::getChannel, BIZ_CHANNEL_TY_COMM)
                .eq(Subscribe::getIspOrderNo, orderId)
                .orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();

        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (TIANYI_COMM_SUBSCRIBE_STATUS_SUB_SUCCESS.equals(orderStatus) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
//                subscribeService.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
//                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
//                    subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), CHANNEL_OWNER_CPA);
//                } else {
//                    String owner = channelOwnerService.queryOwner(subscribe.getSubChannel());
//                    if (StringUtils.isNotBlank(owner)) {
//                        subscribeService.incrChannelProvinceOwnerLimit(subscribe.getChannel(), subscribe.getProvince(), owner);
//                    }
//                }
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else if (TIANYI_COMM_SUBSCRIBE_STATUS_SUB_FAIL.equals(orderStatus)){
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\"开通失败\"}";
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }else if(TIANYI_COMM_SUBSCRIBE_STATUS_UN_SUB.equals(orderStatus) && subscribe!=null){
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"退订\"}";
                upd.setResult(result);
                upd.setVerifyStatus(0);
                subscribeService.updateSubscribeDbAndEs(upd);
                //退订用户加入黑名单
                try {
                    blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }else if(TIANYI_COMM_SUBSCRIBE_STATUS_UN_SUB_FAIL.equals(orderStatus) && subscribe!=null){
                //退订失败
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"退订失败\"}";
                upd.setResult(result);
                subscribeService.updateSubscribeDbAndEs(upd);
                //退订用户加入黑名单
                try {
                    blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }else if(TIANYI_COMM_SUBSCRIBE_STATUS.equals(orderStatus)){
                //未知错误
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"未知错误\"}";
                upd.setResult(result);
                subscribeService.updateSubscribeDbAndEs(upd);
            }

        }
        map.put("code","200");
        map.put("msg","成功");
        return map;

    }
}
