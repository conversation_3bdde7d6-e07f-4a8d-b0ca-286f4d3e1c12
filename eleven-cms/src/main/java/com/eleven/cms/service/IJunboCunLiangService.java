package com.eleven.cms.service;

import com.eleven.cms.dto.JunboCunLiangNotify;
import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.Result;

import java.util.Map;

/**
 * 骏伯上海移动业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/13 15:11
 **/
public interface IJunboCunLiangService {

    Result<?> sendSms(Subscribe subscribe);

    Result<?> submitSms(Subscribe subscribe);

    Result<?> queryRights(Subscribe subscribe);

    Result<?> smsRights(Subscribe subscribe);

    Result<?> rightsOrder(Subscribe subscribe);

    String junboCunLiangNotify(JunboCunLiangNotify junboCunLiangNotify);






}
