package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.AppDictItem;
import com.eleven.cms.mapper.AppDictItemMapper;
import com.eleven.cms.service.IAppDictItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
@Service
public class AppDictItemServiceImpl extends ServiceImpl<AppDictItemMapper, AppDictItem> implements IAppDictItemService {

    @Autowired
    private AppDictItemMapper appDictItemMapper;

    @Override
    public List<AppDictItem> selectItemsByMainId(String mainId) {
        return appDictItemMapper.selectItemsByMainId(mainId);
    }
}
