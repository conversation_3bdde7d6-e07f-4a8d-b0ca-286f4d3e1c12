package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.KeyValueConfig;
import com.eleven.cms.mapper.KeyValueConfigMapper;
import com.eleven.cms.service.IKeyValueConfigService;
import com.eleven.cms.vo.FebsResponse;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @Description: cms_ali_channel_config
 * @Author: jeecg-boot
 * @Date:   2023-04-24
 * @Version: V1.0
 */
@Service
public class KeyValueConfigServiceImpl extends ServiceImpl<KeyValueConfigMapper, KeyValueConfig> implements IKeyValueConfigService {

    @Override
    @Cacheable(cacheNames = CacheConstant.KEY_VALUE_CONFIG_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    public FebsResponse getConfigByKey(String key) {
        FebsResponse febsResponse = new FebsResponse();
        if(StringUtils.isBlank(key)){
            return febsResponse.fail().message("key不能为空");
        }
        KeyValueConfig keyValueConfig = this.lambdaQuery().eq(KeyValueConfig::getConfigKey, key).one();
        return febsResponse.success().data(keyValueConfig);
    }
}
