package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.ColumnMusic;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.ColumnMusicVo;
import com.eleven.cms.entity.Music;
import com.eleven.cms.vo.MusicVo;

import java.util.List;

/**
 * @Description: 栏目歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
public interface IColumnMusicService extends IService<ColumnMusic> {

	public List<ColumnMusic> selectByMainId(String mainId);

    IPage<MusicVo> selectColumnMusicVoPage(Page<MusicVo> page, String cloumnId);

    List<ColumnMusicVo> selectByMainIdNew(String id);

    Music selectByCopyrightId(String copyrightId);

    void importMusic(List<ColumnMusicVo> list,String id);

    MusicVo selectRandomMusicByColumn();
}
