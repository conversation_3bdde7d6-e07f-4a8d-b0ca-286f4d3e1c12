package com.eleven.cms.service;

import com.eleven.cms.dto.MiGuKuaiYouVRJingMengGetUserResult;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.vo.FebsResponse;
import org.jeecg.common.api.vo.Result;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/5 14:45
 **/
public interface IMiGuKuaiYouVRJingMengService {
    Result<?> vrJingMengReadySub(Subscribe subscribe);

    FebsResponse vrJingMengGetUser(Subscribe subscribe);

    FebsResponse vrJingMengGetToken(String mobile, String channel);

    FebsResponse vrJingMengFee(String mobile,String token);

    MiGuKuaiYouVRJingMengGetUserResult miGuKuaiYouVRJingMengGetUser(String mobile, String token);

    FebsResponse vrJingMengGetUser(String mobile, String channelCode);


}
