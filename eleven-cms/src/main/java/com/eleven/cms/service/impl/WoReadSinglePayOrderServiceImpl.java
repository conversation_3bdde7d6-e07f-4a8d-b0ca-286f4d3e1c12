package com.eleven.cms.service.impl;

import com.eleven.cms.entity.OrderPay;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WoReadSinglePayOrder;
import com.eleven.cms.mapper.WoReadSinglePayOrderMapper;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IWoReadSinglePayOrderService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.MobileRegionResult;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.net.URLDecoder;

/**
 * @Description: cms_wo_read_single_pay_order
 * @Author: jeecg-boot
 * @Date:   2023-11-01
 * @Version: V1.0
 */
@Service
public class WoReadSinglePayOrderServiceImpl extends ServiceImpl<WoReadSinglePayOrderMapper, WoReadSinglePayOrder> implements IWoReadSinglePayOrderService {

    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private MobileRegionService mobileRegionService;

    @Override
    public void saveSinglePayOrder(Subscribe subscribe, String contractCode, Integer payType, String company) {

        WoReadSinglePayOrder woReadSinglePayOrder = new WoReadSinglePayOrder();
        try{
            MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
            if(StringUtils.isNotBlank(subscribe.getSource())){
                subscribe = parseLink(subscribe.getSource(),subscribe);
            }

            //保存订单信息
            woReadSinglePayOrder.setMobile(subscribe.getMobile());
            woReadSinglePayOrder.setChannel(subscribe.getChannel());
            woReadSinglePayOrder.setBusinessType(subscribe.getBusinessType());
            woReadSinglePayOrder.setSubChannel(subscribe.getSubChannel());
            woReadSinglePayOrder.setOrderNo(contractCode);
            woReadSinglePayOrder.setOutTradeNo(subscribe.getIspOrderNo());
            woReadSinglePayOrder.setPayType(payType);
            this.save(woReadSinglePayOrder);

            //保存sub信息
            subscribe.setProvince(mobileRegionResult!=null?mobileRegionResult.getProvince():"未知");
            subscribe.setCity(mobileRegionResult!=null?mobileRegionResult.getCity():"未知");
            String isp = mobileRegionResult!=null?mobileRegionResult.getOperator():"未知";
            subscribe.setIsp(isp);
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
            subscribe.setChannel(subscribe.getBusinessType());
            subscribe.setBizType(subscribe.getChannel());
            subscribe.setResult("未支付");
            subscribeService.createSubscribeDbAndEs(subscribe);
        } catch (Exception e) {
            log.error("联通沃悦读扣款写入sub订单出错：",e);
        }
    }

    private Subscribe parseLink(String source, Subscribe subscribe) {
        try{
            source = URLDecoder.decode(source, "UTF-8");
            String subChannel = HttpUrl.parse(source.replace("#/", "")).queryParameter("subChannel");
            subscribe.setSubChannel(subChannel);
            subscribe.setSource(source);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return subscribe;
    }
}
