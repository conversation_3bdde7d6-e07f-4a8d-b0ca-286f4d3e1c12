package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WyyMmOrder;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IWyyMmOrderService;
import com.eleven.cms.vo.WangyiyunMmCreateOrderResult;
import com.eleven.cms.vo.WangyiyunMmSmsCodeResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("wangyiMMCommonService")
@Slf4j
public class WangyiMMCommonServiceImpl implements IBizCommonService {

    @Autowired
    JiangsuYidongService jiangsuYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    YidongVRCaoWeiCrackService yidongVRCaoWeiCrackService;
    @Autowired
    WangyiyunMmService wangyiyunMmService;
    @Autowired
    IWyyMmOrderService wyyMmOrderService;
    @Autowired
    IChannelService channelService;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        //发送验证码网易云MM
        String appOrderId = "hr" + IdWorker.getIdStr();
        WangyiyunMmCreateOrderResult order = wangyiyunMmService.createOrder(subscribe.getMobile(), subscribe.getIp(),appOrderId);
        if (order.isOK()) {
            //保存订单信息
            WyyMmOrder wyyMmOrder = new WyyMmOrder();
            wyyMmOrder.setMobile(subscribe.getMobile());
            wyyMmOrder.setChannel(subscribe.getChannel());
            wyyMmOrder.setOrderId(order.getRes().getOrderId());
            wyyMmOrder.setAppOrderId(appOrderId);
            wyyMmOrder.setStatus(0);
            wyyMmOrder.setCreateTime(new Date());
            wyyMmOrderService.save(wyyMmOrder);
            subscribe.setIspOrderNo(order.getRes().getOrderId());
            subscribe.setResult("获取验证码成功");
            subscribeService.createSubscribeDbAndEs(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
            subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribe.setResult(order.getMsg());
            subscribeService.createSubscribeDbAndEs(subscribe);
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"status\":\""+order.getStatus()+"\",\"msg\":\""+order.getMsg()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        //开通网易云MM
        WangyiyunMmSmsCodeResult wangyiyunMmSmsCodeResult = wangyiyunMmService.smsCode(subscribe.getMobile(), smsCode, subscribe.getIspOrderNo());
        if (wangyiyunMmSmsCodeResult.isOK()) {
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setResult(wangyiyunMmSmsCodeResult.getRes().getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("提交验证码成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(wangyiyunMmSmsCodeResult.getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            //信息流广告转化上报
            channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_FAIL);
            return Result.error("开通失败");
        }
    }
}
