package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.OrderVrbt;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.OrderVrbtMapper;
import com.eleven.cms.queue.DelayedMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.IOrderVrbtService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_TAG_SUBSCRIBE_DELAY_1DAY_VRBT_ORDER;
import static com.eleven.cms.util.BizConstant.*;

/**
 * 移动包月后默认随机彩铃设置
 */
@Slf4j
@Service
public class OrderVrbtServiceImpl extends ServiceImpl<OrderVrbtMapper, OrderVrbt> implements IOrderVrbtService {

    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private LiantongVrbtService liantongVrbtService;
    @Autowired
    private DianxinVrbtService dianxinVrbtService;
    @Autowired
    private BizProperties bizProperties;
    @Autowired
    private IDatangSmsService datangSmsService;
    @Autowired
    private XunfeiJingxianVrbtService xunfeiJingxianVrbtService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Lazy
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    private SubscribeVerifyService subscribeVerifyService;


    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    private static final String ORDER_DEFAULT_VRBT_PERCENT_KEY = "order_default_vrbt_percent";
    public static final String FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG = "该渠道号未配置随机视频彩铃";
    public static final String FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL = "讯飞泾县视频彩铃获取铃音失败";
    private final ObjectMapper mapper = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder()
                //设置超时时间
                .connectTimeout(20L, TimeUnit.SECONDS)
                .readTimeout(30L, TimeUnit.SECONDS)
                .build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        //this.mapper = new ObjectMapper();
    }

    @Cacheable(cacheNames = CacheConstant.CMS_ORDER_VRBT_CACHE,key = "#root.methodName + ':' + #copyrightId", unless = "#result==null")
    @Override
    public String getContentIdByCopyrightId(String copyrightId) {
        if(StringUtils.isEmpty(copyrightId)){
            return null;
        }
        final OrderVrbt vrbt = this.lambdaQuery()
                .eq(OrderVrbt::getCopyrightId, copyrightId)
                .eq(OrderVrbt::getStatus, 1)
                .last(SQL_LIMIT_ONE)
                .one();
        return vrbt == null ? null : vrbt.getVrbtProductId();
    }

    /**
     * 开放平台(或者彩铃运营中心)延迟随机订一首视频彩铃
     *  注: 已改为全部设置
     * @param subscribe
     */
    @Async
    @Override
    public void orderByRandomDelay(Subscribe subscribe) {
        sleepMilliseconds(10*1000L);
        orderByRandom(subscribe);
    }

    /**
     * 开放平台(或者彩铃运营中心)随机订一首视频彩铃
     *  注: 已改为全部设置
     * @param subscribe
     */
    @Override
    public @Nonnull RemoteResult orderByRandom(Subscribe subscribe) {
        //没有设置视频彩铃的需要随机订一首默认的视频彩铃,90%的需要去订购免费视频彩铃
        //int orderDefaultVrbtPercent = this.getOrderDefaultVrbtPercent();
        //log.info("查询当前设置默认视频彩铃百分比为=>订单号:{},手机号:{},百分比:{}",subscribe.getId(),subscribe.getMobile(),orderDefaultVrbtPercent);
        //if(RandomUtils.createRandom(1,100) > orderDefaultVrbtPercent){
        //   return;
        //}
        //移动是指渠道包 订阅是指彩铃中心订阅包
        final String channelCode = subscribe.getChannel();
        final String isp = MiguApiService.isCentralityChannel(channelCode) ? MobileRegionResult.ISP_DINGYUE : MobileRegionResult.ISP_YIDONG;
        final OrderVrbt vrbt = this.lambdaQuery()
                .eq(OrderVrbt::getStatus, 1)
                .eq(OrderVrbt::getIsp, isp)
                .eq(/*MobileRegionResult.ISP_DINGYUE.equals(isp) || BizConstant.isBjhyZhbChannel(channelCode),*/OrderVrbt::getSingerName,channelCode)
                .last("ORDER BY RAND() LIMIT 1")
                .one();
        if (Objects.isNull(vrbt)) {
            log.error("随机订购一首视频彩铃失败=>订单号:{},手机号:{},渠道号:{},原因:{}", subscribe.getId(),subscribe.getMobile(), channelCode,FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, "empty",FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            return RemoteResult.fail(FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
        }
        final String channelCopyrightId = vrbt.getCopyrightId();
        final String ringName = vrbt.getMusicName();
        try {
            log.info("随机订购一首视频彩铃开始=>订单号:{},手机号:{},渠道号:{},版权id:{},ringName:{}", subscribe.getId(),subscribe.getMobile(), channelCode, channelCopyrightId,ringName);
            final RemoteResult remoteResult = vrbtToneFreeOrder(subscribe, channelCopyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE, "1");
            if (remoteResult.isOK()) {
                synchronized (this) {
                    this.plusOrderCount(vrbt.getId());
                }
            } else{
                //{"resCode":"999002","resMsg":"【OPEN】您请求的音源不存在"}这种失败就发短信提醒
                //{"ok":false,"resCode":"999028","resMsg":"【OPEN】非免费视频彩铃无法订购","status":"1"}
                if("999002".equals(remoteResult.getResCode())||"999028".equals(remoteResult.getResCode())) {
                    this.lambdaUpdate().eq(OrderVrbt::getId,vrbt.getId()).set(OrderVrbt::getListenProductId,"INVALID").update();
                    alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, channelCopyrightId,remoteResult.getResMsg());
                }
            }
            return remoteResult;
        } catch (Exception e) {
            log.info("随机订购一首视频彩铃异常=>订单号:{},手机号:{},渠道号:{},版权id:{},ringName:{}", subscribe.getId(),subscribe.getMobile(), channelCode, channelCopyrightId,ringName,e);
            return RemoteResult.fail("随机订购一首视频彩铃异常");
        }
    }

    /**
     * 渠道包(音乐包)随机订一首视频彩铃
     *  注: 已改为全部设置
     * @param subscribe
     */
    @Override
    public @Nonnull RemoteResult orderCpmbByRandom(Subscribe subscribe) {
        //移动是指渠道包 订阅是指彩铃中心订阅包
        final String channelCode = subscribe.getChannel();
        //final String isp = MiguApiService.isCentralityChannel(channelCode) ? MobileRegionResult.ISP_DINGYUE : MobileRegionResult.ISP_YIDONG;
        final OrderVrbt vrbt = this.lambdaQuery()
                .eq(OrderVrbt::getStatus, 1)
                .eq(OrderVrbt::getSingerName,BIZ_TYPE_CPMB)
                .last("ORDER BY RAND() LIMIT 1")
                .one();
        if (Objects.isNull(vrbt)) {
            log.error("渠道包随机订购一首视频彩铃失败=>订单号:{},手机号:{},渠道号:{},原因:{}", subscribe.getId(),subscribe.getMobile(), channelCode,FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, "empty",FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            return RemoteResult.fail(FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
        }
        final String copyrightId = vrbt.getCopyrightId();
        final String vrbtProductId = vrbt.getVrbtProductId();
        final String ringName = vrbt.getMusicName();
        try {
            log.info("渠道包随机订购一首视频彩铃开始=>订单号:{},手机号:{},渠道号:{},版权id:{},vrbtProductId:{},ringName:{}", subscribe.getId(),subscribe.getMobile(), channelCode, copyrightId,vrbtProductId,ringName);
            final RemoteResult remoteResult = activeVrbtFreeOrder(subscribe, copyrightId, vrbtProductId,"1");
            if (remoteResult.isOK()) {
                synchronized (this) {
                    this.plusOrderCount(vrbt.getId());
                }
            } else{
                //{"resCode":"999002","resMsg":"【OPEN】您请求的音源不存在"}这种失败就发短信提醒
                //{"ok":false,"resCode":"999028","resMsg":"【OPEN】非免费视频彩铃无法订购","status":"1"}
                if("999002".equals(remoteResult.getResCode())||"999028".equals(remoteResult.getResCode())) {
                    this.lambdaUpdate().eq(OrderVrbt::getId,vrbt.getId()).set(OrderVrbt::getListenProductId,"INVALID").update();
                    alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, copyrightId,remoteResult.getResMsg());
                }
            }
            return remoteResult;
        } catch (Exception e) {
            log.info("渠道包随机订购一首视频彩铃异常=>订单号:{},手机号:{},渠道号:{},版权id:{},vrbtProductId:{},ringName:{}", subscribe.getId(),subscribe.getMobile(), channelCode, copyrightId, vrbtProductId, ringName, e);
            return RemoteResult.fail("渠道包随机订购一首视频彩铃异常");
        }
    }





    /**
     * 三方支付diy 随机订一首视频彩铃
     * 注: 已改为全部设置
     * @param mobile
     */
    @Override
    public @Nonnull RemoteResult orderDiyByRandom(String mobile) {
        //没有设置视频彩铃的需要随机订一首默认的视频彩铃,90%的需要去订购免费视频彩铃
        //int orderDefaultVrbtPercent = this.getOrderDefaultVrbtPercent();
        //log.info("查询当前设置默认视频彩铃百分比为=>订单号:{},手机号:{},百分比:{}",subscribe.getId(),subscribe.getMobile(),orderDefaultVrbtPercent);
        //if(RandomUtils.createRandom(1,100) > orderDefaultVrbtPercent){
        //   return;
        //}
        //移动是指渠道包 订阅是指彩铃中心订阅包
        final String isp = MiguApiService.isCentralityChannel(BizConstant.RECHARGE_VRBT_CHANNEL_ID) ? MobileRegionResult.ISP_DINGYUE : MobileRegionResult.ISP_YIDONG;
        final OrderVrbt vrbt = this.lambdaQuery()
                .eq(OrderVrbt::getStatus, 1)
                .eq(OrderVrbt::getIsp, isp)
                .eq(OrderVrbt::getSingerName, BizConstant.RECHARGE_VRBT_CHANNEL_ID)
                .last("ORDER BY RAND() LIMIT 1")
                .one();
        if (Objects.isNull(vrbt)) {
            log.error("三方支付diy随机订购一首视频彩铃失败=>手机号:{},渠道号:{},原因:{}", mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID, FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            alertRandomVrbtToneFreeOrderFail(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID, "empty", FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            return RemoteResult.fail(FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
        }
        final String channelCopyrightId = vrbt.getCopyrightId();
        try {
            log.info("三方支付diy随机订购一首视频彩铃开始=>手机号:{},渠道号:{},版权id:{}", mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID, channelCopyrightId);

            RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID, channelCopyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
            if (remoteResult.isOK()) {
                synchronized (this) {
                    this.plusOrderCount(vrbt.getId());
                }
            } else{
                //{"resCode":"999002","resMsg":"【OPEN】您请求的音源不存在"}这种失败就发短信提醒
                //{"ok":false,"resCode":"999028","resMsg":"【OPEN】非免费视频彩铃无法订购","status":"1"}
                if("999002".equals(remoteResult.getResCode())||"999028".equals(remoteResult.getResCode())) {
                    this.lambdaUpdate().eq(OrderVrbt::getId,vrbt.getId()).set(OrderVrbt::getListenProductId,"INVALID").update();
                }
            }
            return remoteResult;
        } catch (Exception e) {
            log.info("三方支付diy随机订购一首视频彩铃异常=>手机号:{},渠道号:{},版权id:{}", mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID, channelCopyrightId,e);
            return RemoteResult.fail("随机订购一首视频彩铃异常");
        }
    }

    /**
     * 讯飞随机订一首视频彩铃
     * @param subscribe
     */
    @Override
    public @Nonnull RemoteResult orderByRandomXunfei(Subscribe subscribe) {
        final String channelCode = subscribe.getChannel();
        //通过讯飞提供的接口随机获取一首视频彩铃的版权id
        final String copyrightId = xunfeiJingxianVrbtService.getRandomVrbtRing();
        if (StringUtils.isBlank(copyrightId)) {
            log.error("随机订购一首视频彩铃失败=>订单号:{},手机号:{},渠道号:{},原因:{}", subscribe.getId(),subscribe.getMobile(), channelCode,FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL);
            alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, "empty",FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL);
            return RemoteResult.fail(FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL);
        }
        try {
            log.info("随机订购一首视频彩铃开始=>订单号:{},手机号:{},渠道号:{},版权id:{}", subscribe.getId(),subscribe.getMobile(), channelCode, copyrightId);
            final RemoteResult remoteResult = vrbtToneFreeOrder(subscribe, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE, "1");
            if (!remoteResult.isOK()) {
                //{"resCode":"999002","resMsg":"【OPEN】您请求的音源不存在"}这种失败就发短信提醒
                //{"ok":false,"resCode":"999028","resMsg":"【OPEN】非免费视频彩铃无法订购","status":"1"}
                if("999002".equals(remoteResult.getResCode())||"999028".equals(remoteResult.getResCode())) {
                    alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, copyrightId,remoteResult.getResMsg());
                }
            }
            return remoteResult;
        } catch (Exception e) {
            log.info("随机订购一首视频彩铃异常=>订单号:{},手机号:{},渠道号:{},版权id:{}", subscribe.getId(),subscribe.getMobile(), channelCode, copyrightId,e);
            return RemoteResult.fail("随机订购一首视频彩铃异常");
        }
    }

    @Override
    public void orderByRandomLiantong(Subscribe subscribe) {
        final OrderVrbt vrbt = this.lambdaQuery()
                                   .eq(OrderVrbt::getStatus, 1)
                                   .eq(OrderVrbt::getIsp, MobileRegionResult.ISP_LIANTONG)
                                   .eq(OrderVrbt::getCopyrightId, subscribe.getServiceId())
                                   .last("ORDER BY RAND() LIMIT 1")
                                   .one();
        if (Objects.isNull(vrbt)) {
            return;
        }
        log.info("联通随机订购一首视频彩铃开始=>订单号:{},手机号:{},视频彩铃产品id:{}", subscribe.getId(), subscribe.getMobile(), vrbt.getVrbtProductId());

        final LiantongResp liantongResp = liantongVrbtService.settingRingOnePointMon(subscribe.getMobile(), vrbt.getVrbtProductId(),subscribe.getVrbtLtChannel());
        try {
            String extra = mapper.writeValueAsString(liantongResp);
            log.info("联通随机订购一首视频彩铃结果=>订单号:{},手机号:{},视频彩铃产品id:{},结果:{}", subscribe.getId(), subscribe.getMobile(),
                    vrbt.getVrbtProductId(), extra);
            final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
//            subscribeService.lambdaUpdate()
//                            .eq(Subscribe::getId, subscribeId)
//                            .set(Subscribe::getExtra, extra)
//                            .set(Subscribe::getLtRingId, vrbt.getVrbtProductId())
//                            .update();


            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setExtra(extra);
            upd.setLtRingId(vrbt.getVrbtProductId());
            subscribeService.updateSubscribeDbAndEs(upd);


            if (liantongResp.isOK()) {
                synchronized (this) {
                    this.plusOrderCount(vrbt.getId());
                }
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void orderByRandomDianxin(String subscribeId, String mobile, String company) {
        final OrderVrbt vrbt = this.lambdaQuery()
                                   .eq(OrderVrbt::getStatus, 1)
                                   .eq(OrderVrbt::getIsp, MobileRegionResult.ISP_DIANXIN)
                                   .eq(OrderVrbt::getSingerName,company)
                                   .last("ORDER BY RAND() LIMIT 1")
                                   .one();
        if (Objects.isNull(vrbt)) {
            return;
        }
        log.info("电信随机订购一首视频彩铃开始=>订单号:{},手机号:{},视频彩铃产品id:{}", subscribeId, mobile, vrbt.getVrbtProductId());
        final DianxinResp dianxinResp = dianxinVrbtService.addToneFreeOnProduct(mobile, vrbt.getVrbtProductId(),company);
        try {
            String extra = mapper.writeValueAsString(dianxinResp);
            log.info("电信随机订购一首视频彩铃结果=>订单号:{},手机号:{},视频彩铃产品id:{},结果:{}", subscribeId, mobile, vrbt.getVrbtProductId(), extra);
            
            final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
            Subscribe upd = new Subscribe();
            upd.setId(subscribeId);
            upd.setExtra(extra);
            upd.setDxToneCode(vrbt.getVrbtProductId());
            subscribeService.updateSubscribeDbAndEs(upd);

            if (dianxinResp.isOK()) {
                synchronized (this) {
                    this.plusOrderCount(vrbt.getId());
                }
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 视频彩铃包月内内容免费订购(延迟60秒后订购)
     *
     * @param mobile
     */
    @Async
    @Override
    public void vrbtToneFreeOrderDelay(String mobile, String channelCode, String copyRightID, String setFlag) {

        try {
            TimeUnit.SECONDS.sleep(30L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        final RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyRightID, setFlag);
        try {
            final ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);
            final Subscribe targetSubscibe = subscribeService.findByMobileAndCopyrightId(mobile, copyRightID);
            subscribeService.updateToneOrderExtra(targetSubscibe, copyRightID, remoteResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Async
    @Override
    public void vrbtToneFreeOrderDelay(Subscribe subscribe, String channelCopyrightId, String setFlag) {
        try {
            TimeUnit.SECONDS.sleep(30L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        vrbtToneFreeOrder(subscribe, channelCopyrightId, setFlag, "0");
    }

    /**
     * 被叫视频彩铃订购
     * @param subscribe
     * @param channelCopyrightId
     * @param randomFlag
     * @return
     */
    @Override
    public @Nonnull RemoteResult vrbtToneFreeOrder(Subscribe subscribe, String channelCopyrightId, String setFlag, String randomFlag) {
        if(subscribe==null|| StringUtils.isEmpty(channelCopyrightId)){
            return RemoteResult.fail("参数错误");
        }
        String mobile = subscribe.getMobile();
        String channelCode = subscribe.getChannel();
        try {
            final RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, channelCopyrightId, setFlag);
            remoteResult.setStatus(randomFlag);
            SpringContextUtils.getBean(ISubscribeService.class).updateToneOrderExtra(subscribe, channelCopyrightId, remoteResult);
            return remoteResult;
        } catch (Exception e) {
            log.warn("设置视频彩铃异常=>订单号:{},手机号:{},渠道号:{},版权id:{},异常!", subscribe.getId(), mobile, channelCode, channelCopyrightId,e);
            return RemoteResult.fail("设置视频彩铃异常");
        }
    }

    /**
     * 主叫视频彩铃订购
     * @param subscribe
     * @param copyrightId
     * @param vrbtProductId
     * @param randomFlag
     * @return
     */
    @Override
    public @Nonnull RemoteResult activeVrbtFreeOrder(Subscribe subscribe, String copyrightId, String vrbtProductId,
                                                     String randomFlag) {
        if(subscribe==null|| StringUtils.isEmpty(copyrightId)||StringUtils.isEmpty(vrbtProductId)){
            return RemoteResult.fail("参数错误");
        }
        String mobile = subscribe.getMobile();
        String channelCode = subscribe.getChannel();
        try {
            final RemoteResult remoteResult = miguApiService.activeVrbtFreeOrder(mobile, channelCode, vrbtProductId, "1", "1");
            remoteResult.setStatus(randomFlag);
            SpringContextUtils.getBean(ISubscribeService.class).updateToneOrderExtra(subscribe, copyrightId, remoteResult);
            return remoteResult;
        } catch (Exception e) {
            log.warn("设置主叫视频彩铃异常=>订单号:{},手机号:{},渠道号:{},版权id:{},vrbtProductId:{},异常!", subscribe.getId(), mobile, channelCode, copyrightId, vrbtProductId, e);
            return RemoteResult.fail("设置主叫视频彩铃异常");
        }
    }


    @Override
    public int getOrderDefaultVrbtPercent() {
        final Object percent = redisUtil.get(ORDER_DEFAULT_VRBT_PERCENT_KEY);
        if (percent == null) {
            return 100;
        }
        try {
            return (Integer) percent;
        } catch (Exception e) {
            try {
                return Integer.parseInt((String) percent);
            } catch (Exception ex) {
            }
        }

        return 100;
    }

    /**
     * 随机设置铃音失败告警
     * @param mobile
     * @param channelCode
     * @param channelCopyrightId
     * @param failMsg
     */
    @Override
    public void alertRandomVrbtToneFreeOrderFail(String mobile, String channelCode, String channelCopyrightId, String failMsg) {
        log.info("发送随机免费订视频铃音失败告警,手机号:{},渠道号:{},版权Id:{},失败消息:{}",mobile,channelCode,channelCopyrightId,failMsg);
        final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
        String redisKey = "subscribe:randomVrbtToneFreeOrderFail:"+channelCode+":"+channelCopyrightId;
        if(redisUtil.hasKey(redisKey)){
            return;
        }
        final String msgContent = bizProperties.getRandomVrbtToneFreeOrderFailNotifySms() + String.format(",渠道号:%s,版权Id:%s,失败消息:%s", channelCode, channelCopyrightId,failMsg);
        bizProperties.getRandomVrbtToneFreeOrderFailNotifyPhoneNumberList().forEach(phoneNumber->{
            datangSmsService.sendSms(phoneNumber, msgContent);
        });
        redisUtil.set(redisKey, "INVALID", expire);
    }

    @Override
    public void setOrderDefaultVrbtPercent(Integer percent) {
        redisUtil.set(ORDER_DEFAULT_VRBT_PERCENT_KEY, percent);
    }

    @Override
    public void plusOrderCount(String orderVrbtId) {
        this.baseMapper.plusOrderCount(orderVrbtId);
    }

    private void sleepMilliseconds(long timeout){
        try {
            TimeUnit.MILLISECONDS.sleep(timeout);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public RemoteResult orderByRandomXunfeiWithRetry(Subscribe subscribe) {
        RemoteResult remoteResult = orderByRandomXunfei(subscribe);
        //设置失败重试一次
        if(!remoteResult.isOK()){
            sleepMilliseconds(500L);
            remoteResult = orderByRandomXunfei(subscribe);
        }
        return remoteResult;
    }

    @Override
    public RemoteResult orderByRandomWithRetry(Subscribe subscribe) {
        //炫视视频彩铃分省合作,切换到咪咕音乐渠道号订购铃音
        if(XUANSHI_RPOVINCE_CHANNEL_LIST.contains(subscribe.getChannel())){
            subscribe.setChannel(MiguApiService.CENTRALITY_CHANNEL_CODE_DEFAULT);
        }
        RemoteResult remoteResult = orderByRandom(subscribe);
        //不是因为未配置铃音的原因导致的失败重试一次
        if (!remoteResult.isOK() && !FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG.equals(remoteResult.getResMsg())) {
            sleepMilliseconds(500L);
            remoteResult = orderByRandom(subscribe);
        }
        return remoteResult;
    }

    /**
     * 视宣号铃音订购
     * @param subscribe
     * @return
     */
    private RemoteResult orderByRandomSxh(Subscribe subscribe) {
        //移动是指渠道包 订阅是指彩铃中心订阅包
        final String channelCode = subscribe.getChannel();
        String mobile = subscribe.getMobile();
        final OrderVrbt vrbt = this.lambdaQuery()
                .eq(OrderVrbt::getStatus, 1)
                .eq(OrderVrbt::getSingerName,channelCode)
                .last("ORDER BY RAND() LIMIT 1")
                .one();
        if (Objects.isNull(vrbt)) {
            log.error("随机订购一首视频彩铃失败=>订单号:{},手机号:{},渠道号:{},原因:{}", subscribe.getId(),subscribe.getMobile(), channelCode,FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, "empty",FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            return RemoteResult.fail(FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
        }
        final String ringId = vrbt.getVrbtProductId();
        final String ringName = vrbt.getMusicName();
        try {
            log.info("随机订购一首视频彩铃开始=>订单号:{},手机号:{},渠道号:{},ringId:{},ringName:{}", subscribe.getId(),subscribe.getMobile(), channelCode, ringId, ringName);
            final RemoteResult remoteResult = miguApiService.sxhRingSet(mobile, channelCode, ringId);
            remoteResult.setStatus("1");
            SpringContextUtils.getBean(ISubscribeService.class).updateToneOrderExtra(subscribe, ringId, remoteResult);
            if (remoteResult.isOK()) {
                synchronized (this) {
                    this.plusOrderCount(vrbt.getId());
                }
            } else{
                //{"resCode":"999002","resMsg":"【OPEN】您请求的音源不存在"}这种失败就发短信提醒
                //{"ok":false,"resCode":"999028","resMsg":"【OPEN】非免费视频彩铃无法订购","status":"1"}
                if("999002".equals(remoteResult.getResCode())||"999028".equals(remoteResult.getResCode())) {
                    this.lambdaUpdate().eq(OrderVrbt::getId,vrbt.getId()).set(OrderVrbt::getListenProductId,"INVALID").update();
                    alertRandomVrbtToneFreeOrderFail(subscribe.getMobile(),channelCode, ringId,remoteResult.getResMsg());
                }
            }
            return remoteResult;
        } catch (Exception e) {
            log.info("随机订购一首视频彩铃异常=>订单号:{},手机号:{},渠道号:{},ringId:{},ringName:{}", subscribe.getId(),subscribe.getMobile(), channelCode, ringId, ringName, e);
            return RemoteResult.fail("随机订购一首视频彩铃异常");
        }
    }

    private RemoteResult vrbtToneFreeOrderWithRetry(String channelCopyrightId, Subscribe subscribe) {
        RemoteResult remoteResult = vrbtToneFreeOrder(subscribe, channelCopyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_BEIJIAO, "0");
        //失败重试一次
        if(!remoteResult.isOK()) {
            sleepMilliseconds(500L);
            remoteResult = vrbtToneFreeOrder(subscribe, channelCopyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_BEIJIAO, "0");
        }
        return remoteResult;
    }

    /**
     * 处理消息队列的铃音延迟订购
     * @param delayedMessage
     */
    @Override
    public void handleMqOrder(DelayedMessage delayedMessage) {
        final String delayedMessageTag = delayedMessage.getTag();
        final String channelCopyrightId = delayedMessage.getExtra();
        final Subscribe subscribe = subscribeService.getById(delayedMessage.getId());
        if (null == subscribe) {
            log.warn("视频彩铃延迟订购消息目标订单不存在=>订单号:{},delayedMessageTag:{},channelCopyrightId:{}", delayedMessage.getId(), delayedMessageTag, channelCopyrightId);
            return;
        }
        final String channelCode = subscribe.getChannel();
        //如果用户选择了要订购的视频彩铃就直接订购,否则按比例随机订购一首默认彩铃
        if(StringUtils.isNotEmpty(channelCopyrightId)){
            vrbtToneFreeOrderWithRetry(channelCopyrightId, subscribe);
            return;
        }

        //讯飞的视频彩铃业务通过讯飞的接口随机获取铃音来设置
        if(miguApiService.isXunfeiChannelCode(channelCode) && BizConstant.BIZ_TYPE_VRBT.equals(subscribe.getBizType())){
            RemoteResult remoteResult = orderByRandomXunfeiWithRetry(subscribe);
            //如果重试任然失败且是10秒延迟订购的,24小时后再试一次
            if(!remoteResult.isOK() && RedisDelayedQueueManager.MESSAG_TAG_SUBSCRIBE_DELAY_10S_VRBT_ORDER.equals(delayedMessageTag)){
                rabbitMQMsgSender.sendSubscribeVrbtOrderDelay1dayMessage(DelayedMessage.builder().tag(MESSAG_TAG_SUBSCRIBE_DELAY_1DAY_VRBT_ORDER).id(subscribe.getId()).msg("延迟1天视频彩铃订购").build());
            }
            return;
        }
        //视宣号铃音随机订购(讯飞的也走这里订购)
        if(BizConstant.BIZ_TYPE_SXH.equals(subscribe.getBizType())){
            orderByRandomSxh(subscribe);
            return;
        }
        //音乐包主叫视频彩铃随机订购
        if(BIZ_TYPE_CPMB.equals(subscribe.getBizType())){
            orderCpmbByRandom(subscribe);
            return;
        }

        //视频彩铃、组合包(含讯飞的)、视彩号铃音订购
        orderByRandomWithRetry(subscribe);
    }


    @Override
    public Result<?> schRingPublish(String mobile, String channelCode){
        //final RemoteResult remoteResult = miguApiService.miguLoginWithoutCache(mobile, channelCode);
        final RemoteResult remoteResult = miguApiService.miguLogin(mobile, channelCode);
        if(!remoteResult.isOK()){
            return Result.error("咪咕登录失败");
        }
        final String token = remoteResult.getToken();
        final RemoteResult schMonthResult = miguApiService.bjhyAndCpmbQuery(token, channelCode, MiguApiService.MONTH_SERVICE_TYPE_BJHY);
        if(!schMonthResult.isBjhyMember()){
            log.info("视彩号铃音发布-用户业务未订购,phone:{},channelCode:{},token:{}",mobile,channelCode,token);
            return Result.error("用户业务未订购");
        }
        final String schPublish = "SCH_PUBLISH";
        final OrderVrbt vrbt = this.lambdaQuery()
                .eq(OrderVrbt::getSingerName, schPublish)
                .eq(OrderVrbt::getStatus, 1)
                .last("ORDER BY RAND() LIMIT 1")
                .one();

        //https://y.migu.cn/app/v5/p/publish-mid/index.html?videoUrl=&videoCover=&token=&tokentype=MGPT
        //String miguRingPublishUrl = "https://y.migu.cn/app/v5/p/publish-mid/index.html";
        //String miguRingPublishUrlWithParam = HttpUrl.parse(miguRingPublishUrl)
        //        .newBuilder()
        //        .addQueryParameter("token",remoteResult.getToken())
        //        .addQueryParameter("tokentype","MGPT")
        //        .addQueryParameter("videoUrl",vrbt.getVrbtProductId())
        //        .addQueryParameter("videoCover",vrbt.getCopyrightId())
        //        .build()
        //        .toString();
        RequestBody formBody = new FormBody.Builder()
                .add("token", token)
                .add("video_url",vrbt.getVrbtProductId())
                .add("coverUrl",vrbt.getCopyrightId())
                .add("videoName", IdWorker.get32UUID())
                .add("phone",mobile)
                .add("chcode",channelCode)
                .build();
        String ringPublishCrackUrl = "http://101.206.107.237:15679/fabu.php";
        log.info("视彩号铃音发布请求,phone:{},channelCode:{},token:{}",mobile,channelCode,token);
        Request request = new Request.Builder().url(ringPublishCrackUrl)
                .post(formBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            //{"code":"000000","info":"发布成功"}
            //{"code":"111111","info":"参数不完整"}
            String bodyString = response.body().string();
            log.info("视彩号铃音发布响应,phone:{},channelCode:{},token:{},bodyString:{}",mobile,channelCode,token,bodyString);
            if(bodyString.contains("\"000000\"")){
                return  Result.ok();
            } else {
                return  Result.error("铃音发布失败");
            }

        } catch (Exception e) {
            log.info("视彩号铃音发布异常,phone:{},channelCode:{},token:{}",mobile,channelCode,token,e);
            return Result.error(e.getMessage());
        }



    }

}
