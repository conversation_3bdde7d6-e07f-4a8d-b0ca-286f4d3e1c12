package com.eleven.cms.service.impl;

import com.eleven.cms.entity.SpclPayLog;
import com.eleven.cms.mapper.SpclPayLogMapper;
import com.eleven.cms.service.ISpclPayLogService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: cms_spcl_pay_log
 * @Author: jeecg-boot
 * @Date:   2022-04-27
 * @Version: V1.0
 */
@Service
public class SpclPayLogServiceImpl extends ServiceImpl<SpclPayLogMapper, SpclPayLog> implements ISpclPayLogService {

    @Override
    public SpclPayLog findByOutTradeNo(String outTradeNo) {
        return this.lambdaQuery().eq(SpclPayLog::getOutTradeNo,outTradeNo).one();
    }
}
