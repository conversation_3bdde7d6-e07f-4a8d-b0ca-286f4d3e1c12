package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PortCrackConfig;
import com.eleven.cms.mapper.PortCrackConfigMapper;
import com.eleven.cms.service.IPortCrackConfigService;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import static org.jeecg.common.constant.CacheConstant.PORT_CACHE_CHANNEL_CRACK_CONFIG;
import static org.jeecg.common.constant.CacheConstant.PORT_CACHE_COMPANYNAME_CRACK_CONFIG;
/**
 * @Description: cms_crack_config
 * @Author: jeecg-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Service
public class PortCrackConfigServiceImpl extends ServiceImpl<PortCrackConfigMapper, PortCrackConfig> implements IPortCrackConfigService {
    @Autowired
    IPortCrackConfigService configService;



    @Override
    @Cacheable(cacheNames = PORT_CACHE_CHANNEL_CRACK_CONFIG, key = "#p0", condition = "#p0!=null", unless = "#result==null")
    public PortCrackConfig getCrackConfigByChannel(String channel) {
        return lambdaQuery().eq(PortCrackConfig::getChannel, channel).eq(PortCrackConfig::getIsValid, 1).orderByDesc(PortCrackConfig::getCreateTime).last("limit 1").one();
    }

    @Override
    @Cacheable(cacheNames = PORT_CACHE_COMPANYNAME_CRACK_CONFIG, key = "#p0", condition = "#p0!=null", unless = "#result==null")
    public PortCrackConfig getCrackConfigByCompanyName(String companyName) {
        return lambdaQuery().eq(PortCrackConfig::getCompanyName, companyName).eq(PortCrackConfig::getIsValid, 1).orderByDesc(PortCrackConfig::getCreateTime).last("limit 1").one();
    }

    @Override
    @CacheEvict(cacheNames = PORT_CACHE_CHANNEL_CRACK_CONFIG, key = "#p0.channel")
    public void updateCrackConfig(PortCrackConfig portCrackConfig) {
        this.updateById(portCrackConfig);
    }


    @Override
    public PortCrackConfig getCrackConfig(String channel,String companyName) {
        if(StringUtils.isNotBlank(channel)){
            PortCrackConfig crackConfig=configService.getCrackConfigByChannel(channel);
            if(crackConfig!=null){
                return crackConfig;
            }
            return configService.getCrackConfigByCompanyName(companyName);
        }
        return configService.getCrackConfigByCompanyName(companyName);
    }
}
