package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.entity.VrbtDiyRing;
import com.eleven.cms.entity.VrbtDiyVideo;
import com.eleven.cms.mapper.VrbtDiyRingMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.VrbtDiyService;
import com.eleven.cms.service.IVrbtDiyRingService;
import com.eleven.cms.service.IVrbtDiyVideoService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.entity.BackgroundMusic;
import com.eleven.qycl.entity.QyclRing;
import com.eleven.qycl.service.AliMediaService;
import com.eleven.qycl.util.QyclConstant;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: cms_vrbt_diy_ring
 * @Author: jeecg-boot
 * @Date: 2023-07-25
 * @Version: V1.0
 */
@Service
@Slf4j
public class VrbtDiyRingServiceImpl extends ServiceImpl<VrbtDiyRingMapper, VrbtDiyRing> implements IVrbtDiyRingService {

    private String regionId;
    private String bucket;

    @Autowired
    AliMediaService aliMediaService;
    @Autowired
    AliMediaProperties aliMediaProperties;
    @Autowired
    IVrbtDiyVideoService vrbtDiyVideoService;
    @Autowired
    private VrbtDiyService vrbtDiyService;
    @Autowired
    VrbtDiyRingMapper vrbtDiyRingMapper;
    @Autowired
    MiguApiService miguApiService;

    @PostConstruct
    public void init() {
        regionId = aliMediaProperties.getRegionId();
        bucket = aliMediaProperties.getBucketName();
    }

    @Override
    public Map<String, String> createDiyRing(String mobile, String[] imageArray, BackgroundMusic backgroundMusic, String subChannel) {
        VrbtDiyRing vrbtDiyRing = new VrbtDiyRing();
        Map<String, String> map = new HashMap<>();
        vrbtDiyRing.setMobile(mobile);
        vrbtDiyRing.setImageUrls(StringUtils.join(imageArray, ","));
        vrbtDiyRing.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
        vrbtDiyRing.setSubChannel(subChannel);
        vrbtDiyRing.setBgmUrl(backgroundMusic.getMusicPath());
        vrbtDiyRing.setRingType(QyclConstant.RING_TYPE_DIY);
        String[] imageUrlArray = vrbtDiyRing.getImageUrls().split(",");
        String urlPre = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/";
        List<String> imageList = Arrays.stream(imageUrlArray).map(item -> urlPre + item).collect(Collectors.toList());
        String videoJobId = aliMediaService.produceVideo(AliMediaProperties.JOB_QUEUE_TAG_DIY, imageList, backgroundMusic != null ? backgroundMusic.getMusicPath() : "", "", "", "");
        vrbtDiyRing.setAliVideoJobId(videoJobId);
        this.save(vrbtDiyRing);
        map.put("ringId", vrbtDiyRing.getId());
        return map;
    }

    @Override
    public Map<String, String> createTemplateRing(String mobile, String templateId, String clipsParam, String subChannel) {
        VrbtDiyRing vrbtDiyRing = new VrbtDiyRing();
        Map<String, String> map = new HashMap<>();
        vrbtDiyRing.setMobile(mobile);
        vrbtDiyRing.setRingType(QyclConstant.RING_TYPE_TEMPLATE);
        vrbtDiyRing.setImageUrls(clipsParam);
        vrbtDiyRing.setRingMakeStatus(QyclConstant.RING_MAKE_IN);
        vrbtDiyRing.setSubChannel(subChannel);
        String videoJobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_DIY, templateId, clipsParam);
        vrbtDiyRing.setAliVideoJobId(videoJobId);
        this.save(vrbtDiyRing);
        map.put("ringId", vrbtDiyRing.getId());
        return map;

    }

    @Override
    public Map<String, String> createVideoRing(String mobile, String videoPath, String subChannel) {
        VrbtDiyRing vrbtDiyRing = new VrbtDiyRing();
        Map<String, String> map = new HashMap<>();
        vrbtDiyRing.setMobile(mobile);
        vrbtDiyRing.setRingType(QyclConstant.RING_TYPE_VIDEO);
        vrbtDiyRing.setRingMakeStatus(QyclConstant.RING_MAKE_COMPLETE);
        vrbtDiyRing.setVideoPath(videoPath);
        vrbtDiyRing.setSubChannel(subChannel);
        String vrbtDiyVideoId = uploadRing(mobile,videoPath);
        if(StringUtils.isNotBlank(vrbtDiyVideoId)){
            vrbtDiyRing.setRemark("上传成功");
            vrbtDiyRing.setVrbtDiyVideoId(vrbtDiyVideoId);
        }else{
            vrbtDiyRing.setRemark("上传失败");
        }
        this.save(vrbtDiyRing);
        map.put("ringId", vrbtDiyRing.getId());
        return map;
    }

    @Override
    public List<VrbtDiyRing> queryRing(String mobile) {
        return vrbtDiyRingMapper.queryRing(mobile);
    }

    @Override
    public RemoteResult settingRing(String vrbtDiyVideoId) {
        VrbtDiyVideo vrbtDiyVideo = vrbtDiyVideoService.getById(vrbtDiyVideoId);
        return miguApiService.vrbtToneFreeMonthOrder(vrbtDiyVideo.getMobile(), BizConstant.RECHARGE_VRBT_CHANNEL_ID, vrbtDiyVideo.getCopyrightId(), MiguApiService.VRBT_TONE_ORDER_SETFLAG_BEIJIAO);
    }

    public String uploadRing(String mobile, String videoPath) {
        String pathName = "/userDiy" + DateUtils.yyyyMMdd.get().format(new Date());
        String fileName = "video" + IdWorker.getId() + ".mp4";
        boolean result = vrbtDiyVideoService.uploadToFtp(pathName, videoPath, fileName);
        if (result) {
            return vrbtDiyService.report(mobile, pathName + "/" + fileName, fileName);
        } else {
            log.warn("移动三方支付上传铃声到ftp失败,手机号:{},oss文件路径:{}", mobile, videoPath);
            return "";
        }
    }

    @Override
    public void aliVideoJobFinishHandle(String videoJobId, String videoPath) {
        VrbtDiyRing vrbtDiyRing = lambdaQuery().eq(VrbtDiyRing::getAliVideoJobId, videoJobId).one();
        if (vrbtDiyRing == null) {
            return;
        }
        vrbtDiyRing.setRingMakeStatus(QyclConstant.RING_MAKE_COMPLETE);
        vrbtDiyRing.setVideoPath(videoPath);
//        lambdaUpdate().eq(VrbtDiyRing::getId, vrbtDiyRing.getId()).set(VrbtDiyRing::getRingMakeStatus, QyclConstant.RING_MAKE_COMPLETE).set(VrbtDiyRing::getVideoPath, videoPath).update();
//        vrbtDiyRing = getById(vrbtDiyRing.getId());
        String vrbtDiyVideoId = uploadRing(vrbtDiyRing.getMobile(), videoPath);
        if (StringUtils.isNotBlank(vrbtDiyVideoId)) {
            vrbtDiyRing.setRemark("上传成功");
            vrbtDiyRing.setVrbtDiyVideoId(vrbtDiyVideoId);
        } else {
            vrbtDiyRing.setRemark("上传失败");
        }
        this.updateById(vrbtDiyRing);
    }
}
