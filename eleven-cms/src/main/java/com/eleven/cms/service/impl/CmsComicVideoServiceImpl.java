package com.eleven.cms.service.impl;

import com.eleven.cms.entity.CmsComicVideo;
import com.eleven.cms.mapper.CmsComicVideoMapper;
import com.eleven.cms.service.ICmsComicVideoService;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: cms_comic_video
 * @Author: jeecg-boot
 * @Date: 2024-08-26
 * @Version: V1.0
 */
@Service
public class CmsComicVideoServiceImpl extends ServiceImpl<CmsComicVideoMapper, CmsComicVideo> implements ICmsComicVideoService {

    @Autowired
    private CmsComicVideoMapper cmsComicVideoMapper;

    @Override
    public List<CmsComicVideo> selectByMainId(String mainId) {
        return cmsComicVideoMapper.selectByMainId(mainId);
    }
}
