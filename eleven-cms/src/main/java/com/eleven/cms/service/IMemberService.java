package com.eleven.cms.service;

import com.eleven.cms.dto.WoMusicResponse;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.jeecg.common.api.vo.Result;

import java.util.List;

public interface IMemberService {

    Result<?> obtainMember(String msisdn,String account, String serviceId, String rightsId,String packName);

    FebsResponse isMemberAndIsReceiveRights(String msisdn,String serviceId,String token,Boolean isExtension,Long rechargeDelayMinute);

    FebsResponse unifyLogin(String msisdn,String channelCode,String serviceId);

    FebsResponse unifyTestLogin(String msisdn,String channelCode);


    FebsResponse douyinMemberLogin(String msisdn,String channelCode);

    FebsResponse unifyIsMember(String unifyToken,String spcltoken,String bdwpToken,String channelCode);

    FebsResponse unifyQueryRightsList(String spcltoken, String bdwpToken, String serviceId);

    FebsResponse unifyMemberReceiveRights(String spcltoken, String bdwpToken, String serviceId, String rightsId, String packName,String account);

    FebsResponse unifyMemberReceiveRightsTest(String spcltoken, String bdwpToken, String serviceId, String rightsId, String packName,String account);

    FebsResponse unifyQueryChargeList(String spcltoken, String bdwpToken,String serviceId,String rightsMonth,String sourse);

    FebsResponse unifyReceiveBdwp(String bdwpToken);

    FebsResponse queryRightsList(String serviceId);

    FebsResponse memberReceiveRights(String serviceId,String packName,String rightsId,String msisdn,String account,Boolean isExtension,Boolean isPrepareCharge);

    FebsResponse unifyQueryProductAndSubscribe(String spcltoken, String bdwpToken);

    WoMusicResponse isWoMusicMember(String mobile);
    //抖音权益充值
    FebsResponse douyinRightsRecharge(String mobile,String account,String couponId,String couponName);

    //抖音查询权益产品价格
    FebsResponse douyinQueryRightsProductPrice(String mobile,String couponId);
    //特定队列判断是否充值
    Boolean queueIsReceiveRecharge(String serviceId,String mobile);

    FebsResponse rechargeBjhyRights(String phone, String channelId, String serviceId, String name, String rightsId);

    FebsResponse rechargeVrbtRights(String phone, Integer nMonth);

    /**
     * 话费充值表同步
     * @param channel
     * @param serviceId
     */
    void feeRechargeAdd(String channel,String serviceId,String date);

    /**
     * 话费充值，续订同步
     */
    void feeRenew(String serviceId);

    /**
     * 话费充值
     */
    void feeRecharge(String serviceId);

    /**
     * 话费充值，城市同步
     */
    void feeCity();

    FebsResponse queryRightsListByChannel(String channelCode);


    Result<?> renewRightsReceiveSms(String channelId,String serviceType);


    FebsResponse alipayRightsRecharge(String channelId,String mobile,String account,String serviceId,String rightsId,String packName,String orderId);

    FebsResponse alipayQueryRechargeByOrderId(String orderId);

    ObjectNode woReadQueryCharge(String phone, String date) throws Exception;

    void aliPayRechargeVrbt(String orderId);


    FebsResponse queryPayOrder(String mobile, List<String> bizType);

    boolean isVrbtDiyMember(String mobile);
}
