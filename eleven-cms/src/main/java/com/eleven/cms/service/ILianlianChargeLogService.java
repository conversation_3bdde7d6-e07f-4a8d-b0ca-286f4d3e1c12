package com.eleven.cms.service;

import com.eleven.cms.dto.LianLianResponse;
import com.eleven.cms.dto.LianlianNotifyLog;
import com.eleven.cms.entity.LianlianChargeLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.FebsResponse;
import org.jeecg.common.api.vo.Result;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 联联分销充值记录
 * @Author: jeecg-boot
 * @Date:   2024-05-14
 * @Version: V1.0
 */
public interface ILianlianChargeLogService extends IService<LianlianChargeLog> {
    List<LianlianChargeLog> findTimeoutSchedule();

    void rechargeForSchedule(LianlianChargeLog lianlianChargeLog);


    FebsResponse isRecharge(String orderId, String packName, LocalDateTime scheduledTime, String itemId, String productId);

    List<LianlianChargeLog> findByOrderIdAndPackName(String orderId,String packName);


    LianlianChargeLog taskRechargeForSchedule(LianlianChargeLog lianlianChargeLog);

    LianLianResponse lianlianChargeNotifyLog(LianlianNotifyLog lianlianChargeNotifyLog);

    LianLianResponse lianlianUseNotifyLog(LianlianNotifyLog lianlianNotifyLog);

    LianLianResponse lianlianRefundNotifyLog(LianlianNotifyLog lianlianNotifyLog);

    LianLianResponse lianlianBookingNotifyLog(LianlianNotifyLog lianlianNotifyLog);

    LianLianResponse lianlianProductStatusUpdateNotifyLog(LianlianNotifyLog lianlianNotifyLog);

    LianLianResponse lianlianProductUpdateNotifyLog(LianlianNotifyLog lianlianNotifyLog);

    LianLianResponse lianlianShopUpdateNotifyLog(LianlianNotifyLog lianlianNotifyLog);

    void guiZhouMobilePayRechargeScheduleDeduct(String id,String orderId);

    boolean monthlyIsRecharge(String orderId, String serviceId);

    Result<?> wechatRefund(String orderId);
}
