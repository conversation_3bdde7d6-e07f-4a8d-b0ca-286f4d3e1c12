package com.eleven.cms.service;

import com.eleven.cms.dto.Rights;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RightsPackDto;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 权益领取校验公共接口
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/26 17:23
 **/
public interface IRightsSubService {

    String setRightCofig(Rights rights,String rightsId,Date payTime);



    Boolean everydayIsSub(String mobile,List<String> channelList);

    LocalDateTime getScheduledTime(String mobile,String serviceId,String packName);

    LocalDateTime getWebScheduledTime(String mobile,String serviceId,String packName);

//    LocalDateTime getScheduledTime(String mobile,String serviceId,String packName,LocalDateTime scheduledTime);

    LocalDateTime getWebScheduledTime(String mobile,String serviceId,String packName,LocalDateTime scheduledTime);

    LocalDateTime getCouponCodeScheduledTime(String mobile,String serviceId,String packName);

    LocalDateTime getShopOrderScheduledTime(String serviceId,String packName);

    FebsResponse isRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId);

    FebsResponse aliPayIsRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId);

    FebsResponse wangYiYunMMIsRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId,Long days);

    FebsResponse shopOrderIsRecharge(String orderId, String packName, LocalDateTime scheduledTime, String couponId);

    Boolean monthlyIsRecharge(String mobile, String serviceId);

    Boolean aliPayMonthlyIsRecharge(String mobile, String serviceId);

    Boolean wangYiYunMMMonthlyIsRecharge(String mobile, String serviceId,Long days);

    Boolean shopOrderDayIsRecharge(String outTradeNo, String serviceId);

    FebsResponse queryRightsList(String serviceId,List<String> businessPackIdList);

    FebsResponse queryWebRightsList(String serviceId,List<String> businessPackIdList);

    FebsResponse queryRechargeList(String mobile);

    Boolean isAccount(String rightsId,String account);

    String nowIsSub(String mobile,String channel);

    FebsResponse woReadShopIsRecharge(String mobile, String packName, LocalDateTime scheduledTime, String couponId,List<String> serviceIdIdList);

    Boolean woReadShopMonthlyIsRecharge(String mobile, String serviceId, List<String> serviceIdList);

    FebsResponse queryRightsListById(String id);


    List<RightsPackDto> queryLianLianWebRightsList(String serviceId,List<String> businessPackIdList);

    List<RightsPackDto> queryLianLianRightsList(String serviceId,List<String> businessPackIdList);

    boolean lianLianPayEverydayIsSub(String orderId,List<String> channelList);

}
