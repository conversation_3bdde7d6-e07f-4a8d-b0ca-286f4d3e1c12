package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.ad.GuangDongMobileStockProperties;
import com.eleven.cms.config.GuangDongMobileStockProduct;
import com.eleven.cms.service.IGuangDongMobileService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.SignatureUtils;
import com.eleven.cms.vo.GuangDongMobileCreateOrderResult;
import com.eleven.cms.vo.GuangDongMobileOrderResult;
import com.eleven.cms.vo.GuangDongMobileSmsResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 广东移动业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/25 16:22
 **/
@Slf4j
@Service
public class GuangDongMobileServiceImpl implements IGuangDongMobileService {
    private static final String LOG_TAG = "广东移动业务API";
    //1：办理
    //2：取消
    //3：修改
    public static final String YIDONG_GUANGDONG_STOCK_SUBSCRIBE = "1";
    public static final String YIDONG_GUANGDONG_STOCK_UNSUBSCRIBE= "2";
    public static final String YIDONG_GUANGDONG_STOCK_UPDATE= "3";
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;

    @Autowired
    private GuangDongMobileStockProperties guangDongMobileStockProperties;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().readTimeout(20L, TimeUnit.SECONDS).writeTimeout(20L, TimeUnit.SECONDS).build();;
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
            this.client = this.client.newBuilder()
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }



    /**
     * 创建订单
     * @param mobile
     * @return
     */
    private GuangDongMobileCreateOrderResult createOrder(String mobile,String channel) {
        GuangDongMobileStockProduct guangDongMobileStockProduct=guangDongMobileStockProperties.getProductMap().get(channel);
        ObjectNode msgbody =mapper.createObjectNode();
        ObjectNode userinfo =mapper.createObjectNode();
        userinfo.put("servernum",mobile);
        ObjectNode productinfo =mapper.createObjectNode();
        productinfo.put("productid",guangDongMobileStockProduct.getProductId());
        productinfo.put("productgroup","0");
        productinfo.put("producttype","0");
        productinfo.put("productname",guangDongMobileStockProduct.getProductName());
        productinfo.put("ordertype",YIDONG_GUANGDONG_STOCK_SUBSCRIBE);
        productinfo.put("existattr","0");
        msgbody.putPOJO("productinfo",productinfo);
        msgbody.putPOJO("userinfo",userinfo);
        msgbody.put("morecommorder","0");
//        1、请求链接 https://**************:443/eaop/rest/BSS/commodity/create_productorder/v1.1.1
//        2、请求头 -H "route_value:15915401656" -H "appId:110041" -H "route_type:1" -H "sign:OZX6Jyt6pwukxOrXj2/fv50fs35aTXs8zyPkaPSrMSjco8gA7gid6Np6JPTmUzXX54U4vtBZlfSOPNF2T75goo41aVwska2KQhWiBi+PV6qGrGF/6mvm576D5tQQRBvd1UZgX5GcPzjskAltWyrJHlSFsXi/ACyiU85TVSsP5WinW8vdz04VCacgV2GRYONyYguKl4KQbomUD+GTMu2HkoA6BxpryEAWOhGCzQtAsfqSLgv/H7R1yPvX1JpExpf+RqESVlfr9a/3XJFQWfo9t3Zfp60OcjbLvnpA6dc3/bQI7QWPKddsmeGKhihuY3cbZrYdRxgoPfTqc94QNl1g4Q==" -H "busiSerial:d0cf703cac31c2d3847bfa239e8b4c36" -H "nonce:95CF274FA042D184A26A3B1AB22BC01D" -H "timestamp:20241101104846845" -H "Content-Type:application/json; charset=utf-8" -H "Content-Length:217" -H "Host:**************" -H "Connection:Keep-Alive" -H "Accept-Encoding:gzip" -H "User-Agent:okhttp/3.14.9"
//        3、报文体 {"productinfo":{"productid":"GS.prod.10086000053672","productgroup":"0","producttype":"0","productname":"视频彩铃订阅-炫视流量包","ordertype":"1"},"userinfo":{"servernum":"15915401656"},"morecommorder":"0"}
//        4、待加密串 appId=110041&busiSerial=d0cf703cac31c2d3847bfa239e8b4c36&nonce=95CF274FA042D184A26A3B1AB22BC01D&route_type=1&route_value=15915401656&timestamp=20241101104846845{"productinfo":{"productid":"GS.prod.10086000053672","productgroup":"0","producttype":"0","productname":"视频彩铃订阅-炫视流量包","ordertype":"1"},"userinfo":{"servernum":"15915401656"},"morecommorder":"0"}
//        5、返回码 {"respcode":"1000030007","respdesc":"参数[手机号码][mobileno]的值不能为空","resptype":"500"}
        Map<String, String> header = Maps.newHashMap();
        header.put("appId",guangDongMobileStockProperties.getAppId());
        header.put("timestamp", DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
        header.put("busiSerial", IdWorker.get32UUID());
        header.put("nonce", IdWorker.get32UUID().toUpperCase());
        header.put("route_type","1");
        header.put("route_value",mobile);
        try {
            header.put("sign", SignatureUtils.generateSign(header,guangDongMobileStockProperties.getPrivateKey(),msgbody.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("{}-创建订单-请求数据=>请求参数:{},header:{}",LOG_TAG,msgbody,header);
        RequestBody body = RequestBody.create(JSON, msgbody.toString());
        Request request = new Request.Builder().url(guangDongMobileStockProperties.getCreateOrderUrl()).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-创建订单-响应数据=>请求参数:{},响应参数:{},header:{}",LOG_TAG,msgbody,content,header);
            return mapper.readValue(content, GuangDongMobileCreateOrderResult.class);

        } catch (Exception e) {
            log.error("{}-创建订单-请求异常=>请求参数:{},header:{}",LOG_TAG,msgbody,header,e);
            return null;
        }
    }
    /**
     * 获取短信验证码
     * @param mobile
     * @return
     */
    @Override
    public Result<?> sendSms(String mobile,String channel) {
        GuangDongMobileStockProduct guangDongMobileStockProduct=guangDongMobileStockProperties.getProductMap().get(channel);
        if(guangDongMobileStockProduct==null){
            log.error("广东移动存量业务,获取验证码,渠道号错误=>手机号:{},渠道号:{}",mobile,channel);
            return Result.error("订购失败");
        }
        GuangDongMobileCreateOrderResult resultCreateOrder=createOrder(mobile, channel);
        if(resultCreateOrder==null || !resultCreateOrder.isOK()){
            return Result.error(resultCreateOrder.getRespdesc());
        }
        ObjectNode msgbody =mapper.createObjectNode();
        msgbody.put("mobileno",mobile);
        msgbody.put("orderid",resultCreateOrder.getResult().getOrderinfo().getOrderid());
        Map<String, String> header = Maps.newHashMap();
        header.put("appId",guangDongMobileStockProperties.getAppId());
        header.put("timestamp", DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
        header.put("busiSerial", IdWorker.get32UUID());
        header.put("nonce", IdWorker.get32UUID().toUpperCase());
        header.put("route_type","1");
        header.put("route_value",mobile);
        try {
            header.put("sign", SignatureUtils.generateSign(header,guangDongMobileStockProperties.getPrivateKey(),msgbody.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("{}-发送验证码-请求数据=>请求参数:{},header:{}",LOG_TAG,msgbody,header);
        RequestBody body = RequestBody.create(JSON, msgbody.toString());
        Request request = new Request.Builder().url(guangDongMobileStockProperties.getSendSmsUrl()).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-发送验证码-响应数据=>请求参数:{},响应参数:{},header:{}",LOG_TAG,msgbody,content,header);
            GuangDongMobileSmsResult guangDongMobileSmsResult =mapper.readValue(content, GuangDongMobileSmsResult.class);
            if(guangDongMobileSmsResult.isOK()){
                Result result=new Result();
                result.setMessage("验证码已发送");
                result.setOrderId(resultCreateOrder.getResult().getOrderinfo().getOrderid());
                result.setNumberId(guangDongMobileSmsResult.getResult().getSmsseq());
                result.setCode(CommonConstant.SC_OK_200);
                return result;
            }
            return Result.error(guangDongMobileSmsResult.getRespdesc());
        } catch (Exception e) {
            log.error("{}-发送验证码-请求异常=>请求参数:{},header:{}",LOG_TAG,msgbody,header,e);
            return Result.error("验证码发送失败");
        }
    }


    /**
     * 产品办理
     * @param mobile
     * @return
     */
    @Override
    public Result<?> submitSms(String mobile,String authCode,String orderId,String smsseq,String channel) {
        GuangDongMobileStockProduct guangDongMobileStockProduct=guangDongMobileStockProperties.getProductMap().get(channel);
        if(guangDongMobileStockProduct==null){
            log.error("广东移动存量业务,提交验证码,渠道号错误=>手机号:{},验证码:{},订单号:{},渠道号:{}",mobile,authCode,orderId,channel);
            return Result.error("订购失败");
        }
        ObjectNode msgbody =mapper.createObjectNode();
        msgbody.put("orderid",orderId);
        msgbody.put("servernum",mobile);
        msgbody.put("smsseq",smsseq);
        msgbody.put("smscode",authCode);

        Map<String, String> header = Maps.newHashMap();
        header.put("appId",guangDongMobileStockProperties.getAppId());
        header.put("timestamp", DateUtil.formatForMiguGroupApi(LocalDateTime.now()));
        header.put("busiSerial", IdWorker.get32UUID());
        header.put("nonce", IdWorker.get32UUID().toUpperCase());
        header.put("authCode",authCode);
        header.put("route_type","1");
        header.put("route_value",mobile);
        try {
            header.put("sign", SignatureUtils.generateSign(header,guangDongMobileStockProperties.getPrivateKey(),msgbody.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("{}-提交验证码-请求数据=>请求参数:{},header:{}",LOG_TAG,msgbody,header);
        RequestBody body = RequestBody.create(JSON, msgbody.toString());
        Request request = new Request.Builder().url(guangDongMobileStockProperties.getSubmitSmsUrl()).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码-响应数据=>请求参数:{},响应参数:{},header:{}",LOG_TAG,msgbody,content,header);
            GuangDongMobileOrderResult result =mapper.readValue(content, GuangDongMobileOrderResult.class);
            if(result.isOK()){
                return Result.ok("订购成功");
            }
            return Result.error(result.getRespdesc());
        } catch (Exception e) {
            log.error("{}-提交验证码-请求异常=>请求参数:{},header:{}",LOG_TAG,msgbody,header,e);
            return Result.error("订购失败");
        }
    }
}
