package com.eleven.cms.service;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.vo.KaSaiStockRequest;
import org.jeecg.common.api.vo.Result;

import java.util.Map;

/**
 * 卡塞存量业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:28
 **/
public interface IKaSaiStockService {
    Result<?> yidongSendSms(Subscribe subscribe);

    Result<?> yidongSubmitSub(Subscribe subscribe);

    Result<?> liantongSendSms(Subscribe subscribe);

    Result<?> liantongSubmitSub(Subscribe subscribe);

    Map<String, Object> upSyncMessage(Map<String, Object> map, KaSaiStockRequest kaSaiStockRequest);

}
