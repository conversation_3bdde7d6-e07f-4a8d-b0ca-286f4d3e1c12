package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.XinjiangYouranProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.SichuanMobileFlowPacketService;
import com.eleven.cms.remote.XinjiangYidongYouranService;
import com.eleven.cms.service.IAdSiteBusinessConfigService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.XinjiangYidongYouranResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2023-08-18 15:46
 */
@Service("xinjiangYidongCommonService")
@Slf4j
public class XinjiangYidongCommonServiceImpl implements IBizCommonService {

    @Autowired
    SichuanMobileFlowPacketService sichuanMobileFlowPacketService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    XinjiangYidongYouranService xinjiangYidongYouranService;
    @Autowired
    XinjiangYouranProperties xinjiangYouranProperties;

    @Autowired
    IChannelService channelService;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        boolean result = xinjiangYidongYouranService.getSms(subscribe.getMobile(), subscribe.getChannel());
        if (result) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\"9999\",\"message\":\"获取验证码失败\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        boolean isCheckCode = xinjiangYidongYouranService.validateSmsCode(subscribe.getMobile(), subscribe.getChannel(), smsCode);
        if (isCheckCode) {
            boolean isCheckAge = xinjiangYidongYouranService.checkAge(subscribe.getMobile(), subscribe.getChannel(), 18) && xinjiangYidongYouranService.checkAge(subscribe.getMobile(), subscribe.getChannel(), 60);
            if (!isCheckAge) {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult("用户年龄不在18-60岁之间");
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error("订阅失败");
            }
            boolean isCheckOpenDate = xinjiangYidongYouranService.checkPersonOpenDate(subscribe.getMobile(), subscribe.getChannel());
            if (!isCheckOpenDate) {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult("用户入网天数不足60天");
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error("订阅失败");
            }
            XinjiangYidongYouranResult xinjiangYidongYouranResult = xinjiangYidongYouranService.order(subscribe.getMobile(), subscribe.getChannel());
            if (xinjiangYidongYouranResult.isOk()) {
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setResult("订购成功");
                subscribeService.updateSubscribeDbAndEs(upd);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                return Result.ok("订阅成功");
            } else {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(xinjiangYidongYouranResult.getResult() != null ? xinjiangYidongYouranResult.getResult().getXResultInfo() : xinjiangYidongYouranResult.getRespDesc());
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error("订阅失败");
            }
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult("验证码检验失败");
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
