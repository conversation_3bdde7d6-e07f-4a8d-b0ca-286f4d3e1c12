package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.YiZunCpaGetSmsResult;
import com.eleven.cms.vo.YiZunCpaSmsCodeResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;

/**
 * @author: yang tao
 * @create: 2024-12-12 11:46
 */
@Service("yiZunCpaCommonService")
@Slf4j
public class YiZunCpaCommonServiceImpl implements IBizCommonService {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IYiZunCpaApiService yiZunCpaApiService;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        YiZunCpaGetSmsResult yiZunCpaGetSmsResult = yiZunCpaApiService.getSms(subscribe.getChannel(), subscribe.getMobile(), subscribe.getIp(), subscribe.getUserAgent(), subscribe.getReferer(), subscribe.getSource());
        if (yiZunCpaGetSmsResult.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            subscribe.setIspOrderNo(yiZunCpaGetSmsResult.getTraceId());
            subscribe.setExtra(yiZunCpaGetSmsResult.getBizToken());
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
            try {
                String errorMsg="{\"code\":\""+yiZunCpaGetSmsResult.getCode()+"\",\"message\":\""+yiZunCpaGetSmsResult.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        YiZunCpaSmsCodeResult yiZunCpaSmsCodeResult = yiZunCpaApiService.smsCode(subscribe.getChannel(), subscribe.getMobile(), subscribe.getSmsCode(),subscribe.getId(),subscribe.getExtra());
        if (yiZunCpaSmsCodeResult.isOK()) {
            upd.setStatus(BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setExtra(subscribe.getSmsCode());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("订阅成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(yiZunCpaSmsCodeResult.getMessage());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
