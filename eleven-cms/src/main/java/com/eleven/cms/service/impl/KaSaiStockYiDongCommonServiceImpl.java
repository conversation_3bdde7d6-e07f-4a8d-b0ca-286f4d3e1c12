package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IKaSaiStockService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * 卡塞存量业务移动
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:25
 **/
@Service("kaSaiStockYiDongCommonService")
@Slf4j
public class KaSaiStockYiDongCommonServiceImpl implements IBizCommonService {
    public static final String YIDONG_SMS_INVALID_KEY_PREFIX = "yidong::SmsInvalid:";
    public static final long YIDONG_SMS_INVALID_CACHE_SECONDS = 60;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IKaSaiStockService kaSaiStockService;
    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        String orderId= IdWorker.get32UUID();
        subscribe.setIspOrderNo(orderId);
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        Result<?> result = kaSaiStockService.yidongSendSms(subscribe);
        if (result.isOK()) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+result.getCode()+"\",\"message\":\""+result.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = YIDONG_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", YIDONG_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        Result<?> submitResult = kaSaiStockService.yidongSubmitSub(subscribe);
        String result = "卡塞存量业务移动订购结果==>{\"resCode\":\"" + submitResult.getCode() + "\",\"resMsg\":\"" + submitResult.getMessage() + "\"}";
        upd.setResult(result);
        if (submitResult != null && submitResult.isOK()) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setExtra(submitResult.getResult().toString());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("订阅成功");
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
