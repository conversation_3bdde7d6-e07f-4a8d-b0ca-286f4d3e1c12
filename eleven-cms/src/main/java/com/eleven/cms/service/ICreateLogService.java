package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Channel;
import com.eleven.cms.entity.CreateLog;
import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Description: cms_channel
 * @Author: jeecg-boot
 * @Date:   2020-10-23
 * @Version: V1.0
 */
public interface ICreateLogService {

    List<CreateLog> createLog(String id);

    ModelAndView searchLog(String id);

    ModelAndView searchFirsthandLog(String id);

    ModelAndView searchSmsCodeLog(String id);

    ModelAndView searchLogV2(String id);

    Map<String,Object> queryFirsthandLog(String id);
}
