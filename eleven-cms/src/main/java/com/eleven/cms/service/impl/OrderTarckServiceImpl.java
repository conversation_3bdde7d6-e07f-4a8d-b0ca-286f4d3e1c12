package com.eleven.cms.service.impl;

import com.eleven.cms.entity.OrderTarck;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.mapper.OrderTarckMapper;
import com.eleven.cms.mapper.SubscribeMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IOrderTarckService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**
 * @Description:   每日凌晨2点查014X04C渠道号2024年1月2日山东订购包月校验
 * @Author: jeecg-boot
 * @Date:   2024-01-22
 * @Version: V1.0
 */
@Service
public class OrderTarckServiceImpl extends ServiceImpl<OrderTarckMapper, OrderTarck> implements IOrderTarckService {
    @Autowired
    private SubscribeMapper subscribeMapper;
    @Autowired
    MiguApiService miguApiService;

    /**
     * 每日凌晨2点查014X04C渠道号2024年1月2日山东订购包月校验
     */
    @Override
    public void addTarck() {
        List<Subscribe> subscribesList=subscribeMapper.querySubscribeList();
        Date date=new Date();
        subscribesList.forEach(sub -> {
            OrderTarck orderTarck=new OrderTarck();
            /**手机号*/
            orderTarck.setMobile(sub.getMobile());
            /**运营商,1移动用户,3联通用户,4电信用户*/
            orderTarck.setIsp(sub.getIsp());
            /**业务类型,vrbt:视频彩铃,rt:振铃*/
            orderTarck.setBizType(sub.getBizType());
            /**渠道号*/
            orderTarck.setChannel(sub.getChannel());
            /**子渠道号*/
            orderTarck.setSubChannel(sub.getSubChannel());
            /**状态(-1=初始,0=失败,1=成功,2=已派发,3=已有包月,4=当日重复,5=已提交短信验证码)*/
            orderTarck.setStatus(sub.getStatus());
            /**订购结果*/
            orderTarck.setResult(sub.getResult());
            Integer status = miguApiService.verifyMiguVrbtMonthStatus(sub.getMobile(), sub.getChannel(),false) > 0 ? BizConstant.SUBSCRIBE_MONTH_VERIFY_EXISTS : BizConstant.SUBSCRIBE_MONTH_VERIFY_NONE;
            /**当前订购状态校验*/
            orderTarck.setVerifyStatus(status);
            /**省份*/
            orderTarck.setProvince(sub.getProvince());
            /**城市*/
            orderTarck.setCity(sub.getCity());
            /**备注*/
            orderTarck.setRemark(sub.getRemark());
            /**查询时间*/
            orderTarck.setQueryTime(date);
            this.baseMapper.insert(orderTarck);
        });
    }
}
