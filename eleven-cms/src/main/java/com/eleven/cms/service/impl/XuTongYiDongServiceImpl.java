package com.eleven.cms.service.impl;

import com.eleven.cms.ad.XuTongYiDongProperties;
import com.eleven.cms.config.XuTongYiDongProduct;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IXuTongYiDongService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.XuTongYiDongResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 广州旭同移动业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/22 14:28
 **/
@Slf4j
@Service
public class XuTongYiDongServiceImpl implements IXuTongYiDongService {
    public static final String XUTONG_STATUS_SUB_SUCCESS = "0"; //开通成功
    public static final String XUTONG_STATUS_OVERFLOW_COUNT = "-1"; //业务[xx省份]到量/到日量
    public static final String XUTONG_STATUS_UN_SUB= "-1"; //退订
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    XuTongYiDongProperties xuTongYiDongProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private BlackListService blackListService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public Result<?> sendMessage(Subscribe subscribe) {
        XuTongYiDongProduct xuTongYiDongProduct=xuTongYiDongProperties.getProductMap().get(subscribe.getChannel());
        if(xuTongYiDongProduct==null){
            return Result.error("获取产品配置失败");
        }
        StringBuffer valueStr = new StringBuffer();
        valueStr.append("?imsi=" + subscribe.getMobile());
        valueStr.append("&imei=" + subscribe.getMobile());
        valueStr.append("&packageId=" + xuTongYiDongProduct.getPackageId());
        valueStr.append("&sid=" + subscribe.getIspOrderNo());
        valueStr.append("&phone=" + subscribe.getMobile());
        valueStr.append("&ip=" + subscribe.getIp());
        valueStr.append("&iccId=1");
        valueStr.append("&notifyUrl=" + xuTongYiDongProperties.getCallbackUrl());
        if(StringUtil.isNotBlank(xuTongYiDongProduct.getCancelUrl())){
            valueStr.append("&cancelUrl=" + xuTongYiDongProduct.getCancelUrl());
        }
        String content =implementHttpGetResult(xuTongYiDongProperties.getGetSendSmsUrl()+valueStr.toString(),"广州旭同"+xuTongYiDongProduct.getProductName()+"获取验证码接口",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("获取验证码失败");
        }
        try {
            XuTongYiDongResult result = mapper.readValue(content, XuTongYiDongResult.class);
            if(result.isOK() && StringUtil.isNotBlank(result.getOrderId())){
                return Result.ok("获取验证码成功",result.getOrderId());
            }
            return Result.error(result.getErrorMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("获取验证码失败");
    }

    @Override
    public Result<?> submitOrder(Subscribe subscribe) {
        XuTongYiDongProduct xuTongYiDongProduct=xuTongYiDongProperties.getProductMap().get(subscribe.getChannel());
        if(xuTongYiDongProduct==null){
            return Result.error("获取产品配置失败");
        }
        StringBuffer valueStr = new StringBuffer();
        valueStr.append("?orderId=" + subscribe.getExtra());
        valueStr.append("&verifyCode=" + subscribe.getSmsCode());
        String content =implementHttpGetResult(xuTongYiDongProperties.getSubmitSendSmsUrl()+valueStr.toString(),"广州旭同移动业务"+xuTongYiDongProduct.getProductName()+"提交验证码接口",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("提交验证码失败");
        }
        try {
            XuTongYiDongResult result = mapper.readValue(content, XuTongYiDongResult.class);
            if(result.isOK()){
                return Result.ok("提交验证码成功");
            }
            return Result.error(result.getErrorMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("提交验证码失败");
    }

    @Override
    public void xuTongYiDongNotify(String phone, String status, String sid,String resultMsg,String cancelTime) {
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, phone).eq(Subscribe::getIspOrderNo, sid).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (XUTONG_STATUS_SUB_SUCCESS.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else if (XUTONG_STATUS_OVERFLOW_COUNT.equals(status) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\""+resultMsg+"\"}";
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }else if (XUTONG_STATUS_UN_SUB.equals(status) && SUBSCRIBE_STATUS_SUCCESS.equals(subscribe.getStatus()) && StringUtil.isNotBlank(cancelTime)) {
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\""+resultMsg+"\"}";
                upd.setResult(result);
                upd.setVerifyStatus(0);
                upd.setVerifyStatusDaily(0);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                try {
                    blackListService.saveBlackList(subscribe.getMobile(), "退订", subscribe.getBizType());
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }else{
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\""+resultMsg+"\"}";
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
    }


    /**
     * 发起http请求get
     */
    public String implementHttpGetResult(String url,String msg,String mobile) {
        return pushGet(url,msg,mobile);
    }

    public String pushGet(String url,String msg,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{}",mobile,url);
        Request request = new Request.Builder().url(url).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},响应参数:{}",mobile,url,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{}",mobile,url,e);
            return null;
        }
    }
}
