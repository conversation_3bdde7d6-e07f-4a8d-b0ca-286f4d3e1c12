package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.ThirdPartyChannelConfigProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.mapper.SubscribeMapper;
import com.eleven.cms.remote.PushSubscribeService;
import com.eleven.cms.service.ISmsValidateLogService;
import com.eleven.cms.service.ISmsValidateService;
import com.eleven.cms.service.IThirdPartyService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2021-10-19 11:37
 */
@Slf4j
@Service
public class ThirdPartyServiceImpl extends ServiceImpl<SubscribeMapper, Subscribe> implements IThirdPartyService {


    private static final String MOBILE_SEND_SMS_LIMIT_KEY_PREFIX = "mobileSendSmsLimit:";

    //定义为90秒,和开通等待短信验证码时间保持一致
    private static final long MOBILE_SEND_SMS_CATCHE_TTL_SECONDS = 60;
    private static final String MOBILE_SEND_SMS_LIMIT_VALUE = "SMS_LIMIT";
    public static final String SMS_INVALID_KEY_PREFIX = "smsInvalid:";
    public static final long SMS_INVALID_CACHE_SECONDS = 60;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ThirdPartyChannelConfigProperties thirdPartyChannelConfigProperties;

    @Autowired
    private PushSubscribeService pushSubscribeService;

    @Autowired
    private ISmsValidateService smsValidateService;
    @Autowired
    private ISmsValidateLogService smsValidateLogService;

    @Autowired
    private IEsDataService esDataService;


    /**
     * 获取短信验证码
     *
     * @param mobile
     * @param channelCode
     * @return
     */
    @Override
    public Result getSmsCode(String mobile, String channelCode, String subChannel) {

        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + mobile;
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        try {
            smsValidateLogService.logCreate(mobile, subChannel);
            boolean result = smsValidateService.createThirdParty(mobile, channelCode);
            if (!result) {
                return Result.error("发送短信验证码失败,请稍后再试");
            }
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.noauth("验证码已发送");
        } catch (IllegalArgumentException exception) {
            return Result.error(exception.getMessage());
        } catch (Exception e) {
            return Result.error("发送短信验证码错误");
        }
    }

    /**
     * 业务订购
     *
     * @param subscribe
     * @return
     */
    @Override
    public Result order(Subscribe subscribe) {

        final Result<Object> okResult = Result.ok("订阅成功");
        final String mobile = subscribe.getMobile();
        final String smsCode = subscribe.getSmsCode();
        String subChannel = subscribe.getSubChannel();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", SMS_INVALID_CACHE_SECONDS);
        try {
            smsValidateService.check(mobile, smsCode);
            smsValidateLogService.logValid(mobile, subChannel);
        } catch (JeecgBootException e) {
            return Result.error("验证码错误");
        }
        try {
            //外部渠道则不需要校验,直接设定为退订状态
            subscribe.setPrice(0);
            this.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            pushSubscribeService.pushThirdParty(subscribe);
            return okResult;
        } catch (Exception e) {
            return Result.error("订购错误");
        }
    }

    public void createSubscribeDbAndEs(Subscribe subscribe) {
        if(this.save(subscribe)){
            esDataService.saveOrUpdateSubscribeAsync(subscribe);
        }
    }

}
