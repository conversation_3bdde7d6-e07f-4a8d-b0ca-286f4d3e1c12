package com.eleven.cms.service.impl;

import com.eleven.cms.config.DuoDianProperties;
import com.eleven.cms.service.IDuoDianRechargeApiService;
import com.eleven.cms.service.IJunboSubService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.SignatureUtils;
import com.eleven.cms.util.junbo.CryptoUtility;
import com.eleven.cms.vo.DuoDianOrderQueryResult;
import com.eleven.cms.vo.DuoDianRechargeResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URLDecoder;
import java.security.PublicKey;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * @author: yang tao
 * @create: 2025-01-01 10:46
 */
@Slf4j
@Service
public class DuoDianRechargeApiServiceImpl implements IDuoDianRechargeApiService {
    public static final String LOG_TAG = "多点充值api";
    @Autowired
    private DuoDianProperties duoDianProperties;
    @Autowired
    private Environment environment;

    private OkHttpClient client;
    private ObjectMapper mapper;
    public static final String DELIMITER_AMP = "&";
    private static final MediaType mediaType = MediaType.parse("application/json");
    @Autowired
    IJunboSubService junboSubService;

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    @Override
    public DuoDianRechargeResult receiveCoupon(String orderId,String mobile,String couponId){
        Map<String, Object> dataMap = new TreeMap<>();
        //业务流水号
        dataMap.put("businessNo",orderId);
        //时间戳
        dataMap.put("timeStamp", System.currentTimeMillis());

        //多点分配的唯一商户
        dataMap.put("merchantNo", duoDianProperties.getAppId());
        dataMap.put("activityId",couponId);
        dataMap.put("phone", mobile);
        try {
            dataMap.put("sign", SignatureUtils.generateSign(dataMap,duoDianProperties.getKey()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> keySet = dataMap.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        String plainTextData = null;
        try {
            plainTextData = mapper.writeValueAsString(dataMap);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        String data = null;
        try {
            data = SignatureUtils.encipher(plainTextData,SignatureUtils.getPublicKeyFromString(duoDianProperties.getPublicKey()),117);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody formBody = new FormBody.Builder().add("data", data).build();
        log.info("{}-领取优惠券-手机号:{},产品ID:{},请求参数:{}", LOG_TAG, mobile, couponId,dataMap);
        Request request = new Request.Builder().url(duoDianProperties.getRechargeVipUrl()).post(formBody).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-领取优惠券-手机号:{},产品ID:{},响应:{}", LOG_TAG, mobile, couponId, content);
            DuoDianRechargeResult duoDianRechargeResult = mapper.readValue(content, DuoDianRechargeResult.class);
            if(StringUtils.isNotBlank(duoDianRechargeResult.getMsg()) && isUnicodeString(duoDianRechargeResult.getMsg())){
                String msg = URLDecoder.decode(duoDianRechargeResult.getMsg().replaceAll("\\\\u", "\\\\u00"), "utf-8");
                duoDianRechargeResult.setMsg(msg);
            }

            return duoDianRechargeResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-领取优惠券-手机号:{},产品ID:{},异常:", LOG_TAG, mobile, couponId, e);
            return DuoDianRechargeResult.FAIL_RESULT;
        }
    }
    @Override
    public DuoDianOrderQueryResult queryReceiveStatus(String orderId,String mobile,String couponId){
        Map<String, Object> dataMap = new TreeMap<>();
        //业务流水号
        dataMap.put("businessNo",orderId);
        //时间戳
        dataMap.put("timeStamp", System.currentTimeMillis());

        //多点分配的唯一商户
        dataMap.put("merchantNo", duoDianProperties.getAppId());
        dataMap.put("activityId",couponId);
        try {
            dataMap.put("sign", SignatureUtils.generateSign(dataMap,duoDianProperties.getKey()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> keySet = dataMap.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        String plainTextData = null;
        try {
            plainTextData = mapper.writeValueAsString(dataMap);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        String data = null;
        try {
            data = SignatureUtils.encipher(plainTextData,SignatureUtils.getPublicKeyFromString(duoDianProperties.getPublicKey()),117);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody formBody = new FormBody.Builder().add("data", data).build();
        log.info("{}-查询领取优惠券状态-手机号:{},产品ID:{},请求参数:{}", LOG_TAG, mobile, couponId,dataMap);
        Request request = new Request.Builder().url(duoDianProperties.getOrderQueryUrl()).post(formBody).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-查询领取优惠券状态-手机号:{},产品ID:{},响应:{}", LOG_TAG, mobile, couponId, content);
            DuoDianOrderQueryResult duoDianOrderQueryResult = mapper.readValue(content, DuoDianOrderQueryResult.class);
            if(StringUtils.isNotBlank(duoDianOrderQueryResult.getMsg()) && isUnicodeString(duoDianOrderQueryResult.getMsg())){
                String msg = URLDecoder.decode(duoDianOrderQueryResult.getMsg().replaceAll("\\\\u", "\\\\u00"), "utf-8");
                duoDianOrderQueryResult.setMsg(msg);
            }

            return duoDianOrderQueryResult;
        } catch (Exception e) {
            //e.printStackTrace();
            log.info("{}-查询领取优惠券状态-手机号:{},产品ID:{},异常:", LOG_TAG, mobile, couponId, e);
            return DuoDianOrderQueryResult.FAIL_RESULT;
        }
    }


    public static boolean isUnicodeString(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 正则表达式匹配Unicode字符
        String regex = "\\\\u[0-9a-fA-F]{4}";
        for (int i = 0; i < str.length(); i++) {
            // 对于不是转义序列的字符，直接返回false
            if (str.charAt(i) == '\\') {
                if (!str.substring(i).matches(regex)) {
                    return false;
                }
            } else if (Character.isSupplementaryCodePoint(str.codePointAt(i))) {
                // 对于超出基本多语言面板(BMP)的字符，直接返回true
                return true;
            }
        }
        return true;
    }
}
