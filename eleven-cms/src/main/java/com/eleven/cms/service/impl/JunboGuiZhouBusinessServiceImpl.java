package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.entity.JunboSub;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.JunboCpaService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IBusinessCommonService;
import com.eleven.cms.service.IJunboSubService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.JunboLlbResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * 骏伯贵州移动业务报备
 * @author: yang tao
 * @create: 2024-08-13 14:21
 */
@Slf4j
@Service
public class JunboGuiZhouBusinessServiceImpl implements IBusinessCommonService {
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    IJunboSubService junboSubService;
//    @Autowired
//    JunboLlbService junboLlbService;
    @Autowired
    JunboCpaService junboCpaService;
    @Autowired
    RedisUtil redisUtil;
    private ObjectMapper mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
//        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
//        String mobile = subscribe.getMobile();
//        //设置归属地
//        try {
//            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
//            if (mobileRegionResult != null) {
//                subscribe.setProvince(mobileRegionResult.getProvince());
//                subscribe.setCity(mobileRegionResult.getCity());
//                subscribe.setIsp(mobileRegionResult.getOperator());
//                if (mobileRegionResult.isIspYidong()) {
//                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
//                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
//                        return Result.error("暂未开放,敬请期待!");
//                    }
//                } else {
//                    return Result.error("暂只支持移动用户!");
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return errorResult;
//        }
        return SpringContextUtils.getBean(JunboGuiZhouBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    public Result receiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String ispOrderNo ="CC" + DateUtils.yyyyMMdd.get().format(new Date()) + IdWorker.getId(); //junboLlbService.getSysOrderId();
            subscribe.setIspOrderNo(ispOrderNo);
            JsonNode jsonNode = mapper.valueToTree(subscribe);
            JunboSub junboSub = null;
            try {
                junboSub = mapper.treeToValue(jsonNode, JunboSub.class);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            junboSub.setId(null);
            junboSubService.save(junboSub);
            JunboLlbResult junboLlbResult = junboCpaService.handleOrderUrl(mobile,ispOrderNo, smsCode, subscribe.getChannel(), subscribe.getUserAgent(), subscribe.getReferer(), subscribe.getSource());
            if (junboLlbResult.isOperationOk()) {
                JunboSub upd = new JunboSub();
                upd.setId(junboSub.getId());
                upd.setStatus(BizConstant.SUBSCRIBE_STATUS_PUSHED);
                upd.setBizTime(new Date());
                upd.setModifyTime(new Date());
                if(StringUtils.isNotBlank(junboLlbResult.getData().getUrl())){
                    Map<String, String> parameterMap= getURLParameters(junboLlbResult.getData().getUrl());
                    if(parameterMap.containsKey("serialNumber")){
                        upd.setExtra(parameterMap.get("serialNumber"));
                    }
                }
                junboSubService.updateById(upd);
                return Result.okAndSetData(junboLlbResult.getData().getUrl());
            }
            String message=junboLlbResult.getData()!=null?junboLlbResult.getData().getMsg():junboLlbResult.getMessage();
            return Result.error(message);
        } else {
            return Result.error("贵州移动存量业务无需提交短信验证码");
        }
    }

    /**
     * 贵州移动二次确认下单
     * @param subscribe
     * @return
     */
    public Result verifyReceiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            JunboSub junboSub=junboSubService.lambdaQuery().eq(JunboSub::getExtra,subscribe.getIspOrderNo()).orderByDesc(JunboSub::getCreateTime).last("limit 1").one();
            JunboLlbResult junboLlbResult = junboCpaService.handleOrderUrl(mobile,junboSub.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getUserAgent(), subscribe.getReferer(), subscribe.getSource());
            if (junboLlbResult.isOperationOk()) {
                JunboSub upd = new JunboSub();
                upd.setId(junboSub.getId());
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setBizTime(new Date());
                upd.setModifyTime(new Date());
                junboSubService.updateById(upd);
                return Result.ok();
            }
            String message=junboLlbResult.getData()!=null?junboLlbResult.getData().getMsg():junboLlbResult.getMessage();
            return Result.error(message);
        } else {
            return Result.error("贵州移动存量业务无需提交短信验证码");
        }
    }
    public static Map<String, String> getURLParameters(String urlString) {
        Map<String, String> params = Maps.newHashMap();
        try {
            URL url = new URL(urlString);
            String query = url.getQuery();

            if (query != null) {
                String[] pairs = query.split("&");
                for (String pair : pairs) {
                    int idx = pair.indexOf("=");
                    String key = pair.substring(0, idx);
                    String value = pair.substring(idx + 1);
                    params.put(key, value);
                }
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return params;
    }
}
