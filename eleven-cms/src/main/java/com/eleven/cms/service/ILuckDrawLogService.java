package com.eleven.cms.service;

import com.eleven.cms.entity.LuckDrawLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.FebsResponse;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 抽奖记录
 * @Author: jeecg-boot
 * @Date:   2023-04-21
 * @Version: V1.0
 */
public interface ILuckDrawLogService extends IService<LuckDrawLog> {

    FebsResponse alipayLuck(String mobile,String channel,String productName,String consignee,String shippingAddress,String detailAddress,String consigneeMobile,String prizeName,String prizeType,String luckDrawStatus);
}
