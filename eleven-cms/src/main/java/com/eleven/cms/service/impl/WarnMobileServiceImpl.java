package com.eleven.cms.service.impl;

import com.eleven.cms.entity.WarnMobile;
import com.eleven.cms.mapper.WarnMobileMapper;
import com.eleven.cms.service.IDatangSmsService;
import com.eleven.cms.service.IWarnMobileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: cms_warn_mobile
 * @Author: jeecg-boot
 * @Date:   2023-05-05
 * @Version: V1.0
 */
@Service
public class WarnMobileServiceImpl extends ServiceImpl<WarnMobileMapper, WarnMobile> implements IWarnMobileService {

    @Autowired
    private IDatangSmsService datangSmsService;

    @Override
    public List<String> smsWarn(String warnType, String businessType) {
        List<String> mobiles = new ArrayList<>();
        //查询配置
        List<WarnMobile> list = this.lambdaQuery()
                .eq(WarnMobile::getWarnType, warnType)
                .eq(WarnMobile::getBusinessType, businessType)
                .list();
        for (WarnMobile warnMobile : list) {
            mobiles.add(warnMobile.getMobile());
        }
        return mobiles;
    }
}
