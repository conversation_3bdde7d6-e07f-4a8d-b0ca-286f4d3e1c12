package com.eleven.cms.service;

import com.eleven.cms.dto.XunYouQueryResp;
import com.eleven.cms.dto.XunYouRechargeResp;
import com.eleven.cms.entity.JunboChargeLog;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/4 9:35
 **/
public interface IXunYouRechargeService {
    XunYouRechargeResp recharge(JunboChargeLog junboChargeLog);
    boolean refund(JunboChargeLog junboChargeLog);
    XunYouQueryResp query(JunboChargeLog junboChargeLog);
}
