package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.DouyinAppConfig;
import com.eleven.cms.mapper.DouyinAppConfigMapper;
import com.eleven.cms.service.IDouyinAppConfigService;
import org.springframework.stereotype.Service;

/**
 * @Description: douyin_app_config
 * @Author: jeecg-boot
 * @Date: 2025-06-24
 * @Version: V1.0
 */
@Service
public class DouyinAppConfigServiceImpl extends ServiceImpl<DouyinAppConfigMapper, DouyinAppConfig> implements IDouyinAppConfigService {

    @Override
    public DouyinAppConfig getByBusinessType(String businessType) {
        return lambdaQuery().eq(DouyinAppConfig::getBusinessType, businessType).eq(DouyinAppConfig::getIsDeleted, 0).last("limit 1").one();
    }

    @Override
    public DouyinAppConfig getDefaultConfig() {
        // 获取默认配置，可以通过业务类型为"default"或者第一个有效配置
        DouyinAppConfig defaultConfig = lambdaQuery()
                .eq(DouyinAppConfig::getBusinessType, "default")
                .eq(DouyinAppConfig::getIsDeleted, 0)
                .last("limit 1")
                .one();

        if (defaultConfig == null) {
            // 如果没有默认配置，获取第一个有效配置
            defaultConfig = lambdaQuery()
                    .eq(DouyinAppConfig::getIsDeleted, 0)
                    .last("limit 1")
                    .one();
        }

        return defaultConfig;
    }
}
