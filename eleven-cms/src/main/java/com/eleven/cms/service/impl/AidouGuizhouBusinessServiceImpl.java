package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.SuccessLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.AidoulaidianService;
import com.eleven.cms.remote.HeyuYidongVrbtService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.AidouGuizhouResult;
import com.eleven.cms.vo.HeyuYidongVrbtResult;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Random;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class AidouGuizhouBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    AidoulaidianService aidoulaidianService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;


    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        return SpringContextUtils.getBean(AidouGuizhouBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    @ValidationLimit
    @SuccessLimit
    public Result receiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            String ispOrderNo = RandomStringUtils.randomNumeric(20);
            boolean result = aidoulaidianService.getSms(mobile, subscribe.getIp(), ispOrderNo);
            if (result) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                subscribe.setIspOrderNo(ispOrderNo);
                subscribeService.createSubscribeDbAndEs(subscribe);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                return Result.noauth("验证码已发送", subscribe.getId());
            } else {
                return Result.error("获取验证码失败");
            }
        } else {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            AidouGuizhouResult result = aidoulaidianService.smsCode(mobile, smsCode);
            Subscribe upd = new Subscribe();
            upd.setId(transactionId);
            if (result.isOK()) {
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setOpenTime(new Date());
                upd.setResult("提交验证码成功");
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.ok("提交验证码成功");
            } else {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result.getMsg());
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error("订阅失败");
            }
        }
    }
}
