package com.eleven.cms.service;

import com.eleven.cms.entity.YinglouRing;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.util.AESUtils;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 影楼铃音表
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
public interface IYinglouRingService extends IService<YinglouRing> {
    void ringUpload(String mobile,String circleId,String channel,String ringUrl);

    void aliRingUpload(String jobId,String ringUrl);

    void ringUploadScheduleDeduct(String id);

    String createTemplateRing(String mobile, String circleId, String templateId, String clipsParam, String channel);

    Result<?> isAliVideoRing(String aliVideoJobId);

    Result<?> isCircleRing(String circleId);


    String aesEncryptMobile(String mobile,String channel);
    String aesDecryptMobile(String mobile,String channel);
}
