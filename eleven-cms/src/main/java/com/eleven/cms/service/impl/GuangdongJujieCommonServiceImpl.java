package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.SubChannel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.GuangdongJujieService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubChannelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.GuangdongJujieResult;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;
import static org.jeecg.common.constant.CacheConstant.CMS_CACHE_GDJJ_GOODSID_INFO;
import static org.jeecg.common.constant.CacheConstant.CMS_CACHE_GDJJ_SMSSERIALNUM_INFO;

/**
 * @author: cai lei
 * @create: 2024-08-13 14:21
 */
@Service("guangdongJujieCommonService")
public class GuangdongJujieCommonServiceImpl implements IBizCommonService {

    @Autowired
    GuangdongJujieService guangdongJujieService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubChannelService subChannelService;
    @Autowired
    ISubscribeService subscribeService;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        String goodsId = "";
        String goodsIdKey = CMS_CACHE_GDJJ_GOODSID_INFO + ":" + subscribe.getMobile();
        String smsSerialNumKey = CMS_CACHE_GDJJ_SMSSERIALNUM_INFO + ":" + subscribe.getMobile();
        SubChannel subChannel = subChannelService.findSubChannel(subscribe.getSubChannel());
        GuangdongJujieResult guangdongJujieResult = guangdongJujieService.getSms(subscribe.getMobile(), goodsId, subChannel != null ? subChannel.getAdPlatform() : "", subscribe.getReferer(), subscribe.getIp(), subscribe.getUserAgent());
        if (guangdongJujieResult.isSmsOK()) {
            subscribeService.createSubscribeDbAndEs(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            redisUtil.set(goodsIdKey, goodsId, 300L);
            if (StringUtils.isNotBlank(guangdongJujieResult.getBiz().getSmsSerialNum())) {
                redisUtil.set(smsSerialNumKey, guangdongJujieResult.getBiz().getSmsSerialNum(), 300L);
            }
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
            return Result.error("获取验证码失败");
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        String goodsIdKey = CMS_CACHE_GDJJ_GOODSID_INFO + ":" + subscribe.getMobile();
        String goodsId = (String) redisUtil.get(goodsIdKey);
        String ispOrderNo = "JJ" + System.currentTimeMillis();
        SubChannel subChannel = subChannelService.findSubChannel(subscribe.getSubChannel());
        GuangdongJujieResult guangdongJujieResult = guangdongJujieService.order(mobile, goodsId, ispOrderNo, smsCode, subChannel != null ? subChannel.getAdPlatform() : "", subscribe.getReferer(), subscribe.getIp(), subscribe.getUserAgent());
        if (guangdongJujieResult.isOrderOK()) {
            upd.setIspOrderNo(ispOrderNo);
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("提交验证码成功");
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setResult(guangdongJujieResult.getBiz().getMsg());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("提交验证码失败");
        }
    }
}
