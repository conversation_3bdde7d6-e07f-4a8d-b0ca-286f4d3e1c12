package com.eleven.cms.service.impl;

import com.eleven.cms.dto.YinglouUserDto;
import com.eleven.cms.entity.YinglouUser;
import com.eleven.cms.mapper.YinglouUserMapper;
import com.eleven.cms.service.IYinglouUserService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 影楼用户表
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
@Service
public class YinglouUserServiceImpl extends ServiceImpl<YinglouUserMapper, YinglouUser> implements IYinglouUserService {

    @Override
    public List<YinglouUserDto> queryMajorUser() {
        return this.baseMapper.findMajorUserByLevels();
    }
}
