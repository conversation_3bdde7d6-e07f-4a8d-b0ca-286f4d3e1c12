package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.CmsAlarmUserConfigDetail;
import com.eleven.cms.entity.CmsAlarmUserConfigDetailDto;
import com.eleven.cms.mapper.CmsAlarmUserConfigDetailMapper;
import com.eleven.cms.service.ICmsAlarmUserConfigDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: cms_alarm_user_config_detail
 * @Author: jeecg-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class CmsAlarmUserConfigDetailServiceImpl extends ServiceImpl<CmsAlarmUserConfigDetailMapper, CmsAlarmUserConfigDetail> implements ICmsAlarmUserConfigDetailService {

    private final CmsAlarmUserConfigDetailMapper alarmUserConfigDetailMapper;

    /**
     * 查询分页列表
     *
     * @param cmsAlarmUserConfigDetail cmsAlarmUserConfigDetail
     * @return IPage<CmsAlarmUserConfigDetail>
     */
    @Override
    public IPage<CmsAlarmUserConfigDetail> queryPageList(IPage<CmsAlarmUserConfigDetail> page, CmsAlarmUserConfigDetail cmsAlarmUserConfigDetail) {
        return alarmUserConfigDetailMapper.queryPageList(page, cmsAlarmUserConfigDetail);
    }

    @Override
    public List<CmsAlarmUserConfigDetailDto> getAlarmUserList(String channelCode, String alarmSceneCode) {
        return alarmUserConfigDetailMapper.getAlarmUserList(channelCode,alarmSceneCode);
    }
}
