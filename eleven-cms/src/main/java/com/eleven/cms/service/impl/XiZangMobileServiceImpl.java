package com.eleven.cms.service.impl;

import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.eleven.cms.ad.XiZangMobileProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IXiZangMobileService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.SignatureUtils;
import com.eleven.cms.vo.XiZangMobileCheckSmsResult;
import com.eleven.cms.vo.XiZangMobileSendSmsResult;
import com.eleven.cms.vo.XiZangMobileSubmitOrderResult;
import com.eleven.cms.vo.XiZangMobileTokenResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 西藏移动业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/28 9:47
 **/
@Slf4j
@Service
public class XiZangMobileServiceImpl implements IXiZangMobileService {
    public static final String LOG_TAG = "西藏移动业务";
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    XiZangMobileProperties xiZangMobileProperties;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    private SymmetricCrypto  des;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private Interceptor xiZangMobileIntercept;
    @PostConstruct
    public void init() {
//        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().addInterceptor(xiZangMobileIntercept).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
        des = new SymmetricCrypto(SymmetricAlgorithm.DES, xiZangMobileProperties.getDesKeyPad().getBytes());
    }
    @Override
    public Result<?> sendSms(String mobile, String accessToken, String orderId, String channel) {

        ObjectNode busiParam = mapper.createObjectNode();
        ObjectNode input = mapper.createObjectNode();
        input.put("smsContent", xiZangMobileProperties.getProductMap().get(channel).getSmsContent());
        input.put("userMobile",mobile);
        busiParam.putPOJO("input",input);
        ObjectNode pubInfo = mapper.createObjectNode();
        pubInfo.put("countyCode","1001");
        pubInfo.put("opId",xiZangMobileProperties.getOperatorId());
        pubInfo.put("orgId",xiZangMobileProperties.getChannelId());
        pubInfo.put("regionCode","891");
        busiParam.putPOJO("pubInfo",pubInfo);

        Map<String, String> sysParam = new HashMap();
        sysParam.put("method", xiZangMobileProperties.getSendSmsMethod());
        sysParam.put("format", "json");
        sysParam.put("appId", xiZangMobileProperties.getAppId());
        sysParam.put("operId", xiZangMobileProperties.getOperatorId());
        sysParam.put("version", "1.0");
        sysParam.put("accessToken",accessToken);
        sysParam.put("timestamp", DateUtil.formatFullTime(LocalDateTime.now()));
        sysParam.put("busiSerial", orderId);
//        sysParam.put("sign",SignatureUtils.signWithSHA256(sysParam,busiParam.toString(), xiZangMobileProperties.getAppKey()));
        RequestBody body = RequestBody.create(JSON,busiParam.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(xiZangMobileProperties.getOppfUrl()).newBuilder();
        sysParam.forEach((httpUrlBuilder::addQueryParameter));
        log.info("{}-获取验证码,请求数据=>手机号:{},url:{},请求参数:{}",LOG_TAG,mobile,httpUrlBuilder,busiParam);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            XiZangMobileSendSmsResult result = mapper.readValue(content, XiZangMobileSendSmsResult.class);
            if(result.isOK()){
                return Result.ok("获取验证码成功");
            }
            if(result.getResult()!=null && result.getResult().getResult()!=null && StringUtils.isNotBlank(result.getResult().getResult().getMessage())){
                return Result.error(result.getResult().getResult().getMessage(),content);
            }
            return Result.error(result.getRespDesc(),content);
        } catch (IOException e) {
            log.error("{}-获取验证码,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        String resultMsg = "{\"resCode\":\"500\",\"resMsg\":\"获取验证码接口请求异常\"}";
        return Result.error("系统异常",resultMsg);
    }


    @Override
    public Result<?> authSms(String mobile, String accessToken, String orderId,String code) {

        ObjectNode busiParam = mapper.createObjectNode();
        ObjectNode input = mapper.createObjectNode();
        String encryptHex = des.encryptHex(code);
        input.put("password",encryptHex);
        input.put("userMobile",mobile);
        busiParam.putPOJO("input",input);
        ObjectNode pubInfo = mapper.createObjectNode();
        pubInfo.put("countyCode","1001");
        pubInfo.put("opId",xiZangMobileProperties.getOperatorId());
        pubInfo.put("orgId",xiZangMobileProperties.getChannelId());
        pubInfo.put("regionCode","891");
        busiParam.putPOJO("pubInfo",pubInfo);
        Map<String, String> sysParam = new HashMap();
        sysParam.put("method", xiZangMobileProperties.getAuthMethod());
        sysParam.put("format", "json");
        sysParam.put("appId", xiZangMobileProperties.getAppId());
        sysParam.put("operId", xiZangMobileProperties.getOperatorId());
        sysParam.put("version", "1.0");
        sysParam.put("accessToken",accessToken);
        sysParam.put("timestamp", DateUtil.formatFullTime(LocalDateTime.now()));
        sysParam.put("busiSerial", orderId);
//        sysParam.put("sign",SignatureUtils.signWithSHA256(sysParam,busiParam.toString(), xiZangMobileProperties.getAppKey()));
        RequestBody body = RequestBody.create(JSON,busiParam.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(xiZangMobileProperties.getOppfUrl()).newBuilder();
        sysParam.forEach((httpUrlBuilder::addQueryParameter));
        log.info("{}-校验验证码,请求数据=>手机号:{},短信验证码:{},url:{},请求参数:{}",LOG_TAG,mobile,code,httpUrlBuilder,busiParam);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-校验验证码,响应数据=>手机号:{},短信验证码:{},响应参数:{}",LOG_TAG,mobile,code,content);
            XiZangMobileCheckSmsResult result = mapper.readValue(content, XiZangMobileCheckSmsResult.class);
            if(result.isOK()){
                return Result.ok("校验验证码成功");
            }
            return Result.error(result.getRespDesc(),content);
        } catch (IOException e) {
            log.error("{}-校验验证码,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        String resultMsg = "{\"resCode\":\"500\",\"resMsg\":\"校验验证码接口请求异常\"}";
        return Result.error("系统异常",resultMsg);
    }

    @Override
    public Result<?> submitOrder(String mobile, String accessToken, String orderId, String channel) {
        ObjectNode busiParam = mapper.createObjectNode();
        List<Map<String,String>> selectedOfferValue= Lists.newArrayList();
        Map<String,String> selectedOfferValueMap= Maps.newHashMap();
        selectedOfferValueMap.put("action","1");
        selectedOfferValueMap.put("offerId",xiZangMobileProperties.getProductMap().get(channel).getProductId());
        selectedOfferValue.add(selectedOfferValueMap);
        ObjectNode input = mapper.createObjectNode();
        input.putPOJO("selectedOfferValue",selectedOfferValue);
        input.put("accessNum",mobile);
        busiParam.putPOJO("input",input);
        ObjectNode pubInfo = mapper.createObjectNode();
        pubInfo.put("countyCode","1001");
        pubInfo.put("opId",xiZangMobileProperties.getOperatorId());
        pubInfo.put("orgId",xiZangMobileProperties.getChannelId());
        pubInfo.put("regionCode","891");
        busiParam.putPOJO("pubInfo",pubInfo);
        Map<String, String> sysParam = new HashMap();
        sysParam.put("method", xiZangMobileProperties.getOrderMethod());
        sysParam.put("format", "json");
        sysParam.put("appId", xiZangMobileProperties.getAppId());
        sysParam.put("operId", xiZangMobileProperties.getOperatorId());
        sysParam.put("version", "1.0");
        sysParam.put("accessToken",accessToken);
        sysParam.put("timestamp", DateUtil.formatFullTime(LocalDateTime.now()));
        sysParam.put("busiSerial", orderId);
//        sysParam.put("sign",SignatureUtils.signWithSHA256(sysParam,busiParam.toString(), xiZangMobileProperties.getAppKey()));
        RequestBody body = RequestBody.create(JSON,busiParam.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(xiZangMobileProperties.getOppfUrl()).newBuilder();
        sysParam.forEach((httpUrlBuilder::addQueryParameter));
        log.info("{}-提交订单,请求数据=>手机号:{},url:{},请求参数:{}",LOG_TAG,mobile,httpUrlBuilder,busiParam);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交订单,响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            XiZangMobileSubmitOrderResult result = mapper.readValue(content, XiZangMobileSubmitOrderResult.class);
            if(result.isOK()){
                return Result.ok("提交订单成功",content);
            }
            return Result.error(result.getRespDesc(),content);
        } catch (IOException e) {
            log.error("{}-提交订单,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        String resultMsg = "{\"resCode\":\"500\",\"resMsg\":\"提交订单接口请求异常\"}";
        return Result.error("系统异常",resultMsg);

    }
    @Override
    public String getToken(String mobile){
        if(redisUtil.hasKey(CacheConstant.XIZANG_MOBILE_TOKEN_RESULT)){
            return (String)redisUtil.get(CacheConstant.XIZANG_MOBILE_TOKEN_RESULT);
        }
        final HttpUrl tokenUrl = HttpUrl.parse(xiZangMobileProperties.getTokenUrl())
                .newBuilder()
                .addQueryParameter("app_id", xiZangMobileProperties.getAppId())
                .addQueryParameter("app_key",  xiZangMobileProperties.getAppKey())
                .addQueryParameter("grant_type", "client_credentials")
                .build();
        log.info("{}-获取token,请求数据=>手机号:{},url:{}",LOG_TAG,mobile,tokenUrl);
        Request request = new Request.Builder().url(tokenUrl).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取token,响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            XiZangMobileTokenResult result = mapper.readValue(content, XiZangMobileTokenResult.class);
            if(result.isOK()){
                final Integer expiresIn = result.getExpiresIn();
                redisUtil.set(CacheConstant.XIZANG_MOBILE_TOKEN_RESULT,result.getAccessToken(), expiresIn >10 ? expiresIn -10 : expiresIn);
                return result.getAccessToken();
            }
        } catch (IOException e) {
            log.error("{}-获取token,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        return null;
    }

    @Override
    public Result<?> sendMessage(Subscribe subscribe) {
        //获取token
        String accessToken=this.getToken(subscribe.getMobile());
        if(StringUtils.isBlank(accessToken)){
            String resultMsg = "{\"resCode\":\"500\",\"resMsg\":\"获取accessToken失败\"}";
            return Result.error("系统异常",resultMsg);
        }
        //获取验证码
        return this.sendSms(subscribe.getMobile(),accessToken,subscribe.getIspOrderNo(),subscribe.getChannel());
    }

    @Override
    public Result<?> submitOrder(Subscribe subscribe) {
        //获取token
        String accessToken=this.getToken(subscribe.getMobile());
        if(StringUtils.isBlank(accessToken)){
            String resultMsg = "{\"resCode\":\"500\",\"resMsg\":\"获取accessToken失败\"}";
            return Result.error("系统异常",resultMsg);
        }
//        //校验验证码
//        Result<?> result=this.authSms(subscribe.getMobile(),accessToken,subscribe.getIspOrderNo(),subscribe.getSmsCode());
//        if(!result.isOK()){
//            return result;
//        }
        //提交订单
        return this.newSubmitOrder(subscribe.getMobile(),accessToken,subscribe.getIspOrderNo(),subscribe.getChannel(),subscribe.getSmsCode());
    }
    @Override
    public Result<?> newSubmitOrder(String mobile, String accessToken, String orderId, String channel,String code) {
        ObjectNode busiParam = mapper.createObjectNode();
        List<Map<String,String>> selectedOfferValue= Lists.newArrayList();
        Map<String,String> selectedOfferValueMap= Maps.newHashMap();
        selectedOfferValueMap.put("action","1");
        selectedOfferValueMap.put("offerId",xiZangMobileProperties.getProductMap().get(channel).getProductId());
        selectedOfferValue.add(selectedOfferValueMap);
        ObjectNode input = mapper.createObjectNode();
        input.putPOJO("selectedOfferValue",selectedOfferValue);
        input.put("accessNum",mobile);
        String encryptHex = des.encryptHex(code);
        input.put("verifyCode",encryptHex);
        busiParam.putPOJO("input",input);
        ObjectNode pubInfo = mapper.createObjectNode();
        pubInfo.put("countyCode","1001");
        pubInfo.put("opId",xiZangMobileProperties.getOperatorId());
        pubInfo.put("orgId",xiZangMobileProperties.getChannelId());
        pubInfo.put("regionCode","891");
        busiParam.putPOJO("pubInfo",pubInfo);
        Map<String, String> sysParam = new HashMap();
        sysParam.put("method", xiZangMobileProperties.getOrderMethod());
        sysParam.put("format", "json");
        sysParam.put("appId", xiZangMobileProperties.getAppId());
        sysParam.put("operId", xiZangMobileProperties.getOperatorId());
        sysParam.put("version", "1.0");
        sysParam.put("accessToken",accessToken);
        sysParam.put("timestamp", DateUtil.formatFullTime(LocalDateTime.now()));
        sysParam.put("busiSerial", orderId);
        RequestBody body = RequestBody.create(JSON,busiParam.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(xiZangMobileProperties.getOppfUrl()).newBuilder();
        sysParam.forEach((httpUrlBuilder::addQueryParameter));
        log.info("{}-提交订单,请求数据=>手机号:{},短信验证码:{},url:{},请求参数:{}",LOG_TAG,mobile,code,httpUrlBuilder,busiParam);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交订单,响应数据=>手机号:{},短信验证码:{},响应参数:{}",LOG_TAG,mobile,code,content);
            XiZangMobileSubmitOrderResult result = mapper.readValue(content, XiZangMobileSubmitOrderResult.class);
            if(result.isOK()){
                return Result.ok("提交订单成功",content);
            }
            if(result.getResult()!=null && result.getResult().getResult()!=null && StringUtils.isNotBlank(result.getResult().getResult().getMessage())){
                return Result.error(result.getResult().getResult().getMessage(),content);
            }
            return Result.error(result.getRespDesc(),content);
        } catch (IOException e) {
            log.error("{}-提交订单,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        String resultMsg = "{\"resCode\":\"500\",\"resMsg\":\"提交订单接口请求异常\"}";
        return Result.error("系统异常",resultMsg);

    }
}
