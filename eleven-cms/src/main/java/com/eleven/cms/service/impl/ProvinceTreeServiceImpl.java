package com.eleven.cms.service.impl;

import com.eleven.cms.entity.ProvinceTree;
import com.eleven.cms.mapper.ProvinceTreeMapper;
import com.eleven.cms.service.IProvinceTreeService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: cms_province_tree
 * @Author: jeecg-boot
 * @Date:   2021-08-13
 * @Version: V1.0
 */
@Service
public class ProvinceTreeServiceImpl extends ServiceImpl<ProvinceTreeMapper, ProvinceTree> implements IProvinceTreeService {


    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_PROVINCE_CACHE, key = "#root.methodName", unless = "#result==null")
    public List<Object> getProvinceList() {
        return this.list().stream().map(provinceTree -> {
            Map<String, Object> map = new HashMap<>();
            map.put("title", provinceTree.getProvinceName());
            map.put("value", provinceTree.getProvinceName());
            return map;
        }).collect(Collectors.toList());
    }
}
