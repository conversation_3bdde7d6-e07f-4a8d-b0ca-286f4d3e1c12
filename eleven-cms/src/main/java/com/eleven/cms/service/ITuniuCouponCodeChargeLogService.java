package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.dto.NanshanOpenResponse;
import com.eleven.cms.entity.TuniuCouponCodeChargeLog;
import com.eleven.cms.vo.FebsResponse;

/**
 * @Description: 途牛券码充值记录
 * @Author: jeecg-boot
 * @Date:   2024-03-29
 * @Version: V1.0
 */
public interface ITuniuCouponCodeChargeLogService extends IService<TuniuCouponCodeChargeLog> {

    NanshanOpenResponse nanshanOpenNotify(String requestBody,String sign,String appKey);

    void sendCodeScheduleDeduct(String id);

    FebsResponse tuniuRecharge(String mobile, String couponCode);
}
