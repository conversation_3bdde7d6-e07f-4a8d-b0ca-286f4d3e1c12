package com.eleven.cms.service;

import com.eleven.cms.entity.ColumnMusicDy;
import com.eleven.cms.entity.ColumnDy;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 订阅包栏目
 * @Author: jeecg-boot
 * @Date:   2024-03-13
 * @Version: V1.0
 */
public interface IColumnDyService extends IService<ColumnDy> {

	/**
	 * 添加一对多
	 * 
	 */
	public void saveMain(ColumnDy columnDy,List<ColumnMusicDy> columnMusicDyList) ;
	
	/**
	 * 修改一对多
	 * 
	 */
	public void updateMain(ColumnDy columnDy,List<ColumnMusicDy> columnMusicDyList);
	
	/**
	 * 删除一对多
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
