package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.YidongVrbtNewCrackService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.BizMixedResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;

/**
 * 音乐包/权益包/渠道包业务类
 * 会根据省份切换渠道号
 * @author: cai lei
 * @create: 2023-08-01 16:37
 */
@Slf4j
@Service("cpmbCommonNewService")
public class CpmbCommonNewServiceImpl  implements IBizCommonService {

    @Autowired
    YidongVrbtNewCrackService yidongVrbtNewCrackService;
    @Autowired
    MiguApiService miguApiService;


    @Override
    @ValidationLimit
    @IpLimit
    public Result getSmsCode(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);
        ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        // if (!MobileRegionResult.ISP_YIDONG.equals(subscribe.getIsp())) {
        //     return Result.msgIspRestrict();
        // }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + mobile;
        if (redisUtil.hasKey(mobileSendSmsLimitKey)) {
            return Result.msgSendSmsTooFrequent();
        }
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.msgRepeatSub();
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //此时才能得到subscribe的主键id
        final String id = subscribe.getId();
        //调用音乐包复合查询接口
        final BizMixedResult bizMixedResult = miguApiService.bizMixedQuery(mobile, subscribe.getChannel(), BizConstant.BIZ_TYPE_CPMB);

        //返回码为000000表示已订购
        if(bizMixedResult.isMonth()){
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setStatus(SUBSCRIBE_STATUS_MONTHLY_EXISTS);
            upd.setResult("已有包月");
            subscribeService.updateSubscribeDbAndEs(upd);
            //设置重复订购缓存
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            return Result.msgRepeatSub();
        }
        //判定购物车第二个serviceId
        //获取验证码接口新增一个serviceId字段
        //1、若不传则跟现在一样，单独订购权益包包月
        //2、若传了serviceId则会通过购物车打包订购  权益包包月+传入的serviceId
        //这样的话就会更灵活，你那根据业务需求控制是否单个或者多个id
        //举例，华岸权益包：
        //四川用户就这样传http://open.hxypw.cn/crack/getsms?phone=13608182222&paycode=HYM00211DI&serviceId=698039020100000134
        //全国（非四川广东浙江重庆）就这样传
        //http://open.hxypw.cn/crack/getsms?phone=13608182222&paycode=HYM00211DI&serviceId=600923018000000007
        //1.主叫只能购物车（购物车就你传主叫的serviceId我们这打包开通)，
        //2.四川因为要购物车打包开通两元特惠包，只有你那调用现有开通接口开被叫
        //3.广东浙江重庆，你传serviceId或者直接开通接口开，都可以
        //        String serviceId = null;
        //        if(bizMixedResult.isNeedCrbtTehuiPack()){
        //            serviceId = bizMixedResult.isCrbtTehuiPack() ? null : MiguApiService.CPMB_CRBT_MONTH_SERVICE_ID;
        //        }else {
        //            serviceId = bizMixedResult.isVrbtFun() ? null : MiguApiService.CPMB_VRBT_FUN_ACTIVE_SERVICE_ID;
        //        }
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtNewCrackService.class).getSmsCpmb(mobile, bizMixedResult.getFinalChannel(), bizMixedResult.getCartSecondServiceId());
        if(billingResult.isOK()) {
            //设置重复发送短信缓存
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            Subscribe upd = new Subscribe();
            upd.setId(id);
            upd.setChannel(bizMixedResult.getFinalChannel());
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("验证码已发送", id);
        }else {
            //{"code":"402","message":"请求验证码过快!"}
            //验证码请求失败,丢弃此条记录
            if("402".equals(billingResult.getCode())){
                subscribeService.removeSubscribeDbAndEs(id);
            }
            return Result.error("系统繁忙,请稍后再试!");
        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {

        RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);
        ISubscribeService subscribeService = SpringContextUtils.getBean(ISubscribeService.class);

        String smsCode = subscribe.getSmsCode();
        //验证短信验证码是否合法
        if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
            return Result.captchaErr("短信验证码错误");
        }
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = MOBILE_SMS_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, MOBILE_SMS_DUPLICATE_KEY_PREFIX, MOBILE_SMS_DUPLICATE_CACHE_SECONDS);
        //此处保存已提交验证码
        if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setBizTime(new Date());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        yidongVrbtNewCrackService.smsCode(subscribe.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
        return Result.ok("提交验证码成功");
    }
}
