package com.eleven.cms.service.impl;

import com.eleven.cms.dto.Rights;
import com.eleven.cms.entity.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 联通沃悦读单次扣款包月校验
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/27 15:19
 **/
@Slf4j
@Service
public class WoReadShopMemberRightsServiceImpl implements IBusinessRightsSubService {

    private static final String LOG_TAG = "联通沃悦读单次扣款[包月校验]";
    private static final String LOG_TAG_ERROR = "联通沃悦读单次扣款[包月校验异常]";
    private static final String BIZ_TYPE_WO_READ_SHOP="WO_READ_SHOP";
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private IWoReadSinglePayOrderService woReadSinglePayOrderService;
    @Override
    public FebsResponse memberVerify(String mobile,String serviceId) {
        Date payTime=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(payTime==null){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        return new FebsResponse().success();
    }
    @Override
    public FebsResponse rechargRecordVerify(String mobile,String serviceId) {
        final List<String> businessIdList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).eq(BusinessChannelRights::getRemark,BIZ_TYPE_WO_READ_SHOP).select(BusinessChannelRights::getId).list().stream().map(BusinessChannelRights::getId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("{}-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId);
            return new FebsResponse().fail().message("业务渠道权益关联未正确配置");
        }

        final List<String> channelList= businessPackService.lambdaQuery().in(BusinessPack::getBusinessId,businessIdList).select(BusinessPack::getServiceId).list().stream().map(BusinessPack::getServiceId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("{}-指定渠道权益未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId);
            return new FebsResponse().fail().message("指定渠道权益未正确配置");
        }
        //联通沃悦读单次扣款权益已全部发放
        Boolean monthlyIsRecharge=rightsSubService.woReadShopMonthlyIsRecharge(mobile,serviceId,channelList);
        if(!monthlyIsRecharge){
            return new FebsResponse().repeatRecharge();
        }
        return new FebsResponse().success();
    }

    /**
     * 当月是否订购
     * @param mobile
     * @return
     */
    private Date monthlyIsSub(String mobile,String serviceId) {
        try {
            //查询沃悦读订单
            Date woReadOrderPayTime=this.woReadRechargeIsMember(mobile,serviceId);
            if(woReadOrderPayTime!=null){
                return woReadOrderPayTime;
            }
        } catch (Exception e) {
            log.error("{}-手机号:{},权益领取业务ID:{}",LOG_TAG_ERROR, mobile,serviceId,e);
        }
        return null;
    }



    /**
     * 校验是否已支付沃阅读订单
     * @param mobile
     * @return
     */
    private Date woReadRechargeIsMember(String mobile,String serviceId) {
        WoReadSinglePayOrder woReadOrder= woReadSinglePayOrderService.lambdaQuery()
                .eq(WoReadSinglePayOrder::getMobile,mobile)
                .eq(WoReadSinglePayOrder::getOrderStatus,1)
                .eq(WoReadSinglePayOrder::getBusinessType, serviceId)
                .between(WoReadSinglePayOrder::getPayTime, DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .orderByDesc(WoReadSinglePayOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(woReadOrder==null){
            return null;
        }
        log.info("{}-手机号:{},订购状态:{}",LOG_TAG, mobile,woReadOrder.getOrderStatus());
        return woReadOrder.getPayTime();
    }

    /**
     * 创建预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse createScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {

        Date payTime=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(payTime==null){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getScheduledTime(mobile,serviceId,packName);
        if(scheduledTime==null){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        Rights rights=new Rights();
        String couponId=rightsSubService.setRightCofig(rights,rightsId,payTime);
        if(StringUtils.isBlank(couponId)){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        final List<String> businessIdList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).eq(BusinessChannelRights::getRemark,BIZ_TYPE_WO_READ_SHOP).select(BusinessChannelRights::getId).list().stream().map(BusinessChannelRights::getId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("{}-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId);
            return new FebsResponse().fail().message("业务渠道权益关联未正确配置");
        }

        final List<String> channelList= businessPackService.lambdaQuery().in(BusinessPack::getBusinessId,businessIdList).select(BusinessPack::getServiceId).list().stream().map(BusinessPack::getServiceId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("{}-指定渠道权益未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId);
            return new FebsResponse().fail().message("指定渠道权益未正确配置");
        }
        //校验当月联通沃悦读单次扣款权益是否已发放
        FebsResponse febsResponse=rightsSubService.woReadShopIsRecharge(mobile,packName,scheduledTime,couponId,channelList);
        if(!febsResponse.isOK()){
            return febsResponse;
        }
        rights.setRechargeSource("login");
        rights.setChannel(channel);
        rights.setSubChannel(rightsSubService.nowIsSub(mobile,channel));
        junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
        return new FebsResponse().readyRecharge(rights.getRightsName());
    }

    /**
     * 创建网页权益预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse webCreateScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {
        //业务类型去重
        final List<String> businessIdList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).eq(BusinessChannelRights::getRemark,BIZ_TYPE_WO_READ_SHOP).select(BusinessChannelRights::getId).list().stream().map(BusinessChannelRights::getId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("{}-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId,channel);
            return new FebsResponse().fail().message("业务渠道权益关联未正确配置");
        }

        final List<String> channelList= businessPackService.lambdaQuery().in(BusinessPack::getBusinessId,businessIdList).select(BusinessPack::getServiceId).list().stream().map(BusinessPack::getServiceId).collect(Collectors.toList());
        if(businessIdList==null || businessIdList.isEmpty()){
            log.info("{}-指定渠道权益未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",LOG_TAG,mobile,serviceId,channel);
            return new FebsResponse().fail().message("指定渠道权益未正确配置");
        }

        Boolean everydayIsSub=rightsSubService.everydayIsSub(mobile,channelList);
        //未查询到页面订购数据
        if(!everydayIsSub){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        Date payTime=this.monthlyIsSub(mobile,serviceId);
        //未包月
        if(payTime==null){
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        //获取预约充值时间
        LocalDateTime scheduledTime=rightsSubService.getWebScheduledTime(mobile,serviceId,packName);
        if(scheduledTime==null){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        Rights rights=new Rights();
        //获取业务权益配置
        String couponId=rightsSubService.setRightCofig(rights,rightsId,payTime);
        if(StringUtils.isBlank(couponId)){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        //校验当月联通沃悦读单次扣款权益是否已发放
        FebsResponse febsResponse=rightsSubService.woReadShopIsRecharge(mobile,packName,scheduledTime,couponId,channelList);
        if(!febsResponse.isOK()){
            return febsResponse;
        }
        rights.setRechargeSource("web");
        rights.setChannel(channel);
        rights.setSubChannel(rightsSubService.nowIsSub(mobile,channel));
        junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
        return new FebsResponse().readyRecharge(rights.getRightsName());
    }



    @Override
    public FebsResponse rechargeForScheduleVerify(JunboChargeLog junboChargeLog) {
        String mobile=junboChargeLog.getMobile();
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=junboChargeLogService.findByMobileAndPackNameAndDate(mobile,junboChargeLog.getPackName());
        //判断当月是否有已充值成功或者充值中的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()) || BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasSuccess){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,当月已充值或充值中");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            updateAlipayRightsStatus(junboChargeLog);
            return new FebsResponse().fail();
        }
        //是否订购业务功能
        Date payTime=this.monthlyIsSub(junboChargeLog.getMobile(),junboChargeLog.getServiceId());
        //未包月
        if(payTime==null){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,订单已退订或暂停");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            updateAlipayRightsStatus(junboChargeLog);
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }
    @Override
    public void rechargeForSchedule(JunboChargeLog junboChargeLog) {
        junboChargeLog=junboChargeLogService.taskRechargeForSchedule(junboChargeLog);
        updateAlipayRightsStatus(junboChargeLog);
    }
    /**
     * 更新订单领取状态
     * @param junboChargeLog
     */
    @Override
    public void updateRechargeState(JunboChargeLog junboChargeLog) {
        updateAlipayRightsStatus(junboChargeLog);
    }

    /**
     * 更新联通沃悦读单次扣款订单领取权益状态
     */
    private void updateAlipayRightsStatus(JunboChargeLog junboChargeLog) {
        //查询最新支付订单

    }
}
