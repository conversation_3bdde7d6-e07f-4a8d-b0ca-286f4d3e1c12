package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.NingxiaYidongService;
import com.eleven.cms.service.*;
import com.eleven.cms.vo.GansuMobileResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SUCCESS;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service("ningxiaYidongCommonService")
public class NingxiaYidongCommonServiceImpl implements IBizCommonService {

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    NingxiaYidongService ningxiaYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;


    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        try {
            GansuMobileResult gansuMobileResult = ningxiaYidongService.getSms(subscribe.getMobile(),subscribe.getChannel());
            if (gansuMobileResult.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                subscribeService.createSubscribeDbAndEs(subscribe);
                //写渠道订阅日志
                BizLogUtils.logSubscribe(subscribe);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
//                return Result.error("获取验证码失败");

                try {
                    String errorMsg="{\"status\":\""+gansuMobileResult.getStatus()+"\",\"code\":\""+gansuMobileResult.getCode()+"\",\"message\":\""+gansuMobileResult.getResMsg()+"\",\"result\":\""+gansuMobileResult.getResult()+"\"}";
                    return Result.errorSmsMsg(errorMsg);
                } catch (Exception e) {
                    log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                    return Result.msgSmsError();
                }
            }
        } catch (Exception e) {
            log.error("宁夏移动业务获取验证码错误:", e);
            return Result.error("系统错误，请稍后再试");

        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        try {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setBizTime(new Date());
            GansuMobileResult gansuMobileResult = ningxiaYidongService.smsCode(subscribe.getMobile(), subscribe.getSmsCode(),subscribe.getChannel());
            if (gansuMobileResult.isOK()) {
                //订阅成功
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                upd.setResult(gansuMobileResult.getResMsg());
                subscribeService.updateSubscribeDbAndEs(upd);
                //信息流广告转化上报
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                } else {
                    channelService.AdEffectFeedbackNew(subscribeService.getById(subscribe.getId()), SUBSCRIBE_STATUS_SUCCESS);
                }
                return Result.ok("订阅成功");
            } else {
                //订阅成功
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setOpenTime(new Date());
                upd.setResult(gansuMobileResult.getResMsg());
                subscribeService.updateSubscribeDbAndEs(upd);
                return Result.error(gansuMobileResult.getResMsg());
            }
        }catch (Exception e){
            log.error("宁夏移动业务提交验证码错误:", e);
            return Result.error("系统错误，请稍后再试");
        }
    }
}
