package com.eleven.cms.service;

import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.Result;

/**
 * 西藏移动业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/28 9:46
 **/
public interface IXiZangMobileService {
    String getToken(String mobile);

    Result<?> sendSms(String mobile, String accessToken, String orderId, String channel);

    Result<?> authSms(String mobile, String accessToken, String orderId,String code);

    Result<?> submitOrder(String mobile,String accessToken,String orderId,String channel);

    Result<?> newSubmitOrder(String mobile, String accessToken, String orderId, String channel,String code);





    Result<?> sendMessage(Subscribe subscribe);

    Result<?> submitOrder(Subscribe subscribe);


}
