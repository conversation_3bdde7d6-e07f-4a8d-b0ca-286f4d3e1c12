package com.eleven.cms.service;

import com.eleven.cms.entity.AliSignRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.IvrResult;
import org.jeecg.common.api.vo.Result;

import javax.transaction.Transactional;
import java.util.Date;

/**
 * @Description: cms_ali_sign_record
 * @Author: jeecg-boot
 * @Date:   2022-12-29
 * @Version: V1.0
 */
public interface IAliSignRecordService extends IService<AliSignRecord> {

    Date saveSignRecord(Subscribe subscribe, String externalAgreementNo, String appId, String businessType, String businessName,String bizType);

    void updateStatus(AliSignRecord aliSignRecord,String outTradeNo);

    void aliSignScheduleDeduct(AliSignRecord aliSignRecord);

    Result<?> aliUnSign(String mobile);

    boolean isAliSignExist(String mobile);

    IvrResult alipayQuerySignAndRefund(String mobile,String bizType);

    IvrResult alipayUnSignAndRefund(String mobile,String bizType);

    @Transactional
    void updateDeductDayAndSendDeductMessage(AliSignRecord aliSignRecord, String deductDay);

    @Transactional
    void updateSendDeductMsgDay(AliSignRecord aliSignRecord, String sendDeductMsgDay);


    void sendDeductMsg(AliSignRecord aliSignRecord);

    boolean unSub(String mobile,String channel,String bizType);
}
