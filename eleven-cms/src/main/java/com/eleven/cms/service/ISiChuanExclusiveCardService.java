package com.eleven.cms.service;

import org.jeecg.common.api.vo.Result;

import java.util.Map;

/**
 * 四川个人名片
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 15:47
 **/
public interface ISiChuanExclusiveCardService {

    Result<?> queryOrderInfoDetails(String mobile);

    Map<String, Object> siChuanBusinessCardNotify(String phone,Integer status,String serialnum,Map<String, Object> map);

    Result<?> sendSms(String mobile,String userName,String channelNumber);

    Result<?> submitSms(String mobile,String userName,String code,String channelNumber);
}
