package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.MiguVrbtPayOrder;
import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: cms_migu_vrbt_pay_order
 * @Author: jeecg-boot
 * @Date:   2022-12-30
 * @Version: V1.0
 */
public interface IMiguVrbtPayOrderService extends IService<MiguVrbtPayOrder> {

    MiguVrbtPayOrder queryNotPayOrder(String orderId);

    void wechatModifyPayStatus(String outTradeNo, String transactionId, String resultCode,String mobile);

    void aliPayModifyPayStatus(String outTradeNo, String transactionId, String resultCode,String mobile);

    void miGuVrbtPayMQMsg(String id);

    MiguVrbtPayOrder queryOrder(String orderId);

    Result<?> aliPayRefund(String outTradeNo);

    Result<?> aliPayQueryRefund(String outTradeNo);

    Result<?> setRing(Subscribe subscribe);
}
