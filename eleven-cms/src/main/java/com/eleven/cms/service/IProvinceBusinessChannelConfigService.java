package com.eleven.cms.service;

import com.eleven.cms.entity.ProvinceBusinessChannelInfo;
import com.eleven.cms.entity.ProvinceBusinessChannelConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: cms_province_business_channel_config
 * @Author: jeecg-boot
 * @Date:   2022-08-17
 * @Version: V1.0
 */
public interface IProvinceBusinessChannelConfigService extends IService<ProvinceBusinessChannelConfig> {

	/**
	 * 添加一对多
	 * 
	 */
	public void saveMain(ProvinceBusinessChannelConfig provinceBusinessChannelConfig,List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList) ;
	
	/**
	 * 修改一对多
	 * 
	 */
	public void updateMain(ProvinceBusinessChannelConfig provinceBusinessChannelConfig,List<ProvinceBusinessChannelInfo> provinceBusinessChannelInfoList);
	
	/**
	 * 删除一对多
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

    /**
     * 渠道省份是否允许访问
     * @param channelCode
     * @param province
     * @return
     */
    public boolean allow(String channelCode, String province);

    /**
     * 获取渠道省份限量
     * @param channelCode
     * @param province
     * @return
     */
    public Integer getLimitByChannelAndProvince(String channelCode,String province);

    /**
     * 获取渠道省份限量手机号
     * @param channelCode
     * @param province
     * @return
     */
    public List<String> getLimitSendMobile(String channelCode,String province);

    /**
     * 获取渠道省份限量
     * @param channelCode
     * @param owner
     * @return
     */
    public Integer getLimitByChannelAndOwner(String channelCode, String province, String owner);


    /**
     * 获取渠道省份限量
     * @param channelCode
     * @return
     */
    public ProvinceBusinessChannelConfig getByChannel(String channelCode);


    /**
     * 获取渠道省份限量手机号
     * @param channelCode
     * @param province
     * @return
     */
    public List<String> getLimitSendCpaMobile(String channelCode,String province);


    /**
     * 获取渠道省份限量
     * @param channelCode
     * @return
     */
    public Integer getTotalLimitByChannelAndOwner(String channelCode,String owner);


    /**
     * 获取渠道省份限量手机号
     * @param channelCode
     * @param province
     * @return
     */
    public List<String> getTotalLimitSendMobile(String channelCode);

    /**
     * 获取渠道省份限量手机号
     * @param channelCode
     * @param province
     * @return
     */
    public List<String> getTotalCpaLimitSendMobile(String channelCode);




}
