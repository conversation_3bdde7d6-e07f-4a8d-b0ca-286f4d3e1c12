package com.eleven.cms.service.impl;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.IAlipayService;
import com.eleven.cms.service.IBusinessCommonService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.BillingResult;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class OutsideQyclBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;

    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂未开放,敬请期待!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        return SpringContextUtils.getBean(OutsideQyclBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    public Result receiveOrder(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                //            this.up dateById(upd);
                subscribeService.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
            return Result.ok("提交验证码成功");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final BillingResult billingResult = SpringContextUtils.getBean(YidongVrbtCrackService.class).getSmsZyhlQycl(subscribe.getMobile(), subscribe.getChannel());
        if (billingResult.isOK()) {
            subscribe.setIspOrderNo(billingResult.getTransId());
            subscribeService.createSubscribeDbAndEs(subscribe);
            return Result.noauth("验证码已发送", subscribe.getId());
        } else {
            return Result.error("短信发送失败,请稍后再试");
        }
    }
}
