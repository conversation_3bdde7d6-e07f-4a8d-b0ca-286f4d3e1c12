package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PageBusinessProvinceConfigVpop;
import com.eleven.cms.mapper.PageBusinessProvinceConfigVpopMapper;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IPageBusinessProvinceConfigVpopService;
import com.eleven.cms.util.BizCommonConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2023-08-09 10:12
 */
@Service
public class PageBusinessProvinceConfigVpopServiceImpl extends ServiceImpl<PageBusinessProvinceConfigVpopMapper, PageBusinessProvinceConfigVpop> implements IPageBusinessProvinceConfigVpopService {

    @Autowired
    MobileRegionService mobileRegionService;

    public static final ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public ObjectNode getPopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province) {
        PageBusinessProvinceConfigVpop pageBusinessProvinceConfigVpop = getPageBusinessProvinceConfigVpop(pageId, province);
        if (pageBusinessProvinceConfigVpop != null) {
            ObjectNode objectNode = mapper.createObjectNode();
            if (MobileRegionResult.ISP_DIANXIN.equals(isp)) {
                objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigVpop.getDianxinPop());
                objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigVpop.getDianxinTailQuantity());
                objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigVpop.getDianxinBizTypeId());
                objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigVpop.getDianxinRefPage());
            } else if (MobileRegionResult.ISP_LIANTONG.equals(isp)) {
                objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigVpop.getLiantongPop());
                objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigVpop.getLiantongTailQuantity());
                objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigVpop.getLiantongBizTypeId());
                objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigVpop.getLiantongRefPage());
            } else if (MobileRegionResult.ISP_YIDONG.equals(isp)) {
                objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigVpop.getYidongPop());
                objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigVpop.getYidongTailQuantity());
                objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigVpop.getYidongBizTypeId());
                objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigVpop.getYidongRefPage());
            }
            return objectNode;
        }
        return null;
    }

    @Override
    public ObjectNode getSparePopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province) {
        PageBusinessProvinceConfigVpop pageBusinessProvinceConfigVpop = getPageBusinessProvinceConfigVpop(pageId, province);
        if (pageBusinessProvinceConfigVpop != null && MobileRegionResult.ISP_YIDONG.equals(isp)) {
            ObjectNode objectNode = mapper.createObjectNode();
            objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigVpop.getYidongSparePop());
            objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigVpop.getYidongSpareTailQuantity());
            objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigVpop.getYidongSpareBizTypeId());
            objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigVpop.getYidongSpareRefPage());
            return objectNode;
        }
        return null;
    }


    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_PAGE_CONFIG_POP_CACHE, key = "#root.methodName + '-' + #pageId+'-'+ #province", unless = "#result==null")
    public PageBusinessProvinceConfigVpop getPageBusinessProvinceConfigVpop(String pageId, String province) {
        return lambdaQuery().eq(PageBusinessProvinceConfigVpop::getPageId, pageId).eq(PageBusinessProvinceConfigVpop::getProvince, province).eq(PageBusinessProvinceConfigVpop::getStatus, 1).one();
    }

}
