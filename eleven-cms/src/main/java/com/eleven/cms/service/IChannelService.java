package com.eleven.cms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Channel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.Subscribe;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;
import java.util.Map;

/**
 * @Description: cms_channel
 * @Author: jeecg-boot
 * @Date:   2020-10-23
 * @Version: V1.0
 */
public interface IChannelService extends IService<Channel> {

    Channel findByTitle(String subChannel);

    @Cacheable(cacheNames = CacheConstant.CMS_CHANNEL_AD_PLATFORM_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
    AdPlatform findAdPlatformByTitle(String subChannel);

//    void AdEffectFeedback(Subscribe subscribe, Integer status);

    boolean aopProxyTest(String subChannel);

    boolean isLegal(String subChannel);

    boolean needSmsValidate(String subChannel);

    List<Map<String, Object>> selectByCondition();

    IPage<Channel> findByPage(Page<Channel> page, QueryWrapper<Channel> queryWrapper);

    void AdEffectFeedbackNew(Subscribe subscribe, Integer status);
}
