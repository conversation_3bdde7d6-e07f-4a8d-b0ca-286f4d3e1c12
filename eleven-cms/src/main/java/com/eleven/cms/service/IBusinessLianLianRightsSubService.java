package com.eleven.cms.service;

import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.entity.LianlianChargeLog;
import com.eleven.cms.vo.FebsResponse;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/14 15:04
 **/
public interface IBusinessLianLianRightsSubService {

    //    1.有没有资格，查订购关系，有没有领取记录
//    2.返回给页面权益列表
//    3.创建预约充值的记录，领取

    //查订购关系
    FebsResponse memberVerify(String mobile, String serviceId);
    //有没有领取记录
    FebsResponse rechargRecordVerify(String mobile,String serviceId);

    //创建权益预约充值记录
    FebsResponse createScheduledRecharge(String orderId,String serviceId,String packName,String channel,String mobile,String productId,String itemId,String idCard,String customerName,String address,String memo,String travelDate);

    //创建网页权益预约充值记录
    FebsResponse webCreateScheduledRecharge(String orderId,String serviceId,String packName,String channel,String mobile,String productId,String itemId,String idCard,String customerName,String address,String memo,String travelDate);

    //定时任务权益充值校验
    FebsResponse rechargeForScheduleVerify(LianlianChargeLog lianlianChargeLog);

    //定时任务权益充值
    void rechargeForSchedule(LianlianChargeLog lianlianChargeLog);

    //根据充值通知更新订单领取状态
    void updateRechargeState(LianlianChargeLog lianlianChargeLog);
}
