package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.SuccessLimit;
import com.eleven.cms.config.AnhuiProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.AnhuiYidongService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.AnhuiResult;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class AnhuiBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    AnhuiYidongService anhuiYidongService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IChannelService channelService;
    @Autowired
    AnhuiProperties anhuiProperties;
    @Autowired
    SmsNotifyService smsNotifyService;


    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }

        Integer limitAmount = provinceBusinessChannelConfigService.getLimitByChannelAndProvince(subscribe.getChannel(), subscribe.getProvince());
        if (limitAmount > 0) {
            //获取当前序列值
            Integer currentCount = subscribeService.getIncrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
            if (currentCount >= limitAmount) {
                log.error("渠道省份开通数量已达到限量,渠道号:{},省份:{}", subscribe.getChannel(), subscribe.getProvince());
                sendAnhuiLimitSms(limitAmount);
                return Result.error("特定省份移动用户限量");
            }
        }
        return SpringContextUtils.getBean(AnhuiBusinessServiceImpl.class).receiveOrder(subscribe);

    }

    @Override
    @SuccessLimit
    public Result receiveOrder(Subscribe subscribe) {
        final String mobile = subscribe.getMobile();
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            try {
                AnhuiResult anhuiResult = anhuiYidongService.getSms(mobile);
                if (anhuiResult.isOK()) {
                    redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                    redisUtil.set(CacheConstant.CMS_CACHE_ANHUI_SMS_DATA + mobile, anhuiResult, 3600L);
                    return Result.noauth("验证码已发送");
                } else {
                    return Result.error("获取验证码失败");
                }
            } catch (Exception e) {
                return Result.error("获取验证码失败");
            }
        } else {
            final String transactionId = subscribe.getTransactionId();
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.error("验证码错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            try {
                AnhuiResult anhuiResult = anhuiYidongService.smsCode(mobile, smsCode);
                if (anhuiResult.isOK()) {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(anhuiResult.getResultMsg());
                    subscribeService.createSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                    //写渠道订阅日志
                    BizLogUtils.logSubscribe(subscribe);
                    return Result.ok("订阅成功");
                } else {
                    //订阅成功
                    subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                    subscribe.setOpenTime(new Date());
                    subscribe.setResult(anhuiResult.getResultMsg());
                    subscribeService.createSubscribeDbAndEs(subscribe);
                    return Result.error(anhuiResult.getResultMsg());
                }
            } catch (Exception e) {
                log.error("系统错误:{}", e);
                return Result.error("系统错误，请稍后再试");
            }
        }
    }

    public void sendAnhuiLimitSms(Integer limitAmount) {
        List<String> mobiles = anhuiProperties.getMobiles();
        String key = CacheConstant.CMS_CACHE_ANHUI_SMS_LIMIT;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        mobiles.forEach(mobile -> {
            smsNotifyService.sendNotify(mobile, "安徽移动已经到达限量,限量数" + limitAmount);
        });
    }
}
