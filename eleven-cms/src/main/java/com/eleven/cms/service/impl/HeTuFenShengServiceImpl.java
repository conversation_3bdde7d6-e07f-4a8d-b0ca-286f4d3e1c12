package com.eleven.cms.service.impl;

import com.eleven.cms.ad.HeTuFenShengProperties;
import com.eleven.cms.config.HeTuFenShengChannel;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IHeTuFenShengService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.util.SignatureUtils;
import com.eleven.cms.vo.HeTuFenShengNotify;
import com.eleven.cms.vo.HeTuFenShengSendSmsResult;
import com.eleven.cms.vo.HeTuFenShengSubmitOrderResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 14:24
 **/
@Slf4j
@Service
public class HeTuFenShengServiceImpl implements IHeTuFenShengService {
    private static final String LOG_TAG = "咪咕互娱分省业务";
    //订购中
    public static final String ORDER_IN_EXECUTION = "1";
    //订购成功
    public static final String ORDER_SUCCESS = "2";
    //订购失败
    public static final String ORDER_FAIL= "3";


    @Autowired
    private HeTuFenShengProperties heTuFenShengProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private Interceptor hetuFenShengIntercept;
    @Autowired
    private IChannelService channelService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().addInterceptor(hetuFenShengIntercept).build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    }

    @Override
    public Result<?> sendSms(String mobile, String channel) {
        HeTuFenShengChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(channel);
        if(heTuFenShengChannel==null){
            log.warn("{}-获取验证码,渠道号未配置=>手机号:{},渠道号:{}",LOG_TAG,mobile,channel);
            return Result.error("渠道号未配置");
        }
        ObjectNode input = mapper.createObjectNode();
        input.put("rightsId", heTuFenShengChannel.getRightsId());
        input.put("mobile", mobile);
        input.put("channel", channel);
        RequestBody body = RequestBody.create(JSON,input.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(heTuFenShengProperties.getSendSmsUrl()).newBuilder();
        log.info("{}-获取验证码,请求数据=>手机号:{},请求参数:{}",LOG_TAG,mobile,input);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-获取验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            HeTuFenShengSendSmsResult result = mapper.readValue(content, HeTuFenShengSendSmsResult.class);
            if(result.isOK()){
                return Result.ok("获取验证码成功");
            }
            return Result.error(result.getMessage());
        } catch (IOException e) {
            log.error("{}-获取验证码,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        return Result.error("系统异常");
    }

    @Override
    public Result<?> submitOrder(String mobile,String code, String ispOrderNo, String ip, String channel) {
        HeTuFenShengChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(channel);
        if(heTuFenShengChannel==null){
            log.warn("{}-提交验证码,渠道号未配置=>手机号:{},渠道号:{}",LOG_TAG,mobile,channel);
            return Result.error("渠道号未配置");
        }
        ObjectNode input = mapper.createObjectNode();
        input.put("mobile", mobile);
        input.put("channel", channel);
        input.put("rightsId", heTuFenShengChannel.getRightsId());
        input.put("code", code);
        ObjectNode snapshotUrl = mapper.createObjectNode();
        String key = DigestUtils.md5DigestAsHex((heTuFenShengChannel.getSnapshotUrl()).getBytes(StandardCharsets.UTF_8));
        snapshotUrl.putPOJO(key, heTuFenShengChannel.getSnapshotUrl());
        input.put("snapshotUrl", snapshotUrl.toString());
        input.put("orderid", ispOrderNo);
        input.put("outUrl", heTuFenShengChannel.getSnapshotUrl());
        input.put("outPageLoadTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        input.put("snapshot", ispOrderNo);
        input.put("outSourceName",heTuFenShengChannel.getAppName());
        input.put("clientIpAddress", ip);
        input.put("selectProtocolTime", DateUtil.formatSplitTime(LocalDateTime.now()));
        RequestBody body = RequestBody.create(JSON,input.toString());
        final HttpUrl.Builder httpUrlBuilder = HttpUrl.parse(heTuFenShengProperties.getCreateOrderUrl()).newBuilder();
        log.info("{}-提交验证码,请求数据=>手机号:{},url:{},请求参数:{}",LOG_TAG,mobile,input);
        Request request = new Request.Builder().url(httpUrlBuilder.build()).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info("{}-提交验证码,响应数据=>手机号:{},响应参数:{}",LOG_TAG,mobile,content);
            HeTuFenShengSubmitOrderResult result = mapper.readValue(content, HeTuFenShengSubmitOrderResult.class);
            if(result.isOK() && result.getResultData()!=null){
                return Result.ok(result.getMessage(),result.getResultData().getOrderId());
            }
            return Result.error(result.getMessage());
        } catch (IOException e) {
            log.error("{}-提交验证码,请求异常=>手机号:{}",LOG_TAG,mobile,e);
        }
        return Result.error("系统异常");
    }

    @Override
    public HeTuFenShengNotify heTuFenShengNotify(String signature,String timestamp,String jsonNode,String transactionId,String  tel,String  busiSerial,String  respCode,String  respMsg,String  orderStatus) {
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, tel).eq(Subscribe::getIspOrderNo, busiSerial).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            final HeTuFenShengChannel heTuFenShengChannel=heTuFenShengProperties.getChannelMap().get(subscribe.getChannel());
            try {
                boolean checkSign =SignatureUtils.checkSignByPublicKey(jsonNode,signature,heTuFenShengChannel.getNotifyPublicKey());
                if(!checkSign){
                    log.warn("{}-验证签名错误,通知数据=>手机号:{},通知数据:{},signature:{}",LOG_TAG,tel,jsonNode,signature);
                    return new HeTuFenShengNotify("060071","签名校验失败");
                }
            } catch (Exception e) {
                log.error("{}-生成签名错误,通知数据=>手机号:{},通知数据:{},signature:{}",LOG_TAG,tel,jsonNode,signature,e);
                return new HeTuFenShengNotify("060071","签名校验失败");
            }
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            upd.setExtra(transactionId);
            String remark=ORDER_IN_EXECUTION.equals(orderStatus)?"订购中->":"";
            String result = remark+"{\"resCode\":\""+respCode+"\",\"resMsg\":\""+respMsg+"\"}";
            if (ORDER_SUCCESS.equals(orderStatus) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                //订阅成功
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);

                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);

                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }

            }else if (ORDER_FAIL.equals(orderStatus) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else{
                upd.setResult(result);
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
        return new HeTuFenShengNotify("000000","处理成功");
    }
}
