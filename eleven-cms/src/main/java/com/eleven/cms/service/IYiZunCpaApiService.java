package com.eleven.cms.service;

import com.eleven.cms.vo.YiZunCpaGetSmsResult;
import com.eleven.cms.vo.YiZunCpaSmsCodeResult;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/14 9:32
 **/
public interface IYiZunCpaApiService {
    /**
     * 发送验证码
     * @param channelCode
     * @param phone
     * @param ip
     * @param userAgent
     * @param appPackage
     * @param sourceUrl
     * @return
     */
    YiZunCpaGetSmsResult getSms(String channelCode, String phone, String ip, String userAgent, String appPackage, String sourceUrl);

    /**
     * 提交验证码
     * @param channelCode
     * @param phone
     * @param smsCode
     * @param channelSeqId
     * @param bizToken
     * @return
     */
    YiZunCpaSmsCodeResult smsCode(String channelCode, String phone, String smsCode, String channelSeqId, String bizToken);
}
