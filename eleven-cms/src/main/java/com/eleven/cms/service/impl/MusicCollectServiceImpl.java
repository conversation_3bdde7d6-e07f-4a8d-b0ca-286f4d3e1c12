package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.entity.AliSignChargingOrder;
import com.eleven.cms.entity.Column;
import com.eleven.cms.entity.MusicCollect;
import com.eleven.cms.mapper.MiguPackMapper;
import com.eleven.cms.mapper.MusicCollectMapper;
import com.eleven.cms.service.IMusicCollectService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MusicVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDate;

/**
 * @Description: 歌曲收藏
 * @Author: jeecg-boot
 * @Date:   2023-11-21
 * @Version: V1.0
 */
@Service
public class MusicCollectServiceImpl extends ServiceImpl<MusicCollectMapper, MusicCollect> implements IMusicCollectService {

    @Override
    public FebsResponse musicCollect(String copyrightId, String mobile) {
        MusicCollect musicCollect=this.lambdaQuery().eq(MusicCollect::getCopyrightId,copyrightId).eq(MusicCollect::getMobile,mobile).orderByDesc(MusicCollect::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(musicCollect==null){
            MusicCollect collect=new MusicCollect();
            collect.setCopyrightId(copyrightId);
            collect.setMobile(mobile);
            this.baseMapper.insert(collect);
            return new FebsResponse().success("收藏成功");
        }else{
            this.baseMapper.deleteById(musicCollect.getId());
            return new FebsResponse().success("取消收藏成功");
        }
    }
    @Override
    public FebsResponse musicIsCollect(String copyrightId, String mobile) {
        MusicCollect musicCollect=this.lambdaQuery().eq(MusicCollect::getCopyrightId,copyrightId).eq(MusicCollect::getMobile,mobile).orderByDesc(MusicCollect::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(musicCollect!=null){
            return new FebsResponse().success("已收藏");
        }else{
            return new FebsResponse().fail("未收藏");
        }
    }

    @Override
    public FebsResponse queryMusicCollect(String mobile, Page<MusicVo> page) {
        IPage<MusicVo> pageList= this.baseMapper.selectMusicCollectPage(page,mobile);
        return new FebsResponse().success().data(pageList);
    }
}
