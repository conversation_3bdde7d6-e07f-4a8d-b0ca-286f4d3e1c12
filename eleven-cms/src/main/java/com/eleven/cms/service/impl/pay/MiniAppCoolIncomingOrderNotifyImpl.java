package com.eleven.cms.service.impl.pay;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.aiunion.component.RabbitMQSender;
import com.eleven.cms.aiunionkp.entity.MiniPayOrder;
import com.eleven.cms.aiunionkp.service.IMiniPayOrderService;
import com.eleven.cms.aivrbt.dto.MiniOrderPayDTO;
import com.eleven.cms.aivrbt.enums.MiniOrderStatusEnum;
import com.eleven.cms.aivrbt.vo.AppUserVO;
import com.eleven.cms.client.AppFeignClient;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.service.pay.PayNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 通用支付订单
 */
@Service
@Slf4j
public class MiniAppCoolIncomingOrderNotifyImpl implements PayNotifyService {

    @Resource
    IMiniPayOrderService miniPayOrderService;
    @Resource
    private RabbitMQSender rabbitMQSender;

    @Value("${kpApp.tmpToken}")
    private String tmpToken;

    @Resource
    private AppFeignClient appFeignClient;

    @Override
    public void handleNotify(String orderNo, Map<String, String> requestParams) {
        String transactionId = "";
        if (Objects.nonNull(requestParams)) {
            transactionId = requestParams.get("transaction_id");
        }
        updateOrder(orderNo, transactionId);
    }

    public void updateOrder(String orderNo, String transactionId) {

        log.info("MiniAppCommonOrderNotifyImpl更新订单状态");
        MiniPayOrder miniPayOrder = miniPayOrderService.lambdaQuery().eq(MiniPayOrder::getOrderNo, orderNo).last("limit 1").one();
        if (Objects.isNull(miniPayOrder)) {
            return;
        }
        MiniOrderPayDTO miniOrderPayDTO = MiniOrderPayDTO.builder().orderNo(orderNo).uid(miniPayOrder.getAppUid()).productId(miniPayOrder.getProductId()).build();
        MiniPayOrder update = new MiniPayOrder();
        update.setId(miniPayOrder.getId());
        update.setPayTime(new Date());
        update.setPaymentTransactionId(transactionId);
        update.setPayTime(new Date());
        Result<AppUserVO> appUserVOResult = appFeignClient.miniOrderEquity(miniOrderPayDTO, tmpToken);
        if (appUserVOResult.isSuccess()) {
            update.setOrderPayStatus(MiniOrderStatusEnum.PAID.getCode());
        } else {
            update.setOrderPayStatus(MiniOrderStatusEnum.EQUITY_FAIL.getCode());
        }

        miniPayOrderService.updateById(update);
        rabbitMQSender.sendWithQueue(RabbitMQConfig.MINI_ORDER_SYNC_APP_QUEUE_NAME, JSONObject.toJSONString(miniPayOrderService.getById(miniPayOrder.getId())));

    }

    @Override
    public PayBusineesTypeEnum getBusinessType() {
        return PayBusineesTypeEnum.COOL_INCOMING_CALL_WX_MINI_APP;
    }
}
