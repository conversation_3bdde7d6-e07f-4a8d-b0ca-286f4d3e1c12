package com.eleven.cms.service.impl;

import com.alipay.api.AlipayApiException;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.RightsRightsProperties;
import com.eleven.cms.config.WoyinyueConfigProperties;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.*;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.eleven.cms.util.BizConstant.DOUYING_CHANNEL_ID;
import static com.eleven.cms.util.BizConstant.DOUYING_CHANNEL_PASSWORD;

@Slf4j
@Service
public class MemberServiceImpl implements IMemberService {

    //未上线的白金会员渠道号
//    final List<String> notOnlineChannelList =Lists.newArrayList("00210U2","00210TY","00210TZ","00210U3","00210U4");


    //支付宝业务类型
    private static final List<String> ALI_PAY_BIZ_TYPE_LIST= Lists.newArrayList(BizConstant.BIZ_TYPE_VRBT_YD_ALIPAY,BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY,BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY);

    final String KG_CHANNEL_CODE="00210VV";
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    private KugouApiService kugouApiService;
//    @Autowired
//    private ISoapFeedbackService soapFeedbackService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IMemberRightsService memberRightsService;
    @Autowired
    private JunboApiService junboApiService;
    @Autowired
    private IMiguPackService miguPackService;
    @Autowired
    private IRightsPackService rightsPackService;
    @Autowired
    private BizProperties bizProperties;
//    @Autowired
//    private IShydChargeOrderService shydChargeOrderService;
//    @Autowired
//    private SichuanMobileOrderMapper sichuanMobileOrderMapper;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private UnifyRightsFeignClient unifyRightsFeignClient;
    @Autowired
    private MemberRightsExecutionThreadService memberRightsExecutionThreadService;
    @Autowired
    IHttpRequestService httpRequestService;
    @Autowired
    private RechargeAlertService rechargeAlertService;
    @Qualifier("threadPoolExecutor")
    ThreadPoolExecutor executor;
    @Autowired
    private DianxinVrbtService dianxinVrbtService;
    @Autowired
    private IAliSignChargingOrderService chargingOrderService;
    @Autowired
    private IAlipayService alipayService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IAlipayConfigService alipayConfigService;
    @Autowired
    private IWoReadOrderService woReadOrderService;
    @Autowired
    private WoReadApiService woReadApiService;
    @Autowired
    private IWyyMmOrderService wyyMmOrderService;
    @Autowired
    private IAliSignChargingOrderService aliSignChargingOrderService;
    @Autowired
    IVrbtDiyRingService vrbtDiyRingService;
    @Autowired
    IOrderVrbtService orderVrbtService;
    @Autowired
    LiantongVrbtService liantongVrbtService;
    @Autowired
    private RightsRightsProperties rightsRightsProperties;
    public static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Autowired
    private RedisUtil redisUtil;
    //    public static final Integer INTERVAL_MINUTE = 61;
//    public static final Integer INTERVAL_MINUTE = -1;
    @Autowired
    WoyinyueConfigProperties woyinyueConfigProperties;
    @Autowired
    private IMobileFeeChargeLogService mobileFeeChargeLogService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    IMiGuKuaiYouVRJingMengService vrJingMengService;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;

    /**
     * 判断当月是否已充值
     * @param mobile
     * @return
     */
    private Result<?> checkRechargeMember(String mobile,String account, String serviceId, Rights rights,String packName,Boolean isExtension,Boolean isPrepareCharge,Long rechargeDelayMinute) {
        //判断是否订购会员
        FebsResponse response=isMemberAndIsReceiveRights(mobile,serviceId,null,isExtension,rechargeDelayMinute);
        if(!response.isOK()){
            String code= response.get("code").toString();
            String message = response.get("message").toString();
            return Result.error(Integer.valueOf(code),message);
        }
        String soapServiceId=response.get("message").toString();
        LocalDateTime effectTime=(LocalDateTime)response.get("data");
        Date payTime=(Date)response.get("payTime");
        Long days=(Long)response.get("days");
//        //是否soap执行查询
//        if(RightBusinessEnum.isSoapLog(serviceId) && !isExtension){
//            try {
//                //获取当月最新订购数据
//                SoapFeedback soapFeedback = soapFeedbackService.findLastOrder(mobile,  soapServiceId);
//                log.info("响应=>soapFeedback:{}",soapFeedback);
//                if(soapFeedback == null) {
//                    return Result.error(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE.value(),BizConstant.NOT_MEMBER_MSG);
//                }
//                effectTime=DateUtil.dateToLocalDateTime(soapFeedback.getCreateTime()).plusMinutes(rechargeDelayMinute);
//            } catch (Exception e) {
//                return Result.error(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE.value(),BizConstant.MEMBER_MSG);
//            }
//        }
        //查询该手机号当月所有订单
        List<JunboChargeLog> chargeLogList=null;
        //支付宝订单与其他业务查询不同
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(serviceId);
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(serviceId);
        if(rightBusinessEnum==null && miguPacks!=null && !miguPacks.isEmpty()){
            chargeLogList = junboChargeLogService.findByMobileAndPackNameAndDate(mobile,packName);
        }else if(serviceId.equals(RightBusinessEnum.WANGYIYUN_MM.getServiceId())){
            chargeLogList = junboChargeLogService.findByMobileAndPackNameAndDate(mobile,packName,days);
        }else{
            chargeLogList = junboChargeLogService.findByMobileAndPackNameAndMonth(mobile,packName);
        }
        //判断当月是否有已充值成功的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(hasSuccess){
            return Result.error(HttpStatus.PRECONDITION_FAILED.value(),BizConstant.RECEIVE_MSG);
        }
        //判断当月是否有预约直充的订单
        boolean hasScheduled = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
        if(hasScheduled){

            //页面重复订购
            if(isExtension && response.get("data")!=null){
                Optional<JunboChargeLog> junboChargeLogOptional=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime));
                if(junboChargeLogOptional.isPresent()){
                    try {
                        JunboChargeLog junboChargeLog=junboChargeLogOptional.get();
                        junboChargeLog.setScheduledTime(Date.from(effectTime.atZone(ZoneId.systemDefault()).toInstant()));
                        junboChargeLogService.updateById(junboChargeLog);
                    } catch (Exception e) {
                        return Result.error(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE.value(),BizConstant.MEMBER_MSG);
                    }
                }
            }

            //判断当月领取权益
            boolean hasReceive = chargeLogList.stream().anyMatch(item-> rights.getCouponId().equals(item.getCouponId()));
            if(hasReceive){
                String couponName=chargeLogList.stream().filter(item-> rights.getCouponId().equals(item.getCouponId())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
                String msg = BizConstant.SCHEDULED_MSG.replace("会员",couponName);
                return Result.error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(),msg);
            }else{
                return Result.error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(),BizConstant.RECEIVE_MSG);
            }
        }
        //判断当月是否有正在充值的订单
        boolean hasProcessing = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(hasProcessing){
            String couponName=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            String msg = BizConstant.RECHARGE_MSG.replace("会员",couponName);
            return Result.error(HttpStatus.LENGTH_REQUIRED.value(),msg);
        }

        //沃音乐权益
//        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(serviceId);
//        if(StringUtils.equals(rightBusinessEnum.getOrderkey(),RightBusinessEnum.WYY.getOrderkey())){
//            //月卡（4个月领取）
//            if(StringUtils.equals("wyy_rights_pack_1",packName)){
//                //订购月份
//                String subMonth=DateUtil.formatYearMonth(effectTime);
//                String rightsMonth=DateUtil.formatYearMonth(LocalDateTime.now());
//                if(StringUtils.equals(subMonth,rightsMonth)){
//                    return Result.error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(),BizConstant.RECEIVE_MSG);
//                }
//                if (DateUtils.subtractMonth(effectTime)%4 != 0){
//                    return Result.error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(),BizConstant.WO_MUSIC_RECEIVE_MSG);
//                }
//            }
//            //周卡（次月领取）
//            if(StringUtils.equals("wyy_rights_pack_2",packName)){
//                //订购月份
//                String subMonth=DateUtil.formatYearMonth(effectTime);
//                String rightsMonth=DateUtil.formatYearMonth(LocalDateTime.now());
//                if(StringUtils.equals(subMonth,rightsMonth)){
//                    return Result.error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(),BizConstant.RECEIVE_MSG);
//                }
//            }
//        }
        if(isExtension){
            //权益领取红名单(预约充值设置为当前时间)
            boolean isRad=this.isRedList(mobile);
            if(isRad){
                effectTime=LocalDateTime.now().plusMinutes(1);
            }
            junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(effectTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
            String msg = BizConstant.SCHEDULED_MSG.replace("会员",rights.getRightsName());
            return Result.error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(),msg);
        }
        //是否预约直充
        if(RightBusinessEnum.isPrepareCharge(serviceId) && isPrepareCharge){
            //判定订单是否已生效
            //会员领取修改：1，1小时之内领取会员，不能直接领取，提示文案修改为“你的权益正在路上，预计一小时内到达~”此次领取即已预约成功，
            // 61分钟后（订购开始算）查询该用户是否有领取资格，若有则直接发放已预约会员，若没有领取资格则不发放会员权益；
            // 2，用户1小时之内来领取第二次，则和第一次提示文案一样，发放流程也相同；  3，用户订购1小时内未领取会员，1小时之后领取会员时查询是否有领取资格，若有领取资格则立即发放会员权益
            // 此处采用预约开通的逻辑来做,如果时间未到先做一个预约,时间到了的就直接直充
            // 预约给一个特定的状态,然后用定时任务来做直充,定时任务直充前还需要再次判定用户是否已退订
            //final LocalDateTime subTime = soapFeedback.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusMinutes(INTERVAL_MINUTE);
            //权益领取红名单(预约充值设置为当前时间)
            boolean isRad=this.isRedList(mobile);
            if(isRad){
                effectTime=LocalDateTime.now().plusMinutes(1);
            }
            if(LocalDateTime.now().isBefore(effectTime)){
                rights.setPayTime(payTime);
                junboChargeLogService.createScheduleRechargeLog(mobile,account,serviceId, rights, Date.from(effectTime.atZone(ZoneId.systemDefault()).toInstant()),packName);
                String msg = BizConstant.SCHEDULED_MSG.replace("会员",rights.getRightsName());
                return Result.error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value(),msg);
            }
        }
        return Result.ok();
    }

    @Override
    public Result<?> obtainMember(String mobile,String account, String serviceId, String rightsId,String packName) {

        //业务明细查询
        Rights rights=new Rights(serviceId,rightsId,packName,mobile);
        FebsResponse febsResponse=this.checkRights(rights);
        if(!febsResponse.isOK()){
            return Result.error(Integer.valueOf(febsResponse.get("code").toString()),febsResponse.get("message").toString());
        }

        Result<?> result=this.checkRechargeMember(mobile,account,serviceId,rights,packName,false,true,rights.getRechargeDelayMinute());
        if(!result.getCode().equals(200)){
            return result;
        }

        JunboChargeLog junboChargeLog = junboChargeLogService.createImmediateRechargeLog(mobile,account,serviceId, rights,packName);
        String orderSEQ = junboChargeLog.getMiguOrderId();
        //券码充值
        if(String.valueOf(BizConstant.RECHARGE_STATE_CODE).equals(String.valueOf(rights.getRechargeState()))){
            FebsResponse febs=junboChargeLogService.couponCodeCharge(junboChargeLog,mobile);
            return Result.error(Integer.valueOf(febs.get("code").toString()),febs.get("message").toString());
        }
        //白金会员权益（mgyybjhy）
        if(BizConstant.RECHARGE_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res=miguApiService.bjhyOrder(mobile, BizConstant.RECHARGE_BJHY_CHANNEL_ID);
            junboChargeLogService.updateJunboChargeBJHYStatus(junboChargeLog, res);
            return Result.error(Integer.valueOf(res.isOK()?"200":"500"),res.getResMsg());
        }
        //酷狗VIP月卡直充（kgykzhichong）
        if(BizConstant.KUGOU_COUPON_ID.equals(junboChargeLog.getCouponId())){
            KugouOrderResult res=kugouApiService.order(orderSEQ,mobile);
            junboChargeLogService.updateJunboChargeKGStatus(junboChargeLog, res);
            return Result.error(Integer.valueOf(res.isOK()?"200":"500"),res.getErrorCode());
        }
        if(BizConstant.COMPANY_OWNER_HUAYI.equals(junboChargeLog.getCompanyOwner())){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            junboChargeLogService.updateById(junboChargeLog);
            return Result.error(Integer.valueOf("000005".equals(huaYiResp.getCode()) || "000000".equals(huaYiResp.getCode())?"200":"500"),huaYiResp.getMsg());

        }

//        final JunboResult junboResult = junboApiService.rechargeVIP(rights.getCouponId(), orderSEQ, account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(rights.getCouponId(), orderSEQ, account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        if(null==junboRespon){
            return Result.error(HttpStatus.BAD_REQUEST.value(),"系统繁忙,请稍后再试!");
        }
        return junboChargeLogService.getReceiveState(junboChargeLog,junboRespon);
    }






//    /**
//     * 是否订购上海移动视频彩铃
//     * @param mobile
//     * @return
//     */
//    private Boolean isSHYDMember(String mobile){
//        if(shydChargeOrderService.isMember(mobile)){
//            return true;
//        }else{
//            return false;
//        }
//    }

//    /**
//     * 是否订购四川移动视频彩铃
//     * @param mobile
//     * @return
//     */
//    private Boolean isSCYDMember(String mobile){
//        //查询当月四川移动彩铃订购数据
//        List<SichunMobileOrder> sichunMobileOrders = sichuanMobileOrderMapper.selectList(new QueryWrapper<SichunMobileOrder>().eq("phone",mobile));
//        if(sichunMobileOrders!=null && sichunMobileOrders.size()>0){
//            return true;
//        }else{
//            return false;
//        }
//    }
    private FebsResponse unifyMemberRightsGet(String mobile,String account, String serviceId, String rightsId,String packName) {
        //业务明细查询
        Rights rights=new Rights(serviceId,rightsId,packName,mobile);
        FebsResponse febsResponse=this.checkRights(rights);
        if(!febsResponse.isOK()){
            return febsResponse;
        }
        Result<?> result=this.checkRechargeMember(mobile,account, serviceId,rights,packName,false,true,rights.getRechargeDelayMinute());
        if(!result.getCode().equals(200)){
            return new FebsResponse().code(result.getCode().toString()).message(result.getMessage());
        }
        JunboChargeLog junboChargeLog = junboChargeLogService.createImmediateRechargeLog(mobile,account,serviceId, rights,packName);
        String orderSEQ = junboChargeLog.getMiguOrderId();
        //券码充值
        if(StringUtils.equals(String.valueOf(rights.getRechargeState()),String.valueOf(BizConstant.RECHARGE_STATE_CODE))){
            return junboChargeLogService.couponCodeCharge(junboChargeLog,mobile);
        }
        //白金会员权益（mgyybjhy）
        if(StringUtils.equals(junboChargeLog.getCouponId(),BizConstant.RECHARGE_COUPON_ID)){
            RemoteResult res=miguApiService.bjhyOrder(mobile, BizConstant.RECHARGE_BJHY_CHANNEL_ID);
            junboChargeLogService.updateJunboChargeBJHYStatus(junboChargeLog, res);
            return new FebsResponse().code(res.isOK()?"200":"500").message(res.getResMsg());
        }
        //酷狗VIP月卡直充（kgykzhichong）
        if(StringUtils.equals(junboChargeLog.getCouponId(),BizConstant.KUGOU_COUPON_ID)){
            KugouOrderResult res=kugouApiService.order(orderSEQ,mobile);
            junboChargeLogService.updateJunboChargeKGStatus(junboChargeLog, res);
            return new FebsResponse().code(res.isOK()?"200":"500").message(res.getErrorCode());
        }
        if(BizConstant.COMPANY_OWNER_HUAYI.equals(junboChargeLog.getCompanyOwner())){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            junboChargeLogService.updateById(junboChargeLog);
            return new FebsResponse().code("000005".equals(huaYiResp.getCode()) || "000000".equals(huaYiResp.getCode())?"200":"500").message(huaYiResp.getMsg());
        }

//        final JunboResult junboResult = junboApiService.rechargeVIP(rights.getCouponId(), orderSEQ, account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
        if(null==junboRespon){
            return new FebsResponse().code(HttpStatus.BAD_REQUEST).message("系统繁忙,请稍后再试!");
        }
        Result<?> chargeResult=junboChargeLogService.getReceiveState(junboChargeLog,junboRespon);
        return new FebsResponse().code(chargeResult.getCode().toString()).message(chargeResult.getMessage());
    }

    /**
     * 检查权益是否存在
     * @param rights
     * @return
     */
    private FebsResponse checkRights(Rights rights) {
        //查询可充值权益
        Optional<MemberRights> memberRightsOptional=memberRightsService.queryMemberRightsDetail(rights.getRightsId());
        if(memberRightsOptional==null || !memberRightsOptional.isPresent()){
            return new FebsResponse().code(HttpStatus.REQUEST_URI_TOO_LONG).message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        MemberRights memberRights=memberRightsOptional.get();
        rights.setCouponId(memberRights.getCouponId());
        rights.setRightsName(memberRights.getRightsName());
        rights.setProductPrice(memberRights.getProductPrice());
        rights.setRechargeState(memberRights.getRechargeState());
        rights.setIsAccount(memberRights.getIsAccount());
        rights.setCompanyOwner(memberRights.getCompanyOwner());
        //查询业务包
        Optional<MiguPack> miguPackOptional=miguPackService.queryMiguPackDetail(rights.getServiceId(),rights.getPackName());
        if(miguPackOptional==null || !miguPackOptional.isPresent()){
            return new FebsResponse().code(HttpStatus.REQUEST_URI_TOO_LONG).message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        MiguPack miguPack=miguPackOptional.get();
        rights.setRechargeDelayMinute(miguPack.getRechargeDelayMinute());
        //判断权益关系是否存在
        Boolean checkRightsPack=rightsPackService.isServiceAssociation(miguPack.getId(),memberRights.getId());
        if(checkRightsPack){
            return new FebsResponse().code(HttpStatus.REQUEST_URI_TOO_LONG).message(BizConstant.RIGHTS_MSG_NOT_ORDER);
        }
        return new FebsResponse().success();
    }

    @Override
    public FebsResponse isMemberAndIsReceiveRights(String mobile,String serviceId,String token,Boolean isExtension,Long rechargeDelayMinute){
        String soapServiceId=serviceId;
        LocalDateTime effectTime=LocalDateTime.now().plusMinutes(rechargeDelayMinute);
        Date payTime=new Date();
        Long days=0L;
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(serviceId);


        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(serviceId);
        if(rightBusinessEnum==null && miguPacks!=null && !miguPacks.isEmpty()){
            Optional<MiguPack> miguPackOptional=miguPacks.stream().filter(item-> serviceId.equals(item.getServiceId())).collect(Collectors.toList()).stream().max(Comparator.comparing(MiguPack::getCreateTime));
            //设置默认业务参数
            if(miguPackOptional.isPresent()){
                rightBusinessEnum=RightBusinessEnum.MEMBER_1880;
                rightBusinessEnum.setServiceId(miguPackOptional.get().getServiceId());
                rightBusinessEnum.setServiceName(miguPackOptional.get().getTitleName());
            }
        }
        if (rightBusinessEnum==null) {
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        List<String> channelList = bizProperties.getChannelByServiceId(serviceId);
        String channelCode=null;
        if(channelList!=null && channelList.size()>0){
            channelCode=channelList.get(0);
        }
        switch (rightBusinessEnum){


            case OFMGTXH:
                //是否订购藕粉咪咕同享会10元包
                try {
                    final RemoteResult batc =miguApiService.asMemberQuery(mobile,channelCode);
                    //渠道订单
                    Boolean isOrderMember=isOrderMember(mobile, rightBusinessEnum,channelCode,effectTime);
                    if(batc.isAsMember()){
                        //预约直充时间
                        log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,batc==null?"":batc.getStatus(),batc==null?"":batc.getResCode(),batc==null?"":batc.getResMsg());
                        //页面充值
                        if(isExtension && !isOrderMember){
                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                        }
                        break;
                    }
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},isExtension:{}", mobile,isExtension);
                    if(!isExtension){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    if(isOrderMember){
                        //预约直充时间
                        break;
                    }else{
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }




            case BJHY:

                try {
                    //是否订购白金会员
                    Boolean isBjhyMember=false;
                    RemoteResult bjhy=null;
                    for (int i = 0; i < channelList.size(); i++) {
//                        if(!notOnlineChannelList.contains(channelList.get(i))){
                        final RemoteResult bjhyMember =miguApiService.bjhyQuery(mobile,channelList.get(i));
                        if(bjhyMember.isBjhyMember()){
                            isBjhyMember=true;
                            bjhy=bjhyMember;
                        }
//                        }
                    }
                    //渠道订单
                    Boolean isOrderMember=isOrderMember(mobile, rightBusinessEnum,channelList,effectTime);
                    if(isBjhyMember){
                        //预约直充时间
                        log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,bjhy==null?"":bjhy.getStatus(),bjhy==null?"":bjhy.getResCode(),bjhy==null?"":bjhy.getResMsg());
                        //页面充值
                        if(isExtension && !isOrderMember){
                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                        }
                        break;
                    }
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},isExtension:{}", mobile,isExtension);
                    if(!isExtension){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    if(isOrderMember){
                        //预约直充时间
                        break;
                    }else{
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }

            case BJHYYS:
                try {
                    //是否订购白金会员[宜搜]
                    Boolean isYiSouMember=false;
                    RemoteResult yiSou=null;
                    for (int i = 0; i < channelList.size(); i++) {
//                        if(!notOnlineChannelList.contains(channelList.get(i))){
                        final RemoteResult bjhyMember =miguApiService.bjhyQuery(mobile,channelList.get(i));
                        if(bjhyMember.isBjhyMember()){
                            isYiSouMember=true;
                            yiSou=bjhyMember;
                        }
//                        }
                    }
                    //渠道订单
                    Boolean isOrderMember=isOrderMember(mobile, rightBusinessEnum,channelList,effectTime);
                    if(isYiSouMember){
                        //预约直充时间
                        log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,yiSou==null?"":yiSou.getStatus(),yiSou==null?"":yiSou.getResCode(),yiSou==null?"":yiSou.getResMsg());
                        //页面充值
                        if(isExtension && !isOrderMember){
                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                        }
                        break;
                    }
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},isExtension:{}", mobile,isExtension);
                    if(!isExtension){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    if(isOrderMember){
                        //预约直充时间
                        break;
                    }else{
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }

//            case XKLD:
//                try {
//                    //视频彩铃渠道侧是否是会员
////                RemoteResult monthStatusResult = miguApiService.vrbtMonthStatusQuery(mobile, MiguApiService.CH_DYB_DEFAULT);
////                soapServiceId = bizProperties.getMiguChannelConfigMap().get(MiguApiService.CH_DYB_DEFAULT).getServiceId();
////                log.info("视频彩铃渠道侧是否是会员=>mobile:{},vrbtRemoteResult:{}", mobile,monthStatusResult);
////                if(!monthStatusResult.isVrbtMember()){
//                    //查询视频彩铃包月状态(彩铃运营中心订阅包)
//
//
//                    final RemoteResult result =miguApiService.vrbtMonthStatusQuery(mobile,MiguApiService.CENTRALITY_CHANNEL_CODE, true);
//                    soapServiceId = bizProperties.getMiguChannelConfigMap().get(MiguApiService.CH_DYB_DEFAULT).getServiceId();
//                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,result==null?"":result.getStatus(),result==null?"":result.getResCode(),result==null?"":result.getResMsg());
//                    if(!result.isVrbtMember()){
////                        //是否是上海移动会员
////                        Boolean isSHYDMember=isSHYDMember(mobile);
////                        log.info("上海移动->:isSHYDMember:{}", isSHYDMember);
////                        if(!isSHYDMember){
////                            //是否是四川移动会员
////                            Boolean isSCYDMember=isSCYDMember(mobile);
////                            log.info("四川移动->:isSCYDMember:{}", isSCYDMember);
////                            if(!isSCYDMember){
//                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
////                            }
////                        }
//                    }
////                }
////                //视频彩铃是否满足已订购3个月
////                Boolean isSatisfactionMonth=subscribeService.isSatisfactionMonth(mobile);
////                if(!isSatisfactionMonth){
////                    return new FebsResponse().code(HttpStatus.I_AM_A_TEAPOT).message(BizConstant.NOT_YET_MSG);
////                }else{
////                    //判断权益是否已领取
////                    Boolean isGetVRBT = junboChargeLogService.isGetVRBTCLZXRights(mobile,serviceId);
////                    if(isGetVRBT){
////                        return new FebsResponse().code(HttpStatus.PRECONDITION_FAILED).message(BizConstant.YETGET_MSG);
////                    }
////                }
//                    break;
////            case BKST:
////                //贝壳视听会员
////                try {
////                    Boolean isRelation=!tianyiSpaceService.queryRelationShip(mobile);
////                    log.info("贝壳视听是否是会员=>mobile:{},isRelation:{}", mobile,isRelation);
////                    if(isRelation){
////                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
////                    }
////                } catch (Exception e) {
////                    e.printStackTrace();
////                }
////                break;
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//                }
            case YDZS:
                try {
                    final RemoteResult result =miguApiService.vrbtMonthStatusQuery(mobile,BizConstant.BIZ_QYLI_CW_CHANNEL_CODE, true);
                    soapServiceId = cmsCrackConfigService.getCrackConfigByChannel(MiguApiService.CH_DYB_DEFAULT).getServiceId();
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,result==null?"":result.getStatus(),result==null?"":result.getResCode(),result==null?"":result.getResMsg());
                    if(!result.isVrbtMember()){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }

            case WYY:
                try {
                    //沃音乐视频彩铃
                    WoMusicResponse woMusic=this.isWoMusicMember(mobile);
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},returnCode:{}",mobile,woMusic!=null?woMusic.getReturnCode():"");
                    if(woMusic==null || !woMusic.isOK()){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }

                    boolean success = woMusic.getSubedProducts().stream().anyMatch(item-> BizConstant.WO_MUSIC_STATUS_SUCCESS.equals(item.getStatus()) &&  BizConstant.WO_MUSIC_PRODUCT_ID.equals(item.getProductId()));
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},success:{}",mobile,success);
                    if(!success){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    Optional<WoMusicResponse.SubedProducts> subedProductsOptional=woMusic.getSubedProducts().stream().filter(item -> BizConstant.WO_MUSIC_STATUS_SUCCESS.equals(item.getStatus()) &&  BizConstant.WO_MUSIC_PRODUCT_ID.equals(item.getProductId())).collect(Collectors.toList()).stream().max(Comparator.comparing(WoMusicResponse.SubedProducts::getSubTime));
                    if(subedProductsOptional.isPresent()){
                        try {
                            String subTime=subedProductsOptional.get().getSubTime();
                            effectTime=StringUtils.isNotBlank(subTime)?DateUtils.parseString(subTime):effectTime;
                            log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},effectTime:{}",mobile,effectTime);
                        } catch (Exception e) {
                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                        }
                    }
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }

            case DDXSPCL:
                try {
                    //10元电信视频彩铃
                    boolean flag = dianxinVrbtService.queryPackageExist(mobile,BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE);
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},flag:{}", mobile,flag);
                    if(!flag){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }
            case MEMBER_990_VS:
            case MEMBER_1880:
            case MEMBER_1990:
            case MEMBER_990_PLUS:
            case MEMBER_1880_PLUS:
            case MEMBER_1990_VRBT:
            case M_HSTJ_10_TEST:
            case M_HSTJ_1990_ZXST:
            case M_HSTJ_2500_ZZHY:
            case M_XR_2500_MD:
            case M_XR_2500_SXK:
            case M_XR_2500_RYX:
            case M_WX_1990_MD:
            case M_WX_1990_RYX:
                try {
                    //查询支付宝签约订单
                    AliSignChargingOrder aliOrder = aliSignRechargeIsMember(mobile, serviceId);
                    if(aliOrder!=null){
                        log.info("支付宝是否订购"+aliOrder.getBusinessType()+"=>手机号:{},订单明细:{}", mobile,aliOrder);
                        payTime=aliOrder.getPayTime();
                        break;
                    }
                    //查询沃悦读订单
                    WoReadOrder woReadOrder=woReadRechargeIsMember(mobile,serviceId);
                    if(woReadOrder!=null){
                        log.info("沃悦读是否订购"+woReadOrder.getBusinessType()+"=>手机号:{},订单明细:{}", mobile,woReadOrder);
                        payTime=woReadOrder.getPayTime();
                        break;
                    }
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }
            case QXYYB:
                try {
                    //是否订购10包月包(趣享音乐包)
                    final RemoteResult qxyybRemoteResult =miguApiService.cpmbQuery(mobile,channelCode);
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,qxyybRemoteResult==null?"":qxyybRemoteResult.getStatus(),qxyybRemoteResult==null?"":qxyybRemoteResult.getResCode(),qxyybRemoteResult==null?"":qxyybRemoteResult.getResMsg());
                    if(!qxyybRemoteResult.isCpmbMember()){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }
            case BATCYYB:
            case KGBATCYYB:
            case YYQQB:
//            case YYQQBQXYY:
            case YYQQBJDYY:
                try {
//                    if(RightBusinessEnum.KGBATCYYB.name().equals(rightBusinessEnum.name())){
//                        channelCode=KG_CHANNEL_CODE;
//                    }
//                    final RemoteResult batc =miguApiService.cpmbQuery(mobile,channelCode);
//                    //渠道订单
//                    Boolean isOrderMember=isOrderMember(mobile, rightBusinessEnum,channelCode,effectTime);
//                    if(batc.isOK()){
//                        //预约直充时间
//                        log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,batc==null?"":batc.getStatus(),batc==null?"":batc.getResCode(),batc==null?"":batc.getResMsg());
//                        //页面充值
//                        if(isExtension && !isOrderMember){
//                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//                        }
//                        break;
//                    }
//                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},isExtension:{}", mobile,isExtension);
//                    if(!isExtension){
//                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//                    }
//                    if(isOrderMember){
//                        //预约直充时间
//                        break;
//                    }else{
//                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//                    }
                    //酷狗北岸唐唱音乐包特定业务ID添加对应的业务渠道号
                    if(RightBusinessEnum.KGBATCYYB.name().equals(rightBusinessEnum.name())){
                        if(channelList==null){
                            channelList= Lists.newArrayList();
                            channelList.add(KG_CHANNEL_CODE);
                        }

                    }
                    //是否订购北岸唐唱
                    Boolean isBatcMember=false;
                    RemoteResult batc=null;
                    for (int i = 0; i < channelList.size(); i++) {
                        final RemoteResult batcMember =miguApiService.cpmbQuery(mobile,channelList.get(i));
                        if(batcMember.isCpmbMember()){
                            isBatcMember=true;
                            batc=batcMember;
                        }
                    }
                    //渠道订单
                    Boolean isOrderMember=isOrderMember(mobile, rightBusinessEnum,channelList,effectTime);
                    if(isBatcMember){
                        //预约直充时间
                        log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},status:{},resCode:{},resMsg:{}", mobile,batc==null?"":batc.getStatus(),batc==null?"":batc.getResCode(),batc==null?"":batc.getResMsg());
                        //页面充值
                        if(isExtension && !isOrderMember){
                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                        }
                        break;
                    }
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},isExtension:{}", mobile,isExtension);
                    if(!isExtension){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    if(isOrderMember){
                        //预约直充时间
                        break;
                    }else{
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }
            case HY_10_XX:
            case HY_25_CW:
                try {
                    final FebsResponse VRJingMengIsMember = vrJingMengService.vrJingMengGetUser(mobile,rightBusinessEnum.name());
                    //渠道订单
                    Boolean isOrderMember=isOrderMember(mobile, rightBusinessEnum,rightBusinessEnum.name(),effectTime);
                    if(VRJingMengIsMember.isOK()){
                        //预约直充时间
                        log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>手机号:{},响应数据:{}", mobile,VRJingMengIsMember);
                        //页面充值
                        if(isExtension && !isOrderMember){
                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                        }
                        break;
                    }
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},isExtension:{}", mobile,isExtension);
                    if(!isExtension){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    if(isOrderMember){
                        //预约直充时间
                        break;
                    }else{
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }
            case WANGYIYUN_MM:
                try {
                    WyyMmOrder wyyMmOrder=wyyMmOrderService.wyyMMIsMember(mobile);
                    //渠道订单
                    Boolean isOrderMember=isOrderMember(mobile, rightBusinessEnum,rightBusinessEnum.name(),effectTime);
                    if(wyyMmOrder!=null){
                        payTime=wyyMmOrder.getPayTime();
                        if(wyyMmOrder.getIsRenew().equals(0)){
                            days=26L;
                        }else if(wyyMmOrder.getIsRenew().equals(2)){
                            days=30L;
                        }
                        //预约直充时间
                        log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>手机号:{},响应数据:{}", mobile,wyyMmOrder);
                        //页面充值
                        if(isExtension && !isOrderMember){
                            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                        }
                        //判断是否在支付时间内
                        if(!isExtension){
                            LocalDateTime  payTimeStart=DateUtil.dateToLocalDateTime(payTime);
                            LocalDateTime  payTimeEnd= DateUtil.dateToLocalDateTime(payTime).plusDays(days);
                            if(!(LocalDateTime.now().isAfter(payTimeStart) && LocalDateTime.now().isBefore(payTimeEnd))){
                                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                            }
                        }
                        break;
                    }
                    log.info("是否订购"+rightBusinessEnum.getServiceName()+"=>mobile:{},isExtension:{}", mobile,isExtension);
                    if(!isExtension){
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                    if(isOrderMember){
                        //预约直充时间
                        break;
                    }else{
                        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
                }
            default: return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        }
        if(StringUtils.isBlank(token)){
            return new FebsResponse().success(soapServiceId).data(effectTime).payTime(payTime).days(days);
        }
        //判断是否领取权益 0 暂无可以领取权益 1 有可以领取权益
        Boolean isReceiveRights=isReceiveRights(rightBusinessEnum,mobile);
        Map map=Maps.newHashMap();
        if(!isReceiveRights){
            map.put("token",token);
            return new FebsResponse().code(HttpStatus.UNSUPPORTED_MEDIA_TYPE).message(BizConstant.RECEIVE_MSG).data(map);
        }else{
            map.put("serviceId",serviceId);
            map.put("token",token);
            return new FebsResponse().success().data(map);
        }
    }



    private boolean isOrderMember(String mobile, RightBusinessEnum rightBusinessEnum, String channelCode,LocalDateTime effectTime) {
        //查询当天最新数据
        Optional<Subscribe> sub=subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getChannel, channelCode)
                .between(Subscribe::getCreateTime, DateUtil.handleSqlDateStartTime(LocalDateTime.now()),DateUtil.handleSqlDateEndTime(LocalDateTime.now()))
                .list()
                .stream()
                .max(Comparator.comparing(Subscribe::getCreateTime));

        if(sub!=null && sub.isPresent()){
            Subscribe subscribe=sub.get();
            log.info("是否订购"+ rightBusinessEnum.getServiceName()+"(渠道订单)=>mobile:{},status:{},createTime:{},effectTime:{}", mobile,subscribe.getStatus(),DateUtil.dateToLocalDateTime(subscribe.getCreateTime()),effectTime);
            return true;
        }
        log.info("是否订购"+ rightBusinessEnum.getServiceName()+"(渠道订单)=>mobile:{},status:{},createTime:{},effectTime:{}", mobile,"未订购",LocalDateTime.now(),effectTime);
        return false;
    }
    private boolean isOrderMember(String mobile, RightBusinessEnum rightBusinessEnum, List<String> channelList,LocalDateTime effectTime) {
        //查询当天最新数据
        Optional<Subscribe> sub=subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .in(Subscribe::getChannel, channelList)
                .between(Subscribe::getCreateTime, DateUtil.handleSqlDateStartTime(LocalDateTime.now()),DateUtil.handleSqlDateEndTime(LocalDateTime.now()))
                .list()
                .stream()
                .max(Comparator.comparing(Subscribe::getCreateTime));

        if(sub!=null && sub.isPresent()){
            Subscribe subscribe=sub.get();
            log.info("是否订购"+ rightBusinessEnum.getServiceName()+"(渠道订单)=>mobile:{},status:{},createTime:{},effectTime:{}", mobile,subscribe.getStatus(),DateUtil.dateToLocalDateTime(subscribe.getCreateTime()),effectTime);
            return true;
        }
        log.info("是否订购"+ rightBusinessEnum.getServiceName()+"(渠道订单)=>mobile:{},status:{},createTime:{},effectTime:{}", mobile,"未订购",LocalDateTime.now(),effectTime);
        return false;
    }
    /**
     * 根据手机号查询是否订购沃音乐视频彩铃
     * @param mobile
     * @return
     */
    @Override
    public WoMusicResponse isWoMusicMember(String mobile) {
        ObjectNode dataNode = mapper.createObjectNode();
        dataNode.put("appkey", woyinyueConfigProperties.getAppkey());
        dataNode.put("callNumber", mobile);
        dataNode.put("productId", woyinyueConfigProperties.getProductId());
        dataNode.put("timestamp", DateUtil.formatFullTime(LocalDateTime.now()));
        dataNode.put("digest", MD5Util.getSign(dataNode,woyinyueConfigProperties.getKey()));
        log.info("沃音乐视频彩铃,会员查询,请求参数=>mobile:{},dataNode:{}",mobile,dataNode);
        String response= httpRequestService.implementHttpGetRequest(woyinyueConfigProperties.getQueryUrl(),dataNode,"沃音乐视频彩铃,会员查询");
        try {
            WoMusicResponse woMusic = mapper.readValue(response, WoMusicResponse.class);
            log.info("沃音乐视频彩铃,会员查询,响应参数=>mobile:{},dataNode:{},woMusic:{}",mobile,dataNode,woMusic);
            return woMusic;
        } catch (JsonProcessingException e) {
            log.error("沃音乐视频彩铃,会员查询,Json异常信息=>mobile:{},msg:{},error:{}",mobile,e.getMessage(),e);
            return null;
        }
    }
    /**
     * 判断是否领取权益
     * @param mobile
     * @return
     */
    private Boolean isReceiveRights(RightBusinessEnum rightBusinessEnum,String mobile) {
        //业务列表
        List<String> packNameList= Lists.newArrayList();
        //查询业务包
        List<MiguPack> list=miguPackService.lambdaQuery().eq(MiguPack::getServiceId, rightBusinessEnum.getServiceId()).list();
        list.forEach(item -> {
            packNameList.add(item.getPackName());
        });
        if(packNameList==null || packNameList.size()<=0){
            return false;
        }
        List<JunboChargeLog> chargeLogLists=null;
        //判断是否是会员大礼包
        //支付宝订单与其他业务查询不同
        RightBusinessEnum right=RightBusinessEnum.getByServiceId(rightBusinessEnum.getServiceId());
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(rightBusinessEnum.getServiceId());
        if(right==null && miguPacks!=null && !miguPacks.isEmpty()){
            chargeLogLists = junboChargeLogService.findByMobileAndPackNameListAndDate(mobile,packNameList);
        }else{
            chargeLogLists = junboChargeLogService.findByMobileAndPackNameListAndMonth(mobile,packNameList);
        }
        //当月权益已全部领取
        if(chargeLogLists !=null && chargeLogLists.size()>=packNameList.size()) {
            return false;
        }
        return true;
    }
    /**
     * 特定队列判断是否充值
     * @param mobile
     * @return
     */
    @Override
    public Boolean queueIsReceiveRecharge(String serviceId,String mobile) {
        List<JunboChargeLog> chargeLogLists=null;

        //支付宝订单与其他业务查询不同
        RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(serviceId);
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(serviceId);
        if(rightBusinessEnum==null && miguPacks!=null && !miguPacks.isEmpty()){
            chargeLogLists = junboChargeLogService.findByServiceIdAndMobileAndDate(serviceId,mobile);
        }else{
            chargeLogLists = junboChargeLogService.findByServiceIdAndMobileAndMonth(serviceId,mobile);
        }
        //当月权益已全部领取
        if(chargeLogLists !=null && chargeLogLists.size()>0) {
            return true;
        }
        return false;
    }



    /**
     * 登录
     * @param mobile
     * @return
     */
    @Override
    public FebsResponse unifyLogin(String mobile,String channelCode,String serviceId) {
        //视频彩铃登录
        String spcltoken = new TokenUtil().setUserLoginTime(mobile);
        //百度网盘登录
        FebsResponse bdwpJson=this.memberLogin(mobile,channelCode);
        String bdwpToken=bdwpJson.get("data").toString();
        //统一登录token
        String token=new TokenUtil().setUserLoginTime(spcltoken,bdwpToken);


        if(StringUtils.equalsAny(channelCode,BizConstant.BIZ_QYLI_COMIC_CHANNEL_CODE,BizConstant.BIZ_QYLI_READ_CHANNEL_CODE)){
            //百度网盘判断是否会员
            FebsResponse bdwp=this.checkMember(token,bdwpToken,channelCode);
            log.info("响应=>bdwp:{}",bdwp);
            boolean isCode = bdwp.get("code").toString().equals(String.valueOf(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE.value()));
            if(isCode){
                return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
            }
            return bdwp;
        }
        if(StringUtils.equalsAny(channelCode,BizConstant.BIZ_QYLI_CW_CHANNEL_CODE)){
            //查询是否订购悦动专属6元包-川网
            return miguCWIsMember(mobile, token);
        }

        if(StringUtils.equalsAny(channelCode,BizConstant.BIZ_LHHY_QYB_CHANNEL_CODE)){
            //查询是否订购联合会员权益包
//            return LHHYQYBIsMember(mobile, token,serviceId);
            return LHHYQYBIsMember(mobile,token);
        }


        //视频彩铃判断是否会员
        FebsResponse vrbt=memberRightsExecutionThreadService.thisThreadCheckMemberRights(mobile,token);
        log.info("响应=>mobile:{},vrbt:{}",mobile,vrbt);
        if(vrbt.isOK()){
            return vrbt;
        }
        //百度网盘判断是否会员
        FebsResponse bdwp=this.checkMember(token,bdwpToken,channelCode);
        log.info("响应=>bdwp:{}",bdwp);
        boolean isCode = bdwp.get("code").toString().equals(String.valueOf(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE.value()));
        if(isCode){
            return vrbt;
        }
        return bdwp;

    }


    //    private FebsResponse LHHYQYBIsMember(String mobile, String token,String serviceId) {
//        List<FebsResponse> febsResponse = Lists.newArrayList();
//        //权益会员大礼包
//        final CompletableFuture<Void> qyhylb = CompletableFuture.runAsync(() -> {
//            FebsResponse response = this.isMemberAndIsReceiveRights(mobile,serviceId, token, false, 0L);
//            febsResponse.add(response);
//        }, executor);
//        try {
//            CompletableFuture.allOf(qyhylb).get();
//            //判断本月是否会员并且本月没有领取权益
//            boolean hasSuccess = febsResponse.stream().anyMatch(febs -> febs.isOK());
//            if (hasSuccess) {
//                //返回本月满足会员并且本月没有领取权益数据
//                return febsResponse.stream().filter(febs -> febs.isOK()).collect(Collectors.toList()).get(0);
//            }
//            boolean isCode = febsResponse.stream().anyMatch(febs -> febs.get("code").toString().equals(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())));
//            if (isCode) {
//                return febsResponse.stream().filter(febs -> febs.get("code").toString().equals(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value()))).collect(Collectors.toList()).get(0);
//            }
//            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
//        } catch (Exception e) {
//            e.printStackTrace();
//
//        }
//        return new FebsResponse().fail();
//    }
    private FebsResponse LHHYQYBIsMember(String mobile, String token) {
        //查询支付宝渠道
        List<String> businessTypeList = alipayConfigService.aliPayRightsRechargeList().stream().map(Alipay::getBusinessType).collect(Collectors.toList());
        if(businessTypeList.isEmpty()){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        //查询支付宝签约订单
        AliSignChargingOrder aliOrder = aliSignIsMember(mobile, businessTypeList);
        Map map=Maps.newHashMap();
        map.put("token",token);
        if(aliOrder!=null){
            log.info("支付宝是否订购"+aliOrder.getBusinessType()+"=>手机号:{},订单明细:{}", mobile,aliOrder);
            map.put("serviceId",aliOrder.getBusinessType());
            return new FebsResponse().success().data(map);
        }
        //查询沃悦读订单
        WoReadOrder woReadOrder=woReadIsMember(mobile,businessTypeList);
        if(woReadOrder!=null){
            log.info("沃悦读是否订购"+woReadOrder.getBusinessType()+"=>手机号:{},订单明细:{}", mobile,woReadOrder);
            map.put("serviceId",woReadOrder.getBusinessType());
            return new FebsResponse().success().data(map);
        }
        return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
    }



    private FebsResponse miguCWIsMember(String mobile, String token) {
        List<FebsResponse> febsResponse = Lists.newArrayList();

        //悦动专属6元包-川网
        final CompletableFuture<Void> ydzs = CompletableFuture.runAsync(()->{
            FebsResponse response=this.isMemberAndIsReceiveRights(mobile,RightBusinessEnum.YDZS.getServiceId(), token,false,0L);
            febsResponse.add(response);
        },executor);
        try {
            CompletableFuture.allOf(ydzs).get();
            //判断本月是否会员并且本月没有领取权益
            boolean hasSuccess = febsResponse.stream().anyMatch(febs-> febs.isOK());
            if(hasSuccess){
                //返回本月满足会员并且本月没有领取权益数据
                return febsResponse.stream().filter(febs->febs.isOK()).collect(Collectors.toList()).get(0);
            }
            boolean isCode = febsResponse.stream().anyMatch(febs-> febs.get("code").toString().equals(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())));
            if(isCode){
                return febsResponse.stream().filter(febs-> febs.get("code").toString().equals(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())) ).collect(Collectors.toList()).get(0);
            }
            return new FebsResponse().code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE).message(BizConstant.NOT_MEMBER_MSG);
        } catch (Exception e) {
            e.printStackTrace();

        }
        return new FebsResponse().fail();
    }
    /**
     * 抖音小程序测试登录
     * @param mobile
     * @return
     */
    @Override
    public FebsResponse unifyTestLogin(String mobile,String channelCode) {
        //视频彩铃登录
        String spcltoken = new TokenUtil().setUserLoginTime(mobile);
        //百度网盘登录
        FebsResponse bdwpJson=this.memberLogin(mobile,channelCode);
        String bdwpToken=bdwpJson.get("data").toString();
        //统一登录token
        String token=new TokenUtil().setUserLoginTime(spcltoken,bdwpToken);
        Map map=Maps.newHashMap();
        map.put("serviceId","9527001");
        map.put("token",token);
        return new FebsResponse().success().data(map);

    }
    /**
     * 判断是否会员
     * @param spcltoken
     * @param bdwpToken
     * @return
     */
    @Override
    public FebsResponse unifyIsMember(String unifyToken,String spcltoken,String bdwpToken,String channelCode){
        String mobile =(String) redisUtil.get(spcltoken);
        //线程执行查询是否会员功能
        FebsResponse vrbt=memberRightsExecutionThreadService.thisThreadCheckMemberRights(mobile,null);
        if(vrbt.isOK()){
            return vrbt;
        }
        FebsResponse bdwp=this.checkMember(unifyToken,bdwpToken,channelCode);
        boolean isCode = bdwp.get("code").toString().equals(String.valueOf(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE.value()));
        if(isCode){
            return vrbt;
        }
        return bdwp;
    }
    /**
     * 查询权益列表
     * @param spcltoken
     * @param bdwpToken
     * @return
     */
    @Override
    public FebsResponse unifyQueryRightsList(String spcltoken, String bdwpToken, String serviceId){
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(serviceId);
        if(miguPacks!=null && !miguPacks.isEmpty()){
            List<RightsPackDto> list = rightsPackService.wholeMemberRightsList(serviceId);
            return new FebsResponse().success().data(list);
        }
        return this.queryRightsList(serviceId,bdwpToken);
    }

    /**
     * 查询权益列表
     * @param serviceId
     * @return
     */
    @Override
    public FebsResponse queryRightsList(String serviceId){
        List<RightsPackDto> list = rightsPackService.wholeMemberRightsList(serviceId);
        return new FebsResponse().success().data(list);
    }
    /**
     * 会员领取权益
     * @param spcltoken
     * @param bdwpToken
     * @param serviceId
     * @param rightsId
     * @param packName
     * @return
     */
    @Override
    public FebsResponse unifyMemberReceiveRights(String spcltoken, String bdwpToken, String serviceId, String rightsId, String packName,String account){
        List<MiguPack> miguPacks= miguPackService.queryMiguPackList(serviceId);
        if(miguPacks!=null && !miguPacks.isEmpty()){
            String mobile =(String) redisUtil.get(spcltoken);
            account= oConvertUtils.isEmpty(account)?mobile:account;
            return this.unifyMemberRightsGet(mobile,account,serviceId,rightsId,packName);
        }
        return this.memberReceiveRights( serviceId, packName, rightsId,bdwpToken,account);
    }
    /**
     * 抖音小程序测试会员领取权益
     * @param spcltoken
     * @param bdwpToken
     * @param serviceId
     * @param rightsId
     * @param packName
     * @return
     */
    @Override
    public FebsResponse unifyMemberReceiveRightsTest(String spcltoken, String bdwpToken, String serviceId, String rightsId, String packName,String account){
        return new FebsResponse().success().message(BizConstant.SCHEDULED_MSG);
    }
    /**
     * 查询充值列表
     * @param spcltoken
     * @param bdwpToken
     * @return
     */
    @Override
    public FebsResponse unifyQueryChargeList(String spcltoken, String bdwpToken,String serviceId,String rightsMonth,String sourse){
        String mobile =(String) redisUtil.get(spcltoken);
        if(StringUtils.isNotBlank(serviceId)){
            List<MiguPack> miguPacks= miguPackService.queryMiguPackList(serviceId);
            if(miguPacks!=null && !miguPacks.isEmpty()){
                List<JunboChargeLog> list = junboChargeLogService.selectMain(mobile,rightsMonth,sourse);
                return new FebsResponse().success().data(list);
            }
            return this.queryChargeList(rightsMonth,sourse,bdwpToken);
        }else{
            List<JunboChargeLog> list = junboChargeLogService.selectMain(mobile,rightsMonth,sourse);
            FebsResponse vrbtJson=this.queryChargeList(rightsMonth,sourse,bdwpToken);
            if(vrbtJson.isOK()){
                try{
                    List<JunboChargeLog> vrbt = mapper.readValue(vrbtJson.get("data").toString(), List.class);
                    if(vrbt!=null && vrbt.size()>0){
                        list.addAll(vrbt);
                    }
                }catch (JsonProcessingException e) {
                    log.error("查询充值列表,Json异常信息=>vrbtJson:{},msg:{}",vrbtJson,e.getMessage(),e);
                }
            }
            return new FebsResponse().success().data(list);
        }

    }
    /**
     * 会员登录
     * @param mobile
     * @return
     */
    private FebsResponse memberLogin(String mobile,String channelCode) {
        FebsResponse vrbtJson= unifyRightsFeignClient.memberLogin(mobile,channelCode);
        return vrbtJson;
    }

    /**
     * 查询是否会员
     * @param unifyToken
     * @param token
     * @return
     */
    private FebsResponse checkMember(String unifyToken,String token,String channelCode) {
        FebsResponse vrbtJson= unifyRightsFeignClient.checkMember(unifyToken,token,channelCode);
        return vrbtJson;
    }

    /**
     * 查询产品和产品订阅列表
     * @param token
     * @return
     */
    private FebsResponse queryProductAndSubscribe(String token) {
        FebsResponse vrbtJson= unifyRightsFeignClient.queryProductAndSubscribe(token);
        return vrbtJson;
    }

    /**
     * 会员领取权益
     * @param serviceId
     * @param packName
     * @param rightsId
     * @param token
     * @return
     */
    private FebsResponse memberReceiveRights(String serviceId,String packName,String rightsId,String token,String account) {
        FebsResponse vrbtJson= unifyRightsFeignClient.memberReceiveRights(serviceId,packName,rightsId,token,account);
        return vrbtJson;
    }

    /**
     * 查询充值列表
     * @param rightsMonth
     * @param token
     * @return
     */
    private FebsResponse queryChargeList(String rightsMonth,String sourse,String token) {
        FebsResponse vrbtJson= unifyRightsFeignClient.queryChargeList(rightsMonth,sourse,token);
        return vrbtJson;
    }

    /**
     *
     * @param bdwpToken
     * @return
     */
    @Override
    public FebsResponse unifyReceiveBdwp(String bdwpToken){
        return this.receiveBdwp(bdwpToken);
    }

    /**
     * 领取百度网盘权益
     * @param token
     * @return
     */
    private FebsResponse receiveBdwp(String token) {
        FebsResponse vrbtJson= unifyRightsFeignClient.receiveBdwp(token);
        return vrbtJson;
    }
    /**
     * 查询权益列表
     * @param serviceId
     * @param token
     * @return
     */
    private FebsResponse queryRightsList(String serviceId,String token) {
        FebsResponse vrbtJson= unifyRightsFeignClient.queryRightsList(serviceId,token);
        return vrbtJson;
    }
    /**
     * 会员权益领取
     * @param serviceId
     * @param packName
     * @param rightsId
     * @param mobile
     * @return
     */
    @Override
    public FebsResponse memberReceiveRights(String serviceId,String packName,String rightsId,String mobile,String account,Boolean isExtension,Boolean isPrepareCharge){
        //权益明细查询
        Rights rights=new Rights(serviceId,rightsId,packName,mobile);
        //检查权益是否存在
        FebsResponse febsResponse=checkRights(rights);
        if(!febsResponse.isOK()){
            return febsResponse;
        }
        Result<?> result=this.checkRechargeMember(mobile,account,serviceId,rights,packName,isExtension,isPrepareCharge,rights.getRechargeDelayMinute());
        if(!result.getCode().equals(200)){
            return new FebsResponse().code(result.getCode().toString()).message(result.getMessage());
        }
        JunboChargeLog junboChargeLog = junboChargeLogService.createImmediateRechargeLog(mobile,account,serviceId, rights,packName);
        String orderSEQ = junboChargeLog.getMiguOrderId();
        //券码充值

        if(StringUtils.equals(String.valueOf(rights.getRechargeState()),String.valueOf(BizConstant.RECHARGE_STATE_CODE))){
            return junboChargeLogService.couponCodeCharge(junboChargeLog,mobile);
        }
        //白金会员权益（mgyybjhy）
        if(BizConstant.RECHARGE_COUPON_ID.equals(junboChargeLog.getCouponId())){
            RemoteResult res=miguApiService.bjhyOrder(mobile, BizConstant.RECHARGE_BJHY_CHANNEL_ID);
            junboChargeLogService.updateJunboChargeBJHYStatus(junboChargeLog, res);
            return new FebsResponse().code(res.isOK()?"200":"500").message(res.getResMsg());
        }
        //酷狗VIP月卡直充（kgykzhichong）
        if(BizConstant.KUGOU_COUPON_ID.equals(junboChargeLog.getCouponId())){
            KugouOrderResult res=kugouApiService.order(orderSEQ,mobile);
            junboChargeLogService.updateJunboChargeKGStatus(junboChargeLog, res);
            return new FebsResponse().code(res.isOK()?"200":"500").message(res.getErrorCode());
        }
        if(BizConstant.COMPANY_OWNER_HUAYI.equals(junboChargeLog.getCompanyOwner())){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            junboChargeLogService.updateById(junboChargeLog);
            return new FebsResponse().code("000005".equals(huaYiResp.getCode()) || "000000".equals(huaYiResp.getCode())?"200":"500").message(huaYiResp.getMsg());

        }

//        final JunboResult junboResult = junboApiService.rechargeVIP(junboChargeLog.getCouponId(), orderSEQ, account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        if(null==junboRespon){
            return new FebsResponse().code(HttpStatus.BAD_REQUEST).message("系统繁忙,请稍后再试!");
        }
        Result<?> chargeResult=junboChargeLogService.getReceiveState(junboChargeLog,junboRespon);
        return new FebsResponse().code(chargeResult.getCode().toString()).message(chargeResult.getMessage());
    }



    /**
     * 查询产品和产品订阅列表
     * @param spcltoken
     * @param bdwpToken
     * @return
     */
    @Override
    public FebsResponse unifyQueryProductAndSubscribe(String spcltoken, String bdwpToken){
        String mobile =(String) redisUtil.get(spcltoken);
        List<Product> list=memberRightsExecutionThreadService.queryProductAndSubscribe(mobile,spcltoken);
        FebsResponse vrbtJson=this.queryProductAndSubscribe(bdwpToken);
        if(vrbtJson.isOK()){
            try{
                List<Product> vrbt = mapper.readValue(vrbtJson.get("data").toString(), List.class);
                if(vrbt!=null && vrbt.size()>0){
                    list.addAll(vrbt);
                }
            }catch (JsonProcessingException e) {
                log.error("查询产品和产品订阅列表,Json异常信息=>vrbtJson:{},msg:{}",vrbtJson,e.getMessage(),e);
            }
        }
        return new FebsResponse().success().data(list);
    }

    /**
     * 抖音会员登录
     * @param mobile 手机号
     * @return
     */
    @Override
    public FebsResponse douyinMemberLogin(String mobile,String channelCode) {
        //视频彩铃登录
        String spcltoken = new TokenUtil().setUserLoginTime(mobile);
        //百度网盘登录
        FebsResponse bdwpJson=this.memberLogin(mobile,channelCode);
        String bdwpToken=bdwpJson.get("data").toString();
        //统一登录token
        String token=new TokenUtil().setUserLoginTime(spcltoken,bdwpToken);
        return new FebsResponse().success().data(token);
    }
    /**
     * 抖音权益充值
     * @param mobile 手机号
     * @param account 账号
     * @param couponId 产品Id
     * @param couponName 产品名称
     * @return
     */
    @Override
    public FebsResponse douyinRightsRecharge(String mobile,String account,String couponId,String couponName) {
        log.info("抖音权益充值=>mobile:{},account:{},couponId:{},couponName:{}",mobile,account,couponId,couponName);
        String sign= DigestUtils.md5DigestAsHex((mobile+account+couponId+couponName+DOUYING_CHANNEL_PASSWORD).getBytes(StandardCharsets.UTF_8));
        return unifyRightsFeignClient.douyinRightsRecharge(mobile,account,couponId,couponName,DOUYING_CHANNEL_ID,sign);
    }

    /**
     * 抖音查询权益产品价格
     * @param mobile 手机号
     * @param couponId 产品Id
     * @return
     */
    @Override
    public FebsResponse douyinQueryRightsProductPrice(String mobile,String couponId) {
        log.info("抖音查询权益产品价格=>mobile:{},couponId:{}",mobile,couponId);
        String sign= DigestUtils.md5DigestAsHex((mobile+couponId+DOUYING_CHANNEL_PASSWORD).getBytes(StandardCharsets.UTF_8));
        return unifyRightsFeignClient.douyinQueryRightsProductPrice(mobile,couponId,DOUYING_CHANNEL_ID,sign);
    }

    /**
     * 白金会员权益充值
     * @param phone
     * @param channelId
     * @return
     */
    @Override
    public FebsResponse rechargeBjhyRights(String phone, String channelId, String serviceId, String name, String rightsId) {
        //白金会员充值限制
        String day = DateUtil.formatYearMonthDay(LocalDateTime.now());
        FebsResponse febs=rechargeAlertService.bjhyVrbtRechargeLimit(phone,serviceId,"bjhyys_rights_pack_1",channelId,day);
        if(!febs.isOK()){
            return febs;
        }
        //查询可充值权益
        Optional<MemberRights> memberRightsOptional=memberRightsService.queryMemberRightsDetail(rightsId);
        if(memberRightsOptional==null || !memberRightsOptional.isPresent()){
            return new FebsResponse().code(HttpStatus.HTTP_VERSION_NOT_SUPPORTED).message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        MemberRights memberRights=memberRightsOptional.get();
        Rights rights=new Rights();
        rights.setCouponId(memberRights.getCouponId());
        rights.setRightsName(memberRights.getRightsName());
        rights.setProductPrice(memberRights.getProductPrice());
        rights.setRechargeState(memberRights.getRechargeState());
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        rights.setRightsMonth(rightsMonth);
        rights.setCompanyOwner(memberRights.getCompanyOwner());
        JunboChargeLog junboChargeLog = junboChargeLogService.buildJunboChargeLog(phone,phone,serviceId,rights,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,null,name,"bjhyys_rights_pack_1");
        RemoteResult res=miguApiService.bjhyOrder(phone, BizConstant.RECHARGE_BJHY_CHANNEL_ID);
        junboChargeLogService.updateJunboChargeBJHYStatus(junboChargeLog, res);
        return new FebsResponse().code(res.isOK()?HttpStatus.OK:HttpStatus.INTERNAL_SERVER_ERROR).message(res.getResMsg());
    }

    /**
     * 视频彩铃包月权益充值
     * 没有功能也能充值成功,只是用不起
     * @param phone
     * @param nMonth 充多少个月
     * @return
     */
    @Override
    public FebsResponse rechargeVrbtRights(String phone, Integer nMonth) {
        //视频彩铃包月充值限制
        String day = DateUtil.formatYearMonthDay(LocalDateTime.now());
        final String packName = "vrbt_rights_pack_1";
        String rightsId = "mgyyvrbt";
        String serviceId = "698039035103445177";
        FebsResponse febs=rechargeAlertService.bjhyVrbtRechargeLimit(phone,serviceId, packName,BizConstant.RECHARGE_VRBT_CHANNEL_ID,day);
        if(!febs.isOK()){
            return febs;
        }
        //查询可充值权益

        Optional<MemberRights> memberRightsOptional=memberRightsService.queryMemberRightsDetail(rightsId);
        if(memberRightsOptional==null || !memberRightsOptional.isPresent()){
            return new FebsResponse().code(HttpStatus.HTTP_VERSION_NOT_SUPPORTED).message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        MemberRights memberRights=memberRightsOptional.get();
        Rights rights=new Rights();
        rights.setCouponId(memberRights.getCouponId());
        rights.setRightsName(memberRights.getRightsName());
        rights.setProductPrice(memberRights.getProductPrice()*nMonth);
        rights.setRechargeState(memberRights.getRechargeState());
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        rights.setRightsMonth(rightsMonth);
        rights.setCompanyOwner(memberRights.getCompanyOwner());
        RemoteResult res = miguApiService.vrbtZeroOrder(phone, BizConstant.RECHARGE_VRBT_CHANNEL_ID,nMonth);
        Integer chargeStatus = res.isOK() ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
        junboChargeLogService.buildJunboChargeLog(phone,phone,serviceId,rights,chargeStatus,null, String.valueOf(nMonth),packName);

        return new FebsResponse().code(res.isOK()?HttpStatus.OK:HttpStatus.INTERNAL_SERVER_ERROR).message(res.getResMsg());
    }

    /**
     * 话费充值表同步
     * @param channel
     * @param serviceId
     */
    @Override
    public void feeRechargeAdd(String channel,String serviceId,String date){
        List<BusinessOrderLog> businessOrderLogList=Lists.newArrayList();
        if("698039020108689345".equals(serviceId)) {
            LocalDateTime first = LocalDateTime.of( LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
            LocalDateTime end=    LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MAX);
            int day =LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
            for(int i=0;i<day;i++){
                try{
                    String  firstDays=DateUtil.formatSplitTime(first.plusDays(i));
                    String  endDays=DateUtil.formatSplitTime(end.plusDays(i));
                    //查询上月20元渠道包月包订购数据
                    List<BusinessOrderLog> businessList=subscribeService.businessOrderLogList(firstDays,endDays,channel,serviceId, MobileRegionResult.ISP_YIDONG);
                    businessOrderLogList.addAll(businessList);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }else if("*********".equals(serviceId)) {
            //上月
            // 获取当前月的第一天
            LocalDateTime firstDay = LocalDateTime.of( LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
            LocalDateTime endDay= LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
            FebsResponse febs=unifyRightsFeignClient.businessOrderLogList(DateUtil.formatSplitTime(firstDay),DateUtil.formatSplitTime(endDay),serviceId);
            if(febs.isOK()) {
                try{
                    List<BusinessOrderLog>  resultList = mapper.readValue(febs.get("data").toString(), List.class);
                    //再次转换list
                    ObjectMapper objectMapper=new ObjectMapper();
                    String tranStr = objectMapper.writeValueAsString(resultList);
                    JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, BusinessOrderLog.class);
                    businessOrderLogList= objectMapper.readValue(tranStr, javaType);
                }catch (Exception e) {
                    log.error("查询上月咪咕动漫订购数据,Json异常信息=>febs:{}",febs,e);
                }
            }
        }
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        LocalDateTime firstDay = LocalDateTime.of( LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        String orderMonth =  DateUtil.formatYearMonth(firstDay);
        if(businessOrderLogList!=null && businessOrderLogList.size()>0){
            log.info("权益充值,20元北岸唐唱音乐包订购关系查询=>businessOrderLogList: {}",businessOrderLogList.size());
            for (int i = 0; i < businessOrderLogList.size(); i++) {
                try{
                    if(redisUtil.hasKey(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE) && StringUtils.equals(redisUtil.get(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE).toString(),BizConstant.MOBILE_FEE_CHARGE_SWITCHS_CLOSE)){
                        log.error("权益充值,话费充值表同步手动停止");
                        break;
                    }
                    BusinessOrderLog business = businessOrderLogList.get(i);
                    //同步充值记录
                    syncFeeRechargeLog(channel, serviceId, rightsMonth, orderMonth, business);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }

    }

    /**
     * 同步话费充值记录
     * @param channel
     * @param serviceId
     * @param rightsMonth
     * @param orderMonth
     * @param business
     */
    private synchronized void syncFeeRechargeLog(String channel, String serviceId, String rightsMonth, String orderMonth, BusinessOrderLog business) {
        String mobile= business.getMobile();
        String subChannel= business.getSubChannel();
        Boolean isMember=false;
        if("698039020108689345".equals(serviceId)) {
            //订购关系
            final RemoteResult batc =miguApiService.cpmbQuery(mobile, channel);
            log.info("权益充值,20元北岸唐唱音乐包订购关系查询=>mobile: {},batc: {}",mobile,batc);
            if(batc.isCpmbMember()) {
                isMember=true;
            }
        }else if("*********".equals(serviceId)) {
            //订购关系
            FebsResponse feb = unifyRightsFeignClient.isMember(mobile);
            log.info("权益充值,骏伯咪咕动漫联合会员订购关系查询=>mobile: {},feb: {}",mobile, feb);
            if (feb.isOK()) {
                isMember=true;
            }
        }
        if(isMember){
            //查询是否添加订购数据
            Integer chargeCount = mobileFeeChargeLogService.lambdaQuery()
                    .eq(MobileFeeChargeLog::getMobile,mobile)
                    .eq(MobileFeeChargeLog::getServiceId, serviceId).count();
            log.info("权益充值,是否添加订购数据=>mobile: {},chargeCount: {}",mobile,chargeCount);
            if(chargeCount<=0){
                MobileFeeChargeLog mobileFeeChargeLog=new MobileFeeChargeLog();
                mobileFeeChargeLog.setMobile(business.getMobile());
                //订购时间
                mobileFeeChargeLog.setOrderMonth(orderMonth);
                mobileFeeChargeLog.setRightsMonth(rightsMonth);
                mobileFeeChargeLog.setServiceId(serviceId);
                mobileFeeChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED);
                MobileRegionResult mobileRegionResult = mobileRegionService.query(business.getMobile());
                if(mobileRegionResult!=null) {
                    mobileFeeChargeLog.setProvince(mobileRegionResult.getProvince());
                    mobileFeeChargeLog.setCity(mobileRegionResult.getCity());
                }
                mobileFeeChargeLog.setAccount(mobile);
                //渠道号
                mobileFeeChargeLog.setChannel(channel);
                if("698039020108689345".equals(serviceId)) {
                    mobileFeeChargeLog.setPackName("cpmb_rights_pack_4");
                }else if("*********".equals(serviceId)) {
                    mobileFeeChargeLog.setPackName("mgdmjb_rights_pack_3");
                }
                mobileFeeChargeLog.setCouponId("JBHF5");
                mobileFeeChargeLog.setProductPrice("500");
                mobileFeeChargeLog.setRechargeState("0");
                mobileFeeChargeLog.setCouponName("5元话费（移动）");
                mobileFeeChargeLog.setUpdateTime(new Date());
                mobileFeeChargeLog.setSubChannel(subChannel);
                mobileFeeChargeLogService.save(mobileFeeChargeLog);
                log.info("权益充值,添加本月充值记录=>chargeLog: {}",mobileFeeChargeLog);
            }
        }
    }
    /**
     * 话费充值，续订同步
     */
    @Override
    public void feeRenew(String serviceId) {
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        //查询当月20元渠道包月包订购数据
        List<MobileFeeChargeLog> mobileFeeChargeLogList=mobileFeeChargeLogService.lambdaQuery()
                .eq(MobileFeeChargeLog::getRightsMonth, rightsMonth)
                .eq(MobileFeeChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED)
                .eq(MobileFeeChargeLog::getServiceId, serviceId)
                .eq(MobileFeeChargeLog::getUpdateBy, 0)
                .list();
        // 获取本月的第一天
        LocalDateTime firstDay = LocalDateTime.of( LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        // 获取本月的最后一天
        LocalDateTime endDay= LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        if(mobileFeeChargeLogList!=null && mobileFeeChargeLogList.size()>0){
            for (int i = 0; i < mobileFeeChargeLogList.size(); i++) {
                try{
                    if(redisUtil.hasKey(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE) && StringUtils.equals(redisUtil.get(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE).toString(),BizConstant.MOBILE_FEE_CHARGE_SWITCHS_CLOSE)){
                        log.error("权益充值,话费充值手动停止");
                        break;
                    }
                    MobileFeeChargeLog mobileFeeChargeLog = mobileFeeChargeLogList.get(i);
                    //续订同步
                    renew(firstDay, endDay,mobileFeeChargeLog);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }

    }

    /**
     * 续订同步
     * @param firstDay
     * @param endDay
     * @param mobileFeeChargeLog
     */
    private synchronized void renew(LocalDateTime firstDay, LocalDateTime endDay, MobileFeeChargeLog mobileFeeChargeLog) {
        try {
            String mobile= mobileFeeChargeLog.getMobile();
            String serviceId= mobileFeeChargeLog.getServiceId();
            String channel= mobileFeeChargeLog.getChannel();
            Boolean isMember=false;
            if("698039020108689345".equals(serviceId)) {
                //订购关系
                final RemoteResult batc =miguApiService.cpmbQuery(mobile,channel);
                String  firstDays=DateUtil.formatSplitTime(firstDay);
                String  endDays=DateUtil.formatSplitTime(endDay);
                //是否重复订购
                Integer subscribeCount=subscribeService.businessOrderLogCount( firstDays,  endDays, channel,  mobile,"1");
                log.info("权益充值,20元北岸唐唱音乐包订购关系查询=>mobile:{},batc:{},subscribeCount:{}",mobile,batc,subscribeCount);
                if(batc.isCpmbMember() && subscribeCount<=0) {
                    isMember=true;
                }
            }else if("*********".equals(serviceId)) {
                //订购关系
                FebsResponse feb = unifyRightsFeignClient.isMember(mobile);
                log.info("权益充值,骏伯咪咕动漫联合会员订购关系查询=>mobile:{},feb: {}", mobile,feb);
                if (feb.isOK()) {
                    isMember=true;
                }
            }
            if(!isMember){
                log.info("权益充值,上月订购,本月重复订购,设置为失效订单=>mobile:{},chargeLog: {}",mobile, mobileFeeChargeLog);
                //设置为失效订单
                mobileFeeChargeLogService.lambdaUpdate()
                        .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                        .set(MobileFeeChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_INVALID)
                        .set(MobileFeeChargeLog::getRemark, "上月订购，本月重复订购")
                        .set(MobileFeeChargeLog::getUpdateBy, 1)
                        .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
            }else{
                log.info("权益充值,续订,设置为设置为已执行=>mobile:{},chargeLog: {}",mobile, mobileFeeChargeLog);
                //设置为已执行
                mobileFeeChargeLogService.lambdaUpdate()
                        .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                        .set(MobileFeeChargeLog::getUpdateBy, 1)
                        .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 话费充值
     */
    @Override
    public void feeRecharge(String serviceId) {
        String rightsMonth = DateUtil.formatYearMonth(LocalDateTime.now());
        //查询当月20元渠道包月包订购数据
        List<MobileFeeChargeLog> mobileFeeChargeLogList=mobileFeeChargeLogService.lambdaQuery()
                .eq(MobileFeeChargeLog::getRightsMonth, rightsMonth)
                .eq(MobileFeeChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED)
                .eq(MobileFeeChargeLog::getServiceId, serviceId)
                .list();

        // 获取本月的第一天
        LocalDateTime firstDay = LocalDateTime.of( LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        // 获取本月的最后一天
        LocalDateTime endDay= LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        // 获取次月的第一天
        LocalDateTime plusDateTime = LocalDateTime.of(LocalDate.now().plusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        //次月权益月份
        String plusRightsMonth = DateUtil.formatYearMonth(plusDateTime);
        if(mobileFeeChargeLogList!=null && mobileFeeChargeLogList.size()>0){
            for (int i = 0; i < mobileFeeChargeLogList.size(); i++) {
                try{
                    if(redisUtil.hasKey(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE) && StringUtils.equals(redisUtil.get(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE).toString(),BizConstant.MOBILE_FEE_CHARGE_SWITCHS_CLOSE)){
                        log.error("权益充值,话费充值手动停止");
                        break;
                    }
                    MobileFeeChargeLog mobileFeeChargeLog = mobileFeeChargeLogList.get(i);
                    //执行充值
                    executeRecharge(rightsMonth, firstDay, endDay, plusRightsMonth, mobileFeeChargeLog);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }

    }

    /**
     * 同步充值
     * @param rightsMonth
     * @param firstDay
     * @param endDay
     * @param plusRightsMonth
     * @param mobileFeeChargeLog
     */
    private synchronized void executeRecharge(String rightsMonth, LocalDateTime firstDay, LocalDateTime endDay, String plusRightsMonth, MobileFeeChargeLog mobileFeeChargeLog) {
        try {
            TimeUnit.SECONDS.sleep(1);
            String mobile= mobileFeeChargeLog.getMobile();
            String account= mobileFeeChargeLog.getAccount();
            String orderMonth= mobileFeeChargeLog.getOrderMonth();
            String serviceId= mobileFeeChargeLog.getServiceId();
            String couponName= mobileFeeChargeLog.getCouponName();
            String channel= mobileFeeChargeLog.getChannel();
            String packName= mobileFeeChargeLog.getPackName();
            String couponId= mobileFeeChargeLog.getCouponId();
            String productPrice= mobileFeeChargeLog.getProductPrice();
            String rechargeState= mobileFeeChargeLog.getRechargeState();

            Boolean isMember=false;
            if("698039020108689345".equals(serviceId)) {
                //订购关系
                final RemoteResult batc =miguApiService.cpmbQuery(mobile,channel);
                String  firstDays=DateUtil.formatSplitTime(firstDay);
                String  endDays=DateUtil.formatSplitTime(endDay);
                //是否重复订购
                Integer subscribeCount=subscribeService.businessOrderLogCount( firstDays,  endDays, channel,  mobile,"1");
                log.info("权益充值,20元北岸唐唱音乐包订购关系查询=>mobile: {},batc: {},subscribeCount: {}",mobile,batc,subscribeCount);
                if(batc.isCpmbMember() && subscribeCount<=0) {
                    isMember=true;
                }
            }else if("*********".equals(serviceId)) {
                //订购关系
                FebsResponse feb = unifyRightsFeignClient.isMember(mobile);
                log.info("权益充值,骏伯咪咕动漫联合会员订购关系查询=>mobile: {},feb: {}",mobile, feb);
                if (feb.isOK()) {
                    isMember=true;
                }
            }

            if(isMember){
                //总数据
                Integer chargeCount = mobileFeeChargeLogService.lambdaQuery()
                        .eq(MobileFeeChargeLog::getMobile,mobile)
                        .eq(MobileFeeChargeLog::getServiceId,serviceId).count();
                //查询次月充值记录是否已添加
                Integer newChargeCount = mobileFeeChargeLogService.lambdaQuery()
                        .eq(MobileFeeChargeLog::getMobile,mobile)
                        .eq(MobileFeeChargeLog::getRightsMonth, plusRightsMonth)
                        .eq(MobileFeeChargeLog::getServiceId,serviceId).count();
                //原始数据已添加一条(添加下月充值记录)
                if(chargeCount<=3 && newChargeCount<=0){
                    MobileFeeChargeLog mobileFeeCharge=new MobileFeeChargeLog();
                    mobileFeeCharge.setMobile(mobile);
                    //订购时间
                    mobileFeeCharge.setOrderMonth(orderMonth);
                    //下月记录
                    mobileFeeCharge.setRightsMonth(plusRightsMonth);
                    mobileFeeCharge.setServiceId(serviceId);
                    mobileFeeCharge.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED);
                    mobileFeeCharge.setCouponName(couponName);
                    MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
                    if(mobileRegionResult!=null) {
                        mobileFeeChargeLog.setProvince(mobileRegionResult.getProvince());
                        mobileFeeChargeLog.setCity(mobileRegionResult.getCity());
                    }
                    mobileFeeCharge.setAccount(account);
                    //渠道号
                    mobileFeeCharge.setChannel(channel);
                    mobileFeeCharge.setPackName(packName);
                    mobileFeeCharge.setCouponId(couponId);
                    mobileFeeCharge.setProductPrice(productPrice);
                    mobileFeeCharge.setRechargeState(rechargeState);
                    mobileFeeCharge.setUpdateTime(new Date());
                    mobileFeeChargeLogService.save(mobileFeeCharge);
                    log.info("权益充值,添加下月充值记录=>mobile: {},chargeLog: {}",mobile,mobileFeeCharge);
                }
                //查询该手机号当月所有订单
                List<JunboChargeLog> chargeLogList=null;


                //支付宝订单与其他业务查询不同
                RightBusinessEnum rightBusinessEnum=RightBusinessEnum.getByServiceId(serviceId);
                List<MiguPack> miguPacks= miguPackService.queryMiguPackList(serviceId);
                if(rightBusinessEnum==null && miguPacks!=null && !miguPacks.isEmpty()){
                    chargeLogList = junboChargeLogService.findByMobileAndPackNameAndDate(mobile,packName);
                }else{
                    chargeLogList = junboChargeLogService.findByMobileAndPackNameAndMonth(mobile,packName);
                }
                //判断当月是否有已充值成功的订单
                boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
                if(hasSuccess){
                    log.info("权益充值,本月订单已充值=>mobile: {},chargeLog: {}",mobile, mobileFeeChargeLog);
                    mobileFeeChargeLogService.lambdaUpdate()
                            .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                            .set(MobileFeeChargeLog::getRemark, "本月订单已充值")
                            .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
                    return;
                }
                //判断当月是否有预约直充的订单
                boolean hasScheduled = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
                if(hasScheduled){
                    log.info("权益充值,本月订单正在预约充值=>mobile: {},chargeLog: {}",mobile, mobileFeeChargeLog);
                    mobileFeeChargeLogService.lambdaUpdate()
                            .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                            .set(MobileFeeChargeLog::getRemark, "本月订单正在预约充值")
                            .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
                    return;
                }
                //判断当月是否有正在充值的订单
                boolean hasProcessing = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
                if(hasProcessing){
                    log.info("权益充值,本月订单正在直充中=>mobile: {},chargeLog: {}",mobile, mobileFeeChargeLog);
                    mobileFeeChargeLogService.lambdaUpdate()
                            .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                            .set(MobileFeeChargeLog::getRemark, "本月订单正在充值中")
                            .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
                    return;
                }
                Rights rights=new Rights();
                rights.setCouponId(couponId);
                rights.setRightsName(couponName);
                rights.setProductPrice(Integer.valueOf(productPrice));
                rights.setRightsMonth(rightsMonth);
                rights.setRechargeState(Integer.valueOf(rechargeState));
                rights.setCompanyOwner("JUNBO");
                JunboChargeLog junboChargeLog = junboChargeLogService.createImmediateRechargeLog(account,mobile,serviceId, rights,packName);
                String orderSEQ = junboChargeLog.getMiguOrderId();
                if(StringUtils.equals(junboChargeLog.getCompanyOwner(),BizConstant.COMPANY_OWNER_HUAYI)){
                    HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
                    junboChargeLog.setRespCode(huaYiResp.getCode());
                    junboChargeLog.setRespMsg(huaYiResp.getMsg());
                    junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
                    junboChargeLog.setUpdateTime(new Date());
                    int status =0;
                    if("000005".equals(huaYiResp.getCode())){
                        status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
                    }else if("000000".equals(huaYiResp.getCode())){
                        status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
                    }else{
                        status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
                    }
                    junboChargeLog.setStatus(status);
                    junboChargeLogService.updateById(junboChargeLog);
                    return;
                }

//                final JunboResult junboResult = junboApiService.rechargeVIP(rights.getCouponId(), orderSEQ, account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());

                final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
                if(null==junboRespon){
                    log.info("权益充值,充值失败=>mobile: {},chargeLog: {}",mobile, mobileFeeChargeLog);
                    mobileFeeChargeLogService.lambdaUpdate()
                            .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                            .set(MobileFeeChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_FAIL)
                            .set(MobileFeeChargeLog::getOrderId, orderSEQ)
                            .set(MobileFeeChargeLog::getRemark, "系统繁忙,请稍后再试!")
                            .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
                }
                Result<?> chargeResult=junboChargeLogService.getReceiveState(junboChargeLog,junboRespon);
                log.info("权益充值,正在充值中=>mobile: {},chargeLog: {}",mobile, mobileFeeChargeLog);
                //充值描述
                mobileFeeChargeLogService.lambdaUpdate()
                        .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                        .set(MobileFeeChargeLog::getOrderId, orderSEQ)
                        .set(MobileFeeChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING)
                        .set(MobileFeeChargeLog::getRemark, chargeResult.getCode().toString()+"："+chargeResult.getMessage())
                        .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
                return;
            }else{
                log.info("权益充值,上月订购,本月重复订购,设置为失效订单=>mobile: {},chargeLog: {}",mobile, mobileFeeChargeLog);
                //设置为失效订单
                mobileFeeChargeLogService.lambdaUpdate()
                        .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                        .set(MobileFeeChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_INVALID)
                        .set(MobileFeeChargeLog::getRemark, "上月订购，本月重复订购")
                        .set(MobileFeeChargeLog::getUpdateTime,new Date()).update();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 话费充值，城市同步
     */
    @Override
    public void feeCity() {
        //查询当月20元渠道包月包订购数据
        List<MobileFeeChargeLog> mobileFeeChargeLogList=mobileFeeChargeLogService.lambdaQuery()
                .eq(MobileFeeChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED)
                .list();
        //次月权益月份
        if(mobileFeeChargeLogList!=null && mobileFeeChargeLogList.size()>0){
            for (int i = 0; i < mobileFeeChargeLogList.size(); i++) {
                try{
                    if(redisUtil.hasKey(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE) && StringUtils.equals(redisUtil.get(CacheConstant.CMS_CACHE_MOBILE_FEE_CHARGE).toString(),BizConstant.MOBILE_FEE_CHARGE_SWITCHS_CLOSE)){
                        log.error("权益充值,话费充值手动停止");
                        break;
                    }
                    MobileFeeChargeLog mobileFeeChargeLog = mobileFeeChargeLogList.get(i);
                    //城市同步
                    city(mobileFeeChargeLog);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }

    }

    /**
     * 城市同步
     * @param mobileFeeChargeLog
     */
    private synchronized void city(MobileFeeChargeLog mobileFeeChargeLog) {
        try {
            String mobile= mobileFeeChargeLog.getMobile();
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            String province="";
            String city="";
            if(mobileRegionResult!=null) {
                province=mobileRegionResult.getProvince();
                city=mobileRegionResult.getCity();
            }
            mobileFeeChargeLogService.lambdaUpdate()
                    .eq(MobileFeeChargeLog::getId, mobileFeeChargeLog.getId())
                    .set(MobileFeeChargeLog::getProvince,province)
                    .set(MobileFeeChargeLog::getCity,city).update();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 查询渠道业务关联的权益列表
     * @param channelCode
     * @return
     */
    @Override
    public FebsResponse queryRightsListByChannel(String channelCode){
        BusinessChannelRights businessChannelRights=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getBusinessChannel,channelCode).eq(BusinessChannelRights::getIsEffect,"1").orderByDesc(BusinessChannelRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(businessChannelRights==null){
            return new FebsResponse().fail().message("业务已下架");
        }
        List<BusinessPack> businessPackList=businessPackService.lambdaQuery().eq(BusinessPack::getBusinessId,businessChannelRights.getId()).orderByDesc(BusinessPack::getCreateTime).list();
        if(businessPackList==null || businessPackList.size()<=0){
            return new FebsResponse().fail().message("权益已下架");
        }
        StringBuffer ids = new StringBuffer();
        for (BusinessPack business:businessPackList){
            if(null != business.getId()){//做非空判断
                ids.append(business.getPackId()+",");
            }
        }
        String id[]=ids.toString().split(",");
        List<RightsPackDto> list = rightsPackService.packList(id);
        FebsResponse listFebs=unifyRightsFeignClient.queryRightsList(ids.toString());
        if(listFebs.isOK()){
            try{
                List<RightsPackDto> lists = mapper.readValue(listFebs.get("data").toString(), List.class);
                if(lists!=null && lists.size()>0){
                    list.addAll(lists);
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return new FebsResponse().success().data(list);
    }
    @Override
    public Result<?> renewRightsReceiveSms(String channelId, String serviceType) {
        String renewRedisKey ="renew::rights::receive::sms:" +channelId;
        if(redisUtil.hasKey(renewRedisKey)){
            return Result.error("该渠道本月续订提醒短信已发送");
        }
        redisUtil.set(renewRedisKey,"success",BizConstant.RENEW_TIME);
        String serviceId = cmsCrackConfigService.getCrackConfigByChannel(channelId).getServiceId();
        LocalDateTime start = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
        LocalDateTime end=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        //查询当天最新数据
        List<Subscribe> subList=subscribeService.lambdaQuery()
                .eq(Subscribe::getChannel, channelId)
                .eq(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS)
                .between(Subscribe::getCreateTime, start,end)
                .list();
        List<Subscribe> subRemovaList = subList.stream().collect(//list是需要去重的list，返回值是去重后的list
                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getMobile()))), ArrayList::new));
        subRemovaList.forEach(sub -> {
            FebsResponse response=this.isMemberAndIsReceiveRights(sub.getMobile(),serviceId,null,false,0L);
            if(response.isOK()){
                smsModelService.sendSmsAsync(sub.getMobile(),channelId,serviceId, serviceType);
            }
        });
        return Result.ok();
    }


    /**
     * 支付宝权益充值
     * @param channelId
     * @param mobile
     * @param account
     * @param serviceId
     * @param rightsId
     * @param packName
     * @return
     */
    @Override
    public FebsResponse alipayRightsRecharge(String channelId,String mobile,String account,String serviceId,String rightsId,String packName,String orderId) {
        //查询可充值权益
        Optional<MemberRights> memberRightsOptional=memberRightsService.queryMemberRightsDetail(rightsId);
        if(memberRightsOptional==null || !memberRightsOptional.isPresent()){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_MEDIA_MEMBER);
        }
        //查询业务包
        Optional<MiguPack> miguPackOptional=miguPackService.queryMiguPackDetail(serviceId,packName);
        if(miguPackOptional==null || !miguPackOptional.isPresent()){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        //支付宝渠道
        List<String> businessTypeList = alipayConfigService.aliPayRightsRechargeList().stream().map(Alipay::getBusinessType).collect(Collectors.toList());
        if(businessTypeList.isEmpty()){
            return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
        }
        AliSignChargingOrder chargingOrder = chargingOrderService.lambdaQuery()
                .eq(AliSignChargingOrder::getMobile,mobile)
                .eq(AliSignChargingOrder::getOrderStatus,1)
                .in(AliSignChargingOrder::getBusinessType, businessTypeList)
                .eq(AliSignChargingOrder::getRefundStatus, 0)
                .between(AliSignChargingOrder::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .orderByDesc(AliSignChargingOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        log.info("是否订购"+chargingOrder.getBusinessName()+"=>mobile:{},order:{}", mobile,chargingOrder);
        if(chargingOrder==null){
            return new FebsResponse().fail().message(BizConstant.NOT_MEMBER_MSG);
        }

        if(!chargingOrder.getBusinessType().equals(serviceId)){
            return new FebsResponse().fail().message("业务已下架");
        }

        List<JunboChargeLog> rechargeList=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getMobile,mobile).eq(JunboChargeLog::getServiceId,serviceId).between(JunboChargeLog::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now()).list();
        //判断当月是否有已充值成功的订单
        boolean isSuccess = rechargeList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(isSuccess){
            log.info("权益充值,本月订单已充值=>手机号: {},充值列表: {}",mobile, rechargeList);
            String couponName=rechargeList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().repeatRecharge(couponName);
        }
        //判断当月是否有预约直充的订单
        boolean isScheduled = rechargeList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus()));
        if(isScheduled){
            log.info("权益充值,本月订单正在预约充值=>手机号: {},充值列表: {}",mobile, rechargeList);
            String couponName=rechargeList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().repeatRecharge(couponName);
        }
        //判断当月是否有正在充值的订单
        boolean isProcessing = rechargeList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus()));
        if(isProcessing){
            log.info("权益充值,本月订单正在直充中=>手机号: {},充值列表: {}",mobile, rechargeList);
            String couponName=rechargeList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(JunboChargeLog::getCreateTime)).get().getCouponName();
            return new FebsResponse().repeatRecharge(couponName);
        }


        MemberRights memberRights=memberRightsOptional.get();
        Rights rights=new Rights();
        rights.setCouponId(memberRights.getCouponId());
        rights.setRightsName(memberRights.getRightsName());
        rights.setProductPrice(memberRights.getProductPrice());
        rights.setRechargeState(memberRights.getRechargeState());
        rights.setRightsMonth(DateUtil.formatYearMonth(LocalDateTime.now()));
        rights.setPayTime(chargingOrder.getPayTime());
        rights.setCompanyOwner(memberRights.getCompanyOwner());
        account= Strings.isNullOrEmpty(account)?mobile:account;

        JunboChargeLog junboChargeLog = junboChargeLogService.alipayRightsRecharge(mobile,account,serviceId,rights,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,channelId,packName,orderId);
        String orderSEQ = junboChargeLog.getMiguOrderId();

        if(StringUtils.equals(junboChargeLog.getCompanyOwner(),BizConstant.COMPANY_OWNER_HUAYI)){
            HuaYiResp huaYiResp=junboApiService.rechargeVIPHuaYi(junboChargeLog.getCouponId(),mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setRespCode(huaYiResp.getCode());
            junboChargeLog.setRespMsg(huaYiResp.getMsg());
            junboChargeLog.setJunboOrderId(huaYiResp.getSysOrderId());
            junboChargeLog.setUpdateTime(new Date());
            int status =0;
            if("000005".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
            }else if("000000".equals(huaYiResp.getCode())){
                status = BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS;
            }else{
                status = BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            }
            junboChargeLog.setStatus(status);
            junboChargeLogService.updateById(junboChargeLog);
            return new FebsResponse().code("000005".equals(huaYiResp.getCode()) || "000000".equals(huaYiResp.getCode())?"200":"500").message(huaYiResp.getMsg());
        }

//        final JunboResult junboResult = junboApiService.rechargeVIP(junboChargeLog.getCouponId(), orderSEQ, account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());

        final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(), junboChargeLog.getMiguOrderId(), account, mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
        if(null==junboRespon){
            return new FebsResponse().fail().message("系统繁忙,请稍后再试!");
        }
        Result<?> chargeResult=junboChargeLogService.getReceiveState(junboChargeLog,junboRespon);
        return new FebsResponse().success().message(chargeResult.getMessage()).data(orderSEQ);
    }


    /**
     * 支付宝权益充值订单查询
     * @param orderId
     * @return
     */
    @Override
    public FebsResponse alipayQueryRechargeByOrderId(String orderId) {
        JunboChargeLog junboCharge=junboChargeLogService.lambdaQuery()
                .eq(JunboChargeLog::getMiguOrderId,orderId).orderByDesc(JunboChargeLog::getCreateTime).last("limit 1").one();
        if(junboCharge==null){
            return new FebsResponse().fail().message("订单不存在");
        }
        if(junboCharge.getStatus()==-1){
            return new FebsResponse().fail().message("预约直充");
        }
        if(junboCharge.getStatus()==0){
            return new FebsResponse().fail().message("直充中");
        }
        if(junboCharge.getStatus()==1){
            return new FebsResponse().success().message("直充成功");
        }
        if(junboCharge.getStatus()==2){
            return new FebsResponse().fail().message("直充失败");
        }
        return new FebsResponse().fail().message("系统错误");
    }


    /**
     * 查询充值列表
     * @param phone
     * @param date
     * @return
     */
    @Override
    public ObjectNode woReadQueryCharge(String phone, String date) throws Exception {
        String mobile=AESUtils.decrypt(phone,"update!@#1234567");
        date=DateUtil.toFormatDate(date);
        log.info("沃阅读查询充值列表=>手机号:{},日期:{}", mobile,date);
        if (mobile == null || !mobile.matches(Regexp.MOBILE_REG)) {
            throw new AlipayApiException("请正确填写手机号");
        }
        List<WoReadJunboChargeLogDto> list = junboChargeLogService.woReadQueryChargeList(mobile,date);
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("code","0000");
        objectNode.putPOJO("message",list);
        return objectNode;
    }


    /**
     * 校验是否已支付沃阅读订单
     * @param mobile
     * @return
     */
    private WoReadOrder woReadIsMember(String mobile, List<String> businessTypeList) {

        WoReadOrder woReadOrder= woReadOrderService.lambdaQuery()
                .eq(WoReadOrder::getMobile,mobile)
                .eq(WoReadOrder::getOrderStatus,1)
                .eq(WoReadOrder::getSubStatus, 2)
//                .in(WoReadOrder::getBusinessType, businessTypeList)
                .eq(WoReadOrder::getBizType, BizConstant.BIZ_TYPE_MEMBER_ALIPAY)
                .orderByDesc(WoReadOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(woReadOrder==null){
            return null;
        }
        WoReadPkgOrderedStatusResult sign=woReadApiService.getPkgOrderedStatus(mobile,woReadOrder.getChannelId());
        if(sign==null || !sign.isOK() || sign.getData()==null){
            return null;
        }
        if(StringUtils.isEmpty(sign.getData().getStatus()) || sign.getData().getStatus().equals("3")){
            return null;
        }
        if(StringUtils.isNotBlank(sign.getData().getExpiretime()) && LocalDateTime.now().isAfter(DateUtil.parseString(sign.getData().getExpiretime(),DateUtil.FULL_TIME_PATTERN))){
            return null;
        }
        //当前时间是否大于生效时间
        if(StringUtils.isNotBlank(sign.getData().getEffectivetime()) && LocalDateTime.now().isAfter(DateUtil.parseString(sign.getData().getEffectivetime(),DateUtil.FULL_TIME_PATTERN))){
            return woReadOrder;
        }
        return null;
    }
    private AliSignChargingOrder aliSignIsMember(String mobile, List<String> businessTypeList) {
        AliSignChargingOrder aliOrder= chargingOrderService.lambdaQuery()
                .eq(AliSignChargingOrder::getMobile, mobile)
                .eq(AliSignChargingOrder::getOrderStatus,1)
                .in(AliSignChargingOrder::getBusinessType, businessTypeList)
                .eq(AliSignChargingOrder::getRefundStatus, 0)
                .between(AliSignChargingOrder::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .eq(AliSignChargingOrder::getBizType, BizConstant.BIZ_TYPE_MEMBER_ALIPAY)
                .last("ORDER BY order_amount*1 DESC , create_time DESC LIMIT 1").one();
        if(aliOrder==null){
            return null;
        }
        return aliOrder;
    }
    private AliSignChargingOrder aliSignRechargeIsMember(String mobile, String serviceId) {
        AliSignChargingOrder aliOrder = chargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getMobile, mobile)
                .eq(AliSignChargingOrder::getOrderStatus,1)
                .eq(AliSignChargingOrder::getBusinessType, serviceId)
                .eq(AliSignChargingOrder::getRefundStatus, 0)
                .between(AliSignChargingOrder::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .eq(AliSignChargingOrder::getBizType, BizConstant.BIZ_TYPE_MEMBER_ALIPAY)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(aliOrder==null){
            return null;
        }
        return aliOrder;
    }

    /**
     * 校验是否已支付沃阅读订单
     * @param mobile
     * @return
     */
    private WoReadOrder woReadRechargeIsMember(String mobile, String serviceId) {

        WoReadOrder woReadOrder= woReadOrderService.lambdaQuery()
                .eq(WoReadOrder::getMobile,mobile)
                .eq(WoReadOrder::getOrderStatus,1)
                .eq(WoReadOrder::getSubStatus, 2)
                .eq(WoReadOrder::getBusinessType, serviceId)
                .eq(WoReadOrder::getBizType, BizConstant.BIZ_TYPE_MEMBER_ALIPAY)
                .orderByDesc(WoReadOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(woReadOrder==null){
            return null;
        }
        WoReadPkgOrderedStatusResult sign=woReadApiService.getPkgOrderedStatus(mobile,woReadOrder.getChannelId());
        if(sign==null || !sign.isOK() || sign.getData()==null){
            return null;
        }
        if(StringUtils.isEmpty(sign.getData().getStatus()) || sign.getData().getStatus().equals("3")){
            return null;
        }
        if(StringUtils.isNotBlank(sign.getData().getExpiretime()) && LocalDateTime.now().isAfter(DateUtil.parseString(sign.getData().getExpiretime(),DateUtil.FULL_TIME_PATTERN))){
            return null;
        }
        //当前时间是否大于生效时间
        if(StringUtils.isNotBlank(sign.getData().getEffectivetime()) && LocalDateTime.now().isAfter(DateUtil.parseString(sign.getData().getEffectivetime(),DateUtil.FULL_TIME_PATTERN))){
            woReadOrder.setPayTime(DateUtil.localDateTimeToDate(DateUtil.parseString(sign.getData().getEffectivetime(),DateUtil.FULL_TIME_PATTERN).minusDays(30)));
            return woReadOrder;
        }
        return null;
    }


    /**
     * 视频彩铃包月权益充值
     * @param mobile
     * @param orderId
     * @param businessType
     */
    private FebsResponse aliPayRechargeVrbtRights(String mobile,String orderId,String businessType,String bizType,String externalAgreementNo) {
        MiguPack miguPack=miguPackService.lambdaQuery().eq(MiguPack::getServiceId, businessType).orderByDesc(MiguPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(miguPack==null){
            log.info(BizConstant.ALIPAY_RIGHTS_SERVICE_NOT_CONFIG + "=>渠道号:{},订单号:{},手机号:{}", businessType,orderId,mobile);
            return new FebsResponse().fail().message(BizConstant.ALIPAY_RIGHTS_SERVICE_NOT_CONFIG);
        }
        Optional<RightsPackDto> serviceOptional=  rightsPackService.wholeMemberRightsList(businessType).stream().max(Comparator.comparing(RightsPackDto::getId));
        if(serviceOptional==null || !serviceOptional.isPresent()){
            log.info(BizConstant.ALIPAY_RIGHTS_SERVICE_NOT_RELEVANCE + "=>渠道号:{},订单号:{},手机号:{}", businessType,orderId,mobile);
            return new FebsResponse().fail().message(BizConstant.ALIPAY_RIGHTS_SERVICE_NOT_RELEVANCE);
        }
        RightsPackDto service=serviceOptional.get();
        Optional<RightsPackList> productOptional=service.getRightsPackList().stream().max(Comparator.comparing(RightsPackList::getRightsId));
        if(productOptional==null || !productOptional.isPresent()){
            log.info(BizConstant.ALIPAY_RIGHTS_PRODUCT_NOT_CONFIG+"=>渠道号:{},订单号:{},手机号:{}", businessType,orderId,mobile);
            return new FebsResponse().fail().message(BizConstant.ALIPAY_RIGHTS_PRODUCT_NOT_CONFIG);
        }
        LocalDateTime effectTime=LocalDateTime.now().plusMinutes(miguPack.getRechargeDelayMinute());
        RightsPackList product=productOptional.get();

        JunboChargeLog junboOrder=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getJunboOrderId,orderId).in(JunboChargeLog::getStatus,BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS).orderByDesc(JunboChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(junboOrder!=null){
            log.info(BizConstant.ALIPAY_RIGHTS_ORDER_RECHARGE_PASS+"=>渠道号:{},订单号:{},手机号:{}", businessType,orderId,mobile);
            return new FebsResponse().fail().message(BizConstant.ALIPAY_RIGHTS_ORDER_RECHARGE_PASS);
        }
        JunboChargeLog junboChargeLog=junboChargeLogService.aliPayVrbtChargeLog(mobile,mobile,orderId,orderId,businessType,product.getCouponId(),product.getRightsName(),product.getProductPrice(),null,BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING,product.getRechargeState(),DateUtil.localDateTimeToDate(effectTime),"支付宝视频彩铃",service.getPackName(),null,product.getCompanyOwner());
        if(junboChargeLog!=null){
            log.info("支付宝视频彩铃正在充值中=>渠道号:{},订单号:{},手机号:{},业务类型:{}", businessType,orderId,mobile,bizType);
            if(bizType.equals(BizConstant.BIZ_TYPE_VRBT_YD_ALIPAY)){
                RemoteResult res = miguApiService.vrbtZeroOrder(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID,1);
                Integer chargeStatus = res.isOK() ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
                junboChargeLogService.lambdaUpdate().eq(JunboChargeLog::getId, junboChargeLog.getId()).set(JunboChargeLog::getStatus,chargeStatus).set(JunboChargeLog::getRespMsg,res.getResMsg()).update();
                if(res.isOK()){
                    return new FebsResponse().success().message(BizConstant.ALIPAY_RIGHTS_ORDER_RECHARGE_SUCCESS);
                }
                return new FebsResponse().fail().message(BizConstant.ALIPAY_RIGHTS_ORDER_RECHARGE_FAIL);
            }else if(bizType.equals(BizConstant.BIZ_TYPE_VRBT_DX_ALIPAY)){
                Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, externalAgreementNo).last("limit 1").one();
                if(subscribe!=null && !StringUtils.isEmpty(subscribe.getExtra())){
                    DianxinResp res=dianxinVrbtService.ismpConfirmOrderThird(mobile, subscribe.getExtra(),orderId, BizConstant.BIZ_DIANXIN_CHANNEL_MAIHE_SFZF);
                    Integer chargeStatus = res.isOK() ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
                    junboChargeLogService.lambdaUpdate().eq(JunboChargeLog::getId, junboChargeLog.getId()).set(JunboChargeLog::getStatus,chargeStatus).set(JunboChargeLog::getRespMsg,res.getResMessage()).update();
                    //电信支付宝视频彩铃充值失败，支付宝订单解约并退款
                    if(!res.isOK()){
                        //解约
                        alipayService.unSign(externalAgreementNo);
                        //退款
                        chargingOrderService.dianXinAliPayVrbtTransferFee(mobile);
                        return new FebsResponse().fail().message(res.getResMessage());
                    }
                    return new FebsResponse().success().message(res.getResMessage());
                }
            }else if(bizType.equals(BizConstant.BIZ_TYPE_VRBT_LT_ALIPAY)){
                Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, externalAgreementNo).last("limit 1").one();
                if(subscribe!=null){
                    LiantongResp exchangeRing=liantongVrbtService.exchangeRing(mobile,BizConstant.BIZ_LT_CHANNEL_HONGSHENG_SFZF);
                    Integer chargeStatus = exchangeRing.isOK() ? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
                    junboChargeLogService.lambdaUpdate().eq(JunboChargeLog::getId, junboChargeLog.getId()).set(JunboChargeLog::getStatus,chargeStatus).set(JunboChargeLog::getRespMsg,exchangeRing.getDescription()).update();
                    //联通支付宝视频彩铃充值失败，支付宝订单解约并退款
                    if(!exchangeRing.isOK()){
                        //解约
                        alipayService.unSign(externalAgreementNo);
                        //退款
                        chargingOrderService.lianTongAliPayVrbtTransferFee(mobile);
                        return new FebsResponse().fail().message(exchangeRing.getDescription());
                    }
                    return new FebsResponse().success().message(exchangeRing.getDescription());
                }
            }
        }
        return new FebsResponse().fail().message(BizConstant.ALIPAY_RIGHTS_ORDER_RECHARGE_SYSTEM_ERROR);
    }

    /**
     *
     * @param orderId
     */
    @Override
    public void aliPayRechargeVrbt(String orderId){
        AliSignChargingOrder aliSignChargingOrder = aliSignChargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getOrderNo,orderId).one();
        if(aliSignChargingOrder==null){
            log.error("支付宝视频彩铃签约订单查询失败=>订单号:{}",orderId);
            return;
        }
        FebsResponse response=this.aliPayRechargeVrbtRights(aliSignChargingOrder.getMobile(),orderId,aliSignChargingOrder.getBusinessType(),aliSignChargingOrder.getBizType(),aliSignChargingOrder.getExternalAgreementNo());
        if(response!=null){
            Integer rightsStatus=response.isOK()? BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS : BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            aliSignChargingOrderService.lambdaUpdate().eq(AliSignChargingOrder::getId, aliSignChargingOrder.getId()).set(AliSignChargingOrder::getRightsStatus,rightsStatus).set(AliSignChargingOrder::getRemark,response.get("message").toString()).update();
            //如果用户没有diy(移动支付宝视频彩铃业务)
            if(aliSignChargingOrder.getBizType().equals(BizConstant.BIZ_TYPE_VRBT_YD_ALIPAY)){
                if (vrbtDiyRingService.queryRing(aliSignChargingOrder.getMobile()).size() == 0) {
                    orderVrbtService.orderDiyByRandom(aliSignChargingOrder.getMobile());
                }
            }

        }
    }



    public Boolean isRedList(String mobile) {
       List<String> redMobileList=rightsRightsProperties.getRedMobileList();
       if(redMobileList.contains(mobile)){
           return true;
       }
        return false;
    }

    /**
     * 查询三方支付订单当月是否订购
     * @param mobile
     * @param bizType
     * @return
     */
    @Override
    public FebsResponse queryPayOrder(String mobile, List<String> bizType){
        AliSignChargingOrder aliOrder = chargingOrderService.lambdaQuery().eq(AliSignChargingOrder::getMobile, mobile)
                .eq(AliSignChargingOrder::getOrderStatus,1)
                .eq(AliSignChargingOrder::getRefundStatus, 0)
                .between(AliSignChargingOrder::getPayTime, LocalDateTime.now().minusMonths(1),LocalDateTime.now())
                .in(AliSignChargingOrder::getBizType, bizType)
                .orderByDesc(AliSignChargingOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(aliOrder!=null){
            log.info("支付宝是否订购"+aliOrder.getBusinessType()+"=>手机号:{},订单明细:{}", mobile,aliOrder);
            return new FebsResponse().success();
        }

        WoReadOrder woReadOrder= woReadOrderService.lambdaQuery()
                .eq(WoReadOrder::getMobile,mobile)
                .eq(WoReadOrder::getOrderStatus,1)
                .eq(WoReadOrder::getSubStatus, 2)
                .in(WoReadOrder::getBizType, bizType)
                .orderByDesc(WoReadOrder::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(woReadOrder==null){
            return new FebsResponse().fail();
        }




        WoReadPkgOrderedStatusResult sign=woReadApiService.getPkgOrderedStatus(mobile,woReadOrder.getChannelId());
        if(sign==null || !sign.isOK() || sign.getData()==null){
            return new FebsResponse().fail();
        }
        if(StringUtils.isEmpty(sign.getData().getStatus()) || sign.getData().getStatus().equals("3")){
            return new FebsResponse().fail();
        }
        if(StringUtils.isNotBlank(sign.getData().getExpiretime()) && LocalDateTime.now().isAfter(DateUtil.parseString(sign.getData().getExpiretime(),DateUtil.FULL_TIME_PATTERN))){
            return new FebsResponse().fail();
        }
        //当前时间是否大于生效时间
        if(StringUtils.isNotBlank(sign.getData().getEffectivetime()) && LocalDateTime.now().isAfter(DateUtil.parseString(sign.getData().getEffectivetime(),DateUtil.FULL_TIME_PATTERN))){
            log.info("沃阅读是否订购"+woReadOrder.getBusinessType()+"=>手机号:{},订单明细:{}", mobile,woReadOrder);
            return new FebsResponse().success();
        }
        return new FebsResponse().fail();
    }

    @Override
    public boolean isVrbtDiyMember(String mobile) {
        FebsResponse febsResponse = this.queryPayOrder(mobile, ALI_PAY_BIZ_TYPE_LIST);
        if (febsResponse.isOK()) {
            return true;
        }
        return false;
    }
}

