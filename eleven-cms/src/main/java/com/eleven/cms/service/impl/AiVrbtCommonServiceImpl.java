package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.*;
import static com.eleven.cms.util.BizConstant.BIZ_CHANNEL_TY_COMM;

/**
 * AI视频彩铃
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 15:30
 **/
@Service("aiVrbtCommonService")
@Slf4j
public class AiVrbtCommonServiceImpl implements IBizCommonService {
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IAiVrbtService aiVrbtService;
    @Autowired
    RedisUtil redisUtil;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        String mobile = subscribe.getMobile();
        Result<?> submitResult = aiVrbtService.sendSms(mobile,subscribe.getChannel());
        String result = "AI视频彩铃发送验证码结果==>{\"resCode\":\"" + submitResult.getCode() + "\",\"resMsg\":\"" + submitResult.getMessage() + "\"}";
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setResult(result);
        upd.setModifyTime(new Date());

        if (submitResult != null && submitResult.isOK()) {
            upd.setIspOrderNo(String.valueOf(submitResult.getResult()));
            subscribeService.updateSubscribeDbAndEs(upd);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
//            return Result.error("获取验证码失败");



            try {
                String errorMsg="{\"code\":\""+submitResult.getCode()+"\",\"message\":\""+submitResult.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        String mobile = subscribe.getMobile();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        Result<?> submitResult = aiVrbtService.submitSms(mobile, subscribe.getIspOrderNo(), smsCode,subscribe.getChannel());
        String result = "AI视频彩铃提交验证码结果==>{\"resCode\":\"" + submitResult.getCode() + "\",\"resMsg\":\"" + submitResult.getMessage() + "\"}";
        upd.setResult(result);
        upd.setModifyTime(new Date());
        if (submitResult != null && submitResult.isOK()) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("订阅成功");
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
