package com.eleven.cms.service.impl;

import com.eleven.cms.config.MiniApiWXPayProperties;
import com.eleven.cms.dto.DypayNotifyMsg;
import com.eleven.cms.entity.MiniApiWxpayLog;
import com.eleven.cms.mapper.MiniApiWxpayLogMapper;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.IMiniApiWxpayLogService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: cms_miniApi_wxpay_log
 * @Author: jeecg-boot
 * @Date:   2022-05-25
 * @Version: V1.0
 */
@Service
@Slf4j
public class MiniApiWxpayLogServiceImpl extends ServiceImpl<MiniApiWxpayLogMapper, MiniApiWxpayLog> implements IMiniApiWxpayLogService {

    @Autowired
    private MiniApiWXPayProperties miniApiWXPayProperties;
    @Autowired
    private IMemberService memberService;

    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public Pair<Boolean, DypayNotifyMsg> parsePaymentCallback(String raw) throws JsonProcessingException {

        final JsonNode tree = mapper.readTree(raw);
        String msgSignature = tree.at("/msg_signature").asText();
        String msg = tree.at("/msg").textValue();
        List<String> sortedString = new ArrayList<>();
        sortedString.add(miniApiWXPayProperties.getPayNotifyToken());
        tree.fields().forEachRemaining(entry -> {
            String feildName = entry.getKey();
            if (!"msg_signature".equals(feildName) && !"type".equals(feildName)) {
                String feildValue = entry.getValue().textValue();
                sortedString.add(feildValue);
            }
        });
        final String concat = sortedString.stream().sorted().collect(Collectors.joining(""));
        final String sign = DigestUtils.sha1Hex(concat.getBytes(StandardCharsets.UTF_8));
        final DypayNotifyMsg douyinPayNotify = mapper.readValue(msg, DypayNotifyMsg.class);

        return Pair.of(StringUtils.equals(msgSignature, sign), douyinPayNotify);
    }

    @Override
    public void douyinRightsRecharge(DypayNotifyMsg notifyMsg) {

        //修改支付状态
        MiniApiWxpayLog miniApiWxpayLog = this.lambdaQuery()
                .eq(MiniApiWxpayLog::getOutTradeNo, notifyMsg.getCpOrderno())
                .one();
        if(miniApiWxpayLog == null){
            log.info("抖音小程序担保支付,未找到订单号,cp_orderno:" + notifyMsg.getCpOrderno());
        }else{
            String channelNo = notifyMsg.getChannelNo();//支付渠道侧单号
            String paymentOrderNo = notifyMsg.getPaymentOrderNo();//支付渠道侧商家订单号
            Integer totalAmount = notifyMsg.getTotalAmount();//支付金额，单位为分
            String status = notifyMsg.getStatus();//支付状态，固定SUCCESS
            String sellerUid = notifyMsg.getSellerUid();//该笔交易卖家商户号
            Integer paidAt = notifyMsg.getPaidAt();//支付时间，Unix 时间戳，10 位，整型数

            //抖音权益充值
            memberService.douyinRightsRecharge(miniApiWxpayLog.getMobile(),
                    miniApiWxpayLog.getAccount() == null ? miniApiWxpayLog.getMobile() : miniApiWxpayLog.getAccount(),
                    miniApiWxpayLog.getCouponId(),
                    miniApiWxpayLog.getSubject());

            miniApiWxpayLog.setChannelNo(channelNo);
            miniApiWxpayLog.setPaymentOrderNo(paymentOrderNo);
            miniApiWxpayLog.setTotalAmount(totalAmount + "");
            miniApiWxpayLog.setPayStatus("SUCCESS".equals(status) ? 1 : 0);
            miniApiWxpayLog.setSellerUid(sellerUid);
            miniApiWxpayLog.setPaidAt(paidAt);
            this.saveOrUpdate(miniApiWxpayLog);
        }

    }
}
