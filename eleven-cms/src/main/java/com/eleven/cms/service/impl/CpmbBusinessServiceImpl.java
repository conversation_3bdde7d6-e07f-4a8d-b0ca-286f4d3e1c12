package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.MiguChannelConfig;
import com.eleven.cms.config.SichuanMobileFlowPacketProperties;
import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.*;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class CpmbBusinessServiceImpl implements IBusinessCommonService {


    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    InnerUnionMemberService innerUnionMemberService;
    @Autowired
    SichuanMobileFlowPacketProperties sichuanMobileFlowPacketProperties;
    @Autowired
    BizProperties bizProperties;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;
    @Autowired
    ICmsCrackConfigService cmsCrackConfigService;


    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        if (adSiteBusinessConfigService.isBlack(BizConstant.BIZ_TYPE_MIGU_MUSIC, subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        //运营商
        String isp = MobileRegionResult.ISP_YIDONG;
        String mobile = subscribe.getMobile();
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                isp = mobileRegionResult.getOperator();
                subscribe.setIsp(isp);
                if (mobileRegionResult.isIspYidong()) {
//                    if(CHENGDU_CITY.equals(mobileRegionResult.getCity())){
//                        log.warn("移动城市限制,渠道号:{},手机号:{},城市:{}", subscribe.getChannel(), mobile, mobileRegionResult.getCity());
//                        return Result.error("暂未开放,敬请期待!");
//                    }
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
//                        if (provinceBusinessChannelConfigService.allow("HETU", mobileRegionResult.getProvince())) {
//                            subscribe.setChannel("HETU");
//                            subscribe.setBizType("HETU");
//                            return SpringContextUtils.getBean(HetuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
//                        }
//                        if (provinceBusinessChannelConfigService.allow("HETU_GZ", mobileRegionResult.getProvince())) {
//                            subscribe.setChannel("HETU_GZ");
//                            subscribe.setBizType("HETU");
//                            return SpringContextUtils.getBean(HetuBusinessServiceImpl.class).receiveOrderWithCache(subscribe);
//                        }
                        if (SICHUAN_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel(BIZ_CHANNEL_SCYD_BJHY);
                            subscribe.setBizType(BIZ_TYPE_SCYD);
                            String bizCode = sichuanMobileFlowPacketProperties.getBizCodeByChannel(subscribe.getChannel());
                            subscribe.setBizCode(bizCode);
                            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveSichuanMobileCrackOrder(subscribe);
                        }
                        if (GUIZHOU_PROVINCE.equals(subscribe.getProvince())) {
                            subscribe.setChannel(BIZ_TYPE_GZYD_YDYP_HJHY);
                            subscribe.setBizType(BIZ_TYPE_GZYD_YDYP_HJHY);
                            return SpringContextUtils.getBean(SubscribeServiceImpl.class).receiveGuiZhouMobileOrder(subscribe);
                        }

//                        if (provinceBusinessChannelConfigService.allow("COMIC", mobileRegionResult.getProvince())) {
//                            //开通咪咕动漫
//                            subscribe.setChannel("COMIC");
//                            Result result = innerUnionMemberService.forword(subscribe, subscribe.getChannel());
//                            result.setServiceId("1300201301");
//                            return result;
//                        }
//                        if (MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_1.equals(subscribe.getChannel()) && provinceBusinessChannelConfigService.allow(BIZ_TYPE_WANGYIYUN_MM, mobileRegionResult.getProvince())) {
//                            subscribe.setChannel(BIZ_TYPE_WANGYIYUN_MM);
//                            subscribe.setBizType(BIZ_TYPE_WANGYIYUN_MM);
//                            Result result = subscribeService.receiveWangyiyunMmOrder(subscribe);
//                            result.setServiceId(BIZ_TYPE_WANGYIYUN_MM);
//                            return result;
//                        }
                        return Result.error("暂未开放,敬请期待!");
                    }
//                    if(GUANGDONG_PROVINCE.equals(subscribe.getProvince())){
//                        subscribe.setChannel(MiguApiService.BIZ_CPMB_20_CHANNEL_CODE_W5);
//                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        Result result = SpringContextUtils.getBean(CpmbBusinessServiceImpl.class).receiveOrder(subscribe);
        return result;
    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(transactionId);
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
            Result result = Result.ok("订阅成功");
            CmsCrackConfig cmsCrackConfig = cmsCrackConfigService.getCrackConfigByChannel(subscribe.getChannel());
            result.setServiceId(cmsCrackConfig != null ? cmsCrackConfig.getServiceId() : "");
            return result;
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final Result<?> result = pushSubscribeService.handleCpmbExistsBillingCrack(subscribe);
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }
}
