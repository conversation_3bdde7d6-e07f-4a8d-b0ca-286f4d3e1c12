package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.ad.BeilehuApiProperties;
import com.eleven.cms.ad.JunboApiProperties;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.MiGuHuYuChannelConfig;
import com.eleven.cms.config.MiGuHuYuQueryMemberProperties;
import com.eleven.cms.config.OtherRecharge;
import com.eleven.cms.dto.Rights;
import com.eleven.cms.duanju.entity.DuanjuCoupon;
import com.eleven.cms.duanju.entity.DuanjuGoldenBeanOrder;
import com.eleven.cms.duanju.entity.DuanjuUser;
import com.eleven.cms.duanju.service.IDuanjuCouponService;
import com.eleven.cms.duanju.service.IDuanjuGoldenBeanOrderService;
import com.eleven.cms.duanju.service.IDuanjuUserService;
import com.eleven.cms.entity.*;
import com.eleven.cms.mapper.CouponCodeMapper;
import com.eleven.cms.queue.*;
import com.eleven.cms.remote.RechargeAlertService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.MD5Util;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RightsPackDto;
import com.eleven.cms.vo.RightsPackList;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import javax.transaction.Transactional;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: cms_coupon_code
 * @Author: jeecg-boot
 * @Date:   2022-03-22
 * @Version: V1.0
 */
@Slf4j
@Service
public class CouponCodeServiceImpl extends ServiceImpl<CouponCodeMapper, CouponCode> implements ICouponCodeService {
    //激活码直充的serviceId
    public static final String CODE_RECHARGE_SERVICE_ID = "CODE_RECHARGE";
    //激活码直充的业务标识
    public static final String CODE_RECHARGE_RIGHTS_PACK = "code_rights_pack_1";
    public static final String LOG_TAG = "激活码";

    public static final String MSG = "编辑成功";
    @Autowired
    private MiGuHuYuQueryMemberProperties miGuHuYuQueryMemberProperties;
    //思维方阵
    public static final String SWFZ_NAME = "swfz";
    //大鱼消除
    public static final String DYXC_NAME = "dyxc";
    //厨房大逃亡
    public static final String CFDTW_NAME = "cfdtw";
    //我自为道
    public static final String WZWD_NAME = "wzwd";
    //短剧
    public static final String DUANJU_NAME = "duanju";
    //水晶传说
    public static final String SJCS_NAME = "sjcs";
    //暗黑主宰
    public static final String AHZZ_NAME = "ahzz";

    //public static final String HETU_RIGHTS_ID="hetu_2";
    public static final String REDIS_KEY_COUPON_HUYU_PREFIX = "coupon:huyu:";
    public static final int REDIS_COUPON_CACHE_SIZE = 1000;



    @Autowired
    private UnifyRightsFeignClient unifyRightsFeignClient;
    @Autowired
    BeilehuApiProperties beilehuApiProperties;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private RechargeAlertService rechargeAlertService;
    @Autowired
    private JunboApiProperties junboApiProperties;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    private IRightsSubService rightsSubService;


    @Autowired
    private IHetuCouponCodeService hetuCouponCodeService;

    @Autowired
    private ISwfzCouponCodeService swfzCouponCodeService;
    @Autowired
    private IDyxcCouponCodeService dyxcCouponCodeService;
    @Autowired
    private ICfdtwCouponCodeService cfdtwCouponCodeService;
    @Autowired
    private IWzwdCouponCodeService wzwdCouponCodeService;
    @Autowired
    private ISjcsCouponCodeService sjscCouponCodeService;
    @Autowired
    private IAhzzCouponCodeService ahzzCouponCodeService;

    @Autowired
    private IGameGetCouponCodeService getCodeService;
    @Autowired
    private IGameSendCouponCodeService sendCodeService;
    @Autowired
    private RedisUtil redisUtil;

    private OkHttpClient client;
    private OkHttpClient codeClient;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private Environment environment;
    private MediaType mediaType;

    @Autowired
    private IDuanjuUserService duanjuUserService;
    @Autowired
    private IDuanjuCouponService duanjuCouponService;
    @Autowired
    private IDuanjuGoldenBeanOrderService duanjuGoldenBeanOrderService;
    @Autowired
    private IMemberRightsService memberRightsService;
    @Autowired
    private IMiguPackService miguPackService;
    @Autowired
    private IRightsPackService rightsPackService;
    @Autowired
    private IGameConfigService gameConfigService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        this.codeClient = OkHttpClientUtils.getNewInstanceCookieEnable().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
        this.mediaType = MediaType.parse(org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }




    /**
     * 查询有效期内未使用的券码
     * @param rightsId
     * @return
     */
    @Override
    public CouponCode getEffectiveDateCouponCode(String rightsId) {
        //查询有效期内未使用的券码
        return this.lambdaQuery()
                .gt(CouponCode::getInvalidTime,new Date())
                .eq(CouponCode::getRightsId,rightsId)
                .eq(CouponCode::getStatus, BizConstant.NOT_USE)
                .last("limit 1")
                .one();
    }
    /**
     * 查询有效期内未使用的券码
     * @param rightsId
     * @return
     */
    private CouponCode getHeTuEffectiveDateCouponCode(String rightsId) {
        //随机获取未使用或者充值中的券码
        return this.lambdaQuery()
                .select(CouponCode::getId,CouponCode::getCouponCode)
                .gt(CouponCode::getInvalidTime,new Date())
                .eq(CouponCode::getRightsId,rightsId)
                .eq(CouponCode::getStatus,BizConstant.NOT_USE)
                .last("ORDER BY RAND() LIMIT 1")
                .one();
    }
    /**
     * 更新券码状态
     * @param id
     * @return
     */
    @Override
    public void updateCouponCodeStatus(String id,String orderId,Integer status) {
        this.lambdaUpdate().eq(CouponCode::getId,id).set(CouponCode::getSendTime,new Date()).set(CouponCode::getUpdateTime,new Date()).set(CouponCode::getOrderId,orderId).set(CouponCode::getStatus,status).update();
    }
    /**
     * 更新券码状态
     * @param id
     * @return
     */
    @Override
    public void updateCouponCodeStatus(String id,String orderId,Integer status,String extrInfo) {
        this.lambdaUpdate().eq(CouponCode::getId,id).set(CouponCode::getSendTime,new Date()).set(CouponCode::getUpdateTime,new Date()).set(CouponCode::getExtrInfo,extrInfo).set(CouponCode::getOrderId,orderId).set(CouponCode::getStatus,status).update();
    }
    public void updateHeTuCouponCodeStatus(String id,String orderId,Integer status,String extrInfo) {
        this.lambdaUpdate().eq(CouponCode::getId,id).set(CouponCode::getSendTime,new Date()).set(CouponCode::getUpdateTime,new Date()).set(CouponCode::getExtrInfo,extrInfo).set(CouponCode::getOrderId,orderId).set(CouponCode::getStatus,status).update();
    }
    /**
     * 更新券码状态
     * @param code
     * @param mobile
     * @return
     */
    @Override
    public FebsResponse updateCouponCodeStatusByMobile(String code, String mobile,String orderId,String rightsId,String account) {
        //限制激活码使用
        this.lambdaUpdate().eq(CouponCode::getCouponCode,code).set(CouponCode::getSendTime,new Date()).set(CouponCode::getUpdateTime,new Date()).set(CouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();

        Rights rights=new Rights();
        //获取业务权益配置
        String couponId=rightsSubService.setRightCofig(rights,rightsId,null);
        if(StringUtils.isNotBlank(couponId)){
            if(rightsSubService.isAccount(rightsId,account)){
                return new FebsResponse().fail().message("账号错误");
            }
            //获取预约充值时间
            LocalDateTime scheduledTime=rightsSubService.getCouponCodeScheduledTime(mobile,CODE_RECHARGE_SERVICE_ID,CODE_RECHARGE_RIGHTS_PACK);
            if(scheduledTime==null){
                return new FebsResponse().fail().message(BizConstant.RIGHTS_MSG_NOT_SERVICE_PACK);
            }
            JunboChargeLog couponCode=junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getCouponCode,code).orderByDesc(JunboChargeLog::getCreateTime).last("limit 1").one();
            if(couponCode!=null){
                return new FebsResponse().fail().message("激活码已使用");
            }
            rights.setRechargeSource("code");
            rights.setCouponCode(code);
            JunboChargeLog junboChargeLog=junboChargeLogService.createScheduleRechargeLog(mobile,account,CODE_RECHARGE_SERVICE_ID, rights, Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()),CODE_RECHARGE_RIGHTS_PACK);
            if(junboChargeLog!=null){
                //更新订单号
                this.lambdaUpdate().eq(CouponCode::getCouponCode,code).set(CouponCode::getUpdateTime,new Date()).set(CouponCode::getOrderId,junboChargeLog.getMiguOrderId()).update();
            }
            return new FebsResponse().success().message("充值中");
        }

        Boolean rechargeStatus=false;
        if(StringUtil.equals("tuniu",rightsId)){
            for(int i=0;i<BizConstant.PRODUCT_ID_LIST.size();i++){
                FebsResponse febs=unifyRightsFeignClient.tuniuSendRights(mobile,BizConstant.PRODUCT_ID_LIST.get(i));
                if(febs.isOK()){
                    rechargeStatus=true;
                }
            }
        }
        Set<String> productIdSet = beilehuApiProperties.getBeilehuProductMap().keySet();
        if(productIdSet.contains(rightsId)){
            String productId=beilehuApiProperties.getBeilehuProductMap().get(rightsId).getProductId();
            Map<String, Object> data= Maps.newHashMap();
            data.put("cp_id",beilehuApiProperties.getCpId());
            data.put("order_id",code);
            data.put("phone",mobile);
            data.put("pid",productId);
            data.put("num",1);
            data.put("pay_time",System.currentTimeMillis());
            String sign=MD5Util.getSigns(data,beilehuApiProperties.getCpSecret());
            data.put("sign",sign);
            try {
                final String content = this.implementHttpPostResult(beilehuApiProperties.getRechargeUrl(),data,"贝乐虎权益订单充值");
                JsonNode jsonNode = mapper.readTree(content);
                String errorCode=jsonNode.at("/errorCode").asText("");
                String errorMessage=jsonNode.at("/errorMessage").asText("");
                String result=jsonNode.at("/result").asText("");
                String remark="贝乐虎权益订单充值=>{\"errorCode\":\""+errorCode+"\",\"errorMessage\":\""+errorMessage+"\",\"result\":\""+result+"\"}";

                if(StringUtil.isNotBlank(orderId)){
                    junboChargeLogService.lambdaUpdate()
                            .eq(JunboChargeLog::getMiguOrderId, orderId)
                            .set(JunboChargeLog::getStatus,StringUtil.equals("0",errorCode)?BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING:BizConstant.JUNBO_RECHARGE_STATUS_FAIL)
                            .set(JunboChargeLog::getRespCode, errorCode)
                            .set(JunboChargeLog::getRespMsg, errorMessage)
                            .set(JunboChargeLog::getRemark, remark)
                            .set(JunboChargeLog::getUpdateTime,new Date())
                            .update();
                }
                if(StringUtil.equals("0",errorCode)){
                    return new FebsResponse().success().message("充值成功");
                }
                return new FebsResponse().fail().message("充值失败");
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return new FebsResponse().fail().message("充值失败");
        }
        if(rechargeStatus && StringUtil.equals("tuniu",rightsId)){
            if(StringUtil.isNotBlank(orderId)){
                junboChargeLogService.lambdaUpdate().eq(JunboChargeLog::getMiguOrderId, orderId).set(JunboChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS).set(JunboChargeLog::getRespCode, "0000").set(JunboChargeLog::getRespMsg, "成功").set(JunboChargeLog::getRemark, "成功").set(JunboChargeLog::getUpdateTime,new Date()).update();
            }
            //更新订单状态
            this.lambdaUpdate().eq(CouponCode::getCouponCode,code).set(CouponCode::getUpdateTime,new Date()).set(CouponCode::getOrderId,StringUtil.isNotBlank(orderId)?orderId:mobile).set(CouponCode::getStatus,BizConstant.IS_USE).update();
            return new FebsResponse().success().message("充值成功");
        }
        return new FebsResponse().fail().message("充值失败");
    }

    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url, Object fromValue,String msg) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg);
    }
    public String push(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }


    /**
     * 更新券码状态
     * @param code
     * @param orderId
     * @return
     */
    @Override
    public FebsResponse updateCouponCodeStatusByMobile(String code,String orderId) {
        Map<String, Object> data= Maps.newHashMap();
        data.put("cp_id",beilehuApiProperties.getCpId());
        data.put("order_id",code);
        String sign=MD5Util.getSigns(data,beilehuApiProperties.getCpSecret());
        data.put("sign",sign);
        try {
            final String content =this.implementHttpPostResult(beilehuApiProperties.getOrderQueryUrl(),data,"贝乐虎权益订单查询");
            JsonNode jsonNode = mapper.readTree(content);
            String errorCode=jsonNode.at("/errorCode").asText("");
            String errorMessage=jsonNode.at("/errorMessage").asText("");
            String cpId=jsonNode.at("/result/cp_id").asText("");
            String message=jsonNode.at("/result/message").asText("");
            String order_id=jsonNode.at("/result/order_id").asText("");
            String status=jsonNode.at("/result/status").asText("");
            if(StringUtil.equals("0",errorCode)){
                String remark="贝乐虎权益订单查询=>{\"errorCode\":\""+errorCode+"\",\"errorMessage\":\""+errorMessage+"\",\"cpId\":\""+cpId+"\",\"message\":\""+message+"\",\"orderId\":\""+order_id+"\",\"status\":\""+status+"\"}";
                if(StringUtil.isNotBlank(orderId)){
                    junboChargeLogService.lambdaUpdate()
                            .eq(JunboChargeLog::getMiguOrderId, orderId)
                            .set(JunboChargeLog::getStatus, StringUtil.equals("2",status)?BizConstant.JUNBO_RECHARGE_STATUS_FAIL:StringUtil.equals("0",status)?BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING:BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS)
                            .set(JunboChargeLog::getRespCode, status)
                            .set(JunboChargeLog::getRespMsg, message)
                            .set(JunboChargeLog::getRemark, remark)
                            .set(JunboChargeLog::getUpdateTime,new Date())
                            .update();
                }
                this.lambdaUpdate().eq(CouponCode::getCouponCode,code)
                        .set(CouponCode::getUpdateTime,new Date())
                        .set(CouponCode::getStatus,StringUtil.equals("2",status)?BizConstant.RECHARGE_FAIL:StringUtil.equals("0",status)?BizConstant.RECHARGE_WAIT:BizConstant.IS_USE)
                        .set(CouponCode::getRemark,remark)
                        .update();
                if(StringUtil.equals("2",status)){
                    return new FebsResponse().fail().message("充值失败");
                }
                return new FebsResponse().success().message(message);
            }else{
                if(StringUtil.isNotBlank(orderId)){
                    junboChargeLogService.lambdaUpdate().eq(JunboChargeLog::getMiguOrderId, orderId)
                            .set(JunboChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_FAIL)
                            .set(JunboChargeLog::getRespCode, errorCode)
                            .set(JunboChargeLog::getRespMsg, errorMessage)
                            .set(JunboChargeLog::getRemark, errorMessage)
                            .set(JunboChargeLog::getUpdateTime,new Date())
                            .update();
                }
                this.lambdaUpdate().eq(CouponCode::getCouponCode,code)
                        .set(CouponCode::getUpdateTime,new Date())
                        .set(CouponCode::getStatus,BizConstant.RECHARGE_FAIL)
                        .update();
                return new FebsResponse().fail().message("充值失败");
            }

        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail().message("充值失败");
    }

    @Override
    public void sendCode(/*String orderId, String userId, String phoneNum, String extrInfo, */JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.heTuSendCodeQueueMessage(SendCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).build());
//        this.sendCodeSync(orderId, phoneNum, extrInfo, junboChargeLog);
    }
    @Override
    public void sendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            this.hetuSendCodeSync(junboChargeLog.getMiguOrderId(),junboChargeLog.getMobile(),junboChargeLog.getJunboOrderId(),junboChargeLog);
        }
    }
    private void hetuSendCodeSync(String orderId, String phoneNum, String extrInfo, JunboChargeLog junboChargeLog) {
        String couponCode=this.miGuHuYouCouponCodeRecharge(junboChargeLog, StringUtils.isNotBlank(phoneNum)? phoneNum : orderId,extrInfo);
        ObjectNode dataNode =mapper.createObjectNode();
        String timestamp= String.valueOf(System.currentTimeMillis());
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("MIGUHUYU");
        String sign=getSign(orderId,timestamp,otherRecharge);
        dataNode.put("orderId", orderId);
        dataNode.put("timestamp",timestamp);
        dataNode.put("sendTime",timestamp);
        dataNode.put("extrInfo", extrInfo);
        if(StringUtils.isNotBlank(couponCode)){
            dataNode.put("sendResult","1");
            dataNode.put("sendMessage","发码成功");
            dataNode.put("cdKey",couponCode);
            log.info("咪咕互娱道具下发-game:{},mobile:{},couponCode:{},orderId:{},extrInfo:{}","hetu",phoneNum,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),"咪咕互娱道具下发回调",sign);
        }else{
            dataNode.put("sendResult","0");
            dataNode.put("sendMessage","发码失败，券码数量不足");
            dataNode.put("cdKey","");
            log.info("咪咕互娱道具下发-发码失败，券码数量不足-game:{},mobile:{},couponCode:{},orderId:{},extrInfo:{}","hetu",phoneNum,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),"咪咕互娱道具下发回调",sign);
        }
    }


    /**
     * 券码充值
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String miGuHuYouCouponCodeRecharge(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //查询有效期内未使用的券码
        //HetuCouponCode hetuCouponCode=null;
        //上条代码查询慢，故优化
        //if(HETU_RIGHTS_ID.equals(junboChargeLog.getCouponId())){
        //    hetuCouponCode =hetuCouponCodeService.lambdaQuery().select(HetuCouponCode::getId,HetuCouponCode::getCouponCode).eq(HetuCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(HetuCouponCode::getStatus,BizConstant.NOT_USE).orderByDesc(HetuCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        //}else{
        //    hetuCouponCode =hetuCouponCodeService.lambdaQuery().select(HetuCouponCode::getId,HetuCouponCode::getCouponCode).eq(HetuCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(HetuCouponCode::getStatus,BizConstant.NOT_USE).last(BizConstant.SQL_LIMIT_ONE).one();
        //}
        HetuCouponCode hetuCouponCode = this.fetchCouponFromCache(junboChargeLog.getCouponId());
        String code="";
        String id="";
        if(hetuCouponCode==null){
            CouponCode couponCode = this.getHeTuEffectiveDateCouponCode(junboChargeLog.getCouponId());
            if(couponCode!=null){
                code=couponCode.getCouponCode();
                id=couponCode.getId();
            }
        }else{
            code=hetuCouponCode.getCouponCode();
            id=hetuCouponCode.getId();
        }
        junboChargeLog.setUpdateTime(new Date());
        if (StringUtils.isNotBlank(code)) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            if(hetuCouponCode==null){
                this.updateHeTuCouponCodeStatus(id, junboChargeLog.getMiguOrderId(),BizConstant.RECHARGE_WAIT,extrInfo);
            }else{
                hetuCouponCodeService.lambdaUpdate().eq(HetuCouponCode::getId,id).set(HetuCouponCode::getMobile,mobile).set(HetuCouponCode::getSendTime,new Date()).set(HetuCouponCode::getUpdateTime,new Date()).set(HetuCouponCode::getExtrInfo,extrInfo).set(HetuCouponCode::getOrderId,junboChargeLog.getMiguOrderId()).set(HetuCouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();
            }
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(code);
            junboChargeLogService.updateById(junboChargeLog);
            return code;
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }

    /**
     * 签名
     * @param orderId
     * @param timestamp
     * @param otherRecharge
     * @return
     */
    private String getSign(String orderId,String timestamp,OtherRecharge otherRecharge) {
        String parameterStr = orderId+timestamp+otherRecharge.getKey();
        try {
            return DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("咪咕互娱道具下发回调,加密参数:{}",parameterStr,e);
        }
        return null;
    }
    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url,String raw,String msg,String sign) {
        return push(url, raw,msg,sign);
    }
    public String push(String url,String raw,String msg,String sign) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).addHeader("sign",sign).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }

    @Async
    @Override
    public void addAliPayVrbtQueue(String orderId) {
        rabbitMQMsgSender.aliPayVrbtQueueMessage(AliPayVrbtMessage.builder().orderId(orderId).build());
    }




    @Override
    public void swfzSendCode(JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.swfzSendCodeQueueMessage(SendSmsCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).build());
    }



    @Override
    public void dyxcSendCode(JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.dyxcSendCodeQueueMessage(SendSmsCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).build());
    }


    @Override
    public void cfdtwSendCode(JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.cfdtwSendCodeQueueMessage(SendSmsCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).build());
    }

    @Override
    public void wzwdSendCode(JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.wzwdSendCodeQueueMessage(SendSmsCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).build());
    }

    @Override
    public void duanjuSendCode(JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.duanjuSendCodeQueueMessage(SendSmsCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).build());
    }

    @Override
    public void sjcsSendCode(JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.sjcsSendCodeQueueMessage(SendSmsCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).build());
    }

    @Override
    public void ahzzSendCode(JunboChargeLog junboChargeLog) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.ahzzSendCodeQueueMessage(SendSmsCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).build());
    }
    @Override
    public void gameSendCode(JunboChargeLog junboChargeLog,String gameName) {
        junboChargeLogService.save(junboChargeLog);
        rabbitMQMsgSender.gameSendCodeQueueMessage(GameSendCodeDeductMessage.builder().id(junboChargeLog.getId()).mobile(junboChargeLog.getMobile()).orderId(junboChargeLog.getMiguOrderId()).gameName(gameName).logTag(junboChargeLog.getCouponName()).build());
    }
    @Override
    public void swfzSendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            String couponCode=this.getSwfzCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId());
            if(StringUtils.isNotBlank(couponCode)){
                smsModelService.sendSmsAsync(junboChargeLog.getMobile(),junboChargeLog.getCouponId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode);
            }
            miguHuyuCallback(junboChargeLog.getMobile(),couponCode,junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),SWFZ_NAME);
        }
    }

    @Override
    public void dyxcSendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            String couponCode=this.getDyxcCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId());
            if(StringUtils.isNotBlank(couponCode)){
                smsModelService.sendSmsAsync(junboChargeLog.getMobile(),junboChargeLog.getCouponId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode);
            }
            miguHuyuCallback(junboChargeLog.getMobile(),couponCode,junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),DYXC_NAME);
        }
    }


    @Override
    public void cfdtwSendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            String couponCode=this.getCfdtwCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId());
            if(StringUtils.isNotBlank(couponCode)){
                smsModelService.sendSmsAsync(junboChargeLog.getMobile(),junboChargeLog.getCouponId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode);
            }
            miguHuyuCallback(junboChargeLog.getMobile(),couponCode,junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),CFDTW_NAME);
        }
    }

    @Override
    public void wzwdSendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            String couponCode=this.getWzwdCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId());
            if(StringUtils.isNotBlank(couponCode)){
                smsModelService.sendSmsAsync(junboChargeLog.getMobile(),junboChargeLog.getCouponId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode);
            }
            miguHuyuCallback(junboChargeLog.getMobile(),couponCode,junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),WZWD_NAME);
        }
    }


    @Override
    public void duanjuSendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            //正式发送权益
            this.getDuanjuCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId());
            //发送权益提醒短信
             smsModelService.sendSmsAsync(junboChargeLog.getMobile(),BizConstant.BIZ_TYPE_DUANJU,BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_RIGHTS);
             //通知互娱
             miguHuyuDuanJuCallback(junboChargeLog.getMobile(),"",junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),DUANJU_NAME);
             //查询是否订购
             Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, junboChargeLog.getMobile())
                    .eq(Subscribe::getBizType, BizConstant.BIZ_TYPE_DUANJU)
                    .in(Subscribe::getStatus, BizConstant.SUBSCRIBE_STATUS_SUCCESS)
                    .orderByDesc(Subscribe::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
             if(subscribe!=null){
                 //查询业务包关联字段SoapServiceId
                 MiguPack miguPack=miguPackService.lambdaQuery().select(MiguPack::getServiceId).eq(MiguPack::getSoapServiceId, junboChargeLog.getServiceId()).last(BizConstant.SQL_LIMIT_ONE).one();
                 if(miguPack==null){
                     //业务未配置
                     log.error("鲲鹏短剧代金券业务未配置,参数:{}",junboChargeLog);
                     return;
                 }
                 Optional<RightsPackDto> serviceOptional=rightsPackService.wholeMemberRightsList(junboChargeLog.getServiceId()).stream().max(Comparator.comparing(RightsPackDto::getId));
                 if(serviceOptional==null || !serviceOptional.isPresent()){
                     //业务未关联
                     log.error("鲲鹏短剧代金券业务未关联,参数:{}",junboChargeLog);
                     return;
                 }
                 RightsPackDto service=serviceOptional.get();
                 Optional<RightsPackList> productOptional=service.getRightsPackList().stream().max(Comparator.comparing(RightsPackList::getRightsId));
                 if(productOptional==null || !productOptional.isPresent()){
                     //产品未配置
                     log.error("鲲鹏短剧代金券产品未配置,参数:{}",junboChargeLog);
                     return;
                 }
                 RightsPackList product=productOptional.get();
                 IBusinessRightsSubService businessRightsSubService = SpringContextUtils.getBean(KunPengCouponCodeRightsServiceImpl.class);
                 businessRightsSubService.createScheduledRecharge(junboChargeLog.getMobile(),subscribe.getDeviceInfo(),miguPack.getServiceId(),service.getPackName(),product.getRightsId(), subscribe.getChannel());
             }

        }
    }

    @Override
    public void sjcsSendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            String couponCode=this.getSjcsCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId());
            if(StringUtils.isNotBlank(couponCode)){
                smsModelService.sendSmsAsync(junboChargeLog.getMobile(),junboChargeLog.getCouponId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode);
            }
            miguHuyuCallback(junboChargeLog.getMobile(),couponCode,junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),SJCS_NAME);
        }
    }
    /**
     * 咪咕互娱通知接口
     * @param couponCode
     * @param orderId
     * @param extrInfo
     * @param gameName
     */
    private void miguHuyuCallback(String mobile,String couponCode,String orderId,String extrInfo,String gameName){
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(gameName);
        ObjectNode dataNode =mapper.createObjectNode();
        String timestamp= String.valueOf(System.currentTimeMillis());
        String sign=getSign(orderId,timestamp,otherRecharge);
        dataNode.put("orderId", orderId);
        dataNode.put("timestamp",timestamp);
        dataNode.put("sendTime",timestamp);
        dataNode.put("extrInfo", extrInfo);
        if(StringUtils.isNotBlank(couponCode)){
            dataNode.put("sendResult","1");
            dataNode.put("sendMessage","发码成功");
            dataNode.put("cdKey",couponCode);
            log.info("咪咕互娱道具下发-game:{},mobile:{},couponCode:{},orderId:{},extrInfo:{}",gameName,mobile,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),otherRecharge.getName()+"咪咕互娱道具下发回调",sign);
        }else{
            dataNode.put("sendResult","0");
            dataNode.put("sendMessage","发码失败，券码数量不足");
            dataNode.put("cdKey","");
            log.info("咪咕互娱道具下发-发码失败，券码数量不足-game:{},mobile:{},couponCode:{},orderId:{},extrInfo:{}",gameName,mobile,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),"咪咕互娱道具下发回调",sign);
        }
    }

    /**
     * 咪咕互娱通知接口
     * @param couponCode
     * @param orderId
     * @param extrInfo
     * @param gameName
     */
    private void miguHuyuDuanJuCallback(String mobile,String couponCode,String orderId,String extrInfo,String gameName){
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get(gameName);
        ObjectNode dataNode =mapper.createObjectNode();
        String timestamp= String.valueOf(System.currentTimeMillis());
        String sign=getSign(orderId,timestamp,otherRecharge);
        dataNode.put("orderId", orderId);
        dataNode.put("timestamp",timestamp);
        dataNode.put("sendTime",timestamp);
        dataNode.put("extrInfo", extrInfo);
        dataNode.put("sendResult","1");
        dataNode.put("sendMessage","充值成功");
//        dataNode.put("cdKey",couponCode);
        log.info("咪咕互娱道具下发-game:{},mobile:{},orderId:{},extrInfo:{}",gameName,mobile,couponCode,orderId,extrInfo);
        this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),otherRecharge.getName()+"咪咕互娱道具下发回调",sign);
    }
    /**
     * 获取思维方阵券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String getSwfzCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //查询有效期内未使用的券码
//        SwfzCouponCode couponCode =swfzCouponCodeService.lambdaQuery().select(SwfzCouponCode::getId,SwfzCouponCode::getCouponCode).gt(SwfzCouponCode::getInvalidTime,new Date()).eq(SwfzCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(SwfzCouponCode::getStatus,BizConstant.NOT_USE).orderByDesc(SwfzCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();

//        SwfzCouponCode couponCode =swfzCouponCodeService.lambdaQuery().select(SwfzCouponCode::getId,SwfzCouponCode::getCouponCode).eq(SwfzCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(SwfzCouponCode::getStatus,BizConstant.NOT_USE).last(BizConstant.SQL_LIMIT_ONE).one();

        SwfzCouponCode couponCode=(SwfzCouponCode)this.fetchCouponFromCache(junboChargeLog.getCouponId(),SWFZ_NAME);
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //激活码绑定订单号
            swfzCouponCodeService.lambdaUpdate().eq(SwfzCouponCode::getId,couponCode.getId()).set(SwfzCouponCode::getMobile,mobile).set(SwfzCouponCode::getSendTime,new Date()).set(SwfzCouponCode::getUpdateTime,new Date()).set(SwfzCouponCode::getExtrInfo,extrInfo).set(SwfzCouponCode::getOrderId,junboChargeLog.getMiguOrderId()).set(SwfzCouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();

            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(couponCode.getCouponCode());
            junboChargeLogService.updateById(junboChargeLog);
            return couponCode.getCouponCode();
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }


    /**
     * 获取大鱼消除券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String getDyxcCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //查询有效期内未使用的券码
//        DyxcCouponCode couponCode =dyxcCouponCodeService.lambdaQuery().select(DyxcCouponCode::getId,DyxcCouponCode::getCouponCode).gt(DyxcCouponCode::getInvalidTime,new Date()).eq(DyxcCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(DyxcCouponCode::getStatus,BizConstant.NOT_USE).orderByDesc(DyxcCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();

//        DyxcCouponCode couponCode =dyxcCouponCodeService.lambdaQuery().select(DyxcCouponCode::getId,DyxcCouponCode::getCouponCode).eq(DyxcCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(DyxcCouponCode::getStatus,BizConstant.NOT_USE).last(BizConstant.SQL_LIMIT_ONE).one();
        DyxcCouponCode couponCode=(DyxcCouponCode)this.fetchCouponFromCache(junboChargeLog.getCouponId(),DYXC_NAME);
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //激活码绑定订单号
            dyxcCouponCodeService.lambdaUpdate().eq(DyxcCouponCode::getId,couponCode.getId()).set(DyxcCouponCode::getMobile,mobile).set(DyxcCouponCode::getSendTime,new Date()).set(DyxcCouponCode::getUpdateTime,new Date()).set(DyxcCouponCode::getExtrInfo,extrInfo).set(DyxcCouponCode::getOrderId,junboChargeLog.getMiguOrderId()).set(DyxcCouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();

            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(couponCode.getCouponCode());
            junboChargeLogService.updateById(junboChargeLog);
            return couponCode.getCouponCode();
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }


    /**
     * 获取厨房大逃亡券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String getCfdtwCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //查询有效期内未使用的券码
//        CfdtwCouponCode couponCode =cfdtwCouponCodeService.lambdaQuery().select(CfdtwCouponCode::getId,CfdtwCouponCode::getCouponCode).gt(CfdtwCouponCode::getInvalidTime,new Date()).eq(CfdtwCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(CfdtwCouponCode::getStatus,BizConstant.NOT_USE).orderByDesc(CfdtwCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();

        //CfdtwCouponCode couponCode =cfdtwCouponCodeService.lambdaQuery().select(CfdtwCouponCode::getId,CfdtwCouponCode::getCouponCode).eq(CfdtwCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(CfdtwCouponCode::getStatus,BizConstant.NOT_USE).last(BizConstant.SQL_LIMIT_ONE).one();
        CfdtwCouponCode couponCode=(CfdtwCouponCode)this.fetchCouponFromCache(junboChargeLog.getCouponId(),CFDTW_NAME);
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //激活码绑定订单号
            cfdtwCouponCodeService.lambdaUpdate().eq(CfdtwCouponCode::getId,couponCode.getId()).set(CfdtwCouponCode::getMobile,mobile).set(CfdtwCouponCode::getSendTime,new Date()).set(CfdtwCouponCode::getUpdateTime,new Date()).set(CfdtwCouponCode::getExtrInfo,extrInfo).set(CfdtwCouponCode::getOrderId,junboChargeLog.getMiguOrderId()).set(CfdtwCouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();

            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(couponCode.getCouponCode());
            junboChargeLogService.updateById(junboChargeLog);
            return couponCode.getCouponCode();
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }

    /**
     * 获取我自为道券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String getWzwdCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //查询有效期内未使用的券码
//        WzwdCouponCode couponCode =wzwdCouponCodeService.lambdaQuery().select(WzwdCouponCode::getId,WzwdCouponCode::getCouponCode).gt(WzwdCouponCode::getInvalidTime,new Date()).eq(WzwdCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(WzwdCouponCode::getStatus,BizConstant.NOT_USE).orderByDesc(WzwdCouponCode::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();

//        WzwdCouponCode couponCode =wzwdCouponCodeService.lambdaQuery().select(WzwdCouponCode::getId,WzwdCouponCode::getCouponCode).eq(WzwdCouponCode::getRightsId,junboChargeLog.getCouponId()).eq(WzwdCouponCode::getStatus,BizConstant.NOT_USE).last(BizConstant.SQL_LIMIT_ONE).one();
        WzwdCouponCode couponCode=(WzwdCouponCode)this.fetchCouponFromCache(junboChargeLog.getCouponId(),WZWD_NAME);
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //激活码绑定订单号
            wzwdCouponCodeService.lambdaUpdate().eq(WzwdCouponCode::getId,couponCode.getId()).set(WzwdCouponCode::getMobile,mobile).set(WzwdCouponCode::getSendTime,new Date()).set(WzwdCouponCode::getUpdateTime,new Date()).set(WzwdCouponCode::getExtrInfo,extrInfo).set(WzwdCouponCode::getOrderId,junboChargeLog.getMiguOrderId()).set(WzwdCouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(couponCode.getCouponCode());
            junboChargeLogService.updateById(junboChargeLog);
            return couponCode.getCouponCode();
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }


    /**
     * 获取水晶传说券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String getSjcsCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //查询有效期内未使用的券码
        SjcsCouponCode couponCode=(SjcsCouponCode)this.fetchCouponFromCache(junboChargeLog.getCouponId(),SJCS_NAME);
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //激活码绑定订单号
            sjscCouponCodeService.lambdaUpdate().eq(SjcsCouponCode::getId,couponCode.getId()).set(SjcsCouponCode::getMobile,mobile).set(SjcsCouponCode::getSendTime,new Date()).set(SjcsCouponCode::getUpdateTime,new Date()).set(SjcsCouponCode::getExtrInfo,extrInfo).set(SjcsCouponCode::getOrderId,junboChargeLog.getMiguOrderId()).set(SjcsCouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(couponCode.getCouponCode());
            junboChargeLogService.updateById(junboChargeLog);
            return couponCode.getCouponCode();
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }


    /**
     * 获取短剧券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized void getDuanjuCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //判断订单是否已发送优惠券
        boolean orderCodeSuccess=duanjuCouponService.lambdaQuery().eq(DuanjuCoupon::getOrderId,junboChargeLog.getMiguOrderId()).count()<=0;
        junboChargeLog.setUpdateTime(new Date());
        if (orderCodeSuccess) {
            DuanjuUser duanjuUser=duanjuUserService.lambdaQuery().select(DuanjuUser::getTotalGoldenBean,DuanjuUser::getInvalidTime,DuanjuUser::getVersion,DuanjuUser::getMobile,DuanjuUser::getId).eq(DuanjuUser::getMobile,mobile).orderByDesc(DuanjuUser::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            MemberRights memberRights=memberRightsService.lambdaQuery().select(MemberRights::getProductPrice,MemberRights::getOriginalPrice,MemberRights::getRightsName).eq(MemberRights::getCouponId,junboChargeLog.getCouponId()).orderByDesc(MemberRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(memberRights==null){
                junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
                junboChargeLog.setRespCode("-1");
                junboChargeLog.setRespMsg("产品未配置");
                junboChargeLog.setRemark("产品未配置");
                junboChargeLogService.updateById(junboChargeLog);
                return;
            }
            if(duanjuUser==null){
                DuanjuUser user=new DuanjuUser();
                /**手机号*/
                user.setMobile(mobile);
                /**总金豆*/
                user.setTotalGoldenBean(memberRights.getProductPrice());
                /**等级:-1=未开通,1=优享,2=精品,3=尊享*/
                user.setLevel(memberRights.getOriginalPrice());
                /**过期时间*/
                user.setInvalidTime(DateUtil.localDateTimeToDate(LocalDateTime.now().plusDays(30)));
                duanjuUserService.save(user);
            }else{
                /**总金豆*/
                Integer totalGoldenBean=Integer.valueOf(duanjuUser.getTotalGoldenBean().intValue()+memberRights.getProductPrice().intValue());
                duanjuUser.setTotalGoldenBean(totalGoldenBean);
                /**等级:-1=未开通,1=优享,2=精品,3=尊享*/
                duanjuUser.setLevel(memberRights.getOriginalPrice());
                /**过期时间*/
                if(duanjuUser.getInvalidTime()==null){
                    duanjuUser.setInvalidTime(DateUtil.localDateTimeToDate(LocalDateTime.now().plusDays(30)));
                }else{
                    duanjuUser.setInvalidTime(DateUtil.localDateTimeToDate(DateUtil.dateToLocalDateTime(duanjuUser.getInvalidTime()).plusDays(30)));
                }
                duanjuUserService.editDuanjuUser(duanjuUser);
            }
            /**兑换码*/
            String sid = RandomStringUtils.randomAlphanumeric(10);
            //等级1发放等级2的优惠券
            Integer couponLevel=memberRights.getOriginalPrice().intValue()==1?2:memberRights.getOriginalPrice();
            //查询是否有上月发码数据
            Integer couponType=duanjuCouponService.lambdaQuery().eq(DuanjuCoupon::getMobile,mobile).eq(DuanjuCoupon::getCouponType,1).eq(DuanjuCoupon::getCouponLevel,couponLevel).count()>0?2:1;
            //等级2不发优惠券
            if(memberRights.getOriginalPrice().intValue()!=2){
                DuanjuCoupon duanjuCoupon=new DuanjuCoupon();
                duanjuCoupon.setMobile(junboChargeLog.getMobile());
                duanjuCoupon.setCouponCode(sid);
                /**订单号*/
                duanjuCoupon.setOrderId(junboChargeLog.getMiguOrderId());
                duanjuCoupon.setCouponLevel(couponLevel);
                //使用等级查询券码名称
                MemberRights rights=memberRightsService.lambdaQuery().select(MemberRights::getRightsName).eq(MemberRights::getOriginalPrice,couponLevel).ne(MemberRights::getRemark,"0").eq(MemberRights::getCompanyOwner,"HUYU").orderByDesc(MemberRights::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
                if(rights!=null){
                    duanjuCoupon.setCouponName(rights.getRightsName());
                }
                /**券码类型:0=初始,1=包月,2=续订,3=金豆兑换*/
                duanjuCoupon.setCouponType(couponType);
                duanjuCouponService.save(duanjuCoupon);
            }

            //创建金豆消费记录
            DuanjuGoldenBeanOrder duanjuGoldenBeanOrder=new DuanjuGoldenBeanOrder();
            /**订单号*/
            duanjuGoldenBeanOrder.setOrderNo(junboChargeLog.getMiguOrderId());
            /**手机号*/
            duanjuGoldenBeanOrder.setMobile(mobile);
            //透传字段
            duanjuGoldenBeanOrder.setExtrInfo(extrInfo);
            /**订单金豆*/
            duanjuGoldenBeanOrder.setOrderGoldenBean(memberRights.getProductPrice());
            /**订单状态:0=发放,1=兑换*/
            duanjuGoldenBeanOrder.setOrderStatus(0);
            /**金豆类型:0=初始,1=包月,2=续订*/
            duanjuGoldenBeanOrder.setGoldenBeanType(couponType);
            duanjuGoldenBeanOrderService.save(duanjuGoldenBeanOrder);


            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(sid);
            junboChargeLogService.updateById(junboChargeLog);
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("订单已充值");
            junboChargeLog.setRemark("订单已充值");
            junboChargeLogService.updateById(junboChargeLog);
        }
    }




    @Override
    public String saveCode(String channel,int length,String edId) {
        MiGuHuYuChannelConfig miGuHuYuChannelConfig= miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(channel);
        if(miGuHuYuChannelConfig==null){
            log.warn("{}-渠道号未配置-渠道号:{}",LOG_TAG,channel);
            return null;
        }
        this.codeLogin();
        String code="";
        List<String> codeList=null;
        if(SWFZ_NAME.equals(miGuHuYuChannelConfig.getGameName())){
            String couponCode=queryCode(channel,edId);
            if(StringUtils.isNotBlank(couponCode)){
                codeList=productCode(couponCode,length);
                code=couponCode+","+String.join(",", codeList);
            }else{
                codeList=productCode(length);
                code=String.join(",", codeList);
            }
        }else if(DYXC_NAME.equals(miGuHuYuChannelConfig.getGameName())){
            String couponCode=queryCode(channel,edId);
            if(StringUtils.isNotBlank(couponCode)){
                codeList=productCode(couponCode,length);
                code=couponCode+","+String.join(",", codeList);
            }else{
                codeList=productCode(length);
                code=String.join(",", codeList);
            }
        }else if(CFDTW_NAME.equals(miGuHuYuChannelConfig.getGameName())){
            String couponCode=queryCode(channel,edId);
            if(StringUtils.isNotBlank(couponCode)){
                codeList=productCode(couponCode,length);
                code=couponCode+","+String.join(",", codeList);
            }else{
                codeList=productCode(length);
                code=String.join(",", codeList);
            }

        }else if(WZWD_NAME.equals(miGuHuYuChannelConfig.getGameName())){
            String couponCode=queryCode(channel,edId);
            if(StringUtils.isNotBlank(couponCode)){
                codeList=productCode(couponCode,length);
                code=couponCode+","+String.join(",", codeList);
            }else{
                codeList=productCode(length);
                code=String.join(",", codeList);
            }

        }
        RequestBody body = RequestBody.create(mediaType, "TB=43&edId="+(StringUtils.isNotBlank(edId)?edId:miGuHuYuChannelConfig.getEdId())+"&Submit=save&bkUrl=https://m3.edg.cn/adm_list.asp?TB=43%26gg=4678308%26ee=1560938&edText1="+miGuHuYuChannelConfig.getEdText1()+"&edText2="+miGuHuYuChannelConfig.getEdText2()+"&edText3="+miGuHuYuChannelConfig.getEdText3()+"&edText4="+code+"&edText5="+miGuHuYuChannelConfig.getEdText5()+"&submit1=保存");
        Request request = new Request.Builder()
                .url("https://m3.edg.cn/adm_edit.asp")
                .method("POST", body)
                .addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .addHeader("accept-language", "zh-CN,zh;q=0.9")
                .addHeader("cache-control", "no-cache")
                .addHeader("content-type", "application/x-www-form-urlencoded")
                .addHeader("origin", "https://m3.edg.cn")
                .addHeader("pragma", "no-cache")
                .addHeader("priority", "u=0, i")
                .addHeader("referer", "https://m3.edg.cn/adm_edit.asp?TB=43&edId="+(StringUtils.isNotBlank(edId)?edId:miGuHuYuChannelConfig.getEdId())+"&gg=6842920")
                .addHeader("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"Windows\"")
                .addHeader("sec-fetch-dest", "iframe")
                .addHeader("sec-fetch-mode", "navigate")
                .addHeader("sec-fetch-site", "same-origin")
                .addHeader("sec-fetch-user", "?1")
                .addHeader("upgrade-insecure-requests", "1")
                .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
                .build();
        try (Response response = codeClient.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            if(content.contains(MSG)){
                Date time=new Date();
                Date invalidTime = null;
                try {
                    invalidTime = DateUtil.stringToDate("2099-12-31 23:59:59");
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                if(SWFZ_NAME.equals(miGuHuYuChannelConfig.getGameName())){
                    List<SwfzCouponCode> couponCodeList=Lists.newArrayList();
                    for(String sid:codeList){
                        if(StringUtils.isNotBlank(sid.trim())){
                            SwfzCouponCode couponCode=new SwfzCouponCode();
                            couponCode.setStatus(0);
                            couponCode.setCouponCode(sid.trim());
                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
                            couponCode.setInvalidTime(invalidTime);
                            couponCode.setCreateTime(time);
                            couponCode.setUpdateTime(time);
                            couponCodeList.add(couponCode);
                        }
                    }
                    if(couponCodeList!=null && couponCodeList.size()>0){
                        swfzCouponCodeService.saveBatch(couponCodeList);
                    }
                }else if(DYXC_NAME.equals(miGuHuYuChannelConfig.getGameName())){
                    List<DyxcCouponCode> couponCodeList=Lists.newArrayList();
                    for(String sid:codeList){
                        if(StringUtils.isNotBlank(sid.trim())){
                            DyxcCouponCode couponCode=new DyxcCouponCode();
                            couponCode.setStatus(0);
                            couponCode.setCouponCode(sid.trim());
                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
                            couponCode.setInvalidTime(invalidTime);
                            couponCode.setCreateTime(time);
                            couponCode.setUpdateTime(time);
                            couponCodeList.add(couponCode);
                        }
                    }
                    if(couponCodeList!=null && couponCodeList.size()>0){
                        dyxcCouponCodeService.saveBatch(couponCodeList);
                    }

                }else if(CFDTW_NAME.equals(miGuHuYuChannelConfig.getGameName())){
                    List<CfdtwCouponCode> couponCodeList=Lists.newArrayList();
                    for(String sid:codeList){
                        if(StringUtils.isNotBlank(sid.trim())){
                            CfdtwCouponCode couponCode=new CfdtwCouponCode();
                            couponCode.setStatus(0);
                            couponCode.setCouponCode(sid.trim());
                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
                            couponCode.setInvalidTime(invalidTime);
                            couponCode.setCreateTime(time);
                            couponCode.setUpdateTime(time);
                            couponCodeList.add(couponCode);
                        }
                    }
                    if(couponCodeList!=null && couponCodeList.size()>0){
                        cfdtwCouponCodeService.saveBatch(couponCodeList);
                    }
                }else if(WZWD_NAME.equals(miGuHuYuChannelConfig.getGameName())){
                    List<WzwdCouponCode> couponCodeList=Lists.newArrayList();
                    for(String sid:codeList){
                        if(StringUtils.isNotBlank(sid.trim())){
                            WzwdCouponCode couponCode=new WzwdCouponCode();
                            couponCode.setStatus(0);
                            couponCode.setCouponCode(sid.trim());
                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
                            couponCode.setInvalidTime(invalidTime);
                            couponCode.setCreateTime(time);
                            couponCode.setUpdateTime(time);
                            couponCodeList.add(couponCode);
                        }
                    }
                    if(couponCodeList!=null && couponCodeList.size()>0){
                        wzwdCouponCodeService.saveBatch(couponCodeList);
                    }

                }
            }


            return content;
        } catch (IOException e) {
            log.error("{}-请求异常:", LOG_TAG, e);
            return null;
        }
    }
    private List<String> newProductCode(String code,String codes){
        List<String> codeList = Arrays.asList(code.split(","));
        List<String> productCodeList = Arrays.asList(codes.split(","));
        List<String> product = codeList.stream().filter(item -> !productCodeList.contains(item)).distinct().collect(Collectors.toList());
        return product;
    }
    private List<String> productCode(String code,int length){
        List<String> productCode= Lists.newArrayList();
        for(int i = 0; i < length; i++){
            String sid = RandomStringUtils.randomAlphanumeric(8);
            if(!code.contains(sid)){
                productCode.add(sid);
            }
        }
        return productCode;
    }
    private List<String> productCode(int length){
        List<String> productCode= Lists.newArrayList();
        for(int i = 0; i < length; i++){
            String sid = RandomStringUtils.randomAlphanumeric(8);
            productCode.add(sid);
        }
        return productCode;
    }



    private String queryCode(String channel,String edId){
        MiGuHuYuChannelConfig miGuHuYuChannelConfig= miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(channel);
        if(miGuHuYuChannelConfig==null){
            log.warn("{}-渠道号未配置-渠道号:{}",LOG_TAG,channel);
            return null;
        }
        Request request = new Request.Builder()
                .url("https://m3.edg.cn/adm_edit.asp?TB=43&edId="+(StringUtils.isNotBlank(edId)?edId:miGuHuYuChannelConfig.getEdId())+"&gg=8083612")
                .method("GET",null)
                .addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .addHeader("accept-language", "zh-CN,zh;q=0.9")
                .addHeader("cache-control", "no-cache")
                .addHeader("pragma", "no-cache")
                .addHeader("priority", "u=0, i")
                .addHeader("referer", "https://m3.edg.cn/adm_list.asp?TB=43&gg=4997217&ee=7469141")
                .addHeader("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"Windows\"")
                .addHeader("sec-fetch-dest", "iframe")
                .addHeader("sec-fetch-mode", "navigate")
                .addHeader("sec-fetch-site", "same-origin")
                .addHeader("sec-fetch-user", "?1")
                .addHeader("upgrade-insecure-requests", "1")
                .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
                .build();
         try (Response response = codeClient.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
             String content = response.body().string();
             Document document = Jsoup.parse(content);
             String edText4= document.selectFirst("textarea[name='edText4']").text();
             return edText4;
        } catch (IOException e) {
            log.error("{}-请求异常:", LOG_TAG, e);
            return null;
        }

    }
    private void codeLogin(){
    RequestBody body = RequestBody.create(mediaType, "Url=adm_index.asp?gg=1094630&UID=lisa&Pass=Mg1128&Mode=1&Submit=立即登陆");
    Request request = new Request.Builder()
            .url("https://m3.edg.cn/web_login.asp")
            .method("POST", body)
            .addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
            .addHeader("accept-language", "zh-CN,zh;q=0.9")
            .addHeader("cache-control", "no-cache")
            .addHeader("content-type", "application/x-www-form-urlencoded")
            .addHeader("cookie", "ASPSESSIONIDCWTBDTTA=PDEBEOOBMANLDGHLEAJNBMLP; ASPSESSIONIDAURBARQD=DJIJCKGCGDOBLGOPIMMMBMMN; ASPSESSIONIDCUQDAQRD=HHDLOBHCGLPEJPPOOLBJBAON; ASPSESSIONIDCUSAARQC=GMEPLAJCHANGIJFCHMAFJKED; ASPSESSIONIDCUSAARQC=CMEPLAJCPPHAIMIBOBGKMGNP")
            .addHeader("origin", "https://m3.edg.cn")
            .addHeader("pragma", "no-cache")
            .addHeader("priority", "u=0, i")
            .addHeader("referer", "https://m3.edg.cn/web_login.asp")
            .addHeader("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
            .addHeader("sec-ch-ua-mobile", "?0")
            .addHeader("sec-ch-ua-platform", "\"Windows\"")
            .addHeader("sec-fetch-dest", "document")
            .addHeader("sec-fetch-mode", "navigate")
            .addHeader("sec-fetch-site", "same-origin")
            .addHeader("sec-fetch-user", "?1")
            .addHeader("upgrade-insecure-requests", "1")
            .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
            .build();
        try (Response response = codeClient.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
        } catch (IOException e) {
            log.error("{}-请求异常:", LOG_TAG, e);
        }
}
//    @Override
//    public String saveCode(String channel,int length,String start,String end) throws ParseException {
//        MiGuHuYuChannelConfig miGuHuYuChannelConfig= miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(channel);
//        if(miGuHuYuChannelConfig==null){
//            log.warn("{}-渠道号未配置-渠道号:{}",LOG_TAG,channel);
//            return null;
//        }
//        this.codeLogin();
//        String code="";
//        List<String> codeList=null;
//        if(SWFZ_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//            String couponCodes=queryCode(channel);
//            List<String> couponCodeList=swfzCouponCodeService.lambdaQuery().select(SwfzCouponCode::getCouponCode).eq(SwfzCouponCode::getRightsId,miGuHuYuChannelConfig.getRightsId()).between(SwfzCouponCode::getSendTime,start,end).list().stream().map(SwfzCouponCode::getCouponCode).collect(Collectors.toList());
//            String couponCode=String.join(",", newProductCode(couponCodes,String.join(",", couponCodeList)));
//            if(StringUtils.isNotBlank(couponCode)){
//                codeList=productCode(couponCode,length);
//                code=couponCode+","+String.join(",", codeList);
//            }else{
//                codeList=productCode(length);
//                code=String.join(",", codeList);
//            }
//        }else if(DYXC_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//            String couponCodes=queryCode(channel);
//            List<String> couponCodeList=dyxcCouponCodeService.lambdaQuery().select(DyxcCouponCode::getCouponCode).eq(DyxcCouponCode::getRightsId,miGuHuYuChannelConfig.getRightsId()).between(DyxcCouponCode::getSendTime,start,end).list().stream().map(DyxcCouponCode::getCouponCode).collect(Collectors.toList());
//            String couponCode=String.join(",", newProductCode(couponCodes,String.join(",", couponCodeList)));
//            if(StringUtils.isNotBlank(couponCode)){
//                codeList=productCode(couponCode,length);
//                code=couponCode+","+String.join(",", codeList);
//            }else{
//                codeList=productCode(length);
//                code=String.join(",", codeList);
//            }
//        }else if(CFDTW_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//            String couponCodes=queryCode(channel);
//            List<String> couponCodeList=cfdtwCouponCodeService.lambdaQuery().select(CfdtwCouponCode::getCouponCode).eq(CfdtwCouponCode::getRightsId,miGuHuYuChannelConfig.getRightsId()).between(CfdtwCouponCode::getSendTime,start,end).list().stream().map(CfdtwCouponCode::getCouponCode).collect(Collectors.toList());
//            String couponCode=String.join(",", newProductCode(couponCodes,String.join(",", couponCodeList)));
//            if(StringUtils.isNotBlank(couponCode)){
//                codeList=productCode(couponCode,length);
//                code=couponCode+","+String.join(",", codeList);
//            }else{
//                codeList=productCode(length);
//                code=String.join(",", codeList);
//            }
//
//        }else if(WZWD_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//            String couponCodes=queryCode(channel);
//            List<String> couponCodeList=wzwdCouponCodeService.lambdaQuery().select(WzwdCouponCode::getCouponCode).eq(WzwdCouponCode::getRightsId,miGuHuYuChannelConfig.getRightsId()).between(WzwdCouponCode::getSendTime,start,end).list().stream().map(WzwdCouponCode::getCouponCode).collect(Collectors.toList());
//            String couponCode=String.join(",", newProductCode(couponCodes,String.join(",", couponCodeList)));
//            if(StringUtils.isNotBlank(couponCode)){
//                codeList=productCode(couponCode,length);
//                code=couponCode+","+String.join(",", codeList);
//            }else{
//                codeList=productCode(length);
//                code=String.join(",", codeList);
//            }
//
//        }
//        RequestBody body = RequestBody.create(mediaType, "TB=43&edId="+(StringUtils.isNotBlank(edId)?edId:miGuHuYuChannelConfig.getEdId())+"&Submit=save&bkUrl=https://m3.edg.cn/adm_list.asp?TB=43%26gg=4678308%26ee=1560938&edText1="+miGuHuYuChannelConfig.getEdText1()+"&edText2="+miGuHuYuChannelConfig.getEdText2()+"&edText3="+miGuHuYuChannelConfig.getEdText3()+"&edText4="+code+"&edText5="+miGuHuYuChannelConfig.getEdText5()+"&submit1=保存");
//        Request request = new Request.Builder()
//                .url("https://m3.edg.cn/adm_edit.asp")
//                .method("POST", body)
//                .addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
//                .addHeader("accept-language", "zh-CN,zh;q=0.9")
//                .addHeader("cache-control", "no-cache")
//                .addHeader("content-type", "application/x-www-form-urlencoded")
//                .addHeader("origin", "https://m3.edg.cn")
//                .addHeader("pragma", "no-cache")
//                .addHeader("priority", "u=0, i")
//                .addHeader("referer", "https://m3.edg.cn/adm_edit.asp?TB=43&edId="+StringUtils.isNotBlank(edId)?edId:miGuHuYuChannelConfig.getEdId()+"&gg=6842920")
//                .addHeader("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
//                .addHeader("sec-ch-ua-mobile", "?0")
//                .addHeader("sec-ch-ua-platform", "\"Windows\"")
//                .addHeader("sec-fetch-dest", "iframe")
//                .addHeader("sec-fetch-mode", "navigate")
//                .addHeader("sec-fetch-site", "same-origin")
//                .addHeader("sec-fetch-user", "?1")
//                .addHeader("upgrade-insecure-requests", "1")
//                .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
//                .build();
//        try (Response response = codeClient.newCall(request).execute()){
//            if (!response.isSuccessful()) {
//                throw new IOException("Unexpected code " + response);
//            }
//            String content = response.body().string();
//            if(content.contains(MSG)){
//                Date time=new Date();
//                Date invalidTime = null;
//                try {
//                    invalidTime = DateUtil.stringToDate("2099-12-31 23:59:59");
//                } catch (ParseException e) {
//                    e.printStackTrace();
//                }
//                if(SWFZ_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//                    List<SwfzCouponCode> couponCodeList=Lists.newArrayList();
//                    for(String sid:codeList){
//                        if(StringUtils.isNotBlank(sid.trim())){
//                            SwfzCouponCode couponCode=new SwfzCouponCode();
//                            couponCode.setStatus(0);
//                            couponCode.setCouponCode(sid.trim());
//                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
//                            couponCode.setInvalidTime(invalidTime);
//                            couponCode.setCreateTime(time);
//                            couponCode.setUpdateTime(time);
//                            couponCodeList.add(couponCode);
//                        }
//                    }
//                    if(couponCodeList!=null && couponCodeList.size()>0){
//                        swfzCouponCodeService.saveBatch(couponCodeList);
//                    }
//                }else if(DYXC_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//                    List<DyxcCouponCode> couponCodeList=Lists.newArrayList();
//                    for(String sid:codeList){
//                        if(StringUtils.isNotBlank(sid.trim())){
//                            DyxcCouponCode couponCode=new DyxcCouponCode();
//                            couponCode.setStatus(0);
//                            couponCode.setCouponCode(sid.trim());
//                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
//                            couponCode.setInvalidTime(invalidTime);
//                            couponCode.setCreateTime(time);
//                            couponCode.setUpdateTime(time);
//                            couponCodeList.add(couponCode);
//                        }
//                    }
//                    if(couponCodeList!=null && couponCodeList.size()>0){
//                        dyxcCouponCodeService.saveBatch(couponCodeList);
//                    }
//
//                }else if(CFDTW_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//                    List<CfdtwCouponCode> couponCodeList=Lists.newArrayList();
//                    for(String sid:codeList){
//                        if(StringUtils.isNotBlank(sid.trim())){
//                            CfdtwCouponCode couponCode=new CfdtwCouponCode();
//                            couponCode.setStatus(0);
//                            couponCode.setCouponCode(sid.trim());
//                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
//                            couponCode.setInvalidTime(invalidTime);
//                            couponCode.setCreateTime(time);
//                            couponCode.setUpdateTime(time);
//                            couponCodeList.add(couponCode);
//                        }
//                    }
//                    if(couponCodeList!=null && couponCodeList.size()>0){
//                        cfdtwCouponCodeService.saveBatch(couponCodeList);
//                    }
//                }else if(WZWD_NAME.equals(miGuHuYuChannelConfig.getGameName())){
//                    List<WzwdCouponCode> couponCodeList=Lists.newArrayList();
//                    for(String sid:codeList){
//                        if(StringUtils.isNotBlank(sid.trim())){
//                            WzwdCouponCode couponCode=new WzwdCouponCode();
//                            couponCode.setStatus(0);
//                            couponCode.setCouponCode(sid.trim());
//                            couponCode.setRightsId(miGuHuYuChannelConfig.getRightsId());
//                            couponCode.setInvalidTime(invalidTime);
//                            couponCode.setCreateTime(time);
//                            couponCode.setUpdateTime(time);
//                            couponCodeList.add(couponCode);
//                        }
//                    }
//                    if(couponCodeList!=null && couponCodeList.size()>0){
//                        wzwdCouponCodeService.saveBatch(couponCodeList);
//                    }
//
//                }
//            }
//            return content;
//        } catch (IOException e) {
//            log.error("{}-请求异常:", LOG_TAG, e);
//            return null;
//        }
//    }
//    @Override
//    public String saveCode(String channel,String start,String end) throws ParseException {
//        MiGuHuYuChannelConfig miGuHuYuChannelConfig = miGuHuYuQueryMemberProperties.getMiGuHuYuConfigByChannel(channel);
//        if (miGuHuYuChannelConfig == null) {
//            log.warn("{}-渠道号未配置-渠道号:{}", LOG_TAG, channel);
//            return null;
//        }
//        this.codeLogin();
//        String code = "";
//        List<String> codeList = null;
//        if (SWFZ_NAME.equals(miGuHuYuChannelConfig.getGameName())) {
//            String couponCodes = queryCode(channel);
//            List<String> couponCodeList = swfzCouponCodeService.lambdaQuery().select(SwfzCouponCode::getCouponCode).eq(SwfzCouponCode::getRightsId, miGuHuYuChannelConfig.getRightsId()).between(SwfzCouponCode::getSendTime, start, end).list().stream().map(SwfzCouponCode::getCouponCode).collect(Collectors.toList());
//            return String.join(",", newProductCodes(couponCodes, String.join(",", couponCodeList)));
//
//        } else if (DYXC_NAME.equals(miGuHuYuChannelConfig.getGameName())) {
//            String couponCodes = queryCode(channel);
//            List<String> couponCodeList = dyxcCouponCodeService.lambdaQuery().select(DyxcCouponCode::getCouponCode).eq(DyxcCouponCode::getRightsId, miGuHuYuChannelConfig.getRightsId()).between(DyxcCouponCode::getSendTime, start, end).list().stream().map(DyxcCouponCode::getCouponCode).collect(Collectors.toList());
//            return String.join(",", newProductCodes(couponCodes, String.join(",", couponCodeList)));
//
//        } else if (CFDTW_NAME.equals(miGuHuYuChannelConfig.getGameName())) {
//            String couponCodes = queryCode(channel);
//            List<String> couponCodeList = cfdtwCouponCodeService.lambdaQuery().select(CfdtwCouponCode::getCouponCode).eq(CfdtwCouponCode::getRightsId, miGuHuYuChannelConfig.getRightsId()).between(CfdtwCouponCode::getSendTime, start, end).list().stream().map(CfdtwCouponCode::getCouponCode).collect(Collectors.toList());
//            return String.join(",", newProductCodes(couponCodes, String.join(",", couponCodeList)));
//
//
//        } else if (WZWD_NAME.equals(miGuHuYuChannelConfig.getGameName())) {
//            String couponCodes = queryCode(channel);
//            List<String> couponCodeList = wzwdCouponCodeService.lambdaQuery().select(WzwdCouponCode::getCouponCode).eq(WzwdCouponCode::getRightsId, miGuHuYuChannelConfig.getRightsId()).between(WzwdCouponCode::getSendTime, start, end).list().stream().map(WzwdCouponCode::getCouponCode).collect(Collectors.toList());
//            return String.join(",", newProductCodes(couponCodes, String.join(",", couponCodeList)));
//
//        }
//        return null;
//    }
//
//    private List<String> newProductCodes(String code,String codes){
//        List<String> codeList = Arrays.asList(code.split(","));
//        List<String> productCodeList = Arrays.asList(codes.split(","));
//        List<String> product = productCodeList.stream().filter(item -> !codeList.contains(item)).distinct().collect(Collectors.toList());
//        return product;
//    }

    /**
     * 互娱券码缓存,一次性从数据库加载多条,然后用redis list缓存发码,发出后更新数据库
     * @param rightsId
     * @return
     */
    @Override
    public HetuCouponCode fetchCouponFromCache(String rightsId){
        final String key = REDIS_KEY_COUPON_HUYU_PREFIX+rightsId;
        HetuCouponCode hetuCouponCode = (HetuCouponCode) redisUtil.lPop(key);
        if(hetuCouponCode!=null){
           return hetuCouponCode;
        }
        //如果没有券码或者已经用完,则从数据库中装载
        List<HetuCouponCode> hetuCouponCodeList = hetuCouponCodeService.lambdaQuery()
                .select(HetuCouponCode::getId,HetuCouponCode::getCouponCode)
                .eq(HetuCouponCode::getRightsId,rightsId)
                .eq(HetuCouponCode::getStatus,BizConstant.NOT_USE)
                .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE)
                .list();
        if(hetuCouponCodeList.isEmpty()){
            log.error("互娱权益Id:{},券码已用尽",rightsId);
            return null;
        }
        redisUtil.lSet(key, Lists.newArrayList(hetuCouponCodeList));

        return (HetuCouponCode) redisUtil.lPop(key);
    }



    /**
     * 互娱券码缓存,一次性从数据库加载多条,然后用redis list缓存发码,发出后更新数据库
     * @param rightsId
     * @return
     */
    @Override
    public Object fetchCouponFromCache(String rightsId, String gameName){
        final String key = REDIS_KEY_COUPON_HUYU_PREFIX+rightsId;
        Object couponCode= redisUtil.lPop(key);
        if(couponCode!=null){
            return couponCode;
        }
        if(SWFZ_NAME.equals(gameName)){
            List<SwfzCouponCode> couponCodeList = swfzCouponCodeService.lambdaQuery()
                    .select(SwfzCouponCode::getId,SwfzCouponCode::getCouponCode)
                    .eq(SwfzCouponCode::getRightsId,rightsId)
                    .eq(SwfzCouponCode::getStatus,BizConstant.NOT_USE)
                    .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE)
                    .list();
            if(couponCodeList.isEmpty()){
                log.error("互娱权益Id:{},券码已用尽",rightsId);
                return null;
            }
            redisUtil.lSet(key, Lists.newArrayList(couponCodeList));
            return redisUtil.lPop(key);
        }else if(DYXC_NAME.equals(gameName)){
            List<DyxcCouponCode> couponCodeList = dyxcCouponCodeService.lambdaQuery()
                    .select(DyxcCouponCode::getId,DyxcCouponCode::getCouponCode)
                    .eq(DyxcCouponCode::getRightsId,rightsId)
                    .eq(DyxcCouponCode::getStatus,BizConstant.NOT_USE)
                    .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE)
                    .list();
            if(couponCodeList.isEmpty()){
                log.error("互娱权益Id:{},券码已用尽",rightsId);
                return null;
            }
            redisUtil.lSet(key, Lists.newArrayList(couponCodeList));
            return redisUtil.lPop(key);
        }else if(CFDTW_NAME.equals(gameName)){
            List<CfdtwCouponCode> couponCodeList = cfdtwCouponCodeService.lambdaQuery()
                    .select(CfdtwCouponCode::getId,CfdtwCouponCode::getCouponCode)
                    .eq(CfdtwCouponCode::getRightsId,rightsId)
                    .eq(CfdtwCouponCode::getStatus,BizConstant.NOT_USE)
                    .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE)
                    .list();
            if(couponCodeList.isEmpty()){
                log.error("互娱权益Id:{},券码已用尽",rightsId);
                return null;
            }
            redisUtil.lSet(key, Lists.newArrayList(couponCodeList));
            return redisUtil.lPop(key);
        }else if(WZWD_NAME.equals(gameName)){
            List<WzwdCouponCode> couponCodeList = wzwdCouponCodeService.lambdaQuery()
                    .select(WzwdCouponCode::getId,WzwdCouponCode::getCouponCode)
                    .eq(WzwdCouponCode::getRightsId,rightsId)
                    .eq(WzwdCouponCode::getStatus,BizConstant.NOT_USE)
                    .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE)
                    .list();
            if(couponCodeList.isEmpty()){
                log.error("互娱权益Id:{},券码已用尽",rightsId);
                return null;
            }
            redisUtil.lSet(key, Lists.newArrayList(couponCodeList));
            return redisUtil.lPop(key);
        }else if(SJCS_NAME.equals(gameName)){
            List<SjcsCouponCode> couponCodeList = sjscCouponCodeService.lambdaQuery()
                    .select(SjcsCouponCode::getId,SjcsCouponCode::getCouponCode)
                    .eq(SjcsCouponCode::getRightsId,rightsId)
                    .eq(SjcsCouponCode::getStatus,BizConstant.NOT_USE)
                    .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE)
                    .list();
            if(couponCodeList.isEmpty()){
                log.error("互娱权益Id:{},券码已用尽",rightsId);
                return null;
            }
            redisUtil.lSet(key, Lists.newArrayList(couponCodeList));
            return redisUtil.lPop(key);
        }else if(AHZZ_NAME.equals(gameName)){
            List<AhzzCouponCode> couponCodeList = ahzzCouponCodeService.lambdaQuery()
                    .select(AhzzCouponCode::getId,AhzzCouponCode::getCouponCode)
                    .eq(AhzzCouponCode::getRightsId,rightsId)
                    .eq(AhzzCouponCode::getStatus,BizConstant.NOT_USE)
                    .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE)
                    .list();
            if(couponCodeList.isEmpty()){
                log.error("互娱权益Id:{},券码已用尽",rightsId);
                return null;
            }
            redisUtil.lSet(key, Lists.newArrayList(couponCodeList));
            return redisUtil.lPop(key);
        }
        log.error("权益产品未配置:{},游戏标识:{}",rightsId,gameName);
        return null;
    }

    /**
     * 河图发码失败重新补发券码
     * @param orderId
     * @param phoneNum
     * @param extrInfo
     * @param junboChargeLog
     */
    @Override
    public void failReissue(String orderId, String phoneNum, String extrInfo, JunboChargeLog junboChargeLog) {
        String couponCode=this.miGuHuYuFailReissue(junboChargeLog, StringUtils.isNotBlank(phoneNum)? phoneNum : orderId,extrInfo);
        ObjectNode dataNode =mapper.createObjectNode();
        String timestamp= String.valueOf(System.currentTimeMillis());
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("MIGUHUYU");
        String sign=getSign(orderId,timestamp,otherRecharge);
        dataNode.put("orderId", orderId);
        dataNode.put("timestamp",timestamp);
        dataNode.put("sendTime",timestamp);
        dataNode.put("extrInfo", extrInfo);
        if(StringUtils.isNotBlank(couponCode)){
            dataNode.put("sendResult","1");
            dataNode.put("sendMessage","发码成功");
            dataNode.put("cdKey",couponCode);
            log.info("咪咕互娱道具下发=>game:hetu,mobile:{},couponCode:{},orderId:{},extrInfo:{}",phoneNum,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),"咪咕互娱道具下发通知",sign);
        }else{
            dataNode.put("sendResult","0");
            dataNode.put("sendMessage","发码失败，券码数量不足");
            dataNode.put("cdKey","");
            log.info("咪咕互娱道具下发-发码失败，券码数量不足=>game:hetu,mobile:{},couponCode:{},orderId:{},extrInfo:{}",phoneNum,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),"咪咕互娱道具下发通知",sign);
        }
    }


    /**
     * 河图发码失败重新补发券码-券码充值
     * @param junboChargeLog
     * @param mobile
     */
    private String miGuHuYuFailReissue(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //用之前的激活码
        CouponCode couponCode = this.lambdaQuery().select(CouponCode::getId,CouponCode::getCouponCode).eq(CouponCode::getRightsId,junboChargeLog.getCouponId()).eq(CouponCode::getStatus,BizConstant.NOT_USE).orderByAsc(CouponCode::getId).last(BizConstant.SQL_LIMIT_ONE).one();
        if(couponCode==null){
            return null;
        }
        String code=couponCode.getCouponCode();
        String id=couponCode.getId();
        junboChargeLog.setUpdateTime(new Date());
        if (StringUtils.isNotBlank(code)) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            this.updateHeTuCouponCodeStatus(id, junboChargeLog.getMiguOrderId(),BizConstant.RECHARGE_WAIT,extrInfo);
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(code);
            junboChargeLogService.updateById(junboChargeLog);
            return code;
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }

    @Override
    @Transactional
    public void updateStatusList(List<String> list){
        this.baseMapper.updateBatch(list);
    };


    @Override
    public void hetuCodeResume(JunboChargeLog junboChargeLog) {
        ObjectNode dataNode =mapper.createObjectNode();
        String timestamp= String.valueOf(System.currentTimeMillis());
        final OtherRecharge otherRecharge = junboApiProperties.getOtherRechargeMap().get("MIGUHUYU");
        String sign=getSign(junboChargeLog.getMiguOrderId(),timestamp,otherRecharge);
        dataNode.put("orderId", junboChargeLog.getMiguOrderId());
        dataNode.put("timestamp",timestamp);
        dataNode.put("sendTime",timestamp);
        dataNode.put("extrInfo", junboChargeLog.getJunboOrderId());
        dataNode.put("sendResult","1");
        dataNode.put("sendMessage","发码成功");
        dataNode.put("cdKey",junboChargeLog.getCouponCode());
        this.implementHttpPostResult(otherRecharge.getSendbackUrl(),dataNode.toString(),"咪咕互娱道具下发回调",sign);
    }

    @Override
    public void ahzzSendCodeScheduleDeduct(String id){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            String couponCode=this.getAhzzCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId());
            if(StringUtils.isNotBlank(couponCode)){
                smsModelService.sendSmsAsync(junboChargeLog.getMobile(),junboChargeLog.getCouponId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode);
            }
            miguHuyuCallback(junboChargeLog.getMobile(),couponCode,junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),AHZZ_NAME);
        }
    }

    /**
     * 获取暗黑主宰券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String getAhzzCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo) {
        //查询有效期内未使用的券码
        AhzzCouponCode couponCode=(AhzzCouponCode)this.fetchCouponFromCache(junboChargeLog.getCouponId(),AHZZ_NAME);
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //激活码绑定订单号
            ahzzCouponCodeService.lambdaUpdate().eq(AhzzCouponCode::getId,couponCode.getId()).set(AhzzCouponCode::getMobile,mobile).set(AhzzCouponCode::getSendTime,new Date()).set(AhzzCouponCode::getUpdateTime,new Date()).set(AhzzCouponCode::getExtrInfo,extrInfo).set(AhzzCouponCode::getOrderId,junboChargeLog.getMiguOrderId()).set(AhzzCouponCode::getStatus,BizConstant.RECHARGE_WAIT).update();
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(couponCode.getCouponCode());
            junboChargeLogService.updateById(junboChargeLog);
            return couponCode.getCouponCode();
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }


    @Override
    public void gameSendCodeScheduleDeduct(String id,String gameName){
        JunboChargeLog junboChargeLog=junboChargeLogService.getById(id);
        if(junboChargeLog!=null){
            String couponCode=this.getGameCouponCode(junboChargeLog, StringUtils.isNotBlank(junboChargeLog.getMobile())? junboChargeLog.getMobile() : junboChargeLog.getJunboOrderId(),junboChargeLog.getJunboOrderId(),gameName);
            if(StringUtils.isNotBlank(couponCode)){
                smsModelService.sendSmsAsync(junboChargeLog.getMobile(),junboChargeLog.getCouponId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_ORDER,couponCode);
            }
            gameCallback(junboChargeLog.getMobile(),couponCode,junboChargeLog.getMiguOrderId(),junboChargeLog.getJunboOrderId(),gameName);
        }
    }


    /**
     * 获取游戏券码以及设置充值状态
     * @param junboChargeLog
     * @param mobile
     */
    public synchronized String getGameCouponCode(JunboChargeLog junboChargeLog,String mobile,String extrInfo,String gameName) {
        //查询有效期内未使用的券码
        GameGetCouponCode couponCode=(GameGetCouponCode)this.fetchCouponCodeFromCache(junboChargeLog.getCouponId(),gameName);
        junboChargeLog.setUpdateTime(new Date());
        if (couponCode!=null) {
            //券码充值告警
            rechargeAlertService.watchElemeRecharge(mobile,junboChargeLog.getServiceId(),junboChargeLog.getPackName());
            //激活码绑定订单号
            GameSendCouponCode sendCouponCode=new GameSendCouponCode();
            sendCouponCode.setMobile(mobile);
            sendCouponCode.setSendTime(new Date());
            sendCouponCode.setCouponCode(couponCode.getCouponCode());
            sendCouponCode.setExtrInfo(extrInfo);
            sendCouponCode.setOrderId(junboChargeLog.getMiguOrderId());
            sendCouponCode.setRightsId(junboChargeLog.getCouponId());
            sendCodeService.save(sendCouponCode);

            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING);
            junboChargeLog.setRespCode("0000");
            junboChargeLog.setRespMsg("成功");
            junboChargeLog.setRemark("成功");
            junboChargeLog.setCouponCode(couponCode.getCouponCode());
            junboChargeLogService.updateById(junboChargeLog);
            return couponCode.getCouponCode();
        } else {
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRespCode("-1");
            junboChargeLog.setRespMsg("暂无激活码");
            junboChargeLog.setRemark("暂无激活码");
            junboChargeLogService.updateById(junboChargeLog);
        }
        return null;
    }


    /**
     * 互娱券码缓存,一次性从数据库加载多条,然后用redis list缓存发码,发出后更新数据库
     * @param rightsId
     * @return
     */
    private Object fetchCouponCodeFromCache(String rightsId, String gameName){
        final String key = REDIS_KEY_COUPON_HUYU_PREFIX+rightsId;
        Object couponCode= redisUtil.lPop(key);
        if(couponCode!=null){
            return couponCode;
        }
        List<GameGetCouponCode> couponCodeList = getCodeService.lambdaQuery()
                .select(GameGetCouponCode::getId,GameGetCouponCode::getCouponCode)
                .gt(GameGetCouponCode::getCreateTime, DateUtil.getFirstDayOfMonthWithMinTime())
                .lt(GameGetCouponCode::getCreateTime, DateUtil.getLastDayOfMonthWithMaxTime())
                .eq(GameGetCouponCode::getRightsId,rightsId)
                .orderByAsc(GameGetCouponCode::getId)
                .last(" LIMIT "+ REDIS_COUPON_CACHE_SIZE).list();
        if(couponCodeList.isEmpty()){
            log.error("互娱权益Id:{},游戏名称:{},券码已用尽",rightsId,gameName);
            return null;
        }
        redisUtil.lSet(key, Lists.newArrayList(couponCodeList));
        Object code=redisUtil.lPop(key);
        couponCodeList.forEach(coupon->{
            getCodeService.removeById(coupon.getId());
        });
        return code;
    }



    /**
     * 咪咕互娱通知接口
     * @param couponCode
     * @param orderId
     * @param extrInfo
     * @param gameName
     */
    private void gameCallback(String mobile,String couponCode,String orderId,String extrInfo,String gameName){
        GameConfig gameConfig=gameConfigService.lambdaQuery().eq(GameConfig::getGameName,gameName).orderByDesc(GameConfig::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        ObjectNode dataNode =mapper.createObjectNode();
        String timestamp= String.valueOf(System.currentTimeMillis());
        String sign=getSign(orderId,timestamp,gameConfig.getSignKey());
        dataNode.put("orderId", orderId);
        dataNode.put("timestamp",timestamp);
        dataNode.put("sendTime",timestamp);
        dataNode.put("extrInfo", extrInfo);
        if(StringUtils.isNotBlank(couponCode)){
            dataNode.put("sendResult","1");
            dataNode.put("sendMessage","发码成功");
            dataNode.put("cdKey",couponCode);
            log.info("咪咕互娱道具下发-game:{},mobile:{},couponCode:{},orderId:{},extrInfo:{}",gameName,mobile,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(gameConfig.getSendbackUrl(),dataNode.toString(),gameConfig.getLogTag()+"咪咕互娱道具下发回调",sign);
        }else{
            dataNode.put("sendResult","0");
            dataNode.put("sendMessage","发码失败，券码数量不足");
            dataNode.put("cdKey","");
            log.info("咪咕互娱道具下发-发码失败，券码数量不足-game:{},mobile:{},couponCode:{},orderId:{},extrInfo:{}",gameName,mobile,couponCode,orderId,extrInfo);
            this.implementHttpPostResult(gameConfig.getSendbackUrl(),dataNode.toString(),gameConfig.getLogTag()+"咪咕互娱道具下发回调",sign);
        }
    }


    /**
     * 签名
     * @param orderId
     * @param timestamp
     * @param key
     * @return
     */
    private String getSign(String orderId,String timestamp,String key) {
        String parameterStr = orderId+timestamp+key;
        try {
            return DigestUtils.md5DigestAsHex(parameterStr.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("咪咕互娱道具下发回调,加密参数:{}",parameterStr,e);
        }
        return null;
    }
}
