package com.eleven.cms.service.impl;

import com.eleven.cms.dto.FaCaiShuNotify;
import com.eleven.cms.entity.FacaishuNotifyLog;
import com.eleven.cms.mapper.FacaishuNotifyLogMapper;
import com.eleven.cms.service.IFacaishuNotifyLogService;
import com.eleven.cms.util.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.Lists;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;

/**
 * @Description: 发财树通知记录
 * @Author: jeecg-boot
 * @Date:   2024-04-03
 * @Version: V1.0
 */
@Service
public class FacaishuNotifyLogServiceImpl extends ServiceImpl<FacaishuNotifyLogMapper, FacaishuNotifyLog> implements IFacaishuNotifyLogService {
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Override
    public void faCaiShuNotify(JsonNode jsonNode) {
        IntStream.range(0, jsonNode.size())
                .mapToObj(index -> jsonNode.get(index))
                .forEach(jsonNodeItem -> {
                    FaCaiShuNotify faCaiShuNotify = null;
                    try {
                        faCaiShuNotify = mapper.treeToValue(jsonNodeItem, FaCaiShuNotify.class);
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }

                    FacaishuNotifyLog facaishuNotifyLog=new FacaishuNotifyLog();
                    /**手机号*/
                    facaishuNotifyLog.setMobile(faCaiShuNotify.getPhone());

                    /**来源*/
                    facaishuNotifyLog.setPointNum(faCaiShuNotify.getPointNum());
                    /**订单时间*/
                    try {
                        facaishuNotifyLog.setOrderTime(DateUtil.stringToDate(faCaiShuNotify.getOrderTime()));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    /**session_id*/
                    facaishuNotifyLog.setSessionId(faCaiShuNotify.getSessionId());
                    /**省份*/
                    facaishuNotifyLog.setProvince(faCaiShuNotify.getProvince());
                    /**运营商*/
                    facaishuNotifyLog.setIsp(faCaiShuNotify.getIsp());
                    this.baseMapper.insert(facaishuNotifyLog);
                });
    }
}
