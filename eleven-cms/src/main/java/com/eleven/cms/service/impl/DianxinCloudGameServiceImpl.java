package com.eleven.cms.service.impl;

import com.eleven.cms.config.DianxinCloudGameProperties;
import com.eleven.cms.dto.DianxinCloudGameNotify;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IDianxinCloudGameService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.MD5Util;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.DianxinCloudGameResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.*;

/**
 * 电信云游戏业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/7 16:44
 **/
@Slf4j
@Service
public class DianxinCloudGameServiceImpl implements IDianxinCloudGameService {
    public static final String DIANXIN_CLOUDGAME_SUBSCRIBE_STATUS_SUB_SUCCESS = "1"; //开通成功
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    private DianxinCloudGameProperties dianxinCloudGameProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getIgnoreVerifySSLInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    @Override
    public Result<?> sendMessage(Subscribe subscribe) {
        ObjectNode node = mapper.createObjectNode();
        node.put("mchId",dianxinCloudGameProperties.getMchId());
        node.put("phone",subscribe.getMobile());
        ObjectNode wrapUser = mapper.createObjectNode();
        wrapUser.put("ip",subscribe.getIp());
        wrapUser.put("userAgent",subscribe.getUserAgent());
        node.putPOJO("wrapUser",wrapUser);
        node.put("signature",MD5Util.getDianxinCloudGameSign(node,dianxinCloudGameProperties.getKey()));
        String content =this.implementHttpPostResult(dianxinCloudGameProperties.getGetSmsCodeUrl(), node,"电信云游戏发送短信验证码",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("短信发送失败",subscribe.getId());
        }
        try {
            DianxinCloudGameResult result = mapper.readValue(content, DianxinCloudGameResult.class);
            if(result.isOK() && StringUtil.isNotBlank(result.getResData().getSequenceId())){
                return Result.ok("短信发送成功",result.getResData().getSequenceId());
            }
            return Result.error(result.getErrMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("短信发送失败",subscribe.getId());
    }

    @Override
    public Result<?> submitOrder(Subscribe subscribe) {
        ObjectNode node = mapper.createObjectNode();
        node.put("mchId",dianxinCloudGameProperties.getMchId());
        node.put("sequenceId",subscribe.getIspOrderNo());
        node.put("verifyCode",subscribe.getSmsCode());
        node.put("signature",MD5Util.getDianxinCloudGameSign(node,dianxinCloudGameProperties.getKey()));
        String content =this.implementHttpPostResult(dianxinCloudGameProperties.getSmsCodeUrl(), node,"电信云游戏提交短信验证码",subscribe.getMobile());
        if(StringUtil.isEmpty(content)){
            return Result.error("短信提交失败",subscribe.getId());
        }
        try {
            DianxinCloudGameResult result = mapper.readValue(content, DianxinCloudGameResult.class);
            if(result.isOK()){
                return Result.ok("短信提交成功");
            }
            return Result.error(result.getErrMsg());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Result.error("短信提交失败",subscribe.getId());
    }

    @Override
    public void dianxinCloudGameNotify(DianxinCloudGameNotify dianxinCloudGameNotify) {
        if(StringUtil.isEmpty(dianxinCloudGameNotify.getSignature())){
            log.error("电信云游戏通知验签失败=>参数:{}",dianxinCloudGameNotify);
            return;
        }
        ObjectNode node = mapper.createObjectNode();
        node.put("errMsg",dianxinCloudGameNotify.getErrMsg());
        node.put("mchId",dianxinCloudGameProperties.getMchId());
        node.put("phone",dianxinCloudGameNotify.getPhone());
        node.put("resultCode",dianxinCloudGameNotify.getResultCode());
        node.put("sequenceId",dianxinCloudGameNotify.getSequenceId());
        node.put("subtime",dianxinCloudGameNotify.getSubtime());
        node.put("transData",dianxinCloudGameNotify.getTransData());
        if(!dianxinCloudGameNotify.getSignature().equals(MD5Util.getDianxinCloudGameSign(node,dianxinCloudGameProperties.getKey()))){
            log.error("电信云游戏通知验签失败=>参数:{}",dianxinCloudGameNotify);
            return;
        }
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, dianxinCloudGameNotify.getPhone())
                .eq(Subscribe::getIspOrderNo, dianxinCloudGameNotify.getSequenceId())
                .eq(Subscribe::getChannel, BIZ_CHANNEL_DX_CLOUDGAME)
                .orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            upd.setOpenTime(new Date());
            if (DIANXIN_CLOUDGAME_SUBSCRIBE_STATUS_SUB_SUCCESS.equals(dianxinCloudGameNotify.getResultCode()) && SUBSCRIBE_STATUS_SMS_CODE_SUBMITED.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else{
                //未知错误
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\"开通失败\"}";
                upd.setResult(result);
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
    }


    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url, Object fromValue, String msg, String mobile) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,mobile);
    }

    private String push(String url,String raw,String msg,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},请求参数:{},响应参数:{}",mobile,url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,raw,e);
            return null;
        }
    }
}
