package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.OrderVrbt;
import com.eleven.cms.entity.VrbtZeroOrder;
import com.eleven.cms.mapper.VrbtZeroOrderMapper;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.SubFreeMessage;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.remote.XunfeiJingxianVrbtService;
import com.eleven.cms.service.IMemberService;
import com.eleven.cms.service.IOrderVrbtService;
import com.eleven.cms.service.IVrbtZeroOrderService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.RandomUtils;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.RemoteResult;
import com.eleven.cms.vo.XunfeiVrbtZeroOrderResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.eleven.cms.vo.VrbtCombinResult.VRBT_FUN_STATUS_NONE;

/**
 * @Description: cms_vrbt_zero_order
 * @Author: jeecg-boot
 * @Date:   2022-10-20
 * @Version: V1.0
 */
@Slf4j
@Service
public class VrbtZeroOrderServiceImpl extends ServiceImpl<VrbtZeroOrderMapper, VrbtZeroOrder> implements IVrbtZeroOrderService {
    @Autowired
    XunfeiJingxianVrbtService xunfeiJingxianVrbtService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    IMemberService memberService;
    @Autowired
    IOrderVrbtService orderVrbtService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    private static final String  VRBT_STATUS_SUCCESS="3";
    @Override
    public void vrbtZeroOrderXunfei(Integer fromId, Integer amount, Integer intervalMaxMill) {
        final List<VrbtZeroOrder> vrbtZeroOrderList = this.lambdaQuery()
                .between(VrbtZeroOrder::getId,  fromId,fromId+amount-1)
                .eq(VrbtZeroOrder::getYm, YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getSource,"xunfei")
                .eq(VrbtZeroOrder::getStatus, -1)
                //.last(BizConstant.SQL_LIMIT_ONE)
                .list();
        log.info("视频彩铃包月0元订购(讯飞),数量总计:{}",vrbtZeroOrderList.size());
        for (int i = 0; i < vrbtZeroOrderList.size(); i++) {
            final VrbtZeroOrder order = vrbtZeroOrderList.get(i);
            log.info("视频彩铃包月0元订购(讯飞),当前处理第{}个,手机号:{}",i+1,order.getMobile());

            try {
                handleOrderXunfei(order);
                TimeUnit.MILLISECONDS.sleep(RandomUtils.createRandom(1000,intervalMaxMill));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void vrbtZeroOrder(Integer fromId, Integer amount, Integer intervalMaxMill) {
        final List<VrbtZeroOrder> vrbtZeroOrderList = this.lambdaQuery()
                .between(VrbtZeroOrder::getId,  fromId,fromId+amount-1)
                .eq(VrbtZeroOrder::getYm,  YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM")))
                .eq(VrbtZeroOrder::getSource,"yrjy")
                .eq(VrbtZeroOrder::getStatus, -1)
                .list();
        log.info("视频彩铃包月0元订购,数量总计:{}",vrbtZeroOrderList.size());
        for (int i = 0; i < vrbtZeroOrderList.size(); i++) {
            final VrbtZeroOrder order = vrbtZeroOrderList.get(i);
            log.info("视频彩铃包月0元订购,当前处理第{}个,手机号:{}",i+1,order.getMobile());
            try {
                handleOrder(order);
                TimeUnit.MILLISECONDS.sleep(RandomUtils.createRandom(1000,intervalMaxMill));
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void handleOrder(VrbtZeroOrder order) {
        final String mobile = order.getMobile();
        final RemoteResult statusResult = miguApiService.vrbtStatusQuery(mobile, BizConstant.RECHARGE_VRBT_CHANNEL_ID);
        //排除没有开功能的
        if(!"3".equals(statusResult.getStatus())){
            this.lambdaUpdate().eq(VrbtZeroOrder::getId, order.getId())
                    .set(VrbtZeroOrder::getStatus, 0)
                    .set(VrbtZeroOrder::getResult, "未开通视频彩铃功能")
                    .set(VrbtZeroOrder::getModifyTime, new Date())
                    .update();
           return;
        }
        final FebsResponse febsResponse = memberService.rechargeVrbtRights(mobile, 1);
        final boolean isOK = febsResponse.isOK();
        LambdaUpdateChainWrapper<VrbtZeroOrder> updateChainWrapper = this.lambdaUpdate().eq(VrbtZeroOrder::getId, order.getId())
                .set(VrbtZeroOrder::getStatus, isOK ? 1 : 0)
                .set(VrbtZeroOrder::getResult, febsResponse.get("message"));
        //订购成功的随机设置一首铃音
        if(isOK){
            final String channelCode = order.getChannel();
            //final String isp = MiguApiService.isCentralityChannel(channelCode) ? MobileRegionResult.ISP_DINGYUE : MobileRegionResult.ISP_YIDONG;
            final OrderVrbt vrbt = orderVrbtService.lambdaQuery()
                    .eq(OrderVrbt::getStatus, 1)
                    //.eq(OrderVrbt::getIsp, isp)
                    .eq(OrderVrbt::getSingerName,"014X04G")   //使用014X04G的歌曲
                    .last("ORDER BY RAND() LIMIT 1")
                    .one();
            final String copyrightId = vrbt.getCopyrightId();
            if (StringUtils.isBlank(copyrightId)) {
                log.error("视频彩铃包月0元订购,随机订购一首视频彩铃失败=>id:{},手机号:{},渠道号:{},原因:{}", order.getId(), order.getMobile(), channelCode,OrderVrbtServiceImpl.FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
                updateChainWrapper.set(VrbtZeroOrder::getExtra,OrderVrbtServiceImpl.FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            } else {
                try {TimeUnit.MILLISECONDS.sleep(500L);} catch (InterruptedException e) {e.printStackTrace();}
                log.info("视频彩铃包月0元订购,随机订购一首视频彩铃开始=>id:{},手机号:{},渠道号:{},版权id:{}", order.getId(), order.getMobile(), channelCode, copyrightId);
                final RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                updateChainWrapper
                        .set(VrbtZeroOrder::getCopyrightId,copyrightId)
                        .set(VrbtZeroOrder::getExtra,remoteResult.getResCode()+":"+remoteResult.getResMsg());
            }
        }
        //保存数据
        updateChainWrapper
                .set(VrbtZeroOrder::getModifyTime, new Date())
                .update();

    }

    @Override
    public void handleOrderXunfei(VrbtZeroOrder order) {
        final String mobile = order.getMobile();
        final XunfeiVrbtZeroOrderResult xunfeiVrbtZeroOrderResult = xunfeiJingxianVrbtService.vrbtZeroOrder(mobile);
        final boolean isOK = xunfeiVrbtZeroOrderResult.isOK();

        LambdaUpdateChainWrapper<VrbtZeroOrder> updateChainWrapper = this.lambdaUpdate()
                .eq(VrbtZeroOrder::getId, order.getId())
                .set(VrbtZeroOrder::getStatus, isOK ? 1 : 0)
                .set(VrbtZeroOrder::getResult, xunfeiVrbtZeroOrderResult.getRetcode() + ":" + xunfeiVrbtZeroOrderResult.getDesc());

        //订购成功的随机设置一首铃音
        if(isOK){
            final String channelCode = order.getChannel();
            //通过讯飞提供的接口随机获取一首视频彩铃的版权id
            String copyrightId = null;
            try {
                copyrightId = xunfeiJingxianVrbtService.getRandomVrbtRing();
            } catch (Exception e) {
                  e.printStackTrace();
            }
            if (StringUtils.isBlank(copyrightId)) {
                log.error("视频彩铃包月0元订购(讯飞),随机订购一首视频彩铃失败=>id:{},手机号:{},渠道号:{},原因:{}", order.getId(), order.getMobile(), channelCode,OrderVrbtServiceImpl.FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL);
                updateChainWrapper.set(VrbtZeroOrder::getExtra,OrderVrbtServiceImpl.FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL);
            } else {
                try {TimeUnit.MILLISECONDS.sleep(500L);} catch (InterruptedException e) {e.printStackTrace();}
                log.info("视频彩铃包月0元订购(讯飞),随机订购一首视频彩铃开始=>id:{},手机号:{},渠道号:{},版权id:{}", order.getId(), order.getMobile(), channelCode, copyrightId);
                final RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                updateChainWrapper
                        .set(VrbtZeroOrder::getCopyrightId,copyrightId)
                        .set(VrbtZeroOrder::getExtra,remoteResult.getResCode()+":"+remoteResult.getResMsg());
            }
        }
        //保存数据
        updateChainWrapper
                .set(VrbtZeroOrder::getModifyTime, new Date())
                .update();
    }

    @Override
    public void handleOrderXunfeiTemp(VrbtZeroOrder order) {
        final String mobile = order.getMobile();
        final XunfeiVrbtZeroOrderResult xunfeiVrbtZeroOrderResult = xunfeiJingxianVrbtService.vrbtZeroOrderTemp(mobile);
        final boolean isOK = xunfeiVrbtZeroOrderResult.isOK();

        LambdaUpdateChainWrapper<VrbtZeroOrder> updateChainWrapper = this.lambdaUpdate()
                .eq(VrbtZeroOrder::getId, order.getId())
                .set(VrbtZeroOrder::getStatus, isOK ? 1 : 0)
                .set(VrbtZeroOrder::getResult, xunfeiVrbtZeroOrderResult.getRetcode() + ":" + xunfeiVrbtZeroOrderResult.getDesc());

        //订购成功的随机设置一首铃音
        if(isOK){
            final String channelCode = order.getChannel();
            //通过讯飞提供的接口随机获取一首视频彩铃的版权id
            //String copyrightId = null;
            //try {
            //    copyrightId = xunfeiJingxianVrbtService.getRandomVrbtRingTemp();
            //} catch (Exception e) {
            //    e.printStackTrace();
            //}
            //if (StringUtils.isBlank(copyrightId)) {
            //    log.error("视频彩铃包月0元订购(讯飞),随机订购一首视频彩铃失败=>id:{},手机号:{},渠道号:{},原因:{}", order.getId(), order.getMobile(), channelCode,OrderVrbtServiceImpl.FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL);
            //    updateChainWrapper.set(VrbtZeroOrder::getExtra,OrderVrbtServiceImpl.FAIL_MESSAGE_XUNFEI_FETCH_RING_FAIL);
            //} else {
            //    try {TimeUnit.MILLISECONDS.sleep(200L);} catch (InterruptedException e) {e.printStackTrace();}
            //    log.info("视频彩铃包月0元订购(讯飞),随机订购一首视频彩铃开始=>id:{},手机号:{},渠道号:{},版权id:{}", order.getId(), order.getMobile(), channelCode, copyrightId);
            //    RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
            //    updateChainWrapper
            //            .set(VrbtZeroOrder::getCopyrightId,copyrightId)
            //            .set(VrbtZeroOrder::getExtra,remoteResult.getResCode()+":"+remoteResult.getResMsg());
            //    if(!remoteResult.isOK()){
            //        try {TimeUnit.SECONDS.sleep(1L);} catch (InterruptedException e) {e.printStackTrace();}
            //        remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
            //        updateChainWrapper
            //                .set(VrbtZeroOrder::getCopyrightId,copyrightId)
            //                .set(VrbtZeroOrder::getExtra,remoteResult.getResCode()+":"+remoteResult.getResMsg());
            //    }
            //}

            //本次刷歌使用给定的歌曲
            final OrderVrbt vrbt = orderVrbtService.lambdaQuery()
                    .eq(OrderVrbt::getStatus, 1)
                    //.eq(OrderVrbt::getIsp, isp)
                    .eq(OrderVrbt::getSingerName,channelCode)   //使用014X0F7
                    .last("ORDER BY RAND() LIMIT 1")
                    .one();
            final String copyrightId = vrbt.getCopyrightId();
            if (StringUtils.isBlank(copyrightId)) {
                log.error("视频彩铃包月0元订购(讯飞临时),随机订购一首视频彩铃失败=>id:{},手机号:{},渠道号:{},原因:{}", order.getId(), order.getMobile(), channelCode,OrderVrbtServiceImpl.FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
                updateChainWrapper.set(VrbtZeroOrder::getExtra,OrderVrbtServiceImpl.FAIL_MESSAGE_CHANNEL_VRBT_NOT_CONFIG);
            } else {
                try {TimeUnit.MILLISECONDS.sleep(500L);} catch (InterruptedException e) {e.printStackTrace();}
                log.info("视频彩铃包月0元订购(讯飞临时),随机订购一首视频彩铃开始=>id:{},手机号:{},渠道号:{},版权id:{}", order.getId(), order.getMobile(), channelCode, copyrightId);
                final RemoteResult remoteResult = miguApiService.vrbtToneFreeMonthOrder(mobile, channelCode, copyrightId, MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                updateChainWrapper
                        .set(VrbtZeroOrder::getCopyrightId,copyrightId)
                        .set(VrbtZeroOrder::getExtra,remoteResult.getResCode()+":"+remoteResult.getResMsg());
            }

        }
        //保存数据
        updateChainWrapper
                .set(VrbtZeroOrder::getModifyTime, new Date())
                .update();
    }

    @Override
    public void handleOrderProvinceVrbtFun(VrbtZeroOrder order) {
        final String mobile = order.getMobile();
        final String channelCode = order.getChannel();

        LambdaUpdateChainWrapper<VrbtZeroOrder> updateChainWrapper = this.lambdaUpdate()
                .eq(VrbtZeroOrder::getId, order.getId());

        final String bizType = BizConstant.getBizTypeByMiguChannel(channelCode);
        boolean isMember = BizConstant.BIZ_TYPE_BJHY.equals(bizType) ? miguApiService.bjhyQuery(mobile, channelCode).isBjhyMember() : miguApiService.cpmbQuery(mobile, channelCode).isCpmbMember();

        //有包月切有功能才订购
        if(isMember && !VRBT_FUN_STATUS_NONE.equals(miguApiService.vrbtStatusQuery(mobile, channelCode).getStatus())){
            final RemoteResult remoteResult = miguApiService.provinceBjhyOrder(mobile, false);
            updateChainWrapper
                    .set(VrbtZeroOrder::getStatus, remoteResult.isOK() ? 1 : 0)
                    .set(VrbtZeroOrder::getResult, remoteResult.getResCode()+":"+remoteResult.getResMsg());
        }else {
            log.info("三方支付音乐包四川开视频彩铃功能,用户已退订包月或无视频彩铃功能=>id:{},手机号:{},渠道号:{},isMember:{}", order.getId(), order.getMobile(), channelCode,isMember);
            updateChainWrapper
                    .set(VrbtZeroOrder::getStatus, 0)
                    .set(VrbtZeroOrder::getResult, "用户已退订包月或无视频彩铃功能");
        }
        //保存数据
        updateChainWrapper
                .set(VrbtZeroOrder::getModifyTime, new Date())
                .update();
    }



    /**
     * 更新订购状态并发送免费包月订购视频彩铃消息
     * @param vrbtZeroOrder
     */
    @Transactional
    @Override
    public void updateVrbtZeroOrderStatusMessage(VrbtZeroOrder vrbtZeroOrder){
        this.lambdaUpdate().eq(VrbtZeroOrder::getId, vrbtZeroOrder.getId()).eq(VrbtZeroOrder::getStatus,-2).set(VrbtZeroOrder::getStatus,-1).update();
        rabbitMQMsgSender.sendFreeMonthSubVrbtMessage(SubFreeMessage.builder().id(String.valueOf(vrbtZeroOrder.getId())).mobile(vrbtZeroOrder.getMobile()).build());
    }
    @Override
    public void handleOrder(String mobile,String channel,Integer id,String copyrightId,String contentId,String amount) {
        RemoteResult vrbtInitiative=miguApiService.vrbtInitiativeStatusQuery(mobile,channel);
        if(!vrbtInitiative.isOK() || !VRBT_STATUS_SUCCESS.equals(vrbtInitiative.getVrbtStatus())){
            this.lambdaUpdate().eq(VrbtZeroOrder::getId, id).set(VrbtZeroOrder::getStatus, 0).set(VrbtZeroOrder::getResult, "主叫功能未开通").set(VrbtZeroOrder::getModifyTime, new Date()).update();
            log.info("咪咕查询主叫视频彩铃功能状态=>手机号:{},渠道号:{},主叫功能未开通!-vrbtInitiative:{}",mobile,channel,vrbtInitiative);
            return;
        }
        //开通免费包月功能-1个月
        RemoteResult  vrbtRemoteResult=miguApiService.vrbtZeroOrderTask(mobile,channel,"1");
        final boolean isOK =vrbtRemoteResult.isOK();
        this.lambdaUpdate().eq(VrbtZeroOrder::getId, id).set(VrbtZeroOrder::getStatus, isOK ? 1 : 0).set(VrbtZeroOrder::getResult,vrbtRemoteResult.getResMsg()).set(VrbtZeroOrder::getModifyTime, new Date()).update();
        //订购成功的随机设置一首铃音
        if(isOK){
            //查询是否设置铃音
            boolean isRing=this.lambdaQuery().eq(VrbtZeroOrder::getMobile, mobile).eq(VrbtZeroOrder::getChannel, channel).eq(VrbtZeroOrder::getRingStatus,1).count()>0;
            if (StringUtils.isBlank(copyrightId) || StringUtils.isBlank(contentId) || isRing) {
                String msg="版权id:"+copyrightId+"-内容id:"+contentId+"-isRing:"+isRing;
                log.error("视频彩铃包月0元订购,设置视频彩铃失败=>id:{},手机号:{},渠道号:{},原因:{}",id, mobile, channel,msg);
                this.lambdaUpdate().eq(VrbtZeroOrder::getId, id).set(VrbtZeroOrder::getRingStatus,0).set(VrbtZeroOrder::getExtra,msg).set(VrbtZeroOrder::getModifyTime, new Date()).update();
            }else{
                try {
                    TimeUnit.MILLISECONDS.sleep(500L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                log.info("视频彩铃包月0元订购,随机订购一首视频彩铃开始=>id:{},手机号:{},渠道号:{},版权id:{}",id, mobile, channel, copyrightId);
                RemoteResult remoteResultPolicy=miguApiService.queryVrbtBusinessPolicy(mobile,channel,copyrightId, contentId);
                log.info("视频彩铃包月0元订购,随机订购一查询策略=>id:{},手机号:{},渠道号:{},版权id:{},remoteResultPolicy:{}",id, mobile, channel, copyrightId,remoteResultPolicy);
                RemoteResult  remoteResult=miguApiService.vrbtToneFreeMonthOrder(mobile,channel,copyrightId,contentId,MiguApiService.VRBT_TONE_ORDER_SETFLAG_NONE);
                final boolean isOKRing =remoteResult.isOK();
                this.lambdaUpdate().eq(VrbtZeroOrder::getId, id).set(VrbtZeroOrder::getRingStatus,isOKRing? 1 : 0).set(VrbtZeroOrder::getExtra,remoteResult.getResCode()+":"+remoteResult.getResMsg()).set(VrbtZeroOrder::getModifyTime, new Date()).update();
            }
        }
    }

    /**
     * 免费包月订购视频彩铃消息队列处理
     * @param id
     */
    @Override
    public void freeMonthSubVrbt(String id){
        VrbtZeroOrder vrbtZeroOrder=this.lambdaQuery().eq(VrbtZeroOrder::getId, id).eq(VrbtZeroOrder::getStatus,-1).orderByDesc(VrbtZeroOrder::getCreateTime).last("limit 1").one();
        if(vrbtZeroOrder==null){
            log.info("免费包月订购视频彩铃消息队列处理-订单查询异常-id:{}",id);
            return;
        }
        this.handleOrder(vrbtZeroOrder.getMobile(),vrbtZeroOrder.getChannel(),vrbtZeroOrder.getId(),vrbtZeroOrder.getCopyrightId(),vrbtZeroOrder.getContentId(),String.valueOf(vrbtZeroOrder.getPrice()));
    }
}
