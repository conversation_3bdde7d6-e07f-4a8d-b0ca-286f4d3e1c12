package com.eleven.cms.service.impl;

import com.eleven.cms.service.IHttpRequestService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;

@Slf4j
@Service
public class HttpRequestServiceImpl implements IHttpRequestService {
    @Autowired
    private Environment environment;
    private OkHttpClient client;
    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            //由于okhttp好像没有提供socks设置Authenticator用户名密码接口，因此设置一个全局的Authenticator

            this.client = this.client.newBuilder()
                    //.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)))
                    // .proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", Credentials.basic("httpUsername", "httpPassword")) .build())
                    .proxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(OkHttpClientUtils.proxyHost, OkHttpClientUtils.proxyPort)))
                    .build();
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(System.out::println)).build();
        }
    }





    /**
     * 发起http请求
     */
    @Override
    public String implementHttpGetRequest(String url, ObjectNode dataNode,String msg) {
        Iterator<Map.Entry<String, JsonNode>> jsonNode = dataNode.fields();
        HttpUrl.Builder builder = HttpUrl.parse(url).newBuilder();
        while (jsonNode.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNode.next();
            String key = entry.getKey();
            String value =  entry.getValue().asText();
            //追加表单信息
            builder.addQueryParameter(key, value);
        }
        HttpUrl httpUrl = builder.build();
        return getRequest(httpUrl,msg);
    }
    @Override
    public String implementHttpGetRequest(String url,String msg,String mobile) {
        return getRequest(url,msg,mobile);
    }
    private String getRequest(HttpUrl httpUrl,String msg) {
        log.info(msg+"Get=>请求地址:{}",httpUrl);
        Request request = new Request.Builder().url(httpUrl).build();
        String jsonResp="";
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            jsonResp = response.body().string();
            log.info(msg+"Get=>响应参数: {}",jsonResp);
        } catch (Exception e) {
            log.error(msg+"Get请求异常", e);
        }
        return jsonResp;
    }
    private String getRequest(String httpUrl,String msg,String mobile) {
        Request request = new Request.Builder().url(httpUrl).build();
        String jsonResp="";
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            jsonResp = response.body().string();
            log.info("{}-响应成功,手机号:{},jsonResp: {}",msg,mobile,jsonResp);
        } catch (Exception e) {
            log.error("{}-请求异常,手机号:{},url:{}",msg,mobile,httpUrl, e);
        }
        return jsonResp;
    }


    @Override
    public String postRequest(String url,RequestBody body,String appCode,String apiCode,String transactionId,String aedkId,String signValue,String publiceKey,String msg,String mobile) {
        Request request = new Request.Builder()
                .url(url)
                .addHeader("appCode", appCode)
                .addHeader("apiCode", apiCode)
                .addHeader("transactionId", transactionId)
                .addHeader("aedkId", aedkId + "")
                .addHeader("signValue", signValue)
                .addHeader("publicKey", publiceKey)
                .addHeader("sdkVersion", "sdk.version.2.2")
                .post(body)
                .build();
        String jsonResp="";
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            jsonResp = response.body().string();
            log.info("{}-响应成功,手机号:{},jsonResp: {}",msg,mobile,jsonResp);
        } catch (Exception e) {
            final Buffer buffer = new Buffer();
            try {
                body.writeTo(buffer);
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
            //编码设为UTF-8
            String busiParam = buffer.readString(StandardCharsets.UTF_8);
            log.error("{}-请求异常,手机号:{},header:{},body:{},url:{}",msg,mobile,request.headers().toMultimap(),busiParam,url, e);
        }
        return jsonResp;
    }

}
