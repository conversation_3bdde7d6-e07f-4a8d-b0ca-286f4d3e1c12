package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.WechatCouponConfig;
import com.eleven.cms.mapper.WechatCouponConfigMapper;
import com.eleven.cms.service.IWechatCouponConfigService;
import com.eleven.cms.service.IWxpayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 微信代金券配置
 * @Author: jeecg-boot
 * @Date:   2024-08-09
 * @Version: V1.0
 */
@Slf4j
@Service
public class WechatCouponConfigServiceImpl extends ServiceImpl<WechatCouponConfigMapper, WechatCouponConfig> implements IWechatCouponConfigService {
    @Autowired
    private IWxpayService wxpayService;
    @Autowired
    WxMpNotifyService wxMpNotifyService;
    /**
     * 根据渠道号查询有效的批次号
     * @param channel
     * @return
     */
    @Override
    public WechatCouponConfig queryEffectiveWechatCouponConfig(String channel) {
        List<WechatCouponConfig> configList=this.lambdaQuery().eq(WechatCouponConfig::getChannel, channel).eq(WechatCouponConfig::getIsValid, 1).list();
        if(!configList.isEmpty()){
            for(WechatCouponConfig config:configList){
                boolean effective=wxpayService.stocksIsValid(config);
                if(effective){
                    return config;
                }else{
                    //设置无效
                    this.lambdaUpdate().eq(WechatCouponConfig::getId,config.getId()).set(WechatCouponConfig::getIsValid,0).update();
                }
            }
        }
        log.error("微信发送代金券异常-暂无可用代金券-渠道号:{}",channel);
        wxMpNotifyService.sendAlertMessage("17723327836", channel, "500","暂无可用微信代金券批次号");
        return null;
    }
}
