package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.config.LianLianFenXiaoProperties;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.*;
import com.eleven.cms.mapper.LianlianChargeLogMapper;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.shanxi.AesUtil;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.LianLianFenXiaoCheckCreateOrder;
import com.eleven.cms.vo.LianLianFenXiaoCreateOrder;
import com.eleven.cms.vo.LianLianFenXiaoProductDetail;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 联联分销充值记录
 * @Author: jeecg-boot
 * @Date:   2024-05-14
 * @Version: V1.0
 */
@Slf4j
@Service
public class LianlianChargeLogServiceImpl extends ServiceImpl<LianlianChargeLogMapper, LianlianChargeLog> implements ILianlianChargeLogService {
    @Autowired
    private IBusinessPackService businessPackService;
    @Autowired
    private ILianLianFenXiaoService lianLianFenXiaoService;
    @Autowired
    private LianLianFenXiaoProperties lianLianFenXiaoProperties;
    @Autowired
    private ILianlianProductService lianlianProductService;
    @Autowired
    private IMiguPackService miguPackService;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private IQyclWxpayService wxpayService;

    private ObjectMapper mapper= new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Override
    public List<LianlianChargeLog> findTimeoutSchedule() {
        return this.lambdaQuery()
                .gt(LianlianChargeLog::getScheduledTime, LocalDateTime.now().plusDays(-7))
                .lt(LianlianChargeLog::getScheduledTime, LocalDateTime.now())
                .eq(LianlianChargeLog::getStatus, BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED)
                .eq(LianlianChargeLog::getOrderType, 0)
                .list();
    }


    /**
     * 联联分销权益充值定时任务
     * @param lianlianChargeLog
     */
    @Override
    public void rechargeForSchedule(LianlianChargeLog lianlianChargeLog) {
        log.info("联联分销权益充值定时任务=>手机号:{},权益领取业务ID:{},充值状态:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus());
        try {
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, lianlianChargeLog.getServiceId()).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.info("联联分销权益充值定时任务业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},充值状态:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus());
                return;
            }
            final String serviceApiBeanName =businessPack.getServiceApiBeanName();
            log.info("联联分销权益充值定时任务ApiBean配置=>手机号:{},权益领取业务ID:{},充值状态:{},ApiBean:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus(),serviceApiBeanName);
            if(StringUtils.isNotBlank(serviceApiBeanName)){
                final IBusinessLianLianRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessLianLianRightsSubService.class);
                FebsResponse febsResponse=businessRightsSubService.rechargeForScheduleVerify(lianlianChargeLog);
                if(febsResponse.isOK()){
                    businessRightsSubService.rechargeForSchedule(lianlianChargeLog);
                }
            }
        } catch (Exception e) {
            log.error("联联分销权益充值定时任务异常=>手机号:{},权益领取业务ID:{},充值状态:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus(),e);
        }
    }


    /**
     * 校验订单会员权益是否已发放
     * @param orderId
     * @param packName
     * @param scheduledTime
     * @param itemId
     * @param productId
     * @return
     */
    @Override
    public FebsResponse isRecharge(String orderId, String packName, LocalDateTime scheduledTime, String itemId, String productId){
        //查询该手机号当月所有订单
        List<LianlianChargeLog> chargeLogList=this.lambdaQuery().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getPackName,packName).list();
        //判断当月是否有已充值成功的订单
        boolean hasSuccess = chargeLogList.stream().anyMatch(item-> BizConstant.JUNBO_RECHARGE_STATUS_SUCCESS.equals(item.getStatus()));
        if(hasSuccess){
            return new FebsResponse().repeatRecharge();
        }
        //判断当月是否有预约直充的订单
        Optional<LianlianChargeLog> scheduledOrder=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_SCHEDULED.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(LianlianChargeLog::getCreateTime));
        if(scheduledOrder.isPresent()){
            try {
                LianlianChargeLog junboChargeLog=scheduledOrder.get();
                junboChargeLog.setScheduledTime(Date.from(scheduledTime.atZone(ZoneId.systemDefault()).toInstant()));
                this.updateById(junboChargeLog);

                //判断当月领取权益是否重复
                Optional<LianlianChargeLog> repeatOrder=chargeLogList.stream().filter(item-> itemId.equals(item.getItemId()) && productId.equals(item.getProductId())).collect(Collectors.toList()).stream().max(Comparator.comparing(LianlianChargeLog::getCreateTime));
                if(repeatOrder.isPresent()){
                    return new FebsResponse().readyRecharge(repeatOrder.get().getProductName());
                }else{
                    return new FebsResponse().repeatRecharge();
                }

            } catch (Exception e) {
                return new FebsResponse().notMember();
            }
        }
        //判断当月是否有正在充值的订单
        Optional<LianlianChargeLog> processingOrder=chargeLogList.stream().filter(item-> BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING.equals(item.getStatus())).collect(Collectors.toList()).stream().max(Comparator.comparing(LianlianChargeLog::getCreateTime));
        if(processingOrder.isPresent()){
            return new FebsResponse().fastRecharge(processingOrder.get().getProductName());
        }
        return new FebsResponse().success();
    }
    /**
     * 判断当月权益是否已全部发放
     * @param orderId
     * @return
     */
    @Override
    public boolean monthlyIsRecharge(String orderId, String serviceId) {
        //查询业务包
        List<String> packNameList=miguPackService.lambdaQuery().eq(MiguPack::getServiceId,serviceId).eq(MiguPack::getIsValid,1).list().stream().map(MiguPack::getPackName).collect(Collectors.toList());
        if(packNameList.isEmpty()){
            return false;
        }
        //查询当月权益已领取权益
        int chargeCount=this.lambdaQuery().eq(LianlianChargeLog::getOrderId,orderId).notIn(LianlianChargeLog::getStatus,2,3,5).in(LianlianChargeLog::getPackName,packNameList).count();
        //判断是否已全部领取
        if(chargeCount>=packNameList.size()) {
            return false;
        }
        return true;
    }

    /**
     * 根据订单号查询权益充值记录
     * @param orderId
     * @param packName
     * @return
     */
    @Override
    public List<LianlianChargeLog> findByOrderIdAndPackName(String orderId,String packName){
        return this.lambdaQuery().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getPackName,packName).list();
    }



    @Override
    public LianlianChargeLog taskRechargeForSchedule(LianlianChargeLog lianlianChargeLog) {
        String mobile=lianlianChargeLog.getMobile();
        String orderNo= lianlianChargeLog.getOrderId();
        String productId=lianlianChargeLog.getProductId();
        Integer itemId=Integer.valueOf(lianlianChargeLog.getItemId());
        Integer settlePrice=lianlianChargeLog.getSettlePrice();
        String travelDate=lianlianChargeLog.getTravelDate()!=null?DateUtil.formatSplitTime(DateUtil.dateToLocalDateTime(lianlianChargeLog.getTravelDate())):"";
        String idCard=lianlianChargeLog.getIdCard();
        String customerName=lianlianChargeLog.getCustomerName();
        String customerPhoneNumber=lianlianChargeLog.getMobile();
        String address=lianlianChargeLog.getAddress();
        Integer thirdSalePrice=lianlianChargeLog.getThirdSalePrice();
        String memo=lianlianChargeLog.getMemo();
        Result<?> checkCreateOrderResult=lianLianFenXiaoService.checkCreateOrder( mobile, orderNo, productId, itemId, settlePrice, travelDate, idCard, customerName, customerPhoneNumber, address, thirdSalePrice, memo);
        int status=BizConstant.JUNBO_RECHARGE_STATUS_PROCESSING;
        if(checkCreateOrderResult.isOK()){
            LianLianFenXiaoCheckCreateOrder checkCreateOrder=(LianLianFenXiaoCheckCreateOrder)checkCreateOrderResult.getResult();
            Result<?> createOrderResult=lianLianFenXiaoService.createOrder(mobile,checkCreateOrder.getValidToken(), orderNo, productId, itemId, settlePrice, travelDate, idCard, customerName, customerPhoneNumber, address, thirdSalePrice, memo);
            if(createOrderResult.isOK()){
                LianLianFenXiaoCreateOrder createOrder=(LianLianFenXiaoCreateOrder)createOrderResult.getResult();
                lianlianChargeLog.setChannelOrderId(createOrder.getChannelOrderId());
            }else{
                status=BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
                lianlianChargeLog.setRespMsg(createOrderResult.getMessage());
            }
        }else{
            status=BizConstant.JUNBO_RECHARGE_STATUS_FAIL;
            lianlianChargeLog.setRespMsg(checkCreateOrderResult.getMessage());
        }
        lianlianChargeLog.setStatus(status);
        lianlianChargeLog.setUpdateTime(new Date());
        this.updateById(lianlianChargeLog);
        return lianlianChargeLog;
    }

    @Override
    public LianLianResponse lianlianChargeNotifyLog(LianlianNotifyLog lianlianNotifyLog) {
        String lianlianJson= AesUtil.aesDecrypt(lianlianNotifyLog.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
        log.info("联联分销充值回调-json数据:{}",lianlianJson);
        try {
            LianlianChargeNotifyLog lianlianChargeNotifyLog = mapper.readValue(lianlianJson, LianlianChargeNotifyLog.class);
            LianlianChargeLog lianlianChargeLog= this.lambdaQuery().eq(LianlianChargeLog::getOrderId,lianlianChargeNotifyLog.getThirdOrderId()).orderByDesc(LianlianChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(lianlianChargeLog==null){
                log.error("联联分销充值回调异常,订单不存在-json数据:{}",lianlianJson);
                return new LianLianResponse("","500","订单不存在");
            }
            Result<?> result=lianLianFenXiaoService.queryProductDetailByProductId(lianlianChargeLog.getProductId(), lianlianChargeLog.getItemId());
            if(result.isOK()) {
                LianLianFenXiaoProductDetail productDetail = (LianLianFenXiaoProductDetail) result.getResult();
                lianlianChargeLog.setBookingType(productDetail.getBookingType());
                if(productDetail.getShopList()!=null && !productDetail.getShopList().isEmpty()){
                    lianlianChargeLog.setPhoneNumber(productDetail.getShopList().get(0).getPhoneNumber());
                }

            }
            if(lianlianChargeNotifyLog.getBatchCode()!=null){
                lianlianChargeLog.setBookingUrl(lianlianChargeNotifyLog.getBatchCode().getBookingUrl());
            }
            if(lianlianChargeNotifyLog.getCodeList()!=null && !lianlianChargeNotifyLog.getCodeList().isEmpty()){
                lianlianChargeLog.setCodeImgUrl(lianlianChargeNotifyLog.getCodeList().get(0).getQrCodeImgUrl());
                lianlianChargeLog.setMinOrderNo(lianlianChargeNotifyLog.getCodeList().get(0).getOrderId());
            }
            lianlianChargeLog.setStatus(lianlianChargeNotifyLog.getSendCodeStatus());
            lianlianChargeLog.setUpdateTime(new Date());
            this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,lianlianChargeNotifyLog.getThirdOrderId()).update(lianlianChargeLog);
            updateRechargeState(lianlianChargeLog);
        } catch (JsonProcessingException e) {
            log.error("联联分销充值回调异常-json数据:{}",lianlianJson,e);
            return new LianLianResponse("","500","系统错误");
        }
        return new LianLianResponse("","200","成功");
    }

    @Override
    public LianLianResponse lianlianUseNotifyLog(LianlianNotifyLog lianlianNotifyLog) {
        String lianlianJson= AesUtil.aesDecrypt(lianlianNotifyLog.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
        log.info("联联分销核销回调-json数据:{}",lianlianJson);
        try {
            LianlianUseNotifyLog lianlianUseNotifyLog = mapper.readValue(lianlianJson, LianlianUseNotifyLog.class);
            LianlianChargeLog lianlianChargeLog= this.lambdaQuery().eq(LianlianChargeLog::getOrderId,lianlianUseNotifyLog.getThirdOrderId()).orderByDesc(LianlianChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(lianlianChargeLog==null){
                log.error("联联分销核销回调异常,订单不存在-json数据:{}",lianlianJson);
                return new LianLianResponse("","500","订单不存在");
            }
            lianlianChargeLog.setStatus(4);
            lianlianChargeLog.setCompleteDate(DateUtil.stringToDate(lianlianUseNotifyLog.getCompleteDate()));
            lianlianChargeLog.setUpdateTime(new Date());
            this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,lianlianUseNotifyLog.getThirdOrderId()).update(lianlianChargeLog);
            updateRechargeState(lianlianChargeLog);
        } catch (Exception e) {
            log.error("联联分销核销回调异常-json数据:{}",lianlianJson,e);
            return new LianLianResponse("","500","系统错误");
        }
        return new LianLianResponse("","200","成功");
    }

    @Override
    public LianLianResponse lianlianRefundNotifyLog(LianlianNotifyLog lianlianNotifyLog) {
        String lianlianJson= AesUtil.aesDecrypt(lianlianNotifyLog.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
        log.info("联联分销退款回调-json数据:{}",lianlianJson);
        try {
            LianlianRefundNotifyLog lianlianRefundNotifyLog = mapper.readValue(lianlianJson, LianlianRefundNotifyLog.class);
            LianlianChargeLog lianlianChargeLog= this.lambdaQuery().eq(LianlianChargeLog::getOrderId,lianlianRefundNotifyLog.getThirdOrderId()).orderByDesc(LianlianChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(lianlianChargeLog==null){
                log.error("联联分销退款回调异常,订单不存在-json数据:{}",lianlianJson);
                return new LianLianResponse("","500","订单不存在");
            }
            lianlianChargeLog.setUpdateTime(new Date());
            lianlianChargeLog.setApplyAmount(lianlianRefundNotifyLog.getApplyAmount());
            lianlianChargeLog.setRefundAmount(lianlianRefundNotifyLog.getRefundAmount());
            lianlianChargeLog.setStatus(3);
            this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,lianlianRefundNotifyLog.getThirdOrderId()).update(lianlianChargeLog);
        } catch (JsonProcessingException e) {
            log.error("联联分销退款回调异常-json数据:{}",lianlianJson,e);
            return new LianLianResponse("","500","系统错误");
        }
        return new LianLianResponse("","200","成功");
    }

    @Override
    public LianLianResponse lianlianBookingNotifyLog(LianlianNotifyLog lianlianNotifyLog) {
        String lianlianJson= AesUtil.aesDecrypt(lianlianNotifyLog.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
        log.info("联联分销预约回调-json数据:{}",lianlianJson);
        try {
            LianlianBookingNotifyLog lianlianBookingNotifyLog = mapper.readValue(lianlianJson, LianlianBookingNotifyLog.class);
            LianlianChargeLog lianlianChargeLog= this.lambdaQuery().eq(LianlianChargeLog::getOrderId,lianlianBookingNotifyLog.getThirdOrderId()).orderByDesc(LianlianChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if(lianlianChargeLog==null){
                log.error("联联分销预约回调异常,订单不存在-json数据:{}",lianlianJson);
                return new LianLianResponse("","500","订单不存在");
            }
            lianlianChargeLog.setUpdateTime(new Date());
            lianlianChargeLog.setBookingStatus(lianlianBookingNotifyLog.getStatus());
            lianlianChargeLog.setBookingDate(DateUtil.stringToDate(lianlianBookingNotifyLog.getBookingDate()));
            this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,lianlianBookingNotifyLog.getThirdOrderId()).update(lianlianChargeLog);
        } catch (Exception e) {
            log.error("联联分销预约回调异常-json数据:{}",lianlianJson,e);
            return new LianLianResponse("","500","系统错误");
        }
        return new LianLianResponse("","200","成功");
    }

    @Override
    public LianLianResponse lianlianProductStatusUpdateNotifyLog(LianlianNotifyLog lianlianNotifyLog) {
        String lianlianJson= AesUtil.aesDecrypt(lianlianNotifyLog.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
        log.info("联联分销产品上下架回调-json数据:{}",lianlianJson);
        try {
            LianlianProductStatusUpdateNotifyLog lianlianProductStatusUpdateNotifyLog = mapper.readValue(lianlianJson, LianlianProductStatusUpdateNotifyLog.class);
            lianlianProductStatusUpdateNotifyLog.getItems().forEach(itemId -> {
                lianlianProductService.lambdaUpdate().eq(LianlianProduct::getProductId,lianlianProductStatusUpdateNotifyLog.getProductId()).eq(LianlianProduct::getItemId,itemId).set(LianlianProduct::getProductType,lianlianProductStatusUpdateNotifyLog.getType()).set(LianlianProduct::getUpdateTime,new Date()).update();
            });
        } catch (JsonProcessingException e) {
            log.error("联联分销产品上下架回调异常-json数据:{}",lianlianJson,e);
            return new LianLianResponse("","500","系统错误");
        }
        return new LianLianResponse("","200","成功");
    }

    @Override
    public LianLianResponse lianlianProductUpdateNotifyLog(LianlianNotifyLog lianlianNotifyLog) {
        String lianlianJson= AesUtil.aesDecrypt(lianlianNotifyLog.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
        log.info("联联分销产品更新回调-json数据:{}",lianlianJson);
        try {
            LianlianProductUpdateNotifyLog lianlianProductUpdateNotifyLog = mapper.readValue(lianlianJson, LianlianProductUpdateNotifyLog.class);
            List<LianlianProduct> productList= Lists.newArrayList();
            if(lianlianProductUpdateNotifyLog!=null && lianlianProductUpdateNotifyLog.getItemList()!=null && !lianlianProductUpdateNotifyLog.getItemList().isEmpty()){
                lianlianProductUpdateNotifyLog.getItemList().forEach(item->{
                    LianlianProduct lianlianProduct=new LianlianProduct();
                    lianlianProduct.setOnlyName(lianlianProductUpdateNotifyLog.getOnlyName()+item.getSubTitle());
                    /**封面图*/
                    lianlianProduct.setFaceImg(lianlianProductUpdateNotifyLog.getFaceImg());
                    /**售价(分)*/
                    lianlianProduct.setSalePrice(item.getSalePrice());
                    /**原价(分)*/
                    lianlianProduct.setOriginPrice(item.getOriginPrice());
                    /**渠道结算价(分)*/
                    lianlianProduct.setChannelPrice(item.getChannelPrice());
                    /**产品编码*/
                    lianlianProduct.setProductId(lianlianProductUpdateNotifyLog.getProductId());
                    /**套餐编码*/
                    lianlianProduct.setItemId(item.getItemId());
                    /**售卖数量*/
                    lianlianProduct.setSingleMax(item.getSingleMax());
                    /**库存数量*/
                    lianlianProduct.setStock(item.getStock());
                    /**预约方式 0-无需预约 1-网址预约 2-电话预约。当为 1 时，发码回调会返回预约地址链接 bookingUrl；当为 2 时，电话预约一般会在商品图文详情里面有说明*/
                    lianlianProduct.setBookingType(lianlianProductUpdateNotifyLog.getBookingType());
                    /**是否需要填写配送地址 0-否 1-是*/
                    lianlianProduct.setBookingShowAddress(lianlianProductUpdateNotifyLog.getBookingShowAddress());
                    /**是否需要身份证 0-否 1-是*/
                    lianlianProduct.setOrderShowIdCard(lianlianProductUpdateNotifyLog.getOrderShowIdCard());
                    /**是否需要填写使用日期 0-否 1-是*/
                    lianlianProduct.setOrderShowDate(lianlianProductUpdateNotifyLog.getOrderShowDate());
                    try {
                        /**抢购开始时间*/
                        if(StringUtils.isNotBlank(lianlianProductUpdateNotifyLog.getBeginTime())){
                            lianlianProduct.setBeginTime(DateUtil.stringToDate(lianlianProductUpdateNotifyLog.getBeginTime()));
                        }

                        /**抢购结束时间*/
                        if(StringUtils.isNotBlank(lianlianProductUpdateNotifyLog.getEndTime())){
                            lianlianProduct.setEndTime(DateUtil.stringToDate(lianlianProductUpdateNotifyLog.getEndTime()));
                        }

                        /**购买后——有效开始时间*/
                        if(StringUtils.isNotBlank(lianlianProductUpdateNotifyLog.getValidBeginDate())){
                            lianlianProduct.setValidBeginDate(DateUtil.stringToDate(lianlianProductUpdateNotifyLog.getValidBeginDate()));
                        }

                        /**购买后——有效结束时间*/
                        if(StringUtils.isNotBlank(lianlianProductUpdateNotifyLog.getValidEndDate())){
                            lianlianProduct.setValidEndDate(DateUtil.stringToDate(lianlianProductUpdateNotifyLog.getValidEndDate()));
                        }
                        /**产品上线时间*/
                        if(StringUtils.isNotBlank(lianlianProductUpdateNotifyLog.getReleaseTime())){
                            lianlianProduct.setReleaseTime(DateUtil.stringToDate(lianlianProductUpdateNotifyLog.getReleaseTime()));
                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    /**商城类型: 0 周边游，1 电商产品，需配送（目前只有周边游这一个类型）*/
                    lianlianProduct.setEcommerce(lianlianProductUpdateNotifyLog.getEcommerce());
                    productList.add(lianlianProduct);
                });
            }
            if(productList!=null && !productList.isEmpty()){
                productList.forEach(product->{
                    product.setUpdateTime(new Date());
                    lianlianProductService.lambdaUpdate().eq(LianlianProduct::getProductId,product.getProductId()).eq(LianlianProduct::getItemId,product.getItemId()).update(product);
                });
            }
        } catch (Exception e) {
            log.error("联联分销产品更新回调异常-json数据:{}",lianlianJson,e);
            return new LianLianResponse("","500","系统错误");
        }
        return new LianLianResponse("","200","成功");
    }

    @Override
    public LianLianResponse lianlianShopUpdateNotifyLog(LianlianNotifyLog lianlianNotifyLog) {
        String lianlianJson= AesUtil.aesDecrypt(lianlianNotifyLog.getData().getEncryptedData(),lianLianFenXiaoProperties.getKey());
        log.info("联联分销店铺信息变更回调-json数据:{}",lianlianJson);
        return new LianLianResponse("","200","成功");
    }

    /**
     * 贵州移动省包（在线支付）领取权益
     * @param id
     */
    @Override
    public void guiZhouMobilePayRechargeScheduleDeduct(String id,String orderId) {
        LianlianChargeLog lianlianChargeLog=this.lambdaQuery().eq(LianlianChargeLog::getId,id).eq(LianlianChargeLog::getOrderId,orderId).orderByDesc(LianlianChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(lianlianChargeLog==null){
            log.error("联联领取权益异常-id:{},orderId:{}",id,orderId);
            return;
        }
        //业务类型去重
        final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, lianlianChargeLog.getServiceId()).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(businessPack==null){
            log.info("联联领取权益异常-业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},渠道号:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getChannel());
            return;
        }
        final String serviceApiBeanName =businessPack.getServiceApiBeanName();
        final IBusinessLianLianRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessLianLianRightsSubService.class);
        FebsResponse febsResponse=businessRightsSubService.rechargeForScheduleVerify(lianlianChargeLog);
        if(febsResponse.isOK()){
            businessRightsSubService.rechargeForSchedule(lianlianChargeLog);
        }
    }


    /**
     * 联联分销更新业务充值状态
     * @param lianlianChargeLog
     */
    public void updateRechargeState(LianlianChargeLog lianlianChargeLog)  {
        log.info("联联分销更新业务充值状态=>手机号:{},权益领取业务ID:{},充值状态:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus());
        try {
            final BusinessPack businessPack = businessPackService.lambdaQuery().eq(BusinessPack::getServiceId, lianlianChargeLog.getServiceId()).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
            if (businessPack == null) {
                log.info("联联分销更新业务充值状态业务渠道权益关联未正确配置=>手机号:{},权益领取业务ID:{},充值状态:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus());
                return;
            }
            final String serviceApiBeanName =businessPack.getServiceApiBeanName();
            log.info("联联分销更新业务充值状态ApiBean配置=>手机号:{},权益领取业务ID:{},充值状态:{},ApiBean:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus(),serviceApiBeanName);
            if(StringUtils.isNotBlank(serviceApiBeanName)){
                final IBusinessLianLianRightsSubService businessRightsSubService = SpringContextUtils.getBean(serviceApiBeanName, IBusinessLianLianRightsSubService.class);
                businessRightsSubService.updateRechargeState(lianlianChargeLog);
            }
        } catch (Exception e) {
            log.error("更新业务充值状态异常=>手机号:{},权益领取业务ID:{},充值状态:{}",lianlianChargeLog.getMobile(),lianlianChargeLog.getServiceId(),lianlianChargeLog.getStatus(),e);
        }
    }


    @Override
    public Result<?> wechatRefund(String orderId){
        String refundOrderNo = IdWorker.get32UUID();
        OrderPay orderPay=orderPayService.lambdaQuery().eq(OrderPay::getOrderId,orderId).eq(OrderPay::getStatus,1).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return lianLianRefundCode(orderId);
        }
        Integer totalFee=orderPay.getTotalFee();
        if(totalFee<=0){
            return Result.error("退款金额非法！");
        }

        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatConfig(orderPay.getTradeType(),orderPay.getChannel());
        if(wechatConfigLog==null){
            return Result.error("退款订单暂不支持！");
        }
        Boolean refund=this.lambdaQuery().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getStatus,6).count()>0;
        if(refund){
            return Result.error("订单退款中！");
        }

        if(StringUtils.equalsAny(orderPay.getTradeType(),BizConstant.TRADE_TYPE_WECHAT)){
            FebsResponse febsRefund=wxpayService.wechatRefund(orderId,refundOrderNo, String.valueOf(totalFee), String.valueOf(totalFee),wechatConfigLog.getAppId(),wechatConfigLog.getMchId(),"https://crbt.cdyrjygs.com/cms-vrbt/api/jsapi/wechat/refund/notify");
            if(febsRefund.isOK()){
                orderPayService.lambdaUpdate().eq(OrderPay::getOrderId,orderId).eq(OrderPay::getStatus,1)
                        .set(OrderPay::getStatus,6)
                        .set(OrderPay::getRefundOrderNo,refundOrderNo)
                        .set(OrderPay::getRefundRemark, "正在退款！")
                        .set(OrderPay::getRefundTime, new Date())
                        .set(OrderPay::getRefundAmount, totalFee)
                        .update();

                this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getStatus,1)
                        .set(LianlianChargeLog::getStatus,6)
                        .set(LianlianChargeLog::getRefundOrderNo,refundOrderNo)
                        .set(LianlianChargeLog::getRefundRemark, "正在退款！")
                        .set(LianlianChargeLog::getUpdateTime, new Date())
                        .update();

                return Result.ok("正在退款！");
            }else{
                orderPayService.lambdaUpdate().eq(OrderPay::getOrderId,orderId).eq(OrderPay::getStatus,1)
                        .set(OrderPay::getStatus,5)
                        .set(OrderPay::getRefundOrderNo,refundOrderNo)
                        .set(OrderPay::getRefundRemark, String.valueOf(febsRefund.get("message")))
                        .set(OrderPay::getRefundTime, new Date())
                        .set(OrderPay::getRefundAmount, totalFee)
                        .update();


                this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getStatus,1)
                        .set(LianlianChargeLog::getStatus,5)
                        .set(LianlianChargeLog::getRefundOrderNo,refundOrderNo)
                        .set(LianlianChargeLog::getRefundRemark, String.valueOf(febsRefund.get("message")))
                        .set(LianlianChargeLog::getUpdateTime, new Date())
                        .update();

                return Result.error(String.valueOf(febsRefund.get("message")));
            }
        }

        orderPayService.lambdaUpdate().eq(OrderPay::getOrderId,orderId).eq(OrderPay::getStatus,1)
                .set(OrderPay::getStatus,5)
                .set(OrderPay::getRefundOrderNo,refundOrderNo)
                .set(OrderPay::getRefundRemark, "退款失败！")
                .set(OrderPay::getRefundTime, new Date())
                .set(OrderPay::getRefundAmount, totalFee)
                .update();


        this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getStatus,1)
                .set(LianlianChargeLog::getStatus,5)
                .set(LianlianChargeLog::getRefundOrderNo,refundOrderNo)
                .set(LianlianChargeLog::getRefundRemark, "退款失败！")
                .set(LianlianChargeLog::getUpdateTime, new Date())
                .update();

        return Result.error("退款失败！");
    }


    private Result<?> lianLianRefundCode(String orderId) {
        Boolean refund=this.lambdaQuery().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getStatus,6).count()>0;
        if(refund){
            return Result.error("订单退款中！");
        }

        LianlianChargeLog lianlianChargeLog=this.lambdaQuery().eq(LianlianChargeLog::getOrderId,orderId).orderByDesc(LianlianChargeLog::getCreateTime).last("limit 1").one();
        if(lianlianChargeLog==null){
            log.error("联联分销退款订单异常-订单号:{}",orderId);
            return Result.error("订单不存在！");
        }
        //防止重复提交
        this.lambdaUpdate().eq(LianlianChargeLog::getOrderId,orderId).set(LianlianChargeLog::getStatus,6).set(LianlianChargeLog::getRefundRemark, "正在退款！").set(LianlianChargeLog::getUpdateTime, new Date()).update();

        List<String> orderList= Lists.newArrayList();
        orderList.add(lianlianChargeLog.getMinOrderNo());
        Result<?> result=lianLianFenXiaoService.refundOrder(lianlianChargeLog.getMobile(), lianlianChargeLog.getChannelOrderId(),orderList);
        if(result.isOK()){
            this.lambdaUpdate().eq(LianlianChargeLog::getId,lianlianChargeLog.getId()).eq(LianlianChargeLog::getStatus,6)
                    .set(LianlianChargeLog::getStatus,3)
                    .set(LianlianChargeLog::getRefundRemark, "退款成功！")
                    .set(LianlianChargeLog::getUpdateTime, new Date())
                    .update();
            return Result.ok("退款成功！");
        }else{
            this.lambdaUpdate().eq(LianlianChargeLog::getId,lianlianChargeLog.getId()).eq(LianlianChargeLog::getStatus,6)
                    .set(LianlianChargeLog::getStatus,5)
                    .set(LianlianChargeLog::getRefundRemark, result.getMessage())
                    .set(LianlianChargeLog::getUpdateTime, new Date())
                    .update();
            return Result.error(result.getMessage());
        }
    }
}
