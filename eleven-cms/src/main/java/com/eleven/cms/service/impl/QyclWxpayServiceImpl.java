package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.config.WechatRSAConfig;
import com.eleven.cms.config.QyclDYPayPropertiesConfig;
import com.eleven.cms.config.QyclWXPayPropertiesConfig;
import com.eleven.cms.dto.*;
import com.eleven.cms.entity.MiguVrbtPayOrder;
import com.eleven.cms.entity.OrderPay;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.service.*;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.qycl.entity.QyclOrderPay;
import com.eleven.qycl.entity.QyclOrderPayLog;
import com.eleven.qycl.service.IQyclOrderPayLogService;
import com.eleven.qycl.service.IQyclOrderPayService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.wxpay.sdk.QyclWXPay;
import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.util.UriUtils;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Date: 2020/7/14 16:03
 * Desc:微信支付
 */
@Slf4j
@Service
public class QyclWxpayServiceImpl implements IQyclWxpayService {
    private static final String AUTH_DOMAIN_NAME="cdyrjygs.com";
    private static final String SCENE_INFO="{\"h5_info\": {\"type\":\"Wap\",\"wap_url\": \"${wxpay.authDomainName}\",\"wap_name\": \"信息认证\"}}";
    private static final String WX_NOTIFY_URL="https://crbt.cdyrjygs.com/cms-vrbt/api/mall/wechat/refund/notify";
    private static final String DY_NOTIFY_URL= "https://crbt.cdyrjygs.com/cms-vrbt/api/mall/douyin/refund/notify";
    private static final String REDIRECT_URI="https://bj.cdyrjygs.com/mobile_hk_ch_v5";
    private static final String RESPONSE_TYPE="code";
    private static final String SCOPE="snsapi_userinfo";
    private static final String STATE="STATE#wechat_redirect";
    @Autowired
    QyclDYPayPropertiesConfig dyPayPropertiesConfig;
    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private QyclWXPayPropertiesConfig payConfig;
    @Autowired
    private WechatRSAConfig rsaConfig;
    @Autowired
    private QyclWXPay wxPay;
    @Autowired
    private IQyclOrderPayService qyclOrderPayService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IOrderPayService orderPayService;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    private IWarnMobileService warnMobileService;
    @Autowired
    private IQyclOrderPayLogService orderPayLogService;
    @Autowired
    private IMiguVrbtPayOrderService miguVrbtPayOrderService;

    public static RSAAutoCertificateConfig RSA_CONFIG = null ;
    public static JsapiServiceExtension SERVICE = null ;
    public static ClassPathResource CLASS_PATH_RESOURCE= null ;
    static {
        if(null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)){
            Security.addProvider(new BouncyCastleProvider());
        }

    }

    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }

    /**
     * 支付设置配置文件
     * @param outTradeNo
     * @param tradeType
     * @param businessType
     * @throws Exception
     */
    private void payConfig(String outTradeNo,String tradeType,String businessType)throws Exception{
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatConfig(tradeType,businessType);
        if(wechatConfigLog==null){
            if(StringUtils.isNotEmpty(outTradeNo)){
                log.info("微信支付下单失败:订单号:{}", outTradeNo);
                throw new Exception("支付失败,请稍后再试!");
            }
            log.info("微信配置查询失败:支付类型:{},业务类型:{}", tradeType,businessType);
            throw new Exception("配置错误,请稍后再试!");
        }else{
            payConfig.setAppID(wechatConfigLog.getAppId());
            payConfig.setMchID(wechatConfigLog.getMchId());
            payConfig.setKey(wechatConfigLog.getMchKey());
            payConfig.setNotifyUrl(wechatConfigLog.getNotifyUrl());
            payConfig.setTotalAmount(wechatConfigLog.getTotalAmount());
            payConfig.setSpbillCreateIp(wechatConfigLog.getServerIp());
            payConfig.setAppSecret(wechatConfigLog.getAppSecret());
            payConfig.setPublicName(wechatConfigLog.getPublicName());
        }
    }

    private JsapiServiceExtension JsapiConfig(String outTradeNo,String tradeType,String businessType)throws Exception{
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatConfig(tradeType,businessType);
        if(wechatConfigLog==null){
            if(StringUtils.isNotEmpty(outTradeNo)){
                log.info("微信支付下单失败:订单号:{}", outTradeNo);
                throw new Exception("支付失败,请稍后再试!");
            }
            log.info("微信配置查询失败:支付类型:{},业务类型:{}", tradeType,businessType);
            throw new Exception("配置错误,请稍后再试!");
        }
        return rsaConfig.jsAPIWechatPay(wechatConfigLog);
    }



    /**
     * 回调设置配置文件
     * @param outTradeNo
     * @param appId
     * @param mchId
     * @throws Exception
     */
    private void notifyConfig(String outTradeNo,String appId,String mchId) throws Exception{
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatNotifyConfig(appId,mchId);
        if(wechatConfigLog==null){
            if(StringUtils.isNotEmpty(outTradeNo)){
                log.info("微信支付回调失败:订单号:{}", outTradeNo);
                throw new Exception("回调失败,请稍后再试!");
            }
            log.info("微信配置查询失败:小程序标识:{},商户号:{}", appId,mchId);
            throw new Exception("配置错误,请稍后再试!");
        }else{
            payConfig.setAppID(wechatConfigLog.getAppId());
            payConfig.setMchID(wechatConfigLog.getMchId());
            payConfig.setKey(wechatConfigLog.getMchKey());
            payConfig.setAppSecret(wechatConfigLog.getAppSecret());
        }

    }


    @Override
    public Map<String, String> pay(String outTradeNo, String totalAmount, String subject,String notifyUrl,String tradeType,String openId,String businessType) throws Exception {
        payConfig(outTradeNo,tradeType,businessType);
        if(totalAmount==null){
            Double totalFee=Double.valueOf(payConfig.getTotalAmount());
            totalAmount=BigDecimal.valueOf(totalFee).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
            qyclOrderPayService.lambdaUpdate().eq(QyclOrderPay::getId,outTradeNo)
                    .set(QyclOrderPay::getTradeType,tradeType)
                    .set(QyclOrderPay::getTotalFee,totalFee)
                    .set(QyclOrderPay::getAppId,payConfig.getAppID())
                    .set(QyclOrderPay::getMchId,payConfig.getMchID()).update();
        }
        Map<String, String> data = new HashMap<>();
        data.put("out_trade_no", outTradeNo);
        data.put("body",  StringUtil.isNotEmpty(subject)?subject:payConfig.getPublicName());
        data.put("total_fee", totalAmount);
        data.put("spbill_create_ip", payConfig.getSpbillCreateIp());
        data.put("notify_url", StringUtil.isNotEmpty(notifyUrl)?notifyUrl:payConfig.getNotifyUrl());
        data.put("trade_type", tradeType);
        if(tradeType.equals(BizConstant.TRADE_TYPE_WECHAT)){
            data.put("openid", openId);
        }
        Map<String, String> resp = wxPay.unifiedOrder(data);
        //{nonce_str=vkiSAxfGZZbLTIM2, mweb_url=https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx1511442547333437eef07faa1004983200&package=3430310883, appid=wx8ad568ceec585da2, sign=97B7BD987983C0FB5E37EC219AC494E4B53486271BD2AA68B743B2DECDD7F560, trade_type=MWEB, return_msg=OK, result_code=SUCCESS, mch_id=1565728481, return_code=SUCCESS, prepay_id=wx1511442547333437eef07faa1004983200}
        /*System.out.println("resp = " + resp);*/
        String resultCode = resp.get("result_code");
        if(!StringUtils.equals(resultCode, WXPayConstants.SUCCESS)){
            //更新渠道订单状态
            Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, outTradeNo).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
            if(subscribe != null ){
                ObjectNode result =mapper.createObjectNode();
                if(resp.containsKey("err_code")){
                    result.put("err_code", resp.get("err_code"));
                };
                if(resp.containsKey("return_msg")){
                    result.put("return_msg", resp.get("return_msg"));
                };
                if(resp.containsKey("result_code")){
                    result.put("result_code", resp.get("result_code"));
                };
                if(resp.containsKey("err_code_des")){
                    result.put("err_code_des", resp.get("err_code_des"));
                };
                if(resp.containsKey("return_code")){
                    result.put("return_code", resp.get("return_code"));
                };
                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                subscribe.setResult(result.toString());
                subscribeService.updateSubscribeDbAndEs(subscribe);
                //信息流广告转化上报
                channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
            }
            log.info("微信支付下单失败:订单号:{},下单结果:{}", outTradeNo,resp);
            String returnMsg=resp.containsKey("return_msg")?StringUtils.substring(resp.get("return_msg"),200):"未知错误";
//            aliPayComplainProperties.getWarnMobileList().forEach(mobiles->{
//                smsModelService.sendSms(mobiles,BizConstant.BIZ_TYPE_QYCL,BizConstant.BIZ_QYCL_SERVICE_ID,BizConstant.BUSINESS_TYPE_WARN,businessType+"，警告描述："+returnMsg);
//            });
            warnMobileService.smsWarn("1",BizConstant.BIZ_TYPE_QYCL).forEach(mobiles->{
                smsModelService.sendSms(mobiles,BizConstant.BIZ_TYPE_QYCL,BizConstant.BIZ_QYCL_SERVICE_ID,BizConstant.BUSINESS_TYPE_WARN,businessType+"，警告描述："+returnMsg);
            });
            throw new Exception("支付失败,请稍后再试!");
        }
        if(tradeType.equals(BizConstant.TRADE_TYPE_WECHAT)){
            Map<String, String> payJsAPI = Maps.newHashMap();
            payJsAPI.put("appId",payConfig.getAppID());
            payJsAPI.put("timeStamp",String.valueOf(System.currentTimeMillis()));
            payJsAPI.put("nonceStr", WXPayUtil.generateNonceStr());
            payJsAPI.put("package","prepay_id="+resp.get("prepay_id"));
            payJsAPI.put("signType",WXPayConstants.HMACSHA256);
            payJsAPI.put("paySign",WXPayUtil.generateSignature(payJsAPI, payConfig.getKey(),WXPayConstants.SignType.HMACSHA256));
            return payJsAPI;
        }
        return  resp;
    }
    @Override
    public Map<String, String> dyPay(String outTradeNo, String totalAmount, String subject,String tradeType,String businessType) throws Exception{
        WechatConfigLog payConfig=wechatConfigLogService.getDyConfig(tradeType,businessType);
        if(payConfig==null){
            log.info("抖音支付下单失败,配置文件错误:订单号:{}", outTradeNo);
            throw new Exception("支付失败,请稍后再试!");
        }

        if(totalAmount==null){
            Double totalFee=Double.valueOf(payConfig.getTotalAmount());
            totalAmount=BigDecimal.valueOf(totalFee).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
            qyclOrderPayService.lambdaUpdate().eq(QyclOrderPay::getId,outTradeNo).set(QyclOrderPay::getTradeType,tradeType).set(QyclOrderPay::getTotalFee,totalFee).update();
        }

        //加签验签的参数需要排序
        Map<String, Object> data = new TreeMap<String, Object>();
        //小程序APPID
        data.put("app_id",payConfig.getAppId());
        //开发者侧的订单号。需保证同一小程序下不可重复
        data.put("out_order_no", outTradeNo);
        //支付价格。单位为[分]，取值范围：[1,10000000000]  100元 = 100*100 分
        data.put("total_amount", Integer.valueOf(totalAmount));
        //商品描述。
        data.put("subject", subject);
        //商品详情
        data.put("body", subject);
        //订单过期时间(秒) 5min-2day
        data.put("valid_time", 1800);
        //通知地址
        data.put("notify_url", payConfig.getNotifyUrl());
        //签名，详见https://microapp.bytedance.com/docs/zh-CN/mini-app/develop/server/ecpay/TE
        String sign = TTPayUtil.getSign(data,payConfig.getMchKey());
        data.put("sign", sign);
        final ObjectNode objectNode = mapper.readValue(implementHttpPostResult(dyPayPropertiesConfig.getPayUrl(), data,"抖音小程序支付"), ObjectNode.class);

        String errNo = objectNode.get("err_no").asText();
        if(!"0".equals(errNo)){
            log.info("抖音支付下单失败,参数错误:订单号:{},下单结果:{}", outTradeNo,objectNode);
            throw new Exception("支付失败,请稍后再试!");
        }
        String objectData = objectNode.get("data").toString();
        Map<String, String> resp = Maps.newHashMap();
        resp.put("pay_json",objectData);
        return resp;

    }
    /**
     * 微信支付回调（平台-->用户）,并返回接收消息成功
     * @return   最终此次请求需要返回以下两种
     * String succMsg = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
     * String errMsg = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
     */
    @Override
    public QyclWxpayNotifyParam payResultNotify(String resultNotifyXml) throws Exception {
        log.info("微信支付交易结果通知xml:{}", resultNotifyXml);
        //该方法在支付成功时已验证签名


        Map<String, String> respData = WXPayUtil.xmlToMap(resultNotifyXml);
        notifyConfig(respData.get("out_trade_no"),respData.get("appid"),respData.get("mch_id"));
        Map<String, String> payResultMap = wxPay.processResponseXml(resultNotifyXml);
        QyclWxpayNotifyParam notifyParam = mapper.convertValue(payResultMap, QyclWxpayNotifyParam.class);
        //String trade_status = notifyParam.getTradeStatus();
        // 支付成功
        //if (trade_status.equals(AliPayConfig.AlipayTradeStatus.TRADE_SUCCESS) || trade_status.equals(
        //        AliPayConfig.AlipayTradeStatus.TRADE_FINISHED)) {
        //}
        //需要做状态判断 防止重复收到回调！！！
        log.info("微信支付订单ID={}已收到支付回调，付款成功", notifyParam.getOutTradeNo());

        return notifyParam;

    }

//    /**
//     * 获取code
//     * @param redirectUrl
//     * @return
//     */
//    @Override
//    public String weChatCode(String redirectUrl,String businessType){
//        return code(redirectUrl,businessType);
//    }
//
//    private String code(String redirectUrl,String businessType){
//        payConfig(null,BizConstant.TRADE_TYPE_WECHAT,businessType);
//
//        ObjectNode code =mapper.createObjectNode();
//        redirectUrl=StringUtils.isNotBlank(redirectUrl)?redirectUrl: UriUtils.encode(REDIRECT_URI, StandardCharsets.UTF_8);
//        code.put("appid",payConfig.getAppID());
//        code.put("redirect_uri",redirectUrl);
//        code.put("response_type",RESPONSE_TYPE);
//        code.put("scope",SCOPE);
//        code.put("state",STATE);
//        try {
//            return implementHttpGetResult(WXPayConstants.WECHAT_CODE_URL, code,"获取Code");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

    /**
     * 微信小程序获取openId
     * @param openId
     * @param code
     * @return
     */
    @Override
    public FebsResponse weChatAppletsOpenId(String openId,String code,String businessType) throws Exception{
        ObjectNode auth =mapper.createObjectNode();
        payConfig(null,BizConstant.TRADE_TYPE_WECHAT,businessType);
        auth.put("appid",payConfig.getAppID());
        auth.put("secret",payConfig.getAppSecret());
        auth.put("js_code",code);
        auth.put("grant_type","authorization_code");
        try {
            final ObjectNode objectNode = mapper.readValue(implementHttpGetResult(WXPayConstants.WECHAT_APPLETS_OPEN_ID_URL, auth,"微信小程序获取openId"), ObjectNode.class);
            return new FebsResponse().success().data(objectNode);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();

    }

    @Override
    public String getWeChatOpenId(String code, String businessType) throws Exception {
        WechatConfigLog wechatConfigLog = wechatConfigLogService.lambdaQuery()
                .eq(WechatConfigLog::getIsValid, BizConstant.IS_VALID)
                .eq(WechatConfigLog::getBusinessType, businessType).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
        if (wechatConfigLog == null) {
            log.info("获取微信配置为空:{}", businessType);
            return null;
        }
        ObjectNode auth =mapper.createObjectNode();
        payConfig(null,BizConstant.TRADE_TYPE_WECHAT,businessType);
        auth.put("appid",payConfig.getAppID());
        auth.put("secret",payConfig.getAppSecret());
        auth.put("js_code",code);
        auth.put("grant_type","authorization_code");
        return implementHttpGetResult(WXPayConstants.WECHAT_APPLETS_OPEN_ID_URL, auth, "微信小程序获取openId");
    }

    /**
     * 抖音小程序获取openId
     * @param openId
     * @param code
     * @return
     */
    @Override
    public FebsResponse tiktokAppletsOpenId(String openId,String code,String anonymousCode,String tradeType,String businessType)throws Exception{
        ObjectNode auth =mapper.createObjectNode();
        WechatConfigLog payConfig=wechatConfigLogService.getDyConfig(tradeType,businessType);
        if(payConfig==null){
            log.info("抖音配置查询失败:授权码:{}", code);
            throw new Exception("配置错误,请稍后再试!");
        }
        wechatConfigLogService.getDyToken(payConfig.getAppId(),payConfig.getAppSecret());
        auth.put("appid",payConfig.getAppId());
        auth.put("secret",payConfig.getAppSecret());
        auth.put("code",code);
        auth.put("anonymous_code",anonymousCode);
        try {
            final ObjectNode objectNode = mapper.readValue(implementHttpPostResult(dyPayPropertiesConfig.getOpenIdUrl(), auth,"抖音小程序获取openId"), ObjectNode.class);
            return new FebsResponse().success().data(objectNode);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();

    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_WECHAT_OAUTH,key = "#openId + '-' + #code + '-' + #businessType" ,unless = "#result==null")
    public FebsResponse weChatAuth(String openId,String code,String businessType) throws Exception {
        payConfig(null,BizConstant.TRADE_TYPE_WECHAT,businessType);
        ObjectNode auth =mapper.createObjectNode();
        auth.put("appid",payConfig.getAppID());
        auth.put("secret",payConfig.getAppSecret());
        auth.put("grant_type","authorization_code");
        auth.put("code",code);
       try {
        final JsonNode jsonNode = mapper.readValue(implementHttpGetResult(WXPayConstants.WECHAT_ACCESS_TOKEN_URL, auth,"获取access_token"), JsonNode.class);
        String openid = jsonNode.at("/openid").asText("");
        if(StringUtils.isNotBlank(openid)){
            return new FebsResponse().success().data(openid);
        }
           return new FebsResponse().fail();
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();
    }


    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_WECHAT_TOKEN,key = "#businessType",unless = "#result==null")
    public FebsResponse wechatToken(String businessType)throws Exception{
        ObjectNode token =mapper.createObjectNode();
        payConfig(null,BizConstant.TRADE_TYPE_WECHAT,businessType);
        token.put("appid",payConfig.getAppID());
        token.put("secret",payConfig.getAppSecret());
        token.put("grant_type","client_credential");
        try {
            final ObjectNode objectNode = mapper.readValue(implementHttpGetResult(WXPayConstants.WECHAT_TOKEN_URL, token,"获取token"), ObjectNode.class);
            return new FebsResponse().success().data(objectNode);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();
    }

    @Override
    public FebsResponse weChatScheme(String accessToken,String ipAddr,String query){
        ObjectNode info =mapper.createObjectNode();
        //到期失效的 scheme 码失效类型，失效时间：0，失效间隔天数：1
        info.put("expire_type","1");
        info.put("expire_interval","10");
        ObjectNode jump_wxa =mapper.createObjectNode();
        jump_wxa.put("query",query);
        info.putPOJO("jump_wxa",jump_wxa);
        try {
            final ObjectNode objectNode = mapper.readValue(implementHttpPostResult(WXPayConstants.WECHAT_GENERATESCHEME_URL+"?access_token=" + accessToken, info,"获取小程序scheme码，"+ipAddr), ObjectNode.class);
            return new FebsResponse().success().data(objectNode);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new FebsResponse().fail();
    }




//    @Override
//    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_WECHAT_INFO,key = "#openId + '-' + #accessToken" ,unless = "#result==null")
//    public FebsResponse weChatInfo(String openId,String accessToken) {
//        ObjectNode info =mapper.createObjectNode();
//        info.put("openid",openId);
//        info.put("access_token",accessToken);
//        info.put("lang","zh_CN");
//        try {
//            final ObjectNode objectNode = mapper.readValue(implementHttpGetResult(WXPayConstants.WECHAT_USER_INFO_URL, info,"获取userinfo"), ObjectNode.class);
//            return new FebsResponse().success().data(objectNode);
//        } catch (JsonProcessingException e) {
//            e.printStackTrace();
//        }
//        return new FebsResponse().fail();
//    }
    /**
     * 发起http请求
     */
    public  String implementHttpGetResult(String url, ObjectNode oauth, String msg) {
        StringBuffer valueStr = new StringBuffer();
        if(oauth.has("appid") && oauth.has("secret") && oauth.has("grant_type") && oauth.has("js_code") ){
            String appid=oauth.get("appid").asText();
            String secret=oauth.get("secret").asText();
            String js_code=oauth.get("js_code").asText();
            String grant_type=oauth.get("grant_type").asText();
            valueStr.append("?appid=" + appid);
            valueStr.append("&secret=" + secret);
            valueStr.append("&js_code=" + js_code);
            valueStr.append("&grant_type=" + grant_type);
        }else if(oauth.has("openid") && oauth.has("access_token") && oauth.has("lang")){
            String openid=oauth.get("openid").asText();
            String accessToken=oauth.get("access_token").asText();
            String lang=oauth.get("lang").asText();
            valueStr.append("?openid=" + openid);
            valueStr.append("&access_token=" + accessToken);
            valueStr.append("&lang=" + lang);
        }else if(oauth.has("appid") && oauth.has("secret") && oauth.has("grant_type") && oauth.has("code")){
            String secret=oauth.get("secret").asText();
            String grantType=oauth.get("grant_type").asText();
            String code=oauth.get("code").asText();
            String appid=oauth.get("appid").asText();
            valueStr.append("?appid=" + appid);
            valueStr.append("&secret=" + secret);
            valueStr.append("&code=" + code);
            valueStr.append("&grant_type=" + grantType);
        }else if(oauth.has("appid") && oauth.has("response_type") &&  oauth.has("redirect_uri") && oauth.has("scope") && oauth.has("state")){
            String redirect_uri=oauth.get("redirect_uri").asText();
            String response_type=oauth.get("response_type").asText();
            String appid=oauth.get("appid").asText();
            String scope=oauth.get("scope").asText();
            String state=oauth.get("state").asText();
            valueStr.append("?appid=" + appid);
            valueStr.append("&redirect_uri=" + redirect_uri);
            valueStr.append("&response_type=" + response_type);
            valueStr.append("&scope=" + scope);
            valueStr.append("&state=" + state);
            return url+valueStr.toString();
        }else if(oauth.has("appid") && oauth.has("secret") && oauth.has("grant_type")){
            String appid=oauth.get("appid").asText();
            String secret=oauth.get("secret").asText();
            String grantType=oauth.get("grant_type").asText();
            valueStr.append("?appid=" + appid);
            valueStr.append("&secret=" + secret);
            valueStr.append("&grant_type=" + grantType);
        }else if(oauth.has("appid") && oauth.has("grant_type") && oauth.has("refresh_token")){
            String appid=oauth.get("appid").asText();
            String grantType=oauth.get("grant_type").asText();
            String refreshToken=oauth.get("refresh_token").asText();
            valueStr.append("?appid=" + appid);
            valueStr.append("&grant_type=" + grantType);
            valueStr.append("&refresh_token=" + refreshToken);
        }else if(oauth.has("access_token") && oauth.has("openid")){
            String accessToken=oauth.get("access_token").asText();
            String openid=oauth.get("openid").asText();
            valueStr.append("?access_token=" + accessToken);
            valueStr.append("&openid=" + openid);
        }
        url = url + valueStr.toString();
        return pushGet(url,msg);
    }


    private String pushGet(String url,String msg) {
        log.info(msg+",请求数据=>地址:{}",url);
        Request request = new Request.Builder().url(url).get().build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},响应参数:{}",url,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{}",url,e);
            return null;
        }
    }

    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url, Object fromValue,String msg) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg);
    }
    public String push(String url,String raw,String msg) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }

    @Override
    public FebsResponse wechatRefund(String outTradeNo,String outRefundNo,String refundAmount,String totalAmount,String appId,String mchId){
        Map<String, String> refundNode = Maps.newHashMap();
        refundNode.put("appid",appId);
        refundNode.put("mch_id",mchId);
        refundNode.put("nonce_str",WXPayUtil.generateNonceStr());
        refundNode.put("out_trade_no",outTradeNo);
        refundNode.put("out_refund_no",outRefundNo);
        refundNode.put("total_fee",totalAmount);
        refundNode.put("refund_fee",refundAmount);
        refundNode.put("refund_fee_type","CNY");
        refundNode.put("notify_url",WX_NOTIFY_URL);
        try{
            WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatNotifyConfig(appId,mchId);
            refundNode.put("sign", WXPayUtil.generateSignature(refundNode, wechatConfigLog.getMchKey()));
            String xmlParam = WXPayUtil.generateSignedXml(refundNode, wechatConfigLog.getMchKey());
            Map<String, String> resp = wxPay.refund(xmlParam, mchId);
            String returnCode = resp.get("return_code");
            String resultCode = resp.get("result_code");
            String errCodeDes = resp.get("err_code_des");
            log.info("微信支付退款:订单号:{},退款结果:{}", outTradeNo,resp);
            if(WXPayConstants.SUCCESS.equals(returnCode) && WXPayConstants.SUCCESS.equals(resultCode)){
                return new FebsResponse().success(errCodeDes);
            }
            return new FebsResponse().fail(errCodeDes);
        } catch (Exception e) {
            e.printStackTrace();
            return new FebsResponse().fail("退款失败！");
        }

    }
    @Override
    public FebsResponse dyRefund(String outTradeNo,String outRefundNo,String refundAmount,String appId,String mchId) throws Exception{
        Map<String, Object> refundNode = Maps.newHashMap();
        refundNode.put("app_id",appId);
        refundNode.put("out_order_no",outTradeNo);
        refundNode.put("out_refund_no",outRefundNo);
        refundNode.put("reason","申请退款");
        refundNode.put("refund_amount",Integer.valueOf(refundAmount));
        refundNode.put("refund_fee_type","CNY");
        refundNode.put("notify_url",DY_NOTIFY_URL);
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatNotifyConfig(appId,mchId);
        String sign = TTPayUtil.getSign(refundNode,wechatConfigLog.getMchKey());
        refundNode.put("sign", sign);
        final ObjectNode objectNode = mapper.readValue(implementHttpPostResult(dyPayPropertiesConfig.getRefundUrl(), refundNode,"抖音小程序退款"), ObjectNode.class);
        String errNo = objectNode.get("err_no").asText();
        if(!"0".equals(errNo)){
            log.info("抖音支付退款失败,参数错误:订单号:{},下单结果:{}", outTradeNo,objectNode);
            return new FebsResponse().fail();
        }
        return new FebsResponse().success().data(objectNode);
    }
    /**
     * 微信退款回调（平台-->用户）,并返回接收消息成功
     * @return   最终此次请求需要返回以下两种
     * String succMsg = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
     * String errMsg = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml>";
     */
    @Override
    public WxInfoNotifyParam refundResultNotify(String resultNotifyXml){
        log.info("微信退款交易结果通知xml:{}", resultNotifyXml);
        //该方法在支付成功时已验证签名
        try {
            Map<String, String> refundResultMap = com.github.wxpay.sdk.WXPayUtil.xmlToMap(resultNotifyXml);
            WxrefundNotifyParam notifyParam = mapper.convertValue(refundResultMap, WxrefundNotifyParam.class);
            final String resultCode = notifyParam.getReturnCode();
            boolean isPaySucc = StringUtils.equals(resultCode, com.github.wxpay.sdk.WXPayConstants.SUCCESS);
            if(isPaySucc){
                WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatNotifyConfig(notifyParam.getAppId(),notifyParam.getMchId());

                String reqInfo=getRefundDecrypt(notifyParam.getReqInfo(),wechatConfigLog.getMchKey());
                Map<String, String> reqInfoMap = com.github.wxpay.sdk.WXPayUtil.xmlToMap(reqInfo);
                WxInfoNotifyParam infoParam = mapper.convertValue(reqInfoMap, WxInfoNotifyParam.class);
                log.info("微信退款订单,已收到退款回调infoParam:{}",infoParam);
                return infoParam;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }
    private String getRefundDecrypt(String reqInfoSecret,String mchKey) {
        String result = "";
        try {
            //Security.addProvider(new BouncyCastleProvider());
            byte[] bt = Base64.getDecoder().decode(reqInfoSecret);
            String md5key = DigestUtils.md5DigestAsHex((mchKey).getBytes(StandardCharsets.UTF_8)).toLowerCase();
            SecretKey secretKey = new SecretKeySpec(md5key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding", "BC");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] resultbt = cipher.doFinal(bt);
            result = new String(resultbt);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public Pair<Boolean, DypayNotifyMsg> parsePaymentCallback(String raw){
        try {
            final ObjectNode  tree = mapper.readValue(raw, ObjectNode.class);
            String msgSignature = tree.get("msg_signature").asText();
            String msg = tree.get("msg").asText();
            final DypayNotifyMsg douyinPayNotify = mapper.readValue(msg, DypayNotifyMsg.class);
            WechatConfigLog payConfig=wechatConfigLogService.getDyNotifyConfig(douyinPayNotify.getAppid());
            List<String> sortedString = new ArrayList<>();
            sortedString.add(payConfig.getMchId());
            tree.fields().forEachRemaining(entry -> {
                String feildName = entry.getKey();
                if (!"msg_signature".equals(feildName) && !"type".equals(feildName)) {
                    String feildValue = entry.getValue().textValue();
                    sortedString.add(feildValue);
                }
            });
            final String concat = sortedString.stream().sorted().collect(Collectors.joining(""));
            final String sign = org.apache.commons.codec.digest.DigestUtils.sha1Hex(concat.getBytes(StandardCharsets.UTF_8));
            return Pair.of(StringUtils.equals(msgSignature, sign), douyinPayNotify);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Pair<Boolean, DypayRefundMsg> parseRefundCallback(String raw) throws JsonProcessingException {

        final JsonNode tree = mapper.readTree(raw);
        String msgSignature = tree.at("/msg_signature").asText();
        String msg = tree.at("/msg").textValue();
        List<String> sortedString = new ArrayList<>();
        sortedString.add(dyPayPropertiesConfig.getPayNotifyToken());
        tree.fields().forEachRemaining(entry -> {
            String feildName = entry.getKey();
            if (!"msg_signature".equals(feildName) && !"type".equals(feildName)) {
                String feildValue = entry.getValue().textValue();
                sortedString.add(feildValue);
            }
        });
        final String concat = sortedString.stream().sorted().collect(Collectors.joining(""));
        final String sign = org.apache.commons.codec.digest.DigestUtils.sha1Hex(concat.getBytes(StandardCharsets.UTF_8));
        final DypayRefundMsg douyinRefundNotify = mapper.readValue(msg, DypayRefundMsg.class);
        return Pair.of(StringUtils.equals(msgSignature, sign), douyinRefundNotify);
    }
    @Override
    public FebsResponse wechatQueryComplainDetail(String complaintId,String mchId){
        try {
            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String response = wxPay.queryComplainDetail(complaintId,wechatConfigLog.getMchSerialNo(),wechatConfigLog.getMchId(),wechatConfigLog.getApiKey());
            log.info("查询微信投诉单详情=>投诉单号:{},商户号:{},返回结果:{}", complaintId,mchId,response);
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                 code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                 message = objectNode.at("/message").asText();
            }
            if(StringUtils.equals("200",code)){
                final WechatComplainDetail wechatComplainDetail = mapper.readValue(response, WechatComplainDetail.class);
                return new FebsResponse().code(code).message(message).data(wechatComplainDetail);
            }
            return new FebsResponse().code(code).message(message);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询微信投诉单详情异常=>投诉单号:{},商户号:{}", complaintId,mchId,e);
        }
        return new FebsResponse().fail().message("查询投诉单详情失败");
    }


    @Override
    public FebsResponse createComplainUrl(String url,String mchId){


        try {
            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            ObjectNode info =mapper.createObjectNode();
            //到期失效的 scheme 码失效类型，失效时间：0，失效间隔天数：1
            info.put("url",url);
            String response=wxPay.createComplain(info.toString() ,wechatConfigLog.getMchSerialNo(),wechatConfigLog.getMchId(),wechatConfigLog.getApiKey());
            log.info("创建微信投诉通知回调地址=>地址:{},商户号:{},返回结果:{}", url,mchId,response);
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                message = objectNode.at("/message").asText();
            }
            return new FebsResponse().code(code).message(message).data(objectNode);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建微信投诉通知回调地址异常=>地址:{},商户号:{}", url,mchId,e);
        }
        return new FebsResponse().fail().message("创建投诉通知回调地址失败");
    }



    @Override
    public FebsResponse queryComplainUrl(String mchId){
        try {

            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String response=wxPay.queryComplain(wechatConfigLog.getMchSerialNo(),wechatConfigLog.getMchId(),wechatConfigLog.getApiKey());
            log.info("查询微信投诉通知回调地址=>地址:{},返回结果:{}",mchId,response);
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                message = objectNode.at("/message").asText();
            }
            return new FebsResponse().code(code).message(message).data(objectNode);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询微信投诉通知回调地址异常=>商户号:{}",mchId,e);
        }
        return new FebsResponse().fail().message("查询投诉通知回调地址失败");
    }



    @Override
    public FebsResponse deleteComplainUrl(String mchId){
        try {
            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String response=wxPay.deleteComplain(wechatConfigLog.getMchSerialNo(),wechatConfigLog.getMchId(),wechatConfigLog.getApiKey());
            log.info("删除微信投诉通知回调地址=>商户号:{},返回结果:{}",mchId,response);
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                message = objectNode.at("/message").asText();
            }
            return new FebsResponse().code(code).message(message).data(objectNode);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("删除微信投诉通知回调地址异常=>商户号:{}",mchId,e);
        }
        return new FebsResponse().fail().message("删除投诉通知回调地址失败");
    }



    @Override
    public FebsResponse updateComplainUrl(String url,String mchId){
        try {
            ObjectNode info =mapper.createObjectNode();
            //到期失效的 scheme 码失效类型，失效时间：0，失效间隔天数：1
            info.put("url",url);
            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String response=wxPay.updateComplain(info.toString(),wechatConfigLog.getMchSerialNo(),wechatConfigLog.getMchId(),wechatConfigLog.getApiKey());
            log.info("更新微信投诉通知回调地址=>地址:{},商户号:{},返回结果:{}", url,mchId,response);
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                message = objectNode.at("/message").asText();
            }
            return new FebsResponse().code(code).message(message).data(objectNode);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("更新微信投诉通知回调地址异常=>地址:{},商户号:{}", url,mchId,e);
        }
        return new FebsResponse().fail().message("更新投诉通知回调地址失败");
    }

    /**
     * 微信处理交易投诉
     * @param complaintId
     * @param mchId
     * @param responseContent
     * @param responseImages
     * @param jumpUrl
     * @param jumpUrlText
     * @return
     */
    @Override
    public FebsResponse wechatSolveComplain(String complaintId, String mchId, String responseContent, String responseImages, String jumpUrl, String jumpUrlText){
        try {
            ObjectNode info =mapper.createObjectNode();
            //到期失效的 scheme 码失效类型，失效时间：0，失效间隔天数：1
            info.put("complainted_mchid",mchId);
            info.put("response_content",responseContent);
            if(StringUtils.isNotBlank(responseImages) && !StringUtils.equals("undefined",responseImages)){
                List<String> response_images=Arrays.asList(responseImages.split(","));
                if(response_images!=null && response_images.size()>0){
                    info.putPOJO("response_images",response_images);
                }
            }
            if(StringUtils.isNotBlank(jumpUrl) && !StringUtils.equals("undefined",jumpUrl)){
                info.put("jump_url",jumpUrl);
            }

            if(StringUtils.isNotBlank(jumpUrlText) && !StringUtils.equals("undefined",jumpUrlText)){
                info.put("jump_url_text",jumpUrlText);
            }

            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String response=wxPay.solveComplain(info.toString(),complaintId,wechatConfigLog.getMchSerialNo(),wechatConfigLog.getMchId(),wechatConfigLog.getApiKey());
            log.info("处理微信交易投诉=>投诉单号:{},商户号:{},返回结果:{}", complaintId,mchId,response);
            if(response==null){
                return new FebsResponse().success();
            }
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                message = objectNode.at("/message").asText();
            }
            return new FebsResponse().code(code).message(message).data(objectNode);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理微信交易投诉异常=>投诉单号:{},商户号:{}", complaintId,mchId,e);
        }
        return new FebsResponse().fail().message("处理微信交易投诉失败");
    }


    @Override
    public FebsResponse queryComplainList(Integer limit, Integer offset, String mchId, String beginDate, String endDate){
        try {
            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String response=wxPay.queryComplainList( limit,  offset,  beginDate,  endDate, wechatConfigLog.getMchSerialNo(), mchId, wechatConfigLog.getApiKey());
            log.info("查询微信投诉列表=>商户号:{},返回结果:{}",mchId,response);
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                message = objectNode.at("/message").asText();
            }

            if(StringUtils.equals("200",code)){
                final WechatComplainResponse wechatComplainResponse = mapper.readValue(response, WechatComplainResponse.class);
                return new FebsResponse().code(code).message(message).data(wechatComplainResponse);
            }
            return new FebsResponse().code(code).message(message);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询微信投诉列表异常=>商户号:{}",mchId,e);
        }
        return new FebsResponse().fail().message("查询微信投诉列表失败");
    }


    /**
     * 反馈投诉处理完成
     * @param complaintId
     * @param mchId
     * @return
     */
    @Override
    public FebsResponse feedbackOverComplain(String complaintId, String mchId){
        try {
            ObjectNode info =mapper.createObjectNode();
            info.put("complainted_mchid",mchId);
            WechatConfigLog wechatConfigLog=wechatConfigLogService.lambdaQuery().eq(WechatConfigLog::getMchId,mchId).orderByDesc(WechatConfigLog::getCreateTime).last("limit 1").one();
            String response=wxPay.feedbackOverComplain(complaintId,info.toString(),wechatConfigLog.getMchSerialNo(),wechatConfigLog.getMchId(),wechatConfigLog.getApiKey());
            log.info("反馈投诉处理完成=>投诉单号:{},商户号:{},返回结果:{}", complaintId,mchId,response);
            if(response==null){
                return new FebsResponse().success();
            }
            final ObjectNode objectNode = mapper.readValue(response, ObjectNode.class);
            String code= "200";
            String message = "成功";
            if(objectNode.has("code")){
                code = objectNode.at("/code").asText();
            }
            if(objectNode.has("message")){
                message = objectNode.at("/message").asText();
            }
            return new FebsResponse().code(code).message(message).data(objectNode);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("反馈投诉处理完成异常=>投诉单号:{},商户号:{}", complaintId,mchId,e);
        }
        return new FebsResponse().fail().message("反馈投诉处理完成失败");
    }
    @Override
    public Map<String, String> shopPay(String outTradeNo, String totalAmount, String productName,String notifyUrl,String tradeType,String openId,String businessType) throws Exception {
        if(tradeType.equals(BizConstant.TRADE_TYPE_WECHAT)){
            JsapiServiceExtension service=this.JsapiConfig(outTradeNo,tradeType,businessType);
            PrepayRequest request = new PrepayRequest();
            // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
            // 调用接口
            request.setAppid(rsaConfig.getAppId());
            request.setMchid(rsaConfig.getMchId());
            request.setDescription(productName);
            request.setOutTradeNo(outTradeNo);
            request.setNotifyUrl(notifyUrl);
            Amount amount=new Amount();
            amount.setTotal(Integer.valueOf(totalAmount));
            amount.setCurrency("CNY");
            request.setAmount(amount);
            Payer payer=new Payer();
            payer.setOpenid(openId);
            request.setPayer(payer);
            PrepayWithRequestPaymentResponse prepayWithRequestPaymentResponse=service.prepayWithRequestPayment(request);
            Map<String, String> payJsAPI = Maps.newHashMap();
            payJsAPI.put("appId",prepayWithRequestPaymentResponse.getAppId());
            payJsAPI.put("timeStamp",prepayWithRequestPaymentResponse.getTimeStamp());
            payJsAPI.put("nonceStr", prepayWithRequestPaymentResponse.getNonceStr());
            payJsAPI.put("package",prepayWithRequestPaymentResponse.getPackageVal());
            payJsAPI.put("paySign",prepayWithRequestPaymentResponse.getPaySign());
            payJsAPI.put("signType",prepayWithRequestPaymentResponse.getSignType());
            return payJsAPI;
        }else{
            payConfig(outTradeNo,tradeType,businessType);
            Map<String, String> data = new HashMap<>();
            data.put("out_trade_no", outTradeNo);
            data.put("body", productName);
            data.put("total_fee", totalAmount);
            data.put("spbill_create_ip", payConfig.getSpbillCreateIp());
            data.put("notify_url", StringUtil.isNotEmpty(notifyUrl)?notifyUrl:payConfig.getNotifyUrl());
            data.put("trade_type", tradeType);
            Map<String, String> resp = wxPay.unifiedOrder(data);
            String resultCode = resp.get("result_code");
            if(!StringUtils.equals(resultCode, WXPayConstants.SUCCESS)){
                //更新渠道订单状态
                Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, outTradeNo).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
                if(subscribe != null ){
                    ObjectNode result =mapper.createObjectNode();
                    if(resp.containsKey("err_code")){
                        result.put("err_code", resp.get("err_code"));
                    };
                    if(resp.containsKey("return_msg")){
                        result.put("return_msg", resp.get("return_msg"));
                    };
                    if(resp.containsKey("result_code")){
                        result.put("result_code", resp.get("result_code"));
                    };
                    if(resp.containsKey("err_code_des")){
                        result.put("err_code_des", resp.get("err_code_des"));
                    };
                    if(resp.containsKey("return_code")){
                        result.put("return_code", resp.get("return_code"));
                    };
                    subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
                    subscribe.setResult(result.toString());
                    subscribeService.updateSubscribeDbAndEs(subscribe);
                    //信息流广告转化上报
                    channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
                }
                log.info("微信支付下单失败:订单号:{},下单结果:{}", outTradeNo,resp);
                String returnMsg=resp.containsKey("return_msg")?StringUtils.substring(resp.get("return_msg"),200):"未知错误";
                orderPayService.lambdaUpdate().eq(OrderPay::getOrderId,outTradeNo).set(OrderPay::getStatus,0).set(OrderPay::getRemark,returnMsg).update();
                log.info("微信支付下单失败:订单号:{},下单结果:{}", outTradeNo,resp);
                throw new Exception("支付失败,请稍后再试!");
            }
            return  resp;
        }
    }
//    @Override
//    public Map<String, String> shopPay(String outTradeNo, String totalAmount, String subject,String notifyUrl,String tradeType,String openId,String businessType) throws Exception {
//        payConfig(outTradeNo,tradeType,businessType);
//        Map<String, String> data = new HashMap<>();
//        data.put("out_trade_no", outTradeNo);
//        data.put("body", subject);
//        data.put("total_fee", totalAmount);
//        data.put("spbill_create_ip", payConfig.getSpbillCreateIp());
//        data.put("notify_url", StringUtil.isNotEmpty(notifyUrl)?notifyUrl:payConfig.getNotifyUrl());
//        data.put("trade_type", tradeType);
//        if(tradeType.equals(BizConstant.TRADE_TYPE_WECHAT)){
//            data.put("openid", openId);
//        }
//        Map<String, String> resp = wxPay.unifiedOrder(data);
//        //{nonce_str=vkiSAxfGZZbLTIM2, mweb_url=https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx1511442547333437eef07faa1004983200&package=3430310883, appid=wx8ad568ceec585da2, sign=97B7BD987983C0FB5E37EC219AC494E4B53486271BD2AA68B743B2DECDD7F560, trade_type=MWEB, return_msg=OK, result_code=SUCCESS, mch_id=1565728481, return_code=SUCCESS, prepay_id=wx1511442547333437eef07faa1004983200}
//        /*System.out.println("resp = " + resp);*/
//        String resultCode = resp.get("result_code");
//        if(!StringUtils.equals(resultCode, WXPayConstants.SUCCESS)){
//            //更新渠道订单状态
//            Subscribe subscribe = subscribeService.lambdaQuery().eq(Subscribe::getIspOrderNo, outTradeNo).orderByDesc(Subscribe::getCreateTime).last("limit 1").one();
//            if(subscribe != null ){
//                ObjectNode result =mapper.createObjectNode();
//                if(resp.containsKey("err_code")){
//                    result.put("err_code", resp.get("err_code"));
//                };
//                if(resp.containsKey("return_msg")){
//                    result.put("return_msg", resp.get("return_msg"));
//                };
//                if(resp.containsKey("result_code")){
//                    result.put("result_code", resp.get("result_code"));
//                };
//                if(resp.containsKey("err_code_des")){
//                    result.put("err_code_des", resp.get("err_code_des"));
//                };
//                if(resp.containsKey("return_code")){
//                    result.put("return_code", resp.get("return_code"));
//                };
//                subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_FAIL);
//                subscribe.setResult(result.toString());
//                subscribeService.updateSubscribeDbAndEs(subscribe);
//                //信息流广告转化上报
//                channelService.AdEffectFeedbackNew(subscribe, subscribe.getStatus());
//            }
//            log.info("微信支付下单失败:订单号:{},下单结果:{}", outTradeNo,resp);
//            String returnMsg=resp.containsKey("return_msg")?StringUtils.substring(resp.get("return_msg"),200):"未知错误";
//            orderPayService.lambdaUpdate().eq(OrderPay::getOrderId,outTradeNo).set(OrderPay::getStatus,0).set(OrderPay::getRemark,returnMsg).update();
//            throw new Exception("支付失败,请稍后再试!");
//        }
//        if(tradeType.equals(BizConstant.TRADE_TYPE_WECHAT)){
//            Map<String, String> payJsAPI = Maps.newHashMap();
//            payJsAPI.put("appId",payConfig.getAppID());
//            payJsAPI.put("timeStamp",String.valueOf(System.currentTimeMillis()));
//            payJsAPI.put("nonceStr", WXPayUtil.generateNonceStr());
//            payJsAPI.put("package","prepay_id="+resp.get("prepay_id"));
//            payJsAPI.put("signType",WXPayConstants.HMACSHA256);
//            payJsAPI.put("paySign",WXPayUtil.generateSignature(payJsAPI, payConfig.getKey(),WXPayConstants.SignType.HMACSHA256));
//            return payJsAPI;
//        }
//        return  resp;
//    }


    @Override
    public Map<String, String> douYinPay(String outTradeNo, String totalAmount, String subject,String tradeType,String businessType)throws Exception{
        WechatConfigLog payConfig=wechatConfigLogService.getDyConfig(tradeType,businessType);
        if(payConfig==null){
            log.info("抖音支付下单失败,配置文件错误:订单号:{}", outTradeNo);
            throw new Exception("支付失败,请稍后再试!");
        }
        if(StringUtils.isBlank(totalAmount)){
            Double totalFee=Double.valueOf(payConfig.getTotalAmount());
            totalAmount=BigDecimal.valueOf(totalFee).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
            orderPayLogService.lambdaUpdate().eq(QyclOrderPayLog::getId,outTradeNo).set(QyclOrderPayLog::getAppId,payConfig.getAppId()).set(QyclOrderPayLog::getMchId,payConfig.getMchId()).set(QyclOrderPayLog::getTradeType,tradeType).set(QyclOrderPayLog::getTotalFee,totalFee).update();
        }
        return DouYinSign.getByteAuthorization(payConfig.getBusinessType(),Integer.valueOf(totalAmount),subject,dyPayPropertiesConfig.getImage(),outTradeNo,dyPayPropertiesConfig.getPath(),payConfig.getAppId(),payConfig.getApiKey(),payConfig.getMchSerialNo());
    }


    @Override
    public DouYinPayNotifyMsg douyinPaymentCallback(String raw, String contentType,String idEntifyName,String logId,String nonceStr,String signaTure,String timeStamp)throws Exception{
        try {
            final ObjectNode  tree = mapper.readValue(raw, ObjectNode.class);
//            String type = tree.get("type").asText();
            String msg = tree.get("msg").asText();
//            String version = tree.get("version").asText();
            final DouYinPayNotifyMsg douYinPayNotifyMsg = mapper.readValue(msg, DouYinPayNotifyMsg.class);
            WechatConfigLog payConfig=wechatConfigLogService.getDyNotifyConfig(douYinPayNotifyMsg.getAppId());
            if(payConfig==null){
                log.error("抖音小程序支付结果回调,配置文件错误:参数:{}", douYinPayNotifyMsg);
                return null;
            }

            if(!DouYinSign.verify(raw,payConfig.getReturnUrl(),signaTure,Long.valueOf(timeStamp),nonceStr)){
                log.error("抖音小程序支付结果回调,验签失败:参数:{}", douYinPayNotifyMsg);
                return null;
            };
            return douYinPayNotifyMsg;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public FebsResponse douYinRefund(String outTradeNo,String outRefundNo,String refundAmount,String appId,String mchId) throws Exception{
        Map<String, Object> refundNode = Maps.newHashMap();
        refundNode.put("order_id",outTradeNo);
        refundNode.put("out_refund_no",outRefundNo);
        refundNode.put("cp_extra","extra_info");
        Map<String, String> orderEntrySchema = new HashMap<>();
        //小程序xxx详情页跳转路径，没有前导的“/”，路径后不可携带query参数，路径中不可携带『？: & *』等特殊字符，路径只可以是『英文字符、数字、_、/ 』等组成，长度<=512byte
        orderEntrySchema.put("path",dyPayPropertiesConfig.getPath());
        refundNode.put("order_entry_schema", orderEntrySchema);

        //退款通知地址
        refundNode.put("notify_url",dyPayPropertiesConfig.getTradeRefundNotifyUrl());

        Map<String, Object> refundReasonMap = new HashMap<>();

        refundReasonMap.put("code", 101);
        refundReasonMap.put("text", "不想要了");
        List refundReason=Lists.newArrayList();
        refundReason.add(refundReasonMap);
        refundNode.put("refund_reason",refundReason);
        refundNode.put("refund_all",true);
        refundNode.put("refund_total_amount",Integer.valueOf(refundAmount));
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatNotifyConfig(appId,mchId);
        if(wechatConfigLog==null){
            log.error("抖音支付新版本退款失败,获取支付配置失败:订单号:{}", outTradeNo);
            return new FebsResponse().fail();
        }
        DouYinToken douYinToken=wechatConfigLogService.getDouYinToken(appId,wechatConfigLog.getAppSecret());
        if(!douYinToken.isOK()){
            log.error("抖音支付新版本退款失败,Token获取失败:订单号:{}", outTradeNo);
            return new FebsResponse().fail();
        }
        // 请求时间戳
        long timestamp = System.currentTimeMillis()/1000L;
        // 随机字符串
        String nonceStr = UUID.randomUUID().toString();

        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type","application/json");
        header.put("access-token", douYinToken.getData().getAccessToken());
        header.put("Byte-Identifyname", "/cms-vrbt/api/douyin/trade/refundNotify");
        header.put("Byte-Logid", douYinToken.getData().getLogId());
        header.put("Byte-Nonce-Str",nonceStr);
        String signaTure=DouYinSign.getSignature(wechatConfigLog.getApiKey(),"POST","/api/trade_basic/v1/developer/refund_create/",timestamp,nonceStr, JSONObject.toJSONString(refundNode));
        header.put("Byte-Signature",signaTure);
        header.put("Byte-Timestamp",String.valueOf(timestamp));

        final DouYinRefund douYinRefund = mapper.readValue(implementHttpPostResult(dyPayPropertiesConfig.getTradeRefundUrl(), refundNode,"抖音小程序新版本退款",header), DouYinRefund.class);
        if(!douYinRefund.isOK()){
            log.error("抖音支付新版本退款失败,参数错误:订单号:{},退款结果:{}", outTradeNo,douYinRefund);
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }


    /**
     * 发起http请求
     */
    private String implementHttpPostResult(String url, Object fromValue,String msg, Map<String, String> header) {
        final ObjectNode dataNode = mapper.valueToTree(fromValue);
        String raw = dataNode.toString();
        return push(url, raw,msg,header);
    }

    private String push(String url,String raw,String msg,Map<String, String> header) {
        log.info(msg+",请求数据=>地址:{},请求参数:{}",url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).build();
        if(header!=null && header.size()>0){
            Request.Builder requestBuilder = request.newBuilder();
            header.entrySet().stream().forEach(entry -> {
                requestBuilder.header(entry.getKey(),entry.getValue());
            });
            request=requestBuilder.build();
        }
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>地址:{},请求参数:{},响应参数:{}",url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>地址:{},请求参数:{}",url,raw,e);
            return null;
        }
    }


    @Override
    public DouYinRefundNotifyMsg douyinTradeRefundNotify(String raw, String contentType,String idEntifyName,String logId,String nonceStr,String signaTure,String timeStamp)throws Exception{
        try {
            final ObjectNode  tree = mapper.readValue(raw, ObjectNode.class);
//            String type = tree.get("type").asText();
            String msg = tree.get("msg").asText();
//            String version = tree.get("version").asText();
            final DouYinRefundNotifyMsg douYinPayNotifyMsg = mapper.readValue(msg, DouYinRefundNotifyMsg.class);
            WechatConfigLog payConfig=wechatConfigLogService.getDyNotifyConfig(douYinPayNotifyMsg.getAppId());
            if(payConfig==null){
                log.error("抖音小程序退款结果回调,配置文件错误:参数:{}", douYinPayNotifyMsg);
                return null;
            }

            if(!DouYinSign.verify(raw,payConfig.getReturnUrl(),signaTure,Long.valueOf(timeStamp),nonceStr)){
                log.error("抖音小程序退款结果回调,验签失败:参数:{}", douYinPayNotifyMsg);
                return null;
            };
            return douYinPayNotifyMsg;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

//
//
//    @Override
//    public FebsResponse douYinCreateRefund(String orderId,String outTradeNo,String outRefundNo,String refundAmount,String appId,String mchId) throws Exception{
//        Map<String, Object> refundNode = Maps.newHashMap();
//        refundNode.put("out_order_no",orderId);
//        refundNode.put("out_refund_no",outRefundNo);
//
//        Map<String, String> orderEntrySchema = new HashMap<>();
//        //小程序xxx详情页跳转路径，没有前导的“/”，路径后不可携带query参数，路径中不可携带『？: & *』等特殊字符，路径只可以是『英文字符、数字、_、/ 』等组成，长度<=512byte
//        orderEntrySchema.put("path",dyPayPropertiesConfig.getPath());
//        refundNode.put("order_entry_schema", orderEntrySchema);
//
//        //退款通知地址
//        refundNode.put("notify_url",dyPayPropertiesConfig.getTradeRefundNotifyUrl());
//
//        Map<String, Object> itemOrderDetailMap = new HashMap<>();
//
//        itemOrderDetailMap.put("item_order_id",outTradeNo);
//        itemOrderDetailMap.put("refund_total_amount",Integer.valueOf(refundAmount));
//
//        List itemOrderDetail=Lists.newArrayList();
//        itemOrderDetail.add(itemOrderDetailMap);
//        refundNode.put("item_order_detail",itemOrderDetail);
//
//
//        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatNotifyConfig(appId,mchId);
//        if(wechatConfigLog==null){
//            log.error("抖音支付新版本退款失败,获取支付配置失败:订单号:{}", outTradeNo);
//            return new FebsResponse().fail();
//        }
//        DouYinToken douYinToken=wechatConfigLogService.getDouYinToken(appId,wechatConfigLog.getAppSecret());
//        if( douYinToken==null || douYinToken.getData()==null || !"0".equals(String.valueOf(douYinToken.getData().getErrorCode()))){
//            log.error("抖音支付新版本退款失败,Token获取失败:订单号:{}", outTradeNo);
//            return new FebsResponse().fail();
//        }
//        // 请求时间戳
//        long timestamp = System.currentTimeMillis()/1000L;
//        // 随机字符串
//        String nonceStr = UUID.randomUUID().toString();
//
//        Map<String, String> header = Maps.newHashMap();
//        header.put("Content-Type","application/json");
//        header.put("access-token", douYinToken.getData().getAccessToken());
//        header.put("Byte-Identifyname", "");
//        header.put("Byte-Logid", douYinToken.getData().getLogId());
//        header.put("Byte-Nonce-Str",nonceStr);
//        String signaTure=DouYinSign.getSignature(wechatConfigLog.getApiKey(),"POST","/api/trade_basic/v1/developer/refund_create",timestamp,nonceStr, JSONObject.toJSONString(refundNode));
//        header.put("Byte-Signature",signaTure);
//        header.put("Byte-Timestamp",String.valueOf(timestamp));
//
//        final DouYinRefund douYinRefund = mapper.readValue(implementHttpPostResult(dyPayPropertiesConfig.getCreateRefundUrl(), refundNode,"抖音小程序新版本退款",header), DouYinRefund.class);
//        if(douYinRefund==null || !"0".equals(douYinRefund.getErrNo())){
//            log.error("抖音支付新版本退款失败,参数错误:订单号:{},退款结果:{}", outTradeNo,douYinRefund);
//            return new FebsResponse().fail();
//        }
//        return new FebsResponse().success();
//    }



    /**
     * 抖音小程序新版本获取用户授权
     * @param code
     * @return
     */
    @Override
    public DouYinTokenRequest douYinAuth(String code,String anonymousCode,String tradeType,String businessType)throws Exception{
        ObjectNode auth =mapper.createObjectNode();
        WechatConfigLog payConfig=wechatConfigLogService.getDyConfig(tradeType,businessType);
        if(payConfig==null){
            log.info("抖音配置查询失败:授权码:{}", code);
            throw new Exception("配置错误,请稍后再试!");
        }
        auth.put("appid",payConfig.getAppId());
        auth.put("secret",payConfig.getAppSecret());
        auth.put("code",code);
        auth.put("anonymous_code",anonymousCode);
        try {
            final DouYinTokenRequest objectNode = mapper.readValue(implementHttpPostResult(dyPayPropertiesConfig.getOpenIdUrl(), auth,"抖音小程序新版本获取用户授权"), DouYinTokenRequest.class);
            return objectNode;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return null;

    }




    @Override
    public FebsResponse wechatRefund(String outTradeNo,String outRefundNo,String refundAmount,String totalAmount,String appId,String mchId,String refundNotifyUrl){
        Map<String, String> refundNode = Maps.newHashMap();
        refundNode.put("appid",appId);
        refundNode.put("mch_id",mchId);
        refundNode.put("nonce_str",WXPayUtil.generateNonceStr());
        refundNode.put("out_trade_no",outTradeNo);
        refundNode.put("out_refund_no",outRefundNo);
        refundNode.put("total_fee",totalAmount);
        refundNode.put("refund_fee",refundAmount);
        refundNode.put("refund_fee_type","CNY");
        refundNode.put("notify_url",StringUtil.isNotEmpty(refundNotifyUrl)?refundNotifyUrl:WX_NOTIFY_URL);
        try{
            WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatNotifyConfig(appId,mchId);
            refundNode.put("sign", WXPayUtil.generateSignature(refundNode, wechatConfigLog.getMchKey()));
            String xmlParam = WXPayUtil.generateSignedXml(refundNode, wechatConfigLog.getMchKey());
            Map<String, String> resp = wxPay.refund(xmlParam, mchId);
            String returnCode = resp.get("return_code");
            String resultCode = resp.get("result_code");
            String errCodeDes = resp.get("err_code_des");
            log.info("微信支付退款:订单号:{},退款结果:{}", outTradeNo,resp);
            if(WXPayConstants.SUCCESS.equals(returnCode) && WXPayConstants.SUCCESS.equals(resultCode)){
                return new FebsResponse().success(errCodeDes);
            }
            return new FebsResponse().fail(errCodeDes);
        } catch (Exception e) {
            e.printStackTrace();
            return new FebsResponse().fail("退款失败！");
        }

    }



    @Override
    public Result<?> vrbtPay(String mobile, String outTradeNo, String subject, String tradeType, String openId, String channel, String subChannel,String returnUrl,String ringType, String ringId,String copyRightId,String bizType,String ringName){
        try {
            payConfig(outTradeNo,tradeType,channel);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Double totalFee=Double.valueOf(payConfig.getTotalAmount());
        String totalAmount=BigDecimal.valueOf(totalFee).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString();
        //创建订单
        MiguVrbtPayOrder miguVrbtPayOrder= new MiguVrbtPayOrder();
        miguVrbtPayOrder.setMobile(mobile);
        miguVrbtPayOrder.setOrderNo(outTradeNo);
        miguVrbtPayOrder.setOrderAmount(payConfig.getTotalAmount());
        miguVrbtPayOrder.setAppId(payConfig.getAppID());
        miguVrbtPayOrder.setChannel(channel);
        miguVrbtPayOrder.setSubChannel(subChannel);
        miguVrbtPayOrder.setRingType(Integer.valueOf(ringType));
        miguVrbtPayOrder.setRingId(ringId);
        miguVrbtPayOrder.setCopyRightId(copyRightId);
        miguVrbtPayOrder.setTradeType(tradeType);
        miguVrbtPayOrder.setBizType(bizType);
        //铃音名称
        miguVrbtPayOrder.setRingName(ringName);
        Map<String, String> data = new HashMap<>();
        data.put("out_trade_no", outTradeNo);
        data.put("body",  StringUtil.isNotEmpty(subject)?subject:payConfig.getPublicName());
        data.put("total_fee", totalAmount);
        data.put("spbill_create_ip", payConfig.getSpbillCreateIp());
        data.put("notify_url",payConfig.getNotifyUrl());
        data.put("trade_type", tradeType);
        if(tradeType.equals(BizConstant.TRADE_TYPE_WECHAT)){
            data.put("openid", openId);
        }
        Map<String, String> resp = null;
        try {
            resp = wxPay.unifiedOrder(data);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //{nonce_str=vkiSAxfGZZbLTIM2, mweb_url=https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx1511442547333437eef07faa1004983200&package=3430310883, appid=wx8ad568ceec585da2, sign=97B7BD987983C0FB5E37EC219AC494E4B53486271BD2AA68B743B2DECDD7F560, trade_type=MWEB, return_msg=OK, result_code=SUCCESS, mch_id=1565728481, return_code=SUCCESS, prepay_id=wx1511442547333437eef07faa1004983200}
        /*System.out.println("resp = " + resp);*/
        String resultCode = resp.get("result_code");
        String returnMsg=resp.containsKey("return_msg")?StringUtils.substring(resp.get("return_msg"),200):"未知错误";
        miguVrbtPayOrder.setRemark(resultCode+":"+returnMsg);
        if(!StringUtils.equals(resultCode, WXPayConstants.SUCCESS)){
            log.info("咪咕视频彩铃三方支付微信支付下单失败:订单号:{},下单结果:{}", outTradeNo,resp);
            miguVrbtPayOrder.setOrderStatus(0);
            miguVrbtPayOrderService.save(miguVrbtPayOrder);
            return Result.error(miguVrbtPayOrder.getRemark());
        }else{
            miguVrbtPayOrder.setOrderStatus(-1);
        }
        miguVrbtPayOrderService.save(miguVrbtPayOrder);
        if(BizConstant.TRADE_TYPE_WECHAT.equals(tradeType)){
            Map<String, String> payJsAPI = Maps.newHashMap();
            payJsAPI.put("appId",payConfig.getAppID());
            payJsAPI.put("timeStamp",String.valueOf(System.currentTimeMillis()));
            payJsAPI.put("nonceStr", WXPayUtil.generateNonceStr());
            payJsAPI.put("package","prepay_id="+resp.get("prepay_id"));
            payJsAPI.put("signType",WXPayConstants.HMACSHA256);
            try {
                payJsAPI.put("paySign",WXPayUtil.generateSignature(payJsAPI, payConfig.getKey(),WXPayConstants.SignType.HMACSHA256));
            } catch (Exception e) {
                e.printStackTrace();
            }
            payJsAPI.put("orderId",outTradeNo);
            return Result.ok("成功",payJsAPI);
        }else if(BizConstant.TRADE_TYPE_HTML.equals(tradeType)) {
            String mwebUrl = resp.get("mweb_url");
            String returnUrlEncode = UriUtils.encode(returnUrl + "?orderId=" + outTradeNo, StandardCharsets.UTF_8);
            String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrlEncode;
            log.info("咪咕视频彩铃三方支付微信支付支付跳转地址->mwebUrlWithRedirect:{}", mwebUrlWithRedirect);
            return Result.ok("成功",mwebUrlWithRedirect);
        }
        return Result.error("咪咕视频彩铃三方支付微信支付通道错误");
    }
}
