package com.eleven.cms.service;

import org.jeecg.common.exception.JeecgBootException;

/**
 * Author: ye<PERSON><PERSON><PERSON>@qq.com
 * Date: 2020/2/25 13:51
 * Desc:发送短信验证码
 */
public interface ISmsValidateService {
    boolean create(String phone,String channelCode);

    boolean createLt(String phone) throws JeecgBootException;

    void check(String phone, String value) throws JeecgBootException;

    boolean createTy(String phone,String channelCode);

    boolean createThirdParty(String phone,String channelCode);

    //boolean sendNotify(String phone, String content);

    boolean createUnify(String phone, String channelCode, String serviceId);

    boolean createUnifyTemplate(String phone);

    boolean rightsCreate(String phone,String channelCode,String serviceId);

}
