package com.eleven.cms.service.impl;

import com.eleven.cms.entity.BusinessChannelRights;
import com.eleven.cms.entity.BusinessPack;
import com.eleven.cms.entity.CouponCode;
import com.eleven.cms.entity.JunboChargeLog;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 兑换码直充校验是否过期
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/7/27 11:27
 **/


@Slf4j
@Service
public class CouponCodeRechargeRightsServiceImpl implements IBusinessRightsSubService {
    private static final String LOG_TAG = "兑换码直充[校验]";
    private static final String LOG_TAG_ERROR = "兑换码直充[校验异常]";
    @Autowired
    private ICouponCodeService couponCodeService;
    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private IBusinessChannelRightsService businessChannelRightsService;
    @Autowired
    private IBusinessPackService businessPackService;
    @Override
    public FebsResponse memberVerify(String mobile,String serviceId) {
        return new FebsResponse().fail();
    }

    @Override
    public FebsResponse rechargRecordVerify(String mobile,String serviceId) {
        return new FebsResponse().fail();
    }

    /**
     * 判断激活码是否使用
     * @param miguOrderId
     * @param serviceId
     * @return
     */
    private Boolean couponCodeIsUse(String miguOrderId,String serviceId) {
        try {
            //业务类型去重
            final List<BusinessPack> businessPackList= businessPackService.lambdaQuery().eq(BusinessPack::getServiceId,serviceId).select(BusinessPack::getBusinessId).list();
            if(businessPackList==null || businessPackList.isEmpty()){
                log.info("{}-业务渠道权益关联未正确配置=>订单号:{},权益领取业务ID:{}",LOG_TAG,miguOrderId,serviceId);
                return false;
            }
            final List<String> businessIdList=businessPackList.stream().map(BusinessPack::getBusinessId).collect(Collectors.toList());
            final List<BusinessChannelRights> businessList=businessChannelRightsService.lambdaQuery().eq(BusinessChannelRights::getIsEffect,1).in(BusinessChannelRights::getId,businessIdList).select(BusinessChannelRights::getBusinessChannel).list();
            if(businessList==null || businessList.isEmpty()){
                log.info("{}-指定渠道权益未正确配置=>订单号:{},权益领取业务ID:{}",LOG_TAG,miguOrderId,serviceId);
                return false;
            }


            CouponCode couponCode=couponCodeService.lambdaQuery().eq(CouponCode::getOrderId,miguOrderId).eq(CouponCode::getStatus,BizConstant.RECHARGE_WAIT).orderByDesc(CouponCode::getCreateTime).last("limit 1").one();
            if(couponCode==null){
                log.info("{}-激活码已使用=>订单号:{},权益领取业务ID:{}",LOG_TAG,miguOrderId,serviceId);
                return false;
            }


            if(couponCode.getInvalidTime()!=null){
                LocalDateTime invalidTime=couponCode.getInvalidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                if(invalidTime.isBefore(LocalDateTime.now())){
                    log.info("{}-激活码已过期=>订单号:{},权益领取业务ID:{}",LOG_TAG,miguOrderId,serviceId);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("{}-订单号:{},权益领取业务ID:{}",LOG_TAG_ERROR, miguOrderId,serviceId,e);
        }
        return false;
    }

    /**
     * 创建预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse createScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {

        return new FebsResponse().fail();
    }

    /**
     * 创建网页权益预约充值记录
     * @param mobile
     * @param account
     * @param serviceId
     * @param packName
     * @param rightsId
     * @return
     */
    @Override
    public FebsResponse webCreateScheduledRecharge(String mobile,String account,String serviceId,String packName,String rightsId,String channel) {

        return new FebsResponse().fail();
    }



    @Override
    public FebsResponse rechargeForScheduleVerify(JunboChargeLog junboChargeLog) {
        //判断激活码是否使用
        Boolean couponCodeIsUse=this.couponCodeIsUse(junboChargeLog.getMiguOrderId(),junboChargeLog.getServiceId());
        //已使用
        if(!couponCodeIsUse){
            junboChargeLog.setStatus(BizConstant.JUNBO_RECHARGE_STATUS_FAIL);
            junboChargeLog.setRemark("预约直充取消,激活码已使用或激活码已过期");
            junboChargeLog.setUpdateTime(new Date());
            junboChargeLogService.updateById(junboChargeLog);
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }
    @Override
    public void rechargeForSchedule(JunboChargeLog junboChargeLog) {
        junboChargeLog=junboChargeLogService.taskRechargeForSchedule(junboChargeLog);
        updateCouponCodeRechargeState(junboChargeLog);
    }

    /**
     * 更新订单领取状态
     * @param junboChargeLog
     */
    @Override
    public void updateRechargeState(JunboChargeLog junboChargeLog) {
        updateCouponCodeRechargeState(junboChargeLog);
    }

    /**
     * 更新兑换码直充权益状态
     * @param junboChargeLog
     */
    private void updateCouponCodeRechargeState(JunboChargeLog junboChargeLog) {
        //查询最新兑换码直充订单
        CouponCode couponCode=couponCodeService.lambdaQuery().eq(CouponCode::getOrderId,junboChargeLog.getMiguOrderId()).eq(CouponCode::getStatus,BizConstant.RECHARGE_WAIT).orderByDesc(CouponCode::getCreateTime).last("limit 1").one();
        if(couponCode!=null){
            Integer status=BizConstant.RECHARGE_WAIT;
            if(junboChargeLog.getStatus().equals(0)){
                status=BizConstant.RECHARGE_WAIT;
            }else if(junboChargeLog.getStatus().equals(1)){
                status=BizConstant.IS_USE;
            }else if(junboChargeLog.getStatus().equals(2)){
                status=BizConstant.RECHARGE_FAIL;
            }
            couponCodeService.lambdaUpdate().eq(CouponCode::getId, couponCode.getId()).set(CouponCode::getStatus,status).update();
        }
    }
}
