package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.dto.JunboRespon;
import com.eleven.cms.dto.JunboResult;
import com.eleven.cms.entity.*;
import com.eleven.cms.mapper.OrderPayMapper;
import com.eleven.cms.queue.CallbackNotifyMessage;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.remote.JunboApiService;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.FebsResponse;
import com.eleven.cms.vo.MobileRegionResult;
import com.github.wxpay.sdk.WXPayConstants;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriUtils;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @Description: cms_order_pay
 * @Author: jeecg-boot
 * @Date:   2022-11-04
 * @Version: V1.0
 */
@Slf4j
@Service
public class OrderPayServiceImpl extends ServiceImpl<OrderPayMapper, OrderPay> implements IOrderPayService {
    //未支付
    public static final int NOT_PAY_STATUS = -1;
    //支付失败
    public static final int FAIL_PAY_STATUS =0;
    //支付成功
    public static final int SUCCESS_PAY_STATUS =1;

    //退款成功
    public static final int SUCCESS_REFUND_STATUS =4;
    //退款失败
    public static final int FAIL_REFUND_STATUS =5;

    @Autowired
    private IJunboChargeLogService junboChargeLogService;
    @Autowired
    private JunboApiService junboApiService;
    @Autowired
    private IRightsSubService rightsSubService;
    @Autowired
    private IShopProductRightsService shopProductRightsService;
    @Autowired
    private MobileRegionService mobileRegionService;
    @Autowired
    private IShopProductService shopProductService;

    @Autowired
    private IWechatConfigLogService wechatConfigLogService;
    @Autowired
    private IQyclWxpayService qyclWxpayService;
    @Autowired
    private IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    private BlackListService blackListService;
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    private IChannelService channelService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;


    @Autowired
    private  ILianlianChargeLogService lianlianChargeLogService;

    @Autowired
    private ILianLianFenXiaoService lianLianFenXiaoService;
    @Override
    public FebsResponse queryOrderPayState(String outTradeNo) {
        OrderPay  orderPay=this.lambdaQuery().eq(OrderPay::getOrderId, outTradeNo).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
        if(orderPay!=null){
            if(orderPay.getStatus()==-1){
                return new FebsResponse().code(HttpStatus.BAD_GATEWAY).message("订单未支付");
            }else if(orderPay.getStatus()==0){
                return new FebsResponse().code(HttpStatus.NOT_IMPLEMENTED).message("订单支付失败");
            }else if(orderPay.getStatus()==1){
                return new FebsResponse().success().message("订单支付成功");
            }
        }
        return new FebsResponse().fail().message("订单不存在");
    }
    @Override
    public void savePay(String tradeType, String orderId, Double totalFee, String appId, String mchId) {
        OrderPay pay=new OrderPay();
        pay.setOrderId(orderId);
        pay.setTradeType(tradeType);
        pay.setStatus(-1);
        pay.setPayTime(new Date());
        pay.setCreateTime(new Date());
        pay.setUpdateTime(new Date());
        this.save(pay);
    }
    @Override
    public void updatePayStatus(String outTradeNo,String transactionId,Integer status) {
        Boolean orderPay=this.lambdaUpdate()
                .eq(OrderPay::getOrderId, outTradeNo)
                .eq(OrderPay::getStatus, -1)
                .set(OrderPay::getStatus,status)
                .set(OrderPay::getOutTradeNo, transactionId)
                .set(OrderPay::getUpdateTime, new Date()).update();
        if(status.equals(1) && orderPay){
            JunboChargeLog junboChargeLog = junboChargeLogService.lambdaQuery().eq(JunboChargeLog::getMiguOrderId,outTradeNo).eq(JunboChargeLog::getStatus,-1).orderByDesc(JunboChargeLog::getCreateTime).last("limit 1").one();
            if(junboChargeLog!=null){
//                final JunboResult junboResult = junboApiService.rechargeVIP(junboChargeLog.getCouponId(),outTradeNo, junboChargeLog.getAccount(), junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());

                final JunboRespon junboRespon = junboApiService.rechargeVIPJunBoMD5(junboChargeLog.getCouponId(),outTradeNo, junboChargeLog.getAccount(), junboChargeLog.getMobile(),junboChargeLog.getServiceId(),junboChargeLog.getPackName());

                junboChargeLogService.getReceiveState(junboChargeLog,junboRespon);
            }
        }
    }


    @Override
    public FebsResponse queryShopOrderList(String mobile) {
        List<OrderPay> orderPayList=this.lambdaQuery().eq(OrderPay::getLoginMobile,mobile)
                .notIn(OrderPay::getStatus, -1,0)
                .select(OrderPay::getRightsName,OrderPay::getMobile,OrderPay::getAccount,OrderPay::getPayTime,OrderPay::getRightsStatus).orderByDesc(OrderPay::getCreateTime).list();
        return new FebsResponse().success().data(orderPayList);
    }



    @Override
    public FebsResponse shopPay(Subscribe subscribe, String mobile) {
        if (rightsSubService.isAccount(subscribe.getRightsId(), subscribe.getAccount())) {
            return new FebsResponse().fail().message("账号错误");
        }
        ShopProductRights shopProductRights = shopProductRightsService.lambdaQuery().eq(ShopProductRights::getRightsId, subscribe.getRightsId()).eq(ShopProductRights::getIsOnline, 1).select(ShopProductRights::getShopId, ShopProductRights::getRightsName, ShopProductRights::getProductPrice).orderByDesc(ShopProductRights::getCreateTime).last("limit 1").one();
        if (shopProductRights == null) {
            return new FebsResponse().fail().message("产品已下架");
        }
        Integer shopProductCount = shopProductService.lambdaQuery().eq(ShopProduct::getId, shopProductRights.getShopId()).eq(ShopProduct::getIsOnline, 1).count();
        if (shopProductCount <= 0) {
            return new FebsResponse().fail().message("产品已下架");
        }
        if(StringUtils.isBlank(subscribe.getTradeType())){
            subscribe.setTradeType(BizConstant.TRADE_TYPE_WECHAT);
        }
        if(subscribe.getTradeType().equals(BizConstant.TRADE_TYPE_WECHAT) && StringUtils.isBlank(subscribe.getUserId())){
            return new FebsResponse().fail().message("userId为空!");
        }
        if(StringUtils.isBlank(subscribe.getBizType())){
            subscribe.setBizType("shop");
        }
        //获取支付配置
        WechatConfigLog wechatConfig=wechatConfigLogService.getWechatConfig(subscribe.getTradeType(),subscribe.getChannel());
        if(wechatConfig==null){
            return new FebsResponse().fail().message("支付接口已下线");
        }
        //黑名单直接返回
        if(blackListService.isBlackList(mobile)){
            return new FebsResponse().fail().message("暂时无法提供服务，请谅解!");
        }


        MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
        if (mobileRegionResult != null && !provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
            log.warn("商城产品订购省份限制,渠道号:{},手机号:{},省份:{}",subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
            return new FebsResponse().fail().message("暂未开放,敬请期待!");
        }
        //保存sub信息
        try{
            String orderId =mobile+System.currentTimeMillis();//订单号
            OrderPay order=new OrderPay();
            order.setMobile(StringUtils.isBlank(subscribe.getMobile())?mobile:subscribe.getMobile());
            order.setAccount(subscribe.getAccount());
            order.setChannel(subscribe.getChannel());
            order.setSubChannel(subscribe.getSubChannel());
            order.setTradeType(subscribe.getTradeType());
            order.setBizType(subscribe.getBizType());
            order.setOrderId(orderId);
            order.setTotalFee(shopProductRights.getProductPrice());
            order.setRightsName(shopProductRights.getRightsName());
            order.setUserId(subscribe.getUserId());
            order.setLoginMobile(mobile);
            order.setRightsId(subscribe.getRightsId());
            this.save(order);

            if(StringUtils.isNotBlank(subscribe.getSource())){
                subscribe = parseLink(subscribe.getSource(),subscribe);
            }
            subscribe.setMobile(mobile);
            subscribe.setProvince(mobileRegionResult!=null?mobileRegionResult.getProvince():"未知");
            subscribe.setCity(mobileRegionResult!=null?mobileRegionResult.getCity():"未知");
            String isp = mobileRegionResult!=null?mobileRegionResult.getOperator():"未知";
            subscribe.setIsp(isp);
            subscribe.setIspOrderNo(orderId);
            subscribe.setResult("未支付");
            subscribe.setCreateTime(new Date());
            subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
            subscribeService.createSubscribeDbAndEs(subscribe);

            Map<String, String> resp = qyclWxpayService.shopPay(orderId,String.valueOf(shopProductRights.getProductPrice()), shopProductRights.getRightsName(),wechatConfig.getNotifyUrl(), order.getTradeType(),subscribe.getUserId(), order.getChannel());
            if(StringUtils.equalsAny(order.getTradeType(),BizConstant.TRADE_TYPE_HTML)){
                String  mwebUrl = resp.get("mweb_url");
                String returnUrlEncode = UriUtils.encode(wechatConfig.getReturnUrl() + "?orderId=" + orderId, StandardCharsets.UTF_8);
                String mwebUrlWithRedirect = mwebUrl + "&redirect_url=" + returnUrlEncode;
                log.info("微信支付支付跳转地址->mwebUrlWithRedirect:{}",mwebUrlWithRedirect);
                return new FebsResponse().success().data(mwebUrlWithRedirect);
            }
            return new FebsResponse().success().data(resp);
        } catch (Exception e) {
            log.error("商城产品支付订单出错：",e);
        }
        return new FebsResponse().fail().message("系统错误!");

    }


    private Subscribe parseLink(String source, Subscribe subscribe) {
        try{
            source = URLDecoder.decode(source, "UTF-8");
            String subChannel = HttpUrl.parse(source.replace("#/", "")).queryParameter("subChannel");
            subscribe.setSubChannel(subChannel);
            subscribe.setSource(source);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return subscribe;
    }



    @Override
    public FebsResponse saveOrderPay(String userId, String mobile,String account,String channel,String subChannel,String tradeType,String bizType,String orderId,String rightsId,String loginMobile) {
        ShopProductRights shopProductRights = shopProductRightsService.lambdaQuery().eq(ShopProductRights::getRightsId, rightsId).eq(ShopProductRights::getIsOnline, 1).select(ShopProductRights::getShopId, ShopProductRights::getRightsName, ShopProductRights::getProductPrice).orderByDesc(ShopProductRights::getCreateTime).last("limit 1").one();
        if (shopProductRights == null) {
            return new FebsResponse().fail().message("产品已下架");
        }
        Integer shopProductCount = shopProductService.lambdaQuery().eq(ShopProduct::getId, shopProductRights.getShopId()).eq(ShopProduct::getIsOnline, 1).count();
        if (shopProductCount <= 0) {
            return new FebsResponse().fail().message("产品已下架");
        }

        OrderPay order=new OrderPay();
        order.setUserId(userId);
        order.setMobile(mobile);
        order.setAccount(account);
        order.setChannel(channel);
        order.setSubChannel(subChannel);
        order.setTradeType(tradeType);
        order.setBizType(bizType);
        order.setOrderId(orderId);
        order.setTotalFee(shopProductRights.getProductPrice());
        order.setRightsName(shopProductRights.getRightsName());
        order.setLoginMobile(loginMobile);
        order.setRightsId(rightsId);
        this.save(order);
        return new FebsResponse().success();
    }

    @Override
    public Result<?> queryShopOrder(String orderId) {
        OrderPay orderPay=this.lambdaQuery().select(OrderPay::getStatus).eq(OrderPay::getOrderId, orderId).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
        if(orderPay==null){
            return Result.error("订单不存在");
        }
        if(orderPay.getStatus().equals(0)){
            return Result.error("支付失败");
        }
        if(orderPay.getStatus().equals(-1)){
            return Result.noauth("支付中");
        }
        if(orderPay.getStatus().equals(1)){
            return Result.ok("支付成功");
        }
        return Result.error("系统错误");
    }
    @Override
    public OrderPay createPayOrder(Subscribe subscribe) {
        WechatConfigLog wechatConfigLog=wechatConfigLogService.getWechatConfig(subscribe.getTradeType(),subscribe.getChannel());
        if(wechatConfigLog==null){
            log.error("支付渠道未配置-业务参数:{}",subscribe);
            return null;
        }

        //同一手机号一个月只能支付5次
        LocalDateTime start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime end=    LocalDateTime.now();
        Boolean payCount=this.lambdaQuery().eq(OrderPay::getMobile, subscribe.getMobile())
                .between(OrderPay::getPayTime, start, end)
                .eq(OrderPay::getStatus, SUCCESS_PAY_STATUS).count()>=5;
        if(payCount){
            log.error("支付次数限制-同一手机号每月只能支付5次-业务参数:{}",subscribe);
            return null;
        }

        String orderId=IdWorker.get32UUID();
        OrderPay pay=new OrderPay();
        pay.setMobile(subscribe.getMobile());
        pay.setAccount(subscribe.getMobile());
        pay.setChannel(subscribe.getChannel());
        pay.setBizType(subscribe.getChannel());
        pay.setSubChannel(subscribe.getSubChannel());
        pay.setOrderId(orderId);
        pay.setUserId(subscribe.getUserId());
        pay.setTradeType(subscribe.getTradeType());
        pay.setStatus(NOT_PAY_STATUS);
        pay.setTotalFee(Integer.valueOf(BigDecimal.valueOf(Double.valueOf(wechatConfigLog.getTotalAmount())).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString()));
        pay.setCreateTime(new Date());
        pay.setUpdateTime(new Date());

        MobileRegionResult mobileRegionResult = mobileRegionService.query(subscribe.getMobile());
        if(StringUtils.isNotBlank(subscribe.getSource())){
            subscribe = parseLink(subscribe.getSource(),subscribe);
        }
        String province=mobileRegionResult!=null?mobileRegionResult.getProvince():"未知";
        String city=mobileRegionResult!=null?mobileRegionResult.getCity():"未知";
        pay.setProvince(province);
        pay.setCity(city);
        this.save(pay);

        subscribe.setProvince(province);
        subscribe.setCity(city);
        String isp = mobileRegionResult!=null?mobileRegionResult.getOperator():"未知";
        subscribe.setIsp(isp);
        subscribe.setIspOrderNo(orderId);
        subscribe.setBizType(subscribe.getChannel());
        subscribe.setResult("未支付");
        subscribe.setCreateTime(new Date());
        subscribe.setStatus(BizConstant.SUBSCRIBE_STATUS_INIT);
        subscribeService.createSubscribeDbAndEs(subscribe);
        return pay;
    }

    @Override
    public OrderPay queryNotPayOrder(String orderId){
        return this.lambdaQuery().eq(OrderPay::getOrderId,orderId).eq(OrderPay::getStatus,NOT_PAY_STATUS).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
    }
    @Override
    public void modifyPayStatus(String outTradeNo, String transactionId, String resultCode,String mobile){
        boolean isPaySucc = StringUtil.equals(resultCode, WXPayConstants.SUCCESS);
        Integer status=FAIL_PAY_STATUS;
        if(isPaySucc){
            status=SUCCESS_PAY_STATUS;
        }
        this.lambdaUpdate().eq(OrderPay::getOrderId, outTradeNo).eq(OrderPay::getStatus, NOT_PAY_STATUS).set(OrderPay::getStatus,status).set(OrderPay::getRightsStatus,-1).set(OrderPay::getOutTradeNo, transactionId).set(OrderPay::getPayTime, new Date()).set(OrderPay::getUpdateTime, new Date()).update();
        payNotify( mobile,isPaySucc,  outTradeNo);
    }





    private void payNotify(String mobile, Boolean isPaySuc, String orderNo) {
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, mobile).eq(Subscribe::getIspOrderNo, orderNo).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setModifyTime(new Date());
            if (isPaySuc && SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
                String result = "开通结果==>{\"resCode\":\"200\",\"resMsg\":\"成功\"}";
                upd.setStatus(SUBSCRIBE_STATUS_SUCCESS);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
                if (!outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    channelService.AdEffectFeedbackNew(subscribe, SUBSCRIBE_STATUS_SUCCESS);
                }
                subscribeService.saveChannelLimit(subscribe);
                //外部渠道加入回调延迟队列(暂时不使用队列)
                if (outsideConfigService.isOutsideChannel(subscribe.getSubChannel())) {
                    final CallbackNotifyMessage callbackNotifyMessage = new CallbackNotifyMessage(subscribe.getId(), 0, "回调通知");
                    rabbitMQMsgSender.sendOutsideCallbackMessage(callbackNotifyMessage);
                }
            }else{
                String result = "开通结果==>{\"resCode\":\"500\",\"resMsg\":\"开通失败\"}";
                upd.setStatus(SUBSCRIBE_STATUS_FAIL);
                upd.setResult(result);
                upd.setOpenTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
        }
    }
    @Override
    public Boolean queryOrder(String mobile,String orderId){
        return this.lambdaQuery().eq(OrderPay::getMobile,mobile).eq(OrderPay::getOrderId,orderId).eq(OrderPay::getStatus,SUCCESS_PAY_STATUS).count()>0;
    }


    @Override
    public Boolean queryPayStatus(String orderId){
        return this.lambdaQuery().eq(OrderPay::getOrderId,orderId).eq(OrderPay::getRightsStatus,-1).eq(OrderPay::getStatus,SUCCESS_PAY_STATUS).count()>0;
    }


    @Override
    public OrderPay queryNotRefundOrder(String refundOrderNo){
        return this.lambdaQuery().eq(OrderPay::getRefundOrderNo,refundOrderNo).eq(OrderPay::getStatus,6).orderByDesc(OrderPay::getCreateTime).last("limit 1").one();
    }


    @Override
    public void modifyRefundStatus(String orderId,String outRefundNo, String refundStatus){
        boolean success =StringUtil.equals(refundStatus, WXPayConstants.SUCCESS);
        Integer status=FAIL_REFUND_STATUS;
        if(success){
            status=SUCCESS_REFUND_STATUS;

            lianLianRefund( orderId,outRefundNo);
        }
        this.lambdaUpdate().eq(OrderPay::getRefundOrderNo, outRefundNo).eq(OrderPay::getStatus,6).set(OrderPay::getStatus,status).set(OrderPay::getUpdateTime, new Date()).update();

    }

    private void lianLianRefund(String orderId, String outRefundNo) {

        LianlianChargeLog lianlianChargeLog=lianlianChargeLogService.lambdaQuery().eq(LianlianChargeLog::getOrderId,orderId).eq(LianlianChargeLog::getStatus,6).orderByDesc(LianlianChargeLog::getCreateTime).last("limit 1").one();
        if(lianlianChargeLog==null){
            log.error("联联分销退款订单异常-订单号:{},退款单号:{}",orderId,outRefundNo);
            return;
        }

        List<String> orderList= Lists.newArrayList();
        orderList.add(lianlianChargeLog.getMinOrderNo());
        Result<?> result=lianLianFenXiaoService.refundOrder( lianlianChargeLog.getMobile(), lianlianChargeLog.getChannelOrderId(),orderList);
        if(result.isOK()){
            lianlianChargeLogService.lambdaUpdate().eq(LianlianChargeLog::getId,lianlianChargeLog.getId()).eq(LianlianChargeLog::getStatus,6)
                    .set(LianlianChargeLog::getStatus,3)
                    .set(LianlianChargeLog::getRefundRemark, "退款成功！")
                    .set(LianlianChargeLog::getUpdateTime, new Date())
                    .update();
        }else{
            lianlianChargeLogService.lambdaUpdate().eq(LianlianChargeLog::getId,lianlianChargeLog.getId()).eq(LianlianChargeLog::getStatus,6)
                    .set(LianlianChargeLog::getStatus,5)
                    .set(LianlianChargeLog::getRefundRemark, result.getMessage())
                    .set(LianlianChargeLog::getUpdateTime, new Date())
                    .update();
        }
    }


}
