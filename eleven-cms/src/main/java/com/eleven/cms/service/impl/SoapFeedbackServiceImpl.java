package com.eleven.cms.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.SoapFeedback;
import com.eleven.cms.mapper.SoapFeedbackMapper;
import com.eleven.cms.service.ISoapFeedbackService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.vo.RemoteResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 订购日志
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Service
//@DS("slave")
public class SoapFeedbackServiceImpl extends ServiceImpl<SoapFeedbackMapper, SoapFeedback> implements ISoapFeedbackService {


    @Override
    public SoapFeedback findLastOrder(String msisdn, String serviceId) {
        return this.lambdaQuery()
                   .eq(SoapFeedback::getMsisdn, msisdn)
                   .eq(SoapFeedback::getResCode, RemoteResult.CODE_OK)
                   .eq(SoapFeedback::getServiceId, serviceId)
                   .orderByDesc(SoapFeedback::getCreateTime)
                   .last(BizConstant.SQL_LIMIT_ONE)
                   .one();
    }
}
