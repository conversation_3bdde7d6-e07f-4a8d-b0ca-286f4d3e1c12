package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.dto.VrJingmengNotify;
import com.eleven.cms.dto.VrJingmengResponse;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.VrJingmeng;
import com.eleven.cms.mapper.VrJingmengMapper;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.IVrJingmengService;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.vo.FebsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;

import static com.eleven.cms.util.BizConstant.SQL_LIMIT_ONE;

/**
 * @Description: cms_vr_jingmeng
 * @Author: jeecg-boot
 * @Date:   2024-03-18
 * @Version: V1.0
 */
@Service
public class VrJingmengServiceImpl extends ServiceImpl<VrJingmengMapper, VrJingmeng> implements IVrJingmengService {
    public static final String VRJINGMENG_STATUS_SUB_SUCCESS = "100"; //订购
    public static final String VRJINGMENG_STATUS_UN_SUB_SUCCESS = "2"; //退订
    @Autowired
    ISubscribeService subscribeService;
    @Override
    public VrJingmengResponse vrJingmengNotify(VrJingmengNotify vrJingmengNotify) {
        Subscribe subscribe=subscribeService.lambdaQuery().eq(Subscribe::getMobile, vrJingmengNotify.getMobile()).eq(Subscribe::getServiceId, vrJingmengNotify.getChannel()).orderByDesc(Subscribe::getCreateTime).last(SQL_LIMIT_ONE).one();
        if(subscribe!=null) {
            VrJingmeng vrJingmeng=new VrJingmeng();
            /**流水号(订单号)*/
            vrJingmeng.setTransactionId(vrJingmengNotify.getTransactionId());
            /**手机号*/
            vrJingmeng.setMobile(vrJingmengNotify.getMobile());
            /**产品标识，用来区分不同的服务类型*/
            vrJingmeng.setProductId(vrJingmengNotify.getProduct());
            try {
                if(VRJINGMENG_STATUS_SUB_SUCCESS.equals(vrJingmengNotify.getStatus())){
                    /**订购时间*/
                    vrJingmeng.setSubTime(DateUtil.stringToDate(vrJingmengNotify.getActionTime(),DateUtil.FULL_TIME_PATTERN));
                    vrJingmeng.setStatus(BizConstant.SUBSCRIBE_STATUS_SUCCESS);
                }else if(VRJINGMENG_STATUS_UN_SUB_SUCCESS.equals(vrJingmengNotify.getStatus())){
                    /**退订时间*/
                    vrJingmeng.setUnSubTime(DateUtil.stringToDate(vrJingmengNotify.getActionTime(),DateUtil.FULL_TIME_PATTERN));
                    vrJingmeng.setStatus(BizConstant.SUBSCRIBE_STATUS_PUSHED);
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            /**资费*/
            vrJingmeng.setPrice(String.valueOf(vrJingmengNotify.getPrice()));
            /**计费类型，目前有两种，话费支付 1，第三方支付 2*/
            vrJingmeng.setFeeType(vrJingmengNotify.getFeeType());
            /**归属地编码*/
            vrJingmeng.setRegion(vrJingmengNotify.getRegion());
            /**渠道号*/
            vrJingmeng.setChannel(subscribe.getChannel());
            /**咪咕通过识别用户来源添加渠道信息*/
            vrJingmeng.setMiguChannel(vrJingmengNotify.getChannel());
            /**省份*/
            vrJingmeng.setProvince(subscribe.getProvince());
            /**城市*/
            vrJingmeng.setCity(subscribe.getCity());
            /**创建时间*/
            Date date=new Date();
            vrJingmeng.setCreateTime(date);
            /**修改时间*/
            vrJingmeng.setModifyTime(date);
            this.baseMapper.insert(vrJingmeng);
        }
        return new VrJingmengResponse(vrJingmengNotify.getTransactionId(),vrJingmengNotify.getMobile(),"OK");
    }

    @Override
    public FebsResponse isMonthMember(String mobile, String channelCode) {
        VrJingmeng vrJingmeng=this.lambdaQuery().eq(VrJingmeng::getMobile,mobile).eq(VrJingmeng::getChannel,channelCode).orderByDesc(VrJingmeng::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(vrJingmeng==null){
            return new FebsResponse().fail();
        }
        if(VRJINGMENG_STATUS_UN_SUB_SUCCESS.equals(vrJingmeng.getStatus())){
            return new FebsResponse().fail();
        }
        return new FebsResponse().success();
    }
}
