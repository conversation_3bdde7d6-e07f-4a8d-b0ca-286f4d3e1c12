package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.RightsPack;
import com.eleven.cms.mapper.RightsPackMapper;
import com.eleven.cms.service.IRightsPackService;
import com.eleven.cms.vo.RightsPackDto;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 会员权益业务关联
 * @Author: jeecg-boot
 * @Date:   2021-04-25
 * @Version: V1.0
 */
@Service
public class RightsPackServiceImpl extends ServiceImpl<RightsPackMapper, RightsPack> implements IRightsPackService {

	@Autowired
	private RightsPackMapper rightsPackMapper;

	@Override
	public List<RightsPack> selectByMainId(String mainId) {
		return rightsPackMapper.selectByMainId(mainId);
	}

	@Override
	public List<RightsPack> selectRightsPackList(String packId) {
		return rightsPackMapper.selectRightsPackList(packId);
	}

	@Override
	public IPage<RightsPack> queryRightsPackPageList(Page<RightsPack> page, RightsPack rightsPack) {
		return rightsPackMapper.selectRightsPackPage(page,rightsPack);
	}

	@Cacheable(cacheNames = CacheConstant.CMS_MIGU_PACK_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
	@Override
	public List<RightsPackDto> wholeMemberRightsList(String serviceId) {
		return rightsPackMapper.wholeMemberRightsList(serviceId);
	}

	@Cacheable(cacheNames = CacheConstant.CMS_BUSINESS_PACK_LIST_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
	@Override
	public List<RightsPackDto> packList(String[] ids) {
		return rightsPackMapper.packList(Arrays.asList(ids));
	}

	@Override
	public Boolean isServiceAssociation(String packId, String memberId) {
		return this.lambdaQuery()
				.eq(RightsPack::getPackId, packId)
				.eq(RightsPack::getMemberId, memberId)
				.count()<=0;

	}

	@Cacheable(cacheNames = CacheConstant.CMS_MIGU_PACK_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
	@Override
	public Optional<RightsPackDto> getMiguPackList(String packName) {
		return rightsPackMapper.getMiguPackList(packName).stream().max(Comparator.comparing(RightsPackDto::getId));
	}


	@Cacheable(cacheNames = CacheConstant.CMS_BUSINESS_PACK_LIST_CACHE,key = "#p0",condition = "#p0!=null",unless = "#result==null")
	@Override
	public List<RightsPackDto> aliPayRightsList(List<String> serviceId) {
		return rightsPackMapper.aliPayRightsList(serviceId);
	}



	@Override
	public List<RightsPackDto> queryRightsPackList(String serviceId,List<String> businessPackIdList) {
		return rightsPackMapper.queryRightsList(serviceId,businessPackIdList);
	}

	@Override
	public List<RightsPackDto> queryWebRightsPackList(String serviceId,List<String> businessPackIdList) {
		return rightsPackMapper.queryWebRightsList(serviceId,businessPackIdList);
	}

	@Override
	public List<RightsPackDto> queryRightsListById(String id) {
		return rightsPackMapper.queryRightsListById(id);
	}
}
