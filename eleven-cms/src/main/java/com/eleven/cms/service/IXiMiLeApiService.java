package com.eleven.cms.service;

import com.eleven.cms.dto.XimileRechargeNotify;
import com.eleven.cms.dto.XimileRechargeNotifyResp;
import com.eleven.cms.vo.XiMiLeQueryBalanceResult;
import com.eleven.cms.vo.XiMiLeQueryOrderResult;
import com.eleven.cms.vo.XiMiLeRechargeResult;

/**
 * 西米乐权益充值
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/19 15:08
 **/
public interface IXiMiLeApiService {
    XiMiLeRechargeResult recharge(String orderId, String skuId, String skuType, String accountId, String faceValue);

    XiMiLeQueryBalanceResult queryBalance();

    XiMiLeQueryOrderResult queryOrder(String orderId);

    XimileRechargeNotifyResp ximileRechargeNotify(XimileRechargeNotify ximileRechargeNotify);
}
