package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PptvGiveLog;
import com.eleven.cms.mapper.PptvGiveLogMapper;
import com.eleven.cms.service.IPptvGiveLogService;
import com.eleven.cms.util.BizConstant;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description: cms_pptv_give_log
 * @Author: jeecg-boot
 * @Date:   2021-06-10
 * @Version: V1.0
 */
@Service
public class PptvGiveLogServiceImpl extends ServiceImpl<PptvGiveLogMapper, PptvGiveLog> implements IPptvGiveLogService {

    @Override
    public List<PptvGiveLog> findTimeoutSchedule() {
        return this.lambdaQuery().le(PptvGiveLog::getSendSmsTime,new Date()).eq(PptvGiveLog::getStatus, BizConstant.PPTV_GIVE_STATUS_SUCCESS).eq(PptvGiveLog::getSendSmsState, BizConstant.PPTV_STATUS_RESERVE).list();
    }
}
