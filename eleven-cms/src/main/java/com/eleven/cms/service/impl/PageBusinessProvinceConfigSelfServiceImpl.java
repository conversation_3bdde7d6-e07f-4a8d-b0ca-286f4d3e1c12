package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.PageBusinessProvinceConfigSelf;
import com.eleven.cms.mapper.PageBusinessProvinceConfigSelfMapper;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.service.IPageBusinessProvinceConfigSelfService;
import com.eleven.cms.util.BizCommonConstant;
import com.eleven.cms.vo.MobileRegionResult;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @author: cai lei
 * @create: 2024-07-10 14:42
 */
@Service
public class PageBusinessProvinceConfigSelfServiceImpl extends ServiceImpl<PageBusinessProvinceConfigSelfMapper, PageBusinessProvinceConfigSelf> implements IPageBusinessProvinceConfigSelfService {
    @Autowired
    MobileRegionService mobileRegionService;

    public static final ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    @Override
    public ObjectNode getPopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province) {
        PageBusinessProvinceConfigSelf pageBusinessProvinceConfigSelf = getPageBusinessProvinceConfigSelf(pageId, province);
        if (pageBusinessProvinceConfigSelf != null) {
            ObjectNode objectNode = mapper.createObjectNode();
            if (MobileRegionResult.ISP_DIANXIN.equals(isp)) {
                objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigSelf.getDianxinPop());
                objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigSelf.getDianxinTailQuantity());
                objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigSelf.getDianxinBizTypeId());
                objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigSelf.getDianxinRefPage());
            } else if (MobileRegionResult.ISP_LIANTONG.equals(isp)) {
                objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigSelf.getLiantongPop());
                objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigSelf.getLiantongTailQuantity());
                objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigSelf.getLiantongBizTypeId());
                objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigSelf.getLiantongRefPage());
            } else if (MobileRegionResult.ISP_YIDONG.equals(isp)) {
                objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigSelf.getYidongPop());
                objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigSelf.getYidongTailQuantity());
                objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigSelf.getYidongBizTypeId());
                objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigSelf.getYidongRefPage());
            }
            return objectNode;
        }
        return null;
    }

    @Override
    public ObjectNode getSparePopAndQuantityByPageIdAndIspProvince(String pageId, String isp, String province) {
        PageBusinessProvinceConfigSelf pageBusinessProvinceConfigSelf = getPageBusinessProvinceConfigSelf(pageId, province);
        if (pageBusinessProvinceConfigSelf != null && MobileRegionResult.ISP_YIDONG.equals(isp)) {
            ObjectNode objectNode = mapper.createObjectNode();
            objectNode.put(BizCommonConstant.POP_ID, pageBusinessProvinceConfigSelf.getYidongSparePop());
            objectNode.put(BizCommonConstant.TAIL_QUANTITY_ID, pageBusinessProvinceConfigSelf.getYidongSpareTailQuantity());
            objectNode.put(BizCommonConstant.BIZ_TYPE_ID, pageBusinessProvinceConfigSelf.getYidongSpareBizTypeId());
            objectNode.put(BizCommonConstant.REF_PAGE_ID, pageBusinessProvinceConfigSelf.getYidongSpareRefPage());
            return objectNode;
        }
        return null;
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_BIZ_PAGE_CONFIG_POP_SELF_CACHE, key = "#root.methodName + '-' + #pageId+'-'+ #province", unless = "#result==null")
    public PageBusinessProvinceConfigSelf getPageBusinessProvinceConfigSelf(String pageId, String province) {
        return lambdaQuery().eq(PageBusinessProvinceConfigSelf::getPageId, pageId).eq(PageBusinessProvinceConfigSelf::getProvince, province).eq(PageBusinessProvinceConfigSelf::getStatus, 1).one();
    }
}
