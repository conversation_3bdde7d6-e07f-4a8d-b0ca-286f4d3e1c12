package com.eleven.cms.service.impl;

import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.RedisDelayedQueueManager;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.IChannelService;
import com.eleven.cms.service.IDianxinCloudGameService;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * 电信云游戏业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/7 16:40
 **/
@Slf4j
@Service("dianxinCloudGameCommonService")
public class DianxinCloudGameCommonServiceImpl implements IBizCommonService {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisDelayedQueueManager redisDelayedQueueManager;
    @Autowired
    IChannelService channelService;
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IDianxinCloudGameService dianxinCloudGameService;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        Result<?> sendMessageResult = dianxinCloudGameService.sendMessage(subscribe);
        if (sendMessageResult.isOK()) {
            subscribe.setIspOrderNo(String.valueOf(sendMessageResult.getResult()));
            subscribeService.updateSubscribeDbAndEs(subscribe);
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
            String result = "电信云游戏业务获取验证码失败==>{\"resCode\":\"" + sendMessageResult.getCode() + "\",\"resMsg\":\"" + sendMessageResult.getMessage() + "\"}";
            subscribe.setResult(result);
            subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(subscribe);
//            return Result.error("获取验证码失败");

            try {
                String errorMsg="{\"code\":\""+sendMessageResult.getCode()+"\",\"message\":\""+sendMessageResult.getMessage()+"\"}";
                return Result.errorSmsMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}",subscribe, e);
                return Result.msgSmsError();
            }
        }
    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getId());
        upd.setBizTime(new Date());
        Result<?> submitResult = dianxinCloudGameService.submitOrder(subscribe);
        String result = "电信云游戏业务订购结果==>{\"resCode\":\"" + submitResult.getCode() + "\",\"resMsg\":\"" + submitResult.getMessage() + "\"}";
        upd.setResult(result);
        if (submitResult != null && submitResult.isOK()) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.ok("订阅成功");
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
            return Result.error("订阅失败");
        }
    }
}
