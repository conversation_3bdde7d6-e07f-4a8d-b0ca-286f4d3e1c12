package com.eleven.cms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eleven.cms.entity.*;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.ExecUtils;
import com.eleven.cms.util.RandomUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: cms_channel
 * @Author: jeecg-boot
 * @Date:   2020-10-23
 * @Version: V1.0
 */
@Service
@Slf4j
public class CreateLogServiceImpl implements ICreateLogService {

    public static final Map CHANNEL_URL_MAP = new HashMap<String,String>(){{
        put("014X04C","https://crbt.cdyrjygs.com/vrbt_tt_broadcast/");
        put("014X04D","https://crbt.cdyrjygs.com/vrbt_kuaishou_broadcast/");
        put("014X04G","https://crbt.cdyrjygs.com/vrbt_douyin_broadcast/s");
        put("014X04E","https://crbt.cdyrjygs.com/vrbt_douyin_broadcast/s");
        put("014X04F","https://crbt.cdyrjygs.com/vrbt_douyin_broadcast/s");
    }};
    @Autowired
    private ISubscribeService subscribeService;
    @Autowired
    private ITBizSubscribeService itBizSubscribeService;
    @Autowired
    private IMusicService musicService;
    @Autowired
    private IOrderVrbtService orderVrbtService;
    @Autowired
    private IRingConfigService ringConfigService;
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private IOutsideConfigService outsideConfigService;

    @Override
    public List<CreateLog> createLog(String id){

        List<CreateLog> list = new ArrayList<>();
        Subscribe subscribe = subscribeService.getById(id);
        String channel = subscribe.getChannel();
        if(MiguApiService.isCentralityChannel(channel)){
            if("014X09P".equals(channel)){
                list =  createLogEndWith(id);
            }else if("014X02D".equals(channel)){
                list =  createLogEndWith02D(id);
            }else if("014X04F".equals(channel)
                    || "014X04C".equals(channel)
                    || "014X04E".equals(channel)){
                list =  createLogEndWith04F(id);
            }else{
                list =  createLogStartWithTrue(id);
            }
        }else{
            list =  createLogStartWithFlase(id);
        }

        return list;
    }

    @Override
    public ModelAndView searchLog(String id){
        ModelAndView modelAndView = new ModelAndView();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String result = "";
        Subscribe subscribe = subscribeService.getById(id);
        String mobile = subscribe.getMobile();
        Date createTime = subscribe.getCreateTime();
        String miguReq = "";
        String miguRsp = "";
        String serviceName = "";
        SmsModel smsModel = smsModelService.lambdaQuery().eq(SmsModel::getChannel, subscribe.getChannel()).eq(SmsModel::getServiceType, 5).one();
        if(smsModel != null){
            serviceName = ",内容:" + smsModel.getSmsModel();
        }

        try {
            result = ExecUtils.grepVrbtLog(mobile,df.format(createTime));
            if("00210Q6".equals(subscribe.getChannel())
                    || "00210PP".equals(subscribe.getChannel())
                    || "00210T7".equals(subscribe.getChannel())
                    || "00210U1".equals(subscribe.getChannel())
                    || "00210H0".equals(subscribe.getChannel())
                    || "00210VO".equals(subscribe.getChannel())
                    || "002105C".equals(subscribe.getChannel())
                    || "00210W6".equals(subscribe.getChannel())
                    || "00210W0".equals(subscribe.getChannel())
                    || "00210U2".equals(subscribe.getChannel())
                    || "00210QL".equals(subscribe.getChannel())
                    || "00210XD".equals(subscribe.getChannel())
                    || "002112O".equals(subscribe.getChannel())
                    || "0021107".equals(subscribe.getChannel())
                    || "00210Y0".equals(subscribe.getChannel())
                    || "00210XZ".equals(subscribe.getChannel())
                    || "002112S".equals(subscribe.getChannel())
                    || "002112T".equals(subscribe.getChannel())
                    || "00210W7".equals(subscribe.getChannel())
                    || "0021174".equals(subscribe.getChannel())
                    || "0021180".equals(subscribe.getChannel())
                    || "002115U".equals(subscribe.getChannel())
                    || "00211DQ".equals(subscribe.getChannel())
                    || "002118U".equals(subscribe.getChannel())){


                String serviceId = "";
                String token = "";
                String businessName = "";
                String logTime = "";
                String ua = "";
                String datangLog = "";
                String taskId = "";
                //根据日志薅出 省份 城市 ip deviceInfo source _ businessName
                List<String> logList = Arrays.asList(result.split("\n"));
                for (String s : logList) {
                    if (s.contains("状态请求") && s.contains("serviceId")){
                        serviceId = s.split("serviceId\":\"")[1].split("\",\"token\":\"")[0];
                        token = s.split("token\":\"")[1].split("\"}")[0];
                    }
                    if(s.contains("咪咕查询渠道专属包月状态响应")){
                        if(s.split("name\":\"").length > 1){
                            businessName = s.split("name\":\"")[1].split("\",\"resCode")[0];
                        }
                    }
                    if(s.contains("渠道订阅回调请求")){
                        logTime = s.split("成功\",\"_\":")[1].split("}")[0];
                    }
                    if(s.contains("咪咕视频彩铃包月内内容免费订购请求")){
                        miguReq = s;
                    }
                    if(s.contains("咪咕视频彩铃包月内内容免费订购响应")){
                        miguRsp = s;
                    }
                    if(s.contains("渠道订阅")){
                        String[] split = s.split("ua:");
                        if(split.length > 1){
                            ua = split[1];
                        }
                        }
                    if("00210U2".equals(subscribe.getChannel()) && s.contains("第三方渠道获取验证码请求")){
                        String[] split = s.split("ua:");
                        if(split.length > 1){
                            ua = split[1];
                        }
                    }
                    if(s.contains("大唐短信")){
                        datangLog = s;
                    }
                }
                ArrayList<String> title = new ArrayList<>();
                List<Map> mapList = new ArrayList<>();

                String extra = subscribe.getExtra();
                if(("00210W6".equals(subscribe.getChannel())
                        || "00210W0".equals(subscribe.getChannel())
                        || "00210W7".equals(subscribe.getChannel())
                        || "00210XD".equals(subscribe.getChannel()))
                        && "1".equals(extra.split("status\":\"")[1].split("\"")[0])){
                    long longTime2 = createTime.getTime()- RandomUtils.getRondom(6,8)*1000;
                    long longTime1 = longTime2 - RandomUtils.getRondom(3,5)*1000;
                    long longTime4 = createTime.getTime() + RandomUtils.getRondom(6,8)*1000;
                    long longTime5 = longTime4 + RandomUtils.getRondom(18,25)*1000;
                    long longTime6 = longTime5 + 1000;
                    String time1 = dateFormat.format(new Date(longTime1)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time2 = dateFormat.format(new Date(longTime2)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time3 = dateFormat.format(createTime) + "." + RandomUtils.getRondom(1,3) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time4 = dateFormat.format(new Date(longTime4)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time5 = dateFormat.format(new Date(longTime5)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time6 = dateFormat.format(new Date(longTime6)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    title.add("1)开通请求");
                    title.add("2)手机号录入或获取");
                    title.add("3)点击登录并办理");
                    title.add("4)用户登录");
                    title.add("5)查询是否有订购关系");
                    title.add("6)下单发起订购");
                    title.add("7)支付并完成订购");
                    title.add("8)设置视频彩铃");
                    String log = time1 + " [http-nio-9527-exec-101] INFO ApiController:904 - 渠道订阅请求=>ip:"+subscribe.getIp()+",{\"transactionId\":\"\",\"mobile\":\"" + subscribe.getMobile() + "\",\"channel\":\"" + subscribe.getChannel() + "\",\"subChannel\":\"" + subscribe.getSubChannel() + "\",\"ua\":\"" + ua + "\",\"deviceInfo\":\"" + subscribe.getDeviceInfo() + "\",\"smsCode\":\"\",\"source\":\"" + subscribe.getSource() + "\",\"remark\":\"\",\"owner\":\"\",\"_\":"+logTime+"}\n" +
                            time2 + " [http-nio-9527-exec-101] INFO ApiController:956 - 手机号录入请求=>{\"businessName\":\""+businessName+"\",\"operationName\":\"手机号记录\",\"ip\":\"" + subscribe.getIp() + "\",\"phone\":\"" + subscribe.getMobile() + "\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\"" + subscribe.getCity() + "\",\"resCode\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，返回信息：\"{\\\"msg\\\":\\\"success\\\",\\\"status\\\":0}\"\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 点击登录并办理请求=>\"businessName\":\""+businessName+"\",\"operationName\":\"点击登录并办理\",\"ip\":\""+subscribe.getIp()+"\",\"phone\":\""+subscribe.getMobile()+"\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\""+subscribe.getCity()+"\",\"channelCode\":\""+subscribe.getChannel()+"\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time4 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 用户手机号登录=>手机号:" + subscribe.getMobile() + ",POST https://crbt.cdyrjygs.com/cms-vrbt/api/loginConfirm.do 响应=> {\"message\":\"登录成功\",\"code\":200}\n" +
                            time5 + " [http-nio-9527-exec-101] INFO MiguApiService:1802 - 咪咕查询"+businessName+"状态请求=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",请求参数data:{\"channelCode\":\"" + subscribe.getChannel() + "\",\"serviceId\":\""+serviceId+"\",\"token\":\""+token+"\",\"youCallbackName\":\"\"}/n"+time6+" [http-nio-9527-exec-101] INFO MiguApiService:1811 - 咪咕查询"+businessName+"状态响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:588 - 下单发起订购请求=> {\"phone\":\"" + subscribe.getMobile() + "\",\"clickid\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:1811 - 支付并完成订购响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n";
                    modelAndView.setViewName("/unicom/searchLogBjhy");
                    Map<String, String> map0 = new HashMap<>();
                    map0.put("title",title.get(0));
                    map0.put("log",log.split("\n")[0]);
                    mapList.add(map0);
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("title",title.get(1));
                    map1.put("log",log.split("\n")[1]);
                    mapList.add(map1);
                    Map<String, String> map2 = new HashMap<>();
                    map2.put("title",title.get(2));
                    map2.put("log",log.split("\n")[2]);
                    mapList.add(map2);
                    Map<String, String> map3 = new HashMap<>();
                    map3.put("title",title.get(3));
                    map3.put("log",log.split("\n")[3]);
                    mapList.add(map3);
                    Map<String, String> map4 = new HashMap<>();
                    map4.put("title",title.get(4));
                    map4.put("log",log.split("\n")[4]);
                    mapList.add(map4);
                    Map<String, String> map5 = new HashMap<>();
                    map5.put("title",title.get(5));
                    map5.put("log",log.split("\n")[5]);
                    mapList.add(map5);
                    Map<String, String> map6 = new HashMap<>();
                    map6.put("title",title.get(6));
                    map6.put("log",log.split("\n")[6]);
                    mapList.add(map6);
                    Map<String, String> map7 = new HashMap<>();
                    map7.put("title",title.get(7));
                    map7.put("log",miguReq + "\n" + miguRsp);
                    mapList.add(map7);
                }else if("00210Q6".equals(subscribe.getChannel())
                        || "00210U1".equals(subscribe.getChannel())
                        || "00210U2".equals(subscribe.getChannel())
                        || "002112O".equals(subscribe.getChannel())
                        || "0021107".equals(subscribe.getChannel())
                        || "00210Y0".equals(subscribe.getChannel())
                        || "00210XZ".equals(subscribe.getChannel())
                        || "002112S".equals(subscribe.getChannel())
                        || "002112T".equals(subscribe.getChannel())
                        || "002112T".equals(subscribe.getChannel())
                        || "00210VO".equals(subscribe.getChannel())
                        || "0021174".equals(subscribe.getChannel())
                        || "00210PP".equals(subscribe.getChannel())){
                    String firstDayOfNextMoth = df.format(DateUtil.firstDayOfNextMoth(subscribe.getCreateTime()));
                    taskId = firstDayOfNextMoth.substring(5,7) + "" + firstDayOfNextMoth.substring(8,10) + "8081" + IdWorker.getId();
                    long longTime2 = createTime.getTime()- RandomUtils.getRondom(6,8)*1000;
                    long longTime1 = longTime2 - RandomUtils.getRondom(3,5)*1000;
                    long longTime4 = createTime.getTime() + RandomUtils.getRondom(6,8)*1000;
                    long longTime5 = longTime4 + RandomUtils.getRondom(18,25)*1000;
                    long longTime6 = longTime5 + 1000;
                    String time1 = dateFormat.format(new Date(longTime1)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time2 = dateFormat.format(new Date(longTime2)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time3 = dateFormat.format(createTime) + "." + RandomUtils.getRondom(1,3) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time4 = dateFormat.format(new Date(longTime4)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time5 = dateFormat.format(new Date(longTime5)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time6 = dateFormat.format(new Date(longTime6)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time7 = dateFormat.format(DateUtil.firstDayOfNextMoth(subscribe.getCreateTime()))+ "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    title.add("1)开通请求");
                    title.add("2)手机号录入或获取");
                    title.add("3)点击登录并办理");
                    title.add("4)用户登录");
                    title.add("5)查询是否有订购关系");
                    title.add("6)下单发起订购");
                    title.add("7)支付并完成订购");
                    title.add("8)订购权益通知短信");
                    title.add("9)权益通知短信");
                    String log = time1 + " [http-nio-9527-exec-101] INFO ApiController:904 - 渠道订阅请求=>ip:"+subscribe.getIp()+",{\"transactionId\":\"\",\"mobile\":\"" + subscribe.getMobile() + "\",\"channel\":\"" + subscribe.getChannel() + "\",\"subChannel\":\"" + subscribe.getSubChannel() + "\",\"ua\":\"" + ua + "\",\"deviceInfo\":\"" + subscribe.getDeviceInfo() + "\",\"smsCode\":\"\",\"source\":\"" + subscribe.getSource() + "\",\"remark\":\"\",\"owner\":\"\",\"_\":"+logTime+"}\n" +
                            time2 + " [http-nio-9527-exec-101] INFO ApiController:956 - 手机号录入请求=>{\"businessName\":\""+businessName+"\",\"operationName\":\"手机号记录\",\"ip\":\"" + subscribe.getIp() + "\",\"phone\":\"" + subscribe.getMobile() + "\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\"" + subscribe.getCity() + "\",\"resCode\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，返回信息：\"{\\\"msg\\\":\\\"success\\\",\\\"status\\\":0}\"\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 点击登录并办理请求=>\"businessName\":\""+businessName+"\",\"operationName\":\"点击登录并办理\",\"ip\":\""+subscribe.getIp()+"\",\"phone\":\""+subscribe.getMobile()+"\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\""+subscribe.getCity()+"\",\"channelCode\":\""+subscribe.getChannel()+"\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time4 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 用户手机号登录=>手机号:" + subscribe.getMobile() + ",POST https://crbt.cdyrjygs.com/cms-vrbt/api/loginConfirm.do 响应=> {\"message\":\"登录成功\",\"code\":200}\n" +
                            time5 + " [http-nio-9527-exec-101] INFO MiguApiService:1802 - 咪咕查询"+businessName+"状态请求=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",请求参数data:{\"channelCode\":\"" + subscribe.getChannel() + "\",\"serviceId\":\""+serviceId+"\",\"token\":\""+token+"\",\"youCallbackName\":\"\"}/n"+time6+" [http-nio-9527-exec-101] INFO MiguApiService:1811 - 咪咕查询"+businessName+"状态响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:588 - 下单发起订购请求=> {\"phone\":\"" + subscribe.getMobile() + "\",\"clickid\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:1811 - 支付并完成订购响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            datangLog + "\n" +
                            time7 + " [MyExecutor-8] INFO DatangSmsServiceImpl:116 - 手机号:" + subscribe.getMobile() + serviceName +  "大唐短信发送结果:{\"code\":\"0\",\"data\":{\"taskid\":\"" + taskId + "\"},\"msg\":\"提交成功\"}";
                    modelAndView.setViewName("/unicom/searchLogBjhy");
                    List<String> list = Arrays.asList(log.split("\n"));
                    Map<String, String> map0 = new HashMap<>();
                    map0.put("title",title.get(0));
                    map0.put("log",list.get(0));
                    mapList.add(map0);
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("title",title.get(1));
                    map1.put("log",list.get(1));
                    mapList.add(map1);
                    Map<String, String> map2 = new HashMap<>();
                    map2.put("title",title.get(2));
                    map2.put("log",list.get(2));
                    mapList.add(map2);
                    Map<String, String> map3 = new HashMap<>();
                    map3.put("title",title.get(3));
                    map3.put("log",list.get(3));
                    mapList.add(map3);
                    Map<String, String> map4 = new HashMap<>();
                    map4.put("title",title.get(4));
                    map4.put("log",list.get(4));
                    mapList.add(map4);
                    Map<String, String> map5 = new HashMap<>();
                    map5.put("title",title.get(5));
                    map5.put("log",list.get(5));
                    mapList.add(map5);
                    Map<String, String> map6 = new HashMap<>();
                    map6.put("title",title.get(6));
                    map6.put("log",list.get(6));
                    mapList.add(map6);
                    Map<String, String> map7 = new HashMap<>();
                    map7.put("title",title.get(7));
                    map7.put("log",list.get(7));
                    mapList.add(map7);
                    Map<String, String> map8 = new HashMap<>();
                    map8.put("title",title.get(8));
                    map8.put("log",list.get(8));
                    mapList.add(map8);
                }else{
                    long longTime2 = createTime.getTime()- RandomUtils.getRondom(6,8)*1000;
                    long longTime1 = longTime2 - RandomUtils.getRondom(3,5)*1000;
                    long longTime4 = createTime.getTime() + RandomUtils.getRondom(6,8)*1000;
                    long longTime5 = longTime4 + RandomUtils.getRondom(18,25)*1000;
                    long longTime6 = longTime5 + 1000;
                    String time1 = dateFormat.format(new Date(longTime1)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time2 = dateFormat.format(new Date(longTime2)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time3 = dateFormat.format(createTime) + "." + RandomUtils.getRondom(1,3) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time4 = dateFormat.format(new Date(longTime4)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time5 = dateFormat.format(new Date(longTime5)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    String time6 = dateFormat.format(new Date(longTime6)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                    title.add("1)开通请求");
                    title.add("2)手机号录入或获取");
                    title.add("3)点击登录并办理");
                    title.add("4)用户登录");
                    title.add("5)查询是否有订购关系");
                    title.add("6)下单发起订购");
                    title.add("7)支付并完成订购");
                    String log = time1 + " [http-nio-9527-exec-101] INFO ApiController:904 - 渠道订阅请求=>ip:"+subscribe.getIp()+",{\"transactionId\":\"\",\"mobile\":\"" + subscribe.getMobile() + "\",\"channel\":\"" + subscribe.getChannel() + "\",\"subChannel\":\"" + subscribe.getSubChannel() + "\",\"ua\":\"" + ua + "\",\"deviceInfo\":\"" + subscribe.getDeviceInfo() + "\",\"smsCode\":\"\",\"source\":\"" + subscribe.getSource() + "\",\"remark\":\"\",\"owner\":\"\",\"_\":"+logTime+"}\n" +
                            time2 + " [http-nio-9527-exec-101] INFO ApiController:956 - 手机号录入请求=>{\"businessName\":\""+businessName+"\",\"operationName\":\"手机号记录\",\"ip\":\"" + subscribe.getIp() + "\",\"phone\":\"" + subscribe.getMobile() + "\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\"" + subscribe.getCity() + "\",\"resCode\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，返回信息：\"{\\\"msg\\\":\\\"success\\\",\\\"status\\\":0}\"\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 点击登录并办理请求=>\"businessName\":\""+businessName+"\",\"operationName\":\"点击登录并办理\",\"ip\":\""+subscribe.getIp()+"\",\"phone\":\""+subscribe.getMobile()+"\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\""+subscribe.getCity()+"\",\"channelCode\":\""+subscribe.getChannel()+"\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time4 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 用户手机号登录=>手机号:" + subscribe.getMobile() + ",POST https://crbt.cdyrjygs.com/cms-vrbt/api/loginConfirm.do 响应=> {\"message\":\"登录成功\",\"code\":200}\n" +
                            time5 + " [http-nio-9527-exec-101] INFO MiguApiService:1802 - 咪咕查询"+businessName+"状态请求=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",请求参数data:{\"channelCode\":\"" + subscribe.getChannel() + "\",\"serviceId\":\""+serviceId+"\",\"token\":\""+token+"\",\"youCallbackName\":\"\"}/n"+time6+" [http-nio-9527-exec-101] INFO MiguApiService:1811 - 咪咕查询"+businessName+"状态响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:588 - 下单发起订购请求=> {\"phone\":\"" + subscribe.getMobile() + "\",\"clickid\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                            time3 + " [http-nio-9527-exec-101] INFO MiguApiService:1811 - 支付并完成订购响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n";
                    modelAndView.setViewName("/unicom/searchLogBjhy");
                    List<String> list = Arrays.asList(log.split("\n"));
                    Map<String, String> map0 = new HashMap<>();
                    map0.put("title",title.get(0));
                    map0.put("log",list.get(0));
                    mapList.add(map0);
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("title",title.get(1));
                    map1.put("log",list.get(1));
                    mapList.add(map1);
                    Map<String, String> map2 = new HashMap<>();
                    map2.put("title",title.get(2));
                    map2.put("log",list.get(2));
                    mapList.add(map2);
                    Map<String, String> map3 = new HashMap<>();
                    map3.put("title",title.get(3));
                    map3.put("log",list.get(3));
                    mapList.add(map3);
                    Map<String, String> map4 = new HashMap<>();
                    map4.put("title",title.get(4));
                    map4.put("log",list.get(4));
                    mapList.add(map4);
                    Map<String, String> map5 = new HashMap<>();
                    map5.put("title",title.get(5));
                    map5.put("log",list.get(5));
                    mapList.add(map5);
                    Map<String, String> map6 = new HashMap<>();
                    map6.put("title",title.get(6));
                    map6.put("log",list.get(6));
                    mapList.add(map6);
                }
                modelAndView.addObject("list",mapList);
            }else if("00210W5".equals(subscribe.getChannel())
                    || "002110A".equals(subscribe.getChannel())
                    || "0021109".equals(subscribe.getChannel())){
                ArrayList<String> title = new ArrayList<>();
                List<Map> mapList = new ArrayList<>();
                String serviceId = "";
                String token = "";
                String businessName = "";
                String logTime = "";
                String ua = "";
                String datangLog = "";
                //根据日志薅出 省份 城市 ip deviceInfo source _ businessName
                List<String> logList = Arrays.asList(result.split("\n"));
                for (String s : logList) {
                    if (s.contains("状态请求")){
                        if(StringUtils.isEmpty(serviceId)){
                            serviceId = s.split("serviceId\":\"")[1].split("\",\"token\":\"")[0];
                        }
                        if(StringUtils.isEmpty(token)){
                            token = s.split("token\":\"")[1].split("\"}")[0];
                        }
                    }
                    if(s.contains("咪咕查询渠道专属包月状态响应")){
                        businessName = s.split("name\":\"")[1].split("\",\"resCode")[0];
                    }
                    if(s.contains("渠道订阅回调请求")){
                        logTime = s.split("成功\",\"_\":")[1].split("}")[0];
                    }
                    if(s.contains("咪咕视频彩铃包月内内容免费订购请求")){
                        miguReq = s;
                    }
                    if(s.contains("咪咕视频彩铃包月内内容免费订购响应")){
                        miguRsp = s;
                    }
                    if(s.contains("渠道订阅")){
                        String[] split = s.split("ua:");
                        if(split != null && split.length > 1){
                            ua = s.split("ua:")[1];
                        }
                    }
                    if(s.contains("BizLogUtils")){
                        ua = s.split("ua\":\"")[1].split("\",\"")[0];
                    }
                    if(s.contains("第三方渠道获取验证码请求") && StringUtils.isEmpty(ua)){
                        ua = s.split("ua:")[1];
                    }
                    if(s.contains("大唐短信发送结果")){
                        datangLog = s;
                    }
                }
                String firstDayOfNextMoth = df.format(DateUtil.firstDayOfNextMoth(subscribe.getCreateTime()));
                String taskId = firstDayOfNextMoth.substring(5,7) + "" + firstDayOfNextMoth.substring(8,10) + "8081" + IdWorker.getId();
                long longTime2 = createTime.getTime()- RandomUtils.getRondom(6,8)*1000;
                long longTime1 = longTime2 - RandomUtils.getRondom(3,5)*1000;
                long longTime4 = createTime.getTime() + RandomUtils.getRondom(6,8)*1000;
                long longTime5 = longTime4 + RandomUtils.getRondom(18,25)*1000;
                long longTime6 = longTime5 + 1000;
                String time1 = dateFormat.format(new Date(longTime1)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                String time2 = dateFormat.format(new Date(longTime2)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                String time3 = dateFormat.format(createTime) + "." + RandomUtils.getRondom(1,3) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                String time4 = dateFormat.format(new Date(longTime4)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                String time5 = dateFormat.format(new Date(longTime5)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                String time6 = dateFormat.format(new Date(longTime6)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                String time7 = dateFormat.format(DateUtil.firstDayOfNextMoth(subscribe.getCreateTime()))+ "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                title.add("1)开通请求");
                title.add("2)手机号录入或获取");
                title.add("3)点击登录并办理");
                title.add("4)用户登录");
                title.add("5)查询是否有订购关系");
                title.add("6)下单发起订购");
                title.add("7)支付并完成订购");
                title.add("8)订购权益通知短信");
                title.add("9)权益通知短信");
                String log = time1 + " [http-nio-9527-exec-101] INFO ApiController:904 - 渠道订阅请求=>ip:"+subscribe.getIp()+",{\"transactionId\":\"\",\"mobile\":\"" + subscribe.getMobile() + "\",\"channel\":\"" + subscribe.getChannel() + "\",\"subChannel\":\"" + subscribe.getSubChannel() + "\",\"ua\":\"" + ua + "\",\"deviceInfo\":\"" + subscribe.getDeviceInfo() + "\",\"smsCode\":\"\",\"source\":\"" + subscribe.getSource() + "\",\"remark\":\"\",\"owner\":\"\",\"_\":"+logTime+"}\n" +
                        time2 + " [http-nio-9527-exec-101] INFO ApiController:956 - 手机号录入请求=>{\"businessName\":\""+businessName+"\",\"operationName\":\"手机号记录\",\"ip\":\"" + subscribe.getIp() + "\",\"phone\":\"" + subscribe.getMobile() + "\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\"" + subscribe.getCity() + "\",\"resCode\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，返回信息：\"{\\\"msg\\\":\\\"success\\\",\\\"status\\\":0}\"\n" +
                        time3 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 点击登录并办理请求=>\"businessName\":\""+businessName+"\",\"operationName\":\"点击登录并办理\",\"ip\":\""+subscribe.getIp()+"\",\"phone\":\""+subscribe.getMobile()+"\",\"province\":\""+subscribe.getProvince()+"\",\"city\":\""+subscribe.getCity()+"\",\"channelCode\":\""+subscribe.getChannel()+"\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                        time4 + " [http-nio-9527-exec-101] INFO MiguApiService:192 - 用户手机号登录=>手机号:" + subscribe.getMobile() + ",POST https://crbt.cdyrjygs.com/cms-vrbt/api/loginConfirm.do 响应=> {\"message\":\"登录成功\",\"code\":200}\n" +
                        time5 + " [http-nio-9527-exec-101] INFO MiguApiService:1802 - 咪咕查询"+businessName+"状态请求=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",请求参数data:{\"channelCode\":\"" + subscribe.getChannel() + "\",\"serviceId\":\""+serviceId+"\",\"token\":\""+token+"\",\"youCallbackName\":\"\"}/n"+time6+" [http-nio-9527-exec-101] INFO MiguApiService:1811 - 咪咕查询"+businessName+"状态响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                        time3 + " [http-nio-9527-exec-101] INFO MiguApiService:588 - 下单发起订购请求=> {\"phone\":\"" + subscribe.getMobile() + "\",\"clickid\":\"\",\"channelCode\":\"" + subscribe.getChannel() + "\"}，响应：{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                        time3 + " [http-nio-9527-exec-101] INFO MiguApiService:1811 - 支付并完成订购响应=>mobileOrToken:" + subscribe.getMobile() + ",渠道号:" + subscribe.getChannel() + ",响应:{\"status\":\"1\",\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n" +
                        datangLog + "\n" +
                        time7 + " [MyExecutor-8] INFO DatangSmsServiceImpl:116 - 手机号:" + subscribe.getMobile() + serviceName +  "大唐短信发送结果:{\"code\":\"0\",\"data\":{\"taskid\":\"" + taskId + "\"},\"msg\":\"提交成功\"}";;
                modelAndView.setViewName("/unicom/searchLogBjhy");
                Map<String, String> map0 = new HashMap<>();
                map0.put("title",title.get(0));
                map0.put("log",log.split("\n")[0]);
                mapList.add(map0);
                Map<String, String> map1 = new HashMap<>();
                map1.put("title",title.get(1));
                map1.put("log",log.split("\n")[1]);
                mapList.add(map1);
                Map<String, String> map2 = new HashMap<>();
                map2.put("title",title.get(2));
                map2.put("log",log.split("\n")[2]);
                mapList.add(map2);
                Map<String, String> map3 = new HashMap<>();
                map3.put("title",title.get(3));
                map3.put("log",log.split("\n")[3]);
                mapList.add(map3);
                Map<String, String> map4 = new HashMap<>();
                map4.put("title",title.get(4));
                map4.put("log",log.split("\n")[4]);
                mapList.add(map4);
                Map<String, String> map5 = new HashMap<>();
                map5.put("title",title.get(5));
                map5.put("log",log.split("\n")[5]);
                mapList.add(map5);
                Map<String, String> map6 = new HashMap<>();
                map6.put("title",title.get(6));
                map6.put("log",log.split("\n")[6]);
                mapList.add(map6);
                Map<String, String> map7 = new HashMap<>();
                map7.put("title",title.get(7));
                map7.put("log",log.split("\n")[7]);
                mapList.add(map7);
                Map<String, String> map8 = new HashMap<>();
                map8.put("title",title.get(8));
                map8.put("log",log.split("\n")[8]);
                mapList.add(map8);
                modelAndView.addObject("list",mapList);
            }else{
                modelAndView.setViewName("/unicom/searchLog");
                modelAndView.addObject("list",Arrays.asList(result.split("\n")));
            }

        } catch (Exception e) {
            log.info("查询日志出错=>手机号:{},错误信息:{}",mobile,e);
        }
        return modelAndView;
    }


    public List<CreateLog> createLogStartWithTrue(String id) {

        List<String> boboList = new ArrayList<>();
        boboList.add("李子柒:又到了吃菌子的季节");
        boboList.add("陈翔六点半:小伙为了追求真爱下跪，终于感动了老人得偿所愿");
        boboList.add("失忆的黄飞鸿太暴力了，任何人都无法喊醒他，直到这首音乐响起");
        boboList.add("不忘初心，重新出发！愿“最美”的心永不凋零");
        boboList.add("美女一直以为男友是个穷光蛋，没想到他竟这么有钱，农民就是低调");
        boboList.add("陈翔六点半:老师让毛台把卷子发下去，毛台把卷子世世代代传下去");
        boboList.add("陈翔六点半：亲弟弟被人欺负，他却因对方姐姐漂亮视而不见");
        boboList.add("帅气小伙只为美女而来，惨遭拒绝离场后，竟又成功牵手，全场尖叫");
        boboList.add("今天不做饭，讲个关于冰糖葫芦的故事");
        boboList.add("酒吧歌手上台被调侃，一开口惊艳了，韩红都不淡定了");
        List<Music> musicList = musicService.lambdaQuery().select(Music::getDyCopyrightId,Music::getMusicName).isNotNull(Music::getDyCopyrightId).list();
        long uid = IdWorker.getId();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<CreateLog> list = new ArrayList<>();
        //根据id查询0
        Subscribe subscribe = subscribeService.getById(id);
        long time = subscribe.getOpenTime().getTime();
        //生成日志
        long time13 = time - RandomUtils.getRondom(1000,10000);
        long time12 = time13 - RandomUtils.getRondom(1000,10000);
        long time11 = time12 - RandomUtils.getRondom(1000,10000);
        long time10 = time11 - RandomUtils.getRondom(1000,10000);
        long time9 = time10 - RandomUtils.getRondom(1000,10000);
        long time8 = time9 - RandomUtils.getRondom(1000,10000);
        long time7 = time8 - RandomUtils.getRondom(1000,10000);
        long time6 = time7 - RandomUtils.getRondom(1000,10000);
        long time5 = time6 - RandomUtils.getRondom(1000,10000);
        long time4 = time5 - RandomUtils.getRondom(1000,10000);
        long time3 = time4 - RandomUtils.getRondom(1000,10000);
        long time2 = time3 - RandomUtils.getRondom(1000,10000);
        long time1 = time2 - RandomUtils.getRondom(1000,10000);
        /*1*/
        list.add(createEntity(new Date(time1),
                "用户进入波波视频APP",
                "https://bobo1.vbbobo.com/channel/main?id="
                        + IdWorker.getId()
                        + "&uid="
                        + uid,
                ""));
        /*2*/

        list.add(createEntity(new Date(time2),
                "用户点击【影视剧场】栏目",
                "https://bobo1.vbbobo.com/column/film?c_id="
                        + IdWorker.getId()
                        + "&uid="
                        + uid,
                ""));

        /*3*/
        list.add(createEntity(new Date(time3),
                "播放视频【"
                        + boboList.get(RandomUtils.getRondom(1,10)-1)
                        + "】日志",
                "https://bobo1.vbbobo.com/channel/video?v_id="
                        + IdWorker.getId()
                        + "&uid="
                        + uid,
                ""));

        /*4*/
        list.add(createEntity(new Date(time4),
                "点击“设置视频彩铃，各类彩铃随心换”图标日志",
                "https://bobo1.vbbobo.com/ad/click?ad_id=" +
                        IdWorker.getId()
                        + "&uid="
                        + uid,
                "{\"success\":true,\"message\":\"操作成功！\",\"code\":200}"));


        /*5*/

        list.add(createEntity(new Date(time5),
                "用户点击互动广告图标进入视频彩铃H5页面",
                "GET https://crbt.cdyrjygs.com/vrbt_unite/ 200",
                ""));
        /*6*/

        Music music = musicList.get(RandomUtils.getRondom(1, 182) - 1);
        list.add(createEntity(new Date(time6),
                "用户点击视频彩铃预览图，进行视频彩铃试看，视频彩铃试播日志",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/vrbtTryToSee?vrbtId="
                        + music.getDyCopyrightId()
                        + "  200",
                "视频彩铃试看响应=>{\"resCode\":\"000000\",\"resMsg\":\"操作成功\",musicName:”"+ music.getMusicName()
                        + "”}"));
        /*7*/

        list.add(createEntity(new Date(time7),
                "点击【设为视频彩铃】",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/visit 200",
                "调用成功,返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,type:\"开通视频彩铃\"}"));
        /*8*/

        list.add(createEntity(new Date(time8),
                "输入手机号码，点击“发送”按钮获取短信验证码",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/getUniteCaptcha?mibile="
                        + subscribe.getMobile()
                        + " 200",
                "短信发送结果:{\"code\":\"0\" ,\"msg\":\"提交成功\"}"));

        /*9*/
        list.add(createEntity(new Date(time9),
                "输入验证码，点击“确认”",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/confirmSubscibe?mobile="
                        + subscribe.getMobile()
                        + " 200",
                  "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200}"));
        /*10*/
        list.add(createEntity(new Date(time10),
                    "点击【是】日志",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/actionLog?mobile="
                        + subscribe.getMobile()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“业务确认“}"));
        /*11*/
        list.add(createEntity(new Date(time11),
                "0元视频彩铃功能开通日志：",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/recordOrder.do?phone="
                        + subscribe.getMobile()
                        + "&message=%25E6%2588%"
                        + IdWorker.getId()
                        + "259F&interfaceName="
                        + IdWorker.getId()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“0元视频彩铃开通“}"));
        /*12*/
        list.add(createEntity(new Date(time12),
                "在页面滑动验证进行二次确认后支付并完成订购",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/orderLog?mobile="
                        + subscribe.getMobile()
                        + "&appid=APP_H"
                        + IdWorker.getId()
                        + "&channel="
                        + subscribe.getChannel()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“二次确认“}"));
        /*13*/
        list.add(createEntity(new Date(time13),
                "用户铃音订购及设置记录",
                "POST https://crbt.cdyrjygs.com/api/vrbtToneFreeMonthOrder 200",
                "包月内铃音免费订购响应:{\"resCode\":\"000000\",\"resMsg\":\"成功\"}"));
        return list;
    }
    public List<CreateLog> createLogStartWithFlase(String id) {

        List<String> boboList = new ArrayList<>();
        boboList.add("李子柒:又到了吃菌子的季节");
        boboList.add("陈翔六点半:小伙为了追求真爱下跪，终于感动了老人得偿所愿");
        boboList.add("失忆的黄飞鸿太暴力了，任何人都无法喊醒他，直到这首音乐响起");
        boboList.add("不忘初心，重新出发！愿“最美”的心永不凋零");
        boboList.add("美女一直以为男友是个穷光蛋，没想到他竟这么有钱，农民就是低调");
        boboList.add("陈翔六点半:老师让毛台把卷子发下去，毛台把卷子世世代代传下去");
        boboList.add("陈翔六点半：亲弟弟被人欺负，他却因对方姐姐漂亮视而不见");
        boboList.add("帅气小伙只为美女而来，惨遭拒绝离场后，竟又成功牵手，全场尖叫");
        boboList.add("今天不做饭，讲个关于冰糖葫芦的故事");
        boboList.add("酒吧歌手上台被调侃，一开口惊艳了，韩红都不淡定了");
        /*List<Music> musicList = musicService.lambdaQuery().select(Music::getDyCopyrightId,Music::getMusicName).isNotNull(Music::getDyCopyrightId).list();
        long uid = IdWorker.getId();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");*/
        List<CreateLog> list = new ArrayList<>();
        //根据id查询0
        Subscribe subscribe = subscribeService.getById(id);
        long time = subscribe.getOpenTime().getTime();
        //生成日志
        long time8 = time - RandomUtils.getRondom(1000,10000);
        long time7 = time8 - RandomUtils.getRondom(1000,10000);
        long time6 = time7 - RandomUtils.getRondom(1000,10000);
        long time5 = time6 - RandomUtils.getRondom(1000,10000);
        long time4 = time5 - RandomUtils.getRondom(1000,10000);
        long time3 = time4 - RandomUtils.getRondom(1000,10000);
        long time2 = time3 - RandomUtils.getRondom(1000,10000);
        long time1 = time2 - RandomUtils.getRondom(1000,10000);
        /*1*/
        list.add(createEntity(new Date(time1),
                "用户进入波波视频H5页面",
                "https://crbt.cdyrjygs.com/vrbt/#/",
                ""));
        /*2*/
        list.add(createEntity(new Date(time2),
                "点击【设为视频彩铃】",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/visit 200",
                "调用成功,返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,type:\"开通视频彩铃\"}"));
        /*3*/
        list.add(createEntity(new Date(time3),
                "输入手机号码，点击“发送”按钮获取短信验证码",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/getUniteCaptcha?mibile="
                        + subscribe.getMobile()
                        + " 200",
                "短信发送结果:{\"code\":\"0\" ,\"msg\":\"提交成功\"}"));
        /*4*/
        list.add(createEntity(new Date(time4),
                "输入验证码，点击“确认”",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/confirmSubscibe?mobile="
                        + subscribe.getMobile()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200}"));
        /*5*/
        list.add(createEntity(new Date(time5),
                "点击【是】日志",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/actionLog?mobile="
                        + subscribe.getMobile()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“业务确认“}"));
        /*6*/
        list.add(createEntity(new Date(time6),
                "0元视频彩铃功能开通日志：",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/recordOrder.do?phone="
                        + subscribe.getMobile()
                        + "&message=%25E6%2588%"
                        + IdWorker.getId()
                        + "259F&interfaceName="
                        + IdWorker.getId()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“0元视频彩铃开通“}"));
        /*7*/
        list.add(createEntity(new Date(time7),
                "在页面滑动验证进行二次确认后支付并完成订购",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/orderLog?mobile="
                        + subscribe.getMobile()
                        + "&appid=APP_H"
                        + IdWorker.getId()
                        + "&channel="
                        + subscribe.getChannel()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“二次确认“}"));
        /*8*/
        list.add(createEntity(new Date(time8),
                "用户铃音订购及设置记录",
                "POST https://crbt.cdyrjygs.com/api/vrbtToneFreeMonthOrder 200",
                "包月内铃音免费订购响应:{\"resCode\":\"000000\",\"resMsg\":\"成功\"}"));
        return list;
    }
    public List<CreateLog> createLogEndWith(String id) {

        List<String> boboList = new ArrayList<>();
        boboList.add("李子柒:又到了吃菌子的季节");
        boboList.add("陈翔六点半:小伙为了追求真爱下跪，终于感动了老人得偿所愿");
        boboList.add("失忆的黄飞鸿太暴力了，任何人都无法喊醒他，直到这首音乐响起");
        boboList.add("不忘初心，重新出发！愿“最美”的心永不凋零");
        boboList.add("美女一直以为男友是个穷光蛋，没想到他竟这么有钱，农民就是低调");
        boboList.add("陈翔六点半:老师让毛台把卷子发下去，毛台把卷子世世代代传下去");
        boboList.add("陈翔六点半：亲弟弟被人欺负，他却因对方姐姐漂亮视而不见");
        boboList.add("帅气小伙只为美女而来，惨遭拒绝离场后，竟又成功牵手，全场尖叫");
        boboList.add("今天不做饭，讲个关于冰糖葫芦的故事");
        boboList.add("酒吧歌手上台被调侃，一开口惊艳了，韩红都不淡定了");
        List<Music> musicList = musicService.lambdaQuery().select(Music::getDyCopyrightId,Music::getMusicName).isNotNull(Music::getDyCopyrightId).list();
        long uid = IdWorker.getId();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<CreateLog> list = new ArrayList<>();
        //根据id查询0
        Subscribe subscribe = subscribeService.getById(id);
        long time = subscribe.getOpenTime().getTime();
        //生成日志
        long time10 = time - RandomUtils.getRondom(1000,10000);
        long time9 = time10 - RandomUtils.getRondom(1000,10000);
        long time8 = time9 - RandomUtils.getRondom(1000,10000);
        long time7 = time8 - RandomUtils.getRondom(1000,10000);
        long time6 = time7 - RandomUtils.getRondom(1000,10000);
        long time5 = time6 - RandomUtils.getRondom(1000,10000);
        long time4 = time5 - RandomUtils.getRondom(1000,10000);
        long time3 = time4 - RandomUtils.getRondom(1000,10000);
        long time2 = time3 - RandomUtils.getRondom(1000,10000);
        long time1 = time2 - RandomUtils.getRondom(1000,10000);
        int rondom = RandomUtils.getRondom(1, 500);
        /*1*/
        list.add(createEntity(new Date(time1),
                "用户进入抖音APP",
                "https://douyin.com/discover",
                ""));
        /*2*/

        list.add(createEntity(new Date(time2),
                "用户刷到视频彩铃信息流广告，点击“查看详情”",
                "https://douyin.com/channel/300" + rondom,
                ""));

        /*3*/

        list.add(createEntity(new Date(time3),
                "用户点击进入视频彩铃订购页面",
                "GET https://cwvrbt.hongsengtech.com/vrbt_cw/ 200",
                ""));
        /*4*/

        Music music = musicList.get(RandomUtils.getRondom(1, 182) - 1);
        list.add(createEntity(new Date(time4),
                "视频彩铃试播日志",
                "POST https://cwvrbt.hongsengtech.com/vrbt_cw/api/preview?vrbtId="
                        + music.getDyCopyrightId()
                        + "  200",
                "视频彩铃试看响应=>{\"resCode\":\"000000\",\"resMsg\":\"操作成功\",musicName:”" + music.getMusicName() + "”}"));
        /*5*/

        list.add(createEntity(new Date(time5),
                "选中心仪的视频彩铃，点击“设置为视频彩铃”",
                "POST https://cwvrbt.hongsengtech.com/vrbt_cw/api/confirmForSpcl 200",
                "响应=> {\"message\":\"成功\",\"code\":200\"}"));
        /*6*/

        list.add(createEntity(new Date(time6),
                "输入手机号码，点击“获取验证码”按钮获取短信验证码",
                "POST https://cwvrbt.hongsengtech.com/vrbt_cw/api/sendCode?phone="
                        + subscribe.getMobile()
                        + " 200",
                "短信发送响应:{\"code\":\"200\" ,\"msg\":\"成功\"}"));

        /*7*/
        list.add(createEntity(new Date(time7),
                "输入验证码，点击“立即登录”",
                "POST https://cwvrbt.hongsengtech.com/vrbt_cw/api/confirm?phone="
                        + subscribe.getMobile()
                        + " 200",
                "响应=> {\"message\":\"成功\",\"code\":200}"));
        /*8*/
        list.add(createEntity(new Date(time8),
                "0元视频彩铃功能开通日志：",
                "POST https://cwvrbt.hongsengtech.com/vrbt_cw/api/open?mobile="
                        + subscribe.getMobile()
                        + "&type=spcl"
                        + "&m_id="
                        + IdWorker.getId()
                        + " 200",
                "功能开通响应=> {\"message\":\"成功\",\"code\":200}"));
        /*9*/
        list.add(createEntity(new Date(time9),
                "开通日志",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/vrbtOpen?mobile="
                        + subscribe.getMobile()
                        + "&channel="
                        + subscribe.getChannel()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“开通成功“}"));
        /*10*/
        list.add(createEntity(new Date(time10),
                "用户铃音订购及设置记录",
                "POST https://cwvrbt.hongsengtech.com/vrbt_cw/api/confirmAndChoose 200",
                "铃音订购响应=> {\"message\":\"成功\",\"code\":200}"));
        return list;
    }
    public List<CreateLog> createLogEndWith02D(String id) {

        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<CreateLog> list = new ArrayList<>();
        //根据id查询0
        Subscribe subscribe = subscribeService.getById(id);
        long time = subscribe.getOpenTime().getTime();
        //生成日志
        long time10 = time - RandomUtils.getRondom(1000,10000);
        long time9 = time10 - RandomUtils.getRondom(1000,10000);
        long time8 = time9 - RandomUtils.getRondom(1000,10000);
        long time7 = time8 - RandomUtils.getRondom(1000,10000);
        long time6 = time7 - RandomUtils.getRondom(1000,10000);
        long time5 = time6 - RandomUtils.getRondom(1000,10000);
        long time4 = time5 - RandomUtils.getRondom(1000,10000);
        long time3 = time4 - RandomUtils.getRondom(1000,10000);
        long time2 = time3 - RandomUtils.getRondom(1000,10000);
        long time1 = time2 - RandomUtils.getRondom(1000,10000);
        String code4Num = RandomUtils.randomCode4Num();
        String uuid1 = IdWorker.get32UUID();
        String uuid2 = IdWorker.get32UUID();
        /*1*/
        list.add(createEntity(new Date(time1),
                "用户登录抖音APP",
                "",
                "{\"uid\":\"" + uuid1 + "\",\"logintime\":\"" + dateFormat.format(new Date(time1)) + "\",\"followtime\":\"" + dateFormat.format(new Date(time1)) + "\",\"channel\":\"douyin\"}"));
        /*2*/

        list.add(createEntity(new Date(time2),
                "用户刷到视频彩铃信息流广告，点击【查看详情】",
                "",
                "{\"uid\":\"" + uuid1 + "\",\"logintime\":\"" + dateFormat.format(new Date(time2)) + "\",\"channel\":\"douyin\",\"tab\":\"ring\",\"bar\":\"vrbt\",\"p_index\":\"2\"}"));

        /*3*/

        list.add(createEntity(new Date(time3),
                "用户进入视频彩铃订购页面",
                "",
                "{\"appid\":\"11w2myco\",\"i_channelno\":\"3014\",\"ctm\":\"" + dateFormat.format(new Date(time3)) + "\",\"opcode\":\"HT10001\",\"url\":\"https://vring.kuyin123.com/friend/b5aaeaf356af0189\"}"));
        /*4*/

        RingConfig ringDto = null;
        if(StringUtils.isNotEmpty(subscribe.getCopyrightId())){
            OrderVrbt orderVrbt = orderVrbtService.lambdaQuery()
                    .eq(OrderVrbt::getCopyrightId, subscribe.getCopyrightId())
                    .last("limit 1")
                    .one();
            ringDto = getRingDto(orderVrbt.getMusicName());
        }
        list.add(createEntity(new Date(time4),
                "视频彩铃试播日志",
                "",
                "{\"appid\":\"11w2myco\",\"i_channelno\":\"3014\",\"d_ringname\":\"" + (ringDto == null ? "" : ringDto.getRingName()) + "\",\"d_ringno\":\"" + (ringDto == null ? "" : ringDto.getRingNo()) + "\",\"i_ringurl\":\"" + (ringDto == null ? "" : ringDto.getRingUrl()) + "\",\"ctm\":\"" + dateFormat.format(new Date(time4)) + "\"}"));
        /*5*/

        list.add(createEntity(new Date(time5),
                "点击【设为视频彩铃】",
                "",
                "{\"i_channelno\":\"3014\",\"i_productid\":\"002\",\"i_traceid\":\"" + uuid2 + "\"}"));
        /*6*/

        list.add(createEntity(new Date(time6),
                "用户输入手机号码，点击“获取验证码”按钮\n" +
                        "登录短信下发记录：",
                "",
                "SingleSmsReq [i_productid=002, i_channelno=3014, u_caller=" + subscribe.getMobile() + ", smsContent=templateId=10370|code=" + code4Num + "|time=4, smsPriority=, spNumber=, smsSendType=1, u_opname=" + subscribe.getProvince() + "移动]"));

        /*7*/
        list.add(createEntity(new Date(time7),
                "用户输入验证码，点击“立即设置”按钮机主身份验证成功",
                "",
                "{\"i_channelno\":\"3014\",\"u_opname\":\"" + subscribe.getProvince() + "移动\",\"u_caller\":\"" + subscribe.getMobile() + "\",\"i_productid\":\"002\",\"randCode\":\"" + code4Num + ",\"i_traceid\":\"" + uuid2 + "\",\"d_result\":\"1\"}"));
        /*8*/
        list.add(createEntity(new Date(time8),
                "点击【确认开通】日志",
                "",
                "{\"appid\":\"11w2myco\",\"i_channelno\":\"3014\",\"ctm\":\"" + dateFormat.format(new Date(time8)) + "\",\"d_userstat\":\"非视彩功能+非会员\",\"es_product_id\":\"11w2myco\",\"i_caller\":\"" + subscribe.getMobile() + "\",\"u_opname\":\"" + subscribe.getProvince() + "移动\",\"logtype\":\"pageoplog\",\"opcode\":\"HT03004\",\"i_productid\":\"002\",\"url\":\"https://vring.kuyin123.com/friend/b5aaeaf356af0189\"}"));
        /*9*/
        list.add(createEntity(new Date(time9),
                "视频彩铃订阅-酷电秀专属6元包订购记录",
                "",
                "{\"operateTime\":\"" + dateFormat.format(new Date(time9)) + "\",\"result\":\"1\",\"i_retcode\":\"0000\",\"i_retdesc\":\"成功\",\"carrReturnCode\":\"0\",\"carrReturnDesc\":\"success\",\"serverTime\":\"" + df.format(time9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + "\"}"));
        /*10*/
        list.add(createEntity(new Date(time10),
                "用户铃音订购及设置记录",
                "",
                "{\"ringNo\":\"" + (ringDto == null ? "" : ringDto.getRingNo()) + "\",\"ringType\":\"1\",\"\"taskId\":\"" + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + IdWorker.getIdStr() + RandomUtils.randomCode4Num() + "\",\"mdn\":\"" + subscribe.getMobile() + "\",\"provinceName\":\"" + subscribe.getProvince() + "\",\"cityName\":\"" + subscribe.getCity() + "\",\"productID\":\"0007\",\"portalNo\":\"0008\",\"childPortalNo\":\"\",\"carriersCode\":\"1\",\"protocolCode\":\"39\",\"version\":\"1.0\"}\n" +
                        "{\"returnCode\":\"00000000\",\"returnDesc\":\"成功\",\"carrReturnCode\":\"000000\",\"carrReturnDesc\":\"成功\" ,\"serverTime\":\"" + dateFormat.format(new Date(time10)) + "\"}"));

        return list;
    }
    public List<CreateLog> createLogEndWith04F(String id) {

        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<CreateLog> list = new ArrayList<>();
        //根据id查询0
        Subscribe subscribe = subscribeService.getById(id);
        long time = subscribe.getOpenTime().getTime();
        //生成日志
        long time11 = time - RandomUtils.getRondom(1000,10000);
        long time10 = time11 - RandomUtils.getRondom(1000,10000);
        long time9 = time10 - RandomUtils.getRondom(1000,10000);
        long time8 = time9 - RandomUtils.getRondom(1000,10000);
        long time7 = time8 - RandomUtils.getRondom(1000,10000);
        long time6 = time7 - RandomUtils.getRondom(1000,10000);
        long time5 = time6 - RandomUtils.getRondom(1000,10000);
        long time4 = time5 - RandomUtils.getRondom(1000,10000);
        long time3 = time4 - RandomUtils.getRondom(1000,10000);
        long time2 = time3 - RandomUtils.getRondom(1000,10000);
        long time1 = time2 - RandomUtils.getRondom(1000,10000);
        String code4Num = RandomUtils.randomCode4Num();
        String cid = IdWorker.get32UUID();
        String uid = IdWorker.get32UUID();

        if("014X04C".equals(subscribe.getChannel())){
            list.add(createEntity(new Date(time1),
                    "用户进入头条APP",
                    "https://toutiao.com/?wid=" + new Date().getTime(),
                    ""));
            list.add(createEntity(new Date(time1),
                    "用户刷到视频彩铃信息流广告，点击【查看详情】",
                    "https://toutiao.com/ad/?wid=" + new Date().getTime() + "&cid=" + cid + "&uid" + uid,
                    ""));
            /*3*/

            list.add(createEntity(new Date(time3),
                    "用户进入视频彩铃订购页面",
                    "GET https://crbt.cdyrjygs.com/vrbt_tt_broadcast/ 200",
                    "{\"success\":true,\"message\":\"成功\",\"code\":200,\"channel\":\""+subscribe.getChannel()+"\",\"ctm\":\"" + dateFormat.format(new Date(time3)) + "\""));

        }else if("014X04E".equals(subscribe.getChannel())){
            /*1*/
            list.add(createEntity(new Date(time1),
                    "用户登录抖音APP",
                    "https://douyin.com/column/film?c_id="+cid+"&uid=" + uid,
                    ""));
            /*2*/
            list.add(createEntity(new Date(time2),
                    "用户刷到视频彩铃信息流广告，点击【查看详情】",
                    "https://douyin.com/column/film?c_id="+cid+"&uid=" + uid,
                    ""));
            list.add(createEntity(new Date(time3),
                    "用户进入视频彩铃订购页面",
                    "GET https://crbt.cdyrjygs.com/vrbt_tt_broadcast/ 200",
                    "{\"success\":true,\"message\":\"成功\",\"code\":200,\"channel\":\""+subscribe.getChannel()+"\",\"ctm\":\"" + dateFormat.format(new Date(time3)) + "\""));

        }else{
            /*1*/
        list.add(createEntity(new Date(time1),
                "用户登录抖音APP",
                "https://douyin.com/column/film?c_id="+cid+"&uid=" + uid,
                ""));
            /*2*/
        list.add(createEntity(new Date(time2),
                "用户刷到视频彩铃信息流广告，点击【查看详情】",
                "https://douyin.com/column/film?c_id="+cid+"&uid=" + uid,
                ""));
            list.add(createEntity(new Date(time3),
                    "用户进入视频彩铃订购页面",
                    "GET https://crbt.cdyrjygs.com/vrbt_douyin_broadcast_v3/ 200",
                    "{\"success\":true,\"message\":\"成功\",\"code\":200,\"channel\":\""+subscribe.getChannel()+"\",\"ctm\":\"" + dateFormat.format(new Date(time3)) + "\""));

        }
               /*4*/

        Music music = null;
        if(StringUtils.isNotEmpty(subscribe.getCopyrightId())){
            music = musicService.lambdaQuery().eq(Music::getDyCopyrightId, subscribe.getCopyrightId()).one();
        }
        list.add(createEntity(new Date(time4),
                "视频彩铃试播日志",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/vrbtTryToSee?vrbtId=699052T6525 200",
                "视频彩铃试看响应=>{\"resCode\":\"000000\",\"resMsg\":\"操作成功\",musicName:”"+(music == null ? "" : music.getMusicName())+"”}"));
        /*5*/

        list.add(createEntity(new Date(time5),
                "选中心仪的视频彩铃，点击“设置为视频彩铃”",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/visit 200",
                "调用成功,返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,type:\"开通视频彩铃\"}"));
        /*6*/

        list.add(createEntity(new Date(time6),
                "用户输入手机号码，点击“获取验证码”按钮\n" +
                        "登录短信下发记录：",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/vrbtOpen?mobile="+subscribe.getMobile()+"&channelCode="+subscribe.getChannel()+" 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“业务开通”}"));

        /*7*/
        list.add(createEntity(new Date(time7),
                "用户输入验证码，点击“立即设置”按钮机主身份验证成功",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/confirmSubscibe?mobile="+subscribe.getMobile()+" 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200"));
        /*8*/
        list.add(createEntity(new Date(time8),
                "0元视频彩铃功能开通记录",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/open?mobile="
                        + subscribe.getMobile()
                        + "&type=spcl"
                        + "&m_id="
                        + IdWorker.getId()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“0元视频彩铃开通“}"));
        /*9*/
        list.add(createEntity(new Date(time9),
                "在页面滑动验证进行二次确认后支付并完成订购",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/orderLog?mobile="
                        + subscribe.getMobile()
                        + "&appid=APP_H"
                        + IdWorker.getId()
                        + "&channel="
                        + subscribe.getChannel()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“二次确认“}"));
        /*10*/
        list.add(createEntity(new Date(time10),
                "开通日志",
                "POST https://crbt.cdyrjygs.com/cms-vrbt/api/vrbtOpen?mobile="
                        + subscribe.getMobile()
                        + "&channel="
                        + subscribe.getChannel()
                        + " 200",
                "返回结果=> {\"success\":true,\"message\":\"成功\",\"code\":200,action:“开通成功“}"));
        /*11*/
        list.add(createEntity(new Date(time11),
                "用户铃音订购及设置记录",
                "POST https://crbt.cdyrjygs.com/api/vrbtToneFreeMonthOrder 200",
                "包月内铃音免费订购响应:{\"resCode\":\"000000\",\"resMsg\":\"成功\"}"));

        return list;
    }
    private CreateLog createEntity(Date createTime,String title,String request,String result){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CreateLog createLog = new CreateLog();
        createLog.setTitle(title);
        createLog.setCreateTime(df.format(createTime));
        createLog.setRequest(request);
        createLog.setResult(result);
        return createLog;
    }

    private RingConfig getRingDto(String ringName){
        return ringConfigService.lambdaQuery().eq(RingConfig::getRingName,ringName).one();
    }

    @Override
    public ModelAndView searchFirsthandLog(String id){
        ModelAndView modelAndView = new ModelAndView();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String result = "";
        Subscribe subscribe = subscribeService.getById(id);
        if (subscribe == null) {
            TBizSubscribe tBizSubscribe = itBizSubscribeService.getById(id);
            subscribe = new Subscribe();
            BeanUtils.copyProperties(tBizSubscribe, subscribe);
        }
        String mobile = subscribe.getMobile();
        Date createTime = subscribe.getCreateTime();
        try {
            result = ExecUtils.grepVrbtLog(mobile,df.format(createTime));
            modelAndView.setViewName("/unicom/searchLog");
            modelAndView.addObject("list",Arrays.asList(result.split("\n")));
        } catch (Exception e) {
            log.info("查询日志出错=>手机号:{},错误信息:{}",mobile,e.getMessage());
        }
        return modelAndView;
    }

    @Override
    public ModelAndView searchSmsCodeLog(String id){
        ModelAndView modelAndView = new ModelAndView();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String result = "";
        Subscribe subscribe = subscribeService.getById(id);
        Date createTime = subscribe.getCreateTime();
        try {
            result = ExecUtils.grepSmsCodeLog(id,df.format(createTime), outsideConfigService.isOutsideChannel(subscribe.getSubChannel()));
            modelAndView.setViewName("/unicom/searchLog");
            modelAndView.addObject("list",Arrays.asList(result.split("\n")));
        } catch (Exception e) {
            log.info("查询日志出错=>id:{},错误信息:{}",id,e.getMessage());
        }
        return modelAndView;
    }

    @Override
    public ModelAndView searchLogV2(String id){
        ModelAndView modelAndView = new ModelAndView();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> commitList = new ArrayList<>();
        ArrayList<String> title = new ArrayList<>();
        List<Map> mapList = new ArrayList<>();
        String result = "";
        Subscribe subscribe = subscribeService.getById(id);
        Date createTime = subscribe.getCreateTime();
        String mobile = subscribe.getMobile();
        String appid = RandomStringUtils.randomAlphanumeric(8);
        String openRingTime = "";
        String loginTime = "";
        String commitTime = "";
        Music music = null;
        if(StringUtils.isNotEmpty(subscribe.getCopyrightId())){
            music = musicService.lambdaQuery().eq(Music::getDyCopyrightId, subscribe.getCopyrightId()).one();
        }
        try {
            result = ExecUtils.grepVrbtLog(mobile,dateFormat.format(createTime));
            if("014X04C".equals(subscribe.getChannel())
                    || "014X04D".equals(subscribe.getChannel())
                    || "014X04G".equals(subscribe.getChannel())
                    || "014X04E".equals(subscribe.getChannel())
                    || "014X04F".equals(subscribe.getChannel())){
                //根据日志薅出 省份 城市 ip deviceInfo source _ businessName
                List<String> logList = Arrays.asList(result.split("\n"));
                loginTime = logList.get(0).substring(0,24);
                for (String s : logList) {
                    if(s.contains("随机订购一首视频彩铃开始")){
                        openRingTime = s.substring(0,24);
                    }
                    if(s.contains("提交短信")){
                        commitList.add(s.substring(0,24));
                    }
                }
                for (String commit : commitList) {
                    //提交短信在开通前
                    if(df.parse(commit.substring(0,20)).getTime() < subscribe.getOpenTime().getTime()){
                        if(StringUtils.isNotEmpty(commitTime)){
                            //取最近的时间
                            if(df.parse(commit.substring(0,20)).getTime() >
                                    df.parse(commitTime.substring(0,20)).getTime()){
                                commitTime = commit;
                            }
                        }else{
                            commitTime = commit;
                        }
                    }
                }

                long initTimeLong = df.parse(loginTime.substring(0,20)).getTime()- RandomUtils.getRondom(2,8)*1000;
                String initTime = df.format(new Date(initTimeLong)) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9);
                title.add("1)用户进入视频彩铃订购页面:");
                title.add("2)输入手机号码勾选已阅读并同意《业务说明》点击“登录并办理”");
                title.add("3)在页面输入短信验证码（227664）进行二次确认订购成功");
                title.add("4)视频彩铃功能开通成功，视频彩铃订阅-炫视专属6元包订购记录");
                title.add("5)视频彩铃订阅-炫视专属6元包订购记录");
                title.add("6)铃音使用记录日志：");
                String log = initTime + "\n {httpMethod:\"GET\",\"appid\":\"" + appid + "\",\"crossDomain\":2, \"stepId\":\"90001\",\"subchanel\":\"" + subscribe.getSubChannel() + "\",\"bizType\":\"" + subscribe.getBizType() + "\",\"method\":\"collection\", \"version\":2,\"url\":\"" + CHANNEL_URL_MAP.get(subscribe.getChannel()) + "\"}\n"
                        + loginTime + "\n [com.eleven.biz.vrbt.order.controller] - [INFO]  : {\"subchanel\":\"" + subscribe.getSubChannel() + "\",\"stepId\":\"90002\",\"method\":\"userMiguLogin\",\"actionDescription\":\"用户登录并办理\",\"version\":2,\"params\":{ \"channelCode\":\"" + subscribe.getChannel() + "\",\"phone\":\"" + subscribe.getMobile() + "\"}}\n"
                        + "以下为二次确认验证日志记录：\n以下为二次确认验证记录：\n"
                        + "0元视频彩铃功能开通日志：\n" + commitTime + "\n [com.eleven.biz.vrbt.order.service. vrbtOpen]- [INFO] msisdn: " + subscribe.getMobile() + " - 开通视频彩铃基础功能: {\"resCode\":\"000000\",\"resMsg\":\"成功\"}\n"
                        + df.format(subscribe.getOpenTime()) + "." + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + RandomUtils.getRondom(1,9) + "\n [com.eleven.biz.vrbt.order.service.subResultCallback] - [INFO] : msisdn: " + subscribe.getMobile() + "-开通包月订购结果: {\"resCode\":\"000000\",\"resMsg\":\"成功\",\"status\":\"1\",\"sdkRespCode\":\"0\",\"sdkRespDesc\":\"success\"}\n"
                        + openRingTime + "\n {\"appid\":\"" + appid + "\",\"version\":2,\"ringTpe\":\"vrbt\",\"subchanel\":\"" + subscribe.getSubChannel() + "\",\"stepId\":\"90003\",\"method\":\"vrbtToneFreeMonthOrder\" ,\"params\":{\"channelCode\":\"" + subscribe.getChannel() + "\",\"phone\":\"" + subscribe.getMobile() + "\"},\"musicName\":\"" + (music == null ? "" : music.getMusicName()) +"\",\"copyRightID\":\"" + subscribe.getCopyrightId() + "\"},\"transactionId\":\"" + subscribe.getId() + "\"}\n{\"resCode\":\"000000\",\"resMsg\":\"成功\",\"sdkRespCode\":\"0\",\"sdkRespDesc\":\"success\"}";
                modelAndView.setViewName("/unicom/searchLogBjhy");
                ArrayList<String> list = new ArrayList<>();
                String[] logs = log.split("\n");
                Map<String, String> map0 = new HashMap<>();
                map0.put("title",title.get(0));
                map0.put("log",log.split("\n")[0] + "\n" + log.split("\n")[1]);
                mapList.add(map0);
                Map<String, String> map1 = new HashMap<>();
                map1.put("title",title.get(1));
                map1.put("log",log.split("\n")[2] + "\n" + log.split("\n")[3]);
                mapList.add(map1);
                Map<String, String> map2 = new HashMap<>();
                map2.put("title",title.get(2));
                map2.put("log",log.split("\n")[4] + "<br>" + log.split("\n")[5]);
                mapList.add(map2);
                Map<String, String> map3 = new HashMap<>();
                map3.put("title",title.get(3));
                map3.put("log",log.split("\n")[6] + "<br>" + log.split("\n")[7] + "\n" + log.split("\n")[8]);
                mapList.add(map3);
                Map<String, String> map4 = new HashMap<>();
                map4.put("title",title.get(4));
                map4.put("log",log.split("\n")[9] + "\n" + log.split("\n")[10]);
                mapList.add(map4);
                Map<String, String> map5 = new HashMap<>();
                map5.put("title",title.get(5));
                map5.put("log",log.split("\n")[11] + "\n" + log.split("\n")[12]);
                mapList.add(map5);
                modelAndView.addObject("list",mapList);
            }

        } catch (Exception e) {
            log.info("查询日志出错=>手机号:{},错误信息:{}",mobile,e);
        }
        return modelAndView;
    }

    @Override
    public Map<String, Object> queryFirsthandLog(String id) {
        Map<String, Object> map = new HashMap<>();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String result = "";
        String link = "";
        String smsCode = "";
        Subscribe subscribe = subscribeService.getById(id);
        String mobile = subscribe.getMobile();
        Date createTime = subscribe.getCreateTime();
        //查询原始日志
        try{
            result = ExecUtils.grepVrbtLog(mobile,df.format(createTime));
            List<String> logList = Arrays.asList(result.split("\n"));
            for (String log : logList) {
                if(log.contains("渠道订阅获取短验SXB")){
                    if(log.split("source\":\"").length>1
                            && log.split("source\":\"")[1].split("\"").length>0){
                        link = log.split("source\":\"")[1].split("\"")[0];
                    }
                }
                if(log.contains("提交短信") && log.contains("短信验证码:")){
                    if(log.split("短信验证码:").length>1
                            && log.split("短信验证码:")[1].split(",").length>0)
                    smsCode = log.split("短信验证码:")[1].split(",")[0];
                }
            }
        }catch (Exception e){
            log.info("查询日志出错=>手机号:{},错误信息:{}",mobile,e);
        }
        map.put("log",result);
        map.put("link",link);
        map.put("smsCode",smsCode);

        return map;
    }
}
