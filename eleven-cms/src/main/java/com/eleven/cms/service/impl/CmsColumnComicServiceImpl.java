package com.eleven.cms.service.impl;

import com.eleven.cms.entity.CmsColumnComic;
import com.eleven.cms.entity.CmsComicVideo;
import com.eleven.cms.mapper.CmsComicVideoMapper;
import com.eleven.cms.mapper.CmsColumnComicMapper;
import com.eleven.cms.service.ICmsColumnComicService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: cms_column_comic
 * @Author: jeecg-boot
 * @Date: 2024-08-26
 * @Version: V1.0
 */
@Service
public class CmsColumnComicServiceImpl extends ServiceImpl<CmsColumnComicMapper, CmsColumnComic> implements ICmsColumnComicService {

    @Autowired
    private CmsColumnComicMapper cmsColumnComicMapper;
    @Autowired
    private CmsComicVideoMapper cmsComicVideoMapper;

    @Override
    @Transactional
    public void saveMain(CmsColumnComic cmsColumnComic, List<CmsComicVideo> cmsComicVideoList) {
        cmsColumnComicMapper.insert(cmsColumnComic);
        if (cmsComicVideoList != null && cmsComicVideoList.size() > 0) {
            for (CmsComicVideo entity : cmsComicVideoList) {
                //外键设置
                entity.setColumnId(cmsColumnComic.getId());
                cmsComicVideoMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateMain(CmsColumnComic cmsColumnComic, List<CmsComicVideo> cmsComicVideoList) {
        cmsColumnComicMapper.updateById(cmsColumnComic);

        //1.先删除子表数据
        cmsComicVideoMapper.deleteByMainId(cmsColumnComic.getId());

        //2.子表数据重新插入
        if (cmsComicVideoList != null && cmsComicVideoList.size() > 0) {
            for (CmsComicVideo entity : cmsComicVideoList) {
                //外键设置
                entity.setColumnId(cmsColumnComic.getId());
                cmsComicVideoMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void delMain(String id) {
        cmsComicVideoMapper.deleteByMainId(id);
        cmsColumnComicMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            cmsComicVideoMapper.deleteByMainId(id.toString());
            cmsColumnComicMapper.deleteById(id);
        }
    }

}
