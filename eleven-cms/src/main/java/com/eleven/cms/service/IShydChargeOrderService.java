package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.ShydChargeOrder;

import java.util.List;

/**
 * 上海移动办理订单,收到办理成功的通知后创建 Service接口
 *
 * <AUTHOR>
 * @date 2021-04-01 10:41:00
 */
public interface IShydChargeOrderService extends IService<ShydChargeOrder> {
    void saveShydChargeOrder(String phone, String orderId, Integer price,String activityId,String productName,String remark,Integer isRight,Integer chargeStatus);

    void updateShydChargeOrder(String orderId,String remark,Integer chargeStatus);

    Boolean isMember(String phone, String productName, Integer chargeStatus);
//
//    Boolean isMember(String phone);
}
