package com.eleven.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.entity.AppDict;
import com.eleven.cms.entity.AppDictItem;
import org.jeecg.common.system.vo.DictModel;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * @<PERSON>
 * @since 2018-12-28
 */
public interface IAppDictService extends IService<AppDict> {

    public List<DictModel> queryDictItemsByCode(String code);

    public Map<String, List<DictModel>> queryAllDictItems();

    @Deprecated
    List<DictModel> queryTableDictItemsByCode(String table, String text, String code);

    @Deprecated
    public List<DictModel> queryTableDictItemsByCodeAndFilter(String table, String text, String code, String filterSql);

    public String queryDictTextByKey(String code, String key);

    @Deprecated
    String queryTableDictTextByKey(String table, String text, String code, String key);

    @Deprecated
    List<String> queryTableDictByKeys(String table, String text, String code, String[] keyArray);

    /**
     * 根据字典类型删除关联表中其对应的数据
     *
     * @param appDict
     * @return
     */
    boolean deleteByDictId(AppDict appDict);

    /**
     * 添加一对多
     */
    public Integer saveMain(AppDict appDict, List<AppDictItem> appDictItemList);

    /**
     * 查询所有部门 作为字典信息 id -->value,departName -->text
     *
     * @return
     */
    public List<DictModel> queryAllDepartBackDictModel();

    /**
     * 查询所有用户  作为字典信息 username -->value,realname -->text
     *
     * @return
     */
    public List<DictModel> queryAllUserBackDictModel();

    /**
     * 通过关键字查询字典表
     *
     * @param table
     * @param text
     * @param code
     * @param keyword
     * @return
     */
    @Deprecated
    public List<DictModel> queryTableDictItems(String table, String text, String code, String keyword);


    /**
     * 真实删除
     *
     * @param id
     */
    public void deleteOneDictPhysically(String id);

    /**
     * 修改delFlag
     *
     * @param delFlag
     * @param id
     */
    public void updateDictDelFlag(int delFlag, String id);

}
