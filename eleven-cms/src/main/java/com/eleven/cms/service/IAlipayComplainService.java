package com.eleven.cms.service;

import com.eleven.cms.entity.AlipayComplain;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 支付宝投诉明细
 * @Author: jeecg-boot
 * @Date:   2023-04-04
 * @Version: V1.0
 */
public interface IAlipayComplainService extends IService<AlipayComplain> {
    Result<?> alipayQueryComplainList(Integer pageSize, Integer pageNum);

    Result<?> alipaySolveComplain(String complainEventId,String feedbackImages,String feedbackContent,String feedbackCode,String operator);

//    Result<?> alipayReplyComplain(String complainEventId,String feedbackImages,String feedbackContent);
//
//    Result<?> alipaySupplementComplain(String complainEventId,String feedbackImages,String feedbackContent);
}
