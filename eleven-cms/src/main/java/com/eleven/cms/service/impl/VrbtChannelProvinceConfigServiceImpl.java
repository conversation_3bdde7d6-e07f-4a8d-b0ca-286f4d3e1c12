package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.entity.ProvinceTree;
import com.eleven.cms.entity.VrbtChannelProvinceConfig;
import com.eleven.cms.entity.VrbtProvinceSwitchConfig;
import com.eleven.cms.mapper.VrbtChannelProvinceConfigMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IProvinceTreeService;
import com.eleven.cms.service.IVrbtChannelProvinceConfigService;
import com.eleven.cms.service.IVrbtProvinceSwitchConfigService;
import com.eleven.cms.util.BizConstant;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @Description: cms_vrbt_province_switch_config
 * @Author: jeecg-boot
 * @Date: 2021-08-16
 * @Version: V1.0
 */
@Slf4j
@Service
public class VrbtChannelProvinceConfigServiceImpl extends ServiceImpl<VrbtChannelProvinceConfigMapper, VrbtChannelProvinceConfig> implements IVrbtChannelProvinceConfigService {

    @Autowired
    IVrbtChannelProvinceConfigService vrbtChannelProvinceConfigService;
    @Autowired
    IProvinceTreeService provinceTreeService;

    /**
     * 根据渠道号获取配置省份列表
     *
     * @param channelCode
     * @return 省份列表
     */
    @Cacheable(cacheNames = CacheConstant.CMS_VRBT_CHANNEL_PROVINCE_CONFIG_CACHE, key = "#root.methodName + ':' + #p0", condition = "#p0!=null", unless = "#result==null")
    @Override
    public List<String> getProvinceListByChannel(String channelCode) {

        VrbtChannelProvinceConfig vrbtChannelProvinceConfig = vrbtChannelProvinceConfigService.lambdaQuery().eq(VrbtChannelProvinceConfig::getMiguChannel, channelCode).one();
        if (vrbtChannelProvinceConfig == null) {
            log.warn("未找到渠道号:{}省份配置", channelCode);
            return provinceTreeService.list().stream().map(ProvinceTree::getProvinceName).collect(Collectors.toList());
        }
        String provinceJson = vrbtChannelProvinceConfig.getProvinceJson();
        Map<String, Object> jsonMap = JSONObject.parseObject(provinceJson);
        return jsonMap.entrySet().stream().filter(e -> (boolean) e.getValue()).map(Map.Entry::getKey).collect(Collectors.toList());
    }

    @Cacheable(cacheNames = CacheConstant.CMS_VRBT_CHANNEL_PROVINCE_CONFIG_CACHE, key = "#root.methodName", condition = "#p0!=null", unless = "#result==null")
    @Override
    public List<VrbtChannelProvinceConfig> findAll() {
        return list();
    }

    @Override
    public boolean allow(String channelCode, String province) {
        if(StringUtils.isEmpty(province)){
            return false;
        }
        return getProvinceListByChannel(channelCode).stream().anyMatch(province::equals);
    }
}
