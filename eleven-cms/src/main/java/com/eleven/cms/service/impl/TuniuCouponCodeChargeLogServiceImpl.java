package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.client.UnifyRightsFeignClient;
import com.eleven.cms.config.NanshanTuniuProperties;
import com.eleven.cms.dto.NanshanOpenNotify;
import com.eleven.cms.dto.NanshanOpenResponse;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.entity.TuniuCouponCodeChargeLog;
import com.eleven.cms.mapper.TuniuCouponCodeChargeLogMapper;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.queue.TuniuSendCodeDeductMessage;
import com.eleven.cms.service.ISmsModelService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.ITuniuCouponCodeChargeLogService;
import com.eleven.cms.util.*;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moczul.ok2curl.CurlInterceptor;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.utils.UuidUtils;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 途牛券码充值记录
 * @Author: jeecg-boot
 * @Date:   2024-03-29
 * @Version: V1.0
 */
@Slf4j
@Service
public class TuniuCouponCodeChargeLogServiceImpl extends ServiceImpl<TuniuCouponCodeChargeLogMapper, TuniuCouponCodeChargeLog> implements ITuniuCouponCodeChargeLogService {

    public static final Integer SUB_DAY = 31;//包月天数
    private static final String ERROR_CODE_SUCCESS = "200"; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String ERROR_CODE_SUCCESS_MSG ="处理成功"; //响应描述
    private static final String ERROR_CODE_STATUS_OK ="OK"; //响应状态

    private static final String ERROR_CODE_FAIL = "500"; //响应中code为200，为正常情况   响应中code不为200，请求异常。
    private static final String ERROR_CODE_FAIL_MSG ="验签失败"; //响应描述
    private static final String ERROR_CODE_STATUS_FAIL ="ERROR"; //响应状态

    //卡券类型 0：二维码；1：条形码；2：条形码和二维码；3：卡券URL地址；4：只包含密码；5：卡号和密码
    private static final String CARD_TYPE = "4";
    private static final String VERSION = "v1.0.0";

    private static final String COUPON_STATUS_INIT = "0";//待核销
    private static final String COUPON_STATUS_SUCCESS = "1"; //已核销
    private static final String COUPON_STATUS_FAIL = "2"; //撤回核销

    private static final String ORDER_STATUS_SUCCESS = "2"; //成功
    private static final String ORDER_STATUS_FAIL = "-1"; //失败
    @Autowired
    private ISmsModelService smsModelService;
    @Autowired
    private NanshanTuniuProperties nanshanTuniuProperties;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    private OkHttpClient client;
    private ObjectMapper mapper;
    private MediaType JSON;
    @Autowired
    private Environment environment;
    @Autowired
    private UnifyRightsFeignClient unifyRightsFeignClient;
    @Autowired
    ISubscribeService subscribeService;
    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance().newBuilder().build();
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(profile -> (profile.contains("prod")))) {
            this.client = this.client.newBuilder().addNetworkInterceptor(new CurlInterceptor(
                    message -> System.out.println(message))).build();
        }
        this.mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);;
    }
    @Override
    public NanshanOpenResponse nanshanOpenNotify(String requestBody,String sign,String appKey) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(requestBody);
            Map<String, Object> params = jsonObject.toJavaObject(Map.class);
            //使用Stream进行排序
            params = params.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new));
            // 将Map转换为JSON字符串
            requestBody = mapper.writeValueAsString(params);
            if(!SignatureUtils.checkSign(requestBody,sign,nanshanTuniuProperties.getPublicKey())){
                log.error("南山下发途牛权益通知-验签失败--->参数:{},sign:{},appKey:{}",requestBody,sign,appKey);
                return new NanshanOpenResponse(ERROR_CODE_FAIL,ERROR_CODE_STATUS_FAIL,ERROR_CODE_FAIL_MSG);
            }
            final NanshanOpenNotify nanshanOpenNotify = new ObjectMapper().readValue(requestBody, NanshanOpenNotify.class);
            TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog=new TuniuCouponCodeChargeLog();
            tuniuCouponCodeChargeLog.setOrderId(nanshanOpenNotify.getOrderNo());
            /**权益Id*/
            tuniuCouponCodeChargeLog.setRightsId(nanshanOpenNotify.getGoodsNo());
            /**事务跟踪id，每次请求保证唯一性*/
            tuniuCouponCodeChargeLog.setDataLinkId(nanshanOpenNotify.getDataLinkId());
            /**手机号*/
            tuniuCouponCodeChargeLog.setMobile(nanshanOpenNotify.getPhone());
            /**商品标识*/
            tuniuCouponCodeChargeLog.setGoodsNo(nanshanOpenNotify.getGoodsNo());
            /**回调通知地址*/
            tuniuCouponCodeChargeLog.setNotifyUrl(nanshanOpenNotify.getNotifyUrl());
            /**激活码状态:0=未使用,1=已使用,2=已失效,3=充值失败,4=充值中*/
            tuniuCouponCodeChargeLog.setStatus(BizConstant.NOT_USE);
            /**过期时间*/
            tuniuCouponCodeChargeLog.setInvalidTime(DateUtil.localDateTimeToDate(LocalDateTime.now().plusDays(SUB_DAY)));
            /**数量*/
            tuniuCouponCodeChargeLog.setNum(nanshanOpenNotify.getNum());

            LocalDateTime start = LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
            LocalDateTime end=    LocalDateTime.of(LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
            //根据上月是否有充值记录判断是否续订
            Integer renewalCount=this.lambdaQuery().eq(TuniuCouponCodeChargeLog::getMobile,nanshanOpenNotify.getPhone()).between(TuniuCouponCodeChargeLog::getCreateTime,start,end).count();
            if(renewalCount>0){
                //2续订
                tuniuCouponCodeChargeLog.setRemark("2");
            }else{
                LocalDateTime subStart = LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                LocalDateTime subEnd=    LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
                //查询当月是否订购 1已订购 0 未订购
                Integer subCount=subscribeService.lambdaQuery().eq(Subscribe::getMobile,nanshanOpenNotify.getPhone()).between(Subscribe::getCreateTime,subStart,subEnd).eq(Subscribe::getChannel,BizConstant.BIZ_TYPE_GZYD_TNLY_HYLB).eq(Subscribe::getStatus,BizConstant.SUBSCRIBE_STATUS_SUCCESS).count();
                tuniuCouponCodeChargeLog.setRemark(subCount>0?"1":"0");
            }
            tuniuCouponCodeChargeLog.setCreateTime(new Date());
            this.baseMapper.insert(tuniuCouponCodeChargeLog);
            rabbitMQMsgSender.tuniuSendCodeQueueMessage(TuniuSendCodeDeductMessage.builder().id(tuniuCouponCodeChargeLog.getId()).mobile(tuniuCouponCodeChargeLog.getMobile()).build());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new NanshanOpenResponse(ERROR_CODE_SUCCESS,ERROR_CODE_STATUS_OK,ERROR_CODE_SUCCESS_MSG);
    }

    @Override
    public void sendCodeScheduleDeduct(String id) {
        TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog=this.baseMapper.selectById(id);
        if(tuniuCouponCodeChargeLog==null){
            log.error("途牛权益发送券码消息队列接收消息-激活码查询失败--->id:{}",id);
            return;
        }
        String sid = RandomStringUtils.randomAlphanumeric(10);
        tuniuCouponCodeChargeLog.setCouponCode(sid);
        this.baseMapper.updateById(tuniuCouponCodeChargeLog);
        if(StringUtils.isNotBlank(tuniuCouponCodeChargeLog.getMobile())){
            smsModelService.sendSmsAsync(tuniuCouponCodeChargeLog.getMobile(),tuniuCouponCodeChargeLog.getRightsId(),BizConstant.SMS_MODEL_COMMON_SERVICE_ID, BizConstant.BUSINESS_TYPE_RIGHTS,sid);
        }
        this.tuniuSendCodeSync(tuniuCouponCodeChargeLog,ORDER_STATUS_SUCCESS,COUPON_STATUS_INIT);
    }


    private void tuniuSendCodeSync(TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog,String status,String couponStatus) {
        String nonce=IdWorker.get32UUID().toUpperCase().substring(0, 16);
        String timestamp=DateUtil.formatForMiguGroupApi(LocalDateTime.now());
        ObjectNode dataNode =mapper.createObjectNode();
        dataNode.put("dataLinkId", tuniuCouponCodeChargeLog.getDataLinkId());
        ObjectNode notifyData =mapper.createObjectNode();
        ObjectNode ciphertextObject =mapper.createObjectNode();
        ObjectNode orderInfo =mapper.createObjectNode();
        orderInfo.put("orderNo",tuniuCouponCodeChargeLog.getOrderId());
        orderInfo.put("status",status);
        List<Map<String,String>>couponInfos= Lists.newArrayList();
        Map<String,String> coupon = Maps.newHashMap();
        coupon.put("cardType",CARD_TYPE);
        coupon.put("coupon",tuniuCouponCodeChargeLog.getCouponCode());
        coupon.put("couponStatus",couponStatus);
        coupon.put("modTime",timestamp);
        couponInfos.add(coupon);
        orderInfo.putPOJO("couponInfos",couponInfos);
        ciphertextObject.putPOJO("data",orderInfo);
        ciphertextObject.put("errorCode",ERROR_CODE_SUCCESS);
        ciphertextObject.put("errorMessage",ERROR_CODE_SUCCESS_MSG);
        ciphertextObject.put("status",ERROR_CODE_STATUS_OK);
        String ciphertext=this.decryptECBNoPadding(ciphertextObject.toString(),nanshanTuniuProperties.getCallbackKey(),nonce);
        notifyData.put("ciphertext",ciphertext);
        notifyData.put("nonce",  nonce);
        dataNode.putPOJO("notifyData", notifyData);
        dataNode.put("timestamp",timestamp);
        dataNode.put("version", VERSION);
        String sign=SignatureUtils.sign(dataNode.toString(),nanshanTuniuProperties.getCallbackPrivateKey());
        this.implementHttpPostResult(tuniuCouponCodeChargeLog.getNotifyUrl(),dataNode.toString(),"南山下发途牛权益交易状态回调",sign,nanshanTuniuProperties.getAppKey(),tuniuCouponCodeChargeLog.getMobile());
    }


    /**
     * 发起http请求
     */
    public String implementHttpPostResult(String url,String raw,String msg,String sign,String appKey,String mobile) {
        return push(url, raw,msg,sign,appKey,mobile);
    }
    public String push(String url,String raw,String msg,String sign,String appKey,String mobile) {
        log.info(msg+",请求数据=>手机号:{},地址:{},请求参数:{}",mobile,url,raw);
        RequestBody body = RequestBody.create(JSON, raw);
        Request request = new Request.Builder().url(url).post(body).addHeader("sign",sign).addHeader("appKey",appKey).build();
        try (Response response =client.newCall(request).execute()){
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            String content = response.body().string();
            log.info(msg+",响应数据=>手机号:{},地址:{},请求参数:{},响应参数:{}",mobile,url,raw,content);
            return content;
        } catch (IOException e) {
            log.error(msg+",请求异常=>手机号:{},地址:{},请求参数:{}",mobile,url,raw,e);
            return null;
        }
    }


    /**
     * 加密
     */
    private String decryptECBNoPadding(String notifyData,String key,String nonce){
        byte[] bytes = Sm4Util.encryptCBCPadding(key.getBytes(StandardCharsets.UTF_8),
                nonce.getBytes(StandardCharsets.UTF_8),
                notifyData.getBytes(StandardCharsets.UTF_8));
        String ciphertext =new String(Base64.getEncoder().encode( bytes), StandardCharsets.UTF_8);
        return ciphertext;
    }
    @Override
    public FebsResponse tuniuRecharge(String mobile,String couponCode){
        //校验激活码
        TuniuCouponCodeChargeLog tuniuCouponCode=this.lambdaQuery().eq(TuniuCouponCodeChargeLog::getCouponCode,couponCode).orderByDesc(TuniuCouponCodeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(tuniuCouponCode==null){
            return new FebsResponse().fail().message("激活码错误");
        }
        //校验激活码状态（失败和未使用的才能继续使用）
        if(!StringUtils.equalsAny(String.valueOf(tuniuCouponCode.getStatus()),String.valueOf(BizConstant.NOT_USE),String.valueOf(BizConstant.RECHARGE_FAIL))){
            return new FebsResponse().fail().message("激活码已使用");
        }

        //校验过期时间
        if(tuniuCouponCode.getInvalidTime()==null){
            //主动设置优惠券失效
            this.lambdaUpdate().eq(TuniuCouponCodeChargeLog::getCouponCode,couponCode).eq(TuniuCouponCodeChargeLog::getMobile,mobile).set(TuniuCouponCodeChargeLog::getUpdateTime,new Date()).set(TuniuCouponCodeChargeLog::getStatus,BizConstant.IS_EXPIRED).update();
            return new FebsResponse().fail().message("激活码已使用");
        }
        //校验激活码状态（已失效禁止使用）
        if(BizConstant.IS_EXPIRED.equals(tuniuCouponCode.getStatus())){
            return new FebsResponse().fail().message("激活码已使用");
        }
        //校验激活码过期时间是否到期
        LocalDateTime invalidTime=tuniuCouponCode.getInvalidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        if(invalidTime.isBefore(LocalDateTime.now())){
            //主动设置优惠券失效
            this.lambdaUpdate().eq(TuniuCouponCodeChargeLog::getCouponCode,couponCode).eq(TuniuCouponCodeChargeLog::getMobile,mobile).set(TuniuCouponCodeChargeLog::getUpdateTime,new Date()).set(TuniuCouponCodeChargeLog::getStatus,BizConstant.IS_EXPIRED).update();
            return new FebsResponse().fail().message("激活码已使用");
        }
        //校验手机号31天内是否已领取途牛会员（充值中和已充值）
        TuniuCouponCodeChargeLog tuniuCouponCodeChargeLog=this.lambdaQuery().eq(TuniuCouponCodeChargeLog::getMobile,mobile).in(TuniuCouponCodeChargeLog::getStatus,BizConstant.IS_USE,BizConstant.RECHARGE_WAIT).between(TuniuCouponCodeChargeLog::getSendTime,LocalDateTime.now().minusDays(SUB_DAY), LocalDateTime.now()).orderByDesc(TuniuCouponCodeChargeLog::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
        if(tuniuCouponCodeChargeLog!=null){
            return new FebsResponse().fail().message("账号已充值");
        }
        //设置激活码状态（使用中）
        this.lambdaUpdate().eq(TuniuCouponCodeChargeLog::getCouponCode,couponCode).eq(TuniuCouponCodeChargeLog::getMobile,mobile).set(TuniuCouponCodeChargeLog::getSendTime,new Date()).set(TuniuCouponCodeChargeLog::getUpdateTime,new Date()).set(TuniuCouponCodeChargeLog::getStatus,BizConstant.RECHARGE_WAIT).update();
        Boolean rechargeStatus=false;
        for(int i=0;i<BizConstant.PRODUCT_ID_LIST.size();i++){
            FebsResponse febs=unifyRightsFeignClient.tuniuSendRights(mobile,BizConstant.PRODUCT_ID_LIST.get(i));
            if(febs.isOK()){
                rechargeStatus=true;
            }
        }
        if(rechargeStatus){
            //更新订单状态
            this.lambdaUpdate().eq(TuniuCouponCodeChargeLog::getCouponCode,couponCode).eq(TuniuCouponCodeChargeLog::getMobile,mobile).set(TuniuCouponCodeChargeLog::getSendTime,new Date()).set(TuniuCouponCodeChargeLog::getUpdateTime,new Date()).set(TuniuCouponCodeChargeLog::getStatus,BizConstant.IS_USE).update();
            this.tuniuSendCodeSync(tuniuCouponCode,ORDER_STATUS_SUCCESS,COUPON_STATUS_SUCCESS);
            return new FebsResponse().success().message("充值成功");
        }else{
            //更新订单状态
            this.lambdaUpdate().eq(TuniuCouponCodeChargeLog::getCouponCode,couponCode).eq(TuniuCouponCodeChargeLog::getMobile,mobile).set(TuniuCouponCodeChargeLog::getSendTime,new Date()).set(TuniuCouponCodeChargeLog::getUpdateTime,new Date()).set(TuniuCouponCodeChargeLog::getStatus,BizConstant.RECHARGE_FAIL).update();
            return new FebsResponse().fail().message("充值失败");
        }

    }
}
