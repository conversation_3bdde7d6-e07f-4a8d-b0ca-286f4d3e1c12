package com.eleven.cms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.entity.AliSignRecord;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.MobileRegionService;
import com.eleven.cms.remote.PushSubscribeService;
import com.eleven.cms.remote.WujiongCrackService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.*;
import com.eleven.cms.util.BizConstant;
import com.eleven.cms.util.DateUtil;
import com.eleven.cms.util.Regexp;
import com.eleven.cms.util.RightBusinessEnum;
import com.eleven.cms.vo.MobileRegionResult;
import com.eleven.cms.vo.WujiongCrackResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.eleven.cms.util.BizConstant.*;

/**
 * @author: cai lei
 * @create: 2022-09-14 14:44
 */
@Slf4j
@Service
public class HetuBusinessServiceImpl implements  IBusinessCommonService {

    @Autowired
    MobileRegionService mobileRegionService;
    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    WujiongCrackService wujiongCrackService;
    @Autowired
    @Lazy
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IAdSiteBusinessConfigService adSiteBusinessConfigService;


    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
//        if (BIZ_CHANNEl_HETU_GZ.equals(subscribe.getChannel()) && subscribe.getMobile().matches("13[6-9]\\d{8}")) {
//            subscribe.setChannel(BIZ_TYPE_GZYD_SXX_HY);
//            subscribe.setBizType(BIZ_TYPE_GZYD_SXX_HY);
//            return subscribeService.receiveGuiZhouMobileOrder(subscribe);
//        }
        if (adSiteBusinessConfigService.isBlack(subscribe.getChannel(), subscribe.getReferer())) {
            log.error("手机号:{},渠道号:{},app:{}已被屏蔽", subscribe.getMobile(), subscribe.getChannel(), subscribe.getReferer());
            return Result.error("暂无资格，敬请期待！");
        }
        final Result<Object> errorResult = Result.error("系统繁忙,请稍后再试!");
        String mobile = subscribe.getMobile();


        //同一手机号每月只能开通一次
        Integer subscribeSuccessCount = subscribeService.lambdaQuery()
                .eq(Subscribe::getMobile, mobile)
                .eq(Subscribe::getStatus, SUBSCRIBE_STATUS_SUCCESS)
                .eq(Subscribe::getBizType,BIZ_TYPE_HETU)
                .between(Subscribe::getCreateTime,DateUtil.getFirstDayOfMonthWithMinTime(),DateUtil.getLastDayOfMonthWithMaxTime())
                .count();
        if(subscribeSuccessCount>0){
            final Result<Object> bizExistsResult = Result.bizExists("你已开通过,请勿重复开通");
            return bizExistsResult;
        }
        //设置归属地
        try {
            MobileRegionResult mobileRegionResult = mobileRegionService.query(mobile);
            if (mobileRegionResult != null) {
                subscribe.setProvince(mobileRegionResult.getProvince());
                subscribe.setCity(mobileRegionResult.getCity());
                subscribe.setIsp(mobileRegionResult.getOperator());
                if (mobileRegionResult.isIspYidong()) {
                    if (!provinceBusinessChannelConfigService.allow(subscribe.getChannel(), mobileRegionResult.getProvince())) {
                        log.warn("移动省份限制,渠道号:{},手机号:{},省份:{}", subscribe.getChannel(), mobile, mobileRegionResult.getProvince());
                        return Result.error("暂未开放,敬请期待!");
                    }
                } else {
                    return Result.error("暂只支持移动用户!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return errorResult;
        }
        Result result = SpringContextUtils.getBean(HetuBusinessServiceImpl.class).receiveOrder(subscribe);
        result.setServiceId(RightBusinessEnum.getServiceId(subscribe.getChannel()));
        return result;
    }

    @Override
    @ValidationLimit
    public Result receiveOrder(Subscribe subscribe) {
        //手机号已经在调用方法中校验,此处跳过
        //如果是带短信的,就发给破解计费方并返回成功
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isNotEmpty(smsCode)) {
            //验证短信验证码是否合法
            if (!smsCode.matches(Regexp.MIGU_SMS_CODE_REG)) {
                return Result.captchaErr("短信验证码错误");
            }
            final String transactionId = subscribe.getTransactionId();
            if (Strings.isEmpty(transactionId)) {
                return Result.captchaErr("请求参数错误");
            }
            final Subscribe target = subscribeService.getById(transactionId);
            if (Objects.isNull(target)) {
                return Result.captchaErr("请求参数错误");
            }
            //避免相同的错误短信验证码反复提交
            String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
            if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
                return Result.error("请勿重复提交");
            }
            redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
            //此处保存已提交验证码
            if (SUBSCRIBE_STATUS_INIT.equals(target.getStatus())) {
                Subscribe upd = new Subscribe();
                upd.setId(target.getId());
                upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
                upd.setModifyTime(new Date());
                subscribeService.updateSubscribeDbAndEs(upd);
            }
            //曹伟破解
            yidongVrbtCrackService.smsCode(target.getIspOrderNo(), smsCode, subscribe.getChannel(), subscribe.getMobile());
            return Result.ok("订阅成功");
        }
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }

        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        final Result<?> result = pushSubscribeService.handleHetuCaoWeiExistsBillingCrack(subscribe);
        if (result != null && CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())) {
            redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + 30L);
        }
        if (result != null && CommonConstant.SC_JEECG_BIZ_EXISTS.equals(result.getCode())) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
        }
        return result;
    }
}
