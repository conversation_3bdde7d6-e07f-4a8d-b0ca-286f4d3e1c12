package com.eleven.cms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.mapper.AccountCofigMapper;
import com.eleven.cms.service.IAccountCofigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import lombok.RequiredArgsConstructor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.Map;

/**
 * 账号配置表 Service实现
 *
 * <AUTHOR>
 * @date 2021-06-02 16:57:09
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class AccountCofigServiceImpl extends ServiceImpl<AccountCofigMapper, AccountCofig> implements IAccountCofigService {

    private final AccountCofigMapper accountCofigMapper;

    @Override
    public IPage<AccountCofig> findByPage(Page<AccountCofig> page,QueryWrapper<AccountCofig> queryWrapper) {

        IPage<AccountCofig> resultPage = accountCofigMapper.findByPage(page, queryWrapper.getEntity());
        return resultPage;
    }

    @Override
    public List<AccountCofig> findAccountCofigs(AccountCofig accountCofig) {
        List<AccountCofig> accountCofigs = this.accountCofigMapper.findAccountCofigs(accountCofig);
        return accountCofigs;
    }

    /**
     *  根据广告id查询配置 2021-6-3 11:47:09 by lihb
     *
     * @param accountCofig 配置实体
     * @return
     */
    @Override
    public List<AccountCofig> findByAdPlatformId(AccountCofig accountCofig) {
        LambdaQueryWrapper<AccountCofig> queryWrapper = new LambdaQueryWrapper<>();
        //广告id为空则返回空
        if(accountCofig == null || StringUtils.isEmpty(accountCofig.getAdPlatformId())){
            return null;
        }
        return this.accountCofigMapper.findAccountCofigs(accountCofig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAccountCofig(AccountCofig accountCofig) {
        this.save(accountCofig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccountCofig(AccountCofig accountCofig) {
        this.saveOrUpdate(accountCofig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAccountCofig(AccountCofig accountCofig) {
        LambdaQueryWrapper<AccountCofig> wrapper = new LambdaQueryWrapper<>();
	    // TODO 设置删除条件
	    this.remove(wrapper);
	}

	private Map<String,Object> switchData(String json){
        Map<String, Object> resultMap = JSONObject.parseObject(json, Map.class);
        return resultMap;
    }
}
