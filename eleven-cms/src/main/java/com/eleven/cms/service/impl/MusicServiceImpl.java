package com.eleven.cms.service.impl;

import com.eleven.cms.entity.ColumnMusic;
import com.eleven.cms.entity.Music;
import com.eleven.cms.mapper.MusicMapper;
import com.eleven.cms.remote.MiguApiService;
import com.eleven.cms.service.IColumnMusicService;
import com.eleven.cms.service.IMusicService;
import com.eleven.cms.vo.MusicVo;
import com.eleven.cms.vo.VrbtProduct;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.TypeRef;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * @Description: 歌曲
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
@Service
public class MusicServiceImpl extends ServiceImpl<MusicMapper, Music> implements IMusicService {
    @Autowired
    IColumnMusicService columnMusicService;
    //spring 4.3+支持self inject,从而解决事务和缓存在service内部调用不走代理类的问题
    @Autowired
    IMusicService musicService;
    @Autowired
    MiguApiService miguApiService;
    @Autowired
    MusicMapper musicMapper;

    private static final ObjectMapper mapper = new ObjectMapper();
    
    /**
     * 获取指定栏目下的歌曲列表
     * @param columnId
     * @return
     */
    @Override
    public List<Music> listVrbtByColumnId(String columnId)
    {
        List<String> musicIds = columnMusicService.lambdaQuery()
                                                  .select(ColumnMusic::getMusicId)
                                                  .eq(ColumnMusic::getColumnId, columnId)
                                                  .orderByDesc(ColumnMusic::getPriority)
                                                  .list()
                                                  .stream()
                                                  .map(ColumnMusic::getMusicId)
                                                  .collect(Collectors.toList());
        if(musicIds.isEmpty()){
            return Lists.newArrayList();
        }
        String ids = musicIds.stream()
                             .map(id -> "'" + id + "'")
                             .collect(Collectors.joining(","));
        List<Music> musicList = this.lambdaQuery()
                                    .select(Music::getCopyrightId, Music::getMusicName, Music::getSingerName,
                                            Music::getVrbtProductId,Music::getVrbtImg,Music::getVrbtVideo)
                                    .in(Music::getId, musicIds)
                                    .last("ORDER BY FIELD(id, "+ids+")")
                                    .list();
        return musicList;

    }


    ///**
    // * 目前已不支持我们的专属订阅库的预览图抓取,只支持渠道的咪咕订阅库
    // * 咪咕音乐-视频彩铃-首页 https://y.migu.cn/app/v3/lite/index.html#/vrbt
    // * 咪咕音乐-视频彩铃-产品详情页 https://y.migu.cn/app/v3/lite/index.html#/vrbt/play?id=600926000004164164
    // * 获取视频彩铃产品图片资源接口地址: https://c.musicapp.migu.cn/MIGUM2.0/v1.0/content/resourceinfo.do?needSimple=01&resourceType=M&resourceId=600926000004164164
    // * @param vrbtProductId
    // * @return
    // */
    //@Override
    //public String fetchImg(String vrbtProductId){
    //    HttpUrl httpUrl = HttpUrl.parse("https://c.musicapp.migu.cn/MIGUM2.0/v1.0/content/resourceinfo.do?needSimple=01&resourceType=M&resourceId="+vrbtProductId);
    //    Request request = new Request.Builder().url(httpUrl).build();
    //    try (Response response = OkHttpClientUtils.getSingletonInstance().newCall(request).execute()) {
    //        Reader dataReader = response.body().charStream();
    //        //JsonNode node = mapper.readTree(dataReader)
    //        //        .get("resource")
    //        //        .get(0)
    //        //        .get("mapImg")
    //        //        .get("9:16");
    //        //
    //        //if(node.isArray()&&node.size()>0){
    //        //    JsonNode imgNode = node.get(node.size()-1);
    //        //    return imgNode.get("img").asText();
    //        //} else {
    //        //    return null;
    //        //}
    //
    //        final Spliterator<JsonNode> spliterator = mapper.readTree(dataReader).at("/resource/0/mapImg/9:16").spliterator();
    //        return StreamSupport.stream(spliterator, false)
    //                .map(jsonNode -> jsonNode.at("/img").asText(""))
    //                .filter(img -> !img.endsWith(".webp"))
    //                .reduce((first, second) -> second).orElseGet(() -> null);
    //
    //    } catch (Exception e) {
    //        e.printStackTrace();
    //        return null;
    //    }
    //}

    //@Override
    //public boolean fetchVideoUrl(Music music) {
    //    final MiguApiService miguApiService = SpringContextUtils.getBean(MiguApiService.class);
    //    try {
    //        String vrbtTryToSee = miguApiService.vrbtTryToSeeUrl(null, MiguApiService.CH_DYB_DEFAULT, music.getCopyrightId());
    //        if(StringUtils.isEmpty(vrbtTryToSee)){
    //            return false;
    //        }
    //        this.lambdaUpdate().set(Music::getVrbtVideo,vrbtTryToSee).eq(Music::getId,music.getId()).update();
    //        return true;
    //    } catch (Exception e) {
    //        e.printStackTrace();
    //        return false;
    //    }
    //}

    @Override
    public void updateProductById(String id) {
        Music music = this.getById(id);
        this.fillProductInfo(music);
        this.updateById(music);
    }

    @Override
    public void updateProductByIdList(List<String> idList) {
        idList.stream().filter(StringUtils::isNotBlank).forEach(this::updateProductById);
    }

    @Cacheable(cacheNames = CacheConstant.CMS_MUSIC_CACHE,key = "#vrbtId", unless = "#result==null")
    @Override
    public MusicVo findVrbtInfoByVrbtId(String vrbtId) {

        return baseMapper.findVrbtInfoByVrbtId(vrbtId);
    }

    @Cacheable(cacheNames = CacheConstant.CMS_MUSIC_CACHE,key = "#copyrightId", unless = "#result==null")
    @Override
    public MusicVo findVrbtInfoByCopyrightId(String copyrightId) {
        return baseMapper.findVrbtInfoByCopyrightId(copyrightId);
    }

    @Override
    public String getContentIdByCopyrightId(String copyrightId) {
        final MusicVo musicVo = musicService.findVrbtInfoByCopyrightId(copyrightId);
        return musicVo == null ? null : musicVo.getVrbtProductId();
    }

    /**
     * 根据渠道号返回开放平台/彩铃中心的版权id,如果是彩铃中心的渠道号就返回彩铃中心的版权id,否则就放回开放平台的版权id
     * @param channelCode
     * @param copyrightId
     * @return
     */
    @Override
    public String getChannelCopyrightId(String channelCode, String copyrightId) {
        final boolean isCentrality = MiguApiService.isCentralityChannel(channelCode);
        return  isCentrality ? this.getDyCopyrightIdByCopyrightId(copyrightId) : copyrightId;
    }

    @Override
    public String getDyCopyrightIdByCopyrightId(String copyrightId) {
        if(StringUtils.isEmpty(copyrightId)){
            return null;
        }
        final MusicVo musicVo = musicService.findVrbtInfoByCopyrightId(copyrightId);
        return musicVo == null ? null : musicVo.getDyCopyrightId();
    }

    @Override
    public void fillProductInfo(Music music){
        Optional.ofNullable(this.lambdaQuery()
                                //因为之前有的歌曲没有渠道的版权id,为了兼容更新,认为版权id匹配渠道和订阅的版权id都为同一首歌
                                .in(Music::getCopyrightId, music.getCopyrightId(),music.getDyCopyrightId())
                                .one())
                .ifPresent(item -> music.setId(item.getId()));
        final VrbtProduct vrbtProduct = miguApiService.fetchVrbtProduct(music.getVrbtProductId());
        music.setVrbtImg(vrbtProduct.getVrbtImg());
        music.setVrbtVideo(vrbtProduct.getVrbtVideo());
        music.setExpiryDate(DateUtils.str2Date(vrbtProduct.getExpiryDate(), DateUtils.date_sdf.get()));
        music.setStatus(vrbtProduct.getStatus());
        music.setUpdateTime(new Date());
    }

    @Override
    public void clickCount(Music music) {
        if(music.getPlayCount() != null && music.getPlayCount() != 0){
            musicMapper.playCountClick(music);
        }else if(music.getLikeCount() != null && music.getLikeCount() != 0){
            musicMapper.likeCountClick(music);
        }else if(music.getFavCount() != null && music.getFavCount() != 0){
            musicMapper.favCountClick(music);
        }
    }

    @Override
    public void offline(String copyrightId) {
        this.lambdaUpdate().set(Music::getStatus,0).eq(Music::getCopyrightId,copyrightId).update();
    }

    public static void main(String[] args) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        //String jsonText = "{\"code\":\"000000\",\"info\":\"\",\"resource\":[{\"resourceType\":\"M\",\"copyrightId\":\"638799T6222\",\"contentId\":\"600926000002776864\",\"songName\":\"是青衣还是绿衣-璨大侠dy\",\"singer\":\"\",\"validTime\":\"2025-03-31\",\"copyright\":\"1\",\"imgs\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ys.webp\",\"fileId\":\"63a6a8fde39e004c325fb3dc\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"04\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yt\",\"fileId\":\"63a6a8fde39e004c325fb3dd\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yv.webp\",\"fileId\":\"63a6a8fde39e004c325fb3df\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"}],\"webpImgs\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ys.webp\",\"fileId\":\"63a6a8fde39e004c325fb3dc\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"04\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yt\",\"fileId\":\"63a6a8fde39e004c325fb3dd\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yv.webp\",\"fileId\":\"63a6a8fde39e004c325fb3df\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"}],\"price\":\"300\",\"rateFormats\":[{\"resourceType\":\"M\",\"formatType\":\"PQ\",\"url\":\"/onwR93O%2BWIbvE7Nfz2nBif4oX40%2FiuTu6%2Ffsv7XPxIRykeQuS8bFeCXYM3b1GKWeUjWpIqvJ7%2B4mfFmlUXhXZr%2FUZnxnMkFCcI8GVVq%2BZ1m3tyo8NgOCq%2Bi%2B9Y7%2F8Baj/1040088723281671778395946PvMmwK0ringtonemp4-cp-daily1_ringtonemp4-cp-daily.mp4?ec=2&flag=+&F=013015\",\"format\":\"013015\",\"size\":\"5866128\",\"fileType\":\"3gp\"}],\"tags\":[],\"createTime\":\"2022-12-24T07:23:41.640+00:00\",\"status\":\"1\",\"shareImg\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ys.webp\",\"webpShareImg\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ys.webp\",\"vipType\":\"\",\"diyStatus\":false,\"libraryType\":\"2\",\"opNumItem\":{\"playNum\":0,\"playNumDesc\":\"\",\"keepNum\":0,\"keepNumDesc\":\"0\",\"commentNum\":0,\"commentNumDesc\":\"0\",\"shareNum\":0,\"shareNumDesc\":\"0\",\"settingNum\":0,\"settingNumDesc\":\"\",\"thumbNum\":0,\"thumbNumDesc\":\"0\",\"followNum\":0,\"followNumDesc\":\"0\",\"subscribeNum\":0,\"subscribeNumDesc\":\"0\",\"livePlayNum\":0,\"livePlayNumDesc\":\"0\",\"bookingNum\":0,\"bookingNumDesc\":\"0\",\"orderNumByTotal\":0,\"orderNumByTotalDesc\":\"0\",\"heatingNum\":0,\"heatingNumDesc\":\"\"},\"vrbtId\":\"1137030867\",\"playImgUrl\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yq.webp\",\"webpPlayImgUrl\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yq.webp\",\"aspectRatio\":\"9:16\",\"mapImg\":{\"1:1\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yn.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d7\",\"imgRatio\":\"1:1\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yo.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d8\",\"imgRatio\":\"1:1\",\"webpImg\":\"\"}],\"7:11\":[{\"imgSizeType\":\"00\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yu.webp\",\"fileId\":\"63a6a8fde39e004c325fb3de\",\"imgRatio\":\"7:11\",\"webpImg\":\"\"}],\"9:16\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ys.webp\",\"fileId\":\"63a6a8fde39e004c325fb3dc\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"04\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yt\",\"fileId\":\"63a6a8fde39e004c325fb3dd\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yv.webp\",\"fileId\":\"63a6a8fde39e004c325fb3df\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"}],\"4:3\":[{\"imgSizeType\":\"01\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ym.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d6\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"},{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yp.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d9\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yq.webp\",\"fileId\":\"63a6a8fde39e004c325fb3da\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"}]},\"webpMapImg\":{\"1:1\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yn.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d7\",\"imgRatio\":\"1:1\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yo.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d8\",\"imgRatio\":\"1:1\",\"webpImg\":\"\"}],\"7:11\":[{\"imgSizeType\":\"00\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yu.webp\",\"fileId\":\"63a6a8fde39e004c325fb3de\",\"imgRatio\":\"7:11\",\"webpImg\":\"\"}],\"9:16\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ys.webp\",\"fileId\":\"63a6a8fde39e004c325fb3dc\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"04\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yt\",\"fileId\":\"63a6a8fde39e004c325fb3dd\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yv.webp\",\"fileId\":\"63a6a8fde39e004c325fb3df\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"}],\"4:3\":[{\"imgSizeType\":\"01\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/ym.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d6\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"},{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yp.webp\",\"fileId\":\"63a6a8fde39e004c325fb3d9\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/oss/resource/00/1v/eb/yq.webp\",\"fileId\":\"63a6a8fde39e004c325fb3da\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"}]},\"dedicatedPic\":{},\"vipFlag\":\"0\",\"allowEdit\":\"1\",\"startWeekDay\":[],\"type\":\"07\",\"videoUserId\":\"10000145\",\"videoUser\":{\"resourceType\":\"4047\",\"videoUserId\":\"10000145\",\"userName\":\"佐悦文化\",\"nickName\":\"佐悦文化\"},\"templateExtend\":{\"cpid\":\"638799\",\"copyrightId\":\"638799T6222\",\"title\":\"是青衣还是绿衣-璨大侠dy\"},\"duration\":15000}]}";
        String jsonText = "{\"code\":\"000000\",\"info\":\"\",\"resource\":[{\"resourceType\":\"M\",\"copyrightId\":\"699212T4709\",\"contentId\":\"600926000002357039\",\"songName\":\"柿子熟了\",\"singer\":\"祺音互娱\",\"validTime\":\"2025-02-10\",\"copyright\":\"1\",\"imgs\":[{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6c\",\"fileId\":\"623c5573d32aa3310240a9fc\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"04\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6j\",\"fileId\":\"623c5573d32aa3310240a9fd\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6v\",\"fileId\":\"623c5573d32aa3310240a9ff\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"}],\"webpImgs\":[],\"price\":\"300\",\"rateFormats\":[{\"resourceType\":\"M\",\"formatType\":\"PQ\",\"url\":\"/7TUCV0PuBf3nOcZa%2B9f6KQ8GOoFYXI9fo9dj7w8HgefTFNjJojR3FllMHB0p4OEe%2B7NccOSPBUVXSPrM5E4%2BgRS8N30kT5xrc%2FVg33XJdt7qF2WT1ZsvhYGcwX0xSRL1/1040075449531640416387926Y2Oxlq0ringtonemp4-cp-daily1_ringtonemp4-cp-daily.mp4?ec=2&flag=+&F=013015\",\"format\":\"013015\",\"size\":\"4309382\",\"fileType\":\"3gp\"}],\"tags\":[],\"status\":\"1\",\"shareImg\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6v\",\"vipType\":\"\",\"diyStatus\":false,\"libraryType\":\"2\",\"opNumItem\":{\"playNum\":0,\"playNumDesc\":\"\",\"keepNum\":0,\"keepNumDesc\":\"0\",\"commentNum\":0,\"commentNumDesc\":\"0\",\"shareNum\":0,\"shareNumDesc\":\"0\",\"settingNum\":0,\"settingNumDesc\":\"\",\"thumbNum\":0,\"thumbNumDesc\":\"0\",\"followNum\":0,\"followNumDesc\":\"0\",\"subscribeNum\":0,\"subscribeNumDesc\":\"0\",\"livePlayNum\":0,\"livePlayNumDesc\":\"0\",\"bookingNum\":0,\"bookingNumDesc\":\"0\",\"orderNumByTotal\":287,\"orderNumByTotalDesc\":\"287\",\"heatingNum\":0,\"heatingNumDesc\":\"\"},\"vrbtId\":\"1136146044\",\"playImgUrl\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6o\",\"aspectRatio\":\"9:16\",\"mapImg\":{\"1:1\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/5p\",\"fileId\":\"623c5573d32aa3310240a9f9\",\"imgRatio\":\"1:1\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/7i\",\"fileId\":\"623c5573d32aa3310240aa02\",\"imgRatio\":\"1:1\",\"webpImg\":\"\"}],\"7:11\":[{\"imgSizeType\":\"00\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/72\",\"fileId\":\"623c5573d32aa3310240aa00\",\"imgRatio\":\"7:11\",\"webpImg\":\"\"}],\"9:16\":[{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6c\",\"fileId\":\"623c5573d32aa3310240a9fc\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"04\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6j\",\"fileId\":\"623c5573d32aa3310240a9fd\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"},{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6v\",\"fileId\":\"623c5573d32aa3310240a9ff\",\"imgRatio\":\"9:16\",\"webpImg\":\"\"}],\"4:3\":[{\"imgSizeType\":\"02\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/5y\",\"fileId\":\"623c5573d32aa3310240a9fa\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"},{\"imgSizeType\":\"03\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/6o\",\"fileId\":\"623c5573d32aa3310240a9fe\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"},{\"imgSizeType\":\"01\",\"img\":\"https://d.musicapp.migu.cn/data/resource-service/file-down/00/30/i1/78\",\"fileId\":\"623c5573d32aa3310240aa01\",\"imgRatio\":\"4:3\",\"webpImg\":\"\"}]},\"webpMapImg\":{},\"dedicatedPic\":{},\"vipFlag\":\"0\",\"allowEdit\":\"1\",\"startWeekDay\":[],\"type\":\"07\",\"videoUserId\":\"10000082\",\"videoUser\":{\"resourceType\":\"4047\",\"videoUserId\":\"10000082\",\"userName\":\"祺音互娱\",\"nickName\":\"祺音互娱\",\"smallPicUrl\":\"https://d.musicapp.migu.cn/prod/cms-import-service/public/agreement/20220531/0bca9036-4744-4850-9642-04703f6869ab.png\",\"bigPicUrl\":\"https://d.musicapp.migu.cn/prod/cms-import-service/public/agreement/20220531/0bca9036-4744-4850-9642-04703f6869ab.png\",\"middlePicUrl\":\"https://d.musicapp.migu.cn/prod/cms-import-service/public/agreement/20220531/0bca9036-4744-4850-9642-04703f6869ab.png\",\"briefIntro\":\"成都祺音互娱文化传播有限公司是集艺人包装推广、音视频内容制作、唱片发行与推广、演出策划与执行、线上直播演艺、及演艺经纪与代理为一体的综合性文化公司。旗下拥有众多原创艺人经纪、音视频版权、且拥有100余位线上直播艺人，具备多年音视频制作及内容版权运营能力。先后运营多款安卓与iOS端客户端产品，同时开展与移动运营商音乐增值业务深度合作，有丰富的联合产品运营经验。现有产品、技术、市场、运营、互联网客服、运营商客服共50余人的专项团队，深度互联网合作伙伴100余家。\"},\"templateExtend\":{\"cpid\":\"699212\",\"copyrightId\":\"699212T4709\",\"title\":\"柿子熟了\"},\"duration\":15000}]}";
        final JsonNode read = JsonPath
                .using( Configuration.builder()
                .jsonProvider(new JacksonJsonProvider(mapper))
                .mappingProvider(new JacksonMappingProvider(mapper))
                .build())
                .parse(jsonText)
                .read("$.resource[0].mapImg.['9:16'][?(@.['img'] =~ /.*(?<!webp)/i)]", new TypeRef<JsonNode>() {});
        System.out.println("read = " + read);

        //final Iterator<JsonNode> iterator = mapper.readTree(jsonText).at("/resource/0/mapImg/9:16").iterator();
        //String imgUrl = StreamSupport.stream(Spliterators.spliteratorUnknownSize(iterator, Spliterator.ORDERED), false)
        //        .map(jsonNode -> jsonNode.at("/img").asText(""))
        //        .filter(img -> !img.endsWith(".webp"))
        //        .reduce((first, second) -> second).orElseGet(() -> null);

        final Spliterator<JsonNode> spliterator = mapper.readTree(jsonText).at("/resource/0/mapImg/9:16").spliterator();
        String imgUrl = StreamSupport.stream(spliterator, false)
                .map(jsonNode -> jsonNode.at("/img").asText(""))
                .filter(img -> !img.endsWith(".webp"))
                .reduce((first, second) -> second).orElseGet(() -> null);
        
        System.out.println("at = " + imgUrl);
    }
}
