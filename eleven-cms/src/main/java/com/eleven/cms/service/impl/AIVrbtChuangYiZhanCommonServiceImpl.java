package com.eleven.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.eleven.cms.annotation.IpLimit;
import com.eleven.cms.annotation.IspLimit;
import com.eleven.cms.annotation.SmsDailyLimit;
import com.eleven.cms.annotation.ValidationLimit;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.config.YidongVrbtCrackProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.remote.PushSubscribeService;
import com.eleven.cms.remote.YidongVrbtCrackService;
import com.eleven.cms.service.IBizCommonService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.vo.BillingResult;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

import static com.eleven.cms.util.BizCommonConstant.*;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_INIT;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * @author: ai彩铃创意站业务开通
 * @create: 2023-08-01 16:37
 */
@Slf4j
@Service("aIVrbtChuangYiZhanCommonService")
public class AIVrbtChuangYiZhanCommonServiceImpl implements IBizCommonService {

    @Autowired
    YidongVrbtCrackService yidongVrbtCrackService;
    @Autowired
    YidongVrbtCrackProperties yidongVrbtCrackProperties;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    PushSubscribeService pushSubscribeService;
    @Autowired
    ThreadPoolTaskExecutor aiRingThreadPoolTaskExecutor;
    @Autowired
    @Lazy
    RabbitTemplate rabbitTemplate;

    @Override
    @ValidationLimit
    @SmsDailyLimit
    @IpLimit
    @IspLimit
    public Result getSmsCode(Subscribe subscribe) {
        String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
        if (redisUtil.get(mobileSendSmsLimitKey) != null) {
            return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
        }
        subscribeService.createSubscribeDbAndEs(subscribe);
        //写渠道订阅日志
        BizLogUtils.logSubscribe(subscribe);
        redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
        BillingResult billingResult = yidongVrbtCrackService.getSms(subscribe.getMobile(), subscribe.getChannel());
        if (billingResult.isOK()) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setIspOrderNo(billingResult.getTransId());
            subscribeService.updateSubscribeDbAndEs(upd);
//            aiRingThreadPoolTaskExecutor.execute(() -> rabbitTemplate.convertAndSend(RabbitMQConfig.SUBSCRIBE_EVENT_QUEUE_NAME, subscribeService.getById(subscribe.getId())));
            return Result.ok("验证码已发送", subscribe.getId());
        } else {
//            return Result.error("系统繁忙,请稍后再试!");

            try {
                String errorMsg = "{\"code\":\"" + billingResult.getCode() + "\",\"message\":\"" + billingResult.getMessage() + "\"}";
                return Result.errorSystemMsg(errorMsg);
            } catch (Exception e) {
                log.error("获取验证码异常!-subscribe:{}", subscribe, e);
                return Result.msgSystemError();
            }
        }

    }

    @Override
    public Result submitSmsCode(Subscribe subscribe) {
        //避免相同的错误短信验证码反复提交
        String smsDuplicateKey = DIANXIN_DAY_DUPLICATE_KEY_PREFIX + subscribe.getMobile() + "-" + subscribe.getSmsCode();
        if (redisUtil.hasKey(smsDuplicateKey)) {
            return Result.captchaErr("请勿重复提交");
        }
        redisUtil.set(smsDuplicateKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        if (SUBSCRIBE_STATUS_INIT.equals(subscribe.getStatus())) {
            Subscribe upd = new Subscribe();
            upd.setId(subscribe.getId());
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setBizTime(new Date());
            upd.setExtra(subscribe.getSmsCode());
            upd.setModifyTime(new Date());
            subscribeService.updateSubscribeDbAndEs(upd);
//            aiRingThreadPoolTaskExecutor.execute(() -> rabbitTemplate.convertAndSend(RabbitMQConfig.SUBSCRIBE_EVENT_QUEUE_NAME, subscribe));
        }
        String ispOrderNo = Optional.ofNullable(subscribeService.getById(subscribe.getTransactionId())).map(Subscribe::getIspOrderNo).orElse(null);
        if (StrUtil.isBlank(ispOrderNo)) {
            return Result.ok("提交验证码失败");
        }
        yidongVrbtCrackService.smsCode(ispOrderNo, subscribe.getSmsCode(), subscribe.getChannel(), subscribe.getMobile());
        return Result.ok("提交验证码成功");
    }
}
