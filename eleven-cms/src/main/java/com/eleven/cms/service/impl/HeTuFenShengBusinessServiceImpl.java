package com.eleven.cms.service.impl;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.log.BizLogUtils;
import com.eleven.cms.service.IBusinessCommonService;
import com.eleven.cms.service.IHeTuFenShengService;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_FAIL;
import static com.eleven.cms.util.BizConstant.SUBSCRIBE_STATUS_SMS_CODE_SUBMITED;

/**
 * 河图分省业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 11:38
 **/
@Slf4j
@Service
public class HeTuFenShengBusinessServiceImpl implements IBusinessCommonService {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    IHeTuFenShengService heTuFenShengService;
    @Override
    public Result receiveOrderWithCache(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        if (StringUtils.isEmpty(smsCode)) {
            String mobileSendSmsLimitKey = MOBILE_SEND_SMS_LIMIT_KEY_PREFIX + subscribe.getBizType() + ":" + subscribe.getMobile();
            if (redisUtil.get(mobileSendSmsLimitKey) != null) {
                return Result.error("操作频繁,请" + MOBILE_SEND_SMS_CATCHE_TTL_SECONDS + "秒后再试");
            }
            subscribeService.createSubscribeDbAndEs(subscribe);
            //写渠道订阅日志
            BizLogUtils.logSubscribe(subscribe);
            Result<?> sendMessageResult = heTuFenShengService.sendSms(subscribe.getMobile(),subscribe.getChannel());
            if (sendMessageResult.isOK()) {
                redisUtil.set(mobileSendSmsLimitKey, MOBILE_SEND_SMS_LIMIT_VALUE, MOBILE_SEND_SMS_CATCHE_TTL_SECONDS);
                return Result.ok("验证码已发送", subscribe.getId());
            } else {
                subscribe.setResult(sendMessageResult.getMessage());
                subscribe.setStatus(SUBSCRIBE_STATUS_FAIL);
                subscribeService.updateSubscribeDbAndEs(subscribe);
                return Result.error(sendMessageResult.getMessage());
            }
        }
        return SpringContextUtils.getBean(HeTuFenShengBusinessServiceImpl.class).receiveOrder(subscribe);
    }

    @Override
    public Result receiveOrder(Subscribe subscribe) {
        String smsCode = subscribe.getSmsCode();
        //避免相同的错误短信验证码反复提交
        String dianxinSmsInvalidKey = DIANXIN_SMS_INVALID_KEY_PREFIX + subscribe.getMobile() + "-" + smsCode;
        if (redisUtil.hasKey(dianxinSmsInvalidKey)) {
            return Result.error("请勿重复提交");
        }
        String mobileMonthExistsKey = MOBILE_MONTH_EXISTS_KEY_PREFIX + subscribe.getMobile();
        if (redisUtil.get(mobileMonthExistsKey) != null) {
            return Result.bizExists("你已开通,请勿重复开通");
        }
        redisUtil.set(dianxinSmsInvalidKey, "invalidSmsCode", DIANXIN_SMS_INVALID_CACHE_SECONDS);
        Result<?> submitResult = heTuFenShengService.submitOrder(subscribe.getMobile(),subscribe.getSmsCode(),subscribe.getId(),subscribe.getIp(),subscribe.getChannel());
        Subscribe upd = new Subscribe();
        upd.setId(subscribe.getTransactionId());
        upd.setResult(submitResult.getMessage());
        upd.setModifyTime(new Date());
        if (submitResult.isOK()) {
            redisUtil.set(mobileMonthExistsKey, MOBILE_MONTH_EXISTS_VALUE, MOBILE_MONTH_EXISTS_CATCHE_TTL_SECONDS);
            //订阅成功
            upd.setStatus(SUBSCRIBE_STATUS_SMS_CODE_SUBMITED);
            upd.setIspOrderNo(submitResult.getResult().toString());
            subscribeService.updateSubscribeDbAndEs(upd);
        } else {
            //订阅失败
            upd.setStatus(SUBSCRIBE_STATUS_FAIL);
            subscribeService.updateSubscribeDbAndEs(upd);
        }
        return submitResult;
    }
}
