package com.eleven.cms.service;

import com.eleven.cms.dto.FeeChargeOrderHaoDangResult;
import com.eleven.cms.entity.MobileFeeChargeLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.vo.FeeChargePlusRequest;
import com.eleven.cms.vo.FeeChargeRequest;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 话费直充记录
 * @Author: jeecg-boot
 * @Date:   2022-09-28
 * @Version: V1.0
 */
public interface IMobileFeeChargeLogService extends IService<MobileFeeChargeLog> {

    Result<?> feeCharge(MobileFeeChargeLog mobileFeeChargeLog);

    Result<?> feeChargePlus(MobileFeeChargeLog mobileFeeChargeLog);

    Result<?>  heBaoAddPlus(MobileFeeChargeLog mobileFeeChargeLog);

    String feeChargeNotify(FeeChargeRequest feeChargeRequest);

    Result<?> detail(String id);

    String feeTokenPlus();

    String feeChargeNotifyPlus(String requestBody);

    String hebaoFeeChargeNotifyPlus(String szAgentId,String szOrderId ,String szPhoneNum,String nDemo,String fSalePrice,String nFlag,String szRtnMsg,String szVerifyString);

    String zhongheFeeChargeNotify(String requestBody);

    String qingHaiWenHangChargeNotify(String userId,String orderId,String serialno,String orderStatus,String errDesc,String sign);


    FeeChargeOrderHaoDangResult haoDangChargeNotify(String orderId, String respCode, String respMsg, String transNo);
}
