package com.eleven.cms.service.impl;

import com.eleven.cms.entity.BusinessPack;
import com.eleven.cms.mapper.BusinessPackMapper;
import com.eleven.cms.service.IBusinessPackService;
import com.eleven.cms.util.BizConstant;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 业务渠道权益关联
 * @Author: jeecg-boot
 * @Date:   2022-12-19
 * @Version: V1.0
 */
@Service
public class BusinessPackServiceImpl extends ServiceImpl<BusinessPackMapper, BusinessPack> implements IBusinessPackService {

    @Override
    public List<BusinessPack> findByBusinessId(List<String> businessIdList) {
        return this.baseMapper.findByBusinessId(businessIdList);
    }

    @Override
    public List<BusinessPack> findByServiceId(String serviceId) {
        return this.baseMapper.findByServiceId(serviceId);
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_RIGHTS_TITLENAME,key = "#serviceId",unless = "#result==null")
    public BusinessPack queryTitleNameByServiceId(String serviceId) {
        return this.lambdaQuery().select(BusinessPack::getTitleName).eq(BusinessPack::getServiceId,serviceId).orderByDesc(BusinessPack::getCreateTime).last(BizConstant.SQL_LIMIT_ONE).one();
    }
}
