package com.eleven.cms.ad;

import com.eleven.cms.config.HeTuFenShengChannel;
import com.eleven.cms.config.HeTuFenShengChongQingChannel;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 14:28
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "hetu-chongqing-api",ignoreUnknownFields = true)
public class HeTuFenShengChongQingProperties {
    private String sendSmsUrl;
    private String createOrderUrl;
    private Map<String, HeTuFenShengChongQingChannel> channelMap;


}
