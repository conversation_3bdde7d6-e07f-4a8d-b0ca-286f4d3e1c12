package com.eleven.cms.ad;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IAdPlatformService;
import com.eleven.cms.util.OkHttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;


/**
 * Author: <EMAIL>
 * Date: 2020/10/29 11:51
 * Desc: 变现猫广告转化上报api(各账号通用,因为不需要配置密钥)
 */
@Slf4j
@Component
public class BianxianmaoAdCommonService implements IAdFeedbackCommonService {

    private static final String LOG_TAG = "变现猫广告转化上报API";

    @Autowired
    private BizProperties bizProperties;
    private OkHttpClient client;
    @Autowired
    private IAdPlatformService iAdPlatformService;
//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
    }

    @Async
    @Override
    public void feedback(Subscribe subscribe,AdPlatform adPlatform,AccountCofig accountCofig) {
        log.info("{}=>订单号:{},手机号:{},bxmId:{}",LOG_TAG,subscribe.getId(),subscribe.getMobile(),subscribe.getDeviceInfo());
        String adreporturl = adPlatform.getAdReportUrl();
        effectFeedback(subscribe.getDeviceInfo(),adreporturl);
    }

    public void effectFeedback(String bxmId,String adreporturl) {
        try {
            HttpUrl httpUrl = HttpUrl.parse(adreporturl)
                                     .newBuilder()
                                     .addQueryParameter("bxmId", bxmId)
                                     .addQueryParameter("type", "2")
                                     .build();

            log.info("{}-请求:{}", LOG_TAG, httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                                                   .build();
            try (Response response = client.newCall(request)
                                           .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-响应:{}", LOG_TAG, content);
            }
        } catch (IOException e) {
            //e.printStackTrace();
            log.warn("{}-异常:", LOG_TAG, e);
        }
    }


}
