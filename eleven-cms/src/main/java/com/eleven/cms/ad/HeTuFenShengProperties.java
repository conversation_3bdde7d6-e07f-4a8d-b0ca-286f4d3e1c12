package com.eleven.cms.ad;

import com.eleven.cms.config.HeTuFenShengChannel;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 14:28
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "hetu-fensheng-api",ignoreUnknownFields = true)
public class HeTuFenShengProperties {
    private String sendSmsUrl;
    private String createOrderUrl;
    private Map<String, HeTuFenShengChannel> channelMap;


}
