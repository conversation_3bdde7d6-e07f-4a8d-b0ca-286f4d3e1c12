package com.eleven.cms.ad;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.queue.RabbitMQMsgSender;
import com.eleven.cms.remote.OutsideCallbackService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.util.OkHttpClientUtils;
import com.eleven.cms.vo.ChannelVo;
import com.eleven.cms.vo.FebsResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;

import static com.eleven.cms.queue.RedisDelayedQueueManager.MESSAG_EXTRA_60_MIN;

/**
 * 广告平台上报工具类
 */
@Slf4j
@Service
public class AdReportService {


    private static final String LOG_TAG = "广告平台上报工具API";

    public static final MediaType JSONTYPE
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

    public static ObjectMapper mapper = new ObjectMapper();

    //四川移动白名单号码
    public static String SCMCC_WHITE_NUMBER = "17260807125";

    @Autowired
    @Lazy //解决循环依赖
    ISubscribeService subscribeService;
    @Lazy
    @Autowired
    RabbitMQMsgSender rabbitMQMsgSender;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    @Lazy //解决循环依赖
    OutsideCallbackService outsideCallbackService;

    @Value("${adReportUrl}")
    String adReportUrl;
    @Value("${adPlatformUrl}")
    String adPlatformUrl;
    @Value("${adUnsubReportUrl}")
    String adUnsubReportUrl;
    @Value("${adSubedReportUrl}")
    String adSubedReportUrl;

    /**
     * 调用广告平台微服务上报接口
     *
     * @param adId       广告id，设备信息
     * @param subChannel 渠道号
     * @param mobile     手机号
     * @param ip         ip地址
     * @param userAgent  浏览器ua
     * @return
     */
    public void sendReportMqMessage(String subscribeId, String adId, String subChannel, String mobile, String isp, String ip, String userAgent,String source,Integer status,String channel,String province) {

        Integer result = -1;

        try {
            ObjectNode objectNode = createAdReportEntityJsonNode(subscribeId, adId, subChannel, mobile, isp, ip, userAgent, source, status, channel,province);
            rabbitMQMsgSender.sendAdReportMessage(objectNode);
            result = 1;
        } catch (Exception e) {
            result = 0;
            log.info("{}-发送上报消息到队列异常:adId:{},subChannel:{},mobile:{}", LOG_TAG, adId, subChannel, mobile, e);
        }finally {
            log.info("{}-发送上报消息到队列后更新状态:subChannel:{},mobile:{}", LOG_TAG, subChannel, mobile);
            //subscribeService.lambdaUpdate().eq(Subscribe::getId, subscribeId).set(Subscribe::getTuiaFeedbackStatus, result).update();
            Subscribe upd = new Subscribe();
            upd.setId(subscribeId);
            upd.setTuiaFeedbackStatus(result);
            subscribeService.updateById(upd);
        }

    }


    private ObjectNode createAdReportEntityJsonNode(String subscribeId, String adId, String subChannel, String mobile, String isp, String ip, String userAgent, String source, Integer status, String channel, String province) {
        ObjectNode objectNode = mapper.createObjectNode();
        objectNode.put("adId", adId);
        objectNode.put("subChannel", subChannel);
        objectNode.put("mobile", mobile);
        objectNode.put("isp", StringUtils.isEmpty(isp) ? 1 : Integer.parseInt(isp));
        objectNode.put("ip", ip);
        objectNode.put("userAgent", userAgent);
        objectNode.put("status", status);
        objectNode.put("remark", "");
        objectNode.put("adLink", source);
        objectNode.put("orderNo", subscribeId);
        objectNode.put("channel", channel);
        objectNode.put("province", province);
        return objectNode;
    }

    /**
     * 调用广告平台微服务1天退订上报接口
     *
     * @param adId       广告id，设备信息
     * @param subChannel 渠道号
     * @param mobile     手机号
     * @param adLink     订购广告链接
     * @param subscribe
     * @param extra
     * @return
     */
    @Async
    public void unsubReport(String adId, String subChannel, String mobile, String adLink, Subscribe subscribe, String extra) {

        //Integer result = -1;
        //外部渠道号不上报广告平台退订
        if (outsideConfigService.isOutsideChannel(subChannel)) {
            //扣量的不回传 provinceCode为1直接扣量
            if ("1".equals(subscribe.getProvinceCode())) {
                return;
            }
            if (MESSAG_EXTRA_60_MIN.equals(extra)) {
                outsideCallbackService.unsubscribeCallbackAsync(subscribe);
            } else {
                outsideCallbackService.dayUnsubscribeCallbackAsync(subscribe);
            }
            return;
        }
        try {
            ObjectNode node = mapper.createObjectNode();
            node.put("adId", adId);
            node.put("subChannel", subChannel);
            node.put("mobile", mobile);
            node.put("adLink", adLink);
            OkHttpClient client = OkHttpClientUtils.getSingletonInstance();
            RequestBody body = RequestBody.create(JSONTYPE, node.toString());
            Request request = new Request.Builder().url(adUnsubReportUrl).post(body).build();
            log.info("{}-调用退订上报接口请求-adId:{},subChannel:{},mobile:{}", LOG_TAG , adId, subChannel, mobile);
            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                FebsResponse febsResponse = mapper.readerFor(FebsResponse.class).readValue(content);

                log.info("{}-调用退订上报接口响应-adId:{},subChannel:{},mobile:{},响应-{}", LOG_TAG, adId, subChannel, mobile, mapper.writeValueAsString(febsResponse));
                //Object code = febsResponse.get("code");
                //if (code != null && HttpStatus.OK.value() == Integer.valueOf(code.toString())) {
                //    result = 1;
                //}
            }

        } catch (Exception e) {
            //result = 0;
            log.info("{}-调用退订上报接口异常:adId:{},subChannel:{},mobile:{}", LOG_TAG, adId, subChannel, mobile, e);
        }

    }

    /**
     * 调用广告平台微服务退订上报接口
     *
     * @param adId       广告id，设备信息
     * @param subChannel 渠道号
     * @param mobile     手机号
     * @return
     */
    @Async
    public void subedReport(String adId, String subChannel, String mobile) {

        //Integer result = -1;
        //外部渠道号不上报广告平台退订
        if (outsideConfigService.isOutsideChannel(subChannel)) {
            return;
        }

        try {
            ObjectNode node = mapper.createObjectNode();
            node.put("adId", adId);
            node.put("subChannel", subChannel);
            node.put("mobile", mobile);
            OkHttpClient client = OkHttpClientUtils.getSingletonInstance();
            RequestBody body = RequestBody.create(JSONTYPE, node.toString());
            Request request = new Request.Builder().url(adSubedReportUrl).post(body).build();
            log.info("{}-调用存量在订上报接口请求-adId:{},subChannel:{},mobile:{}", LOG_TAG , adId, subChannel, mobile);
            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                FebsResponse febsResponse = mapper.readerFor(FebsResponse.class).readValue(content);

                log.info("{}-调用存量在订上报接口响应-adId:{},subChannel:{},mobile:{},响应-{}", LOG_TAG, adId, subChannel, mobile, mapper.writeValueAsString(febsResponse));
                //Object code = febsResponse.get("code");
                //if (code != null && HttpStatus.OK.value() == Integer.valueOf(code.toString())) {
                //    result = 1;
                //}
            }

        } catch (Exception e) {
            //result = 0;
            log.info("{}-调用存量在订上报接口异常:adId:{},subChannel:{},mobile:{}", LOG_TAG, adId, subChannel, mobile, e);
        }

    }

    /**
     *  查询广告平台数据单条
     *
     * @param subChannel 渠道号
     * @return
     */
    public FebsResponse getAdPlatform(String subChannel) {
        FebsResponse febsResponse =  null;
        try {
            OkHttpClient client = OkHttpClientUtils.getSingletonInstance();
            HttpUrl httpUrl = HttpUrl.parse(adPlatformUrl)
                    .newBuilder()
                    .addQueryParameter("subChannel", subChannel)
                    .build();

            Request request = new Request.Builder().url(httpUrl)
                    .build();
            try (Response response = client.newCall(request)
                    .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                String content = response.body().string();
                febsResponse =mapper.readerFor(FebsResponse.class).readValue(content);

            }
        } catch (IOException e) {
            febsResponse = new FebsResponse().success().data(new ChannelVo("","",""));
        }
        return febsResponse;
    }

}