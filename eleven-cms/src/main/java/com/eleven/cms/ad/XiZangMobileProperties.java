package com.eleven.cms.ad;

import com.eleven.cms.config.XiZangMobileProduct;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 西藏移动配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/28 10:18
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "xizang-mobile-api",ignoreUnknownFields = true)
public class XiZangMobileProperties {
    private String appId;
    private String appKey;
    private String sendSmsMethod;
    private String authMethod;
    private String orderMethod;
    private String oppfUrl;

    //工号
    private String operatorId;
    //渠道号
    private String channelId;
    private String desKey;
    private String tokenUrl;
    private Map<String, XiZangMobileProduct> productMap;

    public String getDesKeyPad() {
        return StringUtils.rightPad(this.desKey, 8, '\0');
    }
}
