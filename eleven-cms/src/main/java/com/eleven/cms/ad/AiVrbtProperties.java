package com.eleven.cms.ad;

import com.eleven.cms.config.AiVrbtChannel;
import com.eleven.cms.config.RuanYouTongProduct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * AI视频彩铃配置文件
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/16 10:58
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "aivrbt",ignoreUnknownFields = true)
public class AiVrbtProperties {

    //短信发送接口
    private String initiateOrderUrl;
    //订购提交接口
    private String submitOrderUrl;

    private String returnUrl;

    private String remark;

    private String channel;

    private String authChannelid;

    private String openSecret;

    private String openOrderUrl;

    private Map<String, AiVrbtChannel> channelMap;

}
