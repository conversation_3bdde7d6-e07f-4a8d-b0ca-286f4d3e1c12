package com.eleven.cms.ad;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 天翼通讯助理配置文件
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 16:04
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "tianyitongxunzhuli",ignoreUnknownFields = true)
public class TianYiTongXunZhuLiProperties {
    private String appKey;
    private String secret;
    private String businessYn;
    private String businessSx;
    //订单开通接口
    private String exposedInterfaceUrl;
    //短信发送接口
    private String sendMessageUrl;
    //订购提交接口
    private String submitOrderUrl;
    //四川个人名片创建订单接口
    private String queryOrderInfoDetailsUrl;
}

