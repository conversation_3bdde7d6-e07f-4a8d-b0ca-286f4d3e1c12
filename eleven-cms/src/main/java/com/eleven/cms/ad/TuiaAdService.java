package com.eleven.cms.ad;

import com.eleven.cms.ad.IAdFeedbackService;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.OkHttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;


/**
 * Author: <EMAIL>
 * Date: 2020/10/29 11:51
 * Desc: 推啊广告转化上报api(代理)
 */
@Slf4j
@Component
public class TuiaAdService implements IAdFeedbackService {
    private static final String LOG_TAG = "推啊广告[代理]转化上报API";

    @Autowired
    private BizProperties bizProperties;
    private OkHttpClient client;

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
    }

    @Async
    @Override
    public void feedback(Subscribe subscribe) {
        log.info("{}=>订单号:{},手机号:{},推啊oId:{}",LOG_TAG,subscribe.getId(),subscribe.getMobile(),subscribe.getDeviceInfo());
        this.effectFeedback(subscribe.getDeviceInfo(),subscribe.getIp(),subscribe.getUserAgent());
    }

    public void effectFeedback(String aOId, String ip, String ua){
        try {
            HttpUrl httpUrl = HttpUrl.parse(bizProperties.getTuiaAdEffectApi())
                                     .newBuilder()
                                     .addQueryParameter("a_oId", aOId)
                                     .addQueryParameter("advertKey", bizProperties.getTuiaAdAdvertKey())
                                     .addQueryParameter("subType", "3")
                                     .addQueryParameter("ip", ip)
                                     .addQueryParameter("ua", ua)
                                     .build();

            log.info("{}-请求:{}",LOG_TAG,httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                                                   .build();
            try (Response response = client.newCall(request)
                                           .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-响应:{}",LOG_TAG,content);
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("{}-异常:",LOG_TAG,e);
        }
    }


}
