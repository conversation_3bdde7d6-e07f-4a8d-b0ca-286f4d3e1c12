package com.eleven.cms.ad;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 众智汇融业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/2 10:18
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "zhongzhihuirong",ignoreUnknownFields = true)
public class ZhongZhiHuiRongProperties {
    //获取验证码接口
    private String getSendSmsUrl;
    //提交验证码接口
    private String submitSendSmsUrl;
}
