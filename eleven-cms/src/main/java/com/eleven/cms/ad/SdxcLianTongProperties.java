package com.eleven.cms.ad;

import com.eleven.cms.config.GuangDongMobileStockProduct;
import com.eleven.cms.config.SdxcLianTongProduct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 时代星辰联通业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/2 10:18
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "sdxc-liantong",ignoreUnknownFields = true)
public class SdxcLianTongProperties {
    //获取验证码接口
    private String getSendSmsUrl;
    //提交验证码接口
    private String submitSendSmsUrl;
    private Map<String, SdxcLianTongProduct> productMap;
}
