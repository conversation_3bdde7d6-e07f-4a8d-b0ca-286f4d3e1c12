package com.eleven.cms.ad;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:40
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "kasaistock.yidong",ignoreUnknownFields = true)
public class KaSaiStockYidongProperties {
    private String loadTokenUrl;
    private String sendSmsUrl;
    private String loginUrl;
    private String bookProductUrl;
    private String linkNum;
    private String token;
    private String sourceUrl;
}
