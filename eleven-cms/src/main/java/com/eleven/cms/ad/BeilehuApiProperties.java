package com.eleven.cms.ad;

import com.eleven.cms.config.BeilehuProduct;
import com.eleven.cms.config.OtherRecharge;
import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:业务相关配置类
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "beilehu")
public class BeilehuApiProperties {
    private String rechargeUrl;
    private String orderQueryUrl;
    private String cpId;
    private String cpSecret;
    private Map<String, BeilehuProduct> beilehuProductMap;
}
