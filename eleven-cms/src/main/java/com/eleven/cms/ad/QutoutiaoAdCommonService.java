package com.eleven.cms.ad;

import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.OkHttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;


/**
 * Author: <EMAIL>
 * Date: 2020/10/29 11:51
 * Desc: 头条广告转化api
 * 头条api文档: https://ad.oceanengine.com/openapi/doc/index.html?id=****************
 */
@Slf4j
@Component
public class QutoutiaoAdCommonService implements IAdFeedbackCommonService {
    private static final String LOG_TAG = "趣头条广告转化上报API";

    //@Autowired
    //private BizProperties bizProperties;
    private OkHttpClient client;

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
    }
    
    @Async
    @Override
    public void feedback(Subscribe subscribe, AdPlatform adPlatform, AccountCofig accountCofig) {
        log.info("{}=>订单号:{},手机号:{},callbackUrl:{}",LOG_TAG,subscribe.getId(),subscribe.getMobile(),subscribe.getDeviceInfo());
        this.effectFeedback(subscribe.getDeviceInfo());
    }

    public void effectFeedback(String callbackUrl) {
        try {
            HttpUrl httpUrl = HttpUrl.parse(URLDecoder.decode(callbackUrl))
                                     .newBuilder()
                                     .addQueryParameter("op2", "26")
                                     .build();
            log.info("{}-请求:{}", LOG_TAG, httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                                                   .build();
            try (Response response = client.newCall(request)
                                           .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-响应:{}", LOG_TAG, content);
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("{}-异常:", LOG_TAG, e);
        }
    }

    public static void main(String[] args) throws UnsupportedEncodingException {


        String decode = URLDecoder.decode(
                "https%3A%2F%2Fadv.aiclk.com%2Ftools%2Fcallback%2Ftarget-url-monitor%2F44957%3Fmd5%3Da954c8616fb6c0e4ed8e3831054c8e64",
                StandardCharsets.UTF_8.name());
        System.out.println("decode = " + decode);
        decode = URLDecoder.decode(
                decode,
                StandardCharsets.UTF_8.name());
        System.out.println("decode = " + decode);
    }

}
