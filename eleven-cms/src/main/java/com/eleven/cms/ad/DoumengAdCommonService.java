package com.eleven.cms.ad;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.OkHttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;


/**
 * Author: <EMAIL>
 * Date: 2020/10/29 11:51
 * Desc: 豆盟广告[代理]转化上报API
 */
@Slf4j
@Component
public class DoumengAdCommonService implements IAdFeedbackCommonService {

    private static final String LOG_TAG = "豆盟广告[代理]转化上报API";

    @Autowired
    private BizProperties bizProperties;
    private OkHttpClient client;

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
    }

    @Async
    @Override
    public void feedback(Subscribe subscribe, AdPlatform adPlatform,AccountCofig accountCofig) {
        log.info("{}=>订单号:{},手机号:{},dkey:{}",LOG_TAG,subscribe.getId(),subscribe.getMobile(),subscribe.getDeviceInfo());
        String adreporturl = adPlatform.getAdReportUrl();
        this.orderDeepTranslate(subscribe.getDeviceInfo(),adreporturl,accountCofig);
    }

    public void orderDeepTranslate(String dkey,String adreporturl,AccountCofig accountCofig) {
        try {
            /*String accountId = bizProperties.getDoumengAdAccoundId();
            String secret = bizProperties.getDoumengAdSecret();*/
            String config = accountCofig.getConfig();
            Map configMap = JSONObject.parseObject(config, Map.class);
            String secret = configMap.get("doumengAdSecret").toString();
            String accountId = configMap.get("doumengAdAccoundId").toString();
            String status = "3";
            String orderDate = DateUtils.yyyyMMdd.get().format(new Date());
            String encryptCode = DigestUtils.md5DigestAsHex((accountId + dkey + status + orderDate + secret).getBytes(StandardCharsets.UTF_8));

            RequestBody formBody = new FormBody.Builder()
                    .add("accountId", accountId)
                    .add("encryptCode", encryptCode)
                    .add("dkey", dkey)
                    .add("status", status)
                    .add("orderDate", orderDate)
                    .build();

            Request request = new Request.Builder().url(adreporturl)
                                                   .post(formBody)
                                                   .build();
            log.info("{}-dkey:{},accountId:{},encryptCode:{},status:{},orderDate:{},请求:{},", LOG_TAG, dkey, accountId, encryptCode, status, orderDate, request.toString());
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-dkey:{},响应:{}", LOG_TAG, dkey, content);
            }

        } catch (IOException e) {
            //e.printStackTrace();
            log.warn("{}-dkey:{},异常:", LOG_TAG, dkey, e);
        }
    }

}
