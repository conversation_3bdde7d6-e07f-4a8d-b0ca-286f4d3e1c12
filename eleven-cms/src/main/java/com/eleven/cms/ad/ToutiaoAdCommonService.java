package com.eleven.cms.ad;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.OkHttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Date;


/**
 * Author: <EMAIL>
 * Date: 2020/10/29 11:51
 * Desc: 头条广告转化api
 * 头条api文档: https://ad.oceanengine.com/openapi/doc/index.html?id=****************
 */
@Slf4j
@Component
public class ToutiaoAdCommonService implements IAdFeedbackCommonService {
    private static final String LOG_TAG = "头条广告线索上报API";

    @Autowired
    private BizProperties bizProperties;
    private OkHttpClient client;

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
    }

    @Async
    @Override
    public void feedback(Subscribe subscribe, AdPlatform adPlatform, AccountCofig accountCofig) {
        log.info("{}=>订单号:{},手机号:{},link:{}",LOG_TAG,subscribe.getId(),subscribe.getMobile(),subscribe.getDeviceInfo());
        String adreporturl = adPlatform.getAdReportUrl();
        this.trackFeedback(subscribe.getDeviceInfo(),new Date(),adreporturl);
    }
    
    public void trackFeedback(String link, Date convTime,String adreporturl) {
        try {
            HttpUrl httpUrl = HttpUrl.parse(adreporturl)
                                     .newBuilder()
                                     .addQueryParameter("link", link)
                                     .addQueryParameter("event_type", "19")
                                     .addQueryParameter("conv_time", String.valueOf(convTime.getTime() / 1000L))
                                     .build();
            log.info("{}-请求:{}", LOG_TAG, httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl)
                                                   .build();
            try (Response response = client.newCall(request)
                                           .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body()
                                         .string();
                log.info("{}-响应:{}", LOG_TAG, content);
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("{}-异常:", LOG_TAG, e);
        }
    }

}
