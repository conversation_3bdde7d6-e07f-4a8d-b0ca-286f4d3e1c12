package com.eleven.cms.ad;

import com.eleven.cms.config.RuanYouTongProduct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 软游通业务
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/2 10:18
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "ruanyoutong",ignoreUnknownFields = true)
public class RuanYouTongProperties {
    //获取验证码接口
    private String getSendSmsUrl;
    //提交验证码接口
    private String submitSendSmsUrl;
    //广州旭同移动业务通知地址
    private String callbackUrl;
    private Map<String, RuanYouTongProduct> productMap;
}
