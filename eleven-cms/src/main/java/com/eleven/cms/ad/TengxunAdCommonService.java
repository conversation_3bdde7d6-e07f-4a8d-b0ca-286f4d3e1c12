package com.eleven.cms.ad;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.config.BizProperties;
import com.eleven.cms.config.TengxunAdMaiheProperties;
import com.eleven.cms.entity.AccountCofig;
import com.eleven.cms.entity.AdPlatform;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.OkHttpClientUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;


/**
 * Author: <EMAIL>
 * Date: 2020/10/29 11:51
 * Desc: 腾讯广告[麦禾]转化上报API
 * 头条api文档: https://ad.oceanengine.com/openapi/doc/index.html?id=****************
 */
@Slf4j
@Component
public class TengxunAdCommonService implements IAdFeedbackCommonService {
    private static final String LOG_TAG = "腾讯广告[麦禾]转化上报API";

    @Autowired
    private BizProperties bizProperties;
    private OkHttpClient client;
    private ObjectMapper mapper;
    @Autowired
    private TengxunAdMaiheProperties tengxunAdMaiheProperties;
    public static final MediaType JSON
            = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
        this.mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    @Async
    @Override
    public void feedback(Subscribe subscribe, AdPlatform adPlatform, AccountCofig accountCofig) {
        /*final String mobile = subscribe.getMobile();
        String clickId = subscribe.getDeviceInfo();
        String actionTime = String.valueOf(System.currentTimeMillis() / 1000L);
        final ObjectNode action = mapper.createObjectNode();
        //action.put("outer_action_id",mobile);
        action.put("action_time",actionTime);
        action.put("action_type","PURCHASE");
        action.put("url","crbt.cdyrjygs.com");
        action.set("trace",mapper.createObjectNode().put("click_id",clickId));
        final ArrayNode actions = mapper.createArrayNode().add(action);
        final ObjectNode root = mapper.createObjectNode().set("actions",actions);
        trackFeedback(root.toPrettyString());*/


        /*旧版格式*/
        Map<String, Object> rootMap = new HashMap<>();
        List<Object> list = new ArrayList<>();
        Map<String, Object> actionsMap = new HashMap<>();
        Map<String, Object> traceMap = new HashMap<>();
        String time = new Date().getTime()/1000 + "";
        String clickId = subscribe.getDeviceInfo();
        log.info("{}=>订单号:{},手机号:{},link:{}",LOG_TAG,subscribe.getId(), subscribe.getMobile(),clickId);

        String config = accountCofig.getConfig();
        Map configMap = JSONObject.parseObject(config, Map.class);
        rootMap.put("account_id",configMap.get("accountId").toString());//账户id
        rootMap.put("user_action_set_id",configMap.get("userActionSetId").toString());//应用id
        traceMap.put("click_id",clickId);
        actionsMap.put("trace", com.alibaba.fastjson.JSON.toJSON(traceMap));
        actionsMap.put("url",configMap.get("url").toString());//回调地址
        actionsMap.put("action_time",time);
        actionsMap.put("action_type",configMap.get("actionType").toString());
        list.add(com.alibaba.fastjson.JSON.toJSON(actionsMap));
        rootMap.put("actions",list);
        String adreporturl = adPlatform.getAdReportUrl();
        String accessToken = configMap.get("accessToken").toString();
        trackFeedback(JSONObject.toJSONString(rootMap),time,adreporturl,accessToken);
        /*log.info("接口路径url：" + stringBuilder);
        log.info("接口参数" + JSONObject.toJSONString(rootMap));*/

    }
    
    public void trackFeedback(String raw, String time,String adreporturl,String accessToken) {
        try {
            RequestBody body = RequestBody.create(JSON, raw);
            HttpUrl httpUrl = HttpUrl.parse(adreporturl)
                                     .newBuilder()
                                     .addQueryParameter("access_token", accessToken)
                                     .addQueryParameter("timestamp",time)
                                     .addQueryParameter("nonce","mh" + time)
                                      .build();
            Request request = new Request.Builder().url(httpUrl)
                                                   .post(body)
                                                   .build();
            log.info("{}-请求:{},raw:\n{}", LOG_TAG, request.toString(),raw);

            try (Response response = client.newCall(request)
                                           .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-响应:{}", LOG_TAG, content);
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("{}-异常:", LOG_TAG, e);
        }
    }

}
