package com.eleven.cms.ad;

import com.eleven.cms.config.FeeChargeProduct;
import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * 话费充值配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/17 16:58
 **/
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "fee-charge-api")
public class FeeChargeProperties {

    //生产环境
    private String baseUrl;
    //话费充值接口
    private String feeChargeUrl;
    //话费充值订单查询
    private String feeChargeOrderQueryUrl;
    //话费余额查询
    private String feeQueryBalanceUrl;
    //商户号
    private String salerId;
    //密钥
    private String key;
    //话费充值通知地址
    private String callbackUrl;
    private Map<String, FeeChargeProduct> feeChargeProductMap;
}
