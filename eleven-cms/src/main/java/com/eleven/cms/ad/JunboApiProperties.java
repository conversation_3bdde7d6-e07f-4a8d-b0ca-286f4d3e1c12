package com.eleven.cms.ad;

import com.eleven.cms.config.OtherRecharge;
import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * Author: <EMAIL>
 * Date: 2019/12/11 15:55
 * Desc:业务相关配置类
 */
@Data
@SpringBootConfiguration
@ConfigurationProperties(prefix = "junbo-api")
public class JunboApiProperties {
    private String baseUrl;
    private String rechargeVipUrl;
    private String orderQueryUrl;
    private String uid;
    private String version;
    private String partnerNo;
    private String rsaPrivateKeyPkcs8;
    private String callbackUrl;
    private String key;
    private Map<String, OtherRecharge> otherRechargeMap;
}
