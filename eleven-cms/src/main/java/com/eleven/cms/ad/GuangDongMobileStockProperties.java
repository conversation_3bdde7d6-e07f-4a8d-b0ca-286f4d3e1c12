package com.eleven.cms.ad;

import com.eleven.cms.config.GuangDongMobileStockProduct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 广东移动存量配置
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/25 17:27
 **/
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "guangdong.stock",ignoreUnknownFields = true)
public class GuangDongMobileStockProperties {
    //创建订单
    private String createOrderUrl;
    //发送短信验证码
    private String sendSmsUrl;
    //提交短信验证码
    private String submitSmsUrl;
    //应用ID
    private String appId;
    //私钥
    private String privateKey;
    //公钥
    private String publicKey;

    private Map<String, GuangDongMobileStockProduct> productMap;
}
