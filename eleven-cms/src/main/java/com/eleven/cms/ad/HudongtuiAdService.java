package com.eleven.cms.ad;

import com.eleven.cms.config.BizProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.OkHttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;


/**
 * Author: <EMAIL>
 * Date: 2020/10/29 11:51
 * Desc: 互动推广告转化上报api
 */
@Slf4j
@Component
public class HudongtuiAdService implements IAdFeedbackService {

    private static final String LOG_TAG = "互动推广告转化上报API";

    @Autowired
    private BizProperties bizProperties;
    private OkHttpClient client;

//    @PostConstruct
    public void init() {
        this.client = OkHttpClientUtils.getNewInstance();
    }

    @Async
    @Override
    public void feedback(Subscribe subscribe) {
        log.info("{}=>订单号:{},手机号:{},callbackUrl:{}",LOG_TAG,subscribe.getId(),subscribe.getMobile(),subscribe.getDeviceInfo());
        this.effectFeedback(subscribe.getDeviceInfo(),subscribe.getMobile());
    }

    public void effectFeedback(String adClick,String uid) {
        try {
            HttpUrl httpUrl = HttpUrl.parse(bizProperties.getHudongtuiAdEffectApi())
                                     .newBuilder()
                                     .addQueryParameter("ad_click", adClick)
                                     //转化类型1:表单预约2:下载3:激活4:注册5:付费行为 6:下单7:微信授权 17、复制微信号
                                     .addQueryParameter("type", "6")
                                     //用户唯一标识。例用户打开广告主落地页并注册成功，uid传用户A的用户ID 无用户ID的，可传0
                                     .addQueryParameter("uid", uid)
                                     .addQueryParameter("timestamp", DateUtils.now())
                                     .build();

            log.info("{}-请求:{}", LOG_TAG, httpUrl.toString());
            Request request = new Request.Builder().url(httpUrl).build();
            try (Response response = client.newCall(request)
                                           .execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                String content = response.body().string();
                log.info("{}-响应:{}", LOG_TAG, content);
            }
        } catch (IOException e) {
            //e.printStackTrace();
            log.warn("{}-异常:", LOG_TAG, e);
        }
    }

}
