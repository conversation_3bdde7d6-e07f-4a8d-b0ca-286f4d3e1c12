package com.eleven.cms.log;

import com.eleven.cms.entity.Subscribe;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;

/**
 * Author: <EMAIL>
 * Date: 2020/11/17 11:03
 * Desc: 业务日志工具类
 */
@Slf4j
public class BizLogUtils {
    
    private static final ObjectMapper MAPPER = new ObjectMapper().disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    
    public static void logVisit(VisitLog l){
        try {
            log.warn(MAPPER.writeValueAsString(l));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void logOrder(OrderLog l){
        try {
            log.warn(MAPPER.writeValueAsString(l));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void logSubscribe(SubscribeLog l){
        try {
            log.warn(MAPPER.writeValueAsString(l));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void logSubscribe(Subscribe subscribe){
        //final SubscribeLog subscribeLog = SubscribeLog.builder()
        //                                              .bizType(subscribe.getBizType())
        //                                              .mobile(subscribe.getMobile())
        //                                              .isp(subscribe.getIsp())
        //                                              .province(subscribe.getProvince())
        //                                              .city(subscribe.getCity())
        //                                              .pageUrl(subscribe.getSource())
        //                                              .ip(subscribe.getIp())
        //                                              .ua(subscribe.getUserAgent())
        //                                              .miguChannel(subscribe.getChannel())
        //                                              .subChannel(subscribe.getSubChannel())
        //                                              .copyrightId(subscribe.getCopyrightId())
        //                                              .dxToneCode(subscribe.getDxToneCode())
        //                                              .ltRingId(subscribe.getLtRingId())
        //                                              .ispOrderNo(subscribe.getIspOrderNo())
        //                                              .extra(subscribe.getId())
        //                                              .build();
        //logSubscribe(subscribeLog);
    }

    public static void main(String[] args) throws JsonProcessingException {
        String json = "{\"type\":\"pvuv\",\"action\":\"visit\",\"date\":\"2020-11-17T13:40:20.582999\",\"bizType\":null,\"finger\":\"adfasdfasfd\",\"addr\":null,\"page\":null,\"ip\":null,\"ua\":null,\"miguChannel\":null,\"subChannel\":null,\"extra\":null,\"remark\":null}\n";
        VisitLog visitLog = MAPPER.readValue(json, VisitLog.class);
        System.out.println(visitLog);
    }
}
