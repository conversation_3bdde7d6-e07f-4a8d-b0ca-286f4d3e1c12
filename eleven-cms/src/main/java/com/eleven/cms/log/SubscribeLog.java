package com.eleven.cms.log;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Author: <EMAIL>
 * Date: 2020/11/17 12:00
 * Desc: 渠道信息流订阅日志
 */
@Data
@Builder(toBuilder=true)
@NoArgsConstructor
@AllArgsConstructor
public class SubscribeLog {
    @Builder.Default
    private String type = BizLogConstants.LOG_TYPE_SUBSCRIBE;
    private String bizType; //业务类型
    @Builder.Default
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime date = LocalDateTime.now();
    private String finger;//finger+ip 此字段暂为空,根据ip匹配
    private String mobile; //手机号
    private String isp; //运营商
    private String province; //省份
    private String city; //城市
    private String pageUrl; //访问地址url
    private String ip; //ip
    private String ua;
    private String miguChannel; //咪咕渠道
    private String subChannel;
    private String copyrightId; //版权id
    private String dxToneCode; //电信铃音编码
    private String ltRingId; //联通铃音ID
    private String ispOrderNo; //电信一键开通订单号
    private String extra;
    private String remark;
}
