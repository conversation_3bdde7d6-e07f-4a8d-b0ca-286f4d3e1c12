package com.eleven.cms.log;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;

/**
 * Author: ye<PERSON><PERSON><PERSON>@qq.com
 * Date: 2020/11/17 11:03
 * Desc: 业务日志常量
 */
@Slf4j
public class BizLogConstants {
    
    public static final String LOG_TYPE_VISIT = "VISIT";
    public static final String LOG_TYPE_ORDER = "ORDER";
    public static final String LOG_TYPE_SUBSCRIBE = "SUBSCRIBE";
    public static final String BIZ_TYPE_VRBT = "VRBT";
}
