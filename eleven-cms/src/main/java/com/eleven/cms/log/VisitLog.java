package com.eleven.cms.log;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Author: <EMAIL>
 * Date: 2020/11/17 12:00
 * Desc: 访问日志
 */
@Data
@Builder(toBuilder=true)
@NoArgsConstructor
@AllArgsConstructor
public class VisitLog {
    @Builder.Default
    private String type = BizLogConstants.LOG_TYPE_VISIT;
    private String bizType;
    @Builder.Default
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime date = LocalDateTime.now();
    @Builder.Default
    private String action = "view_page";
    private String finger;//finger+ip
    private String pageUrl; //访问地址url
    private String pageName; //具体页面,比如主页,列表页,详情页
    private String ip;
    private String ua;
    private String referer;
    private String miguChannel;
    private String subChannel;
    private String copyrightId; //版权id
    private String extra;
    private String remark;
}
