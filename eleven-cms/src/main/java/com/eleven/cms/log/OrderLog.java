package com.eleven.cms.log;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Author: <EMAIL>
 * Date: 2020/11/17 12:00
 * Desc: 页面订购日志(真实用户的自然订购)
 */
@Data
@Builder(toBuilder=true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderLog {
    @Builder.Default
    private String type = BizLogConstants.LOG_TYPE_ORDER;
    private String bizType; //业务类型
    @Builder.Default
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime date = LocalDateTime.now();
    private String finger; //finger+ip
    private String mobile; //手机号
    private String province; //省份
    private String city; //城市
    private String pageUrl; //访问地址url
    private String miguChannel; //咪咕渠道
    private String subChannel;
    private String miguResCode; //咪咕订购返回code
    private String miguResMsg; //咪咕订购返回结果
    private String copyrightId; //版权id
    private String extra;
    private String remark;
}
