package com.eleven.cms.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;


/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2020/11/17 10:57
 * Desc: 自定义业务日志过滤器
 */
public class NoneBizLogFilter extends Filter<ILoggingEvent> {

    @Override
    public FilterReply decide(ILoggingEvent event) {
        //System.out.println( event.getLoggerName());
        return event.getLevel() == Level.WARN && event.getLoggerName().contains("BizLogUtils") ? FilterReply.DENY : FilterReply.ACCEPT;
    }

}