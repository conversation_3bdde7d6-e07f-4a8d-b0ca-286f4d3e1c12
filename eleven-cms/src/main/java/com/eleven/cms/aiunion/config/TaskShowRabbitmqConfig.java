package com.eleven.cms.aiunion.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class TaskShowRabbitmqConfig {

    //普通队列
    public static final String PROFILE_QUEUE="talkShow.profile.task.queue";
    public static final String PROFILE_EXCHANGE="talkShow.profile.task.exchange";
    public static final String PROFILE_ROUT_KEY ="talkShow.profile.routKey";




    //死信配置
    public static final String PROFILE_EXCHANGE_DELAY ="talkShow.profile.exchange.delay";
    public static final String PROFILE_QUEUE_DELAY ="talkShow.profile.queue.delay";
    public static final String PROFILE_ROUT_KEY_DELAY ="talkShow.profile.routKey.delay";



    //普通队列
    public static final String RECORD_QUEUE="talkShow.record.task.queue";
    public static final String RECORD_EXCHANGE="talkShow.record.task.exchange";
    public static final String RECORD_ROUT_KEY ="talkShow.record.routKey";




    //死信配置
    public static final String RECORD_EXCHANGE_DELAY ="talkShow.record.exchange.delay";
    public static final String RECORD_QUEUE_DELAY ="talkShow.record.queue.delay";
    public static final String RECORD_ROUT_KEY_DELAY ="talkShow.record.routKey.delay";


    //制作脱口秀队列
    public static final String CREATE_SHOW_QUEUE="createShow.record.task.queue2";
    public static final String CREATE_SHOW_EXCHANGE="createShow.record.task.exchange";
    public static final String CREATE_SHOW_ROUT_KEY ="createShow.record.routKey";



    //制作脱口秀死信配置
    public static final String CREATE_SHOW_EXCHANGE_DELAY ="createShow.record.exchange.delay";
    public static final String CREATE_SHOW_QUEUE_DELAY ="createShow.record.queue.delay2";
    public static final String CREATE_SHOW_ROUT_KEY_DELAY ="createShow.record.routKey.delay";


    @Bean
    public Queue createShowDelayQueue() {
        Map<String, Object> map = new HashMap<>(3);
        map.put("x-message-ttl", 1000*10);
        map.put("x-dead-letter-exchange", CREATE_SHOW_EXCHANGE);
        map.put("x-dead-letter-routing-key", CREATE_SHOW_ROUT_KEY);
        return new Queue(CREATE_SHOW_QUEUE_DELAY, true, false, false, map);
    }


    @Bean
    public DirectExchange createShowDelayExchange() {
        return new DirectExchange(CREATE_SHOW_EXCHANGE_DELAY, true, false);
    }


    @Bean
    public Binding createShowdDelayBinding() {
        return BindingBuilder.bind(createShowDelayQueue()).to(createShowDelayExchange()).with(CREATE_SHOW_ROUT_KEY_DELAY);
    }


    @Bean
    public Queue createShowQueue() {
        return new Queue(CREATE_SHOW_QUEUE,true);
    }

    @Bean
    DirectExchange createShowExchange() {
        return new DirectExchange(CREATE_SHOW_EXCHANGE,true,false);
    }

    @Bean
    Binding bindingDirectCreateShow() {
        return BindingBuilder.bind(createShowQueue()).to(createShowExchange()).with(CREATE_SHOW_ROUT_KEY);
    }



    @Bean
    public Queue RecordDelayQueue() {
        Map<String, Object> map = new HashMap<>(3);
        map.put("x-message-ttl", 1000*30);
        map.put("x-dead-letter-exchange", RECORD_EXCHANGE);
        map.put("x-dead-letter-routing-key", RECORD_ROUT_KEY);
        return new Queue(RECORD_QUEUE_DELAY, true, false, false, map);
    }


    @Bean
    public DirectExchange recordDelayExchange() {
        return new DirectExchange(RECORD_EXCHANGE_DELAY, true, false);
    }


    @Bean
    public Binding recordDelayBinding() {
        return BindingBuilder.bind(RecordDelayQueue()).to(recordDelayExchange()).with(RECORD_ROUT_KEY_DELAY);
    }


    @Bean
    public Queue recordQueue() {
        return new Queue(RECORD_QUEUE,true);
    }

    @Bean
    DirectExchange recordExchange() {
        return new DirectExchange(RECORD_EXCHANGE,true,false);
    }

    @Bean
    Binding bindingDirectRecord() {
        return BindingBuilder.bind(recordQueue()).to(recordExchange()).with(RECORD_ROUT_KEY);
    }


    /**
     * 脱口秀形象 死信队列
     */
    @Bean
    public Queue profileDelayQueue() {
        Map<String, Object> map = new HashMap<>(3);
        map.put("x-message-ttl", 1000*30);
        map.put("x-dead-letter-exchange", PROFILE_EXCHANGE);
        map.put("x-dead-letter-routing-key", PROFILE_ROUT_KEY);
        return new Queue(PROFILE_QUEUE_DELAY, true, false, false, map);
    }

    /**
     * 脱口秀形象 死信交换机
     */
    @Bean
    public DirectExchange profileDelayExchange() {
        return new DirectExchange(PROFILE_EXCHANGE_DELAY, true, false);
    }

    /**
     * 给死信队列绑定交换机
     */
    @Bean
    public Binding profileDelayBinding() {
        return BindingBuilder.bind(profileDelayQueue()).to(profileDelayExchange()).with(PROFILE_ROUT_KEY_DELAY);
    }

    @Bean
    public Queue profileQueue() {
        return new Queue(PROFILE_QUEUE,true);
    }

    @Bean
    DirectExchange profileExchange() {
        return new DirectExchange(PROFILE_EXCHANGE,true,false);
    }

    @Bean
    Binding bindingDirectProfile() {
        return BindingBuilder.bind(profileQueue()).to(profileExchange()).with(PROFILE_ROUT_KEY);
    }
}