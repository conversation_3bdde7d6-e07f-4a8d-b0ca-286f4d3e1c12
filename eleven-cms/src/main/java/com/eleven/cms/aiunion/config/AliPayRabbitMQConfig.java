package com.eleven.cms.aiunion.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
@Service
public class AliPayRabbitMQConfig {
    //普通队列
    public static final String ALIPAY_QUEUE="alipay.pay.query.queue";
    public static final String ALIPAY_EXCHANGE="alipay.pay.query.exchange";
    public static final String ALIPAY_ROUT_KEY ="alipay.pay.query.routKey";

    //死信配置
    public static final String ALIPAY_EXCHANGE_DELAY ="alipay.pay.query.exchange.delay";
    public static final String ALIPAY_QUEUE_DELAY ="alipay.pay.query.queue.delay";
    public static final String ALIPAY_ROUT_KEY_DELAY ="alipay.pay.query.routKey.delay";


    @Bean
    public Queue aliPayDelayQueue() {
        Map<String, Object> map = new HashMap<>(3);
        map.put("x-message-ttl", 1000*5);
        map.put("x-dead-letter-exchange", ALIPAY_EXCHANGE);
        map.put("x-dead-letter-routing-key", ALIPAY_ROUT_KEY);
        return new Queue(ALIPAY_QUEUE_DELAY, true, false, false, map);
    }


    @Bean
    public DirectExchange alipayDelayExchange() {
        return new DirectExchange(ALIPAY_EXCHANGE_DELAY, true, false);
    }


//    @Bean
//    public Binding aliPayDelayBinding() {
//        return BindingBuilder.bind(aliPayDelayQueue()).to(alipayDelayExchange()).with(ALIPAY_ROUT_KEY_DELAY);
//    }


    @Bean
    public Queue aliPayQueue() {
        return new Queue(ALIPAY_QUEUE,true);
    }

    @Bean
    DirectExchange aliPayExchange() {
        return new DirectExchange(ALIPAY_EXCHANGE,true,false);
    }
//
//    @Bean
//    Binding aliPayBindingDirect() {
//        return BindingBuilder.bind(aliPayQueue()).to(aliPayExchange()).with(ALIPAY_ROUT_KEY);
//    }

}
