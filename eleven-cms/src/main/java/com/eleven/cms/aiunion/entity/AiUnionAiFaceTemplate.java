package com.eleven.cms.aiunion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ai_union_ai_face_template
 * @Author: jeecg-boot
 * @Date:   2024-11-20
 * @Version: V1.0
 */
@Data
@TableName("ai_union_ai_face_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ai_union_ai_face_template对象", description="ai_union_ai_face_template")
public class AiUnionAiFaceTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**模板名称*/
	@Excel(name = "模板名称", width = 15)
    @ApiModelProperty(value = "模板名称")
    private String templateName;
	/**模板描述*/
	@Excel(name = "模板描述", width = 15)
    @ApiModelProperty(value = "模板描述")
    private String templateRemark;
	/**模板类名 0-女 1-男*/
	@Excel(name = "模板类名 0-女 1-男", width = 15)
    @ApiModelProperty(value = "模板类名 0-女 1-男")
    private Integer templateType;
	/**活动id*/
	@Excel(name = "活动id", width = 15)
    @ApiModelProperty(value = "活动id")
    private String activityId;
	/**素材id列表*/
	@Excel(name = "素材id列表", width = 15)
    @ApiModelProperty(value = "素材id列表")
    private String materialId;
	/**阿里云模板id*/
	@Excel(name = "阿里云模板id", width = 15)
    @ApiModelProperty(value = "阿里云模板id")
    private String templateId;
	/**模板图片插槽个数*/
	@Excel(name = "模板图片插槽个数", width = 15)
    @ApiModelProperty(value = "模板图片插槽个数")
    private Integer picSlotCount;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer orderBy;
	/**铃音图片地址*/
	@Excel(name = "铃音图片地址", width = 15)
    @ApiModelProperty(value = "铃音图片地址")
    private String picUrl;
	/**素材图url列表*/
	@Excel(name = "素材图url列表", width = 15)
    @ApiModelProperty(value = "素材图url列表")
    private String materialPicUrl;
	/**铃音播放地址*/
	@Excel(name = "铃音播放地址", width = 15)
    @ApiModelProperty(value = "铃音播放地址")
    private String videoUrl;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;
	/**模板人脸框json信息*/
	@Excel(name = "模板人脸框json信息", width = 15)
    @ApiModelProperty(value = "模板人脸框json信息")
    private String param;
	/**模板json信息*/
	@Excel(name = "模板json信息", width = 15)
    @ApiModelProperty(value = "模板json信息")
    private String clipsParam;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**穿越主题类型 1-穿越前世 2-穿越财神*/
    @Excel(name = "穿越主题类型 1-穿越前世 2-穿越财神", width = 15)
    @ApiModelProperty(value = "穿越主题类型 1-穿越前世 2-穿越财神")
    private Integer topicType;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
