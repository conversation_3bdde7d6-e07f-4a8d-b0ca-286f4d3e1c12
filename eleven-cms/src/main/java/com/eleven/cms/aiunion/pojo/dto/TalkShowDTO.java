package com.eleven.cms.aiunion.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class TalkShowDTO {
    private Integer aiRingStatus;
    private String tempId;
    private String profileId;
    private String  picUrl;
    private String  userId;
    private String  mobile;
    private String audioLink;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProfileRes{
        private String profileId;
        private String statue;
        private String errMsg;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecordRes{
        private String id;
        private String statue;
        private String errMsg;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskSowMQDTO {
        private String id;
    }
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskSowRecordMQDTO {
        private String id;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CreateTaskSowMQDTO {
        private String profileId;
        private String recordId;
        private String tempId;
    }
}
