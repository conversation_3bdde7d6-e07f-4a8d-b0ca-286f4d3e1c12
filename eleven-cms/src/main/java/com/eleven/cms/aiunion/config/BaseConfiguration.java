package com.eleven.cms.aiunion.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

@Configuration
public class BaseConfiguration {


    /**
     * 用于脱口秀的线程池
     */
    @Bean(name = "talkShowTaskExecutor")
    public ThreadPoolExecutor talkShowTaskExecutor() {
        return new ThreadPoolExecutor(
                1,                   // 核心线程数
                1,                   // 最大线程数
                0L, TimeUnit.MILLISECONDS, // 线程空闲时间
                new LinkedBlockingQueue<>(100), // 有界队列（防止内存溢出）
                new ThreadPoolExecutor.CallerRunsPolicy() // 任务溢出时由调用者线程执行
        );
    }
    @Bean(name = "talkShowQueryTaskExecutor")
    public ThreadPoolExecutor talkShowQueryTaskExecutor() {
        return new ThreadPoolExecutor(
                1,                   // 核心线程数
                1,                   // 最大线程数
                0L, TimeUnit.MILLISECONDS, // 线程空闲时间
                new LinkedBlockingQueue<>(100), // 有界队列（防止内存溢出）
                new ThreadPoolExecutor.CallerRunsPolicy() // 任务溢出时由调用者线程执行
        );
    }
}
