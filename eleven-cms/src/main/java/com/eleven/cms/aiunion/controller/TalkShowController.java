package com.eleven.cms.aiunion.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunion.entity.TalkShowProfileRecord;
import com.eleven.cms.aiunion.entity.TalkShowRecord;
import com.eleven.cms.aiunion.entity.TalkShowTemplate;
import com.eleven.cms.aiunion.pojo.dto.IdDTO;
import com.eleven.cms.aiunion.pojo.dto.TalkShowDTO;
import com.eleven.cms.aiunion.service.AiTalkShowService;
import com.eleven.cms.aiunion.service.IProfileRecordService;
import com.eleven.cms.aiunion.service.ITalkShowRecordService;
import com.eleven.cms.aiunion.service.ITalkShowTemplateService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;


@RestController
@RequestMapping("/cms/talkShow")
@Slf4j
@RequiredArgsConstructor
public class TalkShowController  {


	private final ITalkShowTemplateService talkShowTemplateService;

	private final ITalkShowRecordService talkShowRecordService;

	private final IProfileRecordService profileRecordService;

	private final AiTalkShowService aiTalkShowService;



	@AutoLog(value = "talk_show_template-分页列表查询")
	@ApiOperation(value="talk_show_template-分页列表查询", notes="talk_show_template-分页列表查询")
	@GetMapping(value = "/template/list")
	public Result<?> queryTempPageList( TalkShowTemplate talkShowTemplate,
										@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
										HttpServletRequest req) {
		QueryWrapper<TalkShowTemplate> queryWrapper = QueryGenerator.initQueryWrapper(talkShowTemplate, req.getParameterMap());
		Page<TalkShowTemplate> page = new Page<TalkShowTemplate>(pageNo, pageSize);
		queryWrapper.setEntity(talkShowTemplate);
		queryWrapper.eq("is_deleted", 0);
		queryWrapper.orderByDesc("id");
		IPage<TalkShowTemplate> pageList = talkShowTemplateService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	@AutoLog(value = "talk_show_record-分页列表查询")
	@ApiOperation(value="talk_show_record-分页列表查询", notes="talk_show_record-分页列表查询")
	@GetMapping(value = "/record/list")
	public Result<?> queryRecordPageList( TalkShowRecord talkShowRecord,
										  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
										  HttpServletRequest req) {
		QueryWrapper<TalkShowRecord> queryWrapper = QueryGenerator.initQueryWrapper(talkShowRecord, req.getParameterMap());
		Page<TalkShowRecord> page = new Page<TalkShowRecord>(pageNo, pageSize);
		queryWrapper.setEntity(talkShowRecord);
		queryWrapper.eq("is_deleted", 0);
		queryWrapper.orderByDesc("id");
		IPage<TalkShowRecord> pageList = talkShowRecordService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	@AutoLog(value = "profile_record-分页列表查询")
	@ApiOperation(value="profile_record-分页列表查询", notes="profile_record-分页列表查询")
	@GetMapping(value = "/profile/list")
	public Result<?> queryProfilePageList( TalkShowProfileRecord talkShowRecord,
										   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
										   HttpServletRequest req) {
		QueryWrapper<TalkShowProfileRecord> queryWrapper = QueryGenerator.initQueryWrapper(talkShowRecord, req.getParameterMap());
		Page<TalkShowProfileRecord> page = new Page<TalkShowProfileRecord>(pageNo, pageSize);
		queryWrapper.setEntity(talkShowRecord);
		queryWrapper.eq("is_deleted", 0);
		queryWrapper.orderByDesc("id");
		IPage<TalkShowProfileRecord> pageList = profileRecordService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	@AutoLog(value = "删除形象")
	@ApiOperation(value="删除形象", notes="删除形象")
	@PostMapping(value = "/profile/delete")
	public Result<?> deleteProfile(@RequestBody @Valid  IdDTO idDTO) {
		TalkShowProfileRecord talkShowProfileRecord = new TalkShowProfileRecord();
		talkShowProfileRecord.setId(idDTO.getId());
		talkShowProfileRecord.setIsDeleted(1);
		return Result.ok(SpringContextUtils.getBean(IProfileRecordService.class).updateById(talkShowProfileRecord));
	}


	@AutoLog(value = "查询任务")
	@ApiOperation(value="查询任务", notes="查询任务")
	@PostMapping(value = "/queryTaskResult")
	public Result<?> queryTaskResult(@RequestBody IdDTO idDTO) {
		return Result.ok(aiTalkShowService.queryRecordTaskResult(idDTO.getId()));
	}
	@AutoLog(value = "查询任务")
	@ApiOperation(value="查询任务", notes="查询任务")
	@PostMapping(value = "/queryProfileTaskResult")
	private Result<?> queryProfileTaskResult(@RequestBody IdDTO idDTO) {
		SpringContextUtils.getBean(AiTalkShowService.class).queryProfileTaskResult(idDTO.getId());
		return Result.ok("");
	}

	@AutoLog(value = "删除制作记录")
	@ApiOperation(value="删除制作记录", notes="删除制作记录")
	@PostMapping(value = "/record/delete")
	public Result<?> deleteRecord(@RequestBody @Valid  IdDTO idDTO) {
		TalkShowRecord talkShowRecord = new TalkShowRecord();
		talkShowRecord.setId(idDTO.getId());
		talkShowRecord.setIsDeleted(1);
		return Result.ok(SpringContextUtils.getBean(ITalkShowRecordService.class).updateById(talkShowRecord));
	}

	@AutoLog(value = "创建脱口秀")
	@ApiOperation(value="创建脱口秀", notes="创建脱口秀")
	@PostMapping(value = "/createTalkShow")
	public Result<?> createTalkShow(@RequestBody TalkShowDTO talkShowDTO) {
		return Result.ok(aiTalkShowService.createTalkShow(talkShowDTO));
	}

	@AutoLog(value = "创建脱口秀")
	@ApiOperation(value = "创建脱口秀", notes = "创建脱口秀")
	@PostMapping(value = "/createTalkShowVideo")
	public TalkShowDTO.RecordRes createTalkShowVideo(@RequestBody TalkShowDTO talkShowDTO) {
		TalkShowDTO.RecordRes result = new TalkShowDTO.RecordRes();
		TalkShowDTO.ProfileRes talkShowProfile = aiTalkShowService.createTalkShowProfile(talkShowDTO);

		if(StringUtils.equals(talkShowProfile.getStatue(),"fail")) {
			result.setStatue("fail");
			result.setErrMsg("制作形象异常");
			return result;
		}

		String profileId = talkShowProfile.getProfileId();
		talkShowDTO.setProfileId(profileId);
		try {
			result = aiTalkShowService.createTalkShow(talkShowDTO);
		} catch (Exception e) {
			result.setStatue("fail");
		}
		return result;
	}

	@AutoLog(value = "创建脱口秀形象")
	@ApiOperation(value="创建脱口秀形象", notes="创建脱口秀形象")
	@PostMapping(value = "/createProfile")
	public Result<?> createProfile(@RequestBody TalkShowDTO talkShowDTO) {
		return Result.ok(aiTalkShowService.createTalkShowProfile(talkShowDTO));
	}

	@ApiOperation(value = "获取创作记录", notes = "获取创作记录")
	@PostMapping(value = "/getTaskRecordById")
	public TalkShowRecord getTaskRecordById(@RequestBody IdDTO idDTO) {
		return talkShowRecordService.getById(idDTO.getId());
	}

	@ApiOperation(value = "删除创作记录", notes = "删除创作记录")
	@PostMapping(value = "/deleteTaskRecord")
	public Boolean deleteTaskRecord(@RequestBody @Valid IdDTO idDTO) {
		TalkShowRecord updateTalkShowRecord = new TalkShowRecord();
		updateTalkShowRecord.setId(idDTO.getId());
		updateTalkShowRecord.setIsDeleted(1);
		return talkShowRecordService.updateById(updateTalkShowRecord);
	}


	@ApiOperation(value = "更新创作记录", notes = "更新创作记录")
	@PostMapping(value = "/updateTaskRecord")
	public Result<?> updateTaskRecord(@RequestBody TalkShowRecord talkShowRecord) {
		talkShowRecordService.updateById(talkShowRecord);
		return Result.ok();
	}
}
