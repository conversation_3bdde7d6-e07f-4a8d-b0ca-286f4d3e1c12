package com.eleven.cms.aiunion.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 任务创作-图片换脸
 *
 * <AUTHOR>
 * @datetime 2024/11/4 15:04
 */

@Data
public class AiPicAiUnionFuseFaceTaskCreateDTO {
    @NotBlank(message = "用户唯一标识不能为空")
    private String userUniqueTag;
    @NotBlank(message = "上传图片不能为空！")
    private String url;

    private String templateId;

    private String recordId;

    @NotBlank(message = "咪咕任务id不能为空！")
    private String miguTaskId;

    @NotBlank(message = "咪咕token不能为空！")
    private String token;
    /**
     * 穿越主题类型 1-穿越前世 2-穿越财神
     */
    private Integer topicType = 1;
}
