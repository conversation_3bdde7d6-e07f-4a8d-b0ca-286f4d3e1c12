package com.eleven.cms.aiunion.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class DigitalRightDTO {





    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DigitalRightReq {

        private String packageId;

        private String mobile;
        private String code;

        private Integer payType;

        private String clientId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class QueryPayReq {
        private String orderNo;
        private String tradeNo;
        private String refundReason;
        private Integer payType;


    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RechargeRes {
        private String body;
        private String orderNo;
        private String accessToken;
    }
}
