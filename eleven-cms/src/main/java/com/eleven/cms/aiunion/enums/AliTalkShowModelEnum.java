package com.eleven.cms.aiunion.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum AliTalkShowModelEnum {

    NORMAL(1,"realman_avatar_picture_v2","realman_avatar_picture_create_role","普通模式"),
    DYNAMIC(2,"realman_avatar_picture_loopy","realman_avatar_picture_create_role_loopy","灵动模式"),
    ;


    private final String picModel;
    private final String videoModel;
    private final Integer code;
    private final String desc;

    AliTalkShowModelEnum(Integer code,String videoModel, String picModel, String desc) {
       this.picModel = picModel;
       this.videoModel = videoModel;
       this.desc = desc;
       this.code = code;
   }


   public static AliTalkShowModelEnum getEnumByCode(Integer code) {

       for (AliTalkShowModelEnum value : AliTalkShowModelEnum.values()) {
           if(Objects.equals(code,value.getCode())) {
               return value;
           }
       }
       return NORMAL;
   }

}
