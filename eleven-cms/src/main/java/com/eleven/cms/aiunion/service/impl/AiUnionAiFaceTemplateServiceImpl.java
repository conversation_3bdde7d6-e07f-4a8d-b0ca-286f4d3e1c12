package com.eleven.cms.aiunion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunion.entity.AiUnionAiFaceTemplate;
import com.eleven.cms.aiunion.mapper.AiUnionAiFaceTemplateMapper;
import com.eleven.cms.aiunion.service.IAiUnionAiFaceTemplateService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: ai_union_ai_face_template
 * @Author: jeecg-boot
 * @Date: 2024-11-20
 * @Version: V1.0
 */
@Service
public class AiUnionAiFaceTemplateServiceImpl extends ServiceImpl<AiUnionAiFaceTemplateMapper, AiUnionAiFaceTemplate> implements IAiUnionAiFaceTemplateService {


    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_UNION_FACE_TEMPLATE, key = "#root.methodName + ':' + #gender")
    @Override
    public List<String> getGenderTemplateIdList(Integer gender) {
        LambdaQueryWrapper<AiUnionAiFaceTemplate> queryWrapper = new LambdaQueryWrapper<AiUnionAiFaceTemplate>()
                .select(AiUnionAiFaceTemplate::getId).eq(AiUnionAiFaceTemplate::getTemplateType, gender)
                .eq(AiUnionAiFaceTemplate::getTopicType, 1)
                .eq(AiUnionAiFaceTemplate::getStatus, 1);
        List<AiUnionAiFaceTemplate> list = super.list(queryWrapper);
        return list.stream().map(AiUnionAiFaceTemplate::getId).collect(Collectors.toList());
    }

    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_UNION_FACE_TEMPLATE, key = "#root.methodName + ':' + #topicType")
    @Override
    public List<AiUnionAiFaceTemplate> getTopicTypeTemplateIdList(Integer topicType) {
        LambdaQueryWrapper<AiUnionAiFaceTemplate> queryWrapper = new LambdaQueryWrapper<AiUnionAiFaceTemplate>()
                .eq(AiUnionAiFaceTemplate::getTopicType, topicType)
                .eq(AiUnionAiFaceTemplate::getStatus, 1)
                .orderByAsc(AiUnionAiFaceTemplate::getOrderBy)
                .orderByAsc(AiUnionAiFaceTemplate::getId);
        return super.list(queryWrapper);
    }
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_AI_UNION_FACE_TEMPLATE, key = "#root.methodName + ':' + #id")
    public AiUnionAiFaceTemplate getById(Serializable id) {
        return super.getById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean save(AiUnionAiFaceTemplate aiUnionAiFaceTemplate) {
        return super.save(aiUnionAiFaceTemplate);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean updateById(AiUnionAiFaceTemplate aiUnionAiFaceTemplate) {
        return super.updateById(aiUnionAiFaceTemplate);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
