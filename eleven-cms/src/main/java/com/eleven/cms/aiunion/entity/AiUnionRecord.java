package com.eleven.cms.aiunion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ai_union_record
 * @Author: jeecg-boot
 * @Date:   2024-11-20
 * @Version: V1.0
 */
@Data
@TableName("ai_union_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ai_union_record对象", description="ai_union_record")
public class AiUnionRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**用户唯一标识*/
	@Excel(name = "用户唯一标识", width = 15)
    @ApiModelProperty(value = "用户唯一标识")
    private String userUniqueTag;
	/**任务id*/
	@Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "任务id")
    private String taskId;
	/**人脸模板对应的id*/
	@Excel(name = "人脸模板对应的id", width = 15)
    @ApiModelProperty(value = "人脸模板对应的id")
    private String faceTemplateId;
	/**原图url列表*/
	@Excel(name = "原图url", width = 15)
    @ApiModelProperty(value = "原图url")
    private String originalUrl;
	/**视频地址*/
	@Excel(name = "视频地址", width = 15)
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;
	/**视频封面图*/
	@Excel(name = "视频封面图", width = 15)
    @ApiModelProperty(value = "视频封面图")
    private String videoPicUrl;
    /**视频封面图*/
    @Excel(name = "生成的转化图", width = 15)
    @ApiModelProperty(value = "生成的转化图")
    private String convertUrls;
    /**模板名*/
    @Excel(name = "模板名", width = 15)
    @ApiModelProperty(value = "备注")
    private String templateName;
    /**模板备注*/
    @Excel(name = "模板备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String templateRemark;
	/**-1:生成失败 0:生成中 1:生成成功*/
	@Excel(name = "-1:生成失败 0:生成中 1:生成成功", width = 15)
    @ApiModelProperty(value = "-1:生成失败 0:生成中 1:生成成功")
    private Integer videoStatus;
    /**穿越主题类型 1-穿越前世 2-穿越财神*/
    @Excel(name = "穿越主题类型 1-穿越前世 2-穿越财神", width = 15)
    @ApiModelProperty(value = "穿越主题类型 1-穿越前世 2-穿越财神")
    private Integer topicType;
	/**逻辑删除 0:未删除 1:已删除*/
	@Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;

    @Excel(name = "取消咪咕任务次数", width = 15)
    @ApiModelProperty(value = "取消咪咕任务次数")
    private Integer cancelTaskCount;

	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 失败原因
     */
    private String failReason;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 用于科技主题
     * 访问 时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "创建日期")
    @TableField(exist = false)
    private Date addTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
