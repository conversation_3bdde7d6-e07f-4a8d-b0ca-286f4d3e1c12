package com.eleven.cms.aiunion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aiunion.constants.AIFaceSwappingConstants;
import com.eleven.cms.aiunion.entity.AiUnionAiFaceTemplate;
import com.eleven.cms.aiunion.entity.AiUnionRecord;
import com.eleven.cms.aiunion.pojo.dto.*;
import com.eleven.cms.aiunion.pojo.vo.TaskCreateResultVO;
import com.eleven.cms.aiunion.pojo.vo.TaskResultQueryVO;
import com.eleven.cms.aiunion.service.IAiUnionAiFaceTemplateService;
import com.eleven.cms.aiunion.service.IAiUnionRecordService;
import com.eleven.cms.aiunion.service.IAiUnionService;
import com.eleven.cms.aiunion.service.MiguAIGCApiService;
import com.eleven.cms.aiunion.utils.TencentCloudAIUtils;
import com.eleven.cms.aivrbt.annotaion.DistributedLock;
import com.eleven.cms.aivrbt.dto.PicFuseFaceDTO;
import com.eleven.cms.aivrbt.dto.PicFuseFaceMergeInfoDTO;
import com.eleven.cms.aivrbt.dto.VideoFaceFusionDTO;
import com.eleven.cms.aivrbt.dto.VideoFaceFusionMergeInfoDTO;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.remote.CommonSecurityService;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ai联盟服务实现类
 *
 * <AUTHOR>
 * @datetime 2024/11/20 17:41
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiUnionServiceImpl implements IAiUnionService {

    public static String AI_TAG = "ai联盟";

    private final IAiUnionRecordService aiUnionRecordService;

    private final IAiUnionAiFaceTemplateService aiUnionAiFaceTemplateService;

    private final AliMediaService aliMediaService;

    private final MiguAIGCApiService miguAIGCApiService;

    private final IAiUnionRecordService aiUinionRecordService;

    private static final String AIUNION_TASK_PARAM_KEY_PREFIX = "aiUnion:task:param:";

    private final ThreadPoolTaskExecutor aiRingThreadPoolTaskExecutor;

    @Autowired
    @Lazy
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CommonSecurityService commonSecurityService;

    /**
     * 查询任务创作结果
     *
     * @param taskId 任务ID，用于标识特定的任务创作结果
     * @return 返回任务创作结果的对象，如果找不到对应的任务则返回null
     */
    @Override
    public Object queryTaskResult(String taskId) {
        AiUnionRecord one = aiUnionRecordService.getOne(new LambdaQueryWrapper<AiUnionRecord>()
                .eq(AiUnionRecord::getId, taskId));
        if (one != null) {
            TaskResultQueryVO taskResultQueryVO = new TaskResultQueryVO();
            taskResultQueryVO.setId(one.getId());
            taskResultQueryVO.setTemplateName(one.getTemplateName());
            taskResultQueryVO.setCreateTime(one.getCreateTime());
            taskResultQueryVO.setRingUrl(one.getVideoUrl());
            taskResultQueryVO.setRingMakeStatus(one.getVideoStatus());
            taskResultQueryVO.setOriginalUrl(one.getOriginalUrl());
            taskResultQueryVO.setTemplateName(one.getTemplateName());
            taskResultQueryVO.setTemplateRemark(one.getTemplateRemark());
            taskResultQueryVO.setFailReason(one.getFailReason());
            taskResultQueryVO.setVideoPicUrl(one.getVideoPicUrl());
            return taskResultQueryVO;
        }
        return null;
    }


    /**
     * 任务创作-图片换脸
     *
     * @param aiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO
     * @return String 视频ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object createFacePicFusionTask(AiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        TaskCreateResultVO resultVO = new TaskCreateResultVO();

        //根据主题策略模板选择
//        templateChooseStrategy(aiPicFuseFaceTaskCreateDTO, resultVO);
        if (StrUtil.isBlank(aiPicFuseFaceTaskCreateDTO.getTemplateId())) {
            throw new JeecgBootException("请选择主题模板");
        }
        AiUnionRecord record = new AiUnionRecord();
        record.setUserUniqueTag(aiPicFuseFaceTaskCreateDTO.getUserUniqueTag());
        record.setFaceTemplateId(aiPicFuseFaceTaskCreateDTO.getTemplateId());
        record.setOriginalUrl(aiPicFuseFaceTaskCreateDTO.getUrl());
        record.setTopicType(aiPicFuseFaceTaskCreateDTO.getTopicType());
        aiUnionRecordService.save(record);
        resultVO.setId(record.getId());
        aiPicFuseFaceTaskCreateDTO.setRecordId(record.getId());
        //缓存任务参数
        redisUtil.set(AIUNION_TASK_PARAM_KEY_PREFIX + record.getId(), JacksonUtils.toJson(aiPicFuseFaceTaskCreateDTO), 1200);
        // 咪咕预扣减次数
        miguAIGCApiService.preReduceCount(aiPicFuseFaceTaskCreateDTO.getToken(), aiPicFuseFaceTaskCreateDTO.getMiguTaskId());
        //任务异步制作
        sendMakingMessage(aiPicFuseFaceTaskCreateDTO);
        return resultVO;
    }

    @Override
    @Transactional
    public Object createVideoFaceSwappingTask(UnionVideoFaceFusionDTO aiVideoFuseFaceTaskCreateDTO){
        Map<String, Object> extraInfoMap = new HashMap<>();
        TaskCreateResultVO taskCreateResultVO = new TaskCreateResultVO();
        extraInfoMap.put("手机号", aiVideoFuseFaceTaskCreateDTO.getUserUniqueTag());
        VideoFaceFusionDTO videoFaceFusionDTO = buildVideoFuseFaceTaskCreateDTO(aiVideoFuseFaceTaskCreateDTO);
        Integer gender = null;
        try {
            gender = TencentCloudAIUtils.parseFaceDetectionAndAnalysis(TencentCloudAIUtils.faceDetectionAndAnalysis(aiVideoFuseFaceTaskCreateDTO.getMergeInfos().get(0)));
        } catch (Exception e) {
            log.error("性别识别错误:{}", JSONObject.toJSONString(aiVideoFuseFaceTaskCreateDTO),e);
        }

        //调用腾讯接口创建任务
        String videoFaceFusion= TencentCloudAIUtils.videoFaceFusion(videoFaceFusionDTO,extraInfoMap);

        JSONObject jsonObject = JSONObject.parseObject(videoFaceFusion);
        String jobId="";
        JSONObject error = jsonObject.getJSONObject("Response").getJSONObject("Error");
        AiUnionRecord record = new AiUnionRecord();

        if(Objects.nonNull(error)){
            record.setTaskId("0");
            Object message = error.get("Message");
            record.setRemark(String.valueOf(message));
            record.setVideoStatus(-1);
        }else{
            jobId = String.valueOf(jsonObject.getJSONObject("Response").get("JobId"));
            record.setVideoStatus(0);
        }

        //新增任务记录
        record.setUserUniqueTag(aiVideoFuseFaceTaskCreateDTO.getUserUniqueTag());
        record.setOriginalUrl(aiVideoFuseFaceTaskCreateDTO.getMergeInfos().get(0));
        record.setTopicType(3);
        record.setFaceTemplateId(aiVideoFuseFaceTaskCreateDTO.getTemplateId());
        aiUnionRecordService.save(record);

        if(Objects.nonNull(error)){
            return record.getId();
        }

        VideoFaceSwappingJobDTO videoFaceSwappingJobDTO = new VideoFaceSwappingJobDTO();
        videoFaceSwappingJobDTO.setId(record.getId());
        videoFaceSwappingJobDTO.setUserUniqueTag(record.getUserUniqueTag());
        videoFaceSwappingJobDTO.setCreateTime(new Date());
        videoFaceSwappingJobDTO.setMiGuTaskId(aiVideoFuseFaceTaskCreateDTO.getMiGuTaskId());
        videoFaceSwappingJobDTO.setToken(aiVideoFuseFaceTaskCreateDTO.getToken());
        videoFaceSwappingJobDTO.setPicUrl(aiVideoFuseFaceTaskCreateDTO.getMergeInfos().get(0));


        // 存放到redis,使用定时任务便利查询到视频，然后上传到阿里oss
        redisUtil.hset(AIFaceSwappingConstants.AI_FACE_SWAPPING_KEY, jobId, JacksonUtils.toJson(videoFaceSwappingJobDTO), 3600);

        // 咪咕预扣减次数
        miguAIGCApiService.preReduceCount(aiVideoFuseFaceTaskCreateDTO.getToken(), aiVideoFuseFaceTaskCreateDTO.getMiGuTaskId());
        taskCreateResultVO.setId(record.getId());
        taskCreateResultVO.setGender(gender);
        return taskCreateResultVO;
    }
    private VideoFaceFusionDTO  buildVideoFuseFaceTaskCreateDTO(UnionVideoFaceFusionDTO unionVideoFaceFusionDTO) {
        VideoFaceFusionDTO videoFaceFusionDTO = new VideoFaceFusionDTO();
        videoFaceFusionDTO.setProjectId(unionVideoFaceFusionDTO.getActivityId());
        videoFaceFusionDTO.setModelId(unionVideoFaceFusionDTO.getMaterialId());
        List<VideoFaceFusionMergeInfoDTO> urlList = unionVideoFaceFusionDTO.getMergeInfos().stream().map(e -> {
            VideoFaceFusionMergeInfoDTO infoDTO = new VideoFaceFusionMergeInfoDTO();
            infoDTO.setUrl(e);
            return infoDTO;
        }).collect(Collectors.toList());
        videoFaceFusionDTO.setMergeInfos(urlList);
        return videoFaceFusionDTO;
    }

    /**
     * 定时任务轮询视频换脸任务
     */
    @DistributedLock(name = "querySwappingFaceTencentJobScheduled")
    @Scheduled(cron = "0/8 * * * * ?")
    public void queryTencentJobScheduled() {
        Map<Object, Object> jobIdMap = redisUtil.hmget(AIFaceSwappingConstants.AI_FACE_SWAPPING_KEY);
        if (CollectionUtil.isEmpty(jobIdMap)) {
            return;
        }
        DateTime current = DateUtil.date();
        jobIdMap.forEach((k, v) -> aiRingThreadPoolTaskExecutor.execute(() -> {
            String jobId = (String) k;
            VideoFaceSwappingJobDTO jobDTO = null;
            try {
                jobDTO = JacksonUtils.readValue((String) v, VideoFaceSwappingJobDTO.class);
            } catch (Exception e) {
                redisUtil.hdel(AIFaceSwappingConstants.AI_FACE_SWAPPING_KEY, jobId);
                return;
            }
            if (jobDTO == null) {
                redisUtil.hdel(AIFaceSwappingConstants.AI_FACE_SWAPPING_KEY, jobId);
                return;
            }
            //至少间隔11秒钟再查
            if (DateUtil.betweenMs(jobDTO.getCreateTime(), current) < 11) {
                return;
            }

            AiUnionRecord aiUnionRecord = aiUnionRecordService.getById(jobDTO.getId());
            if(Objects.isNull(aiUnionRecord)){
                return;
            }

            Map<String, Object> extraInfoMap = new HashMap<>();
            extraInfoMap.put("手机号", jobDTO.getUserUniqueTag());
            Map<String, String> jobResponseMap = com.eleven.cms.aivrbt.utils.TencentCloudAIUtils.parseVideoFuseFaceJobResponse(com.eleven.cms.aivrbt.utils.TencentCloudAIUtils.queryVideoFaceFusionJobId(jobId, extraInfoMap));

            String jobStatusCode = jobResponseMap.get("jobStatusCode");
            String videoUrl = jobResponseMap.get("videoUrl");

            //更新定时任务
            if (StrUtil.isBlank(jobStatusCode) || StrUtil.isNotBlank(jobStatusCode) && jobStatusCode.equals("5")) {
                aiUnionRecordService.update(new LambdaUpdateWrapper<AiUnionRecord>()
                        .set(AiUnionRecord::getVideoStatus, -1)
                         .set(AiUnionRecord::getCancelTaskCount,1)
                        .eq(AiUnionRecord::getId, jobDTO.getId()));
                redisUtil.hdel(AIFaceSwappingConstants.AI_FACE_SWAPPING_KEY, jobId);
                log.warn("{}:ai视频换脸任务失效! jobId：{},删除job轮询任务", AI_TAG, jobId);

                if(Objects.isNull(aiUnionRecord.getCancelTaskCount())||aiUnionRecord.getCancelTaskCount()==0){
                    miguAIGCApiService.cancelTask(jobDTO.getToken(), jobDTO.getMiGuTaskId());
                }
                return;
            }
            int cancelTaskCount =Objects.isNull(aiUnionRecord.getCancelTaskCount()) ? 0 : aiUnionRecord.getCancelTaskCount();
            String coverLink = null;
            if (StrUtil.isNotBlank(videoUrl)) {
                String aliVideoUrl = null;
                try {
                    String filePath = "tx" + "/" + IdWorker.get32UUID() + ".mp4";
                    aliVideoUrl = aliMediaService.putObjectRemoteUrlCdn(filePath, videoUrl);

                    String snapHotUrl = String.format("%s%s", aliVideoUrl, "?x-oss-process=video/snapshot,t_1,f_jpg,w_672,h_1024,m_fast");
                    String coverFilePath = "facieSwapping" + "/" + IdWorker.get32UUID() + ".jpg";
                    try {
                        coverLink = aliMediaService.putObjectRemoteUrlCdn(coverFilePath, snapHotUrl);
                    } catch (Exception e) {
                        log.error("上传图片错误:{}", JSONObject.toJSONString(jobDTO), e);
                    }
                } catch (Exception e) {
                    cancelTaskCount+=1;
                    log.error("{}:ai视频换脸任务上传阿里云失败! 原url：{}", AI_TAG, videoUrl, e);
                    if(Objects.isNull(aiUnionRecord.getCancelTaskCount())||aiUnionRecord.getCancelTaskCount()==0){
                        miguAIGCApiService.cancelTask(jobDTO.getToken(), jobDTO.getMiGuTaskId());
                    }
                }

                aiUnionRecordService.update(new LambdaUpdateWrapper<AiUnionRecord>()
                        .set(AiUnionRecord::getVideoStatus, StrUtil.isBlank(aliVideoUrl) ? 0 : 1)
                        .set(AiUnionRecord::getVideoUrl, aliVideoUrl)
                        .set(AiUnionRecord::getCancelTaskCount, cancelTaskCount)
                        .set(AiUnionRecord::getVideoPicUrl, coverLink)
                        .eq(AiUnionRecord::getId, jobDTO.getId()));
                redisUtil.hdel(AIFaceSwappingConstants.AI_FACE_SWAPPING_KEY, jobId);
                ReportResultDTO reportResultDTO = ReportResultDTO.builder().result(true)
                        .finalResults(Arrays.asList(ContentItemDTO.builder()
                                .content(videoUrl)
                                .contentType("video")
                                .build()))
                        .inputContents(Arrays.asList(ContentItemDTO.builder()
                                .content(jobDTO.getPicUrl())
                                .contentType("image")
                                .build()))
                        .build();
                miguAIGCApiService.reportResult(jobDTO.getToken(), jobDTO.getMiGuTaskId(), reportResultDTO);
                log.info("{}:ai视频换脸任务轮询完成! jobId：{},任务结果：{}", AI_TAG, jobId, StrUtil.isBlank(aliVideoUrl));
            }
        }));
    }
    private void sendMakingMessage(AiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        try {
            rabbitTemplate.convertAndSend(RabbitMQConfig.TX_FACE_ACROSS_QUEUE_NAME, aiPicFuseFaceTaskCreateDTO, new CorrelationData(aiPicFuseFaceTaskCreateDTO.getRecordId()));
        } catch (Exception e) {
            aiUnionRecordService.update(new LambdaUpdateWrapper<AiUnionRecord>()
                    .set(AiUnionRecord::getVideoStatus, -1)
                    .eq(AiUnionRecord::getId, aiPicFuseFaceTaskCreateDTO.getRecordId()));
            log.error("{}-腾讯云ai穿越图片换脸MQ发送消息异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getUserUniqueTag(), e);
            handleMiguTaskResult(aiPicFuseFaceTaskCreateDTO.getRecordId(), false, null);
        }
    }

    @Override
    public void handleMiguTaskResult(String recordId, Boolean result, String videoUrl) {
        String param = null;
        try {
            param = (String) redisUtil.get(AIUNION_TASK_PARAM_KEY_PREFIX + recordId);
            if (StrUtil.isNotBlank(param)) {
                ReportResultDTO reportResultDTO;
                AiPicAiUnionFuseFaceTaskCreateDTO paramDTO = JacksonUtils.readValue(param, AiPicAiUnionFuseFaceTaskCreateDTO.class);
                if (result) {
                    reportResultDTO = ReportResultDTO.builder().result(true)
                            .finalResults(Arrays.asList(ContentItemDTO.builder()
                                    .content(videoUrl)
                                    .contentType("video")
                                    .build()))
                            .inputContents(Arrays.asList(ContentItemDTO.builder()
                                    .content(paramDTO.getUrl())
                                    .contentType("image")
                                    .build()))
                            .build();
                } else {
                    reportResultDTO = ReportResultDTO.builder().result(false)
                            .inputContents(Arrays.asList(ContentItemDTO.builder()
                                    .content(paramDTO.getUrl())
                                    .contentType("image")
                                    .build()))
                            .build();
                }
                miguAIGCApiService.reportResult(paramDTO.getToken(), paramDTO.getMiguTaskId(), reportResultDTO);
                if (!result) {
                    miguAIGCApiService.cancelTask(paramDTO.getToken(), paramDTO.getMiguTaskId());
                }
                redisUtil.del(AIUNION_TASK_PARAM_KEY_PREFIX + recordId);
            } else {
                log.error("{},获取任务参数失败，参数为空，recordId:{}", AI_TAG, recordId);
            }
        } catch (Exception e) {
            log.error("{},上报咪咕制作结果失败,参数：{},失败原因{}", AI_TAG, param, e.getMessage(), e);
        }
    }

    /**
     * 根据用户唯一标签检查任务是否存在，并根据检查结果决定是否取消任务
     *
     * @param userUniqueTag 用户唯一标签，用于识别用户
     * @param token         用于调用API的令牌
     * @param taskId        需要检查的任务ID
     * @return 如果任务存在且未被取消，则返回true；否则返回false
     */
    @Override
    public Boolean checkTaskAndCancel(String userUniqueTag, String token, String taskId) {
        boolean checkResult = false;

        AiUnionRecord aiUnionRecord = aiUinionRecordService.getOne(new QueryWrapper<AiUnionRecord>()
                .eq("user_unique_tag", userUniqueTag)
                .orderByDesc("id")
                .last("limit 1"));
        if (Objects.nonNull(aiUnionRecord)) {
            String param = (String) redisUtil.get(AIUNION_TASK_PARAM_KEY_PREFIX + aiUnionRecord.getId());
            if (StrUtil.isNotBlank(param)) {
                AiPicAiUnionFuseFaceTaskCreateDTO paramDTO = JacksonUtils.readValue(param, AiPicAiUnionFuseFaceTaskCreateDTO.class);
                if (Objects.nonNull(paramDTO)) {
                    String miguTaskId = paramDTO.getMiguTaskId();
                    if (miguTaskId.equals(taskId)) {
                        checkResult = true;
                    }
                }
            }
        }
        if (!checkResult) {
            miguAIGCApiService.cancelTask(token, taskId);
        }
        return checkResult;
    }

    /**
     * 处理消息队列消息 创作任务
     *
     * @param aiPicFuseFaceTaskCreateDTO 任务消息体
     */
    @Override
    public void handleCreateTaskMsg(AiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {

        try {
            AiUnionAiFaceTemplate template = aiUnionAiFaceTemplateService.getById(aiPicFuseFaceTaskCreateDTO.getTemplateId());
            if (template == null) {
                throw new JeecgBootException("模板不存在");
            }
            String[] materialIdList = template.getMaterialId().split(",");

            final Map<String, Object> extraMap = new HashMap<String, Object>() {
                {
                    put("手机号", aiPicFuseFaceTaskCreateDTO.getUserUniqueTag());
                }
            };


            List<String> fuseFaceUrlList = ListUtil.toList(materialIdList).stream()
                    .map(materialId -> {
                        PicFuseFaceDTO faceDTO = new PicFuseFaceDTO();
                        faceDTO.setProjectId(template.getActivityId());
                        faceDTO.setModelId(materialId);
                        PicFuseFaceMergeInfoDTO mergeInfoDTO = new PicFuseFaceMergeInfoDTO();
                        mergeInfoDTO.setUrl(aiPicFuseFaceTaskCreateDTO.getUrl());
                        //默认取第一张脸
                        mergeInfoDTO.setTemplateFaceID(materialId + "_1");
                        faceDTO.setMergeInfos(ListUtil.toList(mergeInfoDTO));
                        return TencentCloudAIUtils.parseFuseFaceResponse(TencentCloudAIUtils.fuseFace(faceDTO, extraMap));
                    })
                    .map(txUrl -> {
                        //存储阿里云合成人脸图片
                        String filePath = "tx" + "/" + IdWorker.get32UUID() + ".jpg";
                        try {
                            return aliMediaService.putObjectRemoteUrl(filePath, txUrl);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).collect(Collectors.toList());


            // 7.获取图片数据构造clipsParam
            LinkedHashMap<String, String> clipsParamMap = JacksonUtils.readValue(template.getClipsParam(), LinkedHashMap.class);

            int index = 0;
            for (Map.Entry<String, String> entry : clipsParamMap.entrySet()) {
                if (index < fuseFaceUrlList.size()) {
                    entry.setValue(fuseFaceUrlList.get(index));
                    index++;
                } else {
                    break; // 如果 fuseFaceUrlList 的长度小于 clipsParamMap 的大小，停止匹配
                }
            }

            // 2.合成视频
            String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_TXAI_ACROSS_FACE, template.getTemplateId(), JacksonUtils.toJson(clipsParamMap));
            AiUnionRecord record = new AiUnionRecord();
            record.setId(aiPicFuseFaceTaskCreateDTO.getRecordId());
            record.setVideoPicUrl(fuseFaceUrlList.get(0));
            record.setConvertUrls(String.join(",", fuseFaceUrlList));
            record.setTaskId(jobId);
            record.setTemplateName(template.getTemplateName());
            record.setTemplateRemark(template.getTemplateRemark());
            aiUnionRecordService.updateById(record);
        } catch (Exception e) {
            aiUnionRecordService.update(new LambdaUpdateWrapper<AiUnionRecord>()
                    .set(AiUnionRecord::getVideoStatus, -1)
                    .set(AiUnionRecord::getFailReason, "换脸异常")
                    .eq(AiUnionRecord::getId, aiPicFuseFaceTaskCreateDTO.getRecordId()));
            log.error("{}-腾讯云ai穿越图片换脸消息处理异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getUserUniqueTag(), e);
            handleMiguTaskResult(aiPicFuseFaceTaskCreateDTO.getRecordId(), false, null);
        }
    }

    private String getRandomId(List<String> ids) {
        Random random = new Random();
        int randomIndex = random.nextInt(ids.size());
        return ids.get(randomIndex);
    }
}
