package com.eleven.cms.aiunion.service.impl;


import com.eleven.cms.aiunion.entity.TalkShowProfileRecord;
import com.eleven.cms.aiunion.mapper.ProfileRecordMapper;
import com.eleven.cms.aiunion.service.IProfileRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


@Service
@RequiredArgsConstructor
public class ProfileRecordServiceImpl extends ServiceImpl<ProfileRecordMapper, TalkShowProfileRecord> implements IProfileRecordService {

}
