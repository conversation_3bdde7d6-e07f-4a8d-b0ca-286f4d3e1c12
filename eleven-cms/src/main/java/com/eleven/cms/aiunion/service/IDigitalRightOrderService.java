package com.eleven.cms.aiunion.service;

import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aiunion.entity.DigitalRightOrder;
import com.eleven.cms.aiunion.pojo.dto.DigitalRightDTO;

/**
 * @Description: right_order
 * @Author: jeecg-boot
 * @Date:   2025-03-19
 * @Version: V1.0
 */
public interface IDigitalRightOrderService extends IService<DigitalRightOrder> {

    DigitalRightDTO.RechargeRes recharge(DigitalRightDTO.DigitalRightReq rightReq);
    DigitalRightOrder getByOrderNo(String orderNo);

    AlipaySystemOauthTokenResponse getAccessToken(String code, String type);
}
