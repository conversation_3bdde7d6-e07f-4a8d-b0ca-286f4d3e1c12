package com.eleven.cms.aiunion.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunion.entity.DigitalRightsPackages;
import com.eleven.cms.aiunion.pojo.dto.DigitalRightDTO;
import com.eleven.cms.aiunion.pojo.vo.DigitalRightsPackageVO;
import com.eleven.cms.aiunion.service.IDigitalRightOrderService;
import com.eleven.cms.aiunion.service.IDigitalRightsPackagesService;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.service.impl.pay.DigitalRightsNotifyServiceImpl;
import com.eleven.cms.service.pay.AlipayTradeWapPay;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/cms/rightOrder/")
@Slf4j
@Validated
@RequiredArgsConstructor
@CrossOrigin
public class digitalRightsController {


    @Resource
    private IDigitalRightOrderService rightOrderService;

    @Resource
    private IDigitalRightsPackagesService digitalRightsPackagesService;

    @Resource
    private AlipayTradeWapPay alipayTradeWapPay;

    @Resource
    private DigitalRightsNotifyServiceImpl digitalRightsNotifyService;


    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "权益订单充值")
    @PostMapping(value = "/recharge")
    public Result<?> recharge(@RequestBody @Validated DigitalRightDTO.DigitalRightReq rightReq) {
        log.info("充值入参:{}", JSONObject.toJSONString(rightReq));
        String rechargeRedisKey = "cms:aiunion:recharge:pay" + rightReq.getMobile();
        if (!redisUtil.setIfAbsent(rechargeRedisKey, rightReq.getMobile(), 10)) {
            throw new BusinessException("请勿重复支付");
        }
        DigitalRightDTO.RechargeRes payBody = rightOrderService.recharge(rightReq);
        redisUtil.del(rechargeRedisKey);
        return Result.ok(payBody);
    }

    @GetMapping(value = "/getAccessToken")
    public Result<?> getAccessToken(String code) {
        return Result.ok(rightOrderService.getAccessToken(code, "ZY_VRBT10"));
    }

    @ApiOperation(value = "鸿盛刷单支付")
    @PostMapping(value = "/createOrder")
    public Result<?> createOrder(@RequestBody @Validated DigitalRightDTO.DigitalRightReq rightReq, HttpServletRequest request) {
        if (StringUtils.isEmpty(rightReq.getCode())) {
            return Result.error("请先授权登录");
        }

        log.info("鸿盛刷单支付入参:{},getPublicIp:{},ua:{}", JSONObject.toJSONString(rightReq), IPUtils.getPublicIp(request), request.getHeader("user-agent"));
        String rechargeRedisKey = "cms:aiunion:createOrder:pay" + rightReq.getMobile();
        if (!redisUtil.setIfAbsent(rechargeRedisKey, rightReq.getMobile(), 10)) {
            return Result.error("请勿重复支付");

        }

        String rechargeMobileRedisKey = "cms:aiunion:createOrder:pay:mobile:" + rightReq.getMobile();
        if (ObjectUtil.isNotEmpty(redisUtil.get(rechargeMobileRedisKey))) {
            return Result.error("该手机号已领取过啦");
        }

        if (!StringUtils.isEmpty(rightReq.getClientId())) {
            String rechargeDeviceRedisKey = "cms:aiunion:createOrder:pay:antiHeavy:" + rightReq.getClientId();
            if (ObjectUtil.isNotEmpty(redisUtil.get(rechargeDeviceRedisKey))) {
                return Result.error("您已领取过啦");
            }
        } else {
            log.error("鸿盛刷单支付 手机号：{}，获取到的浏览器id是空", rightReq.getMobile());
        }

        rightReq.setPayType(2);
        rightReq.setPackageId("1");
        DigitalRightDTO.RechargeRes payBody = rightOrderService.recharge(rightReq);
        redisUtil.del(rechargeRedisKey);
        return Result.ok(payBody);
    }


    @ApiOperation(value = "处理支付成果后充值vip")
    @PostMapping(value = "/handleSuccess")
    public Result<?> queryResult(@RequestBody DigitalRightDTO.QueryPayReq rightReq) {
        int result = 0;
        try {
            AlipayTradeQueryResponse payResult = alipayTradeWapPay.queryPayResult(rightReq.getOrderNo(),rightReq.getTradeNo());
            String tradeStatus = payResult.getTradeStatus();
            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                result = digitalRightsNotifyService.paySuccess(rightReq.getOrderNo());
            }
        } catch (AlipayApiException e) {
            log.error("处理支付成果后充值vip失败 订单编码:{}",rightReq.getOrderNo(),e);
            throw new BusinessException("查询支付状态失败");
        }
        return Result.ok(result);
    }

    @ApiOperation(value = "产品包列表")
    @GetMapping(value = "product/list")
    public Result<?> listProduct(DigitalRightsPackages digitalRightsPackages,
                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                 HttpServletRequest req) {
        QueryWrapper<DigitalRightsPackages> queryWrapper = QueryGenerator.initQueryWrapper(digitalRightsPackages, req.getParameterMap());
        Page<DigitalRightsPackages> page = new Page<>(pageNo, pageSize);
        queryWrapper.setEntity(digitalRightsPackages);
        queryWrapper.eq("is_deleted", 0);
        queryWrapper.orderByDesc("order_no");
        IPage<DigitalRightsPackages> page1 = digitalRightsPackagesService.page(page, queryWrapper);
        List<DigitalRightsPackages> records = page1.getRecords();
        List<DigitalRightsPackageVO> resultList = new ArrayList<>();
        for (DigitalRightsPackages record : records) {
            DigitalRightsPackageVO vo = new DigitalRightsPackageVO();
            BeanUtils.copyProperties(record, vo);
            vo.setAmount(record.getProductPrice());
            resultList.add(vo);
        }
        Page<DigitalRightsPackageVO> resultPage = new Page<>();
        BeanUtils.copyProperties(page1, resultPage);
        resultPage.setRecords(resultList);
        return Result.ok(resultPage);
    }
}
