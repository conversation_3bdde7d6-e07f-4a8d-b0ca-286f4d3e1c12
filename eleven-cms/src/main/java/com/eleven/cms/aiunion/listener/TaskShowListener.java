package com.eleven.cms.aiunion.listener;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.aiunion.config.TaskShowRabbitmqConfig;
import com.eleven.cms.aiunion.pojo.dto.TalkShowDTO;
import com.eleven.cms.aiunion.service.AiTalkShowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskShowListener {

    private final AiTalkShowService aiTalkShowService;

    @RabbitListener(queues = TaskShowRabbitmqConfig.PROFILE_QUEUE)
    public void handProfileTask(TalkShowDTO.TaskSowMQDTO message) {
        try {
            aiTalkShowService.queryProfileTaskResult(message.getId());
        } catch (Exception e) {
            log.info("脱口秀处理消息失败 profileId {}", message.getId(),e);
        }
    }

    @RabbitListener(queues = TaskShowRabbitmqConfig.RECORD_QUEUE)
    public void handRecordTask(TalkShowDTO.TaskSowRecordMQDTO message) {
        try {
            aiTalkShowService.queryRecordTaskResult(message.getId(),Boolean.TRUE);
        } catch (Exception e) {
            log.info("脱口秀处理消息失败 recordId {}", message.getId(),e);
        }
    }

    @RabbitListener(queues = TaskShowRabbitmqConfig.CREATE_SHOW_QUEUE)
    public void handCreatShowTask(TalkShowDTO.CreateTaskSowMQDTO message) {
        try {
            aiTalkShowService.handleCreateShowTask(message);
        } catch (Exception e) {
            log.info("脱口秀处理消息失败 json {}", JSONObject.toJSONString(message),e);
        }
    }
}