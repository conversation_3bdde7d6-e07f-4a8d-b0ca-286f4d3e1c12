package com.eleven.cms.aiunion.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.eleven.cms.aiunion.config.MiguAIGCConfigProperties;
import com.eleven.cms.aiunion.pojo.dto.AiRightResponse;
import com.eleven.cms.aiunion.pojo.dto.ReportResultDTO;
import com.eleven.cms.aivrbt.utils.HttpUtils;
import com.eleven.cms.util.JacksonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.codec.digest.DigestUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;

import static com.eleven.cms.aivrbt.utils.HttpUtils.COMMON_CLIENT;

/**
 * <AUTHOR>
 * @datetime 2024/12/3 9:44
 * @see <a href="https://migumusic.feishu.cn/docx/QpBWdvUqko4oVWxO4bJcl0kmnmd">...</a>
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Data
public class MiguAIGCApiService {

    public static final MediaType JSON = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
    private static final String LOG_TAG = "咪咕ai";
    private final MiguAIGCConfigProperties miguAIGCConfigProperties;

    private String generateSignature(String appId, String appSecret, String token, String timestamp, String nonce) {
        StringJoiner sb = new StringJoiner(":");
        sb.add(appId);
        sb.add(appSecret);
        sb.add(token);
        sb.add(timestamp);
        sb.add(nonce);
        return DigestUtils.md5Hex(sb.toString());
    }

    /**
     * 用户AI权益判断
     *
     * @param token 咪咕的token
     * @return
     */
    public String checkAIRight(String token) {
        HttpUrl url = HttpUrl.parse(miguAIGCConfigProperties.getProtocol() + miguAIGCConfigProperties.getHostName() + miguAIGCConfigProperties.getAiRightsUri());
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appId", miguAIGCConfigProperties.getAppId());
        String json = JacksonUtils.toJson(paramMap);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = UUIDGenerator.generate();
        String signature = generateSignature(miguAIGCConfigProperties.getAppId(), miguAIGCConfigProperties.getAppSecret(), token, timestamp, nonce);
        Headers headers = new Headers.Builder()
                .add("appId", miguAIGCConfigProperties.getAppId())
                .add("x-mgmusic-osign", signature)
                .add("x-mgmusic-otoken", token)
                .add("timestamp", timestamp)
                .add("nonce", nonce)
                .build();
        log.info("请求参数:sign:{},timestamp:{},nonce:{}", signature, timestamp, nonce);
        return checkRespCode(HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "咪咕AI_权益查询"));
    }


    private String checkRespCode(String response) {
        if (StrUtil.isBlank(response)) {
            throw new JeecgBootException("请求咪咕失败,请重试");
        }
        JsonNode rootNode = JacksonUtils.readTree(response);
        //响应失败处理
        if (rootNode.has("code") && !rootNode.get("code").asText().equals("000000")) {
            if (rootNode.has("info")) {
                throw new JeecgBootException(rootNode.get("info").asText());
            }
        }
        return response;
    }

    public AiRightResponse parseAiRightResp(String response) {
        JsonNode rootNode = JacksonUtils.readTree(response);
        //响应失败处理
        if (rootNode.has("code") && !rootNode.get("code").asText().equals("000000")) {
            if (rootNode.has("info")) {
                throw new JeecgBootException(rootNode.get("info").asText());
            }
        }
        if (rootNode.has("data")) {
            JsonNode dataNode = rootNode.get("data");
            AiRightResponse airightResponse = new AiRightResponse();
            airightResponse.setStatus(dataNode.get("status").asInt());
            airightResponse.setRightsCount(dataNode.get("rightsCount").asLong());
            airightResponse.setExperienceCount(dataNode.get("experienceCount").asLong());
            return airightResponse;
        }
        throw new JeecgBootException("响应失败,请重试");
    }

    /**
     * AI能力使用次数预扣减
     *
     * @param token  咪咕的token
     * @param taskId 能力ID
     * @return 响应
     */
    public String preReduceCount(String token, String taskId) {
        HttpUrl url = HttpUrl.parse(miguAIGCConfigProperties.getProtocol() + miguAIGCConfigProperties.getHostName() + miguAIGCConfigProperties.getPreReduceCountUri());
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appId", miguAIGCConfigProperties.getAppId());
        paramMap.put("taskId", taskId);
        String json = JacksonUtils.toJson(paramMap);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = UUIDGenerator.generate();
        Headers headers = new Headers.Builder()
                .add("appId", miguAIGCConfigProperties.getAppId())
                .add("x-mgmusic-osign", generateSignature(miguAIGCConfigProperties.getAppId(), miguAIGCConfigProperties.getAppSecret(), token, timestamp, nonce))
                .add("x-mgmusic-otoken", token)
                .add("timestamp", timestamp)
                .add("nonce", nonce)
                .build();
        return checkRespCode(HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "咪咕AI_能力使用次数预扣减"));
    }

    /**
     * AI创作任务取消
     *
     * @param token  咪咕的token
     * @param taskId 能力ID
     * @return 响应
     */
    public String cancelTask(String token, String taskId) {
        HttpUrl url = HttpUrl.parse(miguAIGCConfigProperties.getProtocol() + miguAIGCConfigProperties.getHostName() + miguAIGCConfigProperties.getCancelTaskUri());
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appId", miguAIGCConfigProperties.getAppId());
        paramMap.put("taskId", taskId);
        String json = JacksonUtils.toJson(paramMap);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = UUIDGenerator.generate();
        Headers headers = new Headers.Builder()
                .add("appId", miguAIGCConfigProperties.getAppId())
                .add("x-mgmusic-osign", generateSignature(miguAIGCConfigProperties.getAppId(), miguAIGCConfigProperties.getAppSecret(), token, timestamp, nonce))
                .add("x-mgmusic-otoken", token)
                .add("timestamp", timestamp)
                .add("nonce", nonce)
                .build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "咪咕AI_AI创作任务取消");
    }

    /**
     * AI创作结果上报
     *
     * @param token           咪咕的token
     * @param reportResultDTO 上报结果
     * @return 响应
     */
    public String reportResult(String token, String taskId, ReportResultDTO reportResultDTO) {
        HttpUrl url = HttpUrl.parse(miguAIGCConfigProperties.getProtocol() + miguAIGCConfigProperties.getHostName() + miguAIGCConfigProperties.getReportResultUri());


        Map<String, Object> paramMap = BeanUtil.beanToMap(reportResultDTO, false, true);
        paramMap.put("appId", miguAIGCConfigProperties.getAppId());
        paramMap.put("taskId", taskId);

        String json = JacksonUtils.toJson(paramMap);
        RequestBody body = RequestBody.create(JSON, json);

        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = UUIDGenerator.generate();
        Headers headers = new Headers.Builder()
                .add("appId", miguAIGCConfigProperties.getAppId())
                .add("x-mgmusic-osign", generateSignature(miguAIGCConfigProperties.getAppId(), miguAIGCConfigProperties.getAppSecret(), token, timestamp, nonce))
                .add("x-mgmusic-otoken", token)
                .add("timestamp", timestamp)
                .add("nonce", nonce)
                .build();
        return HttpUtils.post(COMMON_CLIENT, url, body, headers, LOG_TAG, "咪咕AI_AI创作结果上报");
    }
}
