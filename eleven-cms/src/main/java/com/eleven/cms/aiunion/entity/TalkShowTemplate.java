package com.eleven.cms.aiunion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: talk_show_template
 * @Author: jeecg-boot
 * @Date: 2025-03-12
 * @Version: V1.0
 */
@Data
@TableName("talk_show_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "talk_show_template对象", description = "talk_show_template")
public class TalkShowTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 类别: 1:精选  2:宠物
     */
    @Excel(name = "类别: 1:精选  2:宠物", width = 15)
    @ApiModelProperty(value = "类别: 1:精选  2:宠物")
    private Integer type;
    /**
     * 模式: 1:普通模式  2:灵动
     */
    @Excel(name = "模式: 1:普通模式  2:灵动", width = 15)
    @ApiModelProperty(value = "模式: 1:普通模式  2:灵动")
    private Integer model;
    /**
     * 模板图片资源链接
     */
    @Excel(name = "模板图片资源链接", width = 15)
    @ApiModelProperty(value = "模板图片资源链接")
    private String picLink;

    private String mergeTemplateId;

    /**
     * 模板音频资源链接
     */
    @Excel(name = "模板音频资源链接", width = 15)
    @ApiModelProperty(value = "模板音频资源链接")
    private String audioLink;
    /**
     * 模板视频链接
     */
    @Excel(name = "模板视频链接", width = 15)
    @ApiModelProperty(value = "模板视频链接")
    private String videoLink;
    /**
     * 0禁用 1启用
     */
    @Excel(name = "0禁用 1启用", width = 15)
    @ApiModelProperty(value = "0禁用 1启用")
    private Integer statue;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
