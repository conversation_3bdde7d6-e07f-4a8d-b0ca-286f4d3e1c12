package com.eleven.cms.aiunion.service;

import com.eleven.cms.aiunion.entity.TalkShowRecord;
import com.eleven.cms.aiunion.pojo.dto.TalkShowDTO;

public interface AiTalkShowService {

    TalkShowDTO.RecordRes createTalkShow(TalkShowDTO talkShowDTO);

    TalkShowDTO.ProfileRes createTalkShowProfile(TalkShowDTO talkShowDTO);

    /**
     *
     * @param profileId 形象记录主键
     */

    void queryProfileTaskResult(String profileId);


    /**
     * @param recordId            制作记录主键
     * @param mergeTemplateStatus
     * @return 制作记录
     */

    TalkShowRecord queryRecordTaskResult(String recordId);

    /**
     * 处理异步制作视频
     * @param createTaskSowMQDTO
     */

    void handleCreateShowTask(TalkShowDTO.CreateTaskSowMQDTO createTaskSowMQDTO);

    TalkShowRecord queryRecordTaskResult(String recordId,Boolean isSendMQ);

    /**
     * 处理mns队列
     * @param jobId
     * @param videoUrl
     */
    void handleJob(String jobId,String videoUrl);
}