package com.eleven.cms.aiunion.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aiunion.component.RabbitMQSender;
import com.eleven.cms.aiunion.config.TaskShowRabbitmqConfig;
import com.eleven.cms.aiunion.entity.TalkShowProfileRecord;
import com.eleven.cms.aiunion.entity.TalkShowRecord;
import com.eleven.cms.aiunion.entity.TalkShowTemplate;
import com.eleven.cms.aiunion.enums.AliTalkShowActionEnum;
import com.eleven.cms.aiunion.enums.AliTalkShowModelEnum;
import com.eleven.cms.aiunion.enums.AliTalkShowResponCodeEnum;
import com.eleven.cms.aiunion.pojo.dto.TalkShowDTO;
import com.eleven.cms.aiunion.service.AiTalkShowService;
import com.eleven.cms.aiunion.service.IProfileRecordService;
import com.eleven.cms.aiunion.service.ITalkShowRecordService;
import com.eleven.cms.aiunion.service.ITalkShowTemplateService;
import com.eleven.cms.aiunion.utils.AliTaskShowSign;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiTalkShowServiceImpl implements AiTalkShowService {

    private final AliTaskShowSign aliTaskShowSign;

    private final ITalkShowTemplateService talkShowTemplateService;

    private final ITalkShowRecordService talkShowRecordService;

    private final IProfileRecordService profileRecordService;

    private final AliMediaService aliMediaService;

    private final RabbitMQSender rabbitMQSender;

    private final   RedisUtil redisUtil;
    private final String TALK_SHOWS__KEY_QUERY = "cms:aiunion:talkShow:query:stopTime";
    private final String TALK_SHOWS_KEY_CREATE_PROFILE = "cms:aiunion:talkShow:createProfile:stopTime";
    private final String TALK_SHOWS_KEY_CREATE_SHOW = "cms:aiunion:talkShow:createTalkShow:stopTime";

    @Override
    @Transactional
    public TalkShowDTO.RecordRes createTalkShow(TalkShowDTO talkShowDTO) {
        //获取模板
        TalkShowTemplate talkShowTemplate = talkShowTemplateService.getById(talkShowDTO.getTempId());

        TalkShowProfileRecord profileRecord = profileRecordService.getById(talkShowDTO.getProfileId());
        if (profileRecord.getProfileStatus() == -1) {
            throw new BusinessException("形象错误,请删除再创建");
        }
        String audioLink = StringUtils.isEmpty(talkShowDTO.getAudioLink()) ? talkShowTemplate.getAudioLink() : talkShowDTO.getAudioLink();
        //构建制作记录
        TalkShowRecord talkShowRecord = TalkShowRecord.builder()
                .model(talkShowTemplate.getModel())
                .videoStatus(0)
                .coverLink(talkShowDTO.getPicUrl())
                .createBy(talkShowDTO.getUserId())
                .userId(talkShowDTO.getUserId())
                .tempId(talkShowDTO.getTempId())
                .mobile(talkShowDTO.getMobile())
                .audioLink(audioLink)
                .profileId(talkShowDTO.getProfileId())
                .aiRingStatus(talkShowDTO.getAiRingStatus())
                .build();

        //保存制作记录
        talkShowRecordService.save(talkShowRecord);

        //发送mq  异步调用制作接口
        TalkShowDTO.CreateTaskSowMQDTO mqdto = TalkShowDTO.CreateTaskSowMQDTO.builder()
                .tempId(talkShowDTO.getTempId())
                .recordId(talkShowRecord.getId())
                .profileId(profileRecord.getId())
                .build();
        rabbitMQSender.sendWithDelay(TaskShowRabbitmqConfig.CREATE_SHOW_EXCHANGE_DELAY, TaskShowRabbitmqConfig.CREATE_SHOW_ROUT_KEY_DELAY, mqdto);

        return TalkShowDTO.RecordRes.builder().id(talkShowRecord.getId()).statue("success").build();
    }

    @Override
    public void handleCreateShowTask(TalkShowDTO.CreateTaskSowMQDTO createTaskSowMQDTO) {
        queryProfileTaskResult(createTaskSowMQDTO.getProfileId());
        TalkShowTemplate talkShowTemplate = talkShowTemplateService.getById(createTaskSowMQDTO.getTempId());
        TalkShowProfileRecord profileRecord = profileRecordService.getById(createTaskSowMQDTO.getProfileId());
        TalkShowRecord talkShowRecord = talkShowRecordService.getById(createTaskSowMQDTO.getRecordId());
        if (Objects.isNull(profileRecord)) {
            return;
        }
        if (Objects.equals(profileRecord.getProfileStatus(), -1)) {
            talkShowRecord.setVideoStatus(-1);
            talkShowRecord.setErrMsg("制作形象失败");
            talkShowRecordService.updateById(talkShowRecord);
            sendDeductMQ(talkShowRecord,TaskMakingEventDTO.fail);
            return;
        }
        if (StringUtils.isEmpty(profileRecord.getProfileId())) {
            rabbitMQSender.sendWithDelay(TaskShowRabbitmqConfig.CREATE_SHOW_EXCHANGE_DELAY,
                    TaskShowRabbitmqConfig.CREATE_SHOW_ROUT_KEY_DELAY, createTaskSowMQDTO);
            return;
        }
        doCreatShow(talkShowTemplate, talkShowRecord, profileRecord.getProfileId());
    }

    private void doCreatShow(TalkShowTemplate talkShowTemplate, TalkShowRecord talkShowRecord, String profileId) {
        JSONObject generateParam = new JSONObject();
        generateParam.put("req_key", AliTalkShowModelEnum.getEnumByCode(talkShowTemplate.getModel()).getVideoModel());
        generateParam.put("audio_url", talkShowRecord.getAudioLink());
        generateParam.put("resource_id", profileId);

        SpringContextUtils.getBean("talkShowTaskExecutor",ThreadPoolExecutor.class).execute(() -> {
            try {
                Integer talkShowsTopKey = (Integer) redisUtil.get(TALK_SHOWS_KEY_CREATE_SHOW);
                if(Objects.nonNull(talkShowsTopKey)){
                    Thread.sleep(talkShowsTopKey);
                }
                log.info("脱口秀异步任务 doCreatShow  talkShowRecord:{}",JSONObject.toJSONString(talkShowRecord));
                AliTaskShowSign.Result videoResult = null;
                videoResult = aliTaskShowSign.doPostRequest(generateParam.toString(), AliTalkShowActionEnum.SUBMIT_TASK);
                if (Objects.equals(String.valueOf(videoResult.getCode()), AliTalkShowResponCodeEnum.SUCCESS.getCode())) {
                    talkShowRecord.setVideoTaskId(videoResult.getData().getTaskId());
                    talkShowRecordService.updateById(talkShowRecord);
                } else {
                    sendDeductMQ(talkShowRecord,TaskMakingEventDTO.fail);
                    log.info("调用制作接口错误,返回结果{}", JSONObject.toJSONString(videoResult));
                    talkShowRecord.setVideoStatus(-1);
                    talkShowRecord.setErrMsg(videoResult.getMessage());
                    talkShowRecordService.updateById(talkShowRecord);
                }
            } catch (Exception e) {
                sendDeductMQ(talkShowRecord,TaskMakingEventDTO.fail);
                log.info("制作视频错误:{}", JSONObject.toJSONString(talkShowRecord), e);
            }
        });
        rabbitMQSender.sendWithDelay(TaskShowRabbitmqConfig.RECORD_EXCHANGE_DELAY,
                TaskShowRabbitmqConfig.RECORD_ROUT_KEY_DELAY,
                TalkShowDTO.TaskSowRecordMQDTO.builder().id(talkShowRecord.getId()).build());
    }

    @Override
    public void handleJob(String jobId, String videoUrl) {
        QueryWrapper<TalkShowRecord> query = new QueryWrapper<>();
        query.eq("merge_job_id", jobId);
        List<TalkShowRecord> list = talkShowRecordService.getBaseMapper().selectList(query);
        for (TalkShowRecord record : list) {
            String snapHotUrl = String.format("%s%s", videoUrl, "?x-oss-process=video/snapshot,t_1,f_jpg,w_672,h_1024,m_fast");
            String filePath = "talkShowCover" + "/" + IdWorker.get32UUID() + ".jpg";
            String coverLink = null;
            try {
                coverLink = aliMediaService.putObjectRemoteUrlCdn(filePath, snapHotUrl);
            } catch (Exception e) {
                log.error("上传图片错误:{}", JSONObject.toJSONString(record), e);
            }
            record.setCoverLink(coverLink);
            record.setVideoStatus(1);
            record.setVideoLink(videoUrl);
            talkShowRecordService.updateById(record);
            sendDeductMQ(record,TaskMakingEventDTO.success);
        }
    }

    @Override
    public void queryProfileTaskResult(String profileId) {
        TalkShowProfileRecord profileRecord = profileRecordService.getById(profileId);
        if (Objects.isNull(profileRecord)) {
            return;
        }
        if (!Objects.equals(profileRecord.getProfileStatus(), 0)) {
            return;
        }
        if (StringUtils.isEmpty(profileRecord.getTaskId())) {
            return;
        }
        JSONObject generateParam = new JSONObject();
        generateParam.put("req_key", AliTalkShowModelEnum.getEnumByCode(profileRecord.getModel()).getPicModel());
        generateParam.put("task_id", profileRecord.getTaskId());
        try {
            AliTaskShowSign.Result videoResult = aliTaskShowSign.doPostRequest(generateParam.toString(), AliTalkShowActionEnum.GET_RESULT);
            log.info("查询任务结果:{}", JSONObject.toJSONString(videoResult));
            if (Objects.equals(String.valueOf(videoResult.getCode()), AliTalkShowResponCodeEnum.SUCCESS.getCode())) {
                if (Objects.equals(videoResult.getData().getStatus(), "generating")) {
                    return;
                }
                String respData = videoResult.getData().getRespData();
                JSONObject jsonObject = JSONObject.parseObject(respData);
                Object resourceId = jsonObject.get("resource_id");
                profileRecord.setProfileId(String.valueOf(resourceId));
                profileRecord.setProfileStatus(1);
            } else {
                profileRecord.setProfileStatus(-1);
                profileRecord.setErrMsg(videoResult.getBody());
            }
        } catch (Exception e) {
            profileRecord.setProfileStatus(-1);
            log.info("生成形象失败 profileId:{}", profileId, e);
        }
        profileRecordService.updateById(profileRecord);
    }

    @Override
    public TalkShowRecord queryRecordTaskResult(String recordId, Boolean isSendMQ) {
        TalkShowRecord talkShowRecord = talkShowRecordService.getById(recordId);
        if (Objects.isNull(talkShowRecord)) {
            return null;
        }

        //状态不是生成中直接返回结果
        if (talkShowRecord.getVideoStatus() != 0 ) {
            return talkShowRecord;
        }

        //没有视频任务id直接返回
        if (StringUtils.isEmpty(talkShowRecord.getVideoTaskId())) {
            if (isSendMQ) {
                TalkShowDTO.TaskSowRecordMQDTO mqMessage = TalkShowDTO.TaskSowRecordMQDTO.builder().id(recordId).build();
                rabbitMQSender.sendWithDelay(TaskShowRabbitmqConfig.RECORD_EXCHANGE_DELAY, TaskShowRabbitmqConfig.RECORD_ROUT_KEY_DELAY, mqMessage);
            } else {
                return talkShowRecord;
            }
        }

        if (Objects.equals(talkShowRecord.getVideoStatus(), 0) && StringUtils.isEmpty(talkShowRecord.getMergeJobId())) {
            JSONObject generateParam = new JSONObject();
            generateParam.put("req_key", AliTalkShowModelEnum.getEnumByCode(talkShowRecord.getModel()).getVideoModel());
            generateParam.put("task_id", talkShowRecord.getVideoTaskId());
            try {
                AliTaskShowSign.Result videoResult = aliTaskShowSign.doPostRequest(generateParam.toString(), AliTalkShowActionEnum.GET_RESULT);
                log.info("查询制作视频结果返回:{}", JSONObject.toJSONString(videoResult));
                if (Objects.equals(String.valueOf(videoResult.getCode()), AliTalkShowResponCodeEnum.SUCCESS.getCode())) {
                    if (Objects.equals(videoResult.getData().getStatus(), "generating") && isSendMQ) {
                        TalkShowDTO.TaskSowRecordMQDTO mqMessage = TalkShowDTO.TaskSowRecordMQDTO.builder().id(recordId).build();
                        rabbitMQSender.sendWithDelay(TaskShowRabbitmqConfig.RECORD_EXCHANGE_DELAY, TaskShowRabbitmqConfig.RECORD_ROUT_KEY_DELAY, mqMessage);
                    }
                    if (!Objects.equals(videoResult.getData().getStatus(), "done")) {
                        return talkShowRecord;
                    }
                    String respData = videoResult.getData().getRespData();
                    JSONObject jsonObject = JSONObject.parseObject(respData);
                    String resourceId = jsonObject.getJSONArray("preview_url").get(0).toString();
                    TalkShowTemplate talkShowTemplate = talkShowTemplateService.getById(talkShowRecord.getTempId());
                    if (StrUtil.isNotBlank(resourceId)) {
                        String filePath = "talkShow" + "/" + IdWorker.get32UUID() + ".mp4";
                        String aliVideoUrl = aliMediaService.putObjectRemoteUrl(filePath, resourceId);
                        if (StringUtils.isEmpty(talkShowTemplate.getMergeTemplateId())) {
                            talkShowRecord.setVideoStatus(1);
                            talkShowRecord.setVideoLink(aliVideoUrl);
                            sendDeductMQ(talkShowRecord,TaskMakingEventDTO.success);
                        } else {
                            Map<String, String> clipsParamMap = new LinkedHashMap<>();
                            clipsParamMap.put("media", aliVideoUrl);
                            String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_TALK_SHOW, talkShowTemplate.getMergeTemplateId(), JacksonUtils.toJson(clipsParamMap));
                            talkShowRecord.setMergeJobId(jobId);
                        }
                    }
                } else {
                    talkShowRecord.setErrMsg(videoResult.getBody());
                    talkShowRecord.setVideoStatus(-1);
                    sendDeductMQ(talkShowRecord, TaskMakingEventDTO.fail);
                }
                talkShowRecordService.updateById(talkShowRecord);
            } catch (Exception e) {
                talkShowRecord.setVideoStatus(-1);
                talkShowRecordService.updateById(talkShowRecord);
                log.info("生成视频失败 recordId{}", recordId, e);
                sendDeductMQ(talkShowRecord, TaskMakingEventDTO.fail);
            }
        }
        return talkShowRecord;
    }
    @Override
    public TalkShowRecord queryRecordTaskResult(String recordId) {
        return queryRecordTaskResult(recordId, false);
    }

    @Override
    public TalkShowDTO.ProfileRes createTalkShowProfile(TalkShowDTO talkShowDTO) {
        //获取模板
        TalkShowTemplate talkShowTemplate = talkShowTemplateService.getById(talkShowDTO.getTempId());
        JSONObject param = new JSONObject();
        param.put("req_key", AliTalkShowModelEnum.getEnumByCode(talkShowTemplate.getModel()).getPicModel());
        param.put("image_url", talkShowDTO.getPicUrl());
        String profileId = talkShowDTO.getProfileId();
        //保存形象记录
        TalkShowProfileRecord talkShowProfileRecord = TalkShowProfileRecord.builder()
                .tempId(talkShowDTO.getTempId())
                .userId(talkShowDTO.getUserId())
                .createBy(talkShowDTO.getMobile())
                .mobile(talkShowDTO.getMobile())
                .picUrl(talkShowDTO.getPicUrl())
                .build();
        profileRecordService.save(talkShowProfileRecord);
        TalkShowDTO.ProfileRes profileRes = TalkShowDTO.ProfileRes.builder().profileId(talkShowProfileRecord.getId()).build();
        profileRes.setStatue("success");
        SpringContextUtils.getBean("talkShowTaskExecutor",ThreadPoolExecutor.class).execute(() -> {

            if (Objects.isNull(profileId)) {
                try {
                    Integer stopTime = (Integer) redisUtil.get(TALK_SHOWS_KEY_CREATE_PROFILE);
                    if(Objects.nonNull(stopTime)){
                        Thread.sleep(stopTime);
                    }
                    log.info("脱口秀异步任务 createTalkShowProfile");
                    AliTaskShowSign.Result result  = aliTaskShowSign.doPostRequest(param.toString(), AliTalkShowActionEnum.SUBMIT_TASK);
                    if(Objects.equals(String.valueOf(result.getCode()), AliTalkShowResponCodeEnum.SUCCESS.getCode())) {
                        talkShowProfileRecord.setTaskId(result.getData().getTaskId());
                        //发送mq  获取形象id
                        TalkShowDTO.TaskSowMQDTO mqMessage = TalkShowDTO.TaskSowMQDTO.builder().id(talkShowProfileRecord.getId()).build();
                        rabbitMQSender.sendWithDelay(TaskShowRabbitmqConfig.PROFILE_EXCHANGE_DELAY, TaskShowRabbitmqConfig.PROFILE_ROUT_KEY_DELAY, mqMessage);
                    }else{
                        talkShowProfileRecord.setProfileStatus(-1);
                        talkShowProfileRecord.setErrMsg(result.getMessage());
                        profileRes.setStatue("fail");
                    }
                    profileRecordService.updateById(talkShowProfileRecord);
                } catch (Exception e) {
                    log.info("脱口秀异步任务异常错误{}",JSONObject.toJSONString(talkShowDTO),e);
                    profileRes.setStatue("fail");
                }
            }
        });
        return profileRes;
    }



    private void sendDeductMQ(TalkShowRecord record,String event) {
        TaskMakingEventDTO build = TaskMakingEventDTO.builder()
                .uid(record.getUserId())
                .talkRecordId(record.getId())
                .status(event)
                .build();
        rabbitMQSender.sendWithQueue("app.talk.show.event.queue", build);
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static  class TaskMakingEventDTO {

        private String uid;

        private String talkRecordId;


        private String status;

        public static final String success="SUCCESS";
        public static final String fail="FAIL";
    }
}
