package com.eleven.cms.aiunion.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunion.entity.AiUnionRecord;
import com.eleven.cms.aiunion.service.IAiUnionRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: ai_union_record
 * @Author: jeecg-boot
 * @Date:   2024-11-20
 * @Version: V1.0
 */
@Api(tags="ai_union_record")
@RestController
@RequestMapping("/cms_aiunion/aiUnionRecord")
@Slf4j
public class AiUnionRecordController extends JeecgController<AiUnionRecord, IAiUnionRecordService> {
	@Autowired
	private IAiUnionRecordService aiUnionRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aiUnionRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai_union_record-分页列表查询")
	@ApiOperation(value="ai_union_record-分页列表查询", notes="ai_union_record-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AiUnionRecord aiUnionRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AiUnionRecord> queryWrapper = QueryGenerator.initQueryWrapper(aiUnionRecord, req.getParameterMap());
		Page<AiUnionRecord> page = new Page<AiUnionRecord>(pageNo, pageSize);
		IPage<AiUnionRecord> pageList = aiUnionRecordService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aiUnionRecord
	 * @return
	 */
	@AutoLog(value = "ai_union_record-添加")
	@ApiOperation(value="ai_union_record-添加", notes="ai_union_record-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AiUnionRecord aiUnionRecord) {
		aiUnionRecordService.save(aiUnionRecord);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aiUnionRecord
	 * @return
	 */
	@AutoLog(value = "ai_union_record-编辑")
	@ApiOperation(value="ai_union_record-编辑", notes="ai_union_record-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AiUnionRecord aiUnionRecord) {
		aiUnionRecordService.updateById(aiUnionRecord);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_union_record-通过id删除")
	@ApiOperation(value="ai_union_record-通过id删除", notes="ai_union_record-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aiUnionRecordService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai_union_record-批量删除")
	@ApiOperation(value="ai_union_record-批量删除", notes="ai_union_record-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aiUnionRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai_union_record-通过id查询")
	@ApiOperation(value="ai_union_record-通过id查询", notes="ai_union_record-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AiUnionRecord aiUnionRecord = aiUnionRecordService.getById(id);
		if(aiUnionRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(aiUnionRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aiUnionRecord
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AiUnionRecord aiUnionRecord) {
        return super.exportXls(request, aiUnionRecord, AiUnionRecord.class, "ai_union_record");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AiUnionRecord.class);
    }

}
