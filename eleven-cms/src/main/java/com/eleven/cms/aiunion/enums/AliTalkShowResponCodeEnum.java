package com.eleven.cms.aiunion.enums;

import lombok.Getter;

@Getter
public enum AliTalkShowResponCodeEnum {

    SUCCESS("10000","成功"),
    ECAuth("50400","权限校验失败"),
    ECReqInvalidArgs("50200","参数错误"),
    ECReqMissingArgs("50201","缺少参数"),
    ECParseArgs("50204","参数类型错误/参数缺失"),
    ECImageSizeLimited("50205","图像尺寸超过限制"),
    ECVideoSizeLimited("50211","视频尺寸超过限制"),
    ECRPCProcess("50215","图片、视频、参数等不满足要求"),
    ECJPFaceDetect("60102","算法服务需要输入人脸图，但未检测到"),
    ECFSLeaderRiskError("60208","输入图片中包含敏感信息，未通过审核"),
    ECReqLimit("50429","超过调用QPS限制"),
    DEFAULT("-1","调用接口错误"),
    ;


    private final String code;
    private final String desc;

    AliTalkShowResponCodeEnum(String code, String desc) {
       this.code = code;
       this.desc = desc;
   }

   public static AliTalkShowResponCodeEnum getByCode(String code) {
        AliTalkShowResponCodeEnum[] values = AliTalkShowResponCodeEnum.values();
        for (AliTalkShowResponCodeEnum value : values) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return DEFAULT;
   }

}
