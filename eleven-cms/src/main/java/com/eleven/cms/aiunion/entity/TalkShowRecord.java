package com.eleven.cms.aiunion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
@TableName("talk_show_record")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "talk_show_record对象", description = "talk_show_record")
public class TalkShowRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 模板id
     */
    @Excel(name = "模板id", width = 15)
    @ApiModelProperty(value = "模板id")
    private String tempId;
    /**
     * 形象id
     */
    @Excel(name = "形象id", width = 15)
    @ApiModelProperty(value = "形象id")
    private String profileId;


    @Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "生成视频任务id")
    private String videoTaskId;

    private String mergeJobId;

    private String coverLink;

    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 创建人id
     */
    @Excel(name = "创建人id", width = 15)
    @ApiModelProperty(value = "创建人id")
    private String userId;
    /**
     * 模式: 1:普通模式  2:灵动
     */
    @Excel(name = "模式: 1:普通模式  2:灵动", width = 15)
    @ApiModelProperty(value = "模式: 1:普通模式  2:灵动")
    private Integer model;

    /**
     * 生成的视频链接
     */
    @Excel(name = "生成的视频链接", width = 15)
    @ApiModelProperty(value = "生成的视频链接")
    private String videoLink;

    /**
     * 音频
     */
    @Excel(name = "音频", width = 15)
    @ApiModelProperty(value = "音频")
    private String audioLink;


    @ApiModelProperty(value = "任务失败原因")
    private String errMsg;
    /**
     * -1:生成失败 0:生成中 1:生成成功
     */
    @Excel(name = "-1:生成失败 0:生成中 1:生成成功 ", width = 15)
    @ApiModelProperty(value = "-1:生成失败 0:生成中 1:生成成功 ")
    private Integer videoStatus;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

    @Excel(name = "审核表ID", width = 15)
    @ApiModelProperty(value = "审核表ID")
    private String vrbtDiyVideoId;

    /**
     * 是否是app_ai_ring 表的关联任务
     */
    private Integer aiRingStatus;
}
