package com.eleven.cms.aiunion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunion.entity.PayOrders;
import com.eleven.cms.aiunion.enums.PayOrderPayTypeEnum;
import com.eleven.cms.aiunion.mapper.PayOrdersMapper;
import com.eleven.cms.aiunion.service.IPayOrdersService;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: pay_orders
 * @Author: jeecg-boot
 * @Date: 2025-03-19
 * @Version: V1.0
 */
@Service
public class PayOrdersServiceImpl extends ServiceImpl<PayOrdersMapper, PayOrders> implements IPayOrdersService {


    @Override
    public PayOrders getPayOrdersByOutTradeNo(String outTradeNo) {
        if (StringUtils.isEmpty(outTradeNo)) {
            return null;
        }
        LambdaQueryWrapper<PayOrders> queryWrapper = Wrappers.<PayOrders>query().lambda()
                .eq(PayOrders::getOutTradeNo, outTradeNo);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PayOrders getPayOrdersByOrderNo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return null;
        }
        LambdaQueryWrapper<PayOrders> queryWrapper = Wrappers.<PayOrders>query().lambda()
                .eq(PayOrders::getOrderNo, orderNo);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PayOrders pay(String outTradeNo) {
        return null;
    }

    @Override
    public void updatePayStatus(String orderNo, PayStatueEnum payStatus, Date payTime) {

        PayOrders payOrders = this.getPayOrdersByOrderNo(orderNo);
        if (payOrders == null) {
            throw new BusinessException("订单不存在");
        }
        PayOrders update = new PayOrders();
        update.setId(payOrders.getId());
        update.setPayStatus(payStatus.getPayType());
        update.setPayTime(payTime);
        this.updateById(update);
    }

    @Override
    public void createOrder(String orderNo, PayBusineesTypeEnum businessType, BigDecimal amount, PayOrderPayTypeEnum payType) {
        PayOrders payOrders = new PayOrders();
        payOrders.setOrderNo(orderNo);
        payOrders.setBusinessType(businessType.getType());
        payOrders.setAmount(amount);
        payOrders.setPaymentType(payType.getCode());
        save(payOrders);
    }

}
