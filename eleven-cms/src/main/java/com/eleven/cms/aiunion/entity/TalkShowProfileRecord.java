package com.eleven.cms.aiunion.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;


@Data
@TableName("talk_profile_record")
@Accessors(chain = true)
@Builder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "talk_profile_record对象", description = "talk_profile_record")
public class TalkShowProfileRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 形象id
     */
    @Excel(name = "形象id", width = 15)
    @ApiModelProperty(value = "形象id")
    private String profileId;


    private String taskId;

    @ApiModelProperty(value = "形象生成状态 -1:创建失败 0:正在创建中 1:已创建")
    private Integer profileStatus;

    @ApiModelProperty(value = "创建失败原因")
    private String errMsg;
    /**
     * 模板id
     */
    @Excel(name = "模板id", width = 15)
    @ApiModelProperty(value = "模板id")
    private String tempId;

    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    @Excel(name = "图片链接", width = 15)
    @ApiModelProperty(value = "图片链接")
    private String picUrl;

    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;

    /**
     * 模式: 1:普通模式  2:灵动
     */
    @Excel(name = "模式: 1:普通模式  2:灵动", width = 15)
    @ApiModelProperty(value = "模式: 1:普通模式  2:灵动")
    private Integer model;
    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
