package com.eleven.cms.aiunion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@TableName("digital_right_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "digital_right_order对象", description = "digital_right_order")
public class DigitalRightOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String rightOrderNo;

    /**
     * 产品包id
     */
    private String packageId;

    @Excel(name = "支付类型 1(微信h5) 2(支付宝h5)", width = 15)
    @ApiModelProperty(value = "支付类型 1(微信h5) 2(支付宝h5)")
    private Integer payType;


    private BigDecimal amount;

    /**
     * 支付流水号
     */
    private String payTradeNo;


    /**
     * 支付订单表主键id
     */
    @Excel(name = "支付订单表主键id", width = 15)
    @ApiModelProperty(value = "支付订单表主键id")
    private String payOrdersId;
    /**
     * 退款订单表主键id
     */
    @Excel(name = "退款订单表主键id", width = 15)
    @ApiModelProperty(value = "退款订单表主键id")
    private String payRefundOrdersId;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    /**
     * 充值状态：-1(充值失败),0(充值中),1(已充值)
     */
    @Excel(name = "充值状态：-1(充值失败),0(充值中),1(已充值)", width = 15)
    @ApiModelProperty(value = "充值状态：-1(充值失败),0(充值中),1(已充值)")
    private String rechargeStatus;
    /**
     * -1(支付失败),0(未支付),1(已支付),2(已退款)
     */
    @Excel(name = "-1(支付失败),0(未支付),1(已支付),2(已退款)", width = 15)
    @ApiModelProperty(value = "-1(支付失败),0(未支付),1(已支付),2(已退款)")
    private Integer payStatus;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    private String userId;
    private String openId;
    private String clientId;

    private String rechargeResult;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
