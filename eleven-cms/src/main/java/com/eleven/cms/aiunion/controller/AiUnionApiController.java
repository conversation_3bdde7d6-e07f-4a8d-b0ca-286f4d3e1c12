package com.eleven.cms.aiunion.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunion.constants.AIFaceSwappingConstants;
import com.eleven.cms.aiunion.entity.AiUnionRecord;
import com.eleven.cms.aiunion.pojo.dto.AiPicAiUnionFuseFaceTaskCreateDTO;
import com.eleven.cms.aiunion.pojo.dto.AiRightResponse;
import com.eleven.cms.aiunion.pojo.dto.DisplayConfigDto;
import com.eleven.cms.aiunion.pojo.dto.UnionVideoFaceFusionDTO;
import com.eleven.cms.aiunion.service.IAiUnionAiFaceTemplateService;
import com.eleven.cms.aiunion.service.IAiUnionRecordService;
import com.eleven.cms.aiunion.service.IAiUnionService;
import com.eleven.cms.aiunion.service.MiguAIGCApiService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.eleven.cms.aiunion.service.impl.AiUnionServiceImpl.AI_TAG;

/**
 * <AUTHOR>
 * @datetime 2024/11/20 17:20
 */
@RestController
@RequestMapping("/cms/aiunion")
@Slf4j
@Validated
@RequiredArgsConstructor
public class AiUnionApiController {

    private final IAiUnionRecordService aiUinionRecordService;

    private final IAiUnionAiFaceTemplateService aiFaceTemplateService;

    private final IAiUnionService aiUnionService;

    private final RedisUtil redisUtil;

    private final MiguAIGCApiService miguAIGCApiService;

    private static final String XC_LOTTERY_KEY = "xc:lottery:";

    @ApiOperation(value = "aiunion_record-分页列表查询", notes = "cms_aiunion_record-分页列表查询")
    @GetMapping(value = "/record/list")
    public Result<?> queryPageList(AiUnionRecord aiUnionRecord,
                                   @RequestParam(value = "acToken") String acToken,
                                   @RequestParam(value = "userUniqueTag") String userUniqueTag,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        String userUniqueTagToken = (String) redisUtil.get("cms:cache:aiUnion:acToken:" + acToken);
        if (StrUtil.isBlank(userUniqueTag)) {
            return Result.error("请重新登录！");
        }
        if (!userUniqueTag.equals(userUniqueTagToken)) {
            return Result.error("非法操作！");
        }

        QueryWrapper<AiUnionRecord> queryWrapper = QueryGenerator.initQueryWrapper(aiUnionRecord, req.getParameterMap());
        Page<AiUnionRecord> page = new Page<>(pageNo, pageSize);
        queryWrapper.setEntity(aiUnionRecord);
        queryWrapper.eq("is_deleted", 0);
        queryWrapper.orderByDesc("id");
        IPage<AiUnionRecord> pageList = aiUinionRecordService.page(page, queryWrapper);

        if (pageList.getTotal() > 0) {
            pageList.getRecords().forEach(record -> {
                record.setAddTime(record.getCreateTime());
            });
        }
        return Result.ok(pageList);
    }

    @ApiOperation(value = "用户权益次数查询")
    @GetMapping(value = "/right")
    public Result<?> queryAiRight(String token) {
        AiRightResponse aiRightResp = miguAIGCApiService.parseAiRightResp(miguAIGCApiService.checkAIRight(token));
        return Result.ok(aiRightResp);
    }

    @ApiOperation("删除我的作品")
    @PutMapping("/delete/my-creation")
    public Result<Object> deleteMyCreation(@RequestParam String id, @RequestParam(value = "acToken") String acToken) {
        String userUniqueTag = (String) redisUtil.get("cms:cache:aiUnion:acToken:" + acToken);
        if (StrUtil.isBlank(userUniqueTag)) {
            return Result.error("请重新登录！");
        }
        AiUnionRecord aiUnionRecord = aiUinionRecordService.getById(id);
        if (aiUnionRecord == null || !aiUnionRecord.getUserUniqueTag().equals(userUniqueTag)) {
            return Result.error("非法操作！");
        }
        log.info("{}-删除我的作品,请求参数:{}", userUniqueTag, id);
        aiUinionRecordService.update(new LambdaUpdateWrapper<AiUnionRecord>()
                .set(AiUnionRecord::getIsDeleted, 1)
                .eq(AiUnionRecord::getId, id));
        return Result.ok();
    }


    @ApiOperation("任务创作-穿越")
    @PostMapping("/create/facePicFusionTask")
    public Result<Object> createAiUnionFacePicFusionTask(@RequestBody AiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        log.info("{}-图片换脸任务创作,请求参数:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO);
        String redisKey = "ts:createAiUnionFacePicFusionTask:" + aiPicFuseFaceTaskCreateDTO.getUserUniqueTag();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }

        Result<Object> result;
        AiRightResponse aiRightResp = miguAIGCApiService.parseAiRightResp(miguAIGCApiService.checkAIRight(aiPicFuseFaceTaskCreateDTO.getToken()));
        if (aiRightResp.getStatus() == 1 && (aiRightResp.getRightsCount() + aiRightResp.getExperienceCount()) > 0) {
            result = Result.ok(aiUnionService.createFacePicFusionTask(aiPicFuseFaceTaskCreateDTO));
        } else {
            result = Result.error("可用次数不足！");
        }

        redisUtil.del(redisKey);

        return result;
    }



    @ApiOperation("穿越影视剧-视频换脸")
    @PostMapping("/create/videoFaceSwappingTask")
    public Result<Object> videoFaceSwappingTask(@RequestBody UnionVideoFaceFusionDTO unionVideoFaceFusionDTO) {

        log.info("{}-视频换脸任务创作,请求参数:{}", AIFaceSwappingConstants.AI_TAG, unionVideoFaceFusionDTO);
        String redisKey = "ts:createFaceVideoFusionTask:" + unionVideoFaceFusionDTO.getUserUniqueTag();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }

        Result<Object> result;
        AiRightResponse aiRightResp = miguAIGCApiService.parseAiRightResp(miguAIGCApiService.checkAIRight(unionVideoFaceFusionDTO.getToken()));
        if (aiRightResp.getStatus() == 1 && (aiRightResp.getRightsCount() + aiRightResp.getExperienceCount()) > 0) {
            // 已开通，进行创作
            result = Result.ok(aiUnionService.createVideoFaceSwappingTask(unionVideoFaceFusionDTO));
        } else {
            result = Result.error("可用次数不足！");
        }

        //删除key
        redisUtil.del(redisKey);

        return result;
    }

   @ApiOperation("任务创作-穿越")
    @GetMapping("/query/templateList")
    public Result<Object> queryTemplate(@RequestParam(defaultValue = "2") Integer topicType) {
        log.info("{}-查询主题模板,请求参数:{}", AI_TAG, topicType);
        return Result.ok(aiFaceTemplateService.getTopicTypeTemplateIdList(topicType));
    }

    @ApiOperation("任务创作-穿越")
    @GetMapping("/query/mainTopic")
    public Result<Object> mainTopic() {
        return Result.ok(Optional.ofNullable(redisUtil.get("cms:cache:aiUnion:mainTopic")).orElseGet(() -> {
            DisplayConfigDto defaultDisplayConfig = DisplayConfigDto.builder()
                    .mainTopic(1)
                    .allowSwitch(false)
                    .build();
            redisUtil.set("cms:cache:aiUnion:mainTopic", defaultDisplayConfig);
            return defaultDisplayConfig;
        }));
    }

    @ApiOperation("查询任务创作结果")
    @GetMapping("/query/taskResult")
    public Result<Object> queryTaskResult(@RequestParam String taskId) {
        return Result.ok(aiUnionService.queryTaskResult(taskId));
    }

    @ApiOperation("查询任务创作结果")
    @GetMapping("/query/getAcToken")
    public Result<Object> getAcToken(@RequestParam String userUniqueTag) {

        String exToken = (String) redisUtil.get("cms:cache:aiUnion:userAcToken:" + userUniqueTag);
        if (StrUtil.isNotBlank(exToken)) {
            redisUtil.set("cms:cache:aiUnion:acToken:" + exToken, userUniqueTag, 60 * 60 * 24);
            redisUtil.set("cms:cache:aiUnion:userAcToken:" + userUniqueTag, exToken, 60 * 60 * 24);
            return Result.ok(exToken);
        }
        String token = UUID.randomUUID().toString();
        redisUtil.set("cms:cache:aiUnion:acToken:" + token, userUniqueTag, 60 * 60 * 24);
        redisUtil.set("cms:cache:aiUnion:userAcToken:" + userUniqueTag, token, 60 * 60 * 24);
        return Result.ok(token);
    }

    @ApiOperation("查询任务创作结果")
    @PutMapping("/check/task")
    public Result<Object> checkTask(@RequestParam String userUniqueTag, @RequestParam String token, @RequestParam String taskId) {
        return Result.ok(aiUnionService.checkTaskAndCancel(userUniqueTag, token, taskId));
    }

    @ApiOperation("新春活动-获取抽奖次数")
    @PostMapping("/xc/lottery/getCount")
    public Result xcLotteryGetCount(String mobile) {
        if (LocalDate.now().isAfter(LocalDate.of(2025, 3, 1))) {
            throw new RuntimeException("活动已结束!");
        }
        String key = XC_LOTTERY_KEY + mobile;

        // mobile 待使用抽奖次数 已获取抽奖次数
        // 判断用户已获取抽奖次数是否达10次,若达到则不处理，否则+1
        if (! redisUtil.hasKey(key)) {
            Map<String, Object> map = new HashMap<>();
            map.put("waitUse", 1);
            map.put("alreadyGet", 1);
            redisUtil.hmset(key, map, getSecond());
        } else {
            Map map = redisUtil.hmget(key);
            int i = (int) map.get("alreadyGet");
            int j = (int) map.get("waitUse");
            if (i < 10) {
                map.put("alreadyGet", i + 1);
                map.put("waitUse", j + 1);
                redisUtil.hmset(key, map, getSecond());
            }
        }
        return Result.ok();
    }
    @ApiOperation("新春活动-是否在活动期内")
    @GetMapping("/xc/lottery/activity")
    public Result activity() {
        return Result.ok(!LocalDate.now().isAfter(LocalDate.of(2025, 3, 1)));
    }
    @ApiOperation("新春活动-查询抽奖次数")
    @PostMapping("/xc/lottery/queryCount")
    public Result xcLotteryQueryCount(String mobile) {
        if (LocalDate.now().isAfter(LocalDate.of(2025, 3, 1))) {
            throw new RuntimeException("活动已结束!");
        }
        String key = XC_LOTTERY_KEY + mobile;

        int i = 0;
        if (redisUtil.hasKey(key)) {
            Map map = redisUtil.hmget(key);
            i = (int) map.get("waitUse");
        }
        return Result.okAndSetData(i);
    }

    @ApiOperation("新春活动-刮刮乐抽奖")
    @PostMapping("/xc/lottery")
    public Result xcLottery(String mobile) {
        if (LocalDate.now().isAfter(LocalDate.of(2025, 3, 1))) {
            throw new RuntimeException("活动已结束!");
        }
        String key = XC_LOTTERY_KEY + mobile;

        if (redisUtil.hasKey(key)) {
            Map map = redisUtil.hmget(key);
            Integer i = (Integer) map.get("waitUse");
            if (i > 0) {
                map.put("waitUse", i - 1);
                redisUtil.hmset(key, map, getSecond());
            }
        }
        return Result.ok();
    }

    private Long getSecond() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 获取当前年份
        int year = now.getYear();
        // 获取2月的最后一天
        LocalDate lastDayOfFebruary = LocalDate.of(year, 3, 1).minusDays(1);
        // 将2月最后一天的时间设置为当天的最后一秒
        LocalDateTime endOfFebruary = lastDayOfFebruary.atTime(23, 59, 59);
        // 计算当前时间到2月底的秒数
        long secondsUntilEndOfFebruary = ChronoUnit.SECONDS.between(now, endOfFebruary);
        return secondsUntilEndOfFebruary;
    }
}
