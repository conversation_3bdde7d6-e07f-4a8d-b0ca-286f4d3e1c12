package com.eleven.cms.aiunion.component;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class RabbitMQSender {

    @Resource
    @Lazy
    private RabbitTemplate rabbitTemplate;

    public <T> void sendWithDelay(String exchange,String routKey, T dto) {
        try {
            rabbitTemplate.convertAndSend(exchange, routKey, dto);
        } catch (Exception e) {
            // 统一异常日志格式（推荐记录DTO关键字段而非全部内容）
            String logMessage = String.format("发送MQ失败（类型：%s，ID：%s），详细信息：%s", exchange,
                    JSONObject.toJSONString(dto),
                    JSONObject.toJSONString(dto).substring(0, 500) // 避免日志过长
            );
            log.error(logMessage, e);
        }
    }
    public <T> void sendWithQueue(String queueName,T dto) {
        try {
            rabbitTemplate.convertAndSend(queueName, dto);
        } catch (Exception e) {
            // 统一异常日志格式（推荐记录DTO关键字段而非全部内容）
            String logMessage = String.format("发送MQ失败（类型：%s，ID：%s），详细信息：%s", queueName,
                    JSONObject.toJSONString(dto),
                    JSONObject.toJSONString(dto).substring(0, 500) // 避免日志过长
            );
            log.error(logMessage, e);
        }
    }
}
