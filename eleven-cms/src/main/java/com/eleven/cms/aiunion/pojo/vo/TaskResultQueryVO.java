package com.eleven.cms.aiunion.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @datetime 2024/10/19 12:57
 */
@Data
public class TaskResultQueryVO {

    private String id;

    private String templateName;

    private String templateRemark;

    private String ringUrl;

    private String originalUrl;

    private String videoPicUrl;

    private Integer ringMakeStatus;

    private String failReason;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;
}
