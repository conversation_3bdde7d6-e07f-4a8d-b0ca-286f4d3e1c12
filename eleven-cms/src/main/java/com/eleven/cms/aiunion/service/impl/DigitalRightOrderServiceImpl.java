package com.eleven.cms.aiunion.service.impl;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunion.entity.DigitalRightOrder;
import com.eleven.cms.aiunion.entity.DigitalRightsPackages;
import com.eleven.cms.aiunion.entity.PayOrders;
import com.eleven.cms.aiunion.mapper.DigitalRightOrderMapper;
import com.eleven.cms.aiunion.pojo.dto.DigitalRightDTO;
import com.eleven.cms.aiunion.service.IDigitalRightOrderService;
import com.eleven.cms.aiunion.service.IDigitalRightsPackagesService;
import com.eleven.cms.aiunion.service.IPayOrdersService;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.enums.PayEnum;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.exception.BusinessException;
import com.eleven.cms.mapper.AppDictItemMapper;
import com.eleven.cms.service.pay.AlipayTradeWapPay;
import com.eleven.cms.util.PhoneValidatorUtil;
import com.eleven.cms.vo.AppDictItemVO;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class DigitalRightOrderServiceImpl extends ServiceImpl<DigitalRightOrderMapper, DigitalRightOrder> implements IDigitalRightOrderService {


    @Resource
    IDigitalRightsPackagesService digitalRightsPackagesService;

    @Resource
    IPayOrdersService iPayOrdersService;

    @Resource
    AlipayTradeWapPay alipayTradeWapPay;

    @Resource
    AppDictItemMapper appDictItemMapper;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public DigitalRightDTO.RechargeRes recharge(DigitalRightDTO.DigitalRightReq rightReq) {

        if (!PhoneValidatorUtil.isValidPhone(rightReq.getMobile())) {
            throw new BusinessException("手机号格式错误");
        }
        // 查询虚拟手机号码前缀
        List<AppDictItemVO> dictItemList = appDictItemMapper.getDictItemByCode("brush_order_mobile_prefix");
        if (ObjectUtil.isNotEmpty(dictItemList)) {
            for (AppDictItemVO sysDictItemVO : dictItemList) {
                if (rightReq.getMobile().startsWith(sysDictItemVO.getValue())) {
                    throw new JeecgBootException("虚拟手机号无法下单");
                }
            }
        }

        String openId = "";
        String userId = "";
        try {
            //判断重复当月订单
            AlipaySystemOauthTokenResponse token = alipayTradeWapPay.getToken(rightReq.getCode(), "HS_PAY");
            if (Objects.isNull(token) || StringUtils.isEmpty(token.getUserId())) {
                throw new BusinessException("支付宝授权失败");
            }
            userId = token.getUserId();
            if (StringUtils.isEmpty(userId)) {
                throw new BusinessException("支付宝授权失败");
            }

            DateTime beginOfMonth = DateUtil.beginOfMonth(new Date());
            DateTime endOfMonth = DateUtil.endOfMonth(new Date());
            DigitalRightOrder order = this.getOne(new LambdaQueryWrapper<DigitalRightOrder>()
                    .eq(DigitalRightOrder::getUserId, userId)
                    .ge(DigitalRightOrder::getOrderTime, beginOfMonth)
                    .eq(DigitalRightOrder::getPayStatus, PayStatueEnum.PAID.getPayType())
                    .le(DigitalRightOrder::getOrderTime, endOfMonth).last("limit 1").orderByDesc(DigitalRightOrder::getOrderTime));
            if (Objects.nonNull(order)) {
                throw new BusinessException("本月您已购买过此产品包");
            }

        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
        //判断支付类型是否存在
        PayEnum enumByPayType = PayEnum.getEnumByPayType(rightReq.getPayType());
        if (enumByPayType == null) {
            throw new BusinessException("不存在此支付类型");
        }

        //查询产品包是否存在
        DigitalRightsPackages packages = digitalRightsPackagesService.getById(rightReq.getPackageId());
        if (Objects.isNull(packages)) {
            throw new BusinessException("产品包不存在");
        }
        String orderNo = "dro_" + IdWorker.get32UUID();
        BigDecimal amount = packages.getProductPrice();
        String body = "";
        String outTradeNo = "";
        DigitalRightOrder digitalRightOrder = new DigitalRightOrder();
        digitalRightOrder.setRightOrderNo(orderNo);
        digitalRightOrder.setMobile(rightReq.getMobile());
        digitalRightOrder.setPayType(rightReq.getPayType());
        digitalRightOrder.setPackageId(rightReq.getPackageId());
        digitalRightOrder.setAmount(packages.getProductPrice());
        digitalRightOrder.setOpenId(openId);
        digitalRightOrder.setUserId(userId);
        digitalRightOrder.setClientId(rightReq.getClientId());
        digitalRightOrder.setOrderTime(new Date());
        if (enumByPayType == PayEnum.ALI_H5_PAY) {
            try {
                AlipayTradeWapPayResponse aliResponse = alipayTradeWapPay.wapPay(orderNo, String.valueOf(amount), "权益商城充值", "", "HS_PAY");
                body = aliResponse.getBody();
                System.out.println(aliResponse.isSuccess());
                outTradeNo = aliResponse.getTradeNo();
                PayOrders payOrders = savePayOrders(amount, orderNo, enumByPayType.getPayType(), outTradeNo, JSONObject.toJSONString(aliResponse));
                digitalRightOrder.setPayTradeNo(aliResponse.getTradeNo());
                digitalRightOrder.setPayStatus(PayStatueEnum.UNPAID.getPayType());
                digitalRightOrder.setPayOrdersId(payOrders.getId());
            } catch (AlipayApiException e) {
                log.error("创建订单失败:入参{}", JSONObject.toJSONString(rightReq), e);
                throw new BusinessException("创建订单失败");
            }
        } else if (enumByPayType == PayEnum.WECHAT_H5_PAY) {
            return null;
        } else {
            log.error("");
            throw new BusinessException("不存在此支付类型");
        }

        //保存权益订单
        this.save(digitalRightOrder);
        return DigitalRightDTO.RechargeRes.builder().body(body).orderNo(orderNo).build();
    }


    /**
     * @param amount
     * @param orderNo
     * @param payType
     * @param outTradeNo
     * @param responseJson
     * @return
     */

    public PayOrders savePayOrders(BigDecimal amount, String orderNo, Integer payType, String outTradeNo, String responseJson) {
        PayOrders payOrders = new PayOrders();
        payOrders.setBusinessType(PayBusineesTypeEnum.DIGITAL_RIGHTS.getType());
        payOrders.setPayDesc("权益商城充值");
        payOrders.setOrderNo(orderNo);
        payOrders.setAmount(amount);
        payOrders.setPayTime(new Date());
        payOrders.setPayStatus(PayStatueEnum.UNPAID.getPayType());
        payOrders.setPaymentType(payType);
        payOrders.setOutTradeNo(outTradeNo);
        payOrders.setResponseJson(responseJson);
        iPayOrdersService.save(payOrders);
        return payOrders;
    }

    @Override
    public DigitalRightOrder getByOrderNo(String orderNo) {

        LambdaQueryWrapper<DigitalRightOrder> queryWrapper = Wrappers.<DigitalRightOrder>query().lambda()
                .eq(DigitalRightOrder::getRightOrderNo, orderNo);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public AlipaySystemOauthTokenResponse getAccessToken(String code, String type) {
        try {
            AlipaySystemOauthTokenResponse token = alipayTradeWapPay.getToken(code, type);
            String redisKey = "ali:pay:accessToken" + token.getAccessToken();
            redisUtil.set(redisKey, token.getUserId());
            return token;
        } catch (AlipayApiException e) {
            log.error("获取token失败", e);
            throw new BusinessException("获取token失败");
        }
    }
}
