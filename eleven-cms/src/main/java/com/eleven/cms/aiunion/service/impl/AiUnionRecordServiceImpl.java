package com.eleven.cms.aiunion.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunion.entity.AiUnionRecord;
import com.eleven.cms.aiunion.mapper.AiUnionRecordMapper;
import com.eleven.cms.aiunion.service.IAiUnionRecordService;
import com.eleven.cms.aiunion.service.IAiUnionService;
import com.eleven.cms.aivrbt.annotaion.DistributedLock;
import com.eleven.cms.remote.CommonSecurityService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * @Description: ai_union_record
 * @Author: jeecg-boot
 * @Date:   2024-11-20
 * @Version: V1.0
 */
@Slf4j
@Service
public class AiUnionRecordServiceImpl extends ServiceImpl<AiUnionRecordMapper, AiUnionRecord> implements IAiUnionRecordService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CommonSecurityService commonSecurityService;
    @Autowired
    @Lazy
    private IAiUnionService aiUnionService;

    @DistributedLock(name = "querySecurityResultScheduled")
    @Scheduled(cron = "0/5 * * * * ?")
    public void querySecurityResultScheduled() {
        Map<Object, Object> dataIdMap = redisUtil.hmget(CommonSecurityService.PREFIX_KEY+"pushed");

        if (dataIdMap.isEmpty()) {
            log.debug("没有Aiunion需要查询的送审结果,轮询任务结束");
            return;
        }
        for (Object dataId : dataIdMap.keySet()) {
            String recordId = (String) dataIdMap.get(dataId);
            int i = (Integer)  commonSecurityService.securityResult((String)dataId);
            if (i == 1) {
                log.info("咪咕安审-查询到送审结果为成功,recordId:{},dataId:{}", recordId, dataId);
                AiUnionRecord record = lambdaQuery().select(AiUnionRecord::getVideoUrl).eq(AiUnionRecord::getId, recordId).one();
                String dataUrl = Optional.ofNullable(record).map(AiUnionRecord::getVideoUrl).orElse(null);
                update(new LambdaUpdateWrapper<AiUnionRecord>()
                        .set(AiUnionRecord::getVideoStatus, 1)
                        .eq(AiUnionRecord::getId, recordId));
                aiUnionService.handleMiguTaskResult(recordId, true, dataUrl);
                redisUtil.hdel(CommonSecurityService.PREFIX_KEY+"pushed", dataId);
            } else if (i == -1) {
                log.error("咪咕安审-查询到送审结果为失败,recordId:{},dataId:{}", recordId, dataId);
                AiUnionRecord record = lambdaQuery().select(AiUnionRecord::getVideoUrl).eq(AiUnionRecord::getId, recordId).one();
                String dataUrl = Optional.ofNullable(record).map(AiUnionRecord::getVideoUrl).orElse(null);
                update(new LambdaUpdateWrapper<AiUnionRecord>()
                        .set(AiUnionRecord::getVideoStatus, -1)
                        .set(AiUnionRecord::getFailReason, "内容违规")
                        .eq(AiUnionRecord::getId, recordId));
                aiUnionService.handleMiguTaskResult(recordId, false, dataUrl);
                redisUtil.hdel(CommonSecurityService.PREFIX_KEY+"pushed", dataId);
            }
        }
        log.debug("轮询咪咕视频安全审核结束{}", dataIdMap);
    }
}
