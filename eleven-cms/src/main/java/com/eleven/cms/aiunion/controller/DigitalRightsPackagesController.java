package com.eleven.cms.aiunion.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.eleven.cms.aiunion.entity.DigitalRightsPackages;
import com.eleven.cms.aiunion.service.IDigitalRightsPackagesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: digital_Rights_packages
 * @Author: jeecg-boot
 * @Date:   2025-03-19
 * @Version: V1.0
 */
@Api(tags="digital_Rights_packages")
@RestController
@RequestMapping("/cms/digitalRightsPackages")
@Slf4j
public class DigitalRightsPackagesController extends JeecgController<DigitalRightsPackages, IDigitalRightsPackagesService> {
	@Autowired
	private IDigitalRightsPackagesService digitalRightsPackagesService;
	
	/**
	 * 分页列表查询
	 *
	 * @param digitalRightsPackages
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "digital_Rights_packages-分页列表查询")
	@ApiOperation(value="digital_Rights_packages-分页列表查询", notes="digital_Rights_packages-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DigitalRightsPackages digitalRightsPackages,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DigitalRightsPackages> queryWrapper = QueryGenerator.initQueryWrapper(digitalRightsPackages, req.getParameterMap());
		Page<DigitalRightsPackages> page = new Page<DigitalRightsPackages>(pageNo, pageSize);
		IPage<DigitalRightsPackages> pageList = digitalRightsPackagesService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param digitalRightsPackages
	 * @return
	 */
	@AutoLog(value = "digital_Rights_packages-添加")
	@ApiOperation(value="digital_Rights_packages-添加", notes="digital_Rights_packages-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DigitalRightsPackages digitalRightsPackages) {
		digitalRightsPackagesService.save(digitalRightsPackages);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param digitalRightsPackages
	 * @return
	 */
	@AutoLog(value = "digital_Rights_packages-编辑")
	@ApiOperation(value="digital_Rights_packages-编辑", notes="digital_Rights_packages-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DigitalRightsPackages digitalRightsPackages) {
		digitalRightsPackagesService.updateById(digitalRightsPackages);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "digital_Rights_packages-通过id删除")
	@ApiOperation(value="digital_Rights_packages-通过id删除", notes="digital_Rights_packages-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		digitalRightsPackagesService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "digital_Rights_packages-批量删除")
	@ApiOperation(value="digital_Rights_packages-批量删除", notes="digital_Rights_packages-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.digitalRightsPackagesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "digital_Rights_packages-通过id查询")
	@ApiOperation(value="digital_Rights_packages-通过id查询", notes="digital_Rights_packages-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DigitalRightsPackages digitalRightsPackages = digitalRightsPackagesService.getById(id);
		if(digitalRightsPackages==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(digitalRightsPackages);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param digitalRightsPackages
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DigitalRightsPackages digitalRightsPackages) {
        return super.exportXls(request, digitalRightsPackages, DigitalRightsPackages.class, "digital_Rights_packages");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DigitalRightsPackages.class);
    }

}
