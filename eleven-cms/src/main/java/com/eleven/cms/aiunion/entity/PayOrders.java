package com.eleven.cms.aiunion.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("pay_orders")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "pay_orders对象", description = "pay_orders")
public class PayOrders implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**
     * 订单业务类型
     */
    @Excel(name = "订单业务类型", width = 15)
    @ApiModelProperty(value = "订单业务类型")
    private String businessType;
    /**
     * 订单金额（单位：分）
     */
    @Excel(name = "订单金额（单位：分）", width = 15)
    @ApiModelProperty(value = "订单金额（单位：分）")
    private BigDecimal amount;
    /**
     * 支付类型 1(微信h5) 2(支付宝h5)
     */
    @Excel(name = "支付类型 1(微信h5) 2(支付宝h5) 3(抖音支付)", width = 15)
    @ApiModelProperty(value = "支付类型 1(微信h5) 2(支付宝h5) 3(抖音支付)")
    private Integer paymentType;
    /**
     * -1(支付失败),0(未支付),1(已支付),2(已退款)
     */
    @Excel(name = "-1(支付失败),0(未支付),1(已支付),2(已退款)", width = 15)
    @ApiModelProperty(value = "-1(支付失败),0(未支付),1(已支付),2(已退款)")
    private Integer payStatus;
    /**
     * 外部流水号:微信支付流水号,支付宝流水订单号
     */
    @Excel(name = "外部流水号:微信支付流水号,支付宝流水订单号", width = 15)
    @ApiModelProperty(value = "外部流水号:微信支付流水号,支付宝流水订单号")
    private String outTradeNo;
    /**
     * 支付时间
     */
    @Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
    /**
     * 回调时间
     */
    @Excel(name = "回调时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "回调时间")
    private Date notifyTime;
    /**
     * 支付返回结果
     */
    @Excel(name = "支付返回结果", width = 15)
    @ApiModelProperty(value = "支付返回结果")
    private String responseJson;
    /**
     * 订单描述
     */
    @Excel(name = "订单描述", width = 15)
    @ApiModelProperty(value = "订单描述")
    private String payDesc;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
