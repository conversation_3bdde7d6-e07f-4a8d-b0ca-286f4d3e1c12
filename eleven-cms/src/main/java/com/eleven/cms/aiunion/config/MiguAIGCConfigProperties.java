package com.eleven.cms.aiunion.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @datetime 2024/12/3 11:54
 */
@Data
@Component
@ConfigurationProperties(prefix = "aigc.migu")
public class MiguAIGCConfigProperties {
    /**
     * 咪咕appId
     */
    private String appId;
    /**
     * 咪咕appSecret
     */
    private String appSecret;
    /**
     * 用户AI权益判断URI
     */
    private String aiRightsUri;
    /**
     * 预扣减次数URI
     */
    private String preReduceCountUri;

    /**
     * AI创作任务取消URI
     */
    private String cancelTaskUri;
    /**
     * AI创作结果上报
     */
    private String reportResultUri;
    /**
     * 咪咕ai域名
     */
    private String hostName;
    /**
     * 协议类型前缀
     */
    private String protocol;
}
