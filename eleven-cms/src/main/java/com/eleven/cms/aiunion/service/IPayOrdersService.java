package com.eleven.cms.aiunion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aiunion.entity.PayOrders;
import com.eleven.cms.aiunion.enums.PayOrderPayTypeEnum;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.enums.PayStatueEnum;

import java.math.BigDecimal;
import java.util.Date;


public interface IPayOrdersService extends IService<PayOrders> {

    PayOrders getPayOrdersByOutTradeNo(String outTradeNo);
    PayOrders getPayOrdersByOrderNo(String outTradeNo);
    PayOrders pay(String outTradeNo);

    void updatePayStatus(String orderNo, PayStatueEnum payStatus, Date payTime);

    void createOrder(String outTradeNo, PayBusineesTypeEnum businessType, BigDecimal amount, PayOrderPayTypeEnum payType);
}
