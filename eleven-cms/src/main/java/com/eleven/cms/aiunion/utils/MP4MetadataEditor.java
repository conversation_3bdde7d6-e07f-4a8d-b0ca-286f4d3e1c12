package com.eleven.cms.aiunion.utils;


import org.jcodec.common.io.FileChannelWrapper;
import org.jcodec.common.io.NIOUtils;
import org.jcodec.containers.mp4.MP4Util;
import org.jcodec.containers.mp4.boxes.*;

import java.io.File;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;


public class MP4MetadataEditor {
    public static void main(String[] args) {

        extracted();
    }



    private static void extracted() {
        try {

            File inputFile = new File("input.mp4");
            File outputFile = new File("output.mp4");

            // 1. 读取 MP4 文件
            MovieBox movie = MP4Util.parseMovie(inputFile);

            // 3. 写入新文件
            try (FileChannelWrapper out = NIOUtils.writableFileChannel("23.mp4")) {
                MP4Util.writeMovie(out, movie);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
