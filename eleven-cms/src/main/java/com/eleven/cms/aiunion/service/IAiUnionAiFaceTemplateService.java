package com.eleven.cms.aiunion.service;

import com.eleven.cms.aiunion.entity.AiUnionAiFaceTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * @Description: ai_union_ai_face_template
 * @Author: jeecg-boot
 * @Date:   2024-11-20
 * @Version: V1.0
 */
public interface IAiUnionAiFaceTemplateService extends IService<AiUnionAiFaceTemplate> {

    List<String> getGenderTemplateIdList(Integer gender);

    List<AiUnionAiFaceTemplate> getTopicTypeTemplateIdList(Integer topicType);
}
