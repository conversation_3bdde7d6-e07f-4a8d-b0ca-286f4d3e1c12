package com.eleven.cms.enums;

import lombok.Getter;

@Getter
public enum PayEnum {

    WECHAT_H5_PAY(1, "微信h5支付"),
    ALI_H5_PAY(2, "阿里h5支付");

    PayEnum(Integer payType, String desc) {
        this.desc = desc;
        this.payType = payType;
    }

    public static PayEnum getEnumByPayType(int payType) {
        for (PayEnum payEnum : PayEnum.values()) {
            if (payEnum.getPayType() == payType) {
                return payEnum;
            }
        }
        return null;
    }

    private Integer payType;
    private String desc;
}
