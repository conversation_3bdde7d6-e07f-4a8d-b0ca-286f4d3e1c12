package com.eleven.cms.enums;

import lombok.Getter;

@Getter
public enum PayBusineesTypeEnum {

    DIGITAL_RIGHTS("digitalRights","权益商城"),
    WALLPAPER("miniAppWallpaper", "小程序支付"),
    COOL_INCOMING_CALL_WX_MINI_APP("coolIncomingCallWxMiniApp", "炫酷来电微信小程序"),
    COOL_INCOMING_CALL_OFFICIAL_ACCOUNT("coolIncomingCall-wxOfficialAccount", "炫酷来电微信公众号（炫酷彩铃秀）"),
    MAI_HE_JU_CHANG_DUAN_JU("maiHeJuChangDuanJu", "麦盒剧场短剧"),
    ;
    private final String type;
    private final String desc;
    PayBusineesTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PayBusineesTypeEnum getEnum(String type) {
        for (PayBusineesTypeEnum e : PayBusineesTypeEnum.values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }
}
