package com.eleven.cms.enums;

import lombok.Getter;

@Getter
public enum PayStatueEnum {

    PAID(1, "已支付"),
    FAILED(-1, "支付失败"),
    UNPAID(0, "未支付"),
    CANCEL(2, "已取消"),
    REFUNDING(3, "退款中"),
    REFUNDED(4, "已退款");

    PayStatueEnum(Integer payType, String desc) {
        this.desc = desc;
        this.payType = payType;
    }

    public static PayStatueEnum getEnumByPayType(int payType) {
        for (PayStatueEnum payEnum : PayStatueEnum.values()) {
            if (payEnum.getPayType() == payType) {
                return payEnum;
            }
        }
        return null;
    }

    private Integer payType;
    private String desc;
}
