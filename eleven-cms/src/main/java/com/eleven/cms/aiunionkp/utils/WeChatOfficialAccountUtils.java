package com.eleven.cms.aiunionkp.utils;

import com.alibaba.fastjson.JSONObject;
import com.eleven.cms.util.HttpClient4Utils;
import com.github.wxpay.sdk.WXPayConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Slf4j
public class WeChatOfficialAccountUtils {
    /**
     * 生成随机字符串
     */
    public static String generateNonceStr() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    /**
     * 使用HttpClient通过code获取OpenID
     */
    public static String getOpenIdByCode(String code, String appid, String secret) {
        String url = WXPayConstants.WECHAT_ACCESS_TOKEN_URL +
                "?appid=" + appid +
                "&secret=" + secret +
                "&code=" + code +
                "&grant_type=authorization_code";
        String result = HttpClient4Utils.sendGet(url);
        JSONObject json = JSONObject.parseObject(result);

        // 检查是否有错误
        if (json.containsKey("errcode")) {
            int errcode = json.getIntValue("errcode");
            String errmsg = json.getString("errmsg");
            throw new RuntimeException("获取OpenID失败: " + errcode + ", " + errmsg);
        }
        // 返回OpenID
        return json.getString("openid");
    }
}
