package com.eleven.cms.aiunionkp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: mini_pay_order
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Data
@TableName("mini_pay_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_pay_order对象", description = "mini_pay_order")
public class MiniPayOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 订单号
     */
    @Excel(name = "订单号", width = 15)
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**
     * 小程序用户id
     */
    @Excel(name = "小程序用户id", width = 15)
    @ApiModelProperty(value = "小程序用户id")
    private String miniUid;
    /**
     * 支付流水号
     */
    @Excel(name = "支付流水号", width = 15)
    @ApiModelProperty(value = "支付流水号")
    private String paymentTransactionId;
    /**
     * 小程序包名
     */
    @Excel(name = "小程序包名", width = 15)
    @ApiModelProperty(value = "小程序包名")
    @Dict(dicCode = "app_channel_id")
    private String miniChannelId;
    /**
     * 小程序openid
     */
    @Excel(name = "小程序openid", width = 15)
    @ApiModelProperty(value = "小程序openid")
    private String miniOpenId;
    /**
     * 手机号码
     */
    @Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    /**
     * app用户id
     */
    @Excel(name = "app用户id", width = 15)
    @ApiModelProperty(value = "app用户id")
    private String appUid;
    /**
     * app包名
     */
    @Excel(name = "app包名", width = 15)
    @ApiModelProperty(value = "app包名")
    private String appChannelId;
    /**
     * 套餐id
     */
    @Excel(name = "套餐id", width = 15)
    @ApiModelProperty(value = "套餐id")
    private String productId;
    /**
     * 套餐名称
     */
    @Excel(name = "套餐名称", width = 15)
    @ApiModelProperty(value = "套餐名称")
    private String productName;
    /**
     * 订单金额
     */
    @Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;
    /**
     * 已退金额
     */
    @Excel(name = "已退金额", width = 15)
    @ApiModelProperty(value = "已退金额")
    private BigDecimal refundAmount;
    /**
     * 支付方式
     */
    @Excel(name = "支付方式", width = 15)
    @ApiModelProperty(value = "支付方式")
    @Dict(dicCode = "app_pay_type")
    private Integer payType;
    /**
     * 状态0=下线，1=在线
     */
    @Excel(name = "状态0=下线，1=在线", width = 15)
    @ApiModelProperty(value = "状态0=下线，1=在线")
    private Integer status;
    /**
     * 下单时间
     */
    @Excel(name = "下单时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    /**
     * 订单支付状态
     */
    @Excel(name = "订单支付状态", width = 15)
    @ApiModelProperty(value = "订单支付状态")
    @Dict(dicCode = "app_order_status")
    private Integer orderPayStatus;
    /**
     * 订单支付时间
     */
    @Excel(name = "订单支付时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单支付时间")
    private Date payTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
