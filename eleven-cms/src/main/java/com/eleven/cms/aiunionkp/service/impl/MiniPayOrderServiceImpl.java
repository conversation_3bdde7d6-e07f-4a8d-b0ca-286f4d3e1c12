package com.eleven.cms.aiunionkp.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunion.entity.PayOrders;
import com.eleven.cms.aiunion.service.IPayOrdersService;
import com.eleven.cms.aiunionkp.entity.MiniPayOrder;
import com.eleven.cms.aiunionkp.entity.MiniRefundOrder;
import com.eleven.cms.aiunionkp.mapper.MiniPayOrderMapper;
import com.eleven.cms.aiunionkp.service.IMiniPayOrderService;
import com.eleven.cms.aiunionkp.service.IMiniRefundOrderService;
import com.eleven.cms.aivrbt.dto.MiniOrderReq;
import com.eleven.cms.aivrbt.dto.MiniRefundOrderReq;
import com.eleven.cms.aivrbt.entity.WxMiniAppUserRelationship;
import com.eleven.cms.aivrbt.enums.MiniAndAppRelationShipChannelEnum;
import com.eleven.cms.aivrbt.enums.MiniAppChannelEnum;
import com.eleven.cms.aivrbt.enums.MiniOrderStatusEnum;
import com.eleven.cms.aivrbt.service.WxMiniAppUserRelationshipService;
import com.eleven.cms.aivrbt.vo.AppMembershipPackageProductVO;
import com.eleven.cms.client.AppFeignClient;
import com.eleven.cms.dto.WechatMiniAppPayParam;
import com.eleven.cms.dto.WechatPayResult;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.service.pay.IWechatPayAppService;
import com.eleven.cms.wallpaper.entity.MiniAppWechatUser;
import com.eleven.cms.wallpaper.service.IMiniAppWechatUserService;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * @Description: mini_pay_order
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Service
public class MiniPayOrderServiceImpl extends ServiceImpl<MiniPayOrderMapper, MiniPayOrder> implements IMiniPayOrderService {


    @Resource
    private IWechatPayAppService wechatPayAppService;

    @Resource
    private IPayOrdersService iPayOrdersService;

    @Resource
    private IMiniAppWechatUserService miniAppWechatUserService;
    @Resource
    private WxMiniAppUserRelationshipService wxMiniAppUserRelationshipService;

    @Resource
    private IMiniRefundOrderService miniRefundOrderService;

    @Resource
    private AppFeignClient appFeignClient;

    private static final String REFUND = "refund-";

    @Override
    public Result<Object> createOrder(MiniOrderReq orderReq) {
        BigDecimal actualPrice = getAmount(orderReq);
        if (Objects.isNull(actualPrice)) {
            log.error("createOrder  actualPrice 套餐支付金额失效，请选择生效套餐支付");
            return Result.error("套餐支付金额失效，请选择生效套餐支付");
        }
        BigDecimal payAmount = actualPrice.multiply(new BigDecimal(100));

        String channelId = orderReq.getChannelId();
        String orderNo = IdWorker.get32UUID();

        WechatMiniAppPayParam payParam = WechatMiniAppPayParam.builder().outTradeNo(orderNo)
                .businessType(channelId)
                .amount(payAmount.intValue())
                .orderNo(orderNo)
                .openId(orderReq.getOpenId())
                .build();

        WechatPayResult wechatPayResult = wechatPayAppService.wechatMiniAppPay(payParam);
        savePayOrders(actualPrice, orderNo, orderReq.getPayType(), JSONObject.toJSONString(wechatPayResult), channelId);

        MiniAppWechatUser miniAppWechatUser = miniAppWechatUserService.lambdaQuery().eq(MiniAppWechatUser::getOpenId, orderReq.getOpenId()).eq(MiniAppWechatUser::getChannelId, channelId).last("limit 1").one();
        WxMiniAppUserRelationship wxMiniAppUserRelationship = wxMiniAppUserRelationshipService.getOne(new LambdaQueryWrapper<WxMiniAppUserRelationship>()
                .eq(WxMiniAppUserRelationship::getMiniOpenId, orderReq.getOpenId())
                .eq(WxMiniAppUserRelationship::getMobile, orderReq.getMobile())
                .eq(WxMiniAppUserRelationship::getMiniChannelId, channelId).last("limit 1"));
        MiniPayOrder order = new MiniPayOrder();
        order.setOrderPayStatus(MiniOrderStatusEnum.PENDING_PAYMENT.getCode());
        if (Objects.nonNull(miniAppWechatUser)) {
            order.setMiniUid(miniAppWechatUser.getId().toString());
        }
        order.setOrderAmount(actualPrice);
        order.setOrderNo(orderNo);
        order.setMiniOpenId(orderReq.getOpenId());
        order.setMobile(orderReq.getMobile());
        order.setPayType(orderReq.getPayType());
        order.setOrderTime(new Date());
        order.setMiniChannelId(channelId);
        if (Objects.nonNull(wxMiniAppUserRelationship)) {
            order.setAppUid(wxMiniAppUserRelationship.getAppUid());
            order.setAppChannelId(wxMiniAppUserRelationship.getAppChannelId());
        }
        order.setProductId(orderReq.getProductId());
        order.setProductName(orderReq.getProductName());
        order.setCreateBy(orderReq.getMobile());
        order.setUpdateBy(orderReq.getMobile());
        order.setUpdateTime(new Date());
        order.setCreateTime(new Date());
        save(order);
        return Result.ok(wechatPayResult);
    }


    private BigDecimal getAmount(MiniOrderReq orderReq) {
        String productId = orderReq.getProductId();
        MiniAndAppRelationShipChannelEnum miniChannelIdAndMiniSource = MiniAndAppRelationShipChannelEnum.getByMiniChannelId(orderReq.getChannelId());
        if (Objects.nonNull(miniChannelIdAndMiniSource)) {
            Result<AppMembershipPackageProductVO> memberShipPackageProduct = appFeignClient.getMemberShipPackageProduct(productId);
            AppMembershipPackageProductVO appMembershipPackageProductVO = memberShipPackageProduct.getResult();
            if (Objects.nonNull(appMembershipPackageProductVO)) {
                return appMembershipPackageProductVO.getActualPrice();
            }
        }
        return null;
    }

    @Override
    public Result<Object> createRefundOrder(MiniRefundOrderReq refundOrderReq) {
        MiniPayOrder miniPayOrder = getById(refundOrderReq.getOrderId());
        if (Objects.isNull(miniPayOrder)) {
            return Result.error("订单不存在");
        }
        int count = miniRefundOrderService.count(new LambdaQueryWrapper<MiniRefundOrder>()
                .eq(MiniRefundOrder::getOrderNo, miniPayOrder.getOrderNo()));
        if (count > 0) {
            return Result.error("请勿重复退款");
        }
        MiniRefundOrder refundOrder = miniRefundOrderService.create(miniPayOrder,refundOrderReq.getRefundAmount());
        String totalAmount = miniPayOrder.getOrderAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        String businessType = REFUND+MiniAppChannelEnum.COOL_INCOMING_CALL_WX_MINI_APP.getCode();
        wechatPayAppService.appPayRefundConfig(refundOrder.getOrderNo(), refundOrder.getRefundOrderNo(), refundOrderReq.getRefundAmount(), totalAmount, "管理员退款", businessType);
        miniPayOrder.setRefundAmount(new BigDecimal(refundOrderReq.getRefundAmount()));
        miniPayOrder.setOrderPayStatus(MiniOrderStatusEnum.REFUNDING.getCode());
        updateById(miniPayOrder);
        return Result.ok();
    }

    public void savePayOrders(BigDecimal amount, String orderNo, Integer payType, String responseJson, String channelId) {
        PayBusineesTypeEnum payBusineesTypeEnum = PayBusineesTypeEnum.getEnum(channelId);
        PayOrders payOrders = new PayOrders();
        payOrders.setBusinessType(channelId);
        if (Objects.nonNull(payBusineesTypeEnum)) {
            payOrders.setPayDesc(payBusineesTypeEnum.getDesc() + "支付");
        }
        payOrders.setOrderNo(orderNo);
        payOrders.setAmount(amount);
        payOrders.setPayTime(new Date());
        payOrders.setPayStatus(PayStatueEnum.UNPAID.getPayType());
        payOrders.setPaymentType(payType);
        payOrders.setResponseJson(responseJson);
        iPayOrdersService.save(payOrders);
    }
}
