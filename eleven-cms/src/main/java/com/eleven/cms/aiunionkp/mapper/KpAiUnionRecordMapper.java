package com.eleven.cms.aiunionkp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eleven.cms.aiunionkp.entity.KpAiUnionRecord;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: ai_union_record
 * @Author: jeecg-boot
 * @Date: 2024-11-20
 * @Version: V1.0
 */
public interface KpAiUnionRecordMapper extends BaseMapper<KpAiUnionRecord> {

    Integer queryUsableCount(@Param(value = "count") Integer count, @Param(value = "userUniqueTag") String userUniqueTag);
}
