package com.eleven.cms.aiunionkp.service;

import com.eleven.cms.aiunionkp.pojo.dto.KpAiPicAiUnionFuseFaceTaskCreateDTO;

/**
 * <AUTHOR>
 * @datetime 2024/11/20 17:41
 */
public interface KpIAiUnionService {
    /**
     * 查询任务创作结果
     *
     * @param taskId 任务ID，用于标识特定的任务创作结果
     * @return 返回任务创作结果的对象，如果找不到对应的任务则返回null
     */
    Object queryTaskResult(String taskId);

    /**
     * 任务创作-图片换脸
     *
     * @param aiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO
     * @return String 视频ID
     */
    Object createFacePicFusionTask(KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO);

//    /**
//     * 处理咪咕任务上报结果
//     * @param recordId 本地记录id
//     * @param result 处理结果
//     * @param videoUrl 生成的视频地址
//     */
//    void handleMiguTaskResult(String recordId, Boolean result, String videoUrl);
//
//    /**
//     * 根据用户唯一标签检查任务是否存在，并根据检查结果决定是否取消任务
//     *
//     * @param userUniqueTag 用户唯一标签，用于识别用户
//     * @param token 用于调用API的令牌
//     * @param taskId 需要检查的任务ID
//     * @return 如果任务存在且未被取消，则返回true；否则返回false
//     */
//    Boolean checkTaskAndCancel(String userUniqueTag, String token, String taskId);

    /**
     * 处理消息队列消息 创作任务
     *
     * @param aiPicFuseFaceTaskCreateDTO 任务消息体
     */
    void handleCreateTaskMsg(KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO);
}
