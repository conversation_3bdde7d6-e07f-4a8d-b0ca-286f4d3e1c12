package com.eleven.cms.aiunionkp.service.impl;

import com.eleven.cms.aiunionkp.service.WechatOfficialAccountService;
import com.eleven.cms.aiunionkp.utils.WeChatOfficialAccountUtils;
import com.eleven.cms.aivrbt.enums.MiniAndAppRelationShipChannelEnum;
import com.eleven.cms.entity.WechatConfigLog;
import com.eleven.cms.service.IWechatConfigLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
public class WechatOfficialAccountServiceImpl implements WechatOfficialAccountService {

    @Resource
    private IWechatConfigLogService wechatConfigLogService;

    private static final String OA = "OA";

    @Override
    public Result<Object> getOpenIdByCode(String code, String sourceChannel) {
        MiniAndAppRelationShipChannelEnum appRelationShipChannelEnum = MiniAndAppRelationShipChannelEnum.getByAppSourceChannelId(sourceChannel);
        if (Objects.isNull(appRelationShipChannelEnum)) {
            return Result.error("来源错误");
        }
        WechatConfigLog wechatConfig = wechatConfigLogService.getWechatConfig(OA, sourceChannel);
        if (Objects.isNull(wechatConfig)) {
            return Result.error("必要信息未配置，请联系客服");
        }
        String openId = WeChatOfficialAccountUtils.getOpenIdByCode(code, wechatConfig.getAppId(), wechatConfig.getAppSecret());
        if (StringUtils.isEmpty(openId)) {
            return Result.error("获取微信账户信息失败，请重试");
        }
        return Result.ok((Object) openId);
    }
}
