package com.eleven.cms.aiunionkp.service;

import com.eleven.cms.aiunionkp.entity.KpAiUnionAiFaceTemplate;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: ai_union_ai_face_template
 * @Author: jeecg-boot
 * @Date:   2024-11-20
 * @Version: V1.0
 */
public interface KpIAiUnionAiFaceTemplateService extends IService<KpAiUnionAiFaceTemplate> {

    List<String> getGenderTemplateIdList(Integer gender);

    List<KpAiUnionAiFaceTemplate> getTopicTypeTemplateIdList(Integer topicType);
}
