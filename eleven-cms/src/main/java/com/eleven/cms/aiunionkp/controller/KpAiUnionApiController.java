package com.eleven.cms.aiunionkp.controller;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunionkp.entity.KpAiUnionRecord;
import com.eleven.cms.aiunionkp.pojo.dto.DisplayConfigDto;
import com.eleven.cms.aiunionkp.pojo.dto.KpAiPicAiUnionFuseFaceTaskCreateDTO;
import com.eleven.cms.aiunionkp.service.KpIAiUnionAiFaceTemplateService;
import com.eleven.cms.aiunionkp.service.KpIAiUnionRecordService;
import com.eleven.cms.aiunionkp.service.KpIAiUnionService;
import com.eleven.cms.remote.MiguApiService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

import static com.eleven.cms.aiunionkp.service.impl.KpAiUnionServiceImpl.AI_TAG;

/**
 * AI一图穿越内部版
 *
 * <AUTHOR>
 * @datetime 2024/11/20 17:20
 */
@RestController
@RequestMapping("/cms/aiunion/kp")
@Slf4j
@Validated
public class KpAiUnionApiController {
    @Autowired
    private KpIAiUnionRecordService aiUinionRecordService;
    @Autowired
    private KpIAiUnionAiFaceTemplateService aiFaceTemplateService;
    @Autowired
    private KpIAiUnionService aiUnionService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private MiguApiService miguApiService;

    @ApiOperation(value = "aiunion_record-分页列表查询", notes = "cms_aiunion_record-分页列表查询")
    @GetMapping(value = "/record/list")
    public Result<?> queryPageList(KpAiUnionRecord kpAiUnionRecord,
                                   @RequestParam(value = "userUniqueTag") String userUniqueTag,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
//        String userUniqueTagToken = (String) redisUtil.get("cms:cache:aiUnion:acToken:" + acToken);
        if (StrUtil.isBlank(userUniqueTag)) {
            return Result.error("请重新登录！");
        }
//        if (!userUniqueTag.equals(userUniqueTagToken)) {
//            return Result.error("非法操作！");
//        }

        QueryWrapper<KpAiUnionRecord> queryWrapper = QueryGenerator.initQueryWrapper(kpAiUnionRecord, req.getParameterMap());
        Page<KpAiUnionRecord> page = new Page<>(pageNo, pageSize);
        queryWrapper.setEntity(kpAiUnionRecord);
        queryWrapper.eq("is_deleted", 0);
        queryWrapper.orderByDesc("id");
        IPage<KpAiUnionRecord> pageList = aiUinionRecordService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    @ApiOperation(value = "用户权益次数查询")
    @GetMapping(value = "/right")
    public Result<?> queryAiRight(@RequestParam String userUniqueTag) {
        if (!PhoneUtil.isMobile(userUniqueTag)) {
            throw new RuntimeException("请输入正确的用户信息！");
        }
        return Result.ok(aiUinionRecordService.getRight(userUniqueTag));
    }

    @ApiOperation("查询是否开通AI业务")
    @GetMapping("/query/businessOpenStatus")
    public Result<Object> queryBusinessOpenStatus(@RequestParam String mobile) {
        String channelIdStr = (String) redisUtil.get("cms:cache:aiUnion:queryChannelId");
        String[] split = channelIdStr.split(",");
        for (String vo : split) {
            boolean schMember = false;
            if (StringUtils.equals("014X0JZ", vo)) {
                schMember = miguApiService.schQuery(mobile, vo).isSubMember();
            } else {
                schMember = miguApiService.schQuery(mobile, vo).isSchMember();
            }
            if (schMember) {
                return Result.ok(vo);
            }
        }
        return Result.error(10000, "请先开通业务！");
    }

    @ApiOperation("删除我的作品")
    @PutMapping("/delete/my-creation")
    public Result<Object> deleteMyCreation(@RequestParam String id) {
//        String userUniqueTag = (String) redisUtil.get("cms:cache:aiUnion:acToken:" + acToken);
//        if (StrUtil.isBlank(userUniqueTag)) {
//            return Result.error("请重新登录！");
//        }
//        AiUnionRecord aiUnionRecord = aiUinionRecordService.getById(id);
//        if (aiUnionRecord == null || !aiUnionRecord.getUserUniqueTag().equals(userUniqueTag)) {
//            return Result.error("非法操作！");
//        }
//        log.info("{}-删除我的作品,请求参数:{}", userUniqueTag, id);
        aiUinionRecordService.update(new LambdaUpdateWrapper<KpAiUnionRecord>()
                .set(KpAiUnionRecord::getIsDeleted, 1)
                .eq(KpAiUnionRecord::getId, id));
        return Result.ok();
    }


    @ApiOperation("任务创作-穿越")
    @PostMapping("/create/facePicFusionTask")
    public Result<Object> createAiUnionFacePicFusionTask(@RequestBody KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        log.info("{}-图片换脸任务创作,请求参数:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO);
        String redisKey = "ts:createAiUnionFacePicFusionTask:" + aiPicFuseFaceTaskCreateDTO.getUserUniqueTag();
        if (!redisUtil.setIfAbsent(redisKey, "", 10)) {
            return Result.error("生成中，请稍后再试！");
        }
       if ( aiUinionRecordService.getRight(aiPicFuseFaceTaskCreateDTO.getUserUniqueTag())<=0){
           throw new JeecgBootException("当月次数已用完");
       }
        Result<Object> result;
        result = Result.ok(aiUnionService.createFacePicFusionTask(aiPicFuseFaceTaskCreateDTO));

        redisUtil.del(redisKey);

        return result;
    }

    @ApiOperation("任务创作-穿越")
    @GetMapping("/query/templateList")
    public Result<Object> queryTemplate(@RequestParam(defaultValue = "2") Integer topicType) {
        log.info("{}-查询主题模板,请求参数:{}", AI_TAG, topicType);
        return Result.ok(aiFaceTemplateService.getTopicTypeTemplateIdList(topicType));
    }

    @ApiOperation("任务创作-穿越")
    @GetMapping("/query/mainTopic")
    public Result<Object> mainTopic() {
        return Result.ok(Optional.ofNullable(redisUtil.get("cms:cache:aiUnion:hstj:mainTopic")).orElseGet(() -> {
            DisplayConfigDto defaultDisplayConfig = DisplayConfigDto.builder()
                    .mainTopic(1)
                    .allowSwitch(false)
                    .build();
            redisUtil.set("cms:cache:aiUnion:hstj:mainTopic", defaultDisplayConfig);
            return defaultDisplayConfig;
        }));
    }

    @ApiOperation("查询任务创作结果")
    @GetMapping("/query/taskResult")
    public Result<Object> queryTaskResult(@RequestParam String taskId) {
        return Result.ok(aiUnionService.queryTaskResult(taskId));
    }

//    @ApiOperation("查询任务创作结果")
//    @GetMapping("/query/getAcToken")
//    public Result<Object> getAcToken(@RequestParam String userUniqueTag) {
//
//        String exToken = (String) redisUtil.get("cms:cache:aiUnion:userAcToken:" + userUniqueTag);
//        if (StrUtil.isNotBlank(exToken)) {
//            redisUtil.set("cms:cache:aiUnion:acToken:" + exToken, userUniqueTag, 60 * 60 * 24);
//            redisUtil.set("cms:cache:aiUnion:userAcToken:" + userUniqueTag, exToken, 60 * 60 * 24);
//            return Result.ok(exToken);
//        }
//        String token = UUID.randomUUID().toString();
//        redisUtil.set("cms:cache:aiUnion:acToken:" + token, userUniqueTag, 60 * 60 * 24);
//        redisUtil.set("cms:cache:aiUnion:userAcToken:" + userUniqueTag, token, 60 * 60 * 24);
//        return Result.ok(token);
//    }

//    @ApiOperation("查询任务创作结果")
//    @PutMapping("/check/task")
//    public Result<Object> checkTask(@RequestParam String userUniqueTag, @RequestParam String token, @RequestParam String taskId) {
//        return Result.ok(aiUnionService.checkTaskAndCancel(userUniqueTag, token, taskId));
//    }

//    @ApiOperation("新春活动-获取抽奖次数")
//    @PostMapping("/xc/lottery/getCount")
//    public void test0() {
//        // userId 待使用抽奖次数 已获取抽奖次数
//
//
//        // 1.获取截止2月底的秒数
//        // 2.判断用户已获取抽奖次数是否达10次
//        // 3.
//    }
//
//    @ApiOperation("新春活动-查询抽奖次数")
//    @PutMapping("/xc/lottery")
//    public void test1() {
//        return;
//    }
//
//    @ApiOperation("新春活动-刮刮乐抽奖")
//    @PutMapping("/xc/lottery")
//    public void test2() {
//        return;
//    }
}
