<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.aiunionkp.mapper.KpAiUnionRecordMapper">

    <select id="queryUsableCount" resultType="java.lang.Integer">
        SELECT #{count} - (
            SELECT COUNT(1) FROM kp_ai_union_record
            WHERE user_unique_tag = #{userUniqueTag}
              and video_status in (0, 1)
              and create_time BETWEEN DATE_FORMAT(CURDATE(),'%Y-%m-01 00:00:00') AND DATE_FORMAT(LAST_DAY(CURRENT_DATE()),'%Y-%m-%d 23:59:59')
        ) AS usable_count
    </select>
</mapper>