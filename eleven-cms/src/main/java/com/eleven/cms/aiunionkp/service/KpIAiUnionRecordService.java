package com.eleven.cms.aiunionkp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aiunionkp.entity.KpAiUnionRecord;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.Cacheable;

/**
 * @Description: ai_union_record
 * @Author: jeecg-boot
 * @Date: 2024-11-20
 * @Version: V1.0
 */
public interface KpIAiUnionRecordService extends IService<KpAiUnionRecord> {
    Integer getRight(String userUniqueTag);

    Integer getSubscribeBizCount(String userUniqueTag);
}
