package com.eleven.cms.aiunionkp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunionkp.entity.MiniPayOrder;
import com.eleven.cms.aiunionkp.entity.MiniRefundOrder;
import com.eleven.cms.aiunionkp.mapper.MiniRefundOrderMapper;
import com.eleven.cms.aiunionkp.service.IMiniRefundOrderService;
import com.eleven.cms.aivrbt.enums.MiniOrderStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Service
public class MiniRefundOrderServiceImpl extends ServiceImpl<MiniRefundOrderMapper, MiniRefundOrder> implements IMiniRefundOrderService {

    private static final String REF = "ref-";

    @Override
    public MiniRefundOrder create(MiniPayOrder miniPayOrder, String refundAmount) {
        MiniRefundOrder miniRefundOrder = new MiniRefundOrder();
        miniRefundOrder.setMiniChannelId(miniPayOrder.getMiniChannelId());
        miniRefundOrder.setMiniUid(miniPayOrder.getMiniUid());
        miniRefundOrder.setCreateTime(new Date());
        miniRefundOrder.setCreateBy(StringUtils.EMPTY);
        miniRefundOrder.setUpdateBy(StringUtils.EMPTY);
        miniRefundOrder.setUpdateTime(new Date());
        miniRefundOrder.setOrderNo(miniPayOrder.getOrderNo());
        miniRefundOrder.setOriginalPaymentTransctionId(miniPayOrder.getPaymentTransactionId());
        miniRefundOrder.setPayType(miniPayOrder.getPayType());
        miniRefundOrder.setRefundOrderNo(REF+ UUID.randomUUID().toString());
        miniRefundOrder.setRefundAmount(new BigDecimal(refundAmount));
        miniRefundOrder.setStatus(MiniOrderStatusEnum.REFUNDING.getCode());
        save(miniRefundOrder);
        return miniRefundOrder;
    }
}
