package com.eleven.cms.aiunionkp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("mini_refund_order")
public class MiniRefundOrder {
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;
    private String miniUid;
    private String orderNo;
    private String refundOrderNo;
    private BigDecimal refundAmount;
    private String originalPaymentTransctionId;
    private Integer payType;
    private String remark;
    private Integer status;
    private String failRemark;
    private String createBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String updateBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private String miniChannelId;
}
