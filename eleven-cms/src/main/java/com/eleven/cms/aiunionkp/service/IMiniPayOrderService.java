package com.eleven.cms.aiunionkp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.aiunionkp.entity.MiniPayOrder;
import com.eleven.cms.aivrbt.dto.MiniOrderReq;
import com.eleven.cms.aivrbt.dto.MiniRefundOrderReq;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: mini_pay_order
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
public interface IMiniPayOrderService extends IService<MiniPayOrder> {

    Result<Object> createOrder(MiniOrderReq orderReq);

    Result<Object> createRefundOrder(MiniRefundOrderReq refundOrderReq);
}
