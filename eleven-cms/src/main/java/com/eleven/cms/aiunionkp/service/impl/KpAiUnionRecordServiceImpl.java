package com.eleven.cms.aiunionkp.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunionkp.entity.KpAiUnionRecord;
import com.eleven.cms.aiunionkp.mapper.KpAiUnionRecordMapper;
import com.eleven.cms.aiunionkp.service.KpIAiUnionRecordService;
import com.eleven.cms.aivrbt.annotaion.DistributedLock;
import com.eleven.cms.es.entity.EsSubscribe;
import com.eleven.cms.es.service.IEsDataService;
import com.eleven.cms.remote.CommonSecurityService;
import com.eleven.cms.remote.MiguApiService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * @Description: ai_union_record
 * @Author: jeecg-boot
 * @Date: 2024-11-20
 * @Version: V1.0
 */
@Slf4j
@Service
public class KpAiUnionRecordServiceImpl extends ServiceImpl<KpAiUnionRecordMapper, KpAiUnionRecord> implements KpIAiUnionRecordService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CommonSecurityService commonSecurityService;
    @Autowired
    private MiguApiService miguApiService;
    @Autowired
    @Lazy
    private IEsDataService esDataService;
    @Autowired
    private KpAiUnionRecordMapper kpAiUnionRecordMapper;

    @DistributedLock(name = "queryKpSecurityResultScheduled")
    @Scheduled(cron = "0/5 * * * * ?")
    public void queryKpSecurityResultScheduled() {
        Map<Object, Object> dataIdMap = redisUtil.hmget(CommonSecurityService.PREFIX_KEY + "pushed:kp");

        if (dataIdMap.isEmpty()) {
            log.debug("没有KpAiunion需要查询的送审结果,轮询任务结束");
            return;
        }
        for (Object dataId : dataIdMap.keySet()) {
            String recordId = (String) dataIdMap.get(dataId);
            int i = (Integer) commonSecurityService.securityResult((String) dataId);
            if (i == 1) {
                log.info("咪咕安审-kp查询到送审结果为成功,recordId:{},dataId:{}", recordId, dataId);
                update(new LambdaUpdateWrapper<KpAiUnionRecord>()
                        .set(KpAiUnionRecord::getVideoStatus, 1)
                        .eq(KpAiUnionRecord::getId, recordId));
                redisUtil.hdel(CommonSecurityService.PREFIX_KEY + "pushed:kp", dataId);
            } else if (i == -1) {
                log.error("咪咕安审-kp查询到送审结果为失败,recordId:{},dataId:{}", recordId, dataId);
                update(new LambdaUpdateWrapper<KpAiUnionRecord>()
                        .set(KpAiUnionRecord::getVideoStatus, -1)
                        .eq(KpAiUnionRecord::getId, recordId));
                redisUtil.hdel(CommonSecurityService.PREFIX_KEY + "pushed:kp", dataId);
            }
        }
        log.debug("轮询kp咪咕视频安全审核结束{}", dataIdMap);
    }

    @Override
    public Integer getRight(String userUniqueTag) {
        Integer count = SpringUtil.getBean(getClass()).getSubscribeBizCount(userUniqueTag);
        if (count == 0) {
            return 0;
        }
        return kpAiUnionRecordMapper.queryUsableCount(count, userUniqueTag);
    }

    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_RIGHT, key = "#root.methodName+':'+#userUniqueTag")
    @Override
    public Integer getSubscribeBizCount(String userUniqueTag) {
        int count = 0;
        if (miguApiService.schQuery(userUniqueTag, MiguApiService.BIZ_SCH_CHANNEL_CODE_YTCY_KP_MM).isSchMember()) {
            count += 12;
        } else if (miguApiService.schQuery(userUniqueTag, MiguApiService.BIZ_SCH_CHANNEL_CODE_YTCY_KP_MP).isSchMember()) {
            count += 5;
        } else if (miguApiService.schQuery(userUniqueTag, MiguApiService.BIZ_SCH_CHANNEL_CODE_YTCY_KP_MN).isSchMember()) {
            count += 12;
        } else if (miguApiService.schQuery(userUniqueTag, MiguApiService.BIZ_SCH_CHANNEL_CODE_YTCY_KP_MO).isSchMember()) {
            count += 12;
        } else if (miguApiService.schQuery(userUniqueTag, MiguApiService.BIZ_SCH_CHANNEL_CODE_YTCY_KP_W).isSchMember()) {
            count += 12;
        } else if (miguApiService.schQuery(userUniqueTag, MiguApiService.AI_RING_SUB_CHIANNEL).isSubMember()) {
            count += 12;
        }
        return count;
    }

    private boolean isStatusActive(String userUniqueTag, String channelCode) {
        EsSubscribe es = esDataService.findEsSubscribeByMobileAndChannel(userUniqueTag, channelCode);
        return Optional.ofNullable(es).map(e -> e.getStatus() == 1).orElse(false);
    }
}
