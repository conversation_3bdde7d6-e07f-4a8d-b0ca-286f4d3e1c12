package com.eleven.cms.aiunionkp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eleven.cms.aiunionkp.entity.KpAiUnionAiFaceTemplate;
import com.eleven.cms.aiunionkp.entity.KpAiUnionRecord;
import com.eleven.cms.aiunionkp.pojo.dto.KpAiPicAiUnionFuseFaceTaskCreateDTO;
import com.eleven.cms.aiunionkp.pojo.vo.TaskCreateResultVO;
import com.eleven.cms.aiunionkp.pojo.vo.TaskResultQueryVO;
import com.eleven.cms.aiunionkp.service.KpIAiUnionAiFaceTemplateService;
import com.eleven.cms.aiunionkp.service.KpIAiUnionRecordService;
import com.eleven.cms.aiunionkp.service.KpIAiUnionService;
import com.eleven.cms.aiunionkp.utils.TencentCloudAIUtils;
import com.eleven.cms.aivrbt.dto.PicFuseFaceDTO;
import com.eleven.cms.aivrbt.dto.PicFuseFaceMergeInfoDTO;
import com.eleven.cms.config.RabbitMQConfig;
import com.eleven.cms.util.JacksonUtils;
import com.eleven.qycl.config.AliMediaProperties;
import com.eleven.qycl.service.AliMediaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtil;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ai联盟服务实现类
 *
 * <AUTHOR>
 * @datetime 2024/11/20 17:41
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KpAiUnionServiceImpl implements KpIAiUnionService {

    public static String AI_TAG = "ai联盟hstj";

    private final KpIAiUnionRecordService kpAiUnionRecordService;

    private final KpIAiUnionAiFaceTemplateService aiUnionAiFaceTemplateService;

    private final AliMediaService aliMediaService;


    @Autowired
    @Lazy
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 查询任务创作结果
     *
     * @param taskId 任务ID，用于标识特定的任务创作结果
     * @return 返回任务创作结果的对象，如果找不到对应的任务则返回null
     */
    @Override
    public Object queryTaskResult(String taskId) {
        KpAiUnionRecord one = kpAiUnionRecordService.getOne(new LambdaQueryWrapper<KpAiUnionRecord>()
                .eq(KpAiUnionRecord::getId, taskId));
        if (one != null) {
            TaskResultQueryVO taskResultQueryVO = new TaskResultQueryVO();
            taskResultQueryVO.setId(one.getId());
            taskResultQueryVO.setTemplateName(one.getTemplateName());
            taskResultQueryVO.setRingUrl(one.getVideoUrl());
            taskResultQueryVO.setRingMakeStatus(one.getVideoStatus());
            taskResultQueryVO.setOriginalUrl(one.getOriginalUrl());
            taskResultQueryVO.setTemplateName(one.getTemplateName());
            taskResultQueryVO.setTemplateRemark(one.getTemplateRemark());
            taskResultQueryVO.setVideoPicUrl(one.getVideoPicUrl());
            return taskResultQueryVO;
        }
        return null;
    }


    /**
     * 任务创作-图片换脸
     *
     * @param aiPicFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO
     * @return String 视频ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object createFacePicFusionTask(KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        TaskCreateResultVO resultVO = new TaskCreateResultVO();

        //根据主题策略模板选择
        templateChooseStrategy(aiPicFuseFaceTaskCreateDTO, resultVO);

        KpAiUnionRecord record = new KpAiUnionRecord();
        record.setUserUniqueTag(aiPicFuseFaceTaskCreateDTO.getUserUniqueTag());
        record.setFaceTemplateId(aiPicFuseFaceTaskCreateDTO.getTemplateId());
        record.setOriginalUrl(aiPicFuseFaceTaskCreateDTO.getUrl());
        record.setTopicType(aiPicFuseFaceTaskCreateDTO.getTopicType());
        kpAiUnionRecordService.save(record);
        resultVO.setId(record.getId());
        aiPicFuseFaceTaskCreateDTO.setRecordId(record.getId());
        //缓存任务参数
        // 咪咕预扣减次数
//        miguAIGCApiService.preReduceCount(aiPicFuseFaceTaskCreateDTO.getToken(), aiPicFuseFaceTaskCreateDTO.getMiguTaskId());
        //任务异步制作
        sendMakingMessage(aiPicFuseFaceTaskCreateDTO);
        return resultVO;
    }

    private void templateChooseStrategy(KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO, TaskCreateResultVO resultVO) {
        switch (aiPicFuseFaceTaskCreateDTO.getTopicType()) {
            case 1:
                //穿越前世,根据性别随机获取模板
                String url = aiPicFuseFaceTaskCreateDTO.getUrl();
                Integer gender = TencentCloudAIUtils.parseFaceDetectionAndAnalysis(TencentCloudAIUtils.faceDetectionAndAnalysis(url));
                List<String> idList = aiUnionAiFaceTemplateService.getGenderTemplateIdList(gender);
                if (CollectionUtil.isEmpty(idList)) {
                    throw new JeecgBootException("当前服务暂不可用！");
                }
                String randomId = getRandomId(idList);
                aiPicFuseFaceTaskCreateDTO.setTemplateId(randomId);
                resultVO.setGender(gender);
                return;
            case 2:
                //穿越财神,指定模板
                if (StrUtil.isBlank(aiPicFuseFaceTaskCreateDTO.getTemplateId())) {
                    throw new JeecgBootException("请选择主题模板");
                }
                return;
            default:
                throw new JeecgBootException("活动主题类型不存在！,请刷新重试");
        }
    }

    private void sendMakingMessage(KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {
        try {
            rabbitTemplate.convertAndSend(RabbitMQConfig.TX_FACE_KP_ACROSS_QUEUE_NAME, aiPicFuseFaceTaskCreateDTO, new CorrelationData(aiPicFuseFaceTaskCreateDTO.getRecordId()));
        } catch (Exception e) {
            kpAiUnionRecordService.update(new LambdaUpdateWrapper<KpAiUnionRecord>()
                    .set(KpAiUnionRecord::getVideoStatus, -1)
                    .eq(KpAiUnionRecord::getId, aiPicFuseFaceTaskCreateDTO.getRecordId()));
            log.error("{}-腾讯云ai穿越图片换脸MQ发送消息异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getUserUniqueTag(), e);
        }
    }

//    @Override
//    public void handleMiguTaskResult(String recordId, Boolean result, String videoUrl) {
//        String param = null;
//        try {
//            param = (String) redisUtil.get(AIUNION_TASK_PARAM_KEY_PREFIX + recordId);
//            if (StrUtil.isNotBlank(param)) {
//                ReportResultDTO reportResultDTO;
//                AiPicAiUnionFuseFaceTaskCreateDTO paramDTO = JacksonUtils.readValue(param, AiPicAiUnionFuseFaceTaskCreateDTO.class);
//                if (result) {
//                    reportResultDTO = ReportResultDTO.builder().result(true)
//                            .finalResults(Arrays.asList(ContentItemDTO.builder()
//                                    .content(videoUrl)
//                                    .contentType("video")
//                                    .build()))
//                            .inputContents(Arrays.asList(ContentItemDTO.builder()
//                                    .content(paramDTO.getUrl())
//                                    .contentType("image")
//                                    .build()))
//                            .build();
//                } else {
//                    reportResultDTO = ReportResultDTO.builder().result(false)
//                            .inputContents(Arrays.asList(ContentItemDTO.builder()
//                                    .content(paramDTO.getUrl())
//                                    .contentType("image")
//                                    .build()))
//                            .build();
//                }
//                miguAIGCApiService.reportResult(paramDTO.getToken(), paramDTO.getMiguTaskId(), reportResultDTO);
//                if (!result) {
//                    miguAIGCApiService.cancelTask(paramDTO.getToken(), paramDTO.getMiguTaskId());
//                }
//                redisUtil.del(AIUNION_TASK_PARAM_KEY_PREFIX + recordId);
//            } else {
//                log.error("{},获取任务参数失败，参数为空，recordId:{}", AI_TAG, recordId);
//            }
//        } catch (Exception e) {
//            log.error("{},上报咪咕制作结果失败,参数：{},失败原因{}", AI_TAG, param, e.getMessage(), e);
//        }
//    }

    /**
     * 根据用户唯一标签检查任务是否存在，并根据检查结果决定是否取消任务
     *
     * @param userUniqueTag 用户唯一标签，用于识别用户
     * @param token         用于调用API的令牌
     * @param taskId        需要检查的任务ID
     * @return 如果任务存在且未被取消，则返回true；否则返回false
     */
//    @Override
//    public Boolean checkTaskAndCancel(String userUniqueTag, String token, String taskId) {
//        boolean checkResult = false;
//
//        AiUnionRecord aiUnionRecord = aiUinionRecordService.getOne(new QueryWrapper<AiUnionRecord>()
//                .eq("user_unique_tag", userUniqueTag)
//                .orderByDesc("id")
//                .last("limit 1"));
//        if (Objects.nonNull(aiUnionRecord)) {
//            String param = (String) redisUtil.get(AIUNION_TASK_PARAM_KEY_PREFIX + aiUnionRecord.getId());
//            if (StrUtil.isNotBlank(param)) {
//                AiPicAiUnionFuseFaceTaskCreateDTO paramDTO = JacksonUtils.readValue(param, AiPicAiUnionFuseFaceTaskCreateDTO.class);
//                if (Objects.nonNull(paramDTO)) {
//                    String miguTaskId = paramDTO.getMiguTaskId();
//                    if (miguTaskId.equals(taskId)) {
//                        checkResult = true;
//                    }
//                }
//            }
//        }
//
//        if (!checkResult) {
//            miguAIGCApiService.cancelTask(token, taskId);
//        }
//        return checkResult;
//    }

    /**
     * 处理消息队列消息 创作任务
     *
     * @param aiPicFuseFaceTaskCreateDTO 任务消息体
     */
    @Override
    public void handleCreateTaskMsg(KpAiPicAiUnionFuseFaceTaskCreateDTO aiPicFuseFaceTaskCreateDTO) {

        try {
            KpAiUnionAiFaceTemplate template = aiUnionAiFaceTemplateService.getById(aiPicFuseFaceTaskCreateDTO.getTemplateId());
            if (template == null) {
                throw new JeecgBootException("模板不存在");
            }
            String[] materialIdList = template.getMaterialId().split(",");

            final Map<String, Object> extraMap = new HashMap<String, Object>() {
                {
                    put("手机号", aiPicFuseFaceTaskCreateDTO.getUserUniqueTag());
                }
            };


            List<String> fuseFaceUrlList = ListUtil.toList(materialIdList).stream()
                    .map(materialId -> {
                        PicFuseFaceDTO faceDTO = new PicFuseFaceDTO();
                        faceDTO.setProjectId(template.getActivityId());
                        faceDTO.setModelId(materialId);
                        PicFuseFaceMergeInfoDTO mergeInfoDTO = new PicFuseFaceMergeInfoDTO();
                        mergeInfoDTO.setUrl(aiPicFuseFaceTaskCreateDTO.getUrl());
                        //默认取第一张脸
                        mergeInfoDTO.setTemplateFaceID(materialId + "_1");
                        faceDTO.setMergeInfos(ListUtil.toList(mergeInfoDTO));
                        return TencentCloudAIUtils.parseFuseFaceResponse(TencentCloudAIUtils.fuseFace(faceDTO, extraMap));
                    })
                    .map(txUrl -> {
                        //存储阿里云合成人脸图片
                        String filePath = "tx" + "/" + IdWorker.get32UUID() + ".jpg";
                        try {
                            return aliMediaService.putObjectRemoteUrl(filePath, txUrl);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).collect(Collectors.toList());


            // 7.获取图片数据构造clipsParam
            LinkedHashMap<String, String> clipsParamMap = JacksonUtils.readValue(template.getClipsParam(), LinkedHashMap.class);

            int index = 0;
            for (Map.Entry<String, String> entry : clipsParamMap.entrySet()) {
                if (index < fuseFaceUrlList.size()) {
                    entry.setValue(fuseFaceUrlList.get(index));
                    index++;
                } else {
                    break; // 如果 fuseFaceUrlList 的长度小于 clipsParamMap 的大小，停止匹配
                }
            }

            // 2.合成视频
            String jobId = aliMediaService.mergeTemplate(AliMediaProperties.JOB_QUEUE_TAG_TXAI_ACROSS_FACE, template.getTemplateId(), JacksonUtils.toJson(clipsParamMap));
           redisUtil.set("cms:cache:aiunion:jobBusiness:"+jobId,"kp");
            KpAiUnionRecord record = new KpAiUnionRecord();
            record.setId(aiPicFuseFaceTaskCreateDTO.getRecordId());
            record.setVideoPicUrl(fuseFaceUrlList.get(0));
            record.setConvertUrls(String.join(",", fuseFaceUrlList));
            record.setTaskId(jobId);
            record.setTemplateName(template.getTemplateName());
            record.setTemplateRemark(template.getTemplateRemark());
            kpAiUnionRecordService.updateById(record);
        } catch (Exception e) {
            kpAiUnionRecordService.update(new LambdaUpdateWrapper<KpAiUnionRecord>()
                    .set(KpAiUnionRecord::getVideoStatus, -1)
                    .eq(KpAiUnionRecord::getId, aiPicFuseFaceTaskCreateDTO.getRecordId()));
            log.error("{}-腾讯云ai穿越图片换脸消息处理异常!,手机号:{}", AI_TAG, aiPicFuseFaceTaskCreateDTO.getUserUniqueTag(), e);
        }
    }

    private String getRandomId(List<String> ids) {
        Random random = new Random();
        int randomIndex = random.nextInt(ids.size());
        return ids.get(randomIndex);
    }
}
