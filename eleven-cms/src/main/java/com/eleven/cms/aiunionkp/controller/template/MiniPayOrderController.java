package com.eleven.cms.aiunionkp.controller.template;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunionkp.entity.MiniPayOrder;
import com.eleven.cms.aiunionkp.service.IMiniPayOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: mini_pay_order
 * @Author: jeecg-boot
 * @Date: 2025-06-04
 * @Version: V1.0
 */
@Api(tags = "mini_pay_order")
@RestController
@RequestMapping("/cms/miniPayOrder")
@Slf4j
public class MiniPayOrderController extends JeecgController<MiniPayOrder, IMiniPayOrderService> {
    @Autowired
    private IMiniPayOrderService miniPayOrderService;

    /**
     * 分页列表查询
     *
     * @param miniPayOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "mini_pay_order-分页列表查询")
    @ApiOperation(value = "mini_pay_order-分页列表查询", notes = "mini_pay_order-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniPayOrder miniPayOrder,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<MiniPayOrder> queryWrapper = QueryGenerator.initQueryWrapper(miniPayOrder, req.getParameterMap());
        Page<MiniPayOrder> page = new Page<MiniPayOrder>(pageNo, pageSize);
        IPage<MiniPayOrder> pageList = miniPayOrderService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param miniPayOrder
     * @return
     */
    @AutoLog(value = "mini_pay_order-添加")
    @ApiOperation(value = "mini_pay_order-添加", notes = "mini_pay_order-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniPayOrder miniPayOrder) {
        miniPayOrderService.save(miniPayOrder);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miniPayOrder
     * @return
     */
    @AutoLog(value = "mini_pay_order-编辑")
    @ApiOperation(value = "mini_pay_order-编辑", notes = "mini_pay_order-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniPayOrder miniPayOrder) {
        miniPayOrderService.updateById(miniPayOrder);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_pay_order-通过id删除")
    @ApiOperation(value = "mini_pay_order-通过id删除", notes = "mini_pay_order-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniPayOrderService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "mini_pay_order-批量删除")
    @ApiOperation(value = "mini_pay_order-批量删除", notes = "mini_pay_order-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.miniPayOrderService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "mini_pay_order-通过id查询")
    @ApiOperation(value = "mini_pay_order-通过id查询", notes = "mini_pay_order-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniPayOrder miniPayOrder = miniPayOrderService.getById(id);
        if (miniPayOrder == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniPayOrder);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniPayOrder
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MiniPayOrder miniPayOrder) {
        return super.exportXls(request, miniPayOrder, MiniPayOrder.class, "mini_pay_order");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniPayOrder.class);
    }

}
