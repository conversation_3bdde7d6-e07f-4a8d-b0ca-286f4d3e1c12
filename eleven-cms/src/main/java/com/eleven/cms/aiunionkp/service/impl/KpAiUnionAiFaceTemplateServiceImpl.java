package com.eleven.cms.aiunionkp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.aiunionkp.entity.KpAiUnionAiFaceTemplate;
import com.eleven.cms.aiunionkp.mapper.KpAiUnionAiFaceTemplateMapper;
import com.eleven.cms.aiunionkp.service.KpIAiUnionAiFaceTemplateService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: ai_union_ai_face_template
 * @Author: jeecg-boot
 * @Date: 2024-11-20
 * @Version: V1.0
 */
@Service
public class KpAiUnionAiFaceTemplateServiceImpl extends ServiceImpl<KpAiUnionAiFaceTemplateMapper, KpAiUnionAiFaceTemplate> implements KpIAiUnionAiFaceTemplateService {


    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE, key = "#root.methodName + ':' + #gender")
    @Override
    public List<String> getGenderTemplateIdList(Integer gender) {
        LambdaQueryWrapper<KpAiUnionAiFaceTemplate> queryWrapper = new LambdaQueryWrapper<KpAiUnionAiFaceTemplate>()
                .select(KpAiUnionAiFaceTemplate::getId).eq(KpAiUnionAiFaceTemplate::getTemplateType, gender)
                .eq(KpAiUnionAiFaceTemplate::getTopicType, 1)
                .eq(KpAiUnionAiFaceTemplate::getStatus, 1);
        List<KpAiUnionAiFaceTemplate> list = super.list(queryWrapper);
        return list.stream().map(KpAiUnionAiFaceTemplate::getId).collect(Collectors.toList());
    }

    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE, key = "#root.methodName + ':' + #topicType")
    @Override
    public List<KpAiUnionAiFaceTemplate> getTopicTypeTemplateIdList(Integer topicType) {
        LambdaQueryWrapper<KpAiUnionAiFaceTemplate> queryWrapper = new LambdaQueryWrapper<KpAiUnionAiFaceTemplate>()
                .eq(KpAiUnionAiFaceTemplate::getTopicType, topicType)
                .eq(KpAiUnionAiFaceTemplate::getStatus, 1)
                .orderByAsc(KpAiUnionAiFaceTemplate::getOrderBy)
                .orderByAsc(KpAiUnionAiFaceTemplate::getId);
        return super.list(queryWrapper);
    }
    @Override
    @Cacheable(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE, key = "#root.methodName + ':' + #id")
    public KpAiUnionAiFaceTemplate getById(Serializable id) {
        return super.getById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean save(KpAiUnionAiFaceTemplate kpAiUnionAiFaceTemplate) {
        return super.save(kpAiUnionAiFaceTemplate);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean updateById(KpAiUnionAiFaceTemplate kpAiUnionAiFaceTemplate) {
        return super.updateById(kpAiUnionAiFaceTemplate);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }

    @CacheEvict(cacheNames = CacheConstant.CMS_CACHE_KP_AI_UNION_FACE_TEMPLATE, allEntries = true)
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return super.removeByIds(idList);
    }
}
