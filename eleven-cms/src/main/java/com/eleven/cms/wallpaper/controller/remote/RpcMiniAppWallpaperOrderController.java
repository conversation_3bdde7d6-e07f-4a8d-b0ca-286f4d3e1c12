package com.eleven.cms.wallpaper.controller.remote;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.wallpaper.entity.MiniAppWallpaperOrder;
import com.eleven.cms.wallpaper.service.IMiniAppWallpaperOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: mini_app_wallpaper_order
 * @Author: jeecg-boot
 * @Date: 2025-05-12
 * @Version: V1.0
 */
@Api(tags = "mini_app_wallpaper_order")
@RestController
@RequestMapping("/api/rpc/wallpaper/miniAppWallpaperOrder")
@Slf4j
public class RpcMiniAppWallpaperOrderController extends JeecgController<MiniAppWallpaperOrder, IMiniAppWallpaperOrderService> {
    @Autowired
    private IMiniAppWallpaperOrderService miniAppWallpaperOrderService;

    /**
     * 分页列表查询
     *
     * @param miniAppWallpaperOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "mini_app_wallpaper_order-分页列表查询", notes = "mini_app_wallpaper_order-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MiniAppWallpaperOrder miniAppWallpaperOrder,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<MiniAppWallpaperOrder> queryWrapper = QueryGenerator.initQueryWrapper(miniAppWallpaperOrder, req.getParameterMap());
        Page<MiniAppWallpaperOrder> page = new Page<MiniAppWallpaperOrder>(pageNo, pageSize);
        IPage<MiniAppWallpaperOrder> pageList = miniAppWallpaperOrderService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     *
     * @param miniAppWallpaperOrder
     * @return
     */
    @ApiOperation(value = "mini_app_wallpaper_order-添加", notes = "mini_app_wallpaper_order-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiniAppWallpaperOrder miniAppWallpaperOrder) {
        miniAppWallpaperOrderService.save(miniAppWallpaperOrder);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miniAppWallpaperOrder
     * @return
     */
    @ApiOperation(value = "mini_app_wallpaper_order-编辑", notes = "mini_app_wallpaper_order-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiniAppWallpaperOrder miniAppWallpaperOrder) {
        miniAppWallpaperOrderService.updateById(miniAppWallpaperOrder);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "mini_app_wallpaper_order-通过id删除", notes = "mini_app_wallpaper_order-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miniAppWallpaperOrderService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "mini_app_wallpaper_order-批量删除", notes = "mini_app_wallpaper_order-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.miniAppWallpaperOrderService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "mini_app_wallpaper_order-通过id查询", notes = "mini_app_wallpaper_order-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiniAppWallpaperOrder miniAppWallpaperOrder = miniAppWallpaperOrderService.getById(id);
        if (miniAppWallpaperOrder == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(miniAppWallpaperOrder);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param miniAppWallpaperOrder
     */
    @RequestMapping(value = "/exportXls")
    public Result<?> exportXls(HttpServletRequest request, MiniAppWallpaperOrder miniAppWallpaperOrder) {
        byte[] bytes = super.exportXlsRemote(request, miniAppWallpaperOrder, MiniAppWallpaperOrder.class, "mini_app_wallpaper_order");
        return Result.ok(bytes);
    }

    /**
     * 通过excel导入数据
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MiniAppWallpaperOrder.class);
    }
}
