package com.eleven.cms.wallpaper.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO;
import com.eleven.cms.wallpaper.entity.AppComponentWallpaper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: app_component_wallpaper
 * @Author: jeecg-boot
 * @Date:   2025-03-12
 * @Version: V1.0
 */
public interface AppComponentWallpaperMapper extends BaseMapper<AppComponentWallpaper> {

    Page<AppComponentWallpaperVO> getWallpapersByColumnId(Page<AppComponentWallpaperVO> page, @Param("columnId") String columnId);
    Page<AppComponentWallpaperVO> listFavorite(Page<AppComponentWallpaperVO> page, @Param("openId") String openId);
    Page<AppComponentWallpaperVO> orderList(Page<AppComponentWallpaperVO> page, @Param("openId") String openId);
}
