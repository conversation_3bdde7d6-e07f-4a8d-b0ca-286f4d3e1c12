package com.eleven.cms.wallpaper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: app_component_category_relation
 * @Author: jeecg-boot
 * @Date:   2025-03-12
 * @Version: V1.0
 */
@Data
@TableName("app_component_category_relation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="app_component_category_relation对象", description="app_component_category_relation")
public class AppComponentCategoryRelation implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**关联的分类id*/
	@Excel(name = "关联的分类id", width = 15)
    @ApiModelProperty(value = "关联的分类id")
    private String categoryId;
	/**分类类型 1-桌面组件分类 2-静态壁纸分类，3-动态壁纸分类*/
	@Excel(name = "分类类型 1-桌面组件分类 2-静态壁纸分类，3-动态壁纸分类", width = 15)
    @ApiModelProperty(value = "分类类型 1-桌面组件分类 2-静态壁纸分类，3-动态壁纸分类")
    private Integer categoryType;
	/**组件id*/
	@Excel(name = "组件id", width = 15)
    @ApiModelProperty(value = "组件id")
    private String componentId;
	/**逻辑删除 0:未删除 1:已删除*/
	@Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
