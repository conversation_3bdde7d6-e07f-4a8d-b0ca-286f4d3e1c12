<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eleven.cms.wallpaper.mapper.AppComponentWallpaperMapper">

    <resultMap id="baseMap" type="com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="category" column="category"/>
        <result property="videoUrl" column="video_url"/>
        <result property="functionType" column="function_type"/>
        <result property="appStyleCode" column="app_style_code"/>
        <result property="userRange" column="user_range"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="status" column="status"/>
        <result property="orderNum" column="order_num"/>
        <result property="adUnlockFlag" column="ad_unlock_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="listWallpapersByColumnId" resultType="com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO">
        select

        from
            app_component_category ca left join   app_component_wallpaper t on ca.wallpaper_id = t.id
        <where>
            <if  test="columnId != null and columnId !=''">
                ca.category_id = #{columnId}
            </if>
        </where>
    </select>

    <select id="listFavorite" resultType="com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO">
        select
        *
        from
        wallpaper_favorite ca inner join   app_component_wallpaper t on ca.wallpaper_id = t.id
        where ca.is_deleted =0
        <if test="openId != null and openId !=''">
            and ca.open_id = #{openId}
        </if>
    </select>

    <select id="orderList" resultType="com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO">
        select
        t.*,
        wa.order_amount as orderAmount,
        DATE_FORMAT(wa.pay_time, '%Y-%m-%d') as payTime
        from
        mini_app_wallpaper_order wa inner join   app_component_wallpaper t on wa.wallpaper_id = t.id
        where wa.pay_status = 1
        <if test="openId != null and openId !=''">
            and wa.open_id = #{openId}
        </if>
        order by wa.pay_time desc
    </select>
</mapper>
