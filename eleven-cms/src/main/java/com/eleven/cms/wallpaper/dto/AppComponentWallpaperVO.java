package com.eleven.cms.wallpaper.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "AppComponentWallpaperVO", description = "壁纸信息视图对象")
public class AppComponentWallpaperVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "组件名称")
    private String name;

    @ApiModelProperty(value = "1:静态壁纸 2:动态壁纸")
    private Integer category;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "功能类型")
    private Integer functionType;

    @ApiModelProperty(value = "与APP约定的参数码")
    private String appStyleCode;

    @ApiModelProperty(value = "用户范围")
    private Integer userRange;

    @ApiModelProperty(value = "封面图片")
    private String coverUrl;

    @ApiModelProperty(value = "状态：0:无效 1:有效")
    private Integer status;

    @ApiModelProperty(value = "排序码")
    private Integer orderNum;

    @ApiModelProperty(value = "广告解锁：0：不支持 1：支持")
    private Integer adUnlockFlag;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    private BigDecimal orderAmount;
    private String payTime;
}
