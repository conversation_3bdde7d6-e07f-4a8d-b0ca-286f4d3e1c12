package com.eleven.cms.wallpaper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: mini_app_wallpaper_order
 * @Author: jeecg-boot
 * @Date: 2025-05-12
 * @Version: V1.0
 */
@Data
@TableName("mini_app_wallpaper_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mini_app_wallpaper_order对象", description = "mini_app_wallpaper_order")
public class MiniAppWallpaperOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 订单编码
     */
    @Excel(name = "订单编码", width = 15)
    @ApiModelProperty(value = "订单编码")
    private String orderNo;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 流水号
     */
    @Excel(name = "流水号", width = 15)
    @ApiModelProperty(value = "流水号")
    private String tranNo;
    /**
     * 微信openId
     */
    @Excel(name = "微信openId", width = 15)
    @ApiModelProperty(value = "微信openId")
    private String openId;
    /**
     * 壁纸id
     */
    @Excel(name = "壁纸id", width = 15)
    @ApiModelProperty(value = "壁纸id")
    private String wallpaperId;
    /**
     * 壁纸名称
     */
    @Excel(name = "壁纸名称", width = 15)
    @ApiModelProperty(value = "壁纸名称")
    private String wallpaperName;
    /**
     * 订单金额
     */
    @Excel(name = "订单金额", width = 15)
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;
    /**
     * 支付方式 1: 支付宝，2微信
     */
    @Excel(name = "支付方式 1: 支付宝，2微信", width = 15)
    @ApiModelProperty(value = "支付方式 1: 支付宝，2微信")
    @Dict(dicCode = "mini_app_wallpaper_pay_type")
    private Integer payType;
    /**
     * 下单时间
     */
    @Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    /**
     * 支付时间
     */
    @Excel(name = "支付时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    private Integer userCode;
    /**
     * 支付状态
     */
    @Excel(name = "支付状态", width = 15)
    @ApiModelProperty(value = "支付状态")
    @Dict(dicCode = "mini_app_wallpaper_pay_status")
    private Integer payStatus;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
