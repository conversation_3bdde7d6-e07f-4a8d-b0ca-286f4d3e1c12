package com.eleven.cms.wallpaper.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eleven.cms.aiunion.entity.PayOrders;
import com.eleven.cms.aiunion.service.IPayOrdersService;
import com.eleven.cms.dto.WechatMiniAppPayParam;
import com.eleven.cms.dto.WechatPayResult;
import com.eleven.cms.enums.PayBusineesTypeEnum;
import com.eleven.cms.enums.PayStatueEnum;
import com.eleven.cms.service.pay.IWechatPayAppService;
import com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO;
import com.eleven.cms.wallpaper.dto.OrderReq;
import com.eleven.cms.wallpaper.entity.AppComponentWallpaper;
import com.eleven.cms.wallpaper.entity.MiniAppWallpaperOrder;
import com.eleven.cms.wallpaper.entity.MiniAppWechatUser;
import com.eleven.cms.wallpaper.service.IAppComponentWallpaperService;
import com.eleven.cms.wallpaper.service.IMiniAppWallpaperOrderService;
import com.eleven.cms.wallpaper.service.IMiniAppWechatUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/wallpaper")
@Slf4j
@Validated
public class WallpaperInfoController {

    @Resource
    private IAppComponentWallpaperService wallpaperService;

    @Resource
    IPayOrdersService payOrdersService;

    @Resource
    IWechatPayAppService wechatPayAppService;

    @Resource
    IMiniAppWallpaperOrderService iMiniAppWallpaperOrderService;


    @Resource
    RedisUtil redisUtil;

    @Resource
    IMiniAppWechatUserService miniAppWechatUserService;

    @GetMapping("/listFavorite")
    public Result<Object> getFavorite(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String openId) {
        Page<AppComponentWallpaperVO> page = new Page<>(pageNum, pageSize);

        Page<AppComponentWallpaperVO> result = wallpaperService.listFavorite(page, openId);
        return Result.ok(result);
    }

    @GetMapping("/orderList")
    public Result<Object> payList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String openId) {
        Page<AppComponentWallpaperVO> page = new Page<>(pageNum, pageSize);

        Page<AppComponentWallpaperVO> result = wallpaperService.orderList(page, openId);
        return Result.ok(result);
    }

    @GetMapping("/getPayStatus")
    public Result<Object> getPayStatus(@RequestParam("openId") String openId, @RequestParam("wallPaperId") String wallPaperId) {
        List<MiniAppWallpaperOrder> appWallpaperOrders = iMiniAppWallpaperOrderService.lambdaQuery().eq(MiniAppWallpaperOrder::getWallpaperId, wallPaperId)
                .eq(MiniAppWallpaperOrder::getOpenId, openId).eq(MiniAppWallpaperOrder::getPayStatus, 1).list();
        if (CollectionUtil.isNotEmpty(appWallpaperOrders)) {
            return Result.ok("Paid",Boolean.TRUE);
        } else {
            return Result.ok("Unpaid",Boolean.FALSE);
        }
    }


    @GetMapping("/getWallpaperAmount")
    public Result<Object> getWallpaperAmount() {
        String amount = (String) redisUtil.get("cms:cache:wallpaper:amount");
        BigDecimal result = new BigDecimal(amount);
        return Result.ok(result);
    }

    @PostMapping("/createOrder")
    public Result<Object> creatOrder(@RequestBody OrderReq orderReq, HttpServletRequest request) {

        log.info("创建订单请求参数：{}", orderReq);
        String orderNo = IdWorker.get32UUID();
        AppComponentWallpaper wallpaper = wallpaperService.getById(orderReq.getWallpaperId());
        if (Objects.isNull(wallpaper)) {
            return Result.error("壁纸不存在");
        }
        String amount = (String) redisUtil.get("cms:cache:wallpaper:amount");
        if (StringUtils.isEmpty(amount)) {
            amount = "1";
        }
        BigDecimal payAmount = new BigDecimal(amount).multiply(new BigDecimal(100));

        WechatMiniAppPayParam payParam = WechatMiniAppPayParam.builder().outTradeNo(orderNo)
                .businessType(PayBusineesTypeEnum.WALLPAPER.getType())
                .amount(payAmount.intValue())
                .orderNo(orderNo)
                .openId(orderReq.getOpenId())
                .build();

        WechatPayResult wechatPayResult = wechatPayAppService.wechatMiniAppPay(payParam);
        savePayOrders(new BigDecimal(orderReq.getAmount()), orderNo, 2, JSONObject.toJSONString(wechatPayResult));

        MiniAppWechatUser miniAppWechatUser = miniAppWechatUserService.lambdaQuery().eq(MiniAppWechatUser::getOpenId, orderReq.getOpenId()).last("limit 1").one();

        MiniAppWallpaperOrder order = new MiniAppWallpaperOrder();
        if (miniAppWechatUser != null) {
            order.setUserCode(miniAppWechatUser.getId());
        }
        order.setOrderAmount(new BigDecimal(orderReq.getAmount()));
        order.setOrderNo(orderNo);
        order.setOpenId(orderReq.getOpenId());
        order.setMobile(orderReq.getMobile());
        order.setPayType(2);
        order.setWallpaperId(orderReq.getWallpaperId());
        order.setPayStatus(0);
        order.setWallpaperName(wallpaper.getName());
        order.setOrderTime(new Date());
        iMiniAppWallpaperOrderService.save(order);
        return Result.ok(wechatPayResult);
    }


    public void savePayOrders(BigDecimal amount, String orderNo, Integer payType, String responseJson) {
        PayOrders payOrders = new PayOrders();
        payOrders.setBusinessType(PayBusineesTypeEnum.WALLPAPER.getType());
        payOrders.setPayDesc("壁纸支付");
        payOrders.setOrderNo(orderNo);
        payOrders.setAmount(amount);
        payOrders.setPayTime(new Date());
        payOrders.setPayStatus(PayStatueEnum.UNPAID.getPayType());
        payOrders.setPaymentType(payType);
        payOrders.setResponseJson(responseJson);
        payOrdersService.save(payOrders);
    }
}
