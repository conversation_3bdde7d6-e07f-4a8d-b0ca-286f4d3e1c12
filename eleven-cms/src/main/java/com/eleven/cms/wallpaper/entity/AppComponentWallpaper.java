package com.eleven.cms.wallpaper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: app_component_wallpaper
 * @Author: jeecg-boot
 * @Date:   2025-03-12
 * @Version: V1.0
 */
@Data
@TableName("app_component_wallpaper")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="app_component_wallpaper对象", description="app_component_wallpaper")
public class AppComponentWallpaper implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
	/**组件名称*/
	@Excel(name = "组件名称", width = 15)
    @ApiModelProperty(value = "组件名称")
    private String name;
	/**1:静态壁纸 2:动态壁纸*/
	@Excel(name = "1:静态壁纸 2:动态壁纸", width = 15, dicCode = "app_wallpaper_type")
	@Dict(dicCode = "app_wallpaper_type")
    @ApiModelProperty(value = "1:静态壁纸 2:动态壁纸")
    private Integer category;

    private String videoUrl;

    @Dict(dicCode = "app_wallpaper_function_type")
    private Integer functionType;

    /**
     * 与app约定的参数码
     */
    private String appStyleCode;
    /**
     * UserRangeEnum
     */
    private Integer userRange;
	/**封面图片*/
	@Excel(name = "封面图片", width = 15)
    @ApiModelProperty(value = "封面图片")
    private String coverUrl;
	/**状态：0:无效 1:有效*/
	@Excel(name = "状态：0:无效 1:有效", width = 15, dicCode = "available_status")
	@Dict(dicCode = "available_status")
    @ApiModelProperty(value = "状态：0:无效 1:有效")
    private Integer status;
	/**排序码*/
	@Excel(name = "排序码", width = 15)
    @ApiModelProperty(value = "排序码")
    private Integer orderNum;
	/**广告解锁：0：不支持  1：支持*/
	@Excel(name = "广告解锁：0：不支持  1：支持", width = 15)
    @ApiModelProperty(value = "广告解锁：0：不支持  1：支持")
    private Integer adUnlockFlag;
	/**逻辑删除 0:未删除 1:已删除*/
	@Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    private List<String> categoryIds;

}
