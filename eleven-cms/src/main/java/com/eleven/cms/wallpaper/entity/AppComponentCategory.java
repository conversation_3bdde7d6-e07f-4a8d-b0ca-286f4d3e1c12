package com.eleven.cms.wallpaper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
@TableName("app_component_category")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "app_component_category对象", description = "app_component_category")
public class AppComponentCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 分类名称
     */
    @Excel(name = "分类名称", width = 15)
    @ApiModelProperty(value = "分类名称")
    private String name;
    /**
     * 0:无效 1:有效
     */
    @Excel(name = "0:无效 1:有效", width = 15)
    @ApiModelProperty(value = "0:无效 1:有效")
    private Integer status;
    /**
     * 分类类型 1-桌面组件分类 2-静态壁纸分类，3-动态壁纸分类
     */
    @Excel(name = "分类类型 1-桌面组件分类 2-静态壁纸分类，3-动态壁纸分类", width = 15)
    @ApiModelProperty(value = "分类类型 1-桌面组件分类 2-静态壁纸分类，3-动态壁纸分类")
    private Integer categoryType;
    /**
     * 分类图片
     */
    @Excel(name = "分类图片", width = 15)
    @ApiModelProperty(value = "分类图片")
    private String picUrl;
    /**
     * 排序码
     */
    @Excel(name = "排序码", width = 15)
    @ApiModelProperty(value = "排序码")
    private Integer orderNum;
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @Excel(name = "逻辑删除 0:未删除 1:已删除", width = 15)
    @ApiModelProperty(value = "逻辑删除 0:未删除 1:已删除")
    private Integer isDeleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
