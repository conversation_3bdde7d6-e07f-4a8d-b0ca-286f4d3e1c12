package com.eleven.cms.wallpaper.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO;
import com.eleven.cms.wallpaper.entity.AppComponentWallpaper;
import com.eleven.cms.wallpaper.mapper.AppComponentWallpaperMapper;
import com.eleven.cms.wallpaper.service.IAppComponentWallpaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class AppComponentWallpaperServiceImpl extends ServiceImpl<AppComponentWallpaperMapper, AppComponentWallpaper> implements IAppComponentWallpaperService {
    @Resource
    private AppComponentWallpaperMapper wallpaperMapper;
    /**
     * 根据栏目ID获取壁纸列表
     *
     * @param page 分页对象，用于封装分页信息和查询结果
     * @param columnId 栏目ID，用于指定查询的栏目
     * @return 返回一个分页对象，其中包含查询到的壁纸信息列表
     */
    public Page<AppComponentWallpaperVO> getWallpapersByColumnId(Page<AppComponentWallpaperVO> page, String columnId) {
        return wallpaperMapper.getWallpapersByColumnId(page, columnId);
    }

    public Page<AppComponentWallpaperVO> listFavorite(Page<AppComponentWallpaperVO> page, String openId) {
        return wallpaperMapper.listFavorite(page, openId);
    }

    @Override
    public Page<AppComponentWallpaperVO> orderList(Page<AppComponentWallpaperVO> page, String openId) {
        return wallpaperMapper.orderList(page, openId);
    }
}
