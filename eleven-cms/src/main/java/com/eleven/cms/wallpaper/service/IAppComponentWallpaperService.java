package com.eleven.cms.wallpaper.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eleven.cms.wallpaper.dto.AppComponentWallpaperVO;
import com.eleven.cms.wallpaper.entity.AppComponentWallpaper;

public interface IAppComponentWallpaperService extends IService<AppComponentWallpaper> {

     Page<AppComponentWallpaperVO> getWallpapersByColumnId(Page<AppComponentWallpaperVO> page, String columnId);
     Page<AppComponentWallpaperVO> listFavorite(Page<AppComponentWallpaperVO> page, String openId);
     Page<AppComponentWallpaperVO> orderList(Page<AppComponentWallpaperVO> page, String openId);
}
