package com.eleven.cms.captcha;

import com.eleven.cms.captcha.fateadm.Api;
import com.eleven.cms.captcha.fateadm.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;


/**
 * Author: <EMAIL>
 * Date: 2024/4/16 16:45
 * Desc: 打码
 */
@Service
@Lazy(false)
@Slf4j
public class CaptchaService {

    public static final String PRED_TYPE_SHANDONG_HEXIAOYUAN = "30400"; //识别类型-山东和校园
    public static final String SRC_URL_SHANDONG_HEXIAOYUAN = "eduapp.sd.chinamobile.com"; //识别类型-山东和校园

    @Autowired
    private FateadmProperties fateadmProperties;
    
    private final Api fateadmApi = new Api();
    
    @PostConstruct
    public void init(){
        fateadmApi.Init(fateadmProperties.getAppId(), fateadmProperties.getAppKey(), fateadmProperties.getPdId(), fateadmProperties.getPdKey());
    }


    /**
     * 验证码识别
     * 参数： pred_type：识别类型  img_data：图片数据
     * 返回值：
     *      resp.ret_code：正常返回0
     *      resp.err_msg：异常时返回异常详情
     *      resp.req_Id：唯一订单号
     *      resp.pred_resl：识别的结果
     */
    public Util.HttpResp Predict(String pred_type, byte[] img_data, String src_url) {

        try {
            final Util.HttpResp predict = fateadmApi.Predict(pred_type, img_data, src_url);
            log.info("识别结果:{}",predict);
            return predict;
        } catch (Exception e) {
            log.warn("识别异常:",e);
            return Util.HttpResp.fail();
        }
    }

    /**
     * 文件形式进行验证码识别
     * 参数： pred_type：识别类型  file_name：文件名
     * 返回值：
     *      resp.ret_code：正常返回0
     *      resp.err_msg：异常时返回异常详情
     *      resp.req_Id：唯一订单号
     *      resp.pred_resl：识别的结果
     */
    public Util.HttpResp PredictFromFile(String pred_type, String file_name, String src_url)throws Exception{
        byte[] file_data    = Util.ReadBinaryFile(file_name);
        if( file_data == null){
            Util.HttpResp resp  = new Util.HttpResp();
            resp.ret_code       = -1;
            resp.err_msg        = "ERROR: read file failed! file_name: " + file_name;
            return resp;
        }
        return Predict(pred_type, file_data,src_url);
    }
}
