package com.eleven.cms.aspect;

import com.eleven.cms.config.ChannelOwnerProperties;
import com.eleven.cms.entity.ProvinceBusinessChannelConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.ChannelOwnerService;
import com.eleven.cms.service.IOutsideConfigService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import com.eleven.cms.service.impl.MessageNotifyService;
import com.eleven.cms.service.impl.SmsNotifyService;
import com.eleven.cms.util.BizConstant;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 业务限量aop
 *
 * @author: cai lei
 * @create: 2022-08-19 15:23
 */
@Slf4j
@Aspect
@Component
public class ProvinceChannelLimitAspect {

    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    SmsNotifyService smsNotifyService;
    @Autowired
    ChannelOwnerService channelOwnerService;
    @Autowired
    IOutsideConfigService outsideConfigService;
    @Autowired
    ChannelOwnerProperties channelOwnerProperties;
    @Autowired
    MessageNotifyService messageNotifyService;

    @Pointcut("@annotation(com.eleven.cms.annotation.ValidationLimit)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object before(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        Subscribe subscribe = (Subscribe) args[0];
        String channel = subscribe.getChannel();
        String province = subscribe.getProvince();
        String subChannel = subscribe.getSubChannel();
        //渠道省份限量校验
        boolean verifyOwnerProvinceLimit = verifyOwnerProvinceLimit(channel, province, subChannel);
        if (!verifyOwnerProvinceLimit) {
            return Result.msgAmountLimit();
        }
        //省份限量校验
        boolean verifyProvinceLimit = verifyProvinceLimit(channel, province);
        if (!verifyProvinceLimit) {
            return Result.msgAmountLimit();
        }
        //渠道总量校验
        boolean verifyTotalOwnerLimit = verifyTotalOwnerLimit(channel, subChannel);
        if (!verifyTotalOwnerLimit) {
            return Result.msgAmountLimit();
        }
        //总量校验
        boolean verifyTotalLimit = verifyTotalLimit(channel);
        if (!verifyTotalLimit) {
            return Result.msgAmountLimit();
        }
        return point.proceed();
    }

    public boolean verifyProvinceLimit(String channel, String province) {
        Integer limitAmount = provinceBusinessChannelConfigService.getLimitByChannelAndProvince(channel, province);
        if (limitAmount > 0) {
            //获取当前序列值
            Integer currentCount = subscribeService.getIncrChannelProvinceLimit(channel, province);
            if (currentCount + 20 >= limitAmount) {
                log.warn("渠道省份开通数量即将到达限量,渠道号:{},省份:{},限量数:{}", channel, province, limitAmount);
                sendProvinceVergeSms(channel, province, limitAmount, currentCount);
            }
            if (currentCount >= limitAmount) {
                log.error("渠道省份开通数量已达到限量,渠道号:{},省份:{},限量数:{}", channel, province, limitAmount);
                sendProvinceSms(channel, province, limitAmount);
                return false;
            }
        }
        return true;
    }


    public void sendProvinceSms(String channel, String province, Integer limitAmount) {
        List<String> list = provinceBusinessChannelConfigService.getLimitSendMobile(channel, province);
        if (list.size() == 0) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_LIMIT + channel + ":" + province + ":" + limitAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendProvinceNotify(mobile, channel, province, limitAmount);
        });
    }


    public void sendProvinceVergeSms(String channel, String province, Integer limitAmount, Integer currentCount) {
        List<String> list = provinceBusinessChannelConfigService.getLimitSendMobile(channel, province);
        if (list.size() == 0) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_VERGE_LIMIT + channel + ":" + province + ":" + limitAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendProvinceVergeNotify(mobile, channel, province, limitAmount, currentCount);
        });
    }

    public void sendTotalSms(String channel, Integer limitTotalAmount) {
        List<String> list = provinceBusinessChannelConfigService.getTotalLimitSendMobile(channel);
        if (list.size() == 0) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_LIMIT + channel + ":" + limitTotalAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendTotalNotify(mobile, channel, limitTotalAmount);
        });
    }

    public void sendTotalVergeSms(String channel, Integer limitTotalAmount, Integer currentCount) {
        List<String> list = provinceBusinessChannelConfigService.getTotalLimitSendMobile(channel);
        if (list.size() == 0) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_VERGE_LIMIT + channel + ":" + limitTotalAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendTotalVergeNotify(mobile, channel, limitTotalAmount, currentCount);
        });
    }


    public boolean verifyOwnerProvinceLimit(String channel, String province, String subChannel) {
        //获取子渠道号归属
        String owner = "";
        if (outsideConfigService.isOutsideChannel(subChannel)) {
            owner = BizConstant.CHANNEL_OWNER_CPA;
        } else {
            owner = channelOwnerService.queryOwner(subChannel);
        }
        //获取当前归属限量
        Integer ownerLimitAmount = provinceBusinessChannelConfigService.getLimitByChannelAndOwner(channel, province, owner);
        Integer currentCount = subscribeService.getIncrChannelProvinceOwnerLimit(channel, province, owner);
        if (ownerLimitAmount == null) {
            return true;
        } else {
            //获取当前序列值
            if (currentCount >= ownerLimitAmount * 0.8) {
                log.warn("渠道省份开通数量即将到达限量,渠道号:{},省份:{},归属:{},限量数:{}", channel, province, owner, ownerLimitAmount, currentCount);
                sendProvinceOwnerVergeSms(channel, province, owner, ownerLimitAmount, currentCount);
            }
            if (currentCount >= ownerLimitAmount) {
                log.error("渠道省份开通数量已达到限量,渠道号:{},省份:{},归属:{},限量数:{}", channel, province, owner, ownerLimitAmount);
                sendProvinceOwnerSms(channel, province, owner, ownerLimitAmount);
                return false;
            }
            return true;
        }
    }


    /**
     * 总限量校验
     *
     * @param channel
     * @return
     */
    public boolean verifyTotalLimit(String channel) {
        //获取当前归属限量
        ProvinceBusinessChannelConfig provinceBusinessChannelConfig = provinceBusinessChannelConfigService.getByChannel(channel);
        Integer limitTotalAmount = provinceBusinessChannelConfig != null ? provinceBusinessChannelConfig.getLimitTotalAmount() : null;
        Integer currentCount = subscribeService.getIncrChannelLimit(channel);
        if (limitTotalAmount == null) {
            return true;
        } else {
            //获取当前序列值
            if (currentCount + 20 >= limitTotalAmount) {
                log.warn("渠道总开通数量即将到达限量,渠道号:{},限量数:{}", channel, limitTotalAmount);
                sendTotalVergeSms(channel, limitTotalAmount, currentCount);
            }
            if (currentCount >= limitTotalAmount) {
                log.error("渠道总开通数量即将到达限量,渠道号:{},限量数:{}", channel, limitTotalAmount);
                sendTotalSms(channel, limitTotalAmount);
                return false;
            }
            return true;
        }
    }

    public boolean verifyTotalOwnerLimit(String channel, String subChannel) {
        //获取子渠道号归属
        String owner = "";
        if (outsideConfigService.isOutsideChannel(subChannel)) {
            owner = BizConstant.CHANNEL_OWNER_CPA;
        } else {
            owner = channelOwnerService.queryOwner(subChannel);
        }
        //获取当前归属限量
        Integer ownerLimitTotalAmount = provinceBusinessChannelConfigService.getTotalLimitByChannelAndOwner(channel, owner);
        Integer currentCount = subscribeService.getIncrChannelOwnerLimit(channel, owner);
        if (ownerLimitTotalAmount == null) {
            return true;
        } else {
            //获取当前序列值
            if (currentCount >= ownerLimitTotalAmount * 0.8) {
                log.warn("渠道总开通数量即将到达限量,渠道号:{},归属:{},限量数:{}", channel, owner, ownerLimitTotalAmount);
                sendTotalOwnerVergeSms(channel, owner, ownerLimitTotalAmount, currentCount);
            }
            if (currentCount >= ownerLimitTotalAmount) {
                log.error("渠道总开通数量即将到达限量,渠道号:{},归属:{},限量数:{}", channel, owner, ownerLimitTotalAmount);
                sendTotalOwnerSms(channel, owner, ownerLimitTotalAmount);
                return false;
            }
            return true;
        }
    }


    //发送渠道到量短信
    public void sendProvinceOwnerSms(String channel, String province, String owner, Integer limitAmount) {
        List<String> list;
        if (BizConstant.CHANNEL_OWNER_CPA.equals(owner)) {
            list = provinceBusinessChannelConfigService.getLimitSendCpaMobile(channel, province);
        } else {
            list = channelOwnerProperties.getMobiles(owner);
        }
        if (list.size() == 0 || list == null) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_LIMIT + channel + ":" + province + ":" + owner + ":" + limitAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendProvinceNotify(mobile, channel, province, limitAmount);
        });
    }


    //发送渠道到量短信
    public void sendTotalOwnerSms(String channel, String owner, Integer limitAmount) {
        List<String> list;
        if (BizConstant.CHANNEL_OWNER_CPA.equals(owner)) {
            list = provinceBusinessChannelConfigService.getTotalCpaLimitSendMobile(channel);
        } else {
            list = channelOwnerProperties.getMobiles(owner);
        }
        if (list.size() == 0 || list == null) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_LIMIT + channel + ":" + owner + ":" + limitAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendTotalNotify(mobile, channel, limitAmount);
        });
    }

    //发送渠道省份即将到量短信(短信内容 即将限量,限250,当前230)
    public void sendProvinceOwnerVergeSms(String channel, String province, String owner, Integer limitAmount, Integer currentCount) {
        List<String> list;
        if (BizConstant.CHANNEL_OWNER_CPA.equals(owner)) {
            list = provinceBusinessChannelConfigService.getLimitSendCpaMobile(channel, province);
        } else {
            list = channelOwnerProperties.getMobiles(owner);
        }
        if (list.size() == 0 || list == null) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_VERGE_LIMIT + channel + ":" + province + ":" + owner + ":" + limitAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendProvinceVergeNotify(mobile, channel, province, limitAmount, currentCount);
        });
    }

    //发送渠道省份即将到量短信
    public void sendTotalOwnerVergeSms(String channel, String owner, Integer limitAmount, Integer currentCount) {
        List<String> list;
        if (BizConstant.CHANNEL_OWNER_CPA.equals(owner)) {
            list = provinceBusinessChannelConfigService.getTotalCpaLimitSendMobile(channel);
        } else {
            list = channelOwnerProperties.getMobiles(owner);
        }
        if (list.size() == 0 || list == null) {
            return;
        }
        String key = CacheConstant.CMS_CACHE_SMS_VERGE_LIMIT + channel + ":" + owner + ":" + limitAmount;
        if (!redisUtil.hasKey(key)) {
            final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
            redisUtil.set(key, 1, expire);
        } else {
            return;
        }
        list.forEach(mobile -> {
            messageNotifyService.sendTotalVergeNotify(mobile, channel, limitAmount, currentCount);
        });
    }

//    public static void main(String[] args) {
//        String[] mobilePrefixArray = new String[]{"1350851", "1350852", "1370851", "1370852"};
//        String[] mobileNumberCombination = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
//        List<String> mobileNumberCombinationList = Arrays.asList(mobileNumberCombination);
//        Arrays.asList(mobilePrefixArray).stream().forEach(mobilePrefix -> {
////            mobilePrefix
//            for (int i = 0; i < mobileNumberCombination.length; i++) {
//                for (int j = 0; j < mobileNumberCombination.length; j++) {
//                    for (int k = 0; k < mobileNumberCombination.length; k++) {
//                        for (int l = 0; l < mobileNumberCombination.length; l++) {
//                            String phone = mobilePrefix + mobileNumberCombination[i] + mobileNumberCombination[j] + mobileNumberCombination[k] + mobileNumberCombination[l];
//                            try {
//                                FileUtils.writeStringToFile(new File("D:\\屏蔽号码.txt"), DigestUtils.md5DigestAsHex(phone.getBytes(StandardCharsets.UTF_8)) +"\r\n", "UTF-8", true);
//                            } catch (IOException e) {
//                                e.printStackTrace();
//                            }
//                        }
//                    }
//                }
//            }
//        });
//    }
}
