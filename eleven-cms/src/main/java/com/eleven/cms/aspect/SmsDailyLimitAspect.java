package com.eleven.cms.aspect;

import com.eleven.cms.config.SubscribeServiceTestProperties;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;

/**
 * 短信下发每日限量5条
 *
 * @author: 叶建
 * @create: 2022-10-17 15:34
 */
@Slf4j
@Aspect
@Component
public class SmsDailyLimitAspect {

    @Autowired
    ISubscribeService subscribeService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    SubscribeServiceTestProperties subscribeServiceTestProperties;
    @Autowired
    RedisUtil redisUtil;

    public static final String LOG_TAG = "短信下发每日限量";

    public static final String SMS_DAILY_COUNTER_KEY_PREFIX = "subscribe:smsDailyCounter:";
    public static final int SMS_DAILY_LIMIT_COUNT = 3;

    @Pointcut("@annotation(com.eleven.cms.annotation.SmsDailyLimit)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object limit(ProceedingJoinPoint point) throws Throwable {

        Object[] args = point.getArgs();
        Subscribe subscribe = (Subscribe) args[0];
        final String mobile = subscribe.getMobile();

        String smsCode = subscribe.getSmsCode();
        //log.warn("{}-手机号:{},进入每日短信发送限制AOP", LOG_TAG , mobile);
        //提交短信的不做拦截
        //测试手机号也不拦截
        if (StringUtils.isNotEmpty(smsCode) || subscribeServiceTestProperties.getTestAccountMap().containsKey(mobile)) {
            return point.proceed();
        }

        String redisKey = SMS_DAILY_COUNTER_KEY_PREFIX + LocalDate.now()+ ":" + mobile;
        final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);

        final Integer count = (Integer) redisUtil.get(redisKey);
        //当日已下发过5次短信的,直接返回下发短信失败
        // 红名单的测试号码依然放行
        if(count!=null && count>=SMS_DAILY_LIMIT_COUNT && !blackListService.isRedList(mobile)){
            log.warn("{}-手机号:{},今日获取短信验证码的次数过多", LOG_TAG , mobile);
            return Result.msgSmsDayLimit();
        }
        
         //继续下发,并记录下发成功的次数
        Result result = (Result) point.proceed();
        if(CommonConstant.SC_JEECG_NO_AUTH.equals(result.getCode())|| CommonConstant.SC_OK_200.equals(result.getCode())){
            if (count == null) {
                redisUtil.set(redisKey, 1, expire);
            } else {
                redisUtil.incr(redisKey, 1L);                                                                         
            }
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(LocalDate.now());
    }
}