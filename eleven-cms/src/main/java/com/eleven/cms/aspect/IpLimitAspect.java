package com.eleven.cms.aspect;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.util.BizConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.IPUtils;
import org.springframework.stereotype.Component;

/**
 * 业务限量aop
 *
 * @author: cai lei
 * @create: 2022-08-19 15:23
 */
@Slf4j
@Aspect
@Component
public class IpLimitAspect {


    public static final String[] BLACK_IP_PROVINCE_ARRAY = {};
    public static final String[] BLACK_IP_CITY_ARRAY = {"成都", "南京"};
    public static final String[] BLACK_IP_CITY_ALL_BIZ_ARRAY = {"佛山"};
    public static final String[] COMPANY_WHITE_IP_ARRAY = {"**************", "*************"};
    public static final String[] WHTIE_CHANNEL_ARRAY = {"014X02D", "014X02F", "014X02G"};
    //public static final String[]  BLACK_IP_CITY_CHANNEL_ARRAY = {"0021180"};

    @Pointcut("@annotation(com.eleven.cms.annotation.IpLimit)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object before(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        Subscribe subscribe = (Subscribe) args[0];
        final String channel = subscribe.getChannel();
        String ip = subscribe.getIp();
        if (!checkIp(channel, ip)) {
            return Result.msgIpLimit();
        }
        return point.proceed();
    }

    /**
     * 按渠道号限制特定省份或者城市ip
     *
     * @param ip
     * @return true表示放行, false表示拦截
     */
    public static boolean checkIp(String channel, String ip) {
        try {
//            //为了业务测试,公司ip放行
//            if (StringUtils.equalsAny(ip, COMPANY_WHITE_IP_ARRAY)) {
//                return Boolean.TRUE;
//            }
//            //所有业务屏蔽佛山ip 和湖北ip
//            String[] ipLocations = IPUtils.ipLocation(ip);
//            if (ipLocations.length == 3) {
//                //if (StringUtils.containsAny(ipLocations[1], BLACK_IP_PROVINCE_ARRAY)) {
//                //    log.warn("渠道号:{},ip:{}在所有业务需要屏蔽的黑名单省份:{}", channel, ip, ipLocations[1]);
//                //    return Boolean.FALSE;
//                //}
//                if (StringUtils.containsAny(ipLocations[2], BLACK_IP_CITY_ALL_BIZ_ARRAY)) {
//                    log.warn("渠道号:{},ip:{}在所有业务需要屏蔽的黑名单城市:{}", channel, ip, ipLocations[2]);
//                    return Boolean.FALSE;
//                }
//            }

            //特定渠道号放行(讯飞视频彩铃渠道号)
            //if(StringUtils.equalsAny(channel, WHTIE_CHANNEL_ARRAY)){
            //    return Boolean.TRUE;
            //}
            ////音乐基地业务渠道号都是0开头,企业彩铃都是QYCL,音乐基地业务还需要做ip过滤
            //if(StringUtils.startsWith(channel, "0")||StringUtils.startsWith(channel, BizConstant.BIZ_TYPE_QYCL)){
            //   // //部分渠道号按城市限制,部分渠道号按省份限制
            //   //if(StringUtils.equalsAny(channel, BLACK_IP_CITY_CHANNEL_ARRAY)){
            //   //    if (ipLocations.length == 3 && StringUtils.equalsAny(ipLocations[2], BLACK_IP_CITY_ARRAY)) {
            //   //        log.warn("渠道号:{},ip:{}在黑名单城市:{}", channel, ip, ipLocations[2]);
            //   //        return Boolean.FALSE;
            //   //    }
            //   //}else {
            //   //    if (ipLocations.length == 3 && StringUtils.equalsAny(ipLocations[1], BLACK_IP_PROVINCE_ARRAY)) {
            //   //        log.warn("渠道号:{},ip:{}在黑名单省份:{}",channel, ip, ipLocations[1]);
            //   //        return Boolean.FALSE;
            //   //    }
            //   //}
            //    //现在音乐基地业务只限制ip城市在成都和南京
            //    if (ipLocations.length == 3 && StringUtils.containsAny(ipLocations[2], BLACK_IP_CITY_ARRAY)) {
            //        log.warn("渠道号:{},ip:{}在黑名单城市:{}", channel, ip, ipLocations[2]);
            //        return Boolean.FALSE;
            //    }
            //}
            return Boolean.TRUE;
        } catch (Exception e) {
            log.warn("ip查询归属地错误,ip:{}", ip, e);
            return Boolean.TRUE;
        }
    }
}
