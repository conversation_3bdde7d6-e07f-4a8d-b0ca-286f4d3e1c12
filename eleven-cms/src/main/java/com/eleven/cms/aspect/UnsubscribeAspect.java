package com.eleven.cms.aspect;

import com.eleven.cms.annotation.UnsubscribeLimit;
import com.eleven.cms.exception.UnsubscribeLimitAccessException;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @author: cai lei
 * @create: 2024-02-18 11:12
 */
@Slf4j
@Aspect
@Component
public class UnsubscribeAspect{

    public static final String UN_SUBSCRIBE_LIMIT_KEY_PREFIX = "unsubscribe:limit";

    private final RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public UnsubscribeAspect(@Qualifier("redisTemplate") RedisTemplate<String, Object> limitRedisTemplate) {
        this.redisTemplate = limitRedisTemplate;
    }

    @Autowired
    RedisUtil redisUtil;

    @Pointcut("@annotation(com.eleven.cms.annotation.UnsubscribeLimit)")
    public void pointcut() {

    }
    @Around("pointcut()")
    public Object limit(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        UnsubscribeLimit unsubscribeLimit = method.getAnnotation(UnsubscribeLimit.class);
        int limitCount = unsubscribeLimit.count();
        int limitPeriod = unsubscribeLimit.period();
        String methodName = method.getName();
        ImmutableList<String> keys = ImmutableList.of(StringUtils.join(UN_SUBSCRIBE_LIMIT_KEY_PREFIX + "_", methodName));
        String luaScript = buildLuaScript();
        RedisScript<Number> redisScript = new DefaultRedisScript<>(luaScript, Number.class);
        Number count = redisTemplate.execute(redisScript, keys, limitCount, limitPeriod);
        log.info("第 {} 次访问key为 {}，描述为 [{}] 的接口", count, keys, methodName);
        if (count != null && count.intValue() <= limitCount) {
            return point.proceed();
        } else {
            throw new UnsubscribeLimitAccessException("退订接口访问超出频率限制");
        }

    }

    /**
     * 限流脚本
     * 调用的时候不超过阈值，则直接返回并执行计算器自加。
     *
     * @return lua脚本
     */
    private String buildLuaScript() {
        return "local c" +
                "\nc = redis.call('get',KEYS[1])" +
                "\nif c and tonumber(c) > tonumber(ARGV[1]) then" +
                "\nreturn c;" +
                "\nend" +
                "\nc = redis.call('incr',KEYS[1])" +
                "\nif tonumber(c) == 1 then" +
                "\nredis.call('expire',KEYS[1],ARGV[2])" +
                "\nend" +
                "\nreturn c;";
    }


    public static void main(String[] args) {

    }
}
