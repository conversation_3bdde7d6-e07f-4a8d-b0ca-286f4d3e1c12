package com.eleven.cms.aspect;

import com.eleven.cms.annotation.IvrUnsubscribeLimit;
import com.eleven.cms.config.IvrUnsubscribeConfig;
import com.eleven.cms.config.IvrUnsubscribeProperties;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.service.IDatangSmsService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeecg.common.api.vo.IvrResult;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 业务退订接口每日限量5000次
 *
 * @author: lihb
 * @create: 2023年4月3日11:12:54
 */
@Slf4j
@Aspect
@Component
public class IvrUnsubscribeLimitAspect {

    @Autowired
    IDatangSmsService datangSmsService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    IvrUnsubscribeProperties ivrUnsubscribeProperties;
    @Autowired
    RedisUtil redisUtil;

    public static final String LOG_TAG = "业务退订每日限量";

    public static final String UN_SUBSCRIBE_COUNTER_KEY_PREFIX = "ivr:unsubscribe:";

    @Pointcut("@annotation(com.eleven.cms.annotation.IvrUnsubscribeLimit)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object limit(ProceedingJoinPoint point) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method method = methodSignature.getMethod();
        String methodName = method.getName();
        String redisKey = UN_SUBSCRIBE_COUNTER_KEY_PREFIX + methodName;
        IvrUnsubscribeLimit ivrUnsubscribeLimit = method.getAnnotation(IvrUnsubscribeLimit.class);
        final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
        final Integer currentCount = (Integer) redisUtil.get(redisKey);
        final Integer count = ivrUnsubscribeLimit.count();
        final String[] mobileArray = ivrUnsubscribeLimit.mobileArray();
        //记录调用接口次数
        if (currentCount == null) {
            redisUtil.set(redisKey, 1, expire);
        } else {
            redisUtil.incr(redisKey, 1L);
        }

        //接口调用一半时下发短信通知
        if (currentCount != null && currentCount * 2 == count) {
            log.warn("{}-方法名:{},今日业务退订接口调用次数已使用{}次", LOG_TAG, methodName, count);
            for (String mobile : mobileArray) {
                datangSmsService.sendSms(mobile, ivrUnsubscribeLimit.msgContent() + "(" + methodName + "),次数已达一半,次数:" + count);
            }
        }
        //接口调用超限制时下发短信通知，并停止接口调用
        if (currentCount != null && currentCount.equals(count)) {
            log.warn("{}-方法名:{},今日业务退订接口调用次数过多", LOG_TAG, methodName);
            for (String mobile : mobileArray) {
                datangSmsService.sendSms(mobile, ivrUnsubscribeLimit.msgContent() + "(" + methodName + "),次数已用完,次数:" + count);
            }
        }

        if (currentCount != null && currentCount >= count) {
            log.warn("{}-方法名:{},今日业务退订接口调用次数过多", LOG_TAG, methodName);
            return Result.error("今日业务退订接口调用次数过多");
        }
        Result result = (Result) point.proceed();
        return result;
    }
}
