package com.eleven.cms.aspect;

import com.eleven.cms.annotation.IvrAlipayUnsubscribeLimit;
import com.eleven.cms.remote.BlackListService;
import com.eleven.cms.service.IDatangSmsService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeecg.common.api.vo.IvrResult;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;

/**
 * 支付宝业务退订接口
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/12 11:49
 **/
@Slf4j
@Aspect
@Component
public class IvrAlipayUnsubscribeLimitAspect {
    @Autowired
    IDatangSmsService datangSmsService;
    @Autowired
    BlackListService blackListService;
    @Autowired
    RedisUtil redisUtil;

    public static final String LOG_TAG = "支付宝业务退订每日限量";

    public static final String ALIPAY_UN_SUBSCRIBE_COUNTER_KEY_PREFIX =  "ivr:alipay:unsubscribe:";

    @Pointcut("@annotation(com.eleven.cms.annotation.IvrAlipayUnsubscribeLimit)")
    public void pointcut() {

    }

    @Around("pointcut()")
    public Object limit(ProceedingJoinPoint point) throws Throwable {
        Signature signature = point.getSignature();
        String methodName = signature.getName();
        String redisKey = ALIPAY_UN_SUBSCRIBE_COUNTER_KEY_PREFIX + methodName;
        final long expire = ChronoUnit.SECONDS.between(LocalTime.now(), LocalTime.MAX);
        final Integer count = (Integer) redisUtil.get(redisKey);
        //记录调用接口次数
        if (count == null) {
            redisUtil.set(redisKey, 1, expire);
        } else {
            redisUtil.incr(redisKey, 1L);
        }
        IvrAlipayUnsubscribeLimit alipayUnsubscribeLimit = ((MethodSignature)signature).getMethod().getAnnotation(IvrAlipayUnsubscribeLimit.class);
        //接口调用一半时下发短信通知
        if(count != null && count*2 == alipayUnsubscribeLimit.count()){
            log.warn("{}-方法名:{},今日业务退订接口调用次数已使用{}次", LOG_TAG , methodName,count);
            for (String mobile : alipayUnsubscribeLimit.mobileArray()) {
                datangSmsService.sendSms(mobile,alipayUnsubscribeLimit.msgContent() + "(" + methodName + "),次数已达一半,次数:" + count);
            }
        }
        //接口调用超限制时下发短信通知，并停止接口调用
        if(count != null && count == alipayUnsubscribeLimit.count()){
            log.warn("{}-方法名:{},今日业务退订接口调用次数过多", LOG_TAG , methodName);
            for (String mobile : alipayUnsubscribeLimit.mobileArray()) {
                datangSmsService.sendSms(mobile,alipayUnsubscribeLimit.msgContent() + "(" + methodName + "),次数已用完,次数:" + count);
            }
        }
        if(methodName.equals("alipayUnSignAndRefund")){
            if(count != null && count >= alipayUnsubscribeLimit.count()){
                log.warn("{}-方法名:{},今日业务退订接口调用次数过多", LOG_TAG , methodName);
                return IvrResult.response(500,"今日业务退订接口调用次数过多");
            }
            IvrResult result = (IvrResult) point.proceed();
            return result;
        }
        Result result = (Result) point.proceed();
        return result;
    }
}
