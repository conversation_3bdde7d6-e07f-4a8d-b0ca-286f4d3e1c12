package com.eleven.cms.aspect;

import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.IProvinceBusinessChannelConfigService;
import com.eleven.cms.service.ISubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 业务开通成功aop
 *
 * @author: cai lei
 * @create: 2022-08-19 15:23
 */
@Slf4j
@Aspect
@Component
public class BusinessSuccessAspect extends AspectSupport {

    @Autowired
    IProvinceBusinessChannelConfigService provinceBusinessChannelConfigService;
    @Autowired
    ISubscribeService subscribeService;

    @Pointcut("@annotation(com.eleven.cms.annotation.SuccessLimit)")
    public void pointcut() {

    }

    @AfterReturning(value = "pointcut()", returning = "returnVal")
    public void afterReturning(JoinPoint point, Object returnVal) throws Throwable {
        Object[] args = point.getArgs();
        Subscribe subscribe = (Subscribe) args[0];
        Result result = (Result) returnVal;
        if (result.isOK()) {
            subscribeService.incrChannelProvinceLimit(subscribe.getChannel(), subscribe.getProvince());
        }
    }
}
