package com.eleven.cms.aspect;

import com.eleven.cms.entity.CmsCrackConfig;
import com.eleven.cms.entity.Subscribe;
import com.eleven.cms.service.ICmsCrackConfigService;
import com.eleven.cms.vo.MobileRegionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 业务限量aop
 *
 * @author: cai lei
 * @create: 2022-08-19 15:23
 */
@Slf4j
@Aspect
@Component
public class IspLimitAspect {

    @Autowired
    private ICmsCrackConfigService cmsCrackConfigService;

    @Pointcut("@annotation(com.eleven.cms.annotation.IspLimit)")
    public void pointcut() {

    }
    @Around("pointcut()")
    public Object before(ProceedingJoinPoint point) throws Throwable {
//        Object[] args = point.getArgs();
//        Subscribe subscribe = (Subscribe) args[0];
//        String isp = subscribe.getIsp();
//        String channel = subscribe.getChannel();
//        CmsCrackConfig crackConfig=cmsCrackConfigService.getCrackConfigByChannel(channel);
//        if(crackConfig==null){
//            return point.proceed();
//        }
//        //没有日志默认可以开通
//        String logTag=crackConfig.getLogTag();
//        if(StringUtils.isBlank(logTag)){
//            return point.proceed();
//        }
//        if (logTag.contains("联通") && !MobileRegionResult.ISP_LIANTONG.equals(isp)) {
//            return Result.msgIspRestrict();
//        }
//        if (logTag.contains("电信")  && !MobileRegionResult.ISP_DIANXIN.equals(isp)) {
//            return Result.msgIspRestrict();
//        }
//        if (!logTag.contains("电信") && !logTag.contains("联通") && !MobileRegionResult.ISP_YIDONG.equals(isp)) {
//            return Result.msgIspRestrict();
//        }
        return point.proceed();
    }
}
