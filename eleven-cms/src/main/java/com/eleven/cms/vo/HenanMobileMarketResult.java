package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HenanMobileMarketResult {

    public static final String CODE_OK = "1";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private ResultData data;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static HenanMobileMarketResult fail() {
        return HenanMobileMarketResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }

    @Data
    public static class ResultData {
        @JsonProperty("mealList")
        private List<MealListData> mealList;
        @JsonProperty("entryAllFlag")
        private String entryAllFlag;
        @JsonProperty("activityList")
        private List<ActivityListData> activityList;
    }

    @Data
    public static class MealListData {
        @JsonProperty("mealId")
        private String mealId;
        @JsonProperty("mealName")
        private String mealName;
    }

    @Data
    public static class ActivityListData {
        @JsonProperty("activityCode")
        private String activityCode;
        @JsonProperty("activityName")
        private String activityName;
    }
}
