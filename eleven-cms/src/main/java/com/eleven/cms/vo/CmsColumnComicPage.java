package com.eleven.cms.vo;

import java.util.List;

import com.eleven.cms.entity.CmsColumnComic;
import com.eleven.cms.entity.CmsComicVideo;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: cms_column_comic
 * @Author: jeecg-boot
 * @Date: 2024-08-26
 * @Version: V1.0
 */
@Data
@ApiModel(value = "cms_column_comicPage对象", description = "cms_column_comic")
public class CmsColumnComicPage {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 栏目名称
     */
    @Excel(name = "栏目名称", width = 15)
    @ApiModelProperty(value = "栏目名称")
    private String title;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer priority;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    @ExcelCollection(name = "cms_comic_video")
    @ApiModelProperty(value = "cms_comic_video")
    private List<CmsComicVideo> cmsComicVideoList;

}
