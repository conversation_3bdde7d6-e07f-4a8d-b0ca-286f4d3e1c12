package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HenanMobileCheckResult {

    public static final String CODE_OK = "1";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static HenanMobileCheckResult fail() {
        return HenanMobileCheckResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }
}
