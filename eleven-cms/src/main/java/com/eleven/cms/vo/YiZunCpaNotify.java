package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YiZunCpaNotify {

    public static final String CODE_OK = "0";
    public static final String MSG_OK = "成功";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;





    public static YiZunCpaNotify ok() {
        return YiZunCpaNotify.builder().code(CODE_OK).message(MSG_OK).build();
    }
    public static YiZunCpaNotify fail() {
        return YiZunCpaNotify.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }

    public static YiZunCpaNotify fail(String msg) {
        return YiZunCpaNotify.builder().code(CODE_FAIL).message(msg).build();
    }
}
