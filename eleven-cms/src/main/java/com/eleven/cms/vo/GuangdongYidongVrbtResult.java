package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GuangdongYidongVrbtResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private String code;
    @JsonProperty("data")
    private String data;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("success")
    private boolean success;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static GuangdongYidongVrbtResult fail() {
        return GuangdongYidongVrbtResult.builder().code(CODE_FAIL).msg(MSG_FAIL).build();
    }

}
