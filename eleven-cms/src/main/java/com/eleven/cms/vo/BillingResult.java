package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BillingResult {

    public static final BillingResult FAIL_RESULT = new BillingResult("9999", "通讯失败",null);

    /**
     * code : 0
     * message : success
     * transId : 202003261022439000
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("transId")
    private String transId;

    public static BillingResult fail() {
        return FAIL_RESULT;
    }

    public boolean isOK(){
        //0为新老pj返回成功,00000为森越pj返回成功
        return "0".equals(code)||"00000".equals(code);
    }
}
