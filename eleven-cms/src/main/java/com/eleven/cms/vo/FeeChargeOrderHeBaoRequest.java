package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 话费充值余额查询结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/18 10:17
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeChargeOrderHeBaoRequest implements Serializable {
    public static final int CODE_OK = 0;
    //0:成功；其他:失败
    @JsonProperty("nRtn")
    private int nRtn;
    //订单
    @JsonProperty("szOrderId")
    private String szOrderId;

    @JsonProperty("fNBalance")
    private Double fNBalance;
    @JsonProperty("fSalePrice")
    private Double fSalePrice;

    @JsonProperty("szRtnCode")
    private String szRtnCode;
    public boolean isOK() {
        return CODE_OK==nRtn;
    }
}
