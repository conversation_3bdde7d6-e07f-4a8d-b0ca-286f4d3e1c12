package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-05-25 14:46
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangxiYidongOrderResult {

    private String ret;
    private String msg;
    private Data data;

    private static final String CALL_SUC_CODE = "0";
    private static final String RESULT_SUC_OK = "1";

    public static final JiangxiYidongOrderResult FAIL_RESULT = new JiangxiYidongOrderResult("9999", "通讯失败", null);

    @lombok.Data
    @NoArgsConstructor
    public static class Data {
        private ResultMsg resultMsg;
    }

    @lombok.Data
    @NoArgsConstructor
    public static class ResultMsg {
        private String code;
        private String resultCode;
        private String message;
        private String orderId;
    }

    public boolean isOk() {
        return CALL_SUC_CODE.equals(ret) && data != null && data.getResultMsg() != null &&
                RESULT_SUC_OK.equals(data.getResultMsg().getCode()) && RESULT_SUC_OK.equals(data.getResultMsg().getResultCode());
    }


}
