package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO 江西视频彩铃订单查询返回结果
 * @Date 2023/10/13 15:39
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangXiVrbtQueryOrderResult implements Serializable {
    public static final String CODE_OK = "0";

    /**
     * message : 请求成功
     * returnCode : 0
     * platOrderNo : *****
     * customerOrderNo : ****
     * orderStatus : UNDERWAY
     */

    @JsonProperty("message")
    private String message;
    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("platOrderNo")
    private String platOrderNo;
    @JsonProperty("customerOrderNo")
    private String customerOrderNo;
    @JsonProperty("orderStatus")
    private String orderStatus;
    public boolean isOK(){
        return CODE_OK.equals(returnCode);
    }
}



