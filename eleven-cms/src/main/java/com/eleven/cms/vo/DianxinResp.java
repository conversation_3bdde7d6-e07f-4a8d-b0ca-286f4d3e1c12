package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2020/11/26 16:34
 * Desc: 电信视频材料api响应封装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DianxinResp {

    public static final String CODE_OK = "0000";
    public static final String CODE_OK_EXTRA = "0";

    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    
    public static final String CODE_ERROR_SMS = "0013";

    /**
     * order_no : 20201126120439612006
     * res_message : 成功
     * res_code : 0000
     */

    @JsonProperty("res_code")
    private String resCode;
    @JsonProperty("res_message")
    private String resMessage;
    @JsonProperty("order_no")
    private String orderNo;
    @JsonProperty("fee_url")
    private String feeUrl;

    public boolean isOK(){
        return CODE_OK.equals(resCode) || CODE_OK_EXTRA.equals(resCode);
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getResMessage() {
        return resMessage;
    }

    public void setResMessage(String resMessage) {
        this.resMessage = resMessage;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public static DianxinResp fail(){
        return DianxinResp.builder().resCode(CODE_FAIL).resMessage(MSG_FAIL).build();
    }

    public static DianxinResp fail(String msg){
       return DianxinResp.builder().resCode(CODE_FAIL).resMessage(msg).build();
    }

}
