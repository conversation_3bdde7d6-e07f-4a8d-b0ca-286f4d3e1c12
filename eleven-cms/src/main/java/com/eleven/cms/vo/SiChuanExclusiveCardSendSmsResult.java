package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/14 11:56
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SiChuanExclusiveCardSendSmsResult implements Serializable {
    private static final Integer CODE_OK =200;
    /**
     * code : 200
     * msg : ok
     * data : null
     */

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private Object data;
    public boolean isOK(){
        return this.getCode().equals(CODE_OK);
    }
}
