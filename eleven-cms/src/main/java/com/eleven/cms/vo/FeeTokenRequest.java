package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/9 16:38
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeTokenRequest implements Serializable {
    public static final int CODE_OK = 200;
    /**
     * msg : 成功
     * code : 200
     * data : {"departmentName":"","loginAccount":"bb","departmentId":"","userName":"aa","userId":"*****************","tokenExpire":"43200","token":""}
     */

    @JsonProperty("msg")
    private String msg;
    @JsonProperty("code")
    private int code;
    @JsonProperty("data")
    private Data data;
    public boolean isOK() {
        return CODE_OK==code && this.getData()!=null;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * departmentName :
         * loginAccount : bb
         * departmentId :
         * userName : aa
         * userId : *****************
         * tokenExpire : 43200
         * token :
         */

        @JsonProperty("departmentName")
        private String departmentName;
        @JsonProperty("loginAccount")
        private String loginAccount;
        @JsonProperty("departmentId")
        private String departmentId;
        @JsonProperty("userName")
        private String userName;
        @JsonProperty("userId")
        private String userId;
        @JsonProperty("tokenExpire")
        private String tokenExpire;
        @JsonProperty("token")
        private String token;
    }
}
