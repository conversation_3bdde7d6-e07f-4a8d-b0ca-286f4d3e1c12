package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SichuanMobileResult {


    public static final String CODE_OK = "0000";

    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("res_code")
    private String resCode;
    @JsonProperty("res_msg")
    private String resMsg;
    @JsonProperty("result")
    private BizResult result;

    public boolean isOK() {
        return CODE_OK.equals(resCode);
    }

    public static SichuanMobileResult fail() {
        return SichuanMobileResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).build();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class BizResult {

        public static final String IS_OK = "Y";

        @JsonProperty("order_id")
        private String orderId; //订单号

        //合约办理字段
        @JsonProperty("cheate_accept")
        private String cheateAccept; //流水号
        @JsonProperty("pass_flag")
        private String passFlag; //办理是否成功 Y成功
        @JsonProperty("log_id")
        private String logId; //

        //流量套餐订购

        @JsonProperty("eff_date")
        private String effDate; //生效时间
        @JsonProperty("exp_date")
        private String expDate; //失效时间
        @JsonProperty("effexp_mode")
        private String effexpMode; //生失效标志
        @JsonProperty("brand_id")
        private String brandId; //用户品牌

        public boolean isSuccess() {
            return IS_OK.equals(passFlag);
        }
    }
}
