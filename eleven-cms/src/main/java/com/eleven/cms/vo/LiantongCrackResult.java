package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2021/5/12 16:39
 * Desc: 破解计费返回封装
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LiantongCrackResult {

    public static final LiantongCrackResult FAIL_RESULT = new LiantongCrackResult("9999", "通讯失败",null);

    /**
     * code : 0
     * message : success
     * transId : 202003261022439000
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("sid")
    private String sId;

    public static LiantongCrackResult fail() {
        return FAIL_RESULT;
    }

    public boolean isGetOK() {
        return "00000".equals(code);
    }

    public boolean isSubOK() {
        return "0000".equals(code);
    }
}
