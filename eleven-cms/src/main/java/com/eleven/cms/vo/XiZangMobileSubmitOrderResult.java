package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/29 9:35
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XiZangMobileSubmitOrderResult implements Serializable {
    public static final String RESP_CODE_OK = "00000";
    /**
     * result : {"result":{"code":"0000","message":"操作成功"},"outData":{"orderId":"240411323773281"}}
     * respDesc : 调用成功!
     * respCode : 00000
     */

    @JsonProperty("result")
    private ResultX result;
    @JsonProperty("respDesc")
    private String respDesc;
    @JsonProperty("respCode")
    private String respCode;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getRespCode()) && this.getResult()!=null && this.getResult().getResult()!=null && this.getResult().getResult().isOK();
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ResultX implements Serializable {
        /**
         * result : {"code":"0000","message":"操作成功"}
         * outData : {"orderId":"240411323773281"}
         */

        @JsonProperty("result")
        private Result result;
        @JsonProperty("outData")
        private OutData outData;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class Result implements Serializable {
            public static final String CODE_OK = "0000";
            /**
             * code : 0000
             * message : 操作成功
             */

            @JsonProperty("code")
            private String code;
            @JsonProperty("message")
            private String message;
            public boolean isOK() {
                return CODE_OK.equals(this.getCode());
            }
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class OutData implements Serializable {
            /**
             * orderId : 240411323773281
             */

            @JsonProperty("orderId")
            private String orderId;
        }
    }
}
