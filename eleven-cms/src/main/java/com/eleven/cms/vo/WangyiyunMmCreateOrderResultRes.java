package com.eleven.cms.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
public class WangyiyunMmCreateOrderResultRes{

    /*渠道id*/
    @JsonProperty("ch_id")
    private String chId;
    /*渠道类型*/
    @JsonProperty("ch_type")
    private String chType;
    /*方案样式*/
    @JsonProperty("scheme")
    private String scheme;
    /*Sp接入号*/
    @JsonProperty("spnumber")
    private String spnumber;
    /*短代指令*/
    @JsonProperty("sms_msg")
    private String smsMsg;
    /*计费渠道名称*/
    @JsonProperty("ch_name")
    private String chName;
    /*计费渠道描述*/
    @JsonProperty("ch_desc")
    private String chDesc;
    /*金额*/
    @JsonProperty("amount")
    private String amount;
    /*支付中心订单号*/
    @JsonProperty("orderid")
    private String orderId;
    /*是否包月代码*/
    @JsonProperty("isMonthly")
    private String isMonthly;
    /*计费入口地址*/
    @JsonProperty("fee_url")
    private String feeUrl;
    /*发送短信条数*/
    @JsonProperty("sms_count")
    private String smsCount;
    /*多条短信发送时间间隔*/
    @JsonProperty("sms_interval")
    private String smsInterval;
    /*短信内容分隔符*/
    @JsonProperty("sms_separator")
    private String smsSeparator;
    /*计费代码类型*/
    @JsonProperty("fee_code_type")
    private String feeCodeType;
    }