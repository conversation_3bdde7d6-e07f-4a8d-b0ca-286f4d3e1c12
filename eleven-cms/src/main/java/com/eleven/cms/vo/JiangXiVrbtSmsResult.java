package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 江西视频彩铃发送短信验证码返回结果
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangXiVrbtSmsResult implements Serializable {

    public static final String CODE_OK = "0";
    /**
     * returnCode : 0
     * message : 验证码校验成功
     * ts : 1694930977318
     * data : null
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("ts")
    private Long ts;
    @JsonProperty("data")
    private Object data;

    public boolean isOK(){
        return CODE_OK.equals(returnCode);
    }
}
