package com.eleven.cms.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;

import java.util.List;
@Data
@ApiModel(value="cms_migu_pack对象", description="咪咕业务包")
public class RightsPackDto {
    /**主键id*/
    @ApiModelProperty(value = "主键id")
    private String id;
    /**业务id*/
    @Excel(name = "业务id", width = 15)
    @ApiModelProperty(value = "业务id")
    private String serviceId;
    /**包名*/
    @Excel(name = "包名", width = 15)
    @ApiModelProperty(value = "包名")
    private String titleName;

    /**权益包名*/
    @Excel(name = "权益包名", width = 15)
    @ApiModelProperty(value = "权益包名")
    private String packName;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 产品图片
     */
    @Excel(name = "产品图片", width = 15)
    @ApiModelProperty(value = "产品图片")
    private String productImg;

    /**提供人名称*/
    @Excel(name = "提供人名称", width = 15)
    @ApiModelProperty(value = "提供人名称")
    private String providePersonName;

    @ExcelCollection(name="会员权益业务关联")
    @ApiModelProperty(value = "会员权益业务关联")
    private List<RightsPackList> rightsPackList;


}
