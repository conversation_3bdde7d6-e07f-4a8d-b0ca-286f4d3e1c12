package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 软游通业务获取验证码
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/23 9:25
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RuanYouTongSmsCodeResult implements Serializable {
    public static final Integer  CODE_OK =1;
    /**
     * code : 1
     * orderid : XEBY2023033119054897701825
     * errmsg : 成功
     */

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("orderid")
    private String orderid;
    @JsonProperty("errmsg")
    private String errmsg;

    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }
}
