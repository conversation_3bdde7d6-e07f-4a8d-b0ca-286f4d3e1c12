package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/5 15:31
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SiChuanExclusiveCardRequest implements Serializable {

    /**
     * msg : success
     * serialnum : HLW1701742025237958840424
     * orderNO : OD202312051007052176
     * code : 0000
     * city_code : 511300
     * province_code : 510000
     * phone : roekf2P7hKoMpjYb98ogUg==
     * status : 0
     */

    @JsonProperty("msg")
    private String msg;
    @JsonProperty("serialnum")
    private String serialnum;
    @JsonProperty("orderNO")
    private String orderNO;
    @JsonProperty("code")
    private String code;
    @JsonProperty("city_code")
    private String cityCode;
    @JsonProperty("province_code")
    private String provinceCode;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("status")
    private Integer status;
}
