package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2023-5-25 16:20:37
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WoReadWxBeforeOrderResult {

    public static final String CODE_OK = "0000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    /*0000代表正常，其它代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("innercode")
    private String innercode;
    private String resMessage;
    private WoReadWxBeforeOrderMessage data;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static WoReadWxBeforeOrderResult fail() {
        return WoReadWxBeforeOrderResult.builder().code(CODE_FAIL).resMessage(MSG_FAIL).build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WoReadWxBeforeOrderMessage{
        //类型：String  必有字段  备注：随机字符串
        @JsonProperty("nonce_str")
        private String nonceStr;
        //类型：String  必有字段  备注：签名
        @JsonProperty("sign")
        private String sign;
        //类型：String  必有字段  备注：返回信息
        @JsonProperty("return_msg")
        private String returnMsg;
        //类型：String  必有字段  备注：商户号
        @JsonProperty("mch_id")
        private String mchId;
        //类型：String  必有字段  备注：预支付交易会话标识
        @JsonProperty("prepay_id")
        private String prepayId;
        //类型：String  必有字段  备注：时间轴
//        @JsonProperty("timeStamp")
//        private String timeStamp;
        //类型：String  必有字段  备注：订单号码
        @JsonProperty("out_trade_no")
        private String outTradeNo;
        //类型：String  必有字段  备注：请求appid
        @JsonProperty("appid")
        private String appid;
        //类型：String  必有字段  备注：支付方式
        @JsonProperty("trade_type")
        private String tradeType;
        //类型：String  必有字段  备注：微信返回结果code
        @JsonProperty("result_code")
        private String resultCode;
        //类型：String  必有字段  备注：返回状态码
        @JsonProperty("return_code")
        private String returnCode;
        //类型：String  必有字段  备注：支付跳转地址
        @JsonProperty("reqUrl")
        private String reqUrl;
    }
}
