package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2023-5-25 16:20:37
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WoReadMobilePayResult {

    public static final String CODE_OK = "0000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    /*0000代表正常，其它代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("innercode")
    private String innercode;
    private String resMessage;
    private WoReadMobilePayMessage data;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static WoReadMobilePayResult fail() {
        return WoReadMobilePayResult.builder().code(CODE_FAIL).resMessage(MSG_FAIL).build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WoReadMobilePayMessage{
        @JsonProperty("sign")
        private String sign;
        //类型：String  必有字段  备注：支付跳转链接
        @JsonProperty("requrl")
        private String reqUrl;
        //类型：String  必有字段  备注：支付订单ID
        @JsonProperty("torder")
        private String torder;
    }
}
