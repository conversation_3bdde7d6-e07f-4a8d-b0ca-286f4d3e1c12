package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppDictItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    public AppDictItemVO() {
    }

    public AppDictItemVO(String value, String text, String description, Integer sortOrder, Integer status) {
        this.value = value;
        this.text = text;
        this.description = description;
        this.sortOrder = sortOrder;
        this.status = status;
    }

    /**
     * 字典value
     */
    private String value;
    /**
     * 字典文本
     */
    private String text;

    private String description;

    private Integer sortOrder;

    private Integer status;


}
