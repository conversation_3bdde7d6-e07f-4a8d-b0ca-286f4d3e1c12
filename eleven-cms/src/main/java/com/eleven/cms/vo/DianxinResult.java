package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2020/11/26 16:34
 * Desc: 电信官方视频彩铃网页接口返回结果封装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DianxinResult {

    public static final String CODE_OK = "0000";
    public static final String CODE_OK_EXTRA = "0";
    // {"loginType":"2","backUrl":"https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081711","code":"0001","desc":"验证码不匹配"}
    public static final String CODE_CODE_ERROR = "0001";
    // {"feeProductId":null,"feeType":1,"exeTime":97,"backorder":0,"desc":"订购关系已存在","code":"10131","resp":null,"result":"0"}
    public static final String CODE_PACKAGE_EXISTS = "10131";

    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    
    /**
     * {
     *     "loginType": "2",
     *     "backUrl": "https://m.imusic.cn/h5v/vrbtjx/detailVertical?ringId=910100081713",
     *     "code": "0000",
     *     "result": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
     * }
     */
    @JsonProperty("code")
    private String code;
    @JsonProperty("desc")
    private String desc;
    @JsonProperty("result")
    private String result;
    //@JsonProperty("loginType")
    //private String loginType;
    @JsonProperty("backUrl")
    private String backUrl;


    public boolean isOK(){
        return CODE_OK.equals(code) || CODE_OK_EXTRA.equals(code);
    }

    /**
     * 判断短信验证码错误
     * @return
     */
    public boolean isCodeError(){
        return CODE_CODE_ERROR.equals(code);
    }

    /**
     * 判断是否已开通包月
     * @return
     */
    public boolean isPackageExists(){
        return CODE_PACKAGE_EXISTS.equals(code);
    }

    public static DianxinResult fail(String msg){
        return DianxinResult.builder().code(CODE_FAIL).desc(msg).build();
    }
    public static DianxinResult fail(){
        return DianxinResult.builder().code(CODE_FAIL).desc(MSG_FAIL).build();
    }
}
