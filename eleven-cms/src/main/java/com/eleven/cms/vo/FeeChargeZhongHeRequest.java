package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/10 9:59
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeChargeZhongHeRequest implements Serializable {


    /**
     * AppKey : Test@ZHINK
     * Timespan : 20190312153422345
     * Sign : 4cfb48cd3971268284b64788d41aa1a3
     * Data : 8be4aeb3e9e841bb8abb33dc102c07506b518aad9ff5726a5b527012fd77d617c44d958b4022b2128511fcb47ee23d91940fad281774ecb9bda774071a975cefbd882a07c184f38cb33e5177ec71ae82a6189ae514595ec238387c10045fcd384f664090849fe8633a82a0387c9a0de2276eda882a61dad5b3812c8d44cd9286a425780b0297ddaef69f79bf40de3782cb23172f3215251f4a30b8d1350dfc4fd30feb8a2004aaa59a28774ce5d6291c4c3f3aea18eaf65a56b1a4397110c4a30f64a61adeb50e867fb92df5daed8f295c7b8301377be79541a210ba26da751601c44f21ba70230b67bd9248d375dbcb84538808199af7cf6b434a41e5659180477fc96badac212cd6b1f31b53148730f279671346c312628655e6369d203241
     */

    @JsonProperty("AppKey")
    private String AppKey;
    @JsonProperty("Timespan")
    private String Timespan;
    @JsonProperty("Sign")
    private String Sign;
    @JsonProperty("Data")
    private String Data;
}
