package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO 江西视频彩铃下单返回结果
 * @Date 2023/10/13 14:28
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangXiVrbtSubOrderResult implements Serializable {
    public static final String CODE_OK = "0";
    public static final String CODE_UNDERWAY = "UNDERWAY";
    public static final String CODE_SUCCESS = "SUCCESS";
    /**
     * returnCode : 0
     * message : 请求成功
     * ts : 1694931490421
     * data : {"msg":"[MSG]短信发送时间超时，请在发送短信后5分钟内办理业务","platOrderNo":"CL20230917141809e26b0c","orderStatus":"FAILED"}
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("ts")
    private long ts;
    @JsonProperty("data")
    private Data data;

    public boolean isOK(){
        return CODE_OK.equals(returnCode) && data != null;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * msg : [MSG]短信发送时间超时，请在发送短信后5分钟内办理业务
         * platOrderNo : CL20230917141809e26b0c
         * orderStatus : FAILED
         */

        @JsonProperty("msg")
        private String msg;
        @JsonProperty("platOrderNo")
        private String platOrderNo;
        @JsonProperty("orderStatus")
        private String orderStatus;

        public boolean isOK(){
            return CODE_UNDERWAY.equals(orderStatus) || CODE_SUCCESS.equals(orderStatus);
        }
    }
}
