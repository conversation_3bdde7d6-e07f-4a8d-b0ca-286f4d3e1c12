package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * AI视频彩铃通知
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/16 11:14
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AiVrbtRequest implements Serializable {
    //用户手机号
    @JsonProperty("mobile")
    private String mobile;
    //ISMP 产品 ID
    @JsonProperty("productid")
    private String productid;
    //状态 -1 只发送了短信 0-订购 1-退订 2-延迟扣费 3 1小时内退订
    @JsonProperty("state")
    private String state;

    //操作时间，格式为 yyyy-MM-ddHH:mm:ss
    @JsonProperty("time")
    private String time;
    //订单唯一标识
    @JsonProperty("orderNo")
    private String orderNo;

    //用户设备 ID
    @JsonProperty("deviceNo")
    private String deviceNo;
    //订单流水号
    @JsonProperty("streamingNo")
    private String streamingNo;
    //权益生效时间，格式为 yyyy-MM-dd HH:mm:ss
    @JsonProperty("orderTime")
    private String orderTime;
    //权益失效时间，格式为 yyyy-MM-dd HH:mm:ss
    @JsonProperty("validTime")
    private String validTime;

}
