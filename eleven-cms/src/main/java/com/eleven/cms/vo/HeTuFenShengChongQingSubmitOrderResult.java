package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 河图分省提交验证码响应
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 17:04
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HeTuFenShengChongQingSubmitOrderResult implements Serializable {
    public static final String RESP_CODE_OK = "000000";
    public static final String ORDER_CODE_OK = "060369";
    public static final String AWAIT_CODE_OK = "060402";
    /**
     * returnCode : 000000
     * message : 请求成功
     * resultData : {"orderId":"20241012214514APP_6Q25675028"}
     * serverTime : 1728741228924
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("resultData")
    private ResultData resultData;
    @JsonProperty("serverTime")
    private long serverTime;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getReturnCode()) || ORDER_CODE_OK.equals(this.getReturnCode()) || AWAIT_CODE_OK.equals(this.getReturnCode());
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ResultData implements Serializable {
        /**
         * orderId : 20241012214514APP_6Q25675028
         */

        @JsonProperty("orderId")
        private String orderId;
    }
}
