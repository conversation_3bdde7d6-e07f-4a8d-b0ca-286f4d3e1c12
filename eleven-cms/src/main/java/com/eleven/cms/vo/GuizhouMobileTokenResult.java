package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-20 10:59:49
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GuizhouMobileTokenResult {

    public static final String CODE_OK = "00";
    public static final String CODE_FAIL = "99";
    public static final String MSG_FAIL = "操作失败";

    /*00代表正常，其它代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("token")
    private String token;
    @JsonProperty("expireTimes")
    private String expireTimes;
    @JsonProperty("message")
    private String message;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static GuizhouMobileTokenResult fail() {
        return GuizhouMobileTokenResult.builder().code(CODE_FAIL).message(MSG_FAIL).code("false").build();
    }
}
