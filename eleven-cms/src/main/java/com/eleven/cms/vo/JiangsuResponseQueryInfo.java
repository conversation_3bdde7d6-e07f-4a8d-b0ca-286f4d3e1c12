package com.eleven.cms.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-03-08 11:38
 */
@Data
@JacksonXmlRootElement(localName="Request")
@NoArgsConstructor
@AllArgsConstructor
public class JiangsuResponseQueryInfo {
    @JacksonXmlProperty(localName = "Datetime")
    private String datetime;

    @JacksonXmlProperty(localName = "Content")
    private Content content;


    private String CODE_OK = "0";

    private String ORDER_OK = "3";

    private String ORDER_WAIT_HANDLE = "7";
    private String ORDER_HANDLEING = "-9999";

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        @JacksonXmlProperty(localName = "retCode")
        private String retCode;
        @JacksonXmlProperty(localName = "retMsg")
        private String retMsg;
        @JacksonXmlProperty(localName = "status")
        private String status;
        @JacksonXmlProperty(localName = "statusMsg")
        private String statusMsg;
        @JacksonXmlProperty(localName = "failReason")
        private String failReason;
    }

    public boolean isOk() {
        return content != null && CODE_OK.equals(content.retCode) && ORDER_OK.equals(content.status);
    }

    public boolean reconfirm() {
        return content != null && CODE_OK.equals(content.retCode) && (ORDER_WAIT_HANDLE.equals(content.status) || ORDER_HANDLEING.equals(content.status));
    }


    public static final JiangsuResponseQueryInfo FAIL_RESULT = new JiangsuResponseQueryInfo();

}
