package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广东移动响应
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/26 17:09
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class GuangDongMobileOrderResult implements Serializable {
    public static final String  CODE_OK = "0";
    /**
     * respcode : 0
     * respdesc : 提交成功
     * resptype : 0
     */

    @JsonProperty("respcode")
    private String respcode;
    @JsonProperty("respdesc")
    private String respdesc;
    @JsonProperty("resptype")
    private String resptype;
    public boolean isOK(){
        return CODE_OK.equals(this.getRespcode());
    }
}
