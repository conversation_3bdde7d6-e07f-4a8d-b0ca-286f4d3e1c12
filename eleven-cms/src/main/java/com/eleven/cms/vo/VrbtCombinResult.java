package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: <EMAIL>
 * Date: 2019/12/16 11:24
 * Desc:远程调用接口返回json结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VrbtCombinResult {
    public static final String  CODE_OK = "000000";
    public static final String  CODE_FAIL = "999999";
    public static final String  MSG_OK = "成功";
    private static final VrbtCombinResult SUCC = new VrbtCombinResult(CODE_OK,MSG_OK);

    // funStatus 1:未开通视频彩铃功能。2：已开通视频彩铃功能，但视频彩铃播放功能处于关闭状态。3：已开通视频彩铃功能，且视频彩铃播放功能处于开启状态。
    public static final String  VRBT_FUN_STATUS_NONE = "1";
    //monthStatus 0:未订购视频彩铃包月或渠道视频彩铃包；1:已订购视频彩铃订阅包；2:已订购渠道视频彩铃包；3:已订购视频彩铃订阅包和渠道视频彩铃包。
    public  final static String VRBT_MONTH_STATUS_NONE = "0";   //未包月
    public  final static String VRBT_MONTH_STATUS_DYB = "1";   //已有订阅包
    public  final static String VRBT_MONTH_STATUS_QDB = "2";   //已有渠道包
    public  final static String VRBT_MONTH_STATUS_BOTH = "3";  //已同时有订阅包和渠道包

    //resCode	true	String	结果代码
    //resMsg	false	String	结果描述
    //000000	成功
    //100001	请求发生错误
    @JsonProperty("resCode")
    private String resCode;

    @JsonProperty("resMsg")
    private String resMsg;

    @JsonProperty("funStatus")
    private String funStatus;

    @JsonProperty("monthStatus")
    private String monthStatus;

    @JsonProperty("channelCode")
    private String channelCode;

    @JsonProperty("serviceId")
    private String serviceId;

    @JsonProperty("token")
    private String token;

    @JsonProperty("isp")
    private String isp;

    public boolean isOK(){
        return CODE_OK.equals(this.getResCode());
    }

    public boolean isFun() {
        return funStatus!=null && !VRBT_FUN_STATUS_NONE.equals(funStatus);
    }

    public boolean isMonth() {
        return monthStatus!=null && !VRBT_MONTH_STATUS_NONE.equals(monthStatus);
    }

    public VrbtCombinResult(String resCode, String resMsg) {
        this.resCode = resCode;
        this.resMsg = resMsg;
    }
    public VrbtCombinResult(String resCode, String resMsg,String funStatus,String monthStatus) {
        this.resCode = resCode;
        this.resMsg = resMsg;
        this.funStatus = funStatus;
        this.monthStatus = monthStatus;
    }

    public static VrbtCombinResult success(){
        return SUCC;
    }

    public static VrbtCombinResult success(String funStatus,String monthStatus){
        return new VrbtCombinResult(CODE_OK,MSG_OK,funStatus,monthStatus);
    }

    public static VrbtCombinResult success(String funStatus,String monthStatus,String channelCode,String serviceId,String token){
        return new VrbtCombinResult(CODE_OK,MSG_OK,funStatus,monthStatus,channelCode,serviceId,token,null);
    }

    public static VrbtCombinResult fail(String message){
        return new VrbtCombinResult(CODE_FAIL,message);
    }

}
