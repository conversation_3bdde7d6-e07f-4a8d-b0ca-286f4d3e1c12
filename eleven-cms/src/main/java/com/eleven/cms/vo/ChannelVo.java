package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2020/12/15 14:36
 * Desc: 渠道vo
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChannelVo {

    /**广告id参数名称*/
    private java.lang.String adIdParamName;
    /**前端需要加载的js*/
    private java.lang.String adJsUrl;
    /**广告id参数名称*/
    private java.lang.String adJsEval;
}
