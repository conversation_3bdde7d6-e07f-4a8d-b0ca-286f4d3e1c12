package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-11-08 10:38
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AnhuiResult {
    @JsonProperty("RETURN_CODE")
    private String resultCode;
    @JsonProperty("RETURN_MSG")
    private String resultMsg;
    @JsonProperty("keyid")
    private String keyId;
    @JsonProperty("prod_prcid")
    private String prodPrcid;
    @JsonProperty("exp_date")
    private String expDate;
    @JsonProperty("bff_date")
    private String bffDate;

    public static final AnhuiResult FAIL_RESULT = new AnhuiResult("999", "通讯失败", null, null, null, null);

    public boolean isOK() {
        return "0".equals(resultCode);
    }

}
