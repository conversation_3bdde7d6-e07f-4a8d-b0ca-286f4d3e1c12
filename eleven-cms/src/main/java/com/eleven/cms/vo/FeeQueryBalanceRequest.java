package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 话费充值余额查询结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/18 10:17
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeQueryBalanceRequest  implements Serializable {
    public static final String CODE_OK = "0";
    //0:成功；其他:失败
    @JsonProperty("code")
    private String code;
    //描述
    @JsonProperty("msg")
    private String msg;
    //商户号
    @JsonProperty("salerId")
    private String salerId;
    //商户名称
    @JsonProperty("salerName")
    private String salerName;
    //商户余额
    @JsonProperty("balance")
    private String balance;
    //商户冻结金额
    @JsonProperty("frozenAmount")
    private String frozenAmount;
    //可用余额
    @JsonProperty("canBalance")
    private String canBalance;
    //不可用余额
    @JsonProperty("noBalance")
    private String noBalance;

    @JsonProperty("sign")
    private String sign;
    public boolean isOK() {
        return CODE_OK.equals(code);
    }
}
