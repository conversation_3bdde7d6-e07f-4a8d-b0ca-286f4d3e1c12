package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-05-25 14:46
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangxiYidongNoPageWriteResult {

    private String ret;
    private String msg;
    private Data data;

    private static final String CALL_SUC_CODE = "0";
    private static final String RESULT_SUC_OK = "0";

    public static final JiangxiYidongNoPageWriteResult FAIL_RESULT = new JiangxiYidongNoPageWriteResult("9999", "通讯失败", null);

    @lombok.Data
    @NoArgsConstructor
    public static class Data {
        private ResultMsg resultMsg;
    }

    @lombok.Data
    @NoArgsConstructor
    public static class ResultMsg {
        private String respCode;
        private Result result;

    }
    @lombok.Data
    @NoArgsConstructor
    public static class Result {
        private String pdfBase64;

        @Override
        public String toString() {
            return "Result{" +
                    "pdfBase64=pdfBase64";
        }
    }


    public boolean isOk() {
        return CALL_SUC_CODE.equals(ret) && data != null && data.getResultMsg() != null &&
                RESULT_SUC_OK.equals(data.getResultMsg().getRespCode());
    }




}
