package com.eleven.cms.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-03-08 11:38
 */
@Data
@JacksonXmlRootElement(localName="Response")
@NoArgsConstructor
@AllArgsConstructor
public class JiangsuResponseToken {
    @JacksonXmlProperty(localName = "Datetime")
    private String datetime;

    @JacksonXmlProperty(localName = "Authorization")
    private Authorization authorization;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Authorization {
        @JacksonXmlProperty(localName = "Token")
        private String token;
    }
}
