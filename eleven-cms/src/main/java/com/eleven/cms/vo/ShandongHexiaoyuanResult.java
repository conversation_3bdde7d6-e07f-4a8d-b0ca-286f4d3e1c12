package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lihb
 * @create: 2024-3-14 16:08:59
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShandongHexiaoyuanResult {

    public static final String CODE_OK = "1";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("state")
    private String state;
    @JsonProperty("msg")
    private String msg;

    public boolean isOK() {
        return CODE_OK.equals(state);
    }

    public static ShandongHexiaoyuanResult fail() {
        return ShandongHexiaoyuanResult.builder().state(CODE_FAIL).msg(MSG_FAIL).build();
    }
}
