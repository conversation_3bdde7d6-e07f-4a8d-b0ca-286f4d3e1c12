package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 话费充值余额查询结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/18 10:17
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeQueryBalancePlusRequest implements Serializable {
    public static final int CODE_OK = 200;
    //0:成功；其他:失败
    @JsonProperty("code")
    private int code;
    //描述
    @JsonProperty("msg")
    private String msg;

    @JsonProperty("data")
    private Double data;

    public boolean isOK() {
        return CODE_OK==code;
    }
}
