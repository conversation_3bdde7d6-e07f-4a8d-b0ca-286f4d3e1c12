package com.eleven.cms.vo;

import com.eleven.cms.entity.ColumnMusic;
import com.eleven.cms.entity.Music;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description: 栏目
 * @Author: jeecg-boot
 * @Date:   2020-06-05
 * @Version: V1.0
 */
@Data
@ApiModel(value="cms_columnDetail对象", description="栏目")
public class ColumnDetail {

	/**主键id*/
	@ApiModelProperty(value = "主键id")
	private String id;
	/**名称*/
	@Excel(name = "名称", width = 15)
	@ApiModelProperty(value = "名称")
	private String title;
	/**排序*/
	@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "排序")
	private Integer priority;
	/**状态*/
	@JsonIgnore
	@Excel(name = "状态", width = 15)
	@ApiModelProperty(value = "状态")
	private Integer status;
	/**渠道*/
    @JsonIgnore
	@Excel(name = "渠道", width = 15)
	@ApiModelProperty(value = "渠道")
	private String channel;
	/**创建人*/
    @JsonIgnore
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建日期*/
    @JsonIgnore
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "创建日期")
	private Date createTime;
	/**更新人*/
    @JsonIgnore
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**更新日期*/
    @JsonIgnore
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "更新日期")
	private Date updateTime;
	/**所属部门编码*/
    @JsonIgnore
	@ApiModelProperty(value = "所属部门编码")
	private String sysOrgCode;

    @JsonIgnoreProperties({"crbtProductId","ringProductId","songProductId","listenProductId","musicType","musicStyle","hotLevel","playCount","expiryDate","createBy","createTime","updateBy","updateTime","sysOrgCode"})
    @ExcelCollection(name="栏目歌曲")
	@ApiModelProperty(value = "栏目歌曲")
	private List<Music> musicList;
	
}
