package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广东移动响应
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/10/26 17:09
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class GuangDongMobileSmsResult implements Serializable {
    public static final String  CODE_OK = "0";
    /**
     * result : {"smsseq":"20000002457540"}
     * respcode : 0
     * respdesc : 成功
     * resptype : 0
     */

    @JsonProperty("result")
    private Result result;
    @JsonProperty("respcode")
    private String respcode;
    @JsonProperty("respdesc")
    private String respdesc;
    @JsonProperty("resptype")
    private String resptype;
    public boolean isOK(){
        return CODE_OK.equals(this.getRespcode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Result implements Serializable {
        /**
         * smsseq : 20000002457540
         */

        @JsonProperty("smsseq")
        private String smsseq;
    }
}
