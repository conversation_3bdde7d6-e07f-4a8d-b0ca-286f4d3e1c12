package com.eleven.cms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * Author: ye<PERSON><EMAIL>
 * Date: 2020/6/29 17:01
 * Desc: 视频彩铃产品信息
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class VrbtProduct implements Serializable {
    private static final long serialVersionUID = 1L;

    private String copyrightId;
    /**歌曲名*/
    private String musicName;
    /**视频彩铃产品id*/
    private String vrbtProductId;
    /**视频彩铃预览图地址*/
    private String vrbtImg;
    /**视频彩铃播放地址*/
    private String vrbtVideo;
    /*有效期*/
    private String expiryDate;
    /**状态:0=无效,1=有效*/
    private Integer status;

}
