package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/8/28 15:21
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianTongRingLibraryResp implements Serializable {

    /**
     * description : 查询成功
     * returnCode : 0000
     * list : [{"ringId":"9387700020200605229583","ringName":"缘无份(会员版)","channelId":"3000000188","ringType":"1","isVideo":"0","sourceType":"3","subscriptionTime":"20200806165209","effectiveDate":"20200806165209","expireDate":"2099-01-01","setType":1,"timeType":"1","userSettingIndex":602499,"clickUp":0,"clickDown":0,"callType":1}]
     */

    @JsonProperty("description")
    private String description;
    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("list")
    private java.util.List<List> list;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class List implements Serializable {
        /**
         * ringId : 9387700020200605229583
         * ringName : 缘无份(会员版)
         * channelId : 3000000188
         * ringType : 1
         * isVideo : 0
         * sourceType : 3
         * subscriptionTime : 20200806165209
         * effectiveDate : 20200806165209
         * expireDate : 2099-01-01
         * setType : 1
         * timeType : 1
         * userSettingIndex : 602499
         * clickUp : 0
         * clickDown : 0
         * callType : 1
         */

        @JsonProperty("ringId")
        private String ringId;
        @JsonProperty("ringName")
        private String ringName;
        @JsonProperty("channelId")
        private String channelId;
        @JsonProperty("ringType")
        private String ringType;
        @JsonProperty("isVideo")
        private String isVideo;
        @JsonProperty("sourceType")
        private String sourceType;
        @JsonProperty("subscriptionTime")
        private String subscriptionTime;
        @JsonProperty("effectiveDate")
        private String effectiveDate;
        @JsonProperty("expireDate")
        private String expireDate;
        @JsonProperty("setType")
        private int setType;
        @JsonProperty("timeType")
        private String timeType;
        @JsonProperty("userSettingIndex")
        private int userSettingIndex;
        @JsonProperty("clickUp")
        private int clickUp;
        @JsonProperty("clickDown")
        private int clickDown;
        @JsonProperty("callType")
        private int callType;
    }
}
