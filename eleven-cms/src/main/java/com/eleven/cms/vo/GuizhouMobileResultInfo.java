package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GuizhouMobileResultInfo {

    @JsonProperty("busiDec")
    private String busiDec;
    @JsonProperty("busiName")
    private String busiName;
    @JsonProperty("busiStatus")
    private String busiStatus;
    @JsonProperty("busiType")
    private String busiType;
    @JsonProperty("businessMapId")
    private String businessMapId;
}
