package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 天翼通讯助理订购回调
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/15 17:16
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TianyiCommAssistRequest implements Serializable {
    /**
     * orderNumber : CRM2CO202311161638229616919125
     * phone : 18080928200
     * requestId : CDYL170012385567253489
     * orderStatus : 1
     */

    @JsonProperty("orderNumber")
    private String orderNumber;
    //手机号
    @JsonProperty("phone")
    private String phone;
    //订单号
    @JsonProperty("requestId")
    private String requestId;
    //订购状态（1：已订购2：已退订3：订购失败4: 退订失败 0:未知）
    @JsonProperty("orderStatus")
    private String orderStatus;
}
