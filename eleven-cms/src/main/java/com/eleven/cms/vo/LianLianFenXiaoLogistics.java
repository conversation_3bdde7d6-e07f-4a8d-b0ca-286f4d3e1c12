package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 10:31
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoLogistics implements Serializable {

    /**
     * status : 0
     * msg : ok
     * expressNo : 221022000063
     * logisticsEnterprise : 中通快递
     * expressStatus : 1
     * deliveryType : 1
     * address : 1
     * customerName : 段文超
     * businessPhone :
     * updateTime : 2022-10-22 10:56:56
     * result : {"number":"73189717049701","type":"ZTO","deliverystatus":"3","issign":"0","expName":"中通快递","expSite":"www.zto.com ","expPhone":"95311","logo":"https://img3.fegine.com/express/zto.jpg","courier":"","courierPhone":"***********","updateTime":"2022-09-07 21:22:49","takeTime":"3天11小时21分","list":[{"time":"2022-09-07 21:22:49","status":"【宁德市】 您的快递已签收, 签收人在【多多买菜的郑岐惠客多超市】(郑岐村光明路21号)领取。如有疑问请电联:（***********）, 投诉电话:（***********）, 您的快递已经妥投。风里来雨里去, 只为客官您满意。上有老下有小, 赏个好评好不好？【请在评价快递员处帮忙点亮五颗星星哦~】"},{"time":"2022-09-07 16:54:10","status":"【宁德市】 快件已被【多多买菜的郑岐惠客多超市】代收，如有问题请电联（***********），感谢使用中通快递，期待再次为您服务！"},{"time":"2022-09-07 16:35:55","status":"【宁德市】 【宁德】 的张灯（***********） 正在第1次派件, 请保持电话畅通,并耐心等待（95720为中通快递员外呼专属号码，请放心接听）"},{"time":"2022-09-07 14:04:09","status":"【宁德市】 快件已经到达 【宁德】"},{"time":"2022-09-07 11:56:07","status":"【福州市】 快件离开 【福州中转】 已发往 【宁德】"},{"time":"2022-09-07 10:40:07","status":"【福州市】 快件已经到达 【福州中转】"},{"time":"2022-09-05 19:04:40","status":"【西安市】 快件离开 【西安中转】 已发往 【福州中转】"},{"time":"2022-09-05 19:02:50","status":"【西安市】 快件已经到达 【西安中转】"},{"time":"2022-09-05 19:02:49","status":"【西安市】 快件离开 【西安市场部】 已发往 【西安中转】"},{"time":"2022-09-04 10:01:46","status":"【西安市】 【西安市场部】（029-87964533） 的 王美玲（18710994844） 已揽收"}]}
     */

    @JsonProperty("status")
    private String status;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("expressNo")
    private String expressNo;
    @JsonProperty("logisticsEnterprise")
    private String logisticsEnterprise;
    @JsonProperty("expressStatus")
    private int expressStatus;
    @JsonProperty("deliveryType")
    private int deliveryType;
    @JsonProperty("address")
    private String address;
    @JsonProperty("customerName")
    private String customerName;
    @JsonProperty("businessPhone")
    private String businessPhone;
    @JsonProperty("updateTime")
    private String updateTime;
    @JsonProperty("result")
    private Result result;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Result implements Serializable {
        /**
         * number : 73189717049701
         * type : ZTO
         * deliverystatus : 3
         * issign : 0
         * expName : 中通快递
         * expSite : www.zto.com
         * expPhone : 95311
         * logo : https://img3.fegine.com/express/zto.jpg
         * courier :
         * courierPhone : ***********
         * updateTime : 2022-09-07 21:22:49
         * takeTime : 3天11小时21分
         * list : [{"time":"2022-09-07 21:22:49","status":"【宁德市】 您的快递已签收, 签收人在【多多买菜的郑岐惠客多超市】(郑岐村光明路21号)领取。如有疑问请电联:（***********）, 投诉电话:（***********）, 您的快递已经妥投。风里来雨里去, 只为客官您满意。上有老下有小, 赏个好评好不好？【请在评价快递员处帮忙点亮五颗星星哦~】"},{"time":"2022-09-07 16:54:10","status":"【宁德市】 快件已被【多多买菜的郑岐惠客多超市】代收，如有问题请电联（***********），感谢使用中通快递，期待再次为您服务！"},{"time":"2022-09-07 16:35:55","status":"【宁德市】 【宁德】 的张灯（***********） 正在第1次派件, 请保持电话畅通,并耐心等待（95720为中通快递员外呼专属号码，请放心接听）"},{"time":"2022-09-07 14:04:09","status":"【宁德市】 快件已经到达 【宁德】"},{"time":"2022-09-07 11:56:07","status":"【福州市】 快件离开 【福州中转】 已发往 【宁德】"},{"time":"2022-09-07 10:40:07","status":"【福州市】 快件已经到达 【福州中转】"},{"time":"2022-09-05 19:04:40","status":"【西安市】 快件离开 【西安中转】 已发往 【福州中转】"},{"time":"2022-09-05 19:02:50","status":"【西安市】 快件已经到达 【西安中转】"},{"time":"2022-09-05 19:02:49","status":"【西安市】 快件离开 【西安市场部】 已发往 【西安中转】"},{"time":"2022-09-04 10:01:46","status":"【西安市】 【西安市场部】（029-87964533） 的 王美玲（18710994844） 已揽收"}]
         */

        @JsonProperty("number")
        private String number;
        @JsonProperty("type")
        private String type;
        @JsonProperty("deliverystatus")
        private String deliverystatus;
        @JsonProperty("issign")
        private String issign;
        @JsonProperty("expName")
        private String expName;
        @JsonProperty("expSite")
        private String expSite;
        @JsonProperty("expPhone")
        private String expPhone;
        @JsonProperty("logo")
        private String logo;
        @JsonProperty("courier")
        private String courier;
        @JsonProperty("courierPhone")
        private String courierPhone;
        @JsonProperty("updateTime")
        private String updateTime;
        @JsonProperty("takeTime")
        private String takeTime;
        @JsonProperty("list")
        private java.util.List<List> list;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class List implements Serializable {
            /**
             * time : 2022-09-07 21:22:49
             * status : 【宁德市】 您的快递已签收, 签收人在【多多买菜的郑岐惠客多超市】(郑岐村光明路21号)领取。如有疑问请电联:（***********）, 投诉电话:（***********）, 您的快递已经妥投。风里来雨里去, 只为客官您满意。上有老下有小, 赏个好评好不好？【请在评价快递员处帮忙点亮五颗星星哦~】
             */

            @JsonProperty("time")
            private String time;
            @JsonProperty("status")
            private String status;
        }
    }
}
