package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HenanMobileResult {

    public static final String CODE_OK = "00000";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("result")
    private String result;
    @JsonProperty("respDesc")
    private String respDesc;
    @JsonProperty("respCode")
    private String respCode;

    public boolean isOK() {
        return CODE_OK.equals(respCode);
    }

    public static HenanMobileResult fail() {
        return HenanMobileResult.builder().respCode(CODE_FAIL).respDesc(MSG_FAIL).build();
    }
}
