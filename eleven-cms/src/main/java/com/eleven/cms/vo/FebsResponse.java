package com.eleven.cms.vo;

import com.eleven.cms.util.BizConstant;
import org.springframework.http.HttpStatus;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class FebsResponse extends HashMap<String, Object> {

    private static final long serialVersionUID = -8713837118340960775L;

    public FebsResponse code(HttpStatus status) {
        this.put("code", status.value());
        return this;
    }
    public FebsResponse code(String code) {
        this.put("code", code);
        return this;
    }
    public FebsResponse message(String message) {
        this.put("message", message);
        return this;
    }

    public FebsResponse data(Object data) {
        this.put("data", data);
        return this;
    }
    public FebsResponse payTime(Object payTime) {
        this.put("payTime", payTime);
        return this;
    }
    public FebsResponse days(Object days) {
        this.put("days", days);
        return this;
    }
    public FebsResponse success() {
        this.code(HttpStatus.OK);
        return this;
    }

    public FebsResponse fail() {
        this.code(HttpStatus.INTERNAL_SERVER_ERROR);
        return this;
    }

    /**
     * 需要登录
     * @return
     */
    public FebsResponse needLogin() {
        this.code(HttpStatus.NETWORK_AUTHENTICATION_REQUIRED);
        return this;
    }

    @Override
    public FebsResponse put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    /**
     * 需要登录
     */
    public FebsResponse noauth() {
        this.put("code", 510);
        return this;
    }

    /**
     * 验证码错误
     */
    public FebsResponse captchaErr() {
        this.put("code", 511);
        return this;
    }

    public FebsResponse error(String msg) {
        this.code(HttpStatus.NETWORK_AUTHENTICATION_REQUIRED);
        this.message(msg);
        return this;
    }

    public FebsResponse fail(String msg) {
        this.code(HttpStatus.INTERNAL_SERVER_ERROR);
        this.message(msg);
        return this;
    }


    public FebsResponse success(String msg) {
        this.code(HttpStatus.OK);
        this.message(msg);
        return this;
    }
    /**
     * 判断是否成功
     */
    public boolean isOK() {
        String code=this.get("code").toString();
        return code.equals(String.valueOf(HttpStatus.OK.value()));
    }
    //已领取会员状态
    public FebsResponse repeatRecharge() {
        this.code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE);
        this.message(BizConstant.RECEIVE_MSG);
        return this;
    }
    //已领取会员状态（需要显示权益名称）
    public FebsResponse repeatRecharge(String couponName) {
        this.code(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE);
        String msg = BizConstant.RECEIVE_MSG.replace("会员",couponName);
        this.message(msg);
        return this;
    }
    //1小时内充值到账状态
    public FebsResponse readyRecharge(String couponName) {
        this.code(HttpStatus.UNSUPPORTED_MEDIA_TYPE);
        String msg = BizConstant.SCHEDULED_MSG.replace("会员",couponName);
        this.message(msg);
        return this;
    }

    //非会员状态
    public FebsResponse notMember() {
        this.code(HttpStatus.EXPECTATION_FAILED);
        this.message(BizConstant.MEMBER_MSG);
        return this;
    }

    //马上充值到账状态
    public FebsResponse fastRecharge(String couponName) {
        this.code(HttpStatus.LENGTH_REQUIRED);
        String msg = BizConstant.RECHARGE_MSG.replace("会员",couponName);
        this.message(msg);
        return this;
    }
}
