package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-06-24 10:48
 * 
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WujiongCrackResult {
    private String resultCode;
    private String resultMsg;
    @JsonProperty("orderid")
    private String orderId;
    @JsonProperty("sid")
    private String sid;
    private String key;
    private String code;
    private String msg;
    private String rescode;
    private String tradeid;


    public static final WujiongCrackResult FAIL_RESULT = new WujiongCrackResult("999999", "通讯失败", "", "", "", "999999", "","999999","");

    public static final String GET_OK = "S111003";
    public static final String SUB_OK = "200000";


    public static final String HETU_GET_OK = "200000";
    public static final String HETU_SUB_OK = "200";

    public static final String DX_VRBT_GET_OK = "0";
    public static final String DX_VRBT_SUB_OK = "0";

    public boolean isGetOk() {
        return GET_OK.equals(resultCode);
    }

    public boolean isSubOk(){
        return SUB_OK.equals(resultCode);
    }

    public boolean isHetuGetOk() {
        return HETU_GET_OK.equals(code);
    }

    public boolean isHetuSubOk(){
        return HETU_SUB_OK.equals(code);
    }

    public boolean isDxVrbtGetOk() {
        return DX_VRBT_GET_OK.equals(rescode);
    }

    public boolean isDxVrbtSubOk(){
        return DX_VRBT_SUB_OK.equals(rescode);
    }
}
