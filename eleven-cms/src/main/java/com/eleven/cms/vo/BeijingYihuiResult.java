package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BeijingYihuiResult {

    public static final String CODE_OK = "1";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("status")
    private String status;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("linkid")
    private String linkid;

    public boolean isOK() {
        return CODE_OK.equals(status);
    }

    public static BeijingYihuiResult fail() {
        return BeijingYihuiResult.builder().status(CODE_FAIL).msg(MSG_FAIL).build();
    }
}
