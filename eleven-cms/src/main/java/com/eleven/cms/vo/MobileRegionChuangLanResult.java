package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/20 16:43
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MobileRegionChuangLanResult implements Serializable {
    public static final String IS_RESULT = "1";   // 是否携号转网转网 0 未转网 1 已转网
    public static final String ISP_WEIZHI = "-1";   // 未知
    public static final String ISP_YIDONG = "1";   // 移动
    public static final String ISP_LIANTONG = "2";   // 联通
    public static final String ISP_DIANXIN = "3"; // 电信
    public static final String ISP_GUANGDIAN = "4";  //广电


    /**
     * chargeStatus : 1
     * message : 成功
     * data : {"batchNo":"1330940429755092992","queryResult":[{"result":"0","before":"1","mobile":"15915401656","after":"1"}]}
     * code : 200000
     */

    @JsonProperty("chargeStatus")
    private int chargeStatus;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("code")
    private String code;
    public boolean isOK() {
        return this.getData()!=null && !this.getData().getQueryResult().isEmpty();
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * batchNo : 1330940429755092992
         * queryResult : [{"result":"0","before":"1","mobile":"15915401656","after":"1"}]
         */

        @JsonProperty("batchNo")
        private String batchNo;
        @JsonProperty("queryResult")
        private List<QueryResult> queryResult;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @lombok.Data
        public static class QueryResult implements Serializable {
            /**
             * result : 0
             * before : 1
             * mobile : 15915401656
             * after : 1
             */

            @JsonProperty("result")
            private String result;
            @JsonProperty("before")
            private String before;
            @JsonProperty("mobile")
            private String mobile;
            @JsonProperty("after")
            private String after;
            public boolean isResult(){
                return IS_RESULT.equals(result);
            }
        }
    }
}
