package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/22 16:00
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XuTongYiDongResponse implements Serializable {
    /**
     * error : 0
     * errorMsg : 操作成功
     */

    @JsonProperty("error")
    private Integer error;
    @JsonProperty("errorMsg")
    private String errorMsg;
}
