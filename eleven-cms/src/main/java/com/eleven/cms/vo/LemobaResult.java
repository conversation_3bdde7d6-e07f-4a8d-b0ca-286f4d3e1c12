package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 乐摩吧返回封装
 *
 * @author: cai lei
 * @create: 2021-12-23 15:08
 */

@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LemobaResult {
    private Integer code;
    private String msg;
    private String data;
    public static final LemobaResult FAIL_RESULT = new LemobaResult(9999, "通讯失败", null);

    public boolean isResultOK() {
        return 1 == code;
    }

    public static LemobaResult fail() {
        return FAIL_RESULT;
    }
}
