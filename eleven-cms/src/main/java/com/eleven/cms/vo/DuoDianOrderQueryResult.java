package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/2 12:08
 **/
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DuoDianOrderQueryResult implements Serializable {
    public static final String CODE_OK = "0000";
    /**
     * code : 0000
     * result :
     * time : 1560909762142
     * data : {}
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("result")
    private String result;
    @JsonProperty("time")
    private long time;
    @JsonProperty("data")
    private Data data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * businessNo : xxx
         * timeStamp : xxx
         */

        @JsonProperty("businessNo")
        private String businessNo;
        @JsonProperty("timeStamp")
        private String timeStamp;
        @JsonProperty("status")
        private String status;
        @JsonProperty("outTradeNo")
        private String outTradeNo;
    }
    public boolean isOK() {
        return CODE_OK.equals(this.getCode()) && this.getData()!=null && "1".equals(this.getData().getStatus());
    }

    public static final DuoDianOrderQueryResult FAIL_RESULT = new DuoDianOrderQueryResult("9999",null, "通讯失败", 0,null);
}
