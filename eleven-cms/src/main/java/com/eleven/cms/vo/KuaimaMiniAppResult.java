package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-06-07 14:49
 */

@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KuaimaMiniAppResult {
    private String result;
    private String msg;
    @JsonProperty("orderid")
    private String orderId;
    private String data;

    private static final String SUC_CODE = "0";
    public static final KuaimaMiniAppResult FAIL_RESULT = new KuaimaMiniAppResult("9999", "通讯失败", null,null);

    public boolean isOk() {
        return SUC_CODE.equals(result);
    }
}
