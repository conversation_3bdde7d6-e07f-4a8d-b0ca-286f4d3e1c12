package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 10:07
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoRefundOrder implements Serializable {

    /**
     * channelId : 4
     * thirdOrderId : 1671588991573027
     * channelOrderId : C221221000057000004
     * productId : 3288990
     * thirdUserId :
     * refundAmount : 15
     * chargeAmount : 0
     * refundTime : 2022-12-21 11:02:06
     * refundList : [{"orderId":"S221206000034","corderId":"S221206000034","applyAmount":15,"refundAmount":15,"chargeAmount":0,"refundReason":"主动退款","rejectReason":"","rejectReasonType":-1}]
     * rejectRefundList : [{"orderId":"S221206000035","corderId":"S221206000035","applyAmount":15,"refundAmount":0,"chargeAmount":0,"refundReason":"接口退款","rejectReason":"当前产品,不允许退款！","rejectReasonType":1102}]
     */

    @JsonProperty("channelId")
    private int channelId;
    @JsonProperty("thirdOrderId")
    private String thirdOrderId;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("productId")
    private int productId;
    @JsonProperty("thirdUserId")
    private String thirdUserId;
    @JsonProperty("refundAmount")
    private int refundAmount;
    @JsonProperty("chargeAmount")
    private int chargeAmount;
    @JsonProperty("refundTime")
    private String refundTime;
    @JsonProperty("refundList")
    private List<RefundList> refundList;
    @JsonProperty("rejectRefundList")
    private List<RejectRefundList> rejectRefundList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class RefundList implements Serializable {
        /**
         * orderId : S221206000034
         * corderId : S221206000034
         * applyAmount : 15
         * refundAmount : 15
         * chargeAmount : 0
         * refundReason : 主动退款
         * rejectReason :
         * rejectReasonType : -1
         */

        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("corderId")
        private String corderId;
        @JsonProperty("applyAmount")
        private int applyAmount;
        @JsonProperty("refundAmount")
        private int refundAmount;
        @JsonProperty("chargeAmount")
        private int chargeAmount;
        @JsonProperty("refundReason")
        private String refundReason;
        @JsonProperty("rejectReason")
        private String rejectReason;
        @JsonProperty("rejectReasonType")
        private int rejectReasonType;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class RejectRefundList implements Serializable {
        /**
         * orderId : S221206000035
         * corderId : S221206000035
         * applyAmount : 15
         * refundAmount : 0
         * chargeAmount : 0
         * refundReason : 接口退款
         * rejectReason : 当前产品,不允许退款！
         * rejectReasonType : 1102
         */

        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("corderId")
        private String corderId;
        @JsonProperty("applyAmount")
        private int applyAmount;
        @JsonProperty("refundAmount")
        private int refundAmount;
        @JsonProperty("chargeAmount")
        private int chargeAmount;
        @JsonProperty("refundReason")
        private String refundReason;
        @JsonProperty("rejectReason")
        private String rejectReason;
        @JsonProperty("rejectReasonType")
        private int rejectReasonType;
    }
}
