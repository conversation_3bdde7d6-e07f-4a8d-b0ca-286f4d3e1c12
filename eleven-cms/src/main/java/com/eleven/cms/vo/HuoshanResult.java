package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2023-5-25 16:20:37
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HuoshanResult {

    public static final String CODE_OK = "103000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    /*103000代表成功，其它代表异常*/
    @JsonProperty("resultCode")
    private String resultCode;
    @JsonProperty("desc")
    private String desc;
    @JsonProperty("serviceTime")
    private String serviceTime;
    @JsonProperty("data")
    private ResultData data;

    public boolean isOK() {
        return CODE_OK.equals(resultCode);
    }

    public static HuoshanResult fail() {
        return HuoshanResult.builder().resultCode(CODE_FAIL).desc(MSG_FAIL).build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultData{
        @JsonProperty("msisdn")
        private String msisdn;
        @JsonProperty("expandParams")
        private String expandParams;
        @JsonProperty("securityPhone")
        private String securityPhone;
        private String phone;
    }
}
