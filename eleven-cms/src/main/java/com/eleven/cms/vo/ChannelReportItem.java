package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * Author: <EMAIL>
 * Date: 2020/10/28 15:04
 * Desc:Todo
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChannelReportItem {

    /**
     * channel : hlw8001
     * OpenPage : 0
     * SendSMS : 0
     * VerifySMS : 0
     * Success : 0
     * Date : 2020-10-21
     */

    @JsonProperty("channel")
    private String channel;
    @JsonProperty("PageView")
    private int pageView;
    @JsonProperty("UniqueVisitor")
    private int uniqueVisitor;
    @JsonProperty("SendSMS")
    private int sendSMS;
    @JsonProperty("VerifySMS")
    private int verifySMS;
    @JsonProperty("Success")
    private int success;
    @JsonProperty("Date")
    private LocalDate date;

}
