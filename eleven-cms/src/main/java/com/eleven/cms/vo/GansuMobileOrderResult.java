package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class GansuMobileOrderResult implements Serializable {

    public static final String CODE_OK = "111000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    /**
     * data : {"orderStatus":"0","orderValidDate":"2024-03-05 17:10:37","orderExpireDate":"2024-03-12 17:10:37"}
     * respMsg : 成功
     * retCode : 111000
     */

    @JsonProperty("data")
    private Data data;
    @JsonProperty("respMsg")
    private String respMsg;
    @JsonProperty("retCode")
    private String retCode;

    public boolean isOK() {
        return CODE_OK.equals(retCode);
    }

    public static GansuMobileOrderResult fail() {
        return GansuMobileOrderResult.builder().retCode(CODE_FAIL).respMsg(MSG_FAIL).build();
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * orderStatus : 0
         * orderValidDate : 2024-03-05 17:10:37
         * orderExpireDate : 2024-03-12 17:10:37
         */

        @JsonProperty("orderStatus")
        private String orderStatus;
        @JsonProperty("orderValidDate")
        private String orderValidDate;
        @JsonProperty("orderExpireDate")
        private String orderExpireDate;
    }
}
