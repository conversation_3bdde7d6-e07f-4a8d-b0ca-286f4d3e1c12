package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HenanMobileSmsResult {

    public static final String CODE_OK = "1";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private ResultData data;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static HenanMobileSmsResult fail() {
        return HenanMobileSmsResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }

    @Data
    public static class ResultData {
        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("entryAllFlag")
        private String entryAllFlag;
    }
}
