package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/12/5 14:09
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SiChuanExclusiveCardResult implements Serializable {
    /**
     * code : 200
     * msg : SUCCESS
     * data : {"freeUrl":"https://www.118114.net/h5/marketing/#/verification? channel=M007&phone=roekf2P7hKoMpjYb98ogUg=="}
     * timestamp : 1701671315182
     */

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("timestamp")
    private Long timestamp;
    public boolean isOK(){
        return this.getData()!=null && StringUtils.isNotBlank(this.getData().getFreeUrl());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * freeUrl : https://www.118114.net/h5/marketing/#/verification? channel=M007&phone=roekf2P7hKoMpjYb98ogUg==
         */

        @JsonProperty("freeUrl")
        private String freeUrl;
    }
}
