package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/1/17 10:34
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SichuanMobileRechargeResult implements Serializable {

    public static final String CODE_OK = "0000";

    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";
    public static final String STATUS_SUCCESS = "1";
    /**
     * result : {"time":"2025-01-24 19:40:11","phone":"183****8272","retStatus":1,"status":"true","orderId":"1000042720250124193941000008"}
     * res_code : 0000
     * res_msg : 成功
     */

    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;

    @JsonProperty("data")
    private Object data;
    public boolean isOK() {
        return CODE_OK.equals(this.getCode()) ;
    }


    public static SichuanMobileRechargeResult fail() {
        return SichuanMobileRechargeResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }
}
