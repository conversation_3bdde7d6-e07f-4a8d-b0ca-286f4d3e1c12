package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/30 10:09
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ZhongHeData implements Serializable {

    /**
     * OrderNo : ZHYKT20161111182550
     * Mobile : 18900000001
     * Face : 200
     * StatusCode : 0
     * StatusMessage : 成功
     * FeedbackTime : 20191022135422
     */

    @JsonProperty("OrderNo")
    private String OrderNo;
    @JsonProperty("CPOrderNo")
    private String CPOrderNo;


    @JsonProperty("Mobile")
    private String Mobile;
    @JsonProperty("Face")
    private int Face;
    @JsonProperty("StatusCode")
    private int StatusCode;
    @JsonProperty("StatusMessage")
    private String StatusMessage;
    @JsonProperty("FeedbackTime")
    private String FeedbackTime;
}
