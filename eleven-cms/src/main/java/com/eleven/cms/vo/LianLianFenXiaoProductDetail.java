package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/15 10:23
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoProductDetail implements Serializable {

    /**
     * productId : 954855
     * onlyName : 粉小越东南亚料理2店通用套餐
     * title : 粉小越东南亚料理丨两店适用
     * productSimpleDesc : 【粉小越东南亚料理丨绿地伊藤店、合生汇店】地道东南亚风味，舌尖优享，99元购【粉小越双人精致餐】，168元购【3-4人欢聚套餐】冬阴功汤、特色海鲜菠萝炒饭、马来咖喱牛腩面包、泰式凤爪、越南传统芒果鲜虾米纸卷、泰式青柠蒸鲈鱼、泰式街头炸虾片等
     * shareText : 【粉小越东南亚料理丨绿地伊藤店、合生汇店】地道东南亚风味，舌尖优享，99元购【粉小越双人精致餐】，168元购【3-4人欢聚套餐】冬阴功汤、特色海鲜菠萝炒饭、马来咖喱牛腩面包、泰式凤爪、越南传统芒果鲜虾米纸卷、泰式青柠蒸鲈鱼、泰式街头炸虾片等
     * channelMallImg :
     * saleAmount : 226
     * stockAmount : 1198
     * itemStock : 0
     * salePrice : 9900
     * originPrice : 26400
     * isSoldOut : 0
     * faceImg : https://cdn.lianlianlvyou.com/lianlian/common/5329104c9b8c4995ada8dab21a8148ac.jpg
     * shareImg : https://cdn.lianlianlvyou.com/lianlian/common/5329104c9b8c4995ada8dab21a8148ac.jpg
     * attention :
     * bookingType : 1
     * bookingShowAddress : 0
     * orderShowIdCard : 0
     * orderShowDate : 0
     * beginTime : 2023-04-20 00:00:00
     * endTime : 2023-05-19 15:59:59
     * validBeginDate : 2023-04-19 16:00:00
     * validEndDate : 2023-06-30 15:59:59
     * bookingBeginDate : null
     * releaseTime : 2023-04-20 01:47:18
     * singleMin : 1
     * singleMax : 10
     * ecommerce : 0
     * categoryPath : 餐饮类-异域菜-其他东南亚菜
     * productCategoryId : 141
     * categoryName : 其他东南亚菜
     * qualificationsList : ["http://ll-oss-contract.oss-cn-beijing.aliyuncs.com/ll-center/merchant/9215225370.jpg?Expires=1682047796&OSSAccessKeyId=LTAI5tRSgidRx1faBSfwZniV&Signature=D9tLjN%2BntQZTM6xy4fH%2BrH4wOm8%3D"]
     * itemList : [{"itemId":1505581,"subTitle":"工作日特惠双人套餐","salePrice":9900,"originPrice":26400,"channelPrice":9255,"stock":10,"singleMax":10,"codeAmount":1}]
     * shopList : [{"id":521535,"name":"粉小越东南亚料理","latitude":"30.605176","longitude":"104.155131","cityCode":"510100","address":"喜树街35号1层伊藤广场6F-TY010","phoneNumber":"02885550521"}]
     * noticeVOList : [{"titles":"预约方式","option":[{"optionName":"至少提前1天短信网址预约，高峰期需等位"}]}]
     * productItemsContentMap : {"1505582":[{"id":10902598,"productId":954855,"productItemId":1505582,"contentDesc":"冬阴功汤（小）\n泰式凤爪\n泰式街头炸虾片\n越南传统芒果鲜虾米纸卷\n特色海鲜菠萝炒饭\n泰式青柠蒸鲈鱼\n马来咖喱牛腩面包\n白灼菜心 \n甜品：椰汁西米糕（3～4） \n饮品：柠檬冰红茶（3～4）","numberAndUnit":"1","originPrice":55400,"createDate":"2023-04-19 11:17:28","updateTime":"2023-04-19 11:17:28"}]}
     * imgList : [{"url":"https://cdn2.lianlianlvyou.com/lianlian/common/0cd2169017df499ebd8ddf3ff65cea30.jpg","sort":0}]
     */

    @JsonProperty("productId")
    private Integer productId;
    @JsonProperty("onlyName")
    private String onlyName;
    @JsonProperty("title")
    private String title;
    @JsonProperty("productSimpleDesc")
    private String productSimpleDesc;
    @JsonProperty("shareText")
    private String shareText;
    @JsonProperty("channelMallImg")
    private String channelMallImg;
    @JsonProperty("saleAmount")
    private Integer saleAmount;
    @JsonProperty("stockAmount")
    private Integer stockAmount;
    @JsonProperty("itemStock")
    private Integer itemStock;
    @JsonProperty("salePrice")
    private Integer salePrice;
    @JsonProperty("originPrice")
    private Integer originPrice;
    @JsonProperty("isSoldOut")
    private Integer isSoldOut;
    @JsonProperty("codeType")
    private Integer codeType;
    @JsonProperty("faceImg")
    private String faceImg;
    @JsonProperty("shareImg")
    private String shareImg;
    @JsonProperty("attention")
    private String attention;
    @JsonProperty("bookingType")
    private Integer bookingType;
    @JsonProperty("bookingShowAddress")
    private Integer bookingShowAddress;
    @JsonProperty("orderShowIdCard")
    private Integer orderShowIdCard;
    @JsonProperty("orderShowDate")
    private Integer orderShowDate;
    @JsonProperty("beginTime")
    private String beginTime;
    @JsonProperty("endTime")
    private String endTime;
    @JsonProperty("validBeginDate")
    private String validBeginDate;
    @JsonProperty("validEndDate")
    private String validEndDate;
    @JsonProperty("bookingBeginDate")
    private String bookingBeginDate;
    @JsonProperty("releaseTime")
    private String releaseTime;
    @JsonProperty("singleMin")
    private Integer singleMin;
    @JsonProperty("singleMax")
    private Integer singleMax;
    @JsonProperty("ecommerce")
    private Integer ecommerce;
    @JsonProperty("categoryPath")
    private String categoryPath;
    @JsonProperty("productCategoryId")
    private Integer productCategoryId;
    @JsonProperty("categoryName")
    private String categoryName;
    @JsonProperty("productItemsContentMap")
    private Map<String, List<ProductItemsContentMap>> productItemsContentMap;
    @JsonProperty("qualificationsList")
    private List<String> qualificationsList;
    @JsonProperty("itemList")
    private List<ItemList> itemList;
    @JsonProperty("shopList")
    private List<ShopList> shopList;
    @JsonProperty("noticeVOList")
    private List<NoticeVOList> noticeVOList;
    @JsonProperty("imgList")
    private List<ImgList> imgList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ProductItemsContentMap implements Serializable {
        /**
         * id : 10902598
         * productId : 954855
         * productItemId : 1505582
         * contentDesc : 冬阴功汤（小）
         泰式凤爪
         泰式街头炸虾片
         越南传统芒果鲜虾米纸卷
         特色海鲜菠萝炒饭
         泰式青柠蒸鲈鱼
         马来咖喱牛腩面包
         白灼菜心
         甜品：椰汁西米糕（3～4）
         饮品：柠檬冰红茶（3～4）
         * numberAndUnit : 1
         * originPrice : 55400
         * createDate : 2023-04-19 11:17:28
         * updateTime : 2023-04-19 11:17:28
         */

        @JsonProperty("id")
        private Integer id;
        @JsonProperty("productId")
        private Integer productId;
        @JsonProperty("productItemId")
        private Integer productItemId;
        @JsonProperty("contentDesc")
        private String contentDesc;
        @JsonProperty("numberAndUnit")
        private String numberAndUnit;
        @JsonProperty("originPrice")
        private Integer originPrice;
        @JsonProperty("createDate")
        private String createDate;
        @JsonProperty("updateTime")
        private String updateTime;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ItemList implements Serializable {
        /**
         * itemId : 1505581
         * subTitle : 工作日特惠双人套餐
         * salePrice : 9900
         * originPrice : 26400
         * channelPrice : 9255
         * stock : 10
         * singleMax : 10
         * codeAmount : 1
         */

        @JsonProperty("itemId")
        private Integer itemId;
        @JsonProperty("subTitle")
        private String subTitle;
        @JsonProperty("salePrice")
        private Integer salePrice;
        @JsonProperty("originPrice")
        private Integer originPrice;
        @JsonProperty("channelPrice")
        private Integer channelPrice;
        @JsonProperty("stock")
        private Integer stock;
        @JsonProperty("singleMax")
        private Integer singleMax;
        @JsonProperty("codeAmount")
        private Integer codeAmount;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ShopList implements Serializable {
        /**
         * id : 521535
         * name : 粉小越东南亚料理
         * latitude : 30.605176
         * longitude : 104.155131
         * cityCode : 510100
         * address : 喜树街35号1层伊藤广场6F-TY010
         * phoneNumber : 02885550521
         */

        @JsonProperty("id")
        private Integer id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("latitude")
        private String latitude;
        @JsonProperty("longitude")
        private String longitude;
        @JsonProperty("cityCode")
        private String cityCode;
        @JsonProperty("address")
        private String address;
        @JsonProperty("phoneNumber")
        private String phoneNumber;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class NoticeVOList implements Serializable {
        /**
         * titles : 预约方式
         * option : [{"optionName":"至少提前1天短信网址预约，高峰期需等位"}]
         */

        @JsonProperty("titles")
        private String titles;
        @JsonProperty("option")
        private List<Option> option;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class Option implements Serializable {
            /**
             * optionName : 至少提前1天短信网址预约，高峰期需等位
             */

            @JsonProperty("optionName")
            private String optionName;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ImgList implements Serializable {
        /**
         * url : https://cdn2.lianlianlvyou.com/lianlian/common/0cd2169017df499ebd8ddf3ff65cea30.jpg
         * sort : 0
         */

        @JsonProperty("url")
        private String url;
        @JsonProperty("sort")
        private Integer sort;
    }
}
