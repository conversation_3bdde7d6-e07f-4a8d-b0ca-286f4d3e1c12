package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/15 10:43
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoProductImg implements Serializable {

    /**
     * htmlContent :
     * htmlContentUrl :
     * buyNoticeList : [{"titles":"预约方式","platform":1,"platformName":"微信展示","option":[{"getChecked":true,"optionName":"至少提前1天短信网址预约，高峰期需等位"}]}]
     * productMap : {"1505582":[{"id":10902598,"productId":954855,"productItemId":1505582,"contentDesc":"冬阴功汤（小）\n泰式凤爪\n泰式街头炸虾片\n越南传统芒果鲜虾米纸卷\n特色海鲜菠萝炒饭\n泰式青柠蒸鲈鱼\n马来咖喱牛腩面包\n白灼菜心 \n甜品：椰汁西米糕（3～4） \n饮品：柠檬冰红茶（3～4）","numberAndUnit":"1","originPrice":55400,"contentType":2,"status":0,"createDate":"2023-04-19 11:17:28","updateTime":"2023-04-19 11:17:28"}]}
     * dateInfo : {"beginTime":"2023-04-20 08:00:00","endTime":"2023-05-19 23:59:59","validBeginDate":"2023-04-20 00:00:00","validEndDate":"2023-06-30 23:59:59","releaseTime":"2023-04-20 09:47:18"}
     * shopList : [{"id":521535,"name":"粉小越东南亚料理","latitude":"30.605176","longitude":"104.155131","cityCode":"510100","address":"喜树街35号1层伊藤广场6F-TY010","phoneNumber":"02885550521"}]
     */

    @JsonProperty("htmlContent")
    private String htmlContent;
    @JsonProperty("htmlContentUrl")
    private String htmlContentUrl;
    @JsonProperty("productMap")
    private Map<String,ProductMap> productMap;
    @JsonProperty("dateInfo")
    private DateInfo dateInfo;
    @JsonProperty("buyNoticeList")
    private List<BuyNoticeList> buyNoticeList;
    @JsonProperty("shopList")
    private List<ShopList> shopList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ProductMap implements Serializable {
        /**
         * id : 10902598
         * productId : 954855
         * productItemId : 1505582
         * contentDesc : 冬阴功汤（小）
         泰式凤爪
         泰式街头炸虾片
         越南传统芒果鲜虾米纸卷
         特色海鲜菠萝炒饭
         泰式青柠蒸鲈鱼
         马来咖喱牛腩面包
         白灼菜心
         甜品：椰汁西米糕（3～4）
         饮品：柠檬冰红茶（3～4）
         * numberAndUnit : 1
         * originPrice : 55400
         * contentType : 2
         * status : 0
         * createDate : 2023-04-19 11:17:28
         * updateTime : 2023-04-19 11:17:28
         */

        @JsonProperty("id")
        private int id;
        @JsonProperty("productId")
        private int productId;
        @JsonProperty("productItemId")
        private int productItemId;
        @JsonProperty("contentDesc")
        private String contentDesc;
        @JsonProperty("numberAndUnit")
        private String numberAndUnit;
        @JsonProperty("originPrice")
        private int originPrice;
        @JsonProperty("contentType")
        private int contentType;
        @JsonProperty("status")
        private int status;
        @JsonProperty("createDate")
        private String createDate;
        @JsonProperty("updateTime")
        private String updateTime;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class DateInfo implements Serializable {
        /**
         * beginTime : 2023-04-20 08:00:00
         * endTime : 2023-05-19 23:59:59
         * validBeginDate : 2023-04-20 00:00:00
         * validEndDate : 2023-06-30 23:59:59
         * releaseTime : 2023-04-20 09:47:18
         */

        @JsonProperty("beginTime")
        private String beginTime;
        @JsonProperty("endTime")
        private String endTime;
        @JsonProperty("validBeginDate")
        private String validBeginDate;
        @JsonProperty("validEndDate")
        private String validEndDate;
        @JsonProperty("releaseTime")
        private String releaseTime;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class BuyNoticeList implements Serializable {
        /**
         * titles : 预约方式
         * platform : 1
         * platformName : 微信展示
         * option : [{"getChecked":true,"optionName":"至少提前1天短信网址预约，高峰期需等位"}]
         */

        @JsonProperty("titles")
        private String titles;
        @JsonProperty("platform")
        private int platform;
        @JsonProperty("platformName")
        private String platformName;
        @JsonProperty("option")
        private List<Option> option;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class Option implements Serializable {
            /**
             * getChecked : true
             * optionName : 至少提前1天短信网址预约，高峰期需等位
             */

            @JsonProperty("getChecked")
            private boolean getChecked;
            @JsonProperty("optionName")
            private String optionName;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ShopList implements Serializable {
        /**
         * id : 521535
         * name : 粉小越东南亚料理
         * latitude : 30.605176
         * longitude : 104.155131
         * cityCode : 510100
         * address : 喜树街35号1层伊藤广场6F-TY010
         * phoneNumber : 02885550521
         */

        @JsonProperty("id")
        private int id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("latitude")
        private String latitude;
        @JsonProperty("longitude")
        private String longitude;
        @JsonProperty("cityCode")
        private String cityCode;
        @JsonProperty("address")
        private String address;
        @JsonProperty("phoneNumber")
        private String phoneNumber;
    }
}
