package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 10:13
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoRefreshCode implements Serializable {

    /**
     * messageTag : CHANNEL_ORDER_QR_CODE_NOTICE
     * channelOrderId : C221221000057000004
     * morderId : M221221000057
     * thirdOrderId : 1671588991573027
     * channelId : 4
     * qrCodeTime : 2022-12-21 10:50:32
     * orderOrigin : 4
     * genericCode : {"bookingUrl":"","qrCodeImgUrl":"https://debug.lianlianlvyou.com/n/item/qrcode/4596559/91641bc6642414834f7753440b53ab5e.png","detailUrl":"https://qd.llzby.vip/s/luTQ2KAa","qrCodeTime":"2022-12-21 10:50:32"}
     * orderCodeList : [{"orderId":"S221221000057","code":"459655924143521","qrCodeImgUrl":"https://debug.lianlianlvyou.com/n/item/qrcode/4596559/6961ce48005341be213621d5017fcdc9.png"}]
     */

    @JsonProperty("messageTag")
    private String messageTag;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("morderId")
    private String morderId;
    @JsonProperty("thirdOrderId")
    private String thirdOrderId;
    @JsonProperty("channelId")
    private int channelId;
    @JsonProperty("qrCodeTime")
    private String qrCodeTime;
    @JsonProperty("orderOrigin")
    private String orderOrigin;
    @JsonProperty("genericCode")
    private GenericCode genericCode;
    @JsonProperty("orderCodeList")
    private List<OrderCodeList> orderCodeList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class GenericCode implements Serializable {
        /**
         * bookingUrl :
         * qrCodeImgUrl : https://debug.lianlianlvyou.com/n/item/qrcode/4596559/91641bc6642414834f7753440b53ab5e.png
         * detailUrl : https://qd.llzby.vip/s/luTQ2KAa
         * qrCodeTime : 2022-12-21 10:50:32
         */

        @JsonProperty("bookingUrl")
        private String bookingUrl;
        @JsonProperty("qrCodeImgUrl")
        private String qrCodeImgUrl;
        @JsonProperty("detailUrl")
        private String detailUrl;
        @JsonProperty("qrCodeTime")
        private String qrCodeTime;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class OrderCodeList implements Serializable {
        /**
         * orderId : S221221000057
         * code : 459655924143521
         * qrCodeImgUrl : https://debug.lianlianlvyou.com/n/item/qrcode/4596559/6961ce48005341be213621d5017fcdc9.png
         */

        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("code")
        private String code;
        @JsonProperty("qrCodeImgUrl")
        private String qrCodeImgUrl;
    }
}
