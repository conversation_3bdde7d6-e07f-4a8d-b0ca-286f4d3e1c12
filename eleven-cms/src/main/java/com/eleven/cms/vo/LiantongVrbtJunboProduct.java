package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiantongVrbtJunboProduct {

    //产品名
    private String productName;
    //产品 id
    private String productId;
    //产品类型 0:订购类,1:点播类
    private String productType;
    //价格单位 1：表示价格单位是 0.01 元
        // 2：表示价格单位是 0.01M
           // 例: price=200 FeeType=1, 则为 2 元Price=300 FeeType=2 则为 3M
    private String priceUnit;
    //价格值
    private String price;
    //用户权益
    private String userRights;
    //订购状态 1: 正式订购（正常） 2: 暂停（隐藏） 4: 订购关系停止（等待删除）
    private String status;
    //订购时间
    private String subTime;
    //退订时间
    private String unSubTime;
    //可否退订
    private String unSubscribeable;
    //不可退订原因
    private String cantUnSubscribeReason;
}
