package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 10:37
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoSendMsg implements Serializable {

    /**
     * success : true
     * code : 200
     * message : 请求响应成功!
     * data : {"sign":"48ea23023c4ecb4c37f89ba4a7e1d288","encryptedData":"11O/0TUtwFup5ehcJiVwDg==","timestamp":1671518701261,"channelId":4}
     */

    @JsonProperty("success")
    private boolean success;
    @JsonProperty("code")
    private int code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private Data data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * sign : 48ea23023c4ecb4c37f89ba4a7e1d288
         * encryptedData : 11O/0TUtwFup5ehcJiVwDg==
         * timestamp : 1671518701261
         * channelId : 4
         */

        @JsonProperty("sign")
        private String sign;
        @JsonProperty("encryptedData")
        private String encryptedData;
        @JsonProperty("timestamp")
        private long timestamp;
        @JsonProperty("channelId")
        private int channelId;
    }
}
