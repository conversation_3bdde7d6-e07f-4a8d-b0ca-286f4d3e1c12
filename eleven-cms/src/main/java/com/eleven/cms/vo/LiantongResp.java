package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecg.common.api.vo.Result;

/**
 * Author: <EMAIL>
 * Date: 2020/12/29 14:17
 * Desc: 联通沃音乐开放平台响应封装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiantongResp {

     public static final String RETURN_CODE_SUCC_1 = "0000";
     public static final String RETURN_CODE_SUCC_2 = "000000";
     public static final String RETURN_CODE_ERROR = "999999";
     public static final String DESCRIPTION_ERROR = "接口通讯失败";
     public static LiantongResp ERROR_RESP = new LiantongResp(RETURN_CODE_ERROR,DESCRIPTION_ERROR,null,null);
    /**
     * returnCode : 0000
     * description : success
     * orderId : 1201229122501834981
     * url : https://open.10155.com/confirm/showPage?orderId=1201229122501834981
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("description")
    private String description;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("url")
    private String url;

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }



    public boolean isOK(){
        return RETURN_CODE_SUCC_1.contains(returnCode) || RETURN_CODE_SUCC_2.equals(returnCode);
    }

    public static boolean isCodeOK(String returnCode){
        return RETURN_CODE_SUCC_1.contains(returnCode) || RETURN_CODE_SUCC_2.equals(returnCode);
    }

    public String expr(){
        return this.returnCode+":"+this.getDescription();
    }

    public static LiantongResp error(){
        return ERROR_RESP;
    }

    public Result<?> toResult() {
        return this.isOK() ? Result.ok(description) : Result.error(description);
    }
}
