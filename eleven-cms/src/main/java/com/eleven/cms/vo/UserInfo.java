package com.eleven.cms.vo;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @author: cai lei
 * @create: 2022-06-15 17:35
 */
@Data
public class UserInfo {
    @Excel(name = "mobile")
    private String mobile;
    @Excel(name = "字段C")
    private String c;
    @Excel(name = "字段D")
    private String d;
    @Excel(name = "字段E")
    private String e;
    @Excel(name = "字段F")
    private String f;
    @Excel(name = "字段G", importFormat = "yyyy-MM-dd HH:mm:ss")
    private String g;
    @Excel(name = "字段H", importFormat = "yyyy-MM-dd HH:mm:ss")
    private String h;
    @Excel(name = "字段I", importFormat = "yyyy-MM-dd HH:mm:ss")
    private String i;
    @Excel(name = "字段J", importFormat = "yyyy-MM-dd HH:mm:ss")
    private String j;
    @Excel(name = "字段K", importFormat = "yyyy-MM-dd HH:mm:ss")
    private String k;
    @Excel(name = "字段L")
    private String l;
    @Excel(name = "字段M")
    private String m;
    @Excel(name = "字段N")
    private String n;
    @Excel(name = "字段O")
    private String o;
    @Excel(name = "字段P")
    private String p;
    @Excel(name = "字段Q")
    private String q;
    @Excel(name = "字段R")
    private String r;
    @Excel(name = "字段S")
    private String s;
    private String img1;
    private String img2;
    private String img3;
    private String img4;
    private String img5;
}
