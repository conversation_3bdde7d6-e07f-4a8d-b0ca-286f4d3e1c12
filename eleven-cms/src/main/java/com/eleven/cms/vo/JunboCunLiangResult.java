package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/13 17:38
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JunboCunLiangResult implements Serializable {
    public static final String  CODE_OK ="0000";
    public static final String  RESPONSE_OK="0";
    /**
     * success : true
     * code : 0000
     * message : 执行成功
     * data : {"responseCode":"0"}
     */

    @JsonProperty("success")
    private boolean success;
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private Data data;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * responseCode : 0
         */

        @JsonProperty("responseCode")
        private String responseCode;
        public boolean isOK(){
            return RESPONSE_OK.equals(this.getResponseCode());
        }
    }
}
