package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/22 14:44
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class XuTongYiDongResult implements Serializable {
    public static final Integer  CODE_OK =0;
    @JsonProperty("error")
    private Integer error;
    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("errorMsg")
    private String errorMsg;
    public boolean isOK(){
        return CODE_OK.equals(this.getError());
    }
}
