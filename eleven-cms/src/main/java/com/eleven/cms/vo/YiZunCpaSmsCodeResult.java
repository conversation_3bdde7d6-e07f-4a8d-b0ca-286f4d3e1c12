package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YiZunCpaSmsCodeResult {

    public static final String CODE_OK = "00000000";
    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("traceId")
    private String traceId;
    @JsonProperty("tradeId")
    private String tradeId;

    @JsonProperty("amount")
    private String amount;


    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static YiZunCpaSmsCodeResult fail() {
        return YiZunCpaSmsCodeResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }
    public static YiZunCpaSmsCodeResult fail(String msg) {
        return YiZunCpaSmsCodeResult.builder().code(CODE_FAIL).message(msg).build();
    }
}
