package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SichuanMobilePrepareOrderResult {


    public static final String CODE_OK = "0000";

    public static final String CODE_FAIL = "9999";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("res_code")
    private String resCode;
    @JsonProperty("res_msg")
    private String resMsg;
    @JsonProperty("result")
    private String result;


    public boolean isOK() {
        return CODE_OK.equals(resCode);
    }

    public static SichuanMobilePrepareOrderResult fail() {
        return SichuanMobilePrepareOrderResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).build();
    }

}
