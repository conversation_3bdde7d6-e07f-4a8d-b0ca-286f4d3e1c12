package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2023-05-25 14:46
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangxiYidongResult {

    private String ret;
    private String msg;
    private Data data;
    private String busiSerialNumber;

    private static final String CALL_SUC_CODE = "0";
    private static final String RESULT_SUC_OK = "0";

    public static final JiangxiYidongResult FAIL_RESULT = new JiangxiYidongResult("9999", "通讯失败", null, null);

    public static final JiangxiYidongResult INSERT_CODE_FAIL_RESULT = new JiangxiYidongResult("9999", "能力原子化字段为空", null, null);

    @lombok.Data
    @NoArgsConstructor
    public static class Data {
        private ResultMsg resultMsg;
    }

    @lombok.Data
    @NoArgsConstructor
    public static class ResultMsg {
        private String respCode;
        private String respDesc;
    }

    public boolean isOk() {
        return CALL_SUC_CODE.equals(ret) && data != null && data.getResultMsg() != null && RESULT_SUC_OK.equals(data.getResultMsg().getRespCode());
    }


}
