package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/28 10:51
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KaSaiStockLianTongSubmitResult implements Serializable {
    public static final Integer  CODE_OK = 200;
    /**
     * code : 200
     * data : {"sign":"ffa8a8729d7c4f4bbaf99dfe8b8c102e","torder":"ffa8a8729d7c4f4bbaf99dfe8b8c102e","requrl":null,"applyName":null,"packageName":null}
     * message : null
     * cmccCode : null
     */

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("message")
    private Object message;
    @JsonProperty("cmccCode")
    private Object cmccCode;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @lombok.Data
    public static class Data implements Serializable {
        /**
         * sign : ffa8a8729d7c4f4bbaf99dfe8b8c102e
         * torder : ffa8a8729d7c4f4bbaf99dfe8b8c102e
         * requrl : null
         * applyName : null
         * packageName : null
         */

        @JsonProperty("sign")
        private String sign;
        @JsonProperty("torder")
        private String torder;
        @JsonProperty("requrl")
        private Object requrl;
        @JsonProperty("applyName")
        private Object applyName;
        @JsonProperty("packageName")
        private Object packageName;
    }
}
