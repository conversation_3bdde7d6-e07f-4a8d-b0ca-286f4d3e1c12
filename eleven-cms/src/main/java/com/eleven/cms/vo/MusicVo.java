package com.eleven.cms.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * Author: <EMAIL>
 * Date: 2020/6/29 17:01
 * Desc:Todo
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MusicVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键id*/
    private java.lang.String id;
    /**版权id*/
    private java.lang.String copyrightId;
    /**歌曲名*/
    private java.lang.String musicName;
    /**歌手名*/
    private java.lang.String singerName;
    /**视频彩铃产品id*/
    private java.lang.String vrbtProductId;
    /**cp id*/
    private java.lang.String cpId;
    /**订阅库版权id*/
    private java.lang.String dyCopyrightId;
    /**电信资源编码*/
    private java.lang.String dxResourceCode;
    /**电信铃音编码*/
    private java.lang.String dxToneCode;
    /**联通铃音ID*/
    private java.lang.String ltRingId;
    /**视频彩铃预览图地址*/
    private java.lang.String vrbtImg;
    /**视频彩铃播放地址*/
    private java.lang.String vrbtVideo;
    /**热度*/
    private java.lang.Integer hotLevel;
    /**播放量*/
    private java.lang.Integer playCount;
    /**点赞数*/
    private java.lang.Integer likeCount;
    /**收藏数*/
    private java.lang.Integer favCount;
    /**有效期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "有效期")
    private java.util.Date expiryDate;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
}
