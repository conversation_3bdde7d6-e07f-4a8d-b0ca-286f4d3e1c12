package com.eleven.cms.vo;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Builder;
import lombok.Data;

/**
 * @author: cai lei
 * @create: 2023-03-08 11:38
 */
@Data
@JacksonXmlRootElement(localName="Request")
public class JiangsuRequestToken {
    @JacksonXmlProperty(localName = "Datetime")
    private String datetime;

    @JacksonXmlProperty(localName = "Authorization")
    private Authorization authorization;

    @Data
    @Builder
    public static class Authorization {
        @JacksonXmlProperty(localName = "AppKey")
        private String appKey;
        @JacksonXmlProperty(localName = "Sign")
        private String sign;
        @JacksonXmlProperty(localName = "SecInterface")
        private String secInterface;

    }
}
