package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/13 17:22
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JunboCunLiangRightsResult implements Serializable {
    public static final String  CODE_OK ="0000";
    /**
     * success : true
     * code : 0000
     * message : 执行成功
     * data : {"productInfo":"","data":[{"rightsActId":"45","rightsActName":"","goodsItem":[{"rightsGoodId":"9990400100002010001","rightsGoodsName":"腾讯视频月卡","logo":"https://res.coc.10086.cn/res/res3/gr/imageServer/producticon/2EF12BDD7F67B49E454157CEA51B3CE4.png","displayAmount":"¥0","hiddenAmount":"20"},{"rightsGoodId":"9990400100001970001","rightsGoodsName":"爱奇艺视频月卡","logo":"https://res.coc.10086.cn/res/res3/gr/imageServer/producticon/9B57D5A4B399F5C5D7E6682F4596C1E9.png","displayAmount":"¥0","hiddenAmount":"20"}]}]}
     */

    @JsonProperty("success")
    private boolean success;
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private DataX data;
    public boolean isOK(){
        return CODE_OK.equals(this.getCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class DataX implements Serializable {
        /**
         * productInfo :
         * data : [{"rightsActId":"45","rightsActName":"","goodsItem":[{"rightsGoodId":"9990400100002010001","rightsGoodsName":"腾讯视频月卡","logo":"https://res.coc.10086.cn/res/res3/gr/imageServer/producticon/2EF12BDD7F67B49E454157CEA51B3CE4.png","displayAmount":"¥0","hiddenAmount":"20"},{"rightsGoodId":"9990400100001970001","rightsGoodsName":"爱奇艺视频月卡","logo":"https://res.coc.10086.cn/res/res3/gr/imageServer/producticon/9B57D5A4B399F5C5D7E6682F4596C1E9.png","displayAmount":"¥0","hiddenAmount":"20"}]}]
         */

        @JsonProperty("productInfo")
        private String productInfo;
        @JsonProperty("data")
        private List<Data> data;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @lombok.Data
        public static class Data implements Serializable {
            /**
             * rightsActId : 45
             * rightsActName :
             * goodsItem : [{"rightsGoodId":"9990400100002010001","rightsGoodsName":"腾讯视频月卡","logo":"https://res.coc.10086.cn/res/res3/gr/imageServer/producticon/2EF12BDD7F67B49E454157CEA51B3CE4.png","displayAmount":"¥0","hiddenAmount":"20"},{"rightsGoodId":"9990400100001970001","rightsGoodsName":"爱奇艺视频月卡","logo":"https://res.coc.10086.cn/res/res3/gr/imageServer/producticon/9B57D5A4B399F5C5D7E6682F4596C1E9.png","displayAmount":"¥0","hiddenAmount":"20"}]
             */

            @JsonProperty("rightsActId")
            private String rightsActId;
            @JsonProperty("rightsActName")
            private String rightsActName;
            @JsonProperty("goodsItem")
            private List<GoodsItem> goodsItem;

            @JsonIgnoreProperties(ignoreUnknown = true)
            @lombok.Data
            public static class GoodsItem implements Serializable {
                /**
                 * rightsGoodId : 9990400100002010001
                 * rightsGoodsName : 腾讯视频月卡
                 * logo : https://res.coc.10086.cn/res/res3/gr/imageServer/producticon/2EF12BDD7F67B49E454157CEA51B3CE4.png
                 * displayAmount : ¥0
                 * hiddenAmount : 20
                 */

                @JsonProperty("rightsGoodId")
                private String rightsGoodId;
                @JsonProperty("rightsGoodsName")
                private String rightsGoodsName;
                @JsonProperty("logo")
                private String logo;
                @JsonProperty("displayAmount")
                private String displayAmount;
                @JsonProperty("hiddenAmount")
                private String hiddenAmount;
            }
        }
    }
}
