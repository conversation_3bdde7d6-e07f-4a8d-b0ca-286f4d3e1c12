package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/9 17:36
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FeeChargeOrderPlusRequest implements Serializable {
    public static final int CODE_OK = 200;
    /**
     * msg : 成功
     * code : 200
     * data : 1002991651095838720
     */

    @JsonProperty("msg")
    private String msg;
    @JsonProperty("code")
    private int code;
    @JsonProperty("data")
    private String data;
    public boolean isOK() {
        return CODE_OK==code;
    }
}
