package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/6 22:40
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AiVrbtOpenResult implements Serializable {
    public static final String CODE_OK = "0";
    /**
     * order_no : f241c7f31031490a8f0cdf0cb2cbe1db
     * res_message : 成功
     * fee_url : https://m.imusic.cn/paycenter/#/openauth/confirm? order_no=f241c7f31031490a8f0cdf0cb2cbe1db
     * res_code : 0
     * url : https://m.imusic.cn/paycenter/#/openauth/confirm? order_no=f241c7f31031490a8f0cdf0cb2cbe1db
     */

    @JsonProperty("order_no")
    private String orderNo;
    @JsonProperty("res_message")
    private String resMessage;
    @JsonProperty("fee_url")
    private String feeUrl;
    @JsonProperty("res_code")
    private String resCode;
    @JsonProperty("url")
    private String url;
    /**
     * 判断是否成功
     */
    public boolean isOK() {
        return CODE_OK.equals(this.getResCode()) ;
    }
}
