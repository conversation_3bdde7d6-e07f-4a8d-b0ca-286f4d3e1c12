package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 9:49
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoCheckCreateOrder implements Serializable {

    /**
     * thirdOrderId : 1671529978192g14
     * validToken : C_CKECK_ORDER_TOKEN221220000001
     */

    @JsonProperty("thirdOrderId")
    private String thirdOrderId;
    @JsonProperty("validToken")
    private String validToken;
}
