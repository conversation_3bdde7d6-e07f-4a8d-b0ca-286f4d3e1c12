package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YunnanMobileQrymatchAuthSceneResult {

    public static final String CODE_OK = "0";
    public static final String X_CODE_OK = "0000";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    /*0代表正常，其它代表异常*/
    @JsonProperty("respCode")
    private String respCode;
    @JsonProperty("respDesc")
    private String respDesc;
    @JsonProperty("result")
    private Result result;
    @JsonProperty("nextFlowId")
    private String nextFlowId;

    public boolean isOK() {
        return CODE_OK.equals(respCode);
    }

    public static YunnanMobileQrymatchAuthSceneResult fail() {
        return YunnanMobileQrymatchAuthSceneResult.builder().respCode(CODE_FAIL).respDesc(MSG_FAIL).respCode("false").build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result{
        @JsonProperty("RESULT_CODE")
        private String resultCode;
        @JsonProperty("AUTH_INSTANCE_ID")
        private String authInstanceId;
        @JsonProperty("AUTH_COMPONENTS")
        private List<AuthComponent> authComponents;
        @JsonProperty("AUTH_EPAPER_SWITCH_TAG")
        private String authEpaperSwitchTag;
        @JsonProperty("X_RESULTCODE")
        private String xResultCode;
        @JsonProperty("X_NODE_NAME")
        private String xNodeName;
        @JsonProperty("X_RESULTINFO")
        private String xResultInfo;
        public boolean isOK() {
            return X_CODE_OK.equals(resultCode);
        }
        public static Result fail() {
            return Result.builder().resultCode(CODE_FAIL).xResultInfo(MSG_FAIL).resultCode("false").build();
        }
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder(toBuilder = true)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class AuthComponent{
            @JsonProperty("VALID_DATE")
            private String validDate;
            @JsonProperty("COMPONENT_ID")
            private String componentId;
            @JsonProperty("PERSON_SERVICE")
            private String personService;
            @JsonProperty("AUTH_METHOD_TYPE")
            private String authMethodType;
            @JsonProperty("EXPIRED_TIME")
            private String expiredTime;
            @JsonProperty("SELECT_FLAG")
            private String selectFlag;
            @JsonProperty("AUTH_METHOD_CODE")
            private String authMethodCode;
            @JsonProperty("AUTH_METHOD_NAME")
            private String authMethodName;
            @JsonProperty("PERSON_COMPONENT_SVC")
            private String personComponentSvc;
            @JsonProperty("AUTH_METHOD_ID")
            private String authMethodId;
            @JsonProperty("AUTH_CONTENT")
            private String authContent;
            @JsonProperty("SORT")
            private String sort;
            @JsonProperty("CRUX_PARAMS")
            private String cruxParams;
            @JsonProperty("EXPIRE_DATE")
            private String expireDate;
            @JsonProperty("AUTH_SMS_CODE_TYPE")
            private String authSmsCodeType;
        }
    }
}
