package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecg.common.constant.CommonConstant;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/12 14:02
 **/
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class YouRanResult implements Serializable {
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";
    /**
     * success : true
     * message : 验证码已发送
     * code : 200
     * result : 1867144574825676801
     * timestamp : 1733996931169
     * status : 0
     * ok : true
     */

    @JsonProperty("success")
    private boolean success;
    @JsonProperty("message")
    private String message;
    @JsonProperty("code")
    private int code;
    @JsonProperty("result")
    private String result;
    @JsonProperty("timestamp")
    private long timestamp;
    @JsonProperty("status")
    private int status;
    @JsonProperty("ok")
    private boolean ok;

    public boolean isOK() {
        return CommonConstant.SC_OK_200.equals(this.getCode());
    }

    public static final YouRanResult FAIL_RESULT = new YouRanResult(false, "通讯失败", 500,null,500,500,false);


}
