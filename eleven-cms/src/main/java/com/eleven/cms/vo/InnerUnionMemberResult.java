package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-06-06 15:11
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class InnerUnionMemberResult {
    private Integer code;
    private String message;
    private Object data;
    private String serviceId;

    public static final InnerUnionMemberResult FAIL_RESULT = new InnerUnionMemberResult(9999, "通讯失败", null, "");

    public boolean isGetSmsOK() {
        return 510 == code;
    }

    public boolean isSubSmsOK() {
        return 200 == code;
    }
}
