package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO 江西视频彩铃无纸化协议返回结果
 * @Date 2023/10/13 15:55
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JiangXiVrbtAgreementResult implements Serializable {
    public static final String CODE_OK = "0";
    /**
     * returnCode : 1
     * message :
     * ts : 1695261830255
     * data : ****
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("ts")
    private long ts;
    @JsonProperty("data")
    private Object data;
    public boolean isOK(){
        return CODE_OK.equals(returnCode);
    }
}
