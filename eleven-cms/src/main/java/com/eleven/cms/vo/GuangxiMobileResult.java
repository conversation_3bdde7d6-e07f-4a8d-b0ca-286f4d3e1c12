package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GuangxiMobileResult {

    public static final Integer CODE_OK = 200;
    public static final Integer CODE_FAIL = -1;
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("status")
    private Integer status;
    @JsonProperty("message")
    private String resMsg;
    @JsonProperty("code")
    private String code;

    public boolean isOK() {
        return CODE_OK.equals(status);
    }

    public static GuangxiMobileResult fail() {
        return GuangxiMobileResult.builder().status(CODE_FAIL).resMsg(MSG_FAIL).code("false").build();
    }
}
