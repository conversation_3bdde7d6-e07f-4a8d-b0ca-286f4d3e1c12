package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HenanMobileQueryResult {

    public static final String CODE_OK = "1";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("data")
    private ResultData data;
    @JsonProperty("message")
    private String message;
    @JsonProperty("code")
    private String code;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static HenanMobileQueryResult fail() {
        return HenanMobileQueryResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }

    @Data
    public static class ResultData {
        @JsonProperty("pageCount")
        private String pageCount;
        @JsonProperty("count")
        private String count;
        @JsonProperty("pageSize")
        private String pageSize;
        @JsonProperty("pageNo")
        private String pageNo;
        @JsonProperty("orders")
        private List<Order> orders;
    }

    @Data
    public static class Order {
        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("orderType")
        private String orderType;
        @JsonProperty("orderStatus")
        private String orderStatus;
        @JsonProperty("mobile")
        private String mobile;
        @JsonProperty("accessOperCode")
        private String accessOperCode;
        @JsonProperty("activitiesId")
        private String activitiesId;
        @JsonProperty("activitiesName")
        private String activitiesName;
        @JsonProperty("areaCode")
        private String areaCode;
        @JsonProperty("openResMsg")
        private String openResMsg;
        @JsonProperty("shopId")
        private String shopId;
        @JsonProperty("createTime")
        private String createTime;
        @JsonProperty("finishTime")
        private String finishTime;
        @JsonProperty("dealMsg")
        private String dealMsg;
    }
}
