package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2021-09-22 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HebeiYidongQueryResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "999";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("res_code")
    private String resCode;
    @JsonProperty("res_msg")
    private String resMsg;
    @JsonProperty("res_desc")
    private String resDesc;
    @JsonProperty("result")
    private List<ResultData> result;


    public boolean isOK() {
        return CODE_OK.equals(resCode);
    }

    public static HebeiYidongQueryResult fail() {
        return HebeiYidongQueryResult.builder().resCode(CODE_FAIL).resMsg(MSG_FAIL).build();
    }

    @Data
    public static class ResultData {
        @JsonProperty("displayMode")
        private String displayMode;
        @JsonProperty("offerId")
        private String offerId;
        @JsonProperty("businessPositionCode")
        private String businessPositionCode;
        @JsonProperty("offerName")
        private String offerName;
        @JsonProperty("offerType")
        private String offerType;
        @JsonProperty("priority")
        private String priority;
        @JsonProperty("offerAttrMap")
        private List<Map<String,String>> offerAttrMap;
    }

}
