package com.eleven.cms.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
@Data
public class Product {
    /**
     * 是否已领取当月权益 0否 1是
     */
    @TableField(exist = false)
    private Integer isReceiveRights;

    /**
     * 权益领取描述
     */
    @TableField(exist = false)
    private String receiveRightsMsg;

    /**业务id*/
    @TableField(exist = false)
    private String serviceId;
    /**包名*/
    @TableField(exist = false)
    private String titleName;
    /**产品图片*/
    @TableField(exist = false)
    private String  productImg;
}
