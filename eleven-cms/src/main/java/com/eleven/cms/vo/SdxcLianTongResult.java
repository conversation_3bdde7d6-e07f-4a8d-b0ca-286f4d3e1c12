package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 时代星辰联通业务接口响应结果
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/2 10:46
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SdxcLianTongResult implements Serializable {
    public static final String  CODE_OK = "1";
    /**
     * linkid : 121166178
     * msg : 提交验证码成功
     * status : 1
     */

    @JsonProperty("linkid")
    private String linkid;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("status")
    private String status;
    public boolean isOK(){
        return CODE_OK.equals(this.getStatus());
    }
}
