package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/4/23 9:57
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LianLianFenXiaoCreateOrder implements Serializable {

    /**
     * purchaseTime : 2022-12-21 10:37:23
     * thirdPartyOrderNo : 1671588991573027
     * channelOrderId : C221221000057000004
     * orderList : [{"customerName":"iroha","customerPhoneNumber":"18315956889","count":0,"address":"神滨西区116号","idCard":"510125199904250015","orderId":"S221221000057","corderId":"S221221000057","productItemName":"测试套餐一","productItemId":4596559,"orderState":111,"sendSms":0}]
     * validBeginDate : 2022-12-02 00:00:00
     * validEndDate : 2023-01-31 23:59:59
     */

    @JsonProperty("purchaseTime")
    private String purchaseTime;
    @JsonProperty("thirdPartyOrderNo")
    private String thirdPartyOrderNo;
    @JsonProperty("channelOrderId")
    private String channelOrderId;
    @JsonProperty("validBeginDate")
    private String validBeginDate;
    @JsonProperty("validEndDate")
    private String validEndDate;
    @JsonProperty("orderList")
    private List<OrderList> orderList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class OrderList implements Serializable {
        /**
         * customerName : iroha
         * customerPhoneNumber : 18315956889
         * count : 0
         * address : 神滨西区116号
         * idCard : 510125199904250015
         * orderId : S221221000057
         * corderId : S221221000057
         * productItemName : 测试套餐一
         * productItemId : 4596559
         * orderState : 111
         * sendSms : 0
         */

        @JsonProperty("customerName")
        private String customerName;
        @JsonProperty("customerPhoneNumber")
        private String customerPhoneNumber;
        @JsonProperty("count")
        private int count;
        @JsonProperty("address")
        private String address;
        @JsonProperty("idCard")
        private String idCard;
        @JsonProperty("orderId")
        private String orderId;
        @JsonProperty("corderId")
        private String corderId;
        @JsonProperty("productItemName")
        private String productItemName;
        @JsonProperty("productItemId")
        private int productItemId;
        @JsonProperty("orderState")
        private int orderState;
        @JsonProperty("sendSms")
        private int sendSms;
    }
}
