package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YunnanMobileAuthenticationResult {

    public static final String CODE_OK = "0";
    public static final String XCODE_OK = "0000";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    /*0代表正常，其它代表异常*/
    @JsonProperty("respCode")
    private String respCode;
    @JsonProperty("respDesc")
    private String respDesc;
    @JsonProperty("result")
    private Result result;
    @JsonProperty("nextFlowId")
    private String nextFlowId;

    public boolean isOK() {
        return CODE_OK.equals(respCode);
    }

    public static YunnanMobileAuthenticationResult fail() {
        return YunnanMobileAuthenticationResult.builder().respCode(CODE_FAIL).respDesc(MSG_FAIL).respCode("false").build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result{
        @JsonProperty("RESULT_CODE")
        private String resultCode;
        @JsonProperty("AUTH_INSTANCE_TOKEN")
        private String authInstanceToken;
        @JsonProperty("X_RESULTCODE")
        private String xResultcode;
        @JsonProperty("X_NODE_NAME")
        private String xNodeName;
        @JsonProperty("RESULT_INFO")
        private String resultInfo;
        @JsonProperty("X_RESULTINFO")
        private String xResultinfo;
        public boolean isOK() {
            return CODE_OK.equals(xResultcode);
        }
    }
}
