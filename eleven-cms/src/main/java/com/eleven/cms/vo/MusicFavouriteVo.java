package com.eleven.cms.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: cms_music_favourite
 * @Author: jeecg-boot
 * @Date:   2022-03-01
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MusicFavouriteVo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
    private String id;
	/**手机号*/
    private String mobile;
    /*版权id*/
    private String copyrightId;
    /*歌曲名*/
    private String musicName;
    /**歌手名*/
    private java.lang.String singerName;
    /**视频彩铃产品id*/
    private java.lang.String vrbtProductId;
    /**cp id*/
    private java.lang.String cpId;
    /**订阅库版权id*/
    private java.lang.String dyCopyrightId;
    /**电信资源编码*/
    private java.lang.String dxResourceCode;
    /**电信铃音编码*/
    private java.lang.String dxToneCode;
    /**联通铃音ID*/
    private java.lang.String ltRingId;
    /**视频彩铃预览图地址*/
    private java.lang.String vrbtImg;
    /**视频彩铃播放地址*/
    private java.lang.String vrbtVideo;
    /**热度*/
    private java.lang.Integer hotLevel;
    /**播放量*/
    private java.lang.Integer playCount;
    /**点赞数*/
    private java.lang.Integer likeCount;
    /**收藏数*/
    private java.lang.Integer favCount;
}
