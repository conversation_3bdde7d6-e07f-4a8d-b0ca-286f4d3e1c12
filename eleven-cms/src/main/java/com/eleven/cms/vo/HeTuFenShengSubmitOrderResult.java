package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 河图分省提交验证码响应
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/16 17:04
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HeTuFenShengSubmitOrderResult implements Serializable {
    public static final String RESP_CODE_OK = "000000";
    /**
     * returnCode : 000000
     * message : 请求成功
     * resultData : {"orderId":"a5fc1bde098042e7965dc2903f98772e"}
     * serverTime : 1724382097650
     */

    @JsonProperty("returnCode")
    private String returnCode;
    @JsonProperty("message")
    private String message;
    @JsonProperty("resultData")
    private ResultData resultData;
    @JsonProperty("serverTime")
    private long serverTime;
    public boolean isOK() {
        return RESP_CODE_OK.equals(this.getReturnCode());
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ResultData implements Serializable {
        /**
         * orderId : a5fc1bde098042e7965dc2903f98772e
         */

        @JsonProperty("orderId")
        private String orderId;
    }
}
