package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: cai lei
 * @create: 2022-11-03 15:42
 */
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class YuxunHunanDianxinResult {
    private String code;
    private String msg;
    private String orderNum;
    private String orderPage;

    private static final String SUC_CODE = "0";

    public static final YuxunHunanDianxinResult FAIL_RESULT = new YuxunHunanDianxinResult("9999", "通讯失败", null, null);

    public boolean isOk() {
        return SUC_CODE.equals(code);
    }

}
