package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lihb
 * @create: 2022-9-21 14:32:19
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WenshengVideoResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    /*0代表正常，其它代表异常*/
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("request_id")
    private String requestId;
    @JsonProperty("result")
    private Result result;

    public boolean isOK() {
        return CODE_OK.equals(code);
    }

    public static WenshengVideoResult fail() {
        return WenshengVideoResult.builder().code(CODE_FAIL).message(MSG_FAIL).build();
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result{
        @JsonProperty("task_id")
        private String taskId;
        @JsonProperty("sub_task_ids")
        private List<String> subTaskIds;
        @JsonProperty("sub_task_results")
        private List<SubTaskResult> subTaskResults;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder(toBuilder = true)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class SubTaskResult{
            @JsonProperty("sub_task_id")
            private String subTaskId;
            @JsonProperty("task_completion")
            private String taskCompletion;
            @JsonProperty("task_status")
            private String taskStatus;
            @JsonProperty("video")
            private String video;
            @JsonProperty("cover")
            private String cover;
        }
    }
}
