package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/10 9:59
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HuYuQuanWangOrderNotifyRequest implements Serializable {


    /**
     * orderId : 20241012214514APP_6Q25675028
     * chargeId : 760000159041
     * sign : 306789B20BFE5CD068185E8952B3179E
     * type : 1
     * userCode : BLqoLAO2ScWwrH1ZzDrCsyJjluW0eVTFS0pRMYyfRLzbL6EkZQYs0hhvqV2gC9x0nbortdfHLtklZIUqEP0d5/mCbysGX7dTBzsp4PVDj5zJtoTqFbJ/D/2xsyJ4Kw+cHTomMo+tY8gZuwR0
     */

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("chargeId")
    private String chargeId;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("type")
    private int type;
    @JsonProperty("userCode")
    private String userCode;
}
