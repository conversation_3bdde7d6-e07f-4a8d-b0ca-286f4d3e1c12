package com.eleven.cms.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: lihb
 * @create: 2023年7月14日15:17:15
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HunanYidongResult {

    public static final String CODE_OK = "0";
    public static final String CODE_FAIL = "-1";
    public static final String MSG_FAIL = "操作失败";

    @JsonProperty("respCode")
    private String respCode;
    @JsonProperty("respDesc")
    private String respDesc;
    private List<ResultData> result;

    public boolean isOK() {
        return CODE_OK.equals(respCode);
    }

    public static HunanYidongResult fail() {
        return HunanYidongResult.builder().respCode(CODE_FAIL).respDesc(MSG_FAIL).build();
    }
    @Data
    public static class ResultData {
        @JsonProperty("RESULT_CODE")
        private String resultCode;
        @JsonProperty("RESULT_INFO")
        private String resultInfo;
        @JsonProperty("TRADE_ID")
        private String tradeId;
    }
}
